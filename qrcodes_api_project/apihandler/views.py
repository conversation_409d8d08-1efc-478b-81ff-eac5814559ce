import json
import requests
from datetime import datetime
from django.shortcuts import render
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework.views import APIView
from .helpers import *
from qrcodes_api_project.settings import GET_BOT_API_DOMAIN


class CodesHandler(APIView):

    def post(self, request: Request, format=None):
        data: dict = request.data
        data = data.copy()
        if "qrcode" not in data:
            return Response(status=400, data={"success": False, "error_message": "no qrcode in request"})
        if "lang" not in data:
            return Response(status=400, data={"success": False, "error_message": "no lang in request"})

        def write_data_to_log():
            with open("api_data.json", "a") as file:
                data.update(ip=get_client_ip(request))
                data_for_save = {datetime.utcnow().strftime("%d.%m.%Y %H:%M:%S"): data}
                json.dump(data_for_save, file, indent=1)
        write_data_to_log()
        user_chat_id, bot_id = data.get("user_chat_id"), data.get("bot_id")
        if user_chat_id and bot_id:
            result = requests.post(f"https://{GET_BOT_API_DOMAIN}/api/sendmessage", data=data)
            status = result.status_code
            result_data = result.json()
            return Response(status=status, data=result_data)
        return Response(status=200, data={"success": False, "error_message": "can't send message to user."
                                                                             "Parameters required: user_chat_id, bot_id"})
