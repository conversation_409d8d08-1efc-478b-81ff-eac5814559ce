import asyncio
from asgiref.sync import async_to_sync
from aiogram import <PERSON><PERSON>


def get_client_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def send_message(bot_token: str, user_chat_id: int, text: str) -> dict:
    result = {}

    async def send():
        bot = Bot(bot_token)
        bot_user = await bot.me
        data = dict()
        try:
            message = await bot.send_message(user_chat_id, text)
            data["message"] = f"Bot \"{bot_user.full_name}\" sent data to user {message.chat.full_name}"
            data["success"] = True
            result.update(status=200, data=data)
        except Exception as e:
            data["message"] = str(e)
            data["success"] = False
            result.update(status=400, data=data)
        finally:
            try:
                await bot.session.close()
            except:
                pass
    async_to_sync(send)()
    return result
