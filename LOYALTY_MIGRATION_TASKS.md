# ЗАВДАННЯ: Завершення міграції налаштувань лояльності на LoyaltySettings

## КОНТЕКСТ
Проект переходить від старого способу роботи з налаштуваннями лояльності до нової системи через таблицю `loyalty_settings`. Розробник уже почав роботу - видалив старі схеми incust, почав переробляти роути. Потрібно завершити міграцію для всіх об'єктів.

## ВАЖЛИВІ ПРАВИЛА
- НЕ видаляти існуючі роути
- НЕ переводити на loyalty_settings роут  
- НЕ створювати міграції БД
- НЕ робити коміти
- Зберегти API інтерфейс роутів для сумісності
- Використовувати централізовану валідацію

## ЗАВДАННЯ ПО ПОРЯДКУ

### 1. Створити централізовану валідацію лояльності
**Файл:** `bot/core/loyalty/loyalty_validator.py`
- Створити клас `LoyaltySettingsValidator` 
- Перенести логіку валідації з `LoyaltySettingsAdminService._validate_settings`
- Метод `validate_loyalty_settings(terminal_api_key, white_label_id, server_url)` 
- Повертає `terminal_id`, `loyalty_id` або викидає помилку
- Зробити доступним для всіх роутів

### 2. Переробити роут incust (профіль)
**Файли:** `bot/api/admin/router/incust/`
- Створити нові схеми для заміни старих AdminIncustSettingsSchema
- Переробити `IncustAdminService` для роботи з `loyalty_settings` таблицею
- При збереженні створювати LoyaltySettings з `profile_id = group.id`
- Використати `LoyaltySettingsValidator` для валідації
- Зберегти той же API інтерфейс (GET/PUT/POST ендпоінти)

### 3. Переробити роут products
**Файли:** `bot/api/admin/router/products/`
- Видалити поля `topup_terminal_api_key`, `topup_server_api_url` зі схем
- Додати ендпоінти `/products/{id}/loyalty_settings` (GET/PUT/DELETE)
- При збереженні створювати LoyaltySettings з `product_id`
- Залишити `incust_terminal_id` як readonly поле для відображення
- Використати централізовану валідацію

### 4. Додати налаштування лояльності для stores
**Файли:** `bot/api/admin/router/stores/`
- Додати ендпоінти `/stores/{id}/loyalty_settings` (GET/PUT/DELETE)
- Створити схеми для збереження/отримання LoyaltySettings з `store_id`
- Інтегрувати з `LoyaltySettingsValidator`

### 5. Переробити роут invoice_templates
**Файли:** `bot/api/admin/router/invoice_templates/`
- Видалити `incust_terminal_api_key` зі схем створення/оновлення
- Додати ендпоінти `/invoice_templates/{id}/loyalty_settings` (GET/PUT/DELETE)
- При збереженні створювати LoyaltySettings з `invoice_template_id`
- Залишити `incust_terminal_id` як readonly поле
- Зберегти поле `disabled_loyalty` для відключення лояльності

### 6. Додати налаштування лояльності для ewallets
**Файли:** `bot/api/platform/router/ewallets/`
- Додати ендпоінти `/ewallets/{id}/loyalty_settings` (GET/PUT/DELETE)
- Створити схеми для LoyaltySettings з `ewallet_id`
- Використати централізовану валідацію

### 7. Оновити схеми
**Файли:** `schemas/admin/*.py`
- Переконатись що всі deprecated поля лояльності видалені зі схем створення/оновлення
- Залишити тільки readonly поля (`incust_terminal_id`) для відображення
- Створити нові схеми для роботи з LoyaltySettings де потрібно

## ПОЛЯ ДЛЯ ВИКЛЮЧЕННЯ
- `incust_redeem_type` - НЕ використовується в коді, виключити зі схем
- `incust_terminal_id`, `loyalty_id` - отримуються автоматично, не вводити вручну

## ПОЛЯ ДЛЯ ЗБЕРЕЖЕННЯ  
- `incust_type_client_auth` - використовується в коді, обов'язково зберегти

## ТЕСТУВАННЯ
Після завершення перевірити:
- Валідація працює через централізований клас
- API роутів працює як раніше
- Налаштування зберігаються в loyalty_settings таблиці
- Readonly поля відображаються правильно

## СТАТУС
- ✅ Видалено старі схеми incust
- ✅ Почато переробку роутів  
- ⏳ Потрібно завершити всі 7 завдань вище