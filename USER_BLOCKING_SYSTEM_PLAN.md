# План впровадження системи блокування користувачів

## Поточний стан системи

### Структура користувача
- Таблиця `users` має поле `_status` (varchar(1)) зі значеннями: "a" (active), "r" (readonly), "d" (deactivated)
- Унікальні поля для ідентифікації: `chat_id`, `email`, `wa_phone`  
- Метод `get_allowed_scopes()` враховує статус користувача

### Процес реєстрації та авторизації
1. **Telegram**: через middleware `CreateOrUpdateUserMiddleware` → `create_or_update_messanger_user`
2. **Email**: через `AuthService.register_by_email()` та `AuthService.login()`
3. **WhatsApp**: через `create_or_update_messanger_user`

## Рекомендована архітектура системи блокування

### 1. Розширення моделі User

Додати новий статус до існуючих в `/home/<USER>/pay4say/bot/db/models/user/user.py`:
```python
STATUSES = {
    "a": "active",
    "r": "readonly", 
    "d": "deactivated",
    "b": "blocked"  # Новий статус для заблокованих
}
```

### 2. Створення таблиці для відстеження блокувань

**SQL для нової таблиці:**
```sql
CREATE TABLE user_blocks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    blocked_chat_id BIGINT NULL,
    blocked_email VARCHAR(320) NULL,
    blocked_wa_phone VARCHAR(50) NULL,
    block_type ENUM('chat_id', 'email', 'wa_phone', 'full_user') NOT NULL,
    reason VARCHAR(500) NULL,
    blocked_by_admin_id BIGINT NOT NULL,
    blocked_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_chat_id (blocked_chat_id),
    INDEX idx_email (blocked_email),
    INDEX idx_wa_phone (blocked_wa_phone)
);
```

**Модель SQLAlchemy** (створити `/home/<USER>/pay4say/bot/db/models/user_block.py`):
```python
from sqlalchemy import BigInteger, Boolean, Column, DateTime, Enum, ForeignKey, String
from sqlalchemy.orm import relationship
from db.connection import Base
from db.mixins import BaseDBModel
from utils.date_time import utcnow

class UserBlock(Base, BaseDBModel):
    __tablename__ = "user_blocks"
    
    blocked_chat_id = Column(BigInteger, nullable=True, index=True)
    blocked_email = Column(String(320), nullable=True, index=True)
    blocked_wa_phone = Column(String(50), nullable=True, index=True)
    block_type = Column(Enum('chat_id', 'email', 'wa_phone', 'full_user'), nullable=False)
    reason = Column(String(500), nullable=True)
    blocked_by_admin_id = Column(BigInteger, ForeignKey("users.id"), nullable=False)
    blocked_at = Column(DateTime, default=utcnow, nullable=False)
    is_active = Column(Boolean, default=True)
    
    blocked_by_admin = relationship("User", foreign_keys=[blocked_by_admin_id])
```

### 3. Сервіс блокування

**Створити файл** `/home/<USER>/pay4say/bot/core/user/block_service.py`:
```python
from db.models import User, UserBlock
from db import sess, db_func
from typing import Optional

class UserBlockService:
    @classmethod
    @db_func
    def is_blocked_identifier(
        cls, 
        chat_id: int = None, 
        email: str = None, 
        wa_phone: str = None
    ) -> bool:
        """Перевіряє чи заблокований будь-який з ідентифікаторів"""
        query = sess().query(UserBlock).filter(UserBlock.is_active == True)
        
        conditions = []
        if chat_id:
            conditions.append(UserBlock.blocked_chat_id == chat_id)
        if email:
            conditions.append(UserBlock.blocked_email == email)
        if wa_phone:
            conditions.append(UserBlock.blocked_wa_phone == wa_phone)
            
        if not conditions:
            return False
            
        from sqlalchemy import or_
        query = query.filter(or_(*conditions))
        return query.first() is not None
        
    @classmethod
    @db_func
    def block_user_identifiers(
        cls, 
        user_id: int, 
        reason: str = None, 
        admin_id: int = None
    ):
        """Блокує всі ідентифікатори користувача"""
        user = sess().query(User).get(user_id)
        if not user:
            return False
            
        # Встановлюємо статус користувача як заблокований
        user.status = "blocked"
        
        # Створюємо записи блокувань для всіх ідентифікаторів
        blocks = []
        if user.chat_id:
            blocks.append(UserBlock(
                blocked_chat_id=user.chat_id,
                block_type='chat_id',
                reason=reason,
                blocked_by_admin_id=admin_id
            ))
            
        if user.email:
            blocks.append(UserBlock(
                blocked_email=user.email,
                block_type='email', 
                reason=reason,
                blocked_by_admin_id=admin_id
            ))
            
        if user.wa_phone:
            blocks.append(UserBlock(
                blocked_wa_phone=user.wa_phone,
                block_type='wa_phone',
                reason=reason,
                blocked_by_admin_id=admin_id
            ))
            
        for block in blocks:
            sess().add(block)
            
        sess().commit()
        return True
        
    @classmethod
    async def check_registration_allowed(
        cls,
        chat_id: int = None,
        email: str = None, 
        wa_phone: str = None
    ) -> bool:
        """Перевіряє чи дозволена реєстрація з такими даними"""
        return not await cls.is_blocked_identifier(
            chat_id=chat_id,
            email=email,
            wa_phone=wa_phone
        )
```

### 4. Нові винятки

**Додати в** `/home/<USER>/pay4say/bot/core/user/exceptions.py`:
```python
from utils.exceptions import ErrorWithTextVariable

class UserBlockedError(ErrorWithTextVariable):
    text_variable = "user_blocked_error"
    status_code = 403

class EmailBlockedError(ErrorWithTextVariable):
    text_variable = "email_blocked_error" 
    status_code = 403

class PhoneBlockedError(ErrorWithTextVariable):
    text_variable = "phone_blocked_error"
    status_code = 403
```

### 5. Інтеграція в існуючі процеси

#### 5.1 Middleware
**Модифікувати** `/home/<USER>/pay4say/bot/core/aiogram_middlewares/create_or_update.py`:
```python
# Додати імпорт
from core.user.block_service import UserBlockService
from core.user.exceptions import UserBlockedError

# В методі create_or_update_user додати перевірку:
@staticmethod
async def create_or_update_user(
        from_user: types.User,
        bot: ClientBot | None,
        ask_agreement: bool = True
) -> CreateOrUpdateMessangerUserInfo:
    # Перевірка блокування перед створенням
    if await UserBlockService.is_blocked_identifier(chat_id=from_user.id):
        raise UserBlockedError()
        
    # Решта коду...
```

#### 5.2 Auth Service
**Модифікувати** `/home/<USER>/pay4say/bot/core/auth/services/auth.py`:
```python
# Додати імпорт
from core.user.block_service import UserBlockService
from core.user.exceptions import EmailBlockedError

# В методі register_by_email додати перевірку:
async def register_by_email(
        self,
        email: str, password: str,
        is_accepted_agreement: bool,
        # ... інші параметри
) -> tuple[User, ConfirmEmailRequest]:
    # Перевірка блокування email
    if await UserBlockService.is_blocked_identifier(email=email):
        raise EmailBlockedError()
        
    # Решта коду...
```

#### 5.3 Створення користувачів через месенджери
**Модифікувати** `/home/<USER>/pay4say/bot/db/crud/user/user.py`:
```python
# Додати перевірку в create_or_update_messanger_user
@db_func
@safe_deadlock_handler
def create_or_update_messanger_user(
        new_lang: str,
        available_langs_list: list[str],
        # ... інші параметри
) -> CreateOrUpdateMessangerUserInfo:
    
    # Додати перевірку блокування на початку
    from core.user.block_service import UserBlockService
    if not UserBlockService.check_registration_allowed(
        chat_id=chat_id, wa_phone=wa_phone
    ):
        return CreateOrUpdateMessangerUserInfo()  # Порожній результат
        
    # Решта коду...
```

### 6. API ендпоінти для адмін-панелі

**Створити** `/home/<USER>/pay4say/bot/api/admin/router/user_blocks/`:
- `router.py` - ендпоінти для блокування/розблокування
- `service.py` - бізнес логіка
- `schemas.py` - схеми для API

### 7. Локалізація

**Додати тексти помилок** у локалізаційні файли:
```json
{
  "USER_BLOCKED_ERROR": "Ваш аккаунт заблокований",
  "EMAIL_BLOCKED_ERROR": "Цей email заблокований для реєстрації", 
  "PHONE_BLOCKED_ERROR": "Цей номер телефону заблокований для реєстрації"
}
```

## Етапи впровадження

### Етап 1: Підготовка БД та моделей
- [ ] Створити SQL міграцію для таблиці `user_blocks`
- [ ] Створити модель `UserBlock` 
- [ ] Додати новий статус "blocked" до моделі `User`

### Етап 2: Сервіси та винятки
- [ ] Реалізувати `UserBlockService`
- [ ] Додати нові класи винятків
- [ ] Створити тести для сервісу

### Етап 3: Інтеграція
- [ ] Модифікувати middleware для Telegram/WhatsApp
- [ ] Модифікувати AuthService для email
- [ ] Додати перевірки в CRUD операції

### Етап 4: API та адмін-панель
- [ ] Створити API ендпоінти для блокування
- [ ] Додати інтерфейс в адмін-панель
- [ ] Реалізувати логи та аудит

### Етап 5: Тестування та деплой
- [ ] Протестувати всі сценарії блокування
- [ ] Перевірити зворотну сумісність
- [ ] Задеплоїти на тестовий сервер
- [ ] Провести навантажувальне тестування

## Переваги підходу

1. **Використання існуючої інфраструктури** - статуси користувачів вже реалізовані
2. **Гнучкість** - можна блокувати окремо chat_id, email або wa_phone  
3. **Аудит** - повна історія блокувань з причинами
4. **Централізована логіка** - всі перевірки в одному сервісі
5. **Зворотна сумісність** - не ламає існуючий функціонал
6. **Масштабованість** - легко додавати нові типи блокувань

## Додаткові можливості для майбутнього

- Автоматичне блокування за паттернами поведінки
- Тимчасове блокування з автоматичним розблокуванням
- Блокування діапазонів IP адрес
- Інтеграція з системами антифроду
- Експорт звітів по заблокованим користувачам

## Файли для модифікації

### Нові файли:
- `/home/<USER>/pay4say/bot/db/models/user_block.py`
- `/home/<USER>/pay4say/bot/core/user/block_service.py`
- `/home/<USER>/pay4say/bot/api/admin/router/user_blocks/`

### Модифікуються:
- `/home/<USER>/pay4say/bot/db/models/user/user.py`
- `/home/<USER>/pay4say/bot/core/user/exceptions.py`
- `/home/<USER>/pay4say/bot/core/aiogram_middlewares/create_or_update.py`
- `/home/<USER>/pay4say/bot/core/auth/services/auth.py`
- `/home/<USER>/pay4say/bot/db/crud/user/user.py`

## Контактна інформація

**Дата створення плану**: 09.08.2025
**Автор**: Claude Code Assistant
**Статус**: План готовий до впровадження