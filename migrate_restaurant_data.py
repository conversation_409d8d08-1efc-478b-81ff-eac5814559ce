#!/usr/bin/env python3
"""
Script to migrate restaurant data from production store 332 to local brand_id=14
"""
import pymysql
from datetime import datetime
import json

# Database connections
PROD_DB = {
    'host': '*************',
    'user': 'payforsay',
    'password': 'PaY4SaY',
    'database': 'payforsay',
    'charset': 'utf8mb4'
}

LOCAL_DB = {
    'host': 'localhost',
    'user': 'payforsay',
    'password': 'PaY4SaY',
    'database': 'payforsay',
    'charset': 'utf8mb4'
}

# Source and target configurations
SOURCE_STORE_ID = 332
SOURCE_BRAND_ID = 27
TARGET_BRAND_ID = 14  # China phones profile
TARGET_GROUP_ID = 25

def get_connection(config):
    """Create database connection"""
    return pymysql.connect(**config)

def clean_existing_data(conn):
    """Clean existing data for brand_id=14"""
    print("Cleaning existing data for brand_id=14...")
    with conn.cursor() as cursor:
        # Delete in correct order to avoid foreign key constraints
        # First delete child records
        try:
            cursor.execute("DELETE FROM products_to_categories WHERE product_id IN (SELECT id FROM store_products WHERE brand_id = %s)", (TARGET_BRAND_ID,))
            deleted = cursor.rowcount
            if deleted > 0:
                print(f"  Deleted {deleted} rows from products_to_categories")
        except Exception as e:
            print(f"  Warning: Could not clean products_to_categories: {e}")
            
        try:
            cursor.execute("DELETE FROM attribute_groups_to_products WHERE product_id IN (SELECT id FROM store_products WHERE brand_id = %s)", (TARGET_BRAND_ID,))
            deleted = cursor.rowcount  
            if deleted > 0:
                print(f"  Deleted {deleted} rows from attribute_groups_to_products")
        except Exception as e:
            print(f"  Warning: Could not clean attribute_groups_to_products: {e}")
            
        # Then main tables
        tables = [
            ('store_products', 'brand_id'),
            ('store_categories', 'brand_id'),
            ('loyalty_settings', 'brand_id'),
            ('object_payment_settings', 'store_id', 'stores')
        ]
        
        for table_info in tables:
            try:
                if len(table_info) == 3:  # Special case for object_payment_settings
                    table, column, ref_table = table_info
                    cursor.execute(f"DELETE FROM {table} WHERE {column} IN (SELECT id FROM {ref_table} WHERE brand_id = %s)", (TARGET_BRAND_ID,))
                else:
                    table, column = table_info
                    cursor.execute(f"DELETE FROM {table} WHERE {column} = %s", (TARGET_BRAND_ID,))
                deleted = cursor.rowcount
                if deleted > 0:
                    print(f"  Deleted {deleted} rows from {table}")
            except Exception as e:
                print(f"  Warning: Could not clean {table}: {e}")
                
        # Finally delete stores
        try:
            cursor.execute("DELETE FROM stores WHERE brand_id = %s", (TARGET_BRAND_ID,))
            deleted = cursor.rowcount
            if deleted > 0:
                print(f"  Deleted {deleted} rows from stores")
        except Exception as e:
            print(f"  Warning: Could not clean stores: {e}")
        
        conn.commit()

def migrate_stores(prod_conn, local_conn):
    """Migrate store data"""
    print("\nMigrating stores...")
    with prod_conn.cursor(pymysql.cursors.DictCursor) as prod_cursor:
        # Get store from production
        prod_cursor.execute("""
            SELECT * FROM stores WHERE id = %s
        """, (SOURCE_STORE_ID,))
        store = prod_cursor.fetchone()
        
        if not store:
            print("Store not found!")
            return None
            
        # Prepare store data for local
        store['brand_id'] = TARGET_BRAND_ID
        store['name'] = f"Migrated Restaurant - {store.get('name', 'Unknown')}"
        store['id'] = None  # Will get new ID
        # Clear media and task references that might not exist locally
        store['media_id'] = None
        store['description_media_id'] = None
        store['offer_media_id'] = None
        store['task_id'] = None
        
        # Insert into local database
        with local_conn.cursor() as local_cursor:
            columns = [k for k in store.keys() if k != 'id' and store[k] is not None]
            values = [store[k] for k in columns]
            placeholders = ', '.join(['%s'] * len(columns))
            column_names = ', '.join([f'`{col}`' for col in columns])
            
            query = f"INSERT INTO stores ({column_names}) VALUES ({placeholders})"
            local_cursor.execute(query, values)
            new_store_id = local_cursor.lastrowid
            local_conn.commit()
            
            print(f"  Created store with ID: {new_store_id}")
            return new_store_id

def migrate_categories(prod_conn, local_conn):
    """Migrate categories"""
    print("\nMigrating categories...")
    category_mapping = {}
    
    with prod_conn.cursor(pymysql.cursors.DictCursor) as prod_cursor:
        prod_cursor.execute("""
            SELECT * FROM store_categories 
            WHERE brand_id = %s AND is_deleted = 0
            ORDER BY father_category_id, position
        """, (SOURCE_BRAND_ID,))
        categories = prod_cursor.fetchall()
        
        print(f"  Found {len(categories)} categories to migrate")
        
        # First pass - migrate root categories
        for cat in categories:
            if cat['father_category_id'] is None or cat['father_category_id'] == 0:
                old_id = cat['id']
                cat['id'] = None
                cat['brand_id'] = TARGET_BRAND_ID
                # Clear media references that might not exist locally
                cat['media_id'] = None
                
                with local_conn.cursor() as local_cursor:
                    columns = [k for k in cat.keys() if k != 'id' and cat[k] is not None]
                    values = [cat[k] for k in columns]
                    placeholders = ', '.join(['%s'] * len(columns))
                    column_names = ', '.join([f'`{col}`' for col in columns])
                    
                    query = f"INSERT INTO store_categories ({column_names}) VALUES ({placeholders})"
                    local_cursor.execute(query, values)
                    new_id = local_cursor.lastrowid
                    category_mapping[old_id] = new_id
                    local_conn.commit()
        
        # Second pass - migrate child categories
        for cat in categories:
            if cat['father_category_id'] and cat['father_category_id'] != 0:
                if cat['id'] not in category_mapping:
                    old_id = cat['id']
                    cat['id'] = None
                    cat['brand_id'] = TARGET_BRAND_ID
                    cat['father_category_id'] = category_mapping.get(cat['father_category_id'])
                    # Clear media references that might not exist locally
                    cat['media_id'] = None
                    
                    if cat['father_category_id']:  # Only if parent was migrated
                        with local_conn.cursor() as local_cursor:
                            columns = [k for k in cat.keys() if k != 'id' and cat[k] is not None]
                            values = [cat[k] for k in columns]
                            placeholders = ', '.join(['%s'] * len(columns))
                            column_names = ', '.join([f'`{col}`' for col in columns])
                            
                            query = f"INSERT INTO store_categories ({column_names}) VALUES ({placeholders})"
                            local_cursor.execute(query, values)
                            new_id = local_cursor.lastrowid
                            category_mapping[old_id] = new_id
                            local_conn.commit()
    
    print(f"  Migrated {len(category_mapping)} categories")
    return category_mapping

def migrate_products(prod_conn, local_conn, category_mapping, new_store_id):
    """Migrate products"""
    print("\nMigrating products...")
    product_mapping = {}
    
    with prod_conn.cursor(pymysql.cursors.DictCursor) as prod_cursor:
        prod_cursor.execute("""
            SELECT * FROM store_products 
            WHERE brand_id = %s AND is_deleted = 0
            ORDER BY position
        """, (SOURCE_BRAND_ID,))
        products = prod_cursor.fetchall()
        
        print(f"  Found {len(products)} products to migrate")
        
        for product in products:
            old_id = product['id']
            product['id'] = None
            product['brand_id'] = TARGET_BRAND_ID
            # Clear media and task references that might not exist locally
            product['media_id'] = None
            product['description_media_id'] = None
            product['offer_media_id'] = None
            product['thumbnail_media_id'] = None
            product['task_id'] = None
            
            with local_conn.cursor() as local_cursor:
                columns = [k for k in product.keys() if k != 'id' and product[k] is not None]
                values = [product[k] for k in columns]
                placeholders = ', '.join(['%s'] * len(columns))
                column_names = ', '.join([f'`{col}`' for col in columns])
                
                query = f"INSERT INTO store_products ({column_names}) VALUES ({placeholders})"
                local_cursor.execute(query, values)
                new_id = local_cursor.lastrowid
                product_mapping[old_id] = new_id
                local_conn.commit()
                
                # Link product to store
                if new_store_id:
                    local_cursor.execute("""
                        INSERT INTO product_to_stores (product_id, store_id)
                        VALUES (%s, %s)
                        ON DUPLICATE KEY UPDATE store_id = VALUES(store_id)
                    """, (new_id, new_store_id))
                    local_conn.commit()
        
        # Migrate product-category relationships
        print("  Migrating product-category relationships...")
        # Get relationships by joining with products to filter by brand
        prod_cursor.execute("""
            SELECT ptc.* FROM products_to_categories ptc
            JOIN store_products sp ON sp.id = ptc.product_id
            WHERE sp.brand_id = %s
        """, (SOURCE_BRAND_ID,))
        relationships = prod_cursor.fetchall()
        
        for rel in relationships:
            new_product_id = product_mapping.get(rel['product_id'])
            new_category_id = category_mapping.get(rel['category_id'])
            
            if new_product_id and new_category_id:
                with local_conn.cursor() as local_cursor:
                    local_cursor.execute("""
                        INSERT INTO products_to_categories (product_id, category_id)
                        VALUES (%s, %s)
                        ON DUPLICATE KEY UPDATE category_id = VALUES(category_id)
                    """, (new_product_id, new_category_id))
                    local_conn.commit()
    
    print(f"  Migrated {len(product_mapping)} products")
    return product_mapping

def migrate_loyalty_settings(prod_conn, local_conn, new_store_id):
    """Migrate loyalty settings"""
    print("\nMigrating loyalty settings...")
    
    with prod_conn.cursor(pymysql.cursors.DictCursor) as prod_cursor:
        prod_cursor.execute("""
            SELECT * FROM loyalty_settings 
            WHERE store_id = %s AND is_enabled = 1
        """, (SOURCE_STORE_ID,))
        settings = prod_cursor.fetchall()
        
        if not settings:
            print("  No loyalty settings found for source store")
            return
        
        for setting in settings:
            setting['id'] = None
            setting['store_id'] = new_store_id
            setting['brand_id'] = TARGET_BRAND_ID
            setting['profile_id'] = None  # Will be set to group's profile
            setting['name'] = f"Migrated from Store 332 - {datetime.now().strftime('%Y-%m-%d')}"
            setting['time_created'] = datetime.now()
            setting['time_updated'] = datetime.now()
            
            with local_conn.cursor() as local_cursor:
                columns = [k for k in setting.keys() if k != 'id' and setting[k] is not None]
                values = [setting[k] for k in columns]
                placeholders = ', '.join(['%s'] * len(columns))
                column_names = ', '.join([f'`{col}`' for col in columns])
                
                query = f"INSERT INTO loyalty_settings ({column_names}) VALUES ({placeholders})"
                local_cursor.execute(query, values)
                new_id = local_cursor.lastrowid
                local_conn.commit()
                
                print(f"  Created loyalty setting with ID: {new_id}")

def main():
    """Main migration function"""
    print("Starting migration from production store 332 to local brand_id=14...")
    
    try:
        # Connect to databases
        prod_conn = get_connection(PROD_DB)
        local_conn = get_connection(LOCAL_DB)
        
        # Clean existing data
        clean_existing_data(local_conn)
        
        # Migrate data
        new_store_id = migrate_stores(prod_conn, local_conn)
        category_mapping = migrate_categories(prod_conn, local_conn)
        product_mapping = migrate_products(prod_conn, local_conn, category_mapping, new_store_id)
        migrate_loyalty_settings(prod_conn, local_conn, new_store_id)
        
        # Summary
        print("\n" + "="*50)
        print("Migration completed successfully!")
        print(f"  New store ID: {new_store_id}")
        print(f"  Categories migrated: {len(category_mapping)}")
        print(f"  Products migrated: {len(product_mapping)}")
        print("="*50)
        
    except Exception as e:
        print(f"\nError during migration: {e}")
        import traceback
        traceback.print_exc()
    finally:
        prod_conn.close()
        local_conn.close()

if __name__ == "__main__":
    main()