import asyncio

import pytest
from aiogram.types import InlineKeyboardMarkup

import config as cfg
from utils.platform_admins import send_message_to_platform_admins
from utils.redefined_classes import Bot


@pytest.mark.asyncio
async def test_real_send_message():
    """Тест для реальної відправки повідомлень."""
    message = "Тестове повідомлення для перевірки реальної відправки"
    keyboard = InlineKeyboardMarkup()

    bot = Bot(cfg.ROOT_BOT_API_TOKEN)

    Bot.set_current(bot)

    result = await send_message_to_platform_admins(message, keyboard)

    assert result is not None
    assert len(result) > 0

    await bot.session.close()


if __name__ == "__main__":
    asyncio.run(test_real_send_message())