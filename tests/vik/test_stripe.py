import pytest
from datetime import datetime, timed<PERSON>ta
from stripe import StripeClient

from core.payment.payment_processor.providers.stripe import create_or_update_stripe_webhook
from db import DBSession
from db.crud import get_stripe_webhook_secret

# Maguetta LONDON...
# secret_key = "***********************************************************************************************************"
secret_key = "***********************************************************************************************************"

@pytest.mark.asyncio
async def test_setup_stripe_webhook():
    """Тест налаштування вебхука Stripe."""
    with DBSession() as session:
        whs = await get_stripe_webhook_secret(secret_key)

    print(f"{whs=}")

    new_webhook_secret = await create_or_update_stripe_webhook(
        secret_key,
        'https://api.payforsay.com/payments/callback/stripe',
    )

    print(f"{new_webhook_secret=}")
    assert new_webhook_secret is not None

@pytest.mark.asyncio
async def test_get_transactions():
    """Тест отримання транзакцій."""
    client = StripeClient(secret_key)
    # Отримання списку останніх транзакцій
    payment_intents = client.payment_intents.list(
        {"limit": 50}
    )

    for payment in payment_intents.data:
        customer_email = None
        if payment.get("customer"):  # Перевірка, чи є інформація про клієнта
            customer = client.customers.retrieve(payment["customer"])
            customer_email = customer.get("email", "Невідомо")

        print(
            f"ID: {payment['id']}, Amount: {payment['amount'] / 100:.2f} "
            f"{payment['currency'].upper()}, "
            f"Status: {payment['status']}, Customer Email: {customer_email}, "
            f"Description: {payment.get('description', 'Немає опису')}, Created: "
        )
    assert len(payment_intents.data) > 0

@pytest.mark.asyncio
async def test_get_checkout_sessions():
    """Тест отримання сесій оформлення замовлення."""
    client = StripeClient(secret_key)
    sessions = client.checkout.sessions.list({"limit": 50})
    
    for session in sessions.data:
        print(f"Session ID: {session['id']}")
        print(f"Amount: {session['amount_total'] / 100:.2f} {session['currency'].upper()}")
        print(f"Status: {session['status']}")
        print(f"Customer Email: {session.get('customer_email', 'Немає email')}")
        print(f"Created: {datetime.fromtimestamp(session['created'])}")
        print("-" * 50)
    
    assert len(sessions.data) > 0 