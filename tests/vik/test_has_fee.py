import pytest
from sqlalchemy import or_

from db import DBSession
from db import crud

@pytest.mark.asyncio
async def test_get_payment_methods():
    """Тест отримання методів оплати."""
    try:
        with DBSession():
            payment_methods = await crud.get_payment_methods(12, None, True)
            
            # Перевірки
            assert payment_methods is not None, "Payment methods should not be None"
            assert isinstance(payment_methods, list), "Payment methods should be a list"
            
            # Виводимо інформацію про знайдені методи оплати
            print(f"Found {len(payment_methods)} payment methods")
            for method in payment_methods:
                print(f"Payment method: {method}")
                
    except Exception as e:
        print(str(e))
        raise
    else:
        print("success")

    print("TESTED") 