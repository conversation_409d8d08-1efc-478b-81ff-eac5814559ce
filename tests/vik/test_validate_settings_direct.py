#!/usr/bin/env python3
"""
Простий тест для прямого тестування функції _validate_settings 
з нового InCust адаптера.

Цей скрипт НЕ запускається автоматично - тільки для ручного тестування.
"""

import asyncio
import os
import sys

from core.loyalty import incust

# Додаємо шлях до bot директорії для імпортів
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../bot'))

from incust_terminal_api_client.exceptions import (
    ApiException as TerminalApiException,
    UnauthorizedException
)

from db.models import LoyaltySettings

# Тестові значення з оригінального файлу test_loyalty_settings.py
TEST_VALUES = {
    "url": "https://testapi.incust.com",
    "whitelabel_id": "100341705844195342",
    "terminal_api_key": "eyJpZCI6IjVjM2Q4Y2Q1LTMxMWItMTFlZS04YzNmLTAyMDAwMDk1YmI3ZiJ9.ZR_oTQ.V8lD-06iFSpnB1SuW_LO0UxuLmM"
}


# Проста схема для результату валідації
class LoyaltySettingsValidateSchema:
    def __init__(self, is_valid, errors=None, terminal_info=None, terminal_id=None, loyalty_id=None):
        self.is_valid = is_valid
        self.errors = errors
        self.terminal_info = terminal_info
        self.terminal_id = terminal_id
        self.loyalty_id = loyalty_id


async def validate_settings(
    white_label_id: str,
    terminal_api_key: str,
    server_url: str,
    lang: str = "uk"
) -> LoyaltySettingsValidateSchema:
    """Валідація налаштувань лояльності через API."""
    
    errors = []
    terminal_info = None
    terminal_id = None
    loyalty_id = None
    
    try:
        # Створюємо тимчасові налаштування для валідації
        temp_settings = LoyaltySettings(
            white_label_id=white_label_id,
            terminal_api_key=terminal_api_key,
            loyalty_id="temp_loyalty_id",
            server_url=server_url,
            terminal_id="temp_terminal_id"
        )

        try:
            async with incust.term.SettingsApi(temp_settings, lang=lang) as api:
                terminal_settings = await api.settings()
            
            if terminal_settings:
                terminal_id = str(terminal_settings.id) if terminal_settings.id else None

                loyalty_id = None

                if hasattr(terminal_settings.loyalty, 'additional_properties') and 'id' in terminal_settings.loyalty.additional_properties:
                    loyalty_id = str(terminal_settings.loyalty.additional_properties['id'])
                
                terminal_info = {
                    "terminal_id": terminal_id,
                    "terminal_name": terminal_settings.title,
                    "loyalty_id": loyalty_id,
                }
                
                # Перевіряємо що отримали обидва значення
                if not terminal_id:
                    errors.append("terminal id not received from api")
                if not loyalty_id:
                    errors.append("loyalty id not received from api")
            else:
                errors.append("invalid terminal settings")
                
        except UnauthorizedException as e:
            # Обробляємо помилку 401
            errors.append("invalid terminal api key")
        except TerminalApiException as e:
            # Обробляємо інші помилки API
            if hasattr(e, 'status') and e.status == 404:
                errors.append("terminal not found")
            else:
                status_code = getattr(e, 'status', 'unknown')
                errors.append(f"terminal validation error: Status {status_code}")
        except Exception as e:
            import traceback
            traceback.print_exc()
            error_msg = str(e).lower()
            if "connection" in error_msg or "name or service not known" in error_msg:
                errors.append("cannot connect to loyalty server")
            else:
                errors.append(f"terminal validation error: {str(e)}")
                
    except Exception as e:
        errors.append(f"unexpected validation error: {str(e)}")
    
    return LoyaltySettingsValidateSchema(
        is_valid=len(errors) == 0,
        errors=errors if errors else None,
        terminal_info=terminal_info,
        terminal_id=terminal_id,
        loyalty_id=loyalty_id
    )


async def test_validate_settings_correct():
    """Тест валідації з правильними налаштуваннями."""
    print("=== Тест з правильними налаштуваннями ===")
    
    try:
        result = await validate_settings(
            white_label_id=TEST_VALUES["whitelabel_id"],
            terminal_api_key=TEST_VALUES["terminal_api_key"],
            server_url=TEST_VALUES["url"],
            lang="uk"
        )
        
        print(f"✓ Валідація завершена")
        print(f"  is_valid: {result.is_valid}")
        print(f"  terminal_id: {result.terminal_id}")
        print(f"  loyalty_id: {result.loyalty_id}")
        
        if result.terminal_info:
            print(f"  terminal_info: {result.terminal_info}")
            
        if result.errors:
            print(f"  errors: {result.errors}")
            
        return result
        
    except Exception as e:
        print(f"❌ Помилка при валідації: {e}")
        import traceback
        traceback.print_exc()
        return None


async def main():
    """Головна функція тестування."""
    print("🧪 Тестування функції _validate_settings з новим InCust адаптером")
    print("=" * 70)
    
    # Тест з правильними налаштуваннями
    result = await test_validate_settings_correct()
    
    print("\n" + "=" * 70)
    print("📊 Результат:")
    
    if result:
        if result.is_valid:
            print("✅ УСПІШНО - новий адаптер працює правильно!")
            print(f"   Terminal ID: {result.terminal_id}")
            print(f"   Loyalty ID: {result.loyalty_id}")
        else:
            print("❌ НЕУСПІШНО - є помилки:")
            if result.errors:
                for error in result.errors:
                    print(f"   - {error}")
            
            # Якщо помилка 401, можливо токен застарів або неправильний
            if result.errors and any("invalid terminal api key" in str(e) for e in result.errors):
                print("\n💡 Можливо API ключ застарів або неправильний.")
                print("   Перевірте актуальність тестових даних в TEST_VALUES.")
    else:
        print("❌ КРИТИЧНА ПОМИЛКА - тест не зміг завершитися")
    
    print("\n🎉 Тестування завершено!")


if __name__ == "__main__":
    # Цей скрипт треба запускати з активованим віртуальним середовищем
    # та з правильно налаштованою базою даних
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️  Тестування перервано користувачем")
    except Exception as e:
        print(f"\n\n💥 Критична помилка: {e}")
        import traceback
        traceback.print_exc()