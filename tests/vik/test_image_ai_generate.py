import pytest
import json
import re
import aiohttp
import openai
import asyncio
import time
from config import OPENAI_API_KEY

openai.api_key = OPENAI_API_KEY
client = openai.AsyncClient(api_key=OPENAI_API_KEY)

PROMPT = """
Title: {dish_name}
Description: {dish_description}
"""

PROFILE_SYSTEM_USER_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjI3NTI1IiwidHlwZSI6InVzZXIiLCJzY29wZXMiOm51bGwsImV4cCI6MTcyNjQ5Mjg4M30.Js1TzWcLE1aMF6cKYkZkuXho5ZYL1F5QMYQ3C8Tyy7I"
PROFILE_PRODUCTS_LIMIT = 20
PROFILE_ID = 1728
BASE_7LOC_API_URL = "https://api.7loc.com"

MAX_CONCURRENT_REQUESTS = 5
semaphore_dalle2 = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
semaphore_dalle3 = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)

async def generate_dalle_prompt(dish_name: str, dish_description: str):
    """Генерує промпт для DALL-E на основі назви та опису страви."""
    start = time.time()
    try:
        response = await client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "Your task is to generate a highly descriptive and effective prompt for DALL-E to create a realistic, high-quality photograph of a dish.",
                },
                {
                    "role": "user",
                    "content": PROMPT.format(dish_name=dish_name, dish_description=dish_description),
                }
            ],
            model="gpt-4",
            temperature=0.3,
            max_tokens=150,
        )
        return response.choices[0].message.content.strip()
    finally:
        end = time.time()
        print(f"{dish_name}: Executing generate_dalle_prompt took {end - start} seconds")

async def generate_image(model: str, prompt: str, dish_name: str, retry: int = 0):
    """Генерує зображення за допомогою DALL-E."""
    params = {
        "model": model,
        "prompt": prompt,
        "n": 1,
        "size": "1024x1024"
    }

    if model == "dall-e-3":
        params["quality"] = "standard"
        params["style"] = "natural"
        semaphore = semaphore_dalle3
    else:
        semaphore = semaphore_dalle2

    try:
        async with semaphore:
            response = await client.images.generate(**params)
            return response.data[0].url
    except openai.RateLimitError as e:
        timeout = 10 * (retry + 1)
        print(f"{model}: Rate limit hit for {dish_name}. Waiting {timeout} seconds before retry.")
        await asyncio.sleep(timeout)
        return await generate_image(model, prompt, dish_name, retry + 1)

@pytest.mark.asyncio
async def test_generate_dalle_prompt():
    """Тест генерації промпту для DALL-E."""
    dish_name = "Борщ український"
    dish_description = "Традиційний український борщ з яловичиною"
    
    prompt = await generate_dalle_prompt(dish_name, dish_description)
    assert prompt is not None, "Prompt should not be None"
    assert len(prompt) > 0, "Prompt should not be empty"
    print(f"Generated prompt: {prompt}")

@pytest.mark.asyncio
async def test_generate_image():
    """Тест генерації зображення за допомогою DALL-E."""
    prompt = "A photorealistic image of traditional Ukrainian borsch with beef"
    dish_name = "Борщ"
    
    # Тестуємо обидві моделі
    for model in ["dall-e-2", "dall-e-3"]:
        url = await generate_image(model, prompt, dish_name)
        assert url is not None, f"{model} should return image URL"
        assert url.startswith("http"), f"{model} should return valid URL"
        print(f"{model} generated image URL: {url}")

@pytest.mark.asyncio
async def test_get_products():
    """Тест отримання продуктів з API."""
    products = await get_products()
    assert isinstance(products, list), "Should return list of products"
    assert len(products) > 0, "Should return at least one product"
    for product in products:
        assert "dish" in product, "Each product should have 'dish' field"
        assert "description" in product, "Each product should have 'description' field"
    print(f"Retrieved {len(products)} products") 