import asyncio

import pytest

from core.admin_notification.service import create_system_notification
from core.kafka.producer import producer
from db import DBSession, models
from schemas import (
    NotificationLevel, NotificationRecipientType, SystemNotificationCategory,
    SystemNotificationType,
)


@pytest.mark.asyncio
async def test_send_user_notification():
    """Тест відправки сповіщення користувачу."""
    await producer.initialise()

    with DBSession():
        user = await models.User.get_by_id(2)
        group = await models.Group.get(2)

        assert user is not None, "Test user not found"
        assert group is not None, "Test group not found"

        # Створюємо сповіщення для користувача
        user_notification = await create_system_notification(
            scope="profile:user",
            group_id=group.id,
            category=SystemNotificationCategory.GENERAL,
            type_notification=SystemNotificationType.GENERAL_NOTIFICATION,
            title="user notification test title",
            content="user notification test content",
            level=NotificationLevel.INFO,
            recipient_type=NotificationRecipientType.USER,
            recipient_id=user.id
        )

        assert user_notification is not None, "User notification not created"
        print(f"{user_notification.title=}")

        # Додаткові перевірки
        assert user_notification.category == SystemNotificationCategory.GENERAL, "Wrong category"
        assert user_notification.type_notification == SystemNotificationType.GENERAL_NOTIFICATION, "Wrong type"
        assert user_notification.level == NotificationLevel.INFO, "Wrong level"
        assert user_notification.recipient_type == NotificationRecipientType.USER, "Wrong recipient type"
        assert user_notification.recipient_id == user.id, "Wrong recipient ID"

        # Перевіряємо, що сповіщення збережено в базі даних
        notifications = await models.SystemNotification.get_list(
            recipient_type=NotificationRecipientType.USER,
            recipient_id=user.id,
            is_deleted=False,
        )

        assert len(notifications) > 0, "No notifications found in database"

        # Знаходимо наше сповіщення серед усіх
        found = False
        for notification in notifications:
            if notification.id == user_notification.id:
                found = True
                break

        assert found, "Created notification not found in database"

        # Перевіряємо, що можемо отримати сповіщення за ID
        retrieved_notification = await models.SystemNotification.get(user_notification.id)
        assert retrieved_notification is not None, "Cannot retrieve notification by ID"
        assert retrieved_notification.title == user_notification.title, "Wrong title in retrieved notification"
        assert retrieved_notification.content == user_notification.content, "Wrong content in retrieved notification"

        print(f"Successfully created and verified user notification: {user_notification.id}")


if __name__ == "__main__":
    asyncio.run(test_send_user_notification())
