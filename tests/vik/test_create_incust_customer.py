import pytest
import logging
from psutils.exceptions import ErrorWithTextVariable

from core.incust import IncustService
from db import DBSession, sess
from db.models import Brand, User
from utils.text import f

@pytest.mark.asyncio
async def test_create_incust_customer():
    """Тест створення клієнта Incust."""
    try:
        with DBSession():
            user = await User.get_by_id(1)  # max semka
            brand = await Brand.get(27)  # restaurant
            
            assert user is not None, "User not found"
            assert brand is not None, "Brand not found"
            
            print(f"{user.id=} {user.name=}")
            
            incust_service = await IncustService.setup(user, brand)
            incust_customer = await incust_service.incust_customer
            sess().commit()
            
            print(f"{incust_customer.brand_id=}, {incust_customer.user_id=}, "
                  f"{incust_customer.token=}, {incust_customer.push_token}, "
                  f"{incust_customer.push_token_wl_id=}")
            
            assert incust_customer is not None, "Incust customer not created"
            assert incust_customer.brand_id == brand.id, "Wrong brand_id"
            assert incust_customer.user_id == user.id, "Wrong user_id"
            assert incust_customer.token is not None, "Token is None"

    except ErrorWithTextVariable as e:
        logging.error(e, exc_info=True)
        print(await f(e.text_variable, "uk", **e.text_kwargs))
        raise
    else:
        print("success") 