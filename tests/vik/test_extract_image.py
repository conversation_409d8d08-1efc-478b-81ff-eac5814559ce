import pytest
import cv2
import numpy as np
import os

def extract_logo(image_path: str, output_path: str):
    # Завантажуємо зображення
    image = cv2.imread(image_path)

    if image is None:
        print(f"Не вдалося відкрити зображення за шляхом: {image_path}")
        return False

    # Перетворюємо зображення з BGR у HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

    # Встановлюємо діапазони кольорів для фону
    lower_bg = np.array([0, 0, 200])  # Низькі значення HSV для фону
    upper_bg = np.array([180, 55, 255])  # Високі значення HSV для фону

    # Створюємо маску, де фон (світлий) буде білим, а все інше — чорним
    mask = cv2.inRange(hsv, lower_bg, upper_bg)

    # Інвертуємо маску, щоб фон був чорним, а логотип — білим
    mask_inv = cv2.bitwise_not(mask)

    # Створюємо прозоре зображення (логотип на прозорому фоні)
    transparent_image = cv2.cvtColor(image, cv2.COLOR_BGR2BGRA)
    transparent_image[:, :, 3] = mask_inv  # Використовуємо інверсовану маску як альфа-канал

    # Зберігаємо результат
    cv2.imwrite(output_path, transparent_image)
    print(f"Логотип збережено у {output_path}")
    return True

def test_extract_logo(tmp_path):
    """Тест вилучення логотипу з зображення."""
    # Створюємо тестове зображення
    test_image_path = os.path.join(tmp_path, "test_logo.png")
    test_output_path = os.path.join(tmp_path, "test_logo_output.png")
    
    # Створюємо просте тестове зображення
    test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255  # Біле зображення
    cv2.rectangle(test_image, (30, 30), (70, 70), (0, 0, 0), -1)  # Чорний квадрат посередині
    cv2.imwrite(test_image_path, test_image)
    
    # Перевіряємо функцію extract_logo
    assert os.path.exists(test_image_path), "Test image was not created"
    
    result = extract_logo(test_image_path, test_output_path)
    assert result is True, "Logo extraction failed"
    assert os.path.exists(test_output_path), "Output file was not created"
    
    # Перевіряємо, що вихідний файл має правильний формат (BGRA)
    output_image = cv2.imread(test_output_path, cv2.IMREAD_UNCHANGED)
    assert output_image is not None, "Cannot read output image"
    assert output_image.shape[2] == 4, "Output image should have 4 channels (BGRA)"
    
    print("Logo extraction test passed successfully") 