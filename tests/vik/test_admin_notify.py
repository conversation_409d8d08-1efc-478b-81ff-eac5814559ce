import asyncio

import pytest

from config import ADMIN_HOST
from core.admin_notification.service import create_system_notification
from core.kafka.producer import producer
from db import DBSession
from schemas import (
    NotificationLevel, SystemNotificationCategory,
    SystemNotificationType,
)
from utils.text import f


@pytest.mark.asyncio
async def test_send_admin_notification():
    """Тест відправки сповіщення адміністратору."""
    await producer.initialise()
    with DBSession():
        admin_notify = await create_system_notification(
            "profile:admin",
            2,
            SystemNotificationCategory.BILLING,
            SystemNotificationType.SUBSCRIPTION_PAST_DUE,
            title=await f(
                f"billing {SystemNotificationType.SUBSCRIPTION_PAST_DUE.value} title", 'uk'
            ),
            content=await f(
                f"billing {SystemNotificationType.SUBSCRIPTION_PAST_DUE.value} content",
                'uk',
                billing_url=f"{ADMIN_HOST}/uk/2/billing"
            ),
            level=NotificationLevel.ERROR,
        )

        assert admin_notify is not None, "Admin notification not created"
        print(f"{admin_notify.title=}")
        
        # Додаткові перевірки
        assert admin_notify.category == SystemNotificationCategory.BILLING, "Wrong category"
        assert admin_notify.type_notification == SystemNotificationType.SUBSCRIPTION_PAST_DUE, "Wrong type"
        assert admin_notify.level == NotificationLevel.ERROR, "Wrong level"


if __name__ == "__main__":
    asyncio.run(test_send_admin_notification())