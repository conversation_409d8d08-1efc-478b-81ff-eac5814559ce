import logging

import pytest
from api.client.payments.payment_providers.funcs import (
    ContextualException, payment_error_handler, set_context,
)
from api.client.payments.payment_providers.pl24.funcs import get_pl24_payment_data
from core.payment.exception import (
    PaymentStripeInvalidTypeError,
)

from core.invoice import finalize_payment
from db import DBSession, crud
from schemas import PaymentCallBackData

debugger = logging.getLogger('debugger')

@pytest.mark.asyncio
@payment_error_handler()
async def test_stripe_webhook_exceptions():
    """Тест обробки винятків вебхуків Stripe."""
    context = {}
    response_status = ""
    result = {}

    with DBSession():
        try:
            payment_uuid = "c03e8fac-47ed-4f33-a41d-5f459f917cb1"
            payment, invoice, store_order, brand = await crud.get_payment_data_(payment_uuid)
            assert payment is not None, "Payment not found"
            assert invoice is not None, "Invoice not found"
            
            context = set_context(context, payment, invoice, store_order, brand)

            crc, merchant_id, pos_id, secret, is_sandbox = get_pl24_payment_data(5, None)
            payment_settings_id = 5  # Додано визначення payment_settings_id

            payment_data = PaymentCallBackData(
                payment_method="pl24",
                payment_uuid=payment_uuid,
                callback_data={},
                is_sandbox=is_sandbox,
                payment_settings_id=payment_settings_id,
                status="success",
                external_id=f"ext_{payment_uuid}"
            )

            response_status = "ok"
            await finalize_payment(payment_data, invoice, payment, brand, store_order)

            # Тестування різних винятків
            payment_settings_data = "test payment_settings_data"
            event = {"type": "test"}
            event_map = {"test1": "v1", "test2": "v2", "test3": "v3"}
            
            with pytest.raises(PaymentStripeInvalidTypeError) as exc_info:
                raise PaymentStripeInvalidTypeError(
                    f'{event["type"]=} not in {event_map.keys()=}'
                )
            
            assert str(exc_info.value).startswith("event['type']='test'"), "Wrong exception message"

        except Exception as e:
            response_status = "failed"
            raise ContextualException(e, context)
        finally:
            print(f"Test completed, {response_status=}, {result=}")
            
        print("TESTED") 