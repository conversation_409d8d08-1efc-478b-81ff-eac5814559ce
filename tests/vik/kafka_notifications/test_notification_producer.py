"""
Тест для перевірки відправки повідомлень про оплату рахунків через Kafka
"""
import asyncio
import sys
from pathlib import Path

# Додаємо шлях до проекту
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "bot"))

from core.kafka.producer.functions import add_invoice_payment_notification
from db import DBSession
from loggers import JSONLogger


async def test_invoice_payment_notification():
    """Тест відправки повідомлення про оплату рахунку"""
    
    logger = JSONLogger("test_invoice_payment_notification")
    
    try:
        # Приклад даних для тесту
        test_data = {
            "group_id": 1,
            "brand_id": 1,
            "invoice_id": 123456,
            "invoice_user_id": 1,
            "is_full_bonuses_payment": False,
            "terminal_key": "test_terminal_key",
            "lang": "uk",
            "menu_in_store_id": None,
        }
        
        logger.debug("Sending test invoice payment notification", test_data)
        
        # Відправляємо тестове повідомлення
        result = await add_invoice_payment_notification(**test_data, logger=logger)
        
        logger.debug("Test notification sent successfully", {"result": result})
        print(f"✅ Test passed! Notification sent: {result}")
        
    except Exception as e:
        logger.error("Test failed", e)
        print(f"❌ Test failed: {e}")
        raise


async def main():
    """Головна функція для запуску тесту"""
    with DBSession():
        await test_invoice_payment_notification()


if __name__ == "__main__":
    asyncio.run(main())
