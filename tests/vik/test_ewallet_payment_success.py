import asyncio
import os
import sys

import pytest

# Визначаємо шлях до кореневої директорії проекту
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))

# Змінюємо поточну робочу директорію на директорію bot
os.chdir(os.path.join(BASE_DIR, 'bot'))

# Додаємо шлях до PYTHONPATH
sys.path.insert(0, os.path.join(BASE_DIR, 'bot'))

# Тепер імпортуємо модулі проекту
from db import DBSession

from core.ewallet.external_payment.functions import set_ewallet_ext_payment_status
from core.kafka.producer import producer
from db.models import EWalletExternalPayment, User, Invoice
from schemas import EWalletExternalPaymentStatus
from loggers import JSONLogger

logger = JSONLogger("test.ewallet.external_payment", {})

# UUID створеного платежу, який треба обробити
TARGET_PAYMENT_UUID = "6ea693d9-cc22-4112-9fc2-2b4abcccefe0"

async def test_set_payment_success_status():
    with DBSession():
        invoice = await test_set_payment_success_status_inner()
        return invoice

async def test_set_payment_success_status_inner():
    """
    Тестує встановлення статусу SUCCESS для платежу та перевіряє створення інвойсу.
    
    Етапи тесту:
    1. Знаходить платіж за UUID
    2. Знаходить користувача платежу для використання як платника
    3. Встановлює статус SUCCESS
    4. Перевіряє, що для платежу створено інвойс
    5. Перевіряє, що інвойс правильно налаштований
    """
    try:
        # Ініціалізуємо Kafka producer
        await producer.initialise()
            
        # Знаходимо платіж за UUID
        payment = await EWalletExternalPayment.get(uuid_id=TARGET_PAYMENT_UUID)
        
        if not payment:
            pytest.fail(f"Платіж з UUID {TARGET_PAYMENT_UUID} не знайдено")
        
        logger.debug(f"Знайдено платіж: ID={payment.id}, статус={payment.status.value}")
        
        # Отримуємо користувача (буде виступати як платник)
        user = await User.get_by_id(payment.user_id)
        if not user:
            pytest.fail(f"Користувача з ID {payment.user_id} не знайдено")
        
        logger.debug(f"Користувач платежу: ID={user.id}, name={user.name}")
        
        # Підраховуємо кількість інвойсів до зміни статусу
        invoices_before = await Invoice.get_list(external_transaction_id=payment.uuid_id)
        
        logger.debug(f"Кількість інвойсів до зміни статусу: {len(invoices_before)}")
        
        # Отримуємо мову користувача
        user_lang = await user.get_lang()
        
        # Встановлюємо статус SUCCESS напряму через функцію
        updated_payment = await set_ewallet_ext_payment_status(
            payment=payment,
            user=user,
            new_status=EWalletExternalPaymentStatus.SUCCESS,
            lang=user_lang
        )
        
        logger.debug(f"Статус платежу успішно змінено на: {updated_payment.status.value}")
        
        # Перевіряємо, що для платежу створено інвойс
        invoices = await Invoice.get_list(external_transaction_id=payment.uuid_id)
        
        logger.debug(f"Знайдено інвойсів після зміни статусу: {len(invoices)}")
        
        if len(invoices) <= len(invoices_before):
            pytest.fail(f"Інвойс не був створений для платежу {payment.id}")
            
        # Отримуємо останній створений інвойс
        invoice = invoices[-1]
        
        # Перевіряємо основні параметри інвойсу
        assert invoice.status == "payed", f"Інвойс має статус {invoice.status}, а очікувалось 'payed'"
        assert invoice.ewallet_id == payment.ewallet_id, "Інвойс має невірний ewallet_id"
        assert invoice.user_id == payment.user_id, "Інвойс має невірний user_id"
        # assert invoice.payer_id == payment.payer_id, "Інвойс має невірний payer_id"
        assert invoice.sum_to_pay > 0, "Інвойс має нульову суму до оплати"
        
        logger.debug(f"Інвойс {invoice.id} успішно перевірено")
        logger.debug(f"Інвойс статус: {invoice.status}")
        logger.debug(f"Інвойс ewallet_id: {invoice.ewallet_id}")
        logger.debug(f"Інвойс сума: {invoice.sum_to_pay}")
        
        print(f"\n✅ Тест успішно пройдено!")
        print(f"✅ Платіж ID: {payment.id}")
        print(f"✅ Статус платежу: {updated_payment.status.value}")
        print(f"✅ Створено інвойс ID: {invoice.id}")
        print(f"✅ Статус інвойсу: {invoice.status}")
        print(f"✅ Сума інвойсу: {invoice.sum_to_pay}")
        
        return invoice
    
    except Exception as e:
        logger.error(f"Помилка під час встановлення статусу SUCCESS: {str(e)}")
        pytest.fail(f"Помилка під час тесту: {str(e)}")
        raise


if __name__ == "__main__":
    # Запускаємо асинхронну функцію тесту
    invoice = asyncio.run(test_set_payment_success_status())
