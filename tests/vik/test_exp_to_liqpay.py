import pytest
import logging
from psutils.exceptions import ErrorWithTextVariable

from db import DBSession, crud
from utils.text import f

logger = logging.getLogger("debugger")

@pytest.mark.asyncio
async def test_get_products_for_liqpay():
    """Тест отримання продуктів для LiqPay."""
    try:
        with DBSession():
            products = await crud.get_products_for_liqpay(11, 'А')
            assert products is not None, "No products returned"
            print(f"Found {len(products)} products")
            
    except ErrorWithTextVariable as e:
        logging.error(e, exc_info=True)
        print(await f(e.text_variable, "uk", **e.text_kwargs))
        raise
    else:
        print("success") 