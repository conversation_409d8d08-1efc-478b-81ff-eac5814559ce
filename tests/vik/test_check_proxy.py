import pytest
from aiohttp import ClientSession, ClientTimeout, BasicAuth, ClientConnectorError
import asyncio
import time

@pytest.mark.asyncio
async def test_check_proxy():
    """Тест перевірки проксі."""
    proxy_auth = BasicAuth('YtJ8cPxGu', 'TyGQjjw8U')  # Senin
    proxy_url = "http://**************:63156"

    timeout = ClientTimeout(connect=4, sock_read=7)
    
    print("Starting proxy check...")
    print(f"Proxy URL: {proxy_url}")
    start_time = time.time()
    
    try:
        print("Trying to establish connection...")
        async with ClientSession(timeout=timeout) as session:
            try:
                print("Sending request through proxy...")
                async with session.get(
                    'http://ip-api.com/json/',
                    proxy=proxy_url, 
                    proxy_auth=proxy_auth,
                    verify_ssl=False
                ) as resp:
                    print(f"Got response with status: {resp.status}")
                    data = await resp.json()
                    print(f"Success! Your IP: {data.get('query')}")
                    print(f"Country: {data.get('country')}")
                    print(f"City: {data.get('city')}")
                    
                    # Перевірки
                    assert resp.status == 200, "Proxy request failed"
                    assert data.get('query') is not None, "No IP address in response"
                    assert data.get('country') is not None, "No country in response"
                    assert data.get('city') is not None, "No city in response"
                    return True
            except ClientConnectorError as e:
                print(f"Failed to connect to proxy server: {str(e)}")
                pytest.fail(f"Proxy connection failed: {str(e)}")
            except asyncio.TimeoutError:
                print(f"Proxy connection timed out after {time.time() - start_time:.2f} seconds")
                pytest.fail("Proxy connection timed out")
            except Exception as e:
                print(f"Error during proxy request: {str(e)}")
                pytest.fail(f"Proxy request error: {str(e)}")
                
    except Exception as e:
        print(f"Session creation error: {str(e)}")
        pytest.fail(f"Session creation error: {str(e)}") 