import pytest
import logging
from psutils.exceptions import ErrorWithTextVariable

from core.ext.data_manager.exporter import StoreExporter
from db import DBSession
from utils.text import f

logger = logging.getLogger("debugger")

@pytest.mark.asyncio
async def test_store_export():
    """Тест експорту даних магазину."""
    try:
        with DBSession():
            exporter = StoreExporter(
                14, "excel",  # Тестуємо експорт в Excel
            )
            result = await exporter.start(lang="uk")
            
            assert result is not None, "Export failed"
            print(f"Export completed successfully: {result}")
            
    except ErrorWithTextVariable as e:
        logging.error(e, exc_info=True)
        print(await f(e.text_variable, "uk", **e.text_kwargs))
        raise
    else:
        print("success") 