"""
Тест для перевірки збереження списку країн для eWallet при оновленні
"""
import asyncio

from db import DBSession
from db.models import EWallet
import schemas


async def test_ewallet_countries_update():
    """Тест оновлення списку країн для eWallet"""
    
    with DBSession():
        # Знаходимо будь-який існуючий eWallet для тестування
        ewallet = await EWallet.get_list()
        if not ewallet:
            print("Немає eWallet для тестування")
            return
            
        ewallet = ewallet[0]
        print(f"Тестуємо eWallet ID: {ewallet.id}")
        print(f"Поточні країни: {ewallet.countries}")
        
        # Тестові дані для оновлення
        test_countries = ["US", "UA", "GB", "DE"]
        
        # Створюємо схему для оновлення
        update_data = schemas.AdminEWalletUpdateSchema(
            incust_account_id=ewallet.incust_account_id,
            terminal_api_key=ewallet.terminal_api_key,
            server_api_url=ewallet.server_api_url,
            name=ewallet.name,
            is_enabled=ewallet.is_enabled,
            countries=test_countries,  # Нові країни
            currency=ewallet.currency
        )
        
        # Імітуємо оновлення через CRUD
        from db import crud
        
        await crud.update_ewallet(
            ewallet_id=ewallet.id,
            media_id=ewallet.media_id,
            lang="en",
            langs_list=["en"],
            countries=test_countries,
            is_enabled=ewallet.is_enabled,
            incust_account_id=ewallet.incust_account_id,
            terminal_api_key=ewallet.terminal_api_key,
            server_api_url=ewallet.server_api_url,
            name=ewallet.name,
            currency=ewallet.currency
        )
        
        # Перевіряємо результат
        updated_ewallet = await EWallet.get(ewallet.id)
        print(f"Оновлені країни: {updated_ewallet.countries}")
        
        if updated_ewallet.countries == test_countries:
            print("✅ Тест пройдено! Країни збережено правильно")
        else:
            print(f"❌ Тест не пройдено! Очікувалося: {test_countries}, отримано: {updated_ewallet.countries}")


if __name__ == "__main__":
    asyncio.run(test_ewallet_countries_update())
