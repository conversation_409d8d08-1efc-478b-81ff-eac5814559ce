import os
import sys
import time
import pytest
import requests

# Додаємо шлях до папки bot в PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../bot')))

from config import P4S_API_URL
from schemas.auth.external_login import ExternalLoginRequestStatusEnum

UUID_FILE = os.path.join(os.path.dirname(__file__), "last_uuid.txt")


def test_check_status():
    """Перевірка статусу запиту на отримання incust_external_id.
    Використовує UUID з попереднього тесту test_get_deeplink.py"""

    with open(UUID_FILE, "r") as f:
        uuid = f.read().strip()
    
    url = f"{P4S_API_URL}/auth/externalLogin/{uuid}"
    max_attempts = 60  # 5 секунд * 60 = 5 хвилин
    
    for attempt in range(max_attempts):
        response = requests.get(url)
        assert response.status_code == 200
        
        status_result = response.json()
        print(f"\nСпроба {attempt + 1}/{max_attempts}")
        print(f"Статус запиту: {status_result['status']}")
        print(f"Incust External ID: {status_result['incust_external_id']}")
        
        if status_result["status"] == ExternalLoginRequestStatusEnum.SUCCESS.value:
            assert status_result["incust_external_id"] is not None
            print("\nУспішно отримано incust_external_id!")
            break
            
        time.sleep(5)  # Чекаємо 5 секунд перед наступною спробою
        
    if attempt >= max_attempts:
        pytest.fail("Не вдалося отримати incust_external_id за відведений час")


if __name__ == "__main__":
    test_check_status()