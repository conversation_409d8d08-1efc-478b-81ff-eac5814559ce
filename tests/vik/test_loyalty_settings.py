import requests

# Тестові значення з InCust
TEST_VALUES = {
    "url": "https://testapi.incust.com",
    "whitelabel_id": "100341705844195342",
    "terminal_api_key": "eyJpZCI6IjVjM2Q4Y2Q1LTMxMWItMTFlZS04YzNmLTAyMDAwMDk1YmI3ZiJ9.ZR_oTQ.V8lD-06iFSpnB1SuW_LO0UxuLmM"
}

# API базовий URL
BASE_URL = "http://localhost:1914/admin/2"

# Авторизаційний токен - замінити на реальний
AUTH_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxNjkiLCJ0eXBlIjoidXNlciIsInNjb3BlcyI6bnVsbCwiZXhwIjoxNzQ5NzIxMTEyfQ.Iy2AMFvXhhMpPPhXhNXXLslETgVsXbCvJOITw7LJIBs"

# Headers для запитів
HEADERS = {
    "Authorization": f"Bearer {AUTH_TOKEN}",
    "Content-Type": "application/json"
}


def test_create_loyalty_settings():
    """Тест створення налаштувань лояльності"""
    
    data = {
        # "store_id": 39,
        "white_label_id": TEST_VALUES["whitelabel_id"],
        "terminal_api_key": TEST_VALUES["terminal_api_key"],
        "server_url": TEST_VALUES["url"],
        "type_client_auth": "bot",
        "loyalty_applicable_type": "for_participants",
        "name": "Test Settings",
        "is_enabled": True
    }
    
    response = requests.post(
        f"{BASE_URL}/loyalty-settings/",
        json=data,
        headers=HEADERS
    )
    
    print(f"CREATE - Status: {response.status_code}")
    if response.status_code == 201:
        result = response.json()
        print(f"Created ID: {result.get('id')}")
        print(f"Name: {result.get('name')}")
        return result.get('id')
    else:
        print(f"Error: {response.text}")
        return None


def test_get_loyalty_settings_list():
    """Тест отримання списку налаштувань"""
    
    response = requests.get(
        f"{BASE_URL}/loyalty-settings/",
        headers=HEADERS
    )
    
    print(f"GET LIST - Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Found {len(result)} items")
        return result
    else:
        print(f"Error: {response.text}")
        return []


def test_get_loyalty_settings_by_id(settings_id):
    """Тест отримання налаштувань за ID"""
    
    response = requests.get(
        f"{BASE_URL}/loyalty-settings/{settings_id}",
        headers=HEADERS
    )
    
    print(f"GET BY ID - Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Found: {result.get('name')}")
        return result
    else:
        print(f"Error: {response.text}")
        return None


def test_update_loyalty_settings(settings_id):
    """Тест оновлення налаштувань лояльності"""
    
    data = {
        "name": "Updated Test Settings",
        "priority": 1,
        "is_enabled": False
    }
    
    response = requests.patch(
        f"{BASE_URL}/loyalty-settings/{settings_id}",
        json=data,
        headers=HEADERS
    )
    
    print(f"UPDATE - Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Updated name: {result.get('name')}")
        return result
    else:
        print(f"Error: {response.text}")
        return None


def test_validate_loyalty_settings():
    """Тест валідації налаштувань"""
    
    data = {
        "white_label_id": TEST_VALUES["whitelabel_id"],
        "terminal_api_key": TEST_VALUES["terminal_api_key"],
        "server_url": TEST_VALUES["url"],
        "loyalty_applicable_type": "FOR_PARTICIPANTS",
    }
    
    response = requests.post(
        f"{BASE_URL}/loyalty-settings/validate",
        json=data,
        headers=HEADERS
    )
    
    print(f"VALIDATE - Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Is valid: {result.get('is_valid')}")
        if result.get('terminal_id'):
            print(f"Terminal ID: {result.get('terminal_id')}")
        if result.get('errors'):
            print(f"Errors: {result.get('errors')}")
        return result
    else:
        print(f"Error: {response.text}")
        return None


def test_validate_wrong_credentials():
    """Тест валідації з неправильними даними"""
    
    data = {
        "white_label_id": "wrong_id",
        "terminal_api_key": "wrong_key",
        "server_url": "http://wrong-url.com",
        "loyalty_applicable_type": "FOR_PARTICIPANTS",
    }
    
    response = requests.post(
        f"{BASE_URL}/loyalty-settings/validate",
        json=data,
        headers=HEADERS
    )
    
    print(f"VALIDATE WRONG - Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Is valid: {result.get('is_valid')}")
        if result.get('errors'):
            print(f"Errors: {result.get('errors')}")
        return result
    else:
        print(f"Error: {response.text}")
        return None


def test_delete_loyalty_settings(settings_id):
    """Тест видалення налаштувань лояльності"""
    
    response = requests.delete(
        f"{BASE_URL}/loyalty-settings/{settings_id}",
        headers=HEADERS
    )
    
    print(f"DELETE - Status: {response.status_code}")
    if response.status_code == 204:
        print("Successfully deleted")
        return True
    else:
        print(f"Error: {response.text}")
        return False


def test_create_invalid_data():
    """Тест створення з невалідними даними"""
    
    data = {
        "name": "Test Settings"
        # Відсутні обов'язкові поля
    }
    
    response = requests.post(
        f"{BASE_URL}/loyalty-settings/",
        json=data,
        headers=HEADERS
    )
    
    print(f"CREATE INVALID - Status: {response.status_code}")
    if response.status_code == 422:
        print("Validation error as expected")
        return True
    else:
        print(f"Unexpected response: {response.text}")
        return False


def run_validate_tests():
    """Запуск всіх тестів"""
    
    print("=== LOYALTY SETTINGS API TESTS ===")
    print(f"Base URL: {BASE_URL}")
    print(f"Auth token: {AUTH_TOKEN[:20]}..." if len(AUTH_TOKEN) > 20 else AUTH_TOKEN)
    print()
    
    # 1. Тест валідації з правильними даними
    print("1. Testing validation with correct data...")
    test_validate_loyalty_settings()
    print()

def run_all_tests():
    """Запуск всіх тестів"""
    
    print("=== LOYALTY SETTINGS API TESTS ===")
    print(f"Base URL: {BASE_URL}")
    print(f"Auth token: {AUTH_TOKEN[:20]}..." if len(AUTH_TOKEN) > 20 else AUTH_TOKEN)
    print()
    
    # 1. Тест валідації з правильними даними
    print("1. Testing validation with correct data...")
    test_validate_loyalty_settings()
    print()
    
    # 2. Тест валідації з неправильними даними
    print("2. Testing validation with wrong data...")
    test_validate_wrong_credentials()
    print()
    
    # 3. Тест отримання списку
    print("3. Testing get list...")
    existing_items = test_get_loyalty_settings_list()
    print()
    
    # 4. Тест створення
    print("4. Testing create...")
    created_id = test_create_loyalty_settings()
    print()
    
    # 5. Тест створення з невалідними даними
    print("5. Testing create with invalid data...")
    test_create_invalid_data()
    print()
    
    if created_id:
        # 6. Тест отримання за ID
        print("6. Testing get by ID...")
        test_get_loyalty_settings_by_id(created_id)
        print()
        
        # 7. Тест оновлення
        print("7. Testing update...")
        test_update_loyalty_settings(created_id)
        print()
        
        # 8. Тест видалення
        print("8. Testing delete...")
        test_delete_loyalty_settings(created_id)
        print()
    
    # 9. Тест отримання неіснуючого елементу
    print("9. Testing get non-existent item...")
    test_get_loyalty_settings_by_id(99999)
    print()
    
    print("=== TESTS COMPLETED ===")


if __name__ == "__main__":
    run_validate_tests()
    # run_all_tests()
