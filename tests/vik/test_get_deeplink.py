import os
import sys
import pytest
import requests

# Додаємо шлях до папки bot в PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../bot')))

from schemas.auth.external_login import ExternalLoginRequestPurposeEnum, ExternalLoginRequestTypeEnum
from config import TEST_EXTERNAL_LOGIN_BOT_ID, P4S_API_URL

UUID_FILE = os.path.join(os.path.dirname(__file__), "last_uuid.txt")


def test_get_deeplink():
    """Отримання диплінку для incust_external_id.
    Запустіть цей тест, щоб отримати диплінк для авторизації."""
    
    url = f"{P4S_API_URL}/auth/externalLogin/create"
    data = {
        "type": ExternalLoginRequestTypeEnum.TELEGRAM.value,
        "purpose": ExternalLoginRequestPurposeEnum.INCUST_EXTERNAL_ID.value,
        "bot_id": TEST_EXTERNAL_LOGIN_BOT_ID,
        "continue_url": "https://example.com/success"
    }
    
    response = requests.post(url, json=data)
    assert response.status_code == 200
    
    result = response.json()
    assert result["type"] == ExternalLoginRequestTypeEnum.TELEGRAM.value
    assert result["purpose"] == ExternalLoginRequestPurposeEnum.INCUST_EXTERNAL_ID.value
    assert result["status"] == "created"
    assert "messanger_link" in result
    
    # Зберігаємо UUID для наступного тесту
    with open(UUID_FILE, "w") as f:
        f.write(result["uuid"])
    
    print("\nУспішно створено запит!")
    print(f"UUID: {result['uuid']}")
    print(f"Диплінк: {result['messanger_link']}")
    print("\nПерейдіть по диплінку в боті та авторизуйтесь.")
    print("Після цього запустіть test_check_status.py")


if __name__ == "__main__":
    test_get_deeplink()