# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

**Додаткові інструкції та знання знаходяться в файлі:** `bot/ADDITIONAL_CLAUDE_INSTRUCTIONS.md`

# Pay4Say - Повне керівництво розробника

## 1. Глобальні правила взаємодії

### 1.1 Мова спілкування
- Усі відповіді надавати українською мовою
- Назви змінних, функцій та класів пишуться англійською

### 1.2 Робота з кодом
- **НЕ виводити код у повідомленнях**
- Вносити зміни безпосередньо у файли
- Перевіряти наявність полів та атрибутів обєктів, методів перед використанням
- Уникати зайвих запитів на підтвердження
- Після змін перевіряти код на помилки лінтера
- Виправляти виявлені помилки
- Дотримуватись стилю коду проекту

### 1.3 Заборонені дії
- **НЕ запускати файли на виконання**
- **НЕ робити деплой проекту**
- **НЕ створювати і НЕ виконувати міграції бази даних**
- **НЕ створювати тести** (якщо не вказано спеціально)
- **НЕ запускати тести автоматично**
- **НЕ робити коміти** (окрім випадків коли користувач просить зробити коміт)
- **ПРАВИЛА КОМІТІВ**: Коли користувач просить зробити коміт - робити git add тільки тих файлів які змінював, МАКСИМАЛЬНО КОРОТКИЙ коментар (3-11 слів) БЕЗ БУДЬ-ЯКИХ ПОСИЛАНЬ НА CLAUDE CODE, ANTHROPIC, ЧИ ІНШІ ЗГАДКИ ПРО ШІ
- **КАТЕГОРИЧНО ЗАБОРОНЕНО ВНОСИТИ БУДЬ-ЯКІ ЗМІНИ В БД ПРОДАКШЕНУ БЕЗ ПРЯМОЇ ВКАЗІВКИ!!!**
- **ЗАБОРОНЕНО** використовувати `mcp__mysql__execute` для INSERT, UPDATE, DELETE без явного дозволу
- **ТІЛЬКИ ЧИТАННЯ БД**: використовувати лише `mcp__mysql__query` для SELECT запитів для аналізу та діагностики

### 1.4 Критично важливі правила
- **ЛОКАЛІЗАЦІЯ**: В коді НЕ шукати локалізовані повідомлення - в коді тільки ключі локалізації!
- Меню та кнопки в боті використовують ключі, а не текст
- Для пошуку обробників меню шукати за функціональністю (ewallet, invoice тощо), а не за текстом кнопок
- **ПРАВИЛЬНА РОБОТА З БД В КОДІ**: Використовувати `with DBSession():` для тестових скриптів, як в migrate_loyalty_settings.py
- **НЕ СТВОРЮВАТИ Python скрипти для тестування БД** - використовувати MCP MySQL команди
- **User.get() vs User.get_by_id()**: `User.get()` очікує chat_id, `User.get_by_id()` очікує id
- **EWallet info поле**: Текст в `ewallets.info` показується замість нульового балансу коли немає спеціального рахунку
- Якщо розробник каже "я вже додав/змінив/виправив/вніс зміни/зробив" - **НЕ змінювати цей код**
- Ретельно читати коментарі перед внесенням змін
- **ЗАВЖДИ** поважати існуючі зміни розробника
- **НЕ дублювати** код, який розробник вже реалізував
- При зміні сигнатури функції завжди шукати всі її виклики
- Аналізувати, звідки беруться обов'язкові параметри
- Використовувати правильні джерела даних замість хардкоду

### 1.4.1 ШВИДКИЙ ДОВІДНИК РОЗТАШУВАННЯ КОМПОНЕНТІВ
- **Меню "Мої рахунки" в боті**: `core/bot/ewallet_handlers.py:ewallet_button_handler()`
- **Обробка одного еволета**: `core/bot/ewallet_handlers.py:process_single_ewallet()` 
- **Wrapper для паралельних викликів**: `core/bot/ewallet_handlers.py:process_single_ewallet_parallel()`
- **Отримання списку еволетів**: `db/crud/ewallet/read.py:get_ewallet_list()`
- **Синхронізація external_id веб/бот**: `users.uuid_token` в БД (без "7loc-"), `user.incust_external_id` додає префікс
- **InCust API логування**: `core/loyalty/logging_rest_client.py` - автоматичне логування запитів та відповідей InCust API
- **JSONLogger клас**: `loggers/json.py` - основний JSONLogger клас для структурованого логування
- **InCust API хелпери**: `core/incust/helpers.py` - допоміжні функції для створення InCust чеків

### 1.5 КРИТИЧНО ВАЖЛИВІ ПРАВИЛА ЗАПАМ'ЯТАТИ НАЗАВЖДИ

#### Доступ до БД - ТІЛЬКИ через MCP MySQL сервер
- **ЗАБОРОНЕНО**: `mysql -h localhost -u user -p'password'` або інші прямі підключення через Bash
- **ОБОВ'ЯЗКОВО**: використовувати `mcp__mysql__query`, `mcp__mysql__execute`, `mcp__mysql__list_tables`, `mcp__mysql__describe_table`
- **Запам'ятати**: Для доступу до БД ЗАВЖДИ тільки через MCP сервер!

#### Що робити коли користувач каже "запам'ятай"
- Коли користувач каже "запам'ятай це правило" або подібне - **ЗАВЖДИ додавати в цей файл CLAUDE.md**
- Цей файл читається при кожному перезапуску
- Сюди записувати всі загальні правила проекту щоб не робити дурних помилок при перезапуску

### 1.6 Правила форматування звітів по комітах
- **Коли користувач просить звіт за день**: виводити БЕЗ нумерації, БЕЗ годин, БЕЗ рисочок
- **Формат**: просто окремими рядками з пустим рядком між пунктами
- **Приклад правильного формату**:
```
Виправлено логування методів оплати

Видалено логування про None email користувача

Покращено логування методів оплати
```

### 1.7 Сучасні Python бест практісес типізації (Python 3.10+)
- **ЗАВЖДИ використовувати сучасний синтаксис типізації:**
  - `str | None` замість `Optional[str]`
  - `list[str]` замість `List[str]`
  - `dict[str, int]` замість `Dict[str, int]`
  - `LoyaltySettings | dict` замість `Union[LoyaltySettings, Dict]`
  - `tuple[str, int]` замість `Tuple[str, int]`
- **ЗАБОРОНЕНО використовувати старі конструкції** з `typing.Union`, `typing.Optional`, `typing.List`, `typing.Dict`, `typing.Tuple`
- **Виключення**: тільки якщо проект використовує Python < 3.10

## 2. Технологічний стек та структура проекту

### 2.1 Основні технології
- **Backend**: FastAPI (порт 1914)
- **Telegram боти**: aiogram (Admin Bot, Service Bot, Client Bot)
- **WhatsApp бот**: самописна бібліотека aiowhatsapp
- **База даних**: SQLAlchemy для основного коду, Django ORM тільки для міграцій
- **Віртуальне середовище**: `/home/<USER>/pay4say/.venv/`
- **Робоче середовище**: WSL (Ubuntu)
- **Pydantic**: v1

### 2.2 Шляхи проекту
- **Проект з новою лояльністю**: `/home/<USER>/pay4say`
- **Мастер гілка проекту**: `/home/<USER>/pay4say_master`

### 2.3 Важливі застереження
- Для того щоб правильно працювали ІМПОРТИ запускати Python script в папці: `\home\vik\pay4say\bot`
- Python script запускати з віртуального середовища .venv

### 2.4 Загальна структура
```
/home/<USER>/pay4say/
├── bot/                    # Основна директорія проекту (коренева для імпортів)
├── site/                   # Django застосунок (міграції БД)
├── tests/                  # Тести проекту
├── docs/                   # Документація
├── .venv/                  # Віртуальне середовище Python
└── qrcodes_api_project/    # Окремий сервіс для QR кодів
```

## 3. Логування в проекті

### 3.1 JSONLogger - правильне використання
**КРИТИЧНО ВАЖЛИВО**: JSONLogger має специфічний синтаксис використання:

**✅ ПРАВИЛЬНО - Одноразове використання:**
```python
from loggers import JSONLogger

# Одноразовий лог з даними
JSONLogger("category_name", data_dict).debug("Описове повідомлення")
JSONLogger("payments", {"payment_id": 123}).info("Payment processed")
```

**✅ ПРАВИЛЬНО - Багаторазове використання в межах одного методу:**
```python
from loggers import JSONLogger

debugger = JSONLogger("category_name", {})

# НЕ ПРИСВОЮВАТИ debugger.data! Передавати дані як другий параметр в метод логування!
debugger.debug("Starting process", {"step": 1, "user_id": 123})

# Для іншого логу знову передаємо дані як другий параметр
debugger.info("Process completed", {"step": 2, "result": "success"})
```

**❌ НЕПРАВИЛЬНО - НЕ РОБИТИ ТАК:**
```python
# НЕ присвоювати debugger.data - це зайве!
debugger.data = {"some": "data"}  # НЕ РОБИТИ ТАК!
debugger.debug("message")

# НЕ створювати JSONLogger безпосередньо в виклику якщо потрібно кілька логів
JSONLogger("category", data).debug("message")  # Погано для багатьох логів
```

**❌ НЕПРАВИЛЬНО:**
```python
# НЕ дублювати "debugger" в назві категорії - JSONLogger сам додає це!
logger = JSONLogger("debugger.payments", {})  # НЕ ТАК!
# Правильно:
logger = JSONLogger("payments", {})  # ТАК!

# НЕ створювати новий JSONLogger для кожного лога в межах одного методу
JSONLogger("payments", data1).debug("message1")  # Погано якщо багато логів
JSONLogger("payments", data2).debug("message2")  # в одному методі
```

### 3.2 Структура JSONLogger
- **Перший параметр конструктора**: категорія логу БЕЗ префікса "debugger" (наприклад: "payments", "notifications", "incust.api")
- **Другий параметр**: словник з даними для логування
- **Повідомлення передається в метод** (`.info()`, `.error()`, `.debug()`)
- **JSONLogger автоматично додає префікс "debugger" або "info"** до назви логера залежно від рівня

### 3.3 Оптимальні практики JSONLogger
- **Для одного процесу/методу**: створювати один `debugger = JSONLogger("category", {})` на початку методу
- **Для різних логів у процесі**: змінювати `debugger.data = new_dict` та викликати відповідний метод
- **Короткі тайтли**: в тайтлі тільки ключові значення (ID, статус, метод), повні дані в `data`
- **Умовне логування**: логувати тільки коли є значимі дані для аналізу

### 3.4 Unicode символи в логах
- **Для JSON серіалізації** завжди використовувати `ensure_ascii=False`
- Це забезпечує читабельне відображення українських символів замість `\u0423\u043a\u0440`

## 4. Спеціальні інструкції

### 4.1 Важливі застереження
- **Запам'ятай назавжди:** Ти НЕ створюєш і не виконуєш ніякі міграції. НЕ запускаєш ніякі тести.
- **КРИТИЧНО ВАЖЛИВО:** При зміні структури моделей БД ЗАВЖДИ оновлювати обидві ORM системи:
  - SQLAlchemy моделі в `bot/db/models/`
  - Django моделі в `site/events/models/`
  - Зміни в структурі полів повинні бути ідентичними в обох системах
  - Django ORM використовується ТІЛЬКИ для міграцій БД
  - SQLAlchemy використовується для роботи з даними в проекті

### 4.2 КРИТИЧНО ВАЖЛИВО: Синхронізація external_id між вебом і ботом
- **Проблема**: Веб (Client API) і бот (Terminal API) можуть працювати з різними користувачами в InCust
- **Причина**: Різні external_id через зміну uuid_token користувача в БД
- **Діагностика**: 
  1. Отримати профіль через Client API: `GET /v1/user/profile` з customer_token з `incust_customers`
  2. Порівняти `external_id` з відповіді з `user.incust_external_id` в БД
  3. Якщо різні - оновити `uuid_token` в БД (без префікса "7loc-")
- **ВАЖЛИВО**: В БД зберігається uuid_token БЕЗ префікса, а `user.incust_external_id` автоматично додає "7loc-"

### 4.3 Налаштування БД для mcp server for mysql
- **Продакшен БД:** *************, user: payforsay, pass: PaY4SaY, db: payforsay
- **Локальний доступ:** mysql -hlocalhost (з сервера)

### 4.4 Структура БД та зв'язки між таблицями

#### КРИТИЧНО ВАЖЛИВО: Профілі = Групи = таблиця `groups`
- **ЗАПАМ'ЯТАТИ НАЗАВЖДИ**: Профілі і групи це ТЕ САМЕ! Це таблиця `groups` в БД!
- **profile_id** в інших таблицях = посилання на `groups.id`
- **НЕ ШУКАТИ** таблицю `profiles` - її НЕ ІСНУЄ!
- **ЗАВЖДИ** використовувати таблицю `groups` коли йдеться про профілі

#### 4.4.1 Основні таблиці магазинів та їх зв'язки
- **stores** (id, brand_id, name, incust_terminal_api_key, incust_terminal_id)
  - Головна таблиця магазинів
  - Має прямі foreign key зв'язки: object_payment_settings, product_to_stores
  - Поля для InCust: incust_terminal_api_key, incust_terminal_id

- **store_categories** (id, brand_id, name, father_category_id, media_id)
  - father_category_id - зв'язок з батьківською категорією (НЕ parent_id!)
  - media_id може посилатись на неіснуючі медіа об'єкти - треба обнуляти при міграції

- **store_products** (id, brand_id, name, media_id, description_media_id, offer_media_id, thumbnail_media_id, task_id)
  - Всі media_id поля можуть посилатись на неіснуючі медіа - треба обнуляти при міграції
  - task_id може посилатись на неіснуючі завдання - треба обнуляти при міграції

#### 4.4.2 Зв'язувальні таблиці
- **products_to_categories** (product_id, category_id) - БЕЗ brand_id поля!
- **product_to_stores** (product_id, store_id) - БЕЗ brand_id поля!
- **store_categories_to_stores** (category_id, store_id) - зв'язок категорій з магазинами

#### 4.4.3 Правильний порядок видалення при міграції
1. products_to_categories (через JOIN з store_products)
2. attribute_groups_to_products (через JOIN з store_products)  
3. product_to_stores
4. object_payment_settings (через stores.id)
5. store_products
6. store_categories (спочатку дочірні, потім батьківські)
7. stores

#### 4.4.4 Критично важливо при міграції
- Завжди обнуляти media_id, task_id поля - foreign key constraints!
- Перевіряти чи таблиця має brand_id поле перед використанням у WHERE
- father_category_id у store_categories, НЕ parent_id!
- Спочатку створювати батьківські категорії, потім дочірні

### 4.5 🚨🚨🚨 КРИТИЧНО ВАЖЛИВІ ПРАВИЛА ЗАПУСКУ PYTHON КОДУ 🚨🚨🚨

#### ⚠️ ОБОВ'ЯЗКОВО ДО ВИКОНАННЯ - НІ В ЯКОМУ РАЗІ НЕ ПОРУШУВАТИ!

**ПРАВИЛО 1: ТІЛЬКИ ПАПКА `/home/<USER>/pay4say/bot`**
- **КАТЕГОРИЧНО ЗАБОРОНЕНО** створювати або запускати Python скрипти з будь-якої іншої папки
- **ЗАВЖДИ** використовувати `cd /home/<USER>/pay4say/bot` ПЕРЕД створенням або запуском скриптів
- **ПРИЧИНА**: Всі імпорти в проекті налаштовані відносно папки `bot` як кореневої

**ПРАВИЛО 2: ТІЛЬКИ ВІРТУАЛЬНЕ СЕРЕДОВИЩЕ**
- **КАТЕГОРИЧНО ЗАБОРОНЕНО** використовувати системний Python
- **ЗАВЖДИ** використовувати `/home/<USER>/pay4say/.venv/bin/python`

**ПРАВИЛО 3: ОБОВ'ЯЗКОВИЙ DBSession КОНТЕКСТ**
- **КАТЕГОРИЧНО ЗАБОРОНЕНО** використовувати прямі виклики моделей без DBSession
- **ЗАВЖДИ** обгортати код роботи з БД у `with DBSession():`

**✅ ЄДИНО ПРАВИЛЬНИЙ СПОСІБ:**
```bash
# 1. ЗАВЖДИ переходити в папку bot
cd /home/<USER>/pay4say/bot

# 2. ЗАВЖДИ створювати скрипт у папці bot
cat > test_script.py << 'EOF'
from db import DBSession
with DBSession():
    # код роботи з БД тут
EOF

# 3. ЗАВЖДИ запускати з віртуальним середовищем
/home/<USER>/pay4say/.venv/bin/python test_script.py
```

**❌ ЗАБОРОНЕНО - НЕ РОБИТИ НІКОЛИ:**
```bash
cd /home/<USER>/pay4say        # НЕ ТА ПАПКА!
python test_script.py       # НЕ ТОЙ PYTHON!
cat > /home/<USER>/pay4say/test.py  # НЕ ТА ПАПКА!

# Також заборонено:
ewallet = await EWallet.get(7)  # БЕЗ DBSession - НЕ ПРАЦЮВАТИМЕ!
```

#### КРИТИЧНО ВАЖЛИВІ правила перевірки коду
- **ЗАВЖДИ перевіряти синтаксис** після редагування Python файлів
- **ПРАВИЛЬНИЙ спосіб перевірки**:
  ```bash
  cd /home/<USER>/pay4say/bot
  /home/<USER>/pay4say/.venv/bin/python -m py_compile шлях/до/файлу.py
  ```
- **Результат перевірки**:
  - Якщо файл синтаксично правильний - команда виконується БЕЗ виводу
  - Якщо є помилки - команда показує конкретні помилки синтаксису

#### 🚨🚨🚨 КРИТИЧНО ВАЖЛИВО: Правильна робота з моделями БД 🚨🚨🚨
- **КАТЕГОРИЧНО ЗАБОРОНЕНО** використовувати прямий setattr для оновлення моделей
- **КАТЕГОРИЧНО ЗАБОРОНЕНО** викликати методи моделей БЕЗ `with DBSession():`
- **ОБОВ'ЯЗКОВО ЗАВЖДИ:**
  1. **СПОЧАТКУ** `with DBSession():`
  2. **ПОТІМ** `await Model.create(**data)`, `await Model.get(id)` тощо

**✅ ПРАВИЛЬНО:**
```python
from db import DBSession

with DBSession():
    ewallet = await EWallet.get(7)
    user = await User.get_by_id(1)
```

**❌ ЗАБОРОНЕНО:**
```python
# БЕЗ DBSession - НЕ ПРАЦЮВАТИМЕ!
ewallet = await EWallet.get(7)  # ПОМИЛКА!
```

#### КРИТИЧНО ВАЖЛИВО: JSON серіалізація дат (вирішено!)
- **Проблема**: `TypeError: Object of type date is not JSON serializable` при збереженні incust_check у БД
- **Рішення**: ЗАВЖДИ використовувати `convert_datetime_to_str()` з `utils.datetime_utils` перед збереженням JSON полів з датами
- **Функція**: `convert_datetime_to_str(obj)` - рекурсивно конвертує всі об'єкти `datetime` та `date` у рядки ISO формату
- **ЗАВЖДИ** використовувати цю функцію при роботі з JSON полями що містять дати!

## 5. ЗАВЖДИ використовувати curl для тестування API запитів
- **Коли користувач каже "зробити запит"** - ЗАВЖДИ використовувати curl, НЕ Python скрипти!
- **НЕ створювати JSON файли** - передавати JSON безпосередньо в curl через -d параметр
- **НЕ створювати Python скрипти** для тестових API запитів
- **Виконувати curl ОДРАЗУ** без зайвих проміжних кроків

### 5.1 Готові дані для тестування InCust API (Профіль 2)
**Налаштування лояльності профілю 2:**
```bash
terminal_api_key="eyJpZCI6IjVjM2Q4Y2Q1LTMxMWItMTFlZS04YzNmLTAyMDAwMDk1YmI3ZiJ9.ZR_oTQ.V8lD-06iFSpnB1SuW_LO0UxuLmM"
server_url="https://testapi.incust.com"
white_label_id="100341705844195342"
terminal_id="5c3d8cd5-311b-11ee-8c3f-02000095bb7f"
loyalty_id="5c3b83a0-311b-11ee-8c3f-02000095bb7f"
```

**Тестовий користувач (user_id=2, brand_id=2):**
```bash
user_id=2
chat_id=190722186
uuid_token="e1012897f2b747a6a10d769b64d5c31b"
external_id="7loc-e1012897f2b747a6a10d769b64d5c31b"
email="<EMAIL>"
full_name="Viktor H"
customer_token=".eJxdjM0KgzAQBt_lOxto1cSflwlrsqUB20hWIyK-e3OUngZmYE5swskGjxHGUK-nidTQNb1qPRtFzr1Uw0Sm1g_NLaNC5iQhfjE-Kywkssfk7V2uaZOVvZWj4FPG3Rwd_r2lOZCUmt-4fmb7LPg.aJoAhQ.K3MXVr-QcmmQua9T3wZXDmMBViQ"
```

### 5.2 InCust External ID формування
- **External ID формується**: `INCUST_EXTERNAL_ID_PREFIX + uuid_token`
- **INCUST_EXTERNAL_ID_PREFIX**: `"7loc-"` (з config.py)
- **Приклад**: якщо uuid_token = "86a0e8ad9bd64ee597fe7396fb10dcde", то external_id = "7loc-86a0e8ad9bd64ee597fe7396fb10dcde"
- **Властивість моделі User**: `incust_external_id` - автоматично формується з uuid_token

### 5.3 InCust API авторизація (КРИТИЧНО ВАЖЛИВО!)
- **Terminal API**: Авторизація через `terminal_api_key` з loyalty_settings, користувач визначається через `external_id`
- **Client API**: Авторизація через `token` користувача з incust_customers для конкретного профілю
- **ВАЖЛИВО**: Client та Terminal API можуть працювати з РІЗНИМИ користувачами в InCust!

### 5.4 Отримання правильних даних для запитів

**Для Client/User API (потрібен customer token):**
```sql
SELECT token FROM incust_customers 
WHERE user_id = {user_id} AND brand_id = {brand_id}
```

**Для Terminal API (потрібен terminal key та external_id):**
```sql
SELECT terminal_api_key, server_url, white_label_id 
FROM loyalty_settings 
WHERE brand_id = {brand_id} AND is_enabled = 1

-- External ID формується:
SELECT CONCAT('7loc-', uuid_token) as external_id 
FROM users 
WHERE id = {user_id}
```

### 5.5 Приклади правильних curl запитів для InCust

**1. Process Check (Terminal API) - обробка чека з правилами:**
```bash
curl -X POST "https://testapi.incust.com/v1/term/process_check" \
  -H "Authorization: Bearer eyJpZCI6IjVjM2Q4Y2Q1LTMxMWItMTFlZS04YzNmLTAyMDAwMDk1YmI3ZiJ9.ZR_oTQ.V8lD-06iFSpnB1SuW_LO0UxuLmM" \
  -H "X-Application-Id: 100341705844195342" \
  -H "X-Application-Type: chatbot" \
  -H "Accept-Language: uk" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100.00,
    "id": "7loc-e1012897f2b747a6a10d769b64d5c31b",
    "id_type": "external-id",
    "external_check_id": "unique-check-id"
  }' | jq '.'
```

**2. Get Coupons (Terminal API) - отримати купони користувача:**
```bash
curl -X GET "https://testapi.incust.com/v1/term/coupons/user?user_id_value=7loc-e1012897f2b747a6a10d769b64d5c31b&user_id_type=external-id" \
  -H "Authorization: Bearer eyJpZCI6IjVjM2Q4Y2Q1LTMxMWItMTFlZS04YzNmLTAyMDAwMDk1YmI3ZiJ9.ZR_oTQ.V8lD-06iFSpnB1SuW_LO0UxuLmM" \
  -H "X-Application-Id: 100341705844195342" \
  -H "X-Application-Type: chatbot" \
  -H "Accept-Language: uk" | jq '.'
```

**3. Get Coupons (Client API) - купони авторизованого користувача:**
```bash
curl -X GET "https://testapi.incust.com/v1/client/coupons" \
  -H "Authorization: Bearer .eJxdjM0KgzAQBt_lOxto1cSflwlrsqUB20hWIyK-e3OUngZmYE5swskGjxHGUK-nidTQNb1qPRtFzr1Uw0Sm1g_NLaNC5iQhfjE-Kywkssfk7V2uaZOVvZWj4FPG3Ryd_r2lOZCUmt-4fmb7LPg.aJoAhQ.K3MXVr-QcmmQua9T3wZXDmMBViQ" \
  -H "X-Application-Id: 100341705844195342" \
  -H "X-Application-Type: chatbot" \
  -H "Accept-Language: uk" | jq '.'
```

## 6. Команди для роботи з MCP серверами

### 6.1 Основні команди MCP
```bash
claude mcp list                     # Переглянути всі налаштовані MCP сервери
claude mcp add <name> <command>     # Додати новий MCP сервер
claude mcp remove <name>            # Видалити MCP сервер
claude mcp get <name>               # Отримати деталі про MCP сервер
claude mcp reset-project-choices    # Скинути налаштування проекту
```

### 6.2 Додавання популярних MCP серверів
```bash
# MySQL сервер
claude mcp add mysql "npx -y @f4ww4z/mcp-mysql-server"

# Elasticsearch сервер
claude mcp add elasticsearch "/path/to/mcp-server-elasticsearch"

# PostgreSQL сервер
claude mcp add postgres "npx -y @modelcontextprotocol/server-postgres"
```

### 6.3 Видалення MCP серверів
```bash
claude mcp remove elasticsearch    # Видалити Elasticsearch
claude mcp remove mysql           # Видалити MySQL
claude mcp remove postgres        # Видалити PostgreSQL
```

### 6.4 Перевірка статусу
```bash
claude mcp list    # Показує статус підключення кожного сервера (✓ Connected або ✗ Failed)
```

## 6.6 WhatsApp Bot та webhooks - ВАЖЛИВІ ДЕТАЛІ
- **Точка входу WhatsApp бота**: `bot/run_whatsapp_bot.py` - запускає FastAPI на порту 5419
- **Ініціалізація**: `bot/whatsapp_bot/init.py` - створює FastAPI app з webhook handler
- **Webhook handler**: `bot/whatsapp_bot/bot/webhook_handlers.py` - клас WhatsappAppIdWebhookHandler
- **URL для webhooks**: `https://whatsapp.payforsay.com/whatsapp/{app_id}/` (продакшен)
- **Бібліотека aiowhatsapp**: `/home/<USER>/aiowhatsapp` - окремий проект, версія 1.8.34
- **Схеми валідації WhatsApp**: `/home/<USER>/aiowhatsapp/aiowhatsapp/schemas/update/status.py` - додано підтримку типу "marketing"
- **Перезапуск WhatsApp бота**: команда `/rwb` в root боті

## 6.7 EWallet Merchants API - фільтрація по ewallet_id
- **Ендпоінт**: `/platform/ewallet_merchants/` - підтримує опціональний query параметр `ewallet_id`
- **Використання**: 
  - `/platform/ewallet_merchants/` - всі мерчанти
  - `/platform/ewallet_merchants/?ewallet_id=123` - мерчанти конкретного eWallet
- **Схема параметрів**: `EwalletMerchantsListParams` в `/bot/schemas/ewallet_merchant.py`
- **CRUD функція**: `get_platform_ewallet_merchant_list()` в `/bot/db/crud/ewallet/read.py` - підтримує фільтрацію по ewallet_id
- **Сервіс**: `EWalletMerchantsService.get_list()` в `/bot/api/platform/router/ewallet_merchants/service.py`

## 7. Робота з базою даних

### 7.1 Дві ORM системи
- **SQLAlchemy**: Основна ORM для роботи з даними в проекті
- **Django ORM**: Використовується тільки для міграцій БД
- При зміні моделей БД потрібно оновлювати обидві системи

### 6.2 Використання методів моделей
Для простих КРУД операцій використовувати вбудовані методи моделей які наслідуються від `BaseDBModel` і інших класів:
```python
item = await Model.get(id)
items = await Model.get_list(filter1=value1, filter2=value2)
new_item = await Model.create(**data)
await item.update(**data)
await item.delete()
exists = await Model.is_exists(**filters)
```

### 6.3 Коли створювати CRUD функції
CRUD функції під декоратором `@db_func` створюються **тільки** для:
- Складних JOIN запитів між таблицями
- Спеціальної логіки фільтрації
- Агрегації даних (COUNT, SUM, GROUP BY)
- Bulk операцій
- Складних транзакцій

**НЕ створювати** CRUD функції для базових операцій!

### 6.4 Правила скоупів для loyalty settings
- **Profile, Brand, Store, Product, InvoiceTemplate**: використовувати скоуп `"profile:read"` - це ресурси профілю
- **EWallet**: використовувати скоуп `"platform:admin"` - це платформний ресурс для суперадмінів
- **ЗАПАМ'ЯТАТИ**: EWallet це окремий випадок з доступом на рівні платформи, всі інші об'єкти на рівні профілю

### 6.5 Структура БД
- **Локація**: `/bot/db/` - робити імпорти `from db.models import User`
- **Декоратор**: `@db_func` для синхронних функцій
- **Сесія**: `sess()` (імпорт з `db`) - для КРУД функцій.

### 6.6 Правила імпортів моделей
- **ЗАВЖДИ використовувати**: `from db.models import LoyaltySettings, User, Brand` тощо
- **ЗАБОРОНЕНО**: `from core.loyalty.models import LoyaltySettings` - НЕ ПРАВИЛЬНО!
- **НЕ додавати зайві імпорти** які не використовуються в коді

## 7. Правила рефакторингу та оптимізації коду

### 7.1 DRY принцип та уніфікація логіки
- **ЗАБОРОНЕНО** дублювати однакову логіку між різними handlers (Telegram, WhatsApp, API)
- **ОБОВ'ЯЗКОВО** виносити спільну логіку в core функції
- **ПРАВИЛО**: Якщо код повторюється більше одного разу - створити окрему функцію

### 7.2 Асинхронність та блокуючі операції
- **ЗАБОРОНЕНО** використовувати прямий доступ до властивостей моделей через foreign key (наприклад: `bot.group.name`)
- **ОБОВ'ЯЗКОВО** використовувати асинхронні методи отримання об'єктів: `group = await Group.get(bot.group_id)`
- **ПРАВИЛО**: В асинхронному коді FastAPI з SQLAlchemy всі операції з БД мають бути асинхронними

### 7.3 Оптимізація запитів до БД
- **ПРАВИЛО**: Мінімізувати кількість запитів до БД
- **ПРИКЛАД**: Використовувати Group якщо він вже доступний, замість додаткового запиту на Brand
- **ВАЖЛИВО**: Аналізувати які дані вже доступні перед створенням нових запитів

### 7.4 Обробка винятків та помилок
- **ЗАБОРОНЕНО** використовувати загальний `Exception` якщо існують специфічні класи помилок
- **ОБОВ'ЯЗКОВО** використовувати відповідні класи винятків (наприклад: `term.ApiException` для InCust API)
- **ПРАВИЛО**: Створювати helper функції для обробки повторюваних патернів помилок

### 7.5 Локалізація
- **ФОРМАТ КЛЮЧІВ**: В Google таблицях використовується формат `UPPER_CASE_WITH_UNDERSCORES`
- **В КОДІ**: Використовувати lowercase ключі з пробілами або snake_case
- **ПРАВИЛО**: Завжди використовувати локалізовані повідомлення для користувача

### 7.6 Контексти та параметри функцій
- **ЗАБОРОНЕНО** ігнорувати параметри які мають значення в різних контекстах
- **ПРИКЛАД**: Loyalty settings можуть залежати від store_id, product_id, invoice_template_id тощо
- **ПРАВИЛО**: При створенні спільних функцій враховувати всі можливі контексти використання
- **РЕКОМЕНДАЦІЯ**: Передавати опціональні параметри до спільної функції, де вона сама визначає логіку

### 7.7 Схеми даних для різних інтерфейсів
- **ПРАВИЛО**: Різні інтерфейси можуть використовувати різні схеми даних
- **Bot handlers**: Використовують схеми з `BytesIO` для зображень
- **API endpoints**: Використовують схеми з `str` (base64) для зображень
- **РЕКОМЕНДАЦІЯ**: Спільна функція повертає базовий формат, конвертація на рівні конкретного інтерфейсу

### 7.8 Чистота коду
- **ОБОВ'ЯЗКОВО** видаляти невикористовувані змінні та параметри
- **ЗАБОРОНЕНО** залишати код який не виконує корисної роботи
- **ПРАВИЛО**: Якщо параметр або змінна не використовується - видалити її

### 7.9 Створення helper функцій
- **ПРАВИЛО**: Якщо логіка повторюється в кількох місцях - створити окрему helper функцію
- **ПРИКЛАД**: `extract_incust_api_error_message()` для парсингу помилок InCust API
- **РЕКОМЕНДАЦІЯ**: Helper функції розміщувати в відповідних модулях (наприклад: `core/incust/helpers.py`)