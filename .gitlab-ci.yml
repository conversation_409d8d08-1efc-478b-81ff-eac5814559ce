variables:
  LC_ALL: "en_US.UTF-8"
  LANG: "en_US.UTF-8"
  GIT_STRATEGY: fetch
  GIT_DEPTH: 1
  FF_USE_FASTZIP: "true"
  ARTIFACT_COMPRESSION_LEVEL: "default"
  CACHE_COMPRESSION_LEVEL: "fast"
#  CI_DEBUG_TRACE: "true"
  STARTING_PIPELINE_SEND: "true"
  TAG_RUNNER: "node-build"


default:
  interruptible: true

#workflow:
#  rules:
#    - if: $GITLAB_USER_EMAIL == "<EMAIL>"
#      variables:
#        TAG_RUNNER: "mslocal-node-build"
#    - when: always


stages:
  - start
  - install
  - build
  - deploy


Start:
  stage: start
  variables:
    GIT_STRATEGY: none
  rules:
    - if: $CI_COMMIT_REF_NAME == "test" || $CI_COMMIT_REF_NAME == "master"
      changes:
        - bot/web_app/**/*
        - .gitlab-ci.yml
      when: always
  script:
    - |
      if [[ ${STARTING_PIPELINE_SEND} == "true" ]]; then
        CI_COMMIT_AUTHOR_EDIT=`echo $CI_COMMIT_AUTHOR | cut -d " " -f2 | sed -e 's/<//' | sed -e 's/>//'`
        curl -F "chat_id=$PAYFORSAY_TELEGRAM_DEPLOY_CHAT_ID" \
        -F "text=\"<b> STARTING make web_app - $CI_COMMIT_REF_NAME </b>
      <b>Commit by: </b><i>$CI_COMMIT_AUTHOR_EDIT </i>\"" \
        -F "parse_mode=HTML" \
        "https://api.telegram.org/bot$PAYFORSAY_TELEGRAM_BOT_TOKEN/sendMessage"
        echo "STARTING_PIPELINE_SEND=true" > start_pipeline_send
      fi
  tags:
    - $TAG_RUNNER

Install dep:
  stage: install
  script:
    - cd bot/web_app/
    - nvm use
    - node -v
    - npm install
  cache:
    key:
      files:
        - bot/web_app/package.json
      prefix: "node-modules"
    paths:
      - bot/web_app/node_modules
    policy: push
  rules:
    - if: $CI_COMMIT_REF_NAME == "test" || $CI_COMMIT_REF_NAME == "master"
      changes:
        - bot/web_app/package.json
        - .gitlab-ci.yml
  tags:
    - $TAG_RUNNER

Build web_app:
  stage: build
  cache:
    key:
      files:
        - bot/web_app/package.json
      prefix: "node-modules"
    paths:
      - bot/web_app/node_modules
    policy: pull-push
  script:
    - cd bot/web_app/
    - |
      if [[ ! -d node_modules/ ]]; then
        echo "node_modules install"
        nvm use
        node -v
        npm install
      fi
    - nvm use
    - node -v
    - ls -la
    - CI=false npm run build --verbose
    - echo "Build completed"
  artifacts:
    paths:
      - bot/web_app/build
    expire_in: 1 hrs
  after_script:
    - |
      if [[ $CI_JOB_STATUS == "failed" ]]; then
        CI_COMMIT_AUTHOR_EDIT=`echo $CI_COMMIT_AUTHOR | cut -d " " -f2 | sed -e 's/<//' | sed -e 's/>//'`
        curl -F "chat_id=$PAYFORSAY_TELEGRAM_DEPLOY_CHAT_ID" \
        -F "text=\"`printf '\U000274C'`<b> FAILED - $CI_COMMIT_REF_NAME </b>
      $CI_JOB_NAME
      <b>Commit by: </b><i>$CI_COMMIT_AUTHOR_EDIT </i>\"" \
        -F "parse_mode=HTML" \
        "https://api.telegram.org/bot$PAYFORSAY_TELEGRAM_BOT_TOKEN/sendMessage"
      fi
  rules:
    - if: $CI_COMMIT_REF_NAME == "test" || $CI_COMMIT_REF_NAME == "master"
      changes:
        - bot/web_app/**/*
        - .gitlab-ci.yml
      when: on_success
  tags:
    - $TAG_RUNNER


Download web_app files to server:
  stage: deploy
  variables:
    HOST: ***********
    USER_DEPLOY: gitlab-deploy
  script:
    - cd bot/web_app/
    - nvm use
    - node -v
    - ls -la
    - |-
      if [ -d build ]; then
        find build/ -type d -exec chmod 0755 {} \;
        find build/ -type f -exec chmod 0444 {} \;
        if [ ${CI_COMMIT_REF_NAME} == "master" ]; then
          BUILD_PREFIX="prod"
        elif [ ${CI_COMMIT_REF_NAME} == "test" ]; then
          BUILD_PREFIX="test"
        else
          BUILD_PREFIX="testing"
        fi
        echo "${BUILD_PREFIX}" > prefix
        TAR_BUILD_NAME="payforsay_web-app_build_${BUILD_PREFIX}.tgz"
        tar czvf ${TAR_BUILD_NAME} build
        eval $(ssh-agent -s)
        cat $GITLAB_DEPLOY_SSH_KEY_PRI | ssh-add -
        scp -o StrictHostKeyChecking=no ${TAR_BUILD_NAME} ${USER_DEPLOY}@${HOST}:/tmp/
        ssh-add -D && eval $(ssh-agent -k)
      fi || exit 1
      echo "Downloadign web_app files to server"
  after_script:
    - |
      if [[ $CI_JOB_STATUS == "success" ]]; then
        BUILD_PREFIX=$(cat bot/web_app/prefix)
        CI_COMMIT_AUTHOR_EDIT=`echo $CI_COMMIT_AUTHOR | cut -d " " -f2 | sed -e 's/<//' | sed -e 's/>//'`
        curl -F "chat_id=$PAYFORSAY_TELEGRAM_DEPLOY_CHAT_ID" \
        -F "text=\"`printf '\U0002705'`<b> SUCCESS - $CI_COMMIT_REF_NAME </b>
      $CI_JOB_NAME<b>
      Run from server console: </b><code>p4s_deploy_web ${BUILD_PREFIX}</code>
      <b>Commit by: </b><i>$CI_COMMIT_AUTHOR_EDIT </i>\"" \
        -F "parse_mode=HTML" \
        "https://api.telegram.org/bot$PAYFORSAY_TELEGRAM_BOT_TOKEN/sendMessage"
      fi
      if [[ $CI_JOB_STATUS == "failed" ]]; then
        CI_COMMIT_AUTHOR_EDIT=`echo $CI_COMMIT_AUTHOR | cut -d " " -f2 | sed -e 's/<//' | sed -e 's/>//'`
        curl -F "chat_id=$PAYFORSAY_TELEGRAM_DEPLOY_CHAT_ID" \
        -F "text=\"`printf '\U000274C'`<b> FAILED - $CI_COMMIT_REF_NAME </b>
      $CI_JOB_NAME
      <b>Commit by: </b><i>$CI_COMMIT_AUTHOR_EDIT </i>\"" \
        -F "parse_mode=HTML" \
        "https://api.telegram.org/bot$PAYFORSAY_TELEGRAM_BOT_TOKEN/sendMessage"
      fi
  rules:
    - if: $CI_COMMIT_REF_NAME == "test" || $CI_COMMIT_REF_NAME == "master"
      changes:
        - bot/web_app/**/*
        - .gitlab-ci.yml
      when: on_success
  tags:
    - $TAG_RUNNER
