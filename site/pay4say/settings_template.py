"""
Django settings for pay4say project.

Generated by 'django-admin startproject' using Django 3.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

MEDIA_ROOT = "../bot/"

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = ''

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# ALLOWED_HOSTS = ['a.payforsay.com', '127.0.0.1', 'localhost']
ALLOWED_HOSTS = "*"

REST_FRAMEWORK = {'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema'}

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'events',
    'rest_framework',
    'drf_yasg',
    'allauth',
    'ajax_select',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'pay4say.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'pay4say.wsgi.application'

# Database
# https://docs.djangoproject.com/en/3.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': '',
        'USER': '',
        'PASSWORD': '',
        'HOST': 'localhost',
        'OPTIONS': {
            'charset': 'utf8mb4'
        }
    }
}

AUTH_USER_MODEL = "allauth.User"

# Password validation
# https://docs.djangoproject.com/en/3.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.1/topics/i18n/

LANGUAGE_CODE = 'ru'

TIME_ZONE = "CET"

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.1/howto/static-files/

STATIC_URL = '/static/'

STATIC_ROOT = os.path.join(BASE_DIR, 'static/')
# ID документов
TEXT_VARIABLES = "1078dIetVHmmHbTUqDkqgACALuYhRHPKtJtvAmvvlMAo"

PLATFORM_ADMINS = [
    (634793021, False),  # dev Max-semka
    (260032377, False),  # dev Alexander Pisarev
    (394456075, False),  # Guy Secret
    (397122680, False),  # Maxim Ronshin
    (315373186, True)  # dev Ruslan
]
# Путь к json файлу от google
PATH_TO_GOOGLE_CREDS_JSON = "../bot/config/PythonProject.json"

# путь к локально лежащим переменным
PATH_TO_LOCAL_DATA = f"../local_data.json"

FATHER_BOT_USERNAME = ""
PROD_FATHER_BOT_USERNAME = ""
PROD_SERVICE_BOT_USERNAME = ""

DEFAULT_LANG = "en"

ROOT_BOT_API_TOKEN = ""

DEVELOPERS_CHAT_IDS = [634793021, 260032377]
SENDFILE_ROOT = ''
SENDFILE_URL = ''
SENDFILE_BACKEND = 'django_sendfile.backends.simple'

DEFAULT_MINI_GAME_BET = 50

USE_LOCALISATION = "**localisation**"

SERVICE_BOT_API_TOKEN = ""

SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False
SECURE_SSL_REDIRECT = False
