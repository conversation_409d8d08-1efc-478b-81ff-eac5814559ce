from django.db import models
from django.utils import timezone


class Gallery(models.Model):
    class Meta:
        db_table = "galleries"

    id = models.BigAutoField(primary_key=True)
    time_created = models.DateTimeField(default=timezone.now)


class GalleryItem(models.Model):
    class Meta:
        db_table = "gallery_items"
        unique_together = (
            ("gallery", "media"),
        )

    id = models.BigAutoField(primary_key=True)

    gallery = models.ForeignKey(Gallery, on_delete=models.RESTRICT)
    media = models.ForeignKey("MediaObject", on_delete=models.RESTRICT)

    position = models.PositiveSmallIntegerField()

    time_created = models.DateTimeField(default=timezone.now)

