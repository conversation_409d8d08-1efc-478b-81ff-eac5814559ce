from django.db import models

from events.models import Group


class JournalSetting(models.Model):
    class Meta:
        db_table = "journal_settings"
        verbose_name = "Налаштування журналу"
        verbose_name_plural = "Налаштування журналу"

        indexes = []

    id = models.BigAutoField(primary_key=True)

    name = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Назва налаштування"
    )

    type = models.CharField(max_length=64, null=False, blank=False, default="orders_journal")

    settings = models.JSONField(
        null=True,
        blank=True,
        verbose_name="Дані налаштування"
    )

    table_settings = models.JSONField(
        null=True,
        blank=True,
        verbose_name="Дані налаштування таблиці"
    )

    group = models.ForeignKey(
        Group, blank=False, null=False, on_delete=models.CASCADE, verbose_name="Профіль"
    )

    user = models.ForeignKey(
        "TelegramUser", blank=False, null=False, on_delete=models.CASCADE, verbose_name="Користувач"
    )
