from datetime import datetime

from django.db import models


class ShortToken(models.Model):

    class Meta:
        db_table = "short_tokens"
        verbose_name = "Короткий токен"
        verbose_name_plural = "Короткі токени"

    id = models.BigAutoField(primary_key=True)

    is_used = models.BooleanField(default=False, verbose_name="використаний")
    used_datetime = models.DateTimeField(null=True, blank=True, verbose_name="дата використання")

    user = models.ForeignKey(
        "TelegramUser", related_name="short_tokens",
        on_delete=models.CASCADE, verbose_name="користувач, для якого створено токен"
    )
    bot = models.ForeignKey(
        "ClientBot", related_name="short_tokens",
        on_delete=models.CASCADE, verbose_name="бот, у якому створено токен",
        null=True, blank=True
    )

    lang = models.CharField(max_length=2, null=True, blank=True, verbose_name="мова")
    url_path = models.CharField(max_length=255, null=True, blank=True)
    params = models.JSONField(null=True, blank=True, verbose_name="додаткові параметри")

    scopes = models.CharField(
        max_length=100, verbose_name="api scopes для токену, який буде створено ща цим коротким токеном",
    )

    time_created = models.DateTimeField(
        default=datetime.utcnow,
        verbose_name="дата створення"
    )
