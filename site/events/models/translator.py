from django.db import models


class Translation(models.Model):

    class Meta:
        db_table = "translations"
        verbose_name = "Перевод"
        verbose_name_plural = "Переводы"

    id = models.CharField(max_length=50, primary_key=True, unique=True)

    obj_type = models.CharField(max_length=32, null=True)
    obj_id = models.CharField(max_length=24, null=True)
    lang = models.CharField(max_length=2, null=True)

    data = models.JSONField(verbose_name="переведенные данные")


class TranslationBackground(models.Model):

    class Meta:
        db_table = "translation_backgrounds"
        verbose_name = "Фоновый процесс перевода"
        verbose_name_plural = "Фоновые процессы переводов"

    id = models.BigAutoField(primary_key=True)
    lang = models.CharField(max_length=2, unique=True, verbose_name="Переводимый язык")

    status = models.CharField(max_length=10, default="enabled", verbose_name="статус")

    create_date = models.DateTimeField(auto_now_add=True, verbose_name="Дата создания")
    update_date = models.DateTimeField(
        auto_now_add=True, verbose_name="Дата обновления"
    )

    def __str__(self):
        return f"<{self.__class__.__name__}{self.id}>"
