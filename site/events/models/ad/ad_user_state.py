from django.db import models


class AdUserState(models.Model):
    class Meta:
        db_table = "ad_user_states"

    id = models.BigAutoField(primary_key=True)
    ad = models.ForeignKey("Ad", on_delete=models.CASCADE)
    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE)

    shown_units = models.JSONField()
    last_shown_date = models.DateTimeField()

    time_created = models.DateTimeField(auto_now_add=True)
