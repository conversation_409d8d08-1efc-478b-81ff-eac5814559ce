from django.db import models


class AdUnit(models.Model):
    class Meta:
        db_table = "ad_units"

    id = models.SmallAutoField(primary_key=True)

    ad = models.ForeignKey("Ad", on_delete=models.CASCADE)

    horizontal_video = models.ForeignKey(
        "MediaObject",
        on_delete=models.RESTRICT,
        null=True,
        blank=True,
        related_name="horizontal_video_ad_units",
    )

    vertical_video = models.ForeignKey(
        "MediaObject",
        on_delete=models.RESTRICT,
        null=True,
        blank=True,
        related_name="vertical_video_ad_units",
    )

    position = models.SmallIntegerField()

    time_created = models.DateTimeField(auto_now_add=True)
