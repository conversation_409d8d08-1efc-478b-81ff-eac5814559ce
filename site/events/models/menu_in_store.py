from django.db import models


class MenuInStore(models.Model):
    class Meta:
        db_table = "menus_in_store"
        verbose_name = "Настройка меню в заведении"
        verbose_name_plural = "Настройки меню в заведении"

    id = models.BigAutoField(primary_key=True)
    comment = models.TextField(verbose_name="Комментарий")

    group = models.ForeignKey("Group", on_delete=models.CASCADE, null=False)
    store = models.ForeignKey(
        "Store", on_delete=models.SET_NULL,
        null=True, blank=True, default=None
    )

    need_save_as_active = models.BooleanField(default=True)
    redirect_type = models.TextField(default="web")
    payment_option = models.TextField(default="disabled")
    invoice_template = models.ForeignKey("InvoiceTemplate",
        related_name="menu_in_store", related_query_name="menu_in_store",
        on_delete=models.PROTECT, null=True, blank=True,
        verbose_name="шаблон счёта для оплаты")

    is_e_menu = models.BooleanField(default=False)

    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now_add=True)

    is_deleted = models.BooleanField(default=False)
