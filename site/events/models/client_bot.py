from django.db import models


class ClientBot(models.Model):

    class Meta:
        db_table = "bots"
        verbose_name = "Бот"
        verbose_name_plural = "Боты"

    id = models.BigAutoField(primary_key=True)
    status = models.CharField(max_length=10, default="enabled", verbose_name="статус")
    is_started = models.BooleanField(default=False, verbose_name="запущен ли")

    bot_type = models.CharField(
        max_length=8, null=False, default="telegram", verbose_name="тип боту"
    )
    token = models.CharField(
        max_length=256, unique=True, null=True, blank=True, verbose_name="токен"
    )

    whatsapp_app_id = models.CharField(
        max_length=50, unique=True, null=True, blank=True,
        verbose_name="id whatsapp додатку"
    )
    whatsapp_app_secret = models.CharField(
        max_length=50, null=True, blank=True, verbose_name="secret whatsapp додатку"
    )
    whatsapp_app_name = models.Char<PERSON>ield(
        max_length=256, null=True, blank=True, verbose_name="імʼя whatsapp додатку"
    )
    whatsapp_from = models.Char<PERSON>ield(
        max_length=50, null=True, blank=True, unique=True,
        verbose_name="whatsapp відправник"
    )
    whatsapp_from_phone_number = models.CharField(
        max_length=50, null=True, blank=True, unique=True,
        verbose_name="whatsapp номер телефону відправника",
    )
    whatsapp_business_account_id = models.CharField(
        max_length=50, null=True, blank=True, unique=True, verbose_name="whatsapp бізнес аккаунт id"
    )
    is_pay4say = models.BooleanField(
        default=False, verbose_name="является ли бот pay for say"
    )
    is_friendly = models.BooleanField(
        default=False, verbose_name="является ли бот паблишером"
    )
    is_lip_lep = models.BooleanField(default=False, verbose_name="является ли лип леп")
    ask_lang = models.BooleanField(
        default=True, verbose_name="уточнять язык при первом входе"
    )
    username = models.CharField(
        max_length=256, null=True, blank=True, unique=True, verbose_name="username бота"
    )
    group = models.OneToOneField(
        "Group", on_delete=models.PROTECT, verbose_name="группа",
        related_name="bot", related_query_name="bot"
    )
    args = models.CharField(
        max_length=256, null=True, verbose_name="системные аргументы БОТА", blank=True
    )

    time_created = models.DateTimeField(auto_now_add=True, verbose_name="Дата создания")

    on_join_virtual_manager = models.ForeignKey(
        "VirtualManager", on_delete=models.SET_NULL, null=True, blank=True,
        verbose_name="виртуальный менеджер при первом входе в бот",
        related_name="connected_as_on_join_bots",
        related_query_name="connected_as_on_join_bots",
    )
    share_bot_vm = models.ForeignKey(
        "VirtualManager", on_delete=models.SET_NULL,
        null=True, blank=True,
        verbose_name="вм при нажатии на поделитсья ботом",
        related_name="connected_as_on_share_bots",
        related_query_name="connected_as_share_bots",
    )
    need_chat_header = models.BooleanField(
        default=True, verbose_name="нужно ли отправлять сообщение о входе в режим чата"
    )
    need_leave_chat_keyboard = models.BooleanField(
        default=False, verbose_name="нужно ли отправлять клавиатуру \"выйти из чата\""
    )

    is_auto_answer = models.BooleanField(
        default=False,
        verbose_name="Нужен ли автоответ если никто долго не отвечает пользователю",
    )
    auto_answer_delay = models.IntegerField(default=5, verbose_name="Время ожидания")

    last_set_menu_message_id = models.BigIntegerField(null=True, default=False)

    last_sent_post_datetime = models.DateTimeField(
        null=True, blank=True, verbose_name="время публикации последнего события"
    )
    is_show_order_button = models.BooleanField(
        default=True, verbose_name="Показывать кнопку заказы в главном меню",
    )

    def __str__(self):
        return self.username if self.username is not None else ""
