from django.db import models


class NotificationSetting(models.Model):

    class Meta:
        db_table = "notification_settings"
        verbose_name_plural = "Notification Setting"

    id = models.BigAutoField(primary_key=True)
    group = models.ForeignKey("Group", on_delete=models.CASCADE, null=False)
    is_enabled = models.BooleanField(default=True)
    target = models.TextField(default="CRM")
    user = models.ForeignKey(
        "TelegramUser", null=False, on_delete=models.CASCADE,
        verbose_name="Пользователь"
    )
