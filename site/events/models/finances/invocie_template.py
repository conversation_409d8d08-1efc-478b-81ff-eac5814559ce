from datetime import timed<PERSON>ta

from django.db import models
from django.utils import timezone
from pay4say.settings import USE_LOCALISATION


class InvoiceTemplate(models.Model):
    class Meta:
        db_table = "invoice_templates"
        verbose_name = "Шаблон счёта"
        verbose_name_plural = "Шаблоны счетов"

    id = models.BigAutoField(primary_key=True)
    is_deleted = models.BooleanField(default=False)
    group = models.ForeignKey("Group", on_delete=models.CASCADE)
    currency = models.CharField(max_length=20, verbose_name="валюта")
    _prices = models.JSONField(verbose_name="позиции", null=True)
    payment_mode = models.CharField(max_length=14, null=True, blank=True)
    title = models.TextField(verbose_name="заголовок", null=True, blank=True)
    description = models.TextField(
        verbose_name="описание", null=True, blank=True, default=None
    )
    comment_mode = models.CharField(max_length=8, default="optional")
    comment_label_raw = models.TextField(default=USE_LOCALISATION)
    photo = models.CharField(max_length=100, null=True, blank=True, verbose_name="фото")
    need_name = models.BooleanField(default=True, verbose_name="имя обязательное")
    need_phone_number = models.BooleanField(
        default=True, verbose_name="номер телефона обязательный"
    )
    need_email = models.BooleanField(
        default=False, verbose_name="электронная почта обязательная"
    )
    disabled_qty = models.BooleanField(
        default=False, verbose_name="отключить количество"
    )
    disabled_loyalty = models.BooleanField(
        default=False, verbose_name="отключить лояльность"
    )
    expiration_datetime = models.DateTimeField(
        verbose_name="дата конца принятия оплат", null=True, blank=True
    )
    _live_time = models.FloatField(null=True, blank=True, verbose_name="строк жизни")

    media = models.ForeignKey(
        "MediaObject",
        on_delete=models.RESTRICT,
        null=True, blank=True,
        related_name="invoice_templates",
        related_query_name="invoice_templates"
    )

    plugins = models.JSONField(null=True, blank=True)

    task = models.ForeignKey(
        "Task",
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="invoice_template_task",
        related_query_name="invoice_template_task"
    )
    incust_terminal_api_key = models.TextField(
        verbose_name="Incust terminal api key", null=True, blank=True, default=None
    )

    incust_terminal_id = models.CharField(
        max_length=512, verbose_name="ID терміналу InCust", null=True, blank=True,
        default=None
    )

    min_amount = models.PositiveBigIntegerField(null=True, blank=True)
    max_amount = models.PositiveBigIntegerField(null=True, blank=True)

    max_bonuses_percent = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        default=None,
    )

    product_code = models.CharField(
        max_length=255, null=True, blank=True, verbose_name="Код продукту"
    )

    need_auth = models.BooleanField(
        default=False, verbose_name="Потрібна авторизація для покупки"
    )

    @property
    def live_time(self) -> timedelta | None:
        if self._live_time:
            return timedelta(seconds=self._live_time)

    def __str__(self):
        return f"Шаблон №{self.id} для создания счёта"


class InvoiceTemplateItem(models.Model):
    class Meta:
        db_table = "invoice_template_items"

    id = models.BigAutoField(primary_key=True)
    time_created = models.DateTimeField(default=timezone.now)

    name = models.CharField(max_length=255)
    category = models.CharField(max_length=255, default="uncategorized")
    price = models.BigIntegerField()
    quantity = models.IntegerField(default=1)
    item_code = models.CharField(max_length=255, null=True, blank=True)

    is_deleted = models.BooleanField(default=False)

    invoice_template = models.ForeignKey("InvoiceTemplate", on_delete=models.RESTRICT)
