from datetime import timedelta

from django.db import models
from django.utils import timezone


class Invoice(models.Model):
    class Meta:
        db_table = "invoices"
        verbose_name = "Выставленный счёт"
        verbose_name_plural = "Выставленные счета"

    id = models.BigAutoField(primary_key=True)
    status = models.CharField(max_length=20, default="not_payed")

    invoice_type = models.CharField(max_length=33, default="UNKNOWN")
    invoice_template = models.ForeignKey(
        "InvoiceTemplate", on_delete=models.SET_NULL, null=True, blank=True
    )

    user = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT, verbose_name="пользователь",
        related_name="invoices", related_query_name="invoices"
    )
    bot = models.ForeignKey(
        "ClientBot", on_delete=models.PROTECT,
        verbose_name="бот, в котором выставлен счёт",
        related_name="invoices", related_query_name="invoices", null=True, blank=True
    )
    group = models.ForeignKey("Group", on_delete=models.PROTECT, verbose_name="группа")
    creator = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT, verbose_name="создатель",
        related_name="created_invoices", related_query_name="created_invoices"
    )
    currency = models.CharField(max_length=20, verbose_name="валюта")
    _prices = models.JSONField(verbose_name="позиции", null=True, blank=True)
    count = models.IntegerField(verbose_name="Количество", default=1)
    title = models.TextField(verbose_name="заголовок", null=True, blank=True)
    description = models.TextField(
        verbose_name="описание", null=True, blank=True, default=None
    )
    photo = models.CharField(max_length=255, null=True, blank=True, verbose_name="фото")
    need_name = models.BooleanField(default=False, verbose_name="имя обязательное")
    need_phone_number = models.BooleanField(
        default=False, verbose_name="номер телефона обязательный"
    )
    need_email = models.BooleanField(
        default=False, verbose_name="электронная почта обязательная"
    )
    time_created = models.DateTimeField(auto_now_add=True, verbose_name="дата создания")
    expiration_datetime = models.DateTimeField(
        verbose_name="дата конца принятия оплат", null=True, blank=True
    )
    _live_time = models.FloatField(null=True, blank=True, verbose_name="строк жизни")
    order_info = models.JSONField(
        verbose_name="дополнительные данные", null=True, blank=True
    )

    first_name = models.CharField(max_length=256, null=True, blank=True)
    last_name = models.CharField(max_length=256, null=True, blank=True)
    email = models.CharField(max_length=256, null=True, blank=True)
    phone = models.CharField(max_length=256, null=True, blank=True)

    payment_bot = models.ForeignKey(
        "ClientBot", on_delete=models.PROTECT, null=True, blank=True,
        verbose_name="бот, в котором оплачен счёт",
        related_name="invoices_from_other_bots",
        related_query_name="invoices_from_other_bots"
    )
    incust_check = models.JSONField(verbose_name="чек инкаст", null=True, blank=True)
    check_url = models.TextField(null=True, blank=True)
    payment_bot_menu = models.ForeignKey(
        "ClientBot", on_delete=models.PROTECT, null=True, blank=True,
        verbose_name="другой бот для QR меню",
        related_name="invoices_from_other_bot_qr_menu",
        related_query_name="invoices_from_other_bot_qr_menu"
    )
    menu_in_store = models.ForeignKey(
        "MenuInStore", on_delete=models.PROTECT, null=True, blank=True,
        verbose_name="QR меню",
        related_name="invoices_from_qr_menu", related_query_name="invoices_from_qr_menu"
    )
    message_id = models.IntegerField(
        null=True, blank=True, verbose_name='invoice message id '
    )
    payment_mode = models.CharField(max_length=20)

    external_transaction_id = models.CharField(
        max_length=100, null=True, blank=True,
    )
    incust_transaction_id = models.CharField(
        max_length=100, null=True, blank=True,
    )
    loyalty_settings = models.ForeignKey(
        "LoyaltySettings", on_delete=models.SET_NULL, null=True, blank=True
    )
    
    # Додаткові поля для лояльності (замість JSON зберігання)
    bonuses_added_amount = models.BigIntegerField(default=0)  # Нараховані бонуси
    loyalty_coupons_data = models.JSONField(default=None, null=True, blank=True)  # Список купонів
    special_accounts_charges = models.JSONField(default=None, null=True, blank=True)  # Список спеціальних рахунків
    loyalty_skip_message = models.BooleanField(default=False)  # Пропуск повідомлень
    
    # Додаткові поля для повного функціоналу лояльності
    loyalty_discount_amount = models.BigIntegerField(default=0)  # Знижка в копійках 
    loyalty_amount = models.BigIntegerField(default=0)  # Загальна сума чеку в копійках
    loyalty_amount_to_pay = models.BigIntegerField(default=0)  # Сума до оплати в копійках
    loyalty_transaction_data = models.JSONField(default=None, null=True, blank=True)  # Дані транзакції
    loyalty_implemented_rules = models.JSONField(default=None, null=True, blank=True)  # Застосовані правила лояльності
    is_loyalty_transaction_completed = models.BooleanField(default=False)  # Транзакція лояльності завершена
    
    # Поля loyalty_first_stage_data та loyalty_second_stage_data видалені як непотрібні
    
    client_redirect_url = models.TextField(null=True, blank=True)
    successful_payment_callback_url = models.TextField(null=True, blank=True)

    webhook_result = models.JSONField(null=True, blank=True)

    uuid_id = models.CharField(max_length=36, default=None, null=True, blank=True)
    payer = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT, null=True, blank=True,
        related_name="payed_invoices", related_query_name="payed_invoices"
    )
    is_friend = models.BooleanField(default=None, null=True, blank=True)

    user_comment = models.TextField(null=True, blank=True)

    qr_mode = models.CharField(max_length=3, null=True, blank=True)

    shipment_cost = models.BigIntegerField(default=0)
    custom_payment_cost = models.BigIntegerField(
        default=0
    )  # will be removed on payments refactor

    before_loyalty_sum = models.BigIntegerField(default=0)
    discount = models.BigIntegerField(default=0)
    bonuses_redeemed = models.BigIntegerField(default=0)
    discount_and_bonuses_redeemed = models.BigIntegerField(default=0)

    is_ewallet_discount = models.BooleanField(default=False)

    total_sum = models.BigIntegerField(default=0)  # sum after discount with shipping ?

    # sum_before_extra_fee = models.BigIntegerField(default=0)

    total_sum_with_extra_fee = models.BigIntegerField(default=0)

    tips_sum = models.BigIntegerField(default=0)
    sum_to_pay = models.BigIntegerField(default=0)

    payer_fee = models.BigIntegerField(verbose_name='payer fee amount', default=0)
    paid_sum = models.BigIntegerField(verbose_name='paid sum + payer_fee', default=0)

    _is_read = models.BooleanField(default=False)
    read_by_user = models.ForeignKey(
        "TelegramUser", on_delete=models.SET_NULL, null=True, blank=True
    )
    read_status_change_datetime = models.DateTimeField(null=True, blank=True)

    paid_datetime = models.DateTimeField(null=True, blank=True)

    ewallet = models.ForeignKey(
        "EWallet", on_delete=models.PROTECT, null=True, blank=True,
        verbose_name="eWallet",
        related_name="invoices_from_ewallet", related_query_name="invoices_from_ewallet"
    )

    extra_params = models.JSONField(null=True, blank=True)

    utm_labels = models.JSONField(
        null=True,
        blank=True,
        verbose_name="Analytics Data",
    )

    @property
    def live_time(self) -> timedelta:
        if self._live_time:
            return timedelta(seconds=self._live_time)

    def __str__(self):
        return f"({self.id}) пользователь: {self.user.full_name} | {self.title}"


class InvoiceItem(models.Model):
    class Meta:
        db_table = "invoice_items"

    id = models.BigAutoField(primary_key=True)
    time_created = models.DateTimeField(default=timezone.now)

    name = models.CharField(max_length=512)
    category = models.CharField(max_length=255, default="uncategorized")
    quantity = models.IntegerField(default=1)

    item_code = models.CharField(max_length=255, null=True, blank=True)

    # unit
    price = models.BigIntegerField()
    unit_discount = models.BigIntegerField(default=0)
    unit_bonuses_redeemed = models.BigIntegerField(default=0)
    unit_discount_and_bonuses_redeemed = models.BigIntegerField(default=0)
    final_price = models.BigIntegerField(default=0)

    # unit * quantity
    before_loyalty_sum = models.BigIntegerField()
    discount = models.BigIntegerField(default=0)
    bonuses_redeemed = models.BigIntegerField(default=0)
    discount_and_bonuses_redeemed = models.BigIntegerField(default=0)
    final_sum = models.BigIntegerField()

    invoice = models.ForeignKey("Invoice", on_delete=models.RESTRICT)
