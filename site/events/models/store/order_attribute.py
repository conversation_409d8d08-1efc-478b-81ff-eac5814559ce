from django.db import models


class OrderAttribute(models.Model):
    class Meta:
        db_table = "order_attributes"
        verbose_name = "ордер, копия атрибута товара"
        verbose_name_plural = "ордер, копии атрибутов товара"

    id = models.BigAutoField(primary_key=True)
    name = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Имя атрибута на момент заказа"
    )
    group_name = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Имя группы атрибутов на момент заказа"
    )
    price_impact = models.BigIntegerField(
        blank=True, null=True, verbose_name="Копия влияние цены на атрибут"
    )
    quantity = models.IntegerField(
        blank=True, null=True, verbose_name="Копия кол-во атрибута"
    )

    order_product = models.ForeignKey(
        "OrderProduct",
        on_delete=models.CASCADE,
        verbose_name="Связь с order продуктом"
    )

    attribute = models.ForeignKey(
        "StoreAttribute",
        on_delete=models.CASCADE,
        verbose_name="Связь с атрибутом"
    )

    group_attribute = models.ForeignKey(
        "StoreAttributeGroup",
        on_delete=models.CASCADE,
        verbose_name="Связь с группой атрибутов"
    )



