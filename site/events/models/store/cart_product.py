from django.db import models

from .cart import StoreCart
from .product import StoreProduct
from .store import Store


class StoreCartProduct(models.Model):
    class Meta:
        db_table = "store_cart_product"
        verbose_name = "Магазин, продукт корзины"
        verbose_name_plural = "Магазин, продукты корзины"
        # unique_together = ("cart_id", "product_id")

    id = models.BigAutoField(primary_key=True)
    quantity = models.IntegerField(verbose_name="Количество")

    product = models.ForeignKey(
        StoreProduct, blank=True, null=True,
        on_delete=models.CASCADE, verbose_name="Продукт"
    )
    store = models.ForeignKey(
        Store, on_delete=models.CASCADE, verbose_name="Точка продажи"
    )
    cart = models.ForeignKey(
        StoreCart, blank=True, null=True,
        on_delete=models.CASCADE, verbose_name="Корзина"
    )
    display_name = models.TextField(blank=True, null=True, )
    display_description = models.TextField(blank=True, null=True, )
    floating_sum = models.BigIntegerField(verbose_name="Указанная пользователем цена", default=0, blank=True, null=True)
