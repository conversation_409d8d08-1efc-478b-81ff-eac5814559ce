from django.db import models

from .brand import Brand


class StoreProduct(models.Model):
    class Meta:
        db_table = "store_products"
        verbose_name = "Магазин, товар"
        verbose_name_plural = "Магазин, товари"

        indexes = [
            models.Index(
                fields=['product_id', 'is_deleted'],
                name='idx_store_products_id_deleted'
            ),
        ]

    id = models.BigAutoField(primary_key=True)

    is_enabled = models.BooleanField(default=True)

    product_group = models.ForeignKey(
        "StoreProductGroup", on_delete=models.SET_NULL, null=True, blank=True
    )

    product_id = models.CharField(
        max_length=255, null=True, blank=True, verbose_name="Идентификатор"
    )

    get_order_id = models.IntegerField(
        blank=True, null=True, verbose_name="ID GetOrder"
    )

    name = models.CharField(max_length=255, verbose_name="Имя")

    price = models.BigIntegerField(verbose_name="Цена", default=0)
    old_price = models.BigIntegerField(
        verbose_name="Старая цена", default=0, blank=True, null=True
    )

    media = models.ForeignKey(
        "MediaObject",
        on_delete=models.RESTRICT,
        null=True, blank=True,
        related_name="store_products",
        related_query_name="store_products"
    )
    thumbnail_media = models.ForeignKey(
        "MediaObject",
        on_delete=models.RESTRICT,
        null=True, blank=True,
        related_name="thumbnails_store_products",
        related_query_name="thumbnails_store_products"
    )

    gallery = models.ForeignKey(
        "Gallery", on_delete=models.RESTRICT, null=True, blank=True
    )

    description = models.TextField(blank=True, null=True, verbose_name="Описание")

    is_available = models.BooleanField(default=True, verbose_name="Отображение")
    is_deleted = models.BooleanField(default=False, verbose_name="Удалён")

    buy_min_quantity = models.IntegerField(
        default=1, verbose_name="Минимальное количество для покупки"
    )
    position = models.IntegerField(verbose_name="Порядок", default=0)

    brand = models.ForeignKey(
        Brand, blank=True, null=True, on_delete=models.CASCADE, verbose_name="Бренд"
    )
    external_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="External ID"
    )
    external_type = models.CharField(
        max_length=99, blank=True, null=True, verbose_name="External ID source"
    )
    is_weight = models.BooleanField(default=False, verbose_name="Ваговий товар")
    weight_unit = models.CharField(
        max_length=59, blank=True, null=True,
        verbose_name="одиниці виміру вагового товару"
    )

    floating_sum_enabled = models.BooleanField(
        default=False, verbose_name="Плавающая цена включена"
    )
    floating_sum_min = models.BigIntegerField(
        verbose_name="Минимальная цена для плавающей цены", default=0, blank=True,
        null=True
    )
    floating_sum_max = models.BigIntegerField(
        verbose_name="Максимальная цена для плавающей цены", default=0, blank=True,
        null=True
    )
    floating_sum_options = models.JSONField(
        null=True, blank=True, verbose_name="Опции для плавающей цены"
    )
    floating_sum_user_sum_enabled = models.BooleanField(
        default=True, verbose_name="Ввод пользователя для плавающей цены включен"
    )
    floating_sum_value = models.CharField(
        max_length=255, verbose_name="Значение поля плавающая цена", null=True,
        blank=True
    )

    type = models.CharField(
        max_length=255, verbose_name="Тип продукта", default="goods"
    )

    pti_info_text = models.CharField(
        max_length=255, verbose_name="Значение поля для кнопки инфо", null=True,
        blank=True
    )
    pti_info_link = models.CharField(
        max_length=255, verbose_name="Значение поля для кнопки инфо-ссылки", null=True,
        blank=True
    )

    liqpay_id = models.CharField(
        max_length=99, null=True, blank=True, default=None,
        verbose_name="Ідентифікатор Liqpay"
    )
    liqpay_unit_name = models.CharField(
        max_length=99, null=True, blank=True, default=None,
        verbose_name="найменування одиниці вимірювання Liqpay"
    )
    liqpay_codifier = models.CharField(
        max_length=29, null=True, blank=True, default=None,
        verbose_name="значення з довідника УКТВЕД"
    )
    liqpay_tax_list = models.CharField(
        max_length=1, null=True, blank=True, default=None,
        verbose_name="Система оподаткування (А, Б, В, Г)"
    )

    need_auth = models.BooleanField(
        default=False, verbose_name="Потрібна авторизація для покупки"
    )
    floating_qty_enabled = models.BooleanField(
        default=False, verbose_name="Плавающая кількість включена"
    )

    _internal_name = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Внутрішнє ім'я"
    )

    task = models.ForeignKey(
        "Task",
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="store_product_task",
        related_query_name="store_product_task"
    )

    topup_server_api_url = models.CharField(max_length=255, null=True, blank=True)
    topup_terminal_api_key = models.CharField(max_length=255, null=True, blank=True)
    topup_account_id = models.CharField(max_length=255, null=True, blank=True)
    topup_enabled_card = models.BooleanField(default=None, blank=True, null=True)

    group_hash = models.CharField(max_length=64, null=True, blank=True)
    
    incust_terminal_id = models.CharField(max_length=255, null=True, blank=True)

    @property
    def internal_name(self):
        return self._internal_name or self.name

    def __str__(self):
        return self.name


class StoreProductGroup(models.Model):
    class Meta:
        db_table = "store_product_groups"
        verbose_name = "Групи товарів"
        verbose_name_plural = "Група товарів"

    id = models.BigAutoField(primary_key=True)

    is_deleted = models.BooleanField(default=False)

    external_id = models.CharField(max_length=255, blank=True, null=True)
    external_type = models.CharField(max_length=9, blank=True, null=True)

    name = models.CharField(max_length=255)

    brand = models.ForeignKey(Brand, on_delete=models.CASCADE)


class StoreProductGroupCharacteristic(models.Model):
    class Meta:
        db_table = "store_product_group_characteristics"
        verbose_name = "Характеристики групи товарів"
        verbose_name_plural = "Характеристика групи товарів"

    product_group = models.ForeignKey(
        StoreProductGroup,
        on_delete=models.CASCADE,
        verbose_name="група товарів"
    )

    characteristic = models.ForeignKey(
        "StoreCharacteristic",
        on_delete=models.CASCADE,
        verbose_name="характеристика"
    )

    is_modifier = models.BooleanField(default=False)
    show_one_modification = models.BooleanField(default=False)


class StoreProductSpotPrice(models.Model):
    class Meta:
        db_table = "store_product_spot_prices"
        verbose_name = "Ціни на товари у різних точках"
        verbose_name_plural = "Ціна на товар у точці"
        unique_together = ("product", "store")

    id = models.BigAutoField(primary_key=True)

    product = models.ForeignKey(
        StoreProduct, on_delete=models.CASCADE, verbose_name="товар"
    )
    store = models.ForeignKey("Store", on_delete=models.CASCADE, verbose_name="магазин")

    price = models.BigIntegerField(verbose_name="Ціна")
    old_price = models.BigIntegerField(
        verbose_name="Стара ціна", default=0, blank=True, null=True
    )
