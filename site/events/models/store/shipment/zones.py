from django.db import models


class ShipmentZone(models.Model):

    class Meta:
        db_table = "shipment_zones"
        verbose_name = "Зона доставки"
        verbose_name_plural = "Зоны доставки"

    id = models.BigAutoField(primary_key=True)
    shipment = models.ForeignKey(
        "StoreCustomSettings", null=True,  #TODO: shipments task, replace to False after migration
        on_delete=models.CASCADE,
        verbose_name="Доставка"
    )
    name = models.CharField(max_length=255, null=False, verbose_name="Название")

    is_distance = models.BooleanField(
        default=False,
        verbose_name="Использовать радиус для области доставки"
    )
    is_polygon = models.BooleanField(
        default=False,
        verbose_name="Использовать полигон для области доставки"
    )
    is_swap_coordinates = models.BooleanField(
        default=True,
        verbose_name="Поменять местами координаты широты и долготы"
    )

    polygon = models.JSONField(null=True, blank=True, verbose_name="Область доставки")
    distance = models.IntegerField(default=0, verbose_name="Радиус области доставки")
