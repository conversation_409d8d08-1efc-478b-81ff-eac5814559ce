from django.db import models


class ShipmentPriceToSettings(models.Model):

    class Meta:
        db_table = "shipment_price_to_settings"
        verbose_name = "Связь прайса с настройками"
        verbose_name_plural = "Связи прайсов с настройками"

    id = models.BigAutoField(primary_key=True)
    shipment_price = models.ForeignKey(
        "ShipmentPrice", to_field="id",
        on_delete=models.CASCADE, null=False,
        related_name="shipment_price",
        verbose_name="Прайс"
    )
    settings = models.ForeignKey(
        "BrandCustomSettings", to_field="id",
        on_delete=models.CASCADE,
        null=True, blank=True, default=None,
        related_name="settings_to_price",
        verbose_name="Настройки доставки"
    )
    zone = models.ForeignKey(
        "ShipmentZone", to_field="id",
        on_delete=models.CASCADE,
        null=True, blank=True, default=None,
        related_name="zone_to_price",
        verbose_name="Зона доставки"
    )


class ShipmentPrice(models.Model):

    class Meta:
        db_table = "shipment_prices"
        verbose_name = "Прайс"
        verbose_name_plural = "Прайсы"

    id = models.BigAutoField(primary_key=True)
    _cost_delivery = models.BigIntegerField(default=0, verbose_name="Стоимость доставки")

    _minimum_order_amount = models.BigIntegerField(
        default=0,
        verbose_name="Минимальная сумма заказа"
    )

    _maximum_order_amount = models.BigIntegerField(
        default=0,
        verbose_name="Максимальная сумма заказа"
    )
