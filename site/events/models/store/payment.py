from django.db import models


class Payment(models.Model):
    class Meta:
        db_table = "payments"
        verbose_name = "Платежі"
        verbose_name_plural = "Магазин, платежі"

    id = models.BigAutoField(primary_key=True)
    uuid_id = models.CharField(max_length=36, unique=True)
    invoice = models.ForeignKey('Invoice', on_delete=models.SET_NULL, null=True)
    currency = models.CharField(max_length=3)
    amount = models.BigIntegerField(null=True)
    status = models.CharField(max_length=99)
    payment_method = models.CharField(max_length=99, null=True)
    card_type = models.CharField(max_length=99, null=True)
    card_mask = models.CharField(max_length=99, null=True)
    create_date = models.DateTimeField(auto_now_add=True)
    external_id = models.CharField(max_length=99, null=True)
    return_url = models.Char<PERSON>ield(max_length=999, null=True, default=None)
    user = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT, verbose_name="user", null=True,
        default=None
    )
    is_sandbox = models.BooleanField(null=True, default=None)
    payment_settings = models.ForeignKey(
        "PaymentSettings", on_delete=models.PROTECT, null=True, default=None
    )
    object_payment_settings = models.ForeignKey(
        "ObjectPaymentSettings", on_delete=models.PROTECT, null=True, default=None
    )
    payment_data = models.JSONField(null=True, default=None)

    ewallet_discount_percent = models.DecimalField(
        max_digits=4, decimal_places=2, default=0
    )
    ewallet_discount_amount = models.DecimalField(
        max_digits=12, decimal_places=2, default=0
    )


class PaymentExt(models.Model):
    class Meta:
        db_table = "payments_ext"
        verbose_name = "Атрибути платежів"
        verbose_name_plural = "Магазин, атрибути платежів"

    id = models.BigAutoField(primary_key=True)
    payment = models.OneToOneField(Payment, on_delete=models.CASCADE)
    create_date = models.DateTimeField(auto_now_add=True)
    json_data = models.JSONField(null=True, default=None)
