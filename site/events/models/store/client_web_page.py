from django.db import models

from events.models import Group


class ClientWebPage(models.Model):
    class Meta:
        db_table = "client_web_pages"
        verbose_name = "Кастомні web сторінки, web сторінки клієнта"
        verbose_name_plural = "Кастомні web сторінки, web сторінки клієнта"

        indexes = []

    id = models.BigAutoField(primary_key=True)

    type = models.CharField(max_length=14, null=False, blank=False)

    container_max_width = models.CharField(
        max_length=14, default='xl', verbose_name="Максимальна ширина контейнера"
    )

    custom_container_max_width = models.IntegerField(
        blank=True, null=True, verbose_name="Максимальна ширина контейнера"
    )

    is_enabled = models.BooleanField(default=False)

    show_in_navbar = models.BooleanField(default=False)

    show_in_profile = models.BooleanField(default=False)

    title = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Заголовок сторінки"
    )

    button_title = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Заголовок посилання"
    )

    slug = models.TextField(verbose_name="заголовок", null=False, blank=False)

    page_content = models.JSONField(
        null=True,
        blank=True,
        verbose_name="Контент сторінки"
    )

    position = models.IntegerField(verbose_name="Порядок", default=0)

    group = models.ForeignKey(
        Group, blank=False, null=False, on_delete=models.CASCADE, verbose_name="Профіль"
    )

    _internal_name = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Внутрішнє ім'я"
    )

    @property
    def internal_name(self):
        return self._internal_name or self.name

    def __str__(self):
        return self.name
