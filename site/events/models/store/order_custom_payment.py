from django.db import models


class OrderCustomPayment(models.Model):
    class Meta:
        db_table = "order_custom_payments"
        verbose_name = "Кастомный метод оплаты (Заказ)"
        verbose_name_plural = "Кастомные методы оплаты (Заказ)"

    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255, verbose_name="Название")
    _price = models.BigIntegerField(default=0, verbose_name="Цена")
    comment = models.TextField(
        null=True, blank=True, default=None,
        verbose_name="Комментарий к кастомному методу оплаты"
    )

    store_order = models.ForeignKey(
        "StoreOrder",
        related_name="order_custom_payments_store_order",
        on_delete=models.CASCADE,
        default=None,
        verbose_name="Связь с ордером"
    )

    label_comment = models.TextField(null=True, blank=True, default=None, verbose_name="Label комментария")

    info = models.TextField(
        null=True, blank=True, default=None,
        verbose_name="Инфо после кастомномого метода оплаты"
    )
