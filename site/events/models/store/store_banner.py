from django.db import models


class StoreBanner(models.Model):
    class Meta:
        db_table = "store_banners"
        verbose_name = "Баннер магазина"
        verbose_name_plural = "Баннеры магазина"

    id = models.BigAutoField(primary_key=True)
    media = models.ForeignKey("MediaObject", null=True, blank=True, on_delete=models.SET_NULL)
    store = models.ForeignKey(
        "Store", on_delete=models.CASCADE, verbose_name="Точка продажи"
    )
    name = models.CharField(max_length=255, null=True, blank=True, verbose_name="ім`я")
    url = models.CharField(max_length=1024, blank=True, null=True, verbose_name="URL")
    position = models.IntegerField(blank=True, null=True, verbose_name="Позиция")
    is_visible = models.BooleanField(default=True, verbose_name="Видимость")

    task = models.ForeignKey("Task", on_delete=models.SET_NULL, null=True, blank=True)
