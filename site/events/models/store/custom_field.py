from django.db import models

from events.models.store.store import Store


class StoreCustomField(models.Model):
    class Meta:
        db_table = "store_custom_fields"
        verbose_name = "Магазин, кастомное поле"
        verbose_name_plural = "Магазин, кастомные поля"

    id = models.BigAutoField(primary_key=True)

    name = models.CharField(
        max_length=255, verbose_name="Название"
    )
    value = models.CharField(
        null=True, blank=True,
        max_length=255, verbose_name="Значение"
    )

    store = models.ForeignKey(
        Store, on_delete=models.CASCADE, verbose_name="Магазин"
    )
