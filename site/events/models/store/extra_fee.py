from datetime import datetime

from django.db import models


class ExtraFeeSettings(models.Model):
    id = models.BigAutoField(primary_key=True)
    group = models.ForeignKey('Group', on_delete=models.CASCADE, related_name='extra_fee_settings')
    name = models.CharField(max_length=255)
    extra_fee_percent = models.CharField(max_length=255, null=True, blank=True)
    extra_fee_value = models.CharField(max_length=255, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    time_created = models.DateTimeField(default=datetime.utcnow)
    position = models.IntegerField(blank=False, null=False, verbose_name="Порядок", default=0)
    is_deleted = models.BooleanField(default=False, null=True, blank=True)

    class Meta:
        db_table = 'extra_fee_settings'

    def __str__(self):
        return self.name


class ExtraFeeJournal(models.Model):
    id = models.BigAutoField(primary_key=True)
    extra_fee = models.ForeignKey(ExtraFeeSettings, on_delete=models.CASCADE, related_name='journal')
    invoice = models.ForeignKey(
        'Invoice', on_delete=models.CASCADE, null=True, blank=True, related_name='extra_fee_journals'
    )
    order = models.ForeignKey(
        'StoreOrder', on_delete=models.CASCADE, null=True, blank=True, related_name='extra_fee_journals'
    )
    name = models.CharField(max_length=255)
    extra_fee_percent = models.CharField(max_length=255, null=True, blank=True)
    extra_fee_value = models.CharField(max_length=255, null=True, blank=True)
    applied_amount = models.BigIntegerField(default=0)
    applied_amount_float = models.FloatField(default=0.00)
    time_created = models.DateTimeField(default=datetime.utcnow)

    class Meta:
        db_table = 'extra_fee_journal'

    def __str__(self):
        return f"{self.name} - {self.applied_amount}"
