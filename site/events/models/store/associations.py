from django.db import models

from .attribute_group import StoreAttributeGroup
from .custom_settings import BrandCustomSettings
from .product import StoreProduct


class StoreCategoryToStore(models.Model):
    class Meta:
        db_table = "store_categories_to_stores"
        constraints = [
            models.UniqueConstraint(
                fields=['category', 'store'], name='unique_category_store'
            )
        ]

    id = models.BigAutoField(primary_key=True)
    category = models.ForeignKey("StoreCategory", on_delete=models.CASCADE)
    store = models.ForeignKey("Store", on_delete=models.CASCADE)


class ProductToCategory(models.Model):
    class Meta:
        db_table = "products_to_categories"
        constraints = [
            models.UniqueConstraint(
                fields=['product', 'category'], name='unique_product_category'
            )
        ]

    id = models.BigAutoField(primary_key=True)
    product = models.ForeignKey("StoreProduct", on_delete=models.CASCADE)
    category = models.ForeignKey("StoreCategory", on_delete=models.CASCADE)


class ProductToStore(models.Model):
    class Meta:
        db_table = "product_to_stores"
        constraints = [
            models.UniqueConstraint(
                fields=['product', 'store'], name='unique_product_store'
            )
        ]

    id = models.BigAutoField(primary_key=True)
    product = models.ForeignKey("StoreProduct", on_delete=models.CASCADE)
    store = models.ForeignKey("Store", on_delete=models.CASCADE)


class AttributeGroupToProduct(models.Model):

    class Meta:
        db_table = "attribute_groups_to_products"
        verbose_name = "Магазин, связь продуктов и групп аттрибутов"
        verbose_name_plural = "Магазин, связи продуктов и групп аттрибутов"
        constraints = [
            models.UniqueConstraint(
                fields=['attribute_group', 'product'],
                name='unique_attribute_group_product'
            )
        ]

    id = models.BigAutoField(primary_key=True)
    attribute_group = models.ForeignKey(
        StoreAttributeGroup, to_field="id", on_delete=models.SET_NULL, null=True,
        verbose_name="Группа аттрибутов"
    )
    product = models.ForeignKey(
        StoreProduct, to_field="id", on_delete=models.SET_NULL, null=True,
        verbose_name="Продукт"
    )


class PaymentToShipment(models.Model):

    class Meta:
        db_table = "payments_to_shipments"
        verbose_name = "Магазин, связь кастомных оплат с доставками"
        verbose_name_plural = "Магазин, связи кастомных оплат и доставок"

    id = models.BigAutoField(primary_key=True)
    payment = models.ForeignKey(
        BrandCustomSettings, to_field="id",
        on_delete=models.SET_NULL, null=True,
        related_name="custom_payment",
        verbose_name="Кастомная оплата"
    )
    shipment = models.ForeignKey(
        BrandCustomSettings, to_field="id",
        on_delete=models.SET_NULL, null=True,
        related_name="custom_shipment",
        verbose_name="Доставка"
    )


class PaymentSettingsToShipment(models.Model):
    class Meta:
        db_table = "payments_settings_to_shipments"
        verbose_name = "Магазин, связь методов оплат с доставками"
        verbose_name_plural = "Магазин, связи методов оплат и доставок"

    id = models.BigAutoField(primary_key=True)
    payment_settings = models.ForeignKey(
        "PaymentSettings", to_field="id",
        on_delete=models.SET_NULL, null=True,
        related_name="payment_settings",
        verbose_name="Настройки оплаты"
    )
    shipment = models.ForeignKey(
        BrandCustomSettings, to_field="id",
        on_delete=models.SET_NULL, null=True,
        related_name="custom_shipment_settings",
        verbose_name="Доставка"
    )


class ClientWebPageToStore(models.Model):
    class Meta:
        db_table = "client_web_page_to_stores"
        constraints = [
            models.UniqueConstraint(
                fields=['client_web_page', 'store'], name='unique_client_web_page_store'
            )
        ]

    id = models.BigAutoField(primary_key=True)
    client_web_page = models.ForeignKey(
        "ClientWebPage", on_delete=models.CASCADE, verbose_name="Web страница"
    )
    store = models.ForeignKey("Store", on_delete=models.CASCADE, verbose_name="Магазин")


class ClientWebPageToInvoiceTemplate(models.Model):
    class Meta:
        db_table = "client_web_page_to_invoice_templates"
        constraints = [
            models.UniqueConstraint(
                fields=['client_web_page', 'invoice_template'],
                name='unique_client_web_page_invoice_template'
            )
        ]

    id = models.BigAutoField(primary_key=True)
    client_web_page = models.ForeignKey(
        "ClientWebPage", on_delete=models.CASCADE, verbose_name="Web страница"
    )
    invoice_template = models.ForeignKey(
        "InvoiceTemplate", on_delete=models.CASCADE, verbose_name="Шаблон счёта"
    )
