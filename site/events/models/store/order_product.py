from django.db import models


class OrderProduct(models.Model):
    class Meta:
        db_table = "order_products"
        verbose_name = "ордер, копия  товара"
        verbose_name_plural = "ордер, копии товаров"

    id = models.BigAutoField(primary_key=True)
    name = models.CharField(
        max_length=1024, blank=True, null=True,
        verbose_name="Имя товара на момент заказа"
    )
    price = models.BigIntegerField(
        blank=True, null=True, verbose_name="Копия цены товара"
    )
    price_with_attributes = models.BigIntegerField(
        default=0, verbose_name="цена с учетом атрибутов(unit)"
    )
    final_price = models.BigIntegerField(
        default=0, verbose_name="кінцева ціна за одиницю"
    )

    discount_amount = models.BigIntegerField(
        default=0, verbose_name="сумма скидки(unit)"
    )
    discount_sum = models.BigIntegerField(
        default=0, verbose_name="сумма скидки по позиции"
    )
    price_after_loyalty = models.BigIntegerField(
        default=0, verbose_name="цена с учетом лояльности(unit)"
    )

    before_loyalty_sum = models.BigIntegerField(
        default=0, verbose_name="сумма позиции без модификаций"
    )
    total_sum = models.BigIntegerField(
        default=0, verbose_name="финальная сумма позиции"
    )

    bonuses_redeemed = models.BigIntegerField(
        default=0, verbose_name="бонусы списанные с лояльности(unit)"
    )
    bonuses_redeemed_sum = models.BigIntegerField(
        default=0, verbose_name="бонусы списанные с лояльности по позиции"
    )
    discount_and_bonuses = models.BigIntegerField(
        default=0, verbose_name="бонусы + скидка с лояльности(unit)"
    )
    discount_and_bonuses_sum = models.BigIntegerField(
        default=0, verbose_name="бонусы + скидка с лояльности по позиции"
    )
    quantity = models.IntegerField(
        blank=True, null=True, verbose_name="Копия количества товара"
    )
    topup_charge = models.BigIntegerField(
        default=0, verbose_name="комиссия за пополнение счета"
    )

    store_order = models.ForeignKey(
        "StoreOrder",
        related_name="order_products_store_order",
        on_delete=models.CASCADE,
        default=None,
        verbose_name="Связь с ордером"
    )

    product = models.ForeignKey(
        "StoreProduct",
        on_delete=models.CASCADE,
        default=None,
        verbose_name="Связь с продуктом"
    )

    display_name = models.CharField(
        max_length=1024, blank=True, null=True,
        verbose_name="Имя товара на момент заказа"
    )
    display_description = models.CharField(
        max_length=1024, blank=True, null=True,
        verbose_name="Описание товара на момент заказа"
    )

    incust_pay_term_api_key = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="апи ключ терминала инкаст"
    )
    incust_pay_term_url = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="урл терминала инкаст"
    )
    incust_account = models.JSONField(
        null=True, blank=True, verbose_name="Счет incust"
    )
    incust_card = models.TextField(
        blank=True, null=True, verbose_name="номер карты инкаст"
    )
    incust_user_transaction = models.JSONField(
        null=True, blank=True, verbose_name="Результат пополнения пользователя"
    )
    incust_owner_transaction = models.JSONField(
        null=True, blank=True, verbose_name="Результат списания хозяина профиля"
    )
    charge_percent = models.FloatField(default=0.0)
    charge_fixed = models.FloatField(default=0.0)
    is_topup_error = models.BooleanField(default=False)

    loyalty_settings = models.ForeignKey(
        "LoyaltySettings",
        related_name="order_products_loyalty_settings",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="connection with loyalty settings"
    )

