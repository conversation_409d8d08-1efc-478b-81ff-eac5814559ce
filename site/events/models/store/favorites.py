from django.db import models

from .store import Store
from .. import TelegramUser


class StoreFavorite(models.Model):
    class Meta:
        db_table = "store_favorites"
        verbose_name = "Магазин, избранное"
        verbose_name_plural = "Магазин, избранные"
        unique_together = ("user", "store")

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey(
        TelegramUser, blank=True, null=True, on_delete=models.CASCADE, verbose_name="Пользователь"
    )
    store = models.ForeignKey(
        Store, on_delete=models.CASCADE, verbose_name="Точка продажи"
    )
