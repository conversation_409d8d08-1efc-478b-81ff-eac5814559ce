from django.db import models


class StoreOrderBillingAddress(models.Model):
    class Meta:
        db_table = "store_order_billing_addresss"
        verbose_name = "Магазин, Платежный адрес"
        verbose_name_plural = "Магазин, Платежные адреса"

    id = models.BigAutoField(primary_key=True)
    order = models.OneToOneField(
        "StoreOrder", on_delete=models.CASCADE, verbose_name="Заказ",
        related_name="order", related_query_name="order"
    )
    counterparty_type = models.CharField(max_length=15, default="organisation", verbose_name="тип контрагента")

    first_name = models.Char<PERSON>ield(max_length=255, blank=True, null=True, verbose_name="Имя")
    last_name = models.CharField(max_length=255, blank=True, null=True, verbose_name="Фамилия")

    company_name = models.Char<PERSON>ield(max_length=255, blank=True, null=True, verbose_name="Название компании")
    vat_number = models.CharField(max_length=255, blank=True, null=True, verbose_name="Номер плательщика НДС")
    registration_number = models.CharField(
        max_length=255,
        blank=True, null=True,
        verbose_name="Регистрационный номер компании"
    )

    country = models.CharField(max_length=255, blank=True, null=True, verbose_name="Страна")
    state = models.CharField(max_length=255, blank=True, null=True, verbose_name="Штат/Провинция/Регион")
    city = models.CharField(max_length=255, blank=True, null=True, verbose_name="Город")
    zip_code = models.CharField(max_length=255, blank=True, null=True, verbose_name="Почтовый индекс")

    address_1 = models.CharField(max_length=255, blank=True, null=True, verbose_name="Адрес строка 1")
    address_2 = models.CharField(max_length=255, blank=True, null=True, verbose_name="Адрес строка 2")
    phone_number = models.CharField(max_length=255, blank=True, null=True, verbose_name="Номер телефона")
