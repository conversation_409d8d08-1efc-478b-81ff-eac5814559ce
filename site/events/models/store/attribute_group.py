from django.db import models

from .brand import Brand


class StoreAttributeGroup(models.Model):
    class Meta:
        db_table = "store_attribute_groups"
        verbose_name = "Магазин, группа атрибутов"
        verbose_name_plural = "Магазин, группы атрибутов"

    id = models.BigAutoField(primary_key=True)
    attribute_group_id = models.CharField(max_length=255, verbose_name="Идентификатор")

    name = models.CharField(max_length=255, verbose_name="Имя")
    min = models.IntegerField(verbose_name="Минимум атрибутов")
    max = models.IntegerField(verbose_name="Максимум атрибутов", null=True, blank=True)
    is_deleted = models.BooleanField(default=False, verbose_name="Удалён")
    brand = models.ForeignKey(
        Brand, blank=True, null=True, on_delete=models.CASCADE, verbose_name="Бренд"
    )
    external_id = models.CharField(max_length=255, blank=True, null=True, verbose_name="External ID")
    external_type = models.CharField(max_length=99, blank=True, null=True, verbose_name="External ID source")

    position = models.IntegerField(verbose_name="Порядок", default=0)
    excel_row_number = models.PositiveSmallIntegerField(null=True, blank=True)

    _internal_name = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Внутрішнє ім'я"
    )
