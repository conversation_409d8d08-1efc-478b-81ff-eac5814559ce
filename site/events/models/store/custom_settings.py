from django.db import models


class BrandCustomSettings(models.Model):
    class Meta:
        db_table = "brand_custom_settings"
        verbose_name = "Настройка доставки и оплат бренда"
        verbose_name_plural = "Настройки доставки и оплат бренда"

    id = models.BigAutoField(primary_key=True)
    brand = models.ForeignKey("Brand", on_delete=models.CASCADE, verbose_name="Бренд")

    _name = models.CharField(max_length=255, verbose_name="Название")
    description = models.TextField(
        null=True, blank=True, default=None,
        verbose_name="Описание"
    )

    icon_path = models.CharField(
        max_length=255,
        null=True, blank=True, default=None,
        verbose_name="Путь к иконке"
    )
    _price = models.BigIntegerField(default=0, verbose_name="Цена")

    need_comment = models.BooleanField(default=True, verbose_name="Необходимость комментария")
    label_comment = models.TextField(null=True, blank=True, default=None, verbose_name="Label комментария")

    info = models.TextField(null=True, blank=True, default=None, verbose_name="Label інфо")

    custom_settings_group = models.ForeignKey(
        "BrandCustomSettings", on_delete=models.SET_NULL,
        related_name="custom_settings", related_query_name="custom_settings",
        blank=True, null=True, default=None,
        verbose_name="Группы кастомных настроек",
    )

    custom_type = models.CharField(max_length=50, verbose_name="кастомный тип")
    base_type = models.CharField(
        max_length=255, verbose_name="Базовый тип доставки",
        null=True, blank=True, default=None
    )

    _min_price = models.BigIntegerField(default=0, verbose_name="Минимальная сумма заказа")
    _max_price = models.BigIntegerField(default=0, verbose_name="Максимальная сумма заказа")

    delivery_time_enabled = models.BooleanField(default=True, verbose_name="Спрашивать время доставки")
    delivery_datetime_mode = models.CharField(
        max_length=50, verbose_name="Режим выбора времени доставки",
        null=False, blank=False, default="datetime"
    )
    delivery_time_warning_enabled = models.BooleanField(
        default=False,
        verbose_name="Показывать предупреждение о нерабочем времени"
    )

    allow_online_payment = models.BooleanField(default=True, verbose_name="Доступна онлайн оплата")
    allow_cash_payment = models.BooleanField(default=True, verbose_name="Доступна оплата наличными")

    is_paid_separately = models.BooleanField(default=False, verbose_name="Оплачивается отдельно")

    is_enabled = models.BooleanField(default=True, verbose_name="Включен")

    enabled_tips = models.BooleanField(default=False, verbose_name="Спрашивать чаевые")
    need_address = models.BooleanField(default=True, verbose_name="Необходимость ввода адреса")
    _map_countries_list = models.JSONField(
        null=True, blank=True, verbose_name="Список достпуных стран для запросов на maps api"
    )
    enabled_any_address_from_map = models.BooleanField(
        default=False, verbose_name="Разрешить выбирать любой адрес с карты"
    )

    liqpay_id = models.TextField(null=True, blank=True, default=None, verbose_name="Loyalty ID")

    media = models.ForeignKey("MediaObject", null=True, blank=True, on_delete=models.SET_NULL)

    is_deleted = models.BooleanField(default=False, verbose_name="Удален")


class StoreCustomSettings(models.Model):
    class Meta:
        db_table = "store_custom_settings"
        verbose_name = "Настройка доставки и оплат магазина"
        verbose_name_plural = "Настройки доставки и оплат магазина"

    id = models.BigAutoField(primary_key=True)
    store = models.ForeignKey("Store", on_delete=models.CASCADE, verbose_name="Магазин")

    custom_settings = models.ForeignKey(
        "BrandCustomSettings", on_delete=models.CASCADE,
        verbose_name="Кастомные настройки бренда"
    )

    is_enabled = models.BooleanField(null=True, default=None, verbose_name="Включен")
    is_shipment_zone = models.BooleanField(
        default=False,
        verbose_name="Включить настройки зон доставки"
    )
    _map_countries_list = models.JSONField(
        null=True, blank=True, verbose_name="Список достпуных стран для запросов на maps api"
    )
