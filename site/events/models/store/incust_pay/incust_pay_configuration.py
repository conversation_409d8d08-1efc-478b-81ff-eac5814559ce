from django.db import models
from django.utils import timezone


class IncustPayConfiguration(models.Model):
    class Meta:
        db_table = "incust_pay_configurations"

    id = models.BigAutoField(primary_key=True)

    brand = models.ForeignKey("Brand", on_delete=models.CASCADE)
    store = models.ForeignKey("Store", on_delete=models.CASCADE, null=True, blank=True)

    name = models.CharField(max_length=255)
    server_api_url = models.CharField(max_length=255)

    rules_type = models.CharField(max_length=20, default="by-all-rules")

    terminal_title = models.CharField(max_length=255)
    terminal_api_key = models.CharField(max_length=255, null=True, blank=True)
    terminal_server_api_url = models.CharField(max_length=255)

    time_created = models.DateTimeField(default=timezone.now)

    card_payment_enabled = models.BooleanField(default=False)

    charge_percent = models.FloatField(default=0.0)
    charge_fixed = models.FloatField(default=0.0)
