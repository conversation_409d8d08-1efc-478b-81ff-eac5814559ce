from django.db import models


class BusinessPaymentData(models.Model):
    class Meta:
        verbose_name = 'business payment data'
        verbose_name_plural = 'business payment data'
        db_table = 'business_payment_data'

    id = models.BigAutoField(primary_key=True)
    business_payment_setting = models.ForeignKey('BusinessPaymentSetting', on_delete=models.CASCADE, related_name='payment_data')
    
    payment_method = models.CharField(max_length=29)
    json_data = models.JSONField(null=True, default=None)
    is_enabled = models.BooleanField(default=True)
    
    is_deleted = models.BooleanField(default=False)
    time_created = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now=True)


class BusinessPaymentSetting(models.Model):
    class Meta:
        verbose_name = 'business payment settings'
        verbose_name_plural = 'business payment settings'
        db_table = 'business_payment_settings'

    id = models.BigAutoField(primary_key=True)
    incust_account_id = models.CharField(max_length=36, null=False, blank=False, unique=True)
    
    # Deprecated fields - залишаємо для міграції даних
    payment_method = models.CharField(max_length=29, null=True, blank=True)
    json_data = models.JSONField(null=True, default=None)
    # Deprecated fields - залишаємо для міграції даних

    is_enabled = models.BooleanField(default=True, verbose_name="Включено")
    name = models.CharField(max_length=255, verbose_name="Имя", null=True, default=None)
    description = models.CharField(max_length=255, verbose_name="Описание", null=True, default=None)
    creator = models.ForeignKey("TelegramUser", on_delete=models.CASCADE)

    is_deleted = models.BooleanField(default=False, verbose_name="Удалено")

    time_created = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now=True)
