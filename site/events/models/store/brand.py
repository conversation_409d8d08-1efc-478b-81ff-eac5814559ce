from django.db import models

from events.models import Group


class Brand(models.Model):
    class Meta:
        db_table = "brands"
        verbose_name = "Магазин, Бренд"
        verbose_name_plural = "Магазин, Бренды"

    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255, verbose_name="Имя")

    logo_media = models.ForeignKey(
        "MediaObject", null=True, blank=True,
        on_delete=models.SET_NULL,
        related_name="brand_logos",
        related_query_name="brand_logos",
    )
    image_media = models.ForeignKey(
        "MediaObject", null=True, blank=True,
        on_delete=models.SET_NULL,
        related_name="brand_images",
        related_query_name="brand_images",
    )

    get_order_username = models.CharField(
        max_length=255,
        blank=True, null=True, default=None,
        verbose_name="Логин GetOrder"
    )
    get_order_password = models.Char<PERSON>ield(
        max_length=255,
        blank=True, null=True, default=None,
        verbose_name="Пароль GetOrder"
    )
    is_get_order = models.BooleanField(default=False, verbose_name="Аккаунт GetOrder")

    sheets = models.CharField(
        max_length=255,
        blank=True, null=True, default=None,
        verbose_name="ID google sheets"
    )
    is_sheets_update = models.BooleanField(
        default=True, verbose_name="Обновление google sheets"
    )

    group = models.ForeignKey(
        Group, on_delete=models.CASCADE,
        related_name="store_group", related_query_name="store_group",
        verbose_name="Группа"
    )

    domain = models.CharField(
        max_length=255,
        blank=True, null=True, default=None,
        verbose_name="Домен для сайта"
    )

    description_media = models.ForeignKey(
        "MediaObject", null=True, blank=True,
        on_delete=models.SET_NULL,
        related_name="brand_descriptions",
        related_query_name="brand_descriptions",
    )
    offer_media = models.ForeignKey(
        "MediaObject", null=True, blank=True,
        on_delete=models.SET_NULL,
        related_name="brand_offers",
        related_query_name="brand_offers",
    )

    is_poster_update = models.BooleanField(
        default=True, verbose_name="Обновление poster"
    )

    is_filter_by_price = models.BooleanField(
        default=True, verbose_name="Фильтр по цене"
    )
    is_filter_sort = models.BooleanField(default=True, verbose_name="Сортировка")
    is_filter_search = models.BooleanField(default=True, verbose_name="Поиск")

    image_height = models.PositiveSmallIntegerField(null=True, blank=True)
    product_image_aspect_ratio = models.CharField(max_length=20, null=True, blank=True)

    desktop_view = models.CharField(max_length=12, default="default_grid")
    mobile_view = models.CharField(max_length=12, default="default_grid")

    thumbnails_mode = models.CharField(max_length=9, default="SEVEN_LOC")
    thumbnail_size = models.PositiveSmallIntegerField(default=512)

    mobile_cart_button_mode = models.CharField(max_length=6, default="BOTTOM")

    web_email_mode = models.CharField(
        max_length=8, default="REQUIRED"
    )
    web_phone_mode = models.CharField(
        max_length=8, default="OPTIONAL"
    )

    messanger_email_mode = models.CharField(
        max_length=8, default="OPTIONAL"
    )
    messanger_phone_mode = models.CharField(
        max_length=8, default="OPTIONAL"
    )
    order_comment_mode = models.CharField(
        max_length=8, default="OPTIONAL"
    )

    web_order_name_mode = models.CharField(
        max_length=14, default="FIRST_AND_LAST"
    )

    messanger_order_name_mode = models.CharField(
        max_length=14, default="FIRST_AND_LAST"
    )

    _footer_sign = models.CharField(max_length=256, null=True, blank=True)
    _terms_of_use_link = models.CharField(max_length=256, null=True, blank=True)
    _privacy_policy_link = models.CharField(max_length=256, null=True, blank=True)

    additional_head_tags = models.TextField(null=True, blank=True)
    additional_body_tags = models.TextField(null=True, blank=True)

    is_categories_count_view = models.BooleanField(
        default=True, verbose_name="Показывать кол-во позиций в категории"
    )

    google_maps_api_key_backend = models.CharField(
        max_length=256, null=True, blank=True
    )
    google_maps_api_key_frontend = models.CharField(
        max_length=256, null=True, blank=True
    )
    google_maps_7loc_keys_enabled = models.BooleanField(default=True)

    is_ai_enabled = models.BooleanField(
        default=False, verbose_name="Включить искусственный интеллект"
    )

    show_more_infinite = models.BooleanField(
        default=False, verbose_name="Бесконечная подгрузка"
    )
    products_limit = models.IntegerField(default=20, verbose_name="Лимит товаров")

    task = models.ForeignKey(
        "Task",
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="brand_task",
        related_query_name="brand_task"
    )

    consent_mode = models.CharField(
        max_length=9, default="PER_ORDER"
    )

    analytics_data = models.JSONField(
        null=True,
        blank=True,
        verbose_name="Analytics Data",
    )
