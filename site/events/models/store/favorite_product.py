from django.db import models

from .favorites import StoreFavorite
from .product import StoreProduct


class StoreFavoritesProduct(models.Model):
    class Meta:
        db_table = "store_favorites_products"
        verbose_name = "Магазин, продукт избранного"
        verbose_name_plural = "Магазин, продукты избранного"

    id = models.BigAutoField(primary_key=True)

    product = models.ForeignKey(
        StoreProduct, blank=True, null=True,
        on_delete=models.CASCADE, verbose_name="Продукт"
    )
    favorite = models.ForeignKey(
        StoreFavorite, blank=True, null=True,
        on_delete=models.CASCADE, verbose_name="Избранное"
    )
