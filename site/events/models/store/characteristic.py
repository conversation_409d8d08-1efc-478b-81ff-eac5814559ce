from django.db import models


class StoreCharacteristic(models.Model):
    class Meta:
        db_table = "store_characteristics"
        verbose_name = "Характеристики"
        verbose_name_plural = "Характеристика"

    id = models.BigAutoField(primary_key=True)
    external_id = models.CharField(max_length=255, blank=True, null=True)
    external_type = models.CharField(max_length=9, blank=True, null=True)
    is_deleted = models.BooleanField(default=False)

    brand = models.ForeignKey("Brand", on_delete=models.CASCADE, verbose_name="бренд")

    name = models.Char<PERSON>ield(max_length=255, verbose_name="ім`я")
    filter_type = models.Char<PERSON><PERSON>(max_length=2, verbose_name="тип фільтру")
    is_hide = models.BooleanField(default=False)

    position = models.PositiveSmallIntegerField(null=True, blank=True)
    excel_row_number = models.PositiveSmallIntegerField(null=True, blank=True)


class StoreCharacteristicValue(models.Model):
    class Meta:
        db_table = "store_characteristic_values"
        verbose_name = "Значення характеристик"
        verbose_name_plural = "Значення характеристики"
        unique_together = (
            "characteristic", "product"
        )

    characteristic = models.ForeignKey(
        StoreCharacteristic, on_delete=models.CASCADE, verbose_name="характеристика"
    )
    product = models.ForeignKey(
        "StoreProduct", on_delete=models.CASCADE, verbose_name="товар"
    )

    value = models.CharField(
        max_length=100, null=True, blank=True, verbose_name="значення"
    )


class StoreCharacteristicFiltersSet(models.Model):
    class Meta:
        db_table = "store_characteristic_filters_sets"

    id = models.BigAutoField(primary_key=True)
    min_price = models.FloatField(null=True, blank=True)
    max_price = models.FloatField(null=True, blank=True)


class StoreCharacteristicFilter(models.Model):
    class Meta:
        db_table = "store_characteristic_filters"

    id = models.BigAutoField(primary_key=True)

    set = models.ForeignKey(StoreCharacteristicFiltersSet, on_delete=models.CASCADE)
    characteristic = models.ForeignKey(StoreCharacteristic, on_delete=models.CASCADE)
    filter_type = models.CharField(
        max_length=2, default="v", verbose_name="тип фільтру"
    )

    value = models.CharField(max_length=100, null=True, blank=True)
    values_list = models.JSONField(null=True, blank=True)

    range_min = models.FloatField(null=True, blank=True)
    range_max = models.FloatField(null=True, blank=True)


class StoreCharacteristicFilterSetting(models.Model):
    class Meta:
        db_table = "store_characteristic_filter_settings"

    id = models.BigAutoField(primary_key=True)
    brand = models.ForeignKey("Brand", on_delete=models.CASCADE)
    store = models.ForeignKey("Store", on_delete=models.CASCADE, null=True)
    characteristic = models.ForeignKey(StoreCharacteristic, on_delete=models.CASCADE)
