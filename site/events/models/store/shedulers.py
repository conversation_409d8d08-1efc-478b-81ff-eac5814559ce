from django.db import models

from timezone_field import TimeZoneField

from .. import TelegramUser


class DayOfWeek(models.TextChoices):
    MONDAY = 'Monday'
    TUESDAY = 'Tuesday'
    WEDNESDAY = 'Wednesday'
    THURSDAY = 'Thursday'
    FRIDAY = 'Friday'
    SATURDAY = 'Saturday'
    SUNDAY = 'Sunday'


class StoreScheduler(models.Model):

    class Meta:
        db_table = 'store_schedulers'
        verbose_name = 'Schedule settings'
        verbose_name_plural = 'Schedule settings'

    brand = models.ForeignKey('Brand', on_delete=models.SET_NULL, null=True, verbose_name="Бренд")

    type_data = models.CharField(max_length=50, default='poster')
    import_source = models.CharField(max_length=50, default='poster')
    day_of_week = models.CharField(max_length=9, choices=DayOfWeek.choices, default=DayOfWeek.MONDAY)
    time_value = models.TimeField(null=False)
    time_zone = TimeZoneField(verbose_name="Profile user time zone", null=True, blank=True)

    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(null=True, blank=True, default=None)

    status = models.CharField(max_length=50, null=True, blank=True, default=None)

    user = models.ForeignKey(
        TelegramUser, blank=True, null=True, on_delete=models.CASCADE, verbose_name="User"
    )
    json_data = models.JSONField(default=None, null=True, blank=True)
