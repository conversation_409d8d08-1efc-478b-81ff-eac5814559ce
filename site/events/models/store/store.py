from django.db import models

from .brand import Brand


class Store(models.Model):
    class Meta:
        db_table = "stores"
        verbose_name = "Магазин"
        verbose_name_plural = "Магазины"

    id = models.BigAutoField(primary_key=True)

    is_enabled = models.BooleanField(default=True)

    name = models.CharField(max_length=255, null=True, blank=True, verbose_name="ім`я")
    description = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Описание"
    )
    ai_description = models.CharField(max_length=4096, blank=True, null=True)

    image_path = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Путь к изображению"
    )

    media = models.ForeignKey(
        "MediaObject", null=True, blank=True, on_delete=models.SET_NULL
    )

    position = models.PositiveSmallIntegerField(null=True, blank=True)
    excel_row_number = models.PositiveSmallIntegerField(null=True, blank=True)

    get_order_id = models.IntegerField(
        blank=True, null=True, verbose_name="ID GetOrder"
    )

    location_get_order_id = models.IntegerField(
        blank=True, null=True, verbose_name="location ID GetOrder"
    )
    location_get_order_unique_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="location UniqueID GetOrder"
    )

    latitude = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Координаты, широта"
    )
    longitude = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Координаты, длина"
    )

    organisation_id = models.IntegerField(
        blank=True, null=True, verbose_name="ID организации GetOrder"
    )
    is_deleted = models.BooleanField(default=False, verbose_name="Удалён")

    _currency = models.CharField(max_length=255, default="UAH", verbose_name="Валюта")

    brand = models.ForeignKey(
        Brand, blank=True, null=True, on_delete=models.CASCADE, verbose_name="Бренд"
    )

    city = models.CharField(max_length=255, null=True, blank=True, verbose_name="місто")

    external_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="External ID"
    )
    external_type = models.CharField(
        max_length=99, blank=True, null=True, verbose_name="External ID source"
    )

    polygon = models.JSONField(
        null=True, blank=True, verbose_name="Область доставки магазина"
    )
    distance = models.IntegerField(default=0, verbose_name="Радиус вокруг магазина")
    is_distance = models.BooleanField(
        default=False,
        verbose_name="Использовать радиус для определения области доставки"
    )
    is_polygon = models.BooleanField(
        default=False,
        verbose_name="Использовать полигон для определения области доставки"
    )
    is_swap_coordinates = models.BooleanField(
        default=True, verbose_name="Поменять местами координаты широты и долготы"
    )

    company = models.CharField(
        max_length=255,
        blank=True, null=True, default=None,
        verbose_name="Название компании"
    )
    company_url = models.CharField(
        max_length=255,
        blank=True, null=True, default=None,
        verbose_name="Ссылка на сайт компании"
    )
    data_url = models.CharField(
        max_length=1024,
        blank=True, null=True, default=None,
        verbose_name="Ссылка на данные экспорта"
    )

    description_media = models.ForeignKey(
        "MediaObject", null=True, blank=True,
        on_delete=models.SET_NULL,
        related_name="store_descriptions",
        related_query_name="store_descriptions",
    )
    offer_media = models.ForeignKey(
        "MediaObject", null=True, blank=True,
        on_delete=models.SET_NULL,
        related_name="store_offers",
        related_query_name="store_offers",
    )

    banners = models.JSONField(blank=True, null=True, verbose_name="Баннеры")

    tg_payment_token = models.CharField(
        max_length=256, blank=True, null=True, verbose_name="Платежный токен Telegram"
    )

    is_enabled_emenu = models.BooleanField(default=False, verbose_name="Включен eMenu")

    incust_terminal_api_key = models.CharField(
        max_length=512, blank=True, null=True, verbose_name="API ключ терминала InCust"
    )

    incust_terminal_id = models.CharField(
        max_length=512, blank=True, null=True, verbose_name="ID терминала InCust"
    )

    task = models.ForeignKey(
        "Task",
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="store_task",
        related_query_name="store_task"
    )
