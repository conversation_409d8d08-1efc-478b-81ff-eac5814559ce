from django.db import models
from django.utils.timezone import now
from .store_order import StoreOrder


class OrderShippingStatus(models.Model):

    class Meta:
        db_table = "order_shipping_statuses"

    status = models.CharField(max_length=20)
    store_order = models.ForeignKey(StoreOrder, on_delete=models.CASCADE, related_name='shipping_statuses')
    time_created = models.DateTimeField(default=now)
    comment = models.CharField(max_length=255, null=True, blank=True, default=None)
    source = models.CharField(max_length=39, null=True, blank=True, default=None)

    initiated_by = models.TextField(max_length=8, default="unknown")
    initiated_by_user = models.ForeignKey("TelegramUser", on_delete=models.SET_NULL, null=True, blank=True)
