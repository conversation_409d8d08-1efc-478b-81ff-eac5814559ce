from django.db import models


class OrderShipment(models.Model):
    class Meta:
        db_table = "order_shipments"
        verbose_name = "Кастомный метод доставки (Заказ)"
        verbose_name_plural = "Кастомные методы доставки (Заказ)"

    id = models.BigAutoField(primary_key=True)
    base_type = models.CharField(max_length=11)
    name = models.CharField(max_length=255, verbose_name="Название")
    _price = models.BigIntegerField(default=0, verbose_name="Цена")
    comment = models.TextField(
        null=True, blank=True, default=None,
        verbose_name="Комментарий к кастомному методу доставки"
    )
    is_paid_separately = models.BooleanField(default=False, verbose_name="Оплачивается отдельно")

    store_order = models.ForeignKey(
        "StoreOrder",
        related_name="shipment",
        on_delete=models.CASCADE,
        default=None,
        verbose_name="Связь с ордером"
    )
    settings = models.ForeignKey(
        "BrandCustomSettings",
        on_delete=models.RESTRICT,
    )

    label_comment = models.TextField(null=True, blank=True, default=None, verbose_name="Label комментария")

    delivery_datetime_mode = models.CharField(
        max_length=50, verbose_name="Режим выбора времени доставки",
        null=False, blank=False, default="datetime"
    )
