from django.db import models

from .store import Brand, Store
from .. import TelegramUser


class AdminEmail(models.Model):
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE, null=False)
    store = models.ForeignKey(Store, on_delete=models.CASCADE, null=True, default=None)
    type_email = models.CharField(max_length=9, null=False)
    email = models.CharField(max_length=320, null=False)
    time_created = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now=True)
    creator = models.ForeignKey(TelegramUser, on_delete=models.CASCADE, null=True, default=None)
    is_deleted = models.BooleanField(default=False, null=True, blank=True)

    class Meta:
        db_table = "admin_emails"
        verbose_name = "admin emails"
        verbose_name_plural = "admin emails"
        unique_together = (('brand', 'store', 'email'),)
