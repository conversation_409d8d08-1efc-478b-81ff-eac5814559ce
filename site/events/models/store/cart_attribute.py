from django.db import models

from .attribute import StoreAttribute


class StoreCartAttribute(models.Model):
    class Meta:
        db_table = "store_cart_attributes"
        verbose_name = "Магаз<PERSON><PERSON>, аттрибут продуктов корзины"
        verbose_name_plural = "Магазин, аттрибуты продуктов корзины"

    id = models.BigAutoField(primary_key=True)

    quantity = models.IntegerField(verbose_name="Количество")

    cart_product = models.ForeignKey("StoreCartProduct", on_delete=models.CASCADE, blank=True, null=True)

    attribute = models.ForeignKey(
        StoreAttribute, blank=True, null=True,
        on_delete=models.CASCADE, verbose_name="Аттрибут продукта"
    )
