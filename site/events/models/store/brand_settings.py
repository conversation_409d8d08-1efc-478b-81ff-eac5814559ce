from django.db import models
from .brand import Brand

class BrandSettings(models.Model):
    id = models.BigAutoField(primary_key=True)
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE)
    type_data = models.Char<PERSON>ield(max_length=50)
    json_data = models.JSONField(null=True, default=None)
    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now=True)
    value_data = models.CharField(max_length=255, null=True, default=None)
    name_data = models.CharField(max_length=99, null=True, default=None)    

    class Meta:
        db_table = "brand_settings"
        verbose_name = "Магазин, настройки бренда"
        verbose_name_plural = "Магазин, настройки бренда"        
        constraints = [
            models.UniqueConstraint(fields=['brand', 'type_data'], name='unique_brand_type_data')
        ]
