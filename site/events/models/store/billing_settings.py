from django.db import models

class BillingSettings(models.Model):
    class Meta:
        db_table = "billing_settings"
        verbose_name = "Настройки платежного адреса"
        verbose_name_plural = "Настройка платежного адреса"

    id = models.BigAutoField(primary_key=True)
    brand = models.ForeignKey("Brand", on_delete=models.CASCADE, null=True)
    store = models.ForeignKey("Store", on_delete=models.CASCADE, null=True)

    is_enable = models.BooleanField(default=False, verbose_name="Включен")
    is_require = models.BooleanField(default=False, verbose_name="Обязательность")
    need_person = models.BooleanField(default=False, verbose_name="Для физ лиц")
    need_organisation = models.BooleanField(default=False, verbose_name="Для юр лиц")
