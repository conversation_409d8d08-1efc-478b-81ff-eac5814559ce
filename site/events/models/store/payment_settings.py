from django.db import models


class PaymentSettings(models.Model):
    class Meta:
        db_table = 'payment_settings'
        verbose_name = 'payment settings'
        verbose_name_plural = 'payment settings'

    id = models.BigAutoField(primary_key=True)
    brand = models.ForeignKey('Brand', on_delete=models.SET_NULL, null=True)
    store = models.ForeignKey('Store', on_delete=models.SET_NULL, null=True)
    payment_method = models.CharField(max_length=50)
    json_data = models.JSONField(null=True, default=None)

    is_enabled = models.BooleanField(default=True, verbose_name="Включено")
    name = models.CharField(max_length=255, verbose_name="Имя", null=True, default=None)
    description = models.CharField(
        max_length=255, verbose_name="Описание", null=True, default=None
    )
    with_comment = models.CharField(
        max_length=8, default="disabled", verbose_name="С комментарием"
    )
    label_comment = models.TextField(
        null=True, blank=True, default=None, verbose_name="Label комментария"
    )
    is_online = models.BooleanField(default=None, null=True, verbose_name="Онлайн")

    post_payment_info = models.TextField(
        null=True, blank=True, default=None, verbose_name="Label інфо"
    )

    media = models.ForeignKey(
        "MediaObject", null=True, blank=True, on_delete=models.SET_NULL
    )

    is_deleted = models.BooleanField(default=False, verbose_name="Удалено")
    position = models.PositiveSmallIntegerField(
        null=True, default=None
    )  # TODO: payments, remove nullable

    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now_add=True)


class StoreOrderPayment(models.Model):
    class Meta:
        db_table = 'store_order_payments'
        verbose_name = 'store order payments'
        verbose_name_plural = 'store order payments'

    id = models.BigAutoField(primary_key=True)
    json_data = models.JSONField()
    payment_method = models.CharField(max_length=50)
    name = models.CharField(max_length=255, verbose_name="Имя", null=True, default=None)
    incust_account_name = models.CharField(
        max_length=255, verbose_name="Имя счета", null=True, default=None
    )
    incust_card_name = models.CharField(
        max_length=255, verbose_name="Имя карты", null=True, default=None
    )
    incust_account_id = models.CharField(
        max_length=255, verbose_name="Айди счета", null=True, default=None
    )
    incust_card_id = models.CharField(
        max_length=255, verbose_name="Айди карты", null=True, default=None
    )
    description = models.CharField(
        max_length=255, verbose_name="Описание", null=True, default=None
    )
    comment = models.CharField(
        max_length=255, verbose_name="Комментарий", null=True, default=None
    )
    label_comment = models.TextField(
        null=True, blank=True, default=None, verbose_name="Label комментария"
    )
    post_payment_info = models.TextField(
        null=True, blank=True, default=None, verbose_name="Label інфо"
    )

    _price = models.BigIntegerField(default=0, verbose_name="Цена")

    payment_settings = models.ForeignKey(
        PaymentSettings, blank=True, null=True, on_delete=models.CASCADE,
        verbose_name="Настройки платежа"
    )

    order = models.ForeignKey(
        "StoreOrder",
        related_name="store_order_payments_order",
        on_delete=models.CASCADE,
        default=None,
        verbose_name="Связь с ордером",
        null=True,
    )

    invoice = models.ForeignKey(
        "Invoice",
        related_name="store_order_payments_order",
        on_delete=models.CASCADE,
        default=None,
        verbose_name="Связь с инвойсом",
        null=True,
    )

    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now_add=True)
    confirmed_datetime = models.DateTimeField(
        null=True, blank=True, verbose_name="дата подтверждения"
    )

    business_payment_setting = models.ForeignKey(
        "BusinessPaymentSetting", blank=True, null=True, on_delete=models.CASCADE,
        verbose_name="Настройки бизнес платежа"
    )

    business_payment_merchant_data = models.JSONField(
        blank=True, null=True, verbose_name='данные мерчанта'
    )


class PaymentSettingsMerchantData(models.Model):
    class Meta:
        db_table = 'payment_settings_merchant_data'
        verbose_name = 'payment settings merchan data'
        verbose_name_plural = 'payment settings merchan data'
    
    id = models.BigAutoField(primary_key=True, serialize=False)

    business_payment_data = models.ForeignKey(
        "BusinessPaymentData", 
        blank=True, 
        null=True, 
        on_delete=models.CASCADE,
        verbose_name="Звʼязок з даними бізнес-оплати"
    )
    
    json_data = models.JSONField(blank=True, null=True, verbose_name='дані мерчанта')
    
    is_enabled = models.BooleanField(default=True, verbose_name="Увімкнено")
    is_deleted = models.BooleanField(default=False, verbose_name="Видалено")
    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now=True)


class ObjectPaymentSettings(models.Model):
    class Meta:
        db_table = 'object_payment_settings'
        verbose_name = 'object payment settings'
        verbose_name_plural = 'object payment settings'
        unique_together = ('target', 'payment_settings', 'store', 'invoice_template')

    id = models.BigAutoField(primary_key=True)

    target = models.CharField(default="STORE", max_length=99)
    store = models.ForeignKey(
        "Store", on_delete=models.CASCADE, verbose_name="Магазин", default=None,
        null=True
    )
    invoice_template = models.ForeignKey(
        "InvoiceTemplate", on_delete=models.CASCADE, verbose_name="Шаблон",
        default=None, null=True
    )

    json_data = models.JSONField()
    is_enabled = models.BooleanField(default=True, verbose_name="Включено")
    post_payment_info = models.TextField(
        null=True, blank=True, default=None, verbose_name="Label інфо"
    )

    payment_settings = models.ForeignKey(
        PaymentSettings, blank=True, null=True, on_delete=models.CASCADE,
        verbose_name="Настройки платежа"
    )
    is_deleted = models.BooleanField(default=False, verbose_name="Удалено")

    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now_add=True)
