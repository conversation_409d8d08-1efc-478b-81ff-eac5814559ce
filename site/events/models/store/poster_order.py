from django.db import models

from .brand import Brand


class ExternalOrderRef(models.Model):

    order_id = models.BigIntegerField(null=False)
    external_id = models.BigIntegerField(null=False)
    time_created = models.DateTimeField(auto_now_add=True)
    external_type = models.CharField(
        max_length=99, blank=True, null=True, verbose_name="External ref source"
    )
    brand = models.ForeignKey(
        Brand, on_delete=models.CASCADE, verbose_name="Бренд"
    )

    class Meta:
        db_table = "external_order_refs"
        verbose_name = "Транзакції замовлень POSTER"
        verbose_name_plural = "Транзакції замовлень POSTER"
        constraints = [
            models.UniqueConstraint(
                fields=["brand_id", "order_id"],
                name="uk_exor_brand_id_order_id",
            )
        ]
