from django.db import models

from .brand import Group


class AuthSetting(models.Model):
    class Meta:
        db_table = "auth_settings"
        verbose_name = "Настройки авторизации"
        verbose_name_plural = "Настройки авторизации"

    id = models.BigAutoField(primary_key=True)
    group = models.ForeignKey(
        Group, blank=True, null=True, on_delete=models.CASCADE, verbose_name="Профіль"
    )
    is_auth_email_enabled = models.BooleanField(default=True)
    is_auth_messanger_enabled = models.BooleanField(default=True)
    is_auth_google_enabled = models.BooleanField(default=True)
    is_auth_apple_enabled = models.BooleanField(default=True)
    is_auth_for_orders_enabled = models.BooleanField(default=False)
