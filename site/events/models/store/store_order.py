from django.db import models
from timezone_field import TimeZoneField

from pay4say.settings import TIME_ZONE
from .cart import StoreCart
from .store import Store
from .. import TelegramUser


class StoreOrder(models.Model):
    class Meta:
        db_table = "store_orders"
        verbose_name = "Маг<PERSON><PERSON>ин, ордер"
        verbose_name_plural = "Магазин, ордера"

    id: int = models.BigAutoField(primary_key=True)
    brand_name = models.CharField(
        max_length=1024, blank=True, null=True,
        verbose_name="Имя бренда на момент заказа"
    )

    before_loyalty_sum: int = models.BigIntegerField(
        default=0, verbose_name="total sum, tips excluded, before loyalty"
    )
    tips_sum: int = models.IntegerField(default=0, verbose_name="tips")
    total_sum: int = models.BigIntegerField(
        verbose_name="total sum with loyalty, tips excluded", default=0, )
    # sum_before_extra_fee = models.IntegerField(
    #     verbose_name="total sum, tips included before extra fee", default=0,
    # )
    total_sum_with_extra_fee: int = models.BigIntegerField(
        verbose_name="total sum after extra fee", default=0,
    )
    sum_to_pay: int = models.BigIntegerField(
        verbose_name="total sum, tips & extra_fee included", default=0
    )
    payer_fee: int = models.IntegerField(
        verbose_name="payer fee from sum_to_pay", default=0
    )
    paid_sum: int = models.BigIntegerField(
        verbose_name="paid_sum (sum_to_pay  + payer_fee)", default=0
    )

    order_products = models.ForeignKey(
        "OrderProduct",
        related_name="order_products_store_order",
        on_delete=models.CASCADE,
        default=None,
        null=True,
        verbose_name="Связь с продуктами"
    )

    user = models.ForeignKey(
        TelegramUser, blank=True, null=True,
        on_delete=models.CASCADE,
        verbose_name="Пользователь"
    )
    store = models.ForeignKey(
        Store, on_delete=models.CASCADE, verbose_name="Точка продажи"
    )
    menu_in_store = models.ForeignKey(
        "MenuInStore", on_delete=models.RESTRICT,
        null=True, blank=True,
    )

    _status = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="Статус"
    )
    _status_pay = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="Статус оплати"
    )

    invoice = models.ForeignKey(
        "Invoice",
        on_delete=models.CASCADE,
        default=None,
        null=True,
        verbose_name="Связь с инвойсом",
    )

    delivery_address = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Адрес доставки"
    )
    delivery_method = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Метод доставки"
    )
    payment_method = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Метод оплаты"
    )
    first_name = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Имя"
    )
    last_name = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Фамилия"
    )
    phone = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Номер телефона"
    )
    email = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Email"
    )

    create_date = models.DateTimeField(
        null=True, blank=True, verbose_name="Дата создания"
    )

    address_comment = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Комментарий к адресу"
    )
    desired_delivery_date = models.DateTimeField(
        null=True, blank=True, verbose_name="Желаемая дата доставки"
    )
    desired_delivery_time = models.CharField(
        max_length=10, blank=True, null=True,
        verbose_name="Желаемое время доставки"
    )

    address_street = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="Улица"
    )
    address_house = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="Дом"
    )
    address_flat = models.CharField(
        max_length=100, null=True, blank=True, verbose_name="Квартира"
    )
    address_floor = models.IntegerField(
        blank=True, null=True, verbose_name="Этаж"
    )
    address_entrance = models.IntegerField(
        blank=True, null=True, verbose_name="Подъезд"
    )

    comment = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Комментарий к заказу"
    )

    get_order_id = models.BigIntegerField(
        blank=True, null=True, verbose_name="ID заказа в GetOrder"
    )

    incust_loyalty_check = models.JSONField(
        null=True, default=None,
        verbose_name="Чек лояльности incust, может быть изменен"
    )
    original_incust_loyalty_check = models.JSONField(
        null=True, default=None, verbose_name="Оригинальный чек лояльности incust"
    )
    bonuses_redeemed = models.BigIntegerField(
        default=0, verbose_name="Кол-во списанных бонусов по лояльности"
    )
    discount = models.BigIntegerField(
        default=0, verbose_name="Примененная скидка по лояльности"
    )
    discount_and_bonuses = models.BigIntegerField(
        default=0, verbose_name="cумма скидки вместе со списанными бонусами"
    )
    is_ewallet_discount = models.BooleanField(default=False)
    loyalty_type = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="Тип лояльности"
    )
    incust_export = models.JSONField(
        null=True, default=None, verbose_name="Данные по экспорту чека в инкаст"
    )
    token = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Токен к заказу"
    )
    currency = models.CharField(max_length=3, verbose_name="Валюта")

    custom_payment = models.ForeignKey(
        "OrderCustomPayment", on_delete=models.CASCADE, default=None, null=True,
        verbose_name="Связь с кастомним платежем",
    )

    store_custom_payment = models.ForeignKey(
        "BrandCustomSettings", on_delete=models.SET_NULL, default=None, null=True,
        related_name="store_custom_payment",
        verbose_name="Связь с словарем кастомних платежей"
    )

    friend = models.ForeignKey(
        "TelegramUser",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="store_orders_as_friend"
    )
    date_sent_to_friend = models.DateTimeField(blank=True, default=None, null=True)

    address_lat = models.CharField(
        max_length=30, blank=True, null=True, verbose_name="Широта адреса пользователя"
    )
    address_lng = models.CharField(
        max_length=30, blank=True, null=True, verbose_name="Долгота адреса пользователя"
    )
    address_place_id = models.CharField(
        max_length=1024, blank=True, null=True, verbose_name="Google id"
    )

    type = models.CharField(
        max_length=255, default="regular", verbose_name="Тип заказа"
    )

    order_payment = models.ForeignKey(
        "StoreOrderPayment", on_delete=models.SET_NULL, default=None, null=True,
        verbose_name="Связь с платежом"
    )

    auto_cancelled_loyalty_transaction = models.BooleanField(default=False)
    timezone = TimeZoneField(default=TIME_ZONE, verbose_name="Временная зона")

    cart = models.ForeignKey(
        StoreCart, blank=True, null=True, default=None,
        on_delete=models.CASCADE,
        verbose_name="Корзина"
    )

    utm_labels = models.JSONField(
        null=True,
        blank=True,
        verbose_name="Analytics Data",
    )
