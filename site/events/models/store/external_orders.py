from django.db import models
from .store_order import StoreOrder

class ExternalOrder(models.Model):
    class Meta:
        db_table = 'external_orders'
        verbose_name = 'external orders'
        verbose_name_plural = 'external orders'

    brand = models.ForeignKey('Brand', on_delete=models.CASCADE, null=True)  
    order = models.ForeignKey('StoreOrder', on_delete=models.CASCADE)
    external_order_id = models.CharField(max_length=36, null=True)
    external_type = models.CharField(max_length=50)
    json_data = models.JSONField(null=True)
    status = models.Char<PERSON>ield(max_length=50, null=True)
    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'ExternalOrder {self.id}'
