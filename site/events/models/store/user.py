from django.db import models


class ConfirmEmailRequest(models.Model):
    class Meta:
        db_table = "confirm_email_requests"
        verbose_name = "Магазин, Реквест"
        verbose_name_plural = "Магазин, Реквесты"
        unique_together = ("email", "purpose")

    id = models.BigAutoField(primary_key=True)
    email = models.CharField(max_length=320, verbose_name="Почта")
    is_confirmed = models.BooleanField(default=False, verbose_name="Подтверждённая")
    purpose = models.CharField(max_length=16, verbose_name="Причина")
    datetime_sent = models.DateTimeField(blank=True, null=True, verbose_name="Подтверждённая почта")
    datetime_confirmed = models.DateTimeField(blank=True, null=True, verbose_name="Подтверждённая почта")

