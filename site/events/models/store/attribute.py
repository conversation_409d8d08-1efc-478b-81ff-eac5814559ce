from django.db import models

from .attribute_group import StoreAttributeGroup
from .brand import Brand


class StoreAttribute(models.Model):
    class Meta:
        db_table = "store_attributes"
        verbose_name = "Магазин, атрибут"
        verbose_name_plural = "Магазин, атрибуты"

    id = models.BigAutoField(primary_key=True)

    attribute_id = models.CharField(max_length=255, verbose_name="Идентификатор")

    name = models.CharField(max_length=255, verbose_name="Имя")
    min = models.IntegerField(blank=True, null=True, verbose_name="Минимум атрибутов")
    max = models.IntegerField(blank=True, null=True, verbose_name="Максимум атрибутов")
    selected_by_default = models.BooleanField(default=False, verbose_name="Выбран по умолчанию")
    price_impact = models.BigIntegerField(verbose_name="Добавочная стоимость")
    is_available = models.BooleanField(default=True, verbose_name="Отображение")
    is_deleted = models.BooleanField(default=False, verbose_name="Удалён")
    attribute_group = models.ForeignKey(StoreAttributeGroup, on_delete=models.CASCADE, verbose_name="Группа атрибутов")
    brand = models.ForeignKey(
        Brand, blank=True, null=True, on_delete=models.CASCADE, verbose_name="Бренд"
    )
    external_id = models.CharField(max_length=255, blank=True, null=True, verbose_name="External ID")
    external_type = models.CharField(max_length=99, blank=True, null=True, verbose_name="External ID source")
