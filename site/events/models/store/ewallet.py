from django.db import models

from events.models.default_funcs import make_uuid


class EWallet(models.Model):
    class Meta:
        verbose_name = '7Loc eWallet'
        verbose_name_plural = '7Loc eWallet'
        db_table = 'ewallets'

    id = models.BigAutoField(primary_key=True)
    incust_account_id = models.CharField(
        max_length=36, null=False, blank=False, unique=True
    )
    terminal_api_key = models.CharField(
        max_length=255, null=False, blank=False,
    )
    server_api_url = models.CharField(
        max_length=255, null=False, blank=False,
    )

    name = models.Char<PERSON>ield(max_length=255, verbose_name="Імя", null=True, default=None)
    description = models.Char<PERSON>ield(
        max_length=255, verbose_name="Опис", null=True, default=None
    )

    info = models.CharField(
        max_length=1024, verbose_name="Інформація", null=True, default=None
    )

    creator = models.ForeignKey("TelegramUser", on_delete=models.CASCADE)

    bot = models.ForeignKey(
        "ClientBot", related_name="ewallets",
        on_delete=models.CASCADE, verbose_name="бот, у якому буде оброблятись оплата",
        null=False, blank=False
    )

    ad = models.ForeignKey("Ad", on_delete=models.SET_NULL, null=True, blank=True)

    is_enabled = models.BooleanField(default=True, verbose_name="Дозволений")
    is_deleted = models.BooleanField(default=False, verbose_name="Видалений")

    time_created = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now_add=True)

    _countries = models.JSONField(
        null=False, blank=False, verbose_name="list of available countries"
    )

    # Мова для перекладів
    lang = models.CharField(
        max_length=5, verbose_name="Language", null=False, default="en"
    )
    _langs_list = models.JSONField(
        verbose_name="List of languages", null=True, default=None
    )

    ext_payment_fee_value = models.DecimalField(
        decimal_places=2,
        max_digits=12,
        null=True,
        blank=True,
    )
    ext_payment_fee_percent = models.DecimalField(
        decimal_places=2,
        max_digits=4,
        null=True,
        blank=True,
    )

    @property
    def langs_list(self):
        if not isinstance(self._langs_list, list):
            return []

        return [lang for lang in self._langs_list if lang != self.lang]

    @langs_list.setter
    def langs_list(self, value):
        self._langs_list = value

    currency = models.CharField(
        max_length=3, verbose_name="CURRENCY", null=False, default="USD"
    )

    invoice_template = models.ForeignKey(
        "InvoiceTemplate", null=True, blank=True, on_delete=models.RESTRICT
    )

    media = models.ForeignKey(
        "MediaObject", null=True, blank=True, on_delete=models.SET_NULL
    )

    is_private = models.BooleanField(
        default=False, verbose_name="Приватний (тільки для визначених користувачів)"
    )
    uuid_id = models.CharField(
        max_length=32,
        editable=False,
        default=make_uuid,
        null=False,
        unique=True,
    )

    incust_terminal_id = models.CharField(max_length=255, null=True, blank=True)

    min_amount = models.BigIntegerField(null=True, blank=True)

    discount_percent = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=0,
    )


class EWalletPayment(models.Model):
    class Meta:
        verbose_name = 'eWallet 7Loc payments'
        verbose_name_plural = 'eWallet 7Loc payments'
        db_table = 'ewallet_payments'

    id = models.BigAutoField(primary_key=True)
    uuid_id = models.CharField(
        max_length=36,
        editable=False,
        unique=True,
    )

    ewallet = models.ForeignKey(
        "EWallet", on_delete=models.CASCADE, null=False, blank=False
    )

    profile = models.ForeignKey(
        "Group", on_delete=models.CASCADE, null=False, blank=False
    )

    external_id = models.CharField(
        max_length=99,
    )

    description = models.CharField(
        max_length=255,
        null=True, blank=True, default=None
    )

    status = models.CharField(
        max_length=11, null=False, blank=False
    )

    amount = models.BigIntegerField(null=False, default=0)
    currency = models.CharField(
        max_length=3, verbose_name="Currency", null=False, default="USD"
    )

    success_url = models.CharField(
        max_length=255, verbose_name="success_url", null=True, default=None
    )

    redirect_url = models.CharField(
        max_length=255, verbose_name="redirect_url", null=True, default=None
    )

    creator = models.ForeignKey(
        "TelegramUser", on_delete=models.CASCADE, null=False, blank=False
    )

    bot = models.ForeignKey(
        "ClientBot", related_name="ewallet_payments",
        on_delete=models.CASCADE, verbose_name="бот, у якому буде оброблятись оплата",
        null=False, blank=False
    )

    time_created = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now_add=True)
    paid_datetime = models.DateTimeField(null=True, default=None)

    payment_settings = models.ForeignKey(
        "PaymentSettings", on_delete=models.PROTECT, null=True, default=None
    )

    paid_user = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT, verbose_name="payer",
        related_name="payed_payments", related_query_name="payed_payments",
        null=True, blank=True
    )

    error = models.CharField(
        max_length=255,
        null=True, blank=True, default=''
    )


class EWalletUser(models.Model):
    class Meta:
        verbose_name = 'EWallet User'
        verbose_name_plural = 'EWallet Users'
        db_table = 'ewallet_users'
        unique_together = ('ewallet', 'user')

    id = models.BigAutoField(primary_key=True)
    ewallet = models.ForeignKey(
        'EWallet', on_delete=models.CASCADE, null=False, blank=False
    )
    user = models.ForeignKey(
        'TelegramUser', on_delete=models.CASCADE, null=False, blank=False
    )
    time_created = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now=True)
    is_deleted = models.BooleanField(default=False, verbose_name="Видалений")


class EWalletExternalPayment(models.Model):
    class Meta:
        db_table = "ewallet_external_payments"

    id = models.BigAutoField(primary_key=True)

    uuid_id = models.CharField(
        max_length=36,
        editable=False,
        unique=True,
    )

    ewallet = models.ForeignKey(
        "EWallet", on_delete=models.CASCADE, null=False, blank=False
    )

    profile = models.ForeignKey(
        "Group", on_delete=models.CASCADE, null=False, blank=False
    )

    user = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT,
        related_name="ewallet_external_payments",
        related_query_name="ewallet_external_payments",
    )

    payer = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT,
        related_name="payers_ewallet_external_payments",
        related_query_name="payers_ewallet_external_payments",
        null=True, blank=True
    )

    status = models.CharField(
        max_length=9,
        default="CREATED",
    )

    amount = models.DecimalField(max_digits=12, decimal_places=2)
    fee = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    discount_percent = models.DecimalField(max_digits=4, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    currency = models.CharField(max_length=3)

    incust_transaction_id = models.CharField(max_length=256)

    transfer_type = models.CharField(
        max_length=6,
    )
    transfer_data = models.JSONField()

    messanger_type = models.CharField(max_length=8, null=True, blank=True)
    message_id = models.CharField(max_length=256, null=True, blank=True)

    time_updated = models.DateTimeField(auto_now=True)
    time_created = models.DateTimeField(auto_now=True)


class EWalletExternalPaymentStatusHistory(models.Model):
    class Meta:
        db_table = "ewallet_external_payment_status_history"

    id = models.BigAutoField(primary_key=True)

    payment = models.ForeignKey(EWalletExternalPayment, on_delete=models.RESTRICT)

    initiated_by = models.CharField(max_length=7)
    initiated_by_user = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT,
    )

    status = models.CharField(
        max_length=9,
        default="CREATED",
    )
    time_created = models.DateTimeField(auto_now=True)


class EWalletMerchant(models.Model):
    class Meta:
        db_table = "ewallet_merchants"

    id = models.BigAutoField(primary_key=True)

    name = models.CharField(max_length=255)
    description = models.CharField(max_length=1024, null=True, blank=True)

    ewallet = models.ForeignKey(EWallet, on_delete=models.CASCADE)

    qrcodes = models.JSONField()

    incust_check_item_code = models.CharField(max_length=255, null=True, blank=True)
    incust_check_item_category = models.CharField(max_length=255, null=True, blank=True)
    is_pay_fee_self = models.BooleanField(default=False, verbose_name="Merchant pays fee")

    time_created = models.DateTimeField(auto_now_add=True)
    is_deleted = models.BooleanField(default=False, verbose_name="Видалений")
