from django.db import models


class ActionType(models.TextChoices):
    IMPORT = "import"
    EXPORT = "export"


class StatusType(models.TextChoices):
    NOT_STARTED = "not_started"
    PENDING = "pending"
    LOADING = "loading"
    SAVING = "saving"
    DONE = "done"
    ERROR = "error"


class DataPorter(models.Model):

    class Meta:
        db_table = "data_porters"
        verbose_name = "Переносчик данных"
        verbose_name_plural = "Переносчик данных"

    id = models.BigAutoField(primary_key=True)
    action_type = models.CharField(
        max_length=6,
        choices=ActionType.choices,
        null=False,
        verbose_name="Тип действия",
    )
    external_type = models.CharField(max_length=10, null=False, verbose_name="Тип источника")
    status = models.CharField(
        max_length=12,
        choices=StatusType.choices,
        null=False, default=StatusType.NOT_STARTED,
        verbose_name="Статус",
    )
    uuid_id = models.CharField(max_length=36, unique=True, null=False)

    time_created = models.DateTimeField(null=False, verbose_name="Дата создания")
    start_time = models.DateTimeField(null=True, blank=True, verbose_name="Дата начала процесса")
    end_time = models.DateTimeField(
        null=True, blank=True,
        verbose_name="Дата завершения процесса",
    )

    user = models.ForeignKey(
        "TelegramUser",
        on_delete=models.SET_NULL, verbose_name="Создавший пользователь",
        blank=True, null=True, default=None
    )
    message_id = models.IntegerField(null=True, blank=True, verbose_name="message id")

    brand = models.ForeignKey(
        "Brand", null=False,
        on_delete=models.CASCADE, verbose_name="Бренд",
    )

    lang = models.CharField(max_length=2, null=True, default=None, verbose_name="Язык профиля")
    user_lang = models.CharField(
        max_length=2, null=True, default=None,
        verbose_name="Язык пользователя"
    )
    prom_file_main_lang = models.CharField(max_length=2, null=True, default=None, verbose_name="Язык пром")

    source_data = models.TextField(
        blank=True, null=True, default=None,
        verbose_name="Источник данных"
    )

    result_data = models.TextField(blank=True, null=True, verbose_name="текст результата")
    error_text = models.TextField(blank=True, null=True, verbose_name="текст ошибки")

    callback_url = models.CharField(
        max_length=256,
        null=True, blank=True, default=None,
        verbose_name="Callback url для ответа"
    )
    is_read = models.BooleanField(default=False, blank=True, null=True, verbose_name="Is this task read by user")
