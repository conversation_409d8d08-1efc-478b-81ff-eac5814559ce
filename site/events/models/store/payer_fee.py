from datetime import datetime

from django.db import models


class PayerFee(models.Model):
    class Meta:
        db_table = "payer_fees"
        verbose_name = "payer fee by payment method"
        verbose_name_plural = "payer fee by payment method"

    id = models.BigAutoField(primary_key=True)
    invoice = models.ForeignKey('Invoice', on_delete=models.CASCADE, db_column='invoice_id')
    status = models.CharField(max_length=99)

    type = models.CharField(max_length=99)

    payment_method = models.CharField(max_length=99)

    fee = models.BigIntegerField()
    payer_fee_type = models.CharField(max_length=99, null=True, default=None)
    payer_fee_percent = models.CharField(max_length=99, null=True, default=None)
    payer_fee_value = models.CharField(max_length=99, null=True, default=None)

    fee_value = models.FloatField(default=0)
    fee_percent = models.FloatField(default=0)

    time_created = models.DateTimeField(default=datetime.utcnow)
