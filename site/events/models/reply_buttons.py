from django.db import models


class ReplyButtons(models.Model):

    class Meta:
        db_table = "reply_buttons"
        verbose_name = "Reply Button"
        verbose_name_plural = "Reply Buttons"

    id = models.BigAutoField(primary_key=True)
    media = models.ForeignKey("MediaObject", on_delete=models.RESTRICT, null=True, blank=True)
    content = models.JSONField(null=True)
    params = models.JSONField(null=True)
    content_type = models.CharField(max_length=9)
    hash = models.CharField(max_length=255, null=False, unique=True)
