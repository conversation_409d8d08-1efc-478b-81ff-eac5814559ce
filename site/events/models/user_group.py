from django.db import models
from django.utils import timezone


class UserGroup(models.Model):
    class Meta:
        db_table = "user_groups"

    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=50)

    owner_type = models.CharField(max_length=7)

    owner_profile = models.ForeignKey("Group", on_delete=models.CASCADE)

    time_created = models.DateTimeField(default=timezone.now)


class UserToUserGroup(models.Model):
    class Meta:
        db_table = "user_to_user_groups"

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE)
    user_group = models.ForeignKey("UserGroup", on_delete=models.CASCADE)
