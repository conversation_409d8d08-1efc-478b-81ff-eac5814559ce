from django.db import models


class Admin(models.Model):

    class Meta:
        db_table = "admins"
        verbose_name_plural = "Админы"

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE, verbose_name="пользователь")
    group = models.ForeignKey("Group", on_delete=models.CASCADE, verbose_name="группа")
    is_superadmin_friendly_bot = models.BooleanField(default=False, verbose_name="суперадмин френдли бота")

    def __str__(self):
        username_or_full_name = self.user.username if self.user.username is not None else self.user.full_name
        return username_or_full_name if username_or_full_name is not None else ""
