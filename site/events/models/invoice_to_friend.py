import datetime

from django.db import models


class InvoiceToFriend(models.Model):
    class Meta:

        db_table = "invoice_to_friends"
        verbose_name_plural = "relation invoices and user friends"

    status = models.CharField(max_length=20, blank=True, null=True, verbose_name="Статус", default="pending")
    invoice = models.ForeignKey("Invoice", on_delete=models.CASCADE)
    friend = models.ForeignKey("TelegramUser", on_delete=models.RESTRICT)
    comment = models.TextField(null=True, blank=True)
    comment_media = models.ForeignKey("MediaObject", on_delete=models.RESTRICT, null=True, blank=True)
    date_sent_to_friend = models.DateTimeField(default=datetime.datetime.utcnow)
