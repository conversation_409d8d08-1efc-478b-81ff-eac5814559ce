from django.db import models


class VirtualManagerInteractive(models.Model):
    class Meta:
        db_table = "virtual_manager_interactives"

    id = models.BigAutoField(primary_key=True)
    is_deleted = models.BooleanField(default=False)
    time_created = models.DateTimeField(auto_now_add=True)

    type = models.CharField(max_length=13)
    subtype = models.CharField(max_length=18)

    position = models.PositiveSmallIntegerField()

    step = models.ForeignKey("VirtualManagerStep", on_delete=models.CASCADE)

    params = models.JSONField(null=True, blank=True)
