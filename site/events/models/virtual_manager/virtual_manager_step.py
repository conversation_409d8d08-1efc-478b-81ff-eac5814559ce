from django.db import models


class VirtualManagerStep(models.Model):
    class Meta:
        db_table = 'virtual_manager_steps'

    id = models.BigAutoField(primary_key=True)
    is_deleted = models.BooleanField(default=False)
    time_created = models.DateTimeField(auto_now_add=True)

    position = models.PositiveSmallIntegerField()

    virtual_manager = models.ForeignKey("VirtualManager", on_delete=models.CASCADE)

    text = models.TextField(null=True)
    media = models.ForeignKey("MediaObject", on_delete=models.RESTRICT, null=True, blank=True)

    reminder_mode = models.CharField(max_length=7, default="DEFAULT")
    reminder_delay = models.PositiveSmallIntegerField(null=True, blank=True)
    reminds_count = models.PositiveSmallIntegerField(null=True, blank=True)

    task = models.ForeignKey(
        "Task",
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="vm_step_task",
        related_query_name="vm_step_task"
    )
