from django.db import models
from django.utils import timezone


class VirtualManagerChat(models.Model):
    class Meta:
        db_table = "virtual_manager_chats"
        verbose_name = "Чат виртуального менеджера с пользователем"
        verbose_name_plural = "Чаты виртуальных менеджеров с пользователем"

    id = models.BigAutoField(primary_key=True)
    is_deleted = models.BooleanField(default=False, verbose_name="удалён")
    user = models.ForeignKey(
        "TelegramUser", on_delete=models.CASCADE, verbose_name="пользователь"
    )
    bot = models.ForeignKey("ClientBot", on_delete=models.CASCADE, verbose_name="бот")
    group = models.ForeignKey("Group", on_delete=models.CASCADE, verbose_name="группа")
    virtual_manager = models.ForeignKey(
        "VirtualManager", on_delete=models.CASCADE, verbose_name="виртуальный менеджер"
    )
    started_datetime = models.DateTimeField(
        default=timezone.now, verbose_name="дата старта"
    )
    last_sent_message_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="дата последней отправки сообщения",
    )
    last_answer_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="дата последнего ответа пользователя",
    )
    when_send_message = models.DateTimeField(
        null=True, blank=True,
        verbose_name="ожидаемое время отправки сообщения",
    )

    current_step = models.ForeignKey(
        "VirtualManagerStep",
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="vmc_current_steps"
    )
    continue_to_step = models.ForeignKey(
        "VirtualManagerStep",
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="vmc_continue_to_steps"
    )

    is_last_answer_for_remind = models.BooleanField(
        default=False,
        verbose_name="является ли последний ответ ответом на напоминание",
    )
    need_send_unhandled_message = models.BooleanField(
        default=False,
        verbose_name="нужно ли реагировать на нераспознанное сообщение",
    )
    is_phone_asked = models.BooleanField(
        default=False,
        verbose_name="необходимость вернуться в главное меню после запроса номера "
                     "телефона"
    )
    is_location_asked = models.BooleanField(
        default=False,
        verbose_name="необходимость вернуться в главное меню после запроса локации"
    )
    last_msg_id_for_set_menu_in_vm = models.BigIntegerField(
        null=True,
        verbose_name="id последнего сообщения в вм в котором была изменена клавиатура"
                     " меню"
    )

    ticket = models.ForeignKey(
        "CRMTicket", on_delete=models.RESTRICT, null=True, blank=True
    )

    is_usage_recorded = models.BooleanField(default=False)
