from django.db import models
from django.utils import timezone


class AuthSession(models.Model):
    class Meta:
        db_table = "auth_sessions"

    id = models.BigAutoField(primary_key=True)
    is_active = models.BooleanField(default=True)

    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE)

    auth_source = models.CharField(max_length=7)
    device_info = models.CharField(max_length=255)

    last_refresh_datetime = models.DateTimeField(null=True, blank=True)
    expire_datetime = models.DateTimeField()

    push_token = models.CharField(max_length=512, null=True, blank=True, unique=True)
    push_token_update_datetime = models.DateTimeField(null=True, blank=True)

    time_created = models.DateTimeField(default=timezone.now)

    last_activity = models.DateTimeField(null=True, blank=True)
