from django.db import models
from django.utils import timezone


class Customer(models.Model):
    class Meta:
        db_table = "customers"

    id = models.BigAutoField(primary_key=True)

    lang = models.CharField(max_length=10, null=True, blank=True, verbose_name="язык")
    marketing_consent = models.BooleanField(default=None, null=True, blank=True)
    is_accept_agreement = models.BooleanField(default=None, null=True, blank=True)

    profile = models.ForeignKey("Group", on_delete=models.CASCADE)
    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE)

    time_created = models.DateTimeField(default=timezone.now)
    updated_date = models.DateTimeField(null=True, blank=True)
