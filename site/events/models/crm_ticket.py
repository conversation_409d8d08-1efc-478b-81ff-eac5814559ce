from django.db import models
from django.utils import timezone


class CRMTicket(models.Model):
    class Meta:
        db_table = "crm_tickets"

    id = models.BigAutoField(primary_key=True)
    time_created = models.DateTimeField(default=timezone.now)

    source = models.CharField(max_length=4)
    status = models.CharField(max_length=16)
    change_date = models.DateTimeField(default=timezone.now)

    title = models.CharField(max_length=50)

    group = models.ForeignKey("Group", on_delete=models.RESTRICT)
    bot = models.ForeignKey("ClientBot", on_delete=models.RESTRICT, null=True, blank=True)
    user = models.ForeignKey("TelegramUser", on_delete=models.RESTRICT, null=True, blank=True)

    internal_comment = models.CharField(max_length=1024, null=True, blank=True)


class CRMTicketStatus(models.Model):
    class Meta:
        db_table = "crm_ticket_statuses"

    id = models.BigAutoField(primary_key=True)
    time_created = models.DateTimeField(default=timezone.now)

    status = models.CharField(max_length=16)

    initiated_by = models.CharField(max_length=8)
    initiated_by_user = models.ForeignKey("TelegramUser", on_delete=models.RESTRICT, null=True, blank=True)

    ticket = models.ForeignKey("CRMTicket", on_delete=models.CASCADE)

    header = models.CharField(max_length=124, null=True, blank=True)
    message = models.CharField(max_length=1024, null=True, blank=True)

    internal_comment = models.CharField(max_length=124, null=True, blank=True)
