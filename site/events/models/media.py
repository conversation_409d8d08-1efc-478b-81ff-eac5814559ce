from django.db import models
from django.utils import timezone


class MediaObject(models.Model):
    class Meta:
        db_table = "media_objects"

    id = models.BigAutoField(primary_key=True)

    media_type = models.CharField(max_length=11)
    mime_type = models.CharField(max_length=100, default="")

    hash_sum = models.CharField(max_length=64, unique=True, db_collation="utf8_bin")
    file_path = models.CharField(max_length=256, unique=True)
    original_file_name = models.CharField(max_length=1024, null=True, blank=True)

    file_size = models.PositiveIntegerField()

    time_created = models.DateTimeField(default=timezone.now)


class MediaPack(models.Model):

    class Meta:
        db_table = "media_packs"

    id = models.BigAutoField(primary_key=True)

    media = models.ForeignKey(
        MediaObject,
        on_delete=models.RESTRICT,
        related_name="media_packs_medias",
        related_query_name="media_packs_media",
    )
    thumbnail_media = models.ForeignKey(
        MediaObject,
        null=True, blank=True,
        on_delete=models.RESTRICT,
        related_name="media_packs_thumbnail_medias",
        related_query_name="media_packs_thumbnail_medias"
    )

    time_created = models.DateTimeField(default=timezone.now)


class ProfileMedia(models.Model):
    class Meta:
        db_table = "profile_medias"

    id = models.BigAutoField(primary_key=True)
    profile = models.ForeignKey("Group", on_delete=models.RESTRICT)
    media = models.ForeignKey(MediaObject, on_delete=models.RESTRICT)


class SizedMedia(models.Model):
    class Meta:
        db_table = "sized_medias"

    id = models.BigAutoField(primary_key=True)

    media_pack = models.ForeignKey(MediaPack, on_delete=models.RESTRICT)
    media = models.ForeignKey(MediaObject, on_delete=models.RESTRICT)

    time_created = models.DateTimeField(default=timezone.now)
