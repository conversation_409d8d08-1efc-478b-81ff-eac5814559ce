from django.db import models
from django.utils import timezone as tz


class Draw(models.Model):

    class Meta:
        db_table = "draws"
        verbose_name = "Розыгрыш"
        verbose_name_plural = "Розыгрыши"

    id = models.BigAutoField(primary_key=True)
    is_simple_mode = models.BooleanField(default=True, verbose_name="Режим розыгрыша")

    name = models.TextField(verbose_name="название")
    status = models.CharField(max_length=10, default="active", verbose_name="статус")

    creator = models.ForeignKey(
        "TelegramUser", related_name="creator",
        on_delete=models.PROTECT, verbose_name="создатель",
    )
    bot = models.ForeignKey("ClientBot", db_column="bot_id", on_delete=models.CASCADE, null=True, verbose_name="бот")
    created_by_bot = models.ForeignKey(
        "ClientBot",
        db_column="created_by_bot_id",
        related_name="created_by_bot",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="бот из которого было создано расписание"
    )

    content_type = models.CharField(max_length=15, verbose_name="тип контента")
    media_path = models.CharField(max_length=512, null=True, blank=True, verbose_name="путь к медиа")
    description = models.TextField(null=True, blank=True, verbose_name="описание")
    terms = models.TextField(verbose_name="условия")

    time_spending = models.DateTimeField(null=False, verbose_name="время проведения")
    time_created = models.DateTimeField(default=tz.now, verbose_name="время создания")

    channels_for_subscription = models.ManyToManyField(
        "Channel", db_table="draws_channels_subscription_associations", verbose_name="каналы для подписки",
        blank=True, related_name="channels_for_subscription", related_query_name="channels_for_subscription"
    )

    channel_to_invite_friends = models.ForeignKey(
        "Channel", related_name="channel_to_invite_friends",
        on_delete=models.SET_NULL, verbose_name="канал для приглашения",
        blank=True, null=True, default=None
    )
    count_invite_friends = models.IntegerField(default=0, verbose_name="Количество друзей, что необходимо пригласить")

    follow_link = models.TextField(null=True, blank=True, verbose_name="ссылка для перехода")
    count_followed_link = models.IntegerField(default=0, verbose_name="Количество пользователей, что необходимо для перехода по ссылке")

    channel_for_writing_messages = models.ForeignKey(
        "Channel", related_name="channel_for_writing_messages",
        on_delete=models.SET_NULL, verbose_name="канал для написания сообщений",
        blank=True, null=True, default=None
    )
    count_needed_messages = models.IntegerField(default=0, verbose_name="Количество сообщений для написания")
    instructions_for_writing_messages = models.TextField(null=True, blank=True, verbose_name="инструкции для написания сообщений")
    filter_words_message = models.JSONField(null=True, blank=True, verbose_name="фильтр слов в сообщениях")

    is_public = models.BooleanField(default=True, verbose_name="публичные результаты")
    count_notifications = models.IntegerField(default=3, verbose_name="Количество напоминаний")

    contact = models.ForeignKey(
        "TelegramUser", related_name="contact",
        on_delete=models.PROTECT, verbose_name="контакт для получения выигрыша",
    )



    def __str__(self):
        return str(self.id)


class DrawResult(models.Model):

    class Meta:
        db_table = "draw_results"
        verbose_name = "Результат розыгрыша"
        verbose_name_plural = "Результаты розыгрышей"

    id = models.BigAutoField(primary_key=True)
    draw = models.ForeignKey(Draw, on_delete=models.CASCADE, verbose_name="Розыгрыш")

    time_created = models.DateTimeField(default=tz.now, verbose_name="время создания")

    user = models.ForeignKey(
        "TelegramUser",
        on_delete=models.PROTECT, verbose_name="пользователь",
    )
    user_link = models.TextField(verbose_name="уникальная ссылка пользователя")

    count_writing_messages = models.IntegerField(default=0, verbose_name="Количество написанных сообщений")

    channels_subscripted = models.ManyToManyField(
        "Channel", db_table="draw_results_channels_subscripted_associations", verbose_name="подписанные каналы",
        blank=True, related_name="channels_subscripted", related_query_name="channels_subscripted"
    )

    invite_friends = models.ManyToManyField(
        "TelegramUser", db_table="draw_results_invite_friends_associations", verbose_name="приглашенные друзья",
        blank=True, related_name="invite_friends", related_query_name="invite_friends"
    )

    follow_users = models.ManyToManyField(
        "TelegramUser", db_table="draw_results_follow_users_associations", verbose_name="пользователи, перешедшие по ссылке",
        blank=True, related_name="follow_users", related_query_name="follow_users"
    )

    award_category = models.ForeignKey(
        "AwardCategory", on_delete=models.CASCADE,
        null=True, blank=True, default=None,
        verbose_name="награда",
    )

    count_notifications = models.IntegerField(default=0, verbose_name="Количество отправленных напоминаний")
    time_need_notification = models.DateTimeField(null=True, blank=True, default=None, verbose_name="время следующего напоминания")

    def __str__(self):
        return str(self.id)


class Award(models.Model):

    class Meta:
        db_table = "awards"
        verbose_name = "Награда"
        verbose_name_plural = "Награды"

    id = models.BigAutoField(primary_key=True)
    award_category = models.OneToOneField(
        "AwardCategory", on_delete=models.CASCADE,
        verbose_name="Категория награды",
        related_name="award",
        related_query_name="award"
    )

    content_type = models.CharField(max_length=15, verbose_name="тип контента")
    text = models.TextField(null=True, blank=True, verbose_name="текст")
    media_path = models.CharField(max_length=512, null=True, blank=True, verbose_name="путь к медиа")

    def __str__(self):
        return str(self.id)


class AwardCategory(models.Model):

    class Meta:
        db_table = "award_categories"
        verbose_name = "Категория награды"
        verbose_name_plural = "Категории наград"

    id = models.BigAutoField(primary_key=True)
    name = models.TextField(verbose_name="название")

    count_winners = models.IntegerField(default=1, verbose_name="Количество победителей")
    draw = models.ForeignKey(Draw, on_delete=models.CASCADE, verbose_name="Розыгрыш")

def __str__(self):
        return str(self.id)
