from django.db import models

from events.helpers import f


class Review(models.Model):

    class Meta:

        db_table = "reviews"
        verbose_name_plural = "Отзывы"

    id = models.BigAutoField(primary_key=True)
    bot = models.ForeignKey(
        "ClientBot",
        on_delete=models.SET_NULL,
        verbose_name="бот в котором оставлено",
        null=True, blank=True,
    )
    group = models.ForeignKey(
        "Group", on_delete=models.PROTECT,
        related_name="reviews", related_query_name="reviews",
        verbose_name="группа в которой принадлежит"
    )
    user = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT,
        verbose_name="пользователь оставивший отзыв",
        related_name="reviews", related_query_name="reviews"
    )
    privacy = models.CharField(max_length=7, default="public", verbose_name="приватность")
    media = models.JSONField(null=True, verbose_name="медиа в json", blank=True)
    type = models.CharField(max_length=6)
    review = models.JSONField(verbose_name="оценка")
    text = models.TextField(verbose_name="текст")
    additional_text = models.TextField(verbose_name="текст для персонала", null=True, blank=True)
    time = models.DateTimeField(max_length=256, auto_now_add=True, blank=True, verbose_name="время")

    _is_read = models.BooleanField(default=False)
    read_by_user = models.ForeignKey(
        "TelegramUser",
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="read_reviews",
        related_query_name="read_reviews"
    )
    read_status_change_datetime = models.DateTimeField(null=True, blank=True)

    utm_labels = models.JSONField(
        null=True,
        blank=True,
        verbose_name="Analytics Data",
    )

    @property
    def stars(self) -> int:
        return self.review.get("stars")

    @property
    def emoji(self) -> str:
        return self.review.get("emoji")

    @property
    def number(self) -> int:
        return self.review.get("number")

    @property
    def review_type(self) -> str:
        return list(self.review.keys())[0] if self.review else None

    def review_view(self, lang: str = "ru") -> str:
        if self.review_type == "stars":
            review = f("review star", lang) * self.stars
        else:
            review = getattr(self, self.review_type)
        return f(f"review_{self.review_type}_info", lang, **{self.review_type: review})

    @property
    def media_type(self) -> str:
        return list(self.media.keys())[0] if self.media.keys() else "text"

    @staticmethod
    def stars_count(stars_count: int = None) -> int:
        queryset = Review.objects.annotate(count=models.Count("id", distinct=True))
        if stars_count:
            queryset = queryset.filter(review__stars=stars_count)
        return queryset.count
