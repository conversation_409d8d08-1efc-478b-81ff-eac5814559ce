from django.db import models


class WATemplate(models.Model):
    class Meta:
        verbose_name = 'Whatsapp templates'
        verbose_name_plural = 'Whatsapp template'
        db_table = 'wa_templates'

    id = models.BigAutoField(primary_key=True)

    is_deleted = models.BooleanField(default=False, verbose_name="Виделено")

    template_id = models.CharField(max_length=255, verbose_name="", null=True, default=None)
    template_status = models.CharField(max_length=255, verbose_name="", null=True, default=None)
    template_status_reason = models.CharField(max_length=255, verbose_name="Імя", null=True, default=None)
    template_data = models.JSONField(default=None, null=True, blank=True)

    master_template = models.ForeignKey(
        "WAMAsterTemplate", related_name="wa_master_templates",
        on_delete=models.CASCADE, verbose_name="",
        null=False, blank=False
    )

    lang = models.Char<PERSON><PERSON>(max_length=10, null=True, blank=True, verbose_name="язык")

    time_created = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now_add=True)


class WAMasterTemplate(models.Model):
    class Meta:
        verbose_name = 'Whatsapp master templates'
        verbose_name_plural = 'Whatsapp master template'
        db_table = 'wa_master_templates'

    id = models.BigAutoField(primary_key=True)

    name = models.CharField(max_length=255, verbose_name="Імя", null=True, default=None)
    description = models.CharField(
        max_length=255, verbose_name="Опис", null=True, default=None
    )

    creator = models.ForeignKey("TelegramUser", on_delete=models.CASCADE)

    bot = models.ForeignKey(
        "ClientBot", related_name="wa_templates",
        on_delete=models.CASCADE, verbose_name="",
        null=False, blank=False
    )

    is_deleted = models.BooleanField(default=False, verbose_name="Виделено")

    category = models.CharField(max_length=255, verbose_name="Категорія", null=True, default=None)

    time_created = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now_add=True)
