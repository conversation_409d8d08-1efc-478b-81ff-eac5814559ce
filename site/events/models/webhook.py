from django.db import models
from django.utils import timezone


class Webhook(models.Model):
    class Meta:
        db_table = 'webhooks'

    id = models.BigAutoField(primary_key=True)
    endpoint_url = models.CharField(max_length=512, null=False)
    hook_id = models.CharField(max_length=36, null=False)
    entities = models.JSONField(default=None, null=True, blank=True)
    is_enabled = models.BooleanField(default=True, null=False)
    last_sent_status = models.CharField(max_length=7, null=False, default="idle")
    retries_count = models.IntegerField(default=0, null=False)
    last_sent_datetime = models.DateTimeField(default=None, null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    description = models.CharField(max_length=512, null=True, blank=True)

    persistent_webhook = models.BooleanField(default=False, null=False)

    group = models.ForeignKey("Group", on_delete=models.CASCADE)


class WebhookJournal(models.Model):
    class Meta:
        db_table = 'webhook_journals'

    id = models.BigAutoField(primary_key=True)
    journal_uuid = models.CharField(max_length=36, null=False)
    entity = models.CharField(max_length=255, null=False)
    entity_id = models.BigIntegerField(null=False)
    action = models.CharField(max_length=255, null=False)
    event_created_datetime = models.DateTimeField(default=timezone.now, null=False)
    event_start_datetime = models.DateTimeField(default=None, null=True, blank=True)
    event_end_datetime = models.DateTimeField(default=None, null=True, blank=True)
    json_data = models.JSONField(default=None, null=True, blank=True)
    status = models.CharField(max_length=9, null=False, default="created")

    webhook = models.ForeignKey(Webhook, on_delete=models.CASCADE)
