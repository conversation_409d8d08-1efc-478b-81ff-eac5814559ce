from django.db import models
from datetime import datetime


class QrMediaObject(models.Model):
    class Meta:
        db_table = "qr_media_objects"
        verbose_name_plural = "Медиа объекты qr кода"
        verbose_name = "Медиа объект qr кода"

    id = models.BigAutoField(primary_key=True)
    json_data = models.JSONField(verbose_name="Данные", null=True, blank=True)
    name = models.CharField(max_length=255, null=True, blank=True, verbose_name="Имя")
    target = models.CharField(max_length=255, verbose_name="Цель")
    url = models.CharField(max_length=2048, verbose_name="Ссылка")
    media = models.ForeignKey("MediaObject", null=True, blank=True, on_delete=models.SET_NULL)
    time_created = models.DateTimeField(default=datetime.utcnow)


class MenuInStoreToQrMediaObject(models.Model):
    class Meta:
        db_table = "menu_in_store_to_qr_media_objects"

    id = models.BigAutoField(primary_key=True)
    menu_in_store = models.ForeignKey("MenuInStore", on_delete=models.CASCADE)
    qr_media_object = models.ForeignKey("QrMediaObject", on_delete=models.CASCADE)


class ProfileToQrMediaObject(models.Model):
    class Meta:
        db_table = "profile_to_qr_media_objects"

    id = models.BigAutoField(primary_key=True)
    profile = models.ForeignKey("Group", on_delete=models.CASCADE)
    qr_media_object = models.ForeignKey("QrMediaObject", on_delete=models.CASCADE)


class QrMediaAdditionalObject(models.Model):
    class Meta:
        db_table = "qr_media_additional_objects"
        verbose_name_plural = "Дополнительные объекты медиа qr кода"
        verbose_name = "Дополнительный объект медиа qr кода"

    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=100, verbose_name="Имя")
    media = models.ForeignKey("MediaObject", null=True, blank=True, on_delete=models.SET_NULL)


class QrMediaObjectToQrMediaAdditionalObject(models.Model):
    class Meta:
        db_table = "qr_media_object_to_qr_media_additional_objects"

    id = models.BigAutoField(primary_key=True)
    qr_media_object = models.ForeignKey("QrMediaObject", on_delete=models.CASCADE)
    qr_media_additional_object = models.ForeignKey("QrMediaAdditionalObject", on_delete=models.CASCADE)
