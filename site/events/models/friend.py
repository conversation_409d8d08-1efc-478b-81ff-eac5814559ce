from django.db import models
from django.utils import timezone


class Friend(models.Model):

    class Meta:
        db_table = "friends"
        verbose_name_plural = "Friends"
        unique_together = ("user", "friend")

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE, related_name="friend", verbose_name="user")
    friend = models.ForeignKey("TelegramUser", on_delete=models.CASCADE, related_name="user_friend", verbose_name="friend")
    time_created = models.DateTimeField(default=timezone.now)
    datetime_used = models.DateTimeField(default=timezone.now, verbose_name="date last using")

    def __str__(self):
        return f"Friend #[{self.id}]"
