import uuid

from django.db import models
from django.utils import timezone


class ShortLink(models.Model):
    class Meta:
        db_table = "short_links"

    id = models.CharField(
        max_length=36, editable=False, primary_key=True, unique=True, default=uuid.uuid4
    )
    display_id = models.CharField(max_length=6, db_collation="utf8mb4_bin")

    type = models.CharField(max_length=3)
    url = models.CharField(max_length=2048, null=True, blank=True)

    max_uses = models.PositiveSmallIntegerField(null=True, blank=True)
    uses = models.PositiveBigIntegerField(default=0)

    expiration_date = models.DateTimeField(null=True, blank=True)
    time_created = models.DateTimeField(default=timezone.now)
