from datetime import datetime

from django.db import models

from events.models import Group


class Storage(models.Model):
    class Meta:
        db_table = "storage"
        verbose_name = "Хранилище"
        verbose_name_plural = "Хранилища"

    id = models.BigAutoField(primary_key=True)

    text = models.TextField(null=True, blank=True, verbose_name='текст')
    media = models.ForeignKey("MediaObject", on_delete=models.RESTRICT, null=True, blank=True)

    datetime_upload = models.DateTimeField(
        default=datetime.utcnow,
        verbose_name="Дата загрузки"
    )

    group = models.ForeignKey(
        Group, on_delete=models.CASCADE,
        related_name="storage_group", related_query_name="storage_group",
        verbose_name="Группа"
    )

    is_multi_load = models.BooleanField(default=False, verbose_name="Мультизагрузка файлов")
