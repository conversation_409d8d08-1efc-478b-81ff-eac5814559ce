from django.db import models
from django.utils import timezone


class SSEChannel(models.Model):

    class Meta:
        db_table = "sse_channels"
        verbose_name_plural = "sse channels"

    id = models.BigAutoField(primary_key=True)

    session_id = models.CharField(max_length=36, null=False)

    target = models.CharField(max_length=99, default="ADMIN_NOTIFICATION")

    profile = models.ForeignKey(
        "Group", on_delete=models.PROTECT,
        related_name="sse_channels", related_query_name="sse_channels",
        verbose_name="profile", null=True
    )
    key = models.CharField(max_length=99, null=True, default=None)

    time_created = models.DateTimeField(default=timezone.now)
