# -*- coding: utf-8 -*-
from datetime import date, datetime

from django.db import models
from django.utils import timezone as tz
from pay4say.settings import *

from ..helpers import f, load_local_data

load_local_data()


def fill_certificate_code():
    pass  # to remove django error


class UserSettings(models.Model):
    class Meta:
        db_table = "users_settings"
        verbose_name_plural = "Настройки пользователей"

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE, verbose_name="пользователь")
    bot = models.ForeignKey("ClientBot", on_delete=models.CASCADE, verbose_name="бот")
    accept_voucher_notification = models.BooleanField(
        default=True, verbose_name="получать уведомления о принятии ваучера"
    )
    make_order_by_recommendation_notification = models.BooleanField(
        default=True, verbose_name="получать уведомления о создании заказа рефералом"
    )
    close_order_by_recommendation_notification = models.BooleanField(
        default=True, verbose_name="получать уведомления о закрытии заказа реферала"
    )

    def __str__(self):
        return f"настройки для пользователя {self.user.full_name} в боте {self.bot.username}"


class UserInGroup(models.Model):
    class Meta:
        db_table = "users_in_groups"
        verbose_name = "Активность пользователя в группе"
        verbose_name_plural = "Активности пользователей в группах"
        unique_together = ("user", "group")

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE, verbose_name="пользователь")
    group = models.ForeignKey("Group", on_delete=models.CASCADE, verbose_name="группа")
    is_wrote = models.BooleanField(default=False, verbose_name="писал ли группе")
    is_user_blocked = models.BooleanField(default=False, verbose_name="заблокирован ли пользователь")

    last_activity = models.DateTimeField(null=True, blank=True, default=None)

    def __str__(self):
        return f"Активность {self.user.full_name} в группе {self.group.name}"


class UserGroupSettings(models.Model):
    class Meta:
        db_table = "users_groups_settings"
        verbose_name = "Настройки пользователей относительно группы"
        verbose_name_plural = "Настройки пользователей относительно групп"
        unique_together = ("user_settings", "group")

    id = models.BigAutoField(primary_key=True)
    user_settings = models.ForeignKey(
        UserSettings, on_delete=models.CASCADE,
        verbose_name="Настройки пользователей к которым относится"
    )
    group = models.ForeignKey(
        "Group", on_delete=models.CASCADE,
        verbose_name="Группа, к которой относятся настройки"
    )
    # бывает with_sound, without_sound, blocked
    mailing_mode = models.CharField(max_length=30, default="with_sound", verbose_name="Режим рассылок")
    was_changed_mailing_mode = models.BooleanField(
        default=False, verbose_name="был ли изменён mode рассылки"
    )

    def __str__(self):
        return f"{self.user_settings} по группе {self.group}"


class UserAdminBotActivity(models.Model):
    class Meta:
        db_table = "users_admin_bots_activity"
        verbose_name = "Активность пользователя в админ боте"
        verbose_name_plural = "Активности пользователей в админ боте"

    id = models.BigAutoField(primary_key=True)
    is_active = models.BooleanField(default=True, verbose_name="активен ли")
    last_activity = models.DateTimeField(default=tz.now, verbose_name="Последняя активность")

    user = models.OneToOneField("TelegramUser", on_delete=models.CASCADE, verbose_name="пользователь")

    not_delete_vm_messages_mode = models.BooleanField(
        default=False, verbose_name="включён ли мод не удаления сообщений вм при редактировани"
    )

    is_content_admin = models.BooleanField(default=False, verbose_name="является контент-админом")

    filters = models.JSONField(null=True, blank=True, verbose_name="фильтры")


class UserServiceBotActivity(models.Model):
    class Meta:
        db_table = "users_service_bots_activity"
        verbose_name = "Активность пользователя в сервисном боте"
        verbose_name_plural = "Активности пользователей в сервисном боте"

    id = models.BigAutoField(primary_key=True)
    is_active = models.BooleanField(default=True, verbose_name="активен ли")
    user = models.OneToOneField(
        "TelegramUser", on_delete=models.CASCADE, verbose_name="пользователь",
        related_name="activity_in_service_bot"
    )

    pagination = models.IntegerField(default=5, verbose_name="пагинация")
    show_full_menu = models.BooleanField(default=False, verbose_name="показывать полное меню")

    filters = models.JSONField(null=True, blank=True, verbose_name="фильтры")
    friendly_filters = models.JSONField(null=True, blank=True, verbose_name="фильтры для объектов френдли")

    last_activity = models.DateTimeField(default=tz.now, verbose_name="Последняя активность")
    last_reset_state = models.DateTimeField(
        default=tz.now, null=True, blank=True,
        verbose_name="Последний сброс состояния",
    )
    last_sent_crm_notification_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="Дата последней отправки уведомления CRM",
    )

    need_messages_notifications = models.BooleanField(
        default=True, verbose_name="нужны ли уведомления о сообщениях"
    )
    need_orders_notifications = models.BooleanField(
        default=True, verbose_name="нужны ли уведомления о заказах"
    )
    need_vm_notify_notifications = models.BooleanField(
        default=True, verbose_name="нужны ли уведомления из вм"
    )


class CRMCustomList(models.Model):
    class Meta:
        db_table = "crm_custom_lists"
        verbose_name = "Настраиваемый список"
        verbose_name_plural = "Настраиваемые списки"
        unique_together = ("user_id", "name")

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey("TelegramUser", on_delete=models.PROTECT, verbose_name="пользователь")
    name = models.CharField(max_length=20, verbose_name="название кнопки")
    list_type = models.CharField(max_length=10, verbose_name="тип списка")
    filters = models.JSONField(verbose_name="фильтры")

    def __str__(self):
        return f"Настраиваемый список {self.name}"


class CRMSession(models.Model):
    class Meta:
        db_table = "crm_sessions"
        verbose_name = "Сессия пользователя в CRM"
        verbose_name_plural = "Сессии пользователей в CRM"

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey("TelegramUser", on_delete=models.PROTECT, verbose_name="пользователь")
    list_type = models.CharField(max_length=20, null=True, blank=True, verbose_name="тип списка")
    custom_list = models.OneToOneField(
        CRMCustomList, on_delete=models.SET_NULL,
        null=True, blank=True,
        verbose_name="пользовательский список",
    )

    messages_reading_datetime = models.DateTimeField(
        default=tz.now,
        verbose_name="последнее время просмотра списка сообщений",
    )

    vouchers_reading_datetime = models.DateTimeField(
        default=tz.now,
        verbose_name="последнее время просмотра списка ваучеров",
    )

    orders_reading_datetime = models.DateTimeField(
        default=tz.now,
        verbose_name="последнее время просмотра списка заказов",
    )

    payed_orders_reading_datetime = models.DateTimeField(
        default=tz.now,
        verbose_name="последнее время просмотра списка оплаченных заказов",
    )

    store_orders_reading_datetime = models.DateTimeField(
        default=tz.now,
        verbose_name="последнее время просмотра списка заказов с магазина",
    )

    last_notification_message_id = models.IntegerField(
        null=True, blank=True,
        verbose_name="последнее сообщение с уведомлением",
    )

    back_to = models.CharField(
        max_length=20, default="list",
        verbose_name="куда ведёт кнопка назад",
    )
    _filters = models.JSONField(verbose_name="фильтры", null=True)

    def __str__(self):
        return f"Сессия пользователя {self.user}"


class CRMListData(models.Model):
    class Meta:
        db_table = "crm_lists_data"
        verbose_name = "Данные списка"
        verbose_name_plural = "Данные списков"

    id = models.BigAutoField(primary_key=True)

    _list_type = models.CharField(
        max_length=20,
        null=True, blank=True,
        verbose_name="тип списка",
    )
    custom_list = models.ForeignKey(
        CRMCustomList,
        on_delete=models.CASCADE,
        null=True, blank=True,
        verbose_name="кастомный список",
    )

    session = models.ForeignKey(CRMSession, on_delete=models.CASCADE, verbose_name="сессия")

    prev_list_data = models.ForeignKey(
        "CRMListData",
        null=True, blank=True,
        on_delete=models.SET_NULL,
        related_name="next_list_data",
        related_query_name="next_list_data",
        verbose_name="предыдущий список",
    )

    _filters = models.JSONField(verbose_name="фильтры")
    _selected = models.JSONField(verbose_name="выбранные")
    datetime_used = models.DateTimeField(default=tz.now, verbose_name="дата последнего использования")

    crm_position = models.BigIntegerField(
        null=True, blank=True,
        verbose_name="позиция текущего пользователя",
    )


class UserClientBotActivity(models.Model):
    class Meta:
        db_table = "users_client_bots_activity"
        verbose_name = "Активность пользователя в клиентском боте"
        verbose_name_plural = "Активности пользователей в клиентских ботах"
        unique_together = ("user", "bot")
        indexes = [
            models.Index(fields=['vm_when_remind', 'vm_reminded_count'], name='vm_when_remind_vm_reminded_cnt'),
        ]

    id = models.BigAutoField(primary_key=True)
    is_active = models.BooleanField(default=True, verbose_name="активен ли")
    user = models.ForeignKey(
        "TelegramUser", on_delete=models.CASCADE, verbose_name="пользователь",
        related_name="activities_in_bots"
    )
    bot = models.ForeignKey(
        "ClientBot", db_column="bot_id", on_delete=models.CASCADE,
        verbose_name="бот"
    )
    recommender = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT, verbose_name="рекомендатель",
        related_name="invited_friends", related_query_name="invited_friends", null=True,
        blank=True
    )
    last_activity = models.DateTimeField(default=tz.now, verbose_name="Последняя активность")
    show_full_menu = models.BooleanField(default=False, verbose_name="Показывать полное меню")
    is_entered_bot = models.BooleanField(default=False, verbose_name="Пользователь нажал start")

    active_chat = models.ForeignKey("Chat", on_delete=models.SET_NULL, null=True, blank=True)

    chat_mode = models.TextField(max_length=30, null=True, blank=True, verbose_name="в каком чате")
    group_in_chat = models.ForeignKey(
        "Group", on_delete=models.CASCADE, null=True, blank=True, verbose_name="группа",
        related_name="users_in_chat", related_query_name="users_in_chat"
    )
    last_send_finance_notification = models.DateTimeField(
        null=True, blank=True,
        verbose_name="Последнее отправленное финансовое уведомление",
    )
    liplep_receipt_lists_params = models.JSONField(
        null=True, blank=True,
        verbose_name="Состояния списков в боте liplep для пагинации",
    )

    remind_about_vm_chat = models.ForeignKey(
        "VirtualManagerChat", on_delete=models.SET_NULL,
        null=True, blank=True,
        verbose_name="на какой вм напоминать",
    )
    vm_reminded_count = models.IntegerField(
        default=0,
        verbose_name="какое напоминание по счёту",
    )
    vm_when_remind = models.DateTimeField(
        null=True, blank=True,
        verbose_name="когда напомнить",
    )

    is_click_button_webapp = models.BooleanField(
        default=False,
        null=True,
        verbose_name="Нажимал ли пользователь на кнопку каталог",
    )

    is_open_webapp = models.BooleanField(
        default=False,
        null=True,
        verbose_name="Открыл ли пользователь каталог в веб апп",
    )

    _lang = models.CharField(
        max_length=10,
        null=True, blank=True, default=None,
        verbose_name="Язык пользователя в клиентском боте"
    )

    active_menu_in_store = models.ForeignKey(
        "MenuInStore", on_delete=models.SET_NULL,
        null=True, blank=True, default=None,
    )

    is_accept_agreement = models.BooleanField(default=None, null=True, blank=True)
    accepted_agreement_date = models.DateTimeField(null=True, blank=True)
    answered_marketing = models.BooleanField(default=None, null=True, blank=True)

    def __str__(self):
        return f"Активность {self.user.full_name} в боте {self.bot.username}"


class Tag(models.Model):
    class Meta:
        db_table = "tags"
        verbose_name_plural = "метки"
        verbose_name = "метка"

    id = models.BigAutoField(primary_key=True)
    handle = models.CharField(max_length=20)
    group = models.ForeignKey("Group", on_delete=models.CASCADE)

    def __str__(self):
        return self.handle


class UserAnalyticAction(models.Model):
    class Meta:
        db_table = "user_analytic_actions"
        verbose_name = "Действие для аналитики"
        verbose_name_plural = "Действия для аналитики"

    id = models.BigAutoField(primary_key=True)
    type = models.CharField(max_length=50, verbose_name="тип действия")
    datetime = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(
        "TelegramUser", on_delete=models.CASCADE, verbose_name="пользователь",
        related_name="analytic_actions", related_query_name="analytic_actions"
    )
    bot_username = models.CharField(max_length=256, verbose_name="имя бота")
    bot = models.ForeignKey("ClientBot", on_delete=models.PROTECT, verbose_name="бот", null=True)
    order_sum = models.FloatField(null=True, blank=True, verbose_name="сумма заказа")

    manager_chat_id = models.BigIntegerField(null=True, blank=True, verbose_name="телеграмм ID менеджера")
    virtual_manager_chat = models.ForeignKey(
        "VirtualManagerChat", on_delete=models.PROTECT,
        null=True, blank=True, verbose_name="Чат менеджера",
    )

    group = models.ForeignKey("Group", on_delete=models.PROTECT, verbose_name="группа", null=True, blank=True)
    recommender = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT, verbose_name="рекомендатель",
        null=True, blank=True, related_name="recommender_analytic_actions",
        related_query_name="recommender_analytic_actions"
    )
    message_text_field = models.TextField(verbose_name="текст сообщения", null=True, blank=True)
    message_content_type = models.CharField(max_length=30, verbose_name="тип сообщения", null=True, blank=True)
    message_content = models.JSONField(verbose_name="медиа сообщения", null=True, blank=True)

    receiver = models.ForeignKey(
        "TelegramUser", blank=True, null=True, verbose_name="получатель сообщения",
        related_name="receiver_in_analytic", related_query_name="receiver_in_analytic",
        on_delete=models.PROTECT
    )
    virtual_manager = models.ForeignKey(
        "VirtualManager", on_delete=models.PROTECT,
        blank=True, null=True, verbose_name="виртуальный менеджер"
    )
    invoice = models.ForeignKey(
        "Invoice", on_delete=models.PROTECT,
        blank=True, null=True, verbose_name="счёт",
    )
    reminder_interval = models.FloatField(
        null=True, blank=True,
        verbose_name="интервал для напоминания перед события в секундах"
    )
    pagination_count = models.IntegerField(null=True, blank=True, verbose_name="количество объектов в пагинации")
    game_type = models.CharField(max_length=30, null=True, blank=True, verbose_name="тип мини-игры")
    game_score = models.IntegerField(null=True, blank=True, verbose_name="выбитое значение в мини-игре")

    def __str__(self):
        return f"№{self.id}, тип: {self.type}({self.datetime.strftime('%d.%m.%Y %H:%M')})"


class AdminBotAnalyticAction(models.Model):
    class Meta:
        db_table = "admin_bot_analytic_actions"
        verbose_name = "Действие для аналитики админ бота"
        verbose_name_plural = "Действия для аналитики админ бота"

    id = models.BigAutoField(primary_key=True)
    type = models.CharField(max_length=50, verbose_name="тип действия")
    datetime = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(
        "TelegramUser", on_delete=models.CASCADE, verbose_name="пользователь",
        related_name="admin_bot_analytic_actions", related_query_name="admin_bot_analytic_actions"
    )
    group = models.ForeignKey("Group", on_delete=models.PROTECT, verbose_name="группа")
    bot_username = models.CharField(max_length=256, null=True, blank=True, verbose_name="имя бота")
    bot = models.ForeignKey("ClientBot", on_delete=models.PROTECT, null=True, blank=True, verbose_name="бот")


class Questionnaire(models.Model):
    class Meta:
        db_table = "questionnaires"
        verbose_name = "Опросник"
        verbose_name_plural = "Опросники"

    id = models.BigAutoField(primary_key=True)
    is_deleted = models.BooleanField(default=False, verbose_name="удалён")
    name = models.CharField(max_length=100, verbose_name="имя")
    group = models.ForeignKey("Group", on_delete=models.CASCADE, verbose_name="группа")
    questions = models.JSONField(verbose_name="вопросы")

    def __str__(self):
        return self.name


def fill_message_ratio():
    return {"friends_required": 1, "messages_for_friends": 1}


class Footer(models.Model):
    class Meta:
        db_table = 'footers'
        verbose_name = "футер"
        verbose_name_plural = "футеры"

    id = models.BigAutoField(primary_key=True)
    channel = models.ForeignKey(
        "Channel",
        on_delete=models.CASCADE, verbose_name="канал",
    )

    content_type = models.CharField(max_length=15, verbose_name='тип контента')
    text = models.TextField(null=True, blank=True, verbose_name='текст')
    media_path = models.CharField(max_length=512, null=True, blank=True, verbose_name='путь к медиа')
    buttons = models.TextField(null=True, blank=True, verbose_name='кнопки')
    full_text = models.TextField(null=True, blank=True, verbose_name='полный текст')

    need_concatenation = models.BooleanField(default=True, verbose_name="объединение с сообщением")

    def __str__(self):
        return str(self.id)


class Channel(models.Model):
    class Meta:
        db_table = "channels"
        verbose_name = "Канал"
        verbose_name_plural = "Каналы"
        unique_together = ("chat_id", "bot")

    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=256, verbose_name='имя')
    username = models.CharField(max_length=256, null=True, blank=True, verbose_name='юзернейм группы')
    chat_id = models.BigIntegerField(verbose_name='чат ID')
    admins = models.ManyToManyField(
        "TelegramUser", db_table='admins_channels_association',
        verbose_name='администраторы'
    )
    chat_type = models.CharField(max_length=20, verbose_name='тип чата')
    messages_given = models.BigIntegerField(default=2, verbose_name='стартовые сообщения')
    allowed_messages_given_without_rules = models.BigIntegerField(
        default=1, verbose_name='разрешённые сообщения без подтверждения правил'
    )
    max_messages_length = models.BigIntegerField(default=0, verbose_name='Максимальная длина сообщения')
    message_ratio = models.JSONField(
        default=fill_message_ratio,
        null=True, blank=True, verbose_name='соотношения'
    )
    pending_exceptions = models.TextField(
        default='{}', null=True, blank=True,
        verbose_name='соотношение для тех кто ещё не участник'
    )
    alert_threshold = models.BigIntegerField(default=1, null=True, blank=True, verbose_name="порог предупреждения")
    refresh_datetime = models.DateTimeField(default=tz.now, verbose_name='дата обнуления сообщений')
    _info_message = models.JSONField(default=dict, verbose_name="сообщение инфо", null=True, blank=True)
    _welcome_message = models.JSONField(default=dict, verbose_name="сообщение приветствие", null=True, blank=True)
    _deletion_message = models.JSONField(default=dict, verbose_name="сообщение удаление", null=True, blank=True)
    _restriction_message = models.JSONField(
        default=dict, verbose_name="сообщение об ограничении", null=True,
        blank=True
    )
    _alert_message = models.JSONField(default=dict, verbose_name="сообщение предупреждение", null=True, blank=True)
    _ban_sender_chat_message = models.JSONField(
        default=dict, verbose_name="сообщение о бане за спам от лица канала",
        null=True, blank=True
    )
    _rules_message = models.JSONField(verbose_name="правила чата", null=True, blank=True)
    _rules_ban_message = models.JSONField(
        null=True, blank=True,
        verbose_name="сообщение о бане за не принятие правил от лица канала",
    )
    _required_subscription_message = models.JSONField(
        null=True, blank=True,
        verbose_name="Сообщение об обязательной подписке",
    )
    _channel_menu_text_message = models.JSONField(
        null=True, blank=True,
        verbose_name="Сообщение в чате 'Меню установлено'",
    )
    rules_button_text = models.TextField(
        blank=True, default=USE_LOCALISATION,
        verbose_name=f"кнопка {f('channel rules button', 'ru')}",
    )
    info_button_text = models.TextField(
        blank=True, default=USE_LOCALISATION,
        verbose_name=f"кнопка {f('channel info button', 'ru')}",
    )
    contact_admin_button_text = models.TextField(
        blank=True, default=USE_LOCALISATION,
        verbose_name=f"кнопка {f('channel contact admin button', 'ru')}",
    )
    refresh_messages_daily = models.BooleanField(default=True, verbose_name='обновлять сообщения ежедневно')
    system_message_on_screen_time = models.FloatField(
        default=60, null=True, blank=True,
        verbose_name="время до удаления системных сообщений в чате",
    )
    limited_message_on_screen_time = models.FloatField(
        default=60, null=True, blank=True,
        verbose_name="время до удаления сообщения и команды setlimit",
    )
    system_message_on_screen_time = models.FloatField(
        default=60, null=True, blank=True,
        verbose_name="время отображения системных сообщений в чате",
    )
    group = models.OneToOneField(
        "Group", on_delete=models.CASCADE, verbose_name="группа для связи",
        related_name="groups_channel", related_query_name="groups_channel", null=True,
        default=None
    )
    bot = models.ForeignKey("ClientBot", on_delete=models.PROTECT, verbose_name='бот, с которого создан канал')
    attach_datetime = models.DateTimeField(auto_now_add=True, verbose_name='дата подключения')
    need_delete_join_notification = models.BooleanField(default=True, verbose_name="удалять событие входа в чат")
    need_delete_leave_notification = models.BooleanField(default=True, verbose_name="удалять событие выхода из чата")
    filter_words_username = models.JSONField(null=True, blank=True, verbose_name="фильтр слов в именах пользователей")
    filter_words_message = models.JSONField(null=True, blank=True, verbose_name="фильтр слов в сообщениях")
    need_agree_rules = models.CharField(
        max_length=10, default="nobody",
        verbose_name="необходимость бана из-за правил для:"
    )
    need_agree_rules_set_datetime = models.DateTimeField(
        default=datetime.utcnow, null=True, blank=True,
        verbose_name="время установки бана из-за правил в канале"
    )
    limited_agree_rules_time = models.IntegerField(
        default=10, null=True, blank=True,
        verbose_name="время для принятия правил до бана (в минутах)",
    )
    is_bot_admin = models.BooleanField(default=True, verbose_name="является ли бот админом")
    ban_threshold = models.IntegerField(default=10, verbose_name="порог сообщений до бана")
    ban_interval = models.FloatField(default=20, verbose_name="интервел достижения количества сообщений для бана")
    need_delete_restriction_notification = models.BooleanField(
        default=True,
        verbose_name="удалять сообщение ограничения в чате"
    )
    required_resource_subscription_chat_id = models.BigIntegerField(
        default=0,
        verbose_name='чат id обязательной подписки'
    )
    last_required_resource_msg_id = models.BigIntegerField(
        null=True, blank=True, default=None,
        verbose_name='id последнего сообщения об обязательной подписке'
    )

    _status_info_message = models.CharField(max_length=10, default="show", verbose_name="статус сообщения инфо")
    _status_welcome_message = models.CharField(
        max_length=10, default="disabled",
        verbose_name="статус сообщения приветствие"
    )
    _status_deletion_message = models.CharField(max_length=10, default="show", verbose_name="статус сообщения удаление")
    _status_restriction_message = models.CharField(
        max_length=10, default="show",
        verbose_name="статус сообщения об ограничении"
    )
    _status_alert_message = models.CharField(
        max_length=10, default="show",
        verbose_name="статус сообщения предупреждение"
    )
    _status_ban_sender_chat_message = models.CharField(
        max_length=10, default="show",
        verbose_name="статус сообщения о бане за спам от лица канала"
    )
    _status_rules_message = models.CharField(
        max_length=10, default="disabled",
        verbose_name="статус сообщения правила чата"
    )
    _status_rules_ban_message = models.CharField(
        max_length=10, default="show",
        verbose_name="статус сообщения о бане из-за не принятия правил чата"
    )
    _status_required_subscription_message = models.CharField(
        max_length=10, default="show",
        verbose_name="статус сообщения об обязательно подписке"
    )

    welcome_message_rate_limit = models.FloatField(
        null=True, blank=True, default=7,
        verbose_name="один раз в (секунд) отправлять приветствие"
    )
    alert_message_rate_limit = models.FloatField(
        null=True, blank=True, default=7,
        verbose_name="один раз в (секунд) отправлять предупреждение"
    )
    deletion_message_rate_limit = models.FloatField(
        null=True, blank=True, default=7,
        verbose_name="один раз в (секунд) отправлять удаление"
    )
    restriction_message_rate_limit = models.FloatField(
        null=True, blank=True, default=7,
        verbose_name="один раз в (секунд) отправлять ограничение"
    )

    required_subscription_message_rate_limit = models.FloatField(
        null=True, blank=True, default=10,
        verbose_name="один раз в (секунд) отправлять обязательную подписку"
    )

    welcome_message_last_sent_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="дата последней отправки приветствия"
    )
    alert_message_last_sent_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="дата последней отправки предупреждения"
    )
    deletion_message_last_sent_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="дата последней отправки удаления"
    )
    restriction_message_last_sent_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="дата последней отправки ограничения"
    )

    required_subscription_message_last_sent_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="дата последней отправки обязательной подписки"
    )

    _info_footer = models.ForeignKey(
        "Footer", related_name="info_footer", related_query_name="info_footer",
        on_delete=models.SET_NULL, verbose_name="футер для сообщения информации",
        blank=True, null=True, default=None
    )
    _welcome_footer = models.ForeignKey(
        "Footer", related_name="welcome_footer", related_query_name="welcome_footer",
        on_delete=models.SET_NULL, verbose_name="футер для сообщения приветствия",
        blank=True, null=True, default=None
    )
    _deletion_footer = models.ForeignKey(
        "Footer", related_name="deletion_footer", related_query_name="deletion_footer",
        on_delete=models.SET_NULL, verbose_name="футер для сообщения удаления",
        blank=True, null=True, default=None
    )
    _restriction_footer = models.ForeignKey(
        "Footer", related_name="restriction_footer", related_query_name="restriction_footer",
        on_delete=models.SET_NULL, verbose_name="футер для сообщения ограничения",
        blank=True, null=True, default=None
    )
    _alert_footer = models.ForeignKey(
        "Footer", related_name="alert_footer", related_query_name="alert_footer",
        on_delete=models.SET_NULL, verbose_name="футер для сообщения предупреждения",
        blank=True, null=True, default=None
    )
    _ban_sender_chat_footer = models.ForeignKey(
        "Footer", related_name="ban_sender_chat_footer", related_query_name="ban_sender_chat_footer",
        on_delete=models.SET_NULL, verbose_name="футер для сообщения о бане",
        blank=True, null=True, default=None
    )
    _rules_footer = models.ForeignKey(
        "Footer", related_name="rules_footer", related_query_name="rules_footer",
        on_delete=models.SET_NULL, verbose_name="футер для сообщения о правилах",
        blank=True, null=True, default=None
    )
    _rules_ban_footer = models.ForeignKey(
        "Footer", related_name="rules_ban_footer", related_query_name="rules_ban_footer",
        on_delete=models.SET_NULL, verbose_name="футер для сообщения о бане из-за не принятие правил",
        blank=True, null=True, default=None
    )
    _required_subscription_footer = models.ForeignKey(
        "Footer", related_name="required_subscription_footer", related_query_name="required_subscription_footer",
        on_delete=models.SET_NULL, verbose_name="футер для сообщения о необходимости подписки",
        blank=True, null=True, default=None
    )

    max_count_messages_before_required_resource = models.IntegerField(
        default=5,
        verbose_name="Максимальное количество сообщений перед отправкой сообщения об обязательной подписке"
    )

    need_welcome_system_keyboard = models.BooleanField(
        default=True,
        verbose_name="Показывать системную клавиатуру с сообщением приветствия"
    )

    _logs = models.JSONField(verbose_name="логи ошибок", null=True, blank=True)

    def __str__(self):
        return self.name


class ChannelMenuButton(models.Model):
    class Meta:
        db_table = "channel_menu_buttons"
        verbose_name = "Кнопка меню канала"
        verbose_name_plural = "Меню канала"
        unique_together = ("channel_id", "name_button")

    id = models.BigAutoField(primary_key=True)
    channel = models.ForeignKey(Channel, on_delete=models.CASCADE, verbose_name='канал')
    name_button = models.CharField(max_length=20, verbose_name='Название кнопки')
    is_new_line = models.BooleanField(default=False, verbose_name="Перенести кнопку на новую строку?")
    message = models.TextField(default="", verbose_name="Сообщение")
    media = models.TextField(default="", verbose_name="Id файла медиа")
    content_type = models.TextField(default="", verbose_name="Тип сообщения")
    button_number = models.BigIntegerField(verbose_name='Номер кнопки относительно одного чата', null=True)


class ChatMember(models.Model):
    class Meta:
        db_table = "chats_members"
        verbose_name = "участник чата"
        verbose_name_plural = "участники чата"

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE, verbose_name='пользователь')
    channel = models.ForeignKey(Channel, on_delete=models.CASCADE, verbose_name='канал')
    exception_ratio = models.JSONField(null=True, blank=True, verbose_name='особенное соотношение')
    messages_sent_without_rules = models.BigIntegerField(
        default=0, verbose_name='Сообщения, отправленые без принятия правил'
    )
    joined_datetime = models.DateTimeField(auto_now_add=True, verbose_name='дата присоединения')
    left_datetime = models.DateTimeField(null=True, blank=True, verbose_name='дата ухода')
    limited_datetime = models.DateTimeField(default=None, null=True, blank=True, verbose_name='дата ограничения')
    inviter = models.ForeignKey(
        'ChatMember', on_delete=models.PROTECT, related_name='invited_friend',
        related_query_name='invited_friend', blank=True, null=True
    )
    is_agreed_rules = models.BooleanField(default=False, verbose_name="соглашение с правилами чата")
    ban_user_status = models.BooleanField(default=False, verbose_name="статус бана пользователя")
    rules_start_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="время получения сообщения о необходимости принять правила",
    )
    is_favorite = models.BooleanField(default=False, verbose_name="добавлен в избранное")
    max_message_limit = models.IntegerField(null=True, default=0, verbose_name="Лимит сообщений для пользователя")

    def __str__(self):
        return f"{self.user}({self.channel})"


class Post(models.Model):
    class Meta:
        db_table = 'posts'
        verbose_name = "пост"
        verbose_name_plural = "посты"

    id = models.BigAutoField(primary_key=True)
    schedule = models.ForeignKey('Schedule', on_delete=models.CASCADE, verbose_name='расписание')

    creator = models.ForeignKey(
        "TelegramUser",
        on_delete=models.PROTECT, verbose_name="создатель",
        blank=True, null=True,
    )
    time_created = models.DateTimeField(default=tz.now, verbose_name="время создания")

    text = models.TextField(null=True, blank=True, verbose_name='текст')
    content_type = models.CharField(max_length=15, verbose_name='тип контента')
    media_path = models.CharField(max_length=512, null=True, blank=True, verbose_name='путь к медиа')

    need_contact_button = models.BooleanField(default=True, verbose_name="кнопка связаться")
    is_show_web_page_preview = models.BooleanField(default=False, verbose_name='Показывать превью ссылки')
    is_show_html_tag = models.BooleanField(default=False, verbose_name='Показывать html теги в настройках')
    footer = models.ForeignKey(
        "Footer",
        on_delete=models.SET_NULL, verbose_name="футер",
        blank=True, null=True, default=None
    )

    def __str__(self):
        return str(self.id)


class Schedule(models.Model):
    class Meta:
        db_table = "schedules"
        verbose_name = "расписание"
        verbose_name_plural = "расписания"

    id = models.BigAutoField(primary_key=True)
    _name = models.CharField(max_length=20, null=True, blank=True, verbose_name="имя")
    status = models.CharField(max_length=20, default="active", null=True, blank=True, verbose_name="статус")
    schedule_type = models.CharField(max_length=20, null=True, blank=True, verbose_name="Тип расписания")
    start_date = models.DateField(default=date.today, verbose_name="Дата старта")
    start_time = models.TimeField(null=True, blank=True, verbose_name="Время старта")
    end_time = models.TimeField(null=True, blank=True, verbose_name="Время остановки")
    count_days = models.IntegerField(
        null=True, blank=True, default=None,
        verbose_name="Количество дней публикации по времени"
    )
    frequency_message = models.IntegerField(default=100, verbose_name="частота между сообщениями")
    count_messages = models.IntegerField(default=0, verbose_name="счетчик сообщений")
    index_send_message = models.IntegerField(default=0, verbose_name="индекс сообщения для отправки")
    last_sent_post_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="время публикации последнего поста"
    )
    channel = models.ForeignKey(Channel, on_delete=models.CASCADE, verbose_name='канал')
    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE, verbose_name='пользователь')

    footer = models.ForeignKey(
        "Footer",
        on_delete=models.SET_NULL, verbose_name="футер",
        blank=True, null=True, default=None
    )

    is_public = models.BooleanField(default=False, verbose_name="публичное")
    related_channels = models.ManyToManyField(
        "Channel", related_name="related_channels", related_query_name="related_channels",
        through="SchedulesChannelsAssociation",
        verbose_name="Связанные каналы"
    )

    @property
    def name(self) -> str:
        if not self._name:
            end_time = self.end_time if self.end_time else "24:00"
            return f"{self.start_time}-{end_time}"
        return self._name

    def __str__(self):
        return self.name


class SchedulesChannelsAssociation(models.Model):
    class Meta:
        db_table = "schedules_channels_association"
        verbose_name = "Связь публичного расписания с каналом"
        verbose_name_plural = "Связи публичных расписаний с каналами"

    id = models.BigAutoField(primary_key=True)
    schedule = models.ForeignKey(Schedule, on_delete=models.CASCADE, verbose_name="расписание")
    channel = models.ForeignKey(Channel, on_delete=models.CASCADE, verbose_name="канал")

    count_messages = models.IntegerField(default=0, verbose_name="счетчик сообщений")
    index_send_message = models.IntegerField(default=0, verbose_name="индекс поста для отправки")
    last_sent_post_datetime = models.DateTimeField(verbose_name="время последней отправки поста", null=True, blank=True)

    status = models.CharField(max_length=20, default="active", null=True, blank=True, verbose_name="статус")


class FriendlyChatMessage(models.Model):
    class Meta:
        db_table = "friendly_chat_messages"
        verbose_name = "сообщение френдли бота в чате"
        verbose_name_plural = "сообщение френдли бота в чате"
        indexes = [
            models.Index(fields=['message_type', 'chat_id', 'datetime'], name='fcm_message_type_chat_id_dt'),
        ]

    id = models.BigAutoField(primary_key=True)
    datetime = models.DateTimeField(verbose_name='время отправки')
    message_type = models.CharField(max_length=29, default="", verbose_name="тип сообщения")
    chat_id = models.BigIntegerField(null=True, verbose_name="чат id канала")
    bot = models.ForeignKey(
        "ClientBot", on_delete=models.CASCADE, verbose_name="бот",
        db_column="bot_id"
    )
    schedule = models.ForeignKey(
        Schedule, on_delete=models.CASCADE, verbose_name="расписание",
        db_column="schedule_id", null=True, blank=True, default=None
    )
    message_id = models.BigIntegerField(null=True, verbose_name="id сообщения")
    member = models.ForeignKey(
        ChatMember, on_delete=models.CASCADE, verbose_name="участник чата",
        db_column="member_id", null=True, blank=True,
    )
    user_first_name = models.CharField(max_length=256, null=True, verbose_name="имя", blank=True, default="")


class FriendlyChatListParams(models.Model):
    class Meta:
        db_table = "friendly_chat_list_params"
        verbose_name = "параметры списка чата френдли бота"
        verbose_name_plural = "параметры списка чата френдли бота"

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey(
        "TelegramUser", to_field="id", on_delete=models.CASCADE, null=True, blank=True,
        default=None,
        verbose_name="пользователь"
    )
    bot = models.ForeignKey("ClientBot", to_field="id", on_delete=models.SET_NULL, null=True, blank=True)
    channels_mode = models.CharField(max_length=50, default="", verbose_name="режим списка калов")
    subscriptions = models.JSONField(default=dict, null=True, blank=True, verbose_name='параметры списка подписок')
    admin = models.JSONField(default=dict, null=True, blank=True, verbose_name='параметры списка админ')
    super_admin = models.JSONField(default=dict, null=True, blank=True, verbose_name='параметры списка супер админ')


class FriendlyBotAnalyticAction(models.Model):
    class Meta:
        db_table = "friendly_bot_analytic_actions"
        verbose_name = "Действие для аналитики паблишера"
        verbose_name_plural = "Действия для аналитики паблишера"

    id = models.BigAutoField(primary_key=True)
    type = models.CharField(max_length=50, verbose_name="тип действия")
    datetime = models.DateTimeField(auto_now_add=True)
    channel = models.ForeignKey(
        Channel, on_delete=models.PROTECT, verbose_name="канал",
        related_name="channel_analytic_actions", related_query_name="channel_analytic_actions",
        blank=True, null=True
    )
    member = models.ForeignKey(
        ChatMember, on_delete=models.PROTECT,
        related_name="member_analytic_actions", related_query_name="member_analytic_actions",
        blank=True, null=True, verbose_name="участник чата",
    )
    recommender = models.ForeignKey(
        ChatMember, on_delete=models.PROTECT, verbose_name="рекоммендатель",
        related_name="recommender_analytic_actions",
        related_query_name="recommender_analytic_actions", blank=True, null=True
    )
    message_content_type = models.CharField(max_length=35, verbose_name="тип сообщения", null=True, blank=True)
    filter_trigger = models.TextField(verbose_name="текст", null=True, blank=True)
    filter_triggered = models.TextField(verbose_name="запретное слово", null=True, blank=True)
    filter_type = models.TextField(verbose_name="тип фильтра", null=True, blank=True)
    reason = models.CharField(max_length=20, null=True, blank=True, verbose_name="причина")
    is_deleted = models.BooleanField(default=False, verbose_name="Удалено ли")
    is_broke_length_rule = models.BooleanField(default=False, verbose_name="нарушило ли ограничение длины")
    max_length = models.IntegerField(null=True, blank=True, verbose_name="максимально разрашённая длина сообщения")

    schedule_name = models.CharField(max_length=20, verbose_name="Название расписания", null=True, blank=True)
    schedule_type = models.CharField(max_length=20, verbose_name="тип расписания", null=True, blank=True)
    count_days = models.IntegerField(null=True, blank=True, verbose_name="количество дней")
    frequency_message = models.IntegerField(null=True, blank=True, verbose_name="частота сообщений")

    post_content_type = models.CharField(max_length=15, verbose_name="Тип контента поста", null=True, blank=True)
    post_text = models.TextField(null=True, blank=True, verbose_name="текст поста")
    post_media_path = models.CharField(max_length=512, null=True, blank=True, verbose_name="путь к медиа поста")

    footer_content_type = models.CharField(max_length=15, verbose_name="Тип контента футера", null=True, blank=True)
    footer_text = models.TextField(null=True, blank=True, verbose_name="текст футера")
    footer_media_path = models.CharField(max_length=512, null=True, blank=True, verbose_name="путь к медиа футера")
    footer_buttons = models.TextField(null=True, blank=True, verbose_name="кнопки футера")
    footer_need_concatenation = models.BooleanField(default=False, verbose_name="объединение футера с постом")

    required_resource_type = models.CharField(
        max_length=20, verbose_name="тип ресурса для подписки", null=True,
        blank=True, default=None
    )
    required_resource_id = models.BigIntegerField(
        null=True, blank=True, default=None,
        verbose_name="Id ресурса для подписки"
    )
    user_send_messages = models.IntegerField(
        null=True, blank=True, default=None,
        verbose_name="Количество сообщений пользователя без подписки"
    )

    def __str__(self):
        return f"{self.type}({self.id})"


class ClientBotStatuses(models.Model):
    class Meta:
        db_table = "bots_statuses"
        verbose_name = "Статус ботов"
        verbose_name_plural = "Статусы ботов"

    id = models.BigAutoField(primary_key=True)
    chat_id = models.BigIntegerField(null=True, unique=True, verbose_name='чат id бота')
    status = models.JSONField(default=dict, verbose_name="статус бота", null=True, blank=True)
    checked_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.chat_id} ({self.status})"


class ClientBotCheckRequests(models.Model):
    class Meta:
        db_table = "monitoring_check_requests"
        verbose_name = "Опрос статуса ботов"
        verbose_name_plural = "Опросы статусов ботов"

    id = models.BigAutoField(primary_key=True)
    chat_id = models.BigIntegerField(null=True, verbose_name='чат id бота')
    checked_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.username} ({self.checked_at})"


class InterestSetting(models.Model):
    class Meta:
        db_table = "interest_settings"
        verbose_name = "Настройка постов интересов"
        verbose_name_plural = "Настройки постов интересов"

    id = models.BigAutoField(primary_key=True)
    bot = models.ForeignKey("ClientBot", to_field="id", on_delete=models.CASCADE, verbose_name="бот")
    frequency_for_user = models.IntegerField(
        default=60 * 60 * 24,
        verbose_name="Частота отправки сообщений пользователю (в секундах)"
    )
    lifetime_post = models.IntegerField(default=60 * 60 * 2, verbose_name="Время жизни поста интересов (в секундах)")


class InterestPost(models.Model):
    class Meta:
        db_table = "interest_posts"
        verbose_name = "пост интересов"
        verbose_name_plural = "посты интересов"

    id = models.BigAutoField(primary_key=True)
    text = models.TextField(null=True, blank=True, verbose_name='текст')
    owner = models.ForeignKey("TelegramUser", to_field="id", on_delete=models.CASCADE, verbose_name="владелец")
    created_on = models.DateTimeField(auto_now_add=True, verbose_name="дата и время создания поста интересов")
    interests = models.JSONField(verbose_name="Список интересов поста")


class InterestStatistic(models.Model):
    class Meta:
        db_table = "interest_statistics"
        verbose_name = "Статистика интересов"
        verbose_name_plural = "Статистика интересов"

    id = models.BigAutoField(primary_key=True)
    bot = models.ForeignKey("ClientBot", on_delete=models.CASCADE, verbose_name="бот")
    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE, verbose_name="пользователь")
    post = models.ForeignKey(
        InterestPost, on_delete=models.SET_NULL, null=True, blank=True,
        verbose_name="пост интересов"
    )
    interest = models.CharField(max_length=256, default="", verbose_name="интерес")
    status = models.SmallIntegerField(default=0, verbose_name="статус")
    datetime = models.DateTimeField(auto_now_add=True, verbose_name="дата и время показа поста интересов пользователю")


class InlinePoll(models.Model):
    class Meta:
        db_table = "inline_polls"
        verbose_name = "Инлайн опрос MakeButtonsBot"
        verbose_name_plural = "Инлайн опросы MakeButtonsBot"

    id = models.CharField(max_length=256, primary_key=True)
    chat_type = models.CharField(max_length=10, verbose_name="тип чата")
    sender = models.ForeignKey("TelegramUser", on_delete=models.PROTECT, verbose_name="пользователь")
    original_text = models.TextField(verbose_name="Оригинальный ввод пользователя")
    datetime = models.DateTimeField(default=tz.now, verbose_name="дата создания")


class InlinePollVote(models.Model):
    class Meta:
        db_table = "inline_poll_votes"
        verbose_name = "Голос инлайн опроса"
        verbose_name_plural = "Голоса инлайн опросов"

    id = models.BigAutoField(primary_key=True)
    inline_poll = models.ForeignKey(InlinePoll, on_delete=models.CASCADE, verbose_name="опрос")
    user = models.ForeignKey("TelegramUser", on_delete=models.PROTECT)
    row_id = models.SmallIntegerField(verbose_name="ряд кнопки")
    col_id = models.SmallIntegerField(verbose_name="номер кнопки в ряду")
    datetime = models.DateTimeField(default=tz.now, verbose_name="дата голоса")


class Poll(models.Model):
    class Meta:
        db_table = "polls"
        verbose_name = "Опрос френдли ботов"
        verbose_name_plural = "Опросы френдли ботов"

    id = models.BigAutoField(primary_key=True)
    time_created = models.DateTimeField(verbose_name="Дата создания", default=tz.now)
    user = models.ForeignKey("TelegramUser", on_delete=models.PROTECT, verbose_name="автор")
    name = models.CharField(max_length=255, null=True, verbose_name="Имя")
    question = models.CharField(max_length=100, null=True, verbose_name="Текст вопроса")
    is_anonymous = models.BooleanField(default=True, verbose_name="Анонимность")
    content_type = models.CharField(max_length=255, null=True, verbose_name="Тип медиа")
    file_path = models.CharField(max_length=255, null=True, verbose_name="Путь медиа")
    is_deleted = models.BooleanField(default=False, verbose_name="Удалён")


class PollOption(models.Model):
    class Meta:
        db_table = "poll_options"
        verbose_name = "Вариант ответа опросов"
        verbose_name_plural = "Варианты ответов опросов"

    id = models.BigAutoField(primary_key=True)
    index = models.IntegerField(null=False, verbose_name="Индекс ответа")
    text = models.CharField(max_length=255, verbose_name="Текст ответа")
    is_deleted = models.BooleanField(default=False, verbose_name="Удалён")
    poll = models.ForeignKey(Poll, on_delete=models.CASCADE, verbose_name="опрос")


class PollPost(models.Model):
    class Meta:
        db_table = "poll_posts"
        verbose_name = "Опрос френдли ботов публикация"
        verbose_name_plural = "Опросы френдли ботов публикации"

    id = models.BigAutoField(primary_key=True)
    post_date = models.DateTimeField(default=tz.now, verbose_name="Дата публикации")
    poll = models.ForeignKey(Poll, on_delete=models.CASCADE, verbose_name="опрос")

    message_id = models.BigIntegerField(null=True, blank=True, verbose_name="id сообщения опроса")
    chat_id = models.BigIntegerField(null=True, blank=True, verbose_name="id чата в котором запущен опрос")


class PollAnswer(models.Model):
    class Meta:
        db_table = "poll_answers"
        verbose_name = "Ответ отпроса"
        verbose_name_plural = "Ответы опросов"

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey("TelegramUser", on_delete=models.PROTECT, verbose_name="проголосвавший пользователь")
    is_anonymous = models.BooleanField(null=False, verbose_name="Анонимность")
    option = models.ForeignKey(PollOption, on_delete=models.CASCADE, verbose_name="вариант ответа")
    poll_post = models.ForeignKey(PollPost, on_delete=models.CASCADE, verbose_name="публикация опроса")


class MailingArgs(models.Model):
    class Meta:
        db_table = "mailings_args"
        verbose_name = "Параметры рассылки"
        verbose_name_plural = "Параметры рассылок"

    id = models.BigAutoField(primary_key=True)

    is_finished = models.BooleanField(
        default=False, verbose_name="закончилась ли рассылка",
    )

    text = models.TextField(default=None, null=True, blank=True, verbose_name='текст')
    content_type = models.CharField(max_length=20, verbose_name="тип сообщения")
    file_path = models.CharField(max_length=100, null=True, blank=True, verbose_name='путь к медиа')
    keyboard = models.JSONField(null=True, blank=True, verbose_name="клавиатура")

    creator = models.ForeignKey(
        "TelegramUser", on_delete=models.CASCADE,
        verbose_name="создатель рассылки",
    )
    created_from_bot_token = models.CharField(
        max_length=256, verbose_name="токен бота, из которого создана рассылка"
    )

    sender_group = models.ForeignKey(
        "Group", on_delete=models.CASCADE,
        verbose_name="группа-отправитель"
    )

    start_count = models.IntegerField(
        default=0, verbose_name="сколько пользователей поставлено на отправку"
    )

    sent = models.IntegerField(
        default=0, verbose_name="отправлено"
    )
    turned_off = models.IntegerField(
        default=0, verbose_name="отключили рассылку"
    )
    blocked = models.IntegerField(
        default=0, verbose_name="заблокировали"
    )
    not_entered = models.IntegerField(
        default=0, verbose_name="не входили в бот"
    )
    error = models.IntegerField(
        default=0, verbose_name="ошибка отправки",
    )
    unknown = models.IntegerField(
        default=0, verbose_name="получен неизвестный результат"
    )

    bots = models.TextField(
        default="", verbose_name="боты, в которые идёт рассылка"
    )

    groups = models.TextField(
        default="", verbose_name="профили, в котоыре идёт рассылка"
    )

    created_datetime = models.DateTimeField(default=datetime.utcnow, verbose_name="Дата создания")


class MailingUser(models.Model):
    class Meta:
        db_table = "mailing_users"
        verbose_name = "Пользователь рассылки"
        verbose_name_plural = "Пользователи рассылки"

    id = models.BigAutoField(primary_key=True)
    mailing_args = models.ForeignKey(MailingArgs, on_delete=models.CASCADE, verbose_name="параметры рассылки")
    user = models.ForeignKey("TelegramUser", on_delete=models.PROTECT, verbose_name="получатель рассылки")
    bot = models.ForeignKey("ClientBot", on_delete=models.CASCADE, verbose_name="бот")
    status = models.CharField(max_length=11, default="pending", verbose_name="статус рассылки")
    created_datetime = models.DateTimeField(default=datetime.utcnow, verbose_name="Дата создания")


class CompressedLink(models.Model):
    class Meta:
        db_table = "compressed_links"
        verbose_name = "Сжатая ссылка"
        verbose_name_plural = "Сжатые ссылки"

    id = models.CharField(max_length=6, db_collation="utf8_bin", primary_key=True)
    payload = models.CharField(max_length=100, verbose_name="данные после ?start=")


class CustomField(models.Model):
    class Meta:
        db_table = "custom_fields"
        verbose_name = "Кастомные поля"
        verbose_name_plural = "Кастомное поле"
        unique_together = ("name", "group")

    id = models.BigAutoField(primary_key=True)
    is_deleted = models.BooleanField(default=False, verbose_name="удалён ли")

    name = models.CharField(max_length=25, verbose_name="имя")

    group = models.ForeignKey("Group", on_delete=models.CASCADE, verbose_name="группа")

    time_created = models.DateTimeField(
        default=tz.now,
        verbose_name="дата создания"
    )


class CustomFieldValue(models.Model):
    class Meta:
        db_table = "custom_fields_values"
        verbose_name = "Значения кастомных полей"
        verbose_name_plural = "Значение кастомного поля"

    id = models.BigAutoField(primary_key=True)

    value = models.TextField(verbose_name="значение")

    field = models.ForeignKey("CustomField", on_delete=models.CASCADE, verbose_name="поле")

    user = models.ForeignKey(
        "TelegramUser", on_delete=models.CASCADE,
        verbose_name="пользователь",
        related_name="custom_fields_values",
        related_query_name="custom_fields_values",
    )

    manager_user = models.ForeignKey(
        "TelegramUser", on_delete=models.SET_NULL,
        null=True, blank=True,
        verbose_name="менеджер, который установил значение"
    )

    vm = models.ForeignKey(
        "VirtualManager", on_delete=models.SET_NULL,
        null=True, blank=True,
        verbose_name="вм, который установил значение"
    )

    time_created = models.DateTimeField(default=tz.now)


class UserExtSettings(models.Model):
    class Meta:
        unique_together = ('brand', 'user', 'type_data',)
        db_table = 'user_ext_settings'
        verbose_name = "Дополнительные настройки пользователей"
        verbose_name_plural = "Дополнительные настройки пользователя"

    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey('TelegramUser', on_delete=models.CASCADE)
    brand = models.ForeignKey('Brand', on_delete=models.CASCADE)
    type_data = models.CharField(max_length=50)
    json_data = models.JSONField(null=True, default=None)
    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now=True)
    value_data = models.CharField(max_length=255, null=True, default=None)
    name_data = models.CharField(max_length=99, null=True, blank=True, default=None)
