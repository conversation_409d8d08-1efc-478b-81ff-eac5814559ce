from django.db import models
from django.utils import timezone


class Task(models.Model):
    class Meta:
        db_table = 'tasks'

    id = models.BigAutoField(primary_key=True)

    type = models.CharField(max_length=99)
    type_task = models.CharField(max_length=99)
    status = models.CharField(max_length=99)
    ai_model = models.CharField(max_length=99)
    prompt = models.Char<PERSON>ield(max_length=4096, null=False)
    dalle_prompt = models.CharField(max_length=1024, null=True, default=None)
    object_id = models.BigIntegerField(null=False)

    group = models.ForeignKey("Group", on_delete=models.RESTRICT, related_name='tasks')
    user = models.ForeignKey(
        "TelegramUser", on_delete=models.RESTRICT, null=True, blank=True,
        related_name='tasks'
    )

    change_date = models.DateTimeField(default=timezone.now, null=False)
    time_created = models.DateTimeField(default=timezone.now)

    is_deleted = models.<PERSON><PERSON>anField(default=False, null=False)
    json_data = models.JSONField(default=None, null=True, blank=True)

    start_date = models.DateTimeField(default=None, null=True, blank=True)
    end_date = models.DateTimeField(default=None, null=True, blank=True)
    cancel_date = models.DateTimeField(default=None, null=True, blank=True)

    error = models.CharField(max_length=1024, null=True, default=None)
