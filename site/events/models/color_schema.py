from django.db import models


class ColorSchema(models.Model):
    class Meta:
        db_table = 'color_schemas'
        verbose_name = "Цветовая схема"
        verbose_name_plural = "Цветовые схемы"

    id = models.BigAutoField(primary_key=True)
    is_active = models.BooleanField(default=True)
    group = models.ForeignKey('Group', on_delete=models.CASCADE)

    theme_mode = models.CharField(max_length=5, default='light')

    bg_color = models.CharField(max_length=22, null=True, default=None)
    secondary_bg_color = models.CharField(max_length=22, null=True, default=None)

    primary_color = models.CharField(max_length=22, null=True, default=None)
    secondary_color = models.CharField(max_length=22, null=True, default=None)
    error_color = models.CharField(max_length=22, null=True, default=None)
    warning_color = models.CharField(max_length=22, null=True, default=None)

    text_color = models.Char<PERSON>ield(max_length=22, null=True, default=None)
    font = models.Char<PERSON>ield(max_length=255, null=True, default=None)

    use_telegram_theme = models.BooleanField(default=True)
