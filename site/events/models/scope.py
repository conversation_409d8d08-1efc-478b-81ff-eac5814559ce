from django.db import models
from django.utils import timezone


class Scope(models.Model):
    class Meta:
        db_table = "scopes"

    id = models.BigAutoField(primary_key=True)

    target = models.CharField(max_length=10)

    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE, null=True, blank=True)
    user_group = models.ForeignKey("UserGroup", on_delete=models.CASCADE, null=True, blank=True)

    scope = models.CharField(max_length=50)

    profile = models.ForeignKey("Group", on_delete=models.CASCADE, null=True, blank=True)
    bot = models.ForeignKey("ClientBot", on_delete=models.CASCADE, null=True, blank=True)
    vm = models.ForeignKey("VirtualManager", on_delete=models.CASCADE, null=True, blank=True)
    store = models.ForeignKey("Store", on_delete=models.CASCADE, null=True, blank=True)
    category = models.ForeignKey("StoreCategory", on_delete=models.CASCADE, null=True, blank=True)
    product = models.ForeignKey("StoreProduct", on_delete=models.CASCADE, null=True, blank=True)
    product_group = models.ForeignKey("StoreProductGroup", on_delete=models.CASCADE, null=True, blank=True)
    attribute_group = models.ForeignKey("StoreAttributeGroup", on_delete=models.CASCADE, null=True, blank=True)
    characteristic = models.ForeignKey("StoreCharacteristic", on_delete=models.CASCADE, null=True, blank=True)
    attribute = models.ForeignKey("StoreAttribute", on_delete=models.CASCADE, null=True, blank=True)
    qr_menu = models.ForeignKey("MenuInStore", on_delete=models.CASCADE, null=True, blank=True)
    extra_fee = models.ForeignKey("ExtraFeeSettings", on_delete=models.CASCADE, null=True, blank=True)
    task = models.ForeignKey("Task", on_delete=models.CASCADE, null=True, blank=True)

    time_created = models.DateTimeField(default=timezone.now)
    expire_datetime = models.DateTimeField(null=True, blank=True)
