from django.db import models
from django.utils import timezone


class Chat(models.Model):
    class Meta:
        db_table = "chats"
        unique_together = ("type", "user", "group", "bot")

    id = models.BigAutoField(primary_key=True)
    type = models.CharField(max_length=15)

    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE)
    group = models.ForeignKey("Group", on_delete=models.CASCADE)
    bot = models.ForeignKey("ClientBot", null=True, blank=True, on_delete=models.CASCADE)

    _is_pending = models.BooleanField(default=False)
    last_pending_set_datetime = models.DateTimeField(null=True, blank=True)
    change_date = models.DateTimeField(default=timezone.now)
    time_created = models.DateTimeField(default=timezone.now)
