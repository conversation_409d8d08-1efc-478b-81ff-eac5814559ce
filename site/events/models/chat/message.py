from datetime import datetime

from django.db import models
from django.utils import timezone


class ChatMessage(models.Model):
    class Meta:
        db_table = "chat_messages"

    id = models.BigAutoField(primary_key=True)

    chat = models.ForeignKey("Chat", on_delete=models.CASCADE, related_name="messages")

    sent_by = models.CharField(max_length=7)
    sent_by_user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE, null=True, blank=True)
    vmc = models.ForeignKey("VirtualManagerChat", on_delete=models.CASCADE, null=True, blank=True)

    content_type = models.Char<PERSON>ield(max_length=9)
    text = models.TextField(null=True)
    media = models.ForeignKey("MediaObject", on_delete=models.RESTRICT, null=True, blank=True)
    content = models.JSONField(null=True)

    menu_in_store = models.ForeignKey("MenuInStore", on_delete=models.SET_NULL, null=True, blank=True)

    wa_master_template = models.Foreign<PERSON>ey(
        "WAMasterTemplate", on_delete=models.SET_NULL, null=True, blank=True
    )
    wa_template_variables = models.JSONField(null=True, blank=True)

    is_mailing = models.BooleanField(default=False)
    is_last = models.BooleanField(default=False)

    time_created: datetime = models.DateTimeField(default=timezone.now)
