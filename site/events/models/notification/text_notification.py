from django.db import models
from django.utils import timezone


class TextNotification(models.Model):
    class Meta:
        db_table = "text_notifications"

    id = models.BigAutoField(primary_key=True)
    time_created = models.DateTimeField(default=timezone.now)

    target = models.CharField(max_length=3)
    type = models.CharField(max_length=12)

    profile = models.ForeignKey("Group", on_delete=models.CASCADE)
    store = models.ForeignKey("Store", on_delete=models.SET_NULL, null=True, blank=True)
    from_user = models.ForeignKey(
        "TelegramUser", on_delete=models.SET_NULL, null=True, blank=True, related_name="text_notifications"
    )
    from_bot = models.ForeignKey("ClientBot", on_delete=models.SET_NULL, null=True, blank=True)
    menu_in_store = models.ForeignKey("MenuInStore", on_delete=models.SET_NULL, null=True, blank=True)

    text = models.TextField()

    _is_read = models.<PERSON><PERSON>anField(default=False)
    read_by_user = models.ForeignKey(
        "TelegramUser",
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="read_text_notifications",
    )
    read_status_change_datetime = models.DateTimeField(null=True, blank=True)
