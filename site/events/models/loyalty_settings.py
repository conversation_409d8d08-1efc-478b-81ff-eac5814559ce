from django.db import models


class LoyaltySettings(models.Model):
    """
    Модель для зберігання налаштувань лояльності.
    Налаштування можуть бути прив'язані до профілю, магазину або товару.
    """
    
    class Meta:
        db_table = "loyalty_settings"
        verbose_name_plural = "Налаштування лояльності"
        verbose_name = "Налаштування лояльності"
        indexes = [
            models.Index(fields=["profile"]),
            models.Index(fields=["store"]),
            models.Index(fields=["product"]),
            models.Index(fields=["invoice_template"]),
            models.Index(fields=["ewallet"]),
            models.Index(fields=["priority"]),
            models.Index(fields=["is_enabled"]),
        ]
    
    id = models.BigAutoField(primary_key=True)

    ewallet = models.ForeignKey(
        'EWallet',
        on_delete=models.SET_NULL,
        null=True, blank=True, db_index=True,
        verbose_name="Гаманець"
    )

    profile = models.ForeignKey(
        'Group',
        on_delete=models.SET_NULL,
        null=True, blank=True, db_index=True,
        verbose_name="Профіль"
    )

    brand = models.ForeignKey(
        'Brand',
        on_delete=models.SET_NULL,
        null=True, blank=True, db_index=True,
        verbose_name="Бренд"
    )
    
    store = models.ForeignKey(
        'Store',
        on_delete=models.SET_NULL,
        null=True, blank=True, db_index=True,
        verbose_name="Магазин"
    )

    invoice_template = models.ForeignKey(
        'InvoiceTemplate',
        on_delete=models.SET_NULL,
        null=True, blank=True, db_index=True,
        verbose_name="Шаблон рахунку"
    )
    
    product = models.ForeignKey(
        'StoreProduct',
        on_delete=models.SET_NULL,
        null=True, blank=True, db_index=True,
        verbose_name="Товар"
    )
    
    # Основні налаштування InCust
    white_label_id = models.CharField(
        max_length=255, 
        verbose_name="White Label ID"
    )
    
    terminal_api_key = models.CharField(
        max_length=255,
        null=True, blank=True,
        verbose_name="Terminal API Key"
    )

    server_url = models.CharField(
        max_length=255,
        verbose_name="Server URL"
    )
    
    terminal_id = models.CharField(
        max_length=255,
        verbose_name="Terminal ID",
        null=True, blank=True, default=None
    )
    
    loyalty_id = models.CharField(
        max_length=255,
        verbose_name="Loyalty ID",
        null=True, blank=True, default=None
    )

    
    # Налаштування клієнта
    type_client_auth = models.CharField(
        max_length=50, null=True, blank=True,
        verbose_name="Тип автентифікації клієнта"
    )
    
    prohibit_redeeming_bonuses = models.BooleanField(
        default=False,
        verbose_name="Заборона списання бонусів"
    )
    
    prohibit_redeeming_coupons = models.BooleanField(
        default=False,
        verbose_name="Заборона використання купонів"
    )
    
    loyalty_applicable_type = models.CharField(
        max_length=50,
        default="FOR_PARTICIPANTS",
        verbose_name="Тип застосування лояльності"
    )
    
    # Пріоритет для вибору налаштувань
    priority = models.IntegerField(
        default=0,
        verbose_name="Пріоритет"
    )
    
    # Активність налаштувань
    is_enabled = models.BooleanField(
        default=True,
        verbose_name="Активний"
    )
    
    # Назва для адмін панелі
    name = models.CharField(
        max_length=255, null=True, blank=True,
        verbose_name="Назва"
    )
    
    description = models.CharField(
        max_length=1000, null=True, blank=True,
        verbose_name="Опис"
    )
    
    # Часові мітки
    time_created = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Час створення"
    )
    
    time_updated = models.DateTimeField(
        auto_now=True,
        verbose_name="Час оновлення"
    )

    json_data = models.JSONField(verbose_name="Дані", null=True, blank=True)
