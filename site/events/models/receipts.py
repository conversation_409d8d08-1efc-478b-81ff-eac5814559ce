from django.db import models
from django.utils import timezone as tz

from .user import TelegramUser


class Receipt(models.Model):

    class Meta:

        db_table = "receipts"
        verbose_name_plural = "Чеки"
        verbose_name = "Чек"

    id = models.BigAutoField(primary_key=True)
    receipt_id = models.CharField(max_length=50, unique=True, verbose_name="номер чека")

    issue_datatime = models.DateTimeField(default=tz.now, verbose_name="время выпуска")

    time_scanned = models.DateTimeField(default=tz.now, verbose_name="время сканирования чека")
    user = models.ForeignKey(
        TelegramUser, blank=True, null=True, on_delete=models.CASCADE, verbose_name="Пользователь"
    )
    organisation = models.ForeignKey(
        "Organisation", to_field="id", on_delete=models.SET_NULL, null=True, verbose_name="организации"
    )
    pos = models.ForeignKey(
        "Pos", to_field="id", on_delete=models.SET_NULL, null=True, verbose_name="точка прдажи"
    )
    ppo = models.ForeignKey(
        "PPO", to_field="id", on_delete=models.SET_NULL, null=True, verbose_name="Кассовый аппарат(Укр)"
    )
    json_data = models.JSONField(verbose_name="Информация про чек")
    total_price = models.FloatField(default=0.0, verbose_name="цена всего")

    store_order = models.ForeignKey(
        "StoreOrder",
        on_delete=models.CASCADE,
        default=None,
        null=True,
        verbose_name="Связь с ордером магазина",
    )
    group = models.ForeignKey("Group", on_delete=models.PROTECT, verbose_name="группа", blank=True, null=True)
    incust_check = models.JSONField(verbose_name="чек инкаст", null=True, blank=True)
    incust_transaction = models.JSONField(verbose_name="транзакция инкаст", null=True, blank=True)

    def __str__(self):
        return f"Чек № {self.receipt_id}"


class Organisation(models.Model):

    class Meta:

        db_table = "organisations"
        verbose_name = "Опганизация"
        verbose_name_plural = "Организации"

    id = models.BigAutoField(primary_key=True)
    country = models.CharField(max_length=256, verbose_name="Страна")
    ico_code = models.BigIntegerField(default=None, null=True, verbose_name="ICO")
    _name = models.CharField(max_length=256, verbose_name="Имя")
    municipality = models.CharField(max_length=256, null=True, default=None, verbose_name="Муниципалитет")
    postal_code = models.IntegerField(default=None, null=True, verbose_name="Почтовый код")
    street = models.CharField(max_length=256, default=None, null=True, verbose_name="Улица")
    brand = models.CharField(max_length=256, default=None, null=True, verbose_name="Бренд")
    inn = models.CharField(max_length=256, default=None, null=True,
                           verbose_name="Податковий номер/серія та/або номер паспорта")


class Pos(models.Model):

    class Meta:

        db_table = "pos"
        verbose_name = "Точки прдаж"
        verbose_name_plural = "Точка продажи"
        unique_together = ("name", "country", "municipality", "postal_code", "street")

    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=50, default=None, null=True, verbose_name="Имя")
    country = models.CharField(max_length=50, default=None, null=True, verbose_name="Страна")
    municipality = models.CharField(max_length=50, default=None, null=True, verbose_name="Муниципалитет")
    postal_code = models.IntegerField(default=None, null=True, verbose_name="Почтовый код")
    street = models.CharField(max_length=150, default=None, null=True, verbose_name="Улица")
    cash_register_code = models.CharField(max_length=50, default=None, null=True, verbose_name="Код кассы")
    organisation = models.ForeignKey(
        "Organisation", to_field="id", on_delete=models.SET_NULL, null=True, verbose_name="организации"
    )


class ReceiptItem(models.Model):

    class Meta:

        db_table = "receipt_items"
        verbose_name = "Позиции чека"
        verbose_name_plural = "Позиция чека"

    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=256, verbose_name="Имя")
    quantity = models.FloatField(default=0.0, verbose_name="Количество")
    price = models.FloatField(default=0.0, verbose_name="Цена")
    product_code = models.CharField(max_length=256, verbose_name="Код продукта", blank=True, null=True)
    receipt = models.ForeignKey(
        "Receipt", to_field="id", on_delete=models.SET_NULL, null=True, verbose_name="чек"
    )


class ReceiptItemToTag(models.Model):

    class Meta:

        db_table = "receipt_items_to_tags"
        verbose_name = "связи позиций чека и тегов"
        verbose_name_plural = "связь позиции чека и тега"

    id = models.BigAutoField(primary_key=True)
    receipt_item = models.ForeignKey(
        "ReceiptItem", to_field="id", on_delete=models.SET_NULL, null=True, verbose_name="позиция чека"
    )
    tag = models.ForeignKey(
        "ReceiptItemTag", to_field="id", on_delete=models.SET_NULL, null=True, verbose_name="тег"
    )


class ReceiptItemTag(models.Model):

    class Meta:

        db_table = "receipt_item_tags"
        verbose_name = "теги позиций"
        verbose_name_plural = "тег позиции"

    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=256, unique=True, verbose_name="Имя")


class OrganisationToTag(models.Model):

    class Meta:

        db_table = "organisation_to_tags"
        verbose_name = "связи организаций и тегов"
        verbose_name_plural = "связь организации и тега"

    id = models.BigAutoField(primary_key=True)
    organisation = models.ForeignKey(
        "Organisation", to_field="id", on_delete=models.SET_NULL, null=True, verbose_name="Организация"
    )


class ReceiptQrPattern(models.Model):

    class Meta:

        db_table = "receipt_qr_pattern"
        verbose_name = "паттерн qr кода"
        verbose_name_plural = "паттерны qr кодов"

    id = models.BigAutoField(primary_key=True)
    pattern = models.CharField(max_length=256, verbose_name="Паттерн")
    receipt_id_re = models.CharField(max_length=256, null=True,
                                     verbose_name="Регулярное выражение для получения id чека")
    receipt_ppo_re = models.CharField(max_length=256, null=True, blank=True,
                                      verbose_name="Регулярное выражение для получения ppo чека")
    receipt_date_re = models.CharField(max_length=256, null=True, blank=True,
                                       verbose_name="Регулярное выражение для получения даты чека")
    receipt_date_format = models.CharField(max_length=256, null=True, blank=True, verbose_name="Формат даты")
    description = models.TextField(verbose_name="Описание паттерна")
    receipt_xml_pattern = models.ForeignKey(
        "ReceiptXmlPattern", to_field="id", on_delete=models.SET_NULL, null=True, blank=True, verbose_name="паттерн xml файла чека"
    )


class ReceiptXmlPattern(models.Model):

    class Meta:

        db_table = "receipt_xml_pattern"
        verbose_name = "паттерн xml файла чека"
        verbose_name_plural = "паттерны xml файлов чека"

    id = models.BigAutoField(primary_key=True)
    pattern = models.CharField(max_length=256, verbose_name="Паттерн")
    name_re = models.CharField(max_length=256, null=True, verbose_name="Регулярное выражение. Имя позиции чека")
    quantity_re = models.CharField(max_length=256, null=True, verbose_name="Регулярное выражение. Количество")
    price_re = models.CharField(max_length=256, null=True, verbose_name="Регулярное выражение. Цена позиции за шт")
    total_price = models.CharField(max_length=256, null=True, verbose_name="Регулярное выражение.Цена чека всего")


class PPO(models.Model):

    class Meta:

        db_table = "ppo"
        verbose_name = "Кассовый аппарат"
        verbose_name_plural = "Кассовые аппараты"

    id = models.BigAutoField(primary_key=True)
    ppo_id = models.CharField(max_length=256, null=True, verbose_name="Фіскальний номер РРО")
    name = models.CharField(max_length=256, null=True, verbose_name="Найменування або прізвище, ім’я та по батькові")
    inn = models.CharField(max_length=256, null=True, verbose_name="Податковий номер/серія та/або номер паспорта")
    date_of_ppo_registration = models.DateTimeField(default=tz.now, null=True, verbose_name="Дата реєстрації РРО")
    scope = models.CharField(max_length=256, null=True, verbose_name="Сфера застосування РРО")
    number_of_the_last_book_of_opo = models.CharField(max_length=256, null=True,
                                                      verbose_name="Номер останньої книги ОРО/журнала використання РРО")
    date_of_registration_of_the_last_opo_book = models.DateTimeField(
        default=tz.now,
        null=True,
        verbose_name="Дата реєстрації останньої книги ОРО/журнала використання РРО"
    )
    date_of_cancellation_of_ppo_registration = models.DateTimeField(
        default=tz.now,
        null=True,
        verbose_name="Дата скасування реєстрації РРО"
    )
    address = models.CharField(max_length=256, null=True,
                               verbose_name="Адреса господарської одиниці, де використовується РРО")


class UaApiToken(models.Model):

    class Meta:

        db_table = "ua_api_token"
        verbose_name = "Токен"
        verbose_name_plural = "Токены"

    id = models.BigAutoField(primary_key=True)
    token = models.CharField(max_length=256, null=True, verbose_name="Апи токен")
    requests_count = models.IntegerField(default=0, verbose_name="Количество использований токена")
    last_use = models.DateTimeField(default=tz.now, null=True, verbose_name="Время последнего использования")
