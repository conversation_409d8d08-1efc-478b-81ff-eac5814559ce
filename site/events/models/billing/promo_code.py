from django.db import models


class BillingPromoCode(models.Model):
    class Meta:
        db_table = "billing_promo_codes"

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=24, unique=True)

    trial_period_days = models.PositiveSmallIntegerField(default=0)
    stripe_coupon = models.CharField(max_length=255, null=True, blank=True)

    packet = models.ForeignKey(
        "BillingServicePacket",
        on_delete=models.SET_NULL,
        null=True, blank=True,
    )

    time_created = models.DateTimeField(auto_now_add=True)
