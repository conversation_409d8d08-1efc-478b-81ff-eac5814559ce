from django.db import models
from django.utils import timezone


class BillingSubscription(models.Model):
    class Meta:
        db_table = "billing_subscriptions"

    id = models.AutoField(primary_key=True)

    group = models.ForeignKey(
        "Group",
        on_delete=models.RESTRICT
    )

    currency = models.Char<PERSON>ield(max_length=3)
    status = models.Char<PERSON>ield(max_length=18)

    description = models.CharField(max_length=512, null=True, blank=True)

    stripe_id = models.CharField(max_length=255, unique=True)

    cancel_at = models.DateTimeField(null=True, blank=True)
    cancel_at_period_end = models.BooleanField()
    canceled_at = models.DateTimeField(null=True, blank=True)
    cancellation_details = models.JSONField(null=True, blank=True)

    trial_start = models.DateTimeField(null=True, blank=True)
    trial_end = models.DateTimeField(null=True, blank=True)

    stripe_created = models.DateTimeField()
    start_date = models.DateTimeField()

    current_period_start = models.DateTimeField()
    current_period_end = models.DateTimeField()

    ended_at = models.DateTimeField(null=True, blank=True)

    livemode = models.BooleanField()

    time_created = models.DateTimeField(default=timezone.now)
