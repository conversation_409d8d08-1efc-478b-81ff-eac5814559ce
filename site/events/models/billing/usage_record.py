from django.db import models
from django.utils import timezone


class BillingUsageRecord(models.Model):
    class Meta:
        db_table = "billing_usage_records"

    id = models.BigAutoField(primary_key=True)

    group = models.ForeignKey("Group", on_delete=models.RESTRICT)
    product = models.ForeignKey("BillingProduct", on_delete=models.RESTRICT)

    used_quantity = models.PositiveBigIntegerField(default=0)
    reported_quantity = models.PositiveBigIntegerField(default=0)

    last_record_datetime = models.DateTimeField(null=True, blank=True)
    last_report_datetime = models.DateTimeField(null=True, blank=True)

    time_created = models.DateTimeField(default=timezone.now)
