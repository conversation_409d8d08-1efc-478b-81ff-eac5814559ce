from django.db import models
from django.utils import timezone


class BillingProduct(models.Model):
    class Meta:
        db_table = "billing_products"

    id = models.SmallAutoField(primary_key=True)
    is_deleted = models.BooleanField(default=False)

    position = models.PositiveSmallIntegerField(default=0)

    code = models.CharField(max_length=17, unique=True)

    name = models.CharField(max_length=128)
    description = models.CharField(max_length=1042)
    tax_code = models.CharField(max_length=13)

    stripe_id = models.CharField(max_length=255, unique=True)

    time_created = models.DateTimeField(default=timezone.now)
