from django.db import models
from django.utils import timezone


class BillingSubscriptionItem(models.Model):
    class Meta:
        db_table = 'billing_subscription_items'

    id = models.AutoField(primary_key=True)
    is_deleted = models.BooleanField(default=False)

    subscription = models.ForeignKey("BillingSubscription", on_delete=models.RESTRICT)

    product = models.ForeignKey(
        "BillingProduct",
        on_delete=models.RESTRICT,
    )

    packet_item = models.ForeignKey(
        "BillingServicePacketItem",
        on_delete=models.RESTRICT
    )

    quantity = models.PositiveIntegerField(default=0)

    billing_scheme = models.CharField(max_length=8, default="PER_UNIT")
    tiers_mode = models.CharField(max_length=9, null=True, blank=True)
    tiers = models.JSONField(null=True, blank=True)

    unit_amount = models.DecimalField(max_digits=24, decimal_places=12)

    recurring_interval = models.CharField(max_length=5)
    recurring_interval_count = models.PositiveSmallInteger<PERSON>ield(default=1)

    usage_type = models.CharField(max_length=8)
    meter_id = models.CharField(max_length=255, null=True, blank=True)
    meter_event_name = models.Char<PERSON>ield(max_length=20, null=True, blank=True)

    transform_quantity = models.JSONField(null=True, blank=True)

    stripe_item_id = models.CharField(max_length=255)
    stripe_price_id = models.CharField(max_length=255)

    time_created = models.DateTimeField(default=timezone.now)
