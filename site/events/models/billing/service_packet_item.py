from django.db import models
from django.utils import timezone


class BillingServicePacketItem(models.Model):
    class Meta:
        db_table = 'billing_service_packet_items'

    id = models.SmallAutoField(primary_key=True)
    is_deleted = models.BooleanField(default=False)
    position = models.PositiveSmallIntegerField()
    packet = models.ForeignKey("BillingServicePacket", on_delete=models.RESTRICT)

    name = models.CharField(max_length=128)
    description = models.CharField(max_length=1024)

    product = models.ForeignKey(
        "BillingProduct", on_delete=models.RESTRICT
    )

    # next quantity fields only used when price.usage_tipe is licensed
    quantity = models.PositiveIntegerField(default=0)
    quantity_adjustable = models.BooleanField(default=False)
    min_quantity = models.PositiveIntegerField(default=0)
    max_quantity = models.PositiveIntegerField(default=0)

    billing_scheme = models.CharField(max_length=8, default="PER_UNIT")
    tiers_mode = models.CharField(max_length=9, null=True, blank=True)
    tiers = models.JSONField(null=True, blank=True)

    unit_amount = models.DecimalField(max_digits=24, decimal_places=12)

    usage_type = models.CharField(max_length=8)
    meter_id = models.CharField(max_length=255, null=True, blank=True)
    meter_event_name = models.CharField(max_length=20, null=True, blank=True)

    transform_quantity = models.JSONField(null=True, blank=True)

    stripe_price_id = models.CharField(max_length=255, unique=True)

    time_created = models.DateTimeField(default=timezone.now)
