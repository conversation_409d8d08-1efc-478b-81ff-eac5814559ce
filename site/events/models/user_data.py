from django.db import models


class UserData(models.Model):
    class Meta:
        db_table = "user_data"

    id = models.BigAutoField(primary_key=True)
    time_created = models.DateTimeField(auto_now_add=True)

    user = models.ForeignKey("TelegramUser", on_delete=models.CASCADE)

    type = models.CharField(max_length=255)

    target = models.CharField(max_length=5)
    group = models.ForeignKey(
        "Group", on_delete=models.CASCADE,
        null=True, blank=True,
    )

    data = models.JSONField()

    position = models.PositiveIntegerField()
