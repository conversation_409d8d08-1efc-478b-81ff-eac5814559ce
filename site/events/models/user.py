from datetime import datetime

from django.db import models
from django.utils import timezone
from timezone_field import TimeZoneField

from pay4say.settings import DEFAULT_LANG
from .. import helpers as data


class TelegramUser(
    models.Model
):  # old model name. Not used for whatsapp too. Not easy to rename

    class Meta:
        db_table = "users"
        verbose_name_plural = "Пользователи телеграмма"
        verbose_name = "Пользователь телеграмма"

    id = models.BigAutoField(primary_key=True)
    date_joined = models.DateTimeField(
        auto_now_add=True, verbose_name="дата присоединения"
    )

    chat_id = models.BigIntegerField(null=True, unique=True, verbose_name="чат id")

    wa_phone = models.CharField(
        max_length=50, null=True,
        default=None, unique=True,
        blank=True, verbose_name="номер телефону whatsapp",
    )
    wa_name = models.Char<PERSON>ield(
        max_length=256, default=None,
        null=True, blank=True,
        verbose_name="ім`я whatsapp"
    )

    username = models.CharField(
        max_length=256, unique=True, null=True, blank=True,
        verbose_name="юзернейм пользователя"
    )
    _first_name = models.CharField(
        max_length=256, null=True, verbose_name="имя", blank=True
    )
    _last_name = models.CharField(
        max_length=256, null=True, verbose_name="фамилия", blank=True
    )
    full_name = models.CharField(
        max_length=256, null=True, verbose_name="имя и фамилия", blank=True
    )

    photo = models.CharField(
        max_length=1024, null=True, blank=True, verbose_name="аватар"
    )
    telegram_photo_id = models.CharField(max_length=100, null=True, blank=True)

    _status = models.CharField(null=True, blank=True, max_length=1, default="a")
    email = models.CharField(
        null=True, blank=True, max_length=320, verbose_name="Почта"
    )
    hashed_password = models.TextField(
        null=True, blank=True, verbose_name="Пароль магазина"
    )
    is_accepted_agreement = models.BooleanField(
        default=False, verbose_name="Принял соглашение магазина"
    )
    accept_agreement_datetime = models.DateTimeField(
        null=True, blank=True, verbose_name="дата принятия соглашения магазина"
    )
    is_confirmed_email = models.BooleanField(
        null=True, blank=True, verbose_name="Подтверждённая почта"
    )

    tags = models.ManyToManyField(
        "Tag", db_table="tags_users_associations", verbose_name="метки",
        db_column="user_id", blank=True
    )

    db_timezone = TimeZoneField(verbose_name="Временная зона", null=True, blank=True)
    _lang = models.CharField(max_length=10, null=True, blank=True, verbose_name="язык")

    last_data_update_datetime = models.DateTimeField(
        verbose_name="дата и время последнего обновления данных",
        default=datetime.utcnow
    )

    need_delete_friendly_posts = models.BooleanField(
        default=True,
        verbose_name="нужно ли стирать посты из экрана при редактировании расписания"
    )
    is_guest_user = models.BooleanField(
        default=False,
        verbose_name="Зарегистрирован ли пользователь через быстрый заказ"
    )

    uuid_token = models.CharField(
        max_length=36, null=True, default=None, verbose_name="Внешний ид пользователя"
    )

    is_superadmin = models.BooleanField(default=False, verbose_name="Суперадмин")

    is_system_user = models.BooleanField(default=False)
    system_user_owned_by_profile = models.ForeignKey(
        "Group", on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="system_users",
        related_query_name="system_users",
    )

    birth_date = models.DateTimeField(
        null=True, default=None, blank=True, verbose_name="Дата рождения"
    )

    is_auth_google = models.BooleanField(
        default=False, verbose_name="Зарегистрирован через Google"
    )
    is_auth_apple = models.BooleanField(
        default=False, verbose_name="Зарегистрирован через Apple"
    )

    @property
    def lang(self) -> str:
        return self._lang if self._lang and self._lang in data.local.keys() else (
            DEFAULT_LANG)

    def __str__(self):
        name = self.username if self.username is not None else self.full_name
        return name if name is not None else ""


class ExternalLoginRequest(models.Model):
    class Meta:
        db_table = "external_login_requests"

    id = models.BigAutoField(primary_key=True)
    uuid = models.CharField(max_length=36)

    status = models.CharField(max_length=8)
    type = models.CharField(max_length=8)
    purpose = models.CharField(max_length=20)

    auth_source = models.CharField(max_length=7, null=True, blank=True)
    device_info = models.CharField(max_length=255, null=True, blank=True)

    bot = models.ForeignKey("ClientBot", on_delete=models.CASCADE)
    user = models.ForeignKey(
        TelegramUser, on_delete=models.CASCADE, null=True, blank=True
    )

    extra_data = models.JSONField(null=True, blank=True)

    lang = models.CharField(max_length=2, null=True, blank=True)

    continue_url = models.CharField(max_length=1024, null=True, blank=True)

    time_created = models.DateTimeField(default=timezone.now)


class IncustCustomer(models.Model):

    class Meta:
        db_table = "incust_customers"
        unique_together = ('user', 'brand')

    id = models.BigAutoField(primary_key=True)

    user = models.ForeignKey(TelegramUser, on_delete=models.CASCADE, null=False)
    brand = models.ForeignKey("Brand", null=False, on_delete=models.CASCADE)

    token = models.TextField(null=False)
    token_update_datetime = models.DateTimeField(null=False, default=datetime.utcnow)

    push_token = models.TextField(null=True, blank=True, default=None)
    push_token_wl_id = models.CharField(
        max_length=20, null=True, blank=True, default=None
    )
    push_token_update_datetime = models.DateTimeField(null=True, default=None)

    time_created = models.DateTimeField(null=False, default=datetime.utcnow)
