from django.db import models


class QuantitativeService(models.Model):
    class Meta:
        db_table = "quantitative_services"

    id = models.BigAutoField(primary_key=True)
    group = models.ForeignKey("Group", on_delete=models.RESTRICT)

    service_type = models.CharField(max_length=9)
    quantity = models.PositiveIntegerField(null=True, blank=True)  # null means unlimited
    used = models.PositiveIntegerField()

    expire_at = models.DateTimeField(null=True, blank=True)

    time_created = models.DateTimeField()


class QuantitativeServiceUsageLog(models.Model):
    class Meta:
        db_table = "quantitative_service_usage_logs"

    id = models.BigAutoField(primary_key=True)
    service = models.ForeignKey(QuantitativeService, on_delete=models.RESTRICT)

    service_type = models.CharField(max_length=9)
    quantity = models.PositiveIntegerField(null=True, blank=True)  # null means unlimited

    used_before = models.PositiveIntegerField()
    used = models.PositiveIntegerField()
    used_after = models.PositiveIntegerField()

    time_created = models.DateTimeField()
