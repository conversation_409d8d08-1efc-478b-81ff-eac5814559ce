from django.db import models


class ExternalSystemToken(models.Model):

    class Meta:
        db_table = "external_system_tokens"
        verbose_name = "Токен для внешних систем"
        verbose_name_plural = "Токены для внешних систем"

    id = models.BigAutoField(primary_key=True)
    external_system_type = models.CharField(max_length=20, verbose_name="название внешней системы")
    expire = models.DateTimeField(null=True, blank=True, verbose_name="срок действия")
