from django.db import models
from django.utils import timezone


class Mailing(models.Model):
    class Meta:
        db_table = 'mailings'

    id = models.BigAutoField(primary_key=True)
    status = models.CharField(max_length=8, null=False, default="PENDING")

    group = models.ForeignKey("Group", on_delete=models.CASCADE)

    channels = models.JSONField(default=None, null=True, blank=True)
    last_sent_datetime = models.DateTimeField(default=None, null=True, blank=True)
    description = models.CharField(max_length=512, null=True, blank=True)
    name = models.CharField(max_length=255)
    sent_info = models.JSONField(default=None, null=True, blank=True)
    message_info = models.JSONField()
    filters = models.JSONField(default=None, null=True, blank=True)
    channels_settings = models.JSONField(default=None, null=True, blank=True)
    preferred_channel = models.CharField(max_length=6, null=True, blank=True, default=None)

    is_test = models.<PERSON>olean<PERSON>ield(default=False)
    message_source_text = models.JSONField(default=None, null=True, blank=True)

    media_type = models.CharField(max_length=8, null=False, default="TEXT")
    media = models.ForeignKey("MediaObject", on_delete=models.RESTRICT, null=True, default=None, blank=True)

    email_media_as_attachment = models.BooleanField(default=True)


class MailingMessage(models.Model):
    class Meta:
        db_table = 'mailing_messages'

    id = models.BigAutoField(primary_key=True)
    created_datetime = models.DateTimeField(default=timezone.now, null=False)
    start_datetime = models.DateTimeField(default=None, null=True, blank=True)
    end_datetime = models.DateTimeField(default=None, null=True, blank=True)
    status = models.CharField(max_length=9, null=False, default="CREATED")

    mailing = models.ForeignKey("Mailing", on_delete=models.CASCADE)
    bot = models.ForeignKey("ClientBot", on_delete=models.CASCADE, default=None, null=True, blank=True)
    user = models.ForeignKey(
        "TelegramUser",
        on_delete=models.CASCADE,
        verbose_name="Пользователь"
    )
    email = models.CharField(max_length=255, null=True, blank=True)
    chat_id = models.BigIntegerField(null=True, blank=True)
    phone = models.CharField(max_length=255, null=True, blank=True)
    user_name = models.CharField(max_length=255, null=True, blank=True)

    message = models.JSONField(default=None, null=True, blank=True)
    error_details = models.JSONField(default=None, null=True, blank=True)
    retry_info = models.JSONField(default=None, null=True, blank=True)

    channel_name = models.CharField(max_length=255)
    channel_type = models.CharField(max_length=6, null=False)

    lang = models.CharField(max_length=10, null=True, blank=True, verbose_name="язык")
