from django.db import models
from django.utils import timezone


class CustomMenuButton(models.Model):
    class Meta:
        db_table = "custom_menu_buttons"

    id = models.BigAutoField(primary_key=True)
    time_created = models.DateTimeField(default=timezone.now)

    bot = models.ForeignKey("ClientBot", on_delete=models.CASCADE)
    action_type = models.CharField(max_length=2)

    text = models.CharField(max_length=100)

    vm = models.ForeignKey(
        "VirtualManager", on_delete=models.CASCADE, null=True, blank=True
    )

    position = models.PositiveSmallIntegerField()
