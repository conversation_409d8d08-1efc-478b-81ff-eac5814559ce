from django.db import models
from django.utils import timezone
from timezone_field import TimeZoneField

from pay4say.settings import DEFAULT_LANG, TIME_ZONE, USE_LOCALISATION


class Group(models.Model):

    class Meta:
        db_table = "groups"
        verbose_name_plural = "Группы"

    id = models.BigAutoField(primary_key=True)
    status = models.CharField(max_length=10, default="enabled", verbose_name="статус")

    is_accepted_agreement = models.BooleanField(default=False)
    accept_agreement_datetime = models.DateTimeField(
        null=True, blank=True, default=None
    )

    created_from_bot = models.ForeignKey(
        "ClientBot", on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="created_groups", related_query_name="created_groups",
        verbose_name="создано из какого бота(пусто, если создано не из клиентского "
                     "бота)"
    )

    name = models.CharField(max_length=256, verbose_name="имя")
    location = models.CharField(
        max_length=256, default="WWW", verbose_name="название локации"
    )

    owner = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT, verbose_name="владелец",
        related_name="own_groups", related_query_name="own_groups",
    )
    creator = models.ForeignKey(
        "TelegramUser", on_delete=models.PROTECT, verbose_name="Создатель",
        related_name="creator_groups", related_query_name="creator_groups",
    )

    _menu = models.CharField(max_length=128, null=True, verbose_name="меню", blank=True)
    _menu_gallery = models.JSONField(blank=True, null=True, verbose_name="меню галерея")

    work_time_details = models.JSONField(
        blank=True, null=True, verbose_name="Детальное время работы по дням недели"
    )
    opening_time = models.TimeField(
        null=True, blank=True, verbose_name="время открытия"
    )
    closing_time = models.TimeField(
        null=True, blank=True, verbose_name="время закрытия"
    )

    review_type = models.CharField(
        max_length=20, null=True, verbose_name="тип отзывов", blank=True
    )

    photo = models.CharField(max_length=512, null=True, blank=True, verbose_name="фото")
    coordinates = models.CharField(
        max_length=100, null=True, blank=True, verbose_name="географические координаты"
    )
    address = models.TextField(
        null=True, verbose_name="адрес(как добраться)", blank=True
    )
    links = models.TextField(
        null=True, blank=True, verbose_name="Ссылки и социальные сети"
    )
    contacts = models.TextField(null=True, blank=True, verbose_name="контакты")
    about = models.TextField(null=True, blank=True, verbose_name="о нас(себе)")

    gallery = models.JSONField(blank=True, null=True, verbose_name="Галерея")
    time_created = models.DateTimeField(auto_now_add=True, verbose_name="дата создания")
    log_chat_id = models.BigIntegerField(
        null=True, blank=True, unique=True,
        verbose_name="чат id упрощенного управления",
    )
    log_chat_name = models.CharField(
        max_length=256, null=True, blank=True,
        verbose_name="имя чата упрощенного управления",
    )

    is_from_parsing = models.BooleanField(
        default=False, verbose_name="Создан автоматически из парсера"
    )
    last_parsed_datetime = models.DateTimeField(
        verbose_name="дата и время последнего парсинга данных",
        null=True, blank=True,
    )

    new_order_notification = models.TextField(
        default=USE_LOCALISATION,
        verbose_name="уведомление о новом заказе"
    )
    details_added_order_notification = models.TextField(
        default=USE_LOCALISATION,
        verbose_name="уведомление о деталях к заказу"
    )
    user_shows_reserve_order_notification = models.TextField(
        default=USE_LOCALISATION,
        verbose_name="уведомление о показе брони пользователем"
    )
    confirmed_order_notification = models.TextField(
        default=USE_LOCALISATION,
        verbose_name="уведомление о подтверждении заказа"
    )
    payed_order_notification = models.TextField(
        default=USE_LOCALISATION,
        verbose_name="уведомление об оплаченом заказе"
    )
    canceled_order_notification = models.TextField(
        default=USE_LOCALISATION,
        verbose_name="уведомление ою отменённом заказе"
    )
    closed_order_notification = models.TextField(
        default=USE_LOCALISATION,
        verbose_name="уведомление о закрытом заказе"
    )

    from_user_message_notification = models.TextField(
        default=USE_LOCALISATION,
        verbose_name="уведомление о новом сообщении от пользователя"
    )
    to_user_message_notification = models.TextField(
        default=USE_LOCALISATION,
        verbose_name="уведомление о новом сообщении к пользователю"
    )

    api_token = models.CharField(
        max_length=36,
        null=True, blank=True,
        verbose_name="токен для апи"
    )

    _lang = models.CharField(max_length=10, default=DEFAULT_LANG, verbose_name="язык")
    _langs_list = models.JSONField(
        null=True, blank=True, verbose_name="список доступных языков"
    )
    force_use_lang = models.CharField(
        max_length=2, null=True, blank=True,
        verbose_name="принудительно использовать язык",
    )

    is_translate = models.BooleanField(default=False)
    allow_all_google_langs = models.BooleanField(default=True)

    country = models.CharField(
        max_length=256, null=True, blank=True,
        verbose_name="Страна(помогает в распознавании пола пользователя)"
    )
    country_code = models.CharField(
        max_length=2, null=True, blank=True,
        verbose_name="Код країни"
    )
    currency = models.CharField(
        max_length=3, null=False,
        verbose_name="Валюта"
    )

    email = models.CharField(
        max_length=256, null=True, blank=True,
        verbose_name="Електронна пошта"
    )
    phone = models.CharField(
        max_length=50, null=True, blank=True,
        verbose_name="Номер телефону"
    )
    last_contact_information_asked_datetime = models.DateTimeField(
        null=True, blank=True,
        verbose_name="Коли останній раз запитувалась контактна інформація"
    )

    timezone = TimeZoneField(default=TIME_ZONE, verbose_name="Временная зона")

    last_orders_export_sheet_id = models.CharField(
        max_length=255,
        blank=True, null=True, default=None,
        verbose_name="ID google sheets"
    )

    last_translator_limit_notification = models.DateTimeField(null=True, blank=True)

    is_friends = models.BooleanField(
        default=False, verbose_name='Is profile has friends feature'
    )

    is_ask_about_birthday = models.BooleanField(
        default=False, verbose_name='Is ask client about birthday'
    )

    is_onboard_guide_completed = models.BooleanField(
        default=False, verbose_name='Is onboard guide completed'
    )

    config = models.JSONField(
        blank=True, null=True, verbose_name="Конфигурация профиля"
    )

    image_task = models.ForeignKey(
        'Task',
        on_delete=models.RESTRICT,
        null=True,
        blank=True,
        related_name='image_task_groups'
    )
    logo_task = models.ForeignKey(
        'Task',
        on_delete=models.RESTRICT,
        null=True,
        blank=True,
        related_name='logo_task_groups'
    )

    stripe_customer_id = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return f"{self.name}" if self.name is not None else ""


class GroupTag(models.Model):
    class Meta:
        db_table = "group_tags"

    id = models.BigAutoField(primary_key=True)
    tag_name = models.CharField(max_length=20, unique=True)
    groups = models.ManyToManyField(
        Group,
        through="GroupTagToGroup",
        related_name="group_tags",
        related_query_name="group_tags",
        blank=True,
    )
    time_created = models.DateTimeField(default=timezone.now)


class GroupTagToGroup(models.Model):
    class Meta:
        db_table = "group_tags_to_groups"

    group = models.ForeignKey(Group, on_delete=models.CASCADE)
    group_tag = models.ForeignKey(GroupTag, on_delete=models.CASCADE)


class GroupVerificationDocument(models.Model):
    class Meta:
        db_table = "group_verification_documents"

    id = models.BigAutoField(primary_key=True)
    group = models.ForeignKey("Group", on_delete=models.RESTRICT)
    media = models.ForeignKey("MediaObject", on_delete=models.RESTRICT)
    comment = models.CharField(max_length=512, null=True, blank=True)

    time_created = models.DateTimeField(default=timezone.now)
