import asyncio
import json
import time

import gspread
from aiogram import <PERSON><PERSON>
from oauth2client.service_account import ServiceAccountCredentials

from pay4say.settings import (
    DEFAULT_LANG, PATH_TO_GOOGLE_CREDS_JSON, PATH_TO_LOCAL_DATA, PLATFORM_ADMINS,
    ROOT_BOT_API_TOKEN, TEXT_VARIABLES,
)

scope = ["https://spreadsheets.google.com/feeds",
         "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_name(
    PATH_TO_GOOGLE_CREDS_JSON, scope
)
client = gspread.authorize(creds)


async def send_message_to_platform_admins(message_text: str):
    bot = Bot(ROOT_BOT_API_TOKEN)
    sent_messages = list()
    try:
        for chat_id, status in PLATFORM_ADMINS.items():
            try:
                if status:
                    sent_messages.append(await bot.send_message(chat_id, message_text))
            except:
                pass
    except:
        pass
    finally:
        try:
            await bot.session.close()
        except:
            pass
        finally:
            return sent_messages


def sync_send_message_to_platform_admins(text: str):

    asyncio.ensure_future(send_message_to_platform_admins(text))


local = {}

open(PATH_TO_LOCAL_DATA, "w", encoding="utf-8").close()


def f(
        variable: str, lang: str = DEFAULT_LANG, find_in_langs: bool = True, **kwargs
) -> str:
    variable = variable.upper().strip().replace(" ", "_")
    try:
        text = local.get(lang, local.get("DEFAULT_LANG", dict())).get(variable).replace(
            "\\n", "\n"
        )
    except:
        text = None
    else:
        for key, value in kwargs.items():
            text = text.replace("{" + key + "}", str(value))
    if text:
        text = text.rstrip()
    if not text and find_in_langs:
        for new_lang in local.keys():
            text = f(variable, new_lang, find_in_langs=False, **kwargs)
            if text:
                break
        else:
            text = local.get(lang, local.get("DEFAULT_LANG", dict())).get(
                "LOCALISATION_ERROR"
            )
            sync_send_message_to_platform_admins(
                f"Не найдена переменная <code>{variable}</code>"
            )
    return text


def update_local_data():
    global local
    local.clear()
    while True:
        try:
            sheet = client.open_by_key(TEXT_VARIABLES).get_worksheet(0)
            all_values = sheet.get_all_values()
            all_langs = all_values[0][1:]
            data = all_values[1:]
            langs = list()
            ignored_indexes = list()
            for i, lang in enumerate(all_langs):
                if not lang or "ignore" in lang.lower():
                    ignored_indexes.append(i)
                else:
                    langs.append(lang)
        except:
            time.sleep(10)
            pass
        else:
            break
    for lang in langs:
        local[lang] = dict()
    for line in data:
        variable = line[0]
        texts = line[1:]
        for i, lang in enumerate(all_langs):
            if i in ignored_indexes:
                continue
            text = texts[i]
            if text:
                local[lang][variable] = texts[i]

    with open(PATH_TO_LOCAL_DATA, "w", encoding="utf-8") as file:
        json.dump(local, file)


def load_local_data():
    global local
    local.clear()
    try:
        with open(PATH_TO_LOCAL_DATA, "r", encoding="utf-8") as file:
            local = json.load(file)
    except:
        update_local_data()


def c(mode: str, **kwargs) -> str:
    callback_data = f"{mode}:"
    if "_mode" in kwargs:
        kwargs["mode"] = kwargs["_mode"]
        del kwargs["_mode"]
    for k, v in kwargs.items():
        try:
            callback_data += f"{k}={v}&"
        except:
            pass
    return callback_data


load_local_data()
