# MCP Servers Knowledge Base

## Status 2025-07-11

### Issues discovered during diagnostics:

1. **Memory MCP server (cf-memory-mcp)**
   - **Status**: Not working
   - **Причина**: Відсутня змінна оточення `CF_MEMORY_API_KEY`
   - **Помилка**: "MCP error -32000: Connection closed"
   - **Package**: Встановлений через npx, працює
   - **Command**: `npx cf-memory-mcp`

2. **MySQL MCP server (mysql-mcp)**
   - **Status**: Not working
   - **Причина**: Неправильні налаштування підключення до БД
   - **Помилка**: "Access denied for user 'root'@'**********' (using password: NO)"
   - **Config settings**: Правильн<PERSON> (host: *************, user: payforsay, pass: PaY4SaY, db: payforsay)
   - **Проблема**: Сервер ігнорує параметри з конфігу

### MCP servers configuration:
**File**: `/home/<USER>/.config/claude-code/claude_desktop_config.json`
```json
{
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["cf-memory-mcp"]
    },
    "pay4say-mysql": {
      "command": "npx", 
      "args": [
        "mysql-mcp",
        "--host", "*************",
        "--user", "payforsay",
        "--password", "PaY4SaY",
        "--database", "payforsay"
      ]
    }
  }
}
```

### Environment:
- **npx**: `/home/<USER>/.nvm/versions/node/v21.7.3/bin/npx` (працює)
- **Node.js**: v21.7.3
- **Claude Code**: v1.0.48+

### Необхідні дії для виправлення:

1. **Для memory сервера**:
   - Отримати API ключ CF Memory
   - Встановити змінну `CF_MEMORY_API_KEY` в оточення
   - Команда: `export CF_MEMORY_API_KEY="your_api_key_here"`

2. **Для mysql сервера**:
   - Перевірити чому ігноруються параметри --host, --user, --password, --database
   - Можливо потрібно використовувати змінні оточення замість аргументів
   - Альтернативно - знайти інший MySQL MCP сервер

### База даних проекту Pay4Say:
- **Host**: *************
- **User**: payforsay  
- **Password**: PaY4SaY
- **Database**: payforsay
- **Local access**: `mysql -hlocalhost` (з сервера)

### Project context:
- **Project**: Pay4Say - FastAPI + Telegram bots + WhatsApp bot
- **Main folder**: `/home/<USER>/pay4say`
- **Virtual env**: `/home/<USER>/pay4say/.venv/`
- **Environment**: WSL (Ubuntu)

### Installation history:
Користувач встановлював MCP сервери в попередній сесії. Конфігурація була створена правильно, але сервери не запускаються через проблеми з автентифікацією/підключенням.

### Debug log:
```
[ERROR] MCP server "memory" Connection failed: MCP error -32000: Connection closed
[ERROR] MCP server "pay4say-mysql" Connection failed: MCP error -32000: Connection closed
```

### Diagnostic commands:
- `claude --debug` - запуск з відладкою
- `/mcp` - показує "No MCP servers configured" 
- `npx cf-memory-mcp --help` - перевірка пакету memory
- `npx mysql-mcp --help` - перевірка пакету mysql (показує помилку підключення)