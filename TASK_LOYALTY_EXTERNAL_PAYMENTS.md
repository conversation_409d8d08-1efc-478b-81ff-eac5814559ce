# Задача: Додавання лояльності до зовнішніх EWallet оплат

**Дата**: 15 серпня 2025
**Пріоритет**: Високий
**Статус**: В розробці

## Бізнес-задача

Необхідно реалізувати програму лояльності для зовнішніх оплат через EWallet (QR-код платежі Wave/Orange в Сенегалі), що дозволить давати знижки різним покупцям, використовувати купони та бонуси.

## Контекст

У системі вже є зовнішні платежі через QR-коди (`EWalletExternalPayment`), але вони працюють без лояльності. Потрібно додати можливість застосовувати правила лояльності конкретних мерчантів або дефолтну лояльність EWallet.

## Технічне рішення

### 1. Ідентифікація мерчантів

#### 1.1 Таблиця мерчантів для зовнішніх платежів
- Створити таблицю для зберігання мерчантів зовнішніх платежів
- Поля:
  - QR-коди які ідентифікують мерчанта 
  - Код товару для цього мерчанта (для передачі в InCust)
  - Категорія товару
  - Налаштування лояльності (або посилання на лояльність конкретного мерчанта)

#### 1.2 Логіка розпізнавання
- **Відомий мерчант**: Якщо QR-код знайдений в таблиці → використовуємо лояльність мерчанта + його код товару
- **Невідомий мерчант**: Використовуємо дефолтну лояльність EWallet + стандартний код товару ("external_payment" + ID + валюта)

### 2. Лояльність на рівні EWallet

#### 2.1 Паралельна лояльність
- Лояльність EWallet працює **паралельно** до лояльності замовлення/FastPay
- У одному замовленні можуть бути дві лояльності:
  - Лояльність мерчанта (на рівні замовлення)
  - Лояльність EWallet (на рівні провайдера оплати)

#### 2.2 Процес обробки
1. **Замовлення**: Спочатку застосовуються правила лояльності мерчанта
2. **EWallet оплата**: Потім застосовуються правила лояльності EWallet
3. **Фінальна сума**: В замовленні зберігається сума до знижки EWallet, в платежі - сума з урахуванням знижки EWallet

#### 2.3 Відображення для користувача
- В інтерфейсі вибору оплати показувати "EWallet -20%" (приклад)
- Після вибору EWallet показувати додатковий інтерфейс для застосування купонів EWallet
- Купони та бонуси EWallet застосовуються окремо від купонів мерчанта

### 3. Інтеграція з існуючими методами оплати

#### 3.1 API інтеграція (для сторонніх магазинів)
- Додати опціональний параметр `merchant_code` в API оплати
- Альтернатива: Налаштування в методі оплати в профілі (прописувати Merchant ID)

#### 3.2 FastPay та замовлення
- Додати опціональне поле Merchant ID в налаштування методу оплати
- Логіка розпізнавання мерчанта аналогічна зовнішнім платежам

### 4. Обмеження першого етапу

#### 4.1 Що НЕ реалізовуємо зараз
- **Нагороди** (rewards) - не видаємо купони/бонуси від лояльності EWallet
- **Використання бонусів** - тільки знижки від правил лояльності
- **Використання купонів** - можна залишити на другий етап для спрощення

#### 4.2 Спрощена реалізація
- Тільки знижки які приходять від InCust правил лояльності
- Використовувати "Charge Only Rules" в InCust (не нараховує нагороди)
- Фокус на коректній обробці знижок без ускладнень з купонами

### 5. Технічна реалізація

#### 5.1 Код товару для розрізнення типів оплат
- **Зовнішні платежі**: `external_payment_[id]_[currency]`
- **FastPay**: свій формат коду
- **Utility Bills**: свій формат коду  
- **Мерчанти з власним кодом**: код з налаштувань мерчанта

#### 5.2 Категорії для групування
- Всім мерчантам можна задати однакову категорію "external_payment"
- Можливість розрізняти по категорії різні типи оплат
- Підтримка як коду товару так і категорії в налаштуваннях мерчанта

### 6. Етапи впровадження

#### Етап 1: Базова функціональність
1. Створити таблицю мерчантів для зовнішніх платежів
2. Реалізувати логіку розпізнавання відомих/невідомих мерчантів
3. Інтегрувати InCust process_check для зовнішніх платежів
4. Тестування з простими правилами знижок

#### Етап 2: Розширена функціональність
1. Додати інтерфейс застосування купонів EWallet
2. Інтеграція з API та FastPay (параметр merchant_code)
3. Налаштування в адмін панелі для управління мерчантами

#### Етап 3: Повна функціональність
1. Підтримка використання бонусів та купонів
2. Система нагород від EWallet лояльності
3. Розширена аналітика та звітність

## Очікувані результати

1. **Для користувачів**: Можливість отримувати знижки при оплаті через EWallet
2. **Для мерчантів**: Власні програми лояльності для зовнішніх платежів  
3. **Для платформи**: Додатковий інструмент утримання користувачів через систему знижок

## Ризики та обмеження

1. **Складність інтерфейсу**: Два рівні лояльності можуть заплутати користувачів
2. **Продуктивність**: Додаткові API запити до InCust при кожній оплаті
3. **Підтримка**: Необхідність синхронізації правил між двома системами лояльності

## Зв'язані документи

- `EXTERNAL_PAYMENTS_FLOW.md` - Документація поточної системи зовнішніх платежів
- Конфігурація InCust API в `pay4say/bot/ADDITIONAL_CLAUDE_INSTRUCTIONS.md`