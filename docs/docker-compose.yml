name: pay4say
version: "3.8"
services:
  db:
    container_name: db-pay4say
    image: mysql:8
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./my.cnf:/etc/mysql/conf.d/my.cnf # config mysql server
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
  phpmyadmin:
    container_name: myadmin-pay4say
    image: phpmyadmin/phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_ARBITRARY: 1
      PMA_HOST: db
      PMA_USER: ${MYSQL_USER}
      PMA_PASSWORD: ${MYSQL_PASSWORD}
    depends_on:
      - db

  redis-stack:
    container_name: redis-pay4say
    image: redis:5.0.7
    ports:
      - "6379:6379"
  kafka:
    image: 'bitnami/kafka:3.6.2'
    hostname: kafka
    container_name: kafka-pay4say
    ports:
      - "9092:9092"
      - "9094:9094"
    #restart: unless-stopped
    environment:
      - KAFKA_CFG_NODE_ID=0
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093,EXTERNAL://:9094
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://:9092,EXTERNAL://127.0.0.1:9094
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,EXTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka:9093
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
    volumes:
      - kafka-opt:/opt
      - kafka-data:/bitnami/kafka/data

volumes:
  mysql-data:
  kafka-opt:
  kafka-data:

