aiohttp_cors
aiogram==2.23.1
aiogram-media-group==0.2.0
aioredis
cryptography
Django==3.2
django-ajax-selects
django-sendfile2
django-timezone-field
djangorestframework==3.13.1
drf-yasg
gspread
nameparser
oauth2client
Pillow
PyMySQL
python-dateutil
pytz==2022.1
qrcode
requests
SQLAlchemy==1.4.46
sqlalchemy-json
Unidecode
opencv-python
pyrogram
tgcrypto
xmltodict
beautifulsoup4
geopy
shapely
pyproj
pydantic==1.10.11
psutils[google_sheets, fastapi, bot, translator, country, openai] @ git+ssh://*****************/digsee/psutils.git
aiowhatsapp[redis] @ git+ssh://*****************/digsee/payforsay/aiowhatsapp.git
email-validator
fastapi
uvicorn
google-cloud-translate==3.8.3
google-auth==2.38.0
google-auth-oauthlib==1.2.2
aiofiles
passlib
pandas
openpyxl
async-property
python-multipart==0.0.5
chardet
lxml
itsdangerous
base32_crockford
mpu
pycountry
numbers-parser
transliterate
stripe==11.1.1
pytest
pytest-asyncio
openai==1.37.2
tiktoken
websockets
uvicorn[standard]
gunicorn
setproctitle
imgkit
jinja2==3.1.2
htmlmin
aiogoogle
enum_tools
dateparser
async-firebase==4.0.0
aiokafka
pycryptodome
google-cloud-api-keys==0.5.17

# for aimenu
google-cloud-vision
pillow==10.3.0
json-repair
poppler-utils
pdf2image
pillow_heif
sse-starlette

# Testing libraries
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0

xhtml2pdf
APScheduler==3.11.0
aioshutil

# openapi-python-client
# incust_client_api_client @ git+ssh://*****************/digsee/payforsay/in-cust-api-client.git
# incust_terminal_api_client @ git+ssh://*****************/digsee/payforsay/in-cust-terminal-api-client.git
# incust_user_api_client @ git+ssh://*****************/digsee/payforsay/in-cust-user-api-client.git

incust-api @ git+ssh://*****************/digsee/payforsay/incust-api.git
