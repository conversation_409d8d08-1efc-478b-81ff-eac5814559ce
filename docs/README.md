## Інструкція по запуску ботів та DJANGO адмінки.

---

## Спершу прохання ознайомитись з документов

https://docs.google.com/document/d/1dWnU3Wu0psbEHjGSp39Kusey8m4jifJq47eu87Yn-AQ/edit?usp=sharing

---

## Технічні вимоги:

Ubuntu | MacOS | WSL 2

python3.10

mysql(ver8)

---

## Терміни

SUPER_BOT = ROOT_BOT

---

## Встановлення python3 та pip

```bash
sudo apt update
sudo apt install software-properties-common
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt install python3.10
sudo apt-get install python3-setuptools
sudo apt install python3-pip
```

---

## Налаштування віртуального оточення

```bash
apt install python3.10-venv
python3.10 -m pip install virtualenvwrapper
mkvirtualenv -p python3.10 payforsay
```

## Встановлення модулів python

```bash
sudo workon payforsay
sudo pip install -r docs/requirements.txt
```

---

## Встановлення та налаштування mysql

```bash
sudo apt install mysql-server
sudo mysql_secure_installation
```

Далі будуть питання по налаштуванню mysql.

**!!!ВАЖЛИВО!!!**

пароль треба ввести руками, скопіювати та вставити не можна. (Під час вводу символи не показуються)
надалі усе пропускаємо [ENTER]

## налаштування mysql

После установки mysql:

```bash
sudo nano /etc/mysql/my.cnf
```

у кінець файлу вставити:

```bash
[mysqld]
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
```

```bash
ctrl+s ctrl+x
```

або

```bash
cmd+x
```

Перезапуск mysql:

```bash
sudo systemctl restart mysql
```

---

## Встановлення redis

```bash
sudo apt install redis-server
```

---

## Створення БД

```bash
sudo mysql -u root -p
CREATE DATABASE payforsay;
CREATE USER 'payforsay'@'localhost' IDENTIFIED BY 'PASSWORD';
GRANT ALL PRIVILEGES ON payforsay.* to "payforsay"@"localhost";
exit;
```

---

## Налаштування Django

на основі файлу pay4say/pay4say/settings_template.py треба створити файл pay4say/pay4say/settings.py
та у змінній `DATABASES["DEFAULT"]` замінити такі параметри

'USER' - 'payforsay'

'PASSWORD' - 'PASSWORD'

'NAME' - 'payforsay'

---

## Налаштування ботів

на основі файлу functions/config/prod_config.py створити
functions/config/local_config.py та замінити такі змінні

1. У змінні SERVICE_BOT_API_TOKEN та ROOT_BOT_API_TOKEN треба вставити токени ботів
2. Також у змінні SERVICE_BOT_USERNAME, ROOT_BOT_USERNAME треба вставити username ботів
3. Змінна SUPER_BOT_PASSWORD треба ввести пароль для ROOT_BOT
4. DEFAULT_PAY4SAY_BOT_USERNAME - імʼя Pay4Say бота за замовчуванням(можна заповнити потім)
5. USER - 'payforsay'
6. PASSWD - 'PaY4SaY'
7. DATABASE - 'payforsay'
8. PYTHON — /path_to_virtualenv/python
9. P4S_API_URL — API url
10. PLATFORM_ADMINS — додати ключом свій chat_id і значенням true
11. WEBHOOK_HOST — host webhook. Для MacOS — host.docker.internal, інакше localhost
12. WEB_APP_HOST — host для web_app
13. WEBSERVER_HOST — 0.0.0.0
14. CROCKFORD_SECRET — згенерувати унікальне число

---

## ЗАПУСК

**!!!ВАЖЛИВО!!!** Джанго треба запустити першим. інакше джанго міграції нормально працювати не будуть

---

## Запуск DJANGO адмінки та міграцій

міграції та superuser DJANGO

```
cd <шлях до проекту>/site
python manage.py makemigrations
python manage.py migrate
python manage.py createsuperuser
```

Тепер треба створити FULLTEXT index для таблиці користувачів
```
mysql
ALTER TABLE users ADD FULLTEXT idx_fulltext_search (username, _first_name, _last_name, full_name, email, wa_name);
```

## Для Ubuntu

```bash
cd /etc/systemd/system
```

створюємо django.service

```bash
sudo nano django.service
```

Записуємо наступне:

```bash
[Unit]
Description=Django site 'Django pay4say'
After=syslog.target
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/шлях до папки з проектом/pay4say/site
ExecStart=/шлях до віртуального середовища/bin/python /шлях до папки з проектом/pay4say/site/manage.py runserver 0:80

RestartSec=10
Restart=always

[Install]
WantedBy=multi-user.target
```

зберігаємо та виходимо

```bash
sudo systemctl daemon-reload
sudo systemctl enable django
sudo systemctl start django
```

## Для MacOS

просто запускаємо у терміналі чи редакторі коду

---

## Запуск web-hooks

1. Встановлюємо докер

```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
```

2. Отримуємо app id та app hash:

https://core.telegram.org/api/obtaining_api_id

3. Скачуємо образ:

```bash
docker run -d -p 8081:8081 --network host --name=telegram-bot-api --restart=always -v /var/lib/telegram-bot-api:/var/lib/telegram-bot-api -e TELEGRAM_API_ID=<id> -e TELEGRAM_API_HASH=<hash> -e TELEGRAM_LOCAL=1 aiogram/telegram-bot-api:latest --platform=linux/amd64
```

---

## Запуск ботів Ubuntu

```bash
cd /etc/systemd/system
```

створюмо файл superbot.service

```bash
sudo nano superbot.service
```

записуємо наступне:

```
[Unit]
Description=Telegram bot 'Super-bot pay4say'
After=syslog.target
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/путь к папке склонированы с гита/pay4say_bot
ExecStart=/root/Env/payforsay/bin/python /путь к папке склонированы с гита/pay4say_bot/superbot_main.py

RestartSec=10
Restart=always

[Install]
WantedBy=multi-user.target
```

далее жмём ctrl + s, потом ctrl + x

---

# запускаємо супербота

```bash
sudo systemctl daemon-reload
sudo systemctl enable superbot
sudo systemctl start superbot
```

---

зупинка/запуск/перезапуск супербота

```bash
sudo systemctl stop superbot
sudo systemctl start superbot
sudo systemctl restart superbot
```

---

## Запуск whatsapp bots

Whatsapp bot можна запустити не відразу.

## Запуск інших ботов

Усі боти запустяться разом з рут ботом.
У рут боті є команди для управління ботами
`/start_bots`
`/stop_bots`
`/restart_bots`

`/start_{bot_name}_bot`
`/stop_{bot_name}_bot`
`/restart_{bot_name}_bot`

---

## Створення фін системи

Переходимо у розділ фін системи у джанго адмінці, натискаємо плюс, заповнюємо форму

## Створення клієнтського бота

У адмін боті треба натиснути список боти і там створити бота

**Whatsapp бот**

---

Спершу треба створити whatsapp business account та Meta business account.
Верифікацію компанії робити не треба.
Додати whatsapp business account до Meta business account.
Далі у розділі apps (Meta for developers) треба створити додаток і вибрати тип whatsapp.
Після створення у розділі get_started можна отримати тестовий токен(працює 24 години)
та phone_number_id тестового номеру телефону.

Для створення бота також знадобиться App id та App secret (можна знайти в розділі Settings->Basic)

У розділі WhatsApp->Configurations потрібно додати посилання на вебхук. (WHATSAPP_WEBHOOK_URL)

Для тестового номеру телефону не можна отримати постійний токен і його потрібно міняти кожні 24 години

Для створення постійного токену потрібно створити system_user і у його меню створити токен для цього додатку
Далі треба додати номер телефону. ВАЖЛИВО, не треба додавати свій основний номер телефону, на якому вже є whatsapp
Для постійного токену треба мати ще один номер телефону для того, щоб створити для нього business account.
Далі у налаштуваннях додатку треба заповнити privacy policy link. https://incust.com/en/privacy-policy/
І потім можна перевести додаток у release mode і вже працювати

**telegram бот**

---

тут нічого особливого робити не треба. Треба просто створити бота у https://BotFather.t.me

## Тестування (unit tests)

Модулі тестування знаходяться в директорії ~/pay4say-bot/bot/tests/

Перед запуском тестових модулей - переконайтесь, що ви створили файл ~/pay4say-bot/bot/tests/test_config.py
за зразком ~/pay4say-bot/bot/tests/test_config_example.py та заповнили всі необхідні поля.

Для запуску тестів виконуються наступні команди:

```bash
pytest -q -s tests/incust/test_redeem_schemas.py Запустити всі тести
pytest -q -s tests/incust/test_redeem_schemas.py -k 'test_get_temp_code' Запустити тест з назвою test_get_temp_code
```

[Документація pytest](https://docs.pytest.org/en/7.1.x/contents.html)
