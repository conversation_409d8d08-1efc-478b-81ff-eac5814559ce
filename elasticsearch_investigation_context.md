# Розслідування адмін сповіщень 11 липня 2025

## Проблема
11 липня 2025 року о 19:47-19:50 (Київ) / 16:47-16:50 (UTC) були адмін сповіщення про помилки платежів для профілю "7loc Credit" (ID: 1725), але адміни з правами `profile:admin` не отримали емейли.

## Виявлені факти

### Сповіщення в БД
- **IDs сповіщень:** 10050-10059 (і більше)
- **Заголовок:** "Une erreur s'est produite lors du traitement du paiement du client !"
- **Категорія:** PAYMENT
- **Тип:** EXTERNAL_PAYMENT_ERROR
- **Scope:** `profile:edit` (ПОМИЛКА! Мав би бути `profile:admin`)
- **Группа:** 7loc Credit (ID: 1725)
- **Деталі помилки:** SenEau платіж 22,471 F CFA, користувач Ousmane (+221778010249), рахунок ID 1226300

### Користувачі з правами

#### profile:admin (мали б отримати емейли):
- <EMAIL> (Maksym Skuibida)
- <EMAIL> (Maxim Ronshin)
- <EMAIL> (Viktor H) ← запитувач
- <EMAIL> (Maguette Madauky Sow)
- <EMAIL> (Andrii Petlovanyi)
- <EMAIL> (Valentyn Fedchenko)

#### profile:edit (фактично отримали емейли):
- <EMAIL> (Alina Korchatova)
- <EMAIL> (Maguette Madauky Sow)
- <EMAIL> (Marie-Andrée Lebry)
- <EMAIL> (Andrii Petlovanyi)

## Технічні деталі

### API Endpoint
- **URL:** POST `/profile/{profile_id}/staff`
- **Сервіс:** ProfileNotificationsService
- **Параметри:** CreateProfileStaffNotificationData з полем `scope`

### Kafka логи для пошуку
- **Топік:** `email-regular`
- **Час:** `2025-07-11T16:47:00` - `2025-07-11T16:51:00` (UTC)
- **Логери:** `kafka.producer` або `email.sender`
- **Повідомлення:** `EMAIL_KAFKA_SEND_TIME` або `EMAIL_SEND_COMPLETE_TIME`
- **Subject:** `"7loc Credit: Une erreur s'est produite lors du traitement du paiement du client !"`

### Elasticsearch запит
```json
{
  "query": {
    "bool": {
      "must": [
        {
          "range": {
            "@timestamp": {
              "gte": "2025-07-11T16:47:00.000Z",
              "lte": "2025-07-11T16:51:00.000Z"
            }
          }
        },
        {
          "bool": {
            "should": [
              {"match": {"message": "EMAIL_KAFKA_SEND_TIME"}},
              {"match": {"message": "EMAIL_SEND_COMPLETE_TIME"}}
            ]
          }
        },
        {
          "terms": {
            "destination_email": [
              "<EMAIL>",
              "<EMAIL>", 
              "<EMAIL>",
              "<EMAIL>"
            ]
          }
        },
        {
          "wildcard": {
            "subject": "*7loc Credit*Une erreur*"
          }
        }
      ]
    }
  }
}
```

## Мета після рестарту
Використати Elasticsearch MCP сервер для:
1. Підключення до https://elksearch.monitoring.7loc.com/
2. Знаходження логів відправки емейлів за 11 липня 16:47-16:51 UTC
3. Підтвердження на які емейли фактично відправлялися сповіщення
4. Знаходження причини використання неправильного scope

## Налаштування MCP
- **Сервер:** elasticsearch (npx -y @elastic/mcp-server-elasticsearch)
- **URL:** https://elksearch.monitoring.7loc.com/
- **Користувач:** viktor
- **Пароль:** chai}gaipeeya]Teefuni9cui

## Змінні середовища
```bash
export ES_URL="https://elksearch.monitoring.7loc.com/"
export ES_USERNAME="viktor"
export ES_PASSWORD="chai}gaipeeya]Teefuni9cui"
export OTEL_LOG_LEVEL="none"
```