# 🛠️ Повний набір інструментів для роботи з БД і перекладами

## 📋 Огляд

Створено два основних набори інструментів:

### 1️⃣ **Відновлення конкретних перекладів** (для проблем типу бренд 703)
### 2️⃣ **Розбивка дампу на таблиці** (для загального керування БД)

---

## 1️⃣ Інструменти для відновлення перекладів

### 🎯 Призначення: 
Відновлення втрачених перекладів для конкретних об'єктів (бренди, товари, тощо)

### 📁 Файли:
```
extract_translations_fixed.py      - Витягування ТІЛЬКИ таблиці translations
prepare_translations_file.sh       - Підготовка файлу БЕЗ імпорту  
import_translations_to_prod.sh     - Безпечний імпорт з підтвердженням
analyze_translations_differences.sh - Детальне порівняння таблиць
restore_missing_translations.sh    - Відновлення відсутніх записів
cleanup_temp_tables_prod.sh        - Очищення тимчасових даних
README_FIXED.md                    - Інструкція використання
```

### 🚀 Швидкий старт:
```bash
./prepare_translations_file.sh     # Підготовка (5-15 хв)
./import_translations_to_prod.sh   # Імпорт (2-5 хв)  
./analyze_translations_differences.sh  # Аналіз (1-2 хв)
./restore_missing_translations.sh  # Відновлення (1-3 хв)
```

### ✅ Переваги:
- 🎯 **Точність**: тільки відсутні записи
- 🛡️ **Безпека**: автоматичні бекапи
- ⚡ **Швидкість**: не чіпає зайве
- 🔍 **Аналіз**: детальна статистика

---

## 2️⃣ Інструменти для розбивки дампу

### 🎯 Призначення:
Розбивка великого дампу (53ГБ) на окремі файли таблиць для легшого керування

### 📁 Файли:
```
split_dump_by_tables.py            - Python модуль розбивки
split_dump.sh                      - Головний скрипт розбивки
manage_split_tables.sh             - Інтерактивне керування таблицями
quick_import_tables.sh             - Швидкий імпорт популярних таблиць
README_SPLIT_TABLES.md             - Детальна інструкція
```

### 🚀 Швидкий старт:
```bash
./split_dump.sh                    # Розбивка дампу (10-30 хв)
./quick_import_tables.sh           # Швидкий імпорт
./manage_split_tables.sh           # Інтерактивне керування
```

### ✅ Переваги:
- 📁 **Організація**: кожна таблиця в окремому файлі
- ⚡ **Швидкість**: імпорт тільки потрібного
- 🎯 **Гнучкість**: селективне відновлення
- 💾 **Ефективність**: менше ресурсів

---

## 🔧 Допоміжні інструменти

### Загальні:
```
install_mysql_client.sh            - Встановлення MySQL клієнта під WSL
prod_config.sh                     - Конфігурація підключення до ПРОДУ
```

### Конфігурація ПРОДУ:
```bash
export PROD_HOST="*************"   # IP VPS через VPN
export PROD_USER="payforsay"
export PROD_PASS="PaY4SaY"  
export PROD_DB="payforsay"
export BACKUP_PATH="/mnt/c/dev/payforsay/_backUp/payforsay.sql"
```

---

## 🎯 Сценарії використання

### Сценарій 1: Проблема з перекладами (як бренд 703)
```bash
# Швидке відновлення конкретних перекладів
./prepare_translations_file.sh
./import_translations_to_prod.sh
./analyze_translations_differences.sh
./restore_missing_translations.sh
```
**Час:** 10-25 хвилин  
**Результат:** Відновлені тільки відсутні переклади

### Сценарій 2: Повне відновлення БД після збою
```bash
# Розбивка дампу + селективне відновлення
./split_dump.sh
./quick_import_tables.sh  # Вибрати "всі таблиці"
```
**Час:** 30-60 хвилин  
**Результат:** Повністю відновлена БД

### Сценарій 3: Налаштування локальної БД для розробки
```bash
# Розбивка + імпорт тільки потрібних таблиць
./split_dump.sh
./quick_import_tables.sh  # Вибрати "системні таблиці"
```
**Час:** 20-35 хвилин  
**Результат:** Локальна БД з основними даними

### Сценарій 4: Аналіз конкретної таблиці
```bash
# Розбивка + аналіз
./split_dump.sh
./manage_split_tables.sh  # Опція "аналізувати таблицю"
```
**Час:** 15-25 хвилин  
**Результат:** Детальна інформація про таблицю

---

## 📊 Порівняння підходів

| Завдання | Інструмент | Час | Підходить для |
|----------|------------|-----|---------------|
| Відновлення перекладів | translations tools | 10-25 хв | Конкретні проблеми |
| Повне відновлення БД | split_dump tools | 30-60 хв | Повний збій |
| Селективне відновлення | split_dump tools | 15-45 хв | Часткові проблеми |
| Аналіз даних | обидва підходи | 10-30 хв | Дослідження |

---

## 🛡️ Безпека

### Автоматичні бекапи:
- ✅ **translations tools**: створює бекапи перед кожною операцією
- ✅ **split_dump tools**: не чіпає ПРОД під час розбивки
- ✅ **Підтвердження**: запитує дозвіл перед змінами ПРОДУ

### Відкат змін:
```bash
# Для translations tools
mysql -h"PROD" -u"user" -p"pass" "db" -e "
RENAME TABLE translations TO translations_broken;
RENAME TABLE translations_before_restore_YYYYMMDD_HHMMSS TO translations;"

# Для split_dump tools  
# Просто не імпортувати - дамп не змінюється
```

---

## 🎉 Підсумок

### ✅ Створено:
- **10 готових скриптів** для різних завдань
- **2 Python модулі** для складної обробки
- **3 детальні інструкції** з прикладами
- **Повний набір** для роботи з БД і перекладами

### 🚀 Готово до використання:
- Відновлення проблемних перекладів: **10-25 хвилин**
- Розбивка дампу на таблиці: **10-30 хвилин**  
- Селективне відновлення БД: **15-45 хвилин**
- Повне відновлення БД: **30-60 хвилин**

### 💡 Рекомендації:
1. **Для проблем з перекладами** → використовуйте translations tools
2. **Для загальної роботи з БД** → використовуйте split_dump tools
3. **Для розробки** → розбийте дамп один раз, потім працюйте з окремими таблицями
4. **Для продакшену** → завжди робіть бекапи перед змінами

---

**🎯 Все готово для ефективної роботи з базою даних та відновлення перекладів!**
