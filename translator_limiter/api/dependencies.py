from datetime import datetime

from fastapi import Query, Depends, HTTPException, Path
from sqlalchemy.ext.asyncio import AsyncSession
from starlette import status
from starlette.requests import Request

from config import ADMIN_TOKEN
from db.models import AccessToken, Limiter


def get_db(request: Request) -> AsyncSession:
    return request.state.db


def get_api_key(api_key: str = Query()) -> str:
    return api_key


def get_authorised_limiter(for_update: bool = False):
    async def func(
            db: AsyncSession = Depends(get_db),
            api_key: str = Depends(get_api_key),
    ) -> Limiter:
        access_token = await AccessToken.get(db, api_key=api_key)
        if not access_token:
            raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Invalid api key")

        if access_token.expire_at and access_token.expire_at < datetime.utcnow():
            raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Api key expired")

        limiter = await <PERSON>iter.get(db, access_token.limiter_id, for_update=for_update)
        if not limiter:
            raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Invalid api key")

        return limiter
    return func


async def get_limiter_by_project_id(
        db: AsyncSession = Depends(get_db),
        admin_token: str = Query(),
        project_id: str = Path(),
):
    if admin_token != ADMIN_TOKEN:
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Invalid admin token")

    if project_id:
        limiter = await Limiter.get(db, project_id=project_id)
        if not limiter:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Limiter not found")
        return limiter
