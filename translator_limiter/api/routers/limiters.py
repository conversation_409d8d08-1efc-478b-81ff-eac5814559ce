from fastapi import APIRouter, Depends, HTTPException
from psutils.locker import Locker
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import TimeoutError as SATimeoutError
from starlette import status
from starlette.requests import Request

import schemas
from config import DEFAULT_TOKEN_NAME, DEFAULT_LIMIT
from db import crud
from db.models import Limiter

from ..dependencies import get_db, get_authorised_limiter, get_limiter_by_project_id, get_api_key

router = APIRouter(
    prefix="/limiters",
    tags=["limiters"]
)


locker = Locker()


@router.post("/create", responses={
    200: {"model": schemas.LimiterCreated},
    409: {"description": "Limiter for this project id already exists"},
})
async def create_limiter(
        data: schemas.CreateLimiterData,
        db: AsyncSession = Depends(get_db)
) -> schemas.LimiterCreated:
    if await Limiter.get(db, project_id=data.project_id):
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Limiter for this project id already exists")

    limiter = await crud.create_limiter(db, data)
    access_token = await crud.create_access_token(db, DEFAULT_TOKEN_NAME, limiter)

    return schemas.LimiterCreated(
        api_key=await access_token.awaitable_attrs.api_key,
        project_id=limiter.project_id,
        limit=limiter.custom_limit or DEFAULT_LIMIT,
        used_characters=await limiter.awaitable_attrs.used_characters,
    )


@router.post("/processLimit", status_code=200, responses={
    200: {"model": schemas.LimitSuccess, "description": "Success"},
    401: {"description": "Unauthorized"},
    403: {"model": schemas.LimitExceeded, "description": "Translation not allowed"}
})
async def process(
        request: Request,
        data: schemas.ProcessLimitData,
        db: AsyncSession = Depends(get_db),
        api_key: str = Depends(get_api_key),
        limiter: Limiter = Depends(get_authorised_limiter(True)),
):
    retries = 0
    while True:
        try:
            return await crud.process_limit(db, limiter, data, request.client.host, api_key)
        except SATimeoutError:
            if retries == 5:
                raise
            retries += 1
            continue


@router.get("/getCurrent", responses={
    200: {"model": schemas.Limiter, "description": "success"},
    401: {"description": "Unauthorized"}
})
async def get_current_limiter(
        limiter: Limiter = Depends(get_authorised_limiter()),
) -> schemas.Limiter:
    return limiter


@router.post("/setLimit", responses={
    200: {"model": schemas.Limiter, "description": "success"},
    401: {"description": "Unauthorized"},
})
async def set_current_limit(
        data: schemas.SetLimitData,
        db: AsyncSession = Depends(get_db),
        limiter: Limiter = Depends(get_authorised_limiter()),
) -> schemas.Limiter:
    await limiter.update(db, limit=data.new_limit)
    return limiter


@router.get("/{project_id}", responses={
    200: {"model": schemas.LimiterWithAccessTokens, "description": "success"},
    401: {"description": "Admin token invalid"},
    404: {"description": "Limiter not found"},
})
async def get_limiter_by_project_id(
        db: AsyncSession = Depends(get_db),
        limiter: Limiter = Depends(get_limiter_by_project_id),
) -> schemas.LimiterWithAccessTokens:
    access_tokens = await crud.get_access_tokens(db, limiter.id)
    return schemas.LimiterWithAccessTokens(
        **schemas.Limiter.from_orm(limiter).dict(),
        access_tokens=access_tokens,
    )
