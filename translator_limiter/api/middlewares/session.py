from typing import Callable, Awaitable

from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from db.connection import make_session
from .base import BaseMiddleware


class SessionMiddleware(BaseMiddleware):

    def __init__(self, session_maker: Callable[..., AsyncSession]):
        self.session_maker = session_maker

    async def __call__(self, request: Request, call_next: Callable[..., Awaitable]):
        try:
            request.state.db = make_session()
            return await call_next(request)
        finally:
            await request.state.db.close()
