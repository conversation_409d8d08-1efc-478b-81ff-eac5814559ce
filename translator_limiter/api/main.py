import logging

from fastapi import <PERSON>AP<PERSON>
from psutils.fastapi.logging import setup_uvicorn_loggers
from psutils.logger import setup_logger

from api.middlewares.session import SessionMiddleware
from api.routers import router
from config import LOGS_FOLDER
from db.connection import make_session

app = FastAPI()

SessionMiddleware(make_session).setup(app)

app.include_router(router)


@app.on_event("startup")
async def on_startup():
    setup_logger(
        log_dir=LOGS_FOLDER,
        file_name="errors.log",
        level=logging.WARNING,
        max_bytes=50000,
        backup_count=5,
    )
    setup_logger(
        name='debugger',
        log_dir=LOGS_FOLDER,
        file_name="debug.log",
        level=logging.DEBUG,
    )
    setup_uvicorn_loggers(LOGS_FOLDER)
