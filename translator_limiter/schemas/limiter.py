from datetime import datetime
from typing import Literal

from pydantic import BaseModel


class CreateLimiterData(BaseModel):
    project_id: str
    custom_limit: int | None = None
    used_characters: int | None = None


class BaseLimiter(BaseModel):
    project_id: str
    limit: int
    used_characters: int


class LimiterCreated(BaseLimiter):
    api_key: str


class Limiter(BaseLimiter):
    class Config:
        orm_mode = True

    limit_left: int
    last_use_datetime: datetime | None = None
    last_clear_datetime: datetime


class AccessToken(BaseModel):
    class Config:
        orm_mode = True
    name: str
    api_key: str
    expire_at: datetime | None = None


class LimiterWithAccessTokens(Limiter):
    class Config:
        orm_mode = False

    access_tokens: list[AccessToken]


class ProcessLimitData(BaseModel):
    purpose: Literal["translate", "detect"]
    content: list[str] | str
    from_lang: str | None = None
    to_lang: str | None = None


class LimitExceeded(BaseModel):
    limit: int
    used_characters: int
    content_len: int
    exceeded_by: int


class LimitSuccess(BaseModel):
    limit: int
    used_characters: int
    total_used_characters: int
    limit_left: int


class SetLimitData(BaseModel):
    new_limit: int
