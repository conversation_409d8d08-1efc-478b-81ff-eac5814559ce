from datetime import datetime

from sqlalchemy import select, or_
from sqlalchemy.ext.asyncio import AsyncSession

from db.models import AccessToken


async def get_access_tokens(db: AsyncSession, limiter_id: int) -> list[AccessToken]:
    stmt = select(AccessToken)
    stmt = stmt.where(AccessToken.limiter_id == limiter_id)
    stmt = stmt.where(or_(
        AccessToken.expire_at.is_(None),
        AccessToken.expire_at > datetime.utcnow(),
    ))

    res = await db.scalars(stmt)
    return res.fetchall()  # type: ignore
