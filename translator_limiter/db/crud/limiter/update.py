from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from starlette import status
from starlette.responses import JSONResponse

import schemas
from db.models import Limiter, TranslatorCallLog


async def process_limit(
        db: AsyncSession,
        limiter: Limiter,
        data: schemas.ProcessLimitData,
        client_host: str,
        api_key: str,
) -> JSONResponse | schemas.LimitSuccess:
    try:
        contents = [data.content] if isinstance(data.content, str) else data.content
        contents_len = sum(map(lambda x: len(x.strip()), contents))

        now = datetime.utcnow()

        last_clear_dt = limiter.last_clear_datetime
        if last_clear_dt.month != now.month or last_clear_dt.year != now.year:
            limiter.used_characters = 0
            limiter.last_clear_datetime = now

        total_used_characters = contents_len + limiter.used_characters

        limit = limiter.limit

        is_exceeded = total_used_characters > limit

        log = TranslatorCallLog(
            limiter.project_id,
            client_host,
            data.purpose,
            api_key,
            is_exceeded,
            limit,
            limiter.used_characters,
            contents_len,
            contents,
            data.from_lang[:2] if data.from_lang else None,
            data.to_lang[:2] if data.to_lang else None,
        )
        db.add(log)

        if total_used_characters > limit:
            result = JSONResponse(
                content=schemas.LimitExceeded(
                    limit=limit,
                    used_characters=limiter.used_characters,
                    content_len=contents_len,
                    exceeded_by=total_used_characters - limit,
                ).dict(),
                status_code=status.HTTP_403_FORBIDDEN,
            )
        else:
            limiter.used_characters = total_used_characters
            limiter.last_use_datetime = now

            result = schemas.LimitSuccess(
                limit=limit,
                used_characters=contents_len,
                total_used_characters=total_used_characters,
                limit_left=limit - total_used_characters,
            )
    except:
        await db.rollback()
        raise
    else:
        await db.commit()
        return result
