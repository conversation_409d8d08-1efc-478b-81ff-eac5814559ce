from typing import Type, TypeVar

from psutils.text import paschal_case_to_snake_case
from sqlalchemy import Column, exists, select
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, declared_attr

T = TypeVar("T")


class BaseDBModel:
    __allow_unmapped__ = True

    GET_FIELDS: list[str] = None
    UPDATE_FIELDS: list[str] = None

    @declared_attr
    def __tablename__(self):
        return paschal_case_to_snake_case(self.__name__) + "s"

    id: Mapped[int] = Column(INTEGER(unsigned=True), autoincrement=True, primary_key=True)

    @classmethod
    def __get_expr(cls: Type[T], exists_stmt: bool = False, for_update: bool = False, **kwargs):
        stmt = exists(cls) if exists_stmt else select(cls)

        if for_update:
            stmt = stmt.with_for_update()

        for key, value in kwargs.items():
            if key not in dir(cls):
                raise AttributeError("Unknown attribute '%s'" % key)
            if cls.GET_FIELDS and key not in cls.GET_FIELDS:
                raise AttributeError("Forbidden to use attribute '%s' in get method" % key)

            stmt = stmt.where(getattr(cls, key) == value)
        if exists_stmt:
            stmt = select(stmt)
        return stmt

    @classmethod
    async def get(cls: Type[T], db: AsyncSession, id: int = None, for_update: bool = False, **kwargs) -> T | None:
        if id:
            kwargs["id"] = id

        stmt = cls.__get_expr(for_update=for_update, **kwargs)
        return await db.scalar(stmt)

    @classmethod
    async def is_exists(cls, db: AsyncSession, id: int = None, for_update: bool = False, **kwargs) -> bool:
        if id:
            kwargs["id"] = id
        return await db.scalar(cls.__get_expr(exists_stmt=True, for_update=for_update, **kwargs))

    @classmethod
    async def get_list(cls: Type[T], db: AsyncSession, **kwargs) -> list[T]:
        stmt = cls.__get_expr(**kwargs)
        result = await db.scalars(stmt)
        return result.fetchall()

    async def update(self, db: AsyncSession, **data):
        """
        Function to update model
        """
        for key, value in data.items():
            if key not in dir(self):
                raise AttributeError("Unknown attribute '%s'" % key)
            if self.UPDATE_FIELDS and key not in self.UPDATE_FIELDS:
                raise AttributeError("Forbidden to update attribute '%s'" % key)
            setattr(self, key, value)
        await db.commit()

    def __str__(self):
        return f"{self.__name__} #{self.id}"
