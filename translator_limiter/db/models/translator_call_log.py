from typing import Literal

from sqlalchemy import <PERSON>um<PERSON>, String, <PERSON><PERSON>an, <PERSON><PERSON><PERSON>
from sqlalchemy.dialects.mysql import INTEGER

from db.mixins import BaseDBModel, TimeCreatedMixin
from db.models.base import Base


class TranslatorCallLog(Base, BaseDBModel, TimeCreatedMixin):
    project_id: str = Column(String(256), nullable=False)
    client_host: str = Column(String(100), nullable=False)
    purpose: Literal["translate", "detect"] = Column(String(9), nullable=False)
    by_api_key: str = Column(String(36), nullable=False)
    is_exceeded: bool = Column(Boolean, nullable=False)

    current_limit: int | None = Column(INTEGER(unsigned=True), nullable=False)
    used_characters_before: int = Column(INTEGER(unsigned=True), nullable=False)
    content_len: int = Column(INTEGER(unsigned=True), nullable=False)

    content: list[str] = Column(JSON, nullable=False)

    from_lang: str | None = Column(String(2), nullable=True)
    to_lang: str | None = Column(String(2), nullable=True)

    def __init__(
            self,
            project_id: str,
            client_host: str,
            purpose: Literal["translate", "detect"],
            by_api_key: str,
            is_exceeded: bool,
            current_limit: int | None,
            used_characters_before: int,
            content_len: int,
            content: list[str],
            from_lang: str | None = None,
            to_lang: str | None = None,
    ):
        super().__init__(
            project_id=project_id,
            client_host=client_host,
            purpose=purpose,
            by_api_key=by_api_key,
            is_exceeded=is_exceeded,
            current_limit=current_limit,
            used_characters_before=used_characters_before,
            content_len=content_len,
            content=content,
            from_lang=from_lang,
            to_lang=to_lang,
        )
