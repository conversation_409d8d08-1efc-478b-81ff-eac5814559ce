from datetime import datetime

from sqlalchemy import Column, String, DateTime, ForeignKey, UniqueConstraint, text
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.orm import relationship, Mapped

from db import models
from db.mixins import BaseDBModel, TimeCreatedMixin
from .base import Base


class AccessToken(BaseDBModel, Base, TimeCreatedMixin):
    name: str = Column(String(50), nullable=False)
    api_key: str = Column(String(36), nullable=False, unique=True, server_default=text("(UUID())"))

    limiter_id: Mapped[int] = Column(INTEGER(unsigned=True), ForeignKey("limiters.id", ondelete="CASCADE"), nullable=False)
    limiter: "models.Limiter" = relationship("Limiter", back_populates="access_tokens")

    expire_at: Mapped[datetime | None] = Column(DateTime, nullable=True)

    __table_args__ = (
        UniqueConstraint("name", "limiter_id"),
    )

    def __init__(
            self,
            name: str,
            limiter: "models.Limiter",
            api_key: str | None = None,
    ):
        self.name = name
        self.limiter = limiter
        if api_key is not None:
            self.api_key = api_key
