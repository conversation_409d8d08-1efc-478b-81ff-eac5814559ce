from datetime import datetime

from sqlalchemy import Column, String, DateTime, text
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.orm import relationship

from config import DEFAULT_LIMIT
from db import models
from db.mixins import BaseDBModel, TimeCreatedMixin
from .base import Base


class Limiter(Base, BaseDBModel, TimeCreatedMixin):
    project_id: str = Column(String(256), nullable=False, unique=True)
    custom_limit: int | None = Column(INTEGER(unsigned=True), nullable=True)

    access_tokens: list["models.AccessToken"] = relationship("AccessToken", back_populates="limiter")

    used_characters: int = Column(INTEGER(unsigned=True), nullable=False, server_default="0")
    last_use_datetime: datetime | None = Column(DateTime, nullable=True)
    last_clear_datetime: datetime = Column(DateTime, nullable=False, server_default=text("(UTC_TIMESTAMP())"))

    def __init__(
            self, project_id: str,
            custom_limit: int | None = None,
            used_characters: int | None = None,
    ):
        self.project_id = project_id
        self.custom_limit = custom_limit
        if used_characters is not None:
            self.used_characters = used_characters
        super().__init__()

    @property
    def limit(self):
        return self.custom_limit or DEFAULT_LIMIT

    @limit.setter
    def limit(self, value: int):
        self.custom_limit = value

    @property
    def limit_left(self):
        if self.used_characters < self.limit:
            return self.limit - self.used_characters
        return 0
