from collections import defaultdict

import aiofiles
import asyncio
import json
import typer
from async_typer import As<PERSON><PERSON><PERSON>
from openai import AsyncClient

from config import OPENAI_API_KEY

client = AsyncClient(api_key=OPENAI_API_KEY)


async def translate_text(text: str, source_lang: str, target_lang: str) -> str:
    prompt = (
        f"Translate the following text from {source_lang} to {target_lang}. "
        f"Keep the meaning, but make it natural in the target language, "
        f"but keep the style of the text \n\n Text: {text}"
    )

    response = await client.chat.completions.create(
        model="gpt-4.1-mini",
        messages=[{"role": "user", "content": prompt}],
        temperature=0
    )
    return response.choices[0].message.content.strip()


app = AsyncTyper()


async def translate_obj_key(
        key: str,
        value: str,
        source_lang: str,
        target_lang: str,
        result: dict[str, dict[str, str]],
):
    print(f'--Translating {key} {source_lang} {target_lang}')
    result[target_lang][key] = await translate_text(
        value, source_lang, target_lang
    )
    print(f'--Translated {key} {source_lang} {target_lang}')


async def translate_obj(
        obj: dict,
        source_lang: str,
        target_langs: list[str],
):
    result: dict[str, dict[str, str]] = defaultdict(dict)

    tasks = []

    for target_lang in target_langs:
        for key, value in obj.items():
            tasks.append(
                translate_obj_key(key, value, source_lang, target_lang, result)
            )

    await asyncio.gather(*tasks, return_exceptions=False)
    return result


@app.async_command()
async def translate(
        file_path: str = typer.prompt("File path"),
        source_lang: str = typer.prompt("Source lang"),
        target_langs: str = typer.prompt("List of languages"),
):
    target_langs = [l.strip() for l in target_langs.split(",")]

    async with aiofiles.open(file_path, "r") as file:
        data: dict[str, dict[str, dict[str, str]]] = json.loads(
            await file.read()
        )

    result: dict[str, dict[str, dict[str, str]]] = {}

    async def iteration(i, item):
        obj_key, obj = item
        print(f"({i + 1} / {len(data)})Translating {obj_key} {obj['name']}")
        result[obj_key] = await translate_obj(obj, source_lang, target_langs)
        print(f"({i + 1} / {len(data)})Translated {obj_key} {obj['name']}")

    await asyncio.gather(
        *[iteration(*el) for el in enumerate(data.items())], return_exceptions=False
    )

    print(json.dumps(result, indent=4, ensure_ascii=False))

    file_name, ext = file_path.rsplit(".", 1)
    result_path = f"{file_name}.translated.{ext}"

    async with aiofiles.open(result_path, "w") as file:
        await file.write(json.dumps(result, indent=4, ensure_ascii=False))

    print(f"Result was written to {file_name}")


if __name__ == '__main__':
    app()
