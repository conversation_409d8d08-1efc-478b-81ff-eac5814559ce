from typing import List

from db import db_func, models, sess
from schemas import ExtSysSetTypes


@db_func
def get_reviews(
        group_id: int, bot_id: int,
        position: int = 0, limit: int = None,
        operation: str = "all"
) -> List[models.Review] | int:
    query = sess().query(models.Review)
    query = query.filter(
        models.Review.group_id == group_id, models.Review.bot_id == bot_id, models.Review.privacy == "public"
    )
    slice_args = [position, None]
    if limit:
        slice_args[1] = position + limit
    query = query.order_by(models.Review.time.desc()).slice(*slice_args)
    if operation == "count":
        result = query.count()
    else:
        result = query.all()
    return result


async def is_incust(brand_id: int) -> bool:
    brand_settings = await models.BrandSettings.get_by_brand_and_type(
        brand_id,
        ExtSysSetTypes.incust_loyalty_id.value,
    )
    return True if brand_settings else False
