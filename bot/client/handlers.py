import asyncio
from aiogram import Dispatcher
from aiohttp import web
from psutils.local import setup_psutils_localisation

from core.aiogram_middlewares import (
    ClientBotMiddleware, ExceptionHandlersMiddleware,
    LinkUncompressorMiddleware,
)
from core.aiogram_middlewares.billing_checker import BillingCheckerMiddleware
from core.aiogram_middlewares.cache_time import CacheTimeMiddleware
from core.aiogram_middlewares.callback_data import CallbackDataMiddleware
from core.aiogram_middlewares.create_or_update import CreateOrUpdateUserMiddleware
from core.bot.custom_menu_buttons.handlers import register_custom_menu_buttons_handlers
from core.bot.handlers import register_bot_buttons_handlers
from core.chat.filters.aiogram import bind_chat_filters
from core.chat.handlers import *
from core.crm_ticket.handlers import register_crm_ticket_button_handlers
from core.ewallet.payment.handlers import \
    register_ewallet_payment_buttons_handlers
from core.ewallet.payment.handlers.commands_handlers import \
    register_ewallet_payment_commands_handlers
from core.ewallet_users.handlers.commands_handlers import \
    register_ewallet_user_commands_handlers
from core.external_login.handlers import *
from core.fast_pay.handlers import (
    register_fast_pay_deep_link_handlers,
    register_fast_pay_message_handlers, register_payment_method_button_handlers,
)
from core.friend.handlers import *
from core.handlers import *
from core.invoice.invoice_process.form.form import InvoiceProcessForm
from core.kafka.producer import producer
from core.menu_in_store.handlers.buttons_handlers import \
    register_menu_in_store_buttons_handlers
from core.share_bot.deep_link_handlers import register_recommend_bot_deep_link
from core.share_bot.menu_handlers import register_share_bot_handlers
# from core.topup_ewallet.handlers.button_handlers import \
#     register_topup_ewallet_button_handlers
from core.topup_ewallet.handlers.message_handlers import \
    register_topup_ewallet_message_handlers
from core.user.agreement_processor.buttons_handlers import \
    setup_tg_handlers as setup_agreements_handler
from utils.filters.bind import bind_general_filters
from utils.redefined_classes import Bot
from utils.router import Router
from .auto_answer.worker import auto_answer_sender
from .external_coupon.handlers import register_external_coupon_commands_handlers
from .external_coupon.handlers.callback_handlers import \
    register_external_coupon_callback_handlers
from .incust_referral.handlers import register_incust_referral_commands_handlers
from .incust_referral.handlers.callback_handlers import \
    register_incust_referral_callback_handlers
from .invoice.filters import bind_invoice_filters
from .invoice.handlers import *
from .main.handlers.group import *
from .main.handlers.private import *
from .main.handlers.web import *
from .menu_in_store.handlers import *
from .review.form import MakeReviewForm
from .review.handlers import *
from .settings.handlers import *


async def on_startup(dp: Dispatcher):
    await setup_psutils_localisation()

    Router.set_current(dp["router"])
    Dispatcher.set_current(dp)
    Bot.set_current(dp.bot)

    # функция, отправляющая авто ответ пользователям
    asyncio.ensure_future(auto_answer_sender())
    await producer.initialise()


async def on_shutdown(dp: Dispatcher):
    await dp.storage.close()
    await producer.stop()


def register_web_handlers(app: web.Application, dp: Dispatcher):
    register_main_web_handlers(app, dp)


def bind_filters(dp: Dispatcher):
    bind_general_filters(dp)
    bind_chat_filters(dp)
    bind_invoice_filters(dp)


def register_bot_handlers(dp: Dispatcher):
    Dispatcher.set_current(dp)

    dp.setup_middleware(ClientBotMiddleware())
    dp.setup_middleware(ExceptionHandlersMiddleware())
    dp.setup_middleware(BillingCheckerMiddleware())
    dp.setup_middleware(CreateOrUpdateUserMiddleware())
    dp.setup_middleware(CallbackDataMiddleware())
    dp.setup_middleware(LinkUncompressorMiddleware())
    dp.setup_middleware(CacheTimeMiddleware())

    register_invoice_successful_payments_handlers(dp)
    register_invoice_pre_checkout_query_handlers(dp)
    register_invoice_shipping_query_handler(dp)

    register_main_group_message_handlers(dp)

    register_bot_blocked_handlers(dp)

    register_recommend_bot_deep_link(dp)
    register_fast_pay_deep_link_handlers(dp)
    register_external_login_commands_handlers(dp)
    register_external_coupon_commands_handlers(dp)

    register_ewallet_payment_commands_handlers(dp)
    register_ewallet_user_commands_handlers(dp)

    register_add_friend_commands_handlers(dp)

    register_incust_referral_commands_handlers(dp)

    register_menu_in_store_deep_link_handlers(dp)
    register_chat_commands_handlers(dp)
    register_invoice_commands_handlers(dp)
    register_settings_commands_handlers(dp)
    # register_test_webapp_commands_handlers(dp)
    register_main_commands_handlers(dp)
    register_general_commands_handlers(dp)

    register_review_menu_handlers(dp)
    register_share_bot_handlers(dp)
    register_custom_menu_buttons_handlers(dp)
    register_main_menu_handlers(dp)

    dp["router"].setup_handlers()

    MakeReviewForm.setup_handlers(dp)
    InvoiceProcessForm.setup_handlers(dp)

    register_settings_message_handlers(dp)
    register_chat_message_handlers(dp)

    register_fast_pay_message_handlers(dp)
    register_topup_ewallet_message_handlers(dp)

    register_main_message_handlers(dp)

    setup_agreements_handler(dp)

    register_bot_buttons_handlers(dp)

    register_main_callback_handlers(dp)

    register_menu_in_store_buttons_handlers(dp)
    register_friend_buttons_handlers(dp)
    register_ewallet_payment_buttons_handlers(dp)
    register_payment_method_button_handlers(dp)
    # register_topup_ewallet_button_handlers(dp)

    register_review_callback_handlers(dp)
    register_settings_callback_handlers(dp)

    register_crm_ticket_button_handlers(dp)
    register_external_login_button_handlers(dp)
    register_external_coupon_callback_handlers(dp)
    register_incust_referral_callback_handlers(dp)

    register_chat_callback_handlers(dp)
    register_general_callback_handlers(dp)

    register_general_exception_handlers(dp)


__all__ = [
    "on_startup", "on_shutdown",
    "register_web_handlers",
    "register_bot_handlers",
    "bind_filters",
]
