import logging

from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from core.keyboards import get_bot_menu_keyboard
from core.menu_in_store.deel_links import MenuInStoreDeepLink
from core.menu_in_store.functions import send_menu_in_store
from db.models import MenuInS<PERSON>, User, ClientBot
from utils.text import f


async def menu_in_store_deep_link_handler(
        message: types.Message,
        state: FSMContext,
        data: MenuInStoreDeepLink,
        user: User, bot: ClientBot, lang: str,
):
    await state.finish()
    menu_in_store: MenuInStore | None = await MenuInStore.get(data.menu_in_store_id)
    if not menu_in_store:
        keyboard = await get_bot_menu_keyboard(user, bot, lang)
        text = await f("menu in store link invalid error", lang)
        return await message.answer(text, reply_markup=keyboard)

    try:
        if menu_in_store and menu_in_store.need_save_as_active and not data.is_payment_online:
            user_bot_activity = await user.activity_in_bot
            await user_bot_activity.update(active_menu_in_store_id=menu_in_store.id)
    except Exception as e:
        keyboard = await get_bot_menu_keyboard(user, bot, lang)
        logging.error(e, exc_info=True)
        await message.answer(
            await f("menu in store unable to save active menu error", lang),
            reply_markup=keyboard,
        )

    await send_menu_in_store(message, state, lang, menu_in_store, user, bot, data.is_payment_online)


def register_menu_in_store_deep_link_handlers(dp: Dispatcher):
    dp.register_message_handler(
        menu_in_store_deep_link_handler,
        MenuInStoreDeepLink.get_filter(return_field_name="data"),
        commands="start",
        state="*",
    )

    # Deprecated. Sometime will be removed
    dp.register_message_handler(
        menu_in_store_deep_link_handler,
        MenuInStoreDeepLink.get_filter("mss", return_field_name="data"),
        commands="start",
        state="*",
    )
