from aiogram.dispatcher import FSMContext
from aiogram import types

from db.models import User, ClientBot, UserSettings

from utils.text import f
from utils.router import Router

from .keyboards import get_mailing_notifications_settings_keyboard, get_settings_keyboard
from ..helpers import send_error

from .states import Settings


async def send_mailing_notifications_settings_menu(message: types.Message, user: User, lang: str, mode: str):
    keyboard = await get_mailing_notifications_settings_keyboard(user, lang)
    text = await f("mailing notifications settings header", lang)

    if mode == "edit":
        return await message.edit_text(text, reply_markup=keyboard)
    return await message.answer(text, reply_markup=keyboard)


async def send_settings_menu(message: types.Message, lang: str):
    keyboard = await get_settings_keyboard(message.chat.id, lang)
    await message.answer(await f("settings header", lang), reply_markup=keyboard)


async def send_enter_timezone_menu(message: types.Message, user: User, lang: str):
    await message.answer(await f("enter timezone header", lang, current=user.get_timezone()))


async def save_user_settings(message: types.Message, state: FSMContext, user: User, lang: str):
    state_data = await state.get_data()
    bot = await ClientBot.get_current()
    user_settings: UserSettings = await UserSettings.get(user, bot)
    result = await user_settings.update(**state_data)
    if not result:
        await send_error(message)
        return
    await Settings.Action.set()
    keyboard = await get_settings_keyboard(message.chat.id, lang)
    try:
        await message.edit_reply_markup(keyboard)
    except:
        await send_settings_menu(message, lang)


def register_settings_routes(router: Router):
    router.add_route(Settings.Action, send_settings_menu)
    router.add_route(Settings.TimeZone, send_enter_timezone_menu)
    router.add_route(Settings.MailingSettings, send_mailing_notifications_settings_menu)
    router.add_route(Settings.Save, save_user_settings)
