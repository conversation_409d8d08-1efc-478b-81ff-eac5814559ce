from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import User, UserGroupSettings

from utils.text import f
from utils.router import Router

from core.helpers import send_change_lang_menu

from ...main.keyboards import get_menu_keyboard

from client.settings.states import Settings
from utils.translator import Translator


async def send_settings_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await Settings.first()
    await Router.state_menu(callback_query, state, lang)


async def settings_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, lang: str,
):
    await Settings.Save.set()
    await state.update_data(**callback_data)
    await Router.state_menu(callback_query, state, lang)


async def edit_timezone_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, lang: str,
):
    await Settings.TimeZone.set()
    await Router.state_menu(callback_query, state, lang)


async def mailing_notifications_settings_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, lang: str,
):
    await Settings.MailingSettings.set()
    await Router.state_menu(callback_query, state, lang, mode="new")


async def mailing_mode_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, lang: str,
):
    user_group_settings = await UserGroupSettings.get_by_id(callback_data.get("id"))

    mailing_mode = callback_data.get("m")  # mailing mode
    await user_group_settings.update(mailing_mode=mailing_mode)
    await Router.state_menu(callback_query, state, lang)


async def langs_list_button_handler(callback_query: types.CallbackQuery, lang: str):
    await send_change_lang_menu(callback_query.message, lang)


async def change_lang_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, user: User,
):
    new_lang = callback_data.get("lang")

    lang = await user.set_lang(new_lang)
    await send_change_lang_menu(callback_query.message, lang, mode="edit")

    new_lang_text = await Translator.get_language_name(lang, lang)
    keyboard = await get_menu_keyboard(callback_query.from_user.id)
    await callback_query.message.answer(await f("language changed", lang, new_lang=new_lang_text), reply_markup=keyboard)


def register_settings_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        send_settings_button_handler,
        callback_mode="send_settings",
        state="*",
    )

    dp.register_callback_query_handler(
        settings_button_handler,
        callback_mode="settings",
        state=Settings.Action,
    )

    dp.register_callback_query_handler(
        edit_timezone_button_handler,
        callback_mode="edit_timezone",
        state=Settings.Action
    )

    dp.register_callback_query_handler(
        mailing_notifications_settings_button_handler,
        callback_mode="mailing_notifications_settings",
        state=Settings.Action,
    )

    dp.register_callback_query_handler(
        mailing_mode_button_handler,
        callback_mode="mailing_mode",
        state=Settings.MailingSettings,
    )

    dp.register_callback_query_handler(
        langs_list_button_handler,
        callback_mode="langs_list",
        state="*",
    )

    dp.register_callback_query_handler(
        change_lang_button_handler,
        callback_mode="change_lang",
        state="*",
    )
