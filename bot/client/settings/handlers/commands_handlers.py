from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.router import Router

from ..states import Settings


async def cmd_settings(message: types.Message, state: FSMContext, lang: str):
    await Settings.first()
    await Router.state_menu(message, state, lang)


def register_settings_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(cmd_settings, commands=["settings"], state="*")
