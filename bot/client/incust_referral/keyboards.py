from typing import Literal

import aiowhatsapp

from config import WEB_APP_PATH
from core.incust_referral.callback_data import IncustReferralShareMessage
from core.messangers_adapters.types import (
    InlineKeyboard, InlineKeyboardButton,
    UrlKeyboardButton,
)
from db.models import Brand, ClientBot
from utils.redefined_classes import InlineKb
from utils.text import f, fd


async def get_incust_share_keyboard(
        bot: ClientBot,
        brand: Brand,
        lang: str,
        ref_code: str,
        bot_type: Literal["telegram", "whatsapp"] = "telegram",
) -> InlineKb | aiowhatsapp.types.ReplyKeyboard:
    keyboard = InlineKeyboard()

    texts = await fd(
        {
            "share_friends": "incust loyalty referral invite friends button",
        }, lang
    )

    url_path = (
        f"{WEB_APP_PATH}/profile/share_and_earn?"
        f"bot_id={bot.id}&brand_id={brand.id}"
    )
    text = await f("incust loyalty referral learn more button", lang)

    callback_data = IncustReferralShareMessage(
        brand_id=brand.id,
        referral_code=ref_code,
    ).to_str()

    if bot_type == "telegram":
        keyboard.add_buttons(
            UrlKeyboardButton(
                text,
                url=url_path,
            ),
        )

    keyboard.add_buttons(
        InlineKeyboardButton(
            texts['share_friends'],
            callback_data
        ),
    )

    if bot_type == "telegram":
        return keyboard.to_telegram(row_width=1)
    return keyboard.to_whatsapp()
