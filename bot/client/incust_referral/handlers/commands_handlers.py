import logging

from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from client.main.keyboards import get_menu_keyboard
from core.referral.functions import share_and_earn

from core.incust.deep_links import IncustReferralDeepLink, IncustShareAndEarnDeepLink
from core.incust_referral.referral_processor import IncustReferralProcessor

from db.models import ClientBot, User, Brand

logger = logging.getLogger("debugger")


async def incust_referral_deep_link_handler(
    message: types.Message,
    state: FSMContext,
    data: IncustReferralDeepLink,
    bot: ClientBot,
    user: User, lang: str,
):
    ref_code = ''.join(i for i in data.referral_code if i.isdigit())
    logger.debug(f"{ref_code=}")
    keyboard = await get_menu_keyboard(user, bot)
    processor = IncustReferralProcessor(
        message, state, bot, user, lang,
        keyboard, ref_code,
        store_id=data.store_id,
    )
    await processor.process()


async def share_and_earn_deep_link_handler(
    message: types.Message,
    bot: ClientBot,
    user: User, lang: str,
):
    brand = await Brand.get_by_group(bot.group.id)
    await share_and_earn(user, brand, lang, bot)


def register_incust_referral_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(
        incust_referral_deep_link_handler,
        IncustReferralDeepLink.get_filter(return_field_name="data"),
        commands="start",
        state="*",
    )

    dp.register_message_handler(
        share_and_earn_deep_link_handler,
        IncustShareAndEarnDeepLink.get_filter(return_field_name="data"),
        commands="start",
        state="*",
    )
