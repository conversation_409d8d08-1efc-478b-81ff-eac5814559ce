from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from core.incust_referral.callback_data import IncustReferralCallbackData, IncustReferralQrOrLinkCallbackData, \
    IncustReferralShareMessage
from core.incust_referral.handlers import incust_referral_united_handler, incust_referral_qr_or_link_united_handler, \
    get_share_and_earn_message_united_handler
from db.models import User, ClientBot
from client.main.keyboards import get_menu_keyboard


async def incust_referral_handler(
        callback_query: types.CallbackQuery,
        incust_referral: IncustReferralCallbackData,
        user: User, state: FSMContext,
        lang: str, bot: ClientBot,
):
    keyboard = await get_menu_keyboard(user, bot)
    return await incust_referral_united_handler(
        callback_query.message, state, user, bot, lang, keyboard, incust_referral
    )


async def incust_referral_qr_or_link_handler(
        callback_query: types.CallbackQuery,
        i_ref_qr_or_link: IncustReferralQrOrLinkCallbackData,
        user: User, state: FSMContext,
        lang: str, bot: ClientBot,
):
    keyboard = await get_menu_keyboard(user, bot)
    return await incust_referral_qr_or_link_united_handler(
        callback_query.message, state, user, bot, lang, keyboard, i_ref_qr_or_link
    )


async def get_share_and_earn_message_handler(
        callback_query: types.CallbackQuery, lang: str, bot: ClientBot,
        user: User, incust_share_msg: IncustReferralShareMessage,
):
    await get_share_and_earn_message_united_handler(
        callback_query.message, lang, bot, user, incust_share_msg
    )


def register_incust_referral_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        incust_referral_handler,
        IncustReferralCallbackData.get_filter(),
        state="*",
    )

    dp.register_callback_query_handler(
        incust_referral_qr_or_link_handler,
        IncustReferralQrOrLinkCallbackData.get_filter(),
        state="*",
    )

    dp.register_callback_query_handler(
        get_share_and_earn_message_handler,
        IncustReferralShareMessage.get_filter(),
        state="*",
    )
