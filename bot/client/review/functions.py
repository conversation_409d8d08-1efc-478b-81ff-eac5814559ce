import logging

from aiogram.dispatcher import FSMContext
from psutils.state_router import Router

import config as cfg

from aiogram import types

from client.review.callback_data import LeaveReviewCallbackData
from client.review.states import MakeReview
from config import REVIEWS_LIST_PAGINATION_BUTTONS, HOW_MANY_SCROLLS, SERVICE_BOT_API_TOKEN, SERVICE_BOT_USERNAME
from core.custom_texts import ct
from core.kafka.producer.functions import add_telegram_notifications_for_action
from db.models import Group, Review, MenuInStore, User
from service.notifications.keyboards import get_service_bot_notification_base_keyboard
from utils.platform_admins import send_message_to_platform_admins

from utils.text import c, f
from utils.redefined_classes import InlineKb, InlineBtn, MenuKb
from utils.message import send_tg_message, send_or_edit_message

from client.db_funcs import get_reviews

from client.helpers import send_error


async def send_review_info(
        message: types.Message,
        review: Review, lang: str,
        keyboard: InlineKb | MenuKb | types.ReplyKeyboardRemove | None = None,
):
    text = await f("review info", lang, review=await review.review_view(lang), review_text=review.text)
    await send_tg_message(message.chat.id, review.content_type, keyboard=keyboard, text=text, **review.media)


async def show_reviews_stat(
        message: types.Message, group_id: int,
        bot_id: int, lang: str = cfg.DEFAULT_LANG,
        menu_in_store_id: int | None = None,
        edit_message: bool = False,
):
    group = await Group.get(group_id)
    # получаем строку звезды из локализации
    star = await f("review star", lang)
    # получаем максимальное к-во звёзд установленное в системе
    stars_count = int(await f("review stars count", lang))

    # получаем общее к-во отзывов
    all_reviews_count = await Review.stars_count(group_id, bot_id)
    # получаем среднюю оценку
    mark = round(await Review.get_mark(group_id, bot_id), 2)
    # формируем звёзды текстом
    stars_mark = await Review.get_mark_text(mark, lang)
    # формируем словарь типа {1: 0, 2: 0, ...} для того, чтобы звёзды, отзывов с которыми нету отображались
    stars_stats = dict(((i, 0) for i in range(1, stars_count + 1)))
    # получаем к-во отзывов с каждой звездой и обновляем словарь
    stars_stats.update(await Review.stars_stats(group_id, bot_id))
    # формируем строки для каждой звезды
    stars_states_list = list()
    for stars, count in stars_stats.items():
        stars = star * stars
        stars_states_list.append(await f("stars count text", lang, stars=stars, count=count))
    stars_stats_text = "\n".join(stars_states_list)
    message_text = await f(
        "reviews stats", lang,
        count=all_reviews_count,
        mark=mark, stars=stars_mark,
        group_name=group.name,
        reviews_stats=stars_stats_text,
    )

    keyboard = InlineKb()

    reviews_count = await get_reviews(group_id, bot_id, operation="count")
    button_text = await f("reviews list button", lang, count=reviews_count)
    keyboard.insert(InlineBtn(button_text, callback_data=c("reviews_list", group_id=group_id, bot_id=bot_id)))

    keyboard.insert(
        InlineBtn(
            await f("leave review button", lang),
            callback_data=LeaveReviewCallbackData(
                group_id=group_id,
                menu_in_store_id=menu_in_store_id,
            ).to_str(),
        )
    )

    if edit_message:
        await send_or_edit_message(
            message, message_text,
            keyboard=keyboard,
        )
    else:
        await message.answer(message_text, reply_markup=keyboard)


async def get_reviews_list_pagination(
        group_id: int, bot_id: int,
        position: int = 0,
        lang: str = cfg.DEFAULT_LANG,
) -> InlineKb:
    count: int = await get_reviews(group_id, bot_id, position, operation="count")
    keyboard = None
    btns = list()
    for i, btn in enumerate(REVIEWS_LIST_PAGINATION_BUTTONS):
        previous_btn = REVIEWS_LIST_PAGINATION_BUTTONS[i - 1] if i else 0
        if count > previous_btn:
            if btn > count:
                btn = count
            callback_data = c("reviews_list", group_id=group_id, bot_id=bot_id, position=position, limit=btn)
            btns.append(
                InlineBtn(
                    await f("btn text more", lang, count=btn),
                    callback_data=callback_data
                )
            )
    if btns:
        keyboard = InlineKb(row_width=5).add(*btns)
    return keyboard


async def send_reviews_list(
        message: types.Message,
        group_id: int, bot_id: int,
        position: int = 0,
        limit: int | str = "**default**",
        lang: str = cfg.DEFAULT_LANG,
):
    if limit == "**default**":
        limit = HOW_MANY_SCROLLS

    group = await Group.get(group_id)
    if not group:
        return await send_error(message)

    reviews = await get_reviews(group_id, bot_id, position, limit)
    if not reviews:
        return await message.answer(await f("empty reviews list", lang, group_name=group.name))

    for review in reviews:
        await send_review_info(message, review, lang)

    position += limit
    keyboard = await get_reviews_list_pagination(group_id, bot_id, position, lang)

    if keyboard:
        await message.answer(await f("text change page", lang), reply_markup=keyboard)


async def start_make_review(
        callback_or_message: types.CallbackQuery | types.Message,
        state: FSMContext, lang: str,
        group: Group, menu_in_store_or_id: MenuInStore | int | None = None,
        privacy: str = "public", mode: str = "stars"
):
    if isinstance(menu_in_store_or_id, MenuInStore):
        menu_in_store = menu_in_store_or_id
    elif menu_in_store_or_id:
        menu_in_store: MenuInStore | None = await MenuInStore.get(menu_in_store_or_id)
    else:
        menu_in_store = None

    menu_in_store_id = menu_in_store.id if menu_in_store else None

    new_data = dict(
        group_id=group.id, privacy=privacy, mode=mode,
        menu_in_store_id=menu_in_store_id
    )

    if menu_in_store and menu_in_store.comment:
        new_data["additional_text"] = menu_in_store.comment

    await state.update_data(new_data)
    await MakeReview.first()
    await Router.state_menu(callback_or_message, state, lang, set_state_message=True)


async def add_review_service_bot_notifications(
        review: Review,
        user: User,
        group: Group,
):
    async def get_sending_data(manager_user: User):
        manager_lang = manager_user.lang

        text = await ct(
            group, manager_lang,
            "review",
            "service_notification_text",
            review_id=review.id,
            text=review.text or "",
            additional_text=review.additional_text or "",
            name=user.name,
            group_name=group.name,
            review=await review.review_view(manager_lang)
        )

        sending_data = {
            "content_type": review.content_type,
            "text": text,
        }
        if review.content_type != "text":
            sending_data[review.content_type] = review.media.get(review.content_type)

        if review.bot_id:
            sending_data["keyboard"] = (await get_service_bot_notification_base_keyboard(
                user, group.id, review.bot_id, manager_lang,
            )).to_python()

        return sending_data

    try:
        await add_telegram_notifications_for_action(
            "service",
            SERVICE_BOT_USERNAME,
            SERVICE_BOT_API_TOKEN,
            action="crm_review:read",
            available_data={
                "profile_id": group.id,
                "review_id": review.id,
            },
            message=get_sending_data,
        )
    except Exception as e:
        logging.getLogger("error.review.add_review_service_bot_notifications").error(e, exc_info=True)
        await send_message_to_platform_admins(
            f"An error occurred while sending review notifications to service bot: {str(e)}\n"
            f"Review: #{review.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Review user: {user.name}({user.id})\n"
        )
