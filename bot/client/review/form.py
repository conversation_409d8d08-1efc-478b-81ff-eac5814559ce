from aiogram.dispatcher import FSMContext
from psutils.forms import WizardForm, fields

from client.review.states import MakeReview


async def mark_data_saver(data: dict, state: FSMContext):
    await state.update_data(review=data)


async def media_data_saver(data: dict, state: FSMContext, content_type: str | None = None):
    media: dict[str, str]
    text: str | None
    if "gallery" in data:
        gallery = data["gallery"]
        text = gallery.pop("text", None)

        if len(gallery) == 1 and len(gallery[(content_type := list(gallery.keys())[0])]) == 1:
            media = {content_type: gallery[content_type][0]}
        else:
            media = {"media_group": gallery}
    else:
        media = {content_type: data.get("media")}
        text = data.get("text")

    async with state.proxy() as state_data:
        state_data["media"] = media
        if text:
            state_data.text = (state_data.get("text", "") + text).strip()


class MakeReviewForm(WizardForm):
    state_group = MakeReview

    mark = fields.InlineButtonsField(
        callback_mode="review",
        callback_keys="*",
        data_saver=mark_data_saver,
    )
    text = fields.TextField()
    media = fields.GalleryField(
        caption_as_text=True,
        data_saver=media_data_saver,
    )
