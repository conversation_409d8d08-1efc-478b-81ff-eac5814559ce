from aiogram import types
from aiogram.dispatcher import FSMContext

from core.review.functions import create_review
from core.review.keyboards import get_cancel_review_keyboard, get_review_keyboard
from db.models import ClientBot, Group, MenuInStore, User, UserInGroup
from schemas import ReviewPrivacyEnum, ReviewTypeEnum
from utils.router import Router
from utils.text import f
from .functions import send_review_info, show_reviews_stat
from .states import MakeReview
from ..main.keyboards import get_menu_keyboard


async def send_mark_menu(
        message: types.Message, state: FSMContext, lang: str, bot: ClientBot
):
    state_data = await state.get_data()
    group_id, privacy, mode = state_data.get("group_id"), state_data.get(
        "privacy"
    ), state_data.get("mode")

    user, group = await User.get(message.chat.id), await Group.get(group_id)
    user_group_activity = await UserInGroup.get_or_create(user, group)
    if user_group_activity.is_user_blocked:
        return await message.answer(await f("user blocked", lang))

    if privacy == "public" or not mode:
        mode = "stars"

    message_text = await f(f"review {mode} header", lang)
    keyboard = await get_review_keyboard(mode, lang, bot.bot_type)

    return await message.answer(
        message_text,
        reply_markup=keyboard
    )


async def send_enter_text_menu(message: types.Message, lang: str, bot: ClientBot):
    return await message.edit_text(
        await f("review text header", lang),
        reply_markup=await get_cancel_review_keyboard(lang, bot_type=bot.bot_type),
    )


async def send_enter_media_menu(message: types.Message, lang: str, bot: ClientBot):
    return await message.edit_text(
        await f("review media header", lang),
        reply_markup=await get_cancel_review_keyboard(
            lang, bot_type=bot.bot_type, skip_button=True,
        ),
    )


async def save_review(message: types.Message, state: FSMContext, lang: str):
    ClientBot.set_current(None)
    bot_id = ClientBot.get_current_bot_id()
    bot = await ClientBot.get(bot_id)

    user = await User.get(message.chat.id)

    state_data = await state.get_data()
    await state.finish()

    group = await Group.get(group_id) if (
        group_id := state_data.get("group_id")) else None

    privacy = ReviewPrivacyEnum(state_data.get("privacy"))
    text = state_data.get("text", "")
    additional_text = state_data.get("additional_text")
    review = state_data.get("review", dict())
    media = state_data.get("media", dict())
    menu_in_store_id = state_data.get("menu_in_store_id")
    review_type = ReviewTypeEnum(state_data.get("mode"))

    review = await create_review(
        review_type, privacy, review, user, group, bot, text, additional_text, media
    )

    await message.edit_text(await f("review saved", lang))

    user_bot_activity = await bot.get_user_activity(user)
    if user_bot_activity.active_menu_in_store_id:
        menu_in_store = await MenuInStore.get(user_bot_activity.active_menu_in_store_id)
        keyboard = await get_menu_keyboard(user, bot, lang, menu_in_store)
    else:
        keyboard = None

    await send_review_info(message, review, lang, keyboard)

    if privacy == "public":
        await show_reviews_stat(
            message, group_id, bot.id, lang,
            menu_in_store_id
        )


def register_review_routes(router: Router):
    router.add_route(MakeReview.Mark, send_mark_menu)
    router.add_route(MakeReview.Text, send_enter_text_menu)
    router.add_route(MakeReview.Media, send_enter_media_menu)
    router.add_route(MakeReview.Save, save_review)
