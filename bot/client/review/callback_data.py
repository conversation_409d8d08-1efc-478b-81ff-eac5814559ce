from typing import Literal

from psutils.callback_data import CallbackData
from pydantic import Field


class LeaveReviewCallbackData(CallbackData, callback_mode="lrw"):  # lrw = leave_review
    group_id: int = Field(alias="gid")
    menu_in_store_id: int | None = Field(None, alias="mid")
    privacy: Literal["public", "private"] = Field("public", alias="p")
    review_mode: str = Field("stars", alias="rm")
