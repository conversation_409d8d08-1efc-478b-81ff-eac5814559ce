from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.types import ContentTypes

from core.bot.handlers import MenuButtonFilter
from db.models import ClientBot, User, UserAnalyticAction
from utils.router import Router
from ..states import MakeReview


async def leave_review_handler(
        message: types.Message, state: FSMContext, user: User, lang: str
):
    bot_from_db = await ClientBot.get_current()

    await state.finish()

    await state.update_data(group_id=bot_from_db.group_id, privacy="private")
    await state.update_data(
        mode=bot_from_db.group.review_type, bot_from_db_id=bot_from_db.id
    )

    await MakeReview.first()
    await message.delete()
    await Router.state_menu(state=state, lang=lang, set_state_message=True)

    await UserAnalyticAction.save_button_click(user, bot_from_db, "menu_leave_review")


def register_review_menu_handlers(dp: Dispatcher):
    dp.register_message_handler(
        leave_review_handler,
        MenuButtonFilter("main", "leave review button"),
        content_types=ContentTypes.TEXT,
        state="*",
    )
