from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import Group, UserAnalyticAction, ClientBot, User, MenuInStore

from utils.router import Router
from utils.text import f

from .united_handlers import handle_reviews_list, handle_reviews
from .. import show_reviews_stat
from ..callback_data import LeaveReviewCallbackData
from ..functions import start_make_review

from ...helpers import send_error

from ..states import MakeReview
from core.menu_in_store.functions import check_is_menu_in_store_one_button


async def leave_review_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, lrw: LeaveReviewCallbackData,
        user: User, lang: str,
):
    bot_from_db = await ClientBot.get_current()

    group = await Group.get(lrw.group_id)
    if not group:
        return await send_error(callback_query)

    await start_make_review(
        callback_query, state, lang, group,
        lrw.menu_in_store_id,
        lrw.privacy, lrw.review_mode,
    )

    await UserAnalyticAction.save_button_click(
        user,
        bot_from_db,
        "leave_review",
        group=group,
    )


async def reviews_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        user: User, lang: str,
):
    await handle_reviews(callback_query.message, callback_data, user, lang)


async def reviews_list_button_handler(callback_query: types.CallbackQuery, callback_data: dict, user: User, lang: str):
    await handle_reviews_list(callback_query.message, callback_data, user, lang)


async def cancel_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    bot_id = ClientBot.get_current_bot_id()
    state_data = await state.get_data()
    await state.finish()

    menu_in_store_id = state_data.get("menu_in_store_id")
    group_id = state_data.get("group_id")

    if state_data.get("privacy") == "public":
        await show_reviews_stat(
            callback_query.message, group_id, bot_id,
            lang, menu_in_store_id, edit_message=True,
        )
    elif menu_in_store_id:
        menu_in_store: MenuInStore = await MenuInStore.get(menu_in_store_id)
        if menu_in_store and not await check_is_menu_in_store_one_button(menu_in_store, "reviews"):
            await callback_query.message.delete()

    else:
        await callback_query.message.delete()
        await callback_query.message.answer(await f("action cancel text", lang))


async def skip_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await MakeReview.next()
    await Router.state_menu(callback_query, state, lang)


def register_review_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        leave_review_button_handler,
        LeaveReviewCallbackData.get_filter(),
        state="*",
    )

    dp.register_callback_query_handler(
        reviews_button_handler,
        callback_mode="reviews",
        state="*",
    )

    dp.register_callback_query_handler(
        reviews_list_button_handler,
        callback_mode="reviews_list",
        state="*",
    )

    dp.register_callback_query_handler(
        cancel_button_handler,
        cancel_button=True,
        state=MakeReview,
    )

    dp.register_callback_query_handler(
        skip_button_handler,
        skip_button=True,
        state=MakeReview,
    )
