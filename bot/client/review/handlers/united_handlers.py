from aiogram import types

from client.review import send_reviews_list, show_reviews_stat
from db.models import ClientBot, Group, UserAnalyticAction, User


async def __get_bot_and_group(callback_data: dict):
    bot = await ClientBot.get_current()
    group = await Group.get(callback_data.get("group_id"))
    return bot, group


async def handle_reviews_list(message: types.Message, callback_data: dict, user: User, lang: str):
    bot, group = await __get_bot_and_group(callback_data)

    position = callback_data.get("position", 0)
    limit = callback_data.get("limit", "**default**")

    await send_reviews_list(message, group.id, bot.id, position, limit, lang)

    if position > 0:
        await message.delete()
        await UserAnalyticAction.save_button_click(
            user,
            bot,
            "reviews_pagination",
            group=group,
            pagination_count=limit,
        )
    else:
        await UserAnalyticAction.save_button_click(
            user,
            bot,
            "reviews_list",
            group=group,
        )


async def handle_reviews(message: types.Message, callback_data: dict, user: User, lang: str):
    bot, group = await __get_bot_and_group(callback_data)
    menu_in_store_id = callback_data.get("menu_in_store_id")
    await show_reviews_stat(message, group.id, bot.id, lang, menu_in_store_id)

    await UserAnalyticAction.save_button_click(
        user,
        bot,
        "reviews",
        group=group,
    )
