import asyncio

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

import config as cfg
from client.main.functions import (
    send_hello_message, set_menu_button, start_thread_blocking_operation,
)
from client.main.keyboards import get_menu_keyboard
from core.chat import get_away_from_chat
from core.chat.virtual_manager.functions import start_virtual_manager_chat
from db import crud
from db.models import Brand, Cha<PERSON>, ClientBot, User
from friendly.draw.functions import cmd_start_draw
from utils.redefined_classes import InlineBtn, InlineKb
from utils.router import Router
from utils.text import f, replace_html_symbols
from ...keyboards import get_open_shop_web_and_webapp_keyboard


async def cmd_start(
        message: types.Message, state: FSMContext, user: User, bot: ClientBot, lang: str
):
    await state.finish()
    user_bot_activity = await user.activity_in_bot
    await user_bot_activity.update(active_menu_in_store_id=None)

    if "draw" in message.text:
        return await cmd_start_draw(message, user, lang)

    await send_hello_message(message.chat.id, bot, lang)

    if bot.on_join_virtual_manager_id:
        await start_virtual_manager_chat(
            user, bot.on_join_virtual_manager_id,
            group_or_id=bot.group_id,
            bot_or_id=bot,
        )
    else:
        await crud.delete_currently_running_vm_chats(user.id, bot.id)

    await set_menu_button(bot, user, lang)


async def cmd_cancel(message: types.Message, state: FSMContext, user: User, lang: str):
    await state.finish()
    user_bot_activity = await user.activity_in_bot
    if user_bot_activity.active_chat_id:
        return await get_away_from_chat(message)

    keyboard = await get_menu_keyboard(message.chat.id)
    await message.answer(await f("action cancel text", lang), reply_markup=keyboard)


async def cmd_state(message: types.Message, state: FSMContext, user: User, lang: str):
    user_bot_activity = await user.activity_in_bot

    fsm_data = await state.get_data()
    fsm_state = await state.get_state()
    if fsm_state is None:
        fsm_state = "without state"

    fsm_data.pop(Router.state_message_key, None)

    if user_bot_activity.active_chat_id:
        chat = await Chat.get(user_bot_activity.active_chat_id)
        chat_state = chat.type.value
        chat_data = {
            "group_id": chat.group_id,
            "bot_id": chat.bot_id,
        }
    else:
        chat_state = "without state"
        chat_data = None

    if message.text.endswith("*dev") or message.chat.id in cfg.DEVELOPERS_CHAT_IDS:
        text_variable = "client bot state full info"
    else:
        text_variable = "client bot state short info"
    text = await f(
        text_variable, lang, fsm_state=fsm_state, fsm_data=fsm_data,
        chat_state=chat_state, chat_data=chat_data
    )
    await message.answer(replace_html_symbols(text))


async def cmd_joke(message: types.Message):
    shit_message = await message.answer("🤡")
    await asyncio.sleep(2.5)
    await shit_message.edit_text("💩")


async def cmd_block_threads(message: types.Message):
    await start_thread_blocking_operation(message)


async def cmd_test_web_app(message: types.Message):
    args = message.get_args()

    keyboard = InlineKb()
    keyboard.insert(
        InlineBtn(
            "press me",
            web_app=types.WebAppInfo(
                url=args
            )
        )
    )

    await message.answer("press bellow", reply_markup=keyboard)


async def cmd_shop_web_app(message: types.Message):
    bot = await ClientBot.get_current()
    brand = await Brand.get_by_group(bot.group.id)
    if not brand:
        return await message.answer("No brand found")
    keyboard = await get_open_shop_web_and_webapp_keyboard(bot, brand.domain, "uk")

    await message.answer(
        f"Скопіювати пряме посилання:\n<code>"
        f"{brand.get_url()}</code>",
        reply_markup=keyboard,
    )


async def cmd_test_error(message: types.Message):
    _, *args = message.get_command()
    raise ValueError(message.get_command(" ".join(args)))


def register_main_commands_handlers(dp: Dispatcher):

    dp.register_message_handler(cmd_start, commands=["start"], state="*")
    dp.register_message_handler(cmd_cancel, commands=["cancel"], state="*")
    dp.register_message_handler(cmd_state, commands=["state", "state*dev"], state="*")
    dp.register_message_handler(cmd_joke, commands=["joke"], state="*")
    dp.register_message_handler(
        cmd_block_threads, commands=["block_threads"], state="*"
    )
    dp.register_message_handler(cmd_test_web_app, commands="test", state="*")
    dp.register_message_handler(cmd_shop_web_app, commands="shop", state="*")
    dp.register_message_handler(cmd_test_error, commands="test_error", state="*")
