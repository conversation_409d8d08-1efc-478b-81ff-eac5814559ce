from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.types import ContentTypes

from client.main.keyboards import get_menu_keyboard
from core.chat import get_away_from_chat, user_to_chat
from core.chat.functions import message_from_user_to_group_handler
from db.models import ClientBot
from schemas import ChatT<PERSON><PERSON>num
from utils.text import f


async def unhandled_get_away_from_chat_button_handler(
        message: types.Message, state: FSMContext, lang: str
):
    bot_from_db = await ClientBot.get_current()

    await state.finish()
    await get_away_from_chat(message)

    keyboard = await get_menu_keyboard(message.chat.id, bot_from_db)
    name = message.text.replace(
        await f("get away from chat button", lang, receiver=""), ""
    )
    await message.answer(
        await f("get away from chat text", lang, name=name), reply_markup=keyboard
    )


async def unhandled_messages_handler(message: types.Message, bot: ClientBot, **kwargs):
    chat = await user_to_chat(
        ChatTypeEnum.USER_WITH_GROUP, message,
        group_id=bot.group_id,
    )
    await message_from_user_to_group_handler(message, bot=bot, chat=chat, **kwargs)


def register_main_message_handlers(dp: Dispatcher):

    dp.register_message_handler(
        unhandled_get_away_from_chat_button_handler,
        lstarts="get away from chat button",
        lkwargs=dict(receiver=""),
        content_types=ContentTypes.TEXT,
        state="*",
    )
    dp.register_message_handler(
        unhandled_messages_handler, content_types=ContentTypes.ANY, state="*"
    )
