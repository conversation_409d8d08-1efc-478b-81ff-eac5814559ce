from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext
from db.models import ClientBot, Brand, User

from friendly.draw.handlers import update_conditions_info_button as update_conditions_info_button_
from friendly.draw.handlers import get_followed_link_message_button as get_followed_link_message_button_

from .menu_handlers import profile_button_handler, wallet_button_handler

from core.referral.functions import share_and_earn
from .menu_handlers import benefits_button_handler


async def update_conditions_info_button(
    callback_query: types.CallbackQuery,
    callback_data: dict,
    lang: str
):
    await update_conditions_info_button_(callback_query, callback_data, lang)


async def get_followed_link_message_button(
    callback_query: types.CallbackQuery,
    callback_data: dict,
    lang: str
):
    await get_followed_link_message_button_(callback_query, callback_data, lang)


async def vm_profile_button_handler(callback_query: types.CallbackQuery, user: User, state: FSMContext, lang: str):
    await profile_button_handler(message=callback_query.message, user=user, state=state, lang=lang)


async def vm_wallet_button_handler(callback_query: types.CallbackQuery, user: User, lang: str):
    return await wallet_button_handler(callback_query.message, user, lang)


async def vm_referal_button_handler(
    callback_query: types.CallbackQuery,
    user: User, lang: str,
):
    bot = await ClientBot.get_current()
    brand = await Brand.get_by_group(bot.group.id)
    await share_and_earn(user, brand, lang, bot)


async def show_my_card_incust(
    callback_query: types.CallbackQuery,
    state: FSMContext,
    user: User, lang: str,
):
    bot = await ClientBot.get_current()
    await benefits_button_handler(callback_query.message, state, user, bot, lang)
    await callback_query.message.delete()


def register_main_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        update_conditions_info_button,
        callback_mode="update_conditions_info",
        chat_type="private",
        state="*",
    )

    dp.register_callback_query_handler(
        get_followed_link_message_button,
        callback_mode="get_followed_link_message",
        chat_type="private",
        state="*",
    )

    dp.register_callback_query_handler(
        vm_profile_button_handler,
        callback_mode="vm_profile",
        state="*",
    )

    dp.register_callback_query_handler(
        vm_wallet_button_handler,
        callback_mode="vm_wallet",
        state="*",
    )

    dp.register_callback_query_handler(
        vm_referal_button_handler,
        callback_mode="vm_referal",
        state="*",
    )

    dp.register_callback_query_handler(
        show_my_card_incust,
        callback_mode="my_card_incust",
        state="*",
    )
