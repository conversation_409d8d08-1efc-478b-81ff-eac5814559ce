from aiogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery


def main_menu():
    menu = InlineKeyboardMarkup(row_width=3)

    my_card = InlineKeyboardButton('Моя картка 📝', callback_data='my_card')
    wallet = InlineKeyboardButton('Гаманець 💰', callback_data='wallet')
    search = InlineKeyboardButton('Пошук 🔍', callback_data='search')
    contact = InlineKeyboardButton('Зв\'язатися з нами 📞', callback_data='contact')
    enter_code = InlineKeyboardButton('Ввести код 🔢', callback_data='enter_code')
    settings = InlineKeyboardButton('Налаштування ⚙️', callback_data='settings')
    feedback = InlineKeyboardButton('Відправити відгук 📩', callback_data='feedback')

    menu.add(my_card, wallet, search)
    menu.add(contact)
    menu.add(enter_code, settings)
    menu.add(feedback)

    return menu


async def show_menu(message: Message):
    menu = main_menu()
    await message.answer(text='Головне меню:', reply_markup=menu)


# Функції-обробники для кнопок
async def my_card_handler(callback_query: CallbackQuery):
    await callback_query.message.edit_text(text='Обробник для кнопки "Моя картка"')
    await show_menu(callback_query.message)


async def wallet_handler(callback_query: CallbackQuery):
    await callback_query.message.edit_text(text='Обробник для кнопки "Гаманець"')
    await show_menu(callback_query.message)


async def search_handler(callback_query: CallbackQuery):
# Обробник для кнопки "Пошук"
    search_menu = InlineKeyboardMarkup(row_width=2)
    search_menu.add(
        InlineKeyboardButton('Шукати поруч', callback_data='search_near'),
        InlineKeyboardButton('🏠', callback_data='back_to_menu'),
        )

    await callback_query.message.edit_text(
        text='✍️Введіть текст для пошуку:',
        reply_markup=search_menu
    )
  

async def contact_handler(callback_query: CallbackQuery):
    contact_menu = InlineKeyboardMarkup(row_width=1)
    contact_menu.add(
        InlineKeyboardButton('🏠', callback_data='back_to_menu'),
        )

    await callback_query.message.edit_text(
        text='✍️Введіть повідомлення, яке слід відправити',
        reply_markup=contact_menu
    )


async def enter_code_handler(callback_query: CallbackQuery):
    enter_code_menu = InlineKeyboardMarkup(row_width=1)
    enter_code_menu.add(
        InlineKeyboardButton('Сканувати', callback_data='scan_code'),
    )
    enter_code_menu.add(
        InlineKeyboardButton('🏠', callback_data='back_to_menu'),
        )


    await callback_query.message.edit_text(
        text='Ви збираєтеся прийняти рекомендацію, додати собі в гаманець купон, сертифікат або подарунок. Введіть або відскануйте код',
        reply_markup=enter_code_menu
    )


async def settings_handler(callback_query: CallbackQuery):
    settings_menu = InlineKeyboardMarkup(row_width=3)
    settings_menu.add(
        InlineKeyboardButton('Персональні відомості', callback_data='user_info'),)
    settings_menu.add(
        InlineKeyboardButton('Сповіщення', callback_data='messages'),)    
    settings_menu.add(
        InlineKeyboardButton('Мова', callback_data='set_lang'),
        InlineKeyboardButton('Вийти', callback_data='user_exit'),
        InlineKeyboardButton('🏠', callback_data='back_to_menu'),
        )

    await callback_query.message.edit_text(
        text='Профіль користувача',
        reply_markup=settings_menu
    )


async def feedback_handler(callback_query: CallbackQuery):
    feedback_menu = InlineKeyboardMarkup(row_width=3)
    feedback_menu.add(
        InlineKeyboardButton('5', callback_data='feedback_5'),
        InlineKeyboardButton('4', callback_data='feedback_4'),)    
    feedback_menu.add(
        InlineKeyboardButton('3', callback_data='feedback_3'),
        InlineKeyboardButton('2', callback_data='feedback_2'),)
    feedback_menu.add(
        InlineKeyboardButton('1', callback_data='feedback_1'),    
        InlineKeyboardButton('🏠', callback_data='back_to_menu'),
        )

    await callback_query.message.edit_text(
        text='Оцініть обслуговування! Ми хочемо вдосконалюватися, і ваш відгук важливий для нас!',
        reply_markup=feedback_menu
    )


async def back_to_main_menu(callback_query: CallbackQuery):
    await callback_query.message.edit_text(text='Головне меню:', reply_markup=main_menu())