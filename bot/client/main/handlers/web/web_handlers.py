from functools import partial

from aiohttp import web
from aiogram import types, Dispatcher

from core.helpers import process_update


async def updates_handler(request: web.Request, dp: Dispatcher):
    bot_from_db_id, token = request.match_info.get("bot_id"), request.match_info.get("bot_token")
    json_data = await request.json()
    update = types.Update.to_object(json_data)
    try:
        await process_update(dp, update, bot_from_db_id, token)
    finally:
        return web.Response()


def register_main_web_handlers(app: web.Application, dp: Dispatcher):
    handler = partial(updates_handler, dp=dp)
    app.router.add_post("/client_bot/{bot_id}/{bot_token}", handler)
