from aiogram import types, Dispatcher

from db.models import Group
from utils.filters.simple import not_private_chat_filter


async def leave_group(message: types.Message):
    if not await Group.check_simplified_chat(message.chat.id):
        await message.chat.leave()


def register_main_group_message_handlers(dp: Dispatcher):
    dp.register_message_handler(leave_group, not_private_chat_filter, content_types=types.ContentTypes.ANY, state="*")
