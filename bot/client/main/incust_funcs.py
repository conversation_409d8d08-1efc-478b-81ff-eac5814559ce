import asyncio
import logging
from typing import Literal

import aiowhatsapp as wa
from aiogram import types
from aiowhatsapp import types as wa_types
from incust_api.api import term

import schemas
from config import P4S_API_URL
from core import messangers_adapters as ma
from core.incust.types import IncustCustomerInfo
from core.loyalty.incust_api import incust
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db import crud
from db.models import Brand, ClientBot, User
from schemas import ShowIncustQrCardSchema
from utils.text import f, html_to_markdown
from .keyboards import get_incust_my_card_keyboard


async def show_card(
        message: types.Message | None,
        result: ShowIncustQrCardSchema,
        lang: str,
        incust_customer: IncustCustomerInfo | None = None,
        user: User | None = None,
        brand: Brand | None = None,
):
    message_text = ""
    title = None
    card_image_url = None

    bot = await ClientBot.get_current()
    if not user:
        user = await User.get_current()

    # Перевіряємо наявність інформації про категорію карти
    # Оригінальна CardInfo з нових клієнтів може мати іншу структуру
    if incust_customer and incust_customer.card_info:
        # Перевіряємо чи є customer.card_category в оригінальній схемі
        if (hasattr(incust_customer.card_info, 'customer') and
                hasattr(incust_customer.card_info.customer, 'card_category') and
                incust_customer.card_info.customer.card_category):

            card_category = incust_customer.card_info.customer.card_category
            title = (card_category.title
                     if hasattr(card_category, 'title') and card_category.title and
                        hasattr(
                            card_category, 'show_to_customers'
                        ) and card_category.show_to_customers
                     else None)
            card_image_url = (card_category.image
                              if hasattr(
                card_category, 'image'
            ) and card_category.image and
                                 hasattr(
                                     card_category, 'show_to_customers'
                                 ) and card_category.show_to_customers
                              else None)

    if result.text:
        message_text += result.text

    if title:
        message_text += (f"\n"
        f"{await f('client bot incust card title text', lang, title=title)}")

    if result.temp_code:
        if bot.bot_type == "whatsapp":
            message_text += f"\n{await f('wa client bot temp code text', lang, temp_code=result.temp_code)}"
        else:
            message_text += f"\n{await f('client bot temp code text', lang, temp_code=result.temp_code)}"

    if bot.bot_type == "whatsapp":
        message_text = html_to_markdown(message_text)
        to = ma.get_user_to(user, bot.bot_type)
        if not to:
            debugger = logging.getLogger("debugger")
            debugger.debug(f"User is not in bot {bot.bot_type}")
            return None
        wa_bot = wa.WhatsappBot(bot.token, bot.whatsapp_from)

        if result.photo:
            document_media = await wa_bot.upload_media(result.photo, "image/png")
            image = wa_types.MediaCaption(id=document_media.id, caption=None)
            await wa_bot.send_image(
                user.wa_phone,
                image=image,
            )

        if card_image_url:
            document_media = await wa_bot.upload_media(card_image_url, "image/png")
            image = wa_types.MediaCaption(id=document_media.id, caption=None)
            await wa_bot.send_image(
                user.wa_phone,
                image=image,
            )

        await wa_bot.send_message(
            user.wa_phone,
            message_text,
            keyboard=await get_wa_menu_keyboard(user, bot, lang)
        )
        # not awaited to not block the main thread, result of this function not necessary
        asyncio.ensure_future(
            show_card_timeout(
                None, None, wa_bot, user.wa_phone, bot.bot_type, lang, brand,
            )
        )
        return

    msgs_to_delete = []
    if result.photo:
        photo_msg_1 = await message.answer_photo(photo=result.photo, caption=None)
        msgs_to_delete.append(photo_msg_1)

    if card_image_url:
        photo_msg_2 = await message.answer_photo(photo=card_image_url, caption=None)
        msgs_to_delete.append(photo_msg_2)

    msg = await message.answer(text=message_text)

    # not awaited to not block the main thread, result of this function not necessary
    asyncio.ensure_future(
        show_card_timeout(
            msgs_to_delete, msg, None, None, bot.bot_type, lang,
        )
    )


async def show_card_timeout(
        msgs_to_delete: list[types.Message] | None,
        msg_to_edit: types.Message | None,
        wa_bot: wa.WhatsappBot | None,
        wa_user_phone: str | None,
        bot_type: Literal["whatsapp", "telegram"],
        lang: str,
        brand: Brand | None = None,
):
    await asyncio.sleep(600)
    try:
        keyboard = await get_incust_my_card_keyboard(lang, bot_type)
        text = await f("loyalty my card timeout message", lang)

        if bot_type == "telegram":
            if msgs_to_delete:
                for msg in msgs_to_delete:
                    await msg.delete()
            if msg_to_edit:
                await msg_to_edit.edit_text(text)
                await msg_to_edit.edit_reply_markup(keyboard)
        elif bot_type == "whatsapp":
            if wa_bot and wa_user_phone:
                photo = None
                if brand:
                    loyalty_settings = await crud.get_loyalty_settings_for_context(
                        "brand",
                        schemas.LoyaltySettingsData(brand_id=brand.id)
                    )

                    if loyalty_settings:
                        try:
                            async with incust.term.SettingsApi(loyalty_settings, lang=lang) as api:
                                terminal_settings = await api.settings()

                            # Перевіряємо чи є фотографії в налаштуваннях лояльності
                            if (terminal_settings and
                                    terminal_settings.loyalty and
                                    terminal_settings.loyalty.photos and
                                    len(terminal_settings.loyalty.photos) > 0):
                                photo = terminal_settings.loyalty.photos[0]

                        except term.ApiException as ex:
                            logging.error(f"Error getting terminal settings: {ex.reason}")
                        except Exception as e:
                            logging.error(
                                f"Failed to get terminal settings: {e}", exc_info=True
                            )

                if not photo:
                    photo = f"{P4S_API_URL}/static/images/7loc-com-white-square-preview.jpg"

                await wa_bot.send_image(
                    wa_user_phone,
                    image=photo,
                    caption=text,
                    reply_markup=keyboard,
                )
    except Exception as ex:
        logging.error(f"*** SHOW CARD TIMEOUT {ex}", exc_info=True)
