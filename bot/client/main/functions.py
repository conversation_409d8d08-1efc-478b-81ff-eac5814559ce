import asyncio
import os

from aiogram import types
from psutils.decorators import sync_to_async
from psutils.web_app import check_if_changed_and_set_menu_button

from core.chat.functions import get_business_name_from_bot
from core.custom_texts import ct
from core.custom_texts.models import ClientBotCustomTextsModel
from db import crud
from db.models import Brand, ClientBot, User
from lip_lep.functions import get_lip_lep_menu_button
from utils.message import send_tg_message
from utils.redefined_classes import Bo<PERSON>, InlineKb, MenuKb
from .keyboards import get_menu_keyboard


async def send_hello_message(
        user_or_chat_id: User | int,
        bot: ClientBot,
        lang: str,
        keyboard: MenuKb | InlineKb | types.ReplyKeyboardRemove | None = None,
        message_key: str = "hello text"
):
    user: User = await User.get(user_or_chat_id) if isinstance(
        user_or_chat_id, int
    ) else user_or_chat_id

    if not keyboard:
        keyboard = await get_menu_keyboard(user, bot)

    ct_obj = await ClientBotCustomTextsModel.from_object(bot)

    brand, business_name = await get_business_name_from_bot(bot)

    hello_message_args = {
        "text": await ct(
            ct_obj,
            lang,
            "main",
            message_key,
            user_name=user.name,
            business_name=business_name,
        ),
        "keyboard": keyboard
    }

    await send_tg_message(user.chat_id, "text", **hello_message_args)


@sync_to_async
def blocking_code(seconds: int):
    import time
    time.sleep(seconds)
    return


async def start_thread_blocking_operation(message: types.Message):
    split_message_text = message.text.split(" ")
    if len(split_message_text) >= 2 and split_message_text[1].isdecimal():
        threads_count = int(split_message_text[1])
    else:
        threads_count = os.cpu_count()
    if len(split_message_text) == 3 and split_message_text[2].isdecimal():
        seconds = int(split_message_text[2])
    else:
        seconds = 5
    await message.answer(
        f"{threads_count} threads is blocking now for {seconds} seconds"
    )
    coros = [blocking_code(seconds) for _ in range(1, threads_count + 1)]
    await asyncio.gather(*coros, return_exceptions=False)
    await message.answer(f"{threads_count} threads was blocked for {seconds} seconds")


async def set_menu_button(
        bot: ClientBot,
        user: User = None,
        lang: str = None,
        active_menu_in_store_id: int | None = None,
):
    if bot.bot_type != "telegram":
        return

    if user and not active_menu_in_store_id:
        user_bot_activity = await bot.get_user_activity(user)
        active_menu_in_store_id = user_bot_activity.active_menu_in_store_id

    if not user or active_menu_in_store_id:
        new_button = types.MenuButtonDefault()
    else:
        lang = lang or bot.lang
        brand = await Brand.get(group_id=bot.group_id)
        stores = await crud.get_stores(brand.id) if brand else []

        ct_obj = await ClientBotCustomTextsModel.from_object(bot)

        if (custom_button := await ct(
                ct_obj, lang,
                "additional", "main button",
                "custom button",
        )) and (custom_url := await ct(
            ct_obj, lang,
            "additional", "main button",
            "custom url"
        )):
            new_button = types.MenuButtonWebApp(
                text=custom_button,
                web_app=types.WebAppInfo(
                    url=custom_url,
                )
            )
        elif bot.is_lip_lep:
            new_button = await get_lip_lep_menu_button(user.chat_id, lang)
        elif brand and stores and (
                shop_button := await ct(ct_obj, lang, "shop", "button")):
            url = brand.get_url(bot_id=bot.id)
            new_button = types.MenuButtonWebApp(
                text=shop_button,
                web_app=types.WebAppInfo(
                    url=url,
                )
            )
        else:
            new_button = types.MenuButtonDefault()

    if new_button.type != "web_app" or new_button.text:
        aiogram_bot = Bot.get_current()
        with aiogram_bot.with_token(bot.token):
            user_chat_id = user.chat_id if user else None
            await check_if_changed_and_set_menu_button(
                aiogram_bot, new_button, user_chat_id
            )
