from aiogram import types
from aiogram.dispatcher.filters import Filter
from aiogram.dispatcher.handler import ctx_data

from core.menu_in_store.keyboards import get_active_menu_open_button_text
from db.models import User, MenuInStore, ClientBot


class ActiveMenuInStoreButtonFilter(Filter):

    async def check(self, message: types.Message) -> bool | dict:
        data = ctx_data.get()
        user: User = data.get("user")
        lang: str = data.get("lang")
        bot = await ClientBot.get_current()

        user_bot_activity = await user.activity_in_bot
        if not user_bot_activity.active_menu_in_store_id:
            return False

        menu_in_store: MenuInStore = await MenuInStore.get(
            user_bot_activity.active_menu_in_store_id
        )
        if not menu_in_store:
            return False

        check_text = await get_active_menu_open_button_text(menu_in_store, lang)
        if message.text.lower().strip() == check_text.lower().strip():
            return {"menu_in_store": menu_in_store}

        return False
