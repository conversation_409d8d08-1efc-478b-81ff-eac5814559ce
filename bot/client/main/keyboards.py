from typing import Literal

from aiogram import types

from core.custom_texts import ct, ict
from core.custom_texts.models import CustomTextsModel
from core.incust_referral.functions import is_incust_referral_active
from core.menu_in_store.keyboards import get_active_menu_open_button_text
from core.messangers_adapters.types import InlineKeyboard, InlineKeyboardButton
from core.store.functions.brand import get_brand_scan_receipts_settings
from db import crud
from db.models import Brand, ClientBot, CustomMenuButton, MenuInStore, User
from utils.keyboards import make_share_button
from utils.redefined_classes import InlineBtn, InlineKb, MenuBtn, MenuKb
from utils.text import f
from utils.translator import t


async def get_menu_keyboard(
        user: int | User,
        bot: ClientBot | int = None,
        lang: str | None = None,
        menu_in_store: MenuInStore | None = None,
        need_active_menu_in_store_button: bool = True,
) -> MenuKb | types.ReplyKeyboardRemove:
    if bot is None:
        bot = await ClientBot.get_current()
    elif not isinstance(bot, ClientBot):
        bot = await ClientBot.get(bot)

    if not bot:
        raise ValueError("Bot must be specified")

    user = user if isinstance(user, User) else await User.get(user)

    if not user:
        raise ValueError("User must be specified")

    if bot.is_lip_lep:
        return types.ReplyKeyboardRemove()

    if not lang:
        lang = await user.get_lang(bot.id)

    user_bot_activity = await bot.get_user_activity(user)

    if not menu_in_store:
        if user_bot_activity.active_menu_in_store_id:
            menu_in_store = await MenuInStore.get(
                user_bot_activity.active_menu_in_store_id
            )

    ct_obj = await CustomTextsModel.from_object(bot)
    keyboard = MenuKb(resize_keyboard=True, row_width=3, one_time_keyboard=False)

    if menu_in_store:
        is_payment_bot_menu = menu_in_store.group_id != bot.group_id

        menu_in_store_ct_obj = await CustomTextsModel.from_object(menu_in_store)
        if not menu_in_store.need_save_as_active:
            await user_bot_activity.update(active_menu_in_store_id=None)
        elif need_active_menu_in_store_button:
            keyboard.row(
                MenuBtn(await get_active_menu_open_button_text(menu_in_store, lang))
            )
    else:
        menu_in_store_ct_obj = None
        is_payment_bot_menu = False

    buttons = list()

    brand = await Brand.get_by_group(bot.group.id)
    if brand and bot.is_show_order_button and (
            not menu_in_store or
            not any(
                (
                        await ict(menu_in_store_ct_obj, "menu", "order button"),
                        await ict(
                            menu_in_store_ct_obj, "menu", "delivery pickup button"
                        ),
                )
            )
    ):
        buttons.append(MenuBtn(await ct(ct_obj, lang, "shop", "button")))

    group = bot.group
    for button in await CustomMenuButton.get_list(bot_id=bot.id):
        if group.is_translate:
            buttons.append(
                MenuBtn(
                    await t(
                        button, lang, group.lang,
                        field_name="text",
                        group_id=group.id,
                        is_auto_translate_allowed=group.is_translate,
                    )
                )
            )
        else:
            buttons.append(MenuBtn(button.text))

    if bot.group.review_type is not None and bot.group.review_type != "off" and (
            is_payment_bot_menu or
            not menu_in_store or
            not await ict(menu_in_store_ct_obj, "contacts", "reviews button")
    ):
        buttons.append(MenuBtn(await ct(ct_obj, lang, "main", "leave review button")))

    if (
            is_payment_bot_menu or
            not menu_in_store or
            not await ict(menu_in_store_ct_obj, "contacts", "chat button")
    ):
        buttons.append(MenuBtn(await ct(ct_obj, lang, "chat", "start button")))

    buttons.append(MenuBtn(await ct(ct_obj, lang, "main", "my qr", "show button")))

    if brand:
        buttons.append(MenuBtn(await ct(ct_obj, lang, "main", "profile button")))

        if await brand.is_incust:
            buttons.append(MenuBtn(await ct(ct_obj, lang, "main", "benefits button")))
            is_referral_active = await is_incust_referral_active(user, brand, lang)
            if is_referral_active:
                buttons.append(
                    MenuBtn(await ct(ct_obj, lang, "shop", "share and earn button"))
                )

            buttons.append(MenuBtn(await ct(ct_obj, lang, "main", "wallet button")))

            scan_settings = await get_brand_scan_receipts_settings(brand.id)
            if scan_settings.enabled:
                buttons.append(
                    MenuBtn(await ct(ct_obj, lang, "main", "scan receipt button"))
                )

            ewallets = await crud.get_ewallet_list(bot_id=bot.id, user_id=user.id, operation="count")
            if ewallets:
                buttons.append(
                    MenuBtn(await ct(ct_obj, lang, "main", "ewallet button"))
                )

    buttons = [button for button in buttons if button.text]

    share_bot_button_text = await ct(ct_obj, lang, "main", "share bot button")
    share_bot_button = MenuBtn(share_bot_button_text) if share_bot_button_text else ""

    if not buttons:
        if share_bot_button:
            return MenuKb().insert(share_bot_button)
        return types.ReplyKeyboardRemove()

    if len(buttons) < 4:
        keyboard.row(*buttons)
        keyboard.row(share_bot_button)
        return keyboard

    if len(buttons) <= 6:
        first_row_len = len(buttons) // 2
        keyboard.row(*buttons[:first_row_len])
        keyboard.row(*buttons[first_row_len:])
        keyboard.row(share_bot_button)
        return keyboard

    if not await user.get_shown_client_bot_menu(bot.id):
        first_row = buttons[:2] + [MenuBtn(await f("show full menu button", lang))]
        keyboard.row(*first_row)
        return keyboard

    buttons.append(MenuBtn(await f("hide full menu button", lang)))

    rows: list[list[MenuBtn]] = list()

    for i in range(0, len(buttons), 3):
        if i == 0:
            rows.append(buttons[-3:])
        else:
            rows.insert(0, buttons[-i - 3:-i])

    if len(rows[0]) == 1:
        rows[0].append(rows[1][0])
        rows[1] = rows[1][1:]

    for row in rows:
        keyboard.row(*row)

    keyboard.row(share_bot_button)

    return keyboard


async def make_web_app_keyboard(
        url_path: str, text: str, keyboard: InlineKb = None
) -> InlineKb:
    if not keyboard:
        keyboard = InlineKb()

    keyboard.insert(
        InlineBtn(
            text,
            web_app=types.WebAppInfo(
                url=url_path
            ),
        )
    )
    return keyboard


async def get_open_profile_keyboard(bot: ClientBot, lang: str) -> InlineKb | None:
    brand = await crud.get_brand_by_group(bot.group_id)
    if not brand:
        return None

    return await make_web_app_keyboard(
        url_path=brand.get_url("profile"),
        text=await f("open profile button", lang)
    )


async def get_open_shop_keyboard(bot: ClientBot, lang: str, brand: Brand) -> InlineKb:
    keyboard = InlineKb(row_width=3)

    ct_obj = await CustomTextsModel.from_object(bot)
    text = await ct(ct_obj, lang, "shop", "button")
    keyboard = await make_web_app_keyboard(
        brand.get_url(bot_id=bot.id), text, keyboard=keyboard
    )

    return keyboard


async def get_open_shop_web_and_webapp_keyboard(
        bot: ClientBot | None, brand: Brand,
        lang: str
):
    keyboard = InlineKb()
    if bot:
        keyboard = await get_open_shop_keyboard(bot, lang, brand)

    return keyboard


async def get_share_referral_keyboard(lang: str, link: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)

    button_text = await f("share link button", lang)
    keyboard.insert(await make_share_button(button_text, link))

    return keyboard


async def get_open_share_and_earn_keyboard(
        bot: ClientBot, lang: str, brand: Brand
) -> InlineKb:
    keyboard = InlineKb(row_width=1)

    text = await f("incust loyalty referral learn more button", lang)
    keyboard = await make_web_app_keyboard(
        brand.get_url("profile/share_and_earn", bot_id=bot.id), text, keyboard=keyboard
    )

    return keyboard


async def get_open_share_and_earn_web_and_webapp_keyboard(
        bot: ClientBot | None, brand: Brand,
        lang: str
):
    keyboard = InlineKb()
    if bot:
        keyboard = await get_open_share_and_earn_keyboard(bot, lang, brand)

    return keyboard


async def get_open_scan_receipt_keyboard(
        bot: ClientBot, lang: str, brand: Brand
) -> InlineKb:
    keyboard = InlineKb(row_width=1)

    text = await f("web app scan receipt button", lang)
    keyboard = await make_web_app_keyboard(
        brand.get_url(bot_id=bot.id, scan_receipt=1), text, keyboard=keyboard
    )

    return keyboard


async def get_incust_my_card_keyboard(
        lang: str, bot_type: Literal["telegram", "whatsapp"] = "telegram"
):
    keyboard = InlineKeyboard()
    text = await f("loyalty my card timeout button", lang)
    keyboard.add_buttons(
        InlineKeyboardButton(
            text,
            "my_card_incust:"
        )
    )

    if bot_type == "telegram":
        return keyboard.to_telegram(row_width=1)
    elif bot_type == "whatsapp":
        return keyboard.to_whatsapp()
