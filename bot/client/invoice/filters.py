from datetime import datetime

from aiogram import types, Dispatcher
from aiogram.dispatcher.filters import Bound<PERSON>ilter

from db.models import Invoice

from utils.text import parse_callback_data


class InvoiceAlreadyPayedFilter(BoundFilter):

    key = "is_invoice_already_payed"

    def __init__(self, is_invoice_already_payed: bool = True):

        if is_invoice_already_payed is not True:
            raise ValueError("Argument is_invoice_already_payed must be True")

    async def check(self, query: types.ShippingQuery | types.PreCheckoutQuery) -> bool:
        mode, callback_data = parse_callback_data(query.invoice_payload)
        invoice = await Invoice.get(callback_data.get("invoice_id"))
        return invoice.status != "not_payed"


class InvoiceExpiredFilter(BoundFilter):

    key = "is_invoice_expired"

    def __init__(self, is_invoice_expired: bool = True):

        if is_invoice_expired is not True:
            raise ValueError("Argument is_invoice_expired must be True")

    async def check(self, query: types.ShippingQuery | types.PreCheckoutQuery) -> bool:
        mode, callback_data = parse_callback_data(query.invoice_payload)
        invoice = await Invoice.get(callback_data.get("invoice_id"))
        return invoice.expiration_datetime and invoice.expiration_datetime < datetime.utcnow()


class InvoiceDeadFilter(BoundFilter):

    key = "is_invoice_dead"

    def __init__(self, is_invoice_dead: bool = True):

        if is_invoice_dead is not True:
            raise ValueError("Argument is_invoice_dead must be True")

    async def check(self, query: types.ShippingQuery | types.PreCheckoutQuery) -> bool:
        mode, callback_data = parse_callback_data(query.invoice_payload)
        invoice = await Invoice.get(callback_data.get("invoice_id"))
        return invoice.live_time is not None and invoice.time_created + invoice.live_time <= datetime.utcnow()


class PayloadModeFilter(BoundFilter):
    key = "payload_mode"

    def __init__(self, payload_mode: str):
        if type(payload_mode) is not str:
            raise ValueError(f"Argument payload_mode must be a string not {type(payload_mode)}")

        self.payload_mode = payload_mode

    async def check(self, obj: types.Message | types.ShippingQuery | types.PreCheckoutQuery) -> bool:
        if type(obj) is types.Message:
            if obj.successful_payment is None:
                return False
            invoice_payload = obj.successful_payment.invoice_payload
        elif type(obj) in (types.ShippingQuery, types.PreCheckoutQuery):
            invoice_payload = obj.invoice_payload
        else:
            return False

        mode, callback_data = parse_callback_data(invoice_payload)
        return mode == self.payload_mode


def bind_invoice_filters(dp: Dispatcher):
    dp.bind_filter(InvoiceAlreadyPayedFilter, exclude_event_handlers=[dp.callback_query_handlers])
    dp.bind_filter(InvoiceExpiredFilter, exclude_event_handlers=[dp.callback_query_handlers])
    dp.bind_filter(InvoiceDeadFilter, exclude_event_handlers=[dp.callback_query_handlers])
    dp.bind_filter(PayloadModeFilter, exclude_event_handlers=[dp.callback_query_handlers])
