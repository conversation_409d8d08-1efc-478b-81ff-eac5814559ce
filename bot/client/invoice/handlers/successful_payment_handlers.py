import logging

from aiogram import Dispatcher, types
from aiogram.types import ContentTypes

from core.invoice.bot_funcs import add_check_url_to_invoice
from core.invoice.payment import finalize_payment
from db import crud
from schemas import PaymentCallBackData
from utils.text import parse_callback_data

logger = logging.getLogger()


async def pay_for_invoice_handler(message: types.Message):
    mode, payload_data = parse_callback_data(message.successful_payment.invoice_payload)
    invoice, store_order, brand = await crud.get_payment_data_by_invoice_id(payload_data.get("invoice_id"))

    if invoice.currency == 'UZS':
        add_check_url_to_invoice(invoice, message.successful_payment)

    await finalize_payment(
        PaymentCallBackData(
            payment_method="telegram",
            callback_data={
                "telegram_payment_charge_id": message.successful_payment.telegram_payment_charge_id,
                "provider_payment_charge_id": message.successful_payment.provider_payment_charge_id
            },
            external_id=message.successful_payment.provider_payment_charge_id,
            status="success"
        ), invoice=invoice, brand=brand, store_order=store_order,
    )


def register_invoice_successful_payments_handlers(dp: Dispatcher):
    dp.register_message_handler(
        pay_for_invoice_handler,
        payload_mode="pay_for_invoice",
        content_types=ContentTypes.SUCCESSFUL_PAYMENT,
        state="*",
    )
