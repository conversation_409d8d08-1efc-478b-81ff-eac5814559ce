from aiogram import Dispatcher, types

from db.models import User
from utils.text import f


async def without_invoice_handler(shipping_query: types.ShippingQuery):
    lang = await User.get_lang(shipping_query.from_user.id)
    await shipping_query.bot.answer_shipping_query(
        shipping_query.id,
        ok=False,
        error_message=await f("error", lang),
    )


async def already_payed_handler(shipping_query: types.ShippingQuery):
    lang = await User.get_lang(shipping_query.id)

    return await shipping_query.bot.answer_shipping_query(
        shipping_query.id, ok=False,
        error_message=await f("invoice is already paid", lang)
    )


async def expired_handler(shipping_query: types.ShippingQuery):
    lang = await User.get_lang(shipping_query.from_user.id)

    return await shipping_query.bot.answer_pre_checkout_query(
        shipping_query.id, ok=False,
        error_message=await f("invoice expired", lang)
    )


async def dead_handler(shipping_query: types.ShippingQuery):
    lang = await User.get_lang(shipping_query.from_user.id)

    return await shipping_query.bot.answer_pre_checkout_query(
        shipping_query.id, ok=False,
        error_message=await f("invoice dead", lang)
    )


async def pay_for_invoice_handler(shipping_query: types.ShippingQuery):
    await shipping_query.bot.answer_shipping_query(shipping_query.id, ok=True)


async def pay_invoice_from_store_handler(shipping_query: types.ShippingQuery):
    await shipping_query.bot.answer_shipping_query(shipping_query.id, ok=True)


def register_invoice_shipping_query_handler(dp: Dispatcher):
    dp.register_shipping_query_handler(
        without_invoice_handler,
        callback_keys=dict(invoice_id=None),
        state="*",
    )

    dp.register_shipping_query_handler(
        already_payed_handler,
        is_invoice_already_payed=True,
        state="*",
    )

    dp.register_shipping_query_handler(
        expired_handler,
        is_invoice_expired=True,
        state="*",
    )

    dp.register_shipping_query_handler(
        dead_handler,
        is_invoice_dead=True,
        state="*",
    )

    dp.register_shipping_query_handler(
        pay_for_invoice_handler,
        payload_mode="pay_for_invoice",
        state="*",
    )
