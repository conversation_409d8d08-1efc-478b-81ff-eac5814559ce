import base64

from aiogram import Dispatcher, types

from utils.exceptions import ErrorWithTextVariable
from core.invoice.bot_funcs import (
    send_invoice_from_deep_link,
    send_shop_invoice_for_pay,
)
from core.invoice.deep_links import ExternalInvoiceDeepLink, StoreOrderDeepLink, InvoiceDeepLink
from db.models import ClientBot, StoreOrder, User, UserAnalyticAction
from utils.text import f


async def cmd_shop_pay_invoice_deep_link(
        message: types.Message,
        data: StoreOrderDeepLink,
        user: User, lang: str
):
    bot_from_db = await ClientBot.get_current()
    
    order = await StoreOrder.get(data.order_id)

    if not order:
        return await message.answer(await f("client invoice order not found text", lang))
    if order.user_id != user.id:
        return await message.answer(await f("client invoice you cannot pay for this order text", lang))
    if order.status_pay in ("payed", "processing",):
        return await message.answer(await f("client invoice order has already been paid text", lang))
    if order.status in ("canceled", "closed",):
        return await message.answer(await f("client invoice order has already been closed text", lang))

    try:
        await send_shop_invoice_for_pay(
            order, 
            payment_settings_id=data.payment_settings_id,
            object_payment_settings_id=data.object_payment_settings_id
        )
    except ErrorWithTextVariable as err:
        return await message.answer(await f(err.text_variable, lang, **err.text_kwargs))
    except Exception as err:
        return await message.answer(await f("PAYMENT_UNKNOWN_ERROR", lang))

    await UserAnalyticAction.save_link_following(
        message.from_user.id,
        bot_from_db,
        "pay_store_invoice_from_bot",
        invoice_id=order.invoice.id,
    )


async def cmd_pay_ext_invoice_deep_link(
        message: types.Message,
        data: ExternalInvoiceDeepLink,
        lang: str
):
    bot_from_db = await ClientBot.get_current()

    invoice_decoded_uuid = data.invoice_uuid_id
    invoice_uuid = base64.b64decode(invoice_decoded_uuid).decode("utf-8")
    
    # Отримуємо параметри оплати з даних, якщо є
    payment_settings_id = None
    object_payment_settings_id = None
    
    if hasattr(data, 'payment_settings_id') and data.payment_settings_id:
        try:
            payment_settings_id = int(data.payment_settings_id)
        except (ValueError, TypeError):
            pass
            
    if hasattr(data, 'object_payment_settings_id') and data.object_payment_settings_id:
        try:
            object_payment_settings_id = int(data.object_payment_settings_id)
        except (ValueError, TypeError):
            pass
    
    await send_invoice_from_deep_link(
        message, 
        invoice_uuid, 
        bot_from_db, 
        lang, 
        payment_settings_id=payment_settings_id,
        object_payment_settings_id=object_payment_settings_id
    )


async def cmd_pay_invoice_deep_link(
        message: types.Message,
        data: InvoiceDeepLink,
        lang: str
):
    bot_from_db = await ClientBot.get_current()

    await send_invoice_from_deep_link(
        message, 
        data.invoice_id, 
        bot_from_db, 
        lang,
        payment_settings_id=data.payment_settings_id,
        object_payment_settings_id=data.object_payment_settings_id
    )


def register_invoice_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cmd_pay_ext_invoice_deep_link,
        ExternalInvoiceDeepLink.get_filter(return_field_name="data"),
        state="*",
    )

    dp.register_message_handler(
        cmd_shop_pay_invoice_deep_link,
        StoreOrderDeepLink.get_filter(return_field_name="data"),
        state="*",
    )

    dp.register_message_handler(
        cmd_pay_invoice_deep_link,
        InvoiceDeepLink.get_filter(return_field_name="data"),
        state="*",
    )
