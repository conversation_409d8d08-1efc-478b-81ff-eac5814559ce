import logging

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import ClientBot, Invoice, User
from utils.text import f, parse_callback_data

logger = logging.getLogger('debugger')


async def without_invoice_handler(pre_checkout_query: types.PreCheckoutQuery):
    lang = await User.get_lang(pre_checkout_query.from_user.id)
    await pre_checkout_query.bot.answer_pre_checkout_query(
        pre_checkout_query.id,
        ok=False,
        error_message=await f("error", lang),
    )


async def already_payed_handler(pre_checkout_query: types.PreCheckoutQuery):
    lang = await User.get_lang(pre_checkout_query.from_user.id)

    return await pre_checkout_query.bot.answer_pre_checkout_query(
        pre_checkout_query.id, ok=False,
        error_message=await f("invoice is already paid", lang)
    )


async def expired_handler(pre_checkout_query: types.PreCheckoutQuery):
    lang = await User.get_lang(pre_checkout_query.from_user.id)

    return await pre_checkout_query.bot.answer_pre_checkout_query(
        pre_checkout_query.id, ok=False,
        error_message=await f("invoice expired", lang),
    )


async def dead_handler(pre_checkout_query: types.ShippingQuery):
    lang = await User.get_lang(pre_checkout_query.from_user.id)

    return await pre_checkout_query.bot.answer_pre_checkout_query(
        pre_checkout_query.id, ok=False,
        error_message=await f("invoice dead", lang)
    )


async def pay_for_invoice_handler(pre_checkout_query: types.PreCheckoutQuery, state: FSMContext, ):
    await state.finish()
    bot_from_db = await ClientBot.get_current()
    lang = await User.get_lang(pre_checkout_query.from_user.id)
    mode, payload_data = parse_callback_data(pre_checkout_query.invoice_payload)
    invoice = await Invoice.get(payload_data.get("invoice_id"))

    # try:
    #     await pre_checkout_payment(
    #         invoice, bot_from_db, order_info=pre_checkout_query.order_info,
    #     )
    # except Exception as err:
    #     logger.error(err, exc_info=True)
    #     return await pre_checkout_query.bot.answer_pre_checkout_query(
    #         pre_checkout_query.id,
    #         ok=False,
    #         error_message=await f("error", lang),
    #     )

    await pre_checkout_query.bot.answer_pre_checkout_query(pre_checkout_query.id, ok=True)


def register_invoice_pre_checkout_query_handlers(dp: Dispatcher):
    dp.register_pre_checkout_query_handler(
        without_invoice_handler,
        callback_keys=dict(invoice_id=None),
        state="*",
    )

    dp.register_pre_checkout_query_handler(
        already_payed_handler,
        is_invoice_already_payed=True,
        state="*",
    )

    dp.register_pre_checkout_query_handler(
        expired_handler,
        is_invoice_expired=True,
        state="*",
    )

    dp.register_pre_checkout_query_handler(
        dead_handler,
        is_invoice_dead=True,
        state="*",
    )

    dp.register_pre_checkout_query_handler(
        pay_for_invoice_handler,
        payload_mode="pay_for_invoice",
        state="*",
    )
