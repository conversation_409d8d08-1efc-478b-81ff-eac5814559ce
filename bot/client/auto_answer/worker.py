import asyncio
import logging

from config import AUTO_ANSWER_CHECK_DELAY
from db import own_session

from .db_funcs import get_chats_to_sent_auto_answer
from .functions import send_auto_answer

logger = logging.getLogger()


@own_session
async def auto_answer_sender():
    loop = asyncio.get_running_loop()
    try:
        chats_data = await get_chats_to_sent_auto_answer()
        for chat_data in chats_data:
            await send_auto_answer(*chat_data)
    except Exception as e:
        logger.error(e, exc_info=True)

    loop.call_later(AUTO_ANSWER_CHECK_DELAY, asyncio.create_task, auto_answer_sender())
