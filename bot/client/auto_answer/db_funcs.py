from datetime import datetime

from sqlalchemy import func, select

from db import db_func, sess
from db.models import Cha<PERSON>, ChatMessage, ClientBot, Group, User
from schemas import ChatMessageSentByEnum, ChatTypeEnum


@db_func
def get_chats_to_sent_auto_answer() -> list[tuple[Cha<PERSON>, User, Group, ClientBot]]:
    utc_current_datetime = datetime.utcnow()

    stmt = select(
        Chat,
        User,
        Group,
        ClientBot,
    )
    stmt = stmt.join(Chat.user)
    stmt = stmt.join(Chat.group)
    stmt = stmt.join(Chat.bot)
    stmt = stmt.join(Chat.messages)

    stmt = stmt.where(
        ClientBot.status == "enabled",
        ClientBot.is_auto_answer.is_(True),
        ClientBot.is_started.is_(True)
    )

    stmt = stmt.where(
        Chat.type == ChatTypeEnum.USER_WITH_GROUP,
        Chat.is_pending,
        ChatMessage.is_last,
        ChatMessage.sent_by == ChatMessageSentByEnum.USER,
    )

    time_passed = func.time_to_sec(
        func.timediff(utc_current_datetime, ChatMessage.time_created)
    )
    stmt = stmt.where(time_passed > ClientBot.auto_answer_delay * 60)

    return sess().execute(stmt).fetchall()
