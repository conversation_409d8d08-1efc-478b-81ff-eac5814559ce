from core.chat.chat_message_sender import ChatMessageSender
from db.models import Cha<PERSON>, ClientBot, Group, User

from core.custom_texts import ct
from schemas import MessageContentTypeEnum, ChatMessageSentByEnum


async def send_auto_answer(chat: Chat, user: User, group: Group, bot: ClientBot):
    lang = await user.get_lang(bot.id)
    message_text = await ct(bot, lang, "main", "auto answer text")

    await ChatMessageSender(
        chat, MessageContentTypeEnum.TEXT, message_text,
        sent_by=ChatMessageSentByEnum.MANAGER,
        chat_user=user,
        group=group,
        bot=bot,
        chat_pending=None,
    )
