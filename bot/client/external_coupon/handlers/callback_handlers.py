from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from core.external_coupon.callback_data import ShareCouponCallbackData, ApplyCouponCallbackData
from core.external_coupon.handlers import (
    share_coupon_united_handler, apply_coupon_united_handler,
)
from db.models import User, ClientBot
from client.main.keyboards import get_menu_keyboard


async def share_coupon_handler(
        callback_query: types.CallbackQuery,
        share_coupon: ShareCouponCallbackData,
        user: User, state: FSMContext,
        lang: str, bot: ClientBot,
):
    keyboard = await get_menu_keyboard(user, bot)
    return await share_coupon_united_handler(callback_query.message, state, user, bot, lang, keyboard, share_coupon)


async def apply_coupon_handler(
        callback_query: types.CallbackQuery,
        apply_coupon: ApplyCouponCallbackData,
        user: User, state: FSMContext,
        lang: str, bot: ClientBot,
):
    keyboard = await get_menu_keyboard(user, bot)
    return await apply_coupon_united_handler(callback_query.message, state, user, bot, lang, keyboard, apply_coupon)


def register_external_coupon_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        share_coupon_handler,
        ShareCouponCallbackData.get_filter(),
        state="*",
    )

    dp.register_callback_query_handler(
        apply_coupon_handler,
        ApplyCouponCallbackData.get_filter(),
        state="*",
    )
