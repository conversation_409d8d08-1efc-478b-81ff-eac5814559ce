import logging

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from client.main.keyboards import get_menu_keyboard
from core.external_coupon.deep_links import ExternalCouponDeepLink
from core.external_coupon.external_coupon_processer import ExternalCouponProcessor
from db.models import ClientBot, User

logger = logging.getLogger("debugger")


async def external_coupon_deep_link_handler(
    message: types.Message,
    state: FSMContext,
    data: ExternalCouponDeepLink,
    bot: ClientBot,
    user: User, lang: str,
):
    logger.debug(f"{data.external_coupon_code=}")
    keyboard = await get_menu_keyboard(user, bot)
    processor = ExternalCouponProcessor(
        message, state, bot, user, 
        lang, keyboard, data.external_coupon_code,
        store_id=data.store_id,
    )
    await processor.process()


def register_external_coupon_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(
        external_coupon_deep_link_handler,
        ExternalCouponDeepLink.get_filter(return_field_name="data"),
        commands="start",
        state="*",
    )
