import config as cfg

from aiohttp import web

from config import WEBSERVER_HOST
from utils.logger import setup_logger

from friendly.init import dp, app, router
from friendly.handlers import on_startup, register_web_handlers, register_bot_handlers, bind_filters
from friendly.router import setup_router

from friendly.helpers import setup_scheduler_logger


setup_logger("friendly")

bind_filters(dp)

setup_router(router)
setup_scheduler_logger()

register_bot_handlers(dp)

register_web_handlers(app, dp)


async def run_app():
    await on_startup(dp)
    return app

web.run_app(run_app(), host=WEBSERVER_HOST, port=cfg.ROOT_BOT_PORT + 4)
