import asyncio
import json
import uuid
import webbrowser

from stripe import StripeClient

from config import BILLING_STRIPE_SECRET_KEY

client = StripeClient(BILLING_STRIPE_SECRET_KEY)


async def main():
    while True:
        match input("Command(l|n|d|q|s|p|m|gc): "):
            case "l":
                products = await client.products.list_async(
                    {
                        "limit": 100,
                    }
                )

                print("Product list:")
                print(json.dumps(products, indent=4))
            case "n":
                product = await client.products.create_async(
                    {
                        "name": input("Product Name:"),
                        "description": input("Product Description:"),
                        "tax_code": input("Tax Code:"),
                        "metadata": {
                            "7loc_code": input("7Loc product code:")
                        }
                    }
                )
                print("Product created:")
                print(json.dumps(dict(product), indent=4))
            case "d":
                result = await client.products.delete_async(input("Product ID: "))
                print("Product deleted:")
                print(json.dumps(dict(result), indent=4))
            case "s":
                customer = input("Customer ID:").strip() or None
                result = await client.checkout.sessions.create_async(
                    {
                        "mode": "subscription",
                        "customer": customer,
                        "customer_update": {
                            "name": "auto",
                            "address": "auto",
                        } if customer else None,
                        "client_reference_id": uuid.uuid4(),
                        "locale": "en",
                        "billing_address_collection": "required",
                        "tax_id_collection": {
                            "enabled": True,
                        },
                        "automatic_tax": {
                            "enabled": True,
                        },
                        "line_items": [
                            {
                                "price_data": {
                                    "currency": "eur",
                                    "unit_amount_decimal": "4000",
                                    "tax_behavior": "exclusive",
                                    "product_data": {
                                        "name": "Sales Point",
                                        "description": "Access to creating one Sales "
                                                       "Point at 7Loc Admin",
                                        "metadata": {
                                            "7loc_product_code": "store",
                                            "7loc_price_code": "basic_monthly"
                                        },
                                    },
                                    "recurring": {
                                        "interval": "month"
                                    }
                                },
                                "quantity": 1,
                                "adjustable_quantity": {
                                    "enabled": True,
                                    "minimum": 1,
                                    "maximum": 5000,
                                },
                            },
                            {
                                "price_data": {
                                    "currency": "eur",
                                    "unit_amount_decimal": "20",
                                    "tax_behavior": "exclusive",
                                    "product_data": {
                                        "name": "Product",
                                        "description": "Access to creating one Product "
                                                       "at 7Loc Admin",
                                        "metadata": {
                                            "7loc_product_code": "product",
                                            "7loc_price_code": "basic_monthly"
                                        }
                                    },
                                    "recurring": {
                                        "interval": "month"
                                    }
                                },
                                "quantity": 50,
                                "adjustable_quantity": {
                                    "enabled": True,
                                    "minimum": 50,
                                    "maximum": 5000,
                                },
                            },
                            {
                                "price": "price_1Q4VAnFnZdSOgykS7Wc629z1",
                            },
                        ],
                        "success_url": "https://api.ms.dev.payforsay.com/success",
                        "cancel_url": "https://api.ms.dev.payforsay.com/cancel"
                    }
                )
                print("Session created:")
                print(json.dumps(dict(result), indent=4))
                webbrowser.open(result.url)
            case "p":
                result = await client.checkout.sessions.create_async(
                    {
                        "mode": "setup",
                        "client_reference_id": uuid.uuid4(),
                        "locale": "auto",
                        "currency": "eur",
                        "billing_address_collection": "required",
                        "tax_id_collection": {
                            "enabled": True,
                        },
                        "success_url": "https://api.ms.dev.payforsay.com/success",
                        "cancel_url": "https://api.ms.dev.payforsay.com/cancel"
                    }
                )
                print("Session created:")
                print(json.dumps(dict(result), indent=4))
                webbrowser.open(result.url)
            case "m":
                result = await client.billing.meter_events.create_async(
                    {
                        "event_name": input("Event Name:"),
                        "payload": {
                            "stripe_customer_id": input("Customer ID:"),
                            "tokens": input("Tokens:")
                        }
                    }
                )
                print("Event created:")
                print(json.dumps(dict(result), indent=4))
            case "gc":
                result = await client.customers.retrieve_async(
                    input("Customer ID:").strip(),
                )
                print("Customer retrieved:")
                print(json.dumps(dict(result), indent=4))
            case "lpm":
                result = await client.customers.payment_methods.list_async(
                    input("Customer ID:").strip(),
                )
                print("Customer payment methods list:")
                print(json.dumps(dict(result), indent=4))
            case "q":
                break


if __name__ == "__main__":
    asyncio.run(main())
