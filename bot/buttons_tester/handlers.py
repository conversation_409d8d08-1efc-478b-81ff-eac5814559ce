import logging

import config as cfg

from aiogram import Dispatcher, types
from aiogram.dispatcher.filters import CommandStart
from aiogram.utils.text_decorations import html_decoration, markdown_decoration

from core.handlers import register_general_exception_handlers
from core.user.agreement_processor.buttons_handlers import setup_tg_handlers as setup_agreement_handlers
from db.models import User

from utils.text import f
from core.media import download_file
from utils.localisation import localisation
from utils.update_localisation import update_localisation

from .helpers import get_inline_results, get_text_and_parse_mode, is_as_html
from .keyboards import get_help_keyboard, get_keyboard_for_bot_chat, get_voted_keyboard_for_inline
from .models import InlinePoll
from .exceptions import *


async def cmd_start_help(message: types.Message):
    lang = message.from_user.language_code
    await message.answer(await f("buttons tester help text", lang), reply_markup=await get_help_keyboard(lang))


async def cmd_start(message: types.Message):
    lang = message.from_user.language_code
    await message.answer(await f("buttons tester start text", lang), reply_markup=await get_help_keyboard(lang))


async def cmd_upd(message: types.Message):
    lang = message.from_user.language_code

    await update_localisation()
    await message.answer(await f("updated local text", lang))


async def input_handler(message: types.Message):
    lang = message.from_user.language_code

    input_text = message.md_text if message.entities else message.text or message.caption
    text, parse_mode = get_text_and_parse_mode(message.text or message.caption)

    use_default_text = message.content_type == "text"
    text, keyboard = await get_keyboard_for_bot_chat(input_text, lang, use_default_text=use_default_text)

    if len(text) > 256 and message.content_type != "text":
        raise TooLongCaptionError()

    as_html = is_as_html(input_text)
    text_decoration = html_decoration if as_html else markdown_decoration
    await message.answer(await f("buttons tester results header", lang, user_input=text_decoration.quote(input_text)))

    if message.content_type == "text":
        return await message.answer(text, reply_markup=keyboard)

    if message.content_type not in cfg.MEDIA_WITH_CAPTION:
        return await message.answer(await f("buttons tester unsupported content type", lang))

    file_path = await download_file(message)
    file = types.InputFile(path_or_bytesio=file_path)

    sender_func = getattr(message, f"answer_{message.content_type}")

    await sender_func(file, caption=text, reply_markup=keyboard, parse_mode=parse_mode)


async def format_button_handler(callback_query: types.CallbackQuery):
    lang = callback_query.from_user.language_code
    await callback_query.message.answer(await f("buttons tester format text", lang))


async def only_test_button_handler(callback_query: types.CallbackQuery):
    lang = callback_query.from_user.language_code
    await callback_query.answer(await f("buttons tester only test button click text", lang), show_alert=True)


async def vote_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        user: User,
):
    lang = callback_query.from_user.language_code

    poll = await InlinePoll.get(callback_data.get("poll_id"))
    row_id, col_id = callback_data.get("row_id"), callback_data.get("col_id")

    await poll.set_or_remove_vote(user, row_id, col_id)

    if poll.chat_type != "private":
        return await callback_query.answer(await f("soon", lang))

    keyboard = await get_voted_keyboard_for_inline(poll, lang)

    bot = callback_query.bot
    await bot.edit_message_reply_markup(inline_message_id=callback_query.inline_message_id, reply_markup=keyboard)


async def inline_query_handler(inline_query: types.InlineQuery):
    lang = inline_query.from_user.language_code

    if not inline_query.query:
        text = await f("buttons tester how to use bot button text", lang)
        return await inline_query.answer([], switch_pm_text=text, switch_pm_parameter="help", cache_time=0)

    results = await get_inline_results(inline_query.query, inline_query.chat_type, lang)
    await inline_query.answer(results, cache_time=0)


async def chosen_inline_result_handler(
        chosen_result: types.ChosenInlineResult,
        mode: str, callback_data: dict,
        user: User,
):
    debugger = logging.getLogger("debugger")
    debugger.debug(f"chosen_inline_result: {mode}")

    poll_id, chat_type = callback_data.get("poll_id"), callback_data.get("chat_type")
    await InlinePoll.create(poll_id, user, chat_type, chosen_result.query)


async def errors_handler(update: types.Update, exception: Exception) -> bool:
    if not isinstance(exception, ParsingError):
        logger = logging.getLogger()
        logger.error(exception, exc_info=True)
    if update.message:
        user = update.message.from_user
    elif update.callback_query:
        user = update.callback_query.from_user
    else:
        return False

    lang = user.language_code

    if isinstance(exception, TooLongRowError):
        await update.bot.send_message(user.id, await f("buttons tester too long row error", lang))
    elif isinstance(exception, TooManyButtonsError):
        await update.bot.send_message(user.id, await f("buttons tester too many buttons error", lang))
    elif isinstance(exception, TooLongCaptionError):
        await update.bot.send_message(user.id, await f("buttons tester too long caption error", lang))
    else:
        await update.bot.send_message(user.id, await f("error", lang))
    return True


def register_handlers(dp: Dispatcher):
    dp.register_message_handler(cmd_start_help, CommandStart("help"))
    dp.register_message_handler(cmd_start, CommandStart())
    dp.register_message_handler(cmd_upd, commands="upd")
    dp.register_message_handler(input_handler, content_types=types.ContentTypes.ANY)
    setup_agreement_handlers(dp)
    dp.register_callback_query_handler(format_button_handler, callback_mode="format")
    dp.register_callback_query_handler(only_test_button_handler, callback_mode="only_test_button")
    dp.register_callback_query_handler(vote_button_handler, callback_mode="vote")
    dp.register_inline_handler(inline_query_handler)
    dp.register_chosen_inline_handler(chosen_inline_result_handler)
    dp.register_errors_handler(errors_handler)

    register_general_exception_handlers(dp)
