import re

from typing import List, <PERSON><PERSON>

from aiogram import types

from utils.text import f, c

from .models import InlinePoll

from .keyboards import get_keyboard_for_inline


def is_as_html(text: str) -> bool:
    return bool(re.findall(r"<.*>[\s\S]*</?.*>", text))


def get_text_and_parse_mode(text: str) -> Tuple[str, str]:
    as_html = is_as_html(text)
    parse_mode = getattr(types.ParseMode, "HTML" if as_html else "MARKDOWN_V2")
    return text, parse_mode


async def get_inline_results(query_text: str, chat_type: str, lang: str) -> List[types.InlineQueryResultArticle]:
    text, buttons = await InlinePoll.parse_buttons(query_text, lang)
    text, parse_mode = get_text_and_parse_mode(text)

    inline_poll_id = await InlinePoll.generate_id()

    keyboard = await get_keyboard_for_inline(inline_poll_id, buttons)

    article = types.InlineQueryResultArticle(
        id=c("buttons", poll_id=inline_poll_id, chat_type=chat_type),
        title=await f("buttons tester inline article result title", lang),
        description=await f("buttons tester inline article result description", lang),
        input_message_content=types.InputTextMessageContent(text, parse_mode=parse_mode),
        reply_markup=keyboard,
    )

    results = [article]
    return results
