from aiogram.types import InlineKeyboardMarkup as InlineKb, InlineKeyboardButton as InlineBtn

from utils.text import f, c

from .models import InlinePoll


async def get_keyboard_for_bot_chat(text: str, lang: str, *, use_default_text: bool = True) -> tuple[str, InlineKb]:
    text, buttons = await InlinePoll.parse_buttons(text, lang, use_default_text=use_default_text)

    keyboard = InlineKb()
    for row in buttons:
        keyboard.row(*[InlineBtn(button, callback_data="only_test_button") for button in row])

    return text, keyboard


async def get_keyboard_for_inline(inline_poll_id: str, buttons: list[list[str]]) -> InlineKb:
    keyboard = InlineKb()
    for row_id, buttons_row in enumerate(buttons):
        row = list()
        for col_id, button in enumerate(buttons_row):
            callback_data = c("vote", poll_id=inline_poll_id, row_id=row_id, col_id=col_id)
            row.append(InlineBtn(button, callback_data=callback_data))
        keyboard.row(*row)
    return keyboard


async def _set_button_voted(
        button: str,
        inline_poll: InlinePoll,
        row_id: int, col_id: int, lang: str,
) -> str:

    voters = await inline_poll.get_button_voters(row_id, col_id)
    for voter in voters:
        owner = "owner " if inline_poll.sender == voter else ""
        first_name = voter.first_name if voter.first_name else ""
        last_name = voter.last_name if voter.last_name else ""
        button = await f(
            f"buttons tester private chat {owner}voted", lang,
            voter_first_name=first_name.upper(),
            voter_last_name=last_name.upper(),
            button=button,
        )
    return button


async def get_voted_keyboard_for_inline(inline_poll: InlinePoll, lang: str) -> InlineKb:

    keyboard = InlineKb()

    buttons = await inline_poll.get_buttons(lang)

    for row_id, buttons_row in enumerate(buttons):
        row = list()
        for col_id, button in enumerate(buttons_row):
            if inline_poll.chat_type == "private":
                button = await _set_button_voted(button, inline_poll, row_id, col_id, lang)
            callback_data = c("vote", poll_id=inline_poll.id, row_id=row_id, col_id=col_id)
            row.append(InlineBtn(button, callback_data=callback_data))
        keyboard.row(*row)

    return keyboard


async def get_format_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb()
    keyboard.row(InlineBtn(await f("buttons tester format button text", lang), callback_data="format"))
    return keyboard


async def get_help_keyboard(lang: str) -> InlineKb:
    keyboard = await get_format_keyboard(lang)
    keyboard.insert(InlineBtn(await f("buttons tester switch inline button text", lang), switch_inline_query=""))
    return keyboard
