from psutils.exceptions import ErrorWithTextVariable


class ParsingError(ErrorWithTextVariable):

    text_variable = "buttons tester parsing error"


class TooLongRowError(ErrorWithTextVariable):

    text_variable = "buttons tester too long row error"


class TooManyButtonsError(ErrorWithTextVariable):

    text_variable = "buttons tester too many buttons error"


class TooLongCaptionError(ErrorWithTextVariable):

    text_variable = "buttons tester too long caption error"
