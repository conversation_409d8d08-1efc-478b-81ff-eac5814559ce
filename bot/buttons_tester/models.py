import base64
import itertools
import os
import re
from datetime import datetime
from sqlalchemy import (
    BigInteger, Column, DateTime, ForeignKey, SmallInteger, String,
    Text,
)
from sqlalchemy.orm import relationship
from typing import Awaitable, List, Tuple

from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel
from db.models import User
from utils.text import f
from .exceptions import *


class InlinePoll(Base, BaseDBModel):
    id: str = Column(String(256), primary_key=True)
    chat_type: str = Column(String(10))

    sender_id: int = Column(BigInteger, ForeignKey("users.id"))
    sender: "models.User" = relationship(User, backref="inline_polls")

    original_text = Column(Text)

    datetime = Column(DateTime, default=datetime.utcnow)

    def __init__(self, id: str, sender: User, chat_type: str, original_text: str):
        assert id
        assert sender
        assert chat_type
        assert original_text

        super().__init__(
            id=id, sender=sender,
            chat_type=chat_type,
            original_text=original_text,
        )

    @classmethod
    def create(cls, id: str, sender: User, chat_type: str, original_text: str) -> \
            Awaitable["InlinePoll"]:
        return super().create(
            id=id, sender=sender, chat_type=chat_type, original_text=original_text
        )

    @classmethod
    async def generate_id(cls) -> str:
        id = base64.b64encode(os.urandom(12)).decode("utf-8")
        existing = await cls.get(id)
        if existing:
            return await cls.generate_id()
        return id

    async def vote(self, user: User, row_id: int, col_id: int):
        return await InlinePollVote.create(self, user, row_id, col_id)

    async def set_or_remove_vote(self, user: User, row_id: int, col_id: int):
        vote = await InlinePollVote.get(
            inline_poll_id=self.id,
            user_id=user.id,
            row_id=row_id,
            col_id=col_id,
        )
        if vote:
            await vote.delete()
        else:
            await self.vote(user, row_id, col_id)

    @staticmethod
    async def parse_buttons(text: str, lang: str, *, use_default_text: bool = True) -> \
            Tuple[str, List[List[str]]]:
        buttons_rows_texts = re.search(r"(?:(?:\[[^\[\]]+] *)+\n?)+", text)
        if not buttons_rows_texts:
            return text, list()

        buttons_rows_texts = buttons_rows_texts.group()
        text = text.replace(buttons_rows_texts, "")
        buttons_rows_texts = buttons_rows_texts.split("\n")

        buttons_rows = list()

        for buttons_row_text in buttons_rows_texts:
            buttons_row_text = buttons_row_text.strip()
            buttons = re.findall(r"(?<=\[)[^\[\]]+(?=])", buttons_row_text)
            row = [button.strip() for button in buttons]
            if len(row) > 8:
                raise TooLongRowError()
            buttons_rows.append(row)

        if len(list(itertools.chain(*buttons_rows))) > 100:
            raise TooManyButtonsError()

        text = text.strip()

        if not text and use_default_text:
            text = await f("buttons tester default text for result", lang)

        return text, buttons_rows

    async def get_buttons(self, lang: str):
        return (await self.parse_buttons(self.original_text, lang))[1]

    async def get_text(self, lang: str):
        return (await self.parse_buttons(self.original_text, lang))[0]

    @db_func
    def get_button_voters(self, row_id: int, col_id: int) -> List[User]:
        query = sess().query(User)
        query = query.join(InlinePollVote, InlinePollVote.user_id == User.id)
        query = query.filter(InlinePollVote.inline_poll_id == self.id)
        query = query.filter(InlinePollVote.row_id == row_id)
        query = query.filter(InlinePollVote.col_id == col_id)
        users = query.all()
        return users


class InlinePollVote(Base, BaseDBModel):
    inline_poll_id: str = Column(String(255), ForeignKey("inline_polls.id"))
    inline_poll: InlinePoll = relationship(InlinePoll, backref="votes")

    user_id: int = Column(BigInteger, ForeignKey("users.id"))
    user: "models.User" = relationship(User, backref="inline_poll_votes")

    row_id: int = Column(SmallInteger)
    col_id: int = Column(SmallInteger)

    datetime: datetime = Column(DateTime, default=datetime.utcnow)

    def __init__(self, inline_poll: InlinePoll, user: User, row_id: int, col_id: int):
        assert inline_poll
        assert user
        assert type(row_id) is int
        assert type(col_id) is int

        super().__init__(
            inline_poll=inline_poll,
            user=user,
            row_id=row_id,
            col_id=col_id,
        )

    @classmethod
    def create(cls, inline_poll: InlinePoll, user: User, row_id: int, col_id: int) -> \
            Awaitable["InlinePollVote"]:
        return super().create(
            inline_poll=inline_poll,
            user=user,
            row_id=row_id,
            col_id=col_id,
        )

    @db_func
    def delete(self):
        sess().delete(self)
        sess().commit()
