import atexit

import config as cfg

from aiohttp import web

from config import WEBSERVER_HOST
from utils.logger import setup_logger

from service.init import dp, app, router
from service.handlers import on_shutdown, on_startup, register_bot_handlers, register_web_handlers, bind_filters
from service.router import setup_router

setup_logger("service")

bind_filters(dp)

setup_router(router)

register_bot_handlers(dp)

register_web_handlers(app, dp)


async def run_app():
    await on_startup(dp)

    return app


atexit.register(on_shutdown)

web.run_app(run_app(), host=WEBSERVER_HOST, port=cfg.ROOT_BOT_PORT + 2)
