from enum import Enum
from typing import Annotated, Generic, Type

from pydantic import BaseModel, Extra, Field, HttpUrl
from pydantic.generics import GenericModel
from typing_extensions import TypeVar

from utils.type_vars import T


class BaseORMModel(BaseModel):
    class Config:
        orm_mode = True


class BaseSimpleTextModel(BaseORMModel):
    text: str


class BaseCountResponse(BaseORMModel):
    count: int


class OkResponse(BaseModel):
    ok: bool = True


class ExistsResponse(BaseORMModel):
    is_exists: bool


class EnumWithValues(Enum):
    @classmethod
    def values(cls) -> list[str]:
        return list(map(lambda c: c.value, cls))

    @classmethod
    def filter_values(cls, prefix: str) -> list[str]:
        return [value for value in cls.values() if value.startswith(prefix)]


class EnumWithItems(Enum):
    @classmethod
    def items(cls: Type[T]) -> list[T]:
        return list(cls)

    @classmethod
    def next_item(cls: Type[T], item: T) -> T:
        idx = cls.index(item) + 1
        if idx >= len(cls):
            return None
        return cls.items[idx]


class UrlSchema(BaseModel):
    url: HttpUrl | None = None


class BaseExtraAllowedModel(BaseModel, extra=Extra.allow):
    pass


DataT = TypeVar("DataT", default=dict)


class ListCursorResponse(GenericModel, Generic[DataT]):
    data: list[DataT]
    next: Annotated[str | None, Field(nullable=True)] = None


class ExtraFeeSchema(BaseModel):
    extra_fee_id: int
    name: str
    extra_fee_percent: str | None
    extra_fee_value: str | None
    applied_amount: int
    invoice_id: int | None = None
    order_id: int | None
    applied_amount_float: float | None = None


class ExtraFeeFormated(BaseModel):
    name: str
    formated_amount: str


class ReorderData(BaseModel):
    object_id: int
    new_position: int
