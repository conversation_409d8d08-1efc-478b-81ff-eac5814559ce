import enum
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Annotated, Any, Literal

from incust_api.api import term
from pydantic import BaseModel, Field, HttpUrl, constr

from schemas.auth import Token
from schemas.base import BaseORMModel, ExtraFeeSchema, UrlSchema
from schemas.incust import CouponShowData
from schemas.loyalty_settings import LoyaltySettingsSchema
from ..store.order import UTMLabelsSchema


class InvoiceTypeEnum(Enum):
    STORE_ORDER = "STORE_ORDER"
    EVENT = "EVENT"
    MENU_IN_STORE = "MENU_IN_STORE"
    FROM_LINK = "FROM_LINK"
    INTEGRATION = "INTEGRATION"
    UNKNOWN = "UNKNOWN"
    TOPUP_ACCOUNT = "TOPUP_ACCOUNT"
    EWALLET_EXT_PAYMENT = "EWALLET_EXT_PAYMENT"


InvoiceStatusLiteral = Literal["not_payed", "payed"]


class InvoicePrintReceiptTypeEnum(Enum):
    pdf = "pdf"
    html = "html"


class PrintReceiptDataTypeEnum(Enum):
    invoice = "invoice"
    order = "order"


class InvoicePrintReceiptFormatEnum(Enum):
    A4 = "A4"
    _58mm = "58mm"
    _80mm = "80mm"
    CUSTOM = "CUSTOM"


class InvoiceTemplatePaymentModeEnum(str, enum.Enum):
    ENTERED_AMOUNT = "entered_amount"
    ITEMS = "items"


class InvoicePaymentModeEnum(Enum):
    ENTERED_AMOUNT = 'entered_amount'
    TEMPLATE = 'template'
    STORE_ORDER = 'store_order'


InvoicePaymentModeLiteral = Literal["entered_amount", "template", "store_order"]


class InvoiceItemSchema(BaseORMModel):
    id: int
    name: str
    category: str = "uncategorized"
    quantity: int = 1

    item_code: str | None = Field(None, nullable=False)

    # unit
    price: float
    unit_discount: float = 0
    unit_bonuses_redeemed: float = 0
    unit_discount_and_bonuses_redeemed: float = 0
    final_price: float

    # unit * quantity
    before_loyalty_sum: float
    discount: float = 0
    bonuses_redeemed: float = 0
    discount_and_bonuses_redeemed: float = 0
    final_sum: float


class CreateInvoiceItemData(BaseModel):
    name: str
    category: str = "uncategorized"
    quantity: int = 1

    item_code: str | None = Field(None, nullable=False)

    # unit
    price: float
    unit_discount: float = 0
    unit_bonuses_redeemed: float = 0


InvoiceTemplateLabelCommentMode = Literal["disabled", "optional", "required"]

InvoiceQrMode = Literal["web", "bot", "all"]

PayerFeeType = Literal["percent", "amount", "no_fee"]


class InvoiceTemplateItemSchema(BaseORMModel):
    name: str
    quantity: int
    price: float
    item_code: str | None = None
    category: str


class InvoiceTemplatePluginSchema(BaseModel):
    plugin_name: str
    plugin_url: str
    has_own_amount_input: bool = False
    plugin_data: dict[str, Any] | None = None


class EWalletUserAccountInfo(BaseModel):
    title: str
    message: str | None = None
    used_credit: float | None = None
    available_amount: float | None = None
    credit_limit: float | None = None


class InvoiceTemplateSchema(BaseModel):
    id: int
    currency: str
    items: list[InvoiceTemplateItemSchema]
    title: str
    description: Annotated[str | None, Field(nullable=True)] = None

    comment_mode: InvoiceTemplateLabelCommentMode
    comment_label: str

    photo: Annotated[str | None, Field(nullable=True)] = None
    expiration_datetime: Annotated[datetime | None, Field(nullable=True)] = None
    amount: float

    need_name: bool
    need_email: bool
    need_phone: bool

    payment_mode: InvoiceTemplatePaymentModeEnum
    disabled_qty: bool
    disabled_loyalty: bool
    plugins: Annotated[
        list[InvoiceTemplatePluginSchema] | None, Field(nullable=True)] = None

    min_amount: Annotated[float | None, Field(nullable=True)] = None
    max_amount: Annotated[float | None, Field(nullable=True)] = None
    product_code: Annotated[str | None, Field(nullable=True)] = None

    max_bonuses_percent: Annotated[Decimal | None, Field(nullable=True)] = None

    need_auth: bool

    ewallet_user_account_info: EWalletUserAccountInfo | None = None

    loyalty_settings: LoyaltySettingsSchema | None = None


class InvoiceSchema(BaseModel):
    id: int
    status: Literal["not_payed", "payed"]

    currency: str
    items: list[InvoiceItemSchema]
    payment_mode: InvoicePaymentModeLiteral

    title: str
    description: str
    photo: str | None = None

    first_name: str | None = None
    last_name: str | None = None
    email: str | None = None
    phone: str | None = None

    incust_check: term.m.Check | None = None

    shipment_cost: float
    custom_payment_cost: float

    before_loyalty_sum: float

    discount: float
    bonuses_redeemed: float
    discount_and_bonuses_redeemed: float
    bonuses_added_amount: float = 0
    special_accounts_charges: list[term.m.SpecialAccountCharge] | None = None
    incust_vouchers: list[CouponShowData] | None = None

    total_sum: float
    tips_sum: float
    sum_to_pay: float

    payer_fee: float
    paid_sum: float

    external_transaction_id: str | None = None
    client_redirect_url: str | None = None

    webhook_result: dict[str, Any] | None = None

    user_id: int
    payer_id: int | None = None
    is_friend: bool | None = None

    user_comment_label: str | None = None
    user_comment: str | None = None

    payed_in_bot_id: int | None = None

    time_created: datetime

    invoice_type: InvoiceTypeEnum

    extraFee: list[ExtraFeeSchema] | None = None
    total_sum_with_extra_fee: float
    invoice_template_id: int | None = None

    extra_params: Annotated[dict[Any, Any] | None, Field(nullable=True)] = None

    ewallet_id: Annotated[int | None, Field(nullable=True)] = None
    ewallet_info: EWalletUserAccountInfo | None = None
    post_payment_info: str | None = None


class CreateInvoiceCheckItem(BaseModel):
    item_id: str
    name: str
    price: float | int
    quantity: int


class CreateInvoiceForIntegrationData(BaseModel):
    check_items: list[CreateInvoiceCheckItem]


class InvoiceCreatedResult(UrlSchema):
    invoice: InvoiceSchema
    token_data: Token
    qr_code: str | None = None
    qr_code_bot: str | None = None
    bot_url: HttpUrl | None = None


class PaymentMethodsInfo(BaseModel):
    payment_methods: list[str] | None = None
    tg_token: str | None = None


class UserTokenForIntegrationValid(BaseModel):
    user_id: int


class ProfileTokenForIntegrationValid(BaseModel):
    profile_id: int
    profile_name: str


class InvoiceStatus(BaseModel):
    id: int
    status: str

    total_amount: float

    external_transaction_id: str | None = None
    webhook_result: dict[str, Any] | None = None
    currency: str


class CreateInvoiceData(BaseModel):
    check_items: list[CreateInvoiceCheckItem] | None = None
    amount: float | int | None = None
    currency: str | None = Field(
        None, description="Optional: currency ISO code (USD | EUR or others...)"
    )


class CreateInvoiceWithUrlData(CreateInvoiceData):
    external_transaction_id: constr(max_length=100) = Field(
        description="Transaction id from external system. Will be returned in "
                    "client_redirect_url and successful_payment_callback_url"
    )

    client_redirect_url: HttpUrl | None = Field(
        None,
        description="""
        Url to redirect client after payment(both:success,fail) with params
        {
            "is_success": "0"|"1",
            "external_transaction_id": "string",
            "invoice_id": "number",
            "webhook_result": {
                "http_status": "number" | "Unknown"(if webhook sending failed
                http_status is "Unknown"),
                "body": "string" | "null"(if http_status is "Unknown"),
                "error": "string" | "null" (will be not nul when http_status is
                "Unknown"
            }
        }
        """
    )
    successful_payment_callback_url: HttpUrl | None = Field(
        None,
        description="""
        Url for webhook(POST) after successful payment.
        After successful payment this webhook will be called with data (InvoiceSchema)
        """
    )
    qr_mode: InvoiceQrMode | None = Field(
        None, description="QR code mode: web | bot | all"
    )


class CreateInvoiceWebData(BaseModel):
    group_id: int
    payment_mode: InvoicePaymentModeLiteral

    email: Annotated[str | None, Field(nullable=True)] = None
    phone: Annotated[str | None, Field(nullable=True)] = None
    first_name: Annotated[str | None, Field(nullable=True)] = None
    last_name: Annotated[str | None, Field(nullable=True)] = None
    is_accepted_agreement: bool = False

    menu_in_store_id: Annotated[int | None, Field(nullable=True)] = None
    store_id: Annotated[int | None, Field(nullable=True)] = None
    invoice_template_id: Annotated[int | None, Field(nullable=True)] = None

    entered_amount: Annotated[float | None, Field(nullable=True)] = None
    count: Annotated[int | None, Field(nullable=True)] = None
    with_url: bool = False

    client_redirect_url: Annotated[str | None, Field(nullable=True)] = None
    successful_payment_callback_url: Annotated[str | None, Field(nullable=True)] = None
    incust_check: Annotated[term.m.Check | None, Field(nullable=True)] = None

    user_comment: Annotated[str | None, Field(nullable=True)] = None
    external_transaction_id: Annotated[str | None, Field(nullable=True)] = None

    marketing_consent: Annotated[bool | None, Field(nullable=True)] = None

    extra_params: Annotated[dict[Any, Any] | None, Field(nullable=True)] = None
    ewallet_id: Annotated[int | None, Field(nullable=True)] = None

    utm_labels: Annotated[UTMLabelsSchema | None, Field(nullable=True)] = None
