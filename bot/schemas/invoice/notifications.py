from pydantic import BaseModel

from ..base import ExtraFeeFormated
from ..incust import IncustCheckInfoData, IncustInfoData
from ..invoice import InvoicePaymentModeLiteral
from ..templater import BaseTemplateSchema


class InvoiceMessageData(BaseModel):
    title: str
    amount: str
    status_text_variable: str
    paid_sum: str
    payer_fee: str

    invoice_payment_mode: InvoicePaymentModeLiteral | None
    invoice_image_url: str | None

    type_payed_message: str
    url_in_check_text: str

    time_created: str
    time_payed: str

    loyalty_info_data: IncustInfoData | None = None
    loyalty_check_info_data: IncustCheckInfoData | None = None

    user_comment_line: str
    extra_fee_str: str | None = None
    extra_fees: list[ExtraFeeFormated] | None = None
    extra_fee_txt: str | None = None
    ewallet_topup_info: str | None = None


class InvoiceEmailTemplate(InvoiceMessageData, BaseTemplateSchema):
    TEMPLATE_PATH = "invoice_payment_message.html"

    for_header_text: str
    on_amount_text: str
    sum_amount_header_text: str
    invoice_created_text: str
    invoice_payed_text: str
    header: str

    invoice_id: int
    status_payment: str
    friend_text: str
