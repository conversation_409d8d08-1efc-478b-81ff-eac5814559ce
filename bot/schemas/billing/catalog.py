from fastapi import Query
from pydantic import BaseModel

from .service_packet import (
    AdminBillingServicePacketCatalogSchema, BillingServicePacketCatalogSchema,
    ModifyBillingServicePacketData,
)
from .service_packet_item import BillingRecurringInterval


class BillingCatalogSchema(BaseModel):
    packets: list[BillingServicePacketCatalogSchema]


class BillingCatalogParams(BaseModel):
    only_public: bool = Query(True)
    recurring_interval: BillingRecurringInterval | None = Query(None, nullable=True)


class AdminBillingCatalogSchema(BaseModel):
    packets: list[AdminBillingServicePacketCatalogSchema]


class UpdateBillingCatalogData(BaseModel):
    packets: list[ModifyBillingServicePacketData]
