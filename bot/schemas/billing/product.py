import enum
from typing import Literal

from fastapi import Query
from pydantic import BaseModel, Field

from schemas import BaseORMModel


class BillingProductCode(enum.Enum):
    STORE = "store"
    PRODUCT = "product"
    VM = "vm"
    LOYALTY = "loyalty"

    TRANSACTION = "transaction"
    TRANSACTION_CENT = "transaction_cent"
    TRANSLATOR_SYMBOL = "translator_symbol"
    VM_CHAT = "vm_chat"

    TICKET = "ticket"


class BillingProductListParams(BaseModel):
    offset: int = Query(0)
    limit: int = Query(100, gt=0, le=100)
    search_text: str | None = Query(None)


class BaseBillingProductSchema(BaseORMModel):
    name: str
    code: BillingProductCode
    description: str
    tax_code: str
    record_usages: bool = False
    placeholder_quantity: int | Literal["inf"] = "inf"


class BillingProductSchema(BaseBillingProductSchema):
    id: int
    stripe_id: str


class CreateBillingProductData(BaseBillingProductSchema):
    pass


class UpdateBillingProductData(BaseBillingProductSchema):
    name: str | None = Field(None, nullable=True)
    description: str | None = Field(None, nullable=True)
    tax_code: str | None = Field(None, nullable=True)


BILLING_PRODUCTS = {
    BillingProductCode.STORE: CreateBillingProductData(
        name="Online Point of Sales",
        code=BillingProductCode.STORE,
        description=(
            "Access to creating and managing Online Point of Sales in 7Loc Admin."
        ),
        tax_code="txcd_10103001",
    ),
    BillingProductCode.PRODUCT: CreateBillingProductData(
        name="Items Created in Stores",
        code=BillingProductCode.PRODUCT,
        description="Permission to create and manage items in stores via 7Loc Admin.",
        tax_code="txcd_10103001",
    ),
    BillingProductCode.TRANSACTION: CreateBillingProductData(
        name="Order/Transaction Processing Fee",
        code=BillingProductCode.TRANSACTION,
        description=(
            "Fee for processing a single customer order or transaction in 7Loc."
        ),
        tax_code="txcd_10103001",
        record_usages=True,
    ),
    BillingProductCode.TRANSACTION_CENT: CreateBillingProductData(
        name="Order/Transaction Processing Fee (Percentage)",
        code=BillingProductCode.TRANSACTION_CENT,
        description="Fee calculated as a percentage of the transaction amount in 7Loc.",
        tax_code="txcd_10103001",
        record_usages=True,
    ),
    BillingProductCode.LOYALTY: CreateBillingProductData(
        name="Customer Loyalty Module",
        code=BillingProductCode.LOYALTY,
        description="Access to configure and manage customer loyalty programs in 7Loc.",
        tax_code="txcd_10103001",
    ),
    BillingProductCode.TRANSLATOR_SYMBOL: CreateBillingProductData(
        name="Automated Content Translations (Per Symbol)",
        code=BillingProductCode.TRANSLATOR_SYMBOL,
        description="Fee for automated content translation, calculated per symbol.",
        tax_code="txcd_10103001",
        record_usages=True,
        placeholder_quantity=100_000,
    ),
    BillingProductCode.VM: CreateBillingProductData(
        name="Virtual Managers",
        code=BillingProductCode.VM,
        description="Access to create and manage Virtual Managers in 7Loc Admin.",
        tax_code="txcd_10103001",
    ),
    BillingProductCode.VM_CHAT: CreateBillingProductData(
        name="Virtual Manager Conversation",
        code=BillingProductCode.VM_CHAT,
        description="Fee for a single customer conversation with a Virtual Manager.",
        tax_code="txcd_10103001",
        record_usages=True,
    ),
    BillingProductCode.TICKET: CreateBillingProductData(
        name="Customer Request",
        code=BillingProductCode.TICKET,
        description="Fee for handling a single customer request or ticket in 7Loc.",
        tax_code="txcd_10103001",
        record_usages=True,
    )
}
