from fastapi import File, UploadFile
from openai import BaseModel
from pydantic import Field
from pydantic.dataclasses import dataclass


class VerificationDocumentSchema(BaseModel):
    id: int
    media_url: str
    mime_type: str
    file_name: str
    comment: str | None = Field(None, nullable=True)


class UpdateVerificationDocumentData(BaseModel):
    comment: str | None = Field(None, nullable=True)


@dataclass
class AddVerificationDocumentsData:
    files: list[UploadFile] = File(min_items=1)
