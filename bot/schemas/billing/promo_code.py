from decimal import Decimal
from typing import Annotated, Literal

from pydantic import BaseModel, Field

from .service_packet import BillingServicePacketCatalogSchema


class BillingPromoCodeCouponAppliesTo(BaseModel):
    products: list[str]


class BillingPromoCodeCouponInfoSchema(BaseModel):
    id: str
    name: str

    currencies_amounts_off: Annotated[
        dict[str, Decimal] | None, Field(nullable=True)
    ] = None
    percent_off: Annotated[float | None, Field(nullable=True)] = None

    duration: Literal["forever", "once", "repeating"]
    duration_in_months: Annotated[int | None, Field(nullable=True)] = None

    applies_to: Annotated[
        BillingPromoCodeCouponAppliesTo | None,
        Field(nullable=True)
    ] = None


class BillingPromoCodeInfoSchema(BaseModel):
    id: int
    code: str
    name: str

    trial_period_days: int

    packet: Annotated[
        BillingServicePacketCatalogSchema | None,
        Field(nullable=True)
    ] = None

    coupon: Annotated[
        BillingPromoCodeCouponInfoSchema | None,
        Field(nullable=True),
    ] = None
