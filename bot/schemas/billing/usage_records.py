from fastapi import Query
from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass

from .product import BillingProductCode
from .subscription_item import BillingSubscriptionItemSchema
from ..base import BaseORMModel


class BillingUsageRecordToReport(BaseORMModel):
    id: int
    used_quantity: int
    reported_quantity: int
    meter_event_name: str
    stripe_customer_id: str


class BillingAvailableUnitsParams(BaseModel):
    required_quantity: int = Query(1)


class BillingAvailableUnitsInfo(BaseModel):
    item: BillingSubscriptionItemSchema
    subscription_currency: str


class BillingAvailableUnitsResponse(BaseModel):
    available: bool
    required_quantity: int
    product_code: BillingProductCode
    info: BillingAvailableUnitsInfo | None = Field(
        default=None,
        nullable=True,
        description="Returned only if user has access to profile:admin action"
    )


@dataclass
class GetBillingProductsUsagesParams:
    products: list[BillingProductCode] = Query(min_items=1)


class BillingProductsUsagesData(BaseModel):
    products: dict[BillingProductCode, int]
