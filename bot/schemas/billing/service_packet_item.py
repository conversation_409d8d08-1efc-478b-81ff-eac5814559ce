import enum
from datetime import datetime
from decimal import Decimal
from typing import Annotated, Literal

from pydantic import BaseModel, Field, validator

from .product import BillingProductCode
from ..base import BaseORMModel


class BillingRecurringInterval(enum.Enum):
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    YEAR = "year"


class BillingScheme(enum.Enum):
    PER_UNIT = "per_unit"
    TIERED = "tiered"


class BillingTiersMode(enum.Enum):
    VOLUME = "volume"
    GRADUATED = "graduated"


class BillingUsageType(enum.Enum):
    LICENSED = "licensed"
    METERED = "metered"


class BillingTransformQuantityRound(enum.Enum):
    UP = "up"
    DOWN = "down"


class BillingTierSchema(BaseModel):
    flat_amount: Decimal | None = None
    unit_amount: Decimal | None = None
    up_to: Literal["inf"] | int

    @validator("up_to", pre=True)
    def validate_up_to(cls, value):
        if value is None:
            value = "inf"
        return value


class BillingTransformQuantity(BaseModel):
    divide_by: int
    round: BillingTransformQuantityRound


class BaseBillingServicePacketItemSchema(BaseORMModel):
    name: str = Field(min_length=3, max_length=128)
    description: str = Field(min_length=3, max_length=1024)

    quantity: int
    quantity_adjustable: bool
    min_quantity: int
    max_quantity: int

    billing_scheme: BillingScheme
    tiers_mode: BillingTiersMode | None = Field(None, nullable=True)
    tiers: list[BillingTierSchema] | None = Field(None, nullable=True)

    unit_amount: Decimal

    usage_type: BillingUsageType

    transform_quantity: BillingTransformQuantity | None = Field(
        nullable=True,
    )


class BaseAdminBillingServicePacketItemSchema(BaseBillingServicePacketItemSchema):
    product_code: BillingProductCode
    meter_event_name: str | None = Field(None, nullable=True)


class BillingServicePacketItemCatalogSchema(BaseBillingServicePacketItemSchema):
    id: int
    product_code: BillingProductCode
    stripe_product_id: str


class AdminBillingServicePacketItemTranslationSchema(BaseModel):
    name: Annotated[str | None, Field(nullable=True)] = None
    description: Annotated[str | None, Field(nullable=True)] = None


class AdminBillingServicePacketItemCatalogSchema(
    BaseAdminBillingServicePacketItemSchema
):
    id: int
    position: int
    stripe_price_id: str
    meter_id: str | None = Field(nullable=True)
    time_created: datetime

    translations: (
            dict[
                str,
                AdminBillingServicePacketItemTranslationSchema
            ] | None
    ) = (
        Field(
            default=None,
            nullable=True,
            description=(
                "Translations for different languages. "
                "The key is language ISO code and "
                "the value is fields translations object"
            )
        )
    )


class ModifyBillingServicePacketItemData(BaseAdminBillingServicePacketItemSchema):
    id: int | str | None = Field(
        None, nullable=True,
        description="string id will be ignored"
    )

    translations: (
            dict[
                str,
                AdminBillingServicePacketItemTranslationSchema
            ] | None
    ) = (
        Field(
            default=None,
            nullable=True,
            description=(
                "Translations for different languages. "
                "The key is language ISO code and "
                "the value is fields translations object"
            )
        )
    )
