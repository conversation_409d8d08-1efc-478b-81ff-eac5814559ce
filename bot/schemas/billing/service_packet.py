import enum
from datetime import datetime
from typing import Annotated

from pydantic import BaseModel, Field

from .service_packet_item import (
    AdminBillingServicePacketItemCatalogSchema, BillingRecurringInterval,
    BillingServicePacketItemCatalogSchema, ModifyBillingServicePacketItemData,
)
from ..base import BaseORMModel


class ServicePacketCountryMode(enum.Enum):
    ALL = "ALL"
    DEFAULT = "DEFAULT"  # for all countries that do not have own packets
    SPECIFIC = "SPECIFIC"  # for specific countries


class BaseBillingServicePacketSchema(BaseORMModel):
    name: str
    subtitle: str
    description: str

    recurring_interval: BillingRecurringInterval
    recurring_interval_count: int

    country_mode: ServicePacketCountryMode
    countries: list[str] | None = Field(None, nullable=True)

    currency: str

    is_public: bool

    trial_allowed: bool
    is_free_plan: bool


class BillingServicePacketCatalogSchema(BaseBillingServicePacketSchema):
    id: int
    items: list[BillingServicePacketItemCatalogSchema]


class AdminBillingServicePacketTranslationSchema(BaseModel):
    name: Annotated[str | None, Field(nullable=True)] = None
    subtitle: Annotated[str | None, Field(nullable=True)] = None
    description: Annotated[str | None, Field(nullable=True)] = None


class AdminBillingServicePacketCatalogSchema(BaseBillingServicePacketSchema):
    id: int

    position: int
    time_created: datetime

    billing_amount_threshold: int | None = Field(None, nullable=True)

    items: list[AdminBillingServicePacketItemCatalogSchema]

    lang: str
    langs_list: list[str] = Field(default_factory=list)

    translations: dict[str, AdminBillingServicePacketTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class ModifyBillingServicePacketData(BaseBillingServicePacketSchema):
    id: int | str | None = Field(
        None, nullable=True,
        description="string id will be ignored"
    )

    name: str = Field(min_length=3, max_length=128)
    subtitle: str = Field(min_length=0, max_length=128)
    description: str = Field(min_length=0, max_length=1024)

    billing_amount_threshold: int | None = Field(None, nullable=True)

    lang: str
    langs_list: list[str] = Field(default_factory=list)

    items: list[ModifyBillingServicePacketItemData]

    translations: dict[str, AdminBillingServicePacketTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )
