import enum
from datetime import datetime
from decimal import Decimal

from pydantic import BaseModel, Field

from .service_packet import BaseBillingServicePacketSchema
from .subscription_item import BillingSubscriptionItemSchema
from .. import BaseORMModel


class BillingSubscriptionStatus(enum.Enum):
    INCOMPLETE = "incomplete"
    INCOMPLETE_EXPIRED = "incomplete_expired"
    TRIALING = "trialing"
    ACTIVE = "active"
    PAST_DUE = "past_due"
    CANCELED = "canceled"
    UNPAID = "unpaid"
    PAUSED = "paused"


class BillingSubscriptionCancellationDetailsFeedback(enum.Enum):
    CUSTOMER_SERVICE = "customer_service"
    LOW_QUALITY = "low_quality"
    MISSING_FEATURES = "missing_features"
    OTHER = "other"
    SWITCHED_SERVICE = "switched_service"
    TOO_COMPLEX = "too_complex"
    TOO_EXPENSIVE = "too_expensive"
    UNUSED = "unused"


class BillingSubscriptionCancellationDetailsReason(enum.Enum):
    CANCELLATION_REQUESTED = "cancellation_requested"
    PAYMENT_DISPUTED = "payment_disputed"
    PAYMENT_FAILED = "payment_failed"


class BillingSubscriptionCancellationDetails(BaseModel):
    comment: str | None = Field(None, nullable=True)
    feedback: BillingSubscriptionCancellationDetailsFeedback | None = Field(
        None, nullable=True
    )
    reason: BillingSubscriptionCancellationDetailsReason | None = Field(
        None, nullable=True
    )


class BillingSubscribeItemData(BaseModel):
    quantity: int = Field(None, gt=0, nullable=True)


class BillingSubscribePacketData(BaseModel):
    id: int
    items: dict[int, BillingSubscribeItemData]


class BillingSubscribeData(BaseModel):
    packet: BillingSubscribePacketData
    promo_code: str | None = Field(None, nullable=True)
    payment_method: str | None = Field(
        default=None,
        nullable=True,
        description="Can be null only if packet is trial or with platform:admin role!"
    )
    disable_trial_period: bool = False
    trial_period_days_override: int = Field(
        default=None,
        nullable=True,
        description="Only allowed with platform:admin role",
    )


class BillingSubscriptionUpdatePaymentMethodData(BaseModel):
    payment_method: str
    resume_subscription: bool = False


class BillingSubscriptionSchema(BaseORMModel):
    id: int

    currency: str
    status: BillingSubscriptionStatus

    description: str | None

    cancel_at: datetime | None = Field(None, nullable=True)
    cancel_at_period_end: bool
    canceled_at: datetime | None = Field(None, nullable=True)
    cancellation_details: BillingSubscriptionCancellationDetails | None = Field(
        None, nullable=True
    )

    trial_start: datetime | None = Field(None, nullable=True)
    trial_end: datetime | None = Field(None, nullable=True)

    stripe_created: datetime
    start_date: datetime

    current_period_start: datetime
    current_period_end: datetime

    ended_at: datetime | None = Field(None, nullable=True)

    livemode: bool

    items: list[BillingSubscriptionItemSchema]

    amount: Decimal


class BillingCurrentTariffPlanPacketSchema(BaseBillingServicePacketSchema):
    id: int
    position: int


class BillingCurrentTariffPlanSchema(BaseModel):
    packet: BillingCurrentTariffPlanPacketSchema
    subscription: BillingSubscriptionSchema


class BillingTariffPlanParams(BaseModel):
    has_subscription_before: bool
    first_year_sale_percent: float | int | None = Field(None, nullable=True)
    billing_annual_free_mounts_count: float | int | None = Field(None, nullable=True)
    default_trial_days: int


class BillingCurrentTariffPlanResponse(BaseModel):
    tariff_plan: BillingCurrentTariffPlanSchema | None = Field(None, nullable=True)
    params: BillingTariffPlanParams


class BillingCheckProfileSubscriptionResponse(BaseModel):
    is_subscribed: bool
