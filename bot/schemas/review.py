import enum
from typing import Literal, <PERSON><PERSON>lias

from pydantic import BaseModel, Field, validator

from .store.order import UTMLabelsSchema


class ReviewTypeEnum(enum.Enum):
    STARS = "stars"
    EMOJI = "emoji"
    NUMBER = "number"


class ReviewPrivacyEnum(enum.Enum):
    PRIVATE = "private"
    PUBLIC = "public"


ReviewEmojiLiteral: TypeAlias = Literal["😍", "🙂", "😶", "😟", "😡"]


class ReviewReview(BaseModel):
    stars: int | None = Field(None, nullable=True)
    number: int | None = Field(None, nullable=True)
    emoji: ReviewEmojiLiteral | None = Field(None, nullable=True)


class ReviewMediaMediaGroup(BaseModel):
    photo: list[str] | None = Field(None, nullable=True)
    video: list[str] | None = Field(None, nullable=True)


class ReviewMedia(BaseModel):
    photo: str | None = Field(None, nullable=True)
    video: str | None = Field(None, nullable=True)
    media_group: ReviewMediaMediaGroup | None = Field(None, nullable=True)


class MakeReviewData(BaseModel):
    type: ReviewTypeEnum = ReviewTypeEnum.STARS
    review: ReviewReview
    text: str
    additional_text: str | None = Field(None, nullable=True)

    utm_labels: UTMLabelsSchema | None = Field(
        None, nullable=True,
        description="UTM labels of the invoice, if any"
    )

    @validator("review")
    def validate_review(cls, v, values):
        if "type" not in values:
            return v

        if not getattr(v, values["type"].value):
            raise ValueError(f"{values['type'].value} has to be included in review")

        return v


class Review(MakeReviewData):
    id: int
    media: ReviewMedia | None = Field(None, nullable=True)
