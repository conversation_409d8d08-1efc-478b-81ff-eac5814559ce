from datetime import datetime

from pydantic import BaseModel

from schemas import BaseTemplateSchema
from schemas.store.payment_providers import EWalletPaymentStatus


class PaymentCallBackData(BaseModel):
    payment_method: str | None = None
    payment_uuid: str | None = None
    callback_data: dict | None = None
    status: str | None = None
    is_sandbox: bool | None = None
    payment_settings_id: int | str | None = None
    external_id: str | None = None
    card_type: str | None = None
    card_mask: str | None = None
    incust_transaction: dict | None = None
    incust_pay_data: dict | None = None
    incust_server_api_url: str | None = None
    terminal_api_key: str | None = None
    card_country_iso: str | None = None
    error: str | None = None
    amount: int | float | None = None
    business_payment_result: dict | None = None
    ewallet_id: int | None = None

    class Config:
        arbitrary_types_allowed = True


class FriendEmailTemplate(BaseTemplateSchema):
    TEMPLATE_PATH = "notification_to_friend_web.html"
    message: str
    btn_text: str
    user_info: str | None
    username: str | None
    url: str
    photo: str | None
    friend_comment: str | None


class PayerFeeData(BaseModel):
    payer_fee_percent: str | None = None
    fee_percent: float | None = 0
    payer_fee_value: str | None = None
    fee_value: float | None = 0
    fee: int


class EWalletPaymentStatusData(BaseModel):
    payment_uuid: str | None = None
    status: EWalletPaymentStatus | None = None
    payment_settings_id: int | str | None = None
    external_id: str | None = None
    error: str | None = None
    paid_datetime: datetime | None = None