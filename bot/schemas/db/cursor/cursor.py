import base64
import enum
import json
from dataclasses import MISSING, asdict, fields, is_dataclass
from datetime import datetime
from typing import Any, ClassVar, Iterable, Type

from fastapi import Query
from pydantic import BaseModel
from pydantic.dataclasses import dataclass

from utils.type_vars import T


class CursorDirection(enum.Enum):
    BACK = "back"
    NEXT = "next"


@dataclass
class Cursor:
    SEPARATOR: ClassVar[str] = ";"
    direction: CursorDirection

    def serialise_value(self, value: Any, dumps: bool = True):
        if isinstance(value, enum.Enum):
            return value.value
        elif isinstance(value, datetime):
            return value.isoformat()
        elif isinstance(value, bool):
            return str(int(value))
        elif isinstance(value, str):
            return value
        if is_dataclass(value):
            return self.serialise_value(asdict(value), dumps)
        elif isinstance(value, BaseModel):
            return self.serialise_value(value.dict(), dumps)
        else:
            if isinstance(value, dict):
                value = {
                    key: self.serialise_value(value, False)
                    for key, value in value.items()
                }
            elif isinstance(value, Iterable):
                value = [
                    self.serialise_value(item, False)
                    for item in value
                ]

            if not dumps:
                return value

            try:
                return json.dumps(value)
            except:
                return str(value)

    def to_str(self):
        values = []

        for value in asdict(self).values():
            values.append(self.serialise_value(value))

        cursor = self.SEPARATOR.join(values)
        return base64.urlsafe_b64encode(cursor.encode()).decode()

    @classmethod
    def depend(
            cls: Type[T], cursor: str | None = Query(None, nullable=True)
    ) -> T | None:
        if not cursor:
            return None
        return cls.from_str(cursor)

    @classmethod
    def from_str(cls, s: str):
        if cls.SEPARATOR not in s:
            s = base64.urlsafe_b64decode(s.encode()).decode()

        values = [x for x in s.split(cls.SEPARATOR) if x != ""]

        field_names: list[str] = []
        required_fields: list[str] = []

        for field in fields(cls):
            field_names.append(field.name)
            if field.default is MISSING and field.default_factory is MISSING:
                required_fields.append(field.name)

        if len(values) < len(required_fields):
            raise ValueError("Values length is less than required fields")

        for i, value in enumerate(values):
            if value == "null":
                values[i] = None

        return cls(**dict(zip(field_names, values)))

    def __str__(self):
        return self.to_str()

    def json(self):
        return self.to_str()

    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v: Any):
        if isinstance(v, cls):
            return v
        if not isinstance(v, str):
            raise TypeError(f"Excepted {cls.__name__} or str")
        return cls.from_str(v)
