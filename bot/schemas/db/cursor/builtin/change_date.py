from dataclasses import dataclass
from datetime import datetime
from typing import Protocol

from ..cursor import Cursor, CursorDirection


class ObjType(Protocol):
    id: int
    change_date: datetime


@dataclass
class ChangeDateCursor(Cursor):
    id: int
    change_date: datetime

    @classmethod
    def obj(cls, obj: ObjType, direction: CursorDirection = CursorDirection.NEXT):
        return cls(direction=direction, id=obj.id, change_date=obj.change_date)
