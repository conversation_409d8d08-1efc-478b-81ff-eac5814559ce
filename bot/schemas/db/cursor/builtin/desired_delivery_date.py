from dataclasses import dataclass
from datetime import datetime
from typing import Protocol

from ..cursor import Cursor, CursorDirection


class ObjType(Protocol):
    id: int
    desired_delivery_date: datetime


@dataclass
class DesiredDeliveryDateCursor(Cursor):
    id: int
    desired_delivery_date: datetime | None = None

    @classmethod
    def obj(cls, obj: ObjType, direction: CursorDirection = CursorDirection.NEXT):
        return cls(
            direction=direction, id=obj.id,
            desired_delivery_date=obj.desired_delivery_date
        )
