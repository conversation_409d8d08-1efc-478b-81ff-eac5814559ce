from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass
from fastapi import Depends, Query
from typing import Annotated

from schemas import IDCursor

from .base import ListCursorResponse


class NotificationSettingSchema(BaseModel):
    profile_id: int
    name: str
    logo_url: str | None = Field(None, nullable=True)
    is_enabled: bool


class CRMNotificationSettingsResponse(ListCursorResponse[NotificationSettingSchema]):
    pass


@dataclass
class CRMNotificationSettingsParams:
    search: str | None = None
    limit: int = Query(10, gt=0)
    offset: int | None = Query(None, nullable=True)
    cursor: Annotated[IDCursor | None, Depends(IDCursor.depend)] = None


class CRMUpdateNotificationSettingsForAllProfiles(BaseModel):
    is_enabled: bool


class CRMNotificationSettingsAllProfiles(BaseModel):
    is_all_disabled: bool


class CRMUpdateNotificationSettingsItem(BaseModel):
    profile_id: int
    is_enabled: bool
