from enum import Enum, unique
from typing import Annotated

from enum_tools import StrEnum
from pydantic import BaseModel, Field

from schemas.base import EnumWithValues


class LoyaltySettingsTarget(StrEnum):
    STORE = "store"
    INVOICE_TEMPLATE = "invoice_template"
    EWALLET = "ewallet"
    EWALLET_MERCHANT = "ewallet_merchant"
    BRAND = "brand"
    PROFILE = "profile"
    PRODUCT = "product"


class LoyaltySettingsObjectType(StrEnum):
    """Типи об'єктів для прив'язки налаштувань лояльності"""
    PROFILE = "profile"
    BRAND = "brand"
    STORE = "store"
    PRODUCT = "product"
    INVOICE_TEMPLATE = "invoice_template"
    EWALLET = "ewallet"
    EWALLET_MERCHANT = "ewallet_merchant"


class LoyaltySettingsApplicableTypes(Enum):
    FOR_PARTICIPANTS = "for_participants"
    FOR_ALL = "for_all"


class LoyaltySettingsTypeClientAuth(Enum):
    WEB = "web"
    BOT = "bot"


@unique
class LoyaltyApplicableTypes(EnumWithValues):
    for_all = 'for_all'
    for_participants = 'for_participants'


class LoyaltySettingsData(BaseModel):
    profile_id: Annotated[int | None, Field(nullable=True)] = None
    store_id: Annotated[int | None, Field(nullable=True)] = None
    product_id: Annotated[int | None, Field(nullable=True)] = None
    invoice_template_id: Annotated[int | None, Field(nullable=True)] = None
    ewallet_id: Annotated[int | None, Field(nullable=True)] = None
    ewallet_merchant_id: Annotated[int | None, Field(nullable=True)] = None
    brand_id: Annotated[int | None, Field(nullable=True)] = None
    include_disabled: bool = True  # За замовчуванням перевіряємо is_enabled


class GetLoyaltySettingsByContextParams(LoyaltySettingsData):
    target: LoyaltySettingsTarget


class LoyaltySettingsSchema(BaseModel):
    """Схема для налаштувань лояльності, яка працює поза контекстом БД"""

    # Основні налаштування InCust
    terminal_api_key: str
    server_url: str

    # Додаткові налаштування
    white_label_id: str | None = None
    terminal_id: str | None = None
    loyalty_id: str | None = None

    # Налаштування клієнта
    type_client_auth: LoyaltySettingsTypeClientAuth | None = None

    # Налаштування заборон
    prohibit_redeeming_bonuses: bool = False
    prohibit_redeeming_coupons: bool = False

    # Налаштування застосування лояльності
    loyalty_applicable_type: LoyaltySettingsApplicableTypes = (
        LoyaltySettingsApplicableTypes.FOR_PARTICIPANTS)

    # Метадані
    priority: int = 0
    is_enabled: bool = True
    name: str | None = None
    description: str | None = None

    # Контекстні поля
    brand_id: int | None = None
    ewallet_id: int | None = None
    ewallet_merchant_id: int | None = None
    store_id: int | None = None
    product_id: int | None = None
    profile_id: int | None = None
    invoice_template_id: int | None = None

    # Додаткові дані
    json_data: dict | None = None

    class Config:
        orm_mode = True


class LoyaltySettingsResponseSchema(BaseModel):
    """Схема для налаштувань лояльності"""

    # Основні налаштування InCust
    id: int

    # Налаштування клієнта
    type_client_auth: LoyaltySettingsTypeClientAuth | None = None

    # Налаштування заборон
    prohibit_redeeming_bonuses: bool = False
    prohibit_redeeming_coupons: bool = False

    # Налаштування застосування лояльності
    loyalty_applicable_type: LoyaltySettingsApplicableTypes = (
        LoyaltySettingsApplicableTypes.FOR_PARTICIPANTS)

    is_enabled: bool = True

    class Config:
        orm_mode = True