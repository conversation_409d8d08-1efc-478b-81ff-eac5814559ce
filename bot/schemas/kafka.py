from datetime import datetime
from email.message import Message as EmailMessage
from email.mime.base import MIMEBase
from enum import Enum
from typing import Literal, Union

from async_firebase.messages import Message as FCMMessage
from pydantic import BaseModel

from .webhooks import WebhookEvent


class BaseKafkaMessageValue(BaseModel):
    retry_count: int = 0
    retry_chunk_size: int = 1


class PushMessageValue(BaseKafkaMessageValue):
    device_token: str
    fcm_message: FCMMessage


class TelegramMessageValue(BaseKafkaMessageValue):
    chat_id: int
    user_id: int | None = None
    user_name: str | None = None

    bot_id: int | Literal["root", "admin", "service"]
    bot_token: str
    bot_name: str  # for logs

    sending_data: dict  # params for send_tg_message


class WhatsappMessageValue(BaseKafkaMessageValue):
    bot_token: str
    bot_from: str
    user_id: int | None = None
    user_name: str | None = None  # for logs
    bot_id: int
    bot_name: str  # for logs
    wa_phone: str
    sending_data: dict  # params for send_wa_message


class SendEmailData(BaseModel):
    class Config:
        arbitrary_types_allowed = True

    subject: str
    body: str
    attachments: list[Union[EmailMessage, MIMEBase, str]] | None = None
    html: str | None = None
    from_name: str | None = None


class EmailMessageValue(BaseKafkaMessageValue):
    sender: str
    sender_password: str

    destination: str
    user_id: int | None = None
    user_name: str | None = None

    sending_data: SendEmailData


class TaskValue(BaseKafkaMessageValue):
    id: int
    type: str  # TaskTypeEnum
    type_task: str  # TaskTypeTaskEnum
    user_id: int
    group_id: int
    ai_model: str  # TaskAiModelTypeEnum
    object_id: int
    prompt: str
    name: str
    description: str | None = None
    status: str | None = None # TaskStatusEnum


class WebhookMessageValue(BaseKafkaMessageValue, WebhookEvent):
    event_created_datetime: datetime
    event_uuid: str


class MailingMessageValue(BaseKafkaMessageValue):
    mailing_message: str
    mailing_id: int
    mailing_message_id: int | None = None
    subject: str | None = None
    test: bool = False
    template_id: int | None = None
    variables: list[dict] | None = None


class MailingBotMessageBase(BaseKafkaMessageValue):
    user_id: int | None = None
    user_name: str | None = None
    bot_id: int
    bot_token: str
    bot_name: str  # for logs
    sending_data: dict  # params for send_wa_message


class MailingTelegramMessageValue(MailingBotMessageBase):
    chat_id: int


class MailingWhatsappMessageValue(MailingBotMessageBase):
    bot_from: str
    bot_name: str  # for logs
    wa_phone: str
    template_id: int | None = None
    variables: dict | None = None


class EmailMailingMessageValue(MailingMessageValue, BaseKafkaMessageValue):
    sender: str
    sender_password: str
    destination: str
    subject: str
    from_name: str


class NotificationMessageType(str, Enum):
    ORDER_STATUS = "order_status"
    INVOICE_PAYMENT = "invoice_payment"


class OrderStatusMessageValue(BaseKafkaMessageValue):
    """Повідомлення для обробки сповіщень про зміну статусу замовлення."""
    store_order_id: int
    status: str
    initiated_by: str  # StatusChangeInitiatedBy
    order_shipping_status_id: int | None = None

    invoice_id: int | None = None
    menu_in_store_id: int | None = None
    
    need_process_loyalty: bool = False
    is_cancel: bool = False
    is_payed_new_order: bool = False
    is_payment_error: bool = False
    
    comment: str | None = None
    source: str | None = None
    ignore_session_id: int | None = None


class NotificationMessageValue(BaseKafkaMessageValue):
    """Універсальне повідомлення для обробки різних типів сповіщень."""
    notification_type: NotificationMessageType
    
    # Для ORDER_STATUS
    store_order_id: int | None = None
    status: str | None = None
    initiated_by: str | None = None
    order_shipping_status_id: int | None = None
    need_process_loyalty: bool = False
    is_cancel: bool = False
    is_payed_new_order: bool = False
    is_payment_error: bool = False
    comment: str | None = None
    source: str | None = None
    
    # Для INVOICE_PAYMENT
    group_id: int | None = None
    brand_id: int | None = None
    invoice_id: int | None = None
    invoice_user_id: int | None = None
    is_full_bonuses_payment: bool = False
    terminal_key: str | None = None
    
    # Спільні поля
    lang: str | None = None  # Мова для формування повідомлень (для ORDER_STATUS та INVOICE_PAYMENT)
    menu_in_store_id: int | None = None
    ignore_session_id: int | None = None
