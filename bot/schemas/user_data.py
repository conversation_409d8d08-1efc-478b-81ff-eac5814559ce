import enum
from datetime import datetime
from typing import Annotated, Any, Generic

from pydantic import BaseModel, Field
from typing_extensions import TypeAlias, TypeVar

from schemas import BaseORMModel


class UserDataTarget(enum.Enum):
    GROUP = "group"


class UserDataTargetData(BaseModel):
    target: UserDataTarget
    group_id: Annotated[int | None, Field(nullable=True)] = None


class CreateUserDataData(UserDataTargetData):
    type: Annotated[str, Field(min_length=3, max_length=255)]
    data: Any


DataT = TypeVar("DataT", default=Any)


class UserDataDefaultSchema(BaseORMModel, Generic[DataT]):
    id: int
    time_created: datetime

    user_id: int

    type: str

    target: UserDataTarget
    group_id: Annotated[int | None, Field(nullable=True)] = None
    data: DataT

    position: int


UserDataSchema: TypeAlias = UserDataDefaultSchema


class UserDataListParams(BaseModel):
    type: str | None = None
    target: UserDataTarget | None = None
    group_id: int | None = None


class MoveUserDataData(UserDataListParams):
    new_position: int


class UpdateUserDataByIdData(BaseModel):
    new_data: Any
    partial: bool = False
