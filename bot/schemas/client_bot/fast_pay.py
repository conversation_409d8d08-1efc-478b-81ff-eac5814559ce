from typing import Annotated

from pydantic import BaseModel, Field


class FastPayStateData(BaseModel):
    group_id: int
    invoice_template_id: int
    ewallet_id: int | None = None
    count: Annotated[int, Field(gt=0)] = 1
    is_amount_fixed: bool = False
    entered_amount: float | int | None = None
    payment_settings_id: int | None = None
    object_payment_settings_id: int | None = None
    invoice_id: int | None = None
    entered_bonus_amount: float | None = None
    bonus_action: str | None = None
    max_bonuses_amount: float | None = None
    calculated_amounts: dict | None = None
    incust_check: dict | None = None
    discount_amount: float | None = None
    loyalty_calculated: bool = False
    amount_after_discount: float | None = None
    final_amount: float | None = None  # Фінальна сума до сплати
    items_displayed: bool | None = False
    is_loyalty: bool = False  # Чи доступна лояльність для цього інвойсу
    bonus_payment_limit: float | None = None  # Ліміт використання бонусів у відсотках
    user_available_bonuses: float | None = None  # Доступні бонуси користувача
    initial_max_bonuses: float | None = None  # Початкова максимальна сума бонусів
    is_fully_covered: bool = False  # Чи повністю покрита сума знижками/бонусами
    incust_prohibit_redeeming_bonuses: bool = False  # Чи заборонено списувати бонуси
    user_phone: str | None = None
    used_credit: float | None = None
    ewallet_title: str | None = None
    ewallet_message: str | None = None

