from dataclasses import field
from datetime import datetime
from enum import Enum
from typing import Annotated, Any, Callable, Type

from fastapi import Query
from pydantic import BaseModel, <PERSON><PERSON>
from pydantic.dataclasses import dataclass
from sqlalchemy.engine import Row

from .db.cursor import Cursor, CursorDirection


class FilterType(Enum):
    EQUAL = "equal"
    NOT_EQUAL = "not_equal"

    ONE_OF = "one_of"
    NOT_ONE_OF = "not_one_of"

    EMPTY = "empty"
    NOT_EMPTY = "not_empty"

    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"

    GREATER_THAN = "greater_than"
    GREATER_THAN_OR_EQUAL = "greater_than_or_equal"

    LESS_THAN = "less_than"
    LESS_THAN_OR_EQUAL = "less_than_or_equal"


class BaseFilterData(BaseModel):
    type: FilterType
    field: str


class FilterData(BaseFilterData):
    value: float | int | str | datetime | list[float | int | str] | None = None


class ExtraFilterData(BaseFilterData):
    name: str


class SortData(BaseModel):
    field: str
    desc: bool = False
    if_joined: bool = False


class QueryBuilderParams(BaseModel):
    fields: list[str]
    sort: Annotated[list[SortData], Query(default_factory=list)]
    filters: Annotated[list[FilterData], Query(default_factory=list)]
    cursor: Annotated[str | None, Query(nullable=True)] = None
    limit: int | None = Query(10)


class QueryBuilderAPIParams(QueryBuilderParams):
    sort: Annotated[list[Json[SortData]], Query(default_factory=list)]
    filters: Annotated[list[Json[FilterData]], Query(default_factory=list)]


@dataclass
class QueryBuilderObjConf:
    model: Type
    fields: list[str]
    amount_fields: set[str] = field(default_factory=set)
    default_fields: set[str] = field(default_factory=set)
    fields_processors: dict[str, Callable] = field(default_factory=dict)
    select_from: bool = False
    join_expr: Any = None
    outerjoin: bool = False
    depends_on: tuple[str, ...] = field(default_factory=tuple)

    fields_as_row_data: bool = False

    default_obj: bool = False


@dataclass
class QueryBuilderSettings:
    objects: dict[str, QueryBuilderObjConf]
    extra_filters: list[ExtraFilterData] = field(default_factory=list)
    default_sort: list[SortData] = field(default_factory=list)

    row_data_model: type | None = None

    fields_cache: dict[str, list[str]] = field(default_factory=dict)

    @property
    def fields(self):
        if not self.fields_cache:
            for obj_name, obj_settings in self.objects.items():
                self.fields_cache[obj_name] = obj_settings.fields.copy()
        return self.fields_cache


@dataclass
class QueryBuilderCursorField:
    field: str
    desc: bool = False


@dataclass
class QueryBuilderCursor(Cursor):
    fields: (
            Json[list[QueryBuilderCursorField]] |
            list[QueryBuilderCursorField]
    )
    values: (
            Json[list[float | int | datetime | str | None]] |
            list[float | int | datetime | str | None]
    )

    @classmethod
    def build(
            cls,
            fields: list[QueryBuilderCursorField],
            row: Row,
    ):
        return cls(
            direction=CursorDirection.NEXT,
            fields=fields,
            values=[
                row[sort_field.field]
                for sort_field in fields
            ]
        )
