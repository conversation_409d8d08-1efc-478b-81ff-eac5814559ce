from dataclasses import dataclass
from datetime import datetime

from fastapi import Form, UploadFile, File
from pydantic import BaseModel

from ..base import BaseORMModel


class StorageSchema(BaseORMModel):
    id: int
    name: str

    media_id: int
    media_url: str
    media_type: str
    media_mime_type: str
    media_file_size: int

    is_multi_load: bool
    datetime_upload: datetime

    read_allowed: bool = False
    edit_allowed: bool = False


class UpdateMediaData(BaseModel):
    name: str | None = None


@dataclass
class UploadMediaData:
    name: str = Form()
    file: UploadFile = File()
