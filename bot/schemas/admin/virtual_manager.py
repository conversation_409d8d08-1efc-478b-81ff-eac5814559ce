from typing import Literal, TYPE_CHECKING

from pydantic import BaseModel, Field

from ..base import BaseORMModel
from ..virtual_manager import (
    VMStepReminderMode, VirtualManagerStepSchema,
    VirtualManagerStepTranslationSchema,
)

if TYPE_CHECKING:
    from core.chat.virtual_manager.interactives.schemas import \
        AdminModifyVirtualManagerInteractiveUnionSchema


class BaseAdminVirtualManagerSchema(BaseORMModel):
    name: str = Field(min_length=1)
    name_id: str | None = Field(None, nullable=True, max_length=100)

    allow_click_old_messages: bool
    start_only_in_owner_profile: bool

    on_start_delay: float
    message_delay: float

    is_reminder_enabled: bool
    reminder_delay: float
    reminds_count: int

    bot_hello_message_enabled: bool


class AdminVirtualManagerOrmSchema(BaseAdminVirtualManagerSchema):
    id: int
    profile_id: int
    creator_id: int


class AdminVirtualManagerListSchema(AdminVirtualManagerOrmSchema):
    read_allowed: bool = False
    edit_allowed: bool = False


class AdminVirtualManagerSchema(AdminVirtualManagerListSchema):
    steps: list[VirtualManagerStepSchema]


class BaseAdminVirtualManagerMutateSchema(BaseAdminVirtualManagerSchema):
    allow_click_old_messages: bool | None = None
    start_only_in_owner_profile: bool | None = None

    on_start_delay: float | None = None
    message_delay: float | None = None

    is_reminder_enabled: bool | None = None
    reminder_delay: float | None = None
    reminds_count: int | None = None

    bot_hello_message_enabled: bool | None = None


class AdminUpdateVirtualManagerData(BaseAdminVirtualManagerMutateSchema):
    name: str | None = Field(None, nullable=True)


class AdminModifyVirtualManagerStepData(BaseModel):
    id: int | str
    text: str | None = Field(None, nullable=True)
    media_id: int | None = Field(None, nullable=True)
    media_url: str | None = Field(None, nullable=True)
    media_type: str | None = Field(
        None, nullable=True, description="Ignored. Just for admin panel typings"
    )

    reminder_mode: VMStepReminderMode | None = Field(None, nullable=True)
    reminder_delay: float | None = Field(None, nullable=True)
    reminds_count: int | None = Field(None, nullable=True)

    translations: dict[str, VirtualManagerStepTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )

    interactives: list[
                      "AdminModifyVirtualManagerInteractiveUnionSchema"] | None = Field(
        None, nullable=True
    )


class AdminCreateVirtualManagerData(BaseAdminVirtualManagerMutateSchema):
    copy_id: int | str | None = Field(None, nullable=True)
    create_mode: Literal["create", "copy", "copy_exist"]
    is_copy: bool = Field(False, nullable=True)
    copy_exist_id: int | None = Field(None, nullable=True)


class AdminSetVirtualManagerStepsData(BaseModel):
    steps: list[AdminModifyVirtualManagerStepData]


class VMLinkResponse(BaseModel):
    link: str
    virtual_manager_id: int


class VMNameData(BaseORMModel):
    id: int
    name: str


class VMNameResponse(BaseModel):
    data: VMNameData | None = Field(None, nullable=True)
