from enum import Enum

from pydantic import Field

from ..base import BaseORMModel


class TypeAdminEmailEnum(Enum):
    STORE = "store"
    BRAND = "brand"


class BaseAdminAdminEmailSchema(BaseORMModel):
    email: str = Field(min_length=5, null=False)
    store_id: int = Field(None, null=True)


class AdminAdminEmailListSchema(BaseAdminAdminEmailSchema):
    id: int
    creator_id: int = Field(None, null=True)
    type_email: TypeAdminEmailEnum = Field(null=False)


class AdminAdminEmailSchema(AdminAdminEmailListSchema):
    ...


class AdminCreateAdminEmailSchema(BaseAdminAdminEmailSchema):
    ...


class AdminUpdateAdminEmailSchema(BaseAdminAdminEmailSchema):
    ...
