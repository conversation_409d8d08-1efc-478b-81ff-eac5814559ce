from typing import Literal
from pydantic import BaseModel, Field

AdminQrMenuRedirectTypeLiteral = Literal["only_tg", "try_tg", "web"]
AdminQrMenuPaymentOption = Literal["disabled", "amount_from_user", "amount_from_template"]

AdminQrMenuUrlType = Literal["web", "bot"]


class AdminQrMenuListSchema(BaseModel):
    id: int
    comment: str
    qr_media_urls: list[str]


class AdminQrMenuUrl(BaseModel):
    display_name: str
    url: str
    type: AdminQrMenuUrlType


class AdminQrMenuSchema(BaseModel):
    id: int
    comment: str
    store_id: int | None = Field(None, nullable=True)
    store_name: str | None = Field(None, nullable=True)
    redirect_type: AdminQrMenuRedirectTypeLiteral
    payment_option: AdminQrMenuPaymentOption
    invoice_template_id: int | None = Field(None, nullable=True)
    invoice_template_name: str | None = Field(None, nullable=True)
    is_e_menu: bool
    need_save_as_active: bool
    urls: list[AdminQrMenuUrl]


class AdminCreateQrMenuData(BaseModel):
    comment: str
    store_id: int | None = Field(None, nullable=True)
    redirect_type: AdminQrMenuRedirectTypeLiteral = "web"
    payment_option: AdminQrMenuPaymentOption = "disabled"
    invoice_template_id: int | None = Field(None, nullable=True)
    is_e_menu: bool = False
    need_save_as_active: bool = True


class AdminUpdateQrMenuData(BaseModel):
    comment: str
    store_id: int | None = Field(None, nullable=True)
    redirect_type: AdminQrMenuRedirectTypeLiteral
    payment_option: AdminQrMenuPaymentOption
    invoice_template_id: int | None = Field(None, nullable=True)
    is_e_menu: bool
    need_save_as_active: bool
