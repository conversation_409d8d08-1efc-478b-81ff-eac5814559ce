from typing import Literal, TypeAlias

from pydantic import BaseModel, Field

from ..incust.base import RulesTypeLiteral
from ..payment_settings.schemas import PaymentSettingsMethodLiteral

BasicProvidersType = Literal[
    "liqpay", "stripe", "epay", "comsa",
    "flutterwave", "fondy", "freedompay",
    "orange", "kpay", "pl24", "tpay", "unipos",
    "wave", "directpay", "momo", "tj", "airtel", "ewallet"
]

WithCommentStateLiteral = Literal["disabled", "required", "optional"]


class ShipmentToPaymentSchema(BaseModel):
    shipment_id: int | None = None
    payment_id: int | None = None
    is_enabled: bool = True


class PaymentProviderBaseSchema(BaseModel):
    payer_fee_value: float | int | None = Field(nullable=True, default=None)
    payer_fee_percent: float | int | None = Field(nullable=True, default=None)


class LiqPayPaymentProviderData(PaymentProviderBaseSchema):
    private_key: str
    public_key: str
    is_need_fiscal: bool | None = Field(default=None, nullable=True)
    tax_list: str | None = Field(
        default=None, nullable=True
    )  # TODO: payments, questionable


class EpayPaymentProviderData(PaymentProviderBaseSchema):
    client_id: str
    terminal_id: str
    client_secret: str


class ComsaPaymentProviderData(PaymentProviderBaseSchema):
    vendor_id: str
    secret_key: str
    is_sandbox: bool | None = Field(default=None, nullable=True)


class FlutterWavePaymentProviderData(PaymentProviderBaseSchema):
    encryption_key: str
    secret_key: str


class FondyPaymentProviderData(PaymentProviderBaseSchema):
    merchant_id: str
    secret_key: str


class FreedomPayProviderData(PaymentProviderBaseSchema):
    merchant_id: str
    merchant_secret: str
    is_sandbox: bool | None = Field(default=None, nullable=True)


class OrangePaymentProviderData(PaymentProviderBaseSchema):
    partner_code: str
    client_id: str
    client_secret: str

class KPayPaymentProviderData(PaymentProviderBaseSchema):
    client_id: str
    client_secret: str
    phone: str
    is_sandbox: bool | None = Field(default=None, nullable=True)


class Pl24PaymentProviderData(PaymentProviderBaseSchema):
    is_sandbox: bool | None = Field(default=None, nullable=True)
    merchant_id: str
    pos_id: str
    crc: str
    secret: str


class TPayPaymentProviderData(PaymentProviderBaseSchema):
    security_code: str
    client_id: str
    client_secret: str


class UniposPaymentProviderData(PaymentProviderBaseSchema):
    login: str | None = Field(None, nullable=True)
    password: str | None = Field(None, nullable=True)
    account: str | None = Field(None, nullable=True)
    filial_code: str | None = Field(None, nullable=True)
    inn: str | None = Field(None, nullable=True)


class WavePaymentProviderData(PaymentProviderBaseSchema):
    api_key: str
    secret: str
    aggregated_merchant_id: str | None = Field(nullable=True, default=None)


class IncustPayMerchantData(BaseModel):
    payment_method: BasicProvidersType
    # incust_account_id: str
    data: dict
    business_payment_data_id: int



class IncustPayPaymentProviderData(PaymentProviderBaseSchema):
    server_api_url: str
    rules_type: RulesTypeLiteral | None = None
    terminal_api_key: str
    terminal_server_api_url: str
    card_payment_enabled: bool
    charge_percent: float | int | None = Field(nullable=True, default=None)
    charge_fixed: float | int | None = Field(nullable=True, default=None)
    merchant_data: list[IncustPayMerchantData] | None = None


class StripePaymentProviderData(PaymentProviderBaseSchema):
    public_key: str
    secret_key: str


class EWalletPaymentProviderData(PaymentProviderBaseSchema):
    ewallet_id: str
    merchant_data: list[IncustPayMerchantData] | None = Field(
        None, description="merchant data"
    )
    is_pay_fee_self: bool = Field(False, description="merchant pays fee self")


class TelegramPayPaymentData(PaymentProviderBaseSchema):
    payment_token: str


class DirectpayPaymentProviderData(BaseModel):
    company_token: str | None = Field(nullable=True, default=None)


class DirectpaySchema(BaseModel):
    type: Literal["directpay"]
    data: DirectpayPaymentProviderData


class MomoPaymentProviderData(BaseModel):
    subscription_key: str | None = Field(nullable=True, default=None)
    user_id: str | None = Field(nullable=True, default=None)
    user_api_key: str | None = Field(nullable=True, default=None)


class MomoSchema(BaseModel):
    type: Literal["momo"]
    data: MomoPaymentProviderData


class TjPaymentProviderData(BaseModel):
    client_id: str | None = Field(nullable=True, default=None)
    client_secret: str | None = Field(nullable=True, default=None)
    merchant_id: str | None = Field(nullable=True, default=None)
    profile_id: str | None = Field(nullable=True, default=None)
    is_sandbox: bool | None = Field(default=None, nullable=True)


class TjSchema(BaseModel):
    type: Literal["tj"]
    data: TjPaymentProviderData


class AirTelPaymentProviderData(BaseModel):
    client_id: str | None = Field(nullable=True, default=None)
    client_secret: str | None = Field(nullable=True, default=None)
    pin: str | None = Field(nullable=True, default=None)
    country: str | None = Field(nullable=True, default=None)
    profile_id: str | None = Field(nullable=True, default=None)
    is_sandbox: bool


class AirTelSchema(BaseModel):
    type: Literal["airtel"]
    data: AirTelPaymentProviderData


class TPaySchema(BaseModel):
    type: Literal["tpay"]
    data: TPayPaymentProviderData


class PL24Schema(BaseModel):
    type: Literal["pl24"]
    data: Pl24PaymentProviderData


class OrangeSchema(BaseModel):
    type: Literal["orange"]
    data: OrangePaymentProviderData

class KPaySchema(BaseModel):
    type: Literal["kpay"]
    data: KPayPaymentProviderData

class FreedomPaySchema(BaseModel):
    type: Literal["freedompay"]
    data: FreedomPayProviderData


class FondySchema(BaseModel):
    type: Literal["fondy"]
    data: FondyPaymentProviderData


class FlutterWaveSchema(BaseModel):
    type: Literal["flutterwave"]
    data: FlutterWavePaymentProviderData


class ComsaSchema(BaseModel):
    type: Literal["comsa"]
    data: ComsaPaymentProviderData


class EPaySchema(BaseModel):
    type: Literal["epay"]
    data: EpayPaymentProviderData


class LiqPaySchema(BaseModel):
    type: Literal["liqpay"]
    data: LiqPayPaymentProviderData


class StripeSchema(BaseModel):
    type: Literal["stripe"]
    data: StripePaymentProviderData


class IncustPaySchema(BaseModel):
    type: Literal["incust_pay"]
    data: IncustPayPaymentProviderData


class TelegramPaySchema(BaseModel):
    type: Literal["tg_pay"]
    data: TelegramPayPaymentData


class UniposPaySchema(BaseModel):
    type: Literal["unipos"]
    data: UniposPaymentProviderData


class WavePaySchema(BaseModel):
    type: Literal["wave"]
    data: WavePaymentProviderData


class EWalletPaySchema(BaseModel):
    type: Literal["ewallet"]
    data: EWalletPaymentProviderData


class CustomPaySchema(BaseModel):
    type: Literal["custom"]
    data: PaymentProviderBaseSchema


PaymentProviderSchema: TypeAlias = (StripeSchema | LiqPaySchema | EPaySchema |
                                    ComsaSchema | FlutterWaveSchema |
                                    FondySchema | FreedomPaySchema | OrangeSchema | KPaySchema |
                                    PL24Schema | TPaySchema |
                                    IncustPaySchema | TelegramPaySchema |
                                    UniposPaySchema | WavePaySchema |
                                    CustomPaySchema | DirectpaySchema | MomoSchema |
                                    TjSchema | AirTelSchema |
                                    EWalletPaySchema)


class PaymentProviderItemFieldOptionSchema(BaseModel):
    value: str
    label: str


class PaymentProviderItemFieldSchema(BaseModel):
    type: Literal["string", "number", "boolean", "alert"]
    is_required: bool
    name: str
    options: list[PaymentProviderItemFieldOptionSchema] | None = None
    readonly: bool = False
    field_size: int = 6
    value: str | None = None
    description: str | None = None
    is_copyable: bool | None = False
    is_hidden: bool | None = False


class PaymentProviderItemSchema(BaseModel):
    provider: BasicProvidersType | PaymentSettingsMethodLiteral
    icon_url: str | None = None
    fields: list[PaymentProviderItemFieldSchema]
    provider_id: int | None = None
    provider_name: str | None = None
    business_payment_data_id: int | None = None


class EWalletAdminProfileSchema(BaseModel):
    id: int
    name: str
    description: str
    info: str | None = None
    is_enabled: bool


class PaymentProvidersSchema(BaseModel):
    schemas: list[PaymentProviderItemSchema]
    extra_data: dict[str, str | dict]


class AdminPaymentSettingsListSchema(BaseModel):
    id: int
    position: int
    name: str | None = Field(default=None, nullable=True)
    default_name: str | None = Field(default=None, nullable=True)
    payment_method: PaymentSettingsMethodLiteral | BasicProvidersType
    is_enabled: bool
    icon_url: str | None = Field(default=None, nullable=True)
    is_online: bool
    json_data: dict | None = None


class WaveStoreMerchantItemSchema(BaseModel):
    store_id: int
    store_name: str
    merchant_id: str
    payment_settings_id: int | None = Field(default=None, nullable=True)
    payment_settings_name: str | None = Field(default=None, nullable=True)


class PaymentSettingsExtraDataSchema(BaseModel):
    aggregated_merchant_ids: list[WaveStoreMerchantItemSchema]


class CreateAdminPaymentSettingsListSchema(BaseModel):
    payment_setting: AdminPaymentSettingsListSchema
    extra_data: PaymentSettingsExtraDataSchema | None = Field(
        default=None, nullable=True
    )


class AdminCreatePaymentDataBase(BaseModel):
    payment_method: PaymentSettingsMethodLiteral | BasicProvidersType
    json_data: PaymentProviderSchema | None = Field(default=None, nullable=True)
    name: str | None = Field(default=None, nullable=True)
    description: str | None = Field(default=None, nullable=True)
    with_comment: WithCommentStateLiteral = "disabled"
    payment_to_shipments: list[ShipmentToPaymentSchema] | None = Field(
        default=None, nullable=True
    )
    label_comment: str | None = Field(default=None, nullable=True)
    post_payment_info: str | None = Field(default=None, nullable=True)


class ObjectPaymentSettingsTranslationSchema(BaseModel):
    post_payment_info: str | None = Field(None, nullable=True)


class UpdateAdminPaymentStore(BaseModel):
    store_id: int
    is_enabled: bool | None = None
    json_data: PaymentProviderSchema | None = Field(default=None, nullable=True)
    post_payment_info: str | None = Field(default=None, nullable=True)
    translations: dict[str, ObjectPaymentSettingsTranslationSchema | None] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations to be updated. "
            "The key is language ISO code and "
            "the value is fields translations object."
        )
    )


class AdminStorePaymentData(UpdateAdminPaymentStore):
    payment_settings_id: int
    name: str | None = None
    default_name: str | None = None


class CreateAdminStorePaymentData(UpdateAdminPaymentStore):
    store_id: int | None = None
    payment_settings_id: int


class UpdateAdminPaymentInvoiceTemplate(BaseModel):
    invoice_template_id: int
    is_enabled: bool | None = None
    json_data: PaymentProviderSchema | None = Field(default=None, nullable=True)
    post_payment_info: str | None = Field(default=None, nullable=True)
    translations: dict[str, ObjectPaymentSettingsTranslationSchema | None] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations to be updated. "
            "The key is language ISO code and "
            "the value is fields translations object."
        )
    )


class AdminInvoicePaymentInvoiceTemplate(UpdateAdminPaymentInvoiceTemplate):
    invoice_template_id: int | None = None
    payment_settings_id: int


class AdminInvoicePaymentInvoiceTemplateData(UpdateAdminPaymentInvoiceTemplate):
    payment_settings_id: int
    name: str | None = None
    default_name: str | None = None


class AdminCreatePaymentData(AdminCreatePaymentDataBase):
    stores_json_data: list[UpdateAdminPaymentStore] | None = Field(
        nullable=True, default=None
    )
    invoice_templates_json_data: list[UpdateAdminPaymentInvoiceTemplate] | None = Field(
        nullable=True, default=None
    )


class AdminPaymentTranslationSchema(BaseModel):
    name: str | None = Field(None, nullable=True)
    description: str | None = Field(None, nullable=True)
    post_payment_info: str | None = Field(None, nullable=True)


class AdminPaymentSchema(AdminCreatePaymentDataBase):
    id: int
    default_name: str | None = Field(default=None, nullable=True)
    is_enabled: bool
    icon_url: str | None = Field(default=None, nullable=True)
    is_online: bool
    translations: dict[str, AdminPaymentTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminUpdatePaymentMainDataSchema(AdminCreatePaymentData):
    label_comment: str | None = Field(default=None, nullable=True)
    post_payment_info: str | None = Field(default=None, nullable=True)
    is_enabled: bool
    translations: dict[str, AdminPaymentTranslationSchema | None] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations to be updated."
            "The key is language ISO code and "
            "the value is fields translations object."
            "(specify null as value to delete translation or "
            "null as field value to delete field translation,"
            "if auto translations enabled will be automatically translated after "
            "deleting)"
        )
    )


class AdminPaymentStore(UpdateAdminPaymentStore):
    store_name: str
    object_payment_settings_id: int


class AdminPaymentInvoiceTemplate(UpdateAdminPaymentInvoiceTemplate):
    invoice_template_name: str
    invoice_template_payment_settings_id: int
