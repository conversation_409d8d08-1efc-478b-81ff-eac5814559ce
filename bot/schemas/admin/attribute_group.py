from pydantic import BaseModel, Field

from core.ext.types import ExternalTypeLiteral
from .base import AdminConnectedAttributesSchema
from ..base import BaseORMModel


class AdminAttributeGroupListSchema(BaseORMModel):
    id: int
    name: str
    position: int | None = Field(None, nullable=True)

    internal_name: str | None = Field(None, nullable=True)

    raw_internal_name: str | None = Field(None, nullable=True)

    read_allowed: bool = False
    edit_allowed: bool = False


class AdminAttributeGroupTranslationSchema(BaseORMModel):
    name: str | None = Field(None, nullable=True)


class AdminAttributeGroupSchema(AdminAttributeGroupListSchema):
    attribute_group_id: str
    min: int | None = Field(None, nullable=True)
    max: int | None = Field(None, nullable=True)
    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    attributes: list[AdminConnectedAttributesSchema] = Field(
        default_factory=list,
        description="List of connected attributes. Here will be only attributes "
                    "current user has access to"
    )

    translations: dict[str, AdminAttributeGroupTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminCreateAttributeGroupData(BaseModel):
    attribute_group_id: str
    name: str
    min: int | None = Field(None, nullable=True)
    max: int | None = Field(None, nullable=True)
    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    position: int | None = Field(None, nullable=True)

    translations: dict[str, AdminAttributeGroupTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )

    raw_internal_name: str | None = None


class AdminUpdateAttributeGroupData(BaseModel):
    attribute_group_id: str
    name: str | None = None
    min: int | None = Field(None, nullable=True)
    max: int | None = Field(None, nullable=True)
    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    position: int | None = Field(None, nullable=True)

    raw_internal_name: str | None = Field(None, nullable=True)

    translations: dict[str, AdminAttributeGroupTranslationSchema | None] | None = Field(
        None, nullable=True
    )


class AdminConnectAttributeGroupsData(BaseModel):
    attribute_groups: list[int] = Field(
        # min_items=1,
        description="List of attribute group ids to connect. Access to "
                    "attribute_group:read action is required",
    )
    replace: bool = Field(
        default=False,
        description="Is new list replaces current list"
    )


class AdminAttributeGroupsConnectedResult(BaseModel):
    replaced: bool = False
    connected_attribute_groups: list[AdminConnectedAttributesSchema]


class AdminDisconnectAttributeGroupsData(BaseModel):
    attribute_groups: list[int] | None = Field(
        default=None,
        nullable=True,
        min_items=1,
        description=(
            "List of attribute group ids to disconnect. "
            "Ignored if disconnect_all is specified"
        ),
    )
    disconnect_all: bool = False


class AdminAttributeGroupsDisconnectedResult(BaseModel):
    is_all_deleted: bool = False
    attribute_groups_ids_disconnected: list[int] | None = Field(None, nullable=True)
