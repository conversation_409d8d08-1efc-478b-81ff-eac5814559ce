from datetime import datetime

from fastapi import Query
from pydantic import BaseModel, Field

from ..auth.all import BaseUserSchema, Token
from ..auth.scope import CreateScopeData, ScopeSchema, ScopeTarget


class AdminProfileUser(BaseUserSchema):
    is_own_system_user: bool = False
    scope_names: list[str] = Field(default_factory=list)
    incust_external_id: str | None = Field(None)


class CreateSystemUserData(BaseModel):
    name: str
    scopes: list[CreateScopeData] | None = Field(None, nullable=True)
    token_expire_at: datetime | None = Field(None, nullable=True)


class UpdateSystemUserData(BaseModel):
    full_name: str | None = Field(None, alias="name")


class ScopesResponse(BaseModel):
    target: ScopeTarget
    target_id: int

    scopes: list[ScopeSchema]


class SystemUserCreated(BaseModel):
    user: AdminProfileUser
    scopes: list[ScopeSchema]
    token: Token


class SystemUserToken(BaseModel):
    user_id: int
    token: Token


class GenerateSystemUserTokenData(BaseModel):
    expire_at: datetime | None = Field(None)


class AdminGetUsersListParams(BaseModel):
    limit: int = Query(10, ge=1, le=30)
    offset: int = Query(0)
    search_text: str | None = Query(None)


class AdminGetUserGroupsListParams(BaseModel):
    limit: int = Query(10, ge=1, le=30)
    offset: int = Query(0)
    search_text: str | None = Query(None)


class AdminGetUsersListToAddParams(AdminGetUsersListParams):
    search_text: str = Query(min_length=1)


class AdminUserForAddToProfileSchema(BaseUserSchema):
    is_in_profile: bool = False
    scope_names: list[str] | None = None
