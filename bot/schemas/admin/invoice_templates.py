import enum
from dataclasses import dataclass
from decimal import Decimal
from typing import Annotated, Literal, Optional

from fastapi import Query
from pydantic import BaseModel, Field

from .payments import AdminInvoicePaymentInvoiceTemplate
from ..invoice.invoice import InvoiceTemplatePluginSchema


class AdminInvoiceTemplateListSchema(BaseModel):
    id: int
    title: str
    photo_url: str | None = None


class InvoiceTemplatePaymentModeEnum(str, enum.Enum):
    ENTERED_AMOUNT = "entered_amount"
    ITEMS = "items"


InvoiceTemplateLabelCommentMode = Literal["disabled", "optional", "required"]


class AdminCreateInvoiceTemplateItemSchema(BaseModel):
    name: str
    price: float
    quantity: int
    category: str | None = None
    item_code: str | None = None


class AdminCreateInvoiceTemplate(BaseModel):
    title: str = Field(
        description="Title of the invoice template"
    )
    description: Optional[str] = Field(
        default=None,
        description="Optional description of the invoice template"
    )
    currency: str = Field(
        description="Currency code for the invoice"
    )
    payment_mode: Optional[InvoiceTemplatePaymentModeEnum] = Field(
        default=None,
        description="Payment mode for the invoice"
    )
    comment_mode: InvoiceTemplateLabelCommentMode = Field(
        default="optional",
        description="Mode for comment handling on the invoice"
    )
    comment_label_raw: Optional[str] = Field(
        default=None,
        description="Raw label for comments"
    )
    need_name: bool = Field(
        default=True,
        description="Whether customer name is required"
    )
    need_phone_number: bool = Field(
        default=True,
        description="Whether phone number is required"
    )
    need_email: bool = Field(
        default=True,
        description="Whether email is required"
    )
    media_id: Optional[int] = Field(
        default=None,
        description="ID of associated media"
    )
    items: Optional[list[AdminCreateInvoiceTemplateItemSchema]] = Field(
        default=None,
        description="List of invoice template items"
    )
    payment_data: Optional[list[AdminInvoicePaymentInvoiceTemplate]] = Field(
        default=None,
        description="Payment data for the invoice template"
    )
    disabled_qty: Optional[bool] = Field(
        default=None,
        description="Whether quantity is disabled"
    )
    disabled_loyalty: Optional[bool] = Field(
        default=None,
        description="Whether loyalty features are disabled"
    )
    client_web_pages: list[int] | None = Field(
        None, nullable=True,
        description="List of client web pages ids to be associated with the store."
    )
    incust_terminal_api_key: Optional[str] = Field(
        default=None,
        description="Incust terminal API key"
    )
    incust_terminal_id: Optional[str] = Field(
        default=None,
        description="Incust terminal ID"
    )
    plugins: list[InvoiceTemplatePluginSchema] | None = Field(
        None, nullable=True
    )

    min_amount: Annotated[float | None, Field(nullable=True)] = None
    max_amount: Annotated[float | None, Field(nullable=True)] = None
    product_code: Optional[str] = Field(
        default=None,
        description="Optional product code for the invoice template"
    )

    need_auth: bool = False


class AdminUpdateInvoiceTemplateSchema(BaseModel):
    title: str = Field(
        description="Title of the invoice template"
    )
    description: Optional[str] = Field(
        default=None,
        description="Optional description of the invoice template"
    )
    currency: str = Field(
        description="Currency code for the invoice"
    )
    payment_mode: Optional[InvoiceTemplatePaymentModeEnum] = Field(
        default=None,
        description="Payment mode for the invoice"
    )
    comment_mode: Optional[InvoiceTemplateLabelCommentMode] = Field(
        default=None,
        description="Mode for comment handling on the invoice"
    )
    comment_label_raw: Optional[str] = Field(
        default=None,
        description="Raw label for comments"
    )
    need_name: Optional[bool] = Field(
        default=None,
        description="Whether customer name is required"
    )
    need_phone_number: Optional[bool] = Field(
        default=None,
        description="Whether phone number is required"
    )
    need_email: Optional[bool] = Field(
        default=None,
        description="Whether email is required"
    )
    media_id: Optional[int] = Field(
        default=None,
        description="ID of associated media"
    )
    disabled_qty: Optional[bool] = Field(
        default=None,
        description="Whether quantity is disabled"
    )
    disabled_loyalty: Optional[bool] = Field(
        default=None,
        description="Whether loyalty features are disabled"
    )
    payment_data: Optional[list[AdminInvoicePaymentInvoiceTemplate]] = Field(
        default=None,
        description="Payment data for the invoice template"
    )
    client_web_pages: list[int] | None = Field(
        None, nullable=True,
        description="List of client web pages ids to be associated with the store."
    )
    incust_terminal_api_key: Optional[str] = Field(
        default=None,
        description="Incust terminal API key"
    )
    incust_terminal_id: Optional[str] = Field(
        default=None,
        description="Incust terminal ID"
    )
    plugins: list[InvoiceTemplatePluginSchema] | None = Field(
        None, nullable=True
    )
    min_amount: Annotated[float | None, Field(nullable=True)] = None
    max_amount: Annotated[float | None, Field(nullable=True)] = None
    product_code: Optional[str] = Field(
        default=None,
        description="Optional product code for the invoice template"
    )

    max_bonuses_percent: Annotated[Decimal | None, Field(nullable=True)] = None

    need_auth: bool | None = None


class AdminUpdateInvoiceTemplateItemSchema(BaseModel):
    name: str
    price: float
    quantity: int
    category: str | None = None
    item_code: str | None = None


class AdminInvoiceTemplateResponse(BaseModel):
    id: int
    title: str
    description: str | None
    currency: str
    group_id: int
    payment_mode: Optional[InvoiceTemplatePaymentModeEnum]
    comment_mode: str
    comment_label_raw: Optional[str]
    need_name: bool
    need_phone_number: bool
    need_email: bool
    photo_url: str | None = None
    disabled_qty: bool
    disabled_loyalty: bool
    task_id: int | None = None
    incust_terminal_api_key: str | None = None
    incust_terminal_id: str | None = None
    plugins: list[InvoiceTemplatePluginSchema | None] | None = None

    min_amount: Annotated[float | None, Field(nullable=True)] = None
    max_amount: Annotated[float | None, Field(nullable=True)] = None
    product_code: str | None = None

    max_bonuses_percent: Annotated[Decimal | None, Field(nullable=True)] = None

    need_auth: bool


class AdminInvoiceTemplateItemSchema(BaseModel):
    id: int
    name: str
    price: float
    quantity: int
    category: str
    item_code: str | None = None
    invoice_template_id: int


@dataclass
class AdminInvoiceTemplateListParams(BaseModel):
    search_text: str | None = Query(None, nullable=True)
    offset: int | None = Query(None, nullable=True)
    limit: int = Query(10, gt=0)
