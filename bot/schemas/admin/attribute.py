from pydantic import BaseModel, Field

from core.ext.types import ExternalTypeLiteral
from ..base import BaseORMModel


class AdminAttributeListSchema(BaseORMModel):
    id: int
    attribute_id: str | None = Field(None, nullable=True)
    name: str
    read_allowed: bool = False
    edit_allowed: bool = False
    is_available: bool
    selected_by_default: bool = Field(default=False)
    price_impact: float | int
    attribute_group_id: int


class AdminAttributeTranslationSchema(BaseORMModel):
    name: str | None = Field(None, nullable=True)


class AdminAttributeSchema(AdminAttributeListSchema):
    min: int | None = Field(None, nullable=True)
    max: int | None = Field(None, nullable=True)
    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    translations: dict[str, AdminAttributeTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminCreateAttributeData(BaseModel):
    attribute_id: str | None = Field(None, nullable=True)
    name: str
    min: int | None = Field(None, nullable=True)
    max: int | None = Field(None, nullable=True)
    is_available: bool = True
    selected_by_default: bool = Field(default=False)
    price_impact: float | int
    attribute_group_id: int

    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    translations: dict[str, AdminAttributeTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminUpdateAttributeData(BaseModel):
    attribute_id: str | None = Field(None, nullable=True)
    name: str | None = None
    min: int | None = Field(None, nullable=True)
    max: int | None = Field(None, nullable=True)
    is_available: bool
    selected_by_default: bool = Field(default=False)
    price_impact: float | int
    attribute_group_id: int

    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    translations: dict[str, AdminAttributeTranslationSchema | None] | None = Field(None, nullable=True)
