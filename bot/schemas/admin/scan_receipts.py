from pydantic import BaseModel, Field

from schemas.store.brand import ScanReceiptCountry


class AdminScanReceiptsSettings(BaseModel):
    scan_receipts_enabled: bool | None = Field(None, description="Whether or not to enable scan receipts")
    scan_receipts_country: ScanReceiptCountry | None = Field(None, description="The country to scan receipts")
    scan_receipts_demo_mode: bool | None = Field(False, description="Whether or not to enable demo mode")
    scan_receipts_bin_codes: str | None = Field(None, description="Binary codes to scan, separated by comma")
    scan_receipts_enabled_all_rules: bool | None = Field(False, description="Enable loyalty rules for all transaction")


class AdminScanReceiptsValidateSchema(BaseModel):
    result: str | None = Field(None, description="descript errors validate incust settings")


class ReceiptCountry(BaseModel):
    code: str
    name: str
