from typing import List

from pydantic import BaseModel, Field, validator

from core.ext.types import ExternalTypeLiteral
from .product import AdminProductForProductGroupSchema
from ..base import BaseORMModel


class AdminCharacteristicTranslationSchema(BaseORMModel):
    name: str | None = Field(None, nullable=True)


class AdminCharacteristicValueTranslationSchema(BaseORMModel):
    value: str | None = Field(None, nullable=True)


class AdminProductGroupModifierSchema(BaseModel):
    id: int
    product_group_id: int
    name: str
    is_hide: bool
    show_one_modification: bool = False

    read_allowed: bool = False
    edit_allowed: bool = False


class AdminProductGroupOneModifierSchema(AdminProductGroupModifierSchema):
    translations: dict[str, AdminCharacteristicTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminProductGroupUpdateModifierSchema(BaseModel):
    name: str
    show_one_modification: bool = False


class AdminProductsGroupCreateSchema(BaseModel):
    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)
    name: str | None = None

    @validator('external_type', pre=True, always=True)
    def check_empty_string(cls, v):
        if v == '':
            return None
        return v


class AdminProductGroupBaseSchema(AdminProductsGroupCreateSchema):
    id: int

    read_allowed: bool = False
    edit_allowed: bool = False


class AdminProductGroupOneSchema(AdminProductGroupBaseSchema):
    products: List[AdminProductForProductGroupSchema] = Field(
        default_factory=list, description="List of products for product group"
    )


class AdminProductsGroupUpdateProductsModifierSchema(BaseModel):
    product_id: int
    modifier_id: int
    modifier_value: str | None = None
    translations: dict[str, AdminCharacteristicValueTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )
