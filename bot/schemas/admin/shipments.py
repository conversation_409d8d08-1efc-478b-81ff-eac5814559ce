from typing import Literal
from pydantic import BaseModel, Field
from datetime import timedelta

from ..store.shipping import (
    ShipmentPrice, DeliveryTimeTypeLiteral, NotWorkingHoursLiteral,
    ShipmentZone,
)

from .payments import ShipmentToPaymentSchema

AdminShipmentSettingsType = Literal["shipment", "custom_shipment", "custom_shipment_group"]
AdminCreateShipmentTypeLiteral = Literal["custom_shipment", "custom_shipment_group"]
AdminShipmentTypeLiteral = Literal["delivery", "pickup", "in_store", "no_delivery"]
AdminCustomShipmentBaseTypeLiteral = Literal["delivery", "pickup"]


class AdminShipmentTranslationSchema(BaseModel):
    name: str | None = Field(None, nullable=True)
    description: str | None = Field(None, nullable=True)
    label_comment: str | None = Field(None, nullable=True)
    post_payment_info: str | None = Field(None, nullable=True)


class AdminUpdateShipmentBaseSchema(BaseModel):
    is_paid_separately: bool = False
    allow_online_payment: bool = True
    allow_cash_payment: bool = True
    delivery_datetime_mode: DeliveryTimeTypeLiteral = "datetime"
    not_working_hours: NotWorkingHoursLiteral = "nothing"
    enabled_tips: bool = False
    min_time: timedelta | None = None
    max_time: timedelta | None = None
    execution_time: timedelta | None = None
    is_enabled: bool

    enabled_any_address_from_map: bool = False
    map_countries: list[str] | None = None

    payment_to_shipments: list[ShipmentToPaymentSchema] | None = None

    description: str | None = None
    translations: dict[str, AdminShipmentTranslationSchema | None] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations to be updated."
            "The key is language ISO code and "
            "the value is fields translations object."
            "(specify null as value to delete translation or "
            "null as field value to delete field translation,"
            "if auto translations enabled will be automatically translated after deleting)"
        )
    )


class AdminUpdateBaseShipmentSchema(AdminUpdateShipmentBaseSchema):
    ...


class AdminUpdateCustomShipmentSchema(AdminUpdateShipmentBaseSchema):
    name: str
    need_comment: bool = False
    label_comment: str | None = None
    need_address: bool = True
    base_type: AdminCustomShipmentBaseTypeLiteral | None = None


class AdminUpdateCustomShipmentGroupSchema(BaseModel):
    name: str
    description: str | None = None
    is_enabled: bool = True
    payment_to_shipments: list[ShipmentToPaymentSchema] | None = None
    translations: dict[str, AdminShipmentTranslationSchema | None] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations to be updated."
            "The key is language ISO code and "
            "the value is fields translations object."
            "(specify null as value to delete translation or "
            "null as field value to delete field translation,"
            "if auto translations enabled will be automatically translated after deleting)"
        )
    )


class AdminShipmentStore(BaseModel):
    store_id: int
    is_enabled: bool | None = None


class AdminUpdateShipmentPaymentsCustomPayment(BaseModel):
    id: int
    is_enabled: bool


class AdminUpdateShipmentPaymentSetting(BaseModel):
    id: int
    is_enabled: bool


class AdminUpdateShipmentPayments(BaseModel):
    shipment_payments_settings: list[AdminUpdateShipmentPaymentSetting]


class AdminShipmentCustomPaymentSchema(BaseModel):
    id: int
    name: str | None = None
    payment_method: str
    is_enabled: bool


class AdminShipmentsListSchema(BaseModel):
    id: int
    name: str
    is_enabled: bool
    is_disabled_in_any_store: bool
    is_enabled_in_any_store: bool
    type: AdminShipmentSettingsType
    base_type: AdminShipmentTypeLiteral | None = None
    icon_url: str | None = None


class AdminShipmentItemSchema(BaseModel):
    id: int
    is_disabled_in_any_store: bool
    is_enabled_in_any_store: bool
    type: AdminShipmentSettingsType

    name: str
    map_countries: list[str] | None = None
    base_type: AdminShipmentTypeLiteral | None
    prices: list[ShipmentPrice]
    enabled_any_address_from_map: bool = False

    is_paid_separately: bool = False
    allow_online_payment: bool = True
    allow_cash_payment: bool = True
    delivery_datetime_mode: DeliveryTimeTypeLiteral = "datetime"
    not_working_hours: NotWorkingHoursLiteral = "nothing"
    enabled_tips: bool = False
    min_time: timedelta | None = None
    max_time: timedelta | None = None
    execution_time: timedelta | None = None
    is_enabled: bool

    description: str | None = None
    icon_url: str | None = None

    need_comment: bool = False
    label_comment: str | None = None
    need_address: bool = True
    base_type: AdminShipmentTypeLiteral | None = None

    enabled_store_ids: list[AdminShipmentStore]
    custom_payments: list[AdminShipmentCustomPaymentSchema]

    translations: dict[str, AdminShipmentTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminCreateShipmentData(BaseModel):
    shipment_type: AdminCreateShipmentTypeLiteral
    is_paid_separately: bool = False
    enabled_tips: bool = False
    description: str | None = Field(default=None, nullable=True)
    name: str
    base_type: AdminCustomShipmentBaseTypeLiteral | None = Field(default=None, nullable=True)
    shipment_group_id: int | None = Field(default=None, nullable=True)
    is_enabled: bool = True
    payment_to_shipments: list[ShipmentToPaymentSchema] | None = None


class AdminUpdateShipmentPrices(BaseModel):
    prices: list[ShipmentPrice]
    zone_id: int | None = Field(default=None, nullable=True)


class AdminCreateOrUpdateShipmentZone(BaseModel):
    name: str
    distance: int | None = Field(default=None, nullable=True)
    polygon: list[list[float]] | None = Field(default=None, nullable=True)
    prices: list[ShipmentPrice] = Field(
        description="Prices with id=0 will be created, "
                    "prices not in list will be deleted, prices with id will be updated."
    )


class AdminShipmentZones(BaseModel):
    zones: list[ShipmentZone]
    is_enabled: bool
