from pydantic import BaseModel, Field
from dataclasses import dataclass

from fastapi import Form, UploadFile, File

from ..base import BaseORMModel


class AdminStoreBannerBaseSchema(BaseORMModel):
    name: str | None = Field(None, nullable=True)
    url: str | None = Field(None, nullable=True)
    position: int | None = Field(None, nullable=True)
    is_visible: bool | None = Field(None)


class AdminStoreBannerSchema(AdminStoreBannerBaseSchema):
    id: int

    image_url: str | None = Field(None, nullable=True)
    task_id: int | None = Field(None, nullable=True)


class AdminStoreBannersUpdatePositionsSchema(BaseModel):
    banners_ids: list[int]


@dataclass
class AdminStoreCreateBanner:
    name: str | None = Form(None, nullable=True)
    url: str | None = Form(None, nullable=True)
    position: int | None = Form(None)
    is_visible: bool | None = Form(None)
    file: UploadFile = File()


@dataclass
class AdminStoreUpdateBanner:
    name: str | None = Form(None, nullable=True)
    url: str | None = Form(None, nullable=True)
    position: int | None = Form(None)
    is_visible: bool | None = Form(None)
    file: UploadFile | None = File(None)
