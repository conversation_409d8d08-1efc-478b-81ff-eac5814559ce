from typing import Any, Dict, List, Optional

from pydantic import BaseModel

from ..base import EnumWithValues


class JournalSettingTypeEnum(EnumWithValues):
    ORDERS_JOURNAL = "orders_journal"
    ORDERS_BY_POSITIONS = "orders_by_positions"
    MAILING = "mailing"
    TRANSACTIONS = "transactions"
    TICKETS = "tickets"


class AdminJournalSettingBaseSchema(BaseModel):
    group_id: int
    user_id: int


class AdminJournalSettingGroupSchema(BaseModel):
    id: str
    columns: List[str]


class AdminJournalColumnGroupSizingItem(BaseModel):
    id: str
    width: int


class AdminJournalTableSettings(BaseModel):
    column_sizing: Optional[Dict[str, Any]] = None
    column_groups_sizing: Optional[List[AdminJournalColumnGroupSizingItem]] = None


class AdminJournalSettingCreateSchema(BaseModel):
    name: str
    type: JournalSettingTypeEnum
    settings: List[AdminJournalSettingGroupSchema]
    table_settings: Optional[AdminJournalTableSettings] = None


class AdminJournalSettingUpdateSchema(BaseModel):
    name: str | None = None
    settings: List[AdminJournalSettingGroupSchema] | None = None
    table_settings: Optional[AdminJournalTableSettings] = None


class AdminJournalSettingOneSchema(
    AdminJournalSettingBaseSchema, AdminJournalSettingCreateSchema
):
    id: int


class AdminJournalSettingListSchema(AdminJournalSettingOneSchema):
    pass


class AdminJournalSettingMaxCountNameSchema(BaseModel):
    type: JournalSettingTypeEnum
