from typing import Annotated

from aiowhatsapp.schemas import (
    CreateMessageTemplate, MessageTemplateCategoryType,
    MessageTemplateComponent,
)
from fastapi import Form, UploadFile
from pydantic import BaseModel, Field


class AdminWaTemplateBase(BaseModel):
    name: str
    description: str | None = Field(None, nullable=True)
    category: MessageTemplateCategoryType


class AdminWaMasterListTemplate(AdminWaTemplateBase):
    id: int


class CRMWaMasterListTemplate(AdminWaMasterListTemplate):
    is_active: bool
    is_pending: bool


class CRMReplyMessageSuggestion(BaseModel):
    id: int
    message: str


class MessageTemplateComponentSchema(MessageTemplateComponent):
    media: Annotated[UploadFile | str, None] = Form(None, nullable=True)


class AdminCreateMessageTemplate(CreateMessageTemplate):
    components: list[MessageTemplateComponentSchema]


class AdminWATemplate(BaseModel):
    id: int
    template_id: str
    template_status: str
    template_status_reason: str | None = Field(None, nullable=True)
    template_obj: AdminCreateMessageTemplate
    template_obj_update: CreateMessageTemplate | None = Field(None, nullable=True)


class AdminWaMasterTemplate(AdminWaMasterListTemplate):
    templates: list[AdminWATemplate]


class CreateMessageTemplateSchema(BaseModel):
    name: str
    category: MessageTemplateCategoryType
    allow_category_change: bool | None = None
    language: str
    components: list[MessageTemplateComponentSchema]


class AdminCreateWaTemplate(BaseModel):
    data: Annotated[str, Form()]
    master_template_id: Annotated[int | None, Form()] = None
    lang: Annotated[str, Form()]
    name: Annotated[str, Form()]
    description: Annotated[str | None, Form()] = None
    category: Annotated[MessageTemplateCategoryType, Form()]


class AdminCreateWaTemplateSchema(AdminCreateWaTemplate):
    data: Annotated[CreateMessageTemplateSchema, Form()]


class EditWaMasterTemplate(BaseModel):
    description: str | None = Field(None, nullable=True)
    master_template_id: int


class EditWaTemplate(BaseModel):
    template_id: str | None = None
    language: str
    master_template_id: int
    components: list[MessageTemplateComponentSchema]


class EditWaTemplateSchema(BaseModel):
    master: EditWaMasterTemplate
    template: EditWaTemplate
