from pydantic import BaseModel, Field

from schemas.store.brand import OrderFieldSettingEnum, OrderNameSettingEnum


class AdminOrderFieldsSchema(BaseModel):
    web_email_mode: OrderFieldSettingEnum | None = Field(None, description="Web email mode")
    web_phone_mode: OrderFieldSettingEnum | None = Field(None, description="Web phone mode")
    web_order_name_mode: OrderNameSettingEnum | None = Field(None, description="Web order name mode")
    messanger_email_mode: OrderFieldSettingEnum | None = Field(None, description="Messenger email mode")
    messanger_phone_mode: OrderFieldSettingEnum | None = Field(None, description="Messenger phone mode")
    messanger_order_name_mode: OrderNameSettingEnum | None = Field(None, description="Messenger order name mode")
    order_comment_mode: OrderFieldSettingEnum | None = Field(None, description="Order comment mode")
