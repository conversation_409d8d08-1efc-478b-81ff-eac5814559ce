from pydantic import BaseModel, Field

from core.ext.types import ExternalTypeLiteral
from .base import AdminConnectedObjectSchema
from ..base import BaseORMModel
from ..store.characteristic import CharacteristicFilterType


class AdminCategoryListSchema(BaseORMModel):
    id: int
    name: str
    profile_id: int

    father_category_id: int | None = Field(None, nullable=True)
    father_category_name: str | None = Field(None, nullable=True)

    image_url: str | None = Field(None, nullable=True)

    position: int | None = Field(None, nullable=True)

    read_allowed: bool = False
    edit_allowed: bool = False


class AdminTreeCategorySchema(AdminCategoryListSchema):
    children: list["AdminTreeCategorySchema"] = Field(default_factory=list)


AdminTreeCategorySchema.update_forward_refs()


class AdminCategoryFilterSchema(BaseORMModel):
    id: int = Field(description="characteristic id")
    name: str
    filter_type: CharacteristicFilterType


class AdminCategoryTranslationSchema(BaseORMModel):
    name: str | None = Field(None, nullable=True)


class AdminCategorySchema(AdminCategoryListSchema):
    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    filters: list[AdminCategoryFilterSchema]

    stores: list[AdminConnectedObjectSchema]

    has_child_categories: bool = False

    translations: dict[str, AdminCategoryTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminCreateCategoryData(BaseModel):
    name: str
    father_category_id: int | None = Field(
        default=None,
        nullable=True,
        description="Id of desired father category. Access to father_category is not "
                    "required",
    )
    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    position: int | None = Field(None, nullable=True)

    stores: list[int] | None = Field(
        default=None,
        nullable=True,
        description="list of stores ids to connect category. Access to store:edit "
                    "action is required"
    )

    filters: list[int] | None = Field(
        min_items=1,
        default=None,
        nullable=True,
        description="List of characteristics for filters in category"
    )

    translations: dict[str, dict | None] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminUpdateCategoryData(BaseModel):
    name: str | None = None
    father_category_id: int | None = Field(
        default=None,
        nullable=True,
        description="Id of desired father category. Access to father_category is not "
                    "required"
    )
    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    position: int | None = Field(None, nullable=True)

    translations: dict[str, AdminCategoryTranslationSchema | None] | None = Field(
        None, nullable=True
    )


class AdminConnectCategoriesData(BaseModel):
    categories: list[int] = Field(
        min_items=1,
        description="List of category ids to connect. Access to category:read action "
                    "is required",
    )
    replace: bool = Field(
        default=False,
        description="Is new list replaces current list"
    )


class AdminCategoriesConnectedResult(BaseModel):
    replaced: bool = False
    connected_categories: list[AdminCategoryListSchema]


class AdminDisconnectCategoriesData(BaseModel):
    categories: list[int] | None = Field(
        default=None,
        nullable=True,
        min_items=1,
        description=(
            "List of category ids to disconnect. "
            "Ignored if disconnect_all is specified"
        ),
    )
    disconnect_all: bool = False


class AdminCategoriesDisconnectedResult(BaseModel):
    is_all_deleted: bool = False
    categories_ids_disconnected: list[int] | None = Field(None, nullable=True)


class AdminConnectStoresToCategoryData(BaseModel):
    stores: list[int] = Field(
        description=(
            "List of store ids to add to category. "
            "Access to store:edit action is required."
        )
    )
    replace: bool = Field(
        default=False,
        description=(
            "Is new list replaces current list. "
            "Only stores with store:edit access will be replaced. "
        )
    )


class AdminCategoriesActionPayloadSchema(BaseModel):
    categories: list[int] = Field(
        min_items=1,
        description="List of category ids to perform action"
    )
