from pydantic import BaseModel, Field

from ..base import BaseORMModel


class AdminExtraFeeListSchema(BaseORMModel):
    id: int
    name: str
    read_allowed: bool = False
    edit_allowed: bool = False
    is_active: bool | None = Field(None, alias='is_active')
    extra_fee_percent: str | None = Field(default=None)
    extra_fee_value: str | None = Field(default=None)
    position: int


class AdminExtraFeeSchema(AdminExtraFeeListSchema):
    ...


class AdminCreateExtraFeeData(BaseModel):
    name: str
    extra_fee_percent: str | None = Field(default=None)
    extra_fee_value: str | None = Field(default=None)


class AdminUpdateExtraFeeData(BaseModel):
    name: str
    extra_fee_percent: str | None = Field(default=None)
    extra_fee_value: str | None = Field(default=None)
    position: int
    is_active: bool | None = Field(default=True)


class AdminExtraFeeValidateSchema(BaseModel):
    result: str | None = Field(None, description="descript errors validate bot")
