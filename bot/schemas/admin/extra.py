from pydantic import BaseModel, Field


class CardNumbersInputData(BaseModel):
    count: int = Field(description="Required number of generated card numbers")
    prefix: str | None = Field(None, description="Prefix before the card number")
    add_file: bool = Field(False, description="Do I need to add a file in base64 string")


class CardNumbersOutputData(BaseModel):
    card_numbers: list[str] = Field(description="list of generated card numbers")
    file_data: str | None = Field(None, description="Text file with a generated list of card numbers in base64 string")
    file_name: str | None = Field(None, description="Name of the file with the generated list of card numbers")
