from typing import Literal

from pydantic import BaseModel, Field

IncustTypeClientAuth = Literal["web", "bot"]

IncustRedeemType = Literal["pre_auth", "reserve"]

IncustLoyaltyApplicableType = Literal["for_all", "for_participants"]


class AdminIncustStoreSettingsSchema(BaseModel):
    store_id: int | None = Field(None, description="Store id")
    terminal_api_key: str | None = Field(
        None, description="Specific terminal api key for store"
    )
    terminal_id: str | None = Field(
        None, description="Specific terminal id for store"
    )


class AdminIncustSettingsSchema(BaseModel):
    incust_loyalty_id: str | None = Field(None, description="Incust loyalty id")
    incust_white_label_id: str | None = Field(None, description="Incust white label id")
    incust_term_api: str | None = Field(None, description="Incust term api")
    incust_server_api: str | None = Field(None, description="Incust server api")
    incust_client_url: str | None = Field(None, description="Incust client url")
    incust_type_client_auth: bool | None = Field(
        None, description="Incust type client auth"
    )  # Updated to bool
    incust_terminal_id: str | None = Field(None, description="Incust terminal id")
    incust_redeem_type: IncustRedeemType | None = Field(
        None, description="Incust redeem type"
    )
    incust_prohibit_redeeming_bonuses: bool | None = Field(
        False, description="Incust prohibit redeeming bonuses"
    )
    incust_prohibit_redeeming_coupons: bool | None = Field(
        False, description="Incust prohibit redeeming coupons"
    )
    incust_loyalty_applicable_type: bool | None = Field(
        None, description="Incust loyalty applicable type"
    )  # Updated to bool
    stores_settings: list[AdminIncustStoreSettingsSchema] | None = Field(
        None, description="Incust store settings"
    )


class AdminIncustValidateSchema(BaseModel):
    result: str | None = Field(
        None, description="descript errors validate incust settings"
    )
