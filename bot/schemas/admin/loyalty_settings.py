from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field, validator

from schemas import (
    LoyaltySettingsApplicableTypes, LoyaltySettingsObjectType,
    LoyaltySettingsTypeClientAuth,
)


class LoyaltySettingsConfigSchema(BaseModel):
    """Схема для конфігураційних налаштувань лояльності"""
    incust_server_api: str = Field(..., description="Default InCust server API URL")


class LoyaltySettingsCreateSchema(BaseModel):
    """Схема для створення налаштувань лояльності"""
    
    # Тип та ID об'єкта для прив'язки
    object_type: LoyaltySettingsObjectType = Field(..., description="Type of object to attach settings to")
    object_id: int = Field(..., description="ID of the object")
    
    # Основні налаштування InCust (terminal_id та loyalty_id отримуються автоматично з API)
    white_label_id: str = Field(..., description="InCust white label ID")
    terminal_api_key: str = Field(..., description="InCust terminal API key")
    server_url: str = Field(..., description="InCust server URL")
    
    # Налаштування клієнта
    type_client_auth: LoyaltySettingsTypeClientAuth | None = Field(None, description="Type of client auth")
    
    # Обмеження
    prohibit_redeeming_bonuses: bool = Field(False, description="Prohibit redeeming bonuses")
    prohibit_redeeming_coupons: bool = Field(False, description="Prohibit redeeming coupons")
    
    # Налаштування застосування
    loyalty_applicable_type: LoyaltySettingsApplicableTypes = Field(
        LoyaltySettingsApplicableTypes.FOR_PARTICIPANTS,
        description="Loyalty applicable type"
    )
    
    # Додаткові поля
    priority: int = Field(0, description="Priority for settings selection")
    is_enabled: bool = Field(True, description="Is settings enabled")
    name: str | None = Field(None, description="Settings name for admin panel")
    description: str | None = Field(None, description="Settings description")
    
    @validator('object_type', 'object_id')
    def validate_object_fields(cls, v, values):
        """Перевірка що передано object_type та object_id"""
        if 'object_type' in values and values['object_type'] and 'object_id' in values and v:
            return v
        return v


class LoyaltySettingsUpdateSchema(BaseModel):
    """Схема для оновлення налаштувань лояльності"""
    
    # Основні налаштування InCust (при зміні terminal_id та loyalty_id оновлюються автоматично)
    white_label_id: str | None = Field(None, description="InCust white label ID")
    terminal_api_key: str | None = Field(None, description="InCust terminal API key")
    server_url: str | None = Field(None, description="InCust server URL")
    
    # Налаштування клієнта
    type_client_auth: LoyaltySettingsTypeClientAuth | None = Field(None, description="Type of client auth")
    
    # Обмеження
    prohibit_redeeming_bonuses: bool | None = Field(None, description="Prohibit redeeming bonuses")
    prohibit_redeeming_coupons: bool | None = Field(None, description="Prohibit redeeming coupons")
    
    # Налаштування застосування
    loyalty_applicable_type: LoyaltySettingsApplicableTypes | None = Field(
        None,
        description="Loyalty applicable type"
    )
    
    # Додаткові поля
    priority: int | None = Field(None, description="Priority for settings selection")
    is_enabled: bool | None = Field(None, description="Is settings enabled")
    name: str | None = Field(None, description="Settings name for admin panel")
    description: str | None = Field(None, description="Settings description")


class LoyaltySettingsResponseSchema(BaseModel):
    """Схема відповіді для налаштувань лояльності"""
    
    id: int
    
    # Прив'язка до об'єктів
    ewallet_id: int | None
    profile_id: int | None
    invoice_template_id: int | None
    store_id: int | None
    product_id: int | None
    
    # Основні налаштування InCust (terminal_id та loyalty_id заповнюються автоматично з API)
    white_label_id: str
    terminal_api_key: str
    terminal_id: str | None
    loyalty_id: str | None
    server_url: str
    
    # Налаштування клієнта
    type_client_auth: LoyaltySettingsTypeClientAuth | None
    
    # Обмеження
    prohibit_redeeming_bonuses: bool
    prohibit_redeeming_coupons: bool
    
    # Налаштування застосування
    loyalty_applicable_type: LoyaltySettingsApplicableTypes
    
    # Додаткові поля
    priority: int
    is_enabled: bool
    name: str | None
    description: str | None
    
    # Метадані
    created_at: datetime
    updated_at: datetime | None
    
    class Config:
        from_attributes = True


class LoyaltySettingsListSchema(BaseModel):
    """Схема для списку налаштувань лояльності"""
    
    items: list[LoyaltySettingsResponseSchema]
    
    
class LoyaltySettingsValidateSchema(BaseModel):
    """Схема для валідації налаштувань"""
    
    is_valid: bool
    errors: list[str] | None = None
    terminal_info: dict | None = None
    terminal_id: str | None = Field(None, description="Terminal ID from InCust API")
    loyalty_id: str | None = Field(None, description="Loyalty ID from InCust API")


class LoyaltySettingsByObjectSchema(BaseModel):
    """Схема для запиту налаштувань по типу та ID об'єкта"""
    
    object_type: LoyaltySettingsObjectType = Field(..., description="Type of object")
    object_id: int = Field(..., description="ID of the object")


class LoyaltySettingsCopySchema(BaseModel):
    """Схема для копіювання налаштувань"""
    
    source_id: UUID = Field(..., description="Source settings ID to copy from")
    
    # Цільовий об'єкт (один з них має бути заповнений)
    ewallet_id: int | None = Field(None, description="Target e-wallet ID")
    profile_id: int | None = Field(None, description="Target profile ID")
    invoice_template_id: int | None = Field(None, description="Target invoice template ID")
    store_id: int | None = Field(None, description="Target store ID")
    product_id: int | None = Field(None, description="Target product ID")
    
    # Опціональні поля для перевизначення
    name: str | None = Field(None, description="New settings name")
    description: str | None = Field(None, description="New settings description")
    priority: int | None = Field(None, description="New priority")
