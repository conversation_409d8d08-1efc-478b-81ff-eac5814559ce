from pydantic import BaseModel, Field

from core.ext.types import ExternalTypeLiteral
from ..base import BaseORMModel
from ..store.characteristic import CharacteristicFilterType


class AdminCharacteristicListSchema(BaseORMModel):
    id: int
    name: str

    position: int | None = Field(None, nullable=True)
    excel_row_number: int | None = Field(None, nullable=True)

    is_hide: bool | None = False
    filter_type: CharacteristicFilterType
    read_allowed: bool = False
    edit_allowed: bool = False


class AdminCharacteristicValueTranslationSchema(BaseModel):
    value: str | None = Field(None, nullable=True)


class AdminCharacteristicWithValueListSchema(AdminCharacteristicListSchema):
    value: str
    translations: dict[str, AdminCharacteristicValueTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminCharacteristicFilterSchema(BaseORMModel):
    id: int = Field(description="characteristic id")
    name: str
    filter_type: CharacteristicFilterType


class AdminCharacteristicTranslationSchema(BaseORMModel):
    name: str | None = Field(None, nullable=True)


class AdminCharacteristicSchema(AdminCharacteristicListSchema):
    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    filters: list[AdminCharacteristicFilterSchema]

    translations: dict[str, AdminCharacteristicTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminCreateCharacteristicData(BaseModel):
    name: str

    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    position: int | None = Field(None, nullable=True)
    excel_row_number: int | None = Field(None, nullable=True)

    filters: list[int] | None = Field(
        min_items=1,
        default=None,
        nullable=True,
        description="List of characteristics for filters in category"
    )

    translations: dict[str, AdminCharacteristicTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminUpdateCharacteristicData(BaseModel):
    name: str | None = None

    external_id: str | None = Field(None, nullable=True)
    external_type: ExternalTypeLiteral | None = Field(None, nullable=True)

    position: int | None = Field(None, nullable=True)
    excel_row_number: int | None = Field(None, nullable=True)

    is_hide: bool | None = False
    filter_type: CharacteristicFilterType | None

    translations: dict[str, AdminCharacteristicTranslationSchema | None] | None = Field(
        None, nullable=True
    )


class AdminCharacteristicValueSchema(BaseORMModel):
    id: int
    value: str
    translations: dict[str, AdminCharacteristicValueTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )
