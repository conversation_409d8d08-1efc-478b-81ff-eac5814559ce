from typing import Annotated

from fastapi import Form, UploadFile
from pydantic import BaseModel, Field

from ..base import BaseORMModel
from ..group.color_schema import (
    AppearanceColorSchema, AppearanceColorSchemaFormData,
    ThumbnailsModeEnum,
)
from ..group.group import GroupConfig
from ..store.brand import (
    BaseAuthSettings, ConsentModeEnum, MobileCartButtonMode, ViewType,
)


class AdminProfileListSchema(BaseORMModel):
    id: int
    name: str
    domain: str | None = Field(None, nullable=True)
    logo_url: str | None = Field(None, nullable=True)


class AdminCreateProfileData(BaseORMModel):
    name: str
    country_code: str = Field(min_length=2, max_length=2)
    timezone: str = Field()
    lang: str = Field(min_length=2, max_length=2)
    is_accepted_agreement: bool
    currency: str


class AdminProfileSchema(AdminProfileListSchema):
    brand_id: int | None = None

    is_accepted_agreement: bool = False
    is_own: bool = False
    owner_id: int = Field(
        description=(
            "Profile owner user id"
            "Available only for profile owner and platform:admin users."
            "Otherwise 0 returned"
        )
    )

    is_onboard_guide_completed: bool = False

    country_code: str | None = Field(None, nullable=True)

    email: str | None = Field(None, nullable=True)
    phone: str | None = Field(None, nullable=True)

    timezone: str | None = Field(None, nullable=True)

    lang: str
    is_translate: bool
    allow_all_google_langs: bool
    all_langs_list: list[str] = Field(default_factory=list)
    additional_langs_list: list[str] = Field(default_factory=list)
    force_use_lang: str | None = Field(None, nullable=True)

    google_public_key: str | None = Field(None, nullable=True)

    config: GroupConfig | None = Field(None, nullable=True)

    currency: str | None = Field(None, nullable=True)
    is_incust: bool = False
    service_email: str | None = Field(None, nullable=True)

    image_task_id: int | None = Field(None, nullable=True)
    logo_task_id: int | None = Field(None, nullable=True)

    stripe_customer_id: str | None = Field(None, nullable=True)

    is_billing_tester: bool = False
    transactions_activated: bool = False


class AdminUpdateProfileData(BaseModel):
    name: str | None = None
    email: str | None = Field(None, nullable=True)
    phone: str | None = Field(None, nullable=True)
    timezone: str | None = None
    lang: str | None = Field(None, nullable=True)
    langs_list: list[str] = Field(default_factory=list)
    force_use_lang: str | None = None
    is_translate: bool | None = None
    allow_all_google_langs: bool | None = None
    currency: str | None = None
    is_onboard_guide_completed: bool | None = None


class AdminChangeStatusProfileData(BaseModel):
    password: str


class AdminTransferOwnershipData(AdminChangeStatusProfileData):
    new_owner_id: int


class AdminProfileCompletionSchema(BaseModel):
    has_profile_logo: bool
    has_profile_banner: bool
    has_own_domain: bool
    enable_translations: bool
    has_stores: bool
    has_categories: bool
    has_products: bool
    has_payment_methods: bool
    has_fastpay: bool
    has_delivery: bool


class UpdateAuthSettings(BaseAuthSettings):
    ...


class ProfileAnalyticsSettings(BaseModel):
    meta_pixel_id: str | None = None
    meta_pixel_access_token: str | None = None
    google_analytics_id: str | None = None
    google_tag_manager_id: str | None = None


class ProfileOtherSettings(BaseModel):
    is_friend_payment: bool | None = False
    api_token: str | None = None
    is_ai_enabled: bool | None = False
    additional_head_tags: str | None = None
    additional_body_tags: str | None = None
    is_ask_about_birthday: bool | None = False
    analytics_data: ProfileAnalyticsSettings | None = None


class ProfileBasicSettings(BaseModel):
    name: str
    domain: str | None = None
    timezone: str | None = None
    banner_url: str | None = None
    logo_url: str | None = None
    description: str | None = None
    offer: str | None = None
    country_code: Annotated[str | None, Field(nullable=True)] = None
    currency: str | None = None
    consent_mode: ConsentModeEnum | None = Field(
        None, description="Platform Consent Mode"
    )
    terms_of_use_link: str | None = None
    privacy_policy_link: str | None = None


class AdminProfileUpdateBasicSettings(BaseModel):
    domain: Annotated[str | None, Form()] = None
    name: Annotated[str | None, Form()] = None
    timezone: str | None = None
    logo_file: Annotated[UploadFile | str, None] = Form(None, nullable=True)
    clear_logo: Annotated[bool, Form()] = False
    banner_file: Annotated[UploadFile | str, None] = Form(None, nullable=True)
    description_file: Annotated[UploadFile | str, None] = Form(None, nullable=True)
    offer_file: Annotated[UploadFile | str, None] = Form(None, nullable=True)
    currency: str | None = None
    consent_mode: ConsentModeEnum | None = Form(
        None, description="Platform Consent Mode"
    )
    terms_of_use_link: str | None = None
    privacy_policy_link: str | None = None
    country_code: str | None = Form(None)


class ValidateDomainData(BaseModel):
    domain: str


class ValidatedBrandDomainSchema(BaseModel):
    domain: str
    is_external_domain: bool
    is_equal_current_domain: bool


class AppearanceSettingsBaseFormData(AppearanceColorSchemaFormData):
    product_image_aspect_ratio: Annotated[str | None, Form()] = None
    image_height: Annotated[int | None, Form()] = None
    desktop_view: Annotated[ViewType | None, Form()] = None
    mobile_view: Annotated[ViewType | None, Form()] = None
    is_categories_count_view: Annotated[
        bool | None, Form(description="categories count badges in frontend menu")] = \
        None
    mobile_cart_button_mode: Annotated[MobileCartButtonMode | None, Form()] = None
    is_filter_search: Annotated[
        bool | None, Form(description="show search input in frontend menu")] = None
    is_filter_sort: Annotated[
        bool | None, Form(description="show sort buttons in frontend menu")] = None
    is_filter_by_price: Annotated[
        bool | None, Form(description="show price filter in frontend menu")] = None
    logo_file: Annotated[UploadFile | str, None] = Form(None, nullable=True)
    clear_logo: Annotated[bool | None, Form()] = None
    banner_file: Annotated[UploadFile | str, None] = Form(None, nullable=True)
    show_more_infinite: Annotated[bool | None, Form()] = None
    products_limit: Annotated[int | None, Form()] = None
    thumbnails_mode: Annotated[ThumbnailsModeEnum | None, Form()] = None
    thumbnail_size: Annotated[int | None, Form()] = None


class AppearanceSettingsBase(AppearanceColorSchema):
    product_image_aspect_ratio: str | None = None
    image_height: int | None = None
    desktop_view: ViewType = "default_grid"
    mobile_view: ViewType = "default_grid"
    is_categories_count_view: bool = True
    mobile_cart_button_mode: MobileCartButtonMode = MobileCartButtonMode.BOTTOM
    is_filter_search: bool = True
    is_filter_sort: bool = True
    is_filter_by_price: bool = True
    banner_url: str | None = None
    logo_url: str | None = None
    show_more_infinite: bool
    products_limit: int
    thumbnails_mode: ThumbnailsModeEnum = ThumbnailsModeEnum.SEVEN_LOC
    thumbnail_size: int


class AppearanceSettings(AppearanceSettingsBase):
    product_image_aspect_ratio_converted: list[int | float] | None = Field(
        default=None,
        min_items=2,
        max_items=2,
    )


class UpdateAppearanceSettings(AppearanceSettingsBaseFormData):
    ...


class RevokeApiTokenResponse(BaseModel):
    api_token: str
