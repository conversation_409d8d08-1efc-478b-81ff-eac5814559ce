from datetime import datetime

from pydantic import BaseModel, Field


class DateRangeSchema(BaseModel):
    start_date: datetime = Field(..., description="Start date for the statistics")
    end_date: datetime = Field(..., description="End date for the statistics")


class AdminStatsTransactionsCountSchema(BaseModel):
    total_transactions: int | None = Field(None, description="Total number of transactions")


class AdminStatsOrderUsersCountSchema(BaseModel):
    order_users_count: int | None = Field(None, description="Total number of order users")


class AdminStatsTurnoverSchema(BaseModel):
    total_turnover: float | None = Field(None, description="Total turnover amount")


class AdminStatsReviewsCountSchema(BaseModel):
    total_reviews: int | None = Field(None, description="Total number of reviews")


class AdminStatsAverageReviewRatingSchema(BaseModel):
    average_rating: float | None = Field(None, description="Average review rating")
