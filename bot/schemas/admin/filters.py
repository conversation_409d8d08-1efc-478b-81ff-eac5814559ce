from enum import Enum

from pydantic import BaseModel

from ..store.characteristic import CharacteristicFilterType


class FilterListType(Enum):
    category = "category"
    store = "store"


class AdminFilterItemListSchema(BaseModel):
    id: int
    name: str


class AdminFilterListSchema(BaseModel):
    id: int
    name: str
    connected_filters: list[AdminFilterItemListSchema]
    children: list['AdminFilterListSchema'] | None = None


class AdminFilterItemSchema(BaseModel):
    id: int
    name: str | None = None
    filter_type: CharacteristicFilterType | None = None
    is_hide: bool = False


class AdminFilterSchema(BaseModel):
    id: int
    name: str
    connected_filters: list[AdminFilterItemSchema] | None = None


class AdminFilterUpdateSchema(BaseModel):
    type: FilterListType
    connected_filters: list[AdminFilterItemSchema] | None = None
