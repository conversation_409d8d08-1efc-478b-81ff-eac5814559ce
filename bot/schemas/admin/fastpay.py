from typing import Literal, Optional

from pydantic import BaseModel


class FastPayLinkParams(BaseModel):
    group_id: int
    lang: str
    mode: str
    invoice_template_id: Optional[int] = None
    entered_amount: Optional[float] = None
    bot_type: Optional[str] = None
    bot_id_name: Optional[str] = None
    nl_key: Optional[str] = None
    show_mode: Literal["web", "bot"]
    external_transaction_id: Optional[str] = None
    client_redirect_url: Optional[str] = None


class FastPayLinkResponse(BaseModel):
    link: str
