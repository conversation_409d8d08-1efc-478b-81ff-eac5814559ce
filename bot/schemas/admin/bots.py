from enum import Enum
from typing import Literal

from pydantic import BaseModel, Field

from schemas.bot import BotTypeLiteral

ReviewType = Literal["stars", "emojis", "numbers", "off"]


class AdminBotBaseSchema(BaseModel):
    bot_type: BotTypeLiteral = Field(description="bot type")
    token: str | None = Field(None, description="bot token", nullable=True)
    whatsapp_app_id: str | None = Field(
        None, description="WhatsApp application ID", nullable=True
    )
    whatsapp_from: str | None = Field(
        None, description="WhatsApp sender", nullable=True
    )
    whatsapp_app_secret: str | None = Field(
        None, description="WhatsApp application ID", nullable=True
    )
    whatsapp_business_account_id: str | None = Field(
        None, description="WhatsApp business ID", nullable=True
    )

    # class Config:
    #     schema_extra = {
    #         "required": ["bot_type", "token"]
    #     }


class AdminBotCreateSchema(AdminBotBaseSchema):
    ...


class AdminBotEditSchema(AdminBotCreateSchema):
    ...


class AdminBotGetSchema(AdminBotEditSchema):
    id: int | None = Field(None, description="bot ID")
    group_id: int | None = Field(None, description="group ID")
    username: str | None = Field(None, description="username")
    display_name: str | None = Field(None, description="display name")
    link: str = Field(description="link")
    is_started: bool = Field(description="is started")
    whatsapp_app_name: str | None = Field(
        None, description="WhatsApp application name", nullable=True
    )
    whatsapp_from_phone_number: str | None = Field(
        None, description="WhatsApp sender phone number"
    )
    whatsapp_business_account_id: str | None = Field(
        None, description="WhatsApp business account ID"
    )
    message: str | None = Field(None, description="result change bot type message")


class AdminBotResponse(BaseModel):
    bot: AdminBotGetSchema | None = Field(None, nullable=True)


class AdminBotSetBusinessAccountIdSchema(BaseModel):
    whatsapp_business_account_id: str | None = Field(
        None, description="WhatsApp business account ID"
    )


class AdminBotValidateSchema(BaseModel):
    result: str | None = Field(None, description="descript errors validate bot")


class AdminBotSettings(BaseModel):

    review_type: ReviewType | None = Field(None, description="review types")
    need_chat_header: bool | None = Field(False, description="need chat header")
    need_leave_chat_keyboard: bool | None = Field(
        False, description="need chat keyboard"
    )
    is_show_order_button: bool | None = Field(
        True, description="Need show order button (only for telegram bot)"
    )
    is_auto_answer: bool | None = Field(
        False, description="Need auto answer for user message"
    )
    auto_answer_delay: int | None = Field(
        5, description="Auto answer delay in minutes"
    )
    on_join_virtual_manager_id: int | None = Field(
        None, description="Virtual manager ID for join chat on command /start"
    )


class AdminBotResultSchema(BaseModel):
    status: str | None = Field(None, description="status result")
    detail: str | None = Field(None, description="detail result")


class AdminBotRegisterSchema(BaseModel):
    pin: str = Field(description="whatapp pin for register")


class AdminBotRegisterStatus(Enum):
    SUCCESS = "success"
    FAIL = "fail"


class AdminBotRegisterResultSchema(AdminBotResultSchema):
    status: AdminBotRegisterStatus
