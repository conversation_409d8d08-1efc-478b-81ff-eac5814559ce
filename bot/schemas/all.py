from enum import Enum
from datetime import datetime

from pydantic import BaseModel


class DBQueryOperation(Enum):
    ALL = "all"
    COUNT = "count"


class WebAppUser(BaseModel):
    id: int
    first_name: str
    last_name: str | None = None
    username: str | None = None
    language_code: str | None = None


class WebAppData(BaseModel):
    query_id: str
    user: WebAppUser
    auth_date: datetime


class OrderShippingStatusEnum(Enum):
    OPEN_UNCONFIRMED = "open_unconfirmed"
    OPEN_CONFIRMED = "open_confirmed"
    PAYED = "payed"
    CLOSED = "closed"
    CANCELED = "canceled"
    WAIT_FOR_SHIP = "wait_for_ship"
    SHIPPED = "shipped"
    IN_TRANSIT = "in_transit"
    DELIVERED = "delivered"
    NEW = "new"
    TERMINATED_BY_USER = "terminated_by_user"


class LoyaltyInfo(BaseModel):
    loyalty_type: str | None = None
    loyalty_enabled: bool = False
