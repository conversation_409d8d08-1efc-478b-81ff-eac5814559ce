import enum
from datetime import datetime

from .base import BaseORMModel


class SystemNotificationCategory(enum.Enum):
    PAYMENT = "payment"
    WEBHOOK = "webhook"
    TASK = "task"
    BILLING = "billing"
    GENERAL = "general"
    UTILITY = "utility"
    ORDER = "order"


class SystemNotificationType(enum.Enum):
    MAKE_PAYMENT_ERROR = "make_payment_error"
    EXTERNAL_PAYMENT_ERROR = "external_payment_error"
    EXTERNAL_PAYMENT_RECOVERED = "external_payment_recovered"
    INCUST_PAY_ERROR = "incust_pay_error"
    LIQPAY_ERROR = "liqpay_error"
    WEBHOOK_ERROR = "webhook_error"
    TASK_ERROR = "task_error"
    QUOTA_EXCEEDED = "quota_exceeded"
    SUBSCRIPTION_PAST_DUE = "subscription_past_due"
    SUBSCRIPTION_RECOVERED = "subscription_recovered"
    GENERAL_NOTIFICATION = "general_notification"
    TRANSACTION_CURRENCY_MISMATCH = "transaction_currency_mismatch"
    UTILITY_PAYMENT_ERROR = "utility_payment_error"
    CHANGE_STATUS = "change_status"


class NotificationLevel(enum.Enum):
    ERROR = "error"
    INFO = "info"
    WARNING = "warning"

class NotificationRecipientType(enum.Enum):
    ADMIN = "admin"
    USER = "user"


class AdminNotificationBaseSchema(BaseORMModel):
    category: SystemNotificationCategory
    type_notification: SystemNotificationType
    time_created: datetime
    title: str
    content: str
    is_read: bool
    level: NotificationLevel


class AdminNotificationSchema(AdminNotificationBaseSchema):
    id: int
