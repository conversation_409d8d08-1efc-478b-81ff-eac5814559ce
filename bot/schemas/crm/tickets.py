from datetime import datetime

from fastapi import Query
from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass

from .base import BaseCRMListParams
from ..base import BaseORMModel
from ..crm_ticket import CRMTicketStatusEnum, CRMTicketStatusInitiatedByEnum


class CRMTicketBaseSchema(BaseORMModel):
    id: int
    title: str
    crm_tag: str

    status: CRMTicketStatusEnum
    user_id: int | None = Field(None, nullable=True)

    change_date: datetime
    time_created: datetime


class CRMTicketListSchema(CRMTicketBaseSchema):
    profile_id: int
    business_name: str
    bot_name: str | None = Field(None, nullable=True)
    items_text: str = Field(description="for CRM app. It is just title")

    first_name: str | None = Field(None, nullable=True)
    last_name: str | None = Field(None, nullable=True)
    full_name: str | None = Field(None, nullable=True)
    email: str | None = Field(None, nullable=True)
    photo_url: str | None = Field(None, nullable=True)


class CRMTicketStatusHistoryObject(BaseORMModel):
    id: int
    status: CRMTicketStatusEnum
    set_datetime: datetime

    comment: str | None = Field(None, nullable=True)

    initiated_by: CRMTicketStatusInitiatedByEnum

    initiated_by_user_id: int | None = Field(None, nullable=True)
    initiated_by_user_name: str | None = Field(None, nullable=True)
    initiated_by_user_email: str | None = Field(None, nullable=True)


class CRMTicketSchema(CRMTicketListSchema):
    status_history: list[CRMTicketStatusHistoryObject]


class CRMChangeTicketStatusData(BaseModel):
    status: CRMTicketStatusEnum
    header: str | None = Field(None, nullable=True, max_length=124)
    message: str | None = Field(None, nullable=True, max_length=1024)
    internal_comment: str | None = Field(None, nullable=True, max_length=124)
    notify_user: bool = True


@dataclass
class CRMTicketListParams(BaseCRMListParams):
    ticket_titles: list[str] | None = Query(None, nullable=True)
