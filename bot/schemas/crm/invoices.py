from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional

from fastapi import Query
from incust_api.api import term
from incust_terminal_api_client import SpecialAccountCharge
from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass

from .base import BaseCRMListParams
from ..base import BaseORMModel
from ..invoice import (
    InvoiceItemSchema, InvoicePrintReceiptFormatEnum, InvoicePrintReceiptTypeEnum,
    InvoiceStatusLiteral, InvoiceTypeEnum, PrintReceiptDataTypeEnum,
)
from ..store.order import UTMLabelsSchema


@dataclass
class CRMInvoiceListParams(BaseCRMListParams):
    types: list[InvoiceTypeEnum] | None = Query(None, nullable=True)
    is_read: bool | None = Query(None, nullable=True)


class CRMInvoicePrintReceiptParams(BaseModel):
    format: InvoicePrintReceiptFormatEnum = Query(
        InvoicePrintReceiptFormatEnum.A4,
        description="Format of the receipt - A4, 58mm, 80mm. By default A4"
    )
    type: InvoicePrintReceiptTypeEnum = Query(
        InvoicePrintReceiptTypeEnum.pdf,
        description="Type of the receipt - pdf or html. By default pdf"
    )
    draw_checkboxes: bool = Query(
        False,
        description="Whether to draw checkboxes for items in the receipt. "
                    "If not provided, the default value is False."
    )
    data_type: PrintReceiptDataTypeEnum = Query(
        PrintReceiptDataTypeEnum.invoice,
        description="Type of data to print in the receipt - invoice or order. "
                    "By default invoice"
    )
    custom_width: int | None = Query(
        None,
        ge=30,
        le=210,
        description="Custom width for the receipt. If not provided, the default width "
                    "for the selected format will be used."
    )


class CRMInvoicePrintReceiptResponseSchema(BaseORMModel):
    type: InvoicePrintReceiptTypeEnum
    data: str


class BaseCRMInvoiceSchema(BaseORMModel):
    id: int
    currency: str
    crm_tag: str

    status: InvoiceStatusLiteral
    invoice_type: InvoiceTypeEnum

    user_id: int | None = Field(None, nullable=True)
    menu_in_store_id: int | None = Field()

    title: str

    first_name: str | None = Field(None, nullable=True)
    last_name: str | None = Field(None, nullable=True)
    full_name: str | None = Field(None, nullable=True)
    email: str | None = Field(None, nullable=True)
    phone: str | None = Field(None, nullable=True)

    time_created: datetime

    before_loyalty_sum: float
    discount: float
    bonuses_redeemed: float
    discount_and_bonuses_redeemed: float

    total_sum: float
    tips_sum: float
    sum_to_pay: float

    payer_fee: float
    paid_sum: float

    is_read: bool = False
    read_by_user_id: int | None = Field(None, nullable=True)

    utm_labels: UTMLabelsSchema | None = Field(
        None, nullable=True,
        description="UTM labels of the invoice, if any"
    )


class CRMInvoiceListSchema(BaseCRMInvoiceSchema):
    photo_url: str | None = Field(None, nullable=True)
    status_pay: InvoiceStatusLiteral

    change_date: datetime

    profile_id: int
    profile_name: str
    business_name: str
    bot_name: str | None = Field(None, nullable=True)

    menu_in_store_comment: str | None = Field(None, nullable=True)

    items_text: str

    comment: str | None = Field(None, nullable=True)


class CRMInvoiceSchemaORMAttributes(BaseCRMInvoiceSchema):
    shipment_cost: int = 0
    custom_payment_cost: int = 0

    paid_datetime: datetime | None = Field(None, nullable=True)


class CRMInvoiceSchema(CRMInvoiceSchemaORMAttributes, CRMInvoiceListSchema):
    description: str = ""

    creator_id: int | None = Field(None, nullable=True)
    payer_id: int | None = Field(None, nullable=True)
    bot_id: int | None = Field(None, nullable=True)

    check_url: str | None = Field(None, nullable=True)

    external_transaction_id: str | None = Field(None, nullable=True)

    is_friend: bool | None = Field(None, nullable=True)
    user_comment: str | None = Field(None, nullable=True)

    expiration_date: datetime | None = Field(None, nullable=True)
    live_time: timedelta | None = Field(None, nullable=True)

    is_loyalty_rewards: bool = False
    bonuses_added_amount: float | None = Field(None, nullable=True)
    special_accounts_charges: list[SpecialAccountCharge] | None = Field(
        None, nullable=True
    )
    emitted_coupons_batches: list[term.m.CouponBatch] | None = Field(None, nullable=True)

    items: list[InvoiceItemSchema]


class CRMInvoiceReceiptStoreInfoSchema(BaseORMModel):
    name: str
    address: Optional[str] = Field(None, nullable=True)
    phone: Optional[str] = Field(None, nullable=True)
    logo_url: Optional[str] = Field(None, nullable=True)


class CRMInvoiceReceiptGroupInfoSchema(BaseORMModel):
    name: str
    domain: str
    phone: Optional[str] = Field(None, nullable=True)
    logo_url: Optional[str] = Field(None, nullable=True)


class CRMInvoiceReceiptContentLocalesSchema(BaseORMModel):
    receipt_label: str
    address_label: str
    phone_label: str
    delivery_price_label: str
    bonuses_and_discounts_label: str
    bonuses_label: str
    fees_label: str
    total_for_payment_label: str
    payed_sum_label: str
    position_label: str
    count_label: str
    price_label: str
    sum_label: str
    payment_method_label: str
    footer_label_top: str
    footer_label_bottom: str


class CRMReceiptItemAttributeSchema(BaseORMModel):
    name: str
    price: float = 0.00
    quantity: int = 1


class CRMReceiptItemSchema(BaseORMModel):
    id: int
    name: str
    quantity: int = 1

    price: float
    unit_discount: float = 0
    unit_bonuses_redeemed: float = 0
    unit_discount_and_bonuses_redeemed: float = 0
    final_price: float

    before_loyalty_sum: float = 0
    discount: float = 0
    bonuses_redeemed: float = 0
    discount_and_bonuses_redeemed: float = 0
    final_sum: float

    attributes: Optional[list[CRMReceiptItemAttributeSchema]] = Field(
        None, nullable=True, description="List of attributes for the item"
    )


class CrmReceiptDataSchema(BaseORMModel):
    id: int
    title: Optional[str]
    payment_mode: Optional[str]
    currency: str
    items: Optional[list[CRMReceiptItemSchema]]
    time_created: datetime
    paid_datetime: Optional[datetime]
    discount: float = Field(..., ge=0)
    bonuses_redeemed: float = Field(..., ge=0)
    discount_and_bonuses_redeemed: float = Field(..., ge=0)
    shipment_cost: float = Field(..., ge=0)
    total_sum: float = Field(..., ge=0)
    total_sum_with_extra_fee: float = Field(..., ge=0)
    paid_sum: float = Field(..., ge=0)
    payer_fee: float = Field(..., ge=0)
    tips_sum: float = Field(..., ge=0)
    payment_method: Optional[str] = Field(None, nullable=True)
    store_info: Optional[CRMInvoiceReceiptStoreInfoSchema]
    group_info: CRMInvoiceReceiptGroupInfoSchema
    localised_labels: CRMInvoiceReceiptContentLocalesSchema
