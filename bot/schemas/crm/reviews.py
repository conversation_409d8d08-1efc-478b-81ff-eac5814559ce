import enum
import json
import logging
from datetime import datetime

from fastapi import Query
from pydantic import Field, validator
from pydantic.dataclasses import dataclass

from config import LOC7_API_URL
from utils.media import make_preview_url
from .base import BaseCRMListParams
from ..base import BaseORMModel
from ..review import ReviewPrivacyEnum, ReviewTypeEnum
from ..store.order import UTMLabelsSchema


class CRMReviewReadStatusEnum(enum.Enum):
    READ = "read"
    NOT_READ = "not_read"


@dataclass
class CRMReviewListParams(BaseCRMListParams):
    statuses: list[CRMReviewReadStatusEnum] | None = Query(None, nullable=True)


class CRMReviewMediaTypeEnum(enum.Enum):
    PHOTO = "photo"
    VIDEO = "video"


class CRMReviewMediaObject(BaseORMModel):
    type: CRMReviewMediaTypeEnum
    preview_url: str | None = Field(None, nullable=True)
    media_url: str


class CRMBaseReviewSchema(BaseORMModel):
    id: int
    type: ReviewTypeEnum
    mark: str
    privacy: ReviewPrivacyEnum
    time_created: datetime
    text: str
    additional_text: str | None = Field(None, nullable=True)
    media: list[CRMReviewMediaObject]

    is_read: bool = False
    read_by_user_id: int | None = Field(None, nullable=True)

    user_id: int | None = None

    utm_labels: UTMLabelsSchema | None = Field(
        None, nullable=True,
        description="UTM labels of the invoice, if any"
    )

    @validator("media", pre=True)
    def validate_media(cls, data):
        if not data:
            return []

        if isinstance(data, list):
            return data

        if isinstance(data, str):
            try:
                data = json.loads(data)
            except Exception as e:
                logging.error(
                    f"An error occurred while loading data from json: {e}",
                    exc_info=True
                )

        if not isinstance(data, dict):
            raise ValueError(
                f"data have to be dict, json dict, None or "
                f"list[CRMReviewMediaObject], not {type(data)}"
            )

        def make_obj(type: CRMReviewMediaTypeEnum, path: str):
            preview_url = make_preview_url(type.value, path)
            return CRMReviewMediaObject(
                type=type,
                preview_url=preview_url,
                media_url=f"{LOC7_API_URL}/{path}",
            )

        result = []
        if data.get("photo"):
            result.append(make_obj(CRMReviewMediaTypeEnum.PHOTO, data["photo"]))
        if data.get("video"):
            result.append(make_obj(CRMReviewMediaTypeEnum.VIDEO, data["video"]))
        if isinstance(media_group := data.get("media_group"), dict):
            for key, value in media_group.items():
                if not isinstance(value, list):
                    continue
                for item in value:
                    result.append(make_obj(CRMReviewMediaTypeEnum(key), item))

        return result


class CRMReviewSchema(CRMBaseReviewSchema):
    change_date: datetime
    crm_tag: str

    profile_id: int
    profile_name: str
    business_name: str
    bot_name: str | None = Field(None, nullable=True)

    first_name: str | None = Field(None, nullable=True)
    last_name: str | None = Field(None, nullable=True)
    full_name: str | None = Field(None, nullable=True)
    email: str | None = Field(None, nullable=True)
    photo_url: str | None = Field(None, nullable=True)

    items_text: str = Field(description="for CRM app. It is just mark")
    menu_in_store_comment: str | None = Field(None, nullable=True)
