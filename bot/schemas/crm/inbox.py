import enum
from datetime import datetime
from typing import Annotated, Literal, Protocol, TypeAlias

from fastapi import Depends, Query
from pydantic import BaseModel
from pydantic.dataclasses import dataclass

from .chats import CRMChatSchema
from .ewallet_ext_payment import CRMEwalletExtPaymentListSchema
from .invoices import CRMInvoiceListSchema
from .notifications import CRMTextNotificationSchema
from .orders import CRMOrderListSchema
from .reviews import CRMReviewSchema
from .tickets import CRMTicketListSchema
from ..base import EnumWithItems, ListCursorResponse
from ..db.cursor import Cursor, CursorDirection


class InboxStatus(EnumWithItems):
    NEW = "new"
    IN_PROGRESS = "in_progress"
    DELIVERING = "delivering"
    RECENT = "recent"


class InboxType(enum.Enum):
    ORDER = "order"
    INVOICE = "invoice"
    TICKET = "ticket"
    REVIEW = "review"
    CHAT = "chat"
    TEXT_NOTIFICATION = "text_notification"
    EWALLET_EXT_PAYMENT = "ewallet_ext_payment"


class InboxCursorObj(Protocol):
    inbox_status: InboxStatus
    change_date: datetime
    inbox_type: InboxType
    id: int


@dataclass
class InboxCursor(Cursor):
    status: InboxStatus
    change_date: datetime
    type: InboxType
    id: int

    @classmethod
    def obj(cls, obj: InboxCursorObj):
        return cls(
            direction=CursorDirection.NEXT,
            status=obj.inbox_status,
            change_date=obj.change_date,
            type=obj.inbox_type,
            id=obj.id,
        )


@dataclass
class InboxParams:
    types: list[InboxType] = Query(None, nullable=True, description="default all")
    statuses: list[InboxStatus] = Query(None, nullable=True, description="default all")
    is_paid: bool | None = Query(
        None, nullable=True, description=(
            "specify null or miss this field "
            "to get both paid and unpaid"
        )
    )
    search_text: str | None = Query(None, nullable=True)
    profile_ids: list[int] | None = Query(None, nullable=True)
    store_ids: list[int] | None = Query(None, nullable=True)
    limit: int = Query(10, gt=0)
    cursor: Annotated[InboxCursor | None, Depends(InboxCursor.depend)] = None


class BaseInboxSchema(BaseModel):
    id: int
    status: InboxStatus
    change_date: datetime


class CRMInboxOrderSchema(BaseInboxSchema):
    type: Literal["order"]
    item: CRMOrderListSchema


class CRMInboxInvoiceSchema(BaseInboxSchema):
    type: Literal["invoice"]
    item: CRMInvoiceListSchema


class CRMInboxTicketSchema(BaseInboxSchema):
    type: Literal["ticket"]
    item: CRMTicketListSchema


class CRMInboxReviewSchema(BaseInboxSchema):
    type: Literal["review"]
    item: CRMReviewSchema


class CRMInboxChatSchema(BaseInboxSchema):
    type: Literal["chat"]
    item: CRMChatSchema


class CRMInboxNotificationSchema(BaseInboxSchema):
    type: Literal["text_notification"]
    item: CRMTextNotificationSchema


class CRMInboxEwalletExtPaymentSchema(BaseInboxSchema):
    type: Literal["ewallet_ext_payment"]
    item: CRMEwalletExtPaymentListSchema


CRMInboxSchema: TypeAlias = (
        CRMInboxOrderSchema |
        CRMInboxInvoiceSchema |
        CRMInboxTicketSchema |
        CRMInboxReviewSchema |
        CRMInboxChatSchema |
        CRMInboxNotificationSchema |
        CRMInboxEwalletExtPaymentSchema
)


class CRMInboxResponse(ListCursorResponse[CRMInboxSchema]):
    pass
