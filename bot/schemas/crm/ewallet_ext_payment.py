from datetime import datetime
from typing import Annotated

from fastapi import Query
from pydantic import Field
from pydantic.dataclasses import dataclass

from .base import BaseCRMListParams
from ..base import BaseORMModel
from ..ewallet.external_payment import (
    EWalletExtPaymentStatusInitiatedBy, EWalletExternalPaymentStatus,
    EWalletExternalPaymentTransferData, EWalletExternalPaymentTransferType,
)


@dataclass
class CRMEwalletExtPaymentListParams(BaseCRMListParams):
    statuses: list[EWalletExternalPaymentStatus] | None = Query(
        None, nullable=True
    )


class CRMEwalletExtPaymentListSchema(BaseORMModel):
    id: int
    time_created: datetime
    change_date: datetime
    crm_tag: str
    status: EWalletExternalPaymentStatus

    profile_id: int
    user_id: int

    sum_to_pay: float
    currency: str

    profile_name: str
    business_name: str
    bot_name: str | None = Field(None, nullable=True)

    first_name: str | None = Field(None, nullable=True)
    last_name: str | None = Field(None, nullable=True)
    full_name: str | None = Field(None, nullable=True)
    email: str | None = Field(None, nullable=True)
    photo_url: str | None = Field(None, nullable=True)

    items_text: str = Field(description="for CRM app. It is just text")

    read_allowed: bool = False
    edit_allowed: bool = False


class CRMEwalletExtPaymentStatusHistoryObject(BaseORMModel):
    id: int
    status: EWalletExternalPaymentStatus
    set_datetime: datetime

    comment: str | None = Field(None, nullable=True)

    initiated_by: EWalletExtPaymentStatusInitiatedBy

    initiated_by_user_id: int | None = Field(None, nullable=True)
    initiated_by_user_name: str | None = Field(None, nullable=True)
    initiated_by_user_email: str | None = Field(None, nullable=True)


class CRMEwalletExtPaymentSchema(CRMEwalletExtPaymentListSchema):
    is_payer: bool
    payer_id: int | None = Field(None, nullable=True)
    payer_name: str | None = Field(None, nullable=True)
    payer_email: str | None = Field(None, nullable=True)

    transfer_type: Annotated[
        EWalletExternalPaymentTransferType | None,
        Field(nullable=True)
    ] = None
    transfer_data: Annotated[
        EWalletExternalPaymentTransferData | None,
        Field(nullable=True)
    ] = None

    status_history: list[CRMEwalletExtPaymentStatusHistoryObject]
