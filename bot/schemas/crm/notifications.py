import enum
from datetime import datetime

from fastapi import Query
from pydantic import Field
from pydantic.dataclasses import dataclass

from .base import BaseCRMListParams
from ..base import BaseORMModel
from ..notification import TextNotificationTypeEnum


class CRMTextNotificationReadStatusEnum(enum.Enum):
    READ = "read"
    NOT_READ = "not_read"


@dataclass
class CRMTextNotificationListParams(BaseCRMListParams):
    statuses: list[CRMTextNotificationReadStatusEnum] | None = Query(
        None, nullable=True
    )


class BaseCRMTextNotificationSchema(BaseORMModel):
    id: int
    type: TextNotificationTypeEnum
    time_created: datetime

    profile_id: int

    text: str

    is_read: bool = False
    read_by_user_id: int | None = Field(None, nullable=True)

    user_id: int | None = Field(None, nullable=True)
    menu_in_store_id: int | None = Field(None, nullable=True)


class CRMTextNotificationSchema(BaseCRMTextNotificationSchema):
    change_date: datetime
    crm_tag: str

    profile_name: str
    business_name: str
    bot_name: str | None = Field(None, nullable=True)

    first_name: str | None = Field(None, nullable=True)
    last_name: str | None = Field(None, nullable=True)
    full_name: str | None = Field(None, nullable=True)
    email: str | None = Field(None, nullable=True)
    photo_url: str | None = Field(None, nullable=True)

    items_text: str = Field(description="for CRM app. It is just text")
    menu_in_store_comment: str | None = Field(None, nullable=True)

    read_allowed: bool = False
    edit_allowed: bool = False
