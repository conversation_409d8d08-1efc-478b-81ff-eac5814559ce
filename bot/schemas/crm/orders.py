import enum
from datetime import datetime
from typing import Annotated, Literal

from fastapi import Depends, Query
from pydantic import Field
from pydantic.dataclasses import dataclass

from .base import BaseCRMListParams
from ..base import BaseORMModel
from ..crm.base import StatusChangeInitiatedBy
from ..db.cursor import DesiredDeliveryDateCursor, IDCursor
from incust_terminal_api_client.models import CustomerSpecialAccount
from ..incust.loyalty import CouponBatch
from ..incust.transaction import SpecialAccountCharge
from ..store.order import (
    OrderCurrentStatusType, OrderStatusHistoryStatusType, OrderStatusPayType,
    OrderStatusType,
    OrderType, UTMLabelsSchema,
)
from ..store.types import (
    DeliveryTimeTypeLiteral, PaymentTypeLiteral,
    ShipmentBaseTypeLiteral,
)


class CRMOrdersSort(enum.Enum):
    ID = "id"
    DESIRED_DELIVERY_DATE = "desired_delivery_date"


def detect_cursor(
        sort: CRMOrdersSort = Query(CRMOrdersSort.ID),
        cursor: str | None = Query(None, nullable=True)
):
    if not cursor:
        return None

    match sort:
        case CRMOrdersSort.ID:
            return IDCursor.from_str(cursor)
        case CRMOrdersSort.DESIRED_DELIVERY_DATE:
            return DesiredDeliveryDateCursor.from_str(cursor)


@dataclass
class CRMOrderListParams(BaseCRMListParams):
    is_paid: bool | None = Query(
        None, nullable=True, description=(
            "specify null or miss this field "
            "to get both paid and unpaid"
        )
    )
    sort: CRMOrdersSort = Query(CRMOrdersSort.ID)
    cursor: Annotated[
        IDCursor | DesiredDeliveryDateCursor | None,
        Depends(detect_cursor),
    ] = None
    store_ids: list[int] | None = Query(None, nullable=True)


class BaseCRMOrderSchema(BaseORMModel):
    id: int
    type: OrderType
    crm_tag: str

    status: OrderStatusType
    status_pay: OrderStatusPayType

    first_name: str | None = Field(None, nullable=True)
    last_name: str | None = Field(None, nullable=True)
    full_name: str | None = Field(None, nullable=True)
    email: str | None = Field(None, nullable=True)
    phone: str | None = Field(None, nullable=True)

    currency: str

    before_loyalty_sum: float
    total_sum: float
    tips_sum: float
    sum_to_pay: float
    payer_fee: float = Field(description="will be 0 if order is not paid")
    paid_sum: float = Field(description="will be 0 if order is not paid")

    bonuses_redeemed: float
    discount: float
    discount_and_bonuses_redeemed: float

    menu_in_store_id: int | None = Field(None, nullable=True)

    store_id: int
    user_id: int | None = Field(None, nullable=True)

    comment: str | None = Field(None, nullable=True)

    desired_delivery_date: datetime | None = Field(None, nullable=True)

    time_created: datetime

    utm_labels: UTMLabelsSchema | None = Field(
        None, nullable=True,
        description="UTM labels of the order, if any"
    )


class CRMOrderListSchema(BaseCRMOrderSchema):
    photo_url: str | None = Field(None, nullable=True)

    current_status: OrderCurrentStatusType
    change_date: datetime

    shipment_name: str
    shipment_type: ShipmentBaseTypeLiteral

    store_name: str
    business_name: str = Field(
        description="equal to store_name. For compatibility between objects"
    )

    items_text: str = Field(description="text of order products and counts")

    profile_id: int

    menu_in_store_comment: str | None = Field(None, nullable=True)


class CRMOrderAttributeSchema(BaseORMModel):
    id: int
    quantity: int
    name: str
    price_impact: float
    attribute_id: int
    attribute_code: str


class CRMOrderProductSchema(BaseORMModel):
    id: int
    price: float
    name: str
    quantity: int
    product_id: int
    product_code: str
    thumbnail_url: str | None = Field(None, nullable=True)
    attributes: list[CRMOrderAttributeSchema] | None = Field(None, nullable=True)

    price_with_attributes: float
    discount_amount: float
    before_loyalty_sum: float
    price_after_loyalty: float
    bonuses_redeemed: float
    discount_sum: float
    total_sum: float

    incust_account: CustomerSpecialAccount | None = Field(None, nullable=True)
    incust_card: str | None = Field(None, nullable=True)
    charge_percent: float = 0
    charge_fixed: float = 0
    is_topup_error: bool = False
    topup_charge: float | None = Field(None, nullable=True)


class CRMOrderShipment(BaseORMModel):
    name: str

    base_type: ShipmentBaseTypeLiteral

    price: float
    is_paid_separately: bool

    label_comment: str | None = Field(None, nullable=True)
    comment: str | None = Field(None, nullable=True)

    delivery_time_mode: DeliveryTimeTypeLiteral


class CRMOrderPayment(BaseORMModel):
    name: str
    price: float

    method: PaymentTypeLiteral | Literal["custom"]

    label_comment: str | None = Field(None, nullable=True)
    comment: str | None = Field(None, nullable=True)


class CRMOrderStatusHistoryObject(BaseORMModel):
    id: int
    status: OrderStatusHistoryStatusType
    set_datetime: datetime

    comment: str | None = Field(None, nullable=True)

    source: str | None = Field(None, nullable=True)
    initiated_by: StatusChangeInitiatedBy

    initiated_by_user_id: int | None = Field(None, nullable=True)
    initiated_by_user_name: str | None = Field(None, nullable=True)
    initiated_by_user_email: str | None = Field(None, nullable=True)


class CRMOrderOrmAttributesSchema(BaseCRMOrderSchema):
    delivery_address: str | None = Field(None, nullable=True)
    address_street: str | None = Field(None, nullable=True)
    address_house: str | None = Field(None, nullable=True)
    address_flat: str | None = Field(None, nullable=True)
    address_floor: str | None = Field(None, nullable=True)
    address_entrance: str | None = Field(None, nullable=True)
    address_comment: str | None = Field(None, nullable=True)

    address_lat: str | None = Field(None, nullable=True)
    address_lng: str | None = Field(None, nullable=True)
    address_place_id: str | None = Field(None, nullable=True)

    desired_delivery_time: str | None = Field(None, nullable=True)


class CRMOrderSchema(CRMOrderOrmAttributesSchema, CRMOrderListSchema):
    status_set_datetime: datetime | None = Field(None, nullable=True)
    status_comment: str | None = Field(None, nullable=True)

    shipment: CRMOrderShipment
    payment: CRMOrderPayment

    invoice_id: int | None = Field(None, nullable=True)

    products: list[CRMOrderProductSchema]

    address_text: str | None = Field(None, nullable=True)
    map_link: str | None = Field(None, nullable=True)

    is_loyalty_rewards: bool = False
    bonuses_added_amount: float | None = Field(None, nullable=True)
    special_accounts_charges: list[SpecialAccountCharge] | None = Field(
        None, nullable=True
    )
    emitted_coupons_batches: list[CouponBatch] | None = Field(None, nullable=True)

    status_history: list[CRMOrderStatusHistoryObject] = Field(
        description="History of statuses from creating to latest"
    )
    order_comment_label: str | None = Field(None, nullable=True)

    utm_labels: Annotated[UTMLabelsSchema | None, Field(nullable=True)] = None
