import enum
from typing import Annotated, Literal, TypeAlias

from fastapi import Depends, Query
from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass

from utils.type_vars import T
from ..base import BaseORMModel, ListCursorResponse
from ..db import Cursor, IDCursor


@dataclass
class BaseCRMListParams:
    profile_ids: list[int] | None = Query(None, nullable=True)
    statuses: list[T] | None = Query(None, nullable=True)
    search_text: str | None = Query(None, nullable=True)
    offset: int | None = Query(None, nullable=True)
    limit: int = Query(10)
    cursor: Annotated[IDCursor | None, Depends(IDCursor.depend)] = None


StatusChangeInitiatedBy = Literal["system", "manager", "user", "unknown", "external"]


class CRMFilterTypeEnum(enum.Enum):
    PROFILE = "profile"
    STORE = "store"
    TICKET = "ticket"


class CRMFilterObj(BaseORMModel):
    type: CRMFilterTypeEnum
    profile_id: int
    profile_name: str
    store_id: int | None = Field(None, nullable=True)
    store_name: str | None = Field(None, nullable=True)
    ticket_id: int | None = Field(None, nullable=True)
    ticket_title: str | None = Field(None, nullable=True)


CRMFilterObjectsResponse: TypeAlias = ListCursorResponse[CRMFilterObj]


@dataclass
class CRMFiltersCursor(Cursor):
    profile_id: int
    store_id: int | None = None
    ticket_id: int | None = None


@dataclass
class CRMFilterParams:
    search_text: str | None = Query(None, nullable=True)
    with_stores: bool = Query(False)
    with_tickets: bool = Query(False)
    limit: int = Query(10)
    cursor: Annotated[CRMFiltersCursor | None, Depends(CRMFiltersCursor.depend)] = None


@dataclass
class CRMStoresFilterParams(CRMFilterParams):
    profile_ids: list[int] | None = Query(None, nullable=True)


class CRMReadData(BaseModel):
    read: bool
