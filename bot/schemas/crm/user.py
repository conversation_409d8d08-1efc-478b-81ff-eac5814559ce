from pydantic import BaseModel, Field

from ..base import BaseORMModel


class CRMUserInfo(BaseORMModel):
    id: int
    name: str

    incust_external_id: str | None = Field(None, nullable=True)

    chat_id: int | None = Field(None, nullable=True)
    username: str | None = Field(None, nullable=True)

    first_name: str | None = Field(None, nullable=True)
    last_name: str | None = Field(None, nullable=True)
    full_name: str | None = Field(None, nullable=True)
    photo_url: str
    is_photo_placeholder: bool = False

    email: str | None = Field(None, nullable=True)

    wa_phone: str | None = Field(None, nullable=True)
    wa_name: str | None = Field(None, nullable=True)


class CustomFieldInfo(BaseORMModel):
    field_id: int
    value_id: int
    name: str
    value: str


class CRMUserOwnedProfile(BaseORMModel):
    id: int
    name: str

    logo_url: str | None = Field(None, nullable=True)
    logo_thumbnail_url: str | None = Field(None, nullable=True)


class CRMUser(BaseModel):
    profile_id: int
    info: CRMUserInfo
    custom_fields: list[CustomFieldInfo]
    tags: list[str]
    chat_id: int | None = Field(nullable=True, description="7Loc Chat ID")
    can_chat: bool

    owned_profiles: list[CRMUserOwnedProfile] | None = Field(None, nullable=True)


class CRMUserTgLinkSent(BaseModel):
    sent_to_bot: str
