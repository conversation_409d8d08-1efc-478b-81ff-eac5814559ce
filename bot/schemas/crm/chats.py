import enum
import json
import logging
from datetime import datetime
from typing import Annotated, <PERSON><PERSON><PERSON>s

from fastapi import Depends, Query
from pydantic import Field, validator
from pydantic.dataclasses import dataclass

from .base import BaseCRMListParams
from ..base import BaseORMModel, ListCursorResponse
from ..chat_message import ChatMessageSchema, MessageContentTypeEnum
from ..db.cursor import ChangeDateCursor, IDCursor


class CRMChatPendingStatusEnum(enum.Enum):
    PENDING = "pending"
    NOT_PENDING = "not_pending"


@dataclass
class CRMChatListParams(BaseCRMListParams):
    statuses: list[CRMChatPendingStatusEnum] | None = Query(None, nullable=True)
    cursor: Annotated[ChangeDateCursor | None, Depends(ChangeDateCursor.depend)] = None


class CRMBaseChatSchema(BaseORMModel):
    id: int
    change_date: datetime
    time_created: datetime
    is_pending: bool
    user_id: int


class CRMChatSchema(CRMBaseChatSchema):
    crm_tag: str

    profile_id: int
    profile_name: str
    business_name: str
    bot_name: str | None = Field(None, nullable=True)

    first_name: str | None = Field(None, nullable=True)
    last_name: str | None = Field(None, nullable=True)
    full_name: str | None = Field(None, nullable=True)
    email: str | None = Field(None, nullable=True)
    photo_url: str | None = Field(None, nullable=True)

    is_whatsapp: bool = Field(False)

    items_text: str = Field(description="for CRM app. It is last_message_text")

    last_user_bot_activity: datetime | None = Field(None, nullable=True)

    last_message_text: str | None = Field(None, nullable=True)
    last_message_content_type: MessageContentTypeEnum | None = Field(
        None, nullable=True
    )
    last_message_media_url: str | None = Field(None, nullable=True)
    last_message_media_mime_type: str | None = Field(None, nullable=True)
    last_message_content: dict | None = Field(None, nullable=True)

    @validator("last_message_content", pre=True)
    def validate_media(cls, data):
        if not data:
            return None

        if isinstance(data, str):
            try:
                return json.loads(data)
            except Exception as e:
                logging.error(
                    f"An error occurred while loading data from json: {e}",
                    exc_info=True
                )

        return data


@dataclass
class ChatMessageHistoryParams:
    cursor: Annotated[IDCursor | None, Depends(IDCursor.depend)] = None
    limit: int = Query(10, gt=0, le=100)


ChatMessagesResponse: TypeAlias = ListCursorResponse[ChatMessageSchema]
