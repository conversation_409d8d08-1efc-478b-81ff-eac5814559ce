import enum
from decimal import Decimal
from enum_tools import StrEnum
from pydantic import BaseModel, Field
from typing import Annotated

from .ewallet import EwalletSchema
from ..base import BaseORMModel


class EWalletExternalPaymentStatus(StrEnum):
    CREATED = "CREATED"
    PENDING = "PENDING"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    SUCCESS = "SUCCESS"


class EWalletExtPaymentStatusInitiatedBy(enum.Enum):
    USER = "user"
    MANAGER = "manager"


class EWalletExternalPaymentTransferType(enum.Enum):
    WAVE = "WAVE"
    ORANGE = "ORANGE"


class EWalletExternalPaymentTransferData(BaseModel):
    data: str


class BaseCreateEwalletExternalPaymentData(BaseORMModel):
    amount: Decimal
    transfer_type: EWalletExternalPaymentTransferType
    transfer_data: EWalletExternalPaymentTransferData


class CreateEWalletExternalPaymentData(BaseCreateEwalletExternalPaymentData):
    ewallet_id: int


class EWalletExternalPaymentSchema(CreateEWalletExternalPaymentData):
    id: int
    uuid_id: str

    status: EWalletExternalPaymentStatus

    profile_id: int
    user_id: int
    payer_id: Annotated[int | None, Field(nullable=True)] = None

    transfer_data: Annotated[
        EWalletExternalPaymentTransferData | None,
        Field(nullable=True)
    ] = None

    ewallet: EwalletSchema

    fee: Decimal
    discount_percent: Decimal
    discount_amount: Decimal
    total_amount: Decimal
