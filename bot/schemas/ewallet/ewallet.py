from decimal import Decimal
from pydantic import BaseModel, <PERSON>
from typing import Annotated


class EwalletAccountInfoSchema(BaseModel):
    used_credit: Annotated[float | None, Field(nullable=True)] = None
    available_amount: Annotated[float | bool | None, Field(nullable=True)] = None
    amount: Annotated[float | None, Field(nullable=True)] = None
    credit_limit: Annotated[float | None, Field(nullable=True)] = None
    credit_type: Annotated[str | None, Field(nullable=True)] = None
    is_user_special_account: Annotated[bool | None, Field(nullable=True)] = None
    is_user_can_topup: Annotated[bool | None, Field(nullable=True)] = None


class EwalletSchema(BaseModel):
    id: int
    uuid_id: str
    name: str
    info: Annotated[str | None, Field(nullable=True)] = None
    currency: str
    icon_url: str
    min_amount: Annotated[float | None, Field(nullable=True)] = None

    bot_id: int

    account_info: Annotated[
        EwalletAccountInfoSchema | None,
        Field(nullable=True),
    ] = None

    ad_id: Annotated[int, Field(nullable=True)] = None

    ext_payment_fee_value: Annotated[Decimal | None, Field(nullable=True)] = None
    ext_payment_fee_percent: Annotated[Decimal | None, Field(nullable=True)] = None

    discount_percent: Decimal
