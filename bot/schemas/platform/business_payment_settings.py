import enum

from pydantic import Field

from ..base import BaseORMModel


class BusinessPaymentMethodEnum(enum.Enum):
    WAVE = "wave"
    ORANGE = "orange"
    AIRTEL = "airtel"
    MOMO = "momo"
    EWALLET = "ewallet"
    KPAY = "kpay"


class BaseBusinessPaymentDataSchema(BaseORMModel):
    payment_method: BusinessPaymentMethodEnum
    json_data: dict = Field(default_factory=dict)
    is_enabled: bool = Field(True, null=False)


class BusinessPaymentDataSchema(BaseBusinessPaymentDataSchema):
    id: int
    business_payment_setting_id: int


class BusinessPaymentDataCreateSchema(BaseBusinessPaymentDataSchema):
    ...


class BusinessPaymentDataUpdateSchema(BusinessPaymentDataSchema):
    id: int | None = Field(None, null=True)


class BaseBusinessPaymentSettingSchema(BaseORMModel):
    incust_account_id: str = Field(min_length=36, max_length=36, null=False)
    name: str = Field(null=False)
    description: str | None = Field(None, null=True)
    is_enabled: bool = Field(True, null=False)


class BusinessPaymentSettingSchema(BaseBusinessPaymentSettingSchema):
    id: int
    creator_id: int = Field(null=False)
    payment_data: list[BusinessPaymentDataSchema] = Field(default_factory=list)


class AdminBusinessPaymentSettingListSchema(BusinessPaymentSettingSchema):
    id: int
    creator_id: int = Field(null=False)


class AdminBusinessPaymentSettingUpdateSchema(BaseBusinessPaymentSettingSchema):
    payment_data: list[BusinessPaymentDataUpdateSchema] | None = Field(None, null=True)


class AdminBusinessPaymentSettingCreateSchema(BaseBusinessPaymentSettingSchema):
    payment_data: list[BusinessPaymentDataCreateSchema] | None = Field(None, null=True)


class BusinessPaymentDataStatusUpdateSchema(BaseORMModel):
    is_enabled: bool = Field(..., description="Новий статус активності")
