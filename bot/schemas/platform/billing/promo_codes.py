from typing import Annotated

from fastapi import Query
from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass

from ...billing import BaseBillingServicePacketSchema


@dataclass
class GetBillingPromoCodesParams:
    packet_id: int | None = Query(None, nullable=True)
    trial_period_days: int | None = Query(None, nullable=True)
    stripe_coupon: str | None = Query(None, nullable=True)
    search_text: str | None = Query(None, nullable=True)

    offset: int | None = Query(None, nullable=True)
    limit: int = Query(10, gt=0, le=100)


class CreateBillingPromoCodeData(BaseModel):
    code: Annotated[str | None, Field(min_length=3, max_length=24)] = None
    name: Annotated[str, Field(min_length=3, max_length=255)]
    trial_period_days: Annotated[int, Field(ge=0)] = 0
    stripe_coupon: Annotated[str | None, Field(nullable=True)] = None
    packet_id: Annotated[int | None, Field(nullable=True)] = None


class UpdateBillingPromoCodeData(BaseModel):
    code: Annotated[str | None, Field(min_length=3, max_length=24)] = None
    name: Annotated[str | None, Field(min_length=3, max_length=255)] = None
    trial_period_days: Annotated[int | None, Field(ge=0)] = None
    stripe_coupon: Annotated[str | None, Field(nullable=True)] = None
    packet_id: Annotated[int | None, Field(nullable=True)] = None


class BillingPromoCodePacketSchema(BaseBillingServicePacketSchema):
    id: int


class AdminBillingPromoCodeSchema(BaseModel):
    id: int
    code: str
    name: str

    trial_period_days: int
    stripe_coupon: Annotated[str | None, Field(nullable=True)] = None

    packet: Annotated[BillingPromoCodePacketSchema | None, Field(nullable=True)] = None


class AdminBillingUniquePromoCode(BaseModel):
    code: str
