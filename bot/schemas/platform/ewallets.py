from decimal import Decimal
from pydantic import BaseModel, <PERSON>
from typing import Annotated

from ..ad import AdMutationData, AdSchema
from ..base import BaseORMModel


class EWalletTranslationSchema(BaseORMModel):
    description: str | None = Field(None, nullable=True)
    info: str | None = Field(None, nullable=True)


class BaseAdminEWalletSchema(BaseORMModel):
    incust_account_id: str = Field(min_length=36, max_length=36, null=False)
    name: str = Field(null=False)
    description: str | None = Field(None, null=True)
    info: str | None = Field(None, null=True)
    bot_id: int = Field(null=False)
    is_enabled: bool = Field(null=False)
    countries: list[str] = Field(default_factory=list, description='List of countries')
    invoice_template_id: int | None = None
    is_private: bool = Field(default=False)
    min_amount: float | None = Field(None, ge=0)
    langs_list: list[str] | None = Field(
        default=None, nullable=True, description='List of languages'
    )
    translations: dict[str, EWalletTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )
    currency: str = Field(default="USD", null=False)

    ext_payment_fee_value: Annotated[Decimal | None, Field(nullable=True)] = None
    ext_payment_fee_percent: Annotated[Decimal | None, Field(nullable=True)] = None

    discount_percent: Annotated[Decimal | None, Field(nullable=True)] = None


class AdminEWalletSchema(BaseAdminEWalletSchema):
    id: int
    creator_id: int = Field(null=False)
    uuid_id: str | None = None
    min_amount: float | None = Field(None, ge=0)
    icon_url: str
    deep_link: str
    ad: AdSchema | None = Field(None, nullable=True)

    discount_percent: Decimal


class AdminEWalletUpdateSchema(BaseAdminEWalletSchema):
    ad: AdMutationData | None = Field(None, nullable=True)


class AdminEWalletCreateSchema(BaseAdminEWalletSchema):
    ...


class AdminEWalletDeepLinkSchema(BaseModel):
    deep_link: str


class AdminEWalletUserListSchema(BaseORMModel):
    id: int
    username: str | None = None
    email: str | None = None
    wa_phone: str | None = None
    full_name: str | None = None
    photo_url: str | None = None
    birthday: str | None = None
    chat_id: int | None = None
    wa_name: str | None = None
    name: str | None = None


class AdminEWalletClientBotListSchema(BaseModel):
    id: int
    name: str
    group_id: int
    bot_type: str

    class Config:
        orm_mode = True


class AdminEWalletInvoiceTemplateListSchema(BaseModel):
    id: int
    title: str
    group_id: int
    is_deleted: bool
    photo_url: str | None = None
    currency: str

    class Config:
        orm_mode = True
