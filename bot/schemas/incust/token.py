from pydantic import BaseModel


class Card(BaseModel):
    business_id: str | None = None
    code: str | None = None
    id: str | None = None
    loyalty_id: str | None = None
    photo: str | None = None
    scope: str | None = None
    title: str | None = None


class Email(BaseModel):
    confirm: int | None = None
    id: str | None = None
    primary: int | None = None
    value: str | None = None


class Phone(BaseModel):
    compact: str | None = None
    confirm: int | None = None
    id: str | None = None
    primary: int | None = None
    value: str | None = None


class QrLegacy(BaseModel):
    qr_v1_allowed: bool | None = None
    qr_v1_user_id: str | None = None


class SocialNetwork(BaseModel):
    token: str | None = None
    type: str | None = None


class User(BaseModel):
    avatar: str | None = None
    birth_date: str | None = None
    cards: list[Card] | None | list = None
    country: str | None = None
    emails: list[Email] | None | list = None
    external_id: str | None = None
    gender: str | None = None
    id: str | None = None
    language: str | None = None
    login: str | None = None
    marketing_email: int | None = None
    marketing_sms: int | None = None
    name: str | None = None
    news_email: int | None = None
    password_exist: bool | None = None
    phone: str | None = None
    phones: list[Phone] | None | list = None
    qr_legacy: QrLegacy | None = None
    qr_secret_key: str | None = None
    qr_user_id: str | None = None
    qr_version: int | None = None
    social_networks: list[SocialNetwork] | None | list = None
    transactional_sms: int | None = None
    welcome_message_sent: int | None = None

