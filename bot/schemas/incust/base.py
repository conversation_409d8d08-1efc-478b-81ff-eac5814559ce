import io
from typing import Literal

from incust_api.api import term
from pydantic import BaseModel

from ..templater import BaseTemplateSchema


class IncustErrorOrBaseSchema(BaseModel):
    code: int | None = None
    message: str | None = None
    description: str | None = None

class ShowIncustQrCardSchema(BaseModel):
    class Config:
        arbitrary_types_allowed = True

    identifier: str | None = None
    photo: io.BytesIO | None = None
    temp_code: str | None = None
    text: str | None = None


class CouponShowData(term.m.Coupon):
    # Додаткові поля для Pay4Say
    text: str | None = None  # Форматований текст купона для відображення
    url: str | None = None  # URL купона для відображення
    pdf_url: str | None = None  # URL PDF файлу купона
    pdf_media_id: int | None = None  # ID медіа об'єкта PDF файлу

    class Config:
        arbitrary_types_allowed = True
        
    @classmethod
    def from_coupon(cls, coupon):
        """Створює CouponShowData з term.m.Coupon або подібного об'єкта."""
        # Отримуємо всі поля з базового купона
        coupon_data = {}
        if hasattr(coupon, '__dict__'):
            coupon_data = {k: v for k, v in coupon.__dict__.items() if not k.startswith('_')}
        elif hasattr(coupon, 'dict'):
            coupon_data = coupon.dict()
        elif isinstance(coupon, dict):
            coupon_data = coupon
        
        return cls(**coupon_data)


class Info(BaseModel):
    channel: str
    code: str
    length: int
    recipient: str


class IncustData(BaseModel):
    white_label_id: str | None = None
    loyalty_id: str | None = None
    server_api: str | None = None
    type_client_auth: str | None = None
    loyalty_applicable_type: str | None = None
    prohibit_redeeming_bonuses: bool | None = False
    prohibit_redeeming_coupons: bool | None = False
    loyalty_settings_id: int | None = None
    target_object: dict | None = None


class IncustCustomerData(BaseModel):
    token: str | None = None
    external_id: str | None = None
    user_card: term.m.CardInfo | None = None


class IncustUserCardTemplate(BaseTemplateSchema):
    TEMPLATE_PATH = "incust_card.html"

    username: str
    qr: str
    photo: str | None
    code: str
    title: str


RulesTypeLiteral = Literal["by-all-rules", "by-charge-only-rules", "without-rules"]