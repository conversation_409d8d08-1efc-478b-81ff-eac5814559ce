from typing import Literal, Optional

from incust_api.api import term
from pydantic import BaseModel

from schemas.incust import InCustCouponSchema


class TransactionErrors(BaseModel):
    code: str | None = None
    level: str | None = None
    message: str | None = None
    type: str | None = None


class InlineModel12(BaseModel):
    amount: int | None = None
    price: int | None = None
    volume: int | None = None


class InlineModel13(BaseModel):
    volume: str | None = None


class ProbeAlarm(BaseModel):
    created: str | None = None
    type: str | None = None


class ProbeMeasurements(BaseModel):
    alarms: list[ProbeAlarm] | None = None
    controller_id: str | None = None
    processed: str | None = None
    product_density: float | None = None
    product_height: float | None = None
    product_mass: float | None = None
    product_tc_volume: float | None = None
    product_ullage: float | None = None
    product_volume: float | None = None
    status: str | None = None
    tank_filling_percentage: float | None = None
    tank_id: str | None = None
    temperature: float | None = None
    water_height: float | None = None
    water_volume: float | None = None


class TankConfiguration(BaseModel):
    critical_high_product_alarm_height: int | None = None
    critical_low_product_alarm_height: int | None = None
    height: int | None = None
    high_product_alarm_height: int | None = None
    high_water_alarm_height: int | None = None
    low_product_alarm_height: int | None = None


class FuelControllerConfiguration(BaseModel):
    decimal_digits: InlineModel12 | None = None
    measurement_units: InlineModel13 | None = None


class FuelTank(BaseModel):
    actual_measurements: ProbeMeasurements | None = None
    configuration: TankConfiguration | None = None
    finish_measurements: ProbeMeasurements | None = None
    grade_code: str | None = None
    grade_id: str | None = None
    id: str | None = None
    internal_id: str | None = None
    start_measurements: ProbeMeasurements | None = None


class GoodsPassThruFleet(BaseModel):
    measure_unit: str | None = None
    product_code: str | None = None


class AdditionalServiceCostValue(BaseModel):
    threshold: float | None = None
    type: str | None = None
    value: float | None = None


class AdditionalServiceCost(BaseModel):
    currency: str | None = None
    decimal_digits: int | None = None
    min_value: float | None = None
    values: list[AdditionalServiceCostValue] | None = None


class UserSignature(BaseModel):
    data: str | None = None
    mime_type: str | None = None


class PriceExplainDetailingItem(BaseModel):
    decimal_digits: int | None = None
    exempt: bool | None = None
    include_in_receipt: bool | None = None
    initial_value: float | None = None
    source: str | None = None
    type: str | None = None
    value: float | None = None
    value_code: str | None = None
    value_id: str | None = None
    value_option: str | None = None
    value_origin: float | None = None
    value_title: str | None = None
    value_type: str | None = None


class PriceMargins(BaseModel):
    terminal_additional_costs: list[PriceExplainDetailingItem] | None = None


class PriceTaxes(BaseModel):
    base: list[PriceExplainDetailingItem] | None = None
    sales: list[PriceExplainDetailingItem] | None = None


class JobFuel(BaseModel):
    avi_id: str | None = None
    captured_amount: float | None = None
    captured_price: float | None = None
    captured_volume: float | None = None
    connection_failed: bool | None = None
    controller: str | None = None
    controller_configuration: FuelControllerConfiguration | None = None
    controller_id: str | None = None
    error_message: str | None = None
    filled_amount: float | None = None
    filled_volume: float | None = None
    filling_doze: float | None = None
    filling_price: float | None = None
    filling_price_currency: str | None = None
    filling_price_margins: PriceMargins | None = None
    filling_price_special_account_id: str | None = None
    filling_price_taxes: PriceTaxes | None = None
    filling_price_type: str | None = None
    filling_price_unit: str | None = None
    filling_type: str | None = None
    goods_code: str | None = None
    grade_id: str | None = None
    nozzle: str | None = None
    nozzle_id: str
    odometer: int | None = None
    odometer_unit: str | None = None
    pump: str | None = None
    pump_authorize_confirmation: str | None = None
    pump_id: str | None = None
    status: str | None = None
    tank: str | None = None
    tank_details: FuelTank | None = None
    tank_id: str | None = None
    vehicle_id: str | None = None


class PaymentExternalInfo(BaseModel):
    app_version: str | None = None
    auth_code: str | None = None
    bank_response_code: str | None = None
    batch_number: str | None = None
    card_holder_name: str | None = None
    card_issuer_name: str | None = None
    card_label: str | None = None
    cvm: str | None = None
    emv_ac: str | None = None
    emv_aid: str | None = None
    emv_aid_name: str | None = None
    emv_atc: str | None = None
    emv_iad: str | None = None
    emv_tsi: str | None = None
    emv_tvr: str | None = None
    entry_mode: str | None = None
    gateway_transaction_id: str | None = None
    host_timestamp: str | None = None
    invoice_number: str | None = None
    merchant_id: str | None = None
    pan: str | None = None
    response_code: str | None = None
    response_code_iso: str | None = None
    response_text: str | None = None
    sequence_number: str | None = None
    system_type: str | None = None
    terminal_id: str | None = None
    transaction_name: str | None = None
    transaction_reference_number: str | None = None


class PhoneNumber(BaseModel):
    number: str | None = None


class TransactionReceiptSendTo(BaseModel):
    email: str


class UserExternalForm(BaseModel):
    created: str | None = None
    external_form_id: str | None = None
    file: str | None = None
    form_data: dict | None = None
    id: str | None = None
    signature: str | None = None
    signed: bool | None = None
    title: str | None = None
    type: str | None = None
    user_data: dict | None = None
    user_id: str | None = None


class Job(BaseModel):
    auto_capture_parent_transaction: bool | None = None
    auto_start: bool | None = None
    created: str | None = None
    details: dict | None = None
    finalize_transaction_errors: list[str] | None = None
    finalize_transaction_failed: bool | None = None
    finalize_transaction_id: str | None = None
    finished: str | None = None
    id: str | None = None
    started: str | None = None
    status: str | None = None
    transaction_id: str | None = None
    type: str
    user_id: str | None = None


class MobileAppInfo(BaseModel):
    id: str | None = None
    status: str | None = None
    type: str | None = None


class AdditionalService(BaseModel):
    code: str | None = None
    cost: list[AdditionalServiceCost] | None = None
    default: int | None = None
    id: str | None = None
    public_description: str | None = None
    public_title: str | None = None
    sort_order: int | None = None
    type: str | None = None


class InlineModel19(BaseModel):
    facebook: str | None = None
    google: str | None = None
    instagram: str | None = None
    linkedin: str | None = None
    pinterest: str | None = None
    twitter: str | None = None
    vk: str | None = None
    web: str | None = None


class CorporateStaffPosition(BaseModel):
    id: str | None = None
    public_description: str | None = None
    public_title: str | None = None
    title: str | None = None


class RuleImplementationData(BaseModel):
    coupon: InCustCouponSchema | None = None
    implemented_by: str | None = None
    result: InlineModel19 | None = None


class LoyaltyRulePos(BaseModel):
    id: str
    title: str | None = None


class ThresholdsValues(BaseModel):
    threshold: float
    value: float


class LoyaltyCategory(BaseModel):
    id: int | None = None
    image_url: str | None = None
    title: str | None = None


class InlineModel16(BaseModel):
    facebook: str | None = None
    google: str | None = None
    instagram: str | None = None
    linkedin: str | None = None
    pinterest: str | None = None
    twitter: str | None = None
    vk: str | None = None
    web: str | None = None


class BankCardExternalInfo(BaseModel):
    edc_card_type: str | None = None
    id: str | None = None
    status: str | None = None
    type: str | None = None


class SpecialAccountCharge(BaseModel):
    amount: float
    currency: str | None = None
    id: str
    type: str | None = None
    title: str | None = None


class CheckTank(BaseModel):
    address: str | None = None
    code: str | None = None
    goods_code: str | None = None
    latitude: int | None = None
    longitude: int | None = None
    physical_dip_amount: int | None = None
    product_volume_after: int | None = None
    product_volume_before: int | None = None
    tank_id: str | None = None
    title: str | None = None
    volume: int | None = None


class TransactionReceiptActions(BaseModel):
    print_on_terminal: bool | None = None
    send_to_customer: TransactionReceiptSendTo | None = None


class CheckUserExternalFormRelations(BaseModel):
    required: list[UserExternalForm] | None = None


class Image(BaseModel):
    id: str | None = None
    url: str | None = None


class CheckMarginExplainDetailingItem(BaseModel):
    decimal_digits: int | None = None
    include_in_receipt: bool | None = None
    source: str | None = None
    type: str | None = None
    value: int | None = None
    value_code: str | None = None
    value_id: str | None = None
    value_title: str | None = None


class CheckTaxExplainDetailingItem(BaseModel):
    decimal_digits: int | None = None
    include_in_receipt: bool | None = None
    source: str | None = None
    type: str | None = None
    value: int | None = None
    value_code: str | None = None
    value_id: str | None = None
    value_title: str | None = None


class CheckItemAdditionalInfo(BaseModel):
    job: Job | None = None
    loyalty_item_id: str | None = None
    processing_id: str | None = None
    quantity_decimal_digits: int | None = None
    quantity_recalculate_skipped: bool | None = None
    quantity_recalculate_type: str | None = None


class GoodsPassThru(BaseModel):
    fleet: GoodsPassThruFleet | None = None


class Loyalty(BaseModel):
    categories: list[LoyaltyCategory] | None = None
    legal_info: str | None = None
    links: InlineModel16 | None = None
    photos: list[str] | None = None
    privacy_policy_url: str | None = None
    title: str | None = None


class Payment(BaseModel):
    amount: float
    bank_card_external: BankCardExternalInfo | None = None
    change: float | None = None
    currency: str | None = None
    customer_fee_amount: float | None = None
    external_info: PaymentExternalInfo | None = None
    id: str | None = None
    mobileapp: MobileAppInfo | None = None
    receipt: int | None = None
    status: str | None = None
    type: str


class Pos(BaseModel):
    additional_services: list[AdditionalService] | None = None
    additional_services_required: int | None = None
    addresses: str | None = None
    categories: list[LoyaltyCategory] | None = None
    country: str | None = None
    id: str | None = None
    links: LoyaltyCategory | None = None
    maximum_service_distance: int | None = None
    phone_prefix: str | None = None
    phone_prefix_local: str | None = None
    phones: list[PhoneNumber] | None = None
    photos: list[Image] | None = None
    title: str | None = None
    type: str | None = None


class SpecialAccount(BaseModel):
    active: int | None = None
    corporate: int | None = None
    credit_limit: float | None = None
    credit_type: str | None = None
    currency: str | None = None
    goods_items: list[str] | None = None
    id: str | None = None
    public_title: str | None = None
    title: str | None = None
    type: str | None = None
    unconfirmed_redeem_amount: float | None = None
    amount: int | float | None = None


class LoyaltyRule(BaseModel):
    action_type: str | None = None
    active: int | None = None
    applicable_for_customer: str | None = None
    applicable_for_sale: str | None = None
    apply_when_bonuses_redeemed: int | None = None
    apply_when_customers_account_payment: int | None = None
    automatic: int | None = None
    card_category_id: str | None = None
    charge_type: str | None = None
    combine: int | None = None
    comment: str | None = None
    coupon_batch_code: str | None = None
    coupon_do_not_add_to_wallet: int | None
    depends_calc_period: str | None = None
    depends_type: str | None = None
    excluded_item_categories: list[str] | None = None
    excluded_items: list[str] | None = None
    id: str | None = None
    implementation_data: RuleImplementationData | None = None
    included_item_categories: list[str] | None = None
    included_items: list[str] | None = None
    items_limit_max: int | None = None
    items_limit_min: int | None = None
    loyalty_id: str | None = None
    pos: list[LoyaltyRulePos] | None = None
    priority: int | None = None
    promotional_bonuses_expire_date: str | None = None
    promotional_bonuses_expire_type: str | None = None
    promotional_bonuses_expire_value: int | None = None
    promotional_bonuses_starting_date: str | None = None
    promotional_bonuses_starting_type: str | None = None
    promotional_bonuses_starting_value: int | None = None
    scheduled: int | None = None
    special_account_id: str | None = None
    time_from: str | None = None
    time_to: str | None = None
    title: str | None = None
    valid_after: str | None = None
    valid_before: str | None = None
    value: list[ThresholdsValues] | None = None
    week_days: list[str] | None = None


class CheckService(BaseModel):
    additional_service_id: str | None = None
    amount: float | None = None
    code: str
    cost: float | None = None
    decimal_digits: int | None = None
    title: str | None = None
    type: str | None = None
    value: float | None = None


class CorporateVehicleInfo(BaseModel):
    avi_id: str | None = None
    description: str | None = None
    id: str | None = None
    position: CorporateStaffPosition | None = None
    position_id: str | None = None
    qr_code: str | None = None
    vehicle_id: str | None = None


class CheckMargins(BaseModel):
    terminal_additional_costs: list[
                                   CheckMarginExplainDetailingItem] | None = None


class CheckTaxes(BaseModel):
    base: list[CheckTaxExplainDetailingItem] | None = None
    sales: list[CheckTaxExplainDetailingItem] | None = None


class CheckItem(BaseModel):
    additions: CheckItemAdditionalInfo | None = None
    amount: float | None = None
    amount_decimal_digits: int | None = None
    authorized_amount: float | None = None
    authorized_quantity: float | None = None
    bonuses_added: float | None = None
    bonuses_decimal_digits: int | None = None
    calculated_bonuses_redeemed_amount: float | None = None
    calculated_discount_amount: float | None = None
    calculated_margins: CheckMargins | None = None
    calculated_price: float | None = None
    calculated_taxes: CheckTaxes | None = None
    calculated_unit_bonuses_redeemed_amount: float | None = None
    calculated_unit_discount_amount: float | None = None
    category: str | None = None
    code: str
    discount_amount: float | None = None
    loyalty_item_id: str | None = None
    margins: PriceMargins | None = None
    passthru_data: GoodsPassThru | None = None
    price: float | None = None
    price_decimal_digits: int | None = None
    processed_amount: float | None = None
    processed_quantity: float | None = None
    quantity: int | float | None = None
    quantity_decimal_digits: int | None = None
    quantity_recalculate_type: str | None = None
    special_accounts_decimal_digits: int | None = None
    taxes: PriceTaxes | None = None
    title: str | None = None
    unit: str | None = None
    weighing: int | None = None
    whole_number: int | None = None


class BillingInfo(BaseModel):
    address: dict | None = None
    email: str | None = None
    name: str | None = None
    phone: str | None = None


class BonusAdded(BaseModel):
    amount: float
    expire: str | None = None
    starting: str | None = None


class CheckBusiness(BaseModel):
    description: str | None = None
    id: str | None = None
    photos: list[Image] | None = None
    title: str | None = None


class ProcessIncustCheckPayloadSchema(term.m.Check):
    rules_type: Literal[
                    "by-all-rules", "by-charge-only-rules", "without-rules"] | None = \
        None
    store_id: int | None = None
    invoice_template_id: int | None = None
    loyalty_settings_id: int | None = None


class IncustTransactionSpecialAccount(BaseModel):
    currency: str | None
    id: str
    title: str | None
    type: str | None


class IncustTransactionCheck(BaseModel):
    amount: float
    amount_decimal_digits: int
    bonuses_decimal_digits: int
    comment: str | None = None
    customer_id: str | None
    id: str | None
    id_type: str | None
    payment_id: str | None
    services_amount: float
    shipping_amount: float
    special_account: IncustTransactionSpecialAccount | None
    special_accounts_decimal_digits: int
    summary_amount: float
    transaction_cancelled: int
    transaction_id: str | None
    transaction_type: str | None


class IncustTransaction(BaseModel):
    amount: float
    amount_decimal_digits: int
    amount_to_pay: float
    bonuses_added: float
    bonuses_decimal_digits: int
    bonuses_on_account: float
    bonuses_redeemed: float
    business_customer_id: None | str = None
    canceled: None | bool = None
    cancelled: None | int = None
    check: None | IncustTransactionCheck = None
    corporate_customer_email: None | str = None
    corporate_customer_name: None | str = None
    customer_special_account_id: None | str = None
    description: None | str = None
    discount_amount: float
    finalize_transaction_id: None | str = None
    finalized: None | str = None
    id: str
    loyalty_id: None | str = None
    payment_type: None | str = None
    processed: None | str = None
    processed_local: None | str = None
    services_amount: float
    shipping_amount: float
    special_account_amount: float
    special_account_currency: None | str = None
    special_account_id: None | str = None
    special_account_title: None | str = None
    special_account_type: None | str = None
    special_accounts_decimal_digits: int
    summary_amount: float
    title: None | str = None
    type: None | Literal[
        'reserve', 'presale', 'sale', 'cancel', 'redeem-giftcard', 'bonus-add',
        'special-account-refill', 'coupon-sale', 'authorize-payment',
        'bonus-write-offs', 'special-account-write-offs'
    ] = None
    user_email: None | str = None
    user_external_id: None | str = None
    user_name: None | str = None
    user_phone: None | str = None


class ConsentInfo(BaseModel):
    comment: Optional[str] = None
    confirmation: Optional[Literal['pin', 'email', 'interface', 'business']] = None
    processed: Optional[str] = None
    # request: Optional[dict] = None
    type: Optional[Literal['business-registration', 'customer-registration',
    'business-customer-import', 'terminal-customer-registration']] = None
