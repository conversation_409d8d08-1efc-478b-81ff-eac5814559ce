from incust_api.api import client as incust_client, term
from incust_terminal_api_client.models import (
    CustomerBonusesAccount, CustomerSpecialAccount,
)
from pydantic import BaseModel

from .loyalty import ImageClient


class EWalletCustomerAccount(BaseModel):
    """Кастомна схема для інтеграції з EWallet системою"""
    ewallet_id: int
    incust_account_id: str
    title: str
    currency: str
    invoice_template_id: int | None = None
    message: str | None = None
    is_user_special_account: bool | None = None
    is_user_can_topup: bool | None = None


class IncustWallet(BaseModel):
    """Кастомна схема гаманця з Terminal API типами"""
    coupons: list[term.m.Coupon] | None = None
    bonuses: list[CustomerBonusesAccount] | None = None
    accounts: list[CustomerSpecialAccount] | None = None
    description: str | None = None
    favorite: bool | None = None
    id: str | None = None
    title: str | None = None
    type: str | None = None
    photos: list[ImageClient] | None = None


class IncustCouponExt(BaseModel):
    """Кастомна схема для розширеного купона з Client API"""
    coupon: incust_client.m.Coupon
    is_in_wallet: bool = False
    message: str | None = None
    transaction: incust_client.m.Transaction | None = None
