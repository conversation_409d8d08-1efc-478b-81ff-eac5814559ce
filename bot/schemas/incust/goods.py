from pydantic import BaseModel


class ImageObject(BaseModel):
    id: str = None
    url: str = None


class MultiLanguageObject(BaseModel):
    az: str = None
    de: str = None
    en: str = None
    fr: str = None
    pl: str = None
    ru: str = None
    sk: str = None
    uk: str = None


class PriceExplainDetailingItem(BaseModel):
    decimal_digits: int = None
    exempt: bool = None
    include_in_receipt: bool = None
    initial_value: float = None
    source: str = None
    type: str = None
    value: float = None
    value_code: str = None
    value_id: str = None
    value_option: str = None
    value_origin: float = None
    value_title: str = None
    value_type: str = None


class CorporateSpecialAccountPrice(BaseModel):
    id: str = None
    price: float = None
    price_base: float = None
    price_corporate_customer: float = None
    price_sale: float = None


class CorporateSpecialAccountAccessPrice(BaseModel):
    id: str = None
    price: float = None
    price_base: float = None
    price_corporate_customer: float = None
    price_sale: float = None


class PriceMargins(BaseModel):
    terminal_additional_costs: list[PriceExplainDetailingItem] = None


class SpecialAccount(BaseModel):
    active: int = 1
    corporate: int = 0
    credit_limit: float = None
    credit_type: str = None
    currency: str = None
    goods_items: list[str] = None
    id: str = None
    public_title: str = None
    title: str = None
    type: str = None
    unconfirmed_redeem_amount: float = None


class PriceTaxes(BaseModel):
    base: list[PriceExplainDetailingItem] = None
    sales: list[PriceExplainDetailingItem] = None


class GoodsPrice(BaseModel):
    applicable: bool = None
    corporate_special_account: CorporateSpecialAccountPrice = None
    corporate_special_accounts_access: list[CorporateSpecialAccountAccessPrice] = None
    currency: str = None
    id: str = None
    margins: PriceMargins = None
    price: float = None
    price_decimal_digits: int = None
    price_including_taxes: float = None
    price_posted: float = None
    price_sale: float = None
    special_account: SpecialAccount = None
    special_account_id: str = None
    taxes: PriceTaxes = None
    type: str = None


class InCustGood(BaseModel):
    active: int = 1
    additional_images: list[ImageObject] = None
    barcode: str = None
    code: str = None
    color: int = None
    description: str = None
    id: str = None
    image: ImageObject = None
    image_id: str = None
    loyalty_id: str = None
    parent_code: str = None
    parent_id: str = None
    parent_public_title: str = None
    parent_title: str = None
    preset_recommnded_values_only: int = 0
    price: list[GoodsPrice] = None
    service_types: list[str] = None
    sort_order: int = None
    title: str = None
    unit: str = None
    variable_price: int = 0
    variable_total: int = 0
    weighing: int = 0
    whole_number: int = 0


class TotalRowCount(BaseModel):
    total_row_count: int | None = None


class RespTermGoods(BaseModel):
    data: list[InCustGood]
    total_row_count: TotalRowCount | None


class Image(BaseModel):
    id: str = None
    url: str = None


class InCustGoodCategory(BaseModel):
    active: int = 1
    color: int = None
    id: str = None
    image: Image = None
    link: str = None
    parent_id: str = None
    public_description: str = None
    public_title: str = None
    title: str = None
    nodes: list["InCustGoodCategory"] | None = None
    sort: int | None = None


InCustGoodCategory.update_forward_refs()


class InCustGoodCategories(BaseModel):
    categories: list[InCustGoodCategory] | None = None
