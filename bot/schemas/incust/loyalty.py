from typing import Literal

from pydantic import BaseModel

from .goods import ImageObject
from ..base import BaseExtraAllowedModel


class CouponBatch(BaseExtraAllowedModel):
    active: int | None = None
    business_code: str | None = None
    business_id: str | None = None
    code: str | None = None
    customers_price_amount: int | None = None
    customers_price_currency: str | None = None
    customers_price_special_account_id: str | None = None
    customers_price_special_account_title: str | None = None
    customers_price_type: str | None = None
    description: str | None = None
    emission_period_begin: str | None = None
    emission_period_end: str | None = None
    external_code_visibility_type: str | None = None
    id: str | None = None
    image: str | None = None
    loyalty_id: str | None = None
    public_description: str | None = None
    public_title: str | None = None
    recommendation_fee_amount: int | None = None
    recommendation_fee_bonuses_currency: str | None = None
    recommendation_fee_coupon_batch_code: str | None = None
    recommendation_fee_coupon_batch_id: str | None = None
    recommendation_fee_coupon_batch_title: str | None = None
    recommendation_fee_special_account_id: str | None = None
    recommendation_fee_special_account_title: str | None = None
    recommendation_fee_type: str | None = None
    redeem_at_terminal: int | None = None
    share_allowed: int | None = None
    title: str | None = None
    type: Literal['check-modifier', 'certificate', 'external'] | str | None = None


class InCustCouponSchema(BaseExtraAllowedModel):
    applicable: bool | None = None
    batch: CouponBatch | None = None
    charge_amount: float | None = None
    charge_bonuses_currency: str | None = None
    charge_type: str | None = None
    code: str | None = None
    description: str | None = None
    do_not_add_to_wallet: bool | None = None
    error: str | None = None
    expire_date: str | None = None
    external_code: str | None = None
    external_code_visibility_type: Literal['always-open', 'on-redeem'] | None = None
    id: str | None = None
    image: str | None = None
    locked: bool | None = None
    redeem_at_terminal: int | None = None
    redeem_dt: str | None = None
    redeem_result_transaction_id: str | None = None
    recommendation_fee_amount: float | None = None
    recommendation_fee_bonuses_currency: str | None = None
    recommendation_fee_coupon_batch_title: str | None = None
    recommendation_fee_promotional_bonuses_expire_date: str | None = None
    recommendation_fee_promotional_bonuses_expire_type: (Literal[
                                                             'date', 'day', 'month'] |
                                                         None) = None
    recommendation_fee_promotional_bonuses_expire_value: int | None = None
    recommendation_fee_special_account_title: str | None = None
    recommendation_fee_type: Literal[
                                 'regular-bonuses',
                                 'promotional-bonuses',
                                 'special-account',
                                 'coupon',
                                 'bonuses',
                                 "none",
                             ] | None = None
    share_allowed: Literal[0, 1] | None = None
    status: Literal['open', 'redeemed', 'cancelled'] | None = None
    title: str | None = None
    type: Literal['check-modifier', 'certificate', 'external'] | None = None
    valid: bool | None = None
    emission_dt: str | None = None


class PriceExplainDetailingItemClient(BaseModel):
    amount: float | None = None
    description: str | None = None
    type: str | None = None


class PriceMarginsClient(BaseModel):
    terminal_additional_costs: list[PriceExplainDetailingItemClient] | None = None


class PreConfiguredValuesClient(BaseModel):
    color: int | None = None
    currency: str | None = None
    id: str | None = None
    sort_order: int | None = None
    title: str | None = None
    type: str | None = None
    value: float | None = None


class ImageClient(BaseModel):
    id: str | None = None
    url: str | None = None


class GoodsPriceClient(BaseModel):
    currency: str | None = None
    decimal_digits: int | None = None
    value: float | None = None


class PriceTaxesClient(BaseModel):
    amount: float | None = None
    description: str | None = None
    type: str | None = None


class GoodsClient(BaseModel):
    additional_images: list[ImageClient] | None = None
    barcode: str | None = None
    code: str | None = None
    color: int | None = None
    currency: str | None = None
    description: str | None = None
    id: str | None = None
    image: ImageClient | None = None
    margins: PriceMarginsClient | None = None
    parent_id: str | None = None
    parent_title: str | None = None
    preconfigured_values: list[PreConfiguredValuesClient] | None = None
    preset_recommnded_values_only: int | None = None
    price: float | None = None
    price_decimal_digits: int | None = None
    prices: list[GoodsPriceClient] | None = None
    service_types: list[str] | None = None
    taxes: PriceTaxesClient | None = None
    title: str | None = None
    unit: str | None = None
    variable_price: int | None = None
    variable_total: int | None = None
    weighing: int | None = None
    whole_number: int | None = None


class InlineModel1Client(BaseModel):
    code: str | None = None
    goods: GoodsClient | None = None
    id: str | None = None
    internal_id: str | None = None
    tank_id: str | None = None
    up: bool | None = None


class InlineModel0Client(BaseModel):
    corporate_user: dict | None = None
    id: str | None = None
    suspended: int | None = None


class InlineModel3Client(BaseModel):
    max_price: float | None = None
    min_price: float | None = None


class InlineModel4Client(BaseModel):
    capacity: str | None = None
    consumption: str | None = None
    distance: str | None = None
    volume: str | None = None


class InlineModel5Client(BaseModel):
    display_name: str | None = None
    id: str | None = None
    is_active: int | None = None
    nozzle_count: int | None = None
    order: int | None = None
    pump_type: str | None = None
    status: str | None = None
    type: str | None = None


class FuelControllerConfigurationClient(BaseModel):
    decimal_digits: InlineModel3Client | None = None
    measurement_units: InlineModel4Client | None = None


class FuelRecClient(BaseModel):
    configuration: FuelControllerConfigurationClient | None = None
    configuration_reading_status: str | None = None
    configuration_updated: str | None = None
    id: str | None = None
    pump_authorize_confirmation: str | None = None
    pump_authorize_type: str | None = None
    pumps: list[InlineModel5Client] | None = None
    skip_pumps_in_selection: int | None = None


class InlineModel10Client(BaseModel):
    facebook: str | None = None
    google: str | None = None
    instagram: str | None = None
    linkedin: str | None = None
    pinterest: str | None = None
    twitter: str | None = None
    vk: str | None = None
    web: str | None = None


class PaymentSystemsRecClient(BaseModel):
    mobile_app: list[str] | None = None


class PhoneNumberClient(BaseModel):
    number: str | None = None


class CorporateCustomerInfoClient(BaseModel):
    corporate_user: InlineModel0Client | None = None
    id: str | None = None
    suspended: int | None = None


class AdditionalServiceCostValueClient(BaseModel):
    threshold: float
    value: float


class AdditionalServiceCostClient(BaseModel):
    currency: str | None = None
    decimal_digits: int | None = None
    min_value: float | None = None
    values: list[AdditionalServiceCostValueClient] | None = None


class AdditionalServiceClient(BaseModel):
    code: str | None = None
    cost: list[AdditionalServiceCostClient] | None = None
    default: int | None = None
    id: str | None = None
    public_description: str | None = None
    public_title: str | None = None
    sort_order: int | None = None
    type: str | None = None


class InlineModel2Client(BaseModel):
    account_available_amount: float | None = None
    account_day_limit: float | None = None
    account_day_limit_available: float | None = None
    account_day_limit_used: float | None = None
    account_month_limit: float | None = None
    account_month_limit_available: float | None = None
    account_month_limit_used: float | None = None
    user_day_limit: float | None = None
    user_day_limit_available: float | None = None
    user_day_limit_used: float | None = None
    user_month_limit: float | None = None
    user_month_limit_available: float | None = None
    user_month_limit_used: float | None = None


class AccountsRecClient(BaseModel):
    account_id: str | None = None
    amount: float | None = None
    available_amount: float | None = None
    balance: float | None = None
    credit_limit: float | None = None
    credit_type: str | None = None
    currency: str | None = None
    currency_name: str | None = None
    extended: int | None = None
    goods_items: list[str] | None = None
    id: str | None = None
    limits: InlineModel2Client | None = None
    odometer: str | None = None
    security_code: int | None = None
    title: str | None = None
    type: str | None = None
    unconfirmed_redeem_amount: float | None = None


class LoyaltyCategoryClient(BaseModel):
    id: int | None = None
    image_url: str | None = None
    title: str | None = None


class CorporateSpecialAccountAccessClient(BaseModel):
    available_amount: float | None = None
    corporate_customer_special_account: InlineModel1Client | None = None
    goods_items: list[str] | None = None
    id: str | None = None
    limits: InlineModel2Client | None = None
    odometer: str | None = None
    security_code: int | None = None
    vehicle_id: str | None = None


class CustomerAccessClient(BaseModel):
    access_type: str | None = None


class InlineModel7Client(BaseModel):
    facebook: str | None = None
    google: str | None = None
    instagram: str | None = None
    linkedin: str | None = None
    pinterest: str | None = None
    twitter: str | None = None
    vk: str | None = None
    web: str | None = None


class BonusesRecClient(BaseModel):
    bonuses_amount: float | None = None
    currency: str | None = None
    currency_name: str | None = None
    id: str | None = None
    permanent_bonuses_amount: float | None = None
    temporary_bonuses_amount: float | None = None
    title: str | None = None


class CardCategoryClient(BaseModel):
    id: str | None = None
    image: str | None = None
    title: str | None = None
    type: str | None = None


class CardRecordClient(BaseModel):
    code: str | None = None
    id: str | None = None
    photo: str | None = None
    scope: str | None = None
    title: str | None = None


class InlineModelClient(BaseModel):
    account_available_amount: float | None = None
    day_limit: float | None = None
    day_limit_available: float | None = None
    day_limit_used: float | None = None
    month_limit: float | None = None
    month_limit_available: float | None = None
    month_limit_used: float | None = None


class LoyaltyBonusesSettingsClient(BaseModel):
    bonus_payment_limit: float | None = None
    bonus_payment_settings_source: str | None = None
    bonus_payment_type: str | None = None
    unconfirmed_bonus_payment_amount: float | None = None
    unconfirmed_discount_amount: float | None = None


class OnlineStoreClient(BaseModel):
    active: int | None = None
    additional_customer_info: str | None = None
    cart_expire_days: int | None = None
    order_type: str | None = None
    payment_type: str | None = None
    pos_level_store: dict | None = None
    pos_level_stores_exists: int | None = None
    terminal_id: str | None = None
    title: str | None = None


class PosOnlineStoreClient(BaseModel):
    active: int | None = None
    cart_expire_days: int | None = None
    order_type: str | None = None
    payment_type: str | None = None
    terminal_id: str | None = None
    transaction_type: str | None = None


class CouponBatchClient(BaseModel):
    active: int | None = None
    business_code: str | None = None
    code: str | None = None
    customers_price_amount: float | None = None
    customers_price_currency: str | None = None
    customers_price_special_account_id: str | None = None
    customers_price_special_account_title: str | None = None
    customers_price_type: str | None = None
    description: str | None = None
    emission_period_begin: str | None = None
    emission_period_end: str | None = None
    external_code_visibility_type: str | None = None
    id: str | None = None
    image: str | None = None
    loyalty_id: str | None = None
    public_description: str | None = None
    public_title: str | None = None
    recommendation_fee_amount: float | None = None
    recommendation_fee_bonuses_currency: str | None = None
    recommendation_fee_coupon_batch_code: str | None = None
    recommendation_fee_coupon_batch_id: str | None = None
    recommendation_fee_coupon_batch_title: str | None = None
    recommendation_fee_special_account_id: str | None = None
    recommendation_fee_special_account_title: str | None = None
    recommendation_fee_type: str | None = None
    redeem_at_terminal: int | None = None
    share_allowed: int | None = None
    title: str | None = None
    type: str | None = None


class ThresholdsValuesClient(BaseModel):
    threshold: float
    value: float


class RefferalProgramLevelClient(BaseModel):
    level: int | None = None
    value: float | None = None
    value_depends_matrix: list[ThresholdsValuesClient] | None = None
    value_depends_type: str | None = None


class ReferralProgramClient(BaseModel):
    active: int | None = None
    description: str | None = None
    referral_reward_bonuses_currency: str | None = None
    referral_reward_coupon_batch: CouponBatch | None = None
    referral_reward_promotional_bonuses_expire_date: str | None = None
    referral_reward_promotional_bonuses_expire_type: str | None = None
    referral_reward_promotional_bonuses_expire_value: int | None = None
    referral_reward_promotional_bonuses_starting_date: str | None = None
    referral_reward_promotional_bonuses_starting_type: str | None = None
    referral_reward_promotional_bonuses_starting_value: int | None = None
    referral_reward_special_account: AccountsRecClient | None = None
    referral_reward_type: str | None = None
    referral_reward_value: float | None = None
    referrer_reward_grant_checks_number: int | None = None
    referrer_reward_grant_period_type: str | None = None
    referrer_reward_grant_period_value: int | None = None
    referrer_reward_grant_type: str | None = None
    referrer_reward_levels: list[RefferalProgramLevelClient] | None = None
    referrer_reward_promotional_bonuses_expire_date: str | None = None
    referrer_reward_promotional_bonuses_expire_type: str | None = None
    referrer_reward_promotional_bonuses_expire_value: int | None = None
    referrer_reward_promotional_bonuses_starting_date: str | None = None
    referrer_reward_promotional_bonuses_starting_type: str | None = None
    referrer_reward_promotional_bonuses_starting_value: int | None = None
    referrer_reward_special_account: AccountsRecClient | None = None
    referrer_reward_type: str | None = None
    referrer_reward_value: float | None = None
    referrer_reward_value_type: str | None = None
    title: str | None = None

    referral_title: str | None = None
    referral_description: str | None = None
    referral_logo: ImageObject | None = None
    referrer_title: str | None = None
    referrer_description: str | None = None
    referrer_logo: ImageObject | None = None


class PosClient(BaseModel):
    accounts: list[AccountsRecClient] | None = None
    additional_services: list[AdditionalServiceClient] | None = None
    additional_services_required: int | None = None
    addresses: str | None = None
    bonuses: list[BonusesRecClient] | None = None
    catalog_publication_state: str | None = None
    categories: list[LoyaltyCategoryClient] | None = None
    check_item_enter_mode: str | None = None
    corporate_special_accounts_access: (list[
                                            CorporateSpecialAccountAccessClient] |
                                        None) = None
    country: str | None = None
    coupons: list[InCustCouponSchema] | None = None
    description: str | None = None
    favorite: bool | None = None
    fuel: FuelRecClient | None = None
    id: str | None = None


class FeedbackSourceClient(BaseModel):
    allow_pictures: int | None = None
    allow_video: int | None = None
    allow_voice: int | None = None
    customer_actions: str | None = None
    id: str | None = None
    invitation_text: str | None = None
    loyalty: dict | None = None
    title: str | None = None


class CustomerFeedbackSettingsClient(BaseModel):
    active: int | None = None
    allow_attach_file_for_unauthorized_customer: int | None = None
    allow_rating_for_unauthorized_customer: int | None = None
    allow_review_for_unauthorized_customer: int | None = None
    allowed: str | None = None
    feedback_invitation_text: str | None = None
    feedback_per_customer_limit: int | None = None
    feedback_source: FeedbackSourceClient | None = None


class LoyaltySettingsClientSchema(BaseModel):
    customer_feedback_settings: CustomerFeedbackSettingsClient | None
    loyalty_bonuses_settings: LoyaltyBonusesSettingsClient | None
    online_store: OnlineStoreClient | None = None
    referral_program: ReferralProgramClient | None = None


class ReferrerUserClient(BaseModel):
    email: str | None = None
    external_id: str | None = None
    id: str | None = None
    name: str | None = None
    phone: str | None = None
