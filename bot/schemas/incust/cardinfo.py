from typing import Literal

from incust_client_api_client import Coupon
from pydantic import BaseModel

from .loyalty import ImageClient

YesNoPossible = Literal["yes", "no", "possible"]


class Bonus(BaseModel):
    bonuses_added: float | None = None
    bonuses_amount: float | None = None
    bonuses_redeemed: float | None = None
    currency: str | None = None
    currency_name: str | None = None
    permanent_bonuses_added: float | None = None
    permanent_bonuses_amount: float | None = None
    permanent_bonuses_redeemed: float | None = None
    promotion_bonuses_added: float | None = None
    promotion_bonuses_amount: float | None = None
    promotion_bonuses_redeemed: float | None = None
    temporary_bonuses_amount: float | None = None


class CorporateUser(BaseModel):
    email: str | None = None
    name: str | None = None
    status: bool = True  # True — active, False — suspended


class CorporateCustomer(BaseModel):
    corporate_user: CorporateUser | None = None
    id: str | None = None
    name: str | None = None
    suspended: bool = False


class CorporateCustomerSpecialAccountLimits(BaseModel):
    day_limit: int | None = None
    day_limit_used: int | None = None
    month_limit: int | None = None
    month_limit_used: int | None = None


class SpecialAccount(BaseModel):
    active: bool = True
    corporate: bool = False
    credit_limit: int | None = None
    credit_type: Literal["debit", "credit", "credit-unlimited"] = "debit"
    currency: str | None = None
    goods_items: list[str] | None | list = None
    id: str | None = None
    public_title: str | None = None
    title: str | None = None
    type: Literal["money", "goods", "punches"] = "money"
    unconfirmed_redeem_amount: int | None = None


class CorporateCustomerSpecialAccount(BaseModel):
    active: bool
    available_amount: float | None = None
    corporate_customer: CorporateCustomer | None = None
    credit_limit: int | None = None
    credit_type: str | None = None
    goods_items: list[str] | None | list = None
    id: str | None = None
    limits: CorporateCustomerSpecialAccountLimits | None = None
    special_account: SpecialAccount


class CorporateSpecialAccountAccessLimits(BaseModel):
    account_available_amount: float | None = None
    account_day_limit: int | None = None
    account_day_limit_available: int | None = None
    account_day_limit_used: int | None = None
    account_month_limit: int | None = None
    account_month_limit_available: int | None = None
    account_month_limit_used: int | None = None
    user_day_limit: int | None = None
    user_day_limit_available: int | None = None
    user_day_limit_used: int | None = None
    user_month_limit: int | None = None
    user_month_limit_available: int | None = None
    user_month_limit_used: int | None = None


class CorporateCustomerAccountsAccess(BaseModel):
    available_amount: float | None = None
    corporate_customer_special_account: CorporateCustomerSpecialAccount
    goods_items: list[str] | None | list = None
    id: str | None = None
    limits: CorporateSpecialAccountAccessLimits | None = None
    odometer: YesNoPossible = "no"
    security_code: bool = False
    vehicle_id: YesNoPossible = "no"


class CardCategory(BaseModel):
    id: str | None = None
    image: str | None = None
    show_to_customers: int | None = None
    title: str | None = None
    type: str | None = None
    weight: int | None = None


class Customer(BaseModel):
    access_type: str | None = None
    avatar: str | None = None
    card_category: CardCategory | None = None
    have_mobileapp_token: bool | None = None
    id: str | None = None
    last_sale_date: str | None = None
    new: int | None = None
    show_questionnaire: int | None = None
    user_birth_date: str | None = None
    user_consent_exist: bool | None = None
    user_created: str | None = None
    user_email: str | None = None
    user_external_id: str | None = None
    user_gender: str | None = None
    user_name: str | None = None
    user_phone: str | None = None
    user_status: str | None = None


class IdentificationItem(BaseModel):
    code: str | None = None
    type: str | None = None


class RateInfo(BaseModel):
    currency: str | None = None
    turnover: int | None = None


class CustomerSpecialAccountLimits(BaseModel):
    account_available_amount: float | None = None
    day_limit: int | None = None
    day_limit_available: int | None = None
    day_limit_used: int | None = None
    month_limit: int | None = None
    month_limit_available: int | None = None
    month_limit_used: int | None = None


class EWalletCustomerAccount(BaseModel):
    ewallet_id: int
    incust_account_id: str
    title: str
    currency: str
    invoice_template_id: int | None = None
    message: str | None = None
    is_user_special_account: bool | None = None
    is_user_can_topup: bool | None = None


class CustomerSpecialAccount(BaseModel):
    amount: int | None = None
    available_amount: float | None = None
    balance: int | None = None
    credit_limit: int | None = None
    credit_type: str | None = None
    customer_special_account_id: str | None = None
    extended: bool = False
    goods_items: list[str] | None | list = None
    id: str | None = None
    limits: CustomerSpecialAccountLimits | None = None
    odometer: YesNoPossible = "no"
    redeem_amount: int | None = None
    refill_amount: int | None = None
    security_code: bool = False
    special_account: SpecialAccount | None = None
    title: str | None = None
    type: str | None = None
    currency: str | None = None
    ewallet: EWalletCustomerAccount | None = None


class Account(BaseModel):
    amount: int | None = None
    id: str | None = None
    precision: str | None = None
    public_title: str | None = None


class Category(BaseModel):
    id: str | None = None
    public_title: str | None = None


class AccessTypeData(BaseModel):
    access_type: str


class IncustWallet(BaseModel):
    coupons: list[Coupon] | None = None
    bonuses: list[Bonus] | None = None
    accounts: list[CustomerSpecialAccount] | None = None
    description: str | None = None
    favorite: bool | None = None
    id: str | None = None
    title: str | None = None
    type: str | None = None
    photos: list[ImageClient] | None = None
