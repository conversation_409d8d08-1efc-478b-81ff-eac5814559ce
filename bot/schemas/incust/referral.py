from pydantic import BaseModel


class BonusesBenefits(BaseModel):
    amount: float | None = None
    currency: str | None = None
    currency_name: str | None = None


class AccountsBenefits(BaseModel):
    amount: float | None = None
    currency: str | None = None
    currency_name: str | None = None
    id: str | None = None
    title: str | None = None
    type: str | None = None


class CustomerBenefits(BaseModel):
    accounts: list[AccountsBenefits] | None = None
    bonuses: list[BonusesBenefits] | None = None


class ReferralProgramLevelSummary(BaseModel):
    confirmed_referrals_count: int | None = None
    level: int | None = None
    referrals_count: int | None = None
    rewards: CustomerBenefits | None = None


class ReferralProgramSummary(BaseModel):
    confirmed_referrals_count: int | None = None
    levels: list[ReferralProgramLevelSummary] | None = None
    loyalty_id: str | None = None
    referrals_count: int | None = None
    rewards: CustomerBenefits | None = None


class ReferralProgramChainNode(BaseModel):
    invitation_accepted: bool | None = None
    invitation_accepted_dt: str | None = None
    invitation_confirmed: bool | None = None
    invitation_confirmed_dt: str | None = None
    level: int | None = None
    referrals: list["ReferralProgramChainNode"] | None = None
    referrer_user_id: str | None = None
    rewards: CustomerBenefits | None = None
    type: str | None = None
    user_email: str | None = None
    user_external_id: str | None = None
    user_id: str | None = None
    user_name: str | None = None
    user_phone: str | None = None


class ReferralProgramChain(BaseModel):
    loyalty_id: str | None = None
    referrals: list[ReferralProgramChainNode] | None = None
