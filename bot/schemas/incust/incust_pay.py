from typing import Annotated, Literal

from incust_api.api import term
from incust_terminal_api_client import Transaction
from pydantic import BaseModel, Field

from schemas.admin.payments import IncustPayMerchantData
from incust_terminal_api_client.models import CorporateCustomerSpecialAccountAccess as CorporateCustomerAccountsAccess
from ..base import BaseORMModel, EnumWithValues


class IncustPayConfiguration(BaseORMModel):
    name: str | None = None
    card_payment_enabled: bool
    charge_fixed: float | int | None = None
    charge_percent: float | int | None = None
    payment_settings_id: int
    object_payment_settings_id: int | None = None


class IncustPayConfigurationPaymentObject(BaseORMModel):
    name: str | None = None
    server_api_url: str
    rules_type: Literal[
                    "by-all-rules", "by-charge-only-rules", "without-rules"] | None = \
        None
    terminal_title: str
    terminal_api_key: str
    terminal_server_api_url: str
    card_payment_enabled: bool
    payment_settings_id: int
    object_payment_settings_id: int | None = None
    charge_percent: float | int | None = None
    charge_fixed: float | int | None = None
    merchant_data: list[IncustPayMerchantData] | None = None
    ewallet_info_text: str | None = None


class BaseIncustPayData(BaseModel):
    incust_pay_configuration: IncustPayConfiguration | None = None


class IncustPayData(BaseIncustPayData):
    specials: list[term.m.CustomerSpecialAccount]
    corporate_special_accounts_access: list[CorporateCustomerAccountsAccess]
    total_accounts_count: int
    topup_card_enabled: bool | None = None


class GetIncustPayCardInfoData(BaseModel):
    payment_settings_id: int
    card_number: str
    object_payment_settings_id: int | None = None


class IncustPayDataErrorTypeEnum(EnumWithValues):
    TERMINAL_UNAUTH = "terminal_unauth"  # when request returns 401 status
    INCUST_ERROR = "incust_error"  # when unknown incust error occurred
    UNKNOWN_ERROR = "unknown_error"


class IncustPayPayData(BaseModel):
    payment_settings_id: int
    object_payment_settings_id: int | None = None
    # payment_token: str
    payment_type: Literal["special-account", "corporate-special-account-access"]
    payment_id: str
    order_id: Annotated[int | None, Field(nullable=True)] = None
    invoice_id: Annotated[int | None, Field(nullable=True)] = None

    store_id: Annotated[int | None, Field(nullable=True)] = None
    is_webview: Annotated[bool | None, Field(nullable=True)] = False
    comment: Annotated[str | None, Field(nullable=True)] = None

    pin: Annotated[str | None, Field(nullable=True)] = None
    odometer: Annotated[int | None, Field(nullable=True)] = None
    vehicle_id: Annotated[str | None, Field(nullable=True)] = None

    id_type: Literal["user", "card"]
    card_number: Annotated[str | None, Field(
        nullable=True,
        description="If id_type is 'card'. Else will be ignored"
    )] = None

    card_name: Annotated[str | None, Field(nullable=True)] = None
    account_name: Annotated[str | None, Field(nullable=True)] = None

    is_ewallet_payment: Annotated[bool | None, Field(nullable=True)] = None


class IncustPayDataError(BaseIncustPayData):
    error_type: IncustPayDataErrorTypeEnum
    error_detail: str | None = None


class IncustPayPaymentData(BaseModel):
    payment_token: str | None = None
    no_brand: bool = False
    no_incust_user: bool = False
    incust_pay_list: list[IncustPayData] = Field(default_factory=list)
    incust_pay_errors_list: list[IncustPayDataError] = Field(default_factory=list)
    incust_pay_count: int = 0
    ewallet_info_text: str | None = None


class IncustPayCardInfo(BaseModel):
    card_number: str
    is_error: bool = False
    incust_pay_data: IncustPayData | None = None
    incust_pay_data_error: IncustPayDataError | None = None


class IncustPaySuccess(BaseModel):
    ok: Literal[True]
    incust_transaction: Transaction | None = None
