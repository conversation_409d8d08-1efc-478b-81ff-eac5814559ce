from pydantic import BaseModel

from .base import CouponShowData


class IncustInfoData(BaseModel):
    percent: float
    discount_text: str
    bonuses_sum_text: str

    loyalty_discount_header: str
    loyalty_info_text: str

    sum_amount_text: str
    sum_to_pay_text: str

    payed_order_text: str
    invoice_payed_text: str
    invoice_payed_loyalty_message_to_user: str


class IncustCheckInfoData(BaseModel):
    discount: float | None
    sum_amount: float
    sum_to_pay: float | None

    bonuses_added: float | None
    bonuses_added_text: str | None
    added_bonuses_text: str | None

    coupons: list[CouponShowData] | None
    added_coupons_text: str | None
    added_coupons_count: int | None

    by_check_issued: str | None
    award_info_message: str | None

    specials: list | None
    special_text: str | None

    pre_check_text: str
    loyalty_awards_text: str
