from fastapi import Query
from pydantic import BaseModel
from pydantic.dataclasses import dataclass


@dataclass
class EwalletMerchantsListParams:
    offset: int | None = Query(None, nullable=True)
    limit: int = Query(10)
    search_text: str | None = Query(None, nullable=True)
    ewallet_id: int | None = Query(None, nullable=True)


class EWalletMerchantBase(BaseModel):
    name: str
    description: str | None = None
    ewallet_id: int
    qrcodes: list[str]
    incust_check_item_code: str | None = None
    incust_check_item_category: str | None = None
    is_pay_fee_self: bool = False


class EWalletMerchantCreate(EWalletMerchantBase):
    ...


class EWalletMerchantUpdate(EWalletMerchantBase):
    name: str | None = None
    description: str | None = None
    ewallet_id: int | None = None
    qrcodes: list[str] | None = None
    incust_check_item_code: str | None = None
    is_pay_fee_self: bool | None = None


class EWalletMerchant(EWalletMerchantBase):
    id: int

    class Config:
        orm_mode = True
