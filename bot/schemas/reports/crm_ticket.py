from datetime import datetime
from typing import Annotated

from pydantic import BaseModel, Field

from .brand import ReportsBrandSchema
from .group import ReportsGroupSchema
from .user import ReportsUserSchema
from ..crm_ticket import (
    CRMTicketSourceEnum, CRMTicketStatusEnum,
    CRMTicketStatusInitiatedByEnum,
)


class ReportsCRMTicketSchema(BaseModel):
    id: int
    source: Annotated[CRMTicketSourceEnum, Field()]
    status: Annotated[CRMTicketStatusEnum, Field()]
    change_date: Annotated[datetime, Field()]
    title: Annotated[str, Field()]
    group_id: Annotated[int, Field()]
    bot_id: Annotated[int | None, Field(nullable=True)] = None
    user_id: Annotated[int | None, Field(nullable=True)] = None
    # statuses: Annotated[List["models.CRMTicketStatus"], Field()]
    internal_comment: Annotated[str | None, Field(nullable=True)] = None


class ReportsCRMTicketStatusSchema(BaseModel):
    id: int
    status: Annotated[CRMTicketStatusEnum, Field()]
    initiated_by: Annotated[CRMTicketStatusInitiatedByEnum, Field()]
    initiated_by_user_id: Annotated[int | None, Field(nullable=True)] = None
    ticket_id: Annotated[int, Field()]
    header: Annotated[str | None, Field(nullable=True)] = None
    message: Annotated[str | None, Field(nullable=True)] = None
    internal_comment: Annotated[str | None, Field(nullable=True)] = None


class ReportsTicketRowsSchema(BaseModel):
    ticket: ReportsCRMTicketSchema
    ticket_status: ReportsCRMTicketStatusSchema | None = None
    user: ReportsUserSchema | None = None
    brand: ReportsBrandSchema | None = None
    group: ReportsGroupSchema | None = None
