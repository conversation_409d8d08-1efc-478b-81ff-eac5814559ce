from typing import Annotated

from pydantic import BaseModel, Field


class ReportsOrderPaymentSchema(BaseModel):
    comment: Annotated[str | None, Field(nullable=True)] = None
    price: float | None = None


class ReportsPaymentMethodSchema(BaseModel):
    id: int
    name: str | None = None
    description: Annotated[str | None, Field(nullable=True)] = None
    label_comment: Annotated[str | None, Field(nullable=True)] = None
    post_payment_info: Annotated[str | None, Field(nullable=True)] = None
