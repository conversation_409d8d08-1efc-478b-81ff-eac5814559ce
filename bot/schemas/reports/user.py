from datetime import datetime
from typing import Annotated

from pydantic import BaseModel, Field


class ReportsUserSchema(BaseModel):
    id: int
    uuid_token: Annotated[str | None, Field(nullable=True)] = None
    incust_external_id: Annotated[str | None, Field(nullable=True)] = None

    date_joined: datetime | None = None
    birth_date: Annotated[datetime | None, Field(nullable=True)] = None

    name: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    full_name: str | None = None
    is_guest_user: bool | None = None

    email: Annotated[str | None, Field(nullable=True)] = None
    chat_id: Annotated[int | None, Field(nullable=True)] = None
    username: Annotated[str | None, Field(nullable=True)] = None

    wa_phone: Annotated[int | None, Field(nullable=True)] = None
    wa_name: Annotated[str | None, Field(nullable=True)] = None

    photo_url: Annotated[str | None, Field(nullable=True)] = None
