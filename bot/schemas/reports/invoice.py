from datetime import datetime
from typing import Annotated, Any, Literal

from pydantic import BaseModel, Field

from .brand import ReportsBrandSchema
from .group import ReportsGroupSchema
from ..invoice import InvoiceTypeEnum


class ReportsInvoiceSchema(BaseModel):
    id: int
    status: Annotated[Literal["not_payed", "payed"], Field()]
    currency: Annotated[str | None, Field()] = None
    title: Annotated[str, Field()]
    description: Annotated[str, Field()]
    photo: Annotated[str | None, Field(nullable=True)] = None
    first_name: Annotated[str | None, Field(nullable=True)] = None
    last_name: Annotated[str | None, Field(nullable=True)] = None
    email: Annotated[str | None, Field(nullable=True)] = None
    phone: Annotated[str | None, Field(nullable=True)] = None
    incust_check: Annotated[dict | None, Field(nullable=True)] = None
    shipment_cost: Annotated[float | None, Field()] = None
    custom_payment_cost: Annotated[float | None, Field()] = None
    before_loyalty_sum: Annotated[float | None, Field()] = 0
    discount: Annotated[float | None, Field()] = 0
    bonuses_redeemed: Annotated[float | None, Field()] = 0
    discount_and_bonuses_redeemed: Annotated[float | None, Field()] = 0
    total_sum: Annotated[float | None, Field()] = 0
    tips_sum: Annotated[float | None, Field()] = 0
    sum_to_pay: Annotated[float | None, Field()] = 0
    payer_fee: Annotated[float | None, Field()] = 0
    paid_sum: Annotated[float | None, Field()] = 0
    external_transaction_id: Annotated[str | None, Field(nullable=True)] = None
    client_redirect_url: Annotated[str | None, Field(nullable=True)] = None
    webhook_result: Annotated[dict | None, Field(nullable=True)] = None
    user_id: Annotated[int | None, Field()] = None
    payer_id: Annotated[int | None, Field(nullable=True)] = None
    is_friend: Annotated[bool | None, Field()] = None
    user_comment: Annotated[str | None, Field(nullable=True)] = None
    time_created: Annotated[datetime | None, Field()] = None
    invoice_type: Annotated[InvoiceTypeEnum | None, Field()] = None
    total_sum_with_extra_fee: Annotated[float | None, Field()] = 0
    extra_params: Annotated[dict[Any, Any] | None, Field(nullable=True)] = None
    utm_labels: Annotated[dict[str, Any] | None, Field(nullable=True)] = None


class ReportsInvoiceItemSchema(BaseModel):
    id: Annotated[int, Field()]
    name: Annotated[str, Field(nullable=True)] = "uncategorized"
    quantity: Annotated[int, Field(gt=0)] = 1
    item_code: Annotated[str | None, Field(nullable=True)] = None
    price: Annotated[float, Field()]
    unit_discount: Annotated[float, Field()] = 0
    unit_bonuses_redeemed: Annotated[float, Field()] = 0
    unit_discount_and_bonuses_redeemed: Annotated[float, Field()] = 0
    final_price: Annotated[float | None, Field()] = 0
    before_loyalty_sum: Annotated[float | None, Field()] = 0
    discount: Annotated[float | None, Field()] = 0
    bonuses_redeemed: Annotated[float | None, Field()] = 0
    discount_and_bonuses_redeemed: Annotated[float, Field()] = 0
    final_sum: Annotated[float | None, Field()] = 0


class ReportsTransactionRowsSchema(BaseModel):
    invoice: ReportsInvoiceSchema
    invoice_item: ReportsInvoiceItemSchema | None
    brand: ReportsBrandSchema | None = None
    group: ReportsGroupSchema | None = None
