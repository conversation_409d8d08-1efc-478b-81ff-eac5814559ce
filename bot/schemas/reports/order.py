from datetime import datetime
from typing import Annotated

from pydantic import BaseModel, Field

from .brand import ReportsBrandSchema
from .group import ReportsGroupSchema
from .menu_in_store import ReportsMenuInStoreSchema
from .payment import ReportsOrderPaymentSchema, ReportsPaymentMethodSchema
from .store import ReportsStoreSchema
from .user import ReportsUserSchema
from ..store.order import OrderType
from ..store.product import ProductTypeLiteral
from ..store.shipping import ShipmentBaseTypeLiteral


class ReportsOrderProductSchema(BaseModel):
    id: int
    quantity: int | None = None

    price: float | None = None
    price_with_attributes: float | None = None
    final_price: float | None = None
    discount_amount: float | None = None
    discount_sum: float | None = None
    bonuses_redeemed: float | None = None
    bonuses_redeemed_sum: float | None = None
    discount_and_bonuses: float | None = None
    discount_and_bonuses_sum: float | None = None
    price_after_loyalty: float | None = None
    total_sum: float | None = None

    incust_account: Annotated[dict | None, Field(nullable=True)] = None
    incust_card: Annotated[str | None, Field(nullable=True)] = None

    topup_charge: float | None = None
    charge_percent: float | None = None
    charge_fixed: float | None = None
    is_topup_error: bool | None = None


class ReportsProductSchema(BaseModel):
    id: int
    product_id: str | None = None
    name: str | None = None
    description: Annotated[str | None, Field(nullable=True)] = None
    type: ProductTypeLiteral | None = None


class ReportsOrderShipmentSchema(BaseModel):
    price: float | None = None
    is_paid_separately: bool | None = None


class ReportsDeliveryMethodSchema(BaseModel):
    id: int
    name: str | None = None
    base_type: ShipmentBaseTypeLiteral | None = None
    description: Annotated[str | None, Field(nullable=True)] = None


class ReportsOrderSchema(BaseModel):
    id: int
    type: OrderType
    currency: str
    create_date: datetime | None = None

    status: str | None = None
    status_pay: str | None = None

    first_name: Annotated[str | None, Field(nullable=True)] = None
    last_name: Annotated[str | None, Field(nullable=True)] = None
    phone: Annotated[str | None, Field(nullable=True)] = None
    email: Annotated[str | None, Field(nullable=True)] = None

    delivery_address: Annotated[str | None, Field(nullable=True)] = None

    address_coordinates: Annotated[str | None, Field(nullable=True)] = None
    address_place_id: Annotated[str | None, Field(nullable=True)] = None
    map_link: Annotated[str | None, Field(nullable=True)] = None

    address_comment: Annotated[str | None, Field(nullable=True)] = None

    desired_delivery_date: Annotated[datetime | None, Field(nullable=True)] = None
    comment: Annotated[str | None, Field(nullable=True)] = None

    before_loyalty_sum: float | None = None
    total_sum: float | None = None
    tips_sum: float | None = None

    bonuses_redeemed: float | None = None
    discount: float | None = None
    discount_and_bonuses: float | None = None

    total_sum_with_extra_fee: float | None = None
    sum_to_pay: float | None = None

    payer_fee: float | None = None
    paid_sum: float | None = None

    date_sent_to_friend: Annotated[datetime | None, Field(nullable=True)] = None

    utm_labels: Annotated[dict | None, Field(nullable=True)] = None


class ReportsOrderRowSchema(BaseModel):
    order: ReportsOrderSchema
    store: ReportsStoreSchema | None = None
    brand: ReportsBrandSchema | None = None
    group: ReportsGroupSchema | None = None

    user: ReportsUserSchema | None = None

    menu_in_store: ReportsMenuInStoreSchema | None = None

    shipment: ReportsOrderShipmentSchema | None = None
    delivery_method: ReportsDeliveryMethodSchema | None = None

    payment: ReportsOrderPaymentSchema | None = None
    payment_method: ReportsPaymentMethodSchema | None = None


class ReportsOrderPositionsRowSchema(ReportsOrderRowSchema):
    order_product: ReportsOrderProductSchema | None = None
    product: ReportsProductSchema | None = None
