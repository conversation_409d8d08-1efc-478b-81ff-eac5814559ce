from datetime import datetime
from typing import Annotated

from pydantic import BaseModel, Field

from schemas import (
    MailingChannelTypeEnum, MailingMessageStatusEnum, MailingStatusEnum,

)
from .brand import ReportsBrandSchema
from .group import ReportsGroupSchema


class ReportsMailingMessageSchema(BaseModel):
    id: int
    email: Annotated[str | None, Field(nullable=True)] = None
    phone: Annotated[str | None, Field(nullable=True)] = None
    chat_id: Annotated[int | None, Field(nullable=True)] = None
    user_name: Annotated[str | None, Field(nullable=True)] = None
    message: Annotated[dict | None, Field(nullable=True)] = None
    created_datetime: datetime | None = Field(
        None, description="Datetime when the message was created"
    )
    start_datetime: datetime | None = Field(
        None, description="Datetime when the message was sent"
    )
    end_datetime: datetime | None = Field(
        None, description="Datetime when the message was delivered"
    )
    error_details: Annotated[dict | None, Field(nullable=True)] = None
    retry_info: Annotated[dict | None, Field(nullable=True)] = None
    channel_type: MailingChannelTypeEnum
    channel_name: str
    status: MailingMessageStatusEnum | None = None
    lang: Annotated[str | None, Field(nullable=True)] = None
    mailing_id: Annotated[int | None, Field(nullable=True)] = None
    bot_id: Annotated[int | None, Field(nullable=True)] = None
    user_id: Annotated[int | None, Field(nullable=True)] = None


class ReportsMailingSchema(BaseModel):
    id: int
    status: MailingStatusEnum
    channels: Annotated[list[str] | None, Field(nullable=True)] = None
    last_sent_datetime: datetime | None = Field(
        None, description="Last time the mailing was sent"
    )
    description: Annotated[str | None, Field(nullable=True)] = None
    name: str
    sent_info: Annotated[dict | None, Field(nullable=True)] = None
    message_info: Annotated[dict | None, Field(nullable=True)] = None
    is_test: bool | None = Field(
        None, description="Whether the mailing is a test mailing"
    )
    message_source_text: Annotated[dict | None, Field(nullable=True)] = None
    email_media_as_attachment: bool = Field(
        default=True, description="Whether to send email media as attachment"
    )


class ReportsMailingRowsSchema(BaseModel):
    mailing_message: ReportsMailingMessageSchema
    mailing: ReportsMailingSchema
    brand: ReportsBrandSchema | None = None
    group: ReportsGroupSchema | None = None
