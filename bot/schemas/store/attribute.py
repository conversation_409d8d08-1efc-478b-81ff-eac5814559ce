from enum import Enum

from schemas.base import BaseORMModel


class AttributeSchema(BaseORMModel):
    id: int
    attribute_id: str
    attribute_group_id: int
    name: str
    selected_by_default: bool
    price_impact: float
    is_available: bool
    external_id: str | None = None
    external_type: str | None = None
    min: int | None = None
    max: int | None = None
    is_deleted: bool
    brand_id: int


class AttributeGroupSchema(BaseORMModel):
    id: int
    attribute_group_id: str
    name: str
    external_id: str | None = None
    external_type: str | None = None
    min: int
    max: int | None = None
    is_deleted: bool
    attributes: list[AttributeSchema]
    brand_id: int
    position: int | None = None


class AttributeGroupSortEnum(Enum):
    FOR_EXPORT = "for_export"
    FOR_API = "for_api"
