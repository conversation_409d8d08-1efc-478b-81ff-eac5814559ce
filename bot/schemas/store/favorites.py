from typing import Annotated

from pydantic import BaseModel, Field

from schemas.base import BaseORMModel
from .product import ProductSchema
from ..auth import Token


class FavoriteProductSchema(BaseORMModel):
    id: int
    product: ProductSchema
    token: Annotated[Token | None, Field(nullable=True)] = None


class FavoritesProductDeletedResult(BaseModel):
    product_id: int
    token: Annotated[Token | None, Field(nullable=True)] = None


class ToggleFavoriteProductResult(BaseModel):
    is_deleted: bool
    favorite_product: Annotated[
        FavoriteProductSchema | None,
        Field(nullable=True)
    ] = None
    token: Annotated[Token | None, Field(nullable=True)] = None


class FavoritesSchema(BaseORMModel):
    id: int
    favorite_products: list[FavoriteProductSchema]
    token: Annotated[Token | None, Field(nullable=True)] = None


class AddFavoritesSchema(BaseModel):
    products_ids: list[int]


class CreatedFavoritesSchema(BaseModel):
    token: str
    token_type: str
    favorites: FavoritesSchema


class SyncFavoritesSchema(BaseModel):
    anon_favorites_token: str | None = None
