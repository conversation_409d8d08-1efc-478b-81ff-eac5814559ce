from datetime import time

from pydantic import BaseModel

from schemas.base import BaseORMModel
from .billing import BillingSettings


class StoreCustomFieldSchema(BaseORMModel):
    id: int
    name: str
    value: str | None


class BannerSchema(BaseORMModel):
    url: str | None = None
    image_path: str | None = None
    is_visible: bool = True
    position: int | None = None


class WorkingSlotSchema(BaseORMModel):
    id: int | None = None
    start_time_text: str | None = None
    end_time_text: str | None = None
    start_time: time
    end_time: time


class WorkingDaySchema(BaseORMModel):
    id: int | None = None
    day: str
    is_weekend: bool
    slots: list[WorkingSlotSchema] | None = None


class StoreSchema(BaseORMModel):
    id: int
    brand_id: int
    external_id: str | None = None
    external_type: str | None = None

    name: str
    description: str | None = None
    ai_description: str | None = None
    image_url: str | None = None

    get_order_id: int | None = None

    latitude: str | None = None
    longitude: str | None = None
    distance: int | None = None
    is_swap_coordinates: bool | None = True
    polygon: dict | None = None

    currency: str

    organisation_id: int | None = None
    custom_fields: list[StoreCustomFieldSchema]
    banners: list[BannerSchema] | None = None

    billing_settings: BillingSettings | None = None

    is_offer_doc_exist: bool = False
    is_about_doc_exist: bool = False

    working_days: list[WorkingDaySchema] | None = None

    has_categories: bool = True
    is_enabled_emenu: bool = False


class NotAvailableProduct(BaseModel):
    product_id: int
    name: str


class StorePayloadBaseSchema(BaseModel):
    store_id: int | None = None
