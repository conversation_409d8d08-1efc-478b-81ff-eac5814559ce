from enum import Enum
from typing import Annotated, Literal, <PERSON><PERSON><PERSON><PERSON>

from fastapi import Query
from pydantic import BaseModel, Field, Json
from pydantic.dataclasses import dataclass

from schemas.base import BaseORMModel
from schemas.payment_settings.liqpay import UnitName
from .attribute import AttributeGroupSchema
from .characteristic import CharacteristicFilterType
from ..db import Cursor
from ..gallery import Gallery
from ..payment_settings.liqpay.schemas import TaxList


@dataclass
class BaseProductsAvailabilityCursor(Cursor):
    is_available: bool


@dataclass
class ProductsCategoriesCursor(BaseProductsAvailabilityCursor):
    is_other: bool
    category_position: int | None
    product_position: int | None


@dataclass
class ProductPriceCursor(BaseProductsAvailabilityCursor):
    price: float | None
    product_position: int | None


ProductsListCursor: TypeAlias = ProductsCategoriesCursor | ProductPriceCursor


class ProductsSortEnum(Enum):
    CATEGORIES = "categories"
    LOW_PRICE = "low_price"
    HIGH_PRICE = "high_price"
    DEFAULT = "default"


class ProductListFilterData(BaseModel):
    characteristic_id: int
    filter_type: CharacteristicFilterType

    value: Annotated[str | None, Field(nullable=True)] = None
    values_list: Annotated[list[str] | None, Field(nullable=True)] = None

    range_min: Annotated[float | None, Field(nullable=True)]
    range_max: Annotated[float | None, Field(nullable=True)]


class ProductListFiltersData(BaseModel):
    filters: list[ProductListFilterData] = Field(default_factory=list)

    min_price: Annotated[float | None, Field(nullable=True)] = None
    max_price: Annotated[float | None, Field(nullable=True)] = None


class ProductsListParams(BaseModel):
    brand_id: int | None = Query(None, nullable=True)
    cursor: str | None = Query(None, nullable=True)
    sort: ProductsSortEnum = Query()
    category_id: int | None = Query(None, nullable=True)
    filters_set_id: int | None = Query(None, nullable=True)
    search_text: str | None = Query(None, nullable=True)
    limit: int = Query(10, gt=10, le=100)
    is_export: bool | None = Query(None, nullable=True)
    filters: Json[ProductListFiltersData] | None = Query(None, nullable=True)


class ProductModifierOption(BaseModel):
    is_available: bool
    value: str
    orig_value: str


class ProductModifierSchema(BaseModel):
    id: int
    name: str
    value: str
    orig_value: str
    options: list[ProductModifierOption]


class FloatingSumSettings(BaseModel):
    is_enabled: bool
    min: float | None = None
    max: float | None = None
    options: list[float] | None = None
    user_sum_enabled: bool


ProductTypeLiteral = Literal["goods", "info", "gift", "service", "topup"]


class ProductListCategorySchema(BaseORMModel):
    id: int
    position: int | None = None
    father_category_id: int | None = None


class ProductSchema(BaseORMModel):
    id: int
    is_available: bool
    external_id: str | None = None
    external_type: str | None = None

    product_id: str
    get_order_id: int | None = None

    name: str
    description: str | None
    image_url: str | None
    thumbnail_url: str | None

    gallery: Gallery | None

    price: float | int
    old_price: float | int = 0

    is_weight: bool | None = False
    unit: str | None = None

    buy_min_quantity: int
    position: int | None = None
    brand_id: int
    categories: list[int] | None = None

    attribute_groups: list[AttributeGroupSchema] | None = None

    product_group_id: int | None = None

    modifiers: dict[int, ProductModifierSchema]
    characteristics: dict[str, str]

    floating_sum_settings: FloatingSumSettings | None = None
    floating_qty_enabled: bool = False

    pti_info_text: str | None = None
    pti_info_link: str | None = None
    type: ProductTypeLiteral = "goods"
    need_auth: bool = False

    liqpay_id: str | None = None
    liqpay_unit_name: UnitName | None = None
    liqpay_codifier: str | None = None
    liqpay_tax_list: TaxList | None = None

    topup_account_id: str | None = None

    list_category: Annotated[ProductListCategorySchema | None, Field(
        nullable=True,
        description=(
            "Category in list. "
            "Product can be few times in list with different categories"
        )
    )] = None


class ProductsListResponse(BaseModel):
    data: list[ProductSchema]
    cursor: Annotated[str | None, Field(nullable=True)] = None


class ProductsMinMaxPrices(BaseModel):
    min: float
    max: float


class FindProductModificationData(BaseModel):
    user_id: int | None = None
    find_for_characteristic_id: int | None
    filters: dict[int, str] = Field(default_factory=dict)


class GetMinMaxPricesData(BaseModel):
    category_id: int | None = Query(None, nullable=True)
    filters_set_id: int | None = Query(None, nullable=True)
    filters: Json[ProductListFiltersData] | None = Query(None, nullable=True)
