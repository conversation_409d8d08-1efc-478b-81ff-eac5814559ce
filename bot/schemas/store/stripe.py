from pydantic import BaseModel


class StripeBillingAddress(BaseModel):
    city: str | None = None
    country: str | None = None
    line1: str | None = None
    line2: str | None = None
    postal_code: str | None = None
    state: str | None = None


class StripeCustomerDetails(BaseModel):
    address: StripeBillingAddress | None = None
    email: str | None = None
    name: str | None = None
    phone: str | None = None
    tax_exempt: str | None = None
    tax_ids: list[str] | None = None


class StripeCustomText(BaseModel):
    after_submit: str | None = None
    shipping_address: str | None = None
    submit: str | None = None
    terms_of_service_acceptance: str | None = None


class PaymentMethodConfigurationDetails(BaseModel):
    id: str
    parent: str | None = None


class StripeCheckoutSessionObject(BaseModel):
    id: str
    object: str
    after_expiration: str | None = None
    allow_promotion_codes: str | None = None
    amount_subtotal: int
    amount_total: int
    automatic_tax: dict
    billing_address_collection: str | None = None
    cancel_url: str
    client_reference_id: str
    client_secret: str | None = None
    consent: str | None = None
    consent_collection: str | None = None
    created: int
    currency: str
    currency_conversion: str | None = None
    custom_fields: list[str]
    custom_text: StripeCustomText
    customer: str | None = None
    customer_creation: str
    customer_details: StripeCustomerDetails | None = None
    customer_email: str | None = None
    expires_at: int
    invoice: str | None = None
    invoice_creation: dict | None = None
    livemode: bool
    locale: str
    metadata: dict
    mode: str
    payment_intent: str | None = None
    payment_link: str | None = None
    payment_method_collection: str
    payment_method_configuration_details: str | dict | None = None
    payment_method_options: dict
    payment_method_types: list[str]
    payment_status: str
    phone_number_collection: dict
    recovered_from: str | None = None
    setup_intent: str | None = None
    shipping_address_collection: str | None = None
    shipping_cost: str | None = None
    shipping_details: str | None = None
    shipping_options: list[str]
    status: str
    submit_type: str | None = None
    subscription: str | None = None
    success_url: str
    total_details: dict
    ui_mode: str
    url: str | None = None
