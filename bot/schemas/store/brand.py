from enum import unique
from typing import Annotated, Literal

from pydantic import BaseModel, Field

from schemas.loyalty_settings import LoyaltySettingsSchema
from schemas.all import LoyaltyInfo
from schemas.base import BaseORMModel, EnumWithValues
from schemas.incust import IncustData
from schemas.store.types import DeliveryTimeTypeLiteral

ViewType = Literal["default_grid", "default_list", "only_grid", "only_list"]


class MobileCartButtonMode(EnumWithValues):
    BOTTOM = "bottom"
    BAR = "bar"


class OrderFieldEnum(EnumWithValues):
    EMAIL = "email"
    PHONE = "phone"
    ORDER_NAME = "order_name"


class OrderFieldSettingEnum(EnumWithValues):
    REQUIRED = "required"
    OPTIONAL = "optional"
    DISABLED = "disabled"


class OrderNameSettingEnum(EnumWithValues):
    FIRST_AND_LAST = "first_and_last"
    ONLY_FIRST = "only_first_name"
    OPTIONAL = "optional"


class ConsentModeEnum(EnumWithValues):
    PER_ORDER = "per_order"
    ONE_TIME = "one_time"
    INFO = "info"


class OrderClientFieldsSettings(BaseModel):
    class Config:
        use_enum_values = True

    email: OrderFieldSettingEnum
    phone: OrderFieldSettingEnum
    order_name_mode: OrderNameSettingEnum


class OrderFieldsSettings(BaseModel):
    class Config:
        use_enum_values = True

    web: OrderClientFieldsSettings
    messanger: OrderClientFieldsSettings
    order_comment_mode: OrderFieldSettingEnum


ScanReceiptCountry = Literal["kz", "uz", "sk"]


class BrandScanReceiptsSettings(BaseModel):
    enabled: bool | None = Field(
        None, description="Whether or not to enable scan receipts"
    )
    country: ScanReceiptCountry | None = Field(
        None, description="The country to scan receipts"
    )
    bin_codes: list[str] | None = Field(
        None, description="The bin_codes to scan receipts"
    )
    demo_mode: bool | None = Field(
        False, description="Whether or not to enable demo mode"
    )
    enabled_all_rules: bool = Field(
        False, description="Enable loyalty rules for all transaction"
    )


class BaseAuthSettings(BaseORMModel):
    is_auth_email_enabled: bool = True
    is_auth_messanger_enabled: bool = True
    is_auth_google_enabled: bool = True
    is_auth_apple_enabled: bool = True
    is_auth_for_orders_enabled: bool = False


class AuthSettings(BaseAuthSettings):
    ...


class BrandSchema(BaseORMModel):
    class Config:
        use_enum_values = True

    id: int
    name: str

    image_url: Annotated[str | None, Field(nullable=True)] = None
    logo_url: Annotated[str | None, Field(nullable=True)] = None

    is_get_order: bool
    group_id: int
    bot_id: Annotated[int | None, Field(nullable=True)] = None
    bot_name: Annotated[str | None, Field(nullable=True)] = None
    default_lang: str
    country_code: Annotated[str | None, Field(nullable=True)] = None
    is_translate: bool = False

    image_height: Annotated[int | None, Field(nullable=True)] = None
    image_aspect_ratio: Annotated[
        tuple[float | int, float | int] | None,
        Field(nullable=True)
    ] = None

    is_offer_doc_exist: bool = False
    is_about_doc_exist: bool = False

    delivery_datetime_mode: DeliveryTimeTypeLiteral = "datetime"

    delivery_time_warning_enabled: Annotated[bool | None, Field(nullable=True)] = None
    incust_data: Annotated[IncustData | None, Field(nullable=True)] = None
    loyalty_info: Annotated[LoyaltyInfo | None, Field(nullable=True)] = None
    default_currency: Annotated[str | None, Field(nullable=True)] = None

    stores_count: int
    single_store_id: Annotated[int | None, Field(nullable=True)] = None

    desktop_view: ViewType
    mobile_view: ViewType

    mobile_cart_button_mode: MobileCartButtonMode

    order_fields_settings: OrderFieldsSettings

    footer_sign: str
    terms_of_use_link: str
    privacy_policy_link: str

    is_categories_count_view: bool = True
    is_friend_payment: Annotated[bool | None, Field(nullable=True)] = False

    google_maps_public_api_key: Annotated[str | None, Field(nullable=True)] = None
    is_enabled_google_maps_api: bool

    is_enabled_ai: bool
    transactions_activated: bool

    scan_receipts_settings: Annotated[
        BrandScanReceiptsSettings | None,
        Field(nullable=True),
    ] = None
    consent_mode: Annotated[ConsentModeEnum | None, Field(nullable=True)] = None

    loyalty_settings: Annotated[
        LoyaltySettingsSchema | None,
        Field(nullable=True),
    ] = None


class DetectedBrandSchema(BaseModel):
    brand: BrandSchema
    is_brand_domain: bool


@unique
class ExternalSystemTypes(EnumWithValues):
    poster = 'poster'
    get_order = 'get_order'
    excel = 'excel'


@unique
class ExtSysSetTypes(EnumWithValues):
    poster_api_token = 'poster_api_token'
    poster_auto_update = 'poster_auto_update'
    poster_skip_stores = 'poster_skip_stores'
    poster_skip_desc_product = 'poster_skip_desc_product'
    poster_regex_to_skip_category = 'poster_regex_to_skip_category'
    POSTER_APP_SECRET = 'poster_app_secret'
    poster_tips_sku = 'poster_tips_sku'
    delivery_datetime_mode = 'delivery_datetime_mode'
    delivery_time_warning_enabled = 'delivery_time_warning_enabled'
    wave_is_have_merchants = 'wave_is_have_merchants'
    scan_receipts_enabled = 'scan_receipts_enabled'
    scan_receipts_country = 'scan_receipts_country'
    scan_receipts_bin_codes = 'scan_receipts_bin_codes'
    scan_receipts_demo_mode = 'scan_receipts_demo_mode'
    scan_receipts_enabled_all_rules = 'scan_receipts_enabled_all_rules'
    incust_loyalty_id = 'incust_loyalty_id'
    incust_white_label_id = 'incust_white_label_id'
    incust_client_token = 'incust_client_id'
    incust_term_api = 'incust_term_api'
    incust_server_api = 'incust_server_api'
    incust_client_url = 'incust_client_url'
    incust_type_client_auth = 'incust_type_client_auth'
    incust_redeem_type = 'incust_redeem_type'
    incust_prohibit_redeeming_bonuses = 'incust_prohibit_redeeming_bonuses'
    incust_prohibit_redeeming_coupons = 'incust_prohibit_redeeming_coupons'
    incust_loyalty_applicable_type = 'incust_loyalty_applicable_type'
    incust_stores_import = 'incust_stores_import'
    incust_terminal_id = 'incust_terminal_id'


@unique
class PmtExtTypes(EnumWithValues):
    liqpay = 'liqpay'
    stripe = 'stripe'


@unique
class LoyaltyApplicableTypes(EnumWithValues):
    for_all = 'for_all'
    for_participants = 'for_participants'
