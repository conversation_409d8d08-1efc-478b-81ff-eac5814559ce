from typing import Annotated

from pydantic import BaseModel, Field

from ..admin.base import AdminConnectedObjectSchema
from ..base import BaseORMModel, EnumWithValues


class ClientWebPageTypeEnum(EnumWithValues):
    MENU = "menu"
    STORES = "stores"
    FASTPAY = "fastpay"
    CUSTOM = "custom"
    REVIEW = "review"
    AGREEMENT = "agreement"
    ABOUT = "about"


class ClientWebPageContainerMaxWidthEnum(EnumWithValues):
    xs = "xs"
    sm = "sm"
    md = "md"
    lg = "lg"
    xl = "xl"
    custom = "custom"


class BaseAdminClientWebPageSchema(BaseORMModel):
    id: int
    type: ClientWebPageTypeEnum
    container_max_width: ClientWebPageContainerMaxWidthEnum
    custom_container_max_width: int | None = Field(None, nullable=True)
    title: Annotated[str | None, Field(nullable=True)] = None
    button_title: Annotated[str | None, Field(nullable=True)] = None
    is_enabled: bool = False
    show_in_profile: bool = False
    show_in_navbar: bool = False
    position: int
    slug: str
    internal_name: Annotated[str | None, Field(nullable=True)] = None
    profile_id: Annotated[int | None, Field(nullable=True)] = None


class ClientWebPageOneSchema(BaseAdminClientWebPageSchema):
    page_content: dict | None = Field(None, nullable=True)
    show_in_profile: bool
    stores: list[int]
    invoice_templates: list[int]


class ClientWebPageListSchema(BaseAdminClientWebPageSchema):
    pass


class AdminClientWebPageListSchema(BaseAdminClientWebPageSchema):
    profile_id: int


class AdminConnectedInvoiceTemplateSchema(BaseORMModel):
    id: int
    title: str
    read_allowed: bool = False
    edit_allowed: bool = False


class AdminClientWebPageAdditionalSchema(BaseORMModel):
    page_content: Annotated[dict | None, Field(nullable=True)] = None
    stores: Annotated[
        list[AdminConnectedObjectSchema],
        Field(
            default_factory=list,
            description=(
                "List of connected stores. "
                "Here will be only stores current user"
            )
        )
    ]
    invoice_templates: Annotated[
        list[AdminConnectedInvoiceTemplateSchema],
        Field(
            default_factory=list,
            description=(
                "List of connected invoice templates. "
                "Here will be only invoice templates current user "
            )
        )
    ]


class AdminClientWebPageTranslationSchema(BaseORMModel):
    title: str | None = Field(None, nullable=True)
    button_title: str | None = Field(None, nullable=True)
    page_content: dict | None = Field(None, nullable=True)


class AdminClientWebPageOneSchema(
    BaseAdminClientWebPageSchema, AdminClientWebPageAdditionalSchema
):
    profile_id: int
    raw_internal_name: Annotated[str | None, Field(nullable=True)] = None
    translations: Annotated[
        dict[str, AdminClientWebPageTranslationSchema] | None,
        Field(nullable=True),
    ] = None


class AdminClientWebPageCreateData(BaseModel):
    title: Annotated[str | None, Field(nullable=True)] = None
    button_title: Annotated[str | None, Field(nullable=True)] = None
    is_enabled: bool = True
    show_in_profile: bool = False
    show_in_navbar: bool = False
    slug: str = Field(description="Unique slug for the page", min_length=1)
    type: ClientWebPageTypeEnum
    container_max_width: ClientWebPageContainerMaxWidthEnum
    custom_container_max_width: int | None = Field(None, nullable=True)
    page_content: Annotated[dict | None, Field(nullable=True)] = None
    raw_internal_name: Annotated[str | None, Field(nullable=True)] = None
    stores: Annotated[list[int] | None, Field(nullable=True)] = None
    invoice_templates: Annotated[list[int] | None, Field(nullable=True)] = None
    translations: Annotated[
        dict[str, AdminClientWebPageTranslationSchema] | None,
        Field(nullable=True),
    ] = None


class AdminClientWebPageUpdateData(AdminClientWebPageCreateData):
    pass
