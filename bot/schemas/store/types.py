from typing import Literal

from schemas.base import EnumWithValues


class CustomType(EnumWithValues):
    SHIPMENT = "shipment"
    CUSTOM_SHIPMENT = "custom_shipment"
    CUSTOM_SHIPMENT_GROUP = "custom_shipment_group"
    CUSTOM_PAYMENT = "custom_payment"


CustomTypeLiteral = Literal[
    "shipment", "custom_shipment", "custom_shipment_group"
]


class ShipmentType(EnumWithValues):
    PICKUP = "pickup"
    DELIVERY = "delivery"
    IN_STORE = "in_store"
    NO_DELIVERY = "no_delivery"


ShipmentBaseTypeLiteral = Literal["delivery", "pickup", "in_store", "no_delivery"]


class ShipmentParentType(EnumWithValues):
    BRAND = "brand"
    STORE = "store"


ShipmentParentTypeLiteral = Literal["brand", "store"]


class PaymentType(EnumWithValues):
    ONLINE = "online"
    IN_STORE = "in_store"
    FRIEND = "friend"


PaymentTypeLiteral = Literal["online", "in_store", "friend"]

DeliveryTimeTypeLiteral = Literal["datetime", "date", "disabled"]
