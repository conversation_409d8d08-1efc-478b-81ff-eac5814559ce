from pydantic import BaseModel, Field

from ..notification import TextNotificationTargetEnum, TextNotificationTypeEnum


class SendTextNotificationData(BaseModel):
    brand_id: int | None = Field(None, nullable=True)
    store_id: int | None = Field(None, nullable=True)

    target: TextNotificationTargetEnum
    type: TextNotificationTypeEnum
    menu_in_store_id: int | None = Field(None, nullable=True)
    text: str | None = Field(None, nullable=True)
