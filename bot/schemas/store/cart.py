from typing import Annotated

from pydantic import BaseModel, Field

from schemas.base import BaseORMModel
from schemas.store.attribute import AttributeSchema
from schemas.store.product import ProductSchema
from schemas.store.store import NotAvailableProduct
from ..auth import Token


class CartAttributeSchema(BaseORMModel):
    id: int
    quantity: int
    attribute: AttributeSchema


class CartProductSchema(BaseORMModel):
    id: int
    quantity: int
    product: ProductSchema
    cart_attributes: list[CartAttributeSchema] | None = None
    floating_sum: float | None = None


class CartSchema(BaseORMModel):
    id: int
    cart_products: list[CartProductSchema] | None = None
    store_id: int
    hash: str
    unavailable_products: list[NotAvailableProduct] | None = None
    access_token: Annotated[str | None, Field(nullable=True)] = None


class CartResponseBase(BaseORMModel):
    cart: CartSchema | None = None
    access_token: str | None = None


class CartProductResponseSchema(CartResponseBase):
    cart_product: CartProductSchema


class CartAttributeResponseSchema(CartResponseBase):
    cart_attribute: CartAttributeSchema


class SyncCartSchema(BaseModel):
    anon_cart_token: str | None = None
    store_id: int


class CartCreated(Token):
    cart: CartSchema


class SaveCartAttributeSchema(BaseORMModel):
    quantity: int
    attribute_id: int
    store_id: int | None = None
    delete: bool | None = None


class SaveCartProductSchema(BaseORMModel):
    quantity: int
    product_id: int
    cart_attributes: list[SaveCartAttributeSchema] | None = None
    floating_sum: float | None = None


class AddCartProduct(BaseModel):
    store_id: int | None = None
    product: SaveCartProductSchema


class SaveCartSchema(BaseORMModel):
    cart_products: list[SaveCartProductSchema] | None = None
    store_id: int


class SetCartProductQuantityData(BaseModel):
    store_id: int | None = None
    quantity: int


class ClearCartData(BaseModel):
    store_id: int


class UpdateCartAttribute(BaseModel):
    quantity: int
    store_id: int


class UpdateCartProductRequestSchema(BaseModel):
    quantity: int
    attributes: list[SaveCartAttributeSchema] | None = None
    floating_sum: float | None = None
