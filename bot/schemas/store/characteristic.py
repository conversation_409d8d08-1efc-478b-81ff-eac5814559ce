from enum import Enum

from pydantic import BaseModel

from schemas.base import BaseORMModel


class CharacteristicFilterType(Enum):
    VALUE = "v"
    MULTI = "m"
    RANGE_INTEGER = "ri"
    INTEGER = "i"
    FLOAT = "f"


class CharacteristicSchema(BaseORMModel):
    id: int
    name: str
    filter_type: CharacteristicFilterType
    is_hide: bool = False
    position: int | None = None


class CharacteristicValue(BaseModel):
    display_value: str
    value: str


class CharacteristicValues(BaseModel):
    characteristic_id: int
    store_id: int
    category_id: int | None
    values: list[CharacteristicValue]
