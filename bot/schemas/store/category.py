from fastapi import Query
from pydantic import BaseModel, <PERSON><PERSON>

from schemas.base import BaseORMModel
from .characteristic import CharacteristicSchema
from .product import ProductListFiltersData


class CategorySchema(BaseORMModel):
    id: int
    has_child_categories: bool
    father_category_id: int | None = None

    external_id: str | None = None
    external_type: str | None = None

    name: str
    position: int | None = None

    products_count: int = -1

    filters: list[CharacteristicSchema]

    children: list["CategorySchema"] | None = None


class GetCategoryTreeParams(BaseModel):
    store_id: int = Query()
    products_search: str | None = Query(None, nullable=True)
    filters_set_id: int | None = Query(None, nullable=True)
    filters: Json[ProductListFiltersData] | None = Query(None, nullable=True)
