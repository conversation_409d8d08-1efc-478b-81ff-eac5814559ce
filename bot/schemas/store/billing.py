from typing import Literal

from schemas.base import BaseORMModel


class BillingAddress(BaseORMModel):
    counterparty_type: Literal["person", "organisation"] | None = None

    first_name: str | None = None
    last_name: str | None = None

    company_name: str | None = None
    vat_number: str | None = None
    registration_number: str | None = None

    country: str | None = None
    state: str | None = None
    city: str | None = None
    zip_code: str | None = None

    address_1: str | None = None
    address_2: str | None = None
    phone_number: str | None = None


class BillingSettings(BaseORMModel):
    is_enable: bool
    is_require: bool
    need_person: bool
    need_organisation: bool
