from typing import Literal

from pydantic import BaseModel


class LanguageSchema(BaseModel):
    code: str
    english_name: str | None
    original_name: str | None
    current_name: str | None  # name on current language
    display_name: str
    is_auto_translate: bool


class LocaliseListData(BaseModel):
    variables: dict[str, str]
    lang: str | None = None


class LocaliseDataSet(BaseModel):
    data: dict[str, dict[str, str]]
    lang: str | None = None
