from datetime import timedelta
from typing import Annotated, Literal

from pydantic import BaseModel, <PERSON>, Json

from .price import ShipmentPrice
from .types import CustomTypeLiteral, DeliveryTimeTypeLiteral, ShipmentBaseTypeLiteral
from .zone import ShipmentZone
from ..not_working_hours import NotWorkingHoursLiteral


class ShipmentTimeSchema(BaseModel):
    min_time: timedelta | None = None
    max_time: timedelta | None = None
    execution_time: timedelta | None = None


class BaseShipmentSchema(BaseModel):
    id: int
    custom_type: CustomTypeLiteral
    store_settings_id: int | None = None
    map_countries: list[str] | None
    shipment_time: ShipmentTimeSchema | None = None
    is_enabled: bool | None = None


class ShipmentSchema(BaseShipmentSchema):
    custom_type: Literal["shipment"]
    base_type: ShipmentBaseTypeLiteral
    name: str

    prices: list[ShipmentPrice]

    is_paid_separately: bool

    allow_online_payment: bool
    allow_cash_payment: bool
    payments_ids: list[int]

    delivery_datetime_mode: DeliveryTimeTypeLiteral
    not_working_hours: NotWorkingHoursLiteral

    enabled_tips: bool = False
    enabled_any_address_from_map: bool


class CustomShipmentSchema(ShipmentSchema):
    custom_type: Literal["custom_shipment"]
    description: str | None
    icon_url: str | None

    need_comment: bool
    label_comment: str
    need_address: bool


class CustomShipmentGroupSchema(BaseShipmentSchema):
    custom_type: Literal["custom_shipment_group"]
    name: str
    description: str | None
    icon_url: str | None
    shipments: list[CustomShipmentSchema]


class ShipmentsData(BaseModel):
    base: list[ShipmentSchema]
    groups: list[CustomShipmentGroupSchema]
    rest: list[CustomShipmentSchema]
    no_delivery_shipment: Annotated[ShipmentSchema | None, Field(nullable=True)]


class CustomShipmentMethodData(BaseModel):
    id: int
    comment: str | None = None


class CustomShipmentsSchema(BaseModel):
    groups: list[CustomShipmentGroupSchema]
    rest: list[CustomShipmentSchema]


class ShipmentIdObjSchema(BaseModel):
    shipment_id: int | None = None
    store_settings_id: int | None = None


class ShipmentPriceInputData(BaseModel):
    store_id: int | None = None

    order_sum: float = 0.0
    address: str | None = None

    is_next_price_cheaper: bool = True

    shipment_id: Json[ShipmentIdObjSchema] | ShipmentIdObjSchema | None = None
    shipment_ids: list[Json[ShipmentIdObjSchema] | ShipmentIdObjSchema] | None = None

    address_lat: float | None = None
    address_lng: float | None = None


class ShipmentPriceResultData(BaseModel):
    select_price: ShipmentPrice | None = None
    next_price: ShipmentPrice | None = None
    available_prices: list[ShipmentPrice] | None = None
    available_zones: list[ShipmentZone] | None = None
    has_zones: bool = False
    shipment_id: int | None = None
    all_zones: list[ShipmentZone] | None = None
