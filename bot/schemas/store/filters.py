from typing import Annotated

from pydantic import BaseModel, Field

from .characteristic import CharacteristicFilterType


class Filter(BaseModel):
    filter_type: CharacteristicFilterType
    value: Annotated[float | int | str | None, Field(nullable=True)] = None
    values_list: Annotated[list[float | int | str | None], Field(nullable=True)] = None
    range_min: Annotated[float | int | None, Field(nullable=True)] = None
    range_max: Annotated[float | int | None, Field(nullable=True)] = None


class CreateFiltersData(BaseModel):
    filters: dict[int, Filter | None]
    min_price: Annotated[float | None, Field(nullable=True)] = None
    max_price: Annotated[float | None, Field(nullable=True)] = None


class FiltersCreated(BaseModel):
    filters_set_id: int
