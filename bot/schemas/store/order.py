from datetime import datetime
from typing import Annotated, Literal

from incust_api.api import term
from pydantic import BaseModel, Field

from schemas.base import BaseORMModel, ExtraFeeSchema
from schemas.store.types import (
    DeliveryTimeTypeLiteral, ShipmentBaseTypeLiteral,
)
from .billing import BillingAddress
from .price import ShipmentPrice
from .product import ProductSchema
from ..incust import CouponShowData
from incust_terminal_api_client.models import CustomerSpecialAccount, SpecialAccountCharge

OrderType = Literal["regular", "gift", "topup"]

OrderStatusType = Literal[
    "open_unconfirmed", "open_confirmed", "canceled", "closed", "new",
    "terminated_by_user"]
OrderStatusPayType = Literal["must_pay", "payed", "processing"]
OrderCurrentStatusType = Literal[
    "open_unconfirmed",
    "open_confirmed",
    "wait_for_ship",
    "shipped",
    "in_transit",
    "delivered",
    "canceled",
    "closed",
    "payed",
]

OrderStatusHistoryStatusType = OrderCurrentStatusType | Literal["payed"]


class OrderAttributeSchema(BaseORMModel):
    id: int
    quantity: int
    name: str
    price_impact: float
    attribute_id: int
    attribute_code: str


class OrderProductSchema(BaseORMModel):
    id: int
    product: ProductSchema
    price: float
    name: str
    quantity: int
    product_id: int
    attributes: Annotated[
        list[OrderAttributeSchema] | None, Field(nullable=True)] = None

    price_with_attributes: Annotated[float | None, Field(nullable=True)] = None
    discount_amount: Annotated[float | None, Field(nullable=True)] = None
    price_after_loyalty: Annotated[float | None, Field(nullable=True)] = None
    bonuses_redeemed: Annotated[float | None, Field(nullable=True)] = None
    display_name: Annotated[str | None, Field(nullable=True)] = None
    display_description: Annotated[str | None, Field(nullable=True)] = None
    discount_sum: Annotated[float | None, Field(nullable=True)] = None
    before_loyalty_sum: Annotated[float | None, Field(nullable=True)] = None

    incust_account: Annotated[
        CustomerSpecialAccount | None, Field(nullable=True)] = None
    incust_card: Annotated[str | None, Field(nullable=True)] = None
    charge_percent: float | None = 0
    charge_fixed: float | None = 0
    is_topup_error: bool = False
    topup_charge: Annotated[float | None, Field(nullable=True)] = None
    total_sum: float = 0


class UpdateOrderBaseSchema(BaseORMModel):
    first_name: Annotated[str | None, Field(nullable=True)] = None
    last_name: Annotated[str | None, Field(nullable=True)] = None
    phone: Annotated[str | None, Field(nullable=True)] = None
    email: Annotated[str | None, Field(nullable=True)] = None

    delivery_address: Annotated[str | None, Field(nullable=True)] = None
    address_comment: Annotated[str | None, Field(nullable=True)] = None

    desired_delivery_date: Annotated[datetime | None, Field(nullable=True)] = None
    desired_delivery_time: Annotated[
        str | None, Field(deprecated=True, nullable=True)
    ] = None

    address_street: Annotated[str | None, Field(nullable=True)] = None
    address_house: Annotated[str | None, Field(nullable=True)] = None
    address_flat: Annotated[str | None, Field(nullable=True)] = None
    address_floor: Annotated[int | None, Field(nullable=True)] = None
    address_entrance: Annotated[int | None, Field(nullable=True)] = None
    address_lat: Annotated[str | None, Field(nullable=True)] = None
    address_lng: Annotated[str | None, Field(nullable=True)] = None
    address_place_id: Annotated[str | None, Field(nullable=True)] = None

    comment: Annotated[str | None, Field(nullable=True)] = None
    tips_sum: Annotated[float | None, Field(nullable=True)] = None

    menu_in_store_id: Annotated[int | None, Field(nullable=True)] = None
    marketing_consent: Annotated[bool | None, Field(nullable=True)] = None


class UpdateOrderSchema(UpdateOrderBaseSchema):
    billing_address: Annotated[BillingAddress | None, Field(nullable=True)] = None
    shipment_method_id: Annotated[int | None, Field(nullable=True)] = None
    price: Annotated[ShipmentPrice | None, Field(nullable=True)] = None
    free_shipment: Annotated[bool | None, Field(nullable=True)] = None
    custom_shipment_comment: Annotated[str | None, Field(nullable=True)] = None


class BaseOrderSchema(UpdateOrderBaseSchema):
    loyalty_type: Annotated[str | None, Field(nullable=True)] = None
    price_after_loyalty: Annotated[float | None, Field(nullable=True)] = None
    bonuses_redeemed: Annotated[float | None, Field(nullable=True)] = None
    discount: Annotated[float | None, Field(nullable=True)] = None
    discount_and_bonuses: Annotated[float | None, Field(nullable=True)] = None

    type: Annotated[OrderType, Field()] = "regular"


class CreateOrderAttribute(BaseModel):
    quantity: int
    attribute_id: int
    price_impact: Annotated[int | None, Field(nullable=True)] = None


class UTMLabelsSchema(BaseORMModel):
    utm_source: Annotated[str | None, Field(nullable=True)] = None
    utm_medium: Annotated[str | None, Field(nullable=True)] = None
    utm_campaign: Annotated[str | None, Field(nullable=True)] = None
    utm_content: Annotated[str | None, Field(nullable=True)] = None
    utm_term: Annotated[str | None, Field(nullable=True)] = None


class CreateOrderProduct(BaseModel):
    quantity: int
    product_id: int
    attributes: Annotated[
        list[CreateOrderAttribute] | None, Field(nullable=True)] = None
    display_name: Annotated[str | None, Field(nullable=True)] = None
    display_description: Annotated[str | None, Field(nullable=True)] = None
    floating_sum: Annotated[float | None, Field(nullable=True)] = None
    incust_pay_id: Annotated[int | None, Field(nullable=True)] = None
    incust_account: Annotated[
        CustomerSpecialAccount | None, Field(nullable=True)] = None
    incust_card: Annotated[str | None, Field(nullable=True)] = None


class CreateOrderSchema(BaseOrderSchema):
    store_id: int

    is_accepted_agreement: Annotated[bool | None, Field(nullable=True)] = None

    products: Annotated[list[CreateOrderProduct] | None, Field(nullable=True)] = None
    incust_check: Annotated[term.m.Check | None, Field(nullable=True)] = None
    incust_terminal_id: Annotated[str | None, Field(nullable=True)] = None
    billing_address: Annotated[BillingAddress | None, Field(nullable=True)] = None

    shipment_method_id: int
    price: Annotated[ShipmentPrice | None, Field(nullable=True)] = None
    free_shipment: Annotated[bool | None, Field(nullable=True)] = None
    custom_shipment_comment: Annotated[str | None, Field(nullable=True)] = None

    marketing_consent: Annotated[bool | None, Field(nullable=True)] = None

    utm_labels: Annotated[UTMLabelsSchema | None, Field(nullable=True)] = None


class CreateOrderInStoreSchema(BaseModel):
    store_id: int
    menu_in_store_id: int

    marketing_consent: Annotated[bool | None, Field(nullable=True)] = None
    is_accepted_agreement: Annotated[bool | None, Field(nullable=True)] = None

    products: Annotated[list[CreateOrderProduct] | None, Field(nullable=True)] = None
    comment: Annotated[str | None, Field(nullable=True)] = None

    loyalty_type: Annotated[str | None, Field(nullable=True)] = None
    price_after_loyalty: Annotated[float | None, Field(nullable=True)] = None
    bonuses_redeemed: Annotated[float | None, Field(nullable=True)] = None
    discount: Annotated[float | None, Field(nullable=True)] = None

    incust_check: Annotated[term.m.Check | None, Field(nullable=True)] = None
    incust_terminal_id: Annotated[str | None, Field(nullable=True)] = None
    tips_sum: float

    utm_labels: Annotated[UTMLabelsSchema | None, Field(nullable=True)] = None


class OrderShipmentOrPaymentBaseSchema(BaseORMModel):
    id: int
    price: float
    comment: Annotated[str | None, Field(nullable=True)] = None
    name: str
    label_comment: Annotated[str | None, Field(nullable=True)] = None

    info: Annotated[str | None, Field(nullable=True)] = None


class OrderShipmentSchema(OrderShipmentOrPaymentBaseSchema):
    base_type: ShipmentBaseTypeLiteral
    is_paid_separately: bool
    delivery_datetime_mode: DeliveryTimeTypeLiteral
    settings_id: int


class OrderPayment(BaseModel):
    name: Annotated[str | None, Field(nullable=True)] = None
    description: Annotated[str | None, Field(nullable=True)] = None
    comment: Annotated[str | None, Field(nullable=True)] = None
    post_payment_info: Annotated[str | None, Field(nullable=True)] = None
    label_comment: Annotated[str | None, Field(nullable=True)] = None
    payment_method: str
    payment_settings_id: Annotated[int | None, Field(nullable=True)] = None
    incust_account_name: Annotated[str | None, Field(nullable=True)] = None
    price: float


class OrderSchema(BaseOrderSchema):
    id: int
    type: OrderType
    total_sum: float
    sum_to_pay: float
    create_date: datetime
    status: OrderStatusType
    status_pay: OrderStatusPayType
    order_products: list[OrderProductSchema]
    store_id: int
    token: Annotated[str | None, Field(nullable=True)] = None
    shipment_status: Annotated[str | None, Field(nullable=True)] = None

    order_payment: Annotated[OrderPayment | None, Field(nullable=True)] = None
    shipment: OrderShipmentSchema

    display_name: Annotated[str | None, Field(nullable=True)] = None
    display_description: Annotated[str | None, Field(nullable=True)] = None

    before_loyalty_sum: float
    bonuses_added_amount: Annotated[float | None, Field(nullable=True)] = None
    special_accounts_charges: Annotated[
        list[SpecialAccountCharge] | None, Field(nullable=True)] = None
    original_incust_loyalty_check: Annotated[
        term.m.Check | None, Field(nullable=True)] = None
    currency: str
    incust_vouchers: Annotated[
        list[CouponShowData] | None, Field(nullable=True)] = None
    user_id: int
    date_sent_to_friend: Annotated[datetime | None, Field(nullable=True)] = None
    friend_pending_name: Annotated[str | None, Field(nullable=True)] = None
    payer_fee: Annotated[float | None, Field(nullable=True)] = None
    paid_sum: Annotated[float | None, Field(nullable=True)] = None
    extraFee: Annotated[list[ExtraFeeSchema] | None, Field(nullable=True)] = None
    total_sum_with_extra_fee: Annotated[float | None, Field(nullable=True)] = None


class OrderID(BaseORMModel):
    order_id: int


class OrderPaymentStatusSchema(BaseORMModel):
    payment_status: str


class PaymentStatusSchema(BaseORMModel):
    payment_status: str


class OrderHistorySchema(BaseORMModel):
    time_created: datetime
    status: str
    comment: Annotated[str | None, Field(nullable=True)] = None


class OrderCustomMethodDataSchema(BaseModel):
    name: str
    price: float
    comment: Annotated[str | None, Field(nullable=True)] = None


CRMChangeOrderStatusLiteral = Literal[
    "open_confirmed",
    "payed",
    "shipped",
    "in_transit",
    "delivered",
    "closed",
    "canceled",
]


class CRMChangeOrderStatusData(BaseModel):
    status: CRMChangeOrderStatusLiteral
    comment: str | None = Field(None, nullable=True)


class NewOrdersCheckerWorkerResultItem(BaseModel):
    transaction_id: str
    order_id: int
    success: bool
    skip: bool = False


class StoreProductPosterCheck(BaseModel):
    product_id: int
    name: str
    external_type: str
    external_id: str
