from dataclasses import dataclass
from enum import Enum
from typing import Literal, Optional

from fastapi import Form
from pydantic import BaseModel


class UniposCheque(BaseModel):
    partner: str
    ext_id: str
    amount: int
    currency: int
    purpose: str | None = None
    redirect_url: str
    merchant_id: str
    created_at: str


class UniposExchange(BaseModel):
    rate: int


class UniposStatus(BaseModel):
    locked: str | None = None
    pay_status: str | None = None
    pay_trx_id: str | None = None


class UniposQR(BaseModel):
    amount: int
    currency: int
    qrc_id: str
    local_qrc_id: int
    url: str
    status: UniposStatus


class UniposCallbackAdd(BaseModel):
    name: str
    value: str


class UniposCallback(BaseModel):
    trx_id: str | None = None
    trx_time: str | None = None
    snd_pam: str | None = None
    snd_phone_masked: str | None = None
    add: list[UniposCallbackAdd] | None = list()
    at: str | None = None


class UniposPaymentBody(BaseModel):
    cheque: UniposCheque
    exchange: UniposExchange
    qr: UniposQR
    callback: UniposCallback
    updated_at: str


class UniposPayment(BaseModel):
    payment: UniposPaymentBody


class WaveLastCodeMessage(BaseModel):
    code: str
    message: str


class WavePayment(BaseModel):
    id: str | None
    amount: str | None
    checkout_status: str | None
    client_reference: str | None
    currency: str | None
    error_url: str | None
    last_payment_error: WaveLastCodeMessage | None
    business_name: str | None
    payment_status: str | None
    success_url: str | None
    wave_launch_url: str | None
    when_completed: str | None
    when_created: str | None
    when_expires: str | None
    transaction_id: str | None
    aggregated_merchant_id: str | None


class WaveHookResponse(BaseModel):
    id: str
    type: str
    data: WavePayment


class WavePayOutError(BaseModel):
    error_code: str
    error_message: str


class WavePayOutResult(BaseModel):
    id: str
    currency: str
    receive_amount: str
    fee: str
    mobile: str
    name: str
    status: Literal['processing', 'succeeded', 'failed', 'reversed']
    payout_error: WavePayOutError | None = None
    timestamp: str
    aggregated_merchant_id: str | None = None


class Pl24Token(BaseModel):
    token: str


class Pl24Payment(BaseModel):
    data: Pl24Token
    responseCode: int


class Pl24HookResponse(BaseModel):
    merchantId: int
    posId: int
    sessionId: str
    amount: int
    originAmount: int
    currency: str = "PLN"
    orderId: int | None = None
    methodId: int
    statement: str
    sign: str


class TpayPayment(BaseModel):
    data: str


@dataclass
class TpayHookResponse:
    id: str = Form()
    tr_id: str = Form()
    tr_date: str = Form()
    tr_crc: str = Form()
    tr_amount: str = Form()
    tr_paid: float = Form()
    tr_desc: str = Form()
    tr_status: str = Form()
    tr_error: str | None = Form()
    tr_email: str | None = Form()
    md5sum: str = Form()
    test_mode: bool | None = Form()
    wallet: str | None = Form(None)
    masterpass: str | None = Form(None)


class TpayDate(BaseModel):
    creation: str | None
    realization: str | None


class TpayErrors(BaseModel):
    errorCode: str
    errorMessage: str
    fieldName: str | None


class TpayPayments(BaseModel):
    status: str
    method: str | None
    amountPaid: float
    date: TpayDate
    errors: TpayErrors | None


class TpayPayer(BaseModel):
    payerId: str | None
    email: str | None
    name: str
    phone: str | None
    address: str | None
    postalCode: str | None
    city: str | None
    country: str | None
    taxId: str | None


class TpayError(BaseModel):
    errorCode: str
    errorMessage: str
    fieldName: str
    devMessage: str
    docUrl: str | None


class TpayTransaction(BaseModel):
    transactionPaymentUrl: str
    lang: str | None
    payer: TpayPayer
    payments: TpayPayments | None
    result: str
    requestId: str
    errors: list[TpayError] | None
    transactionId: str
    title: str
    posId: str
    status: str
    amount: float
    currency: str
    description: str
    hiddenDescription: str
    date: TpayDate


class OrangeAmount(BaseModel):
    value: int
    unit: str


class OrangePartner(BaseModel):
    idType: str
    id: str


class OrangeCustomer(BaseModel):
    idType: str
    id: str


class OrangeMetadata(BaseModel):
    cancelRedirectUrl: str
    payment_id: str
    successRedirectUrl: str
    Cause: str | None


class OrangeHookResponse(BaseModel):
    partner: OrangePartner
    customer: OrangeCustomer
    amount: OrangeAmount
    type: str | None
    paymentMethod: str
    channel: str
    reference: str | None
    transactionId: str
    createdAt: str | None
    status: str
    metadata: OrangeMetadata


class OrangeOtpData(BaseModel):
    amount: int
    customer_phone: str
    reference_id: str
    currency: str
    otp: str
    success_url: str


class FondyHookResponse(BaseModel):
    merchant_id: int
    amount: int
    currency: str

    order_id: str
    order_status: Literal[
        "created", "processing", "declined", "approved", "expired", "reversed"]
    response_status: Literal["failure", "success"]
    tran_type: Literal["purchase", "reverse"]
    signature: str

    sender_cell_phone: str | None = None
    sender_account: str | None = None

    masked_card: str | None = None
    card_bin: int | None = None
    card_type: Literal["VISA", "MasterCard"] | None = None

    rrn: str | None = None
    approval_code: str | None = None

    response_code: int | None = None
    response_description: str | None = None

    reversal_amount: int = 0
    settlement_amount: int = 0
    settlement_currency: str | None = None

    order_time: str
    settlement_date: str | None = None

    eci: int | None = None
    fee: int | None = None

    payment_system: str | None = None
    sender_email: str | None = None

    payment_id: int
    actual_amount: int | None = None
    actual_currency: str | None = None
    product_id: str | None = None
    merchant_data: str | None = None

    verification_status: Literal[
                             "verified", "incorrect", "failed", "created"] | None = None

    rectoken: str | None = None
    rectoken_lifetime: str | None = None

    parent_order_id: str | None = None
    additional_info: str


class FondyWebHookResponse(BaseModel):
    response: dict


class FreedompayHookResponse(BaseModel):
    pg_order_id: int
    pg_payment_id: int

    pg_currency: str
    pg_amount: float
    pg_net_amount: float
    pg_ps_amount: float
    pg_ps_full_amount: float
    pg_ps_currency: str

    pg_description: str
    pg_result: bool
    pg_can_reject: bool
    pg_payment_date: str

    pg_user_phone_text: str | None
    pg_need_phone_notification: bool
    pg_user_contact_email: str | None
    pg_need_email_notification: bool
    pg_testing_mode: bool

    pg_payment_method: str
    pg_reference: str
    pg_captured: bool
    pg_card_pan: str | None
    pg_card_exp: str | None
    pg_card_owner: str | None
    pg_card_brand: str | None
    pg_auth_code: str | None

    payment_uuid_id: str | None = None

    pg_salt: str
    pg_sig: str


class FlutterwaveCardDetails(BaseModel):
    first_6digits: str | None
    last_4digits: str | None
    issuer: str | None
    country: str | None
    type: str | None
    token: str | None
    expiry: str | None


class FlutterwaveCustomer(BaseModel):
    id: int | None = None
    phone: str | None = None
    fullName: str | None = None
    customertoken: str | None = None
    email: str | None = None
    createdAt: str | None = None
    updatedAt: str | None = None
    deletedAt: str | None = None
    AccountId: int


class FlutterwaveError(BaseModel):
    code: int | None
    title: str | None
    message: str | None


class FlutterwavePaymentData(BaseModel):
    id: int | None
    tx_ref: str | None
    flw_ref: str | None
    device_fingerprint: str | None
    amount: int | None
    currency: str | None
    charged_amount: int | None
    app_fee: int | None
    merchant_fee: int | None
    processor_response: str | None
    auth_model: str | None
    ip: str | None
    narration: str | None
    status: str | None
    payment_type: str | None
    created_at: str | None
    account_id: int | None
    link: str | None

    card: FlutterwaveCardDetails | None
    amount_settled: int | None
    customer: FlutterwaveCustomer | None


class FlutterwavePaymentResponse(BaseModel):
    status: str | None
    message: str | None
    data: FlutterwavePaymentData | None
    error: FlutterwaveError | None


class FlutterwaveEntity(BaseModel):
    card6: str | None
    card_last4: str | None
    card_country_iso: str | None
    createdAt: str | None


class FlutterwaveTransaction(BaseModel):
    id: int | None = None
    txRef: str | None = None
    flwRef: str | None = None
    orderRef: str | None = None
    paymentPlan: str | None = None
    paymentPage: str | None = None
    createdAt: str | None = None
    amount: float | None = None
    charged_amount: float | None = None
    status: str | None = None
    IP: str | None = None
    currency: str | None = None
    appfee: float | None = None
    merchantfee: float | None = None
    merchantbearsfee: int | None = None
    customer: FlutterwaveCustomer | None = None
    entity: FlutterwaveEntity | None = None
    eventType: str | None = None


class ComsaPaymentNotification(BaseModel):
    AGR_TRANS_ID: str | None = None
    VENDOR_TRANS_ID: str | None = None
    STATUS: int | None = None
    MESSAGE: str | None = None
    SIGN_TIME: int | None = None
    SIGN_STRING: str | None = None


class ComsaWidgetForming(BaseModel):
    VENDOR_ID: int | None = None
    MERCHANT_TRANS_ID: str | None = None
    MERCHANT_TRANS_AMOUNT: float | None = None
    MERCHANT_CURRENCY: str | None = None
    MERCHANT_TRANS_NOTE: str | None = None
    MERCHANT_TRANS_RETURN_URL: str | None = None
    MERCHANT_TRANS_ERROR_RETURN_URL: str | None = None
    MERCHANT_LANG: str | None = None
    MERCHANT_TRANS_DATA: str | None = None
    SIGN_TIME: int | None = None
    SIGN_STRING: str | None = None


class ComsaResultCreateWidget(BaseModel):
    url: str | None = None
    error: str | None = None


class API3GResponse(BaseModel):
    Result: str
    ResultExplanation: str
    TransactionToken: str
    TransactionRef: str
    CustomerName: str
    CustomerCredit: str
    TransactionApproval: str
    TransactionCurrency: str
    TransactionAmount: str
    FraudAlert: str
    FraudExplnation: str  # Note: This is misspelled in the original XML
    TransactionNetAmount: str
    TransactionSettlementDate: str
    TransactionRollingReserveAmount: str
    TransactionRollingReserveDate: str
    CustomerPhone: str
    CustomerCountry: str
    CustomerAddress: str
    CustomerCity: str
    CustomerZip: str
    MobilePaymentRequest: str
    AccRef: str


class DirectpayAllocation(BaseModel):
    AllocationID: str
    AllocationCode: str


class DirectpayTransaction(BaseModel):
    Result: str
    ResultExplanation: str
    TransToken: str | None
    TransRef: str | None
    Allocations: list[DirectpayAllocation] | None = None


class MomoPayee(BaseModel):
    partyIdType: str
    partyId: str


class MomoWebHookData(BaseModel):
    financialTransactionId: str
    externalId: str
    amount: str
    currency: str
    payee: MomoPayee
    payeeNote: str | None = None
    status: str


class MomoPartyType(str, Enum):
    MSISDN = "MSISDN"
    EMAIL = "EMAIL"
    PARTY_CODE = "PARTY_CODE"


class MomoParty(BaseModel):
    party_id_type: MomoPartyType
    party_id: str


class MomoTransactionStatus(str, Enum):
    PENDING = "PENDING"
    SUCCESSFUL = "SUCCESSFUL"
    FAILED = "FAILED"


class MomoErrorReason(BaseModel):
    code: str
    message: str


class MomoTransaction(BaseModel):
    amount: str
    currency: str
    financial_transaction_id: str
    external_id: str
    payee: MomoParty
    payer_message: Optional[str] = None
    payee_note: Optional[str] = None
    status: MomoTransactionStatus
    reason: Optional[MomoErrorReason] = None


class TjCreateSessionResponse(BaseModel):
    ipgwSId: str
    redirectUrl: str


class TjTokenInfo(BaseModel):
    token: str
    brand: str
    expiryDate: str
    maskedPAN: str
    NetworkToken: bool


class TjMetadata(BaseModel):
    payment_uuid_id: str


class TjPaymentMethodDetails(BaseModel):
    card: dict | None = None
    responseCode: str | None = None
    responseText: str | None = None


class TjPaymentResult(BaseModel):
    transactionId: str
    sessionId: str
    paymentIntentId: str
    amount: str
    responseText: str | None = None
    paymentType: str | None = None
    transactionStatus: str
    merchantRef: str
    tokenInfo: TjTokenInfo | None = None
    customerProfileId: str | None = None
    metadata: TjMetadata
    paymentMethodDetails: TjPaymentMethodDetails | None = None


class TjCurrency(BaseModel):
    isoCode: str
    isoNumber: int
    fractionalNumber: int
    symbol: str


class TjTransactionResult(BaseModel):
    transactionId: str
    paymentIntentId: str
    customerProfileId: Optional[str] = None
    paymentType: str | None = None
    amount: float
    currency: TjCurrency
    transactionStatus: str


class AirTelSubscriber(BaseModel):
    country: str
    currency: str
    msisdn: str


class AirTelInitPaymentTransaction(BaseModel):
    amount: str
    country: str
    currency: str
    id: str


class AirTelInitPayment(BaseModel):
    reference: str
    subscriber: AirTelSubscriber
    transaction: AirTelInitPaymentTransaction


class AirTelInitPaymentResponseTransaction(BaseModel):
    id: bool
    status: str


class AirTelInitPaymentData(BaseModel):
    transaction: AirTelInitPaymentResponseTransaction


class AirTelInitPaymentStatus(BaseModel):
    code: str
    message: str
    result_code: str
    response_code: str
    success: bool


class AirTelInitPaymentResponse(BaseModel):
    data: AirTelInitPaymentData
    status: AirTelInitPaymentStatus


class AirTelTransaction(BaseModel):
    id: str
    message: str
    status_code: str
    airtel_money_id: str


class AirTelCallback(BaseModel):
    transaction: AirTelTransaction


class AirTelPayInResult(BaseModel):
    status: str
    id: str


class AirTelCashInTransaction(BaseModel):
    airtel_money_id: str
    id: str


class AirTelCashInData(BaseModel):
    message: str
    status: str
    transaction: AirTelCashInTransaction


class AirTelCashInStatus(BaseModel):
    code: str
    message: str
    response_code: str
    success: bool


class AirTelCashInResponse(BaseModel):
    data: AirTelCashInData
    status: AirTelCashInStatus


class AirTelDisbursementTransactionData(BaseModel):
    reference_id: str
    airtel_money_id: str
    id: str
    status: str
    message: str


class AirTelDisbursementData(BaseModel):
    transaction: AirTelDisbursementTransactionData


class AirTelDisbursementStatus(BaseModel):
    response_code: str
    code: str
    success: bool
    message: str


class AirTelDisbursementResponse(BaseModel):
    data: AirTelDisbursementData
    status: AirTelDisbursementStatus


class EWalletPaymentStatus(Enum):
    CREATED = "created"
    PENDING = "pending"
    PAID = "paid"
    FAILED = "failed"
    CANCELLED = "cancelled"


class EWalletMakeBusinessPaymentResult(BaseModel):
    status: EWalletPaymentStatus


class KPayCustomerDto(BaseModel):
    fullName: str | None = None
    phoneNumber: str | None = None


class KPayMerchantDto(BaseModel):
    firstName: str | None = None
    lastName: str | None = None
    phoneNumber: str | None = None

class KPayPayment(BaseModel):
    amount: int | None = None
    application: str | None = None
    currency: str | None = None
    partner: str | None = None
    correlationReference: str | None = None
    customer: KPayCustomerDto | None = None
    merchant: KPayMerchantDto | None = None
    kpayReference: str | None = None
    qrValue: str | None = None
    status: Literal['succeeded', 'pending', 'failed', 'initiated', 'canceled', 'refunded'] | None = None
    createdAt: str | None = None
    updatedAt: str | None = None
    message: str | None
    code: str | None


class KPayPartnerDto(BaseModel):
    _id: str
    phoneNumber: str
    isActive: bool
    # pinHash: str
    # pinCrypt: str
    createdAt: str
    updatedAt: str
    __v: int


class KPayHookResponse(BaseModel):
    _id: str
    amount: float
    application: str
    partner: KPayPartnerDto | None = None
    correlationReference: str
    useUniqueWallet: bool
    inAppValidation: bool
    qrValidation: bool
    qrValue: Optional[str] = None
    callbackUrl: str | None = None
    customer: KPayCustomerDto | None = None
    merchant: KPayMerchantDto | None = None
    status: Literal[
        'succeeded', 'pending', 'failed', 'initiated', 'canceled', 'refunded']
    createdAt: str | None = None
    updatedAt: str | None = None
    __v: int
    kpayReference: str

    class Config:
        extra = "ignore"  # Ігнорувати додаткові поля, які не визначені в моделі


class KPayUserInfo(BaseModel):
    firstName: str | None = None
    lastName: str | None = None
    phone: str | None = None
    country: str | None = None

class KPayTransferResult(BaseModel):
    amount: int | None = None
    application: str | None = None
    partner: str | None = None
    correlationReference: str | None = None
    receiver: KPayUserInfo | None = None
    sender: KPayUserInfo | None = None
    isForcedToCash: bool | None = None
    status: str | None = None
    _id: str | None = None
    createdAt: str | None = None
    updatedAt: str | None = None
    __v: int | None = None
    kpayReference: str | None = None
    mode: str | None = None
    code: str | int | None = None
    message: str | None = None