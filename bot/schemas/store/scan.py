from psutils.receipts.schemas import Receipt as ScannerReceiptSchema
from pydantic import BaseModel
from incust_terminal_api_client.models.check import Check as IncustCheck

from ..incust import CouponShowData


class ScanPayloadSchema(BaseModel):
    data: str
    with_make_check: bool = False
    order_id: int | None = None
    store_id: int | None = None


class ScanResponseSchema(BaseModel):
    receipt_id: int
    check: IncustCheck  # Використовуємо оригінальну схему з нового API


class ScanResponseUnAuthSchema(BaseModel):
    receipt: ScannerReceiptSchema
    check: IncustCheck  # Використовуємо оригінальну схему з нового API


class ReceiptSchema(BaseModel):
    id: int
    receipt_id: int | str
    incust_check: IncustCheck | None = None
    vouchers: list[CouponShowData] | None = None
    total_price: float
    bin_code: str | None = None
