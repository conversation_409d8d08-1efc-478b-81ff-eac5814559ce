from datetime import datetime
from typing import Literal
from uuid import UUID

from pydantic import BaseModel

from schemas import BaseORMModel

ShortLinkType = Literal["url"]


class CreateShortLinkData(BaseModel):
    type: ShortLinkType
    url: str | None = None
    max_uses: int | None = None
    expiration_date: datetime | None = None
    display_id: str | None = None


class ShortLinkSchema(BaseORMModel, CreateShortLinkData):
    id: UUID
    short_url: str
    available_uses: int | None = None
    is_usage_available: bool
    time_created: datetime
