from typing import Literal
from pydantic import BaseModel

from .places import PlaceLatLng
from .base import AddressComponent


class ReverseGeocodingParamsSchema(BaseModel):
    latlng: str
    language: str | None = None
    region: str | None = None
    result_type: str | None = None
    location_type: str | None = None


class GeocodingParamsSchema(BaseModel):
    address: str
    bounds: str | None = None
    language: str | None = None
    region: str | None = None
    components: str | None = None


class GeocodingGeometry(BaseModel):
    location: PlaceLatLng


class PlusCode(BaseModel):
    compound_code: str | None = None
    global_code: str | None = None


class GeocodingResultItemSchema(BaseModel):
    address_components: list[AddressComponent]
    formatted_address: str
    geometry: GeocodingGeometry
    place_id: str
    types: list[str]
    location_type: str | None = None


class GeocodingResultSchema(BaseModel):
    plus_code: PlusCode | None = None
    results: list[GeocodingResultItemSchema]
    status: Literal["OK", "ZERO_RESULTS", "NOT_FOUND", "INVALID_REQUEST", "OVER_QUERY_LIMIT", "REQUEST_DENIED", "UNKNOWN_ERROR"]
    error_message: str | None = None
