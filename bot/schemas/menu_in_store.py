from typing import Annotated, Any, Literal

from pydantic import BaseModel, Field

from schemas.invoice import InvoiceTemplateSchema

NullableStr = Annotated[str | None, Field(nullable=True)]


class MenuTexts(BaseModel):
    order_button: NullableStr
    delivery_pickup_button: NullableStr


class WaiterTexts(BaseModel):
    call_button: NullableStr
    call_text: str
    called_text: str
    buttons_group_text: str


class ContactsTexts(BaseModel):
    reviews_button: NullableStr
    chat_button: NullableStr
    buttons_group_text: str


class PaymentsTexts(BaseModel):
    pay_button: NullableStr
    cash_button: NullableStr
    card_button: NullableStr
    online_button: NullableStr
    qr_button: NullableStr


class ActiveMenuTexts(BaseModel):
    open_button: NullableStr
    no_more_button: NullableStr
    left_text: str


class RedirectTexts(BaseModel):
    only_tg_text: str
    try_tg_text: str


class MenuInStoreTexts(BaseModel):
    header_text: NullableStr
    greeting_text: NullableStr
    comment_view_text: NullableStr
    menu: MenuTexts
    waiter: WaiterTexts
    contacts: ContactsTexts
    payments: PaymentsTexts
    active_menu: ActiveMenuTexts
    redirect: RedirectTexts


class MenuInStoreSchema(BaseModel):
    id: int

    group_id: int
    store_id: int | None

    comment: str
    redirect_type: str
    need_save_as_active: bool

    is_e_menu: bool

    texts: MenuInStoreTexts

    payment_option: Literal["disabled", "amount_from_user", "amount_from_template"]
    invoice_template_id: int | None = None
    invoice_template: InvoiceTemplateSchema | None = None


class MenuInStoreColumnSchema(BaseModel):
    id: int
    key: str
    value: Any
