import enum
from datetime import datetime

from pydantic import BaseModel, Field

from .base import BaseORMModel


class TaskMode(enum.Enum):
    ALL = "all"
    WITHOUT_IMAGE = "without_images"
    SELECTED = "selected"


class TaskTypeTaskEnum(enum.Enum):
    PRODUCT_IMAGE = "product_image"
    PRODUCT_GALLERY = "product_gallery"
    PROFILE_IMAGE = "profile_image"
    PROFILE_LOGO = "profile_logo"
    STORE_IMAGE = "store_image"
    STORE_BANNER = "store_banner"
    VM_STEP_IMAGE = "vm_step_image"
    INVOICE_TEMPLATE_IMAGE = "invoice_template_image"


class TaskCrateObjectResponse(BaseModel):
    task_id: int | None = None
    object_id: int
    name: str
    type_task: TaskTypeTaskEnum


class TaskCreateResultResponse(BaseModel):
    replacement: list[TaskCrateObjectResponse] | None = Field(default_factory=list)
    created: list[TaskCrateObjectResponse] | None = Field(default_factory=list)


class TaskTypeEnum(enum.Enum):
    AI = "ai"


class TaskAiModelTypeEnum(enum.Enum):
    DALLE2 = "dalle-2"
    DALLE3 = "dalle-3"


class TaskStatusEnum(enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    REPLACEMENT = "replacement"
    CANCELED = "canceled"
    FINISHED = "finished"
    DELETED = "deleted"
    FAILED = "failed"


class TaskListSchema(BaseORMModel):
    id: int
    type: TaskTypeEnum
    type_task: TaskTypeTaskEnum
    status: TaskStatusEnum = TaskStatusEnum.PENDING
    ai_model: TaskAiModelTypeEnum = TaskAiModelTypeEnum.DALLE2
    object_id: int
    object_name: str | None = None
    time_created: datetime
    change_date: datetime
    start_date: datetime | None = None
    end_date: datetime | None = None
    cancel_date: datetime | None = None
    time_processed: str | None = None
    error: str | None = None
    banner_id: int | None = None


class TaskSchema(TaskListSchema):
    media_url: str | None = None
    prompt: str | None = None
    dalle_prompt: str | None = None
    description: str | None = None


class TaskSchemaProduct(BaseModel):
    id: int
    status: TaskStatusEnum


class TaskExtraData(BaseModel):
    url: str | None = None
    name: str | None = None


class TaskCreateBanner(BaseModel):
    id: int | None = None
    url: str | None = None
    description: str | None = None
    is_use_inner_description: bool | None = True


class TaskCreateObject(BaseModel):
    id: int | None = None
    description: str | None = None
    is_use_inner_description: bool | None = True
    banners: list[TaskCreateBanner] | None = None


class CreateTaskSchema(BaseModel):
    mode: TaskMode
    type: TaskTypeEnum
    type_task: TaskTypeTaskEnum
    ai_model: TaskAiModelTypeEnum = TaskAiModelTypeEnum.DALLE2
    prompt: str = Field(..., max_length=4096)
    objects: list[TaskCreateObject] | None = None
    is_save_last_prompt: bool | None = None
    # description: str | None = None
    # extra_data: TaskExtraData | None = None


class TaskPromptResponse(BaseModel):
    prompt: str
    is_default: bool | None = None
    is_save_last_prompt: bool | None = None


class PromptManager:
    AI_PRODUCT_IMAGE_PROMPT = ("Your task is to generate a highly descriptive and effective prompt for DALL-E to "
                               "create a realistic, high-quality photograph of a dish. Ensure the prompt includes "
                               "specific visual details about the dish's appearance, presentation, and environment. "
                               "The prompt should be in the style of detailed food photography descriptions, "
                               "focusing on color, texture, and presentation. The final prompt should be no longer "
                               "than 1000 characters.")

    AI_PROFILE_IMAGE_PROMPT = ("Your task is to generate a highly descriptive and effective prompt for DALL-E to "
                               "create a high-quality image of a banner for internet brand company. "
                               "Ensure the prompt includes specific visual details about the company name or "
                               "description. The final prompt should be no longer than 1000 characters.")

    AI_PROFILE_LOGO_PROMPT = ("Your task is to generate a highly descriptive and effective prompt for DALL-E to "
                              "create high-quality image of a logo for internet brand company. "
                              "Ensure the prompt includes specific visual details about the company name or "
                              "description. The final prompt should be no longer than 1000 characters.")

    AI_STORE_IMAGE_PROMPT = ("Your task is to generate a highly descriptive and effective prompt for DALL-E to create "
                             "a high-quality image of a banner for internet store. Ensure the prompt "
                             "includes specific visual details about the store name and description. The final prompt "
                             "should be no longer than 1000 characters.")

    AI_STORE_BANNER_PROMPT = ("Your task is to generate a highly descriptive and effective prompt for DALL-E to "
                              "create a high-quality image of a banner for internet store. Ensure the "
                              "prompt includes specific visual details about the store name and description. The final "
                              "prompt should be no longer than 1000 characters.")

    AI_VM_STEP_IMAGE_PROMPT = ("Your task is to generate a highly descriptive and effective prompt for DALL-E to "
                               "create a high-quality image of a illustration for user guide step. "
                               "Ensure the prompt includes specific visual details about the user guide step "
                               "description. The final prompt should be no longer than 1000 characters.")

    AI_INVOICE_TEMPLATE_IMAGE_PROMPT = ("Your task is to generate a highly descriptive and effective prompt for DALL-E "
                                        "to create high-quality image of a invoice template. "
                                        "Ensure the prompt includes specific visual details about the invoice "
                                        "template name and description. The final prompt should be no longer than "
                                        "1000 characters.")

    @classmethod
    def get_prompt(cls, task_type: TaskTypeTaskEnum) -> str:

        prompt_map = {
            TaskTypeTaskEnum.PRODUCT_IMAGE: cls.AI_PRODUCT_IMAGE_PROMPT,
            TaskTypeTaskEnum.PROFILE_IMAGE: cls.AI_PROFILE_IMAGE_PROMPT,
            TaskTypeTaskEnum.PROFILE_LOGO: cls.AI_PROFILE_LOGO_PROMPT,
            TaskTypeTaskEnum.STORE_IMAGE: cls.AI_STORE_IMAGE_PROMPT,
            TaskTypeTaskEnum.STORE_BANNER: cls.AI_STORE_BANNER_PROMPT,
            TaskTypeTaskEnum.VM_STEP_IMAGE: cls.AI_VM_STEP_IMAGE_PROMPT,
            TaskTypeTaskEnum.INVOICE_TEMPLATE_IMAGE: cls.AI_INVOICE_TEMPLATE_IMAGE_PROMPT,
        }

        if task_type not in prompt_map:
            raise ValueError(f"Invalid task type: {task_type}")

        return prompt_map[task_type]

    @classmethod
    def get_openai_config(cls, openai_config_dict: dict):
        return {
            **openai_config_dict,
            "ai_product_image_prompt": cls.AI_PRODUCT_IMAGE_PROMPT,
            "ai_profile_image_prompt": cls.AI_PROFILE_IMAGE_PROMPT,
            "ai_profile_logo_prompt": cls.AI_PROFILE_LOGO_PROMPT,
            "ai_store_image_prompt": cls.AI_STORE_IMAGE_PROMPT,
            "ai_store_banner_prompt": cls.AI_STORE_BANNER_PROMPT,
            "ai_vm_step_prompt": cls.AI_VM_STEP_IMAGE_PROMPT,
            "ai_invoice_template_prompt": cls.AI_INVOICE_TEMPLATE_IMAGE_PROMPT,
        }
