from typing import Literal, Annotated
from pydantic import BaseModel, Field
from fastapi import Form, UploadFile

from .base import BaseORMModel

QrMediaObjectTargetType = Literal["qr_menu", "profile", 'fastpay', 'bot', 'custom']


class QrMediaAdditionalObject(BaseModel):
    id: int
    name: str
    media_url: str | None = None


class CreateOrUpdateQrMediaAdditionalObject(BaseModel):
    id: Annotated[int | None, Form()] = None
    name: Annotated[str | None, Form()] = None
    qr_media_file: Annotated[UploadFile | str, None] = Form(None, nullable=True)


class QrMediaObjectSchema(BaseORMModel):
    id: int
    json_data: dict | None = Field(None, nullable=True)
    name: str | None = Field(None, nullable=True)
    target: QrMediaObjectTargetType
    url: str
    media_url: str | None = Field(None, nullable=True)
    additional_media: list[QrMediaAdditionalObject]


class UpdateQrMediaObject(BaseModel):
    json_data: Annotated[str | None, Form()] = None
    name: Annotated[str | None, Form()] = None
    target: Annotated[QrMediaObjectTargetType | None, Form()] = None
    url: Annotated[str | None, Form()] = None
    qr_media_file: Annotated[UploadFile | str, None] = Form(None, nullable=True)
    additional_media: Annotated[list[CreateOrUpdateQrMediaAdditionalObject] | None, Form()] = None


class CreateQrMediaObject(BaseModel):
    json_data: Annotated[str | None, Form()] = None
    name: Annotated[str | None, Form()] = None
    target: Annotated[QrMediaObjectTargetType, Form()]
    url: Annotated[str, Form()]
    qr_media_file: Annotated[UploadFile | str, None] = Form(None, nullable=True)  # TODO: temporary for test purposes
    additional_media: Annotated[list[CreateOrUpdateQrMediaAdditionalObject] | None, Form()] = None
