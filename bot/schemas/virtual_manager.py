import enum

from pydantic import BaseModel, Field

from .base import BaseORMModel


class VMInteractiveType(enum.Enum):
    PRE_ACTION = "pre_action"
    POST_ACTION = "post_action"
    BUTTON = "button"
    BUTTON_ACTION = "button_action"
    ON_INPUT = "on_input"


class VMButtonType(enum.Enum):
    INLINE = "inline"
    REQUEST = "request"
    URL = "url"


class VMRequestType(enum.Enum):
    PHONE = "phone"
    LOCATION = "location"


class VMButtonParams(BaseModel):
    button_text: str
    button_add_new_row: bool = False


class VMRequestButtonParams(VMButtonParams):
    request_type: VMRequestType


class VMUrlButtonParams(VMButtonParams):
    url: str
    url_as_webapp_tg: bool = Field(
        False,
        description=(
            "If true and bot_type is telegram, "
            "webapp button will be generated, "
            "otherwise url button will be generated."
        ),
    )


# noinspection PyUnresolvedReferences
class VMInlineButtonParams(VMButtonParams):
    button_actions: list["VirtualManagerInlineButtonActionParamsSchema"]


class VMStepReminderMode(enum.Enum):
    DEFAULT = "default"
    REMIND = "remind"
    DISABLE = "disabled"


class BaseVMTicketActionParams(BaseModel):
    ticket_title: str | None = Field(None, nullable=True)


class VMTicketCreateActionParams(BaseVMTicketActionParams):
    create_ticket_if_not_exists: bool = False


class TicketSetStatusActionStatusEnum(enum.Enum):
    OPEN_CONFIRMED = "open_confirmed"
    CANCELED = "canceled"


class VMTicketSetStatusActionParams(BaseVMTicketActionParams):
    ticket_status: TicketSetStatusActionStatusEnum


class SetVarActionParams(BaseModel):
    var_name: str


class SetVarValueActionParams(SetVarActionParams):
    var_value: str


class VMGoToParams(BaseModel):
    vm_id: int | None = Field(
        default=None,
        nullable=True,
        description=(
            "The id of VM to start after time. "
            "If not specified, current virtual manager chat will be continued"
        )
    )
    vm_step_id: int | str = Field(description="The id of VM step to go to")


class BaseVirtualManagerStepSchema(BaseORMModel):
    text: str | None = Field(None, nullable=True)
    media_id: int | None = Field(None, nullable=True)

    reminder_mode: VMStepReminderMode
    reminder_delay: float | None = Field(None, nullable=True)
    reminds_count: int | None = Field(None, nullable=True)


class VirtualManagerStepORMSchema(BaseVirtualManagerStepSchema):
    id: int
    position: int
    virtual_manager_id: int
    task_id: int | None = None


class VirtualManagerStepTranslationSchema(BaseModel):
    text: str | None = Field(None, nullable=True)


# noinspection PyUnresolvedReferences
class VirtualManagerStepSchema(VirtualManagerStepORMSchema):
    media_url: str | None = Field(None, nullable=True)
    media_type: str | None = Field(None, nullable=True)
    interactives: list["VirtualManagerInteractiveUnionSchema"]
    translations: dict[str, VirtualManagerStepTranslationSchema] | None = Field(
        default=None,
        nullable=True,
        description=(
            "Translations for different languages. "
            "The key is language ISO code and "
            "the value is fields translations object"
        )
    )


class AdminVirtualManagerStepSchema(VirtualManagerStepORMSchema):
    media_url: str | None = Field(None, nullable=True)
    media_type: str | None = Field(None, nullable=True)
