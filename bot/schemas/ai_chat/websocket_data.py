from enum import Enum
from typing import Any

from psutils.ai_chat.schemas import AI<PERSON>hunk
from pydantic import BaseModel, Field

from ..store.product import ProductSchema


class AIChatSocketMessageFromEnum(Enum):
    CLIENT = "client"
    SERVER = "server"


class AIChatSocketMessageTypeEnum(Enum):
    TEXT = "text"
    CHUNK = "chunk"
    ERROR = "error"
    RECOMMENDED_PRODUCTS = "recommended_products"
    RESTART = "restart"
    PARAM = "param"


class AIChatSocketMessageParamEnum(Enum):
    ALGO = "algo"
    DIST_LIMIT = "dist_limit"


class AIChatSocketActionTypeEnum(Enum):
    MESSAGE = "message"


class RecommendedProductSchema(BaseModel):
    product: ProductSchema
    note: str | None = None


class RecommendedProductsData(BaseModel):
    products: list[RecommendedProductSchema]
    recommend_request: str


class AIChatSocketMessage(BaseModel):
    class Config:
        use_enum_values = True
        allow_population_by_field_name = True

    from_: AIChatSocketMessageFromEnum = Field(alias="from")
    type: AIChatSocketMessageTypeEnum

    text: str | None = None
    chunk: AIChunk | None = None
    recommended_products: RecommendedProductsData | None = None
    error: str | None = None

    param: AIChatSocketMessageParamEnum | None = None
    param_value: Any = None


class AIChatSocketAction(BaseModel):
    class Config:
        use_enum_values = True
        allow_population_by_field_name = True

    action_type: AIChatSocketActionTypeEnum

    message: AIChatSocketMessage | None = None
