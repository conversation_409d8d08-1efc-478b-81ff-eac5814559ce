from typing import Annotated

from openai import BaseModel
from pydantic import Field, validator


class AdUnitMutationData(BaseModel):
    id: Annotated[
        int,
        Field(
            nullable=True,
            description=(
                "For updating existing ad unit"
            )
        ),
    ] = None

    horizontal_video_id: Annotated[int, Field(nullable=True)] = None
    vertical_video_id: Annotated[int, Field(nullable=True)] = None

    @validator('horizontal_video_id')
    def horizontal_video_validator(cls, value, values):
        if not value and not values.get("vertical_video_id"):
            raise ValueError(
                "Either one of horizontal_video_id or "
                "vertical_video_id must be provided"
            )
        return value


class AdMutationData(BaseModel):
    units: Annotated[list[AdUnitMutationData], Field(min_items=1)]


class AdUnitSchema(BaseModel):
    id: int

    horizontal_video_id: Annotated[int, Field(nullable=True)] = None
    horizontal_video_url: Annotated[str, Field(nullable=True)] = None

    vertical_video_id: Annotated[int, Field(nullable=True)] = None
    vertical_video_url: Annotated[str, Field(nullable=True)] = None


class AdSchema(BaseModel):
    id: int
    units: list[AdUnitSchema]


class AdUnitSetAsShownResult(BaseModel):
    ad_unit_id: int
