import enum
from dataclasses import dataclass
from datetime import datetime

from fastapi import File, Form, UploadFile
from pydantic import Field

from .base import BaseORMModel


class ChatMessageSentByEnum(enum.Enum):
    USER = "user"
    MANAGER = "manager"
    VM = "vm"


class MessageContentTypeEnum(enum.Enum):
    PROCESSED = "processed"
    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    DOCUMENT = "document"
    AUDIO = "audio"
    VOICE = "voice"
    STICKER = "sticker"
    LOCATION = "location"
    CONTACT = "contact"
    TEMPLATE = "template"


class ChatMessageSchema(BaseORMModel):
    id: int
    chat_id: int

    sent_by: ChatMessageSentByEnum

    sent_by_name: str

    sent_by_user_id: int | None = Field(None, nullable=True)
    sent_by_user_name: str | None = Field(None, nullable=True)
    sent_by_user_email: str | None = Field(None, nullable=True)
    sent_by_user_photo_url: str | None = Field(None, nullable=True)

    vmc_id: int | None = Field(None, nullable=True)
    vm_name: str | None = Field(None, nullable=True)

    content_type: MessageContentTypeEnum
    text: str | None = Field(None, nullable=True)
    media_id: int | None = Field(None, nullable=True)
    media_url: str | None = Field(None, nullable=True)
    media_mime_type: str | None = Field(None, nullable=True)
    media_file_size: int | None = Field(None, nullable=True)
    media_preview_url: str | None = Field(None, nullable=True)
    media_original_file_name: str | None = Field(None, nullable=True)
    content: dict | None = Field(None, nullable=True)

    is_mailing: bool

    time_created: datetime


@dataclass
class SendChatMessageData:
    content_type: MessageContentTypeEnum = Form()

    text: str | None = Form(None, nullable=True)
    media_id: int | None = Form(None, nullable=True)
    media_url: str | None = Form(None, nullable=True)
    media_file: UploadFile | None = File(None, nullable=True)

    wa_master_template_id: int | None = Form(None, nullable=True)
    wa_template_variables: str | None = Form(None, nullable=True)
    wa_reply_buttons_data: str | None = Form(None, nullable=True)
