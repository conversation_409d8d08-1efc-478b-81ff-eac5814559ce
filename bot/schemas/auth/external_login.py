import enum
from pydantic import BaseModel, Field
from typing import Annotated, Any

from .all import AuthorisedResponse, Token
from .session import AuthSourceEnum


class ExternalLoginRequestStatusEnum(enum.Enum):
    CREATED = "created"
    STARTED = "started"
    SUCCESS = "success"
    CANCELED = "canceled"
    ERROR = "error"


class ExternalLoginRequestTypeEnum(enum.Enum):
    TELEGRAM = "telegram"
    WHATSAPP = "whatsapp"


class ExternalLoginRequestPurposeEnum(enum.Enum):
    AUTH = "auth"
    LINK = "link"
    RECEIPT = "receipt"
    INVITATION = "invitation"
    INCUST_EXTERNAL_ID = "incust_external_id"


class CreateExternalLoginData(BaseModel):
    type: ExternalLoginRequestTypeEnum
    purpose: ExternalLoginRequestPurposeEnum

    extra_data: Annotated[dict[str, Any] | None, Field(nullable=True)] = None

    continue_url: Annotated[str | None, Field(nullable=True)] = None

    auth_source: Annotated[AuthSourceEnum | None, Field(nullable=True)] = None
    device_info: Annotated[str | None, Field(nullable=True)] = None

    bot_id: int


class ExternalLoginSchema(BaseModel):
    uuid: str
    status: ExternalLoginRequestStatusEnum
    type: ExternalLoginRequestTypeEnum
    purpose: ExternalLoginRequestPurposeEnum

    auth_source: Annotated[AuthSourceEnum | None, Field(nullable=True)] = None
    device_info: Annotated[str | None, Field(nullable=True)] = None

    continue_url: Annotated[str | None, Field(nullable=True)] = None
    messanger_link: str
    incust_external_id: Annotated[str | None, Field(nullable=True)] = None

    logged_in_token_data: Annotated[Token | None, Field(nullable=True)] = None
    new_logged_in_data: Annotated[
        AuthorisedResponse | None, Field(nullable=True)
    ] = None
