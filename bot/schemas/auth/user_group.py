from typing import Literal

from pydantic import BaseModel, Field

from .scope import CreateScopeData, ScopeSchema
from ..base import BaseORMModel

UserGroupOwnerType = Literal["profile"]


class UserGroupSchema(BaseORMModel):
    id: int
    title: str

    owner_type: UserGroupOwnerType
    owner_profile_id: int


class CreateProfileUserGroupData(BaseModel):
    title: str
    scopes: list[CreateScopeData] | None = None
    users_ids: list[int] | None = None


class UserGroupCreated(BaseModel):
    user_group: UserGroupSchema
    scopes: list[ScopeSchema]


class UserGroupUpdated(UserGroupCreated):
    pass


class UpdateUserGroupData(BaseModel):
    title: str | None = None
    scopes: list[CreateScopeData] | None = None
    users_ids: list[int] | None = None


class UserGroupUserSchema(BaseORMModel):
    id: int
    name: str
    photo: str | None = None
    photo_url: str | None = None


class UserGroupSchemaWithUsers(UserGroupSchema):
    users: list[UserGroupUserSchema] = Field(default_factory=list)
    scope_names: list[str] = Field(default_factory=list)


class AddUsersToUserGroupData(BaseModel):
    users: list[int]
