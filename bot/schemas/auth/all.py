import enum
from datetime import datetime
from fastapi import File, Form, Query, UploadFile
from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass
from typing import Annotated, Literal

from schemas.base import BaseORMModel, EnumWithValues
from schemas.incust import IncustCustomerData
from .session import AuthSourceEnum
from ..bot import BotTypeLiteral
from ..templater import BaseTemplateSchema

ConfirmEmailPurposeLiteral = Literal[
    "register", "set_email", "reset_password", "change_email", "set_email_admin"]
ConfirmEmailFromAppLiteral = Literal["telegram", "web", "short_token", "incust"]
CrmBotTypeLiteral = Literal["service", "admin"]


class LoginSessionInfo(BaseModel):
    auth_source: AuthSourceEnum
    device_info: str


class ValidateTokenResult(BaseModel):
    sub: int | str
    type: str | None
    exp: datetime | None
    scopes: list[str] | None


class AuthorisationMethodEnum(enum.Enum):
    EMAIL = "email"
    TELEGRAM = "telegram"


class ClientEnum(EnumWithValues):
    WEB = "web"
    MESSANGER = "messanger"


class Token(BaseModel):
    token: str
    token_type: str


class ConfirmEmailData(BaseModel):
    token: str


class TokenData(BaseModel):
    user_id: int = None
    scopes: list[str] | None


class AuthorisedResponse(BaseModel):
    session_id: int
    expire_datetime: datetime
    auth_source: AuthSourceEnum
    device_info: str
    access_token: Token
    refresh_token: Token


class TelegramData(BaseModel):
    id: int
    first_name: str
    last_name: str | None = None
    username: str | None = None
    photo_url: str | None = None
    auth_date: int
    hash: str


class SetTelegramData(BaseModel):
    telegram_data: TelegramData
    password: str | None = None


class RemoveTelegramData(BaseModel):
    password: str | None = None


class RegisterEmailData(BaseModel):
    email: str
    password: str | None = None

    first_name: str
    last_name: str | None = None
    birth_date: str | None = None


class IncustData(BaseModel):
    token: str | None = None
    user: dict | None = None
    code: int | None = None
    message: str | None = None


class RegisterData(BaseModel):
    register_method: AuthorisationMethodEnum

    email_data: RegisterEmailData | None = None
    telegram_data: TelegramData | None = None

    is_accept_agreement: bool


class NewRegisterData(RegisterData, LoginSessionInfo):
    pass


class LoginEmailData(BaseModel):
    email: str
    password: str


class LoginData(BaseModel):
    login_method: AuthorisationMethodEnum

    email_data: LoginEmailData | None = None
    telegram_data: TelegramData | None = None

    need_activate: bool = False
    need_set_agreement_confirm: bool = False
    with_consent: bool | None = None


class NewLoginData(LoginData, LoginSessionInfo):
    pass


class DocsLoginData(BaseModel):
    email: str
    password: str


class ConfirmEmailRequestSent(BaseModel):
    sent: bool
    email: str
    token: str
    token_type: str


class ConfirmEmailResult(BaseModel):
    confirmed: bool
    email: str
    purpose: ConfirmEmailPurposeLiteral


class IsEmailExists(BaseModel):
    email: str
    is_exists: bool
    is_guest_user: bool | None = None
    is_accepted_agreement: bool = False


class IsChatIDExists(BaseModel):
    chat_id: int
    is_exists: bool
    is_activated: bool
    is_agreement_confirmed: bool


class SetEmailData(BaseModel):
    email: str
    password: str | None = None


class ChangeEmailData(BaseModel):
    new_email: str
    password: str | None = None
    confirm_token: str | None = None
    is_admin: bool = False


class ChangePasswordData(BaseModel):
    new_password: str
    current_password: str | None = None


class PasswordData(BaseModel):
    password: str


class UnlinkMessangerData(PasswordData):
    messanger_type: BotTypeLiteral


class ResetPasswordData(BaseModel):
    current_email: str
    new_password: str
    token: str | None = None


class ResetPasswordResult(BaseModel):
    ok: bool


class BaseUpdateUser(BaseModel):
    first_name: str | None = None
    last_name: str | None = None
    username: str | None = None
    photo: str | None = None
    db_timezone: str | None = None
    birth_date: datetime | None = None


class UpdateUser(BaseUpdateUser):
    email: str | None = None
    password: str | None = None
    chat_id: int | None = None
    wa_phone: str | None = None
    wa_name: str | None = None
    is_accepted_agreement: bool | None = None


class GetOrSetUserLangData(BaseModel):
    default_langs: list[str] | None = None


class UpdateUserData(BaseUpdateUser):
    lang: str | None = None


class LangData(BaseModel):
    lang: str


class FriendData(BaseModel):
    id: int
    user_id: int
    photo: str | None = None
    name: str | None = None
    email: str | None = None
    wa_phone: str | None = None
    username: str | None = None
    sending_possible: bool | None = None


@dataclass
class FriendInvoiceData:
    friend_id: int = Form()
    friend_comment: str | None = Form(None)
    friend_comment_media: UploadFile | None = File(None)
    order_id: int | None = Form(None)
    invoice_id: int | None = Form(None)


class BaseUserSchema(BaseORMModel):
    id: int
    lang: str

    email: str | None = None
    is_confirmed_email: bool | None = None
    is_only_email: bool
    is_messanger: bool
    client: ClientEnum

    chat_id: int | None = None
    username: str | None = None

    first_name: str | None = None
    last_name: str | None = None
    full_name: str | None = None

    wa_phone: str | None = None
    wa_name: str | None = None

    photo: str | None = None
    photo_url: str | None = None

    has_multiple_ids: bool
    birth_date: datetime | None = None
    name: str | None = None
    messangers: list | None = None

    is_system_user: bool


class UserSchema(BaseUserSchema):
    is_anonymous: bool
    is_password: bool

    db_timezone: str | None = None

    is_accepted_agreement: bool
    is_guest_user: bool | None = False
    incust_external_id: str | None = Field(None, nullable=True)
    marketing_consent: bool | None = None


class AccessToActionsSchema(BaseModel):
    actions: dict[str, bool] = Field(
        description="key is action and value is boolean: has access or not"
    )


@dataclass
class CheckAccessToActionsData:
    profile_id: int | None = Query(None)
    bot_id: int | None = Query(None)
    vm_id: int | None = Query(None)
    store_id: int | None = Query(None)
    category_id: int | None = Query(None)
    product_id: int | None = Query(None)
    product_group_id: int | None = Query(None)
    attribute_group_id: int | None = Query(None)
    characteristic_id: int | None = Query(None)


class UserIncustCustomerData(BaseModel):
    user_id: int
    incust_customer_data: IncustCustomerData | None = None


class IsEmailConfirmedData(BaseModel):
    email: str
    is_confirmed: bool


class LoginByShortTokenResult(BaseModel):
    token_data: Annotated[Token | None, Field(nullable=True)] = None
    new_login_data: Annotated[AuthorisedResponse | None, Field(nullable=True)] = None
    bot_id: int | None = None
    lang: str | None = None
    url_path: str | None = None
    params: dict | None = None


class SendConfirmEmailData(BaseModel):
    email: str
    purpose: ConfirmEmailPurposeLiteral
    user_id: int | None = None
    is_external_user: bool = False


class LoginByShortTokenData(BaseModel):
    short_token: str
    auth_source: AuthSourceEnum | None = None
    device_info: str | None = None


class OAuthLoginToken(BaseModel):
    access_token: str
    is_new_login: bool = False
    device_info: str | None = None
    auth_source: AuthSourceEnum | None = None


class OAuthRegisterToken(BaseModel):
    oauth_token: str


class NewOauthRegisterToken(OAuthRegisterToken, LoginSessionInfo):
    pass


class OAuthToken(Token):
    is_new_user: bool
    first_name: str | None = None
    last_name: str | None = None
    email: str | None = None
    is_accept_profile_agreement: bool | None = None


class NewOAuthToken(BaseModel):
    new_login_data: AuthorisedResponse
    is_new_user: bool
    first_name: str | None = None
    last_name: str | None = None
    email: str | None = None
    is_accept_profile_agreement: bool | None = None


class OAuthAppleLoginToken(OAuthLoginToken):
    first_name: str | None = None
    last_name: str | None = None
    is_native: bool = False
    is_new_login: bool = False
    device_info: str | None = None
    auth_source: AuthSourceEnum | None = None


class ConfirmEmailTemplate(BaseTemplateSchema):
    TEMPLATE_PATH = "confirm_email.html"

    header: str
    button_link: str
    button_name: str


class IsManagerOrAdmin(BaseModel):
    is_manager_or_admin: bool
    admin_count: int
    manager_count: int


class LoginByWebViewData(BaseModel):
    web_view_token: str
    bot_id: CrmBotTypeLiteral | int


class NewLoginByWebViewData(LoginSessionInfo):
    web_view_token: str
    bot_id: CrmBotTypeLiteral | int


class LoginByWebViewNewData(LoginByWebViewData, LoginSessionInfo):
    pass


class LoginByAuthTokenData(LoginSessionInfo):
    token: str


class MarketingInfo(BaseModel):
    consent_text: str


class MarketingConsentSchema(BaseModel):
    marketing_consent: bool
