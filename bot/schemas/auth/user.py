from fastapi import Query
from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass


class DeleteUserAccountData(BaseModel):
    current_password: str | None = Field(
        None, nullable=True,
        description="Current password, if password exists"
    )


class UserAccountDeleted(BaseModel):
    user_id: int


@dataclass
class GuestUserData:
    first_name: str
    last_name: str
    full_name: str
    username: str
    name: str

    @classmethod
    def fill(cls, guess_text: str):
        return cls(guess_text, guess_text, guess_text, guess_text, guess_text)


class GetUserAllowedActionsParams(BaseModel):
    """
    Any of the scope object id fields can be added here. They will be automatically used if passed
    """
    profile_id: int | None = Query(None, nullable=True, description="0 value will be ignored(as undefined)")
