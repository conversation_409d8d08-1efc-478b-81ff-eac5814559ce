import enum

from pydantic import BaseModel

from .all import Token


class ExternalCouponRequestStatusEnum(enum.Enum):
    CREATED = "created"
    STARTED = "started"
    SUCCESS = "success"
    CANCELED = "canceled"
    ERROR = "error"


class ExternalCouponRequestTypeEnum(enum.Enum):
    TELEGRAM = "telegram"


class CreateExternalCouponData(BaseModel):
    code: str
    type: str
    brand_id: int | None = None


class ExternalCouponSchema(BaseModel):
    code: str

    logged_in_token_data: Token | None = None
