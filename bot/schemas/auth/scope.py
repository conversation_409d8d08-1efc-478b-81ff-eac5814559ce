from datetime import datetime
from typing import Literal

from pydantic import BaseModel

from ..base import BaseORMModel

ScopeTarget = Literal["user", "user_group", "profile"]


class BaseScopeSchema(BaseORMModel):
    scope: str

    profile_id: int | None = None
    bot_id: int | None = None
    vm_id: int | None = None
    store_id: int | None = None
    category_id: int | None = None
    product_id: int | None = None
    product_group_id: int | None = None
    attribute_group_id: int | None = None
    characteristic_id: int | None = None
    attribute_id: int | None = None
    qr_menu_id: int | None = None

    expire_datetime: datetime | None = None


class UpdateScopeData(BaseModel):
    expire_datetime: datetime | None = None


class CreateScopeData(BaseScopeSchema):
    pass


class ScopeSchema(BaseScopeSchema):
    id: int
    target: ScopeTarget

    user_id: int | None = None
    user_group_id: int | None = None

    is_expired: bool
