from typing import Literal
from pydantic import BaseModel, Json

Oauth2Type = Literal["google", "apple"]


class OAuthGoogleUserInfo(BaseModel):
    id: str
    email: str
    verified_email: bool
    given_name: str
    family_name: str | None = None
    picture: str | None = None
    locale: str | None = None


class OAuthAppleUserInfo(BaseModel):
    email: str
    email_verified: bool


class OAuthRegisterData(BaseModel):
    first_name: str
    last_name: str
    lang: str
    email: str
    email_verified: bool
    photo: str | None = None
    oauth_type: Oauth2Type
    host: str | None = None
    continue_url: str | None = None
    profile_id: int | None = None


class Name(BaseModel):
    firstName: str | None = None
    lastName: str | None = None


class User(BaseModel):
    name: Name | None = None
    email: str | None = None


class AppleFormData(BaseModel):
    state: str
    code: str
    user: <PERSON><PERSON>[User] | None = None


OAuthInternalServiceType = Literal["admin", "client"]


class AppleState(BaseModel):
    host: str
    continue_url: str
    service: OAuthInternalServiceType
