from typing import Literal

from pydantic import BaseModel, Field

PaymentSettingsMethodLiteral = Literal[
    "custom", "cash", "incust_pay", "friend", "tg_pay", "bonuses"]


class BasePaymentSettings(BaseModel):
    id: int | None = Field(None)
    brand_id: int
    store_id: int | None = Field(None, description="")


class PaymentValues(BaseModel):
    label: str
    name: str
    value: str | None


class PaymentData(BaseModel):
    order_id: int | None = None
    result_url: str | None = None
    invoice_id: int | None = None
    store_id: int | None = None
    bot_id: int | None = None
    is_webview: bool | None = False
    lang: str | None = None
    order_token: str | None = None
    invoice_token: str | None = None
    args: list[PaymentValues | None] | None = None
    provider: str | None = None
    payment_settings_id: int | None = None
    comment: str | None = None


class PaymentSettingsMerchantDataSchema(BaseModel):
    id: int | None = None
    json_data: dict | None = None
    business_payment_data_id: int | None = None
    is_enabled: bool = Field(True, nullable=False)
    
    
class PaymentSettingsMerchantDataCreateSchema(BaseModel):
    json_data: dict | None = None
    business_payment_data_id: int | None = None
    is_enabled: bool = Field(True, nullable=False)
