from typing import Literal

from pydantic import BaseModel, Field

from schemas.payment_settings import BasePaymentSettings

UnitName = Literal[
    "Метр", "Квадратний метр", "Кубічний метр", "Кілометр", "Квадратний кілометр", "Кубічний кілометр",
    "Дециметр", "Квадратний дециметр", "Кубічний дециметр", "Сантиметр", "Квадратний сантиметр", "Кубічний сантиметр",
    "Гектар", "Літр", "Кілограм", "Грам", "Тонна метрична", "Центнер", "Кіловат-година", "Кіловат",
    "Центнер з гектара", "Голова", "Зошит", "Книга", "Примірник", "Квадратний метр житлової площі", "Штука", "Коробка",
    "Цистерна", "Ящик", "Пакет", "Пачка", "Рулон", "Гривня", "Кілометр за годину", "Погонний метр", "Виріб", "Комплект",
    "Доба", "Послуга", "Година", "Місяць", "пляшка", "Пapa", "Пopція", "Хвилина", "кг"
]
"""unit_name — (обов'язковий) найменування одиниці вимірювання. Повинно бути вказано корректне значення з Довідника."""

TaxList = Literal["А", "Б", "В", "Г"]
"""tax_list — (обов'язковий) список літер оподаткування, розділених комою (А - без ПДВ 0%, Б - ПДВ 20%, В - ПДВ 7%, Г - акциз 5%)"""


class ProductToLiqpay(BaseModel):
    item_name: str
    price: float
    unit_name: UnitName
    vndcode: str | None
    codifier: str | None
    tax_list: TaxList
    category_name: str
    barcode: str | None
    editable_price: Literal["T", "F"] | None
    weight_product: bool | None


"""
item_name — (обов'язковий) найменування товару
price — (обов'язковий) вартість за одиницю товару (число 18.2) у форматі <#.##>
vndcode — (необов'язковий) артикул
codifier — (обов'язковий тільки для певних типів податків — акцизу) значення з довідника УКТВЕД. Повинно містити тільки цифри, не повинно складатися тільки з 0
category_name — (обов'язковий) найменування категорії
barcode — (необов'язковий) штрих-код
editable_price — (необов'язковий) ознака редагування ціни (Т - так / F - ні)
weight_product — (необов'язковий) ознака вагового товару (Т - так / F - ні)
"""


class LiqpayItem(BaseModel):
    liqpay_unit_name: UnitName


class ProductFromLiqPay(BaseModel):
    id: str
    name: str
    codifier: str
    price: str
    unit_name: UnitName
    barcode: str | None
    vndcode: str | None


class Base64EncodedFile(BaseModel):
    filename: str = Field(..., description="File name")
    content_type: str = Field(..., description="MIME type")
    data: str = Field(..., description="Base64 file data")


class LiqPaySettings(BaseModel):
    public_key: str
    private_key: str
    is_need_fiscal: bool | None = Field(None, description="Is need fiscal receipt")
    tax_list: TaxList | None = Field(None, description="tax list")


class LiqPayPaymentSettings(BasePaymentSettings):
    json_data: LiqPaySettings
