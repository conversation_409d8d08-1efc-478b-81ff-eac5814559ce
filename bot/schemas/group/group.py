from typing import Annotated, Literal

from pydantic import BaseModel, Field

from core.ext.types import ExternalAPITypeExportLiteral, ExternalTypeLiteral

NotificationsTarget = Literal["CRM"]
NullableStr = Annotated[str | None, Field(nullable=True)]


class ExternalLoginTexts(BaseModel):
    title: NullableStr
    desktop_instruction: NullableStr
    open_in_messanger_button: str
    qr_instruction: NullableStr


class AuthTexts(BaseModel):
    helper_text: NullableStr
    external_login: ExternalLoginTexts


class ProductTexts(BaseModel):
    description_tab_text: str
    characteristics_tab_text: str
    attributes_tab_text: str


class CartTexts(BaseModel):
    additions_text: str


class CategoriesTexts(BaseModel):
    all_text: str
    choose_text: NullableStr


class MenuTexts(BaseModel):
    shop_info_text: str
    product: ProductTexts
    cart: CartTexts
    categories: CategoriesTexts


class PagesHeadersTexts(BaseModel):
    base: str
    select: str
    menu: str


class OrderTexts(BaseModel):
    sign_in_button: str
    guest_button: NullableStr
    or_text: NullableStr
    order_comment_label: NullableStr


class ReferralSystemTexts(BaseModel):
    info_button: str


class LoyaltyRegisterTexts(BaseModel):
    title: NullableStr
    description: NullableStr


class LoyaltyTexts(BaseModel):
    referral_system: ReferralSystemTexts
    register_: Annotated[LoyaltyRegisterTexts, Field(alias="register")]


class GroupTextsWeb(BaseModel):
    auth: AuthTexts
    menu: MenuTexts
    pages_headers: PagesHeadersTexts
    order: OrderTexts
    loyalty: LoyaltyTexts


class ReviewTexts(BaseModel):
    proposal_text: NullableStr
    leave_button: NullableStr
    leave_header: str
    authorisation_required_text: str


class GroupTexts(BaseModel):
    web: GroupTextsWeb
    review: ReviewTexts


class GroupSchema(BaseModel):
    id: int
    name: str
    timezone: str
    is_bot_connected: bool
    is_payments_available: bool

    lang: str
    country_code: str | None

    texts: GroupTexts
    is_ask_about_birthday: bool


class GroupConfigPositions(BaseModel):
    is_import_primary_products: bool | None = None
    is_import_primary_categories: bool | None = None
    is_import_primary_ags: bool | None = None


class ConfigImportPosterSettings(BaseModel):
    poster_api_token: str | None = None
    poster_app_secret: str | None = None
    # auto_update: bool = False
    poster_skip_stores: str | None = None
    poster_skip_desc_product: bool = False
    poster_regex_to_skip_category: str | None = None
    poster_tips_sku: str | None = None
    poster_brand_webhook_url: str | None = None


class ConfigImportIncustSettings(BaseModel):
    incust_selected_stores: list[str] | None = None


class ConfigImportGetOrderSettings(BaseModel):
    username: str | None = None
    password: str | None = None


class ConfigImportPromSettings(BaseModel):
    url: str | None = None
    prom_file_main_lang: str | None = None


class ConfigImportSheetsSettings(BaseModel):
    url: str | None = None
    export_url: str | None = None


class ConfigOpenAiSettings(BaseModel):
    data: list[str] | None = None
    is_save_last_prompt: bool | None = None
    ai_model: str | None = None
    user_product_image_prompt: str | None = None
    user_profile_image_prompt: str | None = None
    user_profile_logo_prompt: str | None = None
    user_store_image_prompt: str | None = None
    user_store_banner_prompt: str | None = None
    user_vm_step_prompt: str | None = None
    user_invoice_template_prompt: str | None = None


class ConfigOpenAiSettingsResponse(ConfigOpenAiSettings):
    ai_product_image_prompt: str | None = None
    ai_profile_image_prompt: str | None = None
    ai_profile_logo_prompt: str | None = None
    ai_store_image_prompt: str | None = None
    ai_store_banner_prompt: str | None = None
    ai_vm_step_prompt: str | None = None
    ai_invoice_template_prompt: str | None = None


class GroupConfigImport(BaseModel):
    import_source: ExternalTypeLiteral | None = None
    export_source: ExternalAPITypeExportLiteral | None = None
    poster_settings: ConfigImportPosterSettings | None = None
    incust_settings: ConfigImportIncustSettings | None = None
    get_order_settings: ConfigImportGetOrderSettings | None = None
    sheets_settings: ConfigImportSheetsSettings | None = None
    prom_settings: ConfigImportPromSettings | None = None


class GroupConfig(BaseModel):
    positions_config: GroupConfigPositions | None = None
    import_config: GroupConfigImport | None = None
    openai_config: ConfigOpenAiSettings | None = None
