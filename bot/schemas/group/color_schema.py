import enum
from typing import Literal, Annotated
from pydantic import BaseModel
from fastapi import Form

from ..base import BaseORMModel

ThemeMode = Literal["light", "dark"]


class ThumbnailsModeEnum(enum.Enum):
    NGINX = "nginx"
    SEVEN_LOC = "seven_loc"


class AppearanceColorSchema(BaseORMModel):
    bg_color: str
    secondary_bg_color: str
    primary_color: str
    secondary_color: str
    error_color: str
    warning_color: str
    text_color: str
    font: str | None = None
    use_telegram_theme: bool = True
    theme_mode: ThemeMode = "light"
    is_active: bool = True


class AppearanceColorSchemaFormData(BaseModel):
    bg_color: Annotated[str | None, Form(description="set null to reset value to default")] = None
    secondary_bg_color: Annotated[str | None, Form(description="set null to reset value to default")] = None
    primary_color: Annotated[str | None, Form(description="set null to reset value to default")] = None
    secondary_color: Annotated[str | None, Form(description="set null to reset value to default")] = None
    error_color: Annotated[str | None, Form(description="set null to reset value to default")] = None
    warning_color: Annotated[str | None, Form(description="set null to reset value to default")] = None
    text_color: Annotated[str | None, Form(description="set null to reset value to default")] = None
    font: Annotated[str | None, Form()] = None
    use_telegram_theme: Annotated[bool | None, Form()] = None
    theme_mode: Annotated[ThemeMode | None, Form()] = None
    is_active: Annotated[bool | None, Form()] = None
