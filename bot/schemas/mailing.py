import enum
from datetime import datetime
from typing import Annotated

from fastapi import Form, Query, UploadFile
from pydantic import BaseModel
from pydantic.dataclasses import dataclass

from .db.cursor.builtin.id import IDCursor


class MailingStatusEnum(enum.Enum):
    PENDING = "pending"
    CANCELED = "canceled"
    FINISHED = "finished"
    CREATED = "created"


class UpdateStatusEnum(enum.Enum):
    CANCELED = "canceled"


class MailingMessageStatusEnum(enum.Enum):
    CREATED = "created"
    PROCESSED = "processed"
    FAILED = "failed"
    PENDING = "pending"


class MailingChannelTypeEnum(enum.Enum):
    BOT = "bot"
    EMAIL = "email"
    CUSTOM = "custom"


class MailingSentInfo(BaseModel):
    total: int
    total_sent: int
    total_failed: int


class MailingMediaTypeEnum(enum.Enum):
    IMAGE = "image"
    VIDEO = "video"
    DOCUMENT = "document"
    TEXT = "text"


class CreateMailingSchema(BaseModel):
    channels: list[MailingChannelTypeEnum]
    description: str
    name: str
    message_info: dict
    filters: dict | None = None
    channels_settings: dict | None = None
    message_source_text: dict | None = None
    media: Annotated[UploadFile | str, None] = Form(None, nullable=True)
    media_type: MailingMediaTypeEnum = MailingMediaTypeEnum.TEXT
    media_id: int | None = None
    email_media_as_attachment: bool = True


class TestCreateMailingSchema(CreateMailingSchema):
    user_ids: list[int]


class MailingSchema(CreateMailingSchema):
    id: int
    status: MailingStatusEnum
    last_sent_datetime: datetime | None = None
    sent_info: MailingSentInfo | None = None
    message_source_text: dict | None = None
    media_url: str | None = None


class CreateMailingMessageSchema(BaseModel):
    bot_id: int | None = None
    user_id: int
    message: dict
    channel_type: MailingChannelTypeEnum
    channel_name: str
    email: str | None = None
    phone: str | None = None
    chat_id: int | None = None
    mailing_id: int
    user_name: str | None = None
    subject: str | None = None
    lang: str | None = None


class MailingMessageSchema(CreateMailingMessageSchema):
    id: int
    status: MailingMessageStatusEnum
    created_datetime: datetime
    start_datetime: datetime | None = None
    end_datetime: datetime | None = None
    error_details: dict | None = None
    retry_info: dict | None = None


class AdminMailingMessageListItem(BaseModel):
    id: int
    status: MailingMessageStatusEnum
    channel_type: MailingChannelTypeEnum
    channel_name: str


class AdminMailingListItem(BaseModel):
    id: int
    name: str
    status: MailingStatusEnum
    sent_info: MailingSentInfo | None = None
    channels: list[MailingChannelTypeEnum]


class AdminMailingCustomersCountInfo(BaseModel):
    channel: MailingChannelTypeEnum
    count: int
    is_wa_without_template: bool = False


class AdminUpdateMailing(BaseModel):
    status: UpdateStatusEnum


class MailingParams(BaseModel):
    limit: int
    offset: int | None = None
    cursor: IDCursor | None = None


class MailingCustomer(BaseModel):
    lang: str | None = None


class MailingUserBase(BaseModel):
    chat_id: int | None = None
    email: str | None = None
    wa_phone: str | None = None
    username: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    lang: str | None = None
    full_name: str | None = None


class MailingUser(MailingUserBase):
    id: int
    customer: MailingCustomer | None = None


class MailingMessage(BaseModel):
    id: int
    email: str | None = None
    phone: str | None = None
    chat_id: int | None = None
    user_name: str | None = None
    message: dict
    channel_type: MailingChannelTypeEnum
    status: MailingMessageStatusEnum
    lang: str | None = None
    mailing_id: int
    bot_id: int | None = None
    user_id: int


class MailingMessageQueryItemResultUser(BaseModel):
    wa_phone: str | None = None
    username: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    full_name: str | None = None


class MailingMessageQueryItemResultMailing(BaseModel):
    message_info: dict


class MailingMessageQueryItemResultBot(BaseModel):
    bot_type: str | None = None
    display_name: str | None = None
    token: str | None = None
    whatsapp_from: str | None = None


class MailingMessageQueryItemResultGroup(BaseModel):
    name: str


class MailingMessageQueryItemResult(MailingMessage):
    user: MailingMessageQueryItemResultUser
    mailing: MailingMessageQueryItemResultMailing
    client_bot: MailingMessageQueryItemResultBot
    group: MailingMessageQueryItemResultGroup


class MailingMessageQueryItem(MailingMessage, MailingUserBase):
    message_info: dict
    bot_type: str | None = None
    display_name: str | None = None
    token: str | None = None
    whatsapp_from: str | None = None
    profile_name: str


class MailingCustomerInfo(BaseModel):
    id: int
    accept_agreement: bool
    first_name: str | None = None
    last_name: str | None = None
    full_name: str | None = None
    channels: list[MailingChannelTypeEnum]
    is_owner: bool = False


@dataclass
class AdminCountCustomersListParams:
    channels: list[str] | None = Query(None, nullable=True)


class MailingMediaItem(BaseModel):
    media_id: int
    media_url: str
