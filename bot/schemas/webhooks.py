import enum
from datetime import date, datetime
from typing import Literal, <PERSON><PERSON><PERSON><PERSON>

from fastapi import Query
from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass

from .admin.base import AdminListParams
from .chat_message import ChatMessageSchema
from .crm.chats import CRMBaseChatSchema
from .crm.notifications import BaseCRMTextNotificationSchema
from .crm.reviews import CRMBaseReviewSchema
from .crm.tickets import CRMTicketListSchema, CRMTicketStatusHistoryObject
from .crm.user import CRMUser
from .invoice.invoice import InvoiceSchema
from .store.order import OrderPayment, OrderSchema


class WebhookSentStatusEnum(enum.Enum):
    SUCCESS = "success"
    FAILED = "failed"
    IDLE = "idle"


class WebhookJournalStatusEnum(enum.Enum):
    CREATED = "created"
    PROCESSED = "processed"
    FAILED = "failed"


class WebhookEntityEnum(enum.Enum):
    ORDER = "order"
    PAYMENT = "payment"
    TICKET = "ticket"
    REVIEW = "review"
    CHAT = "chat"
    TEXT_NOTIFICATION = "text_notification"
    EWALLET_PAYMENT = "ewallet_payment"


class WebhookActionEnum(enum.Enum):
    CREATED = "created"
    CHANGE_READ = "change_read"
    CHANGE_STATUS = "change_status"
    NEW_MESSAGE = "new_message"
    PAID = "paid"
    FAILED = "failed"


class WebhookUser(CRMUser):
    ...


class WebhookUserSchema(BaseModel):
    user: WebhookUser | None = None


class WebhookJournalDataRequestOrResponseSchema(BaseModel):
    data: dict | None = None


class WebhookJournalDataSchema(BaseModel):
    request: WebhookJournalDataRequestOrResponseSchema | None = None
    response: WebhookJournalDataRequestOrResponseSchema | None = None  # TODO: +
    # headers, status
    detail: str | None = None


class AdminWebhookSecretSchema(BaseModel):
    hook_id: str


class AdminWebhookBaseSchema(BaseModel):
    id: int
    endpoint_url: str
    blocked: bool
    is_enabled: bool


class AdminWebhookSchema(AdminWebhookBaseSchema):
    entities: list[str]
    last_sent_status: WebhookSentStatusEnum
    persistent_webhook: bool = Field(default=False)
    retries_count: int
    last_sent_datetime: datetime | None = Field(nullable=True, default=None)
    description: str | None = Field(nullable=True, default=None)


class AdminWebhookListSchema(AdminWebhookBaseSchema):
    entities: list[str]
    description: str | None = Field(nullable=True, default=None)


class AdminCreateOrUpdateBaseSchema(BaseModel):
    endpoint_url: str
    entities: list[str]
    is_enabled: bool
    description: str | None = Field(None, nullable=True)
    persistent_webhook: bool = Field(default=False, nullable=True)


class AdminCreateWebhookData(AdminCreateOrUpdateBaseSchema):
    ...


class AdminUpdateWebhookData(AdminCreateOrUpdateBaseSchema):
    ...


class WebhookDataBase(BaseModel):
    entity_id: int
    action: WebhookActionEnum
    event_id: str
    profile_id: int
    profile_name: str
    profile_lang: str


class WebhookTicketDataSchema(CRMTicketListSchema, WebhookUserSchema):
    status_history: list[CRMTicketStatusHistoryObject]


class WebhookReviewDataSchema(CRMBaseReviewSchema, WebhookUserSchema):
    change_date: datetime
    profile_id: int
    profile_name: str
    business_name: str
    bot_id: int | None = Field(None, nullable=True)
    bot_name: str | None = Field(None, nullable=True)
    first_name: str | None = Field(None, nullable=True)
    last_name: str | None = Field(None, nullable=True)
    full_name: str | None = Field(None, nullable=True)
    email: str | None = Field(None, nullable=True)
    photo_url: str | None = Field(None, nullable=True)
    menu_in_store_comment: str | None = Field(None, nullable=True)


class WebhookChatDataSchema(CRMBaseChatSchema, WebhookUserSchema):
    profile_id: int
    profile_name: str
    business_name: str
    bot_name: str | None = Field(None, nullable=True)
    first_name: str | None = Field(None, nullable=True)
    last_name: str | None = Field(None, nullable=True)
    full_name: str | None = Field(None, nullable=True)
    email: str | None = Field(None, nullable=True)
    photo_url: str | None = Field(None, nullable=True)


class WebhookTextNotificationSchema(BaseCRMTextNotificationSchema, WebhookUserSchema):
    ...


class WebhookOrderDataSchema(OrderSchema, WebhookUserSchema):
    desired_delivery_datetime: datetime | None = None
    desired_delivery_date: date | None = None


class WebhookOrderOrInvoicePayment(OrderPayment, WebhookUserSchema):
    incust_account_id: str | None = None
    incust_card_id: str | None = None


class WebhookPaymentDataSchema(InvoiceSchema, WebhookUserSchema):
    invoice_template_id: int | None = None
    menu_in_store_id: int | None = None
    payment_bot_menu_id: int | None = None
    payment_bot_id: int | None = None
    message_id: int | None = None
    paid_datetime: datetime | None = None
    uuid_id: str | None = None
    payment: WebhookOrderOrInvoicePayment | None = None


class WebhookEWalletPaymentDataSchema(WebhookUserSchema):
    external_id: str | None = None
    status: str | None = None
    payment_bot_id: int | None = None
    paid_datetime: datetime | None = None
    ewallet_payment_uuid: str | None = None
    error: str | None = None
    payment_settings_id: int | None = None


class WebhookNewChatMessageSchema(WebhookUserSchema):
    chat: WebhookChatDataSchema
    message: ChatMessageSchema


class WebhookOrderData(WebhookDataBase):
    entity: Literal[WebhookEntityEnum.ORDER]
    data: WebhookOrderDataSchema


class WebhookPaymentData(WebhookDataBase):
    entity: Literal[WebhookEntityEnum.PAYMENT]
    data: WebhookPaymentDataSchema


class WebhookChatData(WebhookDataBase):
    entity: Literal[WebhookEntityEnum.CHAT]
    data: WebhookNewChatMessageSchema


class WebhookReviewData(WebhookDataBase):
    entity: Literal[WebhookEntityEnum.REVIEW]
    data: WebhookReviewDataSchema


class WebhookNotificationData(WebhookDataBase):
    entity: Literal[WebhookEntityEnum.TEXT_NOTIFICATION]
    data: WebhookTextNotificationSchema


class WebhookTicketData(WebhookDataBase):
    entity: Literal[WebhookEntityEnum.TICKET]
    data: WebhookTicketDataSchema


class WebhookEWalletPaymentData(WebhookDataBase):
    entity: Literal[WebhookEntityEnum.EWALLET_PAYMENT]
    data: WebhookEWalletPaymentDataSchema


WebhookData: TypeAlias = (
        WebhookPaymentData | WebhookOrderData | WebhookTicketData |
        WebhookNotificationData | WebhookReviewData | WebhookChatData |
        WebhookEWalletPaymentData
)


class WebhookKafkaData(WebhookDataBase):
    entity: WebhookEntityEnum
    data: (WebhookTextNotificationSchema | WebhookTicketDataSchema |
           WebhookReviewDataSchema | WebhookNewChatMessageSchema |
           WebhookOrderDataSchema | WebhookPaymentDataSchema |
           WebhookEWalletPaymentDataSchema)


class WebhookEvent(BaseModel):
    group_id: int
    webhook_id: int
    hook_uuid: str
    data: WebhookKafkaData


class AdminWebhookJournalListSchema(BaseModel):
    id: int
    journal_uuid: str
    entity: WebhookEntityEnum
    entity_id: int
    action: WebhookActionEnum
    event_created_datetime: datetime
    event_start_datetime: datetime | None = Field(None, nullable=True)
    event_end_datetime: datetime | None = Field(None, nullable=True)
    json_data: WebhookJournalDataSchema | None = Field(None, nullable=True)
    status: WebhookJournalStatusEnum


class AdminResendWebhookEventData(BaseModel):
    entity: WebhookEntityEnum
    entity_id: int
    action: WebhookActionEnum
    event_id: str


@dataclass
class AdminWebhookJournalParams(AdminListParams):
    date_start: str | None = Query(None, nullable=True)
    date_end: str | None = Query(None, nullable=True)
