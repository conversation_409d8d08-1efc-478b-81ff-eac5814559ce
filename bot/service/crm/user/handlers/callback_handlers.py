from aiogram import Dispatcher

from core.helpers import send_user_link
from db.models import User


async def send_user_link_button_handler(
        _,
        callback_data: dict,
        user: User,
        lang: str
):
    receiver = user
    user = await User.get(callback_data.get("chat_id"))
    await send_user_link(receiver, user, lang)


def register_crm_user_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        send_user_link_button_handler,
        callback_mode="send_user_link",
        state="*",
    )
