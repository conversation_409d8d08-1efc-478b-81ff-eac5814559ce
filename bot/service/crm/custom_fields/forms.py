from aiogram.dispatcher import FSMContext

from db.models import <PERSON><PERSON>ield, CustomFieldValue, User, Group
from service.crm.custom_fields.states import EditCustomFields, AddCustomField
from psutils.forms import FieldsListForm, fields, WizardForm


async def choose_field_data_saver(data: dict, state: FSMContext):
    field_id = data.get("field_id")
    field = data.get("field", "")

    if field.capitalize() not in EditCustomFields.all_states:
        raise ValueError(f"unknown field: {field}")

    await state.update_data(field_id=field_id)
    await getattr(EditCustomFields, field.capitalize()).set()


class EditCustomFieldsForm(FieldsListForm):
    state_group = EditCustomFields

    choose_field = fields.InlineButtonsField(
        callback_mode="edit",
        callback_keys=["field", ("id", "field_id")],
        data_saver=choose_field_data_saver,
    )
    name = fields.TextField(max_length=25)
    value = fields.TextField()

    @classmethod
    async def data_saver(cls, data: dict, field_name: str, user: User, state_data: dict):
        field = await CustomField.get(state_data.get("field_id"))
        if field_name == "name":
            name = data.get("name")
            await field.update_name(name)
        elif field_name == "value":
            client_user_id = state_data.get("user_id")
            client_user = await User.get_by_id(client_user_id)
            value = data.get("value")
            await CustomFieldValue.set(client_user, field, value, user)


class AddCustomFieldsForm(WizardForm):
    state_group = AddCustomField
    previous_state = EditCustomFields.ChooseField.state
    back_to_previous_state_excluded_keys = ("user_id", "group_id")

    name = fields.TextField(max_length=25)
    value = fields.TextField()

    @classmethod
    async def data_saver(cls, data: dict, field_name: str, user: User, state_data: dict):
        if field_name == "name":
            name = data.get("name")
            if "field_id" in state_data:
                field = await CustomField.get(state_data.get("field_id"))
                await field.update_name(name)
            else:
                group = await Group.get(state_data.get("group_id"))
                await CustomField.get_or_create(group, name)
        elif field_name == "value":
            value = state_data.get("value")
            field = await CustomField.get(state_data.get("field_id"))
            client_user = await User.get_by_id(state_data.get("user_id"))
            await CustomFieldValue.set(client_user, field, value, user)
