from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Custom<PERSON>ield, User
from utils.router import Router
from utils.text import f, enter_text_with_specified_value

from utils.keyboards import get_previous_keyboard
from .list_drawers import CustomFieldsListDrawer

from .states import <PERSON><PERSON><PERSON><PERSON><PERSON>ields, AddCustomField


async def send_name_menu(message: types.Message, state: FSMContext, lang: str):
    cur_state = await state.get_state("")
    enter_or_edit = "enter" if cur_state.startswith(AddCustomField.__group_name__) else "edit"

    state_data = await state.get_data()
    field_id = state_data.get("field_id")

    text = await f(f"{enter_or_edit} custom field name header", lang)
    if field_id:
        field = await CustomField.get(state_data.get("field_id"))
        text = await enter_text_with_specified_value(text, field.name, lang)

    return await message.edit_text(text, reply_markup=await get_previous_keyboard(lang))


async def send_value_menu(message: types.Message, state: FSMContext, lang: str):
    cur_state = await state.get_state("")
    enter_or_edit = "enter" if cur_state.startswith(AddCustomField.__group_name__) else "edit"

    state_data = await state.get_data()
    field = await CustomField.get(state_data.get("field_id"))
    user = await User.get_by_id(state_data.get("user_id"))

    text = await f(
        f"{enter_or_edit} custom field value header", lang,
        field_name=field.name, user_full_name=user.name,
    )
    value = await field.get_last_value(user.id)
    if value:
        text = await enter_text_with_specified_value(text, value, lang)

    return await message.edit_text(text, reply_markup=await get_previous_keyboard(lang))


async def save_field(message: types.Message, state: FSMContext, lang: str):
    await EditCustomFields.ChooseField.set()
    await Router.state_menu(message, state, lang)


def register_crm_custom_fields_routes(router: Router):
    router.add_route(EditCustomFields.ChooseField, CustomFieldsListDrawer())
    router.add_route(EditCustomFields.Name, send_name_menu)
    router.add_route(EditCustomFields.Value, send_value_menu)

    router.add_route(AddCustomField.Name, send_name_menu)
    router.add_route(AddCustomField.Value, send_value_menu)
