from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from psutils.fsm import reset_excluding
from utils.router import Router
from ..states import Edit<PERSON>ustom<PERSON>ields, AddCustomField


async def edit_fields_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, lang: str,
):
    user_id = callback_data.get("user_id")
    group_id = callback_data.get("group_id")

    await state.finish()
    await state.update_data(user_id=user_id, group_id=group_id)

    await EditCustomFields.first()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def add_field_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, lang: str,
):
    await reset_excluding(state, "user_id", "group_id")
    await AddCustomField.first()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


def register_crm_custom_fields_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        edit_fields_button_handler,
        callback_mode="edit_fields",
        state="*",
    )

    dp.register_callback_query_handler(
        add_field_button_handler,
        callback_mode="add_field",
        state="*",
    )
