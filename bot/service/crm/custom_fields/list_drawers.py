from typing import Dict, Any, List

from aiogram.dispatcher import FSMContext

from db.models import User, CustomField
from utils.keyboards import previous_button
from utils.redefined_classes import InlineKb, InlineBtn
from utils.router.route_handlers import BaseListDrawer
from utils.text import f, c, empty_value


class CustomFieldsListDrawer(BaseListDrawer):
    row_width = 2
    config_page_size_variable_name = "FIELDS_LIST_PAGE_SIZE"

    need_setup_pagination_handler = True

    message_text_variable = "custom fields list header"
    empty_text_variable = "custom fields list empty header"

    need_replace_tag_in_search = False

    @classmethod
    async def object_drawer(cls, field: CustomField, user_id: int, keyboard: InlineKb, lang: str):
        button_text = await f("custom field name button", lang, name=field.name)
        keyboard.insert(InlineBtn(button_text, callback_data=c("edit", id=field.id, field="name")))

        value = await field.get_last_value(user_id) or await empty_value(lang)
        button_text = await f("custom field value button", lang, value=value)
        keyboard.insert(InlineBtn(button_text, callback_data=c("edit", id=field.id, field="value")))

    @classmethod
    async def footer_drawer(cls, keyboard: InlineKb, lang: str):
        keyboard.row(InlineBtn(await f("add custom field button", lang), callback_data="add_field"))
        keyboard.insert(await previous_button(lang))

    @classmethod
    async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
        state_data = await state.get_data()
        return {"user_id": state_data.get("user_id"), "group_id": state_data.get("group_id")}

    @classmethod
    async def make_get_objects_kwargs(
            cls,
            user: User,
            search_text: str,
            data_from_state: Dict[str, Any],
    ) -> Dict[str, Any]:
        group_id = data_from_state.get("group_id")
        return {"group_id": group_id}

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0,
            limit: int = None, *,
            operation: str = "all",
    ) -> List[Any] | int:
        group_id = get_objects_kwargs.get("group_id")
        return await CustomField.get_list_for_group(group_id, position, limit, operation)
