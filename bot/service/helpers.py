import traceback

from aiogram import types

from db.models import User

from utils.text import f, c
from utils.platform_admins import send_message_to_platform_admins
from utils.redefined_classes import Bo<PERSON>, InlineKb, InlineBtn


async def send_error(
        message_or_callback_query: types.Message | types.CallbackQuery = None,
        *,
        user_chat_id: int = None,
        user_full_name: str = None,
):
    """
    one of message or (user_chat_id and user_full_name) must be specified

    :param message_or_callback_query: message or callback_query.
        if type(message_or_callback_query) is types.CallbackQuery: alert will be shown
        elif type(message_or_callback_query) is types.Message:
            user_chat_id and user_full_name will be gor from message_or_callback_query
    :type message_or_callback_query: types.Message | types.CallbackQuery

    :param user_chat_id: user telegram id
    :type user_chat_id: int

    :param user_full_name: user full name
    :type user_full_name: str

    """

    if all([message_or_callback_query, user_chat_id, user_full_name]):
        raise ValueError("Only one message_or_callback_query or (user_chat_id, user_full_name) must be specified.")

    if not any([message_or_callback_query, user_chat_id, user_full_name]):
        raise ValueError("One of message_or_callback_query or (user_chat_id, user_full_name) must be specified.")

    message: types.Message | None
    callback_query: types.CallbackQuery | None

    if message_or_callback_query:
        if type(message_or_callback_query) is types.Message:
            message, callback_query = message_or_callback_query, None
            user_chat_id, user_full_name = message.chat.id, message.chat.full_name
        else:
            message, callback_query = None, message_or_callback_query
            user_chat_id, user_full_name = message.from_user.id, message.from_user.full_name
    else:
        callback_query = None

    lang = await User.get_lang(user_chat_id)

    if callback_query:
        callback_query = message_or_callback_query
        return await callback_query.answer(await f("error", lang), show_alert=True, cache_time=0)

    bot = Bot.get_current()
    bot_user = await bot.me

    keyboard_for_platform_admins = InlineKb()
    button_text = await f("username button", "ru", username=user_full_name)
    button = InlineBtn(button_text, callback_data=c("send_user_link", chat_id=user_chat_id))
    keyboard_for_platform_admins.insert(button)

    error_text = "".join(traceback.format_exc())
    platform_admins_text = await f(
        "error message to platform admins", "ru",
        user_full_name=user_full_name,
        user_chat_id=user_chat_id,
        bot_username=bot_user.username,
        error_text=error_text
    )
    await send_message_to_platform_admins(platform_admins_text, keyboard_for_platform_admins)

    kwargs = {
        "text": await f("error", lang)
    }
    await bot.send_message(user_chat_id, **kwargs)
