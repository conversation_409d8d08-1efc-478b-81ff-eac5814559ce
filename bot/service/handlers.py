import asyncio
from aiogram import Dispatcher
from aiohttp import web
from psutils.local import setup_psutils_localisation

import config as cfg
from core.aiogram_middlewares import (
    ExceptionHandlersMiddleware,
    LinkUncompressorMiddleware,
)
from core.aiogram_middlewares.cache_time import CacheTimeMiddleware
from core.aiogram_middlewares.callback_data import CallbackDataMiddleware
from core.aiogram_middlewares.create_or_update import CreateOrUpdateUserMiddleware
from core.handlers import *
from core.handlers import (
    register_general_callback_handlers,
    register_general_commands_handlers,
)
from core.kafka.producer import producer
from core.mailing.send_mailing_info_handler import register_mailing_info_button_handler
from core.user.agreement_processor.buttons_handlers import \
    setup_tg_handlers as setup_agreement_hadnlers
from utils.filters.bind import bind_general_filters
from utils.redefined_classes import Bot
from utils.router import Router
from .crm.custom_fields.forms import AddCustomFieldsForm, EditCustomFieldsForm
from .crm.custom_fields.handlers import *
from .crm.user.handlers import *
from .main.handlers.group import *
from .main.handlers.private import *
from .main.handlers.web import *
from .store.order.delivery.handlers import *
from .store.order.status.handlers import *


def bind_filters(dp: Dispatcher):
    bind_general_filters(dp)


def register_bot_handlers(dp: Dispatcher):
    Dispatcher.set_current(dp)

    dp.setup_middleware(ExceptionHandlersMiddleware())
    dp.setup_middleware(CreateOrUpdateUserMiddleware())
    dp.setup_middleware(LinkUncompressorMiddleware())
    dp.setup_middleware(CallbackDataMiddleware())
    dp.setup_middleware(CacheTimeMiddleware())

    register_main_group_messages_handlers(dp)

    register_main_commands_handlers(dp)
    register_general_commands_handlers(dp)

    register_mailing_info_button_handler(dp)

    EditCustomFieldsForm.setup_handlers(dp)
    AddCustomFieldsForm.setup_handlers(dp)

    dp["router"].setup_handlers()

    register_store_order_status_message_handlers(dp)

    register_main_message_handlers(dp)

    setup_agreement_hadnlers(dp)
    register_crm_custom_fields_callback_handlers(dp)
    register_crm_user_callback_handlers(dp)

    register_main_callback_handlers(dp)

    register_store_order_status_callback_handlers(dp)
    register_store_delivery_status_menu_order_callback_handlers(dp)

    register_general_callback_handlers(dp)


def register_web_handlers(app: web.Application, dp: Dispatcher):
    register_main_web_handlers(app, dp)
    register_general_exception_handlers(dp)


async def on_startup(dp: Dispatcher):
    await setup_psutils_localisation()

    await producer.initialise()

    Router.set_current(dp["router"])
    Dispatcher.set_current(dp)
    Bot.set_current(dp.bot)

    webhook_path = (f"http://{cfg.WEBHOOK_HOST}:{cfg.ROOT_BOT_PORT + 2}/service_bot/"
                    f"{cfg.SERVICE_BOT_API_TOKEN}")
    asyncio.ensure_future(dp.bot.set_webhook(webhook_path))


async def on_shutdown():
    await producer.stop()


__all__ = [
    "register_bot_handlers",
    "register_web_handlers",
    "on_startup",
    "on_shutdown",
    "bind_filters",
]
