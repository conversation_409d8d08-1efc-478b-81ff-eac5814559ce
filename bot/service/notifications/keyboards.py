from aiogram import types

from core.helpers import get_crm_chat_link
from db import crud
from db.models import User
from schemas import ChatTypeE<PERSON>

from utils.redefined_classes import InlineBtn, InlineKb
from utils.text import c, f


async def get_service_bot_notification_base_keyboard(
        user: User,
        group_id: int,
        bot_id: int,
        lang: str
):
    keyboard = InlineKb(resize_keyboard=True)

    if user.chat_id:
        keyboard.row()

        button_text = await f("chat link button", lang)
        callback_data = c("send_user_link", chat_id=user.chat_id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if user.chat_id or user.wa_phone:
        chat = await crud.get_or_create_chat(
            ChatTypeEnum.USER_WITH_GROUP,
            user.id,
            group_id,
            bot_id,
        )

        keyboard.row(
            types.InlineKeyboardButton(
                text=await f("service bot chat in crm button", lang),
                web_app=types.WebAppInfo(
                    url=get_crm_chat_link(chat.id)
                )
            )
        )

    return keyboard
