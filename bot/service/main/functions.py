import re

from aiogram import types

from db.models import User

from utils.redefined_classes import InlineBtn, InlineKb
from utils.text import f


async def send_support_link(
        message: types.Message,
        prev: bool = False, mode: str = "new"
):
    lang = await User.get_lang(message.chat.id)
    support_link = await f("default support link", lang)

    keyboard = InlineKb()
    if re.fullmatch(r"\[.+]", support_link):
        support_link = support_link[1:-1]
        variable = "support link in button header"
        keyboard.insert(InlineBtn(await f("support link button", lang), url=support_link))
    else:
        variable = "support link in text header"
    if prev:
        keyboard.insert(InlineBtn(await f("previous button", lang), callback_data="support"))
    text = await f(variable, lang, support_link=support_link)
    if mode == "edit":
        return await message.edit_text(text, reply_markup=keyboard)
    await message.answer(text, reply_markup=keyboard)


async def send_support_menu(message: types.Message):
    return await send_support_link(message)
