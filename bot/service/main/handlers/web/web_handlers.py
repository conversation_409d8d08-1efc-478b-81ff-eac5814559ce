import asyncio

import config as cfg

from functools import partial

from aiohttp import web
from aiogram import Dispatcher, types

from core.helpers import process_update
from utils.redefined_classes import Bot


async def updates_handler(request: web.Request, dp: Dispatcher):
    Dispatcher.set_current(dp)
    Bot.set_current(dp.bot)
    json_data = await request.json()
    update = types.Update.to_object(json_data)
    try:
        await process_update(dp, update, token=cfg.SERVICE_BOT_API_TOKEN)
    finally:
        return web.Response()


def register_main_web_handlers(app: web.Application, dp: Dispatcher):
    handler = partial(updates_handler, dp=dp)
    app.router.add_post(f"/service_bot/{cfg.SERVICE_BOT_API_TOKEN}", handler)
