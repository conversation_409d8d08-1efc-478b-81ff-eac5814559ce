import logging

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.types import ContentTypes

from service.main.keyboards import get_menu_keyboard

from utils.text import f

logger = logging.getLogger()


async def unhandled_cancel_button_handler(message: types.Message, state: FSMContext, lang: str):
    await state.finish()
    keyboard = await get_menu_keyboard()
    await message.answer(await f("action cancel text", lang), reply_markup=keyboard)


async def unhandled_messages_handler(message: types.Message, state: FSMContext, lang: str):
    await state.finish()
    keyboard = await get_menu_keyboard()
    await message.answer(await f("text unknown command", lang), reply_markup=keyboard)


def register_main_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        unhandled_cancel_button_handler,
        lequal="action cancel button",
        content_types=ContentTypes.TEXT, state="*"
    )

    dp.register_message_handler(
        unhandled_messages_handler,
        content_types=ContentTypes.ANY, state="*"
    )
