from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from core.helpers import send_change_lang_menu

from db.models import User

from utils.text import f
from utils.translator import Translator

from ...functions import send_support_menu
from ...keyboards import get_menu_keyboard


async def cancel_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str, ):
    await state.finish()
    await callback_query.answer(await f("action cancel text", lang), show_alert=True)


async def change_lang_button_handler(callback_query: types.CallbackQuery, callback_data: dict, user: User, ):
    new_lang = callback_data.get("lang")

    lang = await user.set_lang(new_lang)
    await send_change_lang_menu(callback_query.message, lang, mode="edit")

    new_lang_text = await Translator.get_language_name(lang, lang)
    text = await f("language changed", lang, new_lang=new_lang_text)

    keyboard = await get_menu_keyboard()
    await callback_query.message.answer(text, reply_markup=keyboard)


async def support_button_handler(callback_query: types.CallbackQuery):
    await send_support_menu(callback_query.message)


def register_main_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        cancel_button_handler,
        cancel_button=True,
        state="*",
    )

    dp.register_callback_query_handler(
        change_lang_button_handler,
        callback_mode="change_lang",
        state="*",
    )

    dp.register_callback_query_handler(
        support_button_handler,
        callback_mode="support",
        state="*",
    )
