from typing import Callable

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from psutils.web_app import check_if_changed_and_set_menu_button

from config import CRM_HOST
from db.models import User

from utils.text import f

from core.helpers import send_change_lang_menu

from ...keyboards import get_menu_keyboard


async def cmd_start(message: types.Message, state: FSMContext, user: User, lang: str):
    web_crm_button = types.MenuButtonWebApp(
        text=await f("service web crm button", lang),
        web_app=types.WebAppInfo(
            url=CRM_HOST,
        )
    )
    await check_if_changed_and_set_menu_button(message.bot, web_crm_button, user.chat_id)

    await state.finish()
    keyboard = await get_menu_keyboard()
    await message.answer(await f("service bot hello text", lang), reply_markup=keyboard)


async def cmd_cancel(message: types.Message, state: FSMContext, lang: str):
    await state.finish()
    keyboard = await get_menu_keyboard()
    await message.answer(await f("action cancel text", lang), reply_markup=keyboard)


async def cmd_change_lang(message: types.Message, state: FSMContext, lang: str):
    await state.finish()
    await send_change_lang_menu(message, lang)


def register_main_commands_handlers(dp: Dispatcher):
    def register_command(handler: Callable, commands: str | list, **kwargs):
        if not isinstance(commands, list):
            commands = [commands]
        dp.register_message_handler(handler, commands=commands, **kwargs, state="*")

    register_command(cmd_start, "start")
    register_command(cmd_cancel, "cancel")
    register_command(cmd_change_lang, ["changelang", "change_lang"])
