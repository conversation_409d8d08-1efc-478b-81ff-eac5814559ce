from aiogram import Dispatcher, types
from aiogram.types import ContentTypes

from utils.filters.simple import not_private_chat_filter


async def leave_group_handler(message: types.Message):
    await message.chat.leave()


def register_main_group_messages_handlers(dp: Dispatcher):
    dp.register_message_handler(leave_group_handler, not_private_chat_filter, content_types=ContentTypes.ANY, state="*")
