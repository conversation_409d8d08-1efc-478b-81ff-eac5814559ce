from psutils.convertors import date_to_str

from db import crud
from db.models import (BrandCustomSettings, ExternalOrder, Group, OrderShipment, StoreOrder)
from schemas import ExternalSystemTypes
from utils.numbers import format_currency
from utils.text import f
from utils.translator import t


async def get_order_ext_txt(order_id, lang: str) -> str:
    ext_order_text = ''
    order_ext = await ExternalOrder.get_by_store_order_id(order_id)
    if order_ext:
        ext_order_status = await f(f"store brand order ext status {order_ext.status} text", lang)
        ext_order_text = '{}: {}'.format(await f('store brand order ext status text', lang), ext_order_status)
        try:
            if order_ext.status == 'failed' and order_ext.json_data:
                if order_ext.external_type == ExternalSystemTypes.get_order.value:
                    ext_order_err = str(order_ext.json_data.get('data', {}).get('history')[0].get('resp'))
                else:
                    ext_order_err = str(order_ext.json_data)
                ext_order_text += '\n{}'.format(ext_order_err)
        except:
            pass
    return ext_order_text


async def get_cash_or_card_payment_text(
        lang: str,
        is_cash: bool,
):
    if is_cash:
        text = await f("call waiter payment cash text", lang, comment="")
    else:
        text = await f("call waiter payment card text", lang, comment="")

    return text


async def get_store_order_delivery_text(
        order: StoreOrder,
        shipment: OrderShipment,
        group: Group | None,
        lang: str,
) -> str:
    settings = await BrandCustomSettings.get(shipment.settings_id)

    address_texts = []

    address = order.delivery_address
    if order.map_link:
        address = f'<a href="{order.map_link}">{order.delivery_address}</a>'

    if settings.is_custom_shipment:
        if group:
            translated = await t(
                settings, lang, group.lang,
                group_id=group.id,
                is_auto_translate_allowed=group.is_translate,
            )
            shipment_name = translated["name"]
            shipment_label_comment = translated["label_comment"]
        else:
            shipment_name = shipment.name
            shipment_label_comment = shipment.label_comment

        if shipment.comment:
            if not shipment_label_comment:
                shipment_label_comment = await settings.get_label_comment(lang)
            address_texts.append(
                ": ".join(
                    (
                        shipment_label_comment,
                        shipment.comment,
                    )
                )
            )

        if order.delivery_address:
            address_texts += [await f(
                "store brand delivery address notifications text", lang,
            ), address]
    else:
        shipment_name = await f(f"store {shipment.base_type} text", lang)

        if order.delivery_address:
            address_texts += [await f(
                "store brand delivery address notifications text", lang,
            ), address]
        if order.address_flat:
            address_texts += [
                await f("store brand delivery address flat notifications text", lang),
                order.address_flat,
            ]
        if order.address_floor:
            address_texts += [
                await f("store brand delivery address floor notifications text", lang),
                str(order.address_floor)
            ]
        if order.address_entrance:
            address_texts += [
                await f("store brand delivery address entrance notifications text", lang),
                str(order.address_entrance),
            ]
        if order.address_comment:
            address_texts.append(order.address_comment)

    shipment_name = ": ".join(
        (
            await f("store brand delivery method notifications text", lang),
            shipment_name,
        )
    )

    texts = [shipment_name]
    if not group:
        group = await crud.get_group_by_store(order.store_id)

    if shipment.price:
        shipment_cost_field_name = await f("store brand delivery cost notifications text", lang)
        shipment_cost_str = format_currency(shipment.price, order.currency, locale=group.lang)
        texts.append(": ".join((shipment_cost_field_name, shipment_cost_str)))

    texts.extend(address_texts)
    text = "\n".join(texts)
    text = text.replace("\n\n", "\n")
    return text


async def get_desired_delivery_text(order: StoreOrder, lang: str):
    desired_delivery_values = []
    shipment = await crud.get_order_shipment(order.id)
    if order.desired_delivery_date:
        desired_delivery_values.append(date_to_str(order.desired_delivery_date))
    if order.desired_delivery_time and shipment.delivery_datetime_mode == "datetime":
        desired_delivery_values.append(order.desired_delivery_time)

    if desired_delivery_values:
        field_name = await f("store brand desired delivery date notifications text", lang)
        return ": ".join((field_name, " ".join(desired_delivery_values)))
    return None
