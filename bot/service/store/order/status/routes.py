import logging

from aiogram import types
from aiogram.dispatcher import FSMContext
from psutils.exceptions import ErrorWithTextVariable
from psutils.fsm import pop_keys_from_state

from core.store.order.service import change_store_order_status
from db.models import User
from utils.router import Router
from utils.text import f
from .states import SaveStoreOrderState


async def set_status_menu(
        message: types.Message, state: FSMContext, user: User, lang: str = 'en'
):
    state_data = await state.get_data()

    order_id = state_data.get("order_id")
    status = state_data.get("status")
    comment = state_data.get("comment", None)

    manager_user = user
    # await User.get(chat_id=state.user)

    try:
        await change_store_order_status(
            order_id, status,
            "manager", comment,
            'service bot', manager_user,
            message, initiated_by_user=manager_user,
        )
    except ErrorWithTextVariable as ex:
        # if callback_query:
        #     return await callback_query.answer(str(ex), show_alert=True)
        # else:
        return await message.answer(await f(ex.text_variable, lang, **ex.text_kwargs))
    except Exception as ex:
        logging.error(ex, exc_info=True)
        return await message.answer(await f("web store unknown error text", lang))

    await state.reset_state(with_data=False)

    await pop_keys_from_state(state, "order_id", "comment")


def register_store_status_order_routes(router: Router):
    router.add_route(SaveStoreOrderState.Save, set_status_menu)
