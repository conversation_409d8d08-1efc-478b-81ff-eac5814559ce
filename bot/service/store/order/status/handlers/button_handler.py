from aiogram import Dispatcher
from aiohttp import web

from db.models import StoreOrder
from psutils.func import check_function_spec
from utils.router import Router
from utils.type_vars import FuncT


def store_order_button_handler(func: FuncT):

    async def wrapper(order_id: int, status: str, **kwargs):
        user = kwargs.get("user")
        lang = user.lang

        dp = Dispatcher.get_current()
        bot = dp.bot
        state = dp.current_state(user=user.chat_id)
        try:
            message = await Router.get_state_message(state)
        except:
            message = await bot.send_message(user.chat_id, text=".")

        kwargs.update({
            "dp": dp,
            "message": message,
            "state": state,
            "order_id": order_id,
            "status": status,
            "lang": lang,
        })

        result = await func(**check_function_spec(func, kwargs))

        if not result:
            return web.Response()

    return wrapper
