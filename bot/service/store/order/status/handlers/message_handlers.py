from aiogram import Dispatcher, types
from aiogram.types import ContentTypes
from aiogram.dispatcher import FSMContext

from utils.text import f
from psutils.fsm import pop_keys_from_state
from utils.router import Router

from service.main.keyboards import get_menu_keyboard

from ..states import SaveStoreOrderState


async def cancel_button_handler_(message: types.Message, state: FSMContext, lang: str):
    await state.reset_state(with_data=False)
    await pop_keys_from_state(state, "order_id", "comment")

    keyboard = await get_menu_keyboard()
    await message.answer(await f("action cancel text", lang), reply_markup=keyboard)


async def comment_skip_button_button_handler_(_: types.Message, state: FSMContext, lang: str):
    await SaveStoreOrderState.next()
    await Router.state_menu(state=state, lang=lang, get_state_message=True)


async def check_comment_handler_(message: types.Message, state, lang: str):
    if message.content_type != "text":
        return await message.answer(await f("unsupported system format", lang))

    await SaveStoreOrderState.next()
    await state.update_data(comment=message.text)
    await Router.state_menu(state=state, lang=lang, get_state_message=True)


def register_store_order_status_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cancel_button_handler_,
        cancel_button=True,
        state=SaveStoreOrderState,
    )

    dp.register_message_handler(
        comment_skip_button_button_handler_,
        lequal="comment skip button",
        state=SaveStoreOrderState,
    )

    dp.register_message_handler(
        check_comment_handler_,
        content_types=ContentTypes.ANY,
        state=SaveStoreOrderState.Comment,
    )
