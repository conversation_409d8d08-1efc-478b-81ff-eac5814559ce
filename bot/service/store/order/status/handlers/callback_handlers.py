from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.router import Router
from db.models import User

from .united_handlers import handle_store_order_status
from ..states import SaveStoreOrderState
from ....notifications import process_order_message_collapse_or_expand_button


async def store_order_expand_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        user: User,
):
    store_order_id = callback_data.get("o_id")
    return await process_order_message_collapse_or_expand_button(
        callback_query.message, user,
        store_order_id, False,
    )


async def store_order_collapse_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        user: User,
):
    store_order_id = callback_data.get("o_id")
    return await process_order_message_collapse_or_expand_button(
        callback_query.message, user,
        store_order_id, True,
    )


async def store_order_status_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, lang: str,
):
    order_id = callback_data.get("order_id")
    status = callback_data.get("status")
    return await handle_store_order_status(callback_query, state, lang, order_id, status)


async def skip_check_button_handler(state: FSMContext, lang: str):
    await SaveStoreOrderState.next()
    await Router.state_menu(state=state, lang=lang, get_state_message=True)


def register_store_order_status_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        store_order_status_button_handler,
        callback_mode="store_order_status",
        state="*",
    )

    dp.register_callback_query_handler(
        skip_check_button_handler,
        skip_button=True,
        chat_type="private",
        state=SaveStoreOrderState.Comment,
    )

    dp.register_callback_query_handler(
        store_order_expand_button_handler,
        callback_mode="store_order_expand",
        state="*",
    )

    dp.register_callback_query_handler(
        store_order_collapse_button_handler,
        callback_mode="store_order_collapse",
        state="*",
    )
