from aiogram import types
from aiogram.dispatcher import FSMContext

from utils.router import Router
from ..states import SaveStoreOrderState


async def handle_store_order_status(
        obj: types.Message | types.CallbackQuery,
        state: FSMContext, lang: str,
        order_id: int,
        status: str, data: dict | None = None,
):
    if data is None:
        data = {}

    if data and len(data) > 0:
        comment = data.get('comment', None)
    else:
        state_data = await state.get_data()
        comment = state_data.get("comment", None)

    await state.finish()
    await state.update_data(
        order_id=order_id,
        status=status,
        comment=comment,
    )

    await state.set_state(SaveStoreOrderState.Save)  # must be state.set_state because used for web

    await Router.state_menu(obj, state, lang, set_state_message=True)
