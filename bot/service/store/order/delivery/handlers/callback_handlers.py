from aiogram import Dispatcher, types

from db import crud
from db.models import StoreOrder
from service.helpers import send_error
from utils.text import f
from ..keyboards import get_delivery_keyboard


async def delivery_status_menu_order_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        lang: str,
):
    order_id = callback_data.get("order_id")
    order = await StoreOrder.get(order_id)
    if order is None:
        return await send_error(callback_query)

    shipment = await crud.get_order_shipment(order.id)
    if shipment.base_type != 'delivery':
        return await callback_query.answer(await f("order is not has delivery method", lang), show_alert=True)

    keyboard = await get_delivery_keyboard(order.id, lang)
    await callback_query.message.reply(await f("crm button change status text", lang), reply_markup=keyboard)


def register_store_delivery_status_menu_order_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        delivery_status_menu_order_handler,
        callback_mode="del_status_mo",
        state="*",
    )
