from schemas import OrderShippingStatusEnum
from db.models import StoreOrder
from utils.text import c, f
from utils.redefined_classes import InlineKb, InlineBtn


async def get_delivery_keyboard(order_id: int, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)
    order = await StoreOrder.get(order_id)

    for delivery_status in (
        # OrderShippingStatusEnum.WAIT_FOR_SHIP.value,
        OrderShippingStatusEnum.SHIPPED.value,
        OrderShippingStatusEnum.IN_TRANSIT.value,
        OrderShippingStatusEnum.DELIVERED.value,

    ):
        if order.shipment_status in (
            OrderShippingStatusEnum.SHIPPED.value,
            OrderShippingStatusEnum.IN_TRANSIT.value
        ) and delivery_status == OrderShippingStatusEnum.SHIPPED.value:
            continue
        button_text = await f(f'email store order {delivery_status} status text', lang)
        callback_data = c("store_order_status", order_id=order_id, status=delivery_status)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return keyboard
