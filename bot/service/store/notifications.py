import logging

from aiogram import types
from psutils.convertors import datetime_to_str
from psutils.date_time import localise_datetime

from config import CRM_HOST, SERVICE_BOT_API_TOKEN, SERVICE_BOT_USERNAME
from core.kafka.producer.functions import add_telegram_notifications_for_action
from core.store.order.functions import get_pmt_info
from core.store.order.messages_funcs import (
    get_order_end_price_text, get_payment_delivery_text, get_products_data,
    get_products_text,
)
from db import crud
from db.models import (
    Brand, Group, MenuInStore, OrderShippingStatus, Store, StoreOrder,
    User,
)
from service.notifications.keyboards import get_service_bot_notification_base_keyboard
from utils.platform_admins import send_message_to_platform_admins
from utils.redefined_classes import InlineBtn, InlineKb
from utils.text import c, f, fl
from .functions import get_order_ext_txt
from .order.keyboards import add_store_order_actions_buttons

debugger = logging.getLogger('debugger.notifications')


async def get_expand_keyboard(store_order_id: int, lang: str):
    keyboard = InlineKb(row_width=1)

    web_app = types.WebAppInfo(
        url=f"{CRM_HOST}/order/{store_order_id}?listType=inbox&itemIdField=orderId"
    )
    keyboard.row(InlineBtn(await f('manage store order button', lang), web_app=web_app))

    button_text = await f("web store expand button text", lang)
    callback_data = c(
        "store_order_expand",
        o_id=store_order_id,
    )
    keyboard.row(InlineBtn(button_text, callback_data=callback_data))

    return keyboard


async def get_collapse_keyboard(store_order_id: int, lang: str) -> InlineKb:
    keyboard = InlineKb()
    button_text = await f("web store collapse button text", lang)
    callback_data = c(
        "store_order_collapse",
        o_id=store_order_id,
    )
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))
    return keyboard


async def __get_store_order_notification_keyboard__(
        order: StoreOrder, lang: str, store: Store, bot_id: int | None,
        group: Group | None, user: User | None
):
    if store.external_type in ("poster", "get_order"):
        debugger.debug(f"{store.external_type=} do not need keyboard for manage order")
        return None, None

    keyboard = InlineKb()
    await add_store_order_actions_buttons(order, keyboard, lang)

    answer_keyboard = await get_service_bot_notification_base_keyboard(
        user, group.id, bot_id, lang,
    )

    if answer_keyboard.inline_keyboard:
        for row in answer_keyboard.inline_keyboard:
            keyboard.inline_keyboard.append(row)

    return keyboard


async def __get_notification_text__(
        group: Group,
        store: Store,
        order: StoreOrder,
        order_shipping_status: OrderShippingStatus,
        lang: str,
        order_user: User | None = None,
        is_expanded: bool | None = None,
        is_payment_error: bool | None = None,
        is_payed_new_order: bool | None = None,
) -> str:
    brand = await Brand.get(store.brand_id)

    payment_error_text = ""
    if is_payment_error:
        payment_error_text = await f("payment error text", lang)
        header_order_text = await f("manager payment error text", lang, order_id=order.id)
    else:
        header_order_text = await f(f"manager {order_shipping_status.status} text", lang, order_id=order.id)

    if order.type == "gift":
        header_order_text = f"{await f('manager order gift text', lang)}\n{header_order_text}"

    if order.type == "topup":
        header_order_text = f"{await f('manager order topup text', lang)}\n{header_order_text}"

    order_status = order_shipping_status.status if order_shipping_status else "default"
    order_status_text = await f(f"store brand order status {order_status} text", lang)

    is_regular_payed_full_bonuses = order_status == "payed" and order.type == "regular" and order.total_sum == order.discount_and_bonuses

    if (order_status != 'open_unconfirmed' and (is_expanded is None or is_expanded)
            and order.type != "gift"
            and order.total_sum != 0 and order.total_sum != order.discount and not is_regular_payed_full_bonuses
            and not is_payed_new_order):
        return f"{header_order_text}\n{order_status_text}"

    if is_payment_error:
        order_status_pay = "payment_error"
    elif order.status_pay:
        order_status_pay = order.status_pay
    else:
        order_status_pay = "default"
    order_status_pay = await f(f"store brand order status pay {order_status_pay} text", lang)

    brand_store_name = "/".join([order.brand_name, store.name])

    order_time = localise_datetime(order.create_date, group.timezone)
    order_time = datetime_to_str(order_time)

    contacts = await get_contacts(order, lang)
    payment_delivery_text = await get_payment_delivery_text(order, group, lang)

    ext_order_text = await get_order_ext_txt(order.id, lang)
    pmt_info = await get_pmt_info(order.id, lang)

    order_end_price_text = await get_order_end_price_text(group, order, store.currency, lang)

    menu_in_store = await MenuInStore.get(order.menu_in_store_id) if order.menu_in_store_id else None

    text_kwargs = {
        "header_order_text": header_order_text,
        "brand_store_name": brand_store_name,
        "fullname": order_user.name if not order_user.is_anonymous else await f(
            "anonymous user text", lang,
        ),
        "order_id": order.id,
        "order_time": order_time,
        "order_status": order_status_text,
        "order_status_pay": order_status_pay,
        "order_end_price": order_end_price_text,
        "order_products": f"\n- - - - - - - -\n"
                          f"{await get_products_text(await get_products_data(order, brand, store, lang))}"
                          f"\n- - - - - - - -\n",
        "contacts": contacts,
        "username": get_username(order_user),
        "comment": order.comment if order.comment else "-",
        "payment_delivery_text": payment_delivery_text,
        "ext_order_text": ext_order_text,
        "pmt_info": pmt_info,
        "menu_in_store_comment": menu_in_store.comment if menu_in_store else "",
        "order_status_comment": f" [{order_shipping_status.comment}]" if order_shipping_status and order_shipping_status.comment else "",
        "payment_error_text": payment_error_text,
    }
    order_text = await f("store brand notification order telegram or whatsapp text", lang, **text_kwargs)

    if order.billing_address:
        billing_locales = await fl(
            {
                "store brand billing settings button": {},
                "web store form name label text": {},
                "web store form lastname label text": {},
                "web store billing form company name label": {},
                "web store billing form vat number label": {},
                "web store billing form reg number label": {},
                "web store billing form country label": {},
                "web store billing form state label": {},
                "web store billing form city label": {},
                "web store billing form zip label": {},
                "web store billing form address one label": {},
                "web store billing form address two label": {},
                "default phone var name": {},
            }, lang
        )

        billing_text = f"\n\n{billing_locales[0]}:"
        billing_text += f"\n{billing_locales[1]}: {order.billing_address.first_name}"
        billing_text += f"\n{billing_locales[2]}: {order.billing_address.last_name}"
        billing_text += f"\n{billing_locales[3]}: {order.billing_address.company_name}"
        billing_text += f"\n{billing_locales[4]}: {order.billing_address.vat_number}"
        billing_text += f"\n{billing_locales[5]}: {order.billing_address.registration_number}"
        billing_text += f"\n{billing_locales[6]}: {order.billing_address.country}"
        billing_text += f"\n{billing_locales[7]}: {order.billing_address.state}"
        billing_text += f"\n{billing_locales[8]}: {order.billing_address.city}"
        billing_text += f"\n{billing_locales[9]}: {order.billing_address.zip_code}"
        billing_text += f"\n{billing_locales[10]}: {order.billing_address.address_1}"
        billing_text += f"\n{billing_locales[11]}: {order.billing_address.address_2}"
        billing_text += f"\n{billing_locales[12]}: {order.billing_address.phone_number}"

        order_text += billing_text
    debugger.debug(f"__get_notification_text__ -> {order_text=}")
    return order_text


def get_username(user: User) -> str:
    if user.username:
        return "@" + user.username
    wa_link = user.wa_link
    return wa_link if wa_link else "-"


async def get_contacts(order: StoreOrder, lang: str) -> str:
    contacts = []

    name = ""
    if order.first_name or order.last_name:
        name = await f("store brand name notifications text", lang)
        name = f"{name}:"
    if order.first_name:
        name += f" {order.first_name}"
    if order.last_name:
        name += f" {order.last_name}"

    if name:
        contacts.append(name)

    if order.phone:
        phone = (order.phone.replace(' ', '').replace('(', '').replace(')', '').replace('-', ''))
        if phone[0] != '+':
            phone = f'+{phone}'
        phone_text = f'{await f("store brand phone notifications text", lang)}: {phone}'
        contacts.append(phone_text)

    if order.email:
        contacts.append(": ".join([await f("store brand email notifications text", lang), order.email]))

    if not contacts:
        return ""

    return "\n".join(contacts)


async def process_order_message_collapse_or_expand_button(
        message: types.Message,
        manager_user: User,
        store_order_id: int,
        is_expanded: bool,
):
    order = await StoreOrder.get(store_order_id)
    order_user = await User.get_by_id(order.user_id)
    order_shipping_status = order.shipping_statuses[-1]
    group = await crud.get_group_by_store(order.store_id)
    store = await Store.get(order.store_id)

    await process_order_message_collapse_or_expand(
        message, manager_user,
        order, order_shipping_status,
        group, store, order_user, is_expanded,
    )


async def process_order_message_collapse_or_expand(
        message: types.Message,
        manager_user: User,
        order: StoreOrder,
        order_shipping_status: OrderShippingStatus,
        group: Group,
        store: Store,
        order_user: User,
        is_expanded: bool = False,
):
    manager_lang = manager_user.lang

    text = await __get_notification_text__(
        group, store, order, order_shipping_status,
        manager_lang, order_user, is_expanded,
    )

    if is_expanded:
        keyboard = await get_expand_keyboard(order.id, manager_lang)
    else:
        keyboard = await get_collapse_keyboard(order.id, manager_lang, )

    return await message.edit_text(text, reply_markup=keyboard)


async def add_order_service_bot_notifications(
        order: StoreOrder,
        order_shipping_status: OrderShippingStatus,
        group: Group,
        store: Store,
        user: User,
        bot_id: int | None = None,
        sender_manager_user: User | None = None,
        is_expanded: bool | None = None,
        is_payment_error: bool | None = None,
        is_payed_new_order: bool | None = None,
):
    if user.photo:
        content_type = "photo"
        media = user.photo
    else:
        content_type = "text"
        media = None

    async def get_sending_data(manager_user: User):
        manager_lang = manager_user.lang

        sending_data = {
            "content_type": content_type,
            "text": await __get_notification_text__(
                group, store, order, order_shipping_status,
                manager_lang, user, is_expanded, is_payment_error,
                is_payed_new_order=is_payed_new_order,
            )
        }
        if media:
            sending_data[content_type] = media

        if sender_manager_user and sender_manager_user != manager_user:
            sending_data["keyboard"] = (await get_expand_keyboard(order.id, manager_lang)).to_python()
        else:
            keyboard = await __get_store_order_notification_keyboard__(
                order, manager_lang, store,
                bot_id, group, user,
            )
            if isinstance(keyboard, types.InlineKeyboardMarkup):
                sending_data["keyboard"] = keyboard.to_python()

        return sending_data

    try:
        await add_telegram_notifications_for_action(
            "service",
            SERVICE_BOT_USERNAME,
            SERVICE_BOT_API_TOKEN,
            action="crm_order:read",
            available_data={
                "profile_id": group.id,
                "store_id": store.id,
                "order_id": order.id,
            },
            message=get_sending_data,
            is_entered_service_bot=True,
        )
    except Exception as e:
        logging.getLogger("error.order.add_order_service_bot_notifications").error(e, exc_info=True)
        await send_message_to_platform_admins(
            f"An error occurred while sending order notifications to service bot: {str(e)}\n"
            f"Order: #{order.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Order user: {user.name}({order.user_id})\n"
        )
