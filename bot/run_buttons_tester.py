from aiogram import Dispatcher
from aiogram.utils.executor import start_polling

from buttons_tester.middlewares import SessionMiddleware
from config import BUTTONS_TESTER_BOT_API_TOKEN
from core.aiogram_middlewares import CallbackDataMiddleware, CreateOrUpdateUserMiddleware
from utils.filters.callback import Callback<PERSON>ode<PERSON>ilter

from utils.logger import setup_logger
from utils.redefined_classes import Bo<PERSON>

from buttons_tester.handlers import register_handlers

setup_logger("buttons_tester")

bot = Bot(BUTTONS_TESTER_BOT_API_TOKEN)
dp = Dispatcher(bot)

dp.bind_filter(
    CallbackModeFilter, event_handlers=[
        dp.callback_query_handlers,
        dp.pre_checkout_query_handlers,
        dp.shipping_query_handlers,
    ]
)

register_handlers(dp)

dp.setup_middleware(SessionMiddleware())
dp.setup_middleware(CreateOrUpdateUserMiddleware())
dp.setup_middleware(CallbackDataMiddleware())


start_polling(dp)
