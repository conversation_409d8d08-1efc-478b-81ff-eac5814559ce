import atexit

import config as cfg

from aiohttp import web

from config import WEBSERVER_HOST
from utils.logger import setup_logger

from lip_lep.init import dp, app, router
from lip_lep.handlers import on_startup, on_shutdown, register_web_handlers, register_bot_handlers, bind_filters
from lip_lep.router import setup_router

setup_logger("lip_lep")

bind_filters(dp)

setup_router(router)

register_bot_handlers(dp)

register_web_handlers(app, dp)


atexit.register(on_shutdown, dp)


async def run_app():
    await on_startup(dp)

    return app

web.run_app(run_app(), host=WEBSERVER_HOST, port=cfg.ROOT_BOT_PORT + 6)
