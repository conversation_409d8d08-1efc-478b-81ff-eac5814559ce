import asyncio

from core.marketing.worker import MailingWorker
from core.kafka.producer.producer_instance import producer
from utils.logger import setup_logger

setup_logger("mailing")


async def main():
    worker = MailingWorker()
    print("mailing service starting...")
    try:
        await producer.initialise()
        await worker.start()
    finally:
        await producer.stop()


if __name__ == "__main__":
    asyncio.run(main())
