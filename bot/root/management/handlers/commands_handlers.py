import asyncio
import logging
import os
import subprocess
from contextlib import suppress
from typing import Callable, List, Literal

from aiogram import Dispatcher, types
from aiogram.utils.exceptions import Unauthorized

import config as cfg
from client.main.functions import set_menu_button
from config import BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID, BILLING_TIMEZONE
from core.billing.functions import (
    export_invoices,
    get_unpaid_invoices_last_month_2weeks,
)
from core.ext.import_updater.workers import ImportUpdaterWorker
from core.mailing.process import MailingWorker
from db import crud, own_session
from db.models import ClientBot
from friendly.draw.worker import DrawsWorker
from friendly.schedule.posts.workers import FriendlyPostsWorker
from root.helpers import (
    deploy_admin_web, deploy_crm_web, deploy_my7loc_web,
    deploy_web, pull_api, reload_api,
)
from root.states import MainState
from utils.date_time import get_prev_month
from utils.platform_admins import send_message_to_platform_admins
from utils.processes_manager.manager import ProcessesManager
from utils.redefined_classes import Bot
from utils.text import f, make_ending
from utils.translator.workers import LocalisationWorker
from utils.update_localisation import update_localisation

ROOT_BOT_LANG = "uk"


async def start_callback(name: str, result: Literal["already_started", "error", True]):
    if result is True:
        await send_message_to_platform_admins(
            await f(f"{name} started", ROOT_BOT_LANG), force_to_bot=True
        )
    elif result == "already_started":
        await send_message_to_platform_admins(
            await f(f"{name} already started", ROOT_BOT_LANG), force_to_bot=True
        )


async def stop_callback(name: str, result: Literal["not_started", "error", True]):
    if result is True:
        await send_message_to_platform_admins(
            await f(f"{name} stopped", ROOT_BOT_LANG), force_to_bot=True
        )
    elif result == "not_started":
        await send_message_to_platform_admins(
            await f(f"{name} is not started", ROOT_BOT_LANG), force_to_bot=True
        )


process_manager = ProcessesManager(
    processes=[
        ("friendly bots process", (cfg.PYTHON, cfg.FRIENDLY_BOT)),
        ("client bots process", (cfg.PYTHON, cfg.CLIENT_BOT)),
        ("whatsapp bot", (cfg.PYTHON, cfg.WHATSAPP_BOT), cfg.RUN_WHATSAPP),
        ("service bot", (cfg.PYTHON, cfg.SERVICE_BOT)),
        ("lip lep bot", (cfg.PYTHON, cfg.LIP_LEP_BOT)),
        ("api handler", (cfg.PYTHON, cfg.API_HANDLER), cfg.API_RESTART),
        ("porter process", (cfg.PYTHON, cfg.PORTER)),
        ("new orders checker process", (cfg.PYTHON, cfg.NEW_ORDERS_CHECKER)),
        ("vm workers", (cfg.PYTHON, cfg.RUN_VM_WORKERS)),
        (
            "stripe usage report worker",
            (cfg.PYTHON, cfg.RUN_STRIPE_USAGE_REPORT_WORKER)),
        ("mailing worker", (cfg.PYTHON, cfg.MAILING))
    ],
    workers=[
        MailingWorker,
        FriendlyPostsWorker,
        DrawsWorker,
        ImportUpdaterWorker,
        LocalisationWorker,
    ],
    timeout_between_processes=cfg.TIME_SLEEP_BETWEEN_STARTING_BOTS,
    start_callback=start_callback,
    stop_callback=stop_callback,
)


async def cmd_restart_platform(message: types.Message):
    if not hasattr(cfg, "RESTART_PLATFORM_COMMAND"):
        await message.answer(
            await f("restart platform command is undefined", ROOT_BOT_LANG)
        )
    await message.answer("restarting")
    subprocess.Popen(cfg.RESTART_PLATFORM_COMMAND)


async def cmd_upd(message: types.Message):
    await update_localisation()
    await message.answer(await f("updated local text", ROOT_BOT_LANG))


@own_session
async def cmd_start_client_bots_webhooks(
        message: types.Message = None, send_error: bool = True, recall: bool = False,
        recalled: bool = False
):
    bots_to_start = await ClientBot.get_list(
        is_friendly=False, is_started=False, bot_type="telegram"
    )
    if not bots_to_start:
        if not recalled:
            if not send_error:
                group_bots_count = await ClientBot.get_list(
                    is_pay4say=False, is_friendly=False,
                    is_started=True, operation="count",
                    bot_type="telegram",
                )
                pay4say_bots_count = await ClientBot.get_list(
                    is_pay4say=True, is_friendly=False,
                    is_started=True, operation="count",
                    bot_type="telegram",
                )
                group_bots_count_ending = make_ending(group_bots_count, ROOT_BOT_LANG)
                pay4say_bots_count_ending = make_ending(
                    pay4say_bots_count, ROOT_BOT_LANG
                )
                texts = [
                    await f(
                        "group bots started", ROOT_BOT_LANG, count=group_bots_count,
                        ending=group_bots_count_ending
                    ),
                    await f(
                        "pay4say bots started", ROOT_BOT_LANG, count=pay4say_bots_count,
                        ending=pay4say_bots_count_ending
                    ),
                ]
                message_text = "\n".join(texts)
            else:
                all_bots = await ClientBot.get_list(
                    is_friendly=False, bot_type="telegram"
                )
                if not all_bots:
                    message_text = await f("no client bots error")
                else:
                    message_text = await f(
                        "all client bots already started", ROOT_BOT_LANG
                    )
            if message:
                await message.answer(message_text)
            else:
                await send_message_to_platform_admins(message_text, force_to_bot=True)
    else:
        messages_to_edit = None
        superbot = Bot.get_current()
        dp = Dispatcher.get_current()
        for bot in bots_to_start:
            with superbot.with_token(bot.token):
                try:
                    await dp.reset_webhook(True)
                    await dp.skip_updates()
                    await set_menu_button(bot)

                    if bot.is_lip_lep:
                        port = cfg.ROOT_BOT_PORT + 6
                        path = f"lip_lep_bot"
                    else:
                        port = cfg.ROOT_BOT_PORT + 1
                        path = "client_bot"

                    webhook_path = (f"http://{cfg.WEBHOOK_HOST}:{port}/{path}/"
                                    f"{bot.id}/{bot.token}")

                    await superbot.set_webhook(webhook_path)
                except Unauthorized:
                    await bot.delete(bot.group.owner)
                    continue
                except Exception as e:
                    logger = logging.getLogger()
                    logger.error(e, exc_info=True)
                    continue
                else:
                    await bot.started()
            group_bots_count = await ClientBot.get_list(
                is_pay4say=False, is_friendly=False,
                is_started=True, operation="count",
                bot_type="telegram",
            )
            pay4say_bots_count = await ClientBot.get_list(
                is_pay4say=True, is_friendly=False,
                is_started=True, operation="count",
                bot_type="telegram",
            )
            texts = [
                await f(
                    "group bots started", ROOT_BOT_LANG, count=group_bots_count,
                    ending=make_ending(group_bots_count, ROOT_BOT_LANG)
                ),
                await f(
                    "pay4say bots started", ROOT_BOT_LANG, count=pay4say_bots_count,
                    ending=make_ending(pay4say_bots_count, ROOT_BOT_LANG)
                ),
            ]
            message_text = "\n".join(texts)
            if messages_to_edit:
                for message_to_edit in messages_to_edit:
                    with suppress(Exception):
                        await message_to_edit.edit_text(message_text)
            else:
                if message:
                    messages_to_edit = [await message.answer(message_text)]
                else:
                    messages_to_edit = await send_message_to_platform_admins(
                        message_text, force_to_bot=True
                    )
    if recall:
        loop = asyncio.get_event_loop()
        loop.call_later(
            1, asyncio.create_task,
            cmd_start_client_bots_webhooks(send_error=False, recall=True, recalled=True)
        )


@own_session
async def cmd_start_friendly_bots_webhooks(
        message: types.Message = None, send_error: bool = True, recall: bool = False,
        recalled: bool = False
):
    bots_to_start = await ClientBot.get_list(
        is_friendly=True, is_started=False, bot_type="telegram"
    )
    if not bots_to_start:
        if not recalled:
            if not send_error:
                friendly_bots_count = await ClientBot.get_list(
                    is_pay4say=False, is_friendly=True,
                    is_started=True, operation="count",
                    bot_type="telegram",
                )
                ending = make_ending(friendly_bots_count, ROOT_BOT_LANG)
                message_text = await f(
                    "friendly bots started", ROOT_BOT_LANG, count=friendly_bots_count,
                    ending=ending
                )
            else:
                all_bots = await ClientBot.get_list(
                    is_friendly=True, bot_type="telegram"
                )
                if not all_bots:
                    message_text = await f("no friendly bots error")
                else:
                    message_text = await f(
                        "all friendly bots already started", ROOT_BOT_LANG
                    )
            if message:
                await message.answer(message_text)
            else:
                await send_message_to_platform_admins(message_text, force_to_bot=True)
    else:
        messages_to_edit = None
        superbot = Bot.get_current()
        dp = Dispatcher.get_current()
        for bot in bots_to_start:
            with superbot.with_token(bot.token):
                await dp.reset_webhook(True)
                await dp.skip_updates()
                allowed_updates = (
                        types.AllowedUpdates.MESSAGE
                        | types.AllowedUpdates.CALLBACK_QUERY
                        | types.AllowedUpdates.CHAT_MEMBER
                        | types.AllowedUpdates.MY_CHAT_MEMBER
                )
                try:
                    path = (
                        f"http://{cfg.WEBHOOK_HOST}:"
                        f"{cfg.ROOT_BOT_PORT + 4}"
                        f"/friendly_bot/{bot.id}/{bot.token}"
                    )
                    await superbot.set_webhook(path, allowed_updates=allowed_updates)
                except Unauthorized:
                    await bot.delete(bot.group.owner)
                    continue
                except Exception as e:
                    logger = logging.getLogger()
                    logger.error(e, exc_info=True)
                    continue
                else:
                    await bot.started()
            friendly_bots_count = await ClientBot.get_list(
                is_pay4say=False, is_friendly=True,
                is_started=True, operation="count",
                bot_type="telegram",
            )
            ending = make_ending(friendly_bots_count, ROOT_BOT_LANG)
            message_text = await f(
                "friendly bots started", ROOT_BOT_LANG, count=friendly_bots_count,
                ending=ending
            )
            if messages_to_edit:
                for message_to_edit in messages_to_edit:
                    await message_to_edit.edit_text(message_text)
            else:
                if message:
                    messages_to_edit = [await message.answer(message_text)]
                else:
                    messages_to_edit = await send_message_to_platform_admins(
                        message_text, force_to_bot=True
                    )
    if recall:
        loop = asyncio.get_event_loop()
        loop.call_later(
            1, asyncio.create_task,
            cmd_start_friendly_bots_webhooks(
                send_error=False, recall=True, recalled=True
            )
        )


async def cmd_start_client_bots(_: types.Message = None):
    await process_manager.start_process("client bots process")


async def cmd_stop_client_bots(_: types.Message = None):
    await process_manager.stop_process("client bots process")


async def cmd_restart_client_bots(_: types.Message | None = None):
    await cmd_stop_client_bots()
    await cmd_start_client_bots()


async def cmd_start_friendly_bots(_: types.Message = None):
    await process_manager.start_process("friendly bots process")


async def cmd_stop_friendly_bots(_: types.Message = None):
    await process_manager.stop_process("friendly bots process")


async def cmd_restart_friendly_bots(message: types.Message):
    await cmd_stop_friendly_bots(message)
    await cmd_start_friendly_bots(message)


async def cmd_start_service_bot(_: types.Message = None):
    result = await process_manager.start_process("service bot")

    if result is True:
        superbot = Bot.get_current()
        dp = Dispatcher.get_current()
        with superbot.with_token(cfg.SERVICE_BOT_API_TOKEN):
            await dp.reset_webhook(True)
            await dp.skip_updates()
            await superbot.delete_my_commands()
            webhook_path = (
                f"http://{cfg.WEBHOOK_HOST}:"
                f"{cfg.ROOT_BOT_PORT + 2}"
                f"/service_bot/{cfg.SERVICE_BOT_API_TOKEN}"
            )
            await superbot.set_webhook(webhook_path)


async def cmd_stop_service_bot(_: types.Message = None):
    await process_manager.stop_process("service bot")


async def cmd_restart_service_bot(_: types.Message):
    await cmd_stop_service_bot()
    await cmd_start_service_bot()


async def cmd_start_api_handler(message: types.Message = None):
    if cfg.API_RESTART:
        await process_manager.start_process("api handler")
    elif message:
        await message.answer(await f("api start disabled error", ROOT_BOT_LANG))


async def cmd_stop_api_handler(message: types.Message = None):
    if cfg.API_RESTART:
        await process_manager.stop_process("api handler")
    elif message:
        await message.answer(await f("api stop disabled error", ROOT_BOT_LANG))


async def cmd_restart_api_handler(message: types.Message):
    if cfg.API_RESTART:
        await cmd_stop_api_handler(message)
        await cmd_start_api_handler(message)
    elif cfg.RELOAD_API_COMMAND:
        asyncio.ensure_future(reload_api())
        await message.answer(await f("api reload start text", ROOT_BOT_LANG))


async def cmd_pull_git_handler(message: types.Message):
    if cfg.PULL_API_COMMAND:
        asyncio.ensure_future(pull_api())
        await message.answer(await f("pull git start text", ROOT_BOT_LANG))


async def cmd_deploy_web_handler(message: types.message):
    if cfg.ROOT_BOT_DEPLOY_WEB_COMMAND:
        await deploy_web()
        await message.answer(await f("web deployed text", ROOT_BOT_LANG))


async def cmd_deploy_admin_web_handler(message: types.message):
    if cfg.ROOT_BOT_DEPLOY_ADMIN_WEB_COMMAND:
        await deploy_admin_web()
        await message.answer(await f("admin web deployed text", ROOT_BOT_LANG))


async def cmd_deploy_my7loc_web_handler(message: types.message):
    if cfg.ROOT_BOT_DEPLOY_MY_7LOC_WEB_COMMAND:
        await deploy_my7loc_web()
        await message.answer(await f("my7loc web deployed text", ROOT_BOT_LANG))


async def cmd_deploy_crm_web_handler(message: types.message):
    if cfg.ROOT_BOT_DEPLOY_CRM_WEB_COMMAND:
        await deploy_crm_web()
        await message.answer(await f("crm web deployed text", ROOT_BOT_LANG))


async def cmd_start_whatsapp_bot(_: types.Message = None):
    await process_manager.start_process("whatsapp bot")


async def cmd_stop_whatsapp_bot(_: types.Message = None):
    await process_manager.stop_process("whatsapp bot")


async def cmd_restart_whatsapp_bot(message: types.Message):
    await cmd_stop_whatsapp_bot(message)
    await cmd_start_whatsapp_bot(message)


async def cmd_start_lip_lep_bot(_: types.Message = None):
    await process_manager.start_process('lip lep bot')


async def cmd_stop_lip_lep_bot(_: types.Message = None):
    await process_manager.stop_process("lip lep bot")


async def cmd_restart_lip_lep_bot(message: types.Message):
    await cmd_stop_lip_lep_bot(message)
    await cmd_start_lip_lep_bot(message)


async def cmd_restart_client_bots_webhooks(
        message: types.Message = None, send_error: bool = True
):
    bots_to_stop = await crud.get_bots_to_stop(False, "telegram")
    if not bots_to_stop:
        if not send_error:
            return
        message_text = await f("no started client bots", ROOT_BOT_LANG)
    else:
        superbot = Bot.get_current()
        for bot in bots_to_stop:
            with superbot.with_token(bot.token):
                try:
                    await superbot.delete_webhook(drop_pending_updates=True)
                except Unauthorized:
                    await bot.delete(bot.group.owner)
                    continue
                except Exception as e:
                    logger = logging.getLogger()
                    logger.error(e, exc_info=True)
                    continue
        await crud.stop_bots(*(bot.id for bot in bots_to_stop))
        message_text = await f("client bots stopped", ROOT_BOT_LANG)

    if message:
        await message.answer(message_text)
    else:
        await send_message_to_platform_admins(message_text, force_to_bot=True)


async def cmd_restart_friendly_bots_webhooks(
        message: types.Message = None, send_error: bool = True
):
    started_bots = await ClientBot.get_list(
        is_friendly=True, is_started=True, bot_type="telegram"
    )
    if not started_bots:
        if not send_error:
            return
        message_text = await f("no started friendly bots", ROOT_BOT_LANG)
    else:
        superbot = Bot.get_current()
        for bot in started_bots:
            with superbot.with_token(bot.token):
                try:
                    await superbot.delete_webhook(drop_pending_updates=True)
                except Unauthorized:
                    await bot.delete(bot.group.owner)
                    continue
                except Exception as e:
                    logger = logging.getLogger()
                    logger.error(e, exc_info=True)
                else:
                    await bot.stopped()
        message_text = await f("friendly bots stopped", ROOT_BOT_LANG)
    if message:
        await message.answer(message_text)
    else:
        await send_message_to_platform_admins(message_text, force_to_bot=True)


async def cmd_start_vm_workers(_: types.Message = None):
    await process_manager.start_process("vm workers")


async def cmd_stop_vm_workers(_: types.Message = None):
    await process_manager.stop_process("vm workers")


async def cmd_restart_vm_workers(message: types.Message):
    await cmd_stop_vm_workers(message)
    await cmd_start_vm_workers(message)


async def cmd_start_mailing_worker(_: types.Message = None):
    await process_manager.start_process("mailing worker")


async def cmd_stop_mailing_worker(_: types.Message = None):
    await process_manager.stop_process("mailing worker")


async def cmd_restart_mailing_worker(message: types.Message):
    await cmd_stop_mailing_worker(message)
    await cmd_start_mailing_worker(message)


async def cmd_start_porter(_: types.Message = None):
    await process_manager.start_process("porter process")


async def cmd_stop_porter(_: types.Message = None):
    await process_manager.stop_process("porter process")


async def cmd_restart_porter(message: types.Message):
    await cmd_stop_porter(message)
    await cmd_start_porter(message)


async def cmd_start_workers(_: types.Message | None = None):
    await process_manager.start_workers()
    await send_message_to_platform_admins(
        await f("workers started", ROOT_BOT_LANG), force_to_bot=True
    )


async def cmd_stop_workers(_: types.Message | None = None):
    await process_manager.stop_workers()
    await send_message_to_platform_admins(
        await f("workers stopped", ROOT_BOT_LANG), force_to_bot=True
    )


async def cmd_restart_workers(_: types.Message | None = None):
    await cmd_stop_workers()
    await cmd_start_workers()


async def cmd_start_bots(_: types.Message | None = None):
    if not cfg.API_RESTART and cfg.RELOAD_API_COMMAND:
        asyncio.ensure_future(reload_api())

    await process_manager.start_processes()
    await cmd_start_client_bots_webhooks(recall=True)
    await cmd_start_friendly_bots_webhooks(recall=True)
    await cmd_start_workers()


async def cmd_stop_bots(_: types.Message | None = None):
    await process_manager.stop_processes()
    await cmd_stop_workers()


async def cmd_restart_bots(_: types.Message | None = None):
    await cmd_stop_bots()
    await cmd_start_bots()


async def cmd_export_billing(message: types.Message):
    if message.chat.id != BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID:
        return await message.reply("You are not allowed to use this command here")

    period_start, period_end = get_prev_month(BILLING_TIMEZONE)

    async with export_invoices(period_start, period_end) as archive_path:
        filename = os.path.split(archive_path)[-1]

        await message.reply_document(
            types.InputFile(
                path_or_bytesio=archive_path,
                filename=filename,
            )
        )


async def cmd_get_last_month_2weeks_invoices_numbers(message: types.Message):
    if message.chat.id != BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID:
        return await message.reply("You are not allowed to use this command here")

    async with get_unpaid_invoices_last_month_2weeks() as message:

        await message.reply(
            types.Message(
                text=message
            )
        )


def register_management_commands_handlers(dp: Dispatcher):
    def register_management_command(handler: Callable, command: str | List[str]):
        dp.register_message_handler(
            handler, commands=command, state=MainState.Authenticated
        )

    register_management_command(cmd_upd, "upd")

    register_management_command(cmd_restart_platform, ["restart_platform", "rp"])

    register_management_command(
        cmd_start_client_bots_webhooks, ["start_client_bots_webhooks", "scbw"]
    )
    register_management_command(
        cmd_restart_client_bots_webhooks, ["restart_client_bots_webhooks", "rcbw"]
    )

    register_management_command(cmd_start_client_bots, "start_client_bots")
    register_management_command(cmd_stop_client_bots, "stop_client_bots")
    register_management_command(cmd_restart_client_bots, ["restart_client_bots", "rcb"])

    register_management_command(
        cmd_start_friendly_bots_webhooks, ["start_friendly_bots_webhooks", "sfbw"]
    )
    register_management_command(
        cmd_restart_friendly_bots_webhooks, ["restart_friendly_bots_webhooks", "rfbw"]
    )

    register_management_command(cmd_start_friendly_bots, "start_friendly_bots")
    register_management_command(cmd_stop_friendly_bots, "stop_friendly_bots")
    register_management_command(
        cmd_restart_friendly_bots, ["restart_friendly_bots", "rfb"]
    )

    register_management_command(cmd_start_porter, "start_porter")
    register_management_command(cmd_stop_porter, "stop_porter")
    register_management_command(cmd_restart_porter, ["restart_porter", "rprt"])

    register_management_command(cmd_start_service_bot, "start_service_bot")
    register_management_command(cmd_stop_service_bot, "stop_service_bot")
    register_management_command(cmd_restart_service_bot, ["restart_service_bot", "rsb"])

    register_management_command(cmd_start_lip_lep_bot, "start_lip_lep_bot")
    register_management_command(cmd_stop_lip_lep_bot, "stop_lip_lep_bot")
    register_management_command(
        cmd_restart_lip_lep_bot, ["restart_lip_lep_bot", "rllb"]
    )

    register_management_command(cmd_start_api_handler, "start_api_handler")
    register_management_command(cmd_stop_api_handler, "stop_api_handler")
    register_management_command(cmd_restart_api_handler, ["restart_api_handler", "rah"])
    register_management_command(cmd_pull_git_handler, ["pull_git", "pgit", "git_pull"])
    register_management_command(cmd_deploy_web_handler, ["deploy_web", "dw"])
    register_management_command(
        cmd_deploy_admin_web_handler, ["deploy_admin_web", "daw"]
    )
    register_management_command(
        cmd_deploy_my7loc_web_handler, ["deploy_my7loc_web", "dmw"]
    )
    register_management_command(cmd_deploy_crm_web_handler, ["deploy_crm_web", "dcw"])

    register_management_command(cmd_start_whatsapp_bot, "start_whatsapp_bot")
    register_management_command(cmd_stop_whatsapp_bot, "stop_whatsapp_bot")
    register_management_command(
        cmd_restart_whatsapp_bot, ["restart_whatsapp_bot", "rwb"]
    )

    register_management_command(cmd_start_bots, "start_bots")
    register_management_command(cmd_stop_bots, "stop_bots")
    register_management_command(cmd_restart_bots, ["restart_bots", "rb"])

    register_management_command(cmd_start_workers, "start_workers")
    register_management_command(cmd_stop_workers, "stop_workers")
    register_management_command(cmd_restart_workers, "restart_workers")

    register_management_command(cmd_start_vm_workers, "start_vm_workers")
    register_management_command(cmd_stop_vm_workers, "stop_vm_workers")
    register_management_command(cmd_restart_vm_workers, ["restart_vm_workers", "rvmw"])

    register_management_command(cmd_start_mailing_worker, "start_mailing_worker")
    register_management_command(cmd_stop_mailing_worker, "stop_mailing_worker")
    register_management_command(
        cmd_restart_mailing_worker, ["restart_mailing_worker", "rmw"]
    )

    dp.register_message_handler(
        cmd_export_billing,
        commands="export_billing",
        state="*",
        chat_type=[types.ChatType.GROUP, types.ChatType.SUPERGROUP]
    )

    dp.register_message_handler(
        cmd_get_last_month_2weeks_invoices_numbers,
        commands="expiring_invoices",
        state="*",
        chat_type=[types.ChatType.GROUP, types.ChatType.SUPERGROUP]
    )
