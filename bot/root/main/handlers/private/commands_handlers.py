import config as cfg

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.text import f, replace_html_symbols

from root.states import MainState


async def cmd_start(message: types.Message, state: FSMContext):
    lang = "ru"
    cur_state = await state.get_state()
    if not cur_state:
        await MainState.first()
        await state.update_data(tries=list(), password="")
        await message.answer(await f("ENTER_PASSWORD", lang), reply_markup=types.ReplyKeyboardRemove())
    else:
        await message.answer(await f("super bot hello", lang), reply_markup=types.ReplyKeyboardRemove())


async def cmd_state(message: types.Message, state: FSMContext):
    cur_state = await state.get_state()
    state_data = await state.get_data()
    cur_state = f"{cur_state}\n{state_data}"
    if cur_state is None:
        cur_state = "without state"
    await message.answer(replace_html_symbols(cur_state))


async def cmd_exit(message: types.Message, state: FSMContext):
    lang = "ru"
    async with state.proxy() as data:
        if data.get("password") == cfg.SUPER_BOT_PASSWORD:
            await MainState.Authenticated.set()
        else:
            await state.finish()
    await message.answer(await f("logout", lang))


def register_main_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(cmd_start, commands=["start"], state="*")
    dp.register_message_handler(cmd_state, commands=["state"], state="*")
    dp.register_message_handler(cmd_exit, commands=["exit"], state="*")
