from aiogram import Dispatcher, types

from config import (
    BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID, MONITORING_GROUP_CHAT_ID,
    TRANSLATOR_MONITORING_GROUP_ID,
)
from utils.filters.simple import not_private_chat_filter


async def leave_group(message: types.Message):
    if message.chat.id not in (
            TRANSLATOR_MONITORING_GROUP_ID,
            MONITORING_GROUP_CHAT_ID,
            BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID,
    ):
        await message.chat.leave()


def register_main_group_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        leave_group,
        not_private_chat_filter,
        content_types=types.ContentTypes.ANY,
        state="*"
    )
