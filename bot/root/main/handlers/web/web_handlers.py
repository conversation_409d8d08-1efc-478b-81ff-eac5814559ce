import config as cfg

from functools import partial

from aiogram import Dispatcher, types

from aiohttp import web

from utils.redefined_classes import Bo<PERSON>

from core.helpers import process_update


async def updates_handler(request: web.Request, dp: Dispatcher):
    Bot.set_current(dp.bot)
    Dispatcher.set_current(dp)
    json_data = await request.json()
    update = types.Update.to_object(json_data)
    try:
        await process_update(dp, update)
    finally:
        return web.Response()


def register_main_web_handlers(app: web.Application, dp: Dispatcher):
    handler = partial(updates_handler, dp=dp)
    app.router.add_post(f"/root_bot/{cfg.ROOT_BOT_API_TOKEN}", handler)
