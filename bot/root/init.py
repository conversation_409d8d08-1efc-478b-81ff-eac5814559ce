import config as cfg

from aiohttp import web

from aiogram import Dispatcher

from core.bot.forms import get_forms
from utils.redefined_classes import Bo<PERSON>
from utils.redefined_classes.aiogram.my_storages import MyRedisStorage

app = web.Application()

bot = Bot(token=cfg.ROOT_BOT_API_TOKEN)
storage = MyRedisStorage(db=0, host=cfg.REDIS_HOST, port=cfg.REDIS_PORT)
dp = Dispatcher(bot, storage=storage)

forms = get_forms()
