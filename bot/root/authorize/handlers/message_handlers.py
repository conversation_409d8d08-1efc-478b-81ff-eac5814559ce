import config as cfg

from datetime import datetime

from aiogram import Dispatcher, types
from aiogram.types import ContentTypes
from aiogram.dispatcher import FSMContext

from utils.text import f

from root.states import MainState


async def login_password_handler(message: types.Message, state: FSMContext):
    lang = "ru"
    password = message.text
    state_data = await state.get_data()
    tries = state_data.get("tries")
    for t in tries:
        if divmod((datetime.utcnow() - t).total_seconds(), 60)[0] > 5:
            tries.remove(t)
    if len(tries) >= 5:
        time_to_new_try = str(5 - int(divmod((datetime.utcnow() - tries[0]).total_seconds(), 60)[0]))
        await message.answer(await f("new try time", minute=time_to_new_try))
    else:
        if cfg.SUPER_BOT_PASSWORD == password:
            del state_data["tries"]
            state_data["password"] = password
            await MainState.Authenticated.set()
            await state.set_data(state_data)
            await message.answer(await f("super bot hello", lang))
        else:
            await message.answer(await f("incorrect password", lang))
            tries.append(datetime.utcnow())
    await message.delete()


def register_authorize_message_handlers(dp: Dispatcher):
    dp.register_message_handler(login_password_handler, content_types=ContentTypes.TEXT, state=MainState.Password)
