import asyncio
import logging
import os

from aiogram import Dispatcher, types
from aiogram.utils.exceptions import Unauthorized
from psutils.decorators import sync_to_async

import config as cfg
from db import own_session
from db.models import ClientBot
from utils.localisation import localisation
from utils.redefined_classes import Bot
from utils.text import f


def make_dirs():
    for media_dir in ["photos", "videos", "documents", "voice", "music", "animations", "qrcodes"]:
        path = os.path.join(cfg.STATIC_DB, media_dir)
        if not os.path.isdir(path):
            os.mkdir(path)


@own_session
async def stop_disabled_bots():
    disabled_bots = await ClientBot.get_list(status="disabled", is_started=True)
    superbot = Bot.get_current()
    for bot in disabled_bots:
        with superbot.with_token(bot.token):
            try:
                await superbot.delete_webhook(drop_pending_updates=True)
            except Unauthorized:
                await bot.stopped()
                continue
            except Exception as e:
                logger = logging.getLogger()
                logger.error(e, exc_info=True)
                continue
            else:
                await bot.stopped()
    loop = asyncio.get_event_loop()
    loop.call_later(1, asyncio.create_task, stop_disabled_bots())


@own_session
async def set_super_bot_commands(dp: Dispatcher):
    await dp.bot.delete_my_commands()
    for lang in await localisation.langs:
        await dp.bot.set_my_commands(
            [
                types.BotCommand("start", await f("cmd start", lang)),
                types.BotCommand("become_supermanager", await f("cmd become_supermanager", lang)),
                types.BotCommand("bsm", await f("cmd become_supermanager", lang)),
                types.BotCommand("become_superadmin", await f("cmd become_superadmin", lang)),
                types.BotCommand("bsa", await f("cmd become_superadmin", lang)),
                types.BotCommand("start_bots", await f("cmd start bots", lang)),
                types.BotCommand("sb", await f("cmd start bots", lang)),
                types.BotCommand("start_service_bot", await f("cmd start service bot", lang)),
                types.BotCommand("start_admin_bot", await f("cmd start admin bot", lang)),
                types.BotCommand("start_client_bots", await f("cmd start client bots", lang)),
                types.BotCommand("start_friendly_bots", await f("cmd start friendly bots", lang)),
                types.BotCommand("restart_porter", await f("cmd restart porter", lang)),
                types.BotCommand("stop_porter", await f("cmd stop porter", lang)),
                types.BotCommand("start_porter", await f("cmd start porter", lang)),
                types.BotCommand("stop_service_bot", await f("cmd stop service bot", lang)),
                types.BotCommand("stop_admin_bot", await f("cmd stop admin bot", lang)),
                types.BotCommand("stop_client_bots", await f("cmd stop client bots", lang)),
                types.BotCommand("stop_friendly_bots", await f("cmd stop friendly bots", lang)),
                types.BotCommand("stop_bots", await f("cmd stop bots", lang)),
                types.BotCommand("restart_bots", await f("cmd restart bots", lang)),
                types.BotCommand("rb", await f("cmd restart bots", lang)),
                types.BotCommand("restart_service_bot", await f("cmd restart service bot", lang)),
                types.BotCommand("rsb", await f("cmd restart service bot", lang)),
                types.BotCommand("restart_api_handler", await f("cmd restart api handler", lang)),
                types.BotCommand("rah", await f("cmd restart api handler", lang)),
                types.BotCommand("pgit", await f("cmd pull changes handler", lang)),
                types.BotCommand("restart_admin_bot", await f("cmd restart admin bot", lang)),
                types.BotCommand("rab", await f("cmd restart admin bot", lang)),
                types.BotCommand("restart_client_bots", await f("cmd restart client bots", lang)),
                types.BotCommand("rcb", await f("cmd restart client bots", lang)),
                types.BotCommand("restart_friendly_bots", await f("cmd restart friendly bots", lang)),
                types.BotCommand("rfb", await f("cmd restart friendly bots", lang)),
                types.BotCommand("restart_client_bots_webhooks", await f("cmd restart client bots webhooks", lang)),
                types.BotCommand("rcbw", await f("cmd restart client bots webhooks", lang)),
                types.BotCommand("restart_friendly_bots_webhooks", await f("cmd restart friendly bots webhooks", lang)),
                types.BotCommand("rfbw", await f("cmd restart friendly bots webhooks", lang)),
                types.BotCommand("restart_platform", await f("cmd restart platform", lang)),
                types.BotCommand("rp", await f("cmd restart platform", lang)),
            ], language_code=lang
        )


@sync_to_async
def _reload_api():
    if cfg.RELOAD_API_COMMAND:
        os.system(cfg.RELOAD_API_COMMAND)


async def reload_api():
    await _reload_api()


@sync_to_async
def _pull_api():
    if cfg.PULL_API_COMMAND:
        os.system(cfg.PULL_API_COMMAND)


async def pull_api():
    await _pull_api()


@sync_to_async
def _deploy_web():
    if cfg.ROOT_BOT_DEPLOY_WEB_COMMAND:
        os.system(cfg.ROOT_BOT_DEPLOY_WEB_COMMAND)


async def deploy_web():
    await _deploy_web()


@sync_to_async
def _deploy_admin_web():
    if cfg.ROOT_BOT_DEPLOY_ADMIN_WEB_COMMAND:
        os.system(cfg.ROOT_BOT_DEPLOY_ADMIN_WEB_COMMAND)


async def deploy_admin_web():
    await _deploy_admin_web()


@sync_to_async
def _deploy_my7loc_web():
    if cfg.ROOT_BOT_DEPLOY_MY_7LOC_WEB_COMMAND:
        os.system(cfg.ROOT_BOT_DEPLOY_MY_7LOC_WEB_COMMAND)


async def deploy_my7loc_web():
    await _deploy_my7loc_web()


@sync_to_async
def _deploy_crm_web():
    if cfg.ROOT_BOT_DEPLOY_CRM_WEB_COMMAND:
        os.system(cfg.ROOT_BOT_DEPLOY_CRM_WEB_COMMAND)


async def deploy_crm_web():
    await _deploy_crm_web()
