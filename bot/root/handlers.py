import asyncio

from aiogram import Dispatcher
from aiohttp import web

import config as cfg
from core.kafka.producer import producer
from core.scheduler import Scheduler
from core.user.agreement_processor.buttons_handlers import \
    setup_tg_handlers as setup_agreement_handlers
from scheduler.init import init_scheduler
from utils.filters.bind import bind_general_filters
from utils.redefined_classes import Bot
from utils.router import get_router
from utils.update_localisation import update_localisation
from .authorize.handlers import *
from .helpers import make_dirs, set_super_bot_commands, stop_disabled_bots
from .main.handlers.group import *
from .main.handlers.private import *
from .main.handlers.web import *
from .management.handlers import *
from .management.handlers.commands_handlers import cmd_start_bots


async def on_startup(app: web.Application):
    dp: Dispatcher = app["dp"]

    await dp.reset_webhook(True)
    await dp.skip_updates()
    await dp.bot.set_webhook(
        f"http://{cfg.WEBHOOK_HOST}:{cfg.ROOT_BOT_PORT}/root_bot/"
        f"{cfg.ROOT_BOT_API_TOKEN}"
    )

    await update_localisation()

    await producer.initialise()

    Bot.set_current(dp.bot)
    get_router(dp)
    Dispatcher.set_current(dp)
    make_dirs()

    asyncio.ensure_future(cmd_start_bots())

    asyncio.ensure_future(set_super_bot_commands(dp))
    loop = asyncio.get_event_loop()
    loop.call_later(1, asyncio.create_task, stop_disabled_bots())

    scheduler = Scheduler()
    init_scheduler(scheduler)
    scheduler.run()
    app["scheduler"] = scheduler


async def on_shutdown(app: web.Application):
    await app["dp"].storage.close()
    await producer.stop()
    app["scheduler"].shutdown()


async def app_ctx(app: web.Application):
    await on_startup(app)

    yield

    await on_shutdown(app)


def bind_filters(dp: Dispatcher):
    bind_general_filters(dp)


def register_bot_handlers(dp: Dispatcher):
    Dispatcher.set_current(dp)

    register_main_commands_handlers(dp)
    register_management_commands_handlers(dp)

    register_authorize_message_handlers(dp)
    register_main_group_message_handlers(dp)

    setup_agreement_handlers(dp)


def register_web_handlers(app: web.Application, dp: Dispatcher):
    register_main_web_handlers(app, dp)


__all__ = [
    "app_ctx",
    "register_bot_handlers",
    "register_web_handlers",
    "bind_filters",
]
