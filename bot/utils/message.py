import aiowhatsapp as wa
import asyncio
import html
import logging
import re
from aiogram import types
from aiogram.utils.exceptions import CantParseEntities, MessageNotModified, Unauthorized
from aiowhatsapp import WhatsappBot
from aiowhatsapp.schemas import (
    SendTemplate, SendTemplateLanguage, Template,
)
from contextlib import suppress
from functools import partial
from psutils.func import check_function_spec
from traceback import format_stack
from typing import Any, Awaitable, Callable, Dict, List, Tuple

from config import (
    MEDIA_WITH_CAPTION, P4S_API_URL,
    SERVICE_BOT_API_TOKEN,
)
from core.marketing.exceptions import (
    MailingBotNotFoundOrWrongBotTypeError,
    MailingTemplateNotFoundError,
)
from db import models
from .media import get_media
from .platform_admins import send_message_to_platform_admins
from .redefined_classes import Bot, InlineKb, MenuKb
from .text import c, f, replace_html_symbols

debugger = logging.getLogger("debugger.notifications")


async def _send_message(
        first_sender_func: Callable[[], Awaitable[types.Message]] | None,
        second_sender_func: Callable[[], Awaitable[types.Message]] | None
) -> Tuple[types.Message | List[types.Message], types.Message | None]:
    """
    :param first_sender_func: must be functools.partial with all needed params specified
    :type first_sender_func: callable

    :param second_sender_func: must be functools.partial with all needed params
    specified
    :type second_sender_func: callable

    :return: returns tuple of two send messages or message and None
    :rtype: Tuple[types.Message, types.Message | None]
    """
    first_message = await first_sender_func() if first_sender_func else None
    second_message = await second_sender_func() if second_sender_func else None
    return first_message, second_message


async def send_chat_action(sender_bot: Bot, chat_id: int, token: str, action: str):
    coro = sender_bot.send_chat_action(chat_id, action)
    if token:
        with sender_bot.with_token(token):
            return await coro
    return await coro


async def send_tg_message(
        receiver_chat_id: int,
        content_type: str, *,
        bot_token: str = None,
        keyboard: InlineKb | MenuKb | types.ReplyKeyboardRemove | None = None,
        disable_notification: bool = False,
        disable_web_page_preview: bool = True,
        all_messages_in_result: bool = False,
        need_replace_html_symbols: bool = True,
        need_html_escape: bool = False,
        return_blocked_exception: bool = True,
        reply_to_message_id: int = None,
        **kwargs,
) -> types.Message | List[types.Message] | Exception:
    """

    :param receiver_chat_id: chat_id of user, that you want to send message
    :type receiver_chat_id: `int`

    :param content_type: Content type of message that you want to send
    :type content_type: `str`

    :param bot_token: Token of bot with which you want to send a message.
        Also, you can specify "service" for service bot or "admin" for admin bot
    :type bot_token: `str`

    :param keyboard: Keyboard that will be added to message
    :type keyboard: `InlineKb | MenuKb`

    :param disable_notification: Send the message silently.
        Users will receive a notification with no sound
    :type disable_notification: `bool`

    :param disable_web_page_preview: Disables link previews for links in this message
    :type disable_web_page_preview: `bool`

    :param all_messages_in_result: If set to True (default False), tuple of messages
    will be returned,
        else only last-sent message will be returned
    :type all_messages_in_result: `bool`

    :param need_replace_html_symbols: If set to True(default) functions
    replace_html_symbols will be called
        to send a message safely and not get error like "Can't parse entities"
    :type need_replace_html_symbols: `bool`

    :param need_html_escape: If set to True functions html.escape will be called
        to send a message safely and not get error like "Can't parse entities"
    :type need_html_escape: `bool`

    :param return_blocked_exception: If set to True Blocked or same exceptions will
    be returned (default=True)
    :type return_blocked_exception: bool

    :reply_to_message_id: Reply to message
    :type reply_to_message_id: int

    :param kwargs: Kwargs to send a message.
        It must include key equals content_type and
        be any str if content_type is "text"
        or be dict like
          {"photo": ["path_to_photo", ...], "video": ["path_to_video_1", ...]} if
          content_type is "media_group"
        or be file_path to media if content_type in cfg.MEDIA_WITH_CAPTION
        or be sticker_id if content_type is "sticker"
        or be dict with data for content_types "location" and "contact".

       Param "text" and "caption" equals and caption will be used only if "text" is
       not specified.
        This param can be specified for all content_types
    :type kwargs: `Any`

    :return: If success returns sent_message if all_messages_in_result is False
        or tuple of sent messages or message and None if all_messages_in_result is True
        if sending raised CantInitiateConversation or BotBlocked exception this
        functions will return it
    :rtype: `types.Message | Tuple[types.Message, types.Message] | Exception`
    """

    sending_kwargs = kwargs.copy()

    sender_bot = Bot.get_current()
    if bot_token == "service":
        bot_token = SERVICE_BOT_API_TOKEN

    if content_type in ["photo", "video"]:
        action = f"upload_{content_type}"
    elif content_type == "sticker":
        action = "choose_sticker"
    elif content_type == "voice":
        action = "upload_voice"
    elif content_type in MEDIA_WITH_CAPTION:
        action = "upload_document"
    else:
        action = "typing"

    try:
        await send_chat_action(sender_bot, receiver_chat_id, bot_token, action)
    except Unauthorized as e:
        if return_blocked_exception:
            return e
        raise

    sending_args: Dict[str, Any] = dict(
        chat_id=receiver_chat_id,
        disable_notification=disable_notification,
        reply_to_message_id=reply_to_message_id,
        parse_mode="HTML" if "parse_mode" not in kwargs else kwargs["parse_mode"],
    )

    text = sending_kwargs.get("text", sending_kwargs.get("caption"))

    if type(text) is not str:
        text = ""
    if isinstance(text, str):
        text = re.sub(r"\n{3,}", "\n\n", text)

    if need_html_escape:
        text = html.escape(text)
    elif need_replace_html_symbols:
        text = replace_html_symbols(text)

    if content_type == "media_group":

        need_add_text = bool(text)

        # формируем список медиа
        photos_and_videos = list()
        for media_type, medias in sending_kwargs.get("media_group", dict()).items():
            if media_type == "text":
                continue

            if media_type not in ["photo", "video", "audio"]:
                raise ValueError(
                    "MediaGroup must contain only photo, video or audio, not %s" %
                    media_type
                )

            for media in medias:
                media = get_media(media)
                media = types.InputMedia(media=media, type=media_type)
                if need_add_text:
                    media.caption = text
                    need_add_text = False

                photos_and_videos.append(media)

        # создаём альбом для отправки
        album = types.MediaGroup()
        album.attach_many(*photos_and_videos)

        if len(album.media) == 1:
            sending_kwargs.update(photo=album.media[0].file)
            return await send_tg_message(
                receiver_chat_id, album.media[0].type, bot_token=bot_token,
                keyboard=keyboard,
                disable_notification=disable_notification,
                disable_web_page_preview=disable_web_page_preview,
                all_messages_in_result=all_messages_in_result,
                need_replace_html_symbols=need_replace_html_symbols,
                return_blocked_exception=return_blocked_exception,
                reply_to_message_id=reply_to_message_id, **sending_kwargs
            )

        send_coro = sender_bot.send_media_group(
            receiver_chat_id, album,
            disable_notification=disable_notification,
            reply_to_message_id=reply_to_message_id,
        )

        if bot_token:
            with sender_bot.with_token(bot_token):
                messages = await send_coro
        else:
            messages = await send_coro

        if not all_messages_in_result:
            return messages[-1]
        return messages

    if content_type == "invoice":
        debugger.warning("content_type is invoice", exc_info=True)
        stack = "".join(format_stack())
        await send_message_to_platform_admins(
            f"Вызвана send_message с content_type \"invoice\"\n{stack}"
        )

        # test when send_message accepts content_type="invoice" and remove it
        # will be removed in new versions
        from db.models import Invoice
        from core.helpers import send_invoice

        payment_token = sending_kwargs.get("payment_token")
        invoice = await Invoice.get(sending_kwargs.get("invoice_id"))
        payload = c("pay_for_invoice", invoice_id=invoice.id)
        with sender_bot.with_token(bot_token):
            return await send_invoice(invoice, payload, payment_token)

    keyboard_in_caption_message = True

    if content_type == "text":
        sender_func = sender_bot.send_message
        sending_args.update(
            text=text, reply_markup=keyboard,
            disable_web_page_preview=disable_web_page_preview, )
        send_caption_as_new_message = False
    else:
        send_caption_as_new_message = True
        sender_func = getattr(sender_bot, f"send_{content_type}")

        if content_type in MEDIA_WITH_CAPTION:
            if content_type == "video":
                sending_args.update(supports_streaming=True)

            if len(text) <= 1024:
                send_caption_as_new_message = False
                sending_args.update(caption=text, reply_markup=keyboard)
            media = sending_kwargs.get(content_type)
            if isinstance(media, str):
                media = get_media(media)
            elif isinstance(media, dict):
                media = types.InputFile(**media)
            sending_args.update({content_type: media})
        elif content_type == "sticker":
            sticker = sending_kwargs.get(content_type)
            if isinstance(sticker, dict):
                sticker = sticker.get("sticker_id")
            sending_args.update(sticker=sticker)
        else:
            data = sending_kwargs.get(content_type)
            if data:
                if content_type == "contact" and data.get("user_id"):
                    data["chat_id"] = data.pop("user_id")

                if content_type == "location":
                    data.pop("address", None)
                    data["heading"] = data.pop("name", None)

                sending_args.update(**data, reply_markup=keyboard)
                keyboard_in_caption_message = False

        if content_type not in MEDIA_WITH_CAPTION and not text:
            send_caption_as_new_message = False
            sending_args.update(reply_markup=keyboard)

    sender_func = partial(sender_func, **check_function_spec(sender_func, sending_args))
    if send_caption_as_new_message:
        send_caption_func_keyboard = keyboard if keyboard_in_caption_message else None
        send_caption_func = partial(
            sender_bot.send_message,
            receiver_chat_id, text, reply_markup=send_caption_func_keyboard,
            disable_notification=disable_notification,
            disable_web_page_preview=disable_web_page_preview,
        )
    else:
        send_caption_func = None

    if content_type in ["contact", "location", "sticker"]:
        args = (send_caption_func, sender_func)
    else:
        args = (sender_func, send_caption_func)

    try:
        if bot_token:
            with sender_bot.with_token(bot_token):
                first_message, second_message = await _send_message(*args)
        else:
            first_message, second_message = await _send_message(*args)
    except Unauthorized as e:
        if return_blocked_exception:
            return e
        raise
    except CantParseEntities:
        return await send_tg_message(
            receiver_chat_id, content_type, bot_token=bot_token, keyboard=keyboard,
            disable_notification=disable_notification,
            disable_web_page_preview=disable_web_page_preview,
            all_messages_in_result=all_messages_in_result,
            need_replace_html_symbols=False,
            need_html_escape=True, return_blocked_exception=return_blocked_exception,
            reply_to_message_id=reply_to_message_id, **kwargs
        )
    else:
        if not all_messages_in_result:
            return second_message or first_message
        messages = []
        if first_message:
            messages.append(first_message)
        if second_message:
            messages.append(second_message)
        return messages


async def send_wa_message(
        receiver_wa_phone: str,
        content_type: str,
        bot_token: str,
        wa_from: str, *,
        keyboard: wa.types.ReplyKeyboard | wa.types.ListKeyboard | list[
            wa.types.ReplyButton] | None = None,
        reply_to_message_id: str | None = None,
        is_read: bool = False,
        **kwargs,
) -> wa.types.Message:
    if content_type == "photo":
        content_type = "image"

    bot = WhatsappBot(
        bot_token,
        wa_from,
    )
    bot_ctx_reset_token = WhatsappBot.set_current(bot)
    try:
        text = kwargs.get("text", kwargs.get("caption"))
        if isinstance(text, str):
            text = re.sub(r"\n{3,}", "\n\n", text)

        if content_type == "text":
            message = await bot.send_message(
                receiver_wa_phone, text,
                is_read, reply_to_message_id, keyboard,
            )
        elif content_type in ("image", "video", "document", "sticker", "audio"):
            media = kwargs.get(content_type)
            if not media and content_type == "image":
                media = kwargs.get("photo")

            if not media:
                raise ValueError(
                    f"When content_type is {content_type}, {content_type} argument is "
                    f"required"
                )

            if isinstance(media, str) and not re.fullmatch(
                    r"^https?://(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\."
                    r"[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)$", media,
            ) and "/" in media:
                if content_type == "document" and not kwargs.get("filename"):
                    kwargs["filename"] = media.rsplit("/", maxsplit=1)[-1]
                media = f"{P4S_API_URL}/{media}"

            sender_func = getattr(bot, f"send_{content_type}")
            sending_kwargs = {
                "is_read": is_read,
                "reply_to_message_id": reply_to_message_id,
            }

            if content_type in ("image", "video", "document"):
                if keyboard:
                    sending_kwargs["keyboard"] = keyboard
                if text:
                    sending_kwargs["caption"] = text
            elif keyboard:
                raise ValueError(
                    f"Keyboard argument is not allowed for content type={content_type}"
                )
            elif text:
                raise ValueError(
                    f"text and caption arguments are not allowed for content type="
                    f"{content_type}"
                )

            if content_type == "document" and "filename" in kwargs:
                sending_kwargs["filename"] = kwargs["filename"]

            message = await sender_func(
                receiver_wa_phone, media,
                **sending_kwargs,
            )
        elif content_type in ("contacts", "contact"):
            contacts = kwargs.get("contacts") or []
            has_errors = any(
                [not isinstance(contact, wa.types.Contact) for contact in contacts]
            )
            if has_errors:
                raise ValueError(
                    "all contacts must be instances of aiowhatsapp.types.Contact"
                )

            if not contacts:
                contact = kwargs.get("contact")
                if isinstance(contact, types.Contact):
                    contact = wa.types.Contact(
                        name=wa.types.ContactName(
                            formatted_name=contact.full_name,
                            first_name=contact.first_name,
                            last_name=contact.last_name,
                        ),
                        phones=[
                            wa.types.ContactPhone(
                                phone=contact.phone_number,
                            )
                        ]
                    )
                elif isinstance(contact, dict):
                    first_name = contact.get("first_name")
                    last_name = contact.get("last_name")
                    contact = wa.types.Contact(
                        name=wa.types.ContactName(
                            formatted_name=f"{first_name} {last_name}",
                            first_name=first_name,
                            last_name=last_name,
                        ),
                        phones=[
                            wa.types.ContactPhone(
                                phone=contact.get("phone_number"),
                                wa_id=contact.get("phone_number")
                            )
                        ]
                    )
                elif not isinstance(contact, wa.types.Contact):
                    raise ValueError(
                        "Incorrect contact type. "
                        "Contact must be instances of aiowhatsapp.types.Contact or "
                        "aiogram.types.Contact"
                    )

                contacts = [contact]

            message = await bot.send_contacts(
                receiver_wa_phone,
                *contacts,
                is_read=is_read,
                reply_to_message_id=reply_to_message_id,
            )
        elif content_type == "location":
            location = kwargs.get("location", {})
            if isinstance(location, types.Location):
                latitude, longitude, name = (location.latitude, location.longitude,
                                             location.heading)
            else:
                latitude, longitude = location.get("latitude"), location.get(
                    "longitude"
                )

            if not all((latitude, longitude)):
                raise ValueError(
                    "Incorrect location. Both of latitude and longitude must be "
                    "specified in location argument dict"
                )

            name, address = location.get("name"), location.get("address")

            message = await bot.send_location(
                receiver_wa_phone,
                latitude, longitude,
                name, address,
                is_read, reply_to_message_id,
            )
        elif content_type == "interactive":
            if "interactive" not in kwargs:
                raise ValueError(
                    "Argument interactive is required when content_type is "
                    "'interactive'"
                )

            message = await bot.send_interactive(
                receiver_wa_phone, **kwargs["interactive"],
                is_read=is_read, reply_to_message_id=reply_to_message_id,
            )
        else:
            raise ValueError(f"Invalid content_type {content_type}")
    finally:
        WhatsappBot.reset_current(bot_ctx_reset_token)

    return message


async def send_wa_template_message(
        template_id: int,
        to: str,
        bot: models.ClientBot,
        lang: str | None = None,
        variables: list[dict] | None = None,
        reply_buttons_data: list[dict] | None = None,
):
    master_template = await models.WAMasterTemplate.get(id=template_id)
    template = await models.WATemplate.get(master_template_id=template_id, lang=lang)
    if not template:
        group = await models.Group.get(bot.group_id)
        template = await models.WATemplate.get(
            master_template_id=template_id, lang=group.lang
        )
        if not template:
            templates = await models.WATemplate.get_list(master_template_id=template_id)
            if templates:
                template = templates[0]

    if not template or not master_template:
        raise MailingTemplateNotFoundError()
    if not bot or not bot.bot_type == "whatsapp":
        raise MailingBotNotFoundOrWrongBotTypeError()

    header_media = await models.WATemplate.get_header_media(template)
    header_media_url = header_media.url if header_media else None

    debugger.debug(f"Template language: {lang} template ID: {template.id}")

    payload = SendTemplate(
        to=to,
        messaging_product="whatsapp",
        recipient_type="individual",
        type="template",
        template=Template(
            name=master_template.name,
            language=SendTemplateLanguage(
                code=template.lang,
            ),
            components=template.build_send_template_components(
                variables, header_media_url, reply_buttons_data,
            ),
        )
    )

    api = wa.WhatsappApiClient(
        bot.token,
        bot.whatsapp_from,
        whatsapp_business_account_id=bot.whatsapp_business_account_id,
    )

    await api.send_template_message(payload)


async def send_or_edit_message(
        message: types.Message,
        message_text: str = None,
        media: str = None,
        content_type: str = None,
        keyboard: InlineKb = None,
        new: bool = False,
        lang: str | None = None,
        delete_old_message: bool = True,
) -> types.Message:
    if not any([message_text, (media and content_type), keyboard]):
        raise ValueError(
            "One of message_text, (media and content_type) or keyboard must be "
            "specified!"
        )

    if any([media, content_type]) and not all([media, content_type]):
        raise ValueError(
            "If one of media, content_type is specified other must be specified too!"
        )

    if not media:
        content_type = "text"

    if content_type == "text":
        if message.content_type == "text" and not new:
            with suppress(MessageNotModified):
                return await message.edit_text(message_text, reply_markup=keyboard)
            return message

        send_coro = message.answer(message_text, reply_markup=keyboard)
        results = await asyncio.gather(send_coro, message.delete())
        return results[0]

    if message.content_type == content_type and not new:
        media = get_media(media)
        media = types.InputMedia(type=content_type, media=media, caption=message_text)

        with suppress(MessageNotModified):
            return await message.edit_media(media, reply_markup=keyboard)
        return message

    try:
        msg = await send_tg_message(
            message.chat.id, content_type, keyboard=keyboard, text=message_text,
            **{content_type: media}
        )
    except Exception as e:
        logging.error(e, exc_info=True)

        unable_send_error = await f("unable to send media error", lang)
        msg = await send_tg_message(
            message.chat.id, "text", keyboard=keyboard,
            text=f"{unable_send_error}\n\n{message_text}"
        )
    if delete_old_message:
        await message.delete()
    return msg
