from __future__ import annotations

import logging
import os
import re
import shutil
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import TYPE_CHECKING

import aiogram as tg
import cv2
import math
from PIL import Image, ImageFilter
from psutils.decorators import sync_to_async

import config as cfg
from config import IMAGE_PREVIEW_URL, MIN_TG_PHOTO_WIDTH, VIDEO_PREVIEW_URL

if TYPE_CHECKING:
    from _typeshed import SupportsRead, SupportsWrite


def save_file(file_path: str, path_to_save: str = None) -> str | None:
    new_file_path = make_file_path_to_save(file_path, path_to_save)
    if not os.path.exists(file_path):
        return

    shutil.copyfile(file_path, new_file_path)
    return new_file_path


def make_file_path_to_save(orig_file_path: str, path_to_save: str | None = None) -> str:
    split_file_path = orig_file_path.rsplit('/', 1)
    destination_dir = split_file_path[0].rsplit('/', 1)[-1]
    file_name = "".join(str(datetime.utcnow().timestamp()).split("."))
    file_name = file_name + "." + split_file_path[-1].rsplit(".")[-1]

    if path_to_save:
        if not os.path.isdir(path_to_save):
            os.makedirs(path_to_save, exist_ok=True)
        new_file_path = os.path.join(path_to_save, file_name)
    else:
        new_file_path = os.path.join(cfg.STATIC_DB, destination_dir, file_name)
    return new_file_path


def get_media(media: str):
    if not isinstance(media, str):
        media = ""
    if re.fullmatch(cfg.URL_RE_PATTERN, media):
        return media.strip()
    else:
        if not media.startswith(cfg.STATIC_DB) and not media.startswith(
                cfg.STORAGE_STATIC_DIR
        ) and not media.startswith(f"{cfg.STATIC_DIR}/{cfg.UPLOADS_DIR}"):
            media = os.path.join(cfg.STATIC_DB, media)
        try:
            media = tg.types.InputFile(path_or_bytesio=media)
        except:
            media = ""
    if not media:
        media = None
    return media


@sync_to_async
def scale_photo(photo_path: str):
    if not photo_path:
        return
    try:
        photo = Image.open(photo_path)
    except FileNotFoundError:
        return
    except Exception as e:
        logger = logging.getLogger()
        return logger.error(e, exc_info=True)

    min_width = MIN_TG_PHOTO_WIDTH

    try:
        width, height = photo.size
        if width < min_width:
            photo = photo.resize(
                (min_width, int(float(height) * float(min_width / width)))
            )
            width, height = photo.size
            photo.save(photo_path)
        if height > width:
            new_photo = photo.resize((height, height)).filter(
                ImageFilter.GaussianBlur(radius=40)
            )
            new_photo.paste(photo, ((height - width) // 2, 0))
            new_photo.save(photo_path)
    except Exception as e:
        logger = logging.getLogger()
        return logger.error(e, exc_info=True)


@dataclass
class ThumbnailImageData:
    width: int
    height: int
    aspect_ratio: tuple[float | int, float | int]
    raw_image: Image.Image | None = None


def make_thumbnail_image_sync(
        src: str | bytes | Path | SupportsRead[bytes],
        dest: str | bytes | Path | SupportsWrite[bytes] | None = None,
        desired_aspect_ratio: tuple[float | int, float | int] | None = None,
        max_size: float | int | None = None,
        debug: bool = False,
        return_raw_image: bool = False,
) -> ThumbnailImageData:
    image = Image.open(src)

    image_width, image_height = image.size

    # calculating an image aspect ratio
    gcd = math.gcd(image_width, image_height)
    image_aspect_ratio = (round(image_width / gcd, 2), round(image_height / gcd, 2))

    if max_size is None:
        max_size = max(image_width, image_height)

    if not desired_aspect_ratio:
        desired_aspect_ratio = image_aspect_ratio

    # calculating desired width and height
    if desired_aspect_ratio[0] >= desired_aspect_ratio[1]:
        desired_width = max_size
        desired_height = int(
            desired_width / (desired_aspect_ratio[0] / desired_aspect_ratio[1])
        )
    else:
        desired_height = max_size
        desired_width = int(
            desired_height / (desired_aspect_ratio[1] / desired_aspect_ratio[0])
        )

    new_ratio = desired_width / desired_height
    original_ratio = image_width / image_height

    if image_width != desired_width or image_height != desired_height:
        # calculating new image with and height
        if original_ratio > new_ratio:
            new_width = desired_width
            new_height = int(image_height * (new_width / image_width))
        else:
            new_height = desired_height
            new_width = int(image_width * (desired_height / image_height))

        image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        image_width, image_height = image.size

    # make image desired aspect ratio by adding background
    if image_aspect_ratio != desired_aspect_ratio:
        x_offset = (desired_width - image_width) // 2
        y_offset = (desired_height - image_height) // 2

        # adding a transparency background for transparency images
        if has_transparency(image):
            new_image = Image.new("RGBA", (desired_width, desired_height), (0, 0, 0, 0))
        # adding blurred image as a background for not transparency images
        else:
            new_image = image.resize(
                (desired_width, desired_height), Image.Resampling.LANCZOS
            )
            try:
                new_image = new_image.filter(ImageFilter.GaussianBlur(radius=40))
            except Exception as err:
                logging.error(f"{src}\n{err}", exc_info=True)

        new_image.paste(image, (x_offset, y_offset))
        image = new_image

    image_width, image_height = image.size

    if debug:
        image.show(f"{image_width}x{image_height}")
    elif dest:
        image.save(dest)

    return ThumbnailImageData(
        image_width,
        image_height,
        desired_aspect_ratio,
        raw_image=image if return_raw_image else None
    )


make_thumbnail_image = sync_to_async(make_thumbnail_image_sync)


def has_transparency(img: Image):
    if img.info.get("transparency", None) is not None:
        return True

    if img.mode == "P":
        transparent = img.info.get("transparency", -1)
        for _, index in img.getcolors():
            if index == transparent:
                return True

    elif img.mode == "RGBA":
        extrema = img.getextrema()
        if extrema[3][0] < 255:
            return True

    return False


def get_qrcode_data(file_path: str):
    img_decode = cv2.imread(file_path)
    decoder = cv2.QRCodeDetector()
    data, bbox, clear_qrcode = decoder.detectAndDecode(img_decode)
    return data


def delete_file(path: str):
    if path and os.path.isfile(path):
        try:
            os.remove(path)
        except Exception as e:
            logger = logging.getLogger()
            logger.error(e, exc_info=True)


def make_preview_url(media_type: str, path: str, max_size: int = 1024):
    if media_type != "photo" and not media_type.startswith(
            "image"
    ) and not media_type.startswith("video"):
        return None

    path_for_preview = path.replace("static/", "")
    if media_type == "photo" or media_type.startswith("image"):
        preview_url = IMAGE_PREVIEW_URL
    else:
        preview_url = VIDEO_PREVIEW_URL

    file_dir, file_name = path_for_preview.rsplit("/", 1)

    return preview_url.format(
        max_size=max_size,
        file_dir=file_dir,
        file_name=file_name,
        file_path=path_for_preview,
    ) if preview_url else None


__all__ = [
    "get_media",
    "scale_photo",
    "save_file",
    "get_qrcode_data",
    "delete_file",
    "make_thumbnail_image",
    "make_thumbnail_image_sync",
    "ThumbnailImageData",
    "make_preview_url",
]
