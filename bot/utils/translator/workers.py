import asyncio
import traceback

from psutils.translator import translate
from psutils.translator.translator_limiter.exceptions import \
    TranslatorLimiterLimitExceededError

from config import (
    AUTO_TRANSLATION_BACKGROUND_COLOR, AUTO_TRANSLATION_FOREGROUND_COLOR, DEFAULT_LANG,
    IS_LOCALISATION_AUTO_TRANSLATE_ENABLED,
)
from db import DBSession
from db.models import TranslationBackground
from loggers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.processes_manager.background_worker import LoopBackgroundWorker
from . import Translator
from .db_funcs import get_pending_translation_background
from .functions import send_message_to_translator_monitoring_group, split_dictionary
from ..localisation import localisation


class LocalisationWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "load localisation"
    DEFAULT_TIMEOUT = 5
    ON_WRITING_ERROR_SLEEP = 60
    WRITE_TRIES_COUNT = 5

    def __init__(self, **kwargs):
        self.started_workers: set[int] = set()
        super().__init__(**kwargs)

    def start_worker(self, background_id: int):
        self.started_workers.add(background_id)

    def stop_worker(self, background_id: int):
        if background_id in self.started_workers:
            self.started_workers.remove(background_id)

    async def iteration(self):
        with DBSession():
            background = await get_pending_translation_background(self.started_workers)
            if not background:
                return

            if (
                    not IS_LOCALISATION_AUTO_TRANSLATE_ENABLED or
                    background.lang not in
                    await Translator.get_supported_languages(DEFAULT_LANG)
            ):
                return await background.finish()

            logger = JSONLogger(
                "localisation-worker", {
                    "background": background.as_dict(True)
                }
            )

            try:

                logger.debug(f"Started Localisation worker task", background.lang)

                self.start_worker(background.id)
                result = await self.process_localisation(background, logger)

            except Exception as e:
                logger.error("FAILED Localisation worker task", background.lang, e)
            else:
                if result is True:
                    logger.debug(
                        "SUCCESS Localisation worker task", background.lang
                    )
                else:
                    logger.debug(
                        "FAILED Localisation worker task with no results to write",
                        background.lang
                    )
            finally:
                self.stop_worker(background.id)

    @classmethod
    async def translate_part(
            cls, part: dict[str, str],
            dest_lang: str, origin_lang: str,
            is_limit_exceeded: bool = False,
    ) -> tuple[bool, dict[str, str]]:
        texts_to_translate = {}
        cached_texts = {}

        for variable, origin_text in part.items():
            cached_text = await localisation.get_text_from_cache(variable, dest_lang)
            if cached_text is not None:
                cached_texts[variable] = cached_text
            else:
                texts_to_translate[variable] = origin_text

        if is_limit_exceeded or not texts_to_translate:
            return is_limit_exceeded, cached_texts

        texts_and_params = [localisation.get_params(text) for text in
                            list(texts_to_translate.values())]
        texts = [item[0] for item in texts_and_params]
        params = [item[1] for item in texts_and_params]

        try:
            translations = await translate(texts, dest_lang, origin_lang, False)
        except TranslatorLimiterLimitExceededError:
            return True, cached_texts

        translations = [localisation.get_restore_text(text, param) for text, param in
                        zip(translations, params)]

        translated_texts = dict(zip(texts_to_translate.keys(), translations))
        return False, dict(**cached_texts, **translated_texts)

    async def process_localisation(
            self, background: TranslationBackground, logger: JSONLogger
    ):
        await localisation.update_data()

        is_updating = background.lang in await localisation.langs

        default_lang_data = await localisation.storage.get_data(DEFAULT_LANG)
        all_variables = await localisation.variables

        if is_updating:
            lang_data = await localisation.storage.get_data(background.lang)
        else:
            lang_data = None

        if not is_updating or not lang_data or not all(lang_data.values()):
            if is_updating:
                variables = [variable for variable in all_variables if
                             not lang_data.get(variable)]
            else:
                variables = all_variables
                await send_message_to_translator_monitoring_group(
                    f"Adding a new language to localisation: {background.lang}"
                )

            logger.add_data(
                {
                    "is_updating": is_updating,
                    "is_lang_data": bool(lang_data)
                }
            )

            logger.debug(
                f"LOCALE WORKER Language", background.lang,
                {
                    "is_updating": is_updating,
                    "is_lang_data": bool(lang_data),
                },
            )

            texts_and_langs = await localisation.get_origin_text_and_lang(
                variables, default_lang_data
            )

            is_limit_exceeded = False
            translated_data: dict[str, str | None] = {}

            for origin_lang, data in texts_and_langs.items():
                parts = split_dictionary(data)
                for part in parts:
                    logger.debug(
                        f"LOCALE WORKER Language {background.lang} ",
                        {
                            "is_part": bool(part),
                            "origin_lang": origin_lang,
                        }
                    )

                    try:
                        is_limit_exceeded, result = await self.translate_part(
                            part, background.lang,
                            origin_lang, is_limit_exceeded,
                        )
                    except Exception as e:
                        logger.error(
                            "Translating localisation part error", e, {
                                "origin_lang": origin_lang,
                            }
                        )
                        await send_message_to_translator_monitoring_group(
                            f"An error occurred while translating "
                            f"to {background.lang} from {origin_lang} "
                            f"with data: \n{data}",
                        )
                    else:
                        translated_data.update(result)
        else:
            is_limit_exceeded = False
            translated_data = {}

        if not translated_data:
            if is_limit_exceeded:
                return False

            result = True  # no needed to write to table
        else:
            result = await self.update_localisation_lang_data(
                translated_data, background.lang, logger
            )

        if result:
            await background.finish()

        return True

    async def update_localisation_lang_data(
            self,
            new_data: dict[str, str],
            lang: str,
            logger: JSONLogger
    ):
        for i in range(self.WRITE_TRIES_COUNT):
            try:
                await localisation.update_data()
                result = await localisation.update_lang_data(
                    new_data,
                    lang,
                    background_color=AUTO_TRANSLATION_BACKGROUND_COLOR,
                    foreground_color=AUTO_TRANSLATION_FOREGROUND_COLOR
                )
            except Exception as e:
                logger.error("update_localisation_lang_data error", e)
                text = (
                    f"@Semka_96, Localisation worker writing data to table error.\n"
                    f"{repr(e)}\n{''.join(traceback.format_stack())}"
                )
                await send_message_to_translator_monitoring_group(text)
                await asyncio.sleep(self.ON_WRITING_ERROR_SLEEP)
            else:
                if not result:
                    logger.error(
                        "Data was not written to the table", {
                            "lang": lang
                        }
                    )
                    text = f"@Semka_96, Translations were not written to the table.\n" \
                           f"Localisation.update_lang_data returned False.\n" \
                           f"Probably google table is needed in manual fix"
                    await send_message_to_translator_monitoring_group(text)
                else:
                    await localisation.delete_from_cache(*new_data.keys(), lang=lang)
                return result
        else:
            for variable, value in new_data.items():
                await localisation.save_text_to_cache(variable, value, lang)
            text = (f"@Semka_96, Translations was not written to t"
                    f"able in {self.WRITE_TRIES_COUNT} for lang {lang}")
            await send_message_to_translator_monitoring_group(text)
            return False
