from db import db_func, sess
from db.models import TranslationBackground


@db_func
def get_pending_translation_background(started_background: list[int]) -> TranslationBackground:
    query = sess().query(TranslationBackground)

    query = query.filter(TranslationBackground.status == "enabled")
    query = query.filter(TranslationBackground.id.not_in(started_background))

    query = query.limit(1)

    return query.one()
