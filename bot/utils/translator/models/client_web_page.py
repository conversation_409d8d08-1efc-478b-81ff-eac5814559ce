from psutils.translator.models import Translator<PERSON>ield, TranslatorModel
from psutils.translator.models.types import (
    TranslatorDict, TranslatorList,
    TranslatorString,
)


class ClientWebPageTranslatorModel(TranslatorModel):
    title = TranslatorField()
    button_title = TranslatorField()
    page_content = TranslatorField(
        TranslatorDict(
            {
                "blocks": TranslatorList(
                    TranslatorDict(
                        {
                            "data": TranslatorDict(
                                {
                                    "alternateText": TranslatorString(),
                                    "callToActionTitle": TranslatorString(),
                                    "title": TranslatorString(),
                                    "text": TranslatorString(),
                                    "caption": TranslatorString(),
                                    "items": TranslatorList(
                                        TranslatorDict(
                                            {
                                                "content": TranslatorString(),
                                            },
                                            copy_all_original_keys=True,
                                        )
                                    ),
                                    "content": TranslatorList(
                                        TranslatorList(
                                            TranslatorString(),
                                        )
                                    ),
                                },
                                copy_all_original_keys=True,
                            ),
                        },
                        copy_all_original_keys=True,
                    )
                )
            },
            copy_all_original_keys=True,
            check_deep_keys_to_translate=False,
        )
    )
