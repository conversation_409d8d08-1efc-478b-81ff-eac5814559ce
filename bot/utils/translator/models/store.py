from psutils.translator.models import Translator<PERSON>ield, TranslatorModel


class StoreAttributeTranslatorModel(TranslatorModel):
    name = TranslatorField()


class StoreAttributeGroupTranslatorModel(TranslatorModel):
    name = TranslatorField()


class StoreProductTranslatorModel(TranslatorModel):
    name = TranslatorField()
    description = TranslatorField()
    pti_info_text = TranslatorField()


class StoreCategoryTranslatorModel(TranslatorModel):
    name = TranslatorField()


class StoreCharacteristicTranslatorModel(TranslatorModel):
    name = TranslatorField()


class StoreCharacteristicValueTranslatorModel(TranslatorModel):
    value = TranslatorField()


class StoreTranslatorModel(TranslatorModel):
    name = TranslatorField()
    description = TranslatorField()
    ai_description = TranslatorField()


class BrandCustomSettingsTranslatorModel(TranslatorModel):
    name = TranslatorField()
    description = TranslatorField()
    label_comment = TranslatorField()
    info = TranslatorField()


class PaymentSettingsTranslatorModel(TranslatorModel):
    name = TranslatorField()
    description = TranslatorField()
    label_comment = TranslatorField()
    post_payment_info = TranslatorField()


class ObjectPaymentSettingsTranslatorModel(TranslatorModel):
    post_payment_info = TranslatorField()
