import re
from typing import Any, Iterable, Literal, Tuple, Type, Union

from google.api_core.exceptions import ResourceExhausted
from psutils.translator import BaseTranslator
from psutils.translator.models.base import TranslatorModel
from psutils.translator.schemas import TranslateObjectData
from psutils.translator.translator_limiter.exceptions import \
    TranslatorLimiterLimitExceededError
from psutils.type_vars import T, T2

from config import USE_LOCALISATION
from core.billing.quota_processor import BillingQuotaProcessor
from db import db_func, sess
from db.models import Translation
from loggers import JSONLogger
from schemas import BillingProductCode
from .functions import send_message_to_translator_monitoring_group


def __get_quantity__(content: str | list[str | None] | dict[str, str | None] | None):
    if not content:
        return 0

    if isinstance(content, str):
        return len(content)

    if isinstance(content, dict):
        content = content.values()

    return sum(map(lambda x: len(x) if x else 0, content))


class AutoTranslateDisabled(Exception):
    pass


class Translator(BaseTranslator):
    EXCLUDED_TRANSLATOR_VALUES = (USE_LOCALISATION,)

    @classmethod
    async def create_translation(cls, object_: Any, lang: str):
        translation = await Translation.create(*cls.object(object_), lang)
        return translation

    @classmethod
    async def get_translation(cls, object_: Any, lang: str) -> Translation:
        translation = await Translation.get(*cls.object(object_), lang)
        return translation

    @classmethod
    async def get_or_create_translation(cls, object_: Any, lang: str) -> Translation:
        return await Translation.create(*cls.object(object_), lang)

    @classmethod
    async def get_languages(cls, object_: Any) -> list[str]:
        return await Translation.get_languages(*cls.object(object_))

    @classmethod
    async def clear_translation(cls, object_, lang: str, key: str) -> bool:
        keys = (int(el) if el.isdecimal() else el for el in key.split("__"))
        return await Translation.clear(*cls.object(object_), lang, *keys)

    @classmethod
    async def clear_all_translations(cls, object_, key: str) -> bool:
        keys = (int(el) if el.isdecimal() else el for el in key.split("__"))
        return await Translation.clear_full(*cls.object(object_), *keys)

    @classmethod
    async def delete_translation(cls, object_: Any, lang: str) -> bool:
        return await Translation.delete(*cls.object(object_), lang)

    @classmethod
    async def delete_all_translations(cls, object_: Any) -> bool:
        return await Translation.delete_full(*cls.object(object_))

    @classmethod
    @db_func
    def add_data(
            cls, translation: Translation, field_name: Union[Tuple[str, int], str],
            value: str
    ):
        if isinstance(field_name, tuple):
            field_name, question_id = field_name
        else:
            question_id = None
        if field_name == "questions":
            if question_id is None:
                raise ValueError()
            if not translation.data.get(field_name):
                translation.data[field_name] = {}
            translation.data[field_name][str(question_id)] = value
        else:
            translation.data[field_name] = value
        sess().commit()

    @classmethod
    @db_func
    def save_data(cls, translation: Translation, data: dict[str, Any]):
        translation.data = data
        sess().commit()

    @classmethod
    def fix_translation_from_original(
            cls, original_text: str, translate_text: str
    ) -> str:
        if not translate_text or not original_text:
            return translate_text

        res_text = translate_text
        params_original = re.findall(
            r"(=[^\[\]]+])", original_text, flags=re.IGNORECASE
        )
        params_translate = re.findall(
            r"(=[^\[\]]+])", translate_text, flags=re.IGNORECASE
        )

        for param_translate, param_original in zip(params_translate, params_original):
            res_text = res_text.replace(param_translate, param_original)

        return res_text

    @classmethod
    async def get_translations_data(
            cls, object_: Any,
            langs_list: list[str] | None,
    ) -> dict[str, dict[str, str | None]]:
        result: dict[str, Any] = {}

        model = TranslatorModel.detect_model(object_.__class__.__name__)

        if not langs_list:
            return result

        for lang in langs_list:
            translation = await cls.get_translation(object_, lang)
            translator_obj = model(object_, translation.data if translation else None)
            result[lang] = translator_obj.translations

        return result

    @classmethod
    def get_original_values(cls, object_: Any, fields_: Iterable[str]):
        return [cls.get_field_value(object_, field) for field in fields_]

    @classmethod
    def fill_empty_data_values_by_origin_value(cls, object_: Any, data: dict):
        for key, value in data.items():
            if value is None:
                data[key] = cls.get_field_value(object_, key)

    @classmethod
    async def translate(
            cls, content: str | list[str] | dict[str, str],
            dest: str, src: str,
            no_error_when_handler_set: bool = True,
            on_limit_exceeded_kwargs: dict[str, Any] | None = None,
            group_id: int | Literal["internal"] | None = None,
            is_auto_translate_allowed: bool = True,
            **kwargs,
    ):
        if not group_id:
            raise ValueError("group_id is required keyword argument")

        quantity_to_translate = __get_quantity__(content)
        try:
            if quantity_to_translate and is_auto_translate_allowed:
                if (
                        group_id != "internal" and
                        not await BillingQuotaProcessor(
                            group_id, BillingProductCode.TRANSLATOR_SYMBOL,
                            return_on_error="false",
                            suppress_sending_not_available_error=True,
                        ).record_usage(quantity_to_translate)):
                    return None

                return await super().translate(
                    content, dest, src,
                    no_error_when_handler_set=False,
                    on_limit_exceeded_kwargs=on_limit_exceeded_kwargs,
                    **kwargs,
                )
            else:
                raise AutoTranslateDisabled()
        except (
                TranslatorLimiterLimitExceededError,
                AutoTranslateDisabled
        ):
            return None
        except ResourceExhausted as e:
            return await cls.send_error_and_return_none(e)
        except Exception as e:
            return await cls.send_error_and_return_none(e, True)

    @classmethod
    async def send_error_and_return_none(
            cls, e: Exception, trace_required: bool | None = False
    ):
        JSONLogger("translator").error(e, exc_info=trace_required)
        # text = f"{''.join(traceback.format_stack()[:-1])}{e}"
        await send_message_to_translator_monitoring_group(
            f"An error occurred in translator.\n"
            f"Detail in errors.log:\n{repr(e)}"
        )

        return None

    # noinspection PyMethodOverriding
    @classmethod
    def translate_object(
            cls, object_: Any,
            lang: str, original_lang: str,
            no_error_when_handler_set: bool = True,
            translation: Any = None,
            field_name: str | None = None,
            result_type: Literal["dict", "obj", "field"] | None = None,
            result_obj: T = None,
            model: Type[TranslatorModel] | None = None,
            on_limit_exceeded_kwargs: dict[str, Any] | None = None,
            force_original: bool = False,
            fallback_to_original_on_error: bool = True,
            *,
            group_id: int | Literal["internal"],
            is_auto_translate_allowed: bool,
            **kwargs,
    ) -> dict[str, Any] | T | Any:
        return super().translate_object(
            object_, lang, original_lang,
            no_error_when_handler_set,
            translation, field_name,
            result_type, result_obj,
            model, on_limit_exceeded_kwargs,
            force_original,
            fallback_to_original_on_error,
            group_id=group_id,
            is_auto_translate_allowed=is_auto_translate_allowed,
            **kwargs,
        )

    @classmethod
    async def translate_objects_list(
            cls, objects_data: list[TranslateObjectData[T]],
            lang: str, original_lang: str,
            no_error_when_handler_set: bool = True,
            on_limit_exceeded_kwargs: dict[str, Any] | None = None,
            force_original: bool = False,
            fallback_to_original_on_error: bool = True,
            group_id: int | Literal["internal"] | None = None,
            is_auto_translate_allowed: bool = None,
            **kwargs,
    ) -> list[dict[str, Any] | T | Any]:
        if not group_id:
            raise ValueError("Group id is required")
        if is_auto_translate_allowed is None:
            raise ValueError("is_auto_translate_allowed is not specified")
        return await super().translate_objects_list(
            objects_data, lang, original_lang,
            no_error_when_handler_set,
            on_limit_exceeded_kwargs,
            force_original,
            fallback_to_original_on_error,
            group_id=group_id,
            is_auto_translate_allowed=is_auto_translate_allowed,
            **kwargs
        )

    @classmethod
    async def translate_objects_dict(
            cls, objects_data: dict[T2, TranslateObjectData[T]],
            lang: str, original_lang: str,
            no_error_when_handler_set: bool = True,
            on_limit_exceeded_kwargs: dict[str, Any] | None = None,
            force_original: bool = False,
            fallback_to_original_on_error: bool = True,
            group_id: int | Literal["internal"] | None = None,
            is_auto_translate_allowed: bool = None,
            **kwargs,
    ) -> dict[T2, dict[str, Any] | T | Any]:
        return await super().translate_objects_dict(
            objects_data, lang, original_lang,
            no_error_when_handler_set,
            on_limit_exceeded_kwargs,
            force_original,
            fallback_to_original_on_error,
            group_id=group_id,
            is_auto_translate_allowed=is_auto_translate_allowed,
            **kwargs,
        )
