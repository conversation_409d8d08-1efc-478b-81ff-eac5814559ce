import logging

from psutils.text import replace_html_symbols

import config as cfg
from config import ROOT_BOT_API_TOKEN, TRANSLATOR_MONITORING_GROUP_ID

from utils.redefined_classes import Bo<PERSON>


def split_dictionary(input_dict: dict[str, str]) -> list[dict[str, str]]:
    res = []
    new_dict = {}
    for key, value in input_dict.items():
        if len(new_dict) < cfg.TRANSLATION_LOCALISATION_PART:
            new_dict[key] = value
        else:
            res.append(new_dict)
            new_dict = {key: value}
    res.append(new_dict)
    return res


async def send_message_to_translator_monitoring_group(text: str):
    close_session = False
    try:
        bot = Bot.get_current(no_error=False)
    except:
        bot = Bot(ROOT_BOT_API_TOKEN)
        close_session = True

    try:
        text = replace_html_symbols(text)
        await bot.send_message(
            TRANSLATOR_MONITORING_GROUP_ID,
            text
        )
        if close_session:
            await bot.session.close()
    except Exception as e:
        logging.error(e, exc_info=True)
