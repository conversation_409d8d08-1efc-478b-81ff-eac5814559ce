import traceback
from datetime import timedelta, datetime

from psutils.translator.async_translation import AsyncTranslate
from psutils.translator.translator_limiter.exceptions import TranslatorLimiterLimitExceededError

from config import PATH_TO_GOOGLE_CREDS_JSON
from utils.translator.functions import send_message_to_translator_monitoring_group

last_limit_exceeded_error_sending_time: datetime | None = None
TIMEOUT = timedelta(minutes=10)


async def on_limit_exceeded(error: TranslatorLimiterLimitExceededError):
    global last_limit_exceeded_error_sending_time

    if not last_limit_exceeded_error_sending_time or \
            last_limit_exceeded_error_sending_time + TIMEOUT <= datetime.utcnow():

        text = f"{''.join(traceback.format_stack()[:-1])}{error}"
        await send_message_to_translator_monitoring_group(text)
        last_limit_exceeded_error_sending_time = datetime.utcnow()


def initialise():
    AsyncTranslate.initialise(PATH_TO_GOOGLE_CREDS_JSON, on_limit_exceeded)
