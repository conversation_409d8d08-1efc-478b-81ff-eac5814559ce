import inspect
from collections.abc import Callable
from typing import Awaitable, Literal, Protocol

from db.models import Group, QuantitativeService, QuantitativeServiceTypeEnum
from schemas import SystemNotificationCategory, SystemNotificationType
from utils.type_vars import P, RT
from .exceptions import QuantitativeServiceLimitError, QuantitativeServiceUnknownError
from ..text import f


async def get_quantitative_services(
        group_id: int,
        type_: QuantitativeServiceTypeEnum,
        quantity: int,
) -> dict[QuantitativeService, int]:
    from db import crud

    result = await crud.get_quantitative_services_to_use(group_id, type_, quantity)

    if result["success"]:
        return result["services_with_usages"]

    if result["covered_quantity"] > quantity:
        raise QuantitativeServiceUnknownError(group_id, type_)

    raise QuantitativeServiceLimitError(
        group_id, type_, quantity, result["covered_quantity"]
    )


class GetQuantityType(Protocol):
    def __call__(self, *args, **kwargs) -> int | Awaitable[int]: ...


async def run_function_with_quantitative_service(
        service_type: QuantitativeServiceTypeEnum,
        group_id: int, quantity: int,
        func: Callable[P, Awaitable[RT]],
        args: P.args = None, kwargs: P.kwargs = None, *,
        is_disabled: bool = False
) -> RT:
    from db import crud

    args = args or tuple()
    kwargs = kwargs or dict()

    if is_disabled or not quantity:
        return await func(*args, **kwargs)

    try:
        services_with_usages = await get_quantitative_services(
            group_id, service_type, quantity
        )
    except QuantitativeServiceLimitError as e:
        group = await Group.get(group_id)

        from core.admin_notification.service import create_system_notification
        await create_system_notification(
            "profile:admin",
            group_id,
            SystemNotificationCategory.BILLING,
            SystemNotificationType.QUOTA_EXCEEDED,
            title=await f(
                f"{service_type.name} service limit error title",
                group.lang,
                group_name=group.name,
                available=e.available,
                quantity=e.quantity,
            ),
            content=await f(
                f"{service_type.name} service limit error content",
                group.lang,
                group_name=group.name,
                available=e.available,
                quantity=e.quantity,
            )
        )
        raise

    try:
        result = await func(*args, **kwargs)
    except Exception:
        from db import sess
        sess().rollback()
        raise
    else:
        await crud.use_quantitative_services(services_with_usages)
        return result


def with_quantitative_service(
        service_type: QuantitativeServiceTypeEnum, *,
        group_place: Literal[
                         "args_id", "args_group", "kwargs_id", "kwargs_group"] | str
                     | None = None,
        quantity_place: Literal["args", "kwargs"] | str | None = None,
        get_quantity: GetQuantityType | None = None,
):
    """
    Function to deal with quantitative service counting.
    Must be no sess().commit() inside, because it will unlock rows

    @param service_type:  QuantitativeServiceTypeEnum
    @param group_place: Where group or group id is located. Default checks all
    locations.
    @param quantity_place: Where the quantity is located. Default checks all locations.
    @param get_quantity: Function to get quantity from args and kwargs.
    @return: Wrapped function result
    """

    def decorator(func: Callable[P, RT]) -> Callable[P, RT]:
        is_method = inspect.ismethod(func)

        async def wrapper(*args: P.args, **kwargs: P.kwargs):
            def get_group_id_from_args(index: int):
                if group_place and group_place not in ("args_id", "args_group"):
                    return None

                if len(args) > index:
                    arg = args[index]
                    if isinstance(arg, Group) and group_place in (None, "args_group"):
                        return arg.id
                    elif isinstance(arg, int) and group_place in (None, "args_id"):
                        return arg
                return None

            def get_quantity_from_args(index: int):
                if quantity_place not in (None, "args"):
                    return None

                if len(args) > index:
                    if isinstance(args[index], int):
                        return args[index]
                return None

            group_id = get_group_id_from_args(int(is_method))
            if not group_id:
                if group_place in (None, "kwargs_group"):
                    group = kwargs.get("group")
                    if group:
                        if isinstance(group, Group):
                            group_id = group.id
                        else:
                            raise ValueError(
                                f"group argument must be a group, not {type(group)}"
                            )

                if not group_id and group_place in (None, "kwargs_id"):
                    group_id = kwargs.get("group_id")
                    if group_id and not isinstance(group_id, int):
                        raise ValueError(
                            f"group_id argument must be an integer, not "
                            f"{type(group_id)}"
                        )

            if not group_id:
                if group_place not in (
                        None, "args_id", "args_group", "kwargs_id", "kwargs_group"):
                    group_or_id = kwargs.get(group_place)
                    if isinstance(group_or_id, Group):
                        group_id = group_or_id.id
                    elif isinstance(group_or_id, int):
                        group_id = group_or_id
                    elif group_or_id:
                        raise ValueError(
                            f"{group_place} must be an integer or group, not "
                            f"{type(group_or_id)}"
                        )

                if not group_id:
                    raise ValueError("group_id or group must be specified")

            if not get_quantity:
                quantity = get_quantity_from_args(int(is_method) + 1)
                if not quantity and group_place in (None, "kwargs"):
                    quantity = kwargs.get("quantity")

                if not quantity:
                    if group_place not in (None, "args", "kwargs"):
                        quantity = kwargs.get(quantity_place)
            else:
                if inspect.iscoroutinefunction(get_quantity):
                    quantity = await get_quantity(*args, **kwargs)
                else:
                    quantity = get_quantity(*args, **kwargs)

            if not quantity:
                raise ValueError(
                    "quantity argument must be specified as second or keyword argument"
                )

            return await run_function_with_quantitative_service(
                service_type, group_id, quantity, func, *args, **kwargs
            )

        return wrapper

    return decorator
