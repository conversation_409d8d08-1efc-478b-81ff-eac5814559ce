from db.models import QuantitativeServiceTypeEnum


class QuantitativeServiceError(Exception):
    def __init__(
            self, group_id: int,
            service_type: QuantitativeServiceTypeEnum,
            msg: str,
    ):
        self.group_id = group_id
        self.service_type = service_type
        super().__init__(msg)


class QuantitativeServiceUnknownError(QuantitativeServiceError):
    def __init__(self, group_id: int, service_type: QuantitativeServiceTypeEnum):
        super().__init__(group_id, service_type, "Unknown quantitative service error")


class QuantitativeServiceLimitError(QuantitativeServiceError):
    def __init__(
            self, group_id: int,
            service_type: QuantitativeServiceTypeEnum,
            quantity: int, available: int,
    ):
        self.quantity = quantity
        self.available = available

        super().__init__(
            group_id, service_type,
            f"Group {group_id} has not enough usages left for service {service_type}\n"
            f"Needed: {quantity}, available: {available}",
        )
