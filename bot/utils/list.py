from psutils.type_vars import T
from typing import Any, Generator


def split_list(lst: list[T], n: int) -> list[list[T]]:
    if len(lst) < n:
        return [lst]
    result = list()
    for i in range(0, len(lst), n):
        result.append(lst[i:i + n])
    return result


def split_list_gen(lst: list[T], n: int) -> Generator[list[T], None, None]:
    if len(lst) < n:
        yield lst
    else:
        for i in range(0, len(lst), n):
            yield lst[i:i + n]


def is_sublist(sub: list, list_: list) -> bool:
    return set(sub) <= set(list_)


def next_list_el(list_: list, el: Any):
    if el not in list_:
        raise ValueError(f"Element {el} is not in list")

    return list_[(list_.index(el) + 1) % len(list_)]
