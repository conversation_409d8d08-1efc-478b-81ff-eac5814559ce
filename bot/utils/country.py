import logging
from typing import Literal

import mpu
import pycountry

from babel.core import get_global
from mpu.units import Currency


def get_country_data_from_code(code: str, return_type: Literal["name", "flag"] = "name") -> str | None:
    if not isinstance(code, str):
        return None

    code = code.strip()
    if not code or len(code) != 2:
        return None

    data = pycountry.countries.get(alpha_2=code)
    if not data:
        return None

    return getattr(data, return_type)


def get_country_name_from_code(code: str) -> str | None:
    return get_country_data_from_code(code, "name")


def get_country_flag_from_code(code: str) -> str | None:
    return get_country_data_from_code(code, "flag")


def get_countries_codes_from_lang(lang: str) -> set[str]:
    territory_langs = get_global("territory_languages")
    data = {code for (code, langs) in territory_langs.items() if langs.get(lang, {}).get("official_status")}

    return data


def get_country_code_from_lang(lang: str) -> str:
    codes = get_countries_codes_from_lang(lang)
    lang = lang.upper()

    if lang == "EN":
        return "GB"

    code = next(iter(codes))
    for item in codes:
        if lang[0] == item[0]:
            code = item
            break

    return code


def get_currency(
        country_or_currency: str,
        return_type: Literal["name", "code"] | None = "code"
) -> (
        Currency | str | None
):
    try:
        country_or_currency = country_or_currency.upper()
        if country_or_currency == "UNITED KINGDOM":
            country_or_currency = "UNITED KINGDOM OF GREAT BRITAIN AND NORTHERN IRELAND (THE)"
        currency = mpu.units.get_currency(country_or_currency)
    except Exception as e:
        logger = logging.getLogger()
        logger.error(e, exc_info=True)
        return None

    if return_type is None:
        return currency
    return getattr(currency, return_type)
