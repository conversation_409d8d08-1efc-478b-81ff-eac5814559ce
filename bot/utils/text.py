from html import unescape

import html
import re
from bs4 import BeautifulSoup, NavigableString
from psutils.text import make_callback_data
from typing import Any, Dict, <PERSON><PERSON>, TypedDict

import config as cfg
from .localisation import localisation, setup_language_localisation


async def on_variable_not_localised(lang: str, *_):
    await setup_language_localisation(lang)


async def f(
        variable: str,
        lang: str | None = None,
        **kwargs,
):
    variable = variable.upper().strip().replace(" ", "_")
    if lang is None:
        lang = cfg.DEFAULT_LANG

    return await localisation.get_text(
        variable, lang,
        on_not_localised=on_variable_not_localised,
        **kwargs,
    )


async def fl(
        variables: dict[str, dict] | list[str],
        lang: str = None
):
    need_translate = {"value": False}

    async def on_not_localised(*_):
        need_translate["value"] = True

    result = await localisation.get_texts_list(
        variables, lang, on_not_localised=on_not_localised
    )

    if need_translate["value"]:
        await setup_language_localisation(lang)

    return result


class Variable(TypedDict, total=True):
    variable: str


class VariableWithTextKwargs(Variable, total=False):
    text_kwargs: dict[str, Any]


async def fd(
        # variables: dict[str, str | Variable | VariableWithTextKwargs],
        variables: dict[str, str | dict[str, str | dict[str, Any]]],
        lang: str,
) -> dict[str, str]:
    variables_indexes = {}

    i = 0
    variables_to_localise: dict[str, dict] = {}
    for variable_data in variables.values():
        if not isinstance(variable_data, dict):
            variable = variable_data
            text_kwargs = {}
        else:
            variable = variable_data["variable"]
            text_kwargs = variable_data.get("text_kwargs", {})

        if variable in variables_to_localise:
            continue

        variables_to_localise[variable] = text_kwargs
        variables_indexes[variable] = i
        i += 1

    texts = await fl(variables_to_localise, lang)

    data = {}
    for key, variable_data in variables.items():
        try:
            variable = variable_data["variable"] if isinstance(
                variable_data, dict
            ) else variable_data
            data[key] = texts[variables_indexes[variable]]
        except KeyError:
            data[key] = key

    return data


async def empty_value(lang: str | None = None) -> str:
    return await f("empty value", lang)


def str_to_recognized_type(string: str) -> Any:
    try:
        if string.isdecimal() or string.startswith("-") and string[1:].isdecimal():
            string = int(string)
        elif re.fullmatch(r"-?\d+\.\d+", string) is not None:
            string = float(string)
        elif string == "True":
            string = True
        elif string == "False":
            string = False
        elif string == "None":
            string = None
    except:
        pass
    return string


async def value_or_empty_text(value: Any, lang: str | None = None) -> str:
    return value if value else await empty_value(lang)


async def not_field(lang: str = cfg.DEFAULT_LANG) -> str:
    return await f("not field", lang)


def replace_html_symbols(text: str) -> str:
    tags_texts_re = "|".join([f"{tag}" for tag in cfg.SUPPORTED_HTML_TAGS])
    a_href_pattern = r"<a href=\".+\">"
    icon_pattern = r"<i class=\".+\">"
    usual_tags_pattern = rf"</?({tags_texts_re})>"
    texts_for_replace = re.split(
        rf"{a_href_pattern}|{usual_tags_pattern}|{icon_pattern}", text
    )
    for old_text in texts_for_replace:
        if not old_text:
            continue
        new_text = html.escape(old_text, quote=False)
        text = text.replace(old_text, new_text)

    # try find icon, if it exist make html escape for it
    if bool(re.search(icon_pattern, text)):
        icon = re.findall(r'<code>(.+?)</code>', text)
        if icon:
            text = text.replace(icon[0], html.escape(icon[0], quote=False))
    return text


c = make_callback_data


def parse_callback_data(callback_data_str: str) -> Tuple[str, Dict[str, Any]]:
    split_data = callback_data_str.split(":", maxsplit=1)
    mode = split_data[0]
    data = {}

    if len(split_data) > 1:
        for arg in split_data[1].split("&"):
            try:
                key, value = arg.split("=")
            except:
                continue
            value = str_to_recognized_type(value)
            data.update(**dict([(key, value)]))
    return mode, data


def get_callback_mode(callback_data_str: str) -> str:
    return callback_data_str.split(":")[-1]


def get_callback_data(callback_data_str: str) -> Dict[str, Any]:
    mode, callback_data = parse_callback_data(callback_data_str)
    return callback_data


def make_ending(number: int, lang: str) -> str:
    if lang == "ru":
        if 9 < number < 20:
            ending = "ов"
        else:
            last = number % 10
            if last == 1:
                ending = ""
            elif 1 < last < 5:
                ending = "а"
            elif last == 0 or 4 < last < 10:
                ending = "ов"
            else:
                ending = last
    else:
        ending = lang
    return ending


async def enter_text_with_specified_value(
        enter_text: str, current_value: str, lang: str
) -> str:
    current_value_text = await f("current value text", lang, value=current_value)
    return f"{enter_text}\n\n\n{current_value_text}"


def html_to_markdown(text: str):
    # Видаляємо пробіли перед закритими HTML тегами
    text = re.sub(r"(\s+)(</b>|</strong>|</i>|</u>|</strike>|</s>)", r"\2", text)

    # Перетворюємо HTML теги в Markdown
    text = re.sub(r"</?b>", "*", text)
    text = re.sub(r"</?strong>", "*", text)
    text = re.sub(r"</?i>", "_", text)
    text = re.sub(r"</?u>", "", text)
    text = re.sub(r"</?strike>", "~", text)
    text = re.sub(r"</?s>", "~", text)
    text = re.sub(r"</?code>", "`", text)

    # Обробка посилань
    text = re.sub(r"<a href=\"(.*?)\">(.*?)</a>", r"[\2](\1)", text)

    # Обробка абзаців та розривів рядків
    text = re.sub(r"<p>", "", text)
    text = re.sub(r"</p>", "\n\n", text)  # Додано подвійний перенос рядка для абзаців
    text = re.sub(r"</?br(\s+)?/?>", "\n", text)

    # Видаляємо залишені HTML теги, зберігаючи їх вміст
    text = re.sub(r"<[^>]+>", "", text)

    # Розкодовуємо HTML символи
    text = unescape(text)

    return text.strip()  # Видаляємо зайві пробіли на початку та в кінці


def convert_html_to_supported_html(html_string: str, bot_type: str) -> str:
    html_string = re.sub(r"<[^<>]*br[^<>]*>", "<br>", html_string)
    supported_tags = {
        'telegram': ['b', 'strong', 'i', 'em', 'u', 's', 'strike', 'del', 'a', 'code',
                     'pre'],
        'whatsapp': ['b', 'i', 's']
    }

    soup = BeautifulSoup(html_string, 'html.parser')

    def filter_tags(el):
        if el.name in ['br', 'p', 'div']:
            if el.parent:
                el.insert_before(NavigableString('\n'))
                el.unwrap()
        elif re.fullmatch(r"h\d+", el.name):
            if el.parent:
                el.insert_before(NavigableString('\n'))
                old_el = el
                el = soup.new_tag("b")
                old_el.replace_with(el)

                for child in reversed(old_el.contents[:]):
                    el.insert(0, child)

        elif el.name not in supported_tags[bot_type]:
            if el.parent:
                el.unwrap()
        else:
            for child in el.find_all():
                filter_tags(child)

    for element in soup.find_all():
        filter_tags(element)

    return str(soup).strip()


__all__ = [
    "f", "fl", "fd", "c",
    "Variable",
    "VariableWithTextKwargs",
    "empty_value",
    "value_or_empty_text",
    "replace_html_symbols",
    "parse_callback_data",
    "get_callback_mode",
    "get_callback_data",
    "make_ending", "not_field",
    "enter_text_with_specified_value",
    "html_to_markdown",
    "convert_html_to_supported_html",
]
