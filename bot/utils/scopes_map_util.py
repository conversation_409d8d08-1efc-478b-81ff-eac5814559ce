from collections import defaultdict
from typing import Iterable, Literal, Type, Union

from psutils.text import snake_case_to_paschal_case
from pydantic import BaseModel, create_model

from schemas import BaseORMModel


class BaseScopeOperators:
    """
    Base object for scopes.
    """

    def __add__(self, other: "BaseScopeOperators"):
        """
        Implements __add__ operation to mix few scopes or scope batches into one
        scope batch
        """
        added_scopes = []

        for scope in (self, other):
            if isinstance(scope, Scope):
                added_scopes.append(scope)
            elif isinstance(scope, ScopeBatch):
                added_scopes.extend(scope.scopes)
            else:
                raise ValueError(
                    f"each scopes obj have to be either <PERSON>ope or <PERSON>opeBatch, "
                    f"not {scope}"
                )

        return ScopeBatch(*added_scopes)


class Scope(BaseScopeOperators):
    """
    A Scope object is used to define scope and required fields for it
    """

    def __init__(
            self,
            scope: str,
            *required_fields: str,
            tags: str | Iterable[str] | None = None,
            group: str | None = None,
            basic_prefixes: tuple[str, ...] | None = None,
            target: str | None = None,
    ):
        """
        @param scope: name of scope in the database. Scope has to contain ':'
        @param required_fields: fields that are required for checking this scope
        @param tags: tags for picking scopes from batch. Tag cannot contain ':'
        """
        if ":" not in scope:
            raise ValueError("Invalid scope. Scope have to contain ':'")

        self.scope = scope
        self.required_fields = set(required_fields)

        if isinstance(tags, str):
            self.tags = {tags}
        elif isinstance(tags, Iterable):
            if any(map(lambda x: ":" in x, tags)):
                raise ValueError("Invalid tags. Tag cannot contain ':'")
            self.tags = set(tags)
        else:
            self.tags = set()

        self._group = group
        self.target = target

        self._basic_prefixes = basic_prefixes

        self._model: Type[BaseModel] | None = None

    def set_basic_prefixes(self, basic_prefixes: tuple[str, ...]):
        self._basic_prefixes = basic_prefixes

    @property
    def is_basic(self):
        return bool(self._basic_prefixes and self.prefix in self._basic_prefixes)

    @property
    def prefix(self):
        return self.scope.split(":")[0]

    @property
    def suffix(self):
        return self.scope.split(":")[1]

    @property
    def group(self):
        return self._group or self.prefix

    def set_group(self, value: str):
        self._group = value

    def set_target(self, value: str):
        self.target = value

    def __eq__(self, other: "BaseScopeOperators"):
        """
        Compare a Scope object to BaseScopeOperators
        Always False for not Scope objects.
        For Scope objects compares if scope and required_fields are the same.
        Tags are ignored!
        @param other: Object to compare
        """
        if not isinstance(other, Scope):
            return False

        return (self.scope, self.required_fields) == (
            other.scope, other.required_fields)

    def __hash__(self):
        """Hashes Scope object. Tags are ignored!"""
        return hash((self.scope, tuple(sorted(self.required_fields))))

    def omit_fields(self, *omit_fields: str):
        """
        Method to create a new Scope object with fields omitted.
        If no omit_fields are provided, the current object will be returned
        @param omit_fields: Fields to omit.
        @return: A new Scope object without omitted fields.
        """
        if not omit_fields:
            return self

        return Scope(
            self.scope,
            *[field for field in self.required_fields if field not in omit_fields],
            tags=self.tags
        )

    def dict(self):
        return {
            "scope": self.scope,
            "required_fields": list(sorted(self.required_fields)),
        }

    # noinspection PyTypeHints
    def get_model(self):
        if self._model:
            return self._model

        scope_name = snake_case_to_paschal_case(self.scope.replace(":", "_"))
        self._model = create_model(
            f"{scope_name}Schema",
            __base__=BaseORMModel,
            scope=(Literal[self.scope], ...),
            is_basic=(bool, ...),
            required_fields=(set[str], ...),
            tags=(set[str], ...),
            prefix=(Literal[self.prefix], ...),
            suffix=(Literal[self.suffix], ...),
            group=(Literal[self.group], ...),
        )
        return self._model

    def __repr__(self):
        return f"<Scope: {self.scope}, {self.required_fields = }, {self.tags}>"


class ScopeBatch(BaseScopeOperators):
    """
    A ScopeBatch object is used to indicate that multiple Scopes are associated with
    a single API scope.
    """

    def __init__(
            self,
            *scopes: "Scope | ScopeBatch",
            name: str | None = None,
            actions: Iterable[str] | None = None,
            set_group_to_scopes: str | None = None,
            target: str | None = None,
    ):
        """
        @param scopes: Scope and ScopeBatch to be included in ScopeBatch
        """
        self.name: str | None = name
        self.scopes: list[Scope] = []
        for scope in scopes:
            if isinstance(scope, Scope):
                self.scopes.append(scope)
            elif isinstance(scope, ScopeBatch):
                self.scopes.extend(scope.scopes)
            else:
                raise ValueError(
                    f"each scopes obj have to be either Scope or ScopeBatch, "
                    f"not {scope}"
                )

        if set_group_to_scopes:
            [scope.set_group(set_group_to_scopes) for scope in self.scopes if scope]
        if target:
            [scope.set_target(target) for scope in self.scopes if scope]

        self._actions: set[str] = set(actions) if actions else set()
        self.target: str | None = target

    @property
    def scopes_names(self):
        return [scope.scope for scope in self.scopes]

    def __sub__(self, other: "Scope | ScopeBatch | Iterable[Scope | ScopeBatch]"):
        """
        Method to subtract scopes from existing batch.
        If other is Scope, it will be subtracted.
        If other is ScopeBatch, its scopes will be subtracted.
        If other is Iterable, two above rules will be applied to each element
        @param other:
        @return: A new ScopeBatch object with subtracted scopes.
        """
        if isinstance(other, Scope | ScopeBatch):
            to_remove = (other,)
        elif isinstance(other, Iterable):
            to_remove = other
        else:
            raise ValueError(
                f"other has to be either Scope, ScopeBatch or Iterable[Scope | "
                f"ScopeBatch], not {other}"
            )

        to_remove_set = set()
        for scope_or_batch in to_remove:
            if isinstance(scope_or_batch, Scope):
                to_remove_set.add(scope_or_batch)
            elif isinstance(scope_or_batch, ScopeBatch):
                to_remove_set.update(scope_or_batch.scopes)
            else:
                raise ValueError(
                    f"each scopes obj have to be either Scope or ScopeBatch, "
                    f"not {other}"
                )

        return ScopeBatch(
            *[scope for scope in self.scopes if scope not in to_remove_set]
        )

    def pick_scope(self, scope: str, no_error: bool = False):
        """
        Method to get scope by Scope.scope
        @param scope: Scope.scope
        @param no_error: if scope not found, and no_error specified None will be
        returned else error will be raised
        @return: Scope object or None
        """

        for current_scope in self.scopes:
            if current_scope.scope == scope:
                return current_scope

        if no_error:
            return None

        raise ValueError(f"There is not scope {scope} in this Batch")

    def pick_scopes(
            self, *scopes_or_tags: str, omit_fields: str | tuple[str, ...] | None = None
    ):
        """
        Method to pick scopes from existing batch.
        Creates a new ScopeBatch with scopes from currency ScopeBatch, filtered by
        Scope.scope is in scopes or at least one of Scope.tags is in scopes
        @param scopes_or_tags: List of scopes or tags.
         Scope has to contain ':'
         Tag cannot contain ':'
        @param omit_fields: fields to omit in scopes
        @return: A new ScopeBatch object
        """
        if isinstance(omit_fields, str):
            omit_fields = (omit_fields,)
        elif not omit_fields:
            omit_fields = ()
        elif not isinstance(omit_fields, tuple):
            raise ValueError(f"omit_fields have to be a tuple, not {omit_fields}")

        return ScopeBatch(
            *[
                scope.omit_fields(*omit_fields) for scope in self.scopes
                if "*" in scopes_or_tags or (
                        scope.scope in scopes_or_tags or
                        any(map(lambda x: x in scopes_or_tags, scope.tags))
                )
            ]
        )

    def __eq__(self, other: "BaseScopeOperators"):
        """
        Compare a ScopeBatch object to BaseScopeOperators
        Always False for not ScopeBatch objects.
        For ScopeBatch objects compares if scopes lists are the same.
        @param other: Object to compare
        """
        if not isinstance(other, ScopeBatch):
            return False

        return set(self.scopes) == set(other.scopes)

    @classmethod
    def build_for_object(
            cls,
            object_name: str,
            *include_scopes: "Scope | ScopeBatch",
            required_fields: str | Iterable[str] | None = None,
            hierarchy: tuple[str, ...] = ("edit", "read"),
            with_create: bool = False,
            with_contribute: bool = False,
            with_plural: bool = True,
            create_excluded: Iterable[str] | None = ("read",),
            include_object_id_required_field: bool = True,
            **kwargs,
    ):
        if required_fields is None:
            required_fields = ()
        elif isinstance(required_fields, str):
            required_fields = (required_fields,)
        if create_excluded is None:
            create_excluded = ()

        plural_scopes = []
        entity_scopes = []

        for i, scope in enumerate(hierarchy):
            tags = list(hierarchy[i:])

            if with_plural:
                plural_tags = [*tags]
                if with_contribute and scope == "edit":
                    plural_tags.append("contribute")
                if with_create and scope not in create_excluded:
                    plural_tags.append("create")

                plural_scopes.append(
                    Scope(f"{object_name}s:{scope}", *required_fields, tags=plural_tags)
                )

            current_required_fields = [*required_fields]
            if include_object_id_required_field:
                current_required_fields.append(f"{object_name}_id")

            entity_scopes.append(
                Scope(f"{object_name}:{scope}", *current_required_fields, tags=tags)
            )

        actions = [*hierarchy]

        if with_contribute:
            plural_scopes.append(
                Scope(
                    f"{object_name}s:contribute", *required_fields,
                    tags=["contribute", "read", "create"]
                )
            )
            actions.append("contribute")

        if with_create:
            plural_scopes.append(
                Scope(f"{object_name}s:create", *required_fields, tags=["create"])
            )
            actions.append("create")

        return cls(
            *include_scopes,
            ScopeBatch(*plural_scopes, *entity_scopes, set_group_to_scopes=object_name),
            name=object_name,
            actions=actions,
            **kwargs,
        )

    def get_actions(self, *tags: str, prefix: str):
        return {f"{prefix}:{tag}": self.pick_scopes(tag) for tag in tags}

    @property
    def actions(self):
        return self.get_actions(*self._actions, prefix=self.name)


class ScopeMap:
    """
    A ScopeMap object is used to define a scope map.
    """

    def __init__(
            self,
            *scopes_args: ScopeBatch | dict[str, Scope | ScopeBatch],
            basic_prefixes: tuple[str, ...] | None = None
    ):
        """
        @param scopes_args: ScopeBatch or dict, where
        the key is action (or tuple of actions), used in API, and
        the value is Scope or ScopeBatch
        """

        self.available_scopes: dict[str, Scope] = {}

        def add_scope_to_available(el: Scope | ScopeBatch):
            if isinstance(el, Scope):
                el.set_basic_prefixes(basic_prefixes)
                if el.scope not in self.available_scopes:
                    self.available_scopes[el.scope] = el
            else:
                [add_scope_to_available(el) for el in el.scopes]

        scopes = {}
        for obj in scopes_args:
            actions = obj if isinstance(obj, dict) else obj.actions
            for action, scope_or_batch in actions.items():
                if action not in scopes:
                    scopes[action] = scope_or_batch

            if isinstance(obj, ScopeBatch):
                add_scope_to_available(obj)
            else:
                [add_scope_to_available(el) for el in obj.values()]

        self.map: dict[str, Scope | ScopeBatch] = {}

        for key, scope_or_batch in scopes.items():
            self.map[key] = scope_or_batch

        # key is scope, value is a list of actions it grants access
        self.actions_by_scope: dict[str, list[str]] = defaultdict(list)

        for action, scope_or_batch in self.map.items():
            scopes = scope_or_batch.scopes if isinstance(
                scope_or_batch, ScopeBatch
            ) else [scope_or_batch]
            for scope in scopes:
                self.actions_by_scope[scope.scope].append(action)

        self._by_groups_model: Type[BaseModel] | None = None
        self._all_actions_literal: Type[Literal] | None = None
        self._all_scopes_literal: Type[Literal] | None = None

    def iter_action_scopes(self, action: str, available_fields: str | Iterable[str]):
        """
        Method to iter over scopes available for scope_name and available_fields
        @param action: name of API action
        @param available_fields: fields, that are available in this API call
        @return:
        """
        if isinstance(available_fields, str):
            available_fields = (available_fields,)

        scope_or_batch = self.map.get(action)
        if not scope_or_batch:
            raise ValueError(f"Unknown action {action}")

        if isinstance(scope_or_batch, Scope):
            yield scope_or_batch
        else:
            for scope in scope_or_batch.scopes:
                if "*" in available_fields or all(
                        map(lambda x: x in available_fields, scope.required_fields)
                ):
                    yield scope

    def minified_action_scopes(
            self, action: str,
            available_fields: Iterable[str],
    ) -> list[tuple[tuple[str, ...], tuple[str, ...]]]:
        """
        Create a minified scopes list for optimised db queries
        @param action: name of API action
        @param available_fields: fields, that are available in this API call
        @return: List of tuples where the first element is scopes set, and the second
        element is required_fields set
        """
        scope_by_required_fields: dict[tuple[str, ...], set[str]] = defaultdict(set)

        for scope in self.iter_action_scopes(action, available_fields):
            scope_by_required_fields[tuple(sorted(scope.required_fields))].add(
                scope.scope
            )

        return [(tuple(scopes), required_fields) for required_fields, scopes in
                scope_by_required_fields.items()]

    def get_scope(self, scope_name: str, no_error: bool = False):
        if not no_error and scope_name not in self.available_scopes:
            raise ValueError(f"Unknown scope {scope_name}")
        return self.available_scopes.get(scope_name)

    @property
    def available_actions(self):
        return tuple(self.map.keys())

    def by_groups(
            self,
            exclude: Iterable[str] | None = None,
            target: str | None = None
    ):
        result: dict[str, list[Scope]] = defaultdict(list)
        for scope in self.available_scopes.values():
            if exclude and scope.scope in exclude:
                continue
            if target and scope.target != target:
                continue
            result[scope.group].append(scope)
        return result

    def get_all_scopes_models(self):
        models = [scope.get_model() for scope in self.available_scopes.values()]
        return {
            model.__name__: model
            for model in models
        }

    def by_groups_model(
            self,
            exclude: Iterable[str] | None = None,
            target: str | None = None
    ):
        if self._by_groups_model:
            return self._by_groups_model

        self._by_groups_model = create_model(
            "ScopesMapSchema",
            **{
                group: (list[Union[tuple(scope.get_model() for scope in scopes)]], ...)
                for group, scopes in self.by_groups(exclude, target).items()
            }
        )
        return self._by_groups_model

    # noinspection PyTypeHints
    def get_all_actions_literal(self):
        if not self._all_actions_literal:
            self._all_actions_literal = Literal[self.available_actions]
        return self._all_actions_literal

    # noinspection PyTypeHints
    def get_all_scopes_literal(self):
        if not self._all_scopes_literal:
            self._all_scopes_literal = Literal[tuple(self.available_scopes.keys())]
        return self._all_scopes_literal
