import re
from collections import defaultdict
from typing import Type, TypeVar

from fastapi import Depends, UploadFile
from pydantic import BaseModel
import starlette
from starlette.requests import Request


async def request_form_to_dict(request: Request, stringify_file: bool = False):
    request_form = {}
    try:
        form_data = await request.form()
    except:
        return None

    for key, value in form_data.multi_items():
        if isinstance(value, UploadFile | starlette.datastructures.UploadFile) and stringify_file:
            value = f"FILE: {value.filename}"
        if key in request_form:
            if isinstance(request_form[key], list):
                request_form[key].append(value)
            else:
                request_form[key] = [request_form[key], value]
        elif key.endswith("[]"):
            request_form[key] = [value]
        else:
            request_form[key] = value

    obj_keys: dict[str, list[str]] = defaultdict(list)
    obj_list_keys: dict[str, list[str]] = defaultdict(list)

    for key in request_form.keys():
        if re.fullmatch(r"[a-zA-Z0-9_-]+\[[a-zA-Z0-9_-]+](?:\[])?", key):
            if key.endswith("[]"):
                add_to = obj_list_keys
                key = key[:-2]
            else:
                add_to = obj_keys
            obj_name, field_name = key[:-1].split("[", 1)
            add_to[obj_name].append(field_name)

    for obj, keys in obj_keys.items():
        request_form[obj] = {
            key: request_form.pop(f"{obj}[{key}]")
            for key in keys
        }

    for obj, keys in obj_list_keys.items():
        request_form[obj] = []

        for key in keys:
            for i, value in enumerate(request_form.pop(f"{obj}[{key}][]")):
                if i >= len(request_form[obj]):
                    request_form[obj].append({})
                request_form[obj][i][key] = value
    return request_form


ModelT = TypeVar("ModelT", bound=BaseModel)


def pydantic_form_data_depend(model: Type[ModelT]):
    async def depend_func(request: Request, _: model = Depends()) -> ModelT:
        form_data = await request_form_to_dict(request)
        return model(**form_data)

    return Depends(depend_func)
