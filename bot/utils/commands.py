import logging

from psutils.decorators import sync_to_async

from config import ISSUE_CERTIFICATE_COMMAND

import shlex
import subprocess


@sync_to_async
def issue_ssl_certificate(domain: str):
    debugger = logging.getLogger("debugger.issue_ssl_certificate")

    debugger.debug("issuing SSL certificate for domain %s", domain)

    command = ISSUE_CERTIFICATE_COMMAND.format(domain=domain)
    process = subprocess.Popen(shlex.split(command), stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    out, err = process.communicate()

    if out:
        out = out.decode("utf-8")
    if err:
        err = err.decode("utf-8")

    debugger.debug(
        f"Issue SSL certificate for domain {domain} result\n"
        f"Output: {out}\n"
        f"Error: {err}\n"
    )
