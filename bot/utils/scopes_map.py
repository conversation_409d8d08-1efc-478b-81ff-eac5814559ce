from utils.scopes_map_util import <PERSON><PERSON>, <PERSON><PERSON><PERSON>atch, ScopeMap

platform_scope = ScopeBatch(
    Scope(
        "platform:superadmin",
        tags=["superadmin", "admin", "edit", "contribute", "read", "create"]
    ),
    <PERSON>ope("platform:admin", tags=["admin", "edit", "contribute", "read", "create"]),
    name="platform",
    actions=["superadmin", "admin", "edit", "read", "create"],
    target="user",
)

profile = ScopeBatch(
    platform_scope,
    Scope(
        "profile:admin", "profile_id",
        tags=["admin", "edit", "contribute", "read", "create"]
    ),
    <PERSON><PERSON>("profile:edit", "profile_id", tags=["edit", "contribute", "read", "create"]),
    <PERSON><PERSON>("profile:read", "profile_id", tags=["read"]),
    <PERSON><PERSON>("profile:contribute", "profile_id", tags=["contribute", "read", "create"]),
    <PERSON><PERSON>("profile:create", "profile_id", tags=["create"]),
    name="profile",
    actions=["admin", "edit", "read", "create"],
    target="user",
)

notifications = ScopeBatch(
    profile,
    Scope("notifications:create", "profile_id", tags=["create"]),
    name="notifications",
    actions=["create"],
    target="user",
)

profile_data = ScopeBatch(
    profile,
    Scope("profile_data:edit", "profile_id", tags=["edit", "read"]),
    Scope("profile_data:read", "profile_id", tags=["read"]),
    name="profile_data",
    actions=["edit", "read"],
    target="user",
)

bot = ScopeBatch(
    profile,
    name="bot",
    actions=["read", "edit"],
    target="user",
)

crm = ScopeBatch(
    profile.pick_scopes("admin"),
    ScopeBatch(
        Scope("crm:edit", "profile_id", tags=["edit", "read"]),
        Scope("crm:read", "profile_id", tags=["read"]),
        set_group_to_scopes="crm",
    ),
    name="crm_all",
    actions=["edit", "read"],
    target="user",
)

crm_store = ScopeBatch(
    crm,
    ScopeBatch(
        Scope("crm_store:edit", "profile_id", "store_id", tags=["edit", "read"]),
        Scope("crm_store:read", "profile_id", "store_id", tags=["read"]),
        set_group_to_scopes="crm",
    ),
    name="crm_store",
    actions=["edit", "read"],
    target="user",
)

crm_order = ScopeBatch(
    crm,
    crm_store,
    name="crm_order",
    actions=["edit", "read"],
    target="user",
)

crm_invoice = ScopeBatch(
    crm,
    name="crm_invoice",
    actions=["edit", "read"],
    target="user",
)

crm_ticket = ScopeBatch(
    crm,
    name="crm_ticket",
    actions=["edit", "read"],
    target="user",
)

crm_review = ScopeBatch(
    crm,
    name="crm_review",
    actions=["edit", "read"],
    target="user",
)

crm_chat = ScopeBatch(
    crm,
    name="crm_chat",
    actions=["edit", "read"],
    target="user",
)

crm_text_notification = ScopeBatch(
    crm,
    name="crm_text_notification",
    actions=["edit", "read"],
    target="user",
)

crm_ewallet_ext_payment = ScopeBatch(
    crm,
    ScopeBatch(
        Scope(
            "crm_ewallet_ext_payment:edit",
            "profile_id",
            tags=["edit", "read"],
        ),
        Scope(
            "crm_ewallet_ext_payment:read",
            "profile_id",
            tags=["read"],
        ),
        set_group_to_scopes="crm",
    ),
    name="crm_ewallet_ext_payment",
    actions=["edit", "read"],
    target="user",
)

crm_user = ScopeBatch(
    crm,
    name="crm_user",
    actions=["edit", "read"],
    target="user",
)

storage = ScopeBatch(
    profile,
    Scope("storage:edit", "profile_id", tags=["edit", "read"]),
    Scope("storage:read", "profile_id", tags=["read"]),
    name="storage",
    actions=["edit", "read"],
    target="user",
)

store = ScopeBatch.build_for_object(
    "store",
    profile,
    required_fields="profile_id",
    with_create=True,
    with_contribute=True,
    target="user",
)

menu = ScopeBatch(
    profile,
    Scope(
        "menu:edit", "profile_id",
        tags=["edit", "contribute", "read", "create"]
    ),
    Scope("menu:read", "profile_id", tags=["read"]),
    Scope("menu:contribute", "profile_id", tags=["contribute", "read", "create"]),
    Scope("menu:create", "profile_id", tags=["create"]),
    name="menu",
    actions=["edit", "read", "create"],
    target="user",
)

vm = ScopeBatch.build_for_object(
    "vm",
    profile,
    required_fields="profile_id",
    with_create=True,
    with_contribute=True,
    target="user",
)

webhook = ScopeBatch.build_for_object(
    "webhook",
    profile,
    required_fields="profile_id",
    with_create=True,
    with_contribute=True,
    target="user",
)

ewallet_external_payment = ScopeBatch(
    Scope("ewallet_ext_payment:edit", "profile_id", tags=["edit"]),
    name="ewallet_external_payment",
    actions=["edit"],
    target="user",
)

menu_batches = {
    object_name: ScopeBatch(
        menu,
        ScopeBatch(
            Scope(
                f"{object_name}:edit", "profile_id", f"{object_name}_id",
                tags=["edit", "read"]
            ),
            Scope(
                f"{object_name}:read", "profile_id", f"{object_name}_id", tags=["read"]
            ),
            set_group_to_scopes="menu",
        ),
        name=object_name,
        actions=["edit", "read", "create"],
        target="user",
    )
    for object_name in (
        "category",
        "product",
        "product_group",
        "attribute_group",
        "characteristic",
        "attribute",
        "qr_menu",
        "qr_object",
        "extra_fee",
        "task",
    )
}

profile_media = ScopeBatch(
    vm,
    name="profile_media",
    actions=["edit", "create", "read"],
    target="user",
)

# scope for target "profile", allows seeing customer owned profiles in CRM
customer_profiles = ScopeBatch(
    Scope("customer_profiles:read", tags=["read"]),
    name="customer_profiles",
    actions=["read"],
    target="profile",
)

billing = ScopeBatch(
    Scope("billing:tester", tags=["tester"]),
    name="billing",
    actions=["tester"],
    target="profile",
)

scope_map = ScopeMap(
    platform_scope,
    profile,
    notifications,
    profile_data,
    bot,
    crm,
    crm_store,
    crm_order,
    crm_invoice,
    crm_ticket,
    crm_review,
    crm_chat,
    crm_text_notification,
    crm_user,
    crm_ewallet_ext_payment,
    store,
    storage,
    vm,
    webhook,
    ewallet_external_payment,
    *menu_batches.values(),
    profile_media,
    customer_profiles,
    billing,
    basic_prefixes=("profile",),
)

ACTION_NONE = "action:none"
