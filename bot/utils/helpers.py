import os.path
import sys
from collections.abc import Iterable
from typing import Sequence
from urllib.parse import urlparse, urlunparse

from utils.type_vars import T


def get_running_file_name():
    return os.path.split(sys.argv[0])[-1]


def is_objects_keys_equal(a, b, keys: Iterable["str"]):
    def get_key(x, k):
        if hasattr(x, k):
            return getattr(x, k)
        return x[k]

    return not any(
        map(lambda key: get_key(a, key) != get_key(b, key), keys)
    )


def get_duplicated_values(values: Sequence[T]) -> set[T]:
    return {
        el for el in values if
        values.count(el) > 1
    }


def normalize_url(input_url: str):
    if not input_url.startswith(('http://', 'https://')):
        input_url = 'https://' + input_url

    parsed_url = urlparse(input_url)

    normalized_url = urlunparse((parsed_url.scheme, parsed_url.netloc, '', '', '', ''))

    if not normalized_url.endswith('/'):
        normalized_url += '/'

    return normalized_url.lower()
