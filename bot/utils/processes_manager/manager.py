import asyncio
import logging
import subprocess
from dataclasses import dataclass
from typing import Literal, Callable, Any

from utils.processes_manager.background_worker.base import BaseBackgroundWorker


@dataclass
class Process:
    name: str
    command: str
    args: tuple[str, ...]


logger = logging.getLogger(__name__)


ProcessEl = tuple[str, tuple[str, ...] | str] | tuple[str, tuple[str, ...] | str, bool]


class ProcessesManager:
    def __init__(
            self,
            processes: list[ProcessEl] | None = None,
            workers: list[BaseBackgroundWorker | type[BaseBackgroundWorker]] | None = None, *,
            timeout_between_processes: int | float | None = None,
            timeout_between_workers: int | float | None = None,
            start_callback: Callable[[str, Literal["already_started", "error", True]], Any] | None = None,
            stop_callback: Callable[[str, Literal["not_started", "error", True]], Any] | None = None,
    ):
        self.__processes: list[Process] = []
        if processes:
            for process in processes:
                if len(process) == 3 and not process[2]:
                    continue

                self.__processes.append(Process(
                    process[0],
                    process[1][0] if isinstance(process[1], tuple) else process[1],
                    process[1][1:] if isinstance(process[1], tuple) else tuple()
                ))

        self.__started_processes: dict[str, subprocess.Popen] = {}

        self.__workers: dict[str, BaseBackgroundWorker] = {}
        if workers:
            for worker in workers:
                if isinstance(worker, type):
                    worker = worker()
                self.__workers[worker.name] = worker

        self.timeout_between_processes = timeout_between_processes
        self.timeout_between_workers = timeout_between_workers

        self.start_callback = self._wrap_async(start_callback) if start_callback else None
        self.stop_callback = self._wrap_async(stop_callback) if stop_callback else None

    @staticmethod
    def _wrap_async(func: Callable):
        if asyncio.iscoroutinefunction(func):
            return func

        async def wrap(*args, **kwargs):
            return func(*args, **kwargs)

        return wrap

    async def start_process(self, process: Process | str):
        result: Literal["already_started", "error", True]

        if not isinstance(process, Process):
            processes = list(filter(lambda x: x.name == process, self.__processes))
            if not processes:
                raise ValueError(f"Process {process} not found")

            process = processes[0]

        if isinstance(self.__started_processes.get(process.name), subprocess.Popen):
            result = "already_started"
        else:
            try:
                self.__started_processes[process.name] = subprocess.Popen([process.command, *process.args])
            except Exception as e:
                logger.error(e, exc_info=True)
                result = "error"
            else:
                result = True

        if self.start_callback:
            await self.start_callback(process.name, result)

        return result

    async def stop_process(self, name: str):
        result: Literal["not_started", "error", True]

        if not isinstance(self.__started_processes.get(name), subprocess.Popen):
            result = "not_started"
        else:
            try:
                self.__started_processes[name].kill()
                del self.__started_processes[name]
            except Exception as e:
                logger.error(e, exc_info=True)
                result = "error"
            else:
                result = True

        if self.stop_callback:
            await self.stop_callback(name, result)

        return result

    async def restart_process(self, process: Process):
        await self.stop_process(process.name)
        await self.start_process(process)

    async def start_processes(self, *names: str):
        if names:
            processes_to_start = list(filter(lambda x: x.name in names, self.__processes))
        else:
            processes_to_start = self.__processes

        for process in processes_to_start:
            await self.start_process(process)
            if self.timeout_between_processes:
                await asyncio.sleep(self.timeout_between_processes)

    async def stop_processes(self, *names: str):
        if not names:
            names = list(map(lambda x: x.name, self.__processes))

        for name in names:
            await self.stop_process(name)

    async def restart_processes(self, *names: str):
        await self.stop_processes(*names)
        await self.start_processes(*names)

    def start_worker(self, name: str):
        if not self.__workers[name].is_stopped:
            asyncio.create_task(self.__workers[name].start())

    async def stop_worker(self, name: str):
        await self.__workers[name].stop()

    def restart_worker(self, name: str):
        task = asyncio.create_task(self.__workers[name].stop())

        def callback():
            self.start_worker(name)

        task.add_done_callback(callback)

    async def start_workers(self, *names: str):
        if not names:
            names = [name for name in self.__workers]

        for name in names:
            self.start_worker(name)
            if self.timeout_between_workers:
                await asyncio.sleep(self.timeout_between_workers)

    async def stop_workers(self, *names: str):
        if not names:
            names = [name for name in self.__workers]

        for name in names:
            await self.stop_worker(name)

    async def restart_workers(self, *names: str):
        await self.stop_workers(*names)
        await self.start_workers(*names)
