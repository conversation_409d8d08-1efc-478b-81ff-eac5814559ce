import abc
import logging


class BaseBackgroundWorker(abc.ABC):
    DEFAULT_NAME: str | None = None

    def __init__(self, name: str | None = None):
        self.__name = name or self.DEFAULT_NAME
        self.__stop: bool = False
        self.error_logger = logging.getLogger(f"error.{self.__name}.worker")
        self.debugger_logger = logging.getLogger(f"debugger.{self.__name}.worker")

    @property
    def is_stopped(self):
        return self.__stop

    @property
    def name(self):
        return self.__name

    @abc.abstractmethod
    async def start(self):
        raise NotImplementedError

    async def stop(self):
        self.__stop = True
