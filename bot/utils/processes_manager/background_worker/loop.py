import abc
import asyncio
from dataclasses import dataclass

from .base import BaseBackgroundWorker

STOP_CHECK_TIMEOUT = .1


@dataclass
class IterationResult:
    timeout: float | int | None = None


class LoopBackgroundWorker(BaseBackgroundWorker, abc.ABC):
    DEFAULT_TIMEOUT: int | float | None = None

    def __init__(
            self, name: str | None = None,
            timeout: int | float | None = None,
    ):
        self.timeout = timeout or self.DEFAULT_TIMEOUT
        self.__is_iteration: bool = False
        super().__init__(name)

    async def stop(self):
        while True:
            if not self.__is_iteration:
                self.__stop = True
                break

            await asyncio.sleep(STOP_CHECK_TIMEOUT)

    def __await__(self):
        return self.start().__await__()

    @abc.abstractmethod
    async def iteration(self) -> IterationResult | None:
        raise NotImplementedError

    async def start(self):
        self.__stop = False

        while True:
            timeout = self.timeout

            if self.is_stopped:
                break
            try:
                self.__is_iteration = True
                result = await self.iteration()
                if isinstance(result, IterationResult) and result.timeout:
                    timeout = result.timeout
            except Exception as e:
                self.error_logger.error(e, exc_info=True)
            finally:
                self.__is_iteration = False

            if timeout:
                await asyncio.sleep(timeout)
