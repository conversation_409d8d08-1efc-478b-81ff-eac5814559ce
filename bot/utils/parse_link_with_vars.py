import re

AVAILABLE_STORE_VARS = ["product_id", "store_id", "lang"]


def parse_link_with_vars(
        available_vars: list[str],
        link: str | None = None,
        **kwargs: str | int,
) -> str | None:
    if not link:
        return None

    params = {}
    for var in available_vars:
        placeholder = f"{{{var}}}"
        if placeholder in link:
            value = kwargs.get(var, None)
            if value:
                params[var] = value

    result = re.sub(r'\{.*?}', '', link)

    if params:
        start = "?" if "?" not in result else "&"
        if start == "?":
            result += start
        result += "&".join([f"{key}={value}" for key, value in params.items()])

    return result
