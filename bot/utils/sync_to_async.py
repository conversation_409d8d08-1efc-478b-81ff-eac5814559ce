import contextvars
import concurrent.futures
import asyncio

from collections.abc import Callable, Awaitable
from functools import partial

from .type_vars import RT, P


EXECUTOR = concurrent.futures.ThreadPoolExecutor(max_workers=10000)


def run_in_threadpool(func: Callable[P, RT], *args: P.args, **kwargs: P.kwargs) -> Awaitable[RT]:
    if args or kwargs:
        func = partial(func, *args, **kwargs)

    loop = asyncio.get_event_loop()
    asyncio.set_event_loop(loop)
    context = contextvars.copy_context()
    return loop.run_in_executor(EXECUTOR, context.run, func)
