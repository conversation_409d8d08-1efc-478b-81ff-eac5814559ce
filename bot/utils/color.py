import re


def parse_css_rgb(css_color: str) -> tuple:
    """Конвертує CSS формат кольору (rgb або rgba) у кортеж RGB."""
    rgb_values = re.findall(r'\d+', css_color)
    return tuple(map(int, rgb_values[:3]))


def hex_to_rgb(hex_color: str) -> tuple:
    hex_color = hex_color.lstrip('#')
    if len(hex_color) == 3:
        return tuple(int(x, 16) for x in hex_color)
    else:
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))


def rgb_to_hex(rgb_color: tuple) -> str:
    return '#{:02x}{:02x}{:02x}'.format(*rgb_color)


def get_contrast_text_color(color_input: str) -> str:
    # Визначення формату кольору та його конвертація у RGB
    if color_input.startswith('#'):
        background_color = hex_to_rgb(color_input)
    else:
        background_color = parse_css_rgb(color_input)

    # Розрахунок відносної яскравості фону
    brightness = ((background_color[0]*0.299) +
                  (background_color[1]*0.587) +
                  (background_color[2]*0.114)) / 255

    # Вибір контрастного кольору
    contrast_color = (0, 0, 0) if brightness > 0.5 else (255, 255, 255)

    return rgb_to_hex(contrast_color)
