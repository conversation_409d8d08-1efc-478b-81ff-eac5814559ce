import io

import aiogram as tg
import aiowhatsapp as wa
import qrcode
from PIL import Image, ImageDraw, ImageFont
from aiogram import types
from aiogram.dispatcher import FSMContext
from psutils.forms.helpers import save_messages_to_state

import config as cfg
from db.models import ClientBot
from utils.message import send_tg_message, send_wa_message
from utils.redefined_classes import InlineKb


def generate_qr_code(
    body: str,
    text: str = None,
    font_size: int = 16,
    back_color: tuple[int, int, int, int] = None,
    fill_color: tuple[int, int, int, int] = None,
    convert_8_bit_channel: bool = False,
):
    if text:
        qr_code_instance = qrcode.QRCode()
        qr_code_instance.add_data(body)
        image = qr_code_instance.make_image(back_color="transparent")
        image = image.convert("RGBA")
        width, height = image.size

        if not back_color:
            back_color = (255, 255, 255, 255)
        qr_code = Image.new("RGBA", (width, height + font_size), back_color)
        qr_code.paste(image, (0, 0, image.width, image.height))
        draw = ImageDraw.Draw(qr_code)

        font = ImageFont.truetype(cfg.LABEL_FONT, font_size)
        bbox = font.getbbox(text)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        text_position = (width // 2 - text_width // 2, height - text_height)
        if not fill_color:
            fill_color = (0, 0, 0, 255)
        draw.text(text_position, text, font=font, fill=fill_color)

    else:
        qr_code = qrcode.make(body)

    buffer = io.BytesIO()
    if convert_8_bit_channel:
        qr_code = qr_code.convert("L", palette=Image.ADAPTIVE, colors=8)
    qr_code.save(buffer, format="png")
    buffer.seek(0)

    return buffer


async def send_qr_code(
    message: tg.types.Message | wa.types.message.AnswerObject,
    state: FSMContext,
    url: str,
    show_url: bool = True,
    url_apart: bool = False,
    message_text: str | None = None,
    keyboard: InlineKb | None = None,
    is_save: bool = True,
    bot: ClientBot | None = None,
    **kwargs
) -> tg.types.Message | wa.types.message.AnswerObject:
    qr_code = generate_qr_code(url, **kwargs)
    if show_url and message_text:
        message_text = "\n".join([url, message_text])
    else:
        message_text = url if show_url else message_text
    kwargs = {}

    if isinstance(message, wa.types.message.AnswerObject):
        wa_bot = wa.WhatsappBot(bot.token, bot.whatsapp_from)
        document_media = await wa_bot.upload_media(qr_code, "image/png")
        kwargs["photo"] = wa.types.MediaCaption(id=document_media.id, caption=message_text)
        kwargs["text"] = message_text
        kwargs["filename"] = 'qrcode.png'

    if show_url and url_apart:
        if isinstance(message, tg.types.Message):
            msg_photo = await send_tg_message(message.chat.id, "photo", photo=qr_code)
            msg_url = await send_tg_message(message.chat.id, "text", text=message_text, keyboard=keyboard)

            if is_save:
                await save_messages_to_state(state, [msg_photo.message_id, msg_url.message_id])
        elif isinstance(message, wa.types.message.AnswerObject):
            msg_photo = await send_wa_message(
                message.user.id, "photo", bot.token, bot.whatsapp_from,
                **kwargs
            )
            msg_url = await send_wa_message(
                message.user.id, "text", bot.token, bot.whatsapp_from,
                text=message_text, keyboard=keyboard
            )
        else:
            raise Exception("Message type not supported")
    else:
        if isinstance(message, types.Message):
            msg_photo = await send_tg_message(
                message.chat.id, "photo", photo=qr_code, text=message_text,
                keyboard=keyboard
            )
            if is_save:
                await save_messages_to_state(state, msg_photo)
        elif isinstance(message, wa.types.message.AnswerObject):
            msg_photo = await send_wa_message(
                message.user.id, "photo", bot.token, bot.whatsapp_from,
                keyboard=keyboard, **kwargs
            )
        else:
            raise Exception("Message type not supported")

    return msg_photo
