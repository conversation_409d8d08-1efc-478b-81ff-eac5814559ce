STRIPE_LOCALES = [
    "bg", "cs", "da", "de", "el", "en", "en-GB", "es", "es-419", "et",
    "fi", "fil", "fr", "fr-CA", "hr", "hu", "id", "it", "ja", "ko", "lt",
    "lv", "ms", "mt", "nb", "nl", "pl", "pt", "pt-BR", "ro", "ru", "sk",
    "sl", "sv", "th", "tr", "vi", "zh", "zh-HK", "zh-TW"
]


def get_checkout_session_locale(preferred_locale: str):
    if preferred_locale in STRIPE_LOCALES:
        return preferred_locale
    return "auto"
