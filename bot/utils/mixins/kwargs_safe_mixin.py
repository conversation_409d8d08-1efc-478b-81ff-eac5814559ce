from typing import List

from psutils.func import kwargs_safe


class KwargsSafeMixin:

    DEFAULT_KWARGS_SAFE_METHODS = []

    def __init_subclass__(cls, *, methods: List[str] | None | str = "*", **kwargs):
        super().__init_subclass__(**kwargs)
        if methods == "*":
            methods = cls.DEFAULT_KWARGS_SAFE_METHODS

        if not methods:
            return

        for method in methods:
            if not hasattr(cls, method):
                raise NotImplementedError(f"Method {method} is not implemented in {cls.__name__}")
            setattr(cls, method, kwargs_safe(getattr(cls, method)))
