from typing import Callable

from psutils.decorators import ExecTimeLogger as _ExecTimeLogger

from loggers import JSONLogger


class ExecTimeLogger(_ExecTimeLogger):
    def log_info(
            self, func: Callable,
            start_time: float, end_time: float,
            args, kwargs,
    ):
        exec_time = end_time - start_time

        logger = JSONLogger(
            "exec-time", {
                "func_name": func.__name__,
                "start_time": start_time,
                "end_time": end_time,
                "exec_time": exec_time,
            }
        )
        if self.log_args:
            logger.add_data(
                {
                    "args": args,
                    "kwargs": kwargs,
                }
            )

        logger.debug(
            f"{func.__name__}: Executed in {exec_time} sec"
        )


log_exec_time = ExecTimeLogger(True)
log_exec_time_na = ExecTimeLogger(False)  # no args
