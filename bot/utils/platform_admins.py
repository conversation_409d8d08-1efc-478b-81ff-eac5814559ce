import asyncio
import html
import logging

from aiogram.types import (
    InlineKeyboardMarkup as InlineKb,
    ReplyKeyboardMarkup as MenuKb,
)
from aiogram.utils.exceptions import CantParseEntities, ChatNotFound
from psutils.text import replace_html_symbols

import config as cfg
from utils.redefined_classes import Bot


async def send_message_to_platform_admins(
        message_text: str,
        keyboard: InlineKb | MenuKb = None,
        force_to_bot: bool = False
):
    close_session = False
    try:
        bot = Bot.get_current(no_error=False)
    except:
        bot = Bot(cfg.ROOT_BOT_API_TOKEN)
        close_session = True
    sent_messages = list()

    message_text = replace_html_symbols(message_text)

    if cfg.MONITORING_GROUP_CHAT_ID and not force_to_bot:
        try:
            with bot.with_token(cfg.ROOT_BOT_API_TOKEN):
                try:
                    msg = await bot.send_message(
                        cfg.MONITORING_GROUP_CHAT_ID,
                        message_text, reply_markup=keyboard
                    )
                except CantParseEntities:
                    message_text = html.escape(message_text)
                    msg = await bot.send_message(
                        cfg.MONITORING_GROUP_CHAT_ID,
                        message_text, reply_markup=keyboard
                    )
                sent_messages.append(msg)
        except Exception as e:
            logging.error(e, exc_info=True)
    else:
        for chat_id, status in cfg.PLATFORM_ADMINS.items():
            if not status:
                continue
            try:
                with bot.with_token(cfg.ROOT_BOT_API_TOKEN):
                    try:
                        msg = await bot.send_message(
                            chat_id, message_text, reply_markup=keyboard
                        )
                    except ChatNotFound:
                        continue
                    except Exception as e:
                        logging.error(e, exc_info=True)
                        msg = await bot.send_message(
                            chat_id, message_text, reply_markup=keyboard, parse_mode=""
                        )
                    sent_messages.append(msg)
            except Exception as e:
                logger = logging.getLogger()
                logger.error(e, exc_info=True)
    try:
        if close_session:
            await bot.session.close()
    except Exception as e:
        logger = logging.getLogger()
        logger.error(e, exc_info=True)
    finally:
        return sent_messages


def sync_send_message_to_platform_admins(text: str, keyboard: InlineKb | MenuKb = None):
    try:
        asyncio.ensure_future(send_message_to_platform_admins(text, keyboard))
    except Exception as e:
        from loggers import JSONLogger
        JSONLogger("platform-admins").error(
            e, {
                "text": text,
                "keyboard": keyboard,
            }
        )
