from abc import ABC
from typing import Any

from aiogram import types
from aiogram.dispatcher import FSMContext

import config
from db.models import User

from psutils.state_router.route_handlers.list_drawer import BaseListDrawer as _BaseListDrawer

from utils.keyboards import previous_button
from utils.text import f


class BaseListDrawer(_BaseListDrawer, ABC, methods=None):
    config_page_size_variable_name: str  # Variable in config for get page_size
    need_setup_rm_search_handler = False

    @classmethod
    async def handler(
            cls, message: types.Message,
            state: FSMContext,
            user: User = None,
            lang: str = None,
            mode: str = "edit",
            **kwargs,
    ) -> types.Message:
        if not user:
            user_chat_id = state.user
            user = await User.get(user_chat_id)

        if not lang:
            lang = await user.get_lang()

        return await super().handler(
            message=message, state=state,
            user=user, lang=lang, mode=mode,
            **kwargs,
        )

    @classmethod
    async def get_text(cls, text_var: str, lang: str, **text_kwargs: Any):
        return await f(text_var, lang, **text_kwargs)

    @classmethod
    async def get_page_size(cls, **kwargs: Any) -> int:
        if hasattr(cls, "config_page_size_variable_name"):
            return getattr(config, cls.config_page_size_variable_name)
        return await super().get_page_size(**kwargs)

    @classmethod
    async def footer_drawer(cls, **kwargs):
        keyboard, lang = map(kwargs.get, ("keyboard", "lang"))

        if not cls.need_previous_button:
            return

        keyboard.row(await previous_button(lang))
