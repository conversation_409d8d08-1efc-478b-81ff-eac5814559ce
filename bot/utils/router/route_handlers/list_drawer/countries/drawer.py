from typing import Any

from aiogram.dispatcher import FSMContext
from psutils.country import Country, Countries

from utils.keyboards import active_button
from utils.redefined_classes import InlineBtn

from ..base import BaseListDrawer
from .callback_data import CountryCallbackData


class BaseCountriesListDrawer(BaseListDrawer, methods=None):
    page_size = 10
    need_setup_search_handler = True

    @classmethod
    async def get_data_from_state(cls, state: FSMContext) -> dict[str, Any]:
        state_data = await state.get_data()
        return {
            "search_text": state_data.get(cls.search_key)
        }

    @classmethod
    async def object_drawer(cls, country_object: Country, lang: str, active: str | None = None):
        button_text = country_object.name
        if country_object.iso_code == active:
            button_text = await active_button(lang, button_text)
            iso_code = None
        else:
            iso_code = country_object.iso_code

        return InlineBtn(
            button_text,
            callback_data=CountryCallbackData(country=iso_code).to_str(),
        )

    @classmethod
    async def get_objects(
            cls,
            search_text: str | None = None,
            position: int | None = None,
            limit: int | None = None,
            operation: str | None = None,
    ) -> list[Country] | int:
        return Countries().get_countries(search_text, position, limit, operation)
