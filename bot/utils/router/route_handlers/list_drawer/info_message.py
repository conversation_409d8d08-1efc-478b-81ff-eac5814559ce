from abc import ABC, abstractmethod
from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.dispatcher.filters.state import State, StatesGroupMeta
from contextlib import suppress
from datetime import datetime
from psutils.convertors import datetime_to_str
from psutils.date_time import localise_datetime
from pydantic.dataclasses import dataclass
from typing import Any, Iterable, List, Tuple

from config import DEFAULT_TIME_ZONE
from db.models import ChatMessage, Group, User
from schemas import ChatMessageSentByEnum, MessageContentTypeEnum
from utils.keyboards import radio_button
from utils.redefined_classes import InlineBtn, InlineKb
from utils.router import Router
from utils.text import c, f
from .base import BaseListDrawer


@dataclass
class MessageData:
    id: int
    group_id: int
    user_id: int
    sent_by: ChatMessageSentByEnum
    text: str
    content_type: MessageContentTypeEnum
    time_created: datetime


class BaseInfoMessageListDrawer(BaseListDraw<PERSON>, ABC, methods=None):

    need_setup_pagination_handler = True
    need_setup_search_handler = True

    remove_search_text_variable = "cancel search button"
    object_info_button_text_variable = "object info button"
    hide_object_button_variable = "hide object button"

    message_text_variable = None
    search_message_text_variable = None
    empty_text_variable = None
    search_empty_text_variable = None

    filter_types: List[str]
    need_draw_last_message = True
    need_plus_or_minus_symbol = True
    need_message_button = True
    need_fill_empty_cell = False
    need_previous_button = False

    no_tags_text_variable = None
    tags_button_variable = None

    @classmethod
    def format_datetime(
            cls, datetime_: datetime, timezone: str | None,
            group_timezone: str | None = None
    ):
        datetime_ = localise_datetime(
            datetime_, timezone or group_timezone or DEFAULT_TIME_ZONE
        )
        return datetime_to_str(datetime_, short=True)

    @classmethod
    @abstractmethod
    async def get_object_info(
            cls, object: Any, is_selected: bool, timezone: str, lang: str
    ) -> str:
        raise NotImplementedError

    @classmethod
    @abstractmethod
    async def get_last_message(cls, object: Any) -> ChatMessage:
        raise NotImplementedError

    @classmethod
    @abstractmethod
    async def get_message_info(
            cls, last_message: ChatMessage | MessageData, object: Any, lang: str
    ):
        raise NotImplementedError

    @classmethod
    @abstractmethod
    async def get_message_callback_data(cls, object: Any, **kwargs):
        raise NotImplementedError

    @classmethod
    async def get_object_callback_data(cls, object: Any, **kwargs):
        return dict(
            callback_data=c(
                "select_object",
                id=object.id,
                p=kwargs.get("object_position"),
            )
        )

    @classmethod
    async def draw_selected_object(
            cls, object: Any, obj_position: int, keyboard: InlineKb, lang: str
    ):
        pass

    @classmethod
    async def object_drawer(
            cls, object_and_message: Tuple[Any, tuple] | Any,
            position: int,
            radio_position: int,
            iteration: int,
            keyboard: InlineKb,
            selected: List[int],
            timezone: str,
            lang: str,
    ):
        if isinstance(object_and_message, Iterable):

            object, *last_message = object_and_message
            if len(last_message) == 1 and (
                    isinstance(last_message[0], ChatMessage) or isinstance(
                last_message[0], MessageData
            )):
                last_message = last_message[0]
            else:
                last_message = MessageData(*last_message)
        else:
            object = object_and_message
            last_message = "__need_get__"

        if not hasattr(object, "user"):
            raise TypeError("object must has a property \"user\"")

        user: User = object.user
        if hasattr(object, "channel"):
            group: Group = object.channel.group
        else:
            group: Group = object.group

        if not isinstance(user, User):
            return
            # raise TypeError(f"object.user must be typeof User, not {type(user)}")
        if not isinstance(group, Group):
            raise TypeError(f"object.group must be typeof Group, not {type(group)}")

        if last_message == "__need_get__":
            last_message = await cls.get_last_message(object)

        object_position = position + iteration

        is_selected = object.id in selected

        if cls.need_plus_or_minus_symbol:
            plus_or_minus = "plus" if not is_selected else "minus"
            plus_or_minus = await f(f"crm {plus_or_minus} symbol", lang)
        else:
            plus_or_minus = ""

        object_info = await cls.get_object_info(object, is_selected, timezone, lang)
        if not object.user.chat_id and object.user.email:
            object_info = object_info.replace(object.user.full_name, object.user.email)

        button_text = await f(
            cls.object_info_button_text_variable, lang,
            object_info=object_info,
            plus_or_minus=plus_or_minus,
        )

        if object_position == radio_position:
            button_text = await radio_button(lang, button_text)

        object_data = await cls.get_object_callback_data(
            object, object_position=object_position
        )
        keyboard.row(InlineBtn(button_text, **object_data))

        if is_selected:
            if cls.need_message_button:
                button_text = await cls.get_message_info(last_message, object, lang)
                message_data = await cls.get_message_callback_data(
                    object, object_position=object_position
                )
                keyboard.row(InlineBtn(button_text, **message_data))

            keyboard.row()

            await cls.draw_selected_object(object, object_position, keyboard, lang)

            callback_data = c("select_object", id=object.id, p=object_position)
            symbols = (await f(cls.hide_object_button_variable, lang))[::2]

            row = list()
            for i in range(8):
                row.append(InlineBtn(symbols[i], callback_data=callback_data))

            keyboard.row(*row)

    @classmethod
    async def pagination_drawer(
            cls,
            position: int,
            page_size: int,
            all_objects_count: int,
            keyboard: InlineKb, lang: str,
            **kwargs,
    ):
        radio_position = kwargs.get("radio_position")

        last_position = all_objects_count - page_size

        prev_objects_count = position

        next_objects_count = prev_objects_count + page_size
        prev_position = prev_objects_count - page_size
        next_position = position + page_size
        if next_objects_count > all_objects_count:
            next_objects_count = all_objects_count

        callback_data = await cls.get_pagination_callback_data(0, **kwargs)
        first_button = InlineBtn(
            await f("first page button", lang), callback_data=callback_data
        )

        button_text = await f("prev page button", lang, count=prev_objects_count)
        if radio_position is not None and radio_position < position:
            button_text = await radio_button(lang, button_text)

        callback_data = await cls.get_pagination_callback_data(prev_position, **kwargs)
        prev_button = InlineBtn(button_text, callback_data=callback_data)

        button_text = await f("next page button", lang, count=next_objects_count)
        if radio_position is not None and radio_position >= next_position:
            button_text = await radio_button(lang, button_text)

        callback_data = await cls.get_pagination_callback_data(next_position, **kwargs)
        next_button = InlineBtn(button_text, callback_data=callback_data)

        callback_data = await cls.get_pagination_callback_data(last_position, **kwargs)
        last_button = InlineBtn(
            await f("last page button", lang), callback_data=callback_data
        )

        keyboard.row(first_button, prev_button, next_button, last_button)

    @classmethod
    async def pagination_callback_handler(
            cls, callback_query: types.CallbackQuery,
            state: FSMContext,
            mode: str, callback_data: dict,
            user: User, lang: str,
    ):
        new_position = callback_data.get("position")

        await cls.update_position(new_position, user=user, state=state)
        await Router.state_menu(callback_query, state, lang)

    @classmethod
    async def rm_search_button_handler(
            cls, callback_query: types.CallbackQuery,
            state: FSMContext, user: User, lang: str,
    ):
        await cls.update_search(None, state, user)
        await Router.state_menu(callback_query, state, lang)

    @classmethod
    async def search_handler(
            cls, message: types.Message, state: FSMContext, user: User, lang: str
    ):
        await cls.update_search(message.text, state, user)
        await Router.state_menu(state=state, lang=lang, get_state_message=True)
        with suppress(Exception):
            await message.delete()

    @classmethod
    @abstractmethod
    async def select_object_button_handler(
            cls, callback_query: types.CallbackQuery,
            state: FSMContext, callback_data: dict,
            user: User, lang: str,
    ):
        raise NotImplementedError

    @classmethod
    def setup_handlers(cls, dp: Dispatcher, state: StatesGroupMeta | State):
        super().setup_handlers(dp, state)
