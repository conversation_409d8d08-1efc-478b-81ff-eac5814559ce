from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.dispatcher.handler import ctx_data
from psutils.state_router import Router

from db.models import User


async def get_additional_params(
        router_data: dict,
        message_or_callback_query: types.Message | types.CallbackQuery | None = None,
        state: FSMContext = None,
        lang: str = None,
        mode: str = "edit",
):
    data = ctx_data.get(dict())
    router_data.update(**data)

    if isinstance(message_or_callback_query, types.Message):
        message = message_or_callback_query
    else:
        message = message_or_callback_query.message

    user = data.get("user", await User.get(message.chat.id))

    if lang is None:
        lang = data.get("lang", await user.get_lang())

    assert user, lang

    router_data["callback_mode"] = router_data.pop("mode", None)
    router_data["mode"] = mode


def get_router(dp: Dispatcher):
    return Router(dp, get_additional_params)
