from babel import UnknownLocaleError
from babel.numbers import format_currency as babel_format_currency, format_decimal
from decimal import Decimal
from typing import Literal, TypeVar

from config import NO_CENT_CURRENCIES


def format_sum(
        sum_: float | int, lang: str, zero_as_empty: bool = True, currency: str = None
):
    if not sum_ and zero_as_empty:
        return ""
    fmt = "#,##0" if currency and currency in NO_CENT_CURRENCIES and sum_ % 1 == 0 \
        else "#,##0.00"
    return format_decimal(sum_, fmt, lang)


def format_currency(
        sum_: float | int, currency: str, locale: str, territory: str | None = None
):
    """
    Форматує суму грошей згідно локалі та валюти.

    Args:
        sum_: сума для форматування
        currency: код валюти (наприклад, USD, EUR)
        locale: код мови (наприклад, en, uk)
        territory: код країни (наприклад, US, UA)

    Returns:
        Форматована сума у потрібній валюті
    """
    currency = currency.upper()

    try:
        # Спершу спробуємо використати мову та територію разом
        if territory:
            locale_str = f"{locale}_{territory.upper()}"
            try:
                return babel_format_currency(
                    sum_, currency, locale=locale_str, currency_digits=False
                )
            except (UnknownLocaleError, FileNotFoundError):
                # Якщо така комбінація не існує, продовжуємо
                pass

        # Спробуємо використати тільки мову
        try:
            return babel_format_currency(
                sum_, currency, locale=locale, currency_digits=False
            )
        except (UnknownLocaleError, FileNotFoundError):
            # Якщо мова не підтримується, спробуємо знайти іншу відповідну локаль
            pass

        # Перевіряємо, чи є валюта у списку тих, що не використовують копійки
        pattern = "#,##0" if currency in NO_CENT_CURRENCIES and sum_ % 1 == 0 else \
        "#,##0.00"

        # Використовуємо англійську як запасний варіант
        return f"{babel_format_currency(sum_, currency, locale='en_US', currency_digits=False)}"

    except Exception as e:
        # Якщо все інше не спрацювало, використовуємо найпростіший формат
        if currency in NO_CENT_CURRENCIES and sum_ % 1 == 0:
            return f"{int(sum_)} {currency}"
        else:
            return f"{sum_:.2f} {currency}"


TAmount = TypeVar("TAmount", bound=Decimal | float | int)


def fix_modifier(amount: TAmount, modifier: Decimal | float | int) -> TAmount:
    if (
            isinstance(amount, Decimal) and
            not isinstance(modifier, Decimal)
    ):
        return Decimal(modifier)
    elif (
            isinstance(modifier, Decimal) and
            not isinstance(amount, Decimal)
    ):
        return float(modifier)

    return modifier


def calculate_amount_modifiers(
        amount: TAmount,
        percent_modifier: Decimal | float | int | None = None,
        value_modifier: Decimal | float | int | None = None,
) -> TAmount:
    modifier_amount = type(amount)(0)

    if percent_modifier:
        modifier_amount += amount / 100 * fix_modifier(amount, percent_modifier)
    if value_modifier:
        modifier_amount += fix_modifier(amount, value_modifier)

    return modifier_amount


def apply_amount_modifiers(
        amount: TAmount,
        fee_or_discount: Literal["fee", "discount"],
        percent_modifier: Decimal | float | int | None = None,
        value_modifier: Decimal | float | int | None = None,
) -> TAmount:
    modifier_amount = calculate_amount_modifiers(
        amount, percent_modifier, value_modifier
    )

    if fee_or_discount == "fee":
        return amount + modifier_amount

    if fee_or_discount == "discount":
        return max(0, amount - modifier_amount)
