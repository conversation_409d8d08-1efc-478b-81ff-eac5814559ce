from abc import ABC

from psutils.exceptions import BaseErrorWithHTTPStatus
from psutils.text import paschal_case_to_snake_case

from utils.localisation import localisation


class ErrorWithHTTPStatus(BaseErrorWithHTTPStatus, ABC, base=True):
    @property
    def text_variable(self):
        return paschal_case_to_snake_case(self.__class__.__name__)

    @classmethod
    def get_text(cls):
        return localisation.sync_get_text(
            cls.text_variable, cls.examples_lang, **cls.example_text_params
        )
