from abc import ABC

from psutils.exceptions import ErrorWithTextVariable


class GoogleAPIError(ErrorWithTextVariable, ABC):

    def __init__(self, sheet_url: str, **kwargs):
        super().__init__(**{**kwargs, "sheet_url": sheet_url})


class GoogleAPINotFoundError(GoogleAPIError):
    text_variable = "sheet not found error"


class GoogleAPIUnauthorizedError(GoogleAPIError):
    text_variable = "sheet unauthorized error"

    def __init__(self, sheet_url: str, accessor_email: str, **kwargs):
        super().__init__(sheet_url, accessor_email=accessor_email, **kwargs)


class GoogleAPIUnknownError(GoogleAPIError):
    text_variable = "sheet unknown error"

    def __init__(self, sheet_url: str, status_code: int, **kwargs):
        super().__init__(sheet_url, status_code=status_code, **kwargs)


class GoogleAPIFormatError(GoogleAPIError):
    text_variable = "sheet format error"
