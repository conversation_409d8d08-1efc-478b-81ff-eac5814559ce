from gspread.exceptions import APIError

from config import PATH_TO_GOOGLE_CREDS_JSON
import config as cfg

from psutils.google_sheets import AsyncGoogleSheetsClient, get_service_account_email
from psutils.limiter import Limiter
from psutils.limiter.storage import RedisLimiterStorage

from utils.exceptions import (
    GoogleAPIUnauthorizedError, GoogleAPINotFoundError, GoogleAPIUnknownError, GoogleAPIError,
    GoogleAPIFormatError,
)

REDIS_PREFIX = "limiter"
if cfg.REDIS_PREFIX:
    REDIS_PREFIX += f"-{cfg.REDIS_PREFIX}"

limiter = Limiter(
    timeouts={
        "read": 1.02,
        "write": 1.02,
        r"sheet-name-[\w-]+": 60,
    },
    storage=RedisLimiterStorage(
        host=cfg.REDIS_HOST,
        port=cfg.REDIS_PORT,
        db=cfg.LIMITER_REDIS_DB,
        prefix=REDIS_PREFIX,
        pool_size=5000,
    )
)

# key is sheet ID and value is sheet name
sheet_names = {}


async def get_sheet_name(sheet_id: str) -> str:
    key = f"sheet-name-{sheet_id}"
    async with limiter.lock(key):

        when_available = await limiter.get_when_new_usage_available(key)
        if when_available is True or sheet_id not in sheet_names:

            client = AsyncGoogleSheetsClient(
                sheet_id, limiter=limiter,
                creds_file_path=PATH_TO_GOOGLE_CREDS_JSON,
            )
            await client.load_document()
            sheet_names[sheet_id] = client.document.title
            await limiter.save_used(key)

    return sheet_names[sheet_id]


def get_sheet_url(sheet_id: str) -> str:
    return f"https://docs.google.com/spreadsheets/d/{sheet_id}/edit"


def resolve_google_api_error(error: APIError, sheet_url: str, lang: str) -> GoogleAPIError:
    match error.response.status_code:
        case 400:
            return GoogleAPIFormatError(sheet_url, lang=lang)
        case 403:
            accessor_email = get_service_account_email(PATH_TO_GOOGLE_CREDS_JSON)
            return GoogleAPIUnauthorizedError(sheet_url, accessor_email, lang=lang)
        case 404:
            return GoogleAPINotFoundError(sheet_url, lang=lang)
        case _:
            return GoogleAPIUnknownError(sheet_url, error.response.status_code, lang=lang)
