import random


prises = {
    "500UAH": (
        3, "Поздравляем. Вы стали счастливчиком и выиграли главный приз: 500грн. Приз можно забрать подойдя к бару.",
    )
}

codes_and_prises = dict()

CODE_TEXT = " Ваш код: <code>{code}</code>"

codes = list()

for prise, data in prises.items():
    count, win_text = data
    for _ in range(count):
        code = random.randint(100000, 999999)
        while code in codes_and_prises.keys():
            code = random.randint(100000, 999999)
        codes_and_prises[code] = prise
        code_text = win_text + CODE_TEXT.replace("{code}", str(code))
        codes.append(code_text)

codes += ["Вы проиграли😟. Не расстраивайтесь, повезёт в следующий раз" for _ in range(10 - len(codes))]

codes_and_prises_table = ""

for code, prise in codes_and_prises.items():
    text = f"{code}: {prise}\n"
    codes_and_prises_table += text

random.shuffle(codes)


codes_text = "\n".join(codes)
codes_text = codes_text.replace(".", "\.")
codes_text = codes_text.replace(",", "\,")
codes_text = codes_text.replace(";", "\;")

with open("codes.txt", "w", encoding="utf-8") as file:
    file.write(codes_text)

with open("codes_and_prises_table.txt", "w", encoding="utf-8") as file:
    file.write(codes_and_prises_table)
