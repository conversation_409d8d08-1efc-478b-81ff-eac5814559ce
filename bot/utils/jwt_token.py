from datetime import datetime, timedelta

from jose import jwt

from config import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SECRET_KEY


def create_jwt_token(data: dict, expire: datetime | timedelta | None = None):
    to_encode = data.copy()
    if isinstance(expire, timedelta):
        expire = datetime.utcnow() + expire
    if expire:
        to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
