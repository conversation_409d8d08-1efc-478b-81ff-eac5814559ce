"""
Утиліти для роботи з датами та часом
"""

from datetime import datetime


def convert_datetime_to_str(obj):
    """Рекурсивно конвертує всі об'єкти datetime у рядки у словнику або списку."""
    if isinstance(obj, dict):
        return {k: convert_datetime_to_str(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_datetime_to_str(item) for item in obj]
    elif isinstance(obj, datetime):
        return obj.isoformat()
    return obj