def __increment_symbol__(symbol: str) -> str:
    if len(symbol) != 1:
        raise ValueError("symbol must be str with length 1")

    if not symbol.isascii() or not symbol.isalnum():
        raise ValueError("symbol must be letter or digit")

    if symbol == "9":
        return "A"

    if symbol.isdecimal():
        return str(int(symbol) + 1)

    if symbol == "Z":
        return "a"

    if symbol == "z":
        return "0"

    return chr(ord(symbol) + 1)


def increment_string(s: str):
    new_id = list(s)
    index = -1
    while True:
        new_symbol = __increment_symbol__(new_id[index])
        new_id[index] = new_symbol

        if new_symbol != "0":
            break

        index -= 1

        if len(new_id) + index < 0:
            new_id.insert(0, "1")
            break

    return "".join(new_id)


__all__ = [
    "increment_string",
]
