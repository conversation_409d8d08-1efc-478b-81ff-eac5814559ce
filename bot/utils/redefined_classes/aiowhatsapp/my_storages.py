import config as cfg

from aiowhatsapp.contrib.fsm_storage.redis import RedisStorage

import db
from utils.helpers import get_running_file_name


class MyRedisStorage(RedisStorage):
    async def reset_state(
            self, *,
            user: str | int | None = None,
            with_data: bool | None = True
    ):
        await super().reset_state(user=user, with_data=with_data)

        if get_running_file_name() != cfg.WHATSAPP_BOT:
            return

        if not with_data:
            return

        user = await db.models.User.get_by_wa_phone(user)
        if not user:
            return

        user_bot_activity = await user.activity_in_bot
        if not user_bot_activity:
            return

        await user_bot_activity.update(active_chat=None)
