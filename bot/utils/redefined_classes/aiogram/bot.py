import asyncio
from typing import Optional, Union, Type

import aiohttp
from aiogram import <PERSON><PERSON> as _Bot
from aiogram.bot.api import TelegramAPIServer
from aiogram.types import base

from config import TELEGRAM_API_SERVER_BASE
from utils.type_vars import T

local_server = TelegramAPIServer.from_base(TELEGRAM_API_SERVER_BASE)


class Bot(_Bot):
    def __init__(
            self,
            token: base.String,
            loop: Optional[Union[asyncio.BaseEventLoop, asyncio.AbstractEventLoop]] = None,
            connections_limit: Optional[base.Integer] = None,
            proxy: Optional[base.String] = None,
            proxy_auth: Optional[aiohttp.BasicAuth] = None,
            validate_token: Optional[base.Boolean] = True,
            parse_mode: Optional[base.String] = "HTML",
            disable_web_page_preview: Optional[base.Boolean] = True,
            protect_content: Optional[base.Boolean] = None,
            timeout: Optional[Union[base.Integer, base.Float, aiohttp.ClientTimeout]] = None,
            server: TelegramAPIServer = local_server,
    ):
        super().__init__(
            token, loop, connections_limit,
            proxy, proxy_auth, validate_token,
            parse_mode, disable_web_page_preview,
            protect_content, timeout, server,
        )

    @classmethod
    def set_current(cls: Type[T], value: T):
        return _Bot.set_current(value)

    @classmethod
    def get_current(cls: Type[T], no_error=True) -> T:
        return _Bot.get_current(no_error)
