import json
from typing import List

from aiogram import types
from aiogram.contrib.fsm_storage.redis import RedisStorage2

import config as cfg
import db
from config import REDIS_PREFIX
from db.models import ClientBot, UserClientBotActivity
from utils.helpers import get_running_file_name


class MyRedisStorage(RedisStorage2):

    _media_group_ttl = 2

    def __init__(self, prefix: str = "fsm", **kwargs):
        if REDIS_PREFIX:
            prefix += f"-{REDIS_PREFIX}"
        super().__init__(prefix=prefix, **kwargs)

    async def get_connection(self):
        redis = await self._get_adapter()
        redis = await redis.get_redis()
        return redis

    def generate_key(self, *parts):
        parts = list(parts)

        bot_from_db_id = db.models.ClientBot.get_current_bot_id()

        file = get_running_file_name()

        if bot_from_db_id:
            parts.insert(0, bot_from_db_id)

        elif file.endswith(cfg.SERVICE_BOT):
            parts.insert(0, "service")

        elif file.endswith(cfg.ROOT_BOT):
            parts.insert(0, "root")

        elif file.endswith(cfg.API_HANDLER):
            parts.insert(0, "api")

        else:
            raise TypeError("MyRedisStorage can be used only in bots")

        return RedisStorage2.generate_key(self, *parts)

    def _get_media_group_handled_key(self, media_group_id: str) -> str:
        return f"{self._prefix}:{media_group_id}:handled"

    def _get_media_group_messages_key(self, media_group_id: str) -> str:
        return f"{self._prefix}:{media_group_id}:messages"

    async def set_media_group_as_handled(self, media_group_id: str) -> bool:
        connection = await self.get_connection()
        value = await connection.get(self._get_media_group_handled_key(media_group_id))
        if value:
            return False
        return await connection.set(
            self._get_media_group_handled_key(media_group_id), 1, self._media_group_ttl
        )

    async def append_message_to_media_group(
            self, media_group_id: str, message: types.Message
    ):
        connection = await self.get_connection()
        length = await connection.lpush(
            self._get_media_group_messages_key(media_group_id), message.as_json()
        )

        if length == 1:
            await connection.expire(
                self._get_media_group_messages_key(media_group_id),
                self._media_group_ttl
            )

    async def get_media_group_messages(
            self, media_group_id: str
    ) -> List[types.Message]:
        connection = await self.get_connection()
        raw_messages = await connection.lrange(
            self._get_media_group_messages_key(media_group_id), 0, 10
        )
        messages = [types.Message.to_object(json.loads(m)) for m in raw_messages]
        messages.sort(key=lambda m: m.message_id)

        return messages

    async def delete_media_group(self, media_group_id: str):
        connection = await self.get_connection()
        await connection.delete(
            self._get_media_group_handled_key(media_group_id),
            self._get_media_group_messages_key(media_group_id),
        )

    async def reset_state(
            self, *,
            chat: str | int | None = None,
            user: str | int | None = None,
            with_data: bool | None = True
    ):
        await super().reset_state(chat=chat, user=user, with_data=with_data)
        if get_running_file_name() not in [
            cfg.CLIENT_BOT, cfg.FRIENDLY_BOT, cfg.SERVICE_BOT
        ]:
            return

        if not with_data:
            return

        user_chat_id = user or chat

        user = await db.models.User.get(user_chat_id)
        if not user:
            return

        if (bot := await ClientBot.get_current()) and \
                (user_bot_activity := await UserClientBotActivity.get(user, bot)):
            await user_bot_activity.update(active_chat=None)
