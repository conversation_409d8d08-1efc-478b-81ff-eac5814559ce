from __future__ import annotations

import operator
from copy import deepcopy
from functools import reduce
from types import GenericAlias
from typing import Any, Callable, Coroutine, Type, get_origin, get_type_hints

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.dispatcher.filters.state import State, StatesGroup
from psutils.forms import Fields<PERSON><PERSON><PERSON><PERSON>, WizardForm
from psutils.forms.fields import InlineButtons<PERSON>ield, SetStateField
from psutils.forms.fields.base import BaseCallback<PERSON>ield, BaseField
from psutils.forms.fields.set_state import SetStateCallbackData
from psutils.fsm import get_field_from_state
from psutils.func import check_function_spec
from psutils.text import snake_case_to_paschal_case

from db import models
from utils.keyboards import active_button, get_navigation_keyboard, get_yes_or_no_keyboard
from utils.message import send_or_edit_message
from utils.redefined_classes import InlineBtn, InlineKb
from utils.router import Router
from utils.router.route_handlers import BaseListDrawer
from utils.text import enter_text_with_specified_value, f


class Field:
    def __init__(
            self,
            *args: str,
            form_field: BaseField | None = None,
            field_data: dict[str, Any] | None = None,
            route: str | Callable[..., Coroutine] | BaseListDrawer | None = None,
            texts: dict[str, str] | None = None,
            extra_data: dict[str, Any] | None = None,
            is_before_choose_field: bool = False,
            need_group_state: bool = True,
            need_active_button: bool = False,
            default: Any = None,
    ):
        self.type = None
        self._name: str | None = None

        self.args: tuple[str, ...] = args
        self.field = form_field
        self.field_data: dict[str, Any] | None = field_data
        self.texts: dict[str, str] = texts
        self.extra_data: dict[str, Any] | None = extra_data

        if isinstance(route, str):
            route = getattr(self, route, None)
            if not route:
                raise ValueError(f"No function with name '{route}' is defined")
        self.route: Callable[[str], Coroutine] | BaseListDrawer = route

        self._is_clear: bool = False
        self._is_delete: bool = False
        self.need_create_route: bool = False

        self._owner: Type[BaseInterfaceModel] | None = None
        self._callback_data: str | None = None

        self.is_before_choose_field = is_before_choose_field
        self.need_group_state = need_group_state if self._need_group_state is None else self._need_group_state
        self.need_active_button = need_active_button if self._need_active_button is None else self._need_active_button
        self.default = default if self._default is None else self._default

    def __set_name__(self, owner: Type[BaseInterfaceModel], name: str):
        if not issubclass(owner, BaseInterfaceModel):
            raise ValueError("Field object must be set only to subclass of BaseInterfaceModel")
        self._owner = owner
        self._name = name

        if name == "delete":
            self._is_delete = True

        if not self.field:
            type_hints = get_type_hints(owner)
            type_ = type_hints.get(name)
            if isinstance(type_, GenericAlias):
                self.type = get_origin(type_)
            else:
                self.type = type_

            if self._is_delete and not self.type:
                self.type = InlineButtonsField

            if self.field_data is None:
                self.field_data = {}
            if self.type is InlineButtonsField and not self._is_delete:
                if "callback_mode" not in self.field_data and "callback_data_cls" not in self.field_data:
                    self.field_data.update(callback_mode=name)
                if not self.need_group_state:
                    self._callback_data = name
        elif not self.need_group_state and isinstance(self.field, BaseCallbackField):
            self._callback_data = self.field.callback_mode

        if owner.object_data:
            self._is_clear = self.extra_data.get("need_clear_button", False) if self.extra_data else False

        if not self.route and self.texts:
            self.need_create_route = True

        owner.add_field(self)

    @property
    def name(self):
        if not self._name:
            raise ValueError("Field must be set to BaseInterfaceModel before using this method")
        return self._name

    async def get_object_and_field(self, state_data: dict):
        object_ = await self._owner.get_object(state_data, self.name)

        if not self._owner.object_data:
            field = None
        elif "id_name" in self._owner.object_data:
            field = self.extra_data.get("field", self.name) if self.extra_data else self.name
        elif "method" in self._owner.object_data:
            field = self._owner.object_data.get("field", self.name)
        else:
            field = None

        return object_, field

    def get_field(self):
        if self.field:
            field = self.field
        elif self._is_delete:
            field = self.get_delete_field()
        elif self.type:
            args = self.args or []
            field_data = self.field_data or {}

            field = self.type(*args, **field_data)
        else:
            raise ValueError("self.field or self.type must be specified")

        if self.extra_data and self.extra_data.get("need_clear_handler"):
            field &= self.get_clear_field()

        return field

    def get_clear_field(self):
        async def clear_data_saver(_, state: FSMContext):
            state_data = await state.get_data()
            object_, field = await self.get_object_and_field(state_data)

            if object_ and field:
                if self.extra_data and self.extra_data.get("path"):
                    data = deepcopy(getattr(object_, field))
                    data = self.pop_data(data, self.extra_data.get("path"))
                    data = {field: data}

                else:
                    data = {field: self.default}
                if self.extra_data and "clear" in self.extra_data:
                    data.update(self.extra_data["clear"])
                await object_.update(**data)

        return InlineButtonsField(
            callback_mode="clear",
            data_saver=clear_data_saver,
        )

    def get_delete_field(self):
        async def delete_data_saver(data: dict, state: FSMContext):
            state_data = await state.get_data()
            object_ = await self._owner.get_object(state_data, self.name)

            if object_ and data.get("is_deletion_confirmed"):
                await object_.delete()
                await state.set_state(self._owner.form.previous_state)
            elif hasattr(self._owner.state_group, "ChooseField"):
                await self._owner.state_group.ChooseField.set()

        if "data_saver" not in self.field_data:
            self.field_data["data_saver"] = delete_data_saver
        return self.type(
            callback_mode="confirmation",
            callback_keys=("answer", "is_deletion_confirmed",),
            **self.field_data,
        )

    def get_route(self):
        if self.need_create_route:
            self.create_route()
        return self.route

    def create_route(self):
        if not self.need_group_state:
            return

        async def edit_field_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "edit"):
            state_data = await state.get_data()

            if self._owner.form_type is WizardForm:
                current_value = state_data.get(self.name, self.default)
            else:
                object_, field = await self.get_object_and_field(state_data)
                current_value = getattr(object_, field) if object_ and field and not (self.extra_data and
                                                                                      self.extra_data.get(
                                                                                          "is_operation"
                                                                                      )) else (
                    self.default)
                if self.extra_data and self.extra_data.get("path"):
                    current_value = self.extract_data(current_value, self.extra_data.get("path"))

            if callable(current_value):
                current_value = None

            if isinstance(current_value, Coroutine):
                current_value = await current_value

            keyboard = None
            kwargs = self.texts.get("kwargs", {})
            if "method" in kwargs:
                kwargs = await self._owner.get_method_data(state_data, kwargs, self.name, lang)
            is_content_type = self.extra_data.get("is_content_type", False) if self.extra_data else False

            if is_content_type:
                content_type = current_value.get("content_type")
                current_value = current_value.get(content_type)
            else:
                content_type = "text"

            if self._is_clear and current_value:
                keyboard = InlineKb()
                button_text = await f(self.clear_button_text, lang)
                keyboard.insert(InlineBtn(button_text, callback_data="clear"))

            if content_type == "text" and current_value:
                message_text = await enter_text_with_specified_value(
                    await f(self.texts.get("variable"), lang, **kwargs),
                    current_value, lang,
                )
            else:
                message_text = await f(self.texts.get("variable"), lang, **kwargs)
            keyboard = await get_navigation_keyboard(
                lang,
                need_next=self._owner.form_type is WizardForm and bool(current_value),
                keyboard=keyboard,
            )

            new = mode == "new"
            if content_type == "text" or not current_value:
                return await send_or_edit_message(message, message_text, keyboard=keyboard, new=new)
            return await send_or_edit_message(message, message_text, current_value, content_type, keyboard, new=new)

        async def delete_field_menu(message: types.Message, lang: str, state: FSMContext):
            kwargs = self.texts.get("kwargs", {})
            state_data = await state.get_data()
            if "method" in kwargs:
                kwargs = await self._owner.get_method_data(state_data, kwargs, self.name, lang)
            message_text = await f(self.texts.get("variable"), lang, **kwargs)
            keyboard = await get_yes_or_no_keyboard(lang, callback_mode="confirmation")
            return await send_or_edit_message(message, message_text, keyboard=keyboard)

        self.route = delete_field_menu if self._is_delete else edit_field_menu

    def extract_data(self, data: dict, path: str):
        if not path or not isinstance(data, dict):
            return data

        items = path.split("->")
        item = items.pop(0)
        value = data.get(item)

        if isinstance(value, dict) and items:
            return self.extract_data(value, "->".join(items))
        return value

    def pop_data(self, data: dict, path: str):
        items = path.split("->")
        item = items.pop(0)
        value = data.get(item)

        if isinstance(value, dict) and items:
            return {item: self.extract_data(value, "->".join(items))}
        data.pop(item)
        return data

    @property
    def clear_button_text(self):
        button_text = "brand interface clear button"
        return self.texts.get("clear_button", button_text) if self.texts else button_text

    @property
    def callback_data(self):
        return self._callback_data if self._callback_data else SetStateCallbackData(field=self.name).to_str()

    @property
    def _need_group_state(self):
        return self.extra_data.get("need_group_state") if self.extra_data else None

    @property
    def _default(self):
        return self.extra_data.get("default") if self.extra_data else None

    @property
    def _need_active_button(self):
        return self.extra_data.get("need_active_button") if self.extra_data else None

    async def check_active_button(self, button_text: str, lang: str, state_data: dict) -> str:
        if not self.need_active_button:
            return button_text

        object_, field = await self.get_object_and_field(state_data)
        if getattr(object_, field, None):
            button_text = await active_button(lang, button_text)
        return button_text

    async def get_button_text(self, lang: str, state_data: dict):
        if self.texts:
            button_text = self.texts.get("button_text")
            if button_text:
                button_text = await f(button_text, lang)
                return await self.check_active_button(button_text, lang, state_data)

        if self.extra_data:
            button_text = self.extra_data.get("button_text")
            if button_text:
                return await self.check_active_button(button_text, lang, state_data)

            get_name_func = self.extra_data.get("get_name_func")
            if get_name_func:
                get_name_func = getattr(self._owner, get_name_func)
                kwargs = {"state_data": state_data, "lang": lang, "field_name": self.name}
                kwargs = check_function_spec(get_name_func, kwargs)
                return await get_name_func(**kwargs)

        if self._is_delete:
            return await f("store brand interface delete button", lang)

        button_text = self.name.replace("_", " ").capitalize()
        return await self.check_active_button(button_text, lang, state_data)

    async def set(self):
        await self._owner.set_state(self.name)


class BaseInterfaceModel:
    form_type: FieldsListForm | WizardForm = None
    form_data: dict[str, Any] = None
    object_data: dict[str, Any] = None
    row_width: int = 1
    choose_field_texts: dict[str, str] = None
    _fields: dict[str, Field] = None

    name: str = None
    _is_choose_field: bool = False
    state_group: Type[StatesGroup] = None
    form: Type[FieldsListForm | WizardForm] = None

    def __init_subclass__(cls, **kwargs):
        if cls.form_type is None:
            raise ValueError("the form_type parameter must be specified")

        if cls._fields is None:
            cls._fields = {}

        if (
                cls.form_type is FieldsListForm and
                cls.choose_field_texts and
                "variable" in cls.choose_field_texts
        ):
            cls._is_choose_field = True

        cls.set_name()
        cls.create_state_group()
        cls.create_form()

    @classmethod
    def add_field(cls, field: Field):
        if cls._fields is None:
            cls._fields = {}
        cls._fields[field.name] = field

    @classmethod
    def set_name(cls):
        cls.name = cls.__name__.replace("InterfaceModel", "")

    @classmethod
    async def get_method_data(
            cls,
            state_data: dict,
            data: dict,
            field_name: str | None = None,
            lang: str | None = None
    ):
        method = getattr(cls, data.get("method"))
        params_names = data.get("params", [])

        values = []
        for param_name in params_names:
            if param_name == "field_name":
                values.append(field_name)
            elif param_name == "lang":
                values.append(lang)
            else:
                values.append(state_data.get(param_name))

        kwargs = {"field_name": field_name, "lang": lang}
        kwargs.update(dict(zip(params_names, values)))
        kwargs.update(state_data)
        kwargs = check_function_spec(method, kwargs)
        return await method(**kwargs)

    @classmethod
    async def get_object(cls, state_data: dict, field_name: str | None = None):
        if cls.object_data and "id_name" in cls.object_data:
            object_id = state_data.get(cls.object_data.get("id_name"))

            object_type = cls.object_data.get("type")
            if isinstance(object_type, type):
                object_cls = object_type
            else:
                object_cls = getattr(models, cls.object_data.get("type"))
            object_ = await object_cls.get(object_id)

        elif cls.object_data and "method" in cls.object_data:
            object_ = await cls.get_method_data(state_data, cls.object_data, field_name)
        else:
            object_ = None

        return object_

    @classmethod
    async def start(cls):
        await cls.state_group.first()

    @classmethod
    async def set_state(cls, name: str):
        if not name.isupper() or "_" in name:
            state_name = snake_case_to_paschal_case(name)
        else:
            state_name = name
        if not hasattr(cls.state_group, state_name):
            raise ValueError(f"{cls.__name__}.state_group has no state {state_name}")
        await getattr(cls.state_group, state_name).set()

    @classmethod
    async def set_state_field(cls):
        await cls.set_state("choose_field")

    @classmethod
    def create_state_group(cls):
        states = []
        is_choose_field_added = False
        for field in cls._fields.values():
            if not field.need_group_state:
                continue

            if (
                    cls._is_choose_field and
                    not is_choose_field_added and
                    not field.is_before_choose_field
            ):
                states.append("ChooseField")
                is_choose_field_added = True

            states.append(snake_case_to_paschal_case(field.name))

        if not is_choose_field_added:
            states.append("ChooseField")

        data = {
            state_name: State()
            for state_name in states
        }
        # noinspection PyTypeChecker
        cls.state_group = type(f"{cls.name}Group", (StatesGroup,), data)

    @classmethod
    def create_form(cls):
        data = {field.name: field.get_field() for field in cls._fields.values() if field.need_group_state}
        data.update(state_group=cls.state_group)

        if cls.form_data is not None:
            data.update(**cls.form_data)
        if hasattr(cls.state_group, "ChooseField"):
            choose_field_data = [field.get_field() for field in cls._fields.values() if not field.need_group_state]
            data.update(
                choose_field=reduce(operator.and_, choose_field_data, SetStateField()),
                choose_field_state=cls.state_group.ChooseField.state,
            )

        for key in dir(cls):
            if key.startswith("form_") and key not in ("form_type", "form_data"):
                data.update(
                    {
                        key[5:]: getattr(cls, key)
                    }
                )

        prefix = "Edit" if cls.form_type is FieldsListForm else "Create"
        # noinspection PyTypeChecker
        cls.form = type(f"{prefix}{cls.name}Form", (cls.form_type,), data)

        if callable(getattr(cls, "data_saver", None)):
            # noinspection PyUnresolvedReferences
            cls.form.data_saver = cls.data_saver
        elif cls.object_data and "data_saver" not in data:
            cls.form.object_data = cls.object_data

            # noinspection PyDecorator
            @classmethod
            async def data_saver(cls, data: dict, state: FSMContext):
                state_data = await state.get_data()
                field_name = await get_field_from_state(state)
                object_ = await cls.owner.get_object(state_data, field_name)

                if object_:
                    if cls.owner.object_data and "method" in cls.owner.object_data:
                        field = cls.owner.object_data.get("field")
                    else:
                        field = None

                    if field:
                        await object_.update(**{field: data.get(field_name)})
                    else:
                        await object_.update(**data)

            cls.form.data_saver = data_saver
            cls.form.owner = cls

        if callable(getattr(cls, "set_prev_state", None)):
            # noinspection PyUnresolvedReferences
            cls.form.set_prev_state = cls.set_prev_state
        if callable(getattr(cls, "previous_button_handler", None)):
            # noinspection PyUnresolvedReferences
            cls.form.previous_button_handler = cls.previous_button_handler
        if callable(getattr(cls, "back_to_previous_state_button_handler", None)):
            # noinspection PyUnresolvedReferences
            cls.form.back_to_previous_state_button_handler = cls.back_to_previous_state_button_handler

    @classmethod
    def setup_handlers(cls, dp: Dispatcher):
        cls.form.setup_handlers(dp)

    @classmethod
    async def get_choose_keyboard(cls, lang: str, state_data: dict):
        keyboard = InlineKb(row_width=cls.row_width)
        for field in cls._fields.values():
            route = field.get_route()
            if isinstance(route, BaseListDrawer):
                continue

            button_text = await field.get_button_text(lang, state_data)
            callback_data = field.callback_data
            keyboard.insert(InlineBtn(button_text, callback_data=callback_data))
        return await get_navigation_keyboard(lang, keyboard=keyboard)

    @classmethod
    def get_choose_field_route(cls):
        async def choose_field_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "edit"):
            state_data = await state.get_data()
            kwargs = cls.choose_field_texts.get("kwargs", {})
            if "method" in kwargs:
                kwargs = await cls.get_method_data(state_data, kwargs, "choose_field", lang)
            message_text = await f(cls.choose_field_texts.get("variable"), lang, **kwargs)
            keyboard = await cls.get_choose_keyboard(lang, state_data)
            new_mode = mode == "new"
            return await send_or_edit_message(message, message_text, keyboard=keyboard, new=new_mode)

        return choose_field_menu

    @classmethod
    def register_routes(cls, router: Router):
        if cls._is_choose_field:
            # noinspection PyUnresolvedReferences
            router.add_route(cls.state_group.ChooseField, cls.get_choose_field_route())

        for field in cls._fields.values():
            route = field.get_route()
            if not route:
                continue
            state = getattr(cls.state_group, snake_case_to_paschal_case(field.name))
            router.add_route(state, route)
