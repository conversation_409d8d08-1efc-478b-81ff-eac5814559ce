from pydantic import BaseModel


class FieldTexts(BaseModel):
    variable: str | None = None
    clear_button: str | None = None
    button_text: str | None = None
    kwargs: dict = {}


class ExtraData(BaseModel):
    button_text: str | None = None
    get_name_func: str | None = None
    field: str | None = None
    path: str | None = None
    default: str | None = None
    need_group_state: bool = True
    is_content_type: bool = False
    need_active_button: bool = False
    need_clear_button: bool = False
    need_clear_handler: bool = False
    clear: dict | None = None
