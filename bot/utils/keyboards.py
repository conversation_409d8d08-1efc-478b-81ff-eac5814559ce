import config as cfg
from utils.redefined_classes import InlineKb, InlineBtn, MenuKb, MenuBtn
from utils.text import c, f


async def get_yes_or_no_keyboard(
        lang: str, param: str | int = "",
        yes_callback_data: str = None,
        callback_mode: str = None,
        data: dict = None,
        without_no: bool = False
):
    keyboard = InlineKb(row_width=2)
    buttons = list()
    if param:
        param = f":{param}"

    if callback_mode:
        if not isinstance(data, dict):
            data = {}

        yes_callback_data = c(callback_mode, **data, answer=True)
        no_callback_data = c(callback_mode, **data, answer=False)

    else:
        no_callback_data = "no" + param
        if yes_callback_data is None:
            yes_callback_data = "yes" + param

    buttons.append(InlineBtn(await f("yes button", lang), callback_data=yes_callback_data))

    if not without_no:
        buttons.append(InlineBtn(await f("no button", lang), callback_data=no_callback_data))

    keyboard.add(*buttons)
    return keyboard


async def get_cancel_keyboard(lang: str):
    keyboard = MenuKb(resize_keyboard=True, one_time_keyboard=False)
    keyboard.add(MenuBtn(await f("action cancel button", lang)))
    return keyboard


async def previous_button(
        lang: str = None,
        button_text: str = None,
        mode: str | None = "edit",
        prev: bool | int = None,
) -> InlineBtn:
    if not button_text and not lang:
        raise ValueError("lang and button_text can't be None together")

    if not button_text:
        button_text = await f("previous button", lang)

    callback_kwargs = dict()
    if mode:
        callback_kwargs.update(_mode=mode)

    if prev is not None:
        callback_kwargs.update(prev=int(prev))

    return InlineBtn(button_text, callback_data=c("previous", **callback_kwargs))


async def get_previous_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb()
    keyboard.insert(await previous_button(lang))
    return keyboard


async def next_button(lang: str, button_text: str = None) -> InlineBtn:
    text = button_text if button_text else await f("next button", lang)
    return InlineBtn(text, callback_data="next")


async def done_button(lang: str) -> InlineBtn:
    return InlineBtn(await f("done button", lang), callback_data="done")


async def skip_button(lang: str) -> InlineBtn:
    button = InlineBtn(await f("skip button", lang), callback_data="skip")
    return button


async def get_interval_in_types_keyboard(current: str, lang: str, interval_in_types: tuple | None = None) -> InlineKb:
    keyboard = InlineKb()
    if not interval_in_types:
        interval_in_types = cfg.INTERVAL_IN_TYPES

    for interval_in_type in interval_in_types:

        button_text = await f(f"in_{interval_in_type}_button", lang)
        if interval_in_type == current:
            button_text = await active_button(lang, button_text)

        callback_data = c("interval_in", interval_in=interval_in_type)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return keyboard


async def get_end_process_keyboard(lang: str) -> MenuKb:
    keyboard = MenuKb(resize_keyboard=True)
    keyboard.insert(MenuBtn(await f("end process with icon", lang)))
    return keyboard


async def active_button(lang: str, button_text: str) -> str:
    return await f("active button", lang, handle=button_text)


async def get_navigation_keyboard(
        lang: str, keyboard: InlineKb = None,
        need_next: bool = False,
        new_row: bool = True,
        **kwargs,
) -> InlineKb:
    if keyboard is None:
        keyboard = InlineKb()
    elif new_row:
        keyboard.row()

    button_text = kwargs.get("previous_button_text")
    keyboard.insert(await previous_button(lang, button_text))
    if need_next:
        button_text = kwargs.get("next_button_text")
        keyboard.insert(await next_button(lang, button_text))

    return keyboard


async def get_done_keyboard(
        lang: str,
        keyboard: InlineKb = None,
        need_save: bool = False,
        need_next: bool = False,
        need_prev: bool = True,
):
    if not keyboard:
        keyboard = InlineKb()

    row = list()

    if need_prev:
        row.append(await previous_button(lang))

    if need_save:
        row.append(await done_button(lang))

    if need_next:
        row.append(await next_button(lang))

    keyboard.row(*row)
    return keyboard


async def radio_button(lang: str, handle: str) -> str:
    return await f("radio button", lang, handle=handle)


async def make_share_button(button_text: str, link: str):
    return InlineBtn(button_text, url=f"https://telegram.me/share/url?url={link}")
