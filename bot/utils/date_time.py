from datetime import datetime, timedelta, timezone as dt_timezone
from pytz import timezone


def utcnow():
    return datetime.now(dt_timezone.utc).replace(tzinfo=None)


def get_prev_month(tz: str):
    cur_month_start = datetime.now(tz=timezone(tz)).replace(
        day=1, hour=0, minute=0, second=0, microsecond=0
    )

    period_end = cur_month_start - timedelta(microseconds=1)
    period_start = period_end.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    return period_start, period_end
