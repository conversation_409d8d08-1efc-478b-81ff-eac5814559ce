import json
import logging
from typing import Dict, Any

from aiogram import types
from aiogram.dispatcher.filters import Filter, BoundFilter
from aiogram.dispatcher.handler import ctx_data

from psutils.text import parse_deep_link_data

from utils.text import f

from .helpers import get_lang


class LocalisationFilter(Filter):

    def __init__(self, equal: str = None, starts: str = None, localisation_kwargs: dict = None):

        if all([starts, equal]):
            raise ValueError("only one of starts and equal can be specified")

        if localisation_kwargs is None:
            localisation_kwargs = dict()
        elif type(localisation_kwargs) is not dict:
            raise ValueError(f"Argument localisation_kwargs must be a dict or None, not {type(localisation_kwargs)}")

        self.text_variable = equal if equal else starts

        self.equal = bool(equal)
        self.starts = bool(starts)

        self.localisation_kwargs = localisation_kwargs

    @classmethod
    def validate(cls, full_config: Dict[str, Any]) -> Dict[str, Any] | None:
        """
            Validator for filters factory

            From filters factory this filter can be registered with arguments:

             - ``lequal`` (will be passed as ``equal``)
             - ``lstarts`` (will be passed as ``starts``)
             - ``lkwargs`` (will be passed as ``localisation_kwargs``)

            :param full_config:
            :return: config or empty dict
        """
        config = dict()
        if "lequal" in full_config:
            config["equal"] = full_config.pop("lequal")
        if "lstarts" in full_config:
            config["starts"] = full_config.pop("lstarts")
        if "lkwargs" in full_config:
            config["localisation_kwargs"] = full_config.pop("lkwargs")
        return config

    async def check(self, message: types.Message) -> bool:
        if message.content_type != "text":
            return False

        lang = get_lang()
        text = await f(self.text_variable, lang, **self.localisation_kwargs)

        if self.equal:
            return message.text == text
        else:
            return message.text.startswith(text)


class EndProcessButtonFilter(BoundFilter):

    key = "end_process_button"

    def __init__(self, end_process_button: bool = True):
        if type(end_process_button) is not bool:
            raise ValueError(f"Argument end_process_button must be typeof bool, not {type(end_process_button)}")
        self.end_process_button = end_process_button

    async def check(self, message: types.Message) -> bool | dict:
        lang = get_lang()
        text = await f("end process with icon", lang)
        is_end_process_button = message.text == text
        if is_end_process_button != self.end_process_button:
            return False

        return dict(end_process=is_end_process_button)


class SaveButtonFilter(BoundFilter):

    key = "save_button"

    def __init__(self, save_button: bool):

        self.save_button = bool(save_button)

    async def check(self, message: types.Message) -> bool | dict:
        lang = get_lang()

        is_save_button = message.text == await f("save button", lang)
        if is_save_button != self.save_button:
            return False

        return dict(save_button=is_save_button)


class SenderChatFilter(BoundFilter):

    key = "is_sender_chat"

    def __init__(self, is_sender_chat: bool):

        self.is_sender_chat = bool(is_sender_chat)

    async def check(self, message: types.Message) -> bool | dict:
        is_sender_chat = bool(message.sender_chat)

        if is_sender_chat is self.is_sender_chat is False:
            return True

        if is_sender_chat is not self.is_sender_chat:
            return False

        return dict(sender_chat=message.sender_chat)


class DeepLinkFilter(BoundFilter):

    key = "deep_link"

    def __init__(self, deep_link: str):

        self.deep_link = deep_link

    async def check(self, message: types.Message) -> bool | dict:
        if message.content_type != "text":
            return False

        if not message.is_command():
            return False

        if not message.text.startswith("/start"):
            return False

        data = ctx_data.get()

        compressed_link = data.get("compressed_link")
        if compressed_link:
            data_str = compressed_link.payload
        else:
            data_str = message.get_args()

        if data_str is None:
            return False

        mode, data = parse_deep_link_data(data_str)
        if mode != self.deep_link:
            return False

        return dict(mode=mode, deep_link_data=data)


class WebAppDataFilter(BoundFilter):

    key = "web_app_mode"

    def __init__(self, web_app_mode: str):
        if not isinstance(web_app_mode, str):
            raise ValueError(f"argument web_app_mode must be typeof str, not {type(web_app_mode)}")

        self.mode = web_app_mode

    async def check(self, message: types.Message) -> bool | dict:
        web_app_data = message.web_app_data
        if not web_app_data:
            return False

        data = web_app_data.data

        try:
            data = json.loads(data)
        except Exception as e:
            logger = logging.getLogger()
            logger.error(e, exc_info=True)
            return False

        mode = data.get("mode")

        if mode != self.mode:
            return False

        return {"web_app_data": data}
