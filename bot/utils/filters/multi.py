from aiogram import types
from aiogram.dispatcher.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>

from utils.text import f
from .helpers import get_lang


class CancelButtonFilter(BoundFilter):

    key = "cancel_button"

    def __init__(self, cancel_button: bool = True):
        if type(cancel_button) is not bool:
            raise ValueError(f"Argument cancel_button must be typeof bool, not {type(cancel_button)}")

        self.cancel_button = cancel_button

    async def check(self, obj: types.Message | types.CallbackQuery) -> bool | dict:
        if type(obj) is types.Message:
            lang = get_lang()
            text = await f("action cancel button", lang)
            is_cancel_button = obj.text == text

        elif type(obj) is types.CallbackQuery:
            is_cancel_button = obj.data == "cancel"

        else:
            raise ValueError(f"CancelButtonFilter supports only types.Message or types.CallbackQuery not {type(obj)}")

        if is_cancel_button != self.cancel_button:
            return False

        return dict(cancel=is_cancel_button)
