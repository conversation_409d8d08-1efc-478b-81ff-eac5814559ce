from typing import Dict, Any, List

from aiogram import types
from aiogram.dispatcher.filters import Filter, BoundFilter
from aiogram.dispatcher.handler import ctx_data


class BaseButtonFilter(BoundFilter):

    button_mode: str

    def __init__(self, **kwargs):
        setattr(self, self.key, kwargs.get(self.key))

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__()

        if not hasattr(cls, "button_mode"):
            raise TypeError("All child classes must implement \"button_mode\"")

        # устанавливаем key для всех переопределённых методов
        setattr(cls, "key", f"{cls.button_mode}_button")

    async def check(self, callback_query: types.CallbackQuery) -> bool:
        data = ctx_data.get()
        mode = data.get("mode")
        return mode == self.button_mode


class SkipButtonFilter(BaseButtonFilter):

    button_mode = "skip"


class NextButtonFilter(BaseButtonFilter):

    button_mode = "next"


class PreviousButtonFilter(BaseButtonFilter):

    button_mode = "previous"


class YesButtonFilter(BaseButtonFilter):

    button_mode = "yes"


class NoButtonFilter(BaseButtonFilter):

    button_mode = "no"


NOT_EMPTY_CALLBACK_VALUE = "__not_empty__"

EMPTY_CALLBACK_VALUE = "__empty__"

NOT_IN_CALLBACK_DATA_VALUE = "__not_in_data__"


class CallbackModeFilter(Filter):

    def __init__(
            self,
            callback_mode: str | List[str] = None,
            callback_mode_starts: str | List[str] = None,
            callback_keys: Dict[str, Any] = None,
    ):
        if callback_mode and callback_mode_starts:
            raise ValueError("Only one of callback_mode and callback_mode_starts can be specified")

        if callback_mode is not None and type(callback_mode) is not list:
            callback_mode = [callback_mode]

        if callback_mode_starts is not None and type(callback_mode_starts) is not list:
            callback_mode_starts = [callback_mode_starts]

        if callback_keys is None:
            callback_keys = dict()

        self.callback_modes = callback_mode
        self.callback_mode_starts = callback_mode_starts
        self.callback_keys = callback_keys

    @classmethod
    def validate(cls, full_config: Dict[str, Any]) -> Dict[str, Any] | None:
        """
                Validator for filters factory

                From filters factory this filter can be registered with arguments:

                 - ``callback_mode`` (will be passed as ``callback_mode``)
                 - ``callback_mode_starts`` (will be passed as ``callback_mode_starts``)
                 -``callback_keys`` (will be passed as ``callback_keys``)

                :param full_config:
                :return: config or empty dict
        """

        config = dict()
        if "callback_mode" in full_config:
            config["callback_mode"] = full_config.pop("callback_mode")
        if "callback_mode_starts" in full_config:
            config["callback_mode_starts"] = full_config.pop("callback_mode_starts")
        if "callback_keys" in full_config:
            config["callback_keys"] = full_config.pop("callback_keys")
        return config

    async def check(self, query: types.CallbackQuery | types.ShippingQuery | types.PreCheckoutQuery):
        data = ctx_data.get()
        mode: str = data.get("mode")
        callback_data: Dict[str, Any] = data.get("callback_data")

        if self.callback_modes is not None and mode not in self.callback_modes:
            return False

        if self.callback_mode_starts:

            for callback_mode in self.callback_mode_starts:
                if mode.startswith(callback_mode):
                    break
            else:
                return False

        for fileter_key, filter_value in self.callback_keys.items():
            if fileter_key not in callback_data and filter_value == NOT_IN_CALLBACK_DATA_VALUE:
                continue

            callback_value = callback_data.get(fileter_key, None)

            if callback_value and filter_value == NOT_EMPTY_CALLBACK_VALUE:
                continue

            if callback_value is None and filter_value == EMPTY_CALLBACK_VALUE:
                continue

            if callback_value != filter_value:
                return False

        return {"mode": mode, "callback_data": callback_data}


__all__ = [
    "BaseButtonFilter",
    "SkipButtonFilter",
    "NextButtonFilter",
    "PreviousButtonFilter",
    "YesButtonFilter",
    "NoButtonFilter",
    "CallbackModeFilter",
    "NOT_EMPTY_CALLBACK_VALUE",
    "EMPTY_CALLBACK_VALUE",
    "NOT_IN_CALLBACK_DATA_VALUE",
]
