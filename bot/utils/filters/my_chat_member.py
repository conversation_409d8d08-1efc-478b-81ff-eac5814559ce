from aiogram import types
from aiogram.dispatcher.filters import BoundFilter


class BotBLockedFilter(BoundFilter):

    key = "is_bot_blocked"

    def __init__(self, is_bot_blocked: bool):
        self.is_bot_blocked = is_bot_blocked

    async def check(self, update: types.ChatMemberUpdated) -> bool:
        if update.chat.type != "private":
            return False

        if update.old_chat_member.status != "member":
            return False

        if update.new_chat_member.status != "kicked":
            return False

        return True


class BotKickedFilter(BoundFilter):

    key = "is_bot_kicked"

    def __init__(self, is_bot_kicked: bool):
        self.is_bot_kicked = is_bot_kicked

    async def check(self, update: types.ChatMemberUpdated) -> bool:
        if update.chat.type not in ("group", "supergroup", "channel"):
            return False

        if update.old_chat_member.status not in ("member", "administrator"):
            return False

        if update.new_chat_member.status not in ("left", "kicked", "banned"):
            return False

        return True
