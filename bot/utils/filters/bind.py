from aiogram import Dispatcher

from .message import Localisation<PERSON>ilter, EndProcessButtonFilter, DeepLinkFilter
from .message import Sender<PERSON><PERSON><PERSON><PERSON><PERSON>, SaveButtonFilter, WebAppDataFilter
from .multi import CancelButtonFilter
from .callback import *
from .errors import ExceptionTextFilter
from .my_chat_member import Bo<PERSON><PERSON>ocked<PERSON>ilt<PERSON>, Bot<PERSON><PERSON><PERSON>ilter


def bind_general_filters(dp: Dispatcher):
    dp.bind_filter(LocalisationFilter, event_handlers=[dp.message_handlers])
    dp.bind_filter(EndProcessButtonFilter, event_handlers=[dp.message_handlers])
    dp.bind_filter(DeepLinkFilter, event_handlers=[dp.message_handlers])
    dp.bind_filter(SenderChatFilter, event_handlers=[dp.message_handlers])
    dp.bind_filter(SaveButtonFilter, event_handlers=[dp.message_handlers])
    dp.bind_filter(WebAppDataFilter, event_handlers=[dp.message_handlers])

    dp.bind_filter(CancelButtonFilter, event_handlers=[dp.message_handlers, dp.callback_query_handlers])

    dp.bind_filter(SkipButtonFilter, event_handlers=[dp.callback_query_handlers])
    dp.bind_filter(NextButtonFilter, event_handlers=[dp.callback_query_handlers])
    dp.bind_filter(PreviousButtonFilter, event_handlers=[dp.callback_query_handlers])
    dp.bind_filter(YesButtonFilter, event_handlers=[dp.callback_query_handlers])
    dp.bind_filter(NoButtonFilter, event_handlers=[dp.callback_query_handlers])

    dp.bind_filter(
        CallbackModeFilter,
        event_handlers=[
            dp.callback_query_handlers,
            dp.pre_checkout_query_handlers,
            dp.shipping_query_handlers,
        ]
    )

    dp.bind_filter(BotBLockedFilter, event_handlers=[dp.my_chat_member_handlers])
    dp.bind_filter(BotKickedFilter, event_handlers=[dp.my_chat_member_handlers])

    dp.bind_filter(ExceptionTextFilter, event_handlers=[dp.errors_handlers])


__all__ = [
    "bind_general_filters",
]
