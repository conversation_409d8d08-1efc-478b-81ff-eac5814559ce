from aiogram import types
from db.models import Channel, ClientBot


def not_private_chat_filter(message: types.Message):
    return message.chat.type != "private"


def not_bot_filter(update_or_message_or_callback: types.ChatMemberUpdated | types.Message | types.CallbackQuery):
    return not update_or_message_or_callback.from_user.is_bot


def is_joined_group(update: types.ChatMemberUpdated):
    return update.old_chat_member.status in ("left", "kicked", "banned") and \
        update.new_chat_member.status == "member" and \
        update.chat.type in ("group", "supergroup", "channel")


def is_left_group(update: types.ChatMemberUpdated):
    return update.old_chat_member.status == "member" and \
        update.new_chat_member.status in ("left", "kicked", "banned") and \
        update.chat.type in ("group", "supergroup", "channel")


def group_message_filter(message: types.Message):
    return message.chat.type == "supergroup" and not message.left_chat_member and not message.new_chat_members


async def filter_chat_menu(message: types.Message):
    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=message.chat.id, bot_id=bot_from_db.id)

    button_text = await channel.get_menu_buttons(message.text)
    if button_text:
        return True
    return False
