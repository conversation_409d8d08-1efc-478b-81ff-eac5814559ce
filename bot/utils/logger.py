import logging
import os

from aiogram import types
from psutils.logger import setup_logger as psutils_setup_logger

from config import CLIENT_BOT, FRIENDLY_BOT, LOGS_FOLDER
from utils.helpers import get_running_file_name

old_factory = logging.getLogRecordFactory()


def record_factory(*args, **kwargs):
    record = old_factory(*args, **kwargs)

    if get_running_file_name() in (
            CLIENT_BOT,
            FRIENDLY_BOT
    ):
        from db.models import ClientBot

        record.botid = ClientBot.get_bot_id_for_logging()

    user = types.User.get_current()
    record.userchatid = user.id if user else "notset"

    chat = types.Chat.get_current()
    record.chatid = chat.id if chat else "notset"
    return record


logging.setLogRecordFactory(record_factory)


def syslog_formatter():
    return logging.Formatter(
        "7loc-unhandled-logs - %(asctime)s - %(name)s - %(module)s - %(levelname)s - "
        "%(message)s"
    )


def setup_debug_loger(log_dir: str):
    psutils_setup_logger(
        name='debugger',
        log_dir=os.path.join(LOGS_FOLDER, log_dir),
        file_name="debug.log",
        level=logging.DEBUG,
        max_bytes=1048576,  # 1Mb
        backup_count=10,
        syslog_handler=True,
        handlers_formatters={
            "syslog": syslog_formatter,
        },
    )


def setup_warnings_loger(log_dir: str):
    return psutils_setup_logger(
        name='warnings',
        log_dir=os.path.join(LOGS_FOLDER, log_dir),
        file_name="warnings.log",
        level=logging.WARNING,
    )


def setup_user_loger(log_dir: str):
    return psutils_setup_logger(
        name='user',
        log_dir=os.path.join(LOGS_FOLDER, log_dir),
        file_name="user.log",
        level=logging.DEBUG,
        max_bytes=50000,
        backup_count=5,
    )


def setup_errors_logger(log_dir: str):
    return psutils_setup_logger(
        log_dir=os.path.join(LOGS_FOLDER, log_dir),
        file_name="errors.log",
        level=logging.WARNING,
        max_bytes=500000,
        backup_count=5,
        syslog_handler=True,
        handlers_levels={
            "stream": logging.WARNING,
            "file": logging.ERROR,
        },
        handlers_formatters={
            "syslog": syslog_formatter,
        }
    )


def setup_logger(log_dir: str):
    if not os.path.exists(LOGS_FOLDER):
        os.makedirs(LOGS_FOLDER)

    logs_dir = os.path.join(LOGS_FOLDER, log_dir)

    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)

    def default_formatter():
        return logging.Formatter(
            "%(asctime)s - %(name)s - %(module)s - %(levelname)s - %(message)s",
            "%d.%m.%Y %H:%M:%S"
        )

    psutils_setup_logger(
        name="debugger",
        log_dir=logs_dir,
        file_name="debug.log",
        formatter=default_formatter,
        level=logging.DEBUG,
        max_bytes=5_000_000,
        backup_count=10,
        syslog_handler=True,
        handlers_formatters={
            "syslog": syslog_formatter,
        },
    )

    psutils_setup_logger(
        name="info",
        log_dir=logs_dir,
        file_name="info.log",
        formatter=default_formatter,
        level=logging.INFO,
        max_bytes=5_000_000,
        backup_count=10,
        syslog_handler=True,
        handlers_formatters={
            "syslog": syslog_formatter,
        },
    )

    psutils_setup_logger(
        log_dir=logs_dir,
        file_name="errors.log",
        formatter=default_formatter,
        level=logging.WARNING,
        max_bytes=5_000_000,
        backup_count=10,
        syslog_handler=True,
        handlers_formatters={
            "syslog": syslog_formatter,
        },
    )
