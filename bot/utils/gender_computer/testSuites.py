# This Python file uses the following encoding: UTF-8

"""Copyright 2012-2013
Eindhoven University of Technology
Bogdan <PERSON>

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU Lesser General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU Lesser General Public License for more details.

You should have received a copy of the GNU Lesser General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>."""


testSuite1 = [
    (u'adnam', 'Brazil'),
    (u'hash', 'UK'),
    (u'sep', 'Singapore'),
    (u'<PERSON>', None),
    (u'<PERSON>', 'Thailand'),
    (u'<PERSON>', None),
    (u'Mystere Man', 'Brazil'),
    (u'danny', None),
    (u'Tokyo Dan', 'Japan'),
    (u'Mik<PERSON>', 'Belarus'),
    (u'mi<PERSON><PERSON><PERSON>', None),
    (u'<PERSON>', 'USA'),
    (u'Aea', 'USA'),
    (u'<PERSON>', <PERSON>),
    (u'w35l3y', 'Brazil'),
    (u'Cin', 'USA'),
    (u'me22', None),
    (u'deadalus.ai', '<PERSON>'),
    (u'<PERSON>i Unnithan', 'USA'),
    (u'<PERSON>or <PERSON>shtain', '<PERSON>'),
    (u'<PERSON>ckner', <PERSON>),
    (u'<PERSON>', <PERSON>),
    (u'<PERSON>ss', 'India'),
    (u'<PERSON>rk<PERSON>', <PERSON>),
    (u'Dyer', None),
    (u'fazen', 'Italy'),
    (u'Tom', None),
    (u'Ben Benson', 'China'),
    (u'caveman', 'USA'),
    (u'DF.', None),
    (u'7r3y', 'USA'),
    (u'Kai', None),
    (u'dmitrig01', 'USA'),
    (u'Matan', None),
    (u'me_here', None),
    (u'vimma', None),
    (u'Helge V', None),
    (u'Dan', None),
    (u'echo', None),
    (u'Suso Guez', 'UK'),
    (u'Jan Hoefnagels', None),
    (u'stojance', None),
    (u'randy', None),
    (u'Meat', None),
    (u'Sam', None),
    (u'Randy Armstrong', None)
]

testSuite2 = [
    (u'Alexei Matrosov', 'Russia'),
    (u'Matrosov Alexei', 'Russia'),
    (u'abatishchev', 'Russia'),
    (u'SeniorDev', 'Russia'),
    (u'ebaranov', 'Russia'),
    (u'wimvds', 'Belgium'),
    (u'rubenvb', 'Belgium'),
    (u'davidverbeek', 'Belgium'),
    (u'Bogdan', None),
    (u'Bogdan Vasilescu', None),
    (u'Vasilescu Bogdan', None),
    (u'bogdanv', None),
    (u'vbogdan', None),
    (u'Cristi', None),
    (u'Cristi', 'Romania'),
    (u'Andrea', None),
    (u'Andrea', 'Italy'),
    (u'Andrea', 'Germany'),
    (u'Seva Alekseyev', 'USA'),
    (u'TheDarkIn', 'Canada'),
    (u'aix', None),
    (u'Gabi Purcaru', 'Romania'),
    (u'Eugène Savitzkaya', 'Belgium'),
    (u'Török Gábor', 'Hungary'),
    (u'Gábor Török', 'Hungary'),
    (u'greg0ire', 'France'),
    (u'dnagirl', 'Canada'),
    (u'Mr. Shiny and New 安宇', 'Canada'),
    (u'Dmytrii Nagirniak', 'Australia'),
    (u'scottfrazer', None),
    (u'daveb', 'UK'),
    (u'Matěj Zábský', 'Czech Republic'),
    (u'Michał Niklas', 'Poland'),
    (u'yairchu', 'Israel'),
    (u'jasondavis', 'USA'),
    (u'KiwiBastard', 'New Zealand'),
    (u'IttayD', 'Twitter'),
    (u'MadKeithV', 'Belgium'),
    (u'zvolkov', 'USA'),
    (u'Ollie Acheson', 'USA'),
    (u'Ainsley Pereira', 'UK'),
    (u'Ashley Maher', 'Australia'),
    (u'Chi', 'Russia'),
    (u'Christian', 'Ukraine'),
    (u'claudian', 'India'),
    (u'Damas Ally', 'Tanzania'),
    (u'Didine', 'Morocco'),
    (u'Djun Kim', 'Canada'),
    (u'ekes', 'The Netherlands'),
    (u'Gaele Strootman', 'The Netherlands'),
    (u'Jamie Holly', 'USA'),
    (u'Jean Gazis', 'USA'),
    (u'Laurence Mercer', 'UK'),
    (u'Nesta Campbell', 'Jamaica'),
    (u'Piermaria Maraziti', 'Italy'),
    (u'Raven Brooks', 'USA'),
    (u'Robin Sentell', 'USA'),
    (u'TheClue', 'Italy'),
    (u'alumb', 'Canada'),
    (u'ESRogs', None),
    (u'Flubba', 'UK'),
    (u'NilObject', 'USA'),
    (u'Valters Vingolds', 'USA'),
    (u'Olly', 'UK'),
    (u'Nate Smith', 'Canada'),
    (u'Dima Malenko', 'Ukraine'),
]
