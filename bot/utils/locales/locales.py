import hashlib
import json
import logging
import os
from collections import defaultdict

import aiofiles
from psutils.decorators import sync_to_async
from psutils.google_sheets import AsyncGoogleSheetsClient

from config import PATH_TO_GOOGLE_CREDS_JSON, TEXT_VARIABLES
from utils.google import limiter
from utils.locales.schemas import GetLocaleResult, LocaleData
from utils.platform_admins import send_message_to_platform_admins


class Locales:
    VERSION = "1.0.0"
    LOCALES = {
        "crm": "CRM",
        "my-7loc": "My 7Loc"
    }

    def __init__(self, directory: str = "locales"):
        self.directory: str = directory
        self._client: AsyncGoogleSheetsClient | None = None

    @property
    async def client(self):
        if not self._client:
            self._client = AsyncGoogleSheetsClient(
                TEXT_VARIABLES, limiter=limiter,
                creds_file_path=PATH_TO_GOOGLE_CREDS_JSON,
            )
            await self._client.load_document()
        return self._client

    @sync_to_async
    def get_and_check_locale_path(self, locale: str) -> tuple[str, bool]:
        path = os.path.join(self.directory, f"{locale}.json")
        os.makedirs(self.directory, exist_ok=True)
        return path, os.path.exists(path)

    @staticmethod
    def compute_checksum(data: dict):
        json_data = json.dumps(data, sort_keys=True)
        return hashlib.sha256(json_data.encode()).hexdigest()

    def check_locale(self, locale: str, no_error: bool = False):
        if locale not in self.LOCALES:
            if no_error:
                return None
            raise ValueError(f"Unknown locale: {locale}")
        return self.LOCALES[locale]

    async def update(self, locale: str) -> LocaleData:
        client = await self.client
        worksheet = await client.get_worksheet_by_title_from_cache(self.check_locale(locale))
        if not worksheet:
            worksheet = await client.get_worksheet_by_title(locale)
            if not worksheet:
                raise ValueError("Worksheet not found")
        client.use_worksheet(worksheet)

        header: list[str]
        rows: list[list[str]]
        variable: str
        values: list[str]

        header, *rows = await client.get_sheet_data()
        _, *langs = header

        langs_data: dict[str, dict[str, str]] = defaultdict(dict)
        variables: set[str] = set()

        for row in rows:
            variable, *values = row
            variables.add(variable)

            for lang, value in zip(langs, values):
                langs_data[lang][variable] = value

        data = {
            "version": self.VERSION,
            "variables": list(sorted(variables)),
            "available_langs": langs,
            "data": langs_data
        }

        checksum = self.compute_checksum(data)
        data["checksum"] = checksum

        file_path, _ = await self.get_and_check_locale_path(locale)
        async with aiofiles.open(file_path, "w") as file:
            await file.write(json.dumps(data))

        return LocaleData(**data)

    async def update_all(self):
        for locale in self.LOCALES:
            await self.update(locale)

    async def load_data(self, locale: str) -> LocaleData | None:
        file_path, is_exists = await self.get_and_check_locale_path(locale)
        if not is_exists:
            return None

        try:
            async with aiofiles.open(file_path, "r") as file:
                return LocaleData.parse_raw(await file.read())
        except Exception as e:
            await send_message_to_platform_admins(f"An error occurred while reading locale {locale} data")
            logging.error(e, exc_info=True)
            return None

    async def get(self, locale: str, current_checksum: str | None = None) -> GetLocaleResult:
        self.check_locale(locale)
        data = await self.load_data(locale)
        if not data or data.version != self.VERSION:
            data = await self.update(locale)

        if data.checksum == current_checksum:
            return GetLocaleResult(is_modified=False)
        return GetLocaleResult(is_modified=True, new_data=data)
