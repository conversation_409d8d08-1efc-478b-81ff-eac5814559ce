import base32_crockford

from .functions import encode_value, decode_value


class ShortTokenCreator:

    def __init__(self, shift: int, secret: int):
        self.shift = shift
        self.secret = secret

    def make_token(self, value: int) -> int:
        encoded_value = encode_value(value, self.shift, self.secret)
        return base32_crockford.encode(encoded_value).lower()

    def decode_token(self, token: str) -> int:
        decoded_int = base32_crockford.decode(token)
        return decode_value(decoded_int, self.shift, self.secret)
