import uuid


def make_secret() -> int:
    return uuid.uuid4().int


def encode_value(value: int, shift: int, secret: int):
    if not all(map(lambda x: isinstance(x, int), (value, shift, secret))):
        raise ValueError("Value shift and secret must be integers")

    mask = (1 << shift) - 1
    mapping = range(shift)

    value_secreted = value ^ secret
    value_encoded = 0
    for i, b in enumerate(reversed(mapping)):
        if (value_secreted & mask) & (1 << i):
            value_encoded |= (1 << b)
    return (value_secreted & ~mask) | value_encoded


def decode_value(value: int, shift: int, secret: int):
    if not all(map(lambda x: isinstance(x, int), (value, shift, secret))):
        raise ValueError("Value shift and secret must be integers")

    mask = (1 << shift) - 1
    mapping = range(shift)

    value_decoded = 0
    for i, b in enumerate(reversed(mapping)):
        if (value & mask) & (1 << b):
            value_decoded |= (1 << i)
    value_secreted = (value & ~mask) | value_decoded
    return value_secreted ^ secret
