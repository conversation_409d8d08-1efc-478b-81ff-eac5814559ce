import asyncio
import json
import logging

from aiokafka import AIOKafkaConsumer, ConsumerRecord

from config import HTTP_LOG_KAFKA_TOPIC, KAFKA_SERVER


async def main():
    consumer = AIOKafkaConsumer(
        HTTP_LOG_KAFKA_TOPIC,
        group_id=HTTP_LOG_KAFKA_TOPIC,
        bootstrap_servers=KAFKA_SERVER,
    )
    await consumer.start()
    try:
        msg: ConsumerRecord
        async for msg in consumer:
            try:
                value = msg.value.decode("utf-8")
                print(json.dumps(json.loads(value), indent=4))
            except Exception as e:
                logging.error(f"Error while printing log: {str(e)}", exc_info=True)
    finally:
        await consumer.stop()


if __name__ == "__main__":
    asyncio.run(main())
