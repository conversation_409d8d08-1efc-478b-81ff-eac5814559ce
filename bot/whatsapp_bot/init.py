import asyncio
import os

from aiowhatsapp import Dispatcher
from fastapi import FastAP<PERSON>
from psutils.fastapi.logging import setup_uvicorn_loggers
from psutils.local import setup_psutils_localisation

from api.exception_handlers import register_general_exception_handlers
from config import (
    DEBUG_WHATSAPP_WEBHOOK, LOGS_FOLDER, WHATSAPP_WEBHOOK_URL_TOKEN,
)
from core import messangers_adapters
from core.api.middlewares import SessionMiddleware
from core.chat.filters.aiowhatsapp import bind_chat_filters
from core.kafka.producer import producer
from db import DBSession
from db.models import ClientBot
from loggers import J<PERSON>NLogger
from utils.logger import setup_logger, syslog_formatter
from utils.redefined_classes.aiowhatsapp.my_storages import MyRedisStorage
from .api import routers
from .bot.filters import ReplyQueryModeFilter
from .bot.middlewares import setup_middlewares
from .bot.modules import register_handlers
from .bot.webhook_handlers import WhatsappAppIdWebhookHandler
from .helpers import setup_bot

storage = MyRedisStorage()
dp = Dispatcher(
    storage=storage,
    additional_state_cls=messangers_adapters.state.State,
    additional_states_group_cls=messangers_adapters.state.StatesGroup,
)

setup_middlewares(dp)

dp.bind_filter(
    ReplyQueryModeFilter, event_handlers=[
        dp.reply_query_handlers,
        dp.list_reply_query_handlers,
    ]
)
bind_chat_filters(dp)

register_handlers(dp)


def debug(data: dict):
    if DEBUG_WHATSAPP_WEBHOOK:
        logger = JSONLogger("whatsapp.update")

        try:
            display_phone_number = (
                data["entry"][0]
                ["changes"][0]
                ["value"]["metadata"]
                ["display_phone_number"]
            )
        except:
            display_phone_number = "Unknown phone number"
        logger.debug(f"An update received from {display_phone_number}", data)


webhook = WhatsappAppIdWebhookHandler(
    dp, WHATSAPP_WEBHOOK_URL_TOKEN,
    debug_update=debug
)

app = FastAPI()
app.dp = dp

SessionMiddleware.setup(app)

app.include_router(routers.router)
webhook.configure_app(app)

register_general_exception_handlers(app)


@app.on_event("startup")
async def on_startup():
    await setup_psutils_localisation()
    await producer.initialise()

    setup_logger("whatsapp")
    setup_uvicorn_loggers(
        os.path.join(LOGS_FOLDER, "whatsapp"), "7loc.log", errors_handlers_formatters={
            "syslog": syslog_formatter
        }
    )
    loop = asyncio.get_event_loop()

    with DBSession():
        bots: list[ClientBot] = await ClientBot.get_list(bot_type="whatsapp")

    set_webhooks_delay = 1

    loop.call_later(
        set_webhooks_delay,
        asyncio.ensure_future, asyncio.gather(
            *[
                setup_bot(dp, bot)
                for bot in bots
            ]
        )
    )
