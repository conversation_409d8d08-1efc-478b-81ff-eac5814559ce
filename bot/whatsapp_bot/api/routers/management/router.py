import os

from aiowhatsapp import WhatsappBot
from fastapi import APIRouter, HTTPException
from starlette.requests import Request

from config import STATIC_DB, WHATSAPP_WEBHOOK_URL_TOKEN, WHATSAPP_WEBHOOK_URL
from db import DBSession
from db.models import ClientBot
from . import schemas

router = APIRouter(
    prefix="/management",
    tags=["management"],
)


@router.post("/botCreated")
async def bot_created(request: Request, data: schemas.BotCreated):
    with DBSession():
        bot = await ClientBot.get(data.bot_id)
    if not bot:
        raise HTTPException(404, "Bot not found")

    if bot.bot_type != "whatsapp":
        raise HTTPException(403, "Cannot start not whatsapp bot")

    whatsapp_bot = WhatsappBot(
        bot.token,
        bot.whatsapp_from,
        bot.whatsapp_app_id,
        bot.whatsapp_app_secret,
        static_dir=os.path.join(STATIC_DB, "whatsapp"),
    )
    request.app.dp.add_bot(whatsapp_bot)
    await whatsapp_bot.set_webhook(
        WHATSAPP_WEBHOOK_URL.format(app_id=bot.whatsapp_app_id),
        WHATSAPP_WEBHOOK_URL_TOKEN,
        fields=["messages", "phone_number_name_update", "message_template_status_update"]
    )

    return {"ok": True}


@router.post("/botDeleted")
async def bot_deleted(request: Request, data: schemas.BotDeleted):
    if data.from_ not in request.app.dp.bots:
        raise HTTPException(404, "Bot not found")

    await request.app.dp.bots[data.from_].delete_webhook()
    request.app.dp.remove_bot(data.from_)
    return {"ok": True}
