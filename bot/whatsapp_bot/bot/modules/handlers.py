from aiowhatsapp import Dispatcher

from core.bot.custom_menu_buttons.handlers import register_custom_menu_buttons_handlers
from core.bot.handlers import register_bot_buttons_handlers
from core.crm_ticket.handlers import register_crm_ticket_button_handlers
from core.ewallet.payment.handlers import \
    register_ewallet_payment_buttons_handlers
from core.ewallet.payment.handlers.commands_handlers import \
    register_ewallet_payment_commands_handlers
from core.ewallet_users.handlers.commands_handlers import \
    register_ewallet_user_commands_handlers
from core.external_login.handlers import *
from core.fast_pay.handlers import (
    register_fast_pay_deep_link_handlers,
    register_fast_pay_message_handlers, register_payment_method_button_handlers,
)
from core.friend.handlers import *
from core.handlers import register_general_exception_handlers
from core.menu_in_store.handlers.buttons_handlers import \
    register_menu_in_store_buttons_handlers
# from core.topup_ewallet.handlers.button_handlers import \
#     register_topup_ewallet_button_handlers
from core.topup_ewallet.handlers.message_handlers import \
    register_topup_ewallet_message_handlers
from core.user.agreement_processor.buttons_handlers import \
    setup_wa_handlers as setup_agreement_handlers
from core.user.agreement_processor.message_handlers import \
    setup_wa_message_handlers as setup_agreement_message_handlers
from .chat.handlers import *
from .external_coupon.handlers import *
from .incust_referral.handlers import *
from .integration.payments.handlers import register_ext_invoice_pay_deep_links_handlers
from .main.handlers import *
from .menu_in_store.handlers import *
from .store.handlers import *


def register_handlers(dp: Dispatcher):
    register_main_request_welcome_message_handlers(dp)
    register_main_request_message_template_status_handlers(dp)

    register_custom_menu_buttons_handlers(dp)

    register_external_coupon_deep_links_handlers(dp)

    register_ewallet_payment_buttons_handlers(dp)

    register_ewallet_payment_commands_handlers(dp)
    register_ewallet_user_commands_handlers(dp)

    register_ext_invoice_pay_deep_links_handlers(dp)
    register_menu_in_store_deep_links_handlers(dp)
    register_fast_pay_deep_link_handlers(dp)
    register_incust_referral_deep_links_handlers(dp)

    register_add_friend_commands_handlers(dp)

    register_external_login_commands_handlers(dp)

    register_chat_commands_handlers(dp)

    register_main_commands_handlers(dp)

    setup_agreement_message_handlers(dp)

    register_chat_message_handlers(dp)

    register_fast_pay_message_handlers(dp)

    register_topup_ewallet_message_handlers(dp)

    register_main_messages_handlers(dp)

    # register_main_message_status_handlers(dp)

    register_bot_buttons_handlers(dp)

    register_store_list_and_reply_handlers(dp)
    register_main_list_reply_handlers(dp)

    register_payment_method_button_handlers(dp)

    # register_topup_ewallet_button_handlers(dp)

    register_crm_ticket_button_handlers(dp)
    register_external_login_button_handlers(dp)
    register_menu_in_store_buttons_handlers(dp)
    register_friend_buttons_handlers(dp)
    setup_agreement_handlers(dp)

    register_chat_reply_query_handlers(dp)
    register_external_coupon_reply_query_handlers(dp)
    register_incust_referral_reply_query_handlers(dp)
    register_main_reply_query_handlers(dp)

    register_general_exception_handlers(dp)
