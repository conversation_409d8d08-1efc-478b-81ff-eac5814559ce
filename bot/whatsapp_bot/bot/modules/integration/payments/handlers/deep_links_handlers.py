import base64
import logging

from aiowhatsapp import Dispatcher, types

from core.invoice.bot_funcs import send_invoice_from_deep_link
from core.invoice.deep_links import ExternalInvoiceDeepLink
from db.models import ClientBot

logger = logging.getLogger("debugger.integration")


async def ext_invoice_pay_deep_link_handler(
        message: types.Message,
        data: ExternalInvoiceDeepLink,
        bot: ClientBot,
        lang: str,
):

    invoice_decoded_uuid = data.invoice_uuid_id
    invoice_uuid = base64.b64decode(invoice_decoded_uuid).decode("utf-8")
    await send_invoice_from_deep_link(message, invoice_uuid, bot, lang)


def register_ext_invoice_pay_deep_links_handlers(dp: Dispatcher):
    dp.register_message_handler(
        ext_invoice_pay_deep_link_handler,
        ExternalInvoiceDeepLink.get_filter(return_field_name="data"),
        state="*",
    )
