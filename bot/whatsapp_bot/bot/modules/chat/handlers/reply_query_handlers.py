from aiowhatsapp import Dispatcher, types
from aiowhatsapp.dispatcher.storage import FSMContext

from core.chat import user_to_chat
from core.chat.handlers.united_handlers import handle_connect_button
from core.chat.virtual_manager.handles import register_vm_buttons_handlers
from db.crud import delete_currently_running_vm_chats
from db.models import ClientBot, User
from schemas import ChatTypeEnum
from utils.text import f


async def connect_button_handler(
        _: types.ReplyQuery, state: FSMContext,
        reply_data: dict, user: User, lang: str,
):
    await handle_connect_button(reply_data, state, user, lang)


async def chat_button_handler(
        reply_query: types.ReplyQuery,
        state: FSMContext,
        user: User,
        bot: ClientBot,
        reply_data: dict,
        lang: str
):
    group_id = reply_data.get("group_id") or bot.group_id
    menu_in_store_id = reply_data.get("menu_in_store_id")

    if not group_id:
        await reply_query.answer(await f("error", lang))
        raise ValueError("group_id must be specified in callback_data")

    if menu_in_store_id:
        await state.update_data(menu_in_store_id=menu_in_store_id)
    await delete_currently_running_vm_chats(user.id, bot.id)
    await user_to_chat(ChatTypeEnum.USER_WITH_GROUP, reply_query, group_id=group_id)


def register_chat_reply_query_handlers(dp: Dispatcher):
    dp.register_list_and_reply_query_handler(
        chat_button_handler,
        reply_mode="chat",
        state="*",
    )

    dp.register_list_and_reply_query_handler(
        connect_button_handler,
        reply_mode="connect",
        state="*",
    )

    register_vm_buttons_handlers(dp)
