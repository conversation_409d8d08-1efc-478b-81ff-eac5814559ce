from aiogram import Dispatcher
from aiowhatsapp import types
from aiowhatsapp.dispatcher import FSMContext

from core.chat import user_to_chat
from core.chat.chat import ChatDeepLink
from core.chat.virtual_manager.deep_link import V<PERSON>eepLink
from core.chat.virtual_manager.functions import start_virtual_manager_chat
from db import crud
from db.crud import delete_currently_running_vm_chats
from db.models import ClientBot, Group, User
from schemas import ChatTypeEnum
from utils.text import f
from whatsapp_bot.bot.modules.main.functions import send_hello_message


async def cmd_chat_deep_link(
        message: types.Message,
        chat_data: ChatDeepLink,
        user: User,
        lang: str
):
    bot = await ClientBot.get_current()

    group_id = chat_data.group_id or bot.group_id
    group = await Group.get(group_id)
    if not group:
        return await message.answer(
            await f(
                "group not found error",
                lang,
                profile_id=group_id
            ),
        )

    await delete_currently_running_vm_chats(user.id, bot.id)
    await user_to_chat(ChatTypeEnum.USER_WITH_GROUP, message, group_id=group.id)


async def cmd_start_vm_deep_link(
        message: types.Message,
        state: FSMContext,
        bot: ClientBot,
        vm_data: VMDeepLink,
        user: User,
        lang: str
):
    await state.finish()

    vm = await crud.get_vm_by_id_or_name_id(vm_data.vm_id)
    if vm.bot_hello_message_enabled:
        await send_hello_message(message, user, bot, lang, message_key="welcome text")
    await start_virtual_manager_chat(
        user, vm,
        vm_data.group_id,
        bot,
    )


def register_chat_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cmd_chat_deep_link,
        ChatDeepLink.get_filter(return_field_name="chat_data"),
        state="*",
    )

    dp.register_message_handler(
        cmd_start_vm_deep_link,
        VMDeepLink.get_filter(return_field_name="vm_data"),
        state="*",
    )
