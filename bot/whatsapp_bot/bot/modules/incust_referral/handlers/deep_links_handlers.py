import logging

from aiowhatsapp import Dispatcher, types
from aiowhatsapp.dispatcher.storage import FSMContext

from core.incust_referral.referral_processor import IncustReferralProcessor

from db.models import ClientBot, User

from whatsapp_bot.bot.deep_links import incust_referral_deep_link_no_store
from core.whatsapp.keyboards import get_wa_menu_keyboard

logger = logging.getLogger("debugger")


async def incust_referral_deep_link_handler(
    message: types.Message,
    state: FSMContext,
    deep_link_data: dict,
    bot: ClientBot,
    user: User, lang: str,
):
    referral_code = deep_link_data.get("referral_code")
    store_id = deep_link_data.get("store_id", None)
    logger.debug(f"{referral_code=}")
    keyboard = await get_wa_menu_keyboard(user, bot, lang)
    processor = IncustReferralProcessor(message, state, bot, user, lang, keyboard, referral_code, store_id=store_id)
    await processor.process()


def register_incust_referral_deep_links_handlers(dp: Dispatcher):
    dp.register_message_handler(
        incust_referral_deep_link_handler,
        deep_link=incust_referral_deep_link_no_store,
        state="*",
    )
