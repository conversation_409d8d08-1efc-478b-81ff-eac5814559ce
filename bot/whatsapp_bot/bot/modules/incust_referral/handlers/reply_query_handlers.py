import logging
from aiowhatsapp import types, Dispatcher
from aiowhatsapp.dispatcher.storage import FSMContext

from core.incust_referral.callback_data import IncustReferralCallbackData, IncustReferralShareMessage, \
    IncustReferralQrOrLinkCallbackData
from core.incust_referral.handlers import incust_referral_united_handler, get_share_and_earn_message_united_handler, \
    incust_referral_qr_or_link_united_handler

from db.models import ClientBot, User

from core.whatsapp.keyboards import get_wa_menu_keyboard


async def incust_referral_handler(
        reply_query: types.ReplyQuery,
        incust_referral: IncustReferralCallbackData,
        user: User, state: FSMContext,
        lang: str, bot: ClientBot,
):
    keyboard = await get_wa_menu_keyboard(user, bot, lang)
    return await incust_referral_united_handler(reply_query, state, user, bot, lang, keyboard, incust_referral)


async def get_share_and_earn_message_handler(
    reply_query: types.ReplyQuery,
    lang: str, bot: ClientBot,
    user: User, incust_share_msg: IncustReferralShareMessage,
):
    logging.error(f"*** INCUST SHARE MESSAGE {incust_share_msg}")
    return await get_share_and_earn_message_united_handler(
        reply_query, lang, bot, user, incust_share_msg
    )


async def incust_referral_qr_or_link_handler(
        reply_query: types.ReplyQuery,
        i_ref_qr_or_link: IncustReferralQrOrLinkCallbackData,
        user: User, state: FSMContext,
        lang: str, bot: ClientBot,
):
    return await incust_referral_qr_or_link_united_handler(
        reply_query, state, user, bot, lang, None, i_ref_qr_or_link
    )


def register_incust_referral_reply_query_handlers(dp: Dispatcher):
    dp.register_reply_query_handler(
        incust_referral_handler,
        IncustReferralCallbackData.get_filter(),
        state="*",
    )

    dp.register_reply_query_handler(
        get_share_and_earn_message_handler,
        IncustReferralShareMessage.get_filter(),
        state="*",
    )

    dp.register_reply_query_handler(
        incust_referral_qr_or_link_handler,
        IncustReferralQrOrLinkCallbackData.get_filter(),
        state="*",
    )
