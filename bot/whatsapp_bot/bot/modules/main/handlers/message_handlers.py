from aiowhatsapp import Dispatcher, types

from core.chat import user_to_chat
from core.chat.functions import message_from_user_to_group_handler
from db import crud
from db.models import ClientBot, User
from schemas import ChatT<PERSON><PERSON><PERSON>


async def unhandled_message_handler(
        message: types.Message, bot: ClientBot, user: User, **kwargs
):
    active_vmc = await crud.get_active_vmc(user.id, bot.id)
    if active_vmc:
        group_id = active_vmc.group_id
    else:
        group_id = bot.group_id

    chat = await user_to_chat(
        ChatTypeEnum.USER_WITH_GROUP, message,
        group_id=group_id,
    )

    await message_from_user_to_group_handler(
        message, bot=bot, chat=chat, user=user, **kwargs
    )


def register_main_messages_handlers(dp: Dispatcher):
    dp.register_message_handler(
        unhandled_message_handler, content_types=types.ContentType.ANY, state="*"
    )
