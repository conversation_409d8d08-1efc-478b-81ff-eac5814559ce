import logging

from aiowhatsapp import Dispatcher, types
from aiowhatsapp.dispatcher.storage import FSMContext

from client.main.incust_funcs import show_card
from core.bot.ewallet_handlers import ewallet_button_handler
from core.bot.profile.functions import send_wa_profile
from core.loyalty.customer_service import get_or_create_incust_customer
from core.loyalty.incust_api import incust
from core.referral.functions import share_and_earn
from core.wallet.functions import wallet_show
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db import crud
from db.models import Brand, ClientBot, ShortToken, User
from schemas import LoyaltySettingsData
from utils.text import f

debugger = logging.getLogger("debugger.whatsapp.listReplay")


async def profile_button_handler(
        list_reply: types.ListReplyQuery,
        state: FSMContext, bot: ClientBot,
        user: User, lang: str,
):
    await state.finish()
    await send_wa_profile(list_reply, bot, user, lang)


async def benefits_button_handler(
        list_reply: types.ListReplyQuery | types.ReplyQuery,
        state: FSMContext, bot: C<PERSON>B<PERSON>,
        user: User, lang: str,
):
    await state.finish()

    brand = await Brand.get_by_group(bot.group_id)

    # Отримуємо налаштування лояльності
    loyalty_settings = await crud.get_loyalty_settings_for_context(
        "brand",
        LoyaltySettingsData(brand_id=brand.id),
    )

    if not loyalty_settings:
        return await list_reply.answer(
            await f("loyalty not connected text", lang, brand_name=brand.name)
        )

    try:
        # Отримуємо або створюємо InCust клієнта
        incust_customer = await get_or_create_incust_customer(user, loyalty_settings, lang)

        if not incust_customer:
            return await list_reply.answer(
                await f('client bot nodata incust text', lang)
            )

        # Генеруємо тимчасовий код через новий API
        async with incust.term.TemporaryCodeApi(loyalty_settings, lang=lang) as api:
            result = await api.generate_temporary_code(
                user_id_value=user.incust_external_id,
                user_id_type="external-id",
                redeem_allowed=True
            )

        if not result:
            return await list_reply.answer(
                await f('client bot nodata incust text', lang)
            )

        await show_card(None, result, lang, incust_customer, user=user, brand=brand)
        return None
    except Exception as ex:
        logging.error(ex, exc_info=True)
        return await list_reply.answer(
            await f("incust loyalty error", lang)
        )


async def share_and_earn_button_handler(
        list_reply: types.ListReplyQuery,
        state: FSMContext, bot: ClientBot,
        user: User, lang: str,
):
    await state.finish()

    brand = await Brand.get_by_group(bot.group_id)
    await share_and_earn(user, brand, lang, bot)


async def scan_receipt_button_handler(
        list_reply: types.ListReplyQuery,
        state: FSMContext, bot: ClientBot,
        user: User, lang: str,
):
    await state.finish()

    brand = await crud.get_brand_by_group(bot.group_id)

    if brand:
        link = await brand.get_short_token_url(
            user, bot.id, lang, "shop", scan_receipt=1
        )
    else:
        short_token = await ShortToken.create(
            user_id=user.id,
            scopes=user.allowed_scopes_str,
            bot_id=bot.id if bot else None,
        )

        link = short_token.get_webapp_link("")

    return await list_reply.answer(
        await f("wa open scan receipt text", lang, link=link),
        keyboard=await get_wa_menu_keyboard(user, bot, lang)
    )


async def wallet_button_handler(
        list_reply: types.ListReplyQuery,
        state: FSMContext, bot: ClientBot,
        user: User, lang: str,
):
    await state.finish()

    brand = await Brand.get_by_group(bot.group_id)
    await wallet_show(user, brand, lang, list_reply, bot)


def register_main_list_reply_handlers(dp: Dispatcher):
    dp.register_list_reply_query_handler(
        profile_button_handler,
        reply_mode="profile",
        state="*",
    )

    dp.register_list_reply_query_handler(
        benefits_button_handler,
        reply_mode="benefits",
        state="*",
    )

    dp.register_list_reply_query_handler(
        share_and_earn_button_handler,
        reply_mode="share_and_earn",
        state="*",
    )

    dp.register_list_reply_query_handler(
        scan_receipt_button_handler,
        reply_mode="scan_receipt",
        state="*",
    )

    dp.register_list_reply_query_handler(
        wallet_button_handler,
        reply_mode="wallet",
        state="*",
    )

    dp.register_list_reply_query_handler(
        ewallet_button_handler,
        reply_mode="ewallet_account",
        state="*",
    )
