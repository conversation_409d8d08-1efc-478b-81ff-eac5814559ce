from aiowhatsapp import Dispatcher, types
from aiowhatsapp.dispatcher.storage import FSMContext

import exceptions
from core.bot.custom_menu_buttons.functions import process_custom_menu_button
from core.incust.callback_data import IncustMyCardCallbackData
from core.whatsapp.callback_data import (
    CustomMenuButtonCallbackData,
    TemplateReplyCallbackData,
)
from db.models import ClientBot, CustomMenuButton, User
from .list_reply_handlers import benefits_button_handler
from .template_quick_reply_handler import template_reply_handler


async def incust_my_card_handler(
        reply_query: types.ReplyQuery,
        user: User, state: FSMContext, lang: str,
        bot: ClientBot,
):
    await benefits_button_handler(reply_query, state, bot, user, lang)


async def custom_menu_button_handler(
        reply_query: types.ReplyQuery | types.ListReplyQuery,
        data: CustomMenuButtonCallbackData,
        state: FSMContext,
        user: User,
        bot: ClientBot,
):
    await state.finish()
    button = await CustomMenuButton.get(data.button_id)
    if not button:
        raise exceptions.CustomMenuButtonNotFoundError(data.button_id)
    await process_custom_menu_button(reply_query, user, bot, button)


def register_main_reply_query_handlers(dp: Dispatcher):

    dp.register_list_and_reply_query_handler(
        custom_menu_button_handler,
        CustomMenuButtonCallbackData.get_filter("data"),
        state="*",
    )

    dp.register_reply_query_handler(
        incust_my_card_handler, IncustMyCardCallbackData.get_filter(), state="*"
    )

    dp.register_reply_query_handler(
        template_reply_handler,
        TemplateReplyCallbackData.get_filter("data"),
        state="*",
    )
