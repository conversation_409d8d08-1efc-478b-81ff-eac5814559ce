from aiowhatsapp import Dispatcher, types
from aiowhatsapp.dispatcher.storage import FSMContext

from core.chat.virtual_manager.functions import start_virtual_manager_chat
from db import crud
from db.models import ClientBot, User
from utils.text import f
from utils.update_localisation import update_localisation
from whatsapp_bot.bot.modules.main.functions import send_hello_message


async def start_cmd_handler(
        message: types.Message, state: FSMContext, user: User, bot: ClientBot, lang: str
):
    await state.finish()
    user_bot_activity = await user.activity_in_bot
    await user_bot_activity.update(active_menu_in_store_id=None)

    await send_hello_message(message, user, bot, lang)
    if bot.on_join_virtual_manager_id:
        await start_virtual_manager_chat(
            user, bot.on_join_virtual_manager_id,
            bot.group_id,
            bot,
        )
    else:
        await crud.delete_currently_running_vm_chats(user.id, bot.id)


async def state_cmd_handler(message: types.Message, state: FSMContext):
    await message.answer(
        f"State: {await state.get_state()}, data: {await state.get_data()}"
    )


async def upd_cmd_handler(message: types.Message, lang: str):
    await update_localisation()
    await message.answer(await f("updated local text", lang))


async def product_cmd_handler(message: types.Message):
    args = message.text.split(" ")
    if len(args) != 3:
        return await message.answer("catalog_id and product_retailer_id is required")
    catalog_id, product_retailer_id = args[1:]
    return await message.answer_interactive_product(
        catalog_id, product_retailer_id, "Test product"
    )


async def product_list_cmd_handler(message: types.Message):
    args = message.text.split(" ")
    if len(args) < 3:
        return await message.answer("catalog_id and product_retailer_ids is required")
    catalog_id, *product_retailer_ids = args[1:]
    return await message.answer_interactive_product_list(
        catalog_id, [types.ProductListSection(
            product_items=[types.ProductListSectionItem(
                product_retailer_id=product_retailer_id,
            ) for product_retailer_id in product_retailer_ids]
        )],
        header="Test products list",
        body="body"
    )


def register_main_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(start_cmd_handler, commands="start", state="*")
    dp.register_message_handler(state_cmd_handler, commands="state", state="*")
    dp.register_message_handler(upd_cmd_handler, commands="upd", state="*")
    dp.register_message_handler(product_cmd_handler, commands="product", state="*")
    dp.register_message_handler(
        product_list_cmd_handler, commands="product_list", state="*"
    )
