from aiowhatsapp import Dispatcher, types

from core.chat.functions import get_business_name_from_bot
from core.custom_texts import ct
from db.models import ClientBot


async def request_welcome_handler(
        request_welcome: types.RequestWelcome,
        bot: ClientBot,
        lang: str,
):
    brand, business_name = await get_business_name_from_bot(bot)

    message_text = await ct(
        bot, lang,
        "whatsapp",
        "request welcome message",
        user_name=request_welcome.user.name,
        business_name=business_name,
    )
    if not message_text:
        return

    if brand and (image_url := await brand.image_url):
        await request_welcome.answer_image(image_url, message_text, )
    else:
        await request_welcome.answer(message_text)


def register_main_request_welcome_message_handlers(dp: Dispatcher):
    dp.register_request_welcome_handler(request_welcome_handler)
