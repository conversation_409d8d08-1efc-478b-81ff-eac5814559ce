import logging

from aiowhatsapp import types, Dispatcher


async def errors_handler(update_entry: types.UpdateEntry, error: Exception):
    logger = logging.getLogger()
    logger.error(error, exc_info=True)
    await update_entry.bot.send_message(
        update_entry.user.phone_number,
        "Unknown error was occurred.\nPlease, try again"
    )

    return True


def register_main_errors_handlers(dp: Dispatcher):
    dp.register_errors_handler(errors_handler)
