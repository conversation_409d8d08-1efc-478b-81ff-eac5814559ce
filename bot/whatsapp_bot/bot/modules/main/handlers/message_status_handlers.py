from aiowhatsapp import types, Dispatcher


async def sent_message_handler(message_status: types.MessageStatus):
    print(
        f"Message sent at {message_status.timestamp}\n",
        f"Message id: {message_status.id}\n",
        f"Conversation: {message_status.conversation}\n",
        f"Pricing: {message_status.pricing}"
    )


async def delivered_message_handler(message_status: types.MessageStatus):
    print(
        f"Message delivered at {message_status.timestamp}\n",
        f"Message id: {message_status.id}\n",
        f"Conversation: {message_status.conversation}\n",
        f"Pricing: {message_status.pricing}"
    )


async def read_message_handler(message_status: types.MessageStatus):
    print(
        f"Message read at {message_status.timestamp}",
        f"Message id: {message_status.id}\n",
    )


async def failed_message_handler(message_status: types.MessageStatus):
    print(
        f"Message sending failed at {message_status.timestamp}",
        f"Message id: {message_status.id}",
    )


def register_main_message_status_handlers(dp: Dispatcher):
    dp.register_message_status_handler(sent_message_handler, statuses="sent", state="*")
    dp.register_message_status_handler(delivered_message_handler, statuses="delivered", state="*")
    dp.register_message_status_handler(read_message_handler, statuses="read", state="*")
    dp.register_message_status_handler(failed_message_handler, statuses="failed", state="*")
