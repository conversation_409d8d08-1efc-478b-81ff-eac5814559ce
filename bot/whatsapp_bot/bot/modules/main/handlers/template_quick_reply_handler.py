import logging
from typing import Optional

from aiowhatsapp import types
from aiowhatsapp.dispatcher.storage import FSMContext

import schemas
from core.chat.chat_message_sender import ChatMessageSender
from core.whatsapp.callback_data import (
    TemplateReplyCallbackData,
)
from db import crud
from db.models import ClientBot, MediaObject, User
from db.models.bot.reply_buttons import ReplyButton

debugger = logging.getLogger("debugger.whatsapp.templateQuickReply")


async def process_template_quick_reply(
        reply_button_id: int,
        user: User,
        bot: ClientBot,
) -> Optional[tuple[Optional[str], Optional[MediaObject]]]:
    reply_button = await ReplyButton.get(reply_button_id)

    params: Optional[schemas.TemplateReplyButtonParams] = None

    if not reply_button:
        debugger.error(f"Reply button with id {reply_button_id} not found")
        return None

    try:
        params = schemas.TemplateReplyButtonParams(**reply_button.params)
    except Exception as e:
        debugger.error(
            f"Failed to parse params for reply_button_id={reply_button_id}: {e}"
        )

    content_type = reply_button.content_type
    media: Optional[MediaObject] = reply_button.get_media()
    text_content = reply_button.content.get("message") if reply_button.content else None
    original_content = reply_button.content.get(
        "original_text"
    ) if reply_button.content else None

    if not all([params.chat_id, params.manager_id, content_type]):
        debugger.warning(f"Incomplete data in reply_button_id={reply_button_id}")
        return text_content, media

    chat = await crud.get_chat(params.chat_id)
    if not chat:
        debugger.error(f"Chat with id {params.chat_id} not found")
        return text_content, media

    user_sender = ChatMessageSender(
        chat,
        schemas.MessageContentTypeEnum.TEXT,
        original_content,
        sent_by=schemas.ChatMessageSentByEnum.USER,
        sent_by_user_id=user.id,
        bot=bot,
    )

    message = await user_sender.save_message()
    await user_sender.send_message_to_crm(message)

    manager_sender = ChatMessageSender(
        chat,
        content_type,
        text_content,
        media=media,
        sent_by=schemas.ChatMessageSentByEnum.MANAGER,
        sent_by_user_id=params.manager_id,
    )

    message = await manager_sender.save_message()
    await manager_sender.send_message_to_crm(message)

    debugger.debug(f"Saved message to chat {chat.id}")

    return text_content, media


async def template_reply_handler(
        reply_query: types.ReplyQuery,
        data: TemplateReplyCallbackData,
        user: User, state: FSMContext, bot: ClientBot,
):
    debugger.debug(f"Template reply query: {reply_query}")
    if data.button_id:
        message, media = await process_template_quick_reply(
            int(data.button_id),
            user,
            bot,
        )

        debugger.debug(f"Message: {message}, Media: {media}")

        media: MediaObject | None = media if media else None

        if message and not media:
            await reply_query.answer(
                message,
            )
        elif media and media.media_type == 'image':
            await reply_query.answer_image(
                media.url,
                caption=message if message else None,
            )
        elif media and media.media_type == 'video':
            await reply_query.answer_video(
                media.url,
                caption=message if message else None,
            )
        elif media and media.media_type == 'document':
            await reply_query.answer_document(
                media.url,
                caption=message if message else None,
            )
    else:
        debugger.debug(f"Empty button id for reply query: {reply_query}")
