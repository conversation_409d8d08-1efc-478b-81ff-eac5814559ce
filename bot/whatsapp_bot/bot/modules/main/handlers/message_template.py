from aiowhatsapp import Dispatcher, types

from db.models import ClientBot, WATemplate


async def request_message_template_status_handler(
    request_message_template_status: types.MessageTemplateStatus,
    bot: ClientBot,
):
    message_template = await WATemplate.get(
        bot_id=bot.id,
        template_id=request_message_template_status.message_template_id
    )
    if message_template:
        await message_template.update(
            template_status=request_message_template_status.event,
            template_status_reason=request_message_template_status.reason,
        )


def register_main_request_message_template_status_handlers(dp: Dispatcher):
    dp.register_message_template_status_handler(request_message_template_status_handler)
