from aiowhatsapp.types.answer_object import AnswerObject

from core.chat.functions import get_business_name_from_bot
from core.custom_texts import ct
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db.models import ClientBot, User
from utils.text import html_to_markdown


async def send_hello_message(
        obj: AnswerObject, user: User,
        bot: ClientBot, lang: str,
        message_key: str = "hello text"
):
    brand, business_name = await get_business_name_from_bot(bot)
    keyboard = await get_wa_menu_keyboard(user, bot, lang)
    await obj.answer(
        html_to_markdown(
            await ct(
                bot,
                lang,
                "main",
                message_key,
                user_name=user.name,
                business_name=business_name,
            )
        ),
        keyboard=keyboard,
    )
