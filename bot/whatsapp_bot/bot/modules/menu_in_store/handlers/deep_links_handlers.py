from aiowhatsapp import Dispatcher, types
from aiowhatsapp.dispatcher.storage import FSMContext

from core.menu_in_store.deel_links import MenuInStoreDeepLink
from db.models import ClientBot, MenuInStore, User, UserClientBotActivity

from utils.text import f

from core.whatsapp.keyboards import get_wa_menu_keyboard

from whatsapp_bot.bot.modules.menu_in_store.functions import send_menu_in_store


async def menu_in_store_deep_link_handler(
        message: types.Message,
        state: FSMContext,
        data: MenuInStoreDeepLink,
        bot: ClientBot,
        user: User,
        user_bot_activity: UserClientBotActivity,
        lang: str,
):
    await state.finish()
    menu_in_store: MenuInStore | None = await MenuInStore.get(data.menu_in_store_id)

    if not menu_in_store:
        keyboard = await get_wa_menu_keyboard(user, bot, lang)
        message_text = await f("menu in store link invalid error", lang)
        return await message.answer(message_text, reply_markup=keyboard)

    if menu_in_store.need_save_as_active and \
            not data.is_payment_online and \
            user_bot_activity.active_menu_in_store_id != menu_in_store.id:
        await user_bot_activity.update(active_menu_in_store_id=menu_in_store.id)

    await send_menu_in_store(message, menu_in_store, user, bot, lang)


def register_menu_in_store_deep_links_handlers(dp: Dispatcher):
    dp.register_message_handler(
        menu_in_store_deep_link_handler,
        MenuInStoreDeepLink.get_filter(return_field_name="data"),
        state="*",
    )
