import aiowhatsapp as wa

from core.custom_texts import ct, ict
from core.custom_texts.models import MenuInStoreCustomTextsModel
from core.menu_in_store.keyboards import get_wa_menu_in_store_keyboard
from db.models import Brand, ClientBot, MenuInStore, User


async def send_menu_in_store(
        answer_obj: wa.types.message.AnswerObject,
        menu_in_store: MenuInStore,
        user: User, bot: ClientBot, lang: str,
):
    ct_obj = await MenuInStoreCustomTextsModel.from_object(menu_in_store)

    calculated_name = await menu_in_store.get_calculated_in_store_name()

    header_text = await ct(
        ct_obj, lang,
        "header text",
        name=calculated_name,
        comment=menu_in_store.comment,
    )
    greeting_text = await ct(
        ct_obj, lang,
        "greeting text",
        name=calculated_name,
        comment=menu_in_store.comment,
    )
    comment_view_text = await ct(
        ct_obj, lang,
        "comment view text",
        name=calculated_name,
        comment=menu_in_store.comment,
    )

    if greeting_text:
        body = greeting_text
        header = header_text
        footer = comment_view_text
    elif header_text:
        body = header_text
        header = None
        footer = comment_view_text
    elif comment_view_text:
        body = comment_view_text
        header = None
        footer = None
    else:
        body = f"{calculated_name}: {menu_in_store.comment}"
        header = None
        footer = None

    is_order_button = await ict(ct_obj, "menu", "order_button")
    is_pay_button = await ict(ct_obj, "payments", "pay_button")

    if is_order_button:
        main_action_text = await ct(ct_obj, lang, "menu", "main action text")
        main_action = None

        if menu_in_store.store_id:
            main_action_link_path = f"s/{menu_in_store.store_id}/menu"
        else:
            main_action_link_path = f"select"

    elif is_pay_button:
        main_action_text = await ct(ct_obj, lang, "payments", "main action text")
        main_action = "payonline"

        if menu_in_store.store_id:
            main_action_link_path = f"s/{menu_in_store.store_id}/qrmenu"
        else:
            main_action_link_path = "s/qrmenu"
    else:
        main_action_text = None
        main_action = None
        main_action_link_path = None

    if main_action_text:
        link_kwargs = {}
        if main_action:
            link_kwargs["action"] = main_action

        brand = await Brand.get(group_id=menu_in_store.group_id)
        main_action_link = await brand.get_short_token_url(
            user, bot.id, lang,
            main_action_link_path,
            menu_in_store.id,
            **link_kwargs,
        )

        body += f"\n\n{main_action_text}\n{main_action_link}"

    keyboard = await get_wa_menu_in_store_keyboard(
        menu_in_store, bot, user, lang, ct_obj
    )
    return await answer_obj.answer_interactive_list(
        header=header,
        body=body,
        footer=footer,
        button=keyboard.button,
        sections=keyboard.sections,
    )
