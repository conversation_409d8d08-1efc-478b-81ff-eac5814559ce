import logging

from aiowhatsapp import Dispatcher, types
from aiowhatsapp.dispatcher.storage import FSMContext

from core.external_coupon.deep_links import ExternalCouponDeepLink
from core.external_coupon.external_coupon_processer import ExternalCouponProcessor
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db.models import ClientBot, User

logger = logging.getLogger("debugger.ext_coupon")


async def external_coupon_deep_link_handler(
        message: types.Message,
        state: FSMContext,
        data: ExternalCouponDeepLink,
        bot: ClientBot,
        user: User,
):
    logger.debug(f"{data.external_coupon_code=}, {bot.id=}, {user.id=}, {bot.group_id=}")
    lang = await user.get_lang(bot) or 'en'
    keyboard = await get_wa_menu_keyboard(user, bot, lang)
    processor = ExternalCouponProcessor(
        message, state, bot, user, lang, 
        keyboard, data.external_coupon_code, 
        store_id=data.store_id,
    )
    await processor.process()


def register_external_coupon_deep_links_handlers(dp: Dispatcher):
    dp.register_message_handler(
        external_coupon_deep_link_handler,
        ExternalCouponDeepLink.get_filter(return_field_name="data"),
        state="*",
    )
