from aiowhatsapp import types, Dispatcher
from aiowhatsapp.dispatcher.storage import FSMContext

from core.external_coupon.callback_data import ShareCouponCallbackData, ApplyCouponCallbackData
from core.external_coupon.handlers import (
    share_coupon_united_handler, apply_coupon_united_handler,
)

from db.models import ClientBot, User

from core.whatsapp.keyboards import get_wa_menu_keyboard


async def share_coupon_handler(
        reply_query: types.ReplyQuery,
        share_coupon: ShareCouponCallbackData,
        user: User, state: FSMContext,
        lang: str, bot: ClientBot,
):
    keyboard = await get_wa_menu_keyboard(user, bot, lang)
    return await share_coupon_united_handler(reply_query, state, user, bot, lang, keyboard, share_coupon)


async def apply_coupon_handler(
        reply_query: types.ReplyQuery,
        apply_coupon: ApplyCouponCallbackData,
        user: User, state: FSMContext,
        lang: str, bot: ClientBot,
):
    keyboard = await get_wa_menu_keyboard(user, bot, lang)
    return await apply_coupon_united_handler(reply_query, state, user, bot, lang, keyboard, apply_coupon)


def register_external_coupon_reply_query_handlers(dp: Dispatcher):
    dp.register_reply_query_handler(
        share_coupon_handler,
        ShareCouponCallbackData.get_filter(),
        state="*",
    )

    dp.register_reply_query_handler(
        apply_coupon_handler,
        ApplyCouponCallbackData.get_filter(),
        state="*",
    )
