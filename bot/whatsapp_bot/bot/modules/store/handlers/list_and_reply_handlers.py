from aiowhatsapp import Dispatcher, types
from aiowhatsapp.dispatcher.storage import FSMContext

from core.custom_texts import ct
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db import crud
from db.models import ClientBot, User
from utils.text import f


async def shop_button_handler(
        query: types.ListReplyQuery | types.ReplyQuery,
        state: FSMContext,
        bot: ClientBot,
        user: User,
        lang: str,
):
    await state.finish()

    keyboard = await get_wa_menu_keyboard(user, bot, lang)

    brand = await crud.get_brand_by_group(bot.group_id)
    if not brand:
        return await query.answer(await f("bot does not have a shop", lang), keyboard)

    link = await brand.get_short_token_url(user, bot.id, lang)

    return await query.answer(
        await ct(bot, lang, "shop", "wa open text", link=link),
        keyboard=keyboard,
    )


def register_store_list_and_reply_handlers(dp: Dispatcher):
    dp.register_list_and_reply_query_handler(
        shop_button_handler,
        reply_mode="shop",
        state="*",
    )
