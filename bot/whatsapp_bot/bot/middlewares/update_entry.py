from aiowhatsapp import types
from aiowhatsapp.dispatcher.handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from aiowhatsapp.dispatcher.middlewares import BaseMiddleware

from db.models import ClientBot
from utils.platform_admins import send_message_to_platform_admins
from whatsapp_bot.bot.webhook_handlers import ctx_wa_hook_app_id


class UpdateEntryMiddleware(BaseMiddleware):

    async def on_pre_process_update_entry(
            self, update_entry: types.UpdateEntry, data: dict
    ):
        if not update_entry.metadata and update_entry.id:
            bot = await ClientBot.get(whatsapp_business_account_id=update_entry.id)
        else:
            bot = await ClientBot.get(
                whatsapp_from=update_entry.metadata.phone_number_id
            )
        if not bot:
            raise CancelHandler()

        app_id = ctx_wa_hook_app_id.get(None)
        if not app_id:
            await send_message_to_platform_admins(
                f"no wa_hook_app_id in context {update_entry.as_json()}"
            )
            raise CancelHandler()

        if bot.whatsapp_app_id != app_id:
            raise CancelHandler()

        reset_token = ClientBot.set_current_bot_id(bot.id)
        data["__bot_reset_token"] = reset_token

    async def on_post_process_update_entry(self, *args):
        data = args[2]
        ClientBot.reset_current_bot_id(data["__bot_reset_token"])
