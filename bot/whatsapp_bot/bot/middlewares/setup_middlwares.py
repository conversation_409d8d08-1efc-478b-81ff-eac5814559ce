from aiowhatsapp import Dispatcher

from .billing_checker import BillingCheckerMiddleware
from .bot_middleware import BotMiddleware
from .create_or_update_user import CreateOrUpdateUserMiddleware
from .exception_handlers import ExceptionHandlersMiddleware
from .reply_data import ReplyDataMiddleware
from .update_entry import UpdateEntryMiddleware


def setup_middlewares(dp: Dispatcher):
    dp.setup_middleware(UpdateEntryMiddleware())
    dp.setup_middleware(BotMiddleware())
    dp.setup_middleware(ExceptionHandlersMiddleware())
    dp.setup_middleware(BillingCheckerMiddleware())
    dp.setup_middleware(CreateOrUpdateUserMiddleware())
    dp.setup_middleware(ReplyDataMiddleware())
