import logging

from aiowhatsapp import WhatsappBot, types
from aiowhatsapp.dispatcher.handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from aiowhatsapp.dispatcher.middlewares import LifetimeControllerMiddleware

import schemas
from config import DEFAULT_LANG
from core.auth.deep_links import ExternalLogin<PERSON>eepLink
from core.chat.chat_message_sender import Chat<PERSON>essageSender
from core.helpers import check_incust_customer
from core.user.agreement_processor.agreement_processor import agreement_processor
from core.user.exceptions import UserNotCreated
from core.user.functions import create_or_update_messanger_user
from db import crud
from db.models import Client<PERSON>ot, Customer, Group
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f


class CreateOrUpdateUserMiddleware(LifetimeControllerMiddleware):
    skip_patterns = ["update_entry"]

    @staticmethod
    async def is_external_login(message: types.Message):
        try:
            external_login_filter = ExternalLoginDeepLink.get_filter()
            return bool(await external_login_filter(message))
        except Exception as e:
            logging.error(e, exc_info=True)
            return False

    async def pre_process(
            self,
            obj: types.message.AnswerObject,
            data, *args,
    ):
        if isinstance(obj, types.Message) and await self.is_external_login(obj):
            return

        if not hasattr(obj, "from_user") or \
                (isinstance(obj, types.ReplyQuery) and obj.button.id.startswith(
                    agreement_processor.prefix
                )):
            return

        try:
            info = await create_or_update_messanger_user(
                obj.from_user,
                return_is_created=True,
                allowed_create_user=False,
            )

            bot: ClientBot | None = data.get("bot")

            if info.user:
                lang = await info.user.get_lang(bot.id)
            elif bot:
                lang = bot.lang
            else:
                lang = DEFAULT_LANG

            if not isinstance(obj, types.RequestWelcome) and (
                    not info.user or
                    not info.user.is_accepted_agreement
            ):
                await agreement_processor.ask(
                    obj.from_user.id, lang, bot, types.Update.get_current()
                )
                raise CancelHandler()

            if info.user:
                customer = await Customer.get(
                    user_id=info.user.id, profile_id=bot.group_id
                )
                if (not customer or customer.marketing_consent is None or not
                customer.is_accept_agreement):
                    await agreement_processor.ask(
                        obj.from_user.id, lang, bot, types.Update.get_current()
                    )
                    raise CancelHandler()

            data["user"] = info.user
            data["user_bot_activity"] = info.user_bot_activity
            data["lang"] = lang
            data["is_created"] = info.is_user_created
            data["is_created_bot_activity"] = info.is_bot_activity_created

            await check_incust_customer(bot, info.user, lang)

            if not info.user_bot_activity.is_entered_bot:
                group = await Group.get(bot.group_id)
                chat = await crud.get_or_create_chat(
                    schemas.ChatTypeEnum.USER_WITH_GROUP, info.user.id, group.id, bot.id
                )
                await ChatMessageSender(
                    chat, schemas.MessageContentTypeEnum.TEXT,
                    await f("user entered bot message", group.lang),
                    sent_by=schemas.ChatMessageSentByEnum.USER,
                    sent_by_user_id=info.user.id,
                    chat_user=info.user,
                    group=group,
                    bot=bot,
                    chat_pending=False,
                    disable_send_to_service_bot=True,
                )
                await info.user_bot_activity.entered_bot()

        except UserNotCreated as user_not_created:
            logger = logging.getLogger()
            logger.error(user_not_created, exc_info=True)
            user_got_error_message = False

            wa_phone = user_not_created.user_id
            if not wa_phone:
                current_user = types.User.get_current()
                if current_user:
                    wa_phone = current_user.id

            if wa_phone:
                current_bot = WhatsappBot.get_current()
                try:
                    text = await f("user not created error for user", DEFAULT_LANG)
                    await current_bot.send_message(wa_phone, text)
                except:
                    pass
                else:
                    user_got_error_message = True

            # язык ru, так как сообщение к разработчикам
            if user_got_error_message:
                user_got_error_message = await f("user got error message", "ru")
            else:
                user_got_error_message = await f("user did not get error message", "ru")
            username, full_name = user_not_created.username, user_not_created.full_name

            text = await f(
                "user not created error for developers", "ru",
                chat_id=wa_phone, username=username,
                full_name=full_name, bot_username=user_not_created.bot_username,
                user_got_error_message=user_got_error_message,
            )
            await send_message_to_platform_admins(text)
            raise CancelHandler()

    async def post_process(self, obj, data, *args):
        pass
