from aiowhatsapp.dispatcher.middlewares import LifetimeControllerMiddleware

from core.billing.functions import check_bot_billing_status


class BillingCheckerMiddleware(LifetimeControllerMiddleware):
    skip_patterns = ["update_entry", "message_status", "system_message", "error"]

    async def pre_process(self, obj, data: dict, *args):
        if not hasattr(obj, "from_user"):
            return
        await check_bot_billing_status(data["bot"], obj.from_user)
