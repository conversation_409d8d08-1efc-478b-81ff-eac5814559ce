from aiowhatsapp import types
from aiowhatsapp.dispatcher.middlewares import LifetimeControllerMiddleware

from utils.text import parse_callback_data


class ReplyDataMiddleware(LifetimeControllerMiddleware):
    skip_patterns = ["message", "message_status", "system_message", "update_entry", "error"]

    async def pre_process(self, obj, data, *args):
        if isinstance(obj, types.ReplyQuery):
            data_str = obj.button.id
        elif isinstance(obj, types.ListReplyQuery):
            data_str = obj.section_row.id
        else:
            return

        mode, reply_data = parse_callback_data(data_str)
        data.update(mode=mode, reply_data=reply_data)
