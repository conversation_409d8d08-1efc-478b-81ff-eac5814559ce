from typing import Callable, Any

from aiowhatsapp import Dispatcher
from aiowhatsapp.dispatcher.webhook.fastapi import Fastapi<PERSON><PERSON><PERSON><PERSON><PERSON>ebhook<PERSON><PERSON><PERSON>
from contextvars import ContextVar

from fastapi import Path

ctx_wa_hook_app_id = ContextVar("wa_hook_app_id")


class WhatsappAppIdWebhookHandler(FastapiWhatsappWebhookHandler):
    def __init__(
            self, dp: Dispatcher,
            verify_token: str,
            prefix: str = "/whatsapp/{app_id}",  # app_id MUST BE in prefix
            debug_update: Callable[[dict], Any] | None = None,
    ):
        super().__init__(dp, verify_token, prefix, debug_update)

    # noinspection PyMethodOverriding
    async def hook(
            self,
            data: dict,
            app_id: str = Path(),
    ):
        token = ctx_wa_hook_app_id.set(app_id)
        try:
            await super().hook(data)
        finally:
            ctx_wa_hook_app_id.reset(token)
