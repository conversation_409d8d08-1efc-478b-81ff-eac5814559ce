from typing import Any

from aiowhatsapp import types
from aiowhatsapp.dispatcher.filters import Filter
from aiowhatsapp.dispatcher.handler import ctx_data


NOT_EMPTY_REPLY_VALUE = "__not_empty__"

EMPTY_REPLY_VALUE = "__empty__"

NOT_IN_REPLY_DATA_VALUE = "__not_in_data__"


class ReplyQueryModeFilter(Filter):

    def __init__(
            self,
            reply_mode: str | list[str] = None,
            reply_mode_starts: str | list[str] = None,
            reply_keys: dict[str, Any] = None,
    ):
        if reply_mode and reply_mode_starts:
            raise ValueError("Only one of callback_mode and callback_mode_starts can be specified")

        if reply_mode is not None and type(reply_mode) is not list:
            reply_mode = [reply_mode]

        if reply_mode_starts is not None and type(reply_mode_starts) is not list:
            reply_mode_starts = [reply_mode_starts]

        if reply_keys is None:
            reply_keys = dict()

        self.reply_modes = reply_mode
        self.reply_mode_starts = reply_mode_starts
        self.reply_keys = reply_keys

    @classmethod
    def validate(cls, full_config: dict[str, Any]) -> dict[str, Any] | None:
        """
                Validator for filters factory

                From filters factory this filter can be registered with arguments:

                 - ``reply_mode`` (will be passed as ``reply_mode``)
                 - ``reply_mode_starts`` (will be passed as ``reply_mode_starts``)
                 -``reply_keys`` (will be passed as ``reply_keys``)

                :param full_config:
                :return: config or empty dict
        """

        config = dict()
        if "reply_mode" in full_config:
            config["reply_mode"] = full_config.pop("reply_mode")
        if "reply_mode_starts" in full_config:
            config["reply_mode_starts"] = full_config.pop("reply_mode_starts")
        if "reply_keys" in full_config:
            config["reply_keys"] = full_config.pop("reply_keys")
        return config

    async def check(self, query: types.ReplyQuery | types.ListReplyQuery):
        data = ctx_data.get()
        mode: str = data.get("mode")
        reply_data: dict[str, Any] = data.get("reply_data")

        if self.reply_modes is not None and mode not in self.reply_modes:
            return False

        if self.reply_mode_starts:

            for reply_mode in self.reply_mode_starts:
                if mode.startswith(reply_mode):
                    break
            else:
                return False

        for fileter_key, filter_value in self.reply_keys.items():
            if fileter_key not in reply_data and filter_value == NOT_IN_REPLY_DATA_VALUE:
                continue

            callback_value = reply_data.get(fileter_key, None)

            if callback_value and filter_value == NOT_EMPTY_REPLY_VALUE:
                continue

            if callback_value is None and filter_value == EMPTY_REPLY_VALUE:
                continue

            if callback_value != filter_value:
                return False

        return {"mode": mode, "reply_data": reply_data}
