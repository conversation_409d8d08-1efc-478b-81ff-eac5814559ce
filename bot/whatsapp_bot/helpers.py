import os

from aiowhatsapp import Dispatcher, WhatsappBot
from aiowhatsapp.api.exceptions import WhatsappApiError

from config import STATIC_DB, WHATSAPP_WEBHOOK_URL, WHATSAPP_WEBHOOK_URL_TOKEN
from db.models import ClientBot
from loggers import JSONLogger


async def setup_bot(dp: Dispatcher, db_bot: ClientBot):
    bot = WhatsappBot(
        token=db_bot.token,
        from_=db_bot.whatsapp_from,
        app_id=db_bot.whatsapp_app_id,
        app_secret=db_bot.whatsapp_app_secret,
        static_dir=os.path.join(STATIC_DB, "whatsapp"),
        whatsapp_business_account_id=db_bot.whatsapp_business_account_id,
    )

    logger = JSONLogger(
        "whatsapp.setup", {
            "bot": db_bot
        }
    )

    try:
        result = await bot.set_webhook(
            WHATSAPP_WEBHOOK_URL.format(app_id=db_bot.whatsapp_app_id),
            WHATSAPP_WEBHOOK_URL_TOKEN,
            fields=["messages", "phone_number_name_update"]
        )
    except WhatsappApiError as e:
        logger.error(
            f"WhatsApp Api error received while setting up webhook", e.error_data
        )
    except Exception as e:
        logger.error(
            f"An error occurred while settings whatsapp bot {db_bot.display_name}", e
        )
    else:
        logger.debug(
            f"Whatsapp webhook was successfully set up for bot {db_bot.display_name}",
            {
                "result": result,
            }
        )

    dp.add_bot(bot)
