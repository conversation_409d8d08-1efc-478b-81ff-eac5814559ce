import re
from typing import List, Type
from urllib.parse import urlparse

from fastapi import Depends, Header, Request
from sqlalchemy.orm import Session
from starlette.websockets import WebSocket

import schemas
from config import ANONYMOUS_USER_EMAIL, DEFAULT_LANG
from config.test_config import WEB_APP_HOST
from db import sess
from db.models import ClientBot, User
from utils.helpers import normalize_url
from utils.type_vars import T


def get_lang(request: Request):
    return request.state.lang


def get_header_lang(lang: str = Header(DEFAULT_LANG, alias="Accept-Language")):
    return lang


async def get_db() -> Session:
    return sess()


def get_external_token(external_token: str = Header(alias="X-Auth-External-Token")):
    return external_token


IGNORE_REFERRER_PATTERNS = (
    r"facebook\.com",
)


async def get_current_domains(
        request: Request = None,
        websocket: WebSocket = None,
) -> list[str]:
    obj = request or websocket

    urls = [
        obj.headers.get("referer"),
        obj.headers.get("host"),
    ]

    # just to remove warning
    normalized_url = None

    return [
        normalized_url
        for url in urls
        if (
                url and
                (normalized_url := normalize_url(url)) and
                WEB_APP_HOST not in normalized_url
        )
    ]


async def get_current_domain_info(
        request: Request = None,
        websocket: WebSocket = None,
) -> schemas.DomainInfoSchema:
    obj = request or websocket

    origin_url = obj.headers.get(
        "referer", obj.headers.get("host", obj.headers.get("x-forwarded-host"))
    )
    if any(
            map(
                lambda pattern: re.search(pattern, origin_url),
                IGNORE_REFERRER_PATTERNS
            )
    ):
        origin_url = obj.headers.get("host", obj.headers.get("x-forwarded-host"))

    if origin_url:
        if not origin_url.startswith("http"):
            origin_url = "https://" + origin_url
        if not origin_url.endswith("/"):
            origin_url += "/"
        origin_url = "https://" + urlparse(origin_url).netloc + "/"

    domain_info = schemas.DomainInfoSchema(
        backend_host_name=obj.url.hostname,
        backend_ip=obj.client.host,
        backend_port=obj.client.port,
        origin_url=origin_url,
    )
    request.state.domain_info = domain_info.dict()
    return domain_info


async def get_current_bot(
        request: Request = None,
        websocket: WebSocket = None,
) -> ClientBot | str | None:
    obj = request or websocket

    bot_id = None
    path_bot_id = obj.path_params.get("bot_id")
    if path_bot_id:
        bot_id = path_bot_id
    if not path_bot_id:
        query_bot_id = obj.query_params.get("bot_id")
        if query_bot_id:
            bot_id = query_bot_id

    if not bot_id:
        try:
            auth_data = obj.headers.get("TGAuthorization")
            split_data = auth_data.rsplit(",", maxsplit=1)
            bot_id = split_data[1].strip()
            bot_id = int(bot_id) if bot_id.isdecimal() else bot_id
        except:
            pass

    if not bot_id and request:
        try:
            body = await request.json()
            bot_id = body.get("bot_id")
        except:
            pass

    ClientBot.set_current_bot_id(bot_id)

    if bot_id == "service":
        bot = bot_id
    elif bot_id == "admin":
        bot = bot_id
    elif bot_id:
        bot = await ClientBot.get(bot_id)
    else:
        bot = None

    return bot


def get_api_token(api_token: str = Header(alias="X-Auth-Token")):
    return api_token


async def get_anonymous_user() -> User:
    return await User.get_by_email(ANONYMOUS_USER_EMAIL)


def build_form_data_depend(
        model: Type[T],
        list_keys: List[str] | None = None,
        lists_object_keys: dict | None = None
):
    async def form_data_to_model(request: Request, _: model = Depends()):
        form = await request.form()
        form_dict = {}
        if list_keys and not lists_object_keys:
            for key in form.keys():
                if key in list_keys:
                    form_dict[key] = form.getlist(key)
                else:
                    form_dict[key] = form.get(key)

            return model(**form_dict)

        form_dict = dict(form.items())
        if list_keys and lists_object_keys:
            for list_key in list_keys:
                if list_key in lists_object_keys:
                    form_items = []
                    for form_item in form.multi_items():
                        if form_item[0].startswith(list_key):
                            form_items.append(form_item)
                    list_object_key = lists_object_keys[list_key]
                    list_object_key_dict = {}
                    for list_object_key_item in list_object_key:
                        list_object_key_dict[list_object_key_item] = []

                    for form_item in form_items:
                        try:
                            key = form_item[0].split("[")[1].split("]")[0]
                            list_object_key_dict[key].append(form_item[1])
                        except IndexError:
                            continue

                    result_list = [dict(zip(list_object_key_dict, t)) for t in
                                   zip(*list_object_key_dict.values())]
                    form_dict[list_key] = result_list
                else:
                    form_dict[list_key] = form.getlist(list_key)
                    continue

        return model(**form_dict)

    return form_data_to_model


def build_form_data_depend_with_lists(model: Type[T], list_keys: List[str]):
    async def form_data_to_model(request: Request, _: model = Depends()):
        form = await request.form()
        form_data = {}
        for key in form.keys():
            if key in list_keys:
                form_data[key] = form.getlist(key)
            else:
                form_data[key] = form.get(key)

        return model(**form_data)

    return form_data_to_model
