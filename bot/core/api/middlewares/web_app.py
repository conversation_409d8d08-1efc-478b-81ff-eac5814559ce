import json
import logging
from datetime import datetime
from typing import Awaitable, Callable

from aiogram.utils.web_app import safe_parse_webapp_init_data
from psutils.convertors import str_to_int
from psutils.fastapi.middlewares import BaseMiddleware
from starlette.requests import Request

from api.functions import get_bot_token
from config import TESTER_USERS, WEB_APP_DATA_LIFE_TIME
from db import DBSession
from schemas import WebAppData


class WebAppMiddleware(BaseMiddleware):

    async def __call__(self, request: Request, call_next: Callable[..., Awaitable]):
        request.state.web_app_data = None

        logger = logging.getLogger()

        auth_data = request.headers.get("TGAuthorization")

        if not auth_data or (len(auth_data.split(",")) not in (1, 2)):
            return await call_next(request)

        try:
            split_data = auth_data.rsplit(",", maxsplit=1)
            init_data = split_data[0]
            if len(split_data) == 2:
                bot_id = split_data[1].strip()
                bot_id = int(bot_id) if bot_id.isdecimal() else bot_id
            else:
                bot_id = request.path_params.get("bot_id", request.query_params.get("bot_id"))

                if bot_id:
                    bot_id = str_to_int(bot_id, only_positive=True, no_error=True) or bot_id

        except Exception as e:
            logger.error(e, exc_info=True)
            return await call_next(request)

        if init_data and bot_id:
            try:
                if init_data in TESTER_USERS:
                    decoded_init_data = TESTER_USERS[init_data]
                    decoded_init_data["auth_date"] = f"{int(datetime.utcnow().timestamp())}"
                else:
                    # get_bot_token not working without DBSession although session in higher middleware
                    with DBSession():
                        bot_token = await get_bot_token(bot_id)
                    decoded_init_data = safe_parse_webapp_init_data(bot_token, init_data, json.loads)
                web_app_data = WebAppData(**decoded_init_data)

                now = datetime.utcnow()
                if web_app_data.auth_date.replace(tzinfo=now.tzinfo) + WEB_APP_DATA_LIFE_TIME < now:
                    web_app_data = None

            except Exception as e:
                logger.error(e, exc_info=True)
                web_app_data = None
        else:
            web_app_data = None

        request.state.web_app_data = web_app_data
        return await call_next(request)
