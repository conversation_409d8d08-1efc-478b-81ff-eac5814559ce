from datetime import datetime
from typing import Awaitable, Callable

from psutils.fastapi.middlewares import BaseMiddleware
from starlette.requests import Request


class RequestReceivedDateTimeMiddleware(BaseMiddleware):
    async def __call__(self, request: Request, call_next: Callable[..., Awaitable]):
        request.state.received_datetime = datetime.utcnow()
        return await call_next(request)
