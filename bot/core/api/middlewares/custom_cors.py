import typing

from starlette.datastructures import Headers
from starlette.middleware.cors import CORSMiddleware
from starlette.types import ASGIApp, Receive, Scope, Send

from config import AD<PERSON><PERSON>ONAL_CORS_ORIGINS, ADMIN_HOST, CRM_HOST, WEB_APP_HOST
from db import models


class CustomCORSMiddleware(CORSMiddleware):
    origins = (
        "http://localhost",
        "http://localhost:1914",
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:5040",
        "http://localhost:8081",
        ADMIN_HOST,
        CRM_HOST,
        WEB_APP_HOST,
        *ADDITIONAL_CORS_ORIGINS
    )
    origin_regex = r"https://.*\.(payforsay|7loc)\.com"

    def __init__(
            self,
            app: ASGIApp,
            allow_origins: typing.Sequence[str] = origins,
            allow_methods: typing.Sequence[str] = "*",
            allow_headers: typing.Sequence[str] = "*",
            allow_credentials: bool = True,
            allow_origin_regex: typing.Optional[str] = origin_regex,
            expose_headers: typing.Sequence[str] = (),
            max_age: int = 600,
    ):
        super().__init__(
            allow_origins=allow_origins, allow_credentials=allow_credentials,
            allow_methods=allow_methods, allow_headers=allow_headers,
            expose_headers=expose_headers, max_age=max_age, app=app,
            allow_origin_regex=allow_origin_regex
        )

    async def __call__(self, scope: Scope, receive: Receive, send: Send) -> None:
        #  copied full __call__ from CORSMiddleware cause access to scope obj from here broke custom uvicorn logs

        if scope["type"] != "http":  # pragma: no cover
            await self.app(scope, receive, send)
            return

        method = scope["method"]
        headers = Headers(scope=scope)
        origin = headers.get("origin")

        if origin is None:
            await self.app(scope, receive, send)
            return

        if not self.is_allowed_origin(origin):
            brand_origin = origin
            if not brand_origin.endswith('/'):
                brand_origin += '/'
            if await models.Brand.get_by_domain(brand_origin):
                self.allow_origins += (origin,)

        if method == "OPTIONS" and "access-control-request-method" in headers:

            response = self.preflight_response(request_headers=headers)
            await response(scope, receive, send)
            return

        await self.simple_response(scope, receive, send, request_headers=headers)
