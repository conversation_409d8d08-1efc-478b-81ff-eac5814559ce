import re

from fastapi import <PERSON><PERSON><PERSON>
from starlette.types import <PERSON><PERSON><PERSON><PERSON>, Receive, Scope, Send

from db import DBSession


class SessionMiddleware:
    ignore_paths = (
        r"/admin/\d+/admin_notifications/stream",
        r"/payments/\d+/stream",
    )

    def __init__(self, app: ASGIApp) -> None:
        self.app = app

    async def __call__(self, scope: Scope, receive: Receive, send: Send) -> None:
        try:
            path = scope["path"]
        except:
            path = None

        if path and any(
                map(lambda pattern: re.match(pattern, path), self.ignore_paths)
        ):
            await self.app(scope, receive, send)
        else:
            with DBSession():
                await self.app(scope, receive, send)

    @classmethod
    def setup(cls, app: FastAPI):
        # noinspection PyTypeChecker
        app.add_middleware(cls)
