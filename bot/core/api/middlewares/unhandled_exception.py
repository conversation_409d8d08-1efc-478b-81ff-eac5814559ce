import http
import logging
import sys
import traceback

from starlette import status
from starlette.requests import Request
from starlette.responses import JSONResponse
from starlette.types import ASGIApp, Message, Receive, Scope, Send


class UnhandledExceptionMiddleware:
    def __init__(self, app: ASGIApp) -> None:
        self.app = app

    async def __call__(self, scope: Scope, receive: Receive, send: Send):
        response_started = False

        async def _send(message: Message) -> None:
            nonlocal response_started, send

            if message["type"] == "http.response.start":
                response_started = True
            await send(message)

        if scope["type"] == "http":
            request = Request(scope, receive)
            try:
                await self.app(scope, receive, _send)
            except Exception as error:
                if not hasattr(
                        request.state, "exception_info"
                ) or not request.state.exception_info:
                    logging.error(f"Unhandled exception: {str(error)}", exc_info=True)
                    request.state.exception_info = {
                        "message": f"{error.__class__.__name__}: {str(error)}",
                        "info": "".join(traceback.format_exception(*sys.exc_info())),
                    }
                if not response_started:
                    detail = http.HTTPStatus(500).phrase
                    response = JSONResponse(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        content={"detail": detail},
                    )
                    await response(scope, receive, send)
        else:
            await self.app(scope, receive, send)
