from typing import Awaitable, Callable

from psutils.fastapi.middlewares import BaseMiddleware
from starlette.requests import Request

from config import DEFAULT_LANG
from utils.accept_language import parse_accept_language
from utils.translator import Translator


class LangMiddleware(BaseMiddleware):

    async def __call__(self, request: Request, call_next: Callable[..., Awaitable]):
        parsed_lang = parse_accept_language(
            request.headers.get("Accept-Language", DEFAULT_LANG)
        )
        if parsed_lang not in await Translator.get_supported_languages(DEFAULT_LANG):
            parsed_lang = DEFAULT_LANG
        request.state.lang = parsed_lang
        return await call_next(request)
