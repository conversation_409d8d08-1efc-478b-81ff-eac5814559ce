import asyncio
import json
import logging
import uuid
from typing import Any, Awaitable, Callable

import time
from fastapi import BackgroundTasks
from fastapi.routing import APIRoute
from psutils.fastapi.middlewares import BaseMiddleware
from starlette.requests import Request
from starlette.responses import Response

from api.utils.custom_streaming_response import CustomStreamingResponse
from config import HTTP_LOG_KAFKA_TOPIC
from core.kafka.producer import producer
from loggers import JSONLogger
from utils.api import request_form_to_dict
from utils.date_time import utcnow
from utils.platform_admins import send_message_to_platform_admins

logger = JSONLogger("httplog")


class HTTPLogMiddleware(BaseMiddleware):
    DEBUG = False

    async def __call__(self, request: Request, call_next: Callable[..., Awaitable]):
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        request_body = await self.make_request_body(request)

        start = time.time()

        original_response = await call_next(request)

        end = time.time()
        execution_time = end - start

        if hasattr(original_response, "body_iterator"):
            response = CustomStreamingResponse(original_response)
        else:
            if not isinstance(original_response, Response):
                raise ValueError("original_response has to be instance of Response")
            response = original_response

        response.headers["X-Request-Id"] = request_id

        response.background = BackgroundTasks(
            [original_response.background] if original_response.background else None
        )

        response.background.add_task(
            self.make_and_write_log_entry,
            request, request_body,
            response, execution_time,
        )

        return response

    @classmethod
    async def make_and_write_log_entry(
            cls, request: Request,
            request_body: Any = None,
            response: Response = None,
            execution_time: float = None
    ):
        try:
            log_entry = await cls.make_log_entry(
                request, request_body, response, execution_time
            )
        except Exception as e:
            logger.error(f"Exception while making log entry: {str(e)}", exc_info=True)
            await send_message_to_platform_admins(
                f"Making HTTP Log error "
                f"{request.method} {request.url}({request.state.request_id}) to kafka "
                f"error:"
                f" {str(e)}"
            )
        else:
            if log_entry:
                asyncio.ensure_future(cls.write_log(log_entry))

    @classmethod
    async def make_request_body(cls, request: Request):
        try:
            request_body = await request.json()
            request_body = json.dumps(request_body, indent=4, ensure_ascii=False)
        except Exception as json_e:
            if cls.DEBUG:
                logger.error(f"request.json(): {str(json_e)}", exc_info=True)
            try:
                request_body = (await request.body()).decode()
            except Exception as body_e:
                if cls.DEBUG:
                    logging.error(f"request.body(): {str(body_e)}", exc_info=True)
                request_body = None
        return request_body

    @classmethod
    async def make_request_form(cls, request: Request):
        try:
            request_form = await request_form_to_dict(request, True)
            if not request_form:
                return None

            return json.dumps(request_form, indent=4, ensure_ascii=False)
        except Exception as e:
            logging.error("make_request_form", repr(e))
            return None

    @classmethod
    async def make_response_body(cls, response: Response) -> str | None:
        if not hasattr(response, "body"):
            return None

        if isinstance(response.body, bytes):
            try:
                response_body = response.body.decode()
            except UnicodeDecodeError:
                logging.error(
                    f"response.body.decode() error: [UnicodeDecodeError], "
                    f"{response.status_code=}"
                )
                return None
            except Exception as e:
                logging.error(
                    f"response.body.decode() error: {repr(e)}", exc_info=True
                )
                return None
        elif isinstance(response.body, str):
            response_body = response.body
        else:
            return None

        try:
            response_body = json.loads(response_body)
            response_body = json.dumps(response_body, indent=4, ensure_ascii=False)
        except:
            pass

        return response_body

    @classmethod
    def get_user_data(cls, request: Request):
        if not hasattr(request.state, "user_data") or not isinstance(
                request.state.user_data, dict
        ):
            return {}

        return request.state.user_data

    @classmethod
    def get_session_info(cls, request: Request):
        if not hasattr(request.state, "session_info") or not isinstance(
                request.state.session_info, dict
        ):
            return {}

        return request.state.session_info

    @classmethod
    def get_exception_info(cls, request: Request):
        if not hasattr(request.state, "exception_info") or not isinstance(
                request.state.exception_info, dict
        ):
            return None

        return request.state.exception_info

    @classmethod
    def get_domain_info(cls, request: Request):
        if not hasattr(request.state, "domain_info") or not isinstance(
                request.state.domain_info, dict
        ):
            return None

        return request.state.domain_info

    @classmethod
    def get_brand_info(cls, request: Request):
        if not hasattr(request.state, "brand_info") or not isinstance(
                request.state.brand_info, dict
        ):
            return None

        return request.state.brand_info

    @classmethod
    def get_token_session_id(cls, request: Request):
        if not hasattr(request.state, "token_session_id"):
            return ''

        return request.state.token_session_id or ""

    @staticmethod
    def dict_to_key_value_string(d: dict):
        if not d:
            return ""
        return "\n".join((f"{key}: {value}" for key, value in d.items()))

    @classmethod
    async def make_log_entry(
            cls, request: Request,
            request_body: Any = None,
            response: Response = None,
            execution_time: float = None
    ):
        route: APIRoute | None
        if isinstance(route := request.scope.get("route"), APIRoute):
            route_path = f"{request.scope.get('root_path')}{route.path}"
            route_name = route.name
        else:
            route_path = None
            route_name = None

        if (route_path or request.url.path).endswith("/openapi.json"):
            return  # now writing /openapi.json endpoints to logs

        request_form = await cls.make_request_form(request)

        try:
            if not isinstance(response, Response):
                raise ValueError("not Response")
            response_status_code = response.status_code
            response_headers = dict(response.headers)
        except:
            response_status_code = None
            response_headers = None

        if request.url.path.startswith("/static/uploads"):
            response_body = f"FILE: {request.url.path}"
        else:
            response_body = await cls.make_response_body(response)

        return {
            "id": request.state.request_id,
            "created": request.state.received_datetime.isoformat() if hasattr(
                request.state, "received_datetime"
            ) else utcnow().isoformat(),
            "status": response_status_code,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "route": route_path,
            "route_name": route_name,
            "request_headers": cls.dict_to_key_value_string(dict(request.headers)),
            "request_body": request_body,
            "request_form": request_form,
            "request_path_params": cls.dict_to_key_value_string(request.path_params),
            "request_query_params": cls.dict_to_key_value_string(
                dict(request.query_params)
            ),
            "request_cookies": cls.dict_to_key_value_string(request.cookies),
            "response_headers": cls.dict_to_key_value_string(response_headers),
            "response_body": response_body,
            "execution_time": execution_time,
            "user": cls.get_user_data(request),
            "session_info": cls.get_session_info(request),
            "exception_info": cls.get_exception_info(request),
            "domain_info": cls.get_domain_info(request),
            "brand_info": cls.get_brand_info(request),
            "token_session_id": cls.get_token_session_id(request),
        }

    @staticmethod
    async def write_log(log_entry: dict):
        try:
            await producer.p.send_and_wait(
                topic=HTTP_LOG_KAFKA_TOPIC,
                value=json.dumps(log_entry).encode(),
            )
        except Exception as e:
            logging.error(
                f"Sending HTTP Log to kafka error: {str(e)}: {log_entry}", exc_info=True
            )
            await send_message_to_platform_admins(
                f"Sending HTTP Log "
                f"{log_entry['method']} {log_entry['url']}({log_entry['id']}) to "
                f"kafka error:"
                f" {str(e)}"
            )
