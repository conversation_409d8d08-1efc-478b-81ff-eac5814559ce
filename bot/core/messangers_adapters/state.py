from aiogram.dispatcher.filters import state as tg_state

from .dispatcher import Dispatcher


class StatesGroup(tg_state.StatesGroup):

    @classmethod
    async def next(cls) -> str:
        dp = await Dispatcher.get_current()
        state = dp.current_state()
        state_name = await state.get_state()

        try:
            next_step = cls.states_names.index(state_name) + 1
        except ValueError:
            next_step = 0

        try:
            next_state_name = cls.states[next_step].state
        except IndexError:
            next_state_name = None

        await state.set_state(next_state_name)
        return next_state_name

    @classmethod
    async def previous(cls) -> str:
        dp = await Dispatcher.get_current()
        state = dp.current_state()
        state_name = await state.get_state()

        try:
            previous_step = cls.states_names.index(state_name) - 1
        except ValueError:
            previous_step = 0

        if previous_step < 0:
            previous_state_name = None
        else:
            previous_state_name = cls.states[previous_step].state

        await state.set_state(previous_state_name)
        return previous_state_name

    @classmethod
    async def first(cls) -> str:
        dp = await Dispatcher.get_current()
        state = dp.current_state()
        first_step_name = cls.states_names[0]

        await state.set_state(first_step_name)
        return first_step_name

    @classmethod
    async def last(cls) -> str:
        dp = await Dispatcher.get_current()
        state = dp.current_state()
        last_step_name = cls.states_names[-1]

        await state.set_state(last_step_name)
        return last_step_name


class State(tg_state.State):
    async def set(self):
        dp = await Dispatcher.get_current()
        state = dp.current_state()
        await state.set_state(self.state)
