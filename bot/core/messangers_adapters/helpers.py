import aiogram
import aiogram as tg
import aiowhatsapp as wa

import config as cfg
import schemas
from db.models import ClientBot, User
from utils.helpers import get_running_file_name
from . import types


async def detect_bot_type(
        obj: types.AnswerObject | None = None,
) -> schemas.BotTypeLiteral:
    if obj:
        if isinstance(obj, tg.types.base.TelegramObject):
            return "telegram"
        if isinstance(obj, wa.types.base.WhatsappObject):
            return "whatsapp"

        raise ValueError(
            f"obj must be typeof tg.types.Message or wa.types.message.AnswerObject, "
            f"not {type(obj)}"
        )

    file_name = get_running_file_name()

    if file_name == cfg.WHATSAPP_BOT:
        return "whatsapp"

    if file_name in (
            cfg.ROOT_BOT,
            cfg.SERVICE_BOT,
            cfg.CLIENT_BOT,
            cfg.FRIENDLY_BOT,
            cfg.LIP_LEP_BOT,
            cfg.MONITORING_BOT,
    ):
        return "telegram"

    bot = await ClientBot.get_current()
    if bot:
        return bot.bot_type

    raise ValueError("Unable to detect bot_type")


def detect_answer_obj(obj):
    if isinstance(obj, tg.types.CallbackQuery):
        return obj.message
    if isinstance(obj, wa.types.message.AnswerObject | aiogram.types.message.Message):
        return obj
    raise ValueError(f"Invalid query type {type(obj)}")


def get_user_to(user: User, bot_type: schemas.BotTypeLiteral):
    match bot_type:
        case "telegram":
            to = user.chat_id
        case "whatsapp":
            to = user.wa_phone
        case _:
            raise ValueError("Unsupported bot.bot_type")

    return to


def get_user_by_message(message: types.AnswerObject):
    if isinstance(message, tg.types.Message):
        if message.chat.type == "private":
            return User.get(message.chat.id)
        return User.get(message.from_id)
    elif isinstance(message, wa.types.message.AnswerObject):
        return User.get_by_wa_phone(message.user.phone_number)
    else:
        raise ValueError(
            f"Excepted instance of "
            f"aiogram.types.Message or "
            f"aiowhatsapp.types.message.AnswerObject, "
            f"got {message}"
        )
