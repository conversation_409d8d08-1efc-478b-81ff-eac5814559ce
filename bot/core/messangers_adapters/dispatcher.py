import aiogram as tg
import aiowhatsapp as wa

import schemas
from .bot import Bo<PERSON>
from .helpers import detect_bot_type

DispatcherType = tg.Dispatcher | wa.Dispatcher
FSMContext = tg.dispatcher.FSMContext | wa.dispatcher.FSMContext


class Dispatcher:

    def __init__(
            self, type_: schemas.BotTypeLiteral, dp: DispatcherType | None = None
    ) -> None:
        self.type = type_

        if not dp:
            if type_ == "telegram":
                dp = tg.Dispatcher.get_current()
            else:
                dp = wa.Dispatcher.get_current()

        self.dp: DispatcherType = dp

    @classmethod
    async def get_current(
            cls, bot_type: schemas.BotTypeLiteral | None = None
    ) -> "Dispatcher":
        if bot_type not in ("telegram", "whatsapp"):
            bot_type = await detect_bot_type()

        if bot_type is None:
            raise ValueError("bot_type have to be telegram or whatsapp")  # for pycharm

        return cls(bot_type)

    def current_state(
            self, *, user: str | None = None, chat: str | None = None
    ) -> FSMContext:
        if self.type == "whatsapp":
            return self.dp.current_state(user=user)

        elif self.type == "telegram":
            return self.dp.current_state(user=user, chat=chat)

    @property
    def bot(self) -> Bot:
        return Bot(self.type)
