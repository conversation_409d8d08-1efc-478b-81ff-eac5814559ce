import aiogram
import aiowhatsapp

import schemas
from db.models import ClientBot

from psutils.func import check_function_spec

from . import types

BotType = aiogram.Bot | aiowhatsapp.WhatsappBot


class Bot:

    def __init__(self, type_: schemas.BotTypeLiteral | None = None, bot: BotType | ClientBot | None = None):
        if not type_ and (not bot or not isinstance(bot, ClientBot)):
            raise ValueError("Need to specify the type of bot or transfer the bot ClientBot")

        if not type_ and bot and isinstance(bot, ClientBot):
            type_ = bot.bot_type
        self.type = type_

        if not bot:
            if type_ == "telegram":
                bot = aiogram.Bot.get_current()
            else:
                bot = aiowhatsapp.WhatsappBot.get_current()

        elif isinstance(bot, ClientBot):
            bot = self.create(bot)

        self.bot: BotType = bot

    @classmethod
    def set_current(cls, bot: ClientBot):
        current_bot = cls.create(bot)

        if bot.bot_type == "telegram":
            aiogram.Bot.set_current(current_bot)
        elif bot.bot_type == "whatsapp":
            aiowhatsapp.WhatsappBot.set_current(current_bot)

    @classmethod
    def create(cls, bot: ClientBot) -> BotType:
        if bot.bot_type == "telegram":
            current_bot = aiogram.Bot(bot.token)

        elif bot.bot_type == "whatsapp":
            current_bot = aiowhatsapp.WhatsappBot(
                bot.token, bot.whatsapp_from,
            )

        else:
            raise ValueError("Invalid bot type")

        return current_bot

    async def send_message(
            self, user: str | int, text: str,
            keyboard: types.Keyboard | None = None,
            reply_to_message_id: str | int | None = None,
            **kwargs,
    ):
        if keyboard:

            if self.type == "telegram":
                if isinstance(keyboard, types.InlineKeyboard):
                    keyboard = keyboard.to_telegram()

                if not isinstance(keyboard, types.AiogramKeyboard):
                    raise ValueError("Keyboard must be an instance of InlineKeyboard or AiogramKeyboard")

                if "reply_markup" in kwargs:
                    raise ValueError("reply_markup argument cannot be specified with keyboard argument")

                kwargs["reply_markup"] = keyboard

            elif self.type == "whatsapp":
                if isinstance(keyboard, types.InlineKeyboard):
                    keyboard = keyboard.to_whatsapp()

                if not isinstance(
                        keyboard, (
                            aiowhatsapp.types.ReplyKeyboard |
                            aiowhatsapp.types.ListKeyboard |
                            aiowhatsapp.types.UrlKeyboard |
                            list
                        )
                ):
                    raise ValueError('Keyboard must be an instance of InlineKeyboard or AiowhatsappKeyboard')

                kwargs["keyboard"] = keyboard

        kwargs = check_function_spec(self.bot.send_message, kwargs)
        if "parse_mode" not in kwargs and self.type == "telegram":
            kwargs["parse_mode"] = "HTML"
        return await self.bot.send_message(user, text, reply_to_message_id=reply_to_message_id, **kwargs)
