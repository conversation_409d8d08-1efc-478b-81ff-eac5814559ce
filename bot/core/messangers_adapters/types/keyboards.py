import logging
from typing import Literal

import aiogram
import aiowhatsapp

import schemas
from .base import BaseMessangerType

AiogramKeyboard = aiogram.types.InlineKeyboardMarkup | aiogram.types.ReplyKeyboardMarkup
AiowhatsappKeyboard = (aiowhatsapp.types.ReplyKeyboard |
                       aiowhatsapp.types.ListKeyboard |
                       list[aiowhatsapp.types.ReplyButton])


class InlineKeyboardButton(BaseMessangerType):

    def __init__(self, text: str, data: str):
        self.text = text
        self.data = data

    def to_whatsapp(self, reply_or_section_row: Literal["reply", "section_row"]):
        text = self.text[:20] if len(self.text) > 20 else self.text
        match reply_or_section_row:
            case "reply":
                button_cls = aiowhatsapp.types.ReplyButton
            case "section_row":
                button_cls = aiowhatsapp.types.SectionRow
            case _:
                return None
        return button_cls(id=self.data, title=text)

    def to_telegram(self, url_as_web_app: bool = True):
        return aiogram.types.InlineKeyboardButton(self.text, callback_data=self.data)


class UrlKeyboardButton(BaseMessangerType):

    def __init__(self, text: str, url: str):
        self.text = text
        self.url = url

    def to_whatsapp(self):
        if self.text.isascii():
            text = self.text[:20]
        else:
            text = self.text[:10]
        return aiowhatsapp.types.UrlButton(display_text=text, url=self.url)

    def to_telegram(self, url_as_web_app: bool = True):
        return aiogram.types.InlineKeyboardButton(
            self.text,
            web_app=aiogram.types.WebAppInfo(url=self.url) if url_as_web_app else None,
            url=self.url if not url_as_web_app else None,
        )


class InlineKeyboard(BaseMessangerType):

    def __init__(
            self,
            buttons: list[InlineKeyboardButton | UrlKeyboardButton] | None = None,
            row_width: int = 2,
            **kwargs,
    ):
        self.buttons: list[InlineKeyboardButton | UrlKeyboardButton] = buttons or []
        self.row_width: int = row_width

    def add_buttons(self, *buttons: InlineKeyboardButton | UrlKeyboardButton):
        if not buttons:
            raise ValueError("at least one button is required")

        self.buttons.extend(buttons)

    def to_whatsapp(self):
        if len(self.buttons) > 3:
            raise ValueError("Cannot add more buttons than 3 buttons for whatsapp")

        if not self.buttons:
            return None

        if isinstance(self.buttons[0], UrlKeyboardButton):
            return aiowhatsapp.types.UrlKeyboard(button=self.buttons[0].to_whatsapp())

        return aiowhatsapp.types.ReplyKeyboard(
            buttons=[
                button.to_whatsapp("reply") for button in self.buttons
                if isinstance(button, InlineKeyboardButton)
            ]
        )

    def to_telegram(self, **kwargs):
        if kwargs:
            keyboard = aiogram.types.InlineKeyboardMarkup(**kwargs)
        else:
            keyboard = aiogram.types.InlineKeyboardMarkup(row_width=self.row_width)
        keyboard.add(
            *[button.to_telegram(url_as_web_app=kwargs.get("url_as_web_app", True)) for
              button in self.buttons]
        )
        return keyboard


class MenuKeyboardButton(BaseMessangerType):

    def __init__(
            self, text: str,
            data: str | None = None,
            description: str | None = None,
            request_contact: bool | None = None,
    ):
        self.text = text
        self.data = data  # data will be used only for whatsapp
        self.description = description  # description will be used only for whatsapp
        # when type is list
        self.request_contact = request_contact  # request_contact will be used only
        # for telegram

    def to_whatsapp(self, type_: Literal["list", "reply"]):
        if type_ == "list":
            return aiowhatsapp.types.SectionRow(
                id=self.data, title=self.text, description=self.description
            )

        if type_ == "reply":
            if self.description:
                logging.warning(
                    "description specified when type is reply. Description will be "
                    "ignored"
                )

            return aiowhatsapp.types.ReplyButton(id=self.data, title=self.text)

    def to_telegram(self):
        if self.request_contact is not None:
            return aiogram.types.KeyboardButton(
                text=self.text, request_contact=self.request_contact
            )
        return aiogram.types.KeyboardButton(text=self.text)


class MenuKeyboard(BaseMessangerType):

    def __init__(
            self, *,
            open_menu_button: str | None = None,
            buttons: list[MenuKeyboardButton] | None = None,
            resize_keyboard: bool = None,
            one_time_keyboard: bool = None,
            input_field_placeholder: str | None = None,
            selective: bool = None,
            **kwargs,
    ):
        if open_menu_button:
            self.whatsapp_type: Literal["list", "reply"] = "list"
        else:
            self.whatsapp_type: Literal["list", "reply"] = "reply"
        self.open_menu_button = open_menu_button
        self.buttons = buttons or []

        # props bellow will be used only for telegram
        self.resize_keyboard = resize_keyboard
        self.one_time_keyboard = one_time_keyboard
        self.input_field_placeholder = input_field_placeholder
        self.selective = selective

    def add_buttons(self, *buttons: MenuKeyboardButton):
        if not buttons:
            raise ValueError("at least one button is required")

        self.buttons.extend(buttons)

    def to_whatsapp(self):
        buttons = [button.to_whatsapp(self.whatsapp_type) for button in self.buttons]

        if self.whatsapp_type == "list":
            return aiowhatsapp.types.ListKeyboard(
                button=self.open_menu_button,
                sections=[aiowhatsapp.types.Section(rows=buttons)]
            )

        if self.whatsapp_type == "reply":
            return aiowhatsapp.types.ReplyKeyboard(buttons=buttons)

    def to_telegram(self):
        keyboard = aiogram.types.ReplyKeyboardMarkup(
            resize_keyboard=self.resize_keyboard,
            one_time_keyboard=self.one_time_keyboard,
            input_field_placeholder=self.input_field_placeholder,
            selective=self.selective,
        )
        keyboard.add(*[button.to_telegram() for button in self.buttons])
        return keyboard


class InlineMenuKeyboard(BaseMessangerType):

    def __init__(
            self,
            buttons: (
                    list[
                        InlineKeyboardButton |
                        UrlKeyboardButton |
                        MenuKeyboardButton
                        ] | None
            ) = None,
            keyboard_type: Literal["inline", "menu"] = "inline",
            **kwargs
    ):
        if buttons:
            if isinstance(buttons[0], MenuKeyboardButton):
                keyboard_type = "menu"
            else:
                keyboard_type = "inline"

        if keyboard_type == "inline":
            self.keyboard = InlineKeyboard(buttons=buttons, **kwargs)
        else:
            self.keyboard = MenuKeyboard(buttons=buttons, **kwargs)

    def add_buttons(
            self,
            *buttons: InlineKeyboardButton | UrlKeyboardButton | MenuKeyboardButton
    ):
        self.keyboard.add_buttons(*buttons)

    def to_whatsapp(self):
        return self.keyboard.to_whatsapp()

    def to_telegram(self):
        return self.keyboard.to_telegram()

    def get(
            self, bot_type: schemas.BotTypeLiteral = "telegram"
    ) -> AiogramKeyboard | AiowhatsappKeyboard:
        if bot_type == "telegram":
            return self.to_telegram()
        return self.to_whatsapp()


Keyboard = MenuKeyboard | InlineKeyboard | AiogramKeyboard | AiowhatsappKeyboard
