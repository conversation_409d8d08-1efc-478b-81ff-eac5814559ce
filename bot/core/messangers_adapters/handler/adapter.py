from collections.abc import Callable
from typing import Any, Generic, TypedDict

import aiogram as tg
import aiowhatsapp as wa
from psutils.func import check_function_spec

from utils.type_vars import FuncT, P, RT


class MessangersHandlersInputDictType(TypedDict, total=False):
    telegram: tuple[str, ...] | str | None
    whatsapp: tuple[str, ...] | str | None


class HandlerAdapter(Generic[P, RT]):

    def __init__(
            self,
            callback: FuncT,
            messangers_handlers: MessangersHandlersInputDictType,
    ):
        self.handlers: dict[str, tuple[str, ...]] = {}
        for key, value in messangers_handlers.items():
            if isinstance(value, str):
                # noinspection PyTypedDict
                self.handlers[key] = (value,)
            elif isinstance(value, tuple):
                self.handlers[key] = value

        self.callback = callback

    def __call__(self, *args: P.args, **kwargs: P.kwargs) -> RT:
        button_data = kwargs.get("callback_data") or kwargs.get("reply_data")
        safe_kwargs = check_function_spec(
            self.callback, {**kwargs, "button_data": button_data}
        )
        return self.callback(*args, **safe_kwargs)

    @classmethod
    def decorate(cls, messangers_handlers: MessangersHandlersInputDictType):
        def decorator(callback: Callable[P, RT]) -> HandlerAdapter:
            return HandlerAdapter(callback, messangers_handlers)

        return decorator

    @classmethod
    def detect_messanger_name(cls, dp):
        messangers_dispatchers = {
            tg.Dispatcher: "telegram",
            wa.Dispatcher: "whatsapp",
        }
        if type(dp) not in messangers_dispatchers:
            raise ValueError(f"Unknown dispatcher {type(dp)}")

        return messangers_dispatchers[type(dp)]

    def setup(
            self,
            dp: tg.Dispatcher | wa.Dispatcher,
            *custom_filters,
            state = None,
            messangers_kwargs: dict[str, dict[str, Any]] | None = None,
            **kwargs,
    ):
        messanger_name = self.detect_messanger_name(dp)

        if messangers_kwargs and (
                messanger_kwargs := messangers_kwargs.get(messanger_name)
        ):
            kwargs.update(messanger_kwargs)

        for handler_name in self.handlers[messanger_name]:
            register_handler = getattr(dp, f"register_{handler_name}_handler", None)
            if not register_handler:
                raise ValueError(f"Unknown handler {handler_name}")
            register_handler(self, *custom_filters, state=state, **kwargs)
