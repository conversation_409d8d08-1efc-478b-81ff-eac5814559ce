from aiogram import types

from core import messangers_adapters as ma
from db.models import ClientBot, User
from utils.text import f, html_to_markdown


async def send_with_wa_main_menu(
        answer_object: ma.types.AnswerObject, bot: ClientBot, text: str, user: User,
        lang,
        no_edit: bool | None = False, is_text_with_menu: bool | None = None
):
    messanger_bot = ma.Bot(bot=bot)
    if bot.bot_type == "telegram":
        if isinstance(answer_object, types.Message):
            if no_edit:
                await answer_object.answer(text)
            else:
                await answer_object.edit_text(text)
        else:
            await answer_object.message.edit_text(text)
    elif bot.bot_type == "whatsapp":
        text = html_to_markdown(text)
        from core.whatsapp.keyboards import get_wa_menu_keyboard
        keyboard = await get_wa_menu_keyboard(user, bot, lang)
        if not is_text_with_menu:
            await messanger_bot.send_message(user.wa_phone, text)
            await answer_object.answer(await f("main menu button", lang), keyboard)
        else:
            await messanger_bot.send_message(user.wa_phone, text, keyboard=keyboard)
