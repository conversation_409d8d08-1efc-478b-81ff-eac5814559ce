from typing import Literal

from core.messangers_adapters import InlineKeyboard, InlineKeyboardButton
from utils.text import f
from .callback_data import ConfirmAddFriendCallbackData


async def get_yes_or_no_keyboard(
        lang: str,
        req_user_id: int,
        bot_type: Literal["telegram", "whatsapp"] = "telegram",
):
    keyboard = InlineKeyboard(row_width=2)
    yes_callback_data = ConfirmAddFriendCallbackData(
        req_user_id=req_user_id, answer=True
    ).to_str()
    no_callback_data = ConfirmAddFriendCallbackData(
        req_user_id=req_user_id, answer=False
    ).to_str()

    keyboard.add_buttons(
        InlineKeyboardButton(await f("yes button", lang), yes_callback_data),
        InlineKeyboardButton(await f("no button", lang), no_callback_data)
    )

    return keyboard.to_messanger(bot_type)
