import logging

import aiogram as tg

import core.messangers_adapters as ma
from client.main.keyboards import get_menu_keyboard
from config import P4S_API_URL
from core.friend.keyboards import get_yes_or_no_keyboard
from db import crud
from db.models import ClientBot, User
from utils.text import f
from .callback_handlers import send_with_wa_main_menu
from ..deep_links import AddFriendDeepLink

debugger = logging.getLogger("debugger.add_friend")


@ma.handler.message()
async def add_friend_deep_link_handler(
        message: ma.types.Message,
        state: ma.FSMContext,
        data: AddFriendDeepLink,
        bot: ClientBot,
        user: User, lang: str,
):
    await state.finish()
    debugger.debug(f"{data.req_user_id=}")

    request_user: User = await User.get_by_id(data.req_user_id)
    if not request_user:
        raise Exception("User does not exist")

    if request_user.id == user.id:
        return await send_with_wa_main_menu(
            message, bot,
            await f("client bot add friend same text", lang),
            user, lang, True
        )

    if await crud.get_user_friend(request_user.id, user.id):
        return await send_with_wa_main_menu(
            message, bot,
            await f(
                "client bot add friend exist text", lang, req_name=request_user.name
            ),
            user, lang, True
        )

    req_info = request_user.get_info(bot.bot_type)

    message_text = await f(
        "client bot add friend greeting text", lang, req_name=req_info
    )
    reply_markup = await get_menu_keyboard(
        user, bot
    ) if bot.bot_type == "telegram" else None

    if user.photo:
        user_photo = f"{P4S_API_URL}/{user.photo}"
        try:
            await message.answer_photo(
                user_photo, caption=message_text,
                reply_markup=reply_markup
            )
        except Exception as ex:
            logging.error(
                f'message.answer_photo FAILED\n{user.id = }, {user.name = }, '
                f'{bot.bot_type = }, '
                f'{user_photo = }\n{ex = }', exc_info=True
            )
            await message.answer(message_text, reply_markup=reply_markup)
    else:
        await message.answer(message_text, reply_markup=reply_markup)

    await message.answer(
        await f(
            "client bot add friend confirm text", lang,
            req_name=request_user.name,
        ),
        reply_markup=await get_yes_or_no_keyboard(
            lang,
            req_user_id=data.req_user_id,
            bot_type=bot.bot_type,
        )
    )


def register_add_friend_commands_handlers(dp: tg.dispatcher.Dispatcher):
    add_friend_deep_link_handler.setup(
        dp,
        AddFriendDeepLink.get_filter(return_field_name="data"),
        state="*",
    )
