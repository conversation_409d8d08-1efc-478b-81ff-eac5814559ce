import logging

from core import messangers_adapters as ma
from core.friend.callback_data import ConfirmAddFriendCallbackData
from core.friend.functions import send_with_wa_main_menu
from db import crud
from db.models import ClientBot, Friend, User
from utils.text import f

debugger = logging.getLogger("debugger.add_friend")


@ma.handler.button()
async def confirm_add_friend_handler(
        query: ma.types.AnswerObject,
        confirm_add_friend: ConfirmAddFriendCallbackData,
        user: User,
        lang: str,
        bot: ClientBot,
):
    req_user_id = confirm_add_friend.req_user_id
    debugger.debug(f"{req_user_id=}, {user.id=}")
    messanger_bot = ma.Bot(bot=bot)

    if req_user_id == user.id:
        return await send_with_wa_main_menu(
            query, bot,
            await f("client bot add friend same text", lang),
            user, lang,
        )

    req_user: User | None = await User.get_by_id(req_user_id)
    if not req_user:
        return await send_with_wa_main_menu(
            query, bot,
            await f("client bot add friend not found text", lang),
            user, lang,
        )
    friend = user

    if not confirm_add_friend.answer:
        return await send_with_wa_main_menu(
            query, bot,
            await f(
                "client bot add friend no text",
                lang, req_name=req_user.name,
            ),
            user, lang,
        )

    if await crud.get_user_friend(req_user_id, friend.id):
        return await send_with_wa_main_menu(
            query, bot,
            await f(
                "client bot add friend exist text",
                lang, req_name=req_user.name,
            ),
            user, lang,
        )

    new_friend: Friend = await crud.create_user_friend(req_user_id, friend.id)
    if new_friend:
        user_to = ma.get_user_to(req_user, bot.bot_type)

        # message to requested user
        if user_to:
            req_user_lang = await req_user.get_lang(bot.id)
            text = await f(
                "client bot add friend confirmed to user text",
                req_user_lang,
                friend_name=friend.name,
                friend_info=friend.get_info(bot.bot_type),
            )
            await messanger_bot.send_message(user_to, text)

        return await send_with_wa_main_menu(
            query, bot,
            await f(
                "client bot add friend confirmed text",
                lang, req_name=req_user.name,
            ),
            user, lang,
        )


def register_friend_buttons_handlers(dp: ma.DispatcherType):
    confirm_add_friend_handler.setup(
        dp,
        ConfirmAddFriendCallbackData.get_filter(),
        state="*",
    )
