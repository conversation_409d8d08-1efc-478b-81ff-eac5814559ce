import aiogram as tg
import aiowhatsapp as wa
from core import messangers_adapters as ma

from core.auth.deep_links import ExternalLogin<PERSON>eepLink
from db.models import ClientBot
from ..external_login_processer import ExternalLoginProcessor
from ... import messangers_adapters


@messangers_adapters.handler.message()
async def external_login_deep_link_handler(
        message: ma.types.Message,
        state: ma.FSMContext,
        data: ExternalLoginDeepLink,
        bot: ClientBot,
):
    processor = ExternalLoginProcessor(message, state, bot, data.external_login_request_uuid)
    await processor.process()


def register_external_login_commands_handlers(dp: tg.Dispatcher | wa.Dispatcher):
    external_login_deep_link_handler.setup(
        dp,
        ExternalLoginDeepLink.get_filter(return_field_name="data"),
        state="*",
    )
