import aiogram as tg
import aiowhatsapp as wa

import schemas
from db import crud, sess
from db.models import User, ExternalLoginRequest, ClientBot, Brand
from utils.text import f
from ..callback_data import ExternalLoginCallbackData
from ..functions import external_login_success_auth
from ... import messangers_adapters
from ...messangers_adapters.helpers import detect_answer_obj


@messangers_adapters.handler.button()
async def update_or_login_button_handler(
        query: tg.types.CallbackQuery | wa.types.ReplyQuery,
        mode: str, data: ExternalLoginCallbackData,
        user: User, lang: str, bot: ClientBot,
):
    answer_obj = detect_answer_obj(query)

    external_login_request = await ExternalLoginRequest.get(data.external_login_request_id)
    if not external_login_request or mode not in ("ex_login_update", "ex_login_login"):
        return await answer_obj.answer(
            await f("external login unknown error", lang)
        )

    if external_login_request.status == schemas.ExternalLoginRequestStatusEnum.CANCELED:
        return await answer_obj.answer(
            await f("auth external login link was canceled error", lang),
        )

    if mode == "ex_login_update":
        extra_data = external_login_request.extra_data

        if extra_data:
            email = extra_data.get("email")
            password = extra_data.get("password")
            is_accepted_agreement = extra_data.get("is_accepted_agreement") or user.is_accepted_agreement

            await crud.update_user(sess(), user, schemas.UpdateUser(
                email=email,
                password=password,
                is_accepted_agreement=is_accepted_agreement,
            ))

    brand = await Brand.get(group_id=bot.group_id)

    await external_login_success_auth(
        answer_obj,
        external_login_request,
        user, lang, brand, bot,
    )

    if isinstance(answer_obj, tg.types.Message):
        await answer_obj.delete()


@messangers_adapters.handler.button()
async def cancel_button_handler(
        query: tg.types.CallbackQuery | wa.types.ReplyQuery,
        data: ExternalLoginCallbackData, lang: str,
):
    answer_obj = detect_answer_obj(query)

    external_login_request = await ExternalLoginRequest.get(data.external_login_request_id)
    if not external_login_request:
        return await answer_obj.answer(
            await f("external login unknown error", lang)
        )

    if external_login_request.status in (
        schemas.ExternalLoginRequestStatusEnum.SUCCESS,
        schemas.ExternalLoginRequestStatusEnum.CANCELED,
        schemas.ExternalLoginRequestStatusEnum.ERROR,
    ):
        text = await f("external login unable to cancel error", lang)
        if isinstance(answer_obj, tg.types.Message):
            return await answer_obj.edit_text(text)
        else:
            return await answer_obj.answer(text)
    else:
        await external_login_request.update(
            status=schemas.ExternalLoginRequestStatusEnum.CANCELED,
        )

    text = await f("external login canceled text", lang)
    if isinstance(answer_obj, tg.types.Message):
        await answer_obj.edit_text(text)
    else:
        await answer_obj.add_text(text)


def register_external_login_button_handlers(dp: tg.Dispatcher | wa.Dispatcher):
    update_or_login_button_handler.setup(
        dp, ExternalLoginCallbackData.get_filter(
            "ex_login_update",
            "ex_login_login",
            return_column_name="data",
        ),
        state="*",
    )
    cancel_button_handler.setup(
        dp, ExternalLoginCallbackData.get_filter(
            "ex_login_cancel",
            return_column_name="data",
        ),
        state="*",
    )
