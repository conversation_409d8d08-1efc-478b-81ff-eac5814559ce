import logging

import aiogram as tg
import aiowhatsapp as wa
from psutils.exceptions import ErrorWithTextVariable

import schemas
from config import DEFAULT_LANG, MESSANGERS_NAMES
from core import messangers_adapters as ma
from core.auth.services.external_login import ExternalLoginService
from core.keyboards import get_bot_menu_keyboard
from core.user.functions import create_or_update_messanger_user
from db import crud
from db.models import Brand, ClientBot, Customer, ExternalLoginRequest, Group, User
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from .functions import (
    external_login_success_auth, external_login_with_invitation,
    external_login_with_receipt,
)
from .keyboards import get_update_or_login_keyboard
from ..user.agreement_processor.agreement_processor import agreement_processor


class ExternalLoginProcessor:
    def __init__(
            self,
            message: ma.types.Message,
            state: ma.FSMContext,
            bot: ClientBot,
            request_uuid: str,
    ):
        self.message: ma.types.Message = message
        self.state: ma.FSMContext = state
        self.bot: ClientBot = bot
        self.request_uuid: str = request_uuid
        self._user: User | None = None
        self._lang: str | None = None

    @property
    def user(self):
        return self._user

    def set_user(self, user: User | None):
        self._user = user
        self._lang = None

    async def create_or_update_user(self, allowed_create_user: bool = True):
        user = await create_or_update_messanger_user(
            self.message.from_user, self.bot,
            allowed_create_user=allowed_create_user,
        )
        self.set_user(user)
        return user

    @property
    async def lang(self):
        if not self._lang:
            if not self.user:
                if isinstance(self.message, tg.types.Message):
                    lang = self.message.from_user.language_code
                else:
                    lang = None
                if not lang:
                    group = await Group.get(self.bot.group_id)
                    lang = group.lang
                self._lang = lang
            else:
                self._lang = await self.user.get_lang(self.bot.id)
        return self._lang

    async def process(self):
        service = ExternalLoginService(None, self.bot)
        await service.load_request(self.request_uuid)

        await self.state.finish()

        try:
            if (service.request.status ==
                    schemas.ExternalLoginRequestStatusEnum.CANCELED):
                return await self.message.answer(
                    await f(
                        "auth external login link was canceled error", await self.lang
                    ),
                )

            if service.request.status != schemas.ExternalLoginRequestStatusEnum.CREATED:
                await self.create_or_update_user()
                keyboard = await get_bot_menu_keyboard(
                    self.user, self.bot, await self.lang
                )
                return await self.message.answer(
                    await f(
                        "auth external login link has already been used error",
                        await self.lang
                    ),
                    reply_markup=keyboard,
                )

            await self.state.update_data(ext_login_uuid=self.request_uuid)
            if service.request.purpose in (
                    schemas.ExternalLoginRequestPurposeEnum.AUTH,
                    schemas.ExternalLoginRequestPurposeEnum.INCUST_EXTERNAL_ID
            ):
                await self.process_auth(service)
            elif (service.request.purpose ==
                  schemas.ExternalLoginRequestPurposeEnum.LINK):
                await self.process_link_ext_login(service)
            elif (service.request.purpose ==
                  schemas.ExternalLoginRequestPurposeEnum.RECEIPT):
                await self.process_auth(service, with_receipt=True)
            elif (service.request.purpose ==
                  schemas.ExternalLoginRequestPurposeEnum.INVITATION):
                await self.process_auth(service, with_invitation=True)
            else:
                raise ValueError("currently unsupported")
        except Exception as e:
            logging.error(e, exc_info=True)

            if isinstance(e, ErrorWithTextVariable):
                text_variable = e.text_variable
                text_kwargs = e.text_kwargs
            else:
                text_variable = "external login unknown error"
                text_kwargs = {}
            if self.user:
                lang = await self.lang
            else:
                lang = service.request.lang or DEFAULT_LANG
            return await self.message.answer(
                await f(text_variable, lang, **text_kwargs)
            )

    async def set_menu_button(self):
        if self.bot.bot_type == "telegram":
            from client.main.functions import set_menu_button
            await set_menu_button(self.bot, self.user, await self.lang)

    async def process_auth(
            self, service: ExternalLoginService,
            with_receipt: bool = False, with_invitation: bool = False
    ):
        user = await self.create_or_update_user(allowed_create_user=False)
        if not user:
            await self.state.update_data(external_login_request_id=service.request.id)
            return await agreement_processor.ask(
                self.message.from_user.id,
                await self.lang, self.bot,
                text_variable="auth external login {bot_type} accept agreement text",
            )
        customer = await Customer.get(user_id=user.id, profile_id=self.bot.group_id)
        if (not customer or customer.marketing_consent is None or not
        customer.is_accept_agreement):
            await self.state.update_data(external_login_request_id=service.request.id)
            return await agreement_processor.ask(
                self.message.from_user.id,
                await self.lang, self.bot,
            )

        lang = await self.lang
        keyboard = await get_bot_menu_keyboard(self.user, self.bot, await self.lang)

        await self.message.answer(
            await f("auth external login auth success text", lang),
            reply_markup=keyboard,
        )

        await self.set_menu_button()

        brand = await Brand.get(group_id=self.bot.group_id)

        if service.request.extra_data and service.request.extra_data.get(
                "anon_cart_token"
        ) and \
                service.request.extra_data.get("store_id"):
            sync_cart_res = await crud.sync_user_cart(
                service.request.extra_data.get("anon_cart_token"),
                int(service.request.extra_data.get("store_id")),
                self.user
            )
            if not sync_cart_res:
                details = f"Brand: {brand.name} ({brand.id})"
                details += f"\nStore ID: {service.request.extra_data.get('store_id')}"
                details += f"\nUser: {self.user.full_name} ({self.user.id})"
                details += (f"\nCart token: "
                            f"{service.request.extra_data.get('anon_cart_token')}")
                err_text = await f(
                    "store cart external login sync error", lang, details=details
                )
                await send_message_to_platform_admins(err_text, force_to_bot=False)

        if with_receipt:
            return await external_login_with_receipt(
                self.message, service.request,
                self.user, lang, brand, self.bot, keyboard,
            )
        elif with_invitation:
            return await external_login_with_invitation(
                self.message, service.request,
                self.user, lang, brand, self.bot, keyboard,
            )

        await external_login_success_auth(
            self.message, service.request,
            self.user, lang, brand, self.bot, keyboard,
        )

    async def process_link_ext_login(self, service: ExternalLoginService):
        user = None
        if (
                self.bot.bot_type == "telegram" and
                await crud.get_user_own_groups(user_id=service.request.user_id) and
                (user := await User.get_by_id(service.request.user_id)) and
                user.chat_id

        ):
            await service.request.update(
                status=schemas.ExternalLoginRequestStatusEnum.ERROR,
            )
            await self.message.answer(
                f(
                    "auth unlink owner error", await self.lang,
                    messanger="Telegram",
                )
            )
            return

        self.set_user(user)

        is_messanger_conflict = await self.check_is_messanger_user_conflict(
            service.request
        )
        if is_messanger_conflict:
            return

        messanger_user = self.message.from_user
        if service.request.user_id:
            try:
                groups_admin = await crud.get_groups_admin(
                    service.request.user_id, operation="all"
                )
            except Exception as e:
                logging.error(e, exc_info=True)
                await service.request.update(
                    status=schemas.ExternalLoginRequestStatusEnum.ERROR,
                )
                text = await f(
                    "auth link unlink manager or admin request error", await self.lang,
                    messanger="Telegram"
                )
                await self.message.answer(text)
                return

            try:
                if groups_admin:
                    for group in groups_admin:
                        await crud.delete_admin(group.id, service.request.user_id)
            except Exception as e:
                logging.error(e, exc_info=True)
                await service.request.update(
                    status=schemas.ExternalLoginRequestStatusEnum.ERROR,
                )
                text = await f("auth delete manager or admin error", await self.lang)
                await self.message.answer(text)
                return

            await self.__update_user_with_messanger_data(messanger_user, service)

            host = None
            brand = await crud.get_brand_by_group(self.bot.group_id)
            if brand:
                host = brand.domain
            if not host:
                host = service.request.continue_url

            keyboard = await get_bot_menu_keyboard(self.user, self.bot, await self.lang)
            await self.message.answer(
                await f(
                    "auth link success messanger header",
                    await self.lang,
                    messanger=MESSANGERS_NAMES[self.bot.bot_type],
                    name=self.user.name,
                    host=host,
                ),
                reply_markup=keyboard
            )

            await self.set_menu_button()

            lang = await self.lang
            return await external_login_success_auth(
                self.message, service.request,
                self.user, lang, brand, self.bot, keyboard,
            )

    async def check_is_messanger_user_conflict(
            self, request: ExternalLoginRequest,
    ):
        match self.bot.bot_type:
            case "telegram":
                messanger_user = await User.get(self.message.from_user.id)
            case "whatsapp":
                messanger_user = await User.get_by_wa_phone(self.message.from_user.id)
            case _:
                raise ValueError("Unknown bot type")

        if not messanger_user:
            return False

        if messanger_user.id == request.user_id:
            self.set_user(messanger_user)
            return False

        lang = await messanger_user.get_lang(self.bot.id)

        menu_keyboard = await get_bot_menu_keyboard(
            messanger_user, self.bot, await self.lang
        )
        if messanger_user.email:
            text = await f(
                f"auth external login {self.bot.bot_type} user with email exists", lang,
                email=messanger_user.email,
            )
        else:
            text = await f(f"auth external login {self.bot.bot_type} user exists", lang)
        await self.message.answer(text, reply_markup=menu_keyboard)

        if request.user_id:
            await request.update(
                status=schemas.ExternalLoginRequestStatusEnum.ERROR,
            )
            await self.message.answer(
                await f(
                    "external login connect loyalty use another account text", lang
                ),
                reply_markup=menu_keyboard
            )
        else:
            await request.update(
                status=schemas.ExternalLoginRequestStatusEnum.STARTED,
            )

            extra_data = request.extra_data
            if not messanger_user.email and extra_data and (
                    email := extra_data.get("email")):
                extra_data_str = ""

                if password := extra_data.get("password"):
                    extra_data_str += await f(
                        "external login extra field password", lang,
                        password="*" * len(password)
                    )

                text = await f(
                    "external login update telegram account text", lang,
                    email=email, extra_data=extra_data_str
                )
                keyboard = await get_update_or_login_keyboard(
                    "update", request.id, lang
                )
            else:
                text = await f(
                    "external login login with telegram account text", lang,
                )
                keyboard = await get_update_or_login_keyboard("login", request.id, lang)

            await self.message.answer(text, reply_markup=keyboard)

        return True

    async def __update_user_with_messanger_data(
            self,
            messanger_user: tg.types.User | wa.types.User,
            service: ExternalLoginService,
    ):
        if not self.user:
            self.set_user(await User.get_by_id(service.request.user_id))

        match self.bot.bot_type:
            case "telegram":
                data = dict(
                    with_chat_id=True,
                    chat_id=messanger_user.id,
                    first_name=messanger_user.first_name,
                    last_name=messanger_user.last_name,
                    username=messanger_user.username,
                )
            case "whatsapp":
                data = dict(
                    wa_name=messanger_user.name,
                    wa_phone=messanger_user.phone_number,
                )
            case _:
                data = {}

        if data:
            await self.user.update(**data)
