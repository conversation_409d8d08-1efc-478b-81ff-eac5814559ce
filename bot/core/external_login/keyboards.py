from typing import Literal

from db.models import ExternalLoginRequest, ClientBot, <PERSON><PERSON><PERSON>, User, Brand
from utils.redefined_classes import InlineKb, InlineBtn
from utils.text import f, fd
from .callback_data import ExternalLoginCallbackData
from ..messangers_adapters import InlineKeyboard, UrlKeyboardButton


async def get_continue_keyboard(
        request: ExternalLoginRequest,
        brand: Brand | None,
        bot: ClientBot,
        user: User,
        lang: str,
        button_text: str | None = None,
) -> InlineKeyboard:
    keyboard = InlineKeyboard()

    if not request.continue_url:
        raise ValueError("continue_url must be specified for this method")

    continue_url = request.continue_url

    if bot.bot_type != "telegram":
        short_token = await ShortToken.create(
            user.id,
            user.allowed_scopes_str,
            bot.id if bot else None,
            lang,
            params={"brand_id": brand.id} if brand else None,
        )

        if "?" not in continue_url:
            continue_url += "?"
        elif not (continue_url.endswith("?") or continue_url.endswith("&")):
            continue_url += "&"

        continue_url += f"st={short_token.token}"

    if button_text:
        text_button = button_text
    else:
        text_button = await f("auth external login continue button", lang)
    keyboard.add_buttons(UrlKeyboardButton(
        text_button,
        url=continue_url
    ))

    return keyboard


async def get_update_or_login_keyboard(
        update_or_login: Literal["update", "login"],
        external_login_request_id: int, lang: str,
):
    keyboard = InlineKb()

    texts = await fd({
        "update_button": f"external login {update_or_login} button",
        "cancel_button": "external login cancel button",
    }, lang)

    keyboard.insert(InlineBtn(
        texts["update_button"],
        callback_data=ExternalLoginCallbackData(
            external_login_request_id=external_login_request_id
        ).to_str(f"ex_login_{update_or_login}")
    ))

    keyboard.insert(InlineBtn(
        texts["cancel_button"],
        callback_data=ExternalLoginCallbackData(
            external_login_request_id=external_login_request_id
        ).to_str("ex_login_cancel")
    ))

    return keyboard
