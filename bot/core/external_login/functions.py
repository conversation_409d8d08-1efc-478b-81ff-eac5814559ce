import json
import logging

from incust_api.api import term
from psutils.receipts.schemas import Receipt as ReceiptSchema

import schemas
from core import messangers_adapters as ma
from core.external_coupon import send_coupon_info
from core.incust_referral.referral_processor import IncustReferralProcessor
from core.keyboards import get_bot_menu_keyboard
from core.loyalty.customer_service import get_or_create_incust_customer
from core.loyalty.incust_api import incust
from core.messangers_adapters.types import InlineKeyboard, UrlKeyboardButton
from db import crud
from db.models import (
    Brand, ClientBot, ExternalLoginRequest, Receipt,
    User,
)
from utils.text import f
from .keyboards import get_continue_keyboard
from .. import messangers_adapters
from ..incust.helpers import get_currency_from_store_or_brand
from ..scanner_service import ScannerService
from ..scanner_service.exceptions import BaseScannerServiceError


async def external_login_success_auth(
        obj: ma.Message | ma.types.ButtonQuery,
        request: ExternalLoginRequest,
        user: User,
        lang: str,
        brand: Brand | None,
        bot: ClientBot,
        menu_keyboard: messangers_adapters.Keyboard | None = None,
):
    answer_obj = ma.detect_answer_obj(obj)

    if menu_keyboard is None:
        menu_keyboard = await get_bot_menu_keyboard(user, bot, lang)

    await request.update(
        status=schemas.ExternalLoginRequestStatusEnum.SUCCESS,
        user=user,
    )

    success_text = await f("auth external login continue text", lang)
    if request.purpose == schemas.ExternalLoginRequestPurposeEnum.INCUST_EXTERNAL_ID:
        success_text = await f(
            "auth external login external id success",
            lang
        )

    if request.continue_url:
        try:
            return await answer_obj.answer(
                success_text,
                reply_markup=(await get_continue_keyboard(
                    request,
                    brand,
                    bot,
                    user,
                    lang,
                )).to_messanger(bot.bot_type),
            )
        except Exception as e:
            logging.error(e, exc_info=True)

    await answer_obj.answer(
        success_text + "\n\n" + await f("auth external login back to page text", lang),
        reply_markup=menu_keyboard,
    )


async def get_receipt_msg_and_coupons(
        extra_data: dict, lang: str, brand: Brand,
        user: User,
        obj: ma.Message | ma.types.ButtonQuery,
        request: ExternalLoginRequest, bot: ClientBot, store_id: int | None = None
    ) -> tuple[str | None, list[schemas.CouponShowData] | None]:
    answer_obj = ma.detect_answer_obj(obj)

    receipt_schema = ReceiptSchema(**extra_data)

    scan_service = ScannerService(user, lang, brand)

    loyalty_settings = await crud.get_loyalty_settings_for_context(
        "store" if store_id else "brand",
        schemas.LoyaltySettingsData(
            brand_id=brand.id,
            store_id=store_id,
        )
    )

    if not loyalty_settings:
        logging.warning(
            f"Loyalty settings not found for brand {brand.id}, store {store_id}"
        )
        await request.update(
            status=schemas.ExternalLoginRequestStatusEnum.ERROR,
        )
        err_txt = await f("auth external login receipt process error", lang)
        await answer_obj.answer(err_txt)
        return None, None

    # Синхронізуємо користувача з InCust
    try:
        await get_or_create_incust_customer(user, loyalty_settings, lang)
    except Exception as ex:
        logging.error(f"Failed to sync customer: {ex}", exc_info=True)
        # Продовжуємо виконання, можливо обробка чеку спрацює без синхронізації

    err_txt = None
    res = None
    try:
        res = await scan_service.process(
            with_make_check=True,
            receipt_schema=receipt_schema,
            store_id=store_id,
        )
    except BaseScannerServiceError as ex:
        logging.error(ex, exc_info=True)
        if not err_txt:
            err_txt = ex.message
    except Exception as ex:
        logging.error(f"Error processing receipt: {ex}", exc_info=True)
        err_txt = await f("loyalty processing error", lang)

    if err_txt:
        await request.update(
            status=schemas.ExternalLoginRequestStatusEnum.ERROR,
        )
        err_txt = (f'{await f("auth external login receipt process error", lang)}\n'
                   f'{err_txt}')
        await answer_obj.answer(err_txt), None
        return None, None

    if res and res.receipt_id and res.check:
        receipt = await Receipt.get(res.receipt_id)
        coupons = []
        if receipt.incust_transaction:
            res.check.transaction = term.m.Transaction(
                **receipt.incust_transaction
            )
            if res.check.transaction and res.check.transaction.id:
                try:
                    async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
                        transaction_check = await api.transaction_check(
                            res.check.transaction.id
                        )
                except term.ApiException as ex:
                    logging.error(f"Error getting customer coupons: {ex.reason}")
                    transaction_check = None
                except Exception as ex:
                    logging.error(f"Unexpected error getting customer coupons: {ex}")
                    transaction_check = None
                
                if transaction_check:
                    try:
                        if transaction_check and getattr(transaction_check, "emitted_coupons"):
                            # Збагачуємо купони через coupon_service
                            from core.loyalty import coupon_service
                            
                            coupons = []
                            for voucher in transaction_check.emitted_coupons:
                                enriched_coupon = await coupon_service.prepare_coupon_for_display(
                                    voucher,
                                    brand,
                                    loyalty_settings,
                                    user,
                                    lang,
                                    
                                )
                                if enriched_coupon:
                                    coupons.append(enriched_coupon)
                    except Exception as ex:
                        logging.error(f"Failed to get coupons: {ex}", exc_info=True)
                        coupons = []
        return await scan_service.get_rewards_text(
            int(res.receipt_id), res.check, bot, store_id
        ), coupons
    else:
        await request.update(
            status=schemas.ExternalLoginRequestStatusEnum.ERROR,
        )
        await answer_obj.answer(
            await f("auth external login receipt process error", lang),
        )
        return None, None


async def external_login_with_invitation(
        obj: ma.Message | ma.types.ButtonQuery,
        request: ExternalLoginRequest,
        user: User,
        lang: str,
        brand: Brand | None,
        bot: ClientBot,
        menu_keyboard: messangers_adapters.Keyboard | None = None,
):
    answer_obj = ma.detect_answer_obj(obj)

    if not request.extra_data or not request.extra_data.get("referral_code", None):
        await request.update(
            status=schemas.ExternalLoginRequestStatusEnum.ERROR,
        )
        return await answer_obj.answer(
            await f("auth external login invitation extra data error", lang),
        )

    ref_code = request.extra_data.get("referral_code")

    if menu_keyboard is None:
        menu_keyboard = await get_bot_menu_keyboard(user, bot, lang)

    initiator = "external_login"
    debug_data = {
        "referral_code": ref_code,
        "invating_user_first_name": user.first_name,
        "invating_user_last_name": user.last_name,
        "invating_user_id": user.id,
        "bot_id": bot.id,
        "bot_name": bot.display_name,
        "main_keyboard_type": str(type(menu_keyboard)),
        "brand_id": brand.id if brand else None,
    }

    try:
        if not brand:
            brand = await crud.get_brand_by_group(bot.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
            )
        )

        if not loyalty_settings:
            logging.warning(f"Loyalty settings not found for brand {brand.id}")
            await answer_obj.answer(
                await f("auth external login invitation process error", lang),
                reply_markup=menu_keyboard,
            )
            return await request.update(
                status=schemas.ExternalLoginRequestStatusEnum.ERROR,
            )

        processor = IncustReferralProcessor(
            answer_obj, None, bot, user, lang, menu_keyboard
        )
        currency = await get_currency_from_store_or_brand(brand.id)
        await processor.accept_invitation(
            ref_code, loyalty_settings, currency,
            debug_data=debug_data, initiator=initiator,
        )
    except Exception as ex:
        debugger = logging.getLogger("debugger.incust.referral-invite")
        debug_text = (f"sending referral inviting message with keyboard "
                      f"external_login_with_invitation_error_{initiator}")
        debug_text += f"\n {json.dumps(debug_data, indent=4, ensure_ascii=False)}"
        debugger.debug(debug_text)

        logging.error(ex, exc_info=True)
        await answer_obj.answer(
            await f("auth external login invitation process error", lang),
            reply_markup=menu_keyboard,
        )
        return await request.update(
            status=schemas.ExternalLoginRequestStatusEnum.ERROR,
        )

    await request.update(
        status=schemas.ExternalLoginRequestStatusEnum.SUCCESS,
        user=user,
    )


async def external_login_with_receipt(
        obj: ma.Message | ma.types.ButtonQuery,
        request: ExternalLoginRequest,
        user: User,
        lang: str,
        brand: Brand | None,
        bot: ClientBot,
        menu_keyboard: messangers_adapters.Keyboard | None = None,
):
    answer_obj = ma.detect_answer_obj(obj)

    if not request.extra_data or not request.extra_data.get("receipt", None):
        await request.update(
            status=schemas.ExternalLoginRequestStatusEnum.ERROR,
        )
        return await answer_obj.answer(
            await f("auth external login receipt extra data error", lang),
        )
    if request.extra_data and request.extra_data.get("receipt"):
        try:
            if menu_keyboard is None:
                menu_keyboard = await get_bot_menu_keyboard(user, bot, lang)

            receipt_msg, coupons = await get_receipt_msg_and_coupons(
                request.extra_data.get("receipt"),
                lang, brand, user, answer_obj,
                request, bot, request.extra_data.get("store_id", None)
            )

            if receipt_msg:
                await request.update(
                    status=schemas.ExternalLoginRequestStatusEnum.SUCCESS,
                    user=user,
                )

                await answer_obj.answer(
                    receipt_msg,
                    reply_markup=menu_keyboard,
                )

            for coupon in coupons:
                try:
                    await send_coupon_info(
                        coupon,
                        user,
                        bot,
                        lang,
                        brand.id,
                        store_id=request.extra_data.get("store_id", None),
                    )
                except Exception as ex:
                    logging.error(ex, exc_info=True)

            text = await f("auth external login receipt end process text", lang)
            button_text = await f("auth external login receipt continue button", lang)
            if request.continue_url:
                reply_markup = (await get_continue_keyboard(
                    request,
                    brand,
                    bot,
                    user,
                    lang,
                    button_text,
                )).to_messanger(bot.bot_type)

                return await answer_obj.answer(
                    text,
                    reply_markup=reply_markup,
                )

            if bot.bot_type == "telegram":
                keyboard = InlineKeyboard()
                keyboard.add_buttons(
                    UrlKeyboardButton(
                        await f("web app show wallet button", lang),
                        url=brand.get_url("profile/loyalty")
                    )
                )
                return await answer_obj.answer(
                    text,
                    reply_markup=keyboard.to_messanger(bot.bot_type),
                )
            elif bot.bot_type == "whatsapp":
                text += "\n\n" + await f("web app show wallet button", lang) + ":"
                link = await brand.get_short_token_url(
                    user, bot.id, lang, "profile/loyalty"
                )
                text += "\n" + link
                return await answer_obj.answer(text)

        except Exception as ex:
            logging.error(ex, exc_info=True)
            await request.update(
                status=schemas.ExternalLoginRequestStatusEnum.ERROR,
            )
            await answer_obj.answer(
                await f("auth external login receipt process error", lang),
            )
