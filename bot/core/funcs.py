import json
import logging
from functools import wraps

from fastapi import HTTPException
from incust_api.api import client, term

from core.loyalty.exceptions import InCustAPIError
from loggers import J<PERSON>NLogger
from utils.text import f


async def handle_standard_exceptions(
        ex,
        lang = None,
        debugger: logging.Logger |  JSONLogger | None = None
):
    """Standardized exception handler for Incust API routes"""
    if not debugger:
        debugger = logging.getLogger("debugger.external.incust")
    if not lang:
        lang = "en"
    if isinstance(ex, (client.ApiException, term.ApiException)):
        # Використовуємо статус код та причину з ApiException
        status_code = getattr(ex, 'status', 400)
        detail = getattr(ex, 'reason', str(ex))

        try:
            body = getattr(ex, 'body', None)
            if body:
                body_data = json.loads(body) if isinstance(body, str) else body
                if isinstance(body_data, dict) and 'message' in body_data:
                    detail = body_data['message']
        except (json.JSONDecodeError, AttributeError, TypeError):
            # Якщо не вдалося розпарсити JSON, використовуємо reason
            pass
            
        raise HTTPException(
            status_code=status_code,
            detail=detail
        )
    elif isinstance(ex, InCustAPIError):
        raise HTTPException(
            status_code=ex.status_code,
            detail=ex.detail  # <-- ОСЬ ТУТ ВИКОРИСТОВУЄТЬСЯ ОРИГІНАЛЬНИЙ ТЕКСТ
        )
    elif isinstance(ex, HTTPException):
        raise ex
    else:
        debugger.error(ex, exc_info=True)
        raise HTTPException(
            status_code=400,
            detail=await f("STORE_BRAND_LOYALTY_ERROR_HEADER", lang) if lang else str(
                ex
            )
        )


def handle_loyalty_api_exceptions(func):
    """Декоратор для автоматичної обробки ApiException в методах/функціях лояльності"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except (client.ApiException, term.ApiException) as ex:
            logger = logging.getLogger("debugger.loyalty.api")
            logger.error(f"Loyalty API error in {func.__name__}: {ex}", exc_info=True)
            
            # Витягуємо оригінальний текст з API відповіді
            detail = getattr(ex, 'reason', str(ex))
            try:
                import json
                body = getattr(ex, 'body', None)
                if body:
                    body_data = json.loads(body) if isinstance(body, str) else body
                    if isinstance(body_data, dict) and 'message' in body_data:
                        detail = body_data['message']
            except (json.JSONDecodeError, AttributeError, TypeError):
                pass
            
            # Підіймаємо HTTPException з оригінальним текстом
            raise HTTPException(
                status_code=getattr(ex, 'status', 400),
                detail=detail
            )
    return wrapper
