from aiogram import types, Dispatcher

from client.main.keyboards import get_menu_keyboard
from db.models import User, ClientBot, UserClientBotActivity
from utils.text import f


async def cmd_recommend_bot_deep_link(
        message: types.Message,
        user: User,
        deep_link_data: dict,
        is_created_bot_activity: bool,
        lang: str,
):
    client_bot_id = ClientBot.get_current_bot_id()
    if not client_bot_id:
        return

    bot = await ClientBot.get(client_bot_id)

    keyboard = await get_menu_keyboard(user, bot, lang)

    if not is_created_bot_activity:
        return await message.answer(await f("old user followed recommend bot link", lang), reply_markup=keyboard)

    recommender_id = deep_link_data.get("r")

    if user.id == recommender_id:
        return await message.answer(await f("cant accept own inviting", lang), reply_markup=keyboard)

    recommender = await User.get_by_id(recommender_id)
    user_bot_activity = await UserClientBotActivity.get_current()
    await user_bot_activity.set_recommender(recommender)

    text = await f("accepted inviting to bot", lang, recommender_full_name=recommender.full_name)
    await message.answer(text, reply_markup=keyboard)

    await message.bot.send_message(recommender.chat_id, await f("user accepted inviting", lang, full_name=user.name))


def register_recommend_bot_deep_link(dp: Dispatcher):
    dp.register_message_handler(
        cmd_recommend_bot_deep_link,
        commands="start",
        deep_link="rb",
        state="*",
    )
