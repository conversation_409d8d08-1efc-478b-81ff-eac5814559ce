from aiogram import Dispatcher, types

from core.bot.handlers import Menu<PERSON><PERSON>on<PERSON>ilter
from core.chat.virtual_manager.functions import start_virtual_manager_chat
from db.models import ClientBot, User
from utils.keyboards import make_share_button
from utils.redefined_classes import InlineKb
from utils.text import f


async def share_bot_button_handler(
        message: types.Message, user: User, bot: ClientBot, lang: str
):
    if bot.share_bot_vm_id:
        return await start_virtual_manager_chat(
            user,
            bot.share_bot_vm_id,
            bot.group_id,
            bot,
        )

    recommender_link = bot.get_link_for_share(user.id)
    button_text = await f("share recommender link button", lang)
    keyboard = InlineKb().insert(await make_share_button(button_text, recommender_link))
    await message.answer(
        await f("client bot share bot text", lang, link=recommender_link),
        reply_markup=keyboard
    )


def register_share_bot_handlers(dp: Dispatcher):
    dp.register_message_handler(
        share_bot_button_handler,
        MenuButtonFilter("main", "share bot button"),
        content_types=types.ContentTypes.TEXT,
        state="*",
    )
