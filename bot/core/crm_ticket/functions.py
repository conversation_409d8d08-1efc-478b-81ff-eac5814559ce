import logging

from config import CRM_HOST
from core.billing.quota_processor import BillingQuotaProcessor
from core.crm_ticket.notifications_sender import TicketNotificationsSender
from core.kafka.producer.functions import (
    add_push_notifications_for_action,
)
from core.kafka.producer.helpers import build_fcm_message
from core.webhooks.functions import add_webhook_event, prepare_data_for_ticket_webhook
from db import crud
from db.models import (
    Brand, CRMTicket, ClientBot, Group, User, VirtualManager,
    VirtualManagerChat,
)
from schemas import (
    AuthSourceEnum, BillingProductCode, CRMTicketSourceEnum, CRMTicketStatusEnum,
    CRMTicketStatusInitiatedByEnum, WebhookActionEnum, WebhookEntityEnum,
    WebhookTicketDataSchema,
)
from utils.platform_admins import send_message_to_platform_admins
from utils.text import fd


async def create_crm_ticket(
        ticket_title: str,
        source: CRMTicketSourceEnum,
        user: User,
        group: Group,
        initiated_by: CRMTicketStatusInitiatedByEnum,
        initiated_by_user: User | None = None,
        bot: ClientBot | None = None,
        internal_comment: str | None = None,
        vmc: VirtualManagerChat | None = None,
        notify_user: bool | None = None,
):
    if not await BillingQuotaProcessor(
            vmc.group_id,
            BillingProductCode.TICKET,
            exception_on_error="suspended",
            return_on_error="false",
            user=user,
            bot=bot,
    ).record_usage():
        return

    ticket = await crud.create_crm_ticket(
        ticket_title, source, user, group,
        initiated_by, initiated_by_user, bot,
        internal_comment, vmc,
    )
    if notify_user is None:
        notify_user = not vmc

    await send_crm_ticket_notifications(
        ticket, user, group, bot, notify_user=notify_user
    )

    data = await prepare_data_for_ticket_webhook(ticket, group, user)
    await add_webhook_event(
        entity=WebhookEntityEnum.TICKET,
        entity_id=ticket.id,
        action=WebhookActionEnum.CREATED,
        group_id=group.id,
        data=data.dict(),
        data_type=WebhookTicketDataSchema,
    )


async def vm_create_crm_ticket(
        user: User,
        bot: ClientBot,
        vmc: VirtualManagerChat | int | None,
        ticket_title: str | None = None,
        if_not_exists: bool = False,
):
    vmc = vmc if isinstance(vmc, VirtualManagerChat) else await VirtualManagerChat.get(
        vmc
    )

    if vmc:
        group_id = vmc.group_id
    else:
        group_id = bot.group_id
    group = await Group.get(group_id)

    if not ticket_title:
        if vmc:
            vm = await VirtualManager.get(vmc.virtual_manager_id)
            ticket_title = vm.name
        else:
            ticket_title = group.name

    if if_not_exists and (existing := await crud.get_last_crm_ticket_for_user(
            ticket_title,
            user.id,
            group_id,
            bot.id,
            statuses=(
                    CRMTicketStatusEnum.OPEN_UNCONFIRMED,
                    CRMTicketStatusEnum.OPEN_CONFIRMED,
            )
    )):
        return existing

    return await create_crm_ticket(
        ticket_title,
        CRMTicketSourceEnum.USER,
        user, group,
        CRMTicketStatusInitiatedByEnum.USER,
        bot=bot, vmc=vmc,
    )


async def set_crm_ticket_status(
        ticket: CRMTicket,
        status: CRMTicketStatusEnum,
        initiated_by: CRMTicketStatusInitiatedByEnum,
        initiated_by_user: User | None = None,
        header: str | None = None,
        message: str | None = None,
        internal_comment: str | None = None,
        notify_user: bool = True,
        ignore_session_id: int | None = None,
):
    if (initiated_by != CRMTicketStatusInitiatedByEnum.MANAGER and status ==
            CRMTicketStatusEnum.CLOSED):
        raise ValueError(f"{initiated_by.value} cannot set crm ticket status to CLOSED")

    await crud.set_crm_ticket_status(
        ticket, status,
        initiated_by, initiated_by_user,
        header, message, internal_comment,
    )
    await send_crm_ticket_notifications(
        ticket, notify_user=notify_user,
        header=header, message=message,
        ignore_session_id=ignore_session_id
    )

    group = await Group.get(ticket.group_id)
    user = await User.get_by_id(ticket.user_id)
    data = await prepare_data_for_ticket_webhook(ticket, group, user)
    await add_webhook_event(
        entity=WebhookEntityEnum.TICKET,
        entity_id=ticket.id,
        action=WebhookActionEnum.CHANGE_STATUS,
        group_id=group.id,
        data=data.dict(),
        data_type=WebhookTicketDataSchema,
    )


async def send_crm_ticket_notifications(
        ticket: CRMTicket,
        ticket_user: User | None = None,
        group: Group | None = None,
        bot: ClientBot | None = None,
        notify_user: bool = True,
        header: str | None = None,
        message: str | None = None,
        ignore_session_id: int | None = None,
):
    try:
        assert not bot or bot.id == ticket.bot_id, "Bot does not match ticket bot"
        assert not ticket_user or ticket_user.id == ticket.user_id, (
            "User does not match ticket user"
        )

        if not ticket_user:
            ticket_user = await User.get_by_id(ticket.user_id)
        if not group:
            group = await Group.get(ticket.group_id)
        if not bot:
            bot = await ClientBot.get(ticket.bot_id)

        brand = await Brand.get(group_id=group.id)
        try:
            await TicketNotificationsSender(
                ticket,
                await ticket_user.get_lang(bot),
                brand,
                group,
                ticket_user,
                bot,
                notify_user=notify_user,
                header=header,
                message=message,
            ).start()
        except Exception as e:
            logging.error(
                f"TicketNotificationsSender.start() FAILED: ({str(e)}", exc_info=True
            )
            await send_message_to_platform_admins(
                f"An error occurred while sending ticket notifications: {str(e)}\n"
                f"Ticket id: {ticket.id}\n"
                f"Profile: {group.name}({group.id})\n"
                f"Ticket user: {ticket_user.name}({ticket_user.id})\n"
            )

        await send_crm_ticket_push_notifications(
            ticket, ticket_user, group, bot, ignore_session_id=ignore_session_id
        )
    except Exception as e:
        logging.error(e, exc_info=True)

        group = await Group.get(ticket.group_id)
        ticket_user = await User.get_by_id(ticket.user_id)

        logging.error(f"send_crm_ticket_notifications FAILED: ({str(e)}", exc_info=True)
        await send_message_to_platform_admins(
            f"An error occurred while sending ticket notifications: {str(e)}\n"
            f"Ticket id: {ticket.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Ticket user: {ticket_user.name}({ticket_user.id})\n"
        )


async def send_crm_ticket_push_notifications(
        ticket: CRMTicket,
        ticket_user: User,
        group: Group,
        bot: ClientBot | None = None,
        ignore_session_id: int | None = None,
):
    try:
        async def get_message(user: User):
            bot_and_group_texts = []
            if bot:
                bot_and_group_texts.append(bot.display_name)
            bot_and_group_texts.append(group.name)

            texts = await fd(
                {
                    "title": {
                        "variable": f"crm ticket {ticket.status.value} notification "
                                    f"title",
                        "text_kwargs": {
                            "ticket_id": ticket.id,
                            "title": ticket.title,
                        }
                    },
                    "body": {
                        "variable": "crm ticket notification body",
                        "text_kwargs": {
                            "bot_and_group_name": " | ".join(bot_and_group_texts),
                            "user_name": ticket_user.name,
                            "internal_comment": ticket.internal_comment or "-",
                        }
                    }
                }, user.lang
            )

            return build_fcm_message(
                "ticket",
                ticket.id,
                ticket.crm_tag,
                texts["title"].strip(),
                texts["body"].strip(),
                delete_notification=(
                        ticket.status != CRMTicketStatusEnum.OPEN_UNCONFIRMED
                ),
                apns_priority=(
                    "10" if
                    ticket.status == CRMTicketStatusEnum.OPEN_UNCONFIRMED
                    else "5"
                ),
                link=f"{CRM_HOST}/ticket/"
                     f"{ticket.id}?listType=inbox&itemIdField=ticketId"
            )

        return await add_push_notifications_for_action(
            AuthSourceEnum.CRM_WEB, AuthSourceEnum.CRM_APP,
            action="crm_ticket:read",
            available_data={
                "profile_id": ticket.group_id,
                "ticket_id": ticket.id,
            },
            message=get_message,
            ignore_session_id=ignore_session_id,
        )
    except Exception as e:
        logging.error(
            f"send_crm_ticket_push_notifications FAILED: ({str(e)}", exc_info=True
        )
        await send_message_to_platform_admins(
            f"An error occurred while sending ticket push notifications: {str(e)}\n"
            f"Ticket id: {ticket.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Ticket user: {ticket_user.name}({ticket_user.id})\n"
        )
