from psutils.text import replace_variables_in_text

from db.models import Brand, CRMTicket, ClientBot, Group, User
from schemas import BaseTemplateSchema
from schemas.incust.base import CouponShowData
from utils.text import fd
from ..notifications.base_notifications_sender import BaseNotificationsSender


class TicketTemplate(BaseTemplateSchema):
    TEMPLATE_PATH = "ticket_email.html"

    header: str
    message: str

    from_name: str
    coupons: list[CouponShowData] | None = None


class TicketNotificationsSender(BaseNotificationsSender[TicketTemplate]):
    BOT_TEMPLATE_PATH = "ticket_messanger.html"

    def __init__(
            self,
            ticket: CRMTicket,
            lang: str,
            brand: Brand,
            group: Group,
            user: User,
            bot: ClientBot | None = None,
            notify_user: bool = True,
            header: str | None = None,
            message: str | None = None,
    ):
        super().__init__(lang, brand, group, user, bot)
        self.notify_user = notify_user
        self.ticket = ticket
        self.header = header
        self.message = message

    async def build_template(self) -> TicketTemplate:
        texts = await fd(
            {
                "header": f"crm ticket {self.ticket.status.value} user notification "
                          f"header",
                "message": f"crm ticket {self.ticket.status.value} user notification "
                           f"message"
            },
            self.lang,
        )

        text_kwargs = {
            "name": self.user.name,
            "ticket_id": self.ticket.id,
        }

        return TicketTemplate(
            header=replace_variables_in_text(
                self.header or texts["header"], text_kwargs
            ),
            message=replace_variables_in_text(
                self.message or texts["message"], text_kwargs
            ),
            from_name=self.group.name,
        )
