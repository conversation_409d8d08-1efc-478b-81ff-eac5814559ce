from core import messangers_adapters as ma
from db import crud
from db.models import Client<PERSON>ot, User, VirtualManagerChat
from schemas import CRMTicketStatusEnum, CRMTicketStatusInitiatedByEnum

from ..callback_data import CRMTicketCallbackData, CRMTicketCreateCallbackData
from ..functions import set_crm_ticket_status, vm_create_crm_ticket


@ma.handler.button()
async def ticket_button_handler(
        _,
        user: User,
        data: CRMTicketCreateCallbackData,
        bot: ClientBot,
):
    vmc = await VirtualManagerChat.get(data.vmc_id)
    await vm_create_crm_ticket(user, bot, vmc, data.ticket_title, data.if_not_exists)


@ma.handler.button()
async def ticket_confirm_status_button_handler(
        _,
        user: User,
        data: CRMTicketCallbackData,
        bot: ClientBot,
):
    ticket = await crud.get_crm_ticket_for_user(user.id, bot, data.ticket_title, data.vmc_id)
    if not ticket:
        return
    await set_crm_ticket_status(
        ticket,
        CRMTicketStatusEnum.OPEN_CONFIRMED,
        CRMTicketStatusInitiatedByEnum.VM,
        notify_user=False,
    )


@ma.handler.button()
async def ticket_cancel_status_button_handler(
        _,
        user: User,
        data: CRMTicketCallbackData,
        bot: ClientBot,
):
    ticket = await crud.get_crm_ticket_for_user(user.id, bot, data.ticket_title, data.vmc_id)
    if not ticket:
        return
    await set_crm_ticket_status(
        ticket,
        CRMTicketStatusEnum.CANCELED,
        CRMTicketStatusInitiatedByEnum.VM,
        notify_user=False,
    )


def register_crm_ticket_button_handlers(dp: ma.DispatcherType):
    ticket_button_handler.setup(
        dp,
        CRMTicketCreateCallbackData.get_filter("ticket", return_column_name="data")
    )
    ticket_confirm_status_button_handler.setup(
        dp,
        CRMTicketCallbackData.get_filter("ticket_confirm", return_column_name="data")
    )
    ticket_cancel_status_button_handler.setup(
        dp,
        CRMTicketCallbackData.get_filter("ticket_cancel", return_column_name="data")
    )
