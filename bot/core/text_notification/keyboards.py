from aiogram import types

from utils.text import f
from .helpers import get_crm_text_notification_link
from ..helpers import get_crm_chat_link


async def get_text_notification_keyboard(text_notification_id: int, lang: str, chat_id: int | None = None):
    keyboard = types.InlineKeyboardMarkup()
    keyboard.row(
        types.InlineKeyboardButton(
            text=await f("service bot open in crm button", lang),
            web_app=types.WebAppInfo(
                url=get_crm_text_notification_link(text_notification_id)
            )
        )
    )
    if chat_id:
        keyboard.row(
            types.InlineKeyboardButton(
                text=await f("service bot chat in crm button", lang),
                web_app=types.WebAppInfo(
                    url=get_crm_chat_link(chat_id)
                )
            )
        )
    return keyboard
