from starlette import status

from schemas import TextNotificationTypeEnum
from utils.exceptions import ErrorWithHTTPStatus


class TextNotificationMenuInStoreRequiredError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "text notification menu in store required error"

    def __init__(self, type_: TextNotificationTypeEnum):
        super().__init__(
            type=type_.value,
            detail_data={
                "error_code": "menu_in_store_required",
                "type": type_.value,
            }
        )


class TextNotificationTextRequiredError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "text notification text required error"

    def __init__(self, type_: TextNotificationTypeEnum):
        super().__init__(
            type=type_.value,
            detail_data={
                "error_code": "text_required",
                "type": type_.value
            }
        )
