from core.custom_texts import ct
from core.custom_texts.models import CustomTextsModel
from core.text_notification.exceptions import TextNotificationMenuInStoreRequiredError
from core.text_notification.notifications import send_text_notification
from db.models import ClientBot, Group, MenuInStore, TextNotification, Store, User
from schemas import TextNotificationTargetEnum, TextNotificationTypeEnum


async def create_and_send_text_notification(
        target: TextNotificationTargetEnum,
        type_: TextNotificationTypeEnum,
        profile: Group,
        store: Store | None = None,
        from_user: User | None = None,
        from_bot: ClientBot | None = None,
        menu_in_store: MenuInStore | None = None,
        text: str | None = None,
        menu_in_store_ct_obj: CustomTextsModel | None = None,
):
    if type_ in (
            TextNotificationTypeEnum.WAITER,
            TextNotificationTypeEnum.PAYMENT_CASH,
            TextNotificationTypeEnum.PAYMENT_CARD,
    ):
        if not menu_in_store:
            raise TextNotificationMenuInStoreRequiredError(type_)
        if not menu_in_store_ct_obj:
            menu_in_store_ct_obj = await CustomTextsModel.from_object(menu_in_store)

        if not store and menu_in_store.store_id:
            store = await Store.get(menu_in_store.store_id)

        if type_ == TextNotificationTypeEnum.WAITER:
            keys = ("waiter", "call_text")
        else:
            keys = ("payments", "notifications", f"{type_.value[8:]}_text")

        type_text = await ct(
            menu_in_store_ct_obj, profile.lang, *keys,
            comment=menu_in_store.comment or "",
            store_name=await menu_in_store.get_calculated_in_store_name(store),
        )
        text = f"{type_text}: {text}" if text else type_text
    elif type_ == TextNotificationTypeEnum.TEXT and not text:
        raise TextNotificationMenuInStoreRequiredError(type_)

    notification = await TextNotification.create(
        target=target,
        type=type_,
        profile=profile,
        store=store,
        from_user=from_user,
        from_bot=from_bot,
        menu_in_store=menu_in_store,
        text=text,
    )
    await send_text_notification(notification, profile, from_user)
