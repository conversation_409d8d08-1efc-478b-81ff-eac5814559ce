import logging

from config import SERVICE_BOT_API_TOKEN, SERVICE_BOT_USERNAME
from core.group.functions import get_custom_fields
from core.kafka.producer.functions import add_push_notifications_for_action, add_telegram_notifications_for_action
from core.kafka.producer.helpers import build_fcm_message
from core.webhooks.functions import prepare_data_for_notification_webhook, add_webhook_event
from core.text_notification.helpers import get_crm_text_notification_link
from core.text_notification.keyboards import get_text_notification_keyboard
from db import crud
from db.models import Group, TextNotification, User
from schemas import AuthSourceEnum, ChatTypeEnum, WebhookTextNotificationSchema, WebhookEntityEnum, WebhookActionEnum
from utils.platform_admins import send_message_to_platform_admins
from utils.text import fd


async def send_text_notification(
    text_notification: TextNotification,
    profile: Group | None = None,
    text_notification_from_user: User = None,
    ignore_session_id: int | None = None,
    ignore_user_id: int | None = None,
):
    assert not profile or profile.id == text_notification.profile_id, "Group does not match group group"
    assert (
            not text_notification_from_user or
            text_notification_from_user.id == text_notification.from_user_id
    ), "text_notification_from_user does not match text_notification's from user"

    if not profile:
        profile = await Group.get(text_notification.profile_id)
    if not text_notification_from_user:
        text_notification_from_user = await User.get_by_id(text_notification.from_user_id)

    webhook_data: WebhookTextNotificationSchema = await prepare_data_for_notification_webhook(
        text_notification
    )
    await add_webhook_event(
        entity=WebhookEntityEnum.TEXT_NOTIFICATION,
        entity_id=text_notification.id,
        action=WebhookActionEnum.CREATED if not text_notification.is_read else WebhookActionEnum.CHANGE_READ,
        group_id=profile.id,
        data=webhook_data.dict(),
        data_type=WebhookTextNotificationSchema,
    )
    await send_text_notification_push(text_notification, profile, text_notification_from_user, ignore_session_id)
    await add_text_notification_for_service_bot(
        text_notification, profile,
        text_notification_from_user,
        ignore_user_id=ignore_user_id,
    )


async def send_text_notification_push(
    text_notification: TextNotification,
    profile: Group,
    text_notification_from_user: User = None,
    ignore_session_id: int | None = None,
):
    try:
        async def get_message(user: User):

            texts = await fd(
                {
                    "title": {
                        "variable": (
                            "crm text notification read title"
                            if text_notification.is_read else
                            "crm text notification new title"
                        ),
                        "text_kwargs": {
                            "notification_id": text_notification.id,
                            "text": text_notification.text,
                        }
                    },
                    "body": {
                        "variable": f"crm text notification body",
                        "text_kwargs": {
                            "group_name": profile.name,
                            "user_name": text_notification_from_user.name if text_notification_from_user else "",
                        }
                    },
                },
                user.lang,
            )

            title = texts["title"]
            body = texts["body"].strip()

            return build_fcm_message(
                "text_notification",
                text_notification.id,
                text_notification.crm_tag,
                title,
                body,
                delete_notification=text_notification.is_read,
                apns_priority="5" if text_notification.is_read else "10",
                add_data_texts=not text_notification.is_read,
                link=get_crm_text_notification_link(text_notification.id),
            )

        return await add_push_notifications_for_action(
            AuthSourceEnum.CRM_WEB, AuthSourceEnum.CRM_APP,
            action="crm_text_notification:read",
            available_data={
                "profile_id": text_notification.profile_id,
                "text_notification_id": text_notification.id,
            },
            message=get_message,
            ignore_session_id=ignore_session_id,
        )
    except Exception as e:
        logging.getLogger("error.add-notifications.text-notification").error(
            "send_text_push_notifications "
            f"FAILED: ({repr(e)})",
            exc_info=True,
        )
        user_info = f"TextNotification user: {text_notification_from_user.name}({text_notification_from_user.id})" \
            if text_notification_from_user else ""
        await send_message_to_platform_admins(
            f"An error occurred while sending text push notifications: {str(e)}\n"
            f"TextNotification id: {text_notification.id}\n"
            f"Profile: {profile.name}({profile.id})\n"
            f"{user_info}"
        )


async def add_text_notification_for_service_bot(
    text_notification: TextNotification,
    profile: Group,
    text_notification_from_user: User = None,
    ignore_user_id: int | None = None,
):
    try:
        if text_notification_from_user and text_notification.from_bot_id:
            chat = await crud.get_or_create_chat(
                ChatTypeEnum.USER_WITH_GROUP,
                text_notification_from_user.id,
                profile.id,
                text_notification.from_bot_id,
            )
        else:
            chat = None
        chat_id = chat.id if chat else None

        async def get_sending_data(manager_user: User):
            manager_lang = manager_user.lang

            if text_notification_from_user:
                custom_fields = await get_custom_fields(text_notification_from_user, profile, manager_lang)
            else:
                custom_fields = ""
            text_kwargs = {
                "text": text_notification.text,
                "custom_fields": custom_fields,
                "manager_full_name": manager_user.name,
            }

            text = await profile.get_notification_text(
                f"from_user_message", manager_lang,
                text_notification_from_user,
                **text_kwargs
            )

            return {
                "content_type": "text",
                "text": text,
                "keyboard": (
                    await get_text_notification_keyboard(
                        text_notification.id,
                        manager_lang,
                        chat_id,
                    )
                ).to_python()
            }

        return await add_telegram_notifications_for_action(
            "service", SERVICE_BOT_USERNAME, SERVICE_BOT_API_TOKEN,
            action="crm_text_notification:read",
            available_data={
                "profile_id": text_notification.profile_id,
                "text_notification_id": text_notification.id,
            },
            message=get_sending_data,
            ignor_user_id=ignore_user_id,
        )
    except Exception as e:
        logging.getLogger("error.add-notifications.text-notification").error(
            "send_text_notifications_to_telegram "
            f"FAILED: ({repr(e)})",
            exc_info=True,
        )
        user_info = f"TextNotification user: {text_notification_from_user.name}({text_notification_from_user.id})" \
            if text_notification_from_user else ""
        await send_message_to_platform_admins(
            f"An error occurred while sending text notifications to service bot: {str(e)}\n"
            f"TextNotification id: {text_notification.id}\n"
            f"Profile: {profile.name}({profile.id})\n"
            f"{user_info}"
        )
