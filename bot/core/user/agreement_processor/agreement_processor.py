import schemas
from client.main.functions import set_menu_button
from core import messangers_adapters as ma
from core.external_login.functions import (
    external_login_success_auth, external_login_with_invitation,
    external_login_with_receipt,
)
from core.keyboards import get_bot_menu_keyboard
from core.loyalty.customer_service import get_or_create_incust_customer
from core.messangers_adapters import Dispatcher, types
from core.user.functions import (
    detect_messanger_user_lang,
)
from db import crud
from db.models import (
    Brand, ClientBot, Customer, ExternalLoginRequest, Group, User,
    UserClientBotActivity,
)
from schemas import BotTypeLiteral
from utils.date_time import utcnow
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from .keyboards import get_bot_birthday_keyboard
from .states import UserBirthDate


class AgreementProcessor:
    def __init__(
            self,
            prefix: str = "agreement_",
            update_state_key: str = "update",
            continue_button_key: str = "continue",
            decline_button_key: str = "decline",
            settings_button_key: str = "settings",
            decline_marketing_key: str = "decline_marketing",
            continue_setting_button_key: str = "continue_setting",
            continue_with_marketing: str = "continue_with_marketing",
            continue_without_marketing: str = "continue_without_marketing",
    ):
        self.prefix = prefix
        self.update_state_key = update_state_key
        self.continue_button_data = f"{prefix}{continue_button_key}"
        self.continue_setting_button_data = f"{prefix}{continue_setting_button_key}"
        self.decline_button_data = f"{prefix}{decline_button_key}"
        self.setting_button_data = f"{prefix}{settings_button_key}"
        self.decline_marketing_data = f"{prefix}{decline_marketing_key}"
        self.continue_with_marketing = f"{prefix}{continue_with_marketing}"
        self.continue_without_marketing = f"{prefix}{continue_without_marketing}"

    async def ask(
            self,
            messanger_user_id: int | str, lang: str,
            bot: ClientBot | None = None,
            update: ma.tg.types.Update | ma.wa.types.Update | None = None,
            text_variable: str = "{bot_type} bot accept agreement marketing text",
    ):
        bot_type: BotTypeLiteral = bot.bot_type if bot else "telegram"
        dp = await Dispatcher.get_current(bot_type)

        if update:
            state = dp.current_state(user=messanger_user_id)
            await state.update_data({self.update_state_key: update.to_python()})

        keyboard = types.InlineKeyboard(
            buttons=[
                types.InlineKeyboardButton(
                    await f("bot accept agreement setting button", lang),
                    data=self.setting_button_data,
                ),
                types.InlineKeyboardButton(
                    await f("bot accept agreement continue button", lang),
                    data=self.continue_button_data,
                ),
            ]
        )

        bot_type = bot.bot_type if bot else "telegram"
        text_variable_formatted = text_variable.format(bot_type=bot_type)
        await dp.bot.send_message(
            messanger_user_id, await f(
                text_variable_formatted, lang,
                **await self.get_links(lang, bot, with_marketing=True),
            ), keyboard
        )

    @staticmethod
    async def ask_birthday(
            obj: ma.Message | ma.types.ButtonQuery,
            bot: ClientBot,
            lang: str, state_data: dict,
    ):
        answer_obj = ma.detect_answer_obj(obj)

        keyboard = await get_bot_birthday_keyboard(bot, lang)

        birthday_request_message = await answer_obj.answer(
            await f("user registration birthday invite text", lang) + "\n\n" + await f(
                "user registration birthday invite helper text", lang
            ),
            reply_markup=keyboard
        )

        dp = await Dispatcher.get_current()
        state = dp.current_state(user=obj.from_user.id)
        state_data['birthday_request_message_id'] = birthday_request_message.message_id
        await state.set_data(state_data)
        await state.set_state(UserBirthDate.Birthday.state)

    async def post_agreement_actions(
            self,
            query: ma.Message | ma.types.ButtonQuery,
            user: User, bot: ClientBot,
            lang: str, state_data: dict, with_update: bool = False,
    ):
        brand = await Brand.get(group_id=bot.group_id) if bot else None

        # Отримуємо налаштування лояльності для бренду
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                profile_id=bot.group_id if bot else None,
            )
        ) if brand else None
        
        if loyalty_settings:
            # Отримуємо або створюємо InCust клієнта
            await get_or_create_incust_customer(user, loyalty_settings)

        update_data: dict | None = state_data.pop(self.update_state_key, None)
        external_login_request_id = None
        if with_update:
            external_login_request_id = state_data.pop(
                "external_login_request_id", None
            )

        bot_type: BotTypeLiteral = bot.bot_type if bot else "telegram"

        if with_update:
            if update_data:
                tg_or_wa = ma.tg if bot_type == "telegram" else ma.wa
                update = tg_or_wa.types.Update.to_object(update_data)
                dp = await Dispatcher.get_current()
                await dp.dp.process_update(update)

            elif external_login_request_id and (
                    external_login_request := await ExternalLoginRequest.get(
                        external_login_request_id
                    )
            ):
                answer_obj = ma.detect_answer_obj(query)

                keyboard = await get_bot_menu_keyboard(user, bot, lang)
                await answer_obj.answer(
                    await f("auth external login auth success text", lang),
                    reply_markup=keyboard,
                )

                if (external_login_request.extra_data and
                        external_login_request.extra_data.get(
                            "anon_cart_token"
                        ) and external_login_request.extra_data.get("store_id")):
                    sync_cart_res = await crud.sync_user_cart(
                        external_login_request.extra_data.get("anon_cart_token"),
                        int(external_login_request.extra_data.get("store_id")), user
                    )
                    if not sync_cart_res:
                        store_id = external_login_request.extra_data.get('store_id')
                        cart_token = external_login_request.extra_data.get(
                            'anon_cart_token'
                        )

                        details = (
                            f"Brand: {brand.name} ({brand.id})\n"
                            f"Store ID: {store_id}\n"
                            f"User: {user.full_name} ({user.id})\n"
                            f"Cart token: {cart_token}"
                        )

                        err_text = await f(
                            "store cart external login sync error", lang,
                            details=details
                        )
                        await send_message_to_platform_admins(
                            err_text, force_to_bot=False
                        )

                if (external_login_request.purpose ==
                        schemas.ExternalLoginRequestPurposeEnum.RECEIPT):
                    return await external_login_with_receipt(
                        query, external_login_request,
                        user, lang, brand, bot, keyboard,
                    )
                elif (external_login_request.purpose ==
                      schemas.ExternalLoginRequestPurposeEnum.INVITATION):
                    return await external_login_with_invitation(
                        query, external_login_request,
                        user, lang, brand, bot, keyboard,
                    )

                await external_login_success_auth(
                    query, external_login_request,
                    user, lang, brand, bot, keyboard
                )
            elif isinstance(query, ma.tg.types.CallbackQuery | ma.tg.types.Message):
                from client.main.functions import send_hello_message
                return await send_hello_message(query.from_user.id, bot, lang)
            elif isinstance(query, ma.wa.types.ReplyQuery):
                from whatsapp_bot.bot.modules.main.functions import send_hello_message
                return await send_hello_message(query, user, bot, lang)

        if bot.bot_type == "telegram":
            await set_menu_button(bot, user, lang)

    async def after_marketing(
            self,
            text_variable: str,
            query: ma.types.ButtonQuery,
            state: ma.FSMContext,
            user: User, lang: str,
            bot: ClientBot | None = None
    ):
        bot_type: BotTypeLiteral = bot.bot_type if bot else "telegram"

        await agreement_processor.update_customer_and_user_activity(
            user, bot, lang, activity_data={"answered_marketing": True, "is_entered_bot": True},
        )

        answer_obj = await self.detect_answer_obj_and_delete_message(query)
        links = await self.get_links(lang, bot, with_marketing=True)
        await answer_obj.answer(
            await f(
                f"{bot_type} {text_variable}",
                lang, **links,
            ),
            reply_markup=await self.menu_keyboard_if_tg(user, bot, lang)
        )

        group = await Group.get(bot.group_id) if bot else None
        if not group:
            return

        state_data = await state.get_data()
        await self.post_agreement_actions(
            query, user, bot, lang, state_data, with_update=True
        )
        if (user and bot and lang and not user.birth_date and
                group.is_ask_about_birthday):
            await self.ask_birthday(query, bot, lang, state_data)
        else:
            if bot.bot_type == "telegram":
                await set_menu_button(bot, user, lang)

    @classmethod
    async def get_user_and_lang(
            cls,
            obj: ma.types.ButtonQuery | ma.types.Message,
            client_bot: ClientBot | None = None

    ):
        if not client_bot:
            client_bot = await ClientBot.get_current()

        if not client_bot or client_bot.bot_type == "telegram":
            user = await User.get(obj.from_user.id)
        else:
            user = await User.get_by_wa_phone(wa_phone=obj.from_user.id)
        lang = await detect_messanger_user_lang(obj, client_bot, user)
        return user, lang

    @classmethod
    async def menu_keyboard_if_tg(cls, user: User, bot: ClientBot | None, lang: str):
        if bot and bot.bot_type == "telegram":
            return await get_bot_menu_keyboard(user, bot, lang)

    @classmethod
    async def get_links(
            cls, lang: str, bot: ClientBot | None = None, with_marketing: bool = False
    ):
        if not bot:
            bot = await ClientBot.get_current()
        brand = await Brand.get(group_id=bot.group_id) if bot else None

        def fmt_link(link: str):
            return link.format(lang=lang)

        links = {
            "terms_of_use_link": fmt_link(
                brand.terms_of_use_link if brand else "https://7loc.com/{"
                                                      "lang}/termsofuse"
            ),
            "privacy_policy_link": fmt_link(
                brand.privacy_policy_link if brand and brand.privacy_policy_link else
                "https://7loc.com/{lang}/privacy-policy"
            ),
        }
        if with_marketing and brand:
            links["marketing_link"] = brand.get_url("marketing")
        return links

    @classmethod
    async def detect_answer_obj_and_delete_message(
            cls, obj: ma.types.ButtonQuery | ma.types.Message
    ):
        if isinstance(obj, ma.tg.types.CallbackQuery):
            answer_obj = obj.message
            await answer_obj.delete()
        elif isinstance(obj, ma.tg.types.Message):
            answer_obj = obj
            await answer_obj.delete()
        else:
            answer_obj = obj

        return answer_obj

    @classmethod
    async def update_customer_and_user_activity(
            cls, user: User, bot: ClientBot, lang: str,
            customer_data: dict | None = None,
            activity_data: dict | None = None,
    ):
        if activity_data:
            user_bot_activity = await UserClientBotActivity.get(user, bot)
            if user_bot_activity:
                await user_bot_activity.update(**activity_data)

        if customer_data:
            customer = await Customer.get(user_id=user.id, profile_id=bot.group_id)
            if not customer:
                await crud.create_customer(lang, user.id, bot.group_id, **customer_data)
            else:
                await customer.update(
                    **customer_data,
                    updated_date=utcnow(),
                )


agreement_processor = AgreementProcessor()
