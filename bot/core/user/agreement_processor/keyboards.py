from psutils.redefined_classes.my_keyboard_classes import MenuBtn, MenuKb

from core import messangers_adapters as ma
from db.models import (
    ClientBot,
)
from utils.text import c, f


async def get_bot_birthday_keyboard(
    bot: ClientBot,
    lang: str,
):
    message_text = await f("client bot incust skip birth date text", lang)
    keyboard = None
    if bot.bot_type == "telegram":
        keyboard = MenuKb(resize_keyboard=True, row_width=1, one_time_keyboard=False)
        keyboard.row(
            MenuBtn(
                message_text,
            )
        )
    elif bot.bot_type == "whatsapp":
        keyboard = ma.wa.types.ReplyKeyboard()
        button_data = dict(mode="skip_birthday")
        data = c(**button_data)

        keyboard.add_buttons(
            ma.wa.types.ReplyButton(
                id=data, title=message_text,
            )
        )
    return keyboard
