from datetime import datetime
from aiogram.types import ContentTypes

from core import messangers_adapters as ma
from db.models import (
    ClientBot,
)
from utils.text import f

from .agreement_processor import agreement_processor
from .states import UserBirthDate
from .buttons_handlers import handle_skip_birthday


async def handle_birthday_input(
    message: ma.Message, state: ma.FSMContext
):
    client_bot = await ClientBot.get_current()
    user, lang = await agreement_processor.get_user_and_lang(message, client_bot)

    try:
        birth_date = datetime.strptime(message.text, "%d.%m.%Y").date()

        if birth_date > datetime.now().date():
            raise ValueError("Date is in the future")

        await user.update(birth_date=birth_date)

        if client_bot.bot_type == "telegram":
            await message.delete()

        state_data = await state.get_data()
        birthday_request_message_id = state_data.pop(
            'birthday_request_message_id', None
        )
        if (
                birthday_request_message_id and
                client_bot.bot_type == "telegram" and
                birthday_request_message_id
        ):
            try:
                await message.bot.delete_message(
                    chat_id=message.chat.id, message_id=birthday_request_message_id
                )
            except Exception as e:
                print(f"Failed to delete birthday request message: {e}")

        await state.finish()
        await agreement_processor.post_agreement_actions(
            message, user, client_bot, lang, state_data, with_update=True
        )

    except ValueError:
        error_message = await f("date format error", lang)
        await message.reply(error_message)
        await agreement_processor.ask_birthday(
            message, client_bot, lang, await state.get_data()
        )


def setup_tg_message_handlers(dp: ma.tg.Dispatcher):
    dp.register_message_handler(
        handle_skip_birthday,
        lequal="client bot incust skip birth date text",
        content_types=ContentTypes.TEXT,
        state="*",
    )
    dp.register_message_handler(
        handle_birthday_input,
        state=UserBirthDate.Birthday.state
    )


def setup_wa_message_handlers(dp: ma.wa.Dispatcher):
    dp.register_message_handler(
        handle_birthday_input,
        state=UserBirthDate.Birthday.state,
        content_types=ma.wa.types.ContentType.ANY,
    )
