import schemas
from core import messangers_adapters as ma
from core.messangers_adapters import Di<PERSON>atcher, types
from core.user.functions import (
    create_or_update_messanger_user
)
from db import crud
from db.models import (
    ClientBot, Customer, ExternalLoginRequest,
)
from schemas import Bot<PERSON><PERSON><PERSON>iteral
from utils.date_time import utcnow
from utils.text import f

from .agreement_processor import agreement_processor


async def handle_skip_birthday(
        obj: ma.tg.types.Message | ma.wa.types.ReplyQuery,
        state: ma.FSMContext
):
    message = await agreement_processor.detect_answer_obj_and_delete_message(obj)

    state_data = await state.get_data()
    birthday_request_message_id = state_data.pop(
        'birthday_request_message_id', None
    )
    if birthday_request_message_id and isinstance(
            message, ma.tg.types.Message
    ) and birthday_request_message_id:
        try:
            await message.bot.delete_message(
                chat_id=message.chat.id, message_id=birthday_request_message_id
            )
        except Exception as e:
            print(f"Failed to delete birthday request message: {e}")

    await state.finish()

    client_bot = await ClientBot.get_current()

    user, lang = await agreement_processor.get_user_and_lang(obj, client_bot)
    await agreement_processor.post_agreement_actions(
        message, user, client_bot, lang, state_data, with_update=True
    )


async def handle_continue(
        query: ma.types.ButtonQuery,
        state: ma.FSMContext,
        bot: ClientBot | None = None,
):
    user = await create_or_update_messanger_user(
        query.from_user, bot, need_set_is_accepted_agreement=True
    )
    lang = await user.get_lang(bot.id if bot else None)

    await agreement_processor.update_customer_and_user_activity(
        user, bot, lang, activity_data={
            "is_accept_agreement": True, "accepted_agreement_date":
                user.accept_agreement_datetime or utcnow()
        },
        customer_data={
            "marketing_consent": True, "is_accept_agreement": True,
        }
    )

    await agreement_processor.after_marketing(
        f"bot accept agreement accepted marketing text",
        query, state, user, lang, bot
    )


async def handle_continue_settings(
        query: ma.types.ButtonQuery,
        state: ma.FSMContext,
        bot: ClientBot | None = None,
):
    user = await create_or_update_messanger_user(query.from_user, bot)
    lang = await user.get_lang(bot.id if bot else None)

    await agreement_processor.update_customer_and_user_activity(
        user, bot, lang, activity_data={
            "is_accept_agreement": True, "accepted_agreement_date":
                user.accept_agreement_datetime or utcnow(),
            "answered_marketing": False,
        },
        customer_data={
            "marketing_consent": None, "is_accept_agreement": True,
        }
    )

    keyboard = types.InlineKeyboard(
        buttons=[
            types.InlineKeyboardButton(
                await f("bot accept agreement continue button", lang),
                data=agreement_processor.continue_with_marketing,
            ),
            types.InlineKeyboardButton(
                await f("bot accept agreement decline button", lang),
                data=agreement_processor.continue_without_marketing,
            ),
        ]
    )

    bot_type: BotTypeLiteral = bot.bot_type if bot else "telegram"
    text_variable = f"{bot_type} bot accept marketing text"
    bot_type = bot.bot_type if bot else "telegram"
    text_variable_formatted = text_variable.format(bot_type=bot_type)

    text = await f(
        text_variable_formatted, lang,
        **await agreement_processor.get_links(lang, bot, with_marketing=True),
    )

    state_data = await state.get_data()
    await agreement_processor.post_agreement_actions(query, user, bot, lang, state_data)

    additional_kwargs = {}
    answer_obj = await agreement_processor.detect_answer_obj_and_delete_message(query)
    await answer_obj.answer(
        await f(
            f"{bot_type} bot accept agreement accepted text",
            lang, **await agreement_processor.get_links(lang, bot),
        ),
    )

    keyboard = keyboard.to_messanger(bot_type)

    await answer_obj.answer(
        text,
        **additional_kwargs,
        reply_markup=keyboard,
    )


async def handle_decline(
        query: ma.types.ButtonQuery,
        state: ma.FSMContext,
        bot: ClientBot | None = None
):
    bot_type: BotTypeLiteral = bot.bot_type if bot else "telegram"

    user, lang = await agreement_processor.get_user_and_lang(query, bot)

    if user:
        await agreement_processor.update_customer_and_user_activity(
            user, bot, lang, activity_data={
                "is_accept_agreement": False, "accepted_agreement_date": None,
                "answered_marketing": False,
            },
            customer_data={
                "marketing_consent": None, "is_accept_agreement": False,
            }
        )

    async with state.proxy() as state_data:
        external_login_request_id = state_data.get("external_login_request_id")
        state_data.clear()
        state_data.state = None

    additional_kwargs = {}
    if isinstance(query, ma.tg.types.CallbackQuery):
        answer_obj = query.message
        await answer_obj.delete()
        additional_kwargs["reply_markup"] = ma.tg.types.ReplyKeyboardRemove()
    else:
        answer_obj = query

    if external_login_request_id:
        external_login_request = await ExternalLoginRequest.get(
            external_login_request_id
        )
        if external_login_request:
            await external_login_request.update(
                status=schemas.ExternalLoginRequestStatusEnum.CANCELED,
            )

    await answer_obj.answer(
        await f(
            f"{bot_type} bot accept agreement declined text",
            lang, **await agreement_processor.get_links(lang, bot),
        ),
        **additional_kwargs,
    )


async def handle_decline_marketing(
        query: ma.types.ButtonQuery,
        state: ma.FSMContext,
        bot: ClientBot | None = None
):
    user = await create_or_update_messanger_user(query.from_user, bot)
    lang = await user.get_lang(bot.id if bot else None)

    customer = await Customer.get(user_id=user.id, profile_id=bot.group_id)
    if not customer:
        await crud.create_customer(lang, user.id, bot.group_id, False, True)
    else:
        await customer.update(
            marketing_consent=False,
            is_accept_agreement=True,
            updated_date=utcnow(),
        )

    await agreement_processor.after_marketing(
        "bot accept marketing declined text",
        query, state, user, lang, bot
    )


async def handle_accept_marketing(
        query: ma.types.ButtonQuery,
        state: ma.FSMContext,
        bot: ClientBot | None = None
):
    user = await create_or_update_messanger_user(query.from_user, bot)
    lang = await user.get_lang(bot.id if bot else None)

    customer = await Customer.get(user_id=user.id, profile_id=bot.group_id)
    if not customer:
        await crud.create_customer(lang, user.id, bot.group_id, True, True)
    else:
        await customer.update(
            marketing_consent=True,
            is_accept_agreement=True,
            updated_date=utcnow(),
        )

    await agreement_processor.after_marketing(
        "bot accept marketing accepted text",
        query, state, user, lang, bot
    )


async def handle_setting(
        query: ma.types.ButtonQuery,
        bot: ClientBot | None = None,
        update: ma.types.Update | None = None,
):
    bot_type: BotTypeLiteral = bot.bot_type if bot else "telegram"
    dp = await Dispatcher.get_current(bot_type)

    user = await create_or_update_messanger_user(
        query.from_user, bot, need_set_is_accepted_agreement=True
    )
    lang = await user.get_lang(bot.id if bot else None)
    text_variable = "{bot_type} bot accept agreement text"
    keyboard = types.InlineKeyboard(
        buttons=[
            types.InlineKeyboardButton(
                await f("bot accept agreement continue button", lang),
                data=agreement_processor.continue_setting_button_data,
            ),
        ]
    )

    if update:
        state = dp.current_state(user=user.id)
        await state.update_data(
            {agreement_processor.update_state_key: update.to_python()}
        )
    bot_type = bot.bot_type if bot else "telegram"
    text_variable_formatted = text_variable.format(bot_type=bot_type)

    text = await f(
        text_variable_formatted, lang,
        **await agreement_processor.get_links(lang, bot, with_marketing=True),
    )
    additional_kwargs = {}

    answer_obj = await agreement_processor.detect_answer_obj_and_delete_message(query)

    await answer_obj.answer(
        text,
        **additional_kwargs,
        reply_markup=keyboard.to_messanger(bot_type),
    )


def setup_tg_handlers(dp: ma.tg.Dispatcher):
    dp.register_callback_query_handler(
        handle_continue,
        callback_mode=agreement_processor.continue_button_data,
        state="*",
    )
    dp.register_callback_query_handler(
        handle_continue_settings,
        callback_mode=agreement_processor.continue_setting_button_data,
        state="*",
    )
    dp.register_callback_query_handler(
        handle_accept_marketing,
        callback_mode=agreement_processor.continue_with_marketing,
        state="*",
    )
    dp.register_callback_query_handler(
        handle_decline_marketing,
        callback_mode=agreement_processor.continue_without_marketing,
        state="*",
    )
    dp.register_callback_query_handler(
        handle_decline,
        callback_mode=agreement_processor.decline_button_data,
        state="*",
    )
    dp.register_callback_query_handler(
        handle_decline_marketing,
        callback_mode=agreement_processor.decline_marketing_data,
        state="*",
    )
    dp.register_callback_query_handler(
        handle_setting,
        callback_mode=agreement_processor.setting_button_data,
        state="*",
    )


def setup_wa_handlers(dp: ma.wa.Dispatcher):
    dp.register_reply_query_handler(
        handle_continue,
        reply_mode=agreement_processor.continue_button_data,
        state="*",
    )
    dp.register_reply_query_handler(
        handle_continue_settings,
        reply_mode=agreement_processor.continue_setting_button_data,
        state="*",
    )
    dp.register_reply_query_handler(
        handle_accept_marketing,
        reply_mode=agreement_processor.continue_with_marketing,
        state="*",
    )
    dp.register_reply_query_handler(
        handle_decline_marketing,
        reply_mode=agreement_processor.continue_without_marketing,
        state="*",
    )
    dp.register_reply_query_handler(
        handle_decline,
        reply_mode=agreement_processor.decline_button_data,
        state="*",
    )
    dp.register_reply_query_handler(
        handle_decline_marketing,
        reply_mode=agreement_processor.decline_marketing_data,
        state="*",
    )
    dp.register_reply_query_handler(
        handle_setting,
        reply_mode=agreement_processor.setting_button_data,
        state="*",
    )
    dp.register_reply_query_handler(
        handle_skip_birthday,
        reply_mode="skip_birthday",
        state="*",
    )
