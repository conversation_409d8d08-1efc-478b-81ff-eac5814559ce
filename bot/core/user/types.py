from dataclasses import dataclass

from db.models import User, UserClientBotActivity, UserServiceBotActivity, UserAdminBotActivity


@dataclass
class CreateOrUpdateMessangerUserInfo:
    user: User | None = None
    lang: str | None = None
    is_user_created: bool = False

    user_bot_activity: UserClientBotActivity | UserServiceBotActivity | UserAdminBotActivity | None = None
    is_bot_activity_created: bool = False

