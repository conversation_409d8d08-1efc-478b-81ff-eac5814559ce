import json
from typing import Any

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class UserNotCreated(Exception):
    def __init__(self, user_id: int | str, username: str, full_name: str, bot_username: str):
        self.txt = f"""
            User nor created
            New user data:
                user_tg_or_wa_id: {user_id},
                username: {username},
                full_name: {full_name},
                bot_username: {bot_username},
            """
        self.user_id = user_id
        self.username = username
        self.full_name = full_name
        self.bot_username = bot_username


class UserNotCreatedUsernameExists(UserNotCreated):
    def __init__(
            self,
            chat_id: int, username: str,
            full_name: str, bot_username: str,
            exists_user_id: int, exists_user_chat_id: int,

    ):
        self.text = f"""
            User not created when user with username "{username}"
             already exists in db with id {exists_user_id} and chat_id {exists_user_chat_id}
            New user data: 
                username: {username},
                full_name: {full_name},
                chat_id: {chat_id},
                bot_username: {bot_username},
            """


class UserNotCreateUsernameExistsWithoutChatID(UserNotCreated):
    def __init__(self, chat_id: int, username: str, full_name: str, bot_username: str, exists_user_id: int):
        self.text = f"""
            User not created when user with username "{username}" already exists in db 
            with id {exists_user_id} and without chat_id
            New user data: 
                username: {username},
                full_name: {full_name},
                chat_id: {chat_id},
                bot_username: {bot_username},
            """


class CreatingUserForTelegramChatError(UserNotCreated):
    def __init__(self, object_info_dict: dict):
        object_info = json.dumps(object_info_dict, indent=3)
        self.txt = f"You are trying to create user by telegram chat. \nObject info: {object_info}"


class InvalidObjectForCreateOrUpdateUser(Exception):
    def __init__(self, obj: Any):
        self.obj = obj

    def __repr__(self):
        return f"Invalid object for create_or_update_user: {self.obj}"


class SavingUserPhotoError(Exception):
    pass


class CreateGuestUserWithEmailExistError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "create guest user with email exist error"


class UserNotConfirmedEmailError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "user not confirmed email error"
