from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from dataclasses import asdict, dataclass
from datetime import date, datetime
from psutils.type_vars import FuncT
from typing import Callable

from loggers import J<PERSON><PERSON>ogger


@dataclass
class SchedulerArgs:
    year: str | None = None
    month: str | None = None
    week: str | None = None
    day_of_week: str | None = None
    day: str | None = None
    hour: str | None = None
    minute: str | None = None
    second: str | None = None
    start_date: datetime | date | str | None = None
    end_date: datetime | date | str | None = None


@dataclass
class SchedulerJob:
    function: Callable
    args: SchedulerArgs


class Scheduler:
    def __init__(self):
        self.jobs: list[SchedulerJob] = []
        self.logger = JSONLogger("scheduler")
        self._scheduler: AsyncIOScheduler | None = None

    def add_job(self, job: SchedulerJob):
        self.jobs.append(job)

    def job(self, args: SchedulerArgs):
        def decorator(func: FuncT) -> FuncT:
            self.add_job(SchedulerJob(func, args))
            return func

        return decorator

    def run(self):
        self._scheduler = AsyncIOScheduler()

        for job in self.jobs:
            args_dict = asdict(job.args)

            trigger = CronTrigger(
                **{k: v for k, v in args_dict.items() if v is not None}
            )
            self._scheduler.add_job(
                job.function,
                trigger,
                id=job.function.__name__,
                replace_existing=True,
            )
            self.logger.info(
                f"[Scheduler] registered job {job.function.__name__}",
                {
                    "func": job.function.__name__,
                    "args": args_dict,
                }
            )
        self._scheduler.start()
        self.logger.info(f"[Scheduler] started with jobs({len(self.jobs)}")

    def shutdown(self):
        if not self._scheduler:
            self.logger.warning("[Scheduler] no scheduler running")
            return

        self._scheduler.shutdown()
        self.logger.info("[Scheduler] shutdown")
