import json

from core.kafka.producer.functions import (
    add_admin_notification_for_action,
    add_user_notification_for_action,
)
from core.kafka.redis_instance import redis
from db import crud
from db.models import SystemNotification, User
from loggers import JSONLogger
from loggers.json import data_from_locals
from schemas import (
    NotificationLevel, NotificationRecipientType, SSEChannelTarget,
    SystemNotificationCategory,
    SystemNotificationType,
)
from utils.platform_admins import send_message_to_platform_admins


async def create_system_notification(
        scope: str,
        group_id: int, category: SystemNotificationCategory,
        type_notification: SystemNotificationType, title: str,
        content: str,
        level: NotificationLevel | None = NotificationLevel.ERROR,
        recipient_type: NotificationRecipientType = NotificationRecipientType.ADMIN,
        recipient_id: int | None = None,
        **kwargs,
) -> SystemNotification | None:
    data = dict(locals())

    logger = JSONLogger(
        "admin-notifications",
        data_from_locals(locals())
    )

    try:
        system_notification, is_restricted = await crud.creat_system_notification(
            scope, group_id, category, type_notification, title, content, level,
            recipient_type, recipient_id,
        )
    except Exception as error:
        logger.error("An error occurred while creating admin notification", error)
        await send_message_to_platform_admins(
            "An error occurred while CREATING admin notification\n"
            f"Scope: {scope}\n"
            f"Group ID: {group_id}\n"
            f"Category: {category}\n"
            f"Type: {type_notification}\n"
            f"Title: {title}\n"
            f"Level: {level}\n"
        )
        return None
    else:
        logger.add_data(
            {
                "admin_notification": system_notification,
                "is_restricted": is_restricted,
            }
        )

        if is_restricted or not system_notification:
            logger.debug(
                "Admin notification created successfully. "
                "Will not be send due to timeout"
            )
            return system_notification

        try:
            # For admin notifications, send email and notify admin channels
            if recipient_type == NotificationRecipientType.ADMIN:
                await add_admin_notification_for_action(system_notification)
                await notify_all_channels(
                    SSEChannelTarget.ADMIN_NOTIFICATION,
                    "notification_new",
                    profile_id=group_id,
                    admin_notification_id=system_notification.id,
                    title=system_notification.title,
                    content=system_notification.content,
                    level=system_notification.level.value,
                )
            elif recipient_type == NotificationRecipientType.USER and recipient_id:
                user = await User.get_by_id(recipient_id)
                if user:
                    # Відправляємо сповіщення через всі доступні канали (telegram,
                    # email)
                    await add_user_notification_for_action(
                        system_notification, user, **kwargs
                    )

                # Відправляємо сповіщення через SSE канал
                await notify_all_channels(
                    SSEChannelTarget.USER_NOTIFICATION,
                    "notification_new",
                    key=str(recipient_id),
                    notification_id=system_notification.id,
                    title=system_notification.title,
                    content=system_notification.content,
                    level=system_notification.level.value,
                    group_id=group_id,
                )
        except Exception as error:
            logger.error(
                "Admin notification created successfully, but error occurred while "
                "sending", error
            )

            await send_message_to_platform_admins(
                "An error occurred while SENDING admin notification\n"
                f"Scope: {scope}\n"
                f"Group ID: {group_id}\n"
                f"Category: {category}\n"
                f"Type: {type_notification}\n"
                f"Title: {title}\n"
                f"Notification id: {system_notification.id}"
            )
        else:
            logger.debug(
                "Admin notification created and sent successfully"
            )

        return system_notification


async def notify_all_channels(
        target: SSEChannelTarget, event_type: str,
        profile_id: int | None = None,
        key: str | None = None,
        **kwargs,
) -> None:
    """Відправка сповіщення у всі активні канали"""
    try:
        channels = await crud.get_active_sse_channels(
            target=target, profile_id=profile_id, key=key
        )
        message = {
            "event": event_type,
            "data": {**kwargs},
        }
        if profile_id:
            message["data"]["profile_id"] = profile_id

        for channel in channels:
            channel_name = f"sse-{channel.id}"
            await redis.publish(channel_name, json.dumps(message))
    finally:
        await redis.close()
