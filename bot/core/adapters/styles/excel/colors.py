from openpyxl.styles import Pat<PERSON><PERSON>ill
from openpyxl.worksheet.worksheet import Worksheet

from ..base import BaseColorMixin

from ...cell import Cell


class ExcelColorMixin(BaseColorMixin):

    @property
    def color1(self) -> str:
        return self.get_color(1, "hex")

    @property
    def color2(self) -> str:
        return self.get_color(2, "hex")

    @staticmethod
    def __get_fill(
            background_color: str,
    ) -> PatternFill:
        return PatternFill(
            start_color=background_color, end_color=background_color,
            fill_type="solid",
        )

    async def format_cells(
            self, sheet: Worksheet,
            cells: list[Cell],
            background_color: str = None,
            foreground_color: str = None,
    ):
        if not any([background_color, foreground_color]):
            return

        pattern_fill = self.__get_fill(background_color)

        for cell in cells:
            sheet.cell(row=cell.row_id, column=cell.col_id).fill = pattern_fill
