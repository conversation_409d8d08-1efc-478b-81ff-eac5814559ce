from abc import ABC, abstractmethod

from config import DEFAULT_SELECT1_BAC<PERSON>GROUND_COLOR, DEFAULT_SELECT2_BACKGROUND_COLOR

from gspread import Worksheet as GoogleWorksheet

from openpyxl.worksheet.worksheet import Worksheet as ExcelWorksheet

from ..cell import Cell


class BaseColorMixin(ABC):

    def __init__(self, *args, **kwargs):
        self._color1: dict = DEFAULT_SELECT1_BACKGROUND_COLOR
        self._color2: dict = DEFAULT_SELECT2_BACKGROUND_COLOR

        super().__init__(*args, **kwargs)

    def get_color(self, color_id: int, type_: str) -> dict | str:
        color = getattr(self, f"_color{color_id}")
        return color.get(type_)

    @abstractmethod
    async def format_cells(
            self, sheet: GoogleWorksheet | ExcelWorksheet,
            cells: list[Cell],
            background_color: dict | str = None,
            foreground_color: dict | str = None,
    ):
        raise NotImplementedError
