from abc import ABC

from datetime import datetime

from config import PATH_TO_GOOGLE_CREDS_JSON

from db.models import User

from gspread import Worksheet

from psutils.convertors import datetime_to_str
from psutils.date_time import get_local_datetime
from psutils.google_sheets import AsyncGoogleSheetsClient

from utils.google import limiter


class BaseSheetsAdapter(ABC):

    def __init__(self, lang: str, sheets: str, *args, **kwargs):
        super().__init__(lang, *args, **kwargs)

        self.sheets = sheets
        self._client: AsyncGoogleSheetsClient | None = None

    @property
    async def client(self) -> AsyncGoogleSheetsClient:
        if not self._client:
            self._client = AsyncGoogleSheetsClient(
                self.sheets, limiter=limiter,
                creds_file_path=PATH_TO_GOOGLE_CREDS_JSON,
            )
            await self._client.load_document()
        return self._client

    async def backup(self, worksheet: Worksheet, user_id: int | None = None):
        client = await self.client
        if not any([client, worksheet]):
            return worksheet

        origin_title = worksheet.title

        if user_id:
            user = await User.get_by_id(user_id)
            timezone = user.get_timezone()
            date_time = get_local_datetime(timezone)
        else:
            date_time = datetime.utcnow()
        new_title = " - ".join([datetime_to_str(date_time), origin_title])

        client.rename_worksheet(new_title)
        return await client.create_and_use_worksheet(origin_title)
