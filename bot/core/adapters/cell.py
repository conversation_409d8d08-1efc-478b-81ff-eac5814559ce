from dataclasses import dataclass
from operator import attrgetter
from typing import Any


@dataclass
class Cell:
    row_id: int
    col_id: int
    value: Any


class CellsData:

    def __init__(self):
        self._cells: list[Cell] = []

    def add(self, row_id: int, col_id: int, value: Any):
        self._cells.append(Cell(row_id, col_id, value))

    def find_column_by_name(self, name: str) -> int | None:
        for cell in self._cells:
            if cell.row_id == 1 and cell.value == name:
                return cell.col_id
        return None

    def insert_columns(self, col_id: int, column_count: int):
        for cell in self._cells:
            if cell.col_id >= col_id:
                cell.col_id += column_count

    def insert_column(self, col_id: int):
        self.insert_columns(col_id, 1)

    @property
    def max_column(self):
        return max(self._cells, key=attrgetter("col_id")).col_id if self._cells else 0

    @property
    def max_row(self):
        return max(self._cells, key=attrgetter("row_id")).row_id

    @classmethod
    def get_row_data(cls, data: list[tuple[int, str | float | None]], max_column: int) -> dict[int, str | float | None]:
        data = dict(data)
        for index in range(1, max_column):
            if index not in data:
                data[index] = None
        return dict(sorted(data.items()))

    def get_sheets_data(self, max_column: int) -> list[list[str | float | None]]:
        data = {}
        for cell in self._cells:
            if cell.row_id not in data:
                data[cell.row_id] = []
            data[cell.row_id].append((cell.col_id, cell.value))

        data = {
            row_id: list(self.get_row_data(values, max_column).values())
            for row_id, values in data.items()
        }
        data = dict(sorted(data.items()))
        return list(data.values())

    def get_row(
            self, row_id: int,
            values_only: bool = False,
    ) -> list[Cell] | list[str | None]:
        row = []
        for col_id in range(1, self.max_column):
            found_cells = [cell for cell in self._cells if cell.row_id == row_id and cell.col_id == col_id]
            if not found_cells:
                row.append(None)
            elif len(found_cells) == 1:
                cell = found_cells[0]

                if values_only:
                    row.append(cell.value)
                else:
                    row.append(cell)

            else:
                raise ValueError("Multiple cells found")
        return row

    def get_data(self) -> list[Cell | list[str | float | None]]:
        return self._cells

    def get_cells(self) -> dict[int, dict[int, Cell]]:
        data = {}
        for cell in self._cells:
            if cell.row_id not in data:
                data[cell.row_id] = {}
            data[cell.row_id][cell.col_id] = cell

        data = dict(sorted(data.items()))
        for row_id, column in data.items():
            data[row_id] = dict(sorted(column.items()))
        return data

    def clear(self):
        self._cells.clear()
