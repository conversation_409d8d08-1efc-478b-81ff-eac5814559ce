from abc import ABC
from typing import Any, TypedDict

from psutils.exceptions import ErrorWithTextVariable

from pydantic.fields import Undefined


class FieldNameVariable(TypedDict, total=True):
    variable: str


class ExcelFieldError(ErrorWithTextVariable, ABC):

    def __init__(self, message: str, **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"Field error: {self.message}"


class ExcelAdapterFieldError(ExcelFieldError, ABC):
    def __init__(
            self,
            schema_name_variable: str,
            row_number: int,
            field_name: str | FieldNameVariable,
            excepted_type: Any = Undefined,
            actual_value: Any = Undefined,
            **kwargs,
    ):
        self.schema_name_variable: str = schema_name_variable
        self.row_number: int = row_number
        self.field_name: str | FieldNameVariable = field_name
        self.excepted_type: Any = excepted_type
        self.actual_value: Any = actual_value
        self.actual_type: type | Undefined = type(actual_value) if actual_value is not Undefined else Undefined

        super().__init__(
            "Excel adapter field error",
            schema_name_variable=schema_name_variable,
            row_number=row_number,
            field_name=field_name,
            excepted_type=excepted_type,
            actual_value=actual_value,
            actual_type=self.actual_type,
            **kwargs,
        )


class ExcelTooLongValueForFieldError(ExcelAdapterFieldError):
    text_variable = "excel too long string error"

    def __init__(
            self,
            schema_name_variable: str,
            row_number: int,
            field_name: str | FieldNameVariable,
            current_length: int,
            max_length: int,
    ):
        self.current_length = current_length
        self.max_length = max_length
        super().__init__(
            schema_name_variable,
            row_number,
            field_name,
            current_length=current_length,
            max_length=max_length,
        )


class ExcelInvalidFieldTypeError(ExcelAdapterFieldError):
    text_variable = "importer incorrect field type error"

    def __init__(
            self,
            schema_name_variable: str,
            row_number: int,
            field_name: str | FieldNameVariable,
            excepted_type: type,
            actual_value: Any,
    ):
        super().__init__(schema_name_variable, row_number, field_name, excepted_type, actual_value)


class ExcelValueMissedForFieldError(ExcelAdapterFieldError):
    text_variable = "importer value missed for field error"

    def __init__(
            self,
            schema_name_variable: str,
            row_number: int,
            field_name: str | FieldNameVariable,
    ):
        super().__init__(schema_name_variable, row_number, field_name)


class CoordinatesError(ExcelAdapterFieldError, ABC):
    def __init__(
            self,
            schema_name_variable: str,
            row_number: int,
            field_name: str | FieldNameVariable,
            actual_value: Any
    ):
        super().__init__(
            schema_name_variable,
            row_number,
            field_name,
            actual_value=actual_value,
        )


class CoordinatesInvalidLatitudeError(CoordinatesError):
    text_variable = "store import excel coordinates invalid latitude error"


class CoordinatesInvalidLongitudeError(CoordinatesError):
    text_variable = "store import excel coordinates invalid longitude error"


class InvalidCoordinatesFormatError(CoordinatesError):
    text_variable = "store import excel incorrect coordinates format error"
