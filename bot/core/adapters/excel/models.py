from __future__ import annotations

from abc import ABCMeta
from types import GenericAlias
from typing import Any, Iterable, Literal, Type, get_args, get_origin, get_type_hints

from psutils.convertors import str_to_float, str_to_int
from psutils.text import paschal_case_to_snake_case
from pydantic.fields import Undefined

from utils.text import f, fl
from .exceptions import (
    CoordinatesInvalidLatitudeError, CoordinatesInvalidLongitudeError,
    ExcelInvalidFieldTypeError, ExcelTooLongValueForFieldError,
    ExcelValueMissedForFieldError, InvalidCoordinatesFormatError,
)


class Field:
    def __init__(
            self, variable: str,
            required: bool = False,
            default: Any = Undefined,
            export_exclude: bool = False,
            export_variable: str | None = None,
            to_lower_case: bool | None = None,
            max_length: int | None = None,
            no_localisation: bool = False,
    ):
        self.variable: str = variable
        self.type: type | None = None
        self._name: str | None = None
        self.required = required
        self._default = default
        self.export_exclude = export_exclude
        self._export_variable = export_variable
        self.to_lower_case = to_lower_case
        self.max_length = max_length
        self.no_localisation = no_localisation

    @property
    def default(self):
        if self.required or self._default is not Undefined:
            return self._default
        elif self.type in (bool, list):
            return self.type()
        else:
            return None

    @property
    def export_variable(self) -> str:
        return self._export_variable or self.variable

    def get_error_variable(self, instance: BaseExcelModel):
        if (
                hasattr(instance, "fields_place") and
                self.name in instance.fields_place  # type: ignore
        ):
            return self.export_variable
        return self.variable

    async def get_verbose_name(self, lang: str):
        if self.no_localisation:
            return self.export_variable
        return await f(self.export_variable, lang)

    def __get__(self, instance: BaseExcelModel | None, owner: Type[BaseExcelModel]):
        if not issubclass(owner, BaseExcelModel):
            raise ValueError("owner must be subclass of BaseExcelModel")

        if not instance:
            return self

        if self.name in instance.values:
            value = instance.values.get(self.name)
        else:
            value = self.check_and_get_default_value(instance)

        return value

    def check_is_type(self, type_: type):
        return self.type == type_ or type_ in get_args(self.type)

    def check_and_get_default_value(self, instance: BaseExcelModel):
        if self.default is not Undefined:
            value = self.default
        else:
            value = None

        if self.required and (not value or value is Undefined):
            raise ExcelValueMissedForFieldError(
                instance.get_schema_variable(),
                instance.row_number,
                {"variable": self.get_error_variable(instance)},
            )

        return value

    def validate_and_convert_value(self, instance: BaseExcelModel, value: Any) -> Any:
        if value is None:
            value = self.check_and_get_default_value(instance)

        elif isinstance(value, str):
            value = value.strip()

        if self.type == bool:
            if isinstance(value, str):
                value = value.lower().strip() in ("+", "1", "true")
        elif self.type == list:
            if isinstance(value, str):
                split_value = value.split("\n")
                value = []
                for el in split_value:
                    el = el.strip()
                    if self.to_lower_case:
                        el = el.lower()
                    if el:
                        value.append(el)
        elif (
                value is not None and self.check_is_type(str) and
                not (
                        isinstance(value, int | float) and
                        (
                                self.check_is_type(int) or
                                self.check_is_type(float)
                        )
                )
        ):
            if isinstance(value, float) and value % 1 == 0:
                value = int(value)
            value = str(value)
            if self.to_lower_case:
                value = value.lower()
            if self.max_length and len(value) > self.max_length:
                raise ExcelTooLongValueForFieldError(
                    instance.get_schema_variable(),
                    instance.row_number,
                    {"variable": self.get_error_variable(instance)},
                    len(value),
                    self.max_length,
                )
        elif value is not None and isinstance(value, str | int | float):
            if self.check_is_type(float):
                convertor = str_to_float
            elif self.check_is_type(int):
                convertor = str_to_int
            else:
                convertor = None

            if isinstance(value, str):
                temp_value = value.replace(" ", "")
                if "," in temp_value and "." not in temp_value:
                    temp_value = temp_value.replace(",", ".")
            else:
                temp_value = value

            if convertor and (
                    converted_value := convertor(
                        temp_value, no_error=True
                    )) is not None:
                value = converted_value

        if not isinstance(value, self.type):
            raise ExcelInvalidFieldTypeError(
                instance.get_schema_variable(),
                instance.row_number,
                {"variable": self.get_error_variable(instance)},
                self.type, value,
            )

        return value

    def __set__(self, instance: BaseExcelModel, value: Any):
        instance.values[self.name] = self.validate_and_convert_value(instance, value)

    @property
    def name(self):
        if not self._name:
            raise ValueError(
                "Field must be set to BaseExcelModel before using this method"
            )
        return self._name

    def set_name(self, value: str):
        self._name = value

    def set_type(self, value: type):
        self.type = value


class CoordinatesField(Field):
    def validate_and_convert_value(self, instance: BaseExcelModel, value: Any):
        value = super().validate_and_convert_value(instance, value)
        if not isinstance(value, str):
            return value

        split_coordinates = value.split(",")
        if len(split_coordinates) == 2:
            latitude, longitude = map(
                lambda x: str_to_float(x.strip(), no_error=True),
                split_coordinates,
            )

            if latitude and longitude:
                if not (-90 <= latitude <= 90):
                    raise CoordinatesInvalidLatitudeError(
                        instance.get_schema_variable(),
                        row_number=instance.row_number,
                        field_name={"variable": self.get_error_variable(instance)},
                        actual_value=value,
                    )

                if not (-180 <= longitude <= 180):
                    raise CoordinatesInvalidLongitudeError(
                        instance.get_schema_variable(),
                        row_number=instance.row_number,
                        field_name={"variable": self.get_error_variable(instance)},
                        actual_value=value,
                    )

                return f"{latitude},{longitude}"

        raise InvalidCoordinatesFormatError(
            instance.get_schema_variable(),
            row_number=instance.row_number,
            field_name={"variable": self.get_error_variable(instance)},
            actual_value=value,
        )


class ExcelModelMeta(ABCMeta):
    _fields: dict[str, Field]
    _allow_custom_fields: bool
    _allowed_custom_fields: Iterable[str] | Literal["*"]

    def __new__(
            mcs, name: str, bases: tuple[type, ...], namespace: dict[str, Any],
            base: bool = False, **kwargs
    ):
        cls = super().__new__(mcs, name, bases, namespace, **kwargs)

        if base:
            return cls

        fields = {}

        for prop_name, field in namespace.items():
            if isinstance(field, Field):
                field.set_name(prop_name)
                field.set_type(detect_type(cls, prop_name))

                fields[prop_name] = field

        cls._fields = fields

        return cls


def detect_type(cls: type, field_name: str):
    type_hints = get_type_hints(cls)

    field_type = type_hints.get(field_name, str)
    if type(field_type) is GenericAlias:
        field_type = get_origin(field_type)

    return field_type


class BaseExcelModel(metaclass=ExcelModelMeta):
    _allow_custom_fields: bool

    @classmethod
    async def get_verbose_names(cls, lang: str):
        field_names = []
        variables = []

        for field_name, field in cls._fields.items():
            field_names.append(field_name)
            if field.export_variable not in variables:
                variables.append(field.export_variable)

        verbose_names = await fl(variables, lang)
        variables_values = dict(zip(variables, verbose_names))

        result = {}
        for field_name, field in cls._fields.items():
            result[field_name] = variables_values[field.export_variable]
        return result

    @classmethod
    async def check_fields(cls, name: str, lang: str):
        for field in cls._fields.values():
            verbose_name = await f(field.variable, lang)
            if verbose_name == name:
                yield field.name, "variable"

            if field.variable == field.export_variable:
                continue

            verbose_name = await f(field.export_variable, lang)
            if verbose_name == name:
                yield field.name, "export_variable"

    @classmethod
    def get_field_by_name(cls, field_name: str):
        if field_name not in cls._fields:
            raise TypeError(f"Field with name {field_name} does not exist")
        return cls._fields.get(field_name)

    @classmethod
    async def get_field_verbose_name(cls, field_name: str, lang: str):
        field = cls.get_field_by_name(field_name)
        return await field.get_verbose_name(lang)

    @classmethod
    def __get_fields_for_saving__(cls) -> list:
        return list(filter(lambda x: not x.export_exclude, cls._fields.values()))

    @classmethod
    def get_schema_variable(cls, prefix: str | None = None):
        items = [prefix, paschal_case_to_snake_case(cls.__name__), "excel schema"]
        return " ".join([item for item in items if item is not None])

    @classmethod
    async def get_schema_name(cls, lang: str):
        return await f(cls.get_schema_variable(), lang)

    def __init__(
            self, row_number: int,
            values: dict[str, str] | None = None,
            custom_fields: dict[str, str] | None = None,
    ):
        if custom_fields and not self._allow_custom_fields:
            raise ValueError("Custom fields are not allowed for this model")
        self._custom_fields: dict[str, Any] = custom_fields or {}

        self.row_number = row_number + 2
        self.values: dict[str, Any] = values or {}

    @property
    def allow_custom_fields(self) -> bool:
        return self._allow_custom_fields

    @property
    def custom_fields(self):
        if not self._allow_custom_fields:
            raise ValueError("Custom fields are not allowed for this model")
        return self._custom_fields

    def to_dict(self, add_custom_fields: bool = None, exclude: set | None = None):
        if add_custom_fields is None:
            add_custom_fields = self._allow_custom_fields

        d = {}
        for field_name in self._fields:
            if not exclude or field_name not in exclude:
                d[field_name] = getattr(self, field_name)

        if add_custom_fields:
            d["custom_fields"] = self.custom_fields

        return d

    def __str__(self):
        return f"Model {self.__class__.__name__} {self.to_dict()}"
