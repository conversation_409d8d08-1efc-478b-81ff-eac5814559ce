from abc import ABC
from datetime import datetime
import inspect
import os

from openpyxl.workbook import Workbook


class BaseExcelAdapter(ABC):
    external_type: str = None

    def __init__(self, lang: str, *args, **kwargs):
        self.lang = kwargs.get("lang", lang)
        self.path_to_save: str = kwargs.get("path_to_save")
        super().__init__(*args, **kwargs)

    def __init_subclass__(cls, **kwargs):
        if not inspect.isabstract(cls):
            external_type = kwargs.pop("external_type", None)
            if not external_type:
                raise TypeError("external_type is required for subclasses of BaseExcelAdapter")
            cls.external_type: str = external_type
        super().__init_subclass__(**kwargs)

    def __save_excel_file__(self, wb: Workbook):
        file_name = "".join(str(datetime.utcnow().timestamp()).split("."))
        excel_file: str = ".".join([file_name, "xlsx"])
        os.makedirs(self.path_to_save, exist_ok=True)
        excel_file = os.path.join(self.path_to_save, excel_file)
        wb.save(excel_file)

        return excel_file
