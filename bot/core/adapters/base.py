from __future__ import annotations

import abc
from typing import Protocol, Type

from utils.type_vars import T


class BaseAdapterType(Protocol):

    def __init__(self, **kwargs):
        ...

    @classmethod
    def get_adapter(cls: Type[T], name: str) -> Type[T] | None:
        ...


class BaseAdapter(abc.ABC):

    def __init__(self, *args, **kwargs):
        super().__init__()

    @classmethod
    def get_adapter(cls, name: str) -> Type[BaseAdapter] | None:
        return cls._adapters.get(name)

    @classmethod
    def get_available_adapters_names(cls) -> list[str]:
        return list(cls._adapters.keys())
