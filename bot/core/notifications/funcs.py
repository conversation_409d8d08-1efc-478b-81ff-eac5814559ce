from core import messangers_adapters as ma
from core.kafka.producer.functions import build_and_send_bot_message
from db.models import ClientBot, MediaObject, User


async def put_message_to_kafka(
        bot: ClientBot, 
        user: User,
        text: str, 
        content_type: str,
        keyboard:  ma.Keyboard | None = None,
        media:  MediaObject | bool = None, 
        content: dict | None = None, 
        logger: any = None
):
    await build_and_send_bot_message(
        bot.id, bot.display_name,
        bot.bot_type, bot.token,
        bot_wa_from=bot.whatsapp_from,
        tg_chat_id=user.chat_id,
        wa_phone=user.wa_phone,
        user_id=user.id,
        user_name=user.name,
        content_type=content_type,
        text=text,
        media=media,
        content=content,
        keyboard=keyboard,
        logger=logger,
    )