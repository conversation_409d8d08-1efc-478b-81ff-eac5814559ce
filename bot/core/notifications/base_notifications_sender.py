import asyncio
from abc import ABC, abstractmethod
from typing import Generic, TypeVar

import aiowhatsapp as wa
from aiogram.utils.parts import safe_split_text

import core.messangers_adapters as ma
from config import LOC7_EMAIL_LOGIN, LOC7_EMAIL_PASSWORD
from db import crud
from db.models import (
    Brand, ClientBot, Group, MenuInStore, Store,
    User, UserClientBotActivity,
)
from loggers import JSONLogger
from schemas import (
    NotificationLevel, NotificationRecipientType, SendEmailData,
    SystemNotificationCategory,
    SystemNotificationType,
)
from utils.email_funcs import is_valid_email
from utils.log_func_exec_time import log_func_exec_time
from utils.text import f, html_to_markdown
from .funcs import put_message_to_kafka
from ..admin_notification.service import create_system_notification
from ..kafka.producer.functions import add_email_notifications_for_action
from ..templater import templater
from ..whatsapp.keyboards import get_wa_menu_keyboard

TemplateT = TypeVar("TemplateT")


class BaseNotificationsSender(ABC, Generic[TemplateT]):
    BOT_TEMPLATE_PATH: str

    def __init__(
            self,
            lang: str,
            brand: Brand,
            group: Group,
            user: User,
            bot: ClientBot | None = None,
            store: Store | None = None,
    ):
        self.lang: str = lang
        self.brand: Brand = brand
        self.user: User = user
        self.group: Group = group
        self.bot: ClientBot | None = bot

        self.store: Store | None = store
        self.menu_in_store: MenuInStore | None = None
        self.notify_user: bool = True
        self.logger = JSONLogger(
            "notifications",
            {
                "group_id": group.id,
                "brand_id": brand.id,
                "user_id": user.id,
                "bot_id": bot.id if bot else None,
                "store_id": self.store.id if self.store else None,
                "menu_in_store_id": self.menu_in_store.id if self.menu_in_store else
                None,
                "notify_user": self.notify_user,
                "user_email": self.user.email,
                "user_chat_id": self.user.chat_id,
                "user_wa_phone": self.user.wa_phone,
            }
        )

    async def get_user_email(self):
        self.logger.debug("get_user_email ->")
        user_email = self.user.email
        if not user_email or not is_valid_email(user_email):
            self.logger.error(f'User E-mail not valid ')
            return None
        return user_email

    @property
    async def admin_emails(self) -> list[str]:
        self.logger.debug("admin_emails ->")
        user_email = await self.get_user_email()
        manager_emails = await crud.get_admin_emails(
            self.brand.id, self.store.id if self.store else None
        )
        return list(filter(lambda x: x != user_email, manager_emails))

    @abstractmethod
    async def build_template(self) -> TemplateT:
        raise NotImplementedError

    async def start(self):
        self.logger.debug("start ->")

        with log_func_exec_time("build_template", self.logger):
            template = await self.build_template()
            self.logger.debug("self.build_template() — OK")

        if self.notify_user:
            with log_func_exec_time("send_user_message", self.logger):
                await self.send_user_message(self.group, self.user, template)

        if admin_emails := await self.admin_emails:
            with log_func_exec_time("send_email_message", self.logger):
                await self.send_email_message(template, admin_emails)

    def select_sender_bot(self, user: User) -> tuple[ClientBot, str | int]:
        # TODO  add select next available bot
        return self.bot, ma.get_user_to(user, self.bot.bot_type)

    async def send_bot_message_deprecated(self, template: TemplateT):
        self.logger.debug("send_bot_message ->")

        if not self.bot:
            self.logger.debug(f"Message not sent. There is no bot")
            return

        user_bot_activity = await UserClientBotActivity.get(
            self.user,
            self.bot,
            create=False,
        )
        if not user_bot_activity or not user_bot_activity.is_entered_bot:
            self.logger.debug(f"Message not sent. User has not entered bot yet")
            return

        text = await templater.make_template(
            template, self.group.id,
            self.BOT_TEMPLATE_PATH,
            br2nl=True, minify=True,
            bot_type=self.bot.bot_type,
        )

        keyboard = await self.get_keyboard(template)

        match self.bot.bot_type:
            case "telegram":
                if not self.user.chat_id:
                    self.logger.debug(
                        f"Message not sent. There is no telegram connected to user"
                    )
                    return

                await put_message_to_kafka(
                    self.bot, self.user, text,
                    "text", keyboard, logger=self.logger
                )

                self.logger.debug(f"await send_tg_message... Ok")
                await self.after_send_message(template)

            case "whatsapp":
                if keyboard and not isinstance(keyboard, ma.wa.types.ListKeyboard):
                    err_msg = "Only ma.wa.types.ListKeyboard is supported"
                    self.logger.error(err_msg)
                    raise ValueError(err_msg)

                if not self.user.wa_phone:
                    self.logger.debug(
                        f"Message not sent. There is no whatsapp connected to user, "
                        f"{self.user.wa_phone=}"
                    )
                    return

                self.logger.debug(f"{self.user.wa_phone=}")
                text = html_to_markdown(text)

                wa_bot = wa.WhatsappBot(
                    self.bot.token,
                    self.bot.whatsapp_from,
                )

                is_something_sent = False
                is_something_sent_ = False

                end_keyboard_separately = await (
                    self.get_wa_send_keyboard_separately(
                        template
                    ))

                if (
                        keyboard and
                        len(text) <= 1024 and
                        end_keyboard_separately
                ):
                    try:
                        await wa_bot.send_interactive_list(
                            to=self.user.wa_phone,
                            header=template.header[:60] if hasattr(
                                template, "header"
                            ) and template.header else None,
                            body=text,
                            button=keyboard.button,
                            sections=keyboard.sections
                        )
                        self.logger.debug(
                            "await wa_bot.send_interactive_list (wa_main_menu) "
                            "for short text... Ok"
                        )

                        is_something_sent = await self.after_send_message(template)
                    except Exception as e:
                        self.logger.error(e, exc_info=True)
                else:
                    try:
                        parts = safe_split_text(text, 4096, "\n")
                        for part in parts:
                            await wa_bot.send_message(
                                self.user.wa_phone,
                                part
                            )
                            self.logger.debug("wa_bot.send_message... Ok")
                        is_something_sent_ = True
                    except Exception as e:
                        self.logger.error(e, exc_info=True)

                    is_something_sent = await self.after_send_message(template)

                if is_something_sent or is_something_sent_:
                    if is_something_sent or is_something_sent_:
                        asyncio.ensure_future(
                            self.send_delayed_wa_menu(
                                self.user.wa_phone, wa_bot, keyboard
                            )
                        )
                        self.logger.debug(
                            "Scheduled delayed sending of wa_main_menu for long "
                            "text or after send message"
                        )

            case _:
                err_msg = f"Unknown bot type, {self.bot.bot_type=}"
                self.logger.error(err_msg)
                raise ValueError(err_msg)

        self.logger.debug("send bot message Ok")

    async def get_keyboard(self, template: TemplateT) -> ma.Keyboard:
        self.logger.debug("get_keyboard ->")
        if self.bot.bot_type == "whatsapp":
            return await get_wa_menu_keyboard(
                self.user, self.bot, self.lang, self.menu_in_store
            )
        return None

    async def after_send_message(self, template: TemplateT) -> bool:
        return False

    async def get_wa_send_keyboard_separately(self, template: TemplateT):
        return True

    async def get_email_attachments(
            self, template: TemplateT
    ):
        return []

    async def send_user_message(
            self,
            group: Group,
            user: User,
            template: TemplateT,
    ):
        self.logger.debug("send_system_message ->")

        email_to = await self.get_user_email()

        self.logger.debug(
            "send_user_message: Email for notification",
            {
                "email_to": email_to,
                "user_id": user.id,
                "user_name": user.name
            }
        )

        html = await templater.make_template(
            template, self.group.id, for_admin=False,
        ) if email_to else None

        text = await templater.make_template(
            template, self.group.id,
            self.BOT_TEMPLATE_PATH,
            br2nl=True, minify=True,
            bot_type=self.bot.bot_type if self.bot else None,
        )

        keyboard = await self.get_keyboard(template) if self.bot else None

        user_notification = await create_system_notification(
            scope="profile:user",
            group_id=group.id,
            category=SystemNotificationCategory.ORDER,
            type_notification=SystemNotificationType.CHANGE_STATUS,
            title=template.header,
            content=text,
            level=NotificationLevel.INFO,
            recipient_type=NotificationRecipientType.USER,
            recipient_id=user.id,
            html=html,
            from_name=template.from_name,
            attachments=[
                coupon.pdf for
                coupon in template.coupons or []
                if coupon.pdf
            ],
            keyboard=keyboard if keyboard else None,
            email_to=email_to,
        )
        self.logger.debug(f"send_system_message Ok, {user_notification=}")

        is_something_sent = await self.after_send_message(template)

        if (is_something_sent and self.user.wa_phone and self.bot.bot_type ==
                "whatsapp" and keyboard):
            async def send_delayed_menu():
                await asyncio.sleep(4)
                await put_message_to_kafka(
                    self.bot, self.user,
                    await f("main menu button", self.lang),
                    "text", keyboard, logger=self.logger
                )

            asyncio.create_task(send_delayed_menu())

    async def send_email_message(
            self,
            template: TemplateT,
            emails: list[str],
    ):
        self.logger.debug("send_email_message ->")

        html = await templater.make_template(
            template, self.group.id, for_admin=True,
        )

        await add_email_notifications_for_action(
            LOC7_EMAIL_LOGIN,
            LOC7_EMAIL_PASSWORD,
            "profile:admin",
            {"emails": emails},
            SendEmailData(
                subject=template.header,
                body="",
                html=html,
                from_name=template.from_name,
            ),
            logger=self.logger,
            no_error=True,
        )

        self.logger.debug(
            f"send_email_message... Ok\n{template.header=}"
        )

    async def send_delayed_wa_menu(
            self, to: str, wa_bot: wa.WhatsappBot, keyboard: ma.wa.types.ListKeyboard
    ):
        await asyncio.sleep(4)  # 4-second delay
        try:
            await wa_bot.send_interactive_list(
                to=to,
                body=await f("main menu button", self.lang),
                button=keyboard.button,
                sections=keyboard.sections
            )
            self.logger.debug(
                "Delayed wa_main_menu sent successfully after 4 seconds"
            )
        except Exception as e:
            self.logger.error(
                f"Error sending delayed wa_main_menu: {e}", exc_info=True
            )
