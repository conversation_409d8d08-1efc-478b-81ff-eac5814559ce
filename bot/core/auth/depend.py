import logging
from fastapi import Depends, HTTPException, Header, Security, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer, SecurityScopes
from starlette.requests import Request
from typing import Annotated, Iterable

import schemas
from core.auth.functions import parse_access_token
from db import models
from db.models import AuthSession, User
from exceptions import (
    AuthAccountNotFoundError, AuthNoRequiredAuthSessionError, AuthNotAuthorisedError,
    AuthNotEnoughPermissionsError, AuthUserIsDeactivatedError,
    BaseAuthError,
)
from utils.scopes_map import ACTION_NONE, scope_map

debugger = logging.getLogger("debugger")

http_bearer_scheme = HTTPBearer(
    auto_error=False,
)


def get_token(
        data: HTTPAuthorizationCredentials | None = Depends(http_bearer_scheme)
):
    if not data:
        return None
    return data.credentials


async def get_web_app_data(request: Request) -> schemas.WebAppData | None:
    return request.state.web_app_data


async def get_current_tg_user(
        web_app_data: schemas.WebAppData | None = Depends(get_web_app_data)
) -> models.User:
    exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail='Could not validate credentials',
        headers={
            'WWW-Authenticate': 'Bearer'
        }
    )

    if not isinstance(web_app_data, schemas.WebAppData):
        raise exception

    user = await models.User.get(web_app_data.user.id)
    if not user:
        raise exception

    return user


async def get_current_tg_user_safe(
        web_app_data: schemas.WebAppData | None = Depends(get_web_app_data)
) -> models.User | None:
    try:
        user = await get_current_tg_user(web_app_data)
        return user
    except Exception as ex:
        logging.error(ex, exc_info=True)
        return None


async def get_user_or_token_data_safe(
        request: Request,
        security_scopes: SecurityScopes,
        token: str = Depends(get_token),
        web_app_data: schemas.WebAppData | None = Depends(get_web_app_data),
):
    try:
        return await get_user_or_token_data(
            request, security_scopes, token, web_app_data
        )
    except (HTTPException, BaseAuthError):
        return None
    except:
        raise


async def get_user_or_token_data(
        request: Request,
        security_scopes: SecurityScopes,
        token: str = Depends(get_token),
        web_app_data: schemas.WebAppData | None = Depends(get_web_app_data),
):
    if web_app_data:
        user = await models.User.get(web_app_data.user.id)
        if user:
            return user
        return AuthAccountNotFoundError()

    token_data = parse_access_token_depend(token, security_scopes)
    if token_data.get("type") != "user":
        return token_data

    if "session_id" in token_data:
        session = await AuthSession.get(token_data.get("session_id"))
        request.state.token_session_id = token_data.get("session_id")
        if session:
            request.state.session = session
            request.state.session_info = {
                "id": str(session.id),
                "auth_source": session.auth_source.value,
                "device_info": session.device_info,
                "last_refresh_datetime": (
                    session.last_refresh_datetime.isoformat() if
                    session.last_refresh_datetime else None
                ),
                "expire_datetime": session.expire_datetime.isoformat(),
                "is_push_token": bool(session.push_token),
                "push_token_update_datetime": (
                    session.push_token_update_datetime.isoformat() if
                    session.push_token_update_datetime else None
                ),
            }

        if not session or not session.is_valid:
            raise AuthNotAuthorisedError()

    user = await models.User.get_by_id(int(token_data.get("sub")))
    if not user:
        raise AuthNotAuthorisedError()

    request.state.user = user

    request.state.user_data = {
        "id": str(user.id),
        "chat_id": str(user.chat_id),
        "wa_phone": user.wa_phone,
        "wa_name": user.wa_name,
        "username": user.username,
        "name": user.name,
        "email": user.email,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "full_name": user.full_name,
        "status": user.status,
        "incust_external_id": user.incust_external_id,
        "is_guest_user": user.is_guest_user,
    }

    for scope in security_scopes.scopes:
        if (
                scope not in scope_map.available_actions and
                scope != ACTION_NONE and
                not user.is_scope_allowed(scope)
        ):
            debugger.debug(f"Not enough permissions {user}: {scope}")
            raise AuthNotEnoughPermissionsError()

    return user


def parse_access_token_depend(
        token: Annotated[str, Depends(get_token)],
        security_scopes: SecurityScopes,
) -> dict:
    return parse_access_token(token, security_scopes)


async def parse_access_token_safe(
        security_scopes: SecurityScopes,
        token: str = Depends(get_token),
) -> dict | None:
    if not token:
        return None

    return parse_access_token_depend(token, security_scopes)


async def get_user(
        user_or_token_data: dict | User = Depends(get_user_or_token_data),
):
    if not isinstance(user_or_token_data, User):
        raise AuthNotAuthorisedError()

    return user_or_token_data


async def get_active_user(
        user: models.User = Security(get_user, scopes=["me:read"]),
):
    if user.status == "deactivated":
        raise AuthNotAuthorisedError()
    return user


async def get_user_optional(
        user_or_token_data: User | dict | None = Depends(get_user_or_token_data_safe),
) -> User | None:
    if isinstance(user_or_token_data, User):
        return user_or_token_data
    return None


async def get_active_user_optional(
        user: User | None = Security(get_user_optional, scopes=["me:read"])
):
    if user and user.status == "deactivated":
        raise AuthUserIsDeactivatedError()
    return user


# noinspection PyPep8Naming
def Action(
        default: str | None = None,
        allowed: Iterable[str] | None = None,
) -> str | None:
    def depend(scopes: SecurityScopes):
        scopes_actions = []
        for scope in scopes.scopes:
            if (
                    (
                            scope == ACTION_NONE or
                            scope in scope_map.available_actions
                    ) and
                    (
                            not allowed or scope in allowed
                    )
            ):
                scopes_actions.append(scope)

        if len(scopes_actions) > 1:
            raise ValueError("You have to provide only one action in scopes")

        action = scopes_actions[0] if scopes_actions else default

        if action == ACTION_NONE:
            return None

        return action

    return Depends(depend)


async def get_auth_session_by_refresh_token(
        token_data: dict = Security(
            parse_access_token_depend, scopes=["refresh_token"]
        ),
):
    if token_data.get("type") != "refresh_token":
        raise AuthNotAuthorisedError()

    auth_session = await AuthSession.get(token_data.get("sub"))
    if not auth_session or not auth_session.is_active or auth_session.is_expired:
        raise AuthNotAuthorisedError()

    return auth_session


async def get_auth_session_by_access_token(
        token_data: dict = Security(parse_access_token_depend, scopes=["me:read"]),
):
    if not (session_id := token_data.get("session_id")):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Token was created without session"
        )

    auth_session = await AuthSession.get(session_id)
    if not auth_session:
        raise AuthNotAuthorisedError()

    return auth_session


async def get_auth_session_from_access_token(
        token_data: dict = Depends(parse_access_token_depend),
) -> AuthSession | None:
    if token_data.get("session_id"):
        auth_session = await AuthSession.get(token_data.get("session_id"))
        if auth_session and auth_session.is_active and not auth_session.is_expired:
            return auth_session
    return None


async def get_required_auth_session_from_access_token(
        auth_session: AuthSession | None = Depends(get_auth_session_from_access_token)
) -> AuthSession:
    if not auth_session:
        raise AuthNoRequiredAuthSessionError()
    return auth_session


def get_custom_token_data(
        token_type: str,
        header_name: str | None = None,
        optional: bool = False,
):
    if not header_name:
        header_name = f"X-{token_type.capitalize()}-Token"

    def depend_func(
            token: str | None = Header(None, alias=header_name),
    ) -> dict | None:
        if not token:
            if optional:
                return
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"{header_name} Header is required!"
            )

        token_scheme, token = token.split(" ", 1)
        if token_scheme.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )

        token_data = parse_access_token(token)
        if token_data.get("type") != token_type:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid token. Expected token of type {token_type}"
            )

        return token_data

    return depend_func


def user_or_custom_token_data_depend(
        token_type: str,
        header_name: str | None = None,
        optional: bool = False,
):
    if not header_name:
        header_name = f"X-{token_type.capitalize()}-Token"

    def depend_func(
            custom_token_data: dict | None = Depends(
                get_custom_token_data(token_type, header_name, True)
            ),
            user_or_token_data: User | dict | None = Depends(
                get_user_or_token_data_safe
            ),
    ) -> User | dict | None:
        if custom_token_data:
            return custom_token_data

        if isinstance(user_or_token_data, User):
            return user_or_token_data

        if not user_or_token_data:
            if optional:
                return

            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Authorization or {header_name} Header is required!"
            )

        if user_or_token_data.get("type") != token_type:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid token. Expected token of type {token_type}"
            )

        return user_or_token_data

    return depend_func
