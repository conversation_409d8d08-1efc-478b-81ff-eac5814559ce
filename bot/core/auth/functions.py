import json
import urllib.parse
from aiogram.utils.auth_widget import check_integrity
from datetime import datetime, timedelta
from fastapi.security import SecurityScopes
from sqlalchemy.orm import Session

import schemas
from client.main.keyboards import get_menu_keyboard as get_client_menu_keyboard
from config import (
    CONFIRM_EMAIL_TOKEN_EXPIRES, MIN_PASSWORD_LENGTH, NEW_ACCESS_TOKEN_EXPIRE,
    PATH_TO_MARKETING_CONSENT_JSON,
    USER_AUTH_TOKEN_EXPIRE, WEB_APP_ADMIN_URL, WEB_APP_PATH,
)
from core.templater import templater
from db import models
from db.models import AuthSession, Brand, ClientBot, ConfirmEmailRequest, User
from exceptions import (
    AuthEmailExistsError, AuthEmailNotConfirmedError, AuthIncorrectTelegramDataError,
    AuthNotAuthorisedError, AuthNotEnoughPermissionsError, AuthPasswordNotAsciiError,
    AuthPasswordTooShortError,
    AuthRegisterAgreementNotAcceptedError, AuthTelegramExistsError,
    AuthTokenExpiredError,
)
from schemas import ConfirmEmailTemplate
from service.main.keyboards import get_menu_keyboard as get_service_menu_keyboard
from utils.jwt_token import create_jwt_token
from utils.message import send_tg_message
from utils.scopes_map import scope_map
from utils.text import f
from utils.translator import Translator
from .db_funcs import check_exists_chat_id, check_exists_email, check_is_confirmed_email
from .parse_token import parse_token


def validate_password(password: str):
    if not password or len(password) < MIN_PASSWORD_LENGTH:
        raise AuthPasswordTooShortError(MIN_PASSWORD_LENGTH)

    if not password.isascii():
        raise AuthPasswordNotAsciiError()

    return True


async def validate_email(
        db: Session, email: str,
        purpose: schemas.ConfirmEmailPurposeLiteral | None = None,
        validate_exists: bool = True,
        validate_confirm: bool = True,
):
    if validate_exists and await check_exists_email(db, email):
        raise AuthEmailExistsError(email)

    if validate_confirm and not purpose:
        raise ValueError(
            "specified validate_confirm is True, but purpose is not specified"
        )

    if validate_confirm and not await check_is_confirmed_email(db, email, purpose):
        raise AuthEmailNotConfirmedError()

    return True


async def validate_telegram_data_and_chat_id(
        db: Session, tg_data: schemas.TelegramData, bot: models.ClientBot
):
    if not check_integrity(
            bot.token, tg_data.dict(exclude_unset=True, exclude_none=True)
    ):
        raise AuthIncorrectTelegramDataError()

    if await check_exists_chat_id(db, tg_data.id):
        raise AuthTelegramExistsError()


def create_user_access_token(
        user_id: int,
        expire: datetime | timedelta | None = None,
        scopes: list[str] | None = None,
        session_id: int | None = None
):
    data = {
        "sub": f"{user_id}",
        "type": "user",
        "scopes": scopes
    }
    if session_id:
        data["session_id"] = session_id
    token = create_jwt_token(data=data, expire=expire)

    return token


def create_user_auth_token(
        user_id: int,
        expire: datetime | timedelta | None = None,
):
    if expire is None:
        expire = USER_AUTH_TOKEN_EXPIRE
    data = {
        "sub": f"{user_id}",
        "type": "auth",
        "scopes": ["auth"]
    }
    return create_jwt_token(data, expire)


def create_refresh_token(auth_session_id: int, expire: datetime):
    token = create_jwt_token(
        data={
            "sub": str(auth_session_id),
            "type": "refresh_token",
            "scopes": ["refresh_token"]
        }, expire=expire
    )
    return token


def create_auth_tokens_for_session(session: AuthSession):
    user_token = create_user_access_token(
        session.user_id,
        NEW_ACCESS_TOKEN_EXPIRE,
        session_id=session.id
    )
    refresh_token = create_refresh_token(session.id, session.expire_datetime)

    return schemas.AuthorisedResponse(
        session_id=session.id,
        expire_datetime=session.expire_datetime,
        auth_source=session.auth_source,
        device_info=session.device_info,
        access_token=schemas.Token(
            token=user_token,
            token_type="bearer",
        ),
        refresh_token=schemas.Token(
            token=refresh_token,
            token_type="bearer",
        ),
    )


async def create_auth_session_and_get_tokens(
        user: User,
        auth_source: schemas.AuthSourceEnum,
        device_info: str,
):
    session = await AuthSession.create(
        auth_source=auth_source,
        device_info=device_info,
        user=user,
        expire_datetime=AuthSession.make_expire_datetime(),
    )

    return create_auth_tokens_for_session(session)


def create_oauth_access_token(
        data: schemas.OAuthRegisterData, expire: datetime | timedelta | None = None,
        scopes: list[str] | None = None,
):
    token = create_jwt_token(
        data={
            "type": "oauth_register_data",
            "scopes": scopes,
            "first_name": data.first_name,
            "last_name": data.last_name,
            "lang": data.lang,
            "email": data.email,
            "email_verified": data.email_verified,
            "photo": data.photo or "",
            "oauth_type": data.oauth_type,
            "host": data.host,
            "continue_url": data.continue_url,
        }, expire=expire
    )

    return token


def create_check_is_email_confirmed_token(request: ConfirmEmailRequest):
    return create_jwt_token(
        {
            "request_id": str(request.id),
            "scopes": "checkIsEmailConfirmed"
        }, expire=CONFIRM_EMAIL_TOKEN_EXPIRES
    )


def validate_is_accepted_agreement(is_accept_agreement: bool):
    if not is_accept_agreement:
        raise AuthRegisterAgreementNotAcceptedError()
    return True


async def notify_user_about_lang_change(
        user: User, bot: ClientBot | str, new_lang: str
):
    bot_token = bot.token if isinstance(bot, ClientBot) else bot
    if not bot_token:
        return

    new_lang_name = await Translator.get_language_name(new_lang, new_lang)

    if bot == "service":
        keyboard = await get_service_menu_keyboard()
    else:
        keyboard = await get_client_menu_keyboard(user, bot)

    if user.chat_id:
        await send_tg_message(
            user.chat_id, "text", bot_token=bot_token, keyboard=keyboard,
            text=await f("language changed", new_lang, new_lang=new_lang_name)
        )


async def make_confirm_email_html(
        brand: Brand | None,
        request: ConfirmEmailRequest, lang: str,
        user_id: int | None = None,
        is_external_user: bool = False,
):
    match request.purpose:
        case "register":
            scope = "emailConfirm"
        case "reset_password":
            scope = "resetPassword"
        case "change_email":
            scope = ["emailConfirm", "changeEmail"]
        case "set_email_admin":
            scope = ["emailConfirm", "changeEmail"]
        case _:
            scope = "unknownScope"

    token_data = {
        "request_id": str(request.id),
        "scopes": scope,
    }
    if user_id:
        token_data["user_id"] = user_id

    confirm_token = create_jwt_token(token_data, expire=CONFIRM_EMAIL_TOKEN_EXPIRES)

    user = None
    if user_id:
        user = await User.get_by_id(user_id)

    match request.purpose:
        case "register":
            header = await f("confirm email header", lang)
            button_name = await f("confirm email button", lang)

            if brand:
                link = brand.get_url(
                    "auth/confirm",
                    token=confirm_token,
                    lang=lang,
                )
            else:
                link = f"{WEB_APP_PATH}/auth/confirm?token={confirm_token}&lang={lang}"
        case "reset_password":
            button_name = await f("confirm email for reset password button", lang)
            header = await f(
                "confirm email for reset password header", lang, button_name=button_name
            )

            if brand:
                link = brand.get_url(
                    "auth/reset_password",
                    token=confirm_token,
                    email=urllib.parse.quote(request.email),
                    lang=lang,
                )
            else:
                link = (
                    f"{WEB_APP_PATH}/auth/reset_password?"
                    f"token={confirm_token}&"
                    f"email={urllib.parse.quote(request.email)}&"
                    f"lang={lang}"
                )
        case "change_email":
            if user and user.hashed_password:
                is_external_user = False
            button_name = await f("web app auth change email button", lang)
            header = await f(
                "confirm email for change email header", lang, button_name=button_name
            )

            if brand:
                link = brand.get_url(
                    "auth/change_email",
                    token=confirm_token,
                    email=urllib.parse.quote(request.email),
                    lang=lang,
                    is_external_user=is_external_user,
                )
            else:
                link = f"{WEB_APP_PATH}/auth/change_email?token={confirm_token}"
                link += (f"&email={urllib.parse.quote(request.email)}&lang="
                         f"{lang}&is_external_user={is_external_user}")
        case "set_email_admin":
            link = (
                f"{WEB_APP_ADMIN_URL}/{lang}/profile/change_email?"
                f"confirm_email_token={confirm_token}"
            )
            link += f"&email={urllib.parse.quote(request.email)}"
            if user and not user.hashed_password:
                link += "&need_new_password=true"
            button_name = await f("web app auth change email button", lang)
            header = await f(
                "confirm email for change email header", lang, button_name=button_name
            )
        case _:
            header = "unknown"
            button_name = "unknown"
            link = "/"

    template = ConfirmEmailTemplate(
        header=header,
        button_name=button_name,
        button_link=link,
    )

    return await templater.make_template(template, brand.group_id if brand else None)


def get_marketing_info(lang: str, profile_name: str) -> schemas.MarketingInfo:
    text = ""
    with open(PATH_TO_MARKETING_CONSENT_JSON, 'r', encoding='utf-8') as file:
        data_dict = json.load(file)
    if data_dict:
        text = data_dict.get(lang, None)
        if not text:
            text = data_dict["en"]
        text = text.replace("{profile_name}", profile_name)

    return schemas.MarketingInfo(consent_text=text)


def parse_access_token(
        token: str,
        security_scopes: SecurityScopes | None = None,
) -> dict:
    exception = AuthNotAuthorisedError()
    exp_exception = AuthTokenExpiredError()

    if not token:
        raise exception

    payload, token_scopes = parse_token(token, exception, exp_exception)
    if payload is None:
        raise exception

    if not security_scopes:
        return payload

    for scope in security_scopes.scopes:
        if (
                token_scopes is not None and
                scope not in token_scopes and
                scope not in scope_map.available_actions
        ):
            raise AuthNotEnoughPermissionsError()

    return payload
