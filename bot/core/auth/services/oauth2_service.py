import aiohttp
import logging
import re
from datetime import timed<PERSON><PERSON>
from fastapi.responses import RedirectResponse
from jose import jwt
from urllib.parse import quote

import schemas
from config import (
    ACCESS_TOKEN_EXPIRES, APPLE_AUTH_API_REDIRECT_URL, APPLE_AUTH_CLIENT_ID,
    APPLE_AUTH_KEY_ID, APPLE_AUTH_PRIVATE_KEY, APPLE_AUTH_REDIRECT_URL,
    APPLE_AUTH_TEAM_ID,
)
from core.auth.functions import (
    create_auth_session_and_get_tokens, create_oauth_access_token,
    create_user_access_token,
    parse_access_token,
)
from core.auth.parse_token import parse_token
from core.media_manager import media_manager
from db import crud
from db.models import Customer, ShortToken, User
from exceptions import (
    AuthAppleCallbackError, AuthAppleOAuthError, AuthGoogleOAuthError,
    AuthNotAuthorisedByOAuthError, AuthNotProvidedDataOAuthError,
    AuthNotProvidedHostUrlError, Auth<PERSON>okenExpiredError, AuthUnknownOAuthProviderError,
)
from utils.date_time import utcnow


class OAuth2Service:
    @staticmethod
    async def process_google(access_token: str) -> schemas.OAuthGoogleUserInfo:
        url = (f"https://www.googleapis.com/oauth2/v1/userinfo?access_token="
               f"{access_token}")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    user_info = schemas.OAuthGoogleUserInfo(**await response.json())
                    return user_info
        except aiohttp.ClientError as e:
            logging.error(f"Error during google auth: {e}", exc_info=True)
            raise AuthGoogleOAuthError()

    @staticmethod
    async def process_apple(
            access_token: str,
            is_native: bool = False,
    ) -> schemas.OAuthAppleUserInfo:
        headers = {
            'alg': "ES256",
            'kid': APPLE_AUTH_KEY_ID,
        }

        payload = {
            'iss': APPLE_AUTH_TEAM_ID,
            'iat': int(utcnow().timestamp()),
            'exp': int((utcnow() + timedelta(days=180)).timestamp()),
            'aud': 'https://appleid.apple.com',
            'sub': APPLE_AUTH_CLIENT_ID,
        }

        client_secret = jwt.encode(
            payload,
            APPLE_AUTH_PRIVATE_KEY,
            algorithm='ES256',
            headers=headers
        )

        url = "https://appleid.apple.com/auth/token"
        headers = {'content-type': "application/x-www-form-urlencoded"}

        data = {
            'client_id': APPLE_AUTH_CLIENT_ID,
            'client_secret': client_secret,
            'code': access_token,
            'grant_type': 'authorization_code',
            'redirect_uri': APPLE_AUTH_API_REDIRECT_URL if is_native else
            APPLE_AUTH_REDIRECT_URL,
            'scope': 'name email',
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=data, headers=headers) as response:
                    res = await response.json()
                    if res.get("error", None):
                        raise AuthAppleOAuthError(res.get("error"))
                    if res.get("id_token", None):
                        decoded = jwt.decode(
                            res.get("id_token"), '',
                            options={
                                'verify_signature': False, "verify_aud": False,
                                "exp": True
                            },
                            access_token=res.get("access_token", ""),
                        )
                        user_info = schemas.OAuthAppleUserInfo(**decoded)
                        return user_info
                    else:
                        raise AuthAppleOAuthError("")

            # url = "https://appleid.apple.com/auth/revoke"
            # payload = {
            #     'client_id': APPLE_AUTH_CLIENT_ID,
            #     'client_secret': client_secret,
            #     'token': token,
            # }
            # async with aiohttp.ClientSession() as session:
            #     async with session.post(url, data=payload, headers=headers) as
            #     response:
            #         logging.error(f"**** APPLE REVOKE RES {response.status}")

        except aiohttp.ClientError as e:
            logging.error(f"Error during apple auth: {e}", exc_info=True)
            raise AuthAppleOAuthError("")

    async def login_by_google(
            self, data: schemas.OAuthLoginToken,
            lang: str, new_login: bool = False,
            new_login_func = None,
            profile_id: int | None = None,
    ) -> schemas.OAuthToken | schemas.NewOAuthToken:
        user_info: schemas.OAuthGoogleUserInfo = await self.process_google(
            data.access_token
        )
        return await self.verify_by_oauth_data(
            user_info.email, user_info.verified_email, is_auth_google=True,
            first_name=user_info.given_name, last_name=user_info.family_name,
            photo=user_info.picture, lang=lang, new_login=new_login,
            new_login_func=new_login_func, device_info=data.device_info,
            auth_source=data.auth_source or schemas.AuthSourceEnum.MY_7LOC,
            profile_id=profile_id,
        )

    async def login_by_apple(
            self, data: schemas.OAuthAppleLoginToken,
            lang: str, apple_state: str | None = None,
            new_login: bool = False, new_login_func = None,
            profile_id: int | None = None,
    ) -> schemas.OAuthToken | schemas.NewOAuthToken:
        user_info: schemas.OAuthAppleUserInfo = await self.process_apple(
            data.access_token, data.is_native
        )
        return await self.verify_by_oauth_data(
            user_info.email, user_info.email_verified, is_auth_apple=True,
            first_name=data.first_name, last_name=data.last_name,
            apple_state=apple_state,
            lang=lang, new_login=new_login, new_login_func=new_login_func,
            device_info=data.device_info,
            auth_source=data.auth_source or schemas.AuthSourceEnum.MY_7LOC,
            profile_id=profile_id,
        )

    async def verify_by_oauth_data(
            self, email: str, email_verified: bool,
            first_name: str | None = None,
            last_name: str | None = None,
            photo: str | None = None,
            is_auth_google: bool = False,
            is_auth_apple: bool = False,
            apple_state: str | None = None,
            lang: str = "en",
            new_login: bool = False,
            new_login_func = None,
            device_info: str | None = None,
            auth_source: schemas.AuthSourceEnum = schemas.AuthSourceEnum.MY_7LOC,
            profile_id: int | None = None,
    ) -> schemas.OAuthToken | schemas.NewOAuthToken:
        user = await User.get_by_email(email)
        is_accept_profile_agreement = None
        if profile_id and user:
            customer = await Customer.get(user_id=user.id, profile_id=profile_id)
            if not customer or not customer.is_accept_agreement:
                is_accept_profile_agreement = False

        if user:
            if not user.is_confirmed_email:
                await user.update(is_confirmed_email=True)

            if not new_login or not new_login_func:
                token = create_user_access_token(user.id, ACCESS_TOKEN_EXPIRES)
                return schemas.OAuthToken(
                    token=token,
                    token_type="bearer",
                    is_new_user=False,
                    email=email,
                    is_accept_profile_agreement=is_accept_profile_agreement,
                )
            new_login_data = await new_login_func(
                user, auth_source, device_info or "",
            )
            return schemas.NewOAuthToken(
                new_login_data=new_login_data,
                is_new_user=False,
                email=email,
                is_accept_profile_agreement=is_accept_profile_agreement,
            )

        if not email:
            raise AuthNotProvidedDataOAuthError()

        if is_auth_apple:
            oauth_type: schemas.Oauth2Type = "apple"
        elif is_auth_google:
            oauth_type: schemas.Oauth2Type = "google"
        else:
            raise AuthUnknownOAuthProviderError()

        register_data_schemas = schemas.OAuthRegisterData(
            first_name=first_name or "",
            last_name=last_name or "",
            lang=lang,
            email=email,
            email_verified=email_verified,
            photo=photo,
            oauth_type=oauth_type,
        )
        if apple_state:
            apple_state_schema = self.parse_apple_state(apple_state)
            register_data_schemas.host = apple_state_schema.host
            register_data_schemas.continue_url = apple_state_schema.continue_url

        token = create_oauth_access_token(
            register_data_schemas,
            timedelta(minutes=15),
        )
        return schemas.OAuthToken(
            is_new_user=True,
            first_name=first_name,
            last_name=last_name,
            email=email,
            token_type="bearer",
            token=token,
        )

    @classmethod
    def parse_apple_state(cls, state: str) -> schemas.AppleState:
        pattern = r'host:(.*?)_continue:(.*?)_service:(.*?)$'
        match = re.search(pattern, state)
        if match:
            host = match.group(1)
            continue_value = match.group(2)
            service = match.group(3)
            return schemas.AppleState(
                host=host,
                continue_url=continue_value or host,
                service=service,
            )
        return schemas.AppleState(
            host="/",
            continue_url="/",
            service="client",
        )

    @classmethod
    def get_user_data_by_oauth_token(
            cls, oauth_token: str
    ) -> schemas.OAuthRegisterData:
        exception = AuthNotAuthorisedByOAuthError()
        exp_exception = AuthTokenExpiredError()
        token_data, _ = parse_token(oauth_token, exception, exp_exception)
        if token_data and isinstance(token_data, dict):
            return schemas.OAuthRegisterData(**token_data)

        raise exception

    async def process_register_by_oauth_token(
            self, data: schemas.OAuthRegisterToken, lang: str,
            profile_id: int | None = None
    ):
        exception = AuthNotAuthorisedByOAuthError()
        user_data = self.get_user_data_by_oauth_token(data.oauth_token)
        if not user_data:
            raise exception

        user_exist = await User.get_by_email(user_data.email)
        if user_exist:
            token = create_user_access_token(user_exist.id, ACCESS_TOKEN_EXPIRES)
            return schemas.Token(
                token=token,
                token_type="bearer",
            )

        is_auth_google = False
        is_auth_apple = False
        if user_data.oauth_type == "google":
            is_auth_google = True
        elif user_data.oauth_type == "apple":
            is_auth_apple = True

        photo_url = None
        if user_data.photo:
            media = await media_manager.download_media(user_data.photo)
            if media and media.url:
                photo_url = media.url

        user = await crud.create_user(
            user_data.email,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            is_accepted_agreement=True,
            lang=lang,
            is_confirmed_email=user_data.email_verified,
            photo=photo_url,
            is_auth_google=is_auth_google,
            is_auth_apple=is_auth_apple,
        )
        if profile_id:
            await crud.create_customer(
                lang, user.id, profile_id, is_accept_agreement=True
            )

        return user

    async def register_by_oauth_token(
            self, data: schemas.OAuthRegisterToken, lang: str,
            profile_id: int | None = None
    ) -> schemas.Token:
        user = await self.process_register_by_oauth_token(
            data, lang, profile_id=profile_id
        )
        token = create_user_access_token(user.id, ACCESS_TOKEN_EXPIRES)
        return schemas.Token(
            token=token,
            token_type="bearer",
        )

    @classmethod
    async def customer_consent(
            cls, data: schemas.OAuthRegisterToken, lang: str,
            profile_id: int | None = None
    ) -> schemas.Token:
        exception = AuthNotAuthorisedByOAuthError()
        exp_exception = AuthTokenExpiredError()
        token_data, _ = parse_token(data.oauth_token, exception, exp_exception)

        token = None
        if isinstance(token_data, dict):
            user_id: int | None = token_data.get("sub", None)

            if user_id and profile_id:
                customer = await Customer.get(user_id=user_id, profile_id=profile_id)
                if customer:
                    await customer.update(is_accept_agreement=True)
                else:
                    await crud.create_customer(
                        lang, user_id, profile_id, is_accept_agreement=True
                    )

                token = create_user_access_token(user_id, ACCESS_TOKEN_EXPIRES)

        return schemas.Token(
            token=token,
            token_type="bearer",
        )

    @classmethod
    async def new_customer_consent(
            cls, data: schemas.NewOauthRegisterToken, lang: str,
            profile_id: int | None = None
    ):
        token_data = parse_access_token(data.oauth_token)

        user_id: int = token_data.get("sub")
        user = await User.get_by_id(user_id)
        if not user:
            raise AuthNotAuthorisedByOAuthError()

        customer = await Customer.get(user_id=user_id, profile_id=profile_id)
        if customer:
            await customer.update(is_accept_agreement=True)
        else:
            await crud.create_customer(
                lang, user_id, profile_id, is_accept_agreement=True
            )

        return await create_auth_session_and_get_tokens(
            user, data.auth_source, data.device_info
        )

    @classmethod
    def build_apple_callback_redirect(cls, redirect_url: str, query: str):
        if "?" not in redirect_url and not redirect_url.endswith("/"):
            redirect_url = f"{redirect_url}/"
            return f"{redirect_url}?{query}"
        if "?" in redirect_url:
            return f"{redirect_url}&{query}"
        return f"{redirect_url}?{query}"

    def process_apple_oauth_callback_new_user(
            self, apple_state: schemas.AppleState, token: str
    ) -> str:
        if not apple_state.host:
            raise AuthNotProvidedHostUrlError()
        if not apple_state.host.endswith("/"):
            apple_state.host = f"{apple_state.host}/"

        if apple_state.service == "admin":
            redirect_url = apple_state.continue_url
            redirect_url = self.build_apple_callback_redirect(
                redirect_url, f"oauth_token={quote(token)}"
            )
        else:
            redirect_url = f"{apple_state.host}oauth_consent?token={quote(token)}"

        return redirect_url

    async def process_apple_oauth_callback_exist_user(
            self, apple_state: schemas.AppleState,
            user: User, redirect_url: str
    ) -> str:
        if apple_state.service == "admin":
            token = create_user_access_token(user.id, ACCESS_TOKEN_EXPIRES)
            redirect_url = self.build_apple_callback_redirect(
                redirect_url, f"token={quote(token)}"
            )

            return redirect_url

        short_token = await ShortToken.create(
            user.id,
            user.allowed_scopes_str,
            None,
            await user.get_lang(),
        )
        if short_token:
            redirect_url = self.build_apple_callback_redirect(
                redirect_url, f"st={short_token.token}"
            )

        return redirect_url

    async def process_apple_oauth_callback(
            self, apple_data: schemas.AppleFormData, lang: str
    ):
        apple_state = self.parse_apple_state(apple_data.state)
        data = schemas.OAuthAppleLoginToken(
            access_token=apple_data.code,
            first_name=(
                apple_data.user.name.firstName
                if apple_data.user and apple_data.user.name
                else None
            ),
            last_name=(
                apple_data.user.name.lastName
                if apple_data.user and apple_data.user.name
                else None
            ),
            is_native=True,
        )
        processed_user_token_data = await self.login_by_apple(
            data, lang, apple_data.state
        )
        if processed_user_token_data.is_new_user:
            redirect_url = self.process_apple_oauth_callback_new_user(
                apple_state, processed_user_token_data.token
            )

            return RedirectResponse(url=redirect_url, status_code=302)

        user = await User.get_by_email(processed_user_token_data.email)
        if user:
            redirect_url = apple_state.continue_url
            redirect_url = await self.process_apple_oauth_callback_exist_user(
                apple_state, user, redirect_url
            )

            return RedirectResponse(url=redirect_url, status_code=302)

        raise AuthAppleCallbackError()
