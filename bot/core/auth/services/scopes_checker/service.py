from dataclasses import asdict, make_dataclass
from fastapi import Depends, Path, Security
from typing import Any, Sequence

from core.api.depends import get_lang
from core.auth.depend import Action, get_active_user
from db.models import User
from .base import BaseScopesCheckerService


class ScopesCheckerService(BaseScopesCheckerService):
    def __init__(
            self,
            action: str | None,
            user: User,
            lang: str,
            data: Any = None,  # dynamically created dataclass
    ):
        super().__init__(action, user)
        self.lang: str = lang
        self.data: Any = data

    def get_available_data(self):
        return asdict(self.data) if self.data else None

    @staticmethod
    def get_params_dataclass(*path_fields: str, ):
        return make_dataclass(
            "Params", [
                (field, int, Path())
                for field in path_fields
            ]
        )

    @classmethod
    def depend(
            cls,
            default_action: str | None = None,
            *path_fields: str,
            scopes: Sequence[str] | None = None,
            use_cache: bool = True,
    ):
        if path_fields:
            data_cls = cls.get_params_dataclass(*path_fields)

            # noinspection PyShadowingNames
            async def depend_func(
                    action: str | None = Action(default_action),
                    user: User = Depends(get_active_user),
                    data: data_cls = Depends(),
                    lang: str = Depends(get_lang),
            ):

                return await cls(action, user, lang, data).process()
        else:
            # noinspection PyShadowingNames
            async def depend_func(
                    action: str | None = Action(default_action),
                    user: User = Depends(get_active_user),
                    lang: str = Depends(get_lang),
            ):
                return await cls(action, user, lang).process()

        return Security(depend_func, scopes=scopes, use_cache=use_cache)
