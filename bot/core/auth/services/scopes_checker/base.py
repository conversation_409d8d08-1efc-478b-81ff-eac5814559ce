from abc import abstractmethod

from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from db import crud
from db.models import User


class BaseScopesCheckerService:

    def __init__(self, action: str | None, user: User):
        self.action: str | None = action
        self.user: User = user

    @classmethod
    @abstractmethod
    def depend(cls, *args, **kwargs):
        raise NotImplementedError

    @abstractmethod
    def get_available_data(self):
        raise NotImplementedError

    async def process(self):
        available_data = self.get_available_data()
        if self.action and not await crud.check_access_to_action(
                self.action,
                "user", self.user.id,
                available_data,
        ):
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                self.action,
                available_data,
                available_fields=available_data.keys() if available_data else [],
            )
        return self
