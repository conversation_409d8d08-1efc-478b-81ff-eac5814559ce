from fastapi import Depends
from sqlalchemy.orm import Session

from core.api.depends import get_db, get_lang
from core.store.depends import get_current_bot_with_brand, get_current_brand
from db.models import Brand, ClientBot


class BaseAuthService:
    def __init__(
            self,
            db: Session = Depends(get_db),
            lang: str = Depends(get_lang),
            brand: Brand | None = Depends(get_current_brand),
            bot: ClientBot | str | None = Depends(get_current_bot_with_brand),
    ):
        assert isinstance(db, Session)
        assert isinstance(lang, str)
        assert isinstance(brand, Brand | None)
        assert isinstance(bot, ClientBot | str | None)

        self.db: Session = db
        self.lang: str = lang
        self.brand: Brand | None = brand
        self.bot: ClientBot | str | None = bot

    @property
    def safe_bot_id(self) -> int | None:
        return self.bot.id if isinstance(self.bot, ClientBot) else None

    @property
    def safe_brand_id(self) -> int | None:
        return self.brand.id if self.brand else None
