import json
import urllib.parse
from aiogram.utils.web_app import safe_parse_webapp_init_data
from datetime import datetime, timedelta
from dateutil import parser
from fastapi import Response
from psutils.exceptions import ErrorWithTextVariable
from typing import Literal

import schemas
from api.functions import get_bot_token
from config import ACCESS_TOKEN_EXPIRES, WEB_APP_DATA_LIFE_TIME
from core.auth.functions import (
    create_auth_session_and_get_tokens, create_auth_tokens_for_session,
    create_user_access_token,
    make_confirm_email_html, validate_email, validate_is_accepted_agreement,
    validate_password, validate_telegram_data_and_chat_id,
)
from core.auth.services.base import BaseAuthService
from core.auth.services.oauth2_service import OAuth2Service
from core.gmail import gmail_client
from db import crud
from db.models import (
    AuthSession, ClientBot, ConfirmEmailRequest, Customer, ShortToken, User,
)
from exceptions import (
    AuthAccountNotFoundError, AuthConfirmEmailInvalidTokenError, AuthEmailExistsError,
    AuthHeartBeatFutureTimeError, AuthIncorrectPasswordError, AuthInvalidInputDataError,
    AuthNotAuthorisedError, AuthSendingEmailError, AuthTelegramNoBotError,
    AuthUnknownMethodError,
)
from loggers import JSONLogger
from schemas import TelegramData
from utils.date_time import utcnow
from utils.text import f


class AuthService(BaseAuthService, OAuth2Service):

    @classmethod
    async def check_is_email_exists(cls, email: str, profile_id: int | None):
        user = await User.get_by_email(email)
        if not user:
            return schemas.IsEmailExists(
                email=email,
                is_exists=False
            )

        is_accept_agreement = user.is_accepted_agreement
        if profile_id:
            customer = await Customer.get(user_id=user.id, profile_id=profile_id)
            if not customer:
                is_accept_agreement = False
                await crud.create_customer(user.lang, user.id, profile_id)
            else:
                is_accept_agreement = customer.is_accept_agreement

        return schemas.IsEmailExists(
            email=email,
            is_exists=True,
            is_guest_user=user.is_guest_user,
            is_accepted_agreement=bool(is_accept_agreement),
        )

    async def check_is_chat_id_exists(self, chat_id: int):
        user = await crud.check_exists_chat_id(self.db, chat_id)
        is_exists = False
        is_activated = False
        is_agreement_confirmed = False
        if user:
            is_exists = True
            is_activated = user.status == "active"
            is_agreement_confirmed = bool(user.is_accepted_agreement)

        return schemas.IsChatIDExists(
            chat_id=chat_id,
            is_exists=is_exists,
            is_activated=is_activated,
            is_agreement_confirmed=is_agreement_confirmed,
        )

    async def send_confirm_email(
            self, email: str,
            purpose: schemas.ConfirmEmailPurposeLiteral,
            user_id: int | None = None,
            is_external_user: bool = False,
    ) -> ConfirmEmailRequest:
        logger = JSONLogger(
            "auth", "send_confirm_email", {
                "email": email,
                "purpose": purpose,
                "user_id": user_id,
                "is_external_user": is_external_user,
            }
        )

        await validate_email(
            self.db, email,
            validate_confirm=False,
            validate_exists=False,
        )

        try:
            request = await crud.create_confirm_email_request(self.db, email, purpose)
            logger.add_data(
                {
                    "request": request
                }
            )

            if self.safe_brand_id:
                calculated_name = self.brand.name
            elif isinstance(self.bot, ClientBot):
                calculated_name = self.bot.display_name
            else:
                calculated_name = await f("7loc default email name", self.lang)

            title = await f(
                f"confirm email for {purpose} subject", self.lang,
                name=calculated_name,
            )
            html = await make_confirm_email_html(
                self.brand, request, self.lang, user_id, is_external_user
            )

            resp = await gmail_client.send_email(
                email, title,
                "", html=html,
            )
            logger.add_data(
                {
                    "resp": resp
                }
            )

            await crud.confirm_email_request_sent(self.db, request)
        except Exception as e:
            logger.error(e)
            if isinstance(e, ErrorWithTextVariable):
                raise
            raise AuthSendingEmailError(email) from e
        else:
            logger.debug("Successfully sent!")
            return request

    async def confirm_email(self, token_data: dict):
        request = await crud.confirm_email(self.db, token_data.get("request_id"))
        if not request:
            raise AuthConfirmEmailInvalidTokenError()
        return request

    async def check_is_email_confirmed(self, token_data: dict):
        request_id = token_data.get("request_id")
        request = await crud.get_confirm_email_request(self.db, request_id)

        if not request or not request.is_confirmed:
            is_confirmed = False
        else:
            is_confirmed = True

        email = request.email if request else None

        return schemas.IsEmailConfirmedData(
            email=email,
            is_confirmed=is_confirmed,
        )

    @staticmethod
    def validate_method(data: schemas.LoginData | schemas.RegisterData):
        if isinstance(data, schemas.LoginData):
            method = data.login_method
        else:
            method = data.register_method

        for el in schemas.AuthorisationMethodEnum:
            if (el == method) is not bool(getattr(data, f"{el.value}_data")):
                raise AuthInvalidInputDataError()

    async def process_login_data(self, data: schemas.LoginData):
        self.validate_method(data)

        match data.login_method:
            case schemas.AuthorisationMethodEnum.EMAIL:
                user = await User.get_by_email(data.email_data.email)
                if not user:
                    raise AuthAccountNotFoundError()

                if not user.verify_password(data.email_data.password):
                    raise AuthIncorrectPasswordError()

            case schemas.AuthorisationMethodEnum.TELEGRAM:
                user = await User.get(data.telegram_data.id)
                if not user:
                    raise AuthAccountNotFoundError()
            case _:
                raise AuthUnknownMethodError(data.login_method.value)

        if data.need_activate and user.status != "active":
            await user.set_status("active")

        if data.need_set_agreement_confirm and not user.is_accepted_agreement:
            await user.set_agreement(True)

        return user

    async def login(self, data: schemas.LoginData):
        user = await self.process_login_data(data)
        customer = await Customer.get(
            user_id=user.id, profile_id=self.brand.group_id if self.brand else None
        )
        if customer:
            await customer.update(is_accept_agreement=True)
        else:
            if self.brand:
                await crud.create_customer(
                    self.lang, user.id, self.brand.group_id, is_accept_agreement=True
                )

        token = create_user_access_token(user.id, ACCESS_TOKEN_EXPIRES)
        return schemas.Token(
            token=token,
            token_type="bearer",
        )

    async def new_login(self, data: schemas.NewLoginData):
        user = await self.process_login_data(data)
        return await create_auth_session_and_get_tokens(
            user, data.auth_source, data.device_info
        )

    @classmethod
    async def logout(cls, auth_session: AuthSession):
        await auth_session.delete()
        return schemas.OkResponse()

    async def refresh_token(self, auth_session: AuthSession):
        await auth_session.update(
            last_refresh_datetime=utcnow(),
            expire_datetime=auth_session.make_expire_datetime(),
        )
        return create_auth_tokens_for_session(auth_session)

    @classmethod
    async def set_push_token(cls, push_token: str, auth_session: AuthSession):
        await crud.set_auth_session_push_token(auth_session, push_token)
        return schemas.OkResponse()

    @staticmethod
    async def login_by_short_token(
            data: schemas.LoginByShortTokenData
    ) -> schemas.LoginByShortTokenResult:
        short_token_obj, auth_allowed = await ShortToken.use(data.short_token)
        user = await User.get_by_id(short_token_obj.user_id)

        new_login_data = None
        token_data = None

        if auth_allowed:
            if data.auth_source and data.device_info:
                new_login_data = await create_auth_session_and_get_tokens(
                    user, data.auth_source, data.device_info,
                )
            else:
                access_token = create_user_access_token(
                    user.id, ACCESS_TOKEN_EXPIRES,
                    scopes=short_token_obj.scopes.split(",")
                )
                token_data = schemas.Token(
                    token=access_token,
                    token_type="bearer"
                )

        return schemas.LoginByShortTokenResult(
            token_data=token_data,
            new_login_data=new_login_data,
            bot_id=short_token_obj.bot_id,
            lang=short_token_obj.lang,
            url_path=short_token_obj.url_path,
            params=short_token_obj.params,
        )

    async def process_register_data(self, data: schemas.RegisterData):
        self.validate_method(data)

        validate_is_accepted_agreement(data.is_accept_agreement)

        match data.register_method:
            case schemas.AuthorisationMethodEnum.EMAIL:
                user, confirm_email_request = await self.register_by_email(
                    data.email_data.email,
                    data.email_data.password,
                    data.is_accept_agreement,
                    data.email_data.first_name,
                    data.email_data.last_name,
                    data.email_data.birth_date,
                )
            case schemas.AuthorisationMethodEnum.TELEGRAM:
                user = await self.register_by_telegram(
                    data.telegram_data,
                    data.is_accept_agreement,
                )
            case _:
                raise AuthUnknownMethodError(data.register_method.value)

        return user

    async def register(self, data: schemas.RegisterData):
        user = await self.process_register_data(data)

        token = create_user_access_token(user.id, ACCESS_TOKEN_EXPIRES)

        return schemas.Token(
            token=token,
            token_type="bearer",
        )

    async def new_register(self, data: schemas.NewRegisterData):
        user = await self.process_register_data(data)
        return await create_auth_session_and_get_tokens(
            user, data.auth_source, data.device_info
        )

    async def register_by_email(
            self,
            email: str, password: str,
            is_accepted_agreement: bool,
            first_name: str | None = None,
            last_name: str | None = None,
            birth_date: str | None = None,
            chat_id: int | None = None,  # for external login
            username: str | None = None,
    ) -> tuple[User, ConfirmEmailRequest]:
        validate_is_accepted_agreement(is_accepted_agreement)

        user: User | None = await User.get_by_email(email)
        if user and user.is_confirmed_email:
            raise AuthEmailExistsError(email)

        validate_password(password)

        confirm_email_request = await self.send_confirm_email(email, "register")

        if birth_date:
            try:
                birth_date = parser.isoparse(birth_date).date()
            except ValueError:
                JSONLogger(
                    "auth", "register_by_email", {
                        "birth_date": birth_date,
                    }
                ).error("Invalid date format. Expected ISO format")
                birth_date = None
        else:
            birth_date = None

        if user:
            user = await crud.update_user(
                self.db, user, schemas.UpdateUser(
                    first_name=first_name,
                    last_name=last_name,
                    password=password,
                    chat_id=chat_id,
                    username=username,
                    birth_date=birth_date,
                )
            )
        else:
            user = await crud.create_user(
                email, password,
                first_name, last_name,
                birth_date=birth_date,
                chat_id=chat_id,
                username=username,
                is_accepted_agreement=is_accepted_agreement,
                lang=self.lang,
            )

        return user, confirm_email_request

    async def register_by_telegram(
            self,
            telegram_data: TelegramData,
            is_accepted_agreement: bool,
    ):
        validate_is_accepted_agreement(is_accepted_agreement)

        if self.bot:
            bot = self.bot
        elif self.safe_brand_id:
            bot = await crud.get_bot_by_brand(self.safe_brand_id)
        else:
            bot = None

        if not bot:
            raise AuthTelegramNoBotError()

        await validate_telegram_data_and_chat_id(self.db, telegram_data, bot)

        return await crud.create_user(
            telegram_data=telegram_data,
            is_accepted_agreement=is_accepted_agreement,
            lang=self.lang
        )

    async def reset_user_password(self, data: schemas.ResetPasswordData):
        """
        function located here, not in AuthUserService because token requires in
        AuthUserService user
        """
        user = await User.get_by_email(data.current_email)
        if not user:
            raise AuthAccountNotFoundError()

        await validate_email(self.db, data.current_email, "reset_password", False)

        validate_password(data.new_password)

        await crud.update_user_email_or_password(
            self.db, user, "reset_password",
            password=data.new_password,
        )

        return Response(status_code=200)

    async def change_user_email(
            self,
            data: schemas.ChangeEmailData,
            user_id: int | None = None,
            request_id: int | None = None,
    ):
        user = await User.get_by_id(user_id)
        if not user:
            raise AuthAccountNotFoundError()

        if user.email and (user.hashed_password or data.password) and \
                not user.verify_password(data.password):
            raise AuthIncorrectPasswordError()

        if user.hashed_password and data.password:
            if not user.verify_password(data.password):
                raise AuthIncorrectPasswordError()

        purpose: Literal[
            "register", "set_email", "reset_password", "change_email",
            "set_email_admin"] = "change_email"
        if request_id:
            request = await crud.get_confirm_email_request(self.db, request_id)
            if request:
                purpose = request.purpose

        await validate_email(self.db, data.new_email, purpose, validate_confirm=False)
        if data.is_admin and not user.hashed_password:
            validate_password(data.password)
            await crud.update_user_email_or_password(
                self.db, user,
                "change_email",
                data.new_email,
                password=data.password,
            )
        else:
            await crud.update_user_email_or_password(
                self.db, user,
                "change_email",
                data.new_email,
            )

        await crud.confirm_user_email(self.db, user)

        return Response(status_code=200)

    @classmethod
    async def login_by_tg_web_data(
            cls, token: str | None, bot_id: int | Literal["service", "admin"] | None
    ) -> User:
        if not token or not bot_id:
            raise AuthNotAuthorisedError()

        logger = JSONLogger(
            "auth", "login_by_tg_web_data", {
                "token": token,
                "bot_id": bot_id,
            }
        )

        try:
            auth_data = urllib.parse.unquote(token)
            bot_token = await get_bot_token(bot_id)
            decoded_init_data = safe_parse_webapp_init_data(
                bot_token, auth_data, json.loads
            )
            web_app_data = schemas.WebAppData(**decoded_init_data)

            now = utcnow()
            if web_app_data.auth_date.replace(
                    tzinfo=now.tzinfo
            ) + WEB_APP_DATA_LIFE_TIME < now:
                web_app_data = None
        except Exception as e:
            logger.error(e)
            web_app_data = None

        if not web_app_data:
            raise AuthNotAuthorisedError()

        user = await User.get(web_app_data.user.id)
        if not user:
            raise AuthAccountNotFoundError()

        return user

    @classmethod
    async def login_by_tg_web_data_new(cls, data: schemas.LoginByWebViewNewData):
        user = await cls.login_by_tg_web_data(data.web_view_token, data.bot_id)
        return await create_auth_session_and_get_tokens(
            user, data.auth_source, data.device_info
        )

    async def new_register_by_oauth_token(
            self, data: schemas.NewOauthRegisterToken, lang: str,
            profile_id: int | None = None
    ) -> schemas.AuthorisedResponse:
        user = await self.process_register_by_oauth_token(
            data, lang, profile_id=profile_id
        )
        return await create_auth_session_and_get_tokens(
            user, data.auth_source, data.device_info
        )

    @classmethod
    async def heartbeat(
            cls, activity_datetime: datetime, auth_session: AuthSession
    ) -> schemas.OkResponse:

        if activity_datetime.tzinfo is not None:
            activity_datetime = activity_datetime.replace(tzinfo=None)

        now = utcnow()
        now_plus = now + timedelta(seconds=15)
        if activity_datetime > now_plus:
            raise AuthHeartBeatFutureTimeError()
        if (auth_session.last_activity and activity_datetime <
                auth_session.last_activity):
            return schemas.OkResponse()
        await auth_session.update(last_activity=activity_datetime)

        return schemas.OkResponse()
