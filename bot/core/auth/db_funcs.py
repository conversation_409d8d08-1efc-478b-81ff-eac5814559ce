from passlib.handlers.pbkdf2 import pbkdf2_sha256
from sqlalchemy.orm import Session

from db import db_func
from db import models
from schemas.auth import BaseUpdateUser


@db_func
def check_is_confirmed_email(db: Session, email: str, purpose: str) -> bool:
    query = db.query(models.ConfirmEmailRequest).filter(models.ConfirmEmailRequest.email == email)
    query = query.filter(models.ConfirmEmailRequest.is_confirmed.is_(True))
    query = query.filter(models.ConfirmEmailRequest.purpose == purpose)

    query = db.query(query.exists())
    return query.scalar()


@db_func
def check_exists_email(db: Session, email: str) -> bool:
    query = db.query(models.User).filter(models.User.email == email)
    query = db.query(query.exists())
    return query.scalar()


async def check_exists_chat_id(db: Session, chat_id: int) -> bool:
    query = db.query(models.User).filter(models.User.chat_id == chat_id)
    query = db.query(query.exists())
    return query.scalar()


@db_func
def check_exists_chat_id(db: Session, chat_id: int) -> models.User | None:
    query = db.query(models.User).filter(models.User.chat_id == chat_id)
    # query = db.query(query.exists())
    return query.one()


async def get_user(
        db: Session,
        user_id: int = None,
        email: str = None,
        chat_id: int = None
) -> models.User:
    if not any((user_id, email, chat_id)):
        raise ValueError("one of user_id, email, chat_id must be specified")

    query = db.query(models.User)

    if user_id:
        query = query.filter(models.User.id == user_id)

    if email:
        query = query.filter(models.User.email == email)

    if chat_id:
        query = query.filter(models.User.chat_id == chat_id)

    return query.one()


async def update_user(db: Session, user: models.User, new_data: BaseUpdateUser) -> models.User:
    data = new_data.dict(exclude_unset=True)

    password = data.pop("password", None)

    if password:
        data["hashed_password"] = pbkdf2_sha256.hash(password)

    for field, value in data.items():
        if not hasattr(user, field):
            continue

        if getattr(user, field) != value:
            setattr(user, field, value)

    db.commit()

    return user


__all__ = [
    "check_exists_email",
    "check_exists_chat_id",
    "check_is_tg_connected",
    "create_user",
    "get_user",
    "update_user",
    "check_is_confirmed_email",
]
