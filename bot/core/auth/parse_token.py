import logging
from typing import Any
from jose import jwt, JWTError, ExpiredSignatureError
from pydantic import ValidationError

from config import (
    SECRET_KEY, ALGORITHM,
)


def parse_token(
    token: str,
    credentials_exception: Exception | None = None,
    expired_exception: Exception | None = None,
) -> tuple[dict[str, Any] | None, list[str] | None]:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        token_scopes = payload.get("scopes")
        return payload, token_scopes
    except (JWTError, ValidationError) as e:
        if isinstance(e, ExpiredSignatureError) and expired_exception:
            raise expired_exception from e

        if credentials_exception:
            raise credentials_exception from e
        else:
            logging.error(e, exc_info=True)

        return None, None
