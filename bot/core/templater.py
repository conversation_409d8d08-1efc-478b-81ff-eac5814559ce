from collections.abc import Callable
from typing import Generic

import htmlmin
from starlette.templating import Jinja2Templates

from db.models import ColorSchema
from schemas import BaseTemplateSchema, TemplaterColorSchema
from utils.color import get_contrast_text_color
from utils.type_vars import P, RT


class JinjaFilter(Generic[P, RT]):
    filters: dict[str, Callable] = {}

    def __init__(self, func: Callable[P, RT]):
        self.func: Callable[P, RT] = func
        self.filters[func.__name__] = func

    def __call__(self, *args: P.args, **kwargs: P.kwargs) -> RT:
        return self.func(*args, **kwargs)


@JinjaFilter
def nl2br(value: str):
    return value.replace("\n", "<br>")


class Templater:

    def __init__(
            self,
            directory: str = "templates",
    ):
        self.templates = Jinja2Templates(directory)
        self.templates.env.filters.update(JinjaFilter.filters)

    @classmethod
    async def get_color_schema(cls, group_id: int | None):
        if group_id:
            color_schema_db = await ColorSchema.get(group_id=group_id) if group_id else None

            if color_schema_db and color_schema_db.primary_color:
                primary_color = color_schema_db.primary_color
                primary_contrast_text = get_contrast_text_color(primary_color)
            else:
                primary_color = '#1976d2'
                primary_contrast_text = '#ffffff'

            if color_schema_db and color_schema_db.warning_color:
                warning_color = color_schema_db.warning_color
                warning_contrast_text = get_contrast_text_color(warning_color)
            else:
                warning_color = '#ed6c02'
                warning_contrast_text = '#ffffff'
        else:
            primary_color = '#BD4448'
            primary_contrast_text = '#ffffff'
            warning_color = '#ffe57f'
            warning_contrast_text = '#000000'

        return TemplaterColorSchema(
            primary_color=primary_color,
            primary_contrast_text=primary_contrast_text,
            warning_color=warning_color,
            warning_contrast_text=warning_contrast_text,
        )

    async def make_template(
            self,
            template: BaseTemplateSchema,
            group_id: int | None = None,
            template_path_override: str | None = None,
            br2nl: bool = False,
            minify: bool = False,
            **kwargs,
    ):
        color_schema = await self.get_color_schema(group_id)

        template_path = template_path_override or template.TEMPLATE_PATH
        email_template = self.templates.get_template(template_path)
        data = dict(template.dict(), **kwargs)
        html = email_template.render(
            data=data,
            color_schema=color_schema,
            content_type="text/html",
        )
        if minify:
            html = htmlmin.minify(
                html,
                remove_comments=True,
                remove_all_empty_space=True,
                remove_optional_attribute_quotes=False,
                reduce_empty_attributes=False,
                convert_charrefs=False,
            )
        if br2nl:
            html = html.replace("<br/>", "\n")
            html = html.replace("<br>", "\n")
        return html


templater = Templater()
