from typing import Iterable, Literal

from sqlalchemy.engine import Row

from db import crud
from db.models import ClientWebPage, Group
from schemas import ClientWebPageTypeEnum
from utils.text import fd

DEFAULT_SLUGS = {
    ClientWebPageTypeEnum.MENU: "menu",
    ClientWebPageTypeEnum.STORES: "select",
    ClientWebPageTypeEnum.FASTPAY: "/"
}


async def check_and_create_default_pages(
        pages: list[ClientWebPage | Row],
        group: Group,
        types: (
                Iterable[ClientWebPageTypeEnum] |
                ClientWebPageTypeEnum |
                Literal["*"]
        ) = "*"
):
    if types == "*":
        types = [
            type_ for type_ in ClientWebPageTypeEnum
            if type_ != ClientWebPageTypeEnum.CUSTOM
        ]
    elif isinstance(types, ClientWebPageTypeEnum):
        types = [types]

    existing_pages_types = list(map(lambda x: x.type, pages))
    types_to_create = [el for el in types if el not in existing_pages_types]

    if not types_to_create:
        return

    titles = await fd(
        {
            type_.value: f"client web page {type_.value} default title"
            for type_ in types_to_create
        },
        group.lang,
    )

    return await crud.create_default_pages(group, types_to_create, titles)
