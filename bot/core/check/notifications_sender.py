import core.messangers_adapters as ma
from core.check.keyboards import (
    get_leave_review_keyboard_telegram, get_leave_review_keyboard_whatsapp,
    get_order_keyboard_telegram, get_referral_program_keyboard_telegram,
    get_referral_program_keyboard_whatsapp,
)
from db.models import (
    Brand, ClientBot, Group, Invoice, MenuInStore, OrderShippingStatus, ShortToken,
    Store, StoreOrder,
    User,
)
from utils.email_funcs import is_valid_email
from utils.text import f
from .texts import CheckTemplate, build_check_template
from ..external_coupon import send_coupon_info
from ..notifications.base_notifications_sender import BaseNotificationsSender, TemplateT
from ..notifications.funcs import put_message_to_kafka
from ..templater import templater


class CheckNotificationsSender(BaseNotificationsSender[CheckTemplate]):
    BOT_TEMPLATE_PATH = "notification_with_check_messanger.html"

    def __init__(
            self,
            lang: str,
            brand: Brand,
            group: Group,
            user: User,
            bot: ClientBot | None = None,
            store: Store | None = None,
            order: StoreOrder | None = None,
            order_status: OrderShippingStatus | None = None,
            invoice: Invoice | None = None,
            menu_in_store: MenuInStore | None = None,
            need_send_coupons_to_bot: bool = False
    ):

        super().__init__(
            lang, brand, group, user, bot, store,
        )

        self.order: StoreOrder | None = order
        self.order_status: OrderShippingStatus | None = order_status
        self.invoice: Invoice | None = invoice
        self.menu_in_store: MenuInStore | None = menu_in_store
        self.need_send_coupons_to_bot: bool = need_send_coupons_to_bot
        self.store_id: int | None = store.id if store else None

        if self.order.email:
            user_email = self.order.email
        elif self.user.email and self.user.is_confirmed_email:
            user_email = self.user.email
        else:
            user_email = None
            self.logger.debug(f'{self.user.full_name=} not have any email')

        if user_email and not is_valid_email(user_email):
            self.logger.error(f'{user_email=} is not valid ')
            user_email = None
        self.user_email: str | None = user_email

    async def get_user_email(self):
        return self.user_email

    async def build_template(self) -> TemplateT:
        return await build_check_template(
            self.lang,
            self.order,
            self.order_status,
            self.invoice,
            self.store,
            self.brand,
            self.user,
            self.group,
            self.bot,
            logger=self.logger,
        )

    def select_sender_bot(self, user: User) -> tuple[ClientBot, str | int]:
        # TODO  add select next available bot
        return self.bot, ma.get_user_to(user, self.bot.bot_type)

    async def try_send_to_friend(self, template: CheckTemplate):
        self.logger.debug('try_send_to_friend ->')
        try:
            if not self.invoice:
                self.invoice = await Invoice.get(self.order.invoice_id)

            if self.invoice and (
                    not self.invoice.is_friend or not self.invoice.payer_id):
                return False

            payer: User = await User.get_by_id(self.invoice.payer_id)
            bot_to_send, user_to = self.select_sender_bot(payer)
            if not user_to:
                err_msg = f"Not find user_to for {payer.id=}"
                self.logger.error(err_msg)
                raise Exception(err_msg)

            self.logger.debug(
                f"{self.invoice=}, {payer.id=}, {user_to=}, "
                f"{bot_to_send.id=}, {bot_to_send.bot_type=}"
            )

            text = await templater.make_template(
                template, self.group.id,
                "notification_to_friend_with_check_messanger.html",
                br2nl=True, minify=True
            )

            await put_message_to_kafka(
                bot_to_send, payer,
                text,
                "text", None,
                logger=self.logger
            )
            self.logger.debug(
                f"try_send_to_friend Ok, {self.invoice=}, {payer.id=}, {user_to=}, "
                f"{bot_to_send.id=}, {bot_to_send.bot_type=}"
            )
        except Exception as e:
            self.logger.error(e, exc_info=True)

    async def after_send_message(self, template: TemplateT) -> bool:
        if not self.bot:
            return False
        self.logger.debug("after_send_message ->")
        is_something_sent = False
        need_review_proposal = (self.order.status == "closed" and
                                template.review_proposal_text)

        match self.bot.bot_type:
            case "telegram":
                if self.need_send_coupons_to_bot:
                    await self.send_coupons_to_bot(template)

                if (template.referral_program_title and self.user.chat_id and
                        self.order.status in ("closed", "payed")):
                    keyboard = await get_referral_program_keyboard_telegram(template)

                    await put_message_to_kafka(
                        self.bot, self.user,
                        template.referral_program_title,
                        "text", keyboard, logger=self.logger
                    )

                if need_review_proposal:
                    keyboard = await get_leave_review_keyboard_telegram(template)

                    await put_message_to_kafka(
                        self.bot, self.user,
                        template.review_proposal_text, "text", keyboard,
                        logger=self.logger
                    )
            case "whatsapp":
                if self.need_send_coupons_to_bot:
                    sent_coupons_count = await self.send_coupons_to_bot(template)
                    if sent_coupons_count:
                        is_something_sent = True

                    if template.coupons and len(template.coupons) > sent_coupons_count:
                        not_sent_coupons_count = len(
                            template.coupons
                        ) - sent_coupons_count
                        self.logger.debug(
                            f"an error occurred while sending coupons "
                            f"({not_sent_coupons_count}) to user"
                        )

                        try:
                            await put_message_to_kafka(
                                self.bot, self.user,
                                await f(
                                    "bot not sent coupons error text", self.lang,
                                    len_coupons=len(template.coupons),
                                    not_sent_coupons_count=not_sent_coupons_count,
                                ),
                                "text", None, logger=self.logger
                            )
                            self.logger.debug("user NOTIFIED about coupons error")
                        except Exception as e:
                            self.logger.debug("user NOT NOTIFIED about coupons error")
                            self.logger.error(e, exc_info=True)

                try:
                    if (template.referral_program_title and
                            self.order.status in ("closed", "payed")):
                        short_token = await ShortToken.create(
                            user_id=self.user.id,
                            scopes=self.user.allowed_scopes_str,
                            bot_id=self.bot.id if self.bot else None,
                        )

                        referral_keyboard = await (
                            get_referral_program_keyboard_whatsapp(
                                template, short_token
                            ))

                        await put_message_to_kafka(
                            self.bot, self.user,
                            template.referral_program_title,
                            "text", referral_keyboard, logger=self.logger
                        )
                        is_something_sent = True
                except Exception as e:
                    self.logger.error(e, exc_info=True)

                try:
                    if need_review_proposal:
                        short_token = await ShortToken.create(
                            user_id=self.user.id,
                            scopes=self.user.allowed_scopes_str,
                            bot_id=self.bot.id,
                        )

                        review_keyboard = await get_leave_review_keyboard_whatsapp(
                            template, short_token
                        )

                        await put_message_to_kafka(
                            self.bot, self.user,
                            template.review_proposal_text, "text",
                            review_keyboard, logger=self.logger
                        )

                        is_something_sent = True
                except Exception as e:
                    self.logger.error(e, exc_info=True)

        if (self.order and self.order.payment_method == 'friend' and
                self.order.status_pay == 'payed'):
            _ = await self.try_send_to_friend(template)

        return is_something_sent

    async def get_wa_send_keyboard_separately(self, template: TemplateT):
        self.logger.debug("get_wa_send_keyboard_separately ->")
        need_review_proposal = (self.order.status == "closed" and
                                template.review_proposal_text)

        return (
                (
                        template.coupons and
                        self.need_send_coupons_to_bot
                ) or
                (
                        need_review_proposal and
                        template.referral_program_title
                )
        )

    async def get_keyboard(self, template: TemplateT) -> ma.Keyboard:
        if not self.bot:
            return None
        self.logger.debug("get_keyboard ->")
        if self.bot.bot_type == "whatsapp":
            return await super().get_keyboard(template)
        if not self.order:
            return None
        return await get_order_keyboard_telegram(
            self.brand,
            self.store.id,
            self.order.id,
            self.lang,
        )

    async def send_coupons_to_bot(self, template: CheckTemplate):
        self.logger.debug("send_coupons_to_bot ->")
        if not template.coupons or not self.need_send_coupons_to_bot:
            self.logger.debug(
                f"not coupons sent\n{template.coupons=} or not "
                f"{self.need_send_coupons_to_bot=}"
            )
            return 0

        sent_coupons_count = 0

        for coupon in template.coupons:
            try:
                await send_coupon_info(
                    coupon,
                    self.user,
                    self.bot,
                    self.lang,
                    self.brand.id,
                    store_id=self.store_id,
                )
                sent_coupons_count += 1
            except Exception as e:
                self.logger.error(e, exc_info=True)

        return sent_coupons_count

    async def get_email_attachments(
            self, template: TemplateT
    ):
        self.logger.debug("get_email_attachments ->")
        return [
            coupon.pdf for
            coupon in template.coupons or []
            if coupon.pdf
        ]
