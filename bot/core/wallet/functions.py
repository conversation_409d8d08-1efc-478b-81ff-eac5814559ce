import logging

from incust_api.api import client

import schemas
from core.loyalty.customer_service import get_or_create_incust_customer
from core.loyalty.incust_api import incust
from core.messangers_adapters.types import (
    AnswerObject, InlineKeyboard,
    UrlKeyboardButton,
)
from core.payment.incust_pay.service import IncustPayService
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db import crud
from db.models import Brand, ClientBot, User
from utils.text import f


async def wallet_show(
        user: User,
        brand: Brand,
        lang: str,
        answer_obj: AnswerObject,
        bot: ClientBot,
) -> AnswerObject:
    try:
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(brand_id=brand.id)
        )

        if not loyalty_settings:
            return await answer_obj.answer(
                await f("loyalty not connected text", lang, brand_name=brand.name)
            )

        incust_customer = await get_or_create_incust_customer(user, loyalty_settings)

        if not incust_customer:
            return await answer_obj.answer(
                await f('client bot nodata incust text', lang)
            )

        incust_pay_service = IncustPayService(brand, user, lang)

        try:
            async with incust.client.WalletApi(loyalty_settings, user=user, lang=lang) as api:
                wallets = await api.wallet(group_by_business=False)
        except client.ApiException as ex:
            logging.error(f"Error getting wallet: {ex.reason}")
            return await answer_obj.answer(
                await f('client bot error getting wallet text', lang)
            )
        except Exception as ex:
            logging.error(f"Unexpected error getting wallet: {ex}")
            return await answer_obj.answer(
                await f('client bot error getting wallet text', lang)
            )
        incust_pay = await incust_pay_service.get_incust_pay_payment_data(
            none_on_empty=True
        )
        wallet = wallets[0] if wallets else None
        incust_pay_list = incust_pay.incust_pay_list if incust_pay else None
        balance_str = await f("incust wallet balance text", lang)

        if wallet and wallet.coupons:
            coupons_str = "\n" + await f(
                "incust wallet coupons text", lang, coupons_cnt=len(wallet.coupons)
            )
        else:
            coupons_str = None

        if wallet and wallet.bonuses:
            bonuses_str = "\n".join(
                [
                    " ".join([str(bonus.bonuses_amount), bonus.currency_name])
                    for bonus in wallet.bonuses
                ]
            )
        else:
            bonuses_str = None

        if wallet and wallet.accounts:
            accounts_str = "\n".join(
                [
                    "\n".join(
                        [
                            account.title,
                            " ".join(
                                [
                                    balance_str,
                                    str(account.balance),
                                    account.special_account.currency if
                                    account.special_account else ""
                                ]
                            )
                        ]
                    ) for account in wallet.accounts
                ]
            )
        else:
            accounts_str = None

        if incust_pay_list:
            specials = incust_pay_list.specials if not isinstance(
                incust_pay_list, list
            ) and incust_pay_list else []
            incust_pay_str = "\n".join(
                [
                    "\n".join(
                        [
                            special.title,
                            " ".join(
                                [
                                    balance_str,
                                    str(special.balance),
                                    special.special_account.currency if
                                    special.special_account else ""
                                ]
                            )
                        ]
                    ) for special in specials
                ]
            )
        else:
            incust_pay_str = None

        message_text = ""

        if bonuses_str:
            message_text = await f("incust wallet bonuses text", lang)
            message_text += f" {bonuses_str}\n"
        if accounts_str:
            message_text += f"{accounts_str}\n"
        if incust_pay_str:
            message_text += f"{incust_pay_str}\n"
        if coupons_str:
            message_text += f"{coupons_str}\n"

        if any([bonuses_str, accounts_str, incust_pay_str, coupons_str]):
            is_empty = False
            message_text = message_text.replace("\n\n", "\n")
        else:
            is_empty = True
            message_text = await f("incust wallet empty text", lang)

        if bot.bot_type == "telegram":
            if is_empty:
                keyboard = None
            else:
                keyboard = InlineKeyboard()
                keyboard.add_buttons(
                    UrlKeyboardButton(
                        await f("web app show wallet button", lang),
                        url=brand.get_url("profile/loyalty")
                    )
                )
                keyboard = keyboard.to_messanger(bot.bot_type)

            return await answer_obj.answer(
                message_text,
                reply_markup=keyboard,
            )

        elif bot.bot_type == "whatsapp":
            if not is_empty:
                message_text += "\n" + await f("web app show wallet button", lang) + ":"
                link = await brand.get_short_token_url(
                    user, bot.id, lang, "profile/loyalty"
                )
                message_text = "\n".join([message_text, link])
            return await answer_obj.answer(
                message_text, keyboard=await get_wa_menu_keyboard(user, bot, lang)
            )

    except Exception as ex:
        logging.error(ex, exc_info=True)
        await answer_obj.answer(
            await f("incust loyalty error", lang)
        )
