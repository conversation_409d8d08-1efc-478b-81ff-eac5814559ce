from types import UnionType
from typing import Class<PERSON><PERSON>, get_origin, get_type_hints, get_args, Union, Iterable, Any

from core import messangers_adapters as ma
from pydantic import BaseModel

from schemas import BotTypeLiteral
from utils.filters.helpers import get_lang
from utils.text import f, html_to_markdown


class ShortDeepLink(BaseModel):
    mode: ClassVar[str | None]
    separator: ClassVar[str] = "-"

    def __init_subclass__(cls, **kwargs):
        cls.mode = kwargs.pop("mode", None)

    @classmethod
    def get_mode(cls, mode: str | None = None):
        if not mode:
            mode = cls.mode

        if not mode:
            raise ValueError("mode must be specified, if it is not specified when class initialised")

        return mode

    def to_str(
            self,
            bot_type: BotTypeLiteral,
            bot_name: str,  # username for Telegram, phone number for Whatsapp
            mode: str | None = None,
    ):
        mode = self.get_mode(mode)
        match bot_type:
            case "telegram":
                link = f"https://t.me/{bot_name}?start={mode}"
            case "whatsapp":
                link = f"https://wa.me/{bot_name}?text={mode}"
            case _:
                raise ValueError(f"Unknown bot_type {bot_type}")

        if bot_type == "telegram":
            self.convert_params_from_float()

        if data := self.dict(exclude_defaults=True):
            link += self.separator + self.separator.join([str(el) for el in data.values()])
        return link

    @classmethod
    def get_filter(cls, mode: str | None = None, return_field_name: str | None = None):
        mode = cls.get_mode(mode)
        if not return_field_name:
            return_field_name = mode

        async def filter(message: ma.Message):
            if isinstance(message, ma.tg.types.Message):
                if message.get_command() != "/start":
                    return False

                _, *args = message.text.split(" ", 1)
                if not args:
                    return False
                data = args[0]
            else:
                data = message.text

            if not data:
                return

            message_mode, *params = data.split(cls.separator)

            if message_mode != mode:
                return False

            fields_names = tuple(cls.__fields__.keys())

            params = cls.convert_params(fields_names, params)

            try:
                data = cls(**dict(zip(fields_names, params)))
            except:
                lang = get_lang()
                bot_type = await ma.detect_bot_type(message)

                text = await f("deep link handling error", lang)
                if bot_type != "telegram":
                    text = html_to_markdown(text)
                await message.answer(text)

                raise ma.handler.CancelHandler(bot_type)

            return {return_field_name: data}

        return filter

    def convert_params_from_float(self):
        for key, value in self.dict(exclude_defaults=True).items():
            if isinstance(value, float):
                try:
                    if value % 1 == 0:
                        new_value = int(value)
                    else:
                        new_value = f"f{int(round(value * 100, 2))}"
                    setattr(self, key, new_value)
                except Exception:
                    pass

    @classmethod
    def convert_params(cls, names: Iterable[str], params: Iterable[Any]) -> list:
        new_params = []
        type_hints = get_type_hints(cls)

        for name, param in zip(names, params):
            type_ = type_hints.get(name, str)
            type_origin = get_origin(type_)
            is_union = type_origin is Union or type_origin is UnionType

            if type_origin is float or is_union and float in get_args(type_):
                try:
                    if isinstance(param, str) and param.startswith("f"):
                        new_value = float(round(int(param[1:]) / 100, 2))
                    else:
                        new_value = float(param)
                except Exception:
                    new_value = param
                new_params.append(new_value)
            else:
                new_params.append(param)

        return new_params
