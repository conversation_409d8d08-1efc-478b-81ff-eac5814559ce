from aiogram import types, Dispatcher
from aiogram.types import ContentTypes
from aiogram.dispatcher import FSMContext

from schemas import ChatType<PERSON><PERSON>
from ..functions import message_from_user_to_group_handler

from ..init_chat import get_away_from_chat


async def get_away_from_chat_button_handler(message: types.Message, state: FSMContext):
    await get_away_from_chat(message, state)


def register_chat_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        get_away_from_chat_button_handler,
        lstarts="get away from chat button",
        lkwargs=dict(receiver=""),
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        message_from_user_to_group_handler,
        in_chat_type=ChatTypeEnum.USER_WITH_GROUP,
        chat_type="private",
        content_types=ContentTypes.ANY,
        state="*",
    )
