from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

import schemas
from db import crud
from db.crud import delete_currently_running_vm_chats
from db.models import (ClientBot, Group, User, UserGroupSettings, VirtualManagerChat)
from schemas import Chat<PERSON>ype<PERSON>num
from utils.text import f
from .united_handlers import handle_connect_button
from .. import user_to_chat
from ..chat_message_sender import ChatMessageSender
from ..virtual_manager.handles import register_vm_buttons_handlers


async def connect_button_handler(
        callback_query: types.CallbackQuery, state: FSMContext,
        callback_data: dict, user: User, lang: str,
):
    await handle_connect_button(callback_data, state, user, lang, callback_query)


async def chat_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
        user: User,
        bot: ClientBot,
        lang: str
):
    group_id = callback_data.get("group_id")
    menu_in_store_id = callback_data.get("menu_in_store_id")

    if not group_id:
        await callback_query.answer(await f("error", lang), show_alert=True)
        raise ValueError("group_id must be specified in callback_data")

    if menu_in_store_id:
        await state.update_data(menu_in_store_id=menu_in_store_id)

    await delete_currently_running_vm_chats(user.id, bot.id)

    await user_to_chat(
        ChatTypeEnum.USER_WITH_GROUP, callback_query.message, group_id=group_id
    )


async def turn_off_mailing_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict, user: User, lang: str,
):
    bot = await ClientBot.get_current()
    group = await Group.get(callback_data.get("group_id"))

    user_group_settings = await UserGroupSettings.get(user, bot, group)
    await user_group_settings.update(mailing_mode="blocked")

    await callback_query.answer(
        await f("mailing turned off from profile", lang, group_name=group.name),
        show_alert=True
    )


async def stop_repeating_vm_messages_button_handler(
        callback_query: types.CallbackQuery,
        user: User,
        callback_data: dict, lang: str,
):
    vmc = await VirtualManagerChat.get(callback_data.get("vmc_id"))

    user_bot_activity = await user.activity_in_bot
    await user_bot_activity.reset_vm_remind()

    await callback_query.message.answer(await f("vm reminders stopped", lang))

    chat = await crud.get_or_create_chat(
        schemas.ChatTypeEnum.USER_WITH_GROUP,
        user.id,
        vmc.group_id,
        vmc.bot_id,
    )
    await ChatMessageSender(
        chat,
        schemas.MessageContentTypeEnum.TEXT,
        await f("vm not repeat last message button", lang),
        vmc_id=vmc.id,
        sent_by=schemas.ChatMessageSentByEnum.USER,
        sent_by_user_id=user.id,
        chat_pending=False,
    )


async def shop_error_button_handler(
        callback_query: types.CallbackQuery, callback_data: dict, lang: str
):
    error_text = callback_data.get("err")
    await callback_query.answer(
        await f(f"vm button {error_text} error", lang), show_alert=True
    )


def register_chat_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        connect_button_handler,
        callback_mode="connect",
        chat_type="private",
        state="*",
    )

    dp.register_callback_query_handler(
        chat_button_handler,
        callback_mode="chat",
        chat_type="private",
        state="*",
    )

    dp.register_callback_query_handler(
        turn_off_mailing_button_handler,
        callback_mode="turn_off_mailing",
        state="*",
    )

    dp.register_callback_query_handler(
        stop_repeating_vm_messages_button_handler,
        callback_mode="stop_repeating_vm_messages",
        state="*",
    )

    dp.register_callback_query_handler(
        shop_error_button_handler,
        callback_mode="shop",
        chat_type="private",
        state="*",
    )

    register_vm_buttons_handlers(dp)
