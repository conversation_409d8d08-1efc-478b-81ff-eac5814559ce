from aiogram import types

from core.messangers_adapters import FSMContext
from db.crud import delete_currently_running_vm_chats
from db.models import (
    ChatMember, ClientBot, FriendlyBotAnalyticAction, Group, User, UserAnalyticAction,
)
from schemas import ChatTypeEnum
from utils.text import f
from .. import user_to_chat
from ...helpers import detect_bot_type_and_send_message


async def handle_connect_button(
        callback_data: dict,
        state: FSMContext,
        user: User, lang: str,
        callback_query: types.CallbackQuery = None,
):
    bot_from_db = await ClientBot.get_current()
    await state.update_data(**callback_data)

    group_id = callback_data.get("group_id")
    group = await Group.get(group_id)

    virtual_manager_chat_id = callback_data.get("vmc")

    if group is None:
        text = await f("group not found error", lang)
        if callback_query:
            return await callback_query.answer(text, show_alert=True, cache_time=0)
        return await detect_bot_type_and_send_message(
            bot_from_db, user, "text", text=text
        )

    await delete_currently_running_vm_chats(user.id, bot_from_db.id)
    await user_to_chat(
        ChatTypeEnum.USER_WITH_GROUP, user=user, group_id=group.id,
        virtual_manager_chat_id=virtual_manager_chat_id,
        need_get_vm_from_history=False,
    )

    await UserAnalyticAction.save_button_click(
        user, bot_from_db, "connect",
        group=group,
    )

    if bot_from_db.is_friendly:
        chat_member = await ChatMember.create_or_get(user, group.channel)
        await FriendlyBotAnalyticAction.save_contacted_from_bot(
            member_id=chat_member.id
        )
