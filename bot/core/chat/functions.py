from typing import List

from core import messangers_adapters as ma
from db import crud
from db.models import (<PERSON>, <PERSON><PERSON>, ClientBot, Group, Tag, User, UserClientBotActivity)
from loggers import JSONLogger
from schemas import ChatMessageSentByEnum, VMButtonType, VMInteractiveType
from utils.text import f
from .chat_message_sender import ChatMessageSender
from .keyboards import get_menu_keyboard
from .virtual_manager.functions import vmc_answered
from .virtual_manager.interactives import RequestButton
from .virtual_manager.interactives.context import VMInteractiveContext
from .virtual_manager.interactives.functions import process_vm_interactives
from .virtual_manager.interactives.processors.results import (
    VMAnsweredInteractivesResult
)


async def change_tags(user: User, tags: List[str], lang: str):
    text = ""
    for params in tags:
        mode, tag_id = params[0], int(params[1:])
        tag = await Tag.get(tag_id)
        if tag is None:
            result, on_or_off = False, None
        elif mode == "+":
            result, on_or_off = await user.add_tag(tag), "on"
        elif mode == "-":
            result, on_or_off = await user.remove_tag(tag), "off"
        else:
            continue

        if not result:
            text += "\n" + await f(
                "turning on tag for user error", lang, tag_handle=tag.name
            )
        elif on_or_off:
            text += "\n" + await f(
                f"mailing change tag {on_or_off}", lang, tag=tag.name
            )


async def message_from_user_to_group_handler(
        message: ma.Message,
        state: ma.FSMContext,
        chat: Chat,
        bot: ClientBot,
        user: User,
        lang: str,
        **kwargs,
):
    async with state.proxy() as state_data:
        menu_in_store_id = state_data.pop("menu_in_store_id", None)

    sender = await ChatMessageSender.from_message(
        message, chat,
        sent_by=ChatMessageSentByEnum.USER,
        sent_by_user_id=chat.user_id,
        bot=bot,
        chat_user=user,
        menu_in_store_id=menu_in_store_id,
        auto_send=False
    )
    await sender.send()

    vmc = await sender.virtual_manager_chat
    if vmc and vmc.current_step_id:
        logger = JSONLogger(
            "vm", "Message from user", {
                "message": message,
                "user_id": user.id,
                "bot_id": bot.id,
                "vmc_id": vmc.id,
            }
        )

        vmc, vm, vm_step, _, group = await crud.get_vmc_data_for_process_answer(
            vmc, logger
        )
        user_bot_activity = await UserClientBotActivity.get(user, bot)

        logger.add_data(
            {
                "vm_id": vm.id,
                "vm_step_id": vm_step.id,
                "group_id": group.id,
                "user_bot_activity_id": user_bot_activity.id
            }
        )

        context = VMInteractiveContext(
            vm, vmc, bot, group,
            user, lang, logger, user_bot_activity
        )

        result = await process_vm_interactives(
            vmc.current_step_id,
            VMInteractiveType.ON_INPUT,
            context,
            logger,
            message,
            state=state,
            chat=chat,
            bot=bot,
            user=user,
            lang=lang,
            result=VMAnsweredInteractivesResult(),
            **kwargs,
        )

        await vmc_answered(
            vmc, vm, user, bot, vm_step,
            user_bot_activity,
            result.vm_next_step_id,
            result.stop_vm,
            result.pause_vm,
            started_vmc_id=result.started_vmc_id,
        )

        if bot.bot_type == "telegram":
            try:
                request_interactive = await crud.get_first_vm_interactive(
                    vm_step.id, VMInteractiveType.BUTTON,
                    VMButtonType.REQUEST.value,
                )
                if request_interactive:
                    interactive = RequestButton.build(request_interactive, context)
                    keyboard = await get_menu_keyboard(user, bot, lang)
                    text = await f(
                        f"{interactive.params.request_type.value} saved", lang
                    )
                    await message.answer(text, reply_markup=keyboard)
            except Exception as e:
                logger.error(
                    "An error occurred while sending restoring keyboard after request "
                    "button",
                    e
                )


async def get_business_name_from_bot(bot: ClientBot):
    brand = await Brand.get_by_group(bot.group_id)
    if brand:
        business_name = brand.name
    else:
        group = await Group.get(bot.group_id)
        business_name = group.name
    return brand, business_name
