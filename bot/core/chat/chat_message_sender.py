import json
from typing import Union

from fastapi import UploadFile
from psutils.undefined import Undefined

from config import DEBU<PERSON>, SERVICE_BOT_API_TOKEN, SERVICE_BOT_USERNAME
from core import messangers_adapters as ma
from core.chat.keyboards import (
    get_answer_chat_in_crm_keyboard,
    get_answer_message_keyboard,
)
from core.group.functions import get_custom_fields
from core.helpers import get_crm_chat_link
from core.kafka.producer.functions import (
    add_push_notifications_for_action, add_telegram_notifications_for_action,
    build_and_send_bot_message,
)
from core.kafka.producer.helpers import build_fcm_message
from core.media_manager import media_manager
from core.webhooks.functions import add_webhook_event, prepare_data_for_chat_webhook
from db import crud
from db.models import (
    Chat, ChatMessage, ClientBot,
    Group, MediaObject, User,
    UserClientBotActivity, VirtualManager,
    VirtualManagerChat,
)
from loggers import JSONLogger
from schemas import (
    AuthSourceEnum, ChatMessageSentByEnum, MessageContentTypeEnum, WebhookActionEnum,
    WebhookEntityEnum, WebhookNewChatMessageSchema,
)
from utils.message import send_wa_template_message
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f


class ChatMessageSender:
    def __init__(
            self, chat: Chat,
            content_type: MessageContentTypeEnum,
            text: str | None = None,
            media: MediaObject | UploadFile | str | None = None,
            content: dict | None = None, *,
            sent_by: ChatMessageSentByEnum,
            sent_by_user_id: int | None = None,
            vmc_id: int | None = None,
            is_mailing: bool = False,
            # specify None to leave the current pending state and
            # last_pending_set_datetime
            chat_pending: bool | None = Undefined,
            group: Group | None = None,
            bot: ClientBot | None = None,
            chat_user: User | None = None,
            virtual_manager_chat: VirtualManagerChat | None = None,
            virtual_manager: VirtualManager | None = None,
            keyboard: ma.types.Keyboard | None = None,
            crm_ignore_session_id: int | None = None,
            ignore_active_vm_chat: bool = False,
            menu_in_store_id: int | None = None,
            disable_send_to_service_bot: bool = False,
            user_bot_activity: UserClientBotActivity | None = None,
            wa_master_template_id: int | None = None,
            wa_template_variables: list[dict] | None = None,
            wa_reply_buttons_data: list[dict] | None = None,
    ):
        self.chat = chat
        self.content_type = content_type
        self.text = text
        self.media = media
        self.content = content
        self.sent_by = sent_by
        self.sent_by_user_id = sent_by_user_id
        self.vmc_id = vmc_id
        self.is_mailing = is_mailing
        self._chat_pending = chat_pending
        self._group = group
        self._bot = bot
        self._chat_user = chat_user
        self._virtual_manager_chat = virtual_manager_chat
        self._virtual_manager = virtual_manager
        self.keyboard = keyboard
        self.crm_ignore_session_id = crm_ignore_session_id
        self._is_from_message: bool = False
        self._message: ma.Message | None = None
        self.ignore_active_vm_chat = ignore_active_vm_chat
        self.menu_in_store_id = menu_in_store_id
        self.disable_send_to_service_bot = disable_send_to_service_bot
        self._user_bot_activity = user_bot_activity

        self.wa_master_template_id = wa_master_template_id
        self.wa_template_variables = wa_template_variables
        self.wa_reply_buttons_data = wa_reply_buttons_data

        self.logger = JSONLogger("chat.message-sender")
        self.set_logger_data()

    def set_logger_data(self):
        self.logger.set_data(
            "Chat Message Sender:",
            {
                "chat_id": self.chat.id,
                "content_type": self.content_type,
                "text": self.text,
                "media": str(self.media),
                "content": self.content,
                "sent_by": self.sent_by,
                "sent_by_user_id": self.sent_by_user_id,
                "vmc_id": self.vmc_id,
                "keyboard": self.keyboard,
                "crm_ignore_session_id": self.crm_ignore_session_id,
                "ignore_active_vm_chat": self.ignore_active_vm_chat,
                "menu_in_store_id": self.menu_in_store_id,
                "disable_send_to_service_bot": self.disable_send_to_service_bot,
                "wa_master_template_id": self.wa_master_template_id,
                "wa_template_variables": self.wa_template_variables,
                "wa_reply_buttons_data": self.wa_reply_buttons_data,
            }
        )

    @classmethod
    async def from_message(
            cls, message: ma.Message,
            chat: Chat,
            auto_send: bool = True,
            **kwargs,
    ) -> Union["ChatMessageSender", ChatMessage]:
        if not chat.bot_id:
            raise ValueError(
                f"chat.bot_id must be defined for {cls.__name__}.from_message"
            )

        content_type = message.content_type
        if content_type == "photo":
            content_type = "image"
        if content_type == "animation":
            content_type = "video"

        if content_type == ma.tg.types.ContentType.CONNECTED_WEBSITE:
            kwargs["group"] = group = kwargs.get(
                "group", await Group.get(chat.group_id)
            )
            content_type = "text"
            text = await f("connected website text", groi=group.lang)
        else:
            text = message.text if content_type == "text" else message.caption

        content_type = MessageContentTypeEnum(content_type)
        if isinstance(message, ma.tg.types.Message):
            media = await media_manager.download_file_from_tg_message(message)
        elif isinstance(message, ma.wa.types.Message):
            kwargs["bot"] = kwargs.get("bot", await ClientBot.get(chat.bot_id))
            media = await media_manager.download_file_from_wa_message(
                message, kwargs["bot"].token
            )
        else:
            media = None

        if message.content_type in ("sticker", "contact", "location"):
            content = getattr(message, message.content_type)
            if hasattr(content, "to_python"):
                content = content.to_python()
        else:
            content = None
        instance = cls(chat, content_type, text, media, content, **kwargs)
        instance._is_from_message = True
        instance._message = message
        if auto_send:
            return await instance.send()

        return instance

    @property
    async def group(self):
        if not self._group:
            self._group = await Group.get(self.chat.group_id)
        return self._group

    @property
    async def bot(self):
        if not self._bot and self.chat.bot_id:
            self._bot = await ClientBot.get(self.chat.bot_id)
        return self._bot

    @property
    async def chat_user(self):
        if not self._chat_user:
            self._chat_user = await User.get_by_id(self.chat.user_id)
        return self._chat_user

    @property
    async def user_bot_activity(self):
        if not self._user_bot_activity:
            if self.chat.bot_id:
                self._user_bot_activity = await UserClientBotActivity.get(
                    await self.chat_user, await self.bot
                )
        return self._user_bot_activity

    @property
    async def virtual_manager_chat(self):
        if self._virtual_manager_chat:
            return self._virtual_manager_chat

        if self.vmc_id:
            self._virtual_manager_chat = await VirtualManagerChat.get(self.vmc_id)
        elif (not self.ignore_active_vm_chat and self.sent_by ==
              ChatMessageSentByEnum.USER):
            chat_user = await self.chat_user
            group = await self.group
            bot = await self.bot
            self._virtual_manager_chat = await crud.get_active_vmc(
                chat_user.id, bot.id, group.id
            )
        return self._virtual_manager_chat

    @property
    async def chat_pending(self):
        if self._chat_pending is Undefined:
            if self.sent_by != ChatMessageSentByEnum.USER:
                self._chat_pending = False
            else:
                vmc = await self.virtual_manager_chat
                self._chat_pending = not vmc
        return self._chat_pending

    async def validate_input_params(self):
        input_data_errors = []

        if self.sent_by == ChatMessageSentByEnum.USER:
            if not self.sent_by_user_id:
                input_data_errors.append(
                    "Messages with sent_by 'user' or 'manager' require sent_by_user_id"
                )
        elif self.sent_by == ChatMessageSentByEnum.MANAGER:
            if self.vmc_id:
                input_data_errors.append(
                    "Messages with sent_by 'manager' do not support vmc_id"
                )
        elif self.sent_by == ChatMessageSentByEnum.VM:
            if not self.vmc_id:
                input_data_errors.append(
                    "Messages with sent_by 'vm' require vmc_id"
                )
            if self.sent_by_user_id:
                input_data_errors.append(
                    "Messages with sent_by 'vm' do not support sent_by_user_id"
                )

        if self.content_type == MessageContentTypeEnum.PROCESSED and (
                self.text or self.media or self.content):
            input_data_errors.append(
                "Messages with content_type 'processed' do not support text, "
                "media and content"
            )

        if self.content_type == MessageContentTypeEnum.TEXT:
            if not self.text:
                input_data_errors.append(
                    "Messages with content_type 'text' require text"
                )
            if self.media or self.content:
                input_data_errors.append(
                    "Messages with content_type 'text' do not support media and content"
                )

        if self.content_type in (
                MessageContentTypeEnum.IMAGE,
                MessageContentTypeEnum.VIDEO,
                MessageContentTypeEnum.VOICE,
                MessageContentTypeEnum.AUDIO,
                MessageContentTypeEnum.DOCUMENT,
        ):
            if not self.media:
                input_data_errors.append(
                    f"Messages with content_type '{self.content_type.value}' require "
                    f"media"
                )
            if self.content:
                input_data_errors.append(
                    f"Messages with content_type '{self.content_type.value}' do not "
                    f"support content"
                )

        if self.content_type in (
                MessageContentTypeEnum.STICKER,
                MessageContentTypeEnum.LOCATION,
                MessageContentTypeEnum.CONTACT,
        ):
            if not self.content:
                input_data_errors.append(
                    f"Messages with content_type '{self.content_type.value}' require "
                    f"content"
                )
            if self.media or self.text:
                input_data_errors.append(
                    f"Messages with content_type '{self.content_type.value}' do not "
                    f"support media text"
                )

        if self.media and not isinstance(self.media, MediaObject):
            if isinstance(self.media, UploadFile):
                self.media = await media_manager.save_from_upload_file(self.media)
            elif isinstance(self.media, str):
                if self.media.startswith("http"):
                    self.media = await media_manager.download_media(self.media)
                else:
                    self.media = await media_manager.save_from_file_path(self.media)
            else:
                input_data_errors.append(
                    f"Parameter media must be MediaObject, UploadFile or str, "
                    f"not {type(self.media)}"
                )

        if len(input_data_errors) == 1:
            error_text = input_data_errors[0]
        elif input_data_errors:
            error_text = (
                    f"Invalid parameters for save_and_sent_chat_message. "
                    f"Errors:\n" + "\n".join(input_data_errors)
            )
        else:
            error_text = None

        if error_text:
            self.logger.error("Validating input params FAILED", error_text)
            raise ValueError(error_text)

        self.set_logger_data()

    async def save_message(self):
        vmc = await self.virtual_manager_chat

        message = await crud.save_chat_message(
            self.chat, self.content_type, self.text, self.media, self.content,
            sent_by=self.sent_by,
            sent_by_user_id=self.sent_by_user_id, vmc_id=vmc.id if vmc else None,
            is_mailing=self.is_mailing,
            chat_pending=await self.chat_pending,
            menu_in_store_id=self.menu_in_store_id,
            wa_template_variables=self.wa_template_variables,
            wa_master_template_id=self.wa_master_template_id,
        )
        self.logger.add_data({"chat_message_id": message.id})
        if DEBUG:
            self.logger.debug("Message saved")
        return message

    async def send_message_to_user_in_bot(self):
        if (
                self.content_type == MessageContentTypeEnum.PROCESSED or
                self.sent_by == ChatMessageSentByEnum.USER
        ):
            if DEBUG:
                self.logger.debug(
                    "send_message_to_user_in_bot: no message for user, returning"
                )
            return

        bot = await self.bot
        user = await self.chat_user

        user_lang = await user.get_lang(bot)
        group = await self.group

        if self.content_type == MessageContentTypeEnum.TEMPLATE:
            try:
                await send_wa_template_message(
                    self.wa_master_template_id,
                    user.wa_phone,
                    bot,
                    user_lang,
                    self.wa_template_variables,
                    self.wa_reply_buttons_data,
                )
            except Exception as e:
                self.logger.error("send_message_to_user_in_bot FAILED", repr(e))
                await send_message_to_platform_admins(
                    f"An error occurred while sending WhatsApp template to user in "
                    f"bot: "
                    f"{str(e)}\n"
                    f"Chat id: {self.chat.id}\n"
                    f"Profile: {group.name}({group.id})\n"
                    f"Chat user: {user.name}({user.id})\n"
                )

        else:
            user_bot_activity = await self.user_bot_activity

            if (
                    not user_bot_activity or
                    not user_bot_activity.is_active or
                    not user_bot_activity.is_entered_bot
            ):
                self.logger.debug(
                    "user_bot_activity is not active or user is not entered bot, "
                    "returning",
                    {
                        "user_bot_activity": user_bot_activity,
                        "user_bot_activity.is_active": user_bot_activity.is_active if
                        user_bot_activity else None,
                        "user_bot_activity.is_entered_bot":
                            user_bot_activity.is_entered_bot if user_bot_activity
                            else None,
                    }
                )
                vmc = await self.virtual_manager_chat
                if vmc:
                    await vmc.delete()
                return

            if user_bot_activity.active_chat_id != self.chat.id:
                self.keyboard = await get_answer_message_keyboard(
                    user_lang, group,
                    bot_type=bot.bot_type,
                    vmc=await self.virtual_manager_chat,
                    keyboard=self.keyboard,
                )

            try:
                await build_and_send_bot_message(
                    bot.id, bot.display_name,
                    bot.bot_type, bot.token,
                    bot_wa_from=bot.whatsapp_from,
                    tg_chat_id=user.chat_id,
                    wa_phone=user.wa_phone,
                    user_id=user.id,
                    user_name=user.name,
                    content_type=self.content_type.value,
                    text=self.text,
                    media=self.media,
                    content=self.content,
                    keyboard=self.keyboard,
                    logger=self.logger,
                )
            except Exception as e:
                self.logger.error("send_message_to_user_in_bot FAILED", repr(e))
                await send_message_to_platform_admins(
                    f"An error occurred while sending chat message to user in bot: "
                    f"{str(e)}\n"
                    f"Chat id: {self.chat.id}\n"
                    f"Profile: {group.name}({group.id})\n"
                    f"Chat user: {user.name}({user.id})\n"
                )

    async def send_message_to_crm(self, chat_message: ChatMessage):
        group = await self.group
        bot = await self.bot
        chat_user = await self.chat_user

        group_and_bot_names = [group.name]
        if bot and bot.group_id != group.id:
            group_and_bot_names.append(bot.display_name)
        group_and_bot_name = " | ".join(group_and_bot_names)

        chat_pending = await self.chat_pending

        async def get_message(user: User):

            if chat_pending:
                title = await f(
                    "crm chat message new notification title",
                    user.lang,
                    user_name=chat_user.name,
                    group_and_bot_name=group_and_bot_name,
                ) if chat_pending else None

                if self.content_type == MessageContentTypeEnum.TEXT:
                    body = self.text.strip()
                else:
                    body = await f(f"content type {self.content_type.value} text")
                    if self.text:
                        body += f": {self.text.strip()}"
            else:
                title = None
                body = None

            return build_fcm_message(
                "chat",
                self.chat.id,
                self.chat.crm_tag,
                title,
                body,
                delete_notification=chat_pending,
                apns_priority="10",
                apns_push_type="alert" if chat_pending else "background",
                add_data_texts=chat_pending,
                link=get_crm_chat_link(self.chat.id),
                is_chat_pending=self.chat.is_pending,
                profile_id=self.chat.group_id,
                user_id=self.chat.user_id,
            )

        try:
            webhook_data: WebhookNewChatMessageSchema = await (
                prepare_data_for_chat_webhook(
                    chat=self.chat, message=chat_message, group=group,
                    user=await self.chat_user,
                    media=self.media,
                ))
            await add_webhook_event(
                entity=WebhookEntityEnum.CHAT,
                entity_id=self.chat.id,
                action=WebhookActionEnum.NEW_MESSAGE if chat_pending else
                WebhookActionEnum.CHANGE_READ,
                group_id=group.id,
                data=webhook_data.dict(),
                data_type=WebhookNewChatMessageSchema,
            )
            return await add_push_notifications_for_action(
                AuthSourceEnum.CRM_WEB, AuthSourceEnum.CRM_APP,
                action="crm_chat:read",
                available_data={
                    "profile_id": group.id,
                    "chat_id": self.chat.id,
                },
                message=get_message,
                ignore_session_id=self.crm_ignore_session_id,
                logger=self.logger,
            )
        except Exception as e:
            self.logger.error("send_message_to_crm FAILED", repr(e))
            await send_message_to_platform_admins(
                f"An error occurred while sending chat push notifications: {str(e)}\n"
                f"Chat id: {self.chat.id}\n"
                f"Profile: {group.name}({group.id})\n"
                f"Chat user: {chat_user.name}({chat_user.id})\n"
            )

    async def send_message_to_service_bot(self):
        if self.disable_send_to_service_bot:
            return

        chat_user = await self.chat_user
        group = await self.group
        vmc = await self.virtual_manager_chat

        async def get_sending_data(manager_user: User):
            manager_lang = manager_user.lang

            content_type = self.content_type.value
            if content_type == "image":
                content_type = "photo"

            custom_fields = await get_custom_fields(
                await self.chat_user, await self.group, manager_lang
            )
            text_kwargs = {
                "text": self.text or "",
                "custom_fields": custom_fields,
                "manager_full_name": manager_user.name,
            }
            from_or_to = "from" if self.sent_by == ChatMessageSentByEnum.USER else "to"
            text = await group.get_notification_text(
                f"{from_or_to}_user_message", manager_lang,
                chat_user, vmc,
                **text_kwargs
            )

            sending_data = {
                "content_type": content_type,
                "text": text,
            }
            if self.media:
                sending_data[content_type] = dict(
                    path_or_bytesio=self.media.file_path,
                    filename=self.media.original_file_name or self.media.file_name,
                )
            elif self.content:
                sending_data[content_type] = self.content

            sending_data["keyboard"] = (await get_answer_chat_in_crm_keyboard(
                self.chat.id, manager_lang
            )).to_python()

            return sending_data

        try:
            await add_telegram_notifications_for_action(
                "service",
                SERVICE_BOT_USERNAME,
                SERVICE_BOT_API_TOKEN,
                action="crm_chat:read",
                available_data={
                    "profile_id": group.id,
                    "chat_id": self.chat.id,
                },
                message=get_sending_data,
                logger=self.logger,
            )
        except Exception as e:
            self.logger.error("send_message_to_service_bot FAILED", repr(e))
            await send_message_to_platform_admins(
                f"An error occurred while sending chat notifications to service bot: "
                f"{str(e)}\n"
                f"Chat id: {self.chat.id}\n"
                f"Profile: {group.name}({group.id})\n"
                f"Chat user: {chat_user.name}({chat_user.id})\n"
            )

    @property
    def debug_data(self):
        return json.dumps(
            {
                "chat_id": self.chat.id,
                "content_type": self.content_type.value,
                "text": self.text,
                "media": self.media if isinstance(
                    self.media, str
                ) else self.media.filename if isinstance(
                    self.media, UploadFile
                ) else self.media.url if self.media else None,
                "sent_by": self.sent_by.value,
                "sent_by_user_id": self.sent_by_user_id,
                "vmc_id": self.vmc_id,
            }, indent=4, ensure_ascii=False
        )

    async def send(self):
        if DEBUG:
            self.logger.debug("Started")
        await self.validate_input_params()

        chat_message = await self.save_message()

        await self.send_message_to_user_in_bot()
        await self.send_message_to_crm(chat_message)
        await self.send_message_to_service_bot()

        self.logger.debug("Finished")
        return chat_message

    def __call__(self):
        return self.send()

    def __await__(self):
        return self.send().__await__()
