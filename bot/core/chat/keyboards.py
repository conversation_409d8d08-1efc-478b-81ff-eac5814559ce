from typing import Literal

from core import messangers_adapters as ma
from core.helpers import get_crm_chat_link

from db.models import ClientBot, Group, User, VirtualManagerChat
from core.messangers_adapters import MenuKeyboard, MenuKeyboardButton

from utils.text import f, c


async def get_answer_message_keyboard(
        lang: str,
        group: Group,
        *,
        bot_type: Literal["whatsapp", "telegram"] = "telegram",
        vmc: VirtualManagerChat = None,
        keyboard: ma.tg.types.InlineKeyboardMarkup | ma.wa.types.ReplyKeyboard = None
) -> ma.tg.types.InlineKeyboardMarkup | ma.wa.types.ReplyKeyboard | None:
    button_data = dict(mode="connect", group_id=group.id)

    sender = group.name

    if vmc:
        button_data.update(vmc=vmc.id)
        need_reply_button = False
    else:
        need_reply_button = True

    if need_reply_button:
        text = await f("message reply button", lang, sender=sender)
        data = c(**button_data)

        if not keyboard:
            match bot_type:
                case "telegram":
                    keyboard = ma.tg.types.InlineKeyboardMarkup()
                case "whatsapp":
                    keyboard = ma.wa.types.ReplyKeyboard()

        if bot_type == "whatsapp":
            keyboard.add_buttons(
                ma.wa.types.ReplyButton(
                    id=data, title=text,
                )
            )
        else:
            message_reply_btn = ma.tg.types.InlineKeyboardButton(text, callback_data=data)
            keyboard.add(message_reply_btn)
    return keyboard


async def get_start_chat_keyboard(receiver: str, lang: str):
    keyboard = MenuKeyboard(resize_keyboard=True, one_time_keyboard=False)
    keyboard.add_buttons(
        MenuKeyboardButton(await f("get away from chat button", lang, receiver=receiver), "get_away_from_chat"),
    )
    return keyboard


async def get_menu_keyboard(user: User, bot: ClientBot, lang: str):
    if bot.is_friendly:
        from friendly.main.keyboards import get_menu_keyboard as get_friendly_bot_menu_keyboard
        keyboard = await get_friendly_bot_menu_keyboard(user, lang)

    elif bot.bot_type == "whatsapp":
        from core.whatsapp.keyboards import get_wa_menu_keyboard
        keyboard = await get_wa_menu_keyboard(user, bot, lang)

    else:
        from client.main.keyboards import get_menu_keyboard as get_client_bot_menu_keyboard
        keyboard = await get_client_bot_menu_keyboard(user.chat_id, bot)

    return keyboard


async def get_answer_chat_in_crm_keyboard(chat_id: int, lang: str):
    keyboard = ma.tg.types.InlineKeyboardMarkup()
    keyboard.insert(
        ma.tg.types.InlineKeyboardButton(
            await f("service bot answer chat in crm button", lang),
            web_app=ma.tg.types.WebAppInfo(
                url=get_crm_chat_link(chat_id)
            )
        )
    )
    return keyboard
