import logging
from typing import List
from abc import abstractmethod

from aiogram import types, Dispatcher
from aiogram.dispatcher.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>

from db.models import Chat, User
from schemas import ChatTypeEnum


class BaseAiogramInChatFilter(BoundFilter):

    all_chat_types = "*"

    key = "in_chat_type"

    @property
    @abstractmethod
    def allowed_chat_types(self) -> List[ChatTypeEnum]:
        raise NotImplementedError

    def __init__(self, in_chat_type: ChatTypeEnum | List[ChatTypeEnum]):
        if in_chat_type == self.all_chat_types or in_chat_type == [self.all_chat_types]:
            self.chat_types = self.allowed_chat_types
            return

        if type(in_chat_type) is ChatTypeEnum:
            chat_types = [in_chat_type]
        elif type(in_chat_type) is not list:
            raise ValueError(
                f"Argument chat_type must be ChatTypeEnum or list of ChatTypeEnum, not {type(in_chat_type)}"
            )
        else:
            chat_types = in_chat_type

        if self.all_chat_types in chat_types:
            logger = logging.getLogger()
            logger.warning(
                "Chat type \"*\" overrides all other specified chat types."
                "Remove other chat types for best code practice."
            )

        for in_chat_type in chat_types:
            if in_chat_type in self.allowed_chat_types:
                continue

            raise ValueError(
                f"Argument chat mode must be one of {self.all_chat_types}"
                f"or {self.allowed_chat_types} for handle all not null chat_type,"
                f"not {in_chat_type}"
            )
        self.chat_types: List[ChatTypeEnum] = chat_types

    async def check(
            self,
            obj: types.Message | types.CallbackQuery | types.PreCheckoutQuery | types.ShippingQuery,
    ) -> bool:
        if obj.from_user is None:
            return False

        user = await User.get(obj.from_user.id)
        if not user:
            return False

        return await self._check_chat(user)

    @abstractmethod
    async def _check_chat(self, user: User) -> bool:
        raise NotImplementedError


class AiogramInChatFilter(BaseAiogramInChatFilter):
    allowed_chat_types = [
        ChatTypeEnum.USER_WITH_GROUP
    ]

    async def _check_chat(self, user: User):
        user_bot_activity = await user.activity_in_bot

        if not user_bot_activity or not user_bot_activity.active_chat_id:
            return False

        chat = await Chat.get(user_bot_activity.active_chat_id)
        if chat.type in self.chat_types:
            return {
                "chat": chat
            }
        return False


def bind_chat_filters(dp: Dispatcher):
    dp.bind_filter(AiogramInChatFilter)
