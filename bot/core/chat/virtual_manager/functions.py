from collections import defaultdict
from typing import Any

from psutils.translator.schemas import TranslateObjectData

import schemas
from core import messangers_adapters as ma
from db import crud
from db.models import (
    ClientBot, Group, MediaObject, User, UserClientBotActivity, VirtualManager,
    VirtualManagerChat,
    VirtualManagerInteractive, VirtualManagerStep,
)
from loggers import JSONLogger
from schemas import BillingProductCode
from utils.translator import td
from utils.translator.models import VirtualManagerInteractiveTranslatorModel
from .db_funcs import get_group_for_user_vm_link
from ..chat_message_sender import ChatMessageSender
from ...billing.quota_processor import BillingQuotaProcessor


async def get_or_create_group_for_user_vm_link(user: User, bot: ClientBot):
    group = await get_group_for_user_vm_link(user.id, bot.id)
    if not group:
        group = await crud.create_group(
            user.name, user, bot,
        )
    return group


async def send_vm_button_answer_to_chat(
        button_text: str,
        vm: VirtualManager,
        vmc: VirtualManagerChat,
        user: User,
        bot: ClientBot,
        user_bot_activity: UserClientBotActivity,
        group: Group,
):
    chat = await crud.get_or_create_chat(
        schemas.ChatTypeEnum.USER_WITH_GROUP, user.id, group.id, bot.id
    )
    return await ChatMessageSender(
        chat,
        content_type=schemas.MessageContentTypeEnum.TEXT,
        text=button_text,
        sent_by=schemas.ChatMessageSentByEnum.USER,
        sent_by_user_id=user.id,
        vmc_id=vmc.id,
        virtual_manager_chat=vmc,
        bot=bot,
        chat_user=user,
        group=group,
        virtual_manager=vm,
        user_bot_activity=user_bot_activity,
    )


async def vm_button_pre_process(
        action_for_logger: str,
        query: ma.types.ButtonQuery,
        user: User,
        bot: ClientBot,
        user_bot_activity: UserClientBotActivity,
        button_data: Any,
        vmc_id: int,
        interactive_id: int | None = None,
        step_id: int | None = None
):
    logger = JSONLogger(
        "vm", action_for_logger, {
            "query": query,
            "user_id": user.id,
            "bot_id": bot.id,
            "user_bot_activity": user_bot_activity,
            "button_data": button_data,
        }
    )

    vmc, vm, vm_step, db_interactive, group = await (
        crud.get_vmc_data_for_process_answer(
            vmc_id, logger, interactive_id, step_id,
        ))
    logger.add_data(
        {
            "db_interactive_id": db_interactive.id if db_interactive else None,
            "vmc_id": vmc.id,
            "vm_id": vm.id,
            "vm_step_id": vm_step.id,
            "group_id": group.id,
            "user_bot_activity_id": user_bot_activity.id,
        }
    )

    return vmc, vm, vm_step, db_interactive, group, logger


async def get_virtual_manager_steps_schemas(
        vm_id: int, group: Group
):
    langs_to_translate = group.get_langs_list(False)
    data = await crud.get_vm_steps_data(vm_id)

    grouped_data = {}

    for row in data:
        step, step_translation, step_media, interactive, interactive_translation = row

        if step.id not in grouped_data:
            grouped_data[step.id] = {
                "step": step,
                "translations": {},
                "interactives": {},
            }
        step_data = grouped_data[step.id]

        if step_translation and step_translation.lang not in step_data["translations"]:
            step_data["translations"][step_translation.lang] = step_translation

        if step_media and not step_data.get("media"):
            step_data["media"] = step_media

        if not interactive:
            continue

        if interactive.id not in step_data["interactives"]:
            step_data["interactives"][interactive.id] = {
                "interactive": interactive,
                "translations": {},
            }

        interactive_data = step_data["interactives"][interactive.id]

        if interactive_translation and interactive_translation.lang not in \
                interactive_data["translations"]:
            interactive_data["translations"][
                interactive_translation.lang] = interactive_translation

    to_translate: dict[str, dict] = defaultdict(dict)

    params_with_translations = VirtualManagerInteractiveTranslatorModel.params.type.keys

    for step_data in grouped_data.values():
        for lang in langs_to_translate:
            to_translate[lang][step_data["step"]] = TranslateObjectData(
                object=step_data["step"],
                translation=step_data["translations"].get(lang),
            )

        for interactive_data in step_data["interactives"].values():
            for lang in langs_to_translate:
                interactive: VirtualManagerInteractive = interactive_data["interactive"]
                if (
                        not interactive.params or
                        not any(
                            map(
                                lambda x: x[0] in params_with_translations and x[1],
                                interactive.params.items()
                            )
                        )
                ):
                    continue
                to_translate[lang][interactive] = TranslateObjectData(
                    object=interactive,
                    translation=interactive_data["translations"].get(lang),
                )

    translated = {}

    for lang, to_translate_lang in to_translate.items():
        translated[lang] = await td(
            to_translate_lang, lang,
            group.lang,
            group_id=group.id,
            is_auto_translate_allowed=group.is_translate,
            fallback_to_original_on_error=False,
        )

    result: list[schemas.VirtualManagerStepSchema] = []

    for step_data in grouped_data.values():
        step: VirtualManagerStep = step_data["step"]
        step_media: MediaObject | None = step_data.get("media")

        step_translations = {
            lang: translated_lang[step]
            for lang, translated_lang in translated.items()
        }

        schema = schemas.VirtualManagerStepSchema(
            **schemas.VirtualManagerStepORMSchema.from_orm(step).dict(),
            translations=step_translations,
            media_url=step_media.url if step_media else None,
            media_type=step_media.media_type if step_media else None,
            interactives=[
                {
                    "id": el["interactive"].id,
                    "type": el["interactive"].type.value,
                    "subtype": el["interactive"].subtype,
                    "params": el["interactive"].params,
                } for el in
                step_data["interactives"].values()
            ]
        )

        for i, interactive_data in enumerate(step_data["interactives"].values()):
            interactive = interactive_data["interactive"]
            translations = {
                lang: translated_lang[interactive]
                for lang, translated_lang
                in translated.items()
                if translated_lang.get(interactive)
            }
            if translations:
                schema.interactives[i].translations = translations
        result.append(schema)

    return result


async def start_virtual_manager_chat(
        user: User,
        virtual_manager_or_id: VirtualManager | int | str,
        group_or_id: Group | int | None = None,
        bot_or_id: ClientBot | int | None = None,
        on_start_delay: float | None = None,
        step: VirtualManagerStep | int | None = None,
):
    vm = await crud.get_vm_by_id_or_name_id(virtual_manager_or_id)

    bot = await ClientBot.get(bot_or_id) if bot_or_id else None

    if not await BillingQuotaProcessor(
            int(group_or_id) if group_or_id else vm.group_id,
            BillingProductCode.VM_CHAT,
            exception_on_error="suspended",
            return_on_error="false",
            user=user,
            bot=bot,
    ).check_product_limit():
        return None

    return await crud.start_virtual_manager_chat(
        user, vm,
        group_or_id, bot_or_id,
        on_start_delay, step,
    )


async def vmc_answered(
        vmc: VirtualManagerChat,
        vm: VirtualManager,
        user: User,
        bot: ClientBot,
        current_step: VirtualManagerStep,
        user_bot_activity: UserClientBotActivity,
        go_to_step_id: int | None = None,
        stop_vm: bool = False,
        pause_vm: bool = False,
        is_answer_for_remind: bool = False,
        send_next_message_delay: float | None = None,
        started_vmc_id: int | None = None,
):
    if not vmc.is_usage_recorded:
        if not await BillingQuotaProcessor(
                vmc.group_id,
                BillingProductCode.VM_CHAT,
                exception_on_error="suspended",
                return_on_error="false",
                user=user,
                bot=bot,
        ).record_usage():
            return None
        await vmc.update(is_usage_recorded=True)

    return await crud.vmc_answered(
        vmc, vm,
        current_step,
        user_bot_activity,
        go_to_step_id,
        stop_vm,
        pause_vm,
        is_answer_for_remind,
        send_next_message_delay,
        started_vmc_id=started_vmc_id,
    )
