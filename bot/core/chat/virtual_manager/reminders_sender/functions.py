from config import DEBUG
from db import crud
from db.models import UserClientBotActivity, VirtualManagerChat
from loggers import JSONLogger
from schemas import ChatMessageSentByEnum, ChatTypeEnum, MessageContentTypeEnum
from utils.text import f
from .keyboards import get_remind_keyboard
from ..messages_sender import send_virtual_manager_message
from ...chat_message_sender import ChatMessageSender


async def send_repeated_vm_message(
        vmc: VirtualManagerChat, reminded_count: int, logger: JSONLogger | None = None
):
    if reminded_count % 2 == 0:
        await send_virtual_manager_message(vmc, is_repeated_message=True, logger=logger)
        return

    if logger is None:
        logger = JSONLogger("vm")

    logger.add_texts("Send repeated VM message")
    logger.add_data({"vm_id": vmc.id, "is_repeated_message": True})

    vm, user, group, bot, step, step_media = await (
        crud.get_vmc_data_for_send_message(
            vmc
        )
    )
    user_bot_activity = await UserClientBotActivity.get(user, bot)
    lang = await user.get_lang(bot)

    logger.add_data(
        {
            "vm_id": vm.id,
            "user_id": user.id,
            "group_id": group.id,
            "bot_id": bot.id,
            "step_id": step.id,
            "step_media": step_media,
            "user_bot_activity_id": user_bot_activity.id,
        }
    )

    chat = await crud.get_or_create_chat(
        ChatTypeEnum.USER_WITH_GROUP,
        vmc.user_id,
        vmc.group_id,
        vmc.bot_id,
    )

    message_text = await f("vm not answered last message reminder", lang)
    keyboard = await get_remind_keyboard(vmc, lang)

    if DEBUG:
        logger.debug(
            "Sending", {
                "text": message_text,
                "keyboard": keyboard
            }
        )

    await ChatMessageSender(
        chat, MessageContentTypeEnum.TEXT,
        message_text,
        sent_by=ChatMessageSentByEnum.VM,
        vmc_id=vmc.id,
        chat_user=user,
        virtual_manager_chat=vmc,
        keyboard=keyboard,
    )

    await crud.vmc_message_sent(vmc, vm, step, user_bot_activity)
