from datetime import datetime
from typing import List, <PERSON><PERSON>

from db import db_func, sess
from db.models import VirtualManager, VirtualManagerChat, UserClientBotActivity


@db_func
def get_vmcs_for_send_reminders() -> List[Tuple[VirtualManagerChat, int]]:
    query = sess().query(VirtualManagerChat, UserClientBotActivity.vm_reminded_count)
    query = query.join(VirtualManagerChat.bot_activity_reminder_set)
    query = query.join(VirtualManagerChat.virtual_manager)

    query = query.filter(VirtualManagerChat.is_deleted.is_(False))
    query = query.filter(UserClientBotActivity.vm_when_remind <= datetime.utcnow())
    query = query.filter(UserClientBotActivity.vm_reminded_count < VirtualManager.reminds_count)

    return query.all()
