from db.models import VirtualManagerChat
from utils.text import f
from utils.redefined_classes import InlineKb, InlineBtn

from .callback_data import VMRemindCallbackData, VMStopReminderCallbackData


async def get_remind_keyboard(vmc: VirtualManagerChat, lang: str) -> InlineKb:
    keyboard = InlineKb()

    button_text = await f("vm repeat last message button", lang)
    keyboard.insert(
        InlineBtn(
            button_text,
            callback_data=VMRemindCallbackData(
                step_id=vmc.current_step_id,
                vmc_id=vmc.id,
            ).to_str()
        )
    )

    button_text = await f("vm not repeat last message button", lang)
    keyboard.insert(
        InlineBtn(
            button_text,
            callback_data=VMStopReminderCallbackData(
                step_id=vmc.current_step_id,
                vmc_id=vmc.id,
            ).to_str()
        )
    )

    return keyboard
