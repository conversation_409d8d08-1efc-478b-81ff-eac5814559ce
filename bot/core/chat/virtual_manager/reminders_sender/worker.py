from db import DBSession
from loggers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.processes_manager.background_worker import LoopBackgroundWorker
from .functions import send_repeated_vm_message
from .db_funcs import get_vmcs_for_send_reminders


class VmRemindersWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "vm reminders"
    DEFAULT_TIMEOUT = 5

    async def iteration(self):
        with DBSession():
            try:
                chats_to_remind = await get_vmcs_for_send_reminders()
            except Exception as e:
                <PERSON><PERSON><PERSON><PERSON><PERSON>("vm", "Reminder").error(e, exc_info=True)
                chats_to_remind = None

            if chats_to_remind:
                for vmc_and_count in chats_to_remind:
                    logger = JSONLogger("vm")

                    try:
                        await send_repeated_vm_message(*vmc_and_count, logger=logger)
                    except Exception as e:
                        logger.error(e)
                        vmc = vmc_and_count[0]
                        user_bot_activity = await vmc.bot.get_user_activity(vmc.user)
                        await user_bot_activity.reset_vm_remind()
                    else:
                        logger.debug("Success")
