from datetime import datetime, timedelta
from typing import List

from sqlalchemy import desc, not_, and_
from sqlalchemy.orm import aliased

from config import VIRTUAL_MANAGERS_TO_SEND_ITERATION_LIMIT, VIRTUAL_MANAGER_CHAT_IS_EXPIRED_AFTER
from db import db_func, sess
from db.models import VirtualManagerChat


@db_func
def get_vmcs_to_send_message() -> List["VirtualManagerChat"]:
    expiration = VIRTUAL_MANAGER_CHAT_IS_EXPIRED_AFTER
    now = datetime.utcnow()

    query = sess().query(VirtualManagerChat).distinct()
    subquery_vmc = aliased(VirtualManagerChat)

    query = query.join(
        subquery_vmc,
        and_(
            subquery_vmc.user_id == VirtualManagerChat.user_id,
            subquery_vmc.bot_id == VirtualManagerChat.bot_id,
            subquery_vmc.is_deleted.is_(False),
            subquery_vmc.started_datetime > VirtualManagerChat.started_datetime,
        ),
        isouter=True
    )

    query = query.filter(VirtualManagerChat.is_deleted.is_(False))
    query = query.filter(VirtualManagerChat.when_send_message <= now)
    query = query.filter(subquery_vmc.id.is_(None))

    expired_condition = VirtualManagerChat.when_send_message > now - timedelta(seconds=expiration)
    query = query.order_by(expired_condition)
    query = query.order_by(desc(VirtualManagerChat.when_send_message))

    query = query.limit(VIRTUAL_MANAGERS_TO_SEND_ITERATION_LIMIT)

    return query.all()


@db_func
def get_vmcs_to_send_message_deprecated() -> List["VirtualManagerChat"]:
    now = datetime.utcnow()
    query = sess().query(VirtualManagerChat).distinct()

    query = query.filter(VirtualManagerChat.is_deleted.is_(False))
    query = query.filter(VirtualManagerChat.when_send_message <= now)

    subquery_vmc = aliased(VirtualManagerChat)
    subquery = sess().query(subquery_vmc)
    subquery = subquery.filter(subquery_vmc.user_id == VirtualManagerChat.user_id)
    subquery = subquery.filter(subquery_vmc.bot_id == VirtualManagerChat.bot_id)
    subquery = subquery.filter(subquery_vmc.is_deleted.is_(False))
    subquery = subquery.filter(subquery_vmc.started_datetime > VirtualManagerChat.started_datetime)

    query = query.filter(not_(subquery.exists()))

    expiration = VIRTUAL_MANAGER_CHAT_IS_EXPIRED_AFTER
    expired_condition = VirtualManagerChat.when_send_message > now - timedelta(seconds=expiration)
    query = query.order_by(expired_condition)
    query = query.order_by(desc(VirtualManagerChat.when_send_message))

    return query.all()
