import re

from psutils.text import replace_variables_in_text

import exceptions
from config import DEBUG
from core import messangers_adapters as ma
from core.group.functions import get_custom_fields
from core.kafka.producer.functions import build_and_send_bot_message
from db import crud
from db.models import (
    CustomField, Group, MediaObject, User, UserClientBotActivity, VirtualManager,
    VirtualManagerChat, VirtualManagerStep,
)
from loggers import JSONLogger
from schemas import (
    ChatMessageSentByEnum, ChatTypeEnum, MessageContentTypeEnum, VMButtonType,
    VMInteractiveType,
)
from utils.date_time import utcnow
from utils.text import f
from utils.translator import t
from ..interactives.context import VMInteractiveContext
from ..interactives.functions import process_vm_interactives
from ..interactives.processors.button.base import VMButton
from ...chat_message_sender import ChatMessageSender
from ...keyboards import get_menu_keyboard


async def get_vm_notify_text(notify_text: str, user: User, group: Group, lang: str):
    custom_fields = await get_custom_fields(user, group, lang)

    return await f(
        "service vm notify text", lang, text=notify_text, user_full_name=user.name,
        group_name=group.name, custom_fields=custom_fields
    )


async def replace_custom_fields_variables(
        user: User,
        group: Group,
        step_text: str,
        original_step_text: str = None
):
    custom_fields_variables = re.findall(
        r"\{var:[^<>\s]+}", step_text, flags=re.IGNORECASE
    )
    variables_origin = re.findall(
        r"\{var:[^<>\s]+}", original_step_text, flags=re.IGNORECASE
    ) if original_step_text else custom_fields_variables
    for variable_origin, variable in zip(variables_origin, custom_fields_variables):
        var_name = variable_origin.split(":")[-1][:-1]
        custom_field = await CustomField.get(name=var_name, group_id=group.id)
        value = await custom_field.get_last_value(user.id) if custom_field else ""
        step_text = step_text.replace(variable, value)
    return step_text


def replace_user_variables(user: User, step_text: str):
    variables = {
        "firstname": user.first_name,
        "lastname": user.last_name,
        "fullname": user.name,
        "username": f"@{user.username}" if user.username else user.name,
    }
    return replace_variables_in_text(step_text, variables)


async def replace_step_text_variables(
        user: User,
        group: Group,
        step_text: str,
        original_step_text: str = None
) -> str:
    step_text = replace_user_variables(user, step_text)
    step_text = await replace_custom_fields_variables(
        user, group, step_text, original_step_text
    )
    return step_text


async def translate_question_text(
        group: Group,
        lang: str,
        virtual_manager: VirtualManager,
        question_id: int,
) -> str:
    return await t(
        virtual_manager,
        lang, group.lang,
        field_name="questions",
        questions__return_key=question_id,
        group_id=group.id,
        is_auto_translate_allowed=group.is_translate,
    )


async def translate_vm_step_text(
        group: Group,
        lang: str,
        step: VirtualManagerStep,
):
    return await t(
        step, lang, group.lang,
        field_name="text",
        group_id=group.id,
        is_auto_translate_allowed=group.is_translate,
    )


async def send_translation_message(virtual_manager_chat_id: int):
    virtual_manager_chat = await VirtualManagerChat.get(virtual_manager_chat_id)
    now = utcnow()
    await virtual_manager_chat.update(when_send_message=now)


async def send_virtual_manager_message(
        vmc: VirtualManagerChat,
        is_repeated_message: bool = False,
        logger: JSONLogger | None = None
):
    if logger is None:
        logger = JSONLogger("vm")

    logger.add_texts("Send VM message")
    logger.add_data(
        {
            "vmc_id": vmc.id,
            "is_repeated_message": is_repeated_message,
        }
    )

    if (
            not vmc.continue_to_step_id and
            not vmc.when_send_message and
            not is_repeated_message
    ):
        logger.debug(
            "No message to send", {
                "continue_to_step_id": vmc.continue_to_step_id,
                "when_send_message": is_repeated_message,
            }
        )
        return

    vm, user, group, bot, step, step_media = await crud.get_vmc_data_for_send_message(
        vmc
    )
    if not vm:
        logger.error("VM not found")
        raise exceptions.VirtualManagerNotFoundError(vm)

    if not user:
        logger.error("User not found")
        raise exceptions.VirtualManagerNotFoundError(user)

    if not group:
        logger.error("Group not found")
        raise exceptions.VirtualManagerNotFoundError(group)

    if not step:
        logger.error("Step not found")
        await vmc.delete()
        raise exceptions.VirtualManagerNotFoundError(step)

    user_bot_activity = await UserClientBotActivity.get(user, bot)
    lang = await user.get_lang(bot)

    logger.add_data(
        {
            "vm_id": vm.id,
            "user_id": user.id,
            "group_id": group.id,
            "bot_id": bot.id,
            "step_id": step.id,
            "step_media": step_media,
            "user_bot_activity_id": user_bot_activity.id,
        }
    )

    actions_context = VMInteractiveContext(
        vm, vmc, bot, group, user, lang, logger,
    )

    step_text = await translate_vm_step_text(
        group, lang, step
    ) or step.text if group.is_translate else step.text

    if not step_text:
        step_text = ""

    if not is_repeated_message:
        await process_vm_interactives(
            step.id, VMInteractiveType.PRE_ACTION, actions_context, logger
        )
    elif DEBUG:
        logger.debug(
            f"{is_repeated_message=}, skipping {VMInteractiveType.PRE_ACTION}"
        )

    chat = await crud.get_or_create_chat(
        ChatTypeEnum.USER_WITH_GROUP,
        user.id, group.id, bot.id,
    )

    step_text = await replace_step_text_variables(
        user, group,
        step_text=step_text,
        original_step_text=step.text,
    )

    if "<flash>" in step_text.lower():
        step_text_parts = [
            text.strip() for text in
            re.split(r"<flash>", step_text, flags=re.IGNORECASE)
        ]
    else:
        step_text_parts = [step_text]

    db_buttons = await crud.get_vm_interactive_list(
        step.id, VMInteractiveType.BUTTON,
        with_translations=True,
        lang=lang,
    )

    is_url_keyboard = bot.bot_type == "whatsapp" and any(
        map(lambda x: x[0].subtype == "url", db_buttons)
    )
    buttons = []
    request_keyboard = None

    for db_button, translation in db_buttons:
        actions_context.interactive_translation = translation

        if is_url_keyboard and db_button.subtype != "url":
            continue

        button_interactive = VMButton.build(db_button, actions_context)
        button = await button_interactive.build_button(bot.bot_type)
        if not button:
            continue

        if db_button.subtype == VMButtonType.REQUEST.value:
            if not request_keyboard:
                request_keyboard = ma.tg.types.ReplyKeyboardMarkup(
                    resize_keyboard=True,
                    one_time_keyboard=True,
                )
            if button_interactive.params.button_add_new_row and len(
                    sum(request_keyboard, [])
            ):
                request_keyboard.row(button)
            else:
                request_keyboard.insert(button)
            continue

        if (not buttons or (
                isinstance(button_interactive, VMButton) and
                button_interactive.params.button_add_new_row and
                len(buttons[-1]) > 0
        )):
            buttons.append([])

        buttons[-1].append(button)
        if is_url_keyboard:
            break

    logger.add_data(
        {
            "is_url_keyboard": is_url_keyboard,
            "buttons": buttons,
            "bot_type": bot.bot_type,
            "step_text_parts": step_text_parts,
            "vmc": vmc,
            "step_media": step_media
        }
    )

    if is_url_keyboard:
        keyboard = ma.wa.types.UrlKeyboard(button=buttons[0][0])
    elif buttons:
        match bot.bot_type:
            case "telegram":
                keyboard = ma.tg.types.InlineKeyboardMarkup(inline_keyboard=buttons)
            case "whatsapp":
                buttons = sum(buttons, [])[:3]
                keyboard = ma.wa.types.ReplyKeyboard(buttons=buttons)
            case _:
                raise ValueError(f"Unsupported bot type: {bot.bot_type}")
    else:
        keyboard = None

    if bot.bot_type == "telegram" and len(step_text_parts) < 2 and keyboard:
        if request_keyboard:
            text = await f("vm menu set text", lang)
            menu_keyboard = request_keyboard
        elif vmc.last_msg_id_for_set_menu_in_vm:
            text = await f("vm menu set default text", lang)
            menu_keyboard = await get_menu_keyboard(user, bot, lang)
        else:
            text, menu_keyboard = None, None

        if text:
            if DEBUG:
                logger.debug(
                    "Sending message with request_keyboard", {
                        "text": text,
                        "keyboard": menu_keyboard,
                    }
                )
            await build_and_send_bot_message(
                bot.id, bot.display_name, bot.bot_type, bot.token,
                bot_wa_from=bot.whatsapp_from,
                tg_chat_id=user.chat_id,
                user_id=user.id,
                user_name=user.name,
                content_type="text",
                text=text,
                keyboard=menu_keyboard,
            )

    for index, step_text_part in enumerate(step_text_parts):
        media: MediaObject | None = None

        if index == 0 and step_media:
            media = step_media
            if media.media_type in ("image", "video"):
                content_type = MessageContentTypeEnum(media.media_type)
            else:
                content_type = MessageContentTypeEnum.DOCUMENT
        else:
            content_type = MessageContentTypeEnum.TEXT

        if index == len(step_text_parts) - 1 and keyboard:
            part_keyboard = keyboard
        elif bot.bot_type == "telegram":
            part_keyboard = request_keyboard or await get_menu_keyboard(
                user, bot, lang
            )
        else:
            part_keyboard = None

        if not step_text_part and not media and not part_keyboard:
            if DEBUG:
                logger.debug("Skipping part, no content to send")
            continue

        if content_type == MessageContentTypeEnum.TEXT and not step_text_part:
            step_text_part = "..."

        if DEBUG:
            logger.debug(
                "Sending step part", {
                    "content_type": content_type,
                    "step_text_part": step_text_part,
                    "part_keyboard": part_keyboard,
                    "media": media,
                }
            )

        await ChatMessageSender(
            chat, content_type,
            step_text_part, media,
            sent_by=ChatMessageSentByEnum.VM,
            vmc_id=vmc.id,
            group=group, bot=bot,
            chat_user=user,
            virtual_manager_chat=vmc,
            virtual_manager=vm,
            keyboard=part_keyboard,
            user_bot_activity=user_bot_activity,
        )

    await crud.vmc_message_sent(vmc, vm, step, user_bot_activity)

    if not is_repeated_message:
        await process_vm_interactives(
            step.id, VMInteractiveType.POST_ACTION, actions_context, logger
        )
    elif DEBUG:
        logger.debug(
            f"{is_repeated_message=}, skipping {VMInteractiveType.PRE_ACTION}"
        )
