from config import DEBUG, DELAY_BETWEEN_CHECK_VIRTUAL_MANAGERS_MESSAGES
from db import DBSession
from loggers import <PERSON><PERSON><PERSON>ogger
from utils.processes_manager.background_worker import <PERSON>BackgroundWorker
from .db_funcs import get_vmcs_to_send_message
from .functions import send_virtual_manager_message


class VmMessagesWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "vm messages"
    DEFAULT_TIMEOUT = DELAY_BETWEEN_CHECK_VIRTUAL_MANAGERS_MESSAGES

    async def iteration(self):
        with DBSession():

            while True:
                try:
                    chats_to_send = await get_vmcs_to_send_message()
                except Exception as e:
                    J<PERSON>NLogger("vm").error(
                        "An error occurred while retrieving chats to send", e
                    )
                    chats_to_send = None

                if not chats_to_send:
                    break

                for vmc in chats_to_send:
                    logger = JSONLogger(
                        "vm", {
                            "worker_data": {
                                "vm_id": vmc.virtual_manager_id,
                                "user_id": vmc.user_id,
                                "group_id": vmc.group_id,
                                "vmc_id": vmc.id,
                                "current_step_id": vmc.current_step_id,
                            }
                        }
                    )

                    try:
                        if DEBUG:
                            logger.debug("Sending")
                        await send_virtual_manager_message(vmc, logger=logger)
                    except Exception as e:
                        logger.error(e)
                        try:
                            await vmc.delete()
                        except Exception as e:
                            logger.error(
                                "Deleting VMChat error",
                                e
                            )
                    else:
                        logger.debug("Success")
