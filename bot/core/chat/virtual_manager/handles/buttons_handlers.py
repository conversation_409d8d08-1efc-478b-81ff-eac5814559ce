from typing import Type

from psutils.callback_data import CallbackData

from core import messangers_adapters as ma
from core.keyboards import get_bot_menu_keyboard
from db import crud
from db.models import ClientBot, User, UserClientBotActivity
from utils.text import f
from ..functions import (
    send_vm_button_answer_to_chat, vm_button_pre_process,
    vmc_answered,
)
from ..interactives import InlineButton
from ..interactives.context import VMInteractiveContext
from ..interactives.processors.button.inline.callback_data import \
    VMInlineButtonCallbackData
from ..reminders_sender.callback_data import (
    VMRemindCallbackData,
    VMStopReminderCallbackData,
)


@ma.handler.button("list", "reply")
async def vm_button_click_handler(
        query: ma.types.ButtonQuery,
        user: User,
        bot: ClientBot,
        user_bot_activity: UserClientBotActivity,
        data: VMInlineButtonCallbackData,
        lang: str,
        **kwargs,
):
    vmc, vm, vm_step, db_interactive, group, logger = await vm_button_pre_process(
        "Step button click", query,
        user, bot, user_bot_activity,
        data, data.vmc_id, interactive_id=data.vm_interactive_id,
    )

    if (not vm.allow_click_old_messages and vmc.current_step_id and vm_step.id !=
            vmc.current_step_id):
        logger.debug("Old message, returning")
        return

    context = VMInteractiveContext(
        vm, vmc, bot, group,
        user, lang, logger, user_bot_activity
    )

    interactive = InlineButton.build(db_interactive, context)
    result = await interactive.execute(
        query, user=user, bot=bot, data=data, lang=lang, **kwargs
    )

    button_text = await interactive.button_text

    await send_vm_button_answer_to_chat(
        button_text, vm, vmc,
        user, bot, user_bot_activity,
        group,
    )

    await vmc_answered(
        vmc, vm,
        user, bot,
        vm_step,
        user_bot_activity,
        result.vm_next_step_id,
        result.stop_vm,
        result.pause_vm,
        started_vmc_id=result.started_vmc_id,
    )


@ma.handler.button("list", "reply")
async def vm_remind_button_click_handler(
        query: ma.types.ButtonQuery,
        user: User,
        bot: ClientBot,
        user_bot_activity: UserClientBotActivity,
        data: VMRemindCallbackData,
        lang: str,
):
    vmc, vm, vm_step, _, group, logger = await vm_button_pre_process(
        "Remind button click", query,
        user, bot, user_bot_activity,
        data, data.vmc_id, step_id=data.step_id,
    )

    button_text = await f("vm repeat last message button", lang)
    await send_vm_button_answer_to_chat(
        button_text, vm, vmc,
        user, bot, user_bot_activity,
        group,
    )

    await vmc_answered(
        vmc, vm,
        user, bot,
        vm_step,
        user_bot_activity,
        vm_step.id,
        is_answer_for_remind=True,
        send_next_message_delay=0,
    )


@ma.handler.button("list", "reply")
async def vm_stop_reminder_button_click_handler(
        query: ma.types.ButtonQuery,
        user: User,
        bot: ClientBot,
        user_bot_activity: UserClientBotActivity,
        data: VMRemindCallbackData,
        lang: str,
):
    vmc, vm, vm_step, _, group, logger = await vm_button_pre_process(
        "Stop reminder button click", query,
        user, bot, user_bot_activity,
        data, data.vmc_id, step_id=data.step_id,
    )

    button_text = await f("vm not repeat last message button", lang)
    await send_vm_button_answer_to_chat(
        button_text, vm, vmc,
        user, bot, user_bot_activity,
        group,
    )

    keyboard = await get_bot_menu_keyboard(user, bot, lang)
    await query.message.answer(
        await f("vm reminders stopped", lang), reply_markup=keyboard
    )

    await crud.reset_vm_reminder(user_bot_activity)


def register_vm_buttons_handlers(dp: ma.DispatcherType):
    def setup_handler(
            handler: ma.handler.HandlerAdapter, callback_data_cls: Type[CallbackData]
    ):
        handler.setup(
            dp,
            callback_data_cls.get_filter("data"),
            state="*",
            messangers_kwargs={
                "telegram": {
                    "chat_type": "private"
                }
            }
        )

    setup_handler(vm_button_click_handler, VMInlineButtonCallbackData)
    setup_handler(vm_remind_button_click_handler, VMRemindCallbackData)
    setup_handler(vm_stop_reminder_button_click_handler, VMStopReminderCallbackData)
