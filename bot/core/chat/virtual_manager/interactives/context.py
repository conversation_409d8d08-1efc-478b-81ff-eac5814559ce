from dataclasses import dataclass

from db.models import (
    Brand, ClientBot, Group, Translation, User, UserClientBotActivity, VirtualManager,
    VirtualManagerChat,
)
from loggers import JSONLogger


@dataclass
class VMInteractiveContext:
    vm: VirtualManager
    vmc: VirtualManagerChat
    bot: ClientBot
    group: Group
    user: User
    lang: str
    logger: JSONLogger
    user_bot_activity: UserClientBotActivity | None = None
    brand: Brand | None = None
    interactive_translation: Translation | None = None
