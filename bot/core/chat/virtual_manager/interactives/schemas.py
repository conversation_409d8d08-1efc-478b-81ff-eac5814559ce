from typing import TypeAlias, Union

from core.chat.virtual_manager.interactives import VMInteractive
from schemas import (
    AdminModifyVirtualManagerStepData,
    VMInlineButtonParams, VMInteractiveType,
    VirtualManagerStepSchema,
)

not_button_actions_modify_models = VMInteractive.get_all_models(
    exclude_type=VMInteractiveType.BUTTON_ACTION,
    id_mode="optional",
    id_type="str_int",
    model_prefix="AdminModify",
)
not_button_actions_models = VMInteractive.get_all_models(
    exclude_type=VMInteractiveType.BUTTON_ACTION,
    id_mode="required",
    id_type="int",
)

button_actions_models = VMInteractive.get_all_models(
    include_type=VMInteractiveType.BUTTON_ACTION,
    id_mode="disabled",
)

# noinspection PyTypeHints
AdminModifyVirtualManagerInteractiveUnionSchema: TypeAlias = Union[tuple(not_button_actions_modify_models.values())]

# noinspection PyTypeHints
VirtualManagerInteractiveUnionSchema: TypeAlias = Union[tuple(not_button_actions_models.values())]

# noinspection PyTypeHints
VirtualManagerInlineButtonActionParamsSchema: TypeAlias = Union[tuple(button_actions_models)]

VMInlineButtonParams.update_forward_refs(
    VirtualManagerInlineButtonActionParamsSchema=VirtualManagerInlineButtonActionParamsSchema,
    **button_actions_models
)

AdminModifyVirtualManagerStepData.update_forward_refs(
    AdminModifyVirtualManagerInteractiveUnionSchema=AdminModifyVirtualManagerInteractiveUnionSchema,
    **not_button_actions_modify_models
)

VirtualManagerStepSchema.update_forward_refs(
    VirtualManagerInteractiveUnionSchema=VirtualManagerInteractiveUnionSchema,
    **not_button_actions_models
)

VirtualManagerInteractiveDefinitionSchema = VMInteractive.get_all_definition_model()
