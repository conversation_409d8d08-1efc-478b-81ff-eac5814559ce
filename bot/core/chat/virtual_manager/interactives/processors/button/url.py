from core import messangers_adapters as ma

from schemas import BotTypeLiteral, VMButtonType, VMInteractiveType, VMUrlButtonParams
from .base import VMButton


class UrlButton(VMButton[VMUrlButtonParams]):
    type = VMInteractiveType.BUTTON
    subtype = VMButtonType.URL.value
    params_model = VMUrlButtonParams

    full_width_params = (
        "button_text",
        "url",
    )

    async def execute(self):
        """There is nothing to execute for this button"""
        pass

    async def build_button(self, bot_type: BotTypeLiteral):
        match bot_type:
            case "telegram":
                button_params = {}
                if self.params.url_as_webapp_tg:
                    button_params["web_app"] = ma.tg.types.WebAppInfo(
                        url=self.params.url,
                    )
                else:
                    button_params["url"] = self.params.url

                return ma.tg.types.InlineKeyboardButton(
                    await self.button_text, **button_params
                )
            case "whatsapp":
                return ma.wa.types.UrlButton(
                    display_text=await self.button_text,
                    url=self.params.url,
                )
            case _:
                raise ValueError(f"Unsupported bot type: {bot_type}")
