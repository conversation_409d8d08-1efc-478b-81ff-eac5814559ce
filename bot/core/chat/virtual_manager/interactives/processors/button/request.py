from core import messangers_adapters as ma

from schemas import (
    BotTypeLiteral, VMButtonType, VMInteractiveType,
    VMRequestButtonParams, VMRequestType,
)
from .base import VMButton


class RequestButton(VMButton[VMRequestButtonParams]):
    type = VMInteractiveType.BUTTON
    subtype = VMButtonType.REQUEST.value
    params_model = VMRequestButtonParams

    full_width_params = (
        "button_text",
        "request_type",
    )

    async def execute(self):
        """There is nothing to execute for this button"""
        pass

    async def build_button(self, bot_type: BotTypeLiteral):
        match bot_type:
            case "telegram":
                if self.params.request_type == VMRequestType.PHONE:
                    request_type = "contact"
                else:
                    request_type = self.params.request_type.value

                return ma.tg.types.KeyboardButton(
                    await self.button_text, **{
                        f"request_{request_type}": True
                    }
                )
            case "whatsapp":
                return
            case _:
                raise ValueError(f"Unsupported bot type: {bot_type}")
