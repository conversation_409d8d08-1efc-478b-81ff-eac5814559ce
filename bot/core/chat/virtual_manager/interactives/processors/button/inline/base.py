from psutils.func import check_function_spec

from core import messangers_adapters as ma
from schemas import BotTypeLiteral, VMInlineButtonParams, VMInteractiveType
from .callback_data import VMInlineButtonCallbackData
from ..base import VMButton, VMInteractive
from ...results import VMAnsweredInteractivesResult


class InlineButton(VMButton[VMInlineButtonParams]):
    type = VMInteractiveType.BUTTON
    subtype = "inline"
    params_model = VMInlineButtonParams

    full_width_params = (
        "button_text",
        "button_add_new_row",
        "button_actions",
    )

    def __init_subclass__(cls, **kwargs):
        if cls.params_model is None:
            raise TypeError("Class variable params_model is required for InlineButton")
        if not isinstance(cls.params_model, type) or not issubclass(
                cls.params_model, VMInlineButtonParams
        ):
            raise TypeError(
                "Class variable params_model has to be subclass of "
                "VMInlineButtonParams for InlineButton"
            )

        super().__init_subclass__(**kwargs)

    async def execute(self, query: ma.types.ButtonQuery, **kwargs):
        result = VMAnsweredInteractivesResult()

        for action_data in self.params.button_actions:
            action_cls = VMInteractive.detect_interactive(
                action_data.type, action_data.subtype
            )
            action = action_cls(self.interactive, self.context, action_data.params)
            safe_kwargs = check_function_spec(
                action.execute, {
                    **kwargs,
                    "result": result
                }
            )
            action_result = await action.execute(query, **safe_kwargs)
            result.results_list.append(action_result)

        return result

    async def build_button(self, bot_type: BotTypeLiteral):
        button_data = VMInlineButtonCallbackData(
            vm_interactive_id=self.interactive.id,
            vmc_id=self.context.vmc.id,
        ).to_str()

        match bot_type:
            case "telegram":
                return ma.tg.types.InlineKeyboardButton(
                    await self.button_text, callback_data=button_data
                )
            case "whatsapp":
                return ma.wa.types.ReplyButton(
                    id=button_data, title=await self.button_text
                )
            case _:
                raise ValueError(f"Unsupported bot_type: {bot_type}")
