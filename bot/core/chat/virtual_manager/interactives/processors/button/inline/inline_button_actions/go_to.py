from schemas import VMGoToParams
from .base import VMInlineButtonAction
from ....helpers import process_go_to
from ....results import VMAnsweredInteractivesResult


class GoToButtonAction(VMInlineButtonAction[VMGoToParams]):
    subtype = "go_to"
    params_model = VMGoToParams

    full_width_params = (
        "vm_id",
        "vm_step_id",
    )

    async def execute(self, _, result: VMAnsweredInteractivesResult):
        return await process_go_to(self, result)
