from db.models import CustomField, CustomFieldValue, User
from schemas import SetVarValueActionParams
from .base import VMInlineButtonAction


class SetVarButtonAction(VMInlineButtonAction[SetVarValueActionParams]):
    subtype = "set_var"
    params_model = SetVarValueActionParams

    async def execute(self, _, user: User):
        field = await CustomField.get_or_create(self.context.group, self.params.var_name)
        await CustomFieldValue.set(user, field, self.params.var_value, vm=self.context.vm)
