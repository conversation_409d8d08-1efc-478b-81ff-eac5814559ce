from core import messangers_adapters as ma
from core.bot.profile.functions import send_bot_profile
from db.models import ClientBot, User

from .base import VMInlineButtonAction


class ProfileButtonAction(VMInlineButtonAction):
    subtype = "profile"

    async def execute(self, query: ma.types.ButtonQuery, bot: ClientBot, user: User, lang: str):
        await send_bot_profile(query, bot, user, lang)
