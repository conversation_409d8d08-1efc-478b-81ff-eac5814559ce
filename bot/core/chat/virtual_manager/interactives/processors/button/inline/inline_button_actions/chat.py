from core.chat import user_to_chat
from schemas import ChatType<PERSON><PERSON>
from .base import VMInlineButtonAction


class ChatButtonAction(VMInlineButtonAction):
    subtype = "chat"

    async def execute(self, _):
        await self.context.vmc.delete()
        await user_to_chat(
            ChatTypeEnum.USER_WITH_GROUP,
            user=self.context.user, group_id=self.context.vmc.group_id,
            need_get_vm_from_history=False,
        )
