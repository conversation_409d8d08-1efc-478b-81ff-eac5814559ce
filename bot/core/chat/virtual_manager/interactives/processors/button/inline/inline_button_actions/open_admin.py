from pydantic import BaseModel, Field

from config import ACCESS_TOKEN_EXPIRES, ADMIN_HOST
from core import messangers_adapters as ma
from core.auth.functions import create_user_access_token
from db.models import ClientBot, User
from utils.text import f
from .base import VMInlineButtonAction
from ....results import VMAnsweredInteractivesResult


class OpenAdminParams(BaseModel):
    path: str | None = Field(None, nullable=True)


class OpenAdminButtonAction(VMInlineButtonAction[OpenAdminParams]):
    subtype = "open_admin"
    params_model = OpenAdminParams

    async def execute(
            self, query: ma.types.ButtonQuery,
            state: ma.FSMContext,
            bot: ClientBot,
            user: User,
            lang: str,
            result: VMAnsweredInteractivesResult,
    ):
        url = f"{ADMIN_HOST}/{lang}/{self.params.path}"
        # перевірка чи в урл ще немає параметрів, виключаючи, коли останній символ ?
        if "?" in url[-1] and not url.endswith("&"):
            url += "&"
        elif not url.endswith("?"):
            url += "?"

        token = create_user_access_token(user.id, ACCESS_TOKEN_EXPIRES)
        url += f"token={token}"

        keyboard = ma.InlineKeyboard(
            buttons=[ma.UrlKeyboardButton(
                await f("vm open admin button", lang),
                url=url
            )]
        )

        if bot.bot_type == "telegram":
            keyboard = keyboard.to_telegram(url_as_web_app=False)
        else:
            keyboard = getattr(keyboard, f"to_{bot.bot_type}")()

        answer_obj = ma.detect_answer_obj(query)
        await answer_obj.answer(
            await f("vm open admin message", lang),
            reply_markup=keyboard
        )

        result.pause_vm = True
