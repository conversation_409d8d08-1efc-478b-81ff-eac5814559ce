from pydantic import BaseModel

from core import messangers_adapters as ma

from core.external_coupon.handlers import vm_incust_coupon_united_handler
from core.keyboards import get_bot_menu_keyboard
from core.messangers_adapters import FSMContext
from db.models import ClientBot, User
from .base import VMInlineButtonAction


class IncustCouponButtonParams(BaseModel):
    incust_coupon_code: str


class IncustCouponButtonAction(VMInlineButtonAction[IncustCouponButtonParams]):
    subtype = "incust_coupon"
    params_model = IncustCouponButtonParams

    async def execute(
            self, query: ma.types.ButtonQuery,
            state: FSMContext,
            bot: ClientBot,
            user: User,
            lang: str,
    ):
        answer_obj = ma.detect_answer_obj(query)
        keyboard = await get_bot_menu_keyboard(user, bot, lang)
        await vm_incust_coupon_united_handler(
            answer_obj, state, user,
            bot, lang, keyboard,
            self.params.incust_coupon_code,
        )
