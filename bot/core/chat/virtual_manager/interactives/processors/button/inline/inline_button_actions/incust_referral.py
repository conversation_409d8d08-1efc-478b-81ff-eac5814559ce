from core import messangers_adapters as ma
from core.incust_referral.functions import is_incust_referral_active

from core.referral.functions import share_and_earn
from db.models import Brand, ClientBot, User
from utils.text import f
from .base import VMInlineButtonAction


class IncustReferralButtonAction(VMInlineButtonAction):
    subtype = "incust_referral"

    async def execute(
        self, query: ma.types.ButtonQuery,
        bot: ClientBot, user: User,
        lang: str
    ):
        answer_obj = query.message if isinstance(
            query, ma.tg.types.CallbackQuery
        ) else query
        if not self.context.brand:
            self.context.brand = await Brand.get(group_id=self.context.group.id)

        is_referral_active = await is_incust_referral_active(
            user, self.context.brand, lang
        )
        if not is_referral_active:
            return await answer_obj.answer(
                await f("loyalty referral system is not enabled", lang)
            )

        await share_and_earn(user, self.context.brand, lang, bot)
