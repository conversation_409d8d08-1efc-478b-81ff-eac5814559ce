from core import messangers_adapters as ma

from core.wallet.functions import wallet_show
from db.models import Brand, ClientBot, User
from .base import VMInlineButtonAction


class IncustWalletButtonAction(VMInlineButtonAction):
    subtype = "incust_wallet"

    async def execute(self, query: ma.types.ButtonQuery, bot: ClientBot, user: User, lang: str):
        answer_obj = query.message if isinstance(query, ma.tg.types.CallbackQuery) else query
        if not self.context.brand:
            self.context.brand = await Brand.get(group_id=self.context.group.id)
        await wallet_show(user, self.context.brand, lang, answer_obj, bot)
