from abc import ABC, abstractmethod

from schemas import VMInteractiveType
from ..base import VMInteractive
from ...base import ParamsModelT


class VMInlineButtonAction(VMInteractive[ParamsModelT], ABC):
    type = VMInteractiveType.BUTTON_ACTION

    @abstractmethod
    async def execute(self, *args, **kwargs):
        """

        @param args: always will be one arg: ma.types.ButtonQuery
        @param kwargs: kwargs from handler. Will be passed only kwargs that
        implemented method accepts
        @return:
        """
        raise NotImplementedError
