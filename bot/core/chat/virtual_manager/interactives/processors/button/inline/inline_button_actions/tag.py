from pydantic import BaseModel

from db import crud
from db.models import User
from .base import VMInlineButtonAction


class TagActionParams(BaseModel):
    tag_name: str


class TagAddButtonAction(VMInlineButtonAction[TagActionParams]):
    subtype = "tag_add"
    params_model = TagActionParams

    async def execute(self, _, user: User):
        await crud.update_user_tags_in_group(
            user,
            self.context.group.id,
            add_tags=[self.params.tag_name],
        )


class TagRemoveButtonAction(VMInlineButtonAction[TagActionParams]):
    subtype = "tag_remove"
    params_model = TagActionParams

    async def execute(self, _, user: User):
        await crud.update_user_tags_in_group(
            user,
            self.context.group.id,
            remove_tags=[self.params.tag_name],
        )
