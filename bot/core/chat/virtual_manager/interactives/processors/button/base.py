from abc import ABC, abstractmethod
from typing import <PERSON>Var

from db.models import Translation
from schemas import BotTypeLiteral, VMButtonParams, VMInteractiveType
from utils.translator import t
from ..base import VMInteractive

ParamsModelT = TypeVar("ParamsModelT", bound=VMButtonParams)


class VMButton(VMInteractive[ParamsModelT], ABC):
    type = VMInteractiveType.BUTTON

    @property
    async def button_text(self):
        group = self.context.group

        if not group.is_translate:
            return self.params.button_text

        if not self.context.interactive_translation:
            self.context.interactive_translation = await Translation.get_by_obj(
                self.interactive, self.context.lang
            )

        return await t(
            self.interactive, self.context.lang, group.lang,
            field_name="params",
            params__return_key="button_text",
            group_id=group.id,
            is_auto_translate_allowed=group.is_translate,
            translation=self.context.interactive_translation,
        )

    @abstractmethod
    async def build_button(self, bot_type: BotTypeLiteral):
        raise NotImplementedError
