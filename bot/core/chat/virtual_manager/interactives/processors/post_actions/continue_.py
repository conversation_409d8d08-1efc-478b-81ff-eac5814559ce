from core.chat.virtual_manager.functions import start_virtual_manager_chat
from db import crud
from db.models import VirtualManagerStep
from schemas import VMGoToParams
from .base import VMPostAction


class ContinueActionParams(VMGoToParams):
    vm_continue_delay: int = 5


class ContinueAction(VMPostAction[ContinueActionParams]):
    subtype = "continue"
    params_model = ContinueActionParams

    full_width_params = (
        "vm_id",
        "vm_step_id",
    )

    async def execute(self):
        if self.params.vm_id and self.params.vm_id != self.context.vm.id:
            await start_virtual_manager_chat(
                self.context.user,
                self.params.vm_id,
                self.context.group,
                self.context.bot,
                on_start_delay=self.params.vm_continue_delay,
                step=self.params.vm_step_id,
            )
        else:
            step = await VirtualManagerStep.get(
                self.params.vm_step_id,
                virtual_manager_id=self.context.vm.id,
                is_deleted=False,
            )
            if not step:
                return

            await crud.vmc_set_continue(
                self.context.vmc, self.params.vm_continue_delay, self.params.vm_step_id
            )
