import enum
import inspect
from abc import ABC, abstractmethod
from collections import defaultdict
from types import NoneType
from typing import Any, Generic, Iterable, Literal, Type, TypeVar, Union

from psutils.text import snake_case_to_paschal_case
from psutils.type_vars import T
from psutils.undefined import Undefined
from pydantic import BaseModel, Field, create_model

from db.models import VirtualManagerInteractive
from schemas import BaseORMModel, VMInteractiveType
from core.chat.virtual_manager.interactives.context import VMInteractiveContext
from utils.translator.models import VirtualManagerInteractiveTranslatorModel

ParamsModelT = TypeVar('ParamsModelT', bound=BaseModel)


class VMInteractive(ABC, Generic[ParamsModelT]):
    _models: dict[str, Type[BaseModel]] = {}
    _translations_model: Type[BaseModel] | None = None
    _definition_params_models: list[Type[BaseModel]] | None = None
    _definition_model: Type[BaseModel] | None = None
    _definition_params_types: list[dict[str, Any]] | None = None
    _definition: BaseModel | None = None
    _all_definitions_model: Type[BaseModel] | None = None
    _all_definitions: BaseModel | None = None
    _interactives: dict[VMInteractiveType, dict[str, Type["VMInteractive"]]] = defaultdict(dict)

    type: VMInteractiveType
    subtype: str
    params_model: Type[ParamsModelT] | None = None

    full_width_params: Iterable[str] | None = None

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)

        if inspect.isabstract(cls):
            return

        cls._models = {}

        if not hasattr(cls, "type"):
            raise TypeError("Class variable 'type' must be defined!")
        if not isinstance(cls.type, VMInteractiveType):
            raise TypeError("Class variable 'type' must be an instance of VMInteractiveType!")

        if not hasattr(cls, "subtype"):
            raise TypeError("Class variable 'subtype' must be defined!")
        if not isinstance(cls.subtype, str):
            raise TypeError("Class variable 'subtype' must be a string!")

        if cls.params_model is not None and (
                not isinstance(cls.params_model, type) or not issubclass(cls.params_model, BaseModel)):
            raise TypeError("Class variable 'params_model' must be either subclass of pydantic.BaseModel or None!")

        cls._interactives[cls.type][cls.subtype] = cls

    def __init__(
            self, interactive: VirtualManagerInteractive,
            context: VMInteractiveContext,
            params: dict[str, Any] | None = Undefined
    ):
        self.interactive = interactive
        self.context = context
        self.params: ParamsModelT | None = self.validate_params(interactive.params if params is Undefined else params)

    @classmethod
    def validate_params(cls, params: dict[str, Any] | None = None) -> ParamsModelT | None:
        if cls.params_model is None and params:
            raise ValueError(
                f"params_model is not defined for {cls.__name__}. "
                f"Either define params_model or leave params empty!"
            )

        if cls.params_model is not None:
            if isinstance(params, cls.params_model):
                return params

            if not isinstance(params, dict):
                raise ValueError(
                    f"params_model is defined for {cls.__name__}. "
                    f"Argument params must be either dict, not {type(params)}!"
                )

        if params is not None:
            # noinspection PyCallingNonCallable
            return cls.params_model(**params)  # type: ignore
        else:
            return params

    @classmethod
    def detect_interactive(
            cls: Type[T],
            type_: VMInteractiveType | str,
            subtype: str,
    ) -> Type[T]:
        if isinstance(type_, str):
            type_ = VMInteractiveType(type_)

        if not isinstance(type_, VMInteractiveType):
            raise ValueError(
                f"Argument type_ must be instance "
                f"of VMInteractiveType or "
                f"string (value of it), not {type(type_)}!"
            )

        if hasattr(cls, "type") and cls.type != type_:
            raise ValueError("When calling detect_interactive for class with type, type_ must be the same as in class!")

        if not isinstance(subtype, str):
            raise ValueError(f"Argument subtype must be a string, not {type(subtype)}!")

        if hasattr(cls, "subtype") and cls.subtype != subtype:
            raise ValueError(
                "When calling detect_interactive for class with subtype, subtype must be the same as in class!"
            )

        if type_ not in cls._interactives:
            raise KeyError(f"There is not interactive with type {type_.value}")

        if subtype not in cls._interactives[type_]:
            raise KeyError(f"Unknown subtype: {subtype} for type {type_.value}!")

        return cls._interactives[type_][subtype]

    @classmethod
    def build(
            cls: Type[T],
            interactive: VirtualManagerInteractive,
            context: VMInteractiveContext,
            params: dict[str, Any] | None = Undefined
    ) -> T:
        interactive_cls = cls.detect_interactive(interactive.type, interactive.subtype)
        return interactive_cls(interactive, context, params)

    @abstractmethod
    async def execute(self, *args, **kwargs):
        """Executes interactive. Can return something for some interactive types (button, for example)"""
        raise NotImplementedError

    def __call__(self, *args, **kwargs):
        return self.execute(*args, **kwargs)

    @classmethod
    def build_translations_model(cls):
        if cls._translations_model:
            return cls._translations_model

        params_types = cls.get_definition_params_types()
        is_translations = any(map(lambda x: x["translations"] == "enabled", params_types))
        if not is_translations:
            return None

        params_translations_model = create_model(
            f"{cls.__name__}ParamsTranslationsSchema",
            **{
                param["name"]: (str | None, Field(None, nullable=True))
                for param in params_types
                if param["translations"] == "enabled"
            }
        )

        cls._translations_model = create_model(
            f"{cls.__name__}TranslationsSchema",
            params=(params_translations_model | None, Field(None, nullable=True))
        )

        return cls._translations_model

    @classmethod
    def build_model(
            cls,
            id_mode: Literal["optional", "required", "disabled"] = "optional",
            id_type: Literal["str_int", "str", "int"] = "int",
            model_prefix: str | None = None,
    ) -> Type[BaseModel]:
        model_key = f"{id_mode}-{model_prefix}"

        if model_key in cls._models:
            return cls._models[model_key]

        if inspect.isabstract(cls):
            raise TypeError("Method build_model is not available for abstract models")

        kwargs = {}
        if id_mode != "disabled":
            id_type = int | str if id_type == "str_int" else str if id_type == "str" else int
            kwargs["id"] = (id_type, ...) if id_mode == "required" else (id_type | None, Field(None, nullable=True))

        translations_model = cls.build_translations_model()

        base_model_name = (model_prefix or "") + cls.__name__
        # noinspection PyTypeHints
        cls._models[model_key] = create_model(
            f"{base_model_name}Schema",
            **kwargs,
            __base__=BaseORMModel,
            type=(Literal[cls.type.value], ...),
            subtype=(Literal[cls.subtype], ...),
            params=(cls.params_model, ...) if cls.params_model else (NoneType, Field(None, nullable=True)),
            translations=(
                (dict[str, translations_model | None] | None, Field(
                    default=None,
                    nullable=True,
                    description=(
                        "Translations for different languages. "
                        "The key is language ISO code and "
                        "the value is fields translations object"
                    )
                ))
                if translations_model else
                (NoneType, Field(None, nullable=True))
            )
        )
        return cls._models[model_key]

    @classmethod
    def get_all_models(
            cls,
            include_type: VMInteractiveType | None = None,
            exclude_type: VMInteractiveType | None = None,
            id_mode: Literal["optional", "required", "disabled"] = "optional",
            id_type: Literal["str_int", "str", "int"] = "int",
            model_prefix: str | None = None,
    ):
        models = {}

        if include_type:
            subtypes_list = [cls._interactives[include_type]]
        elif exclude_type:
            subtypes_list = [subtypes for type, subtypes in cls._interactives.items() if type != exclude_type]
        else:
            subtypes_list = cls._interactives.values()

        for subtypes in subtypes_list:
            for item in subtypes.values():
                model = item.build_model(id_mode, id_type, model_prefix)
                models[model.__name__] = model
        return models

    @classmethod
    def get_definition_params_types(cls):
        if cls._definition_params_types is not None:
            return cls._definition_params_types

        cls._definition_params_types = []

        if not cls.params_model:
            return cls._definition_params_types

        for name, field in cls.params_model.__fields__.items():
            param = {
                "name": name,
                "required": field.required,
                "field_width": (
                    "full"
                    if cls.full_width_params and name in cls.full_width_params else
                    "auto"
                ),
                "translations": (
                    "enabled"
                    if name in VirtualManagerInteractiveTranslatorModel.params.type.keys else
                    "disabled"
                ),
            }

            if field.name == "button_actions":
                param["type"] = "button_actions"
            elif field.name == "vm_step_id":
                param["type"] = "select_vm_step"
            elif field.name == "vm_id":
                param["type"] = "select_vm"
            elif issubclass(field.type_, enum.Enum):
                param["type"] = "select_value"
                param["values"] = tuple(el.value for el in field.type_)
            elif "delay" in field.name:
                param["type"] = "timedelta"
            elif field.type_.__name__ in ("int", "str", "bool", "float"):
                mapping = {
                    "int": "integer",
                    "str": "string",
                    "bool": "boolean",
                    "float": "float"
                }
                param["type"] = mapping[field.type_.__name__]
            else:
                raise ValueError(f"Unsupported field {name}!")

            if field.default:
                param["default"] = field.default.value if isinstance(field.default, enum.Enum) else field.default

            cls._definition_params_types.append(param)

        return cls._definition_params_types

    # noinspection PyTypeHints
    @classmethod
    def get_definition_params_models(cls):
        if cls._definition_params_models is not None:
            return cls._definition_params_models

        params = cls.get_definition_params_types()

        cls._definition_params_models = []

        if not params:
            return cls._definition_params_models

        for param in params:
            model_kwargs = {
                "name": (Literal[param["name"]], ...),
                "type": (Literal[param["type"]], ...),
                "required": (Literal[param["required"]], ...),
                "default": (Literal[param["default"]], ...) if "default" in param else (
                    NoneType, Field(None, nullable=True)),
                "field_width": (Literal[param["field_width"]], ...),
                "translations": (Literal[param["translations"]], ...),
            }
            if "values" in param:
                model_kwargs["values"] = (list[Literal[param["values"]]], Field(
                    min_items=len(param["values"]),
                    max_items=len(param["values"]),
                ))

            param_name_paschal = snake_case_to_paschal_case(param["name"])

            param_model = create_model(
                f"{cls.__name__}{param_name_paschal}ParamDefinition",
                **model_kwargs,
            )

            cls._definition_params_models.append(param_model)

        return cls._definition_params_models

    @classmethod
    def get_definition_model(cls):
        if cls._definition_model:
            return cls._definition_model

        params_models = cls.get_definition_params_models()

        # noinspection PyTypeHints
        cls._definition_model = create_model(
            f"VirtualManagerInteractive{cls.__name__}DefinitionSchema",
            __base__=BaseORMModel,
            type=(Literal[cls.type.value], ...),
            subtype=(Literal[cls.subtype], ...),
            params=(
                (list[Union[tuple(params_models)]], ...)
                if params_models else
                (NoneType, Field(None, nullable=True))
            )
        )
        return cls._definition_model

    @classmethod
    def build_definition(cls):
        if cls._definition:
            return cls._definition

        model = cls.get_definition_model()

        cls._definition = model(
            type=cls.type.value,
            subtype=cls.subtype,
            params=cls.get_definition_params_types() or None,
        )
        return cls._definition

    @classmethod
    def build_all_definitions(cls):
        if cls._all_definitions:
            return cls._all_definitions

        definitions = []
        params = []

        for subtypes in cls._interactives.values():
            for item in subtypes.values():
                params.extend(item.get_definition_params_types())
                definitions.append(item.build_definition())

        model = cls.get_all_definition_model()
        cls._all_definitions = model(interactives=definitions, params=params)
        return cls._all_definitions

    @classmethod
    def get_all_definition_model(cls):
        if cls._all_definitions_model:
            return cls._all_definitions_model

        models = []
        params_models = []

        for type, subtypes in cls._interactives.items():
            for item in subtypes.values():
                params_models.extend(item.get_definition_params_models())
                models.append(item.get_definition_model())

        cls._all_definitions_model = create_model(
            "VirtualManagerInteractiveDefinitionSchema",
            interactives=(list[Union[tuple(models)]], ...),
            params=(list[Union[tuple(params_models)]], ...),
        )

        return cls._all_definitions_model
