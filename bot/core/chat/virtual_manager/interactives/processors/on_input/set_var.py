from core import messangers_adapters as ma
from core.geo import get_location
from db.models import Custom<PERSON>ield, CustomFieldValue
from schemas import SetVarActionParams

from .base import VMOnInput


class SetVarOnInputAction(VMOnInput[SetVarActionParams]):
    subtype = "set_var"
    params_model = SetVarActionParams

    async def execute(self, message: ma.Message):
        field = await CustomField.get_or_create(
            self.context.group, self.params.var_name
        )
        if message.content_type == "contact":
            value = message.contact.phone_number
        elif message.content_type == "location":
            value = await get_location(message, self.context.group.lang)
        else:
            value = message.caption or message.text

        await CustomFieldValue.set(self.context.user, field, value, vm=self.context.vm)
