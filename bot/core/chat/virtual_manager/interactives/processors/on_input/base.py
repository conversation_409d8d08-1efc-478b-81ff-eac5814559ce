from abc import ABC, abstractmethod

from schemas import VMInteractiveType
from ..base import ParamsModelT, VMInteractive


class VMOnInput(VMInteractive[ParamsModelT], ABC):
    type = VMInteractiveType.ON_INPUT

    @abstractmethod
    async def execute(self, *args, **kwargs):
        """
        @param args: always will be one arg: ma.types.Message
        @param kwargs: kwargs from handler. Will be passed only kwargs that
        implemented method accepts
        @return:
        """
        raise NotImplementedError
