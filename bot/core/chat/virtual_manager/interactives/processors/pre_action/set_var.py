from db.models import CustomField, CustomFieldValue
from schemas import SetVarValueActionParams
from .base import VMPreAction


class SetVarAction(VMPreAction[SetVarValueActionParams]):
    subtype = "set_var"
    params_model = SetVarValueActionParams

    async def execute(self):
        field = await CustomField.get_or_create(self.context.group, self.params.var_name)
        await CustomFieldValue.set(self.context.user, field, self.params.var_value, vm=self.context.vm)
