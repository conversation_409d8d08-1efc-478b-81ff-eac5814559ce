from core.crm_ticket.functions import set_crm_ticket_status, vm_create_crm_ticket
from db import crud
from schemas import (
    CRMTicketStatusEnum, CRMTicketStatusInitiatedByEnum, VMTicketCreateActionParams,
    VMTicketSetStatusActionParams,
)
from .base import VMPreAction


class TicketCreateAction(VMPreAction[VMTicketCreateActionParams]):
    subtype = "ticket_create"
    params_model = VMTicketCreateActionParams

    full_width_params = (
        "ticket_title",
    )

    async def execute(self):
        await vm_create_crm_ticket(
            self.context.user, self.context.bot, self.context.vmc,
            self.params.ticket_title,
            self.params.create_ticket_if_not_exists,
        )


class TicketSetStatusAction(VMPreAction[VMTicketSetStatusActionParams]):
    subtype = "ticket_set_status"
    params_model = VMTicketSetStatusActionParams

    full_width_params = (
        "ticket_title",
        "ticket_status",
    )

    async def execute(self):
        ticket = await crud.get_crm_ticket_for_user(
            self.context.user.id, self.context.bot,
            self.params.ticket_title,
            self.context.vmc.id,
        )
        if not ticket:
            return
        await set_crm_ticket_status(
            ticket,
            CRMTicketStatusEnum(self.params.ticket_status.value),
            CRMTicketStatusInitiatedByEnum.VM,
            notify_user=False,
        )
