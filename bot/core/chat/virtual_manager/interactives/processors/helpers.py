from db.models import VirtualManagerStep
from schemas import VMGoToParams
from .base import VMInteractive
from .results import VMAnsweredInteractivesResult
from ...functions import start_virtual_manager_chat


async def process_go_to(
        instance: VMInteractive[VMGoToParams], result: VMAnsweredInteractivesResult
):
    if instance.params.vm_id and instance.params.vm_id != instance.context.vm.id:
        result.pause_vm = True
        vmc = await start_virtual_manager_chat(
            instance.context.user,
            instance.params.vm_id,
            instance.context.group,
            instance.context.bot,
            step=instance.params.vm_step_id,
        )
        if vmc:
            result.started_vmc_id = vmc.id
    else:
        step = await VirtualManagerStep.get(
            instance.params.vm_step_id,
            virtual_manager_id=instance.context.vm.id,
            is_deleted=False,
        )
        if not step:
            return

        result.vm_next_step_id = step.id
        result.pause_vm = False
