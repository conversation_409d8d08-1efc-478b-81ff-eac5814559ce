from typing import Any

from pydantic import BaseModel, Field


class VMInteractivesResult(BaseModel):
    results_list: list[Any] = Field(default_factory=list)


class VMAnsweredInteractivesResult(VMInteractivesResult):
    started_vmc_id: int | None = None
    vm_next_step_id: int | None = Field(
        default=None,
        nullable=True,
        description="Step to go next(if some button specifies one)",
    )
    pause_vm: bool = False
    stop_vm: bool = False
