from psutils.func import check_function_spec
from typing_extensions import Type<PERSON><PERSON>

from config import <PERSON>B<PERSON><PERSON>
from db import crud
from loggers import <PERSON><PERSON><PERSON>ogger
from schemas import VMInteractiveType
from utils.platform_admins import send_message_to_platform_admins
from .context import VMInteractiveContext
from .processors import VMInteractive, VMInteractivesResult

ResultT = TypeVar("ResultT", bound=VMInteractivesResult, default=VMInteractivesResult)


async def process_vm_interactives(
        step_id: int,
        type_: VMInteractiveType,
        context: VMInteractiveContext,
        logger: JSONLogger,
        *action_args,
        result: ResultT | None = None,
        **action_kwargs
) -> ResultT:
    if not result:
        result = VMInteractivesResult()

    try:
        db_interactives = await crud.get_vm_interactive_list(
            step_id, type_,
        )
    except Exception as e:
        logger.error(
            f"Process interactives {type_.value.upper()} failed! Error while "
            f"retrieving actions",
            e, {
                "interactive_type": type_.value,
            }
        )
        await send_message_to_platform_admins(
            f"Error while retrieving vm interactives: {type_.value}: {repr(e)}\n"
            f"{step_id = }"
        )
        return result

    if not db_interactives:
        return result

    debug_data = {
        "db_interactives": db_interactives,
    }

    if DEBUG:
        logger.debug(
            f"Processing interactives: {type_.value.upper()}", debug_data
        )

    interactive_kwargs = {
        **action_kwargs,
        "result": result
    }

    for db_interactive in db_interactives:
        interactive_debug_data = {
            "interactive_id": db_interactive.id,
            "interactive_type": db_interactive.type.value,
            "interactive_subtype": db_interactive.subtype,
        }
        try:
            interactive = VMInteractive.build(
                db_interactive,
                context,
            )

            safe_kwargs = check_function_spec(interactive.execute, interactive_kwargs)

            interactive_result = await interactive.execute(
                *action_args,
                **safe_kwargs,
            )
        except Exception as e:
            logger.error(
                f"An error occurred while processing "
                f"vm interactive {db_interactive.type.value}:{db_interactive.subtype}",
                e, debug_data, interactive_debug_data
            )
        else:
            result.results_list.append(interactive_result)
            if DEBUG:
                logger.debug(
                    f"Interactive {db_interactive.type.value}:{db_interactive.subtype} "
                    f"processed successfully",
                    debug_data, interactive_debug_data, {
                        "result": interactive_result
                    }
                )

    logger.debug(
        "Interactives processed successfully",
        debug_data, {
            "result": result,
        }
    )

    return result
