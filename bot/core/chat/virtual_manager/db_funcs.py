from itertools import groupby
from operator import itemgetter

from sqlalchemy import delete, select

from db import db_func, sess
from db.models import MediaObject, Scope, Group, VirtualManagerInteractive, VirtualManagerStep


@db_func
def get_group_for_user_vm_link(user_id: int, bot_id: int) -> Group | None:
    query = sess().query(Group)
    query = query.filter(Group.owner_id == user_id)
    query = query.filter(Group.created_from_bot_id == bot_id)
    query = query.filter(Group.status == "enabled")
    return query.one_or_none()


@db_func
def delete_managers(group_id: int) -> bool:
    stmt = delete(Scope).where(
        Scope.profile_id == group_id,
        Scope.scope == "crm:edit",
    )
    sess().execute(stmt)
    sess().commit()
    return True
