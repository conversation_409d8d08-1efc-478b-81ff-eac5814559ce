import logging

import aiogram as tg
import aiowhatsapp as wa

from core import messangers_adapters as ma
from db import crud
from db.models import (
    Chat, ClientBot, Group, User, UserClientBotActivity,
    VirtualManagerChat,
)
from loggers import JSONLogger
from schemas import ChatMessageSentByEnum, ChatTypeEnum
from utils.text import f
from .chat_message_sender import ChatMessageSender
from .keyboards import get_menu_keyboard, get_start_chat_keyboard


async def send_chat_start_message(
        user: User, state: ma.FSMContext, bot: ClientBot, group: Group, lang: str
):
    state_data = await state.get_data()
    virtual_manager_chat_id = state_data.get("virtual_manager_chat_id")

    if virtual_manager_chat_id:
        vmc = await VirtualManagerChat.get(virtual_manager_chat_id)
        vm_name = vmc.virtual_manager.name
    else:
        vm_name = None
    name = vm_name if vm_name else group.name

    if bot.need_leave_chat_keyboard:
        keyboard = await get_start_chat_keyboard(name, lang)
    else:
        keyboard = await get_menu_keyboard(user, bot, lang)

    message_text = await f("chat started header", lang, receiver=name)

    dp = await ma.Dispatcher.get_current()
    user_to = ma.get_user_to(user, bot.bot_type)
    if user_to:
        return await dp.bot.send_message(user_to, message_text, reply_markup=keyboard)
    logging.error(f"user_to is None for {user=}, {bot=}")


async def get_away_from_chat(
        message: ma.types.Message, state: ma.FSMContext | None = None
):
    bot = await ClientBot.get_current()
    user = await ma.get_user_by_message(message)
    lang = await user.get_lang(bot.id)

    if state is None:
        dp = await ma.Dispatcher.get_current()
        state = dp.current_state()

    user_bot_activity = await user.activity_in_bot

    if not await user_bot_activity.active_chat_id:
        return await message.answer(await f("not in chat error", lang))

    await state.finish()  # must be after get_away_from_chat()

    chat = await Chat.get(user_bot_activity.active_chat_id)
    group = await Group.get(chat.group_id)
    name = group.name

    keyboard = await get_menu_keyboard(user, bot, lang)
    await message.answer(
        await f("get away from chat text", lang, name=name), reply_markup=keyboard
    )


async def user_to_chat(
        chat_type: ChatTypeEnum,
        messanger_obj: ma.types.AnswerObject | None = None, *,
        user: User | None = None,
        group_id: int = None,
        virtual_manager_chat_id: int = None,
        need_get_vm_from_history: bool = True,
        need_send_write_message: bool = None,
        need_chat_header: bool = None,
        send_unhandled: bool = False
):
    logger_data = dict(locals())
    logger = JSONLogger("chat", "Start chat", logger_data)

    error_message = None

    if all([messanger_obj, user]):
        error_message = "Only one of message, user must be specified."
    elif not any([messanger_obj, user]):
        error_message = "One of message, user must be specified."

    if error_message:
        logger.error(error_message)
        raise ValueError(error_message)

    if isinstance(messanger_obj, tg.types.Message):
        user = await User.get(messanger_obj.chat.id)
    elif isinstance(messanger_obj, wa.types.message.AnswerObject):
        user = await User.get_by_wa_phone(messanger_obj.user.phone_number)

    bot = await ClientBot.get_current()
    user_bot_activity = await UserClientBotActivity.get(user, bot)

    receiver_address = user.chat_id if bot.bot_type == "telegram" else user.wa_phone

    lang = await user.get_lang()

    dp = await ma.Dispatcher.get_current()
    group = await Group.get(group_id)
    state = dp.current_state(user=receiver_address, chat=user.chat_id)

    if virtual_manager_chat_id:
        virtual_manager_chat = await VirtualManagerChat.get(virtual_manager_chat_id)
    elif need_get_vm_from_history:
        virtual_manager_chat = await crud.get_active_vmc(user.id, bot.id, group_id)
    else:
        virtual_manager_chat = None

    if need_send_write_message is None:
        need_send_write_message = not bool(virtual_manager_chat)

    need_chat_header = (
            need_chat_header is not False and
            bot.need_chat_header and
            not virtual_manager_chat
    )

    chat = await crud.user_to_chat(
        chat_type,
        user.id,
        group_id,
        bot.id,
        user_bot_activity,
    )
    logger.debug("SUCCESS", {"chat": chat})

    if need_chat_header:
        await send_chat_start_message(user, state, bot, group, lang)

    if need_send_write_message:
        await dp.bot.send_message(
            receiver_address, await f("chat write message text", lang)
        )

    if not send_unhandled or not isinstance(messanger_obj, ma.Message):
        return chat

    await ChatMessageSender.from_message(
        messanger_obj, chat,
        group=group, bot=bot,
        chat_user=user,
        sent_by=ChatMessageSentByEnum.USER,
        sent_by_user_id=user.id,
        virtual_manager_chat=virtual_manager_chat,
        need_process_vm_input_to_var=True,
    )

    return chat
