import asyncio
import calendar
import os
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from itertools import groupby
from operator import attrgetter
from typing import Literal, TypeAlias

import aiofiles.os
import aiohttp
import aioshutil
import stripe
from babel import languages
from dateutil.tz import tz
from psutils.convertors import datetime_to_str
from psutils.country import Countries
from psutils.date_time import localise_datetime

import exceptions
import schemas
from config import (
    ADMIN_HOST, BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID,
    BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_TIMEZONE, ROOT_BOT_API_TOKEN,
    ROOT_BOT_USERNAME, STATIC_DIR, SYSTEM_NAME, TEMP_DIR,
)
from core import messangers_adapters as ma
from db import crud
from db.models import (
    BillingProduct, BillingServicePacketItem,
    BillingSubscriptionItem,
    ClientBot, Group, GroupVerificationDocument, MediaObject, Store, StoreProduct,
    VirtualManager,
)
from exceptions import BillingServiceSuspendedError
from loggers import JSONLogger
from schemas import (
    BILLING_PRODUCTS, BillingProductCode, BillingScheme, BillingSubscriptionStatus,
    BillingTiersMode, NotificationLevel, SystemNotificationCategory,
    SystemNotificationType,
)
from utils.list import split_list_gen
from utils.message import send_tg_message
from utils.numbers import format_currency
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from .quota_processor import BillingQuotaProcessor
from .stripe_client import bstripe
from ..admin_notification.service import create_system_notification
from ..kafka.producer.functions import (
    build_and_send_bot_message,
    build_message_kwargs_for_bot_user,
)
from ..media_manager import media_manager

EXPORT_INVOICES_DIR = os.path.join(
    STATIC_DIR,
    TEMP_DIR,
    "invoices",
)


async def sync_billing_products():
    logger = JSONLogger("billing", "sync_products_with_stripe")

    active_products_ids = []

    for i, product in enumerate(BILLING_PRODUCTS.values()):
        db_product = await BillingProduct.get(
            code=product.code
        )

        if db_product:
            logger.debug(
                f"Product {product.code.value} found in database, retrieving stripe "
                f"product",
                {
                    "product": product
                }
            )
            try:
                stripe_product = await bstripe.products.retrieve_async(
                    db_product.stripe_id
                )
            except Exception as e:
                logger.error(
                    "Error retrieving stripe product", e, {
                        "product": product
                    }
                )
                stripe_product = None
        else:
            stripe_product = None

        product_data = {
            **product.dict(exclude={"code", "record_usages", "placeholder_quantity"}),
            "metadata": {
                "system_name": SYSTEM_NAME,
                "7loc_code": product.code.value
            }
        }

        if not stripe_product:
            logger.debug(
                "Creating new stripe product", {
                    "product": product,
                    "product_data": product_data,
                }
            )

            stripe_product = await bstripe.products.create_async(product_data)
        else:
            product_data["active"] = True
            logger.debug(
                "Updating stripe product", {
                    "stripe_product": dict(stripe_product),
                    "product": product,
                    "product_data": product_data,
                }
            )

            stripe_product = await bstripe.products.update_async(
                stripe_product.id, product_data
            )

        db_product_data = {
            "position": i,
            "name": stripe_product.name,
            "description": stripe_product.description,
            "tax_code": stripe_product.tax_code,
            "stripe_id": stripe_product.id
        }

        if db_product:
            await db_product.update(
                db_product_data,
                is_deleted=False,
            )
        else:
            db_product = await BillingProduct.create(
                **db_product_data,
                code=product.code,
            )

        logger.debug(
            "Product synchronised with stripe", {
                "product": product,
                "stripe_product": stripe_product,
                "db_product_id": db_product.id,
            }
        )

        active_products_ids.append(db_product.id)

    old_products = await crud.billing.get_products(exclude_ids=active_products_ids)
    for old_product in old_products:
        try:
            await bstripe.products.update_async(
                old_product.stripe_id, {
                    "active": False,
                }
            )
        except Exception as e:
            logger.error(
                "An error occurred while archiving old product", e, {
                    "id": old_product.id,
                    "name": old_product.name,
                    "stripe_id": old_product.stripe_id,
                }
            )

    await crud.billing.delete_billing_products(map(attrgetter("id"), old_products))


PRODUCTS_MODELS = {
    BillingProductCode.STORE: Store,
    BillingProductCode.PRODUCT: StoreProduct,
    BillingProductCode.VM: VirtualManager,
}


async def get_billing_product_usage(
        group_id: int,
        product_id: int,  # BillingProduct.id
        product_code: BillingProductCode | None = None,
) -> int:
    if not product_code:
        product = await BillingProduct.get(product_id)
        product_code = product.code

    product_data = BILLING_PRODUCTS[product_code]

    if product_data.record_usages:
        return await crud.billing.get_metered_item_usage(group_id, product_id)
    else:
        if not product_code:
            product = await BillingProduct.get(product_id)
            product_code = product.code
        model = PRODUCTS_MODELS.get(product_code)
        if not model:
            return 0

        return await crud.billing.calculate_licensed_item_usage(
            model, group_id
        )


def calculate_billing_item_amount(
        item: BillingSubscriptionItem | BillingServicePacketItem,
        quantity: int | None = None
) -> Decimal:
    if not quantity:
        quantity = item.quantity

    if item.billing_scheme == BillingScheme.TIERED and item.tiers:
        amount = Decimal("0")
        prev_up_to = 0

        for tier in item.tiers:
            if item.tiers_mode == BillingTiersMode.GRADUATED:
                left_quantity = quantity - prev_up_to

                if left_quantity < 0:
                    break

                if tier.flat_amount:
                    amount += tier.flat_amount

                if tier.unit_amount:
                    amount += tier.unit_amount * (
                        left_quantity if tier.up_to == "inf" else min(
                            left_quantity, tier.up_to  # type: ignore
                        )
                    )

                if tier.up_to != "inf":
                    if left_quantity - tier.up_to < 0:
                        break
                    prev_up_to = tier.up_to

            elif tier.up_to == "inf" or tier.up_to >= quantity:
                amount = 0
                if tier.flat_amount:
                    amount += tier.flat_amount
                if tier.unit_amount:
                    amount = tier.unit_amount * quantity
                break

        return amount

    return item.unit_amount * quantity


SubscriptionInfoAction: TypeAlias = Literal[
    "created",
    "activated",
    "payment failed",
    "has no payment method",
]


async def send_subscription_info_to_group(
        data: crud.billing.CreateOrUpdateSubscriptionResult,
        tax_ids_info: schemas.TaxIdsInfo,
        verification_documents: list[tuple[GroupVerificationDocument, MediaObject]],
):
    if not BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID or not (
            data.status_changed or
            data.created_new_subscription
    ):
        return

    logger = JSONLogger(
        "billing", "subscription-info", {
            "profile": data.group
        }
    )

    group, subscription, packet = data.group, data.subscription, data.packet
    packet_info = (
        f"Packet: {packet.name}({packet.currency}); "
        f"{packet.recurring_interval}"
    )
    if packet.recurring_interval_count > 1:
        packet_info += f"({packet.recurring_interval_count})"

    country_name = Countries().get_country_name(data.group.country_code, 'en')

    tax_ids = ", ".join(
        map(lambda x: f"({x.type}) {x.value}", tax_ids_info.tax_ids)
    )

    period_start = datetime_to_str(
        localise_datetime(
            data.subscription.current_period_start,
            BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_TIMEZONE
        )
    )
    period_end = datetime_to_str(
        localise_datetime(
            data.subscription.current_period_end,
            BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_TIMEZONE
        )
    )

    match data.subscription.status:
        case BillingSubscriptionStatus.ACTIVE:
            emoji = "✅"
        case BillingSubscriptionStatus.TRIALING:
            emoji = "⏳"
        case BillingSubscriptionStatus.PAST_DUE:
            emoji = "❗️"
        case BillingSubscriptionStatus.PAUSED:
            emoji = "⏸️"
        case BillingSubscriptionStatus.INCOMPLETE:
            emoji = "⚠️"
        case (
        BillingSubscriptionStatus.INCOMPLETE_EXPIRED |
        BillingSubscriptionStatus.CANCELED |
        BillingSubscriptionStatus.UNPAID
        ):
            emoji = "❌"
        case _:
            emoji = "-"

    text = (
        f"{emoji} #Subscription_{data.subscription.status.value} "
        f"#{data.subscription.stripe_id}\n"
        f"Profile: {data.group.name}\n\n"
        f"Period start: {period_start}\n"
        f"Period end: {period_end}\n"
        f"Country: {country_name}({group.country_code})\n"
        f"{packet_info}\n"
        f"Tax ids: {tax_ids}\n\n"
        f"{ADMIN_HOST}/en/{data.group.id}"
    )

    async def send(documents_data: list[dict]):
        try:
            for el in documents_data:
                try:
                    await send_tg_message(
                        BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID,
                        "document",
                        document=el["file_path"],
                        text=el["comment"]
                    )
                except Exception as e:
                    logger.error("Unable to send verification document", e)
                    await send_tg_message(
                        BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID,
                        "text",
                        text=f"Unable to send document: {el['media_url']}"
                    )

            await send_tg_message(
                BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID,
                "text",
                bot_token=ROOT_BOT_API_TOKEN,
                text=text,
            )
        except Exception as e:
            logger.error(
                "An error occurred while sending verification document "
                "notifications",
                e
            )
            await send_message_to_platform_admins(
                "An error occurred while sending verification document "
                f"notifications {repr(e)}"
            )

    asyncio.ensure_future(
        send(
            [
                {
                    "comment": document.comment,
                    "file_path": media.file_path,
                    "media_url": media.url
                }
                for document, media in verification_documents
            ]
        )
    )


async def send_invoice_info_to_group(
        invoice: stripe.Invoice,
        event_type: str,
):
    if (
            not BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID or
            event_type not in ("invoice.finalized", "invoice.paid") or
            not invoice.amount_due
    ):
        return

    customer_id: str | None = invoice.customer.id if isinstance(
        invoice.customer, stripe.Customer
    ) else invoice.customer

    group = await Group.get(stripe_customer_id=customer_id) if customer_id else None

    stripe_subscription = (
        invoice.subscription
        if isinstance(invoice.subscription, stripe.Subscription)
        else await bstripe.subscriptions.retrieve_async(invoice.subscription)
        if invoice.subscription else None
    )

    to_time_zone = BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_TIMEZONE
    to_time_zone = tz.gettz(to_time_zone) if to_time_zone != "utc" else tz.tzutc()
    period_start = datetime_to_str(
        datetime.fromtimestamp(
            stripe_subscription.current_period_start,
            to_time_zone
        ),
    )
    period_end = datetime_to_str(
        datetime.fromtimestamp(
            stripe_subscription.current_period_end,
            to_time_zone
        ),
    )

    countries = Countries()
    if group:
        country_name = countries.get_country_name(group.country_code, 'en')

        country_code = group.country_code

        group_info = f"Profile: {group.name}\n"
        country_info = f"Country: {country_name}({group.country_code})\n"
        admin_url = f"{ADMIN_HOST}/en/{group.id}"
    else:
        group_info = ""
        country_info = ""
        admin_url = ""

        country_code = invoice.account_country

    subscription_info = (
        f"#Subscription_{stripe_subscription.status} #{stripe_subscription.id}\n"
        f"{group_info}\n"
        f"Period start: {period_start}\n"
        f"Period end: {period_end}\n"
    ) if stripe_subscription else ""

    amount_raw = invoice.amount_paid if invoice.status == "paid" else invoice.amount_due

    langs = languages.get_official_languages(country_code)
    country_lang = langs[0] if langs else "en"

    invoice_amount = format_currency(
        amount_raw / 100, invoice.currency,
        country_lang, country_code
    )

    match event_type:
        case "invoice.finalized":
            emoji = "🧾"
        case "invoice.paid":
            emoji = "💵"
        case _:
            emoji = "-"

    text = (
        f"{emoji} #Invoice_{event_type[8:]} #{invoice.number.replace('-', '_')}\n"
        f"{subscription_info}\n"
        f"Amount: {invoice_amount}\n"
        f"{country_info}"
        f"{admin_url}"
    )

    if invoice.invoice_pdf and event_type == "invoice.finalized":
        content_type = "document"

        media = await media_manager.download_media(
            invoice.invoice_pdf,
            original_file_name=f"Invoice-{invoice.number}.pdf"
        )
    else:
        content_type = "text"
        media = None

    await build_and_send_bot_message(
        "root", ROOT_BOT_USERNAME,
        "telegram", ROOT_BOT_API_TOKEN,
        tg_chat_id=BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID,
        content_type=content_type,
        text=text,
        media=media,
    )


async def create_or_update_subscription(
        data: stripe.Subscription,
        logger: JSONLogger,
        tax_ids_info: schemas.TaxIdsInfo | None = None,
        verification_documents: list[tuple[
            GroupVerificationDocument, MediaObject]] | None = None,
):
    result = await crud.billing.create_or_update_subscription(data, logger)
    if result.status_changed and result.group and result.subscription:
        group = result.group

        if (result.subscription.status == BillingSubscriptionStatus.ACTIVE and
                result.previous_status in (
                        BillingSubscriptionStatus.PAST_DUE,
                        BillingSubscriptionStatus.INCOMPLETE
                )):
            notification_type = SystemNotificationType.SUBSCRIPTION_RECOVERED
            level = NotificationLevel.INFO
        elif result.subscription.status in (
                BillingSubscriptionStatus.PAST_DUE,
                BillingSubscriptionStatus.INCOMPLETE,
        ) and result.status_changed:
            notification_type = SystemNotificationType.SUBSCRIPTION_PAST_DUE
            level = NotificationLevel.ERROR
        else:
            notification_type, level = None, None

        if notification_type and level:
            await create_system_notification(
                "profile:admin",
                result.group.id,
                SystemNotificationCategory.BILLING,
                notification_type,
                title=await f(
                    f"billing {notification_type.value} title", group.lang
                ),
                content=await f(
                    f"billing {notification_type.value} content",
                    group.lang,
                    billing_url=f"{ADMIN_HOST}/{group.lang}/{group.id}/billing"
                ),
                level=level,
            )

    if not result.subscription or not result.group:
        return

    if not tax_ids_info:
        tax_ids_info, verification_documents = await validate_tax_ids(result.group)
    if verification_documents is None:
        verification_documents = []

    await send_subscription_info_to_group(
        result, tax_ids_info, verification_documents,
    )

    return result


async def check_bot_billing_status(
        bot: ClientBot,
        receiver: ma.User,
):
    if (
            await crud.check_access_to_action(
                "billing:tester", "profile", bot.group_id
            ) and
            not await crud.billing.check_profile_subscription(bot.group_id)

    ):
        bot_group = await Group.get(bot.group_id)
        await build_and_send_bot_message(
            **await build_message_kwargs_for_bot_user(bot, receiver),
            content_type="text",
            text=await f(
                "billing service suspended error",
                getattr(receiver, "language_code", bot_group.lang),
            )
        )
        raise ma.handler.CancelHandler(bot.bot_type)


async def check_billing_transaction_limit(group_id: int, currency: str):

    tariff_plan_currency = (
        await crud.billing.get_profile_subscription_currency(group_id)
    )

    if (
            tariff_plan_currency and
            currency.upper() != tariff_plan_currency.upper() and
            await crud.billing.get_is_item_exist_for_product(
                group_id,
                BillingProductCode.TRANSACTION_CENT,
            )
    ):
        group = await Group.get(group_id)
        await create_system_notification(
            "profile:admin",
            group_id,
            SystemNotificationCategory.BILLING,
            SystemNotificationType.TRANSACTION_CURRENCY_MISMATCH,
            await f(
                "billing transaction currency mismatch notification title",
                group.lang,
            ),
            await f(
                "billing transaction currency mismatch notification content",
                group.lang,
                currency=currency,
                tariff_plan_currency=tariff_plan_currency,
            ),
            level=NotificationLevel.ERROR,
        )

        raise exceptions.BillingRecordTransactionCurrencyMismatchError(currency)

    if (
            not await BillingQuotaProcessor(
                group_id,
                BillingProductCode.TRANSACTION,
                exception_on_error=BillingServiceSuspendedError(),
            ).check_product_limit(
                return_not_exists=True
            ) and
            not await BillingQuotaProcessor(
                group_id,
                BillingProductCode.TRANSACTION_CENT,
                exception_on_error=BillingServiceSuspendedError(),
            ).check_product_limit(
                return_not_exists=True,
            )
    ):
        raise exceptions.BillingServiceSuspendedError()


async def record_billing_transaction_usage(
        group: Group,
        amount: int,  # in cents
):
    await BillingQuotaProcessor(
        group.id,
        BillingProductCode.TRANSACTION,
        group=group,
    ).record_usage(
        ignore_validation=True,
    )

    await BillingQuotaProcessor(
        group.id,
        BillingProductCode.TRANSACTION_CENT,
        group=group,
    ).record_usage(
        amount,
        ignore_validation=True,
    )


async def get_tax_ids_info(customer_id: str):
    stripe_customer = await bstripe.customers.retrieve_async(
        customer_id, {
            "expand": ["tax_ids"]
        }
    )

    tax_ids = stripe_customer.tax_ids.data
    by_status = {
        status: list(ids)
        for status, ids in
        groupby(tax_ids, lambda x: x.verification.status)
    }
    return schemas.TaxIdsInfo(
        tax_ids=tax_ids,
        by_status=schemas.TaxIdsInfoByStatus(**by_status),
    )


async def validate_tax_ids(profile: Group):
    tax_ids_info = await get_tax_ids_info(profile.stripe_customer_id)

    if pending := tax_ids_info.by_status.pending:
        raise exceptions.BillingTaxIdsVerificationRequiredError(
            tax_ids=list(tax_id.value for tax_id in pending)
        )

    if unverified := tax_ids_info.by_status.unverified:
        raise exceptions.BillingTaxIdsInvalidError(
            tax_ids=list(tax_id.value for tax_id in unverified)
        )

    # verification_documents = await crud.get_verification_document_list(
    #     profile.id
    # )
    verification_documents = []

    # if tax_ids_info.by_status.unavailable and not verification_documents:
    #     raise exceptions.BillingSupportingDocumentsRequiredError()

    return tax_ids_info, verification_documents


async def get_billing_invoices(
        period_start: datetime,
        period_end: datetime,
):
    invoices = await bstripe.invoices.list_async(
        {
            "created": {
                "gte": int(period_start.timestamp()),
                "lte": int(period_end.timestamp()),
            },
            "expand": ["data.customer"]
        }
    )

    return invoices.auto_paging_iter()


@asynccontextmanager
async def get_unpaid_invoices_last_month_2weeks():
    """
    Fetch unpaid invoices for the last `days` days of the previous calendar month,
    and split them into new vs returning customers.
    """
    days = 14
    now = datetime.now(timezone.utc)
    first_of_this_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    last_of_prev_month = first_of_this_month - timedelta(seconds=1)
    year, month = last_of_prev_month.year, last_of_prev_month.month

    month_length = calendar.monthrange(year, month)[1]
    start_day = month_length - days + 1
    period_start = datetime(year, month, start_day, 0, 0, 0, tzinfo=timezone.utc)
    period_end = datetime(year, month, month_length, 23, 59, 59, tzinfo=timezone.utc)

    resp = await bstripe.invoices.list_async(
        {
            "created": {
                "gte": int(period_start.timestamp()),
                "lte": int(period_end.timestamp()),
            },
            "expand": ["data.customer"]
        }
    )
    invoices = [inv async for inv in resp.auto_paging_iter()]

    customer_ids = {inv.customer.id for inv in invoices}
    has_paid_before = {}
    for cid in customer_ids:
        paid_resp = await bstripe.invoices.list_async(
            {
                "customer": cid,
                "status": "paid",
                "limit": 1,
                "created": {"lt": int(period_start.timestamp())}
            }
        )
        found = False
        async for _ in paid_resp.auto_paging_iter():
            found = True
            break
        has_paid_before[cid] = found

    new_customers = []
    returning_customers = []
    for inv in invoices:
        if has_paid_before[inv.customer.id]:
            returning_customers.append(inv)
        else:
            new_customers.append(inv)

    new_list = new_customers or []
    returning_list = returning_customers or []

    new_ids = [str(inv.number) for inv in new_list]
    returning_numbers = [
        str(inv.number) if inv.number is not None else str(inv.id)
        for inv in returning_list
    ]

    def join_or_none(items: list[str]) -> str:
        return ", ".join(items) if items else "None"

    message = (
        f"Unpaid invoices for the last {days} days of the previous month:\n"
        f"New customers invoice IDs: {join_or_none(new_ids)}\n"
        f"Returning customers invoice numbers: {join_or_none(returning_numbers)}"
    )

    return message


async def download_invoice(
        directory: str,
        invoice: stripe.Invoice,
):
    async with aiohttp.ClientSession() as session:
        async with session.get(invoice.invoice_pdf) as resp:
            invoice_path = os.path.join(
                directory,
                f"{invoice.number}.pdf"
            )
            async with aiofiles.open(invoice_path, "wb") as file:
                while content := await resp.content.read(1024):
                    await file.write(content)

    return invoice_path


async def create_invoices_archive(
        filename: str,
        invoices_dir: str,
):
    basename = os.path.join(
        EXPORT_INVOICES_DIR,
        filename,
    )
    return await aioshutil.make_archive(
        basename,
        "zip",
        invoices_dir,
    )


@asynccontextmanager
async def export_invoices(
        period_start: datetime,
        period_end: datetime,
):
    invoice_paths = []

    invoices_dir = os.path.join(
        EXPORT_INVOICES_DIR,
        uuid.uuid4().hex
    )

    period_start_str = period_start.strftime("%Y-%m-%d")
    period_end_str = period_end.strftime("%Y-%m-%d")
    filename = f"Invoices {period_start_str}-{period_end_str}"

    archive_path = None

    try:
        to_download: list[tuple[str, stripe.Invoice]] = []

        async for invoice in await get_billing_invoices(period_start, period_end):
            if not invoice.invoice_pdf or not invoice.amount_paid:
                continue

            customer_path = os.path.join(
                invoices_dir,
                filename,
                invoice.customer.name.replace(' ', '_')
            )

            await aiofiles.os.makedirs(customer_path, exist_ok=True)

            to_download.append(
                (
                    customer_path,
                    invoice,
                )
            )

        for invoices_part in split_list_gen(to_download, 10):
            invoice_paths.extend(
                await asyncio.gather(
                    *[
                        download_invoice(*el)
                        for el in invoices_part
                    ],
                    return_exceptions=False
                )
            )

        archive_path = await create_invoices_archive(
            filename, invoices_dir
        )
        yield archive_path
    finally:
        await aioshutil.rmtree(invoices_dir, ignore_errors=True)
        if archive_path:
            await aiofiles.os.remove(archive_path)
