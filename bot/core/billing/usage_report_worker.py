import random
from datetime import datetime, timezone

import stripe.v2.billing

from core.billing.stripe_client import bstripe
from db import DBSession, crud
from loggers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.platform_admins import send_message_to_platform_admins
from utils.processes_manager.background_worker import <PERSON><PERSON>ackgroundWorker
from utils.processes_manager.background_worker.loop import IterationResult


class StripeUsageReportWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "usage_report_worker"
    DEFAULT_TIMEOUT = 1
    CHUNK_LIMIT = 1000

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._meter_event_session: stripe.v2.billing.MeterEventSession | None = None

    async def get_token(self):
        if not self._meter_event_session or datetime.fromisoformat(
                self._meter_event_session.expires_at.replace("Z", "")
        ).astimezone(timezone.utc) <= datetime.now(timezone.utc):
            self._meter_event_session = await (
                bstripe.v2.billing.meter_event_session.create_async()
            )
        return self._meter_event_session.authentication_token

    async def iteration(self):
        with DBSession():
            records_to_report = await crud.billing.get_usage_records_to_report(
                self.CHUNK_LIMIT
            )

        if not records_to_report:
            return

        events = []
        update_records_data: dict[int, int] = {}

        for record in records_to_report:
            quantity_to_report = record.used_quantity - record.reported_quantity
            events.append(
                {
                    "event_name": record.meter_event_name,
                    "payload": {
                        "stripe_customer_id": record.stripe_customer_id,
                        "value": str(quantity_to_report)
                    },
                }
            )
            update_records_data[record.id] = quantity_to_report

        logger = JSONLogger(
            "billing.usage_report_worker", {
                "records_to_report": records_to_report,
                "events": events,
                "update_records_data": update_records_data,
            }
        )

        token = None
        try:
            token = await self.get_token()
            client = stripe.StripeClient(token)
            result = await client.v2.billing.meter_event_stream.create_async(
                {
                    "events": events
                }
            )
        except Exception as e:
            retry_timeout = self.DEFAULT_TIMEOUT * (1 + random.random())

            await send_message_to_platform_admins(
                f"An error occurred while sending usages to stripe: {repr(e)}"
            )

            logger.error(
                f"An error occurred while sending usages to stripe. Retry in "
                f"{retry_timeout}", e,
                {
                    "retry_timeout": retry_timeout,
                    "token": token,
                }
            )
            return IterationResult(timeout=retry_timeout)
        else:
            logger.debug(
                "Successfully sent usages to stripe", {
                    "result": result,
                }
            )

        try:
            with DBSession():
                await crud.billing.save_reported_usages(update_records_data)
        except Exception as e:
            await send_message_to_platform_admins(
                f"An error occurred while saving reported usages: {repr(e)}"
            )
            logger.error("An error occurred while saving reported usages", e)
