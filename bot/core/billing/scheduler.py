import os.path

from config import (
    BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID, BILLING_TIMEZONE, ROOT_BOT_API_TOKEN,
    ROOT_BOT_USERNAME,
)
from core.billing.functions import (
    export_invoices,
)
from core.kafka.producer.functions import build_and_send_bot_message
from core.scheduler import Scheduler, SchedulerArgs, SchedulerJob
from utils.date_time import get_prev_month


async def export_and_send_invoices_previous_month_to_group():
    period_start, period_end = get_prev_month(BILLING_TIMEZONE)

    async with export_invoices(period_start, period_end) as archive_path:
        filename = os.path.split(archive_path)[-1]

        await build_and_send_bot_message(
            "root",
            ROOT_BOT_USERNAME,
            "telegram",
            ROOT_BOT_API_TOKEN,
            tg_chat_id=BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID,
            content_type="document",
            text="Archive with customers invoices for the previous month period",
            content=dict(
                path_or_bytesio=archive_path,
                filename=filename,
            ),
        )


def register_billing_scheduler(scheduler: Scheduler):
    scheduler.add_job(
        SchedulerJob(
            export_and_send_invoices_previous_month_to_group,
            SchedulerArgs(
                day="8",
                hour="0",
                minute="0",
                second="0",
            )
        )
    )
