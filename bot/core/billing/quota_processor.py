from functools import wraps
from typing import Literal

from psutils.type_vars import FuncT

import exceptions
from config import ADMIN_HOST
from core.admin_notification.service import create_system_notification
from core.kafka.producer.functions import (
    build_and_send_bot_message,
    build_message_kwargs_for_bot_user,
)
from db import crud
from db.models import BillingUsageRecord, ClientBot, Group, User
from loggers import J<PERSON>NLogger
from schemas import (
    BILLING_PRODUCTS, BillingProductCode, SystemNotificationCategory,
    SystemNotificationType,
)
from utils.text import f


def with_handle_quota_error(func: FuncT) -> FuncT:
    @wraps(func)
    async def wrapper(self: "BillingQuotaProcessor", *args, **kwargs):
        try:
            return await func(self, *args, **kwargs)
        except (
                exceptions.BillingQuotaExceededError,
                exceptions.BillingProductNotAvailableError
        ) as error:
            group_lang = await self.group_lang

            if not self.suppress_sending_not_available_error:
                await create_system_notification(
                    "profile:admin",
                    self.group_id,
                    SystemNotificationCategory.BILLING,
                    SystemNotificationType.QUOTA_EXCEEDED,
                    title=await f(error.text_variable, group_lang, **error.text_kwargs),
                    content=await f(
                        f"billing quota exceeded error content",
                        group_lang, **error.text_kwargs,
                        billing_url=f"{ADMIN_HOST}/{group_lang}/{self.group_id}/billing"
                    ),
                )

                if self.send_to_user_in_bot and self.user and self.bot:
                    await build_and_send_bot_message(
                        **await build_message_kwargs_for_bot_user(self.bot, self.user),
                        content_type="text",
                        text=await f(
                            error.text_variable,
                            await self.user.get_lang(self.bot),
                            **error.text_kwargs,
                        ),
                        no_error=True,
                    )

            if self.return_on_error == "exception":
                return self.exception_on_error or error
            elif self.return_on_error == "false":
                return False

            if self.exception_on_error:
                raise self.exception_on_error
            raise

    return wrapper


ReturnOnError = Literal["exception", "false", "none"]


class BillingQuotaProcessor:
    def __init__(
            self, group_id: int,
            product_code: BillingProductCode,
            return_on_error: ReturnOnError = "none",
            group: Group | None = None,
            group_lang: str | None = None,
            exception_on_error: Exception | Literal["suspended"] | None = None,
            send_to_user_in_bot: bool = True,  # also required user and bot to send
            user: User | None = None,
            bot: ClientBot | None = None,
            suppress_sending_not_available_error: bool = False,
    ):
        self.group_id: int = group_id
        self.product_code: BillingProductCode = product_code

        self._group: Group | None = group
        self._group_lang: str = group_lang
        self.return_on_error: ReturnOnError = return_on_error

        self.exception_on_error: Exception | None = (
            exceptions.BillingServiceSuspendedError()
            if exception_on_error == "suspended"
            else (
                exception_on_error
                if isinstance(exception_on_error, Exception)
                else None
            )
        )

        self.send_to_user_in_bot: bool = send_to_user_in_bot
        self.user: User | None = user
        self.bot: ClientBot | None = bot

        self.suppress_sending_not_available_error: bool = (
            suppress_sending_not_available_error
        )

    @property
    async def group(self) -> Group:
        if not self._group:
            self._group = await Group.get(self.group_id)
        return self._group

    @property
    async def group_lang(self):
        if not self._group_lang:
            self._group_lang = (await self.group).lang
        return self._group_lang

    def get_logger(self, requested_quantity: int | None = None, **extra_data):
        logger = JSONLogger(
            "billing.quota", {
                "product_code": self.product_code,
                "group": self._group or self.group_id,
                "return_on_error": self.return_on_error,
            },
        )
        if requested_quantity is not None:
            logger.add_data({"requested_quantity": requested_quantity})
        if extra_data:
            logger.add_data(extra_data)
        return logger

    @with_handle_quota_error
    async def record_usage(
            self, quantity: int = 1,
            ignore_validation: bool = False,
    ) -> list[BillingUsageRecord] | exceptions.BillingRecordUsageError:
        return await crud.billing.record_usage(
            self.group_id,
            self.product_code,
            quantity,
            self.get_logger(quantity),
            ignore_validation,
        )

    @with_handle_quota_error
    async def check_product_limit(
            self, quantity: int = 1,
            return_not_exists: bool = False,
    ):
        logger = self.get_logger(quantity)

        available_quantity = await crud.billing.get_product_available_quantity(
            self.group_id, self.product_code, return_not_exists
        )
        if available_quantity == "not_exists":
            return False

        if available_quantity != "inf" and (
                not available_quantity or available_quantity < quantity
        ):
            logger.debug(
                "Not enough units available", {
                    "left_quantity": available_quantity,
                }
            )
            raise exceptions.BillingQuotaExceededError(
                BILLING_PRODUCTS[self.product_code].name,
                quantity,
            )
        logger.debug(
            "Product available!", {
                "available_quantity": available_quantity,
            }
        )
        return True
