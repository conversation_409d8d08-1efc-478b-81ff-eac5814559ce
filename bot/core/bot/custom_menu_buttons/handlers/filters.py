from core import messangers_adapters as ma
from db import crud
from db.models import ClientBot


async def menu_button_filter(message: ma.types.Message):
    bot = await ClientBot.get_current()

    data = ma.handler.pick_ctx_data(bot.bot_type).get()
    lang = data.get("lang")

    menu_button = await crud.get_custom_menu_button_by_text(
        bot.id, message.text, lang
    )

    if menu_button:
        return {"button": menu_button}

    return False
