from core import messangers_adapters as ma
from core.bot.custom_menu_buttons.functions import process_custom_menu_button
from core.bot.custom_menu_buttons.handlers.filters import menu_button_filter
from db.models import ClientBot, CustomMenuButton, User


@ma.handler.message()
async def custom_menu_button_handler(
        message: ma.types.Message,
        state: ma.FSMContext,
        button: CustomMenuButton,
        user: User,
        bot: ClientBot,
):
    await state.finish()
    await process_custom_menu_button(message, user, bot, button)


def register_custom_menu_buttons_handlers(dp: ma.DispatcherType):
    custom_menu_button_handler.setup(
        dp, menu_button_filter,
        state="*",
        content_types="text",
    )
