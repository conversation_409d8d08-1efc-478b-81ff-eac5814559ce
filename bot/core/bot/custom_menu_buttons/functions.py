from core import messangers_adapters as ma
from db import crud

from db.models import ClientBot, CustomMenuButton, User, VirtualManager
from schemas import CustomMenuButtonActionTypeEnum
from utils.text import f


async def process_custom_menu_button(
        answer_obj: ma.AnswerObject,
        user: User,
        bot: ClientBot,
        button: CustomMenuButton,
):
    match button.action_type:
        case CustomMenuButtonActionTypeEnum.VM:
            if not button.vm_id:
                return await answer_obj.answer("no vm set to button with action: VM")
            vm = await VirtualManager.get(button.vm_id)
            await crud.start_virtual_manager_chat(
                user,
                vm,
                bot.group_id,
                bot,
            )
        case _:
            lang = await user.get_lang(bot)
            await answer_obj.answer(await f("soon", lang))
