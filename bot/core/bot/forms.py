from aiogram.dispatcher.handler import ctx_data
from psutils.forms.initializer import Forms

from config import DEFAULT_TIME_ZONE, STATIC_DB
from db.models import User
from utils.text import f


def get_timezone(default_timezone: str = None):
    data = ctx_data.get({})
    user: User = data.get("user")

    if not user:
        return default_timezone or DEFAULT_TIME_ZONE
    return user.get_timezone(default_timezone)


def get_forms():
    return Forms(
        STATIC_DB,
        get_text_func=f,
        get_timezone_func=get_timezone,
    )
