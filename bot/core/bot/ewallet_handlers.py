import asyncio
import logging
from dataclasses import dataclass

from aiowhatsapp import types
from aiowhatsapp.dispatcher import FSMContext

from core import messangers_adapters as ma
from core.loyalty.incust_api import incust
from core.topup_ewallet.callback_data import TopUpEwalletCallbackData
from core.whatsapp.keyboards import send_wa_main_menu
from db import crud
from db.models import ClientBot, E<PERSON><PERSON><PERSON>, User
from schemas import LoyaltySettingsData
from utils.numbers import format_currency
from utils.text import c, f, html_to_markdown
from utils.translator import t

debugger = logging.getLogger('debugger.ewallet_handlers')


@dataclass
class EWalletResponse:
    ewallet: EWallet
    message: str
    is_user_can_topup: bool
    used_credit: float | None = None
    available_amount: float | None | bool = None
    amount: float | None = None
    credit_limit: float | None = None
    credit_type: str | None = None
    is_user_special_account: bool | None = None


async def process_single_ewallet(
        ewallet: <PERSON><PERSON><PERSON><PERSON>, user: User, lang: str
) -> EWalletResponse:
    """
    Обробляє окремий ewallet
    """
    try:
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "ewallet",
            LoyaltySettingsData(
                ewallet_id=ewallet.id,
            )
        )

        if not loyalty_settings:
            # Якщо немає налаштувань лояльності для цього ewallet - повертаємо базову
            # відповідь
            debugger.warning(f"No loyalty settings found for ewallet {ewallet.id}")
            return EWalletResponse(
                ewallet=ewallet,
                message=await return_zero_balance(ewallet, lang),
                is_user_can_topup=False,
                available_amount=False,
                is_user_special_account=False,
            )

        async with incust.term.CustomerApi(loyalty_settings, lang=lang) as api:
            # Отримуємо інформацію про карту користувача через новий client API (
            # замість incust_api_client.get_card_info)
            user_card_info = await api.cardinfo(
                user.incust_external_id,
                "external-id",
            )

        is_user_can_topup, is_user_special_account = False, False
        user_special, used_credit, amount, credit_limit, credit_type = (
            None, None, None, None, None
        )

        if user_card_info:

            for special in user_card_info.specials or []:
                if (
                        special.special_account.type == "money" and
                        special.special_account.id ==
                        ewallet.incust_account_id):
                    user_special = special
                    amount = special.amount
                    credit_limit = special.credit_limit
                    used_credit = special.amount
                    credit_type = special.credit_type
                    is_user_can_topup = True
                    is_user_special_account = True
                    break

            # if not user_special and
            # user_card_info.corporate_special_accounts_access or []:
            #     for special in user_card_info.corporate_special_accounts_access or []:
            #         if (special.corporate_customer_special_account.special_account
            #         .id ==
            #                 ewallet.incust_account_id):
            #             user_special = special.corporate_customer_special_account
            #             amount = special.corporate_customer_special_account.amount
            #             credit_limit =
            #             special.corporate_customer_special_account.credit_limit
            #             used_credit =
            #             special.corporate_customer_special_account.amount
            #             credit_type =
            #             special.corporate_customer_special_account.credit_type
            #             break

        if not user_special:
            async with incust.term.BusinessApi(loyalty_settings) as api:
                all_specials = await api.special_accounts("retail")

            for special in all_specials or []:
                if special.type == "money" and special.id == ewallet.incust_account_id:
                    is_user_can_topup = True
                    credit_type = special.credit_type

            if lang == ewallet.lang:
                info_text = ewallet.info
            else:
                translated = await t(
                    ewallet,
                    lang, ewallet.lang,
                    group_id="internal",
                    is_auto_translate_allowed=False,
                )

                info_text = translated.get("info") if translated else None
                info_text = info_text or ewallet.info

            return EWalletResponse(
                ewallet=ewallet,
                message=await return_zero_balance(
                    ewallet, lang
                ) if not info_text else info_text,
                is_user_can_topup=is_user_can_topup,
                available_amount=False,
                credit_type=credit_type,
                is_user_special_account=is_user_special_account,
            )

        available_amount = user_special.available_amount

        if available_amount == 0:
            return EWalletResponse(
                ewallet, await return_zero_balance(ewallet, lang), is_user_can_topup,
                used_credit, available_amount, amount, credit_limit, credit_type,
                is_user_special_account=is_user_special_account,
            )

        if not available_amount:
            available_amount_str = await f("incust pay unlimited text", lang)
        else:
            available_amount_str = format_currency(
                available_amount, ewallet.currency, locale=lang
            )

        available_amount_text = await f(
            "incust pay available for payment text", lang
        ) + ": " + available_amount_str

        if amount is not None:
            available_amount_text += "\n" + await f(
                "client bot account own cost text", lang,
                amount=format_currency(
                    (available_amount - (credit_limit or 0)) if amount >= 0 else 0,
                    ewallet.currency, locale=lang
                )
            )

        # Check if account has credit limit
        message = f"<b>{ewallet.name}</b>\n{available_amount_text}"

        # Check if it's a regular special account
        if (
                hasattr(user_special, 'special_account')
                and user_special.special_account
                and hasattr(user_special, 'credit_limit')
                and hasattr(user_special, 'credit_type')
        ):
            credit_limit = user_special.credit_limit
            credit_type = user_special.credit_type
        # Check if it's a corporate special account
        elif hasattr(
                user_special, 'corporate_customer_special_account'
        ) and user_special.corporate_customer_special_account:
            credit_limit = user_special.corporate_customer_special_account.credit_limit
            credit_type = user_special.corporate_customer_special_account.credit_type

        # If account has credit limit, show used credit amount
        if (credit_limit and credit_type in ["credit",
                                             "credit-unlimited"] and available_amount is
                not None):
            # Обчислюємо використаний кредит як різницю між кредитним лімітом і
            # доступною
            # сумою
            # Але якщо доступна сума більша за кредитний ліміт, то кредит не
            # використаний
            # (used_credit = 0)
            used_credit = max(0, credit_limit - available_amount)

            if used_credit == 0:
                used_credit_text = await f(
                    "incust pay credit limit amount text",
                    lang,
                    amount=f"{format_currency(credit_limit, ewallet.currency, locale=lang)}"
                )
            else:
                used_credit_str = format_currency(
                    used_credit, ewallet.currency, locale=lang
                )
                credit_limit_str = format_currency(
                    credit_limit, ewallet.currency, locale=lang
                )
                used_credit_text = await f(
                    "loyalty special account used credit text",
                    lang,
                    amount=f"{used_credit_str} / {credit_limit_str}"
                )
            message += f"\n{used_credit_text}"

        if credit_type not in ["credit", "credit-unlimited"]:
            used_credit = None

        return EWalletResponse(
            ewallet,
            message,
            is_user_can_topup,
            used_credit,
            available_amount,
            amount,
            credit_limit,
            credit_type,
            is_user_special_account,
        )

    except Exception as e:
        debugger.error(f"Error processing ewallet {ewallet.id}: {e}", exc_info=True)
        # У випадку помилки повертаємо базову відповідь
        return EWalletResponse(
            ewallet=ewallet,
            message=await return_zero_balance(ewallet, lang),
            is_user_can_topup=False,
            available_amount=False,
            is_user_special_account=False,
        )


async def return_zero_balance(ewallet: EWallet, lang: str) -> str:
    available_amount_str = format_currency(
        0, ewallet.currency,
        locale=lang
    )
    available_amount_str = await f(
        "incust pay available for payment text", lang
    ) + ": " + available_amount_str

    return f"<b>{ewallet.name}</b>\n{available_amount_str}"


async def get_topup_ewallet_keyboard(
        bot: ClientBot,
        ewallet: EWallet,
        lang: str,
        used_credit: float | None = None,
):
    # Передаємо used_credit тільки якщо він більше 0
    # В іншому випадку передаємо None, щоб відображалась кнопка "Topup"
    button_used_credit = used_credit if used_credit and used_credit > 0 else None

    button_data = dict(
        mode="topup_ewallet", ewallet_id=ewallet.id, group_id=bot.group_id,
        used_credit=button_used_credit
    )

    data = c(**button_data)

    keyboard = None
    text = await f(
        "client bot topup special account", lang
    ) if not button_used_credit else await f(
        "loyalty special account topup credit text", lang
    )

    match bot.bot_type:
        case "telegram":
            keyboard = ma.tg.types.InlineKeyboardMarkup()
            message_reply_btn = ma.tg.types.InlineKeyboardButton(
                text,
                callback_data=TopUpEwalletCallbackData(
                    ewallet_id=ewallet.id, group_id=bot.group_id,
                    used_credit=used_credit
                ).to_str()
            )
            keyboard.add(message_reply_btn)
        case "whatsapp":
            keyboard = ma.wa.types.ReplyKeyboard()
            keyboard.add_buttons(
                ma.wa.types.ReplyButton(
                    id=data, title=text,
                )
            )

    return keyboard


async def ewallet_button_handler(
        message: types.ListReplyQuery | types.Message,
        state: FSMContext,
        bot: ClientBot,
        user: User,
        lang: str,
):
    debugger.debug("ewallet_button_handler ->")
    ewallets = await crud.get_ewallet_list(bot_id=bot.id, user_id=user.id)

    if not ewallets:
        await state.finish()
        await message.answer(
            await f("WEB_APP_NO_ACCOUNTS_TEXT", lang)
        )
        return await send_wa_main_menu(bot, lang, user)

    # Виконуємо всі запити паралельно
    tasks = [process_single_ewallet(ewallet[0], user, lang) for ewallet in ewallets]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Обробка результатів
    for result in results:
        if isinstance(result, BaseException):
            debugger.error(f"EWallet processing error: {result}")
            continue

        ewallet = result.ewallet
        message_text = result.message

        keyboard = None
        if ewallet.invoice_template_id and result.is_user_can_topup:
            keyboard = await get_topup_ewallet_keyboard(
                bot, ewallet,
                lang, result.used_credit
            )

        await message.answer(
            html_to_markdown(
                message_text
            ) if bot.bot_type == "whatsapp" else message_text,
            reply_markup=keyboard,
        )

    # Повернення головного меню в WhatsApp
    if bot.bot_type == "whatsapp":
        await send_wa_main_menu(bot, lang, user)

    await state.finish()
