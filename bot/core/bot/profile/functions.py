from client.main.keyboards import get_open_profile_keyboard
from core import messangers_adapters as ma
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db import crud
from db.models import ClientBot, User, UserAnalyticAction
from utils.text import f


async def send_tg_profile(
        message: ma.tg.types.Message,
        bot: ClientBot,
        user: User,
        lang: str,
):
    message_text = await f("open profile text", lang)
    keyboard = await get_open_profile_keyboard(bot, lang)
    await message.answer(message_text, reply_markup=keyboard)
    await UserAnalyticAction.save_button_click(user, bot, "profile/balance")


async def send_wa_profile(
        answer_obj: ma.AnswerObject,
        bot: ClientBot,
        user: User,
        lang: str,
):
    brand = await crud.get_brand_by_group(bot.group_id)
    if not brand:
        return await answer_obj.answer("Unable to send profile. No brand found")

    link = await brand.get_short_token_url(user, bot.id, lang, "profile")

    return await answer_obj.answer(
        await f("wa open profile text", lang, link=link),
        keyboard=await get_wa_menu_keyboard(user, bot, lang)
    )


async def send_bot_profile(
        answer_obj: ma.types.AnswerObject | ma.types.ButtonQuery,
        bot: ClientBot,
        user: User,
        lang: str,
):
    match bot.bot_type:
        case "telegram":
            if isinstance(answer_obj, ma.tg.types.CallbackQuery):
                answer_obj = answer_obj.message

            return await send_tg_profile(answer_obj, bot, user, lang)
        case "whatsapp":
            return await send_wa_profile(answer_obj, bot, user, lang)
        case _:
            raise ValueError(f"Unknown bot type: {bot.bot_type}")
