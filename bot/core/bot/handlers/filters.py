import logging

from core import messangers_adapters as ma
from core.custom_texts import ct
from db.models import ClientBot

logger = logging.getLogger("debugger.menu_button_filter")


# noinspection PyPep8Naming
def MenuButtonFilter(*key_parts: str):
    async def filter(message: ma.types.Message) -> bool:
        bot = await ClientBot.get_current()
        data = ma.handler.pick_ctx_data(bot.bot_type).get()
        user, lang = data.get("user"), data.get("lang")

        button_text = await ct(bot, lang, *key_parts)
        # logger.debug(f"? {message.text=} == {button_text=}")
        return message.text == button_text

    return filter
