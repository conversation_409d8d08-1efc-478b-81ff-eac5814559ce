from core import messangers_adapters as ma
from core.keyboards import get_bot_menu_keyboard
from db.models import ClientBot, User, UserClientBotActivity
from utils.text import f


@ma.handler.button("reply", "list")
async def to_main_menu_button_handler(
        query: ma<PERSON>,
        state: ma.<PERSON>ontex<PERSON>,
        user: User,
        bot: ClientBot,
        user_bot_activity: UserClientBotActivity,
        lang: str,
):
    await state.finish()
    await user_bot_activity.update(active_menu_in_store_id=None)
    keyboard = await get_bot_menu_keyboard(user, bot, lang)

    answer_obj = ma.detect_answer_obj(query)
    await answer_obj.answer(
        await f("bot returned to main menu message", lang),
        reply_markup=keyboard,
    )


def register_bot_buttons_handlers(dp: ma.DispatcherType):
    to_main_menu_button_handler.setup(
        dp,
        state="*",
        messangers_kwargs={
            "telegram": {
                "callback_mode": "to_main_menu",
            },
            "whatsapp": {
                "reply_mode": "to_main_menu",
            }
        }
    )
