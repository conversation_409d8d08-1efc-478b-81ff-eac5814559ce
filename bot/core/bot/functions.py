import logging
import re
from typing import Literal

import aiogram as tg
from aiogram import Bo<PERSON>
from aiohttp import ClientSession
from aiowhatsapp import WhatsappApiClient, WhatsappBot, schemas as wa_schemas

import schemas
from db import crud
from db.models import (
    ClientBot, Group, User, UserAdminBotActivity,
    UserClientBotActivity, UserServiceBotActivity,
)
from utils.redefined_classes import Bot
from utils.text import f


async def bot_to_schema(bot: ClientBot) -> schemas.BotSchema:
    return schemas.BotSchema.from_orm(bot)


async def get_tg_bot_info(token: str):
    current_bot = tg.Bot.get_current()
    with current_bot.with_token(token):
        return await current_bot.get_me()


async def create_bot(
        owner: User,
        token: str,
        bot_type: schemas.BotTypeLiteral,
        group: Group | None = None,
        whatsapp_from: str | None = None,
        whatsapp_app_id: str | None = None,
        whatsapp_app_secret: str | None = None,
        is_accepted_agreement: bool = False,
        country_iso_code: str | None = None,
        timezone: str | None = None,
        lang: str | None = None,
        whatsapp_business_account_id: str | None = None,
) -> "ClientBot":
    if bot_type == "telegram":
        tg_bot_user = await get_tg_bot_info(token)
        whatsapp_token_info = None
        whatsapp_phone_number_info = None
    else:
        tg_bot_user = None
        api = WhatsappApiClient(token, whatsapp_from)
        whatsapp_token_info = await api.debug_token(token)
        whatsapp_phone_number_info = await api.get_phone_number_info()

    return await crud.create_bot(
        owner, token,
        bot_type, group,
        whatsapp_from,
        whatsapp_app_id,
        whatsapp_app_secret,
        is_accepted_agreement,
        country_iso_code, timezone, lang,
        tg_bot_user,
        whatsapp_token_info,
        whatsapp_phone_number_info,
        whatsapp_business_account_id=whatsapp_business_account_id,
    )


def convert_empty_to_none(data: dict) -> dict:
    return {k: (None if v == '' else v) for k, v in data.items()}


async def change_bot(
        bot_id: int, bot_type: str, client_bot: ClientBot, lang: str, data: dict
) -> tuple[str | None, bool]:

    keys = (
        "bot_type", "token", "username", "is_started",
        "whatsapp_from", "whatsapp_from_phone_number", "whatsapp_app_id",
        "whatsapp_app_name", "whatsapp_app_secret",
    )
    update_data = convert_empty_to_none({key: data.get(key) for key in keys})

    message_text = None
    if client_bot.bot_type == "whatsapp":
        bot = WhatsappBot(
            client_bot.token,
            client_bot.whatsapp_from,
            client_bot.whatsapp_app_id,
            client_bot.whatsapp_app_secret,
        )
    else:
        bot = Bot(client_bot.token)

    try:
        await bot.delete_webhook()
    except Exception as error:
        logging.error(error, exc_info=True)

    if bot_type == "whatsapp":
        update_data["is_started"] = False
        api = WhatsappApiClient(data["token"], data["whatsapp_from"])
        debug_token = await api.debug_token()
        app_name = debug_token.data.application
        whatsapp_phone_number_info = await api.get_phone_number_info()
        phone_number = "".join(
            re.findall(r"\d", whatsapp_phone_number_info.display_phone_number)
        )
        update_data["whatsapp_from_phone_number"] = phone_number
        update_data["whatsapp_app_name"] = app_name
    else:
        if isinstance(bot, Bot) and bot._token != data.get("token"):
            update_data["is_started"] = False
        tg_bot_info = await get_tg_bot_info(data["token"])
        update_data["username"] = tg_bot_info.username

    is_update_error = False

    try:
        await crud.change_bot(bot_id, update_data)
    except Exception as error:
        logging.error(error, exc_info=True)
        message_text = await f("edit additionally bot update db model error", lang)
        is_update_error = True

    if bot_type == "whatsapp" and not is_update_error:
        async with ClientSession("http://localhost:5419") as session:
            try:
                await session.post("/management/botCreated", json={"bot_id": bot_id})
            except Exception as error:
                logging.error(error, exc_info=True)
                message_text = await f(
                    "edit additionally bot update webhook error", lang
                )
                is_update_error = True

    if not is_update_error:
        message_text = await f("edit additionally bot update success text", lang)

    return message_text, is_update_error


async def setup_webhook(bot_id, lang):
    async with ClientSession("http://localhost:5419") as session:
        try:
            await session.post("/management/botCreated", json={"bot_id": bot_id})
            message_text = await f("edit additionally bot webhook success", lang)
        except Exception as error:
            logging.error(error, exc_info=True)
            message_text = await f("edit additionally bot webhook error", lang)
    return message_text


async def get_user_bot_activity(
        user_id: int, bot_id: int | Literal["root", "admin", "service"]
):
    if bot_id == "root":
        return

    user = await User.get(user_id)
    if not user:
        return None

    match bot_id:
        case "admin":
            return await UserAdminBotActivity.get_or_create(user)
        case "service":
            return await UserServiceBotActivity.get_or_create(user)
        case _:
            bot = await ClientBot.get(int(bot_id))
            if not bot:
                return None
            return await UserClientBotActivity.get(user, bot)


async def register_whatsapp_bot(bot_id: int, pin: str):
    client_bot = await ClientBot.get(bot_id)
    bot = WhatsappBot(client_bot.token, client_bot.whatsapp_from)
    data = wa_schemas.RegisterSentPin(pin=pin)
    response = await bot.register(data)
    return response


async def parse_register_whatsapp_bot(lang: str, response: dict):
    if "error" in response:
        error = response["error"]
        error_data = error.get("error_data")
        details = error_data.get("details", "") if error_data else ""

        error_text = "\n".join([error["message"], details])
        message_text = await f("whatsapp register error", lang, error_text=error_text)

    else:
        message_text = await f("whatsapp register success text", lang)
    return message_text
