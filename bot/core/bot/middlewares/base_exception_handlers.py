from core import messangers_adapters as ma


class BaseExceptionHandlersMiddleware:
    async def on_pre_process_error(self, _update, _error, data: dict):
        data["exception_handler_info"] = {"is_exception_handled": False}

    async def on_process_error(self, _update, _error, data: dict):
        if data["exception_handler_info"]["is_exception_handled"]:
            bot_type = await ma.detect_bot_type()
            raise ma.handler.CancelHandler(bot_type)
