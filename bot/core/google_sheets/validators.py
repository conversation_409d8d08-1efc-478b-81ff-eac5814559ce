import logging
import re

from aiogram import types
from aiogram.dispatcher import FSMContext

from psutils.exceptions import ErrorWithTextVariable
from psutils.forms.validators.base import Validator

from utils.text import f

from .functions import validate_google_sheets

from .keyboards import get_retry_keyboard


class SheetsValidator(Validator):

    async def validate(self, message: types.Message, state: FSMContext, lang: str) -> bool | dict | types.Message:
        sheets_url = re.search(
            "^(?:https?://)?docs.google.com/spreadsheets/d/[-a-zA-Z0-9_-]+/edit\S*",
            message.text, flags=re.IGNORECASE
        )

        if not sheets_url:
            return await message.answer(await f("store brand invalid sheets error", lang))

        sheets_url = sheets_url.group()
        try:
            google_sheet_id = await validate_google_sheets(sheets_url, lang)
        except ErrorWithTextVariable as e:
            logging.error(e, exc_info=True)
            keyboard = await get_retry_keyboard(lang)
            return await message.answer(await f(e.text_variable, lang, **e.text_kwargs), reply_markup=keyboard)

        return dict(sheets_url=sheets_url, google_sheet_id=google_sheet_id)
