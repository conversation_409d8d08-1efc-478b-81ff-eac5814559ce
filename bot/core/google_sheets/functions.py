import logging
import re

from config import PATH_TO_GOOGLE_CREDS_JSON

from gspread.exceptions import APIError

from psutils.google_sheets import AsyncGoogleSheetsClient

from utils.google import limiter, get_service_account_email, resolve_google_api_error

from core.ext.exceptions import GoogleSheetsPermissionError

logger = logging.getLogger("debugger")


async def validate_google_sheets(sheets_url: str, lang: str) -> str:
    try:
        document_id = re.search(r"(?<=/d/)[a-zA-Z0-9_-]+(?=/edit)", sheets_url).group()
        logger.debug(f"{document_id=}")
        client = AsyncGoogleSheetsClient(
            document_id, limiter=limiter,
            creds_file_path=PATH_TO_GOOGLE_CREDS_JSON,
        )
        await client.load_document()
    except APIError as error:
        raise resolve_google_api_error(error, sheets_url, lang) from error
    except PermissionError:
        accessor_email = get_service_account_email(PATH_TO_GOOGLE_CREDS_JSON)
        raise GoogleSheetsPermissionError(
            accessor_email=accessor_email,
        )
    except Exception as error:
        logger.error(f"Error validating google sheets: {error}", exc_info=True)
        raise error
    else:
        return document_id
