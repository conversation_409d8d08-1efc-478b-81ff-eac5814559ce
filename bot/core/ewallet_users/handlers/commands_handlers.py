import logging

import aiogram as tg

import core.messangers_adapters as ma
from db import crud
from db.models import ClientBot, EWallet, User
from utils.text import f
from ..deep_links import EwalletUserDeepLink
from ...bot.ewallet_handlers import ewallet_button_handler
from ...whatsapp.keyboards import get_wa_menu_keyboard

debugger = logging.getLogger("debugger.ewallet_users")


@ma.handler.message()
async def ewallet_user_deep_link_handler(
        message: ma.types.Message,
        state: ma.FSMContext,
        data: EwalletUserDeepLink,
        bot: ClientBot,
        user: User,
):
    await state.finish()
    debugger.debug(f"{data.ewallet_uuid_id=}")
    lang = await user.get_lang(bot) or 'en'
    keyboard = await get_wa_menu_keyboard(user, bot, lang)

    try:
        ewallet = await EWallet.get(uuid_id=data.ewallet_uuid_id)
        if not ewallet:
            raise ValueError("EWallet not found")

        ewallet_user = await crud.create_ewallet_user(ewallet_id=ewallet.id, user_id=user.id)
        if ewallet_user:
            await message.answer(
                await f("bot user got access to ewallet", lang, ewallet_name=ewallet.name), reply_markup=keyboard
            )
            return await ewallet_button_handler(
                message=message, state=state, bot=bot,
                user=user, lang=lang,
            )
        return None

    except Exception as e:
        debugger.error(f"Error while processing deep link: {e}", exc_info=True)
        return await message.answer("Error while processing deep link", reply_markup=keyboard)


def register_ewallet_user_commands_handlers(dp: tg.dispatcher.Dispatcher):
    ewallet_user_deep_link_handler.setup(
        dp,
        EwalletUserDeepLink.get_filter(return_field_name="data"),
        state="*",
    )
