import json
import math
from decimal import Decimal

from incust_api.api import term

import exceptions
import schemas
from config import CRM_HOST, NO_CENT_CURRENCIES
from core.billing.functions import record_billing_transaction_usage
from core.invoice.invoice_to_schema import invoice_to_schema
from core.invoice.service import InvoiceService
from core.kafka.producer.functions import (
    add_invoice_payment_notification, add_push_notifications_for_action,
)
from core.kafka.producer.helpers import build_fcm_message
from core.loyalty.incust_api import incust
from core.webhooks.functions import add_webhook_event, prepare_data_for_payment_webhook
from db import crud
from db.models import (
    Brand, ClientBot, EWallet, EWalletExternalPayment, Group, Invoice,
    User,
)
from loggers import JSONLogger
from schemas import (
    AuthSourceEnum, EWalletExternalPaymentStatus,
    InvoicePaymentModeEnum, InvoiceTypeEnum, WebhookActionEnum, WebhookEntityEnum,
    WebhookPaymentDataSchema,
)
from utils.numbers import format_currency
from utils.platform_admins import send_message_to_platform_admins
from utils.text import fd


def get_loyalty_settings(ewallet_id: int):
    return crud.get_loyalty_settings_for_context(
        "ewallet",
        schemas.LoyaltySettingsData(ewallet_id=ewallet_id),
    )


async def create_ewallet_ext_payment(
        ewallet: EWallet,
        profile: Group,
        user: User,
        data: schemas.BaseCreateEwalletExternalPaymentData,
        lang: str,
):
    logger = JSONLogger(
        "ewallet.ext-payment", {
            "ewallet_id": ewallet.id,
            "profile_id": profile.id,
            "user_id": user.id,
            "data": data,
            "lang": lang,
        }
    )

    discount_amount: Decimal = data.amount / 100 * ewallet.discount_percent
    if ewallet.currency in NO_CENT_CURRENCIES:
        discount_amount = Decimal(math.ceil(discount_amount))

    amount_after_sale = data.amount - discount_amount

    fee = Decimal("0")
    if ewallet.ext_payment_fee_value:
        fee += ewallet.ext_payment_fee_value
    if ewallet.ext_payment_fee_percent:
        fee += amount_after_sale / 100 * ewallet.ext_payment_fee_percent

    if ewallet.currency in NO_CENT_CURRENCIES:
        fee = math.ceil(fee)

    total_amount = amount_after_sale + fee

    code = f"ewextp-{ewallet.id}"

    loyalty_settings = await get_loyalty_settings(ewallet.id)

    async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
        incust_transaction = await api.reserve_check(
            term.m.Check(
                payment_id=ewallet.incust_account_id,
                payment_type="special-account",
                skip_message=False,
                id=user.incust_external_id,
                id_type=term.m.IdType("external-id"),
                amount=total_amount,
                amount_to_pay=total_amount,
                check_items=[
                    term.m.CheckItem(
                        title=f"{ewallet.name} payment",
                        code=code,
                        price=total_amount,
                        quantity=1,
                        amount=total_amount,
                        category=code,
                    )
                ]
            )
        )

    logger.add_data({"incust_transaction": incust_transaction})

    try:
        payment = await crud.create_ewallet_external_payment(
            **data.dict(),
            currency=ewallet.currency,
            ewallet=ewallet,
            profile=profile,
            user=user,
            fee=fee,
            discount_percent=ewallet.discount_percent,
            discount_amount=discount_amount,
            incust_transaction_id=incust_transaction.id,
        )
        logger.add_data({"payment_id": payment.id})
    except Exception as error:
        logger.error("An error occurred creating EWalletExternalPayment", error)

        try:
            async with incust.term.CheckTransactionsApi(
                    loyalty_settings, lang=lang
            ) as api:
                await api.transaction_cancel(
                    term.m.TransactionCancelRequest(
                        transaction_id=incust_transaction.id,
                        comment="Creating external payment failed"
                    )
                )
        except Exception as e:
            logger.error("An error occurred cancelling incust transaction", e)

        raise exceptions.CreatingEwalletExternalPaymentError() from error
    else:
        logger.debug("Successfully created EWalletExternalPayment")

        await send_ewallet_ext_payments_notifications(
            payment, ewallet, user, profile
        )
        return payment


async def set_ewallet_ext_payment_status(
        payment: EWalletExternalPayment,
        user: User,
        new_status: EWalletExternalPaymentStatus,
        lang: str,
        ignore_session_id: int | None = None,
) -> EWalletExternalPayment:
    ewallet = await EWallet.get(payment.ewallet_id)

    logger = JSONLogger(
        "ewallet.ext-payment", {
            "payment_id": payment.id,
            "ewallet_id": ewallet.id,
            "user_id": user.id,
            "new_status": new_status,
            "lang": lang,
        }
    )

    def has_edit_access():
        # return crud.check_access_to_action(
        #     "ewallet_external_payment:edit",
        #     "profile",
        #     payment.profile_id,
        # )
        result = crud.check_access_to_action(
            "crm_ewallet_ext_payment:edit",
            "user",
            user.id,
            {"profile_id": payment.profile_id},
        )
        logger.debug("has_edit_access: action='crm_ewallet_ext_payment:edit'",
            {"profile_id": payment.profile_id, "result": result}
        )
        return result

    change_status_allowed = False

    match new_status:
        case EWalletExternalPaymentStatus.PENDING:
            payer_check = not payment.payer_id
            access_check = await has_edit_access()
            status_check = payment.status == EWalletExternalPaymentStatus.CREATED
            
            logger.debug(f"PENDING status checks: payer_check={payer_check}, access_check={access_check}, status_check={status_check}")
            
            change_status_allowed = payer_check and access_check and status_check
        case EWalletExternalPaymentStatus.CANCELLED:
            change_status_allowed = (
                    user.id == payment.user_id and
                    # status can be set to CANCELLED only from CREATED
                    payment.status == EWalletExternalPaymentStatus.CREATED
            )
        case EWalletExternalPaymentStatus.REJECTED:
            change_status_allowed = (
                    user.id == payment.payer_id or
                    not payment.payer_id and await has_edit_access() and
                    # status can be set to REJECTED only from CREATED and PENDING
                    payment.status in (
                        EWalletExternalPaymentStatus.CREATED,
                        EWalletExternalPaymentStatus.PENDING
                    )
            )
        case EWalletExternalPaymentStatus.SUCCESS:
            change_status_allowed = (
                    user.id == payment.payer_id and
                    # status can be set to SUCCESS only from CREATED and PENDING
                    payment.status in (
                        EWalletExternalPaymentStatus.CREATED,
                        EWalletExternalPaymentStatus.PENDING,
                    )
            )

    if not change_status_allowed:
        raise exceptions.EwalletExternalPaymentChangeStatusForbiddenError(
            new_status.value,
        )

    payment = await crud.set_ewallet_external_payment_status(
        payment, new_status, user
    )

    try:
        match payment.status:
            case EWalletExternalPaymentStatus.SUCCESS:
                loyalty_settings = await get_loyalty_settings(ewallet.id)

                async with incust.term.CheckTransactionsApi(
                        loyalty_settings, lang=lang
                ) as api:
                    incust_result = await api.finalize_check(
                        term.m.FinalizeCheckRequest(
                            id=str(payment.incust_transaction_id),
                            comment=f"EwalletExtPayment #{payment.id} - paid"
                        )
                    )

                # Створення та фіналізація інвойсу для успішної оплати
                invoice = await create_ewallet_ext_payment_invoice(
                    payment, ewallet, lang
                )
                logger.add_data({"invoice_id": invoice.id if invoice else None})
            case (
            EWalletExternalPaymentStatus.CANCELLED |
            EWalletExternalPaymentStatus.REJECTED
            ):
                loyalty_settings = await get_loyalty_settings(ewallet.id)

                async with incust.term.CheckTransactionsApi(
                        loyalty_settings, lang=lang
                ) as api:
                    incust_result = await api.transaction_cancel(
                        term.m.TransactionCancelRequest(
                            transaction_id=payment.incust_transaction_id,
                            comment=f"Transaction was {payment.status.value}"
                        )
                    )

            case _:
                incust_result = None

    except Exception as error:
        logger.error("An error occurred while processing incust payment", error)
        await send_message_to_platform_admins(
            "An error occurred while processing incust payment\n" +
            json.dumps(
                {
                    "payment_id": payment.id,
                    "payment_uuid_id": payment.uuid_id,
                    "user": user.logger_data,
                    "ewallet": ewallet.logger_data,
                }, indent=4
            ),
        )
    else:
        logger.debug(
            f"Payment ${payment.id} status was successfully chanted to "
            f"{new_status.value}",
            {
                "incust_result": incust_result,
            }
        )

    await send_ewallet_ext_payments_notifications(
        payment, ewallet,
        ignore_session_id=ignore_session_id,
    )

    return payment


async def create_ewallet_ext_payment_invoice(
        payment: EWalletExternalPayment,
        ewallet: EWallet,
        lang: str,
) -> Invoice | None:
    """
    Створює оплачений інвойс для успішної зовнішньої оплати EWallet.

    Args:
        payment: Зовнішня оплата EWallet
        ewallet: EWallet, пов'язаний з платежем
        lang: Мова користувача

    Returns:
        Оплачений інвойс
    """
    logger = JSONLogger(
        "ewallet.ext-payment.invoice", {
            "payment_id": payment.id,
            "ewallet_id": ewallet.id,
        }
    )

    profile = await Group.get(payment.profile_id)
    bot = await ClientBot.get(ewallet.bot_id)
    user = await User.get_by_id(payment.user_id)

    invoice_service = InvoiceService(group=profile, user=user)

    code = f"ewextp-{ewallet.id}"

    items = [
        schemas.CreateInvoiceItemData(
            name=f"{ewallet.name} payment",
            category=code,
            quantity=1,
            price=float(payment.amount),
            unit_discount=float(payment.discount_amount),
            item_code=code,
        )
    ]

    try:
        invoice = await invoice_service.create_invoice(
            invoice_type=InvoiceTypeEnum.EWALLET_EXT_PAYMENT,
            payment_mode=InvoicePaymentModeEnum.ENTERED_AMOUNT.value,
            bot=bot,
            items=items,
            ewallet_id=ewallet.id,
            external_transaction_id=payment.uuid_id,
            payer_id=payment.payer_id,
            is_read=True,
        )

        logger.add_data({"invoice_id": invoice.id})

        await invoice.payed(
            payment_method="ewallet",
            payer_fee=round(payment.fee * 100)
        )

        await record_billing_transaction_usage(profile, invoice.sum_to_pay)

        invoice_schema = await invoice_to_schema(invoice, lang, profile)

        webhook_data = await prepare_data_for_payment_webhook(
            invoice_schema, invoice, profile.id, user_id=user.id,
        )
        webhook_result = await add_webhook_event(
            entity=WebhookEntityEnum.PAYMENT,
            entity_id=invoice.id,
            action=WebhookActionEnum.PAID,
            group_id=invoice.group_id,
            data=webhook_data.dict(),
            data_type=WebhookPaymentDataSchema,
        )
        if webhook_result:
            await invoice.update(webhook_result=webhook_result)

        brand = await Brand.get(group_id=profile.id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "ewallet",
            schemas.LoyaltySettingsData(
                ewallet_id=ewallet.id,
            )
        )

        # Відправляємо повідомлення про успішну оплату через Kafka
        await add_invoice_payment_notification(
            group_id=profile.id,
            brand_id=brand.id if brand else None,
            invoice_id=invoice.id,
            invoice_user_id=user.id if user else None,
            terminal_key=loyalty_settings.terminal_api_key if loyalty_settings else None,
            lang=lang,
            menu_in_store_id=invoice.menu_in_store_id,
        )

        logger.debug(
            f"Successfully created and paid invoice {invoice.id} for ewallet external "
            f"payment {payment.id}"
        )

        return invoice
    except Exception as error:
        logger.error(
            f"An error occurred creating invoice for ewallet external payment "
            f"{payment.id}",
            error
        )
        await send_message_to_platform_admins(
            f"An error occurred creating invoice for ewallet external payment:\n"
            f"Payment ID: {payment.id}\n"
            f"Ewallet: {ewallet.name} (ID: {ewallet.id})\n"
            f"Error: {str(error)}"
        )
        return None


async def send_ewallet_ext_payments_notifications(
        payment: EWalletExternalPayment,
        ewallet: EWallet | None = None,
        payment_user: User | None = None,
        profile: Group | None = None,
        bot: ClientBot | None = None,
        ignore_session_id: int | None = None,
):
    logger = JSONLogger(
        "ewallet.ext-payment", {
            "payment_id": payment.id,
            "ewallet_id": ewallet.id if ewallet else None,
            "payment_user_id": payment_user.id if payment_user else None,
            "profile_id": profile.id if profile else None,
            "bot_id": bot.id if bot else None,
            "ignore_session_id": ignore_session_id,
        }
    )

    try:
        assert not ewallet or ewallet.id == payment.ewallet_id, \
            "Ewallet does not match payment ewallet"
        if not ewallet:
            ewallet = await EWallet.get(payment.ewallet_id)

        logger.add_data({"ewallet_id": ewallet.id})

        assert not bot or bot.id == ewallet.bot_id, "Bot does not match ewallet bot"
        assert not payment_user or payment_user.id == payment.user_id, (
            "User does not match payment user"
        )

        if not payment_user:
            payment_user = await User.get_by_id(payment.user_id)
        if not profile:
            profile = await Group.get(payment.profile_id)
        if not bot:
            bot = await ClientBot.get(ewallet.bot_id)

        logger.add_data(
            {
                "payment_user_id": payment_user.id,
                "profile_id": profile.id,
                "bot_id": bot.id if bot else None,
            }
        )

        await send_ewallet_ext_payment_push_notifications(
            payment, ewallet,
            payment_user, profile, bot,
            ignore_session_id=ignore_session_id,
            logger=logger,
        )
    except Exception as e:
        profile = await Group.get(payment.profile_id)
        payment_user = await User.get_by_id(payment.user_id)

        logger.add_data({"profile_id": profile.id, "payment_user_id": payment_user.id})

        logger.error(f"send_ewallet_ext_payments_notifications FAILED", e)
        await send_message_to_platform_admins(
            f"An error occurred while sending ewallet ext payment notifications: "
            f"{str(e)}\n"
            f"Payment id: {payment.id}\n"
            f"Profile: {profile.name}({profile.id})\n"
            f"Payment user: {payment_user.name}({payment_user.id})\n"
        )


async def send_ewallet_ext_payment_push_notifications(
        payment: EWalletExternalPayment,
        ewallet: EWallet,
        payment_user: User,
        group: Group,
        bot: ClientBot | None = None,
        ignore_session_id: int | None = None, *,
        logger: JSONLogger,
):
    try:
        async def get_message(user: User):
            bot_and_group_texts = []
            if bot:
                bot_and_group_texts.append(bot.display_name)
            bot_and_group_texts.append(group.name)

            texts = await fd(
                {
                    "title": {
                        "variable": (
                            f"ewallet ext payment {payment.status.value} "
                            f"notification title"
                        ),
                        "text_kwargs": {
                            "ewallet_name": ewallet.name,
                            "payment_id": payment.id,
                            "amount": format_currency(
                                float(payment.amount),
                                payment.currency,
                                group.lang,
                                group.country_code,
                            ),
                        }
                    },
                    "body": {
                        "variable": "ewallet ext payment notification body",
                        "text_kwargs": {
                            "bot_and_group_name": " | ".join(bot_and_group_texts),
                            "user_name": payment_user.name,
                        }
                    }
                }, user.lang
            )

            return build_fcm_message(
                "ewallet_ext_payment",
                payment.id,
                payment.crm_tag,
                texts["title"].strip(),
                texts["body"].strip(),
                delete_notification=(
                        payment.status != EWalletExternalPaymentStatus.CREATED
                ),
                apns_priority=(
                    "10" if
                    payment.status == EWalletExternalPaymentStatus.CREATED
                    else "5"
                ),
                link=f"{CRM_HOST}/ewalletExtPayment/{payment.id}?"
                     f"listType=inbox&itemIdField=ewalletExtPaymentId"
            )

        return await add_push_notifications_for_action(
            AuthSourceEnum.CRM_WEB, AuthSourceEnum.CRM_APP,
            action="crm_ewallet_ext_payment:read",
            available_data={
                "profile_id": payment.profile_id,
                "ewallet_ext_payment_id": payment.id,
            },
            message=get_message,
            ignore_session_id=ignore_session_id,
        )
    except Exception as e:
        logger.error(
            f"send_ewallet_ext_payment_push_notifications FAILED"
        )
        await send_message_to_platform_admins(
            f"An error occurred while sending ewallet ext payment push notifications: "
            f"{str(e)}\n"
            f"Payment id: {payment.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Payment user: {payment_user.name}({payment_user.id})\n"
        )
