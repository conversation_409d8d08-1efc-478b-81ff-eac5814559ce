import json
import math
from decimal import Decimal

from incust_api.api import term

import exceptions
import schemas
from config import CRM_HOST, NO_CENT_CURRENCIES
from core.billing.functions import record_billing_transaction_usage
from core.invoice.invoice_to_schema import invoice_to_schema
from core.invoice.service import InvoiceService
from core.invoice_loyalty.service import InvoiceLoyaltyService
from core.kafka.producer.functions import (
    add_invoice_payment_notification, add_push_notifications_for_action,
)
from core.kafka.producer.helpers import build_fcm_message
from core.loyalty.incust_api import incust
from core.webhooks.functions import add_webhook_event, prepare_data_for_payment_webhook
from db import crud
from db.models import (
    Brand, ClientBot, EWallet, EWalletExternalPayment, EWalletMerchant, Group, Invoice,
    User,
)
from loggers import JSONLogger
from schemas import (
    AuthSourceEnum, EWalletExternalPaymentStatus,
    InvoicePaymentModeEnum, InvoiceTypeEnum, WebhookActionEnum, WebhookEntityEnum,
    WebhookPaymentDataSchema,
)
from utils.numbers import format_currency
from utils.platform_admins import send_message_to_platform_admins
from utils.text import fd


def get_loyalty_settings(ewallet_id: int):
    return crud.get_loyalty_settings_for_context(
        "ewallet",
        schemas.LoyaltySettingsData(ewallet_id=ewallet_id),
    )


def find_merchant_by_qr(ewallet_id: int, qr_data: str) -> EWalletMerchant | None:
    """
    Знаходить мерчанта по QR-коду

    Args:
        ewallet_id: ID ewallet
        qr_data: Дані QR-коду

    Returns:
        EWalletMerchant або None якщо не знайдено
    """
    # Отримуємо всіх мерчантів для цього ewallet
    # Створюємо простий об'єкт з потрібними атрибутами
    class EwalletMerchantsParams:
        def __init__(self, ewallet_id):
            self.ewallet_id = ewallet_id
            self.search_text = None
            self.offset = None
            self.limit = None

    params = EwalletMerchantsParams(ewallet_id)
    merchants = crud.get_platform_ewallet_merchant_list(params)

    for merchant in merchants:
        if merchant.is_deleted:
            continue

        # Перевіряємо чи QR-код відповідає одному з патернів мерчанта
        for qr_pattern in merchant.qrcodes:
            if qr_data.startswith(qr_pattern) or qr_pattern in qr_data:
                return merchant

    return None


async def get_merchant_loyalty_settings(merchant: EWalletMerchant | None, ewallet_id: int):
    """
    Отримує налаштування лояльності для мерчанта або дефолтні для ewallet

    Args:
        merchant: Мерчант або None
        ewallet_id: ID ewallet

    Returns:
        LoyaltySettings
    """
    # Поки що завжди використовуємо дефолтні налаштування ewallet
    # В майбутньому можна додати підтримку окремих налаштувань лояльності для мерчантів
    return await get_loyalty_settings(ewallet_id)


def get_product_code_and_category(merchant: EWalletMerchant | None, ewallet_id: int, profile_id: int):
    """
    Отримує код товару та категорію для InCust

    Args:
        merchant: Мерчант або None
        ewallet_id: ID ewallet
        profile_id: ID профілю (групи)

    Returns:
        tuple: (product_code, category)
    """
    if merchant and merchant.incust_check_item_code:
        return (
            merchant.incust_check_item_code,
            merchant.incust_check_item_category or f"ewextp-{ewallet_id}"
        )

    # Формуємо код для зовнішньої ewallet оплати: ewextp-{ewallet_id}-{profile_id}-{merchant_id}
    merchant_id = merchant.id if merchant else "unknown"
    product_code = f"ewextp-{ewallet_id}-{profile_id}-{merchant_id}"
    category = f"ewextp-{ewallet_id}"

    return (product_code, category)


async def create_ewallet_ext_payment(
        ewallet: EWallet,
        profile: Group,
        user: User,
        data: schemas.BaseCreateEwalletExternalPaymentData,
        lang: str,
):
    logger = JSONLogger(
        "ewallet.ext-payment", {
            "ewallet_id": ewallet.id,
            "profile_id": profile.id,
            "user_id": user.id,
            "data": data,
            "lang": lang,
        }
    )

    # Розпізнаємо мерчанта по QR-коду
    merchant = find_merchant_by_qr(ewallet.id, data.transfer_data.data)
    logger.add_data({"merchant_id": merchant.id if merchant else None})

    # Отримуємо налаштування лояльності та коди товарів
    loyalty_settings = await get_merchant_loyalty_settings(merchant, ewallet.id)
    product_code, category = get_product_code_and_category(merchant, ewallet.id, profile.id)

    # Обчислюємо суми з урахуванням is_pay_fee_self
    discount_amount: Decimal = data.amount / 100 * ewallet.discount_percent
    if ewallet.currency in NO_CENT_CURRENCIES:
        discount_amount = Decimal(math.ceil(discount_amount))

    amount_after_sale = data.amount - discount_amount

    fee = Decimal("0")
    if ewallet.ext_payment_fee_value:
        fee += ewallet.ext_payment_fee_value
    if ewallet.ext_payment_fee_percent:
        fee += amount_after_sale / 100 * ewallet.ext_payment_fee_percent

    if ewallet.currency in NO_CENT_CURRENCIES:
        fee = math.ceil(fee)

    # Визначаємо суму для оплати мерчанту з урахуванням is_pay_fee_self
    if merchant and merchant.is_pay_fee_self:
        # Мерчант сам платить знижку - до оплати йде сума після знижки
        merchant_payment_amount = amount_after_sale
        platform_discount_amount = Decimal("0")
        merchant_discount_amount = discount_amount
    else:
        # Платформа платить знижку - до оплати йде повна сума
        merchant_payment_amount = data.amount
        platform_discount_amount = discount_amount
        merchant_discount_amount = Decimal("0")

    total_amount = merchant_payment_amount + fee

    # Спочатку створюємо інвойс
    bot = await ClientBot.get(ewallet.bot_id)
    invoice_service = InvoiceService(group=profile, user=user)

    items = [
        schemas.CreateInvoiceItemData(
            name=f"{ewallet.name} payment" + (f" - {merchant.name}" if merchant else ""),
            category=category,
            quantity=1,
            price=float(data.amount),
            unit_discount=float(platform_discount_amount),
            item_code=product_code,
        )
    ]

    invoice = await invoice_service.create_invoice(
        invoice_type=InvoiceTypeEnum.EWALLET_EXT_PAYMENT,
        payment_mode=InvoicePaymentModeEnum.ENTERED_AMOUNT.value,
        bot=bot,
        items=items,
        ewallet_id=ewallet.id,
        is_read=True,
    )

    logger.add_data({"invoice_id": invoice.id})

    # Тепер робимо reserve_check з лояльністю (як в оригінальному коді)
    async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
        incust_transaction = await api.reserve_check(
            term.m.Check(
                payment_id=ewallet.incust_account_id,
                payment_type="special-account",
                skip_message=False,
                id=user.incust_external_id,
                id_type=term.m.IdType("external-id"),
                amount=total_amount,
                amount_to_pay=total_amount,
                check_items=[
                    term.m.CheckItem(
                        title=f"{ewallet.name} payment" + (f" - {merchant.name}" if merchant else ""),
                        code=product_code,
                        price=total_amount,
                        quantity=1,
                        amount=total_amount,
                        category=category,
                    )
                ]
            )
        )

    if not incust_transaction or not incust_transaction.id:
        # Якщо не вдалося зарезервувати лояльність, видаляємо інвойс
        await invoice.delete()
        raise exceptions.CreatingEwalletExternalPaymentError("Failed to reserve loyalty transaction")

    # Оновлюємо інвойс з loyalty_settings_id та incust_transaction_id
    await invoice.update(
        loyalty_settings_id=loyalty_settings.id if loyalty_settings else None,
        incust_transaction_id=incust_transaction.id,
    )

    logger.add_data({"incust_transaction_id": incust_transaction.id})

    try:
        payment = await crud.create_ewallet_external_payment(
            **data.dict(),
            currency=ewallet.currency,
            ewallet=ewallet,
            profile=profile,
            user=user,
            fee=fee,
            discount_percent=ewallet.discount_percent,
            discount_amount=platform_discount_amount,  # Тільки знижка платформи
            incust_transaction_id=incust_transaction.id,
            invoice_id=invoice.id,
            merchant_id=merchant.id if merchant else None,
        )
        logger.add_data({"payment_id": payment.id})
    except Exception as error:
        logger.error("An error occurred creating EWalletExternalPayment", error)

        try:
            # Скасовуємо транзакцію лояльності
            async with incust.term.CheckTransactionsApi(
                    loyalty_settings, lang=lang
            ) as api:
                await api.transaction_cancel(
                    term.m.TransactionCancelRequest(
                        transaction_id=incust_transaction.id,
                        comment="Creating external payment failed"
                    )
                )
            # Видаляємо інвойс
            await invoice.delete()
        except Exception as e:
            logger.error("An error occurred cancelling incust transaction", e)

        raise exceptions.CreatingEwalletExternalPaymentError() from error
    else:
        logger.debug("Successfully created EWalletExternalPayment")

        await send_ewallet_ext_payments_notifications(
            payment, ewallet, user, profile
        )
        return payment


async def set_ewallet_ext_payment_status(
        payment: EWalletExternalPayment,
        user: User,
        new_status: EWalletExternalPaymentStatus,
        lang: str,
        ignore_session_id: int | None = None,
) -> EWalletExternalPayment:
    ewallet = await EWallet.get(payment.ewallet_id)

    logger = JSONLogger(
        "ewallet.ext-payment", {
            "payment_id": payment.id,
            "ewallet_id": ewallet.id,
            "user_id": user.id,
            "new_status": new_status,
            "lang": lang,
        }
    )

    def has_edit_access():
        # return crud.check_access_to_action(
        #     "ewallet_external_payment:edit",
        #     "profile",
        #     payment.profile_id,
        # )
        result = crud.check_access_to_action(
            "crm_ewallet_ext_payment:edit",
            "user",
            user.id,
            {"profile_id": payment.profile_id},
        )
        logger.debug("has_edit_access: action='crm_ewallet_ext_payment:edit'",
            {"profile_id": payment.profile_id, "result": result}
        )
        return result

    change_status_allowed = False

    match new_status:
        case EWalletExternalPaymentStatus.PENDING:
            payer_check = not payment.payer_id
            access_check = await has_edit_access()
            status_check = payment.status == EWalletExternalPaymentStatus.CREATED
            
            logger.debug(f"PENDING status checks: payer_check={payer_check}, access_check={access_check}, status_check={status_check}")
            
            change_status_allowed = payer_check and access_check and status_check
        case EWalletExternalPaymentStatus.CANCELLED:
            change_status_allowed = (
                    user.id == payment.user_id and
                    # status can be set to CANCELLED only from CREATED
                    payment.status == EWalletExternalPaymentStatus.CREATED
            )
        case EWalletExternalPaymentStatus.REJECTED:
            change_status_allowed = (
                    user.id == payment.payer_id or
                    not payment.payer_id and await has_edit_access() and
                    # status can be set to REJECTED only from CREATED and PENDING
                    payment.status in (
                        EWalletExternalPaymentStatus.CREATED,
                        EWalletExternalPaymentStatus.PENDING
                    )
            )
        case EWalletExternalPaymentStatus.SUCCESS:
            change_status_allowed = (
                    user.id == payment.payer_id and
                    # status can be set to SUCCESS only from CREATED and PENDING
                    payment.status in (
                        EWalletExternalPaymentStatus.CREATED,
                        EWalletExternalPaymentStatus.PENDING,
                    )
            )

    if not change_status_allowed:
        raise exceptions.EwalletExternalPaymentChangeStatusForbiddenError(
            new_status.value,
        )

    payment = await crud.set_ewallet_external_payment_status(
        payment, new_status, user
    )

    try:
        # Отримуємо інвойс та налаштування лояльності
        invoice = await Invoice.get(payment.invoice_id) if payment.invoice_id else None
        loyalty_settings = None

        if invoice and invoice.loyalty_settings_id:
            from db.models import LoyaltySettings
            loyalty_settings = await LoyaltySettings.get(invoice.loyalty_settings_id)
        else:
            loyalty_settings = await get_loyalty_settings(ewallet.id)

        match payment.status:
            case EWalletExternalPaymentStatus.SUCCESS:
                if invoice:
                    # Використовуємо InvoiceLoyaltyService для фіналізації лояльності
                    loyalty_service = InvoiceLoyaltyService()
                    incust_result = await loyalty_service.finalize_invoice_loyalty(
                        invoice=invoice,
                        loyalty_settings=loyalty_settings,
                        is_cancel=False,
                        lang=lang,
                    )

                    # Позначаємо інвойс як оплачений
                    await invoice.update(is_paid=True)
                    logger.add_data({"invoice_id": invoice.id})
                else:
                    # Fallback для старих платежів без інвойсу
                    async with incust.term.CheckTransactionsApi(
                            loyalty_settings, lang=lang
                    ) as api:
                        incust_result = await api.finalize_check(
                            term.m.FinalizeCheckRequest(
                                id=str(payment.incust_transaction_id),
                                comment=f"EwalletExtPayment #{payment.id} - paid"
                            )
                        )

            case (
            EWalletExternalPaymentStatus.CANCELLED |
            EWalletExternalPaymentStatus.REJECTED
            ):
                if invoice:
                    # Використовуємо InvoiceLoyaltyService для скасування лояльності
                    loyalty_service = InvoiceLoyaltyService()
                    incust_result = await loyalty_service.finalize_invoice_loyalty(
                        invoice=invoice,
                        loyalty_settings=loyalty_settings,
                        is_cancel=True,
                        lang=lang,
                    )

                    # Видаляємо інвойс
                    await invoice.delete()
                else:
                    # Fallback для старих платежів без інвойсу
                    async with incust.term.CheckTransactionsApi(
                            loyalty_settings, lang=lang
                    ) as api:
                        incust_result = await api.transaction_cancel(
                            term.m.TransactionCancelRequest(
                                transaction_id=payment.incust_transaction_id,
                                comment=f"Transaction was {payment.status.value}"
                            )
                        )

            case _:
                incust_result = None

    except Exception as error:
        logger.error("An error occurred while processing incust payment", error)
        await send_message_to_platform_admins(
            "An error occurred while processing incust payment\n" +
            json.dumps(
                {
                    "payment_id": payment.id,
                    "payment_uuid_id": payment.uuid_id,
                    "user": user.logger_data,
                    "ewallet": ewallet.logger_data,
                }, indent=4
            ),
        )
    else:
        logger.debug(
            f"Payment ${payment.id} status was successfully chanted to "
            f"{new_status.value}",
            {
                "incust_result": incust_result,
            }
        )

    await send_ewallet_ext_payments_notifications(
        payment, ewallet,
        ignore_session_id=ignore_session_id,
    )

    return payment


async def create_ewallet_ext_payment_invoice(
        payment: EWalletExternalPayment,
        ewallet: EWallet,
        lang: str,
) -> Invoice | None:
    """
    Створює оплачений інвойс для успішної зовнішньої оплати EWallet.

    Args:
        payment: Зовнішня оплата EWallet
        ewallet: EWallet, пов'язаний з платежем
        lang: Мова користувача

    Returns:
        Оплачений інвойс
    """
    logger = JSONLogger(
        "ewallet.ext-payment.invoice", {
            "payment_id": payment.id,
            "ewallet_id": ewallet.id,
        }
    )

    profile = await Group.get(payment.profile_id)
    bot = await ClientBot.get(ewallet.bot_id)
    user = await User.get_by_id(payment.user_id)

    invoice_service = InvoiceService(group=profile, user=user)

    code = f"ewextp-{ewallet.id}"

    items = [
        schemas.CreateInvoiceItemData(
            name=f"{ewallet.name} payment",
            category=code,
            quantity=1,
            price=float(payment.amount),
            unit_discount=float(payment.discount_amount),
            item_code=code,
        )
    ]

    try:
        invoice = await invoice_service.create_invoice(
            invoice_type=InvoiceTypeEnum.EWALLET_EXT_PAYMENT,
            payment_mode=InvoicePaymentModeEnum.ENTERED_AMOUNT.value,
            bot=bot,
            items=items,
            ewallet_id=ewallet.id,
            external_transaction_id=payment.uuid_id,
            payer_id=payment.payer_id,
            is_read=True,
        )

        logger.add_data({"invoice_id": invoice.id})

        await invoice.payed(
            payment_method="ewallet",
            payer_fee=round(payment.fee * 100)
        )

        await record_billing_transaction_usage(profile, invoice.sum_to_pay)

        invoice_schema = await invoice_to_schema(invoice, lang, profile)

        webhook_data = await prepare_data_for_payment_webhook(
            invoice_schema, invoice, profile.id, user_id=user.id,
        )
        webhook_result = await add_webhook_event(
            entity=WebhookEntityEnum.PAYMENT,
            entity_id=invoice.id,
            action=WebhookActionEnum.PAID,
            group_id=invoice.group_id,
            data=webhook_data.dict(),
            data_type=WebhookPaymentDataSchema,
        )
        if webhook_result:
            await invoice.update(webhook_result=webhook_result)

        brand = await Brand.get(group_id=profile.id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "ewallet",
            schemas.LoyaltySettingsData(
                ewallet_id=ewallet.id,
            )
        )

        # Відправляємо повідомлення про успішну оплату через Kafka
        await add_invoice_payment_notification(
            group_id=profile.id,
            brand_id=brand.id if brand else None,
            invoice_id=invoice.id,
            invoice_user_id=user.id if user else None,
            terminal_key=loyalty_settings.terminal_api_key if loyalty_settings else None,
            lang=lang,
            menu_in_store_id=invoice.menu_in_store_id,
        )

        logger.debug(
            f"Successfully created and paid invoice {invoice.id} for ewallet external "
            f"payment {payment.id}"
        )

        return invoice
    except Exception as error:
        logger.error(
            f"An error occurred creating invoice for ewallet external payment "
            f"{payment.id}",
            error
        )
        await send_message_to_platform_admins(
            f"An error occurred creating invoice for ewallet external payment:\n"
            f"Payment ID: {payment.id}\n"
            f"Ewallet: {ewallet.name} (ID: {ewallet.id})\n"
            f"Error: {str(error)}"
        )
        return None


async def send_ewallet_ext_payments_notifications(
        payment: EWalletExternalPayment,
        ewallet: EWallet | None = None,
        payment_user: User | None = None,
        profile: Group | None = None,
        bot: ClientBot | None = None,
        ignore_session_id: int | None = None,
):
    logger = JSONLogger(
        "ewallet.ext-payment", {
            "payment_id": payment.id,
            "ewallet_id": ewallet.id if ewallet else None,
            "payment_user_id": payment_user.id if payment_user else None,
            "profile_id": profile.id if profile else None,
            "bot_id": bot.id if bot else None,
            "ignore_session_id": ignore_session_id,
        }
    )

    try:
        assert not ewallet or ewallet.id == payment.ewallet_id, \
            "Ewallet does not match payment ewallet"
        if not ewallet:
            ewallet = await EWallet.get(payment.ewallet_id)

        logger.add_data({"ewallet_id": ewallet.id})

        assert not bot or bot.id == ewallet.bot_id, "Bot does not match ewallet bot"
        assert not payment_user or payment_user.id == payment.user_id, (
            "User does not match payment user"
        )

        if not payment_user:
            payment_user = await User.get_by_id(payment.user_id)
        if not profile:
            profile = await Group.get(payment.profile_id)
        if not bot:
            bot = await ClientBot.get(ewallet.bot_id)

        logger.add_data(
            {
                "payment_user_id": payment_user.id,
                "profile_id": profile.id,
                "bot_id": bot.id if bot else None,
            }
        )

        await send_ewallet_ext_payment_push_notifications(
            payment, ewallet,
            payment_user, profile, bot,
            ignore_session_id=ignore_session_id,
            logger=logger,
        )
    except Exception as e:
        profile = await Group.get(payment.profile_id)
        payment_user = await User.get_by_id(payment.user_id)

        logger.add_data({"profile_id": profile.id, "payment_user_id": payment_user.id})

        logger.error(f"send_ewallet_ext_payments_notifications FAILED", e)
        await send_message_to_platform_admins(
            f"An error occurred while sending ewallet ext payment notifications: "
            f"{str(e)}\n"
            f"Payment id: {payment.id}\n"
            f"Profile: {profile.name}({profile.id})\n"
            f"Payment user: {payment_user.name}({payment_user.id})\n"
        )


async def send_ewallet_ext_payment_push_notifications(
        payment: EWalletExternalPayment,
        ewallet: EWallet,
        payment_user: User,
        group: Group,
        bot: ClientBot | None = None,
        ignore_session_id: int | None = None, *,
        logger: JSONLogger,
):
    try:
        async def get_message(user: User):
            bot_and_group_texts = []
            if bot:
                bot_and_group_texts.append(bot.display_name)
            bot_and_group_texts.append(group.name)

            texts = await fd(
                {
                    "title": {
                        "variable": (
                            f"ewallet ext payment {payment.status.value} "
                            f"notification title"
                        ),
                        "text_kwargs": {
                            "ewallet_name": ewallet.name,
                            "payment_id": payment.id,
                            "amount": format_currency(
                                float(payment.amount),
                                payment.currency,
                                group.lang,
                                group.country_code,
                            ),
                        }
                    },
                    "body": {
                        "variable": "ewallet ext payment notification body",
                        "text_kwargs": {
                            "bot_and_group_name": " | ".join(bot_and_group_texts),
                            "user_name": payment_user.name,
                        }
                    }
                }, user.lang
            )

            return build_fcm_message(
                "ewallet_ext_payment",
                payment.id,
                payment.crm_tag,
                texts["title"].strip(),
                texts["body"].strip(),
                delete_notification=(
                        payment.status != EWalletExternalPaymentStatus.CREATED
                ),
                apns_priority=(
                    "10" if
                    payment.status == EWalletExternalPaymentStatus.CREATED
                    else "5"
                ),
                link=f"{CRM_HOST}/ewalletExtPayment/{payment.id}?"
                     f"listType=inbox&itemIdField=ewalletExtPaymentId"
            )

        return await add_push_notifications_for_action(
            AuthSourceEnum.CRM_WEB, AuthSourceEnum.CRM_APP,
            action="crm_ewallet_ext_payment:read",
            available_data={
                "profile_id": payment.profile_id,
                "ewallet_ext_payment_id": payment.id,
            },
            message=get_message,
            ignore_session_id=ignore_session_id,
        )
    except Exception as e:
        logger.error(
            f"send_ewallet_ext_payment_push_notifications FAILED"
        )
        await send_message_to_platform_admins(
            f"An error occurred while sending ewallet ext payment push notifications: "
            f"{str(e)}\n"
            f"Payment id: {payment.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Payment user: {payment_user.name}({payment_user.id})\n"
        )
