from psutils.text import replace_variables_in_text

from core.bot.ewallet_handlers import process_single_ewallet
from core.notifications.base_notifications_sender import BaseNotificationsSender
from db.models import Brand, ClientBot, EWallet, EWalletExternalPayment, Group, User
from schemas import BaseTemplateSchema, EWalletExternalPaymentStatus
from schemas.incust.base import CouponShowData
from utils.numbers import format_currency
from utils.text import fd


class EwalletExtPaymentTemplate(BaseTemplateSchema):
    TEMPLATE_PATH = "ewallet_ext_payment_email.html"

    header: str
    message: str

    ewallet_info: str

    from_name: str
    coupons: list[CouponShowData] | None = None


class EwalletExtPaymentNotificationsSender(
    BaseNotificationsSender[EwalletExtPaymentTemplate]
):
    BOT_TEMPLATE_PATH = "ewallet_ext_payment_messanger.html"

    def __init__(
            self,
            payment: EWalletExternalPayment,
            ewallet: EWallet,
            lang: str,
            brand: Brand,
            group: Group,
            user: User,
            bot: ClientBot | None = None,
    ):
        super().__init__(lang, brand, group, user, bot)
        self.ewallet = ewallet
        self.payment = payment

    async def build_template(self) -> EwalletExtPaymentTemplate:
        if self.payment.status not in (
                EWalletExternalPaymentStatus.REJECTED,
                EWalletExternalPaymentStatus.SUCCESS,
        ):
            raise ValueError(
                f"Only REJECTED, SUCCESS payments supported, not {self.payment.status}"
            )

        texts = await fd(
            {
                "header": f"ewallet ext payment {self.payment.status.value} user "
                          f"notification header",
                "message": f"ewallet ext payment {self.payment.status.value} user "
                           f"notification message"
            },
            self.lang,
        )

        text_kwargs = {
            "name": self.user.name,
            "payment_id": self.payment.id,
            "amount": format_currency(
                float(self.payment.amount),
                self.payment.currency,
                self.group.lang,
                self.group.country_code,
            )
        }

        res = await process_single_ewallet(self.ewallet, self.user, self.lang)

        return EwalletExtPaymentTemplate(
            header=replace_variables_in_text(
                texts["header"], text_kwargs
            ),
            message=replace_variables_in_text(
                texts["message"], text_kwargs
            ),
            ewallet_info=res.message,
            from_name=self.group.name,
        )
