import datetime
import json
from typing import Literal, Optional

from aiohttp import ContentTypeError
from fastapi.exceptions import HTTPException
from incust_api.api import term
from psutils.exceptions import ErrorWithTextVariable

import schemas
from core import messangers_adapters as ma
from core.admin_notification.service import (
    notify_all_channels,
)
from core.bot.ewallet_handlers import process_single_ewallet
from core.ewallet.payment.exceptions import (
    EWalletPaymentCountryNotSupportedError,
    EWalletPaymentEWalletNotFoundError,
    EWalletPaymentIncustNotFoundError, EWalletPaymentInsufficientFundsError,
    EWalletPaymentNotFoundError,
    EWalletPaymentProfileNotFoundError, EWalletPaymentStatusError,
)
from core.ewallet.payment.keyboards import get_yes_or_no_keyboard
from core.friend.functions import send_with_wa_main_menu
from core.incust.exceptions import IncustError
from core.invoice import finalize_payment
from core.invoice.exception import (
    InvoicePaymentInvoicePayedError,
    InvoicePaymentInvoiceStatusError,
)
from core.loyalty.customer_service import get_or_create_incust_customer
from core.loyalty.incust_api import incust
from core.messangers_adapters import FSMContext, types
from core.payment.funcs import get_translated_post_payment_info
from core.payment.incust_pay.exceptions import BusinessPaymentError
from core.payment.incust_pay.funcs import (
    make_business_payment,
)
from core.webhooks.functions import (
    add_user_to_webhook_data, add_webhook_event,
)
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db import crud
from db.models import (
    Brand, BusinessPaymentSetting, ClientBot, EWallet, EWalletPayment, Group, Invoice,
    LoyaltySettings, Payment, PaymentSettings, StoreOrder, User,
)
from db.models.store.payment_settings import ObjectPaymentSettings, StoreOrderPayment
from loggers import JSONLogger
from schemas import (
    EWalletPaymentStatus,
    IncustPayMerchantData,
    NotificationLevel, PaymentCallBackData,
    SSEChannelTarget, WebhookEWalletPaymentDataSchema,
)
from utils.numbers import format_currency
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f, html_to_markdown
from utils.translator import t


class EwalletPaymentProcessor:
    def __init__(
            self,
            query: ma.ButtonQuery | ma.Message,
            state: FSMContext,
            bot: ClientBot,
            user: User,
            lang: str,
            keyboard: Optional[types.Keyboard] = None,
            ewallet_payment_id: Optional[str | int] = None,
            is_deeplink: bool = False,
    ):
        self.query = query
        self.state = state
        self.bot = bot
        self.user = user
        self.lang = lang
        self.main_keyboard = keyboard
        self.ewallet_payment_id = ewallet_payment_id
        self.debugger = self._create_debugger()

        self.profile: Group | None = None
        self.ewallet: EWallet | None = None
        self.ewallet_payment = None
        self.payment_id = None
        self.amount = None
        self.is_deeplink = is_deeplink

    @property
    def answer_obj(self):
        return ma.detect_answer_obj(self.query)

    def _create_debugger(self) -> JSONLogger:
        """Create a JSON logger with initial context."""
        return JSONLogger(
            "ewallet_payment", {
                "group_id": self.bot.group_id,
                "user_first_name": self.user.first_name,
                "user_last_name": self.user.last_name,
                "user_id": self.user.id,
                "bot_id": self.bot.id,
                "bot_name": self.bot.display_name,
                "main_keyboard_type": str(type(self.main_keyboard)),
            }
        )

    async def validate_payment_prerequisites(self):
        """Validate all prerequisites for payment processing."""
        # Validate EWallet Payment
        if (
                isinstance(self.ewallet_payment_id, str) and
                len(self.ewallet_payment_id) >= 32
        ):
            ewallet_payment = await EWalletPayment.get(uuid_id=self.ewallet_payment_id)
        else:
            ewallet_payment = await EWalletPayment.get(self.ewallet_payment_id)

        if not ewallet_payment:
            raise EWalletPaymentNotFoundError(message=self.ewallet_payment_id)
        self.ewallet_payment = ewallet_payment

        if ewallet_payment.status not in (
                EWalletPaymentStatus.CREATED, EWalletPaymentStatus.PENDING):
            raise EWalletPaymentStatusError(
                message=self.ewallet_payment_id, status=ewallet_payment.status
            )

        # Validate EWallet
        ewallet = await EWallet.get(ewallet_payment.ewallet_id)
        if not ewallet:
            raise EWalletPaymentEWalletNotFoundError(message=self.ewallet_payment_id)
        self.ewallet = ewallet

        # if self.is_deeplink and ewallet.bot_id != self.bot.id:
        #     raise EWalletPaymentInvalidUrlError(f"{ewallet.bot_id=} !={self.bot.id=}")

        # Validate Profile
        profile = await Group.get(id=ewallet_payment.profile_id, status="enabled")
        if not profile:
            raise EWalletPaymentProfileNotFoundError(message=f"{self.bot.group_id}")
        self.profile = profile

        # Validate Country
        if profile.country_code not in ewallet.countries:
            raise EWalletPaymentCountryNotSupportedError(message=profile.country_code)

        # Validate API Configuration
        if not any([ewallet.terminal_api_key, ewallet.server_api_url]):
            raise EWalletPaymentIncustNotFoundError(
                message=f"{ewallet.terminal_api_key=}, {ewallet.server_api_url=}"
            )

        return ewallet_payment, ewallet, profile

    async def _prepare_payment_data(self):
        """Prepare payment and invoice data."""
        brand = await crud.get_brand_by_group(self.ewallet_payment.profile_id)
        self.debugger.add_data({"brand_id": brand.id, "brand_name": brand.name})

        invoice, store_order, amount_to_pay = None, None, self.ewallet_payment.amount

        payment = await Payment.get(uuid_id=self.ewallet_payment.external_id)
        if payment:
            self.payment_id = payment.id
            invoice = await Invoice.get(payment.invoice_id)

            # Validate Invoice Status
            if invoice.status == "payed":
                raise InvoicePaymentInvoicePayedError(invoice.id)
            if invoice.status != "not_payed":
                raise InvoicePaymentInvoiceStatusError(invoice.id, invoice.status)

            # Get Store Order Details
            store_order = await StoreOrder.get(invoice_id=invoice.id)

            if store_order:
                # Determine Payment Amount
                amount_to_pay = (
                    store_order.total_sum_with_extra_fee
                    if store_order
                    else invoice.total_sum_with_extra_fee
                )

        return brand, payment, invoice, store_order, amount_to_pay

    async def _execute_payment(
            self, payment: Payment | None = None
    ):
        """Execute the actual payment process."""
        invoice, json_data, post_payment_info = None, None, None

        if not payment:  # if external ewallet payment
            payment_settings = await PaymentSettings.get(
                id=self.ewallet_payment.payment_settings_id,
                is_deleted=False, is_enabled=True
            )
            json_data = payment_settings.json_data
            # Отримуємо переклад для PaymentSettings
            post_payment_info = await get_translated_post_payment_info(
                self.bot.group_id, payment_settings, None, self.lang,
            )
        else:
            if payment.invoice_id:
                invoice = await Invoice.get(payment.invoice_id)

            payment_settings = await PaymentSettings.get(
                id=payment.payment_settings_id, is_deleted=False, is_enabled=True
            )
            if payment_settings:
                json_data = payment_settings.json_data

            if payment.object_payment_settings_id:
                obj_payment_settings = await ObjectPaymentSettings.get(
                    id=payment.object_payment_settings_id, is_deleted=False,
                    is_enabled=True,
                )
                if obj_payment_settings and obj_payment_settings.json_data:
                    json_data = obj_payment_settings.json_data

                    # Отримуємо переклад post_payment_info з підтримкою
                    # ObjectPaymentSettings
                    post_payment_info = await get_translated_post_payment_info(
                        self.bot.group_id, payment_settings, obj_payment_settings,
                        self.lang,
                    )

        payment_title = f"{self.ewallet.name} #{self.ewallet_payment.id}"

        payment_data = schemas.PaymentCallBackData(
            payment_uuid=self.ewallet_payment.uuid_id,
            payment_method="ewallet",
            incust_server_api_url=self.ewallet.server_api_url,
            terminal_api_key=self.ewallet.terminal_api_key,
            ewallet_id=self.ewallet.id,
        )

        # Prepare base data and merchant data
        base_data = self._prepare_base_data()
        merchant_data = [
            schemas.IncustPayMerchantData(**md)
            for md in json_data.get("merchant_data", []) or []
        ] if json_data else None

        business_payment_settings = await BusinessPaymentSetting.get(
            incust_account_id=self.ewallet.incust_account_id,
            is_enabled=True, is_deleted=False
        )
        if not business_payment_settings:
            merchant_data = []

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "ewallet",
            schemas.LoyaltySettingsData(
                ewallet_id=self.ewallet.id,
            )
        )

        await self.process_make_incust_check(
            base_data,
            payment_data,
            "ewallet",
            loyalty_settings,
            None,
            merchant_data,
            self.ewallet_payment.description,
            skip_incust_message=True,
        )

        await self.ewallet_payment.update(
            paid_datetime=datetime.datetime.now(),
            status=EWalletPaymentStatus.PAID,
            paid_user_id=self.user.id,
        )

        if invoice:
            try:
                await self._process_store_order_payment(
                    payment_settings.id, invoice.id,
                    business_payment_settings.id if business_payment_settings else None,
                    payment_data.business_payment_result, json_data,
                    post_payment_info, "ewallet", self.ewallet.name,

                )
            except Exception as err:
                self.debugger.error(
                    f"ewallet: process_make_incust_check -> {err=}", exc_info=True
                )
                pass

        return payment_data, payment_title

    async def end_payment(
            self, brand: Brand, invoice: Invoice, payment: Payment,
            payment_data: PaymentCallBackData, payment_title: str,
            store_order: StoreOrder | None = None,
    ):
        if invoice:
            await finalize_payment(payment_data, invoice, payment, brand, store_order)

        if payment and self.is_deeplink:
            await notify_all_channels(
                SSEChannelTarget.PAYMENT_STATUS,
                "paid",
                key=f"{self.profile.id}-{self.ewallet_payment.external_id}",
                payment_id=payment.id,
                title=payment_title,
                status=payment.status,
                level=NotificationLevel.INFO.value,
            )

        schema = WebhookEWalletPaymentDataSchema(
            external_id=self.ewallet_payment.external_id,
            status=self.ewallet_payment.status.value,
            paid_datetime=self.ewallet_payment.paid_datetime,
            ewallet_payment_uuid=self.ewallet_payment.uuid_id,
            error=self.ewallet_payment.error,
            payment_settings_id=self.ewallet_payment.payment_settings_id,
        )
        webhook_data = await add_user_to_webhook_data(self.user, schema, self.profile)
        await add_webhook_event(
            entity=schemas.WebhookEntityEnum.EWALLET_PAYMENT,
            entity_id=self.ewallet_payment.id,
            action=schemas.WebhookActionEnum.PAID,
            group_id=self.profile.id,
            data=webhook_data.dict(),
            data_type=schemas.WebhookEWalletPaymentDataSchema,
        )

    def _prepare_base_data(self, ):
        """Prepare base data for payment processing."""
        base_data = {
            "payment_id": self.ewallet.incust_account_id,
            "payment_type": "special-account",
            "skip_message": False,
            "special_account_pin": None,
            "odometer": None,
            "vehicle_id": None,
            "id": self.user.incust_external_id,
            "id_type": "external-id"
        }
        return base_data

    async def confirm_ewallet_payment(self, ):
        """Confirm ewallet payment."""

        ewallet_payment, ewallet, profile = await self.validate_payment_prerequisites()

        available_amount, amount_str, available_amount_str = await (
            self._check_available_amount()
        )

        if available_amount is False:
            text = available_amount_str
            if self.bot.bot_type == 'whatsapp':
                text = html_to_markdown(text)
                keyboard = await get_wa_menu_keyboard(self.user, self.bot, self.lang)
            else:
                keyboard = None
            return await self.answer_obj.answer(
                text, reply_markup=keyboard
            )

        ewallet_payment.status = EWalletPaymentStatus.PENDING

        if self.is_deeplink:
            await notify_all_channels(
                SSEChannelTarget.PAYMENT_STATUS,
                "pending",
                key=f"{self.profile.id}-{self.ewallet_payment.external_id}",
                payment_id=self.payment_id,
                title="pending",
                status="pending",
                level=NotificationLevel.INFO.value,
            )

        kwargs = {
            "description": ewallet_payment.description,
            "amount_to_pay": amount_str,
            "available_amount": available_amount_str,
            "name": profile.name,
            "ewallet_name": ewallet.name,
        }

        text = await f(
            "client bot ewallet payment confirm text", self.lang,
            **kwargs,
        )

        if self.bot.bot_type == 'whatsapp':
            text = html_to_markdown(text)

        return await self.answer_obj.answer(
            text,
            reply_markup=await get_yes_or_no_keyboard(
                self.lang,
                ewallet_payment_id=self.ewallet_payment.id,
                bot_type=self.bot.bot_type,
                is_deeplink=self.is_deeplink,
            )
        )

    async def _check_available_amount(self):
        amount_str = format_currency(
            round(self.ewallet_payment.amount / 100, 2), self.ewallet_payment.currency,
            locale=self.profile.lang, territory=self.profile.country_code,
        )

        ewallet_info = await process_single_ewallet(self.ewallet, self.user, self.lang)

        available_amount = ewallet_info.available_amount

        if available_amount:
            available_amount_str = format_currency(
                available_amount, self.ewallet_payment.currency,
                locale=self.profile.lang, territory=self.profile.country_code,
            )
            if available_amount < round(
                    self.ewallet_payment.amount / 100, 2
            ):
                raise EWalletPaymentInsufficientFundsError(
                    message="Insufficient funds", available_amount=available_amount_str,
                    amount=amount_str
                )
        else:
            if self.lang == self.ewallet.lang:
                info_text = self.ewallet.info
            else:
                translated = await t(
                    self.ewallet,
                    self.lang, self.ewallet.lang,
                    group_id="internal",
                    is_auto_translate_allowed=False,
                )
                info_text = translated.get("info") if translated else None
                info_text = info_text or self.ewallet.info

            if ewallet_info.available_amount is False and not info_text:
                raise EWalletPaymentInsufficientFundsError(
                    message="Insufficient funds",
                    available_amount=format_currency(
                        0, self.ewallet_payment.currency,
                        locale=self.profile.lang, territory=self.profile.country_code,
                    ),
                    amount=amount_str,
                )

            available_amount_str = ewallet_info.message

        return available_amount, amount_str, available_amount_str

    async def process(self):
        """Main method to process ewallet payment."""
        self.debugger.debug("process ->")

        await self.state.finish()

        # Prepare payment details
        brand, payment, invoice, store_order, _ = (
            await self._prepare_payment_data()
        )

        # Execute payment
        payment_data, payment_title = await self._execute_payment(payment)

        amount_str = format_currency(
            round(self.ewallet_payment.amount / 100, 2), self.ewallet_payment.currency,
            locale=self.profile.lang, territory=self.profile.country_code,
        )

        message_text = await f(
            "ewallet payment success text", self.lang, amount=amount_str,
            merchant_name=self.profile.name,
        )

        if self.is_deeplink:
            message_text += "\n\n" + await f(
                "client bot ewallet payment return url text", self.lang,
                return_url=self.ewallet_payment.success_url,
            )

        await send_with_wa_main_menu(
            self.query,
            self.bot,
            message_text,
            self.user,
            self.lang,
            no_edit=True,
            is_text_with_menu=True,
        )

        await self.end_payment(
            brand, invoice, payment, payment_data, payment_title, store_order
        )

    async def process_exception(self, e: Exception):
        self.debugger.error(f"process_exception -> {e=}", exc_info=True)

        if isinstance(e, ErrorWithTextVariable):
            text_variable = e.text_variable
            text_kwargs = e.text_kwargs
            if hasattr(e, "message") and getattr(e, "message"):
                text_kwargs.update({"message": e.message})
        elif isinstance(e, HTTPException):
            text_variable = "client bot ewallet payment error"
            text_kwargs = {"message": e.detail}
        elif isinstance(e, ContentTypeError):
            text_variable = "client bot ewallet payment provider error"
            text_kwargs = {"message": e.message}
        elif isinstance(e, IncustError) and hasattr(e, "message") and e.message:
            text_variable = "external loyalty error"
            text_kwargs = {"message": e.message}
        else:
            text_variable = "client bot ewallet payment unknown error"
            text_kwargs = {"error": str(e)}

        message_text = await f(text_variable, self.lang, **text_kwargs)

        if self.ewallet_payment:
            if self.is_deeplink:
                await notify_all_channels(
                    SSEChannelTarget.PAYMENT_STATUS,
                    "failed",
                    key=f"{self.ewallet_payment.profile_id}-"
                        f"{self.ewallet_payment.external_id}",
                    payment_id=self.payment_id,
                    title=message_text,
                    status="failed",
                    level=NotificationLevel.ERROR.value,
                )
                if self.ewallet_payment.success_url:
                    message_text += "\n\n" + await f(
                        "client bot ewallet payment return url text", self.lang,
                        return_url=self.ewallet_payment.success_url,
                    )
            # self.ewallet_payment.status = EWalletPaymentStatus.FAILED

        message_parts = [
            f"!!! Business payment ERROR !!!\n\n"
            f"{text_variable=}\n{text_kwargs=}\n"
        ]

        if isinstance(e, BusinessPaymentError):
            if hasattr(e.original_error, "text_variable") and hasattr(
                    e.original_error, "text_kwargs"
            ):
                text_variable = e.original_error.text_variable
                text_kwargs = e.original_error.text_kwargs
                bp_message_text = await f(text_variable, self.lang, **text_kwargs)
            else:
                bp_message_text = str(e.original_error)
            message_parts.append(f"\nBusiness payment error: {bp_message_text}\n")

        message_data = {}
        if self.bot is not None:
            message_data.update({"id": self.bot.id})
            message_data.update({"group_id": self.bot.group_id})

        if self.user is not None:
            message_data.update({"full_name": self.user.full_name})
            message_data.update({"id": self.user.id})
            message_data.update(
                {"username": self.user.username or self.user.wa_phone}
            )

        if self.profile is not None:
            message_data.update(
                {"profile_name": f"{self.profile.id}, {self.profile.name}"}
            )
        if self.ewallet is not None:
            message_data.update(
                {"ewallet": f"{self.ewallet.id=}, {self.ewallet.name=}"}
            )
        if self.ewallet_payment is not None:
            message_data.update({"ewallet_payment_id": self.ewallet_payment.id})

        if self.amount is not None:
            message_data.update({"amount": self.amount})

        if not isinstance(e, EWalletPaymentInsufficientFundsError):
            formatted_message_data = json.dumps(
                message_data, indent=4, ensure_ascii=False
            )
            await send_message_to_platform_admins(
                "".join(message_parts) + "\n\n" + formatted_message_data
            )

        return await send_with_wa_main_menu(
            self.query,
            self.bot,
            message_text,
            self.user,
            self.lang,
            no_edit=True,
            is_text_with_menu=True,
        )

    async def process_make_incust_check(
            self,
            base_data: dict,
            payment_data: PaymentCallBackData,
            payment_method: str,
            loyalty_settings: LoyaltySettings,
            rules_type: (
                    Literal[
                        "by-all-rules",
                        "by-charge-only-rules",
                        "without-rules",
                    ] | None
            ) = "by-all-rules",
            merchant_data: list[IncustPayMerchantData] | None = None,
            comment: str | None = None,
            skip_incust_message: bool | None = None,
    ):
        business_payment_result = None

        if not loyalty_settings:
            self.debugger.error(
                f"Loyalty settings not found for ewallet {self.ewallet.id}"
            )
            raise HTTPException(status_code=404, detail="Loyalty settings not found")

        brand = await crud.get_brand_by_group(self.profile.id)
        if not loyalty_settings.brand_id:
            loyalty_settings.brand_id = brand.id
        await get_or_create_incust_customer(self.user, loyalty_settings, self.lang)

        incust_check_data = {
            "amount": round(self.ewallet_payment.amount / 100, 2),
            "amount_to_pay": round(self.ewallet_payment.amount / 100, 2),
            "check_items": [
                {
                    "title": f"{self.ewallet.name} #{self.ewallet_payment.id}",
                    "code": f"#{self.ewallet_payment.id}",
                    "price": round(self.ewallet_payment.amount / 100, 2),
                    "quantity": 1,
                    "amount": round(self.ewallet_payment.amount / 100, 2),
                    "category": "uncategorized",
                }
            ],
            **base_data,
        }

        incust_check = term.m.Check(**incust_check_data)
        incust_check.id = self.user.incust_external_id
        incust_check.id_type = term.m.IdType("external-id")

        if skip_incust_message:
            incust_check.skip_message = True

        business_payment_setting = await BusinessPaymentSetting.get(
            incust_account_id=self.ewallet.incust_account_id,
            is_enabled=True, is_deleted=False
        )

        invoice = None

        async with incust.term.CheckTransactionsApi(
                loyalty_settings, lang=self.lang
        ) as api:
            if not business_payment_setting:
                # Використовуємо простий запит без бізнес платежу
                response = await api.terminal_process_check_by_rules(
                    rules_type, incust_check
                )
            else:
                # Резервуємо чек для бізнес платежу
                incust_transaction = await api.reserve_check(
                    incust_check
                )

                try:
                    # Отримуємо інвойс для поточного платежу, щоб знайти комісію
                    payment = await Payment.get(
                        uuid_id=self.ewallet_payment.external_id
                    )
                    payment_amount_without_fee = self.ewallet_payment.amount

                    if payment:
                        invoice = await Invoice.get(payment.invoice_id)
                        if invoice and invoice.payer_fee:
                            payment_amount_without_fee = (self.ewallet_payment.amount -
                                                          invoice.payer_fee)
                            self.debugger.debug(
                                f"Deducting payment fee from invoice: "
                                f"{invoice.payer_fee}, original amount: "
                                f"{self.ewallet_payment.amount}, new amount: "
                                f"{payment_amount_without_fee}"
                            )

                    business_payment_result = await make_business_payment(
                        payment_amount_without_fee,
                        self.ewallet_payment.currency,
                        f"{self.ewallet.name} #{self.ewallet_payment.id}",
                        merchant_data, self.ewallet_payment.uuid_id,
                        business_payment_setting,
                    )
                except Exception as e:
                    self.debugger.error(e, exc_info=True)
                    # Скасовуємо транзакцію при помилці
                    async with incust.term.CheckTransactionsApi(
                            loyalty_settings
                    ) as api:
                        result = await api.transaction_cancel(
                            term.m.TransactionCancelRequest(
                                transaction_id=str(incust_transaction.id),
                                comment=f"Invoice {invoice.id if invoice else ''} - "
                                        f"failed"
                            )
                        )

                    self.debugger.debug("cancel transaction", {"result": result})
                    raise BusinessPaymentError(e)

                # Фіналізуємо чек
                response = await api.finalize_check(
                    term.m.FinalizeCheckRequest(
                        id=str(incust_transaction.id),
                        comment=f"Invoice {invoice.id if invoice else ''} - paid"
                    )
                )

        payment_data.incust_transaction = response.to_dict() if hasattr(
            response, 'to_dict'
        ) else response.dict()
        payment_data.status = "success"
        payment_data.payment_method = payment_method
        payment_data.business_payment_result = {
            "result": business_payment_result,
            "merchant_data": [md.dict() for md in
                              merchant_data] if merchant_data else None
        } if business_payment_result else None

        return response

    async def _process_store_order_payment(
            self,
            payment_settings_id: int, invoice_id: int,
            business_payment_setting_id: int | None = None,
            business_payment_result: dict | None = None,
            json_data: dict | None = None,
            post_payment_info: str | None = None,
            payment_method: str | None = None,
            payment_settings_name: str | None = None,
    ):
        if not invoice_id:
            return

        order_payment = await StoreOrderPayment.get(invoice_id=invoice_id)
        if not order_payment:
            await crud.create_store_order_payment(
                invoice_id=invoice_id,
                payment_method=payment_method,
                json_data=json_data,
                name=payment_settings_name,
                payment_settings_id=payment_settings_id,
                post_payment_info=post_payment_info,
                business_payment_setting_id=business_payment_setting_id,
                business_payment_merchant_data=business_payment_result if
                business_payment_result else None,
            )
        else:
            await order_payment.update(
                payment_method=payment_method,
                json_data=json_data,
                name=payment_settings_name,
                payment_settings_id=payment_settings_id,
                post_payment_info=post_payment_info,
                business_payment_setting_id=business_payment_setting_id,
                business_payment_merchant_data=business_payment_result if
                business_payment_result else None,
            )
