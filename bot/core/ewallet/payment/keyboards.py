from typing import Literal

from core.messangers_adapters import InlineKeyboard, InlineKeyboardButton
from utils.text import f
from .callback_data import (
    ConfirmEWalletPaymentCallbackData,
)


async def get_yes_or_no_keyboard(
        lang: str,
        ewallet_payment_id: str,
        bot_type: Literal["telegram", "whatsapp"] = "telegram",
        is_deeplink: bool = False,
):
    keyboard = InlineKeyboard(row_width=2)
    yes_callback_data = ConfirmEWalletPaymentCallbackData(
        ewallet_payment_id=ewallet_payment_id, answer=True, is_deeplink=is_deeplink,
    ).to_str()
    no_callback_data = ConfirmEWalletPaymentCallbackData(
        ewallet_payment_id=ewallet_payment_id, answer=False, is_deeplink=is_deeplink,
    ).to_str()

    keyboard.add_buttons(
        InlineKeyboardButton(
            await f("client bot ewallet payment decline button", lang), no_callback_data
        ),

        InlineKeyboardButton(
            await f("client bot ewallet payment confirm button", lang),
            yes_callback_data
        ),
    )

    return keyboard.to_messanger(bot_type)
