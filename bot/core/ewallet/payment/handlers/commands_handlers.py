import logging

import aiogram as tg

import core.messangers_adapters as ma
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db.models import ClientBot, User
from ..deep_links import EwalletPaymentDeepLink
from ..ewallet_payment_processer import EwalletPaymentProcessor

debugger = logging.getLogger("debugger.ewallet_payment")


@ma.handler.message()
async def ewallet_payment_deep_link_handler(
        message: ma.types.Message,
        state: ma.FSMContext,
        data: EwalletPaymentDeepLink,
        bot: ClientBot,
        user: User,
):
    await state.finish()
    debugger.debug(f"{data.ewallet_payment_id=}")

    lang = await user.get_lang(bot) or 'en'
    keyboard = await get_wa_menu_keyboard(user, bot, lang)

    processor = EwalletPaymentProcessor(
        message, state, bot, user, lang,
        keyboard, data.ewallet_payment_id.replace("_", "-"),
        True,
    )

    try:
        return await processor.confirm_ewallet_payment()
    except Exception as e:
        return await processor.process_exception(e)


def register_ewallet_payment_commands_handlers(dp: tg.dispatcher.Dispatcher):
    ewallet_payment_deep_link_handler.setup(
        dp,
        EwalletPaymentDeepLink.get_filter(return_field_name="data"),
        state="*",
    )
