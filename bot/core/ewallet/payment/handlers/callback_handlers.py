import logging

from core import messangers_adapters as ma
from core.admin_notification.service import notify_all_channels
from core.ewallet.payment.callback_data import ConfirmEWalletPaymentCallbackData
from core.ewallet.payment.ewallet_payment_processer import EwalletPaymentProcessor
from core.friend.functions import send_with_wa_main_menu
from core.messangers_adapters import FSMContext
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db.models import ClientBot, User
from schemas import NotificationLevel, SSEChannelTarget
from utils.text import f

debugger = logging.getLogger("debugger.ewallet_payment")


@ma.handler.button()
async def confirm_ewallet_payment_handler(
        query: ma.types.ButtonQuery,
        confirm_ewallet_payment: ConfirmEWalletPaymentCallbackData,
        user: User,
        lang: str,
        bot: ClientBot,
        state: FSMContext,
):
    ewallet_payment_id = confirm_ewallet_payment.ewallet_payment_id
    is_deeplink = confirm_ewallet_payment.is_deeplink
    debugger.debug(f"{ewallet_payment_id=}, {user.id=}")

    keyboard = await get_wa_menu_keyboard(user, bot, lang)

    processor = EwalletPaymentProcessor(
        query, state, bot, user, lang,
        keyboard, ewallet_payment_id.replace("_", "-"),
        is_deeplink,
    )

    try:
        await processor.validate_payment_prerequisites()

        if not confirm_ewallet_payment.answer:
            no_message_text = await f(
                "client bot ewallet payment no text",
                lang, ewallet_payment_id=ewallet_payment_id,
            )
            if is_deeplink:
                await notify_all_channels(
                    SSEChannelTarget.PAYMENT_STATUS,
                    "failed",
                    key=f"{processor.ewallet_payment.profile_id}-"
                        f"{processor.ewallet_payment.external_id}",
                    payment_id=processor.payment_id,
                    title=no_message_text,
                    status="failed",
                    level=NotificationLevel.ERROR.value,
                )

            message_text = no_message_text

            if is_deeplink:
                message_text += "\n\n" + await f(
                    "client bot ewallet payment return url text", lang,
                    return_url=processor.ewallet_payment.success_url,
                )

            return await send_with_wa_main_menu(
                query, bot,
                message_text,
                user, lang,
                is_text_with_menu=True,
            )

        await processor.process()
    except Exception as err:
        return await processor.process_exception(err)


def register_ewallet_payment_buttons_handlers(dp: ma.DispatcherType):
    confirm_ewallet_payment_handler.setup(
        dp,
        ConfirmEWalletPaymentCallbackData.get_filter(),
        state="*",
    )
