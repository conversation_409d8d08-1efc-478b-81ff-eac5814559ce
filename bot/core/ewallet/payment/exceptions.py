from abc import ABC

from psutils.exceptions import ErrorWithTextVariable


class EWalletPaymentError(ErrorWithTextVariable, ABC):

    def __init__(self, message: str, **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"eWallet payment error: {self.message}"


class EWalletPaymentUnknownError(EWalletPaymentError):
    text_variable = "client bot ewallet payment unknown error"

    def __init__(self, **kwargs):
        super().__init__("client bot ewallet payment unknown error", **kwargs)


class EWalletPaymentEWalletNotFoundError(EWalletPaymentError):
    text_variable = "client bot ewallet not found error"

class EWalletPaymentInvalidUrlError(EWalletPaymentError):
    text_variable = "client bot ewallet payment invalid url error"


class EWalletPaymentNotFoundError(EWalletPaymentError):
    text_variable = "client bot ewallet payment not found error"


class EWalletPaymentStatusError(EWalletPaymentError):
    text_variable = "client bot ewallet payment status error"


class EWalletPaymentNotSameError(EWalletPaymentError):
    text_variable = "client bot ewallet payment not same error"


class EWalletPaymentProfileNotFoundError(EWalletPaymentError):
    text_variable = "client bot ewallet payment profile not found error"


class EWalletPaymentCountryNotSupportedError(EWalletPaymentError):
    text_variable = "client bot ewallet country not supported error"


class EWalletPaymentIncustNotFoundError(EWalletPaymentError):
    text_variable = "client bot ewallet incust not found error"


class EWalletPaymentInsufficientFundsError(EWalletPaymentError):
    text_variable = "client bot ewallet payment insufficient funds text"


class EWalletPaymentCardInfoError(EWalletPaymentError):
    text_variable = "client bot ewallet payment card info error"


class EWalletPaymentSpecialsError(EWalletPaymentError):
    text_variable = "client bot ewallet payment specials error"


class EWalletPaymentUserSpecialsError(EWalletPaymentError):
    text_variable = "client bot ewallet payment user specials error"
