import asyncio

from aiohttp import ClientSession
from lxml import etree
# noinspection PyProtectedMember
from lxml.etree import _Element as LxmlElement
from psutils.convertors import str_to_float
from psutils.exceptions import ErrorWithTextVariable

from core.ext import schemas
from db.models import Brand, DataPorter
from utils.translator import tg
from .exceptions import (
    FetchingPromDataError, ObjectIdIsRequiredError,
    PromLanguageIsNotSupportedError,
)
from ..base import BaseAdapter

STORE_EXTERNAL_ID = "prom-store"
STORE_CURRENCY_CODE = "UAH"


class PromAdapter(BaseAdapter):
    AVAILABLE_CUSTOM_FIELDS = [
        "address", "email", "phone_number", "wifi",
        "facebook", "instagram", "pinterest", "telegram",
        "twitter", "viber", "whatsapp", "youtube",
    ]

    def __init__(
            self,
            prom_url: str,
            lang: str,
            prom_file_main_lang: str | None = None,
            store_name: str | None = None,
            **kwargs,
    ):
        self.url = prom_url
        self.lang = lang
        self.file_main_lang = prom_file_main_lang or lang
        if self.file_main_lang not in ("uk", "ru"):
            raise
        self.store_name = store_name

        super().__init__(**kwargs)

    async def get_data(self):
        async with ClientSession() as session:
            try:
                async with session.get(self.url) as resp:
                    tree = etree.XML(await resp.read())
            except ErrorWithTextVariable:
                raise
            except Exception as e:
                raise FetchingPromDataError() from e
        return tree.getroottree()

    async def add_product_to_translate(
            self, product_data: LxmlElement,
            to_translate: dict[str, str]
    ):
        if self.file_main_lang != "ru" or self.lang == "ru":
            return

        external_id = product_data.attrib.get("id")

        name = product_data.find("name").text
        description = product_data.find("description").text

        name_ua = product_data.find("name_ua").text
        description_ua = product_data.find("description_ua").text

        if not name_ua and name:
            to_translate[f"product__{external_id}__name"] = name
        if not description_ua and description:
            to_translate[f"product__{external_id}__description"] = description

    async def convert_product(
            self, product_data: LxmlElement,
            product_groups: dict[str, schemas.ProductGroup],
            characteristics: dict[str, schemas.Characteristic],
            translated: dict[str, str | None],
    ):
        external_id = product_data.attrib.get("id")

        vendor_code = product_data.find("vendorCode")

        product_id = str(
            vendor_code.text
            if vendor_code is not None and vendor_code.text
            else external_id
        )

        product_characteristics_values: dict[str, str] = {}

        # converting params to dict
        for param in product_data.findall("param"):
            # getting param name
            name = param.attrib.get("name")
            value = param.text

            product_characteristics_values[name] = value
            if name not in characteristics:
                characteristics[name] = schemas.Characteristic(
                    external_id=name,
                    name=name,
                )

        # getting group id
        group_id = product_data.attrib.get("group_id")

        # if product have group
        if group_id:
            modifiers = list(product_characteristics_values.keys())[:3]
            # creates product group, if it has not been created yet
            if group_id not in product_groups:
                product_groups[group_id] = schemas.ProductGroup(
                    external_id=group_id,
                    modifiers=modifiers,
                    hide_modifications_by=[],
                )
            # updating product group is it has already been created
            else:
                product_group = product_groups[group_id]
                product_group.modifiers = list(
                    set(product_group.modifiers) | (set(modifiers))
                )
        else:
            group_id = None

        name = product_data.find("name").text
        description = product_data.find("description").text

        if self.file_main_lang == "ru" and self.lang != "ru":
            name_ua = product_data.find("name_ua").text or translated.get(
                f"product__{external_id}__name",
            ) or name

            name_translation, name = name, name_ua

            description_ua = product_data.find("description_ua").text or translated.get(
                f"product__{external_id}__description",
            ) or name

            description_translation, description = description, description_ua
        else:
            name_translation = None
            description_translation = None

        # getting category id of product
        category_id = category_id_el.text if (category_id_el := product_data.find(
            "categoryId"
        )) is not None else None

        if (pictures := product_data.findall("picture")) and len(pictures) > 0:
            image_url = pictures[0].text
            gallery_items = [el.text for el in pictures[1:]]
        else:
            image_url = None
            gallery_items = None

        price = str_to_float(
            product_data.find("price").text,
            only_positive=True,
            no_error=True,
        ) or 0

        old_price_tag = product_data.find("oldprice")
        old_price = (
            str_to_float(
                old_price_tag.text,
                only_positive=True,
                no_error=True,
            ) or 0
            if old_price_tag else 0
        )
        price *= 100
        old_price *= 100

        # building Product model
        return (schemas.Product(
            position=0,
            type="goods",
            product_id=product_id,
            product_group_id=group_id,
            external_id=external_id,
            is_available=product_data.attrib.get("available") == "true",
            name=name,
            description=description,
            # temporary support only one image
            image_url=image_url,
            gallery_items=gallery_items,
            # converting price to int
            price=price,
            old_price=old_price,
            # in prom.ua always exists only one store
            stores_external_ids=[STORE_EXTERNAL_ID],
            # attributes groups and attributes is not supported by prom.ua
            attribute_groups_external_ids=[],
            # in prom.ua product can have 0 or 1 category
            categories_external_ids=[category_id] if category_id else [],
            # converting params dict to characteristics
            characteristics=[
                schemas.CharacteristicValue(
                    external_id=param_name,
                    value=param_value,
                )
                for param_name, param_value in product_characteristics_values.items()
            ]
        ), schemas.TranslationProduct(
            external_id=product_id,
            lang="ru" if self.lang == "uk" else "uk",
            name=name_translation,
            description=description_translation,
            characteristics=[]
        ) if name_translation or description_translation else None)

    async def get_and_convert_data(self) -> schemas.AdapterResult:
        """
        this function didn't split to small because
        there is no possibility to specify typehints for lxml objects
        """

        data = await self.get_data()

        if self.lang not in ("uk", "ru"):
            raise PromLanguageIsNotSupportedError(self.lang)
        if self.file_main_lang not in ("uk", "ru"):
            raise PromLanguageIsNotSupportedError(self.file_main_lang)

        shop_data = data.getroot()[0]

        to_translate: dict[str, str] = {}

        [
            await self.add_product_to_translate(product_data, to_translate)
            for product_data in shop_data.find("offers", [])
        ]

        for category_data in shop_data.find("categories", []):
            category_id = category_data.attrib.get("id")
            if not category_id:
                raise ObjectIdIsRequiredError("category")

            if self.file_main_lang != "ru" or self.lang == "ru":
                continue

            # getting and translating, if needed, to ukrainian
            name = category_data.text
            if name:
                to_translate[f"category__{category_id}__name"] = name

        translated = await tg(
            to_translate, "uk", "ru",
            group_id=self.group_id,
            is_auto_translate_allowed=self.is_auto_translate_allowed,
        ) or {}

        # converting categories to pydantic models
        categories: list[schemas.Category] = []
        categories_translations: list[schemas.Translation] = []
        for index, category_data in enumerate(shop_data.find("categories", [])):
            category_id = category_data.attrib.get("id")
            if not category_id:
                raise ObjectIdIsRequiredError("category")

            # getting and translating, if needed, to ukrainian
            name = category_data.text
            translated_name = translated.get(f"category__{category_id}__name")

            if translated_name:
                categories_translations.append(
                    schemas.Translation(
                        external_id=category_id,
                        lang=self.file_main_lang,
                        name=name,
                    )
                )
                name = translated_name

            # building and adding category
            categories.append(
                schemas.Category(
                    external_id=category_id,
                    name=name,
                    filters=[],
                    stores_external_ids=[STORE_EXTERNAL_ID],
                    father_category_id=category_data.attrib.get("parentId"),
                    position=index
                )
            )

        # declaring product_groups dict(for modifying below)
        product_groups: dict[str, schemas.ProductGroup] = {}

        characteristics: dict[str, schemas.Characteristic] = {}

        # converting products and product_groups to pydantic models
        products_and_translations: list[
            tuple[schemas.Product, schemas.TranslationProduct]
        ] = await asyncio.gather(
            *(
                self.convert_product(
                    product_data, product_groups, characteristics,
                    translated,
                )
                for product_data in shop_data.find("offers", [])
            )
        )
        products = []
        products_translations = []

        index = 0
        for product, product_translation in products_and_translations:
            product.position = index
            products.append(product)
            index += 1
            if product_translation:
                products_translations.append(product_translation)

        # creating custom fields
        custom_fields = []
        for custom_field_name in self.AVAILABLE_CUSTOM_FIELDS:

            custom_field = schemas.StoreCustomField(
                name=custom_field_name,
                value=None,
            )
            custom_fields.append(custom_field)

        self.translations = schemas.AdapterTranslations(
            stores=[],
            products=products_translations,
            categories=categories_translations,
            attribute_groups=[],
            attributes=[],
            characteristics=[],
        )

        if store_name_tag := shop_data.find("name"):
            store_name = store_name_tag.text
        else:
            store_name = self.store_name

        # building AdapterResult model
        return schemas.AdapterResult(
            # building Store model
            stores=[schemas.Store(
                external_id=STORE_EXTERNAL_ID,
                name=store_name,
                company=company.text if (
                    company := shop_data.find("company")) else None,
                company_url=company_url.text if (
                    company_url := shop_data.find("url")) else None,
                currency=STORE_CURRENCY_CODE,
                data_url=self.url,
                custom_fields=custom_fields,
            )],
            products=products,
            product_groups=list(product_groups.values()),
            categories=categories,
            # attribute groups and attributes is not supported by prom.ua
            attribute_groups=[],
            characteristics=list(characteristics.values()),
            translations=self.translations,
        )

    @classmethod
    async def get_data_from_database(
            cls, brand: Brand, data_porter: DataPorter | None, lang: str,
            user_lang: str, **kwargs
    ):
        return dict(store_name=brand.name)
