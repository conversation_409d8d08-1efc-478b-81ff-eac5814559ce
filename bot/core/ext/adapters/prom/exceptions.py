from ...exceptions import StoreImportError, StoreImportInvalidInputError


class FetchingPromDataError(StoreImportInvalidInputError):
    text_variable = "fetching prom data error"

    def __init__(self):
        super().__init__("Fetching prom data error")


class ObjectIdIsRequiredError(StoreImportInvalidInputError):
    text_variable = "object id is required error"

    def __init__(self, object_type: str):
        super().__init__("object id is required error", object_type=object_type)


class PromLanguageIsNotSupportedError(StoreImportError):
    text_variable = "prom language is not supported error"

    def __init__(self, lang: str):
        super().__init__(f"Language {lang} is not supported for prom import", profile_lang=lang)
