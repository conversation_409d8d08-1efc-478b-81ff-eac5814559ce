import logging

from fastapi import HTT<PERSON>Exception
from incust_api.api import client, term
from incust_client_api_client.models import Goods, GoodsCategory, Pos

from core.ext import schemas
from core.loyalty.customer_service import get_or_create_incust_customer
from core.loyalty.incust_api import incust
from db import crud
from db.models import Brand, DataPorter, User
from schemas import LoyaltySettingsData
from schemas.loyalty_settings import LoyaltySettingsSchema
from utils.text import f
from ..base import BaseAdapter
from ...exceptions import IncustNotLoyaltyImportError, IncustNotStoresImportError
from ...schemas import StoreCustomField
from ...types import ExternalType


async def get_custom_fields(store: Pos) -> list[StoreCustomField]:
    custom_fields = []
    AVAILABLE_CUSTOM_FIELDS = [
        "address", "email", "phone_number", "wifi",
        "facebook", "instagram", "pinterest", "telegram",
        "twitter", "viber", "whatsapp", "youtube",
    ]
    for custom_field_name in AVAILABLE_CUSTOM_FIELDS:
        if custom_field_name == 'address':
            custom_fields.append(
                StoreCustomField(
                    name=custom_field_name,
                    value=store.addresses if store.addresses else None,
                )
            )
        if custom_field_name == 'phone_number' and store.phones:
            custom_fields.append(
                StoreCustomField(
                    name=custom_field_name,
                    value=store.phones[0].number,
                )
            )
        if getattr(store.links, custom_field_name, None):
            custom_field = StoreCustomField(
                name=custom_field_name,
                value=getattr(store.links, custom_field_name),
            )
            custom_fields.append(custom_field)

    return custom_fields


def flatten_category_tree(categories: list[term.m.GoodsCategory]) -> list[GoodsCategory]:
    flattened_categories = []
    sort = 0
    for category in categories:
        sort += 1
        category.sort = sort
        flattened_categories.append(category)
        if category.nodes is not None:
            flattened_categories.extend(flatten_category_tree(category.nodes))
    return flattened_categories


class IncustAdapter(BaseAdapter):
    def __init__(self, lang: str, selected_stores: list[str], user: User | None = None, **kwargs):
        super().__init__(**kwargs)

        self.lang = lang
        self.incust_stories = None
        self.user = user

        self.incust_categories: list[GoodsCategory] | None = None

        self.categories_data: dict[str, list[GoodsCategory]] = {}
        self.products_data: dict[str, list[Goods]] = {}

        self.stories: list[schemas.Store] = []
        self.categories: dict[str, schemas.Category] = {}
        self.products: dict[str, schemas.Product] = {}
        self.attribute_groups: dict[str, schemas.AttributeGroup] = {}

        self.cnt_attributes: int = 0
        self.incust_user_domain = None
        self.incust_skip_desc_product = False
        self.custom_fields: schemas.AdapterResult | None = None

        self.loyalty_settings: LoyaltySettingsSchema = kwargs['loyalty_settings']
        self.user_token: str = kwargs['user_token']

        self.logger = logging.getLogger('debugger')
        self.selected_stores: list[str] = selected_stores


    async def convert_store_data(self, incust_stories: list[Pos]):

        for store in incust_stories:
            if store.id not in self.selected_stores:
                continue
            if hasattr(self.loyalty_settings, 'loyalty_id') and store.loyalty_id != self.loyalty_settings.loyalty_id:
                continue

            converted_store = schemas.Store(
                external_id=store.id,
                external_type=ExternalType.INCUST.value,
                # brand_id=self.brand_id,
                name=store.title,
                currency=self.incust_settings.currency,
                latitude=str(store.latitude) if store.latitude else None,
                longitude=str(store.longitude) if store.longitude else None,
                custom_fields=await get_custom_fields(store),
                image_url=store.photos[0].url if store.photos else None,
                distance=1000,
                polygon=None,
            )

            self.stories.append(converted_store)
        if not self.stories:
            raise IncustNotStoresImportError(await f("brand import incust no stores text", self.lang))

    def convert_categories_data(self, categories: list[GoodsCategory]):
        if not categories:
            self.categories = None
            return

        categories_tree = flatten_category_tree(categories)
        sorted_categories = sorted(categories_tree, key=lambda x: (x.sort is None, x.sort))
        for index, category in enumerate(sorted_categories):

            external_id = category.id
            image_url = ''
            if category.image:
                if category.image.url.startswith('http'):
                    image_url = category.image.url
                elif self.incust_user_domain:
                    image_url = self.incust_user_domain + category.image

            converted_category = schemas.Category(
                position=index,
                name=category.title,
                image_url=image_url,
                external_id=category.id,
                filters=[],
                stores_external_ids=[story.external_id for story in self.stories],
                father_category_id=category.parent_id if category.parent_id else None
            )
            self.categories[external_id] = converted_category

    async def get_products_data(self):
        try:
            async with incust.term.GoodsApi(self.loyalty_settings, lang=self.lang) as api:
                return await api.goods()
        except Exception as e:
            self.logger.error(f"Error getting products: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    def convert_products_data(self, products: list[client.m.Goods] | None = None, ):
        if not products:
            return

        sorted_products = sorted(products, key=lambda x: (x.sort_order is None, x.sort_order))
        for index, product in enumerate(sorted_products):
            external_id = product.id
            image_url = ''
            if product.image:
                if product.image.url.startswith('http'):
                    image_url = product.image.url
                elif self.incust_user_domain:
                    image_url = self.incust_user_domain + product.image

            schemas.Product.update_forward_refs()
            price = 0
            for price in product.price:
                if price.currency == self.incust_settings.currency and price.applicable and price.type == 'currency':
                    price = price.price * 100
                    break

            gallery_items = [
                item.url
                for item in product.additional_images
            ] if product.additional_images else None

            converted_product = schemas.Product(
                product_id=product.code,
                name=product.title,
                price=price,
                image_url=image_url,
                description=product.description,
                position=index,
                external_id=product.code,
                # is_available=bool(product.active),
                is_available=True,
                is_weight=bool(product.weighing),
                weight_unit=product.unit,
                characteristics=[],
                stores_external_ids=[story.external_id for story in self.stories],
                attribute_groups_external_ids=[],
                categories_external_ids=[product.parent_id] if product.parent_id else None,
                gallery_items=gallery_items,
                type="goods",
            )

            if not converted_product:
                continue
            self.products[external_id] = converted_product

    async def convert_data(self, ):
        await self.convert_store_data(self.incust_stories)

        self.convert_categories_data(
            self.incust_categories
        )
        products = await self.get_products_data()
        if products:
            self.convert_products_data(products)

        adapter_result = schemas.AdapterResult(
            stores=self.stories,
            products=list(self.products.values()),
            product_groups=[],
            categories=list(self.categories.values()) if self.categories else [],
            attribute_groups=list(self.attribute_groups.values()),
            characteristics=[],
        )
        return adapter_result

    async def get_incust_data(self):
        self.logger.debug('\nSTART get_incust_data -> brand: {}'.format(self.brand_id))
        if not self.selected_stores:
            raise IncustNotStoresImportError(await f("brand import incust no stores text", self.lang))

        try:
            # Отримуємо stores через Client API (POSApi)
            async with incust.client.POSApi(self.loyalty_settings, user_token=self.user_token, lang=self.lang) as api:
                self.incust_stories = await api.pos()
        except Exception as err:
            self.logger.error(f"Error getting stores: {err}", exc_info=True)
            raise IncustNotStoresImportError(await f("brand import incust no stores text", self.lang))

        try:
            # Отримуємо categories через Client API
            async with incust.term.GoodsApi(self.loyalty_settings, lang=self.lang) as api:
                self.incust_categories = await api.categories()
        except Exception as err:
            self.logger.error(f"Error getting categories: {err}")
            self.incust_categories = []
            
        try:
            # Отримуємо terminal settings через Terminal API
            async with incust.term.SettingsApi(self.loyalty_settings, lang=self.lang) as api:
                self.incust_settings = await api.settings()
        except Exception as err:
            self.logger.error(f"Error getting terminal settings: {err}")
            raise HTTPException(status_code=500, detail=str(err))

    async def get_and_convert_data(self):
        await self.get_incust_data()
        return await self.convert_data()

    @classmethod
    async def get_data_from_database(
            cls, brand: Brand, data_porter: DataPorter | None, lang: str, user_lang: str, **kwargs
    ):
        user = kwargs['user']

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            LoyaltySettingsData(brand_id=brand.id),
        )

        if not loyalty_settings:
            raise IncustNotLoyaltyImportError(await f("brand import not incust data for import", lang))

        incust_customer = await get_or_create_incust_customer(
            user, loyalty_settings, lang
        )
        if not incust_customer or not incust_customer.token:
            raise ValueError(
                f"Could not get or create InCust customer for user "
                f"{user.id}"
            )
        user_token = incust_customer.token

        return dict(
            loyalty_settings=LoyaltySettingsSchema.from_orm(loyalty_settings),
            user_token=user_token,
        )
