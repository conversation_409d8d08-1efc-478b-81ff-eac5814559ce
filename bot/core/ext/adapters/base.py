from __future__ import annotations

import abc
import inspect
from typing import Any, TYPE_CHECKING, Type

from psutils.text import paschal_case_to_snake_case
from typing_extensions import Literal

from core.adapters import BaseAdapterType, BaseCoreAdapter
from core.ext import schemas

if TYPE_CHECKING:
    from db.models import Brand, DataPorter, Group


class AdapterType(BaseAdapterType):
    translations: schemas.AdapterTranslations

    async def get_and_convert_data(self) -> schemas.AdapterResult:
        ...

    async def save_data(
            self, data: dict,
            langs_list: list[str], brand_lang: str,
            translations: dict | None = None
    ) -> Any:
        ...

    @classmethod
    async def get_data_from_database(
            cls, brand: "Brand", data_porter: "DataPorter" | None, lang: str,
            user_lang: str, **kwargs
    ) -> dict:
        ...


class BaseAdapter(BaseCoreAdapter, abc.ABC):
    _adapters: dict[str, Type[BaseAdapter]] = {}

    def __init_subclass__(cls, **kwargs):
        if not inspect.isabstract(cls):
            adapter_name = paschal_case_to_snake_case(
                cls.__name__.replace("Adapter", "")
            )
            if adapter_name in cls._adapters:
                raise TypeError(
                    f"adapter with name {cls.__name__} has already been registered"
                )
            cls._adapters[adapter_name] = cls
        super().__init_subclass__(**kwargs)

    def __init__(self, *args, **kwargs):
        self.brand: Brand = kwargs.get("brand")
        self.group: Group = kwargs.get("group")
        self.group_id: int = self.group.id
        self.is_auto_translate_allowed: bool = self.group.is_translate
        self.brand_id: int = self.brand.id
        self.import_or_export: Literal["import", "export"] = kwargs.pop(
            "import_or_export"
        )
        self.data_porter: DataPorter | None = kwargs.get("data_porter")
        if self.import_or_export not in ("import", "export"):
            raise ValueError("import_or_export must be either 'import' or 'export'")

        self.translations: schemas.AdapterTranslations | None = None
        super().__init__()

    @classmethod
    async def get_data_from_database(
            cls, brand: "Brand", data_porter: DataPorter | None, lang: str,
            user_lang: str, **kwargs
    ) -> dict:
        """
        Default implementation that returns an empty dict.
        Override this method in subclasses if database access is needed.
        """
        return {}

    @abc.abstractmethod
    async def get_and_convert_data(self) -> schemas.AdapterResult:
        raise NotImplementedError

    async def save_data(
            self, data: dict,
            langs_list: list[str], brand_lang: str,
            translations: dict | None = None
    ) -> Any:
        raise NotImplementedError(
            f"adapter with name {self.__class__.__name__} does not support data export"
        )
