from __future__ import annotations

from collections import defaultdict
from typing import Type, TypeVar

from core.adapters.excel import BaseExcelModel, Field
from ..excel.exceptions import ExcelAdapterUnknownFieldError


class ExcelModel(BaseExcelModel, base=True):

    def __init_subclass__(cls, **kwargs):
        cls._allow_custom_fields = kwargs.pop("allow_custom_fields", False)
        cls._allowed_custom_fields = kwargs.pop("allowed_custom_fields", "*")

        super().__init_subclass__(**kwargs)

    @classmethod
    async def get_schema_name(cls, lang: str):
        return cls.__name__

    @classmethod
    async def check_fields(cls, name: str, lang: str):
        for field in cls._fields.values():
            verbose_name = field.variable
            if verbose_name == name:
                yield field.name, "variable"

    @classmethod
    async def make_from_raw_data(
            cls: Type[ModelType],
            data: list[list[str] | tuple[str]],
            lang: str,
    ) -> list[ModelType]:
        headers, data = data[0], data[1:]

        columns_fields: dict[int, list[str]] = defaultdict(list)
        used_fields = set()

        for i, header in enumerate(headers):
            if not header:
                continue

            header = str(header)

            async for name in cls.check_fields(header, lang):
                if name in used_fields:
                    continue

                columns_fields[i].append(name)
                used_fields.add(name)

            if i in columns_fields:
                continue

            raise ExcelAdapterUnknownFieldError(cls.get_schema_variable(), 0, header)

        result: list[ModelType] = []

        for i, row in enumerate(data):
            if all(map(lambda x: x is None, row)):
                continue

            obj = cls(i)

            for column_idx, column_names in columns_fields.items():
                for column_name in column_names:
                    setattr(obj, column_name, row[column_idx])

            result.append(obj)

        return result


ModelType = TypeVar("ModelType", bound="ExcelModel")


class Menu(ExcelModel):
    pos_id: str | None = Field("POS ID", no_localisation=True)
    name: str = Field("Dish name", True, max_length=255, no_localisation=True)
    allergens: str | None = Field("Allergens", no_localisation=True)
    category: str = Field("Category", True, no_localisation=True)
    price: int | float = Field("Price", True, no_localisation=True)
    vat: int | float | None = Field("VAT", no_localisation=True)
    description: str | None = Field(
        "Description", max_length=1024, no_localisation=True
    )
    weight: int | None = Field("Weight, g", no_localisation=True)
    time_to_prepare: str | None = Field("Time to prepare", no_localisation=True)
    section: str = Field("Section", True, no_localisation=True)
    image_url: str | None = Field("Dish image", max_length=399, no_localisation=True)
    external_id: str = Field("Inner ID", True, max_length=255, no_localisation=True)
    category_id: str = Field("Inner catID", True, max_length=255, no_localisation=True)
    section_id: str = Field(
        "Inner sectionID", True, max_length=255, no_localisation=True
    )


MODELS = {
    Menu.__name__: Menu,
}
