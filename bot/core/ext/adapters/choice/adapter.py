from typing import Any

from openpyxl.reader.excel import load_workbook
from openpyxl.workbook import Workbook
from openpyxl.worksheet.worksheet import Worksheet
from psutils.decorators import sync_to_async

from core.ext import schemas
from . import models
from .types import CharacteristicFields
from ..excel.base import BaseExcelAdapter
from ...types import ExternalType


class ChoiceAdapter(BaseExcelAdapter, external_type=ExternalType.CHOICE.value):

    def __init__(self, lang: str, excel_file: str | None, *args, **kwargs):
        super().__init__(lang, *args, **kwargs)

        if self.import_or_export == "import" and not excel_file:
            raise ValueError("Excel file is required when importing")

        self.excel_file = excel_file
        self.workbook: Workbook | None = None

    @sync_to_async
    def get_all_values(self, sheet_name: str) -> list[tuple[Any]]:
        if not self.workbook:
            self.workbook = load_workbook(self.excel_file)
        ws: Worksheet = self.workbook[sheet_name]
        return list(ws.values)

    async def convert_products(self, data: list):
        products: list[schemas.Product] = []
        sorted_products = sorted(data, key=lambda x: (x.row_number is None, x.row_number))
        for index, row in enumerate(sorted_products):
            products.append(
                schemas.Product(
                    type="goods",
                    product_id=row.pos_id or row.external_id,
                    external_id=row.external_id,
                    name=row.name,
                    description=row.description,
                    position=index,
                    image_url=row.image_url,
                    price=row.price,
                    characteristics=[
                        schemas.CharacteristicValue(
                            external_id=characteristic_field.value,
                            value=getattr(row, characteristic_field.value),
                        )
                        for characteristic_field in CharacteristicFields
                    ],
                    stores_external_ids=[],
                    attribute_groups_external_ids=[],
                    categories_external_ids=[row.section_id],
                )
            )

        return products

    async def convert_categories(self, data: list):
        categories: list[schemas.Category] = []
        categories_ids: set[str] = set()
        sections_ids: set[str] = set()

        sorted_categories = sorted(data, key=lambda x: (x.row_number is None, x.row_number))
        for index, row in enumerate(sorted_categories):
            if row.category_id not in categories_ids:
                categories_ids.add(row.category_id)
                categories.append(
                    schemas.Category(
                        name=row.category,
                        external_id=row.category_id,
                        filters=[],
                        stores_external_ids=[],
                        position=index,
                    )
                )
            if row.section_id not in sections_ids:
                sections_ids.add(row.section_id)
                categories.append(
                    schemas.Category(
                        name=row.section,
                        external_id=row.section_id,
                        father_category_id=row.category_id,
                        filters=[],
                        stores_external_ids=[],
                        position=index,
                    )
                )

        return categories

    async def convert_characteristics(self, data: list):
        return [
            schemas.Characteristic(
                external_id=characteristic_field.value,
                name=characteristic_field.value,
            )
            for characteristic_field in CharacteristicFields
        ]

    async def get_and_convert_data(self) -> schemas.AdapterResult:
        data = await self.get_converted_values(models.Menu)

        products = await self.convert_products(data)
        categories = await self.convert_categories(data)
        characteristics = await self.convert_characteristics(data)

        return schemas.AdapterResult(
            stores=[],
            products=products,
            product_groups=[],
            categories=categories,
            attribute_groups=[],
            characteristics=characteristics,
        )
