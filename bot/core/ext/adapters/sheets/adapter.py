from typing import Any

from core.adapters.sheets import BaseSheetsCoreAdapter
from core.adapters.sheets.exceptions import SheetsDocumentError

from gspread import Worksheet
from gspread.exceptions import APIError

from utils.google import get_sheet_name, get_sheet_url

from ..excel.base import BaseExcelAdapter

from ...exceptions import StoreAPIGoogleError
from ...types import ExternalType


class SheetsAdapter(BaseSheetsCoreAdapter, BaseExcelAdapter, external_type=ExternalType.SHEETS.value):

    async def get_all_values(self, sheet_name: str) -> list[tuple[Any]]:
        client = await self.client
        if not client:
            document_name = await get_sheet_name(self.sheets)
            raise SheetsDocumentError(document_name, lang=self.lang)

        worksheet: Worksheet = await client.get_worksheet_by_title_from_cache(sheet_name)
        client.use_worksheet(worksheet)

        rows = await client.get_sheet_data()
        return rows

    async def _save_data(
            self, data: dict,
            langs_list: list[str], brand_lang: str,
            translations: dict | None = None
    ) -> Any:
        client = await self.client
        if not client:
            document_name = await get_sheet_name(self.sheets)
            raise SheetsDocumentError(document_name, lang=self.lang)

        to_export = await self.get_cells_to_export(data, langs_list, translations)

        for title, cells_data in to_export.items():
            await client.load_worksheets()
            worksheet: Worksheet = await client.get_worksheet_by_title_from_cache(title)
            if worksheet:
                client.use_worksheet(worksheet)
                await client.clear_worksheet()
            else:
                worksheets = await client.get_worksheets_from_cache()
                await client.create_and_use_worksheet(title, index=len(worksheets))

            await client.write_rows(cells_data.get_sheets_data(cells_data.max_column))

        return get_sheet_url(self.sheets)

    async def save_data(
            self, data: dict,
            langs_list: list[str], brand_lang: str,
            translations: dict | None = None
    ) -> Any:
        try:
            return await self._save_data(data, langs_list, brand_lang, translations)
        except APIError as error:
            raise StoreAPIGoogleError("The caller does not have permission")
