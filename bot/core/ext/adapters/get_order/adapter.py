from config import GET_ORDER_PROVIDER_ID
from core.ext import schemas
from core.ext.api.get_order.client_get_order import GetOrderApiClient
from db.models import Brand
from ..base import BaseAdapter


class GetOrderAdapter(BaseAdapter):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        self.stores: list[schemas.Store] = []
        self.categories: dict[str, schemas.Category] = {}
        self.products: dict[str, schemas.Product] = {}
        self.attribute_groups: dict[str, schemas.AttributeGroup] = {}

    @classmethod
    def error_handler(cls, data: dict, request: str):
        if data and data["status"] != "operation_success":
            data["request"] = f"{request}"
            raise Exception(str(data))

    async def get_locations_data(self) -> list[dict]:
        brand = await Brand.get(self.brand_id)
        client = GetOrderApiClient(brand)
        restaurants_data = await client.get_restaurants(GET_ORDER_PROVIDER_ID)
        self.error_handler(
            restaurants_data,
            f"providers/{GET_ORDER_PROVIDER_ID}/locations"
        )
        return restaurants_data["data"]

    async def convert_locations_data_to_stores(self, locations_data: list[dict]):
        for locations_data in locations_data:
            store_data = locations_data["linked_rest"]

            address = store_data.pop("address")

            custom_fields = [
                schemas.StoreCustomField(
                    name="address",
                    value=address,
                ),
            ]

            self.stores.append(
                schemas.Store(
                    external_id=store_data["uniq_id"],
                    get_order_id=store_data["id"],
                    location_get_order_id=locations_data["id"],
                    name=locations_data["title"],
                    latitude=store_data.get("lat"),
                    longitude=store_data.get("lng"),
                    organisation_id=store_data["org_id"],
                    currency="UAH",
                    custom_fields=custom_fields,
                    city=store_data.get("city", {}).get("title")
                )
            )

    async def get_categories_data(self, location_id: int) -> list[dict]:
        brand = await Brand.get(self.brand_id)
        client = GetOrderApiClient(brand)
        categories = await client.get_categories_tree(location_id)
        self.error_handler(
            categories,
            f"menu/{location_id}/categories_tree"
        )
        categories_data = categories["data"]
        return categories_data

    def convert_category(
        self, store_id: str, category: dict,
        father_category_id: int | None = None,
    ):
        external_id = str(category.get("uniq_id"))

        if external_id in self.categories:
            converted_category = self.categories[external_id]
            if store_id not in converted_category.stores_external_ids:
                converted_category.stores_external_ids.append(store_id)
        else:
            converted_category = schemas.Category(
                external_id=external_id,
                get_order_id=category.get("id"),
                name=category.get("title"),
                image_url=image.get("webp") if (image := category.get("image")) else None,
                filters=[],
                stores_external_ids=[store_id],
                father_category_id=father_category_id,
                position=999,
            )
            self.categories[external_id] = converted_category

        return converted_category

    def convert_categories_data(self, store_id: str, data: list[dict]):
        for category in data:
            converted_category = self.convert_category(store_id, category)

            for child_category in category.get("childrens", []):
                self.convert_category(store_id, child_category, converted_category.external_id)

    async def get_products_data(self, location_id: int, category_id: int) -> list[dict]:
        brand = await Brand.get(self.brand_id)
        client = GetOrderApiClient(brand)
        products = await client.get_products(
            category_id,
            location_id
        )
        self.error_handler(
            products,
            f"/menu/{location_id}/{category_id}/products"
        )

        products_data = products["data"]

        return products_data

    def convert_attribute_groups(self, attribute_groups_data: list[dict]):
        attribute_groups_ids: set[str] = set()
        for attribute_group_data in attribute_groups_data:
            ag_external_id = str(attribute_group_data.get("uniq_id"))
            attribute_groups_ids.add(ag_external_id)

            if ag_external_id in self.attribute_groups:
                attribute_group = self.attribute_groups[ag_external_id]
            else:
                attribute_group = schemas.AttributeGroup(
                    attribute_group_id=ag_external_id,
                    external_id=ag_external_id,
                    name=attribute_group_data.get("name"),
                    min=attribute_group_data.get("min"),
                    max=attribute_group_data.get("max"),
                    attributes=[],
                )
                self.attribute_groups[ag_external_id] = attribute_group

            for attribute_data in attribute_group_data.get("childrens", []):
                attr_external_id = str(attribute_data.get("uniq_id"))

                if not any(filter(lambda x: x.external_id == attr_external_id, attribute_group.attributes)):
                    price_impact = price_impact * 100 if (price_impact := attribute_data.get("price")) else 0
                    attribute_group.attributes.append(
                        schemas.Attribute(
                            attribute_id=attr_external_id,
                            external_id=attr_external_id,
                            name=attribute_data.get("name"),
                            min=attribute_group_data.get("min"),
                            max=attribute_group_data.get("max"),
                            price_impact=price_impact,
                        )
                    )
        return attribute_groups_ids

    def convert_products_data(
        self,
        store_id: str,
        category_id: str,
        products_data: list[dict],
    ):
        attribute_group_data: dict
        attribute_data: dict

        sorted_products = sorted(
            products_data, key=lambda x: (x.get("ordering", None) is None, x.position.get("ordering", None))
        )
        for index, product_data in enumerate(sorted_products):
            attribute_groups_ids = self.convert_attribute_groups(product_data.get("modifiers", []))

            external_id = str(product_data.get("uniq_id"))
            if external_id in self.products:
                product = self.products[external_id]
                if store_id not in product.stores_external_ids:
                    product.stores_external_ids.append(store_id)
                if category_id not in product.categories_external_ids:
                    product.categories_external_ids.append(category_id)
            else:
                self.products[external_id] = schemas.Product(
                    product_id=external_id,
                    external_id=external_id,
                    is_available=bool(product_data.get("state")),
                    position=index,
                    name=product_data.get("title"),
                    description=product_data.get("description"),
                    image_url=product_data.get("image_webp"),
                    price=product_data.get("price") * 100,
                    buy_min_quantity=product_data.get("buy_min_qaul"),
                    characteristics=[],
                    stores_external_ids=[store_id],
                    attribute_groups_external_ids=list(attribute_groups_ids),
                    categories_external_ids=[category_id],
                    type="goods",
                )

    async def get_and_convert_data(self) -> schemas.AdapterResult:
        await self.convert_locations_data_to_stores(await self.get_locations_data())

        for store in self.stores:
            categories_data = await self.get_categories_data(store.location_get_order_id)
            self.convert_categories_data(store.external_id, categories_data)

            for category in self.categories.values():
                products_data = await self.get_products_data(store.location_get_order_id, category.get_order_id)
                self.convert_products_data(store.external_id, category.external_id, products_data)

        sorted_categories = sorted(self.categories.values(), key=lambda x: (x.position is None, x.position))
        for index, category in enumerate(sorted_categories):
            category.position = index

        return schemas.AdapterResult(
            stores=self.stores,
            products=list(self.products.values()),
            product_groups=[],
            categories=list(self.categories.values()),
            attribute_groups=list(self.attribute_groups.values()),
        )
