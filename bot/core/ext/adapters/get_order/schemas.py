from typing import Literal

from pydantic import BaseModel


class GetOrderOrderFormItemModSchema(BaseModel):
    uniq_id: str | None # test
    qty: int | None # test


class GetOrderOrderFormItemSchema(BaseModel):
    # uniq_id or product_id must be specified
    uniq_id: str | None = None
    product_id: str | None = None
    product_name: str
    qty: int
    price: int
    modifiers: list[GetOrderOrderFormItemModSchema]


class GetOrderOrderFormSchema(BaseModel):
    shipping_id: int
    payment_id: int
    uniq_id: int
    amount_due: str
    comment: str
    date_type: Literal["user", "near"]
    # date and time must be specified if date_type is "user"
    date: str | None = None
    time: str | None = None
    delivery_cost: int
    order_items: list[GetOrderOrderFormItemSchema]
    customer_fields: list[dict]
    address_fields: list[dict] | None = None
