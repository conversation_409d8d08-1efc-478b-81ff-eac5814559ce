import asyncio
import logging

from incust_api.api import term

import schemas
from core.ext.api.get_order.client_get_order import GetOrderApiClient
from core.loyalty.incust_api import incust
from db import crud
from db.crud.store.order.update import set_external_order
from db.models import Brand, LoyaltySettings, OrderProduct, Store, StoreOrder
from schemas import ExternalSystemTypes
from utils.platform_admins import send_message_to_platform_admins
from .schemas import (
    GetOrderOrderFormItemModSchema, GetOrderOrderFormItemSchema,
    GetOrderOrderFormSchema,
)
from ...funcs import make_comment_for_order


def get_go_shipping_id(shipment: str):
    match shipment:
        case "pickup":
            return 2
        case "delivery":
            return 1
        case _:
            return 2


def get_go_payment_id(payment: str):
    match payment:
        case "online":
            return 2  # TODO: replace to another value
        case "in_store":
            return 1
        case _:
            return 1


def store_order_product_to_go_order_product(
        product: OrderProduct
) -> GetOrderOrderFormItemSchema:
    modifiers = list()
    product_data = {
        "product_id": product.product.get_order_id,
        "product_name": product.name,
        "qty": product.quantity,
        "price": product.price / 100,
    }

    for ga in product.attribute_groups:
        for attr in ga.attributes:
            modifiers.append(
                {
                    "uniq_id": attr.attribute.external_id,
                    "qty": attr.quantity,
                }
            )

    res = GetOrderOrderFormItemSchema(
        **product_data,
        modifiers=[GetOrderOrderFormItemModSchema(**m) for m in modifiers]
    )

    return res


async def send_order_to_get_order(order: StoreOrder, store: Store) -> bool:
    if store.external_type != ExternalSystemTypes.get_order.value:
        return False
    # if not order.store.brand.is_get_order:
    #     return False
    if order.get_order_id:
        return False

    try:
        data = dict()
        get_order_order_id = None

        shipment = await crud.get_order_shipment(order.id)
        comment = make_comment_for_order(order, shipment, True)

        data["shipping_id"] = get_go_shipping_id(shipment.base_type)
        data["payment_id"] = get_go_payment_id(order.payment_method)
        data["comment"] = comment
        data["uniq_id"] = order.id
        data["amount_due"] = str(order.total_sum / 100)
        data[
            "date_type"] = "user" if (order.desired_delivery_date and
                                      order.desired_delivery_time) else "near"
        if data["date_type"] == "user":
            data["date"] = order.desired_delivery_date.strftime("%Y-%m-%d")
            if shipment.delivery_datetime_mode == "datetime":
                data["time"] = order.desired_delivery_time
        data["delivery_cost"] = 0
        data["customer_fields"] = [{
            "name": order.first_name + " " + order.last_name,
            "phone": order.phone,
            "email": order.email,
        }]

        if shipment.base_type == "delivery":
            data["address_fields"] = [{
                "street": order.address_street,
                "house": order.address_house,
                "flat": order.address_flat,
                "entrance": order.address_entrance,
                "floor": order.address_floor,
            }]

        data["order_items"] = [store_order_product_to_go_order_product(i) for i in
                               order.order_products]
        res = GetOrderOrderFormSchema(**data)

        client = GetOrderApiClient(order.store.brand)

        location_get_order_id = store.location_get_order_id

        response = await client.make_order(location_get_order_id, res)
        # print("*** send get order order", response)

        # if response and response["data"] and response["data"]["id"]:
        #     await order.set_get_order_id(response["data"]["id"])
        status_ = None
        try:
            get_order_order_id = response["data"]["id"]
            status_ = response.get('data', {}).get('status')
            await order.set_get_order_id(get_order_order_id)
            status = 'success'
        except Exception:
            status = 'failed'

        if get_order_order_id:
            max_get_order_req = 10
            cnt_req = 0
            while status_ == 'new' and cnt_req < max_get_order_req:
                cnt_req += 1
                response = await client.get_order(get_order_order_id)
                status_ = response.get('data', {}).get('status')
                print(f'** current status_: {status_}')

                if status_ in ('error', 'finish'):
                    status = 'success' if status_ == 'finish' else 'failed'
                    print(f'** new order status finded & set to: {status}')
                    break
                await asyncio.sleep(1)

        await set_external_order(
            store_order=order,
            external_order_id=get_order_order_id,
            external_type=ExternalSystemTypes.get_order.value,
            json_data=response,
            status=status,
        )

    except Exception as ex:
        logger = logging.getLogger()
        logger.error(f"***sending order to getorder: {ex}")
        return False

    return True


async def sync_orders(orders: list[StoreOrder], brand: Brand) -> list[StoreOrder]:
    client = GetOrderApiClient(brand)
    for order in orders:
        if order.get_order_id and order._status in ('open_confirmed',
                                                    'open_unconfirmed'):
            go_order = await client.get_order_status_pos(order.get_order_id)
            if go_order:
                if go_order["data"]["status"]:
                    # Отримуємо пов'язаний invoice
                    invoice = await order.get_invoice()
                    loyalty_settings = None

                    # Перевіряємо чи замовлення має лояльність через Invoice
                    if invoice and invoice.incust_transaction_id:
                        # Отримуємо налаштування лояльності
                        loyalty_settings = await LoyaltySettings.get(
                            invoice.loyalty_settings_id
                        ) if invoice and invoice.loyalty_settings_id else \
                            await (
                                crud.get_loyalty_settings_for_context(
                                    "store",
                                    schemas.LoyaltySettingsData(
                                        brand_id=brand.id,
                                        store_id=order.store_id,
                                        profile_id=brand.group_id,
                                    )
                                )
                            )
                    need_process_loyalty = False

                    match go_order["data"]["status"]:
                        case "accept":
                            await order.confirm()
                        case "reject":
                            await order.cancel()
                            need_process_loyalty = True
                        case "ready":
                            await order.confirm()
                        case "close":
                            await order.closed()
                            await order.confirm_pay()
                            need_process_loyalty = True
                        case _:
                            continue
                    if (need_process_loyalty and invoice and
                            invoice.incust_transaction_id and loyalty_settings):
                        try:

                            # Перевіряємо чи транзакція лояльності вже завершена
                            if invoice and invoice.is_loyalty_transaction_completed:
                                continue  # Пропускаємо, транзакція вже завершена

                            # Отримуємо transaction_id
                            transaction_id = None
                            if invoice:
                                transaction_id = invoice.incust_transaction_id

                            if transaction_id:
                                # Скасовуємо транзакцію через новий API
                                async with incust.term.CheckTransactionsApi(
                                        loyalty_settings
                                ) as api:
                                    await api.transaction_cancel(
                                        term.m.TransactionCancelRequest(
                                            transaction_id=transaction_id,
                                            comment="Order cancelled via GetOrder "
                                                    "integration"
                                        )
                                    )
                        except Exception as ex:
                            logger = logging.getLogger()
                            logger.error(ex, exc_info=True)

                            err_text = "InCust loyalty transaction close error"
                            err_text += f"\nOrder_id: {order.id}"
                            err_text += f"\nError: {str(ex)}"

                            await send_message_to_platform_admins(err_text)
    return orders


async def check_valid_account(
        get_order_username: str, get_order_password: str, **_
) -> bool:
    client = GetOrderApiClient(None)
    response = await client.get_auth_token(get_order_username, get_order_password)
    if response and response.get("status") == "operation_success":
        return True
    return False
