import logging

from core.ext.api.poster.client_poster import PosterApiClient
from core.store.order.service import change_store_order_status
from db import crud, models
from db.crud.store.order.update import set_external_order
from db.models import BrandSettings, Group, Store
from schemas import (
    ExtSysSetTypes, ExternalSystemTypes, OrderShippingStatusEnum,
    StoreProductPosterCheck,
)
from schemas.store import NotAvailableProduct
from utils.numbers import format_currency
from utils.text import f
from .schemas import (
    ClientAddress, DishModification, Payment, PosterApiOrderCreate, PosterProductShort,
)

logger = logging.getLogger('debugger.poster')


async def get_modifications_from_product(product: models.OrderProduct) -> dict[str, int | str | list[DishModification]]:
    modifiers = list()
    modifier_id: str | None = None
    sum_price_modifiers: int = 0
    order_attributes = [attr.attribute_id for attr in product.attributes]
    store_attributes = await crud.get_attributes_by_ids(order_attributes)

    for order_attribute in product.attributes:
        for store_attribute in store_attributes:
            if order_attribute.attribute_id == store_attribute.id:
                sum_price_modifiers += store_attribute.price_impact
                if store_attribute.attribute_group.attribute_group_id.startswith(
                        ExternalSystemTypes.poster.value
                ):
                    modifier_id = store_attribute.external_id.replace('poster_', '')
                    break

                modifiers.append(
                    {
                        "m": store_attribute.external_id.replace('poster_', ''),
                        "a": order_attribute.quantity,
                    }
                )

        if modifier_id:
            break

    if modifiers:
        modifiers = [DishModification(**m) for m in modifiers]

    result = dict(
        modificator_id=modifier_id,
        modifiers=modifiers,
        sum_price_modifiers=sum_price_modifiers,
    )

    return result


async def convert_store_order_to_poster_order(store_order: models.StoreOrder) -> PosterApiOrderCreate:

    store = await Store.get(store_order.store_id)
    spot_id = store.external_id
    brand_id = store.brand_id

    group: Group = await crud.get_group_by_store(store_order.store_id)
    lang = group.lang

    tips_product = None
    brand_settings = await BrandSettings.get_by_brand_and_type(
        brand_id,
        ExtSysSetTypes.poster_tips_sku.value,
    )
    if brand_settings and brand_settings.value_data:
        poster_tips_sku = brand_settings.value_data
        tips_product = await crud.get_product_as_tips(brand_id, poster_tips_sku)

    user_comments = [f'7LOC_ID: {store_order.id}']

    if store_order.tips_sum and not tips_product:
        tips_text = format_currency(
            store_order.tips_sum / 100, store_order.store.currency, locale=group.lang
        )
        tips_sum_text = f"\n{await f('tips header', lang)}: {tips_text}"
        user_comments.append(tips_sum_text)

    if store_order.discount_and_bonuses == store_order.before_loyalty_sum:
        user_comments.append(await f('loyalty bonuses header', lang))

    if store_order.menu_in_store:
        user_comments.append(f"QR: {store_order.menu_in_store.comment}")

    if store_order.address_comment:
        user_comments.append(f'*USER COMMENT: {store_order.address_comment}')

    poster_order = PosterApiOrderCreate(
        spot_id=spot_id,
        first_name=store_order.first_name if store_order.first_name else await f("menu in store guest text", lang),
        last_name=store_order.last_name if store_order.last_name and len(store_order.last_name) > 1 else '',
        phone=store_order.phone if store_order.phone else '950000000',
        email=store_order.email if store_order.email else '',
        # Створює замовлення зазначеного типу: 1 - обід, 2 - винос, 3 - доставка
        service_mode=1,
        products=[],
        comment='; '.join(user_comments)
    )

    # Add each order product as a new product in the Poster order
    for order_product in store_order.order_products:
        product = PosterProductShort(
            product_id=order_product.product.external_id,
            count=order_product.quantity if not order_product.product.is_weight else order_product.quantity * 100,
            price=order_product.total_sum,
        )

        result = await get_modifications_from_product(order_product)

        if result.get('modificator_id'):
            product.modificator_id = result.get('modificator_id')
        elif result.get('modifiers'):
            product.modification = result.get('modifiers')

        #
        # if order_product.price_after_loyalty > 0:
        #     product.price = order_product.price_after_loyalty
        # else:
        #     product.price = product.price + result.get('sum_price_modifiers')

        poster_order.products.append(product)

    if store_order.tips_sum > 0 and tips_product:
        poster_order.products.append(
            PosterProductShort(
                product_id=tips_product.external_id,
                count=1,
                price=store_order.tips_sum,
            )
        )

    shipment = await crud.get_order_shipment(store_order.id)
    if shipment.base_type == 'delivery':
        poster_order.service_mode = 3  # 3 - delivery
        poster_order.delivery_price = shipment.price * 100

    if store_order.delivery_address:
        poster_order.client_address = ClientAddress(
            address1=store_order.delivery_address,
        )

    # Add the payment details if available
    if store_order.payment_method and store_order.invoice:

        if store_order.discount_and_bonuses == store_order.before_loyalty_sum:
            total_sum = 0
        else:
            total_sum = store_order.total_sum
        if total_sum != 0 and tips_product:
            total_sum += store_order.tips_sum

        payment = Payment(
            # 1 - payed, 0 - not payed - but response error
            # 2 - response ok
            type=2,
            # if store_order._status_pay == 'payed' else 0,
            sum=total_sum,
            currency=store_order.invoice.currency
        )
        poster_order.payment = payment

    return poster_order


async def check_poster_stocks(
        brand_id: int, store_external_id: str, products: list[StoreProductPosterCheck]
) -> list[NotAvailableProduct]:
    invalid_products = []
    try:
        poster_settings = await crud.get_poster_settings(brand_id)
        client = PosterApiClient(brand_id, poster_settings.poster_api_token)

        for product in products:
            if product.external_type is None or product.external_type != "poster" or not product.external_id:
                continue
            poster_item_resp = await client.get_product(product_id=product.external_id)
            try:
                poster_item_resp['response']['product_id']
            except KeyError:
                logger.debug(f"check_poster_stock -> {brand_id=}: poster product {product.external_id} not in stock")
                invalid_products.append(
                    NotAvailableProduct(
                        product_id=product.product_id,
                        name=product.name,
                    )
                )
                continue

            if poster_item_resp.get('hidden') == '1':
                invalid_products.append(
                    NotAvailableProduct(
                        product_id=product.product_id,
                        name=product.name,
                    )
                )

            for spot in poster_item_resp['response'].get('spots', []):
                if spot['spot_id'] != store_external_id:
                    continue
                if spot['visible'] == '0':
                    invalid_products.append(
                        NotAvailableProduct(
                            product_id=product.product_id,
                            name=product.name,
                        )
                    )
                    break

    except Exception as ex:
        logger.error(f"check_poster_stock -> {brand_id=} FAILED: {ex}", exc_info=True)
    finally:
        return invalid_products


async def send_order_to_poster(store_order: models.StoreOrder, store: models.Store) -> dict | None:
    poster_order_resp: dict | None = None
    if store.external_type != ExternalSystemTypes.poster.value:
        return poster_order_resp
    try:
        order_data = await convert_store_order_to_poster_order(store_order)
        poster_settings = await crud.get_poster_settings(store.brand_id)

        client = PosterApiClient(store.brand_id, poster_settings.poster_api_token)
        poster_order_resp = await client.make_order(order_data=order_data)
        poster_order_id = None

        try:
            poster_order_id = poster_order_resp.get('response').get('incoming_order_id')
            status = 'success'
        except Exception as err:
            logger.error(f"send_order_to_poster -> {store.brand_id=} FAILED: {err}", exc_info=True)
            status = 'failed'

        await set_external_order(
            store_order=store_order,
            external_order_id=poster_order_id,
            external_type=ExternalSystemTypes.poster.value,
            json_data=poster_order_resp,
            status=status,
        )

    except Exception as ex:
        logger.error(f"send_order_to_poster -> {store.brand_id=} FAILED: {ex}", exc_info=True)
    finally:
        return poster_order_resp


async def poster_sync_orders(orders: list[models.StoreOrder], brand: models.Brand) -> list[models.StoreOrder]:
    poster_settings = await crud.get_poster_settings(brand.id)
    client = PosterApiClient(brand.id, poster_settings.poster_api_token)

    for order in orders:
        if order.external_order \
                and order.external_order.external_type == 'poster' \
                and order.external_order.external_order_id \
                and order._status in ('open_confirmed', 'open_unconfirmed'):
            poster_order = await client.get_order_status_pos(order.external_order.external_order_id)
            if not poster_order:
                continue
            if 'status' in poster_order:
                match poster_order.get('status'):
                    # case 0: # new order
                    #     await order.confirm()
                    case 7:  # reject
                        # await order.cancel()
                        await change_store_order_status(
                            order,
                            OrderShippingStatusEnum.CANCELED.value,
                            "external",
                            None,
                            "poster sync"
                        )
                    case 1:  # accept
                        # await order.confirm()
                        await change_store_order_status(
                            order,
                            OrderShippingStatusEnum.OPEN_CONFIRMED.value,
                            "external",
                            None,
                            'poster sync'
                        )
                        check = await client.get_check(poster_order.get('transaction_id'))
                        if not check:
                            continue
                        match check.get('status'):
                            case '2':  # 2 - closed
                                # await order.closed()
                                await change_store_order_status(
                                    order,
                                    OrderShippingStatusEnum.CLOSED.value,
                                    "external",
                                    None,
                                    'poster sync'
                                )
                            case '3':  # 3 - deleted
                                # await order.cancel()
                                await change_store_order_status(
                                    order,
                                    OrderShippingStatusEnum.CANCELED.value,
                                    "external",
                                    None,
                                    'poster sync'
                                )
                        if 'sum' in check:
                            if check.get('pay_type') in ('1', '2', '3'):
                                if order._status_pay in ('must_pay', 'processing') \
                                        and order.payment_method != 'online':
                                    await change_store_order_status(
                                        order,
                                        OrderShippingStatusEnum.PAYED.value,
                                        "external",
                                        'poster terminal',
                                        'poster sync'
                                    )
                            elif check.get('pay_type') == '0':
                                logger.debug(f"poster_sync_orders -> {brand.id=} {check.get('reason')=}")
                    case _:
                        continue
    return orders
