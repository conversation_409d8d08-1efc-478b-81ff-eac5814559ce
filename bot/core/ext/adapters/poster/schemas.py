from datetime import date, datetime
from enum import unique
from typing import Any, List, Optional

from pydantic import BaseModel, EmailStr, ValidationError

from schemas import EnumWithValues


# Poster API response\request models
class PosterApiOrderProductModification(BaseModel):
    m: int  # Id модификации тех. карты в Poster
    a: int  # Количество модификации тех. карты в Poster


class PosterApiOrderProduct(BaseModel):
    product_id: int  # Poster id
    modificator_id: int  # Poster id
    # Модификации тех. карты
    # Внутри параметра modification должна быть JSON строка. JSON должен состоять из массива объектов
    modification: str | None = None
    count: int
    price: int | None = None  # Цена товара в копейках, по умолчанию берется цена товара в указанном заведении


class PosterApiCreateOnlineOrderRequest(BaseModel):
    spot_id: int
    # Id клиента в Poster, если id не указан, то нужно передать параметр phone.
    # Poster попробует найти клиента с таким же номером телефона и привяжет его к заказу.
    # Если это новый клиент, то официант выберет для него группу и Poster создаст нового клиента.
    client_id: int | None = None
    first_name: str | None = None
    last_name: str | None = None
    phone: str | None = None
    email: EmailStr | None = None
    sex: bool | None = None
    # формат Y-m-d
    birthday: date | None = None
    # ID адреса клиента в Postr, по умолчанию не передаётся.
    # Если передать этот параметр то адрес клиента возметься
    # исходя из найденного id, проигнорировав параметр client_address
    client_address_id: str | None = None
    # Создает заказ указанного типа: 1 — в заведении, 2 — навынос, 3 — доставка
    service_mode: int | None = None
    # Стоимость доставки в копейках, должно быть целым числом.
    # Указывается только для заказов с типом service_mode = 3.
    delivery_price: int | None = None
    comment: str | None = None
    products: list[PosterApiOrderProduct]
    payment: Any | None = None
    # promotion: PosterApiProductPromotion | None = None
    delivery_time: datetime | None = None

    def __init__(self, **data):
        super().__init__(**data)
        if not self.client_id and not self.phone:
            raise ValidationError(["not client_id and not phone"], BaseModel)
        if not self.service_mode == 3 and self.delivery_price:
            raise ValidationError(["not service_mode == 3 and delivery_price"], BaseModel)


class PosterApiStorage(BaseModel):
    storage_id: str
    storage_name: str
    storage_adress: str


class PosterApiStore(BaseModel):
    spot_id: str
    spot_name: str | None
    spot_adress: str | None
    lat: str | None
    lng: str | None
    storages: list[PosterApiStorage] | None


class PosterApiStores(BaseModel):
    response: list[PosterApiStore]


class PosterApiSpot(BaseModel):
    spot_id: str  # Id заведения
    visible: int  # Признак, что категория видна в этом заведении: 0 — скрыта, 1 — видна


class PosterApiProductSpot(PosterApiSpot):
    price: int
    profit: int
    profit_netto: int


class PosterApiCategoryData(BaseModel):
    category_id: str
    category_name: str
    category_photo: str | None = None
    category_photo_origin: str | None = None
    parent_category: str
    category_color: str  # Цвет категории
    category_hidden: bool  # Признак, что категория скрыта: 0 — не скрыта, 1 — скрыта
    sort_order: int
    fiscal: bool  # Признак фискальности категории: 0 — не фискальная, 1 — фискальная
    nodiscount: bool  # Признак, что распространяются скидки: 0 — не распространяются, 1 — распространяются
    tax_id: int  # Id налога
    left: int  # Id категории слева (по Nested Set)
    right: int  # Id категории справа (по Nested Set)
    level: int  # Уровень вложенности ветки дерева категорий (по Nested Set)
    # category_tag - Предполагаемый тип продуктов в категории,
    # который определил алгоритм машинного обучения Poster.
    # Например, coffee, alcohol, может быть null.
    category_tag: str | None = None
    visible: list[PosterApiSpot]  # Массив в каждом объекте которого есть признак видимости категории в заведении
    id_1c: str | None = None  # Id категории товаров в системе 1С


class PosterApiCategory(BaseModel):
    response: PosterApiCategoryData


class PosterApiCategories(BaseModel):
    response: list[PosterApiCategoryData]


@unique
class PosterApiProductType(EnumWithValues):
    # Product type: 1—semi-finished, 2—dish, 3—product
    # semi_finished: str = '1'
    dish: str = '2'
    product: str = '3'


class PosterApiProductModificator(BaseModel):
    modificator_id: str
    modificator_name: str | None
    modificator_selfprice: str | None
    modificator_selfprice_netto: str | None
    order: int | None
    modificator_barcode: str | None
    modificator_product_code: str | None
    spots: list[dict] | None
    ingredient_id: str | None
    fiscal_code: str | None
    master_id: str | None


class PosterApiDishModification(BaseModel):
    dish_modification_id: str  # Modification ID
    name: str  # Dish modification name
    ingredient_id: str  # Ingredient ID (returned if it’s the product or ingredient)
    type: str  # Type of dish modification: 1 - product, 2 - dish, 3 - prepack, 8 - product modification, 10 - ingredient, 0 - No extra ingredients
    brutto: str  # Brutto of dish modification
    price: int  # Price of dish modification in money
    photo_orig: str  # Original photo of dish modification
    photo_large: str  # Large photo of dish modification
    photo_small: str  # Small photo of dish modification
    last_modified_time: str  # Last modified time of dish modification


class PosterApiGroupModification(BaseModel):
    dish_modification_group_id: str
    name: str
    num_min: int
    num_max: int
    is_deleted: int  # Is the set of dish modifications deleted. 0 - no, 1 - yes
    modifications: list[PosterApiDishModification]  # Array of modifications in a set


# TODO: дополнить, перепроверить
class PosterApiProductData(BaseModel):
    product_id: str
    product_name: str
    sort_order: int
    barcode: str | None = None
    category_name: str
    hidden: bool
    unit: str | None = None  # Единица измерения товара
    cost: int | None = None
    cost_netto: int | None = None
    fiscal: bool  # Признак фискальности категории: 0 — не фискальная, 1 — фискальная
    menu_category_id: str
    workshop: int  # id цеха
    nodiscount: bool
    photo: str | None = None
    photo_origin: str | None = None
    product_code: str | None
    tax_id: int
    # Признак, что налог товара унаследован от налога категории: 0 — не унаследован, 1 — унаследован
    product_tax_id: int
    type: PosterApiProductType  # Тип товара: 1 — полуфабрикат, 2 — тех.карта, 3 — товар
    weight_flag: bool  # Весовой ли товар
    color: str  # Цвет карточки товара в терминале
    spots: list[PosterApiProductSpot | None] | None
    modifications: list[PosterApiProductModificator | None] | None = None
    group_modifications: list[PosterApiGroupModification] | None = None
    product_production_description: str | None = None  # descript production for dish. type = 2
    out: float | None  # total weight good


class PosterApiProducts(BaseModel):
    response: list[PosterApiProductData]


class PosterApiRespOrderProduct(BaseModel):
    io_product_id: int
    product_id: int
    modificator_id: Optional[int]
    incoming_order_id: int
    count: int
    created_at: str


class PosterApiRespOrder(BaseModel):
    incoming_order_id: int
    type: int
    spot_id: int
    status: int
    client_id: int
    first_name: str
    last_name: str
    phone: str
    email: str
    sex: int
    birthday: Optional[str]
    client_address: Optional[int]
    address: Optional[str]
    comment: Optional[str]
    created_at: str
    updated_at: str
    transaction_id: Optional[int]
    fiscal_spreading: Optional[int]
    fiscal_method: Optional[str]
    delivery_time: Optional[str]
    products: List[PosterApiRespOrderProduct]


class PosterApiOrderResponse(BaseModel):
    response: PosterApiRespOrder


class DishModification(BaseModel):
    m: int
    a: int


class InvolvedProduct(BaseModel):
    id: int
    count: int
    modification: Optional[List[DishModification]] = None


class ResultProduct(BaseModel):
    id: int
    count: int
    modification: Optional[List[DishModification]] = None


class Promotion(BaseModel):
    id: int
    involved_products: List[InvolvedProduct]
    result_products: List[ResultProduct]


class Payment(BaseModel):
    type: int = 0
    sum: int
    currency: str


class ClientAddress(BaseModel):
    address1: str
    address2: Optional[str] = None
    comment: Optional[str] = None
    lat: Optional[float]
    lng: Optional[float]


class PosterProductShort(BaseModel):
    product_id: str
    modificator_id: Optional[int] = None
    modification: Optional[List[DishModification]] = None
    count: int
    price: Optional[int] = None


class PosterApiOrderCreate(BaseModel):
    spot_id: int
    client_id: Optional[int] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    sex: Optional[int] = None
    birthday: Optional[str] = None
    client_address_id: Optional[int] = None
    client_address: Optional[ClientAddress] = None
    service_mode: int
    delivery_price: Optional[int] = None
    comment: Optional[str] = None
    products: List[PosterProductShort]
    payment: Optional[Payment] = None
    promotion: Optional[List[Promotion]] = None
    delivery_time: Optional[str] = None


class PosterApiOrderSource(BaseModel):
    id: int
    name: str
    visible: int
    type: int


class PosterApiOrderSources(BaseModel):
    response: List[PosterApiOrderSource]


class PosterPage(BaseModel):
    per_page: int
    page: int
    count: int


class PosterProduct(BaseModel):
    product_id: int
    modification_id: int
    type: int
    workshop_id: int
    num: int
    product_sum: str
    payed_sum: str
    cert_sum: str
    bonus_sum: str
    bonus_accrual: str
    round_sum: str
    discount: int
    print_fiscal: int
    tax_id: int
    tax_value: int
    tax_type: int
    tax_fiscal: int
    tax_sum: str


class PosterTransaction(BaseModel):
    transaction_id: int
    table_id: int
    spot_id: int
    client_id: int
    sum: str
    payed_sum: str
    payed_cash: str
    payed_card: str
    payed_cert: str
    payed_bonus: str
    payed_third_party: str
    round_sum: str
    pay_type: int
    reason: int
    tip_sum: str
    bonus: int
    discount: int
    print_fiscal: int
    date_close: str
    products: List[PosterProduct]


class PosterTransactionResponse(BaseModel):
    count: int
    page: PosterPage
    data: List[PosterTransaction]


class PosterApiTransactions(BaseModel):
    response: PosterTransactionResponse


class PosterApiErrors(BaseModel):
    error: int
    message: str


class PosterInstanceCategoryWithProducts(BaseModel):
    category: PosterApiCategoryData
    products: list[PosterApiProductData]


class PosterInstance(BaseModel):
    store: PosterApiStore
    categories: list[PosterInstanceCategoryWithProducts]
    order_sources: list[PosterApiOrderSource]


class PosterShippingOptionSchema(BaseModel):
    id: int  # 1 - delivery, 2 - pickup
    name: str
    is_active: int
    type: str
    published: int


class PosterTable(BaseModel):
    table_id: int
    table_num: str
    table_title: str
    spot_id: int
    table_shape: str
    hall_id: int
    is_deleted: str
    status: str


class PosterTablesResponse(BaseModel):
    response: list[PosterTable]


class PosterHall(BaseModel):
    hall_id: int
    hall_name: str
    hall_order: str
    spot_id: int
    delete: str
    last_modified_time: str


class PosterResponseHalls(BaseModel):
    response: list[PosterHall]
