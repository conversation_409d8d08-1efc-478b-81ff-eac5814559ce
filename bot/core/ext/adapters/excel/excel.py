import os
from typing import Any

from numbers_parser import Document
from numbers_parser.document import Sheet, Table
from openpyxl.reader.excel import load_workbook
from openpyxl.workbook import Workbook
from openpyxl.worksheet.worksheet import Worksheet
from psutils.decorators import sync_to_async

import config as cfg
from .base import BaseExcelAdapter
from ...types import ExternalType


class ExcelAdapter(BaseExcelAdapter, external_type=ExternalType.EXCEL.value):

    def __init__(self, lang: str, excel_file: str | None = None, *args, **kwargs):
        brand = kwargs.get("brand")
        brand_id = brand.id if brand else None
        path_to_save: str = os.path.join(cfg.STORE_BRAND_STATIC_PATH_FOR_EXCEL, str(brand_id), "documents")
        super().__init__(lang, *args, **{**kwargs, "path_to_save": path_to_save})

        if self.import_or_export == "import" and not excel_file:
            raise ValueError("Excel file is required when importing")

        self.excel_file = excel_file
        self.workbook: Workbook | None = None
        self.numbers_document: Document | None = None

    @sync_to_async
    def get_all_values(self, sheet_name: str) -> list[tuple[Any]]:
        if self.excel_file.endswith(".numbers"):
            if not self.numbers_document:
                self.numbers_document = Document(self.excel_file)
            sheet: Sheet = self.numbers_document.sheets[sheet_name]  # type: ignore
            table: Table = sheet.tables[0]
            data = table.rows(True)

            for i, row in enumerate(data):
                for j, col in enumerate(row):
                    if col == "":
                        data[i][j] = None

            return data

        if not self.workbook:
            self.workbook = load_workbook(self.excel_file)
        ws: Worksheet = self.workbook[sheet_name]
        return list(ws.values)

    async def save_data(
            self, data: dict,
            langs_list: list[str],
            brand_lang: str,
            translations: dict | None = None,
    ) -> Any:
        wb: Workbook = Workbook()
        ws = wb.active
        wb.remove(ws)

        to_export = await self.get_cells_to_export(data, langs_list, translations)
        for title, cells_data in to_export.items():
            wb.create_sheet(title)
            ws: Worksheet = wb[title]

            cells = cells_data.get_data()
            for cell in cells:
                ws.cell(row=cell.row_id, column=cell.col_id, value=cell.value)

        return self.__save_excel_file__(wb)
