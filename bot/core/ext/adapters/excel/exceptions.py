from core.adapters.excel.exceptions import ExcelAdapterFieldError, FieldNameVariable

from ...exceptions import StoreImportError


class ExcelAdapterUnknownFieldError(ExcelAdapterFieldError):
    text_variable = "importer unknown field error"

    def __init__(
            self,
            schema_name_variable: str,
            row_number: int,
            field_name: str | FieldNameVariable,
    ):
        super().__init__(schema_name_variable, row_number, field_name)


class ExcelAdapterNotFoundSheetError(StoreImportError):
    text_variable = "importer not found sheet error"

    def __init__(self, sheet_name: str):
        super().__init__(
            "importer not found sheet error",
            sheet_name=sheet_name,
        )
