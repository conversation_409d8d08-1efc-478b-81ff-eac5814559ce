from __future__ import annotations

import asyncio
import re
from collections import defaultdict
from typing import Iterable, Type, TypeVar, get_args

from config import MAX_ALLOWED_PRICE, NOT_LANG_FIELDS
from core.adapters import CellsData
from core.adapters.excel import BaseExcelModel, Coordinates<PERSON>ield, Field
from core.ext import schemas
from schemas import CharacteristicFilterType
from utils.text import f
from .exceptions import ExcelAdapterUnknownFieldError
from ...funcs import get_store_custom_field_names


async def get_type_str(type_: type, lang: str) -> str:
    if args := get_args(type_):
        return ", ".join(
            await asyncio.gather(*map(lambda x: get_type_str(x, lang), args))
        )

    if type_ is None:
        type_name = "none"
    else:
        type_name = type_.__name__

    return await f(f"importer {type_name} type text", lang)


class ExcelModel(BaseExcelModel, base=True):
    _fields_with_translations: tuple | None = None

    def __init_subclass__(cls, **kwargs):
        cls._allow_custom_fields = kwargs.pop("allow_custom_fields", False)
        cls._allowed_custom_fields = kwargs.pop("allowed_custom_fields", "*")

        fields_with_translations = kwargs.pop("fields_with_translations", None)
        if fields_with_translations is not None:
            if isinstance(fields_with_translations, str):
                fields_with_translations = (fields_with_translations,)

            if not isinstance(fields_with_translations, Iterable):
                raise TypeError("fields_with_translations must be an iterable")

            if not isinstance(fields_with_translations, tuple):
                fields_with_translations = tuple(fields_with_translations)
            cls._fields_with_translations = fields_with_translations
        else:
            cls._fields_with_translations = tuple()

        super().__init_subclass__(**kwargs)

    def __init__(
            self, row_number: int,
            values: dict[str, str] | None = None,
            custom_fields: dict[str, str] | None = None,
            fields_place: dict[str, str] | None = None,
    ):
        super().__init__(row_number, values, custom_fields)

        self.translations: dict[str, dict[str, str]] = {}
        self.custom_fields_translations: dict[str, dict[str, str]] = {}
        self.fields_place = fields_place or {}

    def add_translation(self, name: str, value: str, lang: str):
        if name in self._fields_with_translations:
            translations = self.translations
        else:
            translations = self.custom_fields_translations

        if not translations.get(lang):
            translations[lang] = {}
        translations[lang].update({name: value})

    @classmethod
    async def make_headers(cls, cells: CellsData, lang: str, langs_list: list[str]):
        verbose_names = await cls.get_verbose_names(lang)
        col_id = 1
        for field in cls.__get_fields_for_saving__():
            verbose_name = verbose_names[field.name]
            cells.add(1, col_id, verbose_names[field.name])
            col_id += 1

            if field.name not in cls._fields_with_translations:
                continue

            for translation_lang in langs_list:
                cells.add(1, col_id, f"{verbose_name}_{translation_lang}")
                col_id += 1

    async def make_row_data(self, cells: CellsData, langs_list: list[str], lang: str):
        fields = self.__get_fields_for_saving__()

        col_id = 1
        for field in fields:
            if field.name == "coordinates":
                coordinates = [self.values.get("latitude"),
                               self.values.get("longitude")]
                value = ",".join(coordinates) if all(coordinates) else None

            else:
                value = self.values.get(field.name)
                if field.name in ("price", "old_price", "price_impact",):
                    value = value / 100 if value else value
                    if value and not value % 1:
                        value = int(value)
                elif value and field.name == "filter_type":
                    value = await f(f"store filter type {value.value} text", lang)

            if isinstance(value, list):
                value = "\n".join(value)
            elif isinstance(value, bool):
                value = "+" if value else "-"
            cells.add(self.row_number, col_id, value)

            col_id += 1
            if field.name in self._fields_with_translations:
                col_id += len(langs_list)

        if self._allow_custom_fields and self.custom_fields:
            for name, value in self.custom_fields.items():
                col_id = cells.find_column_by_name(name)
                if col_id:
                    cells.add(self.row_number, col_id, value)
                else:
                    col_id = cells.max_column + 1
                    cells.add(1, col_id, name)
                    cells.add(self.row_number, col_id, value)

    def make_translations(
            self, cells: CellsData,
            langs_list: list[str],
            translations: list[schemas.Translation],
    ):
        if not translations:
            return

        fields = self.__get_fields_for_saving__()

        for translation in translations:
            if translation.lang not in langs_list:
                continue

            col_id = 1
            for field in fields:
                if field.name in self._fields_with_translations:
                    cells.add(
                        self.row_number,
                        col_id + 1 + langs_list.index(translation.lang),
                        getattr(translation, field.name),
                    )
                    col_id += len(langs_list)
                col_id += 1

    @classmethod
    async def make_from_raw_data(
            cls: Type[ModelType],
            data: list[list[str] | tuple[str]],
            lang: str,
    ) -> list[ModelType]:
        headers, data = data[0], data[1:]

        columns_fields: dict[int, list[str]] = defaultdict(list)
        custom_fields_columns: dict[int, str] = {}

        used_fields = set()
        translations: dict[int, tuple[str, str]] = {}

        fields_place: dict[str, str] = {}

        for i, header in enumerate(headers):
            if not header:
                continue

            header = str(header)

            async for name, place in cls.check_fields(header, lang):
                if name in used_fields:
                    continue

                columns_fields[i].append(name)
                used_fields.add(name)
                fields_place[name] = place

            if i in columns_fields:
                continue

            if re.fullmatch(
                    r".*_[a-zA-Z]{2}", header, flags=re.IGNORECASE
            ) and header not in NOT_LANG_FIELDS:
                name, translation_lang = header.rsplit("_", 1)

                is_field = False
                async for field_name, _ in cls.check_fields(name, lang):
                    is_field = True
                    translations[i] = (field_name, translation_lang)

                if not is_field:
                    translations[i] = (name.lower().strip(), translation_lang)
                continue

            if (cls._allow_custom_fields and cls._allowed_custom_fields == "*" or
                    header in cls._allowed_custom_fields):
                custom_fields_columns[i] = header
                continue

            raise ExcelAdapterUnknownFieldError(cls.get_schema_variable(), 0, header)

        result: list[ModelType] = []

        if cls.__name__ == "Characteristic":
            filter_types = list(CharacteristicFilterType)
            filter_types_localisation = [
                await f(f"store filter type {filter_type.value} text", lang)
                for filter_type in filter_types
            ]
        else:
            filter_types = []
            filter_types_localisation = []

        for i, row in enumerate(data):
            if all(map(lambda x: x is None, row)):
                continue

            obj = cls(i, fields_place=fields_place)

            for column_idx, column_names in columns_fields.items():
                for column_name in column_names:
                    if column_name == "filter_type":
                        try:
                            index = filter_types_localisation.index(row[column_idx])
                            value = filter_types[index].value
                        except Exception:
                            value = CharacteristicFilterType.VALUE.value
                        setattr(obj, column_name, value)
                    else:
                        setattr(obj, column_name, row[column_idx])

            for idx, name_lang in translations.items():
                name, translation_lang = name_lang
                value = row[idx]
                obj.add_translation(name, value, translation_lang)

            if cls._allow_custom_fields:
                for column_idx, column_name in custom_fields_columns.items():
                    obj.custom_fields[custom_fields_columns[column_idx]] = row[
                        column_idx]

            result.append(obj)

        return result


ModelType = TypeVar("ModelType", bound="ExcelModel")


class Store(
    ExcelModel,
    allow_custom_fields=True,
    allowed_custom_fields=get_store_custom_field_names(),
    fields_with_translations=("name", "description"),
):
    id: int | None = Field("excel payforsay id field")
    external_id: str | None = Field(
        "excel name field", True,
        export_variable="excel external id field",
        to_lower_case=True,
        max_length=255,
    )

    name: str = Field(
        "excel name field", True,
        max_length=255,
    )
    description: str | None = Field(
        "excel description field",
        max_length=1024,
    )
    ai_description: str | None = Field(
        "excel ai description field",
        max_length=4096,
    )

    image_url: str | None = Field("excel image url field")

    coordinates: str | None = CoordinatesField("excel store coordinates field")
    distance: int = Field("excel store distance field", default=0)

    currency: str = Field("excel store currency field", True)

    city: str | None = Field(
        "excel store city field",
        max_length=255,
    )

    position: int | None = Field("excel position field")


class Category(ExcelModel, fields_with_translations="name"):
    id: int | None = Field("excel payforsay id field")
    external_id: str = Field(
        "excel name field", True,
        export_variable="excel external id field",
        to_lower_case=True,
        max_length=255,
    )

    name: str = Field(
        "excel name field", True,
        max_length=255,
    )
    father_category_id: str | None = Field(
        "excel category father category field",
        to_lower_case=True,
    )

    image_url: str | None = Field(
        "excel image url field",
        max_length=255,
    )

    position: int | None = Field("excel position field", export_exclude=True)

    stores_external_ids: list[str] = Field("excel stores field", to_lower_case=True)
    filters: list[str] = Field("excel category filters field", to_lower_case=True)


class Product(
    ExcelModel, allow_custom_fields=True,
    fields_with_translations=("name", "description")
):
    id: int | None = Field("excel payforsay id field")
    external_id: str = Field(
        "excel product id field", True,
        export_variable="excel external id field",
        to_lower_case=True,
        max_length=255,
    )
    product_id: str = Field(
        "excel product id field", True,
        to_lower_case=True,
        max_length=255,
    )
    product_group_id: str | None = Field(
        "excel products group field",
        to_lower_case=True,
    )

    name: str = Field(
        "excel name field", True,
        max_length=255,
    )
    description: str | None = Field("excel description field")

    image_url: str | None = Field(
        "excel image url field",
        max_length=399,
    )
    gallery_items: list[str] = Field(
        "excel gallery items field",
    )

    is_available: bool = Field("excel is available field", default=True)

    price: int | float = Field(
        "excel price field", default=0, max_length=MAX_ALLOWED_PRICE
    )
    old_price: int | float = Field(
        "excel old price field", default=0, max_length=MAX_ALLOWED_PRICE
    )

    buy_min_quantity: int = Field("excel min field", default=1)

    is_weight: bool = Field("excel product is weight field")
    weight_unit: str | None = Field(
        "excel product weight unit field",
        max_length=59,
    )

    position: int | None = Field("excel position field", export_exclude=True)

    categories_external_ids: list[str] = Field(
        "excel categories field",
        to_lower_case=True,
    )
    attribute_groups_external_ids: list[str] = Field(
        "excel attribute groups field",
        to_lower_case=True,
    )
    stores_external_ids: list[str] = Field(
        "excel stores field",
        to_lower_case=True,
    )

    floating_sum_value: str | None = Field(
        "excel product floating price field",
        max_length=255,
    )

    floating_qty_enabled: bool = Field(
        "admin forms floating qty enabled field",
        max_length=255,
        default=False,
    )

    type: str | None = Field(
        "excel product type field",
        max_length=255,
        default="goods",
    )

    need_auth: bool = Field(
        "excel product need auth field",
        default=False,
    )

    spots_prices: dict[str, 'schemas.ProductSpotPrice'] | None = None

    def make_translations(
            self, cells: CellsData,
            langs_list: list[str],
            translations: list[schemas.TranslationProduct],
    ):
        if not translations:
            return

        super().make_translations(cells, langs_list, translations)

        for translation in translations:
            if translation.lang not in langs_list:
                continue

            if translation.pti_info_text:
                name = f"pti_info_text_{translation.lang}"
                col_id = cells.find_column_by_name(name)
                if not col_id:
                    col_id = cells.find_column_by_name("pti_info_text")
                    if col_id:
                        col_id += 1
                        cells.insert_column(col_id)
                    else:
                        col_id = cells.max_column + 1
                    cells.add(1, col_id, name)
                cells.add(self.row_number, col_id, translation.pti_info_text)

            for characteristic in translation.characteristics:
                if characteristic.lang not in langs_list:
                    continue

                name = f"{characteristic.external_id}_{characteristic.lang}"

                col_id = cells.find_column_by_name(name)
                if not col_id:
                    col_id = cells.find_column_by_name(characteristic.external_id)
                    if col_id:
                        col_id += 1
                        cells.insert_column(col_id)
                    else:
                        col_id = cells.max_column + 1
                    cells.add(1, col_id, name)
                cells.add(self.row_number, col_id, characteristic.value)


class ProductGroup(ExcelModel):
    id: int | None = Field("excel payforsay id field")
    external_id: str = Field(
        "excel product group id field", True,
        to_lower_case=True,
        max_length=255,
    )
    name: str = Field(
        "excel name field", True,
        max_length=255,
    )

    modifiers: list[str] = Field(
        "excel product group modifiers field", True,
        to_lower_case=True
    )
    hide_modifications_by: list[str] = Field(
        "excel product group hide modifications by field",
        to_lower_case=True
    )


class AttributeGroup(ExcelModel, fields_with_translations="name"):
    id: int | None = Field("excel payforsay id field")
    attribute_group_id = Field(
        "excel name field", True,
        export_exclude=True,
        to_lower_case=True,
    )
    external_id: str = Field(
        "excel name field", True,
        export_variable="excel external id field",
        to_lower_case=True,
        max_length=255,
    )

    name: str = Field(
        "excel name field", True,
        max_length=255,
    )

    min: int = Field("excel min field", default=0)
    max: int | None = Field("excel max field", default=None)

    collapse: bool = Field("excel attribute group collapse field", export_exclude=True)
    position: int | None = Field("excel position field")


class Attribute(ExcelModel, fields_with_translations="name"):
    id: int | None = Field("excel payforsay id field")
    external_id: str = Field(
        "excel attribute id field", True,
        export_variable="excel external id field",
        to_lower_case=True,
        max_length=255,
    )
    attribute_id: str = Field(
        "excel attribute id field", True,
        to_lower_case=True,
        max_length=255,
    )
    attribute_group_id: str = Field(
        "excel attribute group field", True,
        to_lower_case=True,
    )

    name: str = Field(
        "excel name field", True,
        max_length=255,
    )

    price_impact: int | float = Field("excel price field", default=0)
    is_available: bool = Field("excel is available field", default=True)

    min: int | None = Field("excel min field")
    max: int | None = Field("excel max field")

    selected_by_default: bool = Field("excel is default field")


class Characteristic(ExcelModel, fields_with_translations="name"):
    id: int | None = Field("excel payforsay id field")
    external_id: str = Field(
        "excel name field", True,
        export_variable="excel external id field",
        to_lower_case=True,
        max_length=255,
    )
    name: str = Field(
        "excel name field", True,
        max_length=50,
    )
    filter_type: str = Field("excel filter type field", True)
    is_hide: bool = Field("excel hide field", default=False)
    position: int = Field("excel position field", default=0)


class SpotPrice(ExcelModel):
    id: int | None = Field("excel payforsay id field")
    product_external_id: str = Field(
        "excel product external id field", True,
        to_lower_case=True,
        max_length=255,
    )
    store_external_id: str = Field(
        "excel store external id field", True,
        to_lower_case=True,
        max_length=255,
    )
    price: int | float = Field(
        "excel price field", default=0, max_length=MAX_ALLOWED_PRICE
    )
    old_price: int | float = Field("excel old price field", default=0)


MODELS = {
    Attribute.__name__: Attribute,
    AttributeGroup.__name__: AttributeGroup,
    Category.__name__: Category,
    Characteristic.__name__: Characteristic,
    Product.__name__: Product,
    ProductGroup.__name__: ProductGroup,
    Store.__name__: Store,
    SpotPrice.__name__: SpotPrice,
}
