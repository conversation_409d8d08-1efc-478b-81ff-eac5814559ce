from abc import ABC, abstractmethod
from collections import defaultdict
from typing import Any, Literal, Type

from core.adapters import CellsData
from core.adapters.excel import BaseExcelCoreAdapter
from core.ext import schemas
from . import models
from .exceptions import ExcelAdapterNotFoundSheetError
from .models import MODELS, ModelType
from ..base import BaseAdapter


class BaseExcelAdapter(BaseExcelCoreAdapter, BaseAdapter, ABC):
    product_custom_fields_filter = ["pti_", "liqpay_"]

    @abstractmethod
    async def get_all_values(self, sheet_name: str) -> list[tuple[Any]]:
        raise NotImplementedError

    async def get_converted_values(self, model: Type[ModelType], is_skip_error: bool = False) -> list[ModelType]:
        sheet_name = await model.get_schema_name(self.lang)
        try:
            data = await self.get_all_values(sheet_name)
        except Exception:
            if is_skip_error:
                return []
            raise ExcelAdapterNotFoundSheetError(sheet_name)
        return await model.make_from_raw_data(data, self.lang) if data else []

    def get_translation_schemas(self, items: list) -> list[schemas.Translation]:
        result = []
        for item in items:
            for lang, translation_data in item.translations.items():
                result.append(
                    schemas.Translation(
                        id=item.id,
                        external_id=item.external_id if hasattr(item, "external_id") else item.name,
                        lang=lang,
                        name=translation_data.get("name"),
                    )
                )
        return result

    async def get_products_translation_schemas(self, products: list) -> list[schemas.TranslationProduct]:
        result = []
        for product in products:
            characteristics_names = list(
                self.__filter_custom_fields(
                    product.custom_fields, "characteristics"
                ).keys()
            )

            for lang, translation_data in product.translations.items():
                characteristics: list[schemas.CharacteristicValueTranslation] = []
                info_text = None
                for name, value in product.custom_fields_translations.get(lang, {}).items():
                    if name == "pti_info_text":
                        info_text = value
                    if name in characteristics_names:
                        characteristics.append(
                            schemas.CharacteristicValueTranslation(
                                external_id=name,
                                lang=lang,
                                value=value,
                            )
                        )

                result.append(
                    schemas.TranslationProduct(
                        id=product.id,
                        external_id=product.external_id,
                        lang=lang,
                        name=translation_data.get("name"),
                        description=translation_data.get("description"),
                        pti_info_text=info_text,
                        characteristics=characteristics,
                    )
                )

        return result

    async def get_stores_translation_schemas(self, stores: list) -> list[schemas.TranslationStore]:
        result = []
        for store in stores:
            for lang, translation_data in store.translations.items():
                result.append(
                    schemas.TranslationStore(
                        id=store.id,
                        external_id=store.external_id,
                        lang=lang,
                        name=translation_data.get("name"),
                        description=translation_data.get("description"),
                    )
                )

        return result

    async def get_and_convert_data(self) -> schemas.AdapterResult:
        stores = await self.get_converted_values(models.Store)
        products = await self.get_converted_values(models.Product)
        product_groups = await self.get_converted_values(models.ProductGroup)
        categories = await self.get_converted_values(models.Category)
        attribute_groups = await self.get_converted_values(models.AttributeGroup)
        attributes = await self.get_converted_values(models.Attribute)
        characteristics = await self.get_converted_values(models.Characteristic)
        spot_prices = await self.get_converted_values(models.SpotPrice, True)

        if spot_prices:
            product_spot_prices = defaultdict(dict)
            for sp in spot_prices:
                product_spot_prices[sp.product_external_id][sp.store_external_id] = schemas.ProductSpotPrice(
                    id=sp.id,
                    product_external_id=sp.product_external_id,
                    store_external_id=sp.store_external_id,
                    price=int(sp.price * 100),
                    old_price=int(sp.old_price * 100)
                )

            for product in products:
                product.spots_prices = product_spot_prices.get(product.external_id, {})

        self.translations = schemas.AdapterTranslations(
            stores=await self.get_stores_translation_schemas(stores),
            products=await self.get_products_translation_schemas(products),
            categories=self.get_translation_schemas(categories),
            attribute_groups=self.get_translation_schemas(attribute_groups),
            attributes=self.get_translation_schemas(attributes),
            characteristics=self.get_translation_schemas(characteristics),
        )

        return schemas.AdapterResult(
            stores=[
                schemas.Store.parse_obj(
                    dict(
                        **store.to_dict(add_custom_fields=False),
                        excel_row_number=store.row_number,
                        custom_fields=[
                            schemas.StoreCustomField(
                                name=name,
                                value=value,
                            )
                            for name, value in store.custom_fields.items()
                        ],
                        is_distance=bool(store.coordinates and store.distance),
                        latitude=store.coordinates.split(",")[0].strip() if store.coordinates else None,
                        longitude=store.coordinates.split(",")[1].strip() if store.coordinates else None,
                    )
                )
                for store in stores
            ],
            products=[
                schemas.Product.parse_obj(
                    {
                        **product.to_dict(exclude={"position", "spots_prices"}, add_custom_fields=False),
                        "position": product.row_number,
                        "excel_position": product.position,
                        "price": product.price * 100,
                        "old_price": product.old_price * 100,
                        "spots_prices": product.spots_prices if product.spots_prices else None,
                        "characteristics": [
                            schemas.CharacteristicValue(
                                external_id=name.lower(),
                                value=str(value),
                            )
                            for name, value in self.__filter_custom_fields(
                                product.custom_fields, "characteristics"
                            ).items()
                            if value is not None
                        ],
                        "product_type_info_custom_fields": [
                            schemas.ProductCustomField(
                                name=name.lower(),
                                value=str(value),
                                type="product_type_info",
                            )
                            for name, value in self.__filter_custom_fields(
                                product.custom_fields, "product_type_info"
                            ).items()
                            if value is not None
                        ],
                        "product_type_liqpay_custom_fields": [
                            schemas.ProductCustomField(
                                name=name.lower(),
                                value=str(value),
                                type="product_type_liqpay",
                            )
                            for name, value in self.__filter_custom_fields(
                                product.custom_fields, "product_type_liqpay"
                            ).items()
                            if value is not None
                        ],
                    }
                )
                for product in products
            ],
            product_groups=[
                schemas.ProductGroup.parse_obj(
                    dict(
                        **product_group.to_dict(),
                    )
                )
                for product_group in product_groups
            ],
            categories=[schemas.Category.parse_obj(
                dict(
                    **category.to_dict(exclude={"position"}),
                    position=category.row_number,
                    excel_position=category.position,
                )
            ) for category in categories],
            attribute_groups=[
                schemas.AttributeGroup.parse_obj(
                    dict(
                        **attribute_group.to_dict(exclude={"position"}),
                        position=attribute_group.row_number,
                        excel_position=attribute_group.position,
                        attributes=[
                            schemas.Attribute.parse_obj(
                                {
                                    **attribute.to_dict(),
                                    "price_impact": attribute.price_impact * 100,
                                }
                            )
                            for attribute in attributes
                            if attribute.attribute_group_id == attribute_group.external_id
                        ],
                    )
                )
                for attribute_group in attribute_groups
            ],
            characteristics=[
                schemas.Characteristic.parse_obj(
                    dict(
                        **characteristic.to_dict(),
                        excel_row_number=characteristic.row_number,
                    )
                )
                for characteristic in characteristics
            ],
            spot_prices=[
                schemas.ProductSpotPrice(
                    id=spot_price.id,
                    product_external_id=spot_price.product_external_id,
                    store_external_id=spot_price.store_external_id,
                    price=int(spot_price.price * 100),  # конвертуємо в центи
                    old_price=int(spot_price.old_price * 100) if spot_price.old_price else None
                )
                for spot_price in spot_prices
            ],
            translations=self.translations,
        )

    async def get_cells_to_export(
            self, data: dict, langs_list: list[str],
            translations: dict | None = None,
    ):
        if self.lang in langs_list:
            langs_list.remove(self.lang)

        cells_to_export: dict[str, CellsData] = {}

        for name, sheet_data in data.items():
            model: Type[ModelType] = MODELS.get(name)
            if not model:
                continue

            cells: CellsData = CellsData()
            title = await model.get_schema_name(self.lang)
            cells_to_export[title] = cells

            page_translations = translations.get(name) if translations is not None else None
            await model.make_headers(cells, self.lang, langs_list)

            for row_id, row_data in enumerate(sheet_data):
                attribute_group_id = None
                if name == "Attribute":
                    attribute_group_id, row_data = row_data

                row_data = row_data.dict()
                if attribute_group_id:
                    row_data["attribute_group_id"] = attribute_group_id

                custom_fields = None
                if "custom_fields" in row_data:
                    custom_fields = row_data.pop("custom_fields", None)
                    custom_fields = {
                        custom_field["name"]: custom_field["value"]
                        for custom_field in custom_fields
                    }
                elif "characteristics" in row_data:
                    custom_fields = row_data.pop("characteristics", None)
                    custom_fields = {
                        custom_field["external_id"]: custom_field["value"]
                        for custom_field in custom_fields
                    }

                if "product_type_info_custom_fields" in row_data:
                    info_custom_fields = row_data.pop("product_type_info_custom_fields", None)
                    info_custom_fields = {
                        custom_field_info["name"]: custom_field_info["value"]
                        for custom_field_info in info_custom_fields
                    }
                    custom_fields.update(info_custom_fields)

                if "product_type_liqpay_custom_fields" in row_data:
                    liqpay_custom_fields = row_data.pop("product_type_liqpay_custom_fields", None)
                    liqpay_custom_fields = {
                        liqpay_field_info["name"]: liqpay_field_info["value"]
                        for liqpay_field_info in liqpay_custom_fields
                    }
                    custom_fields.update(liqpay_custom_fields)

                row = model(
                    row_id,
                    values=row_data,
                    custom_fields=custom_fields,
                )

                row_translations = page_translations.get(row.id) if page_translations else None

                await row.make_row_data(cells, langs_list, self.lang)

                row.make_translations(cells, langs_list, row_translations)
        return cells_to_export

    def __filter_custom_fields(
            self, custom_fields: dict,
            custom_field_type: Literal["characteristics", "product_type_info", "product_type_liqpay"]
    ) -> dict:
        if custom_field_type == "characteristics":
            return {
                key: value
                for key, value in custom_fields.items()
                if not any(key.startswith(prefix) for prefix in self.product_custom_fields_filter)
            }
        elif custom_field_type == "product_type_info":
            return {
                key: value
                for key, value in custom_fields.items()
                if key.startswith("pti_")
            }
        elif custom_field_type == "product_type_liqpay":
            return {
                key: value
                for key, value in custom_fields.items()
                if key.startswith("liqpay_")
            }
        else:
            return custom_fields
