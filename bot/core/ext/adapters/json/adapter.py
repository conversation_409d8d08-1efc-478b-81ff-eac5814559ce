import json
import logging
from typing import Any

from config import OPENAI_API_KEY
from core.ext.data_manager import ai_menu_detector
from db import crud
from db.models import Brand, DataPorter, StoreCustomField
from exceptions.task import OpenAIConfigError
from loggers import JSONLogger
from .exceptions import JsonAdapterNotFoundStores, MenuAdapterError
from ..base import BaseAdapter
from ... import schemas


# import aiofiles


class JsonAdapter(BaseAdapter):

    def __init__(self, json_data: schemas.AdapterJSONData | None = None, **kwargs):
        super().__init__(**kwargs)

        self.json_data = json_data
        self.stores_external_ids: list[str] | None = None

    async def get_converted_values_stores(
            self, data: list[schemas.StoreAPI] | None = None
    ) -> list[schemas.Store]:

        if not data:  # if import json data without stories (from pdf menu...)
            db_stores = await crud.get_stores(brand_id=self.brand_id)
            if not db_stores:
                raise JsonAdapterNotFoundStores()

            data = []
            if len(db_stores) == 1:
                if not db_stores[0].external_id:
                    raise JsonAdapterNotFoundStores()
                self.stores_external_ids = [db_stores[0].external_id]
                store_custom_fields = await StoreCustomField.get_list(
                    store_id=db_stores[0].id
                )
                return [schemas.Store(
                    custom_fields=store_custom_fields,
                    currency=db_stores[0].currency,
                    **db_stores[0].as_dict()
                )
                ]
            else:
                if not self.json_data.to_stores:
                    if not data:
                        raise JsonAdapterNotFoundStores()
                else:
                    stores: list[schemas.Store] = []
                    self.stores_external_ids = []
                    for store in db_stores:
                        if store.id in self.json_data.to_stores:
                            if not store.external_id:
                                raise JsonAdapterNotFoundStores()
                            self.stores_external_ids.append(store.external_id)
                            store_custom_fields = await StoreCustomField.get_list(
                                store_id=store.id
                            )
                            stores.append(
                                schemas.Store(
                                    custom_fields=store_custom_fields,
                                    currency=db_stores[0].currency,
                                    **store.as_dict()
                                )
                            )
                    return stores

        if not data:
            raise JsonAdapterNotFoundStores()

        stores: list[schemas.Store] = []
        for store in data:
            store_schema = schemas.Store(
                id=store.id,
                external_id=store.external_id,
                name=store.name.value if store.name.value else self.brand.name,
                description=store.description.value if store.description else None,
                ai_description=store.ai_description.value if store.ai_description
                else None,
                image_url=store.image_url,
                latitude=store.latitude,
                longitude=store.longitude,
                distance=store.distance,
                polygon=store.polygon,
                currency=store.currency,
                city=store.city,
                position=store.position,
                custom_fields=store.custom_fields,
            )

            store_schema.is_polygon = bool(store_schema.polygon)
            store_schema.is_distance = all(
                [store_schema.latitude, store_schema.longitude, store_schema.distance]
            )
            stores.append(store_schema)

        return stores

    def get_converted_values_categories(self, data: list[schemas.CategoryAPI]) -> list[
        schemas.Category]:
        categories: list[schemas.Category] = []
        sorted_categories = sorted(data, key=lambda x: (x.position is None, x.position))
        for index, category in enumerate(sorted_categories):
            categories.append(
                schemas.Category(
                    id=category.id,
                    external_id=category.external_id,
                    name=category.name.value,
                    is_default=category.is_default,
                    father_category_id=category.father_category_id,
                    image_url=category.image_url,
                    position=index,
                    stores_external_ids=category.stores_external_ids if
                    category.stores_external_ids else self.stores_external_ids,
                    filters=category.filters,
                )
            )
        return categories

    async def get_converted_values_products(self, data: list[schemas.ProductAPI]) -> \
            list[schemas.Product]:
        products: list[schemas.Product] = []

        sorted_products = sorted(data, key=lambda x: (x.position is None, x.position))
        for index, product in enumerate(sorted_products):
            characteristics_data = product.characteristics
            if characteristics_data:
                characteristics = []
                for characteristic in characteristics_data:
                    characteristics.append(
                        schemas.CharacteristicValue(
                            external_id=characteristic.external_id,
                            value=characteristic.value.value,
                        )
                    )
            else:
                characteristics = None

            products.append(
                schemas.Product(
                    id=product.id,
                    external_id=product.external_id,
                    product_id=product.product_id,
                    product_group_id=product.product_group_id,
                    name=product.name.value,
                    description=product.description.value if product.description else
                    None,
                    image_url=product.image_url,
                    gallery_items=product.gallery_items,
                    is_available=product.is_available,
                    price=round(product.price * 100),
                    old_price=round(
                        product.old_price * 100
                    ) if product.old_price else 0,
                    spots_prices=product.spots_prices,
                    buy_min_quantity=product.buy_min_quantity,
                    is_weight=product.is_weight,
                    weight_unit=product.weight_unit,
                    position=index,
                    categories_external_ids=product.categories_external_ids,
                    attribute_groups_external_ids=product.attribute_groups_external_ids,
                    stores_external_ids=product.stores_external_ids if
                    product.stores_external_ids else self.stores_external_ids,
                    floating_sum_value=product.floating_sum_value,
                    characteristics=characteristics,
                    type=product.type,
                    pti_info_text=product.pti_info_text.value if
                    product.pti_info_text else None,
                    product_type_info_custom_fields=product
                    .product_type_info_custom_fields,
                    product_type_liqpay_custom_fields=product
                    .product_type_liqpay_custom_fields,
                    pti_info_link=product.pti_info_link,
                    liqpay_id=product.liqpay_id,
                    liqpay_unit_name=product.liqpay_unit_name,
                    liqpay_codifier=product.liqpay_codifier,
                    need_auth=product.need_auth,
                    floating_qty_enabled=product.floating_qty_enabled,
                    is_skip_download_images=product.is_skip_download_images,
                )
            )

        return products

    def get_converted_values_attributes(self, data: list[schemas.AttributeAPI]) -> dict[
        str, list[schemas.Attribute]]:
        attributes: dict[str, list[schemas.Attribute]] = {}
        for attribute in data:
            attribute_group_id = attribute.attribute_group_id
            if attribute_group_id not in attributes:
                attributes[attribute_group_id] = []

            attributes[attribute_group_id].append(
                schemas.Attribute(
                    id=attribute.id,
                    external_id=attribute.external_id,
                    attribute_id=attribute.attribute_id,
                    name=attribute.name.value,
                    price_impact=round(
                        attribute.price_impact * 100
                    ) if attribute.price_impact else 0,
                    is_available=attribute.is_available,
                    min=attribute.min,
                    max=attribute.max,
                    selected_by_default=attribute.selected_by_default,
                )
            )

        return attributes

    def get_converted_values_attribute_groups(
            self, data: list[schemas.AttributeGroupAPI],
            attributes: dict[str, list[schemas.Attribute]],
    ) -> list[schemas.AttributeGroup]:
        attribute_groups: list[schemas.AttributeGroup] = []
        for attribute_group in (data or []):
            attribute_group_id = attribute_group.attribute_group_id

            attribute_groups.append(
                schemas.AttributeGroup(
                    id=attribute_group.id,
                    external_id=attribute_group.external_id,
                    attribute_group_id=attribute_group_id,
                    name=attribute_group.name.value,
                    min=attribute_group.min,
                    max=attribute_group.max,
                    position=attribute_group.position,
                    attributes=attributes.get(attribute_group_id, []),
                )
            )

        return attribute_groups

    def get_converted_values_characteristics(
            self,
            data: list[schemas.CharacteristicAPI],
    ) -> list[schemas.Characteristic]:
        characteristics: list[schemas.Characteristic] = []
        for characteristic in data:
            characteristics.append(
                schemas.Characteristic(
                    id=characteristic.id,
                    external_id=characteristic.external_id,
                    name=characteristic.name.value,
                    filter_type=characteristic.filter_type,
                    is_hide=characteristic.is_hide,
                    position=characteristic.position,
                )
            )

        return characteristics

    def get_translation_schemas(self, items: list) -> list[schemas.Translation]:
        result = []
        for item in items:
            if not item.name.translations:
                continue

            for translation in (item.name.translations or []):
                result.append(
                    schemas.Translation(
                        id=item.id,
                        external_id=item.external_id if hasattr(
                            item, "external_id"
                        ) else item.name,
                        lang=translation.lang,
                        name=translation.value,
                    )
                )
        return result

    def get_products_translation_schemas(self, products: list) -> list[
        schemas.TranslationProduct]:
        result = []
        for product in products:
            if product.description and product.description.translations:
                descriptions: dict[str, str] = {
                    translation.lang: translation.value
                    for translation in product.description.translations
                }
            else:
                descriptions = None

            if product.pti_info_text and product.pti_info_text.translations:
                pti_info_texts: dict[str, str] = {
                    translation.lang: translation.value
                    for translation in product.pti_info_text.translations
                }
            else:
                pti_info_texts = None

            characteristics: dict[str, schemas.CharacteristicValueTranslation] = {}
            product_characteristics = product.characteristics or []
            for characteristic_value in product_characteristics:
                for translation in (characteristic_value.value.translations or []):
                    if translation.lang not in characteristics:
                        characteristics[translation.lang] = []

                    characteristics[translation.lang].append(
                        schemas.CharacteristicValueTranslation(
                            external_id=characteristic_value.external_id,
                            value=translation.value,
                            lang=translation.lang,
                        )
                    )

            for translation in (product.name.translations or []):
                result.append(
                    schemas.TranslationProduct(
                        id=product.id,
                        external_id=product.external_id,
                        lang=translation.lang,
                        name=translation.value,
                        description=descriptions.get(
                            translation.lang
                        ) if descriptions else None,
                        pti_info_text=pti_info_texts.get(
                            translation.lang
                        ) if pti_info_texts else None,
                        characteristics=characteristics.get(
                            translation.lang, []
                        ) if characteristics else None,
                    )
                )

        return result

    def get_stores_translation_schemas(self, stores: list) -> list[
        schemas.TranslationStore]:
        result = []
        for store in (stores or []):
            if store.description and store.description.translations:
                descriptions: dict[str, str] = {
                    translation.lang: translation.value
                    for translation in store.description.translations
                }
            else:
                descriptions = None

            if store.ai_description and store.ai_description.translations:
                ai_descriptions: dict[str, str] = {
                    translation.lang: translation.value
                    for translation in store.ai_description.translations
                }
            else:
                ai_descriptions = None

            for translation in (store.name.translations or []):
                result.append(
                    schemas.TranslationStore(
                        id=store.id,
                        external_id=store.external_id,
                        lang=translation.lang,
                        name=translation.value,
                        description=descriptions.get(translation.lang),
                        ai_description=ai_descriptions.get(translation.lang),
                    )
                )

        return result

    async def get_and_convert_data(self) -> schemas.AdapterResult:
        stores = await self.get_converted_values_stores(self.json_data.stores)
        categories = self.get_converted_values_categories(self.json_data.categories)
        products = await self.get_converted_values_products(self.json_data.products)
        product_groups = self.json_data.product_groups
        attributes = self.get_converted_values_attributes(self.json_data.attributes)
        attribute_groups = self.get_converted_values_attribute_groups(
            self.json_data.attribute_groups,
            attributes
        )
        characteristics = self.get_converted_values_characteristics(
            self.json_data.characteristics
        )

        translations = schemas.AdapterTranslations(
            stores=self.get_stores_translation_schemas(self.json_data.stores),
            products=self.get_products_translation_schemas(self.json_data.products),
            categories=self.get_translation_schemas(self.json_data.categories),
            attribute_groups=self.get_translation_schemas(
                self.json_data.attribute_groups
            ),
            attributes=self.get_translation_schemas(self.json_data.attributes),
            characteristics=self.get_translation_schemas(
                self.json_data.characteristics
            ),
        )

        return schemas.AdapterResult(
            stores=stores,
            categories=categories,
            products=products,
            product_groups=product_groups,
            attribute_groups=attribute_groups,
            characteristics=characteristics,
            translations=translations,
        )

    def converted_translationns_to_api(
            self, field_name: str,
            translations: list | None = None,
    ) -> list[schemas.TranslationAPI] | None:
        return [
            schemas.TranslationAPI(
                lang=translation.lang,
                value=getattr(translation, field_name, None),
            ) for translation in translations
        ] if translations else None

    def converted_stores_to_api(
            self, data: list[schemas.Store],
            translations: dict | None = None,
    ) -> list[schemas.StoreAPI]:
        stores: list[schemas.StoreAPI] = []
        for store in data:
            stores.append(
                schemas.StoreAPI(
                    id=store.id,
                    external_id=store.external_id,
                    name=schemas.ValueAndTranslationAPI(
                        value=store.name,
                        translations=self.converted_translationns_to_api(
                            "name", translations.get(store.id)
                        ) if translations else None
                    ),
                    description=schemas.ValueAndTranslationAPI(
                        value=store.description,
                        translations=self.converted_translationns_to_api(
                            "description", translations.get(store.id)
                        ) if translations else None
                    ),
                    ai_description=schemas.ValueAndTranslationAPI(
                        value=store.ai_description,
                        translations=self.converted_translationns_to_api(
                            "ai_description", translations.get(store.id)
                        ) if translations else None
                    ),
                    image_url=store.image_url,
                    latitude=store.latitude,
                    longitude=store.longitude,
                    distance=store.distance,
                    polygon=store.polygon,
                    currency=store.currency,
                    city=store.city,
                    position=store.position,
                    custom_fields=store.custom_fields,
                )
            )

        return stores

    def converted_categories_to_api(
            self, data: list[schemas.Category],
            translations: dict | None = None,
    ) -> list[schemas.CategoryAPI]:
        categories: list[schemas.CategoryAPI] = []
        for category in data:
            categories.append(
                schemas.CategoryAPI(
                    id=category.id,
                    external_id=category.external_id,
                    name=schemas.ValueAndTranslationAPI(
                        value=category.name,
                        translations=self.converted_translationns_to_api(
                            "name", translations.get(category.id)
                        ) if translations else None
                    ),
                    is_default=category.is_default,
                    father_category_id=category.father_category_id,
                    image_url=category.image_url,
                    position=category.position,
                    stores_external_ids=category.stores_external_ids,
                    filters=category.filters,
                )
            )
        return categories

    def converted_products_to_api(
            self, data: list[schemas.Product],
            translations: dict | None = None,
    ) -> list[schemas.ProductAPI]:
        products: list[schemas.ProductAPI] = []
        for product in data:
            product_translations = translations.get(
                product.id
            ) if translations else None

            characteristics_data = product.characteristics
            if characteristics_data:
                characteristics = []
                for characteristic in characteristics_data:
                    characteristics.append(
                        schemas.CharacteristicValueAPI(
                            external_id=characteristic.external_id,
                            value=schemas.ValueAndTranslationAPI(
                                value=characteristic.value,
                                translations=self.converted_translationns_to_api(
                                    "value", product_translations.get("characteristics")
                                ) if product_translations else None,
                            ),
                        )
                    )
            else:
                characteristics = None

            products.append(
                schemas.ProductAPI(
                    id=product.id,
                    external_id=product.external_id,
                    product_id=product.product_id,
                    product_group_id=product.product_group_id,
                    name=schemas.ValueAndTranslationAPI(
                        value=product.name,
                        translations=self.converted_translationns_to_api(
                            "name", product_translations
                        ) if product_translations else None,
                    ),
                    description=schemas.ValueAndTranslationAPI(
                        value=product.description,
                        translations=self.converted_translationns_to_api(
                            "description", product_translations
                        ) if product_translations else None,
                    ),
                    image_url=product.image_url,
                    gallery_items=product.gallery_items,
                    is_available=product.is_available,
                    price=round(product.price // 100, 2),
                    old_price=round(
                        product.old_price // 100, 2
                    ) if product.old_price else 0,
                    spots_prices=product.spots_prices,
                    buy_min_quantity=product.buy_min_quantity,
                    is_weight=product.is_weight,
                    weight_unit=product.weight_unit,
                    position=product.position,
                    categories_external_ids=product.categories_external_ids,
                    attribute_groups_external_ids=product.attribute_groups_external_ids,
                    stores_external_ids=product.stores_external_ids,
                    floating_sum_value=product.floating_sum_value,
                    characteristics=characteristics,
                    type=product.type,
                    floating_qty_enabled=product.floating_qty_enabled,
                )
            )
        return products

    def converted_attribute_groups_to_api(
            self, data: list[schemas.AttributeGroup],
            attributes_translations: dict | None = None,
            attribute_groups_translations: dict | None = None,
    ) -> tuple[list[schemas.AttributeGroupAPI], list[schemas.AttributeAPI]]:
        attribute_groups: list[schemas.AttributeGroupAPI] = []
        attributes: list[schemas.AttributeAPI] = []
        for attribute_group in data:
            attribute_groups.append(
                schemas.AttributeGroupAPI(
                    id=attribute_group.id,
                    external_id=attribute_group.external_id,
                    attribute_group_id=attribute_group.attribute_group_id,
                    name=schemas.ValueAndTranslationAPI(
                        value=attribute_group.name,
                        translations=self.converted_translationns_to_api(
                            "name",
                            attribute_groups_translations.get(attribute_group.id)
                        ) if attribute_groups_translations else None
                    ),
                    min=attribute_group.min,
                    max=attribute_group.max,
                    position=attribute_group.position,
                )
            )

            attributes.extend(
                self.converted_attributes_to_api(
                    attribute_group.attribute_group_id, attribute_group.attributes,
                    attributes_translations,
                )
            )

        return attribute_groups, attributes

    def converted_attributes_to_api(
            self, attribute_group_id: str,
            data: list[schemas.Attribute],
            translations: dict | None = None,
    ) -> list[schemas.AttributeAPI]:
        attributes: list[schemas.AttributeAPI] = []
        for attribute in data:
            attributes.append(
                schemas.AttributeAPI(
                    id=attribute.id,
                    external_id=attribute.external_id,
                    attribute_id=attribute.attribute_id,
                    attribute_group_id=attribute_group_id,
                    name=schemas.ValueAndTranslationAPI(
                        value=attribute.name,
                        translations=self.converted_translationns_to_api(
                            "name", translations.get(attribute.id)
                        ) if translations else None
                    ),
                    price_impact=round(
                        attribute.price_impact // 100, 2
                    ) if attribute.price_impact else 0,
                    is_available=attribute.is_available,
                    min=attribute.min,
                    max=attribute.max,
                    selected_by_default=attribute.selected_by_default,
                )
            )

        return attributes

    def converted_characteristics_to_api(
            self,
            data: list[schemas.Characteristic],
            translations: dict | None = None,
    ) -> list[schemas.CharacteristicAPI]:
        characteristics: list[schemas.CharacteristicAPI] = []
        for characteristic in data:
            characteristics.append(
                schemas.CharacteristicAPI(
                    id=characteristic.id,
                    external_id=characteristic.external_id,
                    name=schemas.ValueAndTranslationAPI(
                        value=characteristic.name,
                        translations=self.converted_translationns_to_api(
                            "name", translations.get(characteristic.id)
                        ) if translations else None
                    ),
                    filter_type=characteristic.filter_type.value,
                    is_hide=characteristic.is_hide,
                    position=characteristic.position,
                )
            )

        return characteristics

    async def save_data(
            self, data: dict,
            langs_list: list[str], brand_lang: str,
            translations: dict | None = None
    ) -> Any:
        attribute_groups, attributes = self.converted_attribute_groups_to_api(
            data.get("AttributeGroup"),
            translations.get("Attribute"),
            translations.get("AttributeGroup"),
        )

        result = schemas.AdapterJSONData(
            stores=self.converted_stores_to_api(
                data.get("Store"), translations.get("Store")
            ),
            products=self.converted_products_to_api(
                data.get("Product"), translations.get("Product")
            ),
            product_groups=data.get("ProductGroup"),
            categories=self.converted_categories_to_api(
                data.get("Category"), translations.get("Category")
            ),
            attribute_groups=attribute_groups,
            attributes=attributes,
            characteristics=self.converted_characteristics_to_api(
                data.get("Characteristic"),
                translations.get("Characteristic")
            ),
        )

        return json.dumps(result.dict())


class MenuAdapter(JsonAdapter):

    def __init__(self, openai_key: str, input_files: list[str], **kwargs):
        self.openai_key: str = openai_key
        self.input_files: list[str] = input_files
        super().__init__(**kwargs)

    @classmethod
    async def get_data_from_database(
            cls, brand: Brand, data_porter: DataPorter | None, lang: str,
            user_lang: str, **kwargs
    ):
        group_config = await crud.get_group_config(brand.group_id)

        try:
            openai_key = group_config.openai_config.data[0]
            if openai_key == "1169":
                openai_key = OPENAI_API_KEY
        except (AttributeError, IndexError):
            raise OpenAIConfigError(brand.group_id)

        return {"openai_key": openai_key}

    async def get_and_convert_data(self) -> schemas.AdapterResult:
        debug_data = {
            "brand": {
                "id": self.brand.id,
                "name": self.brand.name,
            },
            "data_porter": {
                "id": self.data_porter.id,
                "uuid": self.data_porter.uuid_id,
            } if self.data_porter else None,
        }

        logger = JSONLogger(
            f"import.menu", debug_data, {
                "input_files": self.input_files,
            }
        )

        logger.debug("Calling ai_menu_detector.detect_menu")

        try:
            self.json_data = await ai_menu_detector.detect_menu(
                self.openai_key, self.input_files, debug_data
            )
        except Exception as e:
            logging.error(str(e), exc_info=True)
            raise MenuAdapterError(str(e))

        logger.debug(
            "Received ai_menu_detector.detect_menu result",
            {
                "result": self.json_data.dict()
            }
        )
        return await super().get_and_convert_data()
