import datetime
import json
import logging
import time
from typing import Literal, Type

from aiohttp import ClientResponse, ClientSession
from pydantic import BaseModel

from core.ext.adapters.poster.schemas import (
    PosterApiCategories, PosterApiCategory, PosterApiErrors, PosterApiOrderCreate, PosterApiOrderSources,
    PosterApiProductSpot, PosterApiProductType, PosterApiProducts, PosterApiStores, PosterApiTransactions, PosterHall,
    PosterInstance, PosterInstanceCategoryWithProducts, PosterResponseHalls, PosterTable, PosterTablesResponse,
    PosterTransaction,
)
from utils.type_vars import T


class PosterApiClient:
    def __init__(self, brand_id: int, token: str):
        self.base_url = "https://joinposter.com/api"
        self.brand_id = brand_id
        self._token = token

        self.logger = logging.getLogger("debugger.poster")

    @property
    def token(self):
        if self._token:
            return self._token
        raise Exception("Poster: api_token for poster not found")

    def _find_high_level_category(self, category_list, category_id):
        for category in category_list:
            if category.category_id == category_id:
                if category.level == 1:
                    return category_id
                else:
                    return self._find_high_level_category(
                        category_list, category.parent_category
                    )
        return None

    async def make_request(
            self,
            method: str,
            path: str,
            headers: dict | BaseModel = None,
            query: dict | BaseModel = None,
            body: dict | BaseModel = None,
            return_model: Type[T] = dict,
    ) -> T:
        resp: ClientResponse | None = None
        if not path.startswith("/"):
            path = f"/{path}"

        url = f"{self.base_url}{path}"
        self.logger.debug(f"Poster: {url=}")

        if isinstance(headers, BaseModel):
            headers = headers.dict(exclude_unset=True)
        elif not isinstance(headers, dict):
            headers = {}

        if isinstance(query, BaseModel):
            query = query.dict(exclude_unset=True, exclude_none=True)

        if not query:
            query = dict()
        query.update({"token": self.token})

        kwargs = {
            "url": url,
            "headers": headers,
        }

        if query:
            kwargs["params"] = query
            self.logger.debug(f'Poster: {query=}')

        if body:
            if isinstance(body, BaseModel):
                kwargs["json"] = json.loads(body.json())
            else:
                kwargs["json"] = body
            self.logger.debug(f'Poster: {body=}')

        async with ClientSession() as session:
            method = getattr(session, method.lower())

            try:
                resp: ClientResponse = await method(**kwargs)
                self.logger.debug(f"Poster: {await resp.text()=}")
            except Exception as e:
                self.logger.error(e, exc_info=True)

            try:
                result = await resp.json()
            except Exception as err:
                self.logger.error(err, exc_info=True)
                raise err

            if resp.ok:
                if "error" in result:
                    poster_err = PosterApiErrors(**result)
                    raise Exception(poster_err.message)
                if return_model:
                    try:
                        return return_model(**result)
                    except Exception as err:
                        self.logger.error(
                            f"\nPoster: response model FAILED: {err}",
                            exc_info=True,
                        )
                else:
                    return result
            else:
                self.logger.error(f"Poster: response not ok: {resp.status=}")
                return {}

    async def get_poster_instances(self, skip_stores: str | None = None):
        start_time = time.time()
        instances = []
        stores_ = await self.get_stores()
        if not stores_:
            raise Exception("Poster: not any store was returned from poster")

        stores = []
        if skip_stores:
            skip_stores_list = skip_stores.split(",")
            for store in stores_.response:
                if store.spot_id not in skip_stores_list:
                    stores.append(store)
        else:
            stores = stores_.response

        categories = await self.get_categories(False)

        order_sources = await self.get_order_sources()

        products_by_cat = {}
        detailed_categories = []
        for category in categories.response:
            resp = await self.get_category(category.category_id)
            detail_category = resp.response
            detailed_categories.append(detail_category)
            products_by_cat[category.category_id] = []

        instance_categories = []

        for type_product in PosterApiProductType.values():
            for category in categories.response:
                products = await self.get_products(category.category_id, type_product)
                if not products.response:
                    continue

                for product in products.response:
                    if product not in products_by_cat[category.category_id]:
                        products_by_cat[category.category_id].append(product)

        for category in detailed_categories:
            instance_category = PosterInstanceCategoryWithProducts(
                category=category,
                products=products_by_cat[category.category_id],
            )
            instance_categories.append(instance_category)

        for store in stores:
            categories = []
            for category in instance_categories:
                products_ = []
                category_ = category.copy()
                for product in category_.products:
                    product_ = product.copy()
                    if product.spots and len(product.spots) > 0:
                        product_.spots = [
                            spot
                            for spot in product.spots
                            if isinstance(spot, PosterApiProductSpot)
                               and spot.spot_id == store.spot_id
                               and spot.price is not None
                        ]
                    products_.append(product_)

                for v in category_.category.visible:
                    if v.spot_id == store.spot_id:
                        if v.visible == 1:
                            category_.products = products_
                            categories.append(category_)

            instances.append(
                PosterInstance(
                    store=store,
                    categories=categories,
                    order_sources=order_sources.response,
                )
            )
        self.logger.debug(
            f"get_poster_instances time: {(time.time() - start_time):.3f}"
        )
        return instances

    async def get_order_sources(self) -> PosterApiOrderSources:
        url = "settings.getOrderSources"
        query = dict()
        return await self.make_request(
            "get", url, query=query, return_model=PosterApiOrderSources
        )

    async def get_stores(self) -> PosterApiStores:
        url = "/access.getSpots"
        query = dict()
        return await self.make_request(
            "get", url, query=query, return_model=PosterApiStores
        )

    async def get_categories(self, fiscal: bool = None) -> PosterApiCategories:
        # Фискальный признак категорий: 0 — не фискальные,
        # 1 — фискальные. По умолчанию — все категории.
        url = "menu.getCategories"
        query = dict()
        if fiscal:
            query["fiscal"] = int(fiscal)
        return await self.make_request(
            "get", url, query=query, return_model=PosterApiCategories
        )

    async def get_category(
            self, category_id: str, onec_id: bool = None
    ) -> PosterApiCategory:
        url = "menu.getCategory"
        query = dict(
            category_id=category_id,
        )
        if onec_id:
            query["onec_id"] = onec_id
        return await self.make_request(
            "get", url, query=query, return_model=PosterApiCategory
        )

    async def get_products(
            self,
            category_id: str = None,
            product_type: Literal["products", "batchtickets"] = None,
    ) -> PosterApiProducts:
        url = "menu.getProducts"
        query = dict()
        if category_id:
            query["category_id"] = category_id
        if product_type:
            query["type"] = product_type
        resp = await self.make_request(
            "get", url, query=query, return_model=PosterApiProducts
        )
        return resp

    async def make_order(self, order_data: PosterApiOrderCreate) -> dict:
        url = "incomingOrders.createIncomingOrder"
        query = dict()
        resp = await self.make_request(
            "post", url, query=query, body=order_data.dict(exclude_unset=True)
        )
        return resp

    async def get_product(self, product_id: str):
        url = "menu.getProduct"
        query = dict()
        if product_id:
            query["product_id"] = product_id
        resp = await self.make_request("get", url, query=query)
        return resp

    async def get_settings(
            self,
    ):
        url = "settings.getAllSettings"
        resp = await self.make_request("get", url)
        return resp

    async def get_order_status_pos(self, incoming_order_id):
        url = "incomingOrders.getIncomingOrder"
        query = dict(incoming_order_id=incoming_order_id)
        resp = await self.make_request("get", url, query=query)
        if "error" in resp:
            self.logger.error("{}".format(resp.get("message")))
        return resp.get("response", {})

    async def get_transaction(self, transaction_id):
        url = "finance.getTransaction"
        query = dict(transaction_id=transaction_id)
        resp = await self.make_request("get", url, query=query)
        if "error" in resp:
            self.logger.error("{}".format(resp.get("message")))
        return resp.get("response", [])

    async def get_poster_transactions(
            self, order_date: datetime
    ) -> PosterApiTransactions | None:
        url = "transactions.getTransactions"
        date_from = f"{order_date:%Y-%m-%d}"
        date_to = f"{datetime.datetime.now():%Y-%m-%d}"
        query = dict(date_from=date_from, date_to=date_to)
        return await self.make_request(
            "get", url, query=query, return_model=PosterApiTransactions
        )

    async def get_table_checks(
            self, table_id: int, order_date: datetime
    ) -> list[PosterTransaction | None]:
        checks = []
        transactions = await self.get_poster_transactions(order_date)
        for check in transactions.response.data:
            if check.table_id == table_id:
                checks.append(check)
        return checks

    async def get_check_by_transaction(
            self, transaction_id: int, order_date: datetime
    ) -> PosterApiTransactions | None:
        result = None
        transactions = await self.get_poster_transactions(order_date)
        for check in transactions.response.data:
            if check.transaction_id == transaction_id:
                result = check
                break
        return result

    async def get_check(self, transaction_id: int) -> dict:
        result = {}
        url = "dash.getTransaction"
        query = dict(
            transaction_id=transaction_id,
            include_history="false",
            include_products="false",
            include_delivery="false",
        )
        resp = await self.make_request("get", url, query=query)
        if "error" in resp:
            self.logger.error("{}".format(resp.get("message")))
            return {}
        for check in resp.get("response", []):
            if check.get("transaction_id") == str(transaction_id):
                result = check
                break
        return result

    async def close_check(
            self, spot_id: int, spot_tablet_id: int, transaction_id: int, amount: int
    ) -> None:
        url = "transactions.closeTransaction"
        data = dict(
            spot_id=spot_id,
            spot_tablet_id=spot_tablet_id,
            transaction_id=transaction_id,
            payed_card=amount,
        )
        result = await self.make_request("post", url, body=data)
        return result

    async def get_halls(
            self,
    ) -> list[PosterHall | None]:
        url = "spots.getSpotTablesHalls"
        result: PosterResponseHalls = await self.make_request(
            "get", url, return_model=PosterResponseHalls
        )
        return result.response

    async def get_hall_tables(
            self, spot_id: int, hall_id: int
    ) -> list[PosterTable | None] | None:
        url = "spots.getTableHallTables"
        query = dict(spot_id=spot_id, hall_id=hall_id, without_deleted=1)
        result: PosterTablesResponse = await self.make_request(
            "get", url, query=query, return_model=PosterTablesResponse
        )
        return result.response

    async def get_tables(self, spot_id: int) -> list[PosterTable | None] | None:
        tables = []
        halls = await self.get_halls()
        for hall in halls:
            tables.extend(await self.get_hall_tables(spot_id, hall.hall_id))
        return tables
