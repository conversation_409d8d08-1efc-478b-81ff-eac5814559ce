import logging
import json
from typing import Type
from async_property import async_property

from aiohttp import ClientSession, ClientResponse
from pydantic import BaseModel

from db.models.store.brand import Brand
from utils.type_vars import T
from core.ext.adapters.get_order.schemas import GetOrderOrderFormSchema


class UrlClient:

    def __init__(self, brand_id: int, url: str):
        self.brand_id = brand_id
        self.url = url

        self.logger = logging.getLogger()

    async def make_request(
            self, method: str, path: str,
            headers: dict | BaseModel = None,
            query: dict | BaseModel = None,
            body: dict | BaseModel = None,
            return_model: Type[T] = None,
    ) -> T:
        if isinstance(headers, BaseModel):
            headers = headers.dict(exclude_unset=True)
        elif not isinstance(headers, dict):
            headers = {}

        if isinstance(query, BaseModel):
            query = query.dict(exclude_unset=True, exclude_none=True)

        if not query:
            query = dict()

        kwargs = {
            'url': self.url,
            'headers': headers,
        }

        if query:
            kwargs['params'] = query

        if body:
            if isinstance(body, BaseModel):
                kwargs['json'] = json.loads(body.json())
            else:
                kwargs['json'] = body

        async with ClientSession() as session:
            method = getattr(session, method.lower())

            try:
                resp: ClientResponse = await method(path, **kwargs)
            except Exception as e:
                self.logger.error(e, exc_info=True)

            if 200 != resp.status:
                return str(resp)

            try:
                result = await resp.json()
            except Exception as e:
                self.logger.error(e, exc_info=True)
                return str(resp)

            if resp.ok:
                if return_model:
                    return return_model(**result)

                return result
            else:
                self.logger.error("resp UrlClient not ok")

    async def get(
            self, path: str,
            headers: dict | BaseModel = None,
            query: dict | BaseModel = None,
            return_model: Type[T] = None,
    ):
        return await self.make_request("get", path, headers, query, return_model=return_model)


class GetOrderApiClient:

    def __init__(self, brand: Brand | None):
        self.base_url = 'https://api.getorder.biz/api/v1'
        self.brand = brand
        self._token = None

    @async_property
    async def token(self):
        if self._token:
            return self._token
        resp = await self.get_auth_token(
            self.brand.get_order_username,
            self.brand.get_order_password
        )
        self._token = resp['data']
        return self._token

    async def make_request(
            self, method: str, path: str,
            headers: dict | BaseModel = None,
            query: dict | BaseModel = None,
            body: dict | BaseModel = None,
            token: str = None, lang: str = None,
            return_model: Type[T] = dict,
    ) -> T:
        if not token:
            token = await self.token

        if not path.startswith('/'):
            path = f'/{path}'

        url = f'{self.base_url}{path}'

        if isinstance(headers, BaseModel):
            headers = headers.dict(exclude_unset=True)
        elif not isinstance(headers, dict):
            headers = {}

        if 'Accept-Language' not in headers and lang:
            headers['Accept-Language'] = lang

        if 'X-GETORDER-AUTH' not in headers and token:
            headers['X-GETORDER-AUTH'] = token

        if isinstance(query, BaseModel):
            query = query.dict(exclude_unset=True)

        kwargs = {
            'url': url,
            'headers': headers,
        }

        if query:
            kwargs['params'] = query
        if body:
            if isinstance(body, BaseModel):
                kwargs['json'] = json.loads(body.json())
            else:
                kwargs['json'] = body

        async with ClientSession() as session:
            method = getattr(session, method.lower())

            try:
                resp: ClientResponse = await method(**kwargs)
            except Exception as e:
                print(e)

            try:
                # print(f'*** getorder result: {resp}, url: {url}')
                result = await resp.json(content_type=None)
                # print(f'*** getorder result: {resp}, url: {url}')
            except Exception as e:
                print('***resp.json: ', e)

            if resp.ok:
                if return_model:
                    return return_model(**result)

                return result
            else:
                print('else')

    async def get_auth_token(self, username: str, password: str):
        path = '/get_token'
        body = {
            'username': username,
            'password': password
        }
        return await self.make_request('post', path, body=body, token='None')

    async def get_locales(self):
        return await self.make_request('get', '/locales')

    async def get_providers(self):
        return await self.make_request('get', f'/providers')

    async def get_restaurants(self, provider_id: int, lang: str = None):
        url = f'providers/{provider_id}/locations'
        return await self.make_request('get', url, lang=lang)

    async def get_categories(self, restaurant_id: int, lang: str = None):
        url = f'menu/{restaurant_id}/categories'
        return await self.make_request('get', url, lang=lang)

    async def get_categories_tree(self, restaurant_id: int, lang: str = None):
        url = f'menu/{restaurant_id}/categories_tree'
        return await self.make_request('get', url, lang=lang)

    async def get_products(self, category_id: int, restaurant_id: int, lang: str = None):
        url = f'/menu/{restaurant_id}/{category_id}/products'
        return await self.make_request('get', url, lang=lang)

    async def make_order(self, restaurant_id: int, body: GetOrderOrderFormSchema):
        url = f'order/{restaurant_id}/dispatch'
        return await self.make_request(
            'post',
            url,
            headers={'accept': 'application/json'},
            body=json.loads(body.json())
        )

    async def get_order(self, get_order_id: int):
        url = f'order/{get_order_id}'
        return await self.make_request(
            'get',
            url,
            headers={'accept': 'application/json'},
        )

    async def get_order_status_pos(self, get_order_id: int):
        url = f'order/{get_order_id}/pos_state'
        return await self.make_request(
            'get',
            url,
            headers={'accept': 'application/json'},
        )

    async def get_shipping_data(self, lang: str):
        url = 'cart/getorder_shippings'
        return await self.make_request('get', url, lang=lang)

    async def get_payments_data(self, lang: str):
        url = 'cart/getorder_payments'
        return await self.make_request('get', url, lang=lang)

    async def get_customer_fields(self, lang: str):
        url = 'cart/customer_fields'
        return await self.make_request('get', url, lang=lang)

    async def get_address_fields(self, lang: str):
        url = 'cart/address_fields'
        return await self.make_request('get', url, lang=lang)

    async def get_order_status(self, order_go_id: str):
        url = f'order/{order_go_id}/states'
        return await self.make_request('get', url)
