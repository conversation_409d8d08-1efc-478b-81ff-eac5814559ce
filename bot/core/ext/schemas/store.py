from __future__ import annotations

from pydantic import BaseModel, Field

from .translations import ValueAndTranslationAPI


class BaseStoreAPI(BaseModel):
    id: int | None = None
    external_id: str | None = None
    external_type: str | None = None

    image_url: str | None = None

    position: int | None = None

    latitude: str | None = None
    longitude: str | None = None
    distance: int = Field(0, description="radius around the store")
    polygon: dict | None = Field(None, description="polygon store services")

    currency: str

    custom_fields: list[StoreCustomField] = Field(description="information fields, for example, youtube, facebook, etc")

    city: str | None = None


class StoreAPI(BaseStoreAPI):
    name: ValueAndTranslationAPI | None = None
    description: ValueAndTranslationAPI | None = None
    ai_description: ValueAndTranslationAPI | None = None
    currency: str | None = None


class Store(BaseStoreAPI):
    get_order_id: int | None = None  # field for get_order_id, not get_order_unique_id, which specified in external_id
    location_get_order_id: int | None = None
    location_get_order_unique_id: int | None = None

    name: str
    description: str | None = None
    ai_description: str | None = None

    image_path: str | None = None
    excel_row_number: int | None = None

    is_distance: bool = False
    is_polygon: bool = False
    is_swap_coordinates: bool = True

    organisation_id: int | None = None

    company: str | None = None
    company_url: str | None = None
    data_url: str | None = None

    banners: list[dict] | None = None


class StoreCustomField(BaseModel):
    name: str
    value: str | list[str] | None


Store.update_forward_refs()
StoreAPI.update_forward_refs()
