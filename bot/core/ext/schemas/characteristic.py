from pydantic import BaseModel

from core.ext.types import FilterType<PERSON>iteral
from schemas import CharacteristicFilterType
from .translations import ValueAndTranslationAPI


class BaseCharacteristicAPI(BaseModel):
    id: int | None = None
    external_id: str

    is_hide: bool = False
    position: int | None = None


class CharacteristicAPI(BaseCharacteristicAPI):
    name: ValueAndTranslationAPI
    filter_type: FilterTypeLiteral = CharacteristicFilterType.VALUE.value


class Characteristic(BaseCharacteristicAPI):
    name: str
    filter_type: CharacteristicFilterType = CharacteristicFilterType.VALUE

    excel_row_number: int | None = None


class BaseCharacteristicValueAPI(BaseModel):
    external_id: str


class CharacteristicValueAPI(BaseCharacteristicValueAPI):
    value: ValueAndTranslationAPI | None = None


class CharacteristicValue(BaseCharacteristicValueAPI):
    value: str | None = None
