from pydantic import BaseModel

from .attributes import AttributeAPI, AttributeGroup, AttributeGroupAPI
from .category import Category, CategoryAPI
from .characteristic import Characteristic, CharacteristicAPI
from .product import Product, ProductAPI, ProductSpotPrice
from .product_group import ProductGroup
from .store import Store, StoreAPI
from .translations import AdapterTranslations


class AdapterResult(BaseModel):
    stores: list[Store]
    products: list[Product]
    product_groups: list[ProductGroup]
    categories: list[Category | None] | None = []
    attribute_groups: list[AttributeGroup]
    characteristics: None | list[Characteristic] = None
    translations: AdapterTranslations | None = None
    spot_prices: list[ProductSpotPrice] | None = None


class AdapterJSONData(BaseModel):
    stores: list[StoreAPI] | None = None
    products: list[ProductAPI]
    product_groups: list[ProductGroup] | None = []
    categories: list[CategoryAPI | None] | None = []
    attribute_groups: list[AttributeGroupAPI] | None = []
    attributes: list[AttributeAPI] | None = None
    characteristics: None | list[CharacteristicAPI] = None
    spot_prices: None | list[ProductSpotPrice]
    to_stores: list[int] | None = None
