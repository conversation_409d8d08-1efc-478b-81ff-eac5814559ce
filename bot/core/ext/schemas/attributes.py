from pydantic import BaseModel

from .translations import ValueAndTranslationAPI


class BaseAttributeAPI(BaseModel):
    id: int | None = None
    external_id: str | None = None

    attribute_id: str

    min: int | None = None
    max: int | None = None
    price_impact: float | int

    is_available: bool = True
    selected_by_default: bool = False


class AttributeAPI(BaseAttributeAPI):
    name: ValueAndTranslationAPI
    attribute_group_id: str | None = None


class Attribute(BaseAttributeAPI):
    name: str


class BaseAttributeGroupAPI(BaseModel):
    id: int | None = None
    external_id: str

    attribute_group_id: str | None = None

    min: int
    max: int | None = None

    position: int | None = None
    excel_position: int | None = None


class AttributeGroupAPI(BaseAttributeGroupAPI):
    name: ValueAndTranslationAPI


class AttributeGroup(BaseAttributeGroupAPI):
    name: str

    attributes: list[Attribute]
