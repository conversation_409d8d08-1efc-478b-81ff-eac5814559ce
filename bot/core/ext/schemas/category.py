from __future__ import annotations

from pydantic import BaseModel

from .translations import ValueAndTranslationAPI


class BaseCategoryAPI(BaseModel):
    id: int | None = None
    external_id: str | None = None

    is_default: bool = False
    position: int

    image_url: str | None = None

    filters: list[str]

    stores_external_ids: list[str]
    father_category_id: str | None = None

    excel_position: int | None = None


class CategoryAPI(BaseCategoryAPI):
    name: ValueAndTranslationAPI


class Category(BaseCategoryAPI):
    get_order_id: int | None = None
    name: str
    image_path: str | None = None
