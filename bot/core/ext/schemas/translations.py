from pydantic import BaseModel, Field


class TranslationAPI(BaseModel):
    lang: str
    value: str | None = Field(None, description="translation in language lang")


class ValueAndTranslationAPI(BaseModel):
    value: str | None = Field(None, description="original name item")
    translations: list[TranslationAPI] | None = None


class BaseTranslation(BaseModel):
    id: int | None = None
    external_id: str | None = None
    lang: str


class CharacteristicValueTranslation(BaseTranslation):
    value: str | None


class Translation(BaseTranslation):
    name: str | None


class TranslationProduct(Translation):
    description: str | None = None
    characteristics: list[CharacteristicValueTranslation] = Field(default_factory=list)
    pti_info_text: str | None = None


class TranslationStore(Translation):
    description: str | None
    ai_description: str | None


class AdapterTranslations(BaseModel):
    stores: list[TranslationStore]
    products: list[TranslationProduct]
    categories: list[Translation]
    attribute_groups: list[Translation]
    attributes: list[Translation]
    characteristics: list[Translation]
