from db import DBSession
from utils.processes_manager.background_worker import <PERSON>BackgroundWorker
from .db_funcs import get_schedules_for_update
from .functions import add_import_to_data_porter


class ImportUpdaterWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "import updater"
    DEFAULT_TIMEOUT = 15

    async def iteration(self):
        with DBSession():
            schedules = await get_schedules_for_update()
            if not schedules:
                return "no_schedules"

            for schedule in schedules:
                await add_import_to_data_porter(schedule)

            return "sent"
