import logging
from datetime import datetime

from core.ext.types import ActionType, ExternalAPIType, StatusType
from core.google_sheets.functions import validate_google_sheets
from db import crud, sess
from db.models import DataPorter, StoreScheduler
from utils.platform_admins import send_message_to_platform_admins

logger = logging.getLogger("debugger.auto_updater")


async def add_import_to_data_porter(data: tuple[StoreScheduler, bool, bool, str]):
    if not data:
        return
    schedule, is_sheets_update, is_poster_update, group_lang = data
    logger.debug(
        f"START scheduled import task\n{schedule.id=}, {schedule.import_source=} on {schedule.time_value:%H:%M}"
        f" {schedule.day_of_week.value}, {schedule.brand_id=}, {is_sheets_update=},"
        f"{is_poster_update=}, {group_lang=}"
    )

    try:
        source_data = None
        match schedule.import_source:
            case ExternalAPIType.SHEETS.value:
                if not is_sheets_update:
                    return
                google_sheets_url = await crud.get_group_import_setting(
                    schedule.brand_id,
                    ExternalAPIType.SHEETS.value, "url"
                )
                if not google_sheets_url:
                    logger.debug(f"google_sheets_url not FOUND")
                    return
                source_data = await validate_google_sheets(google_sheets_url, group_lang)
            case ExternalAPIType.POSTER.value:
                if not is_poster_update:
                    return
            case _:
                raise ValueError(f"Unsupported import_source...,  {schedule.import_source=}")

        logger.debug(f"Add data to DataPorter, {source_data=}")

        await DataPorter.create(
            action_type=ActionType.IMPORT,
            external_type=schedule.import_source,
            status=StatusType.PENDING,
            brand_id=schedule.brand_id,
            user_id=schedule.user_id,
            message_id=None,
            lang=group_lang,
            user_lang=group_lang,
            prom_file_main_lang=schedule.json_data.get("prom_file_main_lang", None) if schedule.json_data else None,
            source_data=source_data,
        )

        schedule.update_date = datetime.utcnow()

        sess().flush()
        sess().commit()
        logger.debug(
            "Add import data to DataPorter OK\nSET schedule [%d] update_date to datetime.utcnow()",
            schedule.id
        )
    except Exception as error:
        await send_message_to_platform_admins(
            f"""Import schedule task error:
            {schedule.brand_id=}
            {schedule.id=}
            {schedule.import_source=}
"""
        )
        logger.error(error, exc_info=True)
