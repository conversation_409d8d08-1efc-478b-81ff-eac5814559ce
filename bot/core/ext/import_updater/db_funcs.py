import logging

from sqlalchemy import exists, func, not_, or_, select

from core.ext.types import ExternalAPIType, StatusType
from db import db_func, sess
from db.models import Brand, DataPorter, Group, StoreScheduler

logger = logging.getLogger("debugger.auto_updater")


@db_func
def get_schedules_for_update() -> list[tuple[StoreScheduler, bool, bool, str]]:
    query = sess().query(StoreScheduler, Brand.is_sheets_update, Brand.is_poster_update, Group._lang)
    query = query.join(Brand, Brand.id == StoreScheduler.brand_id).join(
        Group, Group.id == Brand.group_id
        )

    query = query.where(Group.status == "enabled")

    query = query.where(
        or_(
            Brand.is_poster_update.is_(True),
            Brand.is_sheets_update.is_(True)
        )
    )

    query = query.where(
        StoreScheduler.import_source.in_(
            [
                ExternalAPIType.POSTER.value,
                ExternalAPIType.SHEETS.value,
                ExternalAPIType.PROM.value,
                ExternalAPIType.INCUST.value,
                ExternalAPIType.GET_ORDER.value,
            ]
        ),
        StoreScheduler.status.is_(None)
    )
    # is this day of week ?
    query = query.where(
        StoreScheduler.day_of_week == func.upper(
            func.date_format(
                func.convert_tz(func.UTC_TIMESTAMP(), "UTC", StoreScheduler.time_zone),
                "%W"
            )
        )
    )
    # is this time ?
    query = query.where(
        func.date_format(
            func.convert_tz(
                StoreScheduler.time_value,
                StoreScheduler.time_zone,
                "UTC"
            ),
            "%H:%i"
        ) == func.date_format(func.UTC_TIMESTAMP(), "%H:%i")
    )

    # not run today ?
    query = query.where(or_(
        StoreScheduler.update_date.is_(None),
        func.date(StoreScheduler.update_date) != func.date(func.UTC_TIMESTAMP())
    ))

    # check is import not working...
    porter_query = select(1).where(
        DataPorter.status.in_(
            [
                StatusType.NOT_STARTED,
                StatusType.PENDING,
                StatusType.LOADING,
                StatusType.SAVING,
            ]
        ),
        DataPorter.brand_id == StoreScheduler.brand_id,
        DataPorter.external_type == StoreScheduler.import_source,
    )

    query = query.where(not_(exists(porter_query)))

    return query.all()
