from dataclasses import dataclass
from datetime import timedelta
from enum import Enum
from typing import Any, Literal, Protocol, Type, TypedDict

from psutils.exceptions import ErrorWithTextVariable


class ExternalType(Enum):
    EXCEL = "excel"
    SHEETS = "sheets"
    GET_ORDER = "get_order"
    PROM = "prom"
    POSTER = "poster"
    INCUST = "incust"
    CHOICE = "choice"
    MENU = "menu"


@dataclass
class WarningToLocaliseType:
    error: ErrorWithTextVariable
    file_lang: str
    user_lang: str


WarningType = WarningToLocaliseType | str
WarningsListType = list[WarningType]

StatusTypeLiteral = Literal[
    "not_started", "pending",
    "loading", "processing",
    "saving", "post_saving",
    "done", "error",
]


class StatusType(Enum):
    NOT_STARTED = "not_started"
    PENDING = "pending"
    LOADING = "loading"
    PROCESSING = "processing"
    SAVING = "saving"
    POST_SAVING = "post_saving"
    DONE = "done"
    ERROR = "error"


class OnStatusUpdatedType(Protocol):

    async def __call__(
            self,
            status: StatusTypeLiteral,
            time_passed: timedelta,
            warnings: WarningsListType,
    ):
        ...


class ReturnType(TypedDict, total=False):
    result: Any
    status: StatusTypeLiteral
    time_passed: timedelta
    warnings: WarningsListType | None


class ActionType(Enum):
    IMPORT = "import"
    EXPORT = "export"


class SelectMode(Enum):
    ALL = "all"
    SELECTED = "selected"


ActionTypeLiteral = Literal["import", "export"]

external_api_type_values = [(item.name, item.value,) for item in ExternalType]
external_api_type_values.append(("JSON", "json",))
ExternalAPIType = Enum("ExternalAPIType", names=external_api_type_values)

ExternalTypeLiteral = Literal[
    "get_order", "excel", "prom", "poster", "sheets", "incust", "choice", "json",
    "menu"]
ExternalAPITypeLiteral = Literal[ExternalTypeLiteral, "json"]
ExternalAPITypeExportLiteral = Literal["excel", "sheets", "json"]

ExternalExtTypeLiteral: Type[str] = Literal[
    "get_order", "prom", "poster", "sheets", "choice", "excel", "menu"]

FilterTypeLiteral = Literal["v", "m", "ri", "i", "f"]
