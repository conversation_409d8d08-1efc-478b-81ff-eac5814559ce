import re
from abc import ABC

from psutils.exceptions import ErrorWithTextVariable


class StoreImportError(ErrorWithTextVariable, ABC):

    def __init__(self, message: str, **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"Import error: {self.message}"


class StoreImportUnknownError(StoreImportError):
    text_variable = "store import unknown error"

    def __init__(self, **kwargs):
        super().__init__("Import failed with unknown error", **kwargs)


class StoreImportInvalidInputError(StoreImportError):
    text_variable = "store import invalid input error"


class DifferentExternalTypesError(StoreImportInvalidInputError):
    text_variable = "different external types error"

    def __init__(self):
        super().__init__("All objects must be the same external type.")


class UnknownStoreExternalIdInProductError(StoreImportInvalidInputError):
    text_variable = "unknown store external id in product error"

    def __init__(self, store_ext_id: str, product_ext_id: str):
        super().__init__(
            f"Unknown store external id '{store_ext_id}' in product '{product_ext_id}'",
            store_ext_id=store_ext_id, product_ext_id=product_ext_id,
        )


class UnknownCategoryExternalIdInProductError(StoreImportInvalidInputError):
    text_variable = "unknown category external id in product error"

    def __init__(self, category_ext_id: str, product_ext_id: str):
        super().__init__(
            f"Unknown category external id '{category_ext_id}' in product '{product_ext_id}'",
            category_ext_id=category_ext_id, product_ext_id=product_ext_id,
        )


class UnknownAttributeGroupExternalIdInProductError(StoreImportInvalidInputError):
    text_variable = "unknown attribute group external id in product error"

    def __init__(self, ag_ext_id: str, product_ext_id: str):
        super().__init__(
            f"Unknown attribute group external id '{ag_ext_id}' in product '{product_ext_id}'",
            ag_ext_id=ag_ext_id, product_ext_id=product_ext_id,
        )


class UnknownProductGroupExternalIdInProductError(StoreImportInvalidInputError):
    text_variable = "unknown product group external id in product error"

    def __init__(self, pg_ext_id: str, product_ext_id: str):
        super().__init__(
            f"Unknown product group external id '{pg_ext_id}' in product '{product_ext_id}'",
            pg_ext_id=pg_ext_id, product_ext_id=product_ext_id,
        )


class ImportCheckerError(ImportError):
    def __init__(self):
        super().__init__("Checker failed with error")


class BaseImportDownloadingFileError(StoreImportError, ABC):
    pass


class ImportDownloadingFileError(BaseImportDownloadingFileError):
    text_variable = "import downloading file error"

    def __init__(self, file_url: str):
        self.file_url = file_url
        super().__init__(f"Error downloading file '{file_url}'", file_url=file_url)


class ImportDownloadingNotImageError(BaseImportDownloadingFileError):
    text_variable = "import downloading not image error"

    def __init__(self, image_url: str):
        self.image_url = image_url
        super().__init__(f"Not image {image_url}", image_url=image_url)


class ImportDownloadingNotFileError(BaseImportDownloadingFileError):
    text_variable = "import downloading not file error"

    def __init__(self, file_url: str):
        self.file_url = file_url
        super().__init__("Not file {file_url}", file_url=file_url)


class UnknownFatherCategoryError(StoreImportInvalidInputError):
    text_variable = "unknown father category error"

    def __init__(self, father_category_id: str):
        super().__init__(f"Invalid father category {father_category_id}", father_category_id=father_category_id)


class StoreExportError(ErrorWithTextVariable, ABC):

    def __init__(self, message: str, **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"export error: {self.message}"


class StoreExportUnknownError(StoreExportError):
    text_variable = "store export unknown error"

    def __init__(self, **kwargs):
        super().__init__("Export failed with unknown error", **kwargs)


class ExportCheckerError(StoreExportError):
    def __init__(self):
        super().__init__("Checker failed with error")


class UnknownModifierError(StoreImportInvalidInputError):
    text_variable = "unknown modifier error"

    def __init__(self, modifier: str, product_group_code: str):
        super().__init__(
            f"Unknown modifier {modifier} in product grop {product_group_code}",
            modifier=modifier, product_group_code=product_group_code,
        )


class UnknownFilterError(StoreImportInvalidInputError):
    text_variable = "unknown category filter error"

    def __init__(self, filter_name: str, category_external_id: str):
        super().__init__(
            f"Unknown filter {filter_name} in category {category_external_id}",
            filter_name=filter_name,
            category_external_id=category_external_id,
        )


class GenerationTemplateError(ErrorWithTextVariable):
    text_variable = "generation template error"


class StoreImportObjectNotFoundByIDError(StoreImportInvalidInputError):
    text_variable = "import object not found by id error"

    def __init__(self, object_type: str, id: int):
        super().__init__(
            f"Object of type {object_type} with id {id} not found",
            object_name=object_type, id=id,
        )


class StoreImportCategoryFatherIDError(StoreImportInvalidInputError):
    text_variable = "import category father id cannot refer to itself error"

    def __init__(self, category_name: str):
        super().__init__(
            "import category father id cannot refer to itself error",
            category_name=category_name,
        )


class StoreImportDuplicateDbError(StoreImportInvalidInputError):
    text_variable = "store import duplicate db error"

    def __init__(self, db_error: str):
        pattern = r"Duplicate entry '(.+)' for key '(.+)'"
        match = re.search(pattern, db_error)
        error_key, duplicate_value = "", ""

        if match:
            duplicate_value = match.group(1)
            error_key = match.group(2)

        super().__init__(
            "store import duplicate db error",
            duplicate_value=duplicate_value,
            error_key=error_key,
        )


class StoreImportDuplicateObjectId(StoreImportInvalidInputError):
    text_variable = "store import duplicate object id error"

    def __init__(self, object_name: str, id: int):
        super().__init__(
            f"Duplicate id \"{id}\" in {object_name}",
            object_name=object_name, id=id,
        )


class StoreImportDuplicateObjectExternalId(StoreImportInvalidInputError):
    text_variable = "store import duplicate object external id error"

    def __init__(self, object_name: str, external_id: str):
        super().__init__(
            f"Duplicate external_id \"{external_id}\" in {object_name}",
            object_name=object_name, external_id=external_id,
        )


class StoreImportFloatingSumUnknownValueError(StoreImportInvalidInputError):
    text_variable = "store import floating price unknown value error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            "floating price unknown value error",
            product_id=product_id,
            product_name=product_name,
        )


class StoreImportFloatingSumMinMaxValueError(StoreImportInvalidInputError):
    text_variable = "store import floating price min max value error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            "floating price min max value error",
            product_id=product_id,
            product_name=product_name,
        )


class StoreImportFloatingSumInvalidPatternValueError(StoreImportInvalidInputError):
    text_variable = "store import floating price invalid pattern value error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            "floating price invalid pattern value error",
            product_id=product_id,
            product_name=product_name,
        )


class StoreImportFloatingSumMinMaxCountValueError(StoreImportInvalidInputError):
    text_variable = "store import floating price min max count value error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            "floating price min max count value error",
            product_id=product_id,
            product_name=product_name,
        )


class StoreImportFloatingSumNoUserSumWithoutOptionsValueError(StoreImportInvalidInputError):
    text_variable = "store import floating price no user sum without options error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            "floating price no user sum without options error",
            product_id=product_id,
            product_name=product_name,
        )


class StoreImportFloatingSumMinMaxWithOptionsValueError(StoreImportInvalidInputError):
    text_variable = "store import floating price min max with options error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            "floating price min max with options error",
            product_id=product_id,
            product_name=product_name,
        )


class StoreImportFloatingSumNegativeNumberValueError(StoreImportInvalidInputError):
    text_variable = "store import floating price negative number error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            "floating price negative number error",
            product_id=product_id,
            product_name=product_name,
        )


class StoreImportUnknownProductTypeError(StoreImportInvalidInputError):
    text_variable = "store import unknown product type error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            "store import unknown product type error",
            product_id=product_id,
            product_name=product_name,
        )


class StoreImportTranslationForNotExistingCharacteristicError(StoreImportInvalidInputError):
    text_variable = "store import translation for not existing characteristic error"

    def __init__(self, characteristic_ext_id: str):
        super().__init__(
            f"Specified translation unknown characteristic {characteristic_ext_id}",
            characteristic_ext_id=characteristic_ext_id,
        )


class StoreImportTranslationForNotExistingCharacteristicValueError(StoreImportInvalidInputError):
    text_variable = "store import translation for not existing characteristic value error"

    def __init__(self, product_ext_id: str, characteristic_ext_id: str):
        super().__init__(
            f"Specified translation for characteristic "
            f"{characteristic_ext_id} in product {product_ext_id}, "
            f"but value is not specified",
            product_ext_id=product_ext_id,
            characteristic_ext_id=characteristic_ext_id,
        )


class StoreImportUnknownCharacteristicError(StoreImportInvalidInputError):
    text_variable = "store import unknown characteristic error"

    def __init__(self, characteristic_id: str):
        super().__init__(
            "import characteristic is not found in Characteristics list",
            characteristic_id=characteristic_id,
        )


class StoreExportPostImportError(ErrorWithTextVariable):
    text_variable = "store brand exporter post import error"


class StoreImportDownloadMediasError(StoreImportError):
    text_variable = "store import download medias error"

    def __init__(self, errors: str, errors_count: int):
        super().__init__("Downloading medias error", errors=errors, errors_count=errors_count)


class IncustNotStoresImportError(StoreImportError):
    text_variable = "store brand not stores import error"


class IncustNotLoyaltyImportError(StoreImportError):
    text_variable = "store brand not loyalty import error"


class StoreCreateNotCountryError(StoreImportError):
    text_variable = "store create invalid country code error"


class StoreAPIGoogleError(StoreExportError):
    text_variable = "store google api error does not have permission error"


class ImportObjectMaxPositionError(StoreExportError):
    text_variable = "max object position error"

    def __init__(self, object_name: str):
        super().__init__("max object position error", object_name=object_name)


class ProductImportPositionWarning(StoreImportError):
    text_variable = "import product position warning"

    def __init__(self, product_name: str):
        super().__init__(
            "import product position warning",
            product_name=product_name,
        )


class CategoryImportPositionWarning(StoreImportError):
    text_variable = "import category position warning"

    def __init__(self, category_name: str):
        super().__init__(
            "import category position warning",
            category_name=category_name,
        )

class AttributeGroupImportPositionWarning(StoreImportError):
    text_variable = "import attribute group position warning"

    def __init__(self, attribute_group_name: str):
        super().__init__(
            "import attribute group position warning",
            attribute_group_name=attribute_group_name,
        )


class StoreImportWarning(ErrorWithTextVariable, ABC):

    def __init__(self, message: str, **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"Import warning: {self.message}"


class ProductNotInStoreWarning(StoreImportWarning):
    text_variable = "import product not in store warning"

    def __init__(self, product_external_id: str, product_name: str, store_external_id: str, store_name: str):
        super().__init__(
            "import product not in store warning",
            product_external_id=product_external_id,
            product_name=product_name,
            store_external_id=store_external_id,
            store_name=store_name,
        )


class StoreImportDuplicateObjectWarning(StoreImportWarning):
    text_variable = "store import duplicate object warning"

    def __init__(self, object_name: str, external_id: str):
        super().__init__(
            f"Duplicate \"{external_id}\" for {object_name}",
            object_name=object_name, external_id=external_id,
        )


class GoogleSheetsPermissionError(StoreImportInvalidInputError):
    text_variable = "google sheets permission error"

    def __init__(self, accessor_email: str):
        super().__init__(
            "No permission to access Google Sheets document",
            accessor_email=accessor_email
        )
