import logging
from abc import ABC, abstractmethod
from collections import defaultdict
from typing import Any

import time

from core.ext import schemas
from core.store.product.functions import get_product_gallery
from db import crud
from db.models import (
    Group, Store, StoreCharacteristic, StoreCharacteristicValue, StoreCustomField,
    StoreProduct,
    StoreProductSpotPrice,
)
from schemas import (
    AttributeGroupSortEnum, FloatingSumSettings, ProductsListParams,
    ProductsSortEnum,
)
from utils.translator import Translator


class BaseDataLoader(ABC):
    def __init__(
            self, brand_id: int,
            lang: str,
            brand_lang: str,
    ):
        self.brand_id: int = brand_id
        self.lang: str = lang
        self.brand_lang: str = brand_lang

        self.data: dict = {}
        self.langs: list[str] = []
        self.translations: dict = {}
        self.logger = logging.getLogger("debugger")

    @abstractmethod
    async def load(self) -> dict:
        raise NotImplementedError


def create_floating_sum_value(product: schemas.Product) -> str:
    settings = FloatingSumSettings(
        is_enabled=product.floating_sum_enabled,
        max=round(
            product.floating_sum_max // 100, 2
        ) if product.floating_sum_enabled else 0,
        min=round(
            product.floating_sum_min // 100, 2
        ) if product.floating_sum_min else 0,
        options=product.floating_sum_options,
        user_sum_enabled=product.floating_sum_user_sum_enabled,
    )
    if not settings.is_enabled:
        return ""

    components = []

    # Add options
    if settings.options:
        components.extend(map(str, settings.options))

    # Add min/max range
    if settings.min is not None and settings.max is not None:
        components.append(f"{settings.min}/{settings.max}")

    # Add 'nosum' if user sum is disabled
    if not settings.user_sum_enabled:
        components.append("nosum")

    # Join all components
    return ", ".join(components)


class DataLoader(BaseDataLoader):

    async def get_translations(self, object_: Any) -> dict[str, dict[str, str | None]]:
        if not self.langs:
            group: Group = await Group.get_by_brand(self.brand_id)
            self.langs = group.get_langs_list()
            if group.lang in self.langs:
                self.langs.remove(group.lang)

        return await Translator.get_translations_data(object_, self.langs)

    def swap_languages_data(self, object_data: Any, translation_data: dict):
        if self.lang != self.brand_lang:
            translation = translation_data.pop(self.lang, {})
            translation_data[self.brand_lang] = {}

            for name in translation.keys():
                translation_data[self.brand_lang][name] = getattr(object_data, name)
                setattr(object_data, name, translation[name])

    async def _load_stores(self):
        db_stores: list[Store] = await crud.get_stores(
            self.brand_id, with_translation=False
        )

        stores: dict[int, schemas.Store] = {}
        for store in db_stores:
            if store.id in stores:
                continue

            store_custom_fields: list[StoreCustomField] = store.custom_fields or []

            custom_fields: list[schemas.StoreCustomField] = [
                schemas.StoreCustomField(
                    name=custom_field.name,
                    value=custom_field.value,
                )
                for custom_field in store_custom_fields
            ]

            stores.update(
                {
                    store.id:
                        schemas.Store(
                            id=store.id,
                            external_id=store.external_id or store.name,
                            get_order_id=store.get_order_id,
                            location_get_order_id=store.location_get_order_id,
                            location_get_order_unique_id=store.location_get_order_unique_id,
                            name=store.name,
                            description=store.description,
                            ai_description=store.ai_description,
                            image_url=await store.image_media_url,
                            is_distance=store.is_distance,
                            is_polygon=store.is_polygon,
                            is_swap_coordinates=store.is_swap_coordinates,
                            latitude=store.latitude,
                            longitude=store.longitude,
                            distance=store.distance,
                            polygon=store.polygon,
                            organisation_id=store.organisation_id,
                            currency=store.currency,
                            custom_fields=custom_fields,
                            company=store.company,
                            company_url=store.company_url,
                            data_url=store.data_url,
                            banners=store.banners,
                            city=store.city,
                        )
                }
            )

            translations = await self.get_translations(store)
            self.swap_languages_data(stores[store.id], translations)
            if not translations:
                continue

            if not self.translations.get("Store"):
                self.translations["Store"] = {}
            self.translations["Store"][store.id] = [
                schemas.TranslationStore(
                    id=store.id,
                    external_id="name",
                    lang=lang,
                    name=data.get("name"),
                    description=data.get("description"),
                    ai_description=data.get("description"),
                )
                for lang, data in translations.items()
            ]

        self.data["Store"] = list(stores.values())

    async def _load_categories(self):
        async def load_categories(fc_id: int | None = None):
            tmp_categories = await crud.get_store_categories(
                self.brand_id, father_category_id=fc_id
            )
            if not tmp_categories:
                return
            for tmp_cat in tmp_categories:
                db_categories.append(tmp_cat)
                await load_categories(tmp_cat.id)

        # needed to sort categories by tree
        db_categories = []
        await load_categories()

        categories: dict[int, schemas.Category] = {}
        for category in db_categories:
            if category.id in categories:
                continue

            filters = await crud.get_category_filters(category.id)

            father_category_id = father_category.external_id if (
                father_category := category.father_category) else None
            categories[category.id] = schemas.Category(
                id=category.id,
                external_id=category.external_id or category.id,
                is_default=category.is_default,
                get_order_id=category.get_order_id,
                name=category.name,
                image_url=await category.image_url,
                filters=[filter_.external_id or filter_.name for filter_ in filters],
                stores_external_ids=[store.external_id or store.name for store in
                                     await crud.get_category_stores(category.id)],
                father_category_id=father_category_id,
                position=category.position,
            )

            translations = await self.get_translations(category)
            self.swap_languages_data(categories[category.id], translations)
            if not translations:
                continue

            if not self.translations.get("Category"):
                self.translations["Category"] = {}
            self.translations["Category"][category.id] = [
                schemas.Translation(
                    id=category.id,
                    external_id=field_name,
                    lang=lang,
                    name=value,
                )
                for lang, data in translations.items()
                for field_name, value in data.items()
            ]

        self.data["Category"] = list(categories.values())

    async def _load_products(self):
        products: dict[int, schemas.Product] = {}

        db_products: list[StoreProduct] = await crud.get_store_products(
            self.brand_id,
            hide_modification=False,
            params=ProductsListParams(
                sort=ProductsSortEnum.DEFAULT,
                is_export=True,
            ),
            sort_by_availability=False,
        )

        for product in db_products:
            if product.id in products:
                continue

            product_type_info_custom_fields = []
            if product.pti_info_text:
                info_text_schema = schemas.ProductCustomField(
                    name="pti_info_text",
                    value=product.pti_info_text,
                    type="product_type_info",
                )
                product_type_info_custom_fields.append(info_text_schema)

            if product.pti_info_link:
                product_type_info_custom_fields.append(
                    schemas.ProductCustomField(
                        name="pti_info_link",
                        value=product.pti_info_link,
                        type="product_type_info",
                    )
                )

            product_type_liqpay_custom_fields = []

            if product.liqpay_id:
                product_type_liqpay_custom_fields.append(
                    schemas.ProductCustomField(
                        name="liqpay_id",
                        value=product.liqpay_id,
                        type="product_type_liqpay",
                    )
                )

            if product.liqpay_unit_name:
                product_type_liqpay_custom_fields.append(
                    schemas.ProductCustomField(
                        name="liqpay_unit_name",
                        value=product.liqpay_unit_name,
                        type="product_type_liqpay",
                    )
                )

            if product.liqpay_codifier:
                product_type_liqpay_custom_fields.append(
                    schemas.ProductCustomField(
                        name="liqpay_codifier",
                        value=product.liqpay_codifier,
                        type="product_type_liqpay",
                    )
                )

            if product.liqpay_tax_list:
                product_type_liqpay_custom_fields.append(
                    schemas.ProductCustomField(
                        name="liqpay_tax_list",
                        value=product.liqpay_tax_list,
                        type="product_type_liqpay",
                    )
                )

            characteristics = []
            characteristics_translations: dict[
                str, list[schemas.CharacteristicValueTranslation]] = defaultdict(list)
            for characteristic_value in await StoreCharacteristicValue.get_list(
                    product_id=product.id
            ):
                characteristic = await StoreCharacteristic.get(
                    characteristic_value.characteristic_id
                )
                characteristic_schema = schemas.CharacteristicValue(
                    external_id=characteristic.external_id or characteristic.name,
                    value=characteristic_value.value,
                )
                characteristics.append(characteristic_schema)

                characteristic_value_translations = await self.get_translations(
                    characteristic_value
                )
                self.swap_languages_data(
                    characteristic_schema, characteristic_value_translations
                )
                if not characteristic_value_translations:
                    continue

                for lang, data in characteristic_value_translations.items():
                    characteristics_translations[lang].append(
                        schemas.CharacteristicValueTranslation(
                            external_id=characteristic.external_id,
                            lang=lang,
                            value=data.get("value"),
                        )
                    )

            gallery = await get_product_gallery(product.id)
            gallery_items = [
                item.media_url
                for item in gallery.items
            ] if gallery else None

            products[product.id] = schemas.Product(
                id=product.id,
                product_id=product.product_id,
                product_group_id=product_group.external_id if (
                    product_group := product.product_group) else None,
                external_id=product.external_id or product.id,
                is_available=product.is_available,
                name=product.name,
                description=product.description,
                image_url=await product.media_url,
                gallery_items=gallery_items,
                price=product.price,
                old_price=product.old_price if product.old_price else 0,
                buy_min_quantity=product.buy_min_quantity,
                spots_prices={},
                is_weight=product.is_weight,
                weight_unit=product.weight_unit,
                characteristics=characteristics,
                stores_external_ids=[store.external_id or store.name for store in
                                     await crud.get_product_stores(product.id)],
                attribute_groups_external_ids=[
                    attribute_group.external_id or attribute_group.id
                    for attribute_group in
                    await crud.get_product_attribute_groups(product.id)
                ],
                categories_external_ids=[
                    category.external_id or category.id
                    for category in await crud.get_product_categories(product.id)
                ],
                floating_sum_value=create_floating_sum_value(product),
                type=product.type,
                product_type_info_custom_fields=product_type_info_custom_fields,
                product_type_liqpay_custom_fields=product_type_liqpay_custom_fields,
                pti_info_text=product.pti_info_text,
                pti_info_link=product.pti_info_link,
                liqpay_id=product.liqpay_id,
                liqpay_unit_name=product.liqpay_unit_name,
                liqpay_codifier=product.liqpay_codifier,
                liqpay_tax_list=product.liqpay_tax_list,
                need_auth=product.need_auth,
                floating_qty_enabled=product.floating_qty_enabled,
                position=product.position,
            )

            translations = await self.get_translations(product)
            self.swap_languages_data(products[product.id], translations)
            if not translations:
                continue

            if not self.translations.get("Product"):
                self.translations["Product"] = {}
            self.translations["Product"][product.id] = [
                schemas.TranslationProduct(
                    id=product.id,
                    external_id="name",
                    lang=lang,
                    name=data.get("name"),
                    description=data.get("description"),
                    characteristics=characteristics_translations.get(lang, []),
                    pti_info_text=data.get("pti_info_text"),
                )
                for lang, data in translations.items()
            ]

        debugger = logging.getLogger("debugger")
        products_list = list(products.values())
        debugger.debug(
            f"Export. Products_external_ids: "
            f"{[product.external_id for product in products_list]}"
        )
        self.data["Product"] = products_list

    async def _load_product_groups(self):
        product_groups: dict[int, schemas.ProductGroup] = {}
        for product_group in await crud.get_product_groups(self.brand_id):
            if product_group.id in product_groups:
                continue

            modifiers = await crud.get_product_group_characteristics(
                product_group.id, is_modifier=True
            )
            hide_modifications_by = \
                await crud.get_product_group_characteristics(
                    product_group.id, show_one_modification=True
                )

            modifiers = [modifier.external_id or modifier.name for modifier in
                         modifiers]
            hide_modifications_by = [
                hide_modification_by.external_id or hide_modification_by.name for
                hide_modification_by in hide_modifications_by]

            product_groups.update(
                {
                    product_group.id:
                        schemas.ProductGroup(
                            id=product_group.id,
                            external_id=product_group.external_id or product_group.name,
                            name=product_group.name,
                            modifiers=modifiers,
                            hide_modifications_by=hide_modifications_by,
                        )
                }
            )

        self.data["ProductGroup"] = list(product_groups.values())

    async def _load_attribute_groups(self) -> list[schemas.AttributeGroup]:
        attribute_groups: dict[int, schemas.AttributeGroup] = {}
        db_attribute_groups = await crud.get_attribute_groups(
            self.brand_id,
            sort=AttributeGroupSortEnum.FOR_EXPORT.value
        )
        for attribute_group in db_attribute_groups:
            if attribute_group.id in attribute_groups:
                continue

            attributes = {}
            for attribute in await crud.get_attributes(attribute_group.id):
                if attribute.id in attributes:
                    continue

                attributes[attribute.id] = schemas.Attribute(
                    id=attribute.id,
                    attribute_id=attribute.attribute_id,
                    external_id=attribute.external_id or attribute.attribute_id or
                                attribute.name,
                    name=attribute.name,
                    min=attribute.min,
                    max=attribute.max,
                    price_impact=attribute.price_impact,
                    is_available=attribute.is_available,
                    selected_by_default=attribute.selected_by_default,
                )

                translations = await self.get_translations(attribute)
                self.swap_languages_data(attributes[attribute.id], translations)
                if not translations:
                    continue

                if not self.translations.get("Attribute"):
                    self.translations["Attribute"] = {}
                self.translations["Attribute"][attribute.id] = [
                    schemas.Translation(
                        id=attribute.id,
                        external_id=field_name,
                        lang=lang,
                        name=value,
                    )
                    for lang, data in translations.items()
                    for field_name, value in data.items()
                ]

            attribute_groups[attribute_group.id] = schemas.AttributeGroup(
                id=attribute_group.id,
                attribute_group_id=attribute_group.attribute_group_id,
                external_id=attribute_group.external_id or attribute_group.id,
                name=attribute_group.name,
                min=attribute_group.min,
                max=attribute_group.max,
                position=attribute_group.position,
                attributes=list(attributes.values()),
            )

            translations = await self.get_translations(attribute_group)
            self.swap_languages_data(attribute_groups[attribute_group.id], translations)
            if not translations:
                continue

            if not self.translations.get("AttributeGroup"):
                self.translations["AttributeGroup"] = {}
            self.translations["AttributeGroup"][attribute_group.id] = [
                schemas.Translation(
                    id=attribute_group.id,
                    external_id=field_name,
                    lang=lang,
                    name=value,
                )
                for lang, data in translations.items()
                for field_name, value in data.items()
            ]

        self.data["AttributeGroup"] = list(attribute_groups.values())

        return list(attribute_groups.values())

    async def _load_attributes(self, attribute_groups: list[schemas.AttributeGroup]):
        attributes: list[tuple[str, schemas.Attribute]] = []
        for attribute_group in attribute_groups:
            for attribute in attribute_group.attributes:
                attributes.append(
                    (
                        attribute_group.external_id,
                        attribute,
                    )
                )

        self.data["Attribute"] = attributes

    async def _load_characteristics(self):
        characteristics: dict[int, schemas.Characteristic] = {}
        brand_characteristics = await StoreCharacteristic.get_list(
            brand_id=self.brand_id,
            is_deleted=False,
        )
        for characteristic in brand_characteristics:
            if characteristic.id in characteristics:
                continue

            characteristics[characteristic.id] = schemas.Characteristic(
                id=characteristic.id,
                external_id=characteristic.external_id or characteristic.name,
                name=characteristic.name,
                filter_type=characteristic.filter_type,
                is_hide=characteristic.is_hide,
                position=characteristic.position,
            )

            translations = await self.get_translations(characteristic)
            self.swap_languages_data(characteristics[characteristic.id], translations)
            if not translations:
                continue

            if not self.translations.get("Characteristic"):
                self.translations["Characteristic"] = {}
            self.translations["Characteristic"][characteristic.id] = [
                schemas.Translation(
                    id=characteristic.id,
                    external_id=field_name,
                    lang=lang,
                    name=value,
                )
                for lang, data in translations.items()
                for field_name, value in data.items()
            ]

        self.data["Characteristic"] = list(characteristics.values())

    async def _load_spot_prices(self):
        spot_prices = []
        stores = await Store.get_list(brand_id=self.brand_id)

        for store in stores:
            db_spot_prices = await StoreProductSpotPrice.get_list(store_id=store.id)

            for spot_price in db_spot_prices:
                product = await StoreProduct.get(spot_price.product_id)

                if product:
                    spot_prices.append(
                        schemas.ProductSpotPrice(
                            id=spot_price.id,
                            product_external_id=product.external_id or str(product.id),
                            store_external_id=store.external_id or str(store.id),
                            price=spot_price.price,
                            old_price=spot_price.old_price if spot_price.old_price
                            else None
                        )
                    )

        self.data["SpotPrice"] = spot_prices

    async def _load(self):
        start_time = time.time()

        await self._load_stores()
        await self._load_categories()
        await self._load_products()
        await self._load_product_groups()
        attribute_groups = await self._load_attribute_groups()
        await self._load_attributes(attribute_groups)
        await self._load_characteristics()
        await self._load_spot_prices()

        self.logger.debug(f"_load time: {(time.time() - start_time):.3f}")

    async def load(self) -> dict:
        try:
            await self._load()
        except Exception as e:
            raise e

        return self.data
