import asyncio
from datetime import datetime
from typing import Any, Type

from core.ext.adapters.base import AdapterType, BaseAdapter
from core.ext.exceptions import (
    ExportCheckerError, GenerationTemplateError, StoreExportError,
    StoreExportPostImportError, StoreExportUnknownError,
)
from core.ext.types import (
    ExternalAPITypeLiteral, OnStatusUpdatedType, ReturnType,
    StatusType, WarningsListType,
)
from db.models import Brand, Group
from .data_loader import BaseDataLoader, DataLoader

DEFAULT_CHECKER_TIMEOUT = 1.0


class StoreExporter:

    def __init__(
            self, brand_id: int,
            external_type: ExternalAPITypeLiteral,
            base_adapter_cls: Type[AdapterType] = BaseAdapter,
            data_loader_cls: Type[BaseDataLoader] = DataLoader,
    ):
        self.brand_id = brand_id
        self.external_type = external_type

        self.base_adapter_cls = base_adapter_cls
        self.data_loader_cls = data_loader_cls

        self.time_started: datetime | None = None
        self.time_finished: datetime | None = None

        self.status: StatusType = StatusType.NOT_STARTED
        self.warnings: WarningsListType = []

    async def start(self, on_status_update: OnStatusUpdatedType | None = None, **kwargs) -> ReturnType:
        self.time_started = datetime.utcnow()
        self.status: StatusType = StatusType.LOADING

        try:
            brand = await Brand.get(self.brand_id)
            group = await Group.get(brand.group_id)

            loader = self.data_loader_cls(self.brand_id, kwargs.get("lang"), group.lang)
            data = await loader.load()
            translations = loader.translations

            langs = loader.langs
            if loader.lang != loader.brand_lang:
                langs.append(loader.brand_lang)

            self.status: StatusType = StatusType.SAVING

            adapter_cls = self.base_adapter_cls.get_adapter(self.external_type)
            kwargs["brand_id"] = brand.id
            adapter = adapter_cls(brand=brand, group=group, import_or_export="export", **kwargs)
            result = await adapter.save_data(data, langs, loader.brand_lang, translations)
            # raise Exception("raise TEST ERROR...")

        except Exception as e:
            self.status: StatusType = StatusType.ERROR
            if on_status_update:
                time_passed = datetime.utcnow() - self.time_started if self.time_started else 0
                await on_status_update("error", time_passed, self.warnings)
            if not isinstance(e, StoreExportError):
                raise StoreExportUnknownError(original_error=str(e)) from e
            else:
                raise

        self.status: StatusType = StatusType.DONE
        self.time_finished = datetime.utcnow()

        return {
            "result": result,
            "status": self.status.value,
            "time_passed": self.time_finished - self.time_started
        }

    async def _checker(self, on_status_updated: OnStatusUpdatedType, timeout: float, ignore_errors: bool):
        while True:
            try:
                time_passed = datetime.utcnow() - self.time_started if self.time_started else 0
                await on_status_updated(self.status.value, time_passed, self.warnings)
            except Exception as e:
                if not ignore_errors:
                    raise ExportCheckerError() from e

            if self.status.value in ("done", "error"):
                break

            await asyncio.sleep(timeout)

        time_passed = datetime.utcnow() - self.time_started if self.time_started else 0
        await on_status_updated(self.status.value, time_passed, self.warnings)

    async def start_with_checker(
            self,
            on_status_updated: OnStatusUpdatedType,
            timeout: float = DEFAULT_CHECKER_TIMEOUT,
            ignore_errors: bool = True,
            **kwargs,
    ) -> ReturnType:
        results = await asyncio.gather(
            self.start(**kwargs),
            self._checker(on_status_updated, timeout, ignore_errors),
            return_exceptions=False,
        )

        return results[0]

    async def generate_template(self, **kwargs) -> Any:
        try:
            brand: Brand = await Brand.get(self.brand_id)
            group: Group = await Group.get(brand.group_id)
            langs_list = group.get_langs_list()

            data = {
                "Store": [],
                "Category": [],
                "Product": [],
                "ProductGroup": [],
                "Attribute": [],
                "AttributeGroup": [],
                "Characteristic": [],
            }

            adapter_cls = self.base_adapter_cls.get_adapter(self.external_type)
            adapter = adapter_cls(brand=brand, import_or_export="export", **kwargs)
            return await adapter.save_data(data, langs_list, group.lang)
        except Exception as e:
            raise GenerationTemplateError() from e

    async def post_import(self, **kwargs):
        brand: Brand = await Brand.get(self.brand_id)
        group: Group = await Group.get(brand.group_id)
        kwargs["group"] = group

        try:
            loader = self.data_loader_cls(self.brand_id, kwargs.get("lang"), group.lang)
            data = await loader.load()
            translations = loader.translations

            langs = loader.langs
            if loader.lang != loader.brand_lang:
                langs.append(loader.brand_lang)

            adapter_cls = self.base_adapter_cls.get_adapter(self.external_type)
            adapter = adapter_cls(brand=brand, import_or_export="export", **kwargs)
            return await adapter.save_data(data, langs, loader.brand_lang, translations)
        except Exception as e:
            raise StoreExportPostImportError() from e
