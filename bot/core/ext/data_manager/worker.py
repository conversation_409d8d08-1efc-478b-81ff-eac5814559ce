import logging

from config import DELAY_DATA_PORTER
from db import DBSession
from utils.processes_manager.background_worker import <PERSON>BackgroundWorker
from .db_funcs import get_data_porters
from .service import PorterService

logger = logging.getLogger()


class PorterWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "Data Porter"
    DEFAULT_TIMEOUT = DELAY_DATA_PORTER

    async def iteration(self):
        with DBSession():

            try:
                limit = PorterService.get_available_count()
                if limit < 1:
                    return

                porters = await get_data_porters(
                    limit, PorterService.get_running_porters_ids()
                )

                for porter in porters:
                    PorterService.add(porter)
            except Exception as error:
                logger.error(error, exc_info=True)
