from collections import defaultdict

from sqlalchemy import select

from core.ext.types import ActionType, StatusType
from db import db_func, sess
from db.models import DataPorter


@db_func
def get_data_porters(limit: int, run_ids: list[int]) -> list["DataPorter"]:
    stmt = (
        select(DataPorter)
        .where(
            DataPorter.status.not_in(
                (
                    StatusType.DONE,
                    StatusType.ERROR,
                )
            ),
            DataPorter.id.not_in(run_ids)
        )
        .order_by(DataPorter.time_created)
        .limit(limit)
    )

    porters = sess().scalars(stmt).all()

    brand_export_tasks = defaultdict(list)
    result = []

    for porter in porters:
        if porter.action_type == ActionType.EXPORT:
            brand_export_tasks[porter.brand_id].append(porter)
        else:
            result.append(porter)

    for brand_tasks in brand_export_tasks.values():
        if brand_tasks:
            oldest_task = min(brand_tasks, key=lambda x: x.time_created)
            result.append(oldest_task)

    return sorted(result, key=lambda x: x.time_created)
