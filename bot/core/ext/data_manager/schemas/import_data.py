from typing import Annotated, List

from fastapi import Form, HTTPException, UploadFile, status
from pydantic import BaseModel, Field, UUID4, validator

from core.ext import schemas
from core.ext.types import ExternalAPITypeLiteral, ExternalType, ExternalTypeLiteral


class ExternImportData(BaseModel):
    import_source: ExternalAPITypeLiteral = Field(description="source type for import data")

    prom_file_main_lang: str | None = Field(None, description="Prom main file language for import from prom")

    excel_file: str | UploadFile | None = Field(None, description="Excel file in base64 or UploadFile type")
    choice_file: str | UploadFile | None = Field(None, description="choice file in base64 or UploadFile type")
    menu_files: list[str | UploadFile] | None = Field(None, description="list menu files in base64 or UploadFile type")
    sheets_url: str | None = Field(None, description="Url on google sheets for import data")
    prom_url: str | None = Field(None, description="Url on prom xml for import data")
    incust_selected_stores: list[str] | None = Field(None, description="list uids incust stores to import")
    json_data: schemas.AdapterJSONData | None = Field(None, description="json data for import")

    callback_url: str | None = Field(None, description="Url response about the results")


class ImportResponse(BaseModel):
    import_id: UUID4 = Field(description="import request ID to check status")


class ExternImportExtData(BaseModel):
    import_source: Annotated[ExternalTypeLiteral, Form()]

    prom_file_main_lang: Annotated[str | None, Form(nullable=True)] = None

    sheets_url: Annotated[str | None, Form(nullable=True)] = None
    prom_url: Annotated[str | None, Form(nullable=True)] = None
    is_need_save_url: Annotated[bool | None, Form(nullable=True)] = True
    files: Annotated[list[UploadFile], None] = Form(None, nullable=True)

    poster_api_token: Annotated[str | None, Form(nullable=True)] = None
    poster_app_secret: Annotated[str | None, Form(nullable=True)] = None
    poster_skip_stores: Annotated[str | None, Form(nullable=True)] = None
    poster_skip_desc_product: Annotated[bool | None, Form(nullable=True)] = False
    poster_regex_to_skip_category: Annotated[str | None, Form(nullable=True)] = None
    poster_tips_sku: Annotated[str | None, Form(nullable=True)] = None
    incust_selected_stores: Annotated[List[str] | None, Form(nullable=True)] = None

    @validator('*', pre=True)
    def empty_str_to_none(cls, v):
        if v == '':
            return None
        return v

    @validator('import_source')
    def validate_import_source(cls, v):
        if not v:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Import source is required")
        return v

    @validator('sheets_url')
    def validate_sheets_url(cls, v, values):
        if values.get('import_source') == ExternalType.SHEETS.value and not v:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Google Sheets URL is required for Google Sheets import"
            )
        return v

    @validator('prom_url', 'prom_file_main_lang')
    def validate_prom(cls, v, values, field):
        if values.get('import_source') == ExternalType.PROM.value:
            if field.name == 'prom_url' and not v:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Prom URL is required for Prom import"
                )
            if field.name == 'prom_file_main_lang' and not v:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Prom file main language is required for Prom import"
                )
            if field.name == 'prom_file_main_lang' and v and len(v) != 2:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="code language is required"
                )
        return v

    @validator('files')
    def validate_file(cls, v, values):
        if values.get('import_source') in [ExternalType.EXCEL.value, ExternalType.CHOICE.value,
                                           ExternalType.MENU.value] and not v:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File is required for Excel or Choice or Menu import"
            )
        return v

    @validator('poster_api_token', 'poster_app_secret')
    def validate_poster(cls, v, values, field):
        if values.get('import_source') == ExternalType.POSTER.value:
            if not v:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail=f"{field.name} is required for Poster import"
                )
        return v

    @validator('incust_selected_stores')
    def validate_incust(cls, v, values):
        if values.get('import_source') == ExternalType.INCUST.value:
            if not v or len(v) == 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="At least one Incust store must be selected for Incust import"
                )
        return v

    class Config:
        arbitrary_types_allowed = True


class IncustStores(BaseModel):
    uuid_id: str
    name: str
    image_url: str | None = None
