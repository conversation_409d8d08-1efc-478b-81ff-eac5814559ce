from datetime import datetime

from pydantic import BaseModel, Field

from core.ext.schemas import AdapterJSONData
from core.ext.types import ActionType, ExternalAPIType, StatusTypeLiteral


class StatusResponse(BaseModel):
    uuid_id: str
    status: StatusTypeLiteral = Field(description="process status")

    start_time: datetime | None = Field(None, description="process start time")
    end_time: datetime | None = Field(None, description="process end time")
    time_processed: str | None = Field(
        None, description="how long did the process take"
    )
    url: str | None = Field(None, description="url for import or export files")
    json_data: AdapterJSONData | None = Field(
        None, description="result export json data"
    )
    excel_file: str | None = Field(None, description="excel file in base64 string")
    error_text: str | None = Field(None, description="error text")
    action_type: ActionType = Field(description="import action type")
    external_type: ExternalAPIType = Field(description="import external type")
