from pydantic import BaseModel, Field, UUID4, validator

from core.ext.types import ExternalAPITypeExportLiteral, ExternalType


class ExternExportData(BaseModel):
    export_source: ExternalAPITypeExportLiteral = Field(description="source type for export data")

    lang: str | None = Field(None, description="Language for result export data")
    user_lang: str | None = Field(None, description="User language for result response")

    sheets_url: str | None = Field(None, description="Url on google sheets for import data")
    callback_url: str | None = Field(None, description="Url response about the results")

    @validator('*', pre=True)
    def empty_str_to_none(cls, v):
        if v == '':
            return None
        return v

    @validator('export_source')
    def validate_import_source(cls, v):
        if not v:
            raise ValueError("Export source is required")
        return v

    @validator('sheets_url')
    def validate_sheets_url(cls, v, values):
        if values.get('import_source') == ExternalType.SHEETS.value and not v:
            raise ValueError("Sheets URL is required for Google Sheets import")
        return v


class ExportResponse(BaseModel):
    export_id: UUID4 = Field(description="Export request ID to check status")
    info: str | None = None
