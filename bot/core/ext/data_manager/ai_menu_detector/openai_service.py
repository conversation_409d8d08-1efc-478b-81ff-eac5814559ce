import asyncio
import base64
import json
from typing import Optional, Type, TypeVar, Union

import json_repair

from openai import OpenAIError, AsyncClient
from openai.types.chat import ChatCompletionMessageParam
from pydantic import BaseModel, ValidationError

from .utils import debug

EMBEDDING_MAX_TOKENS_PER_INPUT = 8191

T = TypeVar("T", bound=BaseModel)


class OpenAIResponseError(Exception):
    pass


class DataExtractionError(Exception):
    pass


async def execute(
        client: AsyncClient,
        prompt_messages: list[ChatCompletionMessageParam],
        max_tokens: int = 1000,
        temperature: float = 0.1,
        frequency_penalty: float = 0,
        presence_penalty: float = 0,
        top_p: float = 1,
        model_name: str = 'gpt-4o',
        extra_debug_data: Optional[dict] = None,
) -> str:
    debug(
        'Start executing OpenAI request.', {
            'max_tokens': max_tokens,
            'temperature': temperature,
            'frequency_penalty': frequency_penalty,
            'presence_penalty': presence_penalty,
            'top_p': top_p,
            'model_name': model_name,
            **(extra_debug_data or {})
        }
    )

    attempt = 0
    while True:
        try:
            chat_completion = await client.chat.completions.create(
                messages=prompt_messages,
                model=model_name,
                frequency_penalty=frequency_penalty,
                # max_tokens=max_tokens,
                presence_penalty=presence_penalty,
                temperature=temperature,
                response_format={
                    'type': 'json_object'
                },
                top_p=top_p,
            )
            break
        except OpenAIError as e:
            attempt += 1

            await asyncio.sleep(15)

            if attempt > 3:
                raise e

    output_text = chat_completion.choices[0].message.content

    debug(
        'End executing OpenAI request.', {
            'input_tokens': chat_completion.usage.prompt_tokens,
            'output_tokens': chat_completion.usage.completion_tokens,
            'output_text': output_text,
            **(extra_debug_data or {})
        }
    )

    return output_text


async def extract_data(
        client: AsyncClient,
        model_class: Type[T],
        prompt_messages: list[ChatCompletionMessageParam],
        max_tokens: int = 1000,
        temperature: float = 0.1,
        frequency_penalty: float = 0,
        presence_penalty: float = 0,
        top_p: float = 1,
        model_name: str = 'gpt-3.5-turbo-1106',
        extra_debug_data: Optional[dict] = None,
) -> T:
    attempt = 0
    while True:
        try:
            response = await execute(
                client=client,
                prompt_messages=prompt_messages,
                max_tokens=max_tokens,
                temperature=temperature,
                frequency_penalty=frequency_penalty,
                presence_penalty=presence_penalty,
                top_p=top_p,
                model_name=model_name,
                extra_debug_data=extra_debug_data,
            )

            try:
                data = json_repair.loads(response)
            except json.JSONDecodeError:
                raise DataExtractionError('Failed to extract data from OpenAI response. Json decode error.')

            try:
                model = model_class.validate(data)
            except ValidationError as e:
                if model_class is MenuBlockSecondItems:
                    data['items'] = data['items'][:-1]
                    try:
                        model = model_class.validate(data)
                    except ValidationError as e:
                        raise DataExtractionError(
                            'Failed to validate extracted data from OpenAI response. Errors: %(errors)s' % {
                                'errors': e.json(),
                                'data': data,
                            }
                        )
                else:
                    raise DataExtractionError(
                        'Failed to validate extracted data from OpenAI response. Errors: %(errors)s' % {
                            'errors': e.json(),
                            'data': data,
                        }
                    )
            break
        except DataExtractionError as e:
            if attempt > 3:
                raise e
            attempt += 1

    return model


class MenuItemVariantPrice(BaseModel):
    name: Optional[str]
    price: Optional[Union[float, str]]  # str here is as a tmp fix


class MenuItemAddon(BaseModel):
    name: str
    price: Optional[float]


class MenuBlockAddon(BaseModel):
    name: str
    price: Optional[float]


class MenuItem(BaseModel):
    name: Optional[str]
    description: Optional[str]
    variants_prices: list[MenuItemVariantPrice]
    addons: list[MenuItemAddon]


class MenuBlock(BaseModel):
    name: str
    description: Optional[str]
    variants: list[str]
    addons: list[MenuBlockAddon]
    items: list[MenuItem]


class Menu(BaseModel):
    restaurant_name: Optional[str]
    language: Optional[str]
    currency: Optional[str]
    blocks: list[MenuBlock]


async def extract_menu_from_image(
        client: AsyncClient,
        image: bytes
) -> Menu:
    prompt_messages = [
        {
            'role': 'system',
            'content': """
You are a system that extracts a menu from an image.
Return a JSON object with the following structure:
- restaurant_name (null or string): name of the restaurant;
- currency (null or string): currency as ISO Currency Code;
- blocks (list of objects): list of menu blocks (like main dishes, sides, salads, etc):
    * name (string): name of the block;
    * description (null or string): description of the block;
    * addons (list of objects): list of addons related to the entire block:
        - name (string): name of addon;
        - price (null or float): the price of addon;
    * variants (list of string): a list of product/dish variants (it can be size), if only one variant - name it "Main";
    * items (list of objects): list of products/dishes in the block:
        1. name (string): name of product/dish;
        2. description (null or string): description of product/dish;
        5. variants_prices (list of objects): list of variant prices of the product/dish, prices can be divided by spaces or slash:
            - name (string): name of variant;
            - price (null or float): the price of product in specific variant (you have to find a numbers with dot or comma as decimal separator otherwise its different prices);
        6. addons (list of objects): list of addons for the product/dish (they can be common for block):
            - name (string): name of addon;
            - price (null or float): the price of addon;

Variants are different variations of product/dish by size (small, medium, large, whole, half, etc).
For "null or float" values, then value is not float use null.
"""
        },
        {
            'role': 'user',
            'content': [
                {
                    'type': 'image_url',
                    'image_url': {
                        'url': 'data:image/jpeg;base64,%s' % (base64.b64encode(image).decode('ascii'),),
                    }
                }
            ],
        },
    ]

    menu: Menu = await extract_data(
        client=client,
        model_class=Menu,
        prompt_messages=prompt_messages,
        max_tokens=4096,
        temperature=0,
        model_name='gpt-4o',
    )

    return menu


class MenuBlockSecond(BaseModel):
    name: str
    description: Optional[str]
    variants: list[str]
    addons: list[MenuBlockAddon]


class MenuSecond(BaseModel):
    restaurant_name: Optional[str]
    language: Optional[str]
    currency: Optional[str]
    blocks: list[MenuBlockSecond]


class MenuBlockSecondItems(BaseModel):
    items: list[MenuItem]


async def extract_menu_blocks_from_image(
        client: AsyncClient,
        image: bytes,
        extra_debug_data: Optional[dict] = None
) -> MenuSecond:
    prompt_messages = [
        {
            'role': 'system',
            'content': """
You are a system that extracts a menu data from an image.

Return a JSON object with the following structure and only next fields:
- restaurant_name (null or string): name of the restaurant;
- currency (null or string): currency as ISO Currency Code;
- language (null or string): language of the menu (ISO 639-1 code);
- blocks (list of objects): list of menu blocks (like main dishes, sides, salads, etc):
- blocks.name (string): name of the block;
- blocks.description (null or string): description of the block;
- blocks.addons (list of objects): list of addons related to the entire block:
- blocks.addons.name (string): name of addon;
- blocks.addons.price (null or float): the price of addon;
- blocks.variants (list of string): name of dish variants, it can depend on size for example, if only one variant - name it "Main", if here are two variants of prices without title - name them "Small" and "Large";

Additional instructions and notes:
Blocks are composed from dishes/products.
Blocks had names as general dishes categories (main, deserts, drinks, etc).
If block name looks like specific dish and only one dish in the block then skip the block.
For "null or float" values, then value is not float use null.
If there are some products with no named block, then add them to the block with name "UNDEFINED".
If no data in the image or image is not clear enough just return next JSON object:
{
    "restaurant_name": null,
    "currency": null,
    "language": null,
    "blocks": []
}
"""
        },
        {
            'role': 'user',
            'content': [
                {
                    'type': 'image_url',
                    'image_url': {
                        'url': 'data:image/jpeg;base64,%s' % (base64.b64encode(image).decode('ascii'),),
                    }
                }
            ],
        },
    ]

    menu: MenuSecond = await extract_data(
        client=client,
        model_class=MenuSecond,
        prompt_messages=prompt_messages,
        max_tokens=4096,
        temperature=0,
        model_name='gpt-4o',
        extra_debug_data=extra_debug_data,
    )

    return menu


async def extract_menu_block_items_from_image(
        client: AsyncClient,
        image: bytes,
        block: MenuBlockSecond,
        extra_debug_data: Optional[dict] = None
) -> MenuBlockSecondItems:
    prompt_messages = [
        {
            'role': 'system',
            'content': """
You are a system that extracts a block of the menu data from an image.

Find the block "%(block_name)s".
Return a JSON object with the following structure:
- items (list of objects): list of products/dishes in the block:
- items.name (null or string): name of product/dish;
- items.description (null or string): description of product/dish;
- items.variants_prices (list of objects): list of variant prices of the product/dish, prices can be divided by spaces or slash:
- items.variants_prices.name (string): name of variant, if only one variant - name it "Main" in menu language, if here are two variants of prices without title - name them "Small" and "Large" in menu language;
- items.variants_prices.price (null or float): the price of product in specific variant;
- items.addons (list of objects): list of addons related to the product/dish:
- items.addons.name (string): name of addon;
- items.addons.price (null or float): the price of addon;

Additional instructions and notes:
Any price can be float or null if it is not present or value is not numeric.
Numbers can be divided by spaces or slash.
For "null or float" values, then value is not float use null.
Price variants are different variations of product/dish by size (small, medium, large, whole, half, etc).
Large block can be interrupted by other smaller blocks.
Block is not always a rectangle, it can have separate parts apart of the main block.
""" % {
                'block_name': block.name,
                'prices_count': len(block.variants),
                'prices_names': ', '.join([f'"{variant}"' for variant in block.variants]),
            }
        },
        {
            'role': 'user',
            'content': [
                {
                    'type': 'image_url',
                    'image_url': {
                        'url': 'data:image/jpeg;base64,%s' % (base64.b64encode(image).decode('ascii'),),
                    }
                }
            ],
        },
    ]

    menu: MenuBlockSecondItems = await extract_data(
        client=client,
        model_class=MenuBlockSecondItems,
        prompt_messages=prompt_messages,
        max_tokens=4096,
        temperature=0,
        model_name='gpt-4o',
        extra_debug_data=extra_debug_data,
    )

    return menu


class DetectRotationResult(BaseModel):
    rotation: int


async def detect_rotation(
        client: AsyncClient,
        image: bytes,
        extra_debug_data: Optional[dict] = None
) -> int:
    prompt_messages = [
        {
            'role': 'system',
            'content': """
You are a system that detects if an image need rotation so the text can be readable from top to bottom, left to right.
User will send you an image.
Return a JSON object with the following structure:
- rotation (int): 0 - no rotation, 1 - 90 degrees clockwise, 2 - 180 degrees, 3 - 90 degrees counterclockwise.
"""
        },
        {
            'role': 'user',
            'content': [
                {
                    'type': 'image_url',
                    'image_url': {
                        'url': 'data:image/jpeg;base64,%s' % (base64.b64encode(image).decode('ascii'),),
                    }
                }
            ],
        },
    ]

    result: DetectRotationResult = await extract_data(
        client=client,
        model_class=DetectRotationResult,
        prompt_messages=prompt_messages,
        max_tokens=4096,
        temperature=0,
        model_name='gpt-4o',
        extra_debug_data=extra_debug_data,
    )

    return result.rotation


class GenerateSizeCharacteristic(BaseModel):
    title: str


async def generate_size_characteristic(
        client: AsyncClient,
        values: list[str],
        language: str,
) -> str:
    prompt_messages = [
        {
            'role': 'system',
            'content': """
You are a system that generates a title for characteristic in restaurant menu.
User will send you a list of values.
Return a JSON object with the following structure:
- title (string): title for characteristic.

Title is a name of the characteristic what is common for all values. Example: "Size".
Be short and clear.
Return it in the language: %(language)s.
""" % {
                'language': language,
            }
        },
        {
            'role': 'user',
            'content': '\n'.join(values),
        },
    ]

    result: GenerateSizeCharacteristic = await extract_data(
        client=client,
        model_class=GenerateSizeCharacteristic,
        prompt_messages=prompt_messages,
        max_tokens=4096,
        temperature=0,
        model_name='gpt-4o',
    )

    return result.title


async def extract_menu_from_text(
        client: AsyncClient,
        text: str
) -> Menu:
    """
    Deprecated.
    """

    prompt_messages = [
        {
            'role': 'system',
            'content': """
You are a system that extracts a menu from an image.
User will send you text extracted from image.
Return a JSON object with the following structure:
- items: a list of objects with the following structure:
    * name: a string;
    * price: a string or null;
"""
        },
        {
            'role': 'user',
            'content': text,
        },
    ]

    menu: Menu = await extract_data(
        client=client,
        model_class=Menu,
        prompt_messages=prompt_messages,
        max_tokens=4096,
        temperature=0,
    )

    return menu
