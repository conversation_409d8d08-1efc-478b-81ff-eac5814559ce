import io
from typing import Optional

import aiofiles
from PIL import Image
from openai import Async<PERSON><PERSON>
from pdf2image import convert_from_bytes

from core.ext.schemas import (
    AdapterJSONData, AttributeAPI, AttributeGroupAPI, CategoryAPI, CharacteristicAPI,
    CharacteristicValueAPI,
    ProductAPI, ProductGroup,
    StoreAPI,
    ValueAndTranslationAPI,
)
from schemas import CharacteristicFilterType
from . import openai_service
from .utils import debug


async def detect_menu(
        openai_key: str, file_paths: list[str], extra_debug_data: Optional[dict] = None
) -> AdapterJSONData:
    client = AsyncClient(api_key=openai_key)
    menu = await _extract_menu_as_intern_object(client, file_paths, extra_debug_data)

    return await _to_external_object(client=client, menu=menu)


async def _convert_pdf_to_images(file_path: str) -> list[bytes]:
    contents = []
    async with aiofiles.open(file_path, 'rb') as file:
        images = convert_from_bytes(await file.read())
        for image in images:
            # convert to png
            image = image.convert('RGB')
            bytes_array = io.BytesIO()
            image.save(bytes_array, format='PNG')
            contents.append(bytes_array.getvalue())
    return contents


async def _rotate_image(content: bytes, rotation: int) -> bytes:
    image = Image.open(io.BytesIO(content))
    rotation_angle = {
        0: 0,
        1: 90,
        2: 180,
        3: 270
    }
    image = image.rotate(rotation_angle[rotation])
    bytes_array = io.BytesIO()
    image.save(bytes_array, format='PNG')
    return bytes_array.getvalue()


async def _extract_menu_as_intern_object(
        client: AsyncClient,
        file_paths: list[str],
        extra_debug_data: Optional[dict] = None
) -> openai_service.Menu:
    debug('Opening files.')

    contents = []
    for file_path in file_paths:
        if file_path.endswith('.pdf'):
            contents.extend(await _convert_pdf_to_images(file_path))
        else:
            async with aiofiles.open(file_path, 'rb') as file:
                contents.append(await file.read())

    menu = None
    last_block = None
    for i, content in enumerate(contents):
        debug(
            'Extracting menu from content %(index)s/%(total)s.' % {
                'index': i + 1,
                'total': len(contents)
            }
        )

        rotation = await openai_service.detect_rotation(client, content)
        if rotation != 0:
            content = await _rotate_image(content, rotation)

        local_menu = await _extract_menu_with_blocks_from_content(
            client, content, last_block, extra_debug_data
        )

        if menu is None:
            menu = local_menu
        else:
            if menu.restaurant_name is None and local_menu.restaurant_name is not None:
                menu.restaurant_name = local_menu.restaurant_name
            if menu.currency is None and local_menu.currency is not None:
                menu.currency = local_menu.currency
            menu.blocks.extend(local_menu.blocks)

        if len(menu.blocks) > 0:
            _last_block = menu.blocks[-1]
            last_block = openai_service.MenuBlockSecond(
                name=_last_block.name,
                description=_last_block.description,
                variants=_last_block.variants,
                addons=_last_block.addons,
            )
        else:
            last_block = None

    debug(
        "Menu", {
            "file_paths": file_paths,
            "menu": menu.dict()
        }
    )

    return menu


async def _extract_menu_with_blocks_from_content(
        client: AsyncClient,
        content: bytes,
        last_block: Optional[openai_service.MenuBlockSecond] = None,
        extra_debug_data: Optional[dict] = None,
) -> openai_service.Menu:
    menu = await openai_service.extract_menu_blocks_from_image(
        client,
        content,
        extra_debug_data,
    )

    new_menu = openai_service.Menu(
        restaurant_name=menu.restaurant_name,
        language=menu.language,
        currency=menu.currency,
        blocks=[]
    )
    blocks: list[openai_service.MenuBlockSecond] = menu.blocks
    if len(blocks) == 0 and last_block is not None:
        blocks = [last_block]
    elif len(blocks) > 0 and last_block is not None and any(
            [block.name.lower() == 'undefined' for block in blocks]
    ):
        _blocks = []
        for block in blocks:
            if block.name.lower() == 'undefined':
                _blocks.append(last_block)
            else:
                _blocks.append(block)
        blocks = _blocks
    elif len(blocks) == 0 and last_block is None:
        return new_menu

    for block in blocks:
        debug(
            'Extracting block %(block_name)s.' % {
                'block_name': block.name
            }
        )

        new_block = await openai_service.extract_menu_block_items_from_image(
            client, content, block, extra_debug_data
        )

        # additional preparing
        new_items = []
        for item in new_block.items:
            if item.name is None:
                continue
            for variant in item.variants_prices:
                if type(variant.price) == str:
                    variant.price = None
            new_items.append(item)
        new_block.items = new_items

        if len(new_block.items) == 1 and block.name == new_block.items[0].name:
            block.name = 'UNDEFINED'

        new_menu.blocks.append(
            openai_service.MenuBlock(
                name=block.name,
                description=block.description,
                variants=block.variants,
                addons=block.addons,
                items=new_block.items
            )
        )

    return new_menu


def _slugify(text: str) -> str:
    text = text.lower()

    extra_latin_chars = {
        'ą': 'a',
        'ć': 'c',
        'ę': 'e',
        'ł': 'l',
        'ń': 'n',
        'ó': 'o',
        'ś': 's',
        'ź': 'z',
        'ż': 'z',
        'î': 'i',
        # umlauts
        'ä': 'a',
        'ë': 'e',
        'ï': 'i',
        'ö': 'o',
        'ü': 'u',
        'ÿ': 'y',
        # special chars
        'ß': 'ss',
        'æ': 'ae',
        'œ': 'oe',
        'č': 'c',
        'ž': 'z',
        'ý': 'y',
        'š': 's',
    }
    for k, v in extra_latin_chars.items():
        text = text.replace(k, v)

    cyrillic_to_latin = {
        'а': 'a',
        'б': 'b',
        'в': 'v',
        'г': 'g',
        'д': 'd',
        'е': 'e',
        'ё': 'yo',
        'ж': 'zh',
        'з': 'z',
        'и': 'i',
        'й': 'y',
        'к': 'k',
        'л': 'l',
        'м': 'm',
        'н': 'n',
        'о': 'o',
        'п': 'p',
        'р': 'r',
        'с': 's',
        'т': 't',
        'у': 'u',
        'ф': 'f',
        'х': 'kh',
        'ц': 'ts',
        'ч': 'ch',
        'ш': 'sh',
        'щ': 'shch',
        'ъ': '',
        'ы': 'y',
        'ь': '',
        'э': 'e',
        'ю': 'yu',
        'я': 'ya',
        'ї': 'yi',
        'і': 'i',
        'є': 'ye',
        'ґ': 'g',
        'ў': 'u',
    }
    for k, v in cyrillic_to_latin.items():
        text = text.replace(k, v)

    text = text.lower().replace(' ', '-')
    return text


async def _to_external_object(
        client: AsyncClient,
        menu: openai_service.Menu
) -> AdapterJSONData:
    store = StoreAPI(
        id=None,
        external_id='store',
        image_url=None,
        position=None,
        latitude=None,
        longitude=None,
        distance=0,
        polygon=None,
        currency=menu.currency if menu.currency else 'USD',
        custom_fields=[],
        city=None,
        name=ValueAndTranslationAPI(
            value=menu.restaurant_name,
            translations=None,
        ),
        description=None,
        ai_description=None,
    )
    characteristics_dict = {}

    size_characteristic_index = 1
    categories = {}
    products_dict = {}
    product_groups = []
    product_groups_external_ids = []
    attribute_groups = {}
    attributes = {}
    product_position_index = 1
    category_position_index = 1
    ag_position_index = 1
    for block in menu.blocks:
        if len(block.items) == 0:
            continue

        block_slug = _slugify(block.name)
        if block.name == 'UNDEFINED':
            category = None
        elif block_slug not in categories:
            category = CategoryAPI(
                id=None,
                external_id=block_slug,
                is_default=False,
                position=category_position_index,
                image_url=None,
                filters=[],
                stores_external_ids=['store'],
                father_category_id=None,
                excel_position=None,
                name=ValueAndTranslationAPI(
                    value=block.name,
                    translations=None,
                ),
            )
            categories[block_slug] = category
        else:
            category = categories[block_slug]

        block_addons = block.addons

        attribute_group_items = {}
        attribute_group = None
        if len(block_addons) > 0:
            for addon in block_addons:
                if attribute_group is None:
                    attribute_group_external_id = block_slug + '_attribute_group'
                    if attribute_group_external_id not in attribute_groups:
                        attribute_group = AttributeGroupAPI(
                            id=None,
                            external_id=attribute_group_external_id,
                            attribute_group_id=attribute_group_external_id,
                            min=1,
                            max=None,
                            position=ag_position_index,
                            excel_position=None,
                            name=ValueAndTranslationAPI(
                                value=block.name + ' Addons',
                                translations=None,
                            ),
                        )
                        attribute_groups[attribute_group_external_id] = attribute_group
                        ag_position_index += 1
                    else:
                        attribute_group = attribute_groups[attribute_group_external_id]
                attribute_external_id = (attribute_group.external_id + '_addon_' +
                                         _slugify(
                    addon.name
                ))
                if attribute_external_id not in attribute_group_items:
                    attribute_group_items[attribute_external_id] = AttributeAPI(
                        id=None,
                        external_id=attribute_external_id,
                        attribute_id=attribute_external_id,
                        min=1,
                        max=None,
                        price_impact=addon.price if addon.price else 0,
                        is_available=True,
                        selected_by_default=False,
                        name=ValueAndTranslationAPI(
                            value=addon.name,
                            translations=None,
                        ),
                        attribute_group_id=attribute_group.external_id,
                    )

        attributes.update(attribute_group_items)

        for item in block.items:
            product_external_id_prefix = _slugify(item.name)

            characteristics_values_names = [variant_price.name for variant_price in
                                            item.variants_prices]
            characteristics_values_names = [v for v in characteristics_values_names if
                                            v is not None and v != '']
            if len(characteristics_values_names) == 0:
                continue

            characteristics_values_key = tuple(characteristics_values_names)
            if len(
                    item.variants_prices
            ) > 1 and characteristics_values_key not in characteristics_dict:
                size_title = await openai_service.generate_size_characteristic(
                    client=client,
                    values=characteristics_values_names,
                    language=menu.language if menu.language else 'en',
                )
                size_characteristic = CharacteristicAPI(
                    id=None,
                    external_id='size_' + str(size_characteristic_index),
                    is_hide=False,
                    position=None,
                    name=ValueAndTranslationAPI(
                        value=size_title,
                        translations=None,
                    ),
                    filter_type=CharacteristicFilterType.VALUE.value,
                )
                characteristics_dict[characteristics_values_key] = size_characteristic
                size_characteristic_index += 1
            else:
                size_characteristic = characteristics_dict.get(
                    characteristics_values_key
                )

            product_group = None
            product_group_external_id = product_external_id_prefix + '_group'
            if len(
                    item.variants_prices
            ) > 1 and product_group_external_id not in product_groups_external_ids:
                product_group = ProductGroup(
                    external_id=product_group_external_id,
                    name=item.name,
                    modifiers=[
                        size_characteristic.external_id] if size_characteristic else [],
                    hide_modifications_by=[
                        size_characteristic.external_id] if size_characteristic else [],
                )
                product_groups.append(product_group)
                product_groups_external_ids.append(product_group_external_id)

            variant_index = 1
            for variant_price in item.variants_prices:
                category_suffix = 'undefined' if category is None else (
                    category.external_id)
                product_external_id = (product_external_id_prefix + '_' +
                                       category_suffix + '_' + str(
                    variant_index
                ))
                if (category and variant_price.price is None and product_external_id
                        in products_dict):
                    products_dict[product_external_id].categories_external_ids.append(
                        category.external_id
                    )
                    products_dict[product_external_id].categories_external_ids = list(
                        set(products_dict[product_external_id].categories_external_ids)
                    )
                    continue
                elif variant_price.price is None:
                    continue

                if category and product_external_id in products_dict:
                    products_dict[product_external_id].categories_external_ids.append(
                        category.external_id
                    )
                    products_dict[product_external_id].categories_external_ids = list(
                        set(products_dict[product_external_id].categories_external_ids)
                    )
                else:
                    product = ProductAPI(
                        id=None,
                        product_id=None,
                        product_group_id=product_group_external_id if product_group
                        else None,
                        external_id=product_external_id,
                        is_available=True,
                        position=product_position_index,
                        image_url=None,
                        gallery_items=None,
                        price=variant_price.price,
                        old_price=0,
                        buy_min_quantity=1,
                        spots_prices=None,
                        is_weight=False,
                        weight_unit=None,
                        stores_external_ids=['store'],
                        attribute_groups_external_ids=[
                            attribute_group.external_id] if attribute_group else [],
                        categories_external_ids=[
                            category.external_id] if category else [],
                        floating_sum_value=None,
                        product_type_info_custom_fields=None,
                        product_type_liqpay_custom_fields=None,
                        type="goods",
                        pti_info_link=None,
                        liqpay_id=None,
                        liqpay_unit_name=None,
                        liqpay_codifier=None,
                        liqpay_tax_list=None,
                        need_auth=False,
                        floating_qty_enabled=False,
                        excel_position=None,
                        name=ValueAndTranslationAPI(
                            value=item.name,
                            translations=None,
                        ),
                        description=ValueAndTranslationAPI(
                            value=item.description,
                            translations=None,
                        ) if item.description else None,
                        characteristics=[
                            CharacteristicValueAPI(
                                external_id=size_characteristic.external_id,
                                value=ValueAndTranslationAPI(
                                    value=variant_price.name,
                                    translations=None,
                                )
                            )
                        ] if size_characteristic else None,
                        pti_info_text=None,
                    )
                    products_dict[product_external_id] = product
                product_position_index += 1
                variant_index += 1

        category_position_index += 1

    data = AdapterJSONData(
        stores=[store],
        products=list(products_dict.values()),
        product_groups=product_groups,
        categories=list(categories.values()),
        attribute_groups=list(attribute_groups.values()),
        attributes=list(attributes.values()),
        characteristics=list(characteristics_dict.values()),
    )

    return data
