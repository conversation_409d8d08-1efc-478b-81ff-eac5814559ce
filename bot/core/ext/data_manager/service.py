import asyncio
import base64
import json
import logging
import os
import tempfile

import aiofiles
from aiohttp import ClientSession
from psutils.exceptions import ErrorWithTextVariable
from starlette.datastructures import UploadFile

from config import (
    DATA_PORTER_LIMIT, P4S_API_URL,
    STORE_BRAND_STATIC_PATH_FOR_EXCEL,
)
from core.ext.exceptions import (
    StoreExportUnknownError,
    StoreImportUnknownError,
)
from core.ext.schemas import AdapterJSONData
from core.ext.types import (
    ActionType, ExternalAPIType, ExternalAPITypeLiteral,
    StatusType,
)
from core.media_manager import media_manager
from db import DBSession
from db.models import DataPorter
from loggers import J<PERSON>NLogger
from utils.date_time import utcnow
from utils.google import get_sheet_url
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from .exporter import StoreExporter
from .functions import make_import_error_text
from .importer import StoreImporter
from .importer.importer import get_import_data
from .schemas import ExternImportData, StatusResponse
from ...helpers import calc_time_processed


def save_temp_file(file: UploadFile | str, suffix: str) -> str:
    if isinstance(file, str):
        mode = 'w'
        content = file
    else:
        mode = 'wb'
        content = file.file.read()

    with tempfile.NamedTemporaryFile(
            delete=False, suffix=f".{suffix}", mode=mode
    ) as temp_file:
        if mode == 'w':
            json.dump(json.loads(content), temp_file)
        else:
            temp_file.write(content)
        return temp_file.name


class PorterService:
    _tasks: dict[int, asyncio.Task] = {}
    _errors: dict[int, int] = {}
    _retry_limit: int = 3
    count_available: int

    @classmethod
    def is_base64(cls, base64_str: str) -> bool:
        try:
            return base64.b64encode(
                base64.b64decode(base64_str.encode("utf-8"))
            ).decode("utf-8") == base64_str
        except Exception as error:
            logging.info(error)
            # raise ValueError("input data base64 not valid") from error
            ...
        return False

    @classmethod
    async def save_excel_file(
            cls, external_type: ExternalAPITypeLiteral,
            data: ExternImportData,
            brand_id: int,
    ) -> str:
        if external_type == ExternalAPIType.EXCEL.value:
            excel_file = data.excel_file
        elif external_type == ExternalAPIType.CHOICE.value:
            excel_file = data.choice_file
        else:
            raise ValueError("Unsupported external type")

        if cls.is_base64(excel_file):
            excel_file_data = base64.b64decode(excel_file.encode("utf-8"))
        elif isinstance(excel_file, UploadFile):
            excel_file_data = await excel_file.read()
        else:
            raise ValueError("Unsupported file format")

        path_to_save = os.path.join(STORE_BRAND_STATIC_PATH_FOR_EXCEL, str(brand_id))
        os.makedirs(os.path.join(path_to_save, "documents"), exist_ok=True)
        file_name = "".join(str(utcnow().timestamp()).split(".")) + ".xlsx"
        excel_file_path = os.path.join(path_to_save, file_name)

        async with aiofiles.open(excel_file_path, "wb") as dest:
            await dest.write(excel_file_data)
        return excel_file_path

    @classmethod
    async def excel_file_to_base64(cls, file_path: str) -> str:
        async with aiofiles.open(file_path, "rb") as excel_file:
            data = await excel_file.read()
            excel_file_data = base64.b64encode(data).decode("utf-8")

        return excel_file_data

    @classmethod
    async def get_source_data(
            cls, external_type: ExternalAPITypeLiteral,
            data: dict | ExternImportData,
            brand_id: int | None = None,
    ) -> str | list[str | UploadFile] | None:
        match external_type:
            case ExternalAPIType.EXCEL.value | ExternalAPIType.CHOICE.value:
                if isinstance(data, ExternImportData):
                    return await cls.save_excel_file(external_type, data, brand_id)
                return data.get("excel_file")
            case ExternalAPIType.SHEETS.value:
                if isinstance(data, ExternImportData):
                    return data.sheets_url
                return data.get("sheets")
            case ExternalAPIType.PROM.value:
                if isinstance(data, ExternImportData):
                    return data.prom_url
                return data.get("prom_url")
            case ExternalAPIType.INCUST.value:
                if isinstance(data, ExternImportData):
                    selected_stores = data.incust_selected_stores
                else:
                    selected_stores = data.get("incust_selected_stores")
                return json.dumps(selected_stores)
            case ExternalAPIType.JSON.value:
                return json.dumps(data.json_data.dict())
            case ExternalAPIType.MENU.value:
                if isinstance(data, ExternImportData):
                    return json.dumps(
                        [url.replace(P4S_API_URL + "/", "").strip() for url in
                         data.menu_files]
                    )
            case _:
                return None

    @classmethod
    def get_source_kwargs(
            cls, external_type: ExternalAPITypeLiteral, source_data: str | None
    ) -> dict:
        kwargs = {}
        match external_type:
            case ExternalAPIType.EXCEL.value | ExternalAPIType.CHOICE.value:
                kwargs.update(excel_file=source_data)
            case ExternalAPIType.SHEETS.value:
                kwargs.update(sheets=source_data)
            case ExternalAPIType.PROM.value:
                kwargs.update(prom_url=source_data)
            case ExternalAPIType.INCUST.value:
                selected_stores = json.loads(source_data) if source_data else []
                kwargs.update(selected_stores=selected_stores)
            case ExternalAPIType.JSON.value:
                json_data = AdapterJSONData.parse_obj(
                    json.loads(source_data)
                ) if source_data else None
                kwargs.update(json_data=json_data)
            case ExternalAPIType.MENU.value:
                input_files = json.loads(source_data) if source_data else []
                kwargs.update(input_files=input_files)
            case _:
                pass
        return kwargs

    @classmethod
    def get_available_count(cls):
        done_tasks = []
        for porter_id, task in cls._tasks.items():
            if task.done():
                done_tasks.append(porter_id)
        for porter_id in done_tasks:
            cls.remove(porter_id)
        return DATA_PORTER_LIMIT - len(cls._tasks)

    @classmethod
    def get_running_porters_ids(cls) -> list[int]:
        return list(cls._tasks.keys())

    @classmethod
    def add(cls, porter: DataPorter):
        if porter.id in cls._tasks:
            task = cls._tasks[porter.id]
            if task.done():
                cls.remove(porter.id)
            else:
                return

        if porter.action_type == ActionType.IMPORT:
            task = asyncio.create_task(cls.import_task(porter.id))
        elif porter.action_type == ActionType.EXPORT:
            task = asyncio.create_task(cls.export_task(porter.id))
        else:
            task = None

        if task:
            cls._tasks[porter.id] = task

    @classmethod
    def remove(cls, porter_id: int):
        cls._tasks.pop(porter_id, None)

    @classmethod
    async def import_task(cls, porter_id: int):
        with DBSession():
            porter, brand, group, user, timezone = await get_import_data(porter_id)

        importer = StoreImporter(porter_id, porter.external_type)
        error_text = None

        result = None
        try:
            result = await importer.start(
                lang=porter.lang,
                user_lang=porter.user_lang,
                prom_file_main_lang=porter.prom_file_main_lang,
                **cls.get_source_kwargs(porter.external_type, porter.source_data),
            )
        except ErrorWithTextVariable as e:
            with DBSession():
                porter = await DataPorter.get(porter_id)
                logger = JSONLogger(
                    "porter.import", {
                        "data_porter": {
                            "id": porter.id,
                            "uuid_id": porter.uuid_id,
                        }
                    }
                )

                try:
                    error_text = await make_import_error_text(
                        e, porter.lang, porter.user_lang
                    )

                except Exception as make_text_error:
                    logger.error(make_text_error, exc_info=True)

                    error_text = await f("store import unknown error", porter.lang)

                if e.text_variable == "store import unknown error":
                    await cls.check_and_send_error_to_admins(e, importer, porter)

        else:
            cls._errors.pop(porter_id, None)
            with DBSession():
                porter = await DataPorter.get(porter_id)

        if result and result.get("warnings"):
            warnings_text = "\n".join(
                [
                    await make_import_error_text(
                        warning.error if hasattr(warning, "error") else warning,
                        porter.lang, porter.user_lang
                    )
                    for warning in result["warnings"]]
            )

            if error_text:
                error_text += f"\n\n{warnings_text}"
            else:
                error_text = warnings_text

        with DBSession():
            porter = await DataPorter.get(porter_id)
            await porter.update(
                status=importer.status,
                start_time=importer.time_started,
                end_time=importer.time_finished,
                error_text=error_text,
            )
            cls.remove(porter.id)

            if porter.callback_url:
                await cls.send_callback_response(porter)

    @classmethod
    async def export_task(cls, porter_id: int):
        result = None
        with DBSession():
            porter, brand, group, user, timezone = await get_import_data(porter_id)

        exporter = StoreExporter(
            porter.brand_id, porter.external_type,
        )
        error_text = None

        export_func = exporter.start

        try:
            result = await export_func(
                lang=porter.lang,
                **cls.get_source_kwargs(porter.external_type, porter.source_data),
            )
        except ErrorWithTextVariable as e:
            if porter.id in cls._errors:
                cls._errors[porter.id] += 1
            else:
                cls._errors[porter.id] = 1

            logging.error(e, exc_info=True)
            error_text = await f(e.text_variable, porter.user_lang, **e.text_kwargs)
            with DBSession():
                porter = await DataPorter.get(porter_id)
                if cls._errors[porter.id] >= cls._retry_limit:
                    result = await cls.check_and_send_error_to_admins(
                        e, exporter, porter
                    )

        else:
            cls._errors.pop(porter.id, None)
            with DBSession():
                porter = await DataPorter.get(porter_id)

        if not result:
            return

        result_data, url = result.get("result"), None
        if exporter.status != StatusType.ERROR and not any(
                [porter.message_id, user]
        ) and porter.external_type in \
                (ExternalAPIType.EXCEL.value, ExternalAPIType.JSON.value):

            if porter.external_type == ExternalAPIType.JSON.value:
                file_path = save_temp_file(result_data, 'json')
            else:
                file_path = result_data

            media = await media_manager.save_from_file_path(
                file_path, copy_or_move="move",
                upload_file_name=file_path.split("/")[-1]
            )
            result_data = media.url

        with DBSession():
            porter = await DataPorter.get(porter_id)
            await porter.update(
                status=exporter.status,
                start_time=exporter.time_started,
                end_time=exporter.time_finished,
                result_data=result_data,
                error_text=error_text,
            )
            cls.remove(porter.id)

            if porter.callback_url:
                await cls.send_callback_response(porter)

    @classmethod
    async def check_and_send_error_to_admins(cls, e, exp_or_imp_object, porter):
        result = {"result": StatusType.ERROR}
        exp_or_imp_object.status = StatusType.ERROR
        cls._errors.pop(porter.id, None)
        original_error = e.text_kwargs.get("original_error", None) if isinstance(
            e,
            (StoreExportUnknownError, StoreImportUnknownError)
        ) and e.text_kwargs and "original_error" in e.text_kwargs else None
        await send_message_to_platform_admins(
            f"ERROR {porter.action_type}:\n{porter.id=}\n{porter.brand_id=}\n"
            f"{porter.external_type=}\n"
            f"{porter.source_data=}\n{e=}\n{original_error=}\n"
        )
        return result

    @classmethod
    async def get_status_response(
            cls, porter: DataPorter, is_for_list: bool | None = False
    ) -> StatusResponse:
        excel_file, json_data, url = None, None, None
        if (
                porter.action_type == ActionType.EXPORT and porter.result_data and
                porter.result_data.startswith("http")
        ):
            if not is_for_list:
                media = await media_manager.download_media(porter.result_data)

                if porter.external_type in ExternalAPIType.EXCEL.value:
                    excel_file = await cls.excel_file_to_base64(media.file_path)
                elif porter.external_type == ExternalAPIType.JSON.value:
                    try:
                        with open(media.file_path, 'r') as file:
                            json_data = json.load(file)
                    except json.JSONDecodeError:
                        json_data = None
            else:
                url = porter.result_data

        if porter.action_type == ActionType.IMPORT:
            if (porter.external_type == ExternalAPIType.SHEETS.value and
                    porter.source_data):
                url = get_sheet_url(porter.source_data)
            elif (porter.external_type == ExternalAPIType.MENU.value and
                  porter.source_data):
                try:
                    if porter.source_data is None:
                        raise TypeError("source_data is None")

                    source_data = json.loads(porter.source_data)
                    if isinstance(source_data, list) and len(source_data) > 0:
                        url = P4S_API_URL + "/" + source_data[0]
                    else:
                        raise ValueError("source_data is not a non-empty list")
                except (IndexError, ValueError, TypeError, json.JSONDecodeError) as e:
                    url = None
                    logging.error(f"Error processing source_data: {e}")

        return StatusResponse(
            uuid_id=porter.uuid_id,
            status=porter.status.value,
            start_time=porter.start_time,
            end_time=porter.end_time,
            url=url,
            excel_file=excel_file,
            json_data=json_data,
            error_text=porter.error_text,
            time_processed=calc_time_processed(
                porter.start_time, porter.end_time, porter.lang
            ),
            action_type=porter.action_type,
            external_type=porter.external_type,
        )

    @classmethod
    async def send_callback_response(cls, porter: DataPorter):
        data = await cls.get_status_response(porter)

        async with ClientSession() as session:
            async with session.post(porter.callback_url, json=data.dict()) as response:
                if response.status != 200:
                    resp_data = await response.json()
                    logger = logging.getLogger()
                    logger.error(
                        f"Error send status response:\nstatus: "
                        f"{response.status}\ndata: {resp_data}"
                    )
