from __future__ import annotations

import html
from datetime import timedelta

from aiogram import Bot, types
from aiogram.utils.parts import safe_split_text

from core.ext.types import StatusTypeLiteral, WarningsListType, WarningToLocaliseType

from psutils.convertors import interval_to_str

from utils.text import f, replace_html_symbols

from .functions import make_import_error_text

from .keyboards import get_go_back_keyboard


class Checker:
    def __init__(
            self, bot: Bot,
            chat_id: int,
            lang: str,
            is_export: bool = False
    ):
        self.bot = bot
        self.chat_id: int = chat_id
        self.lang = lang
        self.is_export = is_export

        self.message: types.Message | None = None
        self.is_done_warnings_sent: bool = False

    async def __call__(
            self,
            status: StatusTypeLiteral,
            time_passed: timedelta,
            warnings: WarningsListType,
    ):
        if status == "error":
            return

        import_or_export = "export" if self.is_export else "import"

        if status == "done" and import_or_export == "import" and warnings:
            if not self.is_done_warnings_sent:
                warnings_texts = []

                for warning in warnings:
                    if isinstance(warning, WarningToLocaliseType):
                        warnings_texts.append(await make_import_error_text(
                            warning.error, warning.file_lang, warning.user_lang,
                        ))
                    else:
                        warnings_texts.append(warning)
                warnings_str = "\n\n".join(warnings_texts)
                warnings_text = await f(
                    "store import warnings text", self.lang,
                    warnings=warnings_str, warnings_count=len(warnings)
                )

                parts = safe_split_text(warnings_text)
                for part in parts:
                    try:
                        part_text = replace_html_symbols(part)
                        await self.bot.send_message(self.chat_id, part_text)
                    except Exception:
                        part_text = html.escape(part)
                        await self.bot.send_message(self.chat_id, part_text)

                self.is_done_warnings_sent = True

            variable = "store import status done with warnings text"
        else:
            variable = f"store {import_or_export} status {status} text"

        new_text = await f(
            variable, self.lang,
            warnings_count=len(warnings),
            import_time=interval_to_str(time_passed, self.lang),
        )

        if status == "done":
            await self.bot.send_message(
                self.chat_id, new_text,
                reply_markup=await get_go_back_keyboard(self.lang)
            )
            if self.message:
                try:
                    await self.bot.delete_message(self.chat_id, self.message.message_id)
                except Exception:
                    pass

        elif not self.message:
            self.message = await self.bot.send_message(self.chat_id, new_text)
        elif self.message.text != new_text:
            self.message: types.Message = await self.message.edit_text(new_text)
