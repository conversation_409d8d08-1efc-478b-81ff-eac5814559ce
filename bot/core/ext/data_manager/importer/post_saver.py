from abc import ABC, abstractmethod
from psutils.translator.models import TranslatorModel
from typing import Type

from core.ext.types import ExternalType, ExternalTypeLiteral
from db import db_func, sess
from db.connection import Base
from db.models import Group, Translation
from utils.localisation import localisation
from .objects import ObjectData
from ..exporter import StoreExporter


class BasePostSaver(ABC):

    def __init__(
            self, brand_id: int,
            external_type: ExternalTypeLiteral,
            objects: dict[Type[Base], list[ObjectData[Base]]],
    ):
        self.brand_id: int = brand_id
        self.external_type: ExternalTypeLiteral = external_type
        self.objects: dict[Type[Base], list[ObjectData[Base]]] = objects

    @abstractmethod
    async def save(self, *args, **kwargs):
        raise NotImplementedError


class PostSaver(BasePostSaver):

    async def __get_group__(self) -> Group:
        return await Group.get_by_brand(self.brand_id)

    @db_func
    def clear_translations_and_thumbnails(
            self, localisation_langs: list[str], excluded_langs_list: list[str]
    ):
        ids_to_delete: list[str] = []

        langs_to_clear = [lang for lang in localisation_langs if
                          lang not in excluded_langs_list]

        for object_type, objects in self.objects.items():
            translator_model = TranslatorModel.detect_model(
                object_type.__class__.__name__, True
            )
            if not translator_model:
                continue

            def filter_objects(obj: ObjectData):
                if (
                        obj.is_updated and
                        any(
                            map(
                                lambda x: x in obj.update_fields,
                                translator_model.fields_names
                            )
                        )
                ):
                    return True
                return False

            for object_ in filter(filter_objects, objects):
                if (
                        object_.is_updated and
                        any(
                            map(
                                lambda x: x in object_.update_fields,
                                translator_model.fields_names
                            )
                        )
                ):
                    for lang in langs_to_clear:
                        ids_to_delete.append(
                            f"{object_type.__name__}-{lang}-{object_.id}"
                        )

        if ids_to_delete:
            query = sess().query(Translation).filter(Translation.id.in_(ids_to_delete))
            query.delete(synchronize_session=False)
        sess().commit()

    async def save(self, **kwargs):
        group = await self.__get_group__()
        localisation_langs = await localisation.langs
        await self.clear_translations_and_thumbnails(
            localisation_langs, group.get_langs_list(True)
        )

        if self.external_type in (ExternalType.EXCEL.value, ExternalType.SHEETS.value):
            exporter = StoreExporter(self.brand_id, self.external_type)
            return await exporter.post_import(**kwargs)
        return None
