import logging
import os.path
import time
from abc import abstractmethod
from collections import defaultdict
from contextlib import suppress
from dataclasses import dataclass
from mimetypes import guess_extension
from typing import Any, Iterable, Type

import requests
from multidict import istr
from psutils.decorators import sync_to_async
from psutils.media_manager.exceptions import MediaManagerError
from pydantic import ValidationError
from sqlalchemy import and_, or_, update

from config import (
    ALLOWED_IMAGE_EXTENSIONS, DEFAULT_LANG, GALLERY_ITEM_EXTENSIONS, MAX_POSITION_VALUE,
    P4S_API_URL, STATIC_DB,
    STORAGE_PAYFORSAY_HOST,
)
from core.ext import schemas
from core.ext.adapters.excel import models as excel_models
from core.ext.exceptions import *
from core.ext.types import (
    ExternalType, ExternalTypeLiteral, StatusType,
    WarningToLocaliseType, WarningsListType,
)
from core.media_manager import media_manager
from core.payment.exceptions import LiqpayUnitNameError
from db import DB<PERSON>ession, db_func, sess
from db.connection import Base
from db.crud.store.product.update import update_products_group_hashes_sync
from db.models import (
    Brand, DataPorter, Gallery, GalleryItem, Group, MediaObject, Store, StoreAttribute,
    StoreAttributeGroup,
    StoreCategory,
    StoreCharacteristic, StoreCharacteristicValue, StoreCustomField, StoreProduct,
    StoreProductGroup,
    StoreProductGroupCharacteristic, StoreProductSpotPrice, Translation,
)
from schemas.payment_settings.liqpay.schemas import LiqpayItem
from utils.date_time import utcnow
from utils.list import split_list
from utils.text import f
from .objects import ObjectData
from ..functions import validate_floating_sum_value

SKIP_INVALID_DATA_EXTERNAL_TYPES = (
    ExternalType.PROM.value, ExternalType.POSTER.value, ExternalType.GET_ORDER.value,
    ExternalType.INCUST.value,)

SKIP_POSITIONS_CONFIG_EXTERNAL_TYPES = (
    ExternalType.SHEETS.value, ExternalType.EXCEL.value
)

MEDIA_DOWNLOAD_PART_SIZE = 50

debugger = logging.getLogger('debugger')


class BaseDataSaver(ABC):
    def __init__(
            self, group: Group, brand_id: int, data: schemas.AdapterResult,
            external_type: ExternalTypeLiteral,
            skip_load_img: bool = False, lang: str = DEFAULT_LANG,
            user_lang: str | None = None,
            data_porter_id: int | None = None,
    ):
        self.brand_id: int = brand_id
        self.data: schemas.AdapterResult = data
        self.external_type: ExternalTypeLiteral = external_type
        self.skip_load_img: bool = skip_load_img

        self._added_files: set[str] = set()
        self._delete_files_on_success: set[str] = set()
        self.objects: dict[Type[Base], list[ObjectData[Base]]] = {}

        self.lang = lang
        self.user_lang = user_lang or lang
        self.data_porter_id = data_porter_id

        self.warnings: WarningsListType = []

        self.debugger = logging.getLogger('debugger')
        self.group: Group = group

    @abstractmethod
    async def save(self):
        raise NotImplementedError


class DataSaver(BaseDataSaver):
    available_product_types = ["goods", "info", "gift", "service", "topup"]

    def warning(self, error: ErrorWithTextVariable):
        self.warnings.append(WarningToLocaliseType(error, self.lang, self.user_lang))

    def error_or_warning(
            self, error: ErrorWithTextVariable, from_: Exception | None = None
    ):
        if self.external_type in SKIP_INVALID_DATA_EXTERNAL_TYPES:
            self.warning(error)
        else:
            if from_:
                raise error from from_
            raise error

    def _get_expr(self, object_type: Type[Base], **kwargs):
        query = sess().query(object_type)
        if hasattr(object_type, "brand_id"):
            query = query.filter(object_type.brand_id == self.brand_id)

        for key, value in kwargs.items():
            query = query.filter(getattr(object_type, key) == value)

        return query

    def _get_object(self, object_type: Type[Base], id: int | None = None, **kwargs):
        if id:
            kwargs["id"] = id

        query = self._get_expr(object_type, **kwargs)
        query = query.order_by(object_type.id.desc())
        return query.first()

    def _get_objects_dict(
            self, object_type: Type[Base], *, ids: list[int | str] | None = None,
            external_ids: list[str] | None = None,
            with_for_update: bool = False,
    ) -> dict[int | str, Type[Base]]:
        if not ids and not external_ids:
            return {}

        if all((ids, external_ids)):
            raise ValueError("Specify only one of ids, external_ids")

        query = sess().query(
            object_type.id if ids else object_type.external_id, object_type, )

        if hasattr(object_type, "brand_id"):
            query = query.filter(object_type.brand_id == self.brand_id)

        if ids:
            query = query.filter(object_type.id.in_(ids))
        else:
            query = query.filter(object_type.external_id.in_(external_ids))

        if with_for_update:
            query = query.populate_existing()
            query = query.with_for_update()

        result: list[tuple[int | str, type[Base]]] = query.all()  # type: ignore
        return dict(result)

    def _get_objects(self, object_type: Type[Base], **kwargs):
        query = self._get_expr(object_type, **kwargs)
        return query.all()

    def _update_object(self, obj: Any, _data: dict | None = None, **kwargs):
        if _data is None:
            _data = {}
        _data.update(kwargs)

        update_fields = []

        if hasattr(obj, "is_deleted"):
            obj.is_deleted = False

        if hasattr(obj, "external_type"):
            _data["external_type"] = self.external_type

        is_skip_download_images = False
        if "is_skip_download_images" in _data:
            is_skip_download_images = _data.pop("is_skip_download_images", None)
            if is_skip_download_images is not None:
                is_skip_download_images = is_skip_download_images

        for key, value in _data.items():
            if key not in dir(obj):
                raise AttributeError(f"Unknown attribute: {key}")

            if (
                    key == "image_path" and
                    hasattr(obj, "image_path") and
                    obj.image_path and
                    obj.image_path != value and
                    not is_skip_download_images
            ):
                self._delete_files_on_success.add(obj.image_path)

            if getattr(obj, key) != value:
                update_fields.append(key)
                setattr(obj, key, value)

        if obj.__class__ not in self.objects:
            self.objects[obj.__class__] = []
        self.objects[obj.__class__].append(
            ObjectData(
                id=obj.id, object=obj, is_updated=bool(update_fields),
                update_fields=update_fields, )
        )
        _data["is_skip_download_images"] = is_skip_download_images

    def _delete_old_objects(
            self, object_type: Type[Base], existing_ids: Iterable[int],
    ):
        """
        Delete objects that are not in new data

        @param object_type: Object's model
        @param existing_ids: Objects ids that are in new import
        @return:
        """
        query = sess().query(object_type).filter(
            object_type.brand_id == self.brand_id, )

        query = query.filter(object_type.id.not_in(existing_ids))
        query = query.filter(object_type.is_deleted.is_(False))
        query.update({"is_deleted": True})

    def download_file(
            self, file_url: str, item_type: str, item_id: str, only_image: bool = False
    ) -> str | None:
        if file_url.startswith(STORAGE_PAYFORSAY_HOST):
            return file_url.replace(f"{STORAGE_PAYFORSAY_HOST}/", "")

        file_dir = os.path.join(
            STATIC_DB, "store", "brands", str(self.brand_id), item_type + "s"
        )
        os.makedirs(file_dir, exist_ok=True)

        try:
            resp = requests.get(
                file_url, headers={
                    "User-Agent": "Mozilla/5.0; Windows 8.1; rv:55.0; Firefox/55.0",
                }
            )
            content_type = resp.headers.get(istr("Content-Type"))
            if only_image and content_type.split("/", 1)[0] != "image":
                raise ImportDownloadingNotImageError(file_url)

            ext = guess_extension(content_type)
            if ext is None:
                ext = content_type.rsplit("/", maxsplit=1)
                if len(ext) < 2:
                    if only_image:
                        raise ImportDownloadingNotImageError(file_url)
                    raise ImportDownloadingNotFileError(file_url)
                ext = "." + ext[1]

            # removing all disallowed symbols
            item_id = re.sub(r"\s", "-", item_id)
            item_id = "".join(re.findall(r"[\w-]", item_id))

            file_name = (f"{item_type}-{item_id}-"
                         f"{int(utcnow().timestamp() * 1000000)}{ext}")
            file_path = os.path.join(file_dir, file_name)
            with open(file_path, "wb") as file:
                for content in resp.iter_content(1024):
                    file.write(content)

        except BaseImportDownloadingFileError as e:
            logging.error(e, exc_info=True)
            return None
        except StoreImportError:
            raise
        except Exception as e:
            raise ImportDownloadingFileError(file_url) from e

        self._added_files.add(file_path)
        return file_path

    @staticmethod
    def extract_file_path_from_p4s_url(file_url: str):
        if file_url.startswith(P4S_API_URL):
            return file_url.replace(P4S_API_URL + "/", "").strip()

        return None

    @staticmethod
    def _validate_ids(
            object_name: str, id: int, external_id: str, ids: Iterable[int],
            external_ids: Iterable[str],
    ):
        if id in ids:
            raise StoreImportDuplicateObjectId(object_name, id)
        if external_id in external_ids:
            raise StoreImportDuplicateObjectExternalId(object_name, external_id)

    def _save_stores_choice(self):
        new_objects = set()

        stores = {store.external_id: store for store in self._get_objects(Store)}
        if not stores:
            brand = self._get_object(Brand, id=self.brand_id)
            group = Group.get_sync(brand.group_id)
            currency = group.currency
            store = Store(
                name=brand.name, brand_id=self.brand_id,
                external_type=self.external_type, external_id=brand.name,
                _currency=currency, )
            new_objects.add(store)
            stores[store.external_id] = store

        for product in self.data.products:
            product.stores_external_ids = list(stores.keys())
        for category in self.data.categories:
            category.stores_external_ids = list(stores.keys())

        return stores, new_objects

    def _save_stores(self, medias: dict[str, MediaObject]):
        if self.external_type == ExternalType.CHOICE.value:
            return self._save_stores_choice()

        new_objects = set()
        stores: dict[str, Store] = {}
        ids: set[int] = set()
        external_ids: set[str] = set()

        # ignore when deleting
        existing_stores_ids: set[int] = set()

        for store_data in self.data.stores:
            self._validate_ids(
                "Store", store_data.id, store_data.external_id, ids, external_ids
            )

            if store_data.id:
                store = self._get_object(Store, store_data.id)
                if not store:
                    raise StoreImportObjectNotFoundByIDError(
                        "store", store_data.id, )

                ids.add(store_data.id)
            else:
                store = self._get_object(
                    Store, external_id=store_data.external_id, )

            external_ids.add(store_data.external_id)

            store_media = medias.get(store_data.image_url, None)

            data = store_data.dict(
                exclude={"custom_fields", "id", "image_url"}, exclude_unset=True
            )

            data["media_id"] = store_media.id if store_media else None

            if not store:
                store = Store(
                    **data, brand_id=self.brand_id, external_type=self.external_type, )
                new_objects.add(store)

                if store_data.custom_fields:
                    for field_data in store_data.custom_fields:
                        field = self._create_custom_field(field_data, store)
                        if field:
                            field.store = store
                            new_objects.add(field)
            else:
                if store.id:
                    existing_stores_ids.add(store.id)

                self._update_object(store, data)

                if store_data.custom_fields:
                    for field_data in store_data.custom_fields:
                        field = self._get_object(
                            StoreCustomField, store_id=store.id,
                            name=field_data.name.strip(), )
                        if not field:
                            field = self._create_custom_field(field_data, store)
                            if field:
                                new_objects.add(field)
                        else:
                            field.value = field_data.value

            stores[store_data.external_id] = store

        self._delete_old_objects(Store, existing_stores_ids)

        return stores, new_objects

    @staticmethod
    def _create_custom_field(data: schemas.StoreCustomField, store: Store):
        return StoreCustomField(
            name=data.name.strip(), value=data.value, store_id=store.id, )

    def _save_attributes(self) -> tuple[
        dict[str, StoreAttributeGroup], dict[str, StoreAttribute], set[
            StoreAttributeGroup | StoreAttribute],]:
        new_objects = set()

        attribute_groups: dict[str, StoreAttributeGroup] = {}
        attributes: dict[str, StoreAttribute] = {}

        ag_ids: set[int] = set()
        ag_external_ids: set[str] = set()
        # ignore when deleting
        ag_existing_ids: set[int] = set()

        a_ids: set[int] = set()
        a_external_ids: set[str] = set()
        # ignore when deleting
        a_existing_ids: set[int] = set()

        try:
            is_import_primary_ags = (
                self.group.config.positions_config.is_import_primary_ags)
        except AttributeError:
            is_import_primary_ags = None

        sorted_ag, strict_position = self._get_strict_position(
            is_import_primary_ags,
            self.data.attribute_groups
        )
        for index, attribute_group_data in enumerate(sorted_ag):
            if (attribute_group_data.position > MAX_POSITION_VALUE or index >
                    MAX_POSITION_VALUE):
                raise ImportObjectMaxPositionError(object_name="attribute_group")
            if attribute_group_data.excel_position:
                self.warning(
                    AttributeGroupImportPositionWarning(attribute_group_data.name)
                )

            self._validate_ids(
                "StoreAttributeGroup", attribute_group_data.id,
                attribute_group_data.external_id, ag_ids,
                ag_external_ids, )

            exclude_dict = {"attributes", "id"}
            if strict_position:
                if not attribute_group_data.id:
                    attribute_group_data.position = index
                else:
                    exclude_dict = {"attributes", "id", "position"}

            if attribute_group_data.id:
                attribute_group = self._get_object(
                    StoreAttributeGroup, attribute_group_data.id
                )
                if not attribute_group:
                    raise StoreImportObjectNotFoundByIDError(
                        "attribute_group", attribute_group_data.id
                    )

                ag_ids.add(attribute_group.id)
            else:
                attribute_group = self._get_object(
                    StoreAttributeGroup, external_id=attribute_group_data.external_id, )
            ag_external_ids.add(attribute_group_data.external_id)

            data = attribute_group_data.dict(
                exclude=exclude_dict, exclude_unset=True
            )
            data.pop("excel_position", None)

            if not attribute_group:
                attribute_group = StoreAttributeGroup(
                    **data, brand_id=self.brand_id, external_type=self.external_type, )
                new_objects.add(attribute_group)
            else:
                if attribute_group.id:
                    ag_existing_ids.add(attribute_group.id)
                self._update_object(attribute_group, data)

            attribute_groups[attribute_group.external_id] = attribute_group

            for attribute_data in attribute_group_data.attributes:
                self._validate_ids(
                    "StoreAttribute", attribute_data.id, attribute_data.external_id,
                    a_ids, a_external_ids, )

                if attribute_data.id:
                    attribute = self._get_object(StoreAttribute, attribute_data.id)
                    if not attribute:
                        raise StoreImportObjectNotFoundByIDError(
                            "attribute", attribute_data.id
                        )

                    a_ids.add(attribute_data.id)
                else:
                    attribute = self._get_object(
                        StoreAttribute, external_id=attribute_data.external_id, )
                a_external_ids.add(attribute_data.external_id)

                data = attribute_data.dict(exclude={"id"}, exclude_unset=True)
                if not attribute:
                    attribute = StoreAttribute(
                        **data, brand_id=self.brand_id, attribute_group=attribute_group,
                        external_type=self.external_type, )
                    new_objects.add(attribute)
                else:
                    if attribute.id:
                        a_existing_ids.add(attribute.id)
                    self._update_object(
                        attribute, data, attribute_group=attribute_group
                    )

                attributes[attribute.external_id] = attribute

        self._delete_old_objects(StoreAttributeGroup, ag_existing_ids)
        self._delete_old_objects(StoreAttribute, a_existing_ids)

        return attribute_groups, attributes, new_objects

    def _save_category(
            self, category_data: schemas.Category, stores: dict[str, Store],
            medias: dict[str, MediaObject],
            exclude_dict: list[str] | None = None,
    ):
        new_objects = set()

        category_stores = [store for ext_id, store in stores.items() if
                           ext_id in category_data.stores_external_ids]

        if category_data.id:
            category = self._get_object(StoreCategory, category_data.id)
            if not category:
                raise StoreImportObjectNotFoundByIDError(
                    "category", category_data.id, )
        else:
            category = self._get_object(
                StoreCategory, external_id=category_data.external_id, )

        category_media = medias.get(category_data.image_url, None)

        exclude = {"father_category_id", "stores_external_ids", "filters", "id",
                   "image_url"}
        if exclude_dict:
            for value in exclude_dict:
                exclude.add(value)

        data = category_data.dict(
            exclude=exclude, exclude_unset=True, )
        data["stores"] = category_stores
        data["media_id"] = category_media.id if category_media else None
        data.pop("excel_position", None)

        if not category:
            category = StoreCategory(
                **data, brand_id=self.brand_id, external_type=self.external_type, )
            new_objects.add(category)
        else:
            self._update_object(category, data)

        return category, new_objects

    def _save_categories(
            self, stores: dict[str, StoreCategory], medias: dict[str, MediaObject]
    ):
        new_objects = set()
        categories: dict[str, StoreCategory] = {}
        ids: set[int] = set()
        external_ids: set[str] = set()

        category_existing_ids: set[int] = set()

        test_external_ids = set()

        def process_category(obj: schemas.Category):
            self._validate_ids(
                "StoreCategory", obj.id, obj.external_id, ids, test_external_ids
            )
            test_external_ids.add(obj.external_id)

        list(map(process_category, self.data.categories))

        categories_by_external_id = self._get_objects_dict(
            StoreCategory, external_ids=list(test_external_ids)
        )

        categories_with_ids = []
        for category_data in self.data.categories:
            if categories_by_external_id.get(category_data.external_id):
                category_data.id = categories_by_external_id[
                    category_data.external_id].id
            categories_with_ids.append(category_data)

        try:
            is_import_primary_categories = (
                self.group.config.positions_config.is_import_primary_categories)
        except AttributeError:
            is_import_primary_categories = None

        sorted_categories, strict_position = self._get_strict_position(
            is_import_primary_categories,
            categories_with_ids
        )

        for index, category_data in enumerate(sorted_categories):
            if (category_data.position > MAX_POSITION_VALUE or index >
                    MAX_POSITION_VALUE):
                raise ImportObjectMaxPositionError(object_name="category")

            if category_data.excel_position:
                self.warning(CategoryImportPositionWarning(category_data.name))

            self._validate_ids(
                "StoreCategory", category_data.id, category_data.external_id, ids,
                external_ids
            )

            exclude_dict = None
            if strict_position:
                if not category_data.id:
                    category_data.position = index
                else:
                    exclude_dict = ["position"]

            category, new_child_categories = self._save_category(
                category_data, stores, medias, exclude_dict
            )
            if category_data.id:
                ids.add(category.id)
            if category.id:
                category_existing_ids.add(category.id)
            external_ids.add(category_data.external_id)

            new_objects.update(new_child_categories)
            categories[category_data.external_id] = category

        for category_data in self.data.categories:
            if category_data.father_category_id is None:
                father_category = None
            elif category_data.external_id == category_data.father_category_id:
                self.error_or_warning(
                    StoreImportCategoryFatherIDError(category_data.external_id)
                )
                father_category = None
            else:
                if category_data.father_category_id not in categories:
                    self.error_or_warning(
                        UnknownFatherCategoryError(category_data.father_category_id)
                    )
                    father_category = None
                else:
                    father_category = categories[category_data.father_category_id]

            categories[category_data.external_id].father_category = father_category

        self._delete_old_objects(StoreCategory, category_existing_ids)

        return categories, new_objects

    def _save_product_groups(self):
        new_objects = set()
        product_groups: dict[str, StoreProductGroup] = {}
        ids: set[int] = set()
        external_ids: set[str] = set()

        product_groups_existing_ids: set[int] = set()

        for product_group_data in self.data.product_groups:
            self._validate_ids(
                "StoreProductGroup", product_group_data.id,
                product_group_data.external_id, ids, external_ids, )

            if product_group_data.id:
                ids.add(product_group_data.id)
            external_ids.add(product_group_data.external_id)

        product_groups_by_id = self._get_objects_dict(StoreProductGroup, ids=list(ids))
        product_groups_by_external_id = self._get_objects_dict(
            StoreProductGroup, external_ids=list(external_ids)
        )

        for product_group_data in self.data.product_groups:
            product_group: StoreProductGroup | None

            if product_group_data.id:
                product_group = product_groups_by_id.get(product_group_data.id)
            else:
                product_group = product_groups_by_external_id.get(
                    product_group_data.external_id
                )

            if not product_group_data.name:
                product_group_data.name = product_group_data.external_id

            if not product_group:
                product_group = StoreProductGroup(
                    name=product_group_data.name,
                    external_id=product_group_data.external_id,
                    external_type=self.external_type, brand_id=self.brand_id, )
                new_objects.add(product_group)
            else:
                if product_group.id:
                    product_groups_existing_ids.add(product_group.id)
                self._update_object(
                    product_group, {"external_id": product_group_data.external_id}
                )

            product_groups[product_group_data.external_id] = product_group

        self._delete_old_objects(StoreProductGroup, product_groups_existing_ids)

        return product_groups, new_objects

    def _save_product_groups_characteristics(
            self, characteristics: dict[str, StoreCharacteristic],
            products_groups: dict[str, StoreProductGroup],
    ):
        new_objects = set()
        for product_group_data in self.data.product_groups:
            product_group = products_groups[product_group_data.external_id]

            new_characteristics: list[StoreProductGroupCharacteristic] = []
            for characteristic_ext_id in set(
                    product_group_data.modifiers +
                    product_group_data.hide_modifications_by
            ):
                if characteristic_ext_id not in characteristics:
                    self.error_or_warning(
                        UnknownModifierError(
                            characteristic_ext_id, product_group_data.external_id
                        )
                    )
                    continue

                characteristic = characteristics[characteristic_ext_id]

                product_group_characteristic = self._get_object(
                    StoreProductGroupCharacteristic, product_group_id=product_group.id,
                    characteristic_id=characteristic.id, ) if (product_group.id and
                                                               characteristic.id) \
                    else None

                is_modifier = characteristic_ext_id in product_group_data.modifiers
                show_one_modification = (characteristic_ext_id in
                                         product_group_data.hide_modifications_by)

                if not product_group_characteristic:
                    product_group_characteristic = StoreProductGroupCharacteristic(
                        product_group=product_group, characteristic=characteristic,
                        is_modifier=is_modifier,
                        show_one_modification=show_one_modification, )
                    new_objects.add(product_group_characteristic)
                else:
                    self._update_object(
                        product_group_characteristic, {
                            "is_modifier": is_modifier,
                            "show_one_modification": show_one_modification,
                        }
                    )
                new_characteristics.append(product_group_characteristic)

            for product_group_characteristic in product_group.characteristics:
                if product_group_characteristic not in new_characteristics:
                    sess().delete(product_group_characteristic)

        return new_objects

    def _save_categories_filters(
            self, characteristics: dict[str, StoreCharacteristic],
            categories: dict[str, StoreCategory],
    ):
        for category_data in self.data.categories:
            category = categories[category_data.external_id]

            new_filters = []
            for filter_name in category_data.filters:
                if filter_name not in characteristics:
                    self.error_or_warning(
                        UnknownFilterError(filter_name, category.external_id)
                    )

                new_filters.append(characteristics[filter_name])

            category.filters = new_filters

    def _save_products(
            self, medias: dict[str, MediaObject], stores: dict[str, Store],
            categories: dict[str, StoreCategory],
            product_groups: dict[str, StoreProductGroup],
            attribute_groups: dict[str, StoreAttributeGroup],
            characteristics: dict[str, StoreCharacteristic],
    ):
        new_objects = set()
        external_ids = set()
        ids: set[int] = set()

        product_existing_ids: set[int] = set()

        def process_product(obj: schemas.Product):
            self._validate_ids(
                "StoreProduct", obj.id, obj.external_id, ids, external_ids, )

            if obj.id:
                ids.add(obj.id)
            external_ids.add(obj.external_id)

        list(map(process_product, self.data.products))

        products_by_id = self._get_objects_dict(StoreProduct, ids=list(ids))
        products_by_external_id = self._get_objects_dict(
            StoreProduct, external_ids=list(external_ids)
        )

        try:
            is_import_primary_products = (
                self.group.config.positions_config.is_import_primary_products)
        except AttributeError:
            is_import_primary_products = None

        sorted_products, strict_position = self._get_strict_position(
            is_import_primary_products,
            self.data.products
        )

        for index, product_data in enumerate(sorted_products):
            product: StoreProduct | None

            if product_data.excel_position:
                self.warning(ProductImportPositionWarning(product_data.name))
            if product_data.id:
                product = products_by_id.get(product_data.id)
                if not product:
                    raise StoreImportObjectNotFoundByIDError("product", product_data.id)
            else:
                product = products_by_external_id.get(product_data.external_id)

            if not product:
                product_data.is_skip_download_images = False
            elif product and not product.media_id:
                product_data.is_skip_download_images = False

            if product_data.is_skip_download_images:
                product_media = None
                product_gallery = None
            else:
                product_media = medias.get(product_data.image_url, None)

                product_gallery: Gallery | None = None
                if product_data.gallery_items:
                    product_gallery = product.gallery if product else None
                    if not product_gallery:
                        product_gallery = Gallery()
                        new_objects.add(product_gallery)

                    added_urls: list[str] = []
                    added_medias: list[int] = []
                    new_gallery_items: list[GalleryItem] = []
                    for position, gallery_item_url in enumerate(
                            product_data.gallery_items
                    ):
                        if gallery_item_url in added_urls:
                            continue
                        if gallery_item_url not in medias.keys():
                            continue

                        media = medias[gallery_item_url]
                        if media.id in added_medias:
                            continue

                        gallery_item = self._get_object(
                            GalleryItem, gallery_id=product_gallery.id,
                            media_id=media.id,

                        ) if product_gallery.id else None

                        if not gallery_item:
                            gallery_item = GalleryItem(
                                gallery=product_gallery, media=media, position=position
                            )
                            new_objects.add(gallery_item)
                        else:
                            self._update_object(
                                gallery_item, {
                                    "position": position
                                }
                            )

                        added_medias.append(media.id)
                        added_urls.append(gallery_item_url)
                        new_gallery_items.append(gallery_item)

                    for gallery_item in product_gallery.gallery_items:
                        if gallery_item not in new_gallery_items:
                            sess().delete(gallery_item)
                        sess().flush()
                    product_gallery.gallery_items = new_gallery_items

            if len(product_data.name) > StoreProduct.MAX_NAME_LENGTH:
                product_data.name = product_data.name[:StoreProduct.MAX_NAME_LENGTH]

            data = product_data.dict(
                exclude={"spots_prices", "categories_external_ids",
                         "attribute_groups_external_ids",
                         "stores_external_ids", "characteristics", "product_group_id",
                         "id", "gallery_items",
                         "image_url", "product_type_info_custom_fields",
                         "product_type_liqpay_custom_fields", },
                exclude_unset=True
            )
            
            # Clean carriage return symbols from description field
            if "description" in data and data["description"]:
                data["description"] = data["description"].replace("_x000D_", "")

            if product_media:
                data["media_id"] = product_media.id
                if product and product_media.id != product.media_id:
                    data["thumbnail_media_id"] = None
            elif not product_data.is_skip_download_images:
                data["media_id"] = None
                data["thumbnail_media_id"] = None

            if product_gallery and not product_data.is_skip_download_images:
                data["gallery"] = product_gallery

            res = validate_floating_sum_value(
                data.get("floating_sum_value", None), data["product_id"], data["name"]
            )
            data["floating_sum_min"] = round(res.min * 100) if res.min else 0
            data["floating_sum_max"] = round(res.max * 100) if res.max else 0
            data["floating_sum_enabled"] = res.is_enabled
            data["floating_sum_user_sum_enabled"] = res.user_sum_enabled
            data["floating_sum_options"] = res.options or None

            if not data.get("type", None):
                data["type"] = "goods"

            if data["type"] not in self.available_product_types:
                raise StoreImportUnknownProductTypeError(
                    data["product_id"], data["name"]
                )

            if data["type"] == "info":
                if product_data.product_type_info_custom_fields:
                    info_text = next(
                        (schema for schema in
                         product_data.product_type_info_custom_fields if
                         schema.name == "pti_info_text"), None
                    )
                    info_link = next(
                        (schema for schema in
                         product_data.product_type_info_custom_fields if
                         schema.name == "pti_info_link"), None
                    )
                    data["pti_info_text"] = info_text.value if info_text else None
                    data["pti_info_link"] = info_link.value if info_link else None
                else:
                    data["pti_info_text"] = None
                    data["pti_info_link"] = None

            if data["type"] == "goods":
                if product_data.product_type_liqpay_custom_fields:
                    liqpay_id = next(
                        (schema for schema in
                         product_data.product_type_liqpay_custom_fields if
                         schema.name == "liqpay_id"), None
                    )
                    liqpay_unit_name = next(
                        (schema for schema in
                         product_data.product_type_liqpay_custom_fields if
                         schema.name == "liqpay_unit_name"), None
                    )
                    liqpay_codifier = next(
                        (schema for schema in
                         product_data.product_type_liqpay_custom_fields if
                         schema.name == "liqpay_codifier"), None
                    )
                    liqpay_tax_list = next(
                        (schema for schema in
                         product_data.product_type_liqpay_custom_fields if
                         schema.name == "liqpay_tax_list"), None
                    )

                    data["liqpay_id"] = liqpay_id.value if liqpay_id else None
                    data[
                        "liqpay_unit_name"] = liqpay_unit_name.value if (
                        liqpay_unit_name) else None
                    data[
                        "liqpay_codifier"] = liqpay_codifier.value if liqpay_codifier \
                        else None
                    data[
                        "liqpay_tax_list"] = liqpay_tax_list.value if liqpay_tax_list \
                        else None

                    if liqpay_unit_name and liqpay_unit_name.value:
                        try:
                            _ = LiqpayItem(liqpay_unit_name=liqpay_unit_name.value)
                        except ValidationError as err:
                            logging.error(f"{err}", exc_info=True)
                            raise LiqpayUnitNameError(
                                data["product_id"],
                                liqpay_unit_name.value
                            )

            if data["position"] > MAX_POSITION_VALUE or index > MAX_POSITION_VALUE:
                raise ImportObjectMaxPositionError(object_name="product")
            data.pop("excel_position", None)

            if not product:
                if strict_position:
                    data["position"] = index
                data.pop("is_skip_download_images", None)
                product = StoreProduct(
                    **data, brand_id=self.brand_id, external_type=self.external_type, )
                new_objects.add(product)
            else:
                if product.id:
                    product_existing_ids.add(product.id)
                if strict_position:
                    data.pop("position")
                self._update_object(product, data)

            new_characteristics_values_ids = []
            if product_data.characteristics:
                for characteristic_value_data in product_data.characteristics:
                    if characteristic_value_data.external_id not in characteristics:
                        self.debugger.debug(
                            f"{characteristic_value_data.external_id = }\n"
                            f"{characteristics = }"
                        )
                        self.error_or_warning(
                            StoreImportUnknownCharacteristicError(
                                characteristic_value_data.external_id
                            ), )
                        continue

                    characteristic = characteristics[
                        characteristic_value_data.external_id]

                    characteristic_value = self._get_object(
                        StoreCharacteristicValue, characteristic_id=characteristic.id,
                        product_id=product.id
                    ) if product.id and characteristic.id else None

                    if characteristic_value_data.value:
                        value = characteristic_value_data.value[
                                :StoreCharacteristicValue.MAX_VALUE_LEN]
                    else:
                        value = characteristic_value_data.value
                    if not characteristic_value:
                        characteristic_value = StoreCharacteristicValue(
                            characteristic=characteristic, product=product,
                            value=value, )
                        new_objects.add(characteristic_value)
                    else:
                        self._update_object(
                            characteristic_value, value=value, )
                    new_characteristics_values_ids.append(characteristic_value.id)

            if product.id:  # if updating existing product
                old_characteristic_values = self._get_objects(
                    StoreCharacteristicValue, product_id=product.id
                )
                for el in old_characteristic_values:
                    if el.id not in new_characteristics_values_ids:
                        sess().delete(el)

            if product_data.spots_prices:
                new_spot_prices = []
                for spot_price_store_id, spot_price_data in (
                        product_data.spots_prices.items()):
                    if spot_price_store_id in stores:
                        store = stores[spot_price_store_id]

                        is_product_in_store = store in product.stores

                        if product.id is None or store.id is None:
                            spot_price = None
                        else:
                            spot_price = self._get_object(
                                StoreProductSpotPrice, product_id=product.id,
                                store_id=store.id
                            )
                            if spot_price:
                                spot_price.price = product_data.spots_prices[
                                    store.external_id].price
                                spot_price.old_price = product_data.spots_prices[
                                    store.external_id].old_price
                                if not is_product_in_store:
                                    debugger.debug(
                                        f"product {product.name} not in store "
                                        f"{store.external_id}"
                                    )
                                    self.warning(
                                        ProductNotInStoreWarning(
                                            product.external_id, product.name,
                                            store.external_id, store.name
                                        )
                                    )

                        if not spot_price:
                            spot_price = StoreProductSpotPrice(
                                product=product, store=store,
                                price=product_data.spots_prices[
                                    store.external_id].price,
                                old_price=product_data.spots_prices[
                                    store.external_id].old_price,
                            )
                            if not is_product_in_store:
                                debugger.debug(
                                    f"product {product.name} not in store "
                                    f"{store.external_id}"
                                )
                                self.warning(
                                    ProductNotInStoreWarning(
                                        product.external_id, product.name,
                                        store.external_id, store.name
                                    )
                                )

                        new_spot_prices.append(spot_price)

                for el in product.spots_prices:
                    if el not in new_spot_prices:
                        sess().delete(el)

            if product_data.product_group_id:
                if product_data.product_group_id not in product_groups:
                    self.error_or_warning(
                        UnknownProductGroupExternalIdInProductError(
                            product_data.product_group_id, product_data.external_id, )
                    )
                else:
                    product.product_group = product_groups[
                        product_data.product_group_id]
            else:
                product.product_group = None

            new_stores = []
            for store_ext_id in product_data.stores_external_ids:
                if store_ext_id not in stores:
                    self.error_or_warning(
                        UnknownStoreExternalIdInProductError(
                            store_ext_id, product_data.external_id
                        )
                    )
                else:
                    new_stores.append(stores[store_ext_id])
            product.stores = new_stores

            new_categories = []
            if product_data.categories_external_ids:
                for category_ext_id in product_data.categories_external_ids:
                    if category_ext_id not in categories:
                        self.error_or_warning(
                            UnknownCategoryExternalIdInProductError(
                                category_ext_id, product_data.external_id
                            )
                        )
                    else:
                        new_categories.append(categories[category_ext_id])
            product.categories = new_categories

            new_attribute_groups = []
            for ag_ext_id in product_data.attribute_groups_external_ids:
                if ag_ext_id not in attribute_groups:
                    self.error_or_warning(
                        UnknownAttributeGroupExternalIdInProductError(
                            ag_ext_id, product_data.external_id
                        )
                    )
                else:
                    new_attribute_groups.append(attribute_groups[ag_ext_id])
            product.attribute_groups = new_attribute_groups

        self._delete_old_objects(StoreProduct, product_existing_ids)

        return new_objects, ids

    def _save_characteristics(self):
        new_objects = set()

        ids: set[int] = set()
        external_ids: set[str] = set()

        characteristic_existing_ids: set[int] = set()

        for characteristic_data in self.data.characteristics:
            self._validate_ids(
                "StoreCharacteristic", characteristic_data.id,
                characteristic_data.external_id, ids, external_ids, )

            if characteristic_data.id:
                ids.add(characteristic_data.id)
            external_ids.add(characteristic_data.external_id)

        characteristics_by_id = self._get_objects_dict(
            StoreCharacteristic, ids=list(ids)
        )
        characteristics_by_external_id = self._get_objects_dict(
            StoreCharacteristic, external_ids=list(external_ids)
        )

        characteristics: dict[str, StoreCharacteristic] = {}

        for characteristic_data in self.data.characteristics:
            characteristic: StoreCharacteristic | None

            if characteristic_data.id:
                characteristic = characteristics_by_id.get(characteristic_data.id)
                if not characteristic:
                    raise StoreImportObjectNotFoundByIDError(
                        "characteristic", characteristic_data.id
                    )
            else:
                characteristic = characteristics_by_external_id.get(
                    characteristic_data.external_id
                )

            data = characteristic_data.dict(exclude={"id"}, exclude_unset=True)
            if data.get("filter_type"):
                data["filter_type"] = data["filter_type"].value

            if characteristic:
                characteristic_existing_ids.add(characteristic.id)
                self._update_object(characteristic, data)
            else:
                characteristic = StoreCharacteristic(
                    brand_id=self.brand_id, external_type=self.external_type, **data, )
                new_objects.add(characteristic)

            characteristics[characteristic_data.external_id] = characteristic

        self._delete_old_objects(StoreCharacteristic, characteristic_existing_ids)

        return characteristics, new_objects

    def _save_translations(self) -> set[Base]:
        translation_data, obj_translated_langs = self._prepare_translations_to_save()
        new_objects = set()

        if translation_data:
            ids = [Translation.make_id(*key) for key in translation_data.keys()]

            translations = self._get_objects_dict(
                Translation, ids=ids,
                with_for_update=True,
            )

            # Separate empty translations from non-empty ones
            empty_translation_ids = []
            non_empty_translations = []

            def process(obj: tuple[tuple[str, str, str], dict[str, Any]]):
                translation_id, data = obj

                translation = translations.get(Translation.make_id(*translation_id))
                if not translation:
                    translation = Translation(
                        obj_type=translation_id[0],
                        obj_id=translation_id[1],
                        lang=translation_id[2],
                        data=data,
                    )
                    new_objects.add(translation)
                else:
                    # Check if all translation fields (excluding service fields) are None or empty
                    translation_fields = {k: v for k, v in data.items() 
                                        if k not in ('id', 'external_id', 'lang')}
                    is_empty = all(value is None or value == "" for value in translation_fields.values())
                    
                    if is_empty:
                        empty_translation_ids.append(Translation.make_id(*translation_id))
                    else:
                        non_empty_translations.append((translation, data))

            list(map(process, translation_data.items()))

            # Batch update empty translations
            if empty_translation_ids:
                sess().execute(
                    update(Translation)
                    .values(data={})
                    .where(Translation.id.in_(empty_translation_ids))
                )

            # Update non-empty translations individually
            for translation, data in non_empty_translations:
                self._update_object(translation, data=data)

        conditions = []
        for key, excluded_langs in obj_translated_langs.items():
            obj_type, obj_id = key

            and_conditions = [
                Translation.obj_type == obj_type,
                Translation.obj_id == obj_id,
            ]
            if excluded_langs:
                and_conditions.append(Translation.lang.not_in(excluded_langs))
            conditions.append(and_(*and_conditions))

        if conditions:
            sess().execute(
                update(Translation)
                .values(data={})
                .where(or_(*conditions))
            )

        return new_objects

    @db_func
    def _save(self, medias: dict[str, MediaObject]):
        try:
            start_time = time.time()
            new_objects = set()

            stores, new_stores = self._save_stores(medias)
            new_objects.update(new_stores)

            new_categories_time = time.time()
            categories, new_categories = self._save_categories(stores, medias)
            new_objects.update(new_categories)
            self.debugger.debug(
                f'new_categories_time: {(time.time() - new_categories_time):.3f}'
            )

            characteristics, new_characteristics = self._save_characteristics()
            new_objects.update(new_characteristics)

            attribute_groups, attributes, new_attributes_and_groups = (
                self._save_attributes())
            new_objects.update(new_attributes_and_groups)

            product_groups, new_product_groups = self._save_product_groups()
            new_objects.update(new_product_groups)

            new_product_time = time.time()
            new_product_objs, db_product_ids = self._save_products(
                medias, stores, categories, product_groups, attribute_groups,
                characteristics,
            )
            new_objects.update(new_product_objs)
            self.debugger.debug(
                f'new_product_time: {(time.time() - new_product_time):.3f}'
            )

            new_objects.update(
                self._save_product_groups_characteristics(
                    characteristics, product_groups
                )
            )
            self._save_categories_filters(characteristics, categories)

            sess().add_all(new_objects)
            sess().flush()

            sess().flush()
            update_products_group_hashes_sync(
                product_ids=db_product_ids,
            )

            self._save_new_objects(new_objects)

            new_translations = self._save_translations()

            sess().add_all(new_translations)
            self._save_new_objects(new_objects)

            self._update_status(StatusType.SAVING)

            sess().commit()

            self.debugger.debug(f'_save time: {(time.time() - start_time):.3f}')
        except:
            sess().rollback()
            raise

    def _save_new_objects(self, new_objects: set):
        for new_object in new_objects:
            if new_object.__class__ not in self.objects:
                self.objects[new_object.__class__] = []
            self.objects[new_object.__class__].append(
                ObjectData(
                    id=new_object.id, object=new_object, is_created=True, )
            )

    @staticmethod
    @sync_to_async
    def clear(files: Iterable[str]):
        for file in files:
            with suppress(Exception):
                os.remove(file)

    async def save_media_objects(self):
        self.debugger.debug("save_media_objects START:")
        start_time = time.time()

        to_save_images = ToSaveData([], ALLOWED_IMAGE_EXTENSIONS)
        to_save_gallery_items = ToSaveData([], GALLERY_ITEM_EXTENSIONS)

        to_save: list[ToSaveData] = [to_save_images, to_save_gallery_items]

        # for errors trace
        urls_data: dict[str, list[UrlData]] = defaultdict(list)

        product_image_field_name = await excel_models.Product.get_field_verbose_name(
            "image_url", self.lang, )
        product_gallery_field_name = await excel_models.Product.get_field_verbose_name(
            "gallery_items", self.lang
        )
        store_image_field_ame = await excel_models.Store.get_field_verbose_name(
            "image_url", self.lang
        )
        category_image_field_name = await excel_models.Category.get_field_verbose_name(
            "image_url", self.lang
        )

        for product in self.data.products:
            if product.image_url and product.image_url not in to_save_images.urls:
                to_save_images.urls.append(product.image_url)
            urls_data[product.image_url].append(
                UrlData(
                    product.image_url, product.external_id, product_image_field_name,
                    product.position, )
            )

            if product.gallery_items:
                for gallery_item_url in product.gallery_items:
                    if gallery_item_url not in to_save_gallery_items.urls:
                        to_save_gallery_items.urls.append(gallery_item_url)
                    urls_data[gallery_item_url].append(
                        UrlData(
                            gallery_item_url, product.external_id,
                            product_gallery_field_name,
                            product.position, )
                    )

        for store in self.data.stores:
            if store.image_url and store.image_url not in to_save_images.urls:
                to_save_images.urls.append(store.image_url)
            urls_data[store.image_url].append(
                UrlData(
                    store.image_url, store.external_id, store_image_field_ame,
                    store.position, )
            )

        for category in self.data.categories:
            if category.image_url and category.image_url not in to_save_images.urls:
                to_save_images.urls.append(category.image_url)
            urls_data[category.image_url].append(
                UrlData(
                    category.image_url, category.external_id, category_image_field_name,
                    category.position, )
            )

        medias: dict[str, MediaObject] = {}
        errors: dict[str, MediaManagerError] = {}

        len_to_save = sum(map(lambda x: len(x.urls), to_save))
        self.debugger.debug(f"{len_to_save=}")

        for to_save_obj in to_save:
            urls_to_download = []
            for url in to_save_obj.urls:
                local_file_path = self.extract_file_path_from_p4s_url(url)
                if local_file_path:
                    media = await MediaObject.get(
                        file_path=local_file_path
                    )

                    if media:
                        medias[url] = media
                        continue

                urls_to_download.append(url)

            self.debugger.debug(
                f"START downloading urls: {len(urls_to_download)}, "
                f"{type(urls_to_download)}"
            )

            parts = split_list(urls_to_download, MEDIA_DOWNLOAD_PART_SIZE)

            results: dict[str, MediaObject | MediaManagerError] = {}

            for part in parts:
                part_results = await media_manager.batch_download_media(
                    *part, allowed_types=to_save_obj.allowed_types,
                )
                results.update(part_results)

            for media_url, result in results.items():
                if isinstance(result, MediaManagerError):
                    save_to = errors
                else:
                    save_to = medias
                save_to[media_url] = result
            self.debugger.debug(
                f"END downloading urls: {len(urls_to_download)}, "
                f"{type(urls_to_download)}"
            )

        self.debugger.debug(f"save_media_objects END: {time.time() - start_time} s")

        if errors:
            errors_texts: list[str] = []
            for media_url, error in errors.items():
                for media_url_data in urls_data[media_url]:
                    error_text = await f(
                        "store import media error", self.lang,
                        external_id=media_url_data.external_id,
                        field_name=media_url_data.verbose_field_name,
                        row_number=media_url_data.excel_row_number or "-",
                        error=await f(
                            error.text_variable, self.lang, **error.text_kwargs
                        )
                    )

                    errors_texts.append(error_text)

            if self.external_type in SKIP_INVALID_DATA_EXTERNAL_TYPES:
                self.warnings.extend(errors_texts)
            else:
                errors_str = "\n\n".join(errors_texts)
                raise StoreImportDownloadMediasError(errors_str, len(errors))

        return medias

    async def save(self):
        medias = await self.save_media_objects()
        try:
            await self._save(medias)
        except Exception as e:
            await self.clear(self._added_files)
            raise e

        await self.clear(self._delete_files_on_success)

    def _prepare_object_translations(
            self, object_type: Type[Base], objects: dict[type[Base], "ObjectDicts"],
            translations: list[schemas.Translation],
            translation_data: dict[tuple[str, str, str], dict[str, Any]],
            obj_translated_langs: dict[tuple[str, str], set[str]],
            exclude: set[str] | None = None
    ):
        object_dicts = objects.get(object_type, None)
        if not object_dicts:
            return

        characteristic_dicts = objects.get(StoreCharacteristic)
        characteristic_value_dicts = objects.get(StoreCharacteristicValue)

        obj_translated_langs.update(
            dict(
                map(
                    lambda key: ((object_type.__name__, key), set()),
                    objects[object_type].by_id.keys()
                )
            )
        )

        for translation in translations:
            if translation.id:
                obj = object_dicts.by_id.get(translation.id)
            else:
                obj = object_dicts.by_ext_id.get(translation.external_id)

            if not obj:
                raise ValueError(
                    f"Translation for not existing object of type "
                    f"{object_type.__name__} with "
                    f"{translation.id = } {translation.external_id = }"
                )

            obj_translated_langs[(object_type.__name__, str(obj.id))].add(
                translation.lang
            )

            translation_data[(object_type.__name__, obj.id, translation.lang)] = \
                translation.dict(
                    exclude=exclude
                )

            if isinstance(translation, schemas.TranslationProduct):
                if not characteristic_dicts or not characteristic_value_dicts:
                    continue

                for cv_translation in translation.characteristics:
                    characteristic = characteristic_dicts.by_ext_id.get(
                        cv_translation.external_id
                    )
                    if not characteristic:
                        self.debugger.debug(
                            "StoreImportTranslationForNotExistingCharacteristicError: "
                            f"{characteristic_dicts.by_ext_id = }\n"
                            f"{cv_translation = }"
                        )
                        self.error_or_warning(
                            StoreImportTranslationForNotExistingCharacteristicError(
                                cv_translation.external_id, )
                        )
                        continue

                    cv_ext_id = f"{obj.id}-{characteristic.id}"
                    characteristic_value = characteristic_value_dicts.by_ext_id.get(
                        cv_ext_id
                    )
                    if characteristic_value:
                        translation_data[
                            (
                                characteristic_value.__class__.__name__,
                                characteristic_value.id,
                                cv_translation.lang,
                            )
                        ] = cv_translation.dict()
                    elif cv_translation.value is not None:
                        self.debugger.debug(
                            f"{cv_ext_id = }\n{characteristic_value_dicts = }"
                        )
                        self.error_or_warning(
                            StoreImportTranslationForNotExistingCharacteristicValueError(
                                obj.external_id, cv_translation.external_id,
                            )
                        )

    def _get_object_dicts(self, object_type: Type[Base]):
        by_id_dict: dict[int, Base] = {}
        by_ext_id_dict: dict[str, Base] = {}

        is_cv = object_type is StoreCharacteristicValue

        def process(obj_: ObjectData):
            by_id_dict[obj_.id] = obj_.object

            if is_cv:
                ext_id = f"{obj_.object.product_id}-{obj_.object.characteristic_id}"
            else:
                ext_id = obj_.object.external_id
            by_ext_id_dict[ext_id] = obj_.object

        list(map(process, self.objects.get(object_type, [])))

        return ObjectDicts(by_id_dict, by_ext_id_dict)

    def _get_objects_dicts(self):
        result: dict[type[Base], ObjectDicts] = {}
        for object_type in (
                Store, StoreCategory, StoreCharacteristic, StoreCharacteristicValue,
                StoreProduct, StoreAttribute,
                StoreAttributeGroup,
        ):
            result[object_type] = self._get_object_dicts(object_type)
        return result

    def _prepare_translations_to_save(self):
        translation_data: dict[tuple[str, str, str], dict[str, Any]] = {}
        obj_translated_langs: dict[tuple[str, str], set[str]] = defaultdict(set)

        if self.data.translations is None:
            return translation_data, obj_translated_langs

        objects_dicts = self._get_objects_dicts()

        self._prepare_object_translations(
            Store, objects_dicts, self.data.translations.stores, translation_data,
            obj_translated_langs
        )
        self._prepare_object_translations(
            StoreCategory, objects_dicts, self.data.translations.categories,
            translation_data, obj_translated_langs
        )
        self._prepare_object_translations(
            StoreProduct, objects_dicts, self.data.translations.products,
            translation_data,
            obj_translated_langs,
            exclude={"characteristics"}, )
        self._prepare_object_translations(
            StoreAttribute, objects_dicts, self.data.translations.attributes,
            translation_data, obj_translated_langs
        )
        self._prepare_object_translations(
            StoreAttributeGroup, objects_dicts, self.data.translations.attribute_groups,
            translation_data, obj_translated_langs
        )
        self._prepare_object_translations(
            StoreCharacteristic, objects_dicts, self.data.translations.characteristics,
            translation_data, obj_translated_langs
        )

        return translation_data, obj_translated_langs

    def _update_status(self, status: StatusType):
        with DBSession() as own_sess:
            update(DataPorter).where(DataPorter.id == self.data_porter_id).values(
                status=status
            ).execute()
            own_sess.commit()

    def _get_strict_position(
            self, is_import: bool | None,
            objects: list,
    ) -> tuple[list, bool]:
        sorted_products = objects
        strict_position = False
        if self.external_type in SKIP_POSITIONS_CONFIG_EXTERNAL_TYPES:
            return sorted_products, strict_position

        if is_import is False:
            strict_position = True
            for item in objects:
                if not item.id:
                    item.position = -1
            sorted_products = sorted(
                objects, key=lambda x: (x.position == -1, x.position)
            )

        return sorted_products, strict_position


@dataclass
class UrlData:
    url: str
    external_id: str
    verbose_field_name: str
    excel_row_number: int | None = None


@dataclass
class ToSaveData:
    urls: list[str]
    allowed_types: list[str] | None = None


@dataclass
class ObjectDicts:
    by_id: dict[int, Base]
    by_ext_id: dict[str, Base]
