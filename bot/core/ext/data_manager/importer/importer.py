import asyncio
from datetime import datetime
from typing import Type

from psutils.exceptions import ErrorWithTextVariable
from pymysql.err import IntegrityError

from config import DEFAULT_LANG
from core.adapters.excel.exceptions import ExcelFieldError
from core.ext.adapters.base import AdapterType, BaseAdapter
from core.ext.exceptions import (
    ImportCheckerError, StoreImportDuplicateDbError, StoreImportDuplicateObjectWarning,
    StoreImportError, StoreImportUnknownError,
)
from core.ext.types import (
    ExternalTypeLiteral, OnStatusUpdatedType, ReturnType, StatusType,
    WarningToLocaliseType,
    WarningsListType,
)
from core.helpers import get_next_object_id
from db import DBSession, db_func
from db.models import Brand, DataPorter, Group, StoreProduct, User
from loggers import JSONLogger
from utils.date_time import utcnow
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from .data_saver import BaseDataSaver, DataSaver
from .post_saver import BasePostSaver, PostSaver
from ...schemas import AdapterResult

DEFAULT_CHECKER_TIMEOUT = 1.0


class StoreImporter:
    def __init__(
            self,
            porter_id: int,
            external_type: ExternalTypeLiteral,
            skip_load_image: bool = False,
            base_adapter_cls: Type[AdapterType] = BaseAdapter,
            data_saver_cls: Type[BaseDataSaver] = DataSaver,
            post_saver_cls: Type[BasePostSaver] = PostSaver,
            user_lang: str | None = None
    ):
        self.porter_id = porter_id
        self.external_type = external_type
        self.skip_load_image = skip_load_image
        self.base_adapter_cls = base_adapter_cls
        self.data_saver_cls = data_saver_cls
        self.post_saver_cls = post_saver_cls

        self.time_started: datetime | None = None
        self.time_finished: datetime | None = None
        self.adapter = None

        self.status: StatusType = StatusType.NOT_STARTED
        self.warnings: WarningsListType = []

        self.data_porter: DataPorter | None = None
        self.lang: str = DEFAULT_LANG
        self.group_id: int | None = None
        self.user_lang: str = user_lang
        self.debug_data: dict | None = {}

    async def set_status(self, status: StatusType):
        self.status: StatusType = status
        if self.data_porter:
            await self.data_porter.update(
                status=self.status,
                start_time=self.time_started,
                end_time=self.time_finished,
            )

    async def start(
            self,
            lang: str = DEFAULT_LANG,
            user_lang: str | None = None,
            **kwargs,
    ) -> ReturnType:
        if user_lang is None:
            user_lang = lang
        self.lang = user_lang

        self.time_started = utcnow()

        logger = JSONLogger(f"import.{self.external_type}", "Import")

        try:
            with DBSession():
                data_porter, brand, group, user, _ = await get_import_data(
                    self.porter_id
                )
                self.data_porter = data_porter
                self.external_type = data_porter.external_type
                self.group_id = group.id

                await self.set_status(StatusType.LOADING)

                self.debug_data = {
                    "brand": {
                        "id": brand.id,
                        "name": brand.name,
                    },
                    "lang": lang,
                    "user_lang": user_lang,
                    "data_porter": {
                        "id": self.data_porter.id,
                        "uuid": self.data_porter.uuid_id,
                    } if self.data_porter else None,
                }
                logger.add_data(self.debug_data)

                adapter_cls = self.base_adapter_cls.get_adapter(self.external_type)
                common_params = {
                    "brand": brand,
                    "group": group,
                    "data_porter": data_porter,
                    "lang": lang,
                    "user_lang": user_lang,
                    "import_or_export": "import",
                    "user": user,
                    **kwargs
                }
                adapter_data = await adapter_cls.get_data_from_database(**common_params)

                common_params.update(adapter_data)

                self.adapter = adapter_cls(**common_params)
                logger.debug("executing adapter")

            data = self.deduplicate_list_attributes(
                await self.adapter.get_and_convert_data()
            )

            with DBSession():
                data_porter, brand, group, user, _ = await get_import_data(
                    self.porter_id
                )
                self.data_porter = data_porter
                await self.set_status(StatusType.PROCESSING)

                data = await self.process_ids(data)

                logger.debug(
                    "adapter:success, executing data saver", {
                        "adapter_data": data,
                    }
                )

                saver = self.data_saver_cls(
                    group,
                    brand.id, data,
                    self.external_type,
                    self.skip_load_image,
                    lang, user_lang, data_porter_id=self.data_porter.id,
                )
                await saver.save()

                logger.debug("data_saver:success, executing post_saver")

                self.warnings.extend(saver.warnings)

                post_saver = self.post_saver_cls(
                    brand.id, self.external_type, saver.objects
                )
                await self.set_status(StatusType.POST_SAVING)
                result = await post_saver.save(**kwargs, lang=self.lang)

                logger.debug("post_saver:success", {"result": result})

        except Exception as e:
            with DBSession():
                logger.error("error occurred", e)

                data_porter = await DataPorter.get(self.porter_id)
                self.data_porter = data_porter
                current_status = data_porter.status.value

                self.time_finished = utcnow()
                await self.set_status(StatusType.ERROR)

            if isinstance(
                    e, (StoreImportError, ExcelFieldError, ErrorWithTextVariable)
            ):
                raise

            if hasattr(e, "orig") and isinstance(e.orig, IntegrityError):
                if hasattr(e.orig, "args") and len(e.orig.args) > 1:
                    await send_message_to_platform_admins(
                        f"Import ERROR:\n\n{e.orig.args[1]}\n\n{self.debug_data=}"
                    )
                    raise StoreImportDuplicateDbError(e.orig.args[1]) from e

            current_status_text = await f(
                f"admin import export status {current_status} text", lang
            )
            raise StoreImportUnknownError(
                original_error=str(e),
                current_status=current_status_text if current_status_text else
                ""
            ) from e

        self.time_finished = utcnow()
        with DBSession():
            data_porter = await DataPorter.get(self.porter_id)
            self.data_porter = data_porter
            await self.set_status(StatusType.DONE)

        return {
            "result": result,
            "status": self.status.value,
            "time_passed": self.time_finished - self.time_started,
            "warnings": self.warnings,
        }

    async def _checker(
            self, on_status_updated: OnStatusUpdatedType, timeout: float,
            ignore_errors: bool
    ):
        while True:
            try:
                time_passed = utcnow() - self.time_started if (
                    self.time_started) else 0
                await on_status_updated(self.status.value, time_passed, self.warnings)
            except Exception as e:
                if not ignore_errors:
                    raise ImportCheckerError() from e

            if self.status.value in ("done", "error"):
                break

            await asyncio.sleep(timeout)

        time_passed = utcnow() - self.time_started if self.time_started else 0
        await on_status_updated(self.status.value, time_passed, self.warnings)

    async def start_with_checker(
            self,
            on_status_updated: OnStatusUpdatedType,
            lang: str = DEFAULT_LANG,
            user_lang: str | None = None,
            timeout: float = DEFAULT_CHECKER_TIMEOUT,
            ignore_errors: bool = True,
            **kwargs,
    ) -> ReturnType:
        results = await asyncio.gather(
            self.start(lang, user_lang, **kwargs),
            self._checker(on_status_updated, timeout, ignore_errors),
            return_exceptions=False,
        )

        return results[0]

    def deduplicate_list_attributes(self, data: AdapterResult) -> AdapterResult:
        attributes_to_deduplicate = [
            'categories_external_ids',
            'attribute_groups_external_ids',
            'stores_external_ids',
            'gallery_items',
        ]

        for product in data.products:
            for attr in attributes_to_deduplicate:
                if hasattr(product, attr):
                    value = getattr(product, attr)
                    if value and isinstance(value, list) and len(value) > 0:
                        original_length = len(value)
                        unique_values = list(
                            self.remove_duplicates_preserve_order(value)
                        )
                        if len(unique_values) < original_length:
                            self.warnings.append(
                                WarningToLocaliseType(
                                    StoreImportDuplicateObjectWarning(
                                        object_name=attr,
                                        external_id=product.external_id
                                    ),
                                    self.lang,
                                    self.lang,
                                )
                            )
                        setattr(product, attr, unique_values)

            if product.characteristics:
                original_length = len(product.characteristics)
                seen = set()
                unique_characteristics = []
                for char in product.characteristics:
                    if char.external_id not in seen:
                        seen.add(char.external_id)
                        unique_characteristics.append(char)
                if len(unique_characteristics) < original_length:
                    self.warnings.append(
                        WarningToLocaliseType(
                            StoreImportDuplicateObjectWarning(
                                object_name="characteristics",
                                external_id=product.external_id
                            ),
                            self.lang,
                            self.lang,
                        )
                    )
                product.characteristics = unique_characteristics

            if product.spots_prices:
                original_length = len(product.spots_prices)
                unique_spots_prices = {}
                for store_id, spot_price in product.spots_prices.items():
                    if store_id not in unique_spots_prices:
                        unique_spots_prices[store_id] = spot_price
                if len(unique_spots_prices) < original_length:
                    self.warnings.append(
                        WarningToLocaliseType(
                            StoreImportDuplicateObjectWarning(
                                object_name="spot prices",
                                external_id=product.external_id
                            ),
                            self.lang,
                            self.lang,
                        )
                    )
                product.spots_prices = unique_spots_prices

        for store in data.stores:
            if store.custom_fields:
                original_length = len(store.custom_fields)
                seen = set()
                unique_custom_fields = []
                for field in store.custom_fields:
                    if field.name not in seen:
                        seen.add(field.name)
                        unique_custom_fields.append(field)
                if len(unique_custom_fields) < original_length:
                    self.warnings.append(
                        WarningToLocaliseType(
                            StoreImportDuplicateObjectWarning(
                                object_name="custom fields",
                                external_id=store.external_id
                            ),
                            self.lang,
                            self.lang,
                        )
                    )
                store.custom_fields = unique_custom_fields

        for category in data.categories:
            if category and category.filters:
                original_length = len(category.filters)
                unique_filters = list(
                    self.remove_duplicates_preserve_order(category.filters)
                )
                if len(unique_filters) < original_length:
                    self.warnings.append(
                        WarningToLocaliseType(
                            StoreImportDuplicateObjectWarning(
                                object_name="filters",
                                external_id=category.external_id
                            ),
                            self.lang,
                            self.lang,
                        )
                    )
                category.filters = unique_filters

        return data

    @staticmethod
    def remove_duplicates_preserve_order(input_list: list[str]) -> list[str]:
        seen = set()
        return [item for item in input_list if not (item in seen or seen.add(item))]

    async def process_ids(self, data: AdapterResult):
        max_product_id = int(await get_next_object_id(StoreProduct, self.group_id))
        for product in data.products:
            if not product.product_id:
                product.product_id = str(max_product_id)
                max_product_id += 1
            if not product.external_id:
                product.external_id = product.product_id
        return data


@db_func
def get_import_data(porter_id: int):
    data_porter = DataPorter.get_sync(porter_id)
    if not data_porter:
        raise ValueError(f"DataPorter with id {porter_id} not found")

    brand = Brand.get_sync(data_porter.brand_id)
    group = Group.get_sync(brand.group_id)
    user = User.get_sync(data_porter.user_id) if data_porter.user_id else None
    timezone = user.get_timezone(group.timezone) if user else group.timezone

    return data_porter, brand, group, user, timezone
