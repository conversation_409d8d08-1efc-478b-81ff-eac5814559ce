import enum
import html
import json
import logging
import os
import re
from datetime import <PERSON><PERSON><PERSON>
from types import UnionType
from typing import get_args

from aiogram import Bot, types
from psutils.date_time import get_local_datetime
from psutils.exceptions import ErrorWithTextVariable
from pydantic.fields import Undefined, UndefinedType

from core.ext import schemas
from core.ext.exceptions import (
    StoreImportFloatingSumInvalidPatternValueError,
    StoreImportFloatingSumMinMaxCountValueError, StoreImportFloatingSumMinMaxValueError,
    StoreImportFloatingSumMinMaxWithOptionsValueError, StoreImportFloatingSumNegativeNumberValueError,
    StoreImportFloatingSumNoUserSumWithoutOptionsValueError, StoreImportFloatingSumUnknownValueError,
    StoreImportUnknownError, StoreImportWarning,
)
from core.ext.types import ActionType, ActionTypeLiteral, ExternalType
from db.models import Brand, <PERSON><PERSON>orter, User
from utils.text import f, replace_html_symbols


def get_type_str(type_: type, lang: str):
    return f(f"import {type_.__name__} type text", lang)


async def convert_type_to_str(type_: type, lang: str):
    if isinstance(type_, UnionType):
        return ", ".join(
            [
                await get_type_str(type_, lang)
                for type_ in get_args(type_)
            ]
        )
    elif type_ is Undefined or type_ is UndefinedType:
        return ""
    elif type_:
        return await get_type_str(type_, lang)

    return ""


async def send_result(
        bot: Bot,
        porter: DataPorter,
        action_type: ActionTypeLiteral,
        result: dict,
        timezone: str,
):
    user = await User.get_by_id(porter.user_id)
    if not result.get("result") or not user:
        return

    message_id = porter.message_id
    try:

        if porter.external_type == ExternalType.SHEETS.value:
            sheets_url = result.get("result")
            if action_type == ActionType.IMPORT.value:
                message_text = await f("store sheets post import done text", porter.user_lang, sheets_url=sheets_url)
            else:
                message_text = sheets_url

            try:
                await bot.send_message(user.chat_id, message_text, reply_to_message_id=message_id)
            except Exception:
                await bot.send_message(user.chat_id, message_text)

        else:
            brand = await Brand.get(porter.brand_id)

            if action_type == ActionType.IMPORT.value:
                message_text = await f("store excel post import done text", porter.user_lang)
            else:
                message_text = await f("store excel export done text", porter.user_lang)

            excel_file_path = result.get("result")
            local_dt = get_local_datetime(timezone)

            ext = excel_file_path.rsplit(".", 1)[1]
            filename = await f(
                "store export filename", porter.user_lang,
                brand_name=brand.name,
                datetime=local_dt.strftime("%Y-%m-%d_%H-%M"),
            )

            try:
                await bot.send_document(
                    user.chat_id, types.InputFile(
                        path_or_bytesio=excel_file_path,
                        filename=f"{filename}.{ext}",
                    ),
                    caption=message_text,
                    reply_to_message_id=message_id,
                )
            except Exception:
                await bot.send_document(
                    user.chat_id, types.InputFile(
                        path_or_bytesio=excel_file_path,
                        filename=f"{filename}.{ext}",
                    ),
                    caption=message_text,
                )

            os.remove(excel_file_path)
    except Exception as e:
        logging.getLogger(f"error.porter.{porter.action_type.value}").error(
            f"An error occurred while sending result: {repr(e)}\n" + json.dumps(
                {
                    "data_porter": {
                        "id": porter.id,
                        "uuid": porter.uuid_id,
                    },
                    "user": {
                        "id": user.id,
                        "name": user.name,
                        "chat_id": user.chat_id,
                    },
                    "message_id": message_id,
                    "result": result,
                    "timezone": timezone,
                }, indent=4, ensure_ascii=False, cls=CustomJSONEncoder
            )
        )


async def send_error(bot: Bot, porter: DataPorter, parts: list[str]):
    user = await User.get_by_id(porter.user_id) if porter.user_id else None
    if not user or not user.chat_id:
        return

    message_id = porter.message_id
    if not user:
        return

    try:
        text: str = ""
        for part in parts:
            try:
                part_text = replace_html_symbols(part)
                text = "\n\n".join([text, part_text])
            except:
                part_text = html.escape(part)
                text = "\n\n".join([text, part_text])
            await bot.send_message(user.chat_id, text, reply_to_message_id=message_id)
    except Exception as e:
        logging.getLogger(f"error.porter.${porter.action_type.value}").error(
            f"An error occurred while sending error to user {repr(e)}\n" + json.dumps(
                {
                    "data_porter": {
                        "id": porter.id,
                        "uuid": porter.uuid_id,
                    },
                    "user": {
                        "id": user.id,
                        "name": user.name,
                        "chat_id": user.chat_id,
                    },
                    "parts": parts,
                    "message_id": message_id,
                },
                indent=4,
                ensure_ascii=False,
                cls=CustomJSONEncoder,
            )
        )


async def make_import_error_text(error: ErrorWithTextVariable, file_lang: str, user_lang: str):
    if isinstance(error, str):
        return error
    text_kwargs = error.text_kwargs
    if "schema_name_variable" in text_kwargs:
        text_kwargs["schema_name"] = await f(text_kwargs.pop("schema_name_variable", None), file_lang)
    if "field_name_variable" in text_kwargs:
        text_kwargs["field_name"] = await f(text_kwargs.pop("field_name_variable", None), file_lang)
    if "object_type" in text_kwargs:
        text_kwargs["object_name"] = await f(text_kwargs.pop("object_type", "") + "_object", file_lang)
    if "field_name" in text_kwargs and isinstance((field_name := text_kwargs.get("field_name")), dict):
        text_kwargs["field_name"] = await f(field_name.get("variable", None), file_lang)

    for el in ("excepted_type", "actual_type"):
        if el in text_kwargs:
            text_kwargs[el] = await convert_type_to_str(text_kwargs.get(el), user_lang)

    text = await f(error.text_variable, user_lang, **text_kwargs)
    if not isinstance(error, (StoreImportUnknownError, StoreImportWarning)):
        text = await f("store import base error", user_lang, error=text)

    return text


def validate_floating_sum_value(value: str | None, product_id: str, product_name: str) -> schemas.FloatingSumSettings:
    floating_sum_settings = schemas.FloatingSumSettings(
        is_enabled=False,
        user_sum_enabled=True,
    )

    if not value:
        return floating_sum_settings
    if re.match(r'^[+-]+$', value):
        if value == "+":
            floating_sum_settings.is_enabled = True
            return floating_sum_settings
        return floating_sum_settings

    value = value.replace(" ", "")
    value = value.strip(",")
    value = value.lower()
    # pattern = re.compile(r'\b(-?\d+(\.\d{1,2})?|nosum|-?\d+/-?\d+)(,\s*(-?\d+(\.\d{1,2})?|-?\d+/-?\d+|nosum))*(?=\b|$)')  # all errors valid
    pattern = re.compile(
        r'\b(-?\d+(\.\d{1,2})?|nosum|-?\d+(\.\d{1,2})?/-?\d+(\.\d{1,2})?)(,\s*(-?\d+(\.\d{1,2})?|-?\d+(\.\d{1,2})?/-?\d+(\.\d{1,2})?|nosum))*(?=\b|$)'
    )
    valid_format = bool(re.fullmatch(pattern, value))
    if not valid_format:
        raise StoreImportFloatingSumInvalidPatternValueError(product_id, product_name)  # tested

    count_min_max = value.count('/')
    if count_min_max > 1:
        raise StoreImportFloatingSumMinMaxCountValueError(product_id, product_name)  # tested

    numeric_values = re.split(r',\s*', value)
    options = []

    for numeric_value in numeric_values:
        if '/' in numeric_value:
            # Parse min/max values for fractions like "20/100"
            min_val, max_val = map(float, numeric_value.split('/'))
            if max_val < 0 or min_val < 0:
                raise StoreImportFloatingSumNegativeNumberValueError(product_id, product_name)  # tested
            if max_val < min_val or min_val > max_val:
                raise StoreImportFloatingSumMinMaxValueError(product_id, product_name)  # tested
            floating_sum_settings.min = min_val
            floating_sum_settings.max = max_val
        elif '.' in numeric_value:
            # Parse floating-point numbers like "40.50"
            num = float(numeric_value)
            if num < 0:
                raise StoreImportFloatingSumNegativeNumberValueError(product_id, product_name)  # tested
            options.append(num)
        elif numeric_value.isdigit():
            # Parse integers like "40"
            num = int(numeric_value)
            options.append(num)
        elif numeric_value.lower() == 'nosum':
            # Special case for "nosum"
            floating_sum_settings.user_sum_enabled = False
        else:
            raise StoreImportFloatingSumUnknownValueError(product_id, product_name)

    if not options and not floating_sum_settings.user_sum_enabled:
        raise StoreImportFloatingSumNoUserSumWithoutOptionsValueError(product_id, product_name)  # tested

    if options:
        floating_sum_settings.options = options

    if options and (floating_sum_settings.min or floating_sum_settings.max):
        for option in options:
            if option < floating_sum_settings.min or option > floating_sum_settings.max:
                raise StoreImportFloatingSumMinMaxWithOptionsValueError(product_id, product_name)  # tested

    floating_sum_settings.is_enabled = True
    return floating_sum_settings


def json_dumps_default_func(obj):
    if isinstance(obj, enum.Enum):
        return obj.value
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


def write_debug_log(import_or_export: str, external_type: str, message: str, data: dict | None = None):
    logging.getLogger(f"debugger.{import_or_export}.{external_type}").debug(
        message + '\n' + json.dumps(
            data, indent=4,
            ensure_ascii=False,
            default=json_dumps_default_func,
        ) if data else ''
    )


def write_error_log(import_or_export: str, external_type: str, message: str, data: dict | None = None):
    logging.getLogger(f"error.{import_or_export}.{external_type}").error(
        message + '\n' + json.dumps(
            data, indent=4,
            ensure_ascii=False,
            default=json_dumps_default_func,
        ) if data else '',
        exc_info=True
    )


def timedelta_to_string(td: timedelta) -> str:
    return str(td)


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, timedelta):
            return timedelta_to_string(obj)
        return super().default(obj)
