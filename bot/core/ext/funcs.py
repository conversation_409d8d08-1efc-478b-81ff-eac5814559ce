from db.models import StoreOrder, OrderShipment


def make_comment_for_order(
        order: StoreOrder,
        shipment: OrderShipment,
        is_get_order_or_poster: bool = False,
) -> str:
    comment = f"*USER COMMENT: {order.address_comment}; PHONE: {order.phone}; P4S_ID: {order.id}; " \
              f"USER NAME: {order.first_name} {order.last_name}; "
    if shipment.base_type == 'delivery':
        if is_get_order_or_poster:
            comment += f"ADDRESS: STREET {order.address_street}, HOUSE {order.address_house}, " \
                       f"FLAT {order.address_flat}, ENTRANCE {order.address_entrance}, FLOOR {order.address_floor}; "
        if order.desired_delivery_date:
            comment += f"DELIVERY DATE: {order.desired_delivery_date.strftime('%d.%m.%Y')}; "
        if order.desired_delivery_time and shipment.delivery_datetime_mode == "datetime":
            comment += f"DELIVERY TIME: {order.desired_delivery_time}; "

    comment += "*"

    return comment


CUSTOM_FIELDS_NAMES = [
    "address",
    "work_times",
    "email",
    "phone_number",
    "wifi",
    "facebook",
    "instagram",
    "pinterest",
    "telegram",
    "twitter",
    "viber",
    "whatsapp",
    "youtube",
    "tiktok",
]


def get_store_custom_field_names() -> list[str]:
    return CUSTOM_FIELDS_NAMES
