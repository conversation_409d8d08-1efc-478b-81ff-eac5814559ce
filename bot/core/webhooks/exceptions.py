import schemas


class WebhookError(Exception):
    def __init__(self, message: str, journal_data: schemas.WebhookJournalDataSchema | None = None):
        self.message = message
        self.journal_data = journal_data
        super().__init__(self.message, self.journal_data)


class WebhookUnknownError(WebhookError):
    def __init__(self, journal_data: schemas.WebhookJournalDataSchema | None = None):
        super().__init__("webhook unknown error", journal_data)


class WebhookBlockedError(WebhookError):
    def __init__(self, journal_data: schemas.WebhookJournalDataSchema | None = None):
        super().__init__("webhook blocked error", journal_data)


class WebhookEntityNotExistError(WebhookError):
    def __init__(self, entity: str, journal_data: schemas.WebhookJournalDataSchema | None = None):
        super().__init__(f"webhook entity '{entity}' not in webhook entities", journal_data)


class WebhookInternalError(WebhookError):
    def __init__(self, message: str, journal_data: schemas.WebhookJournalDataSchema | None = None):
        super().__init__(message, journal_data)


class WebhookExternalError(WebhookError):
    def __init__(self, message: str, journal_data: schemas.WebhookJournalDataSchema | None = None):
        super().__init__(message, journal_data)


class WebhookRateLimitError(WebhookError):
    def __init__(self, message: str, journal_data: schemas.WebhookJournalDataSchema | None = None):
        super().__init__(message, journal_data)
