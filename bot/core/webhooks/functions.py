import hashlib
import hmac
import json
import uuid
from asyncio.exceptions import TimeoutError as AsyncioTimeoutError
from typing import Type

import pytz
import time
from aiohttp import (
    ClientConnectorError, ClientResponse, ClientResponseError, ClientSession,
    ClientTimeout,
)

import schemas
from config import (
    MAKE_HOOK_DOMAIN, MAKE_HOOK_ENTITY_BY_NAME, ZAPIER_HOOK_DOMAIN,
    ZAPIER_HOOK_ENTITY_BY_NAME,
)
from core.kafka.producer.functions import add_webhook_event_for_action
from db import crud
from db.models import (
    CRMTicket, Chat, ChatMessage, ClientBot, Group, Invoice, MediaObject, Review,
    StoreOrderPayment, TextNotification, User, Webhook,
)
from loggers import J<PERSON>NLogger
from utils.date_time import utcnow
from utils.media import make_preview_url
from utils.text import f
from utils.type_vars import T
from .exceptions import (
    WebhookExternalError, WebhookInternalError, WebhookRateLimitError,
)


async def add_webhook_event(
        entity: schemas.WebhookEntityEnum,
        entity_id: int,
        action: schemas.WebhookActionEnum,
        group_id: int,
        data: dict,
        data_type: Type[T],
        event_uuid: str | None = None,
):
    profile = await Group.get(group_id)

    if not event_uuid:
        event_uuid = uuid.uuid4().hex
    event_created_datetime = utcnow()
    webhooks = await crud.get_webhooks_by_entity(group_id, entity)
    events = []
    if webhooks:
        for webhook in webhooks:
            if not webhook.blocked and not webhook.is_deleted and webhook.is_enabled:
                event = schemas.WebhookMessageValue(
                    group_id=group_id,
                    webhook_id=webhook.id,
                    hook_uuid=webhook.hook_id,
                    event_uuid=event_uuid,
                    event_created_datetime=event_created_datetime,
                    data=schemas.WebhookKafkaData(
                        entity=entity,
                        entity_id=entity_id,
                        action=action,
                        event_id=event_uuid,
                        data=data_type(**data),
                        profile_id=group_id,
                        profile_name=profile.name,
                        profile_lang=profile.lang
                    ),
                )
                events.append(event)

    if events:
        for event in events:
            await add_webhook_event_for_action(event)


def get_webhook_data_object(
        entity: schemas.WebhookEntityEnum,
        base_data: dict,
        obj_data: dict,
        data_type: Type[T],
):
    match entity:
        case schemas.WebhookEntityEnum.ORDER:
            return schemas.WebhookOrderData(
                **base_data, entity=entity, data=data_type(**obj_data)
            )
        case schemas.WebhookEntityEnum.PAYMENT:
            return schemas.WebhookPaymentData(
                **base_data, entity=entity, data=data_type(**obj_data)
            )
        case schemas.WebhookEntityEnum.EWALLET_PAYMENT:
            return schemas.WebhookEWalletPaymentData(
                **base_data, entity=entity, data=data_type(**obj_data)
            )
        case schemas.WebhookEntityEnum.CHAT:
            return schemas.WebhookChatData(
                **base_data, entity=entity, data=data_type(**obj_data)
            )
        case schemas.WebhookEntityEnum.REVIEW:
            return schemas.WebhookReviewData(
                **base_data, entity=entity, data=data_type(**obj_data)
            )
        case schemas.WebhookEntityEnum.TICKET:
            return schemas.WebhookTicketData(
                **base_data, entity=entity, data=data_type(**obj_data)
            )
        case schemas.WebhookEntityEnum.TEXT_NOTIFICATION:
            return schemas.WebhookNotificationData(
                **base_data, entity=entity, data=data_type(**obj_data)
            )


async def validate_webhook(webhook_id: int, entity: str) -> Webhook | str:
    webhook = await Webhook.get(webhook_id)
    if not webhook:
        return "webhook not found"
    if webhook.blocked:
        return "webhook blocked"
    if not webhook.is_enabled:
        return "webhook disabled"
    if entity not in webhook.entities:
        return f"entity not in webhook {entity} {webhook.entities}"

    return webhook


async def send_webhook_data(
        logger: JSONLogger, webhook_id: int, data: schemas.WebhookKafkaData,
        event_uuid: str, lang: str,
) -> schemas.WebhookJournalDataSchema:
    webhook = await validate_webhook(webhook_id, data.entity.value.upper())
    if isinstance(webhook, str):
        if webhook == "webhook blocked":
            raise WebhookExternalError(
                await f("admin webhooks blocked error message", lang)
            )
        raise WebhookInternalError(webhook)

    is_make_hook = MAKE_HOOK_DOMAIN in webhook.endpoint_url
    is_zapier_hook = ZAPIER_HOOK_DOMAIN in webhook.endpoint_url

    request_body = json.loads(data.json())

    if (is_make_hook and MAKE_HOOK_ENTITY_BY_NAME or is_zapier_hook and
            ZAPIER_HOOK_ENTITY_BY_NAME):
        entity = request_body["entity"]
        request_body[entity] = request_body.pop("data")

    payload = json.dumps(request_body)
    signature = make_webhook_signature(webhook, payload)
    headers = {
        "X-Webhook-Event-Id": event_uuid,
        "X-Webhook-Signature": signature,
    }

    request_data = {
        'url': webhook.endpoint_url,
        'headers': headers,
        'json': request_body,
    }

    logger.debug(
        "webhook, start make_request\n", {
            "webhook": webhook,
            "request_data": request_data,
            "event_id": event_uuid,
        }
    )

    journal_data = schemas.WebhookJournalDataSchema(
        request=schemas.WebhookJournalDataRequestOrResponseSchema(
            data=request_data,
        ),
    )
    timeout = ClientTimeout(total=30)
    async with ClientSession(timeout=timeout) as session:
        try:
            resp: ClientResponse = await session.post(**request_data)
            try:
                response_data = await resp.json()
            except Exception:
                response_data = None

            logger.debug(
                "webhook, end make_request\n", {
                    "webhook": webhook,
                    "event_uuid": event_uuid,
                    "response_data": response_data,
                    "response_headers": resp.headers,
                    "response_status": resp.status,
                }
            )

            response_json = None
            try:
                response_json = await resp.json()
            except Exception:
                ...

            if resp.status >= 400:
                if resp.status == 429:
                    retry_after = resp.headers.get('Retry-After')
                    if retry_after:
                        journal_data.response = (
                            schemas.WebhookJournalDataRequestOrResponseSchema(
                                data={
                                    "data": None, "headers": dict(resp.headers),
                                    "status": resp.status
                                },
                            )
                        )
                        journal_data.detail = f"rate limit, retry after: {retry_after}"
                        raise WebhookRateLimitError(
                            str(retry_after),
                            journal_data=journal_data,
                        )

                logger.error(
                    f"webhook, make_request error\n", {
                        "webhook": webhook,
                        "event_uuid": event_uuid,
                        "response_data": response_data,
                        "response_headers": resp.headers,
                        "response_status": resp.status,
                        "response_reason": resp.reason,
                        "response_json": response_json,
                    }
                )

                journal_data.detail = f"status: {resp.status}, raw: {resp.text}"
                journal_data.response = (
                    schemas.WebhookJournalDataRequestOrResponseSchema(
                        data={
                            "data": None, "headers": dict(resp.headers),
                            "status": resp.status,
                            "json": response_json,
                        },
                    )
                )
                raise WebhookExternalError(
                    f"status: {resp.status}", journal_data
                )

            journal_data.response = schemas.WebhookJournalDataRequestOrResponseSchema(
                data={
                    "data": response_data, "headers": dict(resp.headers),
                    "status": resp.status, "json": response_json,
                },
            )
            return journal_data
        except AsyncioTimeoutError as e:
            logger.error(
                f"webhook, make_request error {repr(e)}\n", {
                    "webhook": webhook,
                    "event_uuid": event_uuid,
                }
            )

            raise WebhookExternalError("timeout error", journal_data)
        except ClientResponseError as e:
            if e.status == 429:
                retry_after = e.headers.get('Retry-After')
                if retry_after:
                    journal_data.response = (
                        schemas.WebhookJournalDataRequestOrResponseSchema(
                            data={
                                "data": None, "headers": dict(e.headers),
                                "status": e.status
                            },
                        )
                    )
                    journal_data.detail = f"rate limit, retry after: {retry_after}"
                    raise WebhookRateLimitError(
                        str(retry_after),
                        journal_data=journal_data,
                    )
            else:
                logger.error(
                    f"webhook, make_request error {repr(e)}\n", {
                        "webhook": webhook,
                        "event_uuid": event_uuid,
                        "response_data": response_data,
                        "response_headers": resp.headers if resp else None,
                        "response_status": resp.status if resp else None,
                        "response_reason": resp.reason if resp else None,
                    }
                )

                journal_data.detail = f"status: {e.status}, raw: {resp.text}"
                journal_data.response = (
                    schemas.WebhookJournalDataRequestOrResponseSchema(
                        data={
                            "data": None,
                            "headers": dict(e.headers),
                            "status": e.status,
                        },
                    ))
                raise WebhookExternalError(
                    f"status: {e.status}", journal_data
                )
        except ClientConnectorError as e:
            logger.error(
                f"webhook, make_request error {repr(e)}\n", {
                    "webhook": webhook,
                    "event_uuid": event_uuid,
                }
            )

            raise WebhookExternalError(str(e), journal_data)
        except WebhookExternalError as e:
            raise e

        except Exception as e:
            logger.error(
                f"webhook, make_request error {repr(e)}\n", {
                    "webhook": webhook,
                    "event_uuid": event_uuid,
                }
            )

            raise WebhookInternalError(str(e), journal_data)


def make_webhook_signature(webhook: Webhook, payload: str):
    timestamp = str(time.time())
    message_with_timestamp = timestamp + str(payload)
    signature = hmac.new(
        webhook.hook_id.encode(),
        msg=message_with_timestamp.encode(),
        digestmod=hashlib.sha256
    ).hexdigest()

    return f"t={timestamp},s={signature}"


def validate_webhook_signature(
        secret: str,
        timestamp: str,
        payload: str,
        signature: str
) -> bool:
    message_with_timestamp = timestamp + payload
    new_signature = hmac.new(
        secret.encode(),
        msg=message_with_timestamp.encode(),
        digestmod=hashlib.sha256
    ).hexdigest()

    return hmac.compare_digest(new_signature, signature)


def get_webhook_action_by_order_status(
        status: str, is_paid_new_order: bool
) -> schemas.WebhookActionEnum:
    if status == "open_unconfirmed" or is_paid_new_order:
        return schemas.WebhookActionEnum.CREATED
    else:
        return schemas.WebhookActionEnum.CHANGE_STATUS


async def prepare_data_for_ticket_webhook(
        ticket: CRMTicket, group: Group, user: User
) -> schemas.WebhookTicketDataSchema:
    base_schema = schemas.CRMTicketBaseSchema.from_orm(ticket)
    bot = await ClientBot.get(ticket.bot_id)

    schema = schemas.WebhookTicketDataSchema(
        **base_schema.dict(),
        business_name=group.name,
        bot_name=bot.display_name if bot else None,
        profile_id=group.id,
        items_text=ticket.title,
        first_name=user.first_name,
        last_name=user.last_name,
        full_name=user.full_name,
        email=user.email,
        photo_url=user.photo_url,
        status_history=await crud.get_crm_ticket_status_history(ticket.id),
    )

    return await add_user_to_webhook_data(user, schema, group)


async def prepare_data_for_review_webhook(
        review: Review, user: User, group: Group
) -> schemas.WebhookReviewDataSchema:
    base_schema = schemas.CRMBaseReviewSchema.from_orm(review)
    bot = await ClientBot.get(review.bot_id)

    schema = schemas.WebhookReviewDataSchema(
        **base_schema.dict(),
        profile_id=group.id,
        profile_name=group.name,
        business_name=group.name,
        bot_id=bot.id if bot else None,
        bot_name=bot.display_name if bot else None,
        first_name=user.first_name,
        last_name=user.last_name,
        full_name=user.full_name,
        email=user.email,
        photo_url=user.photo_url,
        change_date=review.change_date,
        menu_in_store_comment=review.additional_text,
    )

    return await add_user_to_webhook_data(user, schema, group)


async def prepare_data_for_chat_webhook(
        chat: Chat,
        group: Group,
        message: ChatMessage,
        user: User,
        media: MediaObject | None = None,
) -> schemas.WebhookNewChatMessageSchema:
    base_schema = schemas.CRMBaseChatSchema.from_orm(chat)
    bot = await ClientBot.get(chat.bot_id)

    sent_by_user_name = None
    sent_by_user_email = None
    sent_by_user_photo_url = None
    vm_name = None

    if message.sent_by in (
            schemas.ChatMessageSentByEnum.USER,
            schemas.ChatMessageSentByEnum.MANAGER,
    ) and message.sent_by_user_id:
        message_user: User = await User.get_by_id(message.sent_by_user_id)
        sent_by_name = message_user.name
        sent_by_user_name = message_user.name
        sent_by_user_email = message_user.email
        sent_by_user_photo_url = message_user.photo_url
    elif message.sent_by == schemas.ChatMessageSentByEnum.VM:
        message_vm = await crud.get_vm_by_vmc(message.vmc_id)
        sent_by_name = message_vm.name
        vm_name = message_vm.name
    else:
        group = await Group.get(group.id)
        sent_by_name = group.name

    if media:
        media_url = media.url
        media_mime_type = media.mime_type
        media_file_size = media.file_size
        media_original_file_name = media.original_file_name
        media_preview_url = make_preview_url(media_mime_type, media.file_path)
    else:
        media_url = None
        media_mime_type = None
        media_file_size = None
        media_original_file_name = None
        media_preview_url = None

    message = schemas.ChatMessageSchema(
        id=message.id,
        chat_id=message.chat_id,
        sent_by=message.sent_by,
        sent_by_name=sent_by_name,
        sent_by_user_id=message.sent_by_user_id,
        sent_by_user_name=sent_by_user_name,
        sent_by_user_email=sent_by_user_email,
        sent_by_user_photo_url=sent_by_user_photo_url,
        vmc_id=message.vmc_id,
        vm_name=vm_name,
        content_type=message.content_type,
        text=message.text,
        media_id=message.media_id,
        media_url=media_url,
        media_mime_type=media_mime_type,
        media_file_size=media_file_size,
        media_preview_url=media_preview_url,
        media_original_file_name=media_original_file_name,
        content=message.content,
        is_mailing=message.is_mailing,
        time_created=message.time_created,
    )

    chat = schemas.WebhookChatDataSchema(
        **base_schema.dict(),
        profile_id=group.id,
        profile_name=group.name,
        business_name=group.name,
        bot_name=bot.display_name if bot else None,
        first_name=user.first_name,
        last_name=user.last_name,
        full_name=user.full_name,
        email=user.email,
        photo_url=user.photo_url,

    )

    schema = schemas.WebhookNewChatMessageSchema(chat=chat, message=message)

    return await add_user_to_webhook_data(user, schema, group)


async def prepare_data_for_notification_webhook(
        text_notification: TextNotification,
) -> schemas.WebhookTextNotificationSchema:
    base_schema = schemas.BaseCRMTextNotificationSchema.from_orm(text_notification)
    schema = schemas.WebhookTextNotificationSchema(**base_schema.dict())

    return await add_user_to_webhook_data(
        text_notification.user_id, schema, text_notification.profile_id
    )


async def prepare_data_for_payment_webhook(
        invoice_schema: schemas.InvoiceSchema,
        invoice: Invoice,
        profile_id: int,
        user_id: int | None = None,
) -> schemas.WebhookPaymentDataSchema:
    schema = schemas.WebhookPaymentDataSchema(**invoice_schema.dict())

    schema.invoice_template_id = invoice.invoice_template_id
    schema.menu_in_store_id = invoice.menu_in_store_id
    schema.payment_bot_menu_id = invoice.payment_bot_menu_id
    schema.payment_bot_id = invoice.payment_bot_id
    schema.message_id = invoice.message_id
    schema.paid_datetime = invoice.paid_datetime
    schema.uuid_id = invoice.uuid_id

    order_payment = await StoreOrderPayment.get(invoice_id=invoice.id)
    if order_payment:
        incust_account_name = order_payment.incust_account_name

        order_payment_schema = schemas.WebhookOrderOrInvoicePayment(
            name=order_payment.name,
            description=order_payment.description,
            comment=order_payment.comment,
            post_payment_info=order_payment.post_payment_info,
            label_comment=order_payment.label_comment,
            payment_method=order_payment.payment_method,
            payment_settings_id=order_payment.payment_settings_id,
            incust_account_name=incust_account_name,
            incust_account_id=order_payment.incust_account_id,
            incust_card_id=order_payment.incust_card_id,
            price=order_payment.price,
        )

        schema.payment = order_payment_schema

    return await add_user_to_webhook_data(user_id or schema.user_id, schema, profile_id)


async def prepare_data_for_order_webhook(
        order_schema: schemas.OrderSchema, tz: str,
        profile_id: int,
) -> schemas.WebhookOrderDataSchema:
    schema = schemas.WebhookOrderDataSchema(**order_schema.dict())
    if order_schema.desired_delivery_date:
        if (order_schema.shipment and order_schema.shipment.delivery_datetime_mode ==
                "date"):
            target_tz = pytz.timezone(tz)
            local = order_schema.desired_delivery_date.astimezone(target_tz)
            schema.desired_delivery_date = local.date()
            schemas.desired_delivery_datetime = None
        elif (order_schema.shipment and order_schema.shipment.delivery_datetime_mode
              == "datetime"):
            schema.desired_delivery_datetime = order_schema.desired_delivery_date
            schema.desired_delivery_date = None
        else:
            schema.desired_delivery_date = None
            schema.desired_delivery_datetime = None

    return await add_user_to_webhook_data(order_schema.user_id, schema, profile_id)


async def add_user_to_webhook_data(user: int | User, schema, profile_id: int | Group):
    if user and not isinstance(user, User):
        user = await User.get_by_id(int(user))
    if not user or user.is_anonymous:
        schema.user = None
        return schema

    if profile_id and isinstance(profile_id, Group):
        profile_id = profile_id.id
    if not profile_id:
        schema.user = None
        return schema

    can_chat = False
    chat_id = None

    if await crud.check_access_to_action(
            "customer_profiles:read", "profile", profile_id
    ):
        owned_profiles = [
            schemas.CRMUserOwnedProfile(
                id=profile.id,
                name=profile.name,
                logo_url=media.url if media else None,
                logo_thumbnail_url=make_preview_url(
                    media.media_type, media.file_path,
                    max_size=128
                ) if media else None
            )
            for profile, media in
            await crud.get_user_owned_profiles(user.id)
        ]
    else:
        owned_profiles = None

    bot = await ClientBot.get(group_id=profile_id)
    if bot and bot.bot_type in user.messangers:
        can_chat = True
        chat = await crud.get_or_create_chat(
            schemas.ChatTypeEnum.USER_WITH_GROUP,
            user.id,
            profile_id,
            bot.id,
        )
        chat_id = chat.id

    user_schema = schemas.CRMUser(
        profile_id=profile_id,
        info=schemas.CRMUserInfo.from_orm(user),
        custom_fields=await crud.get_custom_fields_for_user(
            profile_id, user.id
        ),
        tags=await crud.get_user_tag_names(profile_id, user.id),
        chat_id=chat_id,
        can_chat=can_chat,
        owned_profiles=owned_profiles,
    )
    schema.user = user_schema

    return schema
