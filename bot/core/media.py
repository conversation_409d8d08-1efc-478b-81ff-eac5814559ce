from __future__ import annotations

import logging

from aiogram.types.mixins import Downloadable
from aiowhatsapp.types import Media
from psutils.media import download_file as _download_file

import config as cfg
from config import STATIC_DB
from core.messangers_adapters import types
from core.messangers_adapters.helpers import detect_bot_type
from utils.media import save_file


async def download_file(message: types.Message, path_to_save: str = None, is_immutable_path: bool = False) -> str | None:
    if message.content_type not in cfg.MEDIA_WITH_CAPTION:
        return None

    file: Downloadable | Media = getattr(message, message.content_type)
    if not file:
        return None

    if message.content_type == "photo":
        file = file[-1]

    try:
        bot_type = await detect_bot_type(message)
        if bot_type == "telegram":
            if is_immutable_path:
                file_info = await file.get_file()
                return save_file(file_info.file_path, path_to_save)
            return await _download_file(message, path_to_save or STATIC_DB)
        else:
            file_data = await file.download(dest_dir=path_to_save)
            file_path = file_data.file_path
    except Exception as e:
        logger = logging.getLogger()
        logger.error(e, exc_info=True)
        return

    return file_path
