import aiowhatsapp
from typing import Literal

import schemas
from core.messangers_adapters import InlineKeyboard, InlineKeyboardButton, UrlKeyboardButton
from utils.redefined_classes import InlineKb
from utils.text import fd, f
from db.models import ClientBot

from .callback_data import IncustReferralCallbackData, IncustReferralQrOrLinkCallbackData, IncustReferralShareMessage


async def get_incust_referral_keyboard(
    brand_id: int,
    lang: str,
    referral_code: str,
    bot_type: schemas.BotTypeLiteral = "telegram",
    store_id: int | None = None,
) -> InlineKb | aiowhatsapp.types.ReplyKeyboard:
    keyboard = InlineKeyboard()

    if bot_type == "telegram":
        texts = await fd(
            {
                "accept_invitation": "incust loyalty accept invitation header",
            }, lang
        )
    else:
        texts = await fd(
            {
                "accept_invitation": "incust loyalty accept invitation short header",
            }, lang
        )

    callback_data = IncustReferralCallbackData(
        referral_code=referral_code,
        brand_id=brand_id,
        store_id=store_id,
    ).to_str()

    keyboard.add_buttons(
        InlineKeyboardButton(
            texts["accept_invitation"],
            callback_data
        )
    )

    if bot_type == "telegram":
        return keyboard.to_telegram()
    return keyboard.to_whatsapp()


async def get_incust_referral_qr_or_link_keyboard(
    referral_code: str,
    lang: str,
    brand_id: int,
    bot_type: schemas.BotTypeLiteral = "telegram",
    store_id: int | None = None,
) -> InlineKb | aiowhatsapp.types.ReplyKeyboard:
    keyboard = InlineKeyboard()

    texts = await fd(
        {
            "show_qr": "qr code referral button",
            "show_link": "get link referral button",
        }, lang
    )

    qr_callback_data = IncustReferralQrOrLinkCallbackData(
        content_type="qr",
        referral_code=referral_code,
        store_id=store_id,
        is_web="1",
        with_qr="1",
    ).to_str()

    # link_callback_data = IncustReferralQrOrLinkCallbackData(
    #     content_type="link",
    #     referral_code=referral_code,
    #     store_id=store_id,
    #     is_web=is_web,
    # ).to_str()

    link_callback_data = IncustReferralShareMessage(
        brand_id=brand_id,
        referral_code=referral_code,
    ).to_str()    

    keyboard.add_buttons(
        InlineKeyboardButton(
            texts["show_qr"],
            qr_callback_data
        )
    )

    keyboard.add_buttons(
        InlineKeyboardButton(
            texts["show_link"],
            link_callback_data
        )
    )

    if bot_type == "telegram":
        return keyboard.to_telegram()
    return keyboard.to_whatsapp()


async def get_accept_invitation_keyboard(
    lang: str, link: str, bot: ClientBot,
) -> InlineKb | aiowhatsapp.types.ReplyKeyboard:
    keyboard = InlineKeyboard(row_width=1)

    button_text = await f("incust loyalty accept invitation header", lang)
    keyboard.add_buttons(
        UrlKeyboardButton(
            button_text,
            url=link,
        ),
    )
    if bot.bot_type == "telegram":
        return keyboard.to_telegram(row_width=1, url_as_web_app=False)
    elif bot.bot_type == "whatsapp":
        return keyboard.to_whatsapp()


async def get_incust_share_qr_keyboard(
    lang: str, referral_code: str,
    bot_type: Literal["telegram", "whatsapp"] = "telegram",
):
    keyboard = InlineKeyboard(row_width=1)

    if bot_type == "telegram":
        texts = await fd(
            {
                "show_qr": "incust loyalty referral invite by qr button",
                "friend_without_messanger": {
                    "variable": "incust loyalty referral friend without messanger button",
                    "text_kwargs": {
                        "messanger_name": "Telegram"
                    }
                }
            }, lang
        )
    else:
        texts = await fd(
            {
                "show_qr": "qr code referral button",
                "friend_without_messanger": {
                    "variable": "incust loyalty referral friend without messanger button wa",
                    "text_kwargs": {
                        "messanger_name": "WhatsApp"
                    }
                }
            }, lang,
        )

    qr_callback_data = IncustReferralQrOrLinkCallbackData(
        content_type="q",
        referral_code=referral_code,
    ).to_str()

    link_callback_data = IncustReferralQrOrLinkCallbackData(
        content_type="l",
        referral_code=referral_code,
        is_web="1",
        with_qr="1",
    ).to_str()

    keyboard.add_buttons(
        InlineKeyboardButton(
            texts['show_qr'],
            qr_callback_data
        ),
        InlineKeyboardButton(
            texts['friend_without_messanger'],
            link_callback_data
        )
    )

    if bot_type == "telegram":
        return keyboard.to_telegram()
    return keyboard.to_whatsapp()
