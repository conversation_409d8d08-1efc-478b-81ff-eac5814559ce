import asyncio
import logging
from incust_api.api import client

import schemas
from core.loyalty.incust_api import incust
from core.messangers_adapters import types as core_types
from db import crud
from db.models import Brand, ClientBot, User
from utils.text import f, html_to_markdown
from .keyboards import get_accept_invitation_keyboard, get_incust_share_qr_keyboard


async def is_incust_referral_active(user: User, brand: Brand, lang: str) -> bool:
    try:
        settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(brand_id=brand.id)
        )
        if not settings:
            return False

        try:
            async with incust.client.LoyaltyApi(settings, user=user, lang=lang) as api:
                loyalty_settings = await api.loyalty(
                    loyalty_id=settings.loyalty_id
                )
        except client.ApiException as ex:
            logging.error(f"Error getting loyalty settings: {ex.reason}")
            return False

        return bool(
            loyalty_settings and loyalty_settings.referral_program and
            loyalty_settings.referral_program.active == 1
        )
    except Exception as ex:
        logging.error(ex, exc_info=True)
        return False


async def send_referral_share_message(
        bot: ClientBot,
        lang: str,
        loyalty_settings: schemas.LoyaltySettingsClientSchema,
        message: core_types.AnswerObject,
        loyalty_photo: str,
        url: str,
        ref_code: str,
        user: User,
):
    logger = logging.getLogger("debugger")
    title = loyalty_settings.referral_program.title
    desc = loyalty_settings.referral_program.description
    if loyalty_settings.referral_program.referral_title:
        title = loyalty_settings.referral_program.referral_title
    if loyalty_settings.referral_program.referral_description:
        desc = loyalty_settings.referral_program.referral_description

    message_text = await f("incust loyalty accept invitation qr message footer", lang)

    if bot.bot_type == "whatsapp":
        message_text = html_to_markdown(message_text)
    await asyncio.sleep(1)
    await message.answer(message_text)

    message_text = f"<b>{title}</b>"
    message_text += f"\n\n{desc}"
    if bot.bot_type == "telegram":
        message_text += f"\n\n{await f('forward link text', lang, link=url)}"
    else:
        message_text += f"\n\n{await f('forward text link text', lang, link=url)}"
    keyboard = await get_accept_invitation_keyboard(lang, url, bot)

    image_message = None
    if loyalty_photo:
        if bot.bot_type == "telegram":
            image_message = await message.answer_photo(
                photo=loyalty_photo, caption=message_text, reply_markup=keyboard
            )
        elif bot.bot_type == "whatsapp":
            message_text = html_to_markdown(message_text)
            image_message = await message.answer_image(
                loyalty_photo, caption=message_text
            )
    else:
        image_message = await message.answer(message_text, reply_markup=keyboard)

    if image_message and bot.bot_type == "telegram":
        logger.debug(
            f"*** REFERRAL_SHARE_IMAGE_MESSAGE user={user.full_name}, "
            f"msg_text={image_message.text or image_message.caption}, "
            f"date={image_message.date.strftime('%Y-%m-%d %H:%M:%S.%f')}, "
            f"timestamp={image_message.date.timestamp()}"
        )

    message_text = await f("incust loyalty accept invitation qr message header", lang)

    if bot.bot_type == "whatsapp":
        message_text = html_to_markdown(message_text)

    keyboard = await get_incust_share_qr_keyboard(lang, ref_code, bot.bot_type)
    await asyncio.sleep(1)
    second_message = await message.answer(message_text, reply_markup=keyboard)
    if second_message and bot.bot_type == "telegram":
        logger.debug(
            f"*** REFERRAL_SHARE_SECOND_MESSAGE user={user.full_name}, "
            f"msg_text={second_message.text}, "
            f"date={second_message.date.strftime('%Y-%m-%d %H:%M:%S.%f')}, "
            f"timestamp={second_message.date.timestamp()}"
        )
