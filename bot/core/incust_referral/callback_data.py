from psutils.callback_data import CallbackData
from pydantic import Field


class IncustReferralCallbackData(CallbackData, callback_mode="incust_referral"):
    referral_code: str = Field(alias="r_c")
    brand_id: int = Field(alias="b_id")
    store_id: int | None = Field(alias="s_id")


class IncustReferralQrOrLinkCallbackData(CallbackData, callback_mode="i_ref_qr_or_link"):
    content_type: str = Field(alias="c_t")
    referral_code: str = Field(alias="r_c")
    store_id: int | None = Field(alias="s_id")
    is_web: str | None = Field(alias="i")
    with_qr: str | None = Field(alias="w")


class IncustReferralShareMessage(CallbackData, callback_mode="incust_share_msg"):
    brand_id: int = Field(alias="b_id")
    referral_code: str = Field(alias="r_c")
