from typing import Literal

from core.messangers_adapters import InlineKeyboard, InlineKeyboardButton
from core.topup_ewallet.callback_data import ConfirmTopUpEWalletCallbackData
from utils.text import f


async def get_yes_or_no_keyboard(
        bot_type: Literal["telegram", "whatsapp"],
        lang
):
    keyboard = InlineKeyboard(row_width=2)
    yes_callback_data = ConfirmTopUpEWalletCallbackData(answer=True).to_str()
    no_callback_data = ConfirmTopUpEWalletCallbackData(answer=False).to_str()

    keyboard.add_buttons(
        InlineKeyboardButton(
            await f("INVOICE_USER_SUM_CHANGE_AMOUNT_BUTTON", lang),
            no_callback_data
        ),

        InlineKeyboardButton(
            await f("client bot ewallet payment confirm button", lang),
            yes_callback_data
        ),
    )

    return keyboard.to_messanger(bot_type)
