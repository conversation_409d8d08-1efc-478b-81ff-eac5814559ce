from core import messangers_adapters as ma
from core.topup_ewallet.callback_data import (
    ConfirmTopUpEWalletCallbackData,
    TopUpEwalletCallbackData,
)
from core.topup_ewallet.processor.processor import (
    TopUpEWalletData,
    TopUpEWalletProcessor,
)
from core.topup_ewallet.processor.state import TopUpEWalletState
from db.models import ClientBot, User


@ma.handler.button("reply", "list")
async def topup_ewallet_button_handler(
        query: ma.<PERSON><PERSON>,
        state: ma.FSMContext,
        ewallet_data: TopUpEwalletCallbackData,
        bot: ClientBot,
        user: User,
        lang: str
):
    processor = await TopUpEWalletProcessor.setup(query, state, user, bot, lang,
        TopUpEWalletData(
            ewallet_id=ewallet_data.ewallet_id,
            group_id=ewallet_data.group_id,
        )
    )
    await TopUpEWalletState.EnterAmount.set()
    await processor.send_state_menu()


@ma.handler.button("reply", "list")
async def topup_ewallet_confirm_button_handler(
        query: ma.<PERSON><PERSON><PERSON>,
        state: ma.FSMContext,
        data: ConfirmTopUpEWalletCallbackData,
        bot: ClientBot,
        user: User,
        lang: str
):
    processor = await TopUpEWalletProcessor.setup(query, state, user, bot, lang)
    if data.answer:
        await processor.confirm_topup_ewallet()
    else:
        await processor.update_data(entered_amount=None)
        await TopUpEWalletState.EnterAmount.set()
        await processor.send_state_menu()



def register_topup_ewallet_button_handlers(dp: ma.DispatcherType):
    topup_ewallet_button_handler.setup(
        dp,
        TopUpEwalletCallbackData.get_filter("ewallet_data"),
        state="*",
        messangers_kwargs={
            "telegram": {
                "callback_mode": "topup_ewallet",
            },
            "whatsapp": {
                "reply_mode": "topup_ewallet"
            }
        }
    )

    topup_ewallet_confirm_button_handler.setup(
        dp,
        ConfirmTopUpEWalletCallbackData.get_filter("data"),
        state=[TopUpEWalletState.Confirmation],
        messangers_kwargs={
            "telegram": {
                "callback_mode": "confirm_topup_ewallet",
            },
            "whatsapp": {
                "reply_mode": "confirm_topup_ewallet"
            }
        }
    )
