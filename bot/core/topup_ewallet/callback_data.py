from psutils.callback_data import CallbackData
from pydantic import Field


class TopUpEwalletCallbackData(CallbackData, callback_mode="topup_ewallet"):
    ewallet_id: int = Field(alias="ewid")
    group_id: int = Field(alias="grpid")
    # special_account_id: str = Field(alias="sacid")
    used_credit: float | None = Field(None, alias="used_credit")


class ConfirmTopUpEWalletCallbackData(CallbackData, callback_mode="confirm_topup_ewallet"):
    answer: bool | None = Field(None, alias="answer")
