import logging

import aiowhatsapp as wa
from incust_api.api import client, term

import schemas
from client.incust_referral.keyboards import get_incust_share_keyboard
from config import P4S_API_URL
from core.loyalty.incust_api import incust
from core.loyalty.customer_service import get_or_create_incust_customer
from db import crud
from db.models import Brand, ClientBot, Group, User, UserAnalyticAction
from schemas import ReferralProgramSummary
from utils.message import send_tg_message
from utils.numbers import format_currency
from utils.text import f, html_to_markdown


async def __send_loyalty_not_connected_message(
        user: User,
        bot: ClientBot,
        lang: str,
        brand_name: str,
        wa_bot = None
):
    """Відправляє повідомлення про те, що лояльність не підключена"""
    message_text = await f("loyalty not connected text", lang, brand_name=brand_name)

    if bot.bot_type == "telegram":
        await send_tg_message(
            user.chat_id, "text",
            text=message_text,
            bot_token=bot.token,
        )
    elif bot.bot_type == "whatsapp" and wa_bot:
        await wa_bot.send_message(
            user.wa_phone,
            text=message_text,
        )


async def share_and_earn(
        user: User,
        brand: Brand,
        lang: str,
        bot: ClientBot,
):
    wa_bot = None
    if bot.bot_type == "whatsapp":
        wa_bot = wa.WhatsappBot(
            bot.token,
            bot.whatsapp_from,
        )

    try:
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(brand_id=brand.id)
        )

        if not loyalty_settings:
            await __send_loyalty_not_connected_message(
                user, bot, lang, brand.name, wa_bot
            )
            return

        incust_customer = await get_or_create_incust_customer(user, loyalty_settings)

        if not incust_customer:
            await __send_loyalty_not_connected_message(
                user, bot, lang, brand.name, wa_bot
            )
            return

        try:
            async with incust.term.SettingsApi(loyalty_settings, lang=lang) as api:
                terminal_settings = await api.settings()
        except term.ApiException as ex:
            logging.error(f"Error getting terminal settings: {ex.reason}")
            terminal_settings = None
        except Exception as ex:
            logging.error(f"Unexpected error getting terminal settings: {ex}")
            terminal_settings = None

        if loyalty_settings.loyalty_id and terminal_settings:
            try:
                async with incust.client.LoyaltyApi(loyalty_settings, user=user, lang=lang) as api:
                    ref_code = await api.referral_program_code_get(
                        loyalty_id=loyalty_settings.loyalty_id
                    )
            except client.ApiException as ex:
                logging.error(f"Error getting referral code: {ex.reason}")
                ref_code = None
            except Exception as ex:
                logging.error(f"Unexpected error getting referral code: {ex}")
                ref_code = None
        else:
            await __send_loyalty_not_connected_message(
                user, bot, lang, brand.name, wa_bot
            )
            return

        if not ref_code or not ref_code.code:
            if bot.bot_type == "telegram":
                await send_tg_message(
                    user.chat_id, "text",
                    text=await f("incust loyalty not valid referral code", lang),
                    bot_token=bot.token,
                )
            elif bot.bot_type == "whatsapp":
                if wa_bot:
                    await wa_bot.send_message(
                        user.wa_phone,
                        text=await f("incust loyalty not valid referral code", lang),
                    )
            return

        try:
            async with incust.client.LoyaltyApi(loyalty_settings, user=user, lang=lang) as api:
                loyalty_settings_data = await api.loyalty_settings(
                    loyalty_id=loyalty_settings.loyalty_id
                )
        except client.ApiException as ex:
            logging.error(f"Error getting loyalty settings: {ex.reason}")
            loyalty_settings_data = None
        except Exception as ex:
            logging.error(f"Unexpected error getting loyalty settings: {ex}")
            loyalty_settings_data = None
        if (not loyalty_settings_data or not loyalty_settings_data.referral_program or
                loyalty_settings_data.referral_program.active != 1):
            if bot.bot_type == "telegram":
                await send_tg_message(
                    user.chat_id, "text",
                    text=await f("incust loyalty referral disabled error", lang),
                    bot_token=bot.token,
                )
            elif bot.bot_type == "whatsapp":
                if wa_bot:
                    await wa_bot.send_message(
                        user.wa_phone,
                        text=await f("incust loyalty referral disabled error", lang),
                    )
            return

        loyalty_photo = None
        if loyalty_settings_data.referral_program.referrer_logo:
            loyalty_photo = loyalty_settings_data.referral_program.referrer_logo.url
        else:
            if terminal_settings.loyalty and terminal_settings.loyalty.photos:
                if len(terminal_settings.loyalty.photos) >= 1:
                    loyalty_photo = terminal_settings.loyalty.photos[0]
        if not loyalty_photo:
            loyalty_photo = (f"{P4S_API_URL}/static/images/7loc-com-white-square"
                             f"-preview.jpg")

        try:
            async with incust.client.LoyaltyApi(loyalty_settings, user=user, lang=lang) as api:
                referral_summary = await api.referral_program_summary(
                    loyalty_id=loyalty_settings.loyalty_id
                )
        except client.ApiException as ex:
            logging.error(f"Error getting referral summary: {ex.reason}")
            referral_summary = None
        except Exception as ex:
            logging.error(f"Unexpected error getting referral summary: {ex}")
            referral_summary = None
        await __send_referral_summary_message(
            referral_summary, brand, lang, bot,
            loyalty_settings_data, loyalty_photo,
            ref_code.code, user,
        )

        await UserAnalyticAction.save_button_click(user, bot, "shop share and earn")
    except Exception as ex:
        logging.error(ex, exc_info=True)
        error_message = await f("incust loyalty error", lang)
        if bot.bot_type == "telegram":
            await send_tg_message(
                user.chat_id, "text",
                text=error_message,
                bot_token=bot.token,
            )
        elif bot.bot_type == "whatsapp":
            if wa_bot:
                await wa_bot.send_message(
                    user.wa_phone,
                    text=error_message,
                )


async def __send_referral_summary_message(
        referral_summary: ReferralProgramSummary,
        brand: Brand,
        lang: str,
        bot: ClientBot,
        loyalty_settings: client.m.LoyaltySettings,
        photo: str,
        ref_code: str,
        user: User,
):
    referrals_count = referral_summary.referrals_count
    group = await Group.get(brand.group_id)

    bonuses_amount = None
    accounts_amount = None
    if referral_summary.rewards:
        if referral_summary.rewards.bonuses:
            bonuses_string_array = []
            for bonus in referral_summary.rewards.bonuses:
                if bonus.amount and bonus.currency:
                    currency_str = format_currency(
                        bonus.amount, bonus.currency, locale=group.lang
                    )
                    bonuses_string_array.append(currency_str)
            if len(bonuses_string_array) > 0:
                bonuses_amount = ", ".join(bonuses_string_array)

        if referral_summary.rewards.accounts:
            accounts_string_array = []
            for account in referral_summary.rewards.accounts:
                if account.amount:
                    currency_str = format_currency(
                        account.amount, account.currency or group.currency,
                        locale=group.lang
                    )
                    accounts_string_array.append(currency_str)
            if len(accounts_string_array) > 0:
                accounts_amount = ", ".join(accounts_string_array)

    if not bonuses_amount and not accounts_amount:
        if group and group.currency:
            bonuses_amount = format_currency(0, group.currency, locale=group.lang)
        else:
            bonuses_amount = "0"

    title = loyalty_settings.referral_program.title
    if loyalty_settings.referral_program.referrer_title:
        title = loyalty_settings.referral_program.referrer_title
    message_text = f"<b>{title}</b>\n\n"

    message_text += await f(
        "incust loyalty referral summary message", lang,
        referrals_count=referrals_count,
        bonuses=bonuses_amount or accounts_amount,
    )

    if accounts_amount and bonuses_amount:
        for account in referral_summary.rewards.accounts:
            amount_str = format_currency(
                account.amount, account.currency or group.currency, locale=group.lang
            )
            message_text += f"\n{account.title}: {amount_str}"

    keyboard = await get_incust_share_keyboard(bot, brand, lang, ref_code, bot.bot_type)
    if bot.bot_type == "telegram":
        return await send_tg_message(
            user.chat_id, "photo", photo=photo, text=message_text, keyboard=keyboard,
            bot_token=bot.token,
        )
    elif bot.bot_type == "whatsapp":
        link = await brand.get_short_token_url(
            user, bot.id, lang, "profile/share_and_earn"
        )
        message_text += (
                "\n\n" +
                await f('incust loyalty referral learn more button', lang) +
                f": {link}"
        )
        message_text = html_to_markdown(message_text)

        wa_bot = wa.WhatsappBot(
            bot.token,
            bot.whatsapp_from,
        )
        await wa_bot.send_image(
            user.wa_phone,
            image=photo,
            caption=message_text,
            reply_markup=keyboard,
        )
        # await message.answer_image(photo, caption=message_text, reply_markup=keyboard)
