import logging
import os
from typing import Iterable

from aiogram.types.mixins import Downloadable
from psutils.media_manager import BaseMediaManager
from psutils.media_manager.data import DownloadedMediaData
from psutils.media_manager.exceptions import MediaManagerError, MediaManagerUnknownError

from config import (
    MAX_MEDIA_SIZE, MEDIA_WITH_CAPTION, PATH_TO_GOOGLE_CREDS_JSON, STATIC_DIR, TEMP_DIR,
    UPLOADS_DIR,
)
from core import messangers_adapters as ma
from db import db_func, sess
from db.models import MediaObject


class MediaManager(BaseMediaManager[MediaObject]):
    @db_func
    def get_or_create(
            self, data: DownloadedMediaData,
            upload_path: str | None,
            upload_file_name: str | None,
            allowed_types: Iterable[str] | None,
            remove_temp_file_on_error: bool = False,
    ):
        try:
            media_type, extension = self.check_file_type(
                data.mime_type, data.media_url, allowed_types
            )

            existing = MediaObject.get_sync(hash_sum=data.hash_sum)
            if existing:
                if (data.original_file_name and existing.original_file_name !=
                        data.original_file_name):
                    existing.original_file_name = data.original_file_name

                try:
                    os.remove(data.temp_file_path)
                except Exception as e:
                    logging.error(e, exc_info=True)
                return existing

            upload_file_path = self.make_upload_path(
                media_type,
                data.hash_sum, extension,
                upload_path,
                upload_file_name,
            )

            uploads_path = os.path.join(self.uploads_path, media_type)
            if not os.path.exists(uploads_path):
                os.makedirs(uploads_path, exist_ok=True)

            os.rename(data.temp_file_path, upload_file_path)
        except:
            os.remove(data.temp_file_path)
            raise

        obj = MediaObject(
            media_type=media_type,
            mime_type=data.mime_type,
            hash_sum=data.hash_sum,
            file_size=data.file_size,
            file_path=upload_file_path,
            original_file_name=data.original_file_name,
        )
        sess().add(obj)
        sess().commit()
        return obj

    @db_func
    def batch_save_media(
            self,
            downloaded_data: dict[str, DownloadedMediaData | Exception],
            upload_path: str | None = None,
            allowed_types: Iterable[str] | None = None,
            remove_temp_file_on_error: bool = True,
    ):
        result: dict[str, MediaObject | MediaManagerError] = {}

        hash_sums = {el.hash_sum for el in downloaded_data.values() if
                     isinstance(el, DownloadedMediaData)}

        existing_objects = sess().query(MediaObject.hash_sum, MediaObject). \
            filter(MediaObject.hash_sum.in_(hash_sums)). \
            populate_existing(). \
            with_for_update().all()

        existing_objects_hashes: dict[str, MediaObject] = dict(
            existing_objects
        )  # type: ignore
        saved_objects_hashes: dict[str, MediaObject] = {}

        new_objects = set()
        for media_url, downloaded in downloaded_data.items():
            if isinstance(downloaded, MediaManagerError):
                result[media_url] = downloaded
            elif isinstance(downloaded, Exception):
                raise MediaManagerUnknownError() from downloaded
            else:
                try:
                    if downloaded.hash_sum in saved_objects_hashes:
                        media = saved_objects_hashes[downloaded.hash_sum]
                    elif downloaded.hash_sum in existing_objects_hashes:
                        media = existing_objects_hashes[downloaded.hash_sum]
                    else:
                        media = None

                    if media:
                        if remove_temp_file_on_error:
                            try:
                                os.remove(downloaded.temp_file_path)
                            except Exception as e:
                                logging.error(e, exc_info=True)
                    else:
                        media_type, extension = self.check_file_type(
                            downloaded.mime_type,
                            downloaded.media_url,
                            allowed_types,
                        )

                        upload_file_path = self.make_upload_path(
                            media_type,
                            downloaded.hash_sum,
                            extension,
                            upload_path,
                            None,
                        )

                        uploads_path = os.path.join(self.uploads_path, media_type)
                        if not os.path.exists(uploads_path):
                            os.makedirs(uploads_path, exist_ok=True)

                        os.rename(downloaded.temp_file_path, upload_file_path)

                        media = MediaObject(
                            media_type=media_type,
                            mime_type=downloaded.mime_type,
                            hash_sum=downloaded.hash_sum,
                            file_size=downloaded.file_size,
                            file_path=upload_file_path,
                            original_file_name=downloaded.original_file_name,
                        )
                        new_objects.add(media)
                        saved_objects_hashes[downloaded.hash_sum] = media
                except:
                    if remove_temp_file_on_error:
                        os.remove(downloaded.temp_file_path)
                    raise
                else:
                    result[downloaded.media_url] = media

        sess().add_all(new_objects)
        sess().commit()
        return result

    async def download_file_from_tg_message(self, message: ma.tg.types.Message):
        if message.content_type not in MEDIA_WITH_CAPTION:
            return None

        file: Downloadable | list[Downloadable] = getattr(message, message.content_type)
        if not file:
            raise ValueError("No file in message")

        if message.content_type == "photo":
            file = file[-1]

        file_info: ma.tg.types.File = await file.get_file()

        original_file_name = getattr(
            file, "file_name", os.path.basename(file_info.file_path)
        )
        file_path = file_info.file_path.replace("/var/lib/", "")

        file_url = f"https://tg-api.payforsay.com/{file_path}"
        return await self.download_media(
            file_url, original_file_name=original_file_name
        )

    async def download_file_from_wa_message(
            self, message: ma.wa.types.Message, bot_token: str
    ):
        if message.content_type not in MEDIA_WITH_CAPTION:
            return None

        file: ma.wa.types.BaseMedia = getattr(message, message.content_type)
        if not file:
            raise ValueError("No file in message")

        if not file.download_url:
            await file.fetch_info()

        if not file.download_url:
            raise ValueError("Unable to download a file")

        original_file_name = file.filename if hasattr(file, "filename") else None
        return await self.download_media(
            file.download_url,
            original_file_name=original_file_name,
            mime_type=file.mime_type,
            headers={
                "Authorization": f"Bearer {bot_token}",
            }
        )


media_manager = MediaManager(
    base_dir=STATIC_DIR,
    uploads_dir=UPLOADS_DIR,
    temp_dir=TEMP_DIR,
    max_size=MAX_MEDIA_SIZE,
    creds_file_path=PATH_TO_GOOGLE_CREDS_JSON,
)
