from google.cloud import api_keys_v2
from google.cloud.api_keys_v2 import LookupKeyRequest, GetKeyRequest
from google.oauth2 import service_account

from config import PATH_TO_GOOGLE_API_MANAGE_CREDS_JSON, LOC7_HOST
from loggers import JSONLogger
from utils.platform_admins import send_message_to_platform_admins

from .exceptions import GoogleApiKeyManageHTTPNoReferrersError, GoogleApiKeyManageError


async def restrict_api_key_http(
    key: str,
    add_referrers: list[str] | None = None,
    remove_referrers: list[str] | None = None,
    debug_data: dict | None = None,
):
    """
    Restricts an API key. To restrict the websites that can use your API key,
    you add one or more HTTP referrer restrictions.

    Args:
        key: API key. To obtain the key_id,
            you can also use the lookup api: client.lookup_key()
        add_referrers: List of referrers to add to the API key.
        remove_referrers: List of referrers to remove from the API key.
        debug_data: Debug data.

    Returns:
        response: Returns the updated API Key.
    """

    logger = JSONLogger(
        "google.manage_api_key", {
            "add_referrers": add_referrers,
            "remove_referrers": remove_referrers,
        },
    )

    if not add_referrers and not remove_referrers:
        logger.error(f"restrict_api_key_http, no referrers provided.", debug_data)
        raise GoogleApiKeyManageHTTPNoReferrersError()

    try:
        logger.debug(f"restrict_api_key_http start", debug_data)
        credentials = service_account.Credentials.from_service_account_file(
            PATH_TO_GOOGLE_API_MANAGE_CREDS_JSON
        )
        client = api_keys_v2.ApiKeysClient(credentials=credentials)

        lookup_key = LookupKeyRequest()
        lookup_key.key_string = key
        lookup = client.lookup_key(lookup_key)

        get_key_request = GetKeyRequest()
        get_key_request.name = lookup.name
        key = client.get_key(get_key_request)

        restrictions = key.restrictions

        new_referrers = restrictions.browser_key_restrictions.allowed_referrers
        if remove_referrers:
            for i in remove_referrers:
                if i in new_referrers and LOC7_HOST not in i:
                    new_referrers.remove(i)
        if add_referrers:
            for i in add_referrers:
                if LOC7_HOST not in i:
                    new_referrers.append(i)

        browser_key_restrictions = api_keys_v2.BrowserKeyRestrictions()
        browser_key_restrictions.allowed_referrers = new_referrers

        restrictions = api_keys_v2.Restrictions()
        restrictions.browser_key_restrictions = browser_key_restrictions

        key = api_keys_v2.Key()
        key.name = lookup.name
        key.restrictions = restrictions

        request = api_keys_v2.UpdateKeyRequest()
        request.key = key
        request.update_mask = "restrictions"

        response = client.update_key(request=request).result()
        logger.debug(f"restrict_api_key_http end", debug_data)

        return response
    except Exception as e:
        logger.error(f"restrict_api_key_http, {repr(e)}")
        adm_msg = f"Google Manage Api Key: {repr(e)}"
        if debug_data:
            adm_msg += f"\n\nDebug data: {debug_data}"
        await send_message_to_platform_admins(adm_msg)

        raise GoogleApiKeyManageError()
