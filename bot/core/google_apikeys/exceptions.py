from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class GoogleApiKeyManageHTTPNoReferrersError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "google api key manage http no referrers error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "not_provided_referrers",
            }
        )


class GoogleApiKeyManageError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "google api key manage error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "failed_manage_api_key",
            }
        )
