from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class QrMediaObjectNotFound(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "qr media object not found error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "qr_media_object_not_found",
            }
        )


class QrMediaObjectCreateError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "qr media object create error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "qr_media_object_create_error",
            }
        )


class QrMediaObjectDeleteError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "qr media object delete error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "qr_media_object_delete_error",
            }
        )


class QrMediaAdditionalObjectCreateError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "qr media additional object create error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "qr_media_additional_object_create_error",
            }
        )


class QrMediaAdditionalObjectNotFound(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "qr media additional object not found error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "qr_media_additional_object_not_found",
            }
        )


class QrMediaAdditionalObjectDeleteError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "qr media additional object delete error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "qr_media_additional_object_delete_error",
            }
        )
