import schemas
import json
from core.media_manager import media_manager
from db.models import MediaObject, QrMediaObject, QrMediaAdditionalObject
from db import crud

from .exceptions import (
    QrMediaObjectNotFound,
    QrMediaObjectCreateError,
    QrMediaObjectDeleteError,
    QrMediaAdditionalObjectCreateError,
    QrMediaAdditionalObjectNotFound,
    QrMediaAdditionalObjectDeleteError,
)


class QrMediaService:
    def __init__(
        self,
        qr_media_id: int | None = None,
        qr_media_additional_id: int | None = None,
    ):
        self.qr_media_id = qr_media_id
        self.qr_media_additional_id = qr_media_additional_id

    async def qr_media_object_to_schema(
        self, qr_media: QrMediaObject,
    ) -> schemas.QrMediaObjectSchema:
        media = await MediaObject.get(qr_media.media_id)

        return schemas.QrMediaObjectSchema(
            id=qr_media.id,
            json_data=qr_media.json_data,
            name=qr_media.name,
            target=qr_media.target,
            url=qr_media.url,
            media_url=media.url if media else None,
            additional_media=await self.get_qr_media_additional_objects(qr_media_id=qr_media.id),
        )

    @classmethod
    async def get_qr_media_additional_objects(cls, qr_media_id: int) -> list[schemas.QrMediaAdditionalObject]:
        additional_medias = await crud.get_qr_media_additional_objects_by_qr_media_object(qr_media_id)

        return [schemas.QrMediaAdditionalObject(
            id=item.id, name=item.name, media_url=(await MediaObject.get(
                item.media_id
            )).url if item.media_id else None
        ) for item in
            additional_medias]

    async def update_qr_media_object(self, data: schemas.UpdateQrMediaObject) -> schemas.QrMediaObjectSchema:
        qr_media = await QrMediaObject.get(self.qr_media_id)
        if not qr_media:
            raise QrMediaObjectNotFound()

        data_dict = await self.__prepare_dict_to_create_or_update(data)
        if data_dict.get("json_data", None):
            data_dict["json_data"] = json.loads(data_dict["json_data"])
        additional_media = data_dict.pop("additional_media")
        await qr_media.update(**data_dict)

        if additional_media:
            for media in additional_media:
                if media.get("id", None):
                    await self.update_qr_media_additional_object(
                        schemas.CreateOrUpdateQrMediaAdditionalObject(**media), media.get("id"),
                    )
                else:
                    await self.create_qr_media_additional_object(schemas.CreateOrUpdateQrMediaAdditionalObject(**media))

        return await self.qr_media_object_to_schema(qr_media)

    async def create_qr_media_object(self, data: schemas.CreateQrMediaObject) -> QrMediaObject:
        data_dict = await self.__prepare_dict_to_create_or_update(data)
        additional_media = data_dict.pop("additional_media")

        qr_media_object = await crud.create_qr_media_object(**data_dict)
        if not qr_media_object:
            raise QrMediaObjectCreateError()

        if additional_media:
            for media in additional_media:
                await self.create_qr_media_additional_object(
                    schemas.CreateOrUpdateQrMediaAdditionalObject(**media),
                    qr_media_object.id
                )

        return qr_media_object

    async def create_qr_media_additional_object(
        self, data: schemas.CreateOrUpdateQrMediaAdditionalObject, qr_media_id: int | None = None
    ) -> schemas.QrMediaAdditionalObject:
        data_dict = await self.__prepare_dict_to_create_or_update(data)
        if data_dict.get("id", None) is not None:
            data_dict.pop("id")
        qr_media_additional_object = await crud.create_qr_media_additional_object(**data_dict)
        if not qr_media_additional_object:
            raise QrMediaAdditionalObjectCreateError()

        await crud.connect_qr_media_object_to_qr_media_additional_object(
            qr_media_id or self.qr_media_id, qr_media_additional_object.id
        )

        return schemas.QrMediaAdditionalObject(
            id=qr_media_additional_object.id,
            name=qr_media_additional_object.name,
            media_url=(await MediaObject.get(qr_media_additional_object.media_id)).url,
        )

    async def update_qr_media_additional_object(
        self, data: schemas.CreateOrUpdateQrMediaAdditionalObject, additional_id: int | None = None
    ) -> schemas.QrMediaAdditionalObject:
        qr_media_additional = await QrMediaAdditionalObject.get(additional_id or self.qr_media_additional_id)
        if not qr_media_additional:
            raise QrMediaAdditionalObjectNotFound()

        data_dict = await self.__prepare_dict_to_create_or_update(data)
        if data_dict.get("id"):
            data_dict.pop("id")
        await qr_media_additional.update(**data_dict)

        return schemas.QrMediaAdditionalObject(
            id=qr_media_additional.id,
            name=qr_media_additional.name,
            media_url=(await MediaObject.get(qr_media_additional.media_id)).url,
        )

    @classmethod
    async def __prepare_dict_to_create_or_update(
        cls,
        data: schemas.UpdateQrMediaObject | schemas.CreateQrMediaObject | schemas.CreateOrUpdateQrMediaAdditionalObject
    ) -> dict:
        data_dict = data.dict(exclude_unset=True)
        if "qr_media_file" in data_dict:
            if data_dict["qr_media_file"]:
                logo_media = await media_manager.save_from_upload_file(data_dict.get("qr_media_file"))
                data_dict["media_id"] = logo_media.id
            else:
                data_dict["media_id"] = None
            data_dict.pop("qr_media_file")

        return data_dict

    async def delete_qr_media_object(self) -> schemas.OkResponse:
        additional_media = await self.get_qr_media_additional_objects(self.qr_media_id)
        if additional_media:
            for media in additional_media:
                await self.delete_qr_media_additional_object(media.id)

        res = await crud.delete_qr_media_object(self.qr_media_id)
        if not res:
            raise QrMediaObjectDeleteError()

        return schemas.OkResponse()

    async def delete_qr_media_additional_object(self, additional_id: int | None = None) -> schemas.OkResponse:
        await crud.delete_qr_media_additional_object_from_qr_media_object(
            self.qr_media_id, additional_id or self.qr_media_additional_id
        )
        res = await crud.delete_qr_media_additional_object(self.qr_media_additional_id)
        if not res:
            raise QrMediaAdditionalObjectDeleteError()

        return schemas.OkResponse()
