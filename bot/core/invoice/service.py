import logging
from datetime import datetime
from functools import wraps

from aiogram.utils.exceptions import (
    BadRequest, CurrencyTotalAmountInvalid,
    PaymentProviderInvalid,
)
from fastapi import Depends, HTTPException, Security
from incust_api.api import term
from psutils.exceptions import ErrorWithTextVariable
from psutils.exceptions.exception_handlers import <PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>
from starlette import status

import schemas
import schemas.invoice.invoice
from config import ANONYMOUS_USER_EMAIL
from core.incust.functions import validate_prohibit_redeeming_bonuses
from core.incust.helpers import get_currency_from_store_or_brand
from core.invoice.invoice_to_schema import invoice_to_schema
from core.invoice_loyalty.service import InvoiceLoyaltyService
from core.webhooks.functions import add_webhook_event, prepare_data_for_payment_webhook
from db import crud
from db.models import (
    Brand, ClientBot, Customer, Group, Invoice, InvoiceTemplate,
    LoyaltySettings, MenuInStore, ObjectPaymentSettings, Payment, PaymentSettings,
    Store, StoreOrder,
    User,
)
from schemas import InvoiceTypeEnum
from utils.date_time import utcnow
from utils.redefined_classes import InlineKb
from utils.text import c
from utils.type_vars import FuncT
from .exception import (
    CreateInvoiceNoItemsError, InvoiceAgreementNotAcceptedError, InvoiceCreateError,
    InvoiceEmailIsRequiredError, InvoiceGroupRequiredError,
    InvoiceInvalidPaymentModeError,
    InvoiceInvalidProfileApiTokenError, InvoiceInvalidTotalAmountError,
    InvoiceItemsAndAmountError, InvoiceNoPermissionsError, InvoiceNotFoundError,
    InvoiceNotPaymentMethodsError,
    InvoiceNotPricesError, InvoiceNotTelegramUserError,
    InvoicePaymentProviderInvalidError, InvoicePhoneIsRequiredError,
    InvoicePricesAndAmountError, InvoiceProfileDoesNotHaveABotError,
    InvoiceProfilePaymentsNotAvailableError,
    InvoiceRequiredError, InvoiceUnknownError, InvoiceUserRequiredError,
    NotInvoiceTokenError,
)
from .functions import (
    calc_extra_fee, detect_currency,
    get_invoice_created_result, get_payment_methods_info, get_title_for_invoice,
    is_group_payments_available, is_valid_friend_for_invoice,
    make_invoice_items,
    make_invoice_message, process_loyalty,
)
from .notifications import send_invoice_push_notifications
from .payment_funcs import finish_process_full_bonus_payment
from ..api.depends import get_anonymous_user, get_api_token
from ..auth.depend import (
    get_active_user, get_active_user_optional,
    parse_access_token_depend,
)
from ..billing.functions import check_billing_transaction_limit
from ..exceptions import InvalidCurrencyError
from ..helpers import get_web_payment_button, send_invoice, send_web_invoice
from ..user.exceptions import UserNotConfirmedEmailError
from ..user.functions import create_guest_user

debugger = logging.getLogger('debugger.invoice')

handle_error = UnknownErrorHandler(InvoiceUnknownError, ErrorWithTextVariable)


def group_required(func: FuncT) -> FuncT:
    @wraps(func)
    async def wrapper(self: "InvoiceService", *args, **kwargs):
        if not self.group:
            raise InvoiceGroupRequiredError()
        return await func(self, *args, **kwargs)

    return wrapper


def user_required(func: FuncT) -> FuncT:
    @wraps(func)
    async def wrapper(self: "InvoiceService", *args, **kwargs):
        if not self.user:
            raise InvoiceUserRequiredError()
        return await func(self, *args, **kwargs)

    return wrapper


def invoice_required(func: FuncT) -> FuncT:
    @wraps(func)
    async def wrapper(self: "InvoiceService", *args, **kwargs):
        if not self.invoice:
            raise InvoiceRequiredError()
        return await func(self, *args, **kwargs)

    return wrapper


class InvoiceService:
    def __init__(
            self,
            invoice: Invoice | None = None,
            group: Group | None = None,
            user: User | None = None,
    ):
        self._invoice: Invoice | None = invoice
        self._group: Group | None = group
        self._user: User | None = user

    @property
    def invoice(self):
        return self._invoice

    @property
    def group(self):
        return self._group

    @property
    def user(self):
        return self._user

    def set_invoice(self, invoice: Invoice | None):
        self._invoice = invoice

    def set_group(self, group: Group | None):
        self._group = group

    def set_user(self, user: User | None):
        self._user = user

    @group_required
    async def validate_group_for_integration(self) -> ClientBot:
        bot = await ClientBot.get(group_id=self.group.id)
        if not bot:
            raise InvoiceProfileDoesNotHaveABotError(self.group.name)

        if not await is_group_payments_available(self.group):
            raise InvoiceProfilePaymentsNotAvailableError(self.group.name)

        return bot

    @user_required
    def validate_user_for_integration(self):
        if not self.user.chat_id:
            raise InvoiceNotTelegramUserError()

    @classmethod
    def depend_user(
            cls, user: User = Depends(get_active_user),
    ):
        return cls(user=user)

    @classmethod
    async def depend_user_for_integration(
            cls, user: User = Security(get_active_user, scopes=["makeInvoice"])
    ):
        return cls(user=user)

    @classmethod
    async def depend_group_for_integration(
            cls, api_token: str = Depends(get_api_token),
    ):
        group = await crud.get_group_by_api_token(api_token)
        if not group:
            raise InvoiceInvalidProfileApiTokenError(api_token)
        return cls(group=group)

    @classmethod
    async def depend_group_user_for_integration(
            cls, api_token: str = Depends(get_api_token),
            user: User = Security(get_active_user, scopes=["makeInvoice"])
    ):
        group = await crud.get_group_by_api_token(api_token)
        if not group:
            raise InvoiceInvalidProfileApiTokenError(api_token)
        return cls(group=group, user=user)

    @classmethod
    async def depend_group_and_anonymous_user(
            cls, api_token: str = Depends(get_api_token),
            user: User | None = Depends(get_active_user_optional),
    ):
        group = await crud.get_group_by_api_token(api_token)
        if not group:
            raise InvoiceInvalidProfileApiTokenError(api_token)

        if not user or not isinstance(user, User):
            user = await User.get_by_email(ANONYMOUS_USER_EMAIL)

        return cls(group=group, user=user)

    @classmethod
    async def depend_invoice_token(
            cls,
            token_data: dict = Security(
                parse_access_token_depend, scopes=["invoice:read"]
            ),
    ):
        if token_data.get("type") == "invoice":
            invoice_id = token_data.get("sub")
            invoice = await Invoice.get(invoice_id) if invoice_id else None
        else:
            invoice = None

        if not invoice:
            raise NotInvoiceTokenError(token_data)

        return cls(invoice)

    @classmethod
    async def depend_invoice_id_with_group(
            cls,
            invoice_id: int,
            api_token: str = Depends(get_api_token),
    ):
        group = await crud.get_group_by_api_token(api_token)
        if not group:
            raise InvoiceInvalidProfileApiTokenError(api_token)

        invoice = await Invoice.get(invoice_id) if invoice_id else None

        if not invoice:
            raise InvoiceNotFoundError()

        if invoice.group_id != group.id:
            raise InvoiceNoPermissionsError(invoice.id)

        return cls(invoice)

    @user_required
    async def load_invoice_by_id(self, invoice_id: int):
        invoice = await Invoice.get(invoice_id)
        if not invoice:
            raise InvoiceNotFoundError(invoice_id)

        if invoice.user_id != self.user.id:
            if not invoice.is_friend:
                raise InvoiceNoPermissionsError(invoice_id)

            if (invoice.is_friend and invoice.user_id != self.user.id
                    and not await is_valid_friend_for_invoice(
                        invoice.id, invoice.user_id, self.user.id
                    )):
                raise InvoiceNoPermissionsError(invoice_id)

        self.set_invoice(invoice)
        return invoice

    @handle_error
    @group_required
    @user_required
    async def create_invoice(
            self,
            invoice_type: InvoiceTypeEnum,
            payment_mode: schemas.InvoicePaymentModeLiteral,
            creator: User | None = None,
            menu: MenuInStore | None = None,
            store_order: StoreOrder | None = None,
            invoice_template: InvoiceTemplate | None = None,
            bot: ClientBot | None = None,
            items: list[schemas.CreateInvoiceItemData] | None = None,
            count: int = 1,
            entered_amount: float | None = None,
            incust_check: dict | None = None,
            expiration_datetime: datetime | None = None,
            external_transaction_id: str | None = None,
            client_redirect_url: str | None = None,
            successful_payment_callback_url: str | None = None,
            first_name: str | None = None,
            last_name: str | None = None,
            email: str | None = None,
            phone: str | None = None,
            user_comment: str | None = None,
            qr_mode: schemas.InvoiceQrMode | None = None,
            utm_labels: dict | None = None,
            **kwargs,
    ) -> Invoice:
        if not count:
            count = 1

        """Common method for create invoice"""
        debugger.debug(
            f'create_invoice: {invoice_type=}, {payment_mode=}, {creator=}, {menu=}, '
            f'{store_order=}, {invoice_template=}, {bot=}, {items=}, {count=}, '
            f'{entered_amount=}, {incust_check=}, {expiration_datetime=}, '
            f'{external_transaction_id=}, {client_redirect_url=}, '
            f'{successful_payment_callback_url=}, {first_name=}, {last_name=}, '
            f'{email=}, {phone=}, {user_comment=}, {qr_mode=}'
        )
        lang = self.group.lang

        if (
                payment_mode ==
                schemas.InvoicePaymentModeEnum.ENTERED_AMOUNT.value and
                not (entered_amount or items)
        ):
            raise InvoiceInvalidPaymentModeError(payment_mode)
        if (
                payment_mode ==
                schemas.InvoicePaymentModeEnum.TEMPLATE.value and
                not invoice_template
        ):
            raise InvoiceInvalidPaymentModeError(payment_mode)
        if (
                payment_mode ==
                schemas.InvoicePaymentModeEnum.STORE_ORDER.value and
                not store_order and not items
        ):
            raise InvoiceInvalidPaymentModeError(payment_mode)

        # get and check currency
        if invoice_template and invoice_template.currency:
            currency = invoice_template.currency
        else:
            currency = await detect_currency(self.group, store_order)

        await check_billing_transaction_limit(self.group.id, currency)

        debugger.debug(f'{currency=}')

        if menu:
            store_currency = await get_currency_from_store_or_brand(
                menu_in_store=menu, group_id=self.group.id
            )
            if store_currency:
                currency = store_currency

        if entered_amount and items:
            raise InvoicePricesAndAmountError()

        if not items:
            incust_check_schema = term.m.Check.parse_obj(
                incust_check
            ) if incust_check else None
            items = await make_invoice_items(
                entered_amount, invoice_template.id if invoice_template else None,
                incust_check_schema, count,
                invoice_template.product_code if invoice_template else None, lang
            )

            if not items:
                raise CreateInvoiceNoItemsError()

        title, description, photo_path = await get_title_for_invoice(
            self.group, store_order, invoice_template, lang
        )

        payment_bot_menu = bot if bot and bot.group_id == self.group.id else None

        if not self.user.is_anonymous:
            if not first_name:
                first_name = self.user.first_name
            if not last_name:
                last_name = self.user.last_name
            if not email:
                email = self.user.email

        if not menu and store_order and store_order.menu_in_store_id:
            menu = await MenuInStore.get(store_order.menu_in_store_id)

        if store_order:
            tips_sum = store_order.tips_sum
            shipment_cost = (await crud.get_order_shipment(store_order.id)).raw_price
        else:
            tips_sum = 0
            shipment_cost = 0

        invoice: Invoice = await crud.create_invoice_db(
            invoice_type=invoice_type,
            user=self.user,
            creator=creator or self.user or None,
            payment_mode=payment_mode,
            items=items,
            bot=bot,
            count=count,
            currency=currency,
            title=title,
            description=description,
            photo_path=photo_path,
            live_time=None,
            expiration_datetime=expiration_datetime,
            brand_group=self.group,
            incust_check=incust_check,
            payment_bot_menu=payment_bot_menu,
            menu_in_store=menu,
            invoice_template=invoice_template,
            store_order=store_order,
            external_transaction_id=external_transaction_id,
            client_redirect_url=client_redirect_url,
            successful_payment_callback_url=successful_payment_callback_url,
            first_name=first_name,
            last_name=last_name,
            email=email,
            phone=phone,
            user_comment=user_comment,
            qr_mode=qr_mode,
            tips_sum=tips_sum,
            shipment_cost=shipment_cost,
            utm_labels=utm_labels,
            **kwargs,
        )

        if not invoice:
            raise InvoiceCreateError()

        # Резервування лояльності для Invoice
        # Спробуємо отримати LoyaltySettings для визначення чи є лояльність
        # Резервування лояльності для всіх типів invoice (включаючи STORE_ORDER)
        # Тепер резерв чек робиться тільки при створенні invoice (в moment prepayment)
        if self.group:
            brand = await Brand.get(group_id=self.group.id)
            if brand:
                # Отримуємо store з menu якщо є
                store = None
                if menu and menu.store_id:
                    store = await Store.get(menu.store_id)
                
                # Спробуємо отримати налаштування лояльності з усіх можливих параметрів
                target = (
                    "ewallet" if kwargs.get('ewallet_id')
                    else "invoice_template" if invoice_template
                    else "store" if store
                    else "brand"
                )
                loyalty_settings = await LoyaltySettings.get(invoice.loyalty_settings_id) \
                if invoice and invoice.loyalty_settings_id else \
                await crud.get_loyalty_settings_for_context(
                    target,
                    schemas.LoyaltySettingsData(
                        brand_id=brand.id,
                        store_id=store.id if store else None,
                        invoice_template_id=invoice_template.id if invoice_template else None,
                        profile_id=self.group.id,
                        ewallet_id=kwargs.get('ewallet_id'),
                    )
                )
                
                # Якщо є налаштування лояльності і incust_check - резервуємо
                if loyalty_settings and incust_check:
                    invoice_loyalty_service = InvoiceLoyaltyService()
                    await invoice_loyalty_service.reserve_invoice_loyalty(
                        invoice, loyalty_settings, incust_check
                    )

        self.set_invoice(invoice)

        if store_order:
            await invoice.update(
                sum_to_pay=store_order.sum_to_pay,
                total_sum_with_extra_fee=store_order.total_sum_with_extra_fee
            )
        else:
            if invoice.sum_to_pay > 0:
                result = await calc_extra_fee(
                    invoice.group_id,
                    invoice.total_sum,
                    currency,
                    invoice.id,
                    store_order.id if store_order else None
                )
                if result.total_extra_fee:
                    await invoice.update(
                        sum_to_pay=invoice.sum_to_pay + result.total_extra_fee,
                        total_sum_with_extra_fee=invoice.total_sum +
                                                 result.total_extra_fee
                    )
                    await crud.add_extra_fee_to_journal(result.journal_entries)

        if invoice_type != schemas.InvoiceTypeEnum.STORE_ORDER:
            invoice_schema = await invoice_to_schema(
                invoice, self.group.lang, self.group
            )
            webhook_data = await prepare_data_for_payment_webhook(
                invoice_schema, invoice, self.group.id, user_id=self.user.id,
            )
            await add_webhook_event(
                entity=schemas.WebhookEntityEnum.PAYMENT,
                entity_id=invoice.id,
                action=schemas.WebhookActionEnum.CREATED,
                group_id=invoice.group_id,
                data=webhook_data.dict(),
                data_type=schemas.WebhookPaymentDataSchema,
            )
            await send_invoice_push_notifications(invoice, self.group, self.user)

        return invoice

    @handle_error
    async def create_invoice_web(
            self,
            data: schemas.CreateInvoiceWebData,
            bot: ClientBot | None, lang: str,
    ):
        if (
                not (
                        self.user and
                        self.user.is_accepted_agreement
                ) and
                not data.is_accepted_agreement
        ):
            raise InvoiceAgreementNotAcceptedError()

        brand = await crud.get_brand_by_group(
            self.group.id if self.group else data.group_id
        )
        if data.incust_check and brand:
            await validate_prohibit_redeeming_bonuses(data.incust_check, brand.id)

        self.set_group(await Group.get(data.group_id))

        invoice_template: InvoiceTemplate | None
        if data.invoice_template_id:
            invoice_template = await InvoiceTemplate.get(data.invoice_template_id)
            if not invoice_template:
                raise HTTPException(
                    status.HTTP_404_NOT_FOUND,
                    detail=f"template with id {data.invoice_template_id} not found",
                )

            if invoice_template.need_email and (
                    not self.user or self.user.is_only_email) and not data.email:
                raise InvoiceEmailIsRequiredError()
            if invoice_template.need_phone_number and not data.phone:
                raise InvoicePhoneIsRequiredError()

        else:
            invoice_template = None

        if not self.user:
            if (
                    data.email and
                    not await User.is_exists(
                        email=data.email, is_guest_user=False
                    )
            ):
                user = await create_guest_user(
                    data.email,
                    data.first_name,
                    data.last_name,
                    lang,
                    is_accepted_agreement=data.is_accepted_agreement,
                )
            else:
                user = await get_anonymous_user()

            self.set_user(user)
        elif self.user.email and not self.user.is_confirmed_email:
            raise UserNotConfirmedEmailError()
        elif data.is_accepted_agreement and not self.user.is_accepted_agreement:
            await self.user.update(
                is_accepted_agreement=data.is_accepted_agreement,
            )

        customer = await Customer.get(user_id=self.user.id, profile_id=self.group.id)
        marketing_cons = False if data.marketing_consent is None else (
            data.marketing_consent)
        if not customer:
            await crud.create_customer(
                lang, self.user.id, self.group.id, marketing_cons, True
            )
        else:
            if data.marketing_consent is not None:
                marketing_cons = data.marketing_consent
            else:
                marketing_cons = customer.marketing_consent

            await customer.update(
                marketing_consent=marketing_cons,
                is_accept_agreement=True,
                updated_date=utcnow(),
            )

        if data.menu_in_store_id:
            menu_in_store = await MenuInStore.get(data.menu_in_store_id)
            if not menu_in_store:
                raise HTTPException(
                    status.HTTP_404_NOT_FOUND,
                    detail=f"menu in store with id {data.menu_in_store_id} not found",
                )
        else:
            menu_in_store = None
        if menu_in_store:
            invoice_type = schemas.InvoiceTypeEnum.MENU_IN_STORE
        else:
            if data.ewallet_id:
                invoice_type = schemas.InvoiceTypeEnum.TOPUP_ACCOUNT
            else:
                invoice_type = schemas.InvoiceTypeEnum.FROM_LINK

        if not bot:
            bot = await ClientBot.get(group_id=self.group.id)

        incust_check = data.incust_check.dict(
            exclude_unset=True
        ) if data.incust_check else None

        target = (
            "ewallet" if data.ewallet_id
            else "store" if data.store_id
            else "invoice_template" if invoice_template
            else "brand"
        )
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            target,
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                store_id=data.store_id,
                invoice_template_id=invoice_template.id if invoice_template else None,
                profile_id=self.group.id,
                ewallet_id=data.ewallet_id,
            )
        )

        invoice = await self.create_invoice(
            invoice_type,
            data.payment_mode,
            menu=menu_in_store,
            invoice_template=invoice_template,
            bot=bot,
            count=data.count,
            entered_amount=data.entered_amount,
            incust_check=incust_check,
            client_redirect_url=data.client_redirect_url,
            successful_payment_callback_url=data.successful_payment_callback_url,
            first_name=data.first_name,
            last_name=data.last_name,
            email=data.email,
            phone=data.phone,
            user_comment=data.user_comment,
            external_transaction_id=data.external_transaction_id,
            extra_params=data.extra_params,
            ewallet_id=data.ewallet_id,
            utm_labels=data.utm_labels.dict() if data.utm_labels else None,
        )

        if data.incust_check and data.incust_check.amount_to_pay == 0:
            await finish_process_full_bonus_payment(
                self.group, brand, loyalty_settings, invoice, lang=lang
            )
        return await get_invoice_created_result(invoice, lang, data.with_url)

    @handle_error
    @user_required
    async def validate_user_token_for_integration(self):
        self.validate_user_for_integration()
        return schemas.UserTokenForIntegrationValid(
            user_id=self.user.id,
        )

    @handle_error
    @group_required
    async def validate_group_token_for_integration(self):
        await self.validate_group_for_integration()
        return schemas.ProfileTokenForIntegrationValid(
            profile_id=self.group.id,
            profile_name=self.group.name,
        )

    @handle_error
    @invoice_required
    async def check_status(self):
        return schemas.InvoiceStatus(
            id=self.invoice.id,
            status=self.invoice.status,
            total_amount=self.invoice.converted_sum_to_pay,
            external_transaction_id=self.invoice.external_transaction_id,
            webhook_result=self.invoice.webhook_result,
            currency=self.invoice.currency,
        )

    @handle_error
    @group_required
    @user_required
    async def create_invoice_with_url(
            self, data: schemas.CreateInvoiceWithUrlData,
            lang: str,
    ) -> schemas.InvoiceCreatedResult:
        if not data.amount and not data.check_items:
            raise InvoiceNotPricesError()
        if data.amount and data.check_items:
            raise InvoiceItemsAndAmountError()

        if data.currency and self.group.currency != data.currency:
            raise InvalidCurrencyError(data.currency)

        create_invoice_kwargs = {
            "invoice_type": schemas.InvoiceTypeEnum.INTEGRATION,
            "payment_mode": schemas.InvoicePaymentModeEnum.ENTERED_AMOUNT.value,
            "external_transaction_id": data.external_transaction_id,
            "client_redirect_url": data.client_redirect_url,
            "successful_payment_callback_url": data.successful_payment_callback_url,
            "qr_mode": data.qr_mode,
        }

        if data.amount:
            create_invoice_kwargs["entered_amount"] = data.amount
        if data.check_items:
            create_invoice_kwargs["items"] = [schemas.CreateInvoiceItemData(
                name=el.name,
                quantity=el.quantity,
                item_code=el.item_id,
                price=el.price,
            ) for el in data.check_items]

        bot = await ClientBot.get(group_id=self.group.id)
        if bot:
            create_invoice_kwargs["bot"] = bot

        brand: Brand = await crud.get_brand_by_group(self.group.id)
        if await brand.is_incust and self.user and not self.user.is_anonymous:
            incust_check = await process_loyalty(
                brand, self.user, data, lang
            )
            if incust_check:
                create_invoice_kwargs["incust_check"] = incust_check.dict(
                    exclude_unset=True
                )

        invoice = await self.create_invoice(**create_invoice_kwargs)
        if not invoice:
            raise InvoiceCreateError()

        invoice_with_token_schema = await get_invoice_created_result(
            invoice, lang, with_url=True
        )

        if bot and not self.user.is_anonymous and self.user.chat_id:
            lang = await self.user.get_lang(bot.id)
            payload = c("pay_for_invoice", invoice_id=invoice.id)
            await show_invoice(invoice, payload, lang=lang)

        return invoice_with_token_schema


@handle_error
async def show_invoice(
        invoice: Invoice, payload: str, keyboard: InlineKb | None = None,
        store_id: int | None = None,
        lang: str | None = None,
        payment_settings_id: int | None = None,
        object_payment_settings_id: int | None = None,
        tg_payment_token: str | None = None,
):
    """
    Показує інвойс для оплати.

    Args:
        invoice: Інвойс для оплати
        payload: Дані для сервера оплати
        keyboard: Клавіатура для повідомлення (опціонально)
        store_id: ID магазину (опціонально)
        lang: Мова (опціонально)
        payment_settings_id: ID налаштувань оплати
        object_payment_settings_id: ID налаштувань об'єкта оплати
        tg_payment_token: Токен Telegram оплати (перевизначає інші параметри)
    """
    debugger.debug('core.invoice.service.show_invoice:')

    bot_id = invoice.payment_bot_menu_id or invoice.bot_id
    bot: ClientBot = await ClientBot.get(bot_id)
    debugger.debug(f'{bot.id=}')

    brand = await crud.get_brand_by_group(invoice.group_id)
    brand_id = brand.id if brand else None

    # Отримуємо доступні методи оплати
    payment_methods_info = await get_payment_methods_info(
        invoice.group_id, brand_id, store_id,
        invoice_template_id=invoice.invoice_template_id if not store_id and
                                                           invoice.invoice_template_id else None
    )

    payment_methods = payment_methods_info.payment_methods

    # Визначаємо, чи це телеграм-оплата і телеграм токен
    tg_token = None
    use_telegram_payment = False

    # Якщо передано токен напряму - використовуємо його
    if tg_payment_token:
        tg_token = tg_payment_token
        use_telegram_payment = True

    # Якщо передані ідентифікатори налаштувань, перевіряємо, чи це телеграм-оплата
    elif payment_settings_id or object_payment_settings_id:
        # Спочатку перевіряємо object_payment_settings, бо воно має пріоритет
        if object_payment_settings_id:
            object_settings = await ObjectPaymentSettings.get(
                object_payment_settings_id
            )
            if object_settings:
                # Перевіряємо, чи це телеграм-оплата
                if object_settings.payment_settings:
                    use_telegram_payment = (
                            object_settings.payment_settings.payment_method == "tg_pay")

                # Отримуємо токен оплати, якщо доступний
                if (object_settings.json_data and "provider_token" in
                        object_settings.json_data):
                    tg_token = object_settings.json_data.get("provider_token")

        # Якщо не знайшли в object_payment_settings, шукаємо в payment_settings
        if not tg_token and payment_settings_id:
            payment_settings = await PaymentSettings.get(payment_settings_id)
            if payment_settings:
                use_telegram_payment = payment_settings.payment_method == "tg_pay"
                if (payment_settings.json_data and "provider_token" in
                        payment_settings.json_data):
                    tg_token = payment_settings.json_data.get("provider_token")

    # Якщо не визначено, але є оплата з payment_methods_info
    elif payment_methods_info.tg_token:
        tg_token = payment_methods_info.tg_token
        use_telegram_payment = True

    # Якщо не вдалося визначити токен, але є оплата зі зв'язаного платежу і це
    # телеграм-оплата
    if not tg_token and use_telegram_payment:
        payment = await Payment.get(invoice_id=invoice.id)
        if payment:
            if payment.payment_settings_id:
                ps = await PaymentSettings.get(payment.payment_settings_id)
                if ps and ps.payment_method == "tg_pay" and ps.json_data:
                    tg_token = ps.json_data.get("payment_token")

                if payment.object_payment_settings_id:
                    pos = await ObjectPaymentSettings.get(
                        payment.object_payment_settings_id
                    )
                    if pos and pos.json_data and "provider_token" in pos.json_data:
                        tg_token = pos.json_data.get("provider_token") or tg_token

    # Якщо треба використовувати телеграм-оплату, але не знайдено токен і немає інших
    # методів
    if use_telegram_payment and not tg_token and not payment_methods:
        raise InvoiceNotPaymentMethodsError()

    msg_text = await make_invoice_message(invoice, lang)

    # Якщо не можна використовувати телеграм-оплату (немає токена або це whatsapp)
    if not tg_token or bot.bot_type == "whatsapp":
        return await send_web_invoice(
            invoice, bot,
            invoice.photo_url,
            msg_text, brand_id,
            store_id, lang, keyboard,
            True,
        )

    # Якщо є інші методи оплати і не вказано конкретно телеграм-оплату
    if payment_methods and not use_telegram_payment:
        if invoice.menu_in_store_id and not store_id:
            store = await Store.get(invoice.menu_in_store_id)
            if store:
                store_id = store.id
        pay_btn = await get_web_payment_button(
            invoice, brand_id, bot.id, store_id, lang
        )
        if not keyboard:
            keyboard = InlineKb().row()
        keyboard.row(pay_btn)

    # Спроба відправити інвойс через телеграм
    try:
        debugger.debug('try to send invoice with tel pmt token...')
        await send_invoice(invoice, payload, tg_token, bot, keyboard=keyboard)
    except (CurrencyTotalAmountInvalid, PaymentProviderInvalid, BadRequest) as ex:
        logging.error(f'send_invoice FAILED\n{str(ex)}', exc_info=True)

        # Якщо є інші методи оплати і це не обов'язково телеграм-оплата
        if payment_methods and not use_telegram_payment:
            await send_web_invoice(
                invoice, bot, invoice.photo_url,
                msg_text, brand_id, store_id,
                lang, keyboard=None,
                is_one_pmt_method=True,
            )

            debugger.debug('send_web_invoice.. Ok')
            return
        else:
            logging.error(
                'send invoice with tel pmt token ERROR and not have web pmt methods...'
            )
            if isinstance(ex, CurrencyTotalAmountInvalid):
                logging.error(
                    f'total amount invalid for {invoice.converted_sum_to_pay=} '
                    f'{invoice.currency=}'
                )
                raise InvoiceInvalidTotalAmountError(
                    invoice.converted_sum_to_pay, invoice.currency
                )
            elif isinstance(ex, PaymentProviderInvalid):
                logging.error(f'payment provider error for {bot_id=}')
                raise InvoicePaymentProviderInvalidError()
            else:
                if str(ex) == 'Currency_invalid':
                    logging.error(f'Currency_invalid for {bot_id=}')
                    raise InvalidCurrencyError(invoice.currency)
                else:
                    raise ex
    except Exception as err:
        logging.error(err)
        raise err
