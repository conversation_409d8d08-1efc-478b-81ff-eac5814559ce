import logging

import schemas
from config import USE_LOCALISATION
from core.bot.ewallet_handlers import process_single_ewallet
from db import crud
from db.models import (
    EWallet, ExtraFeeJournal, Group, Invoice, InvoiceTemplate,
    StoreOrderPayment, User,
)
from schemas.base import ExtraFeeSchema
from utils.text import f
from utils.translator import t


async def invoice_to_schema(
        invoice: Invoice,
        lang: str,
        group: Group | None = None
) -> schemas.InvoiceSchema:

    invoice_template, invoice_photo = None, None

    if invoice.invoice_template_id:
        invoice_template = await InvoiceTemplate.get(invoice.invoice_template_id)
        if invoice_template and invoice_template.media_id:
            invoice_photo = await invoice_template.photo_url

    if not invoice_photo and invoice.photo_url:
        invoice_photo = invoice.photo_url

    user_comment_label = await get_invoice_comment_label(lang, invoice_template, group)
    user = await User.get_by_id(invoice.user_id)

    extra_fees: list[ExtraFeeJournal] = await ExtraFeeJournal.get_list(
        invoice_id=invoice.id
    )
    
    # Отримання інформації про еволлет для інвойсів типу поповнення рахунку
    ewallet_info = None
    if invoice.ewallet_id:
        try:
            ewallet = await EWallet.get(invoice.ewallet_id, is_enabled=True, is_deleted=False)
            if ewallet and user:
                ewallet_response = await process_single_ewallet(ewallet, user, lang)
                ewallet_info = schemas.EWalletUserAccountInfo(
                    title=ewallet.name,
                    message=ewallet_response.message,
                    used_credit=ewallet_response.used_credit,
                    available_amount=ewallet_response.available_amount,
                    credit_limit=ewallet_response.credit_limit,
                )
        except Exception as ex:
            logging.error(f"Error processing ewallet for invoice {invoice.id}: {ex}", exc_info=True)

    store_order_payment = await StoreOrderPayment.get(invoice_id=invoice.id)
    post_payment_info = store_order_payment.post_payment_info if store_order_payment else None

    schema = schemas.InvoiceSchema(
        id=invoice.id,
        status=invoice.status,
        payment_mode=invoice.payment_mode,
        currency=invoice.currency,
        title=invoice.title,
        description=invoice.description,
        first_name=invoice.first_name,
        last_name=invoice.last_name,
        email=invoice.email,
        phone=invoice.phone,
        items=[item.from_orm_converted(schemas.InvoiceItemSchema) for item in
               await crud.get_invoice_items(invoice.id)],
        photo=invoice_photo,
        # incust_check=term.m.Check(
        #     **invoice.incust_check
        # ) if invoice.incust_check else None,
        external_transaction_id=invoice.external_transaction_id,
        client_redirect_url=invoice.client_redirect_url,
        webhook_result=invoice.webhook_result,

        user_id=invoice.user_id,
        payer_id=invoice.payer_id,
        is_friend=invoice.is_friend,
        time_created=invoice.time_created,
        user_comment_label=user_comment_label,
        user_comment=invoice.user_comment,
        payed_in_bot_id=invoice.payment_bot_menu_id or invoice.payment_bot_id or
                        invoice.bot_id,
        invoice_type=invoice.invoice_type,
        **invoice.converted_sums,
        extraFee=[ExtraFeeSchema(**extra_fee.as_dict()) for extra_fee in extra_fees],
        invoice_template_id=invoice_template.id if invoice_template else None,
        extra_params=invoice.extra_params,
        ewallet_id=invoice.ewallet_id,
        ewallet_info=ewallet_info,
        post_payment_info=post_payment_info,

        incust_vouchers=invoice.loyalty_coupons_data,
        special_accounts_charges=invoice.special_accounts_charges,
        bonuses_added_amount= invoice.bonuses_added_amount or 0,
    )

    return schema


async def get_invoice_comment_label(
        lang: str,
        invoice_template: InvoiceTemplate | None = None,
        group: Group | None = None,
):
    if not invoice_template or invoice_template.comment_label_raw == USE_LOCALISATION:
        comment_label = await f("invoice comment label", lang)
    else:
        if not group:
            group = await Group.get(invoice_template.group_id)
        elif group.id != invoice_template.group_id:
            raise ValueError("Specified group is not equal to invoice_template's group")

        comment_label = await t(
            invoice_template, lang, group.lang,
            field_name="comment_label_raw",
            group_id=group.id,
            is_auto_translate_allowed=group.is_translate,
        )

    return comment_label
