from __future__ import annotations

import logging
from copy import deepcopy
from datetime import datetime

from psutils.exceptions.exception_handlers import UnknownErrorHandler

import schemas
from core.invoice.invoice_to_schema import invoice_to_schema
from utils.datetime_utils import convert_datetime_to_str
from core.kafka.producer.functions import add_invoice_payment_notification
from core.store.order.service import change_store_order_status
from core.webhooks.functions import add_webhook_event, prepare_data_for_payment_webhook
from db import crud
from db.models import (
    Brand, ClientBot, Group, Invoice, InvoiceTemplate, LoyaltySettings, MenuInStore,
    Payment, Store,
    StoreOrder,
    StoreOrderPayment, User,
)
from schemas import (
    InvoiceTypeEnum, OrderShippingStatusEnum, PaymentCallBackData,
)
from utils.date_time import utcnow
from utils.platform_admins import send_message_to_platform_admins
from .exception import (
    BaseInvoicePaymentError, InvoicePaymentCanceledError, InvoicePaymentFailedError,
    InvoicePaymentInvoicePayedError, InvoicePaymentPayedError,
    InvoicePaymentStoreOrderStatusError,
    InvoicePaymentUnknownError,
)
from .payment_funcs import (
    export_to_external_source, save_payment_data,
    send_invoice_webhook_if_exists, topup_account,
)
from ..billing.functions import record_billing_transaction_usage

debugger = logging.getLogger('debugger')
handle_error = UnknownErrorHandler(InvoicePaymentUnknownError, BaseInvoicePaymentError)




async def finalize_payment(
        payment_data: PaymentCallBackData, invoice: Invoice,
        payment: Payment | None = None, brand: Brand | None = None,
        store_order: StoreOrder | None = None
) -> None:
    store = None

    if payment:
        if payment.status == "payed":
            raise InvoicePaymentPayedError(payment.id)

        await save_payment_data(payment, payment_data)

    if payment_data.status == "cancel":
        if payment:
            await payment.cancel()
        raise InvoicePaymentCanceledError(payment.id if payment else None)

    if payment_data.status != "success":
        if payment:
            await payment.fail()
        raise InvoicePaymentFailedError(payment.id if payment else None)

    if payment:
        await payment.confirm()
        debugger.debug(f"Payment {payment.id} was confirmed.")

    if invoice.status == "payed":
        if payment:
            await send_error_invoice_message(invoice, payment)
        raise InvoicePaymentInvoicePayedError(invoice.id)

    current_order_info = invoice.order_info or {}
    updated_order_info = {**current_order_info, **payment_data.dict(exclude_none=True)}
    await invoice.set_order_info(convert_datetime_to_str(updated_order_info))
    if not invoice.ewallet_id and payment_data.ewallet_id:
        await invoice.update(ewallet_id=payment_data.ewallet_id)

    if store_order and store_order.status_pay not in ("must_pay", "processing"):
        raise InvoicePaymentStoreOrderStatusError(
            store_order.id, store_order.status_pay
        )

    if payment and payment.ewallet_discount_amount and payment.ewallet_discount_percent:
        invoice = await crud.override_invoice_discount_amount(
            invoice,
            store_order,
            payment.ewallet_discount_percent,
            payment.ewallet_discount_amount,
            is_ewallet_discount=True
        )
    await invoice.payed(payment_method=payment_data.payment_method)

    if invoice.menu_in_store_id:
        menu_in_store = await MenuInStore.get(invoice.menu_in_store_id)
        if menu_in_store.store_id:
            store = await Store.get(menu_in_store.store_id)

    webhook_result = await send_invoice_webhook_if_exists(invoice)
    await invoice.update(webhook_result=webhook_result)

    if not store:
        if invoice.menu_in_store_id:
            menu_in_store = await MenuInStore.get(invoice.menu_in_store_id)
            if menu_in_store.store_id:
                store = await Store.get(invoice.menu_in_store.store_id)
    # Обробка лояльності для Invoice
    # Фіналізуємо транзакцію лояльності для всіх invoice (як з ордером так і без)
    if invoice.incust_transaction_id:
        from core.invoice_loyalty.service import InvoiceLoyaltyService
        invoice_loyalty_service = InvoiceLoyaltyService()
        # Закриваємо існуючу зарезервовану транзакцію та оновлюємо купони
        # finalize_invoice_loyalty тепер сам зберігає response в incust_check
        await invoice_loyalty_service.finalize_invoice_loyalty(
            invoice, is_cancel=False,
        )
    
    # Поповнення рахунку виконується тільки для invoice без ордера
    if not store_order and invoice.invoice_type == InvoiceTypeEnum.TOPUP_ACCOUNT:
        await topup_account(invoice)

    if store_order and store_order.store.external_type in ("poster", "get_order"):
        await export_to_external_source(store_order, store)

    if not store_order and invoice:
        store_order_payment = await StoreOrderPayment.get(invoice_id=invoice.id)
        if store_order_payment:
            await store_order_payment.update(
                confirmed_datetime=utcnow()
            )

    if store_order:
        store = await Store.get(store_order.store_id)
        status = OrderShippingStatusEnum.CLOSED.value if store_order.type == "topup" \
            else OrderShippingStatusEnum.PAYED.value
        await change_store_order_status(
            store_order, status, "user", source='api',
            initiated_by_user_id=store_order.user_id
        )

    if invoice.invoice_type != schemas.InvoiceTypeEnum.STORE_ORDER:
        group = await Group.get(invoice.group_id)

        await record_billing_transaction_usage(group, invoice.sum_to_pay)

        invoice_schema = await invoice_to_schema(invoice, group.lang, group)
        webhook_data = await prepare_data_for_payment_webhook(
            invoice_schema, invoice, group.id,
        )
        await add_webhook_event(
            entity=schemas.WebhookEntityEnum.PAYMENT,
            entity_id=invoice.id,
            action=schemas.WebhookActionEnum.PAID,
            group_id=invoice.group_id,
            data=webhook_data.dict(),
            data_type=schemas.WebhookPaymentDataSchema,
        )

        invoice_template = await InvoiceTemplate.get(
            invoice.invoice_template_id
        ) if invoice and invoice.invoice_template_id else None

        loyalty_settings = await LoyaltySettings.get(invoice.loyalty_settings_id) \
            if invoice and invoice.loyalty_settings_id else \
            await crud.get_loyalty_settings_for_context(
            "invoice_template" if invoice_template else ("store" if store else "brand"),
            schemas.LoyaltySettingsData(
                brand_id=brand.id if brand else None,
                store_id=store.id if store else None,
                invoice_template_id=invoice_template.id if invoice_template else None,
                profile_id=group.id,
            )
        )
        terminal_key = loyalty_settings.terminal_api_key if loyalty_settings else None

        invoice_user = await User.get_by_id(
            invoice.user_id
        ) if invoice.user_id else None

        # Отримуємо бота для правильного обчислення мови
        sender_bot_id = invoice.payment_bot_menu_id or invoice.payment_bot_id or invoice.bot_id
        client_bot = await ClientBot.get(sender_bot_id) if sender_bot_id else None

        # Обчислюємо мову з урахуванням бота
        user_lang = await invoice_user.get_lang(client_bot) if invoice_user else group.lang

        # Відправляємо повідомлення про успішну оплату через Kafka
        await add_invoice_payment_notification(
            group_id=group.id,
            brand_id=brand.id if brand else None,
            invoice_id=invoice.id,
            invoice_user_id=invoice.user_id,
            is_full_bonuses_payment=False,
            # В цьому методі завжди False, бо це звичайна оплата
            terminal_key=terminal_key,
            lang=user_lang,
            menu_in_store_id=invoice.menu_in_store_id,
        )


async def send_error_invoice_message(invoice, payment):
    text = (
        f"Error finalize_payment:\n\nInvoice has already been PAID\n\n"
        f"Invoice data:\n{invoice.id=}\n"
        f"{invoice.title=}\n"
        f"{invoice.time_created:%d.%m.%Y %H:%M:%S}\n"
        f"{invoice.user_id=}\n"
        f"{invoice.payer_id=}\n"
        f"{invoice.invoice_type=}\n"
        f"{invoice.converted_sum_to_pay=}\n"
        f"{invoice.currency=}\n"
        f"{invoice.is_friend=}\n"
        f"{invoice.menu_in_store_id=}\n\n"
        f"Payment data:\n{payment.payment_method=}\n"
        f"{payment.external_id=}\n{payment.user_id=}"
    )

    await send_message_to_platform_admins(text)
