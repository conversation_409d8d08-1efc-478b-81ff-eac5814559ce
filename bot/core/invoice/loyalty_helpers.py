"""
Helper functions for invoice loyalty operations.
"""

from db import crud
from db.models import Group, Invoice, LoyaltySettings, MenuInStore, Store, StoreOrder
from schemas import LoyaltySettingsData


async def get_loyalty_settings_for_invoice(invoice: Invoice) -> LoyaltySettings | None:
    """Отримує налаштування лояльності для рахунку.
    Порядок пошуку:
    1. За invoice.loyalty_settings_id
    2. За всіма ідентифікаторами з інвойса через crud.get_loyalty_settings_for_context
    """
    # 1. За прямим посиланням
    if invoice.loyalty_settings_id:
        ls = await LoyaltySettings.get(invoice.loyalty_settings_id)
        if ls and ls.is_enabled:
            return ls

    # 2. Контекстний пошук
    store_id = None
    if invoice.store_order:
        store_order = await StoreOrder.get(invoice.store_order_id)
        if store_order:
            store = await Store.get(store_order.store_id)
            if store:
                store_id = store.id
    elif invoice.menu_in_store_id:
        menu_in_store = await MenuInStore.get(invoice.menu_in_store_id)
        if menu_in_store and menu_in_store.store_id:
            store_id = menu_in_store.store_id

    # Отримуємо brand_id з рахунку
    group = await Group.get(invoice.group_id)
    brand = await crud.get_brand_by_group(group.id) if group else None

    # Визначаємо пріоритет target
    target = (
        "ewallet" if invoice.ewallet_id
        else "invoice_template" if invoice.invoice_template_id
        else "store" if store_id
        else "brand"
    )
    
    return await crud.get_loyalty_settings_for_context(
        target,
        LoyaltySettingsData(
            profile_id=invoice.group_id,
            brand_id=brand.id if brand else None,
            store_id=store_id,
            invoice_template_id=invoice.invoice_template_id,
            ewallet_id=invoice.ewallet_id,
        )
    )
