import base64
import logging
import math
from datetime import timedelta

from incust_api.api import term
from psutils.convertors import str_to_float
from pydantic.fields import Undefined

import schemas
from config import INVOICE_TOKEN_EXPIRES, NO_CENT_CURRENCIES
from core.helpers import make_invoice_items_text
from core.invoice.exception import (
    GroupFinanceSystemError,
    StoreCurrencyError,
)
from core.shortener import create_short_link
from db import crud
from db.models import (
    Brand, ExtraFeeSettings, Group, Invoice, InvoiceTemplate, MenuInStore, ShortLink,
    Store,
    StoreOrder, User,
)
from schemas import (
    CalculatedExtraFees, EWalletUserAccountInfo, ExtraFeeSchema,
    FriendData,
)
from schemas.loyalty_settings import LoyaltySettingsSchema
from utils.date_time import utcnow
from utils.jwt_token import create_jwt_token
from utils.numbers import format_currency
from utils.qrcode import generate_qr_code
from utils.text import f
from .invoice_to_schema import (
    get_invoice_comment_label as get_invoice_comment_label_func,
)
from ..loyalty.incust_api import incust

debugger = logging.getLogger('debugger')


async def make_invoice_items(
        entered_amount: float | None,
        invoice_template_id: int | None,
        incust_check: term.m.Check | None,
        count: int,
        product_code: str | None,
        lang: str,
):
    if not count:
        count = 1

    items: list[schemas.CreateInvoiceItemData] = []

    if entered_amount and not invoice_template_id:
        assert count == 1, "count must be 1 for entered amount"

        await create_check_item(entered_amount, incust_check, items, product_code, lang)
    elif invoice_template_id and (
            invoice_template_items := await crud.get_invoice_template_items(
                invoice_template_id
            )
    ):
        assert count >= 1, "count must be 1 or greater"

        for invoice_template_item in invoice_template_items:
            incust_item = next(
                (item for item in incust_check.check_items if (
                        (
                                invoice_template_item.item_code and item.code ==
                                invoice_template_item.item_code) or
                        (invoice_template_item.name == item.title and round(
                            invoice_template_item.price / 100, 2
                        ) == round(item.price, 2))
                )), None
            ) if incust_check else None

            items.append(
                schemas.CreateInvoiceItemData(
                    name=invoice_template_item.name,
                    category=invoice_template_item.category,
                    quantity=invoice_template_item.quantity * count,
                    item_code=invoice_template_item.item_code,
                    price=round(invoice_template_item.price / 100, 2),
                    unit_discount=incust_item.calculated_unit_discount_amount or 0 if
                    incust_item else 0,
                    unit_bonuses_redeemed=(
                        incust_item.calculated_unit_bonuses_redeemed_amount or 0
                        if incust_item else 0
                    ),
                )
            )
    elif entered_amount and invoice_template_id:
        invoice_template = await InvoiceTemplate.get(invoice_template_id)
        if (invoice_template.payment_mode ==
                schemas.InvoicePaymentModeEnum.ENTERED_AMOUNT.value):
            await create_check_item(
                entered_amount, incust_check, items, product_code, lang
            )

    return items


async def create_check_item(
        entered_amount: float,
        incust_check: term.m.Check | None,
        items: list,
        product_code: str | None,
        lang: str,
):
    incust_item = next(
        (item for item in incust_check.check_items if (
                item.category == "entered_amount"
        )), None
    ) if incust_check else None
    items.append(
        schemas.CreateInvoiceItemData(
            name=await f("invoice entered amount item name", lang),
            item_code=product_code or "entered_amount",
            price=incust_check.amount if incust_check else entered_amount,
            unit_discount=incust_item.calculated_unit_discount_amount or 0 if
            incust_check and incust_item else 0,
            unit_bonuses_redeemed=(
                incust_item.calculated_unit_bonuses_redeemed_amount or 0
                if incust_check and incust_item else 0
            ),
        )
    )


async def get_photo_path(group: Group, invoice_template: InvoiceTemplate) -> str:
    brand: Brand | None

    if invoice_template:
        if invoice_template_file_path := await invoice_template.file_path:
            return invoice_template_file_path

    if brand := await crud.get_brand_by_group(group.id):
        if brand_image_media := await brand.get_image_media():
            return brand_image_media.file_path
        if brand_logo_media := await brand.get_logo_media():
            return brand_logo_media.file_path

    return "images/7loc-white-wide.png"


async def get_incust_awards_text(
        group: Group, invoice: Invoice, lang: str, is_payed: bool | None
) -> str:
    awards = ""

    if not invoice:
        return awards

    if invoice.bonuses_added_amount or invoice.loyalty_coupons_data:
        bonuses_added = invoice.bonuses_added_amount
        coupons = invoice.loyalty_coupons_data

        if bonuses_added:
            bonuses_added = str_to_float(bonuses_added)

            bonuses_added_text = format_currency(
                bonuses_added, currency=invoice.currency, locale=group.lang,
                territory=group.country_code
            )

            added_bonuses_text = await f("service bot loyalty bonuses header", lang)
            awards += f"\n{added_bonuses_text}: {bonuses_added_text}"

        if coupons:
            added_coupons_text = await f("service bot loyalty coupons header", lang)
            awards += f"\n{added_coupons_text}: {len(coupons)}"

    if awards:
        awards = awards.replace("\n", "", 1)
        if is_payed:
            awards = await f(
                'service bot loyalty awards info after message', lang,
                awards_info=awards
            )
        else:
            awards = await f(
                'service bot loyalty awards info message', lang, awards_info=awards
            )

    return awards


async def make_invoice_message(invoice: Invoice, lang: str) -> str:
    group = await Group.get(invoice.group_id)

    msg_text = invoice.title
    if invoice.menu_in_store:
        msg_text += f"\n{invoice.menu_in_store.store.name}" if (
            invoice.menu_in_store.store) else ""
        msg_text += f"\n{invoice.menu_in_store.comment}"
    if invoice.description and invoice.title != invoice.description:
        msg_text += f"\n{invoice.description}"

    msg_text += "\n" + await f(
        "invoice number header", lang, invoice_id=invoice.id
    ) + "\n"

    msg_text += "\n\n" + await make_invoice_items_text(invoice, group.lang, lang)

    # націнки
    extra_fee = await calc_extra_fee(
        group.id,
        int(invoice.total_sum * 100),
        invoice.currency,
    )
    if extra_fee and extra_fee.total_extra_fee:
        msg_text += "\n" + await f(
            "ADMIN_EXTRA_FEES_TITLE", lang
        ) + f": " + format_currency(
            extra_fee.total_extra_fee / 100, invoice.currency, group.lang,
            territory=group.country_code
        )

    msg_text += (
        f"\n\n{await f('web store make order overall header text', lang)}: "
        f""
        f""
        f"{format_currency(invoice.converted_sum_to_pay, invoice.currency, group.lang, territory=group.country_code)}"
    )

    awards = await get_incust_awards_text(
        group, invoice, lang, True if invoice.status == 'payed' else False
    )
    if awards:
        msg_text += awards

    return msg_text


async def get_title_for_invoice(
        group: Group, store_order: StoreOrder | None = None,
        invoice_template: InvoiceTemplate | None = None,
        lang: str = 'en',
) -> tuple[str, str, str]:
    photo_path = await get_photo_path(group, invoice_template)
    if store_order:
        title = await f(
            "store order invoice title text", lang,
            store_name=store_order.store.name, order_id=store_order.id,
        )
        description = await f(
            "store order invoice description text", lang,
            store_name=store_order.store.name, order_id=store_order.id,
        )
        photo_path = "static/store_stubs/banner-stub.jpeg"
        if media := await (await Store.get(store_order.store_id)).get_image_media():
            photo_path = media.file_path
        elif brand_image_media := await (
                await crud.get_brand_by_store(store_order.store_id)).get_image_media():
            photo_path = brand_image_media.file_path
    else:
        if invoice_template:
            title = invoice_template.title
            description = invoice_template.description
        else:
            title = await f(
                "invoice title from brand text", lang,
                brand_name=group.brand.name if group.brand else group.name,
            )
            description = await f(
                "invoice description from brand text", lang,
                brand_name=group.brand.name if group.brand else group.name,
            )
    return title, description, photo_path


async def detect_currency(
        group: Group, store_order: StoreOrder | None = None
) -> str | None:
    if store_order:
        store = await Store.get(store_order.store_id)
        currency = store.currency
        debugger.debug(f'store_order currency: {currency=}')
        if not currency:
            logging.error(f'store currency not set, {store_order.id=}')
            raise StoreCurrencyError(store_order.store.id)
    else:
        store = None

    if store:
        currency = store.currency
    elif group:
        currency = group.currency
    else:
        logging.error(f'currency not set, {group.id=}')
        raise GroupFinanceSystemError(group.id)

    return currency


async def invoice_template_to_schema(
        invoice_template: InvoiceTemplate,
        lang: str,
        group: Group | None = None,
        ewallet_user_account_info: EWalletUserAccountInfo | None = None,
):
    items = [
        item.from_orm_converted(schemas.InvoiceTemplateItemSchema)
        for item in await crud.get_invoice_template_items(
            invoice_template.id
        )
    ]

    amount = sum(map(lambda x: x.price * x.quantity, items))

    loyalty_settings = await crud.get_loyalty_settings_for_context(
        "invoice_template",
        schemas.LoyaltySettingsData(
            invoice_template_id=invoice_template.id,
        )
    )

    disabled_loyalty = not loyalty_settings.is_enabled if loyalty_settings else True

    schema = schemas.InvoiceTemplateSchema(
        id=invoice_template.id,
        currency=invoice_template.currency,
        items=items,
        title=invoice_template.title,
        description=invoice_template.description,
        comment_mode=invoice_template.comment_mode,
        comment_label=await get_invoice_comment_label(lang, invoice_template, group),
        photo=await invoice_template.photo_url,
        expiration_datetime=invoice_template.expiration_datetime,
        amount=amount,
        need_name=invoice_template.need_name,
        need_email=invoice_template.need_email,
        need_phone=invoice_template.need_phone_number,
        payment_mode=invoice_template.payment_mode.value,
        disabled_qty=invoice_template.disabled_qty,
        disabled_loyalty=disabled_loyalty,
        plugins=invoice_template.plugins,
        product_code=invoice_template.product_code,
        need_auth=invoice_template.need_auth,
        ewallet_user_account_info=ewallet_user_account_info,
        max_bonuses_percent=invoice_template.max_bonuses_percent,
        loyalty_settings=LoyaltySettingsSchema.from_orm(loyalty_settings) if loyalty_settings else None,
        **invoice_template.converted_sums,
    )

    return schema


async def get_invoice_created_result(
        invoice: Invoice, lang: str, with_url: bool = False
):
    from .invoice_to_schema import invoice_to_schema
    result_schema = schemas.InvoiceCreatedResult(
        invoice=await invoice_to_schema(invoice, lang),
        token_data=schemas.Token(
            token_type="bearer",
            token=create_jwt_token(
                {
                    "sub": str(invoice.id),
                    "type": "invoice",
                    "scopes": ["invoice:read", "invoice:write"],
                }, INVOICE_TOKEN_EXPIRES
            )
        )
    )
    if with_url:
        result_schema.url, result_schema.bot_url = await get_invoice_url_with_token(
            result_schema.token_data.token, invoice.group_id,
            invoice.client_redirect_url, invoice.uuid_id,
        )

        if result_schema.url and invoice.qr_mode and invoice.qr_mode in ("web", "all"):
            qr_code = generate_qr_code(result_schema.url)
            result_schema.qr_code = base64.b64encode(qr_code.getvalue()).decode('utf-8')

        if result_schema.bot_url and invoice.qr_mode and invoice.qr_mode in (
                "bot", "all"):
            qr_code = generate_qr_code(result_schema.bot_url)
            result_schema.qr_code_bot = base64.b64encode(qr_code.getvalue()).decode(
                'utf-8'
            )

    return result_schema


async def get_payment_methods_info(
        group_id: int,
        brand_id: int | None = Undefined,
        store_id: int | None = None,
        invoice_template_id: int | None = None,
) -> schemas.PaymentMethodsInfo:
    if brand_id is Undefined:
        brand = await crud.get_brand_by_group(group_id)
        brand_id = brand.id

    if brand_id:
        payment_methods = await crud.get_payment_methods(
            brand_id, store_id=store_id, with_translations=False, for_payment_list=True,
            invoice_template_id=invoice_template_id,
        )
        debugger.debug(f'{payment_methods=} and {len(payment_methods)=}')
    else:
        payment_methods = None

    methods = []
    tg_token = None

    if payment_methods:
        for payment_method in payment_methods:
            if isinstance(payment_method, tuple):
                payment_settings = payment_method[0]
                object_payment_settings = payment_method[1] if len(
                    payment_method
                ) > 1 else None

                is_online = getattr(payment_settings, 'is_online', None)
                if is_online:
                    is_enabled = True
                    if object_payment_settings:
                        is_enabled = getattr(
                            object_payment_settings, 'is_enabled', False
                        )

                    payment_method_value = getattr(
                        payment_settings, 'payment_method', None
                    )
                    if is_enabled and payment_method_value:
                        methods.append(payment_method_value)

                        if payment_method_value == "tg_pay":
                            json_data = getattr(
                                object_payment_settings, 'json_data', {}
                            ) or {} if object_payment_settings else {}
                            if not json_data and hasattr(payment_settings, 'json_data'):
                                json_data = getattr(
                                    payment_settings, 'json_data', {}
                                ) or {}
                            tg_token = json_data.get("payment_token")

            else:
                payment_settings = payment_method
                is_online = getattr(payment_settings, 'is_online', None)
                if is_online:
                    payment_method_value = getattr(
                        payment_settings, 'payment_method', None
                    )
                    if payment_method_value:
                        methods.append(payment_method_value)

                        if payment_method_value == "tg_pay":
                            json_data = getattr(payment_settings, 'json_data', {}) or {}
                            tg_token = json_data.get("payment_token")

    return schemas.PaymentMethodsInfo(
        payment_methods=methods,
        tg_token=tg_token if tg_token and len(methods) == 1 else None,
    )


async def is_group_payments_available(group: Group):
    if group.currency:
        payment_methods_info = await get_payment_methods_info(group.id)
        return bool(
            payment_methods_info.tg_token or payment_methods_info.payment_methods
        )

    return False


async def get_invoice_url_with_token(
        token: str,
        group_id: int,
        client_redirect_url: str | None = None,
        uuid_id: str | None = None,
) -> tuple[str, str | None]:
    brand: Brand = await crud.get_brand_by_group(group_id)
    if not brand:
        raise Exception(f"Brand not found for group_id: {group_id}")

    url = brand.get_url(f"invoice", invoice_token=token)

    if url and client_redirect_url:
        url += f"&client_redirect_url={client_redirect_url}"

    expiration_datetime = utcnow() + timedelta(hours=1)

    short_url: ShortLink = await create_short_link(
        "url", url, expiration_datetime=expiration_datetime
    )
    if short_url:
        url = short_url.short_url

    bot_url = None
    if bot := await crud.get_bot_by_brand(brand.id):
        if uuid_id:
            decoded_uuid = base64.b64encode(uuid_id.encode('utf-8')).decode('utf-8')
            if bot.bot_type == 'telegram':
                bot_url = f"https://t.me/{bot.username}?start=extpi-{decoded_uuid}"
            elif bot.bot_type == 'whatsapp':
                bot_url = (
                    f"https://wa.me/"
                    f"{bot.whatsapp_from_phone_number}"
                    f"?text=extpi-{decoded_uuid}"
                )

            if bot_url:
                bot_short_link: ShortLink = await create_short_link(
                    "url", bot_url, expiration_datetime=expiration_datetime
                )
                bot_url = bot_short_link.short_url

    return url, bot_url


async def is_valid_friend_for_invoice(
        invoice_id: int, user_id: int, friend_id: int, need_write: bool = False
):
    user_friends: list[FriendData] = await crud.get_user_friends(user_id, None)
    if friend_id not in [friend.user_id for friend in user_friends]:
        return False
    if await crud.check_invoice_by_friend(invoice_id, friend_id, need_write):
        return True
    return False


async def get_invoice_comment_label(
        lang: str,
        invoice_template: InvoiceTemplate | None = None,
        group: Group | None = None,
):
    return await get_invoice_comment_label_func(lang, invoice_template, group)


async def process_loyalty(
        brand: Brand, user: User, data: schemas.CreateInvoiceData, lang: str,
        store_id: int | None = None, menu_in_store: MenuInStore | None = None,
) -> term.m.Check | None:

    check = await receipt_to_incust_check(brand, data)

    if menu_in_store:
        store_id = menu_in_store.store_id

    # Отримуємо налаштування лояльності
    target = "store" if store_id else "brand"
    loyalty_settings = await crud.get_loyalty_settings_for_context(
        target,
        schemas.LoyaltySettingsData(
            brand_id=brand.id,
            store_id=store_id,
            profile_id=brand.group_id,
        )
    )

    if not loyalty_settings:
        return None

    async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
        result_check = await api.terminal_process_check_by_rules(
            "by-charge-only-rules",
            check,
        )

    if not result_check:
        return None

    return result_check


async def receipt_to_incust_check(
        brand: Brand, receipt: schemas.CreateInvoiceData
) -> term.m.Check:
    if receipt.check_items:
        items = receipt.check_items
        amount = sum([item.price * item.quantity for item in items])
    else:
        amount = receipt.amount
        items = None

    group: Group = await Group.get(brand.group_id)
    if not group:
        raise Exception("Group not found")
    currency = group.currency

    check = term.m.Check(
        amount=amount,
        amount_to_pay=amount,
        payment_type="currency",
        payment_id=currency,
        check_items=[
            term.m.CheckItem(
                title=item.name,
                code=item.name,
                price=item.price,
                quantity=item.quantity,
                amount=item.price * item.quantity,
                category="uncategorized",
                quantity_decimal_digits=2,
            )
            for item in items
        ] if items else None,
    )

    return check


def invoice_to_incust_data(invoice: Invoice) -> schemas.CreateInvoiceData:
    if invoice.prices:
        items = [schemas.CreateInvoiceCheckItem(
            price=item.get("amount", 0),
            quantity=item.get("count", 1),
            name=item.get("label", "unknown"),
            item_id=item.get("item_id", "unknown_item_id"),
        ) for item in
            invoice.prices]
        amount = sum([item.price * item.quantity for item in items])
    else:
        amount = invoice.raw_total_amount
        items = None
    return schemas.CreateInvoiceData(
        amount=amount, currency=invoice.currency, check_items=items
    )


async def calc_extra_fee(
        group_id: int,
        total_sum: int,
        currency: str,
        invoice_id: int | None = None,
        order_id: int | None = None,
        is_topup: bool = False,
) -> CalculatedExtraFees:

    total_extra_fee = 0
    journal_entries = []

    # if total_sum == 0 than return 0 for all fees

    if total_sum == 0:
        return CalculatedExtraFees(
            total_extra_fee=total_extra_fee,
            journal_entries=journal_entries,
        )

    extra_fees: list[ExtraFeeSettings] = await ExtraFeeSettings.get_list(
        group_id=group_id, is_active=True, is_deleted=False
    )

    for fee in extra_fees:
        if not fee.is_active:
            continue

        fee_amount = 0

        if fee.extra_fee_percent:
            try:
                fee_percent = total_sum * float(fee.extra_fee_percent) / 100
                fee_amount += fee_percent
            except ValueError:
                print(
                    f"Invalid percent value for fee {fee.id}: {fee.extra_fee_percent}"
                )

        if fee.extra_fee_value:
            try:
                fee_value = 100 * float(fee.extra_fee_value)
                fee_amount += fee_value
            except ValueError:
                print(f"Invalid value for fee {fee.id}: {fee.extra_fee_value}")

        if fee_amount:
            if currency in NO_CENT_CURRENCIES:
                fee_amount = math.ceil(fee_amount / 100) * 100
                applied_amount_float = int(fee_amount / 100)
            else:
                fee_amount = math.ceil(fee_amount)
                applied_amount_float = round(fee_amount / 100, 2)

            total_extra_fee += fee_amount

            journal_entry = ExtraFeeSchema(
                extra_fee_id=fee.id,
                name=fee.name,
                extra_fee_percent=fee.extra_fee_percent,
                extra_fee_value=fee.extra_fee_value,
                applied_amount=fee_amount,
                invoice_id=invoice_id,
                order_id=order_id,
                applied_amount_float=applied_amount_float,
            )
            journal_entries.append(journal_entry)

    return CalculatedExtraFees(
        total_extra_fee=total_extra_fee if not is_topup else 0,
        journal_entries=journal_entries
    )
