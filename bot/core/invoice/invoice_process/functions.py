from core.invoice.invoice_process.invoice_processor import InvoiceProcessor
from utils.numbers import format_sum


async def get_invoice_template_items_text(
        processor: InvoiceProcessor,
):
    lines = []

    group = await processor.state.group

    for item in await processor.state.items:
        quantity = item.quantity * processor.state.data.count
        lines.append(
            f"{item.name} x {quantity}    "
            f"{format_sum(item.price / 100 * quantity, group.lang)}"
        )

    return "\n".join(lines)
