import logging
from contextlib import suppress

from aiogram import types
from aiogram.dispatcher import FSMContext
from fastapi import HTTPException
from psutils.exceptions import ErrorWithTextVariable
from psutils.forms.helpers import save_messages_to_state
from psutils.state_router import Router

from db.models import ClientBot, MenuInStore, Store, User
from utils.message import send_or_edit_message
from utils.numbers import format_currency, format_sum
from utils.text import c, f, fd
from .functions import get_invoice_template_items_text
from .invoice_processor import InvoiceProcessor
from .keyboards import add_cancel_button, get_confirmation_keyboard, get_count_keyboard
from .state import InvoiceProcess
from .. import InvoiceService, finish_process_full_bonus_payment, show_invoice
from ...funcs import handle_standard_exceptions
from ...incust.helpers import get_currency_from_store_or_brand
from ...loyalty.helper import count_max_redeem_sum_and_percent, make_loyalty_awards_text


async def send_count_menu(
        message: types.Message, processor: InvoiceProcessor, lang: str,
        new: bool = False
):
    message_text = await f(
        "invoice count menu header", lang,
        title=await processor.state.INVOICE_TEMPLATE,
        items=await get_invoice_template_items_text(processor),
    )

    keyboard = await get_count_keyboard(processor.state.data.count, lang)
    return await send_or_edit_message(
        message, message_text, keyboard=keyboard, new=new, delete_old_message=False
    )


async def send_amount_menu(message: types.Message, lang: str, new: bool):
    message_text = await f("invoice amount menu header", lang)
    keyboard = await add_cancel_button(lang)
    return await send_or_edit_message(
        message, message_text, keyboard=keyboard, new=new, delete_old_message=False
    )


async def send_payment_data_menu(
        message: types.Message, state: FSMContext, lang: str, mode: str
):
    async with InvoiceProcessor(state) as processor:
        if await processor.state.items:
            return await send_count_menu(message, processor, lang, new=mode == "new")
        else:
            return await send_amount_menu(message, lang, new=mode == "new")


async def send_confirmation_menu(
        message: types.Message, state: FSMContext, user: User, lang: str
):
    async with (InvoiceProcessor(state) as processor):
        brand = await processor.state.brand
        menu_in_store = None
        if processor.state.data.menu_in_store_id:
            menu_in_store = await MenuInStore.get(processor.state.data.menu_in_store_id)

        invoice_template = await processor.state.invoice_template

        currency = await get_currency_from_store_or_brand(
            menu_in_store=menu_in_store, group_id=brand.group_id,
            invoice_template=invoice_template,
        )
        
        # Отримуємо налаштування лояльності (якщо потрібно для подальшого використання)

        group = await processor.state.group
        bonuses_redeem_amount = processor.state.data.bonuses_redeem_amount
        currency = currency
        max_bonuses = 0
        max_percent = 0

        loyalty_settings = await processor.state.loyalty_settings

        texts = await fd(
            {
                "entered_amount_title": "invoice entered amount confirmation title",
                "subtotal": "check subtotal text",
                "bonuses_to_be_redeemed": "check bonuses to be redeemed text",
                "discount_text": "check discount text",
                "total": "check total amount text",
                "redeem_bonuses_text": "invoice confirmation menu redeem bonuses text",
                "no_bonuses_available_text": "invoice confirmation menu no bonuses "
                                             "available text",
                "redeem_bonuses_not_possible_text": "invoice confirmation menu redeem"
                                                    " bonuses not possible text",
                "header": "invoice confirmation menu header",
                "processing": "processing text",
            }, lang
        )

        if items := await processor.state.items:
            subtotal = sum(
                map(
                    lambda x: x.price / 100 * x.quantity * processor.state.data.count,
                    items
                )
            )

            title = (await processor.state.INVOICE_TEMPLATE).title
            items_text = await get_invoice_template_items_text(processor)
        else:
            subtotal = processor.state.data.amount

            title = texts["entered_amount_title"]

            entered_amount_item_name = await f("invoice entered amount item name", lang)
            formatted_amount = format_sum(
                processor.state.data.amount, group.lang, currency=currency
            )
            items_text = f"{entered_amount_item_name}  {formatted_amount}"

        if bonuses_redeem_amount and bonuses_redeem_amount > subtotal:
            bonuses_redeem_amount = subtotal
            await processor.update_state_data(
                bonuses_redeem_amount=bonuses_redeem_amount
            )

        if loyalty_settings and loyalty_settings.is_enabled:
            if not message.text.endswith(texts["processing"]):
                await message.edit_text(
                    (message.html_text or "") + "\n\n" + texts["processing"],
                    reply_markup=message.reply_markup
                )

            user_message = types.Message.get_current()
            if user_message and user_message.from_user.is_bot is False:
                with suppress(Exception):
                    await user_message.delete()

            try:
                processed_check = await processor.process_check(user, brand, lang)
            except Exception as e:
                try:
                    await handle_standard_exceptions(e, lang)
                except HTTPException as http_ex:
                    error_message = await message.answer(
                        http_ex.detail
                    )
                    await save_messages_to_state(state, error_message)
                    return message
                except Exception:
                    error_message = await message.answer(
                        str(e)
                    )
                    await save_messages_to_state(state, error_message)
                    return message

            bonuses_redeem_amount = processed_check.bonuses_redeemed_amount
            discount_amount = processed_check.discount_amount
            total = processed_check.amount_to_pay
        else:
            bonuses_redeem_amount = 0
            discount_amount = 0
            total = subtotal

        total_texts = []

        if bonuses_redeem_amount or discount_amount:
            total_texts.append(
                f"{texts['subtotal']}  "
                f"{format_sum(subtotal, group.lang, currency=currency)}"
            )
            if bonuses_redeem_amount:
                total_texts.append(
                    f"{texts['bonuses_to_be_redeemed']}    "
                    f"{format_sum(bonuses_redeem_amount, group.lang, currency=currency)}"
                )
            if discount_amount:
                total_texts.append(
                    f"{texts['discount_text']}    "
                    f"{format_sum(discount_amount, group.lang, currency=currency)}"
                )

        total_texts.append(
            f"<b>{texts['total']}    "
            f"{format_currency(total, currency, group.lang)}</b>"
        )

        message_text = texts["header"].format(
            title=title,
            items=items_text,
            total_text="\n".join(total_texts)
        )

        if loyalty_settings and processed_check:
            awards_text = await make_loyalty_awards_text(
                processed_check, loyalty_settings, currency, lang, False
            )
            message_text += f"\n\n\n<i>{awards_text}</i>"

            max_bonuses_percent = (
                float(invoice_template.max_bonuses_percent)
                if invoice_template and invoice_template.max_bonuses_percent
                else 100
            )
            max_bonuses, max_percent, available_bonuses = \
                await count_max_redeem_sum_and_percent(
                    user, loyalty_settings,
                    subtotal, currency, lang, discount_amount,
                    max_bonuses_percent,
                )

            if available_bonuses:
                if max_bonuses:
                    bonuses_text = texts["redeem_bonuses_text"]
                else:
                    bonuses_text = texts["redeem_bonuses_not_possible_text"]
                bonuses_text = bonuses_text.format(
                    available=format_sum(
                        available_bonuses,
                        group.lang,
                        currency=currency
                    )
                )
            else:
                bonuses_text = texts["no_bonuses_available_text"]

            message_text += "\n\n\n" + bonuses_text

        keyboard = await get_confirmation_keyboard(
            processor, subtotal, lang, max_bonuses, max_percent
        )
        return await send_or_edit_message(message, message_text, keyboard=keyboard)


async def save_invoice_menu(
        message: types.Message, state: FSMContext, user: User, bot: ClientBot, lang: str
):
    async with InvoiceProcessor(state) as processor:
        try:
            group = await processor.state.group
            incust_check_schema = processor.state.data.incust_check
            incust_check_dict = (
                incust_check_schema.dict()
                if incust_check_schema else None
            )
            invoice_service = InvoiceService(
                group=group, user=user
            )
            invoice = await invoice_service.create_invoice(
                invoice_type=processor.state.data.invoice_type,
                payment_mode=processor.state.data.payment_mode.value,
                menu=await processor.state.menu_in_store,
                invoice_template=await processor.state.invoice_template,
                bot=bot,
                count=processor.state.data.count,
                entered_amount=processor.state.data.amount,
                incust_check=incust_check_dict,
            )
            if (
                    processor.state.data.incust_check and
                    processor.state.data.incust_check.amount_to_pay == 0
            ):
                brand = await processor.state.brand
                
                loyalty_settings = await processor.state.loyalty_settings
                
                await finish_process_full_bonus_payment(
                    group, brand, loyalty_settings, invoice, lang=lang
                )
            else:
                menu_in_store = await processor.state.menu_in_store
                store_id = None
                if menu_in_store.store_id:
                    store = await Store.get(menu_in_store.store_id)
                    if store:
                        store_id = store.id
                payload = c("pay_for_invoice", invoice_id=invoice.id)
                await show_invoice(invoice, payload, lang=lang, store_id=store_id)
        except Exception as e:
            logging.exception(e, exc_info=True)
            if isinstance(e, ErrorWithTextVariable):
                error_text = await f(e.text_variable, lang, **e.text_kwargs)
            else:
                error_text = await f("invoice process unknown error", lang)
            error_message = await message.answer(error_text)
            await save_messages_to_state(state, error_message)
            await InvoiceProcess.Confirmation.set()
            return await Router.state_menu(message, state, lang)
        else:
            await message.delete()
            await state.finish()


def register_invoice_process_routes(router: Router):
    router.add_route(InvoiceProcess.PaymentData, send_payment_data_menu)
    router.add_route(InvoiceProcess.Confirmation, send_confirmation_menu)
    router.add_route(InvoiceProcess.Save, save_invoice_menu)
