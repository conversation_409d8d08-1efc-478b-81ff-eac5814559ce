from .callback_data import ConfirmationCallbackData, SetBonusesRedeemCallbackData, SetCountCallbackData
from .invoice_processor import InvoiceProcessor

from utils.keyboards import next_button
from utils.redefined_classes import InlineBtn, InlineKb
from utils.text import f


async def add_cancel_button(lang: str, keyboard: InlineKb | None = None):
    if not keyboard:
        keyboard = InlineKb()
    keyboard.row(
        InlineBtn(
            await f("invoice process cancel button", lang),
            callback_data="cancel"
        )
    )
    return keyboard


def add_count_buttons(count: int, keyboard: InlineKb | None = None):
    if not keyboard:
        keyboard = InlineKb()

    keyboard.insert(
        InlineBtn(
            "➖",
            callback_data=SetCountCallbackData(count="-").to_str(),
        )
    )
    keyboard.insert(InlineBtn(str(count), callback_data="-"))
    keyboard.insert(
        InlineBtn(
            "➕",
            callback_data=SetCountCallbackData(count="+").to_str(),
        )
    )
    return keyboard


async def get_count_keyboard(count: int, lang: str):
    keyboard = add_count_buttons(count)
    keyboard = await add_cancel_button(lang, keyboard)
    keyboard.insert(await next_button(lang))
    return keyboard


async def get_confirmation_keyboard(
        processor: InvoiceProcessor,
        subtotal: float,
        lang: str,
        max_bonuses: float | None = None,
        max_bonuses_percent: float | None = None,
):
    keyboard = InlineKb()

    if await processor.state.items:
        add_count_buttons(processor.state.data.count, keyboard)
    else:
        keyboard.row(
            InlineBtn(
                await f("invoice change amount button", lang),
                callback_data=ConfirmationCallbackData(action="change_data").to_str()
            )
        )

    if bonuses := processor.state.data.bonuses_redeem_amount:
        percent = round(bonuses * 100 / subtotal, 2)
        keyboard.row(
            InlineBtn(
                await f(
                    "invoice cancel bonuses button", lang,
                    bonuses=bonuses, percent=percent,
                ),
                callback_data=SetBonusesRedeemCallbackData(ip_bonuses_redeem_amount=0).to_str()
            )
        )
    elif max_bonuses and max_bonuses_percent:
        keyboard.row(
            InlineBtn(
                await f(
                    "invoice redeem max bonuses button", lang,
                    bonuses=max_bonuses, percent=max_bonuses_percent,
                ),
                callback_data=SetBonusesRedeemCallbackData(ip_bonuses_redeem_amount=max_bonuses).to_str()
            )
        )

    await add_cancel_button(lang, keyboard)
    keyboard.insert(
        InlineBtn(
            await f("invoice confirm button", lang),
            callback_data=ConfirmationCallbackData(action="confirm").to_str()
        )
    )

    return keyboard
