from psutils.forms import WizardF<PERSON>, fields

from ..state import InvoiceProcess
from .data_processors import payment_data_processor, set_count_data_processor
from .data_savers import cancel_data_saver, confirmation_data_saver
from .validators import PaymentDataValidator
from ..callback_data import ConfirmationCallbackData, SetBonusesRedeemCallbackData, SetCountCallbackData


class InvoiceProcessForm(WizardForm):
    state_group = InvoiceProcess

    __extra_field__ = fields.InlineButtonsField(
        callback_mode="cancel",
        data_saver=cancel_data_saver,
        handler_state=state_group,
        need_go_next_state=False,
        need_setup_not_buttons_click_handler=False,
    )

    payment_data = fields.TextField(
        input_validator=PaymentDataValidator(),
        data_processor=payment_data_processor,
    ) & fields.InlineButtonsField(
        SetCountCallbackData,
        data_processor=set_count_data_processor,
        need_go_next_state=False,
    )

    confirmation = fields.FloatField(
        return_field_name="ip_bonuses_redeem_amount",
        need_go_next_state=False,
    ) & fields.InlineButtonsField(
        SetBonusesRedeemCallbackData,
        need_go_next_state=False
    ) & fields.InlineButtonsField(
        ConfirmationCallbackData,
        data_saver=confirmation_data_saver,
        need_go_next_state=False,
    )
