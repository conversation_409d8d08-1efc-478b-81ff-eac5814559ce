from aiogram import types
from aiogram.dispatcher import FSMContext
from psutils.forms.exceptions import CancelField

from core.invoice.invoice_process.state import InvoiceProcess
from utils.message import send_or_edit_message
from utils.text import f


async def confirmation_data_saver(data: dict):
    match data["action"]:
        case "change_data":
            await InvoiceProcess.PaymentData.set()
        case "confirm":
            await InvoiceProcess.Save.set()
        case action:
            raise ValueError(f"Invalid action: {action}")


async def cancel_data_saver(_, message: types.Message, state: FSMContext, lang: str):
    await state.finish()
    await send_or_edit_message(message, await f("invoice process canceled text", lang))
    raise CancelField()
