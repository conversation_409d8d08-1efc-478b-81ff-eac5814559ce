from aiogram import types
from aiogram.dispatcher import FSMContext
from psutils.convertors import str_to_float, str_to_int
from psutils.forms.validators.base import Validator

from core.invoice.invoice_process.invoice_processor import InvoiceProcessor


class PaymentDataValidator(Validator):
    async def validate(self, message: types.Message, state: FSMContext):
        text = message.text

        data = {}

        async with InvoiceProcessor(state) as processor:
            if await processor.state.is_items:
                data["invoice_count"] = str_to_int(text, only_positive=True)
            else:
                data["invoice_amount"] = str_to_float(text, only_positive=True)

        return data
