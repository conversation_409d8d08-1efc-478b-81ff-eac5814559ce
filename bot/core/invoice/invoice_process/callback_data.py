from typing import Literal

from psutils.callback_data import CallbackData
from pydantic import Field


class SetBonusesRedeemCallbackData(CallbackData, callback_mode="set_bonuses_redeem"):
    ip_bonuses_redeem_amount: float = Field(alias="amount")


class SetCountCallbackData(CallbackData, callback_mode="set_count"):
    count: int | Literal["+", "-"]


class ConfirmationCallbackData(CallbackData, callback_mode="confirmation"):
    action: Literal["change_data", "confirm"]
