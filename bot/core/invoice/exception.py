from abc import ABC

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BaseInvoiceError(ErrorWithHTTPStatus, ABC, base=True):
    def __init__(self, message: str = "Invoice error", context: dict | None = None, **kwargs):
        self.message = message
        self.context = context
        super().__init__(**kwargs)

    def __repr__(self):
        return f"Invoice error: {self.message}"

    def __str__(self):
        return f"{self.message}"


class InvoiceUnknownError(BaseInvoiceError):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    text_variable = "invoice unknown error"

    def __init__(self, **kwargs):
        super().__init__("Invoice failed with unknown error", **kwargs)


class InvoiceTemplateError(BaseInvoiceError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "invoice template must belong same profile as vm error"


class GroupFinanceSystemError(BaseInvoiceError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "invoice profile must have finance system error"

    def __init__(self, group_id: int):
        super().__init__(
            f"group_id: '{group_id}' not set finance system'",
            group_id=group_id,
        )


class InvoiceFinanceFaceError(BaseInvoiceError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "invoice profile must have finance face error"

    def __init__(self, group_id: int):
        super().__init__(
            f"group_id: '{group_id}' not have finance face'",
            group_id=group_id,
        )


class StoreCurrencyError(BaseInvoiceError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "store currency not set error text"

    def __init__(self, store_id: int):
        super().__init__(
            f"store for [{store_id=}] not have currency'",
            store_id=store_id,
        )


class CreateOrderStoreLoyaltyCurrencyError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store and loyalty currency not equal"

    def __init__(self, currency: str, loyalty_currency: str):
        super().__init__(
            f"store and loyalty currency not equal {currency=}, {loyalty_currency=}",
            currency=currency, loyalty_currency=loyalty_currency,
        )


class CreateOrderNoIncustCheckForIncustBrandError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store order data was changed message"

    def __init__(self, reason_code: str):
        super().__init__(
            "create order no incust check for incust brand error",
            reason_code=reason_code,
            detail_data={
                "error_code": "store_order_missing_incust_check",
            }
        )


class CreateOrderGiftProductsQtyError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order gift product qty error"


class CreateOrderTypeProductsQtyError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order gift product qty error"

    def __init__(self, types: str):
        super().__init__(types=types)


class CreateOrderProductWithTypeNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order product not found type error"


class CreateOrderTopupProductsTypeError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order topup products type error"


class CreateOrderGiftProductsTypeError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order gift product type error"


class CreateOrderGiftFloatingSumError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order gift floating sum error"


class CreateOrderNeedAuthProductsInCartError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order need auth products in cart error"


class CreateOrderNoIncustDataForTopupError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order no incust data for topup error"


class CreateOrderNoIncustPayConfigForTopupError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order no incust pay config for topup error"


class CreateOrderNotFoundSpecialAccountForTopupError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order not found special account for topup error"


class CreateOrderNotMatchAccountForTopupError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order not match account for topup error"  # TODO: add localization


class InvoiceNotPricesError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "prices not in template and not entered"


class CreateInvoiceNoItemsError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create invoice no items error"


class InvoiceItemsAndAmountError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "cant process items and amount together"


class InvoiceCreateError(ErrorWithHTTPStatus):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    text_variable = "invoice create failed"

    groups = [
        "create_invoice_for_integration"
    ]


class InvoiceInvalidTotalAmountError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice invalid total amount message"

    def __init__(self, total_amount: float, currency: str):
        super().__init__(
            f"total amount to small {total_amount=}, {currency=}",
            total_amount=total_amount, currency=currency
        )


class InvoiceNotPaymentMethodsError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "no payment methods available"


class InvoicePaymentProviderInvalidError(BaseInvoiceError):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    text_variable = "payment provider error"


class InvoicePricesAndAmountError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "only one of the arguments should be provided"


class InvoiceInvalidPaymentModeError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice invalid payment mode"

    def __init__(self, payment_mode: str):
        super().__init__(
            f"payment object not equal payment mode {payment_mode=}",
            payment_mode=payment_mode
        )


class BaseInvoicePaymentError(ErrorWithHTTPStatus, ABC, base=True):
    def __init__(self, message: str | None = "", context: dict | None = None, **kwargs):
        self.message = message
        self.context = context
        super().__init__(**kwargs)

    def __repr__(self):
        return f"Payment error: {self.message}"

    def __str__(self):
        return f"{self.message}"


class InvoicePaymentUnknownError(BaseInvoicePaymentError):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    text_variable = "Payment unknown error"

    def __init__(self, **kwargs):
        super().__init__("Finalize payment failed with unknown error", **kwargs)


class InvoicePaymentNotFoundError(BaseInvoicePaymentError):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "payment not found"

    def __init__(self, payment_data: dict):
        super().__init__(
            f"Not found payment with data {payment_data=}"
        )


class InvoicePaymentInvoiceNotFoundError(BaseInvoicePaymentError):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "not found invoice for payment"

    def __init__(self, payment_id: int | None):
        super().__init__(
            f"Not found invoice for payment {payment_id=}", payment_id=payment_id
        )


class InvoicePaymentInvoiceStatusError(BaseInvoicePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice status error"

    def __init__(self, invoice_id: int, invoice_status: str):
        super().__init__(
            f"invoice status error {invoice_id = }, {invoice_status = }",
            invoice_id=invoice_id, invoice_status=invoice_status,
        )


class InvoicePaymentInvoicePayedError(BaseInvoicePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice already payed"

    def __init__(self, invoice_id: int):
        super().__init__(
            f"invoice already payed {invoice_id = }", invoice_id=invoice_id
        )


class InvoicePaymentPayedError(BaseInvoicePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment already payed"

    def __init__(self, payment_id: int):
        super().__init__(
            f"payment already payed {payment_id = }", payment_id=payment_id,
        )


class InvoicePaymentStoreOrderStatusError(BaseInvoicePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store order status error"

    def __init__(self, store_order_id: int, order_status: str):
        super().__init__(
            f"store order status error {store_order_id = }, {order_status = }",
            store_order_id=store_order_id,
            order_status=order_status,
        )


class InvoicePaymentCanceledError(BaseInvoicePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment canceled"

    def __init__(self, payment_id: int):
        super().__init__(
            f"payment canceled {payment_id=}", payment_id=payment_id,
        )


class InvoicePaymentUnknownStatusError(BaseInvoicePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment unknown status"


class InvoicePaymentFailedError(BaseInvoicePaymentError):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    text_variable = "payment failed error text"

    def __init__(self, payment_id: int):
        self.payment_id = payment_id
        super().__init__(
            payment_id=payment_id
        )


class InvoicePaymentTransactionError(BaseInvoicePaymentError):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    text_variable = "transaction create failed"

    def __init__(self, invoice_id: int):
        super().__init__(
            f"payment failed {invoice_id=}", invoice_id=invoice_id,
        )


class InvoiceGroupRequiredError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice group required error"

    def __init__(self):
        super().__init__("Group is required for this method")


class InvoiceUserRequiredError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice user required error"

    def __init__(self):
        super().__init__("User is required for this method")


class InvoiceRequiredError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice required error"

    def __init__(self):
        super().__init__("Invoice is required for this method")


class InvoiceNotFoundError(BaseInvoiceError):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "invoice not found error"

    def __init__(self, invoice_id: int | None = None):
        self.invoice_id = invoice_id
        super().__init__(
            f"Invoice {invoice_id=} not found",
            invoice_id=invoice_id,
        )


class InvoiceNoPermissionsError(BaseInvoiceError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "invoice no permissions error"

    def __init__(self, invoice_id: int):
        self.invoice_id = invoice_id
        super().__init__(
            f"No permissions to access invoice #{invoice_id}",
            invoice_id=invoice_id,
        )


class NotInvoiceTokenError(BaseInvoiceError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "not invoice token error"

    groups = ["invoice_token_error"]

    def __init__(self, token_data: dict):
        self.token_data = token_data
        super().__init__(
            f"Not invoice token. {token_data = }"
        )


class InvoiceInvalidProfileApiTokenError(BaseInvoiceError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "invoice invalid profile api token error"

    groups = [
        "validate_group_for_integration"
    ]

    def __init__(self, api_token: str):
        self.api_token = api_token
        super().__init__(
            f"Invalid group api token: {api_token}"
        )


class InvoiceCheckItemsEmptyError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice check items empty error"

    groups = [
        "create_invoice_for_integration",
    ]


class InvoiceNotTelegramUserError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice not telegram user error"

    groups = [
        "validate_user_for_integration",
    ]


class InvoiceProfileDoesNotHaveABotError(BaseInvoiceError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "invoice profile does not have a bot error"

    groups = [
        "validate_group_for_integration"
    ]

    example_text_params = {
        "group_name": "Profile name"
    }

    def __init__(self, group_name: str):
        self.group_name = group_name
        super().__init__(
            f"profile {group_name} does not have a bot",
            group_name=group_name
        )


class InvoiceProfilePaymentsNotAvailableError(BaseInvoiceError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "invoice profile payments not available error"

    groups = [
        "validate_group_for_integration",
    ]

    example_text_params = {
        "group_name": "Profile name"
    }

    def __init__(self, group_name: str):
        self.group_name = group_name
        super().__init__(
            f"payments is not available for profile {group_name}",
            group_name=group_name
        )


class PaymentEventDataError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice event data required error"

    def __init__(self):
        super().__init__("Invoice event data required error")


class InvoiceAgreementNotAcceptedError(BaseInvoiceError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "invoice agreement not accepted error"


class InvoiceEmailIsRequiredError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice email is required error"


class InvoicePhoneIsRequiredError(BaseInvoiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice phone is required error"
