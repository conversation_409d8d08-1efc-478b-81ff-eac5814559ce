import asyncio
import logging
from dataclasses import dataclass

from psutils.date_time import localise_datetime
from psutils.mailing import GmailClient
from psutils.text import replace_html_symbols

import config as cfg
from config import (
    CRM_HOST, DATETIME_FORMAT_SECONDS, SERVICE_BOT_API_TOKEN,
    SERVICE_BOT_USERNAME,
)
from core import messangers_adapters as ma
from core.bot.ewallet_handlers import process_single_ewallet
from core.external_coupon import send_coupon_info
from core.invoice.functions import (
    get_invoice_comment_label,
)
from core.kafka.producer.functions import (
    add_push_notifications_for_action,
    add_telegram_notifications_for_action,
)
from core.kafka.producer.helpers import add_items_text, build_fcm_message
from core.payment.utils.extra_fees import (
    get_extra_fee_str, get_extra_fee_txt,
    get_formated_extra_fees,
)
from core.templater import templater
from core.whatsapp.functions import send_delayed_wa_menu
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db import crud
from db.models import (
    Brand, ClientBot, EWallet, Group, Invoice, InvoiceTemplate, MenuInStore,
    StoreOrderPayment, User, UserClientBotActivity,
)
from schemas import (
    AuthSourceEnum, IncustCheckInfoData, IncustInfoData,
    InvoiceEmailTemplate,
    InvoiceMessageData, InvoiceTypeEnum,
)
from utils.email_funcs import send_with_attachments
from utils.message import send_tg_message, send_wa_message
from utils.numbers import format_currency
from utils.platform_admins import send_message_to_platform_admins
from utils.redefined_classes import Bot
from utils.text import f, fd, html_to_markdown

debugger = logging.getLogger('debugger')


async def make_loyalty_check_info_message(
    invoice: Invoice,
    lang: str,
    group: Group,
    currency: str,
    show_awards: bool,
    show_redeemed: bool,
) -> str:
    """Створює повідомлення про лояльність для Service Bot - ТІЛЬКИ базовий текст як в мастері."""
    try:
        # Для Service Bot НЕ потрібно детальну інформацію про лояльність
        # В мастері Service Bot отримує тільки базовий текст без детальної розбивки
        return ""
        
    except Exception as ex:
        logging.error(f"Error creating loyalty check info message: {ex}", exc_info=True)
        return ""




async def get_coupons_data_from_invoice(
    invoice: Invoice,
    lang: str,
    need_pdf_file: bool,
    user: User,
) -> list:
    """Отримує дані купонів зі збережених даних invoice."""
    try:
        # Використовуємо збережені купони з invoice.loyalty_coupons_data
        if invoice.loyalty_coupons_data:
            from schemas.incust.base import CouponShowData
            from core.loyalty.incust_api import incust
            
            result_coupons = []
            
            # Отримуємо налаштування лояльності
            loyalty_settings = None
            if invoice.loyalty_settings_id:
                from db.models import LoyaltySettings
                loyalty_settings = await LoyaltySettings.get(invoice.loyalty_settings_id)
            
            # Якщо є налаштування і транзакція завершена - пробуємо отримати актуальні дані купонів
            if loyalty_settings and invoice.incust_transaction_id and invoice.is_loyalty_transaction_completed:
                try:
                    # Перевіряємо чи в збережених даних є тільки коди серій (без ID)
                    has_only_batch_codes = any(
                        not coupon_dict.get('id') and coupon_dict.get('code') and 
                        len(str(coupon_dict.get('code', '')).replace('-', '')) == 12 
                        for coupon_dict in invoice.loyalty_coupons_data
                    )
                    
                    if has_only_batch_codes:
                        # Отримуємо всі купони користувача
                        async with incust.term.CouponsApi(loyalty_settings, lang=lang) as api:
                            user_coupons = await api.customer_coupons(
                                user_id_value=user.incust_external_id,
                                user_id_type="external-id"
                            )
                        
                        if user_coupons:
                            # Групуємо купони за batch_id
                            coupons_by_batch = {}
                            for coupon in user_coupons:
                                if hasattr(coupon, 'batch') and coupon.batch and hasattr(coupon.batch, 'id'):
                                    batch_id = coupon.batch.id
                                    if batch_id not in coupons_by_batch:
                                        coupons_by_batch[batch_id] = []
                                    coupons_by_batch[batch_id].append(coupon)
                            
                            updated_coupons_data = []
                            # Для кожного збереженого купона з кодом серії
                            for saved_coupon in invoice.loyalty_coupons_data:
                                batch_info = saved_coupon.get('batch', {})
                                batch_id = batch_info.get('id') if batch_info else None
                                
                                if batch_id and batch_id in coupons_by_batch:
                                    # Беремо перший не використаний купон з цієї серії
                                    batch_coupons = coupons_by_batch[batch_id]
                                    selected_coupon = None
                                    
                                    # Пріоритет: не використані (status != 'redeemed')
                                    for coupon in batch_coupons:
                                        if hasattr(coupon, 'status') and coupon.status != 'redeemed':
                                            selected_coupon = coupon
                                            break
                                    
                                    # Якщо всі використані - беремо перший
                                    if not selected_coupon and batch_coupons:
                                        selected_coupon = batch_coupons[0]
                                    
                                    if selected_coupon:
                                        coupon_dict = {
                                            'id': str(selected_coupon.id) if hasattr(selected_coupon, 'id') and selected_coupon.id else None,
                                            'code': selected_coupon.code if hasattr(selected_coupon, 'code') else None,
                                            'title': selected_coupon.title if hasattr(selected_coupon, 'title') else saved_coupon.get('title', ''),
                                            'description': selected_coupon.description if hasattr(selected_coupon, 'description') else saved_coupon.get('description', ''),
                                            'type': selected_coupon.type if hasattr(selected_coupon, 'type') else saved_coupon.get('type'),
                                            'image': selected_coupon.image if hasattr(selected_coupon, 'image') else saved_coupon.get('image'),
                                            'share_allowed': int(selected_coupon.share_allowed) if hasattr(selected_coupon, 'share_allowed') and selected_coupon.share_allowed else saved_coupon.get('share_allowed', 0),
                                            'status': selected_coupon.status if hasattr(selected_coupon, 'status') else None,
                                            'batch': {
                                                'id': selected_coupon.batch.id if hasattr(selected_coupon.batch, 'id') else None,
                                                'type': selected_coupon.batch.type if hasattr(selected_coupon.batch, 'type') else None,
                                                'public_title': selected_coupon.batch.public_title if hasattr(selected_coupon.batch, 'public_title') else None,
                                                'public_description': selected_coupon.batch.public_description if hasattr(selected_coupon.batch, 'public_description') else None,
                                                'share_allowed': int(selected_coupon.batch.share_allowed) if hasattr(selected_coupon.batch, 'share_allowed') and selected_coupon.batch.share_allowed else 0,
                                            } if hasattr(selected_coupon, 'batch') and selected_coupon.batch else saved_coupon.get('batch'),
                                        }
                                        updated_coupons_data.append(coupon_dict)
                                        logging.info(f"Found actual coupon {coupon_dict['id']} for batch {batch_id}")
                                        
                                        # Видаляємо з пулу щоб не використати повторно
                                        batch_coupons.remove(selected_coupon)
                                    else:
                                        # Якщо не знайшли - залишаємо збережені дані
                                        updated_coupons_data.append(saved_coupon)
                                else:
                                    # Якщо немає batch_id - залишаємо як є
                                    updated_coupons_data.append(saved_coupon)
                            
                            if updated_coupons_data:
                                # Оновлюємо збережені дані
                                await invoice.update(loyalty_coupons_data=updated_coupons_data)
                                invoice.loyalty_coupons_data = updated_coupons_data
                                logging.info(f"Updated loyalty_coupons_data for invoice {invoice.id} with actual coupon IDs from user coupons")
                    
                    # Додатково спробуємо отримати актуальну транзакцію
                    actual_transaction = None
                    try:
                        async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
                            actual_transaction = await api.transaction(str(invoice.incust_transaction_id))
                    except Exception as trans_ex:
                        logging.warning(f"Failed to get transaction {invoice.incust_transaction_id}: {trans_ex}")
                    
                    if actual_transaction and hasattr(actual_transaction, 'check') and actual_transaction.check:
                        actual_check = actual_transaction.check
                        if hasattr(actual_check, 'emitted_coupons') and actual_check.emitted_coupons:
                            # Оновлюємо збережені дані актуальними з API
                            updated_coupons_data = []
                            for emitted_coupon in actual_check.emitted_coupons:
                                updated_coupons_data.append(emitted_coupon.dict())
                        
                            # Оновлюємо збережені дані в invoice якщо вони відрізняються
                            if updated_coupons_data != invoice.loyalty_coupons_data:
                                await invoice.update(loyalty_coupons_data=updated_coupons_data)
                                invoice.loyalty_coupons_data = updated_coupons_data
                                logging.info(f"Updated loyalty_coupons_data for invoice {invoice.id} with actual coupon IDs")
                            
                except Exception as api_ex:
                    logging.warning(f"Failed to get actual coupons data from API for invoice {invoice.id}: {api_ex}")
                    # Продовжуємо зі збереженими даними
            
            for coupon_dict in invoice.loyalty_coupons_data:
                # Створюємо CouponShowData з усіх даних зі збереженого dict
                coupon_data = CouponShowData(**coupon_dict)
                
                # PDF буде формуватися безпосередньо в методах відправки (email)
                
                result_coupons.append(coupon_data)
            
            return result_coupons
        
        return []
        
    except Exception as ex:
        logging.error(f"Error getting coupons data from invoice: {ex}", exc_info=True)
        return []


async def make_incust_check_info_data_from_invoice(
    invoice: Invoice,
    group: Group,
    lang: str,
    currency: str,
    user: User,
) -> IncustCheckInfoData | None:
    """Створює дані інформації про перевірку зі збережених даних invoice."""
    try:
        # Перевіряємо чи є дані лояльності
        if not (invoice.bonuses_added_amount or invoice.loyalty_discount_amount or invoice.loyalty_coupons_data):
            return None
        
        # Отримуємо купони зі збережених даних invoice
        coupons = await get_coupons_data_from_invoice(invoice, lang, True, user)
        
        # Створюємо тексти
        bonuses_added_text = None
        if invoice.bonuses_added_amount and invoice.bonuses_added_amount > 0:
            bonuses_added_text = format_currency(
                invoice.bonuses_added_amount / 100, currency=currency,
                locale=group.lang, territory=group.country_code
            )
            added_bonuses_text = await f("service bot loyalty bonuses header", lang)
        else:
            added_bonuses_text = None
        
        # Тексти для купонів
        added_coupons_text = None
        added_coupons_count = None
        if coupons:
            added_coupons_count = len(coupons)
            added_coupons_text = await f(
                "service bot loyalty coupons header", lang, count=added_coupons_count
            )
        
        by_check_issued = await f("by check issued", lang) if coupons else None
        
        # Формуємо pre_check_text
        pre_check_text = ""
        if bonuses_added_text:
            pre_check_text += "\n" + added_bonuses_text + ": " + bonuses_added_text
        if added_coupons_text and added_coupons_count:
            pre_check_text += "\n" + added_coupons_text + ": " + str(added_coupons_count)

        if pre_check_text:
            pre_check_text = pre_check_text.replace("\n", "", 1)
        
        # Обробляємо спеціальні рахунки зі збережених даних
        specials = None
        special_text = None
        if invoice.special_accounts_charges:
            specials = []
            special_parts = []
            
            for account_dict in invoice.special_accounts_charges:
                account_name = account_dict.get('title')
                amount = account_dict.get('amount', 0)
                
                special_data = {
                    'id': account_dict.get('id'),
                    'name': account_name,
                    'amount': amount
                }
                specials.append(special_data)
                
                amount_formatted = format_currency(
                    amount, currency, locale=group.lang, territory=group.country_code
                )
                
                if account_name:
                    # Якщо є назва рахунка - показуємо
                    special_parts.append(f"{account_name}: {amount_formatted}")
                else:
                    # Якщо немає назви - використовуємо загальну фразу
                    special_text_part = await f(
                        "special account topped text", lang,
                        sp_account_name="",  # Порожнє ім'я
                        amount=amount_formatted
                    )
                    special_parts.append(special_text_part)
            
            # Формуємо special_text
            special_text = "\n" + "\n".join(special_parts) if special_parts else None
        
        # Формуємо loyalty_awards_text
        if pre_check_text:
            award_info_message = await f(
                "service bot loyalty awards info after message",
                lang, awards_info=pre_check_text,
            )
            loyalty_awards_text = "\n\n" + award_info_message
            
            # Додаємо спеціальні рахунки якщо є
            if special_text:
                loyalty_awards_text += special_text
        else:
            loyalty_awards_text = ""
            award_info_message = None
        
        return IncustCheckInfoData(
            discount=invoice.loyalty_discount_amount / 100 if invoice.loyalty_discount_amount else None,
            sum_amount=invoice.loyalty_amount / 100 if invoice.loyalty_amount else invoice.total_sum / 100,
            sum_to_pay=invoice.loyalty_amount_to_pay / 100 if invoice.loyalty_amount_to_pay else invoice.total_sum / 100,
            bonuses_added=invoice.bonuses_added_amount / 100 if invoice.bonuses_added_amount else None,
            bonuses_added_text=bonuses_added_text,
            added_bonuses_text=added_bonuses_text,
            coupons=coupons,
            added_coupons_text=added_coupons_text,
            added_coupons_count=added_coupons_count,
            by_check_issued=by_check_issued,
            award_info_message=award_info_message,
            specials=specials,
            special_text=special_text,
            pre_check_text=pre_check_text,
            loyalty_awards_text=loyalty_awards_text,
        )
        
    except Exception as ex:
        logging.error(f"Error making incust check info data from invoice: {ex}", exc_info=True)
        return None


async def __get_invoice_text(
        invoice: Invoice,
        user: User,
        group: Group,
        lang: str,
        is_full_bonuses_payment: bool = False,
):
    invoice_payment = await StoreOrderPayment.get(invoice_id=invoice.id)

    menu_in_store_id = invoice.menu_in_store_id
    if menu_in_store_id:
        menu_in_store = await MenuInStore.get(menu_in_store_id)
        menu_in_store_comment = menu_in_store.comment
    else:
        menu_in_store_comment = ""

    total_sum_txt = await f(
        "manager history invoice total sum text", lang,
        total_sum=format_currency(
            (invoice.total_sum / 100), invoice.currency, locale=group.lang, territory=group.country_code
        ),
    )
    paid_sum = format_currency(
        (invoice.paid_sum / 100), invoice.currency, locale=group.lang, territory=group.country_code
    )
    loyalty_text = ""
    if invoice.bonuses_added_amount or invoice.loyalty_discount_amount:
        loyalty_text = await make_loyalty_check_info_message(
            invoice, lang, group, invoice.currency, True, True,
        )

    time_payed = None
    if invoice.status == 'payed':
        status_text_variable = "manager history payed invoice text"
        if invoice_payment:
            time_payed = (f''
            f'{localise_datetime(invoice_payment.confirmed_datetime, group.timezone, "utc"):%d.%m.%Y %H:%M:%S}')
    else:
        time_payed = ""
        status_text_variable = "manager history payment failed invoice text"

    time_created = localise_datetime(invoice.time_created, group.timezone, "utc")
    if time_payed is None:
        time_payed = time_created

    profile_name = group.name

    if invoice.user_comment:
        if invoice.invoice_template_id:
            invoice_template = await InvoiceTemplate.get(invoice.invoice_template_id)
        else:
            invoice_template = None
        user_comment_label = await get_invoice_comment_label(
            group.lang, invoice_template, group
        )
        user_comment_line = f"{user_comment_label}: {invoice.user_comment}\n"
    else:
        user_comment_line = ""

    text = await f(
        status_text_variable,
        lang,
        profile_name=profile_name,
        menu_in_store_comment=menu_in_store_comment,
        order_id=None,
        order_created_datetime=None,
        order_status=None,
        order_status_pay=None,
        fullname=user.full_name or user.first_name,
        invoice_id=invoice.id,
        time_created=f'{time_created:%d.%m.%Y %H:%M:%S}',
        time_payed=time_payed,
        total_sum_txt=total_sum_txt,
        amount=paid_sum,
        extra_fee_txt=await get_extra_fee_txt(group, invoice, invoice.currency, lang),
        user_comment_line=user_comment_line,
        payer_fee_txt=('\n\n' + await f(
            'check payment costs text', lang
        ) + ' ' + format_currency(
            (invoice.payer_fee / 100), invoice.currency, group.lang, territory=group.country_code
        )) if invoice.payer_fee else "\n",
    )
    if loyalty_text:
        text += f"\n\n{loyalty_text}"
    if invoice.check_url:
        text += '\n\n' + await f(
            'invoice check url', lang, check_url=invoice.check_url
        )
    if is_full_bonuses_payment:
        text += "\n" + await f(
            "service bot loyalty invoice paid by bonuses", lang,
            invoice_id=invoice.id
        )

    return text


async def add_invoice_service_bot_notifications(
        invoice: Invoice,
        group: Group,
        user: User | None = None,
        is_full_bonuses_payment: bool = False,
):
    async def get_sending_data(manager_user: User):
        manager_lang = manager_user.lang

        text = await __get_invoice_text(
            invoice, user, group, manager_lang,
            is_full_bonuses_payment=is_full_bonuses_payment,
        )

        return {
            "content_type": "text",
            "text": text,
        }

    try:
        await add_telegram_notifications_for_action(
            "service",
            SERVICE_BOT_USERNAME,
            SERVICE_BOT_API_TOKEN,
            action="crm_invoice:read",
            available_data={
                "profile_id": group.id,
                "invoice_id": invoice.id,
            },
            message=get_sending_data,
        )
    except Exception as e:
        logging.getLogger("error.invoice.add_invoice_service_bot_notifications").error(
            e, exc_info=True
        )
        await send_message_to_platform_admins(
            f"An error occurred while sending invoice notifications to service bot: {str(e)}\n"
            f"Invoice: #{invoice.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Invoice user: {user.name if user else None}({invoice.user_id})\n"
        )


async def send_payment_notifications(
        group: Group,
        brand: Brand,
        invoice: Invoice,
        invoice_user: User | None = None,
        is_full_bonuses_payment: bool = False,
        terminal_key: str | None = None,
        lang: str | None = None,
):
    user = invoice_user if invoice_user else await User.get_by_id(invoice.user_id)
    payer = await User.get_by_id(
        invoice.payer_id
    ) if invoice.payer_id and invoice.payer_id != invoice.user_id else user
    sender_bot_id = invoice.payment_bot_menu_id or invoice.payment_bot_id or invoice.bot_id
    client_bot = await ClientBot.get(sender_bot_id) if sender_bot_id else None
    menu_in_store = await MenuInStore.get(
        invoice.menu_in_store_id
    ) if invoice.menu_in_store_id else None

    try:
        await __notify_user_about_invoice_payment(
            group, brand, invoice, user, payer, client_bot, menu_in_store, lang
        )
    except Exception as err:
        logging.error(f"send pmt message to user FAILED\n{str(err)}", exc_info=True)
        await send_message_to_platform_admins(
            f"send pmt message to user FAILED\n"
            f"{str(err)}\n\n"
            f"{invoice.id = }\n"
            f"{user.id = }\n"
            f"{payer.id = }\n"
            f"{invoice.email = }\n"
        )

    debugger.debug('Try to send manager pmt Ok messages')

    try:
        # send a message to manager
        await add_invoice_service_bot_notifications(
            invoice,
            group,
            user,
            is_full_bonuses_payment=is_full_bonuses_payment,
        )
    except Exception as err:
        logging.error(f'send pmt message to manager FAILED\n{str(err)}', exc_info=True)
        await send_message_to_platform_admins(
            f"send pmt message to SERVICE BOT FAILED\n"
            f"{str(err)}\n\n"
            f"{invoice.id = }\n"
            f"{user.id = }\n"
            f"{payer.id = }\n"
            f"{invoice.email = }\n"
        )

    await send_invoice_push_notifications(invoice, group, invoice_user)

    # Перевіряємо чи є збережені дані лояльності
    # if invoice.incust_transaction_id and (invoice.loyalty_coupons_data or invoice.special_accounts_charges):
        # Використовуємо збережені дані напряму з invoice
        # lang = await user.get_lang()
        
        # ЗАКОМЕНТОВАНО: Відправляємо купони зі збережених даних invoice
        # if invoice.loyalty_coupons_data and client_bot:
        #     # Отримуємо store_id для правильного створення клавіатури купона
        #     store_id = None
        #     if menu_in_store and menu_in_store.store_id:
        #         store_id = menu_in_store.store_id
        #     
        #     await send_coupons_to_bot_from_invoice_data(
        #         invoice.loyalty_coupons_data, user, lang, brand.id, client_bot, store_id
        #     )


async def __notify_user_about_invoice_payment(
        group: Group,
        brand: Brand,
        invoice: Invoice,
        user: User,
        payer: User,
        client_bot: ClientBot | None,
        menu_in_store: MenuInStore | None = None,
        lang: str | None = None,
):
    debugger.debug(f"DEBUG __notify_user_about_invoice_payment called for invoice {invoice.id}")
    if invoice.status != "payed":
        raise ValueError("Invoice must be payed for this function")

    lang = lang if lang else await user.get_lang(client_bot)

    if not menu_in_store and invoice.menu_in_store_id:
        menu_in_store = await MenuInStore.get(invoice.menu_in_store_id)

    message_data = await __get_invoice_message_data(
        invoice, group, user, lang, menu_in_store
    )

    loyalty_data = await __get_loyalty_data(invoice, group, user, lang)
    debugger.debug(f"DEBUG loyalty_data result: info_data={bool(loyalty_data.info_data)}, check_info_data={bool(loyalty_data.check_info_data)}")
    if loyalty_data.info_data:
        message_data.loyalty_info_data = loyalty_data.info_data
        debugger.debug(f"DEBUG loyalty_info_data.invoice_payed_text: '{loyalty_data.info_data.invoice_payed_text}'")
    if loyalty_data.check_info_data:
        message_data.loyalty_check_info_data = loyalty_data.check_info_data

    debugger.debug(f"DEBUG client_bot exists: {bool(client_bot)}, client_bot: {client_bot}")
    if client_bot:
        if payer.chat_id and client_bot.bot_type == "telegram":
            await __delete_invoice_message(
                payer.chat_id,
                client_bot.token,
                invoice.message_id,
            )

        await __notify_user_in_bot(
            user,
            client_bot, invoice,
            message_data,
            brand.id,
            loyalty_data,
            lang,
        )

        debugger.debug('__notify_user_in_bot Ok')

        if invoice.is_friend and user.id != payer.id:
            await __notify_user_in_bot(
                payer,
                client_bot, invoice,
                message_data,
                brand.id,
                loyalty_data,
                lang,
                True,
            )
            debugger.debug('__notify_payer_in_bot Ok')

    user_chat_id = user.chat_id if user else None
    client_bot_token = client_bot.token if client_bot else None
    await __notify_user_by_email(
        invoice,
        group,
        message_data,
        loyalty_data,
        lang,
        user_chat_id,
        client_bot_token,
    )
    debugger.debug('__notify_user_by_email Ok')

    if invoice.status == 'payed' and invoice.is_friend and user.id != payer.id and payer.email:
        await __notify_user_by_email(
            invoice,
            group,
            message_data,
            loyalty_data,
            lang,
            payer.chat_id,
            client_bot_token,
            True,
        )
        debugger.debug('__notify_payer_by_email Ok')


async def __get_invoice_message_data(
        invoice: Invoice,
        group: Group,
        user: User,
        lang: str,
        menu_in_store: MenuInStore | None = None
):
    title = group.name
    if menu_in_store:
        title += f" [{menu_in_store.comment}]"

    if invoice.status == "payed":
        status_text_variable = "invoice payed message to user"
    else:
        status_text_variable = "invoice payment failed message to user"

    type_payed_message = await f(
        'invoice type payed message', lang, invoice_id=invoice.id
    )

    if invoice.check_url:
        url_in_check_text = await f(
            "invoice check url text", lang, check_url=invoice.check_url
        )
    else:
        url_in_check_text = ""

    localised_created_date = localise_datetime(
        invoice.time_created, user.get_timezone(), "utc"
    )
    time_created = localised_created_date.strftime(DATETIME_FORMAT_SECONDS)

    if invoice.status == "payed":
        invoice_payment = await StoreOrderPayment.get(invoice_id=invoice.id)
        if invoice_payment:
            localised_confirmed_date = localise_datetime(
                invoice_payment.confirmed_datetime, user.get_timezone(), "utc"
            )
        else:
            localised_confirmed_date = localised_created_date
        time_payed = localised_confirmed_date.strftime(DATETIME_FORMAT_SECONDS)
    else:
        time_payed = ""

    if invoice.user_comment:
        if invoice.invoice_template_id:
            invoice_template = await InvoiceTemplate.get(invoice.invoice_template_id)
        else:
            invoice_template = None
        user_comment_label = await get_invoice_comment_label(
            lang, invoice_template, group
        )
        user_comment_line = f"{user_comment_label}: {invoice.user_comment}\n"
    else:
        user_comment_line = ""

    debugger.debug(f"making InvoiceMessageData. {time_created = }, {time_payed = }")

    extra_fees = await get_formated_extra_fees(invoice, invoice.currency, lang, group.country_code)
    extra_fee_str = await get_extra_fee_str(extra_fees)
    extra_fee_txt = await get_extra_fee_txt(group, invoice, invoice.currency, lang)
    ewallet_topup_info = await get_ewallet_topup_info(group, invoice, user, lang)

    return InvoiceMessageData(
        title=title,
        amount=format_currency((invoice.total_sum / 100), invoice.currency, group.lang),
        paid_sum=('\n<b>' + await f(
            "web store sum label text", lang
        ) + "</b> " + format_currency(
            invoice.converted_sums["paid_sum"], invoice.currency, group.lang
        )) if invoice.paid_sum else "",
        payer_fee=('\n' + await f(
            'check payment costs text', lang
        ) + ' ' + format_currency(
            invoice.converted_sums["payer_fee"], invoice.currency, group.lang
        ))
        if invoice.payer_fee else "",
        status_text_variable=status_text_variable,
        invoice_payment_mode=invoice.payment_mode,
        invoice_image_url=invoice.photo_url,
        type_payed_message=type_payed_message,
        url_in_check_text=url_in_check_text,
        time_created=time_created,
        time_payed=time_payed,
        user_comment_line=user_comment_line,
        extra_fee_str=extra_fee_str,
        extra_fees=extra_fees,
        extra_fee_txt=extra_fee_txt,
        ewallet_topup_info=ewallet_topup_info,
    )


async def get_ewallet_topup_info(group: Group, invoice: Invoice, user: User, lang: str):
    ewallet_topup_info = ""
    if invoice.ewallet_id:
        try:
            ewallet = await EWallet.get(invoice.ewallet_id, is_deleted=False, is_enabled=True)
            if ewallet:
                ewallet_response = await process_single_ewallet(ewallet, user, lang)
                if invoice.invoice_type == InvoiceTypeEnum.TOPUP_ACCOUNT:
                    ewallet_topup_info = (await f(
                        "special account topped text",
                        lang,
                        sp_account_name=ewallet.name,
                        amount=format_currency(
                            (invoice.before_loyalty_sum / 100), invoice.currency, group.lang,
                            group.country_code
                        ),
                    ) + "\n\n")
                ewallet_topup_info += ewallet_response.message
        except Exception as ex:
            logging.error(f"Error processing ewallet for notification {invoice.id}: {ex}", exc_info=True)
    return ewallet_topup_info


async def __get_loyalty_data(
        invoice: Invoice,
        group: Group,
        user: User,
        lang: str,
):
    debugger.debug(f"DEBUG __get_loyalty_data called for invoice {invoice.id}, loyalty_discount_amount: {invoice.loyalty_discount_amount}")
    # Використовуємо збережені дані з invoice
    if not (invoice.bonuses_added_amount or invoice.loyalty_discount_amount or invoice.loyalty_coupons_data or (invoice.bonuses_redeemed and invoice.bonuses_redeemed > 0)):
        debugger.debug(f"DEBUG __get_loyalty_data early return - no loyalty data for invoice {invoice.id}")
        return LoyaltyData()

    # Створюємо info_data зі збережених даних
    info_data = None
    if invoice.bonuses_added_amount or invoice.loyalty_discount_amount or (invoice.bonuses_redeemed and invoice.bonuses_redeemed > 0):
        total_amount = invoice.loyalty_amount / 100 if invoice.loyalty_amount else invoice.total_sum / 100
        sum_to_pay = invoice.loyalty_amount_to_pay / 100 if invoice.loyalty_amount_to_pay else invoice.total_sum / 100
        
        # Форматуємо основні суми
        sum_amount_text = format_currency(
            total_amount, invoice.currency, locale=user.lang or 'en'
        )
        sum_to_pay_text = format_currency(
            sum_to_pay, invoice.currency, locale=user.lang or 'en'
        )
        
        # Початок формування loyalty_info_text
        invoice_payed_loyalty_message_to_user = await f(
            "invoice payed loyalty message check amount to user", 
            lang, check_amount=sum_amount_text
        )
        loyalty_info_text = "\n" + invoice_payed_loyalty_message_to_user
        discount_percent = 0
        # Додаємо інформацію про знижки
        if invoice.loyalty_discount_amount:
            discount_formatted = format_currency(
                invoice.loyalty_discount_amount / 100,
                invoice.currency, locale=user.lang or 'en'
            )
            discount_percent = (invoice.loyalty_discount_amount / 100) / total_amount * 100 if total_amount else 0
            
            loyalty_discount_header = await f("loyalty discount header", lang)
            discount_text = f"{loyalty_discount_header}: {discount_formatted} ({discount_percent:.1f}%)"
            
            discount_invoice_payed_text = await f(
                "invoice payed loyalty message discount amount to user", lang,
                discount_amount=discount_formatted,
                discount_percent=f"{discount_percent:.1f}"
            )
            loyalty_info_text += "\n" + discount_invoice_payed_text
        else:
            discount_text = ""
            loyalty_discount_header = ""
            discount_invoice_payed_text = None
        
        # Додаємо інформацію про використані бонуси
        if invoice.bonuses_redeemed and invoice.bonuses_redeemed > 0:
            bonuses_redeemed_formatted = format_currency(
                invoice.bonuses_redeemed / 100,
                invoice.currency, locale=user.lang or 'en'
            )
            bonuses_percent = (invoice.bonuses_redeemed / 100) / total_amount * 100 if total_amount else 0
            
            bonuses_invoice_payed_text = await f(
                "invoice payed loyalty message bonuses amount to user", lang,
                bonuses_amount=bonuses_redeemed_formatted,
                bonuses_percent=f"{bonuses_percent:.1f}"
            )
            loyalty_info_text += "\n" + bonuses_invoice_payed_text
        else:
            bonuses_invoice_payed_text = None
        
        # Додаємо фінальну суму до оплати
        if sum_amount_text != sum_to_pay_text:
            loyalty_info_text += "\n" + await f(
                "invoice payed loyalty message total amount to user", lang,
                total_amount=sum_to_pay_text,
            ) + "\n"
        
        # Формуємо bonuses_sum_text для нарахованих бонусів
        bonuses_sum_text = await f(
            "service bot loyalty bonuses header", lang,
            bonuses=format_currency(
                invoice.bonuses_added_amount / 100 if invoice.bonuses_added_amount else 0,
                "bonus", locale=user.lang or 'en'
            )
        ) if invoice.bonuses_added_amount else ""
        
        # Формуємо invoice_payed_text - тільки знижки та використані бонуси (без нарахованих)
        invoice_payed_parts = []
        if discount_invoice_payed_text:
            invoice_payed_parts.append(discount_invoice_payed_text)
        if bonuses_invoice_payed_text:
            invoice_payed_parts.append(bonuses_invoice_payed_text)
        
        invoice_payed_text = "\n".join(invoice_payed_parts) if invoice_payed_parts else ""
        
        payed_order_text = await f("payed order text", lang)
        
        info_data = IncustInfoData(
            percent=discount_percent if invoice.loyalty_discount_amount else 0,
            discount_text=discount_text,
            bonuses_sum_text=bonuses_sum_text,
            loyalty_discount_header=loyalty_discount_header,
            loyalty_info_text=loyalty_info_text,
            sum_amount_text=sum_amount_text,
            sum_to_pay_text=sum_to_pay_text,
            payed_order_text=payed_order_text,
            invoice_payed_text=invoice_payed_text,
            invoice_payed_loyalty_message_to_user=invoice_payed_loyalty_message_to_user,
        )

    # Створюємо check_info_data зі збережених даних
    check_info_data = await make_incust_check_info_data_from_invoice(
        invoice, group, lang, invoice.currency, user
    )
    
    return LoyaltyData(
        info_data,
        check_info_data,
    )


async def __delete_invoice_message(
        user_chat_id: int,
        client_bot_token: str,
        invoice_message_id: int | None,
):
    if not invoice_message_id:
        return

    try:
        debugger.debug(f'Try to delete invoice message... {invoice_message_id = }')

        bot = Bot.get_current()
        with bot.with_token(client_bot_token):
            await bot.delete_message(user_chat_id, invoice_message_id)

    except Exception as err:
        logging.error(f'delete invoice message FAILED\n{str(err)}', exc_info=True)

    else:
        debugger.debug('Invoice message deleted Ok')


@dataclass
class LoyaltyData:
    info_data: IncustInfoData | None = None
    check_info_data: IncustCheckInfoData | None = None


async def __notify_user_in_bot(
        user: User,
        client_bot: ClientBot,
        invoice: Invoice,
        message_data: InvoiceMessageData,
        brand_id: int,
        loyalty_data: LoyaltyData,
        lang: str,
        is_payer: bool | None = None,
):
    user_to = ma.get_user_to(user, client_bot.bot_type)
    if not user_to:
        return debugger.debug(
            f"message not sent to user. User {user} has no {client_bot.bot_type} connected"
        )

    if client_bot.bot_type == "telegram":
        user_bot_activity = await UserClientBotActivity.get(
            user, client_bot, create=False,
        )
        if not user_bot_activity or not user_bot_activity.is_entered_bot:
            return debugger.debug(
                f"message not sent to user. User {user} have not entered the telegram yet"
            )

    debugger.debug(
        f'Try to send payed invoice message to user... {user.chat_id = }, {user.wa_phone = }'
    )

    try:

        text = await f(
            message_data.status_text_variable, lang,
            **message_data.dict(),
            loyalty_info=loyalty_data.info_data.loyalty_info_text if loyalty_data and loyalty_data.info_data else "",
        )
        if loyalty_data and loyalty_data.check_info_data and loyalty_data.check_info_data.loyalty_awards_text:
            debugger.debug(f"DEBUG loyalty_awards_text: '{loyalty_data.check_info_data.loyalty_awards_text}'")
            text += "\n" + loyalty_data.check_info_data.loyalty_awards_text

        if invoice.is_friend:
            if is_payer:
                friend = await User.get_by_id(invoice.user_id)
                text += "\n\n" + await f(
                    "invoice payed message for friend", lang, friend_name=friend.name
                )
            else:
                friend = await User.get_by_id(invoice.payer_id)
                text += "\n\n" + await f(
                    "invoice payed message by friend", lang, friend_name=friend.name
                )

        text = replace_html_symbols(text)

        kwargs = {}
        if client_bot.bot_type == "telegram":
            func = send_tg_message
        else:
            func = send_wa_message
            kwargs["wa_from"] = client_bot.whatsapp_from
            text = html_to_markdown(text)
        try:
            if invoice.photo_url:
                try:
                    await func(
                        user_to,
                        "photo",
                        photo=invoice.photo_url,
                        bot_token=client_bot.token,
                        text=text,
                        **kwargs,
                    )
                except Exception as err:
                    logging.error("Sending pmt message with photo to user error.")
                    logging.error(err, exc_info=True)
                    await func(
                        user_to,
                        "text",
                        text=text,
                        bot_token=client_bot.token,
                        **kwargs,
                    )
        except Exception as e:
            logging.error("Sending message to user error")
            logging.error(e, exc_info=True)

            try:
                await func(
                    user_to,
                    "text",
                    bot_token=client_bot.token,
                    text=await f(
                        "sending payment message to user error",
                        lang, invoice_id=invoice.id,
                    ),
                )
            except Exception as e:
                logging.error(e, exc_info=True)
                is_user_notified_about_error = False
            else:
                is_user_notified_about_error = True

            client_bot_token = client_bot.token
            err_text = (
                "An error occurred while sending user successful payment MESSAGE\n"
                f"{is_user_notified_about_error = }\n"
                f"{invoice.id = }\n"
                f"{user_to = }\n"
                f"{client_bot_token = }\n"
                f"{brand_id = }\n"
                f"{lang = }\n"
            )

            debugger.debug(f"{err_text}\n{message_data = }\n{loyalty_data = }")

            await send_message_to_platform_admins(err_text)
        finally:
            if client_bot.bot_type == "whatsapp":
                to = user.wa_phone
                bot_token = client_bot.token
                whatsapp_from = client_bot.whatsapp_from
                wa_keyboard = await get_wa_menu_keyboard(user, client_bot, lang)

                asyncio.ensure_future(
                    send_delayed_wa_menu(
                        bot_token, lang, to, wa_keyboard, whatsapp_from, 4
                    )
                )

    except Exception as err:
        logging.error(f'send msg about pmt FAILED\n{str(err)}', exc_info=True)
    else:
        debugger.debug('send msg about pmt Ok')


async def __notify_user_by_email(
        invoice: Invoice,
        group: Group,
        message_data: InvoiceMessageData,
        loyalty_data: LoyaltyData,
        lang: str,
        user_chat_id: int | None,
        client_bot_token: str | None,
        is_payer: bool | None = None,
):
    if not is_payer and not invoice.email:
        return

    async def send():
        status_payment = await f("status payed text", lang) if invoice.status == 'payed' \
            else await f("status failed text", lang)

        friend_text = ""
        payer_email = ""
        if invoice.status == 'payed' and invoice.is_friend:
            if is_payer:
                friend = await User.get_by_id(invoice.user_id)
                friend_text = "\n\n" + await f(
                    "invoice payed message for friend", lang, friend_name=friend.name
                )
                payer_email = friend.email
            else:
                friend = await User.get_by_id(invoice.payer_id)
                friend_text = "\n\n" + await f(
                    "invoice payed message by friend", lang, friend_name=friend.name
                )

        template = InvoiceEmailTemplate(
            **message_data.dict(),
            for_header_text=await f("for header text", lang),
            on_amount_text=await f("on amount text", lang),
            sum_amount_header_text=await f(
                "service bot loyalty check sum header", lang
            ),
            invoice_created_text=await f("invoice created text", lang),
            invoice_payed_text=await f("invoice payed text", lang),
            header=await f("payed invoice", lang, invoice_number=invoice.id),
            invoice_id=invoice.id,
            status_payment=status_payment,
            friend_text=friend_text,
        )
        html = await templater.make_template(template, group.id)

        if (
                invoice.loyalty_coupons_data and
                loyalty_data and
                loyalty_data.check_info_data and
                loyalty_data.check_info_data.coupons and
                not payer_email
        ):
            # Формуємо PDF attachments з купонів через pdf_media_id
            attachments = []
            for coupon in loyalty_data.check_info_data.coupons:
                if coupon.pdf_media_id:
                    try:
                        from core.loyalty import coupon_service, PDFFormatType
                        
                        pdf_attachment = await coupon_service.load_pdf_from_media_object(
                            coupon.pdf_media_id,
                            format_type=PDFFormatType.MIME_ATTACHMENT,
                            filename_override=f"coupon_{coupon.code or coupon.id}.pdf"
                        )
                        
                        if pdf_attachment:
                            attachments.append(pdf_attachment)
                        else:
                            logger.warning(f"Could not load PDF for coupon {coupon.id or coupon.code}")
                    except Exception as e:
                        logger.error(f"Error loading PDF for coupon {coupon.id or coupon.code}: {e}")
        else:
            attachments = []

        gmail = GmailClient(cfg.LOC7_EMAIL_LOGIN, cfg.LOC7_EMAIL_PASSWORD)

        subject_type_payed_message = (
            template.type_payed_message
            .replace('<b>', '')
            .replace('</b>', '')
        )
        await send_with_attachments(
            gmail, destination=invoice.email if not payer_email else payer_email,
            subject=f"{subject_type_payed_message} {template.status_payment}",
            html=html, from_name=template.title, attachments=attachments
        )

    try:
        await send()
    except Exception as e:
        logging.error(e, exc_info=True)

        try:
            if user_chat_id and client_bot_token:
                await send_tg_message(
                    user_chat_id, "text", bot_token=client_bot_token, text=await f(
                        "sending success payment email error", lang,
                        email=invoice.email, invoice_id=invoice.id,
                    )
                )
                is_user_notified_about_error = True
            else:
                is_user_notified_about_error = False
        except Exception as e:
            logging.error(e, exc_info=True)
            is_user_notified_about_error = False

        debugger.debug(
            "An error occurred while sending user successful payment EMAIL\n"
            f"{is_user_notified_about_error = }\n"
            f"{invoice.id = }\n"
            f"{invoice.email = }\n"
            f"{message_data = }\n"
            f"{group = }\n"
            f"{loyalty_data = }\n"
            f"{lang = }"
        )

        await send_message_to_platform_admins(
            "An error occurred while sending user successful payment EMAIL\n"
            f"{is_user_notified_about_error = }\n"
            f"{invoice.id = }\n"
            f"{invoice.email = }\n"
            f"{group = }\n"
            f"{lang = }"
        )


async def send_coupons_to_bot_from_invoice_data(
        coupons_data: list,
        user: User, lang: str,
        brand_id: int,
        bot: ClientBot,
        store_id: int | None = None,
):
    """Відправляє купони використовуючи збережені дані з invoice.loyalty_coupons_data."""
    try:
        if not coupons_data:
            return
            
        from schemas.incust.base import CouponShowData
        from email.mime.base import MIMEBase
        
        for coupon_dict in coupons_data:
            # Створюємо CouponShowData з усіх даних зі збереженого dict
            coupon_data = CouponShowData(**coupon_dict)
            
            # PDF буде формуватися в send_coupon_info з pdf_media_id
            
            await send_coupon_info(
                coupon_data,
                user,
                bot,
                lang,
                brand_id,
                store_id=store_id,
            )
            
    except Exception as ex:
        logging.error(f"Error sending coupons from invoice data: {ex}", exc_info=True)


async def send_invoice_push_notifications(
        invoice: Invoice,
        group: Group | None = None,
        invoice_user: User | None = None,
        ignore_session_id: int | None = None,
):
    try:
        assert not group or group.id == invoice.group_id, "Group does not match invoice group"
        assert not invoice_user or invoice_user.id == invoice.user_id, "User does not match invoice user"

        if not group:
            group = await Group.get(invoice.group_id)
        if not invoice_user:
            invoice_user = await User.get_by_id(invoice.user_id)

        async def get_message(user: User):
            texts = await fd(
                {
                    "title": {
                        "variable": (
                            "crm invoice read notification title"
                            if invoice.is_read else
                            "crm invoice paid notification title"
                            if invoice.status == "payed" else
                            "crm invoice new notification title"
                        ),
                        "text_kwargs": {
                            "invoice_id": invoice.id,
                            "total_sum": format_currency(
                                round(invoice.total_sum / 100, 2),
                                invoice.currency, group.lang, group.country
                            )
                        }
                    },
                    "body": {
                        "variable": "crm invoice notification body",
                        "text_kwargs": {
                            "group_name": group.name,
                            "user_name": invoice_user.name,
                        }
                    },
                },
                user.lang,
            )

            title = texts["title"]
            body = texts["body"] + "\n"

            invoice_items = await crud.get_invoice_items(invoice.id)
            body = add_items_text(body, invoice_items, group.lang, group.currency)

            return build_fcm_message(
                "invoice",
                invoice.id,
                invoice.crm_tag,
                title,
                body,
                delete_notification=invoice.is_read,
                apns_priority="5" if invoice.is_read else "10",
                is_paid=invoice.status == "payed",
                add_data_texts=not invoice.is_read,
                link=f"{CRM_HOST}/invoice/{invoice.id}?listType=inbox&itemIdField=invoiceId"
            )

        return await add_push_notifications_for_action(
            AuthSourceEnum.CRM_WEB, AuthSourceEnum.CRM_APP,
            action="crm_invoice:read",
            available_data={
                "profile_id": invoice.group_id,
                "invoice_id": invoice.id,
            },
            message=get_message,
            ignore_session_id=ignore_session_id,
        )
    except Exception as e:
        group = await Group.get(invoice.group_id)
        invoice_user = await User.get_by_id(invoice.user_id)

        logging.error(
            f"send_invoice_push_notifications FAILED: ({str(e)}", exc_info=True
        )
        await send_message_to_platform_admins(
            f"An error occurred while sending invoice push notifications: {str(e)}\n"
            f"Invoice id: {invoice.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Invoice user: {invoice_user.name}({invoice_user.id})\n"
        )
