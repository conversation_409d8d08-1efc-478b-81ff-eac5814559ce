from core.deep_link import ShortDeepLink


class ExternalInvoiceDeepLink(ShortDeepLink, mode="extpi"):
    invoice_uuid_id: str
    payment_settings_id: int = None
    object_payment_settings_id: int = None


class StoreOrderDeepLink(ShortDeepLink, mode="so"):
    order_id: int
    payment_settings_id: int = None
    object_payment_settings_id: int = None


class InvoiceDeepLink(ShortDeepLink, mode="pi"):
    invoice_id: str
    payment_settings_id: int = None
    object_payment_settings_id: int = None
