import asyncio

from db import DBSession
from .sender import send_mailing_messages
from .sender.api import get_mailing_users
from utils.processes_manager.background_worker import LoopBackgroundWorker


class MailingWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "mailing"
    DEFAULT_TIMEOUT = 1 / 30

    async def iteration(self):
        try:

            with DBSession():
                mailing_users = await get_mailing_users()

            if not mailing_users:
                await asyncio.sleep(1)
                return

            await send_mailing_messages(mailing_users)

        except Exception as e:
            self.error_logger.error(e, exc_info=True)
