from aiogram import Dispatcher, types

from db.models import User, ClientBot, MailingArgs

from utils.message import send_tg_message
from utils.text import f

from .functions import send_mailing_message
from .keyboards import get_menu_keyboard


async def mailing_info_button_handler(
        message: types.Message,
        user: User, lang: str,
):
    bot_token = await ClientBot.get_current_bot_token()
    mailings = await MailingArgs.get_mailings_for_user(user.id, bot_token)

    if not mailings:
        bot_token = await ClientBot.get_current_bot_token()
        keyboard = await get_menu_keyboard(user, bot_token, lang)
        return await message.answer(await f("no started mailings text", lang), reply_markup=keyboard)

    for mailing in mailings:
        msg = await send_mailing_message(
            bot_token=bot_token,
            user_chat_id=user.chat_id,
            user_first_name=user.first_name,
            user_full_name=user.name,
            user_username=user.username,
            text=mailing.text,
            content_type=mailing.content_type,
            file_path=mailing.file_path,
        )

        additional_texts = ""

        error_count = mailing.error
        if error_count:
            additional_texts += await f("mailing error count", lang, error_count=error_count)

        unknown_count = mailing.unknown
        if unknown_count:
            additional_texts += "\n" + await f("mailing unknown count", lang, unknown_count=unknown_count)

        text = await f(
            "mailing info text", lang,
            start_count=mailing.start_count,
            sent_count=mailing.sent,
            turned_off_count=mailing.turned_off,
            blocked_count=mailing.blocked,
            additional_texts=additional_texts,
            bots=mailing.bots, groups=mailing.groups,
        )

        await send_tg_message(message.from_user.id, "text", bot_token=mailing.created_from_bot_token,
                              reply_to_message_id=msg.message_id, text=text)


def register_mailing_info_button_handler(dp: Dispatcher):
    dp.register_message_handler(
        mailing_info_button_handler,
        lequal="mailing info button",
        state="*",
    )
