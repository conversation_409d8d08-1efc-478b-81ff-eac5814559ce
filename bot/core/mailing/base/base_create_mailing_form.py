from typing import Type

from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from psutils.forms.helpers import with_delete_state_messages
from psutils.fsm import pop_keys_from_state
from utils.media import delete_file
from ...media import download_file

from .base_state import BaseCreate<PERSON>ailingState
from psutils.forms import WizardForm, fields
from utils.router import Router
from utils.text import f


async def message_data_processor(message: types.Message, state: FSMContext):
    state_data = await state.get_data()

    old_text = state_data.get("text")
    file_path = state_data.get("file_path")
    content_type = state_data.get("content_type")

    new_text = message.text or message.caption

    text = f"{old_text}\n{new_text}" if old_text and new_text else new_text or old_text

    if message.content_type != "text":
        delete_file(file_path)
        content_type = message.content_type
        file_path = await download_file(message)
    else:
        content_type = content_type or message.content_type

    return {"text": text, "content_type": content_type, "file_path": file_path}


def create_form(mailing_state_group: Type[BaseCreateMailingState]) -> Type[WizardForm]:

    class CreateMailingForm(WizardForm):
        state_group = mailing_state_group
        need_setup_previous_button_handler = False

        choose_group = fields.InlineButtonsField(
            callback_mode="group",
            callback_keys=("id", "sender_group_id"),
            need_setup_not_buttons_click_handler=False,
        )
        create_message = fields.MessageField(
            "*",
            error_text_variable="error",
            with_caption=True, caption_as_text=True,
            data_processor=message_data_processor,
            handler_state=mailing_state_group,
        )

        @classmethod
        async def next_field(cls, state: FSMContext, field_was_specified: bool):
            cur_state = await state.get_state()
            state_data = await state.get_data()
            if cur_state == cls.state_group.ChooseGroup.state and state_data.get("sender_group_id"):
                await cls.state_group.next()

        @classmethod
        @with_delete_state_messages
        async def reset_mailing_data_button_handler(
                cls,
                callback_query: types.CallbackQuery,
                state: FSMContext,
                lang: str,
        ):
            await pop_keys_from_state(state, "text", "content_type", "file_path")
            await callback_query.answer(await f("mailing message reset", lang), show_alert=True)
            await Router.state_menu(callback_query, state, lang)

        @classmethod
        async def run_mailing_button_handler(
                cls,
                callback_query: types.CallbackQuery,
                state: FSMContext,
                lang: str,
        ):
            await cls.state_group.RunMailing.set()
            await Router.state_menu(callback_query, state, lang)

        @classmethod
        def setup_handlers(cls, dp: Dispatcher):
            super().setup_handlers(dp)

            dp.register_callback_query_handler(
                cls.reset_mailing_data_button_handler,
                callback_mode="reset_mailing_data",
                state=cls.state_group.CreateMessage,
            )

            dp.register_callback_query_handler(
                cls.run_mailing_button_handler,
                callback_mode="run_mailing",
                state=cls.state_group.CreateMessage,
            )

    return CreateMailingForm
