from abc import ABC
from typing import List

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Group, User

from psutils.forms.helpers import save_messages_to_state
from utils.message import send_tg_message

from utils.text import f, c
from utils.router import Router, CancelRoute
from utils.router.route_handlers import BaseListDrawer
from utils.redefined_classes import InlineKb, InlineBtn

from .base_state import BaseCreateMailingState
from utils.keyboards import active_button


class BaseGroupsListDrawer(BaseListDrawer, ABC, methods=None):
    config_page_size_variable_name = "MAILING_GROUPS_LIST_PAGE_SIZE"

    need_setup_pagination_handler = True
    need_setup_search_handler = True

    message_text_variable = "mailing groups list header"
    search_message_text_variable = "mailing groups list search header"
    empty_text_variable = "mailing groups empty list header"
    search_empty_text_variable = "mailing groups empty list search header"

    pagination_callback_mode = "page"

    state_group: BaseCreateMailingState

    @classmethod
    async def handler(
            cls,
            message: types.Message,
            state: FSMContext,
            user: User,
            lang: str,
            mode: str = "new",
    ) -> types.Message:

        state_data = await state.get_data()

        text = state_data.get("text")
        content_type = state_data.get("content_type", "text")
        file_path = state_data.get("file_path")

        if text or file_path:
            content = {}
            if content_type != "text":
                content[content_type] = file_path

            msg = await send_tg_message(user.chat_id, content_type, all_messages_in_result=True, text=text, **content)
            await save_messages_to_state(state, msg)

            await message.delete()

            mode = "new"

        return await super().handler(message, state, user, lang, mode)

    @classmethod
    async def object_drawer(cls, group: Group, keyboard: InlineKb, lang: str, sender_group_id: int = None):
        button_text = await f("mailing group button", lang, group_name=group.name)
        if group.id == sender_group_id:
            button_text = await active_button(lang, button_text)

        callback_data = c("group", id=group.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    @classmethod
    async def process_objects_list(
            cls,
            groups: List[Group],
            message: types.Message,
            state: FSMContext,
            lang: str
    ) -> List[Group]:

        if len(groups) == 1:
            await state.update_data(sender_group_id=groups[0].id, is_groups_skipped=True)
            await cls.state_group.next()
            await Router.state_menu(message, state, lang, set_state_message=True)
            raise CancelRoute()

        return groups
