from db import sess
from db.models import UserGroupSettings, UserSettings


def get_mailing_mode_subquery(user_id: int, bot_id: int, group_id: int):
    query = sess().query(UserGroupSettings.mailing_mode)
    query = query.join(UserGroupSettings.user_settings)

    query = query.filter(UserSettings.user_id == user_id)
    query = query.filter(UserSettings.bot_id == bot_id)
    query = query.filter(UserGroupSettings.group_id == group_id)

    query = query.order_by(UserGroupSettings.id.desc())
    query = query.limit(1)

    return query.scalar_subquery().label("mailing_mode")
