import re

from aiogram import types

from utils.message import send_tg_message
from utils.redefined_classes import InlineKb


async def send_mailing_message(
        bot_token: str | None,
        user_chat_id: int,
        user_first_name: str,
        user_full_name: str,
        user_username: str,
        text: str, content_type: str,
        file_path: str = None,
        keyboard: InlineKb = None,
        all_messages_in_result: bool = False,
) -> types.Message:
    text = parse_mailing_text_variables(text, keyboard, user_first_name, user_full_name, user_username)
    content = {"content_type": "text"}
    if content_type != "text" and file_path:
        content["content_type"] = content_type
        content[content_type] = file_path

    result = await send_tg_message(user_chat_id, bot_token=bot_token, keyboard=keyboard,
                                   all_messages_in_result=all_messages_in_result, text=text, **content)

    return result


def parse_mailing_text_variables(
        text: str,
        keyboard: InlineKb,
        user_first_name: str,
        user_full_name: str,
        user_username: str
):
    if not isinstance(text, str):
        return text

    text_vars = {
        "user_full_name": user_full_name,
        "firstname": user_first_name,
        "fullname": user_full_name,
        "username": user_username,
    }
    if keyboard:
        for button_row in keyboard["inline_keyboard"]:
            for button in button_row:
                for k, v in text_vars.items():
                    k = "{" + k + "}"
                    v = v if v else ""
                    button["text"] = re.sub(k, v, button["text"], flags=re.IGNORECASE)
                k = "[" + button["text"] + "]"
                text = text.replace(k, "")

    return text.format(**text_vars)
