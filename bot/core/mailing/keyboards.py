from config import SERVICE_BOT_API_TOKEN
from db.models import ClientBot, User
from utils.keyboards import previous_button
from utils.redefined_classes import InlineBtn, InlineKb, MenuKb
from utils.text import f


async def get_confirm_mailing_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb()

    button_text = await f("reset mailing message button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="reset_mailing_data"))
    keyboard.insert(
        InlineBtn(await f("run mailing button", lang), callback_data="run_mailing")
    )

    keyboard.row(await previous_button(lang))
    return keyboard


async def get_menu_keyboard(user: User, bot_token: str, lang: str) -> MenuKb:
    is_service = bot_token == SERVICE_BOT_API_TOKEN

    if is_service:
        from service.main.keyboards import \
            get_menu_keyboard as get_service_bot_menu_keyboard
        return await get_service_bot_menu_keyboard()
    else:
        from friendly.main.keyboards import \
            get_menu_keyboard as get_friendly_bot_menu_keyboard
        bot = await ClientBot.get(token=bot_token)
        return await get_friendly_bot_menu_keyboard(user, lang, bot.id)
