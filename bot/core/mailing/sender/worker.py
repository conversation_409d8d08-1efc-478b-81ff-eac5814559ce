import asyncio
from typing import List

from .api import process_result, MailingUserData
from ..functions import send_mailing_message


async def send_mailing_messages(mailing_users: List[MailingUserData]):
    coros = list()

    for mailing_user in mailing_users:
        coro = send_mailing_message(
            mailing_user.bot_token,
            mailing_user.user_chat_id,
            mailing_user.user_first_name,
            mailing_user.user_full_name,
            mailing_user.user_username,
            mailing_user.text,
            mailing_user.content_type,
            mailing_user.file_path,
            mailing_user.keyboard,
        )
        coros.append(coro)

    results = await asyncio.gather(*coros, return_exceptions=True)

    await process_result(results, mailing_users)
