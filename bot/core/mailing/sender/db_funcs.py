from typing import List, Dict
from sqlalchemy import literal_column, Integer, String

from db import db_func, sess
from db.models import MailingUser, ClientBot, MailingArgs, User


@db_func
def get_mailing_users():
    mailing_users = []
    bots = (
        sess().query(ClientBot.id, ClientBot.token)
        .join(MailingUser, ClientBot.id == MailingUser.bot_id)
        .filter(MailingUser.status == 'pending', ClientBot.status == 'enabled')
        .distinct()
        .all()
    )
    for bot_id, bot_token in bots:
        query = sess().query(
            MailingUser.id,
            User.chat_id,
            User.first_name,
            User.full_name,
            User.username,
            literal_column(str(bot_id), type_=Integer).label("bot_id"),
            literal_column(f"'{bot_token}'", type_=String).label("bot_token"),
            MailingArgs.id,
            MailingArgs.start_count,
            MailingArgs.sent,
            MailingArgs.turned_off,
            MailingArgs.blocked,
            MailingArgs.not_entered,
            MailingArgs.error,
            MailingArgs.unknown,
            MailingArgs.sender_group_id,
            MailingArgs.text,
            MailingArgs.content_type,
            MailingArgs.file_path,
            MailingArgs.keyboard,
        )

        query = query.join(MailingUser.user)
        query = query.join(MailingUser.mailing_args)
        query = query.filter(MailingUser.status == "pending")
        query = query.filter(MailingUser.bot_id == bot_id)
        result = query.first()
        if len(result) > 0:
            mailing_users.append(result)

    return mailing_users


@db_func
def set_status(status: str, ids: List[int]):
    query = sess().query(MailingUser).filter(MailingUser.id.in_(ids))
    count = query.update({"status": status}, synchronize_session="fetch")
    sess().commit()
    return count


@db_func
def update_mailing_args_counts(mailing_args_id: int, new_counts: Dict[str, int]):
    sess().query(MailingArgs).filter_by(id=mailing_args_id).update(new_counts)
    sess().commit()
