import asyncio
import logging
from collections import defaultdict
from dataclasses import dataclass
from typing import List, Dict, Iterable

from aiogram import types
from aiogram.utils.exceptions import Unauthorized, CantInitiateConversation, ChatNotFound, InvalidPeerID

from db import own_session, DBSession
from db.models import MailingArgs, User

from utils.text import f
from utils.message import send_tg_message
from utils.redefined_classes import InlineKb

from .db_funcs import get_mailing_users as _get_mailing_users, set_status, update_mailing_args_counts

from ..keyboards import get_menu_keyboard

logger = logging.getLogger()


@dataclass
class MailingUserData:
    id: int
    user_chat_id: int
    user_first_name: str
    user_full_name: str
    user_username: str
    bot_id: int
    bot_token: str
    mailing_args_id: int
    mailing_args_start_count: int
    mailing_args_sent: int
    mailing_args_turned_off: int
    mailing_args_blocked: int
    mailing_args_not_entered: int
    mailing_args_error: int
    mailing_args_unknown: int
    sender_group_id: int
    text: str
    content_type: str
    file_path: str
    _keyboard: dict

    @property
    def keyboard(self) -> InlineKb:
        return InlineKb.to_object(self._keyboard) if self._keyboard else None


async def get_mailing_users() -> List[MailingUserData]:
    raw_data = await _get_mailing_users()
    return list(map(lambda data: MailingUserData(*data), raw_data)) if raw_data else []


async def process_result(results: Iterable, mailing_users: List[MailingUserData]):
    mailing_args_counts: dict[int, dict] = defaultdict(dict)

    mailing_args_start_counts = dict()

    for mailing_user in mailing_users:
        mailing_args_counts[mailing_user.mailing_args_id] = {
            "sent": mailing_user.mailing_args_sent,
            "turned_off": mailing_user.mailing_args_turned_off,
            "blocked": mailing_user.mailing_args_blocked,
            "not_entered": mailing_user.mailing_args_not_entered,
            "error": mailing_user.mailing_args_error,
            "unknown": mailing_user.mailing_args_unknown,
        }

        mailing_args_start_counts[mailing_user.mailing_args_id] = mailing_user.mailing_args_start_count

    results_statuses = defaultdict(list)

    for result_ind, result in enumerate(results):
        mailing_user = mailing_users[result_ind]
        if result == "turned_off":
            status = "turned_off"
        elif isinstance(result, types.Message):
            status = "sent"
        elif isinstance(result, CantInitiateConversation | ChatNotFound | InvalidPeerID):
            status = "not_entered"
        elif isinstance(result, Unauthorized):
            status = "blocked"
        elif isinstance(result, Exception):
            status = "error"
            logger.error(result, exc_info=True)
        else:
            status = "unknown"

        results_statuses[status].append(mailing_user.id)

        value = mailing_args_counts[mailing_user.mailing_args_id].get(status, 0) + 1
        mailing_args_counts[mailing_user.mailing_args_id][status] = value

    with DBSession():
        await set_statuses(results_statuses)
        await update_mailing_args(mailing_args_counts)

    for mailing_args_id, start_count in mailing_args_start_counts.items():
        counts = mailing_args_counts[mailing_args_id]

        sum_processed = sum(counts.values())

        if sum_processed >= start_count:
            asyncio.ensure_future(mailing_finished(mailing_args_id, counts))


@own_session
async def mailing_finished(mailing_args_id: int, counts: dict):
    mailing_args = await MailingArgs.get(mailing_args_id)
    creator: User = mailing_args.creator
    lang = creator.lang

    await mailing_args.finished()

    text = await f(
        "mailing finished", lang,
        tries_count=mailing_args.start_count,
        sent_count=counts.get("sent", 0),
        turned_off_count=counts.get("turned_off", 0),
        blocked_count=counts.get("blocked", 0),
    )

    not_entered_count = counts.get("not_entered", 0)
    if not_entered_count:
        text += "\n" + await f("mailing not entered count", lang, not_entered_count=not_entered_count)

    error_count = counts.get("error", 0)
    if error_count:
        text += "\n" + await f("mailing error count", lang, error_count=error_count)

    unknown_count = counts.get("unknown", 0)
    if unknown_count:
        text += "\n" + await f("mailing unknown count", lang, unknown_count=unknown_count)

    keyboard = await get_menu_keyboard(creator, mailing_args.created_from_bot_token, lang)

    await send_tg_message(
        mailing_args.creator.chat_id, "text", bot_token=mailing_args.created_from_bot_token,
        keyboard=keyboard, text=text
    )


async def set_statuses(statuses_ids: dict):
    for status, ids in statuses_ids.items():
        await set_status(status, ids)


async def update_mailing_args(mailing_args_counts: Dict[int, Dict[str, int]]):
    for mailing_args_id, counts in mailing_args_counts.items():
        await update_mailing_args_counts(mailing_args_id, dict(counts))
