from typing import Literal

import config as cfg
import schemas
from config import REVIEW_EMOJIS, REVIEW_NUMBERS_COUNT, REVIEW_STAR, REVIEW_STARS_COUNT

from core.messangers_adapters import types
from utils.text import c, f


async def get_emojis_keyboard(
        bot_type: schemas.BotTypeLiteral = "telegram",
) -> types.Keyboard:
    buttons = []
    button_cls = types.InlineKeyboardButton if bot_type == "telegram" else types.MenuKeyboardButton

    for emoji in REVIEW_EMOJIS:
        emoji_button = button_cls(text=emoji, data=c("review", emoji=emoji))
        buttons.append(emoji_button)

    return types.InlineMenuKeyboard(buttons=buttons, row_width=5)


async def get_stars_keyboard(bot_type: schemas.BotTypeLiteral = "telegram") -> types.Keyboard:
    buttons = []
    button_cls = types.InlineKeyboardButton if bot_type == "telegram" else types.MenuKeyboardButton

    for i in range(1, REVIEW_STARS_COUNT + 1):
        stars = REVIEW_STAR * i
        star_button = button_cls(text=stars, data=c("review", stars=i))
        buttons.append(star_button)

    return types.InlineMenuKeyboard(buttons=buttons, row_width=2)


async def get_numbers_keyboard(bot_type: schemas.BotTypeLiteral = "telegram") -> types.Keyboard:
    buttons = []
    button_cls = types.InlineKeyboardButton if bot_type == "telegram" else types.MenuKeyboardButton

    for i in range(1, REVIEW_NUMBERS_COUNT + 1):
        number_button = button_cls(text=str(i), data=c("review", number=f"{i}"))
        buttons.append(number_button)

    return types.InlineMenuKeyboard(buttons=buttons, row_width=5)


async def get_review_keyboard(
        mode: str, lang: str,
        bot_type: schemas.BotTypeLiteral = "telegram",
) -> types.Keyboard:
    if mode == "stars":
        keyboard = await get_stars_keyboard(bot_type)
    elif mode == "emojis":
        keyboard = await get_emojis_keyboard(bot_type)
    elif mode == "numbers":
        keyboard = await get_numbers_keyboard(bot_type)
    else:
        raise ValueError("Unknown mode")

    return await get_cancel_review_keyboard(lang, keyboard, bot_type)


async def get_cancel_review_keyboard(
        lang: str,
        keyboard: types.InlineMenuKeyboard | None = None,
        bot_type: schemas.BotTypeLiteral = "telegram",
        skip_button: bool = False,
) -> types.Keyboard:
    buttons = []
    button_cls = types.InlineKeyboardButton if bot_type == "telegram" else types.MenuKeyboardButton

    if skip_button:
        buttons.append(
            button_cls(
                text=await f("skip button", lang),
                data="skip"
            )
        )

    buttons.append(
        button_cls(
            text=await f("action cancel button", lang),
            data="cancel",
        )
    )

    if keyboard:
        keyboard.add_buttons(*buttons)
    else:
        keyboard = types.InlineMenuKeyboard(buttons=buttons)
    return keyboard.get(bot_type)
