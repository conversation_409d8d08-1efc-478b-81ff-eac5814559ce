import logging

from config import CRM_HOST
from core.kafka.producer.functions import add_push_notifications_for_action
from core.kafka.producer.helpers import build_fcm_message
from core.webhooks.functions import prepare_data_for_review_webhook, add_webhook_event
from db.models import Group, Review, User
from schemas import AuthSourceEnum, WebhookEntityEnum, WebhookActionEnum, WebhookReviewDataSchema
from utils.platform_admins import send_message_to_platform_admins
from utils.text import fd


async def send_review_push_notifications(
        review: Review,
        group: Group | None = None,
        review_user: User = None,
        ignore_session_id: int | None = None,
):
    try:
        assert not group or group.id == review.group_id, "Group does not match group group"
        assert not review_user or review_user.id == review.user_id, "User does not match review user"

        if not group:
            group = await Group.get(review.group_id)
        if not review_user:
            review_user = await User.get_by_id(review.user_id)

        async def get_message(user: User):
            texts = await fd(
                {
                    "title": {
                        "variable": (
                            "crm review read notification title"
                            if review.is_read else
                            "crm review new notification title"
                        ),
                        "text_kwargs": {
                            "review_id": review.id,
                        }
                    },
                    "body": {
                        "variable": f"crm review notification body",
                        "text_kwargs": {
                            "group_name": group.name,
                            "user_name": review_user.name,
                            "text": (review.text or "") + (review.additional_text or ""),
                        }
                    },
                },
                user.lang,
            )

            title = texts["title"]
            body = texts["body"].strip()

            return build_fcm_message(
                "review",
                review.id,
                review.crm_tag,
                title,
                body,
                delete_notification=review.is_read,
                apns_priority="5" if review.is_read else "10",
                add_data_texts=not review.is_read,
                link=f"{CRM_HOST}/review/{review.id}?listType=inbox&itemIdField=reviewId"
            )

        webhook_data: WebhookReviewDataSchema = await prepare_data_for_review_webhook(review, review_user, group)
        await add_webhook_event(
            entity=WebhookEntityEnum.REVIEW,
            entity_id=review.id,
            action=WebhookActionEnum.CREATED,
            group_id=group.id,
            data=webhook_data.dict(),
            data_type=WebhookReviewDataSchema,
        )
        
        return await add_push_notifications_for_action(
            AuthSourceEnum.CRM_WEB, AuthSourceEnum.CRM_APP,
            action="crm_review:read",
            available_data={
                "profile_id": review.group_id,
                "review_id": review.id,
            },
            message=get_message,
            ignore_session_id=ignore_session_id,
        )
    except Exception as e:
        group = await Group.get(review.group_id)
        review_user = await User.get_by_id(review.user_id)

        logging.error(f"send_review_push_notifications FAILED: ({str(e)}", exc_info=True)
        await send_message_to_platform_admins(
            f"An error occurred while sending review push notifications: {str(e)}\n"
            f"Review id: {review.id}\n"
            f"Profile: {group.name}({group.id})\n"
            f"Review user: {review_user.name}({review_user.id})\n"
        )
