import logging

from client.review.functions import add_review_service_bot_notifications
from core.review.notifications import send_review_push_notifications
from db.models import ClientBot, Group, Review, User
from schemas import ReviewPrivacyEnum, ReviewTypeEnum


async def create_review(
        review_type: ReviewTypeEnum,
        privacy: ReviewPrivacyEnum,
        review: dict,
        user: User,
        group: Group,
        bot: ClientBot | None = None,
        text: str | None = None,
        additional_text: str | None = None,
        media: dict | None = None,
        utm_labels: dict | None = None,
):
    review = await Review.create(
        review_type, review, user, group, bot, privacy, text, additional_text, media,
        utm_labels
    )

    try:
        await add_review_service_bot_notifications(review, user, group)
    except Exception as e:
        logging.error(
            f"An error occurred while putting review notifications(service bot): "
            f"{str(e)}",
            exc_info=True
        )

    await send_review_push_notifications(review, group, user)
    return review
