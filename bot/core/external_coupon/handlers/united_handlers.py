import logging

import schemas
from core.messangers_adapters import FSMContext
from db import crud
from db.models import Brand, ClientBot, User
from utils.text import f
from ..callback_data import ApplyCouponCallbackData, ShareCouponCallbackData
from ..external_coupon_processer import ExternalCouponProcessor
from ...messangers_adapters import types


async def share_coupon_united_handler(
        message: types.AnswerObject, state: FSMContext, user: User,
        bot: ClientBot, lang: str,
        keyboard: types.Keyboard,
        share_coupon: ShareCouponCallbackData,
):
    coupon_id = share_coupon.coupon_id
    brand_id = share_coupon.brand_id
    store_id = share_coupon.store_id

    try:
        if not brand_id and store_id:
            brand = await crud.get_brand_by_store(store_id)
        else:
            brand = await Brand.get(brand_id)
            
        # Отримуємо налаштування лояльності
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "store" if store_id else "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                store_id=store_id,
                profile_id=brand.group_id,
            )
        )
        
        if not loyalty_settings:
            return await message.answer(
                await f("loyalty not connected text", lang, brand_name=brand.name)
            )
            
        processor = ExternalCouponProcessor(
            message, state, bot, user, lang, keyboard, store_id=store_id
        )
        await processor.share_coupon(coupon_id, loyalty_settings, brand)
    except Exception as err:
        logging.error(err, exc_info=True)
        # return await message.answer(
        #     err.original_error_message if err.original_error_message else err.msg_text
        # )
        return await message.answer(await f("external coupon unknown error", lang))


async def apply_coupon_united_handler(
        message: types.AnswerObject, state: FSMContext, user: User,
        bot: ClientBot, lang: str,
        keyboard: types.Keyboard,
        apply_coupon: ApplyCouponCallbackData,
):
    coupon_code_or_id = apply_coupon.coupon_code_or_id
    brand_id = apply_coupon.brand_id

    try:
        if not brand_id and apply_coupon.store_id:
            brand = await crud.get_brand_by_store(apply_coupon.store_id)
        else:
            brand = await Brand.get(brand_id)
            
        # Отримуємо налаштування лояльності
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "store" if apply_coupon.store_id else "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                store_id=apply_coupon.store_id,
                profile_id=brand.group_id,
            )
        )
        
        if not loyalty_settings:
            return await message.answer(
                await f("loyalty not connected text", lang, brand_name=brand.name)
            )
            
        processor = ExternalCouponProcessor(
            message, state, bot, user, lang, keyboard, store_id=apply_coupon.store_id
        )
        return await processor.apply_coupon(coupon_code_or_id, loyalty_settings)
    except Exception as err:
        logging.error(err, exc_info=True)
        return await message.answer(await f("external coupon unknown error", lang))


async def vm_incust_coupon_united_handler(
        message: types.AnswerObject, state: FSMContext, user: User,
        bot: ClientBot, lang: str,
        keyboard: types.Keyboard,
        coupon_code: str,
):
    processor = ExternalCouponProcessor(
        message, state, bot, user, lang, keyboard, coupon_code
    )
    try:
        return await processor.process()
    except Exception as err:
        logging.error(err, exc_info=True)
        return await message.answer(await f("external coupon unknown error", lang))
