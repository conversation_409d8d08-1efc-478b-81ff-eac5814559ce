from psutils.callback_data import CallbackData
from pydantic import Field


class ShareCouponCallbackData(CallbackData, callback_mode="share_coupon"):
    coupon_id: str = Field(alias="c")
    brand_id: int | None = Field(alias="b")
    store_id: int | None = Field(alias="s")


class ApplyCouponCallbackData(CallbackData, callback_mode="apply_coupon"):
    coupon_code_or_id: str = Field(alias="c")
    brand_id: int | None = Field(alias="b")
    store_id: int | None = Field(alias="s")
