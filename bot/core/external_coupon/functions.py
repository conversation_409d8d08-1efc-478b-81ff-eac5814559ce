import logging

import aiowhatsapp as wa

import schemas
from core import messangers_adapters as ma
from core.external_coupon.keyboards import get_coupon_keyboard
from core.notifications.funcs import put_message_to_kafka
from db.models import Brand, ClientBot, User
from utils.text import f, html_to_markdown

debugger = logging.getLogger("debugger.coupons")


async def send_coupon_photo(
        coupon: schemas.CouponShowData,
        user: User,
        bot: ClientBot,
        keyboard: ma.Keyboard | None = None,
        url: str | None = None,
):

    match bot.bot_type:
        case "telegram":
            if not user.chat_id:
                debugger.debug(f"coupons not sent. {user} does not have chat_id")
                return None

            if url:
                text = f"<b>{coupon.title}</b>\n\n{url}"
            else:
                text = coupon.text

            if coupon.image:
                try:
                    return await put_message_to_kafka(
                        bot, user,
                        (text[:1020] + " ...") if len(text) > 1024 else text,
                        "image", keyboard,
                        media=True,
                        content=dict(
                            url=coupon.image,
                            caption=(text[:1020] + " ...") if len(text) > 1024 else text
                        ),
                    )
                except Exception as e:
                    logging.error(e, exc_info=True)
            return await put_message_to_kafka(
                bot, user, text, "text", keyboard,
            )

        case "whatsapp":
            if not user.wa_phone:
                debugger.warning(f"coupons not sent. {user} does not have wa_phone")
                return None

            bot = wa.WhatsappBot(
                bot.token,
                bot.whatsapp_from,
            )
            bot_reset_token = wa.WhatsappBot.set_current(bot)

            try:
                title = html_to_markdown(coupon.title)
                body_text = url or coupon.description
                if body_text:
                    body_text = html_to_markdown(body_text)

                if keyboard:
                    if body_text:
                        if len(title) > 58:
                            header = f"*{title[:55]}...*"
                        else:
                            header = f"*{title}*"
                        body = f"{header}\n\n{body_text}"
                    else:
                        body = f"*{title}*"

                    if len(body) > 1024:
                        body = body[:1021] + "..."

                    if coupon.image:
                        try:
                            debugger.debug(
                                "trying to send coupon with keyboard and image"
                            )
                            return await keyboard.sending_method(
                                user.wa_phone,
                                body=body,
                                image=coupon.image,
                                **keyboard.to_kwargs(),
                            )
                        except Exception as e:
                            debugger.debug(
                                "an error occurred while sending coupon with keyboard "
                                "and image"
                            )
                            logging.error(e, exc_info=True)

                    try:
                        debugger.debug(
                            "trying to send coupon with keyboard and without image"
                        )
                        return await keyboard.sending_method(
                            user.wa_phone,
                            body=body or title,
                            **keyboard.to_kwargs(),
                        )
                    except Exception as e:
                        debugger.debug(
                            "an error occurred while sending coupon with keyboard and "
                            "without image"
                        )
                        logging.error(e, exc_info=True)

                if body_text:
                    text = f"*{title}*\n\n{body_text}"
                else:
                    text = f"*{title}*"

                if coupon.image:
                    if len(text) > 1024:
                        caption = text[:1020] + " ..."
                    else:
                        caption = text

                    try:
                        debugger.debug(
                            "trying to send coupon without keyboard and with image"
                        )

                        return await bot.send_image(
                            user.wa_phone,
                            image=coupon.image,
                            caption=caption,
                        )
                    except Exception as e:
                        debugger.debug(
                            "an error occurred while sending coupon with image and "
                            "without keyboard"
                        )
                        logging.error(e, exc_info=True)

                if len(text) > 4096:
                    text = text[:4092] + " ..."

                return await bot.send_message(
                    user.wa_phone,
                    text=text,
                )
            finally:
                wa.WhatsappBot.reset_current(bot_reset_token)
        case _:
            raise ValueError("Unknown bot_type")


async def send_coupon_info(
        coupon: schemas.CouponShowData,
        user: User,
        bot: ClientBot,
        lang: str,
        brand_id: int,
        store_id: int | None = None,
):
    to = ma.get_user_to(user, bot.bot_type)
    if not to:

        debugger.debug(f"User is not in bot {bot.bot_type}")
        return None

    keyboard = await get_coupon_keyboard(
        coupon, bot.bot_type, lang, brand_id, user, store_id=store_id
    )

    if coupon.type == "external":

        text = coupon.text
        
        # Передаємо pdf_media_id в Kafka для обробки в worker'і
        if coupon.pdf_media_id:
            match bot.bot_type:
                case "telegram":
                    await put_message_to_kafka(
                        bot, user,
                        (text[:1020] + " ...") if len(text) > 1024 else text,
                        "document", keyboard, media=True, content=dict(
                            pdf_media_id=coupon.pdf_media_id,
                            filename=f"coupon_{coupon.code or coupon.id}.pdf",
                            caption=(text[:1020] + "...") if len(text) > 1024 else text
                        )
                    )
                case "whatsapp":
                    await put_message_to_kafka(
                        bot, user, html_to_markdown(text[:1020] + " ..." if len(text) > 1024 else text),
                        "document", keyboard, media=True, content=dict(
                            pdf_media_id=coupon.pdf_media_id,
                            filename=f"coupon_{coupon.code or coupon.id}.pdf",
                        )
                    )
                case _:
                    raise ValueError("Unsupported bot.bot_type")
        else:
            # Якщо немає PDF, відправляємо звичайне повідомлення
            debugger.warning(f"No PDF found for coupon {coupon.id or coupon.code}, sending text message")
            await put_message_to_kafka(
                bot, user,
                (text[:1020] + " ...") if len(text) > 1024 else text,
                "message", keyboard
            )
    else:
        try:
            await send_coupon_photo(
                coupon, user, bot, keyboard
            )
        except Exception as ex:
            logging.error(ex, exc_info=True)

    wallet_text = await f("you got voucher text", lang)
    brand = await Brand.get(brand_id)
    url = brand.get_url("profile/loyalty")
    wallet_keyboard = ma.InlineKeyboard()
    wallet_keyboard.add_buttons(ma.UrlKeyboardButton(await f("web app show wallet button", lang), url=url))

    return await put_message_to_kafka(
        bot, user, wallet_text, "text",
        wallet_keyboard.to_messanger(bot.bot_type)
    )
