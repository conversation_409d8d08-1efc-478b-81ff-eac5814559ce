import logging

import schemas
from core import messangers_adapters as ma
from core.loyalty import coupon_service
from db import crud
from db.models import Brand, User
from schemas import CouponShowData
from utils.text import f
from .callback_data import ApplyCouponCallbackData, ShareCouponCallbackData


async def get_coupon_keyboard(
        coupon: CouponShowData,
        bot_type: schemas.BotTypeLiteral,
        lang: str,
        brand_id: int,
        user: User,
        store_id: int | None = None,
) -> ma.Keyboard | None:

    is_in_wallet = False
    is_need_apply = False

    keyboard = ma.InlineKeyboard()
    brand = await Brand.get(brand_id)

    # Отримуємо налаштування лояльності
    loyalty_settings = await crud.get_loyalty_settings_for_context(
        "store" if store_id else "brand",
        schemas.LoyaltySettingsData(brand_id=brand.id, store_id=store_id)
    )

    if not loyalty_settings:
        return None

    coupon_data = None

    # Перевіряємо чи купон у гаманці користувача через єдиний сервіс
    coupon_identifier = coupon.id or coupon.code
    logging.info(f"KEYBOARD: Processing coupon identifier={coupon_identifier}, user={user.id}")
    
    if coupon_identifier:
        try:
            is_in_wallet = await coupon_service.check_if_in_wallet(
                coupon_identifier,
                user,
                loyalty_settings,
                lang
            )
            logging.info(f"KEYBOARD: Coupon {coupon_identifier} in wallet: {is_in_wallet}")
            
            # Отримуємо дані купона через єдиний сервіс
            coupon_data = await coupon_service.get_coupon_data(
                coupon_identifier,
                user,
                loyalty_settings,
                lang
            )
            
            if coupon_data:
                logging.info(f"KEYBOARD: Coupon data - status={getattr(coupon_data, 'status', None)}, "
                           f"type={getattr(coupon_data, 'type', None)}, "
                           f"id={getattr(coupon_data, 'id', None)}, "
                           f"code={getattr(coupon_data, 'code', None)}, "
                           f"redeem_at_terminal={getattr(coupon_data, 'redeem_at_terminal', None)}")
            else:
                logging.warning(f"KEYBOARD: No coupon data received for {coupon_identifier}")
                
        except Exception as e:
            logging.error(f"Error checking coupon via unified service: {e}")
            coupon_data = None

    # Обробка кнопок
    if coupon.share_allowed and is_in_wallet and coupon.id:
        callback_data = ShareCouponCallbackData(
            coupon_id=coupon.id,
        )
        if store_id:
            callback_data.store_id = store_id
        else:
            callback_data.brand_id = brand_id
        callback_data = callback_data.to_str()

        keyboard.add_buttons(
            ma.InlineKeyboardButton(
                await f("share coupon button", lang),
                callback_data
            )
        )

    # Check Apply
    if not coupon_data:
        logging.warning(f"KEYBOARD: No fresh coupon data available for {coupon_identifier} - skipping apply button")
        return keyboard.to_messanger(bot_type) if len(keyboard.buttons) > 0 else None
        
    check_data = coupon_data
    check_status = check_data.status if hasattr(check_data, 'status') and check_data.status else 'active'
    check_type = check_data.type if hasattr(check_data, 'type') and check_data.type else coupon.type
    
    logging.info(f"KEYBOARD: Apply button check - coupon_identifier={coupon_identifier}, "
               f"check_status={check_status}, check_type={check_type}, is_in_wallet={is_in_wallet}")
    
    if check_data and check_status and check_status != 'redeemed' and (
            check_type and check_type == 'certificate'
            or (
                    check_type and check_type == 'external'
                    and not (hasattr(
                check_data, 'redeem_at_terminal'
            ) and check_data.redeem_at_terminal)
                    and (not hasattr(
                check_data, 'external_code_visibility_type'
            ) or check_data.external_code_visibility_type != 'always-open')
            )
            or (
                    check_type and check_type == 'check-modifier' and not
                     is_in_wallet)
    ):
        is_need_apply = True
        logging.info(f"KEYBOARD: Apply button will be shown for {coupon_identifier}")
    else:
        logging.info(f"KEYBOARD: Apply button will NOT be shown for {coupon_identifier}")

    if is_need_apply and check_data:
        # Визначаємо що використовувати - ID з API або код зі збережених даних
        if coupon_data and hasattr(coupon_data, 'id') and coupon_data.id:
            # Якщо є coupon_data з API - використовуємо його ID
            coupon_identifier = str(coupon_data.id)
        elif coupon.code:
            # Якщо це збережений купон - використовуємо код
            coupon_identifier = str(coupon.code)
        elif coupon.id:
            # Fallback на id
            coupon_identifier = str(coupon.id)
        else:
            # Останній fallback
            coupon_identifier = str(check_data.id if hasattr(check_data, 'id') and check_data.id else "")
        
        callback_data = ApplyCouponCallbackData(
            coupon_code_or_id=coupon_identifier,
        )
        if store_id:
            callback_data.store_id = store_id
        else:
            callback_data.brand_id = brand_id
        keyboard.add_buttons(
            ma.InlineKeyboardButton(
                await f("apply coupon button", lang),
                callback_data.to_str(),
            )
        )

    return keyboard.to_messanger(bot_type) if len(keyboard.buttons) > 0 else None
