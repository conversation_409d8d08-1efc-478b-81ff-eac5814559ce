import asyncio
import json

from psutils.exceptions import ErrorWithTextVariable

import schemas
from core import messangers_adapters as ma
from core.external_coupon import send_coupon_info, send_coupon_photo
from core.external_coupon.deep_links import ExternalCouponDeepLink
from core.loyalty import coupon_service
from core.loyalty.customer_service import get_or_create_incust_customer
from core.loyalty.exceptions import InCustAPIError
from core.messangers_adapters import FSMContext, types
from core.notifications.funcs import put_message_to_kafka
from db import crud
from db.models import Brand, ClientBot, LoyaltySettings, User
from loggers import JSONLogger
from utils.message import send_tg_message, send_wa_message
from utils.qrcode import send_qr_code
from utils.text import f, html_to_markdown


class ExternalCouponProcessor:

    def __init__(
            self,
            message: types.AnswerObject,
            state: FSMContext,
            bot: ClientBot,
            user: User, lang: str,
            keyboard: types.Keyboard | None = None,
            external_coupon_code: str | None = None,
            store_id: int | None = None,
    ):
        self.message: types.AnswerObject = message
        self.state: FSMContext = state
        self.bot: ClientBot = bot
        self.user: User = user
        self.lang: str = lang
        self.main_keyboard = keyboard
        self.external_coupon_code: str = external_coupon_code
        self.store_id: int = store_id
        self.debugger = JSONLogger(
            "ext_coupon", {
                "group_id": self.bot.group_id,
                "inviting_user_first_name": self.user.first_name,
                "inviting_user_last_name": self.user.last_name,
                "inviting_user_id": self.user.id,
                "bot_id": self.bot.id,
                "bot_name": self.bot.display_name,
                "main_keyboard_type": str(type(self.main_keyboard)),
            }
        )

    async def process(self):
        self.debugger.debug("process ->")
        try:
            await self.state.finish()
            brand = await crud.get_brand_by_group(self.bot.group_id)
            self.debugger.add_data({"brand_id": brand.id, "brand_name": brand.name})

            context_type = "store" if self.store_id else "brand"
            loyalty_settings = await crud.get_loyalty_settings_for_context(
                context_type,
                schemas.LoyaltySettingsData(brand_id=brand.id, store_id=self.store_id)
            )

            if not loyalty_settings:
                self.debugger.debug(
                    "sending coupon message with keyboard "
                    "process_error_loyalty_not_connected"
                )
                return await self.message.answer(
                    await f(
                        "loyalty not connected text", self.lang, brand_name=brand.name
                    ),
                    reply_markup=(
                        self.main_keyboard
                        if self.bot.bot_type == "whatsapp" else None
                    ),
                )

            await self._sync_incust_customer(loyalty_settings)

            # Додаємо купон до гаманця
            if added_coupons := await self.add_coupon_to_wallet(loyalty_settings):
                await self.process_coupon(brand.id, loyalty_settings, added_coupons)

            asyncio.ensure_future(self.send_delayed_main_menu_message())

        except Exception as e:
            await self.process_exception(e)

    async def _sync_incust_customer(
            self, loyalty_settings: LoyaltySettings,
    ):
        """Синхронізація користувача з InCust через новий клієнт"""
        try:
            await get_or_create_incust_customer(
                user=self.user,
                loyalty_settings=loyalty_settings,
                lang=self.lang
            )
            self.debugger.debug("Користувач синхронізований з InCust")
        except Exception as e:
            self.debugger.error(
                f"Помилка синхронізації користувача з InCust: {e}", exc_info=True
            )
            raise

    async def process_coupon(
            self, brand_id: int,
            loyalty_settings: LoyaltySettings,
            added_coupons: list[str] = None,
    ):
        """Обробка купона через новий сервіс лояльності"""
        self.debugger.debug(
            f"process_coupon -> {self.external_coupon_code=}, {added_coupons=}"
        )

        try:
            # Використовуємо ID якщо купон було додано, інакше код
            coupon_id_or_code = added_coupons[0] if added_coupons else self.external_coupon_code
            
            # Отримуємо дані купона через єдиний сервіс
            coupon = await coupon_service.get_coupon_data(
                coupon_id_or_code,
                self.user,
                loyalty_settings,
                self.lang
            )

            if coupon:
                # Отримуємо бренд для PDF
                brand = await Brand.get(brand_id)
                
                # Створюємо CouponShowData через єдиний сервіс (з PDF)
                coupon_show_data = await coupon_service.prepare_coupon_for_display(
                    coupon,
                    brand,
                    loyalty_settings,
                    self.user,
                    self.lang,
                    
                    store_id=self.store_id
                )

                if coupon_show_data:
                    await send_coupon_info(
                        coupon_show_data,
                        self.user,
                        self.bot,
                        self.lang,
                        brand_id,
                        store_id=self.store_id
                    )

                    self.debugger.debug(
                        'process_coupon: Messages about coupon received successfully'
                    )
                else:
                    self.debugger.error(f"Не вдалося підготувати дані купона для показу")
            else:
                self.debugger.debug(
                    f"Не вдалося отримати дані купона {self.external_coupon_code}"
                )

        except Exception as e:
            self.debugger.error(f"Помилка обробки купона: {e}", exc_info=True)
            raise

    async def add_coupon_to_wallet(self, loyalty_settings) -> list[str] | None:
        """Додавання купона до гаманця через єдиний сервіс"""
        self.debugger.debug("add_coupon_to_wallet ->")
        try:
            # Використовуємо єдиний сервіс для додавання до гаманця
            coupon_id, message = await coupon_service.add_to_wallet(
                self.external_coupon_code,
                self.user,
                loyalty_settings,
                self.lang
            )

            if coupon_id:
                # Успішно додано
                text = await f("add coupon to wallet success text", self.lang)
                await self.message.answer(text)
                self.debugger.debug("add coupon to wallet message Ok")
                return [coupon_id]
            else:
                # Не вдалося додати
                self.debugger.debug("add coupon to wallet error - no coupon ID returned")
                text = await f("add coupon to wallet failed text", self.lang)
                await self.message.answer(text)
                return None

        except Exception as e:
            self.debugger.error(
                f"Помилка додавання купона до гаманця: {e}", exc_info=True
            )
            text = await f("add coupon to wallet failed text", self.lang)
            await self.message.answer(text)
            return None

    async def share_coupon(
            self, coupon_id: str, loyalty_settings, brand: Brand
    ):
        """Поділитися купоном через єдиний сервіс"""
        self.debugger.debug(f"share_coupon -> {coupon_id=}")
        try:
            shared_code = await coupon_service.share_coupon(
                coupon_id,
                self.user,
                loyalty_settings,
                self.lang
            )

            if not shared_code:
                self.debugger.debug("not available coupon for share")
                raise Exception(await f('not available coupon for share', self.lang))

            # Отримуємо дані купона за кодом через єдиний сервіс
            coupon = await coupon_service.get_coupon_data(
                shared_code,
                self.user,
                loyalty_settings,
                self.lang
            )

            if not coupon:
                raise Exception("Failed to get coupon data after sharing")

            # Формуємо URL для поділення
            if self.bot.bot_type == "telegram":
                url = (f"https://t.me/{self.bot.username}?start=coupon-"
                       f"{shared_code}")
                if self.store_id:
                    url += f"store_id-{self.store_id}"
            else:
                url = ExternalCouponDeepLink(
                    external_coupon_code=shared_code,
                    store_id=self.store_id,
                ).to_str(self.bot.bot_type, self.bot.id_name)

            await send_qr_code(
                self.message, self.state,
                url,
                show_url=False,
                url_apart=False,
                text=await f("please scan QR code", self.lang),
                bot=self.bot,
            )
            self.debugger.debug("send_qr_code Ok")

            await put_message_to_kafka(
                self.bot, self.user,
                await f(
                    "incust loyalty accept invitation qr message footer", self.lang
                ),
                "text", None,
            )

            await send_coupon_photo(
                coupon,
                self.user,
                self.bot,
                url=url,
            )

            await put_message_to_kafka(
                self.bot, self.user,
                await f("can send link or publish footer", self.lang),
                "text", None,
            )

            self.debugger.debug("send_coupon_photo Ok")
            asyncio.ensure_future(self.send_delayed_main_menu_message())
            self.debugger.debug("share_coupon END")
        except Exception as e:
            return await self.process_exception(e)

    async def apply_coupon(
            self, coupon_code_or_id: str, loyalty_settings: LoyaltySettings, ):
        """Застосування купона через єдиний сервіс"""
        self.debugger.debug(f"apply_coupon -> {coupon_code_or_id=}")
        self.debugger.debug(f"User data: id={self.user.id}, external_id={self.user.incust_external_id}, lang={self.lang}")
        self.debugger.debug(f"Loyalty settings: id={loyalty_settings.id}, server_url={loyalty_settings.server_url}")
        
        try:
            texts = []

            # Отримуємо дані купона через єдиний сервіс
            coupon = await coupon_service.get_coupon_data(
                coupon_code_or_id,
                self.user,
                loyalty_settings,
                self.lang
            )

            if not coupon:
                raise Exception("Coupon not found")

            # Перевіряємо чи купон в гаманці
            is_in_wallet = await coupon_service.check_if_in_wallet(
                coupon_code_or_id,
                self.user,
                loyalty_settings,
                self.lang
            )
            
            self.debugger.info(f"APPLY: Coupon {coupon_code_or_id} in wallet check: {is_in_wallet}, "
                             f"coupon_status={getattr(coupon, 'status', None)}, "
                             f"coupon_type={getattr(coupon, 'type', None)}")

            # Якщо купон не в гаманці - додаємо його
            if not is_in_wallet:
                self.debugger.debug(f"Adding coupon to wallet: code={coupon.code}, external_id={self.user.incust_external_id}")
                
                # Визначаємо код для додавання
                coupon_code = coupon.code if hasattr(coupon, 'code') and coupon.code else coupon_code_or_id
                self.debugger.info(f"APPLY: Adding coupon to wallet - using code={coupon_code}")
                
                coupon_id, add_message = await coupon_service.add_to_wallet(
                    coupon_code,
                    self.user,
                    loyalty_settings,
                    self.lang
                )
                
                if coupon_id:
                    self.debugger.info(f"APPLY: Successfully added coupon to wallet, coupon_id={coupon_id}")
                    texts.append(await f('coupon was added', self.user.lang))
                    # Отримуємо оновлені дані купона
                    coupon = await coupon_service.get_coupon_data(
                        coupon_id,
                        self.user,
                        loyalty_settings,
                        self.lang
                    ) or coupon
                    self.debugger.debug(f"Updated coupon data after adding to wallet")
                else:
                    self.debugger.warning("APPLY: Failed to add coupon to wallet")
            else:
                self.debugger.info(f"APPLY: Coupon {coupon_code_or_id} already in wallet")

            self.debugger.debug(f"Current coupon state: status={getattr(coupon, 'status', None)}, type={getattr(coupon, 'type', None)}")
            
            # Перевіряємо статус купона
            if coupon.status and coupon.status == 'redeemed':
                self.debugger.debug("Coupon already redeemed")
                texts.append(await f('coupon already used text', self.user.lang))
            elif (coupon.type and coupon.type != 'check-modifier' and
                  not (hasattr(coupon, 'redeem_at_terminal') and coupon.redeem_at_terminal)):
                # Застосовуємо купон через єдиний сервіс
                if coupon and hasattr(coupon, 'id') and coupon.id:
                    self.debugger.debug(f"Attempting to redeem coupon: id={coupon.id}, user_id={self.user.id}")
                    try:
                        result = await coupon_service.redeem_coupon(
                            str(coupon.id),
                            self.user,
                            loyalty_settings,
                            self.lang
                        )
                        self.debugger.debug(f"Redeem coupon result: {result}")
                        # Не додаємо повідомлення бо воно прийде від InCust
                    except Exception as redeem_ex:
                        self.debugger.error(f"Error redeeming coupon: {redeem_ex}", exc_info=True)
                        raise  # Передаємо виняток до process_exception
                else:
                    self.debugger.error("Cannot redeem coupon - no valid ID available")

            # Якщо це check-modifier купон
            if coupon.type and coupon.type == 'check-modifier':
                texts.append(await f('coupon can use for order', self.user.lang))

            if texts:
                await self.send_coupon_result_message('\n'.join(texts))

        except Exception as e:
            await self.process_exception(e)

    async def send_coupon_result_message(self, text):
        self.debugger.debug("send_coupon_result_message ->")
        to = ma.get_user_to(self.user, self.bot.bot_type)
        if not to:
            self.debugger.debug(
                f"send_coupon_result_message: User is not in bot {self.bot.bot_type=}"
            )
            return None

        kwargs = {
            "keyboard": self.main_keyboard if self.bot.bot_type == "whatsapp" else None
        }

        match self.bot.bot_type:
            case "telegram":
                func = send_tg_message
                kwargs["text"] = text

            case "whatsapp":
                func = send_wa_message
                kwargs["wa_from"] = self.bot.whatsapp_from
                kwargs["text"] = html_to_markdown(text)
            case _:
                err_msg = f"Unsupported {self.bot.bot_type=}"
                self.debugger.debug(f"send_coupon_result_message: {err_msg}")
                raise ValueError(err_msg)

        return await func(
            to,
            "text",
            bot_token=self.bot.token,
            **kwargs,
        )

    async def process_exception(self, e: Exception):
        self.debugger.error(f"process_exception -> {e=}", exc_info=True)

        if isinstance(e, InCustAPIError):
            text_variable = "external loyalty error"
            text_kwargs = {"message": e.detail}
        elif isinstance(e, ErrorWithTextVariable):
            text_variable = e.text_variable
            text_kwargs = e.text_kwargs
        # Обробка HTTPException з coupon_service
        elif hasattr(e, "detail") and getattr(e, "detail"):
            text_variable = "external loyalty error"
            text_kwargs = {"message": e.detail}
        # Обробка ApiException з нових InCust клієнтів
        elif hasattr(e, "body") and getattr(e, "body"):
            try:
                parsed_body = json.loads(e.body)
                message = parsed_body.get("message", str(e))
                text_variable = "external loyalty error"
                text_kwargs = {"message": message}
            except (json.JSONDecodeError, AttributeError):
                text_variable = "external loyalty error"
                text_kwargs = {"message": getattr(e, "reason", str(e))}
        elif hasattr(e, "message") and getattr(e, "message"):
            text_variable = "external loyalty error"
            text_kwargs = {"message": e.message}
        else:
            text_variable = "external coupon unknown error"
            text_kwargs = {}

        return await self.send_coupon_result_message(
            await f(text_variable, self.lang, **text_kwargs)
        )

    async def send_delayed_main_menu_message(self):
        if self.bot.bot_type != "whatsapp":
            return
        self.debugger.debug("send_delayed_main_menu_message ->")
        await asyncio.sleep(4)  # Wait send coupon
        await self.message.answer(
            await f("main menu button", self.lang),
            reply_markup=self.main_keyboard if self.bot.bot_type == "whatsapp" else None
        )
        self.debugger.debug("main wa menu sent Ok")
