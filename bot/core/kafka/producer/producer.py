from aiokafka import AIOKafkaProducer

from config import KAFKA_SERVER


class KafkaProducer:
    def __init__(self):
        self._producer: AIOKafkaProducer | None = None

    @property
    def p(self):
        if not self._producer:
            raise RuntimeError(
                "Kafka producer not initialized. Please, initialise it with "
                "initialise() method"
            )
        return self._producer

    async def initialise(self):
        self._producer = AIOKafkaProducer(
            bootstrap_servers=KAFKA_SERVER,
            max_request_size=5_000_000_000,
        )
        await self.p.start()

    async def stop(self):
        if self._producer:
            return await self.p.stop()

    async def __aenter__(self):
        await self.initialise()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()
