import asyncio
from copy import deepcopy
from email.mime.base import MIMEBase
from pydantic import BaseModel
from typing import Any, Awaitable, Literal, Protocol

import schemas
from config import (
    DEBUG, LOC7_EMAIL_LOGIN,
    LOC7_EMAIL_PASSWORD,
    SERVICE_BOT_API_TOKEN, SERVICE_BOT_USERNAME,
)
from core import messangers_adapters as ma
from core.marketing.exceptions import (
    MailingUnknownChannelError,
)
from db import crud
from db.models import (
    ClientBot, Group, MediaObject, SystemNotification, Task, User,
    UserClientBotActivity,
)
from loggers import JSONLogger
from schemas import (
    BotTypeLiteral, EmailMailingMessageValue, MailingTelegramMessageValue,
    MailingWhatsappMessageValue, NotificationMessageType, NotificationMessageValue,
    SendEmailData, TaskValue,
    TelegramMessageValue, WhatsappMessageValue,
)
from utils.date_time import utcnow
from utils.text import html_to_markdown
from utils.type_vars import T
from .producer_instance import producer
from ..constants import (
    EMAIL_REGULAR_TOPIC, MAILING_EMAIL_REGULAR_TOPIC, MAILING_TELEGRAM_REGULAR_TOPIC,
    MAILING_WHATSAPP_REGULAR_TOPIC, PUSH_REGULAR_TOPIC, TASK_REGULAR_TOPIC,
    TELEGRAM_REGULAR_TOPIC, UNIVERSAL_NOTIFICATION_REGULAR_TOPIC, WEBHOOK_REGULAR_TOPIC,
    WHATSAPP_REGULAR_TOPIC,
)

# Ліміт WhatsApp для звичайного тексту (4096) і для тексту з меню (2048 —
# експериментально, щоб меню гарантовано показувалось)
WHATSAPP_MESSAGE_LIMIT = 4096
WHATSAPP_MENU_SAFE_LIMIT = 2048


class GetMessageFunc(Protocol[T]):
    def __call__(
            self, user: User,
    ) -> T | None | Awaitable[T | None]: ...


async def add_push_notifications_for_action(
        *auth_sources: schemas.AuthSourceEnum,
        action: str,
        available_data: dict,
        message: schemas.FCMMessage | GetMessageFunc[schemas.FCMMessage],
        ignore_session_id: int | None = None,
        logger: JSONLogger = None,
        no_error: bool = False,
):
    push_data = dict(locals())
    del push_data["logger"]
    debug_data = {
        "push_data": push_data
    }

    if not logger:
        logger = JSONLogger("kafka.producer")

    if DEBUG:
        logger.debug("add_push_notifications_for_action: Start", debug_data)

    users_with_push_tokens = None
    result = []

    try:
        users_with_push_tokens = await crud.get_users_with_push_tokens_for_action(
            auth_sources, action, available_data, ignore_session_id
        )

        debug_data["users"] = users_with_push_tokens

        if DEBUG:
            logger.debug(
                "add_push_notifications_for_action: Users retrieved",
                debug_data,
            )

        futures = []
        for user, push_token in users_with_push_tokens:
            if isinstance(message, schemas.FCMMessage):
                fcm_message = deepcopy(message)
                fcm_message.token = push_token
            else:
                msg_result = message(user)
                fcm_message = await msg_result if asyncio.iscoroutine(
                    msg_result
                ) else msg_result
                fcm_message.token = push_token
            value = schemas.PushMessageValue(
                device_token=push_token,
                fcm_message=fcm_message,
            )
            result.append(value)
            futures.append(
                await producer.p.send(
                    topic=PUSH_REGULAR_TOPIC,
                    value=value.json().encode(),
                    key=push_token.encode(),
                )
            )
        await asyncio.gather(*futures)

        debug_data["results"] = result

        logger.debug(
            "add_push_notifications_for_action: Successfully sent to Kafka",
            debug_data,
            {
                "results": result
            }
        )

    except Exception as e:
        logger.error(
            "add_push_notifications_for_action: Error",
            e, debug_data,
        )
        if not no_error:
            raise

    return result


async def add_telegram_notifications_for_action(
        bot_id: int | Literal["service", "admin"],
        bot_name: str,
        bot_token: str,
        action: str,
        available_data: dict,
        message: dict | GetMessageFunc[dict],
        ignor_user_id: int | None = None,
        logger: JSONLogger = None,
        is_entered_service_bot: bool | None = False,
        no_error: bool = False,
        user_chat_id: int | None = None,
):
    message_data = dict(locals())
    del message_data["logger"]
    debug_data = {
        "message_data": message_data
    }

    if not logger:
        logger = JSONLogger("kafka.producer")

    if DEBUG:
        logger.debug("add_telegram_notifications_for_action: Start", debug_data)

    result = []

    try:
        if not user_chat_id:
            users = await crud.get_users_for_action(
                action, available_data, ignor_user_id,
                required_fields=("chat_id",),
                is_entered_service_bot=is_entered_service_bot,
            )
        else:
            user = await User.get(user_chat_id)
            users = [user]

        debug_data["users"] = users

        if DEBUG:
            logger.debug(
                "add_telegram_notifications_for_action: Users retrieved",
                debug_data
            )

        futures = []

        for user in users:
            if callable(message):
                msg_result = message(user)
                sending_data = await msg_result if asyncio.iscoroutine(
                    msg_result
                ) else msg_result
            else:
                sending_data = message
            value = schemas.TelegramMessageValue(
                chat_id=user.chat_id,
                user_id=user.id,
                user_name=user.name,
                bot_id=bot_id,
                bot_token=bot_token,
                bot_name=bot_name,
                sending_data=sending_data
            )
            result.append(value)
            futures.append(
                await producer.p.send(
                    topic=TELEGRAM_REGULAR_TOPIC,
                    value=value.json().encode(),
                    key=f"tg-{bot_id}-{user.chat_id}".encode(),
                )
            )

        await asyncio.gather(*futures)

        logger.debug(
            "add_telegram_notifications_for_action: Successfully sent to Kafka",
            debug_data, {
                "results": result
            }
        )
    except Exception as e:
        logger.error(
            "add_telegram_notifications_for_action: Error",
            e, debug_data,
        )

        if not no_error:
            raise

    return result


async def add_email_notifications_for_action(
        sender: str,
        sender_password: str,
        action: str,
        available_data: dict,
        message: SendEmailData | GetMessageFunc[SendEmailData],
        ignor_user_id: int | None = None,
        logger: JSONLogger = None,
        no_error: bool = False,
):
    message_data = dict(locals())
    del message_data["logger"]
    debug_data = {
        "message_data": message_data
    }

    if not logger:
        logger = JSONLogger("kafka.producer")

    if DEBUG:
        logger.debug("add_email_notifications_for_action: Start", debug_data)

    result = []

    try:
        if available_data.get("profile_id"):
            users = await crud.get_users_for_action(
                action, available_data, ignor_user_id,
                required_fields=("email",),
            )
        elif available_data.get("user_id"):
            users = [await User.get_by_id(available_data.get("user_id"))]
        elif available_data.get("emails"):
            class EmailUser(BaseModel):
                id: int
                email: str
                name: str

            users = [EmailUser(id=id, email=email_data, name=email_data) for
                     id, email_data in enumerate(available_data.get("emails"), 1)]
        else:
            raise ValueError("No profile_id or user_id in available_data")

        debug_data["users"] = users

        if DEBUG:
            logger.debug(
                "add_email_notifications_for_action: Users retrieved", debug_data, {
                    "users": users
                }
            )

        futures = []

        for user in users:
            if callable(message):
                msg_result = message(user)
                sending_data = await msg_result if asyncio.iscoroutine(
                    msg_result
                ) else msg_result
            else:
                sending_data = message

            # Process MIMEBase attachments to make them JSON serializable
            if sending_data.attachments:
                processed_attachments = []
                for attachment in sending_data.attachments:
                    if isinstance(attachment, MIMEBase):
                        # Convert MIMEBase to string for JSON serialization
                        processed_attachments.append(attachment.as_string())
                    else:
                        processed_attachments.append(attachment)
                sending_data.attachments = processed_attachments

            value = schemas.EmailMessageValue(
                sender=sender,
                sender_password=sender_password,
                destination=user.email,
                user_id=user.id,
                user_name=user.name,
                sending_data=sending_data
            )
            result.append(value)

            # Логування перед відправкою в Kafka
            logger.debug(
                "EMAIL_KAFKA_SEND_TIME: Sending email to Kafka",
                {
                    "timestamp": utcnow().isoformat(),
                    "destination_email": user.email,
                    "subject": sending_data.subject,
                    "kafka_key": f"email-{sender}-{user.email}"
                }
            )

            futures.append(
                await producer.p.send(
                    topic=EMAIL_REGULAR_TOPIC,
                    value=value.json().encode(),
                    key=f"email-{sender}-{user.email}".encode(),
                )
            )

        await asyncio.gather(*futures)
        logger.debug(
            "add_email_notifications_for_action: Successfully sent to Kafka",
            debug_data,
            {
                "results": result
            }
        )
    except Exception as e:
        logger.error(
            "add_email_notifications_for_action: Error",
            e, debug_data,
        )

        if not no_error:
            raise

    return result


def build_message_kwargs_for_bot(bot: ClientBot | Literal["service", "admin"]):
    if bot == "service":
        return {
            "bot_id": "service",
            "bot_name": SERVICE_BOT_USERNAME,
            "bot_type": "telegram",
            "bot_token": SERVICE_BOT_API_TOKEN,
        }
    return {
        "bot_id": bot.id,
        "bot_name": bot.display_name,
        "bot_type": bot.bot_type,
        "bot_token": bot.token,
        "bot_wa_from": bot.whatsapp_from,
    }


async def build_message_kwargs_for_user(
        user_or_id: User | int | ma.tg.types.User | ma.wa.types.User
):
    if isinstance(user_or_id, int):
        user = await User.get_by_id(user_or_id)
    else:
        user = user_or_id

    if isinstance(user, User):
        return {
            "tg_chat_id": user.chat_id,
            "wa_phone": user.wa_phone,
            "user_id": user.id,
            "user_name": user.name,
        }

    if isinstance(user, ma.tg.types.User):
        return {
            "tg_chat_id": user.id,
            "user_name": user.mention or user.id
        }

    if isinstance(user, ma.wa.types.User):
        return {
            "wa_phone": user.phone_number,
            "user_name": user.name or user.phone_number,
        }


async def build_message_kwargs_for_bot_user(
        bot: ClientBot | Literal["service", "admin"],
        user_or_id: User | int | str | ma.tg.types.User | ma.wa.types.User
):
    return {
        **build_message_kwargs_for_bot(bot),
        **await build_message_kwargs_for_user(user_or_id),
    }


async def _send_to_kafka(topic, key, value, logger, debug_data):
    value_to_send = value.json().encode()
    if DEBUG:
        if (
                value.sending_data.get("content_type") == "document"
                and value.sending_data.get("document")
        ):
            value.sending_data["document"] = {
                "body": "Some large document replacement by this text for debug only..."
            }
        logger.debug(
            "build_and_send_bot_message: writing to Kafka", debug_data, {
                "topic": topic,
                "key": key,
                "kafka_value": value
            }
        )
    await producer.p.send_and_wait(
        topic=topic,
        value=value_to_send,
        key=key.encode(),
    )


async def build_and_send_bot_message(
        bot_id: int | Literal["root", "admin", "service"],
        bot_name: str,
        bot_type: BotTypeLiteral,
        bot_token: str,
        *,
        bot_wa_from: str | None = None,
        tg_chat_id: int | None = None,
        wa_phone: str | None = None,
        user_id: int | None = None,
        user_name: str | None = None,
        content_type: str,
        text: str | None = None,
        media: MediaObject | bool | None = None,
        content: dict | str | None = None,
        keyboard: ma.Keyboard | None = None,
        logger: JSONLogger | None = None,
        no_error: bool = False,
):
    message_data = dict(locals())
    del message_data["logger"]
    debug_data = {
        "message_data": message_data
    }

    if not logger:
        logger = JSONLogger(f"kafka.producer")

    if (
            (bot_type == "telegram" and not tg_chat_id) or
            (bot_type == "whatsapp" and not wa_phone)
    ):
        if DEBUG:
            logger.debug(
                "build_and_send_bot_message: no tg_chat_id or wa_phone, returning...",
                debug_data
            )
        return

    if bot_type == "telegram":
        if content_type == "image":
            content_type = "photo"
        if content_type == "document":
            content_type = "document"

    sending_data: dict[str, Any] = {
        "content_type": content_type,
        "text": text,
    }
    if media:
        if bot_type == "telegram":
            if (
                    content_type == "document" and
                    isinstance(content, dict) and
                    not isinstance(media, MediaObject)
            ):
                if "document" in content:
                    # Стара логіка з document body
                    sending_data[content_type] = dict(
                        body=content["document"],
                        filename=content["filename"],
                        caption=content.get("caption", ""),
                    )
                elif "pdf_media_id" in content:
                    # Нова логіка з pdf_media_id
                    sending_data[content_type] = dict(
                        pdf_media_id=content["pdf_media_id"],
                        filename=content["filename"],
                        caption=content.get("caption", ""),
                    )
            elif (
                    content_type == "photo" and
                    isinstance(media, bool) and
                    isinstance(content, dict) and
                    content.get("url")
            ):
                sending_data[content_type] = content.get("url")
                sending_data["filename"] = content.get("caption")
                sending_data["caption"] = content.get("caption")
            elif isinstance(media, MediaObject):
                sending_data[content_type] = dict(
                    path_or_bytesio=media.file_path,
                    filename=media.original_file_name or media.file_name,
                )
        elif bot_type == "whatsapp":
            if isinstance(media, MediaObject):
                sending_data[content_type] = media.url
                if content_type == "document":
                    sending_data[
                        "filename"] = media.original_file_name or media.file_name
            elif isinstance(media, bool) and isinstance(content, dict):
                if content.get("url"):
                    sending_data[content_type] = content.get("url")
                    sending_data["filename"] = content.get("filename")
                elif content.get("pdf_media_id"):
                    # Для WhatsApp передаємо pdf_media_id для обробки в worker'і
                    sending_data["pdf_media_id"] = content["pdf_media_id"]
                    sending_data["filename"] = content.get("filename")
                elif content.get("document"):
                    # Стара логіка з document ID
                    sending_data[content_type] = content.get("document")
                    sending_data["filename"] = content.get("filename")

    elif content:
        sending_data[content_type] = content

    if keyboard:
        sending_data["keyboard"] = keyboard.to_python()

    if bot_type == "telegram":
        topic = TELEGRAM_REGULAR_TOPIC
        key = f"{bot_token}-{tg_chat_id}"
        value = TelegramMessageValue(
            bot_id=bot_id,
            bot_name=bot_name,
            bot_token=bot_token,
            user_id=user_id,
            user_name=user_name,
            chat_id=tg_chat_id,
            sending_data=sending_data,
        )
        await _send_to_kafka(topic, key, value, logger, debug_data)
        return
    elif bot_type == "whatsapp":
        topic = WHATSAPP_REGULAR_TOPIC
        key = f"{bot_wa_from}-{wa_phone}"  # однаковий для всіх частин, це ОК
        # --- WhatsApp: якщо текст довгий, розбиваємо на частини, меню лише в
        # останній ---
        if text and len(text) > WHATSAPP_MESSAGE_LIMIT:
            from aiogram.utils.parts import safe_split_text
            import json
            # Розбиваємо всі частини по 4096, але останню з меню — по 2048
            parts = safe_split_text(text, WHATSAPP_MESSAGE_LIMIT, "\n")
            menu_length = 0
            if keyboard:
                menu_length = len(json.dumps(keyboard.to_python(), ensure_ascii=False))
                # Перевіряємо, чи остання частина + меню не перевищує
                # WHATSAPP_MENU_SAFE_LIMIT
                last_part = parts[-1]
                if len(last_part) + menu_length > WHATSAPP_MENU_SAFE_LIMIT:
                    allowed = WHATSAPP_MENU_SAFE_LIMIT - menu_length
                    if allowed > 0:
                        extra_parts = safe_split_text(last_part, allowed, "\n")
                        parts = parts[:-1] + extra_parts
                    else:
                        # Меню не поміститься навіть без тексту
                        logger.debug(
                            f"WhatsApp menu too large: menu_length={menu_length}, "
                            f"cannot send menu even without text. Menu not sent.",
                            {
                                "menu_length": menu_length, "user_id": user_id,
                                "wa_phone": wa_phone, "bot_id": bot_id
                            }
                        )
            for i, part in enumerate(parts):
                part_sending_data = dict(sending_data)
                part_sending_data["text"] = part
                if keyboard:
                    if i == len(parts) - 1:
                        # Додаємо меню лише якщо поміщається у WHATSAPP_MENU_SAFE_LIMIT
                        if len(part) + menu_length <= WHATSAPP_MENU_SAFE_LIMIT:
                            part_sending_data["keyboard"] = keyboard.to_python()
                        else:
                            part_sending_data.pop("keyboard", None)
                    else:
                        part_sending_data.pop("keyboard", None)
                value = WhatsappMessageValue(
                    bot_id=int(bot_id),
                    bot_name=bot_name,
                    bot_token=bot_token,
                    bot_from=bot_wa_from,
                    user_id=user_id,
                    user_name=user_name,
                    wa_phone=wa_phone,
                    sending_data=part_sending_data,
                )
                await _send_to_kafka(topic, key, value, logger, debug_data)
            return
        # --- якщо текст не довгий, відправляємо як було ---
        value = WhatsappMessageValue(
            bot_id=int(bot_id),
            bot_name=bot_name,
            bot_token=bot_token,
            bot_from=bot_wa_from,
            user_id=user_id,
            user_name=user_name,
            wa_phone=wa_phone,
            sending_data=sending_data,
        )
        await _send_to_kafka(topic, key, value, logger, debug_data)
        return
    else:
        logger.debug(
            "build_and_send_bot_message: Unknown bot type, returning...", debug_data
        )
        return


async def add_task_for_action(
        tasks: list[Task],
        logger: JSONLogger = None,
        no_error: bool = False,
):
    if not logger:
        logger = JSONLogger("kafka.producer")

    debug_data = {"len_tasks": len(tasks)}

    if DEBUG:
        logger.debug("add_tasks_for_action: Start", debug_data)

    result = []

    try:
        futures = []
        for task in tasks:
            value = TaskValue(
                id=task.id,
                type=task.type.value,
                status=task.status.value,
                type_task=task.type_task.value,
                ai_model=task.ai_model.value,
                group_id=task.group_id,
                user_id=task.user_id,
                object_id=task.object_id,
                prompt=task.prompt,
                name=task.json_data.get("name"),
                description=task.json_data.get("description"),
            )
            result.append(value)
            futures.append(
                await producer.p.send(
                    topic=TASK_REGULAR_TOPIC,
                    value=value.json().encode(),
                    # key=f"task-{task.type}-{task.type_task}-{
                    # task.object_id}".encode(),
                    # not need key because we not need ordered execute
                )
            )

        await asyncio.gather(*futures)

        logger.debug(
            "add_tasks_for_action: Successfully sent to Kafka", debug_data, {
                "results": result
            }
        )
    except Exception as e:
        logger.error(
            "add_task_for_action: Error",
            e, debug_data,
        )

        if not no_error:
            raise

    return result


async def add_webhook_event_for_action(
        data: schemas.WebhookMessageValue,
        logger: JSONLogger = None,
        no_error: bool = False,
):

    if not logger:
        logger = JSONLogger("kafka.producer")

    if DEBUG:
        logger.debug("add_webhook_event_for_action: Start", {"data": data})

    topic = WEBHOOK_REGULAR_TOPIC
    key = f"webhook-{data.webhook_id}-{data.data.entity}-{data.data.entity_id}"

    try:
        await producer.p.send_and_wait(
            topic=topic,
            value=data.json().encode(),
            key=str(key).encode(),
        )
    except Exception as e:
        logger.error("add_webhook_event_for_action: Error", e, data)
        if not no_error:
            raise
    else:
        logger.debug("add_webhook_event_for_action: Successfully sent to Kafka", data)

    return data


async def add_admin_notification_for_action(
        admin_notification: SystemNotification,
        group: Group | None = None,
        logger: JSONLogger = None,
        no_error: bool = False,
):
    if not group:
        group = await Group.get(admin_notification.group_id)

    if not logger:
        logger = JSONLogger("kafka.producer")

    debug_data = {"admin_notification": admin_notification.as_dict()}

    if DEBUG:
        logger.debug("add_admin_notification_for_action: Start", debug_data)

    subject = f"{group.name}: {admin_notification.title}"

    results_email = await add_email_notifications_for_action(
        LOC7_EMAIL_LOGIN,
        LOC7_EMAIL_PASSWORD,
        admin_notification.scope,
        {"profile_id": admin_notification.group_id},
        SendEmailData(
            subject=subject,
            body="",
            html=admin_notification.content.replace("\n", "<br>"),
            from_name="7Loc",
        ),
        logger=logger,
        no_error=no_error,
    )

    return results_email


async def add_user_notification_for_action(
        user_notification: SystemNotification,
        user: User | None = None,
        group: Group | None = None,
        logger: JSONLogger = None,
        **kwargs,
):
    """Відправляє сповіщення користувачу через доступні канали (push, telegram,
    email)"""
    if not user and user_notification.recipient_id:
        user = await User.get_by_id(user_notification.recipient_id)

    if not user:
        if not logger:
            logger = JSONLogger("kafka.producer")
        logger.error(
            "add_user_notification_for_action: Error - recipient_id is not set",
            {"user_notification": user_notification.as_dict()}
        )
        return None

    if not group:
        group = await Group.get(user_notification.group_id)

    if not logger:
        logger = JSONLogger("kafka.producer")

    bot = await ClientBot.get(group_id=group.id)

    debug_data = {"user_notification": user_notification.as_dict()}

    if DEBUG:
        logger.debug("add_user_notification_for_action: Start", debug_data)

    results = []

    if bot:
        user_bot_activity = await UserClientBotActivity.get(user, bot, create=False)
        if not user_bot_activity or not user_bot_activity.is_entered_bot:
            logger.debug(
                f"add_user_notification_for_action: user has not entered bot, "
                f"skipping Kafka message",
                debug_data
            )
            # Пропускаємо відправку
            pass
        else:
            try:
                text = user_notification.content

                if bot.bot_type == "telegram":
                    title_html = f"<b>{user_notification.title}</b>"
                    if not user_notification.content.strip().startswith(title_html):
                        text = f"{title_html}\n\n{user_notification.content}"
                elif bot.bot_type == "whatsapp":
                    content_md = html_to_markdown(user_notification.content).strip()
                    title_md = f"*{user_notification.title}*"
                    if not content_md.startswith(title_md):
                        text = f"{title_md}\n\n{content_md}"
                    else:
                        text = content_md

                await build_and_send_bot_message(
                    bot.id, bot.display_name,
                    bot.bot_type, bot.token,
                    bot_wa_from=bot.whatsapp_from,
                    tg_chat_id=user.chat_id,
                    wa_phone=user.wa_phone,
                    user_id=user.id,
                    user_name=user.name,
                    content_type="text",
                    text=text,
                    keyboard=kwargs.get("keyboard") if kwargs and kwargs.get(
                        "keyboard"
                    ) else None,
                    logger=logger,
                )

                results.append(
                    {
                        "bot_type": bot.bot_type,
                        "user_id": user.id,
                    }
                )
            except Exception as e:
                logger.error(
                    f"add_user_notification_for_action: Error sending notification "
                    f"via messenger",
                    e, debug_data
                )

    # Перевіряємо, чи є email_to в kwargs, або використовуємо email користувача
    email_to = kwargs.get("email_to") if kwargs and kwargs.get("email_to") else None

    if email_to or (user.email and user.is_confirmed_email):
        try:
            # Якщо є email_to, використовуємо його, інакше - email користувача
            email_data = {"emails": [email_to]} if email_to else {"user_id": user.id}

            subject = user_notification.title if kwargs and kwargs.get(
                "html"
            ) else f"{group.name}: {user_notification.title}"

            email_results = await add_email_notifications_for_action(
                LOC7_EMAIL_LOGIN,
                LOC7_EMAIL_PASSWORD,
                user_notification.scope,
                email_data,
                SendEmailData(
                    subject=subject,
                    body="",
                    html=kwargs.get("html") if kwargs and kwargs.get(
                        "html"
                    ) else user_notification.content.replace("\n", "<br>"),
                    from_name=kwargs.get("from_name") if kwargs and kwargs.get(
                        "from_name"
                    ) else group.name,
                    attachments=kwargs.get("attachments") if kwargs and kwargs.get(
                        "attachments"
                    ) else None,
                ),
                logger=logger,
                no_error=True,
            )

            if email_results:
                results.extend(email_results)
        except Exception as e:
            logger.error(
                "add_user_notification_for_action: Error sending email notification",
                e, debug_data
            )

    return results


async def add_mailing_message_for_action(
        data: schemas.MailingMessageValue,
        channel: schemas.MailingChannelTypeEnum,
        mailing_message: schemas.MailingMessageQueryItem,
        logger: JSONLogger = None,
        no_error: bool = False,
):

    if not logger:
        logger = JSONLogger("kafka.producer")

    if DEBUG:
        logger.debug("add_mailing_message_for_action: Start", {"data": data})

    bot_type = mailing_message.bot_type if mailing_message.bot_type else None
    topic = None
    if channel == schemas.MailingChannelTypeEnum.BOT.value:
        if not bot_type:
            raise MailingUnknownChannelError()
        if bot_type == "telegram":
            topic = MAILING_TELEGRAM_REGULAR_TOPIC
        elif bot_type == "whatsapp":
            topic = MAILING_WHATSAPP_REGULAR_TOPIC
    elif channel == schemas.MailingChannelTypeEnum.EMAIL.value:
        topic = MAILING_EMAIL_REGULAR_TOPIC

    if not topic:
        raise MailingUnknownChannelError()

    key = f"mailing-{topic}-{data.mailing_message_id}"

    if mailing_message.bot_type and channel == "bot":
        if bot_type == "telegram":
            data = MailingTelegramMessageValue(
                bot_id=mailing_message.bot_id,
                bot_name=mailing_message.display_name,
                bot_token=mailing_message.token,
                user_id=mailing_message.user_id,
                user_name=mailing_message.user_name,
                chat_id=mailing_message.chat_id,
                sending_data=data.dict(),
            )
        elif bot_type == "whatsapp":
            data = MailingWhatsappMessageValue(
                bot_id=int(mailing_message.bot_id),
                bot_name=mailing_message.display_name,
                bot_token=mailing_message.token,
                bot_from=mailing_message.whatsapp_from,
                user_id=mailing_message.user_id,
                user_name=mailing_message.user_name,
                wa_phone=mailing_message.phone,
                sending_data=data.dict(),
            )

    if channel == "email":
        data = EmailMailingMessageValue(
            mailing_message_id=mailing_message.id,
            sender=LOC7_EMAIL_LOGIN,
            sender_password=LOC7_EMAIL_PASSWORD,
            mailing_id=mailing_message.mailing_id,
            mailing_message=data.mailing_message,
            subject=data.subject or "7loc",
            destination=mailing_message.email,
            from_name=mailing_message.profile_name,
        )

    try:
        coro = await producer.p.send(
            topic=topic,
            value=data.json().encode(),
            key=str(key).encode(),
        )
        logger.debug("add_mailing_message_for_action: Successfully sent to Kafka", data)

        return coro
    except Exception as e:
        logger.error("add_mailing_message_for_action: Error", e, data)
        if not no_error:
            raise


async def add_order_status_notification(
        store_order_id: int,
        status: str,
        initiated_by: str,
        order_shipping_status_id: int | None = None,
        invoice_id: int | None = None,
        menu_in_store_id: int | None = None,
        need_process_loyalty: bool = False,
        is_cancel: bool = False,
        is_payed_new_order: bool = False,
        is_payment_error: bool = False,
        comment: str | None = None,
        source: str | None = None,
        ignore_session_id: int | None = None,
        lang: str | None = None,
        logger: JSONLogger = None,
        no_error: bool = False,
):
    """Відправляє повідомлення про зміну статусу замовлення в Kafka для асинхронної
    обробки."""
    return await add_notification_message(
        notification_type=NotificationMessageType.ORDER_STATUS,
        store_order_id=store_order_id,
        status=status,
        initiated_by=initiated_by,
        order_shipping_status_id=order_shipping_status_id,
        invoice_id=invoice_id,
        menu_in_store_id=menu_in_store_id,
        need_process_loyalty=need_process_loyalty,
        is_cancel=is_cancel,
        is_payed_new_order=is_payed_new_order,
        is_payment_error=is_payment_error,
        comment=comment,
        source=source,
        ignore_session_id=ignore_session_id,
        lang=lang,
        logger=logger,
        no_error=no_error,
    )


async def add_invoice_payment_notification(
        group_id: int,
        brand_id: int | None,
        invoice_id: int,
        invoice_user_id: int | None = None,
        is_full_bonuses_payment: bool = False,
        terminal_key: str | None = None,
        lang: str | None = None,
        menu_in_store_id: int | None = None,
        logger: JSONLogger = None,
        no_error: bool = False,
):
    """Відправляє повідомлення про успішну оплату рахунку в Kafka для асинхронної
    обробки."""
    return await add_notification_message(
        notification_type=NotificationMessageType.INVOICE_PAYMENT,
        group_id=group_id,
        brand_id=brand_id,
        invoice_id=invoice_id,
        invoice_user_id=invoice_user_id,
        is_full_bonuses_payment=is_full_bonuses_payment,
        terminal_key=terminal_key,
        lang=lang,
        menu_in_store_id=menu_in_store_id,
        logger=logger,
        no_error=no_error,
    )


async def add_notification_message(
        notification_type: NotificationMessageType,
        logger: JSONLogger = None,
        no_error: bool = False,
        **kwargs,
):
    """Універсальна функція для відправки повідомлень в Kafka (замовлення, рахунки,
    тощо)."""
    notification_data = dict(kwargs)
    notification_data["notification_type"] = notification_type
    debug_data = {"notification_data": notification_data}

    if not logger:
        logger = JSONLogger("kafka.producer.notification")

    if DEBUG:
        logger.debug(
            f"add_notification_message ({notification_type}): Start", debug_data
        )

    try:
        value = NotificationMessageValue(
            notification_type=notification_type,
            **kwargs
        )

        # Визначаємо ключ залежно від типу повідомлення
        if notification_type == NotificationMessageType.ORDER_STATUS:
            key = f"order-{kwargs.get('store_order_id', 'unknown')}".encode()
        elif notification_type == NotificationMessageType.INVOICE_PAYMENT:
            key = f"invoice-{kwargs.get('invoice_id', 'unknown')}".encode()
        else:
            key = f"notification-{notification_type}".encode()

        # Відправка в універсальний топік для сповіщень
        await producer.p.send_and_wait(
            topic=UNIVERSAL_NOTIFICATION_REGULAR_TOPIC,
            # Використовується універсальний топік
            value=value.json().encode(),
            key=key,
        )

        logger.debug(
            f"add_notification_message ({notification_type}): Successfully sent to "
            f"Kafka",
            debug_data, {"value": value}
        )
        return value
    except Exception as e:
        logger.error(
            f"add_notification_message ({notification_type}): Error",
            e, debug_data,
        )
        if not no_error:
            raise
