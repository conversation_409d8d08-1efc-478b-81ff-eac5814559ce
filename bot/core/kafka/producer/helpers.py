from typing import Protocol

from async_firebase.messages import (
    APNSConfig, APNSPayload, AndroidConfig, Aps, ApsAlert, WebpushConfig,
    WebpushFCMOptions, WebpushNotification,
)

import schemas
from config import CRM_ICON_PATH, P4S_API_URL
from utils.numbers import format_sum


class ItemType(Protocol):
    name: str
    quantity: int
    final_sum: int


def add_items_text(text: str, items: list[ItemType], lang: str, currency: str):
    for i, item in enumerate(items):
        line = (
            f"\n{item.name} x{item.quantity}  "
            f"{format_sum(round(item.final_sum / 100, 2), lang, currency=currency)}"
        )
        if len(text) + len(line) + (4 if i == len(items) - 1 else 0) < 1024:
            text += line
        else:
            text += "\n..."
            break

    return text


def build_fcm_message(
        action: str,
        obj_id: int | str,
        tag: str,
        title: str | None = None,
        body: str | None = None,
        obj_id_field: str | None = None,
        delete_notification: bool = False,
        apns_priority: str | None = None,
        apns_push_type: str = "alert",
        android_priority: str = "high",
        add_data_texts: bool = True,
        link: str | None = None,
        **extra_data: object,
):
    data = {**extra_data}
    if not obj_id_field:
        obj_id_field = f"{action}_id"
    data["action"] = action
    data["tag"] = tag
    data[obj_id_field] = str(obj_id)
    if delete_notification:
        data["delete_notification"] = tag

    if add_data_texts:
        if title:
            data["notification_title"] = title
        if body:
            data["notification_body"] = body

    if link:
        data["link"] = link

    return schemas.FCMMessage(
        data=data,
        android=AndroidConfig(
            priority=android_priority,
        ),
        apns=APNSConfig(
            headers={
                "apns-collapse-id": tag,
                "apns-priority": apns_priority,
                "apns-push-type": apns_push_type,
            },
            payload=APNSPayload(
                aps=Aps(
                    alert=ApsAlert(
                        title=title,
                        body=body,
                    ) if title else None,
                    content_available=True if not title else None,
                ),
            )
        ),
        webpush=WebpushConfig(
            notification=WebpushNotification(
                title=title,
                body=body,
                tag=tag,
                icon=f"{P4S_API_URL}/{CRM_ICON_PATH}",
            ),
            fcm_options=WebpushFCMOptions(
                link=link,
            )
        )
    )
