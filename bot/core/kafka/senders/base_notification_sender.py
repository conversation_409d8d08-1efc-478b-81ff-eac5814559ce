import asyncio
from abc import ABC, abstractmethod
from typing import Any, <PERSON>wai<PERSON>, <PERSON>ric, NoReturn

import aioredis
from typing_extensions import TypeVar

from core.kafka.constants import MAX_RETRY_COUNT
from core.kafka.functions import save_retry_message
from loggers import JSONLogger
from schemas import BaseKafkaMessageValue

ValueT = TypeVar(
    "ValueT",
    bound=BaseKafkaMessageValue,
    default=BaseKafkaMessageValue,
)


class KafkaNotificationSender(ABC, Generic[ValueT]):
    PREFIX: str
    LIMITED_OBJ: str

    def __init__(
            self, redis: aioredis.Redis, data: ValueT,
            save_retry_if_limited_exists: bool = True,
    ):
        self.redis = redis
        self.data = data
        self.save_retry_if_limited_exists = save_retry_if_limited_exists

        self.logger = JSONLogger(
            "kafka.consumer",
            self.PREFIX,
            f"Notification",
            {
                "data": self.data,
                "save_retry_if_limited_exists": self.save_retry_if_limited_exists,
            }
        )

    @abstractmethod
    def get_sender_receiver_info(self) -> str | Awaitable[str]:
        raise NotImplementedError

    @abstractmethod
    def get_limited_obj_id(self) -> str | Awaitable[str]:
        raise NotImplementedError

    @abstractmethod
    async def send(self) -> Any:
        raise NotImplementedError

    def handle_error(
            self, error: Exception,
            reason: str | None = None,
    ) -> Awaitable[NoReturn] | NoReturn:
        message = "FAILED"
        if reason:
            message += f":{reason}"
        self.logger.error(message, repr(error))

    async def process_retry(self, limited_obj_id: str, timeout: int, is_rate_limit: bool = False):
        # Для rate limit не накопичуємо retry_count - це не помилка повідомлення
        if not is_rate_limit:
            if self.data.retry_count >= MAX_RETRY_COUNT:
                self.logger.error(
                    "RETRY_LIMIT_EXCEEDED",
                    {
                        "retry_count": self.data.retry_count,
                        "max_retry_count": MAX_RETRY_COUNT,
                        "timeout": timeout,
                    }
                )
                # Відправляємо системне сповіщення адмінам
                from utils.platform_admins import send_message_to_platform_admins
                try:
                    await send_message_to_platform_admins(
                        f"🚨 РОЗСИЛКА ЗУПИНЕНА!\n\n"
                        f"Досягнуто максимум спроб повтору: {self.data.retry_count}/{MAX_RETRY_COUNT}\n"
                        f"Limited obj: {limited_obj_id}\n"
                        f"Timeout: {timeout}s\n"
                        f"Prefix: {self.PREFIX}"
                    )
                except Exception as e:
                    self.logger.error("Failed to send admin notification", repr(e))
                return False
            self.data.retry_count += 1
        
        retry_type = "RATE_LIMIT" if is_rate_limit else "RETRY_AFTER"
        self.logger.warning(
            retry_type,
            {
                "retry_count": self.data.retry_count,
                "timeout": timeout,
                "limited_obj_id": limited_obj_id,
                "is_rate_limit": is_rate_limit,
            }
        )
        
        await save_retry_message(
            self.redis, self.PREFIX,
            self.LIMITED_OBJ, limited_obj_id,
            self.data, timeout,
            lpush_message=self.data.retry_count > 1
        )
        return None

    async def start(self):
        sender_receiver_info = self.get_sender_receiver_info()
        if asyncio.iscoroutine(sender_receiver_info):
            sender_receiver_info = await sender_receiver_info

        self.logger.add_texts(sender_receiver_info)

        limited_obj_id = self.get_limited_obj_id()
        if asyncio.iscoroutine(limited_obj_id):
            limited_obj_id = await limited_obj_id

        self.logger.add_data(
            {
                "limited_obj_id": limited_obj_id,
            }
        )

        if self.save_retry_if_limited_exists and await save_retry_message(
                self.redis, self.PREFIX,
                self.LIMITED_OBJ, limited_obj_id,
                self.data, only_if_limited_messages_exists=True
        ):
            return False

        try:
            result = await self.send()
        except RetryNotificationAfter as error:
            await self.process_retry(limited_obj_id, error.timeout, error.is_rate_limit)
            return False
        except Exception as error:
            try:
                res = self.handle_error(error)
                if asyncio.iscoroutine(res):
                    await res
            except RetryNotificationAfter as error:
                await self.process_retry(limited_obj_id, error.timeout, error.is_rate_limit)
                return False
            # Для BotBlocked/UserDeactivated - повертаємо None (не критична помилка)
            # Для інших помилок - повертаємо False (критична помилка)
            from aiogram.utils.exceptions import BotBlocked, UserDeactivated
            if isinstance(error, (BotBlocked, UserDeactivated)):
                return None  # Не критична помилка - продовжуємо обробку
            return False  # Критична помилка - зупиняємо
        else:
            # Перевіряємо чи результат це BotBlocked/UserDeactivated Exception
            from aiogram.utils.exceptions import BotBlocked, UserDeactivated, Unauthorized
            if isinstance(result, (BotBlocked, UserDeactivated, Unauthorized)):
                # Обробляємо як помилку
                try:
                    res = self.handle_error(result)
                    if asyncio.iscoroutine(res):
                        await res
                except RetryNotificationAfter as error:
                    await self.process_retry(limited_obj_id, error.timeout, error.is_rate_limit)
                    return False
                return None  # Не критична помилка - продовжуємо обробку
            
            self.logger.debug(
                "SUCCESS", {
                    "result": result,
                }
            )

        return True

    def __call__(self):
        return self.start()

    def __await__(self):
        return self.start().__await__()


class RetryNotificationAfter(Exception):
    def __init__(self, timeout: int, is_rate_limit: bool = False):
        self.timeout = timeout
        self.is_rate_limit = is_rate_limit
