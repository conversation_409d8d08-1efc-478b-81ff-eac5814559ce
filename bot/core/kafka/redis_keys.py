def get_limited_messages_key(prefix: str, limited_obj: str, limited_obj_id: str | int):
    return ":".join([prefix, limited_obj, limited_obj_id, "limited_messages"])


def get_delayed_key():
    return "delayed_messages"


DELAYED_SEPARATOR = ":"


def get_delayed_item_key(prefix: str, obj_id: str | int):
    return DELAYED_SEPARATOR.join((prefix, obj_id))


def parse_delayed_item_key(key: str):
    return key.split(DELAYED_SEPARATOR, 1)
