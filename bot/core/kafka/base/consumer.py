import logging
from abc import ABC, ABCMeta, abstractmethod
from contextlib import suppress
from typing import Any, Generic, Type, TypeVar

import aioredis
from aiokafka import AIOKafkaConsumer, ConsumerRecord
from pydantic import BaseModel, ValidationError

from config import KAFKA_SERVER
from db import DBSession
from utils.platform_admins import send_message_to_platform_admins

ValueT = TypeVar("ValueT")

debugger_loger = logging.getLogger("debugger.kafka.consumer")
info_logger = logging.getLogger("info.kafka.consumer")
error_logger = logging.getLogger("error.kafka.consumer")


class BaseConsumer(ABC, Generic[ValueT]):
    TOPIC_BASE: str = ""
    VALUE_MODEL: Type[BaseModel] | None = None
    MULTIPLE_PARALLEL_COUNT = 1

    def __init_subclass__(cls, **kwargs):
        if not isinstance(cls.TOPIC_BASE, str):
            raise TypeError("TOPIC_BASE must be a string")
        if cls.VALUE_MODEL and not issubclass(cls.VALUE_MODEL, BaseModel):
            raise TypeError("VALUE_MODEL must be a subclass of BaseModel")
        super().__init_subclass__(**kwargs)

    def __init__(
            self,
            redis: aioredis.Redis,
            *__topics: str,
            group_id: str | None = None,
            consumer_params: dict | None = None,
    ):
        self.redis = redis  # redis client

        if __topics:
            self.topics = tuple(f"{self.TOPIC_BASE}-{topic}" for topic in __topics)
        else:
            self.topics = [self.TOPIC_BASE]

        if consumer_params is None:
            consumer_params = {}

        if "enable_auto_commit" not in consumer_params:
            consumer_params["enable_auto_commit"] = False
        if "bootstrap_servers" not in consumer_params:
            consumer_params["bootstrap_servers"] = KAFKA_SERVER

        if not group_id:
            group_id = self.topics[0]

        self.group_id = group_id

        self.consumer = AIOKafkaConsumer(
            *self.topics,
            group_id=group_id,
            **consumer_params,
        )

    async def on_message(self, message: ConsumerRecord):
        try:
            value = await self.validate_message_value(message)
            result = await self.process_message(message, value)
            if result is not False:
                await self.consumer.commit()
        except Exception as exception:
            result = await self.process_exception(message, exception)
            if result:
                try:
                    await self.consumer.commit()
                except Exception as commit_error:
                    error_logger.error(commit_error)

    async def consume(self):
        async for message in self.consumer:
            await self.on_message(message)

    async def start_consumer(self):
        await self.consumer.start()
        info_logger.info(f"Started consumer: {self.group_id}; {self.topics}")
        try:
            await self.consume()
        finally:
            await self.consumer.stop()
            info_logger.info(f"Stopped consumer: {self.group_id}; {self.topics}")

    @classmethod
    def multiple_tasks(cls, *args, **kwargs):
        return [
            cls(*args, **kwargs) for _ in
            range(cls.MULTIPLE_PARALLEL_COUNT)
        ]

    def __await__(self):
        return self.start_consumer().__await__()

    async def validate_message_value(self, message: ConsumerRecord) -> ValueT:
        if self.VALUE_MODEL:
            try:
                return self.VALUE_MODEL.parse_raw(message.value)
            except ValidationError as exception:
                await self.send_error(
                    "Message value validation error",
                    message,
                    exception,
                )
        else:
            return message.value.decode("utf-8")

    @abstractmethod
    async def process_message(self, message: ConsumerRecord, value: ValueT):
        """
        Method to process message
        @param message: consumer record
        @param value: message value validated with validate_message_value
        @return: Should commit.
        Self.consumer.commit() will be called if True returned
        """
        raise NotImplementedError

    async def process_exception(self, message: ConsumerRecord, exception: Exception):
        """
        Method to process exception
        @param message: consumer record
        @param exception: exception
        @return: Should commit.
        Self.consumer.commit() will be called if True returned
        """
        error_logger.error(
            f"An error occurred while processing message: {repr(exception)}",
            exc_info=True
        )
        await self.send_error(
            "Unknown error",
            message,
            exception,
        )
        return True

    async def send_error(
            self, error_message: str, message: ConsumerRecord,
            exception: Any | None = None
    ):
        with suppress():
            try:
                await send_message_to_platform_admins(
                    f"Kafka error: {error_message}\n"
                    f"Topics: {self.topics}"
                    f"Message: {message}\n"
                    f"Error: {str(exception)}"
                )
            except Exception as e:
                error_logger.error(
                    f"An error occurred while sending error to platform admins: "
                    f"{repr(e)}",
                    exc_info=True
                )
                await send_message_to_platform_admins(
                    "KAFKA Error occurred. An error occurred while sending error "
                    "details.\n"
                    f"Topics: {self.topics}"
                )


class BaseDBConsumer(BaseConsumer, metaclass=ABCMeta):
    async def on_message(self, message: ConsumerRecord):
        with DBSession():
            await super().on_message(message)
