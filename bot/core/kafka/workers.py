import time

import aioredis

from utils.processes_manager.background_worker import LoopBackgroundWorker
from .producer import KafkaProducer

from .redis_keys import get_delayed_key, parse_delayed_item_key
from .constants import LIMITED_SUFFIX


class DelayedMessagesWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "delayed-messages"
    DEFAULT_TIMEOUT = 1

    def __init__(
            self,
            redis: aioredis.Redis,
            producer: KafkaProducer,
            name: str | None = None,
            timeout: int | float | None = None,
    ):
        self.producer = producer
        self.redis = redis
        super().__init__(name, timeout)

    async def iteration(self):
        while delayed_items := await self.redis.zrangebyscore(
                get_delayed_key(),
                0, time.time(),
                start=0,
                num=100,
        ):
            for delayed_item in delayed_items:
                delayed_item_key = delayed_item.decode("utf-8")
                prefix, item_id = parse_delayed_item_key(delayed_item_key)
                limited_topic = f"{prefix}-{LIMITED_SUFFIX}"
                await self.producer.p.send_and_wait(
                    limited_topic,
                    value=item_id.encode(),
                    key=item_id.encode()
                )
                await self.redis.zrem(get_delayed_key(), delayed_item)
                self.debugger_logger.debug(
                    f"Added delayed message to {limited_topic}\nKey:{delayed_item_key}"
                )
