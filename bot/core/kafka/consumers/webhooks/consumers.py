from aiokafka import ConsumerRecord

from core.kafka.base.consumer import BaseDBConsumer
from core.kafka.constants import (
    WEBHOOK_LIMITED_TOPIC, WEBHOOK_PREFIX,
    WEBHOOK_REGULAR_TOPIC,
)
from core.kafka.functions import get_limited_message
from schemas import WebhookMessageValue
from .functions import process_webhook_event


class WebhookConsumer(BaseDBConsumer):
    TOPIC_BASE = WEBHOOK_REGULAR_TOPIC
    VALUE_MODEL = WebhookMessageValue

    async def process_message(
            self, message: ConsumerRecord, value: WebhookMessageValue
    ):
        await process_webhook_event(self.redis, WEBHOOK_PREFIX, value)
        return True


class WebhookLimitedConsumer(BaseDBConsumer):
    TOPIC_BASE = WEBHOOK_LIMITED_TOPIC

    async def process_message(self, message: ConsumerRecord, value: str):
        while message := await get_limited_message(
                self.redis, WE<PERSON>H<PERSON>OK_PREFIX, "webhook", value
        ):
            try:
                message_validated = WebhookMessageValue.parse_raw(message)
            except Exception as exception:
                await self.send_error(
                    "LIMITED Message value validation error",
                    message,
                    exception,
                )
                continue

            result = await process_webhook_event(
                self.redis, WEBHOOK_PREFIX, message_validated, False
            )
            if not result:
                break
        return True
