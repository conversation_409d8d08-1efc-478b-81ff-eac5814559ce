import uuid

import aioredis

import schemas
from core.admin_notification.service import create_system_notification
from core.kafka.constants import MAX_RETRY_COUNT
from core.kafka.functions import save_retry_message
from core.webhooks.exceptions import (
    WebhookExternalError, WebhookInternalError, WebhookRateLimitError,
)
from core.webhooks.functions import send_webhook_data
from db import crud
from db.models import Group, Webhook
from loggers import J<PERSON><PERSON>ogger
from schemas import SystemNotificationCategory, SystemNotificationType
from utils.date_time import utcnow
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f


async def process_webhook_event(
        redis: aioredis.Redis, prefix: str,
        data: schemas.WebhookMessageValue,
        save_retry_if_limited_exists: bool = True
):
    event_start_datetime = utcnow()

    logger = JSONLogger(
        "kafka.consumer", "process_webhook_event", data
    )
    logger.debug("SENDING")

    entity = data.data.entity
    entity_id = data.data.entity_id
    hook_id = data.hook_uuid

    webhook = await Webhook.get(data.webhook_id)
    group = await Group.get(webhook.group_id)
    journal_data = schemas.WebhookJournalDataSchema()
    journal_status = schemas.WebhookJournalStatusEnum.PROCESSED

    # checks if limited messages exist for push token and write to limited messages
    # if so
    if (not webhook.persistent_webhook and save_retry_if_limited_exists and await
    save_retry_message(
        redis, prefix,
        f"webhook", f"{hook_id}-{entity}-{entity_id}",
        data, only_if_limited_messages_exists=True
    )):
        return False

    try:
        result = await send_webhook_data(
            logger, data.webhook_id, data.data, data.event_uuid, group.lang
        )
        if (webhook.retries_count > 1 and webhook.last_sent_status ==
                schemas.WebhookSentStatusEnum.FAILED):
            await create_system_notification(
                "profile:edit",
                group_id=group.id, category=SystemNotificationCategory.WEBHOOK,
                type_notification=SystemNotificationType.GENERAL_NOTIFICATION,
                title="Webhook info",
                content=await f(
                    'admin webhooks recovery msg', group.lang,
                    details=f"\nEVENT_ID:"
                            f" {data.event_uuid}\nWEBHOOK_ID: "
                            f"{webhook.id}\nWEBHOOK_URL:"
                            f" {webhook.endpoint_url}"
                ),
                level=schemas.NotificationLevel.INFO,
            )
        await webhook.update(
            retries_count=0, last_sent_status=schemas.WebhookSentStatusEnum.SUCCESS,
            last_sent_datetime=utcnow(),
        )
        journal_data = result
    except WebhookRateLimitError as error:
        journal_status = schemas.WebhookJournalStatusEnum.FAILED

        if error.journal_data:
            journal_data = error.journal_data
        if data.retry_count < MAX_RETRY_COUNT:
            data.retry_count += 1
            await webhook.update(
                retries_count=webhook.retries_count + 1,
                last_sent_status=schemas.WebhookSentStatusEnum.FAILED,
                last_sent_datetime=utcnow(),
            )
            await save_retry_message(
                redis, prefix,
                f"webhook", f"{hook_id}-{entity}-{entity_id}",
                data, retry_after=int(error.message), lpush_message=data.retry_count > 1
            )

        return False
    except WebhookExternalError as error:
        journal_status = schemas.WebhookJournalStatusEnum.FAILED

        if error.journal_data:
            journal_data = error.journal_data
        if data.retry_count < MAX_RETRY_COUNT:
            data.retry_count += 1
            await webhook.update(
                retries_count=webhook.retries_count + 1,
                last_sent_status=schemas.WebhookSentStatusEnum.FAILED,
                last_sent_datetime=utcnow(),
            )
            await save_retry_message(
                redis, prefix,
                f"webhook", f"{hook_id}-{entity}-{entity_id}",
                data, retry_after=data.retry_count * 4,
                lpush_message=data.retry_count > 1
            )

            if data.retry_count == 1:
                await create_system_notification(
                    "profile:edit",
                    group_id=group.id, category=SystemNotificationCategory.WEBHOOK,
                    type_notification=SystemNotificationType.WEBHOOK_ERROR,
                    title="Webhook error",
                    content=await f(
                        "admin webhooks external error message", group.lang,
                        details=f"\nEVENT_ID: {data.event_uuid} "
                                f"\nWEBHOOK_ID: {webhook.id}"
                                f"\nWEBHOOK_URL: {webhook.endpoint_url}"
                        # f"ERROR: {error.message}"
                    )
                )

        return False
    except WebhookInternalError as error:
        journal_status = schemas.WebhookJournalStatusEnum.FAILED

        if error.journal_data:
            journal_data = error.journal_data
        await webhook.update(
            last_sent_status=schemas.WebhookSentStatusEnum.FAILED,
            last_sent_datetime=utcnow(),
        )
        group = await Group.get(webhook.group_id)
        if data.retry_count == 0:
            await create_system_notification(
                "profile:edit",
                group_id=group.id, category=SystemNotificationCategory.WEBHOOK,
                type_notification=SystemNotificationType.WEBHOOK_ERROR,
                title="Webhook error",
                content=f"\nEVENT_ID: {data.event_uuid}"
                        f"\nWEBHOOK_ID: {webhook.id}"
                        f"\nWEBHOOK_URL: {webhook.endpoint_url}"
            )

        adm_msg = "webhook internal error"
        adm_msg += f"\nevent_id:{data.event_uuid}"
        adm_msg += f"\nwebhook_id:{webhook.id}"
        adm_msg += f"\nprofile_id:{group.id}"
        adm_msg += f"\nerror:{error.message}"

        if data.retry_count == 0:
            await send_message_to_platform_admins(adm_msg)

        logger.error("FAILED", repr(error))

        return False
    except Exception as error:
        journal_status = schemas.WebhookJournalStatusEnum.FAILED

        await webhook.update(
            last_sent_status=schemas.WebhookSentStatusEnum.FAILED,
            last_sent_datetime=utcnow(),
        )

        if data.retry_count == 0:
            await create_system_notification(
                "profile:edit",
                group_id=group.id, category=SystemNotificationCategory.WEBHOOK,
                type_notification=SystemNotificationType.WEBHOOK_ERROR,
                title="Webhook error",
                content=f"\nEVENT_ID: {data.event_uuid}"
                        f"\nWEBHOOK_ID: {webhook.id}"
                        f"\nWEBHOOK_URL: {webhook.endpoint_url}"
            )

        adm_msg = "webhook internal error"
        adm_msg += f"\nevent_id:{data.event_uuid}"
        adm_msg += f"\nwebhook_id:{webhook.id}"
        adm_msg += f"\nprofile_id:{webhook.group_id}"
        adm_msg += f"\nerror:{str(error)}"

        if data.retry_count == 0:
            await send_message_to_platform_admins(adm_msg)

        logger.error("FAILED", repr(error))

        return False
    else:
        logger.debug(
            "SUCCESS", {
                "result": result,
            }
        )
    finally:
        journal_uuid = uuid.uuid4().hex
        journal = await crud.create_webhook_journal(
            entity=data.data.entity,
            entity_id=data.data.entity_id,
            action=data.data.action,
            webhook_id=data.webhook_id,
            journal_uuid=journal_uuid,
            event_created_datetime=data.event_created_datetime,
            event_start_datetime=event_start_datetime,
            event_end_datetime=utcnow(),
            status=journal_status,
            json_data=journal_data
        )
        await journal.update(
            event_start_datetime=event_start_datetime,
            event_end_datetime=utcnow(),
            status=journal_status, json_data=journal_data,
        )

    await webhook.update(
        last_sent_status=schemas.WebhookSentStatusEnum.SUCCESS,
        last_sent_datetime=utcnow(),
    )
    return True
