from aiokafka import ConsumerRecord

from config import CRM_HOST
from core.check.notifications_sender import CheckNotificationsSender
from core.incust.exceptions import IncustError
from core.invoice.notifications import send_payment_notifications
from core.kafka.base.consumer import BaseDBConsumer
from core.kafka.constants import (
    UNIVERSAL_NOTIFICATION_LIMITED_TOPIC, UNIVERSAL_NOTIFICATION_PREFIX,
    UNIVERSAL_NOTIFICATION_REGULAR_TOPIC,
)
from core.kafka.functions import get_limited_message
from core.kafka.producer.functions import add_push_notifications_for_action
from core.kafka.producer.helpers import add_items_text, build_fcm_message
from core.store.functions.order_to_schema import order_to_schema
from core.store.order.service import (
    process_loyalty,
    send_message_to_platform_admins,
)
from core.webhooks.functions import (
    add_webhook_event, get_webhook_action_by_order_status,
    prepare_data_for_order_webhook,
)
from db import crud
from db.models import (
    Brand, Group, Invoice, MenuInStore, OrderShippingStatus, Store, StoreOrder, User,
)
from loggers import J<PERSON><PERSON>ogger
from schemas import (
    AuthSourceEnum, NotificationMessageType, NotificationMessageValue,
    OrderShippingStatusEnum, StatusChangeInitiatedBy,
    WebhookEntityEnum,
    WebhookOrderDataSchema,
)
from service.store.notifications import add_order_service_bot_notifications
from utils.numbers import format_currency
from utils.text import f, fd


class NotificationConsumer(BaseDBConsumer):
    """Універсальний консюмер для обробки різних типів сповіщень (замовлення, рахунки, тощо)"""
    TOPIC_BASE = UNIVERSAL_NOTIFICATION_REGULAR_TOPIC
    VALUE_MODEL = NotificationMessageValue

    async def process_message(self, message: ConsumerRecord, value: NotificationMessageValue):
        """Обробляє універсальні сповіщення"""
        logger = JSONLogger("notification_consumer", {
            "notification_type": value.notification_type,
            "invoice_id": value.invoice_id,
            "store_order_id": value.store_order_id if value.store_order_id else None
        })
        
        logger.debug(f"Processing {value.notification_type} notification")

        match value.notification_type:
            case NotificationMessageType.ORDER_STATUS:
                return await self.process_order_status_notification(message, value, logger)
            case NotificationMessageType.INVOICE_PAYMENT:
                return await self.process_invoice_payment_notification(message, value, logger)
            case _:
                logger.error(f"Unknown notification type: {value.notification_type}")
                return True
    
    async def process_order_status_notification(
        self, 
        message: ConsumerRecord, 
        value: NotificationMessageValue,
        logger: JSONLogger
    ):
        """Обробляє сповіщення про зміну статусу замовлення"""
        store_order = await StoreOrder.get(value.store_order_id)
        if not store_order:
            logger.error(f"Order {value.store_order_id} not found")
            return True
            
        try:
            (
                bot, brand, group,
                menu_in_store, store,
                order_user,
            ) = await crud.get_order_data(store_order)
            logger.add_data(
                {
                    "bot": bot,
                    "brand": brand,
                    "group": group,
                    "menu_in_store": menu_in_store,
                    "store": store,
                    "order_user": order_user,
                }
            )

            if value.order_shipping_status_id:
                order_shipping_status = await OrderShippingStatus.get(value.order_shipping_status_id)
            else:
                order_shipping_status = await crud.get_last_order_status(store_order.id)

            if value.invoice_id:
                invoice = await Invoice.get(value.invoice_id)
            elif store_order.invoice_id:
                invoice = await Invoice.get(store_order.invoice_id)
            else:
                invoice = None
                
            if value.menu_in_store_id:
                menu_in_store = await MenuInStore.get(value.menu_in_store_id)
            elif store_order.menu_in_store_id:
                menu_in_store = await MenuInStore.get(store_order.menu_in_store_id)
            else:
                menu_in_store = None

            # Обробка лояльності (якщо потрібно)
            if value.need_process_loyalty or value.is_cancel:
                try:
                    await process_loyalty(store_order, value.is_cancel)
                    logger.debug(f"Loyalty processed for order {value.store_order_id}")
                except IncustError as ex:
                    logger.error(f"Error processing loyalty: {str(ex)}")

            if not (store_order and order_shipping_status and brand and 
                    group and store and order_user):
                logger.error(
                    "Missing required objects for order status notification",
                    {
                        "store_order": bool(store_order),
                        "order_shipping_status": bool(order_shipping_status),
                        "bot": bool(bot),
                        "brand": bool(brand),
                        "group": bool(group),
                        "store": bool(store),
                        "order_user": bool(order_user),
                    }
                )
                return True
                
            # Відправка сповіщень
            try:
                await CheckNotificationsSender(
                    value.lang if value.lang else order_user.lang, brand, group,
                    order_user, bot, store,
                    store_order, order_shipping_status,
                    invoice, menu_in_store,
                    need_send_coupons_to_bot=False  # value.need_process_loyalty
                ).start()
                logger.debug("CheckNotificationsSender completed successfully")
            except Exception as err:
                error_data = {
                    "order_id": value.store_order_id,
                    "brand_id": brand.id if brand else "",
                    "store_id": store.id if store else "",
                    "group_id": group.id if group else "",
                    "bot_id": bot.id if bot else "",
                    "user_id": order_user.id if order_user else "",
                }
                error_text = (
                    f"CheckNotificationsSender: ERROR\n"
                    f"{error_data}\n"
                    f"{str(err)}"
                )
                await send_message_to_platform_admins(error_text)
                logger.error("Error in CheckNotificationsSender", err)

            if value.status != "new":
                try:
                    order_schema = await order_to_schema(store_order, group.lang)
                    webhook_data = await prepare_data_for_order_webhook(
                        order_schema, store_order.timezone, group.id,
                    )
                    webhook_action = get_webhook_action_by_order_status(
                        value.status, value.is_payed_new_order
                    )
                    await add_webhook_event(
                        entity=WebhookEntityEnum.ORDER,
                        entity_id=store_order.id,
                        action=webhook_action,
                        group_id=group.id,
                        data=webhook_data.dict(),
                        data_type=WebhookOrderDataSchema,
                    )
                    logger.debug(f"Webhook sent for order {value.store_order_id}")
                except Exception as err:
                    logger.error(f"Error sending webhook: {str(err)}")

            try:
                await add_order_service_bot_notifications(
                    store_order,
                    order_shipping_status,
                    group, store, order_user,
                    bot.id if bot else None,
                    None, 
                    is_payed_new_order=value.is_payed_new_order,
                    is_payment_error=value.is_payment_error,
                )
                logger.debug(f"Service bot notifications sent for order {value.store_order_id}")
            except Exception as err:
                logger.error(f"Error sending service bot notifications: {str(err)}")

            try:
                await send_order_push_notifications(
                    store_order,
                    value.status, 
                    value.initiated_by,
                    group, order_user,
                    ignore_session_id=value.ignore_session_id
                )
                logger.debug(f"Push notifications sent for order {value.store_order_id}")
            except Exception as err:
                logger.error(f"Error sending push notifications: {str(err)}")
                
            logger.debug("Order status notification processing completed successfully")
            
        except Exception as err:
            error_data = {
                "store_order_id": value.store_order_id,
                "status": value.status,
                "initiated_by": value.initiated_by,
            }
            error_text = f"Error processing order status notification: {str(err)}\n{error_data}"
            logger.error(error_text, err)
            await send_message_to_platform_admins(error_text)
            
        return True
    
    async def process_invoice_payment_notification(
        self, 
        message: ConsumerRecord, 
        value: NotificationMessageValue,
        logger: JSONLogger
    ):
        """Обробляє сповіщення про успішну оплату рахунку"""
        try:
            # Отримуємо необхідні об'єкти
            group = await Group.get(value.group_id) if value.group_id else None
            brand = await Brand.get(value.brand_id) if value.brand_id else None
            invoice = await Invoice.get(value.invoice_id) if value.invoice_id else None
            invoice_user = await User.get_by_id(value.invoice_user_id) if value.invoice_user_id else None
            
            if not (group and invoice):
                logger.error(
                    "Missing required objects for invoice payment notification",
                    {
                        "group": bool(group),
                        "invoice": bool(invoice),
                        "invoice_id": value.invoice_id,
                        "group_id": value.group_id,
                    }
                )
                return True
            
            logger.debug(
                "Processing invoice payment notification",
                {
                    "invoice_id": invoice.id,
                    "group_id": group.id,
                    "brand_id": brand.id if brand else None,
                    "user_id": invoice_user.id if invoice_user else None,
                }
            )
            
            # Викликаємо оригінальну функцію send_payment_notifications
            await send_payment_notifications(
                group=group,
                brand=brand,
                invoice=invoice,
                invoice_user=invoice_user,
                is_full_bonuses_payment=value.is_full_bonuses_payment,
                terminal_key=value.terminal_key,
                lang=value.lang,
            )
            
            logger.debug("Invoice payment notification processing completed successfully")
            
        except Exception as err:
            error_data = {
                "invoice_id": value.invoice_id,
                "group_id": value.group_id,
                "brand_id": value.brand_id,
            }
            error_text = f"Error processing invoice payment notification: {str(err)}\n{error_data}"
            logger.error(error_text, err)
            await send_message_to_platform_admins(error_text)
            
        return True


class UniversalLimitedConsumer(BaseDBConsumer):
    """Обробляє обмежені універсальні сповіщення (замовлення, рахунки, тощо)"""
    TOPIC_BASE = UNIVERSAL_NOTIFICATION_LIMITED_TOPIC

    async def process_message(self, message: ConsumerRecord, value: str):
        """Обробляє обмежені сповіщення"""
        while message := await get_limited_message(
                self.redis, UNIVERSAL_NOTIFICATION_PREFIX, "notification", value
        ):
            try:
                message_validated = NotificationMessageValue.parse_raw(message)
            except Exception as exception:
                await self.send_error(
                    "LIMITED Message value validation error",
                    message,
                    exception,
                )
                continue

            consumer = NotificationConsumer(self.redis)
            result = await consumer.process_message(message, message_validated)
            if not result:
                break
                
        return True


async def send_order_push_notifications(
        order: StoreOrder,
        status: str,
        initiated_by: StatusChangeInitiatedBy,
        group: Group,
        order_user: User,
        ignore_session_id: int | None = None,
):
    store = await Store.get(order.store_id)
    order_shipment = await crud.get_order_shipment(order.id)

    shipment_name = order_shipment.name or await f(
        f"store {order_shipment.base_type} text", group.lang,
    )

    async def get_message(user: User):
        is_processed = status not in (
            OrderShippingStatusEnum.OPEN_UNCONFIRMED.value,
            OrderShippingStatusEnum.PAYED.value,
        )

        texts = await fd(
            {
                "title": {
                    "variable": (
                        "crm order processed notification title"
                        if is_processed else
                        f"crm order {status} notification title"
                    ),
                    "text_kwargs": {
                        "delivery_name": shipment_name,
                        "order_id": order.id,
                        "total_sum": format_currency(
                            round(order.total_sum / 100, 2),
                            order.currency, group.lang, group.country
                        )
                    }
                },
                "body": {
                    "variable": f"crm order notification body",
                    "text_kwargs": {
                        "store_name": store.name,
                        "user_name": order_user.name,
                    }
                },
            },
            user.lang,
        )

        title = texts["title"]
        body = texts["body"] + "\n"

        order_products = await crud.get_order_products(order.id)
        body = add_items_text(body, order_products, group.lang, group.currency)

        return build_fcm_message(
            "order",
            order.id,
            order.crm_tag,
            title,
            body,
            delete_notification=is_processed,
            apns_priority="5" if is_processed else "10",
            add_data_texts=status == OrderShippingStatusEnum.OPEN_UNCONFIRMED.value
                           or initiated_by != "manager",
            link=f"{CRM_HOST}/order/{order.id}?listType=inbox&itemIdField=orderId"
        )

    await add_push_notifications_for_action(
        AuthSourceEnum.CRM_WEB, AuthSourceEnum.CRM_APP,
        action="crm_order:read",
        available_data={
            "profile_id": group.id,
            "store_id": order.store_id,
            "order_id": order.id,
        },
        message=get_message,
        ignore_session_id=ignore_session_id,
    )
