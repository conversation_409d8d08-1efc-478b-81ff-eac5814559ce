import base64
import io
from email import message_from_string

from aiogram import types
from aiogram.utils.exceptions import (
    BotBlocked, RetryAfter, Unauthorized,
    UserDeactivated,
)

from core.bot.functions import get_user_bot_activity
from core.kafka.constants import TELEGRAM_PREFIX
from schemas import TelegramMessageValue
from utils.message import send_tg_message
from ...senders.base_notification_sender import (
    KafkaNotificationSender,
    RetryNotificationAfter,
)


class TelegramKafkaNotificationSender(KafkaNotificationSender[TelegramMessageValue]):
    PREFIX = TELEGRAM_PREFIX
    LIMITED_OBJ = "tg-bot-chat-id"

    def get_sender_receiver_info(self):
        sender_info = f"{self.data.bot_name}(#{self.data.bot_id})"

        receiver_info = str(self.data.chat_id)
        if self.data.user_id:
            receiver_info += f"(#{self.data.user_id}:{self.data.user_name or '-'})"
        return " -> ".join((sender_info, receiver_info))

    def get_limited_obj_id(self):
        return f"{self.data.bot_token}-{self.data.chat_id}"

    async def send(self):
        sending_data = self.data.sending_data.copy()
        content_type = sending_data.pop("content_type")

        document_bytesio = None
        if content_type == "document":
            if sending_data["document"].get("body"):
                # Стара логіка з body (для сумісності)
                document_bytesio = io.BytesIO(
                    base64.b64decode(
                        message_from_string(sending_data["document"]["body"]).get_payload()
                    )
                )
                sending_data["document"] = types.InputFile(
                    path_or_bytesio=document_bytesio,
                    filename=sending_data["document"]["filename"],
                )
            elif sending_data["document"].get("pdf_media_id"):
                try:
                    from core.loyalty import coupon_service, PDFFormatType
                    
                    pdf_content = await coupon_service.load_pdf_from_media_object(
                        sending_data["document"]["pdf_media_id"],
                        format_type=PDFFormatType.BYTES
                    )
                    
                    if pdf_content:
                        document_bytesio = io.BytesIO(pdf_content)
                        sending_data["document"] = types.InputFile(
                            path_or_bytesio=document_bytesio,
                            filename=sending_data["document"]["filename"],
                        )
                    else:
                        self.logger.warning(f"Could not load PDF for media_id {sending_data['document']['pdf_media_id']}")
                        return None
                except Exception as e:
                    self.logger.error(f"Error loading PDF from media manager: {e}")
                    return None

        result = await send_tg_message(
            self.data.chat_id, content_type, bot_token=self.data.bot_token,
            **sending_data
        )
        if content_type == "document" and document_bytesio:
            document_bytesio.close()
        return result

    async def handle_error(self, error: Exception, reason: str | None = None):
        if isinstance(error, BotBlocked | UserDeactivated):
            self.logger.error("BLOCKED", repr(error))
            if self.data.user_id and isinstance(self.data.bot_id, int):
                try:
                    user_bot_activity = await get_user_bot_activity(
                        self.data.user_id, self.data.bot_id
                    )
                    if user_bot_activity:
                        await user_bot_activity.deactivate()
                    else:
                        self.logger.debug(
                            "User bot activity to deactivate not found",
                        )
                except Exception as e:
                    self.logger.error("SET_USER_BLOCKED:FAILED", repr(e))
                else:
                    self.logger.debug("SET_USER_BLOCKED:SUCCESS")
        elif isinstance(error, Unauthorized):
            super().handle_error(error, "UNAUTHORIZED")
        elif isinstance(error, RetryAfter):
            raise RetryNotificationAfter(error.timeout, is_rate_limit=True)
        super().handle_error(error, reason)
