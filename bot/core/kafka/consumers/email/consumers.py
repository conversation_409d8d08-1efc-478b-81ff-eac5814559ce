from aiokafka import ConsumerRecord

from core.kafka.base.consumer import BaseDBConsumer
from core.kafka.constants import (
    EMAIL_LIMITED_TOPIC, EMAIL_PREFIX, EMAIL_REGULAR_TOPIC,
)
from core.kafka.consumers.email.sender import EmailKafkaNotificationSender
from core.kafka.functions import get_limited_message
from schemas import EmailMessageValue


class EmailConsumer(BaseDBConsumer):
    TOPIC_BASE = EMAIL_REGULAR_TOPIC
    VALUE_MODEL = EmailMessageValue

    async def process_message(
            self, message: ConsumerRecord, value: EmailMessageValue
    ):
        await EmailKafkaNotificationSender(self.redis, value)
        return True


class EmailLimitedConsumer(BaseDBConsumer):
    TOPIC_BASE = EMAIL_LIMITED_TOPIC

    async def process_message(self, message: ConsumerRecord, value: str):
        while message := await get_limited_message(
                self.redis, EMAIL_PREFIX, "email-sender-destination", value
        ):
            try:
                message_validated = EmailMessageValue.parse_raw(message)
            except Exception as exception:
                await self.send_error(
                    "LIMITED Message value validation error",
                    message,
                    exception,
                )
                continue

            result = await EmailKafkaNotificationSender(
                self.redis, message_validated, False
            )
            if not result:
                break
        return True
