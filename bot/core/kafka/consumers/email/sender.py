from email import message_from_string

from psutils.mailing import GmailClient

from core.kafka.constants import EMAIL_PREFIX
from core.kafka.senders.base_notification_sender import KafkaNotificationSender
from loggers import <PERSON><PERSON>NLogger
from schemas import EmailMessageValue
from utils.date_time import utcnow


class EmailKafkaNotificationSender(KafkaNotificationSender[EmailMessageValue]):
    PREFIX = EMAIL_PREFIX
    LIMITED_OBJ = "email-sender-destination"

    def get_sender_receiver_info(self):
        return f"{self.data.sender}-{self.data.destination}"

    def get_limited_obj_id(self):
        return f"{self.data.sender}-{self.data.destination}"

    async def send(self):
        logger = JSONLogger("email.sender")
        logger.debug(
            "EMAIL_KAFKA_RECEIVE_TIME: Received email from Kafka for sending",
            {
                "timestamp": utcnow().isoformat(),
                "destination_email": self.data.destination,
                "subject": self.data.sending_data.subject
            }
        )

        client = GmailClient(self.data.sender, self.data.sender_password)
        sending_data = self.data.sending_data.dict(exclude_unset=True)

        # Handle string attachments by converting them back to MIMEBase objects
        if sending_data.get('attachments'):
            processed_attachments = []
            for attachment in sending_data['attachments']:
                if isinstance(attachment, str):
                    # Convert string back to MIMEBase object
                    mime_attachment = message_from_string(attachment)
                    processed_attachments.append(mime_attachment)
                else:
                    processed_attachments.append(attachment)
            sending_data['attachments'] = processed_attachments

        result = await client.send_email(
            destination=self.data.destination,
            **sending_data,
        )

        logger.debug(
            "EMAIL_SEND_COMPLETE_TIME: Email sent successfully",
            {
                "timestamp": utcnow().isoformat(),
                "destination_email": self.data.destination,
                "subject": self.data.sending_data.subject,
                "result": result
            }
        )

        return result
