from aiokafka import ConsumerRecord

from core.kafka.base.consumer import BaseD<PERSON>onsumer
from core.kafka.constants import (
    MAILING_WHATSAPP_LIMITED_TOPIC,
    MAILING_WHATSAPP_PREFIX, MAILING_WHATSAPP_REGULAR_TOPIC,
)
from core.kafka.consumers.mailing.senders import WhatsappMailingKafkaNotificationSender
from core.kafka.functions import get_limited_message
from schemas import MailingWhatsappMessageValue


class WhatsappMailingConsumer(BaseDBConsumer):
    TOPIC_BASE = MAILING_WHATSAPP_REGULAR_TOPIC
    VALUE_MODEL = MailingWhatsappMessageValue

    async def process_message(
            self, message: ConsumerRecord, value: MailingWhatsappMessageValue
    ):
        await WhatsappMailingKafkaNotificationSender(self.redis, value)
        return True


class WhatsappMailingLimitedConsumer(BaseDBConsumer):
    TOPIC_BASE = MAILING_WHATSAPP_LIMITED_TOPIC

    async def process_message(self, message: ConsumerRecord, value: str):
        while message := await get_limited_message(
                self.redis, MAILING_WHATSAPP_PREFIX, "mailing-wa-from-to", value
        ):
            try:
                message_validated = MailingWhatsappMessageValue.parse_raw(message)
            except Exception as exception:
                await self.send_error(
                    "LIMITED Message value validation error",
                    message,
                    exception,
                )
                continue

            result = await WhatsappMailingKafkaNotificationSender(
                self.redis, message_validated, False
            )
            if not result:
                break
        return True
