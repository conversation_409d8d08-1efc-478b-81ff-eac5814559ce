from aiokafka import ConsumerRecord

from core.kafka.base.consumer import BaseDBConsumer
from core.kafka.constants import (
    MAILING_EMAIL_LIMITED_TOPIC,
    MAILING_EMAIL_PREFIX, MAILING_EMAIL_REGULAR_TOPIC,
)
from core.kafka.consumers.mailing.senders import EmailMailingKafkaNotificationSender
from core.kafka.functions import get_limited_message
from schemas import EmailMailingMessageValue


class EmailMailingConsumer(BaseDBConsumer):
    TOPIC_BASE = MAILING_EMAIL_REGULAR_TOPIC
    VALUE_MODEL = EmailMailingMessageValue

    async def process_message(
            self, message: ConsumerRecord, value: EmailMailingMessageValue
    ):
        await EmailMailingKafkaNotificationSender(self.redis, value)
        return True


class EmailMailingLimitedConsumer(BaseDBConsumer):
    TOPIC_BASE = MAILING_EMAIL_LIMITED_TOPIC

    async def process_message(self, message: ConsumerRecord, value: str):
        while message := await get_limited_message(
                self.redis, MAILING_EMAIL_PREFIX, "mailing-email-sender-destination",
                value
        ):
            try:
                message_validated = EmailMailingMessageValue.parse_raw(message)
            except Exception as exception:
                await self.send_error(
                    "LIMITED Message value validation error",
                    message,
                    exception,
                )
                continue

            result = await EmailMailingKafkaNotificationSender(
                self.redis, message_validated, False
            )
            if not result:
                break
        return True
