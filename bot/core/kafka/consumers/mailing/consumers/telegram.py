from aiokafka import ConsumerRecord

from core.kafka.base.consumer import BaseDBConsumer
from core.kafka.constants import (
    MAILING_TELEGRAM_LIMITED_TOPIC, MAILING_TELEGRAM_PREFIX,
    MAILING_TELEGRAM_REGULAR_TOPIC,
)
from core.kafka.consumers.mailing.senders import (
    TelegramMailingKafkaNotificationSender,
)
from core.kafka.functions import get_limited_message
from schemas import (
    MailingTelegramMessageValue,
)


class TelegramMailingConsumer(BaseDBConsumer):
    TOPIC_BASE = MAILING_TELEGRAM_REGULAR_TOPIC
    VALUE_MODEL = MailingTelegramMessageValue

    async def process_message(
            self, message: ConsumerRecord, value: MailingTelegramMessageValue
    ):
        result = await TelegramMailingKafkaNotificationSender(self.redis, value)
        # Повертаємо True для успіху і не критичних помилок (BotBlocked)
        # Повертаємо False тільки для критичних помилок
        return result is not False


class TelegramMailingLimitedConsumer(BaseDBConsumer):
    TOPIC_BASE = MAILING_TELEGRAM_LIMITED_TOPIC

    async def process_message(self, message: ConsumerRecord, value: str):
        while message := await get_limited_message(
                self.redis, MAILING_TELEGRAM_PREFIX, "mailing-tg-bot-chat-id", value
        ):
            try:
                message_validated = MailingTelegramMessageValue.parse_raw(message)
            except Exception as exception:
                await self.send_error(
                    "LIMITED Message value validation error",
                    message,
                    exception,
                )
                continue

            result = await TelegramMailingKafkaNotificationSender(
                self.redis,
                message_validated,
                False,
            )
            if result is False:  # Тільки критичні помилки зупиняють обробку
                break
            # result is True (успіх) або None (BotBlocked) - продовжуємо
        return True
