from aiogram.utils.exceptions import (
    BotBlocked, RetryAfter, Unauthorized,
    UserDeactivated,
)

from core.bot.functions import get_user_bot_activity
from core.kafka.constants import MAILING_TELEGRAM_PREFIX
from core.kafka.senders.base_notification_sender import RetryNotificationAfter
from core.marketing.service import MailingService
from db.models import Mailing
from schemas import MailingTelegramMessageValue
from utils.message import send_tg_message
from utils.text import convert_html_to_supported_html
from .base import MailingKafkaNotificationSender


class TelegramMailingKafkaNotificationSender(
    MailingKafkaNotificationSender[MailingTelegramMessageValue]
):
    PREFIX = MAILING_TELEGRAM_PREFIX
    LIMITED_OBJ = "mailing-tg-bot-chat-id"

    def get_sender_receiver_info(self):
        sender_info = f"{self.data.bot_name}(#{self.data.bot_id})"

        receiver_info = str(self.data.chat_id)
        if self.data.user_id:
            receiver_info += f"(#{self.data.user_id}:{self.data.user_name or '-'})"
        return " -> ".join((sender_info, receiver_info))

    def get_limited_obj_id(self):
        return f"{self.data.bot_token}-{self.data.chat_id}"

    @property
    def mailing_id(self):
        return self.data.sending_data["mailing_id"]

    @property
    def mailing_message_id(self):
        return self.data.sending_data["mailing_message_id"]

    async def send_message(self, mailing: Mailing):
        sending_data = self.data.sending_data.copy()

        if mailing.media_id:
            mailing_service = MailingService()
            content_type_value, media_value = await (
                mailing_service.get_media_data_for_bot(
                    mailing.media_id, mailing.media_type.value, "telegram",
                ))
            if content_type_value and media_value:
                content_type = content_type_value
                sending_data[content_type] = media_value
            else:
                content_type = "text"
        else:
            content_type = "text"

        sending_data["text"] = convert_html_to_supported_html(
            sending_data.pop("mailing_message"),
            "telegram",
        )

        return await send_tg_message(
            self.data.chat_id, content_type, bot_token=self.data.bot_token,
            **sending_data
        )

    async def handle_error(self, error: Exception, reason: str | None = None):
        if isinstance(error, BotBlocked | UserDeactivated):
            result = await super().handle_error(error, "BLOCKED")

            if self.data.user_id and isinstance(self.data.bot_id, int):
                try:
                    user_bot_activity = await get_user_bot_activity(
                        self.data.user_id, self.data.bot_id
                    )
                    if user_bot_activity:
                        await user_bot_activity.deactivate()
                    else:
                        self.logger.debug(
                            "User bot activity to deactivate not found",
                        )
                except Exception as e:
                    self.logger.error("SET_USER_BLOCKED:FAILED", repr(e))
                else:
                    self.logger.debug("SET_USER_BLOCKED:SUCCESS")

            return result
        elif isinstance(error, Unauthorized):
            return await super().handle_error(error, "UNAUTHORIZED")
        elif isinstance(error, RetryAfter):
            raise RetryNotificationAfter(error.timeout, is_rate_limit=True)
        return await super().handle_error(error, reason)
