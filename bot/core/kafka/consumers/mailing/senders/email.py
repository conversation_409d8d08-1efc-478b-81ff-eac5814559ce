from email import encoders
from email.mime.base import MIMEBase

from psutils.mailing import GmailClient

from core.kafka.constants import MAILING_EMAIL_PREFIX
from db.models import Mailing, MediaObject
from schemas import EmailMailingMessageValue
from .base import MailingKafkaNotificationSender


class EmailMailingKafkaNotificationSender(
    MailingKafkaNotificationSender[EmailMailingMessageValue]
):
    PREFIX = MAILING_EMAIL_PREFIX
    LIMITED_OBJ = "mailing-email-sender-destination"

    @property
    def mailing_id(self):
        return self.data.mailing_id

    @property
    def mailing_message_id(self):
        return self.data.mailing_message_id

    def get_sender_receiver_info(self):
        return f"{self.data.sender}-{self.data.destination}"

    def get_limited_obj_id(self):
        return f"{self.data.sender}-{self.data.destination}"

    async def send_message(self, mailing: Mailing):
        sending_data = {
            "destination": self.data.destination,
            "subject": self.data.subject,
            "html": self.data.mailing_message,
            "body": self.data.mailing_message,
            "from_name": self.data.from_name
        }

        attachments = []
        if mailing.media_id and mailing.email_media_as_attachment:
            media = await MediaObject.get(mailing.media_id)
            if media:
                with open(media.file_path, "rb") as attachment:
                    part = MIMEBase("application", "octet-stream")
                    part.set_payload(attachment.read())
                    encoders.encode_base64(part)
                    part.add_header(
                        "Content-Disposition",
                        f"attachment; filename= {media.file_path}",
                    )
                    attachments.append(part)
        if attachments:
            sending_data["attachments"] = attachments

        client = GmailClient(self.data.sender, self.data.sender_password)
        return await client.send_email(
            **sending_data
        )
