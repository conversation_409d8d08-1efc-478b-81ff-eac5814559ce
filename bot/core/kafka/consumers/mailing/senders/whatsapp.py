from core.kafka.constants import MAILING_WHATSAPP_PREFIX
from core.kafka.senders.base_notification_sender import RetryNotificationAfter
from core.marketing.service import MailingService
from db.models import ClientBot, Mailing
from schemas import MailingWhatsappMessageValue
from utils.message import send_wa_message, send_wa_template_message
from utils.text import convert_html_to_supported_html
from .base import MailingKafkaNotificationSender


class WhatsappMailingKafkaNotificationSender(
    MailingKafkaNotificationSender[MailingWhatsappMessageValue]
):
    PREFIX = MAILING_WHATSAPP_PREFIX
    LIMITED_OBJ = "mailing-wa-from-to"

    def get_sender_receiver_info(self):
        sender_info = f"{self.data.bot_name}(#{self.data.bot_id})"

        receiver_info = str(self.data.wa_phone)
        if self.data.user_id:
            receiver_info += f"(#{self.data.user_id}:{self.data.user_name or '-'})"
        return " -> ".join((sender_info, receiver_info))

    def get_limited_obj_id(self):
        return f"{self.data.bot_from}-{self.data.wa_phone}"

    @property
    def mailing_id(self):
        return self.data.sending_data["mailing_id"]

    @property
    def mailing_message_id(self):
        return self.data.sending_data["mailing_message_id"]

    async def send_message(self, mailing: Mailing):
        sending_data = self.data.sending_data.copy()
        template_id = sending_data.pop("template_id")
        variables = sending_data.pop("variables")

        if template_id:

            bot = await ClientBot.get(self.data.bot_id)
            return await send_wa_template_message(
                template_id=template_id, variables=variables,
                bot=bot, to=self.data.wa_phone,
            )

        if mailing.media_id:
            mailing_service = MailingService()
            content_type_value, media_value = await (
                mailing_service.get_media_data_for_bot(
                    mailing.media_id, mailing.media_type.value, "whatsapp",
                ))
            if content_type_value and media_value:
                content_type = content_type_value
                sending_data[content_type] = media_value
            else:
                content_type = "text"
        else:
            content_type = "text"

        sending_data["text"] = convert_html_to_supported_html(
            sending_data.pop("mailing_message"),
            "whatsapp",
        )

        return await send_wa_message(
            self.data.wa_phone, bot_token=self.data.bot_token,
            wa_from=self.data.bot_from, content_type=content_type,
            **sending_data,
        )

    async def handle_error(self, error: Exception, reason: str | None = None):
        if hasattr(error, "is_rate_limit_exceeded") and error.is_rate_limit_exceeded:
            raise RetryNotificationAfter((self.data.retry_count + 1) * 4)

        await super().handle_error(error, reason)
