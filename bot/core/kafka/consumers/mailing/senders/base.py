from abc import ABC, abstractmethod

from typing_extensions import TypeVar

import schemas
from core.kafka.senders.base_notification_sender import KafkaNotificationSender
from core.marketing.service import MailingService
from db.models import Mailing
from schemas import BaseKafkaMessageValue

ValueT = TypeVar(
    "ValueT",
    bound=BaseKafkaMessageValue,
    default=BaseKafkaMessageValue,
)


class MailingKafkaNotificationSender(
    KafkaNotificationSender[ValueT],
    ABC,
):
    @abstractmethod
    async def send_message(self, mailing: Mailing):
        raise NotImplementedError

    @property
    @abstractmethod
    def mailing_id(self):
        raise NotImplementedError

    @property
    @abstractmethod
    def mailing_message_id(self):
        raise NotImplementedError

    async def send(self):
        mailing_id = self.mailing_id
        mailing_message_id = self.mailing_message_id

        mailing = await Mailing.get(mailing_id)
        if not mailing or mailing.status == schemas.MailingStatusEnum.CANCELED:
            return

        result = await self.send_message(mailing)

        # Перевіряємо чи результат це помилка (BotBlocked тощо)
        from aiogram.utils.exceptions import BotBlocked, UserDeactivated, Unauthorized
        if isinstance(result, (BotBlocked, UserDeactivated, Unauthorized)):
            # Не викликаємо process_sent_message тут - це буде зроблено в handle_error
            return result

        # Викликаємо process_sent_message тільки для успішних результатів
        mailing_service = MailingService()
        await mailing_service.process_sent_message(
            mailing_message_id,
            mailing_id,
            retry_info={
                "retry_count": self.data.retry_count,
            }
        )

        return result

    async def handle_error(self, error: Exception, reason: str | None = None):
        mailing_service = MailingService()
        mailing_id = self.mailing_id
        mailing_message_id = self.mailing_message_id

        if mailing_message_id and mailing_id:
            await mailing_service.process_sent_message(
                mailing_message_id, mailing_id,
                {
                    "retry_count": self.data.retry_count,
                },
                error,
            )

        return super().handle_error(error, reason)
