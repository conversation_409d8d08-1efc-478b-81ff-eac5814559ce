from aiowhatsapp.types import BaseKeyboard

from core.kafka.constants import WHATSAPP_PREFIX
from core.kafka.senders.base_notification_sender import (
    KafkaNotificationSender,
    RetryNotificationAfter,
)
from schemas import WhatsappMessageValue
from utils.message import send_wa_message


class WhatsappKafkaNotificationSender(KafkaNotificationSender[WhatsappMessageValue]):
    PREFIX = WHATSAPP_PREFIX
    LIMITED_OBJ = "wa-from-to"

    def get_sender_receiver_info(self):
        sender_info = f"{self.data.bot_name}(#{self.data.bot_id})"

        receiver_info = str(self.data.wa_phone)
        if self.data.user_id:
            receiver_info += f"(#{self.data.user_id}:{self.data.user_name or '-'})"
        return " -> ".join((sender_info, receiver_info))

    def get_limited_obj_id(self):
        return f"{self.data.bot_from}-{self.data.wa_phone}"

    async def send(self):
        if isinstance(self.data.sending_data.get("keyboard"), dict):
            self.data.sending_data["keyboard"] = BaseKeyboard.detect_from_data(
                self.data.sending_data["keyboard"]
            )

        # Обробка pdf_media_id для document
        if (self.data.sending_data.get("content_type") == "document" and 
            self.data.sending_data.get("pdf_media_id")):
            try:
                from core.loyalty import coupon_service, PDFFormatType
                from aiowhatsapp import wa
                import io
                
                file_path = await coupon_service.load_pdf_from_media_object(
                    self.data.sending_data["pdf_media_id"],
                    format_type=PDFFormatType.FILE_PATH
                )
                
                if file_path:
                    # Завантажуємо PDF в WhatsApp API
                    wa_bot = wa.WhatsappBot(self.data.bot_token, self.data.bot_from)
                    with open(file_path, 'rb') as f:
                        document_bytesio = io.BytesIO(f.read())
                    
                    document_media = await wa_bot.upload_media(
                        document_bytesio, "application/pdf"
                    )
                    document_bytesio.close()
                    
                    # Замінюємо pdf_media_id на document ID
                    self.data.sending_data["document"] = document_media.id
                    del self.data.sending_data["pdf_media_id"]
                else:
                    self.logger.warning(f"Could not load PDF for media_id {self.data.sending_data['pdf_media_id']}")
                    return None
            except Exception as e:
                self.logger.error(f"Error loading PDF from media manager for WhatsApp: {e}")
                return None

        return await send_wa_message(
            self.data.wa_phone, bot_token=self.data.bot_token,
            wa_from=self.data.bot_from, **self.data.sending_data
        )

    async def handle_error(self, error: Exception, reason: str | None = None):
        if hasattr(error, "is_rate_limit_exceeded") and error.is_rate_limit_exceeded:
            raise RetryNotificationAfter((self.data.retry_count + 1) * 4)
        super().handle_error(error, reason)
