from aiokafka import ConsumerRecord

from core.kafka.base.consumer import BaseDBConsumer
from core.kafka.constants import (
    WHATSAPP_LIMITED_TOPIC, WHATSAPP_PREFIX, WHATSAPP_REGULAR_TOPIC,
)
from core.kafka.consumers.whatsapp.sender import WhatsappKafkaNotificationSender
from core.kafka.functions import get_limited_message
from schemas import WhatsappMessageValue


class WhatsappConsumer(BaseDBConsumer):
    TOPIC_BASE = WHATSAPP_REGULAR_TOPIC
    VALUE_MODEL = WhatsappMessageValue

    async def process_message(
            self, message: ConsumerRecord, value: WhatsappMessageValue
    ):
        await WhatsappKafkaNotificationSender(self.redis, value)
        return True


class WhatsappLimitedConsumer(BaseDBConsumer):
    TOPIC_BASE = WHATSAPP_LIMITED_TOPIC

    async def process_message(self, message: ConsumerRecord, value: str):
        while message := await get_limited_message(
                self.redis, WHATSAPP_PREFIX, "wa-from-to", value
        ):
            try:
                message_validated = WhatsappMessageValue.parse_raw(message)
            except Exception as exception:
                await self.send_error(
                    "LIMITED Message value validation error",
                    message,
                    exception,
                )
                continue

            result = await WhatsappKafkaNotificationSender(
                self.redis, message_validated, False
            )
            if not result:
                break
        return True
