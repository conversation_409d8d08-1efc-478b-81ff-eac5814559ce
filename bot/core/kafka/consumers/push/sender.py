from core.kafka.constants import PUSH_PREFIX
from core.kafka.consumers.push.firebase_client import fcm
from core.kafka.senders.base_notification_sender import (
    KafkaNotificationSender,
    RetryNotificationAfter,
)
from db import crud
from schemas import PushMessageValue


class PushKafkaNotificationSender(KafkaNotificationSender[PushMessageValue]):
    PREFIX = PUSH_PREFIX
    LIMITED_OBJ = "device"

    def get_sender_receiver_info(self):
        return self.data.device_token

    def get_limited_obj_id(self):
        return self.data.device_token

    async def send(self):
        response = await fcm.send(self.data.fcm_message)

        if response and response.success:
            return {"message_id": response.message_id}

        exception = response.exception if response else None
        http_response = (
            exception.http_response
            if exception and exception.http_response
            else None
        )
        http_status = http_response.status_code if http_response else None
        exception_message = (exception, "message", None) if exception else None
        if not http_status or http_status >= 500:
            retry_after = self.data.retry_count * 10
        elif http_status == 401 or http_status == 404:
            self.logger.add_data(
                {
                    "response": {
                        "http_status": http_status,
                        "exception_code": exception.code,
                        "exception_message": exception_message,
                    }
                }
            )
            self.logger.error(
                "TOKEN_INVALID",
                f"{http_status}:{exception.code}:{exception_message}",
                {
                    "response": {
                        "http_status": http_status,
                        "exception_code": exception.code,
                        "exception_message": exception_message,
                    }
                }
            )
            try:
                await crud.remove_push_token(self.data.device_token)
                self.logger.debug("TOKEN_REMOVED")
            except Exception as e:
                self.logger.error("REMOVE_TOKEN_FAILED", repr(e))
            return False
        elif http_status == 429:
            try:
                retry_after = int(exception.http_response.headers.get("Retry-After"))
            except:
                retry_after = 0
        else:
            self.logger.debug(
                "FAILED", f"{http_status}:{exception.code}:{exception_message}", {
                    "http_status": http_status,
                    "exception_code": exception.code,
                    "exception_message": exception_message,
                }
            )
            return False

        raise RetryNotificationAfter(retry_after)
