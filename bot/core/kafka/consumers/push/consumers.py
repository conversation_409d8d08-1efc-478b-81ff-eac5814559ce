from aiokafka import ConsumerRecord

from core.kafka.base.consumer import BaseDBConsumer
from core.kafka.constants import PUSH_LIMITED_TOPIC, PUSH_PREFIX, PUSH_REGULAR_TOPIC
from core.kafka.consumers.push.sender import PushKafkaNotificationSender
from core.kafka.functions import get_limited_message
from schemas import PushMessageValue


class PushConsumer(BaseDBConsumer):
    TOPIC_BASE = PUSH_REGULAR_TOPIC
    VALUE_MODEL = PushMessageValue

    async def process_message(self, message: ConsumerRecord, value: PushMessageValue):
        await PushKafkaNotificationSender(self.redis, value)
        return True


class PushLimitedConsumer(BaseDBConsumer):
    TOPIC_BASE = PUSH_LIMITED_TOPIC

    async def process_message(self, message: ConsumerRecord, value: str):
        while message := await get_limited_message(
                self.redis, PUSH_PREFIX, "device", value
        ):
            try:
                message_validated = PushMessageValue.parse_raw(message)
            except Exception as exception:
                await self.send_error(
                    "LIMITED Message value validation error",
                    message,
                    exception,
                )
                continue

            result = await PushKafkaNotificationSender(
                self.redis, message_validated, False
            )
            if not result:
                break
        return True
