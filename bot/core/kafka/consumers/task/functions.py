import json
from copy import deepcopy
from datetime import datetime

import aioredis
import openai
from openai import AuthenticationError

from config import ALLOWED_IMAGE_EXTENSIONS, OPENAI_API_KEY
from core.admin_notification.service import create_system_notification
from core.kafka.constants import MAX_RETRY_COUNT
from core.kafka.consumers.task.openai_funcs import (
    parse_ratelimit_reset,
    process_ai_image,
)
from core.kafka.functions import save_retry_message
from core.media_manager import media_manager
from core.store.functions.media import make_thumbnail
from db import DBSession, crud
from db.models import (
    Brand, Group, InvoiceTemplate, MediaObject, Store, StoreBanner, StoreProduct, Task,
    VirtualManagerStep,
)
from exceptions.task import (
    OpenAIConfigError, TaskKafkaError, TaskStatusError,
    TaskTypeError,
)
from loggers import JSONLogger
from schemas import (
    SystemNotificationCategory, SystemNotificationType, TaskStatusEnum,
    TaskTypeTaskEnum, TaskValue,
)
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f


async def task_action_process(*args, **kwargs) -> int | None:
    debugger = JSONLogger("kafka.consumer", "task_action_process", kwargs)
    result = await process_ai_image(kwargs)

    with DBSession() as db:
        task = await Task.get(result.get("task_id"))
        brand = await Brand.get(group_id=task.group_id)

        if result.get("status") == "success" and result.get("url"):
            media = await media_manager.download_media(
                result["url"], allowed_types=ALLOWED_IMAGE_EXTENSIONS
            )
            debugger.debug(f"{media=}")

            json_data = deepcopy(task.json_data)
            json_data.update({"media_id": media.id})

            if task.status != TaskStatusEnum.PROCESSING:
                await task.update(
                    json_data=json_data, end_date=datetime.utcnow(),
                    dalle_prompt=result.get("dalle_prompt")
                )
                if task.status not in (
                        TaskStatusEnum.CANCELED,
                        TaskStatusEnum.REPLACEMENT,
                        TaskStatusEnum.DELETED,
                ):
                    return
                raise TaskStatusError(task.id, task.status)

            await process_type_task(brand, media, task, json_data)

            await task.update(
                status=TaskStatusEnum.FINISHED, json_data=json_data,
                end_date=datetime.utcnow(),
                change_date=datetime.utcnow(), dalle_prompt=result.get("dalle_prompt")
            )
        else:
            await task.update(
                status=TaskStatusEnum.FAILED, change_date=datetime.utcnow()
            )

        debugger.debug(f"task.update {task.status=}, {task.json_data=}")
        db.commit()
    return kwargs.get("id", None)


async def process_type_task(
        brand: Brand, media: MediaObject, task: Task, json_data: dict
):
    debugger = JSONLogger(
        "kafka.consumer",
        "process_type_task",
        {"brand_id": brand.id, "media_id": media.id, "json_data": task.json_data}
    )
    match task.type_task:
        case TaskTypeTaskEnum.PRODUCT_IMAGE:
            product = await StoreProduct.get(task.object_id)
            thumbnail = await make_thumbnail(media, brand)
            debugger.debug(f"{thumbnail=}")

            product.media_id = media.id
            product.thumbnail_media_id = thumbnail.id
            debugger.debug(
                f"product.updated {product.media_id=}, {product.thumbnail_media_id=}"
            )
        case TaskTypeTaskEnum.STORE_IMAGE:
            store = await Store.get(task.object_id)
            store.media_id = media.id
            debugger.debug(f"store.updated {store.media_id=}")
        case TaskTypeTaskEnum.STORE_BANNER:
            if not task.json_data.get("banner_id"):
                banners = await StoreBanner.get_list(store_id=task.object_id)
                banner = await StoreBanner.create(
                    store_id=task.object_id,
                    name=task.json_data.get("name"),
                    url=task.json_data.get("url"),
                    position=len(banners) + 1,
                    is_visible=True,
                    media=media
                )
                json_data.update({"banner_id": banner.id})
                debugger.debug(f"banner created {banner.id=}")
            else:
                banner = await StoreBanner.get(task.json_data.get("banner_id"))
                banner.media = media
                banner.url = task.json_data.get("url")
                debugger.debug(f"banner updated {banner.id=}")
        case TaskTypeTaskEnum.PROFILE_LOGO:
            brand.logo_media_id = media.id
            debugger.debug(f"brand.updated {brand.logo_media_id=}")
        case TaskTypeTaskEnum.PROFILE_IMAGE:
            brand.image_media_id = media.id
            debugger.debug(f"brand.updated {brand.image_media_id=}")
        case TaskTypeTaskEnum.VM_STEP_IMAGE:
            vm_step = await VirtualManagerStep.get(task.object_id)
            vm_step.media_id = media.id
            debugger.debug(f"vm_step.updated {vm_step.media_id=}")
        case TaskTypeTaskEnum.INVOICE_TEMPLATE_IMAGE:
            invoice_template = await InvoiceTemplate.get(task.object_id)
            invoice_template.media_id = media.id
            debugger.debug(f"invoice_template.updated {invoice_template.media_id=}")
        case _:
            raise TaskTypeError(task.type_task)


async def do_task_action(
        redis: aioredis.Redis, prefix: str, data: TaskValue,
        save_retry_if_limited_exists: bool = True
):

    logger = JSONLogger("kafka.consumer", "do_task_action", data)
    logger.debug("SENDING")

    try:
        with DBSession():
            task = await Task.get(data.id)
            # checks if limited messages exist for tasks and write to limited
            # messages if so
            if save_retry_if_limited_exists and await save_retry_message(
                    redis, prefix,
                    "task-type-type_task-object_id",
                    f"{data.type}-{data.type_task}-{data.object_id}",
                    data, only_if_limited_messages_exists=True
            ):
                await task.update(
                    status=TaskStatusEnum.PENDING,
                    start_date=None,
                    change_date=datetime.utcnow()
                )
                return False

            group = await Group.get(data.group_id)
            group_config = deepcopy(group.config)
            openai_key = None
            try:
                openai_key = group_config.openai_config.data[0]
                if openai_key == "1169":
                    openai_key = OPENAI_API_KEY
            except (AttributeError, IndexError):
                raise OpenAIConfigError(data.group_id)

            if task.status in (
                    TaskStatusEnum.CANCELED,
                    TaskStatusEnum.REPLACEMENT,
                    TaskStatusEnum.DELETED,
            ):
                return True

            if task.status != TaskStatusEnum.PENDING:
                raise TaskStatusError(task.id, task.status.value)

            await task.update(
                status=TaskStatusEnum.PROCESSING, start_date=datetime.utcnow(),
                change_date=datetime.utcnow()
            )

        result = await task_action_process(**data.dict(), openai_key=openai_key)
    except Exception as error:

        if isinstance(error,  openai.RateLimitError) and error.status_code != 429:
            logger.error(
                f"RATE_LIMIT_EXCEEDED {data.retry_count=}", repr(error),
                error.response.json()
            )

            if data.retry_count < MAX_RETRY_COUNT:
                try:
                    timout_str = error.response.headers.get(
                        "x-ratelimit-reset-images",
                        error.response.headers.get("retry-after")
                    )
                    timeout = parse_ratelimit_reset(timout_str)
                except:
                    timeout = (10 * data.retry_count) or 1

                data.retry_count += 1
                await save_retry_message(
                    redis, prefix,
                    "task-type-type_task-object_id",
                    f"{data.type}-{data.type_task}-{data.object_id}",
                    data, timeout,
                    lpush_message=data.retry_count > 1
                )
                with DBSession():
                    task = await Task.get(data.id)
                    await task.update(
                        status=TaskStatusEnum.PENDING,
                        start_date=None,
                        change_date=datetime.utcnow()
                    )
                return False

        with DBSession():
            is_need_cancel_tasks = False
            is_need_sent_to_admins = False
            group = await Group.get(data.group_id)
            lang = group.lang
            error_header = await f("ai product image error header", lang) + "\n\n"
            add_text = None
            error_message = await f("ai product image unknown error", lang)

            if isinstance(error, AuthenticationError):
                error_message = await f(
                    "openai auth error", lang, error=error.body.get(
                        "message"
                    )
                )
                is_need_cancel_tasks = True
            elif isinstance(error, TaskKafkaError):
                error_message = await f(error.text_variable, lang, **error.text_kwargs)
                if isinstance(error, OpenAIConfigError):
                    is_need_cancel_tasks = True
                if isinstance(error, TaskStatusError):
                    is_need_sent_to_admins = True
            elif hasattr(error, "code"):
                if error.code == "content_policy_violation":
                    error_message = await f(
                        "ai product image content policy violation", lang,
                        dish=f"{data.name} - {data.description}"
                    )
                    add_text = await f("ai product image repeat violation text", lang)
                else:
                    is_need_cancel_tasks = True
                    is_need_sent_to_admins = True 
                # elif error.code == "model_not_found":
                    try:
                        err = json.loads(error.response.text)
                        if err and err.get("error") and err["error"].get("message", None):
                            error_message = await f("ai product image error", lang, message=err["error"]["message"])
                            # add_text = await f("ai product image repeat violation text", lang) 
                    except json.JSONDecodeError as err:
                        ...    
            else:
                is_need_sent_to_admins = True

            if is_need_cancel_tasks:
                await crud.update_tasks_for_group(
                    data.group_id, TaskStatusEnum.CANCELED, error=error_message
                )

            message_to_admin = error_header + error_message
            if add_text:
                message_to_admin += f"\n\n{add_text}"

            if not isinstance(error, TaskStatusError):

                await create_system_notification(
                    "profile:edit",
                    group_id=data.group_id, category=SystemNotificationCategory.TASK,
                    type_notification=SystemNotificationType.TASK_ERROR,
                    title="Task error",
                    content=message_to_admin,
                )

                if is_need_sent_to_admins:
                    await send_message_to_platform_admins(
                        f"do_task_action error:\n\n{error}\n\n{data.dict()=}"
                    )

            if not is_need_cancel_tasks:
                await crud.update_task(data.id, TaskStatusEnum.FAILED, error_message)

        logger.error("FAILED", repr(error))
    else:
        logger.debug(
            "SUCCESS", {
                "result": result,
            }
        )

    return True
