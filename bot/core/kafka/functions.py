import aioredis
import time
from pydantic import BaseModel

from core.kafka.redis_keys import (
    get_delayed_item_key, get_delayed_key,
    get_limited_messages_key,
)
from loggers import JSONLogger


# noinspection PyAsyncCall
async def save_retry_message(
        redis: aioredis.Redis,
        prefix: str,
        limited_obj: str,
        limited_obj_id: str | int,
        data: BaseModel,
        retry_after: int = 0,
        lpush_message: bool = False,
        only_if_limited_messages_exists: bool = False
):
    limited_messages_key = get_limited_messages_key(prefix, limited_obj, limited_obj_id)
    if only_if_limited_messages_exists and not await redis.llen(limited_messages_key):
        return False

    async with redis.pipeline() as pipe:

        push_args = (
            get_limited_messages_key(prefix, limited_obj, limited_obj_id),
            data.json(),
        )

        if lpush_message:
            pipe.lpush(*push_args)
        else:
            pipe.rpush(*push_args)

        item_key = get_delayed_item_key(prefix, limited_obj_id)

        new_score = time.time() + retry_after
        current_score = await redis.zscore(get_delayed_key(), item_key)
        if current_score is None or new_score > current_score:
            pipe.zadd(
                get_delayed_key(),
                {item_key: time.time() + retry_after}
            )
        await pipe.execute()

    JSONLogger(
        "kafka.consumer", "save_retry_message", limited_obj, limited_obj_id, data
    ).debug("SUCCESS")
    return True


async def get_limited_message(
        redis: aioredis.Redis, prefix: str, limited_obj: str, limited_obj_id: str
):
    return await redis.lpop(
        get_limited_messages_key(prefix, limited_obj, limited_obj_id)
    )
