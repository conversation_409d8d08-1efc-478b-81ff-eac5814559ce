import logging

from fastapi.templating import Jinja2Templates

from core.check.texts import get_payment_info
from core.payment.utils.extra_fees import get_extra_fee_txt
from db import crud
from db.models import Brand, Group, OrderShipment, Store, StoreOrder, StoreOrderPayment
from schemas.store.types import ShipmentType
from service.store.functions import (
    get_desired_delivery_text,
    get_store_order_delivery_text,
)
from utils.numbers import format_currency
from utils.text import f

debugger = logging.getLogger('debugger')
templates = Jinja2Templates(directory="templates")


async def get_order_end_price_text(
        group: Group, store_order: StoreOrder, currency: str, lang: str
) -> str:
    if store_order.discount_and_bonuses == store_order.sum_to_pay:
        sum_to_pay = 0
    else:
        sum_to_pay = store_order.sum_to_pay

    extra_fee_str = await get_extra_fee_txt(group, store_order, currency, lang)

    order_end_price = format_currency(sum_to_pay / 100, currency, locale=lang)

    if store_order.status_pay != 'payed':
        order_end_price_text = await f(
            "store order to pay text", lang, order_end_price=order_end_price
        )
    else:
        order_end_price_text = await f(
            "store order payed text", lang, order_end_price=order_end_price
        )

    shipment_price_text = ""
    if store_order.shipment.price:
        shipment_cost_field_name = await f(
            "store brand delivery cost notifications text", lang
        )
        shipment_cost_str = format_currency(
            store_order.shipment.price, currency, locale=lang
        )
        shipment_price_text = f"\n{shipment_cost_field_name}: {shipment_cost_str}"

    if store_order.loyalty_type == "incust":
        if store_order.tips_sum:
            return await f(
                'service bot end price discount tips message',
                lang,
                amount=format_currency(
                    store_order.before_loyalty_sum / 100, currency, locale=lang
                ),
                discount=format_currency(
                    store_order.discount / 100, currency, locale=lang
                ),
                extra_fee_str=extra_fee_str,
                order_end_price=order_end_price_text,
                tips=format_currency(store_order.tips_sum / 100, currency, locale=lang),
                total_sum=format_currency(
                    store_order.total_sum / 100, currency, locale=lang
                ),
                bonuses_redeemed=format_currency(
                    store_order.bonuses_redeemed / 100, currency,
                    locale=lang
                ),
                shipment_price_text=shipment_price_text,
            )
        else:
            return await f(
                'service bot end price discount message',
                lang,
                amount=format_currency(
                    store_order.before_loyalty_sum / 100, currency, locale=lang
                ),
                discount=format_currency(
                    store_order.discount / 100, currency, locale=lang
                ),
                extra_fee_str=extra_fee_str,
                order_end_price=order_end_price_text,
                total_sum=format_currency(
                    store_order.total_sum / 100, currency, locale=lang
                ),
                bonuses_redeemed=format_currency(
                    store_order.bonuses_redeemed / 100, currency,
                    locale=lang
                ),
                shipment_price_text=shipment_price_text,
            )
    else:
        order_price_text = ""
        if store_order.tips_sum:
            order_sum = (
                f"{await f('order sum header', lang)}: "
                f"{format_currency(store_order.total_sum / 100, store_order.currency, locale=lang)}"
            )
            tips_sum = (
                    "\n" +
                    await f('tips header', lang) + ": " +
                    format_currency(
                        store_order.tips_sum / 100,
                        store_order.currency, locale=lang,
                    ) + "\n"
            )
            order_price_text += order_sum
            order_price_text += extra_fee_str
            order_price_text += shipment_price_text
            order_price_text += tips_sum
        order_price_text += f"\n{order_end_price_text}"

        return order_price_text


async def get_products_data(
        store_order: StoreOrder, brand: Brand, store: Store, lang: str, ) -> list[dict]:
    data = list()
    store_id = store.id
    currency = store.currency

    order_products_data = await crud.get_order_products(
        store_order.id, with_products=True
    )

    discount_header = await f("loyalty discount header", lang)
    for order_product, store_product in order_products_data:
        if order_product.discount_sum > 0:
            discount = (f"\n{discount_header}: {order_product.discount_sum / 100:.2f} "
                        f"{currency}")
        else:
            discount = ""

        product_ = {
            "image_url": await store_product.media_url,
            "product_url": brand.get_url(
                f"s/{store_id}/menu?product_id={order_product.product_id}"
            ),
            "product_id": store_product.product_id, "name": store_product.name,
            "price": f"{order_product.price_with_attributes / 100:.2f}",
            "quantity": f"{order_product.quantity}",
            "before_loyalty_sum": f"{order_product.before_loyalty_sum / 100:.2f}",
            "currency": currency, "discount": discount, "attributes": {},
            "modifiers_str": ""
        }
        if order_product.attributes:
            attributes = []
            for attr in order_product.attributes:
                attributes.append({"name": attr.name, "quantity": attr.quantity})
            product_.update(attributes=attributes)

        modifiers = await crud.get_product_characteristics(
            store_product.id, modifiers=True
        )
        if modifiers:
            modifiers_data = []
            modifiers_values = []
            for modifier, modifier_value in modifiers:
                modifiers_data.append(
                    {
                        "id": modifier.id,
                        "name": modifier.name,
                        "value": modifier_value.value
                    }
                )
                modifiers_values.append(modifier_value.value)

            product_.update(
                modifiers=modifiers,
                modifiers_str=" " + " ".join(modifiers_values),
            )
        data.append(product_)
    return data


async def get_products_text(data: list[dict]) -> str:
    data_ = list()
    for product in data:
        text = (
            f'[{product["product_id"]}] <a href="{product["product_url"]}">'
            f'{product["name"]} {product["modifiers_str"]}</a> '
            f'{product["price"]} x {product["quantity"]} = '
            f'{product["before_loyalty_sum"]} {product["currency"]}'
            f'{product["discount"]}'
        )
        if product["attributes"]:
            text += "\n"
            for attr in product["attributes"]:
                text += (f'+ {attr["name"]} (x'
                         f'{int(attr["quantity"]) * int(product["quantity"])})\n')
        data_.append(text)
    return "\n".join(data_)


async def get_payment_delivery_text(order: StoreOrder, group: Group, lang: str):
    shipment = await crud.get_order_shipment(order.id)

    texts = []

    payment_text = await get_payment_text(order, shipment, group, lang)
    texts.append(payment_text)

    if not shipment.base_type == ShipmentType.NO_DELIVERY.value:
        delivery_text = await get_store_order_delivery_text(
            order, shipment, group, lang
        )
        texts.append(delivery_text)

    desired_delivery_text = await get_desired_delivery_text(order, lang)
    if desired_delivery_text:
        texts.append(desired_delivery_text)

    text = "\n".join(texts)
    text = text.replace("\n\n", "\n")
    return text


async def get_payment_text(
        order: StoreOrder, shipment: OrderShipment, group: Group | None, lang: str
):
    texts = []

    payment_method_field_name = await f(
        "store brand payment method notifications text", lang
    )
    payment_cost = 0

    order_payment = await StoreOrderPayment.get(order_id=order.id)

    payment_method_name, payment_method_label_comment, _, _ = await get_payment_info(
        shipment, order_payment, group, lang,
        total_sum_str=format_currency(
            round(order.sum_to_pay / 100, 2),
            order.currency or group.currency,
            locale=group.lang,
        )
    )

    texts.append(": ".join((payment_method_field_name, payment_method_name)))

    if payment_cost:
        payment_cost_field_name = await f(
            "store brand payment cost notifications text", lang
        )
        if not group:
            group = await crud.get_group_by_store(order.store_id)
        payment_cost_str = format_currency(
            payment_cost, order.currency, locale=group.lang
        )
        texts.append(": ".join((payment_cost_field_name, payment_cost_str)))

    if payment_method_label_comment:
        texts.append(
            ": ".join((payment_method_label_comment, order_payment.comment or "-"))
        )

    return "\n".join(texts)
