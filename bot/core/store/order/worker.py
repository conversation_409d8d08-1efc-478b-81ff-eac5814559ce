import logging

import schemas
from db import DBSession

from utils.processes_manager.background_worker import LoopBackgroundWorker

from .new_orders_checker_service import NewOrdersChecker

logger = logging.getLogger("error.new_orders_checker.worker")


class NewOrdersCheckerWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "New Orders Checker"
    DEFAULT_TIMEOUT = NewOrdersChecker.worker_delay

    async def iteration(self):
        with DBSession():
            try:
                service = NewOrdersChecker()
                res: list[schemas.NewOrdersCheckerWorkerResultItem] = await service.process()
                if res:
                    for item in res:
                        if not item.success:
                            logger.error(
                                f"Transaction {item.transaction_id} was not canceled for order {item.order_id}"
                            )
            except Exception as error:
                logger.error(error, exc_info=True)
