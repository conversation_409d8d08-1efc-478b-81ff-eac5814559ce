from incust_api.api import term

import schemas
from core.loyalty.incust_api import incust
from db import crud
from db.models import LoyaltySettings, StoreOrder
from loggers import JSONLogger
from utils.platform_admins import send_message_to_platform_admins


class NewOrdersChecker:
    worker_delay = 120

    def __init__(self):
        self.limit = 10
        self.hours_ago = 1
        self.logger = JSONLogger(
            "orders_checker"
        )

    async def process(self) -> list[schemas.NewOrdersCheckerWorkerResultItem]:
        orders = await crud.get_new_orders_for_cancel_transaction(
            self.limit, self.hours_ago
        )
        res = []
        for order in orders:
            if (not order.auto_cancelled_loyalty_transaction and not order.status_pay
                                                                     == "payed"):
                result = await self.__cancel_transaction(order)
                if result.success and not result.skip:
                    await order.update(auto_cancelled_loyalty_transaction=True)
                res.append(result)

        return res

    async def __cancel_transaction(
            self, order: StoreOrder
    ) -> schemas.NewOrdersCheckerWorkerResultItem:
        # Отримуємо пов'язаний invoice
        invoice = await order.get_invoice()

        # Перевіряємо чи транзакція лояльності вже завершена
        if invoice and invoice.is_loyalty_transaction_completed:
            return schemas.NewOrdersCheckerWorkerResultItem(
                transaction_id=invoice.incust_transaction_id or "",
                success=True,
                order_id=order.id,
                skip=True,
            )

        # Отримуємо transaction_id з invoice
        transaction_id = None
        if invoice:
            transaction_id = invoice.incust_transaction_id

        if transaction_id:
            brand = await crud.get_brand_by_store(order.store_id)

            # Отримуємо налаштування лояльності
            loyalty_settings  = await LoyaltySettings.get(invoice.loyalty_settings_id) \
                if invoice and invoice.loyalty_settings_id else \
            await crud.get_loyalty_settings_for_context(
                "store",
                schemas.LoyaltySettingsData(
                    brand_id=brand.id,
                    store_id=order.store_id,
                    profile_id=brand.group_id,
                )
            )

            debug_data = {
                "transaction_id": transaction_id,
                "order_id": order.id,
                "invoice_id": invoice.id if invoice else None
            }

            try:
                if loyalty_settings:
                    async with incust.term.CheckTransactionsApi(
                            loyalty_settings
                    ) as api:
                        await api.transaction_cancel(
                            term.m.TransactionCancelRequest(
                                transaction_id=transaction_id,
                                comment="Auto-cancelled by orders checker"
                            )
                        )
            except Exception as e:
                self.logger.error(
                    f"orders_checker canceling transaction error {repr(e)}", debug_data,
                )
                adm_msg = "orders_checker canceling transaction error"
                adm_msg += f"\ntransaction_id:{transaction_id}"
                adm_msg += f"\norder_id:{order.id}"
                adm_msg += f"\nerror:{str(e)}"

                await send_message_to_platform_admins(adm_msg)

        return schemas.NewOrdersCheckerWorkerResultItem(
            transaction_id=transaction_id,
            success=True,
            order_id=order.id,
        )
