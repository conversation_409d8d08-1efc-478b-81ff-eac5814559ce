from db.models import Payment
from utils.text import f


async def get_pmt_info(order_id: int, lang: str) -> str:
    pmt_info = ''
    pmt_info_db = await Payment.get_pmt_info(order_id)
    pmt_info_ = []
    if pmt_info_db:
        if pmt_info_db.get('date'):
            pmt_info_.append(
                '{}: {}'.format(
                    await f('store brand notification order pmt date', lang), pmt_info_db.get('date', '')
                )
            )
        if pmt_info_db.get('status'):
            pmt_info_.append(
                '{}: {}'.format(
                    await f('store brand notification order pmt status', lang), pmt_info_db.get('status', '')
                )
            )
        if pmt_info_db.get('card_mask'):
            pmt_info_.append(
                '{}: {}'.format(
                    await f('store brand notification order pmt card', lang), pmt_info_db.get('card_mask', '')
                )
            )
        if pmt_info_db.get('amount'):
            pmt_info_.append(
                '{}: {} {}'.format(
                    await f('store brand notification order pmt amount', lang),
                    pmt_info_db.get('amount', ''),
                    pmt_info_db.get('currency', '')
                )
            )
        if pmt_info_db.get('description'):
            pmt_info_.append(
                '{}: {}'.format(
                    await f('store brand notification order pmt desc', lang), pmt_info_db.get('description', '')
                )
            )
        if pmt_info_db.get('type'):
            pmt_info_.append(
                '{}: {}'.format(
                    await f('store brand notification order pmt type', lang), pmt_info_db.get('type', '')
                )
            )
        if pmt_info_db.get('ext_pmt_id'):
            pmt_info_.append(
                '{}: {}'.format(
                    await f('store brand notification order pmt ext id', lang), pmt_info_db.get('ext_pmt_id', '')
                )
            )
        if pmt_info_:
            pmt_info_.insert(0, '\n- - - - - - - -')
            pmt_info = '\n'.join(pmt_info_)
    return pmt_info
