from abc import ABC

from psutils.exceptions import ErrorWithTextVariable


class OrderExportError(ErrorWithTextVariable, ABC):

    def __init__(self, message: str, **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"export error: {self.message}"


class OrderExportUnknownError(OrderExportError):
    text_variable = "order export unknown error"

    def __init__(self, **kwargs):
        super().__init__("Export failed with unknown error", **kwargs)


class ExportCheckerError(OrderExportError):
    def __init__(self):
        super().__init__("Checker failed with error")
