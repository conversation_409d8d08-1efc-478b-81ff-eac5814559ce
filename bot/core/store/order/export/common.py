from __future__ import annotations

from datetime import timedelta
from typing import Protocol, TypedDict

from aiogram import types

from psutils.convertors import interval_to_str

from typing_extensions import Literal

from utils.text import f

StatusTypeLiteral = Literal["not_started", "processing", "done", "error"]


class OnStatusUpdatedType(Protocol):

    async def __call__(self, status: StatusTypeLiteral, time_passed: timedelta):
        ...


class ExporterReturnType(TypedDict):
    status: StatusTypeLiteral
    time_passed: timedelta
    result: str
