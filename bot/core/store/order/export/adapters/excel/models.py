from datetime import datetime
from typing import TypeVar

from psutils.convertors import datetime_to_str

from core.adapters import CellsData
from core.adapters.excel import BaseExcelModel, Field


class ExcelModel(BaseExcelModel, base=True):

    def __init_subclass__(cls, **kwargs):
        cls._allow_custom_fields = kwargs.pop("allow_custom_fields", False)
        cls._allowed_custom_fields = kwargs.pop("allowed_custom_fields", "*")

        if cls._fields is None:
            cls._fields = {}

    @classmethod
    def get_schema_variable(cls):
        return super().get_schema_variable("store brand order export")

    @classmethod
    async def make_headers(cls, cells: CellsData, lang: str):
        verbose_names = await cls.get_verbose_names(lang)
        col_id = 1
        for field in cls.__get_fields_for_saving__():
            verbose_name = verbose_names[field.name]
            cells.add(1, col_id, verbose_name)
            col_id += 1

    async def make_row_data(self, cells: CellsData):
        fields = self.__get_fields_for_saving__()

        col_id = 1
        for field in fields:
            value = self.values.get(field.name)

            if isinstance(value, datetime):
                value = datetime_to_str(value)
            elif isinstance(value, list):
                value = "\n".join(value)

            cells.add(self.row_number, col_id, value)
            col_id += 1

        if self._allow_custom_fields and self.custom_fields:
            for name, value in self.custom_fields.items():
                col_id = cells.find_column_by_name(name)
                if col_id:
                    cells.add(self.row_number, col_id, value)
                else:
                    col_id = cells.max_column + 1
                    cells.add(1, col_id, name)
                    cells.add(self.row_number, col_id, value)


ModelType = TypeVar("ModelType", bound="ExcelModel")


class Order(ExcelModel):
    order_id: int = Field("store order export excel order id field", required=True)
    store_name: str = Field("store order export excel store name field", required=True)
    created_date: datetime = Field("store order export excel created date field", required=True)
    status: str = Field("store order export excel status field", required=True)
    status_pay: str = Field("store order export excel status pay field", required=True)

    user_name: str = Field("store order export excel user name field", required=True)
    phone: str = Field("store order export excel phone field")
    email: str = Field("store order export excel email field")
    telegram_link: str = Field("store order export excel telegram link field")

    billing_address: str = Field("store order export excel billing address field")
    payment_method: str = Field("store order export excel payment method field")
    payment_comment: str = Field("store order export excel payment comment field")
    shipment: str = Field("store order export excel delivery field")
    shipment_comment: str = Field("store order export excel shipment comment field")
    shipping_status: str = Field("store order export excel shipping status field")

    products: list[str] | None = Field("store order export EXCEL products field")

    type: str = Field("store order export excel type field", default="regular")


class Product(ExcelModel, allow_custom_fields=True):
    order_id: int = Field("store order export excel order id field", required=True)
    store_name: str = Field("store order export excel store name field", required=True)
    created_date: datetime = Field("store order export excel created date field", required=True)
    status: str = Field("store order export excel status field", required=True)
    status_pay: str = Field("store order export excel status pay field", required=True)

    user_name: str = Field("store order export excel user name field", required=True)
    phone: str = Field("store order export excel phone field")
    email: str = Field("store order export excel email field")
    telegram_link: str = Field("store order export excel telegram link field")

    billing_address: str = Field("store order export excel billing address field")
    payment_method: str = Field("store order export excel payment method field")
    payment_comment: str = Field("store order export excel payment comment field")
    shipment: str = Field("store order export excel delivery field")
    shipment_comment: str = Field("store order export excel shipment comment field")
    shipping_status: str = Field("store order export excel shipping status field")

    product_id: int = Field("store order export excel product id field", required=True)
    name: str = Field("store order export excel product name field", required=True)
    description: str | None = Field("store order export excel product description field")
    image_url: str | None = Field("store order export excel product image url field")
    price: float = Field("store order export excel product price field", required=True)
    price_with_attributes: float = Field("store order export excel product price with attributes field", required=True)
    count: int = Field("store order export excel product count field", required=True)
    before_loyalty_sum: float | None = Field("store order export excel product before loyalty sum field")
    discount_sum: float | None = Field("store order export excel product discount sum field")
    bonuses_redeemed: float | None = Field("store order export excel product bonuses redeemed field")
    total_sum: float = Field("store order export excel product total sum field", required=True)
    attributes: list[str] | None = Field("store order export excel product attributes field")


MODELS = {
    Order.__name__: Order,
    Product.__name__: Product,
}
