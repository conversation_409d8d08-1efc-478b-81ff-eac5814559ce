from abc import ABC
from typing import Type

from core.adapters import CellsData
from core.adapters.excel import BaseExcelCoreAdapter

from .models import MODELS, ModelType

from ..base import BaseAdapter


class BaseExcelAdapter(BaseExcelCoreAdapter, BaseAdapter, ABC):

    async def get_cells_to_export(self, data: dict):
        cells_to_export: dict[str, CellsData] = {}

        for name, sheet_data in data.items():
            model: Type[ModelType] = MODELS.get(name)
            if not model:
                continue

            cells: CellsData = CellsData()
            title = await model.get_schema_name(self.lang)
            cells_to_export[title] = cells

            await model.make_headers(cells, self.lang)

            for row_id, row_data in enumerate(sheet_data):
                row_data = row_data.dict()

                custom_fields = None
                if "characteristics" in row_data:
                    custom_fields = row_data.pop("characteristics", None)
                    custom_fields = {
                        custom_field["name"]: custom_field["value"]
                        for custom_field in custom_fields
                    }

                row = model(
                    row_id,
                    values=row_data,
                    custom_fields=custom_fields,
                )

                await row.make_row_data(cells)

        return cells_to_export
