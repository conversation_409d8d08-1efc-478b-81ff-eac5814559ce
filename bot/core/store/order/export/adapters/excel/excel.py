import os
from typing import Any

import config as cfg

from core.adapters.styles.excel import ExcelColorMixin

from openpyxl.workbook import Workbook
from openpyxl.worksheet.worksheet import Worksheet

from .base import BaseExcelAdapter

from ...types import OrderExportExternalTypeEnum


class ExcelAdapter(ExcelColorMixin, BaseExcelAdapter, external_type=OrderExportExternalTypeEnum.EXCEL.value):

    def __init__(self, lang: str, *args, **kwargs):
        path_to_save: str = os.path.join(cfg.STATIC_DB, "documents")
        super().__init__(lang, *args, **{**kwargs, "path_to_save": path_to_save})

        self.workbook: Workbook | None = None

    async def save_data(self, data: dict) -> Any:
        wb: Workbook = Workbook()
        ws = wb.active
        wb.remove(ws)

        to_export = await self.get_cells_to_export(data)
        for title, cells_data in to_export.items():
            wb.create_sheet(title)
            ws: Worksheet = wb[title]

            cells = cells_data.get_data()
            for cell in cells:
                ws.cell(row=cell.row_id, column=cell.col_id, value=cell.value)

            color_id, order_id = 1, None
            for row_data in cells_data.get_cells().values():
                if order_id != row_data.get(1).value:
                    order_id = row_data.get(1).value
                    color_id = 2 if color_id == 1 else 1
                await self.format_cells(
                    ws, list(row_data.values()),
                    background_color=getattr(self, f"color{color_id}"),
                )

        return self.__save_excel_file__(wb)
