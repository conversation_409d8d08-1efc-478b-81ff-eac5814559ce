from typing import Any

from gspread import Worksheet

from core.adapters.sheets import BaseSheetsCoreAdapter
from core.adapters.sheets.exceptions import SheetsDocumentError
from core.adapters.styles.sheets import SheetsColorMixin
from utils.google import get_sheet_name, get_sheet_url
from ..excel.base import BaseExcelAdapter
from ...types import OrderExportExternalTypeEnum


class SheetsAdapter(
    BaseSheetsCoreAdapter, SheetsColorMixin, BaseExcelAdapter,
    external_type=OrderExportExternalTypeEnum.SHEETS.value
):

    def __init__(self, *args, **kwargs):
        self.manager_user_id: int = kwargs.get("manager_user_id")
        super().__init__(*args, **kwargs)

    async def save_data(self, data: dict) -> Any:
        client = await self.client
        if not client:
            document_name = await get_sheet_name(self.sheets)
            raise SheetsDocumentError(document_name, lang=self.lang)

        to_export = await self.get_cells_to_export(data)

        for title, cells_data in to_export.items():
            worksheet: Worksheet = await client.get_worksheet_by_title(title)
            if worksheet:
                client.use_worksheet(worksheet)
                worksheet = await self.backup(worksheet, self.manager_user_id)
                await client.clear_worksheet()
            else:
                await client.create_and_use_worksheet(title)

            await client.write_rows(cells_data.get_sheets_data(cells_data.max_column))
            await client.freeze(rows=1)

            color_id, order_id = 1, None
            rows_by_color = {}
            worksheet = await client.get_worksheet_by_title(title)
            for row_data in cells_data.get_cells().values():
                if order_id != row_data.get(1).value:
                    order_id = row_data.get(1).value
                    color_id = 2 if color_id == 1 else 1

                if color_id not in rows_by_color:
                    rows_by_color[color_id] = []
                rows_by_color[color_id].extend(list(row_data.values()))

            for color_id, rows in rows_by_color.items():
                await self.format_cells(
                    worksheet, rows,
                    background_color=getattr(self, f"color{color_id}"),
                )

        return get_sheet_url(self.sheets)
