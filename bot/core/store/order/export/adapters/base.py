from __future__ import annotations

import abc
import inspect
from typing import Any, Type

from core.adapters import BaseAdapterType, BaseCoreAdapter

from psutils.text import paschal_case_to_snake_case


class StoreOrderAdapterType(BaseAdapterType):

    async def save_data(self, data: dict) -> Any:
        ...


class BaseAdapter(BaseCoreAdapter, abc.ABC):
    _adapters: dict[str, Type[BaseAdapter]] = {}

    def __init_subclass__(cls, **kwargs):
        if not inspect.isabstract(cls):
            adapter_name = paschal_case_to_snake_case(cls.__name__.replace("Adapter", ""))
            if adapter_name in cls._adapters:
                raise TypeError(f"adapter with name {cls.__name__} has already been registered")
            cls._adapters[adapter_name] = cls
        super().__init_subclass__(**kwargs)

    @abc.abstractmethod
    async def save_data(self, data: dict) -> Any:
        raise NotImplementedError
