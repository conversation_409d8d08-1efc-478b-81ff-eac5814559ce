from datetime import datetime

from pydantic import BaseModel
import schemas


class BaseOrder(BaseModel):
    order_id: int
    store_name: str
    created_date: datetime
    status: str
    status_pay: str

    user_name: str
    phone: str | None
    email: str | None
    telegram_link: str | None

    billing_address: str | None
    payment_method: str | None
    payment_comment: str | None
    shipment: str | None
    shipment_comment: str | None
    shipping_status: str | None

    type: schemas.OrderType = "regular"
