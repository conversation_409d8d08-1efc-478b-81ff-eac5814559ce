from .base import BaseOrder

from .characteristic import Characteristic


class OrderExportProduct(BaseOrder):
    product_id: str
    name: str
    description: str | None
    image_url: str | None
    price: float
    price_with_attributes: float
    count: int
    before_loyalty_sum: float | None = None
    discount_sum: float | None = None
    bonuses_redeemed: float | None = None
    discount_and_bonuses_sum: float | None = None
    total_sum: float
    attributes: list[str] | None = None
    characteristics: list[Characteristic] | None = None
