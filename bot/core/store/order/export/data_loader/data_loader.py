from abc import ABC, abstractmethod
from datetime import date

from db import crud
from db.models import Store, StoreCharacteristic, StoreCharacteristicValue, StoreProduct
from utils.text import f
from .maker import DataMaker
from .. import schemas


class BaseDataLoader(ABC):
    def __init__(
            self, manager_user_id: int, lang: str,
            date_from: date, date_to: date,
            store_order_status: list[str] | None = None,
            locations: list[str] | None = None,
            bots: list[int] | None = None,
            groups: list[int] | None = None,
            tags: list[int] | None = None,
            search_text: str | None = None,
            group_id: int | None = None,
            **_
    ):
        self.manager_user_id: int = manager_user_id
        self.lang: str = lang
        self.date_from: date = date_from
        self.date_to: date = date_to
        self.store_order_status: list[str] | None = store_order_status
        self.locations: list[str] | None = locations
        self.bots: list[int] | None = bots
        self.groups: list[int] | None = groups
        self.tags: list[int] | None = tags
        self.search_text: str | None = search_text
        self.group_id: int | None = group_id

        self.orders_products_schemas: dict[int, list[schemas.OrderExportProduct]] = {}

    @abstractmethod
    async def load(self) -> dict:
        raise NotImplementedError


class DataLoader(BaseDataLoader):

    async def _load_base_orders(self) -> list[schemas.BaseOrder]:
        orders = await crud.get_store_orders_for_export(
            self.manager_user_id,
            store_order_status=self.store_order_status,
            locations=self.locations,
            bots=self.bots,
            groups=self.groups,
            tags=self.tags,
            search_text=self.search_text,
            group_id=self.group_id,
            date_from=self.date_from,
            date_to=self.date_to,
        )

        data: list[schemas.BaseOrder] = []
        for order in orders:
            store = await Store.get(order.store_id)
            maker = DataMaker(order, self.lang)
            data.append(
                schemas.BaseOrder(
                    order_id=order.id,
                    store_name=store.name,
                    created_date=order.create_date,
                    status=await f(f"store brand order status {order.status} text", self.lang),
                    status_pay=await f(f"store brand order status pay {order.status_pay} text", self.lang),
                    user_name=await maker.get("user_name"),
                    phone=order.phone,
                    email=order.email,
                    telegram_link=await maker.get("telegram_link"),
                    billing_address=await maker.get("billing_address"),
                    payment_method=await maker.get("payment_method"),
                    payment_comment=await maker.get("payment_comment"),
                    shipment=await maker.get("shipment"),
                    shipment_comment=await maker.get("shipment_comment"),
                    shipping_status=await maker.get("shipping_status"),
                    type=order.type,
                )
            )

        return data

    async def _load_products(self, orders: list[schemas.BaseOrder]) -> list[schemas.OrderExportProduct]:
        data: list[schemas.OrderExportProduct] = []

        for order in orders:
            order_products_schemas: list[schemas.OrderExportProduct] = []
            order_products = await crud.get_order_products(order.order_id)
            for order_product in order_products:
                db_attributes = await crud.get_order_product_attributes(order_product.id)
                attributes: list[schemas.Attribute] = []
                for order_attribute, _ in db_attributes:
                    attributes.append(
                        schemas.Attribute(
                            attribute_group_name=order_attribute.group_name,
                            attribute_name=order_attribute.name,
                            count=order_attribute.quantity,
                            price=round(order_attribute.price_impact / 100, 2),
                            total_sum=round((order_attribute.price_impact * order_attribute.quantity) / 100, 2),
                        )
                    )

                product = await StoreProduct.get(order_product.product_id)
                image_url = await product.media_url

                characteristics: list[schemas.Characteristic] = []
                for characteristic_value in await StoreCharacteristicValue.get_list(product_id=product.id):
                    characteristic = await StoreCharacteristic.get(characteristic_value.characteristic_id)
                    characteristics.append(
                        schemas.Characteristic(
                            name=characteristic.name,
                            value=characteristic_value.value,
                        )
                    )

                order_products_schemas.append(
                    schemas.OrderExportProduct(
                        **order.dict(),
                        product_id=product.product_id,
                        name=order_product.name,
                        description=product.description,
                        image_url=image_url,
                        price=round(order_product.price / 100, 2),
                        price_with_attributes=round(order_product.price_with_attributes / 100, 2),
                        count=order_product.quantity,
                        before_loyalty_sum=round(order_product.before_loyalty_sum / 100, 2),
                        discount_sum=round(order_product.discount_sum / 100, 2),
                        bonuses_redeemed=round(order_product.bonuses_redeemed / 100, 2),
                        discount_and_bonuses_sum=round(order_product.discount_and_bonuses_sum / 100, 2),
                        total_sum=round(order_product.total_sum / 100, 2),
                        attributes=[
                            ": ".join(
                                [
                                    attribute.attribute_group_name,
                                    attribute.attribute_name,
                                    f"{attribute.count}x{attribute.price}={attribute.total_sum}",
                                ]
                            ) for attribute in attributes
                        ],
                        characteristics=characteristics,
                    )
                )

            self.orders_products_schemas[order.order_id] = order_products_schemas
            data.extend(order_products_schemas)
        return data

    async def _load_orders(self, orders: list[schemas.BaseOrder]) -> list[schemas.Order]:
        data: list[schemas.Order] = []

        for order in orders:
            order_products = self.orders_products_schemas[order.order_id]
            order_products_texts: list[str] = []
            for order_product in order_products:
                if order_product.discount_and_bonuses_sum:
                    order_products_texts.append(
                        " ".join(
                            [
                                f"[{order_product.product_id}]",
                                order_product.name,
                                "=".join(
                                    [
                                        f"{order_product.count}x{order_product.price_with_attributes}",
                                        f"{order_product.before_loyalty_sum}-{order_product.discount_and_bonuses_sum}",
                                        f"{order_product.total_sum}",
                                    ]
                                ),
                            ]
                        )
                    )
                else:
                    order_products_texts.append(
                        " ".join(
                            [
                                f"[{order_product.product_id}]",
                                order_product.name,
                                "=".join(
                                    [
                                        f"{order_product.count}x{order_product.price_with_attributes}",
                                        f"{order_product.total_sum}",
                                    ]
                                ),
                            ]
                        )
                    )

            data.append(
                schemas.Order(
                    **order.dict(),
                    products=order_products_texts,
                )
            )

        return data

    async def _load(self) -> dict[str, list]:
        base_orders = await self._load_base_orders()
        products = await self._load_products(base_orders)
        orders = await self._load_orders(base_orders)
        return {
            "Order": orders,
            "Product": products,
        }

    async def load(self) -> dict[str, list]:
        try:
            data = await self._load()
        except Exception as e:
            raise e

        return data
