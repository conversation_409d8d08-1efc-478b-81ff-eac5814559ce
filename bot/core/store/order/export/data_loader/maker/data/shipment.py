from abc import ABC

from db import crud
from db.models import OrderShipment, StoreOrder
from service.store.functions import get_store_order_delivery_text, get_desired_delivery_text

from utils.text import f

from .base import BaseMaker


class BaseShipmentMaker(BaseMaker, ABC):

    @classmethod
    async def get_custom_shipment(cls, order: StoreOrder) -> OrderShipment | None:
        return await OrderShipment.get(store_order_id=order.id)


class ShipmentMaker(BaseShipmentMaker):

    @classmethod
    async def get(cls, order: StoreOrder, lang: str) -> str | None:
        shipment = await cls.get_custom_shipment(order)

        if shipment:
            shipment_str = shipment.name
        else:
            shipment = await crud.get_order_shipment(order.id)
            delivery_str = await get_store_order_delivery_text(order, shipment, None, lang)
            desired_date_time_str = await get_desired_delivery_text(order, lang)
            shipment_str = "\n".join([delivery_str, desired_date_time_str])

        return shipment_str


class ShipmentCommentMaker(BaseShipmentMaker):

    @classmethod
    async def get(cls, order: StoreOrder, _: str) -> str | None:
        shipment = await cls.get_custom_shipment(order)

        if shipment:
            shipment_comment = shipment.comment
        else:
            shipment_comment = order.address_comment

        return shipment_comment


class ShippingStatusMaker(BaseMaker):

    @classmethod
    async def get(cls, order: StoreOrder, lang: str) -> str | None:
        status = await crud.get_last_order_status(order.id)
        return await f(f"email store order {status.status} status text", lang) if status else None
