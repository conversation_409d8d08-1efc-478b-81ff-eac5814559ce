from db.models import StoreOrder, User

from .base import BaseMaker


class UserNameMaker(BaseMaker):

    @classmethod
    async def get(cls, order: StoreOrder, lang: str) -> str | None:
        first_name = order.first_name
        last_name = order.last_name
        full_name = first_name if first_name else None
        if last_name:
            full_name = " ".join([full_name, last_name]) if full_name else last_name

        if not full_name:
            user = await User.get_by_id(order.user_id)
            full_name = user.name
        return full_name


class TelegramLinkMaker(BaseMaker):

    @classmethod
    async def get(cls, order: StoreOrder, lang: str) -> str | None:
        user = await User.get_by_id(order.user_id)
        return f"https://t.me/{user.username}" if user and user.username else None
