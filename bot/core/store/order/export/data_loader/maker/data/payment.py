from abc import ABC

from db import crud
from db.models import OrderCustomPayment, StoreOrder
from core.store.order.messages_funcs import get_payment_text

from .base import BaseMaker


class BasePaymentMethodMaker(BaseMaker, ABC):

    @classmethod
    async def get_custom_payment_method(cls, order: StoreOrder) -> OrderCustomPayment | None:
        return await OrderCustomPayment.get(store_order_id=order.id)


class PaymentMethodMaker(BasePaymentMethodMaker):

    @classmethod
    async def get(cls, order: StoreOrder, lang: str) -> str | None:
        custom_payment_method = await cls.get_custom_payment_method(order)

        if custom_payment_method:
            payment_method_str = custom_payment_method.name
        else:
            shipment = await crud.get_order_shipment(order.id)
            payment_method_str = await get_payment_text(order, shipment, None, lang)

        return payment_method_str


class PaymentCommentMaker(BasePaymentMethodMaker):

    @classmethod
    async def get(cls, order: StoreOrder, lang: str) -> str | None:
        custom_payment_method = await cls.get_custom_payment_method(order)

        if custom_payment_method:
            payment_comment = custom_payment_method.comment
        else:
            payment_comment = order.comment

        return payment_comment
