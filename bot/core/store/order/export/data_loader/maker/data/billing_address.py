from db.models import StoreOrder, StoreOrderBillingAddress

from utils.text import fl

from .base import BaseMaker


class BillingAddressMaker(BaseMaker):

    @classmethod
    async def get(cls, order: StoreOrder, lang: str) -> str | None:
        billing_text = None
        billing_address = await StoreOrderBillingAddress.get(order_id=order.id)
        if billing_address:
            billing_locales = await fl({
                "store brand billing settings button": {},
                "web store form name label text": {},
                "web store form lastname label text": {},
                "web store billing form company name label": {},
                "web store billing form vat number label": {},
                "web store billing form reg number label": {},
                "web store billing form country label": {},
                "web store billing form state label": {},
                "web store billing form city label": {},
                "web store billing form zip label": {},
                "web store billing form address one label": {},
                "web store billing form address two label": {},
                "default phone var name": {},
            }, lang)

            billing_text = f"{billing_locales[0]}:"
            if billing_address.first_name:
                billing_text = "\n".join([billing_text, f"{billing_locales[1]}: {billing_address.first_name}"])
            if billing_address.last_name:
                billing_text = "\n".join([billing_text, f"{billing_locales[2]}: {billing_address.last_name}"])
            if billing_address.company_name:
                billing_text = "\n".join([billing_text, f"{billing_locales[3]}: {billing_address.company_name}"])
            if billing_address.vat_number:
                billing_text = "\n".join([billing_text, f"{billing_locales[4]}: {billing_address.vat_number}"])
            if billing_address.registration_number:
                billing_text = "\n".join([
                    billing_text, f"{billing_locales[5]}: {billing_address.registration_number}"
                ])
            if billing_address.country:
                billing_text = "\n".join([billing_text, f"{billing_locales[6]}: {billing_address.country}"])
            if billing_address.state:
                billing_text = "\n".join([billing_text, f"{billing_locales[7]}: {billing_address.state}"])
            if billing_address.city:
                billing_text = "\n".join([billing_text, f"{billing_locales[8]}: {billing_address.city}"])
            if billing_address.zip_code:
                billing_text = "\n".join([billing_text, f"{billing_locales[9]}: {billing_address.zip_code}"])
            if billing_address.address_1:
                billing_text = "\n".join([billing_text, f"{billing_locales[10]}: {billing_address.address_1}"])
            if billing_address.address_2:
                billing_text = "\n".join([billing_text, f"{billing_locales[11]}: {billing_address.address_2}"])
            if billing_address.phone_number:
                billing_text = "\n".join([billing_text, f"{billing_locales[12]}: {billing_address.phone_number}"])
        return billing_text
