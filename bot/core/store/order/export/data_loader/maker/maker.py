from db.models import StoreOrder

from .data import BaseMaker


class DataMaker:

    def __init__(self, order: StoreOrder, lang: str):
        self.order: StoreOrder = order
        self.lang: str = lang

    async def get(self, name: str) -> str:
        if name not in BaseMaker.get_maker_names():
            raise TypeError(f"Unknown data name {name}")

        maker = BaseMaker.get_maker(name)
        return await maker.get(self.order, self.lang)
