import datetime
import logging

from starlette import status as http_status

from core.incust.functions import (
    topup_account_with_store_order,
)
from core.invoice_loyalty.service import InvoiceLoyaltyService
from db import crud, db_func, sess
from db.models import (
    Invoice, OrderShipment, OrderShippingStatus,
    StoreCart, StoreOrder, StoreOrderPayment, User,
)
from loggers import JSONLogger
from schemas import (
    OrderShippingStatusEnum,
    StatusChangeInitiatedBy,
)
from utils.exceptions.error_with_http_status import ErrorWithHTTPStatus
from utils.log_func_exec_time import log_func_exec_time
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from ...billing.functions import record_billing_transaction_usage
from ...kafka.producer.functions import add_order_status_notification

debugger = logging.getLogger("debugger.order")


class StoreOrderNotFoundError(ErrorWithHTTPStatus):
    status_code = http_status.HTTP_404_NOT_FOUND
    text_variable = "store order not found error"

    def __init__(self, order_id: int):
        super().__init__(order_id=order_id)


class ChangeOrderStatusError(ErrorWithHTTPStatus):
    status_code = http_status.HTTP_400_BAD_REQUEST
    text_variable = "store order invalid status error text"

    def __init__(self, order_id: int, new_status: str, current_status: str):
        super().__init__(
            order_id=order_id, new_status=new_status, current_status=current_status
        )


class ChangeOrderSameStatusError(ErrorWithHTTPStatus):
    status_code = http_status.HTTP_400_BAD_REQUEST
    text_variable = "store order same status error text"

    def __init__(self, order_id: int, status: str):
        super().__init__(order_id=order_id, status=status)


@db_func
def process_order_status(
        store_order: StoreOrder,
        status: str,
        initiated_by: StatusChangeInitiatedBy,
        initiated_by_user_id: int | None = None,
        initiated_by_user: User | None = None,
        comment: str | None = None,
        source: str | None = None,
        is_full_bonuses_payment: bool = False,
        new_order_status_text: str | None = None,
        current_order_status_text: str | None = None,
) -> tuple[Invoice, OrderShippingStatus, bool, bool, bool, bool]:

    need_process_loyalty = False
    is_cancel = False
    is_payed_new_order = False
    is_unconfirmed_status_set = False

    invoice = Invoice.get_sync(store_order.invoice_id)

    shipment_base_type = None
    shipment = OrderShipment.get_sync(store_order_id=store_order.id)
    if shipment:
        shipment_base_type = shipment.base_type

    if status != OrderShippingStatusEnum.IN_TRANSIT.value:
        # check for the same status
        order_shipping_status = OrderShippingStatus.get_sync(
            store_order_id=store_order.id, status=status
        )
        if order_shipping_status:
            raise ChangeOrderSameStatusError(store_order.id, new_order_status_text)

    match status:
        case OrderShippingStatusEnum.OPEN_UNCONFIRMED.value:
            if store_order.status in (
                    OrderShippingStatusEnum.CLOSED.value,
                    OrderShippingStatusEnum.CANCELED.value,
                    OrderShippingStatusEnum.OPEN_CONFIRMED.value
            ):
                raise ChangeOrderStatusError(
                    store_order.id, new_status=status, current_status=store_order.status
                )

            store_order._status = OrderShippingStatusEnum.OPEN_UNCONFIRMED.value
            is_unconfirmed_status_set = True
        case OrderShippingStatusEnum.OPEN_CONFIRMED.value:
            if store_order.status in (
                    OrderShippingStatusEnum.CLOSED.value,
                    OrderShippingStatusEnum.CANCELED.value
            ):
                raise ChangeOrderStatusError(
                    store_order.id, new_order_status_text, current_order_status_text
                )

            store_order._status = OrderShippingStatusEnum.OPEN_CONFIRMED.value

        case OrderShippingStatusEnum.CLOSED.value:

            if store_order.status == OrderShippingStatusEnum.CANCELED:
                raise ChangeOrderStatusError(
                    store_order.id, new_order_status_text, current_order_status_text
                )

            store_order._status = OrderShippingStatusEnum.CLOSED.value

            if store_order.status_pay != OrderShippingStatusEnum.PAYED.value:

                set_order_payed(store_order, invoice)

                need_process_loyalty = True

        case OrderShippingStatusEnum.CANCELED.value:
            if store_order.status == OrderShippingStatusEnum.CLOSED.value:
                raise ChangeOrderStatusError(
                    store_order.id, new_order_status_text, current_order_status_text
                )

            store_order._status = OrderShippingStatusEnum.CANCELED.value
            is_cancel = True
            need_process_loyalty = True

        case OrderShippingStatusEnum.PAYED.value:
            if store_order.status in (
                    OrderShippingStatusEnum.CLOSED.value,
                    OrderShippingStatusEnum.CANCELED
            ):
                raise ChangeOrderStatusError(
                    store_order.id, new_order_status_text, current_order_status_text
                )

            if (store_order.status == OrderShippingStatusEnum.NEW.value or
                    is_full_bonuses_payment):
                store_order._status = OrderShippingStatusEnum.OPEN_UNCONFIRMED.value
                is_unconfirmed_status_set = True
                is_payed_new_order = True

                order_shipping_status_ = OrderShippingStatus(
                    store_order_id=store_order.id,
                    status=OrderShippingStatusEnum.OPEN_UNCONFIRMED.value,
                    initiated_by=initiated_by,
                    initiated_by_user_id=initiated_by_user_id,
                    initiated_by_user=initiated_by_user,
                    comment=comment,
                    source=source,
                )
                sess().add(order_shipping_status_)

            set_order_payed(store_order, invoice)
            need_process_loyalty = True

    order_shipping_status = OrderShippingStatus(
        store_order_id=store_order.id,
        status=status,
        initiated_by=initiated_by,
        initiated_by_user_id=initiated_by_user_id,
        initiated_by_user=initiated_by_user,
        comment=comment,
        source=source,
    )
    sess().add(order_shipping_status)

    if (status == OrderShippingStatusEnum.OPEN_CONFIRMED.value and shipment_base_type
            == 'delivery'):
        order_shipping_status = OrderShippingStatus(
            store_order_id=store_order.id,
            status=OrderShippingStatusEnum.WAIT_FOR_SHIP.value,
            initiated_by=initiated_by,
            initiated_by_user_id=initiated_by_user_id,
            initiated_by_user=initiated_by_user,
            comment=comment,
            source=source,
        )
        sess().add(order_shipping_status)

    sess().commit()
    # noinspection PyTypeChecker
    return (
        invoice, order_shipping_status,
        need_process_loyalty, is_cancel,
        is_payed_new_order,
        is_unconfirmed_status_set,
    )


def set_order_payed(store_order: StoreOrder, invoice: Invoice | None = None):
    store_order._status_pay = OrderShippingStatusEnum.PAYED.value
    if invoice and invoice.payer_fee:
        store_order.payer_fee = invoice.payer_fee
    else:
        store_order.payer_fee = 0
    store_order.paid_sum = store_order.sum_to_pay + store_order.payer_fee

    store_order_payment = StoreOrderPayment.get_sync(order_id=store_order.id)
    if store_order_payment:
        store_order_payment.update_sync(confirmed_datetime=datetime.datetime.utcnow())


async def process_loyalty(store_order: StoreOrder, is_cancel: bool):
    """
    Обробляє лояльність для замовлення.
    Тільки для скасування - успішна фіналізація тепер відбувається в invoice/payment.py
    """
    try:
        debugger.debug(
            f"Processing loyalty for order {store_order.id}, is_cancel: {is_cancel}"
        )
        
        # Обробляємо тільки скасування, успішна фіналізація тепер в payment.py
        if not is_cancel:
            debugger.debug(f"Success finalization now handled in payment.py for order {store_order.id}")
            return
        
        # Перевіряємо чи є Invoice для замовлення  
        if not store_order.invoice_id:
            debugger.debug(f"No invoice for order {store_order.id}")
            return
            
        invoice = await Invoice.get(store_order.invoice_id)
        if not invoice:
            debugger.debug(f"Invoice {store_order.invoice_id} not found for order {store_order.id}")
            return
            
        # Скасовуємо транзакцію лояльності через InvoiceLoyaltyService
        invoice_loyalty_service = InvoiceLoyaltyService()
        success = await invoice_loyalty_service.finalize_invoice_loyalty(
            invoice, is_cancel=True
        )
        
        if success:
            debugger.debug(f"Loyalty canceled successfully for order {store_order.id}")
        else:
            debugger.debug(f"No loyalty to cancel for order {store_order.id}")
            
    except Exception as ex:
        logging.error(f"Error in process_loyalty for order {store_order.id}: {ex}", exc_info=True)
        
        err_text = f"Loyalty processing error for order {store_order.id}: {str(ex)}"
        await send_message_to_platform_admins(err_text)


async def change_store_order_status(
        order_or_order_id: int | StoreOrder,
        status: str,
        initiated_by: StatusChangeInitiatedBy,
        comment: str | None = None,
        source: str | None = None,
        manager: User | None = None,
        initiated_by_user_id: int | None = None,  # specify one of
        initiated_by_user: User | None = None,  # specify one of
        ignore_session_id: int | None = None,
        is_full_bonuses_payment: bool = False, # можна передати об'єкт замовлення
) -> StoreOrder:
    debug_data = dict(locals())
    logger = JSONLogger("order", "Change status", str(order_or_order_id), status, debug_data)

    logger.debug("Started")

    store_order = None
    if isinstance(order_or_order_id, int):
        store_order = await StoreOrder.get(order_or_order_id)
    elif isinstance(order_or_order_id, StoreOrder):
        store_order = order_or_order_id

    if not store_order:
        raise StoreOrderNotFoundError(order_or_order_id)

    logger.add_data({"store_order": store_order})

    if initiated_by_user:
        lang = initiated_by_user.lang
    elif manager:
        lang = manager.lang
    else:
        user = await User.get_by_id(store_order.user_id)
        lang = user.lang

    new_order_status_text = await f(f"crm order {status} status", lang)
    current_order_status_text = await f(f"crm order {store_order.status} status", lang)

    # ФАЗА 1: Критичні операції (виконуються синхронно)
    with log_func_exec_time("process_order_status", logger):
        (
            invoice,
            order_shipping_status,
            need_process_loyalty,
            is_cancel,
            is_payed_new_order,
            is_unconfirmed_status_set,
        ) = await process_order_status(
            store_order, status, initiated_by, initiated_by_user_id, initiated_by_user,
            comment, source,
            is_full_bonuses_payment=is_full_bonuses_payment,
            new_order_status_text=new_order_status_text,
            current_order_status_text=current_order_status_text
        )

    logger.add_data(
        {
            "invoice": invoice,
            "order_shipping_status": order_shipping_status,
            "need_process_loyalty": need_process_loyalty,
            "is_cancel": is_cancel,
            "is_payed_new_order": is_payed_new_order,
        }
    )

    with log_func_exec_time("get_order_data", logger):
        (
            bot, brand, group,
            menu_in_store, store,
            order_user,
        ) = await crud.get_order_data(store_order)
        logger.add_data(
            {
                "bot": bot,
                "brand": brand,
                "group": group,
                "menu_in_store": menu_in_store,
                "store": store,
                "order_user": order_user,
            }
        )
        logger.debug('get_order_data:OK')
        lang = await order_user.get_lang(bot)

    # Обробка білінгу (критична операція)
    if is_unconfirmed_status_set:
        with log_func_exec_time("record_billing", logger):
            await record_billing_transaction_usage(group, store_order.sum_to_pay)

    # Очищення кошика (критична операція)
    if status in (
            OrderShippingStatusEnum.PAYED.value,
            OrderShippingStatusEnum.CLOSED.value
    ):
        if store_order.type != "gift" and store_order.type != "topup":
            if store_order.cart_id:
                cart = await StoreCart.get(store_order.cart_id)
                if cart:
                    await cart.clear()
                    logger.debug(
                        f"Cart {store_order.cart_id} cleared after order status "
                        f"changed to {status}"
                    )
            else:
                cart = await StoreCart.get_by_user(
                    store_order.user_id, store.id if store else None
                )
                if cart:
                    await cart.clear()
                    logger.debug(
                        f"User cart cleared after order status changed to {status}"
                    )

    if store_order.type == "topup" and status == "closed":
        await topup_account_with_store_order(store_order, brand, order_user, lang)
        logger.debug('topup_account_with_store_order:PL')

    # ФАЗА 2: Некритичні операції (виконуються асинхронно через Kafka)
    # Відправляємо повідомлення в Kafka для асинхронної обробки
    try:
        with log_func_exec_time("add_order_status_notification", logger):
            await add_order_status_notification(
                store_order_id=store_order.id,
                status=status,
                initiated_by=initiated_by,
                order_shipping_status_id=order_shipping_status.id,
                invoice_id=invoice.id if invoice else None,
                menu_in_store_id=menu_in_store.id if menu_in_store else None,
                need_process_loyalty=need_process_loyalty,
                is_cancel=is_cancel,
                is_payed_new_order=is_payed_new_order,
                comment=comment,
                source=source,
                ignore_session_id=ignore_session_id,
                lang=lang,
                logger=logger,
                no_error=True,
            )
            logger.debug('add_order_status_notification:OK')
    except Exception as err:
        logger.error("Error scheduling order status notifications", err)
        # Навіть якщо відправка в Kafka не вдалася, ми все одно повертаємо успішний результат,
        # оскільки головна операція (зміна статусу) була виконана

    logger.debug("change_store_order_status", "OK")
    return store_order
