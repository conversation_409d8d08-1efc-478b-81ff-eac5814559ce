from typing import Literal

import schemas
from core.store.exceptions import (
    ShipmentPricesMaximumOrderSumError, ShipmentPricesMaximumOrderSumNoDeliveryError,
    ShipmentPricesMinimumOrderSumError, ShipmentPricesMinimumOrderSumNoDeliveryError,
    ShipmentPricesNoPriceError,
    ShipmentPricesNoPriceForAddressError,
)
from db import crud
from db.models import (
    Brand, BrandCustomSettings, Group, ShipmentPrice, Store, StoreCustomSettings,
)
from schemas.store.types import ShipmentType
from utils.numbers import format_currency
from .prices import ShipmentPriceCalculator


class ShipmentConvertor:

    def __init__(
            self, brand: Brand,
            store_id: int | None = None,
            shipment_id: int | None = None,
            shipment_store_settings_id: int | None = None,
            order_sum: float = 0.0,
            address: str | None = None,
            lang: str | bool = False,
            is_next_price_cheaper: bool = False,
            address_lat: int | None = None,
            address_lng: int | None = None,
            purpose: Literal["prices", "price", "validate"] = "prices"
    ):
        self.brand = brand
        self.lang = lang

        self.calculator = ShipmentPriceCalculator(
            brand, store_id=store_id,
            shipment_id=shipment_id, order_sum=order_sum,
            address=address, lang=lang, is_next_price_cheaper=is_next_price_cheaper,
            store_settings_id=shipment_store_settings_id,
            address_lat=address_lat, address_lng=address_lng, purpose=purpose,
        )

    async def get_price_schema(
            self, shipment_price: ShipmentPrice
    ) -> schemas.ShipmentPrice:
        return schemas.ShipmentPrice.from_orm(shipment_price)

    async def get_price(self, for_data: bool = False) -> schemas.ShipmentPrice | None:
        all_zones = await self.get_all_zones()
        price = await self.calculator.get_price(bool(all_zones))
        if not price and self.calculator.prices and not for_data:
            await self.__try_raise_min_or_max_order_sum_error(
                self.brand.group_id, self.calculator.prices
            )

        if not price and not for_data:
            if self.calculator.zones and self.calculator.zones_prices:
                prices = []
                for zone in self.calculator.zones:
                    for price in self.calculator.zones_prices.get(zone.id, []):
                        prices.append(price)
                await self.__try_raise_min_or_max_order_sum_error(
                    self.brand.group_id, prices
                )

            settings = await StoreCustomSettings.get(self.calculator.store_settings_id)
            if settings and settings.is_shipment_zone:
                zones = await crud.get_shipment_zones(
                    brand_id=self.brand.id,
                    shipment_store_settings_id=self.calculator.store_settings_id
                )
                if zones and not price:
                    raise ShipmentPricesNoPriceForAddressError()
            raise ShipmentPricesNoPriceError()

        if not price and for_data:
            return None

        return await self.get_price_schema(price)

    async def get_next_price(self) -> schemas.ShipmentPrice | None:
        next_price = await self.calculator.get_next_price()
        return await self.get_price_schema(next_price) if next_price else None

    async def get_available_prices(self) -> list[schemas.ShipmentPrice] | None:
        return [
            await self.get_price_schema(price)
            for price in self.calculator.available_prices
        ]

    async def get_available_zones(self) -> list[schemas.ShipmentZone] | None:
        return [
            schemas.ShipmentZone(
                id=zone.id,
                name=zone.name,
                is_distance=zone.is_distance,
                is_polygon=zone.is_polygon,
                is_swap_coordinates=zone.is_swap_coordinates,
                distance=zone.distance,
                polygon=zone.polygon,
                prices=[
                    schemas.ShipmentPrice.from_orm(price)
                    for price in self.calculator.zones_prices.get(zone.id, [])
                ],
            ) for zone in self.calculator.zones
        ]

    async def get_all_zones(self) -> list[schemas.ShipmentZone] | None:
        zones = await crud.get_shipment_zones(
            brand_id=self.brand.id,
            shipment_store_settings_id=self.calculator.store_settings_id
        )
        if not zones:
            return None
        zones_prices = await crud.get_shipment_prices(
            self.brand.id, zone_ids=[zone.id for zone in zones]
        )
        return [
            schemas.ShipmentZone(
                id=zone.id,
                name=zone.name,
                is_distance=zone.is_distance,
                is_polygon=zone.is_polygon,
                is_swap_coordinates=zone.is_swap_coordinates,
                distance=zone.distance,
                polygon=zone.polygon,
                prices=[
                    schemas.ShipmentPrice.from_orm(price)
                    for price in zones_prices.get(zone.id, [])
                ],
            ) for zone in zones
        ]

    async def get_data(self):
        settings = await StoreCustomSettings.get(self.calculator.store_settings_id)
        if settings and not settings.is_shipment_zone:
            all_zones = []
        else:
            all_zones = await self.get_all_zones()
        return schemas.ShipmentPriceResultData(
            all_zones=all_zones,
            has_zones=bool(all_zones),
            select_price=await self.get_price(for_data=True),
            next_price=await self.get_next_price(),
            available_prices=await self.get_available_prices(),
            available_zones=await self.get_available_zones(),
            shipment_id=self.calculator.shipment_id,
        )

    async def check_any_prices_exist(self) -> bool:
        prices = await crud.get_shipment_prices(
            self.brand.id, settings_id=self.calculator.shipment_id
        )
        zones = await crud.get_shipment_zones(
            brand_id=self.brand.id,
            shipment_store_settings_id=self.calculator.store_settings_id
        )
        zones_with_prices = False
        settings = await StoreCustomSettings.get(self.calculator.store_settings_id)

        zones_prices = None
        if zones and settings and settings.is_shipment_zone:
            zones_prices = await crud.get_shipment_prices(
                self.brand.id,
                zone_ids=[zone.id for zone in zones],
                operation="count",
            )
            if zones_prices:
                zones_with_prices = True

        if zones and not prices and not zones_prices:
            await self.calculator.check_address()
            if not self.calculator.zones:
                raise ShipmentPricesNoPriceForAddressError()

        if prices or zones_with_prices:
            return True

        return False

    def __get_lowest_cost_in_prices(self, prices: list[ShipmentPrice]) -> float:
        valid_prices = [price for price in prices if price is not None]
        min_shipment_price = min(
            valid_prices, key=lambda price: price.minimum_order_amount
        )
        return min_shipment_price.minimum_order_amount

    def __get_highest_cost_in_prices(self, prices: list[ShipmentPrice]) -> float:
        valid_prices = [price for price in prices if price is not None]
        max_shipment_price = max(
            valid_prices, key=lambda price: price.maximum_order_amount
        )
        return max_shipment_price.maximum_order_amount

    async def __try_raise_min_or_max_order_sum_error(
            self, group_id: int, prices: list[ShipmentPrice]
    ):
        if len(prices) > 0:
            shipment = await BrandCustomSettings.get(self.calculator.shipment_id)
            currency = None
            group = await Group.get(group_id)

            if self.calculator.store_id:
                store = await Store.get(self.calculator.store_id)
                if store:
                    currency = store.currency
            if not currency and group:
                currency = group.currency

            lowest_minimum_order_amount = self.__get_lowest_cost_in_prices(prices)
            if lowest_minimum_order_amount > self.calculator.order_sum:
                min_sum = format_currency(
                    lowest_minimum_order_amount, currency, locale=group.lang
                )

                if shipment and shipment.base_type == ShipmentType.NO_DELIVERY.value:
                    raise ShipmentPricesMinimumOrderSumNoDeliveryError(min_sum=min_sum)
                raise ShipmentPricesMinimumOrderSumError(min_sum=min_sum)

            highest_maximum_order_amount = self.__get_highest_cost_in_prices(prices)
            if highest_maximum_order_amount < self.calculator.order_sum:
                max_sum = format_currency(
                    highest_maximum_order_amount, currency, locale=group.lang
                )

                if shipment and shipment.base_type == ShipmentType.NO_DELIVERY.value:
                    raise ShipmentPricesMaximumOrderSumNoDeliveryError(max_sum=max_sum)

                raise ShipmentPricesMaximumOrderSumError(max_sum=max_sum)
