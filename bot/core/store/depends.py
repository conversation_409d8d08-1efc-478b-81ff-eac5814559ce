from fastapi import Depends
from starlette.requests import Request
from starlette.websockets import WebSocket

from config import DEBUG
from core.api.depends import (
    get_current_bot, get_current_domains,
)
from db import crud
from db.models import Brand, ClientBot
from loggers import J<PERSON>NLogger


async def get_current_brand(
        request: Request = None,
        websocket: WebSocket = None,
        current_domains: list[str] = Depends(get_current_domains),
        # brand_id: int | None = None,
) -> Brand | None:
    obj = request or websocket

    brand_id = None

    if not brand_id:
        brand_id = obj.path_params.get("brand_id", obj.query_params.get("brand_id"))
    if not brand_id and request:
        try:
            data = await request.json()
            brand_id = data.get("brand_id")
        except:
            pass

    logger = JSONLogger(
        "brand", "Detect brand", str(brand_id), ",".join(current_domains), {
            "brand_id": brand_id,
            "current_domains": current_domains,
        }
    )

    brand = await crud.detect_brand(brand_id, current_domains)

    if DEBUG:
        logger.debug(
            "Detect brand result", {
                "brand": brand,
            }
        )

    if brand and request:
        request.state.brand_info = {
            "id": brand.id,
            "name": brand.name,
            "domain": brand.domain,
        }

    return brand


async def get_current_bot_with_brand(
        brand: Brand | None = Depends(get_current_brand),
        bot: ClientBot | str | None = Depends(get_current_bot),
):
    if not bot and brand:
        bot = await crud.get_bot_by_brand(brand.id)

    if isinstance(bot, ClientBot):
        ClientBot.set_current_bot_id(bot.id)
    return bot
