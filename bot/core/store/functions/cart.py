import hashlib

import schemas
from db import crud
from db.models import Group, StoreCart, StoreCartAttribute
from schemas import NotAvailableProduct
from .attribute import attribute_to_schema
from ..product.functions import product_to_schema


async def cart_to_schema(
        cart: StoreCart, lang: str,
        unavailable_products: list[NotAvailableProduct] | None = None
) -> schemas.CartSchema:
    store_id = cart.store_id
    unavailable_products_ids = (
        [item.product_id for item in unavailable_products]
        if unavailable_products else None
    )

    cart_products_data = await crud.get_cart_products_data(
        cart.id, unavailable_products_ids,
    )

    brand = await crud.get_brand_by_store(store_id)
    group = await Group.get(brand.group_id)

    schema = schemas.CartSchema(
        id=cart.id,
        store_id=cart.store_id,
        cart_products=[
            schemas.CartProductSchema(
                id=cart_product.id,
                quantity=cart_product.quantity,
                product=await product_to_schema(
                    product, store_id, lang, brand=brand, group=group,
                ),
                cart_attributes=await convert_cart_attributes_to_schema(
                    attributes, lang, group,
                ),
                floating_sum=round(cart_product.floating_sum / 100, 2),
            )
            for cart_product, product, attributes in cart_products_data
        ],
        hash="",
        unavailable_products=unavailable_products,
    )

    schema_json = schema.json(exclude={"hash"})
    json_bytes = schema_json.encode('utf-8')
    sha256_hash = hashlib.sha256(json_bytes).hexdigest()
    schema.hash = sha256_hash

    return schema


async def convert_cart_to_order_products(
        cart_id: int
) -> list[schemas.CreateOrderProduct]:
    cart_products = await crud.get_cart_products_data(cart_id)

    return [
        schemas.CreateOrderProduct(
            quantity=cart_product.quantity,
            product_id=product.id,
            floating_sum=cart_product.floating_sum,
            attributes=[schemas.CreateOrderAttribute(
                quantity=attribute.quantity,
                attribute_id=attribute.attribute_id,
                price_impact=attribute.attribute.price_impact,
            ) for attribute in attributes]
        ) for cart_product, product, attributes in cart_products
    ]


async def convert_cart_attributes_to_schema(
        cart_attributes: list[StoreCartAttribute],
        lang: str, group: Group,

) -> list[schemas.CartAttributeSchema]:
    return [
        schemas.CartAttributeSchema(
            id=cart_attribute.id,
            quantity=cart_attribute.quantity,
            attribute=await attribute_to_schema(
                group.is_translate, group.id,
                cart_attribute.attribute,
                lang, group.lang,
            )
        )
        for cart_attribute in cart_attributes
    ]
