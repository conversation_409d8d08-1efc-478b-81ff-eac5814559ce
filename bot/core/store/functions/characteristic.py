from psutils.translator.schemas import TranslateObjectData

import schemas
from db import crud
from db.crud.store.product.read import (
    LoadedModifier, LoadedModifierOption,
    ProductObjectsData,
)
from db.models import Group, StoreCharacteristic, StoreCharacteristicValue, Translation
from utils.translator import td

CharacteristicsType = list[tuple[StoreCharacteristic, Translation | None]]
CharacteristicsWithValuesType = \
    list[tuple[
        StoreCharacteristic, StoreCharacteristicValue, Translation | None,
                                                       Translation | None]]
CharacteristicsToTranslateType = dict[
    StoreCharacteristic | StoreCharacteristicValue, TranslateObjectData]
CharacteristicsTranslatedType = dict[
    StoreCharacteristic | StoreCharacteristicValue, str]
LoadedModifiersType = dict[int, LoadedModifier]


async def get_characteristics_to_translate(
        product_id: int, lang: str,
        modifiers: bool = False,
        product_objects_data: ProductObjectsData | None = None,
) -> tuple[CharacteristicsWithValuesType, CharacteristicsToTranslateType]:
    if product_objects_data:
        characteristics = product_objects_data.characteristics
    else:
        characteristics = await crud.get_product_characteristics(
            product_id, lang, modifiers
        )

    to_translate: CharacteristicsToTranslateType = {}

    for characteristic, value_obj, characteristic_translation, value_translation in (
            characteristics):
        to_translate[characteristic] = TranslateObjectData(
            object=characteristic,
            translation=characteristic_translation,
            field_name="name",
        )
        to_translate[value_obj] = TranslateObjectData(
            object=value_obj,
            translation=value_translation,
            field_name="value",
        )

    return characteristics, to_translate


def make_characteristics_from_translated(
        translated: CharacteristicsTranslatedType,
        characteristics: list[tuple[
            StoreCharacteristic, StoreCharacteristicValue,
            Translation | None, Translation | None
        ]]
):
    result: dict[str, str] = {}

    for characteristic, value_obj, characteristic_translation, value_translation in (
            characteristics):
        result[translated[characteristic]] = translated[value_obj]

    return result


async def get_modifiers_to_translate(
        product_id: int,
        product_group_id: int,
        store_id: int,
        lang: str,
        product_objects_data: ProductObjectsData | None = None,
) -> tuple[LoadedModifiersType, CharacteristicsToTranslateType]:
    if product_objects_data:
        to_translate: CharacteristicsToTranslateType = {}
        for loaded_modifier in product_objects_data.modifiers.values():
            to_translate[loaded_modifier.modifier] = TranslateObjectData(
                object=loaded_modifier.modifier,
                translation=loaded_modifier.modifier_translation,
                field_name="name",
            )
            to_translate[loaded_modifier.value_obj] = TranslateObjectData(
                object=loaded_modifier.value_obj,
                translation=loaded_modifier.value_translation,
                field_name="value",
            )

            for option in loaded_modifier.options:
                to_translate[option.value_obj] = TranslateObjectData(
                    object=option.value_obj,
                    translation=option.translation,
                    field_name="value",
                )

        return product_objects_data.modifiers, to_translate

    modifiers, to_translate = await get_characteristics_to_translate(
        product_id, lang, True
    )

    loaded_modifiers: LoadedModifiersType = {}

    for modifier, value_obj, modifier_translation, value_translation in modifiers:
        options = await crud.get_modifier_values(
            product_group_id,
            modifier.id, store_id, lang,
        ) or []

        loaded_options: list[LoadedModifierOption] = []

        for option_value_obj, is_available, option_translation in options:
            loaded_options.append(
                LoadedModifierOption(
                    option_value_obj, option_translation, is_available,
                )
            )

            if option_value_obj not in to_translate:
                to_translate[option_value_obj] = TranslateObjectData(
                    object=option_value_obj,
                    translation=option_translation,
                    field_name="value",
                )

        loaded_modifiers[modifier.id] = LoadedModifier(
            modifier, value_obj,
            modifier_translation, value_translation,
            loaded_options
        )

    return loaded_modifiers, to_translate


def make_modifiers_from_translated(
        translated: CharacteristicsTranslatedType,
        loaded_modifiers: LoadedModifiersType,
):
    result: dict[int, schemas.ProductModifierSchema] = {}

    for modifier_id, data in loaded_modifiers.items():
        result[modifier_id] = schemas.ProductModifierSchema(
            id=data.modifier.id,
            name=translated[data.modifier],
            value=translated[data.value_obj],
            orig_value=data.value_obj.value,
            options=[
                schemas.ProductModifierOption(
                    is_available=loaded_option.is_available,
                    value=translated[loaded_option.value_obj],
                    orig_value=loaded_option.value_obj.value,
                ) for loaded_option in data.options
            ]
        )
    return result


def make_characteristics_to_translate(
        characteristics: CharacteristicsType,
):
    to_translate: dict[StoreCharacteristic, TranslateObjectData] = {}

    for characteristic, translation in characteristics:
        to_translate[characteristic] = TranslateObjectData(
            object=characteristic,
            translation=translation,
            field_name="name",
        )
    return to_translate


def translated_characteristics_to_schemas(
        characteristics: CharacteristicsType,
        translated: dict[StoreCharacteristic, str],
):
    return [
        schemas.CharacteristicSchema(
            id=characteristic.id,
            name=translated[characteristic],
            filter_type=characteristic.filter_type,  # type: ignore
            position=characteristic.position if characteristic.position else 0,
        )
        for characteristic, _ in characteristics
    ]


async def characteristics_to_schemas(
        characteristics: CharacteristicsType,
        group: Group, lang: str,
):
    to_translate = make_characteristics_to_translate(characteristics)

    translated: dict[StoreCharacteristic, str] = await td(
        to_translate,
        lang, group.lang,
        group_id=group.id,
        is_auto_translate_allowed=group.is_translate,
    )

    return translated_characteristics_to_schemas(characteristics, translated)
