import schemas
from core.store.functions.custom_shipment import (
    custom_shipment_groups_list_to_schemas,
    custom_shipments_list_to_schemas,
)
from db import crud
from db.models import Brand, BrandCustomSettings, Group, StoreCustomSettings
from schemas.store.types import ShipmentType


async def get_shipments_data(
        brand: Brand, store_id: int | None, lang: str
) -> schemas.ShipmentsData:
    group = await Group.get(brand.group_id)

    result = schemas.ShipmentsData(
        base=[],
        groups=[],
        rest=[],
        no_delivery_shipment=None,
    )

    for base_type in ShipmentType:
        shipment = await get_base_shipment_if_enabled(base_type, brand.id, store_id)
        if shipment:
            schema = await shipment_to_schema(shipment, brand.id, lang, store_id)
            if schema.base_type == "no_delivery":
                result.no_delivery_shipment = schema
            else:
                result.base.append(schema)

    groups = await crud.get_custom_shipment_groups(brand.id, store_id, lang=lang)
    result.groups = await custom_shipment_groups_list_to_schemas(
        groups, brand,
        store_id, group.id,
        lang, group.lang,
        group.is_translate,
    )

    rest = await crud.get_custom_shipments(
        brand.id, store_id, is_rest_shipments=True, lang=lang
    )
    result.rest = await custom_shipments_list_to_schemas(
        rest, group.id, brand.id, lang, group.lang, group.is_translate, store_id,
    )

    return result


async def get_base_shipment_if_enabled(
        base_type: ShipmentType,
        brand_id: int,
        store_id: int | None = None,
):
    brand_settings = await BrandCustomSettings.get_or_create_base_shipment(
        brand_id, base_type.value
    )
    if brand_settings.base_type == ShipmentType.IN_STORE.value:
        return brand_settings  # in_store is always enabled

    if store_id:
        store_settings = await StoreCustomSettings.get_or_create(
            store_id, brand_settings.id,
        )
        if store_settings.is_enabled:
            return brand_settings
        elif store_settings.is_enabled is False:
            return None

    if brand_settings.is_enabled:
        return brand_settings

    return None


async def shipment_to_schema(
        shipment: BrandCustomSettings, brand_id: int, lang: str, store_id: int | None
):
    shipment_time = await crud.get_shipment_time(settings_id=shipment.id)
    shipment_prices = [
        schemas.ShipmentPrice.from_orm(shipment_price)
        for shipment_price in
        await crud.get_shipment_prices(brand_id, settings_id=shipment.id)
    ]
    store_settings = None
    if store_id:
        store_settings = await StoreCustomSettings.get_or_create(store_id, shipment.id)

    map_countries = []
    if store_settings and store_settings.map_countries_list:
        map_countries = store_settings.map_countries_list
    else:
        if shipment.map_countries_list:
            map_countries = shipment.map_countries_list

    return schemas.ShipmentSchema(
        id=shipment.id,
        custom_type=shipment.custom_type,
        base_type=shipment.base_type,
        name=await shipment.get_name(lang),
        prices=shipment_prices,
        is_paid_separately=shipment.is_paid_separately,
        allow_cash_payment=shipment.allow_cash_payment,
        allow_online_payment=shipment.allow_online_payment,
        payments_ids=await crud.get_shipment_custom_payments_ids(shipment.id),
        delivery_datetime_mode=shipment.delivery_datetime_mode,
        not_working_hours=shipment_time.not_working_hours.value if shipment_time else
        "nothing",
        enabled_tips=shipment.enabled_tips,
        store_settings_id=store_settings.id if store_settings else None,
        map_countries=map_countries,
        enabled_any_address_from_map=shipment.enabled_any_address_from_map,
        shipment_time=None if not shipment_time else schemas.ShipmentTimeSchema(
            min_time=shipment_time.min_time or None,
            max_time=shipment_time.max_time or None,
            execution_time=shipment_time.order_execution or None,
        ),
        is_enabled=shipment.is_enabled,
    )
