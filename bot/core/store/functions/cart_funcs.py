import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status

import schemas
from db.models import StoreCartProduct


def get_cart_data_from_token(
        token_data: dict | None,
        store_id: int | None = None,
) -> tuple[bool, int | None]:
    if not token_data:
        logging.error(
            f"*** get_cart_data_from_token without token_data, store {store_id}"
        )

    is_user = False
    if token_data and (
            token_data.get("type") == "cart" or
            token_data.get("is_cart", False) is True
    ):
        obj_id = token_data.get("sub")
    elif token_data and token_data.get("type") == "user":
        if not store_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="missing required argument store_id for user token error"
            )
        obj_id = token_data.get("sub")
        is_user = True
    else:
        return False, None

    return is_user, obj_id


def validate_cart_product_with_another_attributes(
        data: schemas.SaveCartProductSchema, cart_id: int,
        exclude_cart_product_id: int | None = None,
) -> StoreCartProduct | None:
    cart_products = StoreCartProduct.get_list_sync(
        product_id=data.product_id, cart_id=cart_id, for_update=True
    )
    full_duplicate = None
    if cart_products:
        for cart_product in cart_products:
            if exclude_cart_product_id and cart_product.id == exclude_cart_product_id:
                continue
            if cart_product.cart_attributes:
                is_full_duplicate = True
                attrs_ids = [attr.attribute_id for attr in data.cart_attributes]
                for exist_attr in cart_product.cart_attributes:
                    if exist_attr.attribute_id not in attrs_ids:
                        is_full_duplicate = False
                if is_full_duplicate:
                    full_duplicate = cart_product
            else:
                if cart_product.product_id == data.product_id:
                    full_duplicate = cart_product

    if full_duplicate and len(
            full_duplicate.cart_attributes
    ) == len(data.cart_attributes or []):
        return full_duplicate

    return None
