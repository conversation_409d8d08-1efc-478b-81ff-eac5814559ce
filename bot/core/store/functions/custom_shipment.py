from schemas.store.types import CustomType

from db import crud
from db.models import Brand, BrandCustomSettings, Translation, Group, StoreCustomSettings

from psutils.translator.schemas import TranslateObjectData

import schemas

from utils.translator import td

CustomShipmentsType = list[tuple[BrandCustomSettings, Translation | None]]
CustomShipmentToTranslateType = dict[BrandCustomSettings, TranslateObjectData]
CustomShipmentTranslatedType = dict[str | BrandCustomSettings, dict[str, str | None]]


async def translated_custom_shipment_to_schema(
    custom_shipment: BrandCustomSettings,
    brand_id: int,
    lang: str,
    translated: CustomShipmentTranslatedType,
    store_id: int | None = None,
) -> schemas.CustomShipmentSchema:
    translate_custom_shipment = translated[custom_shipment]
    shipment_time = await crud.get_shipment_time(settings_id=custom_shipment.id)
    shipment_prices = [
        schemas.ShipmentPrice.from_orm(shipment_price)
        for shipment_price in await crud.get_shipment_prices(brand_id, settings_id=custom_shipment.id)
    ]

    store_settings = None
    if store_id:
        store_settings = await StoreCustomSettings.get_or_create(store_id, custom_shipment.id)

    map_countries = []
    if store_settings and store_settings.map_countries_list:
        map_countries = store_settings.map_countries_list
    else:
        if custom_shipment.map_countries_list:
            map_countries = custom_shipment.map_countries_list

    delivery_datetime_mode = custom_shipment.delivery_datetime_mode

    return schemas.CustomShipmentSchema(
        id=custom_shipment.id,
        base_type=custom_shipment.base_type,
        custom_type=custom_shipment.custom_type,
        prices=shipment_prices,
        is_paid_separately=custom_shipment.is_paid_separately,
        allow_online_payment=custom_shipment.allow_online_payment,
        allow_cash_payment=custom_shipment.allow_cash_payment,
        payments_ids=await crud.get_shipment_custom_payments_ids(custom_shipment.id),
        delivery_datetime_mode=delivery_datetime_mode,
        not_working_hours=shipment_time.not_working_hours.value if shipment_time else "nothing",
        name=translate_custom_shipment["name"] or custom_shipment.name,
        description=translate_custom_shipment["description"] or custom_shipment.description,
        icon_url=await custom_shipment.media_url,
        need_comment=custom_shipment.need_comment,
        label_comment=translate_custom_shipment["label_comment"] or await custom_shipment.get_label_comment(
            lang, CustomType.CUSTOM_SHIPMENT.value
        ),
        enabled_tips=custom_shipment.enabled_tips,
        store_settings_id=store_settings.id if store_settings else None,
        need_address=custom_shipment.need_address,
        map_countries=map_countries,
        enabled_any_address_from_map=custom_shipment.enabled_any_address_from_map,
        shipment_time=None if not shipment_time else schemas.ShipmentTimeSchema(
            min_time=shipment_time.min_time or None,
            max_time=shipment_time.max_time or None,
            execution_time=shipment_time.order_execution or None,
        )
    )


async def custom_shipment_group_to_schema(
    custom_shipment_group: BrandCustomSettings,
    brand: Brand, store_id: int | None,
    lang: str,
    translated: CustomShipmentTranslatedType,
) -> schemas.CustomShipmentGroupSchema:
    translate_custom_shipment_group = translated[custom_shipment_group]
    return schemas.CustomShipmentGroupSchema(
        id=custom_shipment_group.id,
        custom_type=custom_shipment_group.custom_type,
        name=translate_custom_shipment_group["name"] or custom_shipment_group.name,
        description=translate_custom_shipment_group["description"] or custom_shipment_group.description,
        icon_url=await custom_shipment_group.media_url,
        shipments=await get_custom_shipments_list_schemas(
            brand, store_id, lang,
            custom_shipment_group_id=custom_shipment_group.id,
        ),
        is_enabled=custom_shipment_group.is_enabled,
    )


async def get_custom_shipment_to_translate(
    custom_shipment: BrandCustomSettings,
    translation: Translation | None = None,
) -> CustomShipmentToTranslateType:
    return {
        custom_shipment: TranslateObjectData(
            object=custom_shipment,
            translation=translation,
        )
    }


async def get_custom_shipment_group_to_translate(
    custom_shipment_group: BrandCustomSettings,
    translation: Translation | None = None,
) -> CustomShipmentToTranslateType:
    return {
        custom_shipment_group: TranslateObjectData(
            object=custom_shipment_group,
            translation=translation,
        )
    }


async def get_custom_shipments_to_translate(
    custom_shipments: CustomShipmentsType,
) -> tuple[list, dict]:
    custom_shipments_data: list = []
    to_translate: dict = {}

    for custom_shipment, translation in custom_shipments:
        custom_shipment_to_translate = await get_custom_shipment_to_translate(custom_shipment, translation)

        custom_shipments_data.append(custom_shipment)
        to_translate.update(custom_shipment_to_translate)

    return custom_shipments_data, to_translate


async def get_custom_shipment_groups_to_translate(
    custom_shipment_groups: CustomShipmentsType,
) -> tuple[list, dict]:
    custom_shipment_groups_data: list = []
    to_translate: dict = {}

    for custom_shipment_group, translation in custom_shipment_groups:
        custom_shipment_group_to_translate = await get_custom_shipment_group_to_translate(
            custom_shipment_group, translation
        )

        custom_shipment_groups_data.append(custom_shipment_group)
        to_translate.update(custom_shipment_group_to_translate)

    return custom_shipment_groups_data, to_translate


async def translated_custom_shipments_to_schemas(
    custom_shipments_data: list[BrandCustomSettings],
    brand_id: int,
    lang: str,
    translated: CustomShipmentTranslatedType,
    store_id: int | None = None,
) -> list[schemas.CustomShipmentSchema]:
    result: list[schemas.CustomShipmentSchema] = []

    for custom_shipment in custom_shipments_data:
        result.append(await translated_custom_shipment_to_schema(custom_shipment, brand_id, lang, translated, store_id))
    return result


async def translated_custom_shipment_groups_to_schemas(
    custom_shipment_groups_data: list[BrandCustomSettings],
    brand: Brand, store_id: int | None,
    lang: str,
    translated: CustomShipmentTranslatedType,
) -> list[schemas.CustomShipmentGroupSchema]:
    result: list[schemas.CustomShipmentGroupSchema] = []

    for custom_shipment_group in custom_shipment_groups_data:
        result.append(
            await custom_shipment_group_to_schema(
                custom_shipment_group, brand, store_id, lang, translated,
            )
        )
    return result


async def custom_shipments_list_to_schemas(
    custom_shipments: CustomShipmentsType,
    group_id: int, brand_id: int,
    lang: str, origin_lang: str,
    is_translate: bool,
    store_id: int | None = None
):
    custom_shipments_data, to_translate = await get_custom_shipments_to_translate(custom_shipments)

    translated = await td(
        to_translate,
        lang, origin_lang,
        group_id=group_id,
        is_auto_translate_allowed=is_translate,
    )

    return await translated_custom_shipments_to_schemas(
        custom_shipments_data, brand_id, lang, translated, store_id,
    )


async def custom_shipment_groups_list_to_schemas(
    custom_shipment_groups: CustomShipmentsType,
    brand: Brand, store_id: int | None,
    group_id: int,
    lang: str, origin_lang: str,
    is_translate: bool,
):
    custom_shipment_groups_data, to_translate = await get_custom_shipment_groups_to_translate(custom_shipment_groups)

    translated = await td(
        to_translate, lang, origin_lang,
        group_id=group_id, is_auto_translate_allowed=is_translate,
    )

    return await translated_custom_shipment_groups_to_schemas(
        custom_shipment_groups_data, brand, store_id, lang, translated,
    )


async def get_custom_shipments_list_schemas(
    brand: Brand, store_id: int | None, lang: str,
    custom_shipment_group_id: int | None = None,
    is_rest_shipments: bool = False,
) -> list[schemas.CustomShipmentSchema]:
    group = brand.group
    if not group:
        raise ValueError("Group not found for brand")

    db_custom_shipments: CustomShipmentsType = await crud.get_custom_shipments(
        brand_id=brand.id,
        store_id=store_id,
        custom_shipment_group_id=custom_shipment_group_id,
        is_rest_shipments=is_rest_shipments,
        lang=lang
    )

    if not db_custom_shipments:
        return []

    return await custom_shipments_list_to_schemas(
        db_custom_shipments, group.id, brand.id,
        lang, group.lang, group.is_translate, store_id,
    )


async def get_custom_shipment_groups_list_schemas(
    brand: Brand, store_id: int | None, lang: str,
) -> list[schemas.CustomShipmentGroupSchema]:
    group = await Group.get(brand.group_id)
    if not group:
        raise ValueError("Group not found for brand")

    db_custom_shipment_groups: CustomShipmentsType = await crud.get_custom_shipment_groups(
        brand_id=brand.id,
        store_id=store_id,
        lang=lang
    )

    if not db_custom_shipment_groups:
        return []

    return await custom_shipment_groups_list_to_schemas(
        db_custom_shipment_groups, brand, store_id, group.id,
        lang, group.lang, group.is_translate,
    )
