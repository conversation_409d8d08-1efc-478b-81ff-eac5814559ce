import schemas
from db.models import BillingSettings


async def get_billing_settings_schema(
        store_id: int, brand_id: int
) -> schemas.BillingSettings | None:
    schema = None
    settings = await BillingSettings.get("Store", store_id)
    if not settings:
        settings = await BillingSettings.get("Brand", brand_id)
    if settings:
        schema = schemas.BillingSettings.from_orm(settings)
    return schema
