import logging

import schemas
from config import (
    GOOGLE_MAPS_API_KEY, GOOGLE_PLACES_GEOCODING_API_KEY,
)
from db import crud
from db.models import Brand, BrandSettings, ClientBot, Group
from schemas import BillingProductCode

debugger = logging.getLogger('debugger.brand')


def determine_loyalty_target(loyalty_settings) -> dict | None:
    """
    Визначає target_object на основі заповнених полів loyalty_settings
    
    Returns:
        dict: {'type': str, 'id': int, 'name': str | None}
    """
    if loyalty_settings.product_id:
        return {
            'type': 'product',
            'id': loyalty_settings.product_id,
            'name': loyalty_settings.name
        }
        
    elif loyalty_settings.invoice_template_id:
        return {
            'type': 'invoice_template', 
            'id': loyalty_settings.invoice_template_id,
            'name': loyalty_settings.name
        }
        
    elif loyalty_settings.store_id:
        return {
            'type': 'store',
            'id': loyalty_settings.store_id,
            'name': loyalty_settings.name
        }
        
    elif loyalty_settings.brand_id:
        return {
            'type': 'brand',
            'id': loyalty_settings.brand_id,
            'name': loyalty_settings.name
        }
        
    elif loyalty_settings.profile_id:
        return {
            'type': 'profile',
            'id': loyalty_settings.profile_id,
            'name': loyalty_settings.name
        }
        
    elif loyalty_settings.ewallet_id:
        return {
            'type': 'ewallet',
            'id': loyalty_settings.ewallet_id,
            'name': loyalty_settings.name
        }
    
    return None


async def try_fill_loyalty_and_incust_brand_settings(
        brand_schema: schemas.BrandSchema, brand: Brand
) -> schemas.BrandSchema:
    loyalty_info = schemas.LoyaltyInfo(loyalty_type=None, loyalty_enabled=False)
    incust_data = None

    loyalty_settings = await crud.get_loyalty_settings_for_context(
        "brand",
        schemas.LoyaltySettingsData(
            brand_id=brand.id,
            profile_id=brand.group_id,
        )
    )
    if loyalty_settings:
        loyalty_info.loyalty_enabled = loyalty_settings.is_enabled
        loyalty_info.loyalty_type = "incust"

        incust_data = schemas.IncustData(
            white_label_id=loyalty_settings.white_label_id,
            loyalty_id=loyalty_settings.loyalty_id,
            server_api=loyalty_settings.server_url,
            type_client_auth=loyalty_settings.type_client_auth.value or "web",
            loyalty_applicable_type=loyalty_settings.loyalty_applicable_type.value or
                                    "for_all",
            prohibit_redeeming_bonuses=loyalty_settings.prohibit_redeeming_bonuses,
            prohibit_redeeming_coupons=loyalty_settings.prohibit_redeeming_coupons,
            loyalty_settings_id=loyalty_settings.id,
            target_object=determine_loyalty_target(loyalty_settings),
        )

        brand_schema.loyalty_settings = schemas.loyalty_settings.LoyaltySettingsSchema.from_orm(loyalty_settings)

    brand_schema.incust_data = incust_data
    brand_schema.loyalty_info = loyalty_info

    return brand_schema


async def fill_delivery_time_brand_settings(
        brand_schema: schemas.BrandSchema, brand: Brand
) -> schemas.BrandSchema:
    delivery_datetime_mode = await BrandSettings.get_by_brand_and_type(
        brand.id, 'delivery_datetime_mode'
    )
    delivery_time_warning = await BrandSettings.get_by_brand_and_type(
        brand.id, 'delivery_time_warning_enabled'
    )

    brand_schema.delivery_datetime_mode = delivery_datetime_mode.value_data if (
        delivery_datetime_mode) else 'datetime'

    if not delivery_time_warning:
        brand_schema.delivery_time_warning_enabled = None
    if delivery_time_warning and delivery_time_warning.value_data:
        brand_schema.delivery_time_warning_enabled = True if (
                delivery_time_warning.value_data == "1") else False

    return brand_schema


async def fill_friend_payment_settings(
        brand_schema: schemas.BrandSchema, brand: Brand
) -> schemas.BrandSchema:
    friend_payment_settings = await crud.get_is_friend_payment(brand.id)
    brand_schema.is_friend_payment = True if (friend_payment_settings and
                                              friend_payment_settings.value_data ==
                                              "1") \
        else False

    return brand_schema


async def fill_additional_brand_settings(
        brand_schema: schemas.BrandSchema, brand: Brand
) -> schemas.BrandSchema:
    brand_schema = await try_fill_loyalty_and_incust_brand_settings(brand_schema, brand)
    brand_schema = await fill_delivery_time_brand_settings(brand_schema, brand)
    brand_schema = await fill_friend_payment_settings(brand_schema, brand)

    return brand_schema


def is_google_enabled(brand: Brand) -> bool:
    if brand.google_maps_api_key_backend:
        return True
    if brand.google_maps_7loc_keys_enabled and GOOGLE_PLACES_GEOCODING_API_KEY:
        return True

    return False


def google_public_key(brand: Brand) -> str | None:
    if brand.google_maps_api_key_frontend:
        return brand.google_maps_api_key_frontend
    if brand.google_maps_7loc_keys_enabled and GOOGLE_MAPS_API_KEY:
        return GOOGLE_MAPS_API_KEY
    return None


async def get_brand_scan_receipts_settings(
        brand_id: int
) -> schemas.BrandScanReceiptsSettings:
    res = schemas.BrandScanReceiptsSettings(enabled=False)
    settings = await crud.get_brand_scan_receipts_settings(brand_id)
    if not settings:
        return res

    for setting in settings:
        if setting.type_data == "scan_receipts_enabled":
            res.enabled = True if setting.value_data in ("1", "True") else False
        if setting.type_data == "scan_receipts_country":
            res.country = setting.value_data
        if setting.type_data == "scan_receipts_bin_codes":
            res.bin_codes = setting.value_data.split(",") if setting.value_data else []
        if setting.type_data == "scan_receipts_demo_mode":
            res.demo_mode = True if setting.value_data in ("1", "True") else False
        if setting.type_data == "scan_receipts_enabled_all_rules":
            res.enabled_all_rules = True if setting.value_data in (
                "1", "True") else False

    return res


async def brand_to_schema(
        brand: Brand,
) -> schemas.BrandSchema:
    group = await Group.get(brand.group_id)
    bot = await ClientBot.get(group_id=group.id)

    group = await Group.get(brand.group_id)
    if group:
        default_currency = group.currency
    else:
        default_currency = None

    stores_count: int = await crud.get_stores(brand.id, operation="count")

    transactions_activated = (
            not await crud.check_access_to_action(
                "billing:tester", "profile", group.id,
            ) or
            await crud.billing.get_is_item_exist_for_product(
                group.id,
                BillingProductCode.TRANSACTION,
                BillingProductCode.TRANSACTION_CENT,
            )
    )

    if stores_count == 1:
        single_store_id = (await crud.get_stores(brand.id, limit=1))[0].id
    else:
        single_store_id = None

    brand_schema = schemas.BrandSchema(
        **brand.as_dict(),
        logo_url=await brand.logo_url,
        image_url=await brand.image_url,
        bot_id=bot.id if bot else None,
        bot_name=bot.username if bot else None,
        is_offer_doc_exist=brand.offer_media_id is not None,
        is_about_doc_exist=brand.description_media_id is not None,
        image_aspect_ratio=brand.product_image_aspect_ratio_converted,
        default_currency=default_currency,
        stores_count=stores_count if stores_count else 0,
        single_store_id=single_store_id,
        default_lang=group.lang,
        country_code=group.country_code,
        is_translate=group.is_translate,
        footer_sign=brand.footer_sign,
        terms_of_use_link=brand.terms_of_use_link,
        privacy_policy_link=brand.privacy_policy_link,
        order_fields_settings=schemas.OrderFieldsSettings(
            web=schemas.OrderClientFieldsSettings(
                email=brand.web_email_mode,
                phone=brand.web_phone_mode,
                order_name_mode=brand.web_order_name_mode,
            ),
            messanger=schemas.OrderClientFieldsSettings(
                email=brand.messanger_email_mode,
                phone=brand.messanger_phone_mode,
                order_name_mode=brand.messanger_order_name_mode,
            ),
            order_comment_mode=brand.order_comment_mode,
        ),
        google_maps_public_api_key=google_public_key(brand),
        is_enabled_google_maps_api=is_google_enabled(brand),
        is_enabled_ai=brand.is_ai_enabled,
        scan_receipts_settings=await get_brand_scan_receipts_settings(brand.id),
        transactions_activated=transactions_activated,
    )

    return await fill_additional_brand_settings(brand_schema, brand)
