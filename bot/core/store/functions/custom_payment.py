from psutils.translator.schemas import TranslateObjectData

import schemas
from db import crud
from db.models import Brand, BrandCustomSettings, Translation
from schemas.store.types import CustomType
from utils.translator import td

CustomPaymentsType = list[tuple[BrandCustomSettings, Translation | None]]
CustomPaymentToTranslateType = dict[BrandCustomSettings, TranslateObjectData]
CustomPaymentTranslatedType = dict[str, BrandCustomSettings, dict[str, str | None]]


async def custom_payment_to_schema(
        custom_payment: BrandCustomSettings,
        lang: str,
        translated: CustomPaymentTranslatedType,
) -> schemas.CustomPaymentSchema:
    translate_custom_payment = translated[custom_payment]
    return schemas.CustomPaymentSchema(
        id=custom_payment.id,
        name=translate_custom_payment["name"] or custom_payment.name,
        description=translate_custom_payment["description"] or custom_payment.description,
        icon_url=await custom_payment.media_url,
        price=custom_payment.price,
        need_comment=custom_payment.need_comment,
        label_comment=translate_custom_payment["label_comment"] or await custom_payment.get_label_comment(
            lang, CustomType.CUSTOM_PAYMENT.value
        ),
        is_enabled=True,
        info=translate_custom_payment["info"] or await custom_payment.get_info(
            lang, CustomType.CUSTOM_PAYMENT.value
        ),
    )


async def get_custom_payment_to_translate(
        custom_payment: BrandCustomSettings,
        translation: Translation | None = None,
) -> CustomPaymentToTranslateType:
    to_translate: CustomPaymentToTranslateType = {
        custom_payment: TranslateObjectData(
            object=custom_payment,
            translation=translation,
        )
    }

    return to_translate


async def get_custom_payments_to_translate(
        custom_payments: CustomPaymentsType,
) -> tuple[list, dict]:
    custom_payments_data: list = []
    to_translate: dict = {}

    for custom_payment, translation in custom_payments:
        custom_payment_to_translate = await get_custom_payment_to_translate(custom_payment, translation)

        custom_payments_data.append(custom_payment)
        to_translate.update(custom_payment_to_translate)

    return custom_payments_data, to_translate


async def translated_custom_payments_to_schemas(
        custom_payments_data: list[BrandCustomSettings],
        lang: str,
        translated: CustomPaymentTranslatedType,
) -> list[schemas.CustomPaymentSchema]:
    result: list[schemas.CustomPaymentSchema] = []

    for custom_payment in custom_payments_data:
        result.append(
            await custom_payment_to_schema(
                custom_payment, lang, translated,
            )
        )
    return result


async def custom_payments_list_to_schemas(
        custom_payments: CustomPaymentsType,
        group_id: int,
        lang: str, origin_lang: str,
        is_translate: bool,
):
    custom_payments_data, to_translate = await get_custom_payments_to_translate(custom_payments)

    translated = await td(
        to_translate, lang, origin_lang,
        group_id=group_id, is_auto_translate_allowed=is_translate,
    )

    return await translated_custom_payments_to_schemas(
        custom_payments_data, lang, translated,
    )


async def get_custom_payments_list(brand: Brand, lang: str, ) -> list[schemas.CustomPaymentSchema]:
    group = brand.group
    if not group:
        raise ValueError("Group not found for store")

    db_custom_payments = await crud.get_custom_payments(
        brand_id=brand.id,
        lang=lang
    )

    if not db_custom_payments:
        return []

    return await custom_payments_list_to_schemas(
        db_custom_payments, group.id,
        lang, group.lang, group.is_translate,
    )
