from psutils.translator.schemas import TranslateObjectData

import schemas
from db import crud
from db.crud.store.product.read import ProductObjectsData
from db.models import StoreAttribute, StoreAttributeGroup, Translation
from utils.translator import t, td

AttributesToTranslateType = dict[
    StoreAttribute | StoreAttributeGroup, TranslateObjectData]
AttributesTranslatedType = dict[StoreAttribute | StoreAttributeGroup, str]
AttributeGroupsDataType = dict[StoreAttributeGroup, list[StoreAttribute]]


async def get_attribute_group_to_translate(
        ag: StoreAttributeGroup, lang: str,
        translation: Translation | None = None,
        attributes_db: list[tuple[StoreAttribute, Translation | None]] | None = None,
) -> tuple[list[StoreAttribute], AttributesToTranslateType]:
    if not attributes_db:
        attributes_db = await crud.get_attributes(ag.id, lang)

    to_translate: AttributesToTranslateType = {}

    for attribute, attribute_translation in attributes_db:
        to_translate[attribute] = get_attribute_to_translate(
            attribute, attribute_translation
        )

    to_translate[ag] = TranslateObjectData(
        object=ag,
        translation=translation,
        field_name="name",
    )
    return [el[0] for el in attributes_db], to_translate


async def get_attribute_groups_to_translate(
        brand_id: int, product_id: int, lang: str,
        product_objects_data: ProductObjectsData | None = None,
) -> tuple[AttributeGroupsDataType, AttributesToTranslateType]:
    attributes_groups_attributes: AttributeGroupsDataType = {}
    to_translate: AttributesToTranslateType = {}

    async def process_attribute_group(
            ag: StoreAttributeGroup,
            ag_translation: Translation | None = None,
            attributes: list[tuple[StoreAttribute, Translation | None]] | None = None,
    ):
        db_attributes, ag_to_translate = await get_attribute_group_to_translate(
            ag, lang, ag_translation, attributes
        )

        to_translate.update(ag_to_translate)
        attributes_groups_attributes[ag] = db_attributes

    if product_objects_data:
        for attribute_group_data in product_objects_data.attribute_groups:
            await process_attribute_group(
                attribute_group_data.attribute_group,
                attribute_group_data.translation,
                attribute_group_data.attributes
            )
    else:
        for attribute_group_data in await crud.get_attribute_groups(
                brand_id, product_id, lang
        ):
            await process_attribute_group(*attribute_group_data)

    return attributes_groups_attributes, to_translate


def translated_attribute_group_to_schema(
        ag: StoreAttributeGroup,
        attributes_db: list[StoreAttribute],
        translated: AttributesTranslatedType,
):
    attributes = [
        translated_attribute_to_schema(attribute, translated[attribute])
        for attribute in attributes_db
    ]

    return schemas.AttributeGroupSchema(
        id=ag.id,
        attribute_group_id=ag.attribute_group_id,
        name=translated[ag],
        external_id=ag.external_id,
        external_type=ag.external_type,
        min=ag.min if ag.min else 0,
        max=ag.max,
        is_deleted=ag.is_deleted,
        brand_id=ag.brand_id,
        position=ag.position,
        attributes=attributes
    )


def translated_attribute_groups_to_schemas(
        attributes_groups_data: AttributeGroupsDataType,
        translated: AttributesTranslatedType
):
    result: list[schemas.AttributeGroupSchema] = []
    for attribute_group, attributes in attributes_groups_data.items():
        result.append(
            translated_attribute_group_to_schema(
                attribute_group, attributes, translated
            )
        )
    return result


async def attribute_group_to_schema(
        ag: StoreAttributeGroup,
        group_id: int,
        lang: str,
        origin_lang: str,
        is_translate: bool,
        translation: Translation | None = None,
) -> schemas.AttributeGroupSchema:
    attributes_db, to_translate = await get_attribute_group_to_translate(
        ag, lang, translation
    )

    translated: AttributesTranslatedType = await td(
        to_translate,
        lang, origin_lang,
        group_id=group_id,
        is_auto_translate_allowed=is_translate,
    )

    return translated_attribute_group_to_schema(ag, attributes_db, translated)


def get_attribute_to_translate(
        attribute: StoreAttribute,
        attribute_translation: Translation | None,
):
    return TranslateObjectData(
        object=attribute,
        translation=attribute_translation,
        field_name="name",
    )


def translated_attribute_to_schema(
        attribute: StoreAttribute,
        translated_name: str,
):
    attribute_schema = schemas.AttributeSchema.from_orm(attribute)
    if attribute_schema.min is None:
        attribute_schema.min = 0

    attribute_schema.price_impact = round(attribute.price_impact / 100, 2)
    attribute_schema.name = translated_name

    return attribute_schema


async def attribute_to_schema(
        is_translate: bool,
        group_id: int,
        attribute: StoreAttribute,
        lang: str,
        origin_lang: str | None = None,
        translation: Translation | None = None
):
    if not origin_lang:
        origin_lang = attribute.brand.group.lang

    translated_name: str = await t(
        attribute, lang, origin_lang,
        translation=translation,
        group_id=group_id,
        field_name="name",
        is_auto_translate_allowed=is_translate,
    )

    return translated_attribute_to_schema(attribute, translated_name)
