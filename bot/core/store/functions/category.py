from _operator import attrgetter
from psutils.translator.schemas import TranslateObjectData

import schemas
from db import crud
from db.crud.store.category.read import TreeCategory
from db.models import Group, StoreCategory, StoreCharacteristic, Translation
from utils.translator import td
from .characteristic import (
    make_characteristics_to_translate,
    translated_characteristics_to_schemas,
)


async def category_to_schema(
        category_obj: StoreCategory | TreeCategory,
        group: Group, store_id: int,
        lang: str,
        filters_set_id: int | None = None,
        filters_data: schemas.ProductListFiltersData | None = None,
        product_search: str | None = None,
        translation: Translation | None = None,
):
    if isinstance(category_obj, TreeCategory):
        category = category_obj.category
        if translation is None:
            translation = category_obj.translation

        has_child_categories = category_obj.has_child_categories
        products_count = category_obj.products_count

        children = [
            await category_to_schema(
                child, group, store_id, lang,
                filters_set_id, filters_data, product_search,
            )
            for child in category_obj.children
        ]
        filters_db = category_obj.filters
    else:
        category = category_obj
        has_child_categories = await crud.get_has_category_children(
            category.id, store_id
        )
        products_count = await crud.get_category_products_count(
            category.id, store_id,
            filters_set_id,
            lang, product_search,
        )
        children = []
        filters_db = await crud.get_category_filters(category.id, lang)

    to_translate: dict[
        StoreCategory | StoreCharacteristic,
        TranslateObjectData
    ] = make_characteristics_to_translate(filters_db)

    to_translate[category] = TranslateObjectData(
        object=category,
        translation=translation,
        field_name="name",
    )

    translated: dict[StoreCategory | StoreCharacteristic, str] = await td(
        to_translate,
        lang, group.lang,
        group_id=group.id,
        is_auto_translate_allowed=group.is_translate,
    )

    filters = translated_characteristics_to_schemas(filters_db, translated)

    filters.sort(key=attrgetter("position"))

    return schemas.CategorySchema(
        id=category.id,
        has_child_categories=has_child_categories,
        father_category_id=category.father_category_id,
        external_id=category.external_id,
        external_type=category.external_type,
        name=translated[category],
        position=category.position,
        products_count=products_count,
        filters=filters,
        children=children or None,
    )
