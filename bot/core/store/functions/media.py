import asyncio
import logging
import os
import uuid
from dataclasses import dataclass

from config import STATIC_DIR, TEMP_DIR, THUMBNAIL_SIZE
from core.media_manager import media_manager
from db.models import MediaObject, Brand
from utils.media import make_thumbnail_image, ThumbnailImageData


@dataclass
class ThumbnailWithData:
    media: MediaObject
    data: ThumbnailImageData


async def make_thumbnail(
        media: MediaObject,
        brand: Brand,
        return_data: bool = False
) -> MediaObject | ThumbnailWithData:
    _, extension = os.path.splitext(media.file_path)
    temp_file_name = f"tmp_{str(uuid.uuid4())}{extension}"
    temp_file_path = os.path.join(STATIC_DIR, TEMP_DIR, temp_file_name)

    thumbnail_data = await make_thumbnail_image(
        media.file_path,
        temp_file_path,
        brand.product_image_aspect_ratio_converted,
        brand.thumbnail_size,
    )
    if not brand.product_image_aspect_ratio_converted:
        await brand.update(product_image_aspect_ratio_converted=thumbnail_data.aspect_ratio)

    thumbnail_media = await media_manager.save_from_file_path(
        temp_file_path, copy_or_move="move",
    )

    if return_data:
        return ThumbnailWithData(
            thumbnail_media,
            thumbnail_data,
        )

    return thumbnail_media


async def batch_make_thumbnails(
        medias: list[MediaObject],
        brand: Brand,
) -> list[MediaObject | None]:
    result: list[MediaObject | None] = []

    if not medias:
        return result

    if not brand.product_image_aspect_ratio_converted:
        media = medias[0]
        medias = medias[1:]

        thumbnail_with_data = await make_thumbnail(media, brand, True)
        result.append(thumbnail_with_data.media)
        aspect_ratio = thumbnail_with_data.data.aspect_ratio
    else:
        aspect_ratio = brand.product_image_aspect_ratio_converted

    if not medias:
        return result

    async def __make_thumbnail_image(from_file_path: str):
        _, extension = os.path.splitext(from_file_path)
        temp_file_name = f"tmp_{str(uuid.uuid4())}{extension}"
        temp_file_path = os.path.join(STATIC_DIR, TEMP_DIR, temp_file_name)

        await make_thumbnail_image(
            from_file_path,
            temp_file_path,
            aspect_ratio,
            THUMBNAIL_SIZE,
        )
        return temp_file_path

    temp_file_paths = await asyncio.gather(
        *[
            __make_thumbnail_image(media.file_path)
            for media in medias
        ]
    )

    saved_thumbnails = await media_manager.batch_save_from_file_path(
        *temp_file_paths,
        copy_or_move="move",
    )

    for el in saved_thumbnails.values():
        if isinstance(el, Exception):
            logging.error(el, exc_info=True)
            result.append(None)
        else:
            result.append(el)

    return result
