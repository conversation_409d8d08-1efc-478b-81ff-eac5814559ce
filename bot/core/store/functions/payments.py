from config import LOC7_API_URL


def get_icon_for_payment(payment_method: str) -> str:
    match payment_method:
        case "tg_pay":
            return f"{LOC7_API_URL}/static/images/store/payments/tg_pay.webp"
        case "cash":
            return f"{LOC7_API_URL}/static/images/store/payments/cash.svg"
        case "incust_pay":
            return f"{LOC7_API_URL}/static/images/store/payments/incust.png"
        case "friend":
            return f"{LOC7_API_URL}/static/images/store/payments/friend.svg"
        case "custom":
            return f"{LOC7_API_URL}/static/images/store/payments/custom.svg"
        case "comsa":
            return f"{LOC7_API_URL}/static/images/store/payments/comsa.webp"
        case "epay":
            return f"{LOC7_API_URL}/static/images/store/payments/epay.png"
        case "epay":
            return f"{LOC7_API_URL}/static/images/store/payments/epay.png"
        case "flutterwave":
            return f"{LOC7_API_URL}/static/images/store/payments/flutter_wave.png"
        case "fondy":
            return f"{LOC7_API_URL}/static/images/store/payments/fondy.png"
        case "freedompay":
            return f"{LOC7_API_URL}/static/images/store/payments/freedompay.png"
        case "orange":
            return f"{LOC7_API_URL}/static/images/store/payments/orange.png"
        case "stripe":
            return f"{LOC7_API_URL}/static/images/store/payments/stripe.png"
        case "tpay":
            return f"{LOC7_API_URL}/static/images/store/payments/tpay.png"
        case "wave":
            return f"{LOC7_API_URL}/static/images/store/payments/wave.png"
        case "liqpay":
            return f"{LOC7_API_URL}/static/images/store/liqpay/logo-liqpay-symbol.png"
        case "unipos":
            return f"{LOC7_API_URL}/static/images/store/unipos/sbp.png"
        case "pl24":
            return f"{LOC7_API_URL}/static/images/store/pl24/P24_logo.png"
        case "tj":
            return f"{LOC7_API_URL}/static/images/store/tj/tj_logo.png"
        case "airtel":
            return f"{LOC7_API_URL}/static/images/store/airtel/airtel_logo.png"
        case "kpay":
            return f"{LOC7_API_URL}/static/images/store/kpay/kpay_logo.png"
        case "momo":
            return f"{LOC7_API_URL}/static/images/store/momo/mtnmomo.svg"
        case "ewallet":
            return f"{LOC7_API_URL}/static/images/store/payments/7loc.png"
        case _:
            return f"{LOC7_API_URL}/static/images/store/payments/online.svg"
