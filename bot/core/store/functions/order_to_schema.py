import logging
import math

from incust_terminal_api_client.models import CustomerSpecialAccount, SpecialAccountCharge
from psutils.date_time import localise_datetime

import schemas
from config import ANON_ORDER_TOKEN_EXPIRES, NO_CENT_CURRENCIES
from core.payment.funcs import get_payment_default_name, get_translated_payment_settings
from core.store.product.functions import product_to_schema
from db import crud
from db.models import (
    ExtraFeeJournal, Invoice, InvoiceToFriend, StoreOrder, StoreOrderPayment, User,
)
from schemas import ExtraFeeSchema
from utils.jwt_token import create_jwt_token
from utils.numbers import format_currency

debugger = logging.getLogger('debugger.order_to_schema')


async def _get_bonuses_added_amount(order: StoreOrder, invoice: Invoice | None) -> float | None:
    """
    Отримати суму нарахованих бонусів з invoice або з original_incust_loyalty_check для сумісності
    """
    # Пріоритет 1: Дані з invoice
    if invoice and invoice.bonuses_added_amount is not None:
        return round(invoice.bonuses_added_amount / 100, 2)
    
    # Пріоритет 2: Для сумісності зі старими записами - з original_incust_loyalty_check
    if order.original_incust_loyalty_check and hasattr(order.original_incust_loyalty_check, 'bonuses_added_amount'):
        bonuses_added = order.original_incust_loyalty_check.bonuses_added_amount
        if bonuses_added is not None:
            return bonuses_added
    
    return None


async def _get_special_accounts_charges(order: StoreOrder, invoice: Invoice | None) -> list[SpecialAccountCharge] | None:
    """
    Отримати поповнені рахунки з invoice або з original_incust_loyalty_check для сумісності
    """
    # Пріоритет 1: Дані з invoice
    if invoice and invoice.special_accounts_charges is not None:
        return invoice.special_accounts_charges
    
    # Пріоритет 2: Для сумісності зі старими записами - з original_incust_loyalty_check
    if order.original_incust_loyalty_check and hasattr(order.original_incust_loyalty_check, 'special_accounts_charges'):
        special_accounts = order.original_incust_loyalty_check.special_accounts_charges  
        if special_accounts is not None:
            return special_accounts
    
    return None


async def order_to_schema(
        order: StoreOrder,
        lang: str,
        with_token: bool = False,
) -> schemas.OrderSchema:
    group = await crud.get_group_by_store(order.store_id)
    store_id = order.store_id

    create_date = localise_datetime(order.create_date, group.timezone)

    if with_token:
        order_token = create_jwt_token(
            data={
                "sub": f"{order.id}",
                "type": "order",
                "scopes": ["order:read", "order:write"],
            }, expire=ANON_ORDER_TOKEN_EXPIRES
        )
    else:
        order_token = None

    brand = await crud.get_brand_by_group(group.id)

    invoice = None
    if order.invoice_id:
        invoice = await Invoice.get(order.invoice_id)

    incust_vouchers = None
    if invoice:
        if invoice.loyalty_coupons_data and invoice.incust_transaction_id:
            incust_vouchers = invoice.loyalty_coupons_data

    shipment = await crud.get_order_shipment(order.id)

    order_payment_schema = None
    order_payment = await StoreOrderPayment.get(order_id=order.id)
    if order_payment:
        payment_name = order_payment.name
        payment_label_comment = order_payment.label_comment

        payment_methods = await crud.get_payment_methods(
            brand_id=brand.id,
            store_id=store_id,
            with_translations=True,
            lang=lang,
            for_payment_list=False,
            payment_settings_id=order_payment.payment_settings_id,
        )
        if payment_methods:
            payments_to_translate = []
            original_payment = None
            for payment_setting, translation, payment_methods in payment_methods:
                original_payment = payment_setting
                payments_to_translate.append((payment_setting, translation))
            translated_dict = await get_translated_payment_settings(
                payments_to_translate, lang, brand.group_id
            )

            try:
                payment_name = translated_dict.get(
                    order_payment.payment_settings_id, {}
                ).get(
                    "name", None
                ) if translated_dict else (
                    payment_methods[0].name)
                payment_label_comment = translated_dict.get(
                    order_payment.payment_settings_id, {}
                ).get(
                    "label_comment",
                    None
                ) if translated_dict else order_payment.label_comment
            except AttributeError:
                payment_name = order_payment.name
                payment_label_comment = order_payment.label_comment

            if original_payment:
                if not payment_name or payment_name == original_payment.payment_method:
                    payment_name = await get_payment_default_name(
                        original_payment.payment_method, lang,
                        original_payment.is_online,
                        ewallet_id=original_payment.json_data.get(
                            "ewallet_id", None
                        ) if original_payment.json_data else None
                    )

        post_payment_info = order_payment.post_payment_info
        if isinstance(post_payment_info, str) and "{amount}" in post_payment_info:
            post_payment_info = post_payment_info.replace(
                "{amount}",
                format_currency(
                    round(order.sum_to_pay / 100, 2),
                    order.currency, locale=group.lang,
                )
            )

        order_payment_schema = schemas.OrderPayment(
            name=payment_name,
            description=order_payment.description,
            comment=order_payment.comment,
            post_payment_info=post_payment_info,
            label_comment=payment_label_comment,
            payment_method=order_payment.payment_method,
            payment_settings_id=order_payment.payment_settings_id,
            incust_account_name=order_payment.incust_account_name if
            order_payment.incust_account_name else None,
            price=order_payment.price,
        )

    date_sent_to_friend = None
    friend_pending_name = None
    if order_payment and order_payment.payment_method == 'friend' and invoice:
        invoice_to_friend: InvoiceToFriend = await crud.get_pending_invoice_to_friend(
            invoice.id
        )
        if invoice_to_friend:
            date_sent_to_friend = invoice_to_friend.date_sent_to_friend
            friend_pending: User = await User.get_by_id(invoice_to_friend.friend_id)
            if friend_pending:
                friend_pending_name = friend_pending.name

    extra_fees = await ExtraFeeJournal.get_list(order_id=order.id)

    logging.debug(
        "Extra fees fields check: %s",
        [(hasattr(f, 'extra_fee_id'), hasattr(f, 'name'), hasattr(f, 'applied_amount'))
         for f in extra_fees]
    )

    debugger.debug(
        "Extra fees as dict data: %s", [extra_fee.as_dict() for extra_fee in extra_fees]
    )

    payer_fee = 0
    if order.payer_fee:
        if order.currency in NO_CENT_CURRENCIES:
            payer_fee = math.ceil(order.payer_fee / 100)
        else:
            payer_fee = round(order.payer_fee / 100, 2)

    return schemas.OrderSchema(
        id=order.id,
        shipment=shipment,
        total_sum=round(order.total_sum / 100, 2) if order.total_sum else 0,
        sum_to_pay=round(order.sum_to_pay / 100, 2) if order.sum_to_pay else 0,
        before_loyalty_sum=round(order.before_loyalty_sum / 100, 2),
        bonuses_added_amount=await _get_bonuses_added_amount(order, invoice),
        special_accounts_charges=await _get_special_accounts_charges(order, invoice),
        create_date=create_date,
        status=order.status,
        status_pay=order.status_pay,
        store_id=order.store_id,
        comment=order.comment,
        delivery_address=order.delivery_address,
        first_name=order.first_name,
        last_name=order.last_name,
        phone=order.phone,
        email=order.email,
        address_comment=order.address_comment,
        desired_delivery_date=order.desired_delivery_date,
        desired_delivery_time=order.desired_delivery_time,
        address_street=order.address_street,
        address_house=order.address_house,
        address_flat=order.address_flat,
        address_floor=order.address_floor,
        address_entrance=order.address_entrance,
        loyalty_type=order.loyalty_type,
        bonuses_redeemed=round(
            order.bonuses_redeemed / 100, 2
        ) if order.bonuses_redeemed else 0,
        discount=round(order.discount / 100, 2) if order.discount else 0,
        discount_and_bonuses=round(
            order.discount_and_bonuses / 100, 2
        ) if order.discount_and_bonuses else 0,
        order_products=[
            schemas.OrderProductSchema(
                id=order_product.id,
                quantity=order_product.quantity,
                product_id=order_product.product_id,
                product=await product_to_schema(
                    order_product.product, store_id, lang, brand=brand
                ),
                name=order_product.name,
                attributes=[
                    schemas.OrderAttributeSchema(
                        id=order_attribute.id,
                        quantity=order_attribute.quantity,
                        name=order_attribute.name,
                        price_impact=round(order_attribute.price_impact / 100, 2),
                        attribute_id=order_attribute.attribute_id,
                        attribute_code=attribute.attribute_id,
                    )
                    for order_attribute, attribute in
                    await crud.get_order_product_attributes(order_product.id)
                ],
                incust_account=CustomerSpecialAccount(
                    **order_product.incust_account
                ) if order_product.incust_account else None,
                incust_card=order_product.incust_card,
                charge_percent=float(order_product.charge_percent) if order_product.charge_percent is not None else None,
                charge_fixed=float(order_product.charge_fixed) if order_product.charge_fixed is not None else None,
                is_topup_error=order_product.is_topup_error,
                **order_product.converted_sums,
            )
            for order_product in await crud.get_order_products(order.id)
        ],
        shipment_status=order.shipment_status,
        token=order_token,
        original_incust_loyalty_check=order.original_incust_loyalty_check,
        currency=order.currency,
        incust_vouchers=incust_vouchers,
        tips_sum=round(order.tips_sum / 100, 2),
        user_id=order.user_id,
        date_sent_to_friend=date_sent_to_friend,
        friend_pending_name=friend_pending_name,
        address_lat=order.address_lat,
        address_lng=order.address_lng,
        address_place_id=order.address_place_id,
        type=order.type,
        payer_fee=payer_fee,
        paid_sum=round(order.paid_sum / 100, 2),
        extraFee=[
            ExtraFeeSchema(
                extra_fee_id=extra_fee.extra_fee_id,
                name=extra_fee.name,
                extra_fee_percent=extra_fee.extra_fee_percent,
                extra_fee_value=extra_fee.extra_fee_value,
                applied_amount=extra_fee.applied_amount,
                invoice_id=extra_fee.invoice_id,
                order_id=extra_fee.order_id,
                applied_amount_float=extra_fee.applied_amount_float
            )
            for extra_fee in extra_fees or []
        ],
        total_sum_with_extra_fee=round(
            order.total_sum_with_extra_fee / 100, 2
        ) if order.total_sum_with_extra_fee else 0,
        order_payment=order_payment_schema,
    )
