import logging
from dataclasses import dataclass

from psutils.translator.schemas import TranslateObjectData

import schemas
from db import crud
from db.crud.store.product.read import ProductObjectsData
from db.models import (
    Brand, Group, MediaObject, StoreCategory, StoreProduct,
    Translation,
)
from utils.media import make_preview_url
from utils.text import f
from utils.translator import td
from ..functions.attribute import (
    AttributeGroupsDataType, AttributesToTranslateType, AttributesTranslatedType,
    get_attribute_groups_to_translate,
    translated_attribute_groups_to_schemas,
)
from ..functions.characteristic import (
    CharacteristicsToTranslateType, CharacteristicsTranslatedType,
    CharacteristicsWithValuesType, LoadedModifiersType,
    get_characteristics_to_translate, get_modifiers_to_translate,
    make_characteristics_from_translated,
    make_modifiers_from_translated,
)
from ..functions.media import make_thumbnail

debugger = logging.getLogger('debugger')


@dataclass
class ProductData:
    characteristics: CharacteristicsWithValuesType
    loaded_modifiers: LoadedModifiersType
    attribute_groups_data: AttributeGroupsDataType
    list_category: StoreCategory | schemas.ProductListCategorySchema | None = None


ProductToTranslateType = (
        dict[StoreProduct, dict[str, str | None]] |
        CharacteristicsToTranslateType |
        AttributesToTranslateType
)
ProductTranslatedType = (
        dict[str, StoreProduct, dict[str, str | None]] |
        CharacteristicsTranslatedType |
        AttributesTranslatedType
)
ProductsType = (
        list[tuple[StoreProduct, Translation | None]] |
        list[tuple[StoreProduct, Translation | None, StoreCategory | None]]
)

ProductsDataType = list[tuple[StoreProduct, ProductData]]


async def get_product_to_translate(
        product: StoreProduct,
        store_id: int, lang: str,
        translation: Translation | None = None,
        product_objects_data: ProductObjectsData | None = None
) -> tuple[
    ProductData,
    ProductToTranslateType
]:
    to_translate: ProductToTranslateType = {}

    characteristics, characteristics_to_translate = await (
        get_characteristics_to_translate(
            product.id, lang, False,
            product_objects_data,
        )
    )
    to_translate.update(characteristics_to_translate)

    loaded_modifiers, modifiers_to_translate = await get_modifiers_to_translate(
        product.id,
        product.product_group_id,
        store_id, lang,
        product_objects_data,
    )
    to_translate.update(modifiers_to_translate)

    attribute_groups_data, attribute_groups_to_translate = await (
        get_attribute_groups_to_translate(
            product.brand_id, product.id,
            lang, product_objects_data,
        )
    )
    to_translate.update(attribute_groups_to_translate)

    to_translate[product] = TranslateObjectData(
        object=product,
        translation=translation,
    )

    return ProductData(
        characteristics,
        loaded_modifiers,
        attribute_groups_data,
    ), to_translate


def floating_sum_settings_to_schema(product: StoreProduct):
    if not product.floating_sum_enabled:
        return None
    return schemas.FloatingSumSettings(
        is_enabled=product.floating_sum_enabled,
        min=round(product.floating_sum_min / 100, 2),
        max=round(product.floating_sum_max / 100, 2),
        options=product.floating_sum_options,
        user_sum_enabled=product.floating_sum_user_sum_enabled,
    )


async def translated_product_to_schema(
        product: StoreProduct,
        store_id: int, lang: str,
        data: ProductData,
        translated: ProductTranslatedType,
        product_objects_data: ProductObjectsData | None = None,
        brand: Brand | None = None,
):
    translated_product = translated[product]

    characteristics = make_characteristics_from_translated(
        translated, data.characteristics
    )
    modifiers = make_modifiers_from_translated(translated, data.loaded_modifiers)
    attribute_groups = translated_attribute_groups_to_schemas(
        data.attribute_groups_data, translated
    )

    store_product_price = product.price
    store_product_old_price = product.old_price

    if product_objects_data:
        spot_prices = product_objects_data.spot_prices
    else:
        spot_prices = await crud.get_store_product_spot_price(store_id, product.id)
    if spot_prices:
        (store_product_price, store_product_old_price) = spot_prices

    if product_objects_data:
        categories = product_objects_data.categories
    else:
        categories = await crud.get_product_categories(
            product.id, store_id, fields=("id",)
        )

    if product.is_weight and product.external_type == 'poster':
        unit = await f("store product poster weight unit name", lang)
    elif product.is_weight and product.liqpay_unit_name:
        unit = product.liqpay_unit_name
    else:
        unit = product.weight_unit

    gallery = await get_product_gallery(product.id, product_objects_data)

    if product_objects_data:
        image_media = product_objects_data.media_data.media
    else:
        image_media = await product.get_media()

    if image_media:
        if not brand:
            brand = await Brand.get(product.brand_id)

        if brand.thumbnails_mode == schemas.ThumbnailsModeEnum.SEVEN_LOC:
            if product_objects_data:
                thumbnail_media = product_objects_data.media_data.thumbnail_media
            else:
                thumbnail_media = None
            thumbnail_media = await get_or_create_product_thumbnail_media(
                product, image_media, thumbnail_media,
            )
            thumbnail_url = thumbnail_media.url if thumbnail_media else None
        else:
            if not brand.product_image_aspect_ratio:
                await get_or_create_product_thumbnail_media(
                    product, image_media,
                    force_generate=True,
                )
            thumbnail_url = make_preview_url(
                "image", image_media.file_path, brand.thumbnail_size
            )
    else:
        thumbnail_url = None

    return schemas.ProductSchema(
        id=product.id,
        is_available=product.is_available,
        product_id=product.product_id,
        get_order_id=product.get_order_id,
        name=translated_product["name"],
        price=round(store_product_price / 100, 2),
        image_url=image_media.url if image_media else None,
        thumbnail_url=thumbnail_url,
        gallery=gallery,
        description=translated_product["description"],
        external_id=product.external_id,
        external_type=product.external_type,
        old_price=round(
            store_product_old_price / 100, 2
        ) if store_product_old_price else 0,
        is_weight=product.is_weight,
        unit=unit,
        buy_min_quantity=product.buy_min_quantity,
        position=product.position,
        brand_id=product.brand_id,
        categories=categories,
        attribute_groups=attribute_groups,
        characteristics=characteristics,
        modifiers=modifiers,
        floating_sum_settings=floating_sum_settings_to_schema(product),
        floating_qty_enabled=product.floating_qty_enabled,
        pti_info_text=translated_product["pti_info_text"],
        pti_info_link=parse_product_pti_info_link(
            product.id, store_id, lang, product.pti_info_link
        ),
        type=product.type,
        liqpay_id=product.liqpay_id,
        liqpay_unit_name=product.liqpay_unit_name,
        liqpay_codifier=product.liqpay_codifier,
        liqpay_tax_list=product.liqpay_tax_list,
        need_auth=product.need_auth,
        topup_account_id=product.topup_account_id,
        list_category=data.list_category,
        product_group_id=product.product_group_id if product.product_group_id else None,
    )


async def get_or_create_product_thumbnail_media(
        product: StoreProduct,
        image_media: MediaObject,
        thumbnail_media: MediaObject | None = None,
        force_generate: bool = False
):
    if image_media:
        if product.thumbnail_media_id and not force_generate:
            return thumbnail_media or await product.get_thumbnail_media()

        try:
            brand = await Brand.get(product.brand_id)
            thumbnail_media = await make_thumbnail(image_media, brand)
            await product.update(thumbnail_media=thumbnail_media)

            return thumbnail_media
        except Exception as ex:
            logging.error(
                f"Error while making thumbnail for product {product.id}, media: "
                f"{image_media.id}: {ex}",
                exc_info=True
            )

    elif product.thumbnail_media_id:
        await product.update(thumbnail_media=None)

    return None


async def product_to_schema(
        product: StoreProduct,
        store_id: int,
        lang: str,
        translation: Translation | None = None,
        brand: Brand | None = None,
        group: Group | None = None,
        list_category: StoreCategory | schemas.ProductListCategorySchema | None = None
) -> schemas.ProductSchema:
    if not group:
        group = await Group.get_by_brand(product.brand_id)

    origin_lang = group.lang
    is_translate = group.is_translate

    data, to_translate = await get_product_to_translate(
        product, store_id, lang, translation
    )
    data.list_category = list_category

    translated: CharacteristicsTranslatedType = await td(
        to_translate, lang, origin_lang,
        group_id=group.id,
        is_auto_translate_allowed=is_translate,
    )

    return await translated_product_to_schema(
        product, store_id, lang,
        data, translated,
        brand=brand,
    )


async def get_products_to_translate(
        products: ProductsType,
        store_id: int, lang: str,
        products_objects: dict[int, ProductObjectsData]
) -> tuple[ProductsDataType, ProductToTranslateType]:
    products_data: ProductsDataType = []
    to_translate: ProductToTranslateType = {}

    for row in products:
        product, translation = row[0], row[1]

        product_data, product_to_translate = await get_product_to_translate(
            product, store_id, lang, translation,
            products_objects.get(product.id),
        )

        if len(row) == 3:
            product_data.list_category = row[2]

        products_data.append((product, product_data))
        to_translate.update(product_to_translate)

    return products_data, to_translate


async def translated_products_to_schemas(
        products_data: ProductsDataType,
        store_id: int, lang: str,
        translated: ProductTranslatedType,
        products_objects: dict[int, ProductObjectsData],
        brand: Brand | None = None,
):
    if not brand:
        brand = await crud.get_brand_by_store(store_id)

    result: list[schemas.ProductSchema] = []

    for product, product_data in products_data:
        result.append(
            await translated_product_to_schema(
                product, store_id,
                lang, product_data,
                translated,
                products_objects.get(product.id),
                brand=brand,
            )
        )
    return result


async def products_list_to_schemas(
        products: ProductsType,
        store_id: int, group_id: int,
        lang: str, origin_lang: str,
        is_translate: bool,
        brand: Brand | None = None,
):
    products_objects = await crud.get_products_objects(
        list(map(lambda x: x[0].id, products)),
        store_id, lang,
    )

    products_data, to_translate = await get_products_to_translate(
        products, store_id, lang, products_objects
    )

    translated = await td(
        to_translate, lang,
        origin_lang,
        group_id=group_id,
        is_auto_translate_allowed=is_translate,
    )

    return await translated_products_to_schemas(
        products_data, store_id,
        lang, translated,
        products_objects,
        brand,
    )


async def get_product_gallery(
        product_id: int,
        product_objects_data: ProductObjectsData | None = None
) -> schemas.Gallery | None:
    if product_objects_data:
        gallery_items = product_objects_data.gallery_items
    else:
        gallery_items = await crud.get_product_gallery_items(product_id)

    if not gallery_items:
        return None

    items: list[schemas.GalleryItem] = []

    for item, media in gallery_items:
        items.append(
            schemas.GalleryItem(
                id=item.id,
                media_id=media.id,
                media_type=media.media_type,
                media_url=media.url,
                position=item.position
            )
        )

    return schemas.Gallery(items=items)


def parse_product_pti_info_link(
        product_id: int,
        store_id: int,
        lang: str,
        link: str | None = None,
) -> str | None:
    if not link:
        return None

    link = link.replace("{product_id}", str(product_id))
    link = link.replace("{store_id}", str(store_id))
    link = link.replace("{lang}", lang)
    link = link.strip()

    if not link.startswith("http") and not link.startswith("/"):
        link = f"/{link}"

    return link
