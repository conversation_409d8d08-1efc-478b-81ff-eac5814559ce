from fastapi import Depends, Path

import schemas
from core.api.depends import get_lang
from db import crud
from db.models import Brand, Group, Store, StoreProduct
from .exceptions import FindModificationValueIsNotSpecifiedError, ProductNotFoundError
from .functions import product_to_schema, products_list_to_schemas
from ..depends import get_current_brand


class ProductsService:
    def __init__(
            self,
            store_id: int = Path(),
            brand: Brand = Depends(get_current_brand),
            lang: str = Depends(get_lang)
    ):
        assert isinstance(store_id, int)
        assert isinstance(lang, str)

        self._store_id: int = store_id
        self.lang = lang

        self._store: Store | None = None
        self.brand: Brand = brand

    @property
    def store_id(self):
        return self._store_id

    @property
    async def store(self) -> Store:
        if not self._store:
            self._store = await Store.get(self.store_id)
        return self._store

    async def get_products_list(
            self, params: schemas.ProductsListParams,
    ) -> schemas.ProductsListResponse:
        if params.cursor:
            match params.sort:
                case schemas.ProductsSortEnum.CATEGORIES:
                    cursor = schemas.ProductsCategoriesCursor.from_str(params.cursor)
                case (
                schemas.ProductsSortEnum.HIGH_PRICE |
                schemas.ProductsSortEnum.LOW_PRICE
                ):
                    cursor = schemas.ProductPriceCursor.from_str(params.cursor)
                case _:
                    cursor = None
        else:
            cursor = None

        if params.search_text and params.sort == schemas.ProductsSortEnum.CATEGORIES:
            params.sort = schemas.ProductsSortEnum.DEFAULT

        db_products = await crud.get_store_products(
            self.brand.id, self.store_id, params,
            lang=self.lang, with_translations=True,
            cursor=cursor,
        )

        group = await Group.get(self.brand.group_id)

        products = await products_list_to_schemas(
            db_products, self.store_id, group.id,
            self.lang, group.lang, group.is_translate,
        )

        response = schemas.ProductsListResponse(
            data=products,
        )

        if len(db_products) > 0:
            product = db_products[-1][0]

            match params.sort:
                case schemas.ProductsSortEnum.CATEGORIES:
                    category = db_products[-1][2]
                    cursor = schemas.ProductsCategoriesCursor(
                        direction=schemas.CursorDirection.NEXT,
                        is_available=product.is_available,
                        is_other=not category or category.id == params.category_id,
                        category_position=category.position if category else None,
                        product_position=product.position,
                    )
                case (
                schemas.ProductsSortEnum.LOW_PRICE |
                schemas.ProductsSortEnum.HIGH_PRICE
                ):
                    cursor = schemas.ProductPriceCursor(
                        direction=schemas.CursorDirection.NEXT,
                        is_available=product.is_available,
                        price=product.price,
                        product_position=product.position,
                    )
                case _:
                    cursor = None

            if cursor and await crud.get_store_products(
                    self.brand.id, self.store_id, params,
                    lang=self.lang, with_translations=True,
                    cursor=cursor,
                    is_exists=True,
            ):
                response.cursor = cursor.to_str()

        return response

    async def get_product(self, product_id: int):
        product = await StoreProduct.get(product_id, is_deleted=False, is_enabled=True)
        if not product:
            raise ProductNotFoundError()

        return await product_to_schema(product, self.store_id, self.lang)

    async def find_modification(
            self, product_id: int,
            data: schemas.FindProductModificationData,
    ):
        if (not data.find_for_characteristic_id or data.find_for_characteristic_id not
                in data.filters):
            raise FindModificationValueIsNotSpecifiedError()

        product = await crud.find_product_modification(product_id, data)
        if not product:
            raise ProductNotFoundError()

        return await product_to_schema(product, self.store_id, self.lang)

    async def get_min_max_prices(
            self, data: schemas.GetMinMaxPricesData,
    ) -> schemas.ProductsMinMaxPrices:
        result = await crud.get_products_min_max_prices(
            self.store_id,
            data.category_id,
            data.filters_set_id,
            data.filters,
        )

        if result:
            min_price = round(result[0] / 100, 2) if result[0] else 0
            max_price = round(result[1] / 100, 2) if result[1] else 0
        else:
            min_price, max_price = 0, 0

        return schemas.ProductsMinMaxPrices(
            min=min_price, max=max_price,
        )
