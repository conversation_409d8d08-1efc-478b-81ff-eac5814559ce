from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class ProductNotFoundError(ErrorWithHTTPStatus):
    status_code = 404
    text_variable = "product not found error"


class StoreNotFoundError(ErrorWithHTTPStatus):
    status_code = 404
    text_variable = "store not found error"


class BrandNotFoundError(ErrorWithHTTPStatus):
    status_code = 404
    text_variable = "brand not found error"


class UnknownDocumentTypeError(ErrorWithHTTPStatus):
    status_code = 400
    text_variable = "unknown document type error"

    def __init__(self, document_type: str):
        super().__init__(document_type=document_type)


class DocumentNotFoundError(ErrorWithHTTPStatus):
    status_code = 404
    text_variable = "document not found error"


class AttributeNotFoundError(ErrorWithHTTPStatus):
    status_code = 404
    text_variable = "attribute not found error"

    def __init__(self, attribute_id: int):
        super().__init__(attribute_id=attribute_id)


class IncorrectAttributeGroupError(ErrorWithHTTPStatus):
    status_code = 400
    text_variable = "incorrect attribute group error"


class CreateFiltersSetNoFiltersError(ErrorWithHTTPStatus):
    status_code = 400
    text_variable = "create filters set no filters error"


class FindModificationValueIsNotSpecifiedError(ErrorWithHTTPStatus):
    status_code = 400
    text_variable = "find modification value is not specified error"


class NotHaveAccessToOrderError(ErrorWithHTTPStatus):
    text_variable = "user dont have access to order"
    status_code = status.HTTP_403_FORBIDDEN


class NoWritePermissionToOrderError(ErrorWithHTTPStatus):
    text_variable = "user dont have write permission to order"
    status_code = status.HTTP_403_FORBIDDEN


class InvalidTokenOrderError(ErrorWithHTTPStatus):
    text_variable = "user invalid token to order"
    status_code = status.HTTP_403_FORBIDDEN


class TokenNotFoundOrderError(ErrorWithHTTPStatus):
    text_variable = "order token not found"
    status_code = status.HTTP_404_NOT_FOUND


class OrderNotFoundOrderError(ErrorWithHTTPStatus):
    text_variable = "client invoice order not found text"
    status_code = status.HTTP_404_NOT_FOUND


class UserNotFoundError(ErrorWithHTTPStatus):
    text_variable = "client bot add friend not found text"
    status_code = status.HTTP_401_UNAUTHORIZED

    def __init__(self, user_id: int):
        super().__init__(user_id=user_id)
