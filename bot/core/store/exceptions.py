from abc import ABC

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BaseShipmentPricesError(ErrorWithHTTPStatus, ABC, base=True):
    def __init__(self, message: str = "Shipment prices error", **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"Shipment prices error: {self.message}"


class ShipmentPricesMinimumOrderSumError(BaseShipmentPricesError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "shipment prices min order sum error"

    def __init__(self, min_sum: str):
        super().__init__(
            min_sum=min_sum,
        )


class ShipmentPricesMinimumOrderSumNoDeliveryError(BaseShipmentPricesError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "shipment prices min order sum no delivery error"

    def __init__(self, min_sum: str):
        super().__init__(
            min_sum=min_sum,
        )


class ShipmentPricesMaximumOrderSumError(BaseShipmentPricesError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "shipment prices max order sum error"

    def __init__(self, max_sum: str):
        super().__init__(
            max_sum=max_sum,
        )


class ShipmentPricesMaximumOrderSumNoDeliveryError(BaseShipmentPricesError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "shipment prices max order sum no delivery error"

    def __init__(self, max_sum: str):
        super().__init__(
            max_sum=max_sum,
        )


class ShipmentPricesNoPriceError(BaseShipmentPricesError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "shipment prices get price error"


class ShipmentPricesNoPriceForAddressError(BaseShipmentPricesError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "shipment prices address not in zones error"


class ShipmentPricesNoCoordinatesForZonesError(BaseShipmentPricesError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "shipment prices no coordinates for zones error"


class OrderNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "order not found error"
