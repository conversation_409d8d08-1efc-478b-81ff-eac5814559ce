from operator import attrgetter
from typing import Literal

import math

from core.geo import get_coordinates
from core.geo.functions import check_in_distance, check_inside
from core.store.exceptions import (
    ShipmentPricesNoCoordinatesForZonesError,
    ShipmentPricesNoPriceForAddressError,
)
from db import crud
from db.models import Brand, ShipmentPrice, ShipmentZone, Store


class ShipmentPriceCalculator:

    def __init__(
            self, brand: Brand,
            store_id: int | None = None,
            shipment_id: int | None = None,
            order_sum: float = 0.0,
            address: str | None = None,
            lang: str | bool = False,
            is_next_price_cheaper: bool = False,
            store_settings_id: int | None = None,
            address_lat: int | None = None,
            address_lng: int | None = None,
            purpose: Literal["prices", "price", "validate"] = "prices"
    ):
        self.brand: Brand = brand
        self.order_sum: float | None = order_sum
        self.shipment_id: int | None = shipment_id
        self.store_id: int | None = store_id
        self.address: str | None = address
        self.lang: str | bool = lang
        self.is_next_price_cheaper: bool = is_next_price_cheaper

        self.zones: list[ShipmentZone] = []
        self.zones_prices: dict[int, list[ShipmentPrice]] = {}
        self.available_prices: list[ShipmentPrice] = []
        self.price: ShipmentPrice | None = None
        self.prices: list[ShipmentPrice] | None = None
        self.store_settings_id = store_settings_id
        self.address_lat = address_lat
        self.address_lng = address_lng
        self.purpose = purpose

    async def get_store(self) -> Store | None:
        if self.store_id:
            return await Store.get(id=self.store_id)

        stores = await crud.get_stores(brand_id=self.brand.id)
        if len(stores) == 1:
            return stores[0]
        return None

    async def check_address(self):
        if not self.address or not any([self.address_lat, self.address_lng]):
            return

        point = None
        zones = await crud.get_shipment_zones(
            brand_id=self.brand.id, store_id=self.store_id,
            shipment_store_settings_id=self.store_settings_id,
        )
        if self.address_lat and self.address_lng:
            point = [self.address_lat, self.address_lng]
        if not point and zones and self.purpose != "prices":
            if self.purpose == "validate":
                raise ShipmentPricesNoCoordinatesForZonesError()
            else:
                raise ShipmentPricesNoPriceForAddressError()

        if not point:
            point = await get_coordinates(self.address, self.lang)
        if not point:
            return

        if not zones:
            return

        store = await self.get_store()
        zones = sorted(zones, key=attrgetter("is_polygon"), reverse=True)

        for zone in zones:
            if zone.is_polygon and zone.polygon and check_inside(
                    zone.polygon, point, zone.is_swap_coordinates
            ):
                self.zones.append(zone)
                continue

            if not store or not any([store.latitude, store.longitude]):
                break

            if zone.is_distance and check_in_distance(
                    [store.latitude, store.longitude], zone.distance, point
            ):
                self.zones.append(zone)

    async def get_prices(self, is_zone: bool = True):
        if is_zone:
            await self.check_address()

        if is_zone and self.zones:
            self.zones_prices = await crud.get_shipment_prices(
                self.brand.id, zone_ids=[zone.id for zone in self.zones]
            )
            prices = []
            for items in self.zones_prices.values():
                prices.extend(items)
        else:
            prices = await crud.get_shipment_prices(
                self.brand.id, settings_id=self.shipment_id
            )

        self.prices = sorted(prices, key=attrgetter("minimum_order_amount"))

    def get_available_prices_by_min_order_sum(self, prices: list[ShipmentPrice]) -> \
            list[ShipmentPrice]:
        return [price for price in prices if
                self.order_sum >= price.minimum_order_amount]

    def get_available_prices_by_max_order_sum(self, prices: list[ShipmentPrice]) -> \
            list[ShipmentPrice]:
        return [
            price for price in prices
            if math.isclose(price.maximum_order_amount, 0.0) or (
                    self.order_sum <= price.maximum_order_amount)
        ]

    def get_available_prices(self) -> list[ShipmentPrice]:
        prices = self.get_available_prices_by_min_order_sum(self.prices)
        prices = self.get_available_prices_by_max_order_sum(prices)
        return prices

    def check_order_sum(self, prices: list[ShipmentPrice]):
        for price in prices:
            if not self.price or price.cost_delivery < self.price.cost_delivery:
                self.price = price

    async def get_price(self, zones_exist: bool = False) -> ShipmentPrice | None:
        if self.prices is None:
            await self.get_prices()

        self.available_prices = self.get_available_prices()
        self.check_order_sum(self.available_prices)

        if self.price and not self.zones and zones_exist:
            return None

        if not self.price and self.zones:
            await self.get_prices(is_zone=False)
            self.available_prices = self.get_available_prices()
            self.check_order_sum(self.available_prices)
        else:
            return self.price

        return self.price

    async def get_next_price(self) -> ShipmentPrice | None:
        if not self.price or not self.prices:
            return None

        prices = self.get_available_prices_by_max_order_sum(self.prices)

        for price in prices:
            if price.minimum_order_amount > self.price.minimum_order_amount and (
                    not self.is_next_price_cheaper or price.cost_delivery <
                    self.price.cost_delivery
            ):
                next_price = price
                break
        else:
            next_price = None

        return next_price
