from datetime import datetime

from pydantic import BaseModel, Field


class TestAPIResult(BaseModel):
    group_name: str = Field(description="Group name")


class BaseResponseSchema(BaseModel):
    date_from: datetime = Field(description="Start date for load data")
    date_to: datetime = Field(description="end date for load data")
    offset: int = Field(description="offset for load data")
    limit: int = Field(description="limit for load data")
    store_id: int | None = Field(None, description="id store for load data")

    is_end: bool = Field(False, description="flag if finish load data")
    next_filters: dict | None = Field(None, description="filters settings for next load data")
