from datetime import datetime

from pydantic import BaseModel, Field


class ExternDataFilters(BaseModel):
    date_from: datetime | None = Field(None, description="Start date in iso format for load data")
    date_to: datetime | None = Field(None, description="end date in iso format for load data")
    store_id: int | None = Field(None, description="id store for load data")
    offset: int = Field(0, description="offset for load data")
    limit: int = Field(100, description="limit for load data")
