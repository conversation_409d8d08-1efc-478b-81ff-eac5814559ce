from datetime import datetime
from typing import Any, Literal

from incust_api.api import term
from pydantic import BaseModel, Field

from schemas import InvoicePaymentModeLiteral, InvoiceStatusLiteral
from .common import BaseResponseSchema

InvoiceTypeLiteral = Literal[
    "store_order", "event", "menu_in_store",
    "VM", "from_manager", "from_link",
    "integration", "unknown",
]


class InvoiceExternalDataSchema(BaseModel):
    id: int = Field(description="id invoice")
    status: InvoiceStatusLiteral = Field(description="status invoice")

    currency: str = Field(description="currency invoice")
    prices: list[term.m.CheckItem]

    title: str = Field(description="title invoice")
    description: str = Field(description="description invoice")
    photo: str | None = Field(None, description="photo invoice")

    first_name: str | None = Field(None, description="first name customer")
    last_name: str | None = Field(None, description="last name customer")
    email: str | None = Field(None, description="email customer")
    phone: str | None = Field(None, description="phone customer")

    incust_check: dict | None = Field(None, description="incust check data for invoice")

    total_amount: str = Field(description="total amount invoice", deprecated=True)
    total_amount_raw: float = Field(description="raw total amount invoice", deprecated=True)

    shipment_cost: float
    custom_payment_cost: float

    before_loyalty_sum: float
    discount: float
    bonuses_redeemed: float
    discount_and_bonuses_redeemed: float

    total_sum: float
    tips_sum: float
    sum_to_pay: float

    payer_fee: float
    paid_sum: float

    external_transaction_id: str | None = Field(None, description="id external transaction invoice")
    client_redirect_url: str | None = Field(None, description="client redirect url invoice")

    webhook_result: dict[str, Any] | None = Field(None, description="webhook result data invoice")

    user_id: int = Field(description="id user invoice")
    payer_id: int | None = Field(None, description="id payer invoice")
    is_friend: bool | None = Field(None, description="flag if friend invoice")

    user_comment_label: str | None = Field(None, description="user comment label invoice")
    user_comment: str | None = Field(None, description="user comment invoice")

    payed_in_bot_id: int | None = Field(None, description="id payed bot invoice")

    time_created: datetime = Field(description="time created invoice")

    payment_mode: InvoicePaymentModeLiteral | None = None
    invoice_type: InvoiceTypeLiteral

    group_id: int = Field(description="id group invoice")
    creator_id: int = Field(description="id creator invoice")

    invoice_template_id: int | None = Field(None, description="id invoice template invoice")
    bot_id: int | None = Field(None, description="id bot invoice")
    menu_in_store_id: int | None = Field(None, description="id menu in store invoice")
    store_order_id: int | None = Field(None, description="id store order invoice")
    transaction_id: int | None = Field(None, description="id transaction invoice")
    payment_bot_menu_id: int | None = Field(None, description="id payment bot menu invoice")
    payment_bot_id: int | None = Field(None, description="id payment bot invoice")

    need_name: bool = Field(description="flag need name invoice")
    need_phone_number: bool = Field(description="flag need phone number invoice")
    need_email: bool = Field(description="flag need email invoice")

    expiration_datetime: datetime | None = Field(None, description="expiration datetime invoice")
    live_time: float | None = Field(None, description="live time invoice")

    order_info: dict | None = None
    message_id: int | None = None

    check_url: str | None = Field(None, description="check url invoice")
    successful_payment_callback_url: str | None = Field(None, description="successful payment callback url invoice")
    uuid_id: str | None = None


class InvoiceResponseSchema(BaseResponseSchema):
    invoices: list[InvoiceExternalDataSchema]
    statuses: list[InvoiceStatusLiteral] | None = None
