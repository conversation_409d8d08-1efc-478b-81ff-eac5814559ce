from datetime import datetime
from typing import Literal

from pydantic import Field

from schemas import (
    BillingAddress, BaseOrderSchema, OrderAttributeSchema,
    OrderShipmentSchema, OrderStatusPayType, OrderStatusType,
)
from schemas.base import BaseORMModel

from .common import BaseResponseSchema

ShipmentStatusLiteral = Literal[
    "open_unconfirmed", "open_confirmed",
    "payed", "closed", "canceled",
    "wait_for_ship", "shipped", "in_transit", "delivered",
]


class OrderHistorySchema(BaseORMModel):
    time_created: datetime
    status: ShipmentStatusLiteral
    comment: str | None = None


class ExternalOrderSchema(BaseORMModel):
    id: int
    brand_id: int

    external_order_id: str | None = None
    external_type: str

    json_data: dict | None = None
    status: ShipmentStatusLiteral | None = None

    create_date: datetime
    update_date: datetime


class OrderProductExternDataSchema(BaseORMModel):
    id: int
    name: str
    quantity: int = Field(description="count this products in order")

    price: float = Field(description="price at the time of order")
    price_with_attributes: float | None = Field(None, description="price including attributes")
    discount_amount: float | None = Field(None, description="discount amount")
    price_after_loyalty: float | None = Field(None, description="price including loyalty")
    bonuses_redeemed: float | None = Field(None, description="bonuses written off from loyalty")
    discount_sum: float | None = Field(None, description="discount amount per item")
    before_loyalty_sum: float | None = Field(None, description="position sum with attributes, up to loyalty")

    product_id: int
    attributes: list[OrderAttributeSchema] | None = None

    display_name: str | None = None
    display_description: str | None = None


class BaseOrderExternDataSchema(BaseOrderSchema):
    id: int
    user_id: int
    store_id: int
    currency: str

    total_sum: float = Field(description="total sum, tips excluded")
    sum_to_pay: float = Field(description="total sum, tips included")
    before_loyalty_sum: float = Field(description="total sum, tips excluded, before loyalty")

    original_incust_loyalty_check: dict | None = None
    date_sent_to_friend: datetime | None = None
    create_date: datetime

    status: OrderStatusType
    status_pay: OrderStatusPayType

    token: str | None = None
    shipment: OrderShipmentSchema

    order_products: list[OrderProductExternDataSchema]


class OrderExternalDataSchema(BaseOrderExternDataSchema):
    brand_name: str

    invoice_id: int | None = None
    transaction_id: int | None = None
    get_order_id: int | None = None

    delivery_method: str | None = None

    external_order: ExternalOrderSchema | None = None

    incust_loyalty_check: dict | None = None
    incust_export: dict | None = None

    statuses: list[OrderHistorySchema] | None = None

    billing_address: BillingAddress | None = None


class OrderResponseSchema(BaseResponseSchema):
    orders: list[OrderExternalDataSchema]
    statuses: list[ShipmentStatusLiteral] | None = None
