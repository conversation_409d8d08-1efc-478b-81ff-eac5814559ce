from utils.exceptions import ErrorWithHTTPStatus


class ExternDataAPIUnauthorized(ErrorWithHTTPStatus):
    text_variable = "extern data api unauthorized error"
    status_code = 401
    groups = ["read_data"]
    summary = "unauthorized"


class BrandNotFoundExternDataAPI(ErrorWithHTTPStatus):
    text_variable = "extern data brand not found error"
    status_code = 404
    groups = ["read_data"]
    summary = "brand not found"


class DateTimeIncorrectFormatdExternDataAPI(ErrorWithHTTPStatus):
    text_variable = "extern data datetime incorrect format error"
    status_code = 400
    groups = ["read_data"]
    summary = "datetime incorrect format"


class OrdersExternDataAPI(ErrorWithHTTPStatus):
    text_variable = "extern data get orders error"
    status_code = 500
    groups = ["read_data"]
    summary = "get orders"


class InvoicesExternDataAPI(ErrorWithHTTPStatus):
    text_variable = "extern data get invoices error"
    status_code = 500
    groups = ["read_data"]
    summary = "get invoices"
