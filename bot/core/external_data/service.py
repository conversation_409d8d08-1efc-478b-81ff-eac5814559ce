import calendar
from datetime import datetime, timedelta
from typing import Any

from fastapi import Depends
from incust_api.api import term

from core.api.depends import get_api_token, get_lang
from db import crud, db_func
from db.models import (
    Brand, Group, Invoice, OrderAttribute, OrderProduct, OrderShippingStatus,
    StoreAttribute, StoreOrder,
)
from schemas import (
    BillingAddress, OrderAttributeSchema, OrderHistorySchema,
    OrderShipmentSchema,
)
from utils.numbers import format_currency
from . import schemas
from .exceptions import (
    BrandNotFoundExternDataAPI, ExternDataAPIUnauthorized, InvoicesExternDataAPI,
    OrdersExternDataAPI,
)
from .types import ExternDataType, ExternDataTypeLiteral


class ExternDataService:

    def __init__(self, group: Group, brand: Brand, lang: str):
        self.group = group
        self.brand = brand
        self.lang = lang

    @classmethod
    async def depend(
            cls,
            api_token: str = Depends(get_api_token),
            lang: str = Depends(get_lang),
    ):
        group = await crud.get_group_by_api_token(api_token)
        if not group:
            raise ExternDataAPIUnauthorized()

        brand = await crud.get_brand_by_group(group.id)
        if not brand:
            raise BrandNotFoundExternDataAPI()

        return cls(group, brand, lang)

    def test_api(self):
        return schemas.TestAPIResult(group_name=self.group.name)

    @classmethod
    def check_filters(cls, filters: schemas.ExternDataFilters):
        now = datetime.utcnow()
        if not filters.date_from:
            filters.date_from = now.replace(day=1)

        month_days = calendar.monthrange(filters.date_from.year, filters.date_from.month)[1]

        if not filters.date_to:
            date_to = filters.date_from + timedelta(days=month_days)
            filters.date_to = now if date_to > now else date_to

        elif filters.date_to > now:
            filters.date_to = now
        elif filters.date_to > (filters.date_from + timedelta(days=month_days)):
            filters.date_to = filters.date_from + timedelta(days=month_days)

        filters.limit = min(filters.limit, 100)

        return filters

    @db_func
    def get_orders(
            self, filters: schemas.ExternDataFilters,
            statuses: list[schemas.ShipmentStatusLiteral] | None = None,
    ) -> list[schemas.OrderExternalDataSchema]:
        orders = crud.get_orders_external_data(self.brand.id, **filters.dict(), statuses=statuses)
        order_ids = [order.id for order in orders]

        order_products = crud.get_orders_products_external_data(order_ids)
        order_attributes = crud.get_order_product_attributes_external_data(list(order_products.keys()))

        shipments = crud.get_orders_shipments_external_data(order_ids)
        shipments = self.make_schemas_from_models(shipments, OrderShipmentSchema)

        db_statuses = crud.get_shipment_statuses_external_data(order_ids)
        db_statuses = self.make_order_statuses_schemas(db_statuses)

        external_orders = crud.get_orders_external_orders_external_data(order_ids)
        external_orders = self.make_schemas_from_models(external_orders, schemas.ExternalOrderSchema)

        billings = crud.get_orders_billing_address_external_data(order_ids)
        billings = self.make_schemas_from_models(billings, BillingAddress)

        return self.make_orders_schemas(
            orders, order_products, order_attributes,
            shipments, db_statuses,
            external_orders, billings,
        )

    @classmethod
    def make_schemas_from_models(cls, models: dict[int, Any], schema_cls: Any) -> dict[int, Any]:
        results = {}
        for order_id, data in models.items():
            if data:
                data = schema_cls.from_orm(data)
            results[order_id] = data
        return results

    @classmethod
    def make_order_statuses_schemas(
            cls,
            statuses: dict[int, list[OrderShippingStatus]],
    ) -> dict[int, list[OrderHistorySchema]]:
        results = {}
        for order_id, data in statuses.items():
            if data:
                data = [OrderHistorySchema.from_orm(item) for item in data]
            results[order_id] = data
        return results

    def make_orders_schemas(
            self,
            orders: list[StoreOrder],
            order_products: dict[int, list[OrderProduct]],
            order_attributes: dict[int, list[tuple[OrderAttribute, StoreAttribute]]],
            shipments: dict[int, OrderShipmentSchema],
            statuses: dict[int, list[OrderHistorySchema]],
            external_orders: dict[int, schemas.ExternalOrderSchema],
            billings: dict[int, BillingAddress],
    ) -> list[schemas.OrderExternalDataSchema]:
        return [
            schemas.OrderExternalDataSchema(
                id=order.id,
                brand_name=order.brand_name,
                first_name=order.first_name,
                last_name=order.last_name,
                phone=order.phone,
                email=order.email,
                payment_method=order.payment_method,
                delivery_method=order.delivery_method,
                delivery_address=order.delivery_address,
                address_comment=order.address_comment,
                desired_delivery_date=order.desired_delivery_date,
                desired_delivery_time=order.desired_delivery_time,
                address_street=order.address_street,
                address_house=order.address_house,
                address_flat=order.address_flat,
                address_floor=order.address_floor,
                address_entrance=order.address_entrance,
                address_lat=order.address_lat,
                address_lng=order.address_lng,
                address_place_id=order.address_place_id,
                comment=order.comment,
                user_id=order.user_id,
                menu_in_store_id=order.menu_in_store_id,
                invoice_id=order.invoice_id,
                transaction_id=order.transaction_id,
                get_order_id=order.get_order_id,
                loyalty_type=order.loyalty_type,
                bonuses_redeemed=round(order.bonuses_redeemed / 100, 2) if order.bonuses_redeemed else 0.0,
                discount=round(order.discount / 100, 2) if order.discount else 0.0,
                discount_and_bonuses=round(order.discount_and_bonuses / 100, 2) if order.discount_and_bonuses else 0.0,
                tips_sum=round(order.tips_sum / 100, 2) if order.tips_sum else 0.0,
                total_sum=round(order.total_sum / 100, 2) if order.total_sum else 0.0,
                sum_to_pay=round(order.sum_to_pay / 100, 2) if order.sum_to_pay else 0.0,
                before_loyalty_sum=round(order.before_loyalty_sum / 100, 2) if order.before_loyalty_sum else 0.0,
                create_date=order.create_date,
                status_pay=order.status_pay,
                order_products=self.make_order_products_schemas(order_products.get(order.id, []), order_attributes),
                store_id=order.store_id,
                token=order.token,
                status=order.shipment_status,
                shipment=shipments.get(order.id),
                original_incust_loyalty_check=order.original_incust_loyalty_check,
                incust_loyalty_check=order.incust_loyalty_check,
                incust_export=order.incust_export,
                currency=order.currency,
                statuses=statuses.get(order.id),
                external_order=external_orders.get(order.id),
                billing_address=billings.get(order.id),
                date_sent_to_friend=order.date_sent_to_friend,
            ) for order in orders
        ]

    def make_order_products_schemas(
            self,
            order_products: list[OrderProduct],
            order_attributes: dict[int, list[tuple[OrderAttribute, StoreAttribute]]],
    ) -> list[schemas.OrderProductExternDataSchema]:
        return [
            schemas.OrderProductExternDataSchema(
                id=order_product.id,
                name=order_product.name,
                quantity=order_product.quantity,
                product_id=order_product.product_id,
                price=round(order_product.price / 100, 2) if order_product.price else 0.0,
                price_with_attributes=round(order_product.price_with_attributes / 100, 2),
                discount_amount=round(order_product.discount_amount / 100, 2),
                price_after_loyalty=round(order_product.price_after_loyalty / 100, 2),
                bonuses_redeemed=round(order_product.bonuses_redeemed / 100, 2),
                discount_sum=round(order_product.discount_sum / 100, 2),
                before_loyalty_sum=round(order_product.before_loyalty_sum / 100, 2),
                display_name=order_product.display_name,
                display_description=order_product.display_description,
                attributes=self.make_order_product_attributes_schemas(order_attributes.get(order_product.id, [])),
            ) for order_product in order_products
        ]

    @classmethod
    def make_order_product_attributes_schemas(
            cls,
            order_attributes: list[tuple[OrderAttribute, StoreAttribute]],
    ) -> list[OrderAttributeSchema]:
        return [
            OrderAttributeSchema(
                id=order_attribute.id,
                quantity=order_attribute.quantity,
                name=order_attribute.name,
                attribute_id=order_attribute.attribute_id,
                attribute_code=attribute.attribute_id,
                price_impact=round(order_attribute.price_impact / 100, 2) if order_attribute.price_impact else 0.0,
            ) for order_attribute, attribute in order_attributes
        ]

    @db_func
    def get_invoices(
            self, filters: schemas.ExternDataFilters,
            statuses: list[schemas.InvoiceStatusLiteral] | None = None,
    ) -> list[schemas.InvoiceExternalDataSchema]:
        invoices = crud.get_invoices_external_data(self.group.id, **filters.dict(), statuses=statuses)

        return self.make_invoices_schemas(invoices)

    def make_invoices_schemas(
            self,
            invoices: list[Invoice],
    ) -> list[schemas.InvoiceExternalDataSchema]:
        return [
            schemas.InvoiceExternalDataSchema(
                id=invoice.id,
                status=invoice.status,
                payment_mode=invoice.payment_mode,
                invoice_type=invoice.invoice_type.value,
                invoice_template_id=invoice.invoice_template_id,
                user_id=invoice.user_id,
                bot_id=invoice.bot_id,
                group_id=invoice.group_id,
                creator_id=invoice.creator_id,
                menu_in_store_id=invoice.menu_in_store_id,
                store_order_id=invoice.store_order.id if invoice.store_order else None,
                payer_id=invoice.payer_id,
                external_transaction_id=invoice.external_transaction_id,
                transaction_id=invoice.transaction_id,
                payment_bot_menu_id=invoice.payment_bot_menu_id,
                payment_bot_id=invoice.payment_bot_id,
                currency=invoice.currency,
                prices=[
                    item.from_orm_converted(term.m.CheckItem)
                    for item in invoice.items  # this function is called under @db_func
                ],
                title=invoice.title,
                description=invoice.description,
                photo=invoice.photo,
                first_name=invoice.first_name,
                last_name=invoice.last_name,
                email=invoice.email,
                phone=invoice.phone,
                incust_check=term.m.Check(**invoice.incust_check) if invoice.incust_check else None,
                total_amount=format_currency(invoice.converted_sum_to_pay, invoice.currency, locale=self.lang),
                total_amount_raw=invoice.converted_sum_to_pay,
                client_redirect_url=invoice.client_redirect_url,
                webhook_result=invoice.webhook_result,
                is_friend=invoice.is_friend,
                user_comment_label=invoice.invoice_template.comment_label_raw if invoice.invoice_template else None,
                user_comment=invoice.user_comment,
                payed_in_bot_id=invoice.payment_bot_menu_id or invoice.payment_bot_id or invoice.bot_id,
                time_created=invoice.time_created,
                need_name=invoice.need_name,
                need_phone_number=invoice.need_phone_number,
                need_email=invoice.need_email,
                expiration_datetime=invoice.expiration_datetime,
                live_time=invoice.live_time.total_seconds() if invoice.live_time else None,
                order_info=invoice.order_info,
                message_id=invoice.message_id,
                check_url=invoice.check_url,
                successful_payment_callback_url=invoice.successful_payment_callback_url,
                uuid_id=invoice.uuid_id,
                **invoice.converted_sums,
            ) for invoice in invoices
        ]

    async def read_data(
            self,
            data_type: ExternDataTypeLiteral,
            filters: schemas.ExternDataFilters,
            statuses: list[schemas.ShipmentStatusLiteral] | list[schemas.InvoiceStatusLiteral] | None = None,
    ) -> schemas.OrderResponseSchema | schemas.InvoiceResponseSchema:
        filters = self.check_filters(filters)

        match data_type:
            case ExternDataType.ORDER.value:
                try:
                    orders = await self.get_orders(filters, statuses)
                    is_end = len(orders) < filters.limit
                    return schemas.OrderResponseSchema(
                        date_from=filters.date_from,
                        date_to=filters.date_to,
                        offset=filters.offset,
                        limit=filters.limit,
                        store_id=filters.store_id,
                        orders=orders,
                        statuses=statuses,
                        is_end=is_end,
                        next_filters=None if is_end else schemas.ExternDataFilters(
                            **filters.dict(exclude={"offset"}),
                            offset=filters.offset + filters.limit,
                        ).dict(),
                    )
                except Exception as error:
                    raise OrdersExternDataAPI() from error

            case ExternDataType.INVOICE.value:
                try:
                    invoices = await self.get_invoices(filters, statuses)
                    is_end = len(invoices) < filters.limit
                    return schemas.InvoiceResponseSchema(
                        date_from=filters.date_from,
                        date_to=filters.date_to,
                        offset=filters.offset,
                        limit=filters.limit,
                        store_id=filters.store_id,
                        invoices=invoices,
                        statuses=statuses,
                        is_end=is_end,
                        next_filters=None if is_end else schemas.ExternDataFilters(
                            **filters.dict(exclude={"offset"}),
                            offset=filters.offset + filters.limit,
                        ).dict(),
                    )
                except Exception as error:
                    raise InvoicesExternDataAPI() from error

            case _:
                raise ValueError("invalid data_type")
