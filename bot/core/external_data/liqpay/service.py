import base64
import csv
import io
import logging

from fastapi import UploadFile

from core.payment.exceptions import (
    LiqpayRroIsNeedFiscalNotSetError,
    LiqpayRroTaxListNotSetError,
)
from core.store.functions.shipment import get_shipments_data
from db import crud
from db.models import Brand
from schemas import (
    CustomShipmentGroupSchema, CustomShipmentSchema, ShipmentSchema,
    ShipmentsData,
)
from schemas.payment_settings.liqpay import (
    Base64EncodedFile, ProductFromLiqPay,
    ProductToLiqpay, TaxList,
)

debugger = logging.getLogger('debugger.liqpay')


async def export_liqpay_products(
        brand_id: int, payment_settings_id: int, store_id: int | None = None
) -> Base64EncodedFile:
    payment_data = await crud.get_payment_data(
        payment_settings_id=payment_settings_id, payment_method='liqpay',
        brand_id=brand_id, store_id=store_id
    )

    is_need_fiscal = payment_data.get('is_need_fiscal')
    if not is_need_fiscal:
        raise LiqpayRroIsNeedFiscalNotSetError()

    tax_code: TaxList | None = payment_data.get('tax_list')
    if not tax_code:
        raise LiqpayRroTaxListNotSetError()

    products = await crud.get_products_for_liqpay(brand_id, store_id)
    shipments = await get_shipments_for_liqpay(brand_id, store_id, tax_code)

    f = io.StringIO()
    fields = "^".join(ProductToLiqpay.__fields__.keys())
    f.write(f"{fields}\n")
    for product in products:
        f.write(
            f"{product.name}^{product.price}^{product.unit_name}^{product.vndcode}^"
            f"{product.codifier or ''}^{product.tax_list or ''}^7loc^"
            f"{product.barcode or ''}^{product.editable_price or ''}^"
            f"{product.weight_product or ''}\n"
        )
    for row in shipments:
        f.write('^'.join(row))
        f.write('\n')

    f.seek(0)
    encoded_data = base64.b64encode(f.read().encode('utf-8')).decode('utf-8')
    f.close()

    debugger.debug(f'Export products to {brand_id}-{tax_code}-liqpay.csv OK')
    return Base64EncodedFile(
        filename=f'{brand_id}-{tax_code}-liqpay.csv', content_type='text/csv',
        data=encoded_data
    )


async def import_liqpay_products(
        payment_settings_id: int, brand_id: int, uploaded_file: UploadFile,
        store_id: int | None = None, object_payment_settings_id: int | None = None,
) -> list[ProductFromLiqPay | None]:
    content = io.StringIO((await uploaded_file.read()).decode('utf-8'))
    csv_reader = csv.DictReader(content)
    return await crud.update_product_to_liqpay(
        payment_settings_id, brand_id, csv_reader, store_id, object_payment_settings_id
    )


async def get_shipments_for_liqpay(
        brand_id: int, 
        store_id: int | None = None, 
        tax_code: TaxList | None = "А",
        ) -> list[
    list[TaxList | str | None]]:

    shipment: ShipmentSchema | CustomShipmentSchema | CustomShipmentGroupSchema

    shipments: ShipmentsData = await get_shipments_data(
        await Brand.get(brand_id), None, 'uk'
    )
    result = []
    is_have_tips = False

    for type_shipment in shipments.__fields__.keys():
        debugger.debug(f'Обробка типу відправлення: {type_shipment}')
        for shipment in getattr(shipments, type_shipment):
            if type_shipment == 'groups' or not isinstance(shipment, ShipmentSchema):
                continue
            debugger.debug(
                f'Перевірка відправлення: {shipment.name}\n'
                f'  - тип: {type_shipment}\n'
                f'  - allow_online_payment: {shipment.allow_online_payment}\n'
                f'  - is_paid_separately: {shipment.is_paid_separately}\n'
                f'  - enabled_tips: {shipment.enabled_tips}'
            )
            if (
                    shipment.allow_online_payment and
                    not shipment.is_paid_separately 
            ):
                price = f'{shipment.prices[0].cost_delivery:.2f}' if shipment.prices and len(shipment.prices) > 0 else "0.01"
                result.append(
                    [shipment.name, price,
                     'Послуга', f'S{shipment.id}', '',
                     tax_code, '7loc', '', 'T', '']
                )
                debugger.debug(f'✅ Відправлення {shipment.name} додано до результату')
                if not is_have_tips and shipment.enabled_tips:
                    is_have_tips = True
            else:
                debugger.debug(f'❌ Відправлення {shipment.name} не додано до результату')

    for group in shipments.groups:
        debugger.debug(f'Обробка групи: {group.name}')
        for shipment in group.shipments:
            debugger.debug(
                f'Перевірка відправлення: {shipment.name}\n'
                f'  - allow_online_payment: {shipment.allow_online_payment}\n'
                f'  - is_paid_separately: {shipment.is_paid_separately}\n'
                f'  - enabled_tips: {shipment.enabled_tips}'
                f'  - is_enabled: {shipment.is_enabled}'
            )
            
            if (
                    shipment.is_enabled and
                    shipment.allow_online_payment and
                    not shipment.is_paid_separately
            ):
                price = f'{shipment.prices[0].cost_delivery:.2f}' if shipment.prices and len(shipment.prices) > 0 else "0.01"
                result.append(
                    [shipment.name, price,
                     'Послуга', f'S{shipment.id}', '',
                     tax_code, '7loc', '', 'T', '']
                )
                debugger.debug(f'✅ Відправлення {shipment.name} додано до результату')
                if not is_have_tips and shipment.enabled_tips:
                    is_have_tips = True
            else:
                debugger.debug(f'❌ Відправлення {shipment.name} не додано до результату')

    if is_have_tips:
        result.append(
            ['Чайові', '0.01', 'Послуга', 'TIPS', '', tax_code, '7loc', '', 'T', '']
        )
        debugger.debug('✅ Додано чайові до результату')

    return result
