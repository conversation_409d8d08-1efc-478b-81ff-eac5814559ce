from typing import Literal

import aiogram as tg
import aiowhatsapp as wa
from aiowhatsapp import types

from core import messangers_adapters
from core.custom_texts import ct
from core.custom_texts.models import MenuInStoreCustomTextsModel
from core.menu_in_store.callback_data import MenuInStoreButtonCallbackData
from core.whatsapp.menu_keyboard_builder import MenuKeyboardBuilder, MenuKeyboardConf
from db.models import Brand, ClientBot, Group, MenuInStore, User
from utils.redefined_classes import InlineBtn, InlineKb
from utils.text import c, f


async def get_wa_menu_in_store_keyboard(
        menu_in_store: MenuInStore,
        bot: ClientBot,
        user: User,
        lang: str,
        ct_obj: MenuInStoreCustomTextsModel | None = None,
) -> types.ListKeyboard:
    if not ct_obj:
        ct_obj = await MenuInStoreCustomTextsModel.from_object(menu_in_store)

    builder = MenuKeyboardBuilder(user, bot, lang)

    group: Group = await Group.get(menu_in_store.group_id)

    callback_data = MenuInStoreButtonCallbackData(menu_in_store_id=menu_in_store.id)

    order_button = await ct(ct_obj, lang, "menu", "order_button")
    if order_button:
        builder.add_sections(
            types.Section(
                title=await ct(ct_obj, lang, "menu", "buttons_group_text"),
                rows=[types.SectionRow(
                    id=callback_data.to_str("wa_qrm_order"),
                    title=order_button,
                    description=await ct(
                        ct_obj, lang, "menu", "order_button_description"
                    )
                )]
            )
        )

    waiter_button = await ct(ct_obj, lang, "waiter", "call_button")
    pay_button = await ct(ct_obj, lang, "payments", "pay_button")

    if waiter_button or pay_button:
        section = types.Section(
            title=await ct(ct_obj, lang, "waiter", "buttons_group_text"),
        )

        if waiter_button:
            section.add_rows(
                types.SectionRow(
                    id=callback_data.to_str("waiter"),
                    title=waiter_button,
                    description=await ct(
                        ct_obj, lang, "waiter", "call_button_description"
                    )
                )
            )

        if pay_button:
            section.add_rows(
                types.SectionRow(
                    id=callback_data.to_str("payment"),
                    title=pay_button,
                    description=await ct(
                        ct_obj, lang, "payments", "pay_button_description"
                    )
                )
            )
        builder.add_sections(section)

    reviews_button = await ct(ct_obj, lang, "contacts", "reviews button")

    chat_button = await ct(ct_obj, lang, "contacts", "chat button")

    if reviews_button or chat_button:
        section = types.Section(
            title=await ct(ct_obj, lang, "contacts", "buttons_group_text")
        )

        if reviews_button:
            section.add_rows(
                types.SectionRow(
                    id=callback_data.to_str("wa_qrm_review"),
                    title=reviews_button,
                    description=await ct(
                        ct_obj, lang, "contacts",
                        "reviews_button_description",
                    )
                )
            )

        if chat_button:
            section.add_rows(
                types.SectionRow(
                    id=c("chat", group_id=group.id, menu_in_store_id=menu_in_store.id),
                    title=chat_button,
                    description=await ct(
                        ct_obj, lang, "contacts", "chat_button_description"
                    )
                )
            )

        builder.add_sections(section)

    name = await menu_in_store.get_calculated_in_store_name()

    no_more_button = await ct(
        ct_obj, lang,
        "active menu",
        "no more button",
        name=name,
        comment=menu_in_store.comment,
    )
    if no_more_button:
        section = types.Section(
            title=await f("wa menu navigation text", lang),
            rows=[types.SectionRow(
                id=callback_data.to_str("qrm_leave"),
                title=no_more_button,
                description=await ct(
                    ct_obj, lang, "active menu", "no more button description"
                )
            )]
        )
        builder.add_sections(section, position="end")

    keyboard = await builder.build(
        conf=MenuKeyboardConf(
            shop=False,
            chat=False,
            profile=False,
        )
    )

    return keyboard


async def get_tg_menu_in_store_keyboard(
        menu: MenuInStore,
        bot: ClientBot,
        lang: str,
) -> InlineKb | wa.types.ReplyKeyboard | wa.types.ListKeyboard | None:
    is_payment_bot_menu = menu.group_id != bot.group_id
    buttons: list[
        messangers_adapters.InlineKeyboardButton | InlineBtn | Literal["row"]] = []

    ct_obj = await MenuInStoreCustomTextsModel.from_object(menu)

    brand = await Brand.get(group_id=menu.group_id)
    if brand and not is_payment_bot_menu:
        bot = await ClientBot.get(group_id=menu.group_id)

        button_type: Literal["order", "delivery pickup"]
        for button_type in ("order", "delivery pickup"):
            button = await get_open_menu_button(
                menu, ct_obj, brand,
                bot, button_type,
                lang
            )
            if button:
                buttons.append(button)

    menu_callback_data = MenuInStoreButtonCallbackData(menu_in_store_id=menu.id)

    buttons.append('row')

    waiter_button = await ct(ct_obj, lang, "waiter", "call button")
    if waiter_button:
        buttons.append(
            messangers_adapters.InlineKeyboardButton(
                text=waiter_button,
                data=menu_callback_data.to_str("waiter"),
            )
        )

    pay_button = await ct(ct_obj, lang, "payments", "pay button")
    if pay_button:
        buttons.append(
            messangers_adapters.InlineKeyboardButton(
                text=pay_button,
                data=menu_callback_data.to_str("payment"),
            )
        )

    buttons.append('row')

    reviews_button = await ct(ct_obj, lang, "contacts", "reviews button")
    if reviews_button:
        from client.review.callback_data import LeaveReviewCallbackData
        callback_data = LeaveReviewCallbackData(
            group_id=menu.group_id,
            menu_in_store_id=menu.id,
            privacy="private",
        )
        buttons.append(
            messangers_adapters.InlineKeyboardButton(
                text=reviews_button,
                data=callback_data.to_str(),
            )
        )

    chat_button = await ct(ct_obj, lang, "contacts", "chat button")
    if chat_button:
        callback_data = c("chat", group_id=menu.group_id, menu_in_store_id=menu.id)
        buttons.append(
            messangers_adapters.InlineKeyboardButton(
                text=chat_button,
                data=callback_data,
            )
        )

    name = await menu.get_calculated_in_store_name()

    no_more_button = await ct(
        ct_obj, lang,
        "active menu",
        "no more button",
        name=name,
        comment=menu.comment,
    )
    if no_more_button:
        buttons.append('row')
        buttons.append(
            messangers_adapters.InlineKeyboardButton(
                text=no_more_button,
                data=menu_callback_data.to_str("qrm_leave")
            )
        )

    match bot.bot_type:
        case "telegram":
            keyboard = InlineKb()

            for button in buttons:
                if button == "row":
                    keyboard.row()
                elif isinstance(button, InlineBtn):
                    keyboard.insert(button)
                elif isinstance(button, messangers_adapters.InlineKeyboardButton):
                    keyboard.insert(button.to_telegram())
        case "whatsapp":
            wa_buttons = [button for button in buttons if
                          isinstance(button, messangers_adapters.InlineKeyboardButton)]
            if len(wa_buttons) <= 3:
                keyboard = messangers_adapters.InlineKeyboard(
                    buttons=wa_buttons,
                ).to_whatsapp()
            else:
                keyboard = wa.types.ListKeyboard(
                    button=await ct(ct_obj, lang, "choose_action_button")
                )
                keyboard.add_rows(
                    *(button.to_whatsapp("section_row") for button in wa_buttons)
                )
        case _:
            keyboard = None

    return keyboard


async def get_open_menu_button(
        menu_in_store: MenuInStore,
        ct_obj: MenuInStoreCustomTextsModel,
        brand: Brand, bot: ClientBot,
        button_type: Literal["order", "delivery pickup"], lang: str,
) -> InlineBtn | messangers_adapters.InlineKeyboardButton | None:

    params = {
        "qrmenu": menu_in_store.id if button_type == "order" else None,
        "bot_id": bot.id,
    }

    if menu_in_store.store_id:
        url = brand.get_url(f"s/{menu_in_store.store_id}/menu", **params)
    else:
        url = brand.get_url("select", **params)

    button_text = await ct(ct_obj, lang, "menu", f"{button_type} button")
    if not button_text:
        return

    match bot.bot_type:
        case "telegram":
            return InlineBtn(
                button_text,
                web_app=tg.types.WebAppInfo(
                    url=url
                ),
            )
        case "whatsapp":
            return messangers_adapters.InlineKeyboardButton(
                text=button_text,
                data=MenuInStoreButtonCallbackData(
                    menu_in_store_id=menu_in_store.id,
                ).to_str(button_type.replace(" ", "_"))
            )


async def get_payments_keyboard(
        menu: MenuInStore, lang: str,
        available_payments: list,
) -> messangers_adapters.InlineKeyboard:
    keyboard = messangers_adapters.InlineKeyboard(row_width=1)

    ct_obj = await MenuInStoreCustomTextsModel.from_object(menu)
    callback_data = MenuInStoreButtonCallbackData(menu_in_store_id=menu.id)

    for button_type in available_payments:
        button_text = await ct(ct_obj, lang, "payments", f"{button_type} button")
        if button_text:
            keyboard.add_buttons(
                messangers_adapters.InlineKeyboardButton(
                    text=button_text,
                    data=callback_data.to_str(button_type),
                )
            )

    return keyboard


async def get_active_menu_open_button_text(
        menu_in_store: MenuInStore, lang: str,
):
    return await ct(
        menu_in_store, lang,
        "active menu", "open button",
        name=await menu_in_store.get_calculated_in_store_name(),
        comment=menu_in_store.comment
    )
