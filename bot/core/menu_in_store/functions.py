import logging
from typing import Any, Literal

import aiogram as tg
import aiowhatsapp as wa
from psutils.state_router import Router

from core import messangers_adapters as ma
from core.custom_texts import ct, ict
from core.custom_texts.models import MenuInStoreCustomTextsModel
from core.invoice.invoice_process.invoice_processor import (
    InvoiceProcessor,
    InvoiceProcessorData,
)
from core.keyboards import get_bot_menu_keyboard
from core.menu_in_store.keyboards import (
    get_payments_keyboard, get_tg_menu_in_store_keyboard, get_wa_menu_in_store_keyboard,
)
from core.text_notification.functions import create_and_send_text_notification
from core.whatsapp.functions import send_whatsapp_menu_message
from db import crud
from db.models import Brand, ClientBot, Group, MenuInStore, User
from schemas import (
    InvoiceTypeEnum, TextNotificationTargetEnum,
    TextNotificationTypeEnum,
)
from utils.text import f


async def send_payment_message(
        answer_obj: ma.AnswerObject,
        user: User,
        bot: ClientBot,
        lang: str,
        payment_type: Literal["cash", "card"],
        menu_in_store: MenuInStore,
):
    profile = await Group.get(menu_in_store.group_id)
    ct_obj = await MenuInStoreCustomTextsModel.from_object(menu_in_store)

    await create_and_send_text_notification(
        TextNotificationTargetEnum.CRM,
        TextNotificationTypeEnum.PAYMENT_CASH
        if payment_type == "cash" else
        TextNotificationTypeEnum.PAYMENT_CARD,
        profile,
        from_user=user,
        from_bot=bot,
        menu_in_store=menu_in_store,
        menu_in_store_ct_obj=ct_obj,
    )

    if answer_obj:
        ct_obj = await MenuInStoreCustomTextsModel.from_object(menu_in_store)
        message_text = await ct(
            ct_obj, lang,
            "payments", "notifications", "sent text",
        )
        if bot.bot_type == "telegram" and answer_obj.from_user.is_bot:
            return await answer_obj.edit_text(message_text)

        if bot.bot_type == "whatsapp":
            keyboard = await get_wa_menu_in_store_keyboard(
                menu_in_store, bot, user, lang, ct_obj
            )
        else:
            keyboard = None
        return await answer_obj.answer(message_text, reply_markup=keyboard)


async def send_menu_in_store_menu(
        message: ma.Message,
        menu_in_store: MenuInStore,
        user: User,
        bot: ClientBot,
        lang: str,
):
    ct_obj = await MenuInStoreCustomTextsModel.from_object(menu_in_store)
    calculated_name = await menu_in_store.get_calculated_in_store_name()

    header_text = await ct(
        ct_obj, lang,
        "header text",
        name=calculated_name,
        comment=menu_in_store.comment,
    )
    if header_text:
        header_text = f"<b>{header_text}</b>\n"

    greeting_text = await ct(
        ct_obj, lang,
        "greeting text",
        name=calculated_name,
        comment=menu_in_store.comment,
    )

    comment_view_text = await ct(
        ct_obj, lang,
        "comment view text",
        name=calculated_name,
        comment=menu_in_store.comment,
    )
    if comment_view_text:
        comment_view_text = f"<i>{comment_view_text}</i>"

    message_text = "\n".join(
        [el for el in (header_text, greeting_text, comment_view_text) if el]
    )

    keyboard = await get_bot_menu_keyboard(user, bot, lang, menu_in_store, False)
    await message.answer(message_text, reply_markup=keyboard)

    message_text = await ct(
        menu_in_store, lang,
        "choose action text",
        name=await menu_in_store.get_calculated_in_store_name(),
        comment=menu_in_store.comment,
    )

    keyboard = await get_tg_menu_in_store_keyboard(menu_in_store, bot, lang)

    messanger_bot = ma.Bot(bot=bot)
    return await messanger_bot.send_message(
        message.from_user.id,
        message_text,
        keyboard,
    )


async def get_menu_in_store_main_buttons_settings(menu_in_store: MenuInStore):
    custom_texts_obj = await MenuInStoreCustomTextsModel.from_object(menu_in_store)

    return {
        "reviews": await ict(custom_texts_obj, "contacts", "reviews button"),
        "chat": await ict(custom_texts_obj, "contacts", "chat button"),
        "waiter": await ict(custom_texts_obj, "waiter", "call button"),
        "order": await ict(custom_texts_obj, "menu", "order button"),
        "delivery_pickup": await ict(
            custom_texts_obj, "menu", "delivery pickup button"
        ),
        "payments": await ict(custom_texts_obj, "payments", "pay_button"),
    }


async def check_is_menu_in_store_one_button(
        obj: MenuInStore | dict,
        button_name: str,
):
    if isinstance(obj, MenuInStore):
        settings = await get_menu_in_store_main_buttons_settings(obj)
    else:
        settings = obj
    statuses = [name for name, value in settings.items() if value]
    return len(statuses) == 1 and statuses[0] == button_name


async def send_menu_in_store(
        message: ma.Message,
        state: ma.FSMContext, lang: str,
        menu_in_store: MenuInStore,
        user: User, bot: ClientBot,
        is_payment_online: bool = False,
):
    try:
        buttons_settings = await get_menu_in_store_main_buttons_settings(menu_in_store)

        if is_payment_online:
            if bot.bot_type == "telegram":
                keyboard = await get_bot_menu_keyboard(user, bot, lang)
                await message.answer(
                    await f(
                        "qr menu payment online from web text", lang,
                        name=await menu_in_store.get_calculated_in_store_name()
                    ), reply_markup=keyboard
                )

            await process_menu_in_store_payment_mode(
                message, state, user, bot, lang, "online", menu_in_store
            )
        elif await check_is_menu_in_store_one_button(buttons_settings, "payments"):
            await send_payment_menu(message, menu_in_store, state, user, lang, bot)
        elif await check_is_menu_in_store_one_button(buttons_settings, "reviews"):
            from client.review.functions import start_make_review
            await start_make_review(
                message, state, lang, menu_in_store.group, menu_in_store
            )
        else:
            await state.update_data(active_menu_in_store_id=menu_in_store.id)
            await send_menu_in_store_menu(message, menu_in_store, user, bot, lang)
            from client.main.functions import set_menu_button
            await set_menu_button(bot, user, lang, menu_in_store.id)
        return True
    except Exception as e:
        logging.error(e, exc_info=True)

        try:
            keyboard = await get_bot_menu_keyboard(user, bot, lang, menu_in_store)
        except Exception as e:
            logging.error(e, exc_info=True)
            keyboard = None
        await message.answer(
            await f("menu in store sending error", lang), reply_markup=keyboard
        )
        return False


async def process_menu_in_store_payment_mode(
        answer_obj: ma.AnswerObject,
        state: ma.FSMContext | None,
        user: User,
        bot: ClientBot,
        lang: str,
        mode: Literal["cash", "card", "online"],
        menu_in_store: MenuInStore,
        is_edit: bool = True,
        brand: Brand | None = None,
):
    if mode == "online":
        bot_type = await ma.detect_bot_type(answer_obj)

        match bot_type:
            case "telegram":
                await state.update_data(
                    is_edit=is_edit,
                    mode=mode,
                    user_id=user.id,
                    group_id=menu_in_store.group_id,
                    menu_in_store_id=menu_in_store.id,
                )

                await InvoiceProcessor.start(
                    state, InvoiceProcessorData(
                        payment_mode=menu_in_store.invoice_payment_mode,
                        invoice_type=InvoiceTypeEnum.MENU_IN_STORE,
                        invoice_template_id=menu_in_store.invoice_template_id,
                        menu_in_store_id=menu_in_store.id,
                    )
                )
                return await Router.state_menu(
                    answer_obj, state, lang, set_state_message=True, mode="new"
                )
            case "whatsapp":
                return await send_whatsapp_menu_in_store_button_link(
                    "payments",
                    "pay online link text",
                    answer_obj, menu_in_store,
                    user, bot, lang, brand,
                    additional_link_kwargs={
                        "action": "payonline"
                    }
                )
            case _:
                raise ValueError(f"invalid bot_type {bot_type}")

    bot = await ClientBot.get_current()
    return await send_payment_message(
        answer_obj, user,
        bot, lang,
        # online mode handled above
        mode,  # type: ignore
        menu_in_store,
    )


async def send_whatsapp_menu_in_store_button_link(
        ct_field_name: str,
        ct_key_parts: tuple[str, ...] | str,
        answer_obj: wa.types.message.AnswerObject,
        menu_in_store: MenuInStore,
        user: User,
        bot: ClientBot,
        lang: str,
        brand: Brand | None = None,
        link_path: str = "qrmenu",
        additional_link_kwargs: dict[str, Any] | None = None
):
    if not brand:
        brand = await Brand.get(group_id=menu_in_store.group_id)

    if isinstance(ct_key_parts, str):
        ct_key_parts = (ct_key_parts,)

    ct_obj = await MenuInStoreCustomTextsModel.from_object(menu_in_store)

    link_kwargs = {
        "menu_in_store_id": menu_in_store.id,
    }
    if additional_link_kwargs:
        link_kwargs.update(additional_link_kwargs)

    qr_menu_link = await brand.get_short_token_url(
        user, bot.id, lang, link_path, **link_kwargs
    )
    text = await ct(
        ct_obj, lang,
        ct_field_name,
        *ct_key_parts,
        link=qr_menu_link,
    )

    keyboard = await get_wa_menu_in_store_keyboard(
        menu_in_store, bot, user, lang, ct_obj
    )
    return await answer_obj.answer(text, keyboard)


async def send_payment_menu(
        answer_obj: ma.AnswerObject | tg.types.CallbackQuery,
        menu_in_store: MenuInStore,
        state: ma.FSMContext,
        user: User, lang: str,
        bot: ClientBot,
):
    if isinstance(answer_obj, ma.AnswerObject):
        callback_query = None
        message = answer_obj
    else:
        callback_query = answer_obj
        message = callback_query.message

    debugger = logging.getLogger("debugger")

    available_payments: list[Literal["cash", "card", "online"]] = []

    payment_methods = []
    brand = await Brand.get(group_id=menu_in_store.group_id)
    if brand:
        payment_methods = await crud.get_payment_methods(brand.id)
        debugger.debug(f'{brand.id=}, {payment_methods=}')

    custom_texts_obj = await MenuInStoreCustomTextsModel.from_object(menu_in_store)

    is_cash_button = await ict(custom_texts_obj, "payments", "cash_button")
    is_card_button = await ict(custom_texts_obj, "payments", "card_button")
    is_online_button = await ict(custom_texts_obj, "payments", "online_button")
    debugger.debug(f'{is_cash_button=}, {is_card_button=}, {is_online_button=}')

    is_online_payment_available = False
    if payment_methods:
        for payment in payment_methods:
            if payment.is_enabled and payment.is_online:
                is_online_payment_available = True
                break
    if is_online_button and menu_in_store.payment_option != "disabled" and \
            is_online_payment_available:
        available_payments.append('online')
    if is_card_button:
        available_payments.append('card')
    if is_cash_button:
        available_payments.append('cash')

    if not available_payments:
        text = await f("not available payment methods text", lang)
        if callback_query:
            return await callback_query.answer(text, show_alert=True)
        else:
            return await message.answer(text)

    if len(available_payments) > 1:
        message_text = await f("menu in store choose payment method header", lang)
        keyboard = await get_payments_keyboard(menu_in_store, lang, available_payments)
        await state.update_data(bot_id=bot.id)

        messanger_bot = ma.Bot(bot=bot)

        user_to = ma.get_user_to(user, bot.bot_type)
        if not user_to:
            raise Exception(f"user_to is None, {user=}, {bot=}")

        msg = await messanger_bot.send_message(
            user_to,
            message_text,
            keyboard=keyboard,
        )

        if bot.bot_type == "whatsapp":
            msg = await send_whatsapp_menu_message(
                answer_obj, user, bot, lang, menu_in_store
            )

        return msg

    await state.update_data(bot_id=bot.id)
    return await process_menu_in_store_payment_mode(
        message, state,
        user, bot, lang,
        available_payments[0], menu_in_store,
    )
