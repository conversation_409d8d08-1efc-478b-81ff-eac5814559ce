import logging
from typing import Literal

import aiogram as tg
import aiowhatsapp as wa

from core import messangers_adapters as ma
from core.custom_texts import ct
from core.custom_texts.models import MenuInStoreCustomTextsModel
from db.models import ClientBot, Group, MenuInStore, User, UserClientBotActivity
from schemas import TextNotificationTargetEnum, TextNotificationTypeEnum
from ..callback_data import MenuInStoreButtonCallbackData
from ..functions import (
    process_menu_in_store_payment_mode, send_payment_menu,
    send_whatsapp_menu_in_store_button_link,
)
from ..keyboards import get_wa_menu_in_store_keyboard
from ...keyboards import get_bot_menu_keyboard
from ...text_notification.functions import create_and_send_text_notification

logger = logging.getLogger('debugger')


@ma.handler.button("list")
async def waiter_button_handler(
        query: tg.types.CallbackQuery | wa.types.ListReplyQuery,
        menu_data: MenuInStoreButtonCallbackData,
        user: User, lang: str,
        bot: ClientBot,
):
    answer_obj = ma.detect_answer_obj(query)

    menu_in_store: MenuInStore

    if not (menu_in_store := await MenuInStore.get(menu_data.menu_in_store_id)):
        raise ValueError(f"MenuInStore not found with id {menu_data.menu_in_store_id}")

    profile = await Group.get(menu_in_store.group_id)
    ct_obj = await MenuInStoreCustomTextsModel.from_object(menu_in_store)

    await create_and_send_text_notification(
        TextNotificationTargetEnum.CRM,
        TextNotificationTypeEnum.WAITER,
        profile,
        from_user=user,
        from_bot=bot,
        menu_in_store=menu_in_store,
        menu_in_store_ct_obj=ct_obj,
    )

    message_text = await ct(
        ct_obj, lang, "waiter", "called text", comment=menu_in_store.comment or ""
    )

    if bot.bot_type == "whatsapp":
        keyboard = await get_wa_menu_in_store_keyboard(
            menu_in_store, bot, user, lang, ct_obj
        )
    else:
        keyboard = None

    return await answer_obj.answer(message_text, reply_markup=keyboard)


@ma.handler.button("list")
async def payment_button_handler(
        query: tg.types.CallbackQuery | wa.types.ListReplyQuery,
        menu_data: MenuInStoreButtonCallbackData,
        state: ma.FSMContext,
        user: User, lang: str,
        bot: ClientBot,
):
    if not (menu := await MenuInStore.get(menu_data.menu_in_store_id)):
        raise ValueError(f"MenuInStore not found with id {menu_data.menu_in_store_id}")

    await send_payment_menu(query, menu, state, user, lang, bot)


@ma.handler.button("reply", "list")
async def payment_with_type_button_handler(
        query: tg.types.CallbackQuery | wa.types.ListReplyQuery,
        state: ma.FSMContext, user: User, lang: str,
        mode: Literal["cash", "card", "online"],
        menu_data: MenuInStoreButtonCallbackData,
        bot: ClientBot,
):
    answer_obj = ma.detect_answer_obj(query)
    menu: MenuInStore = await MenuInStore.get(menu_data.menu_in_store_id)
    await process_menu_in_store_payment_mode(
        answer_obj, state, user, bot, lang, mode, menu
    )


@ma.handler.button("list")
async def no_more_in_store_button_handler(
        query: tg.types.CallbackQuery | wa.types.ListReplyQuery,
        menu_data: MenuInStoreButtonCallbackData,
        state: ma.FSMContext,
        user: User,
        lang: str,
        bot: ClientBot,
        user_bot_activity: UserClientBotActivity,
):
    answer_obj = ma.detect_answer_obj(query)

    menu_in_store: MenuInStore = await MenuInStore.get(menu_data.menu_in_store_id)

    name = await menu_in_store.get_calculated_in_store_name()
    text_kwargs = {
        "name": name,
        "comment": menu_in_store.comment
    }

    await user_bot_activity.update(active_menu_in_store_id=None)
    text = await ct(menu_in_store, lang, "active menu", "left text", **text_kwargs)

    await state.finish()

    keyboard = await get_bot_menu_keyboard(user, bot, lang)
    await answer_obj.answer(text, reply_markup=keyboard)

    if bot.bot_type == "telegram":
        if isinstance(answer_obj, tg.types.Message):
            await answer_obj.delete()

        from client.main.functions import set_menu_button
        await set_menu_button(bot, user, lang)


async def wa_qrm_review_button_handler(
        reply_query: wa.types.ListReplyQuery,
        menu_data: MenuInStoreButtonCallbackData,
        user: User, bot: ClientBot, lang: str,
):
    menu_in_store: MenuInStore = await MenuInStore.get(menu_data.menu_in_store_id)
    return await send_whatsapp_menu_in_store_button_link(
        "contacts", "reviews link text",
        reply_query, menu_in_store, user, bot, lang,
        additional_link_kwargs={
            "action": "review"
        }
    )


async def wa_qrm_order_button_handler(
        reply_query: wa.types.ListReplyQuery,
        menu_data: MenuInStoreButtonCallbackData,
        user: User, bot: ClientBot, lang: str,
):
    menu_in_store: MenuInStore = await MenuInStore.get(menu_data.menu_in_store_id)

    if menu_in_store.store_id:
        link_path = f"s/{menu_in_store.store_id}/menu"
    else:
        link_path = f"select"

    return await send_whatsapp_menu_in_store_button_link(
        "menu", "order link text",
        reply_query, menu_in_store, user, bot, lang,
        link_path=link_path,
    )


def register_menu_in_store_buttons_handlers(dp: tg.Dispatcher | wa.Dispatcher):
    waiter_button_handler.setup(
        dp,
        MenuInStoreButtonCallbackData.get_filter(
            "waiter", return_column_name="menu_data"
        ),
        state="*",
    )
    payment_button_handler.setup(
        dp,
        MenuInStoreButtonCallbackData.get_filter(
            "payment", return_column_name="menu_data"
        ),
        state="*",
    )

    payment_with_type_button_handler.setup(
        dp,
        MenuInStoreButtonCallbackData.get_filter(
            "cash", "card", "online", return_column_name="menu_data"
        ),
        state="*",
    )

    no_more_in_store_button_handler.setup(
        dp,
        MenuInStoreButtonCallbackData.get_filter(
            "qrm_leave", return_column_name="menu_data"
        ),
        state="*",
    )

    if isinstance(dp, wa.Dispatcher):
        dp.register_list_reply_query_handler(
            wa_qrm_review_button_handler,
            MenuInStoreButtonCallbackData.get_filter(
                "wa_qrm_review", return_column_name="menu_data"
            ),
            state="*",
        )

        dp.register_list_reply_query_handler(
            wa_qrm_order_button_handler,
            MenuInStoreButtonCallbackData.get_filter(
                "wa_qrm_order", return_column_name="menu_data"
            ),
            state="*",
        )
