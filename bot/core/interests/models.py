from datetime import datetime, timed<PERSON>ta
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>, Union

from sqlalchemy import Column, Integer, BigInteger, SmallInteger, ForeignKey
from sqlalchemy import String, Text, DateTime, not_
from sqlalchemy.orm import relationship

from functions.sql_funcs.models import connection, Base
from functions.sql_funcs.models import catch_exception
from functions.sql_funcs.models import NestedMutableJson
from functions.sql_funcs.models import User, ClientBot, UserClientBotActivity


class InterestSetting(Base):

    __tablename__ = "interest_settings"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot = relationship(ClientBot, foreign_keys=bot_id)
    frequency_for_user = Column(Integer, default=60*60*24)
    lifetime_post = Column(Integer, default=60*60*2)

    @staticmethod
    @catch_exception()
    def create(bot_id: int) -> "InterestSetting":
        setting = InterestSetting(bot_id=bot_id)
        connection.session.add(setting)
        connection.session.commit()
        return setting

    @staticmethod
    @catch_exception()
    def get(bot_id: int) -> "InterestSetting":
        query = connection.session.query(InterestSetting)
        result = query.filter(InterestSetting.bot_id == bot_id).one()
        if not result:
            result = InterestSetting.create(bot_id)
        return result

    @staticmethod
    @catch_exception()
    def get_bots() -> List[int]:
        query = connection.session.query(InterestSetting)
        result = [setting.bot_id for setting in query.all()]
        return result

    @catch_exception(error_value=False)
    def update(self, field_name: str, value: int) -> bool:
        """Update fields: [
            frequency_for_user,
            live_time_post]
        """
        setattr(self, field_name, value)
        connection.session.commit()
        return True


class InterestPost(Base):

    __tablename__ = "interest_posts"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    text = Column(
        Text(collation="utf8mb4_unicode_ci"),
        default=None, nullable=True
    )
    owner_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    owner = relationship("User", foreign_keys=owner_id)
    created_on = Column(DateTime(timezone=True), default=datetime.utcnow)
    interests = Column(NestedMutableJson)

    @staticmethod
    @catch_exception()
    def create(
        text: str,
        user: "User",
        interests: List[str] = None
    ) -> "InterestPost":
        if interests is None:
            interests = list()

        post = InterestPost(text=text, owner_id=user.id)
        post.interests = [interest for interest in interests]
        connection.session.add(post)
        connection.session.commit()
        return post

    @staticmethod
    @catch_exception()
    def get(
        post_id: Union[Literal["all"], int]
    ) -> Union[List["InterestPost"], "InterestPost"]:
        query = connection.session.query(InterestPost)
        if post_id == "all":
            result = query.all()
        else:
            result = query.filter(InterestPost.id == post_id).one()
        return result

    @catch_exception()
    def get_interests(self) -> List[str]:
        result = [interest for interest in self.interests]
        return result

    @staticmethod
    @catch_exception()
    def get_posts_for_users(bot_id: int) -> List[Tuple[int, "InterestPost"]]:
        settings = InterestSetting.get(bot_id)
        td_lifetime_post = timedelta(seconds=settings.lifetime_post)
        td_frequency_for_user = timedelta(seconds=settings.frequency_for_user)
        time_post_ago = datetime.utcnow() - td_lifetime_post
        time_user_ago = datetime.utcnow() - td_frequency_for_user

        subquery = connection.session.query(InterestStatistic.id)
        subquery = subquery.join(UserClientBotActivity, UserClientBotActivity.bot_id == bot_id)
        subquery = subquery.join(InterestPost, InterestPost.created_on > time_post_ago)

        subquery = subquery.filter(InterestStatistic.bot_id == bot_id)
        subquery = subquery.filter(UserClientBotActivity.is_entered_bot.is_(True))
        subquery = subquery.filter(InterestStatistic.user_id == UserClientBotActivity.user_id)
        subquery = subquery.filter(InterestStatistic.post_id == InterestPost.id)
        subquery = subquery.filter(InterestStatistic.datetime > time_user_ago)

        query = connection.session.query(User.chat_id, InterestPost)
        query = query.join(User)
        query = query.join(UserClientBotActivity, UserClientBotActivity.bot_id == bot_id)

        query = query.filter(InterestPost.created_on > time_post_ago)
        query = query.filter(UserClientBotActivity.is_entered_bot.is_(True))
        query = query.filter(User.id == UserClientBotActivity.user_id)
        query = query.filter(not_(subquery.exists()))

        result = query.all()
        return result if result else []


class InterestStatistic(Base):

    __tablename__ = "interest_statistics"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot = relationship(ClientBot, foreign_keys=bot_id)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user = relationship(User, foreign_keys=user_id)
    post_id = Column(BigInteger, ForeignKey("interest_posts.id", ondelete="SET NULL"), nullable=True)
    post = relationship(InterestPost, foreign_keys=post_id)
    interest = Column(String(256, collation="utf8mb4_unicode_ci"), default="")
    status = Column(SmallInteger, default=0)
    datetime = Column(DateTime(timezone=True), default=datetime.utcnow)

    @staticmethod
    @catch_exception()
    def create(bot_id: int, user_id: int, post_id: int) -> "InterestStatistic":
        result = InterestStatistic(bot_id=bot_id, user_id=user_id, post_id=post_id)
        connection.session.add(result)
        connection.session.commit()
        return result

    @staticmethod
    @catch_exception()
    def get(statistic_id: int) -> "InterestStatistic":
        query = connection.session.query(InterestStatistic)
        result = query.filter(InterestStatistic.id == statistic_id).first()
        return result

    @staticmethod
    @catch_exception()
    def get_user_statistic(
            bot_id: int,
            user_id: int = None,
            post_id: int = None,
            interest: str = None,
            status: int = None,
            date_begin: datetime = None,
            date_end: datetime = None
    ) -> Union[List["InterestStatistic"], "InterestStatistic"]:
        query = connection.session.query(InterestStatistic)
        query = query.filter(InterestStatistic.bot_id == bot_id)
        if user_id:
            query = query.filter(InterestStatistic.user_id == user_id)
        if post_id:
            query = query.filter(InterestStatistic.post_id == post_id)
            return query.one()
        if interest:
            query = query.filter(InterestStatistic.interest == interest)
        if status is not None:
            query = query.filter(InterestStatistic.status == status)
        if date_begin:
            query = query.filter(InterestStatistic.datetime >= date_begin)
        if date_end:
            query = query.filter(InterestStatistic.datetime <= date_end)
        return query.all()

    @catch_exception(error_value=False)
    def update(self, interest: str, status: int) -> bool:
        if interest:
            self.interest = interest
        self.status = status
        connection.session.commit()
        return True
