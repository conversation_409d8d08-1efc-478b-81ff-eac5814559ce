import asyncio

from .api import get_bots_from_settings
from .api import get_setting_frequency
from .api import get_posts_for_users
from .api import send_post_for_user
from .api import update_user_statistic


async def send_interests_posts():
    loop = asyncio.get_event_loop()
    bots_id_list = get_bots_from_settings()
    bot_posts_for_users = dict.fromkeys(bots_id_list)
    for bot_id in bots_id_list:
        bot_posts_for_users[bot_id] = get_posts_for_users(bot_id)

    if not any([any(posts_for_users.values()) for posts_for_users in bot_posts_for_users.values()]):
        timeout = get_setting_frequency()
        return loop.call_later(
            timeout,
            asyncio.create_task,
            send_interests_posts()
        )

    for bot_id in bots_id_list:
        for user_chat_id, post in bot_posts_for_users[bot_id].items():
            await update_user_statistic(bot_id, user_chat_id, post.id)
            await send_post_for_user(bot_id, user_chat_id, post)

    loop.call_soon(asyncio.create_task, send_interests_posts())
