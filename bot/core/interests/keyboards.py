from typing import List

from aiogram.types import InlineKeyboardMarkup as InlineKb
from aiogram.types import InlineKeyboardButton as InlineBtn
from aiogram.types import ReplyKeyboardMarkup as MenuKb
from aiogram.types import KeyboardButton as MenuBtn

from utils.text import c, f


async def get_interests_keyboard(post_id: int, interests: List[str], lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=2)
    buttons = list()
    for interest in interests:
        buttons.append(InlineBtn(interest, callback_data=c("interest", interest_post_id=post_id, name=interest)))
    if 1 < len(interests):
        buttons.append(InlineBtn(
            await f("everything is interesting button", lang),
            callback_data=c("interest_all", interest_post_id=post_id)
        ))
    buttons.append(InlineBtn(await f("not show button", lang), callback_data=c("interest_not_show", interest_post_id=post_id)))
    keyboard.add(*buttons)
    return keyboard


async def get_cancel_keyboard(lang: str) -> MenuKb:
    keyboard = MenuKb(resize_keyboard=True)
    keyboard.insert(MenuBtn(text=await f("cancel", lang)))
    return keyboard
