import schemas
from db import crud


def ad_to_schema(data: crud.AdData):
    units = [
        schemas.AdUnitSchema(
            **unit_data.unit.as_dict(True),
            horizontal_video_url=unit_data.get_video_url("horizontal"),
            vertical_video_url=unit_data.get_video_url("vertical"),
        )
        for unit_data in data.units
    ]
    return schemas.AdSchema(**data.ad.as_dict(True), units=units)
