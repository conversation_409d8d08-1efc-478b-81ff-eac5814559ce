"""
Сервіс для обробки купонів
"""
import logging
import re
from typing import Optional

from fastapi import HTTPException

from core.loyalty import coupon_service, incust
from db.models import User
from schemas import IncustCouponExt
from utils.text import f

logger = logging.getLogger('debugger.loyalty.coupon_processor')


def extract_digits(s: str) -> str:
    """Витягує тільки цифри з рядка"""
    return "".join(re.findall(r"\d", s))


class CouponProcessor:
    """Процесор для роботи з купонами через нові InCust API клієнти"""

    def __init__(
            self, loyalty_settings, user: Optional[User] = None,
            lang: str = "en"
    ):
        self.loyalty_settings = loyalty_settings
        self.user = user
        self.lang = lang

    async def process_coupon(self, coupon_id_or_code: str, action: str = None) -> IncustCouponExt:
        """
        Обробляє купон: додає до гаманця якщо потрібно, викупляє якщо можливо
        """
        coupon_transaction_response = None
        is_in_wallet = False
        message = None

        try:
            # Отримуємо дані купона
            coupon = await coupon_service.get_coupon_data(
                coupon_id_or_code,
                self.user,
                self.loyalty_settings,
                self.lang
            )
            
            if not coupon:
                raise HTTPException(
                    status_code=404,
                    detail=await f("COUPON_NOT_FOUND", self.lang)
                )

            # Без користувача - тільки check-modifier купони дозволені
            if not self.user and coupon.type and coupon.type != "check-modifier":
                raise HTTPException(
                    status_code=400,
                    detail=await f("WEB_CLIENT_COUPON_AUTH_ONLY_TEXT", self.lang)
                )

            # Перевіряємо чи купон заблокований
            if hasattr(coupon, 'locked') and coupon.locked:
                raise HTTPException(
                    status_code=400,
                    detail=await f("WEB_CLIENT_COUPON_LOCKED_TEXT", self.lang)
                )

            if self.user:
                # Перевіряємо чи купон в гаманці
                is_in_wallet = await coupon_service.check_if_in_wallet(
                    coupon_id_or_code,
                    self.user,
                    self.loyalty_settings,
                    self.lang
                )
                
                # Якщо не в гаманці і це код - додаємо
                if not is_in_wallet and len(extract_digits(coupon_id_or_code)) == 12:
                    coupon_id, add_message = await coupon_service.add_to_wallet(
                        coupon_id_or_code,
                        self.user,
                        self.loyalty_settings,
                        self.lang
                    )
                    if coupon_id:
                        is_in_wallet = True
                        message = add_message
                        # Отримуємо оновлені дані купона
                        coupon = await coupon_service.get_coupon_data(
                            coupon_id,
                            self.user,
                            self.loyalty_settings,
                            self.lang
                        )

            # Якщо купон вже використаний - повертаємо повідомлення
            if coupon.status and coupon.status == "redeemed":
                # Для сертифікатів отримуємо транзакцію
                if (coupon.type and coupon.type == "certificate" and
                        hasattr(coupon, 'redeem_result_transaction_id') and 
                        coupon.redeem_result_transaction_id):
                    try:
                        from incust_client_api_client.api.transaction_api import TransactionApi
                        async with incust.client.TransactionApi(
                            self.loyalty_settings, user=self.user, lang=self.lang
                        ) as api:
                            coupon_transaction_response = await api.get_transaction(
                                transaction_id=coupon.redeem_result_transaction_id
                            )
                    except Exception as ex:
                        logger.error(f"Error getting transaction: {ex}")
                        
                # Повертаємо повідомлення про використання або викидуємо помилку
                if not message:
                    message = await f('coupon already used text', self.lang)
                
                # Якщо це спроба застосування - викидуємо помилку
                if action == "apply":
                    raise HTTPException(
                        status_code=400,
                        detail=message
                    )

            # Викупляємо купон якщо потрібно та можливо
            elif (self.user and is_in_wallet and
                  coupon.type and coupon.type != "check-modifier" and
                  not (hasattr(coupon, 'redeem_at_terminal') and coupon.redeem_at_terminal)):

                try:
                    redeem_result = await coupon_service.redeem_coupon(
                        str(coupon.id),
                        self.user,
                        self.loyalty_settings,
                        self.lang
                    )
                    
                    if redeem_result:
                        # Отримуємо оновлені дані купона
                        coupon = await coupon_service.get_coupon_data(
                            str(coupon.id),
                            self.user,
                            self.loyalty_settings,
                            self.lang
                        )
                        
                        # Отримуємо транзакцію якщо є
                        if (hasattr(coupon, 'redeem_result_transaction_id') and
                                coupon.redeem_result_transaction_id):
                            try:
                                from incust_client_api_client.api.transaction_api import TransactionApi
                                async with incust.client.TransactionApi(
                                    self.loyalty_settings, user=self.user, lang=self.lang
                                ) as api:
                                    coupon_transaction_response = await api.get_transaction(
                                        transaction_id=coupon.redeem_result_transaction_id
                                    )
                            except Exception as ex:
                                logger.error(f"Error getting transaction after redeem: {ex}")
                                
                except Exception as e:
                    logger.warning(f"Failed to redeem coupon {coupon.id}: {e}")
                    # Витягуємо повідомлення з JSON API відповіді і викидуємо HTTPException
                    error_message = str(e)
                    if hasattr(e, 'body') and e.body:
                        try:
                            import json
                            error_data = json.loads(e.body)
                            if 'message' in error_data:
                                error_message = error_data['message']
                        except:
                            pass
                    
                    raise HTTPException(
                        status_code=400,
                        detail=error_message
                    )

            return IncustCouponExt(
                coupon=coupon,
                is_in_wallet=is_in_wallet,
                message=message,
                transaction=coupon_transaction_response,
            )

        except Exception as e:
            logger.error(
                f"Error processing coupon {coupon_id_or_code}: {e}", exc_info=True
            )
            raise

    async def check_coupon(self, coupon_id_or_code: str) -> IncustCouponExt:
        """
        Перевіряє купон без додавання до гаманця та викупу
        """
        coupon_transaction_response = None
        is_in_wallet = False
        message = None

        try:
            # Отримуємо дані купона
            coupon = await coupon_service.get_coupon_data(
                coupon_id_or_code,
                self.user,
                self.loyalty_settings,
                self.lang
            )

            if coupon and self.user:
                # Перевіряємо чи купон в гаманці
                is_in_wallet = await coupon_service.check_if_in_wallet(
                    coupon_id_or_code,
                    self.user,
                    self.loyalty_settings,
                    self.lang
                )

            # Якщо купон використаний, отримуємо транзакцію
            if (coupon and coupon.status and coupon.status == "redeemed" and
                    hasattr(coupon, 'redeem_result_transaction_id') and 
                    coupon.redeem_result_transaction_id):

                try:
                    from incust_client_api_client.api.transaction_api import TransactionApi
                    async with incust.client.TransactionApi(
                        self.loyalty_settings, user=self.user, lang=self.lang
                    ) as api:
                        coupon_transaction_response = await api.get_transaction(
                            transaction_id=coupon.redeem_result_transaction_id
                        )
                except Exception as ex:
                    logger.error(f"Error getting transaction: {ex}")

            return IncustCouponExt(
                coupon=coupon,
                is_in_wallet=is_in_wallet,
                message=message,
                transaction=coupon_transaction_response,
            )

        except Exception as e:
            logger.error(
                f"Error checking coupon {coupon_id_or_code}: {e}", exc_info=True
            )
            raise

