from typing import Any, Optional, Type, TypedDict

import incust_api
import incust_api.api
from incust_api.types import TClient
from psutils.func import check_function_spec

from config import DEFAULT_LANG
from db.models import LoyaltySettings, User
from schemas.loyalty_settings import LoyaltySettingsSchema
from .logging_rest_client import (
    LoggingTerminalRESTClient,
    LoggingClientRESTClient,
    LoggingUserRESTClient
)


class ParamsType(TypedDict):
    lang: Optional[str]
    user: Optional[User]
    user_token: Optional[str]


class IncustAPI(incust_api.BaseIncustAPI[LoyaltySettings | LoyaltySettingsSchema, ParamsType]):
    _client_configs = {}
    _terminal_configs = {}
    _user_configs = {}

    @staticmethod
    def _get_config_key(settings: LoyaltySettings | LoyaltySettingsSchema, client_type: str) -> str:
        """Генерує унікальний ключ для кешування конфігурації."""
        # Включаємо server_url в ключ щоб уникнути кешування між різними серверами
        server_key = settings.server_url.replace("://", "_").replace(".", "_").replace("/", "_")
        return (f"{client_type}_{settings.brand_id}_"
                f"{settings.white_label_id or 'default'}_{server_key}")

    @classmethod
    def _get_client_config(
            cls,
            settings: LoyaltySettings | LoyaltySettingsSchema,
            lang: str = DEFAULT_LANG
    ) -> incust_api.api.client.Configuration:
        """Отримує або створює конфігурацію для Client API."""
        config_key = cls._get_config_key(settings, "client")

        if config_key not in cls._client_configs:
            api_path = "/v1/wlclient" if settings.white_label_id else "/v1/client"
            config = incust_api.api.client.Configuration(
                host=settings.server_url + api_path
            )
            if not hasattr(config, 'api_key_prefix') or config.api_key_prefix is None:
                config.api_key_prefix = {}
            config.api_key_prefix['Accept-Language'] = lang
            cls._client_configs[config_key] = config
        return cls._client_configs[config_key]

    @classmethod
    def _get_term_config(
            cls,
            settings: LoyaltySettings | LoyaltySettingsSchema,
    ) -> incust_api.api.term.Configuration:
        """Отримує або створює конфігурацію для Terminal API."""
        config_key = cls._get_config_key(settings, "terminal")

        if config_key not in cls._terminal_configs:
            config = incust_api.api.term.Configuration(
                host=settings.server_url + "/v1/term"
            )
            cls._terminal_configs[config_key] = config
        return cls._terminal_configs[config_key]

    @classmethod
    def _get_user_config(
            cls,
            settings: LoyaltySettings | LoyaltySettingsSchema, lang: str = DEFAULT_LANG
    ) -> incust_api.api.user.Configuration:
        """Отримує або створює конфігурацію для User API."""
        config_key = cls._get_config_key(settings, "user")

        if config_key not in cls._user_configs:
            # User API завжди використовує /v1/user (без white label варіанту)
            config = incust_api.api.user.Configuration(
                host=settings.server_url + "/v1/user"
            )
            if not hasattr(config, 'api_key_prefix') or config.api_key_prefix is None:
                config.api_key_prefix = {}
            config.api_key_prefix['Accept-Language'] = lang
            cls._user_configs[config_key] = config
        return cls._user_configs[config_key]

    async def _create_client_with_logging(
            self, 
            client_cls: Type[TClient], 
            config: Any,
            api_type: str
    ) -> TClient:
        """Створює клієнта з логуючим REST client'ом"""
        client = client_cls(configuration=config)
        
        # Закриваємо оригінальний REST client щоб уникнути витоку сесій
        # Він створюється автоматично в конструкторі ApiClient
        if hasattr(client, 'rest_client') and hasattr(client.rest_client, 'close'):
            # Закриваємо оригінальний REST client перед заміною
            await client.rest_client.close()
        
        # Замінюємо стандартний REST client на логуючий
        if api_type == "term" and LoggingTerminalRESTClient:
            client.rest_client = LoggingTerminalRESTClient(config)
        elif api_type == "client" and LoggingClientRESTClient:
            client.rest_client = LoggingClientRESTClient(config)
        elif api_type == "user" and LoggingUserRESTClient:
            client.rest_client = LoggingUserRESTClient(config)
        
        return client

    async def setup_api(
            self,
            api_type: str,
            client_cls: Type[TClient],
            settings: LoyaltySettings | LoyaltySettingsSchema,
            params: ParamsType,
    ) -> TClient:
        lang: str | None = params.get("lang", None)

        config_kwargs: dict[str, Any] = {"settings": settings}
        if lang is not None:
            config_kwargs["lang"] = lang

        get_config = getattr(self, f"_get_{api_type}_config")
        config_kwargs = check_function_spec(
            get_config, {"settings": settings, **params}
        )
        config = get_config(**config_kwargs)
        client = await self._create_client_with_logging(client_cls, config, api_type)

        if lang is None:
            lang = DEFAULT_LANG

        match api_type:
            case "client":
                user: User | None = params.get("user", None)
                user_token: str | None = params.get("user_token", None)

                if user:
                    from .customer_service import get_or_create_incust_customer
                    incust_customer = await get_or_create_incust_customer(
                        user, settings, lang
                    )
                    if not incust_customer or not incust_customer.token:
                        raise ValueError(
                            f"Could not get or create InCust customer for user "
                            f"{user.id}"
                        )
                    user_token = incust_customer.token

                # Додаємо токен авторизації, якщо є
                if user_token:
                    client.set_default_header("Authorization", f"Bearer {user_token}")
            case "term":
                # Додаємо токен авторизації
                client.set_default_header(
                    "Authorization", f"Bearer {settings.terminal_api_key}"
                )
            case "user":
                user: User | None = params.get("user", None)
                user_token: str | None = params.get("user_token", None)

                if user:
                    from .customer_service import get_or_create_incust_customer
                    incust_customer = await get_or_create_incust_customer(
                        user, settings, lang
                    )
                    if not incust_customer or not incust_customer.token:
                        raise ValueError(
                            f"Could not get or create InCust customer for user "
                            f"{user.id}"
                        )
                    user_token = incust_customer.token

                # Додаємо токен авторизації, якщо є
                if user_token:
                    client.set_default_header("Authorization", f"Bearer {user_token}")

        # Додаємо white_label_id, якщо є
        if settings.white_label_id:
            client.set_default_header(
                "X-Application-Id", settings.white_label_id
            )

        # Додаємо мову
        client.set_default_header("Accept-Language", lang)

        # Додаємо обов'язковий заголовок X-Application-Type
        client.set_default_header("X-Application-Type", "chatbot")

        return client


incust = IncustAPI()
