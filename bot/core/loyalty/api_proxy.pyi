from typing import Generic, TypeVar

TSettings = TypeVar("TSettings")
TAPIModule = TypeVar("TAPIModule")


class APIModuleProxy(Generic[TSettings, TAPIModule]):
    def __call__(
            self,
            settings: TSettings,
            **kwargs,
    ) -> APIContextManager[TAPIModule]: ...


class APIContextManager(Generic[TAPIModule]):
    async def __aenter__(self) -> TAPIModule: ...

    async def __aexit__(self, exc_type, exc_val, exc_tb): ...
