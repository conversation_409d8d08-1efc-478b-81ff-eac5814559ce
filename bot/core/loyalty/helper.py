"""
This module provides helper functions for loyalty program logic
using the new InCust API clients.
"""
import re
from html import unescape
from typing import Optional, <PERSON><PERSON>

from incust_api.api import term

import schemas
from core.loyalty.incust_api import incust
from db import crud
from db.models import Invoice, LoyaltySettings, User
from loggers import JSONLogger
from utils.numbers import format_currency, format_sum
from utils.text import fd


async def count_max_redeem_sum_and_percent(
    user: User,
    loyalty_settings: LoyaltySettings,
    user_sum: float | None = None,
    currency: str | None = None,
    lang: str | None = "en",
    discount: Optional[float] = None,
    max_bonuses_percent: Optional[float] = None,
) -> Tuple[float, float, float]:
    """
    Calculates the maximum possible amount of bonuses to redeem.

    :param user: The user object.
    :param loyalty_settings: The loyalty settings.
    :param card_info: The user's card info from InCust API.
    :param user_sum: The total sum of the purchase.
    :param currency: The currency of the purchase.
    :param lang: The language for localization.
    :param discount: An optional discount to apply before calculations.
    :param max_bonuses_percent: An optional limit on the percentage of the sum
                                that can be paid with bonuses.
    :return: A tuple containing:
             - max_bonuses_to_use (float): The max sum of bonuses to apply.
             - percent_of_bonuses_value (float): The percentage of the sum
                                                 covered by bonuses.
             - bonuses_amount (float): The total available bonuses for the user.
    """
    logger = JSONLogger(
        "loyalty.helper",
        {"user_id": user.id, "loyalty_settings_id": loyalty_settings.id},
    )

    if not user:
        return 0, 0, 0

    if not max_bonuses_percent:
        max_bonuses_percent = 0
    max_bonuses_to_use, percent_of_bonuses_value, bonuses_amount = 0, 0, 0

    if discount:
        user_sum -= discount

    try:
        async with incust.term.CustomerApi(loyalty_settings, lang=lang) as api:
            card_info = await api.cardinfo(
                user.incust_external_id,
                term.m.IdType("external-id"),
            )
        async with incust.term.SettingsApi(loyalty_settings, lang=lang) as api:
            terminal_settings = await api.settings()

        if terminal_settings and terminal_settings.id:
            max_bonuses_percent = min(
                terminal_settings.loyalty_settings.bonus_payment_limit,
                max_bonuses_percent

            )

        if card_info and card_info.bonuses:
            for bonuses_item in card_info.bonuses:
                if bonuses_item and bonuses_item.currency == currency:
                    if bonuses_item.bonuses_amount:
                        bonuses_amount = bonuses_item.bonuses_amount

        if max_bonuses_percent:
            max_bonuses_to_use = min(
                user_sum * (max_bonuses_percent / 100), bonuses_amount
            )
            percent_of_bonuses_value = (max_bonuses_to_use / user_sum) * 100
    except term.ApiException as ex:
        logger.error(
            f"count_max_redeem_sum_and_percent: {repr(ex)}",
            {"user_sum": user_sum, "discount": discount}
        )

        return max_bonuses_to_use, percent_of_bonuses_value, bonuses_amount

    return max_bonuses_to_use, percent_of_bonuses_value, bonuses_amount

async def make_loyalty_awards_text(
    processed_check: term.m.Check,
    loyalty_settings: LoyaltySettings,
    currency: str,
    lang: str,
    is_paid: bool = False,
) -> str:
    """
    Generates a human-readable text describing the loyalty awards for an invoice.

    :param processed_check: The processed check data from InCust API.
    :param loyalty_settings: The loyalty settings.
    :param currency: The currency of the transaction.
    :param lang: The language for localization.
    :param is_paid: A flag to change the wording (e.g., "issued" vs. "to be issued").
    :return: A formatted string with loyalty awards information.
    """
    texts = await fd(
        {
            "issued_per_order_text": "check loyalty issued per order text",
            "to_be_issued_per_payment_text": "check loyalty to be issued per payment text",
            "bonuses_text": "check loyalty bonuses text",
            "customer_accounts_text": "check loyalty customer accounts text",
            "customer_accounts_to_be_replenished_text": "check loyalty customer accounts to be replenished text",
            "vouchers_text": "check loyalty vouchers text",
        },
        lang,
    )

    lines = []

    # Special accounts
    if processed_check.special_accounts_charges:
        line_text = (
            texts["customer_accounts_text"]
            if is_paid
            else texts["customer_accounts_to_be_replenished_text"]
        )
        lines.append(f"<b>{line_text}:</b>")

        async with incust.term.BusinessApi(loyalty_settings, lang=lang) as api:
            special_accounts = await api.special_accounts()

        for charge in processed_check.special_accounts_charges:
            amount_str = format_currency(
                charge.get("amount", 0),
                charge.get("currency"),
                locale=lang
            )
            title = "Special Account"
            for sp in special_accounts:
                if sp.id == charge.id:
                    title = sp.title
                    break
            lines.append(f"{title}: {amount_str}")
        lines.append("")  # Add extra new line

    # Bonuses and Coupons
    if processed_check.bonuses_added_amount or processed_check.emitted_coupons:
        line_text = (
            texts["issued_per_order_text"]
            if is_paid
            else texts["to_be_issued_per_payment_text"]
        )
        lines.append(f"<b>{line_text}</b>")

        if processed_check.bonuses_added_amount:
            lines.append(
                " ".join(
                    (
                        texts["bonuses_text"],
                        format_sum(
                            processed_check.bonuses_added_amount,
                            lang,
                            currency=currency,
                        ),
                    )
                )
            )
        if processed_check.emitted_coupons:
            lines.append(
                " ".join(
                    (texts["vouchers_text"], str(len(processed_check.emitted_coupons)))
                )
            )

    return "\n".join(lines)


def filter_none_values(data):
    """
    Рекурсивно видаляє всі None значення з dict, list та вкладених структур.
    """
    if isinstance(data, dict):
        return {k: filter_none_values(v) for k, v in data.items() if v is not None}
    elif isinstance(data, list):
        return [filter_none_values(item) for item in data if item is not None]
    else:
        return data


def strip_html_tags_for_telegram(input_string):
    # Список тегів, які підтримує Telegram
    telegram_supported_tags = r'b|strong|i|em|u|ins|s|strike|del|code|pre'

    # Замінюємо <br>, <br/>, </p><p> на \n
    input_string = re.sub(r'<br\s*/?>|</p>\s*<p>', '\n', input_string)

    # Видаляємо всі теги, крім підтримуваних Telegram, зберігаючи їх вміст
    pattern = fr'<(?!/?({telegram_supported_tags})\b)[^\
    >]+>|</(?!({telegram_supported_tags})\b)[^>]+>'
    cleaned_string = re.sub(pattern, '', input_string)

    # Видаляємо зайві пробіли, зберігаючи переноси рядків
    cleaned_string = re.sub(
        r'[ \t]+', ' ', cleaned_string
    )  # Заміна множинних пробілів та табуляцій на один пробіл
    cleaned_string = re.sub(
        r' *\n *', '\n', cleaned_string
    )  # Видалення пробілів навколо переносів рядків
    cleaned_string = re.sub(
        r'\n{3,}', '\n\n', cleaned_string
    )  # Обмеження послідовних переносів рядків до максимум двох

    # Видаляємо порожні теги
    cleaned_string = re.sub(r'<([^>]+)>\s*</\1>', '', cleaned_string)

    # Розкодовуємо HTML сутності
    cleaned_string = unescape(cleaned_string)

    return cleaned_string.strip()


async def get_loyalty_settings_by_context(
    invoice: Invoice = None,
    brand_id: int = None,
    store_id: int = None, 
    ewallet_id: int = None,
    profile_id: int = None,
    product_id: int = None,
    invoice_template_id: int = None,
    terminal_api_key: str = None,
) -> LoyaltySettings | None:
    """Отримує налаштування лояльності з урахуванням контексту"""
    
    # 1. Якщо invoice вже має loyalty_settings_id - отримуємо напряму
    if invoice and invoice.loyalty_settings_id:
        return await LoyaltySettings.get(invoice.loyalty_settings_id)
    
    # 2. Визначаємо контекст за пріоритетом
    if product_id:
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "product",
            schemas.LoyaltySettingsData(product_id=product_id)
        )
    elif store_id:
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "store", 
            schemas.LoyaltySettingsData(store_id=store_id)
        )
    elif invoice_template_id:
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "invoice_template",
            schemas.LoyaltySettingsData(invoice_template_id=invoice_template_id)
        )
    elif ewallet_id:
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "ewallet",
            schemas.LoyaltySettingsData(ewallet_id=ewallet_id)
        )
    elif profile_id:  # profile замість brand
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "profile",
            schemas.LoyaltySettingsData(profile_id=profile_id)
        )
    else:
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(brand_id=brand_id)
        )
    
    # 3. Якщо треба замінити terminal_api_key
    if loyalty_settings and terminal_api_key and loyalty_settings.terminal_api_key != terminal_api_key:
        loyalty_settings.terminal_api_key = terminal_api_key
    
    return loyalty_settings
