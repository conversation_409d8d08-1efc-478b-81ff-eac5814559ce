from .api_proxy import APIModuleProxy
from incust_api.api import client, term, user
from db.models import LoyaltySettings


class ClientApi:
    BeaconApi: APIModuleProxy[LoyaltySettings, client.api.BeaconApi]
    CardInfoApi: APIModuleProxy[LoyaltySettings, client.api.CardInfoApi]
    CardsApi: APIModuleProxy[LoyaltySettings, client.api.CardsApi]
    ChatbotApi: APIModuleProxy[LoyaltySettings, client.api.ChatbotApi]
    CheckApi: APIModuleProxy[LoyaltySettings, client.api.CheckApi]
    CouponApi: APIModuleProxy[LoyaltySettings, client.api.CouponApi]
    CustomerFeedbackApi: APIModuleProxy[LoyaltySettings, client.api.CustomerFeedbackApi]
    EquipmentApi: APIModuleProxy[LoyaltySettings, client.api.EquipmentApi]
    ExternalFormsApi: APIModuleProxy[LoyaltySettings, client.api.ExternalFormsApi]
    FavoriteApi: APIModuleProxy[LoyaltySettings, client.api.FavoriteApi]
    GiftCardApi: APIModuleProxy[LoyaltySettings, client.api.GiftCardApi]
    GoodsApi: APIModuleProxy[LoyaltySettings, client.api.GoodsApi]
    JobsApi: APIModuleProxy[LoyaltySettings, client.api.JobsApi]
    LoyaltyApi: APIModuleProxy[LoyaltySettings, client.api.LoyaltyApi]
    MessageApi: APIModuleProxy[LoyaltySettings, client.api.MessageApi]
    NewsApi: APIModuleProxy[LoyaltySettings, client.api.NewsApi]
    OnlineStoreApi: APIModuleProxy[LoyaltySettings, client.api.OnlineStoreApi]
    POSApi: APIModuleProxy[LoyaltySettings, client.api.POSApi]
    PaymentApi: APIModuleProxy[LoyaltySettings, client.api.PaymentApi]
    PkpassApi: APIModuleProxy[LoyaltySettings, client.api.PkpassApi]
    PurchaseOrderApi: APIModuleProxy[LoyaltySettings, client.api.PurchaseOrderApi]
    PushApi: APIModuleProxy[LoyaltySettings, client.api.PushApi]
    QrCodeApi: APIModuleProxy[LoyaltySettings, client.api.QrCodeApi]
    SystemApi: APIModuleProxy[LoyaltySettings, client.api.SystemApi]
    TouristCardsApi: APIModuleProxy[LoyaltySettings, client.api.TouristCardsApi]
    TransactionApi: APIModuleProxy[LoyaltySettings, client.api.TransactionApi]
    WalletApi: APIModuleProxy[LoyaltySettings, client.api.WalletApi]


class TermApi:
    AuthorizationApi: APIModuleProxy[LoyaltySettings, term.api.AuthorizationApi]
    BusinessApi: APIModuleProxy[LoyaltySettings, term.api.BusinessApi]
    CheckTransactionsApi: APIModuleProxy[LoyaltySettings, term.api.CheckTransactionsApi]
    CouponsApi: APIModuleProxy[LoyaltySettings, term.api.CouponsApi]
    CustomerApi: APIModuleProxy[LoyaltySettings, term.api.CustomerApi]
    CustomerBenefitsApi: APIModuleProxy[LoyaltySettings, term.api.CustomerBenefitsApi]
    DisplayApi: APIModuleProxy[LoyaltySettings, term.api.DisplayApi]
    EquipmentApi: APIModuleProxy[LoyaltySettings, term.api.EquipmentApi]
    EventsStatusApi: APIModuleProxy[LoyaltySettings, term.api.EventsStatusApi]
    ExternalFormsApi: APIModuleProxy[LoyaltySettings, term.api.ExternalFormsApi]
    FuelApi: APIModuleProxy[LoyaltySettings, term.api.FuelApi]
    GeneralInfoApi: APIModuleProxy[LoyaltySettings, term.api.GeneralInfoApi]
    GiftCardApi: APIModuleProxy[LoyaltySettings, term.api.GiftCardApi]
    GoodsApi: APIModuleProxy[LoyaltySettings, term.api.GoodsApi]
    JobsApi: APIModuleProxy[LoyaltySettings, term.api.JobsApi]
    LoyaltyGoodsApi: APIModuleProxy[LoyaltySettings, term.api.LoyaltyGoodsApi]
    OnlineStoreApi: APIModuleProxy[LoyaltySettings, term.api.OnlineStoreApi]
    PaymentApi: APIModuleProxy[LoyaltySettings, term.api.PaymentApi]
    PosterApi: APIModuleProxy[LoyaltySettings, term.api.PosterApi]
    PricesApi: APIModuleProxy[LoyaltySettings, term.api.PricesApi]
    RouteApi: APIModuleProxy[LoyaltySettings, term.api.RouteApi]
    SettingsApi: APIModuleProxy[LoyaltySettings, term.api.SettingsApi]
    StaffApi: APIModuleProxy[LoyaltySettings, term.api.StaffApi]
    TanksApi: APIModuleProxy[LoyaltySettings, term.api.TanksApi]
    TemporaryCodeApi: APIModuleProxy[LoyaltySettings, term.api.TemporaryCodeApi]
    TouristCardsApi: APIModuleProxy[LoyaltySettings, term.api.TouristCardsApi]
    DefaultApi: APIModuleProxy[LoyaltySettings, term.api.DefaultApi]


class UserApi:
    AuthApi: APIModuleProxy[LoyaltySettings, user.api.AuthApi]
    AuthIdentifiersApi: APIModuleProxy[LoyaltySettings, user.api.AuthIdentifiersApi]
    QrCodeApi: APIModuleProxy[LoyaltySettings, user.api.QrCodeApi]
    ShippingAndDeliveryApi: APIModuleProxy[LoyaltySettings, user.api.ShippingAndDeliveryApi]
    UserApi: APIModuleProxy[LoyaltySettings, user.api.UserApi]
    UsersVehiclesApi: APIModuleProxy[LoyaltySettings, user.api.UsersVehiclesApi]


