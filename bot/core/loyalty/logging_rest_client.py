"""
Логування REST клієнт для InCust API
Додає детальне логування всіх HTTP запитів та відповідей
"""
import json
import logging
from typing import Any

from config import DEBUG
from loggers import J<PERSON>NLogger
from utils.datetime_utils import convert_datetime_to_str

# Імпортуємо базові REST клієнти з кожного API пакету
try:
    from incust_terminal_api_client.rest import RESTClientObject as TerminalRESTClient
    from incust_terminal_api_client.exceptions import ApiException as TerminalApiException
except ImportError:
    TerminalRESTClient = None
    TerminalApiException = None

try:
    from incust_client_api_client.rest import RESTClientObject as ClientRESTClient
    from incust_client_api_client.exceptions import ApiException as ClientApiException
except ImportError:
    ClientRESTClient = None
    ClientApiException = None

try:
    from incust_user_api_client.rest import RESTClientObject as UserRESTClient
    from incust_user_api_client.exceptions import ApiException as UserApiException
except ImportError:
    UserRESTClient = None
    UserApiException = None


class LoggingRESTClientMixin:
    """
    Mixin для додавання логування до REST клієнтів InCust API
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = logging.getLogger('debugger.incust.api')
    
    async def close(self):
        """Закриваємо базовий REST client та його session"""
        try:
            await super().close()
        except Exception as e:
            self.logger.error(f"Error closing REST client: {e}")

    def _sanitize_headers_for_logging(self, headers: dict[str, Any] | None) -> dict[str, Any]:
        """Повертає заголовки без змін для повного діагностування"""
        if not headers:
            return {}
        
        # Повертаємо всі заголовки включаючи повні токени для діагностики
        return dict(headers)

    def _prepare_body_for_logging(self, body: Any) -> Any:
        """Підготовка тіла запиту для логування"""
        if body is None:
            return None
        
        if isinstance(body, (str, bytes)):
            try:
                # Спроба розпарсити JSON
                if isinstance(body, bytes):
                    body = body.decode('utf-8')
                parsed = json.loads(body)
                # Конвертуємо дати для JSON серіалізації та забезпечуємо коректне Unicode кодування
                converted = convert_datetime_to_str(parsed)
                return json.loads(json.dumps(converted, ensure_ascii=False))
            except (json.JSONDecodeError, UnicodeDecodeError):
                return str(body)[:1000]  # Обмежуємо розмір для логування
        
        # Конвертуємо дати для JSON серіалізації та забезпечуємо коректне Unicode кодування
        converted = convert_datetime_to_str(body)
        return json.loads(json.dumps(converted, ensure_ascii=False))

    def _log_request(self, method: str, url: str, headers: dict | None, body: Any, query_params: dict | None):
        """Логування HTTP запиту"""
        try:
            logger = JSONLogger(
                "incust.api.request",
                {
                    "method": method,
                    "url": url,
                    "headers": self._sanitize_headers_for_logging(headers),
                    "query_params": query_params or {},
                    "body": self._prepare_body_for_logging(body),
                    "request_type": "incust_api"
                }
            )
            logger.info(f"{method} {url}")
        except Exception as e:
            # Не ламаємо основний запит через проблеми з логуванням
            self.logger.error(f"Failed to log request: {e}")

    def _log_response(self, method: str, url: str, response: Any = None, error: Exception | None = None, request_headers: dict | None = None, request_body: Any = None):
        """Логування HTTP відповіді - успішної або помилкової"""
        try:
            response_data = {
                "method": method,
                "url": url,
                "request_type": "incust_api"
            }
            
            if error:
                response_data["error_type"] = error.__class__.__name__

                if request_headers:
                    response_data["request_headers"] = self._sanitize_headers_for_logging(request_headers)
                
                if request_body is not None:
                    response_data["request_body"] = self._prepare_body_for_logging(request_body)
                
                # Додаємо деталі з ApiException
                if hasattr(error, 'status'):
                    response_data["status_code"] = error.status
                if hasattr(error, 'reason'):
                    response_data["reason"] = error.reason
                
                # Обробляємо тіло помилки
                if hasattr(error, 'body'):
                    try:
                        # Декодуємо та парсимо JSON
                        if isinstance(error.body, bytes):
                            body_str = error.body.decode('utf-8')
                        elif isinstance(error.body, str):
                            body_str = error.body
                        else:
                            body_str = str(error.body)
                        
                        # Парсимо JSON і зберігаємо з правильним кодуванням
                        response_data["error_body"] = json.loads(body_str)
                        
                        # Додаємо коротке повідомлення для швидкого читання
                        if isinstance(response_data["error_body"], dict) and "message" in response_data["error_body"]:
                            response_data["error_message"] = response_data["error_body"]["message"]
                            
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        # Якщо не вдалось розпарсити JSON, зберігаємо як є
                        response_data["error_body_raw"] = str(error.body)[:1000]
                
                # Не включаємо повний str(error) щоб уникнути дублювання та закодованих символів
                if hasattr(error, 'message'):
                    response_data["error_detail"] = error.message
                elif not response_data.get("error_message"):
                    # Тільки якщо немає іншого повідомлення
                    error_str = str(error)
                    # Прибираємо закодовані байти з повідомлення
                    if '\\x' not in error_str:
                        response_data["error_detail"] = error_str[:500]
                        
                logger = JSONLogger("incust.api.response.error", response_data)
                logger.error(f"{method} {url}")
            else:
                # Логування успішної відповіді
                if hasattr(response, 'status'):
                    response_data["status_code"] = response.status
                if hasattr(response, 'reason'):
                    response_data["reason"] = response.reason
                
                # В DEBUG режимі додаємо тіло відповіді
                if hasattr(response, 'data'):
                    try:
                        # Парсимо JSON відповідь
                        if isinstance(response.data, bytes):
                            data_str = response.data.decode('utf-8')
                            response_data["response_body"] = json.loads(data_str)
                        elif isinstance(response.data, str):
                            response_data["response_body"] = json.loads(response.data)
                        else:
                            response_data["response_body"] = response.data
                        
                        # Обмежуємо розмір для великих відповідей
                        if isinstance(response_data["response_body"], (dict, list)):
                            body_str = json.dumps(response_data["response_body"], ensure_ascii=False)
                            if len(body_str) > 5000:  # Обмежуємо до 5KB
                                response_data["response_body"] = {
                                    "_truncated": True,
                                    "_size": len(body_str),
                                    "_preview": body_str[:1000]
                                }
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        response_data["response_body_raw"] = str(response.data)[:1000]
                
                logger = JSONLogger("incust.api.response.success", response_data)
                logger.debug(f"{method} {url}")  # Використовуємо debug рівень для успішних відповідей
                
        except Exception as e:
            # Не ламаємо основний запит через проблеми з логуванням
            self.logger.error(f"Failed to log response: {e}")
    
    def _log_response_success(self, method: str, url: str, response: Any, headers: dict | None = None, body: Any = None):
        """Wrapper для логування успішної відповіді"""
        self._log_response(method, url, response=response, request_headers=headers, request_body=body)
    
    def _log_response_error(self, method: str, url: str, error: Exception, headers: dict | None = None, body: Any = None):
        """Wrapper для логування помилки"""
        self._log_response(method, url, error=error, request_headers=headers, request_body=body)

    async def request(self, method, url, query_params=None, headers=None,
                      body=None, post_params=None, _preload_content=True,
                      _request_timeout=None):
        """Перевизначений метод request з логуванням"""
        
        # Логуємо запит
        self._log_request(method, url, headers, body, query_params)
        
        try:
            # Викликаємо оригінальний метод
            response = await super().request(
                method=method,
                url=url, 
                query_params=query_params,
                headers=headers,
                body=body,
                post_params=post_params,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout
            )
            
            if DEBUG:
                self._log_response_success(method, url, response, headers, body)
            
            return response
            
        except Exception as error:
            self._log_response_error(method, url, error, headers, body)

            # Перекидаємо оригінальну помилку зі збереженням стеку викликів
            # Але якщо це ApiException, модифікуємо його __str__ метод напряму
            if hasattr(
                    error, '__class__'
            ) and 'ApiException' in error.__class__.__name__:
                # Створюємо чисте повідомлення без HTTP headers та body
                status_code = getattr(error, 'status', 400)
                detail = getattr(error, 'reason', str(error))
                clean_message = f"({status_code})\nReason: {detail}"

                if hasattr(error, 'body'):
                    try:
                        if isinstance(error.body, bytes):
                            body = json.loads(error.body.decode('utf-8'))
                        else:
                            body = json.loads(error.body) if isinstance(
                                error.body, str
                            ) else error.body
                        if isinstance(body, dict) and 'message' in body:
                            clean_message += f"\nAPI Error: {body['message']}"
                    except:
                        pass

                # Модифікуємо __str__ метод оригінального error об'єкта
                def clean_str_method(self):
                    return clean_message

                # Заміняємо __str__ метод на чистий
                error.__class__.__str__ = clean_str_method

            # Перекидаємо помилку зі збереженням повного стеку викликів
            raise


# Створюємо логуючі версії REST клієнтів для кожного API
if TerminalRESTClient:
    class LoggingTerminalRESTClient(LoggingRESTClientMixin, TerminalRESTClient):
        pass
else:
    LoggingTerminalRESTClient = None

if ClientRESTClient:
    class LoggingClientRESTClient(LoggingRESTClientMixin, ClientRESTClient):
        pass
else:
    LoggingClientRESTClient = None

if UserRESTClient:
    class LoggingUserRESTClient(LoggingRESTClientMixin, UserRESTClient):
        pass
else:
    LoggingUserRESTClient = None