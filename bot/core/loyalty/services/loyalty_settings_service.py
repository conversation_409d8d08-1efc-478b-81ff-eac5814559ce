from datetime import datetime
from typing import Optional

from fastapi import HTTPException, status
from incust_api.api import term

from config import INCUST_SERVER_API
from core.loyalty.incust_api import incust
from db.crud import check_access_to_action
from db.models import (
    Brand, EWallet, InvoiceTemplate, LoyaltySettings, Store,
    StoreProduct, User,
)
from schemas import (
    LoyaltySettingsApplicableTypes, LoyaltySettingsObjectType,
    LoyaltySettingsTypeClientAuth,
)
from schemas.admin.loyalty_settings import (
    LoyaltySettingsConfigSchema, LoyaltySettingsCopySchema, LoyaltySettingsCreateSchema,
    LoyaltySettingsResponseSchema, LoyaltySettingsUpdateSchema,
    LoyaltySettingsValidateSchema,
)
from utils.text import f


def prepare_settings_data(settings_dict: dict) -> dict:
    """Helper function to map database field names to schema field names"""
    # Map database field names to schema field names
    if 'time_created' in settings_dict:
        settings_dict['created_at'] = settings_dict.pop('time_created')
    if 'time_updated' in settings_dict:
        settings_dict['updated_at'] = settings_dict.pop('time_updated')
    
    # Ensure timestamps exist
    if 'created_at' not in settings_dict or settings_dict['created_at'] is None:
        settings_dict['created_at'] = datetime.now().isoformat()
    if 'updated_at' not in settings_dict or settings_dict['updated_at'] is None:
        settings_dict['updated_at'] = None
        
    return settings_dict


class LoyaltySettingsService:
    """
    Universal service for loyalty settings management.
    Can be used by both admin and platform routes.
    """

    def __init__(
            self,
            user: User,
            lang: str,
            profile_id: Optional[int] = None,
    ):
        """
        Initialize the service.
        
        Args:
            user: Current user object
            lang: Language for localization
            profile_id: Profile ID for profile-scoped operations (optional for platform operations)
        """
        self.user = user
        self.lang = lang
        self.profile_id = profile_id

    async def get_config(self) -> LoyaltySettingsConfigSchema:
        """Отримати конфігураційні налаштування лояльності"""
        return LoyaltySettingsConfigSchema(
            incust_server_api=INCUST_SERVER_API
        )

    async def get_loyalty_settings_list(
            self,
            store_id: Optional[int] = None,
            product_id: Optional[int] = None,
            invoice_template_id: Optional[int] = None,
            ewallet_id: Optional[int] = None,
            is_enabled: Optional[bool] = None,
            limit: int = 100,
            offset: int = 0
    ) -> list[LoyaltySettingsResponseSchema]:
        """
        Get list of loyalty settings with optional filters.
        For profile-scoped operations, will filter by profile_id.
        For platform operations (ewallet), can work without profile_id filter.
        """
        
        # Prepare filters
        filters = {}
        if self.profile_id is not None:
            filters['profile_id'] = self.profile_id
        if store_id is not None:
            filters['store_id'] = store_id
        if product_id is not None:
            filters['product_id'] = product_id
        if invoice_template_id is not None:
            filters['invoice_template_id'] = invoice_template_id
        if ewallet_id is not None:
            filters['ewallet_id'] = ewallet_id
        if is_enabled is not None:
            filters['is_enabled'] = is_enabled

        items = await LoyaltySettings.get_list(**filters)

        return [LoyaltySettingsResponseSchema(**item.as_dict()) for item in items]

    async def get_loyalty_settings_by_id(
            self,
            loyalty_settings_id: int,
    ) -> LoyaltySettingsResponseSchema:
        """Get loyalty settings by ID with access control"""

        loyalty_settings = await LoyaltySettings.get(loyalty_settings_id)

        if not loyalty_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Loyalty settings not found"
            )

        # Access control based on context
        await self._check_access_to_settings(loyalty_settings)

        settings_data = prepare_settings_data(loyalty_settings.as_dict())
        return LoyaltySettingsResponseSchema(**settings_data)

    async def create_loyalty_settings(
            self,
            data: LoyaltySettingsCreateSchema
    ) -> LoyaltySettingsResponseSchema:
        """Create new loyalty settings"""
        
        # Мапимо object_type та object_id на відповідні поля
        object_fields = {}
        if data.object_type and data.object_id:
            object_fields = self._map_object_to_fields(data)
        
        # Перевірка прав доступу
        await self._check_object_access(data.object_type, data.object_id)

        # Якщо налаштування вимкнені, не потрібно валідувати API
        terminal_id = None
        loyalty_id = None
        
        if data.is_enabled:
            validation_result = await self._validate_settings(
                white_label_id=data.white_label_id,
                terminal_api_key=data.terminal_api_key,
                server_url=data.server_url
            )

            if not validation_result.is_valid:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=validation_result.errors[
                        0] if validation_result.errors else "Invalid settings"
                )

            # Перевіряємо що отримали terminal_id та loyalty_id з API
            if not validation_result.terminal_id or not validation_result.loyalty_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to get terminal or loyalty ID from API"
                )
                
            terminal_id = validation_result.terminal_id
            loyalty_id = validation_result.loyalty_id

        # Автоматично встановлюємо priority в залежності від object_type (як в міграції)
        priority_by_type = {
            LoyaltySettingsObjectType.PROFILE: 10,
            LoyaltySettingsObjectType.EWALLET: 5,
            LoyaltySettingsObjectType.STORE: 30,
            LoyaltySettingsObjectType.PRODUCT: 50,
            LoyaltySettingsObjectType.INVOICE_TEMPLATE: 40,
            LoyaltySettingsObjectType.BRAND: 15,
        }
        auto_priority = priority_by_type.get(data.object_type, 0)

        # Підготовка даних для створення
        create_data = {
            'white_label_id': data.white_label_id,
            'terminal_api_key': data.terminal_api_key,
            'terminal_id': terminal_id,
            'loyalty_id': loyalty_id,
            'server_url': data.server_url,
            'type_client_auth': LoyaltySettingsTypeClientAuth(data.type_client_auth) if data.type_client_auth else None,
            'prohibit_redeeming_bonuses': data.prohibit_redeeming_bonuses,
            'prohibit_redeeming_coupons': data.prohibit_redeeming_coupons,
            'loyalty_applicable_type': LoyaltySettingsApplicableTypes(data.loyalty_applicable_type) if data.loyalty_applicable_type else LoyaltySettingsApplicableTypes.FOR_PARTICIPANTS,
            'priority': auto_priority,  # Встановлюємо автоматично в залежності від типу
            'is_enabled': data.is_enabled,
            'name': data.name,
            'description': data.description
        }
        
        # Додаємо поля об'єкта
        create_data.update(object_fields)
        
        # Для profile встановлюємо поточний profile_id якщо не вказаний
        if data.object_type == LoyaltySettingsObjectType.PROFILE and 'profile_id' not in object_fields:
            if self.profile_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Profile ID is required for profile-scoped settings"
                )
            create_data['profile_id'] = self.profile_id
        
        settings = await LoyaltySettings.create(**create_data)

        settings_data = prepare_settings_data(settings.as_dict())
        return LoyaltySettingsResponseSchema(**settings_data)

    async def update_loyalty_settings(
            self,
            loyalty_settings_id: int,
            data: LoyaltySettingsUpdateSchema
    ) -> LoyaltySettingsResponseSchema:
        """Update existing loyalty settings"""

        existing_loyalty_settings = await LoyaltySettings.get(loyalty_settings_id)

        if not existing_loyalty_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Loyalty settings not found"
            )

        # Access control
        await self._check_access_to_settings(existing_loyalty_settings)

        # Визначаємо чи будуть налаштування включені
        will_be_enabled = data.is_enabled if data.is_enabled is not None else existing_loyalty_settings.is_enabled
        
        # Якщо налаштування будуть увімкнені, потрібно валідувати API
        terminal_id = existing_loyalty_settings.terminal_id
        loyalty_id = existing_loyalty_settings.loyalty_id
        
        if will_be_enabled:
            # Для увімкнених налаштувань потрібна валідація
            validation_result = await self._validate_settings(
                white_label_id=data.white_label_id or existing_loyalty_settings.white_label_id,
                terminal_api_key=data.terminal_api_key or existing_loyalty_settings.terminal_api_key,
                server_url=data.server_url or existing_loyalty_settings.server_url
            )

            if not validation_result.is_valid:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=validation_result.errors[
                        0] if validation_result.errors else "Invalid settings"
                )

            # Перевіряємо що отримали terminal_id та loyalty_id з API
            if not validation_result.terminal_id or not validation_result.loyalty_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to get terminal or loyalty ID from API"
                )
                
            terminal_id = validation_result.terminal_id
            loyalty_id = validation_result.loyalty_id

        update_data = data.dict(exclude_unset=True)

        # Оновлюємо terminal_id та loyalty_id якщо була валідація
        if will_be_enabled and any([data.white_label_id, data.terminal_api_key, data.server_url]):
            update_data['terminal_id'] = terminal_id
            update_data['loyalty_id'] = loyalty_id

        await existing_loyalty_settings.update(**update_data)

        settings_data = prepare_settings_data(existing_loyalty_settings.as_dict())
        return LoyaltySettingsResponseSchema(**settings_data)

    async def delete_loyalty_settings(
            self,
            loyalty_settings_id: int
    ):
        """Delete loyalty settings (soft delete)"""

        existing_settings = await LoyaltySettings.get(loyalty_settings_id)

        if not existing_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Loyalty settings not found"
            )

        # Access control
        await self._check_access_to_settings(existing_settings)

        await existing_settings.update(is_deleted=True)

    async def copy_loyalty_settings(
            self,
            data: LoyaltySettingsCopySchema
    ) -> LoyaltySettingsResponseSchema:
        """Copy loyalty settings to new configuration"""

        source_settings = await LoyaltySettings.get(id=data.source_id)

        if not source_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Source loyalty settings not found"
            )

        # Access control for source settings
        await self._check_access_to_settings(source_settings)

        settings_data = {
            'ewallet_id': data.ewallet_id,
            'profile_id': data.profile_id or self.profile_id,
            'invoice_template_id': data.invoice_template_id,
            'store_id': data.store_id,
            'product_id': data.product_id,
            'name': data.name if data.name is not None else source_settings.name,
            'description': data.description if data.description is not None else source_settings.description,
            'priority': data.priority if data.priority is not None else source_settings.priority,
        }

        for field in source_settings.__table__.columns:
            field_name = field.name
            if field_name in settings_data or field_name in ['id', 'time_created',
                                                             'time_updated']:
                continue
            settings_data[field_name] = getattr(source_settings, field_name)

        new_settings = await LoyaltySettings.create(**settings_data)

        settings_data = prepare_settings_data(new_settings.as_dict())
        return LoyaltySettingsResponseSchema(**settings_data)

    async def validate_loyalty_settings(
            self,
            data: LoyaltySettingsCreateSchema | LoyaltySettingsUpdateSchema
    ) -> LoyaltySettingsValidateSchema:
        """Validate loyalty settings configuration"""

        return await self._validate_settings(
            white_label_id=data.white_label_id,
            terminal_api_key=data.terminal_api_key,
            server_url=data.server_url
        )

    async def _validate_settings(
            self,
            white_label_id: str,
            terminal_api_key: str,
            server_url: str
    ) -> LoyaltySettingsValidateSchema:
        """Валідація налаштувань лояльності через API."""

        errors = []
        terminal_info = None
        terminal_id = None
        loyalty_id = None

        try:
            # Створюємо тимчасові налаштування для валідації
            temp_settings = LoyaltySettings(
                white_label_id=white_label_id,
                terminal_api_key=terminal_api_key,
                server_url=server_url,
            )

            try:
                async with incust.term.SettingsApi(temp_settings, lang=self.lang) as api:
                    terminal_settings = await api.settings()

                if terminal_settings:
                    # Отримуємо terminal_id з відповіді
                    terminal_id = str(
                        terminal_settings.id
                    ) if terminal_settings.id else None

                    # Шукаємо loyalty_id у різних можливих місцях
                    loyalty_id = None

                    if hasattr(
                            terminal_settings.loyalty, 'id'
                    ):
                        loyalty_id = str(
                            terminal_settings.loyalty.id
                        )

                    terminal_info = {
                        "terminal_id": terminal_id,
                        "terminal_name": terminal_settings.title,
                        "loyalty_id": loyalty_id,
                    }

                    # Перевіряємо що отримали обидва значення
                    if not terminal_id:
                        errors.append(
                            await f("terminal id not received from api", self.lang)
                        )
                    if not loyalty_id:
                        errors.append(
                            await f("loyalty id not received from api", self.lang)
                        )
                else:
                    errors.append(await f("invalid terminal settings", self.lang))

            except term.ApiException as e:
                # Обробляємо помилки API
                if hasattr(e, 'status') and e.status == 401:
                    errors.append(await f("invalid terminal api key", self.lang))
                elif hasattr(e, 'status') and e.status == 404:
                    errors.append(await f("terminal not found", self.lang))
                else:
                    status_code = getattr(e, 'status', 'unknown')
                    errors.append(
                        await f(
                            "terminal validation error", self.lang,
                            error=f"Status: {status_code}"
                        )
                    )
            except Exception as e:
                import traceback
                traceback.print_exc()
                error_msg = str(e).lower()
                if "connection" in error_msg:
                    errors.append(
                        await f("cannot connect to loyalty server", self.lang)
                    )
                else:
                    errors.append(
                        await f("terminal validation error", self.lang, error=str(e))
                    )

        except Exception as e:
            errors.append(
                await f("unexpected validation error", self.lang, error=str(e))
            )

        return LoyaltySettingsValidateSchema(
            is_valid=len(errors) == 0,
            errors=errors if errors else None,
            terminal_info=terminal_info,
            terminal_id=terminal_id,
            loyalty_id=loyalty_id
        )
    
    async def get_by_object(
            self,
            object_type: LoyaltySettingsObjectType,
            object_id: int
    ) -> Optional[LoyaltySettingsResponseSchema]:
        """Отримати налаштування лояльності для конкретного об'єкта"""
        
        # Перевірка прав доступу
        await self._check_object_access(object_type, object_id)
        
        # Формуємо фільтри для пошуку
        filters = self._get_filters_by_object_type(object_type, object_id)
        
        # Шукаємо налаштування
        settings = await LoyaltySettings.get(**filters)
        
        if not settings:
            return None
            
        settings_data = prepare_settings_data(settings.as_dict())
        return LoyaltySettingsResponseSchema(**settings_data)
    
    async def create_or_update_by_object(
            self,
            object_type: LoyaltySettingsObjectType,
            object_id: int,
            data: LoyaltySettingsCreateSchema
    ) -> LoyaltySettingsResponseSchema:
        """Створити або оновити налаштування лояльності для об'єкта"""
        
        # Встановлюємо object_type та object_id
        data.object_type = object_type
        data.object_id = object_id
        
        # Перевірка прав доступу
        await self._check_object_access(object_type, object_id)
        
        # Перевіряємо чи існують налаштування
        filters = self._get_filters_by_object_type(object_type, object_id)
        existing_settings = await LoyaltySettings.get(**filters)
        
        if existing_settings:
            # Оновлюємо існуючі
            update_data = data.dict(exclude_unset=True, exclude={'object_type', 'object_id'})
            
            # Визначаємо чи будуть налаштування включені
            will_be_enabled = data.is_enabled if data.is_enabled is not None else existing_settings.is_enabled
            
            # Валідація тільки якщо налаштування будуть увімкнені
            if will_be_enabled:
                validation_result = await self._validate_settings(
                    white_label_id=data.white_label_id,
                    terminal_api_key=data.terminal_api_key,
                    server_url=data.server_url
                )
                
                if not validation_result.is_valid:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=validation_result.errors[0] if validation_result.errors else "Invalid settings"
                    )
                    
                update_data['terminal_id'] = validation_result.terminal_id
                update_data['loyalty_id'] = validation_result.loyalty_id
            else:
                # Якщо вимикаємо - очищуємо terminal_id та loyalty_id
                update_data['terminal_id'] = None
                update_data['loyalty_id'] = None
            
            await existing_settings.update(**update_data)
            settings_data = prepare_settings_data(existing_settings.as_dict())
            return LoyaltySettingsResponseSchema(**settings_data)
        else:
            # Створюємо нові
            return await self.create_loyalty_settings(data)
    
    async def delete_by_object(
            self,
            object_type: LoyaltySettingsObjectType,
            object_id: int
    ):
        """Видалити налаштування лояльності для об'єкта"""
        
        # Перевірка прав доступу
        await self._check_object_access(object_type, object_id)
        
        # Формуємо фільтри для пошуку
        filters = self._get_filters_by_object_type(object_type, object_id)
        
        # Шукаємо налаштування
        settings = await LoyaltySettings.get(**filters)
        
        if not settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Loyalty settings not found"
            )
            
        await settings.update(is_deleted=True)
    
    def _map_object_to_fields(self, data: LoyaltySettingsCreateSchema) -> dict:
        """Мапити object_type та object_id на відповідні поля моделі"""
        
        fields = {}
        
        # Встановлюємо потрібне поле
        if data.object_type == LoyaltySettingsObjectType.PROFILE:
            fields['profile_id'] = data.object_id
        elif data.object_type == LoyaltySettingsObjectType.BRAND:
            fields['brand_id'] = data.object_id
        elif data.object_type == LoyaltySettingsObjectType.STORE:
            fields['store_id'] = data.object_id
        elif data.object_type == LoyaltySettingsObjectType.PRODUCT:
            fields['product_id'] = data.object_id
        elif data.object_type == LoyaltySettingsObjectType.INVOICE_TEMPLATE:
            fields['invoice_template_id'] = data.object_id
        elif data.object_type == LoyaltySettingsObjectType.EWALLET:
            fields['ewallet_id'] = data.object_id
            
        return fields
    
    async def _check_access_to_settings(self, settings: LoyaltySettings):
        """Check access to existing loyalty settings"""
        
        # For profile-scoped settings, check profile access
        if settings.profile_id is not None:
            if self.profile_id is None or settings.profile_id != self.profile_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        
        # For ewallet settings, check platform access
        if settings.ewallet_id is not None:
            has_access = await check_access_to_action(
                "platform:admin",
                "user",
                self.user.id,
                available_data={
                    "ewallet_id": settings.ewallet_id
                }
            )
            if not has_access:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
    
    async def _check_object_access(self, object_type: LoyaltySettingsObjectType, object_id: int):
        """Перевірити існування об'єкта та права доступу до нього"""
        
        if object_type == LoyaltySettingsObjectType.PROFILE:
            # Для профілю перевіряємо що це поточний профіль та права доступу
            if self.profile_id is None or object_id != self.profile_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Profile not found"
                )
            # Перевірка прав доступу через Scope
            has_access = await check_access_to_action(
                "profile:read",
                "user",
                self.user.id,
                available_data={
                    "profile_id": self.profile_id
                }
            )
            if not has_access:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Profile not found"
                )
                
        elif object_type == LoyaltySettingsObjectType.BRAND:
            # Перевірка існування бренду
            brand = await Brand.get(object_id)
            if not brand or brand.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Brand not found"
                )
            # Перевірка прав доступу через Scope
            if self.profile_id is not None:
                has_access = await check_access_to_action(
                    "profile:read",
                    "user", 
                    self.user.id,
                    available_data={
                        "profile_id": self.profile_id,
                        "brand_id": object_id
                    }
                )
                if not has_access:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Brand not found"
                    )
                
        elif object_type == LoyaltySettingsObjectType.STORE:
            # Перевірка існування магазину
            store = await Store.get(object_id)
            if not store or store.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Store not found"
                )
            # Перевірка прав доступу через Scope
            if self.profile_id is not None:
                has_access = await check_access_to_action(
                    "profile:read",
                    "user",
                    self.user.id,
                    available_data={
                        "profile_id": self.profile_id,
                        "store_id": object_id
                    }
                )
                if not has_access:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Store not found"
                    )
                
        elif object_type == LoyaltySettingsObjectType.PRODUCT:
            # Перевірка існування товару
            product = await StoreProduct.get(object_id)
            if not product or product.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Product not found"
                )
            # Перевірка прав доступу через Scope
            if self.profile_id is not None:
                has_access = await check_access_to_action(
                    "profile:read",
                    "user",
                    self.user.id,
                    available_data={
                        "profile_id": self.profile_id,
                        "product_id": object_id
                    }
                )
                if not has_access:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Product not found"
                    )
                
        elif object_type == LoyaltySettingsObjectType.INVOICE_TEMPLATE:
            # Перевірка існування шаблону рахунку
            template = await InvoiceTemplate.get(object_id)
            if not template or template.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Invoice template not found"
                )
            # Перевірка прав доступу через Scope
            if self.profile_id is not None:
                has_access = await check_access_to_action(
                    "profile:read",
                    "user",
                    self.user.id,
                    available_data={
                        "profile_id": self.profile_id,
                        "invoice_template_id": object_id
                    }
                )
                if not has_access:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Invoice template not found"
                    )
                
        elif object_type == LoyaltySettingsObjectType.EWALLET:
            # Перевірка існування e-wallet
            ewallet = await EWallet.get(object_id)
            if not ewallet or ewallet.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="E-wallet not found"
                )
            # Перевірка прав доступу через Scope (ewallet це платформний ресурс)
            has_access = await check_access_to_action(
                "platform:admin",
                "user",
                self.user.id,
                available_data={
                    "ewallet_id": object_id
                }
            )
            if not has_access:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="E-wallet not found"
                )
    
    def _get_filters_by_object_type(self, object_type: LoyaltySettingsObjectType, object_id: int) -> dict:
        """Отримати фільтри для пошуку за типом об'єкта"""
        
        filters = {}
        
        if object_type == LoyaltySettingsObjectType.PROFILE:
            filters['profile_id'] = object_id
        elif object_type == LoyaltySettingsObjectType.BRAND:
            filters['brand_id'] = object_id
        elif object_type == LoyaltySettingsObjectType.STORE:
            filters['store_id'] = object_id
        elif object_type == LoyaltySettingsObjectType.PRODUCT:
            filters['product_id'] = object_id
        elif object_type == LoyaltySettingsObjectType.INVOICE_TEMPLATE:
            filters['invoice_template_id'] = object_id
        elif object_type == LoyaltySettingsObjectType.EWALLET:
            filters['ewallet_id'] = object_id
            
        return filters