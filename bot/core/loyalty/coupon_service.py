"""
Єдиний сервіс для роботи з купонами через нові InCust API клієнти
"""

import io
import logging
from enum import Enum
from typing import Optional, Tuple, Union

from fastapi import HTTPException, UploadFile
from incust_api.api import client, term
from incust_client_api_client import MessageResponse

import schemas
from config import INCUST_SERVER_API
from core.loyalty.helper import strip_html_tags_for_telegram
from core.loyalty.incust_api import incust
from core.media_manager import media_manager
from db.models import Brand, LoyaltySettings, MediaObject, User
from utils.text import f

logger = logging.getLogger('debugger.coupon_service')


class PDFFormatType(Enum):
    """Типи форматів для завантаження PDF"""
    BYTES = "bytes"
    FILE_PATH = "file_path"
    MIME_ATTACHMENT = "mime_attachment"


class CouponService:
    """Єдиний сервіс для всіх операцій з купонами"""

    def __init__(self):
        self._pdf_cache = {}  # Кеш для PDF даних на час виконання

    async def get_coupon_data(
        self,
        coupon_id_or_code: str,
        user: Optional[User],
        loyalty_settings: LoyaltySettings,
        lang: str
    ) -> Optional[client.m.Coupon]:
        """
        Отримує дані купона з API InCust
        
        Args:
            coupon_id_or_code: ID або код купона
            user: Користувач (може бути None для публічного перегляду)
            loyalty_settings: Налаштування лояльності
            lang: Мова
            
        Returns:
            Дані купона або None
        """
        try:
            # Визначаємо чи це код (12 цифр) чи ID
            is_code = len(coupon_id_or_code.replace('-', '')) == 12
            
            if user:
                async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
                    if is_code:
                        coupon = await api.coupon_info(code=coupon_id_or_code)
                    else:
                        # Спробуємо спочатку як ID користувача
                        try:
                            coupon = await api.coupon_get(coupon_id=coupon_id_or_code)
                        except client.ApiException:
                            # Якщо не знайдено - спробуємо як код
                            coupon = await api.coupon_info(code=coupon_id_or_code)
            else:
                # Без користувача - тільки публічна інформація
                async with incust.client.CouponApi(loyalty_settings, lang=lang) as api:
                    coupon = await api.coupon_info(code=coupon_id_or_code)
                    
            return coupon
            
        except Exception as e:
            logger.error(f"Error getting coupon data {coupon_id_or_code}: {e}", exc_info=True)
            return None

    async def add_to_wallet(
        self,
        coupon_code: str,
        user: User,
        loyalty_settings: LoyaltySettings,
        lang: str
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        Додає купон до гаманця користувача
        
        Args:
            coupon_code: Код купона
            user: Користувач
            loyalty_settings: Налаштування лояльності
            lang: Мова
            
        Returns:
            Tuple[coupon_id, message] - ID доданого купона та повідомлення або (None, None)
        """
        # Додаємо через terminal API
        async with incust.term.CouponsApi(loyalty_settings, lang=lang) as api:
            result = await api.customer_coupon_add(
                coupon_code=[term.m.CouponCode(code=coupon_code)],
                user_id_value=user.incust_external_id,
                user_id_type="external-id"
            )

        if result and hasattr(result, 'coupons') and result.coupons:
            added_coupon = result.coupons[0]
            coupon_id = str(added_coupon.id) if hasattr(added_coupon, 'id') and added_coupon.id else coupon_code
            message = getattr(result, 'message', None)

            logger.info(f"Coupon {coupon_code} added to wallet for user {user.id}: {coupon_id}")
            return coupon_id, message

        return None, None


    async def redeem_coupon(
        self,
        coupon_id: str,
        user: User,
        loyalty_settings: LoyaltySettings,
        lang: str
    ) -> MessageResponse:
        """
        Застосовує/викупляє купон
        
        Args:
            coupon_id: ID купона
            user: Користувач
            loyalty_settings: Налаштування лояльності
            lang: Мова
            
        Returns:
            Результат викупу або None
        """
        async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
            result = await api.coupon_client_redeem(coupon_id=coupon_id)

        logger.info(f"Coupon {coupon_id} redeemed for user {user.id}")
        return result


    async def share_coupon(
        self,
        coupon_id: str,
        user: User,
        loyalty_settings: LoyaltySettings,
        lang: str
    ) -> Optional[str]:
        """
        Ділиться купоном
        
        Args:
            coupon_id: ID купона
            user: Користувач
            loyalty_settings: Налаштування лояльності
            lang: Мова
            
        Returns:
            Код для поділення або None
        """
        try:
            async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
                result = await api.coupon_client_share(coupon_id=coupon_id)
                
            if result and hasattr(result, 'message'):
                logger.info(f"Coupon {coupon_id} shared by user {user.id}: {result.message}")
                return result.message
                
            return None
            
        except client.ApiException as e:
            logger.error(f"API error sharing coupon {coupon_id}: {e.reason}")
            if "not available" in str(e.reason).lower():
                raise HTTPException(
                    status_code=400,
                    detail=await f('not available coupon for share', lang)
                )
            raise
        except Exception as e:
            logger.error(f"Error sharing coupon {coupon_id}: {e}", exc_info=True)
            raise

    async def save_coupon_pdf(
        self,
        coupon_id: str,
        pdf_data: bytes
    ) -> Optional[dict]:
        """
        Зберігає PDF купона через media_manager
        
        Args:
            coupon_id: ID купона
            pdf_data: Бінарні дані PDF
            
        Returns:
            {"media_id": int, "url": str} або None
        """
        try:
            # Створюємо UploadFile з PDF даних
            pdf_file = UploadFile(
                file=io.BytesIO(pdf_data),
                filename=f"coupon_{coupon_id}.pdf",
            )
            
            # Зберігаємо через media_manager
            media_object = await media_manager.save_from_upload_file(
                from_file=pdf_file,
            )
            
            if media_object:
                relative_path = media_object.file_path.replace(
                    media_manager.base_dir + "/", ""
                )
                logger.info(f"PDF for coupon {coupon_id} saved: media_id={media_object.id}")
                
                return {
                    "media_id": media_object.id,
                    "url": relative_path
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error saving PDF for coupon {coupon_id}: {e}", exc_info=True)
            return None

    async def get_saved_coupon_pdf_url(
        self,
        coupon_id_or_code: str,
        media_id: Optional[int] = None
    ) -> Optional[str]:
        """
        Перевіряє чи є збережений PDF файл для купона
        
        Args:
            coupon_id_or_code: ID або код купона (використовується як ідентифікатор)
            media_id: ID медіа об'єкта (якщо відомий)
            
        Returns:
            URL збереженого PDF або None
        """
        try:
            media_object = None
            
            if media_id:
                media_object = await MediaObject.get(media_id)
            else:
                # Шукаємо за назвою файлу (використовуємо переданий ідентифікатор)
                media_objects = await MediaObject.get_list(
                    media_type="pdf",
                    original_file_name=f"coupon_{coupon_id_or_code}.pdf"
                )
                if media_objects:
                    media_object = media_objects[0]
                    
            if media_object:
                relative_path = media_object.file_path.replace(
                    media_manager.base_dir + "/", ""
                )
                return relative_path
                
            return None
            
        except Exception as e:
            logger.error(f"Error checking saved PDF for coupon {coupon_id_or_code}: {e}", exc_info=True)
            return None

    async def load_pdf_from_media_object(
        self,
        media_id: int,
        format_type: PDFFormatType = PDFFormatType.BYTES,
        filename_override: Optional[str] = None
    ) -> Optional[Union[bytes, str, any]]:
        """
        Завантажує PDF дані з MediaObject в різних форматах
        
        Args:
            media_id: ID медіа об'єкта
            format_type: Тип формату повернення (PDFFormatType enum)
            filename_override: Перевизначене ім'я файлу для attachment
            
        Returns:
            - PDFFormatType.BYTES: bytes (бінарні дані)
            - PDFFormatType.FILE_PATH: str (шлях до файлу)
            - PDFFormatType.MIME_ATTACHMENT: MIMEBase (готовий email attachment)
            або None при помилці
        """
        try:
            media_obj = await MediaObject.get(media_id)
            if not media_obj:
                logger.warning(f"MediaObject not found for id {media_id}")
                return None
                
            import os
            file_path = media_obj.file_path
            if not os.path.exists(file_path):
                logger.warning(f"PDF file not found at path: {file_path}")
                return None
            
            if format_type == PDFFormatType.FILE_PATH:
                return file_path
            elif format_type == PDFFormatType.BYTES:
                with open(file_path, 'rb') as f:
                    return f.read()
            elif format_type == PDFFormatType.MIME_ATTACHMENT:
                from email.mime.base import MIMEBase
                from email import encoders
                
                with open(file_path, 'rb') as f:
                    pdf_content = f.read()
                
                pdf_mime = MIMEBase('application', 'pdf')
                pdf_mime.set_payload(pdf_content)
                encoders.encode_base64(pdf_mime)
                pdf_mime.add_header(
                    'Content-Disposition',
                    'attachment',
                    filename=filename_override or media_obj.original_file_name or media_obj.file_name
                )
                return pdf_mime
            else:
                logger.error(f"Unsupported format_type: {format_type}")
                return None
                
        except Exception as e:
            logger.error(f"Error loading PDF from media_id {media_id}: {e}", exc_info=True)
            return None

    async def get_coupon_pdf_from_external_service(
        self,
        brand: Brand,
        coupon_id: str,
        coupon_code: str,
        base_url: str = INCUST_SERVER_API,
        lang: str = "uk"
    ) -> Optional[bytes]:
        """
        Отримує PDF дані купона через зовнішній InCust сервіс
        
        Args:
            brand: Бренд
            coupon_id: ID купона
            coupon_code: Код купона
            base_url: Базовий URL API
            lang: Мова
            
        Returns:
            Бінарні дані PDF або None
        """
        try:
            import aiohttp
            import base64
            
            # Формуємо URL для PDF endpoint
            invoice_pdf_url = base_url.replace('api.', 'mf-invoices.')
            pdf_url = f"{invoice_pdf_url}/api/generate_coupon/{coupon_id or coupon_code}"
            
            # URL купона для параметра
            coupon_url = brand.get_url(f"{lang}/coupon/show/{coupon_id or coupon_code}")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    pdf_url,
                    params={
                        "lang": lang,
                        "custom_open_link": coupon_url
                    }
                ) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        pdf_base64_data = response_data.get('data')
                        if pdf_base64_data:
                            return base64.b64decode(pdf_base64_data)
                            
            return None
            
        except Exception as e:
            logger.error(f"Error getting PDF from external service for coupon {coupon_id}: {e}", exc_info=True)
            return None

    async def prepare_coupon_for_display(
        self,
        coupon_data: term.m.Coupon,
        brand: Brand,
        loyalty_settings: LoyaltySettings,
        user: Optional[User],
        lang: str,
    ) -> Optional[schemas.CouponShowData]:
        """
        Готує CouponShowData з усіма даними (PDF генеруватиметься в методах відправки)
        
        Args:
            coupon_data: Дані купона (term.m.Coupon об'єкт або dict)
            brand: Бренд
            loyalty_settings: Налаштування лояльності
            user: Користувач
            lang: Мова
            
        Returns:
            CouponShowData або None
        """
        try:
            # Працюємо безпосередньо з об'єктом купона
            coupon_id = getattr(coupon_data, 'id', None)
            coupon_code = getattr(coupon_data, 'code', None)
            
            if not coupon_id and not coupon_code:
                logger.warning(f"Coupon without ID and code: {coupon_data}")
                return None
                
            # Отримуємо повні дані купона якщо потрібно
            if not getattr(coupon_data, 'title', None) or not getattr(coupon_data, 'description', None):
                full_coupon = await self.get_coupon_data(
                    coupon_id or coupon_code,
                    user,
                    loyalty_settings,
                    lang
                )
                if full_coupon:
                    coupon_data = full_coupon
                    
            # Обробка PDF метаданих тільки якщо купон має ID
            pdf_media_id = None
            
            if coupon_id or coupon_code:  # PDF метадані для купонів з ID або кодом
                # Визначаємо ідентифікатор для PDF (пріоритет по ID, потім по коду)
                pdf_identifier = coupon_id if coupon_id else coupon_code
                pdf_filename = f"coupon_{pdf_identifier}.pdf"
                
                # Перевіряємо чи є збережений PDF за цим ім'ям
                existing_media_objects = await MediaObject.get_list(
                    media_type="pdf",
                    original_file_name=pdf_filename
                )
                
                if existing_media_objects:
                    # Використовуємо існуючий PDF
                    media_object = existing_media_objects[0]
                    pdf_media_id = media_object.id
                else:
                    # Генеруємо новий PDF і зберігаємо
                    pdf_data = await self.get_coupon_pdf_from_external_service(
                        brand,
                        pdf_identifier,
                        coupon_code or coupon_id,
                        loyalty_settings.server_url,
                        lang
                    )
                    
                    if pdf_data:
                        # Зберігаємо PDF з тим самим ім'ям що й шукали
                        save_result = await self.save_coupon_pdf(pdf_identifier, pdf_data)
                        if save_result:
                            pdf_media_id = save_result["media_id"]
                    
            # Формуємо текст
            title = getattr(coupon_data, 'title', '')
            description = strip_html_tags_for_telegram(getattr(coupon_data, 'description', ''))
            
            # Формуємо URL купона
            coupon_url = brand.get_url(f"{lang}/coupon/show/{coupon_id or coupon_code}")
            
            # Формуємо PDF URL якщо PDF збережений
            pdf_url = None
            if pdf_media_id:
                pdf_url = await self.get_saved_coupon_pdf_url(
                    coupon_id or coupon_code,
                    pdf_media_id
                )
            
            # Створюємо CouponShowData з оригінального об'єкта
            coupon_show_data = schemas.CouponShowData.from_coupon(coupon_data)
            
            # Додаємо збагачені поля
            coupon_show_data.text = f"<b>{title}</b>\n\n{description}\n"
            coupon_show_data.url = coupon_url
            coupon_show_data.pdf_url = pdf_url
            
            # PDF метадані тільки для збереження (сам PDF буде формуватися в методах відправки)
            coupon_show_data.pdf_media_id = pdf_media_id
            
            return coupon_show_data
            
        except Exception as e:
            logger.error(f"Error preparing coupon for display: {e}", exc_info=True)
            return None

    async def check_if_in_wallet(
        self,
        coupon_id_or_code: str,
        user: User,
        loyalty_settings: LoyaltySettings,
        lang: str
    ) -> bool:
        """
        Перевіряє чи купон вже в гаманці користувача
        
        Args:
            coupon_id_or_code: ID або код купона
            user: Користувач
            loyalty_settings: Налаштування лояльності
            lang: Мова
            
        Returns:
            True якщо купон в гаманці
        """
        try:
            # Визначаємо чи це код чи ID
            is_code = len(coupon_id_or_code.replace('-', '')) == 12
            
            if is_code:
                # Для коду - перевіряємо через terminal API
                async with incust.term.CouponsApi(loyalty_settings, lang=lang) as api:
                    user_coupons = await api.customer_coupons(
                        user_id_value=user.incust_external_id,
                        user_id_type="external-id"
                    )
                    
                if user_coupons:
                    for coupon in user_coupons:
                        if hasattr(coupon, 'code') and coupon.code == coupon_id_or_code:
                            return True
            else:
                # Для ID - перевіряємо через client API
                try:
                    async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
                        coupon = await api.coupon_get(coupon_id=coupon_id_or_code)
                    return bool(coupon)
                except client.ApiException:
                    return False
                    
            return False
            
        except Exception as e:
            logger.error(f"Error checking if coupon in wallet: {e}", exc_info=True)
            return False

coupon_service = CouponService()