from abc import ABC

from psutils.exceptions import ErrorWithTextVariable


class InCustAPIBaseError(ErrorWithTextVariable, ABC):
    """
    Спеціальний виняток для обробки помилок, що повертаються з InCust API.
    Зберігає оригінальний текст помилки.
    """

    def __init__(self, detail: str, status_code: int = 400):
        super().__init__(detail=detail, status_code=status_code)
        self.detail = detail
        self.status_code = status_code


class InCustAPIError(InCustAPIBaseError):
    """
    Конкретна реалізація InCustAPIError для використання в коді.
    """
    text_variable = "incust api error"
