"""
Сервіс для роботи з InCust клієнтами користувачів.
Винесено з incust_client_adapter.py щоб уникнути циклічних імпортів.
"""
import uuid
from typing import Optional

from sqlalchemy.exc import IntegrityError

from config import (
    INCUST_EXTERNAL_SYSTEM_ID, RENEW_INCUST_TOKEN_DATETIME, TOKEN_SERIALIZER,
    USER_VERIFICATION_TOKEN_EXPIRE,
)
from db import crud
from db.models import IncustCustomer, LoyaltySettings, User
from db.sync_to_async import async_rollback
from loggers import J<PERSON><PERSON>ogger
from schemas import LoyaltySettingsTypeClientAuth
from utils.date_time import utcnow
from utils.jwt_token import create_jwt_token


async def create_and_set_push_token(
        user: User,
        loyalty_settings: LoyaltySettings,
        incust_customer: Optional[IncustCustomer] = None,
        user_token: Optional[str] = None
) -> Optional[str]:
    """
    Створює push токен та встановлює його в InCust API.
    Повертає push токен або None у разі помилки.
    """
    logger = JSONLogger(
        "loyalty.customer_service.create_push_token",
        {
            "user_id": user.id if user else None,
            "loyalty_settings": loyalty_settings.dict() if loyalty_settings else None,
            "has_incust_customer": bool(incust_customer),
            "has_user_token": bool(user_token),
        }
    )
    
    if not user_token:
        if not incust_customer:
            logger.debug("create_and_set_push_token: no incust_customer and no user_token")
            return None
        user_token = incust_customer.token

    # Отримуємо white_label_id
    white_label_id = loyalty_settings.white_label_id
    if not white_label_id:
        logger.debug("create_and_set_push_token: no white_label_id available")
        return None

    # Створюємо push_token
    push_token = TOKEN_SERIALIZER.dumps({
        "brand_id": loyalty_settings.brand_id,
        "user_id": user.id,
        "white_label_id": white_label_id,
        "api_url": loyalty_settings.server_url,
        "user_token": user_token,
        "version": 1,
    })

    # Зберігаємо старі значення для можливого видалення
    old_push_token = incust_customer.push_token if incust_customer else None
    old_wl_id = incust_customer.push_token_wl_id if incust_customer else None

    # Встановлюємо токен в API
    if push_token:
        try:
            from core.loyalty.incust_api import incust
            from incust_api.api import client
            
            # Створюємо дані для chatbot токена
            chatbot_token_data = client.m.ChatbotTokenData(
                channel=client.m.ChatbotChannels.TELEGRAM,
                token=push_token
            )
            
            async with incust.client.ChatbotApi(loyalty_settings, user_token=user_token) as api:
                await api.chatbot_token_set(chatbot_token_data)

            # Оновлюємо incust_customer з новими значеннями, якщо він є
            if incust_customer:
                await incust_customer.update({
                    "push_token": push_token,
                    "push_token_wl_id": white_label_id,
                    "push_token_update_datetime": utcnow()
                })

            # Видаляємо старий токен, якщо він відрізняється
            if old_push_token and old_push_token != push_token or (
                    old_wl_id and old_wl_id != white_label_id):
                try:
                    async with incust.client.ChatbotApi(loyalty_settings, user_token=user_token) as api:
                        await api.chatbot_token_del(old_push_token, "telegram")
                except Exception as e:
                    logger.debug(f"create_and_set_push_token: delete old token error: {e}")

        except Exception as e:
            logger.error(f"create_and_set_push_token: unexpected error: {e}")
            return None

    return push_token


async def get_or_create_incust_customer(
        user: User,
        loyalty_settings: LoyaltySettings,
        lang: str = "en",
        force_renew: bool = False,
        update_lang: bool = True
) -> Optional[IncustCustomer]:
    """
    Отримує або створює InCust клієнта для користувача.
    Відтворює логіку _init_incust_customer
    """
    logger = JSONLogger(
        "loyalty.customer_service.get_or_create_customer",
        {
            "user_id": user.id if user else None,
            "chat_id": user.chat_id if user else None,
            "loyalty_settings": loyalty_settings.dict() if loyalty_settings else None,
            "lang": lang,
            "force_renew": force_renew,
            "update_lang": update_lang,
        }
    )
    
    if not user:
        raise ValueError("User is required for get_or_create_incust_customer")

    if user.is_anonymous or user.is_guest_user:
        logger.debug(
            "get_or_create_incust_customer: user is anonymous or guest user"
        )
        return None

    # Шукаємо brand через пов'язані об'єкти
    brand_id = await get_brand_id_from_loyalty_settings(loyalty_settings)
    
    if not brand_id:
        raise ValueError("Cannot determine brand_id from loyalty settings - no valid references found")

    # Встановлюємо incust_external_id якщо немає
    if not user.incust_external_id:
        await user.update(uuid_token=uuid.uuid4().hex)

    # Шукаємо існуючого клієнта
    incust_customer: Optional[IncustCustomer] = await IncustCustomer.get(
        user_id=user.id,
        brand_id=brand_id,
    )

    async def update_existing_incust_customer():
        """Оновлює існуючого клієнта подібно до логіки _init_incust_customer"""
        if not incust_customer:
            return "need_register"

        user_token = incust_customer.token

        try:
            # Перевіряємо чи потрібно оновити токен
            if force_renew or (
                incust_customer.token_update_datetime + RENEW_INCUST_TOKEN_DATETIME < utcnow()
            ):
                try:
                    # Імпортуємо incust тут щоб уникнути циклічного імпорту
                    from core.loyalty.incust_api import incust
                    
                    async with incust.user.AuthApi(loyalty_settings, user_token=user_token, lang=lang) as user_api:
                        token_response = await user_api.token_renew()

                    if not token_response or not token_response.token:
                        return "need_register"

                    user_token = token_response.token
                    
                    await incust_customer.update({
                        "token": user_token,
                        "token_update_datetime": utcnow()
                    })

                except Exception as ex:
                    logger.error(f"Token renewal failed for user {user.id}: {ex}")
                    return "need_register"
            
            # Перевіряємо чи потрібно оновити push токен
            if not incust_customer.push_token or force_renew:
                try:
                    push_token = await create_and_set_push_token(
                        user, loyalty_settings, incust_customer, user_token
                    )
                except Exception as ex:
                    logger.error(f"Push token update failed for user {user.id}: {ex}")
                    # Не повертаємо need_register, бо push токен не є критичним

            # Оновлюємо мову користувача та перевіряємо токен
            try:
                from core.loyalty.incust_api import incust
                from incust_api.api import user as user_client

                async with incust.user.UserApi(loyalty_settings, user_token=user_token, lang=lang) as user_api:
                    if update_lang:
                        try:
                            profile = user_client.m.UserProfile(language=lang)
                            await user_api.profile_patch(profile)
                        except Exception as ex:
                            logger.error(f"Failed to update language for user {user.id}: {ex}")

                    # Потім перевіряємо що токен працює
                    profile_result = await user_api.profile_get()
                
                return "ok"

            except Exception as ex:
                logger.error(f"Token validation failed for user {user.id}: {ex}")
                return "need_register"

        except Exception as ex:
            logger.error(f"Error during existing customer update for user {user.id}: {ex}")
            return "need_register"

    # Якщо клієнт існує, перевіряємо його стан
    if incust_customer:
        customer_status = await update_existing_incust_customer()
        if customer_status == "ok":
            # Повертаємо оновленого клієнта
            logger.info(f"Updated incust_customer: user_id={user.id}, brand_id={brand_id}, external_id={user.incust_external_id}")
            return incust_customer

    # Реєстрація нового клієнта або перереєстрація
    try:
        # Створюємо JWT токен для реєстрації користувача (з правильною структурою)
        token = create_jwt_token(
            data={
                "sub": f"{user.id}",
                "type": "user",
                "scopes": ["me:read"],
                "brand_id": brand_id,
            },
            expire=USER_VERIFICATION_TOKEN_EXPIRE
        )

        # Імпортуємо incust тут щоб уникнути циклічного імпорту
        from core.loyalty.incust_api import incust
        from incust_api.api import user as user_client
        
        # Створюємо запит для trusted system
        trusted_request = user_client.m.TrustedAuthRequestData(
            system=INCUST_EXTERNAL_SYSTEM_ID,
            create=True,
            token=token
        )
        
        async with incust.user.AuthApi(loyalty_settings, lang=lang) as api:
            token_response = await api.token_by_trusted_system(trusted_request)

        if not token_response or not token_response.token:
            raise Exception("Token is not received from User API")

        # Створюємо push токен
        push_token = await create_and_set_push_token(
            user, loyalty_settings, incust_customer, token_response.token
        )

        # Оновлюємо мову користувача
        if update_lang:
            try:
                from core.loyalty.incust_api import incust
                from incust_api.api import user as user_client

                profile = user_client.m.UserProfile(language=lang)
                
                async with incust.user.UserApi(loyalty_settings, user_token=token_response.token, lang=lang) as api:
                    await api.profile_patch(profile)
            except Exception as ex:
                logger.error(f"Failed to update language for user {user.id}: {ex}")

        # Встановлюємо правильний access_type на основі налаштувань лояльності
        try:
            from incust_api.api import term

            # Отримуємо поточну інформацію про карту
            async with incust.term.CustomerApi(loyalty_settings, lang=lang) as api:
                card_info = await api.cardinfo(
                    user_id_value=user.incust_external_id,
                    user_id_type="external-id"
                )

            # Визначаємо бажаний access_type
            # Якщо type_client_auth = "bot" і користувач НЕ з мессенджера - тільки накопичення бонусів
            # Інакше - повний доступ до бонусів
            if (loyalty_settings.type_client_auth == LoyaltySettingsTypeClientAuth.BOT and 
                not user.is_messanger):
                desired_access_type = "add-bonuses-only"
            else:
                desired_access_type = "full-bonuses-access"

            # Перевіряємо чи потрібно оновити access_type
            if card_info.customer and hasattr(card_info.customer, 'access_type'):
                if card_info.customer.access_type != desired_access_type:
                    async with incust.term.CustomerApi(loyalty_settings, lang=lang) as api:
                        await api.customer_access_type_change_ex(
                            user.incust_external_id,
                            "external-id",
                            term.m.CustomerAccess(
                                access_type=term.m.CustomerAccessType(desired_access_type)
                            ),
                        )
            else:
                logger.warning(f"Cannot get access_type for user {user.id}, card_info: {card_info}")

        except Exception as ex:
            logger.error(f"Failed to set access_type for user {user.id}: {ex}")
            # Не кидаємо виключення - це не критично для створення клієнта

        # Створюємо або оновлюємо запис в БД
        if incust_customer:
            update_data = {
                "token": token_response.token,
                "token_update_datetime": utcnow()
            }
            if push_token:
                update_data.update({
                    "push_token": push_token,
                    "push_token_wl_id": loyalty_settings.white_label_id,
                    "push_token_update_datetime": utcnow()
                })
            await incust_customer.update(update_data)
            logger.info(f"Updated incust_customer: user_id={user.id}, brand_id={brand_id}, external_id={user.incust_external_id}")
            return incust_customer
        else:
            try:
                new_customer = await IncustCustomer.create(
                    user_or_id=user.id,
                    brand_or_id=brand_id,
                    token=token_response.token,
                    push_token=push_token,
                    push_token_wl_id=loyalty_settings.white_label_id if push_token else None
                )
                logger.info(f"Created incust_customer: user_id={user.id}, brand_id={brand_id}, external_id={user.incust_external_id}")
                return new_customer

            except IntegrityError:
                # Можливо, запис вже створено іншим процесом
                await async_rollback()
                existing_customer = await IncustCustomer.get(
                    user_id=user.id,
                    brand_id=brand_id,
                )
                if existing_customer:
                    update_data = {
                        "token": token_response.token,
                        "token_update_datetime": utcnow()
                    }
                    if push_token:
                        update_data.update({
                            "push_token": push_token,
                            "push_token_wl_id": loyalty_settings.white_label_id,
                            "push_token_update_datetime": utcnow()
                        })
                    await existing_customer.update(update_data)
                    logger.info(f"Updated incust_customer after IntegrityError: user_id={user.id}, brand_id={brand_id}, external_id={user.incust_external_id}")
                    return existing_customer
                else:
                    raise Exception(
                        f"Failed to create or find IncustCustomer for user {user.id}"
                    )

    except Exception as ex:
        logger.error(f"Failed to get or create InCust customer for user {user.id}: {ex}")
        raise


async def get_brand_id_from_loyalty_settings(loyalty_settings: LoyaltySettings):
    logger = JSONLogger(
        "loyalty.customer_service.get_brand_id",
        {
            "loyalty_settings": loyalty_settings.dict() if loyalty_settings else None,
        }
    )
    
    brand_id = loyalty_settings.brand_id
    if brand_id:
        return brand_id

    if loyalty_settings.profile_id:
        from db.models import Group
        group = await Group.get(loyalty_settings.profile_id)
        if group:
            from db.models import Brand
            brand = await Brand.get(group_id=group.id)
            if brand:
                brand_id = brand.id
    elif loyalty_settings.store_id:
        from db.models import Store
        store = await Store.get(loyalty_settings.store_id)
        if store:
            brand_id = store.brand_id
    elif loyalty_settings.product_id:
        from db.models import StoreProduct
        product = await StoreProduct.get(loyalty_settings.product_id)
        if product:
            from db.models import Store
            store = await Store.get(product.store_id)
            if store:
                brand_id = store.brand_id
    elif loyalty_settings.invoice_template_id:
        from db.models import InvoiceTemplate
        invoice_template = await InvoiceTemplate.get(loyalty_settings.invoice_template_id)
        if invoice_template:
            brand = await crud.get_brand_by_group(invoice_template.group_id)
            if brand:
                brand_id = brand.id
    elif loyalty_settings.ewallet_id:
        from db.models import EWallet
        ewallet = await EWallet.get(loyalty_settings.ewallet_id)
        if ewallet and ewallet.bot_id:
            from db.models import ClientBot
            bot = await ClientBot.get(ewallet.bot_id)
            if bot and bot.group_id:
                from db.models import Brand
                brand = await Brand.get(group_id=bot.group_id)
                if brand:
                    brand_id = brand.id
    return brand_id
