from core import messangers_adapters as ma
from core.brand.functions import auto_create_brand
from core.fast_pay.deep_links import PayEnteredAmountDeepLink, PayTemplateDeepLink
from core.fast_pay.keyboards import get_tg_fast_pay_keyboard
from core.keyboards import get_bot_menu_keyboard
from core.topup_ewallet.callback_data import TopUpEwalletCallbackData
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db.models import Brand, ClientBot, Group, User
from utils.text import f, fd


async def send_web_fast_pay_menu(
        message: ma.Message,
        brand: Brand,
        user: User,
        bot: ClientBot,
        lang: str,
        header: str,
        body: str,
        params: dict,
        is_entered_amount: bool = False,
        invoice_template_id: int | None = None,
):
    bot_type = await ma.detect_bot_type(message)

    if is_entered_amount:
        path = "fastpay/enteramount"
    else:
        path = f"fastpay/{invoice_template_id}"

    match bot_type:
        case "telegram":
            url = brand.get_url(path, **params, bot_id=bot.id)
            text = f"<b>{header}</b>\n\n{body}"
            keyboard = await get_tg_fast_pay_keyboard(url, lang)
            await message.answer(text, reply_markup=keyboard)
        case "whatsapp":
            url = await brand.get_short_token_url(
                user, bot.id, lang, path, **params
            )

            body += f"\n\n{url}"

            keyboard = await get_wa_menu_keyboard(user, bot, lang)
            await message.answer_interactive_list(
                header=header,
                body=body,
                button=keyboard.button,
                sections=keyboard.sections,
            )


async def send_fast_pay_error(
        message: ma.Message,
        error: str,
        user: User,
        bot: ClientBot,
        lang: str,
        is_ewallet: bool | None = None,
):
    texts = await fd(
        {
            "header": "fast pay invalid link error header",
            "body": "fast pay invalid link error body",
        }, lang
    )

    header = texts["header"].format(error=error) if not is_ewallet else None
    body = texts["body"].format(error=error) if not is_ewallet else error

    keyboard = await get_bot_menu_keyboard(user, bot, lang)

    bot_type = await ma.detect_bot_type(message)
    match bot_type:
        case "telegram":
            text = "<b>{header}</b>\n\n{body}".format(
                header=header,
                body=body,
            ) if not is_ewallet else body
            await message.answer(text, reply_markup=keyboard)
        case "whatsapp":
            await message.answer_interactive_list(
                header=header,
                body=body,
                button=keyboard.button,
                sections=keyboard.sections,
            )


async def fast_pay_get_and_check_brand(
        message: ma.Message,
        data: PayEnteredAmountDeepLink | PayTemplateDeepLink | TopUpEwalletCallbackData,
        user: User,
        bot: ClientBot,
        lang: str,
):
    brand = await Brand.get(group_id=data.group_id)
    if not brand:
        group = await Group.get(data.group_id)
        if not group:
            error = await f(
                "fast pay group not found error",
                lang, group_id=data.group_id,
            )
            await send_fast_pay_error(message, error, user, bot, lang)
            return None

        brand = await auto_create_brand(group)
    return brand
