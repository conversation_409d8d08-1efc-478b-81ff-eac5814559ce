from typing import Literal

from config import WEB_APP_PATH
from db import crud
from schemas import Bot<PERSON><PERSON><PERSON>iteral
from .deep_links import PayEnteredAmountDeepLink, PayTemplateDeepLink


class FastPayLinkGenerator:

    def __init__(
            self, group_id: int,
            lang: str, mode: str,
            invoice_template_id: int | None = None,
            entered_amount: float | None = None,
            bot_type: BotTypeLiteral | None = None,
            bot_id_name: str | None = None,
            nl_key: str | None = None,
            external_transaction_id: str | None = None,
            client_redirect_url: str | None = None,
    ):
        self.group_id: int = group_id
        self.lang = lang
        self.mode: str = mode
        self.invoice_template_id: int | None = invoice_template_id
        self.entered_amount: float | None = entered_amount
        self.bot_type: BotTypeLiteral | None = bot_type
        self.bot_id_name: str | None = bot_id_name
        self.nl_key: str | None = nl_key
        self.external_transaction_id: str | None = external_transaction_id
        self.client_redirect_url: str | None = client_redirect_url

    async def check_template_amount(self):
        if not self.invoice_template_id:
            return

        if await crud.get_invoice_template_items(self.invoice_template_id, "exists"):
            self.entered_amount = None

    async def get_shop_base_link(self) -> str | None:
        brand = await crud.get_brand_by_group(self.group_id)
        if not brand:
            return None

        if brand.domain:
            domain = brand.domain
        else:
            domain = WEB_APP_PATH

        if not domain.endswith("/"):
            domain += "/"
        return domain + "fastpay"

    async def make_link_web(self) -> str | None:
        await self.check_template_amount()
        link = await self.get_shop_base_link()
        if not link:
            return None

        if self.mode == "entered_amount":
            link += "enteramount"
        else:
            link += f"/{self.invoice_template_id}"

        params = {}

        if self.entered_amount:
            params["entered_amount"] = self.entered_amount

        if self.nl_key:
            params["nl_key"] = self.nl_key

        if self.external_transaction_id:
            params["external_id"] = self.external_transaction_id

        if self.client_redirect_url:
            params["client_redirect_url"] = self.client_redirect_url

        if params:
            params_str = "&".join([f"{key}={value}" for key, value in params.items()])
            link += f"?{params_str}"

        return link

    async def make_link_bot(self) -> str | None:
        await self.check_template_amount()
        if self.bot_type is None or not self.bot_id_name:
            return None

        if self.invoice_template_id:
            deep_link = PayTemplateDeepLink(
                group_id=str(self.group_id),
                invoice_template_id=self.invoice_template_id,
                entered_amount=self.entered_amount,
            )

        else:
            deep_link = PayEnteredAmountDeepLink(
                group_id=str(self.group_id),
                entered_amount=self.entered_amount,
            )

        return deep_link.to_str(self.bot_type, self.bot_id_name)

    async def make_link(self, show_mode: Literal["web", "bot"]) -> str | None:
        if show_mode == "web":
            return await self.make_link_web()
        return await self.make_link_bot()
