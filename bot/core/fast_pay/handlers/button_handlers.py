import asyncio

from core import messangers_adapters as ma
from core.fast_pay.callback_data import (
    BonusChoiceCallbackData,
    PaymentMethodCallbackData,
)
from core.fast_pay.functions import fast_pay_get_and_check_brand, send_fast_pay_error
from core.fast_pay.processor import FastPayProcessor
from core.fast_pay.processor.state import FastPay
from core.topup_ewallet.callback_data import TopUpEwalletCallbackData
from db import crud, own_session
from db.models import ClientBot, EWallet, InvoiceTemplate, User
from schemas import FastPayStateData, InvoiceTemplatePaymentModeEnum
from utils.text import f


@ma.handler.button("reply", "list")
async def topup_ewallet_button_handler(
        message: ma.Message,
        data: TopUpEwalletCallbackData,
        user: User,
        bot: ClientBot,
        state: ma.FSMContext,
        lang: str,
):
    brand = await fast_pay_get_and_check_brand(message, data, user, bot, lang)

    if not brand:
        return

    ewallet = await EWallet.get(id=data.ewallet_id, is_enabled=True, is_deleted=False)
    if not ewallet:
        error = await f(
            "fast pay ewallet not found error",
            lang, ewallet_id=data.ewallet_id,
        )
        return await send_fast_pay_error(message, error, user, bot, lang, True)

    if not ewallet.invoice_template_id:
        error = await f(
            "fast pay ewallet invoice template not found error",
            lang, ewallet_id=ewallet.id,
        )
        return await send_fast_pay_error(message, error, user, bot, lang, True)

    invoice_template = await InvoiceTemplate.get(ewallet.invoice_template_id)
    if not invoice_template:
        error = await f(
            "fast pay invoice template not found error",
            lang, invoice_template_id=ewallet.invoice_template_id,
        )
        return await send_fast_pay_error(message, error, user, bot, lang)

    entered_amount = None if not data.used_credit else data.used_credit
    if not data.used_credit and invoice_template.items:
        entered_amount = sum([item.price * item.quantity for item in invoice_template.items])

    processor = await FastPayProcessor.setup(
        message,
        state,
        user,
        lang,
        FastPayStateData(
            group_id=data.group_id,
            invoice_template_id=invoice_template.id,
            entered_amount=entered_amount,
            is_amount_fixed=bool(entered_amount and not data.used_credit),
            ewallet_id=data.ewallet_id,
            used_credit=data.used_credit,
        ),
        invoice_template,
    )
    await FastPay.PaymentData.set()
    await processor.send_state_menu()


@ma.handler.button("reply", "list")
async def payment_method_button_handler(
        query: ma.ButtonQuery,
        state: ma.FSMContext,
        payment_data: PaymentMethodCallbackData,
        bot: ClientBot,
        user: User,
        lang: str
):
    asyncio.ensure_future(make_payment(
        query, state, payment_data, bot.id, user.id, lang
        )
    )


@own_session
async def make_payment(
        query: ma.ButtonQuery,
        state: ma.FSMContext,
        payment_data: PaymentMethodCallbackData,
        bot_id: int,
        user_id: int,
        lang: str
):
    user = await User.get_by_id(user_id)
    bot = await ClientBot.get(bot_id)
    processor = await FastPayProcessor.setup(query, state, user, lang)
    await processor.handle_selected_payment_method(payment_data, bot)


@ma.handler.button("reply", "list")
async def submit_count_button_handler(
        query: ma.ButtonQuery,
        state: ma.FSMContext,
        user: User,
        lang: str
):
    processor = await FastPayProcessor.setup(query, state, user, lang)

    # Отримуємо базову суму для розрахунку лояльності
    data = await processor.data
    items = await crud.get_invoice_template_items(
        (await processor.invoice_template).id
    )
    total = sum(
        item.price * item.quantity for item in items
    ) * data.count / 100

    await processor.update_data(
        entered_bonus_amount=None,
        invoice_id=None,
        payment_settings_id=None,
        object_payment_settings_id=None,
        loyalty_calculated=False,
        discount_amount=None,
        amount_after_discount=None,
        items_displayed=False,
        bonus_action=None,
        is_fully_covered=False,
    )

    if await processor.check_and_process_bonuses(total):
        return

    # Якщо немає бонусів, переходимо до підтвердження
    await FastPay.Confirmation.set()
    await processor.send_state_menu()


@ma.handler.button("reply", "list")
async def bonus_choice_button_handler(
        query: ma.ButtonQuery,
        state: ma.FSMContext,
        payment_data: BonusChoiceCallbackData,
        user: User,
        lang: str
):
    processor = await FastPayProcessor.setup(query, state, user, lang)
    data = await processor.data
    bonus_amount = 0

    match payment_data.bonus_action:
        case "use_all_bonuses":
            # Беремо мінімальне значення між доступними бонусами і максимально можливою сумою для чеку
            bonus_amount = min(
                data.user_available_bonuses or 0,
                data.max_bonuses_amount or 0
            )

            await processor.update_data(
                entered_bonus_amount=bonus_amount,
                bonus_action="use_all_bonuses",
                loyalty_calculated=False,
            )

        case "next":
            await processor.update_data(
                bonus_action="skip",
                entered_bonus_amount=0,
                loyalty_calculated=False,

            )

    if data.amount_after_discount == 0 or data.amount_after_discount <= bonus_amount:
        await processor.update_data(is_fully_covered=True)
        await state.set_state(FastPay.ConfirmFullBonusPayment)
        await processor.send_full_bonus_payment_menu()
    else:
        await FastPay.Confirmation.set()
        await processor.send_state_menu()


@ma.handler.button("reply", "list")
async def submit_bonus_menu_handler(
        query: ma.ButtonQuery,
        state: ma.FSMContext,
        user: User,
        lang: str
):
    processor = await FastPayProcessor.setup(query, state, user, lang)
    await processor.send_state_menu()


@ma.handler.button("reply", "list")
async def back_to_bonuses_handler(
        query: ma.ButtonQuery,
        state: ma.FSMContext,
        user: User,
        lang: str,
):
    processor = await FastPayProcessor.setup(query, state, user, lang)
    data = await processor.data

    await processor.update_data(
        entered_bonus_amount=None,
        bonus_action=None,
        loyalty_calculated=False,
        max_bonuses_amount=data.initial_max_bonuses,
        payment_settings_id=None,
    )

    await FastPay.EnterBonusAmount.set()
    await processor.send_state_menu()


@ma.handler.button("reply", "list")
async def confirm_full_bonus_payment_handler(
        query: ma.ButtonQuery,
        state: ma.FSMContext,
        user: User,
        lang: str,
):
    processor = await FastPayProcessor.setup(query, state, user, lang)
    await processor.update_data(is_fully_covered=True)
    await processor.handle_full_bonus_payment()


@ma.handler.button("reply", "list")
async def back_from_full_bonus_handler(
        query: ma.ButtonQuery,
        state: ma.FSMContext,
        user: User,
        lang: str,
):
    processor = await FastPayProcessor.setup(query, state, user, lang)
    data = await processor.data

    await processor.update_data(
        entered_bonus_amount=None,
        bonus_action=None,
        loyalty_calculated=False,
        max_bonuses_amount=data.initial_max_bonuses,
    )

    await FastPay.EnterBonusAmount.set()
    await processor.send_state_menu()


@ma.handler.button("reply", "list")
async def back_to_payment_data_handler(
        query: ma.ButtonQuery,
        state: ma.FSMContext,
        user: User,
        lang: str,
):
    processor = await FastPayProcessor.setup(query, state, user, lang)

    data = await processor.data
    invoice_template = await processor.invoice_template

    # Перевіряємо чи дійсно можна повернутися до редагування
    can_go_back = (
            (
                        invoice_template.payment_mode == InvoiceTemplatePaymentModeEnum.ENTERED_AMOUNT
                        and not data.is_amount_fixed)
            or
            (invoice_template.payment_mode == InvoiceTemplatePaymentModeEnum.ITEMS
             and not invoice_template.disabled_qty)
    )
    if not can_go_back:
        return

    # Скидаємо дані про бонуси
    await processor.update_data(
        entered_amount=None,
        entered_bonus_amount=None,
        bonus_action=None,
        loyalty_calculated=False,
        items_displayed=False,
    )

    await FastPay.PaymentData.set()
    await processor.send_payment_data_menu()


def register_payment_method_button_handlers(dp: ma.DispatcherType):

    payment_method_button_handler.setup(
        dp,
        PaymentMethodCallbackData.get_filter("payment_data"),
        state=[FastPay.PaymentData, FastPay.Confirmation],
    )

    submit_count_button_handler.setup(
        dp,
        state=[FastPay.PaymentData, FastPay.Confirmation],
        messangers_kwargs={
            "telegram": {
                "callback_mode": "submit_count",
            },
            "whatsapp": {
                "reply_mode": "submit_count"
            }
        }
    )

    bonus_choice_button_handler.setup(
        dp,
        BonusChoiceCallbackData.get_filter("payment_data"),
        state=[FastPay.EnterBonusAmount],
        messangers_kwargs={
            "telegram": {
                "callback_mode": "bonus_choice",
            },
            "whatsapp": {
                "reply_mode": "bonus_choice"
            }
        }
    )

    topup_ewallet_button_handler.setup(
        dp,
        TopUpEwalletCallbackData.get_filter("data"),
        state="*",
        messangers_kwargs={
            "telegram": {
                "callback_mode": "topup_ewallet",
            },
            "whatsapp": {
                "reply_mode": "topup_ewallet"
            }
        }
    )

    submit_bonus_menu_handler.setup(
        dp,
        state=[FastPay.EnterBonusAmount],
        messangers_kwargs={
            "telegram": {
                "callback_mode": "submit_bonus_menu",
            },
            "whatsapp": {
                "reply_mode": "submit_bonus_menu"
            }
        }
    )

    back_to_bonuses_handler.setup(
        dp,
        state="*",
        messangers_kwargs={
            "telegram": {
                "callback_mode": "back_to_bonuses",
            },
            "whatsapp": {
                "reply_mode": "back_to_bonuses"
            }
        }
    )

    confirm_full_bonus_payment_handler.setup(
        dp,
        state="*",
        messangers_kwargs={
            "telegram": {
                "callback_mode": "confirm_full_bonus_payment",
            },
            "whatsapp": {
                "reply_mode": "confirm_full_bonus_payment"
            }
        }
    )

    back_from_full_bonus_handler.setup(
        dp,
        state="*",
        messangers_kwargs={
            "telegram": {
                "callback_mode": "back_from_full_bonus",
            },
            "whatsapp": {
                "reply_mode": "back_from_full_bonus"
            }
        }
    )

    back_to_payment_data_handler.setup(
        dp,
        state="*",
        messangers_kwargs={
            "telegram": {
                "callback_mode": "back_to_payment_data",
            },
            "whatsapp": {
                "reply_mode": "back_to_payment_data"
            }
        }
    )
