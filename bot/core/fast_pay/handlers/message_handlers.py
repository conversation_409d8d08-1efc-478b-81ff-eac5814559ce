import asyncio

from psutils.convertors import str_to_float, str_to_int

import exceptions
from core import messangers_adapters as ma
from core.fast_pay.callback_data import PaymentMethodCallbackData
from core.fast_pay.processor import FastPayProcessor
from core.fast_pay.processor.helpers import HandleFormatError
from core.fast_pay.processor.state import FastPay
from db import own_session
from db.models import ClientBot, User
from schemas import InvoiceTemplatePaymentModeEnum
from utils.text import f


@ma.handler.message()
async def handle_payment_data(
        message: ma.Message,
        state: ma.FSMContext,
        user: User,
        lang: str,
):
    processor = await FastPayProcessor.setup(message, state, user, lang)
    data = await processor.data

    current_state = await state.get_state()
    if (current_state == FastPay.Confirmation.state and
        data.is_loyalty and
        not data.incust_prohibit_redeeming_bonuses
        and data.max_bonuses_amount
        and data.user_available_bonuses > 0
        and data.is_amount_fixed
        ):
        return

    invoice_template = await processor.invoice_template

    async with HandleFormatError(message, lang):
        if (invoice_template.payment_mode ==
                InvoiceTemplatePaymentModeEnum.ENTERED_AMOUNT):

            if data.is_amount_fixed:
                raise exceptions.FastPayNotButtonClickError()

            entered_amount = str_to_float(
                message.text or message.caption or "",
                only_positive=True
            )
            # Reset loyalty calculation when amount changes
            await processor.update_data(
                entered_amount=entered_amount,
                invoice_id=None,
                payment_settings_id=None,
                object_payment_settings_id=None,
                loyalty_calculated=False,  # Reset loyalty calculation
                entered_bonus_amount=None,
                bonus_action=None,
                discount_amount=None,
                amount_after_discount=None,
            )
            if await processor.check_and_process_bonuses(entered_amount):
                return
            await FastPay.Confirmation.set()
        elif (
                invoice_template.payment_mode ==
                InvoiceTemplatePaymentModeEnum.ITEMS
        ):
            if invoice_template.disabled_qty:
                raise exceptions.FastPayNotButtonClickError()

            count = str_to_int(
                message.text or message.caption or "", only_positive=True
            ) or 1
            await processor.update_data(
                count=count,
                invoice_id=None,
                payment_settings_id=None,
                object_payment_settings_id=None,
                loyalty_calculated=False,  # Reset loyalty calculation
                discount_amount=None,
                amount_after_discount=None,
                items_displayed=False,
                bonus_action=None,
                entered_bonus_amount=None,
            )
            await FastPay.PaymentData.set()

        await processor.send_state_menu()

@ma.handler.message()
async def handle_bonus_amount(
        message: ma.Message,
        state: ma.FSMContext,
        user: User,
        lang: str,
):
    current_state = await state.get_state()

    # Якщо ми в стані підтвердження повної оплати - ігноруємо введення
    if current_state == FastPay.ConfirmFullBonusPayment.state:
        return

    processor = await FastPayProcessor.setup(message, state, user, lang)
    data = await processor.data

    async with HandleFormatError(message, lang):
        entered_amount = str_to_float(
            message.text or message.caption or "",
            only_positive=True
        )

        bounded_amount = min(
            entered_amount,
            data.max_bonuses_amount or 0,
            data.user_available_bonuses or 0
        )

        await processor.update_data(
            entered_bonus_amount=bounded_amount,
            bonus_action="use_entered_amount",
            loyalty_calculated=False
        )

        if data.amount_after_discount <= bounded_amount:
            await processor.update_data(is_fully_covered=True)
            await state.set_state(FastPay.ConfirmFullBonusPayment)
            await processor.send_full_bonus_payment_menu()
        else:
            await FastPay.Confirmation.set()
            await processor.send_state_menu()

@ma.handler.message()
async def handle_confirm_full_bonus_payment(message: ma.Message):
    return  # ігноруємо всі повідомлення в цьому стані


@ma.handler.message()
async def handle_phone_number(
        message: ma.Message,
        state: ma.FSMContext,
        user: User,
        bot: ClientBot,
        lang: str,
):
    raw_phone_number = message.text.strip() if message.text else ""
    phone_number = ''.join(c for c in raw_phone_number if c not in ' ()+-')

    if not phone_number or len(phone_number) < 9 or not phone_number.isdigit():
        text = await f("CLENT_BOT_INVALID_PHONE_NUMBER_TEXT", lang)
        return await message.answer(text)

    asyncio.ensure_future(make_payment_with_phone(message, state, user.id, bot.id, lang, phone_number,))


@own_session
async def make_payment_with_phone(
        message: ma.Message,
        state: ma.FSMContext,
        user_id: int,
        bot_id: int,
        lang: str,
        phone_number
):
    user = await User.get_by_id(user_id)
    bot = await ClientBot.get(bot_id)
    processor = await FastPayProcessor.setup(message, state, user, lang)
    data = await processor.data
    await processor.update_data(user_phone=phone_number)
    # Повертаємось до стандартного процесу оплати
    # Відновлюємо стан підтвердження
    await FastPay.Confirmation.set()
    # Викликаємо обробник вибору методу оплати з тими ж параметрами, що були збережені
    callback_data = PaymentMethodCallbackData(
        payment_settings_id=data.payment_settings_id,
        object_payment_settings_id=data.object_payment_settings_id,
    )
    await processor.handle_selected_payment_method(callback_data, bot)


def register_fast_pay_message_handlers(dp: ma.DispatcherType):
    handle_payment_data.setup(
        dp,
        state=[FastPay.PaymentData, FastPay.Confirmation],
    )

    handle_bonus_amount.setup(
        dp,
        state=[FastPay.EnterBonusAmount],
    )

    handle_confirm_full_bonus_payment.setup(
        dp,
        state=[FastPay.ConfirmFullBonusPayment],
    )

    handle_phone_number.setup(
        dp,
        state=[FastPay.EnterPhoneNumber],
    )
