from core import messangers_adapters as ma
from core.fast_pay.deep_links import PayEnteredAmountDeepLink, PayTemplateDeepLink
from core.fast_pay.functions import (
    fast_pay_get_and_check_brand, send_fast_pay_error, send_web_fast_pay_menu,
)
from core.fast_pay.processor import FastPayProcessor
from core.fast_pay.processor.state import FastPay
from db.models import ClientBot, InvoiceTemplate, User
from schemas import FastPayStateData
from utils.text import f


@ma.handler.message()
async def pay_entered_amount_deep_link_handler(
        message: ma.Message,
        data: PayEnteredAmountDeepLink,
        user: User,
        bot: ClientBot,
        lang: str,
):
    brand = await fast_pay_get_and_check_brand(message, data, user, bot, lang)
    if not brand:
        return

    bot_type = await ma.detect_bot_type(message)

    header = brand.name
    body = await f(f"fast pay bot {bot_type} open text", lang)

    params = {}
    if data.entered_amount:
        params["entered_amount"] = data.entered_amount

    await send_web_fast_pay_menu(
        message, brand,
        user, bot, lang,
        header, body,
        params,
    )


@ma.handler.message()
async def pay_template_deep_link_handler(
        message: ma.Message,
        data: PayTemplateDeepLink,
        user: User,
        bot: ClientBot,
        state: ma.FSMContext,
        lang: str,
):
    brand = await fast_pay_get_and_check_brand(message, data, user, bot, lang)
    if not brand:
        return None

    invoice_template = await InvoiceTemplate.get(data.invoice_template_id)
    if not invoice_template:
        error = await f(
            "fast pay invoice template not found error",
            lang, invoice_template_id=data.invoice_template_id,
        )
        return await send_fast_pay_error(message, error, user, bot, lang)

    processor = await FastPayProcessor.setup(
        message,
        state,
        user,
        lang,
        FastPayStateData(
            group_id=data.group_id,
            invoice_template_id=invoice_template.id,
            entered_amount=data.entered_amount,
            is_amount_fixed=bool(data.entered_amount),
        ),
        invoice_template,
    )
    await FastPay.PaymentData.set()
    await processor.send_state_menu()
    return None


def register_fast_pay_deep_link_handlers(dp: ma.DispatcherType):
    pay_entered_amount_deep_link_handler.setup(
        dp,
        PayEnteredAmountDeepLink.get_filter(return_field_name="data"),
        state="*",
    )

    pay_template_deep_link_handler.setup(
        dp,
        PayTemplateDeepLink.get_filter(return_field_name="data"),
        state="*",
    )
