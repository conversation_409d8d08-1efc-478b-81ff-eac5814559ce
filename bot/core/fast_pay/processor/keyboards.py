import math
from typing import Literal

import schemas
from config import NO_CENT_CURRENCIES
from core import messangers_adapters as ma
from core.fast_pay.callback_data import (
    BonusChoiceCallbackData,
    PaymentMethodCallbackData,
)
from core.fast_pay.processor.helpers import calculate_payment_method_fee
from db.models import Group
from utils.numbers import format_currency
from utils.text import f, html_to_markdown


async def get_submit_count_keyboard(lang: str):
    return ma.InlineKeyboard(
        buttons=[
            ma.InlineKeyboardButton(
                await f("fast pay continue button", lang),
                data="submit_count"
            )
        ]
    )


async def get_wa_payment_methods_keyboard(
        payment_methods: schemas.AvailablePaymentSchema,
        amount_to_pay: float,
        currency: str,
        group: Group,
        lang: str,
        show_back_button: bool = False
):
    pay_button = await f("fast pay pay button", lang)

    if payment_method := payment_methods.single_payment_method:
        return ma.wa.types.ReplyKeyboard(
            buttons=[
                ma.wa.types.ReplyButton(
                    id=PaymentMethodCallbackData(
                        payment_settings_id=payment_method.settings_id,
                        object_payment_settings_id=payment_method.object_settings_id,
                    ).to_str(),
                    title=pay_button,
                )
            ]
        )

    keyboard = ma.wa.types.ListKeyboard(
        button=pay_button
    )

    if show_back_button:
        keyboard.add_rows(
            ma.wa.types.SectionRow(
                id="back_to_bonuses",
                title=await f("web app back", lang)
            )
        )

    max_methods = 8 if show_back_button else 9

    for i, payment_method in enumerate(payment_methods.methods):
        if i > max_methods:
            break
        fee_amount = calculate_payment_method_fee(payment_method, amount_to_pay)

        if fee_amount:
            if currency in NO_CENT_CURRENCIES:
                fee_amount = math.ceil(fee_amount)

            fee_amount_formatted = format_currency(
                fee_amount, currency,
                group.lang, group.country_code,
            )
            fee_text = (
                    await f(
                        "payer fee receipt list order text", lang
                    ) +
                    f": {fee_amount_formatted}"
            )
            description = fee_text
            if payment_method.desc:
                description += f"\n{payment_method.desc}"
        else:
            description = payment_method.desc

        if description:
            description = description.strip()

        keyboard.add_rows(
            ma.wa.types.SectionRow(
                id=PaymentMethodCallbackData(
                    payment_settings_id=payment_method.settings_id,
                    object_payment_settings_id=payment_method.object_settings_id,
                ).to_str(),
                title=payment_method.name,
                description=description,
            )
        )

    return keyboard


async def get_tg_payment_methods_keyboard(
        payment_methods: schemas.AvailablePaymentSchema,
        lang: str,
        show_back_button: bool = False
):
    keyboard = ma.tg.types.InlineKeyboardMarkup()

    if show_back_button:
        keyboard.row(
            ma.tg.types.InlineKeyboardButton(
                await f("web app back", lang),
                callback_data="back_to_bonuses"
            )
        )

    max_methods = 8 if show_back_button else 9

    if payment_method := payment_methods.single_payment_method:
        keyboard.insert(
            ma.tg.types.InlineKeyboardButton(
                text=await f("fast pay pay button", lang),
                callback_data=PaymentMethodCallbackData(
                    payment_settings_id=payment_method.settings_id,
                    object_payment_settings_id=payment_method.object_settings_id,
                ).to_str(),
            )
        )
        return keyboard

    for i, payment_method in enumerate(payment_methods.methods):
        if i > max_methods:
            break
        keyboard.row(
            ma.tg.types.InlineKeyboardButton(
                payment_method.name,
                callback_data=PaymentMethodCallbackData(
                    payment_settings_id=payment_method.settings_id,
                    object_payment_settings_id=payment_method.object_settings_id,
                ).to_str()
            )
        )

    return keyboard


async def send_bonus_choice_message(
        bot_type: str,
        lang: str,
        max_bonuses_amount: str,
        show_back_button: bool,
        answer_obj,
        body_text: str
):

    # Get the full text first
    bonus_button_text = await f(
        "fast pay use all bonuses button",
        lang,
        max_bonuses_amount=max_bonuses_amount,
    )
    # If the text is too long, use the short version
    if len(bonus_button_text) > 20:
        bonus_button_text = await f(
            "fast pay use all bonuses button short",
            lang,
        )

    match bot_type:
        case "telegram":
            keyboard = ma.tg.types.InlineKeyboardMarkup()

            if show_back_button:
                keyboard.row(
                    ma.tg.types.InlineKeyboardButton(
                        await f("web app back", lang),
                        callback_data="back_to_payment_data"
                    )
                )

            keyboard.row(
                ma.tg.types.InlineKeyboardButton(
                    bonus_button_text,
                    callback_data=BonusChoiceCallbackData(
                        bonus_action="use_all_bonuses"
                    ).to_str()
                )
            )

            keyboard.row(
                ma.tg.types.InlineKeyboardButton(
                    await f("FAST_PAY_SKIP_BONUSES_BUTTON", lang),
                    callback_data=BonusChoiceCallbackData(
                        bonus_action="next"
                    ).to_str()
                )
            )

            await answer_obj.answer(body_text, reply_markup=keyboard)

        case "whatsapp":
            keyboard = ma.wa.types.ListKeyboard(
                button=await f("fast pay available bonuses button", lang)
            )

            if show_back_button:
                keyboard.add_rows(
                    ma.wa.types.SectionRow(
                        id="back_to_payment_data",
                        title=await f("web app back", lang)
                    )
                )

            keyboard.add_rows(
                ma.wa.types.SectionRow(
                    id=BonusChoiceCallbackData(
                        bonus_action="use_all_bonuses"
                    ).to_str(),
                    title=bonus_button_text,
                )
            )

            keyboard.add_rows(
                ma.wa.types.SectionRow(
                    id=BonusChoiceCallbackData(
                        bonus_action="next"
                    ).to_str(),
                    title=await f("FAST_PAY_SKIP_BONUSES_BUTTON", lang),
                )
            )

            body_md = html_to_markdown(body_text)
            # Обмеження довжини тексту повідомлення до 1024 символів (максимум для WhatsApp API)
            if len(body_md) > 1024:
                body_md = body_md[:1021] + '...'
            
            await keyboard.sending_method(
                to=answer_obj.user.id,
                body=body_md,
                **keyboard.to_kwargs()
            )


async def get_full_bonus_payment_keyboard(bot_type: str, lang: str):
    """Повертає клавіатуру для підтвердження повної оплати бонусами"""
    back_text = await f("web app back", lang)
    confirm_text = await f("fast pay confirm payment bonuses button", lang)

    match bot_type:
        case "telegram":
            keyboard = ma.tg.types.InlineKeyboardMarkup()
            keyboard.row(
                ma.tg.types.InlineKeyboardButton(
                    back_text,
                    callback_data="back_from_full_bonus"
                ),
                ma.tg.types.InlineKeyboardButton(
                    confirm_text,
                    callback_data="confirm_full_bonus_payment"
                )
            )
        case "whatsapp":
            keyboard = ma.wa.types.ListKeyboard(
                button=confirm_text
            )
            keyboard.add_rows(
                ma.wa.types.SectionRow(
                    id="back_from_full_bonus",
                    title=back_text
                )
            )
            keyboard.add_rows(
                ma.wa.types.SectionRow(
                    id="confirm_full_bonus_payment",
                    title=confirm_text
                )
            )
        case _:
            raise ValueError(f"Unsupported bot type: {bot_type}")

    return keyboard


async def get_keyboard_for_payment_provider_message(bot_type: Literal["telegram", "whatsapp"], lang, data):
    keyboard = None
    if data.is_loyalty and data.user_available_bonuses:
        back_button_text = await f("web app back", lang)

        match bot_type:
            case "telegram":
                keyboard = ma.tg.types.InlineKeyboardMarkup()
                keyboard.row(
                    ma.tg.types.InlineKeyboardButton(
                        back_button_text,
                        callback_data="back_to_bonuses"
                    )
                )
            case "whatsapp":
                keyboard = ma.wa.types.ListKeyboard(button=back_button_text)
                keyboard.add_rows(
                    ma.wa.types.SectionRow(
                        id="back_to_bonuses",
                        title=back_button_text,
                    )
                )
    return keyboard