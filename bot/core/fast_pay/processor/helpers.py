import logging
from typing import Type

from psutils.exceptions import ErrorWithTextVariable

from core import messangers_adapters as ma
from core.keyboards import get_to_main_menu_keyboard
from schemas import PaymentMethodSchema
from utils.text import f


class HandleFormatError:

    def __init__(self, answer_obj: ma.AnswerObject, lang: str):
        self.answer_obj = answer_obj
        self.lang = lang

    async def __aenter__(self):
        return self

    async def __aexit__(
            self,
            exc_type: Type[Exception] | None,
            exc_val: Exception | None,
            exc_tb
    ):
        if not exc_val:
            return True

        logging.getLogger("error.fastpay").error(
            exc_val, exc_info=(exc_type, exc_val, exc_tb)
        )

        if isinstance(exc_val, ErrorWithTextVariable):
            error_text = await f(
                exc_val.text_variable, self.lang,
                **exc_val.text_kwargs
            )
        else:
            error_text = await f("bot unknown error message", self.lang)

        footer_text = await f("fast pay cancel process footer text", self.lang)

        bot_type = await ma.detect_bot_type(self.answer_obj)

        keyboard = (
            await get_to_main_menu_keyboard(self.lang)
        ).to_messanger(bot_type)

        match bot_type:
            case "whatsapp":
                await self.answer_obj.answer_interactive_button(
                    error_text,
                    **keyboard.to_kwargs(),
                    footer=footer_text,
                )
            case "telegram":
                await self.answer_obj.answer(
                    f"{error_text}\n><i>{footer_text}</i>",
                    reply_markup=keyboard,
                )

        return True


def calculate_payment_method_fee(
        payment_method: PaymentMethodSchema,
        amount_to_pay: float,
):
    return (
            (payment_method.fee_value or 0) +
            (amount_to_pay / 100 * (payment_method.fee_percent or 0))
    )
