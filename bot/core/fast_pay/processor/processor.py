import logging
import math
from typing import TypedDict

from aiogram.types import InlineKeyboardMarkup
from aiowhatsapp.types import ListKeyboard
from incust_api.api import term

import exceptions
import schemas
from config import NO_CENT_CURRENCIES
from core import messangers_adapters as ma
from core.bot.ewallet_handlers import process_single_ewallet
from core.ewallet.payment.ewallet_payment_processer import EwalletPaymentProcessor
from core.fast_pay.callback_data import (
    PaymentMethodCallbackData,
)
from core.fast_pay.processor.keyboards import (
    get_full_bonus_payment_keyboard, get_submit_count_keyboard,
    get_tg_payment_methods_keyboard, get_wa_payment_methods_keyboard,
    send_bonus_choice_message,
)
from core.fast_pay.processor.state import FastPay
from core.incust.helpers import make_incust_check_items_for_invoice
from core.invoice import InvoiceService, finish_process_full_bonus_payment
from core.invoice.exception import InvoicePaymentInvoicePayedError
from core.invoice.functions import calc_extra_fee
from core.loyalty.customer_service import get_or_create_incust_customer
from core.loyalty.incust_api import incust
from core.payment.payment_methods import (
    get_available_payment_methods, get_payment_method_name,
)
from core.payment.payments_service import PaymentsService
from core.shortener import create_short_link
from core.whatsapp.keyboards import get_wa_menu_keyboard
from db import crud
from db.models import (
    Brand, BrandSettings, ClientBot, EWallet, Group, Invoice, InvoiceTemplate,
    LoyaltySettings, PaymentSettings, ShortLink, User,
)
from schemas import (
    AvailablePaymentSchema, ExtSysSetTypes, FastPayStateData,
    InvoiceTemplatePaymentModeEnum, MakePaymentData,
)
from utils.message import send_tg_message, send_wa_message
from utils.numbers import format_currency
from utils.text import f, html_to_markdown
from utils.translator import t

debugger = logging.getLogger('debugger.fastpaybot')


class PaymentAmounts(TypedDict):
    base_amount: float  # Початкова сума
    base_amount_formatted: str
    discount_amount: float | None  # Сума знижки
    discount_amount_formatted: str | None
    amount_after_discount: float  # Сума після знижки
    amount_after_discount_formatted: str | None
    applied_bonuses: float  # Застосовані бонуси
    applied_bonuses_formatted: str | None
    bonuses: float | None  # Доступні бонуси
    bonuses_formatted: str | None
    max_bonuses_amount: float | None  # Максимально можлива сума бонусів
    max_bonuses_amount_formatted: str | None
    amount_after_bonuses: float  # Сума після застосування бонусів
    amount_after_bonuses_formatted: str | None
    total_extra_fee: float  # Сума націнки
    total_extra_fee_formatted: str | None
    amount_after_extra_fee: float  # Сума після націнки
    amount_after_extra_fee_formatted: str | None
    fee_amount: float | None  # Комісія платіжного провайдера
    fee_amount_formatted: str | None
    final_amount: float  # Фінальна сума до сплати
    final_amount_formatted: str | None


class FastPayProcessor:
    def __init__(
            self,
            messanger_obj: ma.Message | ma.ButtonQuery,
            state: ma.FSMContext,
            user: User,
            lang: str,
            data: FastPayStateData | None = None,
            invoice_template: InvoiceTemplate | None = None
    ):
        self.messanger_obj: ma.Message | ma.ButtonQuery = messanger_obj
        self.state = state
        self.user: User = user
        self.lang: str = lang
        self._data: FastPayStateData | None = data
        self._invoice_template: InvoiceTemplate | None = invoice_template
        self._invoice: Invoice | None = None
        self._group: Group | None = None
        self._brand: Brand | None = None
        self._payment_methods: AvailablePaymentSchema | None = None
        self._ewallet: EWallet | None = None

    @property
    def answer_obj(self):
        return ma.detect_answer_obj(self.messanger_obj)

    async def update_data(self, new_data: FastPayStateData | None = None, **kwargs):
        if not new_data:
            new_data = await self.data

        for key, value in kwargs.items():
            setattr(new_data, key, value)

        await self.state.update_data(fast_pay_data=new_data.dict(exclude_none=True))
        self._data = new_data
        return new_data

    async def reload_data(self):
        state_data = await self.state.get_data()
        try:
            self._data = FastPayStateData(**state_data.get("fast_pay_data"))
        except Exception as e:
            raise exceptions.FastPayInvalidStateDataError() from e
        return self._data

    @property
    async def data(self):
        if not self._data:
            await self.reload_data()
        return self._data

    @property
    async def invoice_template(self) -> InvoiceTemplate:
        if not self._invoice_template:
            data = await self.data
            self._invoice_template = await InvoiceTemplate.get(
                data.invoice_template_id,
                is_deleted=False,
            )
            if not self._invoice_template:
                raise exceptions.InvoiceTemplateNotFoundError(data.invoice_template_id)

        return self._invoice_template

    @property
    async def invoice(self):
        if not self._invoice:
            data = await self.data
            if data.invoice_id:
                self._invoice = await Invoice.get(
                    data.invoice_id,
                )
                if not self._invoice:
                    raise exceptions.InvoiceNotFoundError(data.invoice_id)

        return self._invoice

    @property
    async def group(self):
        if not self._group:
            data = await self.data
            self._group = await Group.get(
                data.group_id,
                status="enabled",
            )
            if not self._group:
                raise exceptions.ProfileNotFoundError(data.group_id)

        return self._group

    @property
    async def brand(self):
        if not self._brand:
            data = await self.data
            self._brand = await Brand.get(group_id=data.group_id)
        return self._brand

    @property
    async def payment_methods(self):
        if not self._payment_methods:
            data = await self.data
            bot_type = await ma.detect_bot_type(self.answer_obj)
            self._payment_methods = await (
                get_available_payment_methods(
                    await self.brand, self.user, self.lang,
                    invoice_template_id=data.invoice_template_id,
                    bot_type=bot_type,
                    only_online=True
                )
            )
            if not self._payment_methods.methods:
                raise exceptions.FastPayNoPaymentMethodsAvailableError()
        return self._payment_methods

    @property
    async def ewallet(self):
        if not self._ewallet:
            data = await self.data
            self._ewallet = await EWallet.get(
                id=data.ewallet_id, is_enabled=True, is_deleted=False
            ) if data.ewallet_id else None
        return self._ewallet

    @classmethod
    async def setup(
            cls,
            messanger_obj: ma.Message | ma.ButtonQuery,
            state: ma.FSMContext,
            user: User,
            lang: str,
            data: FastPayStateData | None = None,
            invoice_template: InvoiceTemplate | None = None
    ):
        processor = cls(messanger_obj, state, user, lang, data, invoice_template)

        # if data and data.ewallet_id and not data.used_credit:
        #     await processor.update_data(is_loyalty=False)

        if data and invoice_template:  # Якщо це перший вхід через діплінк
            # Перевіряємо можливість використання лояльності
            # Перевірка disabled_loyalty тепер через loyalty_settings.is_enabled
                # and (not data.ewallet_id or data.used_credit):
            loyalty_settings, terminal_settings = await (
                processor.get_loyalty_settings())
            if loyalty_settings and loyalty_settings.is_enabled and terminal_settings:

                bonus_payment_limit = min(
                    terminal_settings.loyalty_settings.bonus_payment_limit,
                    (
                        float(invoice_template.max_bonuses_percent)
                        if invoice_template.max_bonuses_percent is not None
                        else 100
                    ),
                )

                # Отримуємо баланс бонусів користувача
                user_bonuses = await processor._get_available_bonuses(
                    loyalty_settings,
                    invoice_template.currency
                )

                incust_prohibit_redeeming_bonuses = await (
                    BrandSettings.get_by_brand_and_type(
                        (await processor.brand).id,
                        ExtSysSetTypes.incust_prohibit_redeeming_bonuses.value,
                        True,
                    ))

                # Зберігаємо дані про лояльність
                await processor.update_data(
                    is_loyalty=True,
                    bonus_payment_limit=bonus_payment_limit,
                    user_available_bonuses=user_bonuses,
                    incust_prohibit_redeeming_bonuses=bool(
                        int(incust_prohibit_redeeming_bonuses)
                    ) if incust_prohibit_redeeming_bonuses else False,
                )

                data = await processor.data
                debugger.debug(f"{data=}")

            await processor.update_data(data)
            if data.ewallet_id:
                ewallet = await EWallet.get(
                    id=data.ewallet_id, is_enabled=True, is_deleted=False
                )
                if ewallet:
                    ewallet_user_account_info = await process_single_ewallet(
                        ewallet, user, lang
                    )
                    await processor.update_data(
                        ewallet_title=ewallet_user_account_info.ewallet.name,
                        ewallet_message=ewallet_user_account_info.message,
                    )

        return processor

    async def get_invoice_template_text(self):
        invoice_template = await self.invoice_template
        text = f"<b>{invoice_template.title}</b>"
        if invoice_template.description:
            text += f"\n{invoice_template.description}"
        data = await self.data
        if data.ewallet_title and data.ewallet_message:
            text += (
                    "\n\n" + (await f(
                "ewallet account recharge title", self.lang
            )) + ": " + f"{data.ewallet_message}\n"
            )
        return text + "\n"

    async def send_enter_amount_menu(self):
        data = await self.data

        invoice_template = await self.invoice_template
        media = await invoice_template.get_media()

        bot_type = await ma.detect_bot_type(self.answer_obj)

        invoice_template_text = await self.get_invoice_template_text()

        if data.is_amount_fixed and data.entered_amount:
            body_text = f"{invoice_template_text}"
            return await self.send_one_step_confirmation_menu(
                data.entered_amount,
                body_text
            )

        footer_text = await f("fast pay enter amount text", self.lang)
        text = f"{invoice_template_text}\n\n<i>{footer_text}</i>"

        match bot_type:
            case "telegram":
                await send_tg_message(
                    self.answer_obj.chat.id,
                    content_type="photo" if media else "text",
                    text=text,
                    photo=media.file_path if media else None,
                )
                return None
            case "whatsapp":
                text = html_to_markdown(text)

                bot = await ClientBot.get_current()
                await send_wa_message(
                    self.answer_obj.user.phone_number,
                    content_type="image" if media else "text",
                    bot_token=bot.token,
                    wa_from=bot.whatsapp_from,
                    text=text,
                    image=media.url if media else None,
                )
                return None
        return None

    async def send_items_menu(self):
        data = await self.data
        invoice_template = await self.invoice_template

        # Додаємо заголовок з назвою шаблону
        body_text = await self.get_invoice_template_text()

        if invoice_template.payment_mode == InvoiceTemplatePaymentModeEnum.ITEMS:
            items_text = await self.format_items_list()
            body_text = f"{body_text}\n{items_text}"
            await self.update_data(items_displayed=True)

        items = await crud.get_invoice_template_items(invoice_template.id)
        total_amount_raw = sum(
            item.price * item.quantity * data.count for item in items
        )
        total_amount = round(total_amount_raw / 100, 2)

        footer_text = await self.get_items_footer_text()

        # Перевіряємо можливість використання бонусів
        if (data.user_available_bonuses and data.user_available_bonuses > 0 and not
        data.incust_prohibit_redeeming_bonuses):
            # Якщо є бонуси - показуємо кнопку "Далі"
            bot_type = await ma.detect_bot_type(self.answer_obj)
            media = await invoice_template.get_media()

            match bot_type:
                case "telegram":
                    if footer_text:
                        body_text += f"\n\n<i>{footer_text}</i>"
                    keyboard = (
                        await get_submit_count_keyboard(self.lang)
                    ).to_messanger(bot_type)
                    await send_tg_message(
                        self.answer_obj.chat.id,
                        content_type="photo" if media else "text",
                        text=body_text,
                        photo=media.file_path if media else None,
                        keyboard=keyboard,
                    )
                case "whatsapp":
                    keyboard = await get_submit_count_keyboard(self.lang)
                    keyboard = keyboard.to_messanger(bot_type)
                    body_md = html_to_markdown(body_text)
                    # Обмеження довжини тексту повідомлення до 1024 символів
                    # максимум для WhatsApp API)
                    if len(body_md) > 1024:
                        body_md = body_md[:1021] + '...'

                    await keyboard.sending_method(
                        to=self.answer_obj.user.id,
                        body=body_md,
                        footer=footer_text[:60] if footer_text else None,
                        image=media.url if media else None,
                        **keyboard.to_kwargs()
                    )
        else:
            # Якщо бонусів немає - показуємо одразу методи оплати
            await self.send_one_step_confirmation_menu(
                total_amount, body_text, footer_text
            )

    async def get_items_footer_text(self):
        data = await self.data
        invoice_template = await self.invoice_template

        if invoice_template.disabled_qty:
            return None

        if data.count > 1:
            return await f(
                "fast pay current count text",
                self.lang, count=data.count
            )
        return await f("fast pay enter count text", self.lang)

    async def calculate_fee_info(
            self, amount: float,
            payment_settings_id: int | None = None,
    ):
        group = await self.group
        invoice_template = await self.invoice_template

        fees = [
            (method.fee_value or 0) + (amount / 100 * (method.fee_percent or 0))
            for method in (await self.payment_methods).methods
            if not payment_settings_id or method.settings_id == payment_settings_id
        ]

        if not fees:
            return None

        max_fee = max(fees)
        if not max_fee:
            return None

        all_fees_equal = all(map(lambda x: x == max_fee, fees))

        if invoice_template.currency in NO_CENT_CURRENCIES:
            max_fee = math.ceil(max_fee)

        max_fee_formatted = format_currency(
            max_fee, invoice_template.currency,
            group.lang, group.country_code,
        )

        if all_fees_equal and max_fee:
            text = (
                    await f(
                        "payer fee receipt list order text", self.lang
                    ) +
                    f": {max_fee_formatted}"
            )
        else:
            text = "<i>" + await f(
                "payment service fee text", self.lang,
                service_fee=max_fee_formatted,
            ) + "</i>"
        return {
            "all_fees_equal": all_fees_equal,
            "text": text,
            "max_fee": max_fee,
            "max_fee_formatted": max_fee_formatted,
        }

    async def send_one_step_confirmation_menu(
            self,
            base_amount: float,
            body_text: str,
            footer_text: str | None = None,
    ):
        """Відображає меню вибору способу оплати"""
        invoice_template = await self.invoice_template
        media = await invoice_template.get_media()
        bot_type = await ma.detect_bot_type(self.answer_obj)

        amounts = await self.calculate_payment_amounts(base_amount)

        await self.update_data(
            final_amount=amounts["final_amount"],
            calculated_amounts=amounts
        )

        if not body_text:
            body_text = await self.get_invoice_template_text()

        # Додаємо інформацію про суми після базової інформації
        amount_text = await self.format_payment_message(amounts)
        message_text = f"{body_text}\n\n{amount_text}"

        payment_methods = await self.payment_methods

        if (
                not payment_methods.single_payment_method and
                bot_type == "whatsapp" and media
        ):
            keyboard = (
                await get_submit_count_keyboard(self.lang)
            ).to_messanger(bot_type)
        elif bot_type == "whatsapp":
            keyboard = await get_wa_payment_methods_keyboard(
                payment_methods,
                amounts["amount_after_extra_fee"],
                invoice_template.currency,
                await self.group,
                self.lang
            )
        elif bot_type == "telegram":
            keyboard = await get_tg_payment_methods_keyboard(
                payment_methods, self.lang
            )
        else:
            keyboard = None

        match bot_type:
            case "telegram":
                if footer_text:
                    message_text += f"\n\n<i>{footer_text}</i>"

                if not payment_methods.single_payment_method:
                    message_text += "\n\n" + await f(
                        "fast pay select payment method text", self.lang
                    ) + ":"

                await send_tg_message(
                    self.answer_obj.chat.id,
                    content_type="photo" if media else "text",
                    text=message_text,
                    photo=media.file_path if media else None,
                    keyboard=keyboard,
                )
            case "whatsapp":
                message_text = html_to_markdown(message_text)
                if footer_text:
                    footer_text = html_to_markdown(footer_text)[:60]

                # Обмеження довжини тексту повідомлення до 1024 символів (максимум
                # для WhatsApp API)
                if len(message_text) > 1024:
                    message_text = message_text[:1021] + '...'

                await keyboard.sending_method(
                    to=self.answer_obj.user.id,
                    body=message_text,
                    footer=footer_text,
                    image=media.url if media else None,
                    **keyboard.to_kwargs()
                )

    async def send_payment_data_menu(self):
        invoice_template = await self.invoice_template

        match invoice_template.payment_mode:
            case InvoiceTemplatePaymentModeEnum.ENTERED_AMOUNT:
                data = await self.data
                if data.entered_amount:
                    if await self.check_and_process_bonuses(data.entered_amount):
                        return None
                    if not data.is_amount_fixed:
                        # Якщо сума вже введена й is_amount_fixed=False, переходимо
                        # до підтвердження
                        await FastPay.Confirmation.set()
                        return await self.send_confirmation_menu()
                await self.send_enter_amount_menu()
                return None

            case InvoiceTemplatePaymentModeEnum.ITEMS:
                data = await self.data
                items = await crud.get_invoice_template_items(invoice_template.id)
                total = sum(
                    item.price * item.quantity for item in items
                ) * data.count / 100

                if (invoice_template.disabled_qty and await
                self.check_and_process_bonuses(
                    total
                )):
                    return None

                await self.send_items_menu()
                return None
        return None

    async def send_confirmation_menu(self):
        data = await self.data
        show_back_button = bool(
            data.user_available_bonuses and
            not data.incust_prohibit_redeeming_bonuses
            and data.max_bonuses_amount
        )
        invoice_template = await self.invoice_template

        if (invoice_template.payment_mode ==
                InvoiceTemplatePaymentModeEnum.ENTERED_AMOUNT):
            if not data.entered_amount:
                await self.state.set_state(FastPay.PaymentData.state)
                return await self.send_payment_data_menu()
            base_amount = data.entered_amount
        else:
            items = await crud.get_invoice_template_items(invoice_template.id)
            base_amount = round(
                sum(map(lambda x: x.price * x.quantity, items)) * data.count / 100,
                2
            )

        if (invoice_template.payment_mode ==
                InvoiceTemplatePaymentModeEnum.ENTERED_AMOUNT):
            footer_text = (
                await f("fast pay confirmation footer text", self.lang)
                if not data.is_amount_fixed
                else None
            )
        else:
            footer_text = (
                await f("fast pay confirmation items footer text", self.lang)
                if not invoice_template.disabled_qty
                else None
            )

        amounts = await self.calculate_payment_amounts(
            base_amount
        )

        amount_after_bonuses = amounts["amount_after_bonuses"]

        body_text = await self.get_invoice_template_text() + "\n"

        body_text += await self.format_payment_message(amounts)
        await self.update_data(calculated_amounts=amounts)

        bot_type = await ma.detect_bot_type(self.answer_obj)
        payment_methods = await self.payment_methods

        if bot_type == "telegram":
            keyboard = await get_tg_payment_methods_keyboard(
                payment_methods, self.lang,
                show_back_button=show_back_button
            )

            text = body_text
            if footer_text:
                text += f"\n\n<i>{footer_text}</i>"

            if not payment_methods.single_payment_method:
                text += "\n\n" + await f(
                    "fast pay select payment method text", self.lang
                ) + ":"

            return await self.answer_obj.answer(text, reply_markup=keyboard)
        else:
            keyboard = await get_wa_payment_methods_keyboard(
                payment_methods,
                amount_after_bonuses,
                invoice_template.currency,
                await self.group,
                self.lang,
                show_back_button=show_back_button
            )

            body_text = html_to_markdown(body_text)
            if footer_text:
                footer_text = html_to_markdown(footer_text)

            # Обмеження довжини тексту повідомлення до 1024 символів (максимум для
            # WhatsApp API)
            if len(body_text) > 1024:
                body_text = body_text[:1021] + '...'

            return await keyboard.sending_method(
                to=self.answer_obj.user.id,
                body=body_text,
                footer=footer_text[:60] if footer_text else None,
                **keyboard.to_kwargs()
            )

    async def send_state_menu(self):
        cur_state = await self.state.get_state()

        match cur_state:
            case FastPay.PaymentData.state:
                await self.send_payment_data_menu()
            case FastPay.EnterBonusAmount.state:
                await self.send_bonus_choice_menu()
            case FastPay.Confirmation.state:
                await self.send_confirmation_menu()
            case FastPay.ConfirmFullBonusPayment.state:
                await self.send_full_bonus_payment_menu()
            case _:
                raise exceptions.FastPayInvalidStateError(cur_state)

    async def get_or_create_invoice(self):
        invoice = await self.invoice
        data = await self.data
        if invoice:
            if data.incust_check:
                if invoice.incust_check and invoice.incust_check.get(
                        "amount_to_pay"
                ) == data.incust_check.get(
                    "amount_to_pay"
                ):
                    return invoice
            return invoice

        if not data.calculated_amounts:
            raise exceptions.FastPayInvalidStateError("Not found calculated amounts")

        # Беремо суму після застосування бонусів
        amount_after_bonuses = data.calculated_amounts["amount_after_bonuses"]

        # Фінальна сума до оплати (сума після бонусів + комісія)
        invoice_template = await self.invoice_template
        if data.entered_amount:
            if invoice_template.currency in NO_CENT_CURRENCIES:
                entered_amount = math.ceil(data.entered_amount)
            else:
                entered_amount = round(data.entered_amount, 2)
        else:
            entered_amount = None

        invoice_service = InvoiceService(
            group=await self.group,
            user=self.user,
        )

        bot = await ClientBot.get_current()
        self._invoice = await invoice_service.create_invoice(
            schemas.InvoiceTypeEnum.TOPUP_ACCOUNT if data.ewallet_id else
            schemas.InvoiceTypeEnum.FROM_LINK,
            "template",
            invoice_template=await self.invoice_template,
            bot=bot,
            count=data.count,
            entered_amount=entered_amount,
            first_name=self.user.first_name,
            last_name=self.user.last_name,
            email=self.user.email,
            phone=self.user.wa_phone,
            incust_check=data.incust_check.dict() if isinstance(
                data.incust_check, term.m.Check
            ) else data.incust_check,
            ewallet_id=data.ewallet_id if data.ewallet_id else None,
        )
        await self.update_data(invoice_id=self._invoice.id)
        return self._invoice

    async def handle_selected_payment_method(
            self,
            payment_data: PaymentMethodCallbackData,
            bot: ClientBot,
    ):
        fast_pay_data = await self.data
        if not fast_pay_data.calculated_amounts:
            raise exceptions.FastPayInvalidStateError("Not found calculated amounts")

        payment_settings = await PaymentSettings.get(payment_data.payment_settings_id)

        if payment_settings.payment_method == "kpay" and not fast_pay_data.user_phone:
            await self.update_data(
                payment_settings_id=payment_data.payment_settings_id,
                object_payment_settings_id=payment_data.object_payment_settings_id,
            )

            # Переходимо в стан введення номера телефону
            await FastPay.EnterPhoneNumber.set()

            # Відправляємо повідомлення про необхідність ввести номер телефону
            text = await f("PAYMENT_KPAY_PHONE_REQUIRED_TEXT", self.lang)
            return await self.answer_obj.answer(text)

        # Update payment method without resetting loyalty calculation
        await self.update_data(**payment_data.dict())

        # Recalculate amounts with new payment method
        invoice_template = await self.invoice_template
        base_amount = (fast_pay_data.entered_amount if
                       invoice_template.payment_mode ==
                       InvoiceTemplatePaymentModeEnum.ENTERED_AMOUNT
                       else await self._get_template_amount())

        amounts = await self.calculate_payment_amounts(base_amount, is_no_calc_max=True)

        # Existing payment creation logic
        invoice = await self.get_or_create_invoice()

        if invoice.status == "payed":
            raise InvoicePaymentInvoicePayedError(invoice.id)

        brand = await self.brand

        payments_service = PaymentsService(
            brand, self.user, self.lang,
            invoice=invoice, bot=bot,
        )

        # Для kpay передаємо номер телефону в параметрах
        provider_data = await payments_service.make_provider_payment(
            MakePaymentData(
                payment_settings_id=payment_data.payment_settings_id,
                object_payment_settings_id=payment_data.object_payment_settings_id,
                user_phone=fast_pay_data.user_phone if fast_pay_data.user_phone else
                None,
                # Додаємо номер телефону
            )
        )

        try:
            data = provider_data["data"]
            if error := data.get("error"):
                return await self.answer_obj.answer(error)
            if provider_data["type"] == "orange":
                url = data["data"]["orange_maxit_url"]
            elif provider_data["type"] == "ewallet":
                ewallet_payment_id = data["data"]["payment_id"]
                keyboard = await get_wa_menu_keyboard(self.user, bot, self.lang)
                processor = EwalletPaymentProcessor(
                    self.messanger_obj, self.state, bot, self.user, self.lang,
                    keyboard, ewallet_payment_id,
                )

                try:
                    return await processor.confirm_ewallet_payment()
                except Exception as e:
                    return await processor.process_exception(e)
            elif provider_data["type"] == "kpay":
                if "data" in data and "payment_id" in data["data"]:
                    keyboard = await get_wa_menu_keyboard(self.user, bot, self.lang)
                    return await self.answer_obj.answer(
                        await f("PAYMENT_OPEN_KPAY_TEXT", self.lang),
                        reply_markup=keyboard
                    )
                else:
                    raise ValueError("Kpay provider initiate payment error")
            else:
                url = data["data"]["url"]
        except (KeyError, AttributeError) as e:
            payment_settings = await PaymentSettings.get(
                payment_data.payment_settings_id
            )
            raise exceptions.FastPayPaymentMethodNotSupportedError(
                payment_settings.payment_method
            ) from e

        short_link = await create_short_link("url", url)

        # Форматування повідомлення з сумами
        text, keyboard = await self._format_payment_provider_message(
            amounts, payment_data, short_link
        )

        if bot.bot_type == "whatsapp":
            text = html_to_markdown(text)

        await self.answer_obj.answer(text, reply_markup=keyboard)

    async def get_loyalty_settings(self):
        """Отримує налаштування лояльності та терміналу."""
        brand = await self.brand
        invoice_template = await self.invoice_template

        # Перенесено перевірку is_enabled до отримання loyalty_settings

        if not await brand.is_incust:
            return None, None

        # Отримуємо налаштування лояльності
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "invoice_template",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                invoice_template_id=invoice_template.id,
                profile_id=brand.group_id,
            )
        )

        if not loyalty_settings or not loyalty_settings.is_enabled:
            return None, None

        # Синхронізуємо користувача з InCust
        try:
            await get_or_create_incust_customer(self.user, loyalty_settings, self.lang)
        except Exception as ex:
            logging.error(f"Failed to sync customer: {ex}", exc_info=True)

        # Отримуємо налаштування терміналу
        try:
            async with incust.term.SettingsApi(loyalty_settings) as api:
                terminal_settings = await api.settings()
            return loyalty_settings, terminal_settings
        except Exception as ex:
            logging.error(f"Failed to get terminal settings: {ex}", exc_info=True)
            return None, None

    async def send_bonus_choice_menu(self):
        """Показує меню вибору бонусів використовуючи дані зі стейту"""
        bot_type = await ma.detect_bot_type(self.answer_obj)
        data = await self.data
        invoice_template = await self.invoice_template
        group = await self.group

        # Показуємо кнопку "Назад" якщо можна змінити суму або кількість
        show_back_button = (
                (
                        invoice_template.payment_mode ==
                        InvoiceTemplatePaymentModeEnum.ENTERED_AMOUNT
                        and not data.is_amount_fixed)
                or
                (invoice_template.payment_mode == InvoiceTemplatePaymentModeEnum.ITEMS
                 and not invoice_template.disabled_qty)
        )

        # Додаємо заголовок шаблону
        body_text = await self.get_invoice_template_text()

        if (invoice_template.payment_mode ==
                InvoiceTemplatePaymentModeEnum.ENTERED_AMOUNT):
            entered_amount = data.entered_amount
            if entered_amount is not None:
                body_text += await f(
                    "fast pay entered amount text", self.lang,
                    amount=format_currency(
                        entered_amount,
                        invoice_template.currency,
                        group.lang,
                        group.country_code
                    )
                ) + "\n"
        else:
            items_text = await self.format_items_list()
            body_text += f"\n{items_text}\n"

        if data.max_bonuses_amount and data.max_bonuses_amount > 0:
            max_bonuses_amount_formated = format_currency(
                data.max_bonuses_amount,
                invoice_template.currency,
                group.lang,
                group.country_code,
            )

            if data.calculated_amounts and data.calculated_amounts.get(
                    "discount_amount"
            ):
                body_text += await f(
                    "check discount text", self.lang
                ) + f": {data.calculated_amounts['discount_amount_formatted']}\n"

            body_text += "\n" + await f(
                "FAST_PAY_SEND_BONUS_CHOICE_TEXT",
                lang=self.lang,
                max_bonuses_amount=max_bonuses_amount_formated,
                user_available_bonuses=format_currency(
                    data.user_available_bonuses,
                    invoice_template.currency,
                    group.lang,
                    group.country_code,
                ),
            )
        else:
            body_text += await f("FAST_PAY_EMPTY_BONUSES_TEXT", self.lang)
            max_bonuses_amount_formated = None

        await send_bonus_choice_message(
            bot_type=bot_type,
            lang=self.lang,
            max_bonuses_amount=max_bonuses_amount_formated,
            show_back_button=show_back_button,
            answer_obj=self.answer_obj,
            body_text=body_text
        )

    async def check_and_process_bonuses(self, total_amount: float):
        """Перевіряє чи доступні бонуси та показує меню вибору бонусів"""
        if total_amount is None or total_amount <= 0:
            return False

        data = await self.data
        if not data.is_loyalty:
            return False

        # Calculate amounts which will also calculate loyalty if needed
        amounts = await self.calculate_payment_amounts(total_amount)
        await self.update_data(calculated_amounts=amounts)

        if not amounts.get("max_bonuses_amount") or amounts.get(
                "max_bonuses_amount"
        ) == 0:
            return False

        debugger.debug(f"{amounts=}")

        await self.update_data(
            max_bonuses_amount=amounts["max_bonuses_amount"],
            initial_max_bonuses=amounts["max_bonuses_amount"]
        )

        if data.incust_prohibit_redeeming_bonuses:
            return False

        if data.user_available_bonuses == 0:
            return False

        if not data.max_bonuses_amount:
            return False

        await FastPay.EnterBonusAmount.set()
        await self.send_bonus_choice_menu()
        return True

    async def calculate_payment_amounts(
            self, base_amount: float | None = None, is_no_calc_max: bool | None = None
    ) -> PaymentAmounts:
        data = await self.data
        if not base_amount:
            base_amount = data.calculated_amounts.get("base_amount")

        invoice_template = await self.invoice_template
        group = await self.group

        amounts = {
            "base_amount": base_amount,
            "base_amount_formatted": format_currency(
                base_amount, invoice_template.currency, group.lang, group.country_code
            )
        }

        if not data.is_loyalty:
            return await self._calculate_amounts_without_loyalty(amounts)

        # Визначаємо суму бонусів для використання
        bonuses_to_use = self._calculate_applied_bonuses(
            data
        ) if not is_no_calc_max else data.entered_bonus_amount
        debugger.debug(f"{bonuses_to_use=}")

        if data.bonus_action == "skip":
            bonuses_to_use = 0

        if not data.loyalty_calculated:
            incust_check = await self._process_loyalty_check(
                base_amount, bonuses_to_use
            )

            debugger.debug(
                f"{incust_check.amount_to_pay=}, {data.bonus_payment_limit=}"
            )

            # if not data.entered_bonus_amount and not data.bonus_action:

            await self.update_data(
                max_bonuses_amount=self._calculate_max_bonuses(
                    incust_check.amount_to_pay,
                    data.bonus_payment_limit,
                )
            )

            debugger.debug(f"{data.max_bonuses_amount=}")

            await self.update_data(
                loyalty_calculated=True,
                discount_amount=incust_check.discount_amount,
                amount_after_discount=incust_check.amount_to_pay,
                incust_check=incust_check.dict()
            )

        # Формуємо amounts для відображення
        amounts.update(
            {
                "discount_amount": data.discount_amount,
                "discount_amount_formatted": format_currency(
                    data.discount_amount, invoice_template.currency,
                    group.lang, group.country_code
                ) if data.discount_amount else None,
                "amount_after_discount": data.amount_after_discount,
                "amount_after_discount_formatted": format_currency(
                    data.amount_after_discount, invoice_template.currency,
                    group.lang, group.country_code
                ),
                "applied_bonuses": bonuses_to_use,
                "applied_bonuses_formatted": format_currency(
                    bonuses_to_use, invoice_template.currency,
                    group.lang, group.country_code
                ) if bonuses_to_use else None,
                "bonuses": data.user_available_bonuses,
                "bonuses_formatted": format_currency(
                    data.user_available_bonuses, invoice_template.currency,
                    group.lang, group.country_code
                ) if data.user_available_bonuses else None,
                "max_bonuses_amount": data.max_bonuses_amount,
                "max_bonuses_amount_formatted": format_currency(
                    data.max_bonuses_amount, invoice_template.currency,
                    group.lang, group.country_code
                ) if data.max_bonuses_amount else None,
                "amount_after_bonuses": data.amount_after_discount,
                "amount_after_bonuses_formatted": format_currency(
                    data.amount_after_discount, invoice_template.currency,
                    group.lang, group.country_code
                )
            }
        )

        amount_after_bonuses = data.incust_check.get(
            "amount_to_pay"
        ) if data.incust_check else base_amount

        return await self._calculate_final_amounts(amount_after_bonuses, amounts)

    async def _calculate_amounts_without_loyalty(
            self, amounts: PaymentAmounts
    ) -> PaymentAmounts:
        """Розрахунок сум без врахування лояльності"""
        amount_without_loyalty = amounts["base_amount"]

        amounts.update(
            {
                "discount_amount": None,
                "discount_amount_formatted": None,
                "amount_after_discount": amount_without_loyalty,
                "amount_after_discount_formatted": amounts["base_amount_formatted"],
                "applied_bonuses": 0,
                "applied_bonuses_formatted": None,
                "bonuses": None,
                "max_bonuses_amount": None,
                "max_bonuses_amount_formatted": None,
                "amount_after_bonuses": amount_without_loyalty,
                "amount_after_bonuses_formatted": amounts["base_amount_formatted"]
            }
        )

        return await self._calculate_final_amounts(amount_without_loyalty, amounts)

    async def _get_available_bonuses(
            self, loyalty_settings: LoyaltySettings, currency: str
    ) -> float:
        """Отримує доступні бонуси користувача"""
        try:
            async with incust.term.CustomerApi(loyalty_settings, lang=self.lang) as api:
                user_card = await api.cardinfo(
                    self.user.incust_external_id,
                    "external-id",
                )

            if user_card and hasattr(user_card, 'bonuses') and user_card.bonuses:
                for bonus in user_card.bonuses:
                    if hasattr(bonus, 'currency') and bonus.currency == currency:
                        return bonus.bonuses_amount if hasattr(
                            bonus, 'bonuses_amount'
                        ) else 0
        except Exception as ex:
            logging.error(f"Failed to get user bonuses: {ex}", exc_info=True)
        return 0

    def _calculate_max_bonuses(
            self, amount: float, bonus_payment_limit: float
    ) -> float:
        """Розраховує максимальну суму бонусів"""
        return amount * bonus_payment_limit / 100

    def _calculate_applied_bonuses(self, data: FastPayStateData) -> float:
        """Розраховує суму бонусів до застосування"""
        debugger.debug(f"{data.entered_bonus_amount=}, {data.max_bonuses_amount=}")
        if data.incust_prohibit_redeeming_bonuses:
            return 0
        if data.entered_bonus_amount:
            return min(data.entered_bonus_amount, data.max_bonuses_amount or 0)
        elif data.bonus_action == "use_all_bonuses":
            return data.max_bonuses_amount or 0
        return 0

    async def _get_template_amount(self) -> float:
        """Helper method to calculate total amount from template items"""
        invoice_template = await self.invoice_template
        data = await self.data

        items = await crud.get_invoice_template_items(invoice_template.id)
        if not items:
            return 0

        count = data.count or 1

        total_amount_raw = sum(
            item.price * item.quantity * count
            for item in items
        )

        return round(total_amount_raw / 100, 2)

    async def format_payment_message(self, amounts: PaymentAmounts) -> str:
        """Форматує повідомлення з сумами для вибору способу оплати"""
        invoice_template = await self.invoice_template
        data = await self.data
        lines = []

        if invoice_template.payment_mode == InvoiceTemplatePaymentModeEnum.ITEMS:
            if not data.items_displayed:
                items_text = await self.format_items_list()
                lines.append(items_text)
            await self.update_data(items_displayed=False)
        else:
            # Завжди показуємо початкову суму для випадку ENTERED_AMOUNT
            lines.append(
                await f(
                    "fast pay entered amount text",
                    self.lang,
                    amount=amounts["base_amount_formatted"]
                )
            )

        # Знижка
        if amounts.get("discount_amount"):
            lines.append(
                await f("check discount text", self.lang) +
                f": {amounts['discount_amount_formatted']}"
            )

        # Бонуси
        if not data.incust_prohibit_redeeming_bonuses and amounts.get(
                'max_bonuses_amount'
        ):
            if amounts.get("applied_bonuses"):
                lines.append(
                    await f("loyalty will redeemed bonuses header", self.lang) +
                    f": {amounts['applied_bonuses_formatted']}"
                )
            elif amounts.get("bonuses") and amounts.get(
                    "bonuses"
            ) > 0 and not data.bonus_action:
                lines.append(
                    await f(
                        "fast pay bot bonuses available text", self.lang,
                        bonuses=amounts["bonuses_formatted"]
                    )
                )
                lines.append(
                    await f(
                        "fast pay bot max bonuses available text", self.lang,
                        max_bonuses_amount_formatted=amounts[
                            'max_bonuses_amount_formatted']
                    )
                )

        # Прибираємо рядок про відсутність бонусів
        # if (data.is_loyalty and not data.incust_prohibit_redeeming_bonuses and not
        # amounts.get(
        #     "bonuses"
        # ) or amounts.get("bonuses") == 0):
        #     lines.append(await f("FAST_PAY_EMPTY_BONUSES_TEXT", self.lang))

        # if data.incust_prohibit_redeeming_bonuses:
        #     lines.append("<i>" + await f("FAST_PAY_DISABLE_USE_BONUSES_TEXT",
        #     self.lang) + "</i>")

        # Націнка
        if not data.is_fully_covered and amounts.get("total_extra_fee"):
            lines.append(
                await f("admin extra fees title", self.lang) +
                f": {amounts['total_extra_fee_formatted']}"
            )

        # Сума до сплати - показуємо тільки якщо вона відрізняється від введеної суми
        # Або якщо є знижки, бонуси чи націнки
        show_final_amount = (
                data.is_fully_covered or
                amounts.get("discount_amount") or
                amounts.get("applied_bonuses") or
                amounts.get("total_extra_fee") or
                (
                        invoice_template.payment_mode ==
                        InvoiceTemplatePaymentModeEnum.ITEMS) or
                (amounts.get("base_amount") != amounts.get("final_amount"))
        )

        if show_final_amount:
            lines.append("")
            lines.append(
                "<b>" + await f(
                    "fast pay amount to pay text",
                    self.lang,
                    amount=amounts[
                        "final_amount_formatted"] if not data.is_fully_covered else "0"
                ) + "</b>"
            )

        if data.is_fully_covered:
            if amounts.get("max_bonuses_amount"):
                lines.append(
                    await f("loyalty will redeemed bonuses header", self.lang) +
                    f": {amounts['max_bonuses_amount_formatted']}"
                )
            lines.append("")
            lines.append(await f("FAST_PAY_CONFIRM_PAYMENT_BONUSES_TEXT", self.lang))
            return "\n".join(lines)

        # Комісія провайдера - показуємо тільки коли ще не вибрано метод оплати
        if not data.payment_settings_id and amounts.get("amount_after_extra_fee"):
            fee_info = await self.calculate_fee_info(amounts["amount_after_extra_fee"])
            if fee_info:
                lines.append(fee_info.get("text", ""))

        return "\n".join(lines)

    async def _format_payment_provider_message(
            self,
            amounts: PaymentAmounts,
            payment_data: PaymentMethodCallbackData,
            short_link: ShortLink
    ) -> tuple[str, None | InlineKeyboardMarkup | ListKeyboard]:

        invoice_template = await self.invoice_template

        payment_settings = await PaymentSettings.get(
            payment_data.payment_settings_id
        )
        group = await self.group
        translated_payment = {
            "name": await t(
                payment_settings, self.lang,
                group.lang, field_name="name",
                group_id=group.id,
                is_auto_translate_allowed=group.is_translate,
            )
        }
        payment_name = await get_payment_method_name(
            payment_settings, self.lang, translated_payment,
            for_messanger=True,
        )
        lines = [f"<b>{payment_name}</b>", ""]

        if invoice_template.payment_mode == InvoiceTemplatePaymentModeEnum.ITEMS:
            lines.append(await self.format_items_list())
        else:
            lines.append(
                await f(
                    "fast pay entered amount text",
                    self.lang,
                    amount=amounts["base_amount_formatted"]
                )
            )

        if amounts["discount_amount"]:
            lines.append(
                await f(
                    "check discount text", self.lang
                ) + f": {amounts['discount_amount_formatted']}"
            )

        if amounts["applied_bonuses"]:
            lines.append(
                await f("loyalty will redeemed bonuses header", self.lang) +
                f": {amounts['applied_bonuses_formatted']}"
            )

        if amounts["total_extra_fee"]:
            lines.append(
                await f("admin extra fees title", self.lang) +
                f": {amounts['total_extra_fee_formatted']}"
            )

        if amounts.get("fee_amount"):
            lines.append(
                await f(
                    "payer fee receipt list order text", self.lang
                ) + f": {amounts['fee_amount_formatted']}"
            )

        lines.append(
            "\n<b>" + await f(
                "fast pay amount to pay text",
                self.lang,
                amount=amounts["final_amount_formatted"]
            ) + "</b>"
        )

        lines.append(
            "\n" + await f(
                "fast pay open provider link message",
                self.lang,
                link=short_link.short_url
            )
        )

        message_text = "\n".join(lines)

        data = await self.data
        # keyboard = await get_keyboard_for_payment_provider_message(
        #     await ma.detect_bot_type(answer_obj), self.lang, data
        # )

        return message_text, None

    async def format_items_list(self) -> str:
        data = await self.data
        invoice_template = await self.invoice_template
        items = await crud.get_invoice_template_items(invoice_template.id)

        lines = []
        for item in items:
            quantity = item.quantity * data.count
            item_amount = round(item.price * quantity / 100, 2)
            amount_formatted = format_currency(
                item_amount,
                invoice_template.currency,
                (await self.group).lang,
                (await self.group).country_code
            )
            lines.append(f"x{quantity} <b>{item.name}</b>  {amount_formatted}")
        lines.append("—————————")

        return "\n".join(lines)

    async def _calculate_final_amounts(self, base_amount: float, amounts: dict) -> dict:
        """Спільна логіка розрахунку фінальних сум для обох методів розрахунку"""
        invoice_template = await self.invoice_template
        group = await self.group
        data = await self.data

        # Перевіряємо чи це повна оплата бонусами
        if data.is_fully_covered:
            amounts.update(
                {
                    "total_extra_fee": 0,
                    "total_extra_fee_formatted": format_currency(
                        0, invoice_template.currency,
                        group.lang, group.country_code
                    ),
                    "amount_after_extra_fee": base_amount,
                    "amount_after_extra_fee_formatted": format_currency(
                        base_amount,
                        invoice_template.currency,
                        group.lang, group.country_code
                    ),
                    "fee_amount": 0,
                    "fee_amount_formatted": format_currency(
                        0, invoice_template.currency,
                        group.lang, group.country_code
                    ),
                    "final_amount": 0,
                    "final_amount_formatted": format_currency(
                        0, invoice_template.currency,
                        group.lang, group.country_code
                    )
                }
            )
            return amounts

        # Націнка
        extra_fee = await calc_extra_fee(
            group.id,
            int(base_amount * 100),
            invoice_template.currency
        )
        total_extra_fee = extra_fee.total_extra_fee / 100 if (extra_fee and
                                                              extra_fee.total_extra_fee) else 0

        amount_after_extra_fee = base_amount + total_extra_fee

        amounts.update(
            {
                "total_extra_fee": total_extra_fee,
                "total_extra_fee_formatted": format_currency(
                    total_extra_fee, invoice_template.currency,
                    group.lang, group.country_code
                ),
                "amount_after_extra_fee": amount_after_extra_fee,
                "amount_after_extra_fee_formatted": format_currency(
                    amount_after_extra_fee,
                    invoice_template.currency,
                    group.lang, group.country_code
                )
            }
        )

        # Фінальна сума
        final_amount = amount_after_extra_fee
        if data.payment_settings_id:
            fee_info = await self.calculate_fee_info(
                amount_after_extra_fee,
                data.payment_settings_id
            )
            if fee_info:
                amounts.update(
                    {
                        "fee_amount": fee_info["max_fee"],
                        "fee_amount_formatted": fee_info["max_fee_formatted"]
                    }
                )
                final_amount += fee_info["max_fee"]

        if invoice_template.currency in NO_CENT_CURRENCIES:
            final_amount = math.ceil(final_amount)

        amounts["final_amount"] = final_amount
        amounts["final_amount_formatted"] = format_currency(
            final_amount, invoice_template.currency,
            group.lang, group.country_code
        )

        return amounts

    async def _process_loyalty_check(
            self,
            base_amount: float,
            bonuses_to_use: float = 0
    ) -> term.m.Check:

        invoice_template = await self.invoice_template

        check_data = term.m.Check(
            id=self.user.incust_external_id,
            id_type=term.m.IdType("external-id"),
            amount=base_amount,
            amount_to_pay=base_amount,
            bonuses_redeemed_amount=bonuses_to_use,
            payment_type="currency",
            payment_id=invoice_template.currency,

            check_items=await make_incust_check_items_for_invoice(
                base_amount,
                invoice_template.items if invoice_template.payment_mode ==
                                          InvoiceTemplatePaymentModeEnum.ITEMS else
                None,
                (await self.data).count,
                invoice_template.product_code,
                self.lang,
            )
        )

        # Отримуємо налаштування лояльності
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "invoice_template",
            schemas.LoyaltySettingsData(
                brand_id=(await self.brand).id,
                invoice_template_id=invoice_template.id,
                profile_id=(await self.brand).group_id,
            )
        )

        if not loyalty_settings:
            raise Exception("Loyalty settings not found")

        async with incust.term.CheckTransactionsApi(
                loyalty_settings, lang=self.lang
        ) as api:
            result_check = await api.terminal_process_check(check_data)

        if not result_check:
            raise Exception("Failed to process check")

        # Перевіряємо чи сума до оплати нульова
        is_fully_covered = result_check.amount_to_pay == 0
        await self.update_data(is_fully_covered=is_fully_covered)

        return result_check

    async def send_full_bonus_payment_menu(self):
        """Показує меню підтвердження повної оплати бонусами"""
        data = await self.data
        await self.update_data(items_displayed=False)
        message_text = await self.format_payment_message(data.calculated_amounts)
        message_text = await self.get_invoice_template_text() + f"\n{message_text}"
        bot_type = await ma.detect_bot_type(self.answer_obj)
        keyboard = await get_full_bonus_payment_keyboard(bot_type, self.lang)

        if bot_type == "whatsapp":
            message_text = html_to_markdown(message_text)

        await self.answer_obj.answer(message_text, reply_markup=keyboard)

    async def handle_full_bonus_payment(self):
        """Обробляє підтвердження повної оплати бонусами"""
        loyalty_settings, _ = await self.get_loyalty_settings()
        if not loyalty_settings:
            raise exceptions.FastPayInvalidStateError("Loyalty settings not available")

        await self.update_data(loyalty_calculated=False)

        amounts = await self.calculate_payment_amounts()
        await self.update_data(calculated_amounts=amounts)

        invoice = await self.get_or_create_invoice()

        if invoice.status == "payed":
            raise InvoicePaymentInvoicePayedError(invoice.id)

        await finish_process_full_bonus_payment(
            await self.group, await self.brand, loyalty_settings, invoice, self.lang
        )
