import logging

from aiogram import Dispatcher

from db.models import UserClientBotActivity


async def blocked_bot_handler(_, user_bot_activity: UserClientBotActivity):
    if user_bot_activity:
        await user_bot_activity.deactivate()
    else:
        logging.error("blocked_bot_handler -> user_bot_activity is None ...")


def register_bot_blocked_handlers(dp: Dispatcher):
    dp.register_my_chat_member_handler(
        blocked_bot_handler,
        is_bot_blocked=True,
        chat_type="private",
        state="*",
    )
