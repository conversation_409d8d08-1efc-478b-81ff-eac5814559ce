import logging

import aiogram as tg
import aiowhatsapp as wa
from aiowhatsapp.utils.exceptions import \
    ErrorWithTextVariable as WAErrorWithTextVariable
from psutils.exceptions import ErrorWithTextVariable as PSErrorWithTextVariable

from config import DEFAULT_LANG
from db.models import ClientBot, Group, User
from loggers import J<PERSON>NLogger
from utils.text import f


async def calculate_user_and_lang(
        update: tg.types.Update | wa.types.UpdateEntry,
        exception: Exception,
) -> tuple[
    tg.types.User | wa.types.User,
    bool,
    User,
    str,
]:
    user: User | None = None
    lang = exception.lang if hasattr(exception, "lang") else None

    if isinstance(update, tg.types.Update):
        to_user = tg.types.User.get_current()
        is_bot = to_user.is_bot

        default_lang = to_user.language_code or DEFAULT_LANG

        if to_user and not to_user.is_bot:
            try:
                user = await User.get(to_user.id)
            except Exception as e:
                logging.error(e, exc_info=True)

    else:
        is_bot = False

        to_user = update.user

        try:
            if to_user:
                user = await User.get_by_wa_phone(to_user.phone_number)

            bot_id = ClientBot.get_current_bot_id()
            if bot_id:
                group = await Group.get_by_bot(bot_id)
                default_lang = group.lang
            else:
                default_lang = DEFAULT_LANG
        except Exception as e:
            logging.error(e, exc_info=True)
            default_lang = DEFAULT_LANG

    if not lang:
        if user:
            lang = await user.get_lang()
        else:
            lang = default_lang

    return to_user, is_bot, user, lang


async def error_with_text_variable_handler(
        update: tg.types.Update | wa.types.UpdateEntry,
        exception: PSErrorWithTextVariable | WAErrorWithTextVariable,
        exception_handler_info: dict
):
    logging.error(exception, exc_info=True)

    to_user, is_bot, user, lang = await calculate_user_and_lang(update, exception)
    if to_user and not is_bot:
        await update.bot.send_message(
            to_user.id,
            await f(exception.text_variable, lang, **exception.text_kwargs)
        )

    exception_handler_info["is_exception_handled"] = True

    return True


async def unknown_error_handler(
        update: tg.types.Update | wa.types.UpdateEntry,
        exception: Exception,
):
    bot = await ClientBot.get_current()

    if isinstance(exception, tg.exceptions.Unauthorized):
        if bot and bot.is_friendly:
            return True

    JSONLogger(
        "bot.unknown-error", {
            "bot": await ClientBot.get_current_bot_username(),
            "update": update,
        }
    ).error(exception, exc_info=True)

    to_user, is_bot, user, lang = await calculate_user_and_lang(update, exception)
    if to_user and not is_bot:
        await update.bot.send_message(
            to_user.id,
            await f("bot unknown error occurred text", lang)
        )
    return True


def register_general_exception_handlers(dp: tg.Dispatcher | wa.Dispatcher):
    dp.register_errors_handler(
        error_with_text_variable_handler,
        exception=(PSErrorWithTextVariable, WAErrorWithTextVariable),
    )

    dp.register_errors_handler(
        unknown_error_handler,
        exception=Exception,
    )
