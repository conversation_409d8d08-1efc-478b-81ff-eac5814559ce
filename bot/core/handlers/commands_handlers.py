import re

import config as cfg
from pprint import pformat

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import CompressedLink
from utils.redefined_classes import InlineKb, InlineBtn
from utils.router import Router

from utils.text import replace_html_symbols, f
from utils.update_localisation import update_localisation


async def cmd_state(message: types.Message, state: FSMContext):
    cur_state = await state.get_state()
    if cur_state is None:
        cur_state = "without state"

    state_data = await state.get_data()
    state_data.pop(Router.state_message_key, None)

    text = cur_state
    if message.text.endswith("*dev") or message.chat.id in cfg.DEVELOPERS_CHAT_IDS:
        text += f"\n{state_data}"

    await message.answer(pformat(replace_html_symbols(text), indent=4))


async def cmd_update_local(message: types.Message, lang: str):
    keyboard = InlineKb().row(
        InlineBtn(
            await f("delete this message button", lang),
            callback_data="delete_message",
        )
    )

    if message.text.endswith("*dev") or message.chat.id in cfg.PLATFORM_ADMINS:
        msg = await message.answer(await f("updating local text", lang))
        await message.delete()

        await update_localisation()
        await msg.edit_text(await f("updated local text", lang), reply_markup=keyboard)
    else:
        await message.answer(await f("do not have upd permission", lang), reply_markup=keyboard)
        await message.delete()


async def cmd_short_link(message: types.Message, lang: str):
    raw_link = message.get_args()
    link = re.fullmatch(r"(https?://)?t\.me/[a-zA-Z0-9_]{5,32}\?start=[a-zA-Z0-9_-]+", raw_link)

    if link:
        link = await CompressedLink.compress(link.group(0))
        await message.answer(await f(f"value", lang, value=link))
    else:
        await message.answer(await f(f"compressed link format error", lang))


def register_general_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(cmd_state, commands=["state", "state*dev"], state="*")
    dp.register_message_handler(cmd_update_local, commands=["updatelocal*dev", "upd"], state="*")
    dp.register_message_handler(cmd_short_link, commands="shortlink", state="*")
