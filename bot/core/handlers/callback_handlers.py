from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.router import Router
from psutils.fsm import pop_keys_from_state


async def rm_search_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict,
):
    key = callback_data.get("key")
    await pop_keys_from_state(state, key)
    await state.update_data(position=0)
    await Router.state_menu(callback_query, state)


async def delete_message_button_handler(callback_query: types.CallbackQuery):
    await callback_query.message.delete()


def register_general_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        rm_search_button_handler,
        callback_mode="rm_search",
        state="*",
    )

    dp.register_callback_query_handler(
        delete_message_button_handler,
        callback_mode="delete_message",
        state="*",
    )
