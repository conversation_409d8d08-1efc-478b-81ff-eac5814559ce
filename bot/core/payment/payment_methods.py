from fastapi import HTTPException

import schemas
from config import (
    PAYMENT_METHODS_EMAIL_REQUIRED, QR_PAYMENT_METHODS,
)
from core.ewallet.funcs import ewallet_to_schema
from core.payment.funcs import (
    get_payment_default_name,
    get_translated_payment_settings,
)
from core.payment.incust_pay.service import IncustPayService
from core.store.functions.payments import get_icon_for_payment
from db import crud
from db.models import Brand, EWallet, MediaObject, PaymentSettings, User
from db.models.finances.invoice_template import InvoiceTemplate
from loggers import JSONLogger
from schemas import BotTypeLiteral
from utils.text import f


async def get_available_payment_methods(
        brand: Brand,
        user: User,
        lang: str,
        store_id: int | None = None,
        shipment_id: int | None = None,
        incust_account_id: str | None = None,
        is_qr_menu: bool = False,
        invoice_template_id: int | None = None,
        only_online: bool = False,
        bot_type: BotTypeLiteral | None = None,
):
    logger = JSONLogger(
        "payments.get_available_payments", {
            "brand": brand,
            "store_id": store_id,
            "lang": lang,
        },
    )

    payment_methods = await crud.get_payment_methods(
        brand_id=brand.id,
        store_id=store_id,
        with_translations=True,
        lang=lang,
        for_payment_list=True,
        shipment_id=shipment_id,
        invoice_template_id=invoice_template_id,
        only_online=only_online,
    )

    logger.debug(
        "payment_methods",
        {"payment_methods_length": len(payment_methods) if payment_methods else None}
    )

    qr_pmt_methods = set([ps.payment_method for ps, _, _ in payment_methods]) & set(
        QR_PAYMENT_METHODS
    )
    email_required = []

    payments_count = 0
    single_payment_settings = None
    single_payment_object_settings = None
    methods = []
    available_telegram = False

    payments_to_translate = []
    for payment_setting, translation, object_payment_setting in payment_methods:
        if (object_payment_setting and object_payment_setting.is_enabled and
                payment_setting and payment_setting.is_enabled):
            payments_to_translate.append((payment_setting, translation))

    translated_dict = await get_translated_payment_settings(
        payments_to_translate, lang, brand.group_id
    )

    for payment_setting, translation, object_payment_setting in payment_methods:
        if not payment_setting or not payment_setting.is_enabled:
            continue

        # skip friend payment method for fastpay in bot
        if payment_setting.payment_method == "friend" and bot_type:
            continue

        if payment_setting.payment_method == "tg_pay":
            if bot_type and bot_type != "telegram":
                continue

            available_telegram = True

        single_payment_settings = payment_setting
        single_payment_object_settings = object_payment_setting
        payments_count += 1

        for pm in PAYMENT_METHODS_EMAIL_REQUIRED:
            if pm == payment_setting.payment_method:
                email_required.append(pm)

        ewallet = None
        if payment_setting.payment_method == "ewallet":
            if not payment_setting.json_data.get("ewallet_id"):
                continue

            ewallet = (
                await EWallet.get(
                    payment_setting.json_data["ewallet_id"], is_enabled=True,
                    is_deleted=False
                )
            )
            if not ewallet:
                continue
            if ewallet and ewallet.is_private:
                if not await crud.check_access_user_to_ewallet(
                        ewallet.id, user.id if user else None
                ):
                    continue

        media_url = None
        if payment_setting.media_id:
            media = await MediaObject.get(payment_setting.media_id)
            if media:
                media_url = media.url

        if not media_url and ewallet and ewallet.media_id:
            media = await MediaObject.get(ewallet.media_id)
            if media:
                media_url = media.url

        if not media_url:
            media_url = get_icon_for_payment(payment_setting.payment_method)

        if object_payment_setting and object_payment_setting.json_data:
            if payment_setting.payment_method == "incust_pay":
                fee_value = object_payment_setting.json_data.get(
                    "charge_fixed", None
                )
                fee_percent = object_payment_setting.json_data.get(
                    "charge_percent", None
                )
            else:
                fee_value = object_payment_setting.json_data.get(
                    "payer_fee_value", None
                )
                fee_percent = object_payment_setting.json_data.get(
                    "payer_fee_percent", None
                )
        else:
            if payment_setting.payment_method == "incust_pay":
                fee_value = payment_setting.json_data.get(
                    "charge_fixed", None
                ) if payment_setting.json_data else None
                fee_percent = payment_setting.json_data.get(
                    "charge_percent", None
                ) if payment_setting.json_data else \
                    None
            else:
                fee_value = payment_setting.json_data.get(
                    "payer_fee_value", None
                ) if payment_setting.json_data else None
                fee_percent = payment_setting.json_data.get(
                    "payer_fee_percent", None
                ) if payment_setting.json_data else \
                    None

        translated_payment = translated_dict.get(payment_setting.id) or {}

        payment_name = await get_payment_method_name(
            payment_setting, lang, translated_payment,
            is_qr_menu, ewallet,
            for_messanger=bool(bot_type)
        )

        payment_description = (
                translated_payment.get("description") or
                payment_setting.description
        )
        payment_label_comment = (
                translated_payment.get("label_comment") or
                payment_setting.label_comment
        )

        if (
                ewallet and
                incust_account_id and
                ewallet.incust_account_id == incust_account_id
        ):
            continue

        incust_pay = None
        if payment_setting.payment_method in ("incust_pay", "ewallet"):
            incust_pay_service = IncustPayService(
                brand, user, lang
            )
            invoice_template = None
            if invoice_template_id:
                invoice_template = await InvoiceTemplate.get(
                    id=invoice_template_id, is_deleted=False
                )
            incust_pay = await incust_pay_service.get_incust_pay_payment_data(
                store_id=store_id,
                none_on_empty=True,
                is_topup=False,
                payment_settings_id=payment_setting.id,
                ewallet=ewallet,
                payment_settings=payment_setting,
                ignore_incust_account_id=incust_account_id,
                object_payment_settings_id=object_payment_setting.id if
                object_payment_setting else None,
                invoice_template=invoice_template,
            )

        methods.append(
            schemas.PaymentMethodSchema(
                provider=payment_setting.payment_method,
                logo=media_url,
                fee_value=fee_value,
                fee_percent=fee_percent,
                name=payment_name,
                is_online=payment_setting.is_online,
                desc=payment_description,
                settings_id=payment_setting.id,
                object_settings_id=(
                    object_payment_setting.id
                    if object_payment_setting
                    else None
                ),
                need_comment=payment_setting.with_comment,
                label_comment=payment_label_comment,
                incust_pay_data=incust_pay,
                ewallet=await ewallet_to_schema(ewallet) if ewallet else None
            )
        )

    multiple_payments = payments_count > 1

    logger.debug("result payment_methods", {"methods": methods})

    res = schemas.AvailablePaymentSchema(
        is_multiple_payment_methods=multiple_payments,
        single_payment_method=schemas.PaymentMethodSchema(
            settings_id=single_payment_settings.id,
            object_settings_id=(
                single_payment_object_settings.id
                if single_payment_object_settings
                else None
            ),
            need_comment=single_payment_settings.with_comment,
            provider=single_payment_settings.payment_method,
            is_online=single_payment_settings.is_online,
        ) if single_payment_settings and not multiple_payments else None,
        available_telegram=available_telegram,
        qr_pmt_methods=qr_pmt_methods,
        email_required=set(email_required),
        methods=methods,
    )

    for pm in res.methods:
        if "epay" == pm.provider:
            data = await crud.get_payment_data(
                payment_settings_id=pm.settings_id, payment_method="epay",
                brand_id=brand.id, store_id=store_id,
                invoice_template_id=invoice_template_id,
            )
            client_id = data.get("client_id")
            res.is_epay_test_mode = client_id == "test"

    return res


async def get_payment_method_name(
        payment_settings: PaymentSettings,
        lang: str,
        translated_payment: dict | None = None,
        is_qr_menu: bool = False,
        ewallet: EWallet | None = None,
        for_messanger: bool = False,
):
    if not isinstance(translated_payment, dict):
        translated_payment = {}

    payment_name = (
            translated_payment.get("name") or
            payment_settings.name
    )

    if not payment_name or payment_name == payment_settings.payment_method:
        if is_qr_menu and payment_settings.payment_method == "cash":
            payment_name = await f("store payment later text", lang)
        elif payment_settings.payment_method == "ewallet":
            if not ewallet:
                ewallet = await EWallet.get(
                    payment_settings.json_data.get("ewallet_id")
                )
                if not ewallet:
                    raise HTTPException(404, "ewallet not found...")

            payment_name = ewallet.name
        else:
            payment_name = await get_payment_default_name(
                payment_settings.payment_method, lang,
                payment_settings.is_online,
                for_messanger=for_messanger,
            )

    return payment_name
