import math

from aiohttp import ClientConnectorError, ContentTypeError, ServerTimeoutError
from fastapi import HTTPException
from incust_api.api import term

import exceptions
import schemas
from config import (
    EXTERNAL_PAYMENT_METHODS, NO_CENT_CURRENCIES, PAYMENT_METHODS_EMAIL_REQUIRED,
    PAYMENT_METHOD_CURRENCIES,
    PMT_CLIENT_REDIRECT_CALLBACK_URL,
    PMT_SERVER_CALLBACK_URL, QR_PAYMENT_METHODS,
)
from core.admin_notification.service import create_system_notification
from core.invoice.exception import InvoicePaymentInvoicePayedError
from core.loyalty.incust_api import incust
from core.payment.exceptions import (
    BasePaymentError, PaymentCreateError, PaymentInvalidStatusError,
    PaymentNotifyAdminExceptionError,
    PaymentStoreOrderPayedError,
)
from core.payment.funcs import (
    calc_payer_fee_data, create_or_update_payer_fee, get_user_info,
    make_client_return_url,
)
from core.payment.incust_pay.service import IncustPayService
from core.payment.payment_processor import PaymentProcessor
from core.payment.payment_processor.providers.ewallet.client import \
    get_ewallet_by_payment_data
from core.store.functions.order import make_invoice_for_order
from core.store.order.service import change_store_order_status
from db import crud
from db.models import (
    Brand, ClientBot, Group, Invoice, ObjectPaymentSettings, Payment, PaymentSettings,
    StoreOrder, StoreOrderPayment,
    User,
)
from loggers import JSONLogger
from schemas import SystemNotificationCategory, SystemNotificationType
from utils.email_funcs import is_valid_email
from utils.numbers import calculate_amount_modifiers, format_currency
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f


class PaymentsService:
    def __init__(
            self,
            brand: Brand,
            user: User,
            lang: str,
            order: StoreOrder | None = None,
            invoice: Invoice | None = None,
            bot: ClientBot | None = None,
    ):
        if not any((order, invoice)):
            raise ValueError("order or invoice must be provided")

        self.brand = brand
        self.user = user
        self.lang = lang
        self.order = order
        self.invoice = invoice
        self.bot = bot

    @property
    def order_id(self):
        return self.order.id if self.order else None

    @property
    def invoice_id(self):
        return self.invoice.id if self.invoice else None

    @property
    def logger_data(self):
        return {
            "brand": self.brand,
            "user": self.user,
            "lang": self.lang,
            "order": self.order,
            "invoice": self.invoice,
        }

    async def pre_payment(self, data: schemas.NewPrePaymentData):
        payment_settings = await PaymentSettings.get(data.payment_settings_id)
        # validation for already paid order/invoice
        if (
                (self.order and self.order.status_pay == "payed") or
                (self.invoice and self.invoice.status == "payed")
        ):
            if payment_settings.payment_method == "ewallet":
                return None, None, None, None
            if self.order:
                raise PaymentStoreOrderPayedError(store_order_id=self.order.id)
            elif self.invoice:
                raise InvoicePaymentInvoicePayedError(invoice_id=self.invoice.id)

        if (
                self.order and
                self.order.status in ("canceled", "closed")
        ):
            raise PaymentInvalidStatusError(self.order.status)

        if not payment_settings:
            raise exceptions.PaymentMethodNotFoundError(data.payment_settings_id)

        object_payment_settings = await ObjectPaymentSettings.get(
            data.object_payment_settings_id
        )

        if not object_payment_settings and data.object_payment_settings_id:
            raise exceptions.ObjectPaymentMethodNotFoundError(
                data.object_payment_settings_id
            )

        logger = JSONLogger(
            "payments", "Pre Payment", self.logger_data, {
                "data": data,
                "payment_settings": payment_settings.as_dict(),
                "object_payment_settings": (
                    object_payment_settings.as_dict()
                    if object_payment_settings
                    else None
                ),
            },
        )

        try:
            # reopen incust transaction if previous was cancelled (because of timeout)
            if (
                    self.order and
                    self.order.loyalty_type == "incust" and
                    self.order.auto_cancelled_loyalty_transaction
            ):
                await self.reopen_incust_transaction(logger)

            if object_payment_settings and object_payment_settings.json_data:
                json_data = object_payment_settings.json_data
            else:
                json_data = payment_settings.json_data or {}

            # creating invoice for order, if needed
            if self.order and payment_settings.is_online:
                order_invoice = await self.get_or_create_order_invoice(data.incust_check)
            else:
                order_invoice = None

            # creating or updating StoreOrderPayment for order/invoice
            if not data.skip_process_order_payment:
                await self.process_order_payment(
                    payment_settings,
                    json_data,
                    data.comment,
                    order_invoice,
                )

            if (
                    self.order and
                    self.order.status == "new" and
                    (
                            not payment_settings.is_online or
                            payment_settings.payment_method == "friend"
                    )
            ):
                await change_store_order_status(
                    self.order,
                    schemas.OrderShippingStatusEnum.OPEN_UNCONFIRMED.value,
                    "user",
                    None,
                    "order_api",
                    initiated_by_user=self.user,
                )
        except Exception as err:
            logger.error("ERROR", err)
            raise
        else:
            logger.debug("SUCCESS")

        return payment_settings, object_payment_settings, json_data, order_invoice

    async def process_order_payment(
            self, payment_settings: PaymentSettings,
            json_data: dict,
            comment: str | None = None,
            order_invoice: Invoice | None = None,
    ):
        # defining data for StoreOrderPayment object
        order_payment_data = {
            "payment_method": payment_settings.payment_method,
            "comment": comment,
            "json_data": json_data,
            "name": payment_settings.name,
            "description": payment_settings.description,
            "payment_settings_id": payment_settings.id,
            "label_comment": payment_settings.label_comment,
            "post_payment_info": payment_settings.post_payment_info,
        }

        # defining data for retrieving and creating StoreOrderPayment object
        order_payment_object_data = {
            "order_id": self.order_id,
            "invoice_id": self.invoice_id,
        }

        # retrieving StoreOrderPayment for order or invoice
        order_payment = await StoreOrderPayment.get(
            **order_payment_object_data,
        )

        # if pre-payment is made for a friend
        if (
                self.order and
                order_payment and
                order_payment.payment_method == "friend" and
                (
                        (
                                self.order and self.user.id !=
                                self.order.user_id) or
                        (
                                self.invoice and self.user.id ==
                                self.invoice.user_id)
                ) and
                (payment_invoice := order_invoice or self.invoice)
        ):
            # retrieving StoreOrderPayment for order's invoice or just invoice
            order_payment = await StoreOrderPayment.get(
                invoice_id=payment_invoice.id,
            )

            # overriding payment data, because payment has to be created
            order_payment_object_data["order_id"] = None
            order_payment_object_data["invoice_id"] = payment_invoice.id

        # create a new StoreOrderPayment object for order/invoice
        if not order_payment:
            await crud.create_store_order_payment(
                **order_payment_object_data,
                **order_payment_data,
            )
        # update an existing StoreOrderPayment object
        else:
            await order_payment.update(**order_payment_data)

    async def get_or_create_order_invoice(self, incust_check: term.m.Check | None = None):
        if self.order.invoice_id:
            return await Invoice.get(self.order.invoice_id)
        return await make_invoice_for_order(self.order, self.lang, self.bot, incust_check)

    async def reopen_incust_transaction(self, logger: JSONLogger):
        try:
            loyalty_settings = await crud.get_loyalty_settings_for_context(
                "store",
                schemas.LoyaltySettingsData(
                    brand_id=self.brand.id,
                    store_id=self.order.store_id,
                    profile_id=self.brand.group_id,
                )
            )

            if loyalty_settings and self.order.invoice_id:
                try:
                    # Отримуємо інвойс для доступу до структурованих даних лояльності
                    invoice = await Invoice.get(self.order.invoice_id)
                    if not invoice or not invoice.incust_transaction_id:
                        logger.debug("No invoice or transaction_id found")
                        await self.order.update(
                            auto_cancelled_loyalty_transaction=False
                        )
                        return

                    # Отримуємо дані чеку з інвойсу замість deprecated
                    # order.original_incust_loyalty_check
                    # Використовуємо структуровані поля інвойсу
                    invoice_items = await crud.get_invoice_items(invoice.id)
                    check_items = []
                    for invoice_item in invoice_items:
                        check_items.append(
                            {
                                "title": invoice_item.name,
                                "code": invoice_item.item_code or invoice_item.name,
                                "price": round(invoice_item.final_price / 100, 2),
                                "quantity": invoice_item.quantity,
                                "amount": round(invoice_item.final_sum / 100, 2),
                                "category": invoice_item.category,
                            }
                        )

                    # Створюємо чек з структурованих даних інвойсу
                    new_check = term.m.Check(
                        amount=round(
                            (invoice.loyalty_amount or invoice.sum_to_pay) / 100, 2
                        ),
                        amount_to_pay=round(
                            (invoice.loyalty_amount_to_pay or invoice.sum_to_pay) / 100,
                            2
                        ),
                        check_items=check_items,
                        payment_id="currency",  # Базовий тип платежу для відновлення
                        payment_type="currency",
                        skip_message=invoice.loyalty_skip_message or True,
                    )

                    async with incust.term.CheckTransactionsApi(
                            loyalty_settings
                    ) as api:
                        # Використовуємо тільки reserve (без pre_auth як зазначено)
                        response = await api.reserve_check(new_check)

                    if response:
                        # Оновлюємо transaction_id в інвойсі та скидаємо прапор
                        # скасування
                        transaction_id = getattr(response, 'id', None) or getattr(
                            response, 'transaction_id', None
                        )
                        if transaction_id:
                            await invoice.update(incust_transaction_id=transaction_id)

                        await self.order.update(
                            auto_cancelled_loyalty_transaction=False
                        )
                        logger.debug(
                            "Transaction reopened successfully", {
                                "new_transaction_id": transaction_id,
                                "invoice_id": invoice.id
                            }
                        )
                    else:
                        logger.error("No response from reopen transaction")
                        await self.order.update(
                            auto_cancelled_loyalty_transaction=False
                        )

                except Exception as api_error:
                    logger.error(
                        "Failed to reopen transaction via new API",
                        {"error": str(api_error)}
                    )
                    # Якщо не вдалося відновити, все ж таки оновлюємо статус
                    await self.order.update(auto_cancelled_loyalty_transaction=False)
            else:
                logger.debug("No loyalty settings or transaction_id found")
                await self.order.update(auto_cancelled_loyalty_transaction=False)
        except Exception as ex:
            logger.error("reopen_incust_transaction error", ex)

    async def make_provider_payment(self, data: schemas.MakePaymentData):
        payment_settings, object_payment_settings, json_data, order_invoice = await (
            self.pre_payment(
                data
            )
        )

        logger = JSONLogger(
            "payments", "Make Provider Payment", self.logger_data, {
                "data": data,
                "payment_settings": payment_settings.as_dict(),
                "object_payment_settings": (
                    object_payment_settings.as_dict()
                    if object_payment_settings
                    else None
                ),
            },
        )

        if not any(
                [
                    payment_settings,
                    object_payment_settings,
                    json_data,
                    order_invoice
                ]
        ):
            logger.error(
                f"make_provider_payment: pre_payment returned None values. "
                f"payment_settings_id={getattr(data, 'payment_settings_id', None)}, "
                f"object_payment_settings_id="
                f"{getattr(data, 'object_payment_settings_id', None)}, "
                f"order_invoice={order_invoice}, user_id="
                f"{getattr(self.user, 'id', None)}"
            )
            raise HTTPException(
                status_code=400,
                detail="Cannot process payment: pre_payment returned None values. "
                       "Possible reasons: invoice/order already paid, or invalid "
                       "payment settings."
            )

        invoice: Invoice = order_invoice or self.invoice

        if (
                invoice.invoice_type == schemas.InvoiceTypeEnum.INTEGRATION and
                invoice.client_redirect_url
        ):
            return_url = invoice.client_redirect_url
        else:
            return_url = await make_client_return_url(
                self.brand, self.lang,
                self.bot, self.order, invoice,
                data.is_webview,
            )

        amount_to_pay = self.order.sum_to_pay if self.order else invoice.sum_to_pay

        is_loyalty = bool(
            self.order.original_incust_loyalty_check
            if self.order else
            invoice.incust_check
        )

        if payment_settings.payment_method == "ewallet" and not is_loyalty:
            ewallet = await get_ewallet_by_payment_data(json_data)

            ewallet_discount_percent = ewallet.discount_percent
            ewallet_discount_amount = calculate_amount_modifiers(
                amount_to_pay,
                ewallet_discount_percent,
            )

            amount_to_pay -= ewallet_discount_amount
        else:
            ewallet = None
            ewallet_discount_percent = None
            ewallet_discount_amount = None

        user_name, user_email, user_phone, user_address = await get_user_info(
            self.user, self.order, invoice
        )

        if data.user_phone:
            user_phone = data.user_phone

        logger.add_data(
            {
                "user_name": user_name,
                "user_email": user_email,
                "user_phone": user_phone,
                "user_address": user_address,
            }
        )

        payment = await crud.get_payment_by_invoice_and_payment_settings(
            invoice_id=invoice.id,
            payment_settings_id=data.payment_settings_id,
            object_payment_settings_id=data.object_payment_settings_id,
        )

        if payment and payment.status != "pending":
            payment = None

        if payment and payment.payment_data and "data" in payment.payment_data:
            payment_data = payment.payment_data.get("data")
            if not payment_data.get("error"):
                return payment.payment_data
            payment = None

        if not payment:
            payment = await Payment.create(
                invoice_id=invoice.id, payment_method="",
                amount=amount_to_pay, status="pending",
                currency=invoice.currency, return_url=return_url,
                user_id=self.user.id, brand_id=self.brand.id,
                store_id=self.order.store_id if self.order else None,
                payment_settings_id=data.payment_settings_id,
                object_payment_settings_id=data.object_payment_settings_id,
                ewallet_discount_percent=ewallet_discount_percent,
                ewallet_discount_amount=(
                    round(ewallet_discount_amount / 100, 2)
                    if ewallet_discount_amount is not None
                    else None
                ),
            )

        if not payment:
            raise PaymentCreateError()

        logger.add_data({"payment": payment})

        server_url = (
            f"{PMT_SERVER_CALLBACK_URL}/"
            f"{payment_settings.payment_method}/"
            f"{payment.uuid_id}"
        )
        client_return_url = f"{PMT_CLIENT_REDIRECT_CALLBACK_URL}/{payment.id}"

        logger.debug(
            "payment urls", {
                "server_url": server_url,
                "client_return_url": client_return_url,
            }
        )

        store_id = self.order.store_id if self.order else None

        if payment_settings.payment_method == "incust_pay":
            incust_pay_service = IncustPayService(
                self.brand,
                self.user,
                self.lang,
                self.bot,
            )
            incust_pay = await incust_pay_service.get_incust_pay_payment_data(
                payment, store_id, none_on_empty=True, order=self.order
            )

            return {
                "type": "incust_pay",
                "data": incust_pay,
                "amount_to_pay": round(amount_to_pay / 100, 2),
            }

        kwargs = {
            "invoice": invoice,
            "order": self.order,
            "payment": payment,
            "amount_to_pay": amount_to_pay,
            "brand": self.brand,
            "bot": self.bot,
            "brand_id": self.brand.id,
            "store_id": store_id,
            "user": self.user,
            "lang": self.lang,
            "success_url": client_return_url,
            "server_url": server_url,
            "user_email": user_email, "user_name": user_name, "user_phone": user_phone,
            "user_address": user_address,
            "payment_settings_id": payment_settings.id,
            "args": data.args,
            "ewallet": ewallet,
        }

        if (
                PAYMENT_METHOD_CURRENCIES.get(payment_settings.payment_method) and
                (
                        invoice.currency not in
                        (PAYMENT_METHOD_CURRENCIES[payment_settings.payment_method])
                )
        ):
            ...  # TODO: raise error

        if (
                payment_settings.payment_method in PAYMENT_METHODS_EMAIL_REQUIRED and
                user_email and not is_valid_email(user_email)
        ):
            ...  # TODO: raise error

        group = await Group.get(self.brand.group_id)

        fees = ""

        if payment_settings.payment_method == "incust_pay":
            payer_fee_value = json_data.get("charge_fixed", 0)
            payer_fee_percent = json_data.get("charge_percent", 0)
        else:
            payer_fee_value = json_data.get("payer_fee_value", 0)
            payer_fee_percent = json_data.get("payer_fee_percent", 0)

        # calc service fee without tips
        payer_fee_data = calc_payer_fee_data(
            amount_to_pay,
            invoice.currency,
            str(payer_fee_percent), str(payer_fee_value)
        )

        if payer_fee_data:
            await create_or_update_payer_fee(
                payment_settings.payment_method,
                invoice.id,
                payer_fee_data,
                "pending",
            )

            # Оновлюємо поле payer_fee в інвойсі
            await invoice.update(payer_fee=payer_fee_data.fee)
            logger.debug(
                f"Updated invoice.payer_fee: {payer_fee_data.fee} for invoice "
                f"{invoice.id}"
            )

            kwargs["amount_to_pay"] = amount_to_pay + payer_fee_data.fee
            formatted_fee = format_currency(
                (
                    round(payer_fee_data.fee / 100, 2)
                    if invoice.currency not in NO_CENT_CURRENCIES else
                    math.ceil(payer_fee_data.fee / 100)
                ),
                invoice.currency,
                locale=group.lang,
            )
            fees = f"{await f('payer fee receipt text', self.lang)}: {formatted_fee}"

        credentials = await crud.get_payment_data(
            payment_settings_id=payment_settings.id,
            payment_method=payment_settings.payment_method,
            brand_id=self.brand.id,
            store_id=store_id,
            invoice_template_id=invoice.invoice_template_id,
        )

        kwargs["credentials"] = credentials

        processor = PaymentProcessor(payment_settings.payment_method)
        payment_data = {}
        try:
            payment_data = await processor.create_payment(**kwargs)
            if not payment_data:
                logger.error(
                    f"make_provider_payment: processor.create_payment returned None. "
                    f"payment_settings_id="
                    f"{getattr(data, 'payment_settings_id', None)}, "
                    f"object_payment_settings_id="
                    f"{getattr(data, 'object_payment_settings_id', None)}, "
                    f"invoice_id={getattr(invoice, 'id', None)}, user_id="
                    f"{getattr(self.user, 'id', None)}"
                )
                raise HTTPException(
                    status_code=400,
                    detail="Cannot process payment: failed to create payment data. "
                           "Possible reasons: payment provider error or invalid data."
                )
        except Exception as err:
            await self.process_make_payment_error(
                err, group, invoice,
                payment, payment_settings,
                kwargs, payment_data, logger
            )

        if payment_data and "error" not in payment_data:
            if payment_settings.payment_method in QR_PAYMENT_METHODS:
                payment_data["is_qr"] = True
            if payment_settings.payment_method in EXTERNAL_PAYMENT_METHODS:
                payment_data["is_external"] = True
            if payment_settings.payment_method in PAYMENT_METHODS_EMAIL_REQUIRED:
                payment_data["is_email_required"] = True

            if payment_data and fees:
                if "description" in payment_data:
                    payment_data["description"] += "\n" + fees
                else:
                    payment_data["description"] = fees

        await invoice.update(payer_id=self.user.id)
        await payment.update(
            payment_data={
                "type": payment_settings.payment_method,
                "data": payment_data,
                "amount_to_pay": round(amount_to_pay / 100, 2),
            },
            payment_setting=payment_settings,
            object_payment_settings=object_payment_settings,
        )
        return {
            "type": payment_settings.payment_method,
            "data": payment_data,
            "amount_to_pay": round(amount_to_pay / 100, 2),
        }

    async def process_make_payment_error(
            self,
            err: Exception,
            group: Group,
            invoice: Invoice,
            payment: Payment,
            payment_setting: PaymentSettings,
            kwargs: dict,
            payment_data: dict,
            logger: JSONLogger,
    ):
        logger.error("Create Payment error", err)
        if isinstance(err, PaymentNotifyAdminExceptionError):
            text_error = await f(
                "error make payment admin message", group.lang, error=err.message,
                order_id=self.order_id,
                invoice_id=invoice.id,
                brand_id=self.brand.id,
                brand_name=self.brand.name,
                store_id=self.order.store_id if self.order else None,
                user=f"@{self.user.username}" if self.user.username else self.user.name,
                payment_method=payment_setting.payment_method,
            )
            await create_system_notification(
                "profile:edit",
                group_id=invoice.group_id, category=SystemNotificationCategory.PAYMENT,
                type_notification=SystemNotificationType.MAKE_PAYMENT_ERROR,
                title="Make payment error", content=text_error
            )

        if isinstance(err, BasePaymentError):
            payment_data["error"] = await f(
                err.text_variable, self.lang,
                message=err.message if getattr(err, "message", "") else "",
                **err.text_kwargs
            )
        else:
            if isinstance(err, ClientConnectorError | ServerTimeoutError):
                payment_data["error"] = await f(
                    'payment provider timeout error', self.lang
                )
            elif isinstance(err, ContentTypeError):
                payment_data["error"] = await f(
                    "payment provider content type error", self.lang
                )
            else:
                payment_data["error"] = await f(
                    "payment provider unexpected error", self.lang
                )
            await send_message_to_platform_admins(
                f'''Make payment error:

                        {payment_setting.payment_method=}

                        error: {err}

                        {payment.amount=}
                        {invoice.currency=}

                        {kwargs=}
                        '''
            )
