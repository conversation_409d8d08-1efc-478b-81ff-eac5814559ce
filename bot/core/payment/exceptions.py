from abc import ABC

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BasePaymentError(ErrorWithHTTPStatus, ABC, base=True):
    def __init__(
            self, message: str = "Payment error",
            context: dict | None = None, **kwargs
    ):
        self.message = message
        self.context = context
        super().__init__(**kwargs)

    def __repr__(self):
        return f"Payment error: {self.message}"

    def __str__(self):
        return f"{self.message}"


class PaymentUnknownError(BasePaymentError):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    text_variable = "payment unknown error"

    def __init__(self, **kwargs):
        super().__init__("payment unknown error", **kwargs)


class PaymentSendMessageError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment message error"


class PaymentCreateError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment create error"


class PaymentInvoicePaidError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "invoice is already paid"

    def __init__(self, invoice_id: int):
        self.invoice_id = invoice_id
        super().__init__(
            invoice_id=invoice_id,
        )


class PaymentStoreOrderPayedError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store order already payed error"

    def __init__(self, store_order_id: int):
        self.order_id = store_order_id
        super().__init__(order_id=self.order_id)


class PaymentInvalidStatusError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment invalid status error"

    def __init__(self, status_: str | None = None):
        self.status = status_ if status_ is not None else ""
        super().__init__(status=status_)


class PaymentResponseInvalidStatusError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment response invalid status error"

    def __init__(self, status_: int | None = None):
        self.status = status_ if status_ is not None else ""
        super().__init__(status=f"{self.status}")


class PaymentWaveApiKeyError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment wave response api key error"

    def __init__(self, message: str | None = None, **kwargs):
        super().__init__(message=message, **kwargs)


class PaymentResponseCommonError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment response common error"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class PaymentMiniAmountRequiredError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "minimum amount required error text"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class PaymentStoreOrderClosedError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store order pay closed error"

    def __init__(self, store_order_id: int):
        self.order_id = store_order_id
        super().__init__(order_id=self.order_id)


class PaymentStoreOrderCanceledError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store order pay canceled error"

    def __init__(self, store_order_id: int):
        self.order_id = store_order_id
        super().__init__(order_id=self.order_id)


class PaymentInvoiceTemplateNotFoundError(BasePaymentError):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "payment invoice template not found error"

    def __init__(self, invoice_template_id: int):
        super().__init__(
            f"Invoice template with id {invoice_template_id} not found",
            invoice_template_id=invoice_template_id,
        )


class PaymentTokenRequiredError(BasePaymentError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = 'payment token required error'

    def __init__(self, message: str = ""):
        super().__init__(f"token required {message}".strip())


class PaymentNotAllowedError(BasePaymentError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "payment not allowed error"

    def __init__(self, message: str = ""):
        super().__init__(f"payment not allowed {message}".strip())


class PaymentTokenInvalidError(BasePaymentError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "payment token invalid error"

    def __init__(self, message: str = ""):
        super().__init__(f"payment token invalid error, {message}".strip())


class PaymentInvoiceNotFoundError(BasePaymentError):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "payment invoice not found error"

    def __init__(self, message: str = ""):
        super().__init__(f"payment invoice not found {message}".strip())


class PaymentSendToFriendError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "web add friend set error text"

    def __init__(self, friend_name: str = ""):
        self.friend_name = friend_name
        super().__init__(friend_name=self.friend_name)


class PaymentInvalidCallBackDataError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment invalid callback data error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentInvalidReferenceError(BasePaymentError):
    status_code = 400
    text_variable = "payment invalid reference error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentInvalidUrlError(BasePaymentError):
    status_code = 400
    text_variable = "payment invalid url error"

    def __init__(self, detail: str | None = None):
        super().__init__(detail=detail if detail else "")


class PaymentStripeInvalidReferenceError(BasePaymentError):
    status_code = 400
    text_variable = "payment stripe invalid reference error"

    def __init__(self, ):
        super().__init__(self.text_variable)


class PaymentStripeWrongSystemError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment stripe wrong system error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentStripeNotFoundPaymentError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment stripe not found payment error"

    def __init__(self, ):
        super().__init__(self.text_variable)


class PaymentExpiredError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment expired error"


class PaymentStripeInvalidTypeError(PaymentSendMessageError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment stripe invalid type error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentStripeIntentError(PaymentSendMessageError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment stripe intent error"

    def __init__(self, ):
        super().__init__(self.text_variable)


class PaymentNotFoundCredentialsError(PaymentSendMessageError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment not found payment data error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentSettingsNotFoundError(PaymentSendMessageError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment settings not found error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentGetSignatureDataError(PaymentSendMessageError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment get signature data error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentCheckSignatureDataError(PaymentSendMessageError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment check signature data error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentCheckSignatureError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment check signature data error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentCheckUuidsError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment check uuids error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentNotFoundPaymentDataByUuidError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment not found payment data by uuid error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}: {detail}")


class PaymentInvalidPayloadError(PaymentSendMessageError):
    text_variable = "payment invalid payload error"

    def __init__(self, detail: str | None = None):
        self.detail = detail if detail is not None else ""
        super().__init__(detail=self.detail)


class PaymentInvalidResponseError(PaymentSendMessageError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment invalid response error"

    def __init__(self, detail: str | None = None):
        self.detail = detail if detail is not None else ""
        super().__init__(detail=self.detail)


class PaymentProviderInvalidError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment provider invalid error"


class PaymentFailedError(BasePaymentError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment failed error"


class LiqpayRroUnknownError(PaymentSendMessageError):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    text_variable = "liqpay fiscal unknown error"


class BaseLiqpayRroError(PaymentSendMessageError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "liqpay fiscal base error"
    #
    # def __init__(self, detail: str | None = None):
    #     self.detail = detail


class LiqpayRroDataNotFoundError(BaseLiqpayRroError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "liqpay fiscal data not found"


class LiqpayRroTaxListNotSetError(BaseLiqpayRroError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "liqpay fiscal tax list not set error"


class LiqpayRroIsNeedFiscalNotSetError(BaseLiqpayRroError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "liqpay fiscal is need fiscal not set error"


class LiqpayRroDataNotValidError(BaseLiqpayRroError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "liqpay fiscal data not valid"

    def __init__(self, total_amount_fiscal: float, payment_amount: float):
        super().__init__(
            f"{self.text_variable} {total_amount_fiscal=} {payment_amount=}",
            total_amount_fiscal=total_amount_fiscal,
            payment_amount=payment_amount
        )


class LiqpayRroIdProductError(BaseLiqpayRroError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment liqpay id product error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}", detail=detail)


class LiqpayRroIdTipsError(BaseLiqpayRroError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment liqpay id tips error"


class LiqpayRroIdShipmentError(BaseLiqpayRroError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment liqpay id shipment error"

    def __init__(self, detail: str | None = None):
        super().__init__(f"{self.text_variable}", detail=detail)


class CreateOrUpdateOrderPaymentError(BaseLiqpayRroError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "payment create or update order payment error"


class LiqpayUnitNameError(BaseLiqpayRroError):
    text_variable = "liqpay unit name error"

    def __init__(self, product_id: str, liqpay_unit_name: str):
        super().__init__(
            product_id=product_id,
            liqpay_unit_name=liqpay_unit_name,
        )


class LiqpayUnitNameEmptyError(BaseLiqpayRroError):
    text_variable = "liqpay unit name empty error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            product_id=product_id,
            product_name=product_name,
        )


class LiqpayVndCodeError(BaseLiqpayRroError):
    text_variable = "liqpay vnd code error"

    def __init__(self, product_name: str):
        super().__init__(
            product_name=product_name,
        )


class LiqpayPriceEmptyError(BaseLiqpayRroError):
    text_variable = "liqpay price empty error"
    status_code = status.HTTP_400_BAD_REQUEST

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            product_id=product_id,
            product_name=product_name,
        )


class PaymentNotifyAdminExceptionError(Exception):
    def __init__(self, message, error_code: int | str | None = None):
        super().__init__(message)
        self.error_code = error_code
        self.message = message


class PaymentPhoneRequiredError(BasePaymentError):
    status_code = 400
    text_variable = "payment phone required error"

    def __init__(self, ):
        super().__init__(self.text_variable)