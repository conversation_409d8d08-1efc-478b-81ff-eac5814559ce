import logging
from typing import Any, Literal


class PaymentLogger:
    def __init__(self):
        self._debugger = logging.getLogger("debugger.payments")
        self._error = logging.getLogger("error.payments")

    def log(
        self, level: Literal["debug", "error"],
        func_name: str,
        profile_id: int,
        invoice_id: int | None = None,
        order_id: int | None = None,
        store_id: int | None = None,
        user_id: int | None = None,
        reason: str | None = None,
        payment_settings_id: int | None = None,
        payment_method: str | None = None,
        token_data: str | None = None,
        extra_info: str | None = None,
        exc_info: Any = None
    ):
        first_line_parts = [
            f"{func_name}: {reason if reason else 'exception'}",
            f"profile_id: {profile_id}"
        ]
        if order_id:
            first_line_parts.append(f"order_id: {order_id}")
        if invoice_id:
            first_line_parts.append(f"invoice_id: {invoice_id}")
        if invoice_id:
            first_line_parts.append(f"store_id: {store_id}")
        if user_id:
            first_line_parts.append(f"user_id: {user_id}")
        if payment_settings_id:
            first_line_parts.append(f"payment_settings_id: {payment_settings_id}")
        if payment_method:
            first_line_parts.append(f"payment_method: {payment_method}")
        if token_data:
            first_line_parts.append(f"token_data: {token_data}")
        if extra_info:
            first_line_parts.append(extra_info)
        first_line = ": ".join(first_line_parts) if first_line_parts else []
        log_str = f"{first_line}"

        kwargs = {}
        if exc_info:
            kwargs["exc_info"] = exc_info
        if level == "debug":
            self._debugger.debug(log_str, **kwargs)
        else:
            self._error.error(log_str, **kwargs)

    def debug(
        self, func_name: str,
        profile_id: int,
        invoice_id: int | None = None,
        order_id: int | None = None,
        store_id: int | None = None,
        user_id: int | None = None,
        reason: str | None = None,
        payment_settings_id: int | None = None,
        payment_method: str | None = None,
        token_data: str | None = None,
        extra_info: str = "",
        exc_info: Any = None
    ):
        self.log(
            "debug", func_name,
            profile_id, invoice_id, order_id, store_id, user_id, reason, payment_settings_id, payment_method,
            token_data, extra_info, exc_info=exc_info,
        )

    def error(
        self, func_name: str,
        profile_id: int,
        invoice_id: int | None = None,
        order_id: int | None = None,
        store_id: int | None = None,
        user_id: int | None = None,
        reason: str | None = None,
        payment_settings_id: int | None = None,
        payment_method: str | None = None,
        token_data: str | None = None,
        extra_info: str | None = None,
        exc_info: Any = True
    ):
        self.log(
            "error", func_name,
            profile_id, invoice_id, order_id, store_id, user_id, reason, payment_settings_id, payment_method,
            token_data, extra_info, exc_info=exc_info
        )
