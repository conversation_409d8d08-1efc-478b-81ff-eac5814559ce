import schemas
from db.models import ExtraFeeJournal, Invoice, StoreOrder
from utils.numbers import format_currency


async def get_formated_extra_fees(
        obj: StoreOrder | Invoice, currency: str, lang: str,
        country_code: str | None = None
) -> list[schemas.ExtraFeeFormated]:

    if isinstance(obj, StoreOrder):
        extra_fees = await ExtraFeeJournal.get_list(order_id=obj.id)
    elif isinstance(obj, Invoice):
        extra_fees = await ExtraFeeJournal.get_list(invoice_id=obj.id)
    else:
        return []

    if not extra_fees:
        return []

    extra_fees_formated = []

    for extra_fee in extra_fees:
        extra_fees_formated.append(
            schemas.ExtraFeeFormated(
                name=extra_fee.name,
                formated_amount=format_currency(
                    extra_fee.applied_amount / 100, currency, lang, country_code
                )
            )
        )

    return extra_fees_formated


async def get_extra_fee_str(
        extra_fees: list[schemas.ExtraFeeFormated] | None = None
) -> str:

    extra_fee_str = ""
    if extra_fees:
        extra_fee_str = "\n- - - - - - - -\n"
        for extra_fee in extra_fees:
            extra_fee_str += f"{extra_fee.name}: {extra_fee.formated_amount}\n"
    return extra_fee_str


async def get_extra_fee_txt(
        group, obj: StoreOrder | Invoice, currency: str, lang: str, ) -> str:

    extra_fee_txt = ""
    extra_fees = await get_formated_extra_fees(
        obj, currency, group.lang, group.country_code
    )
    if extra_fees:
        from utils.text import f
        extra_fee_txt = await get_extra_fee_str(
            extra_fees
        )  # add --- symbols and add name extra fee
        extra_fee_txt += await f(
            "service bot total sum with extra fee txt", lang,
            total_sum_with_extra_fee=format_currency(
                obj.total_sum_with_extra_fee / 100, currency, locale=group.lang,
                territory=group.country_code
            )
        )
    return extra_fee_txt