import math
from typing import Literal

from psutils.translator.schemas import TranslateObjectData

import schemas
from config import (
    ANON_ORDER_TOKEN_EXPIRES, INVOICE_TOKEN_EXPIRES, NO_CENT_CURRENCIES,
    PAYMENT_DEFAULT_NAMES,
)
from core.payment.exceptions import (
    PaymentInvoicePaidError,
    PaymentStoreOrderCanceledError, PaymentStoreOrderClosedError,
    PaymentStoreOrderPayedError,
)
from core.store.functions.payments import get_icon_for_payment
from db import crud
from db.models import (
    Brand, BrandCustomSettings, ClientBot, EWallet, Group, Invoice,
    ObjectPaymentSettings, PaymentSettings, StoreOrder, StoreOrderPayment, Translation,
    User,
)
from db.models.store.payer_fee import PayerFee
from schemas.payment.payment import PayerFeeData
from schemas.store.types import ShipmentType
from utils.jwt_token import create_jwt_token
from utils.text import f
from utils.translator import t, td

PaymentSettingsType = list[tuple[PaymentSettings, Translation | None]]
OrderPaymentType = list[tuple[StoreOrderPayment, Translation | None]]
PaymentSettingsToTranslateType = dict[PaymentSettings, TranslateObjectData]
PaymentSettingsTranslatedType = dict[str | PaymentSettings, dict[str, str | None]]


async def get_translated_payment_settings(
        payment_settings: PaymentSettingsType, lang: str, group_id: int
) -> dict:
    group = await Group.get(group_id)

    payment_settings_data, to_translate = await get_payment_settings_to_translate(
        payment_settings
    )
    translated = await td(
        to_translate, lang, group.lang,
        group_id=group_id,
        is_auto_translate_allowed=group.is_translate,
    )

    return await translated_payment_settings_to_schemas(
        payment_settings_data, translated,
    )


async def translated_payment_settings_to_schemas(
        payments_settings_data: list[PaymentSettings],
        translated: PaymentSettingsTranslatedType,
):
    result = {}

    for payment_setting in payments_settings_data:
        result[payment_setting.id] = await translated_payment_settings_to_schema(
            payment_setting, translated
        )

    return result


async def translated_payment_settings_to_schema(
        payment_setting: PaymentSettings,
        translated: PaymentSettingsTranslatedType,
) -> dict:
    return translated[payment_setting]


async def get_payment_settings_to_translate(
        payment_settings: PaymentSettingsType,
) -> tuple[list, dict]:
    payment_settings_data: list = []
    to_translate: dict = {}

    for payment_setting, translation in payment_settings:
        payment_setting_to_translate = await get_payment_setting_to_translate(
            payment_setting, translation
        )

        payment_settings_data.append(payment_setting)
        to_translate.update(payment_setting_to_translate)

    return payment_settings_data, to_translate


async def get_payment_setting_to_translate(
        payment_setting: PaymentSettings,
        translation: Translation | None = None,
) -> PaymentSettingsToTranslateType:
    return {
        payment_setting: TranslateObjectData(
            object=payment_setting,
            translation=translation,
        )
    }


async def get_payment_default_name(
        provider: str, lang: str,
        is_online: bool = False,
        ewallet_id: int | None = None,
        for_messanger: bool = False,
) -> str | None:
    default_name = PAYMENT_DEFAULT_NAMES.get(provider, None)
    if default_name:
        default_name_text = default_name.get("text", None)
        default_name_var = default_name.get("var", None)
        if default_name_text:
            default_name = default_name_text
        elif default_name_var:
            default_name = await f(default_name_var, lang)
    else:
        if provider == "ewallet" and ewallet_id:
            ewallet = await EWallet.get(id=ewallet_id)
            if ewallet:
                return ewallet.name
            return "EWallet"
        if is_online:
            default_name_dict = PAYMENT_DEFAULT_NAMES.get("online_card", None)
            default_name_var = default_name_dict.get("var", None)
            if default_name_var:
                default_name = await f(default_name_var, lang)
                if for_messanger:
                    default_name = f"{default_name}:{provider.capitalize()}"
                else:
                    default_name = f"{default_name} ({provider.capitalize()})"

    return default_name


async def get_or_create_full_bonuses_payment_settings(brand_id: int) -> PaymentSettings:
    payment_settings = await PaymentSettings.get(
        payment_method="bonuses", brand_id=brand_id
    )
    if not payment_settings:
        payment_settings = await crud.new_create_payment_settings(
            "bonuses",
            brand_id,
        )

    return payment_settings


async def create_cash_for_brand_if_not_exist(brand_id: int):
    cash_payment = await PaymentSettings.get(brand_id=brand_id, payment_method="cash")
    if not cash_payment:
        cash_payment = await crud.new_create_payment_settings(
            payment_method="cash",
            brand_id=brand_id,
            with_comment="disabled"
        )

    if not cash_payment:
        return

    shipments = []

    for base_type in ShipmentType:
        brand_settings = await BrandCustomSettings.get_or_create_base_shipment(
            brand_id, base_type.value
        )
        shipments.append(brand_settings)

    custom_shipments = await crud.get_all_custom_shipments_and_groups(brand_id)
    for settings in custom_shipments:
        shipments.append(settings)

    for shipment in shipments:
        await crud.update_payment_setting_to_shipment(
            shipment.id, cash_payment.id, True
        )


def get_payment_method_fields(
        provider: schemas.BasicProvidersType, is_business_payment: bool | None = None,
) -> schemas.PaymentProviderItemSchema:

    fee_fields = [] if is_business_payment else [
        schemas.PaymentProviderItemFieldSchema(
            type="number",
            is_required=False,
            name="payer_fee_value",
        ),
        schemas.PaymentProviderItemFieldSchema(
            type="number",
            is_required=False,
            name="payer_fee_percent",
        )
    ]

    match provider:
        case "liqpay":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="public_key",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="private_key",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="boolean",
                    is_required=False,
                    name="is_need_fiscal",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=False,
                    name="tax_list",
                    options=[
                        schemas.PaymentProviderItemFieldOptionSchema(
                            value="А",
                            label="без ПДВ 0%"
                        ),
                        schemas.PaymentProviderItemFieldOptionSchema(
                            value="Б",
                            label="ПДВ 20%"
                        ),
                        schemas.PaymentProviderItemFieldOptionSchema(
                            value="В",
                            label="ПДВ 7%"
                        ),
                        schemas.PaymentProviderItemFieldOptionSchema(
                            value="Г",
                            label="акциз 5%"
                        ),
                    ]
                )
            ]
        case "stripe":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="public_key",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="secret_key",
                    field_size=12,
                )
            ]
        case "epay":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="terminal_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_secret",
                    field_size=12,
                )
            ]
        case "comsa":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="vendor_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="secret_key",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="boolean",
                    is_required=True,
                    name="is_sandbox",
                )
            ]
        case "flutterwave":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="encryption_key",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="secret_key",
                    field_size=12,
                )
            ]
        case "directpay":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="company_token",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="boolean",
                    is_required=False,
                    name="is_sandbox",
                )
            ]
        case "momo":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="subscription_key",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="user_api_key",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="user_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="boolean",
                    is_required=False,
                    name="is_sandbox",
                )
            ]
        case "tj":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="merchant_id",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="profile_id",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_id",
                    field_size=6,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_secret",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="boolean",
                    is_required=False,
                    name="is_sandbox",
                )
            ]
        case "airtel":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="country",
                    field_size=6,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_id",
                    field_size=6,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_secret",
                    field_size=12,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="pin",
                    field_size=6,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="boolean",
                    is_required=False,
                    name="is_sandbox",
                )
            ]
            if is_business_payment:
                base_fields.append(
                    schemas.PaymentProviderItemFieldSchema(
                        type="string",
                        is_required=True,
                        name="phone",
                        field_size=6,
                    )
                )
        case "fondy":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="merchant_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="secret_key",
                    field_size=12,
                )
            ]
        case "freedompay":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="merchant_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="merchant_secret",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="boolean",
                    is_required=False,
                    name="is_sandbox",
                )
            ]
        case "orange":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_secret",
                )
            ]
            if not is_business_payment:
                base_fields.append(
                    schemas.PaymentProviderItemFieldSchema(
                        type="string",
                        is_required=True,
                        name="partner_code",
                    ))
            if is_business_payment:
                base_fields.append(
                    schemas.PaymentProviderItemFieldSchema(
                        type="string",
                        is_required=True,
                        name="phone",
                        field_size=6,
                    )
                )
                # Додаємо обов'язкове поле для PIN-коду, яке буде використовуватись для операції Cash In
                base_fields.append(
                    schemas.PaymentProviderItemFieldSchema(
                        type="string",
                        is_required=True,
                        name="pin_code",
                        field_size=6,
                    )
                )
        case "kpay":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_secret",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="phone",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="boolean",
                    is_required=True,
                    name="is_sandbox",
                )
            ]
        case "pl24":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="boolean",
                    is_required=True,
                    name="is_sandbox",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="merchant_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="pos_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="crc",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="secret",
                )
            ]
        case "tpay":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="security_code",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_id",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="client_secret",
                )
            ]
        case "unipos":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="account",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="filial_code",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="inn",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="login",
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="password",
                )
            ]
        case "wave":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="api_key",
                    field_size=12,
                )
            ]
            if not is_business_payment:
                base_fields.append(
                    schemas.PaymentProviderItemFieldSchema(
                        type="string",
                        is_required=True,
                        name="secret",
                        field_size=12,
                    )
                )
            base_fields.append(
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=False,
                    name="aggregated_merchant_id",
                    field_size=6,
                )
            )
        case "ewallet":
            base_fields = [
                schemas.PaymentProviderItemFieldSchema(
                    type="string",
                    is_required=True,
                    name="ewallet_id",
                    field_size=12,
                    is_hidden=True,
                ),
                schemas.PaymentProviderItemFieldSchema(
                    type="boolean",
                    is_required=False,
                    name="is_pay_fee_self",
                    field_size=12,
                ),
            ]
        case "custom":
            base_fields = []
        case _:
            raise ValueError("Unknown payment provider")

    return schemas.PaymentProviderItemSchema(
        provider=provider,
        fields=base_fields + fee_fields,
        icon_url=get_icon_for_payment(provider),
    )


def calc_payer_fee_data(
        amount_to_pay: int, currency: str,
        payer_fee_percent: str, payer_fee_value: str
) -> PayerFeeData | None:

    fee = 0
    fee_percent = 0
    fee_value = 0

    if payer_fee_percent and payer_fee_percent == "None":
        payer_fee_percent = None

    if payer_fee_value and payer_fee_value == "None":
        payer_fee_value = None

    if payer_fee_percent:
        fee_percent = amount_to_pay * float(payer_fee_percent) / 100
        fee += fee_percent
    if payer_fee_value:
        fee_value = 100 * float(payer_fee_value)
        fee += fee_value

    if fee:
        return PayerFeeData(
            payer_fee_percent=payer_fee_percent,
            fee_percent=fee_percent,
            payer_fee_value=payer_fee_value,
            fee_value=fee_value,
            fee=(math.ceil(
                fee / 100
            )) * 100 if currency in NO_CENT_CURRENCIES else math.ceil(
                fee
            ),
        )
    return None


async def validate_status(invoice: Invoice, order: StoreOrder | None):
    if invoice.status == 'payed':
        raise PaymentInvoicePaidError(invoice.id)

    if order and order.status_pay == 'payed':
        raise PaymentStoreOrderPayedError(order.id)

    if order and order.status_pay == 'closed':
        raise PaymentStoreOrderClosedError(order.id)

    if order and order.status_pay == 'canceled':
        raise PaymentStoreOrderCanceledError(order.id)


async def make_client_return_url(
        brand: Brand,
        lang: str,
        bot: ClientBot | None,
        order: StoreOrder | None,
        invoice: Invoice | None,
        is_webview: bool = False
):
    query_params = {}

    if order:
        path = f"{lang}/s/{order.store_id}/orders/{order.id}"
        query_params["order_token"] = create_jwt_token(
            data={
                "sub": f"{order.id}",
                "type": "order",
                "scopes": ["order:read"],
            }, expire=ANON_ORDER_TOKEN_EXPIRES
        )
    elif invoice:
        path = "successPayment"
        query_params["invoice_token"] = create_jwt_token(
            {
                "sub": str(invoice.id),
                "type": "invoice",
                "scopes": ["invoice:read", "invoice:write"],
            }, INVOICE_TOKEN_EXPIRES
        )
    else:
        raise ValueError("order or invoice is required")

    brand_bot = await crud.get_bot_by_brand(brand.id)
    if brand_bot and brand_bot.id != bot.id:
        query_params["bot_id"] = bot.id

    query_params["lang"] = lang

    if is_webview:  # It is not a mistake. Param is_webview should be included
        # only when it is true
        query_params["is_webview"] = True
    if (
            menu_in_store_id := (
                    order.menu_in_store_id
                    if order else
                    invoice.menu_in_store_id
            )
    ):
        query_params["in_store_id"] = menu_in_store_id
    return brand.get_url(path, **query_params)


async def get_user_info(
        user: User,
        order: StoreOrder | None = None,
        invoice: Invoice | None = None,
) -> tuple[str, str, str, str]:
    user_name = ""
    user_email = ""
    user_address = ""
    user_phone = ""

    if order:
        user_email = order.email if order.email else ""
        user_name = (
            f"{order.last_name} {order.first_name}"
            if order.last_name and order.first_name
            else ""
        )
        user_address = order.delivery_address if order.delivery_address else ""
        user_phone = order.phone if order.phone else ""
    elif invoice:
        user_email = invoice.email if invoice.email else ""
        user_name = (
            f"{invoice.last_name} {invoice.first_name}"
            if invoice.last_name and invoice.first_name
            else ""
        )
        user_phone = invoice.phone if invoice.phone else ""
    elif user:
        if user.email:
            user_email = user.email
        if user.full_name:
            user_name = user.full_name

    if invoice and invoice.is_friend and invoice.payer_id:
        payer: User = await User.get_by_id(invoice.payer_id)
        if payer:
            # user_name = payer.full_name
            if payer.email:
                user_email = payer.email

    return user_name, user_email, user_phone, user_address


async def create_or_update_payer_fee(
        payment_method: str,
        invoice_id: int,
        payer_fee_data: PayerFeeData,
        status: Literal["payed", "pending"] | None = None,
) -> PayerFee:
    payer_fee = await PayerFee.get(
        invoice_id=invoice_id, payment_method=payment_method, type="payment_method"
    )

    if payer_fee:
        await payer_fee.update(
            status=status,
            fee=payer_fee_data.fee,
            payer_fee_percent=payer_fee_data.payer_fee_percent,
            fee_percent=payer_fee_data.fee_percent,
            payer_fee_value=payer_fee_data.payer_fee_value,
            fee_value=payer_fee_data.fee_value,
        )
    else:
        payer_fee = await PayerFee.create(
            invoice_id=invoice_id,
            status="pending",
            type="payment_method",
            payment_method=payment_method,
            fee=payer_fee_data.fee,
            payer_fee_percent=payer_fee_data.payer_fee_percent,
            fee_percent=payer_fee_data.fee_percent,
            payer_fee_value=payer_fee_data.payer_fee_value,
            fee_value=payer_fee_data.fee_value,
        )

    return payer_fee


async def get_translated_post_payment_info(
    group_id: int,
    payment_settings: PaymentSettings,
    object_payment_settings: ObjectPaymentSettings | None = None,
    lang: str | None = None,
) -> str | None:
    if not lang:
        lang = "en"

    group = await Group.get(group_id)
    original_lang = group.lang if group else "en"
    is_auto_translate_allowed = group.is_translate or False

    if object_payment_settings:
        if object_payment_settings.post_payment_info:
            translated_data = await t(
                object_payment_settings,
                lang,
                original_lang,
                fallback_to_original_on_error=True,
                group_id=group_id or "internal",
                is_auto_translate_allowed=is_auto_translate_allowed
            )
            return translated_data.get("post_payment_info", object_payment_settings.post_payment_info)

    if payment_settings.post_payment_info:
        translated_data = await t(
            payment_settings,
            lang,
            original_lang,
            fallback_to_original_on_error=True,
            group_id=group_id or "internal",
            is_auto_translate_allowed=is_auto_translate_allowed
        )
        return translated_data.get("post_payment_info", payment_settings.post_payment_info)

    return None
