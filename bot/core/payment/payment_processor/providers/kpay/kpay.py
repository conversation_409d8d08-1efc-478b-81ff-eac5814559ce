import math

from starlette.requests import Request

from config import KPAY_MERCHANT_API, KPAY_SERVICE_API
from core.payment.exceptions import (
    PaymentInvalidReferenceError, PaymentInvalidUrlError, PaymentPhoneRequiredError,
    PaymentResponseCommonError,
)
from core.payment.payment_processor.providers.base import PaymentProvider
from core.payment.payment_processor.providers.funcs import trim_phone_number
from core.payment.payment_processor.providers.kpay.client import kpay_api
from core.payment.payment_processor.providers.kpay.funcs import (
    get_kpay_payment_settings, get_kpay_token,
)
from db.models import Invoice, Payment
from loggers import JSONLogger
from schemas import (
    KPayHookResponse,
    KPayPayment, KPayTransferResult, PaymentCallBackData,
)

# debugger = logging.getLogger('debugger.payments.kpay')
debugger = JSONLogger("kpay")


def fix_phones(merchant_data, phone):
    sender_phone = trim_phone_number(phone)

    if sender_phone.startswith('+'):
        sender_phone = sender_phone[1:]
    if sender_phone.startswith('221'):
        sender_phone = sender_phone[3:]
    sender_phone = int(f'221{sender_phone}')

    receiver_phone = trim_phone_number(merchant_data.get("phone"))

    if receiver_phone.startswith('+'):
        receiver_phone = receiver_phone[1:]
    if receiver_phone.startswith('221'):
        receiver_phone = receiver_phone[3:]
    receiver_phone = f'221{receiver_phone}'

    return receiver_phone, sender_phone


class KpayProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    def return_status(cls, status: str):
        return {"status": status}

    async def get_payment_uuid(self, data: KPayHookResponse):

        if not data.correlationReference:
            raise PaymentInvalidReferenceError(f"{data.correlationReference=}")

        return data.correlationReference

    async def get_payment_result(
            self,
            data: KPayHookResponse,
            request: Request,
            credentials: dict,
    ) -> PaymentCallBackData:
        status = "failed"

        (
            client_id, client_secret, merchant_phone, is_sandbox
        ) = get_kpay_payment_settings(credentials)

        if data.status == 'succeeded':
            status = 'success'
        elif data.status == 'canceled':  # set for future.
            status = 'cancel'

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status=status,
            amount=data.amount,
        )

    async def create_payment(
            self,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            server_url: str,
            success_url: str,
            user_phone: str,
    ) -> dict | None:
        """
        Payment:
        https://encaissements-test.kpay-api.com/doc#/payment/PaymentController_create
        """

        if not user_phone:
            raise PaymentPhoneRequiredError()

        debugger.debug(f"create_payment ->", {"credentials": credentials})
        (
            client_id, client_secret, phone, is_sandbox
        ) = get_kpay_payment_settings(credentials)

        token = await get_kpay_token(credentials)

        payment_url_api = f'{KPAY_MERCHANT_API}/payment/initiate_payment'
        if not is_sandbox:
            payment_url_api = payment_url_api.replace('-test', '')

        user_phone = trim_phone_number(user_phone)
        if user_phone.startswith('+'):
            user_phone = user_phone[1:]
        if user_phone.startswith('221'):
            user_phone = user_phone[3:]

        json_data = {
            "customerPhoneNumber": user_phone,
            "amount": math.ceil(amount_to_pay / 100),
            "description": "",
            "callbackUrl": server_url,
            "useUniqueWallet": True,
            "inAppValidation": True,
            "qrValidation": False,
            "correlationReference": payment.uuid_id,
        }


        result = await kpay_api(
            url=payment_url_api, type_action='POST', json=json_data, token=token,
        )
        kpay_payment = KPayPayment(**result)

        if kpay_payment.code and kpay_payment.message:
            if kpay_payment.code == "DUPLICATE_ITEM":
                return None

        await payment.save_pmt_data({"kpay": result})
        if not kpay_payment.kpayReference and kpay_payment.status != "pending":
            raise PaymentInvalidUrlError(kpay_payment.message)

        return {"data": {"payment_id": payment.id, "qrcode": kpay_payment.qrValue}}

    async def make_business_payment(
            self,
            payment_uuid_id: str,
            amount_to_pay: int,
            currency: str,
            credentials: dict,
            merchant_data: dict,
    ) -> dict | None:
        """https://transfert-test.kpay-api.com/doc#/transaction/TransactionController_create_v1"""
        debugger.debug(f"make_business_payment ->",
            {
                "payment_uuid_id": payment_uuid_id,
                "amount_to_pay": amount_to_pay,
                "currency": currency,
                "credentials": credentials,
                "merchant_data": merchant_data,
            }
        )


        (
            client_id, client_secret, phone, is_sandbox
        ) = get_kpay_payment_settings(credentials)

        token = await get_kpay_token(credentials, True)
        receiver_phone, sender_phone = fix_phones(merchant_data, phone)

        payload = {
            "sender": {
                "phone": sender_phone,
            },
            "receiver": {
                "firstName": merchant_data.get("first_name"),
                "lastName": merchant_data.get("last_name"),
                "phone": receiver_phone,
            },
            "currency": currency,
            "correlationReference": payment_uuid_id,
            "isForcedToCash": False,
            "amount": math.ceil(amount_to_pay / 100)
        }
        debugger.debug(
            "make_business_payment payload:",
            {"payload": payload, "is_sandbox": is_sandbox}
        )

        payment_url_api = f'{KPAY_SERVICE_API}/transaction/sendTranfer'
        original_url = payment_url_api

        if not is_sandbox:
            payment_url_api = payment_url_api.replace('-test', '')
            debugger.set_data({
                "url_logic": {
                    "original_url": original_url,
                    "final_url": payment_url_api,
                    "is_sandbox": is_sandbox,
                    "condition": "not is_sandbox",
                    "condition_result": True
                }
            })
        else:
            debugger.set_data({
                "url_logic": {
                    "original_url": original_url,
                    "final_url": payment_url_api,
                    "is_sandbox": is_sandbox,
                    "condition": "not is_sandbox",
                    "condition_result": False
                }
            })
        debugger.debug("make_business_payment")
        result = await kpay_api(
            url=payment_url_api, type_action='POST', json=payload, token=token,
        )
        debugger.debug(
            "make_business_payment",
            {"result": result}
        )

        if not isinstance(result, dict):
            debugger.debug(
                "make_business_payment",
                "KPay make_business_payment failed: result is not a dict"
            )
            raise PaymentResponseCommonError(detail="payment failed")

        kpay_result = KPayTransferResult(**result)

        if kpay_result.code and kpay_result.message:
            if kpay_result.code == "DUPLICATE_ITEM":
                return None
            raise PaymentResponseCommonError(
                detail=kpay_result.message
            )

        if kpay_result.status == "succeeded":
            return result

        raise PaymentResponseCommonError(detail="payment failed")
