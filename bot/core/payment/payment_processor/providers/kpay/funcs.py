from config import KPAY_MERCHANT_API, KPAY_SERVICE_API
from core.payment.exceptions import (
    PaymentNotFoundCredentialsError,
)
from core.payment.payment_processor.providers.kpay.client import kpay_api
from loggers import JSONLogger

# debugger = logging.getLogger("debugger.payments.kpay")
debugger = JSONLogger("payments.kpay")


async def get_kpay_token(
        data: dict,
        is_service_api: bool = False,
):
    token = None
    params = {
        "clientId": data.get('client_id'),
        "clientSecret": data.get('client_secret'),
    }

    is_sandbox_value = data.get('is_sandbox')

    debugger.set_data({
        "credentials_source": "get_kpay_token",
        "is_sandbox_value": is_sandbox_value,
        "is_sandbox_type": type(is_sandbox_value).__name__ if is_sandbox_value is not None else 'None',
        "data": data,
    })
    debugger.debug("get_kpay_token")

    url = f"{KPAY_MERCHANT_API if not is_service_api else KPAY_SERVICE_API}/auth/token"
    original_url = url

    if data.get('is_sandbox') is not None and data['is_sandbox'] == False:
        url = url.replace('-test', '')
        debugger.set_data({
            "url_logic": {
                "original_url": original_url,
                "final_url": url,
                "condition_result": True
            }
        })
    else:
        debugger.set_data({
            "url_logic": {
                "original_url": original_url,
                "final_url": url,
                "condition_result": False
            }
        })
    debugger.debug("get_kpay_token")

    result = await kpay_api(url=url, type_action='GET', params=params)

    if result:
        token = result.get('access_token')

    return token


def get_kpay_payment_settings(
        credentials: dict
) -> tuple[str , str, str, bool]:
    client_id = credentials.get('client_id')
    client_secret = credentials.get('client_secret')
    phone = credentials.get('phone')
    is_sandbox_value = credentials.get('is_sandbox')

    debugger.set_data({
        "credentials_source": "get_kpay_payment_settings",
        "is_sandbox_value": is_sandbox_value,
        "is_sandbox_type": type(is_sandbox_value).__name__ if is_sandbox_value is not None else 'None'
    })
    debugger.debug("get_kpay_payment_settings")

    if not any([client_id, client_secret, phone]):
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    is_sandbox = True

    if is_sandbox_value is not None and is_sandbox_value == False:
        is_sandbox = False
        debugger.set_data({
            "is_sandbox_condition": "is_sandbox_value is not None and is_sandbox_value == False",
            "is_sandbox_result": "set to False"
        })
        debugger.debug("get_kpay_payment_settings")

    return client_id, client_secret, phone, is_sandbox
