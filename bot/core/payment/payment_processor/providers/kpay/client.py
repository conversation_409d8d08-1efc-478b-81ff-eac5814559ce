import logging
from typing import Literal

from aiohttp import ClientSession

from core.payment.exceptions import (
    PaymentResponseCommonError, PaymentResponseInvalidStatusError,
)

debugger = logging.getLogger("debugger.payments.kpay")


async def kpay_api(
        url: str,
        type_action: Literal["GET", "POST", "PUT"],
        json: dict | None = None,
        token: str | None = None,
        params: dict | None = None,
) -> dict:
    headers = {}
    if token:
        headers = {
            'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'
        }

    debugger.debug(f"request: {type_action} {url}\n{json=}\n{params=}\n{headers=}")

    async with ClientSession() as session:
        async with session.request(
                method=type_action, url=url, json=json, params=params, headers=headers
        ) as response:
            debugger.debug(f'{response.status=} {await response.text()=}')
            result = await response.json()
            if response.status > 201:
                if "code" in result and "message" in result:
                    # if result["code"] == "DUPLICATE_ITEM":
                    #     return result
                    # else:
                    raise PaymentResponseCommonError(
                        detail=result["message"]
                    )
                raise PaymentResponseInvalidStatusError(response.status)

    return result
