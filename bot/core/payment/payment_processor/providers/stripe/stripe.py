import math

from fastapi import Request
from psutils.text import strip_html_tags
from stripe import SignatureVerificationError, StripeClient

from config import SYSTEM_NAME
from core.payment.exceptions import (
    PaymentCheckSignatureDataError, PaymentExpiredError, PaymentGetSignatureDataError,
    PaymentInvalidPayloadError, PaymentStripeIntentError,
    PaymentStripeInvalidTypeError,
)
from db.models import Invoice, Payment, User
from loggers import JSONLogger
from schemas import (
    CheckoutEventTypes, PaymentCallBackData,
)
from .funcs import (
    get_checkout_session, get_payment_uuid_from_stripe_data,
)
from ..base import PaymentProvider

event_map = {
    'checkout.session.completed': CheckoutEventTypes.completed.value,
    'checkout.session.expired': CheckoutEventTypes.expired.value,
    'checkout.session.async_payment_succeeded': CheckoutEventTypes.completed.value,
    'checkout.session.async_payment_failed': CheckoutEventTypes.failed.value,
}


class StripeProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    async def create_payment(
            self,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            brand_id: int | None,
            store_id: int | None,
            user: User,
            success_url: str,
            user_email: str | None = None,
            lang: str | None = None,
    ) -> dict:
        logger = JSONLogger(
            "payments.stripe",
            "create_stripe_payment_intent",
            {
                "invoice_id": invoice.id,
                "payment_id": payment.id,
                "amount_to_pay": amount_to_pay,
                "brand_id": brand_id,
                "store_id": store_id,
                "user_id": user.id,
                "success_url": success_url,
                "user_email": user_email,
                "lang": lang,
                "credentials": credentials,
            }
        )

        logger.debug("Start")
        result = {"data": {}}

        if invoice.currency == "XOF":
            amount_to_pay = math.ceil(amount_to_pay / 100)

        credentials.update(
            client_reference_id=f'{SYSTEM_NAME}:{payment.uuid_id}',
            customer_email=user_email.strip() if user_email and user_email.strip() else None,
            idempotency_key=f'{user.id}|{payment.id}|{amount_to_pay}',
            currency=invoice.currency,
            unit_amount=amount_to_pay,
            name=invoice.title,
            description=strip_html_tags(
                invoice.description
            ) if invoice.description and len(
                invoice.description
            ) < 1024 else None,
            success_url=success_url,
            lang=lang,
        )
        client = StripeClient(credentials.get("secret_key"))
        checkout_session = await get_checkout_session(client, credentials)
        result['data']['url'] = checkout_session.url

        logger.debug(
            "Session created", {
                "session": dict(checkout_session)
            }
        )
        await payment.save_pmt_data({"stripe": checkout_session.to_dict()})

        return result

    def return_status(self, status: str):
        return {"status": status}

    async def get_payment_uuid(self, data: dict):
        return await get_payment_uuid_from_stripe_data(data)

    async def get_payment_result(
            self,
            request: Request,
            credentials: dict,
    ) -> PaymentCallBackData:
        sig_header = request.headers.get("stripe-signature")

        is_sandbox, stripe_api_key, stripe_endpoint_secret = await (
            self.get_credentials_data(
                credentials
            ))

        logger = JSONLogger("payments.stripe", credentials)

        logger.debug(
            "credentials",
            {"sig_header": sig_header, "stripe_endpoint_secret": stripe_endpoint_secret}
        )

        if not all([stripe_api_key, stripe_endpoint_secret]):
            raise PaymentGetSignatureDataError(f"{credentials=}")

        client = StripeClient(stripe_api_key)
        try:
            event = client.construct_event(
                await request.body(), sig_header, stripe_endpoint_secret
            )
        except ValueError as err:
            # Invalid payload
            raise PaymentInvalidPayloadError(str(err))
        except SignatureVerificationError as err:
            raise PaymentCheckSignatureDataError(
                "Error stripe webhook signature verification (not valid "
                f"stripe_endpoint_secret or request body)\n{err}"
            )

        if "type" not in event:
            raise PaymentStripeInvalidTypeError(f'{event.get("type", None)=}')

        if event["type"] not in event_map:
            raise PaymentStripeInvalidTypeError(
                f'{event["type"]=} not in {event_map.keys()=}'
            )

        payment_data = PaymentCallBackData(
            callback_data=event,
            is_sandbox=is_sandbox,
        )

        if event_map[event["type"]] == CheckoutEventTypes.expired.value:
            raise PaymentExpiredError()
        elif event_map[event["type"]] == CheckoutEventTypes.completed.value:
            payment_intent_id = event.get("data", {}).get("object", {}).get(
                "payment_intent"
            )
            if not payment_intent_id:
                raise PaymentStripeIntentError()

            payment_intent = await client.payment_intents.retrieve_async(
                payment_intent_id
            )

            payment_data.external_id = payment_intent.id
            status = (
                "success" if payment_intent.status == "succeeded"
                else (
                    "cancel" if payment_intent.status == "canceled"
                    else "failed"
                )
            )
            payment_data.status = status
        else:
            raise PaymentStripeInvalidTypeError(f'{event["type"]=}')

        return payment_data

    @classmethod
    async def get_credentials_data(cls, credentials):
        is_sandbox = True
        stripe_api_key = credentials.get('secret_key')
        stripe_endpoint_secret = credentials.get('stripe_endpoint_secret')
        if stripe_api_key.find('live') > -1:
            is_sandbox = False
        if stripe_api_key.find('live') > -1:
            is_sandbox = False
        return is_sandbox, stripe_api_key, stripe_endpoint_secret
