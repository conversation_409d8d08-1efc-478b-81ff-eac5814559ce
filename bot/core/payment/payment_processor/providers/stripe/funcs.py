import logging

from stripe import Invalid<PERSON>e<PERSON><PERSON><PERSON>r, StripeClient, api_version

from config import P4S_API_URL, SYSTEM_NAME
from core.payment.exceptions import (
    PaymentMiniAmountRequiredError, PaymentNotifyAdminExceptionError,
    PaymentStripeInvalidReferenceError,
    PaymentStripeNotFoundPaymentError, PaymentStripeWrongSystemError,
)

debugger = logging.getLogger('debugger.payments.stripe')


async def get_payment_uuid_from_stripe_data(
        webhook_data: dict
) -> str | None:
    client_reference_id = webhook_data["data"]["object"].get("client_reference_id")

    if not client_reference_id or client_reference_id.find(':') < 0:
        raise PaymentStripeInvalidReferenceError()

    req_system_name, payment_uuid = client_reference_id.split(
        ':'
    )
    if req_system_name != SYSTEM_NAME:
        raise PaymentStripeWrongSystemError(f"{SYSTEM_NAME} != {req_system_name}")

    if not payment_uuid:
        raise PaymentStripeNotFoundPaymentError()

    return payment_uuid


async def get_checkout_session(client: StripeClient, stripe_pmt_data: dict):
    # raise PaymentNotifyAdminExceptionError("TEST ERROR!!!")
    try:
        checkout_session = await client.checkout.sessions.create_async(
            params=dict(
                mode='payment',
                client_reference_id=stripe_pmt_data["client_reference_id"],
                customer_email=stripe_pmt_data["customer_email"],
                locale='auto',
                submit_type='pay',
                billing_address_collection='auto',
                tax_id_collection=None,
                # idempotency_key=stripe_pmt_data["idempotency_key"],
                line_items=[{
                    'price_data': {
                        'currency': stripe_pmt_data["currency"],
                        'unit_amount': stripe_pmt_data["unit_amount"],
                        'product_data': {
                            'name': stripe_pmt_data["name"],
                            'description': stripe_pmt_data["description"],
                        },
                    },
                    'quantity': 1,
                }],
                success_url=stripe_pmt_data["success_url"],
                cancel_url=stripe_pmt_data["success_url"],
            )
        )
    except InvalidRequestError as err:
        if err.code == 'amount_too_small':
            raise PaymentMiniAmountRequiredError(message=err.user_message)
        elif err.code in ['payment_method_unactivated',
                          'payment_method_unsupported_type']:
            raise PaymentNotifyAdminExceptionError(str(err), err.code)
        else:
            raise err
    except Exception as e:
        raise e

    return checkout_session


async def create_or_update_stripe_webhook(
        secret_key: str,
        stripe_webhook_endpoint: str | None = None,
) -> str:
    stripe_endpoint_secret = None
    try:
        if not stripe_webhook_endpoint:
            stripe_webhook_endpoint = f'{P4S_API_URL}/payments/callback/stripe'
        client = StripeClient(secret_key)
        stripe_webhooks = await client.webhook_endpoints.list_async()
        for stripe_webhook in stripe_webhooks.data:
            if stripe_webhook.url == stripe_webhook_endpoint:
                await client.webhook_endpoints.delete_async(stripe_webhook.id)
                debugger.debug(f'DELETED {stripe_webhook.url=}')

        webhook_params = {
            "url": stripe_webhook_endpoint,
            "enabled_events": [
                "checkout.session.async_payment_failed",
                "checkout.session.async_payment_succeeded",
                "checkout.session.completed",
                "checkout.session.expired"
            ],
            "api_version": api_version,
        }

        new_stripe_webhook = await client.webhook_endpoints.create_async(
            params=webhook_params
        )
        debugger.debug(f'CREATED {new_stripe_webhook.url=}, {new_stripe_webhook=}')
        stripe_endpoint_secret = new_stripe_webhook.secret
    except Exception as err:
        logging.error(f'Stripe: {err}', exc_info=True)
    return stripe_endpoint_secret
