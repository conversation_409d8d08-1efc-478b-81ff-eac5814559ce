import logging
import math

from starlette.requests import Request

from core.payment.exceptions import (
    PaymentInvalidCallBackDataError,
    PaymentInvalidReferenceError, PaymentInvalidUrlError, PaymentResponseCommonError,
)
from core.payment.payment_processor.providers.base import PaymentProvider
from core.payment.payment_processor.providers.funcs import trim_phone_number
from core.payment.payment_processor.providers.wave.client import wave_api
from core.payment.payment_processor.providers.wave.funcs import (
    do_check_hash,
    get_wave_payment_settings,
)
from db.models import Invoice, Payment
from schemas import (
    PaymentCallBackData,
    WaveHookResponse, WavePayOutResult, WavePayment,
)

debugger = logging.getLogger('debugger.payments.wave')


class WaveProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    def return_status(cls, status: str):
        return {"status": status}

    async def get_payment_uuid(self, data: WaveHookResponse):

        if not data.data:
            raise PaymentInvalidCallBackDataError(f"{data.data=}")
        if not data.data.client_reference:
            raise PaymentInvalidReferenceError(f"{data.data.client_reference=}")

        return data.data.client_reference

    async def get_payment_result(
            self,
            data: WaveHookResponse,
            request: Request,
            credentials: dict,
    ) -> PaymentCallBackData:
        status = "failed"

        (
            secret,
            _,
            _,
            is_sandbox,
        ) = get_wave_payment_settings(credentials)

        await do_check_hash(secret, request)

        if (
                data.type == 'checkout.session.completed' and
                data.data.payment_status == 'succeeded' and
                data.data.checkout_status == 'complete'
        ):
            status = 'success'
        elif data.data.checkout_status == 'canceled':  # set for future.
            # really cancel status not in docs.
            status = 'cancel'

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status=status,
        )

    async def create_payment(
            self,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            store_id: int | None,
            success_url: str,
    ) -> dict:
        """
        Payment:
        https://docs.wave.com/business#create-a-checkout-session
        """

        debugger.debug(f"create_payment ->")
        (
            secret, api_key, aggregated_merchant_id, is_sandbox
        ) = get_wave_payment_settings(credentials)

        payment_url_api = 'https://api.wave.com/v1/checkout/sessions'

        json_data = {
            'amount': math.ceil(amount_to_pay / 100),
            'currency': invoice.currency,
            'error_url': success_url,
            'success_url': success_url,
            'client_reference': payment.uuid_id,
        }

        if aggregated_merchant_id:
            json_data['aggregated_merchant_id'] = aggregated_merchant_id

        result = await wave_api(
            url=payment_url_api, type_action='POST', json=json_data, api_key=api_key
        )
        wave_payment = WavePayment(**result)

        await payment.save_pmt_data({"wave": result})
        if not wave_payment.id or not wave_payment.wave_launch_url:
            raise PaymentInvalidUrlError(wave_payment.last_payment_error.message)

        return {"data": {"url": wave_payment.wave_launch_url}}

    async def make_business_payment(
            self,
            payment_uuid_id: str,
            amount_to_pay: int,
            currency: str,
            credentials: dict,
            merchant_data: dict,
    ) -> dict | None:
        debugger.debug(f"make_business_payment ->")
        _, api_key, aggregated_merchant_id, _ = get_wave_payment_settings(credentials)

        phone = trim_phone_number(merchant_data.get("phone"))
        
        if phone and not phone.startswith("+221"):
            if phone.startswith("221"):
                phone = "+" + phone
            elif len(phone) == 9:  # Формат 77XXXXXXX
                phone = "+221" + phone

        json = {
            "currency": currency,
            "receive_amount": f"{math.ceil(amount_to_pay / 100)}",
            "name": merchant_data.get("name"),
            "mobile": phone,
        }

        if aggregated_merchant_id:
            json.update({"aggregated_merchant_id": aggregated_merchant_id})

        payment_url_api = 'https://api.wave.com/v1/payout'
        result = await wave_api(
            url=payment_url_api, type_action='POST', json=json, api_key=api_key,
            idempotency_key=payment_uuid_id
        )
        if not isinstance(result, dict):
            raise PaymentResponseCommonError(detail="payment failed")

        wave_result = WavePayOutResult(**result)

        if wave_result.payout_error:
            raise PaymentResponseCommonError(
                detail=wave_result.payout_error.error_message
            )

        if wave_result.status == "succeeded":
            return result

        raise PaymentResponseCommonError(detail="payment failed")
