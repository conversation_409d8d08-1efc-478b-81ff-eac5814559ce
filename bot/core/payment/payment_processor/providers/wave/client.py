import logging
from typing import Literal

from aiohttp import ClientSession

from core.payment.exceptions import (
    PaymentResponseInvalidStatusError,
    PaymentWaveApiKeyError, PaymentResponseCommonError,
)

debugger = logging.getLogger("debugger.payments.wave")


async def wave_api(
        url: str,
        type_action: Literal["GET", "POST", "PUT"],
        json: dict | None = None,
        api_key: str | None = None,
        params: dict | None = None,
        idempotency_key: str | None = None,
) -> dict:
    headers = {}
    if api_key:
        headers = {
            'Authorization': f'Bearer {api_key}', 'Content-Type': 'application/json'
        }
        if idempotency_key:
            headers.update({'idempotency-key': idempotency_key})

    debugger.debug(f"request: {type_action} {url}\n{json=}\n{params=}\n{headers=}")

    async with ClientSession() as session:
        async with session.request(
                method=type_action, url=url, json=json, params=params, headers=headers
        ) as response:
            debugger.debug(f'{response.status=} {await response.text()=}')
            result = await response.json()
            if response.status > 201:
                err_text = result.get('message', '')
                details = "\n".join(msg.get("msg") for msg in result["details"]) if result.get("details") else None
                if details:
                    err_text += f"\n\n{details}"

                if "code" in result:
                    if result["code"] == "no-matching-api-key":
                        raise PaymentWaveApiKeyError(detail=err_text)
                    else:
                        raise PaymentResponseCommonError(
                            detail=err_text
                        )
                raise PaymentResponseInvalidStatusError(response.status)

    return result
