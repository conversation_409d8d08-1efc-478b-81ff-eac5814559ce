import datetime
import hashlib
import hmac
import logging

from starlette.requests import Request

from core.payment.exceptions import (
    PaymentCheckSignatureDataError, PaymentInvalidResponseError,
    PaymentNotFoundCredentialsError,
)
from core.payment.payment_processor.providers.wave.client import wave_api
from db import crud
from db.models import Brand

debugger = logging.getLogger("debugger.payments.wave")


async def get_secret(
        brand_id: int | None,
        store_id: int | None,
        data: dict,
        url: str,
        api_key: str,
):
    token = None
    payload = {
        "jsonrpc": "2.0",
        "id": f"7loc_login_{brand_id}_{store_id}" if store_id else f"7loc_login_"
                                                                   f"{brand_id}",
        "method": "login",
        "params": {
            "username": data.get('login'),
            "password": data.get('password'),
            "refresh": False
        }
    }
    result = await wave_api(url=url, type_action='GET', json=payload, api_key=api_key)
    if result:
        token = result.get('result', {}).get('access_token')
    return token


async def create_aggregated_merchant_id(
        merchant_name: str,
        payment_settings_id: int,
        brand: Brand,
        store_id: int | None = None,
        invoice_template_id: int | None = None,
) -> str:

    payment_data = await crud.get_payment_data(
        payment_settings_id=payment_settings_id, payment_method='wave',
        store_id=store_id, invoice_template_id=invoice_template_id,
    )

    api_key = payment_data.get("api_key")

    if not api_key:
        raise PaymentNotFoundCredentialsError(f"{payment_data=}")

    url = 'https://api.wave.com/v1/aggregated_merchants'

    merchants = await wave_api(url=url, type_action='GET', json=None, api_key=api_key)

    if merchants and isinstance(merchants, dict):
        for merchant in merchants.get('items', []):
            if merchant.get('name') == merchant_name:
                return merchant.get('id')

    data = {
        "name": merchant_name,
        "business_sector": None,
        "business_type": "other",
        "business_registration_identifier": None,
        "website_url": brand.domain,
        "business_description": f"Point of sale {merchant_name} of retail consumer "
                                f"goods. There are promotions and "
                                f"loyalty.",
    }

    result = await wave_api(url=url, type_action='POST', json=data, api_key=api_key)

    if result and "id" in result:
        return result["id"]

    raise PaymentInvalidResponseError()


async def delete_aggregated_merchant_id(
        brand_id: int, store_id: int, aggregated_merchant_id: str
):
    payment_data = await crud.get_store_payment_data('wave', brand_id, store_id)
    if not payment_data:
        raise PaymentNotFoundCredentialsError("")

    api_key = payment_data.get("api_key")
    if not api_key:
        raise PaymentNotFoundCredentialsError(f"{payment_data=}")

    url = 'https://api.wave.com/v1/aggregated_merchants'
    params = dict(aggregated_merchant_id=aggregated_merchant_id)

    await wave_api(url=url, type_action='PUT', params=params, api_key=api_key)


async def get_aggregated_merchants(brand_id: int, store_id: int, ) -> dict:
    payment_data = await crud.get_store_payment_data('wave', brand_id, store_id)
    if not payment_data:
        raise PaymentNotFoundCredentialsError()

    api_key = payment_data.get("api_key")
    if not api_key:
        raise PaymentNotFoundCredentialsError(f"{payment_data=}")

    url = 'https://api.wave.com/v1/aggregated_merchants'

    return await wave_api(url=url, type_action='GET', api_key=api_key)


def get_sha256(payload: bytes, secret: str) -> str:
    sha256 = hmac.new(bytes(secret, 'UTF-8'), payload, hashlib.sha256).hexdigest()
    return sha256


async def do_check_hash(secret: str, request: Request):
    check_hash = False

    timestamp_now = int(datetime.datetime.timestamp(datetime.datetime.now()))
    wave_signatures = request.headers.get('Wave-Signature')

    hashs = []
    timestamp: str = ''
    for w in wave_signatures.split(','):
        (k, v) = w.split('=')
        if k == 'v1':
            hashs.append(v)
        if k == 't':
            timestamp = v

    payload = timestamp.encode() + await request.body()
    calc_hash = get_sha256(payload, secret)

    for hash_in in hashs:
        if hash_in == calc_hash:
            if timestamp_now - int(timestamp) < 30 * 60:
                check_hash = True
                break

    if not check_hash:
        raise PaymentCheckSignatureDataError()


def get_wave_payment_settings(
        credentials: dict
) -> tuple[str, str, str, bool]:
    secret = credentials.get('secret')
    api_key = credentials.get('api_key')
    if not api_key:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    aggregated_merchant_id = credentials.get('aggregated_merchant_id')

    return secret, api_key, aggregated_merchant_id, False
