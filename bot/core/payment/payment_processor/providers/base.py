import inspect
from abc import ABC, abstractmethod
from typing import Dict, Type

from psutils.text import paschal_case_to_snake_case

from schemas import PaymentCallBackData


class PaymentProvider(ABC):
    _providers: Dict[str, Type['PaymentProvider']] = {}

    def __init_subclass__(cls, **kwargs):
        if not inspect.isabstract(cls):
            provider_name = paschal_case_to_snake_case(cls.__name__).replace(
                '_provider', ''
            )
            cls._providers[provider_name] = cls
        super().__init_subclass__(**kwargs)

    @abstractmethod
    def create_payment(self, **kwargs) -> dict:
        raise NotImplementedError

    @classmethod
    def get_provider(cls, name: str) -> Type['PaymentProvider']:
        print(f"Available providers: {cls._providers.keys()}")
        return cls._providers.get(name)

    @abstractmethod
    def get_payment_result(self, **kwargs) -> PaymentCallBackData:
        raise NotImplementedError

    @abstractmethod
    def get_payment_uuid(self, **kwargs) -> str:
        raise NotImplementedError

    @abstractmethod
    def return_status(self, **kwargs) -> any:
        raise NotImplementedError

    def make_business_payment(self, **kwargs) -> dict:
        raise NotImplementedError(
            "This payment provider doesn't support business payments"
        )
