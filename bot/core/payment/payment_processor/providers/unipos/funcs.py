import logging
import re
from typing import Any

from aiohttp import (ClientSession, ClientTimeout)

from config import TEST_UNIPOS_DESCRIPT
from core.payment.exceptions import (
    PaymentInvalidResponseError,
    PaymentNotifyAdminExceptionError, PaymentResponseInvalidStatusError,
)
from db.models import Invoice, Payment
from schemas import (
    UniposCallback, UniposCheque, UniposExchange, UniposPayment, UniposPaymentBody,
    UniposQR,
    UniposStatus,
)
from utils.text import f

debugger = logging.getLogger("debugger.payments.unipos")


async def get_values_from_unipos_desc(text: str, lang: str) -> dict:
    data = {}
    params = {}
    pattern = (r"Курс конвертации (\d+\.?\d*) RUB - (\d+\.\d+) UZS, Сумма к оплате: ("
               r"\d+\.\d+) UZS")
    match = re.search(pattern, text)
    if match:
        params['amount_2'] = f"{float(match.group(3)) / float(match.group(2)):.2f} RUB"
        params['kurs'] = f"{float(match.group(2)):.2f} UZS / 1 RUB"
        params['amount_1'] = f"{float(match.group(3)):.2f} UZS"
        data['description'] = await f('unipos qr code text', lang, **params)

    return data


async def create_unipos_payment(
        unipos_token: str,
        terminal_id: str,
        invoice: Invoice,
        payment: Payment,
        amount_to_pay: int,
        lang: str,
        success_url: str,
        server_url: str,
) -> UniposPayment:
    payload = {
        "jsonrpc": "2.0",
        "id": payment.uuid_id,
        "method": "payment.create",
        "params": {
            "ext_id": payment.uuid_id,
            "amount": amount_to_pay,
            "purpose_code": f"Invoice_{invoice.id}",
            # "purpose_code": invoice.title,
            "terminal_id": terminal_id,
            "redirect_url": success_url,
            "callback_url": server_url,
        }
    }
    if TEST_UNIPOS_DESCRIPT:
        return await test_unipos_response()

    result = await unipos_api(unipos_token, payload, lang)
    return UniposPayment(**result.get('result'))


async def test_unipos_response():
    return UniposPayment(
        payment=UniposPaymentBody(
            cheque=UniposCheque(
                partner="example_partner",
                ext_id="12345",
                amount=1000000,  # 10,000 узбецьких сомів
                currency=860,  # Код валюти для узбецького сома
                purpose="Курс конвертации 1.00 RUB - 77.00 UZS, Сумма к оплате: "
                        "10000.00 UZS",
                redirect_url="https://example.com/redirect",
                merchant_id="MERCH001",
                created_at="2024-08-15T12:00:00Z"
            ),
            exchange=UniposExchange(
                rate=77  # Приблизний курс: 1 російський рубль = 77 узбецьких сомів
            ),
            qr=UniposQR(
                amount=10000000,  # 100,000 узбецьких сомів
                currency=860,  # Код валюти для узбецького сома
                qrc_id="QRC12345",
                local_qrc_id=1,
                url="https://example.com/qr/QRC12345",
                status=UniposStatus(
                    locked="2024-08-15T12:05:00Z",
                    pay_status="PAID",
                    pay_trx_id="TRX12345"
                )
            ),
            callback=UniposCallback(),
            updated_at="2024-08-15T12:10:05Z"
        )
    )


async def get_unipos_token(
        brand_id: int | None,
        store_id: int | None,
        data: dict,
        lang: str,
):
    token = None
    payload = {
        "jsonrpc": "2.0",
        "id": f"7loc_login_{brand_id}_{store_id}" if store_id else f"7loc_login_"
        f"{brand_id}",
        "method": "login",
        "params": {
            "username": data.get('login'),
            "password": data.get('password'),
            "refresh": False
        }
    }
    result = await unipos_api(token, payload, lang)
    if result:
        token = result.get('result', {}).get('access_token')
    return token


async def add_unipos_terminal(
        brand_id: int | None,
        store_id: int | None,
        token: str,
        terminal_data: dict[str, Any],
        lang: str | None,
):
    terminal_id = None
    payload = {
        "jsonrpc": "2.0",
        "id": f"7loc_term_{brand_id}_{store_id}" if store_id else f"7loc_term_{brand_id}",
        "method": "terminal.add",
        "params": {
            "account": terminal_data["account"],
            "filial_code": terminal_data["filial_code"],
            "inn": terminal_data["inn"],
            "name": terminal_data["name"]
        }
    }
    result = await unipos_api(token, payload, lang)
    if result:
        terminal_id = result.get('result', {}).get('terminal_id')
    return terminal_id


async def unipos_api(token: str | None, payload: dict, lang: str | None) -> dict:
    result = None
    headers = {}
    url = 'https://sbp-qr.unired.uz/api/v1/jsonrpc'
    if token:
        headers = {
            'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'
        }
    debugger.debug(f'{url}\n{headers=}\n{payload=}')

    timeout = ClientTimeout(connect=4, sock_read=7)
    async with ClientSession(timeout=timeout) as session:
        async with session.post(url, json=payload, headers=headers) as response:
            debugger.debug(f'{response.status=}, {await response.text()=}')
            if response.status != 200:
                raise PaymentResponseInvalidStatusError(response.status)
            result = await response.json()
            await process_error(result, lang, )

    return result


async def process_error(result: dict, lang: str | None = None, ):
    if 'error' in result:
        err_message = None
        if lang:
            err_message = result.get('error', {}).get('message', {}).get(lang, None)
        if not err_message:
            err_message = result.get('error', {}).get('message', {}).get('en')
        if 'code' in result['error']:
            # 3 - Day is closed, 11 - Undefined error occurred
            raise PaymentNotifyAdminExceptionError(err_message, result['error']['code'])
        raise PaymentInvalidResponseError(err_message)
