import logging

from core.payment.exceptions import (
    PaymentCheckUuidsError, PaymentInvalidResponseError, PaymentInvalidUrlError,
    PaymentNotFoundCredentialsError,
)
from core.payment.payment_processor.providers.base import PaymentProvider
from core.payment.payment_processor.providers.unipos.funcs import (
    create_unipos_payment,
    get_values_from_unipos_desc,
)
from db.models import Invoice, Payment
from schemas import PaymentCallBackData, UniposPayment

debugger = logging.getLogger('debugger.payments.unipos')


class UniposProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: UniposPayment):
        return data.payment.cheque.ext_id

    @classmethod
    def return_status(cls, status: str):
        return {"status": status}

    @classmethod
    async def get_payment_result(
            cls,
            data: UniposPayment,
            payment_uuid: str,
    ) -> PaymentCallBackData:
        debugger.debug(
            f'Unipos -> Webhook event received {data=}'
        )
        status = False
        unipos_payment = data.payment
        result = PaymentCallBackData(
            callback_data=unipos_payment.dict(),
            status=status,
            is_sandbox=False,
        )
        if payment_uuid != unipos_payment.cheque.ext_id:
            raise PaymentCheckUuidsError(
                f"{payment_uuid=}, {unipos_payment.cheque.ext_id=}"
            )

        qr = data.payment.qr

        if qr.status.pay_status == 'ACWP':
            status = 'success'
        elif qr.status.pay_status == 'canceled':  # set for future. really not in docs.
            status = 'cancel'

        result.external_id = qr.qrc_id
        result.status = status,

        return result

    @classmethod
    async def create_payment(
            cls,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            brand_id: int | None,
            store_id: int | None,
            lang: str,
            success_url: str,
            server_url: str,
    ) -> dict:
        result: dict = {"data": {}}

        unipos_token = credentials.get('token')
        terminal_id = credentials.get('terminal_id')

        if not any([unipos_token, terminal_id]):
            raise PaymentNotFoundCredentialsError(f"{unipos_token=}, {terminal_id=}")

        unipos_payment = await create_unipos_payment(
            unipos_token,
            terminal_id,
            invoice,
            payment,
            amount_to_pay,
            lang,
            success_url,
            server_url,
        )

        if not unipos_payment:
            raise PaymentInvalidResponseError()

        if not unipos_payment.payment.qr.url:
            raise PaymentInvalidUrlError()

        result['data']['url'] = unipos_payment.payment.qr.url
        result['description'] = unipos_payment.payment.cheque.purpose
        if (
                'data' in result and
                result['data'] and
                'description' in result and
                result['description']
        ):
            result.update(
                await get_values_from_unipos_desc(result.get('description'), lang)
            )

        await payment.save_pmt_data({'unipos': unipos_payment.dict()})

        return result
