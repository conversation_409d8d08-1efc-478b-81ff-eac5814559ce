from core.payment.exceptions import PaymentNotFoundCredentialsError
from schemas import TpayTransaction


def get_tpay_error_message(tpay_payment: TpayTransaction) -> str:
    err_message = ""
    if hasattr(tpay_payment, "errors"):
        for error in tpay_payment.errors:
            err_message += error.errorMessage + '\n'

    if hasattr(tpay_payment, 'payments') and hasattr(tpay_payment.payments, 'errors') \
            and hasattr(tpay_payment.payments.errors, 'errorMessage'):
        err_message += tpay_payment.payments.errors.errorMessage

    return err_message


def get_tpay_payment_data(credentials: dict) -> tuple[str, str, str, bool]:

    is_sandbox = True

    security_code = credentials.get('security_code')
    client_id = credentials.get('client_id')
    client_secret = credentials.get('client_secret')

    if not security_code or not client_id or not client_secret:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    if not credentials.get("is_sandbox"):
        is_sandbox = False

    return client_id, client_secret, security_code, is_sandbox
