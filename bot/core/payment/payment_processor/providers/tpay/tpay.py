from starlette.responses import PlainTextResponse

from core.payment.exceptions import (
    PaymentCheckSignatureDataError, PaymentCheckUuidsError,
    PaymentInvalidCallBackDataError,
    PaymentInvalidReferenceError, PaymentInvalidResponseError,
)
from db.models import Invoice, Payment
from schemas import PaymentCallBackData, TpayHookResponse
from .client import calculate_tpay_checksum, create_tpay_transaction
from .funcs import get_tpay_error_message, get_tpay_payment_data
from ..base import PaymentProvider


class TpayProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: TpayHookResponse):
        if not data:
            raise PaymentInvalidCallBackDataError(f"{data=}")
        if not data.tr_crc:
            raise PaymentInvalidReferenceError(f"{data.tr_crc=}")

        return data.tr_crc

    @classmethod
    def return_status(cls, status: str):
        return PlainTextResponse(content="TRUE", status_code=200)

    @classmethod
    async def create_payment(
            cls,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            brand_id: int | None,
            store_id: int | None,
            lang: str,
            success_url: str,
            server_url: str,
            user_email: str | None,
            user_name: str | None,
            user_phone: str | None,
            user_address: str | None,
    ) -> dict:
        result = {"data": {}}

        (
            client_id,
            client_secret,
            security_code,
            is_sandbox
        ) = get_tpay_payment_data(credentials)

        tpay_payment = await create_tpay_transaction(
            invoice,
            payment,
            amount_to_pay,
            lang,
            success_url,
            server_url,
            client_id,
            client_secret,
            user_email,
            user_name,
            user_phone,
            user_address,
        )

        if tpay_payment and getattr(
                tpay_payment, "result"
        ) and tpay_payment.result == "success" and \
                getattr(
                    tpay_payment, "transactionPaymentUrl"
                ) and tpay_payment.transactionPaymentUrl:
            result['data']['url'] = tpay_payment.transactionPaymentUrl
        else:
            if tpay_payment:
                raise PaymentInvalidResponseError(get_tpay_error_message(tpay_payment))
            raise PaymentInvalidResponseError()

        await payment.save_pmt_data({"tpay": tpay_payment.dict()})

        return result

    async def get_payment_result(
            self,
            payment_uuid: str,
            data: TpayHookResponse,
            credentials: dict,
    ) -> PaymentCallBackData | PlainTextResponse:

        if payment_uuid != data.tr_crc:
            raise PaymentCheckUuidsError(f"{payment_uuid=} {data.tr_crc=}")

        _, _, security_code, is_sandbox = get_tpay_payment_data(credentials)

        md5sum = calculate_tpay_checksum(
            data.id, data.tr_id, data.tr_amount,
            data.tr_crc, security_code
        )

        if md5sum != data.md5sum:
            raise PaymentCheckSignatureDataError(f"{md5sum=} <> {data.md5sum=}")

        return PaymentCallBackData(
            payment_uuid=payment_uuid,
            callback_data=data.__dict__,
            is_sandbox=is_sandbox,
            status=(
                'success' if (
                        data.tr_status == 'TRUE' and
                        data.tr_error == 'none'
                )
                else 'failed'
            ),
            external_id=data.tr_id,
        )
