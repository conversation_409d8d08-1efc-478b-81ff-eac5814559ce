import hashlib
import logging
from typing import Literal
from urllib.parse import urlencode

from aiohttp import ClientSession

from config import TPAY_OPENAPI, TPAY_PMT_PAGE
from core.payment.exceptions import (
    PaymentInvalidPayloadError, PaymentInvalidResponseError,
    PaymentNotFoundCredentialsError, PaymentResponseInvalidStatusError,
)
from db.models import Invoice, Payment
from schemas import TpayPayment, TpayTransaction

debugger = logging.getLogger("debugger.payments.tpay")


def get_tpay_pmt_data(tpay_data: dict) -> tuple[str, str, str]:
    security_code = tpay_data.get('security_code')
    client_id = tpay_data.get('client_id')
    client_secret = tpay_data.get('client_secret')
    if not any([security_code, client_id, client_secret]):
        raise PaymentNotFoundCredentialsError(
            f"{tpay_data=}"
        )
    return security_code, client_id, client_secret


def calculate_tpay_checksum(*args, separator: str | None = None) -> str:
    if not separator:
        separator = ""
    data_to_hash = separator.join([*args])
    md5sum = hashlib.md5(data_to_hash.encode()).hexdigest()
    return md5sum


async def get_tpay_token(
        client_id: str,
        client_secret: str,
) -> str | None:
    """
    get tpay access token
    """

    data = {
        "client_id": client_id,
        "client_secret": client_secret,
        "scope": "read write"
    }
    url = f'{TPAY_OPENAPI}/oauth/auth'
    debugger.debug(f"POST {url}\n{data=}")
    async with ClientSession() as session:
        async with session.post(
                url, json=data,
                headers={'Content-Type': 'application/json'}
        ) as response:
            debugger.debug(f'{response.status=} {await response.text()=}')
            if response.status > 200:
                raise PaymentResponseInvalidStatusError(response.status)
            result = await response.json()
            if result and "access_token" not in result:
                raise PaymentInvalidResponseError("access_token")

    return result["access_token"]


async def create_tpay_payment(
        invoice: Invoice,
        payment: Payment,
        amount_to_pay: int,
        lang: str,
        success_url: str,
        server_url: str,
        code: str,
        merchant_id: str,
) -> TpayPayment:
    """
    url builder:
    https://docs.tpay.com/#!/Tpay/tpay_no_api
    """

    json_data = {
        "id": merchant_id,
        "amount": f"{amount_to_pay / 100:.2f}",
        "description": invoice.title,
        "crc": payment.uuid_id,
        "md5sum": calculate_tpay_checksum(
            merchant_id, f"{amount_to_pay / 100:.2f}", payment.uuid_id, code,
            separator='&'
        ),
        "online": 1,
        # "group": 150,
        "result_url": server_url,
        # "result_email": "<EMAIL>",
        "merchant_description": invoice.title,
        "custom_description": invoice.user.full_name,
        "return_url": success_url,
        "return_error_url": success_url,
        "language": lang,
        "email": invoice.user.email,
        "name": invoice.user.name,
        "address": "",
        "city": "",
        "zip": "",
        "country": "PL",
        "phone": "",
        "accept_tos": 1,
    }

    query_string = urlencode(json_data)
    debugger.debug(f'{query_string=}')

    # hash_object = hashlib.md5(query_string.encode())
    # md5_hash = hash_object.hexdigest()
    # url = f'{TPAY_PMT_PAGE}?h=' + md5_hash
    url = f'{TPAY_PMT_PAGE}?' + query_string

    debugger.debug(f'{url=}')

    tpay_payment = TpayPayment(data=url)

    return tpay_payment


async def create_tpay_transaction(
        invoice: Invoice,
        payment: Payment,
        amount_to_pay: int,
        lang: str,
        success_url: str,
        server_url: str,
        client_id: str,
        client_secret: str,
        user_email: str | None = "",
        user_name: str | None = "",
        user_phone: str | None = "",
        user_address: str | None = "",
) -> TpayTransaction:
    """
    url builder:
    https://openapi.tpay.com/
    """

    data = {
        "amount": amount_to_pay / 100,
        "description": invoice.title,
        "hiddenDescription": payment.uuid_id,
        "callbacks": {
            "payerUrls": {
                "success": success_url,
                "error": success_url,
            },
            "notification": {
                "url": server_url,
                "email": ""
            }
        },
        "lang": lang,
        "payer":
            {
                "name": user_name,
                # "address": "",
                # "city": "",
                # "country": "PL",
                # "phone": "",
                # "code": "",
                # "taxId": "",
            }
    }
    if user_email:
        data["payer"]["email"] = user_email
    if user_phone:
        data["payer"]["phone"] = user_phone
    if user_address:
        data["payer"]["address"] = user_address

    result = await tpay_api(
        f'{TPAY_OPENAPI}/transactions', type_action="POST", client_id=client_id,
        client_secret=client_secret, data=data
    )
    if not result or "result" not in result or not result["result"] == "success":
        raise PaymentInvalidResponseError()

    return TpayTransaction(**result)


async def tpay_api(
        url: str,
        type_action: Literal["GET", "POST", "PUT"],
        client_id: str,
        client_secret: str,
        data: dict | None = None,
        params: dict | None = None,
) -> dict:
    access_token = await get_tpay_token(client_id, client_secret)
    headers = {
        'Content-Type': 'application/json', 'Authorization': f'Bearer {access_token}'
    }

    async with ClientSession() as session:
        async with session.request(
                method=type_action, url=url, json=data, params=params, headers=headers
        ) as response:
            debugger.debug(f"{response.status=} {await response.text()=}")
            result = await response.json()
            if response.status != 200:
                if result.get("errors"):
                    error_message = ""
                    for error in result.get("errors"):
                        if error.get("errorMessage"):
                            if error.get("errorMessage").find(
                                    "This value is too short"
                            ) > -1 and error.get("fieldName"):
                                error["errorMessage"] = error["errorMessage"].replace(
                                    "This value", error["fieldName"].replace(".", " ")
                                )
                            error_message += error["errorMessage"]
                    if error_message:
                        raise PaymentInvalidPayloadError(error_message)
                raise PaymentResponseInvalidStatusError(response.status)
            return result
