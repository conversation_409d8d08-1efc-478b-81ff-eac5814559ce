import logging
import xml.etree.ElementTree as ET

from pydantic import parse_obj_as
from starlette.requests import Request
from starlette.responses import Response

from core.payment.exceptions import (
    PaymentInvalidCallBackDataError, PaymentInvalidReferenceError,
)
from db.models import Invoice, Payment
from schemas import API3GResponse, PaymentCallBackData
from .client import create_directpay_transaction
from .funcs import get_directpay_payment_data
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.directpay")


class DirectpayProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, request: Request):
        body = await request.body()
        root = ET.fromstring(body)

        data_dict = {elem.tag: elem.text for elem in root}

        webhook_data = parse_obj_as(API3GResponse, data_dict)

        debugger.info("Отримані дані API3G:")
        debugger.info(webhook_data.dict())

        if not webhook_data:
            raise PaymentInvalidCallBackDataError(f"{webhook_data=}")
        if not webhook_data.AccRef:
            raise PaymentInvalidReferenceError(f"{webhook_data.AccRef=}")

        return webhook_data.AccRef

    @classmethod
    def return_status(cls, status: str) -> Response:
        response_root = ET.Element("API3G")
        response_element = ET.SubElement(response_root, "Response")
        response_element.text = "OK"

        xml_response = ET.tostring(
            response_root, encoding="utf-8", xml_declaration=True
        )

        return Response(content=xml_response, media_type="application/xml")

    async def create_payment(
            self,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            lang: str,
            success_url: str,
            server_url: str,
            user_email: str | None,
            user_name: str | None,
            user_phone: str | None,
            user_address: str | None,
    ) -> dict:
        result = {"data": {}}
        debugger.debug(f"Directpay ->")
        company_token, is_sandbox = get_directpay_payment_data(credentials)

        directpay_payment = await create_directpay_transaction(
            invoice,
            payment,
            amount_to_pay,
            lang,
            success_url,
            server_url,
            company_token,
            user_email,
            user_name,
            user_phone,
            user_address,
        )

        if directpay_payment and directpay_payment.Result == "000":
            result['data'][
                'url'] = (f"https://secure.3gdirectpay.com//payv2.php?ID="
                          f"{directpay_payment.TransToken}")
        else:
            result['status'] = 'error'
            result['error'] = directpay_payment.ResultExplanation

        debugger.debug(f'Directpay: {directpay_payment=}')
        await payment.save_pmt_data({"directpay": directpay_payment.dict()})

        return result

    async def get_payment_result(
            self,
            request: Request,
            credentials: dict,
            payment_uuid: str,
    ) -> PaymentCallBackData | Response:
        body = await request.body()

        root = ET.fromstring(body)

        data_dict = {elem.tag: elem.text for elem in root}

        data = parse_obj_as(API3GResponse, data_dict)

        _, is_sandbox = get_directpay_payment_data(credentials)

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status='success' if data.Result == '0000' else 'failed',
            external_id=data.TransactionRef,
        )
