from core.payment.exceptions import PaymentNotFoundCredentialsError


def get_directpay_payment_data(credentials: dict) -> tuple[str, bool]:

    is_sandbox = True

    company_token = credentials.get('company_token')

    if not company_token:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    if not credentials.get("is_sandbox"):
        is_sandbox = False

    return company_token, is_sandbox
