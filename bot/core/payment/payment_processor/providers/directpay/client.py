import json
import logging
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from xml.dom import minidom

from aiohttp import ClientSession

from core.payment.exceptions import (
    PaymentInvalidResponseError, PaymentNotFoundCredentialsError,
    PaymentResponseInvalidStatusError,
)
from db.models import Invoice, Payment
from schemas import DirectpayTransaction

debugger = logging.getLogger("debugger.payments.directpay")


def get_future_date(hours_ahead = 2):
    now = datetime.now()
    future_time = now + timedelta(hours=hours_ahead)
    formatted_date = future_time.strftime("%Y/%m/%d %H:%M")

    return formatted_date


def get_directpay_pmt_data(data: dict) -> tuple[str, str, str]:
    security_code = data.get('security_code')
    client_id = data.get('client_id')
    client_secret = data.get('client_secret')
    if not any([security_code, client_id, client_secret]):
        raise PaymentNotFoundCredentialsError(f"{data=}")
    return security_code, client_id, client_secret


async def create_directpay_transaction(
        invoice: Invoice,
        payment: Payment,
        amount_to_pay: int,
        lang: str,
        success_url: str,
        server_url: str,
        company_token: str,
        user_email: str | None = "",
        user_name: str | None = "",
        user_phone: str | None = "",
        user_address: str | None = "",
) -> DirectpayTransaction:
    """
    https://directpayonline.atlassian.net/wiki/spaces/API/pages/36110341/createToken+V6
    """

    root = ET.Element("API3G")

    ET.SubElement(root, "CompanyToken").text = company_token
    ET.SubElement(root, "Request").text = "createToken"

    transaction = ET.SubElement(root, "Transaction")
    ET.SubElement(transaction, "PaymentAmount").text = f"{amount_to_pay / 100:.2f}"
    ET.SubElement(transaction, "PaymentCurrency").text = invoice.currency
    ET.SubElement(transaction, "RedirectURL").text = success_url
    ET.SubElement(transaction, "BackURL").text = success_url

    ET.SubElement(
        root, "MetaData"
    ).text = f'<![CDATA[{json.dumps({"payment_id": payment.uuid_id})}]]>'

    services = ET.SubElement(root, "Services")
    service_elem = ET.SubElement(services, "Service")
    ET.SubElement(service_elem, "ServiceType").text = "3854"
    ET.SubElement(service_elem, "ServiceDescription").text = "Service number 1"
    ET.SubElement(service_elem, "ServiceDate").text = get_future_date()

    xml_str = ET.tostring(root, encoding="utf-8")
    parsed = minidom.parseString(xml_str)
    data = parsed.toprettyxml(indent="  ", encoding="utf-8").decode("utf-8")

    result = await directpay_api(data=data)

    directpay_transaction = DirectpayTransaction(**result)

    return directpay_transaction


async def directpay_api(data: str) -> dict:
    headers = {'Content-Type': 'application/xml'}
    url = "https://secure.3gdirectpay.com/API/v6/"

    async with ClientSession() as session:
        async with session.post(url, data=data, headers=headers) as response:
            response_text = await response.text()
            debugger.debug(f"{response.status=} {response_text=}")

            if response.status > 201:
                raise PaymentResponseInvalidStatusError(response.status)
            if not response_text:
                PaymentInvalidResponseError("empty response")
            result = xml_to_dict(response_text)

    return result


def xml_to_dict(xml_string: str) -> dict:
    root = ET.fromstring(xml_string)
    return element_to_dict(root)


def element_to_dict(element):
    result = {}
    for child in element:
        child_data = element_to_dict(child)
        if child.tag in result:
            if type(result[child.tag]) is list:
                result[child.tag].append(child_data)
            else:
                result[child.tag] = [result[child.tag], child_data]
        else:
            result[child.tag] = child_data or child.text.strip() if child.text else ''
    return result
