import hashlib
import json
import logging
import math
from typing import Literal

from aiohttp import ClientResponse, ClientSession

from config import FLUTTERWAVE_API, NO_CENT_CURRENCIES
from core.payment.exceptions import (
    PaymentInvalidResponseError,
    PaymentNotFoundCredentialsError,
    PaymentResponseInvalidStatusError,
)
from schemas import FlutterwavePaymentResponse

debugger = logging.getLogger("debugger.payments.flutterwave")


def get_flutterwave_pmt_data(flutterwave_data: dict) -> tuple[str, str, int, str, bool]:
    crc = flutterwave_data.get('crc')
    merchant_id = flutterwave_data.get('merchant_id')
    pos_id = flutterwave_data.get('pos_id')
    secret = flutterwave_data.get('secret')
    is_sandbox = flutterwave_data.get('is_sandbox', False)
    if not any([crc, merchant_id, pos_id, secret]):
        raise PaymentNotFoundCredentialsError(f"{flutterwave_data=}")
    return crc, merchant_id, pos_id, secret, is_sandbox


def calculate_checksum(json_dict: dict) -> str:
    parameters_json = json.dumps(json_dict, separators=(',', ':'))
    sha384_hash = hashlib.sha384()
    sha384_hash.update(parameters_json.encode('utf-8'))
    checksum = sha384_hash.hexdigest()

    return checksum


async def test_access(
) -> bool:
    ...
    return True


async def create_flutterwave_payment(
        currency: str,
        payment_uuid_id: str,
        amount_to_pay: int,
        success_url: str,
        secret_key: str,
        user_email: str,
        user_name: str | None = "",
        user_phone: str | None = "",
        title: str | None = "",
        logo: str | None = "",
) -> FlutterwavePaymentResponse:

    payment_url_api = f'{FLUTTERWAVE_API}/payments'

    customer = dict(email=user_email)
    if user_phone:
        customer['phonenumber'] = user_phone
    if user_name:
        customer['name'] = user_name

    redirect_url = success_url

    if currency in NO_CENT_CURRENCIES:
        amount = math.ceil(amount_to_pay / 100)
    else:
        amount = round(amount_to_pay / 100, 2)

    json_data = dict(
        tx_ref=payment_uuid_id,
        amount=f"{amount}",
        currency=currency,
        redirect_url=redirect_url,
        customer=customer,
    )

    if title or logo:
        json_data.update(
            {
                "customizations": dict(
                    title=title if title else "", logo=logo if logo else ""
                )
            }
        )

    result = await flutterwave_api(
        url=payment_url_api, type_action='POST', data=json_data, secret_key=secret_key
    )

    if result:
        return FlutterwavePaymentResponse(**result)

    raise PaymentInvalidResponseError()


async def get_flutterwave_response(response: ClientResponse) -> dict:
    debugger.debug(f"{response.status=} {await response.text()=}")

    result = await response.json()

    if response.status > 201:
        if "error" in result:
            raise PaymentInvalidResponseError(result["error"])
        raise PaymentResponseInvalidStatusError(response.status)

    return result


async def flutterwave_api(
        url: str,
        type_action: Literal["GET", "POST", "PUT"],
        secret_key: str,
        data: dict | None = None,
        params: dict | None = None,
) -> dict:
    headers = {
        'Content-Type': 'application/json', 'Authorization': f'Bearer {secret_key}'
    } if secret_key else None

    debugger.debug(f"request: {type_action=} {url}\n{data=}\n{params=}")

    async with ClientSession() as session:
        async with session.request(
                method=type_action, url=url, json=data, params=params, headers=headers
        ) as response:
            return await get_flutterwave_response(response)
