from core.payment.exceptions import PaymentNotFoundCredentialsError


def get_flutterwave_payment_data(credentials: dict) -> tuple[str, bool]:

    is_sandbox = True

    encryption_key = credentials.get('encryption_key')

    if not encryption_key:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    if encryption_key.find("TEST") > -1:
        is_sandbox = False

    return encryption_key, is_sandbox
