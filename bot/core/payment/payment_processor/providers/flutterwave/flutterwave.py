import logging

from fastapi import Request

from core.payment.exceptions import (
    PaymentCheckSignatureDataError, PaymentInvalidCallBackDataError,
    PaymentInvalidPayloadError, PaymentInvalidReferenceError,
    PaymentInvalidResponseError, PaymentInvalidUrlError,
    PaymentNotFoundCredentialsError,
)
from db.models import Invoice, Payment
from schemas import (
    FlutterwavePaymentResponse, FlutterwaveTransaction,
    PaymentCallBackData,
)
from utils.text import f
from .client import create_flutterwave_payment
from .funcs import get_flutterwave_payment_data
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.flutterwave")


class FlutterwaveProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: FlutterwaveTransaction):
        if not data:
            raise PaymentInvalidCallBackDataError(f"{data=}")
        if not data.txRef:
            raise PaymentInvalidReferenceError(f"{data.txRef=}")

        return data.txRef

    @classmethod
    def return_status(cls, status: str):
        return {"status": status}

    @classmethod
    async def create_payment(
            cls,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            success_url: str,
            user_email: str | None,
            user_name: str | None,
            user_phone: str | None,
            lang: str | None = None,
    ) -> dict:
        result = {"data": {}}
        if not lang:
            lang = "fr"

        debugger.debug(f"create_payment ->")

        secret_key = credentials.get('secret_key')
        if not secret_key:
            raise PaymentNotFoundCredentialsError(f"{secret_key=}")

        if not user_email:
            raise PaymentInvalidPayloadError(await f("payment payload email is required text", lang))

        flutterwave_payment: FlutterwavePaymentResponse = await (
            create_flutterwave_payment(
                invoice.currency,
                payment.uuid_id,
                amount_to_pay,
                success_url,
                secret_key,
                user_email=user_email,
                user_name=user_name,
                user_phone=user_phone,
                title=invoice.title,
                logo=invoice.photo_url,
            ))

        if not flutterwave_payment.data:
            if flutterwave_payment.status in ('cancelled', 'failed'):
                raise PaymentInvalidResponseError(f"{flutterwave_payment.message=}")
            raise PaymentInvalidResponseError()

        if not flutterwave_payment.data.link:
            raise PaymentInvalidUrlError()

        result['data']['url'] = flutterwave_payment.data.link

        await payment.save_pmt_data({"flutterwave": flutterwave_payment.dict()})
        return result

    @classmethod
    async def get_payment_result(
            cls,
            request: Request,
            data: FlutterwaveTransaction,
            credentials: dict,
    ) -> PaymentCallBackData:

        status = "failed"

        encryption_key, is_sandbox = get_flutterwave_payment_data(credentials)

        if request.headers.get("verif-hash") != encryption_key:
            raise PaymentCheckSignatureDataError(
                f'{request.headers.get("verif-hash")=} != {encryption_key=}'
            )

        if data.status == "successful":
            status = "success"

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status=status,
            external_id=data.flwRef,
            card_mask=f"{data.entity.card6}***{data.entity.card_last4}",
            card_country_iso=data.entity.card_country_iso,
        )
