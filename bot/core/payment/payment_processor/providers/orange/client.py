import base64
import logging

from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA
from aiohttp import ClientSession

from config import ORANGE_API_URL
from core.payment.exceptions import (
    PaymentInvalidResponseError, PaymentResponseCommonError,
    PaymentResponseInvalidStatusError,
)

debugger = logging.getLogger('debugger.payments.orange')


async def get_orange_token(client_id: str, client_secret: str):
    url = ORANGE_API_URL + '/oauth/token'

    payload = {
        'client_id': client_id, 'client_secret': client_secret,
        'grant_type': 'client_credentials'
    }
    debugger.debug(f'POST {url}\n{payload=}')
    async with ClientSession() as session:
        async with session.post(url, data=payload) as response:
            debugger.debug(f'{response.status=} {await response.text()=}')
            result = await response.json()
            if response.status > 201:
                raise PaymentResponseInvalidStatusError(response.status)
            if "access_token" not in result:
                raise PaymentInvalidResponseError("access_token not find")

    return result["access_token"]


async def one_step_payment(client_id: str, client_secret: str, data: dict) -> dict:
    token = await get_orange_token(client_id, client_secret)

    profile = await get_customer_profile(token, data.get('customer_phone'))
    debugger.debug(f"one_step_payment -> {profile=}")

    payload = {
        'customer': {
            'idType': 'MSISDN',
            'id': data.get('customer_phone'),
            'otp': data.get('otp'),
        },
        'partner': {
            'idType': 'CODE',
            'id': data.get('partner_code')
        },
        'amount': {
            'value': data.get('amount'),
            'unit': data.get('currency')
        },
        'reference': data.get('reference_id')
    }

    headers = {'Authorization': 'Bearer ' + token}
    url = ORANGE_API_URL + '/api/eWallet/v1/payments/onestep'

    async with ClientSession() as session:
        async with session.post(url, json=payload, headers=headers) as response:
            debugger.debug(f"{response.status=} {await response.text()=}")
            if response.status > 201:
                raise PaymentResponseInvalidStatusError(response.status)
            return await response.json()


async def get_customer_profile(token: str, customer_phone):
    headers = {'Authorization': 'Bearer ' + token}
    url = ORANGE_API_URL + '/api/eWallet/v1/account?msisdn={}&type=customer'.format(
        customer_phone
    )
    async with ClientSession() as session:
        async with session.get(url, headers=headers) as response:
            if response.status > 201:
                raise PaymentResponseInvalidStatusError(response.status)
            return await response.json()


async def get_customer_balance(token: str, customer_phone: str, encrypted_code: str):
    url = ORANGE_API_URL + '/api/eWallet/v1/account/customer/balance'
    headers = {'Authorization': 'Bearer ' + token}
    data = {
        "encryptedPinCode": encrypted_code,
        "id": customer_phone,
        "idType": "MSISDN",
        "wallet": "PRINCIPAL"
    }
    async with ClientSession() as session:
        async with session.post(url, headers=headers, json=data) as response:
            debugger.debug(f"{response.status=} {await response.text()=}")
            if response.status > 201:
                raise PaymentResponseInvalidStatusError(response.status)
            return await response.json()


async def get_test_opt(token: str, customer_phone: str, encrypted_code: str) -> str:
    headers = {'Authorization': 'Bearer ' + token}
    url = ORANGE_API_URL + '/api/eWallet/v1/payments/otp'
    data = {
        "encryptedPinCode": encrypted_code,
        "id": customer_phone,
        "idType": "MSISDN",
        "walletType": "PRINCIPAL"
    }
    async with ClientSession() as session:
        async with session.post(url, headers=headers, json=data) as response:
            debugger.debug(f"{response.status=} {await response.text()=}")
            if response.status > 201:
                raise PaymentResponseInvalidStatusError(response.status)
            result = await response.json()
            if "otp" not in result:
                raise PaymentInvalidResponseError("otp not find")
            return result["otp"]


async def get_public_key(token: str) -> str:
    url = ORANGE_API_URL + '/api/account/v1/publicKeys'
    headers = {'Authorization': 'Bearer ' + token}
    async with ClientSession() as session:
        async with session.get(url, headers=headers) as response:
            debugger.debug(f"{response.status=} {await response.text()=}")
            if response.status > 201:
                raise PaymentResponseInvalidStatusError(response.status)
            result = await response.json()
            if "key" not in result:
                raise PaymentInvalidResponseError("key")
    return result["key"]


async def generate_orange_qr_code(client_id, client_secret, data: dict) -> dict:

    payload = {
        'amount': {
            'value': data.get('amount'),
            'unit': data.get('currency')
        },
        "callbackCancelUrl": data.get('success_url'),
        "callbackSuccessUrl": data.get('success_url'),
        "code": data.get('partner_code'),
        "metadata": {"payment_id": data.get('reference_id')},
        "name": data.get('payment_name'),
        "validity": 3000
    }

    url = "/api/eWallet/v4/qrcode"
    return await orange_api(
        url, client_id, client_secret, payload, data.get('server_url')
    )


async def orange_api(
        url: str, client_id: str, client_secret: str, payload: dict,
        server_url: str | None = None
) -> dict:
    url = ORANGE_API_URL + url
    token = await get_orange_token(client_id, client_secret)
    headers = {
        'Authorization': 'Bearer ' + token, 'X-Api-Key': client_id,
    }
    if server_url:
        headers.update({'X-Callback-Url': server_url})

    debugger.debug(f'POST {url}\n{headers=}\n{payload=}')

    async with ClientSession() as session:
        async with session.post(url, json=payload, headers=headers) as response:
            debugger.debug(f"{response.status=} {await response.text()=}")
            result = await response.json()
            if "code" in result and "detail" in result:
                raise PaymentResponseCommonError(detail=result["detail"])
            if response.status > 201:
                raise PaymentResponseInvalidStatusError(response.status)
            return result


def encrypt_pin_with_public_key(pin_code: str, public_key_str: str) -> str:
    """
    Шифрує PIN-код з використанням простого RSA (без OAEP).
    Використовується для шифрування PIN-коду для Orange API.

    Args:
        pin_code: PIN-код для шифрування (наприклад, "9073")
        public_key_str: Публічний ключ у форматі PEM

    Returns:
        Зашифрований PIN-код у форматі Base64
    """
    try:
        # Завантаження публічного ключа
        rsa_key = RSA.import_key(f"-----BEGIN PUBLIC KEY-----\n{public_key_str}\n-----END PUBLIC KEY-----")
        cipher_rsa = PKCS1_v1_5.new(
            rsa_key
        )  # Використовуємо PKCS1_v1_5 для простого RSA

        # Шифрування PIN-коду
        encrypted_pin = cipher_rsa.encrypt(pin_code.encode())

        # Конвертація у Base64
        encrypted_pin_b64 = base64.b64encode(encrypted_pin).decode()

        return encrypted_pin_b64
    except Exception as e:
        print(f"Error encrypting PIN code: {e}")
        raise Exception(f"Failed to encrypt PIN code: {e}")
