import logging
import math
import urllib.parse

from starlette.responses import PlainTextResponse, RedirectResponse

from config import ORANGE_DIAL_FOR_GET_OTP
from core.payment.exceptions import (
    PaymentCheckSignatureDataError, PaymentCheckUuidsError,
    PaymentInvalidCallBackDataError,
    PaymentInvalidPayloadError, PaymentInvalidReferenceError,
    PaymentInvalidResponseError,
    PaymentResponseCommonError,
)
from db.models import Invoice, Payment
from schemas import (
    OrangeHookResponse, OrangeOtpData,
    PaymentCallBackData, PaymentValues,
)
from utils.text import f
from .client import generate_orange_qr_code, one_step_payment, orange_api
from .funcs import get_orange_payment_data
from ..base import PaymentProvider
from ..funcs import trim_phone_number

debugger = logging.getLogger('debugger.payments.orange')


class OrangeProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: OrangeHookResponse):
        if not data:
            raise PaymentInvalidCallBackDataError(f"{data=}")

        if not data.metadata.payment_id:
            raise PaymentInvalidReferenceError(f"{data.metadata.payment_id=}")

        return data.metadata.payment_id

    @classmethod
    def return_status(cls, status: str):
        return PlainTextResponse(content="TRUE", status_code=200)

    @classmethod
    async def create_payment(
            cls,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            success_url: str,
            server_url: str,
    ) -> dict:
        result = {"data": {}}
        debugger.debug("create_payment ->")
        partner_code, client_id, client_secret, _, _, _ = get_orange_payment_data(
            credentials
        )

        data = {
            'amount': math.ceil(amount_to_pay / 100),
            'reference_id': payment.uuid_id,
            'partner_code': partner_code,
            'currency': invoice.currency, "success_url": success_url,
            "server_url": server_url,
            "payment_name": f"Invoice #{invoice.id}"
        }

        result_pmt = await generate_orange_qr_code(client_id, client_secret, data)
        if not result_pmt:
            raise PaymentInvalidResponseError()

        if result_pmt.get('deepLink'):
            result['data']['orange_money_url'] = result_pmt.get('deepLink')
            result['data']['orange_maxit_url'] = result_pmt['deepLinks']['MAXIT']
        else:
            raise PaymentInvalidResponseError(
                result_pmt[
                    'title'] if 'title' in result_pmt else ""
            )

        await payment.save_pmt_data({"orange2": result_pmt})

        return result

    async def get_payment_result(
            self,
            payment_uuid: str,
            data: OrangeHookResponse,
            credentials: dict,
    ) -> PaymentCallBackData | dict[str, str]:

        if payment_uuid != data.metadata.payment_id:
            raise PaymentCheckUuidsError(f"{payment_uuid=} {data.metadata.payment_id=}")

        partner_code, _, _, is_sandbox, _, _ = get_orange_payment_data(credentials)

        if partner_code != data.partner.id:
            raise PaymentCheckSignatureDataError(
                f"{partner_code=} != {data.partner.id=}"
            )

        status = "success" if (
                data.status == "SUCCESS" and not data.metadata.Cause) else "failed"

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status=status,
            external_id=data.transactionId,
        )

    async def make_business_payment(
            self,
            payment_uuid_id: str,
            amount_to_pay: int,
            currency: str,
            credentials: dict,
            merchant_data: dict,
    ) -> dict | None:
        """
        Виконує операцію Cash In для Orange.
        Використовує зашифрований PIN-код з налаштувань бізнес-платежу.
        """
        debugger.debug(f"orange_business_payment ->")

        _, client_id, client_secret, __, phone, encrypted_pin_code = get_orange_payment_data(
            credentials
        )
        
        if not encrypted_pin_code:
            debugger.error(
                "No encrypted PIN code found in credentials",
                {"phone": phone}
            )
            raise PaymentInvalidPayloadError("No encrypted PIN code found in credentials")
        customer_phone = trim_phone_number(merchant_data.get("phone")).replace('+221', '')
        if customer_phone.startswith('221'):
            customer_phone = customer_phone[3:]
        partner_phone = trim_phone_number(phone).replace('+221', '')
        if partner_phone.startswith('221'):
            partner_phone = partner_phone[3:]

        json = {
            "amount": {
                "unit": currency,
                "value": int(math.ceil(amount_to_pay / 100))
            },
            "customer": {
                "id": customer_phone,
                "idType": "MSISDN",
                "walletType": "PRINCIPAL"
            },
            "metadata": {"payment_uuid_id": payment_uuid_id},
            "partner": {
                "encryptedPinCode": encrypted_pin_code,
                "id": partner_phone,
                "idType": "MSISDN",
                "walletType": "PRINCIPAL"
            },
            "receiveNotification": False,
            "reference": payment_uuid_id,
        }

        url = '/api/eWallet/v1/cashins'
        result = await orange_api(url, client_id, client_secret, json)
        if not isinstance(result, dict):
            raise PaymentResponseCommonError(detail="payment failed")

        if result.get("status") == "SUCCESS":
            return result

        raise PaymentResponseCommonError(detail="payment failed")


class OrangeOtpProvider(PaymentProvider):

    @classmethod
    async def get_payment_uuid(cls, data: OrangeHookResponse):
        if not data:
            raise PaymentInvalidCallBackDataError(f"{data=}")

        if not data.metadata.payment_id:
            raise PaymentInvalidReferenceError(f"{data.metadata.payment_id=}")

        return data.metadata.payment_id

    def return_status(self, success_url: str) -> RedirectResponse:
        return RedirectResponse(url=success_url)

    @classmethod
    async def create_payment(
            cls,
            payment: Payment,
            lang: str,
            success_url: str,
            server_url: str,
            user_phone: str,
            args: list[PaymentValues | None] | None = None,
            # additional args from buyer

    ) -> dict:
        result = {"data": success_url}

        debugger.debug(f"create_payment ->")

        if not args:
            success_url = urllib.parse.urlencode({"success_url": success_url})
            user_phone = trim_phone_number(user_phone).replace('+221', '')
            result = dict(
                data=f'{server_url}?amount={payment.amount}&currency={payment.currency}&'
                     f'{success_url}',
                args=[

                    dict(
                        label=await f('enter orange phone header', lang),
                        name='customer_phone', value=user_phone
                    ),
                    dict(
                        label=await f(
                            'enter orange otp header', lang,
                            dial_number=ORANGE_DIAL_FOR_GET_OTP
                        ),
                        name='otp'
                    ),
                ]
            )
            return result
        return result

    @classmethod
    async def get_payment_result(
            cls,
            payment_uuid: str,
            credentials: dict,
            data: OrangeOtpData,
    ) -> PaymentCallBackData | RedirectResponse:

        debugger.debug(f"get_payment_result ->")
        status = 'failed'

        partner_code, client_id, client_secret, is_sandbox, _, _ = get_orange_payment_data(
            credentials
        )

        if not data.otp:
            raise PaymentCheckSignatureDataError(f"{data.otp=}")

        customer_phone = trim_phone_number(data.customer_phone).replace('+221', '')

        data = {
            'otp': data.otp, 'amount': data.amount / 100,
            'customer_phone': customer_phone,
            'reference_id': payment_uuid,
            'partner_code': partner_code, 'currency': data.currency
        }

        result = await one_step_payment(client_id, client_secret, data)

        if 'status' in result and result['status'] == 'SUCCESS':
            status = 'success'

        return PaymentCallBackData(
            callback_data=result,
            is_sandbox=is_sandbox,
            status=status,
            external_id=result.get('transactionId'),
        )
