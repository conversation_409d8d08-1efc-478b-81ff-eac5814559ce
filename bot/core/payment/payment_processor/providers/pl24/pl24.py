import logging

from fastapi import Request

from config import PL24_PMT_PAGE
from core.payment.exceptions import PaymentCheckUuidsError, \
    PaymentInvalidCallBackDataError, PaymentInvalidResponseError
from db.models import Invoice, Payment
from schemas import PaymentCallBackData, Pl24HookResponse
from .client import create_pl24_payment, verify_pl24_payment
from .funcs import check_hashsum, get_pl24_payment_data
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.pl24")

allowed_ips = [
    "**************",
    "**************",
    "**************",
    "**************",
    "**************",
    "*************",
    "*************",
]


class Pl24Provider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: Pl24HookResponse):
        if not data.sessionId:
            raise PaymentInvalidCallBackDataError(f"{data.sessionId=}")

        return data.sessionId

    @classmethod
    def return_status(cls, status: str):
        return {"status": status}

    async def create_payment(
            self,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            lang: str,
            success_url: str,
            server_url: str,
            user_email: str | None,
            user_name: str | None,
            user_phone: str | None,
            user_address: str | None,
    ) -> dict:
        result = {"data": {}}

        debugger.debug(f"create_payment ->")

        crc, merchant_id, pos_id, secret, is_sandbox = get_pl24_payment_data(
            credentials
        )

        pl24_payment = await create_pl24_payment(
            invoice.id, invoice.currency,
            payment.uuid_id,
            amount_to_pay,
            lang,
            success_url,
            server_url,
            crc,
            pos_id,
            merchant_id,
            secret,
            is_sandbox,
            user_email=user_email,
            user_name=user_name,
            user_phone=user_phone,
            user_address=user_address,
        )

        if pl24_payment and pl24_payment.data:
            await payment.save_pmt_data({"pl24": pl24_payment.dict()})

            result['data']['url'] = (
                f""
                f""
                f"{PL24_PMT_PAGE.replace('secure', 'sandbox') if is_sandbox else PL24_PMT_PAGE}"
                f"/{pl24_payment.data.token}")
        else:
            if pl24_payment and pl24_payment.responseCode != 0:
                raise PaymentInvalidResponseError(f"{pl24_payment.responseCode=}")
            raise PaymentInvalidResponseError()

        return result

    @classmethod
    async def get_payment_result(
            cls,
            request: Request,
            data: Pl24HookResponse,
            credentials: dict,
            payment_uuid: str,
    ) -> PaymentCallBackData:

        if request.client.host not in allowed_ips:
            debugger.debug(f"{request.client.host=} not in allowed_ips")
            # raise HTTPException(status_code=403, detail="Access forbidden.")

        crc, merchant_id, pos_id, secret, is_sandbox = get_pl24_payment_data(
            credentials
        )

        result = PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status="failed",
            external_id=data.orderId,
        )

        if data.sessionId != payment_uuid:
            raise PaymentCheckUuidsError(f"{payment_uuid=}, {data.sessionId=}")

        check_hashsum(crc, merchant_id, pos_id, data)

        if await verify_pl24_payment(
                data.sessionId, data.amount, data.currency,
                data.orderId,
                crc, pos_id, merchant_id, secret, is_sandbox
        ):
            result.status = 'success'

        return result
