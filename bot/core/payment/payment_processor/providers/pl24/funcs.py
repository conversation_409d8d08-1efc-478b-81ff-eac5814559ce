import hashlib
import json
import logging

from core.payment.exceptions import (
    PaymentCheckSignatureDataError,
    PaymentNotFoundCredentialsError,
)

debugger = logging.getLogger("debugger.payments.pl24")


def calculate_checksum(json_dict: dict) -> str:
    parameters_json = json.dumps(json_dict, separators=(',', ':'))
    sha384_hash = hashlib.sha384()
    sha384_hash.update(parameters_json.encode('utf-8'))
    checksum = sha384_hash.hexdigest()

    return checksum


def check_hashsum(crc, merchant_id, pos_id, webhook_data):
    check_hash = calculate_checksum(
        dict(
            merchantId=merchant_id,
            posId=pos_id,
            sessionId=webhook_data.sessionId,
            amount=webhook_data.amount,
            originAmount=webhook_data.originAmount,
            currency=webhook_data.currency,
            orderId=webhook_data.orderId,
            methodId=webhook_data.methodId,
            statement=webhook_data.statement,
            crc=crc
        )
    )
    if not check_hash:
        raise PaymentCheckSignatureDataError("check_hash = calculate_checksum = None")
    if check_hash != webhook_data.sign:
        debugger.debug(
            f"Check hash not equals!!! {check_hash=} != {webhook_data.sign=}"
        )
        # raise PaymentCheckSignatureDataError(f"{check_hash=} <> {webhook_data.sign=}")


def get_pl24_payment_data(credentials: dict) -> tuple[str, str, int, str, bool]:
    crc = credentials.get('crc')
    merchant_id = credentials.get('merchant_id')
    pos_id = credentials.get('pos_id')
    secret = credentials.get('secret')
    is_sandbox = credentials.get('is_sandbox', False)
    if not any([crc, merchant_id, pos_id, secret]):
        raise PaymentNotFoundCredentialsError(
            f"{credentials=}"
        )
    return crc, merchant_id, pos_id, secret, is_sandbox
