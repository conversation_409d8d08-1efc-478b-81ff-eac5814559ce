import logging
from typing import Literal

from aiohttp import BasicAuth, ClientResponse, ClientSession

from core.payment.payment_processor.providers.pl24.funcs import \
    calculate_checksum
from config import PL24API
from core.payment.exceptions import (
    PaymentGetSignatureDataError, PaymentInvalidPayloadError,
    PaymentInvalidResponseError,
    PaymentResponseInvalidStatusError,
)
from schemas import Pl24Payment
from utils.email_funcs import is_valid_email

debugger = logging.getLogger("debugger.payments.pl24")


async def test_access(
        pos_id: int,
        secret: str,
        is_sandbox: bool | None = False,
) -> bool:
    """
    test access:
    https://developers.przelewy24.pl/index.php?en#tag/Additional-API-functionality
    """
    payment_url_api = (f''
                       f''
                       f''
                       f'{PL24API.replace("secure", "sandbox") if is_sandbox else PL24API}/api/v1/testAccess')

    resp = await pl24_api(
        url=payment_url_api, type_action='GET', user=pos_id, secret=secret
    )

    if resp and "data" in resp:
        return True

    raise PaymentGetSignatureDataError("")


async def create_pl24_payment(
        invoice_id: int,
        currency: str,
        payment_uuid_id: str,
        amount_to_pay: int,
        lang: str,
        success_url: str,
        server_url: str,
        crc: str,
        pos_id: int,
        merchant_id: str,
        secret: str,
        is_sandbox: bool | None,
        user_email: str | None = "",
        user_name: str | None = "",
        user_phone: str | None = "",
        user_address: str | None = "",
) -> Pl24Payment:
    """
    reg transaction:
    https://developers.przelewy24.pl/index.php?en#tag/Transaction-service-API/paths/~1api~1v1~1transaction~1register/post
    """
    payment_url_api = f'{PL24API.replace("secure", "sandbox") if is_sandbox else PL24API}/api/v1/transaction/register'

    json_data = {
        "merchantId": merchant_id,
        "posId": pos_id,
        "sessionId": payment_uuid_id,
        "amount": amount_to_pay,
        "currency": currency,
        "description": f"# {invoice_id}",
        # "email": user_email,
        # "client": user_name,
        # "address": user_address,
        # "zip": "",
        # "city": "",
        "country": "PL",
        # "phone": user_phone,
        "language": lang,
        # "method": 0, # Ціле число
        # Ідентифікатор способу оплати. Список способів оплати, представлених на панелі або доступних через API
        "urlReturn": success_url,
        "urlStatus": server_url,
        "timeLimit": 0,
        "channel": 1 + 2 + 4 + 4096 + 8192,
        # Enum: 1 2 4 8 16 32 64 ************ 8192 16384
        # 1 - картка + ApplePay + GooglePay, 2 - переказ, 4 - традиційний переказ, 8 - Н/Д,
        # 16 - всі 24/7 - робить доступними всі способи оплати, 32 - використовувати передоплату,
        # 64 - тільки способи оплати за посиланням, 128 - форми оплати в розстрочку, 256 - гаманці, 4096 - карта,
        # 8192 - blik, 16384 - всі способи, крім blik
        # Щоб активувати конкретні канали, їх значення слід підсумувати.
        # Приклад: Трансфер і традиційний трансфер: канал=6
        "waitForResult": False,
        "regulationAccept": False,
        "shipping": 0,
        "transferLabel": "",
        # "mobileLib": 0,
        # "sdkVersion": "",
        "sign": calculate_checksum(
            dict(
                sessionId=payment_uuid_id, merchantId=int(merchant_id),
                amount=amount_to_pay, currency=currency, crc=crc
            )
        ),
        "encoding": "UTF-8",
        "methodRefId": "",
        # Special parameter for some payment flows e.g. BLIK and Card one-click.
        # "urlCardPaymentNotification": "",  # callback for get info about card
        # "cart": [
        #   {
        #     "sellerId": store_id if store_id else 0,
        #     "sellerCategory": "other",
        #     "name": None,
        #     "description": "",
        #     "quantity": 1,
        #     "price": 0,
        #     "number": ""
        #   }
        # ],
        # "additional": {
        #   "shipping": {
        #     "type": 0,
        #     "address": "",
        #     "zip": "",
        #     "city": "",
        #     "country": ""
        #   }
        # }
    }

    if user_email and is_valid_email(user_email):
        json_data["email"] = user_email
    if user_phone:
        json_data["phone"] = user_phone
    if user_name:
        json_data["client"] = user_name
    if user_address:
        json_data["address"] = user_address

    result = await pl24_api(
        url=payment_url_api, type_action='POST', data=json_data, user=pos_id,
        secret=secret
    )

    if result:
        return Pl24Payment(**result)

    raise PaymentInvalidResponseError()


async def verify_pl24_payment(
        uuid_id: str,
        amount: int,
        currency: str,
        order_id: int,
        crc: str,
        pos_id: int,
        merchant_id: str,
        secret: str,
        is_sandbox: bool | None = False,
) -> bool:
    """
    reg transaction:
    https://developers.przelewy24.pl/index.php?en#tag/Transaction-service-API/paths/~1api~1v1~1transaction~1register/post
    """
    payment_url_api = f'{PL24API.replace("secure", "sandbox") if is_sandbox else PL24API}/api/v1/transaction/verify'

    data = {
        "merchantId": merchant_id,
        "posId": pos_id,
        "sessionId": uuid_id,
        "amount": amount,
        "currency": currency,
        "orderId": order_id,
        "sign": calculate_checksum(
            dict(
                sessionId=uuid_id, orderId=order_id,
                amount=amount, currency=currency, crc=crc
            )
        ),
    }

    result = await pl24_api(
        url=payment_url_api, type_action='PUT', data=data, user=pos_id, secret=secret
    )

    if result and "data" in result and "status" in result["data"] and result["data"][
        "status"] == "success":
        return True

    raise PaymentInvalidResponseError()


async def get_pl24_response(response: ClientResponse) -> dict:
    debugger.debug(f'{response.status=} {await response.text()=}')

    result = await response.json()

    if response.status > 201:
        if "error" in result:
            raise PaymentInvalidPayloadError(result['error'])
        raise PaymentResponseInvalidStatusError(response.status)

    return result


async def pl24_api(
        url: str,
        type_action: Literal["GET", "POST", "PUT"],
        user: id,
        secret: str,
        data: dict | None = None,
        params: dict | None = None,
) -> dict:
    auth = BasicAuth(user, secret)
    headers = {'Content-Type': 'application/json'}

    debugger.debug(
        f"request: {type_action} {url}\n{data=}\n{params=}\n{user=} {secret=}"
    )

    async with ClientSession() as session:
        async with session.request(
                method=type_action, url=url, json=data, params=params, headers=headers,
                auth=auth
        ) as response:
            return await get_pl24_response(response)
