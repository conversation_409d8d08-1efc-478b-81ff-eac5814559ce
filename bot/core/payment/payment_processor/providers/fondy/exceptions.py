class RequestError(Exception):

    def __init__(self, param: str):
        self.param = param

    def __str__(self):
        return f"Required parameter '{self.param}' is missing."


def process_error(response: dict):
    message = ""
    # if "response_status" in response:
    #     status = response.get("response_status", "")
    #     message += f"Response status is {status}."

    if "error_message" in response:
        message += response.get("error_message")

    # if "error_code" in response:
    #     code = response.get("error_code")
    #     message += f" Error code: {code}."

    if "request_id" in response:
        request_id = response.get("request_id")
        message += f" Request id: {request_id}."

    # message += " Check parameters"
    return message
