import logging

from fastapi import Request

from core.payment.exceptions import (
    PaymentCheckSignatureDataError, PaymentCheckUuidsError,
    PaymentInvalidCallBackDataError,
    PaymentInvalidReferenceError, PaymentInvalidResponseError, PaymentInvalidUrlError,
)
from db.models import Invoice, Payment
from schemas import FondyHookResponse, PaymentCallBackData
from .api import FondyClient, verify_payment
from .functions import get_fondy_payment_data
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.fondy")

allowed_ips = [
    "*************",
]


class FondyProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: FondyHookResponse):
        if not data:
            raise PaymentInvalidCallBackDataError(f"{data=}")

        if not data.merchant_data:
            raise PaymentInvalidReferenceError(f"{data.merchant_data=}")

        return data.merchant_data

    @classmethod
    def return_status(cls, status: str):
        return {"status": status}

    @classmethod
    async def create_payment(
            cls,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            lang: str,
            success_url: str,
            server_url: str,
            user_email: str | None,
    ) -> dict:
        result = {"data": {}}

        debugger.debug("Fondy -> create_payment")

        client = FondyClient(
            credentials.get("merchant_id"), credentials.get("secret_key"), )

        fondy_payment = await client.create_payment(
            payment,
            amount_to_pay,
            lang,
            success_url,
            server_url,
            user_email=user_email,
        )

        fondy_checkout_data = await client.checkout_url(fondy_payment)
        if fondy_checkout_data:
            await payment.save_pmt_data({"fondy": fondy_checkout_data.dict()})
            if fondy_checkout_data.error:
                raise PaymentInvalidResponseError(fondy_checkout_data.error)

            if fondy_checkout_data.data and fondy_checkout_data.data.url:
                result["data"]["url"] = fondy_checkout_data.data.url
            else:
                PaymentInvalidUrlError()

        return result

    @classmethod
    async def get_payment_result(
            cls,
            request: Request,
            data: FondyHookResponse,
            payment_uuid: str,
            credentials: dict,
    ) -> PaymentCallBackData:
        debugger.debug(
            f"Fondy -> Webhook event received {data=}"
        )
        status = "failed"

        if request.client.host not in allowed_ips:
            debugger.debug(f"{request.client.host=} not in allowed_ips")

        if payment_uuid != data.merchant_data:
            raise PaymentCheckUuidsError(f"{payment_uuid=} {data.merchant_data=}")

        merchant_id, secret_key, is_sandbox = get_fondy_payment_data(credentials)

        client = FondyClient(merchant_id, secret_key)

        check_signature = await client.check_signature(data)

        if check_signature != data.signature:
            raise PaymentCheckSignatureDataError(
                f"{check_signature=} {data.signature=}"
            )

        if await verify_payment(data):
            status = "success"

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status=status,
            external_id=data.payment_id,
            card_mask=data.masked_card,
            card_type=data.card_type,
        )
