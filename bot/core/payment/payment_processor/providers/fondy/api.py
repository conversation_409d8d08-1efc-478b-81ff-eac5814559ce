import logging

from aiohttp import ClientSession

import schemas
from core.payment.exceptions import (
    PaymentGetSignatureDataError, PaymentInvalidResponseError,
    PaymentNotFoundCredentialsError, PaymentResponseInvalidStatusError,
)
from db.models import Payment
from utils.email_funcs import is_valid_email
from .exceptions import process_error
from .functions import (
    check_data, generate_description, generate_order_id,
    get_signature,
)


async def verify_payment(
        webhook_data: schemas.FondyHookResponse | None = None
) -> bool:
    if not webhook_data or webhook_data.response_status != "success":
        return False

    if webhook_data.order_status != "approved":
        return False

    payment = await Payment.get(uuid_id=webhook_data.merchant_data)
    if not payment:
        return False

    if payment.currency != webhook_data.currency:  # or (payment.amount !=
        # webhook_data.amount): # TODO: need add service fee
        return False

    return True


class FondyClient:
    PROTOCOL = "1.0.1"

    def __init__(self, merchant_id: int, secret_key: str):
        self.merchant_id: int = merchant_id
        self.secret_key: str = secret_key

        if not any([self.merchant_id, self.secret_key]):
            raise PaymentNotFoundCredentialsError(f"{merchant_id=} {secret_key=}")

        self.api_url: str = "https://pay.fondy.eu/api"
        self.order_id: str | None = None

        self.debugger = logging.getLogger("debugger.payments.fondy")

    @property
    def headers(self):
        return {
            "Content-Type": "application/json; charset=utf-8",
        }

    async def _request(self, url: str, data: dict) -> dict:
        self.debugger.debug("%s URL: %s", self.__class__.__name__, url)
        self.debugger.debug("%s request data: %s", self.__class__.__name__, str(data))

        async with ClientSession(headers=self.headers) as session:
            async with session.post(url, json={"request": data}) as response:
                self.debugger.debug(f"{response.status=} {await response.text()=}")

                if response.status > 201:
                    raise PaymentResponseInvalidStatusError(response.status)

                data = await response.json()
                data = data.get("response", data)
                self.debugger.debug(
                    "%s response data: %s", self.__class__.__name__, str(data)
                )

                if "error_message" in data:
                    raise PaymentInvalidResponseError(process_error(data))
                return data

    async def post(self, url: str, data: dict) -> dict:
        if "merchant_id" not in data:
            data["merchant_id"] = self.merchant_id

        data["version"] = self.PROTOCOL
        data["signature"] = get_signature(self.secret_key, data)

        self.debugger.debug("%s payment data: %s", self.__class__.__name__, str(data))
        return await self._request(f"{self.api_url}{url}", data=data)

    def _required(self, data: dict) -> dict:
        self.order_id = data.get("order_id") or generate_order_id()
        order_description = data.get("order_desc", generate_description(self.order_id))

        params = {
            "order_id": self.order_id,
            "order_desc": order_description,
            "amount": data.get("amount", ""),
            "currency": data.get("currency", "")
        }

        check_data(params)
        params.update(data)

        return params

    async def checkout_url(
            self, data: schemas.FondyPaymentData
    ) -> schemas.FondyCheckOutData:
        path = "/checkout/url/"
        params = self._required(data.dict(exclude_unset=True))

        result = await self.post(path, data=params)
        data = schemas.FondyCheckOutResponseData(**result)

        return schemas.FondyCheckOutData(
            data=schemas.PaymentCheckOutData(url=data.checkout_url),
            payment_id=data.payment_id,
            error=data.error_message,
        )

    async def create_payment(
            self,
            payment: Payment,
            amount_to_pay: int,
            lang: str,
            success_url: str,
            server_url: str,
            user_email: str | None = None,
    ) -> schemas.FondyPaymentData:
        data = schemas.FondyPaymentData(
            merchant_id=self.merchant_id,
            amount=amount_to_pay,
            currency=payment.currency,
            merchant_data=payment.uuid_id,
            lang=lang,
            response_url=success_url,
            server_callback_url=server_url,
        )

        if user_email and is_valid_email(user_email):
            data.sender_email = user_email

        return data

    async def check_signature(
            self,
            webhook_data: schemas.FondyHookResponse | None =
            None,
    ) -> str | None:
        try:
            signature = webhook_data.signature
            data = webhook_data.dict(exclude={"signature"}, exclude_unset=True)
            self.debugger.debug("%s\n%s", signature, str(data))

            data["merchant_id"] = self.merchant_id
            return get_signature(self.secret_key, data)
        except Exception as e:
            raise PaymentGetSignatureDataError(str(e))
