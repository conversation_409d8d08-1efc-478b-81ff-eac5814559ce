import re
import uuid
from hashlib import sha1

from core.payment.exceptions import PaymentNotFoundCredentialsError
from .exceptions import RequestError


def get_signature(secret_key: str, params: dict) -> str:
    data = [secret_key]
    data.extend(
        [
            str(params[key]) for key in sorted(iter(params.keys()))
            if params[key] != "" and params[key] is not None
        ]
    )
    return sha1("|".join(data).encode("utf-8")).hexdigest()


def join_url(url: str, *paths: list[str]) -> str:
    for path in paths:
        url = re.sub(r"/?$", re.sub(r"^/?", "/", path), url)
    return url


def generate_order_id() -> str:
    return str(uuid.uuid4())


def generate_description(order_id: str) -> str:
    return f"Pay for order #: {order_id}"


def check_data(data: dict):
    for key, value in data.items():
        if not value:
            raise RequestError(key)
        if key == "amount":
            try:
                int(value)
            except ValueError as exc:
                raise ValueError("Amount must numeric") from exc


def get_fondy_payment_data(credentials: dict) -> tuple[None, None, bool]:

    is_sandbox = True
    secret_key = credentials.get('secret_key', None)
    merchant_id = credentials.get('merchant_id', None)

    if not secret_key or not merchant_id:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    if not credentials.get("is_sandbox"):
        is_sandbox = False

    return merchant_id, secret_key, is_sandbox
