from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
from base64 import b64encode
import binascii

from config import AIRTEL_PUBLIC_KEY


class AirtelEncryption:
    def __init__(self, ):
        # Remove headers, footers and newlines from the public key
        clean_key = AIRTEL_PUBLIC_KEY

        # Convert the key from base64 to binary
        key_bytes = binascii.a2b_base64(clean_key)

        # Create RSA key object
        self.key = RSA.importKey(key_bytes)

    def encrypt(self, data: str) -> str:
        # Convert string data to bytes
        data_bytes = data.encode('utf-8')

        # Create cipher object
        cipher = PKCS1_v1_5.new(self.key)

        # Encrypt the data
        encrypted_bytes = cipher.encrypt(data_bytes)

        # Convert to base64 string
        encrypted_b64 = b64encode(encrypted_bytes)

        # Return as string
        return encrypted_b64.decode('utf-8')


# Usage example:
def encrypt_pin(pin: str, ) -> str:
    encryption = AirtelEncryption()
    return encryption.encrypt(pin)
