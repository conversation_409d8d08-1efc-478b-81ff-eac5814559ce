import logging

from core.payment.exceptions import (
    PaymentFailedError, PaymentInvalidCallBackDataError,
    PaymentInvalidReferenceError, PaymentResponseCommonError,
)
from db.models import Invoice, Payment
from schemas import (
    AirTelCallback, AirTelCashInResponse, AirTelDisbursementResponse,
    AirTelInitPaymentResponse, BusinessPaymentResult,
    BusinessPaymentStatusEnum,
    PaymentCallBackData,
)
from .client import (
    create_airtel_cashin_payment, create_airtel_payment,
    get_airtel_pmt_data,
)
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.airtel")


class AirtelProvider(PaymentProvider):
    """
    https://developers.airtel.africa/documentation/authorization/1.0
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: AirTelCallback):
        if not data:
            raise PaymentInvalidCallBackDataError(f"{data=}")
        if not data.transaction:
            raise PaymentInvalidReferenceError(f"{data.transaction=}")

        return data.transaction.id

    @classmethod
    def return_status(cls, status: str):
        return {"status": status}

    @classmethod
    async def create_payment(
            cls,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            success_url: str,
            user_email: str | None,
            user_name: str | None,
            user_phone: str | None,
    ) -> dict:
        result = {"data": {}}

        _, _, _ = get_airtel_pmt_data(credentials)

        airtel_payment: AirTelInitPaymentResponse = await (
            create_airtel_payment(
                payment.uuid_id,
                amount_to_pay,
                credentials,
                invoice.currency,
                user_phone,
                invoice.title,
            ))

        result["data"]["url"] = success_url

        debugger.debug(f"AirTel: {airtel_payment=}")
        await payment.save_pmt_data({"airtel": airtel_payment.dict()})

        return result

    @classmethod
    async def get_payment_result(
            cls,
            data: AirTelCallback,
            credentials: dict,
    ) -> PaymentCallBackData:
        debugger.debug("get_payment_result ->")

        if data.transaction.status_code == "TS":
            return PaymentCallBackData(
                callback_data=data.dict(),
                is_sandbox=credentials.get("is_sandbox", True),
                status="success",
                external_id=data.transaction.airtel_money_id,
            )

        raise PaymentFailedError(data.transaction.message)

    async def make_business_payment(
            self,
            payment_uuid_id: str,
            amount_to_pay: int,
            currency: str,
            credentials: dict,
            merchant_data: dict,
    ) -> dict:
        debugger.debug(f"make_business_payment ->")
        result = await create_airtel_cashin_payment(
            payment_uuid_id, amount_to_pay, credentials, currency,
            merchant_data
        )
        if not isinstance(result, dict):
            raise PaymentResponseCommonError(detail="payment failed")

        airtel_result = AirTelCashInResponse(**result)

        if (airtel_result.data.status == "SUCCESS" and airtel_result.status.message ==
                "SUCCESS"):
            return result
        if airtel_result.data.message:
            raise PaymentResponseCommonError(detail=airtel_result.data.message)

        raise PaymentResponseCommonError(detail="payment failed")

    async def make_business_payment2(
            self,
            payment_uuid_id: str,
            amount_to_pay: int,
            currency: str,
            credentials: dict,
            merchant_data: dict,
    ) -> dict | None:
        debugger.debug(f"make_business_payment ->")
        result = await create_airtel_cashin_payment(
            payment_uuid_id, amount_to_pay, credentials, currency,
            merchant_data
        )
        if not isinstance(result, dict):
            raise PaymentResponseCommonError(detail="payment failed")

        airtel_result = AirTelDisbursementResponse(**result)

        if (airtel_result.data.transaction.status == "TS" and
        airtel_result.status.message == "SUCCESS"):
            return result
        if airtel_result.data.transaction.message:
            raise PaymentResponseCommonError(
                detail=airtel_result.data.transaction.message
            )

        raise PaymentResponseCommonError(detail="payment failed")
