from typing import Literal
import base64
import json
from Crypto.Cipher import A<PERSON>, PKCS1_OAEP
from Crypto.PublicKey import RSA
from Crypto.Random import get_random_bytes

from aiohttp import ClientResponse, ClientSession

from core.payment.payment_processor.providers.airtel.funcs import encrypt_pin
from config import AIRTEL_API, AIRTEL_API_TEST
from core.payment.exceptions import (
    PaymentInvalidPayloadError, PaymentNotFoundCredentialsError,
    PaymentResponseInvalidStatusError,
)
from loggers import <PERSON><PERSON>NLogger
from schemas import AirTelInitPaymentResponse


def get_airtel_pmt_data(credentials: dict) -> tuple[str, str, bool]:
    client_id = credentials.get("client_id")
    client_secret = credentials.get("client_secret")
    is_sandbox = credentials.get("is_sandbox", True)
    if not any([client_id, client_secret]):
        raise PaymentNotFoundCredentialsError(f"{credentials=}")
    return client_id, client_secret, is_sandbox


async def test_access(credentials: dict) -> bool:
    if await get_airtel_token(
            credentials["client_id"], credentials["client_secret"],
            credentials["is_sandbox"]
    ):
        return True


async def get_airtel_token(
        client_id: str, client_secret: str, is_sandbox: bool | None = True
) -> str:
    api_url = f"{AIRTEL_API_TEST if is_sandbox else AIRTEL_API}/auth/oauth2/token"

    result = await airtel_api(
        url=api_url, type_action="POST",
        json_data={
            "client_id": client_id,
            "client_secret": client_secret,
            "grant_type": "client_credentials"
        }
    )
    return result["access_token"]


def create_airtel_signs(rsa_public_key_pem: str, payload: dict):
    # 1. Генеруємо випадковий AES ключ та IV
    aes_key = get_random_bytes(32)  # 256-бітний AES ключ
    aes_iv = get_random_bytes(16)  # 128-бітний IV

    # 2. Шифруємо payload за допомогою AES ключа та IV
    def encrypt_aes(data, key, iv):
        cipher = AES.new(key, AES.MODE_CBC, iv)
        # Вирівнювання PKCS5Padding
        pad = lambda s: s + (16 - len(s) % 16) * chr(16 - len(s) % 16)
        data_padded = pad(json.dumps(data)).encode()
        encrypted_payload = cipher.encrypt(data_padded)
        return base64.b64encode(encrypted_payload).decode()

    # Зашифрований payload для заголовка x-signature
    encrypted_payload = encrypt_aes(payload, aes_key, aes_iv)

    # 3. Конкатенуємо AES ключ та IV
    key_iv_concat = base64.b64encode(aes_key).decode() + ":" + base64.b64encode(
        aes_iv
    ).decode()

    # 4. Шифруємо key:iv за допомогою RSA публічного ключа
    def encrypt_rsa_key_iv(key_iv, public_key_pem):
        rsa_key = RSA.import_key(public_key_pem)
        cipher_rsa = PKCS1_OAEP.new(rsa_key)
        encrypted_key_iv = cipher_rsa.encrypt(key_iv.encode())
        return base64.b64encode(encrypted_key_iv).decode()

    # Зашифрований AES ключ та IV для заголовка x-key
    encrypted_key_iv = encrypt_rsa_key_iv(key_iv_concat, rsa_public_key_pem)
    return encrypted_payload, encrypted_key_iv


async def get_rsa_public_key_pem(token, country, currency, is_sandbox):
    headers = {
        'Accept': '*/* ',
        'Content-Type': 'application/json',
        'X-Country': country,
        'X-Currency': currency,
        'Authorization': f'Bearer {token}'
    }

    api_url = f"{AIRTEL_API_TEST if is_sandbox else AIRTEL_API}/v1/rsa/encryption-keys"

    result = await airtel_api(url=api_url, type_action="GET", headers=headers)
    return result["data"]["key"]


async def create_airtel_payment(
        payment_uuid_id: str,
        amount_to_pay: int,
        credentials: dict,
        currency: str,
        msisdn: str,
        title: str,
) -> AirTelInitPaymentResponse:

    is_sandbox = credentials.get("is_sandbox", True)
    json_data = {
        "reference": title,
        "subscriber": {
            "country": credentials["country"],
            "currency": currency,
            "msisdn": msisdn,
        },
        "transaction": {
            "amount": amount_to_pay,
            "country": "UG",
            "currency": currency,
            "id": payment_uuid_id,
        }
    }

    headers = await make_airtel_header(credentials, currency, is_sandbox, json_data)

    api_url = f"{AIRTEL_API_TEST if is_sandbox else AIRTEL_API}/standard/v2/cashin/"

    result = await airtel_api(
        url=api_url, type_action="POST", json_data=json_data, headers=headers
    )
    return AirTelInitPaymentResponse(**result)


async def get_airtel_response(response: ClientResponse) -> dict:
    logger = JSONLogger(
        "payments.airtel",
        "get_airtel_response",
        {
            "response.status": response.status,
            "response.text": await response.text(),
        }
    )
    logger.debug()

    result = await response.json()

    if response.status > 201:
        if "error_description" in result:
            raise PaymentInvalidPayloadError(result["error_description"])
        raise PaymentResponseInvalidStatusError(response.status)

    return result


async def create_airtel_cashin_payment(
        payment_uuid_id: str,
        amount_to_pay: int,
        credentials: dict,
        currency: str,
        merchant_data: dict,
) -> dict:

    is_sandbox = credentials.get("is_sandbox", True)
    json_data = {
        "subscriber": {
            "msisdn": merchant_data["msisdn"]
        },
        "transaction": {
            "amount": f"{amount_to_pay}",
            "id": payment_uuid_id
        },
        "additional_info": [
            {
                "key": "payment_uuid_id",
                "value": payment_uuid_id
            }
        ],
        "reference": payment_uuid_id,
        "pin": encrypt_pin(merchant_data["pin"])
    }

    headers = await make_airtel_header(credentials, currency, is_sandbox, json_data)

    api_url = f"{AIRTEL_API_TEST if is_sandbox else AIRTEL_API}/merchant/v2/payments/"

    return await airtel_api(
        url=api_url, type_action="POST", json_data=json_data, headers=headers
    )


async def create_airtel_disbursements_payment(
        payment_uuid_id: str,
        amount_to_pay: int,
        credentials: dict,
        currency: str,
        merchant_data: dict,
) -> dict:

    is_sandbox = credentials.get("is_sandbox", True)
    json_data = {
        "payee": {
            "msisdn": merchant_data["msisdn"],
            "wallet_type": "SALARY",  # "SALARY or NORMAL"
        },
        "reference": payment_uuid_id,
        "pin": encrypt_pin(merchant_data["pin"]),
        "transaction": {
            "amount": amount_to_pay,
            "id": payment_uuid_id,
            "type": "B2C",  # "B2C or B2B"
        }
    }

    headers = await make_airtel_header(credentials, currency, is_sandbox, json_data)

    api_url = f"{AIRTEL_API_TEST if is_sandbox else AIRTEL_API}/merchant/v2/payments/"

    return await airtel_api(
        url=api_url, type_action="POST", json_data=json_data, headers=headers
    )


async def make_airtel_header(credentials, currency, is_sandbox, json_data):
    token = await get_airtel_token(
        credentials["client_id"], credentials["client_secret"], is_sandbox
    )
    rsa_public_key_pem = await get_rsa_public_key_pem(
        token, credentials["country"], currency, is_sandbox
    )
    signature, key = create_airtel_signs(rsa_public_key_pem, json_data)
    headers = {
        'Accept': '*/* ',
        'Content-Type': 'application/json',
        'X-Country': 'UG',
        'X-Currency': 'UGX',
        'Authorization': f'Bearer {token}',
        'x-signature': signature,
        'x-key': key
    }
    return headers


async def airtel_api(
        url: str,
        type_action: Literal["GET", "POST", "PUT"],
        json_data: dict | None = None,
        params: dict | None = None,
        headers: dict | None = None,
) -> dict:

    logger = JSONLogger(
        "payments.airtel",
        "airtel_api",
        {
            "type_action": type_action,
            "url": url,
            "headers": headers,
            "json": json_data,
            "params": params,
        }
    )

    logger.debug()

    async with ClientSession() as session:
        async with session.request(
                method=type_action, url=url, json=json_data, headers=headers,
                params=params
        ) as response:
            result = await get_airtel_response(response)
    return result
