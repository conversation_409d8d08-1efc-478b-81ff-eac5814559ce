import hashlib

from core.payment.exceptions import (
    PaymentCheckSignatureDataError,
    PaymentNotFoundCredentialsError,
)
from schemas import ComsaPaymentNotification


def verify_payment_notification(
        notification: ComsaPaymentNotification, secret_key: str
) -> bool | str:
    required_fields = [
        notification.AGR_TRANS_ID,
        notification.VENDOR_TRANS_ID,
        notification.SIGN_TIME,
        notification.SIGN_STRING
    ]

    if not all(field is not None for field in required_fields):
        raise PaymentCheckSignatureDataError(
            f"Some of required fields are None: {required_fields=}"
        )

    expected_sign_string = hashlib.md5(
        f"{secret_key}{notification.AGR_TRANS_ID}{notification.VENDOR_TRANS_ID}"
        f"{notification.STATUS}{notification.SIGN_TIME}".encode()
    ).hexdigest()

    if expected_sign_string != notification.SIGN_STRING:
        raise PaymentCheckSignatureDataError(
            f'{expected_sign_string=} != {notification.SIGN_STRING=}'
        )

    return True


def get_comsa_payment_data(credentials: dict) -> tuple[str, bool]:

    is_sandbox = True
    secret_key = credentials.get('secret_key')

    if not secret_key:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    if not credentials.get("is_sandbox"):
        is_sandbox = False

    return secret_key, is_sandbox
