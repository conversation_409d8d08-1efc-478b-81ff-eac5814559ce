import logging

from core.payment.exceptions import (
    PaymentInvalidCallBackDataError,
    PaymentInvalidReferenceError, PaymentInvalidResponseError, PaymentInvalidUrlError,
    PaymentNotFoundCredentialsError,
)
from db.models import Invoice, Payment
from schemas import ComsaPaymentNotification, PaymentCallBackData
from .client import create_comsa_payment
from .funcs import get_comsa_payment_data, verify_payment_notification
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.comsa")


class ComsaProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: ComsaPaymentNotification):
        result = {}
        if not data:
            result = {
                "ERROR": "1",
                "ERROR_NOTE": "webhook data is None",
                "VENDOR_TRANS_ID": ""
            }
            raise PaymentInvalidCallBackDataError(f"{data=}")

        if not data.VENDOR_TRANS_ID:
            result = {
                "ERROR": "1",
                "ERROR_NOTE": "not exist VENDOR_TRANS_ID in webhook data",
                "VENDOR_TRANS_ID": ""
            }
            raise PaymentInvalidReferenceError(f"{data.VENDOR_TRANS_ID=}")

        result["VENDOR_TRANS_ID"] = data.VENDOR_TRANS_ID

        return data.VENDOR_TRANS_ID

    @classmethod
    def return_status(cls, status: str, payment_uuid: str | None = None) -> dict:
        result = {
            "ERROR": "1",
            "ERROR_NOTE": "",
            "VENDOR_TRANS_ID": payment_uuid if payment_uuid else "",
        }
        # result["ERROR_NOTE"] = "already paid"
        if status == "success":
            result.update(
                {
                    "ERROR": "0",
                    "ERROR_NOTE": "Success",
                }
            )
        return result

    async def create_payment(
            self,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            lang: str,
            success_url: str,
            server_url: str,
    ) -> dict:
        result = {"data": {}}
        debugger.debug("create_payment ->")

        is_sandbox, secret_key, vendor_id = self.get_comsa_credentials(credentials)

        comsa_payment = await create_comsa_payment(
            invoice,
            payment,
            amount_to_pay,
            lang,
            success_url,
            vendor_id,
            secret_key,
            is_sandbox,
        )

        if not comsa_payment:
            raise PaymentInvalidResponseError()

        await payment.save_pmt_data({"comsa": comsa_payment.dict()})

        if comsa_payment.error:
            raise PaymentInvalidResponseError(comsa_payment.error)

        if not comsa_payment.url:
            raise PaymentInvalidUrlError()

        result["data"]["url"] = comsa_payment.url

        return result

    def get_comsa_credentials(self, credentials):
        secret_key = credentials.get("secret_key")
        vendor_id = credentials.get("vendor_id")
        is_sandbox = credentials.get("is_sandbox")
        if not any([secret_key, vendor_id, is_sandbox]):
            raise PaymentNotFoundCredentialsError(f"{credentials=}")
        return is_sandbox, secret_key, vendor_id

    async def get_payment_result(
            self,
            data: ComsaPaymentNotification,
            credentials: dict,
            payment_uuid: str,
    ) -> PaymentCallBackData:
        debugger.debug('Comsa -> Webhook event received')

        secret_key, is_sandbox = get_comsa_payment_data(credentials)

        verify_payment_notification(data, secret_key)

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status='success' if data.STATUS == 2 else 'failed',
            external_id=data.AGR_TRANS_ID,
        )
