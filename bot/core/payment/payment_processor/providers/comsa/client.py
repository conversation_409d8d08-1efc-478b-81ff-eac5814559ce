import hashlib
import logging
from datetime import datetime
from typing import Any, Dict, Literal

from aiohttp import ClientSession

from config import COMSA_API
from core.payment.exceptions import PaymentResponseInvalidStatusError
from db.models import Invoice, Payment
from schemas import ComsaResultCreateWidget

debugger = logging.getLogger("debugger.payments.comsa")


def calculate_comsa_checksum(*args: str) -> str:
    data_to_hash = ''.join(args)
    md5sum = hashlib.md5(data_to_hash.encode()).hexdigest()
    return md5sum


async def create_comsa_payment(
        invoice: Invoice,
        payment: Payment,
        amount_to_pay: int,
        lang: str,
        success_url: str,
        vendor_id: str,
        secret_key: str,
        is_sandbox: bool | None = True,
) -> ComsaResultCreateWidget:
    sign_time = int(datetime.now().timestamp() * 1000)
    data = {
        "VENDOR_ID": vendor_id,
        "MERCHANT_TRANS_ID": payment.uuid_id,
        "MERCHANT_TRANS_AMOUNT": amount_to_pay,
        "MERCHANT_CURRENCY": "UZS",
        "MERCHANT_TRANS_NOTE": invoice.title,
        "MERCHANT_TRANS_RETURN_URL": success_url,
        "MERCHANT_TRANS_ERROR_RETURN_URL": success_url,
        "MERCHANT_LANG": lang,
        "MERCHANT_TRANS_DATA": "",
        "SIGN_TIME": sign_time,
    }

    sign_str = calculate_comsa_checksum(
        secret_key,
        str(data["VENDOR_ID"]),
        str(data["MERCHANT_TRANS_ID"]),
        str(data["MERCHANT_TRANS_AMOUNT"]),
        data["MERCHANT_CURRENCY"],
        str(data["SIGN_TIME"])
    )
    data["SIGN_STRING"] = sign_str

    url = COMSA_API.replace(
        "baas.comsa", "test-baas.test.comsa"
    ) if is_sandbox else COMSA_API

    result = await comsa_api(
        f'{url}/payment/payform', type_action="POST", data=data
    )

    return ComsaResultCreateWidget(**result)


async def comsa_api(
        url: str,
        type_action: Literal["GET", "POST", "PUT"],
        data: Dict[str, Any] | None = None,
        params: Dict[str, Any] | None = None,
) -> Dict[str, Any]:
    headers = {'Content-Type': 'application/x-www-form-urlencoded'}
    debugger.debug(f"{type_action} {url}\n{data=}\n{params=}")

    async with ClientSession() as session:
        async with session.request(
                method=type_action, url=url, data=data, params=params, headers=headers
        ) as response:
            debugger.debug(f'{await response.text()=}')
            if response.status > 201:
                raise PaymentResponseInvalidStatusError(response.status)
            return await response.json()


async def test_comsa_access(
        vendor_id: str, secret_key: str, is_sandbox: bool
) -> bool:
    return True
