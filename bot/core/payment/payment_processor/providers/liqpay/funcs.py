import logging

from fastapi import (
    Form,
)

from core.payment.exceptions import (
    PaymentCheckSignatureError,
    PaymentNotFoundCredentialsError,
)
from db import crud
from .liqpay3 import LiqPay

debugger = logging.getLogger('debugger.payments.liqpay')


def check_callback_data(public_key: str, private_key: str, form_data: Form) -> dict:
    liqpay = LiqPay(public_key, private_key)
    sign = liqpay.str_to_sign(private_key + form_data.data + private_key)
    if sign != form_data.signature:
        raise PaymentCheckSignatureError(f'{sign=} != {form_data.signature=}')
    callback_data = liqpay.decode_data_from_str(form_data.data)

    if not any(
            [
                callback_data,
                callback_data.get('order_id'),
                callback_data.get('amount'),
                callback_data.get('currency'),
                callback_data.get('status'),
            ]
    ):
        raise PaymentCheckSignatureError(f"{callback_data=}")

    return callback_data


def _refund(p_key, s_key, order_id, amount = '0.00'):
    liqpay = LiqPay(p_key, s_key)
    response = liqpay.api(
        'request',
        {
            'action': 'refund',
            'amount': amount,
            'order_id': order_id,
            'version': '3',
        },
    )
    return response


async def get_liqpay_client(credentials: dict) -> tuple[LiqPay, bool]:

    public_key, private_key, is_need_fiscal, is_sandbox = get_credentials_data(
        credentials
    )

    liqpay = LiqPay(public_key, private_key)

    return liqpay, is_need_fiscal


async def add_fiscal_data(order_id: int, brand_id: int, store_id: int | None, ) -> dict:

    if not order_id:
        return {}

    rro_info = dict(items=[], delivery_emails=[])

    items = await crud.get_order_fiscal_items(brand_id, order_id)
    if not items:
        return {}

    rro_info['items'] = items

    if admin_emails := await crud.get_admin_emails(brand_id, store_id):
        rro_info['delivery_emails'] = admin_emails
    debugger.debug(f"{rro_info=}")
    return rro_info


def get_credentials_data(credentials: dict) -> tuple[str, str, bool, bool]:

    is_sandbox = True
    public_key = credentials.get('public_key')
    private_key = credentials.get('private_key')
    is_need_fiscal = credentials.get('is_need_fiscal', False)

    if not public_key or not private_key:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    if not private_key.startswith("sandbox"):
        is_sandbox = False

    return public_key, private_key, is_need_fiscal, is_sandbox
