import logging

from core.payment.exceptions import (
    PaymentFailedError, PaymentInvalidCallBackDataError,
    PaymentInvalidReferenceError, PaymentInvalidStatusError, PaymentInvalidUrlError,
)
from db.models import Invoice, Payment
from schemas import (
    PaymentCallBackData, TjCreateSessionResponse, TjPaymentResult,
)
from .client import create_tj_session, get_tj_pmt_data, get_tj_transaction
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.tj")


class TjProvider(PaymentProvider):
    """
    https://tj-dev.transactionjunction.com/docs/hosted-payment-page/2fz0815oclkz8
    -online-payments-with-transaction-junction
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: TjPaymentResult):
        if not data:
            raise PaymentInvalidCallBackDataError(f"{data=}")
        if not data.metadata:
            raise PaymentInvalidReferenceError(f"{data.metadata=}")

        return data.metadata.payment_uuid_id

    @classmethod
    def return_status(cls, status: str):
        return {"status": status}

    @classmethod
    async def create_payment(
            cls,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            success_url: str,
            user_email: str | None,
            user_name: str | None,
            user_phone: str | None,
    ) -> dict:
        result = {"data": {}}

        _, _, _ = get_tj_pmt_data(credentials)

        session: TjCreateSessionResponse = await (
            create_tj_session(
                payment.uuid_id,
                payment.id,
                amount_to_pay,
                success_url,
                credentials,
                payment.currency,
            ))

        if not session.redirectUrl:
            raise PaymentInvalidUrlError()

        result["data"]["url"] = session.redirectUrl

        debugger.debug(f"TJ: {session=}")
        await payment.save_pmt_data({"tj": session.dict()})

        return result

    @classmethod
    async def get_payment_result(
            cls,
            data: TjPaymentResult,
            credentials: dict,
    ) -> PaymentCallBackData:
        debugger.debug("get_payment_result ->")
        status = "failed"

        tj_transaction = await get_tj_transaction(data.transactionId, credentials)

        if not tj_transaction:
            raise PaymentInvalidReferenceError(f"{data.transactionId=}")

        if tj_transaction.transactionStatus == data.transactionStatus:

            if data.transactionStatus == "PAYMENT_AUTHORISED":
                status = "authorized"
            elif data.transactionStatus == "PAYMENT_SETTLED":
                status = "success"
            elif data.transactionStatus in ["PAYMENT_FAILED", "3DS_FAILED"]:
                detail = data.paymentMethodDetails.responseText if (getattr(
                    data, "paymentMethodDetails"
                ) and getattr(data.paymentMethodDetails, "responseText")
                                                                    ) else (
                                                                    data.transactionStatus)
                raise PaymentFailedError(detail)
            else:
                raise PaymentInvalidStatusError(data.transactionStatus)
        else:
            raise PaymentInvalidCallBackDataError(
                f"{tj_transaction.transactionStatus=}!= {data.transactionStatus=}"
            )

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=credentials.get("is_sandbox", True),
            status=status,
            external_id=data.transactionId,
            card_mask=f"{data.tokenInfo.maskedPAN if data.tokenInfo else None}",
        )
