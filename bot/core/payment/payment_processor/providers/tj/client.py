from typing import Literal

from aiohttp import ClientResponse, ClientSession

from config import TJ_API, TJ_API_TEST, TJ_API_TOKEN, TJ_API_TOKEN_TEST
from core.payment.exceptions import (
    PaymentInvalidPayloadError, PaymentNotFoundCredentialsError,
    PaymentResponseInvalidStatusError,
)
from loggers import JSONLogger
from schemas import TjCreateSessionResponse, TjTransactionResult


def get_tj_pmt_data(credentials: dict) -> tuple[str, str, bool]:
    merchant_id = credentials.get("merchant_id")
    profile_id = credentials.get("profile_id")
    is_sandbox = credentials.get("is_sandbox", True)
    if not any([merchant_id, profile_id]):
        raise PaymentNotFoundCredentialsError(f"{credentials=}")
    return merchant_id, profile_id, is_sandbox


async def test_access(
) -> bool:
    ...
    return True


async def get_tj_token(
        client_id: str, client_secret: str, is_sandbox: bool | None = True
) -> str:
    api_url = f"{TJ_API_TOKEN_TEST if is_sandbox else TJ_API_TOKEN}"
    result = await tj_api(
        url=api_url, type_action="POST",
        data={
            "grant_type": "client_credentials", "client_id": client_id,
            "client_secret": client_secret
        }
    )
    return result["access_token"]


async def get_tj_transaction(
        tj_transaction_id: str, credentials: dict, ) -> TjTransactionResult:
    token = await get_tj_token(
        credentials["client_id"], credentials["client_secret"], credentials.get(
            "is_sandbox",
            True
        )
    )
    headers = {"Authorization": f"Bearer {token}"}
    api_url = (f""
    f"{TJ_API_TEST if credentials.get('is_sandbox', True) else TJ_API}/transactions/{tj_transaction_id}")
    result = await tj_api(url=api_url, type_action="GET", headers=headers)
    return TjTransactionResult(**result)


async def create_tj_session(
        payment_uuid_id: str,
        payment_id: int,
        amount_to_pay: int,
        success_url: str,
        credentials: dict,
        currency: str,
) -> TjCreateSessionResponse:

    is_sandbox = credentials.get("is_sandbox", True)

    token = await get_tj_token(
        credentials["client_id"], credentials["client_secret"], is_sandbox
    )
    headers = {"Authorization": f"Bearer {token}"}

    api_url = f"{TJ_API_TEST if is_sandbox else TJ_API}/sessions"

    json_data = {
        "redirectSuccessUrl": success_url,
        "redirectFailedUrl": success_url,
        "redirectCancelUrl": success_url,
        "paymentMethod": "ALL",
        "metaData": {
            "payment_uuid_id": payment_uuid_id
        },
        "transaction": {
            "paymentMethods": ["ALL"],
            "amount": float(f"{amount_to_pay / 100:.2f}"),
            "currency": currency,
            "merchantId": credentials["merchant_id"],
            "profileId": credentials["profile_id"],
            "merchantRef": str(payment_id)
        }
    }
    result = await tj_api(
        url=api_url, type_action="POST", json=json_data, headers=headers
    )
    return TjCreateSessionResponse(**result)


async def get_tj_response(response: ClientResponse) -> dict:
    logger = JSONLogger(
        "payments.tj",
        "get_tj_response",
        {
            "response.status": response.status,
            "response.text": await response.text(),
        }
    )
    logger.debug()

    result = await response.json()

    if response.status > 201:
        if "detail" in result:
            raise PaymentInvalidPayloadError(result["detail"])
        raise PaymentResponseInvalidStatusError(response.status)

    return result


async def tj_api(
        url: str,
        type_action: Literal["GET", "POST", "PUT"],
        data: dict | None = None,
        json: dict | None = None,
        params: dict | None = None,
        headers: dict | None = None,
) -> dict:

    logger = JSONLogger(
        "payments.tj",
        "tj_api",
        {
            "type_action": type_action,
            "url": url,
            "headers": headers,
            "json": json,
            "data": data,
            "params": params,
        }
    )

    logger.debug()

    async with ClientSession() as session:
        async with session.request(
                method=type_action, url=url, json=json, data=data, headers=headers,
                params=params
        ) as response:
            result = await get_tj_response(response)
    return result
