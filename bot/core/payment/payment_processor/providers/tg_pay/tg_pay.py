import logging

from core.invoice.deep_links import InvoiceDeepLink, StoreOrderDeepLink
from core.payment.payment_processor.providers.base import PaymentProvider
from db.models import ClientBot, Group, Invoice, StoreOrder

logger = logging.getLogger("debugger.payments.tg_pay")


class TgPayProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    async def create_payment(
            self,
            invoice: Invoice,
            order: StoreOrder | None = None,
            bot: ClientBot | None = None,
            payment_settings_id: int | None = None,
            object_payment_settings_id: int | None = None,
            **kwargs,
    ) -> dict:
        if not bot:
            from core.invoice.exception import InvoiceProfileDoesNotHaveABotError
            group = await Group.get(invoice.group_id)
            raise InvoiceProfileDoesNotHaveABotError(group.name if group else "unknown")

        if order:
            deep_link = StoreOrderDeepLink(
                order_id=order.id,
                payment_settings_id=payment_settings_id,
                object_payment_settings_id=object_payment_settings_id
            )
            url = deep_link.to_str("telegram", bot.username)
        else:
            deep_link = InvoiceDeepLink(
                invoice_id=str(invoice.id),
                payment_settings_id=payment_settings_id,
                object_payment_settings_id=object_payment_settings_id
            )
            url = deep_link.to_str("telegram", bot.username)

        return {"data": {"url": url}}

    def return_status(self, status: str):
        return {"status": status}

    async def get_payment_uuid(self, data: dict):
        return data.get("payment_id")

    async def get_payment_result(self):
        return False
