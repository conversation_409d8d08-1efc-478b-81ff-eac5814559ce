import logging

import aiohttp

import schemas
from core.payment.exceptions import PaymentNotFoundCredentialsError, \
    PaymentResponseInvalidStatusError
from db.models import Invoice, Payment
from utils.email_funcs import is_valid_email


def form_data_to_dict(form_data: aiohttp.FormData) -> dict:
    return {field[0]['name']: field[2] for field in form_data._fields}


class EpayClient:

    def __init__(self, terminal_id: str, client_id: str, client_secret: str):
        self.terminal_id: str = terminal_id
        self.client_id: str = client_id
        self.client_secret: str = client_secret

        if not any([self.terminal_id, self.client_id, self.client_secret]):
            raise PaymentNotFoundCredentialsError(
                f"{terminal_id=} {client_id=} {client_secret=}"
            )

        self.prod_url_token: str = "https://epay-oauth.homebank.kz/oauth2/token"
        self.test_url_token: str = "https://testoauth.homebank.kz/epay2/oauth2/token"
        self.token_url = self.test_url_token if self.client_id == "test" else (
        self.prod_url_token)
        self.is_sandbox: bool = True if self.client_id == "test" else False

        self.debugger = logging.getLogger("debugger.payments.epay")

    def create_invoice_id(self, invoice_id: int) -> str:
        # return str(random.randint(***************, ***************))
        fill_char = '0'
        if self.is_sandbox:
            return "********" + str(invoice_id).rjust(7, fill_char)
        return str(invoice_id).rjust(7, fill_char)

    def create_payment_id(self, payment_id: int) -> str:
        fill_char = '0'
        if self.is_sandbox:
            return "********" + str(payment_id).rjust(7, fill_char)
        return str(payment_id).rjust(7, fill_char)

    def get_token_data(
            self,
            amount: int,
            currency: str,
            invoice_id: str,
            postLink: str = "",
            failurePostLink: str = "",
    ) -> aiohttp.FormData:
        data = aiohttp.FormData()
        data.add_field("grant_type", "client_credentials")
        data.add_field(
            "scope",
            "webapi usermanagement email_send verification statement statistics payment"
        )
        data.add_field("terminal", self.terminal_id)
        data.add_field("client_id", self.client_id)
        data.add_field("client_secret", self.client_secret)
        data.add_field("amount", amount)
        data.add_field("currency", currency)
        data.add_field("invoiceID", invoice_id)
        data.add_field("postLink", postLink)
        data.add_field("failurePostLink", failurePostLink)
        return data

    def get_token_response(self, data: dict) -> schemas.EpayTokenResponse:
        return schemas.EpayTokenResponse(**data)

    async def get_token(
            self, invoice: Invoice, payment: Payment
    ) -> schemas.EpayDataResponse:
        amount = round(payment.amount / 100)
        invoice_id = self.create_invoice_id(invoice.id)
        payment_id = self.create_payment_id(payment.id)
        data = self.get_token_data(amount, payment.currency, invoice_id)

        self.debugger.debug(f"POST {self.token_url}, data={form_data_to_dict(data)}")
        async with aiohttp.ClientSession() as session:
            async with session.post(self.token_url, data=data) as response:
                self.debugger.debug(f"{response.status=} {await response.text()=}")

                if response.status > 201:
                    raise PaymentResponseInvalidStatusError(response.status)
                resp_data = await response.json()
                token = self.get_token_response(resp_data)

        return schemas.EpayDataResponse(
            auth=token,
            invoiceId=invoice_id,
            amount=amount,
            currency=payment.currency,
            invoiceIdAlt=payment_id,
        )

    def get_epay_payment_data(
            self,
            data: schemas.EpayDataResponse,
            language: str,
            description: str,
            success_url: str,
            server_url: str,
            name: str | None = None,
            user_email: str | None = None,
            phone: str | None = None,
    ) -> schemas.EpayPaymentData:
        match language:
            case "kz":
                lang = "kaz"
            case "en":
                lang = "eng"
            case _:
                lang = "rus"

        payment_data = schemas.EpayPaymentData(
            terminal=self.terminal_id,
            amount=data.amount,
            currency=data.currency,
            invoiceId=data.invoiceId,
            invoiceIdAlt=data.invoiceIdAlt,
            language=lang,
            description=description,
            backLink=success_url,
            postLink=server_url,
            name=name,
            phone=phone,
            auth=data.auth,
        )

        if user_email and is_valid_email(user_email):
            payment_data.email = user_email

        return payment_data
