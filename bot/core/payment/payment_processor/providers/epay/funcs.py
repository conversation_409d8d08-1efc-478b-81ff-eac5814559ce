from core.payment.exceptions import PaymentNotFoundCredentialsError


def get_epay_payment_data(credentials: dict) -> tuple[str, str, str, bool]:

    is_sandbox = True
    terminal_id = credentials.get('terminal_id')
    client_id = credentials.get('client_id')
    client_secret = credentials.get('client_secret')

    if not terminal_id or not client_id or not client_secret:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    if client_id != "test":
        is_sandbox = False

    return terminal_id, client_id, client_secret, is_sandbox
