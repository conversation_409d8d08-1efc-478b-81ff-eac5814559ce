import logging

from starlette.responses import PlainTextResponse

from core.payment.exceptions import (
    PaymentCheckUuidsError, PaymentInvalidCallBackDataError,
    PaymentInvalidReferenceError,
)
from db.models import Invoice, Payment
from schemas import EpayWebhook, PaymentCallBackData
from .api import EpayClient
from .funcs import get_epay_payment_data
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.epay")


class EpayProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: EpayWebhook, is_sand_box: bool = True):
        if not data:
            raise PaymentInvalidCallBackDataError(f"{data=}")

        if not data.data.invoiceIdAlt:
            raise PaymentInvalidReferenceError(f"{data.data.invoiceIdAlt=}")

        if is_sand_box and data.data.invoiceIdAlt.startswith("73942516"):
            payment_id = int(data.data.invoiceIdAlt.replace("73942516", ""))
        else:
            payment_id = int(data.data.invoiceId)

        payment = await Payment.get(payment_id)

        return payment.uuid_id

    @classmethod
    def return_status(cls, status: str):
        return PlainTextResponse(content="TRUE", status_code=200)

    async def create_payment(
            self,
            invoice: Invoice,
            payment: Payment,
            credentials: dict,
            lang: str,
            success_url: str,
            server_url: str,
            user_name: str | None = None,
            user_email: str | None = None,
            user_phone: str | None = None,
    ) -> dict:
        result = {"data": {}}
        debugger.debug("create_payment ->")
        terminal_id, client_id, client_secret, is_sandbox = get_epay_payment_data(
            credentials
        )

        client = EpayClient(terminal_id, client_id, client_secret)
        data = await client.get_token(invoice, payment)
        payment_data = client.get_epay_payment_data(
            data, lang,
            invoice.title,
            success_url, server_url,
            user_name, user_email, user_phone,
        )

        result["data"] = payment_data.dict()
        await payment.save_pmt_data({"epay": {"pmt_create": payment_data.dict()}})

        return result

    async def get_payment_result(
            self,
            data: EpayWebhook,
            credentials: dict,
            payment_uuid: str,
    ) -> PaymentCallBackData:

        _, _, _, is_sandbox = get_epay_payment_data(credentials)

        payment_uuid_from_data = await self.get_payment_uuid(data, is_sandbox)

        if payment_uuid != payment_uuid_from_data:
            raise PaymentCheckUuidsError(f"{payment_uuid=}, {payment_uuid_from_data=}")

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status="success" if data.data.code == "ok" else "failed",
            external_id=data.data.id,
        )
