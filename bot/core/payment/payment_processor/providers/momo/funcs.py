from core.payment.exceptions import PaymentNotFoundCredentialsError


def get_momo_payment_data(credentials: dict) -> tuple[str, str, str, bool]:

    subscription_key = credentials.get("subscription_key")
    user_id = credentials.get("user_id")
    user_api_key = credentials.get("user_api_key")
    is_sandbox = credentials.get("is_sandbox", True)

    if not any([subscription_key, user_id, user_api_key]):
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    return subscription_key, user_id, user_api_key, is_sandbox
