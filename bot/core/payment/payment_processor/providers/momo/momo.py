import asyncio
import logging

from starlette.responses import PlainTextResponse, Response

from core.payment.exceptions import (
    PaymentCheckUuidsError, PaymentInvalidCallBackDataError,
    PaymentInvalidReferenceError, PaymentInvalidResponseError,
    PaymentResponseCommonError,
)
from db.models import Invoice, Payment
from schemas import (
    MomoTransaction,
    MomoTransactionStatus,
    MomoWebHookData, PaymentCallBackData,
)
from .client import create_momo_transfer, get_momo_transfer, request_to_pay
from .funcs import get_momo_payment_data
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.momo")


class MomoProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: MomoWebHookData):
        if not data:
            raise PaymentInvalidCallBackDataError(f"{data=}")
        if not data.externalId:
            raise PaymentInvalidReferenceError(f"{data.externalId=}")

        return data.externalId

    @classmethod
    def return_status(cls, status: str) -> Response:
        return PlainTextResponse(content="TRUE", status_code=200)

    async def create_payment(
            self,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            lang: str,
            success_url: str,
            server_url: str,
            user_email: str | None,
            user_phone: str | None,
    ) -> dict:
        result = {"data": {}}
        debugger.debug("Momo ->")

        subscription_key, user_id, user_api_key, is_sandbox = get_momo_payment_data(
            credentials
        )

        momo_payment = await request_to_pay(
            invoice,
            payment,
            amount_to_pay,
            lang,
            success_url,
            server_url,
            subscription_key,
            user_id,
            user_api_key,
            user_email,
            user_phone,
            is_sandbox,
        )

        if momo_payment:
            result["status"] = "success"
            result["data"]["url"] = success_url
        else:
            raise PaymentInvalidResponseError("")

        await payment.save_pmt_data({"momo": f"{momo_payment=}"})

        return result

    async def get_payment_result(
            self,
            data: MomoWebHookData,
            credentials: dict,
            payment_uuid: str,
    ):

        payment_uuid_from_data = await self.get_payment_uuid(data)

        if payment_uuid_from_data != payment_uuid:
            raise PaymentCheckUuidsError(f"{payment_uuid_from_data} != {payment_uuid}")

        _, _, _, is_sandbox = get_momo_payment_data(credentials)

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status="success" if data.status == "SUCCESSFUL" else "failed",
            external_id=data.financialTransactionId,
        )

    async def make_business_payment(
            self,
            payment_uuid_id: str,
            amount_to_pay: int,
            currency: str,
            credentials: dict,
            merchant_data: dict,
            title: str,
    ) -> dict | None:
        debugger.debug(f"make_business_payment ->")

        await create_momo_transfer(
            payment_uuid_id, amount_to_pay, credentials, currency,
            merchant_data, title
        )

        retries = 3
        delay_seconds = 1

        for attempt in range(retries):
            momo_transfer: MomoTransaction = await get_momo_transfer(
                payment_uuid_id, credentials
            )

            if momo_transfer.status == MomoTransactionStatus.PENDING:
                if attempt < retries - 1:  # Don't sleep on last attempt
                    await asyncio.sleep(delay_seconds)
                    continue

            if momo_transfer.status == MomoTransactionStatus.SUCCESSFUL:
                return momo_transfer.dict()

            if (momo_transfer.status == MomoTransactionStatus.FAILED and
                    momo_transfer.reason.message):
                raise PaymentResponseCommonError(detail=momo_transfer.reason.message)

        raise PaymentResponseCommonError(detail="payment failed")
