import logging
from typing import Literal

from aiohttp import BasicAuth, ClientSession, ContentTypeError

from core.payment.payment_processor.providers.momo.funcs import \
    get_momo_payment_data
from config import MOMO_API, PMT_SERVER_CALLBACK_URL
from core.payment.exceptions import (
    PaymentInvalidPayloadError, PaymentInvalidResponseError,
    PaymentResponseInvalidStatusError,
)
from db.models import Invoice, Payment
from schemas import MomoTransaction

debugger = logging.getLogger("debugger.payments.momo")


async def get_momo_token(
        subscription_key: str,
        user_id: str,
        user_api_key: str,
) -> str | None:
    """
    https://momodeveloper.mtn.com/API-collections#api=collection&operation
    =CreateAccessToken
    """
    auth = BasicAuth(user_id, user_api_key)
    headers = {"Ocp-Apim-Subscription-Key": subscription_key}

    url = f"{MOMO_API}/collection/token/"
    debugger.debug(f"POST {url}\n{headers=}\n{auth=}")
    async with ClientSession() as session:
        async with session.post(url, headers=headers, auth=auth) as response:
            debugger.debug(f"{response.status=} {await response.text()=}")
            if response.status != 200:
                raise PaymentResponseInvalidStatusError(response.status)
            result = await response.json()
            if "access_token" not in result:
                raise PaymentInvalidResponseError("access_token")
            return result["access_token"]


async def request_to_pay(
        invoice: Invoice,
        payment: Payment,
        amount_to_pay: int,
        lang: str,
        success_url: str,
        server_url: str,
        subscription_key: str,
        user_id: str,
        user_api_key: str,
        user_email: str | None = "",
        user_phone: str | None = "",
        is_sandbox: bool = True,
) -> bool:
    """
    https://momodeveloper.mtn.com/API-collections#api=collection&operation=RequesttoPay
    """
    if user_phone:
        partyIdType = "MSISDN"
        partyId = user_phone.replace('+', '').replace(' ', '')
    elif user_email:
        partyIdType = "EMAIL"
        partyId = user_email
    else:
        raise PaymentInvalidPayloadError("E-mail or phone user is required !!!")

    data = {
        "amount": f"{amount_to_pay}",
        "currency": invoice.currency,
        "externalId": payment.uuid_id,
        "payer": {
            "partyIdType": partyIdType,
            "partyId": partyId
        },
        "payerMessage": f"Invoice {invoice.id}",
        "payeeNote": f"Invoice {invoice.id}",
    }

    headers = {"X-Callback-Url": server_url, "X-Reference-Id": payment.uuid_id}

    return await momo_api(
        f"{MOMO_API}/collection/v1_0/requesttopay", type_action="POST",
        subscription_key=subscription_key,
        user_id=user_id, user_api_key=user_api_key, data=data, headers=headers,
        is_sandbox=is_sandbox
    )


async def momo_api(
        url: str,
        type_action: Literal["GET", "POST", "PUT"],
        subscription_key: str,
        user_id: str,
        user_api_key: str,
        data: dict | None = None,
        headers: dict | None = None,
        is_sandbox: bool = True,
) -> dict | bool | None:
    access_token = await get_momo_token(subscription_key, user_id, user_api_key)

    _headers = {
        "Content-Type": "application/json", "Authorization": f"Bearer {access_token}",
        "X-Target-Environment": "sandbox" if is_sandbox else "production",
        "Ocp-Apim-Subscription-Key": subscription_key
    }

    if headers is None:
        headers = _headers
    else:
        headers.update(_headers)

    debugger.debug(f"{type_action.upper()} {url}\n{headers=}\n{data=}")
    async with ClientSession() as session:
        async with session.request(
                method=type_action, url=url, json=data, headers=headers
        ) as response:
            result = None
            debugger.debug(f"Momo: {await response.text()=}")
            try:
                result = await response.json()
            except ContentTypeError:
                ...
            if response.status == 409:
                raise PaymentInvalidPayloadError("Conflict, duplicated reference id")
            if response.status != 202:
                if result and "message" in result:
                    raise PaymentInvalidPayloadError(result['message'])
                raise PaymentResponseInvalidStatusError(response.status)
            return result if result else True


async def get_momo_transfer(payment_uuid_id: str, credentials: dict) -> MomoTransaction:
    """
    https://momodeveloper.mtn.com/api-details#api=disbursement&operation
    =GetTransferStatus
    """

    headers = {"referenceId": payment_uuid_id}
    subscription_key, user_id, user_api_key, is_sandbox = get_momo_payment_data(
        credentials, True
    )

    result = await momo_api(
        f"{MOMO_API}/disbursement/v1_0/transfer/{payment_uuid_id}", type_action="GET",
        subscription_key=subscription_key, user_id=user_id, user_api_key=user_api_key,
        headers=headers,
        is_sandbox=is_sandbox
    )

    return MomoTransaction(**result)


async def create_momo_transfer(
        payment_uuid_id: str, amount_to_pay: int, credentials: dict, currency: str,
        merchant_data: dict,
        title: str
):
    """
    https://momodeveloper.mtn.com/api-details#api=disbursement&operation=Transfer
    """

    subscription_key, user_id, user_api_key, is_sandbox = get_momo_payment_data(
        credentials
    )

    partyIdType = "MSISDN"
    partyId = merchant_data.get("user_phone").replace('+', '').replace(' ', '')

    data = {
        "amount": f"{amount_to_pay}",
        "currency": currency,
        "externalId": payment_uuid_id,
        "payee": {
            "partyIdType": partyIdType,
            "partyId": partyId
        },
        "payerMessage": title,
        "payeeNote": title,
    }
    server_url = f"{PMT_SERVER_CALLBACK_URL}/momo/disbursement/{payment_uuid_id}"
    headers = {"X-Callback-Url": server_url, "X-Reference-Id": payment_uuid_id}

    return await momo_api(
        f"{MOMO_API}/disbursement/v1_0/transfer", type_action="POST",
        subscription_key=subscription_key,
        user_id=user_id, user_api_key=user_api_key, data=data, headers=headers,
        is_sandbox=is_sandbox
    )
