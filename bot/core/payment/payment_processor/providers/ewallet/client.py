import uuid
from fastapi import HTT<PERSON>Exception

from core.ewallet.payment.deep_links import EwalletPaymentDeepLink
from core.payment.exceptions import (
    PaymentNotFoundCredentialsError
)
from core.shortener import create_short_link
from db import crud
from db.models import ClientBot, EWallet, EWalletPayment


def get_ewallet_pmt_data(credentials: dict) -> int:
    ewallet_id = credentials.get("ewallet_id")
    if not ewallet_id:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")
    return ewallet_id


async def get_ewallet_by_payment_data(credentials: dict):
    ewallet = await EWallet.get(get_ewallet_pmt_data(credentials))
    if not ewallet:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")
    return ewallet


async def test_access(
) -> bool:
    ...
    return True


async def get_ewallet_payment(
        ewallet_payment_id: str, credentials: dict, ) -> EWalletPayment:
    return await EWalletPayment.get(ewallet_payment_id)


async def create_ewallet_payment(
        profile_id: int,
        external_id: str,
        amount_to_pay: int,
        success_url: str,
        credentials: dict,
        currency: str,
        creator_id: int,
        payment_settings_id: int,
        description: str | None = None,
        ewallet: EWallet | None = None,
) -> EWalletPayment:
    if not ewallet:
        ewallet = await get_ewallet_by_payment_data(credentials)

    bot = await ClientBot.get(ewallet.bot_id)
    if not bot:
        raise HTTPException(status_code=404, detail="Payment bot not found")

    uuid_id = str(uuid.uuid4().hex)

    redirect_url = EwalletPaymentDeepLink(
        ewallet_payment_id=uuid_id.replace("-", "_"),
    ).to_str(bot.bot_type, bot.id_name)

    short_link = await create_short_link("url", redirect_url)

    ewallet_payment = await crud.create_ewallet_payment(
        uuid_id=uuid_id,
        profile_id=profile_id,
        external_id=external_id,
        amount=amount_to_pay,
        currency=currency,
        ewallet_id=ewallet.id,
        bot_id=bot.id,
        creator_id=creator_id,
        description=description,
        success_url=success_url,
        redirect_url=short_link.short_url,
        payment_settings_id=payment_settings_id,
    )

    if not ewallet_payment:
        raise HTTPException(status_code=500, detail="Error create eWallet payment")

    return ewallet_payment
