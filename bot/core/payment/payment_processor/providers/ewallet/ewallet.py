import logging
from fastapi import HTTPException
from sqlalchemy import inspect

from core.payment.exceptions import (
    PaymentInvalidCallBackDataError,
    PaymentInvalidReferenceError, PaymentInvalidUrlError,
    PaymentNotFoundPaymentDataByUuidError,
)
from core.shortener import create_short_link
from db.models import EWallet, EWalletPayment, Invoice, Payment, ShortLink
from schemas import (
    PaymentCallBackData,
    TjPaymentResult,
)
from schemas.payment.payment import EWalletPaymentStatusData
from .client import create_ewallet_payment
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.ewallet")


def object_as_dict(obj):
    return {c.key: getattr(obj, c.key)
            for c in inspect(obj).mapper.column_attrs}


class EwalletProvider(PaymentProvider):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: TjPaymentResult):
        if not data:
            raise PaymentInvalidCallBackDataError(f"{data=}")
        if not data.metadata:
            raise PaymentInvalidReferenceError(f"{data.metadata=}")

        return data.metadata.payment_uuid_id

    @classmethod
    def return_status(cls, status: str):
        return {"status": status}

    @classmethod
    async def create_payment(
            cls,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            success_url: str,
            payment_settings_id: int,
            user_email: str | None,
            user_name: str | None,
            user_phone: str | None,
            ewallet: EWallet | None = None,
    ) -> dict:
        result = {"data": {}}

        short_link: ShortLink = await create_short_link(
            "url", success_url,
        )

        if not short_link:
            debugger.error(
                f"Failed to create short link for success_url: {success_url}. "
                f"payment={payment.uuid_id}, invoice={invoice.id}"
            )
            raise PaymentInvalidUrlError()

        ewallet_payment: EWalletPayment = await (
            create_ewallet_payment(
                invoice.group_id,
                payment.uuid_id,
                amount_to_pay,
                short_link.short_url,
                credentials,
                invoice.currency,
                invoice.user_id,
                payment_settings_id,
                invoice.title,
                ewallet,
            )
        )

        if not ewallet_payment.redirect_url:
            raise PaymentInvalidUrlError()

        result["data"]["url"] = ewallet_payment.redirect_url
        result["data"]["payment_id"] = ewallet_payment.id

        debugger.debug(f"eWallet: {ewallet_payment.as_dict()=}")
        await payment.save_pmt_data({"ewallet": ewallet_payment.as_dict()})

        return result

    @classmethod
    async def get_payment_result(
            cls,
            data: any,
            credentials: dict,
    ) -> PaymentCallBackData:
        raise ValueError("provider not support webhook callback...")

    async def make_business_payment(
            self,
            payment_uuid_id: str,
            amount_to_pay: int,
            currency: str,
            credentials: dict,
            merchant_data: dict,
    ) -> dict | None:
        raise HTTPException(400, "Provider not support this payment method...")

    @classmethod
    async def get_payment_status(
            cls, ewallet_payment_uuid: str
    ) -> EWalletPaymentStatusData:
        ewallet_payment = await EWalletPayment.get(uuid_id=ewallet_payment_uuid)
        if not ewallet_payment:
            raise PaymentNotFoundPaymentDataByUuidError(f"{ewallet_payment_uuid=}")
        return EWalletPaymentStatusData(
            payment_uuid=ewallet_payment_uuid,
            status=ewallet_payment.status,
            payment_settings_id=ewallet_payment.payment_settings_id,
            external_id=ewallet_payment.external_id,
            error=ewallet_payment.error,
            paid_datetime=ewallet_payment.paid_datetime,
        )
