import logging

from starlette.responses import PlainTextResponse

from core.payment.exceptions import (
    PaymentInvalidCallBackDataError,
    PaymentInvalidReferenceError, PaymentInvalidResponseError, PaymentInvalidUrlError,
)
from db.models import Invoice, Payment
from schemas import FreedompayHookResponse, PaymentCallBackData
from .api import FreedompayClient
from .funcs import get_freedompay_payment_data, verify_payment
from ..base import PaymentProvider

debugger = logging.getLogger("debugger.payments.freedompay")


class FreedompayProvider(PaymentProvider):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    async def get_payment_uuid(cls, data: FreedompayHookResponse):
        if not data or not data.pg_result:
            raise PaymentInvalidCallBackDataError(f"{data=}")

        if not data.payment_uuid_id:
            raise PaymentInvalidReferenceError(f"{data.payment_uuid_id=}")

        return data.payment_uuid_id

    @classmethod
    def return_status(cls, status: str):
        return PlainTextResponse(content="TRUE", status_code=200)

    async def create_payment(
            self,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            credentials: dict,
            lang: str,
            success_url: str,
            server_url: str,
            user_email: str | None,
            user_phone: str | None,
    ) -> dict:
        result = {"data": {}, "status": "error"}
        debugger.debug("Freedompay -> create_payment")

        merchant_id, merchant_secret, is_sandbox = get_freedompay_payment_data(
            credentials
        )

        client = FreedompayClient(merchant_id, merchant_secret, is_sandbox)

        freedompay_payment = await client.create_payment(
            invoice,
            payment,
            amount_to_pay,
            lang,
            success_url,
            server_url,
            user_email=user_email,
            user_phone=user_phone,
            is_sandbox=is_sandbox,
        )

        freedompay_checkout_data = await client.checkout_url(freedompay_payment)

        if not freedompay_checkout_data:
            raise PaymentInvalidResponseError("Not found checkout data")

        await payment.save_pmt_data(
            {"freedompay": {"make_payment": freedompay_checkout_data.dict()}}
        )

        if freedompay_checkout_data.error:
            raise PaymentInvalidResponseError("freedompay_checkout_data.error")

        if freedompay_checkout_data.data and freedompay_checkout_data.data.url:
            result["data"]["url"] = freedompay_checkout_data.data.url
            result["status"] = "success"
        else:
            raise PaymentInvalidUrlError()

        return result

    async def get_payment_result(
            self,
            payment_uuid: str,
            data: FreedompayHookResponse,
            credentials: dict,
            amount: int,
            currency: str,
    ) -> PaymentCallBackData:
        debugger.debug(
            f"Freedompay -> Webhook event received: {data=}"
        )
        status = "failed"

        merchant_id, merchant_secret, is_sandbox = get_freedompay_payment_data(
            credentials
        )

        if verify_payment(amount, currency, data):
            status = "success"

        return PaymentCallBackData(
            callback_data=data.dict(),
            is_sandbox=is_sandbox,
            status=status,
            external_id=data.pg_payment_id,
        )
