import hashlib
import logging
import random
import re
import string
import urllib.parse
import xml.etree.ElementTree as eTree

from aiohttp import ClientSession

import schemas
from core.payment.exceptions import (
    PaymentInvalidResponseError, PaymentNotFoundCredentialsError,
    PaymentResponseInvalidStatusError,
)
from db.models import Invoice, Payment
from utils.email_funcs import is_valid_email
from .exceptions import FreedompaySignatureError, process_error

debugger = logging.getLogger("debugger.payments.freedompay")


def generate_salt_str() -> str:
    random.seed()
    length = random.randint(6, 20)
    return "".join(
        [
            random.choice(
                string.ascii_lowercase + string.ascii_uppercase + string.digits
            )
            for _ in range(length)
        ]
    )


def convert_data(in_data: dict) -> dict:
    out_data = {}
    for key, value in in_data.items():
        if isinstance(value, bool):
            out_data[key] = str(int(value))
        elif isinstance(value, int) or isinstance(value, float):
            out_data[key] = str(value)
        else:
            out_data[key] = value

    return out_data


class FreedompayClient:

    def __init__(
            self, merchant_id: int, merchant_secret: str, is_sandbox: bool = False,
            **kwargs
    ):
        self.merchant_id: int = merchant_id
        self.secret_key: str = merchant_secret
        self.is_sandbox: bool = is_sandbox

        if not any([self.merchant_id, self.secret_key]):
            raise PaymentNotFoundCredentialsError(f"{merchant_id} {merchant_secret=}")

        self.api_url: str = "https://api.freedompay.kg"
        self.test_api_url: str = "https://api.freedompay.kg"

        self.debugger = logging.getLogger("debugger.payments.freedompay")

    @classmethod
    def get_response(
            cls, data_bytes: bytes
    ) -> schemas.FreedompayHookResponse:
        data_str = urllib.parse.unquote(data_bytes)
        params_list = data_str.split("&")
        data = {}
        for param in params_list:
            key, value = param.split("=")
            data[key] = value

        try:
            pg_net_amount = float(data.get("pg_net_amount"))
        except ValueError:
            pg_net_amount = 0.00

        result = schemas.FreedompayHookResponse(
            pg_order_id=int(data.get("pg_order_id")),
            pg_payment_id=int(data.get("pg_payment_id")),
            pg_currency=data.get("pg_currency"),
            pg_amount=float(data.get("pg_amount")),
            pg_net_amount=pg_net_amount,
            pg_ps_amount=float(data.get("pg_ps_amount")),
            pg_ps_full_amount=float(data.get("pg_ps_full_amount")),
            pg_ps_currency=data.get("pg_ps_currency"),
            pg_description=data.get("pg_description"),
            pg_result=bool(int(data.get("pg_result"))),
            pg_can_reject=bool(int(data.get("pg_can_reject"))),
            pg_payment_date=data.get("pg_payment_date"),
            pg_need_phone_notification=bool(
                int(data.get("pg_need_phone_notification"))
            ),
            pg_user_contact_email=data.get("pg_user_contact_email"),
            pg_need_email_notification=bool(
                int(data.get("pg_need_email_notification"))
            ),
            pg_testing_mode=bool(int(data.get("pg_testing_mode"))),
            pg_payment_method=data.get("pg_payment_method"),
            pg_reference=data.get("pg_reference"),
            pg_captured=bool(int(data.get("pg_captured"))),
            pg_card_pan=data.get("pg_card_pan"),
            pg_card_exp=data.get("pg_card_exp"),
            pg_card_owner=data.get("pg_card_owner"),
            pg_card_brand=data.get("pg_card_brand"),
            pg_auth_code=data.get("pg_auth_code"),
            payment_uuid_id=data.get("payment_uuid_id"),
            pg_salt=data.get("pg_salt"),
            pg_sig=data.get("pg_sig"),
        )
        if data.get("pg_user_phone_text"):
            result.pg_user_phone_text = data.get("pg_user_phone_text")

        return result

    async def _request(self, url: str, data: dict) -> dict:
        self.debugger.debug("%s URL: %s", self.__class__.__name__, url)
        self.debugger.debug("%s request data: %s", self.__class__.__name__, str(data))
        # headers = {"Content-Type": "multipart/form-data"}

        async with ClientSession() as session:
            response = await session.post(
                url, data=data,
                # headers=headers
            )
            headers = response.request_info.headers
            self.debugger.debug(
                "%s request headers: %s", self.__class__.__name__, str(headers)
            )

            if response.status > 201:
                raise PaymentResponseInvalidStatusError(response.status)

            data_str = await response.text()
            self.debugger.debug(
                "%s response data:\n%s", self.__class__.__name__, data_str
            )
            if not data_str:
                raise PaymentInvalidResponseError("empty response")
            root = eTree.ElementTree(eTree.fromstring(data_str))
            data = {element.tag: element.text for element in root.iter()}
            data.pop("response", None)

            return data

    def create_signature(self, script_name: str, data: dict) -> str:
        data = dict(sorted(data.items()))
        values = [script_name]
        values.extend([str(value) for value in data.values()])
        values.append(self.secret_key)
        values_str = ";".join(values)
        return hashlib.md5(values_str.encode("utf-8")).hexdigest()

    @property
    def payment_url(self) -> str:
        return "init_payment.php"

    async def checkout_url(
            self, data: schemas.FreedompayPaymentData
    ) -> schemas.PaymentCheckOutDataBase:
        base_url = self.test_api_url if self.is_sandbox else self.api_url
        path = "/".join([base_url, self.payment_url])

        data_dict = convert_data(data.dict(exclude_unset=True))

        result = await self._request(path, data=data_dict)
        if result["pg_status"] != "ok":
            raise PaymentInvalidResponseError(process_error(result))

        data_dict = result.copy()
        data_dict.pop("pg_sig", None)
        check_signature = self.create_signature(self.payment_url, data_dict)
        if check_signature != result["pg_sig"]:
            raise FreedompaySignatureError()

        data = schemas.PaymentCheckOutDataBase(
            data=schemas.PaymentCheckOutData(url=result["pg_redirect_url"]),
        )
        return data

    async def create_payment(
            self,
            invoice: Invoice,
            payment: Payment,
            amount_to_pay: int,
            lang: str,
            success_url: str,
            server_url: str,
            user_email: str | None = None,
            user_phone: str | None = None,
            is_sandbox: bool | None = True,
    ) -> schemas.FreedompayPaymentData:
        match lang:
            case "ru" | "uk":
                user_lang = "ru"
            case _:
                user_lang = "en"

        data = schemas.FreedompayPaymentData(
            pg_merchant_id=self.merchant_id,
            pg_order_id=str(invoice.id),
            pg_amount=round(amount_to_pay / 100, 2),
            pg_currency=payment.currency,
            pg_description=invoice.title,
            pg_salt=generate_salt_str(),
            pg_result_url=server_url,
            pg_success_url=success_url,
            pg_failure_url=success_url,
            pg_language=user_lang,
            pg_testing_mode=self.is_sandbox,
            pg_user_id=str(payment.user_id) if payment.user_id else None,
            payment_uuid_id=payment.uuid_id,
            pg_auto_clearing=not is_sandbox,
        )
        if user_phone:
            data.pg_user_phone_text = re.sub(r'\D', '', user_phone)

        if user_email and is_valid_email(user_email):
            data.pg_user_contact_email = user_email

        data_dict = {}
        for key, value in data.dict(exclude_unset=True).items():
            if value == "":
                continue
            if isinstance(value, bool):
                data_dict[key] = int(value)
            else:
                data_dict[key] = value

        data.pg_sig = self.create_signature(self.payment_url, data_dict)
        return data
