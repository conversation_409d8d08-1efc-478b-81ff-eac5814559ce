import schemas
from core.payment.exceptions import PaymentNotFoundCredentialsError


def get_freedompay_payment_data(credentials: dict) -> tuple[int, str, bool]:

    is_sandbox = True
    merchant_secret = credentials.get('merchant_secret')
    merchant_id = credentials.get('merchant_id')

    if not merchant_secret or not merchant_id:
        raise PaymentNotFoundCredentialsError(f"{credentials=}")

    if not credentials.get("is_sandbox"):
        is_sandbox = False

    return merchant_id, merchant_secret, is_sandbox


def verify_payment(
        amount: int, currency: str,
        webhook_data: schemas.FreedompayHookResponse
) -> bool:
    if webhook_data.pg_amount != round(amount / 100, 2):
        return False
    if webhook_data.pg_currency != currency:
        return False

    return True
