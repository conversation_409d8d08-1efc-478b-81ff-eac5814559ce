from psutils.func import check_function_spec

from schemas import PaymentCallBackData
from schemas.payment.payment import EWalletPaymentStatusData
from .providers.base import PaymentProvider
from ..exceptions import PaymentProviderInvalidError


class PaymentProcessor:
    def __init__(self, provider_name: str):
        provider_class = PaymentProvider.get_provider(provider_name)
        if provider_class is None:
            raise PaymentProviderInvalidError(provider_name)
        self.provider = provider_class()

    async def create_payment(
            self,
            **kwargs
    ) -> dict:
        create_payment_func = getattr(self.provider, "create_payment")
        safe_kwargs = check_function_spec(create_payment_func, kwargs)
        return await create_payment_func(**safe_kwargs)

    async def get_payment_result(self, **kwargs) -> PaymentCallBackData:
        get_payment_result_func = getattr(self.provider, "get_payment_result")
        safe_kwargs = check_function_spec(get_payment_result_func, kwargs)
        return await get_payment_result_func(**safe_kwargs)

    async def get_payment_uuid(self, **kwargs) -> str:
        get_payment_uuid_func = getattr(self.provider, "get_payment_uuid")
        safe_kwargs = check_function_spec(get_payment_uuid_func, kwargs)
        return await get_payment_uuid_func(**safe_kwargs)

    def return_status(self, **kwargs) -> any:
        return_status_func = getattr(self.provider, "return_status")
        safe_kwargs = check_function_spec(return_status_func, kwargs)
        return return_status_func(**safe_kwargs)

    async def make_business_payment(self, **kwargs) -> dict:
        if hasattr(self.provider, "make_business_payment"):
            create_payment_func = getattr(self.provider, "make_business_payment")
            if (create_payment_func.__code__.co_code !=
                    PaymentProvider.make_business_payment.__code__.co_code):
                safe_kwargs = check_function_spec(create_payment_func, kwargs)
                return await create_payment_func(**safe_kwargs)
        raise NotImplementedError(
            "This payment provider doesn't support business payments"
        )

    async def get_payment_status(self, **kwargs) -> EWalletPaymentStatusData:
        if not hasattr(self.provider, "get_payment_status"):
            raise NotImplementedError(
                "This payment provider doesn't support business payments"
            )
        get_payment_status_func = getattr(self.provider, "get_payment_status")
        safe_kwargs = check_function_spec(get_payment_status_func, kwargs)
        return await get_payment_status_func(**safe_kwargs)
