import asyncio
import functools
import logging
from typing import Any, Callable

from starlette.responses import PlainTextResponse

from core.invoice.exception import BaseInvoicePaymentError
from core.payment.exceptions import BasePaymentError, PaymentSendMessageError
from core.payment.payment_processor import PaymentProcessor
from db import crud
from db.models import Brand, ClientBot, Group, Store, StoreOrder, User
from loggers import JSONLogger
from service.store.notifications import add_order_service_bot_notifications
from utils.platform_admins import send_message_to_platform_admins


def payment_error_handler():
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> PlainTextResponse | Any:
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            except Exception as err:
                context = getattr(err, 'context', {})

                logger = JSONLogger(
                    "payments.webhook",
                    "webhook",
                    context,
                )

                original_error = err

                payment_uuid = context.get('payment_uuid')
                brand_id = context.get('brand_id')
                store_id = context.get('store_id')
                order_id = context.get('order_id')
                invoice_id = context.get('invoice_id')

                logger.error(err)

                if isinstance(
                        original_error, PaymentSendMessageError
                ) or not isinstance(
                    original_error, (BasePaymentError, BaseInvoicePaymentError)
                ):
                    await send_error_messages(
                        payment_uuid, brand_id, store_id, order_id, invoice_id,
                        f"🔴 WEBHOOK Unexpected ERROR 🔴\n\n{repr(err)}\n"
                        f""
                        f"{getattr(err, 'text_kwargs', None) or ''}"
                        "\n\n❗️ Please take immediate action ❗️"
                    )

                if context.get('payment_method'):
                    processor = PaymentProcessor(context.get('payment_method'))
                    return processor.return_status(
                        status="failed", payment_uuid=payment_uuid
                    )

            return PlainTextResponse(content="TRUE", status_code=200)

        return wrapper

    return decorator


async def send_error_messages(
        payment_uuid: str | None, brand_id: int | None = None,
        store_id: int | None = None, order_id: int | None = None,
        invoice_id: int | None = None,
        message: str | None = None
):
    logger = logging.getLogger("debugger.payment.errors")
    logger.debug("Send_error_messages ->")
    await send_message_to_platform_admins(
        f"{message}\n"
        f"{payment_uuid=}, {brand_id=}, {store_id=}, {order_id=}, {invoice_id=}"
    )

    if brand_id and order_id:
        store_order = await StoreOrder.get(order_id)
        if not store_order:
            logger.error(f"Order {order_id} not found")
            return

        store = await Store.get(store_id)
        brand = await Brand.get(brand_id)
        group = await Group.get(brand.group_id)
        bot = await ClientBot.get(group_id=group.id)
        user = await User.get_by_id(store_order.user_id)

        logger.debug("add_order_service_bot_notifications...")

        last_order_status = await crud.get_last_order_status(store_order.id)

        await add_order_service_bot_notifications(
            store_order,
            last_order_status,
            group, store, user,
            bot.id if bot else None,
            is_payment_error=True,
        )
