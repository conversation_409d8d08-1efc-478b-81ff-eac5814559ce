import asyncio
import json
import logging
from typing import Any, Dict

from fastapi import Depends, HTTPException
from incust_api.api import term
from psutils.exceptions import ErrorWithTextVariable
from starlette import status

import schemas
from config import INCUST_PAY_TOKEN_EXPIRES
from core.api.depends import get_lang
from core.auth.depend import get_active_user_optional
from core.incust.helpers import get_currency_from_store_or_brand
from core.invoice import finalize_payment
from core.invoice.exception import (
    InvoicePaymentInvoicePayedError,
    InvoicePaymentInvoiceStatusError,
)
from core.loyalty.helper import get_loyalty_settings_by_context
from core.loyalty.incust_api import incust
from core.payment.funcs import (
    calc_payer_fee_data, create_or_update_payer_fee, make_client_return_url,
)
from core.payment.incust_pay.exceptions import (
    BusinessPaymentError,
    IncustPayCardNumberIsMissedForIdTypeCardError,
    IncustPayConfigurationNotFoundError,
)
from core.payment.incust_pay.funcs import (
    process_make_incust_check,
)
from core.payment.payment_processor.providers.ewallet.client import \
    get_ewallet_by_payment_data
from core.store.depends import get_current_bot_with_brand, get_current_brand
from core.store.functions.order import make_invoice_for_order
from db import crud
from db.models import (
    Brand, ClientBot, EWallet, Group, Invoice, LoyaltySettings, Payment,
    PaymentSettings, Store,
    StoreOrder,
    StoreProduct, User,
)
from db.models.finances.invoice_template import InvoiceTemplate
from db.models.store.payment_settings import ObjectPaymentSettings
from exceptions import AuthNotAuthorisedError
from loggers import JSONLogger
from schemas import (
    IncustPayMerchantData,
    PaymentCallBackData,
)
from schemas.payment.payment import PayerFeeData
from utils.jwt_token import create_jwt_token
from utils.numbers import calculate_amount_modifiers
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from utils.translator import t

debugger = logging.getLogger("debugger.incust.incust_pay")


def get_rules_special_ids(invoice: Invoice) -> list[str]:
    """
    Отримує ID спеціальних рахунків з implemented_rules з даних лояльності Invoice
    """
    if not invoice or not invoice.loyalty_implemented_rules:
        return []

    if not isinstance(invoice.loyalty_implemented_rules, list):
        return []

    special_account_ids = []

    # Зберігаємо оригінальну логіку: шукаємо правила з
    # action_type='charge-percent-by-item' та charge_type='special-account'
    for rule in invoice.loyalty_implemented_rules:
        if isinstance(rule, dict):
            if (rule.get('action_type') == 'charge-percent-by-item' and
                    rule.get('charge_type') == 'special-account'):
                special_account_id = rule.get('special_account_id')
                if special_account_id:
                    special_account_ids.append(str(special_account_id))

    return special_account_ids


async def get_topped_special_ids(invoice: Invoice) -> list[str]:
    """
    Отримує ID спеціальних рахунків які були поповнені
    Зберігає оригінальну логіку: спочатку правила, потім продукти через order
    """
    if not invoice:
        return []

    special_account_ids = get_rules_special_ids(invoice)

    # Зберігаємо оригінальну логіку: якщо є store_order, отримуємо продукти з нього
    if hasattr(invoice, 'store_order') and invoice.store_order:
        order_products = await crud.get_order_products(invoice.store_order.id)

        for product in order_products:
            if product.incust_account and isinstance(
                    product.incust_account, dict
            ) and "id" in product.incust_account:
                special_account_ids.append(str(product.incust_account.get('id')))

    return special_account_ids


class IncustPayService:
    def __init__(
            self,
            brand: Brand = Depends(get_current_brand),
            user: User = Depends(get_active_user_optional),
            lang: str = Depends(get_lang),
            bot: ClientBot | str | None = Depends(get_current_bot_with_brand),
    ):
        self.brand: Brand = brand
        self.user: User | None = user
        self.lang: str = lang
        self.bot: ClientBot | str | None = bot

    async def get_incust_pay_payment_data(
            self,
            payment: Payment | None = None,
            store_id: int | None = None,
            none_on_empty: bool = False,
            order: StoreOrder | None = None,
            is_topup: bool | None = None,
            product_id: int | None = None,
            payment_settings_id: int | None = None,
            ewallet: EWallet | None = None,
            payment_settings: PaymentSettings | None = None,
            ignore_incust_account_id: str | None = None,
            object_payment_settings_id: int | None = None,
            invoice_template: InvoiceTemplate | None = None,
    ):
        logger = JSONLogger(
            "incust.incust_pay", {
                "payment_id": payment.id if payment else None,
                "store_id": store_id,
                "none_on_empty": none_on_empty,
                "order_id": order.id if order else None,
                "is_topup": is_topup,
                "product_id": product_id,
            },
        )
        if not self.brand:
            if none_on_empty:
                return None
            return schemas.IncustPayPaymentData(
                no_brand=True,
            )

        logger.debug(
            "get_incust_pay_payment_data, retrieving incust pay list", {
                "user_id": self.user.id if self.user else None,
                "brand_id": self.brand.id,
                "store_id": store_id,
            }
        )

        currency = await get_currency_from_store_or_brand(
            store_id=store_id, group_id=self.brand.group_id,
            invoice_template=invoice_template,
        ) if not ewallet else ewallet.currency

        topup_product = None
        if product_id:
            topup_product = await StoreProduct.get(product_id)

        incust_pay_configurations = None

        if not topup_product:
            if not ewallet:
                incust_pay_configurations: (
                        list[schemas.IncustPayConfigurationPaymentObject] | None
                ) = await crud.get_incust_pay_configurations_list(
                    self.brand.id, store_id, with_disabled=False,
                    payment_settings_id=payment_settings_id,
                    object_payment_settings_id=object_payment_settings_id
                )
                logger.debug(f"{len(incust_pay_configurations)=}")
            else:
                if not payment_settings:
                    raise HTTPException(404, "payment_settings not found")

                incust_pay_configurations = [
                    schemas.IncustPayConfigurationPaymentObject(
                        name=payment_settings.name,
                        server_api_url=ewallet.server_api_url,
                        rules_type="by-all-rules",
                        terminal_title=ewallet.name,
                        terminal_api_key=ewallet.terminal_api_key,
                        terminal_server_api_url=ewallet.server_api_url,
                        card_payment_enabled=False,
                        payment_settings_id=payment_settings_id,
                        object_payment_settings_id=object_payment_settings_id,
                        charge_percent=payment_settings.json_data.get("fee_percent"),
                        charge_fixed=payment_settings.json_data.get("fee_value"),
                        merchant_data=payment_settings.json_data.get("merchant_data"),
                    )
                ]

        incust_pay_list: list[schemas.IncustPayData] = []
        incust_pay_errors_list: list[schemas.IncustPayDataError] = []
        processed_data: list[str] = []  # {server_api_url}:{terminal_api_key}

        task_list = []
        if not topup_product:
            for incust_pay_configuration in incust_pay_configurations:
                loyalty_settings = await crud.get_loyalty_settings_for_context(
                    "brand",
                    schemas.LoyaltySettingsData(brand_id=self.brand.id),
                )
                if loyalty_settings and incust_pay_configuration:
                    loyalty_settings.terminal_api_key = incust_pay_configuration.terminal_api_key
                task_list.append(
                    self.fill_incust_pay_list(
                        incust_pay_configuration=incust_pay_configuration,
                        currency=currency,
                        incust_pay_list=incust_pay_list,
                        incust_pay_errors_list=incust_pay_errors_list,
                        processed_data=processed_data,
                        loyalty_settings=loyalty_settings,
                        order=order,
                        is_topup=is_topup,
                        incust_account_id=ewallet.incust_account_id if ewallet else
                        None,
                        ignore_incust_account_id=ignore_incust_account_id,
                    )
                )
        else:
            loyalty_settings = await crud.get_loyalty_settings_for_context(
                "product",
                schemas.LoyaltySettingsData(product_id=product_id, brand_id=self.brand.id),
            )
            task_list.append(
                self.fill_incust_pay_list(
                    incust_pay_configuration=None,
                    currency=currency,
                    incust_pay_list=incust_pay_list,
                    incust_pay_errors_list=incust_pay_errors_list,
                    processed_data=processed_data,
                    loyalty_settings=loyalty_settings,
                    order=order,
                    is_topup=True,
                    topup_enabled_card=topup_product.topup_enabled_card,
                    ignore_incust_account_id=ignore_incust_account_id,
                )
            )

        await asyncio.gather(*task_list)

        incust_pay_count = len(incust_pay_list) + len(incust_pay_errors_list)
        if none_on_empty and not incust_pay_count:
            return None

        if payment:
            payment_token = create_jwt_token(
                data={
                    "sub": payment.uuid_id,
                    "type": "incust-pay",
                    "scopes": ["incust-pay"]
                }, expire=INCUST_PAY_TOKEN_EXPIRES
            )
        else:
            payment_token = None

        logger.debug(
            "get_incust_pay_payment_data, return result", {
                "incust_pay_count": incust_pay_count,
                "payment_token": payment_token,
                "incust_pay_errors_list": incust_pay_errors_list,
                "no_incust_user": not self.user or not self.user.incust_external_id,
            }
        )

        return schemas.IncustPayPaymentData(
            no_incust_user=not self.user or not self.user.incust_external_id,
            payment_token=payment_token,
            incust_pay_list=incust_pay_list,
            incust_pay_errors_list=incust_pay_errors_list,
            incust_pay_count=incust_pay_count,
            ewallet_info_text=await self.get_translated_ewallet_info(
                ewallet
            ) if ewallet else None,
        )

    async def get_translated_ewallet_info(self, ewallet: EWallet):
        if not ewallet or not ewallet.info:
            return None

        if self.lang == ewallet.lang:
            return ewallet.info

        translated = await t(
            ewallet,
            self.lang, ewallet.lang,
            group_id="internal",
            is_auto_translate_allowed=True,
        )

        info_text = translated.get("info") if translated else None
        return info_text or ewallet.info

    async def fill_incust_pay_list(
            self,
            incust_pay_configuration:
            schemas.IncustPayConfigurationPaymentObject | None,
            currency: str,
            incust_pay_list: list[schemas.IncustPayData],
            incust_pay_errors_list: list[schemas.IncustPayDataError],
            processed_data: list[str],
            loyalty_settings: LoyaltySettings | None = None,
            order: StoreOrder | None = None,
            is_topup: bool | None = None,
            topup_enabled_card: bool | None = None,
            incust_account_id: str | None = None,
            ignore_incust_account_id: str | None = None,
    ):
        if not loyalty_settings:
            debugger.debug("No loyalty_settings provided")
            return

        # Берем дані з loyalty_settings
        incust_server_api_url = loyalty_settings.server_url
        terminal_api_key = loyalty_settings.terminal_api_key

        debugger.debug(f"{terminal_api_key=}")

        processed_key = f"{incust_server_api_url}:{terminal_api_key}"
        if processed_key in processed_data:
            return

        processed_data.append(processed_key)

        try:
            debugger.debug(f"checking {incust_pay_configuration}")

            specials = []
            corporate_special_accounts_access = []

            if self.user and self.user.incust_external_id and loyalty_settings:
                async def get_card_info():
                    async with incust.term.CustomerApi(loyalty_settings, lang=self.lang) as api:
                        return await api.cardinfo(
                            self.user.incust_external_id,
                            term.m.IdType("external-id"),
                        )

                async def get_global_exist_specials():
                    async with incust.term.CustomerBenefitsApi(loyalty_settings, lang=self.lang) as api:
                        return await api.customer_benefits_special_accounts_general(
                            self.user.incust_external_id,
                            term.m.IdType("external-id"),
                        )

                card_info, global_exist_specials = await asyncio.gather(
                    get_card_info(),
                    get_global_exist_specials()
                )

                if not card_info.specials:
                    debugger.debug(
                        "No specials in card_info !!! Incust Pay not available"
                    )

                global_exist_specials_ids = (
                    [
                        x.id for x in global_exist_specials
                        if x.id != ignore_incust_account_id
                    ]
                    if not incust_account_id else
                    [incust_account_id]
                )

                debugger.debug(f"{global_exist_specials_ids=}")

                def get_exist_special_ids():
                    ids = []
                    for i in incust_pay_list:
                        for x in i.specials:
                            ids.append(x.id)

                    return ids

                exist_special_ids = get_exist_special_ids()

                debugger.debug(f"{exist_special_ids=}")

                # Отримуємо Invoice для роботи з новими структурованими даними лояльності
                invoice = None
                if order and order.invoice_id:
                    invoice = await Invoice.get(order.invoice_id)

                topped_special_ids = await get_topped_special_ids(
                    invoice
                ) if invoice else []

                debugger.debug(f"{topped_special_ids=}")

                if not is_topup:
                    specials = [
                        special_account
                        for special_account in (card_info.specials or [])
                        if (
                                special_account.special_account.type == "money" and
                                # special_account.special_account.active and
                                special_account.special_account.currency == currency and
                                special_account.id not in exist_special_ids and
                                special_account.id in global_exist_specials_ids and
                                special_account.id not in topped_special_ids
                        )
                    ]
                    if specials:
                        for special in specials:
                            special.special_account.active = True if special.special_account.active is None else special.special_account.active
                            special.special_account.corporate = False if special.special_account.corporate is None else special.special_account.corporate
                            special.special_account.credit_type = "debit" if special.special_account.credit_type is None else special.special_account.credit_type
                else:
                    specials = [
                        special_account
                        for special_account in (global_exist_specials or [])
                        if (
                                special_account.special_account.type == "money" and
                                # special_account.special_account.active and
                                special_account.special_account.currency == currency and
                                special_account.id not in exist_special_ids and
                                special_account.id in global_exist_specials_ids and
                                special_account.id not in topped_special_ids
                        )
                    ]

                    for special in specials:
                        for card_special in card_info.specials or []:
                            if card_special.id == special.id and not special.amount:
                                special.amount = card_special.amount

                    debugger.debug(f"{len(specials)=}")

                def get_exist_corporate_special_ids():
                    ids = []
                    for i in incust_pay_list:
                        for x in i.corporate_special_accounts_access:
                            ids.append(x.id)

                    return ids

                exist_special_ids = get_exist_corporate_special_ids()

                corporate_special_accounts_access = [
                    access
                    for access in (card_info.corporate_special_accounts_access or [])
                    if (
                            access.corporate_customer_special_account.active and
                            access.corporate_customer_special_account.special_account
                            .type == "money" and
                            access.corporate_customer_special_account.special_account
                            .currency == currency and
                            access.corporate_customer_special_account.special_account
                            .id not in exist_special_ids and
                            access.corporate_customer_special_account.special_account
                            .id in global_exist_specials_ids
                    )
                ]

            # if specials or corporate_special_accounts_access or
            # incust_pay_configuration.card_payment_enabled:
            incust_pay_list.append(
                schemas.IncustPayData(
                    incust_pay_configuration=incust_pay_configuration,
                    specials=specials,
                    corporate_special_accounts_access=corporate_special_accounts_access,
                    total_accounts_count=len(specials) + len(
                        corporate_special_accounts_access
                    ),
                    topup_card_enabled=topup_enabled_card,
                )
            )

        except Exception as e:
            incust_pay_errors_list.append(
                self.handle_error(incust_pay_configuration, e)
            )

    @classmethod
    def handle_error(
            cls,
            incust_pay_configuration: schemas.IncustPayConfigurationPaymentObject,
            exception: Exception,
    ) -> schemas.IncustPayDataError:
        # Обробка помилок нових API клієнтів InCust
        error_detail = str(exception)

        # Перевіряємо чи є це HTTP помилка з JSON відповіддю
        if hasattr(exception, 'response') and hasattr(exception.response, 'json'):
            try:
                response_data = exception.response.json()
                if isinstance(response_data, dict):
                    error_detail = response_data.get('message', str(exception))
            except Exception:
                pass

        # Перевіряємо status_code в response або безпосередньо в exception
        status_code = None
        if hasattr(exception, 'response') and hasattr(
                exception.response, 'status_code'
        ):
            status_code = exception.response.status_code
        elif hasattr(exception, 'status_code'):
            status_code = exception.status_code

        if status_code == 401:
            debugger.debug(
                f"terminal unauth (401) for {incust_pay_configuration}: {exception}"
            )
            error_type = schemas.IncustPayDataErrorTypeEnum.TERMINAL_UNAUTH
        elif status_code and 400 <= status_code < 500:
            debugger.debug(
                f"client error ({status_code}) occurred for {incust_pay_configuration}: {exception}"
            )
            logging.error(exception, exc_info=True)
            error_type = schemas.IncustPayDataErrorTypeEnum.INCUST_ERROR
        else:
            debugger.debug(
                f"unknown error occurred for {incust_pay_configuration}: {exception}"
            )
            logging.error(exception, exc_info=True)
            error_type = schemas.IncustPayDataErrorTypeEnum.UNKNOWN_ERROR

        return schemas.IncustPayDataError(
            incust_pay_configuration=schemas.IncustPayConfiguration(
                **incust_pay_configuration.dict() if hasattr(
                    incust_pay_configuration, 'dict'
                ) else incust_pay_configuration.__dict__
            ) if incust_pay_configuration else None,
            error_type=error_type,
            error_detail=error_detail,
        )

    async def get_incust_card_info(
            self,
            card_number: str,
            payment_settings_id: int | None = None,
            product_id: int | None = None,
            store_id: int | None = None,
            object_payment_settings_id: int | None = None,
    ) -> schemas.IncustPayCardInfo:
        incust_pay_configuration = None
        topup_product = None
        if payment_settings_id:
            incust_pay_configuration = await (
                self.get_and_check_incust_pay_configuration(
                    payment_settings_id, store_id, None, None,
                    object_payment_settings_id
                ))
        elif product_id:
            topup_product = await StoreProduct.get(product_id)
            if not topup_product:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Product with id {product_id} not found"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="One of payment_settings_id or product_id is required",
            )

        if store_id:
            store = await Store.get(store_id)
            currency = store.currency
        else:
            group = await Group.get(self.brand.group_id)
            currency = group.currency

        try:
            if incust_pay_configuration:
                loyalty_settings = await crud.get_loyalty_settings_for_context(
                    "brand",
                    schemas.LoyaltySettingsData(brand_id=self.brand.id),
                )
                if loyalty_settings:
                    loyalty_settings.terminal_api_key = incust_pay_configuration.terminal_api_key
            else:
                loyalty_settings = await crud.get_loyalty_settings_for_context(
                    "product",
                    schemas.LoyaltySettingsData(product_id=product_id, brand_id=self.brand.id),
                )

            if not loyalty_settings:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Loyalty settings not found"
                )

            try:
                async with incust.term.CustomerApi(loyalty_settings, lang=self.lang) as api:
                    card_info = await api.cardinfo(
                        card_number,
                        term.m.IdType("card"),
                    )
            except term.ApiException as ex:
                raise HTTPException(status_code=ex.status, detail=ex.reason)
            specials = card_info.specials or []
            if len(specials):
                specials = [
                    special_account
                    for special_account in (card_info.specials or [])
                    if (
                            special_account.special_account.type == "money" and
                            special_account.special_account.active and
                            special_account.special_account.currency == currency
                    )
                ]
            corporate_special_accounts_access = (
                    card_info.corporate_special_accounts_access or [])
            incust_pay_data = schemas.IncustPayData(
                incust_pay_configuration=schemas.IncustPayConfiguration(
                    **incust_pay_configuration.dict()
                ) if incust_pay_configuration else None,
                specials=specials,
                corporate_special_accounts_access=corporate_special_accounts_access,
                total_accounts_count=len(specials) + len(
                    corporate_special_accounts_access
                )
            )
            incust_pay_data_error = None
        except Exception as e:
            incust_pay_data = None
            incust_pay_data_error = self.handle_error(incust_pay_configuration, e)

        return schemas.IncustPayCardInfo(
            card_number=card_number,
            is_error=bool(incust_pay_data_error),
            incust_pay_data=incust_pay_data,
            incust_pay_data_error=incust_pay_data_error,
        )

    @classmethod
    async def get_and_check_incust_pay_configuration(
            cls, payment_settings_id: int, store_id: int | None = None,
            is_ewallet_payment: bool | None = None,
            json_data: dict | None = None,
            object_payment_settings_id: int | None = None,
    ) -> schemas.IncustPayConfigurationPaymentObject:

        if is_ewallet_payment:
            ewallet = await EWallet.get(
                id=json_data.get('ewallet_id'), is_enabled=True, is_deleted=False
            )
            if not ewallet:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"EWallet with id {json_data.get('ewallet_id')} not found",
                )
            return schemas.IncustPayConfigurationPaymentObject(
                name=ewallet.name,
                server_api_url=ewallet.server_api_url,
                rules_type="by-all-rules",
                terminal_title=ewallet.name,
                terminal_api_key=ewallet.terminal_api_key,
                terminal_server_api_url=ewallet.server_api_url,
                card_payment_enabled=False,
                payment_settings_id=payment_settings_id,
                object_payment_settings_id=object_payment_settings_id,
                charge_percent=json_data.get("fee_percent"),
                charge_fixed=json_data.get("fee_value"),
                merchant_data=json_data.get("merchant_data"),
            )

        incust_pay_configurations: list[schemas.IncustPayConfigurationPaymentObject] \
            = await crud.get_incust_pay_configurations_list(
            store_id=store_id, payment_settings_id=payment_settings_id,
            object_payment_settings_id=object_payment_settings_id,
        )
        if not incust_pay_configurations:
            raise IncustPayConfigurationNotFoundError(payment_settings_id)

        return incust_pay_configurations[0]

    async def pay(self, data: schemas.IncustPayPayData):

        store_id = data.store_id
        order_id = data.order_id

        if order_id:
            order = await StoreOrder.get(order_id)
            invoice = await Invoice.get(order.invoice_id)
            if not invoice:
                invoice = await make_invoice_for_order(order, self.lang, self.bot)

            is_loyalty = bool(order.original_incust_loyalty_check)
        else:
            order = None
            invoice = await Invoice.get(data.invoice_id)
            is_loyalty = bool(invoice.incust_check)

        payment = await crud.get_payment_by_invoice_and_payment_settings(
            invoice_id=invoice.id,
            payment_settings_id=data.payment_settings_id,
            object_payment_settings_id=data.object_payment_settings_id,
        )

        if invoice:
            brand = await crud.get_brand_by_group(invoice.group_id)
        else:
            brand = await crud.get_brand_by_store(store_id)

        # отримуємо платіжні налаштування
        json_data = None
        payment_settings = await PaymentSettings.get(
            data.payment_settings_id
        )

        if not payment_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Object payment settings with id {data.payment_settings_id} "
                       f"not found",
            )

        if data.object_payment_settings_id:
            object_payment_settings = await ObjectPaymentSettings.get(
                data.object_payment_settings_id
            )
            if object_payment_settings:
                json_data = object_payment_settings.json_data

        if not json_data:
            json_data = payment_settings.json_data

        incust_pay_configuration = await self.get_and_check_incust_pay_configuration(
            data.payment_settings_id, store_id=store_id,
            is_ewallet_payment=data.is_ewallet_payment,
            json_data=json_data,
            object_payment_settings_id=data.object_payment_settings_id,
        )

        amount_to_pay = order.total_sum_with_extra_fee if order else (
            invoice.total_sum_with_extra_fee)

        amount = order.sum_to_pay if order else invoice.sum_to_pay

        if data.is_ewallet_payment and not is_loyalty:
            ewallet = await get_ewallet_by_payment_data(json_data)
            ewallet_discount_percent = ewallet.discount_percent
            ewallet_discount_amount = calculate_amount_modifiers(
                amount_to_pay, ewallet_discount_percent
            )

            amount_to_pay -= ewallet_discount_amount
            amount -= ewallet_discount_amount
        else:
            ewallet_discount_percent = None
            ewallet_discount_amount = None

        payer_fee_value = json_data.get("payer_fee_value", 0)
        payer_fee_percent = json_data.get("payer_fee_percent", 0)

        payer_fee_data: PayerFeeData = calc_payer_fee_data(
            amount_to_pay, invoice.currency,
            payer_fee_percent or str(
                incust_pay_configuration.charge_percent or 0
            ) or None,
            payer_fee_value or str(
                incust_pay_configuration.charge_fixed or 0
            ) or None,
        )
        if payer_fee_data:
            amount = amount + payer_fee_data.fee

            await create_or_update_payer_fee(
                "incust_pay", invoice.id, payer_fee_data, "pending"
            )

        if not payment:
            payment = await make_payment_for_incust_pay(
                brand, amount_to_pay, data, self.lang,
                invoice, order, payer_fee_data,
                store_id, self.bot,
                ewallet_discount_percent,
                (
                    round(ewallet_discount_amount / 100, 2)
                    if ewallet_discount_amount is not None
                    else None
                ),
            )

        if invoice.status == "payed":
            raise InvoicePaymentInvoicePayedError(invoice.id)

        if invoice.status != "not_payed":
            raise InvoicePaymentInvoiceStatusError(invoice.id, invoice.status)

        # await pre_checkout_payment(invoice)

        terminal_api_key = incust_pay_configuration.terminal_api_key
        server_api_url = incust_pay_configuration.server_api_url
        payment_title = (f"{incust_pay_configuration.name} #"
                         f"{data.order_id or data.invoice_id or ''}")
        rules_type = incust_pay_configuration.rules_type
        incust_account_id = data.payment_id
        payment_method = "incust_pay"
        incust_account_name = data.account_name
        incust_card_id = data.card_number
        incust_card_name = data.card_name
        comment = data.comment
        merchant_data = [IncustPayMerchantData(**md) for md in
                         json_data.get("merchant_data")] if json_data.get(
            "merchant_data", None
        ) else None

        base_data = {
            "payment_id": incust_account_id,
            "payment_type": data.payment_type,
            "skip_message": False,  # messages should not be skipped
            "special_account_pin": data.pin,
            "odometer": data.odometer,
            "vehicle_id": data.vehicle_id,
        }

        if data.id_type == "user":
            if not self.user:
                raise AuthNotAuthorisedError()
            base_data["id"] = self.user.incust_external_id
            base_data["id_type"] = "external-id"
        elif data.id_type == "card":
            if not data.card_number:
                raise IncustPayCardNumberIsMissedForIdTypeCardError()
            base_data["id"] = data.card_number
            base_data["id_type"] = "card"
        else:
            raise ValueError(f"Unknown id_type {data.id_type}")

        payment_data = PaymentCallBackData(
            payment_uuid=payment.uuid_id,
            payment_method="incust-pay" if not data.is_ewallet_payment else "ewallet",
            incust_pay_data=json.loads(data.json()),
            incust_server_api_url=server_api_url,
            terminal_api_key=terminal_api_key,
            ewallet_id=json_data[
                'ewallet_id'] if data.is_ewallet_payment and json_data.get(
                'ewallet_id'
            ) else None,
        )

        params = {
            "amount_to_pay": amount_to_pay,
            "amount": amount,
            "incust_account_id": incust_account_id,
            "invoice_id": invoice.id if invoice else None,
            "base_data": base_data,
            "payment_id": payment.id if payment else None,
            "payment_method": payment_method,
            "payment_settings_id": payment_settings.id if payment_settings else None,
            "object_payment_settings_id": data.object_payment_settings_id,
            "rules_type": rules_type,
            "order_id": order.id if order else None,
            "comment": comment,
            "incust_account_name": incust_account_name,
            "incust_card_id": incust_card_id,
            "incust_card_name": incust_card_name,
            "payment_title": payment_title,
            "merchant_data": merchant_data,
        }

        ewallet_id = None
        if data.is_ewallet_payment and json_data.get('ewallet_id'):
            ewallet_id = json_data.get('ewallet_id')

        loyalty_settings = await get_loyalty_settings_by_context(
            invoice=invoice,
            brand_id=self.brand.id,
            store_id=store_id,
            ewallet_id=ewallet_id,
            profile_id=invoice.group_id if invoice else self.brand.group_id,
            invoice_template_id=invoice.invoice_template_id if invoice else None,
        )
        if not loyalty_settings:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No loyalty settings found"
            )

        try:
            response = await process_make_incust_check(
                params["amount_to_pay"], params["amount"], params["incust_account_id"],
                invoice, base_data,
                payment, payment_data, params["payment_method"], payment_settings,
                loyalty_settings,
                params["rules_type"],
                order, params["merchant_data"],
                params["comment"], params["incust_account_name"],
                params["incust_card_id"], params["incust_card_name"],
                params["payment_title"],
                self.lang
            )
            if not response:
                return None
        except Exception as e:
            await self.handle_incust_check_error(e, params)
            raise e

        await finalize_payment(payment_data, invoice, payment, self.brand, order)

        return schemas.IncustPaySuccess(
            ok=True,
            incust_transaction=response,
        )

    async def handle_incust_check_error(self, e: Exception, params: Dict[str, Any]):
        message_parts = ["!!! process_make_incust_check ERROR !!!\n\n"]

        if isinstance(e, BusinessPaymentError):
            if hasattr(e.original_error, "text_variable") and hasattr(
                    e.original_error, "text_kwargs"
            ):
                text_variable = e.original_error.text_variable
                text_kwargs = e.original_error.text_kwargs
                bp_message_text = await f(text_variable, self.lang, **text_kwargs)
            else:
                bp_message_text = str(e.original_error)
            message_parts = [f"<b>Business payment error</b>: {bp_message_text}\n\n"]

        if isinstance(e, ErrorWithTextVariable):
            text_variable = e.text_variable
            text_kwargs = e.text_kwargs
            if hasattr(e, "message") and e.message:
                text_kwargs["message"] = e.message

        elif isinstance(e, HTTPException):
            text_variable = "incust pay incust error"
            text_kwargs = {"message": e.detail}
        elif hasattr(e, "message") and e.message:
            text_variable = "external loyalty error"
            text_kwargs = {"message": e.message}
        else:
            text_variable = "make incust check unknown error"
            text_kwargs = {"error": str(e)}

        message_parts.append(await f(text_variable, self.lang, **text_kwargs))
        message_parts.append("\n")

        message_data = {}

        if self.bot:
            message_data.update({"bot_id": self.bot.id})
            if hasattr(self.bot, "group_id") and self.bot.group_id:
                message_data.update({"group_id": self.bot.group_id})

        if self.user:
            message_data.update({"full_name": self.user.full_name})
            if hasattr(self.user, "id") and self.user.id:
                message_data.update({"user_id": self.user.id})
            if hasattr(self.user, "username") and self.user.username:
                message_data.update(
                    {"username": self.user.username or self.user.wa_phone}
                )

        if self.brand:
            message_data.update({"brand_id": self.brand.id})
            if hasattr(self.brand, "name") and self.brand.name:
                message_data.update({"brand_name": self.brand.name})

        if params.get("merchant_data"):
            message_data.update(
                {"merchant_data": [md.dict() for md in params['merchant_data']]}
            )
            params.pop("merchant_data")

        # for key, value in params.items():
        #     if value is not None:
        #         message_data.update({key: value})

        formatted_message_data = json.dumps(message_data, indent=4, ensure_ascii=False)
        await send_message_to_platform_admins(
            "".join(message_parts) + "\n\n" + formatted_message_data
        )


async def make_payment_for_incust_pay(
        brand: Brand,
        amount: int,
        data: schemas.IncustPayPayData,
        lang: str,
        invoice: Invoice,
        order: StoreOrder | None = None,
        payer_fee_data: PayerFeeData | None = None,
        store_id: int | None = None,
        bot: ClientBot | None = None,
        ewallet_discount_percent: float | None = None,
        ewallet_discount_amount: float | None = None,
):
    return_url = await make_client_return_url(
        brand, lang,
        bot, order, invoice,
        data.is_webview,
    )
    if invoice.invoice_type == schemas.InvoiceTypeEnum.INTEGRATION:
        if invoice.client_redirect_url:
            return_url = invoice.client_redirect_url

    if payer_fee_data:
        amount = amount + payer_fee_data.fee
    payment = await Payment.create(
        invoice_id=invoice.id, payment_method="",
        amount=amount,
        status='pending',
        currency=invoice.currency,
        return_url=return_url,
        user_id=invoice.user_id,
        brand_id=brand.id, store_id=store_id,
        ewallet_discount_percent=ewallet_discount_percent,
        ewallet_discount_amount=ewallet_discount_amount,
    )
    return payment
