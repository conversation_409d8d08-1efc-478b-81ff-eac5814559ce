from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BusinessPaymentError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "business payment error text"

    def __init__(self, original_error: Exception):
        self.original_error = original_error
        super().__init__(original_error=str(original_error))

    def __repr__(self):
        return f"Business payment error: {self.original_error}"


class IncustPayTokenExpiredError(ErrorWithHTTPStatus):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "incust pay token expired error"


class IncustPayTokenInvalidError(ErrorWithHTTPStatus):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "incust pay token invalid error"


class IncustPayConfigurationNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "incust pay configuration not found error"

    def __init__(self, id: int):
        super().__init__(id=id)


class IncustPayConfigurationDisabledError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "incust pay configuration disabled error"

    def __init__(self, id: int, name: str):
        super().__init__(id=id, name=name)


class IncustPayCardNumberIsMissedForIdTypeCardError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "incust pay configuration card number is missed for id type card error"


class IncustNeedConfirmError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "incust pay need confirm error"


class BusinessPayMerchantNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "business pay merchant not found error"
