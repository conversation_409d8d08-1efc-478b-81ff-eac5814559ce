import logging
from typing import Literal

from fastapi import HTTPException
from incust_api.api import term
from incust_terminal_api_client import MessageResponse
from incust_terminal_api_client.models import (
    Check as IncustCheck,
)
from starlette import status

from core.admin_notification.service import create_system_notification
from core.loyalty.incust_api import incust
from core.payment.funcs import get_translated_post_payment_info
from core.payment.incust_pay.exceptions import (
    BusinessPayMerchantNotFoundError, BusinessPaymentError, IncustNeedConfirmError,
)
from core.payment.payment_processor import PaymentProcessor
from db import crud
from db.models import (
    BusinessPaymentSetting, EWalletPayment, Invoice, LoyaltySettings,
    ObjectPaymentSettings,
    OrderProduct, Payment, PaymentSettings, StoreOrder, StoreOrderPayment, StoreProduct,
)
from loggers import JSONLogger
from schemas import (
    IncustPayMerchantData,
    SystemNotificationCategory, SystemNotificationType,
)
from schemas.payment.payment import PaymentCallBackData
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f


async def create_incust_check_data(
        base_data: dict, amount: int, invoice: Invoice, order: StoreOrder | None = None
) -> IncustCheck:
    """Створює дані чеку в оригінальній схемі нового InCust API."""
    check_items = []

    if order:
        order_id = order.id
        order_products: list[tuple[OrderProduct, StoreProduct]] \
            = await crud.get_order_products(order_id, with_products=True)

        for order_product, product in order_products:
            category = await crud.get_product_main_category(product.id)
            if category:
                check_category = category.external_id or category.name
            else:
                check_category = "uncategorized"

            check_items.append(
                {
                    "title": product.name,
                    "code": product.product_id,
                    "price": round(order_product.final_price / 100, 2),
                    "quantity": order_product.quantity,
                    "amount": round(order_product.total_sum / 100, 2),
                    "category": check_category,
                }
            )
    else:
        invoice_items = await crud.get_invoice_items(invoice.id)
        for invoice_item in invoice_items:
            check_items.append(
                {
                    "title": invoice_item.name,
                    "code": invoice_item.item_code or invoice_item.name,
                    "price": round(invoice_item.final_price / 100, 2),
                    "quantity": invoice_item.quantity,
                    "amount": round(invoice_item.final_sum / 100, 2),
                    "category": invoice_item.category,
                }
            )

    # Створюємо чек в оригінальній схемі нового API
    incust_check = term.m.Check(
        amount=round(amount / 100, 2),
        amount_to_pay=round(amount / 100, 2),
        check_items=check_items,
        **base_data,
    )
    return incust_check


async def make_business_payment(
        amount_to_pay: int,
        currency: str, title: str,
        merchant_data_in: list[IncustPayMerchantData],
        payment_uuid_id: str,
        business_payment_setting: BusinessPaymentSetting
) -> dict | None:
    business_payment_logger = JSONLogger(
        "incust.incust_pay.business_pay", {
            "data": {"amount_to_pay": amount_to_pay},
            "merchant_data": merchant_data_in if merchant_data_in else None,
        },
    )

    if (
            not business_payment_setting or
            business_payment_setting.is_deleted or
            not business_payment_setting.is_enabled
    ):
        business_payment_logger.error(
            "business payment setting not found, deleted or not enabled"
        )
        raise BusinessPayMerchantNotFoundError()

    # Перевіряємо наявність даних мерчанта - дані мерчанта не обов'язкові
    if not merchant_data_in:
        business_payment_logger.debug(
            "merchant_data_in is empty, skipping business payment transfer"
        )
        return None

    if not hasattr(
            business_payment_setting, "payment_data"
    ) or not business_payment_setting.payment_data:
        business_payment_logger.debug(
            "business_payment_setting has no payment_data, skipping business payment "
            "transfer"
        )
        return None

    merchant_data = None
    payment_data = None

    business_payment_logger.set_data(
        {
            "business_payment_setting": {
                "id": business_payment_setting.id,
                "name": business_payment_setting.name,
                "is_enabled": business_payment_setting.is_enabled,
                "is_deleted": business_payment_setting.is_deleted,
                "payment_data_count": len(business_payment_setting.payment_data)
            }
        }
    )
    business_payment_logger.debug("Business payment setting info")

    for bps_pd in business_payment_setting.payment_data:
        if bps_pd.is_deleted or not bps_pd.is_enabled:
            continue

        for merchant_payment_data in merchant_data_in:
            if (
                    merchant_payment_data.business_payment_data_id == bps_pd.id and
                    merchant_payment_data.payment_method == bps_pd.payment_method.value
            ):
                payment_data = bps_pd
                merchant_data = merchant_payment_data.data

                business_payment_logger.set_data(
                    {
                        "payment_data_source": "business_payment_setting.payment_data",
                        "payment_data_id": bps_pd.id,
                        "payment_method": bps_pd.payment_method.value,
                        "is_sandbox_value": bps_pd.json_data.get(
                            'is_sandbox'
                        ) if bps_pd.json_data else None,
                        "is_sandbox_type": type(
                            bps_pd.json_data.get('is_sandbox')
                        ).__name__ if bps_pd.json_data and bps_pd.json_data.get(
                            'is_sandbox'
                        ) is not None else 'None'
                    }
                )
                business_payment_logger.debug("Found payment credentials")
                break

        if merchant_data:
            break

    if not merchant_data or not payment_data:
        business_payment_logger.debug(
            "matching merchant_data or payment_data not found, skipping business "
            "payment transfer"
        )
        return None

    processor = PaymentProcessor(payment_data.payment_method.value)
    make_bp_payment_func = getattr(processor, "make_business_payment")

    is_sandbox_value = payment_data.json_data.get('is_sandbox')

    business_payment_logger.set_data(
        {
            "payment_data_source": "before_make_bp_payment_func",
            "payment_method": payment_data.payment_method.value,
            "is_sandbox_value": is_sandbox_value,
            "is_sandbox_type": type(
                is_sandbox_value
            ).__name__ if is_sandbox_value is not None else 'None'
        }
    )
    business_payment_logger.debug(
        "Payment credentials before make_bp_payment_func call"
    )

    result = await make_bp_payment_func(
        amount_to_pay=amount_to_pay,
        currency=currency,
        credentials=payment_data.json_data,
        merchant_data=merchant_data,
        payment_uuid_id=payment_uuid_id,
        title=title,
    )
    business_payment_logger.debug("result make business payment", {"result": result})
    return {"merchant_data": merchant_data, "result": result} if result else None


async def process_store_order_payment(
        group_id: int,
        payment_settings: PaymentSettings, invoice_id: int,
        incust_account_id: str, incust_account_name: str,
        comment: str | None = None,
        business_payment_setting_id: int | None = None,
        incust_card_id: str | None = None, incust_card_name: str | None = None,
        business_payment_result: dict | None = None,
        order_id: int | None = None, store_id: int | None = None,
        invoice_template_id: int | None = None,
        lang: str | None = None,
):

    store_order_payment_logger = JSONLogger(
        "incust.incust_pay", {
            "data": {
                "payment_settings_id": payment_settings.id,
                "incust_account_id": incust_account_id,
                "business_payment_setting_id": business_payment_setting_id,
                "store_id": store_id, "invoice_template_id": invoice_template_id
            }
        },
    )
    store_order_payment_logger.debug("process_store_order_payment -> ")

    store_payment = await ObjectPaymentSettings.get(
        payment_settings_id=payment_settings.id,
        store_id=store_id, is_enabled=True, is_deleted=False,
        invoice_template_id=invoice_template_id,
    )
    if store_payment and store_payment.json_data:
        json_data = store_payment.json_data
    else:
        json_data = payment_settings.json_data

    post_payment_info = await get_translated_post_payment_info(
        group_id, payment_settings, store_payment, lang or "en",
    )
    if order_id:
        order_payment = await StoreOrderPayment.get(order_id=order_id)
    else:
        order_payment = await StoreOrderPayment.get(invoice_id=invoice_id)
    if not order_payment:
        await crud.create_store_order_payment(
            order_id=order_id,
            invoice_id=invoice_id if not order_id else None,
            payment_method=payment_settings.payment_method,
            comment=comment,
            json_data=json_data,
            name=payment_settings.name,
            description=payment_settings.description,
            payment_settings_id=payment_settings.id,
            label_comment=payment_settings.label_comment,
            post_payment_info=post_payment_info,
            incust_account_id=incust_account_id,
            incust_card_id=incust_card_id,
            incust_card_name=incust_card_name,
            incust_account_name=incust_account_name,
            business_payment_setting_id=business_payment_setting_id,
            business_payment_merchant_data=business_payment_result if
            business_payment_result else None,
        )
    else:
        await order_payment.update(
            payment_method=payment_settings.payment_method,
            comment=comment,
            json_data=json_data,
            name=payment_settings.name,
            description=payment_settings.description,
            payment_settings_id=payment_settings.id,
            label_comment=payment_settings.label_comment,
            post_payment_info=post_payment_info,
            incust_account_id=incust_account_id,
            incust_card_id=incust_card_id,
            incust_card_name=incust_card_name,
            incust_account_name=incust_account_name,
            business_payment_setting_id=business_payment_setting_id,
            business_payment_merchant_data=business_payment_result if
            business_payment_result else None,
        )


async def request_make_incust_check(
        group_id: int,
        incust_check: IncustCheck,
        loyalty_settings: LoyaltySettings,
        lang: str | None = "en",
        rules_type: Literal[
                        "by-all-rules", "by-charge-only-rules", "without-rules"] |
                    None = "by-all-rules",
):
    try:
        async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
            response = await api.make_check_by_rules(
                rules_type,
                incust_check,
            )
    except HTTPException:
        # Перепускаємо HTTP винятки як є
        raise
    except Exception as e:
        logging.error(e, exc_info=True)
        await send_message_to_platform_admins(
            f"An unknown error occurred while processing incust payment.\n"
            f"{incust_check.check_items[0].title=}\n"
            f"Error: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=await f("incust pay incust unknown error", lang)
        )
    if isinstance(response,  MessageResponse):
        err_text = await f("incust pay need confirm settings text", lang)
        err_text += f"{incust_check.check_items[0].title=}\n"

        await create_system_notification(
            "profile:edit",
            group_id=group_id, category=SystemNotificationCategory.PAYMENT,
            type_notification=SystemNotificationType.INCUST_PAY_ERROR,
            title="Incust pay error",
            content=err_text
        )

        raise IncustNeedConfirmError()
    return response


async def process_make_incust_check(
        amount_to_pay: int, amount: int, incust_account_id: str, invoice: Invoice,
        base_data: dict, payment: Payment | EWalletPayment,
        payment_data: PaymentCallBackData,
        payment_method: str, payment_settings: PaymentSettings,
        loyalty_settings: LoyaltySettings,
        rules_type: Literal[
                        "by-all-rules", "by-charge-only-rules", "without-rules"] |
                    None = "by-all-rules",
        order: StoreOrder | None = None,
        merchant_data: list[IncustPayMerchantData] | None = None,
        comment: str | None = None,
        incust_account_name: str | None = None, incust_card_id: str | None = None,
        incust_card_name: str | None = None,
        payment_title: str | None = None,
        lang: str | None = None,
        skip_incust_message: bool | None = None,
):
    if not lang:
        lang = "en"

    business_payment_result = None

    incust_check = await create_incust_check_data(
        base_data, amount, invoice, order
    )

    if skip_incust_message:
        incust_check.skip_message = True

    business_payment_setting = await BusinessPaymentSetting.get(
        incust_account_id=incust_account_id,
        is_enabled=True,
        is_deleted=False
    )
    make_incust_check_logger = JSONLogger("imake_incust_check")

    if business_payment_setting:
        payment_data_count = len(business_payment_setting.payment_data) if hasattr(
            business_payment_setting, 'payment_data'
        ) and business_payment_setting.payment_data else 0

        make_incust_check_logger.set_data(
            {
                "business_payment_setting_source": "process_make_incust_check",
                "business_payment_setting": {
                    "id": business_payment_setting.id,
                    "name": business_payment_setting.name,
                    "incust_account_id": business_payment_setting.incust_account_id,
                    "payment_data_count": payment_data_count
                }
            }
        )
        make_incust_check_logger.debug("Found BusinessPaymentSetting")

    if not business_payment_setting:
        # Якщо немає BusinessPaymentSetting, то просто виконуємо запит через новий API
        response = await request_make_incust_check(
            invoice.group_id,
            incust_check,
            loyalty_settings,
            lang, rules_type,
        )
    else:
        # Якщо є BusinessPaymentSetting, то спочатку резервуємо чек

        async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
            incust_transaction = await api.reserve_check(incust_check)

        try:
            make_incust_check_logger.set_data(
                {
                    "payment_source": "process_make_incust_check",
                    "payment": {
                        "id": payment.id,
                        "uuid_id": payment.uuid_id,
                        "type": type(payment).__name__,
                        "payment_method": getattr(payment, 'payment_method', None)
                    }
                }
            )
            make_incust_check_logger.debug("Before make_business_payment call")

            # Визначаємо суму для бізнес-платежу без комісії платіжного методу
            payment_amount_without_fee = amount_to_pay - invoice.payer_fee
            make_incust_check_logger.debug(
                f"Deducting payment fee from invoice: {invoice.payer_fee}, original "
                f"amount: {amount_to_pay}, new amount: {payment_amount_without_fee}"
            )

            business_payment_result = await make_business_payment(
                payment_amount_without_fee, invoice.currency, payment_title,
                merchant_data, payment.uuid_id, business_payment_setting
            )
            make_incust_check_logger.debug("After make_business_payment call")
        except Exception as e:
            make_incust_check_logger.error(e, exc_info=True)
            async with incust.term.CheckTransactionsApi(
                    loyalty_settings, lang=lang
            ) as api:
                result = await api.transaction_cancel(
                    term.m.TransactionCancelRequest(
                        transaction_id=incust_transaction.id,
                        comment="Canceled by error business payment"
                    )
                )
            make_incust_check_logger.debug("cancel transaction", {"result": result})
            raise BusinessPaymentError(e)

        async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
            response = await api.finalize_check(
                term.m.FinalizeCheckRequest(
                    id=incust_transaction.id
                )
            )

    payment_data.incust_transaction = response.to_dict() if hasattr(
        response, 'to_dict'
    ) else response.dict()
    payment_data.status = "success"
    payment_data.payment_method = payment_method
    payment_data.business_payment_result = {
        "result": business_payment_result,
        "merchant_data": [md.dict() for md in merchant_data] if merchant_data else None
    } if business_payment_result else None

    await process_store_order_payment(
        invoice.group_id,
        payment_settings,
        invoice.id,
        incust_account_id,
        incust_account_name or "",
        comment,
        business_payment_setting.id if business_payment_setting else None,
        incust_card_id,
        incust_card_name,
        business_payment_result,
        order.id if order else None,
        store_id=order.store_id if order else None,
        invoice_template_id=invoice.invoice_template_id,
        lang=lang,
    )
    return response


def make_incust_error_detail(exception: Exception):
    return str(exception)
