from db.models import ClientBot, MenuInStore, User
from utils.text import f
from . import messangers_adapters as ma
from .whatsapp.keyboards import get_wa_menu_keyboard as get_wa_main_menu_keyboard


async def get_bot_menu_keyboard(
        user: User,
        bot: ClientBot,
        lang: str,
        menu_in_store: MenuInStore | None = None,
        need_active_menu_in_store_button: bool = True,
):

    if bot.bot_type == "telegram":
        from client.main.keyboards import get_menu_keyboard
        return await get_menu_keyboard(
            user, bot, lang, menu_in_store, need_active_menu_in_store_button
        )
    elif bot.bot_type == "whatsapp":
        return await get_wa_main_menu_keyboard(user, bot, lang, menu_in_store)
    return None


async def get_to_main_menu_keyboard(lang: str):
    keyboard = ma.InlineKeyboard()
    keyboard.add_buttons(
        ma.InlineKeyboardButton(
            await f("bot main menu button", lang),
            data="to_main_menu"
        )
    )
    return keyboard
