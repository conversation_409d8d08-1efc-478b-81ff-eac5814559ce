from pydantic import BaseModel

from schemas import BaseTemplateSchema, CouponShowData


class Notification(BaseModel):
    title: str
    body: str


class Data(BaseModel):
    ic_message_id: str
    ic_type: str
    ic_id: str


class InCustMessage(BaseModel):
    to: list[str]
    data: Data
    notification: Notification


class IncustMessageTemplate(BaseTemplateSchema):
    TEMPLATE_PATH = "incust_message.html"

    header: str
    title: str
    coupons: list[CouponShowData] | None = None
    message: str | None = None
    link: str | None = None
    photo: str | None = None
    link_text: str | None = None
