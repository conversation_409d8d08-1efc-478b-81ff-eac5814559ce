import asyncio
from urllib.parse import urlparse

from incust_client_api_client.models.news import News
from psutils.mailing import GmailClient

import schemas
from config import DEBUG, LOC7_EMAIL_LOGIN, LOC7_EMAIL_PASSWORD
from core.external_coupon import send_coupon_info
from core.messangers_adapters import types
from core.templater import templater
from db.models import Brand, ClientBot, User
from loggers import JSONLogger
from utils.email_funcs import is_valid_email, send_with_attachments
from utils.redefined_classes import InlineBtn, InlineKb
from utils.text import f, fd, html_to_markdown
from .schemas import IncustMessageTemplate, Notification
from ...notifications.funcs import put_message_to_kafka


def is_valid_url(url):
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except ValueError:
        return False


class IncustMessageSender:

    def __init__(
            self, brand: Brand, group_bot: ClientBot,
            user: User, lang: str,
    ):
        self.brand: Brand = brand
        self.group_bot: ClientBot = group_bot
        self.user: User = user
        self.lang: str = lang
        self.debugger = JSONLogger("incust.message",
            {   
                "brand_id": self.brand.id,
                "user_is_only_email": self.user.is_only_email,
                "user_email": self.user.email,
                "user_messangers": self.user.messangers,
                "self.group_bot.bot_type": self.group_bot.bot_type if self.group_bot else None,
                "self.group_bot.username": self.group_bot.username if self.group_bot else None,
            }
        )


    async def send_coupon(
            self, coupon: schemas.CouponShowData, is_telegram_only: bool | None = False
    ) -> (
            types.Message |
            None):

        if self.user.is_only_email and not is_telegram_only:
            return await self.send_email(coupons=[coupon])

        await send_coupon_info(
            coupon,
            self.user,
            self.group_bot,
            self.lang,
            self.brand.id,
        )
        self.debugger.debug("Coupon sent successfully")
        return

    async def send_notification(
            self,
            notification: Notification | News,
            coupons: list[schemas.CouponShowData] | None = None,
            wa_keyboard: types.Keyboard | None = None
    ) -> None | bool:
        self.debugger.debug("send_notification -> ")
        is_wa_menu_sent = None

        if (self.user.is_only_email or (self.user.email and self.group_bot and self.group_bot.bot_type not in
                self.user.messangers)
        ):
            await self.send_email(notification=notification, coupons=coupons)
            return None

        if DEBUG:
            await self.send_email(notification=notification, coupons=coupons)

        if not self.group_bot:
            self.debugger.debug("group_bot is None. Return.")
            return None

        photo = None
        content_type = "text"
        keyboard = None

        if isinstance(notification, News):
            # Обробка News типу з нового API
            title = notification.title if notification.title is not None else ""
            description_extended = notification.description_extended if notification.description_extended is not None else None
            description = notification.description if notification.description is not None else ""
            photo = notification.photo if notification.photo is not None else None
            link = notification.link if notification.link is not None else None

            text = (f"{title}\n\n"
                    f"{description_extended if description_extended else description}")

            if self.group_bot.bot_type == "whatsapp":
                text = html_to_markdown(text)
            else:
                text = text.replace("<br>", "\n")

            if photo:
                photo = photo
                content_type = "photo"

            if link:
                if self.group_bot.bot_type == "telegram":
                    if is_valid_url(link):
                        keyboard = InlineKb().add(
                            InlineBtn(
                                await f('web store open button text', self.lang),
                                url=link
                            )
                        )
                    else:
                        self.debugger.error(f"Not valid url {link=}")
                        text += f"\n\n{link}"
                else:
                    text += f"\n\n{link}"
        elif isinstance(notification, Notification):
            if self.group_bot.bot_type == "whatsapp":
                text = html_to_markdown(notification.body)
            else:
                text = notification.body.replace("<br>", "\n")
        else:
            return None

        kwargs = {}

        if self.group_bot.bot_type == "telegram":
            kwargs["keyboard"] = keyboard
        else:
            if wa_keyboard and not photo:
                kwargs["keyboard"] = wa_keyboard
                is_wa_menu_sent = True

        await put_message_to_kafka(self.group_bot, self.user, text, content_type,
            kwargs.get("keyboard", None),
            media=True if photo else False,
            content=dict(
                url=photo,
                caption=(text[:1020] + " ...") if len(text) > 1024 else text
            ) if photo else None,

        )

        self.debugger.debug("Message sent to kafka successfully",
            {"user_id": self.user.id, "user_name": self.user.name, "text": text[0:100]}
        )
        return is_wa_menu_sent

    async def get_localisation_texts(self) -> dict[str, str]:
        return await fd(
            {
                "coupon_subject": "incust message sender coupon subject text",
                "notification_subject": "incust message sender notification subject "
                                        "text",
                "coupon_message": "incust message sender coupon body text",
                "notification_message": "incust message sender notification body text",
                "link_text": "web store open button text",
            }, self.lang
        )

    async def send_email(
            self,
            coupons: list[schemas.CouponShowData] | None = None,
            notification: Notification | News | None = None,
    ):
        self.debugger.debug("send_email -> ")
        if not any([coupons, notification]):
            self.debugger.error("Email not sent. Not any coupons or notification")
            return

        if not is_valid_email(self.user.email):
            self.debugger.error("Email not valid")
            return

        gmail = GmailClient(LOC7_EMAIL_LOGIN, LOC7_EMAIL_PASSWORD)
        texts = await self.get_localisation_texts()

        if coupons and not notification:
            subject = texts["coupon_subject"]
            header = texts["coupon_message"].format(user_name=self.user.name)

            if coupons[0].title:
                title = f"{coupons[0].title} - {coupons[0].code}"
            else:
                title = coupons[0].code

            template = IncustMessageTemplate(
                header=header, title=title, coupons=coupons
            )
            
            # Отримуємо PDF з media manager для email
            attachments = None
            if coupons[0].pdf_media_id:
                try:
                    from core.loyalty import coupon_service, PDFFormatType
                    
                    pdf_mime = await coupon_service.load_pdf_from_media_object(
                        coupons[0].pdf_media_id,
                        format_type=PDFFormatType.MIME_ATTACHMENT,
                        filename_override=f"coupon_{coupons[0].code or coupons[0].id}.pdf"
                    )
                    
                    if pdf_mime:
                        attachments = [pdf_mime]
                    else:
                        self.debugger.warning(f"Could not load PDF for media_id {coupons[0].pdf_media_id}")
                except Exception as e:
                    self.debugger.error(f"Error loading PDF from media manager for email: {e}")

        else:
            if isinstance(notification, News):
                description_extended = notification.description_extended if notification.description_extended is not None else None
                description = notification.description if notification.description is not None else ""
                title = notification.title if notification.title is not None else ""
                    
                message = f"{description_extended}" if description_extended else description
                subject = description
                header = title
            else:
                message = notification.body
                subject = texts["notification_subject"]
                header = texts["notification_message"].format(
                    user_name=self.user.name, brand_name=self.brand.name
                )

            # Отримуємо поля для template залежно від типу notification
            if isinstance(notification, News):
                template_title = notification.title if notification.title is not None else ""
                template_photo = notification.photo if notification.photo is not None else None
                template_link = notification.link if notification.link is not None else None
            else:
                template_title = ""
                template_photo = None
                template_link = None
                
            template = IncustMessageTemplate(
                header=header,
                title=template_title,
                message=message,
                photo=template_photo,
                link=template_link,
                link_text=await f(
                    "incust loyalty referral learn more button", lang=self.lang
                ) if template_link else None,
                coupons=coupons,
            )
            # Отримуємо PDF файли для купонів в notification
            attachments = []
            if coupons:
                for coupon in coupons:
                    if coupon.pdf_media_id:
                        try:
                            from core.loyalty import coupon_service, PDFFormatType
                            
                            pdf_mime = await coupon_service.load_pdf_from_media_object(
                                coupon.pdf_media_id,
                                format_type=PDFFormatType.MIME_ATTACHMENT,
                                filename_override=f"coupon_{coupon.code or coupon.id}.pdf"
                            )
                            
                            if pdf_mime:
                                attachments.append(pdf_mime)
                            else:
                                self.debugger.warning(f"Could not load PDF for media_id {coupon.pdf_media_id}")
                        except Exception as e:
                            self.debugger.error(f"Error loading PDF from media manager for email: {e}")

        html = await templater.make_template(template, self.brand.group_id)

        asyncio.ensure_future(
            send_with_attachments(
                gmail,
                destination=self.user.email,
                subject=subject,
                html=html,
                from_name=self.brand.name,
                attachments=attachments,
            )
        )
