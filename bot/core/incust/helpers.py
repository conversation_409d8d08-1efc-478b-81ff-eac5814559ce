from incust_api.api import term

import schemas
from core.exceptions import CoreFinanceSystemError
from db.models import (
    Brand, Group, InvoiceTemplate, InvoiceTemplateItem, MenuInStore,
    Store,
)
from utils.text import f


async def make_incust_check_items_for_invoice(
        entered_amount: float | None = None,
        template_items: list[InvoiceTemplateItem] | None = None,
        template_count: int | None = 1,
        product_code: str | None = None,
        lang: str | None = None
):
    if template_items:
        return [
            term.m.CheckItem(
                title=item.name,
                code=item.item_code or item.name,
                price=round(item.price / 100, 2),
                quantity=item.quantity * (template_count or 1),
                amount=round(item.price / 100, 2) * (template_count or 1),
                category=item.category,
            )
            for item in template_items
            if not item.is_deleted
        ]

    if not entered_amount:
        return []

    return [
        schemas.CheckItem(
            title=await f("invoice entered amount item name", lang),
            code=product_code or "entered_amount",
            price=entered_amount,
            quantity=1,
            amount=entered_amount,
            category="entered_amount",
        )
    ]


async def get_currency_from_store_or_brand(
        brand_id: int | None = None, group_id: int | None = None,
        store_id: int | None = None,
        menu_in_store: MenuInStore | None = None,
        exception: any = None,
        invoice_template: InvoiceTemplate | None = None,
) -> str | None:

    if invoice_template:
        return invoice_template.currency

    if menu_in_store and menu_in_store.store_id:
        store_id = menu_in_store.store_id

    if store_id:
        store = await Store.get(store_id)
        if store:
            return store.currency
    if brand_id:
        brand = await Brand.get(brand_id)
        group_id = brand.group_id

    if group_id:
        group = await Group.get(group_id)
        if not group:
            if exception:
                raise exception()
            raise CoreFinanceSystemError(group_id)
        return group.currency

    return None
