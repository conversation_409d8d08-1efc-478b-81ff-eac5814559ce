from __future__ import annotations

from abc import ABC
from typing import Literal, Type

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus
from utils.text import f

ExTypeLiteral = Literal["internal", "external"]


class IncustError(Exception):
    code: int | None = None
    response_status: int | None = None
    _errors_by_code: dict[int, Type[IncustError]] = dict()
    _errors_by_status: dict[int, Type[IncustError]] = dict()

    def __init__(
            self,
            ex_type: ExTypeLiteral,
            msg_text: str,
            code: int | None = None,
            message: str | None = None,
            original_error_message: str | None = None,
            response_status: int | None = None,
            response_reason: str | None = None,
    ):
        self.ex_type: ExTypeLiteral = ex_type  # internal or external error
        self.msg_text: str = msg_text  # message text to view

        self.code: int | None = code  # incust error code
        self.message: str | None = message  # incust error message
        self.original_error_message: str | None = original_error_message

        self.response_status: int | None = response_status
        self.response_reason: str | None = response_reason

        super().__init__(self.message)

    def __repr__(self):
        return (
            f"{self.__class__.__name__}: {self.ex_type} "
            f"code={self.code} "
            f"message={self.message} "
            f"response_status={self.response_status} "
            f"response_reason={self.response_reason}"
        )

    def __init_subclass__(cls, **kwargs):
        if cls.code:
            if cls.code in cls._errors_by_code:
                raise TypeError(
                    f"IncustError with code {cls.code} has already been added"
                )
            cls._errors_by_code[cls.code] = cls
        if cls.response_status:
            if cls.response_status in cls._errors_by_status:
                raise TypeError(
                    f"IncustError with response_status {cls.response_status} has "
                    f"already been added"
                )
            cls._errors_by_status[cls.response_status] = cls
        super().__init_subclass__(**kwargs)

    @classmethod
    def detect_error_cls(
            cls,
            code: int | None = None,
            response_status: int | None = None,
    ) -> Type[IncustError]:
        if type(cls) is not IncustError:
            return cls

        cls_by_code = cls._errors_by_code.get(code) if code else None
        cls_by_status = cls._errors_by_status.get(
            response_status
        ) if response_status else None

        if all((cls_by_code, cls_by_status)) and not issubclass(
                cls_by_code, cls_by_status
        ):
            raise TypeError(
                "Detected both cls_by_code and cls_by_status"
                "For this cases cls_by_code have to be a child of cls_by_status"
            )

        return cls_by_code or cls_by_status or IncustUnknownError

    @classmethod
    async def build(
            cls,
            ex_type: ExTypeLiteral,
            lang: str,
            code: int | None = None,
            original_error_message: str | None = None,
            message: str | None = None,
            response_status: int | None = None,
            response_reason: str | None = None,
    ):
        error_cls = cls.detect_error_cls(code, response_status)

        if original_error_message:
            message = original_error_message
        else:
            message = message if message else await f(
                "web store unknown err text", lang
            )

        errors_texts = {
            140: 'coupon not found text',
            # 143: 'coupon used by another user'
        }

        variable = "store brand loyalty error"
        # variable = "external loyalty error"

        if code and code in errors_texts.keys():
            variable = errors_texts[code]
            original_error_message = await f(variable, lang, code=None, message=None)

        msg_text = await f(variable, lang, code=code, message=message)

        return error_cls(
            ex_type=ex_type,
            msg_text=msg_text,
            code=code,
            original_error_message=original_error_message,
            message=message,
            response_status=response_status,
            response_reason=response_reason,
        )


class IncustTerminalUnauth(IncustError):
    pass


class IncustUnknownError(IncustError):
    pass


class IncustUnauthorisedError(IncustError):
    response_status = status.HTTP_401_UNAUTHORIZED


class IncustCheckCompareError(ErrorWithHTTPStatus):
    status_code = status.HTTP_409_CONFLICT
    text_variable = "loyalty compare check error"


class IncustProhibitRedeemingBonusesError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "incust prohibit redeeming bonuses error"


class IncustProhibitRedeemingCouponsError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "incust prohibit redeeming coupons error"


class BaseIncustTopupError(ErrorWithHTTPStatus, ABC, base=True):
    def __init__(self, message: str = "Incust topup error", **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"Incust topup error: {self.message}"


class IncustTopupNotFoundOwnerCardError(BaseIncustTopupError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "loyalty topup not found owner card error"


class IncustTopupNotFoundSpecialAccountError(BaseIncustTopupError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "loyalty topup not found special account error"

    def __init__(self, account_id: str):
        super().__init__(
            account_id=account_id,
        )


class IncustTopupNotFoundOwnerSpecialAccountError(BaseIncustTopupError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "loyalty topup not found owner special account error"

    def __init__(self, account_id: str):
        super().__init__(
            account_id=account_id,
        )


class IncustTopupOwnerNotEnoughMoneyError(BaseIncustTopupError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "loyalty topup owner not enough money error"

    def __init__(self, account_name: str, account_id: str):
        super().__init__(
            account_name=account_name,
            account_id=account_id,
        )


class IncustTopupCantWriteoffWithoutOwnerError(BaseIncustTopupError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "loyalty topup cant writeoff without owner error"

    def __init__(self, account_id: str):
        super().__init__(
            account_id=account_id,
        )


class IncustTopupNoSpecialAccountForProvidedTypeError(BaseIncustTopupError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "loyalty topup no special account for provided type error"
