import logging
import re
from datetime import date, datetime, time
from typing import Any, Dict, Literal, Optional, Tuple, Type

import pytz
import transliterate as tl
from aiogram import <PERSON><PERSON>, Di<PERSON>atcher, types
from psutils.convertors import interval_to_str
from sqlalchemy import Integer, func, select
from sqlalchemy.orm import DeclarativeMeta

import exceptions
import schemas
from config import CRM_HOST, DEFAULT_TIME_ZONE, QRCODE_SCANNER_URL
from core.loyalty.customer_service import get_or_create_incust_customer
from core.messangers_adapters import Keyboard, Message
from db import DBSession, crud, db_func, sess
from db.models import (
    Brand, ClientBot, Group, IncustCustomer, Invoice, StoreAttribute, StoreProduct,
    User,
)
from loggers import JSONLogger
from utils.keyboards import active_button
from utils.localisation import localisation
from utils.message import send_tg_message, send_wa_message
from utils.numbers import format_currency
from utils.redefined_classes import InlineBtn, InlineKb
from utils.router import Router
from utils.text import c, f, html_to_markdown, parse_callback_data
from utils.translator import Translator, tg


async def make_invoice_tg_prices(invoice_id: int):
    items = await crud.get_invoice_items(invoice_id)
    return [
        types.LabeledPrice(
            label=item.name,
            amount=item.final_sum
        )
        for item in items
    ]


async def make_invoice_items_text(invoice: Invoice, group_lang: str, lang: str):
    items = await crud.get_invoice_items(invoice.id)
    items_texts = []
    for item in items:
        items_texts.append(
            await f(
                "invoice price info", lang,
                label=item.name,
                count=item.quantity,
                price=format_currency(
                    item.final_price / 100, invoice.currency, group_lang
                ),
                sum_amount=format_currency(
                    item.final_sum / 100, invoice.currency, group_lang
                ),
            )
        )
    return "\n".join(items_texts)


async def send_invoice(
        invoice: Invoice,
        payload: str,
        payment_token: str,
        bot_or_id: ClientBot | int | None = None,
        keyboard: InlineKb = None,
        user_chat_id: int | None = None,
) -> Optional[types.Message]:
    logger = JSONLogger(
        "send_invoice",
        {"invoice_id": invoice.id, "payload": payload, "user_chat_id": user_chat_id}
    )
    sender_bot = Bot.get_current()

    if isinstance(bot_or_id, ClientBot):
        bot = bot_or_id
    else:
        bot_id = bot_or_id
        if bot_id is None:
            bot_id = invoice.payment_bot_menu_id or invoice.bot_id
        bot = await ClientBot.get(bot_id)

    user = await User.get_by_id(invoice.user_id)
    group = await Group.get(invoice.group_id)
    receiver_chat_id = user.chat_id
    lang = await user.get_lang(bot.id)

    if not receiver_chat_id and user_chat_id:
        receiver_chat_id = user_chat_id

    if not keyboard:
        keyboard = InlineKb().row()
        pay_button_text = await f(
            "invoice pay button one", lang,
            amount=format_currency(
                invoice.converted_sum_to_pay, invoice.currency, group.lang
            ),
        )
    else:
        pay_button_text = await f(
            "invoice pay telegram button", lang,
            amount=format_currency(
                invoice.converted_sum_to_pay, invoice.currency, group.lang
            ),
        )

    keyboard.inline_keyboard[0].insert(0, InlineBtn(pay_button_text, pay=True))
    description = invoice.description or await f(
        "pay invoice to group", lang, group_name=group.name
    )
    logger.debug(
        f"with sender_bot.with_token({bot.token})", {
            "bot_id": bot.id, "sender_bot_id": sender_bot.id,
            "invoice_title": invoice.title, "description": description,
            "payload": payload,
            "payment_token": payment_token, "invoice_currency": invoice.currency
        }
    )
    with sender_bot.with_token(bot.token):
        msg = await sender_bot.send_invoice(
            receiver_chat_id,
            invoice.title,
            description,
            payload,
            payment_token,
            **invoice.photo_params,
            start_parameter="",
            currency=invoice.currency,
            prices=await make_invoice_tg_prices(invoice.id),
            photo_url=invoice.photo_url,
            need_name=invoice.need_name,
            need_phone_number=invoice.need_phone_number,
            need_email=invoice.need_email,
            reply_markup=keyboard,
        )
    if not invoice.message_id:
        await crud.save_invoice_message_id(invoice.id, msg.message_id)
    return msg


async def get_web_payment_button(
        invoice: Invoice, brand_id, bot_id: int,
        store_id: int, lang: str, is_one_pmt_method: bool = False,
        bot_type: Literal["telegram", "whatsapp"] = 'telegram'
) -> InlineBtn | tuple[str, str]:
    brand = await Brand.get(brand_id)
    group = await Group.get(brand.group_id)

    path = f"invoice/{invoice.id}"
    if store_id:
        path = f"s/{store_id}/{path}"

    url = brand.get_url(
        path, bot_id=bot_id,
        is_webview=True if bot_type == "telegram" else None,
    )

    if is_one_pmt_method:
        btn_text = await f(
            "invoice pay button one", lang,
            amount=format_currency(
                invoice.converted_sum_to_pay, invoice.currency, lang, group.country_code
            ),
        )
    else:
        btn_text = await f(
            "invoice pay web button", lang,
            amount=format_currency(
                invoice.converted_sum_to_pay, invoice.currency, lang, group.country_code
            ),
        )

    if bot_type == "whatsapp":
        return btn_text, url

    return InlineBtn(
        btn_text,
        web_app=types.WebAppInfo(
            url=url
        ),
    )


async def send_web_invoice(
        invoice: Invoice,
        bot: ClientBot,
        photo_url: str,
        msg_text: str,
        brand_id: int,
        store_id: int,
        lang: str,
        keyboard: InlineKb = None,
        is_one_pmt_method: bool = False,
):
    logger = JSONLogger(
        "send_web_invoice",
        {
            "invoice_id": invoice.id, "photo_url": photo_url, "brand_id": brand_id,
            "store_id": store_id
        }
    )

    user: User = await User.get_by_id(invoice.user_id)
    receiver_chat_id = user.chat_id if bot.bot_type == 'telegram' else user.wa_phone
    if not receiver_chat_id:
        raise Exception(f"User {user.id} has no bot for this profile business")

    bot = await ClientBot.get(invoice.payment_bot_menu_id or invoice.bot_id)

    if bot.bot_type == 'telegram':
        if not keyboard:
            keyboard = InlineKb().row()

        pay_button = await get_web_payment_button(
            invoice, brand_id,
            bot.id, store_id, lang, is_one_pmt_method, bot.bot_type
        )
        keyboard.row(pay_button)
    elif bot.bot_type == 'whatsapp':
        keyboard = None
        _, url_path = await get_web_payment_button(
            invoice, brand_id,
            bot.id, store_id, lang, is_one_pmt_method, bot.bot_type
        )
        msg_text += f"""\n\n
{await f("payments pay online link text", lang, link=url_path)}"""
        msg_text = html_to_markdown(msg_text)

    try:
        if bot.bot_type == 'telegram':
            msg = await send_tg_message(
                receiver_chat_id, "photo", bot_token=bot.token, keyboard=keyboard,
                caption=msg_text,
                photo=photo_url
            )
        elif bot.bot_type == 'whatsapp':
            msg = await send_wa_message(
                receiver_chat_id, "photo", bot.token, bot.whatsapp_from, text=msg_text,
                photo=photo_url
            )
    except Exception as err:
        logger.error(f'sender_bot.send_photo FAILED\n{str(err)}')
        if bot.bot_type == 'telegram':
            msg = await send_tg_message(
                receiver_chat_id, "text", bot_token=bot.token, keyboard=keyboard,
                text=msg_text
            )
        elif bot.bot_type == 'whatsapp':
            msg = await send_wa_message(
                receiver_chat_id, "text", bot.token, bot.whatsapp_from, text=msg_text
            )

    await crud.save_invoice_message_id(invoice.id, msg.message_id)

    return msg


async def get_data_from_callback(
        callback_query: types.CallbackQuery
) -> Tuple[User, str, str, Dict[str, Any]]:
    bot_id = ClientBot.get_current_bot_id()
    user = await User.get(callback_query.from_user.id)
    lang = await user.get_lang(bot_id)
    mode, callback_data = parse_callback_data(callback_query.data)
    return user, lang, mode, callback_data


async def send_change_lang_menu(message: types.Message, lang: str, mode: str = "new"):
    user = await User.get(message.chat.id)
    keyboard = InlineKb(row_width=3)
    bot = await ClientBot.get_current()
    langs_list = bot.group.get_langs_list() if bot else await localisation.langs
    for lang_var in langs_list:
        button_text = await Translator.get_language_name(lang, lang_var)
        if lang_var == await user.get_lang(bot.id):
            button_text = await active_button(lang, button_text)
        keyboard.insert(
            InlineBtn(button_text, callback_data=c("change_lang", lang=lang_var))
        )
    if "edit" in mode:
        await message.delete()
    await message.answer(await f("choose lang header", lang), reply_markup=keyboard)


async def send_user_link(receiver: User, user: User, lang: str):
    if user.username:
        url = f"https://t.me/{user.username}"
        keyboard = InlineKb().insert(
            InlineBtn(await f("username button", lang, username=user.username), url=url)
        )
        await send_tg_message(
            receiver.chat_id, "text",
            text=await f("username exists header", lang),
            reply_markup=keyboard
        )
    else:
        bot = await send_tg_user_link(receiver, user, lang)
        await send_tg_message(
            receiver.chat_id, "text",
            text=await f("link was sent", lang, bot_username=bot.username)
        )


async def send_tg_user_link(receiver: User, user: User, lang: str):
    link = f'<a href="tg://user?id={user.chat_id}">{user.name}</a>'

    bot = await crud.get_latest_bot_for_two_users(receiver.id, user.id)
    if not bot:
        raise exceptions.UnableToFindBotToSendUserLinkError()
    await send_tg_message(
        receiver.chat_id, "text",
        bot_token=bot.token,
        text=await f("user link message", lang, link=link),
    )
    return bot


def get_scan_qrcode_url(**params) -> str:
    text_params = "&".join([f"{k}={v}" for k, v in params.items()])
    scan_qrcode_url = QRCODE_SCANNER_URL + "/?"
    return scan_qrcode_url + text_params


async def process_update(
        dp: Dispatcher, update: types.Update, bot_id: int = None, token: str = None
):
    Dispatcher.set_current(dp)
    Bot.set_current(dp.bot)
    Router.set_current(dp["router"])
    with DBSession():
        if bot_id:
            ClientBot.set_current_bot_id(bot_id)
        try:
            if token:
                with dp.bot.with_token(token):
                    await dp.process_update(update)
            else:
                await dp.process_update(update)
        except Exception as e:
            JSONLogger(
                "bot.process-update", {
                    "bot": await ClientBot.get_current_bot_username(),
                    "update": update,
                    "bot_id": bot_id,
                    "token": token,
                }
            ).error("An error occurred while processing the update", e)


async def detect_bot_type_and_send_message(
        bot: ClientBot, receiver: User,
        content_type: str, *,
        keyboard: Keyboard | None = None,
        reply_to_message_id: int | str | None = None,
        **kwargs,
) -> Message:
    if bot.bot_type == "telegram":
        return await send_tg_message(
            receiver.chat_id, content_type, bot_token=bot.token, keyboard=keyboard,
            **kwargs
        )

    if bot.bot_type == "whatsapp":
        return await send_wa_message(
            receiver.wa_phone, content_type, bot.bot_type, bot.whatsapp_from,
            keyboard=keyboard, reply_to_message_id=reply_to_message_id, **kwargs
        )

    raise ValueError("Invalid bot type: %s" % bot.bot_type)


async def time_to_utc(
        target_time: time | None = None, group_id: int | None = None
) -> time:
    if not group_id or not target_time:
        return target_time or time(0, 0)

    timezone = await __get_timezone_by_group(group_id)
    input_datetime = timezone.localize(datetime.combine(date.today(), target_time))
    target_time = input_datetime.astimezone(pytz.utc).time()

    return target_time


def utc_time_to_local(
        target_time: time | None = None, timezone: str | None = None
) -> time:
    if not target_time:
        return target_time or time(0, 0)

    timezone = pytz.timezone(timezone or DEFAULT_TIME_ZONE)
    naive_datetime = datetime.combine(date.today(), target_time)
    localized_datetime = pytz.utc.localize(naive_datetime).astimezone(timezone)
    target_time = localized_datetime.time()

    return target_time


async def __get_timezone_by_group(group_id: int) -> pytz.timezone:
    group_timezone = None
    if group_id:
        group = await Group.get(id=group_id)
        if group:
            group_timezone = group.timezone
    if not group_timezone:
        group_timezone = DEFAULT_TIME_ZONE

    return pytz.timezone(group_timezone)


@db_func
def get_next_object_id(model: Type[DeclarativeMeta], profile_id: int) -> str:

    if not hasattr(model, 'brand'):
        raise Exception("Model does not have 'brand' attribute")

    if model == StoreProduct:
        model_object_id = StoreProduct.product_id
    elif model == StoreAttribute:
        model_object_id = StoreAttribute.attribute_id
    else:
        raise Exception("Model not supported")

    stmt = select(func.max(func.cast(model_object_id, Integer)))

    stmt = stmt.join(model.brand).join(Brand.group)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)

    # stmt = stmt.where(model_object_id.is_not(None))
    stmt = stmt.where(model.is_deleted.is_not(True))
    stmt = stmt.where(model_object_id.op('regexp')(r'^[0-9]+$'))

    max_id = sess().scalar(stmt)

    return str(int(max_id) + 1) if max_id is not None else '1'


async def check_incust_customer(bot: ClientBot, user: User, lang: str | None = None):
    brand = await Brand.get(group_id=bot.group_id)
    
    # Отримуємо налаштування лояльності
    loyalty_settings = await crud.get_loyalty_settings_for_context(
        "brand",
        schemas.LoyaltySettingsData(
            brand_id=brand.id,
            profile_id=bot.group_id,
        )
    ) if brand else None
    
    if loyalty_settings:
        incust_customer = await IncustCustomer.get(user_id=user.id, brand_id=brand.id)
        if not incust_customer:
            await get_or_create_incust_customer(user, loyalty_settings)


def get_crm_chat_link(chat_id: int):
    return f"{CRM_HOST}/chat/{chat_id}?listType=inbox&itemIdField=chatId"


async def create_url_prefix_from_text(
        text: str, lang: str, default_value: Any = None
) -> str | None:
    if not text:
        return str(default_value)

    result = text.strip()

    if not text.isascii():
        if lang in tl.get_available_language_codes():
            try:
                result = tl.translit(result, lang, reversed=True)
            except Exception:
                pass

        if not result.isascii():
            try:
                result = await tg(
                    text,
                    "en", "",
                    no_error_when_handler_set=False,
                    group_id="internal",
                )
            except Exception as e:
                logging.error(e, exc_info=True)
                result = None
    else:
        result = text

    if result:
        result = result.replace("-", "-")
        result = re.sub(r"\s", "_", result)
        result = "".join(re.findall(r"[\w_]", result))
        if result.startswith("_"):
            result = result[1:]
        return result

    elif default_value is not None:
        return str(default_value)

    return default_value


def calc_time_processed(
        start_time: datetime, end_time: datetime, lang: str
) -> str | None:
    time_processed = None
    if start_time and end_time:
        delta = end_time - start_time
        time_processed = interval_to_str(delta, lang)
    return time_processed
