import re
from datetime import <PERSON><PERSON><PERSON>
from typing import Any

import schemas
from core.chat.virtual_manager.messages_sender.functions import \
    replace_variables_in_text
from core.kafka.producer.functions import add_mailing_message_for_action
from db import crud
from db.models import ClientBot, Mailing, MailingMessage, MediaObject, WATemplate
from loggers import J<PERSON><PERSON>ogger
from schemas import MailingChannelTypeEnum
from utils.date_time import utcnow
from utils.platform_admins import send_message_to_platform_admins


class MailingService:
    worker_delay = 5

    def __init__(self, bot: ClientBot | None = None):
        self.bot = bot
        self.limit = 10
        self.hours_ago = 1
        self.logger = JSONLogger(
            "mailing"
        )

    async def add_mailing_message(
            self, mailing_message: schemas.MailingMessageQueryItem
    ):
        client_user = schemas.MailingUser(
            id=mailing_message.user_id,
            chat_id=mailing_message.chat_id,
            email=mailing_message.email,
            wa_phone=mailing_message.wa_phone,
            username=mailing_message.username,
            first_name=mailing_message.first_name,
            last_name=mailing_message.last_name,
            lang=mailing_message.lang,
            full_name=mailing_message.full_name
        )

        validated = self.__validate_user_for_channel(
            client_user, mailing_message.channel_type.value.lower(),
            mailing_message.bot_type,
        )
        if not validated:
            self.logger.error(
                "add_mailing_message_not_validated",
                {
                    "mailing_message_id": mailing_message.id,
                    "user_id": client_user.id,
                    "full_name": client_user.full_name,
                    "chat_id": client_user.chat_id,
                    "wa_phone": client_user.wa_phone,
                    "email": client_user.email,
                    "channel_type": mailing_message.channel_type.value.lower(),
                    "bot_type": mailing_message.bot_type,
                }
            )
            return

        return await add_mailing_message_for_action(
            data=schemas.MailingMessageValue(
                mailing_message=mailing_message.message_info["text"],
                mailing_id=mailing_message.mailing_id,
                mailing_message_id=mailing_message.id,
                subject=mailing_message.message_info.get("subject", ""),
                template_id=mailing_message.message_info.get("template_id", None),
                variables=mailing_message.message_info.get("variables", []),
            ),
            channel=mailing_message.channel_type.value.lower(),
            mailing_message=mailing_message,
        )

    async def create_mailing_messages(
            self, mailing: Mailing,
            test_user_ids: list[int] | None = None
    ) -> int:
        try:
            message_schemas = []
            channels_settings = mailing.channels_settings
            for channel in mailing.channels:
                contact_data_field = None
                if channel == "bot":
                    if self.bot and self.bot.bot_type == "whatsapp":
                        contact_data_field = "user.wa_phone"
                    elif self.bot and self.bot.bot_type == "telegram":
                        contact_data_field = "user.chat_id"
                elif channel == "email":
                    contact_data_field = "user.email"

                if not contact_data_field:
                    continue
                filters: list[schemas.FilterData] = [
                    schemas.FilterData(
                        type=schemas.FilterType.NOT_EMPTY,
                        field=contact_data_field,
                    ),
                ]

                if test_user_ids:
                    filters.append(
                        schemas.FilterData(
                            type=schemas.FilterType.ONE_OF,
                            field="user.id",
                            value=test_user_ids,
                        )
                    )

                extra_filters = {
                    "profile_id": mailing.group_id,
                    "is_marketing_consent": True,
                    "is_accept_agreement": True,
                }

                if channel == "bot" and self.bot:
                    extra_filters["bot_id"] = self.bot.id
                    extra_filters["is_entered_bot"] = True
                    extra_filters["is_bot_active"] = True
                    if (
                            channel == "bot" and
                            self.bot and
                            self.bot.bot_type == "whatsapp" and
                            not mailing.message_info.get("template_id", None)
                    ):
                        extra_filters["last_activity_gte"] = \
                            utcnow() - timedelta(hours=24)

                params = schemas.QueryBuilderParams(
                    fields=[
                        "user.id",
                        "user.chat_id",
                        "user.email",
                        "user.wa_phone",
                        "user.username",
                        "user.first_name",
                        "user.last_name",
                        "user.full_name",
                        "customer.lang",
                    ],
                    sort=[],
                    filters=filters,
                    cursor=None,
                    limit=500,
                )
                end_list = False
                cursor = None
                while not end_list:
                    params.cursor = cursor
                    users = await crud.customers.make_list_response(
                        params,
                        **extra_filters,
                    )

                    cursor = users.next
                    if not cursor:
                        end_list = True
                    for user in users.data:
                        validate_channel = self.__validate_by_channels_rules(
                            channels_settings, user, channel
                        )
                        if not validate_channel:
                            continue

                        data = await self.__process_client_message_data(
                            user, mailing, channel
                        )
                        if data:
                            message_schemas.append(data)

            await crud.create_mailing_messages(message_schemas)

            return len(message_schemas)
        except Exception as ex:
            self.logger.error("create_mailing_messages", repr(ex))

    async def format_message(self, client: schemas.MailingUser, message_info: dict):
        template_id = None
        if message_info.get("template_id", None):
            template_id = message_info.get("template_id")
        msg = await self.__format_variables(
            client,
            message_info.get("text", ""),
            message_info,
            template_id
        )

        return msg

    @staticmethod
    async def get_media_data_for_bot(
            media_id: int,
            mailing_media_type: str,
            bot_type: str,
    ) -> tuple[str | None, str | None]:
        media = await MediaObject.get(media_id)
        if media:
            if ".gif" in media.url:
                if bot_type == "whatsapp":
                    return "document", media.url
                elif bot_type == "telegram":
                    return "video", media.url

            if mailing_media_type == "image":
                return "photo", media.url
            elif mailing_media_type == "video":
                if bot_type == "whatsapp":
                    return "document", media.url
                elif bot_type == "telegram":
                    return "video", media.url
            elif mailing_media_type == "document":
                return "document", media.url
        return None, None

    @staticmethod
    def __validate_by_channels_rules(
            channels_settings: dict | None, user: schemas.MailingUser,
            channel: str
    ):
        if not channels_settings:
            return True
        if channels_settings.get("only_channels", None):
            only_channel = channels_settings.get("only_channels")[0]

            if only_channel == "email" and channel != "email" and (user.chat_id or
                                                                   user.wa_phone):
                return False
            if only_channel == "bot" and channel != "bot" and user.email:
                return False

        return True

    async def __process_client_message_data(
            self,
            client: schemas.MailingUser,
            mailing: Mailing,
            channel: str,
    ) -> schemas.CreateMailingMessageSchema | None:
        try:
            validated = self.__validate_user_for_channel(
                client, channel, None if not self.bot else self.bot.bot_type
            )
            if not validated:
                return None

            exist = await MailingMessage.get(
                bot_id=None if not self.bot else self.bot.id,
                channel_type=channel.upper(), user_id=client.id,
                mailing_id=mailing.id,
            )

            if not exist:
                template_id = None
                if mailing.message_info.get("template_id", None):
                    template_id = mailing.message_info.get("template_id")
                msg = await self.__format_variables(
                    client,
                    mailing.message_info.get("text", "test"),
                    mailing.message_info,
                    template_id
                )

                mailing.message_info["text"] = msg
                data = schemas.CreateMailingMessageSchema(
                    bot_id=None if not self.bot else self.bot.id,
                    message=mailing.message_info,
                    user_id=client.id,
                    channel_type=MailingChannelTypeEnum(channel),
                    channel_name=(
                        self.bot.bot_type
                        if self.bot and channel == "bot"
                        else channel
                    ),
                    email=client.email if channel == "email" else None,
                    phone=client.wa_phone if channel == "bot" else None,
                    chat_id=client.chat_id if channel == "bot" else None,
                    mailing_id=mailing.id,
                    user_name=client.username if channel == "bot" else None,
                    lang=client.customer.lang if client.customer else client.lang,
                )

                return data
        except Exception as ex:
            self.logger.error("__process_client_message_data", repr(ex))

    async def process_sent_message(
            self,
            mailing_message_id: int,
            mailing_id: int,
            retry_info: dict | None = None,
            error: Exception | str | None = None,
            debug_data: Any | None = None,
    ):
        status = None

        try:
            if error:
                status = schemas.MailingMessageStatusEnum.FAILED
                error_details = {
                    "error_details": (
                        repr(error)
                        if isinstance(error, Exception)
                        else error
                    )
                }
            else:
                status = schemas.MailingMessageStatusEnum.PROCESSED
                error_details = None

            await crud.update_mailing_message_status(
                mailing_message_id,
                mailing_id,
                status, retry_info, error_details
            )
        except Exception as ex:
            await send_message_to_platform_admins(
                "An error occurred while process_sent_message\n"
                f"Mailing id: {mailing_id}\n"
                f"Mailing message id: {mailing_message_id}\n"
                f"Status: {status}\n"
                f"Error: "
                f"{repr(error) if isinstance(error, Exception) else error or '-'}"
            )
            self.logger.error(
                "process_sent_message", repr(ex), {
                    "data": debug_data
                }
            )

    @staticmethod
    def parse_text_for_bot_message(text: str):
        text = re.sub(r'<p[^>]*>', '\n', text).replace('</p>', '')
        text = re.sub(r'<div[^>]*>', '\n', text).replace('</div>', '')
        text = re.sub(r"<br[^<>]*>", "\n", text)
        text = (text.replace('<br>', '\n')
                .replace('<br/>', '\n')
                .replace('<br />', '\n')
                .replace('</br>', '\n'))

        return text

    @staticmethod
    async def update_mailings_state():
        mailings_pending = await Mailing.get_list(
            status=schemas.MailingStatusEnum.PENDING
        )
        mailings_created = await Mailing.get_list(
            status=schemas.MailingStatusEnum.CREATED
        )
        mailings = [*mailings_pending, *mailings_created]

        for mailing in mailings:
            failed = await crud.get_mailing_messages_count_by_status(
                schemas.MailingMessageStatusEnum.FAILED,
                mailing.id
            )
            sent = await crud.get_mailing_messages_count_by_status(
                schemas.MailingMessageStatusEnum.PROCESSED,
                mailing.id
            )
            if not mailing.sent_info:
                await mailing.update(status=schemas.MailingStatusEnum.CANCELED)
            else:
                data_to_update = {
                    "sent_info": {
                        **mailing.sent_info.dict(),
                        "total_failed": failed,
                        "total_sent": sent,
                    }
                }

                if mailing.sent_info.total == sent + failed:
                    data_to_update["status"] = schemas.MailingStatusEnum.FINISHED

                await mailing.update(data_to_update)

    @staticmethod
    def __validate_user_for_channel(
            user: schemas.MailingUser,
            channel: str,
            bot_type: str | None = None,
    ) -> bool:
        if bot_type and channel == "bot":
            if bot_type == "whatsapp":
                if user.wa_phone:
                    return True
            elif bot_type == "telegram":
                if user.chat_id:
                    return True
        elif channel == "email":
            if user.email:
                return True

        return False

    async def __format_variables(
            self, user: schemas.MailingUser, msg: str, msg_info: dict,
            template_id: int | None = None
    ):
        if template_id:
            template = await WATemplate.get(template_id)
            if template:
                msg = template.template_data.get()
            for var in msg_info.get("variables", []):
                if var.get("key", "") == '1':
                    msg = msg.replace(
                        f"{{{{{var.get('key', '')}}}}}", var.get("value", ""), 1
                    )
                else:
                    msg = msg.replace(
                        f"{{{{{var.get('key', '')}}}}}", var.get("value", ""),
                    )

        msg = self.__replace_user_variables(user, msg)

        return msg

    @staticmethod
    def __replace_user_variables(user: schemas.MailingUser, text: str):
        variables = {
            "firstname": user.first_name,
            "lastname": user.last_name,
            "fullname": user.full_name,
            "username": f"@{user.username}" if user.username else user.full_name,
        }
        return replace_variables_in_text(text, variables)
