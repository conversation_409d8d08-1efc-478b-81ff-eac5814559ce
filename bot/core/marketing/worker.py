import asyncio
import traceback

import schemas
from db import <PERSON><PERSON>ession, crud
from loggers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.date_time import utcnow
from utils.platform_admins import send_message_to_platform_admins
from utils.processes_manager.background_worker import LoopBackgroundWorker
from .service import MailingService

logger = J<PERSON>NLogger("mailing.worker")


class MailingWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "Mailing service"
    DEFAULT_TIMEOUT = MailingService.worker_delay

    async def iteration(self):
        with DBSession():
            try:
                params = schemas.QueryBuilderParams(
                    fields=[
                        "mailing_message.id",
                        "mailing_message.email",
                        "mailing_message.phone",
                        "mailing_message.chat_id",
                        "mailing_message.user_name",
                        "mailing_message.message",
                        "mailing_message.channel_type",
                        "mailing_message.status",
                        "mailing_message.lang",
                        "mailing_message.mailing_id",
                        "mailing_message.bot_id",
                        "mailing_message.user_id",
                        "user.first_name",
                        "user.last_name",
                        "user.full_name",
                        "client_bot.bot_type",
                        "client_bot.display_name",
                        "client_bot.token",
                        "client_bot.whatsapp_from",
                        "mailing.message_info",
                        "group.name",
                    ],
                    sort=[],
                    filters=[
                        schemas.FilterData(
                            type=schemas.FilterType.EQUAL,
                            field="mailing_message.status",
                            value="CREATED",
                        ),
                    ],
                    cursor=None,
                    limit=500,
                )
                end_list = False
                cursor = None
                service = MailingService()
                await service.update_mailings_state()

                mailing_ids = []
                message_ids = []
                all_messages: list[schemas.MailingMessageQueryItemResult] = []

                coros = []

                try:
                    while not end_list:
                        params.cursor = cursor
                        messages = await crud.mailing_messages.make_list_response(
                            params
                        )

                        cursor = messages.next
                        if not cursor:
                            end_list = True
                        for message in messages.data:
                            mailing_message_schema = schemas.MailingMessageQueryItem(
                                **message.dict(
                                    exclude={"bot", "user", "mailing", "group"}
                                ),
                                **message.user.dict(exclude={"wa_phone", "username"}),
                                **message.client_bot.dict(),
                                message_info=message.mailing.message_info,
                                wa_phone=message.phone,
                                username=message.user_name,
                                profile_name=message.group.name,
                            )

                            coro = await service.add_mailing_message(
                                mailing_message_schema
                            )
                            if coro:
                                if message.mailing_id not in mailing_ids:
                                    mailing_ids.append(message.mailing_id)
                                coros.append(coro)
                                message_ids.append(message.id)

                                all_messages.append(message)
                finally:
                    if coros:
                        results = await asyncio.gather(*coros, return_exceptions=True)

                        error_indexes = [
                            i for i, result in enumerate(results) if
                            isinstance(result, Exception) or result is None
                        ]

                        mailing_ids = []
                        message_ids = []

                        for i, message in enumerate(all_messages):
                            if i not in error_indexes:
                                if message.mailing_id not in mailing_ids:
                                    mailing_ids.append(message.mailing_id)
                                message_ids.append(message.id)

                        if mailing_ids:
                            await crud.mass_update_mailings_status(
                                status="PENDING", mailing_ids=mailing_ids
                            )
                        if message_ids:
                            await crud.mass_update_mailing_messages_status(
                                status="PENDING",
                                start_date=utcnow(),
                                mailing_messages_ids=message_ids,
                            )
                        if error_indexes:
                            failed_messages_ids = [
                                all_messages[error_index].id for
                                error_index in error_indexes
                            ]

                            await crud.mass_update_mailing_messages_status(
                                status="FAILED",
                                start_date=utcnow(),
                                mailing_messages_ids=failed_messages_ids,
                            )

                            failed_messages = [
                                {
                                    "message": all_messages[error_index],
                                    "error": repr(results[error_index]) if results[error_index] else "Not validated",
                                    "traceback": (
                                        "".join(
                                            traceback.format_tb(
                                                getattr(
                                                    results[error_index],
                                                    "__traceback__", None,
                                                )
                                            )
                                        )
                                    ) if results[error_index] else "Not validated"
                                }
                                for error_index in error_indexes
                            ]
                            logger.error(
                                "Sending messages to kafka failed", {
                                    "failed_messages": failed_messages
                                }
                            )
                            await send_message_to_platform_admins(
                                f"Failed to add {len(failed_messages)} mailing "
                                f"messages to kafka"
                            )
            except Exception as error:
                logger.error(error)
