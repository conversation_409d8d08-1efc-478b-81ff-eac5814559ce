from core.helpers import create_url_prefix_from_text
from core.payment.funcs import create_cash_for_brand_if_not_exist
from db import crud
from db.models import Brand, Group, User


async def auto_create_brand(group: Group) -> Brand:
    group_owner = await User.get_by_id(group.owner_id)
    domain_prefix = await create_url_prefix_from_text(group.name, group_owner.lang, group_owner.username or group.id)
    brand = await crud.auto_create_brand(group, domain_prefix)
    await create_cash_for_brand_if_not_exist(brand_id=brand.id)

    return brand
