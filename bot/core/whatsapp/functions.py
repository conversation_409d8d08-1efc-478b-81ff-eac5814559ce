import asyncio

from aiowhatsapp import types
from aiowhatsapp.types import ListKeyboard

from core.whatsapp.keyboards import get_wa_menu_keyboard
from db.models import ClientBot, MenuInStore, User
from utils.message import send_wa_message
from utils.text import f


async def send_whatsapp_menu_message(
        answer_obj: types.message.AnswerObject,
        user: User,
        bot: ClientBot,
        lang: str,
        menu_in_store: MenuInStore | None = None,
        text: str | None = None,
        wait_time: int | None = None,
):
    if text is None:
        text = await f("main menu button", lang)
    keyboard = await get_wa_menu_keyboard(user, bot, lang, menu_in_store)
    if wait_time:
        await asyncio.sleep(wait_time)
    return await answer_obj.answer(text, keyboard)


async def send_delayed_wa_menu(
        bot_token: str, lang: str, to: str, wa_keyboard: ListKeyboard, whatsapp_from: str,
        wait_time: int
):
    await asyncio.sleep(wait_time)
    await send_wa_message(
        to, "text",
        bot_token, whatsapp_from,
        text=await f("main menu button", lang=lang),
        keyboard=wa_keyboard,
    )
