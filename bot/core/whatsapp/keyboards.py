import asyncio
import logging

from db.models import ClientBot, MenuInStore, User, UserClientBotActivity
from utils.message import send_wa_message
from utils.text import f
from .menu_keyboard_builder.builder import MenuKeyboardBuilder
from ..menu_in_store.keyboards import get_wa_menu_in_store_keyboard


async def get_wa_menu_keyboard(
        user: User, bot: ClientBot, lang: str, menu_in_store: MenuInStore | None = None
):
    if bot.bot_type != "whatsapp":
        return
    if not menu_in_store:
        user_client_bot_activity = await UserClientBotActivity.get(user, bot)
        if user_client_bot_activity.active_menu_in_store_id:
            menu_in_store = await MenuInStore.get(
                user_client_bot_activity.active_menu_in_store_id
            )

    if menu_in_store:
        return await get_wa_menu_in_store_keyboard(menu_in_store, bot, user, lang)

    builder = MenuKeyboardBuilder(user, bot, lang)
    return await builder.build()


async def send_wa_main_menu(
        group_bot: ClientBot, lang: str, user: User, wait_time: int | None = None
):
    if group_bot.bot_type != "whatsapp":
        return

    debugger = logging.getLogger('debugger.send_wa_main_menu')
    if not any([group_bot, user, lang]):
        debugger.debug(f"not enough conditions: {group_bot=}, {user=}, {lang=}")
        return

    to = user.wa_phone
    bot_token = group_bot.token
    whatsapp_from = group_bot.whatsapp_from

    if not any([to, bot_token, whatsapp_from]):
        debugger.debug(f"not enough conditions: {to=}, {bot_token=}, {whatsapp_from=}")
        return

    keyboard = await get_wa_menu_keyboard(user, group_bot, lang)

    if wait_time:
        await asyncio.sleep(wait_time)

    await send_wa_message(
        to, "text",
        bot_token, whatsapp_from,
        text=await f("main menu button", lang=lang),
        keyboard=keyboard,
    )
    debugger.debug("wa main menu send Ok")
