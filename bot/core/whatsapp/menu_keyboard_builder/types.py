from collections import defaultdict
from dataclasses import dataclass, field, fields
from typing import Iterable

from aiowhatsapp import types


@dataclass
class MenuKeyboardFromMainSection:
    before: list[types.Section] = field(default_factory=list)
    after: list[types.Section] = field(default_factory=list)


@dataclass
class MenuKeyboardAdditionalSectionsType:
    start: list[types.Section] = field(default_factory=list)
    end: list[types.Section] = field(default_factory=list)
    from_main_sections: defaultdict[str, MenuKeyboardFromMainSection] = field(
        default_factory=lambda: defaultdict(MenuKeyboardFromMainSection)
    )


@dataclass
class MenuKeyboardConf:
    shop: bool = True
    chat: bool = True
    profile: bool = True
    back_to_main_menu_button: bool = False


@dataclass
class MenuKeyboardSections:
    shop: types.Section | None = None
    extra: types.Section | None = None
    chat: types.Section | None = None
    profile: types.Section | None = None

    def iterate_items(self) -> Iterable[tuple[str, types.Section | None]]:
        for field_obj in fields(self):
            yield field_obj.name, getattr(self, field_obj.name)
