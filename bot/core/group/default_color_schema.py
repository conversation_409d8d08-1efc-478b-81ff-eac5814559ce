from dataclasses import dataclass
from db.models import ColorSchema
import schemas


@dataclass
class DefaultGroupColorSchemaLight:
    bg_color: str = "#fff"
    secondary_bg_color: str = "#fff"
    primary_color: str = "#1976d2"
    secondary_color: str = "#9c27b0"
    text_color: str = "#000000de"
    warning_color: str = "#ed6c02"
    error_color: str = "#d32f2f"


@dataclass
class DefaultGroupColorSchemaDark:
    bg_color: str = "#121212"
    secondary_bg_color: str = "#121212"
    primary_color: str = "#90caf9"
    secondary_color: str = "#ce93d8"
    text_color: str = "#fff"
    warning_color: str = "#ffa726"
    error_color: str = "#f44336"


def get_group_color_schema_with_defaults(color_schema: ColorSchema | None) -> schemas.AppearanceColorSchema:
    if not color_schema:
        default_schema = DefaultGroupColorSchemaLight
    else:
        default_schema = DefaultGroupColorSchemaDark if color_schema.theme_mode == "dark" else (
            DefaultGroupColorSchemaLight)

    schema = schemas.AppearanceColorSchema(
        theme_mode=color_schema.theme_mode if color_schema and color_schema.theme_mode else "light",
        bg_color=color_schema.bg_color if color_schema and color_schema.bg_color else default_schema.bg_color,
        secondary_bg_color=color_schema.secondary_bg_color if color_schema and color_schema.secondary_bg_color else (
            default_schema.secondary_bg_color),
        primary_color=color_schema.primary_color if color_schema and color_schema.primary_color else (
            default_schema.primary_color),
        secondary_color=color_schema.secondary_color if color_schema and color_schema.secondary_color else (
            default_schema.secondary_color),
        text_color=color_schema.text_color if color_schema and color_schema.text_color else default_schema.text_color,
        warning_color=color_schema.warning_color if color_schema and color_schema.warning_color else (
            default_schema.warning_color),
        error_color=color_schema.error_color if color_schema and color_schema.error_color else (
            default_schema.error_color),
        font=color_schema.font if color_schema else None,
        use_telegram_theme=color_schema.use_telegram_theme if color_schema else True,
        is_active=color_schema.is_active if color_schema else True,
    )

    return schema
