import asyncio
import html

from aiogram import Bo<PERSON>

import schemas
from db import crud
from db.models import Brand, ClientBot, ColorSchema, CustomField, Group, User
from utils.text import f
from .default_color_schema import get_group_color_schema_with_defaults
from ..custom_texts.models.group import GroupCustomTextsModel
from ..invoice.functions import get_payment_methods_info


async def group_to_schema(group: Group, lang: str) -> schemas.GroupSchema:
    ct_obj = await GroupCustomTextsModel.from_object(group)
    texts_dict = await ct_obj.to_dict(lang)

    return schemas.GroupSchema(
        id=group.id,
        name=group.name,
        timezone=group.timezone,
        is_bot_connected=bool(await ClientBot.get(group_id=group.id)),
        is_payments_available=await is_group_payments_available(group),
        lang=group.lang,
        country_code=group.country_code,
        texts=texts_dict,
        is_ask_about_birthday=group.is_ask_about_birthday,
    )


async def is_group_payments_available(group: Group):
    if group.currency:
        payment_methods_info = await get_payment_methods_info(group.id)
        return bool(
            payment_methods_info.tg_token or payment_methods_info.payment_methods
        )

    return False


async def change_group_owner(
        group: Group,
        new_owner: User,
):
    old_owner = await crud.change_group_owner(group, new_owner)

    lang = await User.get_lang(old_owner.chat_id)
    text = await f(
        "new group owner notification", lang,
        group_name=group.name,
        user_fullname=new_owner.full_name
    )

    bot = Bot.get_current()
    asyncio.ensure_future(bot.send_message(old_owner.chat_id, text))
    return True


async def appearance_to_schema(
        group_id: int, brand: Brand | None = None
) -> schemas.AppearanceSettings:
    color_schema_db = await ColorSchema.get(group_id=group_id)
    color_schema = get_group_color_schema_with_defaults(color_schema_db)

    if not brand:
        brand = await Brand.get(group_id=group_id)

    return schemas.AppearanceSettings(
        product_image_aspect_ratio=brand.product_image_aspect_ratio,
        image_height=brand.image_height,
        desktop_view=brand.desktop_view,
        mobile_view=brand.mobile_view,
        is_categories_count_view=brand.is_categories_count_view,
        mobile_cart_button_mode=brand.mobile_cart_button_mode,
        product_image_aspect_ratio_converted=brand.product_image_aspect_ratio_converted,
        banner_url=brand.image_media.url if brand.image_media else None,
        logo_url=brand.logo_media.url if brand.logo_media else None,
        show_more_infinite=brand.show_more_infinite,
        products_limit=brand.products_limit,
        is_filter_search=brand.is_filter_search,
        is_filter_sort=brand.is_filter_sort,
        is_filter_by_price=brand.is_filter_by_price,
        thumbnails_mode=brand.thumbnails_mode,
        thumbnail_size=brand.thumbnail_size,
        **color_schema.dict(),
    )


async def get_custom_fields(user: User, group: Group, lang: str):
    fields = await CustomField.get_list_for_group(group.id)

    if not fields:
        return ""

    custom_fields = []

    for field in fields:
        value = await field.get_last_value(user.id, none_as_empty_str=False)

        if value is None:
            continue

        value = html.escape(value)
        custom_fields.append(
            await f("custom field value", lang, field_name=field.name, value=value)
        )

    return await f("custom fields text", lang, fields="\n".join(custom_fields))
