from abc import ABC

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BaseScannerServiceError(ErrorWithHTTPStatus, ABC, base=True):
    def __init__(self, err_message: str = "ScannerService error", **kwargs):
        self.message = err_message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"ScannerService error: {self.message}"


class ScannerServiceNoReceiptError(BaseScannerServiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand scan receipts service no receipt error"


class ScannerServiceNoReceiptAndReceiptIdError(BaseScannerServiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand scan receipts service no receipt or id error"


class ScannerServiceInvalidBinCodeError(BaseScannerServiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand scan receipts service bin code error"


class ScannerServiceInvalidCountryError(BaseScannerServiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand scan receipts service country error"


class ScannerServiceReceiptExistError(BaseScannerServiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand scan receipts service receipt exist error"


class ScannerServiceReceiptCreateError(BaseScannerServiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand scan receipts service create receipt error"


class ScannerServiceReceiptLoyaltyError(BaseScannerServiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand scan receipts service loyalty error"


class ScannerServiceReceiptFinanceSystemError(BaseScannerServiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand scan receipts service fin system error"


class ScannerServiceReceiptProcessCheckError(BaseScannerServiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand scan receipts service process check error"


class ScannerServiceScanError(BaseScannerServiceError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand scan receipts scanner error"

    def __init__(self, err_msg: str):
        super().__init__(
            "store brand scan receipts scanner error",
            msg=err_msg,
        )
