from typing import Any

from fastapi.exceptions import HTTPException, RequestValidationError
from pydantic import ValidationError
from starlette import status

import schemas
from core.user_data.validators.base import UserDataValidator
from db.models import UserData


def user_data_to_schema(user_data: UserData):
    return schemas.UserDataDefaultSchema.from_orm(user_data)


def user_data_list_to_schemas(user_data_list: list[UserData]):
    return list(map(user_data_to_schema, user_data_list))


def validate_user_data(
        type: str,
        data: Any,
        loc: tuple[str, ...] | None = None,
        is_request: bool = True,
        current_data: Any | None = None,
        partial: bool = False,
):
    if is_request and not loc:
        loc = tuple()

    if partial:
        if not isinstance(data, dict):
            message = "Data must be a dict for partial update"
            if is_request:
                raise RequestValidationError(
                    [
                        {
                            "msg": message,
                            "type": "invalid_type",
                            "loc": loc,
                        }
                    ]
                )
            raise ValueError(message)
        if not isinstance(current_data, dict):
            message = "Current data must be a dict for partial update"
            if is_request:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=message,
                )
            raise ValueError(message)

        data = {
            **current_data,
            **data,
        }

    validator_cls = UserDataValidator.detect(type, no_error=True)
    if not validator_cls:
        return data

    validator = validator_cls(data)
    try:
        return validator.validate_data()
    except ValidationError as e:
        if is_request:
            raise RequestValidationError(
                [
                    {**err, "loc": loc + err.get("loc", ())}
                    for err in e.errors()
                ]
            )
        raise
