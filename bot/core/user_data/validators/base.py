from abc import ABC, abstractmethod
from typing import Any, Generic, Type

from pydantic import BaseModel
from typing_extensions import TypeVar

ValidatedDataT = TypeVar("ValidatedDataT", default=Any)


class UserDataValidator(ABC, Generic[ValidatedDataT]):
    type: str
    _validators: dict[str, Type["UserDataValidator"]] = {}

    def __init_subclass__(cls, **kwargs):
        skip: bool = kwargs.pop("skip", False)
        if not skip:
            cls._validators[cls.type] = cls

    def __init__(self, data: Any):
        self.data = data

    @abstractmethod
    def validate_data(self) -> ValidatedDataT:
        raise NotImplementedError

    @classmethod
    def detect(cls, type: str, no_error: bool = False):
        if no_error and type not in cls._validators:
            return None
        return cls._validators[type]


PydanticDataT = TypeVar("PydanticDataT", bound=BaseModel)


class UserDataPydanticValidator(
    Generic[PydanticDataT],
    UserDataValidator[PydanticDataT],
    skip=True,
):
    model: Type[PydanticDataT]

    def validate_data(self) -> ValidatedDataT:
        return self.model.parse_obj(self.data).dict()
