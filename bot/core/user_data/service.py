import exceptions
import schemas
from core.exceptions import GroupNotFoundByIdError
from core.user_data.functions import validate_user_data
from db import crud
from db.models import Group, User, UserData


class UserDataService:
    def __init__(
            self, user: User | int,
    ):
        if isinstance(user, int):
            self.user_id: int = user
            self._user = None
        else:
            self.user_id: int = user.id
            self._user = user

    @property
    async def user(self):
        if self._user is None:
            self._user = await User.get_by_id(self.user_id)
        return self._user

    @classmethod
    async def validate_target_data(cls, data: schemas.UserDataTargetData):
        match data.target:
            case schemas.UserDataTarget.GROUP:
                group = await Group.get(data.group_id)
                if not group:
                    raise GroupNotFoundByIdError(data.group_id)

                return {"group": group}
            case _:
                raise ValueError("Invalid target")

    async def create_user_data(
            self,
            data: schemas.CreateUserDataData,
            loc: tuple[str, ...]
    ):
        target_data = await self.validate_target_data(data)
        data.data = validate_user_data(data.type, data.data, loc)
        user_data = await UserData.create(
            user_id=self.user_id,
            user=self._user,
            position=0,
            **data.dict(),
            **target_data,
        )
        await self.move_user_data(
            user_data.id, schemas.MoveUserDataData(
                type=data.type,
                target=data.target,
                group_id=data.group_id,
                new_position=0,
            )
        )
        return user_data

    async def get_user_data_by_id(self, user_data_id: int):
        user_data = await UserData.get(
            id=user_data_id,
            user_id=self.user_id,
        )
        if not user_data:
            raise exceptions.UserDataNotFoundByIdError(user_data_id)
        return user_data

    async def update_user_data(
            self,
            user_data: UserData,
            data: schemas.UpdateUserDataByIdData,
            loc: tuple[str, ...]
    ):
        new_data = validate_user_data(
            user_data.type,
            data.new_data, loc,
            True, user_data.data, data.partial
        )
        return await user_data.update(data=new_data)

    async def get_user_data_list(self, params: schemas.UserDataListParams):
        return await UserData.get_list(
            user_id=self.user_id,
            **params.dict(exclude_none=True)
        )

    async def delete_user_data_by_target_and_type(
            self,
            target: schemas.UserDataTarget,
            target_id: int,
            type: str
    ):
        await crud.delete_user_data_by_target_and_type(
            self.user_id, target, target_id, type
        )

    async def move_user_data(self, user_data_id: int, data: schemas.MoveUserDataData):
        await crud.reorder_object(
            UserData,
            user_data_id,
            data.new_position,
            user_id=self.user_id,
            **data.dict(exclude={"new_position"}, exclude_none=True)
        )
