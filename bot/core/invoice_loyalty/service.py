"""
Сервіс для роботи з лояльністю Invoice використовуючи нові InCust API клієнти
"""

import logging
from typing import Optional, TYPE_CHECKING

from incust_api.api import term
from incust_terminal_api_client import TransactionCancelRequest

from core.loyalty.incust_api import incust
from db.models import LoyaltySettings, User
from db.models.finances.invoice import Invoice

if TYPE_CHECKING:
    pass

logger = logging.getLogger('debugger.invoice_loyalty')


def remove_none(data):
    if isinstance(data, dict):
        return {
            k: remove_none(v)
            for k, v in data.items()
            if v is not None and remove_none(v) is not None
        }
    elif isinstance(data, list):
        cleaned_list = [remove_none(item) for item in data]
        return [item for item in cleaned_list if item is not None]
    else:
        return data


class InvoiceLoyaltyService:
    """Сервіс для роботи з лояльністю Invoice"""

    async def reserve_invoice_loyalty(
            self,
            invoice: Invoice,
            loyalty_settings: LoyaltySettings,
            incust_check: dict,
    ) -> Optional[str]:
        """
        Резервування транзакції лояльності для Invoice

        Returns:
            Optional[str]: ID зарезервованої транзакції або None при помилці
        """
        try:

            if not loyalty_settings:
                logger.warning(f"Немає налаштувань лояльності для invoice {invoice.id}")
                return None

            # Перевіряємо наявність incust_check (тепер це єдина умова для резервування)
            if not incust_check:
                logger.warning(f"Немає incust_check для invoice {invoice.id}")
                return None

            check = term.m.Check(**remove_none(incust_check))

            async with incust.term.CheckTransactionsApi(loyalty_settings) as api:
                response = await api.reserve_check(check)

            if isinstance(response, term.m.Transaction):
                transaction_id = response.id

                # Витягуємо дані з incust_check для заповнення структурованих полів
                bonuses_added = int(
                    (incust_check.get('bonuses_added_amount', 0) or 0) * 100
                )  # конвертуємо в копійки
                discount_amount = int(
                    (incust_check.get('discount_amount', 0) or 0) * 100
                )  # конвертуємо в копійки
                amount = int(
                    (incust_check.get('amount', 0) or 0) * 100
                )  # конвертуємо в копійки
                amount_to_pay = int(
                    (incust_check.get('amount_to_pay', 0) or 0) * 100
                )  # конвертуємо в копійки
                # Обробляємо дані купонів
                coupons_data = []
                emitted_coupons = incust_check.get('emitted_coupons', [])
                if emitted_coupons:
                    for coupon in emitted_coupons:
                        # InCust може повертати лише код серії без ID конкретного купона
                        coupon_data = {
                            'id': coupon.get('id') if isinstance(coupon, dict) and coupon.get('id') else None,
                            'code': coupon.get('code') if isinstance(coupon, dict) else str(coupon),
                            'title': coupon.get('title', '') if isinstance(coupon, dict) else '',
                            'description': coupon.get('description', '') if isinstance(coupon, dict) else '',
                            'type': coupon.get('type') if isinstance(coupon, dict) else None,
                            'image': coupon.get('image') if isinstance(coupon, dict) else None,
                            'share_allowed': coupon.get('share_allowed', 0) if isinstance(coupon, dict) else 0,
                            'batch': coupon.get('batch') if isinstance(coupon, dict) else None,
                        }
                        coupons_data.append(coupon_data)
                special_accounts_charges = incust_check.get('special_accounts_charges', [])
                skip_message = incust_check.get('skip_message', False)
                transaction_data = incust_check.get('transaction', {})
                implemented_rules = incust_check.get('implemented_rules', [])

                # Зберігаємо всі дані лояльності
                await invoice.update(
                    incust_transaction_id=str(transaction_id),
                    loyalty_settings_id=loyalty_settings.id,
                    bonuses_added_amount=bonuses_added,
                    loyalty_discount_amount=discount_amount,
                    loyalty_amount=amount,
                    loyalty_amount_to_pay=amount_to_pay,
                    loyalty_coupons_data=coupons_data,
                    special_accounts_charges=special_accounts_charges,
                    loyalty_skip_message=skip_message if skip_message is not None
                    else False,
                    loyalty_transaction_data=transaction_data,
                    loyalty_implemented_rules=implemented_rules,
                )

                logger.info(
                    f"Зарезервовано транзакцію {transaction_id} для invoice "
                    f"{invoice.id} з loyalty_settings {loyalty_settings.id}"
                )
                return str(transaction_id)
            elif isinstance(response, term.m.PinConfirmationResponse):
                logger.warning(f"Потрібне PIN підтвердження для invoice {invoice.id}")
                return None
            else:
                logger.error(
                    f"Невідома відповідь при резервуванні для invoice {invoice.id}"
                )
                return None

        except Exception as e:
            logger.error(
                f"Помилка резервування лояльності для invoice {invoice.id}: {e}",
                exc_info=True
            )
            return None

    async def finalize_invoice_loyalty(
            self,
            invoice: Invoice,
            is_cancel: bool = False,
    ) -> bool:
        """
        Фіналізація транзакції лояльності для Invoice
        """
        try:
            # Перевіряємо наявність transaction_id
            if not invoice.incust_transaction_id:
                logger.warning(f"Немає incust_transaction_id для invoice {invoice.id}")
                return False

            # Використовуємо збережений loyalty_settings_id якщо є
            loyalty_settings = None
            if invoice.loyalty_settings_id:
                loyalty_settings = await LoyaltySettings.get(
                    invoice.loyalty_settings_id
                )

            if not loyalty_settings:
                logger.warning(f"Немає налаштувань лояльності для invoice {invoice.id}")
                return False

            # Фіналізуємо або скасовуємо транзакцію
            async with incust.term.CheckTransactionsApi(loyalty_settings) as api:
                if is_cancel:
                    # Скасовуємо транзакцію
                    response = await api.transaction_cancel(
                        TransactionCancelRequest(
                            transaction_id=str(invoice.incust_transaction_id)
                        )
                    )
                else:
                    # Фіналізуємо транзакцію
                    finalize_body = term.m.FinalizeCheckRequest(
                        id=str(invoice.incust_transaction_id),
                        comment=f"Invoice {invoice.id} - paid"
                    )
                    response = await api.finalize_check(finalize_body)

            if response and response.finalized:
                # Якщо транзакція скасована, очищуємо дані лояльності
                if is_cancel:
                    await invoice.update(
                        bonuses_added_amount=0,
                        loyalty_discount_amount=0,
                        loyalty_amount=0,
                        loyalty_amount_to_pay=0,
                        loyalty_coupons_data=None,
                        special_accounts_charges=None,
                        loyalty_skip_message=False,
                        loyalty_transaction_data=None,
                    )
                    logger.info(
                        f"Скасовано та очищено дані лояльності для invoice {invoice.id}"
                    )
                else:
                    # При успішній фіналізації отримуємо актуальні emitted_coupons з фінальної транзакції
                    try:
                        # ВАЖЛИВО: response містить нову фінальну транзакцію
                        final_transaction_id = response.id if hasattr(response, 'id') and response.id else invoice.incust_transaction_id
                        logger.info(f"Отримуємо emitted_coupons з фінальної транзакції {final_transaction_id} (резервна була {invoice.incust_transaction_id})")
                        
                        # Отримуємо фінальний чек за ID транзакції
                        async with incust.term.CheckTransactionsApi(loyalty_settings) as api:
                            receipt = await api.transaction_receipt(
                                str(final_transaction_id)
                            )
                        
                        if receipt:
                            # Оновлюємо incust_check з даними фінального чека
                            # from copy import deepcopy
                            # updated_incust_check = deepcopy(receipt.check.dict())
                            # Додаємо дані транзакції від фіналізації
                            # updated_incust_check["transaction"] = response.dict()
                            
                            # Збираємо всі дані для одного оновлення
                            update_data = {
                                'is_loyalty_transaction_completed': True,
                                # 'incust_check': convert_datetime_to_str(updated_incust_check)
                            }
                            
                            # Оновлюємо special_accounts_charges з фінального чека
                            if receipt.check.special_accounts_charges:
                                logger.info(f"Знайдено {len(receipt.check.special_accounts_charges)} special_accounts_charges в фінальному чеку")
                                
                                # Зберігаємо дані спеціальних рахунків з назвами
                                update_data['special_accounts_charges'] = [
                                    special_account.dict() for special_account in receipt.check.special_accounts_charges
                                ]

                            # Обробляємо купони з фінального чека
                            if receipt.check.emitted_coupons:
                                logger.info(f"Знайдено {len(receipt.check.emitted_coupons)} emitted_coupons в фінальному чеку")
                                
                                # Збагачуємо дані купонів через coupon_service
                                try:
                                    from core.loyalty import coupon_service
                                    from db import crud
                                    
                                    brand = await crud.get_brand_by_group(invoice.group_id)
                                    user = await User.get_by_id(invoice.user_id)
                                    lang = await user.get_lang(invoice.bot_id) if user else 'uk'
                                    
                                    if brand:
                                        enriched_coupons = []
                                        
                                        # Передаємо оригінальні об'єкти term.m.Coupon для збагачення
                                        for coupon in receipt.check.emitted_coupons:
                                            if not coupon:
                                                continue
                                                
                                            # Готуємо купон для відображення
                                            enriched_coupon = await coupon_service.prepare_coupon_for_display(
                                                coupon,
                                                brand,
                                                loyalty_settings,
                                                user,
                                                lang,
                                                
                                            )
                                            
                                            if enriched_coupon:
                                                enriched_coupons.append(enriched_coupon)
                                        
                                        # Зберігаємо збагачені дані купонів як dict (без PDF!)
                                        coupons_for_save = []
                                        for coupon in enriched_coupons:
                                            coupon_dict = coupon.dict()
                                            # Видаляємо PDF з даних для збереження (залишаємо тільки метадані)
                                            coupon_dict.pop('pdf', None)
                                            coupons_for_save.append(coupon_dict)
                                        update_data['loyalty_coupons_data'] = coupons_for_save
                                        logger.info(f"Збагачено {len(enriched_coupons)} купонів через coupon_service")
                                    else:
                                        # Якщо немає бренду, зберігаємо базові дані
                                        update_data['loyalty_coupons_data'] = [
                                            coupon.dict() for coupon in receipt.check.emitted_coupons if coupon
                                        ]
                                        logger.warning("Не знайдено бренд для збагачення купонів")
                                except Exception as coupon_ex:
                                    logger.error(f"Помилка збагачення купонів: {coupon_ex}", exc_info=True)
                                    # У разі помилки зберігаємо базові дані
                                    update_data['loyalty_coupons_data'] = [
                                        coupon.dict() for coupon in receipt.check.emitted_coupons if coupon
                                    ]
                            
                            # Якщо фінальна транзакція має інший ID - оновлюємо incust_transaction_id
                            if final_transaction_id != invoice.incust_transaction_id:
                                update_data['incust_transaction_id'] = str(final_transaction_id)
                                logger.info(f"Оновлено incust_transaction_id на {final_transaction_id} (був {invoice.incust_transaction_id})")

                            if receipt.check.bonuses_added_amount:
                                update_data['bonuses_added_amount'] = int(
                                    receipt.check.bonuses_added_amount * 100
                                )

                            await invoice.update(**update_data)
                            logger.info(f"Оновлено всі дані лояльності для invoice {invoice.id} з фінального чека")
                        else:
                            logger.error(f"Не вдалося отримати фінальний чек для транзакції {final_transaction_id}")
                            return False
                        
                    except Exception as update_ex:
                        logger.error(f"Помилка оновлення даних з фінального чека: {update_ex}", exc_info=True)
                        return False

                return True
            else:
                logger.error(
                    f"Помилка фіналізації транзакції {invoice.incust_transaction_id} "
                    f"для invoice {invoice.id}"
                )
                return False

        except Exception as e:
            logger.error(
                f"Помилка фіналізації лояльності для invoice {invoice.id}: {e}",
                exc_info=True
            )
            return False
