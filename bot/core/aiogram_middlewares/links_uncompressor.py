from aiogram import types
from aiogram.dispatcher.middlewares import BaseMiddleware

from db.models import CompressedLink


class LinkUncompressorMiddleware(BaseMiddleware):

    @staticmethod
    async def on_pre_process_message(message: types.Message, data: dict):
        if not message.is_command():
            return

        if not message.text:
            return

        if not message.text.startswith("/start"):
            return

        link = message.get_args()
        if link is None:
            return

        compressed_link = await CompressedLink.get(link)
        if not compressed_link:
            return

        data.update(compressed_link=compressed_link)
