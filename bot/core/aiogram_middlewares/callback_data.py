from aiogram import types
from aiogram.dispatcher.middlewares import LifetimeControllerMiddleware

from utils.text import parse_callback_data


class CallbackDataMiddleware(LifetimeControllerMiddleware):

    async def pre_process(self, obj, data, *args):
        if isinstance(obj, types.CallbackQuery):
            data_str = obj.data
        elif isinstance(obj, types.ChosenInlineResult):
            data_str = obj.result_id
        elif hasattr(obj, "invoice_payload"):
            data_str = obj.invoice_payload
        else:
            return

        mode, callback_data = parse_callback_data(data_str)
        data.update(mode=mode, callback_data=callback_data)
