import logging

from aiogram import types
from aiogram.dispatcher.handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from aiogram.dispatcher.middlewares import LifetimeControllerMiddleware
from aiogram.utils.exceptions import BadRequest

import schemas
from core.auth.deep_links import ExternalLoginDeepLink
from core.chat.chat_message_sender import ChatMessageSender
from core.helpers import check_incust_customer
from core.menu_in_store.deel_links import MenuInStore<PERSON>eepLink
from core.user.agreement_processor.agreement_processor import agreement_processor
from core.user.exceptions import UserNotCreated
from core.user.functions import (
    create_or_update_messanger_user,
    detect_messanger_user_lang,
)
from core.user.types import CreateOrUpdateMessangerUserInfo
from db import crud
from db.models import ClientBot, Customer, Group, User, UserClientBotActivity
from loggers import JSONLogger
from utils.platform_admins import send_message_to_platform_admins
from utils.redefined_classes import Bot
from utils.text import f


class CreateOrUpdateUserMiddleware(LifetimeControllerMiddleware):

    @staticmethod
    async def is_external_login(message: types.Message):
        try:
            external_login_filter = ExternalLoginDeepLink.get_filter()
            return bool(await external_login_filter(message))
        except Exception as e:
            logging.error(e, exc_info=True)
            return False

    async def pre_process(self, obj, data, *args):
        logger = JSONLogger(
            "create-or-update-middleware", {
                "obj": obj,
                "data": data,
            }
        )

        if isinstance(obj, types.Message):
            if obj.content_type in ("new_chat_members", "left_chat_member"):
                return

            # if external login link — return
            if await self.is_external_login(obj):
                return

        if isinstance(obj, types.CallbackQuery) and obj.data.startswith(
                agreement_processor.prefix
        ):
            return

        if not hasattr(obj, "from_user") or not obj.from_user:
            return

        if isinstance(obj, types.Message):
            if not hasattr(obj, "chat") or not obj.chat:
                await send_message_to_platform_admins(
                    'ERROR pre_process:\nif not hasattr(obj, "chat") or not obj.chat'
                )
                logger.error(
                    f'pre_process:\nif not hasattr(obj, "chat") or not obj.chat\n'
                    f'{obj = }'
                )
                return
            ask_agreement = (obj.chat and obj.chat.type == "private" and obj.chat.id
                             == obj.from_user.id)
        elif isinstance(obj, types.CallbackQuery):
            ask_agreement = (
                    obj.message.chat and
                    obj.message.chat.type == "private" and
                    obj.from_user.id == obj.message.chat.id
            )
        else:
            ask_agreement = False

        bot: ClientBot | None = data.get("bot")

        info = await self.create_or_update_user(
            obj.from_user, bot, ask_agreement,
        )
        await self.insert_data(data, info)

        user_bot_activity: UserClientBotActivity | None = info.user_bot_activity

        if not isinstance(obj, types.Message) or \
                not bot or obj.chat.id != obj.from_user.id or \
                not user_bot_activity:
            return

        group = await Group.get(bot.group_id)
        user: User = data.get("user")
        lang: str = data.get("lang")

        try:
            try:
                qr_menu_filter = MenuInStoreDeepLink.get_filter()
                qr_menu_data = await qr_menu_filter(obj)

                if qr_menu_data:
                    active_menu_in_store_id = qr_menu_data["qrmenu"].menu_in_store_id
                else:
                    active_menu_in_store_id = user_bot_activity.active_menu_in_store_id
            except Exception as e:
                logger.error(
                    "An error occurred while retrieving active_menu_in_store_id", e
                )
                active_menu_in_store_id = None

            if not bot.is_friendly:
                from client.main.functions import set_menu_button
                await set_menu_button(bot, user, lang, active_menu_in_store_id)
        except Exception as e:
            logger.error("An error occurred while setting menu button in middleware", e)
            if not isinstance(e, BadRequest) or e.text != "User not found":
                await send_message_to_platform_admins(
                    "An error occurred while setting menu button in middleware.\n"
                    "Details in logs"
                )

        await check_incust_customer(bot, info.user, lang)

        if not user_bot_activity.is_entered_bot:
            chat = await crud.get_or_create_chat(
                schemas.ChatTypeEnum.USER_WITH_GROUP, user.id, group.id, bot.id
            )
            await ChatMessageSender(
                chat, schemas.MessageContentTypeEnum.TEXT,
                await f("user entered bot message", group.lang),
                sent_by=schemas.ChatMessageSentByEnum.USER,
                sent_by_user_id=user.id,
                chat_user=user,
                group=group,
                bot=bot,
                chat_pending=False,
                disable_send_to_service_bot=True,
            )
            await user_bot_activity.entered_bot()

    @staticmethod
    async def insert_data(data: dict, info: CreateOrUpdateMessangerUserInfo):
        data.update(
            user=info.user,
            lang=info.lang,
            is_created_user=info.is_user_created,
            user_bot_activity=info.user_bot_activity,
            is_created_bot_activity=info.is_bot_activity_created,
        )

    @staticmethod
    async def create_or_update_user(
            from_user: types.User,
            bot: ClientBot | None,
            ask_agreement: bool = True
    ) -> CreateOrUpdateMessangerUserInfo:
        try:
            info = await create_or_update_messanger_user(
                from_user,
                return_is_created=True,
                allowed_create_user=False,
            )

            if ask_agreement and bot:
                if (
                        # if new user, or not accepted agreement
                        not info.user or
                        not info.user.is_accepted_agreement or

                        (
                                info.user and
                                (
                                        # if not customer (consent has not been asked
                                        # yet)
                                        not (customer := await Customer.get(
                                            user_id=info.user.id,
                                            profile_id=bot.group_id
                                        )) or
                                        # if there is no answer for marketing consent
                                        customer.marketing_consent is None or
                                        # if user agreement not accepted
                                        not customer.is_accept_agreement
                                )
                        )
                ):
                    lang = await detect_messanger_user_lang(from_user, bot, info.user)

                    await agreement_processor.ask(
                        from_user.id, lang, bot, types.Update.get_current()
                    )
                    raise CancelHandler()

            return info
        except UserNotCreated as user_not_created:
            logger = logging.getLogger()
            logger.error(user_not_created, exc_info=True)
            user_got_error_message = False

            chat_id = user_not_created.user_id
            if not chat_id:
                current_user = types.User.get_current()
                if current_user:
                    chat_id = current_user.id

            if chat_id:
                current_bot = Bot.get_current()
                try:
                    text = await f(
                        "user not created error for user", from_user.language_code
                    )
                    await current_bot.send_message(chat_id, text)
                except:
                    pass
                else:
                    user_got_error_message = True

            # язык ru, так как сообщение к разработчикам
            if user_got_error_message:
                user_got_error_message = await f("user got error message", "ru")
            else:
                user_got_error_message = await f("user did not get error message", "ru")
            username, full_name = user_not_created.username, user_not_created.full_name

            text = await f(
                "user not created error for developers", "ru",
                chat_id=chat_id, username=username,
                full_name=full_name, bot_username=user_not_created.bot_username,
                user_got_error_message=user_got_error_message,
            )
            await send_message_to_platform_admins(text)
            raise CancelHandler()
