from aiogram import types
from aiogram.dispatcher.middlewares import BaseMiddleware


class CacheTimeMiddleware(BaseMiddleware):

    async def on_post_process_callback_query(self, callback_query: types.CallbackQuery, *_):
        await self.answer_cache_time(callback_query)

    @staticmethod
    async def answer_cache_time(callback_query: types.CallbackQuery):
        try:
            await callback_query.answer(cache_time=0)
        except:
            pass
