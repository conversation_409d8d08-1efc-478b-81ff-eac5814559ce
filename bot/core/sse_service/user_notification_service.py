from fastapi.security import SecurityScopes
from starlette.requests import Request

from core.auth.depend import get_active_user, get_user, get_user_or_token_data
from core.auth.services.scopes_checker import ScopesCheckerService
from core.sse_service.service import SSEStreamService
from schemas import SSEChannelTarget
from typing import Any


class UserSSEService(SSEStreamService):
    def __init__(
            self,
            session_id: str,
    ):
        super().__init__(
            session_id=session_id,
            target=SSEChannelTarget.USER_NOTIFICATION
        )

    async def get_data(
            self,
            request: Request,
            token: str,
            lang: str,
            data: Any
    ) -> dict:
        user = await get_active_user(
            await get_user(
                await get_user_or_token_data(
                    request,
                    SecurityScopes(scopes=["me:read"]),
                    token,
                    None,
                )
            )
        )

        scopes = ScopesCheckerService(
            None, user, lang, data
        )
        await scopes.process()

        return {"key": user.id}
