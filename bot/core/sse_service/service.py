import asyncio
import json
from abc import ABC, abstractmethod
from typing import Any

from fastapi.security import SecurityScopes
from sse_starlette import EventSourceResponse
from starlette.requests import Request

import exceptions
from config import DEBUG
from core.auth.depend import get_active_user, get_user, get_user_or_token_data
from core.auth.services.scopes_checker import ScopesCheckerService
from core.kafka.redis_instance import redis
from db import DBSession, crud
from db.models import EWalletPayment
from loggers import JSONLogger
from schemas import SSEChannelTarget


class SSEStreamService(ABC):
    def __init__(
            self,
            session_id: str,
            target: SSEChannelTarget,
    ):
        self.session_id: str = session_id
        self.target: SSEChannelTarget = target

    @abstractmethod
    async def get_data(self, **kwargs) -> dict:
        pass

    @classmethod
    async def event_stream(cls, channel_id: int, logger: JSONLogger):
        """Генератор подій для SSE"""
        redis_channel_name = f"sse-{channel_id}"

        pubsub = redis.pubsub()

        try:
            await pubsub.subscribe(redis_channel_name)
            async for message in pubsub.listen():
                try:
                    if message["type"] == "message":
                        message_data = json.loads(message["data"].decode())
                        yield dict(
                            data=json.dumps(message_data["data"]),
                            event=message_data["event"],
                        )
                except Exception as e:
                    logger.error(
                        "Error processing redis message", e, {
                            "message": message
                        }
                    )
        except asyncio.CancelledError:
            logger.debug(f"Client disconnected. Cleaning up resources. {channel_id=}")
            raise
        finally:
            # if not pubsub.subscribed:
            logger.debug(f"delete_sse_channel({channel_id=})")
            try:
                with DBSession():
                    await crud.delete_sse_channel(channel_id)
            except Exception as e:
                logger.error(
                    "Unable to delete database SSEChannel", e, {
                        "channel_id": channel_id,
                    }
                )
            try:
                # відписуємось від Redis каналу
                await pubsub.unsubscribe(redis_channel_name)
            except Exception as e:
                logger.error(
                    "Unable to pubsub.unsubscribe()", e, {
                        "redis_channel": redis_channel_name,
                    }
                )
            try:
                # Видаляємо канал в Redis
                await redis.delete(redis_channel_name)
            except Exception as e:
                logger.error(
                    "Unable to delete redis channel", e, {
                        "redis_channel": redis_channel_name,
                    }
                )

    async def stream_notifications(
            self,
            **kwargs,
    ):
        logger = JSONLogger(
            "stream_notifications", {
                "session_id": self.session_id,
                **kwargs,
            }
        )

        try:
            with DBSession():
                channel = await crud.create_sse_channel(
                    self.session_id,
                    self.target,
                    **kwargs
                )
                if DEBUG:
                    logger.debug(
                        "Created SSE Channel", {
                            "channel": channel,
                        }
                    )
                channel_id = channel.id
        except Exception as e:
            logger.error("An error occurred while opening SSE Stream", e)
            raise exceptions.CannotOpenSSEStreamError() from e

        generator = self.event_stream(channel_id, logger)
        return EventSourceResponse(generator)


class AdminSSEService(SSEStreamService):
    async def get_data(
            self,
            request: Request,
            token: str,
            lang: str,
            data: Any
    ) -> dict:
        user = await get_active_user(
            await get_user(
                await get_user_or_token_data(
                    request,
                    SecurityScopes(scopes=["me:read"]),
                    token,
                    None,
                )
            )
        )

        scopes = ScopesCheckerService(
            None, user, lang, data
        )
        await scopes.process()

        return {"profile_id": data.profile_id, "key": user.id}


class PaymentSSEService(SSEStreamService):
    async def get_data(
            self,
            request: Request,
            payment_id: int,
            profile_id: int,
    ) -> dict:
        ewallet_payment = await EWalletPayment.get(id=payment_id, profile_id=profile_id)

        return {"external_id": ewallet_payment.external_id}
