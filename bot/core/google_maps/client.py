import logging
from types import GenericAlias
from typing import Type, get_origin

from aiohttp import ClientResponse, ClientSession
from pydantic import BaseModel

import schemas
from utils.type_vars import T


class MapsClient:
    __slots__ = ["api_key", "lang", "debug_logger"]

    def __init__(self, api_key: str, lang: str):
        self.api_key = api_key
        self.lang = lang
        self.debug_logger = logging.getLogger('debugger.maps')

    async def __get_headers(self) -> dict:
        return {
            'Accept-Language': self.lang,
        }

    async def __make_request(
            self, url: str, params: dict = None, method: str = 'GET',
            return_model: Type[T] | list[Type[T]] = dict | list,
    ) -> T:
        request_data = {
            'url': url,
            'headers': await self.__get_headers(),
        }
        if params:
            request_data['params'] = params

        async with ClientSession() as session:
            self.debug_logger.debug(f'[MAPS]\n{method} {request_data}')
            method = getattr(session, method.lower())
            resp: ClientResponse = await method(**request_data)

            if resp.status != 200:
                logging.error(f'[MAPS] {resp.status} {resp.reason}')
                # TODO: raise exception with original error message
                ...

            data = await resp.json()
            self.debug_logger.debug(f'[MAPS]\n{data}')
            if type(return_model) is GenericAlias:
                return_model_type = get_origin(return_model)

                if return_model_type is list:
                    if issubclass(return_model.__args__[0], BaseModel):
                        model = return_model.__args__[0]
                        if isinstance(data, list):
                            return [model(**item) for item in data]
                    elif issubclass(return_model.__args__[0], dict):
                        return data
            return return_model(**data)

    async def autocomplete(
            self, params: schemas.AutocompleteParamsSchema,
    ) -> schemas.Predictions:
        url = "https://maps.googleapis.com/maps/api/place/autocomplete/json"
        params = params.dict(exclude_none=True)
        params['key'] = self.api_key

        return await self.__make_request(
            url, params=params, return_model=schemas.Predictions
        )

    async def place_details(
            self, params: schemas.PlaceDetailsParamsSchema
    ) -> schemas.PlaceDetails:
        url = "https://maps.googleapis.com/maps/api/place/details/json"
        params = params.dict(exclude_none=True)
        params['key'] = self.api_key

        return await self.__make_request(
            url, params=params, return_model=schemas.PlaceDetails
        )

    async def reverse_geocoding(
            self, params: schemas.ReverseGeocodingParamsSchema,
    ) -> schemas.GeocodingResultSchema:
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = params.dict(exclude_none=True)
        params['key'] = self.api_key

        return await self.__make_request(
            url, params=params, return_model=schemas.GeocodingResultSchema
        )

    async def geocoding(
            self, params: schemas.GeocodingParamsSchema,
    ) -> schemas.GeocodingResultSchema:
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = params.dict(exclude_none=True)
        params['key'] = self.api_key

        return await self.__make_request(
            url, params=params, return_model=schemas.GeocodingResultSchema
        )
