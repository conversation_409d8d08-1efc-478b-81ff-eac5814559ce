import logging
from typing import Literal

from fastapi import Depends, HTTPException

import schemas
from config import GOOGLE_MAPS_API_KEY, GOOGLE_PLACES_GEOCODING_API_KEY
from core.api.depends import get_lang
from core.store.depends import get_current_brand
from db.models import Brand
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from ..client import MapsClient


class MapsService:
    __slots__ = ["client", "brand", "brand_txt"]

    def __init__(
            self, lang: str = Depends(get_lang),
            brand: Brand | None = Depends(get_current_brand),
            # TODO: use to get country code etc,
    ):
        api_key = ""
        self.brand_txt = ""
        if brand:
            self.brand_txt = f"[BRAND {brand.name}]"
            if brand.google_maps_api_key_backend:
                api_key = brand.google_maps_api_key_backend
            else:
                if (brand.google_maps_7loc_keys_enabled and
                        GOOGLE_PLACES_GEOCODING_API_KEY):
                    api_key = GOOGLE_PLACES_GEOCODING_API_KEY

        self.client = MapsClient(api_key, lang)
        self.brand = brand

    async def autocomplete(
            self, params: schemas.AutocompleteParamsSchema
    ) -> schemas.PredictionsResponse:
        try:
            predictions = await self.client.autocomplete(params)
        except Exception as ex:
            logging.error(
                f"[GOOGLE API ERROR] {self.brand_txt} {ex} {params}", exc_info=True
            )
            await send_message_to_platform_admins(
                f"[GOOGLE API ERROR] {self.brand_txt} autocomplete\n\n{ex}\n\n{params}",
                force_to_bot=False,
            )
            raise HTTPException(
                status_code=400,
                detail=await f(
                    "maps api status error", self.client.lang, status="internal"
                )
            )

        if predictions.status != "OK":
            await self.__handle_error(
                "places", predictions.status, predictions.error_message
            )

        return schemas.PredictionsResponse(
            predictions=predictions,
        )

    async def place_details(
            self, params: schemas.PlaceDetailsParamsSchema
    ) -> schemas.PlaceDetails:
        try:
            params.fields = "geometry,formatted_address,address_components,place_id"
            details = await self.client.place_details(params)
        except Exception as ex:
            logging.error(
                f"[GOOGLE API ERROR] {self.brand_txt} {ex} {params}", exc_info=True
            )
            await send_message_to_platform_admins(
                f"[GOOGLE API ERROR] {self.brand_txt} place_details \n\n{ex}\n\n"
                f"{params}",
                force_to_bot=False,
            )
            raise HTTPException(
                status_code=400,
                detail=await f(
                    "maps api status error", self.client.lang, status="internal"
                )
            )

        if details.status != "OK":
            await self.__handle_error("places", details.status, details.error_message)

        return details

    async def reverse_geocoding(
            self, params: schemas.ReverseGeocodingParamsSchema,
    ) -> schemas.GeocodingResultSchema:
        if not params.latlng:
            raise HTTPException(
                status_code=400,
                detail=await f("maps api no address or latlng error", self.client.lang)
            )
        try:
            result = await self.client.reverse_geocoding(params)
        except Exception as ex:
            logging.error(
                f"[GOOGLE API ERROR] {self.brand_txt} {ex} {params}", exc_info=True
            )
            await send_message_to_platform_admins(
                f"[GOOGLE API ERROR] {self.brand_txt} reverse_geocoding \n\n{ex}\n\n"
                f"{params}",
                force_to_bot=False,
            )
            raise HTTPException(
                status_code=400,
                detail=await f(
                    "maps api status error", self.client.lang, status="internal"
                )
            )

        if result.status != "OK":
            await self.__handle_error("geocoding", result.status, result.error_message)

        return result

    async def geocoding(
            self, params: schemas.GeocodingParamsSchema,
    ) -> schemas.GeocodingResultSchema:
        if not params.address:
            raise HTTPException(
                status_code=400,
                detail=await f("maps api no address or latlng error", self.client.lang)
            )
        if len(params.address) <= 3:
            raise HTTPException(
                status_code=400,
                detail=await f("maps api address low length error", self.client.lang)
            )
        try:
            result = await self.client.geocoding(params)
        except Exception as ex:
            logging.error(
                f"[GOOGLE API ERROR] {self.brand_txt} {ex} {params}", exc_info=True
            )
            await send_message_to_platform_admins(
                f"[GOOGLE API ERROR] {self.brand_txt} geocoding \n\n{ex}\n\n{params}",
                force_to_bot=False,
            )
            raise HTTPException(
                status_code=400,
                detail=await f(
                    "maps api status error", self.client.lang, status="internal"
                )
            )

        if result.status != "OK":
            if result.status == "ZERO_RESULTS":
                raise HTTPException(
                    status_code=400,
                    detail=await f("maps api zero results error", self.client.lang)
                )
            await self.__handle_error("geocoding", result.status, result.error_message)

        return result

    async def validate_address(
            self, address_components: list[schemas.AddressComponent]
    ) -> schemas.AddressValidSchema:
        valid = False
        purpose = "default"

        is_street = False
        is_street_number = False
        is_city = False
        is_country = False

        for component in address_components:
            if "street_number" in component.types:
                is_street_number = True
            if "route" in component.types:
                is_street = True
            if "street" in component.types:
                is_street = True
            if "administrative_area_level_1" in component.types:
                is_city = True
            if "administrative_area_level_2" in component.types:
                is_city = True
            if "country" in component.types:
                is_country = True

        if not is_street:
            purpose = "street"
        elif not is_street_number:
            purpose = "street_number"
        elif not is_city:
            purpose = "city"
        elif not is_country:
            purpose = "country"
        else:
            valid = True

        if valid:
            text = ""
        else:
            text = await f(f"address validation error {purpose} text", self.client.lang)

        return schemas.AddressValidSchema(valid=valid, purpose=purpose, text=text)

    async def get_maps_key(self) -> schemas.MapsKeySchema:
        return schemas.MapsKeySchema(
            key=GOOGLE_MAPS_API_KEY,
        )

    async def __handle_error(
            self, api_type: Literal["places", "geocoding"],
            status: str, error_message: str | None = None,
    ):
        if status != "ZERO_RESULTS":
            err_text = f"[GOOGLE API ERROR {api_type}] {status}"
            logging.error(err_text, exc_info=True)
            if error_message:
                err_text += f"\n{error_message}"
            await send_message_to_platform_admins(err_text, force_to_bot=False)

            raise HTTPException(
                status_code=400,
                detail=await f(
                    "maps api status error", self.client.lang, status=status
                ),
            )
