import logging

from psutils.ai_chat.base import BaseAIChat
from psutils.ai_chat.functions import AIChatFunction
from starlette.websockets import WebSocket

import schemas
from core.ai_chat.functions import send_recommended_products
from core.store.product.functions import products_list_to_schemas
from db import crud
from db.models import Brand, Group, Store, StoreProduct, Translation

LIMIT = 15


class AIChat(BaseAIChat):

    @AIChatFunction(
        "Call recommend products service to find products and send to user\n"
        "Send the most complete user request for a recommendation "
        "to another artificial intelligence to find products"
    )
    async def find_and_send_products_to_user(
            self, data: schemas.RecommendAndSendProductData,
            brand: Brand,
            store: Store,
            group: Group,
            lang: str,
            websocket: WebSocket,
    ):
        debugger = logging.getLogger("debugger")
        debugger.debug(f"Search products request: {data.recommend_request}")

        db_products: list[tuple[StoreProduct, Translation | None]]
        products_notes: dict[int, str] = {}

        db_products = await crud.get_store_products(
            brand.id, store.id, lang=lang,
            params=schemas.ProductsListParams(
                sort=schemas.ProductsSortEnum.DEFAULT,
                search_text=data.recommend_request,
            )
        )

        if not db_products:
            debugger.debug("products not found")
            return "Products not found"

        products_schemas = await products_list_to_schemas(
            db_products, store.id, group.id,
            lang, group.lang, group.is_translate, brand
        )

        products = [schemas.RecommendedProductSchema(
            product=product_schema,
            note=products_notes.get(product_schema.id)
        ) for product_schema in products_schemas]

        debug_products = [
            '#' + str(product.product.id) +
            ' ' + product.product.name +
            ' ' + str(product.note)
            for product in products
        ]

        debugger.debug(
            f"Products found: "
            f"{debug_products}"
        )

        await send_recommended_products(
            websocket, data.recommend_request, products,
        )

        return f"Successfully showed {len(products)} products to user"
