from psutils.convertors import time_to_str
from starlette.websockets import WebSocket

import schemas
from core.helpers import utc_time_to_local
from db.models import (
    Group, Store,
)
from utils.text import f

SYSTEM_MESSAGE_LANG = "en"


async def get_system_message(
        group: Group,
        store: Store,
        user_lang: str,
):
    if store.ai_description:
        store_description = store.ai_description
    else:
        store_description = await f(
            "ai default store description", SYSTEM_MESSAGE_LANG,
            name=store.name, description=store.description
        )

    custom_fields: list[schemas.StoreCustomFieldSchema] \
        = [schemas.StoreCustomFieldSchema.from_orm(el) for el in store.custom_fields]

    working_days: list[schemas.WorkingDaySchema] \
        = [schemas.WorkingDaySchema.from_orm(el) for el in store.working_days]

    shop_info_texts: list[str] = []
    for custom_field in custom_fields:
        if custom_field.name and custom_field.value:
            shop_info_texts.append(" ".join((custom_field.name, custom_field.value)))

    if working_days:
        working_days_texts: list[str] = []
        for day in working_days:

            slots_texts: list[str] = []
            for slot in day.slots:
                start_time_text = time_to_str(
                    utc_time_to_local(slot.start_time, group.timezone)
                )
                end_time_text = time_to_str(
                    utc_time_to_local(slot.end_time, group.timezone)
                )

                slots_texts.append("-".join((start_time_text, end_time_text)))

            if not slots_texts:
                continue
            slots_text = ",".join(slots_texts)

            working_days_texts.append(": ".join((day.day, slots_text)))

        working_days_text = ";".join(working_days_texts)
        shop_info_texts.append(" ".join(("WORK SCHEDULE", working_days_text)))

    if shop_info_texts:
        shop_info_text = "\n".join(shop_info_texts)
    else:
        shop_info_text = ""

    system_message = await f(
        "group ai chat system message", SYSTEM_MESSAGE_LANG,
        description=store_description,
        shop_info=shop_info_text,
    )

    if user_lang:
        lang_text = await f(
            "group ai chat system message user lang", SYSTEM_MESSAGE_LANG,
            user_lang=user_lang,
        )
        system_message += "\n\n" + lang_text

    return system_message


async def send_websocket_action(
        websocket: WebSocket,
        action_type: schemas.AIChatSocketActionTypeEnum,
        message: schemas.AIChatSocketMessage | None = None,
):
    if action_type == schemas.AIChatSocketActionTypeEnum.MESSAGE and not message:
        raise ValueError("when action type is \"message\" message is required")

    action = schemas.AIChatSocketAction(
        action_type=action_type,
        message=message,
    )

    await websocket.send_json(action.dict(by_alias=True))

    return action


async def send_message(
        websocket: WebSocket,
        message: schemas.AIChatSocketMessage,
):
    return await send_websocket_action(
        websocket,
        schemas.AIChatSocketActionTypeEnum.MESSAGE,
        message
    )


async def send_recommended_products(
        websocket: WebSocket,
        recommend_request: str,
        products: list[schemas.RecommendedProductSchema],
):
    return await send_websocket_action(
        websocket,
        schemas.AIChatSocketActionTypeEnum.MESSAGE,
        schemas.AIChatSocketMessage(
            from_=schemas.AIChatSocketMessageFromEnum.SERVER,
            type=schemas.AIChatSocketMessageTypeEnum.RECOMMENDED_PRODUCTS,
            recommended_products=schemas.RecommendedProductsData(
                products=products,
                recommend_request=recommend_request,
            ),
        ),
    )
