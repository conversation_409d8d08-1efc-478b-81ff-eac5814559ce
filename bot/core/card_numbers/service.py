import base64
from datetime import datetime
import io
import uuid

from psutils.convertors import datetime_to_str


class CardNumbersService:

    def __init__(self, count: int, prefix: str | None = None):
        self.count: int = count
        self.prefix = prefix

        self._card_numbers: list[str] | None = None
        self._card_numbers_bytes: bytes | None = None
        self.generate()

    def generate(self):
        self._card_numbers = [str(int(uuid.uuid4())) for _ in range(self.count)]
        if self.prefix:
            self._card_numbers = ["-".join([self.prefix, card_number]) for card_number in self._card_numbers]

        self._card_numbers_bytes = "\n".join(self._card_numbers).encode("utf-8")

    @property
    def card_numbers(self) -> list[str]:
        return self._card_numbers

    @property
    def file_name(self) -> str:
        date_time_str = datetime_to_str(datetime.now(), short=True).replace(":", "-").replace(" ", "_")
        return f"CardNumbers-{self.count}-{date_time_str}.txt"

    @property
    def bytes_file(self) -> io.BytesIO:
        return io.BytesIO(self._card_numbers_bytes)

    @property
    def base64_str(self) -> str:
        return base64.b64encode(self._card_numbers_bytes).decode("utf-8")
