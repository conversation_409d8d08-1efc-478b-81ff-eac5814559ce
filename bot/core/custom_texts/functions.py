from db.connection import Base
from db.models import CustomTextsStorage, Translation
from .models import CustomTextsModel


async def get_custom_text(
        object_: CustomTextsModel | CustomTextsStorage | Base,
        lang: str, field_name: str, *key_parts,
        translation: Translation | None = None,
        **text_kwargs,
):
    custom_texts_obj = await CustomTextsModel.from_object(object_)
    return await custom_texts_obj.get_value(
        field_name, key_parts,
        lang=lang, text_kwargs=text_kwargs,
        translation=translation,
    )


async def get_is_custom_text_enabled(
        object_: CustomTextsModel | CustomTextsStorage | Base,
        field_name: str, *key_parts,
):
    custom_texts_obj = await CustomTextsModel.from_object(object_)
    return bool(await custom_texts_obj.get_value(field_name, key_parts, raw=True))
