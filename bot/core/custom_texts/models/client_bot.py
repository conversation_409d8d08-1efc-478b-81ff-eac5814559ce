from db.models import Group
from .base import CustomTextsDict, CustomTextsField, CustomTextsModel, CustomTextsString
from .base.base import TranslatorSettings


class ClientBotCustomTextsModel(CustomTextsModel, scope_action_object_name="bot"):
    main = CustomTextsField(
        CustomTextsDict(
            {
                "profile_button": CustomTextsString(),
                "profile_button_description": CustomTextsString(need_disable=True),
                "enter_code_button": CustomTextsString(None),
                "leave_review_button": CustomTextsString(None),
                "review_button_description": CustomTextsString(need_disable=True),
                "share_bot_button": CustomTextsString(None),
                "auto_answer_text": CustomTextsString(),
                "wallet_button": CustomTextsString(),
                "wallet_button_description": CustomTextsString(need_disable=True),
                "ewallet_button": CustomTextsString(),
                "ewallet_button_description": CustomTextsString(need_disable=True),
                "benefits_button": CustomTextsString(
                    display_text_variable="client bot main benefits button display "
                                          "text",
                ),
                "benefits_button_description": CustomTextsString(need_disable=True),
                "share_and_earn_button_description": CustomTextsString(
                    need_disable=True
                ),
                "my_qr": CustomTextsDict(
                    {
                        "show_button": CustomTextsString(None),
                        "message_text": CustomTextsString(),
                    }
                ),
                "scan_receipt_button": CustomTextsString(),
                "scan_receipt_button_description": CustomTextsString(need_disable=True),
                "hello_text": CustomTextsString(),
                "welcome_text": CustomTextsString(),
                "welcome_back_text": CustomTextsString(),
            }
        )
    )

    chat = CustomTextsField(
        CustomTextsDict(
            {
                "start_button": CustomTextsString(None),
                "start_button_description": CustomTextsString(None, need_disable=True),
                "with_text": CustomTextsString(),
                "my_list_button": CustomTextsString(),
            }
        )
    )

    catalog = CustomTextsField(
        CustomTextsDict(
            {
                "button": CustomTextsString(),
                "events": CustomTextsDict(
                    "button",
                    "all_button",
                    "search_button",
                    "not_found_text",
                    "found_text",
                    "list_empty_text",
                    "sale_label_text",
                    "cashback_label_text",
                    "recommendation_label_text",
                    "header_text",
                ),
                "groups": CustomTextsDict(
                    "button",
                    "all_button",
                    "list_button",
                    "header_text",
                ),
                "vouchers": CustomTextsDict(
                    "button",
                    "header_text",
                    "open_button",
                    "open_text",
                ),
                "orders": CustomTextsDict(
                    "button",
                    "header_text",
                    "open_button",
                    "open_text",
                    "show_web_button",
                ),
                "categories": CustomTextsDict(
                    "button",
                    "chat_button",
                    "empty_text",
                    "header_text",
                    "open_button",
                    "open_text",
                )
            }
        )
    )

    payments = CustomTextsField(
        CustomTextsDict(
            "invoice_from_other_bot_text",
        )
    )

    shop = CustomTextsField(
        CustomTextsDict(
            {
                "button": CustomTextsString(),
                "button_description": CustomTextsString(need_disable=True),
                "section_title": CustomTextsString(),
                "open_text": CustomTextsString(),
                "wa_open_text": CustomTextsString(),
                "share_and_earn_button": CustomTextsString(),
                "share_and_earn_open_text": CustomTextsString(),
            }
        )
    )

    finances = CustomTextsField(
        CustomTextsDict(
            {
                "how_to": CustomTextsDict(
                    {
                        "earn_and_use_cash_button": CustomTextsString(
                            additional_params="vm"
                        ),
                        "earn_cash_button": CustomTextsString(additional_params="vm"),
                        "use_cash_button": CustomTextsString(additional_params="vm"),
                        "earn_rcoins_button": CustomTextsString(additional_params="vm"),
                        "use_rcoins_button": CustomTextsString(additional_params="vm"),
                        "earn_rcash_button": CustomTextsString(additional_params="vm"),
                        "use_rcash_button": CustomTextsString(additional_params="vm"),
                    }
                ),
                "withdraw_rcash_button": CustomTextsString(),
            }
        )
    )

    additional = CustomTextsField(
        CustomTextsDict(
            {
                "main_button": CustomTextsDict(
                    {
                        "custom_button": CustomTextsString(
                            None,
                            need_use_localisation=False,
                            need_enable=False,
                        ),
                        "custom_url": CustomTextsString(
                            None, need_use_localisation=False,
                            need_disable=True, need_enable=False,
                        ),
                    }
                )
            }
        )
    )

    whatsapp = CustomTextsField(
        CustomTextsDict(
            {
                "menu": CustomTextsDict(
                    {
                        "button": CustomTextsString(
                            need_disable=False,
                        ),
                    }
                ),
                "request_welcome_message": CustomTextsString(
                    need_disable=True,
                    display_text_variable="client bot whatsapp request welcome "
                                          "message display button",
                    edit_middle_text_variable="client bot whatsapp request welcome "
                                              "message edit middle text",
                )
            }
        )
    )

    SEPARATOR = "__"

    async def get_translator_settings(self) -> TranslatorSettings:
        group = await Group.get_by_bot(self.get_id())
        return TranslatorSettings(group.id, group.lang, group.is_translate)
