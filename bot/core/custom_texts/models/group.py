from core.custom_texts.models import (
    CustomTextsDict, CustomTextsModel, CustomTextsString,
)
from core.custom_texts.models.base.base import CustomTextsField, TranslatorSettings
from db.models import Group


class GroupCustomTextsModel(CustomTextsModel, scope_action_object_name="profile"):
    web = CustomTextsField(
        CustomTextsDict(
            {
                "auth": CustomTextsDict(
                    {
                        "helper_text": CustomTextsString(need_disable=True),
                        "external_login": CustomTextsDict(
                            {
                                "title": CustomTextsString(),
                                "desktop_instruction": CustomTextsString(
                                    need_disable=True
                                ),
                                "open_in_messanger_button": CustomTextsString(
                                    need_disable=False,
                                ),
                                "qr_instruction": CustomTextsString(
                                    need_disable=True
                                ),
                            }
                        )
                    }
                ),
                "menu": CustomTextsDict(
                    {
                        "shop_info_text": CustomTextsString(need_disable=False),
                        "product": CustomTextsDict(
                            {
                                "description_tab_text": CustomTextsString(),
                                "characteristics_tab_text": CustomTextsString(),
                                "attributes_tab_text": CustomTextsString(),
                            }
                        ),
                        "cart": CustomTextsDict(
                            {
                                "additions_text": CustomTextsString()
                            }
                        ),
                        "categories": CustomTextsDict(
                            {
                                "all_text": CustomTextsString(),
                                "choose_text": CustomTextsString(need_disable=True),
                            }
                        )
                    }
                ),
                "pages_headers": CustomTextsDict(
                    {
                        "base": CustomTextsString(),
                        "select": CustomTextsString(),
                        "menu": CustomTextsString(),
                    }
                ),
                "order": CustomTextsDict(
                    {
                        "sign_in_button": CustomTextsString(),
                        "guest_button": CustomTextsString(
                            need_disable=True,
                        ),
                        "or_text": CustomTextsString(
                            need_disable=True,
                        ),
                        "order_comment_label": CustomTextsString(
                            need_disable=True,
                        ),
                    }
                ),
                "loyalty": CustomTextsDict(
                    {
                        "referral_system": CustomTextsDict(
                            {
                                "info_button": CustomTextsString(),
                            }
                        ),
                        "register": CustomTextsDict(
                            {
                                "title": CustomTextsString(
                                    need_disable=True,
                                ),
                                "description": CustomTextsString(
                                    need_disable=True,
                                )
                            }
                        )
                    }
                ),
            }
        )
    )

    review = CustomTextsField(
        CustomTextsDict(
            {
                "proposal_text": CustomTextsString(need_disable=True),
                "leave_button": CustomTextsString(),
                "leave_header": CustomTextsString(),
                "service_notification_text": CustomTextsString(),
                "authorisation_required_text": CustomTextsString(),
            }
        )
    )

    store = CustomTextsField(
        CustomTextsDict(
            {
                "orders": CustomTextsDict(
                    {
                        "notifications": CustomTextsDict(
                            {
                                "message": CustomTextsDict(
                                    {
                                        "open_unconfirmed_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message open "
                                                                  "unconfirmed button",
                                        ),
                                        "open_confirmed_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message open "
                                                                  "confirmed button",
                                        ),
                                        "payed_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message payed "
                                                                  "button",
                                        ),
                                        "canceled_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message canceled "
                                                                  "button"
                                        ),
                                        "closed_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message closed "
                                                                  "button"
                                        ),
                                        "wait_for_ship_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message wait for "
                                                                  "ship button",
                                        ),
                                        "shipped_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message shipped "
                                                                  "button",
                                        ),
                                        "in_transit_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message in transit "
                                                                  "button"
                                        ),
                                        "delivered_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message delivered "
                                                                  "button",
                                        ),
                                        "gift_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message gift button",
                                        ),
                                        "topup_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "message gift button",
                                        ),
                                    }
                                ),
                                "header": CustomTextsDict(
                                    {
                                        "open_unconfirmed_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header open "
                                                                  "unconfirmed button",
                                        ),
                                        "open_confirmed_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header open "
                                                                  "confirmed button",
                                        ),
                                        "payed_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header payed button",
                                        ),
                                        "friend_payed_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header payed "
                                                                  "friend_button",
                                        ),
                                        "canceled_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header canceled "
                                                                  "button"
                                        ),
                                        "closed_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header closed button"
                                        ),
                                        "wait_for_ship_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header wait for "
                                                                  "ship button",
                                        ),
                                        "shipped_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header shipped "
                                                                  "button",
                                        ),
                                        "in_transit_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header in transit "
                                                                  "button"
                                        ),
                                        "delivered_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header delivered "
                                                                  "button",
                                        ),
                                        "gift_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header gift button",
                                        ),
                                        "topup_text": CustomTextsString(
                                            display_text_variable="group store orders "
                                                                  "notifications "
                                                                  "header gift button",
                                        ),
                                    }
                                ),
                                "footer_text": CustomTextsString(),
                            }
                        )
                    }
                )
            }
        )
    )

    ai = CustomTextsField(
        CustomTextsDict(
            {
                "start_message": CustomTextsString(),
            }
        )
    )

    async def get_translator_settings(self) -> TranslatorSettings:
        group = await Group.get(self.get_id())
        return TranslatorSettings(group.id, group.lang, group.is_translate)
