from db.models import Group
from . import CustomTextsString
from .base import CustomTextsDict, CustomTextsField, CustomTextsModel
from .base.base import TranslatorSettings


class MenuInStoreCustomTextsModel(CustomTextsModel, scope_action_object_name="qr_menu"):
    header_text = CustomTextsField(
        CustomTextsString(
            need_disable=True,
            display_text_variable="menu in store header text display"
        )
    )
    greeting_text = CustomTextsField(CustomTextsString(need_disable=True))
    comment_view_text = CustomTextsField(
        CustomTextsString(
            need_disable=True,
            display_text_variable="menu in store comment view text display"
        )
    )
    choose_action_text = CustomTextsField(CustomTextsString())

    choose_action_button = CustomTextsField(
        CustomTextsString(
            need_disable=False,
        )
    )

    menu = CustomTextsField(
        CustomTextsDict(
            {
                "order_button": CustomTextsString(),
                "order_button_description": CustomTextsString(need_disable=True),
                "order_link_text": CustomTextsString(),
                "buttons_group_text": CustomTextsString(),
                "delivery_pickup_button": CustomTextsString(None),
                "main_action_text": CustomTextsString(need_disable=True),
            }
        )
    )

    waiter = CustomTextsField(
        CustomTextsDict(
            {
                "call_button": CustomTextsString(),
                "call_button_description": CustomTextsString(need_disable=True),
                "call_text": CustomTextsString(),
                "called_text": CustomTextsString(),
                "buttons_group_text": CustomTextsString(),
            }
        )
    )

    contacts = CustomTextsField(
        CustomTextsDict(
            {
                "reviews_button": CustomTextsString(),
                "reviews_button_description": CustomTextsString(need_disable=True),
                "reviews_link_text": CustomTextsString(),
                "chat_button": CustomTextsString(),
                "chat_button_description": CustomTextsString(need_disable=True),
                "buttons_group_text": CustomTextsString(),
            }
        )
    )

    payments = CustomTextsField(
        CustomTextsDict(
            {
                "pay_button": CustomTextsString(),
                "pay_button_description": CustomTextsString(need_disable=True),
                "cash_button": CustomTextsString(),
                "card_button": CustomTextsString(),
                "online_button": CustomTextsString(),
                "pay_online_link_text": CustomTextsString(),
                "main_action_text": CustomTextsString(need_disable=True),
                "notifications": CustomTextsDict(
                    "cash_text",
                    "card_text",
                    "online_text",
                    "qr_text",
                    "sent_text",
                ),
            }
        )
    )

    active_menu = CustomTextsField(
        CustomTextsDict(
            {
                "open_button": CustomTextsString(),
                "no_more_button": CustomTextsString(),
                "no_more_button_description": CustomTextsString(need_disable=True),
                "left_text": CustomTextsString(),
            }
        )
    )

    redirect = CustomTextsField(
        CustomTextsDict(
            "only_tg_text",
            "try_tg_text",
        )
    )

    async def get_translator_settings(self) -> TranslatorSettings:
        group = await Group.get_by_menu_in_store(self.storage.id.split("-")[1])
        return TranslatorSettings(group.id, group.lang, group.is_translate)
