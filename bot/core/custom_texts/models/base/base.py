from __future__ import annotations

from abc import abstractmethod
from dataclasses import dataclass
from typing import Any, Iterable, Type, TypeVar

from psutils.copy_ import deep_copy_sa_dict
from psutils.dict import deep_dict_to_one_level, parse_one_level_dict
from psutils.func import kwargs_safe
from psutils.localisation import Localisation
from psutils.text import paschal_case_to_snake_case
from pydantic.fields import Undefined

from config import USE_LOCALISATION
from db import crud
from db.connection import Base
from db.models import CustomTextsStorage, Group
from utils.text import f, fd, fl
from utils.translator import t
from .types import CustomTextsString, CustomTextsType


class CustomTextsField:
    def __init__(
            self,
            type_: CustomTextsType | Type[CustomTextsType] | None = None,
    ):
        if type_ is None:
            self.type: CustomTextsType = CustomTextsString()
        elif isinstance(type_, type):
            self.type: CustomTextsType = type_()
        else:
            self.type: CustomTextsType = type_

        self.name: str | None = None
        self.owner: Type[CustomTextsModel] | None = None

    def set_owner_and_name(self, owner: CustomTextsModelMeta, name: str):
        self.name = name
        self.owner = owner

    def __get__(
            self, instance: CustomTextsModel | None, owner: Type[CustomTextsModel]
    ) -> CustomTextsFieldProxy:
        if not isinstance(instance, CustomTextsModel | None):
            raise TypeError("CustomTextsField can be used only in CustomTextsModel")
        return CustomTextsFieldProxy(instance, owner, self)


class CustomTextsFieldProxy:
    def __init__(
            self, instance: CustomTextsModel, owner: Type[CustomTextsModel],
            field: CustomTextsField
    ):
        self.instance: CustomTextsModel = instance
        self.owner: Type[CustomTextsModel] = owner
        self.field: CustomTextsField = field

    @property
    def name(self):
        return self.field.name

    @property
    def type(self) -> CustomTextsType:
        return self.field.type

    async def get_current_value(
            self,
            default: Any = None,
            raw: bool = False,
            **kwargs,
    ):
        data = await self.instance.get_data(raw, **kwargs)
        return data.get(self.name, default)

    async def get_value(
            self, key_parts: Iterable[str],
            raw: bool = False,
            param_name: str | None = None,
            **kwargs,
    ):
        data = await self.get_current_value(
            Undefined, raw, **kwargs,
        )
        return self.type.get_value(data, *key_parts, param_name=param_name)

    async def make_new_data(
            self, new_value: Any, *key_parts: str, param_name: str | None = None
    ):
        current_value = await self.get_current_value(raw=True)
        return await self.type.make_new_data(
            new_value, current_value, *key_parts, param_name=param_name
        )

    def get_key_type(self, *key_parts: str):
        return self.type.get_key_type(*key_parts)

    def get_keys(self, *key_parts: str):
        return self.type.get_keys(*key_parts)

    def get_all_keys(self):
        return self.type.get_all_keys()


class CustomTextsModelMeta(type):
    models: dict[str, Type[CustomTextsModel]] = {}
    fields_names: list[str]
    localisation_prefix: str
    scope_action_object_name: str

    def __new__(mcs, name, bases, namespace, **kwargs):
        cls = super().__new__(mcs, name, bases, namespace)

        fields_names: list[str] = []

        for prop_name, prop in namespace.items():
            if isinstance(prop, CustomTextsField):
                prop.set_owner_and_name(cls, prop_name)
                fields_names.append(prop_name)

        obj_name = cls.__name__.replace(
            "CustomTextsModel", ""
        )  # if not specified, use class name
        localisation_prefix = kwargs.pop(
            "localisation_prefix",
            paschal_case_to_snake_case(obj_name).replace("_", " "),
        )

        cls.scope_action_object_name = kwargs.pop(
            "scope_action_object_name", paschal_case_to_snake_case(obj_name)
        )
        cls.fields_names = fields_names
        cls.localisation_prefix = localisation_prefix
        cls.models[obj_name] = cls  # type: ignore

        return cls

    def detect_model(cls, obj_or_name: CustomTextsModel | Base | str) -> Type[
        CustomTextsModel]:
        if cls is not CustomTextsModel:
            return cls  # type: ignore

        if isinstance(obj_or_name, CustomTextsModel):
            return obj_or_name.__class__

        if not isinstance(obj_or_name, str):
            name = obj_or_name.__class__.__name__
        else:
            name = obj_or_name

        if name not in cls.models:
            raise KeyError(f"Model with name {name} not found")

        return cls.models[name]

    @property
    def fields(cls) -> dict[str, CustomTextsFieldProxy]:
        return {field_name: getattr(cls, field_name) for field_name in cls.fields_names}


class CustomTextsModel(metaclass=CustomTextsModelMeta):
    reset_excluding: tuple[str] | None = None
    fields: dict[str, CustomTextsFieldProxy]

    def __init__(
            self, custom_texts_storage: CustomTextsStorage,
    ):
        self.storage: CustomTextsStorage = custom_texts_storage
        self._cache: dict[str, dict[str, Any]] = {}

    def __init_subclass__(cls, **kwargs):
        if not kwargs.pop("base", False):
            cls.get_custom_keyboard = kwargs_safe(cls.get_custom_keyboard)
        super().__init_subclass__(**kwargs)

    async def get_data(self, raw: bool = False, **kwargs):
        if raw:
            return deep_copy_sa_dict(self.storage.data)

        translator_settings = await self.get_translator_settings()

        lang = kwargs.pop("lang")  # must be no default value

        if translator_settings.original_lang == lang:
            return deep_copy_sa_dict(self.storage.data)

        if lang not in self._cache:
            data = await t(
                self.storage,
                lang, translator_settings.original_lang,
                group_id=translator_settings.group_id,
                is_auto_translate_allowed=translator_settings.is_auto_translate_allowed,
                **kwargs,
            )
            self._cache[lang] = data

        return deep_copy_sa_dict(self._cache[lang])

    def clear_cache(self, lang: str | None = None):
        if lang:
            self._cache.pop(lang, None)
        else:
            self._cache.clear()

    def get_field(
            self: CustomTextsModel | Type[CustomTextsModel], field_name: str
    ) -> CustomTextsFieldProxy:
        if field_name not in self.fields_names:
            raise ValueError(f"Unknown field_name: {field_name}")

        return getattr(self, field_name)

    @classmethod
    def get_key_type(cls, field_name: str, *key_parts: str):
        field_name, key_parts = convert_field_name_and_key_parts(field_name, key_parts)
        return cls.get_field(cls, field_name).get_key_type(*key_parts)

    @classmethod
    def get_keys(cls, field_name: str, *key_parts: str):
        field_name, key_parts = convert_field_name_and_key_parts(field_name, key_parts)
        return cls.get_field(cls, field_name).get_keys(*key_parts)

    async def get_value(
            self,
            field_name: str,
            key_parts: Iterable[str] | None = None,
            lang: str | None = None,
            raw: bool | None = None,
            param_name: str | None = None,
            text_kwargs: dict[str, Any] | None = None,
            return_use_localisation: bool = False,
            **kwargs,
    ) -> Any:
        if raw is None:
            raw = param_name not in (None, "value")

        if text_kwargs is None:
            text_kwargs = {}

        if key_parts is None:
            key_parts = ()

        field_name, key_parts = convert_field_name_and_key_parts(field_name, key_parts)

        field = self.get_field(field_name)
        field_value = await field.get_value(key_parts, True, param_name)

        if field_value is Undefined:
            return None

        if raw:
            return field_value

        if not lang:
            raise ValueError("lang is required when raw is False")

        if field_value == USE_LOCALISATION:
            if return_use_localisation:
                return field_value
            return await self.get_field_localisation_name(
                lang, field_name, *key_parts, **text_kwargs
            )

        if not field_value:
            return field_value

        field_value = await field.get_value(key_parts, False, **kwargs, lang=lang)

        if isinstance(field_value, str):
            field_value = Localisation.replace_text(field_value, text_kwargs, lang)

        return field_value

    async def update_data(
            self, new_value: Any,
            field_name: str, *key_parts: str,
            param_name: str | None = None,
            translations: dict[str, str | None] | None = Undefined,
            # if None specified — translations will be reset
    ):
        field_name, key_parts = convert_field_name_and_key_parts(field_name, key_parts)

        field = self.get_field(field_name)
        new_field_data, is_changed = await field.make_new_data(
            new_value, *key_parts, param_name=param_name
        )

        await crud.update_custom_text(
            self.storage, [field_name, *key_parts],
            new_value, new_field_data, is_changed,
            translations
        )

    @classmethod
    def get_field_localisation_variable(
            cls,
            field_name: str,
            *key_parts,
            display: bool = False,
    ):
        field = cls.get_field(cls, field_name)
        type_ = field.get_key_type(*key_parts)
        return type_.get_localisation_variable(
            cls.localisation_prefix, field_name, *key_parts, display=display
        )

    @classmethod
    def get_field_localisation_name(
            cls, lang: str, field_name: str,
            *key_parts,
            display: bool = False,
            **text_kwargs,
    ):
        variable = cls.get_field_localisation_variable(
            field_name, *key_parts, display=display
        )
        return f(variable, lang, **text_kwargs)

    @classmethod
    async def from_storage_id(cls, id: str):
        storage = await CustomTextsStorage.get_or_create(id=id)
        return cls(storage)

    @classmethod
    async def from_object(cls, object_: CustomTextsModel | CustomTextsStorage | Base):
        model = cls.detect_model(object_)
        if isinstance(object_, CustomTextsModel):
            return object_

        if isinstance(object_, CustomTextsStorage):
            storage = object_
        else:
            storage = await CustomTextsStorage.get_or_create(object_=object_)
        return model(storage)

    async def to_dict(self, lang: str, raw: bool = False, **kwargs) -> dict[str, Any]:
        result = {}

        for field_name in self.fields_names:
            field = self.get_field(field_name)
            field_keys_list = field.get_all_keys()

            if field_keys_list:
                field_data = {}
                for field_keys in field_keys_list:
                    value = await self.get_value(
                        field_name, field_keys, lang,
                        return_use_localisation=True,
                        **kwargs
                    )

                    current = field_data
                    for key in field_keys[:-1]:
                        if key not in current:
                            current[key] = {}
                        current = current[key]

                    if not raw and value == USE_LOCALISATION:
                        value = {
                            "__to_localise__": self.get_field_localisation_variable(
                                field_name, *field_keys,
                            )
                        }

                    current[field_keys[-1]] = value
            else:
                field_data = await self.get_value(
                    field_name, lang=lang,
                    return_use_localisation=True,
                    **kwargs
                )
                if not raw and field_data == USE_LOCALISATION:
                    field_data = {
                        "__to_localise__": self.get_field_localisation_variable(
                            field_name
                        )
                    }

            result[field_name] = field_data

        if raw:
            return result

        to_localise: dict[tuple[str, ...], str] = {}

        def process(
                key_: str, hierarchy: tuple[str, ...],
                value_: dict[str, Any] | str | None
        ):
            if not isinstance(value_, dict):
                return value_

            current_hierarchy = (*hierarchy, key_)

            if "__to_localise__" in value_:
                to_localise[current_hierarchy] = value_["__to_localise__"]
            else:
                for sub_key, sub_key_value in value_.items():
                    process(sub_key, current_hierarchy, sub_key_value)

        for key, value in result.items():
            process(key, (), value)

        if to_localise:
            variables = []
            keys_indexes: dict[tuple[str, ...], int] = {}
            for key, variable in to_localise.items():
                if variable not in variables:
                    variables.append(variable)
                keys_indexes[key] = variables.index(variable)

            localised = await fl(variables, lang)

            for key_hierarchy, localised_index in keys_indexes.items():
                current = result
                for key in key_hierarchy[:-1]:
                    current = current[key]
                current[key_hierarchy[-1]] = localised[localised_index]

        return result

    async def to_schema(self, group: Group):
        to_localise = {}

        for field_name in self.fields_names:
            to_localise[field_name] = self.get_key_type(
                field_name
            ).get_to_localise_for_schema(
                self.localisation_prefix, field_name
            )

        separator = "__"
        to_localise = deep_dict_to_one_level(to_localise, separator)
        localised = parse_one_level_dict(
            await fd(to_localise, group.lang), separator
        )

        data = await self.get_data(True)
        translated_data = {
            lang: await self.get_data(
                lang=lang,
                fallback_to_original_on_error=False,
            )
            for lang in group.get_langs_list(False)
        }

        return [
            self.get_key_type(field_name).build_schema(
                (field_name,),
                data.get(field_name, Undefined),
                localised.get(field_name),
                {lang: d.get(field_name) for lang, d in translated_data.items()}
            )
            for field_name in self.fields_names
        ]

    def get_id(self):
        return int(self.storage.id.split("-")[1])

    @abstractmethod
    async def get_translator_settings(self) -> TranslatorSettings:
        raise NotImplementedError

    async def get_custom_keyboard(self, **kwargs):
        pass

    def __repr__(self):
        return (
            f"<{self.__class__.__name__}\n"
            f"data={self.storage.data}"
            f">"
        )


@dataclass
class TranslatorSettings:
    group_id: int
    original_lang: str
    is_auto_translate_allowed: bool


def convert_field_name_and_key_parts(field_name: str, key_parts: Iterable[str]) -> \
        tuple[str, tuple[str, ...]]:
    field_name = field_name.replace(" ", "_")
    key_parts = tuple(el.replace(" ", "_") for el in key_parts)
    return field_name, key_parts


CustomTextsModelT = TypeVar("CustomTextsModelT", bound=CustomTextsModel)
