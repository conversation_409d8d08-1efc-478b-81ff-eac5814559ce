from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any

from pydantic.fields import Undefined

from config import USE_LOCALISATION
from schemas import (
    CustomTextDictSchema, CustomTextParamTypeLiteral, CustomTextSchema,
    CustomTextStringSchema,
    CustomTextStringTranslationSchema,
)


class CustomTextsType(ABC):
    def __init__(
            self, *,
            display_text_variable: str | None = None,
            additional_params: tuple[
                                   CustomTextParamTypeLiteral, ...] |
                               CustomTextParamTypeLiteral | None = None,
            need_use_localisation: bool = True,
            need_disable: bool | None = None,
            need_enable: bool = True,
    ):
        # text variable for button in admin bot interface
        self.display_text_variable: str | None = display_text_variable

        if isinstance(additional_params, str):
            additional_params = (additional_params,)
        self.additional_params: tuple[
                                    CustomTextParamTypeLiteral, ...] | None = (
            additional_params)
        self.need_use_localisation: bool = need_use_localisation
        self.need_disable: bool | None = need_disable
        self.need_enable: bool = need_enable

    @abstractmethod
    def get_key_type(self, key: str | None = None, *sub_keys: str) -> CustomTextsType:
        """
        Get a key type
        @param key:
        @param sub_keys:
        @return: Type of key or subkey
        """
        raise NotImplementedError

    def get_keys(self, key: str | None = None, *sub_keys: str) -> dict[
        str, CustomTextsType]:
        """
        Get sub keys for a type
        @param key:
        @param sub_keys:
        @return:
        """
        raise NotImplementedError

    def get_localisation_variable(
            self, localisation_prefix: str, *path: str, display: bool = False
    ):
        if display and self.display_text_variable is not None:
            variable = self.display_text_variable
        else:
            variable = " ".join((localisation_prefix, *path))
        return variable

    @abstractmethod
    def get_additional_params(self, key: str | None = None, *sub_keys: str) -> tuple[
                                                                                   CustomTextParamTypeLiteral] | None:
        raise NotImplementedError

    @abstractmethod
    def get_value(
            self, data: Any,
            key: str | None = None,
            *sub_keys: str,
            param_name: str | None = None,
    ) -> Any:
        """
        Returns current value for a type by key and sub_keys
        :param data: current data
        :param key: key to return value
        :param sub_keys: sub keys for subtype
        :param param_name: param name, default "value"
        :return: value for a key in data
        """
        raise NotImplementedError

    @abstractmethod
    def get_to_localise_for_schema(
            self,
            localisation_prefix: str,
            *path: str,
    ) -> dict[str, Any]:
        """
        Returns dict of variables to localise for schema.
        Will be passed to get_schema_params_from_localised
        """
        raise NotImplementedError

    @abstractmethod
    def build_schema(
            self,
            path: tuple[str, ...],
            data: Any,
            localised: dict[str, Any],
            translations: dict[str, Any]
    ) -> CustomTextSchema:
        """
        Returns dict of localised CustomTextSchema fields
        @return:
        """

    def get_all_keys(self, only_last: bool = True) -> list[tuple[str]] | None:
        """
        Get all keys with a sub keys
        :param: only_last: adds only last types in hierarchy
        :return: list of a key list in tuple
        """
        raise NotImplementedError

    @abstractmethod
    async def make_new_data(
            self, new_value: Any,
            current_value: Any = None,
            key: str | None = None,
            *sub_keys: str,
            param_name: str | None = None,
    ) -> tuple[Any, bool]:
        """
        Makes new_data by new_value
        :param new_value: dict[str, str | None] | str | None
        :param current_value: current value
        :param key: key to update
        :param sub_keys: sub keys for subtype
        :param param_name: param name
        :return: returns new data dict and is_changed boolean value
        """
        raise NotImplementedError


class CustomTextsString(CustomTextsType):

    def __init__(
            self,
            default: Any = USE_LOCALISATION,
            edit_middle_text_variable: str | None = None,
            **kwargs,
    ):
        self.default = default

        # text variable for middle text in edit header in admin bot interface
        self.edit_middle_text_variable = edit_middle_text_variable

        super().__init__(**kwargs)

    def get_key_type(self, key: str | None = None, *sub_keys: str):
        if key or sub_keys:
            raise ValueError("key and sub_keys is not supported for CustomTextsString")
        return self

    def get_value(
            self, data: Any,
            key: str | None = None,
            *sub_keys,
            param_name: str | None = None,
    ) -> Any:
        if param_name is None:
            param_name = "value"

        if key or sub_keys:
            raise ValueError(
                "key and sub_keys cannot be specified for CustomTextsString"
            )

        if data is Undefined:
            return self.default if param_name == "value" else None

        if isinstance(data, dict):
            return data.get(param_name)

        if not isinstance(data, str | None):
            raise ValueError("Invalid data for CustomTextsString")

        if param_name == "value":
            return data
        return None

    def get_to_localise_for_schema(
            self,
            localisation_prefix: str,
            *path: str,
    ):
        result = {}

        if self.display_text_variable:
            result["display_text"] = self.display_text_variable

        if (
                not self.display_text_variable or
                self.need_use_localisation or
                self.default == USE_LOCALISATION
        ):
            result["localisation_text"] = self.get_localisation_variable(
                localisation_prefix, *path
            )

        return result

    def build_schema(
            self,
            path: tuple[str, ...],
            data: Any,
            localised: dict[str, str],
            translations: dict[str, Any]
    ) -> CustomTextStringSchema:
        localisation_value = localised["localisation_text"]
        value = self.get_value(data)
        if value == USE_LOCALISATION:
            value = localisation_value

        default_value = self.default
        if default_value == USE_LOCALISATION:
            default_value = localisation_value

        additional_params_names = self.get_additional_params()
        additional_params = {
            param_name: self.get_value(data, param_name=param_name) for param_name in
            additional_params_names
        } if additional_params_names else None

        is_default = value == default_value

        translations = {
            lang: CustomTextStringTranslationSchema(
                value=value.get("value") if isinstance(value, dict) else value,
            )
            for lang, value in translations.items()
        } if not is_default else None

        return CustomTextStringSchema(
            type="string",
            is_button=path[-1].endswith("_button"),
            path=path,
            display_text=localised.get("display_text", value or localisation_value),
            display_text_raw=localised.get(
                "display_text"
            ) if self.display_text_variable else None,
            value=value,
            localisation_value=localisation_value,
            default_value=default_value,
            edit_middle_text_variable=(
                self.edit_middle_text_variable.replace(" ", "_").lower()
                if self.edit_middle_text_variable else None
            ),
            need_use_localisation=self.need_use_localisation,
            need_disable=self.need_disable if self.need_disable is not None else path[
                -1].endswith("button"),
            need_enable=self.need_enable,
            additional_params=additional_params,
            is_default=is_default,
            translations=translations,
        )

    def get_all_keys(self, only_last: bool = True) -> None:
        return None

    def get_additional_params(self, key: str | None = None, *sub_keys: str) -> tuple[
                                                                                   CustomTextParamTypeLiteral] | None:
        if key or sub_keys:
            raise ValueError(
                "key and sub_keys cannot be specified for CustomTextsString"
            )

        return self.additional_params

    async def make_new_data(
            self, new_value: Any,
            current_value: Any = None,
            key: str | None = None,
            *sub_keys,
            param_name: str | None = None
    ) -> tuple[Any, bool]:
        if param_name is None:
            param_name = "value"

        if key:
            raise ValueError(
                "key and sub keys cannot be specified for CustomTextsString"
            )

        if isinstance(current_value, dict):
            current_param_value = current_value.get(param_name)
        elif current_value and param_name == "value":
            current_param_value = current_value
        else:
            current_param_value = self.default if param_name == "value" else None

        is_changed = current_param_value != new_value

        if new_value is not None:
            new_value = str(new_value)

        if isinstance(current_value, dict):
            return {**current_value, param_name: new_value}, is_changed

        if param_name != "value" and current_value:
            return {"value": current_value, param_name: new_value}, is_changed

        return {param_name: new_value}, is_changed


class CustomTextsDict(CustomTextsType):

    def __init__(
            self,
            *keys: str | tuple[str, CustomTextsType] | dict[str, CustomTextsType],
            **kwargs
    ):
        if len(keys) == 1 and isinstance(keys[0], dict):
            keys = keys[0]

        if isinstance(keys, dict):
            self.keys: dict[str, CustomTextsType] = keys
        else:
            self.keys: dict[str, CustomTextsType] = {}
            for key in keys:
                if isinstance(key, str):
                    self.keys[key] = CustomTextsString()
                elif isinstance(key, tuple):
                    if (
                            len(key) != 2 or
                            not isinstance(key[0], str) or
                            not isinstance(key[1], CustomTextsType)
                    ):
                        raise ValueError(
                            "tuple must be of 2 items: name and CustomTextsType"
                        )

                    self.keys[key[0]] = key[1]
                else:
                    raise TypeError(
                        f"key must be a string or tuple[str, CustomTextsType], "
                        f"not {type(key)}"
                    )

        super().__init__(**kwargs)

    def get_key_type(self, key: str | None = None, *sub_keys: str):
        if not key:
            return self

        if key not in self.keys:
            raise KeyError(f"Invalid key: {key}. {key} not in keys")

        return self.keys[key].get_key_type(*sub_keys)

    def get_keys(self, key: str | None = None, *sub_keys: str):
        if not key:
            return self.keys

        if key not in self.keys:
            raise KeyError(f"Invalid key: {key}. {key} not in keys")

        return self.keys[key].get_keys(*sub_keys)

    def validate_data(self, data: Any):
        if data is not Undefined and not isinstance(data, dict | None):
            raise ValueError(
                f"data must be a dict or None or Undefined, not {type(data)}"
            )

    def get_value(
            self, data: dict[str, Any] | None,
            key: str | None = None,
            *sub_keys,
            param_name: str | None = None,
    ) -> Any:
        self.validate_data(data)

        if not key:
            return data

        if key not in self.keys:
            raise KeyError(f"Invalid key: {key}. {key} not in keys")

        if isinstance(data, dict):
            current_data = data.get(key, Undefined)
        else:
            current_data = Undefined

        type_ = self.keys[key]
        return type_.get_value(current_data, *sub_keys, param_name=param_name)

    def get_to_localise_for_schema(
            self,
            localisation_prefix: str,
            *path: str,
    ):
        result = {
            "display_text": self.get_localisation_variable(
                localisation_prefix, *path, display=True
            ),
        }

        for key, key_type in self.keys.items():
            result[key] = key_type.get_to_localise_for_schema(
                localisation_prefix, *path, key
            )
        return result

    def build_schema(
            self,
            path: tuple[str, ...],
            data: Any,
            localised: dict[str, Any],
            translations: dict[str, Any],
    ) -> CustomTextDictSchema:
        items: list[CustomTextSchema] = []

        for key, key_type in self.keys.items():
            key_localised = localised.get(key)
            key_data = data.get(key, Undefined) if isinstance(data, dict) else data
            key_translations = {lang: t.get(key) if t else None for lang, t in
                                translations.items()}
            items.append(
                key_type.build_schema(
                    (*path, key), key_data, key_localised, key_translations
                )
            )

        return CustomTextDictSchema(
            type="dict",
            path=path,
            display_text=localised["display_text"],
            display_text_raw=localised[
                "display_text"] if self.display_text_variable else None,
            items=items,
        )

    def get_all_keys(self, only_last: bool = True) -> list[tuple[str]]:
        result = []

        for key, type_ in self.keys.items():
            sub_keys_list = type_.get_all_keys(only_last)

            if sub_keys_list is None or not only_last:
                result.append((key,))

            if sub_keys_list is not None:
                for sub_keys in sub_keys_list:
                    result.append((key, *sub_keys))

        return result

    def get_additional_params(self, key: str | None = None, *sub_keys: str) -> tuple[
                                                                                   CustomTextParamTypeLiteral] | None:
        if not key:
            return self.additional_params

        if key not in self.keys:
            raise KeyError(f"Invalid key: {key}. {key} not in keys")

        type_ = self.keys[key]
        return type_.get_additional_params(*sub_keys)

    async def make_new_data(
            self, new_value: Any,
            current_value: dict[str, Any] | None = None,
            key: str | None = None,
            *sub_keys: str,
            param_name: str | None = None,
    ) -> tuple[Any, bool]:
        if not key:
            raise ValueError("key is required for CustomTextsDict.make_new_data")

        if key not in self.keys:
            raise KeyError(f"Invalid key: {key}. {key} not in keys")

        if current_value is None:
            key_current_value = None
        elif isinstance(current_value, dict):
            key_current_value = current_value.get(key)
        else:
            raise ValueError(
                f"current_value must be a dict or None, not {type(current_value)}"
            )

        type_ = self.keys[key]
        key_new_value, is_changed = await type_.make_new_data(
            new_value, key_current_value,
            *sub_keys, param_name=param_name,
        )

        new_data = {key: key_new_value}

        if current_value:
            new_data = {
                **{key: value for key, value in current_value.items() if
                   key in self.keys}, **new_data
            }

        return new_data, is_changed
