from incust_api.api import term

import schemas
from core.loyalty.incust_api import incust
from db import crud
from db.models import (
    Brand, Organisation, Receipt, Store, StoreOrder,
    User,
)


async def receipt_to_schema(
        receipt: Receipt, user: User,
        brand: Brand, lang: str,
        store_id: int | None,
) -> schemas.ReceiptSchema:
    incust_vouchers = None
    if receipt.incust_transaction and receipt.incust_transaction.get('id'):
        if not store_id:
            if receipt.store_order_id:
                store_order = await StoreOrder.get(receipt.store_order_id)
                if store_order:
                    store = await Store.get(store_order.store_id)
                    if store:
                        store_id = store.id

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "store" if store_id else "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                store_id=store_id,
                profile_id=brand.group_id,
            )
        )

        if loyalty_settings:
            try:
                transaction_id = receipt.incust_transaction['id']
                async with incust.term.CheckTransactionsApi(loyalty_settings) as api:
                    check_result = await api.transaction_check(transaction_id)
                if check_result and hasattr(
                        check_result, 'emitted_coupons'
                ) and check_result.emitted_coupons:
                    # Збагачуємо купони для відображення
                    from core.loyalty import coupon_service
                    
                    incust_vouchers = []
                    for coupon in check_result.emitted_coupons:
                        enriched_coupon = await coupon_service.prepare_coupon_for_display(
                            coupon,
                            brand,
                            loyalty_settings,
                            user,
                            lang,
                        )
                        if enriched_coupon:
                            incust_vouchers.append(enriched_coupon)
            except Exception:
                pass

    org: Organisation = await Organisation.get_by_id(receipt.organisation_id)

    return schemas.ReceiptSchema(
        id=receipt.id,
        receipt_id=receipt.receipt_id,
        incust_check=term.m.Check(
            **receipt.incust_check
        ) if receipt.incust_check else None,
        vouchers=incust_vouchers,
        total_price=receipt.total_price,
        bin_code=org.ico_code if org else None,
    )
