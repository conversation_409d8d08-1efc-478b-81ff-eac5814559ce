from contextlib import asynccontextmanager
import logging
from typing import Literal

from core import messangers_adapters as ma

import config as cfg

from geopy import Location
from geopy.adapters import AioHTTPAdapter
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut


def location_to_str(message: ma.Message) -> str:
    coordinates = message.location
    return ", ".join([str(coordinates.latitude), str(coordinates.longitude)])


@asynccontextmanager
async def get_locator() -> Nominatim:
    yield Nominatim(
        user_agent=cfg.APP_NAME,
        timeout=cfg.LOCATION_TIMEOUT,
        adapter_factory=AioHTTPAdapter,
    )


async def _get_location(coordinates: str, lang: str | bool = False) -> Location:
    async with get_locator() as locator:
        location = await locator.reverse(coordinates, language=lang)
        return location


async def get_location(message: ma.Message, lang: str | bool = False) -> str:
    coordinates = location_to_str(message)
    location = await _get_location(coordinates, lang)
    return location.raw["display_name"]


async def get_address(
        message: ma.Message,
        lang: str = "en",
        return_data: Literal[
            "full", "country", "country_code", "city",
            "municipality", "district", "state", "postcode",
            "road", "residential", "suburb", "borough",
        ] = "full",
) -> dict | str:
    coordinates = location_to_str(message)
    location = await _get_location(coordinates, lang)
    address = location.raw["address"]
    if return_data == "full":
        return address
    return address.get(return_data)


async def search(
        message: ma.Message,
        lang: str | bool = False,
        exactly_one: bool = False
) -> str | list[str]:
    return await _search(message.text, lang, exactly_one=exactly_one, return_type="address")


async def _search(
        search_text: str,
        lang: str | bool = False,
        exactly_one: bool = False,
        return_type: Literal["raw", "address"] = "raw"
) -> str | list[str] | Location:
    async with get_locator() as locator:
        try:
            results = await locator.geocode(search_text, exactly_one=exactly_one, language=lang)
        except GeocoderTimedOut as e:
            logger = logging.getLogger()
            logger.error("geocode fail with error: %s", e)
            results = None
        finally:
            if results and return_type == "address":
                results = results.address if exactly_one else [result.address for result in results]
            return results


async def get_coordinates(
        address: str,
        lang: str | bool = False
) -> tuple[str, str] | None:
    result = await _search(address, lang, exactly_one=True)
    if result:
        result = [result.latitude, result.longitude]
    return result
