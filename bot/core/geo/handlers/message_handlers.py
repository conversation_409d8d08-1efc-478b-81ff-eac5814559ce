from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.types import ContentTypes

from ..api import search, get_location
from ..states import Geo


async def geo_search_handler(message: types.Message, state: FSMContext, lang: str):
    if message.text == "/exit":
        await state.finish()
        return

    result = await search(message, lang)
    await message.answer(result)


async def location_button_handler(message: types.Message, lang: str):
    location = await get_location(message, lang)
    await message.answer(location)


def register_geo_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        geo_search_handler,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=Geo.GeoSearch,
    )
