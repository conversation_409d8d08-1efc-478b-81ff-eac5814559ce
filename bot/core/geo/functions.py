from math import radians, sin, cos, sqrt, atan2
from typing import Literal
from functools import partial

from geopy.distance import lonlat
from geopy.distance import distance as geopy_distance

from shapely.geometry import Point, Polygon, mapping
from shapely.ops import transform

import pyproj


def distance(
        point1: list, point2: list,
        units: Literal["km", "m", "mi"] = "km",
        ndigits: int = 3
) -> float | str:
    if isinstance(point1[0], float) and isinstance(point1[1], float):
        point1 = lonlat(*point1)
    else:
        point1 = ", ".join(point1)

    if isinstance(point2[0], float) and isinstance(point2[1], float):
        point2 = lonlat(*point2)
    else:
        point2 = ", ".join(point2)

    dist = geopy_distance(point1, point2)
    try:
        dist = getattr(dist, units)
    except:
        dist = dist.km

    if dist < 1:
        dist = dist.m
        ndigits = 2

    dist = round(dist, ndigits=ndigits)
    return float(dist)


def from_polygon(polygon: Polygon) -> list:
    data = mapping(polygon)
    return list(data["coordinates"][0])


def to_polygon(points: list) -> Polygon:
    return Polygon(points)


def check_inside(polygon: list, point: list, is_swap_coordinates: bool = True) -> bool:
    polygon = to_polygon(polygon)

    if isinstance(point[0], str) and isinstance(point[1], str):
        point = list(map(float, point))

    if is_swap_coordinates:
        point = Point(*point)
    else:
        point = Point(*[point[1], point[0]])

    return polygon.contains(point)


def check_in_distance(center_point: list, distance: int, point: list) -> bool:
    if isinstance(center_point[0], str) and isinstance(center_point[1], str):
        center_point = list(map(float, center_point))

    if isinstance(point[0], str) and isinstance(point[1], str):
        point = list(map(float, point))

    return is_point_inside_radius(center_point[0], center_point[1], point[0], point[1], distance)


def buffer_in_meters(center_point: Point, radius: int):
    proj_meters = pyproj.Proj(init="epsg:3857")
    proj_latlng = pyproj.Proj(init="epsg:4326")

    project_to_meters = partial(pyproj.transform, proj_latlng, proj_meters)
    project_to_latlng = partial(pyproj.transform, proj_meters, proj_latlng)

    pt_meters = transform(project_to_meters, center_point)
    buffer_meters = pt_meters.buffer(radius)
    buffer_latlng = transform(project_to_latlng, buffer_meters)

    return buffer_latlng


def polygon_from_string(polygon: str, is_swap_coordinates: bool) -> list:
    polygon = polygon.strip()
    if polygon.startswith("POLYGON"):
        polygon = polygon.replace("POLYGON", "")
    polygon = polygon.replace("(", "").replace(")", "")

    points = polygon.split(",")
    points = [list(map(float, point.split(" "))) for point in points]

    if is_swap_coordinates:
        for point in points:
            point[0], point[1] = point[1], point[0]

    return check_polygon_points_value(points)


def check_polygon_points_value(points: list) -> list | None:
    for point in points:
        if (-180 < float(point[0]) < 180) and (-180 < float(point[1]) < 180):
            if (-90 < float(point[0]) < 90) and (-90 < float(point[1]) < 90):
                continue
            elif (-90 < float(point[0]) < 90) or (-90 < float(point[1]) < 90):
                continue
            else:
                return None
        else:
            return None
    return points


def haversine(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    # Radius of the Earth in meters
    r = 6371000.0

    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    res_distance = r * c

    return res_distance


def is_point_inside_radius(
        center_lat: float, center_lon: float,
        point_lat: float, point_lon: float,
        radius: int,
) -> bool:
    res_distance = haversine(center_lat, center_lon, point_lat, point_lon)
    return res_distance <= radius
