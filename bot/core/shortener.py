import logging
from datetime import datetime, timedelta

import schemas
from db import crud
from db.crud.short_link.create import NoShortLinkAvailableError
from utils.platform_admins import send_message_to_platform_admins

logger = logging.getLogger("debugger.shortener")
DELAY_BETWEEN_NOTIFICATIONS = timedelta(minutes=1)
last_notification_datetime = datetime(1970, 1, 1, 0, 0, 0)


async def create_short_link(
        type: schemas.ShortLinkType,
        url: str | None = None,
        max_uses: int | None = None,
        expiration_datetime: datetime | None = None,
        display_id: str | None = None,  # will be auto generated if empty
):
    global last_notification_datetime
    logger.debug(f"Attempting to create short link: type={type}, url={url[:50] + '...' if url and len(url) > 50 else url}")
    
    try:
        result = await crud.create_short_link(
            type, url, max_uses, expiration_datetime, display_id
        )
        if result:
            logger.debug(f"Successfully created short link: {result.display_id}")
        else:
            logger.warning(f"create_short_link returned None without exception! type={type}, url={url}")
        return result
    except NoShortLinkAvailableError as e:
        logger.error(f"No short link available: {e}")
        if last_notification_datetime + DELAY_BETWEEN_NOTIFICATIONS < datetime.utcnow():
            await send_message_to_platform_admins(
                "Unable to create short link. No short link available."
            )
            last_notification_datetime = datetime.utcnow()
        return None
    except Exception as e:
        logger.error(f"Unexpected error creating short link: {e}", exc_info=True)
        raise
