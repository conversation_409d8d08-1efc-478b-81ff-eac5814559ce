from copy import deepcopy

from sqlalchemy.sql import func

from core.payment.payment_processor.providers.stripe import \
    create_or_update_stripe_webhook
from db import DBSession
from db.models import PaymentSettings


async def migrate_stripe():
    with DBSession() as sess:
        pks_data = {}

        payment_settings = sess.query(PaymentSettings).filter(
            PaymentSettings.payment_method == 'stripe'
        ).filter(
            ~func.json_extract(PaymentSettings.json_data, '$.secret_key').is_(None),
            ~func.json_extract(
                PaymentSettings.json_data, '$.stripe_endpoint_secret'
            ).is_(None)
        ).all()

        for payment_setting in payment_settings:
            await upd_webhook_secret(payment_setting, pks_data, sess)


async def upd_webhook_secret(payment_setting, pks_data, sess):
    secret_key = payment_setting.json_data.get("secret_key")
    if secret_key not in pks_data:
        data = deepcopy(payment_setting.json_data)
        data["stripe_endpoint_secret"] = create_or_update_stripe_webhook(secret_key)
        print(f"Webhook UPDATED for {secret_key=}")
        pks_data[secret_key] = data
    if secret_key in pks_data:
        payment_setting.json_data = pks_data[secret_key]
        sess.commit()
        print(
            f"data UPDATED for {payment_setting.brand_id=}, "
            f"{payment_setting.store_id=}, {secret_key=}"
        )


if __name__ == '__main__':
    import asyncio

    asyncio.run(migrate_stripe())
