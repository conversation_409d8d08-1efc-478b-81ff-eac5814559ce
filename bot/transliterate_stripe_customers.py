import asyncio
from typing import Any, Dict

import stripe
from sqlalchemy import select

from api.billing.router.stripe.helper import contains_cyrillic, transliterate_string
from config import BILLING_STRIPE_SECRET_KEY
from core.billing.stripe_client import bstripe
from db import DBSession
from db.models import Group


def build_transliterated_update_from_customer(
        customer: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Given a Stripe Customer object, identify any fields containing Cyrillic,
    transliterate them, and return a dict suitable for passing to
    stripe.Customer.modify().

    For the `address` field (if present and a dict), merges only those
    sub-fields that contained Cyrillic back into the full address so that
    non-Cyrillic parts are preserved.

    :param customer: A dict representing the Stripe Customer object.
    :return: Dict of fields to update, with Cyrillic values transliterated.
    """
    update_fields: Dict[str, Any] = {}

    for field, value in customer.items():
        # Handle nested address object
        if field == "address" and isinstance(value, dict):
            transliterated_subfields: Dict[str, str] = {}
            for subkey, subval in value.items():
                if isinstance(subval, str) and contains_cyrillic(subval):
                    transliterated_subfields[subkey] = transliterate_string(subval)
            if transliterated_subfields:
                # Merge so non-Cyrillic parts stay intact
                update_fields["address"] = {**value, **transliterated_subfields}

        # Handle top-level string fields
        elif isinstance(value, str) and contains_cyrillic(value):
            update_fields[field] = transliterate_string(value)

    return update_fields


async def main():
    stripe.api_key = BILLING_STRIPE_SECRET_KEY

    with DBSession() as db:
        non_null_ids = db.scalars(
            select(Group.stripe_customer_id)
            .where(Group.stripe_customer_id.is_not(None))
        ).all()

    for customer_id in non_null_ids:
        try:
            customer = await bstripe.customers.retrieve_async(customer_id)
            if customer:
                # print(f" Customer {customer.to_dict()}")
                updates = build_transliterated_update_from_customer(customer)

                stripe.api_key = BILLING_STRIPE_SECRET_KEY

                stripe.Customer.modify(
                    customer_id,
                    **updates
                )

                print(f"✅ Updated customer {customer_id}")

            else:
                print(f"⚠️  Customer {customer_id} not found.")
        except stripe.error.StripeError as e:
            print(f"❌ Error retrieving customer {customer_id}: {e}")


if __name__ == "__main__":
    asyncio.run(main())
