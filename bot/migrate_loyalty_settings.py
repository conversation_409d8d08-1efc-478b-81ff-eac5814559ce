import asyncio

from sqlalchemy import and_, or_, select

# ОБМЕЖЕННЯ ДЛЯ ТЕСТУВАННЯ: мігрувати тільки конкретний профіль
# Щоб прибрати обмеження: встановити TEST_PROFILE_ID = None
TEST_PROFILE_ID = None  # Міграція для всіх профілів

from config import INCUST_SERVER_API
from core.loyalty.incust_api import incust
from db import DBSession, sess
from db.models import (
    Brand, BrandSettings, EWallet, InvoiceTemplate, LoyaltySettings, Store,
    StoreProduct,
)
from schemas import LoyaltySettingsApplicableTypes, LoyaltySettingsTypeClientAuth

# Кеш для brand_incust_settings щоб не робити повторні запити
_brand_settings_cache: dict[int, dict] = {}

# Кеш для terminal та loyalty IDs щоб не робити повторні запити до API
_terminal_info_cache: dict[str, tuple[str, str]] = {}


async def get_terminal_and_loyalty_ids(
        terminal_api_key: str, server_url: str, white_label_id: str = ""
) -> tuple[str, str]:
    """
    Отримує terminal_id та loyalty_id через InCust API.
    Використовує кеш для оптимізації.
    
    Args:
        terminal_api_key: API ключ терміналу
        server_url: URL сервера
        white_label_id: ID білої мітки (не використовується для Terminal API)
    
    Returns:
        tuple: (terminal_id, loyalty_id) або ("", "") якщо не вдалось отримати
    """
    # Кеш базується тільки на ключі та URL (без white_label_id)
    cache_key = f"{terminal_api_key}_{server_url}"

    # Перевіряємо кеш
    if cache_key in _terminal_info_cache:
        cached_result = _terminal_info_cache[cache_key]
        print(
            f"🔄 Використано кеш для ключа {terminal_api_key[:20]}...: terminal_id="
            f"{cached_result[0]}, loyalty_id={cached_result[1]}"
        )
        return cached_result

    terminal_id = ""
    loyalty_id = ""

    try:
        # Створюємо тимчасові налаштування БЕЗ white_label_id
        temp_settings = LoyaltySettings(
            white_label_id="",  # Завжди порожній - не впливає на Terminal API
            terminal_api_key=terminal_api_key,
            server_url=server_url,
        )

        async with incust.term.SettingsApi(temp_settings, lang="uk") as api:
            terminal_settings = await api.settings()

            if terminal_settings:
                # Отримуємо terminal_id
                if hasattr(terminal_settings, 'id') and terminal_settings.id:
                    terminal_id = str(terminal_settings.id)
                
                # Отримуємо loyalty_id
                if hasattr(terminal_settings, 'loyalty') and terminal_settings.loyalty:
                    if hasattr(terminal_settings.loyalty, 'id') and terminal_settings.loyalty.id:
                        loyalty_id = str(terminal_settings.loyalty.id)

    except Exception as e:
        print(f"❌ Помилка при отриманні terminal/loyalty IDs для ключа {terminal_api_key[:20]}...: {str(e)}")

    # Зберігаємо в кеш
    _terminal_info_cache[cache_key] = (terminal_id, loyalty_id)

    return terminal_id, loyalty_id


def get_or_create_loyalty_settings(**filters) -> tuple[LoyaltySettings | dict, bool]:
    """Отримує існуючий запис або повертає схему для нового
    
    Returns:
        tuple[LoyaltySettings | dict, bool]: 
        - Якщо існує: (LoyaltySettings object, False)
        - Якщо новий: (dict schema, True)
    """
    existing = sess().query(LoyaltySettings).filter_by(**filters).first()

    if existing:
        print(f"Оновлюємо існуючий LoyaltySettings {existing.id} для {filters}")
        return existing, False  # existing object, не потрібно створювати новий
    else:
        # Повертаємо схему з базовими значеннями для нового запису
        schema = {
            'terminal_api_key': '',  # Буде заповнено пізніше
            'server_url': INCUST_SERVER_API,
            'loyalty_applicable_type': LoyaltySettingsApplicableTypes.FOR_PARTICIPANTS,
            'type_client_auth': LoyaltySettingsTypeClientAuth.WEB,  # Додано значення за замовчуванням
            'is_enabled': True,
            **filters
        }
        print(f"Створено схему для нового LoyaltySettings для {filters}")
        return schema, True  # schema dict, потрібно створити новий


async def create_loyalty_settings(schema: dict, base_data: dict) -> LoyaltySettings:
    """Створює об'єкт LoyaltySettings з заповненими даними"""
    schema.update(base_data)
    new_settings = LoyaltySettings(**schema)
    sess().add(new_settings)
    sess().flush()
    print(f"Створено новий LoyaltySettings {new_settings.id}")

    return new_settings


async def update_loyalty_settings(loyalty_settings: LoyaltySettings, base_data: dict) -> None:
    """Оновлює існуючий LoyaltySettings об'єкт з правильним оновленням дат"""
    await loyalty_settings.update(base_data, no_commit=True)


async def get_brand_incust_settings_cached(brand_id: int) -> dict:
    """Отримання incust налаштувань для бренду з кешем"""
    if brand_id in _brand_settings_cache:
        return _brand_settings_cache[brand_id]

    settings = await get_brand_incust_settings(brand_id)
    _brand_settings_cache[brand_id] = settings
    return settings


async def get_brand_incust_settings(brand_id: int) -> dict:

    """Отримання всіх incust налаштувань для бренду"""

    brand_settings = sess().execute(
        select(BrandSettings).where(
            BrandSettings.brand_id == brand_id,
            BrandSettings.type_data.like("incust_%")
        )
    ).scalars().all()

    settings_dict = {}
    for setting in brand_settings:
        # Видаляємо префікс "incust_" для маппінгу на поля LoyaltySettings
        field_name = setting.type_data[len("incust_"):]

        # Спеціальна обробка для деяких полів як в оригінальному коді
        if setting.type_data == 'incust_type_client_auth':
            # Конвертуємо в правильний enum
            if setting.value_data == 'web':
                settings_dict[field_name] = LoyaltySettingsTypeClientAuth.WEB
            elif setting.value_data == 'bot':
                settings_dict[field_name] = LoyaltySettingsTypeClientAuth.BOT
            else:
                settings_dict[field_name] = None
        elif setting.type_data == 'incust_loyalty_applicable_type':
            if setting.value_data == 'for_all':
                settings_dict[field_name] = LoyaltySettingsApplicableTypes.FOR_ALL
            else:
                settings_dict[
                    field_name] = LoyaltySettingsApplicableTypes.FOR_PARTICIPANTS
        elif setting.type_data == 'incust_prohibit_redeeming_bonuses':
            if setting.value_data:
                try:
                    # Спочатку пробуємо як число
                    settings_dict[field_name] = bool(int(setting.value_data))
                except ValueError:
                    # Якщо не число, то як строку boolean
                    settings_dict[field_name] = setting.value_data.lower() in ['true', '1', 'yes']
            else:
                settings_dict[field_name] = False
        elif setting.type_data == 'incust_prohibit_redeeming_coupons':
            if setting.value_data:
                try:
                    # Спочатку пробуємо як число
                    settings_dict[field_name] = bool(int(setting.value_data))
                except ValueError:
                    # Якщо не число, то як строку boolean
                    settings_dict[field_name] = setting.value_data.lower() in ['true', '1', 'yes']
            else:
                settings_dict[field_name] = False
        else:
            settings_dict[field_name] = setting.value_data

    return settings_dict


def create_base_loyalty_data(
        name: str,
        description: str,
        priority: int,
        brand_incust_settings: dict,
        is_enabled: bool = True,
        json_data: dict | None = None,
) -> dict:
    """Створює базові дані для міграції налаштувань лояльності"""
    return {
        'name': name,
        'description': description,
        'is_enabled': is_enabled,
        'priority': priority,
        'loyalty_applicable_type': brand_incust_settings.get(
            "loyalty_applicable_type", LoyaltySettingsApplicableTypes.FOR_PARTICIPANTS
        ) if brand_incust_settings else LoyaltySettingsApplicableTypes.FOR_PARTICIPANTS,
        'white_label_id': brand_incust_settings.get(
            "white_label_id", ""
        ) if brand_incust_settings else "",
        'loyalty_id': brand_incust_settings.get(
            "loyalty_id", ""
        ) if brand_incust_settings else "",
        'terminal_api_key': brand_incust_settings.get(
            "term_api", ""
        ) if brand_incust_settings else "",
        'terminal_id': brand_incust_settings.get(
            "terminal_id", ""
        ) if brand_incust_settings else "",
        'server_url': brand_incust_settings.get(
            "server_api", INCUST_SERVER_API
        ) if brand_incust_settings else INCUST_SERVER_API,
        'type_client_auth': brand_incust_settings.get(
            "type_client_auth"
        ) if brand_incust_settings else LoyaltySettingsTypeClientAuth.WEB,
        'prohibit_redeeming_bonuses': brand_incust_settings.get(
            "prohibit_redeeming_bonuses", False
        ) if brand_incust_settings else False,
        'prohibit_redeeming_coupons': brand_incust_settings.get(
            "prohibit_redeeming_coupons", False
        ) if brand_incust_settings else False,
        'json_data': json_data if json_data else None,
    }


async def migrate_profile_loyalty_settings() -> int:
    """Міграція налаштувань лояльності для профілів (Brand)"""
    print("\n=== Міграція профілів (BrandSettings) ===")

    # ОБМЕЖЕННЯ ДЛЯ ТЕСТУВАННЯ: тільки певний профіль
    if TEST_PROFILE_ID:
        print(f"🧪 ТЕСТОВИЙ РЕЖИМ: міграція тільки для профілю {TEST_PROFILE_ID}")
        # Отримуємо бренди тільки для конкретного профілю
        brands_with_incust = sess().execute(
            select(Brand.id).join(BrandSettings).where(
                BrandSettings.type_data.like("incust_%"),
                Brand.group_id == TEST_PROFILE_ID
            ).distinct()
        ).scalars().all()
    else:
        # Отримуємо всі бренди що мають incust налаштування
        brands_with_incust = sess().execute(
            select(Brand.id).join(BrandSettings).where(
                BrandSettings.type_data.like("incust_%")
            ).distinct()
        ).scalars().all()

    print(f"Знайдено {len(brands_with_incust)} брендів з InCust налаштуваннями")

    migrated_count = 0

    for brand_id in brands_with_incust:
        try:
            brand = sess().query(Brand).get(brand_id)
            if not brand:
                print(f"Пропущено brand_id {brand_id}: brand не знайдено")
                continue

            # Отримуємо всі incust налаштування для цього бренду
            settings_dict = await get_brand_incust_settings_cached(brand_id)

            # Перевіряємо наявність мінімально необхідних полів
            if not settings_dict.get("term_api"):  # incust_term_api
                print(f"Пропущено brand_id {brand_id}: відсутній terminal_api_key")
                continue

            # Отримуємо terminal_id та loyalty_id через API якщо їх немає
            terminal_id = settings_dict.get('terminal_id', '')
            loyalty_id = settings_dict.get('loyalty_id', '')

            # Якщо немає terminal_id або loyalty_id - отримуємо через API
            terminal_api_key = settings_dict.get("term_api")
            if terminal_api_key and (not terminal_id or not loyalty_id):
                api_terminal_id, api_loyalty_id = await get_terminal_and_loyalty_ids(
                    terminal_api_key=terminal_api_key,
                    server_url=settings_dict.get('server_api', INCUST_SERVER_API),
                    white_label_id=settings_dict.get('white_label_id', '')
                )
                if api_terminal_id:
                    terminal_id = api_terminal_id
                if api_loyalty_id:
                    loyalty_id = api_loyalty_id

            # Підготовлюємо всі дані для міграції з правильними значеннями
            base_data = create_base_loyalty_data(
                name=f"Profile {brand.group_id} settings",
                description=f"Migrated from BrandSettings for brand {brand_id}",
                priority=10,  # Profile має найменший пріоритет
                brand_incust_settings=settings_dict,
                # Для профіля settings_dict це і є brand_incust_settings
                is_enabled=True,
            )

            # Додаємо специфічні поля для профілю
            base_data.update(
                {
                    'brand_id': brand_id,
                    'terminal_id': terminal_id,
                    'loyalty_id': loyalty_id,
                }
            )

            # Отримуємо існуючий запис або схему для нового
            loyalty_settings, is_new = get_or_create_loyalty_settings(
                profile_id=brand.group_id,
                brand_id=brand_id
            )

            if is_new:
                # loyalty_settings тут це словник-схема, створюємо об'єкт БД з
                # заповненими даними
                loyalty_obj = await create_loyalty_settings(loyalty_settings, base_data)
                print(
                    f"Створено новий LoyaltySettings {loyalty_obj.id} для профілю "
                    f"{brand.group_id}"
                )
            else:
                # loyalty_settings тут це об'єкт LoyaltySettings, оновлюємо існуючий
                # запис
                await update_loyalty_settings(loyalty_settings, base_data)
                print(
                    f"Оновлено LoyaltySettings {loyalty_settings.id} для профілю "
                    f"{brand.group_id}"
                )
            migrated_count += 1

        except Exception as e:
            sess().rollback()
            print(f"Помилка при міграції brand_id {brand_id}: {str(e)}")

    sess().commit()
    return migrated_count


async def migrate_ewallet_loyalty_settings() -> int:
    """Міграція налаштувань лояльності для EWallet"""
    print("\n=== Міграція EWallet ===")

    ewallets = sess().execute(select(EWallet)).scalars().all()
    print(f"Знайдено {len(ewallets)} eWallet записів")

    migrated_count = 0

    for ewallet in ewallets:
        try:
            # Перевіряємо наявність мінімальних налаштувань
            if not ewallet.terminal_api_key:
                print(f"Пропущено eWallet {ewallet.id}: відсутній terminal_api_key")
                continue

            # Отримуємо Brand через зв'язок bot -> group -> brand
            if not ewallet.bot or not ewallet.bot.group or not ewallet.bot.group.brand:
                print(f"Пропущено eWallet {ewallet.id}: відсутній зв'язок з Brand")
                continue

            brand = ewallet.bot.group.brand
            brand_incust_settings = await get_brand_incust_settings_cached(brand.id)

            # Отримуємо terminal_id та loyalty_id
            terminal_id = ewallet.incust_terminal_id or ""
            loyalty_id = ""

            # Якщо немає terminal_id або loyalty_id - отримуємо через API
            print(f"\n📋 Обробка EWallet ID: {ewallet.id} ('{ewallet.name}')")
            print(
                f"   🔑 Поточні значення: terminal_id='{terminal_id}', loyalty_id='"
                f"{loyalty_id}'"
            )
            print(
                f"   🔍 Потреба в API запиті: "
                f"{ewallet.terminal_api_key is not None and (not terminal_id or not loyalty_id)}"
            )

            if ewallet.terminal_api_key and (not terminal_id or not loyalty_id):
                print(
                    f"   🚀 Викликаю get_terminal_and_loyalty_ids для EWallet {ewallet.id}"
                )
                api_terminal_id, api_loyalty_id = await get_terminal_and_loyalty_ids(
                    terminal_api_key=ewallet.terminal_api_key,
                    server_url=ewallet.server_api_url,
                    white_label_id=""  # Завжди порожній для EWallet
                )
                print(
                    f"   📥 Отримано з API: terminal_id='{api_terminal_id}', "
                    f"loyalty_id='{api_loyalty_id}'"
                )

                if api_terminal_id:
                    terminal_id = api_terminal_id
                    print(f"   ✅ Оновлено terminal_id: '{terminal_id}'")
                if api_loyalty_id:
                    loyalty_id = api_loyalty_id
                    print(f"   ✅ Оновлено loyalty_id: '{loyalty_id}'")
            else:
                print(
                    f"   ⏭️  API запит не потрібен (terminal_id та loyalty_id вже є "
                    f"або немає ключа)"
                )

            # Підготовлюємо всі дані для міграції з правильними значеннями
            base_data = create_base_loyalty_data(
                name=f"EWallet {ewallet.name or ewallet.id} settings",
                description=f"Migrated from EWallet {ewallet.id}",
                priority=5,
                # EWallet має найвищий пріоритет серед всіх, але працює окремо
                brand_incust_settings=brand_incust_settings,
                is_enabled=True,
            )

            # Перезаписуємо специфічні поля EWallet
            base_data.update(
                {
                    'terminal_api_key': ewallet.terminal_api_key,
                    'server_url': ewallet.server_api_url,
                    'terminal_id': terminal_id,
                    'loyalty_id': loyalty_id,
                }
            )

            loyalty_settings, is_new = get_or_create_loyalty_settings(
                ewallet_id=ewallet.id
            )

            if is_new:
                # loyalty_settings тут це словник-схема, створюємо об'єкт БД з
                # заповненими даними
                loyalty_obj = await create_loyalty_settings(loyalty_settings, base_data)
                print(
                    f"Створено новий LoyaltySettings {loyalty_obj.id} для eWallet "
                    f"{ewallet.id}"
                )
            else:
                # loyalty_settings тут це об'єкт LoyaltySettings, оновлюємо існуючий
                # запис
                await update_loyalty_settings(loyalty_settings, base_data)
                print(
                    f"Оновлено LoyaltySettings {loyalty_settings.id} для eWallet "
                    f"{ewallet.id}"
                )
            migrated_count += 1

        except Exception as e:
            sess().rollback()
            print(f"Помилка при міграції eWallet {ewallet.id}: {str(e)}")

    sess().commit()
    return migrated_count


async def migrate_store_loyalty_settings() -> int:
    """Міграція налаштувань лояльності для Store"""
    print("\n=== Міграція Store ===")

    stores = sess().execute(
        select(Store).where(
            Store.incust_terminal_api_key.is_not(None),
            Store.incust_terminal_api_key != ''
        )
    ).scalars().all()
    print(f"Знайдено {len(stores)} Store записів з InCust налаштуваннями")

    migrated_count = 0

    for store in stores:
        try:
            # Перевіряємо наявність мінімальних налаштувань
            if not store.incust_terminal_api_key:
                print(f"Пропущено Store {store.id}: відсутній terminal_api_key")
                continue

            # Отримуємо налаштування профілю для успадкування
            brand_incust_settings = await get_brand_incust_settings_cached(
                store.brand_id
            )

            # Отримуємо terminal_id та loyalty_id
            terminal_id = store.incust_terminal_id or ""
            loyalty_id = ""

            # Якщо немає terminal_id або loyalty_id - отримуємо через API
            white_label_id = brand_incust_settings.get(
                "white_label_id", ""
            ) if brand_incust_settings else ""
            server_url = brand_incust_settings.get(
                "server_api", INCUST_SERVER_API
            ) if brand_incust_settings else INCUST_SERVER_API
            if store.incust_terminal_api_key and (not terminal_id or not loyalty_id):
                api_terminal_id, api_loyalty_id = await get_terminal_and_loyalty_ids(
                    terminal_api_key=store.incust_terminal_api_key,
                    server_url=server_url,
                    white_label_id=white_label_id
                )
                if api_terminal_id:
                    terminal_id = api_terminal_id
                if api_loyalty_id:
                    loyalty_id = api_loyalty_id

            # Підготовлюємо всі дані для міграції з правильними значеннями
            base_data = create_base_loyalty_data(
                name=f"Store {store.name} settings",
                description=f"Migrated from Store {store.id}",
                priority=30,
                brand_incust_settings=brand_incust_settings,
                is_enabled=True,
            )

            # Перезаписуємо специфічні поля Store
            base_data.update(
                {
                    'terminal_api_key': store.incust_terminal_api_key,
                    'terminal_id': terminal_id,
                    'loyalty_id': loyalty_id,
                }
            )

            # Отримуємо існуючий запис або схему для нового
            loyalty_settings, is_new = get_or_create_loyalty_settings(
                store_id=store.id
            )

            if is_new:
                # loyalty_settings тут це словник-схема, створюємо об'єкт БД з
                # заповненими даними
                loyalty_obj = await create_loyalty_settings(loyalty_settings, base_data)
                print(
                    f"Створено новий LoyaltySettings {loyalty_obj.id} для Store "
                    f"{store.id}"
                )
            else:
                # loyalty_settings тут це об'єкт LoyaltySettings, оновлюємо існуючий
                # запис
                await update_loyalty_settings(loyalty_settings, base_data)
                print(
                    f"Оновлено LoyaltySettings {loyalty_settings.id} для Store "
                    f"{store.id}"
                )
            migrated_count += 1

        except Exception as e:
            sess().rollback()
            print(f"Помилка при міграції Store {store.id}: {str(e)}")

    sess().commit()
    return migrated_count


async def migrate_product_loyalty_settings() -> int:
    """Міграція налаштувань лояльності для StoreProduct"""
    print("\n=== Міграція StoreProduct ===")

    products = sess().execute(
        select(StoreProduct).where(
            StoreProduct.topup_terminal_api_key.is_not(None),
            StoreProduct.topup_terminal_api_key != ''
        )
    ).scalars().all()
    print(f"Знайдено {len(products)} StoreProduct записів з topup налаштуваннями")

    migrated_count = 0

    for product in products:
        try:
            # Перевіряємо наявність мінімальних налаштувань
            if not product.topup_terminal_api_key:
                print(f"Пропущено Product {product.id}: відсутній terminal_api_key")
                continue

            # Отримуємо налаштування профілю для успадкування
            brand_incust_settings = await get_brand_incust_settings_cached(
                product.brand_id
            )

            # Отримуємо terminal_id та loyalty_id
            terminal_id = product.incust_terminal_id or ""
            loyalty_id = ""

            # Якщо немає terminal_id або loyalty_id - отримуємо через API
            white_label_id = brand_incust_settings.get(
                "white_label_id", ""
            ) if brand_incust_settings else ""
            server_url = product.topup_server_api_url or (brand_incust_settings.get(
                "server_api", INCUST_SERVER_API
            ) if brand_incust_settings else INCUST_SERVER_API)
            if product.topup_terminal_api_key and (not terminal_id or not loyalty_id):
                api_terminal_id, api_loyalty_id = await get_terminal_and_loyalty_ids(
                    terminal_api_key=product.topup_terminal_api_key,
                    server_url=server_url,
                    white_label_id=white_label_id
                )
                if api_terminal_id:
                    terminal_id = api_terminal_id
                if api_loyalty_id:
                    loyalty_id = api_loyalty_id

            # Створюємо json_data для полів, яких немає в LoyaltySettings
            json_data = {}
            if product.topup_account_id:
                json_data["topup_account_id"] = product.topup_account_id
            if product.topup_enabled_card is not None:
                json_data["topup_enabled_card"] = product.topup_enabled_card

            # Підготовлюємо всі дані для міграції з правильними значеннями
            base_data = create_base_loyalty_data(
                name=f"Product {product.name} settings",
                description=f"Migrated from StoreProduct {product.id}",
                priority=50,
                brand_incust_settings=brand_incust_settings,
                is_enabled=True,
                json_data=json_data,
            )

            # Перезаписуємо специфічні поля StoreProduct
            base_data.update(
                {
                    'terminal_api_key': product.topup_terminal_api_key,
                    'server_url': server_url,
                    'terminal_id': terminal_id,
                    'loyalty_id': loyalty_id,
                }
            )

            # Отримуємо існуючий запис або схему для нового
            loyalty_settings, is_new = get_or_create_loyalty_settings(
                product_id=product.id
            )

            if is_new:
                # loyalty_settings тут це словник-схема, створюємо об'єкт БД з
                # заповненими даними
                loyalty_obj = await create_loyalty_settings(loyalty_settings, base_data)
                print(
                    f"Створено новий LoyaltySettings {loyalty_obj.id} для "
                    f"StoreProduct {product.id}"
                )
            else:
                # loyalty_settings тут це об'єкт LoyaltySettings, оновлюємо існуючий
                # запис
                await update_loyalty_settings(loyalty_settings, base_data)
                print(
                    f"Оновлено LoyaltySettings {loyalty_settings.id} для StoreProduct "
                    f"{product.id}"
                )
            migrated_count += 1

        except Exception as e:
            sess().rollback()
            print(f"Помилка при міграції StoreProduct {product.id}: {str(e)}")

    sess().commit()
    return migrated_count


async def migrate_invoice_template_loyalty_settings() -> int:
    """Міграція налаштувань лояльності для InvoiceTemplate"""
    print("\n=== Міграція InvoiceTemplate ===")

    # Мігруємо шаблони які мають incust налаштування АБО disabled_loyalty=True
    templates = sess().execute(
        select(InvoiceTemplate).where(
            or_(
                and_(
                    InvoiceTemplate.incust_terminal_api_key.is_not(None),
                    InvoiceTemplate.incust_terminal_api_key != ''
                ),
                InvoiceTemplate.disabled_loyalty == True
            )
        )
    ).scalars().all()
    print(f"Знайдено {len(templates)} InvoiceTemplate записів з InCust налаштуваннями")

    migrated_count = 0

    for template in templates:
        try:
            # Якщо тільки disabled_loyalty=True без ключа - створюємо запис
            # is_enabled=False
            if not template.incust_terminal_api_key and template.disabled_loyalty:
                print(
                    f"InvoiceTemplate {template.id}: тільки disabled_loyalty=True, "
                    f"створюємо запис is_enabled=False"
                )

            # Отримуємо Group через зв'язок
            group = template.group
            if not group:
                print(f"Пропущено InvoiceTemplate {template.id}: Group не знайдено")
                continue

            # Отримуємо налаштування бренду для успадкування
            brand = group.brand
            brand_incust_settings = {}
            if brand:
                brand_incust_settings = await get_brand_incust_settings_cached(brand.id)

            # Отримуємо terminal_id та loyalty_id
            terminal_id = template.incust_terminal_id or ""
            loyalty_id = ""

            # Якщо немає terminal_id або loyalty_id і є ключ - отримуємо через API
            white_label_id = brand_incust_settings.get(
                "white_label_id", ""
            ) if brand_incust_settings else ""
            server_url = brand_incust_settings.get(
                "server_api", INCUST_SERVER_API
            ) if brand_incust_settings else INCUST_SERVER_API
            if template.incust_terminal_api_key and (not terminal_id or not loyalty_id):
                api_terminal_id, api_loyalty_id = await get_terminal_and_loyalty_ids(
                    terminal_api_key=template.incust_terminal_api_key,
                    server_url=server_url,
                    white_label_id=white_label_id
                )
                if api_terminal_id:
                    terminal_id = api_terminal_id
                if api_loyalty_id:
                    loyalty_id = api_loyalty_id

            # Підготовлюємо всі дані для міграції з правильними значеннями
            base_data = create_base_loyalty_data(
                name=f"InvoiceTemplate {template.title} settings",
                description=f"Migrated from InvoiceTemplate {template.id}",
                priority=40,
                brand_incust_settings=brand_incust_settings,
                is_enabled=not template.disabled_loyalty,
                # Завжди переносимо disabled_loyalty
            )

            # Перезаписуємо специфічні поля InvoiceTemplate якщо є ключ
            if template.incust_terminal_api_key:
                base_data.update(
                    {
                        'terminal_api_key': template.incust_terminal_api_key,
                        'terminal_id': terminal_id,
                        'loyalty_id': loyalty_id,
                    }
                )

            # Отримуємо існуючий запис або схему для нового
            loyalty_settings, is_new = get_or_create_loyalty_settings(
                invoice_template_id=template.id
            )

            if is_new:
                # loyalty_settings тут це словник-схема, створюємо об'єкт БД з
                # заповненими даними
                loyalty_obj = await create_loyalty_settings(loyalty_settings, base_data)
                print(
                    f"Створено новий LoyaltySettings {loyalty_obj.id} для "
                    f"InvoiceTemplate {template.id}"
                )
            else:
                # loyalty_settings тут це об'єкт LoyaltySettings, оновлюємо існуючий
                # запис
                await update_loyalty_settings(loyalty_settings, base_data)
                print(
                    f"Оновлено LoyaltySettings {loyalty_settings.id} для "
                    f"InvoiceTemplate {template.id}"
                )
            migrated_count += 1

        except Exception as e:
            sess().rollback()
            print(f"Помилка при міграції InvoiceTemplate {template.id}: {str(e)}")

    sess().commit()
    return migrated_count


async def migrate_loyalty_settings() -> dict:
    """Головна функція міграції налаштувань лояльності"""
    print("Початок міграції налаштувань лояльності...")

    print("Міграція налаштувань лояльності (з оновленням існуючих записів)...")

    # ОБМЕЖЕННЯ ДЛЯ ТЕСТУВАННЯ: міграція тільки для певного профілю
    if TEST_PROFILE_ID:
        print(f"🧪 ТЕСТОВИЙ РЕЖИМ: міграція тільки для профілю {TEST_PROFILE_ID}")
        print("Мігруємо тільки профілі, інші типи пропускаємо для тестування")
        
        # Мігруємо тільки профілі
        profile_count = await migrate_profile_loyalty_settings()
        
        # Пропускаємо всі інші міграції в тестовому режимі
        ewallet_count = 0
        store_count = 0  
        product_count = 0
        template_count = 0
    else:
        # Мігруємо в правильному порядку: спочатку профілі, потім залежні об'єкти
        profile_count = await migrate_profile_loyalty_settings()
        ewallet_count = await migrate_ewallet_loyalty_settings()
        store_count = await migrate_store_loyalty_settings()
        product_count = await migrate_product_loyalty_settings()
        template_count = await migrate_invoice_template_loyalty_settings()

    total_migrated = (profile_count + ewallet_count + store_count + product_count +
                      template_count)

    print(f"\n=== Результати міграції ===")
    print(f"Профілі: {profile_count}")
    print(f"EWallet: {ewallet_count}")
    print(f"Store: {store_count}")
    print(f"StoreProduct: {product_count}")
    print(f"InvoiceTemplate: {template_count}")
    print(f"Всього мігровано: {total_migrated}")

    # Очищуємо кеші після завершення міграції
    _brand_settings_cache.clear()
    _terminal_info_cache.clear()

    return {
        "profile_count": profile_count,
        "ewallet_count": ewallet_count,
        "store_count": store_count,
        "product_count": product_count,
        "template_count": template_count,
        "total_migrated": total_migrated
    }


async def main():
    with DBSession():
        result = await migrate_loyalty_settings()
        print(f"Міграцію завершено. Результати: {result}")


if __name__ == "__main__":
    asyncio.run(main())
