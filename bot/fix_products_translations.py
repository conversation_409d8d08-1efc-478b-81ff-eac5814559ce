import json
import typer

from db import DBSession
from db.models import Translation

app = typer.Typer()


@app.command()
def fix_translations(
        file_path: str = typer.prompt("File path"),
):
    with open(file_path, "r") as file:
        data = json.load(file)

    with DBSession() as db:
        for i, el in enumerate(data.items()):
            product_id, langs_data = el

            print(f"Product({i + 1} / {len(data)}) {product_id} PROCESSING")

            for lang, translated in langs_data.items():
                print(f"--{lang.upper()}")

                translation = Translation.create_sync(
                    "StoreProduct", product_id, lang, no_commit=True
                )
                translation.data = translated

            print(f"Product({i + 1 / len(data)}) {product_id} DONE")

        db.commit()

    print("DONE")


if __name__ == '__main__':
    app()
