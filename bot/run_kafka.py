import asyncio

from config import ROOT_BOT_API_TOKEN
from core.kafka.consumers.email import EmailConsumer, EmailLimitedConsumer
from core.kafka.consumers.mailing.consumers import (
    EmailMailingConsumer, EmailMailingLimitedConsumer, TelegramMailingConsumer,
    TelegramMailingLimitedConsumer, WhatsappMailingConsumer,
    WhatsappMailingLimitedConsumer,
)
from core.kafka.consumers.notifications import (
    NotificationConsumer,
    UniversalLimitedConsumer,
)
from core.kafka.consumers.push.consumers import PushConsumer, PushLimitedConsumer
from core.kafka.consumers.task import TaskConsumer, TaskLimitedConsumer
from core.kafka.consumers.telegram import TelegramConsumer, TelegramLimitedConsumer
from core.kafka.consumers.webhooks import WebhookConsumer, WebhookLimitedConsumer
from core.kafka.consumers.whatsapp import WhatsappConsumer, WhatsappLimitedConsumer
from core.kafka.producer.producer_instance import producer
from core.kafka.redis_instance import redis
from core.kafka.workers import DelayedMessagesWorker
from utils.logger import setup_logger
from utils.redefined_classes import Bot

Bot.set_current(Bot(ROOT_BOT_API_TOKEN))


async def main():
    consumers = [
        PushConsumer(redis),
        PushLimitedConsumer(redis),
        TelegramConsumer(redis),
        TelegramLimitedConsumer(redis),
        WhatsappConsumer(redis),
        WhatsappLimitedConsumer(redis),
        EmailConsumer(redis),
        EmailLimitedConsumer(redis),
        *TaskConsumer.multiple_tasks(redis),
        TaskLimitedConsumer(redis),
        WebhookConsumer(redis),
        WebhookLimitedConsumer(redis),
        NotificationConsumer(redis),
        UniversalLimitedConsumer(redis),
        WhatsappMailingLimitedConsumer(redis),
        WhatsappMailingConsumer(redis),
        EmailMailingConsumer(redis),
        TelegramMailingConsumer(redis),
        TelegramMailingLimitedConsumer(redis),
        EmailMailingLimitedConsumer(redis),
    ]
    try:
        await redis.initialize()
        async with producer:
            await asyncio.gather(*consumers, DelayedMessagesWorker(redis, producer))
    finally:
        await redis.close()


if __name__ == "__main__":
    setup_logger("kafka")
    asyncio.run(main())
