from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from utils.router import Router

from utils.text import f
from ...api import schemas
from ...connect_telegram.states import ConnectTelegram
from ...functions import send_lip_lep_user_info
from ...login.states import Login
from ..keyboards import get_menu_keyboard


async def cmd_start(message: types.Message, state: FSMContext, lip_lep_user: schemas.User, lang: str):
    await state.finish()
    await message.answer(await f("lip lep hello message", lang), reply_markup=get_menu_keyboard())

    if lip_lep_user:
        await send_lip_lep_user_info(message, lip_lep_user, lang)
        if not lip_lep_user.chat_id:
            await ConnectTelegram.first()
            await Router.state_menu(message, state, lang, set_state_message=True)
    else:
        await Login.first()
        await Router.state_menu(message, state, lang, set_state_message=True)


async def cmd_exit(message: types.Message, state: FSMContext, lang: str):
    await state.storage.reset_bucket(user=state.user)
    await message.answer(await f("logout message", lang))


def register_main_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cmd_start,
        commands="start",
        state="*",
        chat_type="private",
    )

    dp.register_message_handler(
        cmd_exit,
        commands="exit",
        state="*",
        chat_type="private",
    )
