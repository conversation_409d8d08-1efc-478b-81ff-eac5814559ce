from aiogram import types, Dispatcher

from lip_lep.api.errors import ApiError
from utils.text import f


async def api_error_handler(update: types.Update, exception: ApiError):
    if update.message:
        user = update.message.from_user
    elif update.callback_query:
        user = update.callback_query.from_user
    else:
        return False

    text = await f(
        "lip lep api error", user.language_code,
        code=exception.code, detail=exception.text,
    )
    await update.bot.send_message(user.id, text)


def register_main_errors_handlers(dp: Dispatcher):
    dp.register_errors_handler(
        api_error_handler,
        exception=ApiError,
    )
