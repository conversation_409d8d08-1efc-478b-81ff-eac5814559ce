from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from utils.router import Router
from .filters import IsLoginFilter

from ...api import schemas
from ...functions import send_lip_lep_user_info
from ...login.states import Login


async def unauthorized_message_handler(message: types.Message, state: FSMContext, lang: str):
    await Login.first()
    await Router.state_menu(message, state, lang, set_state_message=True)


async def authorized_message_handler(message: types.Message, lip_lep_user: schemas.User, lang: str):
    await send_lip_lep_user_info(message, lip_lep_user, lang)


def register_main_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        unauthorized_message_handler,
        IsLoginFilter(is_login=False),
        content_types=types.ContentTypes.ANY,
        chat_type="private",
    )

    dp.register_message_handler(
        authorized_message_handler,
        IsLoginFilter(is_login=True),
        content_types=types.ContentTypes.ANY,
        chat_type="private",
    )
