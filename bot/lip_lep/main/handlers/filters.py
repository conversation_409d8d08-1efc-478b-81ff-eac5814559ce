from aiogram import types
from aiogram.dispatcher.filters import <PERSON><PERSON><PERSON>ilt<PERSON>
from aiogram.dispatcher.handler import ctx_data


class IsLoginFilter(BoundFilter):
    key = "is_login"
    default = True

    def __init__(self, is_login: bool):
        if not isinstance(is_login, bool):
            raise TypeError(f"is_login must be a boolean, not {type}")

        self.is_login = is_login

    async def check(self, obj: types.Message | types.CallbackQuery) -> bool:
        data = ctx_data.get()
        is_login = data.get("is_lip_lep_login", False)
        return is_login is self.is_login
