from aiogram.dispatcher.filters.state import StatesGroup, State


class Login(StatesGroup):
    Email = State()
    Password = State()
    Login = State()


class ChangeTelegram(StatesGroup):
    ConfirmEmail = State()
    Password = State()
    ChangeTelegram = State()


class ResetPassword(StatesGroup):
    Email = State()
    NewPassword = State()
    ConfirmPassword = State()
    ResetPassword = State()


class ChangeEmail(StatesGroup):
    Password = State()
    NewEmail = State()
    ConfirmEmail = State()
    ChangeEmail = State()


