import logging
from pprint import pformat

from aiogram import types
from aiogram.dispatcher import FSMContext
from aiogram.dispatcher.handler import ctx_data

from utils.router import Router
from utils.text import f

from ..api import api
from .keyboards import get_enter_email_keyboard, get_incorrect_authorization_data_keyboard
from .states import Login
from ..api.errors import ApiUnauthorized
from ..connect_telegram.states import ConnectTelegram
from ..functions import set_token


async def send_enter_email(message: types.Message, lang: str, mode: str):
    text = await f("enter email header", lang)
    keyboard = await get_enter_email_keyboard(lang)
    if mode == "edit":
        return await message.edit_text(text, reply_markup=keyboard)
    return await message.answer(text, reply_markup=keyboard)


async def send_enter_password(message: types.Message, lang: str):
    text = await f("enter password header", lang)
    return await message.edit_text(text)


async def login(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    await state.finish()

    email = state_data.get("email")
    password = state_data.get("password")

    try:
        token_data = await api.login(lang, email, password)
    except ApiUnauthorized as e:
        logger = logging.getLogger()
        logger.error(e, exc_info=True)
        token_data = None

    if token_data is None:
        text = await f("incorrect email or password message", lang)
        keyboard = await get_incorrect_authorization_data_keyboard(lang)
        return await message.edit_text(text, reply_markup=keyboard)

    await set_token(token_data.token, state.storage, state.user)

    await message.edit_text(await f("lip lep successfully logged in message", lang))

    user = await api.get_me(lang, token_data.token)
    message = await message.answer(pformat(user.dict()))

    data = ctx_data.get()
    data.update(lip_lep_user=user, token=token_data.token)

    if not user.chat_id:
        await ConnectTelegram.first()
        return await Router.state_menu(message, state, lang, mode="new")


def register_login_routes(router: Router):
    router.add_route(Login.Email, send_enter_email)
    router.add_route(Login.Password, send_enter_password)
    router.add_route(Login.Login, login)
