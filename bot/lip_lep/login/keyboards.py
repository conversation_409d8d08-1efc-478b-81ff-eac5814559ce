from utils.redefined_classes import InlineKb, InlineBtn
from utils.text import f


async def get_enter_email_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb()
    keyboard.insert(InlineBtn(await f("enter email not registered button", lang), callback_data="register"))
    return keyboard


async def get_incorrect_authorization_data_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb()

    keyboard.row(InlineBtn(await f("lip lep login try again", lang), callback_data="try_again"))
    keyboard.row(InlineBtn(await f("lip lep login forgot password", lang), callback_data="change_password"))

    return keyboard


async def get_create_new_account_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb()
    keyboard.insert(InlineBtn(await f("create new account button", lang), callback_data="register"))
    return keyboard
