from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from lip_lep.login.states import Login
from utils.router import Router


async def try_again_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await Login.first()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


def register_login_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        try_again_button_handler,
        callback_mode="try_again",
        state="*",
    )
