from contextlib import suppress

from aiogram import types
from aiogram.dispatcher import FSMContext
from aiogram.utils.exceptions import MessageNotModified

from psutils.forms.helpers import delete_messages
from psutils.forms.validators.base import Validator
from utils.router import Router
from utils.text import f
from .keyboards import get_create_new_account_keyboard

from ..api import api


class EmailExistsValidator(Validator):
    async def validate(
            self,
            message: types.Message,
            state: FSMContext,
            lang: str, email: str,
    ) -> bool | dict | types.Message:

        result = await api.check_is_email_exists(lang, email)
        if not result.is_exists:
            await state.update_data(not_exists_email=result.email)

            text = await f("email does not exists error", lang, email=email)
            keyboard = await get_create_new_account_keyboard(lang)

            router_message = await Router.get_state_message(state)
            with suppress(MessageNotModified):
                await router_message.edit_text(text, reply_markup=keyboard)

            async with state.proxy() as state_data:
                messages = state_data.pop("messages", [])
                await delete_messages(message.chat.id, message.message_id, *messages)

            return False

        return True
