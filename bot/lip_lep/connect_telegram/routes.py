from aiogram import types
from aiogram.dispatcher import FSMContext

from config import LIP_LEP_BOT_URL
from db.models import ClientBot

from utils.router import Router
from utils.text import f
from .keyboards import get_other_telegram_connected_keyboard

from ..api import schemas, api

from .states import ConnectTelegram
from ..functions import get_token


async def check_if_other_telegram_connected(message: types.Message, lip_lep_user: schemas.User, lang: str):
    if lip_lep_user.chat_id:
        text = await f("other telegram is connected message", lang)
        keyboard = await get_other_telegram_connected_keyboard(lang)
        return await message.answer(text, reply_markup=keyboard)

    await ConnectTelegram.ConfirmEmail.set()
    return await Router.state_menu(message)


async def send_confirm_email(message: types.Message, lip_lep_user: schemas.User, lang: str):
    message = await message.answer(await f("lip lep need connect telegram message", lang))

    await api.send_confirm_email(
        lang, lip_lep_user.email,
        "set_telegram",
        schemas.CallbackParams(
            url=f"{LIP_LEP_BOT_URL}/connectTelegram/emailConfirmed",
            params={"chat_id": message.chat.id}
        )
    )
    return message


async def connect_telegram(message: types.Message, state: FSMContext, lang: str):
    bot = await ClientBot.get_current()

    token = await get_token(state.storage, state.user)

    await api.set_telegram(
        lang, message.chat,
        bot.token, token,
    )

    return await message.answer(await f("telegram was connected to account", lang))


def register_connect_telegram_routes(router: Router):
    router.add_route(ConnectTelegram.Check, check_if_other_telegram_connected)
    router.add_route(ConnectTelegram.ConfirmEmail, send_confirm_email)
    router.add_route(ConnectTelegram.ConnectTelegram, connect_telegram)
