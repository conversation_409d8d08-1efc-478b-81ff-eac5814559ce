from config import LIP_LEP_BOT_URL
from psutils.forms import WizardForm, fields, validators

from ..validators import ConfirmEmailValidator
from ..data_savers import ConfirmEmailDataSaver

from .states import ChangePassword


class ChangePasswordForm(WizardForm):
    state_group = ChangePassword

    email = fields.TextField(
        input_validator=validators.EmailValidator(),
        data_saver=ConfirmEmailDataSaver("change_password", f"{LIP_LEP_BOT_URL}/changePassword/emailConfirmed")
    )
    confirm_email = fields.MessageField("*", input_validator=ConfirmEmailValidator())
    password = fields.TextField(min_length=8)
    confirm_password = fields.TextField(
        input_validator=validators.PasswordsMatchValidator()
    )
