from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import ClientBot
from utils.router import Router
from utils.text import f

from ..api import api
from ..api.schemas import ChangePasswordData
from .states import ChangePassword
from ..functions import set_token


async def send_enter_email(message: types.Message, lang: str, mode: str):
    text = await f("lip lep change password enter email", lang)

    if mode == "edit":
        return await message.edit_text(text)
    return await message.answer(text)


async def send_confirm_email(message: types.Message, lang: str):
    text = await f("lip lep wait for confirm email message", lang)
    return await message.edit_text(text)


async def check_is_tg_connected(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    email = state_data.get("email")

    result = await api.check_is_tg_connected(lang, email)

    if result.is_connected:
        await state.finish()
        return await message.edit_text(await f("lip lep change password other tg connected to account", lang))

    await state.set_state(ChangePassword.Password)
    return await Router.state_menu(message, state, lang)


async def send_enter_password(message: types.Message, lang: str):
    return await message.answer(await f("lip lep enter new password", lang))


async def send_confirm_password(message: types.Message, lang: str):
    return await message.edit_text(await f("lip lep confirm password", lang))


async def change_password(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    await state.finish()

    bot = await ClientBot.get_lip_lep_bot()
    data = ChangePasswordData(**state_data, telegram_user=message.from_user, bot_token=bot.token)
    await api.change_password(lang, data)

    token_data = await api.login(lang, data.email, data.password)

    await set_token(token_data.token, state.storage, state.user)

    return await message.edit_text(await f("password was successfully changed", lang))


def register_change_password_routes(router: Router):
    router.add_route(ChangePassword.Email, send_enter_email)
    router.add_route(ChangePassword.ConfirmEmail, send_confirm_email)
    router.add_route(ChangePassword.CheckIsTGConnected, check_is_tg_connected)
    router.add_route(ChangePassword.Password, send_enter_password)
    router.add_route(ChangePassword.ConfirmPassword, send_confirm_password)
    router.add_route(ChangePassword.ChangePassword, change_password)
