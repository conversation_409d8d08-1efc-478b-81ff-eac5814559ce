from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from utils.router import Router
from ..states import ChangePassword


async def change_password_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, lang: str,
):
    mode = callback_data.get("m", "edit")
    await ChangePassword.first()
    await Router.state_menu(callback_query, state, lang, mode, set_state_message=True)


def register_change_password_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        change_password_button_handler,
        callback_mode="change_password",
        state="*",
    )
