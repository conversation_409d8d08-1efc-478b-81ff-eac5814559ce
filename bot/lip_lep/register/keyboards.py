from utils.redefined_classes import InlineKb, InlineBtn
from utils.text import f, c


async def get_accept_agreement_keyboard(lang: str, with_dont_accept: bool = True) -> InlineKb:
    keyboard = InlineKb()

    if with_dont_accept:
        keyboard.row(InlineBtn(await f("do not accept agreement button", lang), callback_data=c("accept", accept=False)))
    keyboard.row(InlineBtn(await f("accept agreement button", lang), callback_data=c("accept", accept=True)))

    return keyboard
