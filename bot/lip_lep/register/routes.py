from aiogram import types
from aiogram.dispatcher import FSMContext

from config import LIP_LEP_BOT_URL
from db.models import ClientBot
from utils.router import Router
from utils.text import f

from .keyboards import get_accept_agreement_keyboard
from .states import Register
from ..api import api, schemas
from ..connect_email.functions import send_confirm_email
from ..connect_email.states import ConnectEmail
from ..functions import set_token, send_lip_lep_user_info
from ..connect_email.keyboards import get_connect_email_keyboard


async def send_accept_agreement(message: types.Message, lang: str, mode: str):
    text = await f("accept agreement message", lang)
    keyboard = await get_accept_agreement_keyboard(lang)
    if mode == "edit":
        return await message.edit_text(text, reply_markup=keyboard)
    return await message.answer(text, reply_markup=keyboard)


async def create_user(
        callback_query: types.CallbackQuery,
        message: types.Message,
        state: FSMContext, lang: str,
):
    state_data = await state.get_data()

    bot = await ClientBot.get_current()

    register_user_data = schemas.RegisterUserData(
        **state_data,
        telegram_user=callback_query.from_user,
        bot_token=bot.token,
    )

    if not register_user_data.is_accept_agreement:
        await Register.AcceptAgreement.set()
        keyboard = await get_accept_agreement_keyboard(lang, with_dont_accept=False)
        return await message.edit_text(await f("do not accepted agreement", lang), reply_markup=keyboard)

    await state.finish()

    token_data = await api.create_user(lang, register_user_data)

    await set_token(token_data.token, state.storage, state.user)

    user = await api.get_me(lang, token_data.token)

    await message.edit_text(await f("lip lep registered successfully", lang))

    await send_lip_lep_user_info(message, user, lang, with_keyboard=False)

    not_exists_email = state_data.get("not_exists_email")
    if not_exists_email:
        is_exists_result = await api.check_is_email_exists(lang, not_exists_email)
        if not is_exists_result.is_exists:

            callback_url = f"{LIP_LEP_BOT_URL}/connectEmail/emailConfirmed"
            await send_confirm_email(not_exists_email, user.chat_id, callback_url, "set_email", lang)

            await ConnectEmail.ConfirmEmail.set()
            await state.update_data(email=not_exists_email)
            return await message.answer(await f("not exists email confirm text", lang))

    keyboard = await get_connect_email_keyboard(lang)
    await message.answer(await f("suggest to connect email message", lang), reply_markup=keyboard)


def register_register_routes(router: Router):
    router.add_route(Register.AcceptAgreement, send_accept_agreement)
    router.add_route(Register.CreateUser, create_user)
