from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from utils.router import Router
from ..states import Register


async def register_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await Register.first()
    await Router.state_menu(callback_query, state, lang)


def register_register_button_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        register_button_handler,
        callback_mode="register",
        state="*",
    )
