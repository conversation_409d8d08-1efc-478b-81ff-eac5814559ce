from datetime import datetime

from aiogram.dispatcher import FSMContext

from psutils.forms import WizardForm, fields

from .states import Register


class RegisterForm(WizardForm):
    state_group = Register

    accept_agreement = fields.InlineButtonsField(
        callback_mode="accept",
        callback_keys=("accept", "is_accept_agreement"),
    )

    @classmethod
    async def data_saver(cls, data: dict, state: FSMContext):
        if data.get("is_accept_agreement", False):
            data["accept_agreement_datetime"] = datetime.utcnow().timestamp()
        await state.update_data(data)
