from aiogram.dispatcher import FSMContext

from db.models import User
from psutils.forms.data_savers.base import DataSaver

from .connect_email.functions import send_confirm_email


class ConfirmEmailDataSaver(DataSaver):

    def __init__(self, purpose: str, callback_url: str, need_save_to_state: bool = True):
        self.callback_url = callback_url
        self.purpose = purpose
        self.need_save_to_state = need_save_to_state

    async def save(self, data: dict, state: FSMContext, user: User, field_name: str, lang: str):
        email = data.get(field_name)
        await send_confirm_email(email, user.chat_id, self.callback_url, self.purpose, lang)

        if self.need_save_to_state:
            await state.update_data(data)
