from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from utils.router import Router
from ..states import ChangeTelegram


async def change_telegram_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, lang: str,
):
    await ChangeTelegram.first()
    await Router.state_menu(callback_query, state, lang)


def register_change_telegram_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        change_telegram_button_handler,
        callback_mode="change_telegram",
        state="*",
    )
