from datetime import datetime

from pydantic import BaseModel, EmailStr, HttpUrl


class CheckChatId(BaseModel):
    chat_id: int
    is_exists: bool


class CheckEmail(BaseModel):
    email: EmailStr
    is_exists: bool


class IsTGConnected(BaseModel):
    email: EmailStr
    is_connected: bool


class TokenData(BaseModel):
    token: str
    token_type: str

    @property
    def token_str(self):
        return f"{self.token_type} {self.token}"


class SentConfirmEmail(BaseModel):
    sent: bool
    email: EmailStr


class User(BaseModel):
    id: str
    status: str
    email: str | None = None
    chat_id: int | None = None
    first_name: str | None = None
    last_name: str | None = None
    username: str | None = None
    photo_url: str | None = None


class CallbackParams(BaseModel):
    url: HttpUrl
    params: dict


class TelegramUserData(BaseModel):
    id: int
    first_name: str
    last_name: str | None = None
    username: str | None = None
    photo_url: str | None = None


class BaseChangeAuthData(BaseModel):
    email: EmailStr
    password: str
    telegram_user: TelegramUserData
    bot_token: str


class ChangePasswordData(BaseChangeAuthData):
    telegram_user: TelegramUserData | None = None
    bot_token: str | None = None


class ChangeEmailData(BaseChangeAuthData):
    pass


class ChangeTelegramData(BaseChangeAuthData):
    pass


class AuthorizationChanged(BaseModel):
    ok: bool


class RegisterUserData(BaseModel):
    is_accept_agreement: bool
    accept_agreement_datetime: datetime | None = None
    telegram_user: TelegramUserData
    bot_token: str
    from_app: str = "telegram"


class UpdateUserData(BaseModel):
    first_name: str | None = None
    last_name: str | None = None
    username: str | None = None
    photo_url: str | None = None
