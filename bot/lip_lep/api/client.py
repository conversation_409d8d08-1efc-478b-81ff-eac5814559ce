import json
import logging
from typing import Type

from aiogram import types
from aiohttp import ClientSession, ClientResponse
from pydantic import BaseModel

from utils.type_vars import T
from . import errors, schemas
from .errors import ApiUnavailableError
from .helpers import build_authorize_body, build_telegram_data
from .schemas import ChangePasswordData, ChangeEmailData, ChangeTelegramData


class LipLepApiClient:

    def __init__(self, host: str, port: str, from_app: str):
        self.from_app = from_app
        self.base_url = f"{host}:{port}"

    async def make_request(
            self, method: str, path: str,
            headers: dict | BaseModel = None,
            query: dict | BaseModel = None,
            body: dict | BaseModel = None,
            lang: str = None,
            token: str = None,
            return_model: Type[T] = dict,
    ) -> T:
        if not path.startswith("/"):
            path = f"/{path}"

        url = f"{self.base_url}{path}"

        if isinstance(headers, BaseModel):
            headers = headers.dict(exclude_unset=True)
        elif not isinstance(headers, dict):
            headers = {}

        if "Accept-Language" not in headers and lang:
            headers["Accept-Language"] = lang

        if "Authorization" not in headers and token:
            headers["Authorization"] = f"bearer {token}"

        if isinstance(query, BaseModel):
            query = query.dict(exclude_unset=True)

        kwargs = {
            "url": url,
            "headers": headers,
        }

        if query:
            kwargs["params"] = query
        if body:
            if isinstance(body, BaseModel):
                kwargs["json"] = json.loads(body.json())
            else:
                kwargs["json"] = body

        async with ClientSession() as session:
            method = getattr(session, method.lower())

            try:
                resp: ClientResponse = await method(**kwargs)
            except Exception as e:
                logger = logging.getLogger()
                logger.error(e, exc_info=True)
                raise ApiUnavailableError(lang)

            try:
                result = await resp.json()
            except Exception as e:
                logger = logging.getLogger()
                logger.error(e, exc_info=True)
                result = {}

            if resp.ok:
                if return_model:
                    return return_model(**result)

                return result
            else:
                raise errors.ApiError.detect(resp.status, result)

    async def check_chat_id(self, lang: str, chat_id: int):
        path = f"/users/checkChatID/{chat_id}"
        return await self.make_request("get", path, lang=lang, return_model=schemas.CheckChatId)

    async def check_email(self, lang: str, email: str):
        return await self.make_request("get", f"/users/checkEmail/{email}", lang=lang, return_model=schemas.CheckEmail)

    async def check_is_tg_connected(self, lang: str, email: str):
        return await self.make_request(
            "get", f"/users/checkIsConnectedTG/{email}",
            lang=lang, return_model=schemas.IsTGConnected,
        )

    async def login(
            self, lang: str,
            email: str = None,
            password: str = None,
            telegram_user: types.User = None,
            bot_token: str = None,
    ):
        body = build_authorize_body(email, password, telegram_user, bot_token)

        return await self.make_request("post", "/users/login", body=body, lang=lang, return_model=schemas.TokenData)

    async def send_confirm_email(self, lang: str, email: str, purpose: str, callback_params: schemas.CallbackParams):
        body = {
            "email": email,
            "from_app": self.from_app,
            "purpose": purpose,
            "callback_params": callback_params.dict(),
        }

        return await self.make_request("post", "/users/sendConfirmEmail", body=body, lang=lang,
                                       return_model=schemas.SentConfirmEmail)

    async def create_user(
            self, lang: str,
            data: schemas.RegisterUserData,
    ):
        body = build_authorize_body(telegram_user=data.telegram_user, bot_token=data.bot_token)
        body["is_accept_agreement"] = data.is_accept_agreement
        body["accept_agreement_datetime"] = data.accept_agreement_datetime.timestamp()
        body["from_app"] = data.from_app

        return await self.make_request("post", "/users/create", body=body, lang=lang, return_model=schemas.TokenData)

    async def update_user(
            self, lang: str, token: str,
            data: schemas.UpdateUserData | dict
    ):
        if isinstance(data, schemas.UpdateUserData):
            data = data.dict(exclude_unset=True)
        return await self.make_request(
            "patch", "/users/update",
            body=data, lang=lang,
            token=token, return_model=schemas.User,
        )

    async def get_me(self, lang: str, token: str):
        return await self.make_request("get", "/users/me", lang=lang, token=token, return_model=schemas.User)

    async def set_email(self, lang: str, email: str, password: str, token: str):
        body = {
            "email": email,
            "password": password,
        }

        return await self.make_request(
            "post", "/users/setEmail",
            body=body, lang=lang, token=token,
            return_model=schemas.AuthorizationChanged,
        )

    async def set_telegram(self, lang: str, telegram_user: types.User | types.Chat, bot_token: str, token: str):
        body = build_telegram_data(telegram_user, bot_token)
        return await self.make_request(
            "post", "/users/setTelegram",
            body=body, lang=lang, token=token,
            return_model=schemas.AuthorizationChanged,
        )

    async def change_email(self, lang: str, data: ChangeEmailData):
        body = build_authorize_body(**data.dict())

        return await self.make_request(
            "post", "/users/changeEmail",
            body=body, lang=lang,
            return_model=schemas.AuthorizationChanged,
        )

    async def change_telegram(self, lang: str, data: ChangeTelegramData):
        body = build_authorize_body(**data.dict())

        return await self.make_request(
            "post", "/users/changeTelegram",
            body=body, lang=lang,
            return_model=schemas.AuthorizationChanged,
        )

    async def change_password(self, lang: str, data: ChangePasswordData):
        body = build_authorize_body(**data.dict())

        return await self.make_request(
            "post", "/users/changePassword",
            body=body, lang=lang,
            return_model=schemas.AuthorizationChanged,
        )
