import hashlib
import hmac
import logging
from datetime import datetime

from aiogram import types

from lip_lep.api.schemas import TelegramUserData


def build_telegram_data(telegram_user: types.Chat | types.User | TelegramUserData | dict, bot_token: str):
    if isinstance(telegram_user, types.User) and telegram_user.is_bot:
        raise ValueError(f"telegram_user must be typeof {types.Chat} or not is_bot")

    if isinstance(telegram_user, types.Chat | types.User):
        telegram_user = telegram_user.to_python()

    if isinstance(telegram_user, dict):
        telegram_user = TelegramUserData(**telegram_user)

    telegram_data = telegram_user.dict(exclude_none=True, exclude_unset=True)
    telegram_data["auth_date"] = int(datetime.utcnow().timestamp())
    secret = hashlib.sha256(bot_token.encode('utf-8'))
    check_string = '\n'.join(map(lambda k: f'{k}={telegram_data[k]}', sorted(telegram_data)))
    hash_str = hmac.new(secret.digest(), check_string.encode('utf-8'), digestmod=hashlib.sha256).hexdigest()
    telegram_data["hash"] = hash_str
    return telegram_data


def build_authorize_body(
        email: str = None, password: str = None,
        telegram_user: types.Chat | types.User | TelegramUserData | dict = None,
        bot_token: str = None,
):
    body = {}

    if telegram_user and bot_token:
        body["telegram_data"] = build_telegram_data(telegram_user, bot_token)

    if email and password:
        body["email"] = email
        body["password"] = password

    if not body:
        raise ValueError("one of (email and password) or (telegram_user and token) must be specified")

    return body
