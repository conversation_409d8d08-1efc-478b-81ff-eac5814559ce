from typing import Type

from utils.localisation import localisation


class ApiError(Exception):
    code: int = None

    __errors: list[Type["ApiError"]] = []

    def __init__(self, code: int, response_data: dict):
        self.code = code
        self.text = response_data.get("detail", "")

        super().__init__(self.text)

    def __init_subclass__(cls, **kwargs):
        ApiError.__errors.append(cls)

    @classmethod
    def check(cls, code: int):
        return cls.code == code

    @classmethod
    def detect(cls, code: int, response_data: dict):
        for error in cls.__errors:
            if error.check(code):
                return error(code, response_data)
        else:
            return ApiError(code, response_data)


class ApiUnavailableError(ApiError):
    code = 503

    def __init__(self, lang: str):
        super().__init__(503, {"detail": "Coludn't connect to lip lep"})


class ApiUnauthorized(ApiError):
    code = 401


class ApiBadRequest(ApiError):
    code = 400


class ApiConflict(ApiError):
    code = 409


class ApiValidationError(ApiError):
    code = 426


class ApiInternalServerError(ApiError):
    code = 500
