from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from utils.router import Router
from ..states import ChangeEmail


async def change_email_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, lang: str,
):
    await ChangeEmail.first()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


def register_change_email_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        change_email_button_handler,
        callback_mode="change_email",
        state="*",
    )
