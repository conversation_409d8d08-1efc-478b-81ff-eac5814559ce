from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import ClientBot
from utils.router import Router
from utils.text import f

from ..api import api
from ..api.schemas import ChangeEmailData, TelegramUserData

from .states import ChangeEmail


async def send_enter_password(message: types.Message, lang: str):
    return await message.answer(await f("lip lep change email enter password", lang))


async def send_enter_email(message: types.Message, lang: str):
    return await message.edit_text(await f("lip lep change email enter email", lang))


async def send_confirm_email(message: types.Message, lang: str):
    return await message.edit_text(await f("lip lep wait for confirm email message", lang))


async def change_email(message: types.Message, state: FSMContext, lang: str):
    bot = await ClientBot.get_lip_lep_bot()
    data = ChangeEmailData(
        telegram_user=TelegramUserData(**message.from_user.to_python()),
        bot_token=bot.token,
        **await state.get_data(),
    )
    await state.finish()

    await api.change_email(lang, data)
    return await message.answer(await f("lip lep email successfully changed", lang))


def register_change_email_routes(router: Router):
    router.add_route(ChangeEmail.Password, send_enter_password)
    router.add_route(ChangeEmail.Email, send_enter_email)
    router.add_route(ChangeEmail.ConfirmEmail, send_confirm_email)
    router.add_route(ChangeEmail.ChangeEmail, change_email)
