from config import LIP_LEP_BOT_URL
from psutils.forms import WizardF<PERSON>, fields, validators

from ..data_savers import ConfirmEmailDataSaver
from ..validators import EmailIsNotExistsValidator, ConfirmEmailValidator
from .states import ChangeEmail


class ChangeEmailForm(WizardForm):
    state_group = ChangeEmail

    password = fields.TextField()
    email = fields.TextField(
        input_validator=validators.EmailValidator() & EmailIsNotExistsValidator(),
        data_saver=ConfirmEmailDataSaver(
            "change_email",
            f"{LIP_LEP_BOT_URL}/changeEmail/emailConfirmed",
        )
    )
    confirm_email = fields.MessageField("*", input_validator=ConfirmEmailValidator())
