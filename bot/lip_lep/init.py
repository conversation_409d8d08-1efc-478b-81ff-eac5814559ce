import config as cfg

from aiohttp import web

from aiogram import Dispatcher

from core.bot.forms import get_forms
from utils.router import get_router
from utils.redefined_classes import Bot
from utils.redefined_classes.aiogram.my_storages import MyRedisStorage

app = web.Application()

bot = Bot(cfg.ROOT_BOT_API_TOKEN)

storage = MyRedisStorage(db=2, host=cfg.REDIS_HOST, port=cfg.REDIS_PORT)
dp = Dispatcher(bot, storage=storage)

router = get_router(dp)
forms = get_forms()

app["bot"] = bot
