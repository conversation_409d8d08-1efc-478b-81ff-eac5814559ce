import logging
from pprint import pformat

from aiogram import types, Dispatcher
from aiogram.dispatcher.storage import BaseStorage

from config import LIP_LEP_WEB_APP_PATH
from lip_lep.api import schemas
from utils.text import f

from .keyboards import get_lip_lep_user_keyboard


async def send_lip_lep_user_info(message: types.Message, user: schemas.User, lang: str, with_keyboard: bool = True):
    keyboard = await get_lip_lep_user_keyboard(user, lang) if with_keyboard else None
    await message.answer(pformat(user.dict()), reply_markup=keyboard)


async def get_token(storage: BaseStorage, user_chat_id: int) -> str:
    bucket = await storage.get_bucket(user=user_chat_id)
    token = bucket.get("token")
    return token


async def set_token(token: str, storage: BaseStorage, user_chat_id: int) -> str:
    await storage.set_bucket(user=user_chat_id, bucket={"token": token})
    return await get_token(storage, user_chat_id)


async def get_lip_lep_menu_button(user_chat_id: int | None, lang: str):
    default_button = types.MenuButtonDefault()

    if not user_chat_id:
        return default_button

    dp = Dispatcher.get_current()
    token = await get_token(dp.storage, user_chat_id)

    logger = logging.getLogger()
    logger.error(token)

    if not token:
        return default_button

    text = await f("lip lep menu button", lang)
    url = f"{LIP_LEP_WEB_APP_PATH}/{lang}?token={token}"
    return types.MenuButtonWebApp(text=text, web_app=types.WebAppInfo(url=url))
