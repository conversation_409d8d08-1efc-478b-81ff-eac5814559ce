from aiogram import types

from psutils.forms.validators.base import Validator
from utils.text import f

from lip_lep.api import api


class ConfirmEmailValidator(Validator):
    async def validate(self, message: types.Message, lang: str) -> bool | dict | types.Message:
        return await message.answer(await f("lip lep wait for confirm email message", lang))


class EmailIsNotExistsValidator(Validator):
    async def validate(self, message: types.Message, email: str, lang: str) -> bool | dict | types.Message:
        result = await api.check_is_email_exists(lang, email)
        if result.is_exists:
            return await message.answer(await f("lip lep email is already exists error", lang))

        return True

