from aiogram import types, Dispatcher
from aiogram.dispatcher.handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from aiogram.dispatcher.middlewares import BaseMiddleware

from db.models import ClientBot, User
from utils.router import Router

from .api import api
from .api.errors import ApiUnauthorized
from .connect_telegram.states import ConnectTelegram
from .functions import get_token


class TGAuthorizationMiddleware(BaseMiddleware):

    @staticmethod
    async def pre_process(obj: types.Message | types.CallbackQuery, data: dict):
        from_user = obj.from_user
        p4s_user: User = data.get("user")
        if not p4s_user:
            return

        lang = data.get("lang")

        dp = Dispatcher.get_current()

        token = await get_token(dp.storage, from_user.id)

        lip_lep_user = None

        if token:
            try:
                lip_lep_user = await api.get_me(lang, token)
            except ApiUnauthorized:
                token = None

        if not token:
            result = await api.check_chat_id(lang, from_user.id)
            if result.is_exists:
                bot = await ClientBot.get_current()
                try:
                    token_data = await api.login(lang, telegram_user=from_user, bot_token=bot.token)
                except ApiUnauthorized:
                    token = None
                else:
                    token = token_data.token

                if token:
                    try:
                        lip_lep_user = await api.get_me(lang, token)
                    except ApiUnauthorized:
                        token = None

        await dp.storage.update_bucket(user=from_user.id, bucket={"token": token})

        data.update({"token": token, "lip_lep_user": lip_lep_user, "is_lip_lep_login": bool(lip_lep_user)})

        if lip_lep_user:
            if lip_lep_user.chat_id != from_user.id and (
                    not isinstance(obj, types.Message) or
                    not obj.is_command() or
                    obj.get_command() not in ["/start", "/exit"]
            ):
                await ConnectTelegram.first()
                state = dp.current_state(user=from_user.id)
                await Router.state_menu(state=state, set_state_message=True)
                raise CancelHandler()
            else:
                keys = ("first_name", "last_name", "username", "photo_url")
                new_data = {}
                for key in keys:
                    if getattr(lip_lep_user, key) != getattr(p4s_user, key):
                        new_data[key] = getattr(p4s_user, key)
                if new_data:
                    if "username" in new_data:
                        new_data["telegram_username"] = new_data.pop("username", None)
                    lip_lep_user = await api.update_user(lang, token, new_data)
                    data.update(lip_lep_user=lip_lep_user)

    async def on_pre_process_message(self, message: types.Message, data: dict):
        await self.pre_process(message, data)

    async def on_pre_process_callback_query(self, callback_query: types.CallbackQuery, data: dict):
        await self.pre_process(callback_query, data)
