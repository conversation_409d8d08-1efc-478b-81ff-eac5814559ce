from aiogram import Dispatcher
from aiohttp import web
from psutils.local import setup_psutils_localisation

from core.aiogram_middlewares import (
    ExceptionHandlersMiddleware,
    LinkUncompressorMiddleware,
)
from core.aiogram_middlewares.cache_time import CacheTimeMiddleware
from core.aiogram_middlewares.callback_data import CallbackDataMiddleware
from core.aiogram_middlewares.create_or_update import CreateOrUpdateUserMiddleware
from core.chat.filters.aiogram import bind_chat_filters
from core.chat.handlers import *
from core.handlers import *
from core.user.agreement_processor.buttons_handlers import \
    setup_tg_handlers as setup_agreement_handlers
from lip_lep.middlewares import TGAuthorizationMiddleware
from utils.filters.bind import bind_general_filters
from utils.redefined_classes import Bot
from utils.router import Router
from .change_email.forms import ChangeEmailForm
from .change_email.handlers import *
from .change_password.forms import ChangePasswordForm
from .change_password.handlers import *
from .connect_email.forms import ConnectEmailForm
from .connect_email.handlers import *
from .connect_telegram.handlers import *
from .login.forms import LoginForm
from .login.handlers import *
from .main.handlers import *
from .register.forms import RegisterForm
from .register.handlers import *


async def on_startup(dp: Dispatcher):
    await setup_psutils_localisation()

    Router.set_current(dp["router"])
    Dispatcher.set_current(dp)
    Bot.set_current(dp.bot)


async def on_shutdown(dp: Dispatcher):
    await dp.storage.close()


def register_web_handlers(app: web.Application, dp: Dispatcher):
    register_main_web_handlers(app, dp)
    ConnectEmailConfirmedHandler.setup(app, dp)
    ConnectTelegramEmailConfirmedHandler.setup(app, dp)
    ChangePasswordEmailConfirmedHandler.setup(app, dp)
    ChangeEmailConfirmedHandler.setup(app, dp)


def bind_filters(dp: Dispatcher):
    bind_general_filters(dp)
    bind_chat_filters(dp)


def register_bot_handlers(dp: Dispatcher):
    Dispatcher.set_current(dp)

    dp.setup_middleware(ExceptionHandlersMiddleware())
    dp.setup_middleware(CreateOrUpdateUserMiddleware())
    dp.setup_middleware(TGAuthorizationMiddleware())
    dp.setup_middleware(CallbackDataMiddleware())
    dp.setup_middleware(LinkUncompressorMiddleware())
    dp.setup_middleware(CacheTimeMiddleware())

    register_main_errors_handlers(dp)

    register_bot_blocked_handlers(dp)

    register_main_commands_handlers(dp)
    register_chat_commands_handlers(dp)
    register_general_commands_handlers(dp)

    dp["router"].setup_handlers()

    ConnectEmailForm.setup_handlers(dp)
    ChangePasswordForm.setup_handlers(dp)
    ChangeEmailForm.setup_handlers(dp)
    LoginForm.setup_handlers(dp)
    RegisterForm.setup_handlers(dp)

    register_main_message_handlers(dp)

    setup_agreement_handlers(dp)
    register_chat_callback_handlers(dp)
    register_general_callback_handlers(dp)
    register_login_callback_handlers(dp)
    register_connect_email_callback_handlers(dp)
    register_change_password_handlers(dp)
    register_change_email_callback_handlers(dp)
    register_register_button_handlers(dp)

    register_general_exception_handlers(dp)


__all__ = [
    "on_startup", "on_shutdown",
    "register_web_handlers",
    "register_bot_handlers",
    "bind_filters",
]
