from config import LIP_LEP_BOT_URL
from psutils.forms import WizardF<PERSON>, fields, validators

from ..data_savers import ConfirmEmailDataSaver
from ..validators import Confirm<PERSON>mailValidator, EmailIsNotExistsValidator
from .states import ConnectEmail


class ConnectEmailForm(WizardForm):
    state_group = ConnectEmail

    email = fields.TextField(
        input_validator=validators.EmailValidator() & EmailIsNotExistsValidator(),
        data_saver=ConfirmEmailDataSaver("set_email", f"{LIP_LEP_BOT_URL}/connectEmail/emailConfirmed"),
    )
    confirm_email = fields.MessageField(
        "*", input_validator=ConfirmEmailValidator(),
    )
    password = fields.TextField(
        min_length=8
    )
    confirm_password = fields.TextField(
        input_validator=validators.PasswordsMatchValidator(),
        data_saver="do_nothing",
    )
