from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from utils.router import Router
from utils.text import f
from ..states import ConnectEmail


async def connect_email_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await ConnectEmail.first()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def connect_email_later_button_handler(callback_query: types.CallbackQuery, lang: str):
    await callback_query.message.edit_text(await f("lip lep connect email later message", lang))


def register_connect_email_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        connect_email_button_handler,
        callback_mode="connect_email",
        state="*",
    )

    dp.register_callback_query_handler(
        connect_email_later_button_handler,
        callback_mode="connect_email_later",
        state="*",
    )
