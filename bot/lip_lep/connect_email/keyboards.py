from utils.redefined_classes import InlineKb, InlineBtn
from utils.text import f


async def get_connect_email_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb()

    keyboard.row(InlineBtn(await f("lip lep connect email button", lang), callback_data="connect_email"))
    keyboard.row(InlineBtn(await f("lip lep connect email later button", lang), callback_data="connect_email_later"))

    return keyboard
