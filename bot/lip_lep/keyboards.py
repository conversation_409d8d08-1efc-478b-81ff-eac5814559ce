from utils.redefined_classes import InlineKb, InlineBtn
from utils.text import f, c
from .api import schemas


async def get_lip_lep_user_keyboard(user: schemas.User, lang: str):
    keyboard = InlineKb()

    if not user.email:
        keyboard.row(InlineBtn(await f("lip lep connect email button", lang), callback_data="connect_email"))
    else:
        keyboard.row(InlineBtn(await f("lip lep change email button", lang), callback_data="change_email"))
        keyboard.row(InlineBtn(await f("lip lep change password button", lang), callback_data=c("change_password", m="new")))

    return keyboard
