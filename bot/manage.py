import asyncio
import curses
import getpass
import logging
import os
import sys
from datetime import datetime, timedelta

from jose import jwt
from passlib.handlers.pbkdf2 import pbkdf2_sha256
from psutils.convertors import str_to_datetime, str_to_int
from psutils.exceptions import LessT<PERSON><PERSON><PERSON>Error, NotIntError

from config import ALGORITH<PERSON>, ANONYMOUS_USER_EMAIL, ANONYMOUS_USER_NAME, SECRET_KEY
from core.auth.functions import validate_password
from core.brand.functions import auto_create_brand
from core.ext.data_manager.exporter import StoreExporter
from core.ext.data_manager.importer import StoreImporter
from core.ext.types import StatusTypeLiteral
from db import DBSession, crud, sess
from db.crud import check_access_to_action_sync
from db.models import (
    Brand, CustomHTMLPage, ExternalSystemToken, Group, Scope, StoreCart, StoreFavorite, User, UserAnalyticAction,
    UserClientBotActivity, Customer,
)
from utils.email_funcs import is_valid_email
from utils.update_localisation import update_localisation


def input_or_exit(prompt: str) -> str:
    text = input(prompt)
    if text in ("q", "quit", "quit()", "exit", "exit()"):
        exit()

    return text


def interval_to_str(interval: timedelta):
    time_interval_data = {
        "day": round(interval.days),
        "hour": round(interval.total_seconds() % 86400 // 3600),
        "minute": round(interval.total_seconds() % 86400 % 3600 // 60),
        "second": round(interval.total_seconds() % 86400 % 3600 % 60)
    }
    texts = list()
    time_units = time_interval_data.keys()
    for time_unit in time_units:
        value = time_interval_data[time_unit]
        if value <= 0:
            continue
        if value == 1:
            text = f"one {time_unit}"
        else:
            text = f"{value} {time_unit}s"
        texts.append(text)
    return " ".join(texts)


async def import_():
    brand: Brand | None = None
    file_path: str | None = None

    if len(sys.argv) > 4:
        print("too many arguments")
        exit(2)

    if len(sys.argv) >= 3:
        try:
            brand_id = str_to_int(sys.argv[2])
        except NotIntError:
            print("the second argument (brand`s id) must be an integer")
            exit(2)
        except LessThenZeroError:
            print("the second argument (brand`s id) must be greater than zero")
            exit(2)
        else:
            if brand_id == 0:
                print("the second argument (brand`s id) must be greater than zero")
                exit(2)
            brand = await Brand.get(brand_id)
            if not brand:
                print("the second argument (brand`s id) must be an id of existing brand")
                exit(2)

    if len(sys.argv) == 4:
        file_path = sys.argv[3]

        if not os.path.isfile(file_path):
            print("the third argument (file_path) must be a valid path to file")
            exit(2)

        if not file_path.endswith(".xls") and not file_path.endswith(".xlsx"):
            print("the third argument (file_path) must be a path to excel file (.xls, .xlsx)")
            exit(2)

    while brand is None:
        try:
            brand_id = str_to_int(input_or_exit("Enter brand`s id: "))
        except NotIntError:
            print("Brand`s id must be an integer")
        except LessThenZeroError:
            print("Brand`s id must be greater than zero")
        else:
            if brand_id == 0:
                print("Brand`s id must be greater than zero")
            else:
                brand = await Brand.get(brand_id)
                if not brand:
                    print(f"Brand with id {brand_id} not found")

    while file_path is None:
        if not os.path.isfile(file_path := input_or_exit("Enter path to excel file: ")):
            print("File path must be a valid path to excel file")
            file_path = None

        elif not file_path.endswith(".xls") and not file_path.endswith(".xlsx") and not file_path.endswith(".numbers"):
            print("File path must be a path to excel file (.xls, .xlsx, .numbers)")
            file_path = None

    importer = StoreImporter(
        brand.id, "excel"
    )

    class Checker:
        def __init__(self):
            self.is_inited = False
            self.console = curses.initscr()
            self.iteration = 0

        async def __call__(self, status: StatusTypeLiteral, time_passed: timedelta):
            try:
                if not self.is_inited:
                    self.is_inited = True
                else:
                    self.console.erase()

                match status:
                    case "not_started":
                        self.console.addstr("Import initialising")
                    case "loading":
                        dots = "." * (self.iteration % 3 + 1)
                        self.console.addstr(f"Loading data{dots}")
                    case "saving":
                        dots = "." * (self.iteration % 3 + 1)
                        self.console.addstr(f"Saving data{dots}")
                    case "error":
                        self.console.addstr("\nImport error")
                    case "done":
                        self.console.addstr("\nImport successfully finished")

                self.console.addstr("\n")
                self.console.addstr(f"Time elapsed: {interval_to_str(time_passed)}")
                self.console.refresh()
            finally:
                if status in ("error", "done"):
                    curses.endwin()
                self.iteration += 1

    try:
        group = await Group.get(brand.group_id)
        await importer.start_with_checker(Checker(), timeout=1, excel_file=file_path, lang=group.lang)
    except Exception as e:
        curses.endwin()
        logging.error(e, exc_info=True)
    else:
        print("\nImport successfully finished")
    finally:
        curses.endwin()


async def export():
    brand: Brand | None = None

    while brand is None:
        try:
            brand_id = str_to_int(input_or_exit("Enter brand`s id: "))
        except NotIntError:
            print("Brand`s id must be an integer")
        except LessThenZeroError:
            print("Brand`s id must be greater than zero")
        else:
            if brand_id == 0:
                print("Brand`s id must be greater than zero")
            else:
                brand = await Brand.get(brand_id)
                if not brand:
                    print(f"Brand with id {brand_id} not found")

    exporter = StoreExporter(
        brand.id, "excel",
    )

    class Checker:
        def __init__(self):
            self.is_inited = False
            self.console = curses.initscr()
            self.iteration = 0

        async def __call__(self, status: StatusTypeLiteral, time_passed: timedelta):
            try:
                if not self.is_inited:
                    self.is_inited = True
                else:
                    self.console.erase()

                match status:
                    case "not_started":
                        self.console.addstr("Export initialising")
                    case "loading":
                        dots = "." * (self.iteration % 3 + 1)
                        self.console.addstr(f"Loading data{dots}")
                    case "saving":
                        dots = "." * (self.iteration % 3 + 1)
                        self.console.addstr(f"Saving data{dots}")
                    case "error":
                        self.console.addstr("Export error")
                    case "done":
                        self.console.addstr("Export successfully finished")

                self.console.addstr("\n")
                self.console.addstr(f"Time passed: {interval_to_str(time_passed)}")
                self.console.refresh()
            finally:
                if status in ("error", "done"):
                    curses.endwin()
                self.iteration += 1

    try:
        result = await exporter.start_with_checker(Checker(), timeout=1, lang=brand.group.lang)
    except Exception as e:
        curses.endwin()
        logging.error(e, exc_info=True)
    else:
        curses.endwin()
        print(f"Export successfully finished\nFile saved at {result.get('result')}")


async def generate_example():
    brand: Brand | None = None

    while brand is None:
        try:
            brand_id = str_to_int(input_or_exit("Enter brand`s id: "))
        except NotIntError:
            print("Brand`s id must be an integer")
        except LessThenZeroError:
            print("Brand`s id must be greater than zero")
        else:
            if brand_id == 0:
                print("Brand`s id must be greater than zero")
            else:
                brand = await Brand.get(brand_id)
                if not brand:
                    print(f"Brand with id {brand_id} not found")

    exporter = StoreExporter(
        brand.id, "excel",
    )

    result = await exporter.generate_template(lang=brand.group.lang)
    print(f"Successfully generated template\nFile saved at {result}")


async def create_brand():
    owner: User | None = None
    name: str | None = None

    if len(sys.argv) > 3:
        print("too many arguments")
        exit(2)

    if len(sys.argv) >= 3:
        try:
            owner_id = str_to_int(sys.argv[2])
        except NotIntError:
            print("the second argument (owner`s id) must be an integer")
            exit(2)
        except LessThenZeroError:
            print("the second argument (owner`s id) must be greater than zero")
            exit(2)
        else:
            if owner_id == 0:
                print("the second argument (owner`s id) must be greater than zero")
                exit(2)
            owner = await User.get_by_id(owner_id)
            if not owner:
                print("the second argument (owner`s id) must be an id of existing user")
                exit(2)

    if len(sys.argv) >= 4:
        name = sys.argv[3]

    while owner is None:
        try:
            owner_id = str_to_int(input_or_exit("Enter owner`s id: "))
        except NotIntError:
            print("Owner`s id must be an integer")
        except LessThenZeroError:
            print("Owner`s id must be greater than zero")
        else:
            if owner_id == 0:
                print("Owner`s id must be greater than zero")
            else:
                owner = await User.get_by_id(owner_id)
                if not owner:
                    print(f"User with id {owner_id} not found")

    while name is None:
        if not (name := input_or_exit("Enter group`s name: ").strip()):
            print("Group`s name must be a string with length more than 0")
            name = None

    group = await crud.create_group(name, owner)
    print(f"Group created.\nID: {group.id}\nName: {name}")

    brand = await auto_create_brand(group)
    print(f"Brand created.\nID: {brand.id}\nName: {name}")

    exit(0)


async def update_local():
    print("updating...")
    await update_localisation()
    print("updated...")


# example: python3 manage.py generate_external_system_token test_system_name "20.06.2023 10:30"
async def generate_external_system_token(system_name: str, expire: str | None = None):
    expire_datetime = None
    if expire:
        expire_datetime = str_to_datetime(expire)

    external_system = await ExternalSystemToken.get(external_system_type=system_name)
    if not external_system:
        external_system = await ExternalSystemToken.create_or_update(external_system_type=system_name, expire=expire)

    to_encode = {
        "system_name": system_name,
        "id": external_system.id,
    }
    if expire_datetime:
        to_encode.update({"exp": expire})

    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

    print(f"*** token: {encoded_jwt} for {external_system.external_system_type}({external_system.id}) ***")


# deletes user and basic related data (not including orders)
async def delete_user(user_id: int):
    user = await User.get_by_id(user_id)
    if not user:
        print(f"User with id {user_id} not found")
        exit(2)
    query = sess().query(UserClientBotActivity).filter(UserClientBotActivity.user_id == user_id)
    query.delete()
    query = sess().query(StoreCart).filter(StoreCart.user_id == user_id)
    query.delete()
    query = sess().query(StoreFavorite).filter(StoreFavorite.user_id == user_id)
    query.delete()
    query = sess().query(UserAnalyticAction).filter(UserAnalyticAction.user_id == user_id)
    query.delete()
    query = sess().query(Customer).filter(Customer.user_id == user_id)
    query.delete()
    query = sess().query(User).filter(User.id == user_id)
    query.delete()

    sess().commit()
    print(f"User with id {user_id} successfully deleted")


def add_anonymous_user():
    user = User(
        lang="en",
        first_name=ANONYMOUS_USER_NAME,
        email=ANONYMOUS_USER_EMAIL,
        is_accepted_agreement=True,
        accept_agreement_datetime=datetime.utcnow(),
        is_confirmed_email=True,
    )

    sess().add(user)
    sess().commit()
    print(f"anonymous user with id {user.id} successfully created")


def add_custom_html_page():
    if len(sys.argv) < 4:
        raise ValueError("invalid params count")

    media_object_id = str_to_int(sys.argv[3], only_positive=True, no_error=True)
    if not media_object_id:
        raise ValueError("Invalid media_object_id")

    custom_html_page = CustomHTMLPage(
        name=sys.argv[2],
        html_media_id=media_object_id,
    )
    sess().add(custom_html_page)
    sess().commit()
    print(f"Added {custom_html_page}")


def create_superadmin():
    user = None
    is_exists = False
    while not user:
        email = input("Enter email: ").strip()

        if not email:
            print("Email is required!")
            continue

        user = User.get_sync(email=email)

        is_exists = bool(user)
        if not user:
            if not is_valid_email(email):
                raise ValueError("Email invalid!")

            while True:
                password = getpass.getpass("Create password:").strip()
                confirm_password = getpass.getpass("Confirm password:").strip()

                if password != confirm_password:
                    print("Password does not match")
                    continue

                try:
                    validate_password(password)
                except Exception as e:
                    print(f"Password invalid: {e.__class__.__name__}")
                    continue

                break

            user = User(
                email=email,
                first_name=email.split("@")[0],
                is_accepted_agreement=True,
                accept_agreement_datetime=datetime.utcnow(),
                is_confirmed_email=True,
                hashed_password=pbkdf2_sha256.hash(password)
            )
            sess().add(user)

    if not is_exists or not check_access_to_action_sync("platform:superadmin", "user", target_id=user.id):
        scope = Scope(
            target="user",
            user=user,
            scope="platform:superadmin",
        )
        sess().add(scope)
        sess().commit()

        if is_exists:
            print(f"User {user.email} has been granted superadmin access!")
        else:
            print(f"Superadmin user {user.email} has been successfully created!")
    else:
        print(f"User {user.email} is already superadmin")


async def main():
    if len(sys.argv) < 2:
        print("Command missed")
        exit(2)

    command = sys.argv[1]

    with DBSession():

        match command:
            case "import":
                await import_()
            case "export":
                await export()
            case "generatetemplate":
                await generate_example()
            case "createbrand":
                await create_brand()
            case "generate_external_system_token":
                await generate_external_system_token(*sys.argv[2:])
            case "updatelocal":
                await update_local()
            case "delete_user":
                await delete_user(*sys.argv[2:])
            case "add_anonymous_user":
                add_anonymous_user()
            case "add_custom_html_page":
                add_custom_html_page()
            case "createsuperadmin":
                create_superadmin()
            case _:
                print(f"Unknown command {command}")
                exit(2)


if __name__ == '__main__':
    asyncio.run(main())
