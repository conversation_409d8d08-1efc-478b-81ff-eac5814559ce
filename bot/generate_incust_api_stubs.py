from incust_api.api import client, term, user
from inspect import isclass

APIS = {
    "client": client.api,
    "term": term.api,
    "user": user.api
}

FILE_PATH = "core/loyalty/api_modules.pyi"

BASE_FILE_TEXT = """from .api_proxy import APIModuleProxy
from incust_api.api import client, term, user
from db.models import LoyaltySettings


"""


def get_api_module_code(api_name: str, name: str):
    return f"    {name}: APIModuleProxy[LoyaltySettings, {api_name}.api.{name}]\n"


def main():
    file_text = BASE_FILE_TEXT

    for api_name, api in APIS.items():
        file_text += f"class {api_name.capitalize()}Api:\n"
        for module_name, module in vars(api).items():
            if not module_name.endswith("Api") or not isclass(module):
                continue

            file_text += get_api_module_code(api_name, module_name)

        file_text += "\n\n"

    with open(FILE_PATH, "w") as file:
        file.write(file_text)

    print(f"Stubs was written to {FILE_PATH}")


if __name__ == '__main__':
    main()
