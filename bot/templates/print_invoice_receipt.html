<!DOCTYPE html>
<html lang="uk">
<head>
  <meta charset="UTF-8" />
  <title>{{ title if title else localised_labels.receipt_label }}</title>
  <style>
    @page { size: {{ width }} auto; margin: 0; }
    body {
      width: {{ width }};
      margin: 0 auto;
      padding: 5px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      color: #000;
    }
    .receipt { padding: 10px; }
	.payed_label {font-weight: 700; font-size: 16px; color: #000; }
	.payed_amount { font-weight: 500; font-size: 14px; color: #000; }

    .logo { display: block; object-fit: cover;border-radius:4px; object-position: center; width: 48px; height: auto; margin: 0 auto 5px; margin-bottom:10px; }
	.receipt-title { text-align: center; font-size: 16px; font-weight: bold; margin: 5px 0 10px; }
    .receipt-header { text-align: center; font-size: 12px; line-height: 1.4; margin-bottom: 24px; }

	.checkbox {
		display: inline-block;
		width: 10px;
		min-width: 10px;
		min-height: 10px;
		height: 10px;
		border: 1px solid #000;
		margin-right: 4px;
		vertical-align: middle;
		border-radius: 3px;
	}

    .items-table table {
      width: 100%; border-collapse: collapse; table-layout: auto;
      font-size: 12px;
    }
    .items-table th, .items-table td {
      padding: 4px 2px; vertical-align: top; word-break: break-word;
    }
    .items-table th { border-bottom: 1px solid #000; text-align: left; }
    .items-table td { border-bottom: 1px dashed #ccc; text-align: left; }
    .items-table th:nth-child(1), .items-table td:nth-child(1) { width: 50%; }
    .items-table th:nth-child(2), .items-table td:nth-child(2) { width: 15%; white-space: nowrap; }
    .items-table th:nth-child(3), .items-table td:nth-child(3) { width: 15%; white-space: nowrap; }
    .items-table th:nth-child(4), .items-table td:nth-child(4) { width: 20%; text-align: right; white-space: nowrap; }

    .item-block { font-size: 12px; margin-bottom: 6px; margin-top: 8px }
    .item-block .line1 { display: flex; justify-content: space-between; gap:10px; }
    .item-block .line1 span { white-space: pre-wrap; }
    .item-block .line2 { margin-top: 2px; word-break: break-word; font-weight: bold }
	.item-block .right_row {white-space: pre-wrap; word-break: break-all;}

    hr.divider { border: none; border-top: 1px solid #212121; margin: 10px 0; }
    .total { margin-top: 8px; text-align: right; font-weight: bold; font-size: 15px; }
	.sum_for_pay { margin-top: 8px; text-align: right; font-weight: 500; font-size: 13px; }
    .footer { text-align: center; font-size: 12px; margin-top: 21px; }
  </style>
</head>
<body>
  <div class="receipt">
    {% if store_info and store_info.logo_url %}
      <img src="{{ store_info.logo_url }}" alt="Logo" class="logo" />
    {% elif group_info.logo_url %}
      <img src="{{ group_info.logo_url }}" alt="Logo" class="logo" />
    {% endif %}

    <div class="receipt-title">{{ localised_labels.receipt_label }}</div>
    <div class="receipt-header">
      	<div style="font-weight: bold;">{{ store_info.name or group_info.name }}</div>

		{% if store_info.address %}
			<div>{{ localised_labels.address_label }}: {{ store_info.address }}</div>
		{% endif %}

		{% if store_info.phone %}
			<div>{{ localised_labels.phone_label }}: {{ store_info.phone }}</div>
		{% elif group_info.phone %}
			<div>{{ localised_labels.phone_label }}: {{ group_info.phone }}</div>
		{% endif %}

		<div>{{ localised_labels.receipt_label }} #: {{ id }}</div>
		{% if paid_datetime %}
			<div>{{ paid_datetime.strftime("%d.%m.%Y %H:%M") }}</div>
		{% else %}
			<div>{{ time_created.strftime("%d.%m.%Y %H:%M") }}</div>
		{% endif %}

    </div>

    {% if items %}
      {% set w_val = (width.rstrip('mm')|int) if width.endswith('mm') else 0 %}

      {% if w_val and w_val <= 100 %}
        {% for item in items %}
			<div class="item-block">
			  <div style="display: flex; justify-content: space-between; gap:10px;">
				<span>
					{% if (item.quantity % 1) == 0 %}
					  {{ item.quantity|int }}
					{% else %}
					  {{ "%.3f"|format(item.quantity) }}
					{% endif %}
					 <span style="margin-left: 4px; margin-right: 4px">x</span>{{ "%.2f"|format(item.price) }}</span>
				<span> {{ "%.2f"|format(item.final_sum) }} {{ currency }}</span>
			  </div>
			  <div class="line2">
				   {% if draw_checkboxes %}
					   <span class="checkbox"></span>
				   {% endif %}
				  {{ item.name }}
			  </div>
			</div>
			{% if item.attributes %}
			  {% for attr in item.attributes %}
				{% set parent_qty = [ item.quantity|int, 1 ]|max %}
				{% set child_qty  = [ attr.quantity|int, 1 ]|max %}
				{% set line_qty   = parent_qty * child_qty %}
				{% set unit_price = attr.price %}
				{% set line_total = (line_qty * unit_price)|round(2) %}

				<div class="item-block">
				  <div class="line1">
					<span>
					  {{ line_qty }} × {{ unit_price }}
					</span>
					<span>
					  {{ line_total }} {{ currency }}
					</span>
				  </div>
				  <div class="line2">
					{% if draw_checkboxes %}
					  <span class="checkbox"></span>
					{% endif %}
					{{ attr.name }}
				  </div>
				</div>
			  {% endfor %}
			{% endif %}

        {% endfor %}

	  	<hr class="divider"/>

	  	{% if shipment_cost %}
			<div class="item-block">
			  <div class="line1">
				<span style="font-weight: bold">{{ localised_labels.delivery_price_label }}:</span>
				<span class="right_row">{{ "%.2f" | format(shipment_cost) }} {{ currency }}</span>
			  </div>
			</div>
		{% endif %}

	  	{% if discount_and_bonuses_redeemed %}
			<div class="item-block">
			  <div class="line1">
				<span style="font-weight: bold">{{ localised_labels.bonuses_and_discounts_label }}:</span>
				<span class="right_row">{{ "%.2f" | format(discount_and_bonuses_redeemed) }} {{ currency }}</span>
			  </div>
			</div>
		{% elif bonuses_redeemed %}
			<div class="item-block">
			  <div class="line1">
				<span style="font-weight: bold">{{ localised_labels.bonuses_label }}:</span>
				<span class="right_row">{{ "%.2f" | format(bonuses_redeemed) }} {{ currency }}</span>
			  </div>
			</div>
		{% endif %}

	  	{% if tips_sum %}
			<div class="item-block">
			  <div class="line1">
				<span>{{ localised_labels.tips_label }}:</span>
				<span class="right_row">{{ "%.2f"|format(tips_sum) }} {{ currency }}</span>
			  </div>
			</div>
	  	{% endif%}

		  {% if payer_fee %}
			<div class="item-block">
			  <div class="line1">
				<span>{{ localised_labels.fees_label }}:</span>
				<span class="right_row">{{ "%.2f"|format(payer_fee) }} {{ currency }}</span>
			  </div>
			</div>
	  	{% endif%}

	  	<div class="item-block">
          <div class="line1">
            <span>{{ localised_labels.total_for_payment_label }}:</span>
            <span class="right_row">{{ "%.2f"|format(total_sum_with_extra_fee) }} {{ currency }}</span>
          </div>
        </div>

	  	<hr class="divider"/>

	  	{% if show_payed_sum %}
			<div class="item-block">
			  <div class="line1">
				<span class="payed_label">{{ localised_labels.payed_sum_label }}:</span>
				<span class="payed_amount">{{ "%.2f"|format(paid_sum) }} {{ currency }}</span>
			  </div>
			</div>
		{% endif %}

	  	{% if payment_method %}
			<div class="item-block">
			  <div class="line1">
				<span>{{ localised_labels.payment_method_label }}:</span>
				<span class="right_row">{{ payment_method }}</span>
			  </div>
			</div>
	  	{% endif %}
      {% else %}
        <div class="items-table">
          <table>
            <thead><tr><th>{{ localised_labels.position_label }}</th><th>{{ localised_labels.count_label }}</th><th>{{ localised_labels.price_label }}</th><th>{{ localised_labels.sum_label }}</th></tr></thead>
            <tbody>
              {% for item in items %}
              <tr>
                <td style="display: flex; align-items: center;">
					{% if draw_checkboxes %}
						<span class="checkbox"></span>
					{% endif %}
					{{ item.name }}
				</td>
                <td>
					{% if (item.quantity % 1) == 0 %}
					  {{ item.quantity|int }}
					{% else %}
					  {{ "%.3f"|format(item.quantity) }}
					{% endif %}
				</td>
                <td>{{ "%.2f"|format(item.price) }} </td>
                <td>{{ "%.2f"|format(item.final_sum) }} {{ currency }}</td>
              </tr>
				  {% if item.attributes %}
					  {% for attr in item.attributes %}
						{% set parent_qty = [ item.quantity|int, 1 ]|max %}
						{% set child_qty  = [ attr.quantity|int, 1 ]|max %}

						{% set line_qty   = parent_qty * child_qty %}
						{% set unit_price = attr.price %}
						{% set line_total = line_qty * unit_price %}

						<tr>
						  <td style="display: flex; align-items: center;">
							{% if draw_checkboxes %}
							  <span class="checkbox"></span>
							{% endif %}
							{{ attr.name }}
						  </td>
						  <td>{{ line_qty }}</td>
						  <td>{{ unit_price }}</td>
						  <td>{{ line_total }} {{ currency }}</td>
						</tr>
					  {% endfor %}
					{% endif %}
              {% endfor %}
            </tbody>
          </table>
        </div>

		<hr class="divider" />

		{% if shipment_cost %}
			<div style="margin-top:5px;font-size:12px;">{{ localised_labels.delivery_price_label }}: {{ "%.2f"|format(shipment_cost) }} {{ currency }}</div>
    	{% endif %}

	  	{% if discount_and_bonuses_redeemed %}
			<div style="margin-top:5px;font-size:12px;">{{ localised_labels.bonuses_and_discounts_label }}: {{ "%.2f"|format(discount_and_bonuses_redeemed) }} {{ currency }}</div>
    	{% elif bonuses_redeemed %}
			<div style="margin-top:5px;font-size:12px;">{{ localised_labels.bonuses_label }}: {{ "%.2f"|format(bonuses_redeemed) }} {{ currency }}</div>
    	{% endif %}

	  	{% if tips_sum %}
			<div style="margin-top:5px;font-size:12px;">{{ localised_labels.tips_label }}: {{ "%.2f"|format(tips_sum) }} {{ currency }}</div>
    	{% endif %}

	  	{% if payer_fee %}
			<div style="margin-top:5px;font-size:12px;">{{ localised_labels.fees_label }}: {{ "%.2f"|format(payer_fee) }} {{ currency }}</div>
    	{% endif %}

		<div class="sum_for_pay">{{ localised_labels.total_for_payment_label }}: {{ "%.2f"|format(total_sum_with_extra_fee) }} {{ currency }}</div>

	  	{% if show_payed_sum %}
			<div class="total">{{ localised_labels.payed_sum_label }}: {{ "%.2f"|format(paid_sum) }} {{ currency }}</div>
		{% endif %}

	  	{% if payment_method %}
			<div style="margin-top:5px;font-size:12px;">{{ localised_labels.payment_method_label }}: {{ payment_method }}</div>
		{% endif %}


      {% endif %}
    {% endif %}

  		<div class="footer">
			<div>
				<div>{{ localised_labels.footer_label_top }}</div>
				<div>{{ localised_labels.footer_label_bottom }}</div>
			</div>
			{%  if group_info.domain %}
				<p style="font-size: 10px">{{ group_info.domain }}</p>
			{% endif %}
		</div>
  </div>
</body>
</html>
