<div style="text-align: start; width: 768px; max-width: 100%">
	{% if data.type == "regular" %}
		<h3 style="margin: 0">{{ data.store_name_text }}</h3>
	{% endif %}

	{% if data.order_type == "regular" %}
		<p style="margin-top: .25rem">
			{% for text in [
                data.date_text,
                data.to_text,
                data.status_text,
                data.status_pay_text,
                data.email,
                data.phone,
                data.payment_method_text,
                data.payment_method_comment_text,
                data.delivery_method_text,
                data.delivery_method_pickup_point_text,
                data.delivery_method_address_text,
                data.delivery_method_comment_text,
                data.desired_delivery_date_text,
                data.desired_pickup_date_text,
                data.comment_text,
            ] %}
				{% if text %}
					{{ text|safe }}
					<br />
				{% endif %}
			{% endfor %}
		</p>
	{% endif %}

	<div>
		{% if data.type == "regular" %}
			<h3 style="margin-bottom: .5rem">{{ data.items_text }}</h3>
		{% endif %}

		{%- if data.topup_account -%}
			<div>{{ data.topup_account }}</div>
		{%- endif -%}

		{%- if data.topup_card -%}
			<div>{{ data.topup_card }}</div>
		{%- endif -%}

		<table style="width: 100%; border-spacing: 0">
			<tbody>
			{% for item in data.check_items %}
				<tr>
					<td style="
                            width: 80px;
                            height: auto;
                            overflow: hidden;
                            border-bottom: 1px solid #e0e0e0;
                        ">
						{% if item.thumbnail_url %}
							<img
								style="width: 80px; height: auto; border-radius: 0.2rem"
								alt="product-image"
								src="{{ item.thumbnail_url }}"
							/>
						{% endif %}
					</td>
					<td style="
                            padding-left: 10px;
                            vertical-align: top;
                            border-bottom: 1px solid #e0e0e0;
                        ">
						<table style="width: 100%; height: 100%; border-spacing: 0">
							<tbody>
							<tr>
								<td>
									{% if data.type == "regular" %}
										{% if item.code %}
											[{{ item.code }}]
										{% endif %}
									{% endif %}
									{% if item.url %}
										<a
											style="margin-left: 4px; color: {{ color_schema.primary_color }}"
											href="{{ item.url }}"
										>
											{{ item.name|safe }}
											{% if item.modifiers_str %}
												{{ ' ' + item.modifiers_str|safe }}
											{% endif %}
										</a>
									{% else %}
										<span>
                                                    {{ item.name|safe }}
											{% if item.modifiers_str %}
												{{ ' ' + item.modifiers_str|safe }}
											{% endif %}
                                                </span>
									{% endif %}
									<span style="margin-left: 4px">
                                                {% if item.price != '0,00' %}
													{{ item.price }} x {{ item.quantity }}
												{% else %}
													x {{ item.quantity }}
												{% endif %}
                                            </span>
								</td>
								{% if item.price != '0,00' %}
									<td style="font-weight: bold; text-align: end">
										{{ item.sum }}
									</td>
								{% endif %}
							</tr>
							{% if item.attributes %}
								{% for attribute in item.attributes %}
									<tr>
										<td>
											<span>+</span>
											{% if attribute.code %}
												<span style="margin-left: .25rem">
                                                            [{{ attribute.code }}]
                                                        </span>
											{% endif %}
											<span style="margin-left: 4px">
                                                        {{ attribute.name|safe }}
                                                    </span>
											<span style="margin-left: 4px">
                                                        {{ attribute.price }} x {{ attribute.quantity }}
                                                    </span>
										</td>
										<td style="font-weight: bold; text-align: end">
											{{ attribute.sum }}
										</td>
									</tr>
								{% endfor %}
							{% endif %}
							{% if item.discount_sum %}
								<tr style="color: {{ color_schema.warning_color }}">
									<td>
										{{ data.item_sale_text }}
									</td>
									<td style="font-weight: bold; text-align: end">
										-{{ item.discount_sum }}
									</td>
								</tr>
							{% endif %}
							</tbody>
						</table>
					</td>
				</tr>
			{% endfor %}
			</tbody>
		</table>
		<table style="width: 100%; border-spacing: 0">
			<tbody>

			{% if data.order_type == 'regular' or data.order_type == 'topup' %}
				{% if data.topup_charge and data.topup_charge_text %}
					<tr style="font-weight: bold">
						<td>
							{{ data.topup_charge_text }}
						</td>
						<td style="
                                text-align: end;
                            ">
							{{ data.topup_charge }}
						</td>
					</tr>
				{% endif %}
				{% if data.subtotal_value > 0 %}
				<tr style="font-weight: bold">
					<td>
						{{ data.subtotal_text }}
					</td>
					<td style="font-weight: bold; text-align: end">
						{{ data.subtotal }}
					</td>
				</tr>
				{% endif %}

				{% if data.total_discount %}
					<tr style="color: {{ color_schema.warning_color }}">
						<td>
							{{ data.total_discount_text }}
						</td>
						<td style="font-weight: bold; text-align: end">
							-{{ data.total_discount }}
						</td>
					</tr>
				{% endif %}

				{%- if data.payment_price -%}
					<tr>
						<td>
							{{ data.payment_costs_text }}
						</td>
						<td style="font-weight: bold; text-align: end">
							{{ data.payment_price }}
						</td>
					</tr>
				{%- endif -%}

				{% if data.delivery_price %}
					<tr>
						<td>
							{{ data.delivery_costs_text }}
							{% if data.is_delivery_payment_separately %}
								<span style="margin-left: 4px">
                                        {{ data.delivery_payment_separately_text }}
                                    </span>
							{% endif %}
						</td>
						<td style="font-weight: bold; text-align: end">
							{{ data.delivery_price }}
						</td>
					</tr>
				{% endif %}

				{% if data.total_amount_value > 0 %}
				<tr style="font-weight: bold">
					<td style="
                            border-bottom: 1px solid #e0e0e0;
                        ">
						{{ data.total_amount_text }}
					</td>
					<td style="
                            text-align: end;
                            border-bottom: 1px solid #e0e0e0;
                        ">
						{{ data.total_amount }}
					</td>
				</tr>
				{% endif %}

				{% if data.extra_fees %}
					{% for extra_fee in data.extra_fees %}
						<tr>
							<td>
								{{ extra_fee.name }}
							</td>
							<td style="font-weight: bold; text-align: end">
								{{ extra_fee.formated_amount }}
							</td>
						</tr>
					{% endfor %}
					<tr style="font-weight: bold">
						<td style="
                                border-bottom: 1px solid #e0e0e0;
                            ">
							{{ data.total_amount_text }}
						</td>
						<td style="
                                text-align: end;
                                border-bottom: 1px solid #e0e0e0;
                            ">
							{{ data.total_sum_with_extra_fee }}
						</td>
					</tr>


				{% endif %}

				{% if data.tips %}
					<tr>
						<td>
							{{ data.tips_text }}
						</td>
						<td style="font-weight: bold; text-align: end">
							{{ data.tips }}
						</td>
					</tr>
					<tr style="font-weight: bold">
						<td style="
                                border-bottom: 1px solid #e0e0e0;
                            ">
							{{ data.total_amount_with_tips_text }}
						</td>
						<td style="
                                text-align: end;
                                border-bottom: 1px solid #e0e0e0;
                            ">
							{{ data.total_amount_with_tips }}
						</td>
					</tr>
				{% endif %}

				{% if data.payer_fee %}
					<tr>
						<td>
							{{ data.payer_fee_text }}
						</td>
						<td style="font-weight: bold; text-align: end">
							{{ data.payer_fee }}
						</td>
					</tr>

				{% endif %}

				{% if (data.status_pay in ('payed', 'closed') and data.paid_sum_value > 0) or (data.status_pay not in ('payed', 'closed') and data.total_amount_with_tips_value > 0) %}
				<tr>
					<td>
						{% if data.status_pay == 'payed' %}
							{{ data.paid_text }}
						{% else %}
							{{ data.payment_due_text }}
						{% endif %}
					</td>
					<td style="font-weight: bold; text-align: end">
						{% if data.status_pay in ('payed', 'closed') %}
							{{ data.paid_sum }}
						{% else %}
							{{ data.total_amount_with_tips }}
						{% endif %}
					</td>
				</tr>
				{% endif %}
			{% endif %}

			{% if data.for_admin and data.pmt_info_html %}
				<tr style="margin-top: 2rem;">
					<td style="font-weight: bold">
						{{ data.pmt_info_header | safe }}:
					</td>
					<td>
						{{ data.pmt_info_html | safe }}
					</td>
				</tr>
			{% endif %}
			</tbody>
		</table>

		{% if not data.for_admin and data.payment_method_info_text and data.status_pay != 'payed' and data.status != 'closed' %}
			<br>
			<div
				style="background-color: #e5f6fd; color: #014361; border: 1px solid #2196f3; border-radius: 4px; padding: 6px; display: inline-flex; align-items: center;"
				role="alert">
				<div style="flex: 1;">
					{{ data.payment_method_info_text | safe }}
				</div>
			</div>
		{% endif %}

		{% if data.is_loyalty_awards %}
			{% if data.loyalty_customer_accounts %}
				<div>
					<h4 style="margin-bottom: .25rem">
						{% if data.status_pay == "payed" %}
							{{ data.loyalty_customer_accounts_text }}
						{% else %}
							{{ data.loyalty_customer_accounts_to_be_replenished_text }}
						{% endif %}
					</h4>
					{% for customer_account in data.loyalty_customer_accounts %}
						{{ customer_account.title }}: <b>{{ customer_account.amount }}</b>
						<br />
					{% endfor %}
				</div>
			{% endif %}

			<h4 style="margin-bottom: .25rem">
				{% if data.status_pay == "payed" %}
					{% if data.order_type == "regular" %}
						{{ data.loyalty_issued_per_order_text }}
					{% else %}
						{{ data.gifts_awarded_text }}: {{ data.loyalty_vouchers_count }}
					{% endif %}
				{% else %}
					{{ data.loyalty_to_be_issued_per_payment_text }}
				{% endif %}
			</h4>
			{% if data.loyalty_bonuses %}
				<div>
					{{ data.loyalty_bonuses_text }}
					{{ data.loyalty_bonuses }}
				</div>
			{% endif %}
			{% if data.loyalty_vouchers_count %}
				{% if data.type == "regular" %}
					<div>
						{{ data.loyalty_vouchers_text }}
						{{ data.loyalty_vouchers_count }}
					</div>
				{% endif %}
				{% if data.coupons %}
					{% for coupon in data.coupons %}
						<b>{{ coupon.title }}: </b>
						{{ coupon.coupon_link | safe }}
						<br>
					{% endfor %}
				{% endif %}
			{% endif %}
		{% endif %}
	</div>
	{% if data.topup_error %}
		<div style="color: {{ color_schema.error }}">
			{{ data.topup_error }}
		</div>
	{% endif %}
</div>
