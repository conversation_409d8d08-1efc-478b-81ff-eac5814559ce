{% extends "base.html" %}
{% block content %}
	<div>
		{% if data.invoice_image_url %}
			<div style="position: relative; overflow: hidden; width: 100%; max-width: 460px">
				<img
					style="border-radius: 4px; display: block; width: 100%; height: auto"
					src="{{ data.invoice_image_url }}"
					alt="{{ data.title }}"
				/>
			</div>

		{% endif %}

		<h2 style="margin-top: .25rem; margin-bottom: .25rem">
			{{ data.type_payed_message|safe }}
		</h2>

		<b>{{ data.for_header_text }}:</b> <u>{{ data.title }}</u>
		<br>

		<b>{{ data.invoice_created_text }}:</b> <u>{{ data.time_created }}</u>
		<br>

		<b>{{ data.invoice_payed_text }}:</b> <u>{{ data.time_payed }}</u>
		<br>
		<span style="white-space: pre-wrap">{{ data.user_comment_line|safe }}</span>
		<br>

		{% if data.loyalty_info_data and data.loyalty_info_data.invoice_payed_text %}
			<b>{{ data.sum_amount_header_text }}:</b> <u>{{ data.loyalty_info_data.sum_amount_text }}</u>
			<br>

			{{ data.loyalty_info_data.invoice_payed_text|nl2br|safe }}
			<br>

			<b>{{ data.loyalty_info_data.payed_order_text }}:</b>
			<u>{{ data.loyalty_info_data.sum_to_pay_text }}</u>

			{% if data.extra_fees %}
				<br /><br />
				{% for extra_fee in data.extra_fees %}
					{{ extra_fee.name }}: {{ extra_fee.formated_amount }}
					<br />
				{% endfor %}
			{% endif %}

			{% if data.payer_fee and data.paid_sum %}
				<br />
				<u>{{ data.payer_fee }}</u>
				<br />{{ data.paid_sum | safe }}
			{% endif %}
		{% else %}
			<b>{{ data.on_amount_text }}:</b> <u>{{ data.amount }}</u>

			{% if data.extra_fees %}
				<br /><br />
				{% for extra_fee in data.extra_fees %}
					{{ extra_fee.name }}: {{ extra_fee.formated_amount }}
					<br />
				{% endfor %}
			{% endif %}

			{% if data.payer_fee and data.paid_sum %}
				<br />
				<u>{{ data.payer_fee }}</u>
				<br />{{ data.paid_sum | safe }}
			{% endif %}
		{% endif %}
		<br><br>

		<b><u>{{ data.status_payment }}</u></b>
		<br>
		<br>

		{% if data.url_in_check_text %}
			{{ data.url_in_check_text | safe }}
			<br>
			<br>
		{% endif %}

		{% if data.friend_text %}
			{{ data.friend_text | safe }}
			<br>
			<br>
		{% endif %}

	</div>
	{% include 'loyalty_html.html' %}
{% endblock %}
