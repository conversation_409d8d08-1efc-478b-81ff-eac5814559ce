{% extends "base.html" %}
{% block content %}
	<div>

		<div style="margin: 0">

			{%- if data.photo -%}
				<div style='align-content: start; text-align: left'>
					<img src="{{ data.photo }}" class="responsive-image" />
				</div>
			{%- endif -%}

			<div style="
				line-height: 24px;
				font-size: 16px;
				font-weight: 350;"
			>
				{{ data.header | safe }}
			</div>
			<br>

			{%- if data.message -%}
				{{ data.message | safe }}
			{%- endif -%}

			{%- if data.link -%}

				<div style="margin-top: 1rem;">
					<a
						class="link_button"
						href="{{ data.link }}"
						role="button"
					>
						{{ data.link_text }}
					</a>
				</div>
			{%- endif -%}

			{% if data.coupons %}
				<div style="margin-top: 1rem;">
					{% for coupon in data.coupons %}
						<b>{{ coupon.title | safe }}</b>
						<br>
						{% if data.coupons|length < 2 %}
							{{ coupon.description | safe }}<br>
						{% endif %}
						{{ coupon.coupon_link | safe }}<br>
					{% endfor %}
				</div>
			{% endif %}

		</div>
		<br>

	</div>
{% endblock %}