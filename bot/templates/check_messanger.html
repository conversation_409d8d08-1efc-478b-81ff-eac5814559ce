{%- if data.order_type == "regular" and data.total_sum_with_extra_fee_value > 0 -%}
	<b>{{ data.total_amount_text }}: {{ data.total_sum_with_extra_fee }}</b>
{%- endif -%}
{%- set divider = "—" * 20 -%}
{#{%- if data.topup_account -%}#}
{#	{{ data.topup_account }}<br />#}
{#{%- endif -%}#}
{##}
{#{%- if data.topup_card -%}#}
{#	{{ data.topup_card }}<br />#}
{#{%- endif -%}#}
{#{%- for item in data.check_items -%}#}
{#	{%- if data.order_type == "regular" -%}#}
{#		[{{ item.code }}]{{ ' ' }}#}
{#	{%- endif -%}#}
{#	{%- if item.url -%}#}
{#		<a href="{{ item.url }}">#}
{#			{{ item.name|safe }}#}
{#			{%- if item.modifiers_str -%}#}
{#				{{ ' ' + item.modifiers_str|safe }}#}
{#			{%- endif -%}#}
{#		</a>#}
{#	{%- else -%}#}
{#		{{ item.name|safe }}#}
{#		{%- if item.modifiers_str -%}#}
{#			{{ ' ' + item.modifiers_str|safe }}#}
{#		{%- endif -%}#}
{#	{%- endif -%}#}
{##}
{#	{%- if item.price != '0,00' -%}#}
{#		{{ ' ' }}{{ item.price }} x {{ item.quantity }} = {{ item.sum }}#}
{#	{%- else -%}#}
{#		{{ ' ' }} x {{ item.quantity }}#}
{#	{%- endif -%}#}
{##}
{#	<br />#}
{#	{%- if item.attributes -%}#}
{#		{%- for attribute in item.attributes -%}#}
{#			{{ ' + ' }}{%- if attribute.code -%}[{{ attribute.code }}]{{ ' ' }}{%- endif -%}#}
{#			{{ attribute.name + ' '|safe }}#}
{#			{{ attribute.price }} x {{ attribute.quantity }} = {{ attribute.sum }}#}
{#			<br />#}
{#		{%- endfor -%}#}
{#	{%- endif -%}#}
{#	{%- if item.discount_sum -%}#}
{#		{{ " " + data.item_sale_text }} -{{ item.discount_sum }}#}
{#		<br />#}
{#	{%- endif -%}#}
{#	{{ divider }}#}
{#	<br />#}
{#{%- endfor -%}#}
{#{%- if data.order_type == "regular" -%}#}
{#	{{ divider }}#}
{#	<br/>#}
{#{%- endif -%}#}
{#{%- if data.order_type == 'regular' -%}#}
{#	<b>{{ data.subtotal_text }}: {{ data.subtotal }}</b>#}
{#	<br />#}
{#	{%- if data.total_discount -%}#}
{#		{{ data.total_discount_text }}: -{{ data.total_discount }}#}
{#		<br />#}
{#	{%- endif -%}#}
{#	{%- if data.payment_price -%}#}
{#		{{ data.payment_costs_text }}: {{ data.payment_price }}#}
{#	{%- endif -%}#}
{#	{%- if data.delivery_price -%}#}
{#		{{ data.delivery_costs_text }}#}
{#		{%- if data.is_delivery_payment_separately -%}#}
{#			{{ data.delivery_payment_separately_text }}#}
{#		{%- endif -%}#}
{#		: {{ data.delivery_price }}#}
{#		<br />#}
{#	{%- endif -%}#}
{##}
{#	<b>{{ data.total_amount_text }}: {{ data.total_amount }}</b>#}
{##}
{#{%- endif -%}#}
{##}
{##}
{#{% if data.extra_fees %}#}
{#	<br />#}
{#	{{ divider }}#}
{#	<br />#}
{#	{% for extra_fee in data.extra_fees %}#}
{#		{{ extra_fee.name }}: {{ extra_fee.formated_amount }}<br />#}
{#	{% endfor %}#}
{#	<b>{{ data.total_amount_text }}: {{ data.total_sum_with_extra_fee }}</b>#}
{##}
{#{% endif %}#}
{##}
{##}
{#{%- if data.tips -%}#}
{#	<br />#}
{#	{{ divider }}#}
{#	<br />#}
{#	{{ data.tips_text }}: {{ data.tips }}#}
{#	<br />#}
{#	<b>{{ data.total_amount_with_tips_text }}: {{ data.total_amount_with_tips }}</b>#}
{#{%- endif -%}#}
{##}
{#{%- if data.topup_charge_text and data.topup_charge -%}#}
{#	<br />#}
{#	{{ divider }}#}
{#	<br />#}
{#	{{ data.topup_charge_text }}: {{ data.topup_charge }}<br />#}
{#{%- endif -%}#}
{##}
{#{%- if data.payer_fee -%}#}
{#	<br />#}
{#	{{ divider }}#}
{#	<br />#}
{#	{{ data.payer_fee_text }}: {{ data.payer_fee }}#}
{#{%- endif -%}#}
{##}
{#{%- if data.order_type == "regular" or data.order_type == "topup" -%}#}
{#	<br />#}
{#	{{ divider }}#}
{#	<br />#}
{#	{%- if data.status_pay in ('payed', 'closed') -%}#}
{#		{{ data.paid_text }}: {{ data.paid_sum }}#}
{#	{%- else -%}#}
{#		{{ data.payment_due_text }}: {{ data.total_amount_with_tips }}#}
{#	{%- endif -%}#}
{#{%- endif -%}#}
{##}
{#{%- if data.friend_payment_text -%}#}
{#	<br />#}
{#	{{ divider }}#}
{#	<br />#}
{#	{{ data.friend_payment_text }}#}
{#{%- endif -%}#}
{##}
{#{%- if data.status_pay != 'payed' and data.status != 'closed' and data.payment_method_info_text -%}#}
{#	<br />#}
{#	{{ divider }}#}
{#	<br />#}
{#	{{ data.payment_method_info_text | safe }}#}
{#	{{ divider }}#}
{#{%- endif -%}#}

{%- if data.is_loyalty_awards -%}
	{%- if data.order_type == "regular" and data.total_sum_with_extra_fee_value > 0 -%}
		<br />
		<br />
	{%- elif data.order_type == "regular" -%}
		<br />
	{%- endif -%}
	{%- if data.loyalty_customer_accounts -%}
		<b>
			{%- if data.status_pay == "payed" -%}
				{{ data.loyalty_customer_accounts_text }}
			{%- else -%}
				{{ data.loyalty_customer_accounts_to_be_replenished_text }}
			{%- endif -%}
		</b>
		<br />
		{%- for customer_account in data.loyalty_customer_accounts -%}
			{{ customer_account.title }}: <b>{{ customer_account.amount }}</b><br />
		{%- endfor -%}
		<br />
	{%- endif -%}
	<b>
		{%- if data.status_pay == "payed" -%}
			{%- if data.order_type == "regular" -%}
				{{ data.loyalty_issued_per_order_text }}:
			{%- else -%}
				{{ data.gifts_awarded_text }}:
			{%- endif -%}
		{%- else -%}
			{{ data.loyalty_to_be_issued_per_payment_text }}
		{%- endif -%}
	</b>
	{%- if data.loyalty_bonuses -%}
		<br />{{ data.loyalty_bonuses_text }} {{ data.loyalty_bonuses }}
	{%- endif -%}
	{%- if data.loyalty_vouchers_count -%}
		{%- if data.order_type == "regular" -%}
			<br />{{ data.loyalty_vouchers_text }} {{ data.loyalty_vouchers_count }}
		{%- else -%}
			{{ data.loyalty_vouchers_count }}
		{%- endif -%}
	{%- endif -%}
{%- endif -%}

{%- if data.topup_error -%}
	<br />
	<br />
	<b>{{ data.topup_error }}</b>
{%- endif -%}
