{% extends "base.html" %}
{% block content %}
    <div>
        {% if not data.for_admin and data.message %}
            <p style="margin-top: 0">
                {{ data.message|nl2br|safe }}
            </p>
        {% endif %}
        {% include "check_email.html" %}

        {% if not data.for_admin and data.is_order %}
            <div style="margin-top: .75rem;">
                <a
                    style="
                        margin-top: .25rem;
                        margin-right: .25rem;
                        user-select: none;
                        text-transform: uppercase;
                        white-space: nowrap;
                        padding: 8px 22px;
                        background-color: {{ color_schema.primary_color }};
                        border-radius: 4px;
                        border: none;
                        color: {{ color_schema.primary_contrast_text }} !important;
                        text-align: center;
                        text-decoration: none;
                        display: inline-block;
                        box-shadow: 0 3px 1px -2px rgba(0,0,0,0.2),0 2px 2px 0 rgba(0,0,0,0.14),0 1px 5px 0 rgba(0,0,0,0.12);
                    "
                    href="{{data.pmt_button_url}}"
                    role="button"
                >
                    {{data.goto_order_text}}
                </a>

                {%if data.pmt_button_text%}
                    <a
                        style="
                            margin-top: .25rem;
                            margin-right: .25rem;
                            user-select: none;
                            text-transform: uppercase;
                            white-space: nowrap;
                            padding: 8px 22px;
                            background-color: {{ color_schema.primary_color }};
                            border-radius: 4px;
                            border: none;
                            color: {{ color_schema.primary_contrast_text }} !important;
                            text-align: center;
                            text-decoration: none;
                            display: inline-block;
                            box-shadow: 0 3px 1px -2px rgba(0,0,0,0.2),0 2px 2px 0 rgba(0,0,0,0.14),0 1px 5px 0 rgba(0,0,0,0.12);
                        "
                        href="{{data.pmt_button_url}}"
                        role="button"
                    >
                        {{data.pmt_button_text}}
                    </a>
                {%endif%}

                <a
                    style="
                        margin-top: .25rem;
                        user-select: none;
                        text-transform: uppercase;
                        white-space: nowrap;
                        padding: 8px 22px;
                        background-color: {{ color_schema.primary_color }};
                        border-radius: 4px;
                        border: none;
                        color: {{ color_schema.primary_contrast_text }} !important;
                        text-align: center;
                        text-decoration: none;
                        display: inline-block;
                        box-shadow: 0 3px 1px -2px rgba(0,0,0,0.2),0 2px 2px 0 rgba(0,0,0,0.14),0 1px 5px 0 rgba(0,0,0,0.12);
                    "
                    href="{{data.web_store_back_home_url}}"
                    role="button"
                >
                    {{data.web_store_back_home_link_text}}
                </a>

            </div>
        {% endif %}

        {% include "referral_proposal_web.html" %}
        {% include "review_proposal_web.html" %}

        {% if data.review_proposal_text and data.leave_review_link %}

        {% endif %}
    </div>
{% endblock %}
