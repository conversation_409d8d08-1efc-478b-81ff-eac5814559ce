import pytest

from db import <PERSON><PERSON><PERSON><PERSON>, crud, models
from .....test_config import TEST_STORE_ID
from schemas import WorkingDaySchema, WorkingSlotSchema


class TestCrudStore:
    @pytest.mark.asyncio
    async def test_create_working_time(self):
        with DBSession():
            res = await crud.delete_days_by_store(TEST_STORE_ID)
            assert res is True

            working_days = list()
            days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
            for day in days:

                slot_one = WorkingSlotSchema(start_time="09:00", end_time="14:00")
                slot_two = WorkingSlotSchema(start_time="15:00", end_time="20:00")

                working_day = WorkingDaySchema(day=day, is_weekend=False, slots=[slot_one, slot_two])
                working_days.append(working_day)

            working_days = await crud.create_working_times(TEST_STORE_ID, working_days)

            assert isinstance(working_days, list)
            assert len(working_days) == 7
            for day in working_days:
                assert isinstance(day, models.WorkingDay)

                slots = day.slots
                assert isinstance(slots, list)
                assert len(slots) == 2

            res = await crud.delete_days_by_store(TEST_STORE_ID)
            assert res is True

    @pytest.mark.asyncio
    async def test_update_working_time(self):
        with DBSession():
            res = await crud.delete_days_by_store(TEST_STORE_ID)
            assert res is True

            slot_one = WorkingSlotSchema(start_time="09:00", end_time="14:00")
            working_day = WorkingDaySchema(day="week", is_weekend=False, slots=[slot_one])
            working_days = await crud.create_working_times(TEST_STORE_ID, [working_day])
            assert isinstance(working_days, list)
            assert len(working_days) == 1
            working_day = working_days[0]
            assert isinstance(working_day, models.WorkingDay)
            assert working_day.day == "week"

            slot_one = WorkingSlotSchema(start_time="15:00", end_time="18:00")
            working_day = await crud.update_working_times(
                store_id=TEST_STORE_ID,
                working_day_id=working_day.id,
                slots=[slot_one],
                is_weekend=False,
            )
            assert isinstance(working_day, models.WorkingDay)
            assert working_day.day == "week"
            assert len(working_day.slots) == 2

            working_day = await crud.update_working_times(
                store_id=TEST_STORE_ID,
                working_day_id=working_day.id,
                slots=None,
                is_weekend=True,
            )
            assert isinstance(working_day, models.WorkingDay)
            assert working_day.day == "week"
            assert len(working_day.slots) == 2
            assert working_day.is_weekend is True

            res = await crud.delete_days_by_store(TEST_STORE_ID)
            assert res is True

    @pytest.mark.asyncio
    async def test_delete_working_time_slot(self):
        with DBSession():
            res = await crud.delete_days_by_store(TEST_STORE_ID)
            assert res is True

            slot_one = WorkingSlotSchema(start_time="10:00", end_time="13:00")
            working_day = WorkingDaySchema(day="monday", is_weekend=False, slots=[slot_one])
            working_days = await crud.create_working_times(TEST_STORE_ID, [working_day])
            assert isinstance(working_days, list)
            assert len(working_days) == 1
            working_day = working_days[0]
            assert isinstance(working_day, models.WorkingDay)
            assert working_day.day == "monday"

            slot = working_day.slots[0]

            await crud.delete_time_slot(slot.id)

            assert working_day.slots == []
