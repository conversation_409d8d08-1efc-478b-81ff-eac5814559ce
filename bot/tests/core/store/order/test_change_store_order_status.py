import asyncio

import pytest

from core.kafka.producer import producer
from core.store.order.service import change_store_order_status
from db import DBSession, models
from db.models import OrderShippingStatus
from schemas import OrderShippingStatusEnum


@pytest.mark.asyncio
async def test_change_store_order_status_in_transit():
    # Вкажіть ці значення вручну перед запуском тесту
    store_order_id = 2018  # <-- ваш ID замовлення
    # status = OrderShippingStatusEnum.IN_TRANSIT.value
    status = OrderShippingStatusEnum.CLOSED.value

    await producer.initialise()

    with DBSession():
        order = await models.StoreOrder.get(store_order_id)
        assert order is not None
        await order.update(
            _status=OrderShippingStatusEnum.IN_TRANSIT.value,
        )
        
        # Видаляємо всі записи зі статусом CLOSED для цього ордера
        from db import sess
        sess().query(OrderShippingStatus).filter(
            OrderShippingStatus.store_order_id == store_order_id,
            OrderShippingStatus.status == OrderShippingStatusEnum.CLOSED.value
        ).delete()
        sess().commit()
        # Додаємо статус IN_TRANSIT для ордера
        shipping_status = OrderShippingStatus(
            store_order_id=store_order_id,
            status=OrderShippingStatusEnum.IN_TRANSIT.value,
        )
        sess().add(shipping_status)
        sess().commit()
        
        # Змінюємо статус на in_transit
        await change_store_order_status(
            store_order_id,
            status=status,
            initiated_by="external",
        )
        await asyncio.sleep(7)  # Затримка для відправки відкладеного меню
        # Оновлюємо order з бази
        order = await models.StoreOrder.get(store_order_id)
        assert order.status == status

if __name__ == "__main__":
    asyncio.run(test_change_store_order_status_in_transit())
    print("Test passed!") 