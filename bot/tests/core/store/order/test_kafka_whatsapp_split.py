import pytest
from db import DBSession
from core.kafka.producer import producer

@pytest.mark.asyncio
def make_big_text(length=1000):
    return "Тестове велике повідомлення " + ("A" * (length - 25))

@pytest.mark.asyncio
async def test_put_message_to_kafka_big_text():
    # Вкажіть ці значення вручну перед запуском тесту
    user_id = 167   # <-- ваш ID юзера (WhatsApp)
    bot_id = 6    # <-- ваш ID бота (WhatsApp)

    from db.models import User, ClientBot
    from core.notifications.funcs import put_message_to_kafka
    from core.whatsapp.keyboards import get_wa_menu_keyboard

    await producer.initialise()
    with DBSession():

        user = await User.get_by_id(user_id)
        bot = await ClientBot.get(bot_id)

        text = make_big_text(10000)  # >4096 символів
        keyboard = await get_wa_menu_keyboard(user, bot, "uk")

        # Відправляємо велике повідомлення з меню
        await put_message_to_kafka(
            bot, user, text, "text", keyboard=keyboard
        )
        print("Big text sent to Kafka (перевірте доставку у WhatsApp)")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_put_message_to_kafka_big_text())
    print("Test passed!") 