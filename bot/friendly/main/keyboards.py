from core.custom_texts import ct
from db.models import Channel, ClientBot, MailingArgs, User
from friendly.draw.db_funcs import check_user_draws
from friendly.poll.db_funcs import check_user_polls
from utils.redefined_classes import InlineBtn, InlineKb, MenuBtn, MenuKb
from utils.text import f


async def get_menu_keyboard(user: User, lang: str, bot_id: int = None):
    bot = await ClientBot.get_current() if not bot_id else await ClientBot.get(bot_id)

    keyboard = MenuKb(resize_keyboard=True, one_time_keyboard=False)

    is_have_mailings = await MailingArgs.get_mailings_for_user(
        user.id,
        bot.token,
        operation="exists",
    )

    if is_have_mailings:
        keyboard.row(MenuBtn(await f("mailing info button", lang)))

    keyboard.row(MenuBtn(await f("admin button", lang)))
    if await user.is_friendly_super_admin(bot.id):
        keyboard.insert(MenuBtn(await f("super admin button", lang)))
    keyboard.insert(MenuBtn(await f("subscriptions button", lang)))

    if await check_user_draws(user.id):
        keyboard.insert(MenuBtn(await f("draws list button", lang)))
    else:
        keyboard.insert(MenuBtn(await f("create draw button", lang)))

    if await check_user_polls(user.id):
        keyboard.insert(MenuBtn(await f("polls list button", lang)))
    else:
        keyboard.insert(MenuBtn(await f("create poll button", lang)))

    share_bot_button = await ct(bot, lang, "main", "share bot button")
    if share_bot_button:
        keyboard.row(MenuBtn(share_bot_button))

    return keyboard


async def get_contact_admin_button(
        channel: Channel, bot_username: str, lang: str
) -> InlineBtn:
    group = channel.group
    button_text = await channel.get_button_text("contact_admin", lang)
    return InlineBtn(button_text, url=group.get_chat_link(bot_username))


async def get_system_message_keyboard(channel: Channel, bot_username: str) -> InlineKb:
    lang = channel.lang

    keyboard = InlineKb(row_width=1)

    button_text = await channel.get_button_text("info", lang)
    keyboard.insert(InlineBtn(button_text, url=channel.get_info_link(bot_username)))

    keyboard.insert(await get_contact_admin_button(channel, bot_username, lang))
    return keyboard


async def get_return_to_chat_keyboard(channel_username: str, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)
    link = f"https://t.me/{channel_username}"
    keyboard.insert(InlineBtn(await f("return to chat button", lang), url=link))
    return keyboard
