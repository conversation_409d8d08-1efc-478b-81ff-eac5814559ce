from aiogram import Dispatcher, types
from aiogram.utils.exceptions import BadRequest

from db.models import Channel
from utils.platform_admins import send_message_to_platform_admins


async def no_rights_to_send_message_error(update: types.Update, exception: BadRequest):
    chat = types.Chat.get_current()

    chat_name = chat.title if chat else "Unknown"

    await send_message_to_platform_admins(f"handled no rights error in chat {chat_name}")

    if not chat:
        return

    await chat.leave()

    channel = await Channel.get(chat.id)
    if channel:
        await channel.set_bot_is_not_admin()

    return True


def register_main_errors_handlers(dp: Dispatcher):
    dp.register_errors_handler(
        no_rights_to_send_message_error,
        exception=BadRequest,
        exception_text="Have no rights to send a message"
    )
