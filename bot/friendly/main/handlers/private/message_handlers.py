from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.types import ContentTypes

from core.chat import user_to_chat
from core.chat.functions import message_from_user_to_group_handler
from db.models import ClientBot, User
from schemas import ChatType<PERSON><PERSON>
from utils.text import f
from ...keyboards import get_menu_keyboard


async def unhandled_cancel_button_handler(
        message: types.Message, state: FSMContext, user: User, lang: str
):
    await state.finish()
    keyboard = await get_menu_keyboard(user, lang)
    await message.answer(await f("action cancel text", lang), reply_markup=keyboard)


async def unhandled_messages_handler(message: types.Message, bot: ClientBot, **kwargs):
    chat = await user_to_chat(
        ChatTypeEnum.USER_WITH_GROUP, message,
        group_id=bot.group_id,
    )
    await message_from_user_to_group_handler(message, bot=bot, chat=chat, **kwargs)


def register_unhandled_messages_handlers(dp: Dispatcher):
    dp.register_message_handler(
        unhandled_cancel_button_handler,
        cancel_button=True,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state="*"
    )
    dp.register_message_handler(
        unhandled_messages_handler,
        chat_type="private",
        content_types=ContentTypes.ANY,
        state="*"
    )
