from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from core.helpers import get_crm_chat_link
from db import crud
from db.models import ChatMember, Channel, ClientBot, Group, User
from schemas import ChatTypeEnum

from utils.text import f
from utils.router import Router
from utils.message import send_tg_message

from friendly.main.keyboards import get_menu_keyboard

from friendly.draw.handlers import update_conditions_info_button as update_conditions_info_button_
from friendly.draw.handlers import get_followed_link_message_button as get_followed_link_message_button_


async def write_to_user_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict, lang: str,
        bot: ClientBot
):
    member = await ChatMember.get(callback_data.get("member_id"))
    channel = await Channel.get(member.channel_id)
    group = await Group.get(channel.group_id)

    chat = await crud.get_or_create_chat(ChatTypeEnum.USER_WITH_GROUP, member.user_id, group.id, bot.id)

    link = get_crm_chat_link(chat.id)
    await callback_query.message.answer(await f("message to user link", lang, link=link))


async def cant_write_to_user_button_handler(callback_query: types.CallbackQuery, lang: str):
    await callback_query.answer(await f("cant message to member text", lang), show_alert=True, cache_time=0)


async def interval_in_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    interval_in = callback_data.get("interval_in")
    await state.update_data(interval_in=interval_in)
    await Router.state_menu(callback_query, state, mode="edit")


async def channel_info_button(callback_query: types.CallbackQuery, callback_data: dict, user: User, lang: str):
    channel = await Channel.get(callback_data.get("channel_id"))
    chat_member = await ChatMember.get(user.id, channel.id)

    allowed_messages, messages_for_friends, friends_required = await chat_member.get_limits_info_texts()

    first_name = callback_query.from_user.first_name
    full_name = callback_query.from_user.full_name
    username = callback_query.from_user.username

    message_data = await channel.get_message(
        "info",
        firstname=first_name,
        user_first_name=first_name,
        fullname=full_name,
        user_full_name=full_name,
        username=username,
        friends_added=await chat_member.get_invites_count(),
        friends_required=friends_required,
        messages_given=messages_for_friends,
        allowed_messages=allowed_messages,
        chat_name=channel.get_link()
    )

    keyboard = await get_menu_keyboard(user, lang)
    await send_tg_message(callback_query.message.chat.id, keyboard=keyboard, **message_data)


async def update_conditions_info_button(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        lang: str
):
    await update_conditions_info_button_(callback_query, callback_data, lang)


async def get_followed_link_message_button(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        lang: str
):
    await get_followed_link_message_button_(callback_query, callback_data, lang)


def register_main_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        write_to_user_button_handler,
        callback_mode="write_to_user",
        chat_type="private",
        state="*",
    )

    dp.register_callback_query_handler(
        cant_write_to_user_button_handler,
        callback_mode="cant_write_to_user",
        chat_type="private",
        state="*",
    )

    dp.register_callback_query_handler(
        interval_in_button_handler,
        callback_mode="interval_in",
        chat_type="private",
        state="*",
    )

    dp.register_callback_query_handler(
        channel_info_button,
        callback_mode="channel_info",
        chat_type="private",
        state="*",
    )

    dp.register_callback_query_handler(
        update_conditions_info_button,
        callback_mode="update_conditions_info",
        chat_type="private",
        state="*",
    )

    dp.register_callback_query_handler(
        get_followed_link_message_button,
        callback_mode="get_followed_link_message",
        chat_type="private",
        state="*",
    )
