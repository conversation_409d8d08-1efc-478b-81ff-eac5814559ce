import logging
from typing import Callable, List

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from core.chat.virtual_manager.functions import start_virtual_manager_chat
from db.models import ClientBot, User
from friendly.draw.functions import cmd_start_draw
from friendly.helpers import set_friendly_bot_default_commands
from friendly.main.keyboards import get_menu_keyboard
from utils.text import f


async def cmd_start(
        message: types.Message, state: FSMContext, user: User, bot: ClientBot, lang: str
):
    await state.finish()

    if "draw" in message.text:
        return await cmd_start_draw(message, user, lang)

    text = await f("friendly welcome message", lang)
    keyboard = await get_menu_keyboard(user, lang)
    await message.answer(text, reply_markup=keyboard)

    if bot.on_join_virtual_manager_id:
        await start_virtual_manager_chat(
            user, bot.on_join_virtual_manager_id,
            bot.group_id,
        )


async def cmd_cancel(message: types.Message, state: FSMContext, user: User, lang: str):
    await state.finish()
    keyboard = await get_menu_keyboard(user, lang)
    await message.answer(await f("action cancel text", lang), reply_markup=keyboard)


async def cmd_update_commands(message: types.Message, lang: str):
    bot = message.bot
    client_bot = await ClientBot.get_current()
    with bot.with_token(client_bot.token):
        try:
            await set_friendly_bot_default_commands(bot)
        except Exception as e:
            logging.error(e, exc_info=True)

    await message.answer(await f("friendly commands updated text", lang))


def register_main_commands_handlers(dp: Dispatcher):
    def register_command(handler: Callable, command: List[str] | str):
        commands = command if isinstance(command, list) else [command]
        dp.register_message_handler(
            handler, commands=commands, state="*", chat_type="private"
        )

    register_command(cmd_start, "start")
    register_command(cmd_cancel, "cancel")
    register_command(cmd_update_commands, "update_commands")
