import copy

import aiogram.utils.exceptions
from aiogram import types

import config as cfg
from db.models import Footer
from loggers import <PERSON><PERSON><PERSON>ogger
from utils.message import send_tg_message
from utils.redefined_classes import InlineKb


async def make_data_for_send(
        message_data: dict, footers: list[Footer] | Footer,
        need_concatenation: bool = None
):
    if not isinstance(footers, list):
        footers = [footers]
    footers_data = [footer.kwargs_for_send for footer in footers]
    footers_data_copy = copy.deepcopy(footers_data)

    for footer, footer_data in zip(footers, footers_data_copy):
        if need_concatenation is None:
            need_concatenation = footer.need_concatenation
        else:
            await footer.update(need_concatenation=need_concatenation)

        if need_concatenation:
            footer_text = footer_data.get("text") if footer_data.get("text") else ""
            message_data_text = message_data["text"] if message_data["text"] else ""
            message_text = "\n".join([message_data_text, footer_text])
            if (footer.content_type == "text" and len(
                    message_text
            ) >= cfg.FRIENDLY_LIMIT_LENGHT_MESSAGE_TEXT) or \
                    (footer.content_type != "text" and len(
                        message_text
                    ) >= cfg.FRIENDLY_LIMIT_LENGHT_MESSAGE_CAPTION):
                need_concatenation = False
            else:
                message_data["text"] = message_text

            if need_concatenation and footer_data["content_type"] != "text" and \
                    message_data["content_type"] == "text":
                content_type = footer_data["content_type"]
                message_data["content_type"] = content_type
                message_data[content_type] = footer_data[content_type]

        if need_concatenation:
            if message_data.get("keyboard") and footer_data.get("keyboard"):
                buttons = Footer._get_buttons(footer.buttons)
                for row in buttons:
                    message_data["keyboard"].add(*row)
            elif not message_data.get("keyboard") and footer_data.get("keyboard"):
                message_data["keyboard"] = footer_data["keyboard"]

            footers_data.remove(footer_data)

    result = [message_data, ]
    result.extend(footers_data)
    return result


async def send_message_and_footer(
        chat_id: int, message_data: dict, keyboard: InlineKb | None,
        footer: Footer | list[Footer] | None, return_all_messages: bool = False,
        **kwargs
) -> types.Message | list[types.Message] | None:
    message_data.update(keyboard=keyboard)
    messages_for_send = await make_data_for_send(message_data, footer) if footer else [
        message_data, ]

    results = list()
    for message_data in messages_for_send:
        try:
            result = await send_tg_message(chat_id, **message_data, **kwargs)
        except aiogram.utils.exceptions.BadRequest:
            if message_data.get("text"):
                message_data["content_type"] = "text"
            result = await send_tg_message(chat_id, **message_data, **kwargs)
        except:
            try:
                message_data = await make_data_for_send(
                    message_data, footer, need_concatenation=True
                )
                result = await send_tg_message(chat_id, **message_data[0], **kwargs)
            except Exception as e:
                JSONLogger("friendly.footer").error(
                    f"error sending message with footer", e
                )
                return None
        results.append(result)

    if return_all_messages:
        return results
    return results[0]
