from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Footer

from psutils.forms import FieldsListForm

from psutils.forms.fields import MessageField

from psutils.forms.helpers import save_messages_to_state

from utils.text import f

from friendly.functions import post_data_processor

from friendly.footer.edit.states import EditFooter


async def data_saver_content_type(data: dict, state: FSMContext):
    state_data = await state.get_data()
    footer_id = state_data.get("footer_id")
    footer = await Footer.get(footer_id)

    result = await footer.change(footer.need_concatenation, **data)
    if isinstance(result, str):
        await state.update_data(error=result)


async def post_save_content_type(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    error = state_data.get("error")
    if error:
        msg = await message.answer(await f(error, lang))
    else:
        msg = await message.answer(await f("footer changed successfully text", lang))
    await save_messages_to_state(state, msg)


class EditFooterForm(FieldsListForm):
    state_group = EditFooter

    need_setup_previous_button_handler = False
    back_to_previous_state_excluded_keys = ("channel_id", "footer_id",)

    change_footer = MessageField(
        *Footer.SUPPORTED_MESSAGE_TYPES,
        error_text_variable="invalid post message type error",
        data_processor=post_data_processor,
        data_saver=data_saver_content_type,
        post_save=post_save_content_type,
    )

    @classmethod
    async def data_saver(cls, *args, **kwargs):
        pass
