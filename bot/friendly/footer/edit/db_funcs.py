from typing import Literal

from sqlalchemy import or_, and_, func

from db import db_func, sess
from db.models import Footer


@db_func
def get_footers(
        channel_id: int,
        position: int = 0,
        limit: int = None,
        operation: Literal["all", "count"] = "all",
) -> list[Footer] | None:
    if operation == "count":
        query = sess().query(func.count(Footer.id))
    else:
        query = sess().query(Footer)

    query = query.filter(Footer.channel_id == channel_id)

    if operation == "count":
        return query.scalar() - position

    slice_args = [position, None]
    if limit:
        slice_args[1] = position + limit
    query = query.slice(*slice_args)

    return query.all()
