from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import Channel, Footer, Post, Schedule

from psutils.forms.helpers import save_messages_to_state

from utils.text import f

from utils.router import Router

from friendly.channel.edit.states import EditChannel
from friendly.footer.create.states import <PERSON><PERSON><PERSON>oot<PERSON>
from friendly.footer.edit.states import <PERSON><PERSON>ooter
from friendly.schedule.edit.states import EditSchedule


async def create_footer_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext
):
    await CreateFooter.CreateFooter.set()
    await Router.state_menu(callback_query, state)


async def delete_footer_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    await state.update_data(**callback_data)
    await EditFooter.DeleteFooter.set()
    await Router.state_menu(callback_query, state)


async def yes_delete_footer_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
        lang: str
):
    footer_id = callback_data.get("footer_id")
    footer = await Footer.get(footer_id)

    messages_types = await footer.channel.check_footer(footer_id)
    for message_type in messages_types:
        await footer.channel.set_footer(message_type, None)
    for schedule in footer.schedules:
        await schedule.set_footer(None)
    for post in footer.posts:
        await post.set_footer(None)

    result = await footer.delete()

    prefix = "" if result else "un"
    message_text = await f(f"footer delete {prefix}successfully text", lang)
    msg = await callback_query.answer(message_text)
    await save_messages_to_state(state, msg)

    await EditFooter.ChooseFooter.set()
    await Router.state_menu(callback_query, state)


async def no_delete_footer_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
):
    await EditFooter.ChooseFooter.set()
    await Router.state_menu(callback_query, state)


async def choose_footer_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    await state.update_data(**callback_data)
    state_data = await state.get_data()
    prev = state_data.get("prev")
    footer_id = state_data.get("footer_id")
    footer = await Footer.get(footer_id)
    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)

    if prev == "channel":
        await EditFooter.ChangeFooter.set()
    elif prev == "messages":
        message_type = state_data.get("message_type")
        await channel.set_footer(message_type, footer_id)
    elif prev == "schedule":
        schedule = await Schedule.get(state_data.get("schedule_id"))
        await schedule.set_footer(footer)
    elif prev == "post":
        post = await Post.get(state_data.get("post_id"))
        await post.set_footer(footer)

    await Router.state_menu(callback_query, state)


async def settings_footer_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    await state.update_data(**callback_data)
    await EditFooter.ChangeFooter.set()
    await Router.state_menu(callback_query, state)


async def previous_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    cur_state = await state.get_state()
    state_data = await state.get_data()
    prev = state_data.get("prev")

    if cur_state in (CreateFooter.CreateFooter.state, EditFooter.ChangeFooter.state,):
        await EditFooter.ChooseFooter.set()
    elif prev == "channel":
        await EditChannel.ChooseField.set()
    elif prev == "messages":
        await EditChannel.SetMessage.set()
    elif prev == "schedule":
        await EditSchedule.ChooseField.set()
    elif prev == "post":
        await EditSchedule.Posts.set()

    await Router.state_menu(callback_query, state)


def register_edit_footer_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        create_footer_button_handler,
        callback_mode="create_footer",
        chat_type="private",
        state=EditFooter.ChooseFooter,
    )

    dp.register_callback_query_handler(
        delete_footer_button_handler,
        callback_mode="delete_footer",
        chat_type="private",
        state=EditFooter.ChangeFooter,
    )

    dp.register_callback_query_handler(
        yes_delete_footer_button_handler,
        callback_mode="confirm_delete",
        callback_keys=dict(answer=True),
        state=EditFooter.DeleteFooter,
    )

    dp.register_callback_query_handler(
        no_delete_footer_button_handler,
        callback_mode="confirm_delete",
        callback_keys=dict(answer=False),
        state=EditFooter.DeleteFooter,
    )

    dp.register_callback_query_handler(
        choose_footer_button_handler,
        callback_mode="choose_footer",
        chat_type="private",
        state=EditFooter.ChooseFooter,
    )

    dp.register_callback_query_handler(
        settings_footer_button_handler,
        callback_mode="footer_settings",
        chat_type="private",
        state=EditFooter.ChooseFooter,
    )

    dp.register_callback_query_handler(
        previous_button_handler,
        previous_button=True,
        chat_type="private",
        state=[EditFooter, CreateFooter],
    )
