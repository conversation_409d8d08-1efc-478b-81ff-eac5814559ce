from aiogram.dispatcher import FSMContext

from db.models import Footer

from utils.redefined_classes import InlineKb, InlineBtn

from utils.text import f, c

from utils.keyboards import active_button, get_navigation_keyboard


async def get_edit_footer_keyboard(footer: Footer, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=2)

    button_text = await f("footer concatenation button", lang)
    if footer.need_concatenation:
        button_text = await active_button(lang, button_text)

    callback_data = "concatenation"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("delete footer button", lang)
    callback_data = c("delete_footer", footer_id=footer.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return await get_navigation_keyboard(lang, keyboard)
