from typing import Dict, Any, List, Literal

from aiogram.dispatcher import FSMContext

from db.models import Channel, Footer, Post, Schedule, User

from utils.keyboards import previous_button, active_button

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn
from utils.router.route_handlers import BaseListDrawer

from friendly.footer.edit.db_funcs import get_footers


class FooterListDrawer(BaseListDrawer):

    row_width = 2

    config_page_size_variable_name = "FRIENDLY_FOOTER_LIST_PAGE_SIZE"

    need_check_selected = False
    need_previous_button = True
    need_setup_pagination_handler = True
    need_setup_search_handler = False

    message_text_variable = "pick footer"
    empty_text_variable = "footer list empty"

    pagination_callback_mode = "friendly_footer_pagination"

    @classmethod
    async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
        state_data = await state.get_data()
        return {
            "channel_id": state_data.get("channel_id"),
            "prev": state_data.get("prev"),
            "message_type": state_data.get("message_type"),
            "schedule_id": state_data.get("schedule_id"),
            "post_id": state_data.get("post_id"),
        }

    @classmethod
    async def make_get_objects_kwargs(
        cls,
        user: User,
        search_text: str,
        data_from_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        return data_from_state.copy()

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: Literal["all", "count"] = "all"
    ) -> List[Footer]:
        channel_id = get_objects_kwargs.get("channel_id")

        footers = await get_footers(channel_id, position=position, limit=limit, operation=operation)
        return footers

    @classmethod
    async def object_drawer(
        cls,
        footer: Footer,
        keyboard: InlineKb, lang: str,
        channel_id: int,
        **kwargs
    ):
        text = footer.text.strip()
        if not text:
            text = footer.buttons

        prev = kwargs.get("prev")
        if prev == "messages":
            message_type = kwargs.get("message_type")
            channel = await Channel.get(channel_id)
            if await channel.get_footer(message_type) == footer:
                text = await active_button(lang, text)
        elif prev == "schedule":
            schedule_id = kwargs.get("schedule_id")
            schedule = await Schedule.get(schedule_id)
            if schedule.footer == footer:
                text = await active_button(lang, text)
        elif prev == "post":
            post_id = kwargs.get("post_id")
            post = await Post.get(post_id)
            if post.footer == footer or post.schedule.footer == footer:
                text = await active_button(lang, text)

        callback_data = c("choose_footer", footer_id=footer.id)
        button = InlineBtn(text, callback_data=callback_data)
        keyboard.insert(button)

        button_text = await f("footer settings button", lang)
        callback_data = c("footer_settings", footer_id=footer.id)
        button = InlineBtn(button_text, callback_data=callback_data)
        if prev in ("schedule", "post",):
            keyboard.insert(button)

    @classmethod
    async def footer_drawer(cls, channel_id: int, keyboard: InlineKb, lang: str):
        button_text = await f("create footer button", lang)
        callback_data = c("create_footer", channel_id=channel_id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        keyboard.row(await previous_button(lang))
