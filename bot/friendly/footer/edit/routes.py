import html
from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Footer, SchedulesChannelsAssociation

from utils.keyboards import get_yes_or_no_keyboard

from utils.message import send_or_edit_message

from utils.router import Router

from utils.text import f

from friendly.footer.edit.keyboards import get_edit_footer_keyboard

from friendly.footer.edit.list_drawers import FooterListDrawer

from friendly.footer.edit.states import EditFooter


async def send_edit_footer_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    footer = await Footer.get(state_data.get("footer_id"))

    text = footer.full_text if footer.full_text else footer.text if footer.text else footer.buttons
    text = html.escape(text)
    message_text = await f("edit footer header", lang, current=text)
    keyboard = await get_edit_footer_keyboard(footer, lang)

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


async def send_delete_footer_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel_id = state_data.get("channel_id")
    footer_id = state_data.get("footer_id")
    footer = await Footer.get(footer_id)

    messages_types = await footer.channel.check_footer(footer_id)
    related_messages = "\n".join([await f(f"message type {message_type} text", lang) for message_type in messages_types])
    related_messages = await f("related messages with footer handler", lang, messages_types=related_messages) if related_messages else ""

    related_schedules = "\n".join([schedule.name for schedule in footer.schedules])
    related_schedules = await f("related schedules with footer handler", lang, schedules_names=related_schedules) if related_schedules else ""

    related_posts = list()
    for post in footer.posts:
        post_index = post.schedule.index_send_message if post.schedule.channel_id == channel_id else (await SchedulesChannelsAssociation.get(post.schedule_id, channel_id)).index_send_message
        related_posts.append(await f("post with footer text", lang, post_index=post_index, schedule_name=post.schedule.name))
    related_posts = "\n".join(related_posts)
    related_posts = await f("related posts with footer handler", lang, related_posts=related_posts) if related_posts else ""

    related_objects = await f(
        "related objects with footer handler", lang,
        related_messages=related_messages,
        related_schedules=related_schedules,
        related_posts=related_posts
    )
    message_text = await f("footer confirm delete text", lang, related_objects=related_objects)
    keyboard = await get_yes_or_no_keyboard(lang, callback_mode="confirm_delete", data={"footer_id": footer_id})

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


def register_edit_footer_routes(router: Router):
    router.add_route(EditFooter.ChooseFooter, FooterListDrawer())
    router.add_route(EditFooter.ChangeFooter, send_edit_footer_menu)
    router.add_route(EditFooter.DeleteFooter, send_delete_footer_menu)
