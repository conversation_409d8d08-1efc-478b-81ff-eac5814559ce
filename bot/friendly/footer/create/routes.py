from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Footer

from utils.message import send_or_edit_message
from utils.text import f
from utils.router import Router

from friendly.footer.create.keyboards import get_create_footer_keyboard

from friendly.footer.create.states import C<PERSON><PERSON>ooter


async def send_enter_footer_menu(message: types.Message, state: FSMContext, lang: str):
    message_text = await f("enter footer header", lang)
    keyboard = await get_create_footer_keyboard(state, lang)

    return await send_or_edit_message(message, message_text=message_text, keyboard=keyboard)


def register_create_footer_routes(router: Router):
    router.add_route(CreateFooter.CreateFooter, send_enter_footer_menu)
