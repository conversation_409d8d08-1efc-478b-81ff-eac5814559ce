from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Footer

from psutils.forms import WizardForm

from psutils.forms.fields import MessageField

from psutils.forms.helpers import save_messages_to_state

from utils.text import f

from utils.router import Router

from friendly.functions import post_data_processor

from friendly.footer.create.states import C<PERSON><PERSON>ooter

from friendly.footer.edit.states import <PERSON><PERSON>ooter


async def data_saver_content_type(data: dict, state: FSMContext):
    state_data = await state.get_data()
    need_concatenation = state_data.get("need_concatenation", False)
    channel_id = state_data.get("channel_id")

    footer = await Footer.create(channel_id, need_concatenation, **data)
    if isinstance(footer, str):
        await state.update_data(error=footer)


async def post_save_content_type(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    error = state_data.get("error")
    if error:
        msg = await message.answer(await f(error, lang))
    else:
        msg = await message.answer(await f("footer created successfully text", lang))
    await save_messages_to_state(state, msg)


class CreateFooterForm(WizardForm):
    state_group = CreateFooter

    need_setup_previous_button_handler = False
    back_to_previous_state_excluded_keys = ("channel_id", "message_type",)

    create_footer = MessageField(
        *Footer.SUPPORTED_MESSAGE_TYPES,
        error_text_variable="invalid post message type error",
        data_processor=post_data_processor,
        data_saver=data_saver_content_type,
        post_save=post_save_content_type,
    )

    @classmethod
    async def set_next_state(cls):
        await EditFooter.ChooseFooter.set()
