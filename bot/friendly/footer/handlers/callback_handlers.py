from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import Footer

from utils.router import Router

from friendly.footer.create.states import <PERSON><PERSON><PERSON>ooter

from friendly.footer.edit.states import EditFooter


async def footer_concatenation_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    state_data = await state.get_data()
    need_concatenation = state_data.get("need_concatenation", False)
    await state.update_data(need_concatenation=not need_concatenation)

    cur_state = await state.get_state()
    if cur_state == EditFooter.ChangeFooter.state:
        footer = await Footer.get(state_data.get("footer_id"))
        await footer.update(need_concatenation=not need_concatenation)

    await Router.state_menu(callback_query, state)


def register_footer_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        footer_concatenation_button_handler,
        callback_mode="concatenation",
        chat_type="private",
        state=[CreateFoot<PERSON>.CreateFooter, EditFooter.ChangeFooter],
    )
