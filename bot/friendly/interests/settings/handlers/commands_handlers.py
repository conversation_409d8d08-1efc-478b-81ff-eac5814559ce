import config as cfg

from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from utils.router import Router

from ..states import InterestSettingStates


async def cmd_cfg_frequency_for_user(message: types.Message, state: FSMContext):
    if message.chat.id not in (_[0] for _ in cfg.PLATFORM_ADMINS):
        return
    await InterestSettingStates.FrequencyForUser.set()
    await Router.state_menu(message, state)


async def cmd_cfg_lifetime_post(message: types.Message, state: FSMContext):
    if message.chat.id not in (_[0] for _ in cfg.PLATFORM_ADMINS):
        return
    await InterestSettingStates.LifetimePost.set()
    await Router.state_menu(message, state)


def register_interests_settings_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cmd_cfg_frequency_for_user,
        commands="cmd_cfg_frequency_for_user",
        chat_type="private",
        state="*",
    )

    dp.register_message_handler(
        cmd_cfg_lifetime_post,
        commands="cmd_cfg_lifetime_post",
        chat_type="private",
        state="*",
    )
