from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import User

from utils.text import f
from psutils.fsm import get_field_from_state

from ..functions import update_settings

from friendly.main.keyboards import get_menu_keyboard

from ..states import InterestSettingStates


async def cancel_button_handler(message: types.Message, state: FSMContext, user: User, lang: str):
    await state.finish()

    keyboard = await get_menu_keyboard(user, lang)
    await message.answer(await f("action cancel text", lang), reply_markup=keyboard)


async def field_handler(message: types.Message, state: FSMContext, user: User, lang: str):
    if message.content_type != "text":
        return await message.answer(await f("not text error", lang))

    if not message.text.isdecimal():
        return await message.answer(await f("wrong time value set", lang))

    field = await get_field_from_state(state)
    await update_settings(field, 60 * 60 * int(message.text))

    await state.finish()

    keyboard = await get_menu_keyboard(user, lang)
    await message.answer(await f("new value is saved in db", lang), reply_markup=keyboard)


def register_interests_settings_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cancel_button_handler,
        cancel_button=True,
        chat_type="private",
        content_types=types.ContentTypes.TEXT,
        state=InterestSettingStates,
    )

    dp.register_message_handler(
        field_handler,
        chat_type="private",
        content_types=types.ContentTypes.ANY,
        state=InterestSettingStates,
    )
