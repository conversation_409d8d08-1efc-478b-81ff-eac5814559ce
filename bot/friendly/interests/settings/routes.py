from aiogram import types

from utils.router import Router
from utils.text import f

from utils.keyboards import get_cancel_keyboard

from .states import InterestSettingStates


async def send_edit_frequency_for_user_menu(message: types.Message, lang: str):
    keyboard = await get_cancel_keyboard(lang)
    await message.answer(await f("enter time send posts for user", lang), reply_markup=keyboard)


async def send_edit_lifetime_post_menu(message: types.Message, lang: str):
    keyboard = await get_cancel_keyboard(lang)
    await message.answer(await f("enter lifetime post", lang), reply_markup=keyboard)


def register_interests_settings_routes(router: Router):
    router.add_route(InterestSettingStates.FrequencyForUser, send_edit_frequency_for_user_menu)
    router.add_route(InterestSettingStates.LifetimePost, send_edit_lifetime_post_menu)
