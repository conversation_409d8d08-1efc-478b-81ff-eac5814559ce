from config import FREQUENCY_OF_SENDING_INTEREST_POSTS, INTERESTS_ITERATION_DELAY
from db.models import InterestSetting, ClientBot


async def update_settings(field_name: str, value: int):
    bot_from_db_id = ClientBot.get_current_bot_id()
    settings = await InterestSetting.get(bot_from_db_id)
    await settings.update(field_name, value)


async def get_settings(bot_id: int) -> int:
    return await InterestSetting.get(bot_id)


def get_setting_frequency() -> int:
    return FREQUENCY_OF_SENDING_INTEREST_POSTS


def get_setting_iteration_delay() -> int:
    return INTERESTS_ITERATION_DELAY
