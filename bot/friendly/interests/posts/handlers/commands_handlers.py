from contextlib import suppress

from aiogram import Dispatcher, types
from aiogram.utils.exceptions import Unauthorized

from db.models import Admin, ClientBot, User

from utils.text import f

from ..functions import create_interest_post
from ...functions import get_user_statistic


async def cmd_interest(message: types.Message, reply: types.Message):
    lang = await User.get_lang(message.from_user.id)

    post = await create_interest_post(reply)

    with suppress(Unauthorized):
        bot = message.bot

        if not post:
            message_text = await f("creating interest post error", lang)
            return await bot.send_message(message.from_user.id, message_text)

        message_text = await f("interest post created", lang)
        await bot.send_message(message.from_user.id, message_text)


async def cmd_intereststat(message: types.Message, user: User):
    bot = await ClientBot.get_current()
    if bot.owner.id != user.id and not await Admin.get(user.id, bot.group_id):
        return await message.delete()

    data_split = message.text.split(" ")
    if len(data_split) == 2:
        statistic = await get_user_statistic(interest=data_split[1])
    else:
        statistic = await get_user_statistic()

    await message.answer(str(statistic))


def register_interests_posts_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cmd_interest,
        is_chat_admin=True,
        is_reply=True,
        chat_type=["supergroup", "group", "channel"],
        commands=["interest"],
        state="*",
    )

    dp.register_message_handler(
        cmd_intereststat,
        commands=["intereststat"],
        chat_type="private",
        state="*",
    )
