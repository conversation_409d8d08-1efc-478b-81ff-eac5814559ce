import asyncio
from typing import Dict

from db import own_session
from db.models import InterestPost, InterestSetting

from .functions import get_posts_for_users, send_post_for_user
from ..settings.functions import get_setting_frequency, get_setting_iteration_delay


@own_session
async def send_interests_posts_in_bot(bot_id: int, posts: Dict[int, int], iteration_delay: int):
    for user_chat_id, post_id in posts.items():
        post = await InterestPost.get(post_id)
        await send_post_for_user(bot_id, user_chat_id, post)
        await asyncio.sleep(iteration_delay)


@own_session
async def send_interests_posts():
    loop = asyncio.get_event_loop()

    bots_ids = await InterestSetting.get_bots()
    posts = dict.fromkeys(bots_ids)

    for bot_id in bots_ids:
        posts[bot_id] = await get_posts_for_users(bot_id)

    if not any([any(posts_for_users.values()) for posts_for_users in posts.values()]):
        timeout = get_setting_frequency()
        return loop.call_later(timeout, asyncio.create_task, send_interests_posts())

    iteration_delay = get_setting_iteration_delay()

    coros = [send_interests_posts_in_bot(bot_id, posts[bot_id], iteration_delay) for bot_id in bots_ids]

    await asyncio.gather(*coros, return_exceptions=True)

    loop.call_soon(asyncio.create_task, send_interests_posts())
