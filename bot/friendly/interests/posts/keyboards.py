from typing import List

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn

from utils.keyboards import active_button


async def get_interests_keyboard(post_id: int, interests: List[str], lang: str, *, active: str = None) -> InlineKb:
    keyboard = InlineKb()
    for i, interest in enumerate(interests):
        button_text = await f("interest button", lang, interest=interest)
        if interest == active:
            button_text = await active_button(lang, button_text)
        callback_data = c("interest", interest_post_id=post_id, name=interest)
        button = InlineBtn(button_text, callback_data=callback_data)
        keyboard.insert(button) if i % 2 else keyboard.row(button)

    if len(interests) > 1:
        button_text = await f("everything is interesting button", lang)
        if active == "__all__":
            button_text = await active_button(lang, button_text)
        callback_data = c("interest_all", interest_post_id=post_id)
        button = InlineBtn(button_text, callback_data=callback_data)
        keyboard.insert(button) if len(interests) % 2 else keyboard.row(button)

    button_text = await f("not show button", lang)
    if active == "__not_show__":
        button_text = await active_button(lang, button_text)
    callback_data = c("interest_not_show", interest_post_id=post_id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return keyboard
