from typing import Dict, List

from aiogram import types

from db.models import ClientBot, InterestPost, InterestStatistic, User
from utils.text import f
from utils.message import send_tg_message

from ..status import Status

from .keyboards import get_interests_keyboard


def get_interests_from_message(message: types.Message) -> List[str]:
    cmd, *interests = message.text.split(" ")
    return interests


async def create_interest_post(message: types.Message):
    _, *interests = message.text.split(" ")
    user = await User.get(message.from_user.id)
    post = await InterestPost.create(message.text, user, interests)
    return post


async def get_interest_post_text(post: InterestPost, lang: str) -> str:
    text = await f("interest post text", lang, post_text=post.text, author_name=post.owner.full_name)
    return text


async def get_posts_for_users(bot_id: int) -> Dict[int, int]:
    users_posts = await InterestPost.get_posts_for_users(bot_id)
    return dict(users_posts)


async def send_post_for_user(bot_id: int, user_chat_id: int, post: "InterestPost"):
    client_bot = await ClientBot.get(bot_id)
    lang = await User.get_lang(user_chat_id, bot_id)

    keyboard = await get_interests_keyboard(post.id, post.get_interests(), lang)
    text = await get_interest_post_text(post, lang)

    await send_tg_message(user_chat_id, content_type="text", bot_token=client_bot.token, keyboard=keyboard, text=text)


async def edit_post_keyboard(message: types.Message, statistic: InterestStatistic, lang: str):
    active = statistic.active
    active = active if type(active) is str else f"__{Status(active).name.lower()}__"
    interest_post = statistic.post
    keyboard = await get_interests_keyboard(interest_post.id, interest_post.get_interests(), lang, active=active)
    await message.edit_reply_markup(keyboard)
