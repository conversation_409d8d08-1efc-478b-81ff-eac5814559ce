from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional

from db.models import InterestPost, InterestStatistic, ClientBot, User

from .status import Status


@dataclass
class StatisticData:
    user: str
    status: str
    datetime: datetime
    interests: list = None


async def update_user_statistic(
        user_id: int,
        interest_post_id: int,
        interest: str = None,
        status: Status = Status.NEUTRAL
) -> InterestStatistic:
    bot_id = ClientBot.get_current_bot_id()

    statistic = await InterestStatistic.get_user_statistic(bot_id, user_id, interest_post_id)
    if not statistic:
        statistic = await InterestStatistic.create(bot_id, user_id, interest_post_id)

    await statistic.update(interest, int(status))
    return statistic


async def get_user_statistic(
        user_chat_id: int = None,
        interest: str = None,
        status: Status = None,
        date_begin: datetime = None,
        date_end: datetime = None
) -> List[StatisticData]:
    bot_from_db_id = ClientBot.get_current_bot_id()
    user_id = await User.get_id_by_chat_id(user_chat_id) if user_chat_id else None
    status = int(status) if status is not None else None

    statistics = await InterestStatistic.get_user_statistic(
        bot_from_db_id, user_id, interest=interest,
        status=status, date_begin=date_begin, date_end=date_end
    )

    results = []
    for statistic in statistics:
        data = StatisticData(
            statistic.user.name,
            Status(statistic.status).name,
            statistic.datetime
        )

        if statistic.status == Status.LIKE:
            data.interests = [statistic.interest]
        else:
            post = await InterestPost.get(statistic.post_id)
            data.interests = post.get_interests()

        results.append(data)
    return results
