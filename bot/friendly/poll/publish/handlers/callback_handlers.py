from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import PollAnswer, PollPost, User

from utils.text import f

from ..states import Publish<PERSON>oll


async def poll_vote_button_handler(callback_query: types.CallbackQuery, callback_data: dict, user: User, lang: str):

    poll_post_id = callback_data.get("pp_id")
    option_id = callback_data.get("opt_id")

    if await PollAnswer.check_user_vote(user.id, poll_post_id):
        text = await f("poll user second vote", lang)
        return await callback_query.answer(text, show_alert=True)

    vote = await PollAnswer.add_vote(user, option_id, poll_post_id)

    text = await f("poll user vote", lang, user_full_name=callback_query.from_user.full_name, option_text=vote.option.text)
    await callback_query.message.answer(text, reply=True)


async def anonymity_vote_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict,
        user: User, lang: str,
):
    await state.finish()

    poll_post = await PollPost.get(callback_data.get("pp_id"))
    option_id = callback_data.get("opt_id")

    if await PollAnswer.check_user_vote(user.id, poll_post.id):
        text = await f("poll user second vote", lang)
        return await callback_query.answer(text, show_alert=True, cache_time=0)

    vote = await PollAnswer.add_vote(user, option_id, poll_post.id, True)
    option_text = vote.option.text.get_chat_with_user_link

    await callback_query.answer(await f("poll anonymity user vote", lang, option_text=option_text), show_alert=True)
    text = await f("anonymity vote chat message", lang, option_text=option_text)
    await callback_query.bot.send_message(poll_post.chat_id, text, reply_to_message_id=poll_post.message_id)


def register_publish_poll_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        poll_vote_button_handler,
        callback_mode="poll_vote",
        chat_type=["group", "supergroup", "channel"],
        state="*",
    )

    dp.register_callback_query_handler(
        anonymity_vote_button_handler,
        callback_mode="anonymity_vote",
        chat_type="private",
        state=PublishPoll.AnonymityVote,
    )
