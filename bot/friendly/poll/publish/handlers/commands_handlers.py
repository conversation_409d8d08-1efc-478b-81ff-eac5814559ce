from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.router import Router

from ..states import Publish<PERSON>oll


async def cmd_publish_deep_link(message: types.Message, state: FSMContext, deep_link_data: dict):
    poll_id = deep_link_data.get("id")
    await state.update_data(poll_id=poll_id)
    await PublishPoll.Publish.set()
    await Router.state_menu(message, state)


async def cmd_vote_poll_deep_link(message: types.Message, state: FSMContext, deep_link_data: dict, lang: str):
    await PublishPoll.AnonymityVote.set()
    await state.update_data(poll_post_id=deep_link_data.get("ppid"))
    await Router.state_menu(message, state, lang)


async def cmd_see_poll_result_deep_link(message: types.Message, state: FSMContext, deep_link_data: dict, lang: str):
    await PublishPoll.SeeResults.set()
    await state.update_data(poll_post_id=deep_link_data.get("ppid"))
    await Router.state_menu(message, state, lang)


def register_publish_poll_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cmd_publish_deep_link,
        deep_link="publish",
        chat_type="group",
    )

    dp.register_message_handler(
        cmd_vote_poll_deep_link,
        deep_link="pv",
        chat_type="private",
    )

    dp.register_message_handler(
        cmd_see_poll_result_deep_link,
        deep_link="spr",
        chat_type="private",
    )
