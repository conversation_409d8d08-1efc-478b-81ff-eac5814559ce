from typing import List

from db.models import PollOption, PollPost

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn


async def get_poll_keyboard(poll_post: PollPost, options: List[PollOption], bot_username: str, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=3)

    for option in options:
        button_text = await f("option text", lang, option_text=option.text)
        callback_data = c("poll_vote", pp_id=poll_post.id, opt_id=option.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if poll_post.is_anonymous:
        url = f"t.me/{bot_username}?start=pv_ppid-{poll_post.id}"
        button_anonymity = InlineBtn(await f("anonymity vote", lang), url=url)
        keyboard.row(button_anonymity)

    url = f"t.me/{bot_username}?start=spr_ppid-{poll_post.id}"
    button_see_result = InlineBtn(await f("poll result button", lang), url=url)
    keyboard.row(button_see_result)
    return keyboard


async def get_anonymity_vote_keyboard(options: [PollOption], poll_post_id: int, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=3)

    for option in options:
        button_text = await f("option text", lang, option_text=option.text)
        callback_data = c("poll_a_vote", pp_id=poll_post_id, opt_id=option.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return keyboard
