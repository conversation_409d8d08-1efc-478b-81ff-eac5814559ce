import asyncio

from contextlib import suppress

from aiogram import types
from aiogram.dispatcher import FSMContext
from aiogram.utils.exceptions import MessageError

from config import SEND_POLL_TIMER
from db.models import ClientBot, Poll, PollAnswer, PollOption, PollPost, User

from utils.text import f
from utils.router import Router
from utils.message import send_tg_message

from .keyboards import get_poll_keyboard, get_anonymity_vote_keyboard
from friendly.main.keyboards import get_menu_keyboard

from .states import PublishPoll


async def publish_poll(message: types.Message, state: FSMContext, lang: str):
    poll_timer = SEND_POLL_TIMER

    state_date = await state.get_data()
    poll_id = state_date.get("poll_id")
    poll = await Poll.get(poll_id)

    is_media = False if poll.content_type == "text" else True

    poll_question = await f("poll question", lang, poll_question=poll.question)
    enter_field_text = await f("pool send timer", lang, send_timer=poll_timer)
    text = await f(
        "enter poll field text", lang,
        poll_question=poll_question,
        enter_field_text=enter_field_text,
    )

    poll_message = await send_tg_message(message.chat.id, poll.content_type, text=text,
                                         **{poll.content_type: poll.file_path})
    with suppress(MessageError):
        await message.delete()

    loop = asyncio.get_event_loop()
    loop.call_later(1, asyncio.create_task, send_timer(
        poll_message, state, lang,
        poll_id, poll_timer, is_media,
    ))


async def send_timer(
        message: types.Message,
        state: FSMContext,
        lang: str,
        poll_id: int,
        timer_position: int,
        is_media: bool
):
    timer_position -= 1

    poll = await Poll.get(poll_id)

    if timer_position == 0:
        await send_new_poll_menu(message, lang, poll_id, is_media)

        return await state.finish()

    poll_question = await f("poll question", lang, poll_question=poll.question)
    enter_field_text = await f("pool send timer", lang, send_timer=timer_position)
    text = await f(
        "enter poll field text", lang,
        poll_question=poll_question,
        enter_field_text=enter_field_text,
    )

    if is_media:
        message = await message.edit_caption(text)

    else:
        message = await message.edit_text(text)

    loop = asyncio.get_event_loop()
    loop.call_later(1, asyncio.create_task, send_timer(
        message,
        state,
        lang,
        poll_id,
        timer_position,
        is_media
    ))


async def send_new_poll_menu(message: types.Message, lang: str, poll_id: int, is_media: bool):
    poll = await Poll.get(poll_id)
    poll_post = await PollPost.create(poll_id, message.chat.id, message.message_id)

    options = await PollOption.get_list(poll_id, True, True)
    bot_username = await ClientBot.get_current_bot_username()

    keyboard = await get_poll_keyboard(poll_post, options, bot_username, lang)

    if is_media:
        return await message.edit_caption(poll.question, reply_markup=keyboard)

    await message.edit_text(poll.question, reply_markup=keyboard)


async def send_anonymity_vote_menu(message: types.Message, state: FSMContext, lang: str):
    state_date = await state.get_data()
    poll_post_id = state_date.get("poll_post_id")
    poll = await PollPost.get_poll_by_post_id(poll_post_id)

    options = await PollOption.get_list(poll.id, True, True)

    keyboard = await get_anonymity_vote_keyboard(options, poll_post_id, lang)

    await send_tg_message(message.chat.id, poll.content_type, keyboard=keyboard, text=poll.question,
                          **{poll.content_type: poll.file_path})


async def send_results_menu(message: types.Message, state: FSMContext, lang: str):
    user = await User.get(message.chat.id)

    state_date = await state.get_data()
    poll_post_id = state_date.get("poll_post_id")
    results = await PollAnswer.get_votes(poll_post_id)

    total_votes = 0
    for result in results.values():
        total_votes += result

    texts = []
    for result, value in results.items():
        percentage_value = round(value / total_votes, 2) * 100
        text = await f(
            "poll result text", lang,
            result=result, value=value,
            total_votes=total_votes,
            percentage_value=percentage_value,
        )
        texts.append(text)

    poll_results = ",\n".join(texts)

    message_text = await f("poll results message", lang, poll_results=poll_results)
    keyboard = await get_menu_keyboard(user, lang)
    await message.answer(message_text, reply_markup=keyboard)


def register_publish_poll_routes(router: Router):
    router.add_route(PublishPoll.Publish, publish_poll)
    router.add_route(PublishPoll.AnonymityVote, send_anonymity_vote_menu)
    router.add_route(PublishPoll.SeeResults, send_results_menu)
