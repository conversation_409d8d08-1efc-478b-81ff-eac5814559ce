import asyncio

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Poll

from utils.text import f
from psutils.fsm import get_field_from_state
from utils.router import Router
from utils.message import send_tg_message

from .list_drawers import PollsListDrawer

from .keyboards import get_edit_menu_keyboard, get_delete_poll_keyboard

from .states import EditPoll


EDIT_TEXT_STATES = [
    EditPoll.EditQuestion.state,
    EditPoll.EditOption.state,
    EditPoll.AddOption.state,
]


async def send_edit_menu(message: types.Message, state: FSMContext, lang: str):
    cur_state = await state.get_state()
    field = await get_field_from_state(cur_state)

    state_data = await state.get_data()
    option_index = state_data.get("option_index")

    poll = await Poll.get(state_data.get("poll_id"))

    question_text = await f("poll question", lang, poll_question=poll.question) if poll.question is not None else ""

    if cur_state in EDIT_TEXT_STATES:

        enter_field_text = await f(f"poll {field} text", lang)
        text = await f(
            "enter poll field text", lang,
            poll_question=question_text,
            enter_field_text=enter_field_text,
        )

    else:
        text = question_text

    keyboard = await get_edit_menu_keyboard(poll, option_index, lang, field)

    if message.content_type != poll.content_type:
        media = {poll.content_type: poll.file_path} if poll.content_type != "text" else dict()
        coro = send_tg_message(message.chat.id, poll.content_type, keyboard=keyboard, text=text, **media)

        return await asyncio.gather(coro, message.delete())

    edit_func = message.edit_text if message.content_type == "text" else message.edit_caption
    return await edit_func(text, reply_markup=keyboard)


async def send_delete_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    poll = await Poll.get(state_data.get("poll_id"))

    text = await f("delete poll header", lang, question=poll.question)
    keyboard = await get_delete_poll_keyboard(lang)

    if message.content_type == "text":
        return await message.edit_text(text, reply_markup=keyboard)

    await message.edit_caption(text, reply_markup=keyboard)


def register_edit_poll_routes(router: Router):
    router.add_route(EditPoll.List, PollsListDrawer())
    router.add_route(EditPoll.Delete, send_delete_menu)
    router.add_route(EditPoll, send_edit_menu)
