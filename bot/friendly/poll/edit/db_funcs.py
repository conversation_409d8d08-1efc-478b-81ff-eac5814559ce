from typing import List

from sqlalchemy import func

from db import db_func, sess
from db.models import Poll


@db_func
def get_polls(user_id: int, position: int = 0, limit: int = None, operation: str = "all") -> List["Poll"]:
    query = sess().query(func.count(Poll.id)) if operation == "count" else sess().query(Poll)
    query = query.filter(Poll.user_id == user_id, Poll.is_deleted.is_(False))

    if operation == "count":
        result = query.scalar()
        return result - position if result else 0

    query = query.order_by(Poll.time_created.desc())

    slice_args = [position, None]
    if limit:
        slice_args[1] = position + limit
    query = query.slice(*slice_args)

    polls = query.all()
    return polls
