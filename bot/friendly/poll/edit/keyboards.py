from db.models import Poll, PollOption

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn

from utils.keyboards import previous_button


async def get_edit_menu_keyboard(poll: Poll, option_index: int, lang: str, active_field: str) -> InlineKb:
    keyboard = InlineKb(row_width=3)

    question_button_text = await f("edit poll question", lang)
    question_action = "active" if active_field == "edit_question" else "editing"
    question_button_text = await f(f"{question_action} button", lang, handle=question_button_text)

    callback_data = "edit_question" if active_field != "edit_question" else "cancel_edit_question"
    button_question = InlineBtn(question_button_text, callback_data=callback_data)

    button_text = await f("anonymity on", lang) if poll.is_anonymous else await f("anonymity off", lang)
    button_anonymity = InlineBtn(button_text, callback_data="edit_anonymity")

    button_delete_poll = InlineBtn(await f("delete poll button", lang), callback_data="delete_poll")
    keyboard.row(button_question, button_anonymity, button_delete_poll)

    poll_options = await PollOption.get_list(poll.id, True, True)

    for option in poll_options:
        if option_index == option.index and active_field == "edit_option":
            option_action = "active"
            callback_data = "cancel_edit_option"
        else:
            option_action = "editing"
            callback_data = c("edit_option", option_index=option.index)

        option_button_text = await f("option text", lang, option_text=option.text)
        option_button_text = await f(f"{option_action} button", lang, handle=option_button_text)
        keyboard.insert(InlineBtn(option_button_text, callback_data=callback_data))

    add_option_button_text = await f("poll add option", lang)
    add_option_action = "active" if active_field == "add_option" else "ignore"
    add_option_button_text = await f(f"{add_option_action} button", lang, handle=add_option_button_text)

    callback_data = "add_option" if active_field != "add_option" else "cancel_add_option"
    button_add_option = InlineBtn(add_option_button_text, callback_data=callback_data)

    button_swap_left = InlineBtn(await f("button swap left", lang), callback_data=c("swap_option", side="left"))

    button_delete_option = InlineBtn(await f("delete option button", lang), callback_data="delete_option")

    button_swap_right = InlineBtn(await f("button swap right", lang), callback_data=c("swap_option", side="right"))

    keyboard.row(button_swap_left, button_delete_option, button_swap_right)
    keyboard.row(button_add_option, await previous_button(lang))

    return keyboard


async def get_delete_poll_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb()
    keyboard.insert(InlineBtn(await f("delete poll yes", lang), callback_data="confirm_delete_poll"))
    keyboard.insert(InlineBtn(await f("delete poll no", lang), callback_data="cancel_delete_poll"))
    return keyboard
