from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.router import Router

from ..states import EditPoll


async def polls_list_button_handler(message: types.Message, state: FSMContext, lang: str):
    await EditPoll.List.set()
    await Router.state_menu(message, state, lang)


def register_edit_poll_menu_handlers(dp: Dispatcher):
    dp.register_message_handler(
        polls_list_button_handler,
        lequal="polls list button",
        chat_type="private",
        state="*",
    )