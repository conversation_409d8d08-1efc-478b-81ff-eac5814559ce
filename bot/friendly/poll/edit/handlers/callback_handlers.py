from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import Poll, PollOption, User

from utils.router import Router
from utils.text import f

from ...db_funcs import check_user_polls

from friendly.main.keyboards import get_menu_keyboard

from ..states import <PERSON><PERSON><PERSON>


async def edit_poll_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    poll_id = callback_data.get("poll_id")
    await state.update_data(poll_id=poll_id)

    await EditPoll.Poll.set()
    await Router.state_menu(callback_query, state)


async def edit_question_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await EditPoll.EditQuestion.set()
    await Router.state_menu(callback_query, state, set_state_message=True)


async def cancel_edit_question_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await EditPoll.Poll.set()
    await Router.state_menu(callback_query, state, set_state_message=True)


async def edit_anonymity_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    state_date = await state.get_data()
    poll = await Poll.get(state_date.get("poll_id"))
    await poll.change_anonymity()
    await Router.state_menu(callback_query, state)


async def swap_option_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, lang: str,
):
    cur_state = await state.get_state()
    if cur_state != EditPoll.EditOption.state:
        return await callback_query.answer(await f("unexpected message error", lang), show_alert=True)

    side = callback_data.get("side")

    state_data = await state.get_data()
    poll_id = state_data.get("poll_id")
    option_index = int(state_data.get("option_index"))

    swapper = getattr(PollOption, f"swap_{side}")
    option_index = await swapper(poll_id, option_index)
    await state.update_data(option_index=option_index)

    await Router.state_menu(callback_query, state, lang)


async def delete_option_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    cur_state = await state.get_state()

    if cur_state != EditPoll.EditOption.state:
        await callback_query.answer(await f("unexpected message error", lang), show_alert=True, cache_time=0)

    async with state.proxy() as state_data:
        poll_id = state_data.get("poll_id")
        option_index = state_data.pop("option_index")

    await PollOption.delete_poll_option(poll_id, option_index)

    await callback_query.answer(await f("edit poll success", lang), cache_time=0)

    await EditPoll.Poll.set()
    await Router.state_menu(callback_query, state, lang)


async def delete_poll_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await EditPoll.Delete.set()
    await Router.state_menu(callback_query, state)


async def confirm_delete_poll_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, user: User, lang: str,
):
    state_data = await state.get_data()
    poll = await Poll.get(state_data.get("poll_id"))

    await poll.delete()

    text = await f("delete poll success", lang)
    is_polls_left = await check_user_polls(user.id)

    if is_polls_left:
        await callback_query.answer(text)
    else:
        keyboard = await get_menu_keyboard(user, lang)
        await callback_query.message.answer(text, reply_markup=keyboard)

    await EditPoll.List.set()
    await Router.state_menu(callback_query, state, lang)


async def cancel_delete_poll_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await callback_query.answer(await f("action cancel text", lang))
    await EditPoll.Poll.set()
    await Router.state_menu(callback_query, state, lang)


async def add_option_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await EditPoll.AddOption.set()
    await Router.state_menu(callback_query, state, state, set_state_message=True)


async def cancel_add_option_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await EditPoll.Poll.set()
    await Router.state_menu(callback_query, state)


async def edit_option_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    option_index = callback_data.get("option_index")
    await state.update_data(option_index=option_index)
    await EditPoll.EditOption.set()
    await Router.state_menu(callback_query, state, state, set_state_message=True)


async def cancel_edit_option_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    async with state.proxy() as state_data:
        if "option_index" in state_data:
            del state_data["option_index"]

    await EditPoll.Poll.set()
    await Router.state_menu(callback_query, state)


async def previous_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await EditPoll.List.set()
    await Router.state_menu(callback_query, state)


def register_edit_poll_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        edit_poll_button_handler,
        callback_mode="edit_poll",
        state=EditPoll.List,
    )

    dp.register_callback_query_handler(
        edit_question_button_handler,
        callback_mode="edit_question",
        state=EditPoll,
    )

    dp.register_callback_query_handler(
        cancel_edit_question_button_handler,
        callback_mode="cancel_edit_question",
        state=EditPoll.EditQuestion,
    )

    dp.register_callback_query_handler(
        edit_anonymity_button_handler,
        callback_mode="edit_anonymity",
        state=EditPoll,
    )

    dp.register_callback_query_handler(
        edit_question_button_handler,
        callback_mode="swap_option",
        state=EditPoll,
    )

    dp.register_callback_query_handler(
        delete_option_button_handler,
        callback_mode="delete_option",
        state=EditPoll,
    )

    dp.register_callback_query_handler(
        delete_poll_button_handler,
        callback_mode="delete_poll",
        state=EditPoll,
    )

    dp.register_callback_query_handler(
        confirm_delete_poll_button_handler,
        callback_mode="confirm_delete_poll",
        state=EditPoll.Delete,
    )

    dp.register_callback_query_handler(
        cancel_delete_poll_button_handler,
        callback_mode="cancel_delete_poll",
        state=EditPoll.Delete,
    )

    dp.register_callback_query_handler(
        add_option_button_handler,
        callback_mode="add_option",
        state=EditPoll,
    )

    dp.register_callback_query_handler(
        cancel_add_option_button_handler,
        callback_mode="cancel_add_option",
        state=EditPoll.AddOption,
    )

    dp.register_callback_query_handler(
        edit_option_button_handler,
        callback_mode="edit_option",
        state=EditPoll,
    )

    dp.register_callback_query_handler(
        cancel_edit_option_button_handler,
        callback_mode="cancel_edit_option",
        state=EditPoll.EditOption,
    )

    dp.register_callback_query_handler(
        previous_button_handler,
        previous_button=True,
        state=EditPoll,
    )
