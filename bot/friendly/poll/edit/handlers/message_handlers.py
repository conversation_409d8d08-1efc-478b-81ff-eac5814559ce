import asyncio

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from psutils.forms.helpers import process_after_save_input

from core.media import download_file
from db.models import Poll
from utils.media import scale_photo
from utils.router import Router
from utils.text import f
from ..states import Edit<PERSON>oll


async def edited_question_text_handler(message: types.Message, state: FSMContext):
    state_data = await state.get_data()
    poll = await Poll.get(state_data.get("poll_id"))
    await poll.update_question(message.content_type, message.text)
    await process_after_save_input(state, message.chat.id, message.message_id)


async def edited_question_media_handler(
        message: types.Message, state: FSMContext, lang: str
):
    state_data = await state.get_data()
    poll = await Poll.get(state_data.get("poll_id"))

    if message.content_type not in Poll.AVAILABLE_CONTENT_TYPES:
        return await message.answer(await f("unsupported system format", lang))

    file_path = await download_file(message)

    if message.content_type == "photo":
        await scale_photo(file_path)

    await poll.update_question(message.content_type, message.caption, file_path)
    await process_after_save_input(state, message.chat.id, message.message_id)


async def edited_option_handler(message: types.Message, state: FSMContext, lang: str):
    if message.content_type != "text":
        return await message.answer(await f("not text error", lang))

    state_data = await state.get_data()
    poll = await Poll.get(state_data.get("poll_id"))
    option_index = int(state_data.get("option_index"))

    option = await poll.get_option(option_index)
    await option.edit_text(message.text)

    coro = await Router.state_menu(state=state, lang=lang, get_state_message=True)
    await asyncio.gather(coro, message.delete())


async def new_option_handler(message: types.Message, state: FSMContext, lang: str):
    if message.content_type != "text":
        return await message.answer(await f("not text error", lang))

    state_data = await state.get_data()
    poll = await Poll.get(state_data.get("poll_id"))

    await poll.add_poll_option(message.text)
    coro = Router.state_menu(state=state, lang=lang, get_state_message=True)
    await asyncio.gather(coro, message.delete())


def register_edit_poll_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        edited_question_text_handler,
        content_types=types.ContentTypes.TEXT,
        state=EditPoll.EditQuestion,
    )

    dp.register_message_handler(
        edited_question_media_handler,
        content_types=types.ContentTypes.ANY,
        state=EditPoll.EditQuestion,
    )

    dp.register_message_handler(
        edited_option_handler,
        content_types=types.ContentTypes.ANY,
        state=EditPoll.EditOption,
    )

    dp.register_message_handler(
        new_option_handler,
        content_types=types.ContentTypes.ANY,
        state=EditPoll.AddOption,
    )
