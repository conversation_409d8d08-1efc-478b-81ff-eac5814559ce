import asyncio
from typing import Any, Dict, List

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import ClientBot, Poll, User

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn
from utils.router.route_handlers import BaseListDrawer

from .db_funcs import get_polls


class PollsListDrawer(BaseListDrawer):

    need_setup_pagination_handler = True

    need_setup_search_handler = True

    search_key = "poll_search"

    message_text_variable = "polls list header"

    config_page_size_variable_name = "POLLS_LIST_PAGE_SIZE"

    pagination_callback_mode = "polls_list_pagination"

    @classmethod
    async def handler(cls, message: types.Message, state: FSMContext, user: User, lang: str, mode: str = "new"):
        message_text, keyboard = await cls.draw_menu(message, state, lang, mode, user=user)

        if mode == "new":
            return await message.answer(message_text, reply_markup=keyboard)

        if message.content_type != "text":
            coro_1 = message.answer(message_text, reply_markup=keyboard)
            coro_2 = message.delete()
            return await asyncio.gather(coro_1, coro_2)

        return await message.edit_text(message_text, reply_markup=keyboard)

    @classmethod
    async def object_drawer(cls, poll: Poll, bot_username: str, keyboard: InlineKb, lang: str):
        button_text = await f("poll name", lang, poll_name=poll.name)
        keyboard.insert(InlineBtn(button_text, callback_data=c("edit_poll", poll_id=poll.id)))
        keyboard.insert(InlineBtn(await f("poll post", lang), url=f"t.me/{bot_username}?startgroup=publish_id-{poll.id}"))

    @classmethod
    async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
        bot_username = await ClientBot.get_current_bot_username()
        return dict(bot_username=bot_username)

    @classmethod
    async def make_get_objects_kwargs(
            cls,
            user: User,
            search_text: str,
            data_from_state: Dict[str, Any],
    ) -> Dict[str, Any]:
        return dict(user_id=user.id)

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: str = "all",
    ) -> List[Any] | int:
        user_id = get_objects_kwargs.get("user_id")
        return await get_polls(user_id, position, limit, operation)

    @classmethod
    async def footer_drawer(cls, keyboard: InlineKb, lang: str):
        keyboard.add(InlineBtn(await f("add poll", lang), callback_data="create_poll"))
