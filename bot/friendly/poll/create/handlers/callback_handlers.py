from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.router import Router

from ..states import C<PERSON><PERSON>oll


async def create_poll_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await CreatePoll.first()
    await callback_query.answer()
    await Router.state_menu(callback_query, state)


def register_create_poll_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        create_poll_button_handler,
        callback_mode="create_poll",
        chat_type="private",
        state="*",
    )
