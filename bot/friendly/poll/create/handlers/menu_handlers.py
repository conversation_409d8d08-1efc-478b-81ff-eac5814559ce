from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.router import Router

from ..states import Create<PERSON>oll


async def create_poll_button_handler(message: types.Message, state: FSMContext, lang: str):
    await CreatePoll.Name.set()
    await Router.state_menu(message, state, lang)


def register_create_poll_menu_handlers(dp: Dispatcher):
    dp.register_message_handler(
        create_poll_button_handler,
        lequal="create poll button",
        chat_type="private",
        content_types=types.ContentTypes.TEXT,
        state="*",
    )
