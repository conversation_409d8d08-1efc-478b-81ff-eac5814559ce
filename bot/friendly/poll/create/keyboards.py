from utils.text import f
from utils.redefined_classes import MenuKb, MenuBtn


async def get_skip_and_cancel_keyboard(lang: str) -> MenuKb:
    keyboard = MenuKb(resize_keyboard=True, row_width=2)

    skip_btn = MenuBtn(await f("skip button", lang))
    cancel_btn = MenuBtn(await f("action cancel button", lang))
    keyboard.row(skip_btn, cancel_btn)

    return keyboard


async def get_save_keyboard(lang: str) -> MenuKb:
    keyboard = MenuKb(resize_keyboard=True)
    keyboard.row(MenuBtn(await f("save button", lang)))
    return keyboard

