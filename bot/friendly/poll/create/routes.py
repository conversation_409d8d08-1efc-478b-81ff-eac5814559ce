from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import ClientBot, Poll, PollOption, User

from utils.text import f
from utils.router import Router

from utils.keyboards import get_cancel_keyboard
from friendly.main.keyboards import get_menu_keyboard
from .keyboards import get_save_keyboard, get_skip_and_cancel_keyboard

from .states import C<PERSON><PERSON><PERSON>


async def send_enter_name_menu(message: types.Message, lang: str):
    keyboard = await get_skip_and_cancel_keyboard(lang)
    await message.answer(await f("enter poll name", lang), reply_markup=keyboard)


async def send_enter_question_menu(message: types.Message, lang: str):
    keyboard = await get_cancel_keyboard(lang)
    await message.answer(await f("enter poll question", lang), reply_markup=keyboard)


async def send_enter_first_option_menu(message: types.Message, lang: str):
    keyboard = await get_cancel_keyboard(lang)
    await message.answer(await f("enter poll first option", lang), reply_markup=keyboard)


async def send_enter_else_options_menu(message: types.Message, lang: str):
    keyboard = await get_save_keyboard(lang)
    await message.answer(await f("enter poll else options", lang), reply_markup=keyboard)


async def create_poll(message: types.Message, state: FSMContext, lang: str):
    user = await User.get(message.chat.id)

    state_data = await state.get_data()
    await state.finish()

    bot_username = await ClientBot.get_current_bot_username()

    name = state_data.get("name")
    question = state_data.get("question")
    content_type = state_data.get("content_type", "text")
    file_path = state_data.get("file_path")

    poll = await Poll.save_poll(user, name, question, content_type, file_path)

    await PollOption.save_poll_options(state_data.get("options"), poll)
    text = await f("poll post message", lang, bot_username=bot_username, command=f"startgroup=post_{poll.id}")
    keyboard = await get_menu_keyboard(user, lang)

    await message.answer(text, reply_markup=keyboard)


def register_create_poll_routes(router: Router):
    router.add_route(CreatePoll.Name, send_enter_name_menu)
    router.add_route(CreatePoll.Question, send_enter_question_menu)
    router.add_route(CreatePoll.FirstOption, send_enter_first_option_menu)
    router.add_route(CreatePoll.ElseOptions, send_enter_else_options_menu)
    router.add_route(CreatePoll.Save, create_poll)
