from sqlalchemy import func

from db import db_func, sess
from db.models import Poll, PollAnswer, PollOption, PollPost


@db_func
def check_user_polls(user_id: int) -> bool:
    query = sess().query(Poll.id)
    query = query.filter(Poll.user_id == user_id)
    query = query.filter(Poll.is_deleted.is_(False))
    return sess().query(query.exists()).scalar()


@db_func
def get_votes(poll_post: PollPost) -> dict:
    answer_count_query = sess().query(func.count(PollAnswer.id))
    answer_count_query = answer_count_query.filter(PollAnswer.poll_post_id == poll_post.id)
    answer_count_query = answer_count_query.filter(PollAnswer.option_id == PollOption.id)
    answer_count_query = answer_count_query.label("votes_count")

    query = sess().query(PollOption.text, answer_count_query)
    query = query.filter(PollOption.poll_id == poll_post.poll_id)
    query = query.filter(PollOption.is_deleted.is_(False))
    query = query.order_by(PollOption.index.asc())
    
    return dict(query.all())
