import logging
import os
import traceback
from logging.handlers import RotatingFileHandler

from aiogram import types

from config import LOGS_FOLDER
from db.models import User
from utils.localisation import localisation
from psutils.logger import default_formatter

from utils.text import f, c
from utils.redefined_classes import Bo<PERSON>, InlineKb, InlineBtn, MenuKb
from utils.platform_admins import send_message_to_platform_admins

from .main.keyboards import get_menu_keyboard


async def are_user_and_bot_chat_admins(chat: types.Chat, user_chat_id: int, telegram_bot_id: int) -> bool:
    bot = Bot.get_current()

    user_info = await bot.get_chat_member(chat.id, user_chat_id)
    is_user_admin = user_info.user.id == chat.id or user_info.status in ("creator", "administrator")

    bot_info = await bot.get_chat_member(chat.id, telegram_bot_id)
    is_bot_admin = bot_info.status in ("creator", "administrator")

    return all([is_user_admin, is_bot_admin])


async def send_error(
        message_or_callback_query: types.Message | types.CallbackQuery = None,
        *,
        user_chat_id: int = None,
        user_full_name: str = None,
        keyboard: InlineKb | MenuKb | str = "without_keyboard"
):
    """
    one of message or (user_chat_id and user_full_name) must be specified

    :param message_or_callback_query: message or callback_query.
        if type(message_or_callback_query) is types.CallbackQuery: alert will be shown
        elif type(message_or_callback_query) is types.Message:
            user_chat_id and user_full_name will be gor from message_or_callback_query
    :type message_or_callback_query: types.Message | types.CallbackQuery

    :param user_chat_id: user telegram id
    :type user_chat_id: int

    :param user_full_name: user full name
    :type user_full_name: str

    :param keyboard: optional param. If not specified, get_menu_keyboard will be used.
        Specify "without_keyboard" for send without keyboard
    :type keyboard: InlineKb | MenuKb | None | str
    """

    if all([message_or_callback_query, user_chat_id, user_full_name]):
        raise ValueError("Only one message_or_callback_query or (user_chat_id, user_full_name) must be specified.")

    if not any([message_or_callback_query, user_chat_id, user_full_name]):
        raise ValueError("One of message_or_callback_query or (user_chat_id, user_full_name) must be specified.")

    message: types.Message | None
    callback_query: types.CallbackQuery | None

    if message_or_callback_query:
        if type(message_or_callback_query) is types.Message:
            message, callback_query = message_or_callback_query, None
            user_chat_id, user_full_name = message.chat.id, message.chat.full_name
        else:
            message, callback_query = None, message_or_callback_query
            user_chat_id, user_full_name = message.from_user.id, message.from_user.full_name
    else:
        callback_query = None

    user = await User.get(user_chat_id)
    lang = await user.get_lang()

    if callback_query:
        callback_query = message_or_callback_query
        return await callback_query.answer(await f("error", lang), show_alert=True, cache_time=0)

    bot = Bot.get_current()
    bot_user = await bot.me

    keyboard_for_platform_admins = InlineKb()
    button_text = await f("username button", "ru", username=user_full_name)
    button = InlineBtn(button_text, callback_data=c("send_user_link", chat_id=user_chat_id))
    keyboard_for_platform_admins.insert(button)

    error_text = "".join(traceback.format_exc())
    platform_admins_text = await f(
        "error message to platform admins", "ru",
        user_full_name=user_full_name,
        user_chat_id=user_chat_id,
        bot_username=bot_user.username,
        error_text=error_text
    )
    await send_message_to_platform_admins(platform_admins_text, keyboard_for_platform_admins)

    kwargs = {
        "text": await f("error", lang)
    }
    if keyboard is None:
        keyboard = await get_menu_keyboard(user, lang)
        kwargs["reply_markup"] = keyboard
    elif keyboard != "without_keyboard":
        kwargs["reply_markup"] = keyboard
    await bot.send_message(user_chat_id, **kwargs)


async def set_friendly_bot_default_commands(bot: Bot):
    for lang in await localisation.langs:
        await bot.set_my_commands(
            [
                types.BotCommand("setlimit", await f("cmd setlimit", lang)),
                types.BotCommand("setlimitdel", await f("cmd setlimit del", lang)),

                types.BotCommand("interest", await f("cmd interest", lang)),
                types.BotCommand("intereststat", await f("cmd intereststat", lang))
            ], scope=types.BotCommandScopeAllChatAdministrators(), language_code=lang
        )

        await bot.set_my_commands(
            [
                types.BotCommand("cmd_cfg_frequency_for_user", await f("cmd cfg frequency for user", lang)),
                types.BotCommand("cmd_cfg_lifetime_post", await f("cmd cfg lifetime post", lang)),
            ], language_code=lang
        )


def setup_scheduler_logger():
    logger = logging.getLogger("scheduler")
    logger.setLevel(logging.INFO)

    file_path = os.path.join(LOGS_FOLDER, "friendly", "scheduler.log")
    rh = RotatingFileHandler(file_path, maxBytes=20000, backupCount=1)

    rh.setFormatter(default_formatter)
    rh.setLevel(logging.DEBUG)

    logger.addHandler(rh)
