from aiogram import types

from core.media import download_file

import config as cfg


async def post_data_processor(message: types.Message, content_type: str) -> dict:
    post_data = dict(content_type=content_type)
    if content_type == "text":
        post_data.update(text=message.text)
    elif content_type == "sticker":
        post_data.update(media_path=message.sticker.file_id)
    else:
        file_path = await download_file(message)
        post_data.update(media_path=file_path)

        if content_type in cfg.MEDIA_WITH_CAPTION:
            post_data.update(text=message.caption)

    return post_data
