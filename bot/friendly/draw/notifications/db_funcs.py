from datetime import datetime

from sqlalchemy import or_, and_, func
from db import db_func, sess
from db.models import Draw, DrawResult


@db_func
def get_draw_users():
    utc_current_datetime = datetime.utcnow()

    query = sess().query(DrawResult)

    query = query.join(Draw, Draw.id == DrawResult.draw_id)

    query = query.filter(Draw.status == "active")
    query = query.filter(Draw.time_spending < utc_current_datetime)

    query = query.filter(Draw.count_notifications > 0)
    query = query.filter(Draw.count_notifications > DrawResult.count_notifications)

    query = query.filter(DrawResult.time_need_notification < utc_current_datetime)

    return query.all()
