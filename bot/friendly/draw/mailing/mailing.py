from typing import Dict, Any, List, Tuple

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import Draw, User, Group

from utils.keyboards import active_button

from core.mailing import BaseMailing
from core.mailing.base.base_create_mailing_form import create_form
from core.mailing.base.base_groups_list_drawer import BaseGroupsListDrawer

from utils.redefined_classes import InlineKb, InlineBtn

from utils.router import Router, CancelRoute

from utils.text import f, c

from friendly.draw.edit.db_funcs import get_draws
from friendly.draw.db_funcs import get_users

from .states import DrawMailingState
from friendly.draw.edit.states import EditDraw
from friendly.draw.statistics.states import DrawStatistics


class DrawMailing(BaseMailing):

    state_group = DrawMailingState

    @classmethod
    async def get_users(cls, mailing_creator: User, state: FSMContext) -> List[Tuple[int, int, str, str, str | None]]:
        state_data = await state.get_data()
        sender_draw_id = state_data.get("sender_draw_id")

        if sender_draw_id:
            users_data = await get_users(draw_id=sender_draw_id)
        else:
            users_data = await get_users(creator_id=mailing_creator.id)

        return users_data

    @classmethod
    async def make_get_groups_kwargs(cls, user: User, state_data: dict):
        return {
            "user_id": user.id,
            "search_text": state_data.get("search_text"),
        }

    @classmethod
    async def get_groups(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0,
            limit: int = None, *,
            operation: str = "all",
            only_names: bool = False,
    ) -> List[Group] | int:
        user_id = get_objects_kwargs.get("user_id")
        search_text = get_objects_kwargs.get("search_text")

        draws = await get_draws(user_id, search_text=search_text, status="active", position=position, limit=limit, operation=operation)

        if operation == "count":
            return draws

        if only_names:
            return [draw.name for draw in draws]

        return [draw.bot.group for draw in draws]

    @classmethod
    async def get_sender_groups(cls, user: User, state_data: dict) -> Group | list[Group]:
        kwargs = await cls.make_get_groups_kwargs(user, state_data)
        sender_groups = await cls.get_groups(kwargs)
        return sender_groups

    @classmethod
    async def get_draws(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0,
            limit: int = None, *,
            operation: str = "all",
            only_names: bool = False,
    ) -> List[Draw] | int:
        user_id = get_objects_kwargs.get("user_id")
        search_text = get_objects_kwargs.get("search_text")

        draws = await get_draws(user_id, search_text=search_text, status="active", position=position, limit=limit, operation=operation)

        if operation == "count":
            return draws

        if only_names:
            return [draw.name for draw in draws]

        return draws

    @classmethod
    async def get_bots_ids(
            cls,
            user: User, state_data: dict,
            users_and_bots_ids_and_mailing_mode = None
    ) -> list[int]:
        kwargs = await cls.make_get_groups_kwargs(user, state_data)
        sender_draws = await cls.get_draws(kwargs)
        return list(set([sender_draw.bot_id for sender_draw in sender_draws]))

    @classmethod
    async def back_to_previous_state(cls, message: types.Message, state: FSMContext, user: User, lang: str):
        state_data = await state.get_data()
        is_groups_skipped = state_data.get("is_groups_skipped", False)

        if is_groups_skipped:
            await DrawStatistics.ShowMembers.set()
        else:
            await EditDraw.ChooseDraw.set()
        await Router.state_menu(message, state, lang)

    @classmethod
    def setup_mailing(cls, dp: Dispatcher):

        router: Router = dp["router"]

        form = create_form(cls.state_group)

        form.setup_handlers(dp)

        make_get_objects_kwargs = cls.make_get_groups_kwargs
        get_groups = cls.get_groups
        get_draws = cls.get_draws

        class DrawsListDrawer(BaseGroupsListDrawer):
            message_text_variable = "mailing draws list header"
            search_message_text_variable = "mailing draws list search header"
            empty_text_variable = "mailing draws empty list header"
            search_empty_text_variable = "mailing draws empty list search header"

            state_group = cls.state_group

            @classmethod
            async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
                return await state.get_data()

            @classmethod
            async def make_get_objects_kwargs(
                    cls,
                    user: User,
                    search_text: str,
                    data_from_state: Dict[str, Any],
            ) -> Dict[str, Any]:
                return await make_get_objects_kwargs(user, data_from_state)

            @classmethod
            async def get_objects(
                    cls,
                    get_objects_kwargs: Dict[str, Any],
                    position: int = 0,
                    limit: int = None, *,
                    operation: str = "all",
            ) -> List[Any] | int:
                draws = await get_draws(get_objects_kwargs, position, limit, operation=operation)
                groups = await get_groups(get_objects_kwargs, position, limit, operation=operation)

                if operation == "count":
                    return draws

                return [(draw, group,) for draw, group in zip(draws, groups)]

            @classmethod
            async def object_drawer(cls, draw_group: Tuple[Draw, Group], keyboard: InlineKb, lang: str, sender_draw_id: int = None):
                draw, group = draw_group

                button_text = await f("mailing draw button", lang, draw_name=draw.name)
                if draw.id == sender_draw_id:
                    button_text = await active_button(lang, button_text)

                callback_data = c("group", id=group.id)
                keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

            @classmethod
            async def process_objects_list(
                    cls,
                    draws_groups: List[Any],
                    message: types.Message,
                    state: FSMContext,
                    lang: str
            ):
                draws = [draw_group[0] for draw_group in draws_groups]
                groups = [draw_group[1] for draw_group in draws_groups]
                bots = [draw.bot for draw in draws]

                if len(draws) == 1:
                    await state.update_data(sender_draw_id=draws[0].id, sender_group_id=groups[0].id, bot_id=bots[0].id, is_groups_skipped=True)
                    await cls.state_group.next()
                    await Router.state_menu(message, state, lang, set_state_message=True)
                    raise CancelRoute()

                return draws_groups

        dp.register_callback_query_handler(
            cls.previous_button_handler,
            previous_button=True,
            state=cls.state_group,
        )

        router.add_route(cls.state_group.ChooseGroup, DrawsListDrawer())
        router.add_route(cls.state_group.CreateMessage, cls.send_create_message_menu)
        router.add_route(cls.state_group.RunMailing, cls.run_mailing)
