from aiogram.dispatcher import FSMContext

from utils.keyboards import active_button

from utils.redefined_classes import InlineKb, InlineBtn
from utils.text import f, c

from db.models import Draw, DrawResult


async def get_conditions_update_info_keyboard(draw: Draw, draw_result: DrawResult, lang: str):
    keyboard = InlineKb()

    if draw.count_followed_link:
        button_text = await f("followed link button", lang)
        callback_data = c("get_followed_link_message", draw_id=draw.id, draw_result=draw_result.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("update conditions info button", lang)
    callback_data = c("update_conditions_info", draw_id=draw.id, draw_result=draw_result.id)
    keyboard.row(InlineBtn(button_text, callback_data=callback_data))

    return keyboard


async def get_count_keyboard() -> InlineKb:
    keyboard = InlineKb(row_width=3)

    for num in range(1, 7):
        callback_data = c("set_count", count=num)
        keyboard.insert(InlineBtn(str(num), callback_data=callback_data))

    return keyboard


async def get_follow_link_keyboard(state: FSMContext, lang: str) -> InlineKb:
    cur_state = await state.get_state()
    state_data = await state.get_data()

    keyboard = InlineKb(row_width=3)

    button_text = await f("follow link to draw button", lang)
    callback_data = "follow_link"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if cur_state.startswith("EditDraw"):
        draw = await Draw.get(state_data.get("draw_id"))
        if draw.follow_link:

            button_text = await f("delete follow link to draw button", lang)
            callback_data = "delete_follow_link"
            keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return keyboard


async def get_draw_results_type_keyboard(state: FSMContext, lang: str) -> InlineKb:
    cur_state = await state.get_state()
    state_data = await state.get_data()
    is_public = None
    if cur_state.startswith("EditDraw"):
        draw = await Draw.get(state_data.get("draw_id"))
        is_public = draw.is_public

    keyboard = InlineKb(row_width=2)

    button_text = await f("public results draw button", lang)
    if is_public is True:
        button_text = await active_button(lang, button_text)
    callback_data = c("draw_results_type", is_public=True)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("anonymous results draw button", lang)
    if is_public is False:
        button_text = await active_button(lang, button_text)
    callback_data = c("draw_results_type", is_public=False)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return keyboard


async def get_advanced_mode_keyboard(lang: str, keyboard: InlineKb = None) -> InlineKb:
    if not keyboard:
        keyboard = InlineKb(row_width=2)

    button_text = await f("draw award clear button", lang)
    callback_data = "clear_advanced_mode"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw simplified mode button", lang)
    callback_data = "advanced_mode"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return keyboard
