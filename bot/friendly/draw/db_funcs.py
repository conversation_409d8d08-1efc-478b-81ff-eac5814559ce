from typing import List, <PERSON><PERSON>

from db import db_func, sess
from db.models import Draw, DrawResult, ClientBot, Group, User

from core.mailing.base.db_funcs import get_mailing_mode_subquery


@db_func
def check_user_draws(user_id: int) -> bool:
    query = sess().query(Draw.id)
    query = query.filter(Draw.creator_id == user_id)
    return sess().query(query.exists()).scalar()


@db_func
def get_users(
        draw_id: int = None,
        creator_id: int = None,
) -> List[Tuple[int, int, str, str, str | None]]:
    mailing_mode = get_mailing_mode_subquery(User.id, ClientBot.id, ClientBot.group_id)
    query = sess().query(User.id, ClientBot.id, ClientBot.username, Group.name, mailing_mode)

    query = query.join(ClientBot.group)
    query = query.join(Draw, Draw.bot_id == ClientBot.id)
    query = query.join(DrawResult, DrawResult.draw_id == Draw.id)

    if draw_id:
        query = query.filter(Draw.id == draw_id)
    elif creator_id:
        query = query.filter(Draw.creator_id == creator_id)

    query = query.filter(User.id == DrawResult.user_id)

    return query.all()
