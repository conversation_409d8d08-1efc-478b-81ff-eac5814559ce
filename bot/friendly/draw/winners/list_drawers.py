from typing import Dict, Any, List, Literal

from aiogram.dispatcher import FSMContext

from db.models import Award<PERSON><PERSON><PERSON><PERSON>, Draw, DrawResult, User

from utils.keyboards import previous_button

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn
from utils.router.route_handlers import BaseListDrawer

from friendly.draw.functions import get_service_to_user_link

from friendly.draw.statistics import list_draw_results_with_all_conditions

from friendly.draw.winners.db_funcs import get_winners


class BaseWinnersListDrawer(BaseListDrawer, methods=None):
    config_page_size_variable_name = "FRIENDLY_DRAW_WINNERS_LIST_PAGE_SIZE"
    pagination_callback_mode = "friendly_draw_winners_pagination"

    need_check_selected = False
    need_previous_button = True
    need_setup_pagination_handler = True
    need_setup_search_handler = False

    @classmethod
    async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
        state_data = await state.get_data()
        return {"draw_id": state_data.get("draw_id")}

    @classmethod
    async def make_get_objects_kwargs(
        cls,
        user: User,
        search_text: str,
        data_from_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        data = data_from_state.copy()
        return data

    @classmethod
    async def footer_drawer(cls, keyboard: InlineKb, lang: str):
        keyboard.row(await previous_button(lang))


class WinnersListDrawer(BaseWinnersListDrawer):
    row_width = 2

    message_text_variable = "draw winners"
    empty_text_variable = "draw winners list empty"

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: Literal["all", "count"] = "all"
    ) -> List[DrawResult]:
        draw_id = get_objects_kwargs.get("draw_id")

        winners = await get_winners(draw_id, position=position, limit=limit, operation=operation)
        return winners

    @classmethod
    async def object_drawer(
        cls,
        draw_result: DrawResult,
        keyboard: InlineKb,
        user: User, lang: str,
        **kwargs
    ):
        is_simple_mode = draw_result.draw.is_simple_mode
        draw_winner = draw_result.user.name if draw_result.draw.is_public else f"{draw_result.id}"

        button_text = await f("draw winner button", lang, draw_winner=draw_winner)
        if user.id == draw_result.draw.creator_id:
            callback_data = c("choose_winner", draw_result_id=draw_result.id)
            button = InlineBtn(button_text, callback_data=callback_data)
        else:
            url = get_service_to_user_link(draw_result)
            button = InlineBtn(button_text, url=url)
        keyboard.add(button)

        if not is_simple_mode:
            button_text = await f("draw award button", lang, draw_award=draw_result.award_category.name)
            if user.id == draw_result.draw.creator_id:
                callback_data = c("choose_award", draw_result_id=draw_result.id)
            else:
                callback_data = c("show_award", draw_result_id=draw_result.id)
            button = InlineBtn(button_text, callback_data=callback_data)
            keyboard.insert(button)


class MembersListDrawer(BaseWinnersListDrawer):
    row_width = 1

    message_text_variable = "draw members with all conditions"
    empty_text_variable = "draw members list empty"

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: Literal["all", "count"] = "all"
    ) -> List[DrawResult] | int:
        draw_id = get_objects_kwargs.get("draw_id")

        draw = await Draw.get(draw_id)
        draw_results = await list_draw_results_with_all_conditions(draw)

        if operation == "count":
            return len(draw_results)
        return draw_results

    @classmethod
    async def object_drawer(
        cls,
        draw_result: DrawResult,
        keyboard: InlineKb,
        user: User, lang: str,
        **kwargs
    ):
        button_text = await f("draw member button", lang, draw_member=draw_result.user.name)
        callback_data = c("choose_winner", draw_result_id=draw_result.id)
        button = InlineBtn(button_text, callback_data=callback_data)
        keyboard.insert(button)


class AwardsListDrawer(BaseWinnersListDrawer):
    row_width = 1

    message_text_variable = "draw awards categories"
    empty_text_variable = "draw awards categories list empty"

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: Literal["all", "count"] = "all"
    ) -> List[AwardCategory] | int:
        draw_id = get_objects_kwargs.get("draw_id")

        draw = await Draw.get(draw_id)
        award_categories = draw.award_categories

        if operation == "count":
            return len(award_categories)
        return award_categories

    @classmethod
    async def object_drawer(
        cls,
        award_category: AwardCategory,
        keyboard: InlineKb,
        user: User, lang: str,
        **kwargs
    ):
        button_text = await f("draw award category button", lang, award_category=award_category.name)
        callback_data = c("choose_award", award_category_id=award_category.id)
        button = InlineBtn(button_text, callback_data=callback_data)
        keyboard.insert(button)
