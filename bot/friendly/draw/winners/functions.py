import random

from aiogram.dispatcher import FSMContext

from db.models import Draw, DrawResult

from ..statistics import list_draw_results_with_all_conditions


async def set_random_winners(draw: Draw):
    random.seed()

    await draw.update(status="stopped")

    results = await list_draw_results_with_all_conditions(draw)
    if not results:
        return

    for category in draw.award_categories:
        for _ in range(category.count_winners):
            result = random.choice(results)
            results.remove(result)

            await result.set_award_category(category)
