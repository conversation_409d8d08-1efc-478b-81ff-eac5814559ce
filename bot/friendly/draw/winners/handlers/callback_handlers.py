from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import Award<PERSON>ategory, DrawResult

from utils.message import send_tg_message
from utils.router import Router

from friendly.draw.statistics.states import DrawStatistics
from friendly.draw.winners.states import DrawWinners


async def choose_winner_draw_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    await state.update_data(**callback_data)
    cur_state = await state.get_state()

    draw_result_id = callback_data.get("draw_result_id")
    draw_result = await DrawResult.get(draw_result_id=draw_result_id)

    if cur_state == DrawWinners.ShowWinners.state:
        award_category_id = draw_result.award_category.id
        await state.update_data(award_category_id=award_category_id)
        await draw_result.set_award_category(None)
        await DrawWinners.ChooseWinner.set()

    else:
        state_data = await state.get_data()
        award_category_id = state_data.get("award_category_id")
        award_category = await AwardCategory.get(award_category_id)
        await draw_result.set_award_category(award_category)
        await DrawWinners.ShowWinners.set()

    await Router.state_menu(callback_query, state)


async def choose_award_draw_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    await state.update_data(**callback_data)
    state_data = await state.get_data()
    cur_state = await state.get_state()

    draw_result_id = state_data.get("draw_result_id")
    draw_result = await DrawResult.get(draw_result_id=draw_result_id)

    if cur_state == DrawWinners.ShowWinners.state:
        await draw_result.set_award_category(None)
        await DrawWinners.ChooseAward.set()

    else:
        award_category_id = callback_data.get("award_category_id")
        award_category = await AwardCategory.get(award_category_id)
        await draw_result.set_award_category(award_category)
        await DrawWinners.ShowWinners.set()

    await Router.state_menu(callback_query, state)


async def show_award_draw_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    await state.update_data(**callback_data)
    state_data = await state.get_data()

    draw_result_id = state_data.get("draw_result_id")
    draw_result = await DrawResult.get(draw_result_id=draw_result_id)

    award_category = draw_result.award_category
    await send_tg_message(callback_query.message.chat.id, **award_category.kwargs_for_send)


async def previous_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    cur_state = await state.get_state()

    if cur_state == DrawWinners.ShowWinners.state:
        await DrawStatistics.ShowResults.set()
    else:
        await DrawWinners.ShowWinners.set()

    await Router.state_menu(callback_query, state)


def register_winners_draw_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        choose_winner_draw_button_handler,
        callback_mode="choose_winner",
        chat_type="private",
        state=[DrawWinners.ShowWinners, DrawWinners.ChooseWinner],
    )

    dp.register_callback_query_handler(
        choose_award_draw_button_handler,
        callback_mode="choose_award",
        chat_type="private",
        state=[DrawWinners.ShowWinners, DrawWinners.ChooseAward],
    )

    dp.register_callback_query_handler(
        show_award_draw_button_handler,
        callback_mode="show_award",
        chat_type="private",
        state=DrawWinners.ShowWinners,
    )

    dp.register_callback_query_handler(
        previous_button_handler,
        previous_button=True,
        chat_type="private",
        state=DrawWinners,
    )
