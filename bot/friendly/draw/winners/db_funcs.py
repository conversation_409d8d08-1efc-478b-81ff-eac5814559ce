from datetime import datetime
from typing import Literal

from sqlalchemy import or_, and_, func
from db import db_func, sess
from db.models import Draw, DrawResult, User


@db_func
def get_winners(
        draw_id: int,
        position: int = 0,
        limit: int = None,
        operation: Literal["all", "count"] = "all",
):
    if operation == "count":
        query = sess().query(func.count(DrawResult.id))
    else:
        query = sess().query(DrawResult)

    query = query.filter(DrawResult.draw_id == draw_id)
    query = query.filter(DrawResult.award_category_id.is_not(None))

    if operation == "count":
        return query.scalar()

    slice_args = [position, None]
    if limit:
        slice_args[1] = position + limit
    query = query.slice(*slice_args)

    winners = query.all()
    return sorted(winners, key=lambda winner: winner.award_category_id)


@db_func
def get_draws():
    utc_current_datetime = datetime.utcnow()
    local_datetime = func.convert_tz(utc_current_datetime, "UTC", User.db_timezone)

    query = sess().query(Draw)
    query = query.join(User, User.id == Draw.creator_id)

    query = query.filter(Draw.status == "active")
    query = query.filter(Draw.time_spending <= local_datetime)

    return query.all()
