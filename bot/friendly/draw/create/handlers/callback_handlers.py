from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from psutils.fsm import get_field_from_state

from utils.router import Router

from friendly.draw.functions import clear_state_from_awards

from friendly.draw.create.functions import get_draw_link

from friendly.draw.create.states import Create<PERSON>raw


async def count_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    count = callback_data.get("count", 1)
    field_name = await get_field_from_state(state)

    if field_name == "award_category_count_winners":
        data = list()
        async with state.proxy() as state_data:
            saved_data = state_data.pop(field_name, [])
            data.extend(saved_data)

        data.append(count)
        await state.update_data({field_name: data})

        state_data = await state.get_data()
        is_simple_mode = state_data.get("is_simple_mode", True)
        count_award_categories = state_data.get("count_award_categories")

        if is_simple_mode:
            await state.update_data(count_award_categories=1, award_category_name=["simple_mode"], award_category_award=[{}])
            await CreateDraw.Contact.set()
        elif count_award_categories == len(data):
            await CreateDraw.next()
    else:
        await state.update_data({field_name: count})
        await CreateDraw.next()

    await Router.state_menu(callback_query, state)


async def follow_link_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        lang: str
):
    follow_link = await get_draw_link(callback_query.message.chat.id, state)

    await state.update_data(follow_link=follow_link)

    await CreateDraw.next()
    await Router.state_menu(callback_query, state, lang)


async def draw_results_type_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    await state.update_data(**callback_data)
    state_data = await state.get_data()
    is_simple_mode = state_data.get("is_simple_mode", True)

    if is_simple_mode:
        await CreateDraw.AwardCategoryCountWinners.set()
    else:
        await CreateDraw.next()
    await Router.state_menu(callback_query, state)


async def choose_bot_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    await state.update_data(**callback_data)

    await clear_state_from_awards(state)

    await CreateDraw.next()
    await Router.state_menu(callback_query, state)


async def draw_advanced_mode_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
):
    state_data = await state.get_data()
    is_simple_mode = state_data.get("is_simple_mode", True)
    is_simple_mode = not is_simple_mode

    await state.update_data(is_simple_mode=is_simple_mode)
    async with state.proxy() as state_data:
        state_data.pop("award_category_name")
        state_data.pop("award_category_count_winners")
        state_data.pop("award_category_award")

    if is_simple_mode:
        await state.update_data(count_award_categories=1, award_category_name=["simple_mode"], award_category_award=[{}])
        await CreateDraw.AwardCategoryCountWinners.set()
    else:
        await CreateDraw.CountAwardCategories.set()
    await Router.state_menu(callback_query, state)


async def draw_clear_advanced_mode_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
):
    cur_state = await state.get_state()

    async with state.proxy() as state_data:
        if cur_state == CreateDraw.AwardCategoryName.state:
            state_data.pop("award_category_name")
        elif cur_state == CreateDraw.AwardCategoryCountWinners.state:
            state_data.pop("award_category_count_winners")
        elif cur_state == CreateDraw.AwardCategoryAward.state:
            state_data.pop("award_category_award")
        else:
            state_data.pop("award_category_name")
            state_data.pop("award_category_count_winners")
            state_data.pop("award_category_award")

    await Router.state_menu(state=state, get_state_message=True)


def register_create_draw_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        count_button_handler,
        callback_mode="set_count",
        chat_type="private",
        state=[
            CreateDraw.CountInviteFriends,
            CreateDraw.CountFollowedLink,
            CreateDraw.CountNeededMessages,
            CreateDraw.CountAwardCategories,
            CreateDraw.AwardCategoryCountWinners,
            CreateDraw.CountNotifications,
        ],
    )

    dp.register_callback_query_handler(
        follow_link_button_handler,
        callback_mode="follow_link",
        chat_type="private",
        state=CreateDraw.FollowLink,
    )

    dp.register_callback_query_handler(
        draw_results_type_button_handler,
        callback_mode="draw_results_type",
        chat_type="private",
        state=CreateDraw.IsPublic,
    )

    dp.register_callback_query_handler(
        choose_bot_button_handler,
        callback_mode="draw_choose_bot",
        chat_type="private",
        state=CreateDraw.ChooseBot,
    )

    dp.register_callback_query_handler(
        draw_advanced_mode_button_handler,
        callback_mode="advanced_mode",
        chat_type="private",
        state=[
            CreateDraw.CountAwardCategories,
            CreateDraw.AwardCategoryName,
            CreateDraw.AwardCategoryCountWinners,
            CreateDraw.AwardCategoryAward,
        ],
    )

    dp.register_callback_query_handler(
        draw_clear_advanced_mode_button_handler,
        callback_mode="clear_advanced_mode",
        chat_type="private",
        state=[
            CreateDraw.CountAwardCategories,
            CreateDraw.AwardCategoryName,
            CreateDraw.AwardCategoryCountWinners,
            CreateDraw.AwardCategoryAward,
        ],
    )
