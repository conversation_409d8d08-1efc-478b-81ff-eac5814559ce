import logging
from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import ClientBot

from psutils.convertors import str_to_datetime
from psutils.date_time import localise_datetime

from friendly.main.keyboards import get_menu_keyboard

from friendly.draw.create.list_drawers import ClientBotListDrawer

from friendly.draw.create.states import CreateDraw
from friendly.draw.edit.states import EditDraw

from friendly.draw.routes import *


async def save_draw_done_menu(message: types.Message, state: FSMContext, user: User, lang: str):
    keys = [
        "name", "media", "description", "terms", "time_spending",
        "channels_for_subscription", "channel_to_invite_friends", "count_invite_friends", "follow_link", "count_followed_link",
        "channel_for_writing_messages", "count_needed_messages", "instructions_for_writing_messages", "filter_words_message",
        "award_category_name", "award_category_count_winners", "award_category_award",
        "bot_id", "is_public", "contact", "count_notifications",
    ]

    state_data = await state.get_data()
    is_simple_mode = state_data.get("is_simple_mode", True)
    name, media, description, terms, time_spending, channels_for_subscription, channel_to_invite_friends, count_invite_friends, follow_link, count_followed_link, \
    channel_for_writing_messages, count_needed_messages, instructions_for_writing_messages, filter_words_message, award_category_name, award_category_count_winners, award_category_award, \
    bot_id, is_public, contact, count_notifications = list(map(lambda key: state_data.get(key), keys))

    content_type, media_path = "text", None
    if media:
        content_type, media_path = media.get("content_type"), media.get("media_path")

    time_spending = str_to_datetime(time_spending)
    time_spending = localise_datetime(time_spending, user.get_timezone())

    # bot_id это бот для которого мы создаем розыгрыш, а bot.id это бот в котором мы его создавали
    crated_by_bot = await ClientBot.get_current()
    draw = await Draw.create(
        name, bot_id, crated_by_bot.id, user.id,
        contact, terms, time_spending,
        content_type, description, media_path
    )

    if not channels_for_subscription:
        channels_for_subscription = list()
    for channel_to_subscription_id in channels_for_subscription:
        channel_to_subscription = await Channel.get(channel_to_subscription_id)
        await draw.add_channel_to_subscription(channel_to_subscription)

    if channel_to_invite_friends:
        channel_to_invite_friends = await Channel.get(channel_to_invite_friends)
        await draw.set_channel_to_invite_friends(channel_to_invite_friends, count_invite_friends)

    if follow_link:
        await draw.set_follow_link(follow_link, count_followed_link)

    if channel_for_writing_messages:
        channel_for_writing_messages = await Channel.get(channel_for_writing_messages)
        await draw.set_channel_for_writing_messages(channel_for_writing_messages, count_needed_messages, instructions_for_writing_messages)
        if filter_words_message:
            await draw.append_filter(filter_words_message)

    await draw.update(
        is_simple_mode=is_simple_mode,
        is_public=is_public,
        contact_id=contact,
        count_notifications=count_notifications,
    )

    for name, count_winners, award in zip(award_category_name, award_category_count_winners, award_category_award):
        award_category = await AwardCategory.create(draw.id, name, count_winners)
        if not is_simple_mode:
            await Award.create(award_category, **award)

    message_text = await f("draw successfully saved text", lang)
    keyboard = await get_menu_keyboard(user, lang, crated_by_bot.id)
    msg = await message.answer(message_text, reply_markup=keyboard)
    await message.delete()

    await EditDraw.first()
    await Router.state_menu(msg, state, lang, mode="new")


def register_create_draw_routes(router: Router):
    router.add_route(CreateDraw.ChooseBot, ClientBotListDrawer())
    router.add_route(CreateDraw.Name, send_enter_draw_name_menu)
    router.add_route(CreateDraw.Media, send_enter_draw_media_menu)
    router.add_route(CreateDraw.Description, send_enter_draw_description_menu)
    router.add_route(CreateDraw.Terms, send_enter_draw_terms_menu)
    router.add_route(CreateDraw.TimeSpending, send_enter_draw_time_spending_menu)
    router.add_route(CreateDraw.ChannelsForSubscription, send_enter_draw_channels_for_subscription_menu)
    router.add_route(CreateDraw.ChannelToInviteFriends, send_enter_draw_channel_to_invite_friends_menu)
    router.add_route(CreateDraw.CountInviteFriends, send_enter_draw_count_invite_friends_menu)
    router.add_route(CreateDraw.FollowLink, send_enter_draw_follow_link_menu)
    router.add_route(CreateDraw.CountFollowedLink, send_enter_draw_count_followed_link_menu)
    router.add_route(CreateDraw.ChannelForWritingMessages, send_enter_draw_channel_for_writing_messages_menu)
    router.add_route(CreateDraw.CountNeededMessages, send_enter_draw_count_needed_messages_menu)
    router.add_route(CreateDraw.InstructionsForWritingMessages, send_enter_draw_instructions_for_writing_messages_menu)
    router.add_route(CreateDraw.FilterWordsMessage, send_enter_draw_filter_words_message_menu)
    router.add_route(CreateDraw.IsPublic, send_enter_draw_is_public_menu)
    router.add_route(CreateDraw.CountAwardCategories, send_enter_draw_count_award_categories_menu)
    router.add_route(CreateDraw.AwardCategoryName, send_enter_draw_award_category_name_menu)
    router.add_route(CreateDraw.AwardCategoryCountWinners, send_enter_draw_award_category_count_winners_menu)
    router.add_route(CreateDraw.AwardCategoryAward, send_enter_draw_award_category_award_menu)
    router.add_route(CreateDraw.Contact, send_enter_draw_contact_menu)
    router.add_route(CreateDraw.CountNotifications, send_enter_draw_count_notifications_menu)
    router.add_route(CreateDraw.Done, save_draw_done_menu)
