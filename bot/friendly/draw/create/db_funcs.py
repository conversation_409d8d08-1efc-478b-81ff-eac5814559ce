from typing import Literal

from sqlalchemy import func
from sqlalchemy import and_, or_

from db import db_func, sess
from db.models import Draw, ClientBot, UserClientBotActivity


@db_func
def get_last_draw() -> Draw:
    query = sess().query(Draw)

    query = query.order_by(Draw.id.desc())
    query = query.limit(1)

    return query.first()


@db_func
def get_client_and_friendly_bots(
        user_id: int,
        position: int = 0,
        limit: int = None,
        operation: Literal["all", "count"] = "all"
) -> list[ClientBot]:
    if operation == "count":
        query = sess().query(func.count(ClientBot.id))
    else:
        query = sess().query(ClientBot)

    query = query.join(UserClientBotActivity, UserClientBotActivity.user_id == user_id)
    query = query.filter(ClientBot.id == UserClientBotActivity.bot_id)

    bot_type_filter = or_(
        ClientBot.is_pay4say.is_(True),
        ClientBot.is_friendly.is_(True),
    )
    query = query.filter(bot_type_filter)

    if operation == "count":
        return query.scalar()

    slice_args = [position, None]
    if limit:
        slice_args[1] = position + limit
    query = query.slice(*slice_args)

    return query.all()
