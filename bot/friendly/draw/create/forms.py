from aiogram import types
from aiogram.dispatcher import FSMContext

import config as cfg

from db.models import Award

from psutils.forms import WizardForm

from psutils.forms.fields import DateT<PERSON><PERSON>ield, IntegerField
from psutils.forms.fields import Message<PERSON>ield, TextField

from psutils.forms.helpers import with_delete_state_messages

from psutils.fsm import get_field_from_state

from utils.text import f

from utils.router import Router

from friendly.functions import post_data_processor

from friendly.draw.functions import check_and_get_channel, check_and_get_contact

from friendly.channel.edit.states import EditChannel
from friendly.draw.create.states import CreateDraw
from friendly.draw.edit.states import EditDraw


async def data_saver_media(data: dict, state: FSMContext):
    await state.update_data(media=data)


async def data_saver_time_spending(data: dict, state: FSMContext):
    time_spending = data.get("time_spending")
    await state.update_data(time_spending=time_spending)


async def data_saver_channels_for_subscription(data: dict, state: FSMContext):
    channel_username = data.get("channels_for_subscription")
    channel_id = await check_and_get_channel(channel_username)

    if channel_id:
        channels = list()
        async with state.proxy() as state_data:
            channels_for_subscription = state_data.pop("channels_for_subscription", [])
            channels.extend(channels_for_subscription)

        channels.append(channel_id)
        channels = list(set(channels))
        await state.update_data(channels_for_subscription=channels)
        await state.update_data(add_channel_status=True)
    else:
        await state.update_data(add_channel_status=False)


async def post_save_channels_for_subscription(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    add_channel_status = state_data.get("add_channel_status", False)

    status = "success" if add_channel_status else "error"
    await message.answer(await f(f"draw {status} add channel subscription text", lang))


async def data_saver_channel_to_invite_friends(data: dict, state: FSMContext):
    channel_username = data.get("channel_to_invite_friends")
    channel_id = await check_and_get_channel(channel_username)

    if channel_id:
        await state.update_data(channel_to_invite_friends=channel_id)


async def data_saver_channel_for_writing_messages(data: dict, state: FSMContext):
    channel_username = data.get("channel_for_writing_messages")
    channel_id = await check_and_get_channel(channel_username)

    if channel_id:
        await state.update_data(channel_for_writing_messages=channel_id)


async def post_save_add_channel(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    field_name = await get_field_from_state(state)
    add_channel_status = state_data.get(field_name)

    if not add_channel_status:
        await message.answer(await f(f"draw error add {field_name} text", lang))
        return await Router.state_menu(message, state, mode="new")

    await CreateDraw.next()


async def data_saver_follow_link(_: dict):
    pass


async def post_save_follow_link(message: types.Message, state: FSMContext, lang: str):
    await Router.state_menu(message, state, lang)


async def data_saver_award_category(data: dict, state: FSMContext):
    field_name = await get_field_from_state(state)
    state_data = await state.get_data()
    if state_data.get("is_simple_mode", True):
        await state.update_data(count_award_categories=1, award_category_name=["simple_mode"], award_category_award=[{}])
    award_data = list()
    async with state.proxy() as state_data:
        saved_data = state_data.pop(field_name, [])
        award_data.extend(saved_data)

    if field_name == "award_category_award":
        award_data.append(data)
    else:
        award_data.append(data.get(field_name))
    await state.update_data({field_name: award_data})


async def post_save_award_category(state: FSMContext):
    field_name = await get_field_from_state(state)
    state_data = await state.get_data()
    is_simple_mode = state_data.get("is_simple_mode", True)
    count_award_categories = state_data.get("count_award_categories")
    saved_data = state_data.get(field_name, [])

    if is_simple_mode:
        await CreateDraw.Contact.set()
    elif count_award_categories == len(saved_data):
        await CreateDraw.next()


async def data_saver_contact(data: dict, state: FSMContext):
    user = await check_and_get_contact(data.get("contact"))

    if user:
        await state.update_data(contact=user.id)


async def post_save_contact(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    add_contact_status = state_data.get("contact")

    status = "success" if add_contact_status else "error"
    await message.answer(await f(f"draw {status} add contact text", lang))

    if add_contact_status:
        await CreateDraw.next()


class CreateDrawForm(WizardForm):
    state_group = CreateDraw

    name = TextField()

    media = MessageField(
        *cfg.MEDIA_WITH_CAPTION,
        error_text_variable="invalid draw media message type error",
        data_processor=post_data_processor,
        data_saver=data_saver_media,
    )

    description = TextField()
    terms = TextField()

    time_spending = DateTimeField(
        data_saver=data_saver_time_spending,
    )

    channels_for_subscription = TextField(
        data_saver=data_saver_channels_for_subscription,
        post_save=post_save_channels_for_subscription,
        need_go_next_state=False,
    )

    channel_to_invite_friends = TextField(
        data_saver=data_saver_channel_to_invite_friends,
        post_save=post_save_add_channel,
        need_go_next_state=False,
    )
    count_invite_friends = IntegerField(
        only_positive=True,
    )

    follow_link = TextField(
        data_saver=data_saver_follow_link,
        post_save=post_save_follow_link,
        need_go_next_state=False,
    )
    count_followed_link = IntegerField(
        only_positive=True,
    )

    channel_for_writing_messages = TextField(
        data_saver=data_saver_channel_for_writing_messages,
        post_save=post_save_add_channel,
        need_go_next_state=False,
    )
    count_needed_messages = IntegerField(
        only_positive=True,
    )
    instructions_for_writing_messages = TextField()
    filter_words_message = TextField()

    count_award_categories = IntegerField(
        only_positive=True,
    )

    award_category_name = TextField(
        data_saver=data_saver_award_category,
        post_save=post_save_award_category,
        need_go_next_state=False,
    )
    award_category_count_winners = IntegerField(
        only_positive=True,
        data_saver=data_saver_award_category,
        post_save=post_save_award_category,
        need_go_next_state=False,
    )
    award_category_award = MessageField(
        *Award.SUPPORTED_MESSAGE_TYPES,
        error_text_variable="invalid draw media message type error",
        data_processor=post_data_processor,
        data_saver=data_saver_award_category,
        post_save=post_save_award_category,
        need_go_next_state=False,
    )

    contact = TextField(
        data_saver=data_saver_contact,
        post_save=post_save_contact,
        need_go_next_state=False,
    )

    count_notifications = IntegerField(
        only_positive=True,
    )

    @classmethod
    async def data_saver(cls, data: dict, state: FSMContext):
        field_name = await get_field_from_state(state)
        await state.update_data({field_name: data.get(field_name)})

    @classmethod
    async def next_field(cls, state: FSMContext):
        await cls.set_next_state(state)

    @classmethod
    async def set_next_state(cls, state: FSMContext = None):
        await cls.state_group.next()

    @classmethod
    @with_delete_state_messages
    async def next_button_handler(
            cls, callback_query: types.CallbackQuery,
            state: FSMContext, lang: str,
    ):
        cur_state = await state.get_state()
        state_data = await state.get_data()
        is_simple_mode = state_data.get("is_simple_mode", True)

        if cur_state == CreateDraw.ChannelToInviteFriends.state and not state_data.get("channel_to_invite_friends"):
            await CreateDraw.FollowLink.set()
        elif cur_state == CreateDraw.FollowLink.state and not state_data.get("follow_link"):
            await CreateDraw.ChannelForWritingMessages.set()
        elif cur_state == CreateDraw.ChannelForWritingMessages.state and not state_data.get("channel_for_writing_messages"):
            await CreateDraw.IsPublic.set()
        elif cur_state == CreateDraw.IsPublic.state and is_simple_mode:
            await CreateDraw.AwardCategoryCountWinners.set()
        elif cur_state == CreateDraw.AwardCategoryCountWinners.state and is_simple_mode:
            await CreateDraw.Contact.set()
        else:
            await cls.set_next_state()

        await Router.state_menu(callback_query, state, lang)

    @classmethod
    @with_delete_state_messages
    async def set_prev_state(cls, state: FSMContext):
        cur_state = await state.get_state()
        state_data = await state.get_data()
        is_simple_mode = state_data.get("is_simple_mode", True)

        if cur_state == CreateDraw.ChooseBot.state:
            await state.finish()
            if state_data.get("main", False):
                await EditChannel.first()
            else:
                await EditDraw.first()
        elif cur_state == CreateDraw.FollowLink.state and not state_data.get("channel_to_invite_friends"):
            await CreateDraw.ChannelToInviteFriends.set()
        elif cur_state == CreateDraw.ChannelForWritingMessages.state and not state_data.get("follow_link"):
            await CreateDraw.FollowLink.set()
        elif cur_state == CreateDraw.IsPublic.state and not state_data.get("channel_for_writing_messages"):
            await CreateDraw.ChannelForWritingMessages.set()
        elif cur_state == CreateDraw.AwardCategoryCountWinners.state and is_simple_mode:
            await CreateDraw.IsPublic.set()
        elif cur_state == CreateDraw.Contact.state and is_simple_mode:
            await CreateDraw.AwardCategoryCountWinners.set()
        elif cur_state == CreateDraw.AwardCategoryCountWinners.state and not is_simple_mode:
            async with state.proxy() as state_data:
                state_data.pop("award_category_name")
            await CreateDraw.AwardCategoryName.set()
        elif cur_state == CreateDraw.AwardCategoryAward.state and not is_simple_mode:
            async with state.proxy() as state_data:
                state_data.pop("award_category_count_winners")
            await CreateDraw.AwardCategoryCountWinners.set()
        elif cur_state == CreateDraw.Contact.state and not is_simple_mode:
            async with state.proxy() as state_data:
                state_data.pop("award_category_award")
            await CreateDraw.AwardCategoryAward.set()
        else:
            await cls.state_group.previous()
