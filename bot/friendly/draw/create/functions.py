from aiogram.dispatcher import FSMContext

from db.models import ClientBot

from utils.message import send_tg_message

from .db_funcs import get_last_draw


async def get_draw_link(chat_id: int, state: FSMContext) -> str:
    state_data = await state.get_data()

    bot_id = state_data.get("bot_id")
    client_bot = await ClientBot.get(bot_id=bot_id)

    draw = await get_last_draw()
    index = draw.id + 1 if draw else 1

    follow_link = f"https://t.me/{client_bot.username}?start=draw-{index}"
    await send_tg_message(chat_id, "text", text=follow_link)

    return follow_link
