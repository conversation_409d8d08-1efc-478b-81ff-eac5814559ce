from aiogram.dispatcher.filters.state import StatesGroup, State


class CreateDraw(StatesGroup):
    ChooseBot = State()
    Name = State()
    Media = State()
    Description = State()
    Terms = State()
    TimeSpending = State()
    ChannelsForSubscription = State()
    ChannelToInviteFriends = State()
    CountInviteFriends = State()
    FollowLink = State()
    CountFollowedLink = State()
    ChannelForWritingMessages = State()
    CountNeededMessages = State()
    InstructionsForWritingMessages = State()
    FilterWordsMessage = State()
    IsPublic = State()
    CountAwardCategories = State()
    AwardCategoryName = State()
    AwardCategoryCountWinners = State()
    AwardCategoryAward = State()
    Contact = State()
    CountNotifications = State()
    Done = State()
