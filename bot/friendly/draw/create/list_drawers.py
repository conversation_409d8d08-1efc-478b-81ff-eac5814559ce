from typing import Any, Dict, List, Literal

from aiogram.dispatcher import FSMContext

from db.models import ClientBot, User
from utils.redefined_classes import InlineBtn, InlineKb
from utils.router.route_handlers import BaseListDrawer
from utils.text import c, f
from .db_funcs import get_client_and_friendly_bots


class ClientBotListDrawer(BaseListDrawer):

    row_width = 1

    config_page_size_variable_name = "DRAW_CLIENT_BOT_LIST_PAGE_SIZE"

    need_previous_button = True
    need_setup_pagination_handler = True
    need_setup_search_handler = False

    message_text_variable = "pick client bot"
    empty_text_variable = "client bot list empty"

    pagination_callback_mode = "draw_bot_pagination"

    @classmethod
    async def get_data_from_state(
            cls, user: User, state: FSMContext, mode: str = "new"
    ) -> Dict[str, Any]:
        return {"user_id": user.id}

    @classmethod
    async def make_get_objects_kwargs(
            cls,
            user: User,
            search_text: str,
            data_from_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        return data_from_state.copy()

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: Literal["all", "count"] = "all"
    ) -> List[ClientBot]:
        user_id = get_objects_kwargs.get("user_id")
        client_and_friendly_bots = await get_client_and_friendly_bots(
            user_id, position=position, limit=limit, operation=operation
        )

        if operation == "count":
            return client_and_friendly_bots

        cur_bot = await ClientBot.get_current()
        bots = [bot for bot in client_and_friendly_bots if
                cur_bot and cur_bot.id == bot.id]
        bots.extend(
            [bot for bot in client_and_friendly_bots if
             cur_bot and cur_bot.id != bot.id]
        )

        return bots

    @classmethod
    async def object_drawer(
            cls,
            client_bot: ClientBot,
            keyboard: InlineKb, lang: str
    ):
        cur_bot = await ClientBot.get_current()
        bot_username = await f(
            "draw friendly bot this text", lang
        ) if cur_bot and cur_bot.id == client_bot.id else client_bot.username

        bot_btn_handle = await f(
            "draw client bot button", lang,
            bot_username=bot_username,
        )
        callback_data = c("draw_choose_bot", bot_id=client_bot.id)

        button = InlineBtn(bot_btn_handle, callback_data=callback_data)
        keyboard.insert(button)
