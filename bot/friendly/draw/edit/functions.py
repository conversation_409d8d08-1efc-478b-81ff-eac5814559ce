from aiogram.dispatcher import FSMContext

from db.models import Draw

from utils.message import send_tg_message


async def get_draw_link(
        chat_id: int,
        state: FSMContext,
        send_link: bool = False,
        save_link: bool = False,
) -> str:
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    client_bot = draw.bot
    index = draw.id

    follow_link = f"https://t.me/{client_bot.username}?start=draw-{index}"

    if send_link:
        await send_tg_message(chat_id, "text", text=follow_link)

    if save_link:
        await draw.update(follow_link=follow_link)

    return follow_link
