from typing import Literal

from sqlalchemy import or_, and_, func
from db import db_func, sess
from db.models import Draw


@db_func
def get_draws(
        user_id: int,
        search_text: str = None,
        status: str = None,
        position: int = 0,
        limit: int = None,
        operation: Literal["all", "count"] = "all",
):
    if operation == "count":
        query = sess().query(func.count(Draw.id))
    else:
        query = sess().query(Draw)

    query = query.filter(Draw.creator_id == user_id)

    if search_text:
        query = query.filter(Draw.name.contains(search_text))

    if status:
        query = query.filter(Draw.status == status)

    if operation == "count":
        return query.scalar()

    query = query.order_by(Draw.time_created.desc())

    slice_args = [position, None]
    if limit:
        slice_args[1] = position + limit
    query = query.slice(*slice_args)

    return query.all()
