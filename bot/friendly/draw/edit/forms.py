from aiogram import types
from aiogram.dispatcher import FSMContext

import config as cfg

from db.models import Award, Award<PERSON>ategory, Draw, User, Channel

from psutils.convertors import str_to_datetime

from psutils.forms import FieldsListForm

from psutils.forms.fields import DateT<PERSON><PERSON>ield, IntegerField
from psutils.forms.fields import <PERSON><PERSON>ield, TextField

from psutils.forms.helpers import with_delete_state_messages

from psutils.fsm import get_field_from_state

from utils.text import f
from psutils.date_time import localise_datetime
from utils.router import Router

from friendly.functions import post_data_processor

from friendly.draw.functions import check_and_get_channel, check_and_get_contact, clear_state_from_awards

from friendly.channel.edit.states import EditChannel
from friendly.draw.edit.states import EditDraw


async def data_saver_media(data: dict, state: FSMContext):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    data.pop("text")
    await draw.update(**data)

    await EditDraw.ChooseField.set()
    await Router.state_menu(state=state)


async def data_saver_time_spending(data: dict, state: FSMContext, user: User):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    time_spending = data.get("time_spending")
    time_spending = str_to_datetime(time_spending)
    time_spending = localise_datetime(time_spending, user.get_timezone())

    await draw.update(time_spending=time_spending)

    await EditDraw.ChooseField.set()
    await Router.state_menu(state=state)


async def data_saver_channels_for_subscription(data: dict, state: FSMContext):
    channel_username = data.get("channels_for_subscription")
    channel_id = await check_and_get_channel(channel_username)

    if channel_id:
        state_data = await state.get_data()
        draw = await Draw.get(state_data.get("draw_id"))
        channel = await Channel.get(channel_id)
        await draw.add_channel_to_subscription(channel)
        await state.update_data(add_channel_status=True)


async def post_save_channels_for_subscription(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    add_channel_status = state_data.get("add_channel_status", False)

    status = "success" if add_channel_status else "error"
    await message.answer(await f(f"draw {status} add channel subscription text", lang))

    await Router.state_menu(message, state, mode="new")


async def data_saver_channel_to_invite_friends(data: dict, state: FSMContext):
    state_data = await state.get_data()
    channel_username = data.get("channel_to_invite_friends")
    channel_id = await check_and_get_channel(channel_username)

    if channel_id:
        draw = await Draw.get(state_data.get("draw_id"))
        await draw.update(channel_to_invite_friends_id=channel_id)
        await state.update_data(add_channel_status=True)
    else:
        await state.update_data(add_channel_status=False)


async def data_saver_channel_for_writing_messages(data: dict, state: FSMContext):
    state_data = await state.get_data()
    channel_username = data.get("channel_for_writing_messages")
    channel_id = await check_and_get_channel(channel_username)

    if channel_id:
        draw = await Draw.get(state_data.get("draw_id"))
        await draw.update(channel_for_writing_messages_id=channel_id)


async def post_save_add_channel(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    field_name = await get_field_from_state(state)
    add_channel_status = state_data.get(field_name)

    if not add_channel_status:
        await message.answer(await f(f"draw error add {field_name} text", lang))
        await Router.state_menu(message, state, mode="new")
    else:
        await EditDraw.ChooseField.set()
        await Router.state_menu(message, state)


async def data_saver_filter_words_message(data: dict, state: FSMContext):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    filter_words_message = data.get("filter_words_message")
    await draw.append_filter(filter_words_message)

    await EditDraw.ChooseField.set()
    await Router.state_menu(state=state)


async def data_saver_count_award_categories(data: dict, state: FSMContext):
    await clear_state_from_awards(state)
    await state.update_data(count_award_categories=data.get("count_award_categories"))

    await EditDraw.next()


async def data_saver_award_category(data: dict, state: FSMContext):
    field_name = await get_field_from_state(state)
    award_data = list()
    async with state.proxy() as state_data:
        saved_data = state_data.pop(field_name, [])
        award_data.extend(saved_data)

    if field_name == "award_category_award":
        award_data.append(data)
    else:
        award_data.append(data.get(field_name))
    await state.update_data({field_name: award_data})


async def post_save_award_category(message: types.Message, state: FSMContext, lang: str):
    field_name = await get_field_from_state(state)
    state_data = await state.get_data()
    count_award_categories = state_data.get("count_award_categories")
    saved_data = state_data.get(field_name, [])

    draw = await Draw.get(state_data.get("draw_id"))
    is_simple_mode = draw.is_simple_mode

    if count_award_categories == len(saved_data):
        if field_name == "award_category_award" or is_simple_mode:
            for award_category in draw.award_categories:
                await award_category.delete()

            award_category_name = state_data.get("award_category_name", [])
            award_category_count_winners= state_data.get("award_category_count_winners", [])
            award_category_award = state_data.get("award_category_award", [])

            for name, count_winners, award in zip(award_category_name, award_category_count_winners, award_category_award):
                award_category = await AwardCategory.create(draw.id, name, count_winners)
                if not is_simple_mode:
                    await Award.create(award_category, **award)

            await EditDraw.ChooseField.set()

        else:
            await EditDraw.next()
    await Router.state_menu(message, state, lang)


async def data_saver_contact(data: dict, state: FSMContext):
    state_data = await state.get_data()
    user = await check_and_get_contact(data.get("contact"))

    if user:
        draw = await Draw.get(state_data.get("draw_id"))
        await draw.update(contact_id=user.id)
        await state.update_data(add_contact_status=True)
    else:
        await state.update_data(add_contact_status=False)


async def post_save_contact(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    add_contact_status = state_data.get("add_contact_status", False)

    status = "success" if add_contact_status else "error"
    msg = await message.answer(await f(f"draw {status} add contact text", lang))
    await message.delete()

    if add_contact_status:
        await EditDraw.ChooseField.set()
    return await Router.state_menu(msg, state, mode="new")


class EditDrawForm(FieldsListForm):
    state_group = EditDraw

    choose_field_state = EditDraw.ChooseField.state
    back_to_previous_state_excluded_keys = ("draw_id",)

    name = TextField()

    media = MessageField(
        *cfg.MEDIA_WITH_CAPTION,
        error_text_variable="invalid draw media message type error",
        data_processor=post_data_processor,
        data_saver=data_saver_media,
    )

    description = TextField()
    terms = TextField()

    time_spending = DateTimeField(
        data_saver=data_saver_time_spending,
    )

    channels_for_subscription = TextField(
        data_saver=data_saver_channels_for_subscription,
        post_save=post_save_channels_for_subscription,
        need_go_next_state=False,
    )

    channel_to_invite_friends = TextField(
        data_saver=data_saver_channel_to_invite_friends,
        post_save=post_save_add_channel,
    )
    count_invite_friends = IntegerField(
        only_positive=True,
    )

    count_followed_link = IntegerField(
        only_positive=True,
    )

    channel_for_writing_messages = TextField(
        data_saver=data_saver_channel_for_writing_messages,
        post_save=post_save_add_channel,
    )
    count_needed_messages = IntegerField(
        only_positive=True,
    )
    instructions_for_writing_messages = TextField()
    filter_words_message = TextField(
        data_saver=data_saver_filter_words_message,
    )

    count_award_categories = IntegerField(
        only_positive=True,
        data_saver=data_saver_count_award_categories,
    )

    award_category_name = TextField(
        data_saver=data_saver_award_category,
        post_save=post_save_award_category,
        need_go_next_state=False,
    )
    award_category_count_winners = IntegerField(
        only_positive=True,
        data_saver=data_saver_award_category,
        post_save=post_save_award_category,
        need_go_next_state=False,
    )
    award_category_award = MessageField(
        *Award.SUPPORTED_MESSAGE_TYPES,
        error_text_variable="invalid draw media message type error",
        data_processor=post_data_processor,
        data_saver=data_saver_award_category,
        post_save=post_save_award_category,
        need_go_next_state=False,
    )

    contact = TextField(
        data_saver=data_saver_contact,
        post_save=post_save_contact,
    )

    count_notifications = IntegerField(
        only_positive=True,
    )

    @classmethod
    async def data_saver(cls, data: dict, state: FSMContext):
        field_name = await get_field_from_state(state)
        state_data = await state.get_data()
        draw = await Draw.get(state_data.get("draw_id"))

        await draw.update(**{field_name: data.get(field_name)})

        await EditDraw.ChooseField.set()
        await Router.state_menu(state=state)

    @classmethod
    @with_delete_state_messages
    async def set_prev_state(cls, state: FSMContext):
        cur_state = await state.get_state()

        if cur_state == EditDraw.ChooseDraw.state:
            await state.finish()
            await EditChannel.first()
        elif cur_state == EditDraw.SelectMenu.state:
            await state.finish()
            await EditDraw.first()
        elif cur_state == EditDraw.ChooseField.state:
            await EditDraw.SelectMenu.set()
        else:
            await EditDraw.ChooseField.set()

        await Router.state_menu(state=state)
