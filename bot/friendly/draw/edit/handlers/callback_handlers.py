from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import AwardCategory, Draw

from psutils.fsm import get_field_from_state, get_state_name_from_field

from friendly.helpers import send_error
from utils.text import f

from utils.router import Router

from friendly.draw.edit.functions import get_draw_link

from friendly.draw.create.states import CreateDraw
from friendly.draw.edit.states import EditDraw
from friendly.draw.mailing.states import DrawMailingState
from friendly.draw.statistics.states import DrawStatistics


async def create_draw_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext
):
    await CreateDraw.first()
    await Router.state_menu(callback_query, state)


async def choose_draw_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    await state.update_data(**callback_data)
    draw = await Draw.get(callback_data.get("draw_id"))

    if draw.status == "active":
        await EditDraw.SelectMenu.set()
    else:
        await DrawStatistics.ShowResults.set()

    await Router.state_menu(callback_query, state)


async def edit_draw_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, mode: str,
):
    field_name = mode[5:]
    state_name = get_state_name_from_field(field_name)

    await getattr(EditDraw, state_name).set()
    await Router.state_menu(callback_query, state)


async def count_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    count = callback_data.get("count", 1)
    field_name = await get_field_from_state(state)

    if field_name == "count_award_categories":
        await state.update_data(count_award_categories=count)
        await EditDraw.next()
    elif field_name == "award_category_count_winners":
        data = list()
        async with state.proxy() as state_data:
            saved_data = state_data.pop(field_name, [])
            data.extend(saved_data)

        data.append(count)
        await state.update_data({field_name: data})

        state_data = await state.get_data()
        count_award_categories = state_data.get("count_award_categories")
        draw = await Draw.get(state_data.get("draw_id"))
        if draw.is_simple_mode:
            for award_category in draw.award_categories:
                await award_category.delete()

            award_category_name = state_data.get("award_category_name", [])
            award_category_count_winners= state_data.get("award_category_count_winners", [])

            for name, count_winners in zip(award_category_name, award_category_count_winners):
                await AwardCategory.create(draw.id, name, count)

            await EditDraw.ChooseField.set()

        elif count_award_categories == len(data):
            await EditDraw.next()
    else:
        state_data = await state.get_data()
        draw = await Draw.get(state_data.get("draw_id"))

        await draw.update(**{field_name: count})
        await EditDraw.ChooseField.set()

    await Router.state_menu(callback_query, state)


async def draw_results_type_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    is_public = callback_data.get("is_public")
    await draw.update(is_public=is_public)

    await EditDraw.ChooseField.set()
    await Router.state_menu(callback_query, state)


async def draw_change_awards_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    await state.update_data(is_simple_mode=draw.is_simple_mode)
    if draw.is_simple_mode:
        await state.update_data(count_award_categories=1, award_category_name=["simple_mode"], award_category_award=[{}])
        await EditDraw.AwardCategoryCountWinners.set()
    else:
        await EditDraw.CountAwardCategories.set()
    await Router.state_menu(callback_query, state)


async def follow_link_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        lang: str
):
    await get_draw_link(callback_query.message.chat.id, state, save_link=True)

    await EditDraw.ChooseField.set()
    await Router.state_menu(callback_query, state, lang)


async def draw_delete_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
):
    await EditDraw.DeleteDraw.set()
    await Router.state_menu(callback_query, state)


async def yes_delete_draw_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    result = await draw.delete()
    if not result:
        return await send_error(callback_query.message)

    await EditDraw.first()
    await Router.state_menu(callback_query, state, lang)


async def no_delete_draw_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await callback_query.answer(await f("action cancel text", lang))
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    if draw.status == "active":
        await EditDraw.ChooseField.set()
    else:
        await DrawStatistics.ShowResults.set()
    await Router.state_menu(callback_query, state, lang)


async def draw_members_list_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
):
    await state.update_data(members_from_edit=True)

    await DrawStatistics.ShowMembers.set()
    await Router.state_menu(callback_query, state)


async def follow_link_delete_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
):
    await EditDraw.DeleteFollowLink.set()
    await Router.state_menu(callback_query, state)


async def yes_delete_follow_link_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    result = await draw.update(follow_link=None)
    if not result:
        return await send_error(callback_query.message)

    await EditDraw.ChooseField.set()
    await Router.state_menu(callback_query, state, lang)


async def no_delete_follow_link_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await callback_query.answer(await f("action cancel text", lang))

    await EditDraw.ChooseField.set()
    await Router.state_menu(callback_query, state, lang)


async def mailing_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        lang: str
):
    await DrawMailingState.CreateMessage.set()
    await Router.state_menu(callback_query, state, lang)


def register_edit_draw_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        create_draw_button_handler,
        callback_mode="create_draw",
        chat_type="private",
        state=EditDraw.ChooseDraw,
    )

    dp.register_callback_query_handler(
        choose_draw_button_handler,
        callback_mode="choose_draw",
        chat_type="private",
        state=EditDraw.ChooseDraw,
    )

    dp.register_callback_query_handler(
        edit_draw_button_handler,
        callback_mode=[
            "edit_name", "edit_media", "edit_description", "edit_terms",
            "edit_time_spending", "edit_channels_for_subscription",
            "edit_channel_to_invite_friends", "edit_count_invite_friends", "edit_follow_link", "edit_count_followed_link",
            "edit_channel_for_writing_messages", "edit_count_needed_messages",
            "edit_instructions_for_writing_messages", "edit_filter_words_message",
            "edit_is_public", "edit_contact", "edit_count_notifications",
        ],
        chat_type="private",
        state=EditDraw.ChooseField,
    )

    dp.register_callback_query_handler(
        count_button_handler,
        callback_mode="set_count",
        chat_type="private",
        state=[
            EditDraw.CountInviteFriends,
            EditDraw.CountFollowedLink,
            EditDraw.CountNeededMessages,
            EditDraw.CountAwardCategories,
            EditDraw.AwardCategoryCountWinners,
            EditDraw.CountNotifications,
        ],
    )

    dp.register_callback_query_handler(
        draw_results_type_button_handler,
        callback_mode="draw_results_type",
        chat_type="private",
        state=EditDraw.IsPublic,
    )

    dp.register_callback_query_handler(
        draw_change_awards_button_handler,
        callback_mode="edit_change_awards",
        chat_type="private",
        state=EditDraw.ChooseField,
    )

    dp.register_callback_query_handler(
        follow_link_button_handler,
        callback_mode="follow_link",
        chat_type="private",
        state=EditDraw.FollowLink,
    )

    dp.register_callback_query_handler(
        draw_delete_button_handler,
        callback_mode="delete_draw",
        chat_type="private",
        state=[EditDraw.ChooseField, DrawStatistics.ShowResults],
    )

    dp.register_callback_query_handler(
        yes_delete_draw_button_handler,
        yes_button=True,
        chat_type="private",
        state=EditDraw.DeleteDraw,
    )

    dp.register_callback_query_handler(
        no_delete_draw_button_handler,
        no_button=True,
        chat_type="private",
        state=EditDraw.DeleteDraw,
    )

    dp.register_callback_query_handler(
        draw_members_list_button_handler,
        callback_mode="show_members",
        chat_type="private",
        state=EditDraw.SelectMenu,
    )

    dp.register_callback_query_handler(
        follow_link_delete_button_handler,
        callback_mode="delete_follow_link",
        chat_type="private",
        state=EditDraw.FollowLink,
    )

    dp.register_callback_query_handler(
        yes_delete_follow_link_button_handler,
        yes_button=True,
        chat_type="private",
        state=EditDraw.DeleteFollowLink,
    )

    dp.register_callback_query_handler(
        no_delete_follow_link_button_handler,
        no_button=True,
        chat_type="private",
        state=EditDraw.DeleteFollowLink,
    )

    dp.register_callback_query_handler(
        mailing_button_handler,
        callback_mode="draw_mailing",
        chat_type="private",
        state=EditDraw.ChooseDraw,
    )
