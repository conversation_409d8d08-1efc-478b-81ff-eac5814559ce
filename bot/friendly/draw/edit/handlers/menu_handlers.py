from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from utils.router import Router

from ..states import EditDraw


async def draws_list_button_handler(message: types.Message, state: FSMContext, lang: str):
    await EditDraw.first()
    await Router.state_menu(message, state, lang)


def register_edit_draw_menu_handlers(dp: Dispatcher):
    dp.register_message_handler(
        draws_list_button_handler,
        lequal="draws list button",
        chat_type="private",
        state="*",
    )
