from aiogram.dispatcher import FSMContext

from db.models import Draw

from utils.redefined_classes import InlineKb, InlineBtn
from psutils.convertors import datetime_to_str
from utils.text import f, c

from utils.keyboards import active_button, get_navigation_keyboard


async def get_edit_draw_keyboard(draw: Draw, lang: str) -> InlineKb:
    is_simple_mode = draw.is_simple_mode

    keyboard = InlineKb(row_width=2)

    button_text = await f("draw name button", lang, draw_name=draw.name)
    callback_data = "edit_name"
    keyboard.add(InlineBtn(button_text, callback_data=callback_data))

    keys = [
        "media", "description", "terms",
    ]
    for edit_type in keys:
        button_text = await f(f"draw {edit_type} button", lang)
        callback_data = f"edit_{edit_type}"
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    time_spending = datetime_to_str(draw.time_spending)
    button_text = await f("draw time spending button", lang, current_time_spending=time_spending)
    callback_data = "edit_time_spending"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    count = len(draw.channels_for_subscription)
    button_text = await f("draw channels for subscription button", lang, count=count)
    if count > 0:
        button_text = await active_button(lang, button_text)
    callback_data = "edit_channels_for_subscription"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw channel to invite friends button", lang)
    if draw.channel_to_invite_friends:
        button_text = await active_button(lang, button_text)
    callback_data = "edit_channel_to_invite_friends"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw count invite friends button", lang, count=draw.count_invite_friends)
    callback_data = "edit_count_invite_friends"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw follow link button", lang)
    if draw.follow_link:
        button_text = await active_button(lang, button_text)
    callback_data = "edit_follow_link"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw count followed link button", lang, count=draw.count_followed_link)
    callback_data = "edit_count_followed_link"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw channel for writing messages button", lang)
    if draw.channel_for_writing_messages:
        button_text = await active_button(lang, button_text)
    callback_data = "edit_channel_for_writing_messages"
    keyboard.add(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw count needed messages button", lang, count=draw.count_needed_messages)
    callback_data = "edit_count_needed_messages"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    for edit_type in ["instructions_for_writing_messages", "filter_words_message",]:
        button_text = await f(f"draw {edit_type} button", lang)
        callback_data = f"edit_{edit_type}"
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    public = await f("public results draw button", lang)
    anonymous = await f("anonymous results draw button", lang)
    button_text = public if draw.is_public else anonymous
    callback_data = "edit_is_public"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    for edit_type in ["change_awards", "contact",]:
        button_text = await f(f"draw {edit_type} button", lang)
        if is_simple_mode and edit_type == "change_awards":
            count = draw.award_categories[0].count_winners if len(draw.award_categories) > 0 else 0
            button_text = await f("draw count winners button", lang, count=count)
        callback_data = f"edit_{edit_type}"
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw count notifications button", lang, count=draw.count_notifications)
    callback_data = "edit_count_notifications"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw delete button", lang)
    callback_data = c("delete_draw", draw_id=draw.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return await get_navigation_keyboard(lang, keyboard)


async def get_select_draw_keyboard(draw: Draw, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=2)

    members_count = len(draw.results)
    button_text = await f("draw info members button", lang, members_count=members_count)
    callback_data = "show_members"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("draw settings button", lang)
    callback_data = c("draw_settings", draw_id=draw.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return await get_navigation_keyboard(lang, keyboard)
