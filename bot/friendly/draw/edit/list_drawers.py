from typing import Dict, Any, List, Literal

from aiogram.dispatcher import FSMContext

from db.models import Draw, User

from utils.keyboards import previous_button

from psutils.convertors import datetime_to_str
from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn
from utils.router.route_handlers import BaseListDrawer

from friendly.draw.edit.db_funcs import get_draws


class DrawListDrawer(BaseListDrawer):

    row_width = 1

    config_page_size_variable_name = "FRIENDLY_DRAW_LIST_PAGE_SIZE"

    need_check_selected = False
    need_previous_button = True
    need_setup_pagination_handler = True
    need_setup_search_handler = True

    message_text_variable = "pick draw"
    empty_text_variable = "draw list empty"

    pagination_callback_mode = "friendly_draw_pagination"

    @classmethod
    async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
        return {}

    @classmethod
    async def make_get_objects_kwargs(
        cls,
        user: User,
        search_text: str,
        data_from_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        data = data_from_state.copy()
        data.update(user_id=user.id)
        data.update(search_text=search_text)
        return data

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: Literal["all", "count"] = "all"
    ) -> List[Draw]:
        user_id = get_objects_kwargs.get("user_id")
        search_text = get_objects_kwargs.get("search_text")

        draws = await get_draws(user_id, search_text=search_text, position=position, limit=limit, operation=operation)
        return draws

    @classmethod
    async def object_drawer(
        cls,
        draw: Draw,
        keyboard: InlineKb, lang: str,
        **kwargs
    ):
        icon = ""
        time_spending = datetime_to_str(draw.time_spending)

        if draw.status == "active":
            icon = await f("draw active icon", lang)
        elif draw.status == "stopped":
            icon = await f("draw stopped icon", lang)

        button_text = await f("draw name with time spending button", lang, icon=icon, draw_name=draw.name, time_spending=time_spending)
        callback_data = c("choose_draw", draw_id=draw.id)
        button = InlineBtn(button_text, callback_data=callback_data)
        keyboard.insert(button)

    @classmethod
    async def header_drawer(cls, keyboard: InlineKb, lang: str):
        button_text = await f("create draw list button", lang)
        callback_data = "create_draw"
        keyboard.row(
            InlineBtn(button_text, callback_data=callback_data),
            InlineBtn(await f("mailing button", lang), callback_data="draw_mailing")
        )

    @classmethod
    async def footer_drawer(cls, keyboard: InlineKb, lang: str):
        keyboard.row(await previous_button(lang))
