from utils.keyboards import get_yes_or_no_keyboard

from friendly.draw.edit.keyboards import get_edit_draw_keyboard, get_select_draw_keyboard

from friendly.draw.edit.list_drawers import Draw<PERSON>ist<PERSON>rawer

from friendly.draw.edit.states import EditDraw

from friendly.draw.routes import *


async def send_select_draw_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    message_text = await f("select menu draw header", lang, draw_name=draw.name)
    keyboard = await get_select_draw_keyboard(draw, lang)

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


async def send_edit_draw_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "edit"):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    message_text = await f("edit draw header", lang, draw_name=draw.name)
    keyboard = await get_edit_draw_keyboard(draw, lang)

    new = True if mode == "new" else False
    return await send_or_edit_message(message, message_text, keyboard=keyboard, new=new)


async def send_delete_draw_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    draw = await Draw.get(state_data.get("draw_id"))

    message_text = await f("delete draw confirmation header", lang, draw_name=draw.name)
    keyboard = await get_yes_or_no_keyboard(lang)

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


async def send_delete_follow_link_menu(message: types.Message, lang: str):
    message_text = await f("delete follow link confirmation header", lang)
    keyboard = await get_yes_or_no_keyboard(lang)

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


def register_edit_draw_routes(router: Router):
    router.add_route(EditDraw.ChooseDraw, DrawListDrawer())
    router.add_route(EditDraw.SelectMenu, send_select_draw_menu)
    router.add_route(EditDraw.ChooseField, send_edit_draw_menu)
    router.add_route(EditDraw.DeleteDraw, send_delete_draw_menu)
    router.add_route(EditDraw.DeleteFollowLink, send_delete_follow_link_menu)

    router.add_route(EditDraw.Name, send_enter_draw_name_menu)
    router.add_route(EditDraw.Media, send_enter_draw_media_menu)
    router.add_route(EditDraw.Description, send_enter_draw_description_menu)
    router.add_route(EditDraw.Terms, send_enter_draw_terms_menu)
    router.add_route(EditDraw.TimeSpending, send_enter_draw_time_spending_menu)
    router.add_route(EditDraw.ChannelsForSubscription, send_enter_draw_channels_for_subscription_menu)
    router.add_route(EditDraw.ChannelToInviteFriends, send_enter_draw_channel_to_invite_friends_menu)
    router.add_route(EditDraw.CountInviteFriends, send_enter_draw_count_invite_friends_menu)
    router.add_route(EditDraw.FollowLink, send_enter_draw_follow_link_menu)
    router.add_route(EditDraw.CountFollowedLink, send_enter_draw_count_followed_link_menu)
    router.add_route(EditDraw.ChannelForWritingMessages, send_enter_draw_channel_for_writing_messages_menu)
    router.add_route(EditDraw.CountNeededMessages, send_enter_draw_count_needed_messages_menu)
    router.add_route(EditDraw.InstructionsForWritingMessages, send_enter_draw_instructions_for_writing_messages_menu)
    router.add_route(EditDraw.FilterWordsMessage, send_enter_draw_filter_words_message_menu)
    router.add_route(EditDraw.IsPublic, send_enter_draw_is_public_menu)
    router.add_route(EditDraw.CountAwardCategories, send_enter_draw_count_award_categories_menu)
    router.add_route(EditDraw.AwardCategoryName, send_enter_draw_award_category_name_menu)
    router.add_route(EditDraw.AwardCategoryCountWinners, send_enter_draw_award_category_count_winners_menu)
    router.add_route(EditDraw.AwardCategoryAward, send_enter_draw_award_category_award_menu)
    router.add_route(EditDraw.Contact, send_enter_draw_contact_menu)
    router.add_route(EditDraw.CountNotifications, send_enter_draw_count_notifications_menu)
