from db import DBSession

from .notifications.workers import run_draws_notifications
from .winners.workers import run_draws

from utils.processes_manager.background_worker import LoopBackgroundWorker


class DrawsWorker(LoopBackgroundWorker):
    DEFAULT_NAME = "draws"
    DEFAULT_TIMEOUT = 60

    async def iteration(self):
        with DBSession():
            await run_draws()
            await run_draws_notifications()
