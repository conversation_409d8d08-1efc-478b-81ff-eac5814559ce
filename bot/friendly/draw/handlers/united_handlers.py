from datetime import datetime
import os

from aiogram import types

import config as cfg

from db.models import Draw, DrawResult

from friendly.draw.functions import get_conditions_progress_text, process_photo
from friendly.draw.keyboards import get_conditions_update_info_keyboard

from utils.media import get_media
from utils.text import f

import qrcode


async def update_conditions_info_button(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        lang: str
):
    draw = await Draw.get(callback_data.get("draw_id"))
    draw_result = await DrawResult.get(callback_data.get("draw_result"))

    conditions_progress_text = await get_conditions_progress_text(callback_query.message, draw, draw_result, lang)
    conditions_progress_keyboard = await get_conditions_update_info_keyboard(draw, draw_result, lang)

    try:
        await callback_query.message.edit_text(conditions_progress_text, reply_markup=conditions_progress_keyboard)
    except:
        alert_text = await f("progress conditions alert", lang)
        await callback_query.answer(alert_text)


async def get_followed_link_message_button(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        lang: str
):
    draw = await Draw.get(callback_data.get("draw_id"))
    draw_result = await DrawResult.get(callback_data.get("draw_result"))

    photo_path = draw.media_path

    if photo_path and not photo_path.startswith(cfg.STATIC_DB):
        photo_path = os.path.join(cfg.STATIC_DB, photo_path)

    draw_result_code = draw_result.id
    qrcode_photo_path = f"{draw_result_code}__{datetime.timestamp(datetime.utcnow())}.jpg"
    qrcode_photo_path = os.path.join(cfg.STATIC_DB, "qrcodes", qrcode_photo_path)

    qr_code = qrcode.make(draw_result.user_link)
    qr_code.save(qrcode_photo_path)

    content_type = draw.content_type
    if content_type == "photo":
        photo_path = process_photo(photo_path, qrcode_photo_path, "center")
    else:
        photo_path = qrcode_photo_path

    text = await f("followed link text", link=draw_result.user_link, lang=lang)
    await callback_query.message.answer_photo(photo=get_media(photo_path), caption=text)
