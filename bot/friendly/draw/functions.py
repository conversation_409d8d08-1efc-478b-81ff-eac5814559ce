import os
from datetime import datetime

from PIL import Image
from aiogram import types
from aiogram.dispatcher import FSMContext

import config as cfg
from core.helpers import get_crm_chat_link
from db import crud

from db.models import Draw, User, DrawResult, Channel, ChatMember

from utils.text import f

from utils.message import send_tg_message

from friendly.channel.edit.functions import is_bot_admin_in_chat_subscription

from friendly.draw.keyboards import get_conditions_update_info_keyboard


async def check_and_get_channel(channel_username: str):
    if channel_username[0] == "@":
        channel_username = channel_username[1:]

    if "t.me" in channel_username:
        channel_username = channel_username.rsplit("/", maxsplit=1)[-1]

    channel = await Channel.get(username=channel_username)
    if channel and await is_bot_admin_in_chat_subscription(channel.chat_id):
        return channel.id
    return None


async def check_and_get_contact(contact: str):
    if contact[0] == "@":
        contact = contact[1:]

    if "t.me" in contact:
        contact = contact.rsplit("/", maxsplit=1)[-1]

    user = await User.get_by_username(contact)
    return user


async def clear_state_from_awards(state: FSMContext):
    clear_keys = [
        "channels_for_subscription", "award_category_name",
        "award_category_count_winners", "award_category_award",
    ]

    async with state.proxy() as state_data:
        for clear_list in clear_keys:
            state_data.pop(clear_list, [])


def get_user_link(draw: Draw, user: User) -> str:
    link = f"t.me/{draw.bot.username}?start=draw-{draw.id}_user-{user.chat_id}"
    return link


async def is_user_in_chat(message: types.Message, channel: Channel, draw_result: DrawResult):
    bot = message.bot
    user_info = await bot.get_chat_member(channel.chat_id, draw_result.user.chat_id)
    if user_info.status in ["member", "creator"]:
        return True
    return False


async def get_follow_friends_text(draw: Draw, draw_result: DrawResult, lang: str):
    # Условие 1 (Приглашение друзей на розыгрыш)

    if not draw.count_followed_link:
        return ""

    condition_status_true = await f("condition status true text", lang)
    condition_status_false = await f("condition status false text", lang)

    follow_users = len(draw_result.follow_users)
    if follow_users < draw.count_followed_link:
        follow_friends_status = condition_status_false
    else:
        follow_friends_status = condition_status_true

    follow_friends_text = await f(
        "follow a friends text",
        status=follow_friends_status,
        x=follow_users,
        y=draw.count_followed_link,
        lang=lang
    )

    return follow_friends_text


async def get_channels_for_subscription_text(message: types.Message, draw: Draw, draw_result: DrawResult, lang: str):
    # Условие 2 (Подписка на каналы)

    bot = message.bot

    channels_list = draw.channels_for_subscription
    if not channels_list:
        return ""

    channels_for_subscription_text = ""

    subscription_status_true = await f("subscription status true", lang)
    subscription_status_false = await f("subscription status false", lang)
    condition_status_true = await f("condition status true text", lang)
    condition_status_false = await f("condition status false text", lang)

    subscriptions_status = True
    with bot.with_token(draw.created_by_bot.token):
        for channel in channels_list:
            user_in_chat = await is_user_in_chat(message, channel, draw_result)
            if user_in_chat:
                subscription_status = subscription_status_true
            else:
                subscription_status = subscription_status_false
                subscriptions_status = False
            channels_for_subscription_text += await f(
                "channel for subscription text",
                username=channel.username,
                status=subscription_status,
                lang=lang
            )
    if subscriptions_status:
        subscriptions_status = condition_status_true
    else:
        subscriptions_status = condition_status_false

    channels_for_subscription_text = await f(
        "channels for subscription text",
        status=subscriptions_status,
        channels_info=channels_for_subscription_text,
        lang=lang
    )
    return channels_for_subscription_text


async def get_chat_send_messages_text(draw: Draw, draw_result: DrawResult, lang: str):
    # Условие 3 (Написание сообщений в канал)
    if not draw.channel_for_writing_messages:
        return ""

    writing_messages = draw_result.count_writing_messages

    condition_status_true = await f("condition status true text", lang)
    condition_status_false = await f("condition status false text", lang)

    condition_status = condition_status_true
    if writing_messages < draw.count_needed_messages:
        condition_status = condition_status_false

    instructions_msg = draw.instructions_for_writing_messages
    chat_send_messages_text = await f(
        "chat send messages text",
        status=condition_status,
        chat_link=f"https://t.me/{draw.channel_for_writing_messages.username}",
        count_messages=draw.count_needed_messages,
        send_messages=writing_messages,
        instructions_for_writing_messages=instructions_msg if instructions_msg else "",
        lang=lang
    )

    return chat_send_messages_text


async def get_invite_friends_text(draw: Draw, draw_result: DrawResult, lang: str):
    # Условие 4 (Приглашение друзей в канал)
    if not draw.channel_to_invite_friends:
        return ""

    condition_status_true = await f("condition status true text", lang)
    condition_status_false = await f("condition status false text", lang)

    invite_friends_status = condition_status_true
    invite_friends = draw_result.count_invite_friends
    if invite_friends < draw.count_invite_friends:
        invite_friends_status = condition_status_false

    invite_friends_text = await f(
        "invite a friends text",
        status=invite_friends_status,
        x=invite_friends,
        y=draw.count_invite_friends,
        chat_link=f"https://t.me/{draw.channel_to_invite_friends.username}",
        lang=lang
    )

    return invite_friends_text


async def get_conditions_progress_text(message: types.Message, draw: Draw, draw_result: DrawResult, lang: str):

    # Условие 1 (Приглашение друзей на розыгрыш)
    follow_friends_text = await get_follow_friends_text(draw, draw_result, lang)

    # Условие 2 (Подписка на каналы)
    channels_for_subscription_text = await get_channels_for_subscription_text(message, draw, draw_result, lang)

    # Условие 3 (Написание сообщений в канал)
    chat_send_messages_text = await get_chat_send_messages_text(draw, draw_result, lang)

    # Условие 4 (Приглашение друзей в канал)
    invite_friends_text = await get_invite_friends_text(draw, draw_result, lang)

    # Текст об анонимности розыгрыша
    if draw.is_public:
        is_public_text_var = "draw not anonymous text"
    else:
        is_public_text_var = "draw anonymous text"

    is_public_text = await f(
        is_public_text_var,
        num=draw_result.id,
        date=draw.time_spending.strftime(cfg.DATETIME_SHORT_FORMAT),
        admin=draw.contact.username,
        lang=lang
    )

    # Итоговое сообщение
    conditions_progress_text = await f(
        "draw conditions text",
        condition1=follow_friends_text,
        condition2=channels_for_subscription_text,
        condition3=chat_send_messages_text,
        condition4=invite_friends_text,
        is_public_text=is_public_text,
        lang=lang
    )

    while "\n\n\n" in conditions_progress_text:
        conditions_progress_text = conditions_progress_text.replace("\n\n\n", "\n\n")

    return conditions_progress_text


async def cmd_start_draw(message: types.Message, user: User, lang: str):
    draw_id = int(message.text.split("draw-")[1].split("_user")[0])  # id розыгрыша

    # id юзера который пригласил на розыгрыш
    inviter_id = int(message.text.split("user-")[1]) if "user" in message.text else None
    draw = await Draw.get(draw_id)  # розыгрыш

    user_link = get_user_link(draw, user)  # Ссылка нового учасника для приглашения новых учасников

    # Создание нового учасника
    new_draw_result = await DrawResult.get(user_link=user_link)
    if not new_draw_result:
        new_draw_result = await DrawResult.get_or_create(draw_id=draw_id, user_id=user.id, user_link=user_link)
        if inviter_id:
            inviter = await User.get(chat_id=inviter_id)  # Пользователь который пригласил
            inviter_result = await DrawResult.get(
                draw_id=draw_id, user_id=inviter.id
            )  # Учасник розыгрыша который пригласил
            await inviter_result.add_follow_user(user)

    draw_terms_text = await f("draw terms text", terms=draw.terms, lang=lang)
    draw_text = await f("draw text", title=draw.name, description=draw.description, terms=draw_terms_text, lang=lang)
    kwargs = dict()
    content_type = draw.content_type
    if content_type != "text":
        kwargs = dict({content_type: draw.media_path})

    await send_tg_message(message.chat.id, content_type, text=draw_text, **kwargs)

    conditions_progress_text = await get_conditions_progress_text(message, draw, new_draw_result, lang)
    conditions_progress_keyboard = await get_conditions_update_info_keyboard(draw, new_draw_result, lang)

    await message.answer(conditions_progress_text, reply_markup=conditions_progress_keyboard)


async def draw_user_write_message_channel(message: types.Message, user: User):
    user_draws = await DrawResult.get_draws_by_users(user.id)
    for draw_result in user_draws:
        draw = draw_result.draw
        if draw.channel_for_writing_messages:
            if draw.filter_words_message:
                filter_results = list(map(lambda word: bool(word in message.text), draw.filter_words_message))
                if all(filter_results):
                    await draw_result.write_message()
            else:
                await draw_result.write_message()


async def draw_member_joined(
        update: types.ChatMemberUpdated,
        channel: Channel,
        user_entered: User,
        inviter: ChatMember,
        is_existing: bool
):
    if update.from_user.id != update.new_chat_member.user.id:  # пользователь вошёл по приглашению
        user_draws = await DrawResult.get_draws_by_users(inviter.user_id)
        for draw_result in user_draws:
            draw = draw_result.draw
            if channel.id == draw.channel_to_invite_friends_id:
                if not is_existing:
                    await draw_result.add_invite_friend(user_entered)


async def get_service_to_user_link(draw_result: DrawResult) -> str:
    chat = await crud.get_or_create_chat_by_draw_result(draw_result)
    return get_crm_chat_link(chat.id)


def get_percent_of_num(num, percent):
    return int(num / 100 * percent)


def get_position(position, photo_size, qr_code_width):
    padding_percent = 5
    width, height = photo_size
    padding_pixels_x = get_percent_of_num(width, padding_percent)
    padding_pixels_y = get_percent_of_num(height, padding_percent)
    positions = {
        "left_top":
            (padding_pixels_x, padding_pixels_y),
        "top_center":
            (int((width / 2) - (qr_code_width / 2)), padding_pixels_y),
        "right_top":
            (width - qr_code_width - padding_pixels_x, padding_pixels_y),

        "left_center":
            (padding_pixels_x, int((height / 2) - (qr_code_width / 2))),
        "center":
            (int((width / 2) - (qr_code_width / 2)), int((height / 2) - (qr_code_width / 2))),
        "right_center":
            (height - qr_code_width - padding_pixels_x, int((height / 2) - (qr_code_width / 2))),

        "left_bottom":
            (padding_pixels_x, height - padding_pixels_y - qr_code_width),
        "bottom_center":
            (int((width / 2) - (qr_code_width / 2)), height - padding_pixels_y - qr_code_width),
        "right_bottom":
            (width - padding_pixels_x - qr_code_width, height - padding_pixels_y - qr_code_width),
    }
    return positions.get(position)


def process_photo(photo_path: str, qrcode_photo_path: str, position: str):
    if not photo_path:
        photo = Image.open(qrcode_photo_path)
    else:
        photo = Image.open(photo_path)
        width, height = photo.size

        qr_code = Image.open(qrcode_photo_path)
        width_and_height_qr = qr_code.width
        width_new = int(width_and_height_qr * 2)
        height_new = int(height * width_new / width)

        photo = photo.resize((width_new, height_new))
        photo.paste(qr_code, get_position(position, photo.size, width_and_height_qr))

    new_photo_path = f"{datetime.utcnow().timestamp()}.jpg"
    new_photo_path = os.path.join(cfg.STATIC_DB, "photos", new_photo_path)
    photo.save(new_photo_path)
    return new_photo_path
