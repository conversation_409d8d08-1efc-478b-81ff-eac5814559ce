from typing import Dict, Any, List, Literal

from aiogram.dispatcher import FSM<PERSON>ontext

from db.models import Draw, DrawR<PERSON>ult, User

from utils.keyboards import previous_button

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn
from utils.router.route_handlers import BaseListDrawer

from friendly.draw.functions import get_service_to_user_link

from friendly.draw.statistics import get_progress_status


class MembersListDrawer(BaseListDrawer):

    row_width = 2

    config_page_size_variable_name = "FRIENDLY_DRAW_LIST_PAGE_SIZE"

    need_check_selected = False
    need_previous_button = True
    need_setup_pagination_handler = True
    need_setup_search_handler = False

    message_text_variable = "draw members list"
    empty_text_variable = "draw members list empty"

    pagination_callback_mode = "friendly_draw_pagination"

    @classmethod
    async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
        state_data = await state.get_data()
        draw_id = state_data.get("draw_id")
        members_from_edit = state_data.get("members_from_edit", False)
        return {"draw_id": draw_id, "members_from_edit": members_from_edit}

    @classmethod
    async def make_get_objects_kwargs(
        cls,
        user: User,
        search_text: str,
        data_from_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        data = data_from_state.copy()
        return data

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: Literal["all", "count"] = "all"
    ) -> List[DrawResult]:
        draw_id = get_objects_kwargs.get("draw_id")

        draw = await Draw.get(draw_id)
        results = draw.results

        return len(results) if operation == "count" else results

    @classmethod
    async def object_drawer(
        cls,
        draw_result: DrawResult,
        keyboard: InlineKb, lang: str,
        members_from_edit: bool = False,
        **kwargs
    ):
        member = draw_result.user.name if draw_result.draw.is_public or members_from_edit else f"{draw_result.id}"
        button_text = await f("draw member button", lang, draw_member=member)
        url = get_service_to_user_link(draw_result)
        button = InlineBtn(button_text, url=url)
        keyboard.add(button)

        count_statuses_successfully, total_statuses = get_progress_status(draw_result)
        button_text = await f("draw member status button", lang, statuses_successfully=count_statuses_successfully, total_statuses=total_statuses)
        callback_data = c("show_details", draw_result_id=draw_result.id)
        button = InlineBtn(button_text, callback_data=callback_data)
        keyboard.insert(button)

    @classmethod
    async def footer_drawer(cls, keyboard: InlineKb, lang: str):
        keyboard.row(await previous_button(lang))

    @classmethod
    async def header_drawer(cls, keyboard: InlineKb, lang: str):
        button_text = await f("mailing button", lang)
        callback_data = "draw_mailing"
        keyboard.row(InlineBtn(button_text, callback_data=callback_data))
