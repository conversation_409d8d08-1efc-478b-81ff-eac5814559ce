from typing import <PERSON><PERSON>

from aiogram.dispatcher import FSMContext

from db.models import Draw, DrawResult, User


async def list_users_in_draw(draw: Draw) -> list[User]:
    return [result.user for result in draw.results]


async def count_followed_link_users_in_draw(draw: Draw) -> int:
    return sum([result.count_follow_users for result in draw.results])


async def list_users_with_all_conditions(draw: Draw) -> list[User]:
    return [result.user for result in await list_draw_results_with_all_conditions(draw)]


async def list_draw_results_with_all_conditions(draw: Draw) -> list[DrawResult]:
    results = list()
    for result in draw.results:
        status = check_all_statuses(result)
        if status:
            results.append(result)
    return results


def channels_subscripted_status(result: DrawResult) -> bool:
    return bool(result.count_channels_subscripted == result.draw.count_channels_for_subscription)


def invite_friends_status(result: DrawResult) -> bool:
    return bool(result.count_invite_friends == result.draw.count_invite_friends)


def follow_link_status(result: DrawResult) -> bool:
    return bool(result.count_follow_users == result.draw.count_followed_link)


def writing_messages_status(result: DrawResult) -> bool:
    return bool(result.count_writing_messages == result.draw.count_needed_messages)


def check_all_statuses(result: DrawResult) -> bool:
    channels_subscripted_status_ = channels_subscripted_status(result)
    invite_friends_status_ = invite_friends_status(result)
    follow_link_status_ = follow_link_status(result)
    writing_messages_status_ = writing_messages_status(result)

    return all([channels_subscripted_status_, invite_friends_status_, follow_link_status_, writing_messages_status_])


def get_progress_status(result: DrawResult) -> Tuple[int, int]:
    draw = result.draw
    total_statuses: int = 0
    progress_statuses = list()

    if draw.count_channels_for_subscription > 0:
        total_statuses += 1
        progress_statuses.append(channels_subscripted_status(result))
    if draw.channel_to_invite_friends_id:
        total_statuses += 1
        progress_statuses.append(invite_friends_status(result))
    if draw.follow_link:
        total_statuses += 1
        progress_statuses.append(follow_link_status(result))
    if draw.channel_for_writing_messages_id:
        total_statuses += 1
        progress_statuses.append(writing_messages_status(result))

    count_statuses_successfully = len([status for status in progress_statuses if status])
    return count_statuses_successfully, total_statuses


def channels_subscripted_progress(result: DrawResult) -> str:
    return f"{result.count_channels_subscripted}/{result.draw.count_channels_for_subscription}"


def invite_friends_progress(result: DrawResult) -> str:
    return f"{result.count_invite_friends}/{result.draw.count_invite_friends}"


def follow_link_progress(result: DrawResult) -> bool:
    return f"{result.count_follow_users}/{result.draw.count_followed_link}"


def writing_messages_progress(result: DrawResult) -> bool:
    return f"{result.count_writing_messages}/{result.draw.count_needed_messages}"
