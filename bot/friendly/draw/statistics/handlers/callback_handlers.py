from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import Draw, DrawResult

from utils.router import Router

from friendly.draw.functions import get_conditions_progress_text

from friendly.draw.edit.states import EditDraw
from friendly.draw.mailing.states import DrawM<PERSON>State
from friendly.draw.statistics.states import DrawStatistics
from friendly.draw.winners.states import DrawWinners


async def show_members_draw_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    await state.update_data(**callback_data)

    await DrawStatistics.ShowMembers.set()
    await Router.state_menu(callback_query, state)


async def show_winners_draw_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    await state.update_data(**callback_data)

    await DrawWinners.ShowWinners.set()
    await Router.state_menu(callback_query, state)


async def show_settings_draw_button_handler(
        callback_query: types.CallbackQuery,
        state: FSM<PERSON>ontext,
        callback_data: dict
):
    await state.update_data(**callback_data)

    await EditDraw.ChooseField.set()
    await Router.state_menu(callback_query, state)


async def show_details_draw_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
        lang: str
):
    draw_result = await DrawResult.get(callback_data.get("draw_result_id"))
    conditions_text = await get_conditions_progress_text(callback_query.message, draw_result.draw, draw_result, lang)

    await callback_query.message.answer(conditions_text)
    await Router.state_menu(callback_query, state)


async def mailing_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        lang: str
):
    state_data = await state.get_data()
    draw_id = state_data.get("draw_id")
    draw = await Draw.get(draw_id)

    await state.update_data(sender_group_id=draw.bot.group.id)
    await state.update_data(sender_draw_id=draw_id, is_groups_skipped=True)

    await DrawMailingState.CreateMessage.set()
    await Router.state_menu(callback_query, state, lang)


async def previous_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    cur_state = await state.get_state()
    state_data = await state.get_data()

    if cur_state == DrawStatistics.ShowResults.state:
        await EditDraw.first()
    elif state_data.get("members_from_edit", False):
        await EditDraw.SelectMenu.set()
    else:
        await DrawStatistics.ShowResults.set()
    await Router.state_menu(callback_query, state)


def register_statistics_draw_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        show_members_draw_button_handler,
        callback_mode="show_members",
        chat_type="private",
        state=DrawStatistics.ShowResults,
    )

    dp.register_callback_query_handler(
        show_winners_draw_button_handler,
        callback_mode="show_winners",
        chat_type="private",
        state=DrawStatistics.ShowResults,
    )

    dp.register_callback_query_handler(
        show_settings_draw_button_handler,
        callback_mode="draw_settings",
        chat_type="private",
        state=[
            EditDraw.SelectMenu,
            DrawStatistics.ShowResults,
        ],
    )

    dp.register_callback_query_handler(
        show_details_draw_button_handler,
        callback_mode="show_details",
        chat_type="private",
        state=DrawStatistics.ShowMembers,
    )

    dp.register_callback_query_handler(
        mailing_button_handler,
        callback_mode="draw_mailing",
        chat_type="private",
        state=DrawStatistics.ShowMembers,
    )

    dp.register_callback_query_handler(
        previous_button_handler,
        previous_button=True,
        chat_type="private",
        state=DrawStatistics,
    )
