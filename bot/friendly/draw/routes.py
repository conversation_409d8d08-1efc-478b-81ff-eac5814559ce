import asyncio
from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Channel, Draw, Award, AwardCategory, User

from utils.keyboards import get_navigation_keyboard

from psutils.fsm import get_field_from_state
from psutils.forms.helpers import save_messages_to_state

from psutils.convertors import datetime_to_str
from utils.message import send_tg_message, send_or_edit_message
from utils.redefined_classes import InlineKb, InlineBtn
from utils.text import f
from utils.router import Router

from friendly.draw.edit.functions import get_draw_link

from friendly.draw.keyboards import get_count_keyboard, get_follow_link_keyboard, get_draw_results_type_keyboard, get_advanced_mode_keyboard


async def base_send_enter_draw_menu(
        message: types.Message,
        state: FSMContext,
        lang: str,
        keyboard: InlineKb = None,
        need_next: bool = False,
        new: bool = False,
        kwargs: dict = None
):
    if not kwargs:
        kwargs = dict()
    field_name = await get_field_from_state(state)
    follow_link = None

    cur_state = await state.get_state()
    if cur_state.startswith("EditDraw"):
        need_next = False

        state_data = await state.get_data()
        draw = await Draw.get(state_data.get("draw_id"))
        keys_for_pass = ("is_public", "count_award_categories",)
        state_pass = True if field_name in keys_for_pass or field_name.startswith("award_category_") else False
        new = False if state_pass else True

        if not state_pass:
            message_text = await f("draw field current value text", lang)
            msg = await send_tg_message(message.chat.id, "text", text=message_text)
            await save_messages_to_state(state, [message.message_id, msg.message_id])

        if state_pass:
            pass
        elif field_name == "media":
            if draw.content_type != "text":
                content_type = draw.content_type
                kwargs = dict(content_type=content_type)
                kwargs.update({content_type: draw.media_path})
                msg = await send_tg_message(message.chat.id, **kwargs)
                await save_messages_to_state(state, msg)
        elif field_name == "time_spending":
            time_spending = datetime_to_str(draw.time_spending)
            msg = await send_tg_message(message.chat.id, "text", text=time_spending)
            await save_messages_to_state(state, msg)
        elif field_name == "channels_for_subscription":
            current_value = "\n".join([f"@{channel.username}" for channel in draw.channels_for_subscription])
            msg = await send_tg_message(message.chat.id, "text", text=current_value)
            await save_messages_to_state(state, msg)
        elif field_name.startswith("channel_"):
            channel = getattr(draw, field_name)
            if channel:
                msg = await send_tg_message(message.chat.id, "text", text=f"@{channel.username}")
                await save_messages_to_state(state, msg)
        elif field_name == "instructions_for_writing_messages":
            current_value = draw.instructions_for_writing_messages
            if current_value:
                msg = await send_tg_message(message.chat.id, "text", text=current_value)
                await save_messages_to_state(state, msg)
        elif field_name == "filter_words_message":
            current_value = ", ".join(draw.filter_words_message) if draw.filter_words_message else None
            if current_value:
                msg = await send_tg_message(message.chat.id, "text", text=current_value)
                await save_messages_to_state(state, msg)
        elif field_name == "contact":
            if draw.contact:
                msg = await send_tg_message(message.chat.id, "text", text=f"@{draw.contact.username}")
                await save_messages_to_state(state, msg)
        elif field_name == "follow_link":
            follow_link = await get_draw_link(message.chat.id, state, send_link=True)
            new = True
        else:
            current_value = getattr(draw, field_name)
            if isinstance(current_value, int):
                current_value = str(current_value)
            msg = await send_tg_message(message.chat.id, "text", text=current_value)
            await save_messages_to_state(state, msg)

    message_text = await f(f"enter draw {field_name} header", lang, **kwargs)
    keyboard = await get_navigation_keyboard(lang, keyboard=keyboard, need_next=need_next)

    if new:
        corro = message.answer(message_text, reply_markup=keyboard)
        results = await asyncio.gather(corro, message.delete())
        return results[0]

    try:
        return await message.edit_text(message_text, reply_markup=keyboard)
    except:
        pass


async def send_enter_draw_name_menu(message: types.Message, state: FSMContext, lang: str):
    return await base_send_enter_draw_menu(message, state, lang)


async def send_enter_draw_media_menu(message: types.Message, state: FSMContext, lang: str):
    return await base_send_enter_draw_menu(message, state, lang, need_next=True)


async def send_enter_draw_description_menu(message: types.Message, state: FSMContext, lang: str):
    return await base_send_enter_draw_menu(message, state, lang, need_next=True)


async def send_enter_draw_terms_menu(message: types.Message, state: FSMContext, lang: str):
    return await base_send_enter_draw_menu(message, state, lang)


async def send_enter_draw_time_spending_menu(message: types.Message, state: FSMContext, lang: str):
    return await base_send_enter_draw_menu(message, state, lang)


async def send_enter_draw_channels_for_subscription_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "edit"):
    state_data = await state.get_data()
    channels_for_subscription = state_data.get("channels_for_subscription", [])
    new = True if channels_for_subscription else True if mode == "new" else False
    return await base_send_enter_draw_menu(message, state, lang, need_next=True, new=new)


async def send_enter_draw_channel_to_invite_friends_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "edit"):
    new = True if mode == "new" else False
    return await base_send_enter_draw_menu(message, state, lang, need_next=True, new=new)


async def send_enter_draw_count_invite_friends_menu(message: types.Message, state: FSMContext, lang: str):
    keyboard = await get_count_keyboard()
    return await base_send_enter_draw_menu(message, state, lang, keyboard=keyboard, new=True)


async def send_enter_draw_follow_link_menu(message: types.Message, state: FSMContext, lang: str):
    keyboard = await get_follow_link_keyboard(state, lang)
    return await base_send_enter_draw_menu(message, state, lang, keyboard=keyboard, need_next=True)


async def send_enter_draw_count_followed_link_menu(message: types.Message, state: FSMContext, lang: str):
    keyboard = await get_count_keyboard()
    return await base_send_enter_draw_menu(message, state, lang, keyboard=keyboard, new=True)


async def send_enter_draw_channel_for_writing_messages_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    follow_link = state_data.get("follow_link")
    new = True if follow_link else False
    return await base_send_enter_draw_menu(message, state, lang, need_next=True, new=new)


async def send_enter_draw_count_needed_messages_menu(message: types.Message, state: FSMContext, lang: str):
    keyboard = await get_count_keyboard()
    return await base_send_enter_draw_menu(message, state, lang, keyboard=keyboard, new=True)


async def send_enter_draw_instructions_for_writing_messages_menu(message: types.Message, state: FSMContext, lang: str):
    return await base_send_enter_draw_menu(message, state, lang, need_next=True)


async def send_enter_draw_filter_words_message_menu(message: types.Message, state: FSMContext, lang: str):
    return await base_send_enter_draw_menu(message, state, lang, need_next=True)


async def send_enter_draw_is_public_menu(message: types.Message, state: FSMContext, lang: str):
    keyboard = await get_draw_results_type_keyboard(state, lang)
    return await base_send_enter_draw_menu(message, state, lang, keyboard=keyboard)


async def send_enter_draw_count_award_categories_menu(message: types.Message, state: FSMContext, lang: str, mode:str = "edit"):
    keyboard = await get_count_keyboard()
    keyboard = await get_advanced_mode_keyboard(lang, keyboard=keyboard)
    new = True if mode == "new" else False
    return await base_send_enter_draw_menu(message, state, lang, keyboard=keyboard, new=new)


async def send_enter_draw_award_category_name_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "edit"):
    state_data = await state.get_data()
    award_category_name = state_data.get("award_category_name", [])
    count_award_categories = state_data.get("count_award_categories")
    category_index = min(len(award_category_name) + 1, count_award_categories)

    kwargs = dict(category_index=category_index)
    keyboard = await get_advanced_mode_keyboard(lang)
    new = True if mode == "new" else False
    return await base_send_enter_draw_menu(message, state, lang, keyboard=keyboard, new=new, kwargs=kwargs)


async def send_enter_draw_award_category_count_winners_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "edit"):
    cur_state = await state.get_state()
    state_data = await state.get_data()
    is_simple_mode = state_data.get("is_simple_mode", True)
    count_award_categories = state_data.get("count_award_categories")

    award_category_name = state_data.get("award_category_name")
    award_category_count_winners = state_data.get("award_category_count_winners", [])
    category_index = len(award_category_count_winners)
    if category_index == count_award_categories:
        category_index = -1
    award_category_name = "" if is_simple_mode else award_category_name[category_index]
    kwargs = dict(award_category_name=award_category_name)

    keyboard = await get_count_keyboard()
    if cur_state.startswith("CreateDraw") and is_simple_mode:
        button_text = await f("draw advanced mode button", lang)
        callback_data = "advanced_mode"
        keyboard.row(InlineBtn(button_text, callback_data=callback_data))
    elif cur_state.startswith("CreateDraw") and not is_simple_mode:
        keyboard = await get_advanced_mode_keyboard(lang, keyboard=keyboard)

    new = True if mode == "new" else False
    return await base_send_enter_draw_menu(message, state, lang, keyboard=keyboard, new=new, kwargs=kwargs)


async def send_enter_draw_award_category_award_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "edit"):
    state_data = await state.get_data()
    count_award_categories = state_data.get("count_award_categories")

    award_category_name = state_data.get("award_category_name")
    award_category_award = state_data.get("award_category_award", [])
    category_index = len(award_category_award)
    if category_index == count_award_categories:
        category_index -= 1

    kwargs = dict(award_category_name=award_category_name[category_index])
    keyboard = await get_advanced_mode_keyboard(lang)
    new = True if mode == "new" else False
    return await base_send_enter_draw_menu(message, state, lang, keyboard=keyboard, new=new, kwargs=kwargs)


async def send_enter_draw_contact_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "edit"):
    new = True if mode == "new" else False
    return await base_send_enter_draw_menu(message, state, lang, new=new)


async def send_enter_draw_count_notifications_menu(message: types.Message, state: FSMContext, lang: str):
    keyboard = await get_count_keyboard()
    return await base_send_enter_draw_menu(message, state, lang, keyboard=keyboard, new=True)
