from utils.router import Router

from .channel.correspondences.routes import register_channel_correspondences_routes
from .channel.edit.routes import register_edit_channel_routes
from .channel.chat_menu_buttons.create.routes import register_create_channel_menu_buttons_routes
from .channel.chat_menu_buttons.edit.routes import register_edit_channel_menu_buttons_routes
from .channel.filters.routes import register_channel_filters_routes
from .channel.ratio.routes import register_channel_ratio_routes
from .channel.subscriptions.routes import register_channel_subscriptions_routes

from .draw.create.routes import register_create_draw_routes
from .draw.edit.routes import register_edit_draw_routes
from .draw.statistics.routes import register_statistics_draw_routes
from .draw.winners.routes import register_winners_draw_routes

from .footer.create.routes import register_create_footer_routes
from .footer.edit.routes import register_edit_footer_routes

from .interests.settings.routes import register_interests_settings_routes

from .poll.create.routes import register_create_poll_routes
from .poll.edit.routes import register_edit_poll_routes
from .poll.publish.routes import register_publish_poll_routes

from .schedule.copy_posts.routes import register_schedule_copy_posts_routes
from .schedule.create.routes import register_create_schedule_routes
from .schedule.delete.routes import register_delete_schedule_routes
from .schedule.edit.routes import register_edit_schedule_routes


def setup_router(router: Router):
    register_channel_correspondences_routes(router)
    register_edit_channel_routes(router)
    register_channel_filters_routes(router)
    register_channel_ratio_routes(router)
    register_channel_subscriptions_routes(router)
    register_create_channel_menu_buttons_routes(router)
    register_edit_channel_menu_buttons_routes(router)

    register_create_draw_routes(router)
    register_edit_draw_routes(router)
    register_statistics_draw_routes(router)
    register_winners_draw_routes(router)

    register_create_footer_routes(router)
    register_edit_footer_routes(router)

    register_interests_settings_routes(router)

    register_create_poll_routes(router)
    register_edit_poll_routes(router)
    register_publish_poll_routes(router)

    register_schedule_copy_posts_routes(router)
    register_create_schedule_routes(router)
    register_delete_schedule_routes(router)
    register_edit_schedule_routes(router)
