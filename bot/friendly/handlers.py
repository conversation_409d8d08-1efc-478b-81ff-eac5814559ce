import asyncio

from aiogram import <PERSON><PERSON>, Dispatcher
from aiohttp import web
from psutils.local import setup_psutils_localisation

from core.aiogram_middlewares import (
    ClientBotMiddleware, ExceptionHandlersMiddleware,
    LinkUncompressorMiddleware,
)
from core.aiogram_middlewares.cache_time import CacheTimeMiddleware
from core.aiogram_middlewares.callback_data import CallbackDataMiddleware
from core.aiogram_middlewares.create_or_update import CreateOrUpdateUserMiddleware
from core.chat.filters.aiogram import bind_chat_filters
from core.chat.handlers import *
from core.handlers import *
from core.kafka.producer import producer
from core.mailing.send_mailing_info_handler import register_mailing_info_button_handler
from core.share_bot.deep_link_handlers import register_recommend_bot_deep_link
from core.share_bot.menu_handlers import register_share_bot_handlers
from core.user.agreement_processor.buttons_handlers import \
    setup_tg_handlers as setup_agreements_handlers
from utils.filters.bind import bind_general_filters
from utils.router import Router
from .channel.add.handlers import *
from .channel.admins_synchronizer import synchronize_channels
from .channel.chat_menu_buttons.create.forms import AddChannelMenuButtonForm
from .channel.chat_menu_buttons.create.handlers.callback_handlers import \
    register_create_channel_menu_buttons_callback_handlers
from .channel.chat_menu_buttons.edit.forms import EditChannelMenuButtonForm
from .channel.chat_menu_buttons.edit.handlers.callback_handlers import \
    register_edit_channel_menu_buttons_callback_handlers
from .channel.correspondences.handlers import *
from .channel.correspondences.mailing import CorrespondencesMailing
from .channel.edit.handlers import *
from .channel.filters.handlers import *
from .channel.limitations.handlers import *
# Отложили функционал
# from .channel.limitations.workers import check_ban_rules_time_friendly_bot
from .channel.limitations.workers import channel_refresh_messages
from .channel.mailing import LocationMailing
from .channel.members.filters import bind_members_filters
from .channel.members.handlers import *
from .channel.message_deleter import delete_old_friendly_messages
from .channel.ratio.forms import SetMessageLimitForm
from .channel.ratio.handlers import *
from .channel.subscriptions.handlers import *
from .draw.create.forms import CreateDrawForm
from .draw.create.handlers import *
from .draw.edit.forms import EditDrawForm
from .draw.edit.handlers import *
from .draw.mailing import DrawMailing
from .draw.statistics.handlers import *
from .draw.winners.handlers import *
from .footer.create.forms import CreateFooterForm
from .footer.edit.forms import EditFooterForm
from .footer.edit.handlers import *
from .footer.handlers import *
from .interests.posts import send_interests_posts
from .interests.posts.handlers import *
from .interests.settings.handlers import *
from .main.handlers.group import *
from .main.handlers.private import *
from .main.handlers.web import *
from .poll.create.handlers import *
from .poll.edit.handlers import *
from .schedule.copy_posts.handlers import *
from .schedule.create.forms import CreateScheduleForm
from .schedule.create.handlers import *
from .schedule.delete.handlers import *
from .schedule.edit.forms import EditScheduleForm
from .schedule.edit.handlers import *
from .schedule.posts import stop_old_schedules


async def on_startup(dp: Dispatcher, bot_id: int = None):
    await setup_psutils_localisation()
    await producer.initialise()

    Dispatcher.set_current(dp)
    Bot.set_current(dp.bot)
    Router.set_current(dp["router"])

    loop = asyncio.get_event_loop()
    # Отложили функционал
    # loop.call_soon(asyncio.create_task, check_ban_rules_time_friendly_bot())

    loop.call_soon(asyncio.create_task, channel_refresh_messages())

    # ф-ция, которая отправляет посты интересов
    asyncio.ensure_future(send_interests_posts())

    # ф-ции, которые удаляют сообщения френдли бота в чатах
    asyncio.ensure_future(delete_old_friendly_messages())

    # ф-ция, которая обновляет статусы админов в канале
    asyncio.ensure_future(synchronize_channels())

    # ф-ция, которая останавливает устаревшие расписания
    asyncio.ensure_future(stop_old_schedules())

    if bot_id:
        await dp.reset_webhook()


def register_web_handlers(app: web.Application, dp: Dispatcher):
    register_main_web_handlers(app, dp)


def bind_filters(dp: Dispatcher):
    bind_members_filters(dp)
    bind_chat_filters(dp)
    bind_general_filters(dp)


def register_bot_handlers(dp: Dispatcher):
    Dispatcher.set_current(dp)

    dp.setup_middleware(ClientBotMiddleware())
    dp.setup_middleware(ExceptionHandlersMiddleware())
    dp.setup_middleware(CreateOrUpdateUserMiddleware())
    dp.setup_middleware(LinkUncompressorMiddleware())
    dp.setup_middleware(CallbackDataMiddleware())
    dp.setup_middleware(CacheTimeMiddleware())

    register_main_errors_handlers(dp)

    # хэндлеры в канале
    register_channel_limitations_channel_handlers(dp)

    # хэндлеры команд в группе
    register_channel_limitation_commands_handlers(dp)
    register_channel_limitations_message_handlers(dp)

    register_interests_posts_commands_handlers(dp)

    register_bot_blocked_handlers(dp)

    # хэндлеры chat_member и new_members и left_members
    register_channel_chat_member_handlers(dp)
    register_add_channel_my_chat_member_handlers(dp)
    register_channel_telegram_notifications_handlers(dp)

    # хэндлеры команд приватного чата
    register_recommend_bot_deep_link(dp)
    register_channel_members_commands_handlers(dp)
    register_chat_commands_handlers(dp)
    register_main_commands_handlers(dp)
    register_general_commands_handlers(dp)

    # хэндлеры главного меню
    # обработчики меню для розыгрышей
    register_create_draw_menu_handlers(dp)
    register_edit_draw_menu_handlers(dp)

    register_share_bot_handlers(dp)
    register_edit_channel_menu_handlers(dp)
    register_subscriptions_menu_handlers(dp)
    register_create_poll_menu_handlers(dp)
    register_edit_poll_menu_handlers(dp)
    register_mailing_info_button_handler(dp)

    CorrespondencesMailing.setup_mailing(dp)
    LocationMailing.setup_mailing(dp)
    DrawMailing.setup_mailing(dp)

    dp["router"].setup_handlers()

    # хэндлеры сообщений
    register_edit_channel_message_handlers(dp)
    register_channel_filters_message_handlers(dp)
    register_channel_ratio_message_handlers(dp)

    register_interests_settings_message_handlers(dp)

    register_create_poll_message_handlers(dp)
    register_edit_poll_message_handlers(dp)

    register_create_schedule_message_handlers(dp)
    register_edit_schedule_message_handlers(dp)

    # формы для розыгрышей
    CreateDrawForm.setup_handlers(dp)
    EditDrawForm.setup_handlers(dp)

    # формы scheduler
    CreateScheduleForm.setup_handlers(dp)
    EditScheduleForm.setup_handlers(dp)
    AddChannelMenuButtonForm.setup_handlers(dp)
    EditChannelMenuButtonForm.setup_handlers(dp)

    SetMessageLimitForm.setup_handlers(dp)

    # формы для футеров
    CreateFooterForm.setup_handlers(dp)
    EditFooterForm.setup_handlers(dp)

    register_chat_message_handlers(dp)
    register_unhandled_messages_handlers(dp)

    # хэндлеры callback_query
    setup_agreements_handlers(dp)
    register_add_channel_callback_handlers(dp)
    register_channel_correspondences_callback_handlers(dp)
    register_edit_channel_callback_handlers(dp)
    register_channel_filters_callback_handlers(dp)
    register_channel_members_callback_handlers(dp)
    register_channel_ratio_callback_handlers(dp)
    register_create_channel_menu_buttons_callback_handlers(dp)
    register_edit_channel_menu_buttons_callback_handlers(dp)

    # обработчики для розыгрышей
    register_create_draw_callback_handlers(dp)
    register_edit_draw_callback_handlers(dp)
    register_statistics_draw_callback_handlers(dp)
    register_winners_draw_callback_handlers(dp)

    # обработчики для футеров
    register_footer_callback_handlers(dp)
    register_edit_footer_callback_handlers(dp)

    register_interests_posts_callback_handlers(dp)

    register_create_poll_callback_handlers(dp)
    register_edit_poll_callback_handlers(dp)

    register_schedule_copy_posts_callback_handlers(dp)
    register_create_schedule_callback_handlers(dp)
    register_delete_schedule_callback_handlers(dp)
    register_edit_schedule_callback_handlers(dp)

    register_chat_callback_handlers(dp)
    register_main_callback_handlers(dp)
    register_general_callback_handlers(dp)

    register_general_exception_handlers(dp)


__all__ = [
    "on_startup",
    "register_web_handlers",
    "register_bot_handlers",
    "bind_filters",
]
