from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Channel

from utils.message import send_or_edit_message

from psutils.state_router.route_handlers import BaseRouteMenu

from utils.text import f


class ChannelRouteMenu(BaseRouteMenu):

    @classmethod
    async def header(
            cls,
            message: types.Message,
            state: FSMContext,
            lang: str,
    ) -> types.Message:
        state_data = await state.get_data()

        channel_id = state_data.get("channel_id")
        channel = await Channel.get(channel_id)
        message_text = await f("schedule channel link header", lang, channel_link=channel.get_link())

        message_header_id = state_data.get("message_header_id")
        msg = cls.message_header if message_header_id else message

        return await send_or_edit_message(msg, message_text)
