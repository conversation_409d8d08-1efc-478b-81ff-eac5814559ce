from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Channel

from psutils.convertors import datetime_to_str
from psutils.date_time import get_local_datetime

from psutils.forms.helpers import save_messages_to_state, delete_messages

from utils.message import send_tg_message, send_or_edit_message

from utils.text import f

from utils.router import Router

from utils.keyboards import get_navigation_keyboard

from friendly.schedule.route_menu import ChannelRouteMenu

from friendly.schedule.keyboards import *

from friendly.schedule.create.keyboards import get_create_schedule_type_keyboard, get_create_schedule_posts_keyboard

from friendly.schedule.create.states import CreateSchedule


async def send_enter_schedule_type_menu(message: types.Message, lang: str):
    message_text = await f("select schedule type header", lang)
    keyboard = await get_create_schedule_type_keyboard(lang)
    return await message.answer(message_text, reply_markup=keyboard)


async def send_enter_schedule_start_date_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    timezone = channel.timezone

    current_datetime = get_local_datetime(timezone)
    message_text = await f(
        "enter start datetime header", lang,
        current_datetime=datetime_to_str(current_datetime),
    )

    keyboard = await get_schedule_datetime_keyboard(timezone, lang)
    return await send_or_edit_message(message, message_text=message_text, keyboard=keyboard)


async def send_enter_schedule_start_time_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    timezone = channel.timezone
    message_text = await f("enter start time header", lang)
    keyboard = await get_schedule_time_keyboard(timezone, lang)
    return await send_or_edit_message(message, message_text=message_text, keyboard=keyboard)


async def send_enter_schedule_name_menu(message: types.Message, lang: str):
    message_text = await f("input schedule name", lang)
    keyboard = await get_navigation_keyboard(lang, need_next=True)
    return await send_or_edit_message(message, message_text, keyboard=keyboard)


async def send_enter_schedule_time_period_menu(message: types.Message, lang: str):
    message_text = await f("enter time period header", lang)
    keyboard = await get_schedule_time_period_keyboard(lang)
    return await send_or_edit_message(message, message_text=message_text, keyboard=keyboard)


async def send_enter_schedule_count_days_menu(message: types.Message, lang: str):
    message_text = await f("enter count days header", lang)
    keyboard = await get_schedule_count_days_keyboard(lang)
    return await send_or_edit_message(message, message_text=message_text, keyboard=keyboard)


async def send_enter_schedule_frequency_message_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule_type = state_data.get("schedule_type")

    if "by_time" == schedule_type:
        message_text = await f("enter count of time per day header", lang)
        keyboard = await get_schedule_count_of_time_per_day_keyboard(lang)
    elif "between_posts" == schedule_type:
        message_text = await f("enter post frequency header new", lang)
        keyboard = await get_schedule_frequency_message_keyboard(lang)
    else:
        raise ValueError("schedule_type must be one of \"by_time\" or \"between_posts\"")

    return await send_or_edit_message(message, message_text=message_text, keyboard=keyboard)


async def send_enter_schedule_posts_menu(message: types.Message, state: FSMContext, lang: str):
    async with state.proxy() as state_data:
        messages = state_data.pop("messages", list())

    state_data = await state.get_data()
    posts = state_data.get("posts", list())
    chat_id = message.chat.id

    message_text = await f("enter posts header", lang)
    keyboard = await get_create_schedule_posts_keyboard(lang)
    msg = await message.answer(message_text, reply_markup=keyboard)
    await save_messages_to_state(state, msg)

    if posts:
        for post in posts:
            post_message_kwargs = {
                "text": post["text"],
                "content_type": post["content_type"]
            }
            if post["content_type"] != "text":
                post_message_kwargs.update({post["content_type"]: post["media_path"]})
            msg = await send_tg_message(chat_id, **post_message_kwargs)
            await save_messages_to_state(state, msg)
        message_text = await f("send more posts header", lang)
        msg = await message.answer(message_text, reply_markup=keyboard)
        await save_messages_to_state(state, msg)

    await delete_messages(message.chat.id, *messages)


def register_create_schedule_routes(router: Router):
    router.add_route(CreateSchedule.ScheduleType, ChannelRouteMenu(send_enter_schedule_type_menu))
    router.add_route(CreateSchedule.StartTime, ChannelRouteMenu(send_enter_schedule_start_time_menu))
    router.add_route(CreateSchedule.StartDate, ChannelRouteMenu(send_enter_schedule_start_date_menu))
    router.add_route(CreateSchedule.CountDays, send_enter_schedule_count_days_menu)
    router.add_route(CreateSchedule.Name, ChannelRouteMenu(send_enter_schedule_name_menu))
    router.add_route(CreateSchedule.TimePeriod, ChannelRouteMenu(send_enter_schedule_time_period_menu))
    router.add_route(CreateSchedule.FrequencyMessage, ChannelRouteMenu(send_enter_schedule_frequency_message_menu))
    router.add_route(CreateSchedule.Posts, ChannelRouteMenu(send_enter_schedule_posts_menu))
