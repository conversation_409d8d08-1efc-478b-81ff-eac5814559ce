import asyncio

from datetime import time

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Schedule, User
from friendly.schedule.posts.functions import send_post_to_channel

from psutils.convertors import str_to_date

from utils.message import send_tg_message

from utils.text import f

from utils.router import Router

from friendly.main.keyboards import get_menu_keyboard

from friendly.schedule.edit.states import EditSchedule


async def create_schedule(state: FSMContext, creator: User) -> Schedule:
    state_data = await state.get_data()

    keys = (
    "channel_id", "schedule_type", "name", "start_time", "end_time", "start_date", "count_days", "frequency_message",
    "posts",)
    channel_id, schedule_type, name, start_time, end_time, start_date, count_days, frequency_message, posts_data = \
        map(lambda key: state_data.get(key), keys)

    start_time, end_time = map(lambda x: time(*map(int, x.split(":"))) if type(x) is str else x, (start_time, end_time))
    if start_date:
        start_date = str_to_date(start_date)

    schedule = await Schedule.create(
        start_time=start_time, end_time=end_time,
        frequency_message=frequency_message, channel_id=channel_id, user_id=creator.id,
        schedule_type=schedule_type,
        name=name, count_days=count_days,
        start_date=start_date
    )
    if posts_data:
        await schedule.add_posts(posts_data, creator=creator)

    state_data = await state.get_data()

    return schedule


async def save_schedule(message: types.Message, state: FSMContext, creator: User, lang: str):
    schedule = await create_schedule(state, creator)

    message_text = await f("schedule created", lang, schedule_name=schedule.name, publish_time=schedule.publish_time)
    keyboard = await get_menu_keyboard(creator, lang)
    chat_id = message.chat.id
    send_coro = send_tg_message(chat_id, "text", keyboard=keyboard, text=message_text)
    results = await asyncio.gather(send_coro, message.delete())

    await state.finish()
    await state.update_data(channel_id=schedule.channel_id)
    await EditSchedule.ChooseSchedule.set()

    await Router.state_menu(results[0], state, mode="new")

    return schedule


async def save_and_send_schedule(message: types.Message, state: FSMContext, creator: User, lang: str):
    schedule = await save_schedule(message, state, creator, lang)

    await send_post_to_channel(schedule, schedule.channel, last=True)
