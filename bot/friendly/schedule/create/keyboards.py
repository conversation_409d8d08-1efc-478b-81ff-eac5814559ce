from utils.redefined_classes import InlineKb, InlineBtn, MenuKb, MenuBtn

from utils.text import f, c

from utils.keyboards import get_navigation_keyboard


async def get_create_schedule_type_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=2)

    button_text = await f("schedule type by time button", lang)
    callback_data = c("schedule_type", type="by_time")
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("schedule type between posts button", lang)
    callback_data = c("schedule_type", type="between_posts")
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return await get_navigation_keyboard(lang, keyboard)


async def get_create_schedule_posts_keyboard(lang: str) -> MenuKb:
    keyboard = MenuKb(resize_keyboard=True, row_width=4)

    button_text = await f("save and send button", lang)
    keyboard.insert(MenuBtn(button_text))

    button_text = await f("save posts button", lang)
    keyboard.insert(MenuBtn(button_text))

    button_text = await f("copy posts button", lang)
    keyboard.insert(MenuBtn(button_text))

    button_text = await f("action cancel button", lang)
    keyboard.insert(MenuBtn(button_text))

    return keyboard
