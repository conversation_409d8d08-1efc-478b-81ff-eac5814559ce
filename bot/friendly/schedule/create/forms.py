from datetime import datetime

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Post, User

from psutils.convertors import date_to_str, time_to_str

from psutils.date_time import localise_datetime

from psutils.forms import WizardForm

from psutils.forms.fields import <PERSON><PERSON>ield, IntegerField
from psutils.forms.fields import DateTimeField, TimeField, TimePeriodField
from psutils.forms.fields import MessageField
from psutils.forms.fields import InlineButtonsField

from psutils.forms.validators import MaxTextLengthValidator

from utils.text import f

from utils.router import Router

from friendly.schedule.create.validators import IsTimeValidator

from friendly.schedule.create.states import CreateSchedule
from friendly.schedule.edit.states import EditSchedule

from friendly.functions import post_data_processor


async def data_saver_content_type(data: dict, state: FSMContext):
    async with state.proxy() as state_data:
        state_data["posts"] = state_data.get("posts", list()) + [data]


async def post_save_frequency(message: types.Message, state: FSMContext, lang: str):
    if int(message.text) == 0:
        await message.answer(await f("schedule disabled _MESSAGE", lang))
        await Router.state_menu(message, state, mode="new")


async def data_saver_schedule_type(data: dict, state: FSMContext):
    await state.update_data(schedule_type=data.get("type"))


async def post_save_schedule_type(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule_type = state_data.get("schedule_type")
    await callback_query.answer(await f(f"chosen {schedule_type} schedule type", lang))


async def data_processor_start_date(user: User, datetime_: datetime, is_time: bool) -> dict | None:
    datetime_ = localise_datetime(datetime_, "utc", user.get_timezone())
    return {"datetime": datetime_, "is_time": is_time}


async def data_saver_start_date(data: dict, state: FSMContext):
    datetime_ = data.get("datetime")
    await state.update_data(start_date=date_to_str(datetime_.date()))

    is_time = data.pop("is_time")
    if is_time:
        await state.update_data(start_time=time_to_str(datetime_.time()))


class CreateScheduleForm(WizardForm):
    state_group = CreateSchedule

    previous_state = EditSchedule.ChooseSchedule.state
    back_to_previous_state_excluded_keys = ("channel_id",)

    schedule_type = InlineButtonsField(
        callback_mode="schedule_type",
        callback_keys="type",
        data_saver=data_saver_schedule_type,
        post_save=post_save_schedule_type,
    )

    name = TextField(
        input_validator=MaxTextLengthValidator(20, "invalid schedule name error")
    )

    start_time = TimeField()

    start_date = DateTimeField(
        input_validator=IsTimeValidator(),
        data_processor=data_processor_start_date,
        data_saver=data_saver_start_date,
    )

    count_days = IntegerField(
        only_positive=True,
    )
    frequency_message = IntegerField(
        only_positive=True,
        post_save=post_save_frequency,
    )

    time_period = TimePeriodField()

    posts_from_bot = IntegerField(
    )

    posts = MessageField(
        *Post.SUPPORTED_MESSAGE_TYPES,
        error_text_variable="invalid post message type error",
        data_processor=post_data_processor,
        data_saver=data_saver_content_type,
        need_go_next_state=False,
        handler_state=CreateSchedule.Posts,
    )

    @classmethod
    async def next_field(cls, state: FSMContext):
        await cls.set_next_state(state)

    @classmethod
    async def set_next_state(cls, state: FSMContext = None):
        if not state:
            return await cls.state_group.next()

        cur_state = await state.get_state()
        state_data = await state.get_data()
        schedule_type = state_data.get("schedule_type")

        if cur_state == CreateSchedule.ScheduleType.state and "between_posts" == schedule_type:
            await CreateSchedule.FrequencyMessage.set()
        elif cur_state == CreateSchedule.StartDate.state and state_data.get("start_time"):
            await CreateSchedule.CountDays.set()
        elif cur_state == CreateSchedule.FrequencyMessage.state and "by_time" == schedule_type and state_data.get("frequency_message") == 1:
            await CreateSchedule.Name.set()
        else:
            await cls.state_group.next()

    @classmethod
    async def set_prev_state(cls, state: FSMContext):
        cur_state = await state.get_state()
        state_data = await state.get_data()
        schedule_type = state_data.get("schedule_type")

        if cur_state == CreateSchedule.CountDays.state:
            await CreateSchedule.StartDate.set()
        elif cur_state == CreateSchedule.FrequencyMessage.state and "between_posts" == schedule_type:
            await CreateSchedule.ScheduleType.set()
        elif cur_state == CreateSchedule.Name.state and "by_time" == schedule_type and state_data.get("frequency_message") == 1:
            await CreateSchedule.FrequencyMessage.set()
        else:
            await super().set_prev_state(state)
