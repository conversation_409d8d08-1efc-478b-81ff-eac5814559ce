from datetime import datetime, time, timedelta

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from psutils.convertors import date_to_str, time_to_str

from utils.router import Router

from friendly.schedule.create.states import CreateSchedule


async def create_schedule_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    state_data = await state.get_data()
    channel_id = state_data.get("channel_id")

    await state.finish()
    await state.update_data(channel_id=channel_id)

    await CreateSchedule.first()
    await Router.state_menu(callback_query, state, set_state_message=True)


async def skip_schedule_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await CreateSchedule.next()
    await Router.state_menu(callback_query, state)


async def whole_day_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await state.update_data(start_time="00:00")

    await CreateSchedule.next()
    await Router.state_menu(callback_query, state)


async def start_datetime_buttons_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    start_date, start_time = None, None
    now = datetime.utcnow()
    day = callback_data.get("day")

    if "tomorrow" == day:
        start_date = now.date() + timedelta(days=1)
    else:
        start_date = now.date()

    if "now" == day:
        start_time = now.time()
    elif "round" == day:
        in_one_hour = now + timedelta(hours=1)
        start_time = time(in_one_hour.hour, 0)

    if start_date:
        await state.update_data(start_date=date_to_str(start_date))
    if start_time:
        await state.update_data(start_time=time_to_str(start_time))

    if day in ("now", "round",):
        await CreateSchedule.CountDays.set()
    else:
        await CreateSchedule.next()
    await Router.state_menu(callback_query, state)


async def count_buttons_days_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    cur_state = await state.get_state()
    count = callback_data.get("count")

    if cur_state == CreateSchedule.CountDays.state:
        await state.update_data(count_days=count)
    elif cur_state == CreateSchedule.FrequencyMessage.state:
        await state.update_data(frequency_message=count)

    if cur_state == CreateSchedule.FrequencyMessage.state and count == 1:
        await CreateSchedule.Name.set()
    else:
        await CreateSchedule.next()
    await Router.state_menu(callback_query, state)


def register_create_schedule_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        skip_schedule_button_handler,
        skip_button=True,
        chat_type="private",
        state=CreateSchedule.Name,
    )

    dp.register_callback_query_handler(
        whole_day_button_handler,
        callback_mode="whole_day",
        chat_type="private",
        state=CreateSchedule.TimePeriod,
    )

    dp.register_callback_query_handler(
        start_datetime_buttons_handler,
        callback_mode="schedule_date",
        chat_type="private",
        state=[CreateSchedule.StartDate, CreateSchedule.StartTime],
    )

    dp.register_callback_query_handler(
        count_buttons_days_handler,
        callback_mode=["count_days", "count_of_time_per_day", "posts_interval"],
        chat_type="private",
        state=[CreateSchedule.CountDays, CreateSchedule.FrequencyMessage],
    )

    dp.register_callback_query_handler(
        create_schedule_button_handler,
        callback_mode="create_schedule",
        chat_type="private",
        state="*"
    )
