from typing import List

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.types import ContentTypes
from aiogram_media_group import media_group_handler, MediaGroupFilter

from db.models import User, ClientBot
from friendly.channel.db_funcs import get_admined_channels
from psutils.forms.helpers import with_delete_state_messages, save_messages_to_state

from utils.text import f
from core.media import download_file
from utils.router import Router

from friendly.schedule.create.functions import save_schedule, save_and_send_schedule

from friendly.main.keyboards import get_menu_keyboard

from friendly.schedule.create.states import CreateSchedule
from friendly.schedule.edit.states import EditSchedule
from friendly.schedule.copy_posts.states import CopyPosts


async def posts_save_button_handler(message: types.Message, state: FSMContext, user: User, lang: str):
    await save_schedule(message, state, user, lang)


async def posts_save_and_send_button_handler(message: types.Message, state: FSMContext, user: User, lang: str):
    await save_and_send_schedule(message, state, user, lang)


async def posts_copy_posts_button_handler(message: types.Message, state: FSMContext):
    bot_id = int(ClientBot.get_current_bot_id())
    user = await User.get_current()
    is_super_admin_mode = await user.is_friendly_super_admin(bot_id)
    channels = await get_admined_channels(user, bot_id, is_super_admin_mode=is_super_admin_mode)
    if len(channels) == 1:
        await CopyPosts.ChooseSchedule.set()
        await state.update_data(
            import_channel_id=channels[0].id,
            one_channel_admin=True
        )
    else:
        await CopyPosts.ChooseChannel.set()
    await Router.state_menu(message, state)


@with_delete_state_messages
async def cancel_button_handler(message: types.Message, state: FSMContext, user: User, lang: str):
    keyboard = await get_menu_keyboard(user, lang)
    await message.answer(await f("action cancel text", lang), reply_markup=keyboard)

    state_data = await state.get_data()
    channel_id = state_data.get("channel_id")
    await state.finish()

    await state.update_data(channel_id=channel_id)
    await EditSchedule.first()
    await Router.state_menu(message, state)


async def posts_media_group_handler(messages: List[types.Message], state: FSMContext, lang: str):
    new_posts = list()
    for message in messages:
        file_path = await download_file(message)
        post = dict(content_type=message.content_type, media_path=file_path, text=message.caption)
        new_posts.append(post)
    async with state.proxy() as state_data:
        state_data["posts"] = state_data.get("posts", list()) + new_posts

    msg = await messages[0].answer(await f("send more posts header", lang))
    await save_messages_to_state(state, msg)


def register_create_schedule_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cancel_button_handler,
        cancel_button=True,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=[CopyPosts, CreateSchedule],
    )

    dp.register_message_handler(
        posts_save_button_handler,
        lequal="save posts button",
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=[CopyPosts, CreateSchedule.Posts],
    )

    dp.register_message_handler(
        posts_save_and_send_button_handler,
        lequal="save and send button",
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=[CopyPosts, CreateSchedule.Posts],
    )

    dp.register_message_handler(
        posts_copy_posts_button_handler,
        lequal="copy posts button",
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=[CopyPosts, CreateSchedule.Posts],
    )

    dp.register_message_handler(
        media_group_handler(posts_media_group_handler, storage_driver=dp.storage),
        MediaGroupFilter(),
        chat_type="private",
        content_types=ContentTypes.ANY,
        state=[CopyPosts, CreateSchedule.Posts],
    )
