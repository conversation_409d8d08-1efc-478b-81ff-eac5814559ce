from db.models import Schedule

from utils.keyboards import previous_button, active_button

from psutils.convertors import date_to_str, time_to_str, time_period_to_str

from utils.redefined_classes import InlineKb, InlineBtn, MenuKb, MenuBtn

from utils.text import f, c


async def get_edit_schedule_keyboard(schedule: Schedule, lang: str) -> InlineKb:
    posts_count = await schedule.get_media_count()
    time_period = time_period_to_str(lang, schedule.start_time, schedule.end_time)
    start_time = time_to_str(schedule.start_time)
    start_date = date_to_str(schedule.start_date) if schedule.start_date else ""
    type_between_posts = bool("between_posts" == schedule.schedule_type)
    type_by_time = bool("by_time" == schedule.schedule_type)

    keyboard = InlineKb(row_width=2)

    button_text = await f("change name button", lang, name=schedule.name)
    callback_data = c("name", schedule_id=schedule.id)
    keyboard.row(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("edit posts button", lang, count=posts_count)
    callback_data = c("posts", schedule_id=schedule.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    schedule_type = schedule.schedule_type
    if schedule_type == "between_posts":
        schedule_type_text = await f("schedule type between posts button", lang)
    else:
        schedule_type_text = await f("schedule type by time button", lang)
    button_text = await f("edit schedule type button", type=schedule_type_text, lang=lang)
    callback_data = c("edit_schedule_type", schedule_id=schedule.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if type_by_time:
        start_time_str = start_time if type_by_time else "0"
        button_text = await f("change start time by time button", lang, start_time=start_time_str)
        callback_data = c("start_time", schedule_id=schedule.id)
        keyboard.add(InlineBtn(button_text, callback_data=callback_data))

        start_date_str = start_date if type_by_time else "0"
        button_text = await f("change start date by time button", lang, start_date=start_date_str)
        callback_data = c("start_date", schedule_id=schedule.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        count_days = schedule.count_days if type_by_time else 0
        if count_days is None:
            count_days = await f("schedule count days message disabled", lang)
        elif count_days > 0:
            count_days = await f("schedule count days message", lang, count_days=count_days)
        elif count_days == 0:
            count_days = await f("schedule count days message constantly", lang)

        button_text = await f("change count days by time button", lang, count_days=count_days)
        callback_data = c("count_days", schedule_id=schedule.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        count_of_time_per_day = schedule.frequency_message if type_by_time else 0
        button_text = await f(
            "change count of time per day by time button", lang,
            count_of_time_per_day=count_of_time_per_day
        )
        callback_data = c("count_of_time_per_day", schedule_id=schedule.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))
    else:
        frequency_message = schedule.frequency_message if type_between_posts else 0
        button_text = await f("change frequency message button", lang, frequency_message=frequency_message)
        callback_data = c("frequency_message", schedule_id=schedule.id)
        keyboard.add(InlineBtn(button_text, callback_data=callback_data))

        time_period_str = time_period if type_between_posts else "0"
        button_text = await f("change time period by frequency button", lang, time_period=time_period_str)
        callback_data = c("time_period", schedule_id=schedule.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("select footer button", lang)
    callback_data = c("select_footer", channel_id=schedule.channel_id, schedule_id=schedule.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("schedule public button", lang)
    if schedule.is_public:
        button_text = await active_button(lang, button_text)
    callback_data = c("schedule_public", schedule_id=schedule.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if schedule.status == "active":
        button_text = await f("stop schedule", lang)
        callback_data = c("stop", schedule_id=schedule.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))
    else:
        button_text = await f("activate schedule", lang)
        callback_data = c("activate", schedule_id=schedule.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("delete schedule", lang)
    callback_data = c("delete", schedule_id=schedule.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return keyboard


async def get_schedule_posts_menu_keyboard(lang: str) -> MenuKb:
    keyboard = MenuKb(resize_keyboard=True)
    buttons = list()
    buttons.append(MenuBtn(await f("done button", lang)))
    buttons.append(MenuBtn(await f("copy posts button", lang)))
    buttons.append(MenuBtn(await f("admin button", lang)))

    keyboard.row(*buttons)

    return keyboard


async def get_stop_schedule_keyboard(lang) -> InlineKb:
    keyboard = InlineKb(row_width=2)

    keyboard.insert(InlineBtn(await f("schedule stop now button", lang), callback_data="right_now"))
    keyboard.insert(await previous_button(lang))
    return keyboard
