from datetime import date

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Schedule, Post, User
from friendly.helpers import send_error
from friendly.schedule.edit.functions import activate_schedule

from psutils.convertors import str_to_date

from psutils.forms import FieldsListForm

from psutils.forms.fields import InlineButtonsField, SetStateField
from psutils.forms.fields import <PERSON><PERSON>ield, IntegerField
from psutils.forms.fields import Date<PERSON>ield, TimeField, TimePeriodField
from psutils.forms.fields import MessageField

from psutils.forms.validators import MaxTextLengthValidator

from utils.text import f

from friendly.channel.edit.states import EditChannel

from friendly.schedule.edit.states import EditSchedule

from friendly.functions import post_data_processor

from friendly.schedule.posts.functions import send_post_to_channel


async def choose_schedule_data_saver(data: dict, state: FSMContext):
    await state.update_data(**data)
    await EditSchedule.ChooseField.set()


async def start_date_data_saver(data: dict, state: FSMContext):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    start_date = str_to_date(data.get("start_date"))
    await schedule.set_start_date(start_date)
    await EditSchedule.ChooseField.set()


async def posts_data_saver(data: dict, state: FSMContext, user: User):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    await schedule.add_posts(data, creator=user)


async def count_data_saver(data: dict, state: FSMContext):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    if not schedule.start_date:
        today = date.today()
        await schedule.set_start_date(today)

    result = await schedule.update(frequency_message=data.get("count_of_time_per_day", 0))
    await state.update_data(result=result)
    await EditSchedule.ChooseField.set()


async def frequency_post_save(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))
    if message.text.isdecimal() and int(message.text) != 0:
        await schedule.counter_messages(reset=True)
        await schedule.counter_messages(add_messages=schedule.frequency_message)
    else:
        await message.answer(await f("schedule disabled message", lang))
        result = await schedule.stop()
        if not result:
            return await send_error(message)


async def activate_post_save(message: types.Message, state: FSMContext, lang: str):
    if message.text.isdecimal() and int(message.text) != 0:
        state_data = await state.get_data()
        schedule = await Schedule.get(state_data.get("schedule_id"))
        count = int(message.text)

        await schedule.update(count_days=count)
        await activate_schedule(message, state, schedule, lang)
    else:
        await message.answer(await f("frequency message less then zero error", lang))


async def send_post_save(state: FSMContext):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))
    await schedule.counter_messages(reset=True)
    await send_post_to_channel(schedule, schedule.channel, last=True)


class EditScheduleForm(FieldsListForm):
    state_group = EditSchedule

    choose_field_state = EditSchedule.ChooseField.state
    back_to_previous_state_excluded_keys = ("channel_id", "schedule_id",)

    choose_schedule = InlineButtonsField(
        callback_mode="schedule",
        callback_keys=["schedule_id"],
        data_saver=choose_schedule_data_saver,
    )
    add_public_schedule = InlineButtonsField(
        callback_mode="schedule",
        callback_keys=["schedule_id"],
        data_saver=choose_schedule_data_saver,
    )

    choose_field = SetStateField()

    name = TextField(
        input_validator=MaxTextLengthValidator(20, "invalid schedule name error"),
    )

    time_period = TimePeriodField(
        post_save=send_post_save,
    )
    start_time = TimeField(
        post_save=send_post_save,
    )

    start_date = DateField(
        data_saver=start_date_data_saver,
        post_save=send_post_save,
    )

    frequency_message = IntegerField(
        only_positive=True,
        post_save=frequency_post_save,
    )
    count_days = IntegerField(
        only_positive=True,
    )
    count_of_time_per_day = IntegerField(
        only_positive=True,
        data_saver=count_data_saver,
    )

    posts = MessageField(
        *Post.SUPPORTED_MESSAGE_TYPES,
        error_text_variable="invalid post message type error",
        data_processor=post_data_processor,
        data_saver=posts_data_saver,
    )

    activate_schedule = IntegerField(
        only_positive=True,
        post_save=activate_post_save,
    )

    @classmethod
    async def data_saver(cls, data: dict, state: FSMContext):
        cur_state = await state.get_state()
        state_data = await state.get_data()
        schedule = await Schedule.get(state_data.get("schedule_id"))

        if cur_state in (EditSchedule.TimePeriod.state, EditSchedule.FrequencyMessage.state,) and schedule.schedule_type == "by_time":
            await schedule.update(schedule_type="between_posts")
        elif cur_state in (EditSchedule.StartTime.state, EditSchedule.CountDays.state,) and schedule.schedule_type == "between_posts":
            today = date.today()
            await schedule.set_start_date(today)
            await schedule.update(schedule_type="by_time")

        result = await schedule.update(**data)
        await state.update_data(result=result)
        await EditSchedule.ChooseField.set()

    @classmethod
    async def set_prev_state(cls, state: FSMContext):
        cur_state = await state.get_state()
        if cur_state == EditSchedule.ChooseSchedule.state:
            await EditChannel.first()
        elif cur_state == EditSchedule.ChooseField.state:
            await EditSchedule.first()
        elif cur_state == EditSchedule.PublicSchedule.state:
            await state.update_data(public=False, need_add=True)
            await EditSchedule.first()
        else:
            await EditSchedule.ChooseField.set()
