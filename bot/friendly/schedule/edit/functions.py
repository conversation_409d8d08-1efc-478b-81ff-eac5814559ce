import html
import logging

from aiogram import types
from aiogram.dispatcher import FSMContext
from psutils.forms.helpers import save_messages_to_state

from db.models import Schedule, User
from friendly.footer.functions import send_message_and_footer
from friendly.schedule.edit.states import EditSchedule
from utils.keyboards import active_button
from utils.message import send_tg_message
from utils.redefined_classes import InlineBtn, InlineKb
from utils.router import Router
from utils.text import c, f


async def send_schedule_posts(
        message: types.Message, state: FSMContext, schedule: Schedule, user: User,
        lang: str
):
    logger = logging.getLogger()

    need_delete_friendly_posts = user.need_delete_friendly_posts

    messages = list()
    for post in schedule.posts:
        keyboard = InlineKb(row_width=1)

        button_text = await f("schedule preview checkbox", lang)
        if post.is_show_web_page_preview:
            button_text = await active_button(lang, button_text)
        callback_data = c("preview", post_id=post.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        button_text = await f("show html tags in schedule button", lang)
        if post.is_show_html_tag:
            button_text = await active_button(lang, button_text)
        callback_data = c("show_html_tag", post_id=post.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        button_text = await f("footer message button", lang)
        callback_data = c("select_footer", channel_id=post.channel.id, post_id=post.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        keyboard.insert(
            InlineBtn(
                await f("delete post", lang),
                callback_data=c("delete_post", post_id=post.id)
            )
        )

        post_kwargs = post.kwargs_for_send
        footers = list()
        if post.footer:
            footers.append(post.footer)
        if post.schedule.footer:
            footers.append(post.schedule.footer)

        try:
            results = await send_message_and_footer(
                message.chat.id,
                post_kwargs, keyboard,
                footers, True,
                need_html_escape=post.is_show_html_tag,
            )

        except Exception as e:
            logger.error(e, exc_info=True)

            text = post_kwargs.get("text")
            if not text:
                content_type = post_kwargs.get("content_type")
                text = await f(f"content type {content_type} text")

            text = html.escape(text)

            text = await f(
                "friendly post sending error", lang, text=text, post_id=post.id
            )

            try:
                results = await send_tg_message(
                    message.chat.id, "text", keyboard=keyboard,
                    all_messages_in_result=True,
                    text=text
                )
            except Exception as e:
                logger.error(e, exc_info=True)
                continue

        if need_delete_friendly_posts and all(
                [not isinstance(result, Exception) for result in results]
        ):
            messages.extend(results)

    if messages:
        await save_messages_to_state(state, messages)


async def activate_schedule(
        message: types.Message | types.CallbackQuery,
        state: FSMContext,
        schedule: Schedule,
        lang: str,
        mode: str = "edit"
):
    await schedule.activate()
    await schedule.counter_messages(reset=True)
    await schedule.counter_messages(add_messages=schedule.frequency_message)

    message_text = await f("schedule activated", lang)
    await EditSchedule.ChooseField.set()
    if isinstance(message, types.CallbackQuery):
        await message.answer(message_text)
    if mode == "new":
        await Router.state_menu(message, state, mode="new", set_state_message=True)
