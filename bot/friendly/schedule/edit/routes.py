from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Schedule, User

from utils.keyboards import get_navigation_keyboard

from psutils.forms.helpers import save_messages_to_state, delete_messages

from utils.message import send_or_edit_message

from psutils.convertors import date_to_str, time_to_str, time_period_to_str

from utils.router import Router

from utils.text import f

from friendly.schedule.keyboards import get_schedule_count_days_keyboard, get_schedule_count_of_time_per_day_keyboard

from friendly.schedule.edit.functions import send_schedule_posts, activate_schedule

from friendly.schedule.edit.keyboards import get_schedule_posts_menu_keyboard, get_stop_schedule_keyboard
from friendly.schedule.edit.keyboards import get_edit_schedule_keyboard

from friendly.schedule.edit.list_drawers import ScheduleListDrawer

from friendly.schedule.edit.states import EditSchedule

from friendly.schedule.keyboards import get_schedule_time_period_keyboard, get_schedule_date_keyboard


async def send_edit_schedule_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "edit"):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    message_text = await f("edit schedule header", lang, schedule_name=schedule.name)
    keyboard = await get_edit_schedule_keyboard(schedule, lang)

    if mode == "new":
        new = True
    else:
        new = False
    return await send_or_edit_message(message, message_text, keyboard=keyboard, new=new)


async def send_edit_schedule_name_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    message_text = await f("edit schedule name header", lang, schedule_name=schedule.name)
    keyboard = await get_navigation_keyboard(lang)

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


async def send_edit_schedule_time_period_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    schedule_time_period = time_period_to_str(lang, schedule.start_time, schedule.end_time)

    message_text = await f("edit schedule time period header", lang, schedule_time_period=schedule_time_period)
    keyboard = await get_schedule_time_period_keyboard(lang)

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


async def send_enter_schedule_start_time_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    start_time = time_to_str(schedule.start_time)
    message_text = await f("edit start time header", lang, current_start_time=start_time)
    keyboard = await get_navigation_keyboard(lang)
    return await send_or_edit_message(message, message_text=message_text, keyboard=keyboard)


async def send_enter_schedule_start_date_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    start_date = schedule.start_date
    start_date = date_to_str(start_date) if start_date else await f("schedule start date not set", lang)
    message_text = await f("edit start date header", lang, current_start_date=start_date)
    keyboard = await get_schedule_date_keyboard(lang)
    return await send_or_edit_message(message, message_text=message_text, keyboard=keyboard)


async def send_edit_count_days_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    count_days = schedule.count_days
    if count_days is None:
        count_days = await f("schedule count days message disabled", lang)
    elif count_days > 0:
        count_days = await f("schedule count days message", lang, count_days=count_days)
    elif count_days == 0:
        count_days = await f("schedule count days message constantly", lang)

    message_text = await f("edit count days by time header", lang, count_days=count_days)
    disable = False if schedule.count_days is None else True
    keyboard = await get_schedule_count_days_keyboard(lang, disable=disable)

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


async def send_edit_frequency_message_menu(message: types.Message, state: FSMContext, lang: str):
    cur_state = await state.get_state()
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    frequency_message = schedule.frequency_message
    if frequency_message > 0:
        frequency_message = await f("schedule frequency message", lang, frequency_message=frequency_message)
    else:
        frequency_message = await f("schedule frequency message disabled", lang)

    if cur_state == EditSchedule.CountOfTimePerDay.state:
        message_text = await f(
            "edit count of time per day by time header", lang, count_of_time_per_day=frequency_message
        )
        keyboard = await get_schedule_count_of_time_per_day_keyboard(lang)
    else:
        message_text = await f(
            "edit schedule frequency message header", lang, schedule_post_frequency=frequency_message
        )
        keyboard = await get_navigation_keyboard(lang)

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


async def send_edit_schedule_posts_menu(message: types.Message, state: FSMContext, user: User, lang: str):
    async with state.proxy() as state_data:
        messages = state_data.pop("messages", list())
        posts = state_data.get("posts", list())
        try:
            del state_data["posts"]
        except KeyError:
            pass

    schedule = await Schedule.get(state_data.get("schedule_id"))

    if posts:
        for post in posts:
            post_message_kwargs = {
                "text": post["text"],
                "content_type": post["content_type"]
            }
            if post["content_type"] != "text":
                post_message_kwargs.update(
                    {
                        post["content_type"]: post["media_path"],
                        "media_path": post["media_path"],
                    }
                )
            await schedule.add_posts(post_message_kwargs, creator=user)

    message_text = await f("enter posts header", lang)
    keyboard = await get_schedule_posts_menu_keyboard(lang)

    msg = await message.answer(message_text, reply_markup=keyboard)
    await save_messages_to_state(state, msg)

    await send_schedule_posts(message, state, schedule, user, lang)

    message_text = await f("send more posts header", lang)
    msg = await message.answer(message_text, reply_markup=keyboard)

    await delete_messages(message.chat.id, *messages)

    await save_messages_to_state(state, msg)


async def send_stop_schedule_menu(message: types.Message, lang: str):
    message_text = await f("stop schedule now header", lang)
    keyboard = await get_stop_schedule_keyboard(lang)

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


async def send_activate_schedule_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))
    mode = "new"

    if not schedule.count_days:
        count_days = await f("schedule count days message disabled", lang)
        message_text = await f("edit count days by time header", lang, count_days=count_days)
        keyboard = await get_schedule_count_days_keyboard(lang, disable=False)

        return await send_or_edit_message(message, message_text, keyboard=keyboard)

    await activate_schedule(message, state, schedule, lang, mode)


def register_edit_schedule_routes(router: Router):
    router.add_route(EditSchedule.ChooseSchedule, ScheduleListDrawer())
    router.add_route(EditSchedule.PublicSchedule, ScheduleListDrawer())
    router.add_route(EditSchedule.ChooseField, send_edit_schedule_menu)
    router.add_route(EditSchedule.Name, send_edit_schedule_name_menu)
    router.add_route(EditSchedule.TimePeriod, send_edit_schedule_time_period_menu)
    router.add_route(EditSchedule.StartTime, send_enter_schedule_start_time_menu)
    router.add_route(EditSchedule.StartDate, send_enter_schedule_start_date_menu)
    router.add_route(EditSchedule.CountDays, send_edit_count_days_menu)
    router.add_route(EditSchedule.CountOfTimePerDay, send_edit_frequency_message_menu)
    router.add_route(EditSchedule.FrequencyMessage, send_edit_frequency_message_menu)
    router.add_route(EditSchedule.Posts, send_edit_schedule_posts_menu)
    router.add_route(EditSchedule.ActivateSchedule, send_activate_schedule_menu)
    router.add_route(EditSchedule.StopSchedule, send_stop_schedule_menu)
