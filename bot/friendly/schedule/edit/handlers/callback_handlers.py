from datetime import date, time, timedelta

from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import Channel, Schedule, Post, User

from friendly.schedule.edit.functions import activate_schedule
from friendly.schedule.posts.functions import send_post_to_channel

from psutils.forms.helpers import delete_messages

from psutils.fsm import get_state_name_from_field

from utils.router import Router

from utils.text import f

from friendly.helpers import send_error

from friendly.schedule.delete.states import DeleteSchedule

from friendly.schedule.edit.states import EditSchedule
from friendly.footer.edit.states import EditFooter


async def schedules_list_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    await state.update_data(**callback_data)
    await EditSchedule.first()
    await Router.state_menu(callback_query, state)


async def edit_schedule_buttons_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, mode: str, callback_data: dict
):
    await state.update_data(**callback_data)
    state_name = get_state_name_from_field(mode)
    await getattr(EditSchedule, state_name).set()
    await Router.state_menu(callback_query, state)


async def edit_schedule_start_time_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict
):
    schedule = await Schedule.get(callback_data.get("schedule_id"))
    if schedule.schedule_type == "between_posts":
        await schedule.set_start_date_time_now()
        await schedule.update(schedule_type="by_time")
    else:
        await state.update_data(**callback_data)
        await EditSchedule.StartTime.set()
    await Router.state_menu(callback_query, state)


async def stop_schedule_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    await state.update_data(**callback_data)
    await EditSchedule.StopSchedule.set()
    await Router.state_menu(callback_query, state)


async def right_now_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    result = await schedule.stop()
    if not result:
        return await send_error(callback_query.message)

    message_text = await f("schedule stopped", lang, name=schedule.name)
    await callback_query.answer(message_text)
    await EditSchedule.ChooseField.set()
    await Router.state_menu(callback_query, state, mode="new", set_state_message=True)


async def activate_schedule_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    await state.update_data(**callback_data)
    await EditSchedule.ActivateSchedule.set()
    await Router.state_menu(callback_query, state)


async def count_days_buttons_handler(
        callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict, lang: str
):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))
    count = callback_data.get("count")
    mode = "new"

    await schedule.update(count_days=count)
    await activate_schedule(callback_query, state, schedule, lang, mode)


async def delete_schedule_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    await state.update_data(**callback_data)
    await DeleteSchedule.set()
    await Router.state_menu(callback_query, state)


async def delete_post_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
        user: User, lang: str
):
    post = await Post.get(callback_data.get("post_id"))
    result = await post.delete()
    if not result:
        return await send_error(callback_query)

    await callback_query.answer(await f("post deleted", lang))
    if user.need_delete_friendly_posts:
        async with state.proxy() as state_data:
            messages = state_data.pop("messages", [])

        if messages:
            await delete_messages(callback_query.from_user.id, messages)

    await Router.state_menu(callback_query, state, mode="new")


async def whole_day_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    start_time = time(0, 0)
    await schedule.update(start_time=start_time)
    await schedule.set_start_date(date.today())

    await EditSchedule.ChooseField.set()
    await Router.state_menu(callback_query, state)


async def start_date_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    start_date = None
    day = callback_data.get("day")
    if "today" == day:
        start_date = date.today()
    elif "tomorrow" == day:
        start_date = date.today() + timedelta(days=1)

    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))
    await schedule.set_start_date(start_date)
    await schedule.counter_messages(reset=True)

    await EditSchedule.ChooseField.set()
    await Router.state_menu(callback_query, state)


async def count_buttons_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict
):
    count = callback_data.get("count")

    cur_state = await state.get_state()
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    if cur_state == EditSchedule.CountDays.state:
        await schedule.update(count_days=count)
    elif cur_state == EditSchedule.CountOfTimePerDay.state:
        await schedule.update(frequency_message=count)
    await schedule.counter_messages(reset=True)

    await EditSchedule.ChooseField.set()
    await Router.state_menu(callback_query, state)


async def preview_post_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    post = await Post.get(callback_data.get("post_id"))
    await post.is_show_web_page_preview_button()
    await Router.state_menu(callback_query, state)


async def show_html_tag_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    post = await Post.get(callback_data.get("post_id"))
    await post.is_show_html_tags_button()
    await Router.state_menu(callback_query, state)


async def edit_schedule_type_button(
        callback_query: types.CallbackQuery,
        state: FSMContext
):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))
    schedule_type = schedule.schedule_type
    if schedule_type == "between_posts":
        new_schedule_type = "by_time"
    else:
        new_schedule_type = "between_posts"
    await schedule.set_type(new_schedule_type)

    await schedule.counter_messages(reset=True)
    await Router.state_menu(callback_query, state)


async def button_select_footer(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    cur_state = await state.get_state()
    mode = "edit"

    await state.update_data(**callback_data)
    if cur_state == EditSchedule.ChooseField.state:
        await state.update_data(prev="schedule")
    elif cur_state == EditSchedule.Posts.state:
        await state.update_data(prev="post")
        mode = "new"

    await EditFooter.ChooseFooter.set()
    await Router.state_menu(callback_query, state, mode=mode)


async def schedule_public_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    schedule = await Schedule.get(callback_data.get("schedule_id"))
    await schedule.update(is_public=not schedule.is_public)

    await Router.state_menu(callback_query, state)


async def change_public_schedule_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await state.update_data(public=True, need_add=False)

    await EditSchedule.PublicSchedule.set()
    await Router.state_menu(callback_query, state)


async def save_public_schedule_button_handler(
        callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict
):
    await state.update_data(**callback_data)
    state_data = await state.get_data()
    is_send_last_post = False

    schedule = await Schedule.get(state_data.get("schedule_id"))
    channel_id = state_data.get("import_channel_id", state_data.get("channel_id"))
    channel = await Channel.get(channel_id)
    if await channel.check_schedule_in_related_schedules(schedule):
        await channel.delete_schedule_from_related_schedules(schedule)
    else:
        await channel.add_schedule_to_related_schedules(schedule)
        is_send_last_post = True

    await Router.state_menu(callback_query, state)

    if is_send_last_post:
        await schedule.counter_messages(reset=True)
        await send_post_to_channel(schedule, channel, last=True)


def register_edit_schedule_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        edit_schedule_type_button,
        callback_mode="edit_schedule_type",
        chat_type="private",
        state=EditSchedule.ChooseField,
    )

    dp.register_callback_query_handler(
        schedules_list_button_handler,
        callback_mode="schedules",
        chat_type="private",
        state="*",
    )

    dp.register_callback_query_handler(
        edit_schedule_buttons_handler,
        callback_mode=[
            "schedule", "name",
            "time_period", "start_date",
            "count_days", "count_of_time_per_day",
            "frequency_message", "posts",
        ],
        chat_type="private",
        state=[EditSchedule.ChooseSchedule, EditSchedule.ChooseField],
    )

    dp.register_callback_query_handler(
        edit_schedule_start_time_button_handler,
        callback_mode="start_time",
        chat_type="private",
        state=EditSchedule.ChooseField,
    )

    dp.register_callback_query_handler(
        stop_schedule_button_handler,
        callback_mode="stop",
        chat_type="private",
        state=EditSchedule.ChooseField,
    )

    dp.register_callback_query_handler(
        activate_schedule_button_handler,
        callback_mode="activate",
        chat_type="private",
        state=EditSchedule.ChooseField,
    )

    dp.register_callback_query_handler(
        delete_schedule_button_handler,
        callback_mode="delete",
        chat_type="private",
        state=EditSchedule.ChooseField,
    )

    dp.register_callback_query_handler(
        delete_post_button_handler,
        callback_mode="delete_post",
        chat_type="private",
        state=EditSchedule.Posts,
    )

    dp.register_callback_query_handler(
        preview_post_button_handler,
        callback_mode="preview",
        chat_type="private",
        state=EditSchedule.Posts,
    )

    dp.register_callback_query_handler(
        show_html_tag_button_handler,
        callback_mode="show_html_tag",
        chat_type="private",
        state=EditSchedule.Posts,
    )

    dp.register_callback_query_handler(
        whole_day_button_handler,
        callback_mode="whole_day",
        chat_type="private",
        state=EditSchedule.TimePeriod,
    )

    dp.register_callback_query_handler(
        start_date_button_handler,
        callback_mode="schedule_date",
        chat_type="private",
        state=EditSchedule.StartDate,
    )

    dp.register_callback_query_handler(
        count_buttons_handler,
        callback_mode=["count_days", "count_of_time_per_day"],
        chat_type="private",
        state=[EditSchedule.CountDays, EditSchedule.CountOfTimePerDay],
    )

    dp.register_callback_query_handler(
        count_days_buttons_handler,
        callback_mode="count_days",
        chat_type="private",
        state=EditSchedule.ActivateSchedule,
    )

    dp.register_callback_query_handler(
        right_now_button_handler,
        callback_mode="right_now",
        chat_type="private",
        state=EditSchedule.StopSchedule,
    )

    dp.register_callback_query_handler(
        button_select_footer,
        callback_mode="select_footer",
        state=[EditSchedule.ChooseField, EditSchedule.Posts],
    )

    dp.register_callback_query_handler(
        schedule_public_button_handler,
        callback_mode="schedule_public",
        state=EditSchedule.ChooseField,
    )

    dp.register_callback_query_handler(
        change_public_schedule_button_handler,
        callback_mode="change_public_schedule",
        state=EditSchedule.ChooseSchedule,
    )

    dp.register_callback_query_handler(
        save_public_schedule_button_handler,
        callback_mode="save_public_schedule",
        state=EditSchedule.PublicSchedule,
    )
