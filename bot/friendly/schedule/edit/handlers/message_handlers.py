from typing import List

from aiogram import Dispatcher, types
from aiogram.types import ContentTypes
from aiogram.dispatcher import FSMContext
from aiogram_media_group import media_group_handler, MediaGroupFilter

from db.models import Schedule, User
from friendly.schedule.copy_posts.states import CopyPosts

from psutils.forms.helpers import delete_messages

from core.media import download_file

from utils.router import Router

from utils.text import f

from friendly.helpers import send_error

from friendly.main.keyboards import get_menu_keyboard

from friendly.schedule.edit.states import EditSchedule

from friendly.schedule.posts.functions import send_post_to_channel


async def posts_done_button_handler(message: types.Message, state: FSMContext, user: User, lang: str):
    if user.need_delete_friendly_posts:
        async with state.proxy() as state_data:
            messages = state_data.pop("messages", [])

        if messages:
            await delete_messages(message.from_user.id, messages)

    message_text = await f("posts saved message", lang)
    keyboard = await get_menu_keyboard(user, lang)
    await message.answer(message_text, reply_markup=keyboard)

    await EditSchedule.ChooseSchedule.set()
    await Router.state_menu(message, state)

    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))
    await schedule.counter_messages(reset=True)
    await send_post_to_channel(schedule, schedule.channel, last=True)


@media_group_handler
async def posts_media_group_handler(messages: List[types.Message], state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    for message in messages:
        file_path = await download_file(message)
        post = await schedule.add_post(message.content_type, media_path=file_path, text=message.caption)
        if not post:
            return await send_error(message)

    await messages[0].answer(await f("posts saved", lang))
    await Router.state_menu(messages[0], state)


async def posts_copy_posts_button_handler(message: types.Message, state: FSMContext):
    await CopyPosts.ChooseChannel.set()
    await state.update_data(prev_state="edit")
    await Router.state_menu(message, state)


def register_edit_schedule_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        posts_done_button_handler,
        chat_type="private",
        lequal="done button",
        content_types=ContentTypes.TEXT,
        state=EditSchedule.Posts,
    )

    dp.register_message_handler(
        posts_media_group_handler,
        MediaGroupFilter(),
        chat_type="private",
        content_types=ContentTypes.ANY,
        state=EditSchedule.Posts,
    )

    dp.register_message_handler(
        posts_copy_posts_button_handler,
        lequal="copy posts button",
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=[CopyPosts, EditSchedule.Posts],
    )
