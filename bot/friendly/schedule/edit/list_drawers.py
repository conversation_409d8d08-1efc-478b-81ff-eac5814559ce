from typing import Dict, Any, List, Literal

from aiogram.dispatcher import FSMContext

from db.models import Channel, Schedule, User

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn
from utils.router.route_handlers import BaseListDrawer

from utils.keyboards import previous_button, active_button


class ScheduleListDrawer(BaseListDrawer):

    row_width = 1

    config_page_size_variable_name = "FRIENDLY_SCHEDULE_LIST_PAGE_SIZE"

    need_previous_button = True
    need_setup_pagination_handler = True
    need_setup_search_handler = True

    message_text_variable = "pick schedule"
    empty_text_variable = "schedule list empty"

    pagination_callback_mode = "schedule_pagination"

    @classmethod
    async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
        state_data = await state.get_data()

        search_text = state_data.get(cls.search_key)
        channel_id = state_data.get("import_channel_id", state_data.get("channel_id"))
        public = state_data.get("public", False)
        need_add = state_data.get("need_add", True)
        return {"search_text": search_text, "channel_id": channel_id, "public": public, "need_add": need_add}

    @classmethod
    async def make_get_objects_kwargs(
            cls,
            user: User,
            search_text: str,
            data_from_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        channel_id = data_from_state.get("channel_id")
        public = data_from_state.get("public")
        return {"channel_id": channel_id, "search_text": search_text, "public": public}

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: Literal["all", "count"] = "all"
    ) -> List[Schedule]:
        channel_id = get_objects_kwargs.get("channel_id")
        search_text = get_objects_kwargs.get("search_text")
        public = get_objects_kwargs.get("public")
        schedules = await Schedule.get_channel_schedules(
            channel_id, position, limit, operation, search_text=search_text, public=public
        )
        return schedules

    @classmethod
    async def object_drawer(
            cls,
            schedule: Schedule,
            keyboard: InlineKb, lang: str,
            channel_id: int, public: bool = False
    ):
        if public:
            channel = await Channel.get(channel_id)

        active = await f("friendly schedule active icon", lang) if (not public and schedule.status == "active") or (
                public and await channel.get_status_related_schedule(schedule) == "active") else ""
        count_days = schedule.count_days if schedule.count_days else "-"
        schedule_name = " ".join([schedule.name, f"({schedule.channel.name})"]) if public else schedule.name

        schedule_btn_handle = await f(
            f"friendly schedule {schedule.schedule_type} button", lang,
            active=active,
            schedule_name=schedule_name,
            count_posts=len(schedule.posts),
            frequency_message=schedule.frequency_message,
            count_days=count_days
        )
        if public and await channel.check_schedule_in_related_schedules(schedule):
            schedule_btn_handle = await active_button(lang, schedule_btn_handle)

        callback_mode = "save_public_schedule" if public else "schedule"
        callback_data = c(callback_mode, schedule_id=schedule.id)

        button = InlineBtn(schedule_btn_handle, callback_data=callback_data)
        keyboard.insert(button)

    @classmethod
    async def footer_drawer(
            cls,
            keyboard: InlineKb, lang: str,
            need_add: bool = True
    ):
        if need_add:
            button_text = await f("add schedule button", lang)
            callback_data = "create_schedule"
            keyboard.row(InlineBtn(button_text, callback_data=callback_data))

            button_text = await f("change public schedule button", lang)
            callback_data = "change_public_schedule"
            keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        keyboard.insert(await previous_button(lang))
