from aiogram.dispatcher.filters.state import StatesGroup, State


class EditSchedule(StatesGroup):
    ChooseSchedule = State()
    ChooseField = State()
    Name = State()
    TimePeriod = State()
    StartTime = State()
    StartDate = State()
    CountDays = State()
    CountOfTimePerDay = State()
    FrequencyMessage = State()
    Posts = State()
    ActivateSchedule = State()
    StopSchedule = State()
    PublicSchedule = State()
