from typing import Dict, Any, List

from aiogram.dispatcher import FSMContext

from db.models import User, Channel, ClientBot

from utils.text import c
from utils.redefined_classes import InlineKb, InlineBtn
from utils.router.route_handlers import BaseListDrawer

from friendly.channel.db_funcs import get_admined_channels


class ChannelsListDrawer(BaseListDrawer):
    config_page_size_variable_name = "CHANNELS_LIST_PAGE_SIZE"

    need_setup_pagination_handler = True
    need_setup_search_handler = True
    need_previous_button = False

    message_text_variable = "choose channel for copy posts header"
    empty_text_variable = "empty channels list for copy posts header"
    pagination_callback_mode = "channels_pagination"

    @classmethod
    async def get_position(cls, state: FSMContext):
        state_data = await state.get_data()
        return state_data.get("channels_position", 0)

    @classmethod
    async def update_position(cls, new_position: int, state: FSMContext):
        await state.update_data(channels_position=new_position)

    @classmethod
    async def object_drawer(cls, channel: Channel, keyboard: InlineKb, lang: str):
        callback_data = c("channel", channel_id=channel.id)
        keyboard.insert(InlineBtn(channel.name, callback_data=callback_data))

    @classmethod
    async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
        state_data = await state.get_data()

        search_text = state_data.get(cls.search_key)
        bot_id = ClientBot.get_current_bot_id()
        return {"search_text": search_text, "bot_id": bot_id}

    @classmethod
    async def make_get_objects_kwargs(
            cls,
            user: User,
            search_text: str,
            data_from_state: Dict[str, Any],
    ) -> Dict[str, Any]:
        bot_id = data_from_state.get("bot_id")
        return {"user": user, "bot_id": bot_id, "search_text": search_text}

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0,
            limit: int = None, *,
            operation: str = "all",
    ) -> List[Any] | int:
        user: User = get_objects_kwargs.get("user")
        bot_id = get_objects_kwargs.get("bot_id")
        search_text = get_objects_kwargs.get("search_text")

        is_super_admin_mode = await user.is_friendly_super_admin(bot_id)

        return await get_admined_channels(user, bot_id, position, limit, search_text, operation, is_super_admin_mode)
