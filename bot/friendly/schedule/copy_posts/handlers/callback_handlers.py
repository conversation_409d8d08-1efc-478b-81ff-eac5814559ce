from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import Schedule
from friendly.schedule.edit.states import EditSchedule

from utils.router import Router

from friendly.schedule.copy_posts.states import CopyPosts

from friendly.schedule.create.states import CreateSchedule
from utils.text import f


async def channel_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    channel_id = callback_data.get("channel_id")
    await state.update_data(import_channel_id=channel_id)
    await CopyPosts.next()
    await Router.state_menu(callback_query, state)


async def schedule_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, lang: str,
):
    schedule = await Schedule.get(callback_data.get("schedule_id"))
    posts_for_copy = schedule.posts_for_copy
    async with state.proxy() as state_data:
        del state_data["import_channel_id"]
        state_data["posts"] = state_data.get("posts", list()) + posts_for_copy
        prev_state = state_data.get("prev_state", "crate")
        if prev_state == "edit":
            await EditSchedule.Posts.set()
            del state_data["prev_state"]
        else:
            await CreateSchedule.Posts.set()

    await Router.state_menu(callback_query, state)


async def previous_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    state_data = await state.get_data()
    one_channel_admin = state_data.get("one_channel_admin", False)
    if one_channel_admin:
        await CreateSchedule.Posts.set()
    else:
        await CopyPosts.ChooseChannel.set()
    await Router.state_menu(callback_query, state)


def register_schedule_copy_posts_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        channel_button_handler,
        chat_type="private",
        callback_mode="channel",
        state=CopyPosts.ChooseChannel,
    )

    dp.register_callback_query_handler(
        schedule_button_handler,
        chat_type="private",
        callback_mode="schedule",
        state=CopyPosts.ChooseSchedule,
    )

    dp.register_callback_query_handler(
        previous_button_handler,
        previous_button=True,
        chat_type="private",
        state=CopyPosts.ChooseSchedule,
    )
