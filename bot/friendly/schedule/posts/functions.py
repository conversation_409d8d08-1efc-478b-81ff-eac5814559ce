import logging

from aiogram.utils.exceptions import BadRequest

from db.models import Channel, ChatMember, Footer, Schedule, Post
from db.models import FriendlyBotAnalyticAction, SchedulesChannelsAssociation

from utils.redefined_classes import Bot
from utils.text import f

from friendly.footer import send_message_and_footer


async def send_post_part(post: Post, channel: Channel):
    footers = list()
    if post.footer:
        footers.append(post.footer)
    if post.schedule.footer:
        footers.append(post.schedule.footer)

    await send_message_and_footer(channel.chat_id, post.kwargs_for_send, None, footers)


async def send_post_to_channel(schedule: Schedule, channel: Channel, last: bool = False):
    if not schedule.posts:
        return

    bot = Bot.get_current()
    client_bot = channel.bot

    association = None if schedule.channel_id == channel.id else await SchedulesChannelsAssociation.get(
        schedule.id, channel.id
    )
    post = schedule.get_post(last, association=association)
    if not post:
        return

    with bot.with_token(client_bot.token):
        # отправляем пост в канал
        try:
            await send_post_part(post, channel)
        except BadRequest as e:
            if str(e) == "Have no rights to send a message":
                await schedule.channel.add_log(
                    await f(
                        "friendly logs warning schedule have no rights to send a message text", channel.lang,
                        schedule_name=schedule.name
                    )
                )
                return await post.schedule.stop() if post.schedule.channel_id == channel.id else await association.stop()

    await schedule.post_sent(last, association=association)

    chat_member = await ChatMember.get(user_id=schedule.user.id, channel_id=channel.id)
    chat_member_id = None
    if chat_member:
        chat_member_id = chat_member.id

    footer = None
    if isinstance(post, Post):
        footer = post.footer if post.footer else schedule.footer

    footer_kwargs = footer.kwargs_for_analitic if footer else {f"footer_{k}": None for k in Footer.KEYS_FOR_DICT}
    await FriendlyBotAnalyticAction.save_schedule_post_sent(
        member_id=chat_member_id, channel_id=channel.id,
        schedule_name=schedule.name, schedule_type=schedule.schedule_type,
        count_days=schedule.count_days, frequency_message=schedule.frequency_message,
        post_content_type=post.content_type,
        post_text=post.text if isinstance(post, Post) else post.event_title,
        post_media_path=post.media_path if isinstance(post, Post) else post.media,
        **footer_kwargs
    )


async def send_error_for_admins(schedule: Schedule, channel: Channel):
    association = None if schedule.channel_id == channel.id else await SchedulesChannelsAssociation.get(
        schedule.id, channel.id
    )
    post_index = association.index_send_message if association else schedule.index_send_message

    message_text = await f(
        "friendly schedule error send post to channel",
        channel.lang, post_index=post_index,
        schedule_name=schedule.name,
        channel_name=channel.name,
    )

    bot = Bot.get_current()
    client_bot = channel.bot
    with bot.with_token(client_bot.token):
        for admin in schedule.channel.admins:
            try:
                await bot.send_message(admin.chat_id, message_text)
            except Exception as e:
                logger = logging.getLogger()
                logger.error(e, exc_info=True)
