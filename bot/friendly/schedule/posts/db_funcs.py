from datetime import datetime

from sqlalchemy import or_, and_, func, case
from sqlalchemy.orm import aliased

from db import db_func, sess
from db.models import Schedule, Channel, Group, SchedulesChannelsAssociation


@db_func
def get_schedules_to_enable_deprecated() -> list:
    utc_current_datetime = datetime.utcnow()
    channel_local_time = func.convert_tz(
        utc_current_datetime, "UTC", Group.timezone)

    query = sess().query(Schedule, Channel)
    query = query.join(Group, Channel.group_id == Group.id)
    query = query.outerjoin(SchedulesChannelsAssociation,
                            SchedulesChannelsAssociation.channel_id == Channel.id)

    query = query.filter(or_(
        Schedule.channel_id == Channel.id,
        Schedule.id == SchedulesChannelsAssociation.schedule_id,
    ))

    count_messages = case([(Schedule.channel_id == Channel.id, Schedule.count_messages,), ],
                          else_=SchedulesChannelsAssociation.count_messages)
    index_send_message = case([(Schedule.channel_id == Channel.id, Schedule.index_send_message,), ],
                              else_=SchedulesChannelsAssociation.index_send_message)
    last_sent_post_datetime = case([(Schedule.channel_id == Channel.id, Schedule.last_sent_post_datetime,), ],
                                   else_=SchedulesChannelsAssociation.last_sent_post_datetime)
    status = case([(Schedule.channel_id == Channel.id, Schedule.status,), ],
                  else_=SchedulesChannelsAssociation.status)

    query = query.filter(Schedule.start_time <= channel_local_time)
    query = query.filter(or_(
        Schedule.end_time.is_(None),
        Schedule.end_time >= channel_local_time
    ))
    query = query.filter(Schedule.frequency_message != 0)
    between_posts = and_(
        count_messages != 0,
        count_messages >= Schedule.frequency_message
    )

    last_sent_post_datetime_filter = or_(
        last_sent_post_datetime.is_(None),
        func.date(last_sent_post_datetime) <= utc_current_datetime.date(),
    )

    count_days_passed = func.datediff(
        utc_current_datetime.date(), Schedule.start_date)

    count_days_filter = and_(
        Schedule.count_days.is_not(None),
        or_(
            Schedule.count_days == 0,
            count_days_passed < Schedule.count_days,
        )
    )

    count_messages_per_day = or_(
        and_(
            count_days_passed == 0,
            count_messages < Schedule.frequency_message
        ),
        count_messages < Schedule.frequency_message * count_days_passed,
    )

    all_minutes = (12 * 60) / Schedule.frequency_message
    hours = all_minutes / 60
    minutes = all_minutes % 60
    check_time = func.subtime(utc_current_datetime,
                              func.maketime(hours, minutes, 0))

    day_send = and_(
        Schedule.start_date <= utc_current_datetime.date(),
        last_sent_post_datetime_filter,
        count_days_filter,
        count_messages_per_day,
        or_(
            last_sent_post_datetime.is_(None),
            last_sent_post_datetime < check_time,
        ),
    )

    schedule_type_filter = case(
        [
            (Schedule.schedule_type == "between_posts", between_posts,),
            (Schedule.schedule_type == "by_time", day_send,),
        ],
    )

    query = query.filter(schedule_type_filter)
    query = query.filter(status == "active")

    schedules_channels = query.all()
    return schedules_channels


@db_func
def get_schedules_to_enable() -> list:
    utc_current_datetime = datetime.utcnow()
    channel_local_time = func.convert_tz(
        utc_current_datetime, "UTC", Group.timezone)

    Schedules = aliased(Schedule)
    Channels = aliased(Channel)
    Groups = aliased(Group)
    Sca = aliased(SchedulesChannelsAssociation)

    query = sess().query(
        Schedules,
        Channels,
        Sca,
        Groups
    ).outerjoin(
        Sca, Sca.schedule_id == Schedules.id
    ).outerjoin(
        Channels, Channels.id == Sca.channel_id
    ).join(
        Groups, Groups.id == Channels.group_id
    ).union(
        sess().query(
            Schedules,
            Channels,
            Sca,
            Groups
        ).join(
            Channels, Schedules.channel_id == Channels.id
        ).join(
            Groups, Groups.id == Channels.group_id
        ).outerjoin(
            Sca, and_(
                Sca.channel_id == Channels.id,
                Sca.schedule_id == Schedules.id
            )
        )
    )

    query = query.filter(Schedule.start_time <= channel_local_time)
    query = query.filter(or_(
        Schedule.end_time.is_(None),
        Schedule.end_time >= channel_local_time
    ))
    query = query.filter(Schedule.frequency_message != 0)

    count_messages = case([(Schedule.channel_id == Channel.id, Schedule.count_messages,), ],
                          else_=SchedulesChannelsAssociation.count_messages)
    index_send_message = case([(Schedule.channel_id == Channel.id, Schedule.index_send_message,), ],
                              else_=SchedulesChannelsAssociation.index_send_message)
    last_sent_post_datetime = case([(Schedule.channel_id == Channel.id, Schedule.last_sent_post_datetime,), ],
                                   else_=SchedulesChannelsAssociation.last_sent_post_datetime)
    status = case([(Schedule.channel_id == Channel.id, Schedule.status,), ],
                  else_=SchedulesChannelsAssociation.status)

    between_posts = and_(
        count_messages != 0,
        count_messages >= Schedule.frequency_message
    )

    last_sent_post_datetime_filter = or_(
        last_sent_post_datetime.is_(None),
        func.date(last_sent_post_datetime) <= utc_current_datetime.date(),
    )

    count_days_passed = func.datediff(
        utc_current_datetime.date(), Schedule.start_date)

    count_days_filter = and_(
        Schedule.count_days.is_not(None),
        or_(
            Schedule.count_days == 0,
            count_days_passed < Schedule.count_days,
        )
    )

    count_messages_per_day = or_(
        and_(
            count_days_passed == 0,
            count_messages < Schedule.frequency_message
        ),
        count_messages < Schedule.frequency_message * count_days_passed,
    )

    all_minutes = (12 * 60) / Schedule.frequency_message
    hours = all_minutes / 60
    minutes = all_minutes % 60
    check_time = func.subtime(utc_current_datetime,
                              func.maketime(hours, minutes, 0))

    day_send = and_(
        Schedule.start_date <= utc_current_datetime.date(),
        last_sent_post_datetime_filter,
        count_days_filter,
        count_messages_per_day,
        or_(
            last_sent_post_datetime.is_(None),
            last_sent_post_datetime < check_time,
        ),
    )

    schedule_type_filter = case(
        [
            (Schedule.schedule_type == "between_posts", between_posts,),
            (Schedule.schedule_type == "by_time", day_send,),
        ],
    )

    query = query.filter(schedule_type_filter)
    query = query.filter(status == "active")

    schedules_channels = query.all()
    return schedules_channels


@db_func
def get_schedules_for_stopped() -> list:
    utc_current_datetime = datetime.utcnow()

    query = sess().query(Schedule)
    query = query.join(Channel, Schedule.channel_id == Channel.id)
    query = query.join(Group, Channel.group_id == Group.id)

    count_days_filter = and_(
        Schedule.count_days.is_not(None),
        Schedule.count_days != 0,
    )

    count_days_passed = func.datediff(
        utc_current_datetime.date(), Schedule.start_date)

    count_days_and_passed = and_(
        count_days_filter,
        count_days_passed >= Schedule.count_days,
    )

    query = query.filter(count_days_and_passed)

    query = query.filter(Schedule.status == "active")

    return query.all()
