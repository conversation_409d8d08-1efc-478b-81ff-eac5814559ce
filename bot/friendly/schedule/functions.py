from db.models import Channel, ClientBot, Schedule, SchedulesChannelsAssociation


async def add_counter(chat_id: int):
    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=chat_id, bot_id=bot_from_db.id)
    for schedule in channel.schedules:
        await schedule.counter_messages()

    for schedule in channel.related_schedules:
        association = await SchedulesChannelsAssociation.get(schedule.id, channel.id)
        await schedule.counter_messages(association=association)


async def get_schedules_count(channel_id: int) -> int:
    return await Schedule.get_channel_schedules(channel_id, operation="count")
