from datetime import datetime, time, timedelta

from config import FRIENDLY_SCHEDULE_COUNT_DAYS_BUTTONS, FRIENDLY_SCHEDULE_COUNT_OF_TIME_PER_DAY_BUTTONS

from psutils.date_time import get_local_time, localise_datetime
from psutils.convertors import time_to_str

from utils.redefined_classes import InlineKb, InlineBtn

from utils.text import f, c

from utils.keyboards import get_navigation_keyboard


async def get_schedule_time_period_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)

    button_text = await f("whole day", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="whole_day"))

    return await get_navigation_keyboard(lang, keyboard)


async def get_schedule_frequency_message_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=4)

    default_values = [5, 10, 15, 20, 30, 40, 50, 60, 70, 80, 100, 150, 200]

    for default_value in default_values:
        button_text = await f("message to users", lang, message_text=default_value)
        keyboard.insert(InlineBtn(button_text, callback_data=c("posts_interval", count=default_value)))

    return await get_navigation_keyboard(lang, keyboard)


async def get_schedule_date_keyboard(lang: str, keyboard: InlineKb = None) -> InlineKb:
    if not keyboard:
        keyboard = InlineKb(row_width=2)

    button_text = await f("schedule date today button", lang)
    callback_data = c("schedule_date", day="today")
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("schedule date tomorrow button", lang)
    callback_data = c("schedule_date", day="tomorrow")
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    return await get_navigation_keyboard(lang, keyboard)


async def get_schedule_time_keyboard(timezone: str, lang: str, keyboard: InlineKb = None) -> InlineKb:
    main_keyboard = True
    if not keyboard:
        keyboard = InlineKb(row_width=2)
        main_keyboard = False

    current_time = get_local_time(timezone)
    button_text = await f("schedule time now button", lang, current_time=time_to_str(current_time))
    callback_data = c("schedule_date", day="now")
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    in_one_hour = datetime.utcnow() + timedelta(hours=1)
    in_one_hour = localise_datetime(in_one_hour, timezone)
    round_time = time(in_one_hour.hour, 0)
    button_text = await f("schedule time round button", lang, round_time=time_to_str(round_time))
    callback_data = c("schedule_date", day="round")
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if main_keyboard:
        return keyboard
    return await get_navigation_keyboard(lang, keyboard)


async def get_schedule_datetime_keyboard(timezone: str, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=4)
    keyboard = await get_schedule_time_keyboard(timezone, lang, keyboard)
    keyboard = await get_schedule_date_keyboard(lang, keyboard)
    return keyboard


async def get_schedule_count_days_keyboard(lang: str, disable: bool = True) -> InlineKb:
    keyboard = InlineKb(row_width=2)

    if disable:
        button_text = await f("schedule count days disable button", lang)
        callback_data = c("count_days", count=None)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("schedule count days constantly button", lang)
    callback_data = c("count_days", count=0)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    for number in FRIENDLY_SCHEDULE_COUNT_DAYS_BUTTONS:
        callback_data = c("count_days", count=number)
        keyboard.insert(InlineBtn(str(number), callback_data=callback_data))

    return await get_navigation_keyboard(lang, keyboard)


async def get_schedule_count_of_time_per_day_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=3)

    for number in FRIENDLY_SCHEDULE_COUNT_OF_TIME_PER_DAY_BUTTONS:
        callback_data = c("count_of_time_per_day", count=number)
        keyboard.insert(InlineBtn(str(number), callback_data=callback_data))

    return await get_navigation_keyboard(lang, keyboard)


__all__ = [
    "get_schedule_time_period_keyboard",
    "get_schedule_date_keyboard",
    "get_schedule_time_keyboard",
    "get_schedule_datetime_keyboard",
    "get_schedule_count_days_keyboard",
    "get_schedule_count_of_time_per_day_keyboard",
    "get_schedule_frequency_message_keyboard",
]
