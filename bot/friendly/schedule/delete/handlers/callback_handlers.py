from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import Schedule

from utils.router import Router

from utils.text import f

from friendly.helpers import send_error

from friendly.schedule.delete.states import DeleteSchedule

from friendly.schedule.edit.states import EditSchedule


async def yes_delete_schedule_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    result = await schedule.delete()
    if not result:
        return await send_error(callback_query.message)

    await EditSchedule.first()
    await Router.state_menu(callback_query, state, lang)


async def no_delete_schedule_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await callback_query.answer(await f("action cancel text", lang))
    await EditSchedule.ChooseField.set()
    await Router.state_menu(callback_query, state, lang)


def register_delete_schedule_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        yes_delete_schedule_button_handler,
        yes_button=True,
        chat_type="private",
        state=DeleteSchedule,
    )

    dp.register_callback_query_handler(
        no_delete_schedule_button_handler,
        no_button=True,
        chat_type="private",
        state=DeleteSchedule,
    )
