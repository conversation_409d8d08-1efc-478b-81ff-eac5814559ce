from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Schedule

from utils.message import send_or_edit_message

from utils.router import Router

from utils.text import f

from utils.keyboards import get_yes_or_no_keyboard

from friendly.schedule.delete.states import DeleteSchedule


async def send_delete_schedule_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    schedule = await Schedule.get(state_data.get("schedule_id"))

    message_text = await f("delete schedule confirmation header", lang, schedule_name=schedule.name)
    keyboard = await get_yes_or_no_keyboard(lang)

    return await send_or_edit_message(message, message_text, keyboard=keyboard)


def register_delete_schedule_routes(router: Router):
    router.add_route(DeleteSchedule, send_delete_schedule_menu)
