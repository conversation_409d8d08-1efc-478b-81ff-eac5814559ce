from typing import Dict, Any, List, Tuple

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import Channel, ClientBot, User, Group, FriendlyChatListParams

from core.mailing import BaseMailing
from core.mailing.base.base_create_mailing_form import create_form

from psutils.forms.helpers import with_delete_state_messages

from utils.router import Router

from friendly.channel.db_funcs import get_admined_channels
from friendly.channel.db_funcs import get_users

from .states import LocationMailingState
from friendly.channel.edit.states import EditChannel


class LocationMailing(BaseMailing):

    state_group = LocationMailingState

    @classmethod
    async def get_users(cls, mailing_creator: User, state: FSMContext) -> List[Tuple[int, int, str, str, str | None]]:
        users_data = await get_users(mailing_creator.id)
        return users_data

    @classmethod
    async def make_get_groups_kwargs(cls, user: User, state_data: dict):
        bot_id = ClientBot.get_current_bot_id()
        chats_lists_params = await FriendlyChatListParams.get(user.id, bot_id)

        channels_mode = chats_lists_params.channels_mode
        list_params = getattr(chats_lists_params, channels_mode)

        return {
            "user": user,
            "bot_id": bot_id,
            "search_text": list_params.get("search_text"),
            "is_active_chats_filter": list_params.get("is_active_chats_filter", True),
            "is_active_channels_filter": list_params.get("is_active_channels_filter", True),
            "is_active_is_admin_filter": list_params.get("is_active_is_admin_filter", True),
            "is_active_is_not_admin_filter": list_params.get("is_active_is_not_admin_filter", True),
            "location_id_filter": list_params.get("location_id_filter", None),
        }

    @classmethod
    async def get_groups(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0,
            limit: int = None, *,
            operation: str = "all",
            only_names: bool = False,
    ) -> list[Group] | list[str] | int:
        admin_channels = await cls.get_channels(
            get_objects_kwargs,
            position, limit,
            operation=operation,
        )

        if operation == "count":
            return admin_channels

        if only_names:
            return [channel.name for channel in admin_channels]

        return [channel.group for channel in admin_channels]

    @classmethod
    async def get_sender_groups(cls, user: User, state_data: dict) -> Group | list[Group]:
        kwargs = await cls.make_get_groups_kwargs(user, state_data)
        sender_groups = await cls.get_groups(kwargs)
        return sender_groups

    @classmethod
    async def get_channels(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0,
            limit: int = None, *,
            operation: str = "all",
            only_names: bool = False,
    ) -> List[Channel] | int:
        user = get_objects_kwargs.get("user")
        bot_id = get_objects_kwargs.get("bot_id")
        search_text = get_objects_kwargs.get("search_text")
        is_active_chats_filter = get_objects_kwargs.get("is_active_chats_filter", True)
        is_active_channels_filter = get_objects_kwargs.get("is_active_channels_filter", True)
        is_active_is_admin_filter = get_objects_kwargs.get("is_active_is_admin_filter", True)
        is_active_is_not_admin_filter = get_objects_kwargs.get("is_active_is_not_admin_filter", True)
        location_id_filter = get_objects_kwargs.get("location_id_filter", None)
        channels_mode = get_objects_kwargs.get("channels_mode", "admin")
        is_super_admin_mode = True if channels_mode == "super_admin" else False

        admin_channels = await get_admined_channels(
            user, bot_id,
            position, limit,
            search_text, operation,
            is_super_admin_mode,
            is_active_chats_filter,
            is_active_channels_filter,
            is_active_is_admin_filter,
            is_active_is_not_admin_filter,
            location_id_filter,
        )

        if operation == "count":
            return admin_channels

        if only_names:
            return [channel.name for channel in admin_channels]

        return admin_channels

    @classmethod
    async def get_bots_ids(
            cls,
            user: User, state_data: dict,
            users_and_bots_ids_and_mailing_mode = None
    ) -> list[int] | set[int]:
        kwargs = await cls.make_get_groups_kwargs(user, state_data)
        sender_channels = await cls.get_channels(kwargs)
        return set([sender_channel.bot_id for sender_channel in sender_channels])

    @classmethod
    async def back_to_previous_state(cls, message: types.Message, state: FSMContext, user: User, lang: str):
        await EditChannel.ChooseChannel.set()
        await Router.state_menu(message, state, lang)

    @classmethod
    @with_delete_state_messages
    async def previous_button_handler(
            cls,
            callback_query: types.CallbackQuery,
            state: FSMContext,
            user: User,
            lang: str,
    ):
        cur_state = await state.get_state("%:%")
        state_data = await state.get_data()

        state_name = cur_state.split(":")[-1]

        if state_name == "CreateMessage" or state_data.get("is_groups_skipped", False):
            await cls.back_to_previous_state(callback_query.message, state, user, lang)
        else:
            await cls.state_group.previous()
            await Router.state_menu(callback_query, state, lang)

    @classmethod
    def setup_mailing(cls, dp: Dispatcher):

        router: Router = dp["router"]

        form = create_form(cls.state_group)

        form.setup_handlers(dp)

        dp.register_callback_query_handler(
            cls.previous_button_handler,
            previous_button=True,
            state=cls.state_group,
        )

        router.add_route(cls.state_group.CreateMessage, cls.send_create_message_menu)
        router.add_route(cls.state_group.RunMailing, cls.run_mailing)
