from typing import List, Tuple

from sqlalchemy import desc, func, or_, distinct

from db import db_func, sess
from db.models import Channel, ChatMember, ClientBot, Group, User

from core.mailing.base.db_funcs import get_mailing_mode_subquery


@db_func
def get_channels(
        user_id: int,
        bot_id: int,
        position: int = 0,
        limit: int = None,
        operation: str = "all",
        location_id_filter: str | None = None,
) -> int | List[Channel]:
    if operation == "count":
        query = sess().query(func.count(Channel.id))
    else:
        query = sess().query(Channel)

    if location_id_filter:
        query = query.join(Channel.group)
        query = query.filter(Group.location == location_id_filter)

    query = query.join(ChatMember, ChatMember.channel_id == Channel.id)

    query = query.filter(ChatMember.user_id == user_id)
    query = query.filter(Channel.bot_id == bot_id)

    if operation == "count":
        return query.scalar() - position

    query = query.order_by(ChatMember.joined_datetime.desc())

    slice_args = [position, None]
    if limit:
        slice_args[1] = position + limit
    query = query.slice(*slice_args)

    return query.all()


@db_func
def get_admined_channels(
        user: User,
        bot_id: int,
        position: int = 0,
        limit: int = None,
        search_text: str = None,
        operation: str = "all",
        is_super_admin_mode: bool = False,
        is_active_chats_filter: bool = True,
        is_active_channels_filter: bool = True,
        is_active_is_admin_filter: bool = True,
        is_active_is_not_admin_filter: bool = True,
        location_id_filter: str | None = None,
) -> int | List[Channel]:

    is_chat = Channel.chat_type.in_(["group", "supergroup"])

    if operation == "count":
        query = sess().query(func.count(distinct(Channel.id)))
    else:
        query = sess().query(Channel).distinct()

    if is_super_admin_mode:
        query = query.join(Channel.bot)
        query = query.join(ClientBot.group)

        query = query.filter(or_(
            Group.owner.has(id=user.id),
            Group.admins.any(user_id=user.id, is_superadmin_friendly_bot=True),
        ))

        admin_filters = (is_active_is_admin_filter, is_active_is_not_admin_filter)
        if not all(admin_filters) and any(admin_filters):
            value = True if is_active_is_admin_filter else False
            query = query.filter(Channel.is_bot_admin.is_(value))

        chat_filters = (is_active_chats_filter, is_active_channels_filter)
        if not all(chat_filters) and any(chat_filters):
            value = ["group", "supergroup"] if is_active_chats_filter else ["channel"]
            query = query.filter(Channel.chat_type.in_(value))
    else:
        query = query.filter(Channel.admins.any(id=user.id))

    if location_id_filter:
        query = query.join(Channel.group)
        query = query.filter(Group.location == location_id_filter)

    query = query.filter(Channel.bot_id == bot_id)

    if search_text:
        query = query.filter(or_(
            Channel.name.contains(search_text),
            Channel.username.contains(search_text),
        ))

    if operation == "count":
        return query.scalar()

    query = query.order_by(desc(is_chat))
    query = query.order_by(desc(Channel.is_bot_admin.is_(True)))
    query = query.order_by(desc(Channel.attach_datetime))

    slice_args = [position, None]
    if limit:
        slice_args[1] = position + limit
    query = query.slice(*slice_args)
    return query.all()


@db_func
def get_users(
        user_id: int,
) -> List[Tuple[int, int, str, str, str | None]]:
    mailing_mode = get_mailing_mode_subquery(User.id, ClientBot.id, ClientBot.group_id)
    query = sess().query(User.id, ClientBot.id, ClientBot.username, Group.name, mailing_mode).distinct()

    query = query.join(ClientBot.group)
    query = query.join(Channel, Channel.bot_id == ClientBot.id)
    query = query.join(ChatMember, ChatMember.channel_id == Channel.id)

    query = query.filter(Group.owner_id == user_id)
    query = query.filter(User.id == ChatMember.user_id)

    return query.all()
