from aiogram import Dispatcher, types
from utils.filters.simple import group_message_filter
from friendly.channel.limitations.functions import limit_user


async def cmd_set_limit(message: types.Message):
    await limit_user(message, by_command=True, is_delete_limit_message=False)


async def cmd_set_limit_del(message: types.Message):
    await limit_user(message, by_command=True, is_delete_limit_message=True)


def register_channel_limitation_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cmd_set_limit,
        group_message_filter,
        commands="setlimit",
        is_reply=True,
        state="*"
    )

    dp.register_message_handler(
        cmd_set_limit_del,
        group_message_filter,
        commands="setlimitdel",
        is_reply=True,
        state="*"
    )
