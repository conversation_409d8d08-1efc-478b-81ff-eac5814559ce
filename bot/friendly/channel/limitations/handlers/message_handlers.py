from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.types import ContentTypes

from config import DEBUG
from db.models import Channel, ChatMember, ClientBot, FriendlyBotAnalyticAction, User
from friendly.draw.functions import draw_user_write_message_channel
from friendly.schedule.functions import add_counter
from loggers import J<PERSON><PERSON>ogger
from utils.filters.simple import filter_chat_menu, group_message_filter
from utils.text import f
from ..functions import (
    delete_message_and_send_deletion, limit_user, send_menu_button_message,
    send_required_message, send_rules_ban_message, send_rules_notification_message,
    send_threshold_alert,
)
from ...add.functions import add_channel


async def messages_from_sender_chat_handler(message: types.Message):
    if message.chat.id == message.sender_chat.id:
        return
    if message.chat.id == message.sender_chat.linked_chat_id:
        return
    if message.chat.linked_chat_id == message.sender_chat.id:
        return
    if message.is_automatic_forward:
        return

    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=message.chat.id, bot_id=bot_from_db.id)

    logger = JSONLogger(
        "friendly.sender-chat", {
            "message": message,
            "channel": {
                "id": channel.id,
                "name": channel.name,
            },
            "bot": {
                "id": bot_from_db.id,
                "name": bot_from_db.display_name,
            }
        }
    )
    if DEBUG:
        logger.debug("Deleting message from sender_chat")
    await message.delete()


async def group_messages_handler(message: types.Message, state: FSMContext, user: User):
    if message.is_automatic_forward:
        return

    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=message.chat.id, bot_id=bot_from_db.id)

    logger = JSONLogger(
        "group_messages_handler", {
            "message": message,
            "channel": {
                "id": channel.id,
                "name": channel.name,
                "group_id": channel.group_id,
            },
            "bot": {
                "id": bot_from_db.id,
                "name": bot_from_db.display_name,
            }
        }
    )

    if message.migrate_to_chat_id:
        return await channel.update(chat_id=message.migrate_to_chat_id)

    if not channel:
        channel = await add_channel(message.chat)

    await channel.update_chat_info(message.chat)

    chat_member = await ChatMember.create_or_get(user, channel)
    # chat_member будет None если группу не добавили в бота
    if not chat_member:
        return

    if not channel.is_bot_admin:
        if channel.group_id:
            await channel.add_log(
                await f("friendly logs error bot is not admin text", channel.lang)
            )
        else:
            logger.add_data({"group": channel.group})
            logger.error("Bot is not has channel.group and channel.lang")
        return await add_counter(channel.chat_id)

    is_sender_chat = bool(message.sender_chat)
    # admins and creators can send as many messages as they desire
    allowed_messages = "infinite" if is_sender_chat else await (
        chat_member.get_messages_left())
    telegram_member_obj = await message.chat.get_member(message.from_user.id)
    unlimited = allowed_messages == "infinite"
    is_admin = telegram_member_obj.status in ["creator", "administrator"]

    allowed_to_write = type(allowed_messages) is int and allowed_messages > 0
    rules_ok = chat_member.check_rules_agreement()

    forbidden_word = await channel.validate_user_message_is_safe(message, chat_member)
    length_rule_ok = await channel.validate_max_length_message(message)

    need_delete = False
    reason = None
    max_length = None

    logger = JSONLogger(
        "friendly.limitations", {
            "channel": {
                "id": channel.id,
                "name": channel.name,
            },
            "bot": {
                "id": bot_from_db.id,
                "name": bot_from_db.display_name,
            },
            "user": message.from_user,
        }
    )

    # Проверяем, учавствует ли пользователь в розыграшах
    await draw_user_write_message_channel(message, user)

    if is_sender_chat:
        return

    if not is_admin:
        if rules_ok is False:
            await chat_member.update_messages_sent_without_rules()
            if (channel.allowed_messages_given_without_rules <=
                    chat_member.messages_sent_without_rules):
                need_delete = True
                reason = "rules"
            else:
                rules_ok = True
        # должно быть именно forbidden_word is not True, потому что в случае находки
        # запрещённых слов вернётся строка
        elif forbidden_word is not True:
            need_delete = True
            reason = "filters"
        elif allowed_to_write is False and not unlimited:
            need_delete = True
            reason = "messages_limit"
        elif length_rule_ok is False:
            need_delete = True
            reason = f"max_length_messages"
            max_length = channel.max_messages_length

    await FriendlyBotAnalyticAction.save_message_sent(
        member_id=chat_member.id,
        message_type=message.content_type,
        unlimited=unlimited,
        is_deleted=need_delete,
        reason=reason,
        max_length=max_length,
    )

    if not need_delete:
        await add_counter(channel.chat_id)

    if is_admin:
        return

    if isinstance(forbidden_word, str):
        return await limit_user(message)

    if rules_ok is None:
        if DEBUG:
            logger.debug(
                f"Deleting message: rules is not OK in chat {channel.name}: "
                f"{message.from_user.full_name}",
            )
        await message.delete()
        if chat_member.ban_user_status:
            await send_rules_ban_message(chat_member)
        else:
            await send_rules_notification_message(chat_member, message_was_deleted=True)
        await FriendlyBotAnalyticAction.save_message_deleted_by_rules(chat_member.id)

    # Кол-во сообщений, которые написал пользователь
    user_send_messages = await chat_member.messages_sent_with_unlimited - 1
    # Кол-во сообщений, которые можно написать в чат перед отправкой сообщений
    max_messages_before_required = channel.max_count_messages_before_required_resource
    # Включена подписка на ресурс и у пользователя закончились сообщения либо
    # превышен лимит сообщений перед отправкой
    allowed_to_write = True if unlimited else allowed_to_write
    if channel.required_resource_subscription_chat_id and \
            allowed_to_write and \
            user_send_messages >= max_messages_before_required:
        await send_required_message(message, channel, chat_member, user_send_messages)

    # если не осталось сообщений
    if not allowed_to_write or not length_rule_ok:
        await delete_message_and_send_deletion(
            message, state, channel, chat_member, not length_rule_ok
        )

        is_ban_limit_surpassed = await chat_member.is_ban_limit_surpassed()
        if is_ban_limit_surpassed:
            await chat_member.ro()
            await FriendlyBotAnalyticAction.save_user_banned(chat_member.id, channel.id)
        return

    if isinstance(allowed_messages, int):
        allowed_messages -= 1

    # если достигнет alert_threshold
    if (isinstance(
            allowed_messages, int
    ) and allowed_messages <= channel.alert_threshold and channel.messages_given !=
        0) or channel.alert_threshold <= 0:
        await send_threshold_alert(
            message, state, channel, chat_member, allowed_messages
        )


async def menu_buttons_handler(message: types.Message):
    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=message.chat.id, bot_id=bot_from_db.id)

    await send_menu_button_message(channel, message.text)


def register_channel_limitations_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        messages_from_sender_chat_handler,
        is_sender_chat=True,
        state="*",
    )

    dp.register_message_handler(
        menu_buttons_handler,
        filter_chat_menu,
        chat_type=["group", "supergroup", "channel"],
        content_types=ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        group_messages_handler,
        group_message_filter,
        lambda msg: msg.content_type not in ("left_chat_member", "left_chat_member"),
        content_types=ContentTypes.ANY,
        state="*",
    )
