import asyncio
import re
from contextlib import suppress
from typing import Dict, List, Tuple

from aiogram import types
from aiogram.dispatcher import FSMContext
from aiogram.utils.exceptions import (
    MessageCantBeDeleted, MessageIdentifierNotSpecified, MessageToDeleteNotFound,
)
from psutils.forms.helpers import delete_messages

from config import DEBUG
from core.user.functions import create_or_update_messanger_user
from db.models import (
    Channel, ChatMember, ClientBot, FriendlyBotAnalyticAction, FriendlyChatMessage,
    User, UserClientBotActivity,
)
from friendly.channel.edit.functions import get_chat_or_bot_info
from friendly.footer import send_message_and_footer
from friendly.helpers import are_user_and_bot_chat_admins
from loggers import J<PERSON>NLogger
from utils.message import send_tg_message
from utils.redefined_classes import Bot
from utils.text import f
from .keyboards import (
    get_instructions_keyboard, get_rules_keyboard,
    parse_keyboard_in_text,
)
from ...main.keyboards import get_system_message_keyboard


async def _delete_message(token: str, chat_id: int, message_id: int):
    bot = Bot.get_current()
    with suppress(MessageCantBeDeleted, MessageToDeleteNotFound):
        with bot.with_token(token):
            await bot.delete_message(chat_id, message_id)


async def delete_limited_message(
        message: types.Message,
        channel: Channel,
        bot_from_db: ClientBot,
        is_delete_limit_message: bool = True,
):
    loop = asyncio.get_running_loop()
    bot = Bot.get_current()
    on_screen_time = channel.limited_message_on_screen_time

    with bot.with_token(bot_from_db.token):
        coro_kwargs = dict(
            chat_id=message.chat.id, message_id=message.message_id,
            token=bot_from_db.token
        )
        loop.call_later(
            on_screen_time, asyncio.create_task, _delete_message(**coro_kwargs)
        )

        if not message.reply_to_message:
            return

        if is_delete_limit_message:
            coro_kwargs.update(message_id=message.reply_to_message.message_id)
            loop.call_later(
                on_screen_time, asyncio.create_task, _delete_message(**coro_kwargs)
            )

        friendly_chat_message = await FriendlyChatMessage.get_chat_message(
            message.reply_to_message
        )
        if friendly_chat_message:
            user_limited = friendly_chat_message.member.user
        else:
            user_limited = await create_or_update_messanger_user(
                message.reply_to_message, bot_from_db
            )

        chat_member = await ChatMember.create_or_get(user_limited, channel)
        await FriendlyBotAnalyticAction.save_message_deleted_by_restriction(
            member_id=chat_member.id,
            message_type=message.reply_to_message.content_type,
        )


def _parse_args(text: str) -> Tuple[Dict[str, int] | None, List[str] | None, str]:
    if not text:
        return None, None, ""

    ratio_search = re.search(r"\d+[/\\]\d+", text)
    if ratio_search:
        ratio_str = ratio_search.group()
        text = text.replace(ratio_str, "")
        messages_for_friends, friends_required = map(int, re.split(r"[/\\]", ratio_str))
        ratio_dict = dict(
            messages_for_friends=messages_for_friends, friends_required=friends_required
        )
    else:
        ratio_dict = None

    usernames_search = re.search(
        r"(?:@[a-z\d_]{5,}(?:, ?)?)+", text, flags=re.IGNORECASE
    )
    if usernames_search:
        usernames_str = usernames_search.group()
        text = text.replace(usernames_str, "")
        usernames = re.findall(r"@[a-z\d_]{5,}", usernames_str, flags=re.IGNORECASE)
    else:
        usernames = None

    return ratio_dict, usernames, text


async def limit_user(
        message: types.Message, by_command: bool = False,
        is_delete_limit_message: bool = True
):
    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=message.chat.id, bot_id=bot_from_db.id)

    logger = JSONLogger(
        "friendly.limitations", {
            "channel": {
                "id": channel.id,
                "name": channel.name,
            },
            "bot": {
                "id": bot_from_db.id,
                "name": bot_from_db.display_name,
            },
            "user": message.from_user,
        }
    )

    if message.chat.type not in ["supergroup", "group"]:
        lang = channel.lang if channel else await User.get_lang(message.chat.id)
        return await message.reply(
            await f("setlimit cant be used outside of group chat", lang)
        )

    if not await are_user_and_bot_chat_admins(
            message.chat, message.from_user.id, bot_from_db.telegram_bot_id
    ):
        if DEBUG:
            logger.debug(
                f"Deleting /limit command message(user or bot not chat admins) "
                f"in chat {channel.name}: {message.from_user.full_name}"
            )
        return await message.delete()

    if DEBUG:
        logger.debug(
            f"Deleting limited message(limit_user) in chat {channel.name}: "
            f"{message.from_user.full_name}",
        )
    await delete_limited_message(message, channel, bot_from_db, is_delete_limit_message)

    if message.reply_to_message:
        message = message.reply_to_message
        need_limit_user = True
    else:
        need_limit_user = not message.is_command()

    friendly_chat_message = await FriendlyChatMessage.get_chat_message(message)
    if friendly_chat_message:  # если сообщение бота, то берём пользователя из
        # сохранённого сообщения чата
        filter_type = "username"
        ratio_dict, usernames, filter_text = (None, None,
                                              friendly_chat_message.user_first_name)
        user_to_limit = friendly_chat_message.member.user
    else:
        user_to_limit = await create_or_update_messanger_user(
            message
        ) if need_limit_user else None
        filter_type = "message"
        ratio_dict, usernames, filter_text = _parse_args(message.get_args())

    if usernames is not None:
        await limit_usernames(channel, usernames, ratio_dict)

    if filter_text:
        await channel.append_filter(filter_type, filter_text)

    if not user_to_limit:
        return

    chat_member = await ChatMember.create_or_get(user_to_limit, channel)
    await chat_member.set_limit(
        ratio_dict
    ) if ratio_dict else await chat_member.increase_limit()

    hide_name = filter_type == "username"
    await send_restriction_message(
        message.chat.id, chat_member, channel, hide_name, by_command
    )


async def limit_usernames(
        channel: Channel, usernames: List[str], ratio: Dict[str, int | float] = None
):
    for username in usernames:
        username = username.replace("@", "")
        member = await ChatMember.get_member_by_username(username, channel.id)
        if member is None:
            member_user = await create_or_update_messanger_user(username)
            member = await ChatMember.create_or_get(member_user, channel)
        await member.increase_limit() if not ratio else await member.set_limit(ratio)


async def send_restriction_message(
        chat_id: int, member: ChatMember,
        channel: Channel, hide_name: bool = False,
        by_command: bool = False,
):
    if channel.ratio(member) > channel.ratio():
        return

    if not channel.get_chat_message_status("restriction", "show"):
        return

    if not by_command and not channel.check_is_allowed_to_send("restriction"):
        return

    user = member.user

    restricted_user_first_name = user.first_name
    restricted_user_full_name = user.full_name

    if hide_name:
        restricted_user_username = await f("limited user name", channel.lang)
        restricted_user_first_name = await f("limited user name", channel.lang)
        restricted_user_full_name = await f("limited user name", channel.lang)
    elif not user.username:
        restricted_user_username = user.user_url
    else:
        restricted_user_username = f"@{user.username}"

    bot_username = await ClientBot.get_current_bot_username()
    keyboard = await get_system_message_keyboard(channel, bot_username)

    message_data = await channel.get_message(
        "restriction",
        add_at_to_username=True,
        username=restricted_user_username,
        firstname=restricted_user_first_name,
        user_first_name=restricted_user_first_name,
        fullname=restricted_user_full_name,
        user_full_name=restricted_user_full_name,
    )

    footer = await channel.get_footer("restriction")

    restriction_message = await send_message_and_footer(
        chat_id, message_data, keyboard, footer
    )

    if not by_command:
        await channel.save_sent_rate_limited_message("restriction")

    if channel.need_delete_restriction_notification:
        bot_from_db = await ClientBot.get_current()
        await FriendlyChatMessage.save(
            restriction_message,
            "restriction_message",
            bot=bot_from_db,
            member=member
        )


async def delete_message_and_send_deletion(
        message: types.Message, state: FSMContext,
        channel: Channel, chat_member: ChatMember,
        is_broke_length_rule: bool,
):
    bot_from_db = await ClientBot.get_current()

    logger = JSONLogger(
        "friendly.limitations", {
            "channel": {
                "id": channel.id,
                "name": channel.name,
            },
            "bot": {
                "id": bot_from_db.id,
                "name": bot_from_db.display_name,
            },
            "user": message.from_user,
        }
    )
    if DEBUG:
        logger.debug(
            f"Deleting limited message in chat {channel.name}: "
            f"{message.from_user.full_name}",
        )
    with suppress(MessageCantBeDeleted, MessageToDeleteNotFound):
        await message.delete()

    await FriendlyBotAnalyticAction.save_message_deleted_by_bot(
        chat_member.id, is_broke_length_rule, channel.max_messages_length,
    )

    if not channel.get_chat_message_status("deletion", "show"):
        return

    if not channel.check_is_allowed_to_send("deletion"):
        return

    state_data = await state.get_data()
    deletion_message_id = state_data.get("deletion_message_id")

    with suppress(MessageCantBeDeleted, MessageToDeleteNotFound):
        if deletion_message_id:
            if DEBUG:
                logger.debug(
                    f"Deleting previous deletion message in chat {channel.name}:  "
                    f"{message.from_user.full_name}",
                )
            await message.bot.delete_message(message.chat.id, deletion_message_id)

    first_name = message.from_user.first_name
    full_name = message.from_user.full_name
    username = message.from_user.username

    message_data = await channel.get_message(
        "deletion",
        add_at_to_username=True,
        firstname=first_name,
        user_first_name=first_name,
        fullname=full_name,
        user_full_name=full_name,
        username=username,
    )

    keyboard = await get_instructions_keyboard(channel)
    footer = await channel.get_footer("deletion")

    deletion_message = await send_message_and_footer(
        message.chat.id, message_data, keyboard, footer
    )

    await channel.save_sent_rate_limited_message("deletion")

    bot_from_db = await ClientBot.get_current()
    await FriendlyChatMessage.save(
        deletion_message, "deletion_message",
        bot_from_db, None, chat_member,
        message.from_user.first_name,
    )

    if deletion_message:
        await state.update_data(deletion_message_id=deletion_message.message_id)


async def send_threshold_alert(
        message: types.Message, state: FSMContext,
        channel: Channel, chat_member: ChatMember,
        allowed_messages: int
):
    bot_from_db = await ClientBot.get_current()

    logger = JSONLogger(
        "friendly.limitations", {
            "channel": {
                "id": channel.id,
                "name": channel.name,
            },
            "bot": {
                "id": bot_from_db.id,
                "name": bot_from_db.display_name,
            },
            "user": message.from_user,
        }
    )

    await FriendlyBotAnalyticAction.save_user_hit_threshold(chat_member.id)

    if not channel.get_chat_message_status("alert", "show"):
        return

    if not channel.check_is_allowed_to_send("alert"):
        return

    state_data = await state.get_data()
    alert_message_id = state_data.get("alert_message_id")
    with suppress(
            MessageCantBeDeleted, MessageToDeleteNotFound, MessageIdentifierNotSpecified
    ):
        if DEBUG:
            logger.debug(
                f"Deleting previous alert message in chat {channel.name}: "
                f"{message.from_user.full_name}",
            )
        await delete_messages(message.chat.id, alert_message_id)

    first_name = message.from_user.first_name
    full_name = message.from_user.full_name
    username = message.from_user.username

    message_data = await channel.get_message(
        "alert",
        add_at_to_username=True,
        firstname=first_name,
        user_first_name=first_name,
        fullname=full_name,
        user_full_name=full_name,
        usernmae=username,
        messages_left=allowed_messages,
        allowed_messages=allowed_messages,
    )

    keyboard = await get_instructions_keyboard(channel)
    footer = await channel.get_footer("alert")

    alert_message = await send_message_and_footer(
        message.chat.id, message_data, keyboard, footer
    )
    if alert_message:
        await state.update_data(alert_message_id=alert_message.message_id)

    await channel.save_sent_rate_limited_message("alert")

    await FriendlyChatMessage.save(
        alert_message, "alert_message",
        bot_from_db, None, chat_member,
        message.from_user.first_name,
    )


async def send_rules_notification_message(
        chat_member: ChatMember, *, message_was_deleted: bool
):
    client_bot = await ClientBot.get_current()

    channel = chat_member.channel
    lang = channel.lang

    if not channel.get_chat_message_status("rules", "show"):
        return

    if channel.get_chat_message_status("rules", "default"):
        message_deleted = await f(
            "your message has been deleted", lang
        ) if message_was_deleted else ""
        text = await f(
            "write in bot must read rules", lang,
            full_name=chat_member.user.name,
            message_deleted=message_deleted,
        )

        message_data = {
            "text": text,
            "content_type": "text"
        }
    else:
        allowed_messages, messages_for_friends, friends_required = await (
            chat_member.get_limits_info_texts())

        first_name = chat_member.user.first_name
        full_name = chat_member.user.full_name
        username = chat_member.user.username

        message_data = await channel.get_message(
            "rules",
            add_at_to_username=True,
            firstname=first_name,
            user_first_name=first_name,
            fullname=full_name,
            user_full_name=full_name,
            username=username,
            friends_added=await chat_member.get_invites_count(),
            friends_required=friends_required,
            messages_given=messages_for_friends,
            allowed_messages=allowed_messages,
            chat_name=channel.get_link(),
        )

    keyboard = await get_rules_keyboard(channel, client_bot.username)
    if not chat_member.rules_start_datetime:
        await chat_member.set_rules_start_datetime()

    footer = await channel.get_footer("rules")
    message = await send_message_and_footer(
        channel.chat_id, message_data, keyboard, footer
    )
    await FriendlyChatMessage.save(
        message,
        "rules_message_notification",
        client_bot,
        member=chat_member,
        user_first_name=chat_member.user.first_name,
    )


async def send_rules_ban_message(chat_member: ChatMember):
    client_bot = await ClientBot.get_current()
    user = chat_member.user
    channel = chat_member.channel

    first_name = user.first_name
    full_name = user.full_name
    username = user.username

    message_data = await channel.get_message(
        "rules_ban",
        add_at_to_username=True,
        firstname=first_name,
        user_first_name=first_name,
        fullname=full_name,
        user_full_name=full_name,
        username=username,
    )

    keyboard = await get_rules_keyboard(channel, client_bot.username)
    footer = await channel.get_footer("rules_ban")

    message = await send_message_and_footer(
        channel.chat_id, message_data, keyboard, footer
    )

    await FriendlyChatMessage.save(
        message, "rules_ban", client_bot,
        member=chat_member,
        user_first_name=user.first_name,
    )


async def send_subscribe_and_delete_message(
        channel: Channel, user: User, message: types.Message
):
    first_name = user.first_name
    full_name = user.full_name
    username = user.username
    chat_info = await get_chat_or_bot_info(message, channel)

    bot_from_db = await ClientBot.get_current()

    logger = JSONLogger(
        "friendly.limitations", {
            "channel": {
                "id": channel.id,
                "name": channel.name,
            },
            "bot": {
                "id": bot_from_db.id,
                "name": bot_from_db.display_name,
            },
            "user": message.from_user,
        }
    )

    message_data = await channel.get_message(
        "required_subscription",
        add_at_to_username=True,
        firstname=first_name,
        user_first_name=first_name,
        fullname=full_name,
        user_full_name=full_name,
        username=username,
        channel_username=chat_info.username
    )
    footer = await channel.get_footer("required_subscription")

    try:
        if DEBUG:
            logger.debug(
                f"Deleting message(subscribe required) in chat {channel.name}: "
                f"{message.from_user.full_name}",
            )
        await message.delete()
    except Exception as e:
        if DEBUG:
            logger.error(
                f"An error occurred while deleting message(subscribe required)"
                f"in chat {channel.name}: {message.from_user.full_name}",
                repr(e)
            )

    if not channel.check_is_allowed_to_send("required_subscription"):
        return

    last_required_resource_msg_id = channel.last_required_resource_msg_id
    msg = await send_message_and_footer(channel.chat_id, message_data, None, footer)
    if last_required_resource_msg_id:
        try:
            if DEBUG:
                logger.debug(
                    f"Deleting previous last_required_resource message "
                    f"in chat {channel.name}: {message.from_user.full_name}",
                )
            await message.bot.delete_message(
                chat_id=channel.chat_id, message_id=last_required_resource_msg_id
            )
        except:
            pass
    await channel.update(last_required_resource_msg_id=msg.message_id)
    await channel.save_sent_rate_limited_message("required_subscription")


def get_title(text: str, is_delete_contacts: bool = False):
    title = text[:300]
    res = ""
    if ' ' in title:
        title = title.split(' ')[:-1]
        for x in title:
            res += x + " "
    else:
        res = title
    if is_delete_contacts:
        res = res.replace('@', '')
    return res


async def send_menu_button_message(channel: Channel, button_text: str):
    button = await channel.get_menu_buttons(button_text=button_text)
    if button:
        text_without_tags, keyboard = parse_keyboard_in_text(button.message)

        data = {
            "text": text_without_tags,
            "content_type": button.content_type
        }
        if button.content_type == "photo":
            data["photo"] = button.media
        elif button.content_type == "video":
            data["video"] = button.media

        # Если изначальный текст кнопки отличается от text_without_tags значит были
        # найдены кнопки
        if button.message != text_without_tags:
            data["keyboard"] = keyboard

        await send_tg_message(channel.chat_id, **data)


async def send_required_message(
        message: types.Message, channel: Channel, chat_member: ChatMember,
        user_send_messages: int
):
    bot_from_db = await ClientBot.get_current()
    logger = JSONLogger(
        "friendly.limitations", {
            "channel": {
                "id": channel.id,
                "name": channel.name,
            },
            "bot": {
                "id": bot_from_db.id,
                "name": bot_from_db.display_name,
            },
            "user": message.from_user,
        }
    )

    # Проверяем, является ли ресрс ботом
    required_resource_bot = await ClientBot.get(
        channel.required_resource_subscription_chat_id
    )
    if required_resource_bot:
        required_resource_type = "bot"
        # Проверяем, подписан ли человек на бота
        user_bot_activity = await UserClientBotActivity.get(
            user=chat_member.user, bot=required_resource_bot
        )
        if user_bot_activity and user_bot_activity.is_entered_bot:
            return
        await send_subscribe_and_delete_message(channel, chat_member.user, message)

    else:
        # Ресурс является чатом или каналом
        required_resource_type = "group_or_channel"
        try:
            user_status = await message.bot.get_chat_member(
                channel.required_resource_subscription_chat_id,
                chat_member.user.chat_id
            )
        except Exception as e:
            logger.error(
                f"An error occurred while getting chat_member status "
                f"in chat {channel.name}: {message.from_user.full_name}: {repr(e)}",
                {
                    "chat_member_id": chat_member.id
                }
            )
            return

        # Проверяем, подписан ли пользователь на чат/канал
        if user_status["status"] == "left":
            await send_subscribe_and_delete_message(channel, chat_member.user, message)
        else:
            return

    await FriendlyBotAnalyticAction.save_subscribe_required_resource(
        member_id=chat_member.id,
        channel_id=channel.id,
        required_resource_id=channel.required_resource_subscription_chat_id,
        required_resource_type=required_resource_type,
        user_send_messages=user_send_messages
    )
