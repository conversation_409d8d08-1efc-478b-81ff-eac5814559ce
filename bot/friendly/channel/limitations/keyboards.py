import re

from db.models import Channel, ClientBot
from friendly.main.keyboards import get_contact_admin_button

from utils.redefined_classes import InlineKb, InlineBtn


async def get_instructions_keyboard(channel: Channel) -> InlineKb:
    lang = channel.lang

    bot_username = await ClientBot.get_current_bot_username(no_error=False)

    keyboard = InlineKb()

    button_text = await channel.get_button_text("info", lang)
    keyboard.insert(InlineBtn(button_text, url=channel.get_info_link(bot_username)))

    keyboard.insert(await get_contact_admin_button(channel, bot_username, lang))
    return keyboard


async def get_rules_keyboard(channel: Channel, bot_username: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)
    button_text = await channel.get_button_text("rules", channel.lang)
    url = f"https://t.me/{bot_username}?start=rules_chid-{channel.id}"
    keyboard.insert(InlineBtn(button_text, url))
    return keyboard


# __parse_keyboard__
def split_button(_button: str):
    button_split = _button.replace('[', '').replace(']', '')
    button_split = button_split.split("=")
    return button_split[0], button_split[1]


def get_link_in_button_context(_button_context: str):
    if "link:" not in _button_context:
        return
    _link = _button_context.split("link:")[1]
    return _link


def parse_keyboard_in_text(text: str):
    text_without_tags = text
    keyboard = InlineKb(row_width=1)

    buttons_objects = re.findall(r"\[.*]", text)
    if not buttons_objects:
        return text_without_tags, keyboard

    for button_object in buttons_objects:
        if '=' not in button_object:
            continue
        button_text, button_context = split_button(button_object)
        link = get_link_in_button_context(button_context)
        if not link:
            continue
        keyboard.row(InlineBtn(button_text, url=link))
        text_without_tags = text_without_tags.replace(button_object, '')

    return text_without_tags, keyboard

# ____
