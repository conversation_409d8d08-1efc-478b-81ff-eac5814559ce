import asyncio
from datetime import datetime, timedelta

from db import own_session
from db.models import Channel


@own_session
async def channel_refresh_messages():
    loop = asyncio.get_event_loop()
    channels = await Channel.get_all()

    now = datetime.utcnow()
    now = now.date()
    tomorrow = now + timedelta(days=1)
    timeout = (tomorrow - now).total_seconds()

    for channel in channels:
        if now != channel.refresh_datetime.date():
            await channel.refresh_messages()
    loop.call_later(timeout, asyncio.create_task, channel_refresh_messages())
