from typing import Dict, Any, List, Tuple

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import User, Group, Channel, ClientBot

from core.mailing import BaseMailing
from utils.router import Router

from ..db_funcs import get_users

from .states import CorrespondencesMailingState
from ..states import Correspondences


class CorrespondencesMailing(BaseMailing):

    state_group = CorrespondencesMailingState

    @classmethod
    async def get_users(cls, mailing_creator: User, state: FSMContext) -> List[Tuple[int, int, str, str, str | None]]:
        state_data = await state.get_data()

        bot = await ClientBot.get_current()
        channel_id = state_data.get("channel_id")
        filters = state_data.get("filters") or ["active"]
        search_text = state_data.get("search_text")

        sender_group_id = state_data.get("sender_group_id")

        channel = await Channel.get(channel_id)

        users = await get_users(
            str(bot.id), channel.id, filters,
            search_text=search_text,
            for_mailing=True,
            bot_username=str(bot.username),
            sender_group_id=sender_group_id,
        )

        return users

    @classmethod
    async def make_get_groups_kwargs(cls, user: User, state_data: dict):
        channel = await Channel.get(state_data.get("channel_id"))
        return {"channel": channel}

    @classmethod
    async def get_groups(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0,
            limit: int = None, *,
            operation: str = "all",
            only_names: bool = False,
    ) -> List[Group] | int:
        channel = get_objects_kwargs.get("channel")

        if operation == "count":
            return 1

        if only_names:
            return [channel.group.name]

        return [channel.group]

    @classmethod
    async def back_to_previous_state(cls, message: types.Message, state: FSMContext, user: User, lang: str):
        await Correspondences.set()
        await Router.state_menu(message, state, lang)
