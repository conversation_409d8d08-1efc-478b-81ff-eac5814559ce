from typing import List, Literal, Tuple

from sqlalchemy import desc, func, case, or_, and_, text, distinct
from sqlalchemy.orm import aliased

from db import sess, db_func
from db.models import Channel, Chat, ChatMember, ChatMessage, User, UserClientBotActivity, Group
from core.mailing.base.db_funcs import get_mailing_mode_subquery
from schemas import ChatType<PERSON>num


@db_func
def get_last_message_for_user_in_channel(bot_id: int, channel_id: int, user_id: int) -> ChatMessage:
    query = sess().query(ChatMessage)
    query = query.join(Channel, Channel.group_id == ChatMember.group_id)
    query = query.join(ChatMessage.chat)

    query = query.filter(Channel.id == channel_id)
    query = query.filter(ChatMessage.bot_id == bot_id)
    query = query.filter(ChatMessage.user_id == user_id)
    query = query.filter(Chat.type == ChatTypeEnum.USER_WITH_GROUP)

    query = query.order_by(ChatMessage.id.desc()).limit(1)
    return query.one_or_none()


def get_last_message_query():
    message_query = sess().query(
        ChatMessage.id,
        ChatMessage.group_id,
        ChatMessage.user_id,
        func.lower(ChatMessage.sent_by),
        ChatMessage.text,
        func.lower(ChatMessage.content_type),
        ChatMessage.time_created,
    )
    message_query = message_query.join(ChatMessage.chat)
    message_query = message_query.filter(Chat.type == ChatTypeEnum.USER_WITH_GROUP)

    message_query = message_query.filter(ChatMessage.is_last.is_(True))
    return message_query.subquery()


def get_added_friends_query():
    friends_query = sess().query(ChatMember)

    friends = aliased(ChatMember)
    friends_query = friends_query.filter(ChatMember.id == friends.inviter_id)

    return friends_query.subquery()


@db_func
def get_users(
        bot_id: int, channel_id: int,
        filters: List[Literal[
            "all", "active", "setlimit",
            "start", "wrote",
            "favorites", "added", "left",
        ]],
        position: int = 0, limit: int = None,
        operation: Literal["all", "count"] = "all",
        search_text: str = None,
        for_mailing: bool = False,
        bot_username: str = None,
        sender_group_id: int = None,
) -> List[Tuple[ChatMember, tuple]] | List[Tuple[int, int, str, str, str | None]] | int:
    last_message = get_last_message_query()

    if not for_mailing:
        query = sess().query(ChatMember, last_message).distinct()
    else:
        mailing_mode = get_mailing_mode_subquery(User.id, Channel.bot_id, sender_group_id)
        query = sess().query(User.id, text(f"'{bot_id}'"), text(f"'{bot_username}'"), Group.name, mailing_mode)

    if for_mailing:
        query = query.join(User.chats_members)
    else:
        query = query.join(ChatMember.user)

    query = query.join(ChatMember.channel)

    if for_mailing:
        query = query.join(Channel.group)

    query = query.outerjoin(
        last_message, and_(
            last_message.c.user_id == ChatMember.user_id,
            last_message.c.group_id == Channel.group_id,
        )
    )

    if search_text:
        query = query.filter(
            or_(
                User.name.contains(search_text),
                User.username.contains(search_text),
            )
        )

    query = query.filter(Channel.id == channel_id)

    query = query.join(User.client_bot_activities)

    query = query.filter(UserClientBotActivity.bot_id == bot_id)

    if for_mailing:
        query = query.filter(UserClientBotActivity.is_entered_bot.is_(True))

    if "all" not in filters:
        filters_conditions = list()

        if "setlimit" in filters:
            filters_conditions.append(ChatMember.exception_ratio.is_not(None))

        if "start" in filters:
            filters_conditions.append(UserClientBotActivity.is_entered_bot.is_(True))

        if "wrote" in filters or "active" in filters:
            filters_conditions.append(last_message.c.id.is_not(None))

        if "favorites" in filters:
            filters_conditions.append(ChatMember.is_favorite.is_(True))

        if "added" in filters or "active" in filters:
            friends_query = get_added_friends_query()
            query = query.outerjoin(
                friends_query, and_(
                    friends_query.c.user_id == ChatMember.user_id,
                    friends_query.c.channel_id == ChatMember.channel_id,
                )
            )
            filters_conditions.append(friends_query.c.id.is_not(None))

        if "left" in filters:
            filters_conditions.append(
                and_(
                    ChatMember.left_datetime.is_not(None),
                    ChatMember.left_datetime > ChatMember.joined_datetime
                )
            )

        query = query.filter(or_(*filters_conditions))

    if "left" not in filters:
        query = query.filter(
            or_(
                ChatMember.left_datetime.is_(None),
                ChatMember.joined_datetime > ChatMember.left_datetime,
            )
        )

    if operation == "count":
        return query.with_entities(func.count(distinct(ChatMember.id))).scalar()

    case_order = case(
        [
            ("favorites" in filters, ChatMember.is_favorite,),
            (last_message.c.id.is_not(None), last_message.c.time_created,),
        ],
        else_=ChatMember.joined_datetime
    )

    query = query.group_by(ChatMember.id)

    query = query.order_by(desc(case_order))

    slice_args = [position, None]
    if limit:
        slice_args[1] = position + limit
    query = query.slice(*slice_args)

    return query.all()
