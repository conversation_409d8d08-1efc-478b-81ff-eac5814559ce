from contextlib import suppress

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.utils.exceptions import MessageNotModified

from db.models import ChatMember

from psutils.forms.helpers import with_delete_state_messages

from utils.text import f
from utils.router import Router

from friendly.helpers import send_error
from ..mailing.states import CorrespondencesMailingState

from ..functions import send_messages_limit

from ..states import Correspondences
from ...edit.states import EditChannel


async def correspondences_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    channel_id = callback_data.get("channel_id")
    await state.update_data(channel_id=channel_id)
    await state.update_data(filters=["active",])

    await Correspondences.set()
    await Router.state_menu(callback_query, state)


async def filter_type_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, lang: str,
):
    filter_type = callback_data.get("type")

    async with state.proxy() as state_data:
        state_filters_types = state_data.get("filters", ["active",])

        if state_filters_types is not None and filter_type in state_filters_types:
            state_filters_types.remove(filter_type)
        else:
            state_filters_types.append(filter_type)
        state_data["filters"] = state_filters_types

    await Router.state_menu(callback_query, state, lang)


async def change_favorite_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
        lang: str,
):
    state_data = await state.get_data()

    user_id = callback_data.get("user_id")
    channel_id = state_data.get("channel_id")

    member = await ChatMember.get(user_id, channel_id)
    result = await member.change_favorite() if member else False
    if not result:
        await send_error(callback_query)
    else:
        added_or_removed = "added to" if member.is_favorite else "removed from"
        await callback_query.answer(await f(f"user {added_or_removed} favorites", lang))

    await Router.state_menu(callback_query, state, lang)


async def change_ban_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
        lang: str,
):
    member_id = callback_data.get("member_id")
    member = await ChatMember.get(chat_member_id=member_id)

    status = not member.ban_user_status
    result = await ChatMember.set_ban_users_status(member.id, status) if member else False
    if not result:
        await send_error(callback_query)
    else:
        ban_or_unban = "ban" if status else "unban"
        await callback_query.answer(await f(f"user {ban_or_unban} text", lang))

    await Router.state_menu(callback_query, state, lang)


async def show_messages_limit_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
        mode: str, lang: str,
):
    member_id = callback_data.get("member_id")
    await send_messages_limit(callback_query.message, state, member_id, mode, lang)

    await Router.state_menu(callback_query, state, mode="new")


@with_delete_state_messages
async def update_list_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, lang: str,
):
    with suppress(MessageNotModified):
        await Router.state_menu(callback_query, state, lang)


async def mailing_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await CorrespondencesMailingState.first()
    await Router.state_menu(callback_query, state, lang)


@with_delete_state_messages
async def previous_menu_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    state_data = await state.get_data()
    await state.finish()

    channel_id = state_data.get("channel_id")
    await state.update_data(channel_id=channel_id)

    await EditChannel.first()
    await Router.state_menu(callback_query, state)


def register_channel_correspondences_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        correspondences_button_handler,
        callback_mode="correspondences",
        state="*",
    )

    dp.register_callback_query_handler(
        filter_type_button_handler,
        callback_mode="filter_type",
        state=Correspondences,
    )

    dp.register_callback_query_handler(
        change_favorite_button_handler,
        callback_mode="change_favorite",
        state=Correspondences,
    )

    dp.register_callback_query_handler(
        change_ban_button_handler,
        callback_mode="ban_or_unban",
        state=Correspondences,
    )

    dp.register_callback_query_handler(
        show_messages_limit_button_handler,
        callback_mode=["restriction", "message_filter"],
        state=Correspondences,
    )

    dp.register_callback_query_handler(
        update_list_button_handler,
        callback_mode="update_list",
        state=Correspondences,
    )

    dp.register_callback_query_handler(
        mailing_button_handler,
        callback_mode="mailing",
        state="*",
    )

    dp.register_callback_query_handler(
        previous_menu_button_handler,
        previous_button=True,
        chat_type="private",
        state=Correspondences,
    )
