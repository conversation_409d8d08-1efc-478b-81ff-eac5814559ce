from datetime import datetime
from typing import Literal

from aiogram import types
from aiogram.dispatcher import FSMContext

from config import FRIENDLY_USERS_CORRESPONDENCE_TEXT_MESSAGE_SIZE
from db.models import Channel, ChatMember, ChatMessage, FriendlyBotAnalyticAction, User

from dateutil.relativedelta import relativedelta

from psutils.date_time import localise_datetime

from psutils.forms.helpers import save_messages_to_state

from schemas import ChatMessageSentByEnum
from utils.message import send_tg_message

from utils.text import f

from utils.router.route_handlers import MessageData


async def get_object_info(member: ChatMember, lang: str) -> str:
    allowed_messages, messages_for_friends, friends_required = await member.get_limits_info_texts()
    ratio = await f("ratio text", lang, messages_for_friends=messages_for_friends, friends_required=friends_required)

    count_invites = await member.get_invites_count() if member else 0
    write_messages = await member.messages_sent

    return await f(
        "correspondences info button", lang,
        name=member.user.name,
        invites=count_invites,
        write_messages=write_messages,
        allowed_messages=allowed_messages,
        ratio=ratio,
    )


async def get_message_info(last_message: ChatMessage | MessageData, user: User, lang: str) -> str:
    msg_limit = FRIENDLY_USERS_CORRESPONDENCE_TEXT_MESSAGE_SIZE

    from_or_to = "from" if last_message and last_message.sent_by == ChatMessageSentByEnum.USER else "to"
    direction = await f(f"correspondences message {from_or_to} user", lang)

    message_text = await f("correspondences start text", lang) if not last_message else last_message.text_field
    if last_message.content_type != "text":
        message_text = await f(f"content type {last_message.content_type} text", lang)

    if message_text is None:
        message_text = ""
    message_text = message_text[:msg_limit]

    result = await f(
        "correspondences message button", lang,
        direction=direction,
        user_full_name=user.name,
        text=message_text,
    )
    return result


async def send_messages_limit(
        message: types.Message,
        state: FSMContext,
        member_id: int,
        reason: Literal["restriction", "message_filter"],
        lang: str
):
    data = await FriendlyBotAnalyticAction.get_member_setlimit_reasons(member_id, reason)

    if data:
        for word_ban, message_ban in data:
            word = f"{word_ban} - " if reason == "message_filter" else ""
            message_text = f"{word}{message_ban}"
            msg = await send_tg_message(message.chat.id, "text", text=message_text)
            await save_messages_to_state(state, msg)
    else:
        message_text = await f("messages deleted list empty", lang)
        msg = await send_tg_message(message.chat.id, "text", text=message_text)
        await save_messages_to_state(state, msg)
    await message.delete()


async def get_channel_statistics_header(channel: Channel, lang: str) -> str:
    current_date = localise_datetime(datetime.utcnow(), channel.group.timezone).date()

    a = await channel.get_saldo(current_date)
    b = await channel.get_saldo(current_date - relativedelta(days=1))
    c = await channel.get_saldo(current_date - relativedelta(weeks=1))
    d = await channel.get_saldo(current_date - relativedelta(months=3))

    text = await f(
        "friendly correspondences header", lang,
        channel_name=channel.name,
        a=a, b=b, c=c, d=d,
    )
    return text
