from contextlib import suppress
from typing import Dict, Any, List, Literal, Tuple

from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext
from aiogram.dispatcher.filters.state import State, StatesGroupMeta

from core.helpers import get_crm_chat_link
from db import crud
from db.models import Channel, ChatMember, ChatMessage, ClientBot, User
from schemas import ChatTypeEnum

from utils.keyboards import previous_button, active_button

from psutils.forms.helpers import with_delete_state_messages

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn

from utils.router import Router
from utils.router.route_handlers import BaseInfoMessageListDrawer, MessageData

from .db_funcs import get_last_message_for_user_in_channel, get_users

from .functions import get_object_info, get_message_info

from .states import Correspondences


class CorrespondenceListDrawer(BaseInfoMessageListDrawer):

    row_width = 2

    remove_search_text_variable = "rm search button"
    empty_text_variable = "list correspondence is empty"

    config_page_size_variable_name = "FRIENDLY_USERS_CORRESPONDENCE_LIST_PAGE_SIZE"

    pagination_callback_mode = "correspondence_pagination"

    filter_types = [
        "active", "setlimit",
        "start", "wrote",
        "favorites", "added", "left",
    ]

    @classmethod
    async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
        state_data = await state.get_data()
        timezone = user.db_timezone

        data = dict(
            bot_id=ClientBot.get_current_bot_id(),
            channel_id=state_data.get("channel_id"),
            filters=state_data.get("filters", ["active"]),
            selected=state_data.get("selected", list()),
            search_text=state_data.get("search_text"),
            radio_position=state_data.get("radio_position"),
            timezone=timezone,
        )
        return data

    @classmethod
    async def make_get_objects_kwargs(
            cls, user: User,
            search_text: str,
            data_from_state: Dict[str, Any],
    ) -> Dict[str, Any]:
        channel = await Channel.get(data_from_state.get("channel_id"))
        data = dict(
            bot_id=data_from_state.get("bot_id"),
            filters=data_from_state.get("filters"),
            channel_id=channel.id,
            group_id=channel.group_id,
            search_text=search_text,
        )
        return data

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0,
            limit: int = None, *,
            operation: Literal["all", "count"] = "all",
    ) -> List[Tuple[ChatMember, MessageData]] | int:

        bot_id = get_objects_kwargs.get("bot_id")
        channel_id = get_objects_kwargs.get("channel_id")
        filters = get_objects_kwargs.get("filters")
        search_text = get_objects_kwargs.get("search_text")

        result = await get_users(bot_id, channel_id, filters, position, limit, operation, search_text)

        if operation == "count":
            return result

        return list(map(lambda x: (x[0], MessageData(*x[1:])), result))

    @classmethod
    async def get_object_info(cls, object: Any, is_selected: bool, timezone: str, lang: str) -> str:
        return await get_object_info(object, lang)

    @classmethod
    async def get_last_message(cls, object: Any) -> ChatMessage:
        return await get_last_message_for_user_in_channel(object.bot.id, object.channel.id, object.user.id)

    @classmethod
    async def get_message_info(cls, last_message: ChatMessage | MessageData, object: Any, lang: str):
        return await get_message_info(last_message, object.user, lang)

    @classmethod
    async def get_message_callback_data(cls, chat_member: ChatMember, **kwargs):
        channel = await Channel.get(chat_member.channel_id)
        bot = await ClientBot.get(channel.bot_id)
        user = await User.get_by_id(chat_member.user_id)

        chat = await crud.get_or_create_chat(
            ChatTypeEnum.USER_WITH_GROUP,
            user.id,
            channel.group_id,
            bot.id,
        )

        return dict(
            url=get_crm_chat_link(chat.id)
        )

    @classmethod
    async def header_drawer(
            cls, bot_id: int, channel_id: int,
            filters: List[str], keyboard: InlineKb, lang: str
    ):
        users_count = dict.fromkeys(cls.filter_types, "")

        filter_texts = dict.fromkeys(cls.filter_types)
        for filter_type in cls.filter_types:
            filter_texts[filter_type] = await f(
                f"{filter_type} users button", lang, users_count=users_count[filter_type]
            )
            if filter_type in filters:
                filter_texts[filter_type] = await active_button(lang, filter_texts[filter_type])

        for filter, button_text in filter_texts.items():
            keyboard.insert(InlineBtn(button_text, callback_data=c("filter_type", type=filter)))

    @classmethod
    async def draw_selected_object(cls, object: Any, obj_position: int, keyboard: InlineKb, lang: str):
        admins = [admin.id for admin in object.channel.admins]

        callback_data = c("select_object", id=object.id, p=obj_position)
        if object.ban_user_status:
            status = "ban"
        elif object.user.id in admins:
            status = "admin"
        else:
            status = "normal"
        button_text = await f(f"user {status} status text", lang)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        keyboard.row()
        callback_data = c("restriction", member_id=object.id)
        button_text = await f("messages deleted by restriction button", lang)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        callback_data = c("message_filter", member_id=object.id)
        button_text = await f("messages deleted by message filter button", lang)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        keyboard.row()
        button_text = await f("friendly setlimit button", lang)
        callback_data = c("change_ratio", member_id=object.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        button_text = await f("friendly setlimit disable button", lang)
        callback_data = c("delete_ratio", member_id=object.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        keyboard.row()
        save_or_remove = "remove" if object.is_favorite else "save"
        button_text = await f(f"{save_or_remove} favorite button", lang)
        callback_data = c("change_favorite", user_id=object.user_id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        ban_or_unban = "unban" if object.ban_user_status else "ban"
        button_text = await f(f"{ban_or_unban} button", lang)
        callback_data = c("ban_or_unban", member_id=object.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        keyboard.row()
        button_text = await f("set message limit button", lang)
        if object.max_message_limit:
            button_text = await active_button(lang, button_text)
        callback_data = c("change_messages_limit", member_id=object.id)
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    @classmethod
    async def get_message_text(cls, channel_id: int, search_text: str, lang: str) -> str:
        channel = await Channel.get(channel_id)

        if search_text and cls.search_message_text_variable:
            text = cls.search_message_text_variable
        else:
            text = await f("friendly correspondences no statistics header", lang, channel_name=channel.name)
        return text

    @classmethod
    async def get_empty_list_text(cls, channel_id: int, search_text: str, lang: str) -> str:
        if search_text and cls.search_empty_text_variable:
            text = cls.search_empty_text_variable
        else:
            text = await cls.get_message_text(channel_id, search_text, lang)
        return text

    @classmethod
    async def footer_drawer(cls, keyboard: InlineKb, lang: str):
        update_button = InlineBtn(await f("update correspondence list", lang), callback_data="update_list")
        keyboard.row(update_button)
        keyboard.insert(InlineBtn(await f("mailing button", lang), callback_data="mailing"))
        keyboard.insert(await previous_button(lang))

    @classmethod
    async def update_search(cls, new_search_text: str | None, state: FSMContext, user: User):
        await state.update_data(search_text=new_search_text)

    @classmethod
    @with_delete_state_messages
    async def select_object_button_handler(
            cls, callback_query: types.CallbackQuery,
            state: FSMContext, callback_data: dict,
            user: User, lang: str,
    ):
        object_id = callback_data.get("id")

        async with state.proxy() as state_data:
            selected = state_data.pop("selected", list())
            if object_id in selected:
                selected = []
            else:
                selected = [object_id]
            state_data["selected"] = selected

        await Router.state_menu(callback_query, state, lang)

    @classmethod
    async def search_handler(cls, message: types.Message, state: FSMContext, user: User, lang: str):
        find_string = "t.me/"
        pos = message.text.find(find_string)
        if pos != -1:
            search_text = message.text[pos + len(find_string):]
        else:
            search_text = message.text
        await cls.update_search(search_text, state, user)
        await Router.state_menu(state=state, lang=lang, get_state_message=True)
        with suppress(Exception):
            await message.delete()

    @classmethod
    def setup_handlers(cls, dp: Dispatcher, state: StatesGroupMeta | State):
        super().setup_handlers(dp, state)

        dp.register_callback_query_handler(
            cls.rm_search_button_handler,
            callback_mode="rm_search",
            state=Correspondences,
        )

        dp.register_callback_query_handler(
            cls.select_object_button_handler,
            callback_mode="select_object",
            state=Correspondences,
        )
