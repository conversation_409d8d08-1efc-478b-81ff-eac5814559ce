from aiogram import types

from core.user.functions import create_or_update_messanger_user
from db import crud
from db.models import Channel, ClientBot


async def add_channel(chat: types.Chat) -> Channel:
    bot_from_db = await ClientBot.get_current()
    admins = await chat.get_administrators()

    admins = [admin for admin in admins if admin.user.is_bot is False]
    if admins:
        owner_member = [admin for admin in admins if admin.status == "creator"]
        if owner_member:
            owner_member = owner_member[0]
        else:
            owner_member = [admin for admin in admins if admin.user.is_bot is False][0]
        owner_user = await create_or_update_messanger_user(owner_member.user, bot_from_db)
    else:
        owner_user = bot_from_db.owner

    bot = await ClientBot.get_current()
    channel = await crud.add_channel(owner_user, bot, chat.type, chat.title, chat.id, "creator", chat.username)
    return channel
