from aiogram import Dispatcher, types
from utils.text import f


async def add_channel_button_handler(callback_query: types.CallbackQuery, lang: str):
    await callback_query.message.answer(await f("add channel text", lang))


def register_add_channel_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        add_channel_button_handler,
        callback_mode="add_channel",
        state="*",
    )
