from aiogram import Dispatcher, types

from db.models import Channel, ClientBot

from ..functions import add_channel
from ...admins_synchronizer.functions import synchronize_admins


async def bot_got_admin_permissions_handler(update: types.ChatMemberUpdated):
    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=update.chat.id, bot_id=bot_from_db.id)
    if not channel:
        channel = await add_channel(update.chat)

    can_delete_messages = getattr(update.new_chat_member, "can_delete_messages", False)

    if can_delete_messages:
        await channel.set_bot_is_admin()
        await synchronize_admins(update)


async def bot_kicked_handler(update: types.ChatMemberUpdated):
    return await bot_lost_admin_permissions_handler(update)


async def bot_lost_admin_permissions_handler(update: types.ChatMemberUpdated):
    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=update.chat.id, bot_id=bot_from_db.id)
    if channel:
        await channel.set_bot_is_not_admin()


def register_add_channel_my_chat_member_handlers(dp: Dispatcher):
    dp.register_my_chat_member_handler(
        bot_got_admin_permissions_handler,
        got_admin_permissions=True,
        state="*",
    )

    dp.register_my_chat_member_handler(
        bot_lost_admin_permissions_handler,
        lost_admin_permissions=True,
        state="*",
    )

    dp.register_my_chat_member_handler(
        bot_kicked_handler,
        is_bot_kicked=True,
        state="*",
    )
