from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Channel

from utils.text import f
from utils.router import Router
from utils.redefined_classes import InlineKb

from utils.keyboards import previous_button, next_button, done_button

from .states import AddChannelMenuButton


async def input_name_button_field_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    keyboard = InlineKb()
    keyboard.row(await previous_button(lang))

    message_text = ""
    button_name = state_data.get("name_button_field")
    if button_name:
        message_text += button_name
        keyboard.insert(await next_button(lang))

    message_text = await f("input button name for channel menu text", name_button=message_text, lang=lang)
    return await message.edit_text(message_text, reply_markup=keyboard)


async def input_message_button_field_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    keyboard = InlineKb()
    keyboard.row(await previous_button(lang))

    message_text = ""
    data_text = state_data.get("message_button_field")
    if data_text:
        message_text += data_text
        keyboard.insert(await next_button(lang))

    message_text = await f("input button content for channel menu text", message_button=message_text, lang=lang)
    return await message.edit_text(message_text, reply_markup=keyboard)


async def input_message_button_done_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    keyboard = InlineKb()
    keyboard.row(await previous_button(lang))

    channel = await Channel.get(state_data.get("channel_id"))
    name_button = state_data.get("name_button_field")
    button = await channel.get_menu_buttons(name_button)
    if button:
        message_text = await f("create button for channel menu error text", lang=lang)
        return await message.edit_text(message_text, reply_markup=keyboard)

    message_text = await f("confirm create button for channel menu text", name_button=name_button, lang=lang)
    keyboard.insert(await done_button(lang))
    return await message.edit_text(message_text, reply_markup=keyboard)


def register_create_channel_menu_buttons_routes(router: Router):
    router.add_route(AddChannelMenuButton.NameButtonField, input_name_button_field_menu)
    router.add_route(AddChannelMenuButton.MessageButtonField, input_message_button_field_menu)
    router.add_route(AddChannelMenuButton.Done, input_message_button_done_menu)
