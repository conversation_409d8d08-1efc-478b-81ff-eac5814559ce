from psutils.forms import Wizard<PERSON><PERSON>

from psutils.forms.fields import <PERSON><PERSON><PERSON>, MessageField
from friendly.channel.chat_menu_buttons.create.states import AddChannelMenuButton
from friendly.channel.edit.states import EditChannel

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import User
from utils.router import Router

from psutils.forms.helpers import with_delete_state_messages


class AddChannelMenuButtonForm(WizardForm):
    state_group = AddChannelMenuButton
    previous_state = EditChannel.MenuButtonSettings.state
    back_to_previous_state_excluded_keys = ("name_button_field", "message_button_field")

    name_button_field = TextField()
    message_button_field = MessageField(
        "text",
        "photo",
        "video",
        error_text_variable="invalid event media error",
        with_caption=True
    )

    @classmethod
    @with_delete_state_messages
    async def back_to_previous_state_button_handler(
            cls, callback_query: types.CallbackQuery,
            state: FSMContext, user: User, lang: str,
    ):
        if await state.get_state() == AddChannelMenuButton.NameButtonField.state:
            await state.update_data(name_button_field=None, message_button_field=None)
            await state.set_state(cls.previous_state)
            await Router.state_menu(callback_query, state, lang)
        else:
            await cls.set_prev_state(state)
            await Router.state_menu(callback_query, state, lang)
