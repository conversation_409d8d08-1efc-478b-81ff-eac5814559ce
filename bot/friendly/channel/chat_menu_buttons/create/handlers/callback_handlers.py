from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import Channel, ChannelMenuButton
from utils.router import Router

from ..states import AddChannelMenuButton
from ....edit.states import EditChannel


async def input_message_button_done_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    message = state_data.get("message_button_field")
    content_type = state_data.get("content_type")
    caption = state_data.get("text")

    media_res = ""
    if content_type == "text":
        message_res = message
    else:
        message_res = caption
        media_res = message

    await ChannelMenuButton.create(
        channel=channel,
        name_button=state_data.get("name_button_field"),
        message=message_res,
        content_type=content_type,
        media=media_res
    )
    await EditChannel.MenuButtonSettings.set()
    state_data = await state.get_data()
    del state_data["message_button_field"]
    del state_data["name_button_field"]
    if "text" in state_data.keys():
        del state_data["text"]
    await state.set_data(state_data)
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def create_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await AddChannelMenuButton.next()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


def register_create_channel_menu_buttons_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        input_message_button_done_handler,
        callback_mode="done",
        state=AddChannelMenuButton.Done,
    )

    dp.register_callback_query_handler(
        create_button_handler,
        callback_mode="create_button",
        state=EditChannel.MenuButtonSettings,
    )
