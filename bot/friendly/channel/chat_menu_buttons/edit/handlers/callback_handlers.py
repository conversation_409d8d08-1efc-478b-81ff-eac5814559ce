from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import Channel, ChannelMenuButton, ClientBot
from utils.router import Router
from utils.text import f

from ..states import EditChannelMenuButton
from ....edit.states import EditChannel
from ..keyboards import get_channel_menu_keyboard
from contextlib import suppress
from aiogram.utils.exceptions import MessageError
from utils.message import send_tg_message
from psutils.forms.helpers import with_delete_state_messages


async def edit_button_name_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        lang: str
):
    await EditChannelMenuButton.NameButtonField.set()
    await state.update_data(name_button_field="", message_button_field="")
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def edit_button_content_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        lang: str
):
    await EditChannelMenuButton.MessageButtonField.set()
    await state.update_data(show_post_as_in_chat=False)
    await state.update_data(name_button_field="", message_button_field="")

    await Router.state_menu(callback_query, state, lang, set_state_message=True, mode="new")


@with_delete_state_messages
async def show_post_as_in_chat_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    show_post_as_in_chat = state_data.get("show_post_as_in_chat")
    await state.update_data(show_post_as_in_chat=not show_post_as_in_chat)
    await Router.state_menu(callback_query, state, lang, set_state_message=True, mode="new")


async def edit_new_line_buttons_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        lang: str
):
    await EditChannel.EditNewlineButtons.set()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def edit_new_line_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        state: FSMContext,
        lang: str
):
    button = await ChannelMenuButton.get(callback_data.get("button_id"))
    await button.update_is_new_line()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def edit_buttons_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await EditChannel.EditButtonsMenu.set()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def edit_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict, lang: str):
    await EditChannel.EditButtonMenu.set()
    await state.update_data(button_id=callback_data.get("id"))
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def delete_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        lang: str
):
    state_data = await state.get_data()
    button = await ChannelMenuButton.get(state_data.get("button_id"))
    await button.delete()
    await EditChannel.EditButtonsMenu.set()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def menu_button_item_position_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        state: FSMContext,
        lang: str
):
    state_data = await state.get_data()
    buttons = state_data.get("buttons")
    button_id = callback_data.get("id")

    if button_id in buttons:
        buttons.remove(button_id)
    else:
        buttons.append(button_id)

    if len(buttons) == 2:
        channel = await Channel.get(state_data.get("channel_id"))
        await channel.move_menu_buttons(buttons[0], buttons[1])
        buttons = []
    await state.update_data(buttons=buttons)
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def menu_button_position_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await EditChannel.ButtonPosition.set()
    await state.update_data(buttons=[])
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def update_channel_menu_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    cur_bot = await ClientBot.get_current()
    channel = await Channel.get(state_data.get("channel_id"))

    if cur_bot.last_set_menu_message_id:
        with suppress(MessageError):
            await callback_query.bot.delete_message(
                chat_id=channel.chat_id,
                message_id=cur_bot.last_set_menu_message_id
            )
    keyboard = await get_channel_menu_keyboard(channel)
    message_menu_data = await channel.get_message(
        "channel_menu_text",
        add_at_to_username=True,
        firstname="",
        user_first_name="",
        fullname="",
        user_full_name="",
        username="",
    )
    menu_message = await send_tg_message(channel.chat_id, keyboard=keyboard, **message_menu_data)

    await cur_bot.update(last_set_menu_message_id=menu_message.message_id)

    await callback_query.answer(await f("channel menu update text", lang))


def register_edit_channel_menu_buttons_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        edit_button_name_handler,
        callback_mode="edit_button_name",
        state=EditChannel.EditButtonMenu,
    )

    dp.register_callback_query_handler(
        edit_button_content_handler,
        callback_mode="edit_button_content",
        state=EditChannel.EditButtonMenu,
    )

    dp.register_callback_query_handler(
        edit_new_line_buttons_handler,
        callback_mode="new_line",
        state=EditChannel.MenuButtonSettings,
    )

    dp.register_callback_query_handler(
        edit_new_line_button_handler,
        callback_mode="edit_new_line_button",
        state=EditChannel.EditNewlineButtons,
    )

    dp.register_callback_query_handler(
        edit_buttons_handler,
        callback_mode="edit_buttons_menu",
        state=EditChannel.MenuButtonSettings,
    )

    dp.register_callback_query_handler(
        edit_button_handler,
        callback_mode="edit_button",
        state=EditChannel.EditButtonsMenu,
    )

    dp.register_callback_query_handler(
        delete_button_handler,
        callback_mode="delete_button",
        state=EditChannel.EditButtonMenu,
    )

    dp.register_callback_query_handler(
        menu_button_item_position_handler,
        callback_mode="button_position_item",
        state=EditChannel.ButtonPosition,
    )

    dp.register_callback_query_handler(
        menu_button_position_handler,
        callback_mode="button_position",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        update_channel_menu_button_handler,
        callback_mode="update_channel_menu",
        state=EditChannel.MenuButtonSettings,
    )

    dp.register_callback_query_handler(
        show_post_as_in_chat_button_handler,
        callback_mode="show_post_as_in_chat",
        state=EditChannelMenuButton.MessageButtonField,
    )
