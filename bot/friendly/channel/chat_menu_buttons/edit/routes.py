from contextlib import suppress

from aiogram import types
from aiogram.dispatcher import FSMContext

from utils.router import Router
from utils.message import send_tg_message
from psutils.forms.helpers import save_messages_to_state

from .keyboards import *

from utils.keyboards import previous_button

from .states import EditChannelMenuButton
from ...edit.states import EditChannel


async def edit_button_name_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    keyboard = InlineKb()
    keyboard.row(await previous_button(lang))

    button = await ChannelMenuButton.get(state_data.get("button_id"))

    message_text = await f("input button name for channel menu text", name_button=button.name_button, lang=lang)
    await message.edit_text(message_text, reply_markup=keyboard)


async def edit_button_content_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    show_post_as_in_chat = state_data.get("show_post_as_in_chat")

    button = await ChannelMenuButton.get(state_data.get("button_id"))

    text_without_tags, keyboard_post, keyboard = await get_edit_content_button_keyboard(
        button,
        show_post_as_in_chat,
        lang
    )

    data = {
        "text": button.message,
        "content_type": button.content_type
    }
    if button.content_type == "photo":
        data["photo"] = button.media
    elif button.content_type == "video":
        data["video"] = button.media

    if show_post_as_in_chat and button.message != text_without_tags:
        data["text"] = text_without_tags
        data["keyboard"] = keyboard_post

    with suppress(Exception):
        await message.delete()

    msg = await send_tg_message(message.chat.id, **data)
    message_text = await f("input button content for channel menu text", message_button="", lang=lang)
    msg2 = await message.answer(message_text, reply_markup=keyboard)
    await save_messages_to_state(state, msg)
    await save_messages_to_state(state, msg2)


async def edit_new_line_buttons_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    keyboard = await get_channel_menu_new_line_buttons_keyboard(channel, lang)

    message_text = await f("channel menu new line text", lang)
    await message.edit_text(message_text, reply_markup=keyboard)


async def edit_buttons_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    keyboard = await get_channel_menu_preview_for_edit_button_keyboard(channel, lang)
    message_text = await f("select button text", lang)
    return await message.edit_text(message_text, reply_markup=keyboard)


async def edit_button_menu(message: types.Message, mode: str, lang: str):
    keyboard = await get_edit_button_menu_keyboard(lang)
    message_text = await f("settings button text", lang)
    if mode == "edit":
        return await message.edit_text(message_text, reply_markup=keyboard)
    else:
        return await message.answer(message_text, reply_markup=keyboard)


async def button_settings_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    message_text = await f("menu keyboard settings text", lang)
    keyboard = await get_channel_menu_edit_keyboard(channel, lang)
    return await message.edit_text(message_text, reply_markup=keyboard)


async def button_position_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    active_buttons = state_data.get("buttons")

    message_text = await f("edit channel menu buttons layout text", lang)

    keyboard = await get_channel_menu_preview_for_edit_position_keyboard(channel, active_buttons, lang)
    return await message.edit_text(message_text, reply_markup=keyboard)


def register_edit_channel_menu_buttons_routes(router: Router):
    router.add_route(EditChannel.MenuButtonSettings, button_settings_menu)
    router.add_route(EditChannel.ButtonPosition, button_position_menu)
    router.add_route(EditChannel.EditButtonsMenu, edit_buttons_menu)
    router.add_route(EditChannel.EditButtonMenu, edit_button_menu)

    router.add_route(EditChannelMenuButton.NameButtonField, edit_button_name_menu)
    router.add_route(EditChannelMenuButton.MessageButtonField, edit_button_content_menu)
    router.add_route(EditChannel.EditNewlineButtons, edit_new_line_buttons_menu)
