from psutils.forms import Wizard<PERSON>orm

from psutils.forms.fields import <PERSON><PERSON><PERSON>, MessageField
from friendly.channel.chat_menu_buttons.edit.states import EditChannelMenuButton
from friendly.channel.edit.states import EditChannel

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import User, Channel, ChannelMenuButton
from psutils.fsm import reset_excluding
from utils.router import Router

from psutils.forms.helpers import with_delete_state_messages


async def name_button_field_save(message: types.Message, state: FSMContext):
    state_data = await state.get_data()
    button = await ChannelMenuButton.get(state_data.get("button_id"))
    await button.edit_name_button(state_data.get("name_button_field"))


async def message_button_field_save(message: types.Message, state: FSMContext):
    state_data = await state.get_data()
    button = await ChannelMenuButton.get(state_data.get("button_id"))

    message = state_data.get("message_button_field")
    content_type = state_data.get("content_type")
    caption = state_data.get("text")

    media_res = ""
    if content_type == "text":
        message_res = message
    else:
        message_res = caption
        media_res = message

    await button.edit_content_button(text=message_res, content_type=content_type, media=media_res)


class EditChannelMenuButtonForm(WizardForm):
    state_group = EditChannelMenuButton
    previous_state = EditChannel.EditButtonMenu.state
    back_to_previous_state_excluded_keys = ("name_button_field", "message_button_field")

    name_button_field = TextField(post_save=name_button_field_save)
    message_button_field = MessageField(
        "text",
        "photo",
        "video",
        error_text_variable="invalid event media error",
        with_caption=True,
        post_save=message_button_field_save
    )

    @classmethod
    async def set_prev_state(cls, state: FSMContext):
        await state.set_state(cls.previous_state)

    @classmethod
    @with_delete_state_messages
    async def back_to_previous_state_button_handler(
            cls, callback_query: types.CallbackQuery,
            state: FSMContext, user: User, lang: str,
    ):
        cur_state = await state.get_state()
        await state.update_data(name_button_field=None, message_button_field=None)
        await state.set_state(cls.previous_state)
        if cur_state == EditChannelMenuButton.MessageButtonField.state:
            await Router.state_menu(callback_query, state, lang, mode="new")
        else:
            await Router.state_menu(callback_query, state, lang)


    @classmethod
    @with_delete_state_messages
    async def previous_button_handler(
            cls, callback_query: types.CallbackQuery,
            state: FSMContext, lang: str,
    ):
        cur_state = await state.get_state()
        await state.update_data(name_button_field=None, message_button_field=None)
        await state.set_state(cls.previous_state)
        if cur_state == EditChannelMenuButton.MessageButtonField.state:
            await Router.state_menu(callback_query, state, lang, mode="new")
        else:
            await Router.state_menu(callback_query, state, lang)
