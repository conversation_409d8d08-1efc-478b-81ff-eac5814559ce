from db.models import Channel, ChannelMenuButton

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn, MenuKb, MenuBtn

from utils.keyboards import previous_button, active_button
from friendly.channel.limitations.keyboards import parse_keyboard_in_text


async def get_channel_menu_edit_keyboard(channel: Channel, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)

    button_text = await f("add channel menu button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="create_button"))

    count_buttons = len(await channel.get_menu_buttons())
    button_text = await f("edit channel menu button", count=count_buttons, lang=lang)
    keyboard.insert(InlineBtn(button_text, callback_data="edit_buttons_menu"))

    button_text = await f("edit channel menu buttons layout", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="button_position"))

    button_text = await f("channel menu new line buttons", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="new_line"))

    button_text = await f("update channel menu button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="update_channel_menu"))

    button_text = await f("edit channel menu text button", lang)
    callback_data = c("set_message", channel_id=channel.id, message_type="channel_menu_text")
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    keyboard.row(await previous_button(lang))

    return keyboard


async def get_channel_menu_preview_for_edit_button_keyboard(
        channel: Channel,
        lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=3)
    buttons = await channel.get_menu_buttons()
    for button in buttons:
        button_text = button.name_button
        callback_data = c("edit_button", id=button.id)
        if button.is_new_line:
            keyboard.row(InlineBtn(button_text, callback_data=callback_data))
        else:
            keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    keyboard.row(await previous_button(lang))
    return keyboard


async def get_edit_button_menu_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)

    button_text = await f("edit channel menu button text button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="edit_button_name"))

    button_text = await f("edit channel menu button content button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="edit_button_content"))

    button_text = await f("delete channel menu button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="delete_button"))

    keyboard.row(await previous_button(lang))

    return keyboard


async def get_channel_menu_preview_for_edit_position_keyboard(
        channel: Channel,
        active_buttons: dict,
        lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=3)
    buttons = await channel.get_menu_buttons()
    for button in buttons:
        button_text = button.name_button
        if button.id in active_buttons:
            button_text = await active_button(lang, button_text)
        callback_data = c("button_position_item", id=button.id)
        if button.is_new_line:
            keyboard.row(InlineBtn(button_text, callback_data=callback_data))
        else:
            keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    keyboard.row(await previous_button(lang))
    return keyboard


async def get_channel_menu_new_line_buttons_keyboard(
        channel: Channel,
        lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=3)
    buttons = await channel.get_menu_buttons()
    for button in buttons:
        button_text = button.name_button
        callback_data = c("edit_new_line_button", button_id=button.id)
        if button.is_new_line:
            button_text = await active_button(lang, button_text)
            keyboard.row(InlineBtn(button_text, callback_data=callback_data))
        else:
            keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    keyboard.row(await previous_button(lang))
    return keyboard


async def get_channel_menu_keyboard(channel: Channel):
    keyboard = MenuKb(resize_keyboard=True, one_time_keyboard=False)
    buttons = await channel.get_menu_buttons()
    for button in buttons:
        button_text = button.name_button
        if button.is_new_line:
            keyboard.row(MenuBtn(button_text))
        else:
            keyboard.insert(MenuBtn(button_text))
    return keyboard


async def get_edit_content_button_keyboard(button: ChannelMenuButton, show_post_as_in_chat, lang: str):
    keyboard = InlineKb()
    text_without_tags, keyboard_post = parse_keyboard_in_text(button.message)

    # Если изначальный текст кнопки отличается от text_without_tags значит были найдены кнопки
    if button.message != text_without_tags:
        button_text = await f("show post as in chat button", lang)
        if show_post_as_in_chat:
            button_text = await active_button(lang, button_text)
        keyboard.row(InlineBtn(button_text, callback_data="show_post_as_in_chat"))
    keyboard.row(await previous_button(lang))
    return text_without_tags, keyboard_post, keyboard
