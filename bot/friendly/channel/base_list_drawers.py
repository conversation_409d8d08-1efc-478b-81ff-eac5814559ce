from abc import ABC
from typing import Any, Dict, List

from db.models import Client<PERSON><PERSON>, User, Channel

from utils.router.route_handlers import BaseListDrawer

from .db_funcs import get_admined_channels


class BaseChannelsListDrawer(BaseListDrawer, ABC, methods=None):
    row_width = 4

    need_setup_search_handler = True
    need_setup_pagination_handler = True

    pagination_callback_mode = "channels_pagination"

    @classmethod
    async def make_get_objects_kwargs(
            cls,
            user: User,
            search_text: str,
            data_from_state: Dict[str, Any],
    ) -> Dict[str, Any]:
        return dict(user=user, search_text=search_text)

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: str = "all",
    ) -> List[Channel] | int:

        user: User = get_objects_kwargs.get("user")
        channels_mode = get_objects_kwargs.get("channels_mode", "admin")
        bot_id = ClientBot.get_current_bot_id()

        is_super_admin_mode = True if channels_mode == "super_admin" else False
        is_active_is_admin_filter = get_objects_kwargs.get("is_active_is_admin_filter", True)
        is_active_channels_filter = get_objects_kwargs.get("is_active_channels_filter", True)
        is_active_chats_filter = get_objects_kwargs.get("is_active_chats_filter", True)
        is_active_is_not_admin_filter = get_objects_kwargs.get("is_active_is_not_admin_filter", True)
        location_id_filter = get_objects_kwargs.get("location_id_filter", None)
        search_text = get_objects_kwargs.get("search_text")

        admin_channels = await get_admined_channels(
            user, bot_id,
            position, limit,
            search_text, operation,
            is_super_admin_mode,
            is_active_chats_filter,
            is_active_channels_filter,
            is_active_is_admin_filter,
            is_active_is_not_admin_filter,
            location_id_filter,
        )
        return admin_channels
