from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Chat<PERSON>ember

from psutils.forms import Wizard<PERSON><PERSON>

from psutils.forms.fields import TextField
from .states import RatioExceptions
from ..correspondences.states import Correspondences


async def save_message_limit(state: FSMContext):
    state_data = await state.get_data()
    chat_member = await ChatMember.get(chat_member_id=state_data.get("member_id"))
    limit = int(state_data.get("change_message_limit"))
    limit = 0 if limit < 0 else limit
    await chat_member.set_max_message_limit(limit)


class SetMessageLimitForm(WizardForm):
    state_group = RatioExceptions
    previous_state = Correspondences.state
    need_setup_previous_button_handler = False

    change_message_limit = TextField(post_save=save_message_limit)

    @classmethod
    async def next_field(cls, state: FSMContext):
        pass

    @classmethod
    async def set_next_state(cls, state: FSMContext = None):
        pass

    @classmethod
    async def set_prev_state(cls, state: FSMContext):
        pass
