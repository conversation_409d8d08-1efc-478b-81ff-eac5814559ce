from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import Chat<PERSON>ember

from utils.router import Router

from friendly.helpers import send_error

from friendly.channel.correspondences.states import Correspondences

from ..states import RatioExceptions


async def delete_ratio_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    member_id = callback_data.get("member_id")
    chat_member = await ChatMember.get(chat_member_id=member_id)

    result = await chat_member.delete_limit()
    if not result:
        return await send_error(callback_query.message)

    await Router.state_menu(callback_query, state)


async def change_ratio_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    await state.update_data(**callback_data)

    await RatioExceptions.ChangeLimit.set()
    await Router.state_menu(callback_query, state)


async def change_message_limit_button_handler(callback_query: types.CallbackQuery, state: FSM<PERSON>ontext, callback_data: dict):
    await state.update_data(**callback_data)

    await RatioExceptions.ChangeMessageLimit.set()
    await Router.state_menu(callback_query, state)


async def remove_message_limit_button_handler(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    state_data = await state.get_data()
    chat_member = await ChatMember.get(chat_member_id=state_data.get("member_id"))
    await chat_member.set_max_message_limit(0)
    await Router.state_menu(callback_query, state)


async def previous_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await Correspondences.set()
    await Router.state_menu(callback_query, state)


def register_channel_ratio_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        previous_button_handler,
        previous_button=True,
        state=RatioExceptions,
    )

    dp.register_callback_query_handler(
        delete_ratio_button_handler,
        callback_mode="delete_ratio",
        chat_type="private",
        state=Correspondences,
    )

    dp.register_callback_query_handler(
        change_ratio_button_handler,
        callback_mode="change_ratio",
        chat_type="private",
        state=Correspondences,
    )

    dp.register_callback_query_handler(
        change_message_limit_button_handler,
        callback_mode="change_messages_limit",
        chat_type="private",
        state=Correspondences,
    )

    dp.register_callback_query_handler(
        remove_message_limit_button_handler,
        callback_mode="remove_message_limit",
        chat_type="private",
        state=RatioExceptions.ChangeMessageLimit,
    )
