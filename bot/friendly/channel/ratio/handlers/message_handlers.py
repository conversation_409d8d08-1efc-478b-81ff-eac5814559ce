import re

from aiogram import Dispatcher, types
from aiogram.types import ContentTypes
from aiogram.dispatcher import FSMContext

from db.models import Chat<PERSON><PERSON>ber, User

from utils.text import f
from utils.router import Router
from utils.filters.message import EndProcessButtonFilter
from utils.filters.multi import CancelButtonFilter

from friendly.helpers import send_error

from friendly.main.keyboards import get_menu_keyboard

from friendly.channel.correspondences.states import Correspondences

from ..states import RatioExceptions


async def cancel_or_end_process_button_handler(
        message: types.Message, state: FSMContext,
        user: User, lang: str, end_process: bool = False,
):
    keyboard = await get_menu_keyboard(user, lang)
    saved_or_cancel = "saved" if end_process else "cancel"
    await message.answer(await f(f"action {saved_or_cancel} text", lang), reply_markup=keyboard)

    await Correspondences.set()
    return await Router.state_menu(message, state, lang)


async def change_ratio_handler(message: types.Message, state: FSM<PERSON>ontext, user: User, lang: str):
    ratio = str(message.text)
    if not re.fullmatch(r"\d+[/\\]\d+", ratio):
        return await message.answer(await f("incorrect limit format error", lang))

    state_data = await state.get_data()

    messages_for_friends, friends_required = [int(value) for value in re.split(r"[/\\]", ratio)]
    ratio_dict = dict(messages_for_friends=messages_for_friends, friends_required=friends_required)

    member = await ChatMember.get(chat_member_id=state_data.get("member_id"))
    if not member:
        return await send_error(message)

    await member.set_limit(ratio_dict)

    messages_for_friends = member.messages_for_friends
    friends_required = member.friends_required
    ratio = await f("ratio text", messages_for_friends=messages_for_friends, friends_required=friends_required)
    text = await f("user exception edited", lang, name=member.name, ratio=ratio)

    keyboard = await get_menu_keyboard(user, lang)
    await message.answer(text, reply_markup=keyboard)

    await Correspondences.set()
    return await Router.state_menu(message, state)


def register_channel_ratio_message_handlers(dp: Dispatcher):

    dp.register_message_handler(
        cancel_or_end_process_button_handler,
        EndProcessButtonFilter() | CancelButtonFilter(),
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=RatioExceptions,
    )

    dp.register_message_handler(
        change_ratio_handler,
        chat_type="private",
        content_types=ContentTypes.ANY,
        state=RatioExceptions.ChangeLimit
    )
