from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Chat<PERSON>ember

from utils.text import f
from utils.router import Router

from utils.keyboards import get_end_process_keyboard

from .states import RatioExceptions
from .keyboards import get_limit_message_menu_keyboard


async def send_change_ratio_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    chat_member = await ChatMember.get(chat_member_id=state_data.get("member_id"))

    current_ratio = await chat_member.ratio_str
    text = await f(
        "add member ratio", lang,
        user_full_name=chat_member.name,
        username=f"@{chat_member.user.username}",
        current_ratio=current_ratio
    )

    keyboard = await get_end_process_keyboard(lang)

    await message.answer(text, reply_markup=keyboard)


async def send_change_message_limit_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    chat_member = await ChatMember.get(chat_member_id=state_data.get("member_id"))

    current_message_limit = chat_member.max_message_limit
    keyboard = await get_limit_message_menu_keyboard(current_message_limit, lang)

    if not current_message_limit:
        current_message_limit = await f("limit message not set text", lang)

    button_text = await f("current message limit text", current_message_limit=current_message_limit, lang=lang)

    await message.edit_text(button_text, reply_markup=keyboard)


def register_channel_ratio_routes(router: Router):
    router.add_route(RatioExceptions.ChangeLimit, send_change_ratio_menu)
    router.add_route(RatioExceptions.ChangeMessageLimit, send_change_message_limit_menu)
