from aiogram import types, Dispatcher

from core.user.functions import create_or_update_messanger_user
from db.models import Channel, ClientBot

from utils.filters.simple import not_bot_filter, is_joined_group, is_left_group

from ..functions import process_member_left, process_member_joined


async def chat_member_left_handler(update: types.ChatMemberUpdated):
    await process_member_left(update)


async def chat_member_joined_handler(update: types.ChatMemberUpdated):
    await process_member_joined(update)


async def user_got_admin_permissions_handler(update: types.ChatMemberUpdated):
    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=update.chat.id, bot_id=bot_from_db.id)
    if not channel:
        return

    user = await create_or_update_messanger_user(update.new_chat_member.user, bot_from_db)
    await channel.add_user_to_admins(user)


async def user_lost_admin_permissions_handler(update: types.ChatMemberUpdated):
    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=update.chat.id, bot_id=bot_from_db.id)
    if not channel:
        return

    user = await create_or_update_messanger_user(update.new_chat_member.user, bot_from_db)
    await channel.delete_user_from_admins(user)


def register_channel_chat_member_handlers(dp: Dispatcher):
    dp.register_chat_member_handler(
        chat_member_left_handler,
        is_left_group,
        not_bot_filter,
        state="*",
    )

    dp.register_chat_member_handler(
        chat_member_joined_handler,
        is_joined_group,
        not_bot_filter,
        state="*",
    )

    dp.register_chat_member_handler(
        user_got_admin_permissions_handler,
        not_bot_filter,
        got_admin_permissions=True,
        state="*",
    )

    dp.register_chat_member_handler(
        user_lost_admin_permissions_handler,
        not_bot_filter,
        lost_admin_permissions=True,
        state="*",
    )
