from aiogram import Dispatcher, types

from db.models import <PERSON>t<PERSON>ember

from utils.text import f

from friendly.main.keyboards import get_return_to_chat_keyboard


async def agree_rules_button_handler(callback_query: types.CallbackQuery, callback_data: dict, lang: str):
    chat_member = await ChatMember.get(chat_member_id=callback_data.get("chat_member_id"))
    await chat_member.agreed_rules()
    keyboard = await get_return_to_chat_keyboard(chat_member.channel.username, lang)
    await callback_query.message.answer(await f("rules passed successfully", lang), reply_markup=keyboard)


def register_channel_members_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        agree_rules_button_handler,
        callback_mode="agreed_rules",
        state="*",
    )
