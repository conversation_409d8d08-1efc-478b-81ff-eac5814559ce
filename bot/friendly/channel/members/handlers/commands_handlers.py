from aiogram import Dispatcher, types

from db.models import Channel, ChatMember, ClientBot, FriendlyBotAnalyticAction, User

from utils.message import send_tg_message

from ..keyboards import get_contact_and_back_to_chat_link_keyboard, get_agree_rules_keyboard


async def cmd_info_deep_link(message: types.Message, deep_link_data: dict, user: User, lang: str):
    bot_from_db = await ClientBot.get_current()

    channel = await Channel.get(deep_link_data.get("chid"))
    chat_member = await ChatMember.create_or_get(user, channel)

    await FriendlyBotAnalyticAction.save_followed_instructions_link(member_id=chat_member.id)

    allowed_messages, messages_for_friends, friends_required = await chat_member.get_limits_info_texts()

    if not channel.get_chat_message_status("info", "show"):
        return

    first_name = user.first_name
    full_name = user.first_name
    username = user.username

    message_data = await channel.get_message(
        "info",
        firstname=first_name,
        user_first_name=first_name,
        fullname=full_name,
        user_full_name=full_name,
        username=username,
        friends_added=await chat_member.get_invites_count(),
        friends_required=friends_required,
        messages_given=messages_for_friends,
        allowed_messages=allowed_messages,
        chat_name=channel.get_link(),
    )

    keyboard = await get_contact_and_back_to_chat_link_keyboard(channel, bot_from_db.username, lang)
    await send_tg_message(message.chat.id, keyboard=keyboard, **message_data)


async def cmd_rules_deep_link(message: types.Message, deep_link_data: dict, user: User, lang: str):
    channel = await Channel.get(deep_link_data.get("chid"))
    chat_member = await ChatMember.get(user.id, channel.id)

    allowed_messages, messages_for_friends, friends_required = await chat_member.get_limits_info_texts()

    if not channel.get_chat_message_status("rules", "show"):
        return

    first_name = user.first_name
    full_name = user.first_name
    username = user.username

    text_kwargs = dict(
        firstname=first_name,
        user_first_name=first_name,
        fullname=full_name,
        user_full_name=full_name,
        username=username,
        friends_added=await chat_member.get_invites_count(),
        friends_required=friends_required,
        messages_given=messages_for_friends,
        allowed_messages=allowed_messages,
        chat_name=channel.get_link()
    )

    message_data = await channel.get_message("rules", **text_kwargs)

    if channel.messages_given != 0:
        await send_tg_message(message.chat.id, **await channel.get_message("info", **text_kwargs))

    keyboard = await get_agree_rules_keyboard(chat_member.id, lang)
    await send_tg_message(message.chat.id, keyboard=keyboard, **message_data)


def register_channel_members_commands_handlers(dp: Dispatcher):
    dp.register_message_handler(
        cmd_info_deep_link,
        deep_link="info",
        state="*",
    )

    dp.register_message_handler(
        cmd_rules_deep_link,
        deep_link="rules",
        state="*",
    )
