from aiogram import Dispatcher, types
from aiogram.dispatcher.filters import BoundFilter


class GotAdminPermissionFilter(BoundFilter):

    key = "got_admin_permissions"

    def __init__(self, got_admin_permissions: bool):
        self.got_admin_permissions = got_admin_permissions

    @staticmethod
    def get_is_got_admin_permission(update: types.ChatMemberUpdated):
        return update.new_chat_member.status in ("administrator", "owner")

    async def check(self, update: types.ChatMemberUpdated) -> bool | dict:
        is_got_admin_permission = self.get_is_got_admin_permission(update)
        if self.got_admin_permissions is not is_got_admin_permission:
            return False

        if not is_got_admin_permission:
            return True

        return dict(old_status=update.old_chat_member.status, new_status=update.new_chat_member.status)


class LostAdminPermissionFilter(BoundFilter):

    key = "lost_admin_permissions"

    def __init__(self, lost_admin_permissions: bool):
        self.lost_admin_permissions = lost_admin_permissions

    @staticmethod
    def get_is_lost_admin_permission(update: types.ChatMemberUpdated):
        if update.chat.type not in ("group", "supergroup", "channel"):
            return False

        if getattr(update.new_chat_member, "can_delete_messages", False) is False:
            return True

        if update.old_chat_member.status not in ("administrator", "owner"):
            return False

        if update.new_chat_member.status != "member":
            return False

        return True

    async def check(self, update: types.ChatMemberUpdated) -> bool | dict:
        is_lost_admin_permission = self.get_is_lost_admin_permission(update)
        if self.lost_admin_permissions is not is_lost_admin_permission:
            return False

        if not is_lost_admin_permission:
            return True

        return dict(old_status=update.old_chat_member.status, new_status=update.new_chat_member.status)


def bind_members_filters(dp: Dispatcher):
    dp.bind_filter(GotAdminPermissionFilter, event_handlers=[dp.my_chat_member_handlers, dp.chat_member_handlers])
    dp.bind_filter(LostAdminPermissionFilter, event_handlers=[dp.my_chat_member_handlers, dp.chat_member_handlers])
