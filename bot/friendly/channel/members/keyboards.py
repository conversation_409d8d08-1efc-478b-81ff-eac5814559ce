from db.models import Channel

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn

from friendly.main.keyboards import get_contact_admin_button


async def get_contact_and_back_to_chat_link_keyboard(channel: Channel, bot_username: str, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)
    keyboard.insert(await get_contact_admin_button(channel, bot_username, lang))
    keyboard.insert(InlineBtn(await f("chat route button", lang), url=f"https://t.me/{channel.username}"))
    return keyboard


async def get_agree_rules_keyboard(chat_member_id: int, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)
    callback_data = c("agreed_rules", chat_member_id=chat_member_id)
    keyboard.insert(InlineBtn(await f("i agree button", lang), callback_data=callback_data))
    return keyboard
