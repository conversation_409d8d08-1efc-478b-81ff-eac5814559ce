import json
import logging
from contextlib import suppress

from aiogram import types
from aiogram.utils.exceptions import MessageError, Unauthorized

from config import DEBUG
from core.user.functions import create_or_update_messanger_user
from db.models import (
    Channel, ChatMember, ClientBot, FriendlyBotAnalyticAction, FriendlyChatMessage,
)
from friendly.channel.chat_menu_buttons.edit.keyboards import get_channel_menu_keyboard
from friendly.draw.functions import draw_member_joined
from friendly.footer import send_message_and_footer
from friendly.main.keyboards import get_menu_keyboard, get_system_message_keyboard
from utils.message import send_tg_message
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from ..limitations.functions import (
    send_rules_ban_message, send_rules_notification_message,
)


async def notify_inviter_about_invited_member(inviter: ChatMember, is_existing: bool):
    user = inviter.user
    lang = await user.get_lang()
    channel = inviter.channel

    inviter_bot_activity = await user.activity_in_bot

    if not inviter_bot_activity.is_entered_bot or not inviter_bot_activity.is_active:
        return

    total_friends_added = await inviter.get_invites_count()

    allowed_messages, messages_for_friends, friends_required = await (
        inviter.get_limits_info_texts())

    message_kwargs = dict(
        added_friends_total=total_friends_added,
        group_name=channel.name, allowed_messages=allowed_messages,
        friends_required=friends_required, messages_for_friends=messages_for_friends
    )

    new_or_existing = "existing" if is_existing else "new"

    text = await f(
        f"added {new_or_existing} member notification", lang, **message_kwargs
    )
    keyboard = await get_menu_keyboard(user, lang)

    kwargs = dict(group_lang=channel.lang, user=user)
    result = await send_tg_message(
        inviter.user.chat_id, "text", keyboard=keyboard, text=text, **kwargs
    )

    if isinstance(result, Unauthorized):
        return await inviter_bot_activity.deactivate()


async def process_member_left(update: types.ChatMemberUpdated):
    bot_from_db_id = ClientBot.get_current_bot_id()
    user = await create_or_update_messanger_user(
        update.new_chat_member.user, bot_from_db_id
    )
    channel = await Channel.get(chat_id=update.chat.id, bot_id=bot_from_db_id)
    if channel is None:
        return
    chat_member = await ChatMember.get(user.id, channel.id)
    if not chat_member:
        chat_member = await ChatMember.create_or_get(user, channel)
    await chat_member.left()
    await FriendlyBotAnalyticAction.save_user_left(member_id=chat_member.id)


async def process_member_joined(update: types.ChatMemberUpdated):
    bot_from_db_id = ClientBot.get_current_bot_id()
    channel = await Channel.get(chat_id=update.chat.id, bot_id=bot_from_db_id)
    if channel is None:
        return
    bot_from_db = await ClientBot.get(bot_from_db_id)

    inviter: ChatMember | None = None

    if update.from_user.id != update.new_chat_member.user.id:  # пользователь вошёл
        # по приглашению
        inviter_user = await create_or_update_messanger_user(
            update.from_user, bot_from_db_id
        )
        inviter = await ChatMember.get(inviter_user.id, channel.id)
        if inviter is None:
            inviter = await ChatMember.create_or_get(inviter_user, channel)

    user_entered = await create_or_update_messanger_user(
        update.new_chat_member.user, bot_from_db_id
    )
    is_existing = await ChatMember.is_existing(user_entered.id, channel.id)
    chat_member = await ChatMember.create_or_get(user_entered, channel, inviter)

    # участвует ли пользователь в розыгрыше
    await draw_member_joined(update, channel, user_entered, inviter, is_existing)

    if not chat_member:
        text = await f(
            "chat member not created",
            bot_username=bot_from_db.username,
            user_chat_id=update.from_user.id,
            user_full_name=update.from_user.full_name,
            username=f"@{update.from_user.username}",
        )
        return await send_message_to_platform_admins(text)

    is_rules_ok = chat_member.check_rules_agreement()
    if chat_member.ban_user_status and not is_rules_ok:
        await send_rules_ban_message(chat_member)
    elif channel.check_is_allowed_to_send("welcome"):
        is_sent = False

        chat_member_user = update.new_chat_member.user
        first_name = chat_member_user.first_name
        full_name = chat_member_user.full_name
        username = chat_member_user.username

        if not channel.get_chat_message_status("welcome", "disabled"):

            message_data = await channel.get_message(
                "welcome",
                add_at_to_username=True,
                firstname=first_name,
                user_first_name=first_name,
                fullname=full_name,
                user_full_name=full_name,
                username=username,
            )

            keyboard = await get_system_message_keyboard(
                channel, bot_from_db.username
            ) if channel.need_welcome_system_keyboard else None
            footer = await channel.get_footer("welcome")

            welcome_message = await send_message_and_footer(
                update.chat.id, message_data, keyboard, footer
            )

            await FriendlyChatMessage.save(
                welcome_message, "welcome_message",
                bot=bot_from_db,
                member=chat_member
            )

            is_sent = True

        if bot_from_db.last_set_menu_message_id:
            with suppress(MessageError):
                debugger = logging.getLogger("debugger.friendly.limitations")
                debug_str = json.dumps(
                    {
                        "channel": {
                            "id": channel.id,
                            "name": channel.name,
                        },
                        "user": chat_member_user.to_python(),
                        "message_id": bot_from_db.last_set_menu_message_id,
                    }
                )
                if DEBUG:
                    debugger.debug(
                        f"Deleting last_set_menu_message {channel.name}: {full_name}\n"
                        f"{debug_str}",
                        exc_info=True
                    )
                await update.bot.delete_message(
                    chat_id=channel.chat_id,
                    message_id=bot_from_db.last_set_menu_message_id
                )
        keyboard = await get_channel_menu_keyboard(channel)
        if await channel.get_menu_buttons():
            message_menu_data = await channel.get_message(
                "channel_menu_text",
                add_at_to_username=True,
                firstname=first_name if first_name else "",
                user_first_name=first_name if first_name else "",
                fullname=full_name if full_name else "",
                user_full_name=full_name if full_name else "",
                username=username if username else "",
            )
            menu_message = await send_tg_message(
                update.chat.id, keyboard=keyboard, **message_menu_data
            )
            await bot_from_db.update(last_set_menu_message_id=menu_message.message_id)

        if not chat_member.check_rules_agreement() and not is_rules_ok:
            await send_rules_notification_message(
                chat_member, message_was_deleted=False
            )
            is_sent = True

        if is_sent:
            await channel.save_sent_rate_limited_message("welcome")

    if inviter:
        await notify_inviter_about_invited_member(inviter, is_existing)
