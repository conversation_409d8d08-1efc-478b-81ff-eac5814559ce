import asyncio
from aiogram import Dispatcher, types
from aiogram.types import ContentTypes
from aiogram.dispatcher import FSMContext

from config import INTERVAL_IN_COEF
from db.models import Channel, User

from psutils.forms.helpers import with_delete_state_messages

from utils.text import f
from utils.router import Router
from psutils.convertors import str_to_float
from utils.filters.message import EndProcessButtonFilter
from utils.filters.multi import CancelButtonFilter

from friendly.helpers import send_error

from friendly.main.keyboards import get_menu_keyboard

from friendly.channel.filters.states import ChannelFilters


async def old_messages_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()

    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)

    delay = str_to_float(message.text)
    if delay is None:
        return await message.answer(await f("not float error", lang))

    interval_in_types = INTERVAL_IN_COEF
    interval_in = state_data.get("interval_in")
    if interval_in in interval_in_types:
        interval_in = "seconds"

    delay *= interval_in_types.get(interval_in)
    result = await channel.set_system_message_on_screen_time(delay)
    if not result:
        return await send_error(message)

    await message.answer(await f("delete old messages delay set", lang))

    await ChannelFilters.Action.set()
    await Router.state_menu(message, state, mode="new")


@with_delete_state_messages
async def cancel_or_end_process_button_handler(
        message: types.Message,
        state: FSMContext,
        user: User, lang: str,
        end_process: bool = False,
):
    keyboard = await get_menu_keyboard(user, lang)
    saved_or_cancel = "saved" if end_process else "cancel"
    send_coro = message.answer(await f(f"action {saved_or_cancel} text", lang), reply_markup=keyboard)
    results = await asyncio.gather(send_coro, message.delete())

    await ChannelFilters.Content.set()
    await Router.state_menu(results[0], state)


async def clear_filters_list_button_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    cur_state = await state.get_state()
    channel = await Channel.get(state_data.get("channel_id"))

    filter_type = cur_state.split(":")[1].lower()
    words = list()
    await channel.set_filter(filter_type, words)

    await message.answer(await f("channel filters words cleared message", lang))
    await Router.state_menu(message, state)


async def append_filter_handler(message: types.Message, state: FSMContext, lang: str):
    if message.content_type != "text":
        return await message.answer(await f("not text error"), lang)

    state_data = await state.get_data()
    cur_state = await state.get_state()
    channel = await Channel.get(state_data.get("channel_id"))

    filter_type = cur_state.split(":")[1].lower()
    await channel.append_filter(filter_type, message.text)

    send_coro = message.answer(await f("channel filters words added message", lang))
    results = await asyncio.gather(send_coro, message.delete())
    await Router.state_menu(results[0], state)


async def add_username_filters_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    filter_type = state_data.get("filter_type")
    await channel.append_filter(filter_type, message.text)

    await message.delete()
    await Router.state_menu(message, state, lang)


def register_channel_filters_message_handlers(dp: Dispatcher):
    dp.register_message_handler(
        add_username_filters_handler,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=ChannelFilters.UsernameFilters,
    )

    # --

    dp.register_message_handler(
        old_messages_handler,
        chat_type="private",
        content_types=ContentTypes.ANY,
        state=ChannelFilters.OldMessages,
    )

    dp.register_message_handler(
        cancel_or_end_process_button_handler,
        EndProcessButtonFilter() | CancelButtonFilter(),
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=[ChannelFilters.Message, ChannelFilters.Username]
    )

    dp.register_message_handler(
        clear_filters_list_button_handler,
        lequal="channel filters clear list button",
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=[ChannelFilters.Message, ChannelFilters.Username],
    )

    dp.register_message_handler(
        append_filter_handler,
        chat_type="private",
        content_types=ContentTypes.ANY,
        state=[ChannelFilters.Message, ChannelFilters.Username]
    )
