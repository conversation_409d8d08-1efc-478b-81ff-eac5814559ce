from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import Channel

from utils.router import Router
from psutils.fsm import get_state_name_from_field

from friendly.helpers import send_error
from core.helpers import get_data_from_callback

from friendly.channel.filters.states import ChannelFilters
from friendly.channel.edit.states import EditChannel


async def filters_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await ChannelFilters.Action.set()
    await Router.state_menu(callback_query, state, mode="edit")


async def filters_chat_notifications_handler(callback_query: types.CallbackQuery, state: FSMContext):
    user, lang, notification_type, callback_data = await get_data_from_callback(callback_query)

    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    result = await channel.change_deleting_notification_status(notification_type)
    if not result:
        return await send_error(callback_query.message)

    await Router.state_menu(callback_query, state, mode="edit")


async def filters_types_buttons_handler(callback_query: types.CallbackQuery, state: FSMContext, mode: str):
    state_name = get_state_name_from_field(mode)
    await getattr(ChannelFilters, state_name).set()
    await Router.state_menu(callback_query, state, mode="edit")


async def previous_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    cur_state = await state.get_state()
    if cur_state in [ChannelFilters.Action.state, ChannelFilters.UsernameFilters.state]:
        await EditChannel.ChooseField.set()
    else:
        await ChannelFilters.Action.set()
    await Router.state_menu(callback_query, state, mode="edit")


async def add_ussername_filters_button(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict):
    await ChannelFilters.UsernameFilters.set()
    filter_type = callback_data.get("filter_type")
    await state.update_data(filter_type=filter_type)
    await Router.state_menu(callback_query, state, mode="edit")


async def clear_filters_list_button(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    filter_type = state_data.get("filter_type")
    await channel.set_filter(filter_type, list())
    await Router.state_menu(callback_query, state)


def register_channel_filters_callback_handlers(dp: Dispatcher):
    dp.register_callback_query_handler(
        add_ussername_filters_button,
        callback_mode="set_username_filters",
        chat_type="private",
        state=EditChannel.ChooseField,
    )

    dp.register_callback_query_handler(
        clear_filters_list_button,
        callback_mode="clear_list_button",
        chat_type="private",
        state=ChannelFilters.UsernameFilters,
    )

    dp.register_callback_query_handler(
        filters_button_handler,
        callback_mode="filters",
        chat_type="private",
        state=EditChannel.ChooseField,
    )

    dp.register_callback_query_handler(
        filters_types_buttons_handler,
        callback_mode=["notifications", "content", "old_messages", "username", "message"],
        chat_type="private",
        state=ChannelFilters,
    )

    dp.register_callback_query_handler(
        filters_chat_notifications_handler,
        callback_mode=["join", "leave"],
        chat_type="private",
        state=EditChannel.ChooseField,
    )

    dp.register_callback_query_handler(
        previous_button_handler,
        previous_button=True,
        chat_type="private",
        state=ChannelFilters,
    )
