import asyncio
from datetime import timed<PERSON><PERSON>

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import Channel

from psutils.forms.helpers import save_messages_to_state

from utils.text import f
from utils.router import Router
from psutils.convertors import interval_to_str
from utils.redefined_classes import MenuKb, MenuBtn, InlineKb, InlineBtn

from .keyboards import get_chat_notification_keyboard, get_channel_filters_menu_keyboard, get_filter_content_keyboard
from utils.keyboards import previous_button, get_interval_in_types_keyboard

from .states import ChannelFilters


async def send_chat_notifications_menu(message: types.Message, state: FSMContext, lang: str):

    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    keyboard = await get_chat_notification_keyboard(channel, lang)
    message_text = await f("channel filters notifications header", lang)

    send_coro = message.answer(message_text, reply_markup=keyboard)
    results = await asyncio.gather(send_coro, message.delete())
    return results[0]


async def send_channel_filters_menu(message: types.Message, lang: str):

    keyboard = await get_channel_filters_menu_keyboard(lang)
    message_text = await f("channel filters header", lang)

    send_coro = message.answer(message_text, reply_markup=keyboard)
    results = await asyncio.gather(send_coro, message.delete())
    return results[0]


async def send_channel_filters_content_menu(message: types.Message, state: FSMContext, lang: str):
    cur_state = await state.get_state()
    state_data = await state.get_data()
    content_type = cur_state.split(":")[1].lower()

    channel = await Channel.get(state_data.get("channel_id"))
    current_filter = channel.get_filter(content_type)

    if current_filter:
        filter_words = ", ".join(current_filter)
        msg = await message.answer(await f("channel filters content filter message", lang, filter=filter_words))
        await save_messages_to_state(state, msg)

    keyboard = MenuKb(resize_keyboard=True)
    keyboard.insert(MenuBtn(text=await f("end process with icon", lang)))
    keyboard.insert(MenuBtn(text=await f("channel filters clear list button", lang)))

    message_text = await f("channel filters new content filter message", lang)
    send_coro = message.answer(message_text, reply_markup=keyboard)
    results = await asyncio.gather(send_coro, message.delete())
    await save_messages_to_state(state, results[0])
    return results[0]


async def send_channel_content_filter_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    keyboard = await get_filter_content_keyboard(channel, lang)

    message_text = await f("channel filters header", lang)
    return await message.answer(message_text, reply_markup=keyboard)


async def send_old_messages_menu(message: types.Message, state: FSMContext, lang: str, mode: str = "new"):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    old_messages_delete_delay = channel.system_message_on_screen_time

    interval_in = state_data.get("interval_in", "minutes")
    interval_in_text = await f(f"in_{interval_in}_interval", lang)

    message_text = await f("delete old messages interval message", lang, interval_in=interval_in_text)
    if old_messages_delete_delay:
        current_delay = timedelta(seconds=old_messages_delete_delay)
        current_delay = interval_to_str(current_delay, lang)
        current_delay = await f("delete old messages current", lang, delay=current_delay)
        message_text += f"\n{current_delay}"

    keyboard = await get_interval_in_types_keyboard(interval_in, lang)

    if "edit" in mode:
        return await message.edit_text(message_text, reply_markup=keyboard)
    keyboard.row(await previous_button(lang))
    return await message.answer(message_text, reply_markup=keyboard)


async def add_username_filters_menu(message: types.Message, state: FSMContext, lang: str):
    cur_state = await state.get_state()
    state_data = await state.get_data()
    filter_type = state_data.get("filter_type")

    channel = await Channel.get(state_data.get("channel_id"))
    current_filter = channel.get_filter(filter_type)

    filter_words = ", ".join(current_filter)
    message_text = await f("channel filters new content filter message", lang)  + f"\n{filter_words}"

    keyboard = InlineKb(row_width=2)
    text = await f("channel filters clear list button", lang)
    keyboard.insert(InlineBtn(text=text, callback_data="clear_list_button"))
    keyboard.insert(await previous_button(lang))

    message_id = state_data["__router_message__"]["message_id"]
    await message.bot.edit_message_text(text=message_text,
                                        reply_markup=keyboard,
                                        message_id=message_id,
                                        chat_id=message.chat.id)


def register_channel_filters_routes(router: Router):
    router.add_route(ChannelFilters.Action, send_channel_filters_menu)
    router.add_route(ChannelFilters.Notifications, send_chat_notifications_menu)
    router.add_route(ChannelFilters.Content, send_channel_content_filter_menu)
    router.add_route(ChannelFilters.OldMessages, send_old_messages_menu)
    router.add_route(ChannelFilters.Message, send_channel_filters_content_menu)
    router.add_route(ChannelFilters.Username, send_channel_filters_content_menu)

    router.add_route(ChannelFilters.UsernameFilters, add_username_filters_menu)
