from db.models import Channel

from utils.text import f
from utils.redefined_classes import InlineKb, InlineBtn

from utils.keyboards import previous_button, active_button


async def get_chat_notification_keyboard(channel: Channel, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)

    for notification_type in Channel.NOTIFICATION_TYPES:
        text = await f(f"channel delete {notification_type} notification", lang)
        status = channel.get_deleting_notification_status(notification_type)
        if not status:
            text = await active_button(lang, text)
        keyboard.insert(InlineBtn(text, callback_data=notification_type))

    keyboard.insert(await previous_button(lang))
    return keyboard


async def get_channel_filters_menu_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=1)

    button_text = await f("channel filters notifications button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="notifications"))

    button_text = await f("channel filters content button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="content"))

    button_text = await f("channel filters system messages deleting time button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="old_messages"))

    keyboard.insert(await previous_button(lang))
    return keyboard


async def get_filter_content_keyboard(channel: Channel, lang: str):
    keyboard = InlineKb(row_width=1)
    for content_type in Channel.FILTER_CONTENT_TYPES:
        state = channel.get_filter(content_type)
        text = await f(f"channel filters {content_type} button", lang)
        if state:
            text = await active_button(lang, text)
        keyboard.insert(InlineBtn(text, callback_data=content_type))

    keyboard.row(await previous_button(lang))
    return keyboard
