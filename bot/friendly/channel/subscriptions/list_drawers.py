from typing import Any, Dict, List

from aiogram import types
from aiogram.dispatcher import FSMContext

from db.models import ClientBot, FriendlyChatListParams, User, Channel

from utils.text import f, c
from utils.redefined_classes import InlineKb, InlineBtn
from utils.router import Router
from utils.router.route_handlers import BaseListDrawer

from ..db_funcs import get_channels

from .states import SubscriptionsList


class SubscriptionsListDrawer(BaseListDrawer):
    config_page_size_variable_name = "CHATS_SUBSCRIBE_LIST_PAGE_SIZE"

    message_text_variable = "subscriptions list header"
    empty_text_variable = "chat list empty text"

    need_setup_pagination_handler = True

    pagination_callback_mode = "subscriptions_pagination"

    need_previous_button = False
    row_width = 3

    @classmethod
    async def get_position(cls, position: int):
        return position

    @classmethod
    async def update_position(cls, new_position: int, channels_mode: str, chats_lists_params: FriendlyChatListParams):
        await chats_lists_params.update(channels_mode, position=new_position)

    @classmethod
    async def get_data_from_state(cls, user: User, state: FSMContext, mode: str = "new") -> Dict[str, Any]:
        bot_id = ClientBot.get_current_bot_id()
        chats_lists_params = await FriendlyChatListParams.get(user.id, bot_id)

        channels_mode = chats_lists_params.channels_mode
        list_params = getattr(chats_lists_params, channels_mode)

        position = list_params.get("position")
        return dict(
            position=position,
            channels_mode=channels_mode,
            chats_lists_params=chats_lists_params,
        )

    @classmethod
    async def make_get_objects_kwargs(
            cls,
            user: User,
            search_text: str,
            data_from_state: Dict[str, Any],
    ) -> Dict[str, Any]:

        state_data = dict(user=user, search_text=search_text)
        return state_data

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: str = "all",
    ) -> List[Channel] | int:
        user: User = get_objects_kwargs.get("user")
        bot_id = ClientBot.get_current_bot_id()
        channels = await get_channels(user.id, bot_id, position, limit, operation)
        return channels

    @classmethod
    async def object_drawer(cls, channel: Channel, keyboard: InlineKb, lang: str):
        keyboard.insert(await channel.get_channel_button(lang))

        callback_data = c("channel_info", channel_id=channel.id)
        keyboard.insert(InlineBtn(await f("show info button", lang), callback_data=callback_data))

        url = channel.group.get_chat_link()
        keyboard.insert(InlineBtn(await f("contact admin short", lang), url=url))

    @classmethod
    async def pagination_callback_handler(
            cls, callback_query: types.CallbackQuery,
            state: FSMContext,
            mode: str, callback_data: dict,
            user: User, lang: str,
    ):
        position = callback_data.get("position")
        await user.update_friendly_channel_lists_params(position=position)

        await state.finish()
        await SubscriptionsList.set()
        await Router.state_menu(callback_query, state, lang)
