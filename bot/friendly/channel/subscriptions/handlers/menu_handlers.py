from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import User
from utils.router import Router

from ..states import SubscriptionsList


async def subscriptions_button_handler(message: types.Message, state: FSMContext, user: User):
    await state.finish()
    await user.update_friendly_channel_lists_params(channels_mode="subscriptions")
    await SubscriptionsList.set()
    await Router.state_menu(message, state)


def register_subscriptions_menu_handlers(dp: Dispatcher):

    dp.register_message_handler(
        subscriptions_button_handler,
        lequal="subscriptions button",
        chat_type="private",
        content_types=types.ContentTypes.TEXT,
        state="*"
    )
