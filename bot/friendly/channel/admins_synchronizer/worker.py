import asyncio
import logging


from db import own_session
from .db_funcs import get_channels_for_synchronize_admins
from .functions import synchronize_channel
from utils.redefined_classes import Bot


@own_session
async def synchronize_channels():
    bot = Bot.get_current()
    channels = await get_channels_for_synchronize_admins()
    for channel in channels:
        try:
            await synchronize_channel(bot, channel)
        except Exception as e:
            logger = logging.getLogger()
            logger.error(e, exc_info=True)
        finally:
            await asyncio.sleep(1/30)
