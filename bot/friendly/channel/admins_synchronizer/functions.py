from aiogram import types
from aiogram.utils.exceptions import Unauthorized, ChatNotFound, MigrateToChat

from core.user.functions import create_or_update_messanger_user
from db.models import Channel, ClientBot
from utils.redefined_classes import Bot


async def update_channel_admin_list(
        channel: Channel,
        updated_list: list[types.ChatMemberOwner | types.ChatMemberAdministrator]
):
    """
    Update channel's administrators list.
    """
    admins_users = filter(lambda x: x.user.is_bot is False, updated_list)
    users_list = [await create_or_update_messanger_user(chat_member.user) for chat_member in admins_users]
    return await channel.update(admins=users_list)


async def synchronize_admins(update: types.ChatMemberUpdated):
    bot_from_db = await ClientBot.get_current()
    channel = await Channel.get(chat_id=update.chat.id, bot_id=bot_from_db.id)
    updated_admins_list = await update.bot.get_chat_administrators(update.chat.id)
    await update_channel_admin_list(channel, updated_admins_list)


async def synchronize_channel(bot: Bot, channel: Channel, second: bool = False):
    bot_from_db = channel.bot
    with bot.with_token(bot_from_db.token):

        try:
            bot_member = await bot.get_chat_member(channel.chat_id, bot_from_db.telegram_bot_id)

            if bot_member.status in ["administrator", "owner"] and \
                    getattr(bot_member, "can_delete_messages", False):
                admins = await bot.get_chat_administrators(channel.chat_id)
                bot_admin = True
            else:
                admins = []
                bot_admin = False

        except MigrateToChat as e:
            if second:
                return
            if await Channel.get(chat_id=e.migrate_to_chat_id, bot_id=channel.bot_id):
                return
            await channel.update(chat_id=e.migrate_to_chat_id)
            return await synchronize_channel(bot, channel, True)
        except Unauthorized:
            return await channel.set_bot_is_not_admin()
        except ChatNotFound:
            return await channel.set_bot_is_not_admin()
        else:
            if bot_admin:
                await channel.set_bot_is_admin()
            else:
                await channel.set_bot_is_not_admin()
            await update_channel_admin_list(channel, admins)
