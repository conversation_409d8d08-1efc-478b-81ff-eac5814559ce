from aiogram import Dispatcher, types
from aiogram.dispatcher import FSMContext

from db.models import User
from utils.router import Router

from ..states import EditChannel


async def admin_button_handler(message: types.Message, state: FSMContext, user: User):
    await user.update_friendly_channel_lists_params(channels_mode="admin")
    await state.finish()
    await EditChannel.first()
    await Router.state_menu(message, state)


async def super_admin_button_handler(message: types.Message, state: FSMContext, user: User):
    await user.update_friendly_channel_lists_params(channels_mode="super_admin")
    await state.finish()
    await EditChannel.first()
    await Router.state_menu(message, state)


def register_edit_channel_menu_handlers(dp: Dispatcher):

    dp.register_message_handler(
        admin_button_handler,
        lequal="admin button",
        chat_type="private",
        content_types=types.ContentTypes.TEXT,
        state="*",
    )

    dp.register_message_handler(
        super_admin_button_handler,
        lequal="super admin button",
        chat_type="private",
        content_types=types.ContentTypes.TEXT,
        state="*",
    )
