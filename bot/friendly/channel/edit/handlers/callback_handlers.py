from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext

from db.models import Channel, ClientBot, User

from psutils.forms.helpers import with_delete_state_messages
from utils.text import f
from psutils.fsm import get_state_name_from_field
from utils.router import Router

from friendly.helpers import send_error

from friendly.footer.edit.states import EditFooter

from friendly.channel.mailing.states import Location<PERSON>ailingState

from ..states import EditChannel


async def filter_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, user: User,
):
    filter_type = callback_data.get("ft")

    friendly_channel_lists_params = await user.friendly_channel_lists_params
    await friendly_channel_lists_params.update_filter(filter_type)

    await Router.state_menu(callback_query, state)


async def location_filter_button_handler(
        callback_query: types.CallbackQuery, state: FSMContext
):
    await EditChannel.ChooseLocationFilter.set()
    await Router.state_menu(callback_query, state)


async def chosen_location_filter_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, user: User,
):
    location_id = callback_data.get("name", None)

    friendly_channel_lists_params = await user.friendly_channel_lists_params
    await friendly_channel_lists_params.update_location_filter(location_id)

    await EditChannel.ChooseChannel.set()
    await Router.state_menu(callback_query, state)


async def edit_channel_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
        user: User, lang: str,
):
    bot_from_db_id = ClientBot.get_current_bot_id()
    channel = await Channel.get(callback_data.get("channel_id"))

    if not await channel.is_user_has_permission(user, bot_from_db_id):
        await state.finish()
        await EditChannel.first()
        await Router.state_menu(callback_query, state, lang)
        text = await f("not admin error", lang, group_name=channel.name)
        return await callback_query.answer(text, show_alert=True)

    await state.update_data(**callback_data)
    await EditChannel.ChooseField.set()
    await Router.state_menu(callback_query, state)


async def statistics_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, mode: str, callback_data: dict,
):
    await state.update_data(**callback_data)
    state_name = get_state_name_from_field(mode)
    await getattr(EditChannel, state_name).set()
    await Router.state_menu(callback_query, state)


async def refresh_messages_button_handler(
        callback_query: types.CallbackQuery,
        callback_data: dict, lang: str,
):
    channel = await Channel.get(callback_data.get("channel_id"))
    result = await channel.refresh_messages()
    if not result:
        return await send_error(callback_query.message)

    text = await f("messages refreshed success", lang, group_name=channel.name)
    await callback_query.answer(text, show_alert=True, cache_time=0)


async def refresh_messages_daily_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict,
):
    channel = await Channel.get(callback_data.get("channel_id"))
    result = await channel.change_refresh_status()
    if not result:
        return await send_error(callback_query.message)

    await Router.state_menu(callback_query, state)


async def change_need_agree_rules_status_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict,
):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    users_type = callback_data.get("users_type")
    await channel.change_chat_rules_status(users_type)

    await Router.state_menu(callback_query, state)


async def change_count_massages_before_rules_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
):
    await EditChannel.SetLimitMessagesBeforeRules.set()
    await Router.state_menu(callback_query, state)


async def count_massages_before_rules_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict
):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    count_messages = callback_data.get("count")
    await channel.set_new_allowed_messages_given_without_rules(count_messages)
    await Router.state_menu(callback_query, state)


async def change_message_status_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict, lang: str
):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    status_type = callback_data.get("s")
    message_type = callback_data.get("m")

    status_type = "show" if status_type == "s" else "default"

    if status_type == "default" and not channel.get_chat_message_status(message_type, status_type):
        alert_text = await f("default message is used text", lang)
        await callback_query.answer(alert_text, show_alert=True)

    await channel.change_chat_message_status(message_type, status_type)

    mode = "edit_drawed" if status_type == "show" else "edit"
    await Router.state_menu(callback_query, state, lang, mode)


async def rm_search_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext, user: User, lang: str,
):
    await user.update_friendly_channel_lists_params(search_text=None, position=0)
    await Router.state_menu(callback_query, state, lang)


@with_delete_state_messages
async def previous_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):

    cur_state = await state.get_state()
    mode = "edit"
    if cur_state == EditChannel.ChooseField.state:
        await state.finish()
        await EditChannel.first()
    elif cur_state == EditChannel.ChooseLocationFilter.state:
        await EditChannel.ChooseChannel.set()
    elif cur_state == EditChannel.RulesMenu.state:
        await EditChannel.SetMessage.set()
        mode = "edit_drawed"
    elif cur_state == EditChannel.SetBanTime.state:
        await EditChannel.RulesMenu.set()
    elif cur_state == EditChannel.SetMessageRateLimit.state:
        await EditChannel.SetMessage.set()
    elif cur_state == EditChannel.ButtonPosition.state:
        await EditChannel.MenuButtonSettings.set()
    elif cur_state == EditChannel.EditButtonsMenu.state:
        await EditChannel.MenuButtonSettings.set()
    elif cur_state == EditChannel.EditButtonMenu.state:
        await EditChannel.EditButtonsMenu.set()
    elif cur_state == EditChannel.EditNewlineButtons.state:
        await EditChannel.MenuButtonSettings.set()
    elif cur_state == EditChannel.ShowMessage.state:
        await EditChannel.SetMessage.set()
    elif cur_state == EditChannel.SetLimitMessagesBeforeRules.state:
        await EditChannel.RulesMenu.set()
    elif cur_state == EditChannel.SetMessageCount.state:
        await EditChannel.SubscribeMenu.set()
    else:
        await EditChannel.ChooseField.set()
    await Router.state_menu(callback_query, state, lang, mode=mode, set_state_message=True)


# New edit channel interface
async def button_set_message(callback_query: types.CallbackQuery, state: FSMContext, callback_data: dict, lang: str):
    await EditChannel.SetMessage.set()

    await state.update_data(message_type=callback_data.get("message_type"))
    await Router.state_menu(callback_query, state, lang, get_state_message=True, set_state_message=True)


async def button_set_limit(callback_query: types.CallbackQuery, state: FSMContext):
    await EditChannel.SetLimit.set()
    await Router.state_menu(callback_query, state)


async def button_set_alert_threshold(callback_query: types.CallbackQuery, state: FSMContext):
    await EditChannel.SetAlertThreshold.set()
    await Router.state_menu(callback_query, state)


async def button_set_always_alert(callback_query: types.CallbackQuery, state: FSMContext):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    await channel.set_new_alert_threshold(0)
    await Router.state_menu(callback_query, state)


async def button_set_messages_ratio(callback_query: types.CallbackQuery, state: FSMContext):
    await EditChannel.SetMessagesRatio.set()
    await Router.state_menu(callback_query, state)


async def button_set_limited_message_on_screen_time(callback_query: types.CallbackQuery, state: FSMContext):
    await EditChannel.SetLimitedMessageOnScreenTime.set()
    await Router.state_menu(callback_query, state)


async def button_unlim_limit(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict
):
    channel_id = callback_data.get("channel_id")
    channel = await Channel.get(channel_id)
    # Если лимит сообщений 0 - разрешаем писать безлимитно
    await channel.set_new_allowed_messages(new_allowed_messages=0)

    await Router.state_menu(callback_query, state)


async def button_set_message_length(callback_query: types.CallbackQuery, state: FSMContext):
    await EditChannel.SetMessagesLength.set()
    await Router.state_menu(callback_query, state)


async def button_set_unlim_message_length(
        callback_query: types.CallbackQuery,
        state: FSMContext, callback_data: dict
):
    channel_id = callback_data.get("channel_id")
    channel = await Channel.get(channel_id)
    # Если длина 0 - разрешаем писать безлимитно
    await channel.set_max_messages_length(max_messages_length=0)

    await Router.state_menu(callback_query, state)


async def button_menu_rules(callback_query: types.CallbackQuery, state: FSMContext):
    await EditChannel.RulesMenu.set()
    await Router.state_menu(callback_query, state, set_state_message=True)


# Функционал пока отложили
async def button_set_ban_time(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await EditChannel.SetBanTime.set()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def rate_limit_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await EditChannel.SetMessageRateLimit.set()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def button_subscribe_menu_button(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await EditChannel.SubscribeMenu.set()
    await Router.state_menu(callback_query, state, lang)


async def disable_required_subscription_button(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    await channel.set_required_resource_subscription_chat_id(0)
    await Router.state_menu(callback_query, state, lang)


async def update_info_subscription_button(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await Router.state_menu(callback_query, state, lang)


async def menu_button_settings_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await EditChannel.MenuButtonSettings.set()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


@with_delete_state_messages
async def show_message_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await EditChannel.ShowMessage.set()
    await Router.state_menu(callback_query, state, lang, set_state_message=True)


async def show_locations(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await EditChannel.SetLocation.set()
    await Router.state_menu(callback_query, state, lang)


async def set_location(
        callback_query: types.CallbackQuery,
        callback_data: dict,
        state: FSMContext, lang: str
):
    location = callback_data.get("name")

    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    await channel.group.update(location=location)

    await EditChannel.ChooseField.set()
    await Router.state_menu(callback_query, state, lang)


async def location_mailing_button_handler(callback_query: types.CallbackQuery, state: FSMContext):
    await LocationMailingState.CreateMessage.set()
    await Router.state_menu(callback_query, state)


async def set_message_count_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await EditChannel.SetMessageCount.set()
    await Router.state_menu(callback_query, state, lang)


async def button_edit_footer(callback_query: types.CallbackQuery, state: FSMContext):
    await state.update_data(prev="channel")
    await EditFooter.ChooseFooter.set()
    await Router.state_menu(callback_query, state)


async def button_select_footer(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
):
    await state.update_data(**callback_data)
    await state.update_data(prev="messages")
    await EditFooter.ChooseFooter.set()
    await Router.state_menu(callback_query, state)


async def show_errors_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    await EditChannel.ShowErrors.set()
    await Router.state_menu(callback_query, state, lang)


async def clear_errors_button_handler(callback_query: types.CallbackQuery, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    await channel.add_log(None)

    await Router.state_menu(callback_query, state, lang)


async def change_need_system_keyboard_status_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    await channel.change_need_welcome_system_keyboard_status()

    await Router.state_menu(callback_query, state)


async def change_language_button_handler(
        callback_query: types.CallbackQuery,
        state: FSMContext,
        callback_data: dict,
        lang: str
):
    cur_state = await state.get_state()
    state_data = await state.get_data()
    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)

    if cur_state == EditChannel.SetLanguage.state:
        new_lang = callback_data.get("lang")
        await channel.group.update(lang=new_lang)

    else:
        await state.update_data(group_id=channel.group_id)
        await EditChannel.SetLanguage.set()
    await Router.state_menu(callback_query, state, lang)


def register_edit_channel_callback_handlers(dp: Dispatcher):

    dp.register_callback_query_handler(
        show_message_handler,
        callback_mode="msg_show",
        state=EditChannel.SetMessage,
    )

    dp.register_callback_query_handler(
        set_message_count_button_handler,
        callback_mode="set_message_count",
        state=EditChannel.SubscribeMenu,
    )

    dp.register_callback_query_handler(
        menu_button_settings_handler,
        callback_mode="menu_button_settings",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        button_set_message,
        callback_mode="set_message",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        show_locations,
        callback_mode="change_location",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        set_location,
        callback_mode="location",
        state=EditChannel.SetLocation,
    )

    dp.register_callback_query_handler(
        location_mailing_button_handler,
        callback_mode="location_mailing",
        state=EditChannel.ChooseChannel,
    )

    dp.register_callback_query_handler(
        button_edit_footer,
        callback_mode="edit_footer",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        button_select_footer,
        callback_mode="select_footer",
        state=EditChannel.SetMessage,
    )

    dp.register_callback_query_handler(
        change_need_system_keyboard_status_button_handler,
        callback_mode="system_keyboard_menu_button",
        state=EditChannel.SetMessage,
    )

    dp.register_callback_query_handler(
        button_set_limit,
        callback_mode="set_limit",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        button_set_alert_threshold,
        callback_mode="set_alert_threshold",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        button_set_always_alert,
        callback_mode="always_alert",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        button_set_messages_ratio,
        callback_mode="set_messages_ratio",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        button_set_limited_message_on_screen_time,
        callback_mode="set_limited_message_on_screen_time",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        button_subscribe_menu_button,
        callback_mode="subscribe_menu",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        disable_required_subscription_button,
        callback_mode="disable_required_subscription",
        state=EditChannel.SubscribeMenu,
    )

    dp.register_callback_query_handler(
        update_info_subscription_button,
        callback_mode="update_info",
        state=EditChannel.SubscribeMenu,
    )

    dp.register_callback_query_handler(
        button_unlim_limit,
        callback_mode="unlim_limit_button",
        state=EditChannel.SetLimit,
    )

    dp.register_callback_query_handler(
        button_set_message_length,
        callback_mode="set_message_length",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        button_set_unlim_message_length,
        callback_mode="set_unlim_message_length_button",
        state=EditChannel.SetMessagesLength,
    )

    dp.register_callback_query_handler(
        button_menu_rules,
        callback_mode="necessarily_menu_button",
        state=EditChannel.SetMessage,
    )

    # Функционал пока отложили
    # dp.register_callback_query_handler(
    #     button_set_ban_time,
    #     callback_mode="set_ban_time",
    #     state=EditChannel.RulesMenu,
    # )

    dp.register_callback_query_handler(
        filter_button_handler,
        callback_mode="filter",
        state=EditChannel.ChooseChannel,
    )

    dp.register_callback_query_handler(
        location_filter_button_handler,
        callback_mode="l_filter",
        state=EditChannel.ChooseChannel,
    )

    dp.register_callback_query_handler(
        chosen_location_filter_button_handler,
        callback_mode="location",
        state=EditChannel.ChooseLocationFilter,
    )

    dp.register_callback_query_handler(
        edit_channel_button_handler,
        callback_mode="edit_channel",
        chat_type="private",
        state=EditChannel.ChooseChannel,
    )

    dp.register_callback_query_handler(
        statistics_button_handler,
        callback_mode="statistics",
        chat_type="private",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        refresh_messages_button_handler,
        callback_mode="refresh_messages",
        chat_type="private",
        state=[EditChannel.SetLimit, EditChannel.SetMessageCount],
    )

    dp.register_callback_query_handler(
        refresh_messages_daily_button_handler,
        callback_mode="refresh_messages_daily",
        chat_type="private",
        state=[EditChannel.SetLimit, EditChannel.SetMessageCount],
    )

    dp.register_callback_query_handler(
        change_need_agree_rules_status_button_handler,
        callback_mode="change_need_agree_rules_status",
        state=EditChannel.RulesMenu,
    )

    dp.register_callback_query_handler(
        change_count_massages_before_rules_button_handler,
        callback_mode="change_count_massages_before_rules",
        state=EditChannel.RulesMenu,
    )

    dp.register_callback_query_handler(
        count_massages_before_rules_button_handler,
        callback_mode="rules_message_limit",
        state=EditChannel.SetLimitMessagesBeforeRules,
    )

    dp.register_callback_query_handler(
        change_message_status_handler,
        callback_mode="msg",
        chat_type="private",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        rate_limit_button_handler,
        callback_mode="rate_limit",
        state=EditChannel.SetMessage,
    )

    dp.register_callback_query_handler(
        rm_search_button_handler,
        callback_mode="rm_search",
        chat_type="private",
        state=EditChannel.ChooseChannel,
    )

    dp.register_callback_query_handler(
        previous_button_handler,
        previous_button=True,
        chat_type="private",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        show_errors_button_handler,
        callback_mode="show_errors",
        chat_type="private",
        state=EditChannel,
    )

    dp.register_callback_query_handler(
        clear_errors_button_handler,
        callback_mode="clear_errors",
        chat_type="private",
        state=EditChannel.ShowErrors,
    )

    dp.register_callback_query_handler(
        change_language_button_handler,
        callback_mode=["change_language", "lang"],
        chat_type="private",
        state=EditChannel,
    )
