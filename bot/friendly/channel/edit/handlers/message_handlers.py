import re

from aiogram import Dispatcher, types
from aiogram.types import ContentTypes
from aiogram.dispatcher import FSMContext

from db.models import Channel, ClientBot
from psutils.convertors import str_to_float
from psutils.exceptions import ErrorWithTextVariable
from psutils.forms.helpers import save_messages_to_state, delete_messages

from utils.text import f
from core.media import download_file
from utils.router import Router
from ..functions import get_chat

from ..states import EditChannel


async def set_message_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()

    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)
    message_type = state_data.get("message_type")

    new_text = message.text if message.text else message.caption if message.caption else ""
    file_path = await download_file(message)
    new_message_data = {"text": new_text}
    if file_path:
        new_message_data[message.content_type] = file_path

    await channel.set_message(message_type, new_message_data)
    if channel.get_chat_message_status(message_type, "default"):
        await channel.change_chat_message_status(message_type, "default")
    await message.answer(await f("new channel message saved", lang))

    await message.delete()
    await Router.state_menu(message, state, lang, get_state_message=True, set_state_message=True)


async def set_limit_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()

    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)

    try:
        count = int(message.text)
    except ValueError:
        await message.delete()
        return

    if count >= 0:
        await channel.set_new_allowed_messages(new_allowed_messages=int(message.text))
        await channel.refresh_messages()

    await message.delete()
    await Router.state_menu(message, state, lang)


async def set_limit_messages_before_rules_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()

    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)

    try:
        count = int(message.text)
    except ValueError:
        await message.delete()
        return

    if count >= 0:
        await channel.set_new_allowed_messages_given_without_rules(new_allowed_messages=int(message.text))

    await message.delete()
    await Router.state_menu(message, state, lang, get_state_message=True)


async def set_alert_threshold_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()

    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)

    try:
        count = int(message.text)
    except ValueError:
        await message.delete()
        return

    if count >= 0:
        await channel.set_new_alert_threshold(count)

    await message.delete()
    await Router.state_menu(message, state, lang)


async def set_messages_ratio_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()

    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)

    if message.content_type == "text" and re.fullmatch(r"\d+[/\\]\d+", message.text):
        messages_for_friends, friends_required = [int(value) for value in re.split(r"[/|\\]", message.text)]
        await channel.set_new_ratio(messages_for_friends, friends_required)

    await message.delete()
    await Router.state_menu(message, state, lang)


async def set_messages_length_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()

    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)

    try:
        count = int(message.text)
    except ValueError:
        await message.delete()
        return

    if count >= 0:
        await channel.set_max_messages_length(count)

    await message.delete()
    await Router.state_menu(message, state, lang)


async def set_ban_time_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()

    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)
    if message.text.isdecimal():
        value = int(message.text)
        await channel.set_limited_agree_rules_time(value)

    await message.delete()
    await Router.state_menu(message, state, lang)


async def set_limited_message_on_screen_time_handler(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()

    channel_id = state_data.get("channel_id")
    channel = await Channel.get(channel_id)

    try:
        count = int(message.text)
    except ValueError:
        await message.delete()
        return

    if count >= 0:
        await channel.set_limited_message_on_screen_time(count)
        await channel.set_system_message_on_screen_time(count)

    await message.delete()
    await Router.state_menu(message, state, lang)


async def set_message_rate_limit(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()

    channel = await Channel.get(state_data.get("channel_id"))

    try:
        rate_limit = str_to_float(message.text, only_positive=True)
    except ErrorWithTextVariable as e:
        msg = await message.answer(await f(e.text_variable, lang))
        await message.delete()
        return await save_messages_to_state(state, msg)

    message_type = state_data.get("message_type")

    await channel.update({f"{message_type}_message_rate_limit": rate_limit})

    await message.delete()

    async with state.proxy() as state_data:
        messages = state_data.pop("messages", [])

    await Router.state_menu(message, state, lang, get_state_message=True)
    await delete_messages(message.chat.id, messages)


async def set_required_resource(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    text = message.text

    # Обязательная подписка уже включена
    if channel.required_resource_subscription_chat_id:
        return

    # Является ли добавленый текст ссылкой
    # Переводим тип "https://t.me/username" в @username
    if "http" in text:
        text = '@' + text.split('/')[-1]

    # Проверка добавляем мы бот или чат/канал
    if text[-3:].lower() == "bot":
        # Проверка, является ли бот нашим(Обязательная подписка доступна только для наших ботов)
        cur_bot = await ClientBot.get(username=text[1:])
        if cur_bot:
            await channel.set_required_resource_subscription_chat_id(cur_bot.id)
        else:
            await state.update_data(bot_kicked=True)
    else:
        # Проверка, можем ли мы получить доступ к чату/каналу
        chat_info = await get_chat(message, text)
        if chat_info:
            await channel.set_required_resource_subscription_chat_id(chat_info.id)
        else:
            await state.update_data(bot_kicked=True)

    await message.delete()
    await Router.state_menu(get_state_message=True, state=state, lang=lang)


async def set_message_count(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    count = message.text

    try:
        count = int(count)
        await channel.update(max_count_messages_before_required_resource=count)
        await Router.state_menu(get_state_message=True, state=state, lang=lang)
    except Exception as e:
        pass

    await message.delete()


def register_edit_channel_message_handlers(dp: Dispatcher):

    dp.register_message_handler(
        set_message_handler,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SetMessage,
    )

    dp.register_message_handler(
        set_limit_handler,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SetLimit,
    )

    dp.register_message_handler(
        set_limit_messages_before_rules_handler,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SetLimitMessagesBeforeRules,
    )

    dp.register_message_handler(
        set_alert_threshold_handler,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SetAlertThreshold,
    )

    dp.register_message_handler(
        set_messages_ratio_handler,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SetMessagesRatio,
    )

    dp.register_message_handler(
        set_messages_length_handler,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SetMessagesLength,
    )

    dp.register_message_handler(
        set_ban_time_handler,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SetBanTime,
    )

    dp.register_message_handler(
        set_limited_message_on_screen_time_handler,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SetLimitedMessageOnScreenTime,
    )

    dp.register_message_handler(
        set_message_rate_limit,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SetMessageRateLimit,
    )

    dp.register_message_handler(
        set_required_resource,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SubscribeMenu,
    )

    dp.register_message_handler(
        set_message_count,
        chat_type="private",
        content_types=ContentTypes.TEXT,
        state=EditChannel.SetMessageCount,
    )
