from typing import Any, Dict, List

from aiogram import types
from aiogram.dispatcher import FSMContext

from db import crud
from db.models import Channel, ClientBot, FriendlyChatListParams, Group, User
from friendly.schedule.functions import get_schedules_count
from utils.keyboards import active_button, previous_button
from utils.redefined_classes import InlineBtn, InlineKb
from utils.router import Router
from utils.router.route_handlers import BaseListDrawer
from utils.text import c, f
from utils.translator import Translator
from .callback_data import LangCallbackData
from .states import EditChannel
from ..base_list_drawers import BaseChannelsListDrawer


class ChannelsListDrawer(BaseChannelsListDrawer):
    config_page_size_variable_name = "CHATS_SUBSCRIBE_LIST_PAGE_SIZE"

    message_text_variable = "my chats list admin"
    empty_text_variable = "admin chat list empty text"

    row_width = 4

    @classmethod
    async def get_position(cls, position: int):
        return position

    @classmethod
    async def update_position(
            cls, new_position: int, channels_mode: str,
            chats_lists_params: FriendlyChatListParams
    ):
        await chats_lists_params.update(channels_mode, position=new_position)

    @classmethod
    async def update_search(
            cls, new_search_text: str, channels_mode: str,
            chats_lists_params: FriendlyChatListParams
    ):
        await chats_lists_params.update(
            channels_mode, search_text=new_search_text, position=0
        )

    @classmethod
    async def get_data_from_state(
            cls, user: User, state: FSMContext, mode: str = "new"
    ) -> Dict[str, Any]:
        bot_id = ClientBot.get_current_bot_id()
        chats_lists_params = await FriendlyChatListParams.get(user.id, bot_id)

        channels_mode = chats_lists_params.channels_mode
        list_params = getattr(chats_lists_params, channels_mode)

        position = list_params.get("position")
        search_text = list_params.get("search_text")
        is_active_chats_filter = list_params.get("is_active_chats_filter", True)
        is_active_channels_filter = list_params.get("is_active_channels_filter", True)
        is_active_is_admin_filter = list_params.get("is_active_is_admin_filter", True)
        is_active_is_not_admin_filter = list_params.get(
            "is_active_is_not_admin_filter", True
        )
        location_id_filter = list_params.get("location_id_filter", None)

        return dict(
            position=position,
            search_text=search_text,
            channels_mode=channels_mode,
            is_active_channels_filter=is_active_channels_filter,
            is_active_chats_filter=is_active_chats_filter,
            is_active_is_admin_filter=is_active_is_admin_filter,
            is_active_is_not_admin_filter=is_active_is_not_admin_filter,
            location_id_filter=location_id_filter,
            chats_lists_params=chats_lists_params,
        )

    @classmethod
    async def make_get_objects_kwargs(
            cls, user: User, search_text: str, data_from_state: Dict[str, Any]
    ):
        data = await super().make_get_objects_kwargs(user, search_text, data_from_state)

        channels_mode = data_from_state.get("channels_mode")
        position = data_from_state.get("position")
        is_active_chats_filter = data_from_state.get("is_active_chats_filter")
        is_active_channels_filter = data_from_state.get("is_active_channels_filter")
        is_active_is_admin_filter = data_from_state.get("is_active_is_admin_filter")
        is_active_is_not_admin_filter = data_from_state.get(
            "is_active_is_not_admin_filter"
        )
        location_id_filter = data_from_state.get("location_id_filter", None)

        data.update(
            position=position,
            channels_mode=channels_mode,
            is_active_channels_filter=is_active_channels_filter,
            is_active_chats_filter=is_active_chats_filter,
            is_active_is_admin_filter=is_active_is_admin_filter,
            is_active_is_not_admin_filter=is_active_is_not_admin_filter,
            location_id_filter=location_id_filter,
        )

        return data

    @classmethod
    async def object_drawer(
            cls, channel: Channel, user: User, keyboard: InlineKb, lang: str
    ):
        keyboard.row()
        keyboard.insert(await channel.get_channel_button(lang))

        button_text = await f("edit channel button", lang)

        if channel.chat_type in ["group", "supergroup"]:
            button_text = await f("group chat type", lang, handle=button_text)
        else:
            button_text = await f("channel chat type", lang, handle=button_text)

        if channel.is_bot_admin:
            button_text = await f("is admined", lang, handle=button_text)
        else:
            button_text = await f("is not admined", lang, handle=button_text)

        keyboard.insert(
            InlineBtn(
                button_text, callback_data=c("edit_channel", channel_id=channel.id)
            )
        )

        count_schedules = await get_schedules_count(channel.id)
        button_text = await f("post schedules button", lang, count=count_schedules)
        keyboard.insert(
            InlineBtn(button_text, callback_data=c("schedules", channel_id=channel.id))
        )

        button_text = await f("admin chats without count users button", lang)
        keyboard.insert(
            InlineBtn(
                button_text, callback_data=c("correspondences", channel_id=channel.id)
            )
        )

    @classmethod
    async def footer_drawer(
            cls,
            channels_mode: str,
            all_objects_count: int,
            is_active_channels_filter: bool,
            is_active_chats_filter: bool,
            is_active_is_admin_filter: bool,
            is_active_is_not_admin_filter: bool,
            keyboard: InlineKb,
            lang: str,
    ):
        if channels_mode == "admin":
            keyboard.row(
                InlineBtn(
                    await f("add channel button", lang), callback_data="add_channel"
                )
            )
            keyboard.insert(
                InlineBtn(
                    await f("mailing button", lang), callback_data="location_mailing"
                )
            )

    @classmethod
    async def header_drawer(
            cls,
            channels_mode: str,
            is_active_channels_filter: bool,
            is_active_chats_filter: bool,
            is_active_is_admin_filter: bool,
            is_active_is_not_admin_filter: bool,
            location_id_filter: str | None,
            keyboard: InlineKb, lang: str,
    ):
        if location_id_filter:
            location_value = location_id_filter
        else:
            location_value = await f("friendly location filter all", lang)
        button_text = await f("friendly location filter", lang, location=location_value)
        callback_data = "l_filter"
        keyboard.row(InlineBtn(button_text, callback_data=callback_data))

        if channels_mode == "super_admin":
            buttons_row = list()
            filters = dict(
                is_active_channels_filter=is_active_channels_filter,
                is_active_chats_filter=is_active_chats_filter,
                is_active_is_admin_filter=is_active_is_admin_filter,
                is_active_is_not_admin_filter=is_active_is_not_admin_filter,
            )

            for filter_type, value in filters.items():
                button_text = await f(f"friendly superadmin {filter_type}", lang)
                if value:
                    button_text = await active_button(lang, button_text)

                callback_data = c("filter", ft=filter_type)
                buttons_row.append(InlineBtn(button_text, callback_data=callback_data))

                if len(buttons_row) >= 2:
                    keyboard.row(*buttons_row)
                    buttons_row = list()

            keyboard.row(*buttons_row)

    @classmethod
    async def pagination_callback_handler(
            cls, callback_query: types.CallbackQuery,
            state: FSMContext,
            mode: str, callback_data: dict,
            user: User, lang: str,
    ):
        position = callback_data.get("position")
        await user.update_friendly_channel_lists_params(position=position)

        await state.finish()
        await EditChannel.first()
        await Router.state_menu(callback_query, state, lang)

    @classmethod
    async def search_handler(
            cls, message: types.Message, state: FSMContext, user: User, lang: str
    ):
        await user.update_friendly_channel_lists_params(
            search_text=message.text, position=0
        )

        await state.finish()
        await EditChannel.first()
        await Router.state_menu(message, state, lang)

    @classmethod
    async def get_message_text(cls, channels_mode: str, lang: str) -> str:
        if channels_mode == "super_admin":
            message_text_variable = "super admin chats list"
        else:
            message_text_variable = "my chats list admin"

        return await f(message_text_variable, lang)


class BaseLocationsListDrawer(BaseListDrawer):
    row_width = 1
    need_setup_pagination_handler = True
    need_setup_search_handler = True
    message_text_variable = "choose location header"
    config_page_size_variable_name = "LOCATIONS_LIST_PAGE_SIZE"
    pagination_callback_mode = "locations_list_pagination"

    @classmethod
    async def object_drawer(
            cls, location: str, selected: list[str], keyboard: InlineKb, lang: str
    ):
        button_text = await f(
            "choose location button", lang, location_name=location
        )
        if location in selected:
            button_text = await active_button(lang, button_text)
        keyboard.insert(
            InlineBtn(button_text, callback_data=c("location", name=location))
        )

    @classmethod
    async def get_data_from_state(
            cls, user: User, state: FSMContext, mode: str = "new"
    ) -> Dict[str, Any]:
        state_data = await state.get_data()
        search_text = state_data.get(cls.search_key, "")
        data_from_state = dict(search_text=search_text)

        group = await Group.get(state_data.get("group_id"))
        if group:
            data_from_state.update(selected=[group.location])
        return data_from_state

    @classmethod
    async def make_get_objects_kwargs(
            cls,
            user: User,
            search_text: str,
            data_from_state: Dict[str, Any],
    ) -> Dict[str, Any]:
        return dict(**data_from_state, search_text=search_text)

    @classmethod
    async def get_objects(
            cls,
            get_objects_kwargs: Dict[str, Any],
            position: int = 0, limit: int = None, *,
            operation: str = "all",
    ) -> List[str] | int:
        search_text = get_objects_kwargs.get("search_text")
        return await crud.get_locations(
            search_text, position, limit, operation
        )

    @classmethod
    async def footer_drawer(cls, selected: List[str], keyboard: InlineKb, lang: str):
        keyboard.insert(await previous_button(lang))


class LocationsListDrawer(BaseLocationsListDrawer):

    @classmethod
    async def footer_drawer(cls, selected: List[str], keyboard: InlineKb, lang: str):
        keyboard.insert(await previous_button(lang))


class LocationsFilterListDrawer(BaseLocationsListDrawer):
    row_width = 1
    need_setup_pagination_handler = True
    need_setup_search_handler = True
    message_text_variable = "choose location header"
    config_page_size_variable_name = "LOCATIONS_LIST_PAGE_SIZE"
    pagination_callback_mode = "locations_list_pagination"

    @classmethod
    async def header_drawer(
            cls,
            keyboard: InlineKb, lang: str,
    ):
        button_text = await f("friendly location filter all", lang)
        callback_data = c("location", location=None)
        keyboard.row(InlineBtn(button_text, callback_data=callback_data))

    @classmethod
    async def footer_drawer(cls, selected: List[str], keyboard: InlineKb, lang: str):
        keyboard.insert(await previous_button(lang))


class MainLangsListDrawer(BaseListDrawer):
    message_text_variable = "profile langs list header"
    alert_force_text_variable = "group languages settings use force lang alert text"

    row_width = 2
    config_page_size_variable_name = "LANGS_LIST_PAGE_SIZE"
    need_setup_pagination_handler = True

    @classmethod
    async def object_drawer(
            cls, lang_to_choose: str, langs_list: List[str], keyboard: InlineKb,
            lang: str
    ):
        button_text = await Translator.get_language_name(lang, lang_to_choose)

        if lang_to_choose in langs_list:
            button_text = await active_button(lang, button_text)
        keyboard.insert(
            InlineBtn(
                button_text,
                callback_data=LangCallbackData(selected_lang=lang_to_choose).to_str()
            )
        )

    @classmethod
    async def footer_drawer(
            cls, langs_list: List[str], keyboard: InlineKb, lang: str,
            other: bool = False
    ):
        if other:
            button_text = await f("profile other languages button", lang)
            callback_data = "google_languages"
            keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

        keyboard.row(await previous_button(lang))

    @classmethod
    async def get_objects(
            cls,
            langs_list: list,
            position: int = 0,
            limit: int = None,
            operation: str = "all",
    ) -> List[Any] | int:
        if operation == "count":
            return len(langs_list) - position

        if position:
            langs_list = langs_list[position:]
        if limit:
            langs_list = langs_list[:limit]

        return langs_list

    @classmethod
    async def make_get_objects_kwargs(
            cls,
            user: User,
            search_text: str,
            data_from_state: Dict[str, Any],
    ) -> Dict[str, Any]:
        group = data_from_state.get("group")
        return {"group": group}

    @classmethod
    async def get_message_text(cls, **kwargs: Any) -> str:
        group = kwargs.get("group")
        lang = kwargs.get("lang")
        search_text = kwargs.get("search_text")
        text_var = cls.alert_force_text_variable if group.force_use_lang else (
            cls.message_text_variable)
        return await cls.get_text(
            text_var, lang,
            **{"search_text": search_text, **await cls.get_text_kwargs(**kwargs)}
        )

    @classmethod
    async def get_data_from_state(
            cls, user: User, state: FSMContext, mode: str = "new"
    ) -> Dict[str, Any]:
        state_data = await state.get_data()
        group = await Group.get(state_data.get("group_id"))
        langs_list = [el for el in await Translator.get_supported_languages("en") or []]
        other = state_data.get("other", False)

        return {"group": group, "langs_list": langs_list, "other": other}
