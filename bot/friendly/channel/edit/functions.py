from datetime import datetime
from dateutil.relativedelta import relativedelta
from dateutil.rrule import rrule, WEEKLY, MONTHLY, DAILY

from db.models import Channel, ClientBot

from utils.text import f
from psutils.date_time import localise_datetime
from aiogram import types
from utils.redefined_classes import Bot


async def get_channel_statistics(channel: Channel, lang: str) -> str:

    attach_date = localise_datetime(channel.attach_datetime, channel.group.timezone).date()
    current_date = localise_datetime(datetime.utcnow(), channel.group.timezone).date()

    months_count = rrule(MONTHLY, dtstart=attach_date, until=current_date).count()
    weeks_count = rrule(WEEKLY, dtstart=attach_date, until=current_date).count()
    days_count = rrule(DAILY, dtstart=attach_date, until=current_date).count()

    total_members_joined = await channel.get_saldo(attach_date)
    text = await f("ps total", lang, count=total_members_joined)

    if months_count >= 12:
        count = await channel.get_saldo(current_date-relativedelta(years=1))
        text += "\n" + await f("ps one year", lang, count=count)

    if months_count >= 6:
        count = await channel.get_saldo(current_date-relativedelta(months=6))
        text += "\n" + await f("ps six months", lang, count=count)

    if months_count >= 3:
        count = await channel.get_saldo(current_date-relativedelta(months=3))
        text += "\n" + await f("ps three months", lang, count=count)

    if months_count >= 1:
        count = await channel.get_saldo(current_date-relativedelta(months=1))
        text += "\n" + await f("ps one month", lang, count=count)

    if weeks_count >= 1:
        count = await channel.get_saldo(current_date-relativedelta(weeks=1))
        text += "\n" + await f("ps one week", lang, count=count)

    if days_count >= 1:
        count = await channel.get_saldo(current_date-relativedelta(days=1))
        text += "\n" + await f("ps yesterday", lang, count=count)

    count = await channel.get_saldo(current_date)
    text += "\n" + await f("ps today", lang, count=count)
    return text


async def is_bot_admin_in_chat_subscription(chat_id: int, bot: Bot = None):
    bot = bot if bot else Bot.get_current()

    cur_bot = await ClientBot.get(chat_id)
    if cur_bot:
        return True

    try:
        await bot.send_chat_action(chat_id, "typing")
        return True
    except Exception as e:
        pass

    return False


async def is_bot_admin_in_chat(channel: Channel, lang: str):
    bot = Bot.get_current()
    result = await is_bot_admin_in_chat_subscription(channel.required_resource_subscription_chat_id, bot=bot)

    if not result:
        try:
            chat = await bot.get_chat(channel.required_resource_subscription_chat_id)
        except:
            chat = None

        name = chat.title if chat and chat.title else str(channel.required_resource_subscription_chat_id)
        await channel.add_log(await f("friendly logs warning set admin to resource text", lang, name=name))

    return result


async def get_chat(message: types.Message, text: str):
    try:
        chat = await message.bot.get_chat(text)
        return chat
    except Exception as e:
        return False


async def get_chat_or_bot_info(message: types.Message, channel: Channel):
    cur_bot = await ClientBot.get(channel.required_resource_subscription_chat_id)
    if cur_bot:
        return cur_bot
    try:
        chat_info = await message.bot.get_chat(channel.required_resource_subscription_chat_id)
        return chat_info
    except Exception as e:
        return False
