from aiogram.dispatcher.filters.state import StatesGroup, State


class EditChannel(StatesGroup):
    ChooseChannel = State()
    ChooseLocationFilter = State()
    ChooseField = State()

    Statistics = State()

    SetMessage = State()
    SetMessageRateLimit = State()
    SetLimit = State()
    SetAlertThreshold = State()
    SetMessagesRatio = State()
    SetLimitedMessageOnScreenTime = State()
    SetMessagesLength = State()
    RulesMenu = State()
    SetLimitMessagesBeforeRules = State()
    SetBanTime = State()
    SubscribeMenu = State()
    ShowMessage = State()

    MenuButtonSettings = State()
    ButtonPosition = State()
    EditButtonsMenu = State()
    EditButtonMenu = State()
    EditNewlineButtons = State()
    SetMessageCount = State()  # Установить лимит сообщений для обязательной подписки

    SetLocation = State()
    SetLanguage = State()
    ShowErrors = State()
