from config import FRIENDLY_MESSAGE_LIMIT_BEFORE_RULES_BUTTONS
from db.models import Channel

from utils.keyboards import previous_button, active_button, get_navigation_keyboard

from psutils.convertors import interval_to_str

from utils.country import get_country_code_from_lang, get_country_flag_from_code
from utils.redefined_classes import InlineKb, InlineBtn
from utils.text import c, f, empty_value
from utils.translator import Translator

from .functions import is_bot_admin_in_chat


async def get_edit_channel_keyboard(channel: Channel, lang: str):
    messages_for_friends = channel.messages_for_friends()
    friends_required = channel.friends_required()

    if not all([[friends_required, messages_for_friends]]):
        messages_for_friends, friends_required = await empty_value(lang), await empty_value(lang)

    keyboard = InlineKb(row_width=2)

    # Статистика
    button_text = await f("friendly statistics button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="statistics"))

    # Настройки кнопок меню в чате
    button_text = await f("menu buttons in chats button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="menu_button_settings"))

    # Уведомление вход/выход
    text = await f("channel delete join notification", lang)
    status = channel.get_deleting_notification_status("join")
    if not status:
        text = await active_button(lang, text)
    keyboard.row(InlineBtn(text, callback_data="join"))

    text = await f("channel delete leave notification", lang)
    status = channel.get_deleting_notification_status("leave")
    if not status:
        text = await active_button(lang, text)
    keyboard.insert(InlineBtn(text, callback_data="leave"))

    # Приветствие
    text = await f("friendly welcome button", lang)
    if channel.get_chat_message_status("welcome", "show"):
        text = await active_button(lang, text)
    callback_data = c("set_message", channel_id=channel.id, message_type="welcome")
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # 📝 Правила
    text = await f("friendly rules button", lang)
    # Функционал для новых юзеров отложили
    # for users_type in ("all", "new",):
    #     if getattr(channel, f"is_chat_rules_ban_{users_type}"):
    #         text = "🆗" + text
    #         break
    if getattr(channel, f"is_chat_rules_ban_all"):
        text = "🆗" + text

    if channel.get_chat_message_status("rules", "show"):
        text = await active_button(lang, text)
    callback_data = c("set_message", channel_id=channel.id, message_type="rules")
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # Лимит Сообщений
    messages_given = channel.messages_given
    if messages_given == 0:
        messages_given = await f("friendly unlimited messages", lang)
        is_active = False
    else:
        is_active = True
    text = await f("message limit button", lang, current_limit=messages_given)
    if is_active:
        text = await active_button(lang, text)
    keyboard.insert(InlineBtn(text, callback_data="set_limit"))

    # 💬Сообщения/👥Друзья
    ratio = await f("ratio text", lang, messages_for_friends=messages_for_friends, friends_required=friends_required)
    button_text = await f("messages to friends ratio", lang, ratio=ratio)
    keyboard.insert(InlineBtn(button_text, callback_data="set_messages_ratio"))

    if channel.messages_given:
        # Предупреждать за (2)
        alert_threshold = channel.alert_threshold
        if alert_threshold == 0:
            alert_threshold = await f("always text", lang)
        button_text = await f("alert message threshold options", lang, count=alert_threshold)
        keyboard.insert(InlineBtn(button_text, callback_data="set_alert_threshold"))

        # 📝 Предупреждение
        text = await f("friendly alert button", lang)
        if channel.get_chat_message_status("alert", "show"):
            text = await active_button(lang, text)
        callback_data = c("set_message", channel_id=channel.id, message_type="alert")
        keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # 📝 Ограничение
    text = await f("friendly restriction button", lang)
    if channel.get_chat_message_status("restriction", "show"):
        text = await active_button(lang, text)
    callback_data = c("set_message", channel_id=channel.id, message_type="restriction")
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    if channel.messages_given:
        # 📝 Инструкции
        text = await f("friendly info button", lang)
        if channel.get_chat_message_status("info", "show"):
            text = await active_button(lang, text)
        callback_data = c("set_message", channel_id=channel.id, message_type="info")
        keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # 📝 Удаление
    text = await f("friendly deletion button", lang)
    if channel.get_chat_message_status("deletion", "show"):
        text = await active_button(lang, text)
    callback_data = c("set_message", channel_id=channel.id, message_type="deletion")
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # ⏱ Удалять
    text = await f("limited message on screen time button", lang, delay=channel.limited_message_on_screen_time)
    callback_data = "set_limited_message_on_screen_time"
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # ⛔ Текст
    is_filter = channel.get_filter("message")
    text = await f("friendly setlimit text button", lang)
    if is_filter:
        text = await active_button(lang, text)
    callback_data = c("set_username_filters", filter_type="message")
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # ⛔ Имена
    is_filter = channel.get_filter("username")
    text = await f("friendly setlimit name button", lang)
    if is_filter:
        text = await active_button(lang, text)
    callback_data = c("set_username_filters", filter_type="username")
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # ⛔ Длина сообщ
    message_length = channel.max_messages_length if channel.max_messages_length else await f("unlim limit text", lang)
    text = await f("friendly setlimit length text button", lang, count=message_length)
    if channel.max_messages_length:
        text = await active_button(lang, text)
    callback_data = "set_message_length"
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # Подписка на ресурс
    text = await f("friendly subscribe edit channel button", lang)
    if channel.required_resource_subscription_chat_id and await is_bot_admin_in_chat(channel, lang):
        text = await active_button(lang, text)
    callback_data = "subscribe_menu"
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # футеры
    button_text = await f("select footer button", lang)
    callback_data = c("edit_footer", channel_id=channel.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    # настройка локации
    text = await f("friendly channel location button", lang, location_name=channel.group.location)
    callback_data = "change_location"
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # настройка языка группы
    current_lang = await Translator.get_language_name(lang, channel.lang)
    country_code = get_country_code_from_lang(channel.lang)
    flag = get_country_flag_from_code(country_code) or await empty_value(lang)
    text = await f("friendly channel language button", lang, current_lang=current_lang, flag=flag)
    callback_data = "change_language"
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # логи ошибок
    text = await channel.get_logs_text(lang, "count")
    callback_data = "show_errors"
    keyboard.insert(InlineBtn(text, callback_data=callback_data))

    # Назад
    keyboard.row(await previous_button(lang, mode=None))
    return keyboard


async def get_set_message_keyboard(channel: Channel, message_type: str, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=2)

    button_text = await f("show text", lang)
    if channel.get_chat_message_status(message_type, "show"):
        button_text = await active_button(lang, button_text)
    callback_data = c("msg", m=message_type, s="s")
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("channel message default checkbox", lang)
    if channel.get_chat_message_status(message_type, "default"):
        button_text = await active_button(lang, button_text)
    callback_data = c("msg", m=message_type, s="d")
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if message_type == "rules":
        button_text = await f("necessarily text", lang)
        callback_data = "necessarily_menu_button"
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if message_type in ["welcome", "alert", "deletion", "restriction", "required_subscription"]:
        value = getattr(channel, f"{message_type}_message_rate_limit")
        value = interval_to_str(value, lang) if value else await empty_value(lang)
        button_text = await f("edit rate limit button", lang, value=value)
        keyboard.row(InlineBtn(button_text, callback_data="rate_limit"))

    button_text = await f("show message button", lang)
    callback_data = c("msg_show", m=message_type)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    if message_type == "welcome":
        button_text = await f("friendly default system keyboard button", lang)
        if channel.need_welcome_system_keyboard:
            button_text = await active_button(lang, button_text)
        callback_data = "system_keyboard_menu_button"
        keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f("footer message button", lang)
    callback_data = c("select_footer", m=message_type, channel_id=channel.id)
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    keyboard.row(await previous_button(lang))

    return keyboard


async def get_subscribe_menu_keyboard(channel: Channel, is_admin: bool, lang: str):
    keyboard = InlineKb(row_width=2)

    if is_admin:
        button_text = await f("required subscription update button", lang)
        keyboard.row(InlineBtn(button_text, callback_data="update_info"))

    if not is_admin and channel.required_resource_subscription_chat_id:
        button_text = await f("required subscription edit message button", lang)
        callback_data = c("set_message", message_type="required_subscription")
        keyboard.row(InlineBtn(button_text, callback_data=callback_data))

        button_text = await f(
            "channel count messages before required resource button",
            count=channel.max_count_messages_before_required_resource,
            lang=lang
        )
        callback_data = c("set_message_count")
        keyboard.row(InlineBtn(button_text, callback_data=callback_data))

    if channel.required_resource_subscription_chat_id:
        button_text = await f("required subscription disable button", lang)
        keyboard.row(InlineBtn(button_text, callback_data="disable_required_subscription"))

    keyboard.row(await previous_button(lang))
    return keyboard


async def get_send_rules_keyboard(channel: Channel, lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=2)

    button_text = await f(
        f"show rules after x messages button", lang, count=channel.allowed_messages_given_without_rules
    )
    callback_data = "change_count_massages_before_rules"
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    button_text = await f(f"necessarily button", lang)

    if getattr(channel, f"is_chat_rules_ban_all"):
        button_text = await f("active button", lang, handle=button_text)

    callback_data = c("change_need_agree_rules_status", users_type="all")
    keyboard.insert(InlineBtn(button_text, callback_data=callback_data))

    keyboard.insert(await previous_button(lang))

    return keyboard


async def get_message_limit_before_rules_keyboard(lang: str) -> InlineKb:
    default_values = FRIENDLY_MESSAGE_LIMIT_BEFORE_RULES_BUTTONS

    keyboard = InlineKb(row_width=3)

    for default_value in default_values:
        button_text = await f("message to users", lang, message_text=default_value)
        keyboard.insert(InlineBtn(button_text, callback_data=c("rules_message_limit", count=default_value)))

    keyboard.insert(await previous_button(lang))

    return keyboard


async def get_logs_keyboard(lang: str) -> InlineKb:
    keyboard = InlineKb(row_width=2)

    button_text = await f("friendly logs clear button", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="clear_errors"))

    return await get_navigation_keyboard(lang, keyboard)


async def get_refresh_messages_keyboard(channel: Channel, with_unlim_button: bool, lang: str):
    keyboard = InlineKb(row_width=2)
    keyboard.insert(
        InlineBtn(await f("refresh button", lang), callback_data=c("refresh_messages", channel_id=channel.id))
    )

    refresh_status = "active" if channel.refresh_messages_daily else "inactive"
    button_text = await f("refresh daily button", lang)
    button_text = await f(f"{refresh_status} button", lang, handle=button_text)
    keyboard.insert(InlineBtn(button_text, callback_data=c("refresh_messages_daily", channel_id=channel.id)))

    if with_unlim_button:
        button_text = await f("unlim limit button", lang)
        keyboard.insert(InlineBtn(button_text, callback_data=c("unlim_limit_button", channel_id=channel.id)))

    keyboard.add(await previous_button(lang))
    return keyboard
