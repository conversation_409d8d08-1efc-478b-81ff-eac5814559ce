import html
from contextlib import suppress

from aiogram import types
from aiogram.dispatcher import FSMContext
from aiogram.utils.exceptions import MessageError
from psutils.forms.helpers import save_messages_to_state

from db.models import Channel
from friendly.footer import send_message_and_footer
from utils.keyboards import active_button, get_previous_keyboard, previous_button
from utils.message import send_tg_message
from utils.redefined_classes import InlineBtn, InlineKb
from utils.router import Router
from utils.text import c, empty_value, enter_text_with_specified_value, f
from .functions import (
    get_channel_statistics, get_chat_or_bot_info, is_bot_admin_in_chat,
)
from .keyboards import (
    get_edit_channel_keyboard, get_logs_keyboard,
    get_message_limit_before_rules_keyboard, get_refresh_messages_keyboard,
    get_send_rules_keyboard, get_set_message_keyboard, get_subscribe_menu_keyboard,
)
from .list_drawers import (
    ChannelsListDrawer, LocationsFilterListDrawer,
    LocationsListDrawer, MainLangsListDrawer,
)
from .states import <PERSON>Channel


async def send_edit_channel_menu(
        message: types.Message, state: FSMContext, lang: str, mode: str
):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    text = await f("edit channel header", lang, channel_name=channel.name)
    keyboard = await get_edit_channel_keyboard(channel, lang)

    if mode == "edit":
        await message.edit_text(text=text, reply_markup=keyboard)
    else:
        await message.answer(text, reply_markup=keyboard)


async def send_statistics(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    keyboard = InlineKb(row_width=1)
    keyboard.insert(await previous_button(lang))

    text = await get_channel_statistics(channel, lang)
    await message.edit_text(text, reply_markup=keyboard)


async def set_message_menu(
        message: types.Message, state: FSMContext, lang: str, mode: str = "new"
):
    state_data = await state.get_data()
    message_type = state_data.get("message_type")
    channel = await Channel.get(state_data.get("channel_id"))
    channel_message = await channel.get_message(message_type)

    text = html.escape(channel_message.get("text", ""))
    header_text = await f(f"{message_type} message example header", lang)
    text = await f(
        "friendly set message example", lang, header_text=header_text, text=text
    )
    text = await f(
        "current channel example header", lang, header_text=text,
        channel_name=channel.name
    )
    message_text = await f(f"set {message_type} message text", lang)
    keyboard = await get_set_message_keyboard(channel, message_type, lang)

    if mode == "edit_drawed":
        await message.edit_text(message_text, reply_markup=keyboard)
        return message

    else:
        channel_message["text"] = text
        footer = await channel.get_footer(message_type)
        results = await send_message_and_footer(
            message.chat.id, channel_message, None, footer, True,
            need_replace_html_symbols=False
        )
        if results:
            msg = results[0]
            await save_messages_to_state(state, msg.message_id)
            msg_footer = results[1] if len(results) > 1 else None
            if msg_footer:
                await save_messages_to_state(state, msg_footer.message_id)
        msg = await message.answer(message_text, reply_markup=keyboard)
        await message.delete()
        return msg


async def set_message_rate_limit_menu(
        message: types.Message, state: FSMContext, lang: str
):
    state_data = await state.get_data()

    channel = await Channel.get(state_data.get("channel_id"))
    message_type = state_data.get("message_type")

    text = await f(f"enter {message_type} message rate limit header", lang)
    value = getattr(channel, f"{message_type}_message_rate_limit")
    if value:
        text = await enter_text_with_specified_value(text, value, lang)

    keyboard = await get_previous_keyboard(lang)
    return await message.edit_text(text, reply_markup=keyboard)


async def set_limit_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    if channel.messages_given:
        keyboard = await get_refresh_messages_keyboard(
            channel=channel, with_unlim_button=True, lang=lang
        )
    else:
        keyboard = await get_previous_keyboard(lang)

    messages_given = channel.messages_given if channel.messages_given else await f(
        "unlim limit text", lang
    )

    message_text = await f("current message limit", lang, message_limit=messages_given)

    message_id = state_data["__router_message__"]["message_id"]
    await message.bot.edit_message_text(
        text=message_text,
        reply_markup=keyboard,
        message_id=message_id,
        chat_id=message.chat.id
    )


async def set_alert_threshold_menu(
        message: types.Message, state: FSMContext, lang: str
):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    keyboard = InlineKb(row_width=1)
    alert_threshold = channel.alert_threshold
    button_text = await f("always alert button", lang)
    if alert_threshold == 0:
        button_text = await active_button(lang, button_text)
        alert_threshold = await f("always text", lang)
    keyboard.insert(InlineBtn(button_text, callback_data="always_alert"))
    keyboard.insert(await previous_button(lang))
    message_text = await f("current message threshold", lang, threshold=alert_threshold)

    message_id = state_data["__router_message__"]["message_id"]
    await message.bot.edit_message_text(
        text=message_text,
        reply_markup=keyboard,
        message_id=message_id,
        chat_id=message.chat.id
    )


async def set_messages_ratio_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    keyboard = InlineKb(row_width=2)
    keyboard.insert(await previous_button(lang))

    messages_for_friends = channel.messages_for_friends()
    friends_required = channel.friends_required()
    if None in [friends_required, messages_for_friends]:
        messages_for_friends, friends_required = await empty_value(
            lang
        ), await empty_value(lang)

    ratio = await f(
        "ratio text", lang, messages_for_friends=messages_for_friends,
        friends_required=friends_required
    )

    message_text = await f("current message ratio", lang, ratio=ratio)
    message_id = state_data["__router_message__"]["message_id"]
    await message.bot.edit_message_text(
        text=message_text,
        reply_markup=keyboard,
        message_id=message_id,
        chat_id=message.chat.id
    )


async def set_limited_message_on_screen_time_menu(
        message: types.Message, state: FSMContext, lang: str
):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    keyboard = InlineKb(row_width=2)
    keyboard.insert(await previous_button(lang))

    delay = channel.limited_message_on_screen_time
    message_text = await f("limited message on screen time message", lang, delay=delay)

    message_id = state_data["__router_message__"]["message_id"]
    await message.bot.edit_message_text(
        text=message_text,
        reply_markup=keyboard,
        message_id=message_id,
        chat_id=message.chat.id
    )


async def set_message_length_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))
    keyboard = InlineKb(row_width=2)
    button_text = await f("set unlim message length button", lang)
    message_length = channel.max_messages_length
    if message_length:
        keyboard.insert(
            InlineBtn(
                button_text,
                callback_data=c(
                    "set_unlim_message_length_button",
                    channel_id=channel.id
                )
            )
        )
    keyboard.insert(await previous_button(lang))
    message_length = message_length if message_length else await f("unlim limit text")
    message_text = await f(
        "set message length limit", lang, message_length=message_length
    )

    message_id = state_data["__router_message__"]["message_id"]
    await message.bot.edit_message_text(
        text=message_text,
        reply_markup=keyboard,
        message_id=message_id,
        chat_id=message.chat.id
    )


async def send_rules_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    keyboard = await get_send_rules_keyboard(channel, lang)

    message_text = await f("rules menu text", lang)
    await message.edit_text(message_text, reply_markup=keyboard)


async def set_message_limit_before_rules_menu(
        message: types.Message, state: FSMContext, lang: str
):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    keyboard = await get_message_limit_before_rules_keyboard(lang)

    message_text = await f(
        "set message limit before rules text", lang,
        count=channel.allowed_messages_given_without_rules
    )
    with suppress(MessageError):
        await message.edit_text(message_text, reply_markup=keyboard)


async def set_ban_time_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    keyboard = await get_previous_keyboard(lang)

    message_text = await f(
        "set ban time rules text", lang, ban_time=channel.limited_agree_rules_time
    )
    return await message.edit_text(message_text, reply_markup=keyboard)


async def subscribe_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    keyboard = await get_subscribe_menu_keyboard(channel, False, lang)

    if state_data.get("bot_kicked"):
        message_text = await f("friendly subscribe bot kicked in chat text", lang=lang)
        await state.update_data(bot_kicked=False)
        return await message.edit_text(message_text, reply_markup=keyboard)

    if channel.required_resource_subscription_chat_id == 0:
        message_text = await f("friendly subscribe without channel text", lang)
        return await message.edit_text(message_text, reply_markup=keyboard)

    chat_or_bot_info = await get_chat_or_bot_info(message, channel)

    is_admin = await is_bot_admin_in_chat(channel, lang)
    if is_admin:
        message_text = await f(
            "friendly subscribe done text", username=chat_or_bot_info.username,
            lang=lang
        )
    else:
        keyboard = await get_subscribe_menu_keyboard(channel, True, lang)
        if isinstance(chat_or_bot_info, bool):
            message_text = await f(
                "friendly subscribe bot is not correct channel text", lang
            )
        else:
            message_text = await f(
                "friendly subscribe bot is not admin text",
                username=chat_or_bot_info.username, lang=lang
            )

    return await message.edit_text(message_text, reply_markup=keyboard)


async def set_message_count_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    keyboard = await get_refresh_messages_keyboard(
        channel=channel, with_unlim_button=False, lang=lang
    )
    count = channel.max_count_messages_before_required_resource

    message_text = await f(
        "friendly number of posts before showing mandatory subscription", lang,
        count=count
    )

    return await message.edit_text(message_text, reply_markup=keyboard)


async def show_message_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    message_type = state_data.get("message_type")
    channel = await Channel.get(state_data.get("channel_id"))
    channel_message = await channel.get_message(message_type)

    text = channel_message.get("text", "")
    channel_message["text"] = text
    msg = await send_tg_message(
        message.chat.id, need_replace_html_symbols=False, **channel_message
    )
    await save_messages_to_state(state, msg.message_id)

    text = await f("show message text", lang)
    keyboard = await get_previous_keyboard(lang)

    msg = await message.answer(text, reply_markup=keyboard)
    await message.delete()
    return msg


async def set_show_errors_menu(message: types.Message, state: FSMContext, lang: str):
    state_data = await state.get_data()
    channel = await Channel.get(state_data.get("channel_id"))

    keyboard = await get_logs_keyboard(lang)
    message_text = await channel.get_logs_text(lang, "data")

    return await message.edit_text(message_text, reply_markup=keyboard)


def register_edit_channel_routes(router: Router):
    router.add_route(EditChannel.ChooseChannel, ChannelsListDrawer())
    router.add_route(EditChannel.ChooseLocationFilter, LocationsFilterListDrawer())
    router.add_route(EditChannel.SetLocation, LocationsListDrawer())
    router.add_route(EditChannel.SetLanguage, MainLangsListDrawer())
    router.add_route(EditChannel.ChooseField, send_edit_channel_menu)

    router.add_route(EditChannel.Statistics, send_statistics)

    router.add_route(EditChannel.SetMessage, set_message_menu)
    router.add_route(EditChannel.SetMessageRateLimit, set_message_rate_limit_menu)
    router.add_route(EditChannel.SetLimit, set_limit_menu)
    router.add_route(EditChannel.SetAlertThreshold, set_alert_threshold_menu)
    router.add_route(EditChannel.SetMessagesRatio, set_messages_ratio_menu)
    router.add_route(
        EditChannel.SetLimitedMessageOnScreenTime,
        set_limited_message_on_screen_time_menu
    )
    router.add_route(EditChannel.SetMessagesLength, set_message_length_menu)
    router.add_route(EditChannel.RulesMenu, send_rules_menu)
    router.add_route(
        EditChannel.SetLimitMessagesBeforeRules, set_message_limit_before_rules_menu
    )
    router.add_route(EditChannel.SetBanTime, set_ban_time_menu)
    router.add_route(EditChannel.SubscribeMenu, subscribe_menu)
    router.add_route(EditChannel.ShowMessage, show_message_menu)
    router.add_route(EditChannel.SetMessageCount, set_message_count_menu)
    router.add_route(EditChannel.ShowErrors, set_show_errors_menu)
