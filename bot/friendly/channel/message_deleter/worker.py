import asyncio
from collections import defaultdict

from config import (
    CHECK_OLD_FRIENDLY_MESSAGES_DELAY, DEBUG,
    ONE_DELETING_FRIENDLY_OLD_MESSAGES_ITERATION,
)
from db import own_session
from loggers import <PERSON><PERSON><PERSON>og<PERSON>
from utils.list import split_list
from utils.redefined_classes import Bot
from .db_funcs import delete_old_messages_by_ids, get_old_messages


async def _delete_old_friendly_message(
        bot: Bot,
        chat_id: int,
        message_id: int,
        message_type: str,
        channel_id: int,
        channel_name: str,
):
    logger = JSONLogger(
        "friendly.delete-old-messages", {
            "channel": {
                "id": channel_id,
                "name": channel_name,
                "chat_id": chat_id,
            },
            "message_id": message_id,
            "message_type": message_type,
        }
    )

    result = None
    try:
        if DEBUG:
            logger.debug(f"Deleting old friendly message in chat {channel_name}")
        await bot.delete_message(chat_id, message_id)
        result = "SUCCESS"
    except Exception as error:
        result = f"ERROR:{repr(error)}"
    finally:
        if DEBUG:
            logger.debug(f"Delete old friendly message in chat {channel_name}", result)


async def _delete_old_friendly_messages_in_bot(
        bot: Bot, bot_token: str,
        messages: list[dict],
):
    with bot.with_token(bot_token):
        coros = [
            _delete_old_friendly_message(bot, **message_data)
            for message_data in messages
        ]
        part_size = ONE_DELETING_FRIENDLY_OLD_MESSAGES_ITERATION
        parts = split_list(coros, part_size)
        for part in parts:
            await asyncio.gather(*part, return_exceptions=True)
            await asyncio.sleep(1)


@own_session
async def delete_old_friendly_messages():
    logger = JSONLogger("friendly.delete-old-messages")

    bot = Bot.get_current()
    loop = asyncio.get_running_loop()
    try:
        messages_to_delete = await get_old_messages()
    except Exception as e:
        logger.error(
            f"An error occurred while getting old messages: {repr(e)}",
        )
        messages_to_delete = None

    if not messages_to_delete:
        timeout = CHECK_OLD_FRIENDLY_MESSAGES_DELAY
        return loop.call_later(
            timeout, asyncio.create_task, delete_old_friendly_messages()
        )

    try:
        # группировка сообщений по ботам
        groups: dict[str, list[dict]] = defaultdict(list)
        for (message_id, tg_message_id, chat_id, message_type, channel_id,
             channel_name, bot_token) in messages_to_delete:
            groups[bot_token].append(
                {
                    "chat_id": chat_id,
                    "message_id": tg_message_id,
                    "message_type": message_type,
                    "channel_id": channel_id,
                    "channel_name": channel_name,
                }
            )
        coros = [
            _delete_old_friendly_messages_in_bot(bot, bot_token, messages)
            for bot_token, messages in groups.items()
        ]
        await asyncio.gather(*coros, return_exceptions=True)
    except Exception as e:
        logger.error(
            f"An error occurred while deleting old friendly messages: {repr(e)}",
        )
    finally:
        try:
            message_ids = [message_data[0] for message_data in messages_to_delete]
            await delete_old_messages_by_ids(message_ids)
        except Exception as e:
            logger.error(
                f"An error occurred while deleting old friendly messages from "
                f"database: {repr(e)}",
            )

    loop.call_soon(asyncio.create_task, delete_old_friendly_messages())
