from typing import Sequence

from psutils.exceptions import ErrorWithTextVariable
from starlette import status

from utils.exceptions import ErrorWithHTTPStatus
from ..base import ObjectNotFoundError


class BillingPromoCodeNotFoundError(ObjectNotFoundError):
    pass


class BillingGenerateUniquePromoCodeError(ErrorWithTextVariable):
    text_variable = "generate unique promo code error text"


class BillingPromoCodeInvalidCurrencyError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST

    def __init__(
            self, packet_currency: str,
            coupon_currencies: Sequence[str],
    ):
        self.packet_currency = packet_currency
        self.coupon_currencies = list(coupon_currencies)

        super().__init__(
            packet_currency=self.packet_currency,
            coupon_currencies=self.coupon_currencies,
            detail_data={
                "packet_currency": self.packet_currency,
                "coupon_currencies": self.coupon_currencies,
            }
        )


class BillingPromoCodeInvalidPacketError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN

    def __init__(self, packet_id: int, promo_code_packet_id: int):
        self.packet_id = packet_id
        self.promo_code_packet_id = packet_id

        super().__init__(
            packet_id=packet_id,
            promo_code_packet_id=promo_code_packet_id,
            detail_data={
                "packet_id": packet_id,
                "promo_code_packet_id": promo_code_packet_id,
            }
        )
