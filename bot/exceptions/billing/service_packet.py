from starlette import status

from utils.exceptions import ErrorWithHTTPStatus
from ..base import IdNotUniqueError, ObjectListNotFoundError, ObjectNotFoundError


class BillingServicePacketNotFoundError(ObjectNotFoundError):
    pass


class BillingServicePacketItemsCannotBeUpdatedError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "billing service packet items cannot be updated error"


class BillingServicePacketsIdNotUniqueError(IdNotUniqueError):
    pass


class BillingServicePacketsNotFoundError(ObjectListNotFoundError):
    pass
