from starlette import status

from utils.exceptions import ErrorWithHTTPStatus
from ..base import ObjectNotFoundError


class BillingSubscriptionNotFoundError(ObjectNotFoundError):
    pass


class BillingSubscriptionCannotBeResumedError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST


class BillingServiceSuspendedError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN


class BillingProfileNotEligibleForTrialPlanError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN


class BillingPaymentMethodRequiredError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
