from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BillingRecordUsageError(ErrorWithHTTPStatus, base=True):
    def __init__(
            self, product_name: str,
            quantity: int,
            detail_data: dict | None = None,
            **kwargs
    ):
        self.product_name = product_name

        if detail_data is None:
            detail_data = {}

        detail_data.update(
            {
                "product_name": product_name,
                "quantity": quantity,
            }
        )

        super().__init__(
            product_name=product_name,
            quantity=quantity,
            detail_data=detail_data,
            **kwargs,
        )


class BillingProductNotAvailableError(BillingRecordUsageError):
    status_code = status.HTTP_400_BAD_REQUEST
    error_code = "product_not_available_error"


class BillingQuotaExceededError(BillingRecordUsageError):
    status_code = status.HTTP_400_BAD_REQUEST
    error_code = "quote_exceeded_error"


class BillingRecordTransactionCurrencyMismatchError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    error_code = "invalid_currency"

    def __init__(self, currency: str):
        self.currency = currency
        super().__init__(
            currency=currency.upper(),
            detail_data={
                "currency": currency,
            }
        )
