from typing import Sequence

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BillingTaxIdsVerificationRequiredError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST

    def __init__(self, tax_ids: Sequence[str]):
        self.tax_ids = list(tax_ids)
        super().__init__(
            tax_ids=", ".join(tax_ids),
            detail_data={"tax_ids": list(tax_ids)}
        )


class BillingTaxIdsInvalidError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST

    def __init__(self, tax_ids: Sequence[str]):
        self.tax_ids = list(tax_ids)
        super().__init__(
            tax_ids=",".join(tax_ids),
            detail_data={"tax_ids": list(tax_ids)}
        )


class BillingSupportingDocumentsRequiredError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
