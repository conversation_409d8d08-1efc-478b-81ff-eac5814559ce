from typing import Any, Iterable

from psutils.text import paschal_case_to_snake_case
from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class ReplaceClassNameMixin:
    replace_with = ""
    replace: str | tuple[str, ...]
    _repl: list[str]

    @property
    def name(self):
        res = self.__class__.__name__
        for to_replace in sorted(getattr(self, "_repl", ()), key=lambda x: len(x)):
            res = res.replace(to_replace, self.replace_with)
        return res

    @property
    def paschal_name(self):
        return paschal_case_to_snake_case(self.name)

    def __init_subclass__(cls, **kwargs):
        if not hasattr(cls, "_repl") or kwargs.pop("overwrite_repl", False):
            cls._repl = []

        if hasattr(cls, "replace"):
            if isinstance(cls.replace, str):
                cls._repl.append(cls.replace)
            elif isinstance(cls.replace, tuple):
                cls._repl.extend(cls.replace)
            else:
                raise TypeError(
                    f"replace must be str or tuple, not {type(cls.replace)}"
                )

        super().__init_subclass__(**kwargs)


class ObjectNotFoundError(ErrorWithHTTPStatus, ReplaceClassNameMixin):
    replace = ("NotFoundError", "NotFoundByIdError")
    status_code = status.HTTP_404_NOT_FOUND
    param_name = "id"

    def __init__(self, param: Any):
        text_kwargs_param = ", ".join(map(str, param)) if isinstance(
            param, Iterable
        ) and not isinstance(param, str) else param

        super().__init__(
            **{
                "param": param,
                self.param_name: text_kwargs_param,
                f"{self.paschal_name}_{self.param_name}": text_kwargs_param,
            },
            detail_data={
                "error_code": f"{self.paschal_name}_not_found",
                f"{self.paschal_name}_{self.param_name}": param,
            }
        )


class ObjectListNotFoundError(ErrorWithHTTPStatus, ReplaceClassNameMixin):
    replace = ("NotFoundError", "NotFoundByIdError")
    status_code = status.HTTP_400_BAD_REQUEST

    def __init__(self, ids: Iterable[int]):
        ids_str = ", ".join(map(str, ids))

        super().__init__(
            ids=ids_str,
            **{
                f"{self.paschal_name}_ids": ids_str,
            },
            detail_data={
                "error_code": f"{self.paschal_name}_not_found",
                f"{self.paschal_name}_ids": list(ids),
            }
        )


class IdNotUniqueError(ErrorWithHTTPStatus, ReplaceClassNameMixin):
    replace = "IdUniqueError"
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY

    def __init__(self, duplicated_ids: Iterable[int]):
        super().__init__(
            detail_data={
                "error_code": f"{self.paschal_name}_ids_not_unique_error",
                "duplicated_ids": list(duplicated_ids)
            },
            duplicated_ids=", ".join(map(str, duplicated_ids)),
        )
