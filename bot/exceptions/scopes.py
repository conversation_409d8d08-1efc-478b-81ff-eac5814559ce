from typing import Sequence

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class NotAllowedScopeTargetError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY

    def __init__(self, target: str, allowed_targets: Sequence[str]):
        self.target = target
        self.allowed_targets = allowed_targets
        super().__init__(
            detail_data={
                "error_code": "not_allowed_scope_target",
                "target": target,
                "allowed_targets": allowed_targets,
            }
        )
