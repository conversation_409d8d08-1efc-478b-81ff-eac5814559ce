from starlette import status

from utils.exceptions import ErrorWithHTTPStatus
from ..base import ObjectNotFoundError


class EwalletExternalPaymentNotFoundError(ObjectNotFoundError):
    pass


class EwalletExternalPaymentChangeStatusForbiddenError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN

    def __init__(self, new_status: str):
        self.new_status = new_status

        super().__init__(
            detail_data={
                "new_status": new_status,
            },
            new_status=new_status,
        )


class CreatingEwalletExternalPaymentError(ErrorWithHTTPStatus):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
