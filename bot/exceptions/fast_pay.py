from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class FastPayInvalidStateDataError(ErrorWithHTTPStatus):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR


class FastPayInvalidStateError(ErrorWithHTTPStatus):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

    def __init__(self, state: str):
        self.state = state
        super().__init__(state=state)


class FastPayAmountRequiredError(ErrorWithHTTPStatus):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR


class FastPayPaymentMethodNotSupportedError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST

    def __init__(self, payment_method: str):
        self.payment_method = payment_method

        super().__init__(
            payment_method=payment_method,
            detail_data={
                "payment_method": payment_method
            }
        )


class FastPayNoPaymentMethodsAvailableError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST


class FastPayNotButtonClickError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
