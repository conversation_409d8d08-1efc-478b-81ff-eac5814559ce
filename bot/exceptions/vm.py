from typing import Iterable

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus
from .base import ObjectNotFoundError


class VirtualManagerNotFoundError(ObjectNotFoundError):
    pass


class VirtualManagerStepNotFoundError(ObjectNotFoundError):
    pass


class VirtualManagerButtonNotFoundError(ObjectNotFoundError):
    pass


class AdminVMStepIdsNotUniqueError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin vm step ids not unique error"

    def __init__(self, duplicated_ids: Iterable[int]):
        super().__init__(
            duplicated_ids=", ".join(map(str, duplicated_ids)),
            detail_data={
                "error_code": "vm_step_ids_not_unique",
                "duplicated_ids": list(duplicated_ids),
            }
        )


class AdminVMStepsNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin vm steps not found error"

    def __init__(self, not_found_ids: Iterable[int]):
        super().__init__(
            not_found_ids=", ".join(map(str, not_found_ids)),
            detail_data={
                "error_code": "vm_steps_not_found",
                "not_found_ids": list(not_found_ids)
            }
        )


class AdminVMStepsInteractivesNoFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin vm steps interactives not found error"

    def __init__(self, not_found_ids: Iterable[int]):
        super().__init__(
            not_found_ids=", ".join(map(str, not_found_ids)),
            detail_data={
                "error_code": "vm_steps_interactives_not_found",
                "not_found_ids": list(not_found_ids)
            }
        )
