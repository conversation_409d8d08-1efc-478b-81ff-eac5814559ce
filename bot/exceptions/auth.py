from abc import ABC
from typing import Any, Iterable

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus
from utils.scopes_map import scope_map


class BaseAuthError(ErrorWithHTTPStatus, ABC, base=True):
    groups = ["auth"]


class AuthSendingEmailError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth sending email error"

    groups = ["auth", "register", "send_confirm_email"]

    def __init__(self, email: str):
        self.email = email

        super().__init__(email=email)


class AuthConfirmEmailInvalidTokenError(BaseAuthError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "auth confirm email invalid token error"

    groups = ["auth", "confirm_email"]


class AuthInvalidInputDataError(BaseAuthError):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "auth invalid input data error"

    groups = ["auth", "login", "register"]


class AuthRegisterAgreementNotAcceptedError(BaseAuthError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "auth register agreement not accepted error"

    groups = ["auth", "register"]


class AuthEmailExistsError(BaseAuthError):
    status_code = status.HTTP_409_CONFLICT
    text_variable = "auth email exists error"

    groups = ["auth", "register", "change_email"]

    def __init__(self, email: str):
        self.email = email
        super().__init__(email=email)


class AuthPasswordTooShortError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth password too short error"

    groups = ["auth", "register", "change_password"]

    def __init__(self, min_length: int):
        self.min_length = min_length
        super().__init__(
            min_length=min_length,
        )


class AuthPasswordNotAsciiError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth password not ascii error"

    groups = ["auth", "register", "change_password"]


class AuthIncorrectTelegramDataError(BaseAuthError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "auth incorrect telegram data error"

    groups = ["auth", "register", "change_telegram"]


class AuthTelegramExistsError(BaseAuthError):
    status_code = status.HTTP_409_CONFLICT
    text_variable = "auth telegram exists error"

    groups = ["auth", "register", "change_telegram"]


class AuthTelegramNoBotError(BaseAuthError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "auth telegram no bot error"

    groups = ["auth", "register", "change_telegram"]


class AuthUnknownMethodError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth unknown method error"

    groups = [
        "login",
        "register",
    ]

    def __init__(self, register_method: str):
        self.register_method: register_method
        super().__init__(register_method=register_method)


class AuthIncorrectPasswordError(BaseAuthError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "auth incorrect password error"

    groups = [
        "auth",
        "login",
        "change_telegram",
        "change_email",
        "change_password"
    ]


class AuthAccountNotFoundError(BaseAuthError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "auth account not found error"

    groups = [
        "auth",
        "user_token_error",
    ]


class AuthNotAuthorisedError(BaseAuthError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "auth not authorised error"

    groups = [
        "auth",
        "token_error",
    ]


class AuthTokenExpiredError(BaseAuthError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "auth token expired error"

    groups = [
        "auth",
        "token_error",
    ]


class AuthNotEnoughPermissionsError(BaseAuthError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "auth not enough permissions error"

    groups = [
        "auth",
        "token_scopes_error",
    ]


class AuthUserIsDeactivatedError(BaseAuthError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "auth user is deactivated error"

    groups = [
        "auth",
        "user_token_error",
    ]


class AuthEmailNotConfirmedError(BaseAuthError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "auth email not confirmed error"

    groups = [
        "auth",
        "change_email",
    ]


class AuthBaseExternalLoginError(BaseAuthError, ABC, base=True):
    groups = [
        "auth",
        "external_login"
    ]


class AuthExternalLoginUnknownError(AuthBaseExternalLoginError):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    text_variable = "auth external login unknown error"


class AuthExternalLoginNotFoundError(AuthBaseExternalLoginError):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "auth external login not found error"

    def __init__(self, uuid: str):
        self.uuid = uuid
        super().__init__(uuid=uuid)


class AuthExternalLoginBotRequiredError(AuthBaseExternalLoginError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth external login bot required error"


class AuthExternalLoginBotTypeInvalidError(AuthBaseExternalLoginError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth external login bot type invalid error"

    def __init__(self, needed_bot_type: str, actual_bot_type: str):
        self.needed_bot_type = needed_bot_type
        self.actual_bot_type = actual_bot_type

        super().__init__(
            needed_bot_type=needed_bot_type,
            actual_bot_type=actual_bot_type,
        )


class AuthExternalLoginInvalidTypeError(AuthBaseExternalLoginError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth external login invalid type error"

    def __init__(self, actual_type: str):
        self.actual_type = actual_type
        super().__init__(actual_type=actual_type)


class AuthCantUnlinkError(BaseAuthError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "auth unlink error"


class AuthCantCheckAdminOrManagerError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth link unlink manager or admin request error"

    def __init__(self, messanger: str):
        self.messanger = messanger
        super().__init__(messanger=messanger)


class AuthDeleteAdminOrManagerError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth delete manager or admin error"


class AuthCantUnlinkOwnerError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth unlink owner error"

    def __init__(self, messanger: str):
        self.messanger = messanger
        super().__init__(messanger=messanger)


class AuthNoObjectsOrHaveNotEnoughPermissionsError(BaseAuthError):
    status_code = status.HTTP_403_FORBIDDEN

    def __init__(
            self,
            action: str,
            data: dict[str, Any] | None = None,
            available_fields: str | Iterable[str] = "*",
    ):
        if data is None:
            data = {}

        scopes_info_lines = []
        converted_scopes = []

        for scope in scope_map.iter_action_scopes(
                action, available_fields,
        ):
            line = scope.scope
            if scope.required_fields:
                line += " for " + ", ".join(sorted(scope.required_fields))
            scopes_info_lines.append(line)
            converted_scopes.append(scope.dict())

        scopes_info = "\n".join(scopes_info_lines)

        super().__init__(
            data=data,
            scopes_info=scopes_info,
            detail_data={
                "error_code": "not_enough_scopes",
                "action": action,
                "data": data,
                "required_one_of": converted_scopes,
            }
        )


class AuthUnknownActionsError(BaseAuthError):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "auth unknown actions error"

    def __init__(self, unknown_actions: list[str]):
        self.unknown_actions = unknown_actions
        super().__init__(
            unknown_actions=",".join(unknown_actions),
            detail_data={
                "unknown_actions": unknown_actions
            }
        )


class AuthGoogleOAuthError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth google oauth error"


class AuthAppleOAuthError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth apple oauth error"

    def __init__(self, error: str):
        self.error = error
        super().__init__(error=error)


class AuthNotProvidedHostUrlError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth not provided host url error"


class AuthAppleCallbackError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth apple callback error"


class AuthNotProvidedDataOAuthError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth not provided data oauth error"


class AuthUnknownOAuthProviderError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth unknown oauth provider error"


class AuthNotAuthorisedByOAuthError(BaseAuthError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "auth not authorised by oauth error"


class AuthNoRequiredAuthSessionError(BaseAuthError):
    status_code = status.HTTP_401_UNAUTHORIZED
    text_variable = "auth no required auth session error"


class AuthHeartBeatFutureTimeError(BaseAuthError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "auth heartbeat future datetime error"


class AuthRoleRequiredError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN

    def __init__(self, required_role: str):
        self.required_role = required_role
        super().__init__(
            required_role=required_role,
            detail_data={
                "required_role": required_role,
            }
        )
