from typing import Any

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus
from .base import ObjectNotFoundError


class BotNotFoundError(ObjectNotFoundError):
    pass


class ProfileBotDoesNotExistError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "profile bot does not exist error"

    def __init__(self, profile_id: Any):
        super().__init__(
            profile_id=profile_id,
            detail_data={
                "error_code": "profile_bot_does_not_exist",
                "profile_id": profile_id,
            }
        )


class UnableToFindBotToSendUserLinkError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
