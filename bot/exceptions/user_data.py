from starlette import status

import schemas
from exceptions import ObjectNotFoundError
from utils.exceptions import ErrorWithHTTPStatus


class UserDataNotFoundByIdError(ObjectNotFoundError):
    pass


class UserDataNotFoundByTargetAndTypeError(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND

    def __init__(self, target: schemas.UserDataTarget, target_id: int, type: str):
        self.target = target
        self.target_id = target_id
        self.type = type

        super().__init__(
            target=target,
            target_id=target_id,
            type=type,
            detail_data={
                "target": target,
                "target_id": target_id,
                "type": type,
            }
        )
