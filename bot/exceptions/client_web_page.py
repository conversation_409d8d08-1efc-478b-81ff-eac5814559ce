from starlette import status

from exceptions import ObjectNotFoundError
from utils.exceptions import ErrorWithHTTPStatus


class ClientWebPageNotFoundBySlugError(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND

    def __init__(self, slug: str):
        self.slug = slug
        super().__init__(
            slug=slug,
        )


class ClientWebPageNotFoundByTypeError(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND

    def __init__(self, page_type: str):
        self.page_type = page_type
        super().__init__(
            page_type=page_type,
        )


class ClientWebPageNotFoundError(ObjectNotFoundError):
    pass
