from abc import ABC

from psutils.exceptions import ErrorWithTextVariable
from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class AdminNotificationNotFound(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "admin alert not found error"

    def __init__(self, task_id: int):
        super().__init__(
            task_id=task_id,
            detail_data={
                "error_code": "admin_notification_not_found",
            }
        )


class AdminNotificationCategoryError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin alert category error text"

    def __init__(self, category: str):
        super().__init__(
            category=category,
            detail_data={
                "error_code": "invalid_category_admin_notification",
            }
        )


class AdminNotificationKafkaError(ErrorWithTextVariable, ABC):

    def __init__(self, message: str, **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"admin alert kafka error: {self.message}"


class AdminNotificationStatusError(AdminNotificationKafkaError):
    text_variable = "admin notification status error"

    def __init__(
            self,
            admin_notification_id: int,
            status: str,
    ):
        self.admin_notification_id = admin_notification_id
        self.status = status
        super().__init__(
            self.text_variable, task_id=str(self.admin_notification_id),
            status=self.status
        )

    def __str__(self):
        return f"{self.text_variable}"


class AdminNotificationTypeError(AdminNotificationKafkaError):
    text_variable = "admin alert type error text"

    def __init__(
            self,
            type_notification: str,
    ):
        self.type_notification = type_notification
        super().__init__(self.text_variable, type_notification=type_notification)

    def __str__(self):
        return f"{self.text_variable}"
