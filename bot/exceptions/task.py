from abc import ABC

from psutils.exceptions import ErrorWithTextVariable
from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class AdminTaskNotFound(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "admin task not found error"

    def __init__(self, task_id: int):
        super().__init__(
            task_id=task_id,
            detail_data={
                "error_code": "task_not_found",
            }
        )


class AdminTaskInvalidMedia(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin task invalid media"

    def __init__(self, task_id: int,):
        super().__init__(
            task_id=task_id,
            detail_data={
                "error_code": "task_invalid_media",
            }
        )


class AdminTaskObjectsRequiredError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin task objects required error text"

    def __init__(self,):
        super().__init__(
            detail_data={
                "error_code": "objects_required",
            }
        )


class AdminTaskTypeError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "task type error text"

    def __init__(self, type_task: str):
        super().__init__(
            type_task=type_task,
            detail_data={
                "error_code": "invalid_type_task",
            }
        )


class BaseTaskCrudError(ErrorWithHTTPStatus, ABC, base=True):
    def __init__(self, message: str = "tax crud error", **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"tax crud error: {self.message}"

    def __str__(self):
        return f"{self.message}"


class ObjectForProcessNotFoundError(BaseTaskCrudError):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "object for task not found error"

    def __init__(self, type_task: str, object_ids: str):
        self.type_task = type_task
        self.object_ids = object_ids
        super().__init__("object for task not found error", type_task=self.type_task, object_ids=object_ids)


class TaskKafkaError(ErrorWithTextVariable, ABC):

    def __init__(self, message: str, **kwargs):
        self.message = message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"task kafka error: {self.message}"


class OpenAIConfigError(TaskKafkaError):
    text_variable = "openai not found keys"

    def __init__(
            self,
            group_id: int,
    ):
        self.group_id = group_id
        super().__init__(str(self.group_id))

    def __str__(self):
        return f"{self.text_variable}"


class TaskStatusError(TaskKafkaError):
    text_variable = "ai product image task status error"

    def __init__(
            self,
            task_id: int,
            status: str,
    ):
        self.task_id = task_id
        self.status = status
        super().__init__(self.text_variable, task_id=str(self.task_id), status=self.status)

    def __str__(self):
        return f"{self.text_variable}"


class TaskTypeError(TaskKafkaError):
    text_variable = "task type error text"

    def __init__(
            self,
            task_type: str,
    ):
        self.task_type = task_type
        super().__init__(self.text_variable, task_type=task_type)

    def __str__(self):
        return f"{self.text_variable}"


class TaskUrlError(TaskKafkaError):
    text_variable = "kafka consumer task url error text"

    def __init__(
            self,
            url: str,
    ):
        super().__init__(self.text_variable, url=url)

    def __str__(self):
        return f"{self.text_variable}"


class TaskInvalidRateLimitFormatError(TaskKafkaError):
    text_variable = "kafka consumer task invalid rate limit format error text"

    def __init__(
            self,
            value: str,
    ):
        super().__init__(self.text_variable, value=value)

    def __str__(self):
        return f"{self.text_variable}"
