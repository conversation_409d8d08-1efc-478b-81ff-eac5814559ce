import asyncio
from pprint import pprint

import stripe
from sqlalchemy import select

from config import BILLING_STRIPE_SECRET_KEY
from core.billing.stripe_client import bstripe
from db import DBSession
from db.models import Group


async def main():
    stripe.api_key = BILLING_STRIPE_SECRET_KEY

    with DBSession() as db:
        non_null_ids = db.scalars(
            select(Group.stripe_customer_id)
            .where(Group.stripe_customer_id.is_not(None))
        ).all()

    for customer_id in non_null_ids:
        try:
            customer = await bstripe.customers.retrieve_async(customer_id)
            if customer:
                print(f"✅ ============== ✅")
                pprint(customer.to_dict())
            else:
                print(f"⚠️  Customer {customer_id} not found.")
        except stripe.error.StripeError as e:
            print(f"❌ Error retrieving customer {customer_id}: {e}")


if __name__ == "__main__":
    asyncio.run(main())
