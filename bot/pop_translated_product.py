import json

import typer

app = typer.Typer()


def separate():
    print()
    print("-" * 10)
    print()


@app.command()
def pop_product(
        file_path: str = typer.prompt("File path"),
):
    with open(file_path, "r") as file:
        data = json.load(file)

    while True:
        key = input("Enter key: ").strip()

        obj = data[key]

        for lang in obj:
            print(f"Name {lang.upper()}:")
            print(obj[lang]["name"])

            separate()

        separate()

        for lang in obj:
            print(f"Description {lang.upper()}:")
            print(obj[lang]["description"])

            separate()


if __name__ == '__main__':
    app()
