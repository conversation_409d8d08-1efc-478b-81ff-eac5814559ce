from fastapi import APIRouter, Depends

import schemas
from .service import EWalletExternalPaymentService

router = APIRouter(
    prefix="/external_payments",
    tags=["external_payments"],
)


@router.post("/")
async def create_ewallet_external_payment(
        data: schemas.CreateEWalletExternalPaymentData,
        service: EWalletExternalPaymentService = Depends()
) -> schemas.EWalletExternalPaymentSchema:
    payment = await service.create(data)
    return await service.to_schema(payment)


@router.get("/{payment_uuid_id}")
async def get_ewallet_external_payment(
        payment_uuid_id: str,
        service: EWalletExternalPaymentService = Depends()
) -> schemas.EWalletExternalPaymentSchema:
    payment = await service.get(payment_uuid_id)
    return await service.to_schema(payment)


@router.post("/{payment_uuid}/cancel")
async def cancel_ewallet_external_payment(
        payment_uuid: str,
        service: EWalletExternalPaymentService = Depends()
) -> schemas.EWalletExternalPaymentSchema:
    payment = await service.set_status(
        payment_uuid,
        schemas.EWalletExternalPaymentStatus.CANCELLED
    )
    return await service.to_schema(payment)
