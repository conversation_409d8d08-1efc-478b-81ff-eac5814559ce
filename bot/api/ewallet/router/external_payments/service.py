from typing import Annotated

from fastapi import Depends

import exceptions
import schemas
from core.api.depends import get_lang
from core.auth.depend import get_active_user
from core.ewallet.external_payment.functions import (
    create_ewallet_ext_payment,
    set_ewallet_ext_payment_status,
)
from core.ewallet.funcs import ewallet_to_schema
from db.models import EWallet, EWalletExternalPayment, Group, MediaObject, User


class EWalletExternalPaymentService:
    def __init__(
            self,
            user: Annotated[User, Depends(get_active_user)],
            lang: Annotated[str, Depends(get_lang)],
    ):
        self.user = user
        self.lang = lang
        self._ewallet: EWallet | None = None
        self._ewallet_media: MediaObject | None = None

    async def get(self, payment_uuid_id: str):
        payment = await EWalletExternalPayment.get(
            uuid_id=payment_uuid_id,
            user_id=self.user.id,
        )
        if not payment:
            raise exceptions.EwalletExternalPaymentNotFoundError(payment_uuid_id)
        return payment

    async def get_or_set_ewallet(self, id: int) -> EWallet:
        if self._ewallet is None:
            self._ewallet = await EWallet.get(id)
            if not self._ewallet:
                raise exceptions.EwalletNotFoundError(id)
        return self._ewallet

    async def create(self, data: schemas.CreateEWalletExternalPaymentData):
        ewallet = await self.get_or_set_ewallet(data.ewallet_id)
        profile = await Group.get_by_bot(ewallet.bot_id)

        return await create_ewallet_ext_payment(
            ewallet, profile,
            self.user, data,
            self.lang,
        )

    async def set_status(
            self, payment_uuid_id: str,
            new_status: schemas.EWalletExternalPaymentStatus,
    ):
        payment = await self.get(payment_uuid_id)
        await set_ewallet_ext_payment_status(
            payment, self.user,
            new_status, self.lang,
        )
        return payment

    async def to_schema(self, payment: EWalletExternalPayment):
        ewallet = await self.get_or_set_ewallet(payment.ewallet_id)

        if not self._ewallet_media and ewallet.media_id:
            self._ewallet_media = await MediaObject.get(ewallet.media_id)

        schema = schemas.EWalletExternalPaymentSchema(
            **payment.as_dict(True),
            total_amount=payment.total_amount,
            ewallet=await ewallet_to_schema(
                ewallet, self.user, self.lang, self._ewallet_media,
                add_account_info=self.user.id == payment.user_id,
            )
        )
        if self.user.id not in (payment.user_id, payment.payer_id):
            schema.transfer_data = None
        return schema
