from fastapi import APIRouter, Security
from psutils.fastapi.api_route_error_groups import APIRouteErrorGroups

from api.ewallet.router.payments.service import (
    EWalletApiService, EWalletPaymentData, EWalletPaymentResponse,
)
from schemas.payment.payment import EWalletPaymentStatusData

router = APIRouter(
    prefix="/payments",
    tags=["payments"],
    route_class=APIRouteErrorGroups,
)


@router.post(
    "/{payment_settings_id}",
)
async def create_ewallet_payment(
        data: EWalletPaymentData,
        service: EWalletApiService = Security(scopes=["profile_data:edit"])
) -> EWalletPaymentResponse:
    return await service.create_ewallet_payment(data)


@router.get(
    "/{payment_settings_id}/{ewallet_payment_uuid}",
)
async def get_payment_status(
        ewallet_payment_uuid: str,
        service: EWalletApiService = Security(scopes=["profile_data:edit"]),
) -> EWalletPaymentStatusData:
    return await service.get_payment_status(ewallet_payment_uuid)
