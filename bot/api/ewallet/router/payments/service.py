from datetime import datetime
from fastapi import Depends, HTTPException, Path
from pydantic import BaseModel

from api.admin.router.users.exceptions import \
    SystemUserNotExistsOrNotOwnerByProfileError
from core.api.depends import get_lang
from core.auth.depend import get_active_user
from core.payment.payment_processor import PaymentProcessor
from core.payment.payment_processor.providers.ewallet.client import (
    create_ewallet_payment, get_ewallet_by_payment_data,
)
from db import crud
from db.models import EWallet, EWalletPayment, Group, PaymentSettings, User
from loggers import JSONLogger
from schemas import (
    EWalletPaymentStatus,
)
from schemas.payment.payment import EWalletPaymentStatusData


class EWalletPaymentData(BaseModel):
    external_id: str
    amount: int
    currency: str
    return_url: str
    # lang: str | None = None
    description: str | None = None


class EWalletPaymentResponse(BaseModel):
    payment_url: str | None = None
    status: EWalletPaymentStatus | None = None
    ewallet_payment_uuid: str | None = None
    time_created: datetime | None = None
    message: str | None = None
    error: str | None = None


class EWalletApiService(object):
    def __init__(
            self,
            payment_settings_id: int = Path(),
            user: User = Depends(get_active_user),
            lang: str = Depends(get_lang),
    ):
        self.payment_settings_id = payment_settings_id
        self.user = user
        self.lang = lang
        self.group_id = None

    async def create_ewallet_payment(
            self, data: EWalletPaymentData
    ) -> EWalletPaymentResponse:

        if data.amount == 0:
            raise HTTPException(status_code=404, detail="Payment value not valid")

        # if data.lang:
        #     self.lang = data.lang

        credentials, ewallet, group, payment_settings = await self.get_data()

        logger = JSONLogger(
            "payments.ewallet", "ewallet api payment", {
                "data": data,
                "payment_settings": payment_settings.as_dict(),
            },
        )

        if not self.lang:
            self.lang = group.lang

        if data.currency != ewallet.currency:
            raise HTTPException(
                status_code=400,
                detail=f"Currency not valid. The valid currency is {ewallet.currency}"
            )

        kwargs = {
            "profile_id": self.group_id,
            "external_id": data.external_id,
            "amount_to_pay": data.amount,
            "success_url": data.return_url,
            "credentials": credentials,
            "currency": data.currency,
            "creator_id": self.user.id,
            "payment_settings_id": self.payment_settings_id,
            "description": data.description,
        }

        logger.debug("create_ewallet_payment", kwargs)

        try:
            ewallet_payment: EWalletPayment = await (
                create_ewallet_payment(**kwargs)
            )
        except Exception as err:
            logger.error(err, exc_info=True)
            raise HTTPException(status_code=500, detail="Error create eWallet payment")

        return EWalletPaymentResponse(
            payment_url=ewallet_payment.redirect_url,
            status=ewallet_payment.status,
            ewallet_payment_uuid=ewallet_payment.uuid_id,
            time_created=ewallet_payment.time_created,
        )

    async def get_data(self) -> tuple[dict, EWallet, Group, PaymentSettings]:
        payment_settings = await PaymentSettings.get(self.payment_settings_id)
        if not payment_settings:
            raise HTTPException(status_code=404, detail="Payment settings not found")

        group = await crud.get_group_by_brand_id(payment_settings.brand_id)
        if not group:
            raise HTTPException(status_code=404, detail="Profile not found")
        self.group_id = group.id

        system_user = await crud.get_system_user(self.user.id, self.group_id)
        if not system_user:
            raise SystemUserNotExistsOrNotOwnerByProfileError(
                self.user.id, self.group_id
            )

        credentials = await crud.get_payment_data(
            payment_settings_id=self.payment_settings_id,
        )
        if not credentials:
            raise HTTPException(status_code=404, detail="Credentials not found")

        ewallet = await get_ewallet_by_payment_data(credentials)

        return credentials, ewallet, group, payment_settings

    async def get_payment_status(
            self, ewallet_payment_uuid: str
    ) -> EWalletPaymentStatusData:

        credentials, ewallet, group, payment_settings = await self.get_data()

        logger = JSONLogger(
            "payments", "ewallet api payment", {
                "ewallet_payment_uuid": ewallet_payment_uuid,
                "payment_settings": payment_settings.as_dict(),
            },
        )

        kwargs = {
            "credentials": credentials, "ewallet_payment_uuid": ewallet_payment_uuid
        }

        processor = PaymentProcessor("ewallet")

        try:
            return await processor.get_payment_status(**kwargs)
        except Exception as err:
            logger.error(err, exc_info=True)
            raise HTTPException(
                status_code=500, detail="Error get eWallet payment status"
            )
