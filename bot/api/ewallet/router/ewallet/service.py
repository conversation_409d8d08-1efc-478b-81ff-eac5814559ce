from typing import Annotated

from fastapi import Depends

import exceptions
from core.api.depends import get_lang
from core.auth.depend import get_active_user_optional
from core.ewallet.funcs import ewallet_to_schema
from db import crud
from db.models import EWallet, MediaObject, User


class EwalletService:
    def __init__(
            self,
            lang: Annotated[str, Depends(get_lang)],
            user: Annotated[User | None, Depends(get_active_user_optional)]
    ):
        self.lang: str = lang
        self.user: User | None = user
        self._ewallet_media: MediaObject | None = None

    @property
    def user_id(self):
        return self.user.id if self.user else None

    async def to_schema(self, ewallet: EWallet):
        if not self._ewallet_media and ewallet.media_id:
            self._ewallet_media = await MediaObject.get(ewallet.media_id)

        return await ewallet_to_schema(
            ewallet, self.user, self.lang, self._ewallet_media
        )

    async def get(self, ewallet_uuid_id: str):
        ewallet = await crud.get_ewallet(ewallet_uuid_id, self.user_id)
        if ewallet is None:
            raise exceptions.EwalletNotFoundError(ewallet_uuid_id)
        return ewallet
