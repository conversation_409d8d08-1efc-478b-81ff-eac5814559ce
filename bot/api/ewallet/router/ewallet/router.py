from fastapi import APIRouter, Depends

import schemas
from .service import EwalletService

# doesn't have any prefix, because API already has /ewallet prefix
router = APIRouter(
    tags=["ewallet"]
)


@router.get("/{ewallet_uuid_id}")
async def get_ewallet(
        ewallet_uuid_id: str,
        service: EwalletService = Depends(),
) -> schemas.EwalletSchema:
    ewallet = await service.get(ewallet_uuid_id)
    return await service.to_schema(ewallet)
