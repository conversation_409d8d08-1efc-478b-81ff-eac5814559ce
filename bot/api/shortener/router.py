from fastapi import APIRouter, HTTPException
from starlette import status
from starlette.responses import RedirectResponse

from db import crud
from db.models import ShortLink

router = APIRouter()


@router.get("/{link_id}")
async def get_short_link(
        link_id: str,
):
    link_info: tuple[ShortLink, int] | None = await crud.use(link_id)
    if not link_info:
        raise HTTPException(
            detail="Invalid link",
            status_code=status.HTTP_404_NOT_FOUND,
        )

    link, uses = link_info

    if link.is_expired:
        raise HTTPException(
            detail="Link is expired",
            status_code=status.HTTP_410_GONE,
        )

    if link.max_uses is not None and uses >= link.max_uses:
        raise HTTPException(
            detail=(
                "Link have already been used"
                if link.max_uses == 1 else
                "You are no more allowed to use this link"
            ),
            status_code=status.HTTP_403_FORBIDDEN,
        )

    return RedirectResponse(url=link.url, status_code=status.HTTP_302_FOUND)
