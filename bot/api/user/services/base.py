from fastapi import Depends
from sqlalchemy.orm import Session

from core.api.depends import get_db, get_lang
from core.auth.depend import get_active_user
from core.auth.services.base import BaseAuthService
from core.store.depends import get_current_bot_with_brand, get_current_brand
from db.models import Brand, ClientBot, User


class BaseUserService(BaseAuthService):
    def __init__(
            self,
            db: Session = Depends(get_db),
            lang: str = Depends(get_lang),
            user: User = Depends(get_active_user),
            brand: Brand | None = Depends(get_current_brand),
            bot: ClientBot | str | None = Depends(get_current_bot_with_brand),
    ):
        self.user: User = user

        super().__init__(db, lang, brand, bot)
