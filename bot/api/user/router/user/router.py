from fastapi import APIRouter, Depends, Query, Security

import schemas
from core.store.depends import get_current_brand
from db import crud
from db.models import Brand, Customer, User
from utils.scopes_map import scope_map
from .functions import user_to_schema
from .service import AuthUserService

router = APIRouter(
    tags=["user"]
)


@router.get("/")
async def get_user(
        service: AuthUserService = Depends(),
) -> schemas.UserSchema:
    if service.brand:
        customer = await Customer.get(
            user_id=service.user.id, profile_id=service.brand.group_id
        )
        if not customer:
            await crud.create_customer(
                service.user.lang, service.user.id, service.brand.group_id, None
            )
    return await user_to_schema(service.user, service.bot, service.brand)


@router.get("/check_access_to_actions")
async def check_user_access_to_actions(
        actions: list[str] = Query(),
        available_data: schemas.CheckAccessToActionsData = Depends(),
        service: AuthUserService = Depends()
) -> schemas.AccessToActionsSchema:
    return await service.check_user_access_to_actions(actions, available_data)


@router.get("/incustCustomerData")
async def get_user_incust_customer_data(
        service: AuthUserService = Depends(),
        store_id: int | None = Query(default=None, nullable=True),
        invoice_template_id: int | None = Query(default=None, nullable=True),
) -> schemas.UserIncustCustomerData:
    return await service.get_user_incust_customer_data(store_id, invoice_template_id)


@router.post("/getOrSetUserLang")
async def get_or_set_user_lang(
        data: schemas.GetOrSetUserLangData,
        service: AuthUserService = Security(scopes=["me:write"])
) -> schemas.LangData:
    lang = await service.get_or_set_user_lang(data.default_langs)
    return schemas.LangData(
        lang=lang
    )


@router.patch("/")
async def update_user(
        data: schemas.UpdateUserData,
        service: AuthUserService = Security(scopes=["me:write"]),
) -> schemas.UserSchema:
    user = await service.update_user(data)
    return await user_to_schema(user, service.bot, service.brand)


@router.patch("/lang")
async def update_user_lang(
        data: schemas.LangData,
        service: AuthUserService = Security(scopes=["me:write"]),
) -> schemas.LangData:
    user = await service.update_user(schemas.UpdateUserData(lang=data.lang))
    return schemas.LangData(
        lang=await user.get_lang(service.safe_bot_id),
    )


@router.post("/unlinkMessanger")
async def unlink_messanger(
        data: schemas.UnlinkMessangerData,
        service: AuthUserService = Security(scopes=["me:write"])
) -> schemas.UserSchema:
    await service.unlink_user_messanger(data.messanger_type, data.password)
    return await user_to_schema(service.user, service.bot, service.brand)


@router.post("/changePassword")
async def change_password(
        data: schemas.ChangePasswordData,
        service: AuthUserService = Security(scopes=["me:write"]),
) -> schemas.UserSchema:
    await service.change_user_password(data)
    return await user_to_schema(service.user, service.bot, service.brand)


@router.get("/checkIsManagerOrAdmin")
async def check_is_manager_or_admin_exists(
        service: AuthUserService = Security(),
) -> schemas.IsManagerOrAdmin:
    return await service.check_is_manager_or_admin()


@router.delete("/friends/{friend_id:int}")
async def delete_friend(
        friend_id: int,
        service: AuthUserService = Security(scopes=["me:write"]),
) -> list[schemas.FriendData]:
    await service.delete_user_friend(friend_id)
    return await service.get_user_friends()


@router.get("/friends")
async def get_friends(
        service: AuthUserService = Security(),
) -> list[schemas.FriendData]:
    return await service.get_user_friends()


@router.post("/friends/{req_user_id:int}")
async def add_friend(
        req_user_id: int,
        service: AuthUserService = Security(scopes=["me:write"]),
) -> dict:
    await service.add_user_friend(req_user_id)
    return {"message": "Ok"}


@router.get("/friends/{req_user_id:int}")
async def get_friend(
        req_user_id: int,
        brand: Brand | None = Depends(get_current_brand),
) -> schemas.UserSchema:
    req_user = await User.get_by_id(req_user_id)
    bot = await crud.get_bot_by_brand(brand.id)
    return await user_to_schema(req_user, bot, brand)


@router.post("/delete-account")
async def delete_user_account(
        data: schemas.DeleteUserAccountData,
        service: AuthUserService = Security(scopes=["me:write"]),
) -> schemas.UserAccountDeleted:
    return await service.delete_user_account(data)


@router.get("/allowed_actions")
async def get_user_allowed_actions(
        params: schemas.GetUserAllowedActionsParams = Depends(),
        service: AuthUserService = Depends(),
) -> list[scope_map.get_all_actions_literal()]:
    return await service.get_user_allowed_actions(params)
