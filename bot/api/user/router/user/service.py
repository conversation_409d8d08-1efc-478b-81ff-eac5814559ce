import logging
from dataclasses import asdict

import aiogram as tg
import aiowhatsapp as wa
from fastapi import Depends, HTTPException
from incust_api.api import term
from sqlalchemy.orm import Session
from starlette import status

import schemas
from config import DEFAULT_LANG, MESSANGERS_NAMES
from core.api.depends import get_db, get_lang
from core.auth.depend import get_active_user
from core.auth.functions import (
    notify_user_about_lang_change, validate_email, validate_password,
    validate_telegram_data_and_chat_id,
)
from core.incust.functions import (
    update_incust_birth_date,
    update_incust_user_lang,
)
from core.loyalty.customer_service import get_or_create_incust_customer
from core.loyalty.incust_api import incust
from core.store.depends import get_current_bot_with_brand, get_current_brand
from db import crud
from db.models import (
    Brand, ClientBot, Group, User,
    UserClientBotActivity,
)
from exceptions import (
    AuthCantCheckAdminOrManagerError, AuthCantUnlinkError, AuthCantUnlinkOwnerError,
    AuthDeleteAdminOrManagerError,
    AuthIncorrectPasswordError, AuthUnknownActionsError,
)
from utils.message import send_tg_message
from utils.scopes_map import scope_map
from utils.text import f
from utils.translator import Translator
from ...services.base import BaseUserService


class AuthUserService(BaseUserService):
    def __init__(
            self,
            db: Session = Depends(get_db),
            lang: str = Depends(get_lang),
            user: User = Depends(get_active_user),
            brand: Brand | None = Depends(get_current_brand),
            bot: ClientBot | str | None = Depends(get_current_bot_with_brand),
    ):
        super().__init__(db, lang, user, brand, bot)

    async def update_user(
            self, data: schemas.UpdateUserData,
    ):
        bot_id: int | None = self.bot.id if isinstance(self.bot, ClientBot) else None

        if not self.user.birth_date and data.birth_date:
            loyalty_settings = await crud.get_loyalty_settings_for_context(
                "brand",
                schemas.LoyaltySettingsData(
                    brand_id=self.brand.id,
                    profile_id=self.brand.group_id,
                )
            ) if self.brand else None

            if loyalty_settings:
                try:
                    await update_incust_birth_date(
                        self.user, self.brand,
                        data.lang or await self.user.get_lang(bot_id),
                        data.birth_date
                    )
                except Exception as e:
                    logging.error(e, exc_info=True)

        user, is_lang_updated = await crud.update_user(
            self.db, self.user, data, bot_id,
            need_lang_updated_info=True,
        )

        if not is_lang_updated:
            return user

        new_lang: str = data.lang

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=self.brand.id if self.brand else None,
                profile_id=self.brand.group_id if self.brand else None,
            )
        )

        if loyalty_settings:
            try:
                await update_incust_user_lang(user, self.brand, new_lang)
            except Exception as e:
                logging.error(e, exc_info=True)

        if self.bot:
            try:
                await notify_user_about_lang_change(user, self.bot, new_lang)
            except Exception as e:
                logging.error(e, exc_info=True)

        return user

    def _verify_password(self, password: str | None):
        if (self.user.hashed_password or password) and \
                not self.user.verify_password(password):
            raise AuthIncorrectPasswordError()

    async def change_user_telegram(self, data: schemas.SetTelegramData):
        self._verify_password(data.password)

        tg_data = data.telegram_data

        await validate_telegram_data_and_chat_id(self.db, tg_data, self.bot)
        await crud.update_user(
            self.db, self.user, schemas.UpdateUser(
                chat_id=tg_data.id,
                first_name=tg_data.first_name,
                last_name=tg_data.last_name,
                username=tg_data.username,
            )
        )

    async def unlink_user_messanger(
            self,
            messanger_type: schemas.BotTypeLiteral,
            password: str | None = None,
    ):
        self.__validate_can_unlink_messanger()
        if self.user.hashed_password or password:
            self._verify_password(password)

        if messanger_type == "telegram":
            is_any_owner = await crud.get_user_own_groups(user_id=self.user.id)
            if is_any_owner:
                raise AuthCantUnlinkOwnerError(messanger="Telegram")
            await self.__try_remove_user_from_groups_manager_and_admin()

        data = {}

        match messanger_type:
            case "telegram":
                data.update(
                    chat_id=None,
                    username=None,
                )
            case "whatsapp":
                data.update(
                    wa_phone=None,
                    wa_name=None,
                )

        await crud.update_user(
            self.db, self.user, schemas.UpdateUser(
                chat_id=None,
                username=None,
                **data,
            )
        )

        try:
            if self.bot:
                if self.brand:
                    host = self.brand.get_url()
                else:
                    host = self.bot.display_name

                text = await f(
                    "auth unlink success messanger header",
                    self.lang,
                    messanger=MESSANGERS_NAMES[messanger_type],
                    name=self.user.name,
                    host=host,
                )

                match messanger_type:
                    case "telegram":
                        await send_tg_message(
                            self.user.chat_id, 'text', bot_token=self.bot.token,
                            keyboard=tg.types.ReplyKeyboardRemove(), text=text
                        )
                    case "whatsapp":
                        bot = wa.WhatsappBot(
                            self.bot.token,
                            self.bot.whatsapp_from,
                        )
                        await bot.send_message(self.user.wa_phone, text)
        except Exception as ex:
            logging.error(ex, exc_info=True)

    async def change_user_email(self, data: schemas.ChangeEmailData):
        self._verify_password(data.password)

        await validate_email(self.db, data.new_email, "change_email")
        await crud.update_user_email_or_password(
            self.db, self.user,
            "change_email",
            data.new_email,
        )

    async def change_user_password(self, data: schemas.ChangePasswordData):
        self._verify_password(data.current_password)
        validate_password(data.new_password)
        await crud.update_user(
            self.db, self.user,
            schemas.UpdateUser(password=data.new_password),
        )

    async def check_is_manager_or_admin(self) -> schemas.IsManagerOrAdmin:
        res = schemas.IsManagerOrAdmin(
            is_manager_or_admin=False,
            admin_count=0,
            manager_count=0,
        )

        try:
            groups_admin = await crud.get_groups_admin(self.user.id, operation="count")

            if groups_admin:
                res.is_manager_or_admin = True
                res.admin_count = groups_admin
        except Exception as e:
            logging.error(e, exc_info=True)
            raise AuthCantCheckAdminOrManagerError(messanger="Telegram")

        return res

    async def __try_remove_user_from_groups_manager_and_admin(self):
        admin_or_manager = await self.check_is_manager_or_admin()
        if admin_or_manager.is_manager_or_admin:
            try:
                groups_admin = await crud.get_groups_admin(
                    self.user.id, operation="all"
                )
            except Exception as e:
                logging.error(e, exc_info=True)
                raise AuthCantCheckAdminOrManagerError(messanger="Telegram")

            try:
                if groups_admin:
                    for group in groups_admin:
                        await crud.delete_admin(group.id, self.user.id)
            except Exception as e:
                logging.error(e, exc_info=True)
                raise AuthDeleteAdminOrManagerError()

    def __validate_can_unlink_messanger(self):
        if not self.user.email:
            raise AuthCantUnlinkError()

    @staticmethod
    async def delete_user_friend(friend_id: int):
        await crud.delete_user_friend(friend_id=friend_id)

    async def get_user_friends(self) -> list[schemas.FriendData]:
        return await crud.get_user_friends(
            user_id=self.user.id, bot_type=self.bot.bot_type if self.bot and isinstance(
                self.bot, ClientBot
            ) else None
        )

    async def add_user_friend(self, req_user_id: int):
        lang = await self.user.get_lang()
        friend = self.user
        if friend.id == req_user_id:
            raise HTTPException(
                status.HTTP_400_BAD_REQUEST, "You can not add self as friend"
            )

        req_user = await User.get_by_id(req_user_id)
        if not req_user:
            raise HTTPException(status.HTTP_404_NOT_FOUND, "Requested user not found")

        if await crud.get_user_friend(req_user_id, friend.id):
            raise HTTPException(
                status.HTTP_400_BAD_REQUEST,
                await f(
                    "client bot add friend exist text", lang, req_name=req_user.name
                )
            )

        await crud.create_user_friend(req_user_id, friend.id)

    async def get_user_friend(self, req_user_id: int) -> User:
        req_user = await User.get_by_id(req_user_id)
        if not req_user:
            raise HTTPException(status.HTTP_404_NOT_FOUND, "Requested user not found")
        return req_user

    async def check_user_access_to_actions(
            self, actions: list[str],
            available_data: schemas.CheckAccessToActionsData,
    ):
        unknown_actions = [action for action in actions if
                           action not in scope_map.available_actions]
        if unknown_actions:
            raise AuthUnknownActionsError(unknown_actions)

        return schemas.AccessToActionsSchema(
            actions=await crud.check_access_to_actions(
                actions,
                "user", self.user.id,
                asdict(available_data),
            )
        )

    async def get_user_incust_customer_data(
            self, store_id: int | None = None, invoice_template_id: int | None = None
    ):
        incust_customer_data = None

        target = "invoice_template" if invoice_template_id else ("store" if store_id else "brand")
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            target,
            schemas.LoyaltySettingsData(
                brand_id=self.brand.id if self.brand else None,
                store_id=store_id,
                invoice_template_id=invoice_template_id,
                profile_id=self.brand.group_id if self.brand else None,
            )
        )

        if loyalty_settings:
            try:
                # Створюємо або отримуємо InCust customer
                incust_customer = await get_or_create_incust_customer(
                    self.user, loyalty_settings, self.lang
                )

                if incust_customer:
                    async with incust.term.CustomerApi(
                            loyalty_settings, lang=self.lang
                    ) as api:
                        card_info = await api.cardinfo(
                            self.user.incust_external_id,
                            term.m.IdType("external-id"),
                        )

                    incust_customer_data = schemas.IncustCustomerData(
                        token=incust_customer.token,
                        external_id=self.user.incust_external_id,
                        user_card=card_info,
                    )

            except Exception as e:
                logging.error(f"Failed to get InCust customer data: {e}", exc_info=True)

        return schemas.UserIncustCustomerData(
            user_id=self.user.id,
            incust_customer_data=incust_customer_data,
        )

    async def delete_user_account(self, data: schemas.DeleteUserAccountData):
        self._verify_password(data.current_password)
        await crud.delete_user_account(self.user)
        return schemas.UserAccountDeleted(
            user_id=self.user.id,
        )

    async def get_or_set_user_lang(self, default_langs: list[str] | None = None):
        group = await Group.get(self.brand.group_id) if self.brand else None

        if not group or (group.is_translate and group.allow_all_google_langs):
            available_langs = list(
                (await Translator.get_supported_languages(DEFAULT_LANG)).keys()
            )
        else:
            available_langs = group.get_langs_list()

        default_lang = next(
            (
                x for x in default_langs
                if x in available_langs
            ), None
        ) if default_langs else None

        if not default_lang or default_lang not in available_langs:
            default_lang = group.lang if group else DEFAULT_LANG

        new_lang: str | None = None
        if self.bot:
            user_bot_activity = await UserClientBotActivity.get(self.user, self.bot)
            current_lang = user_bot_activity.lang if user_bot_activity.is_lang else None
        else:
            current_lang = self.user.lang if self.user.is_lang else None

        if not current_lang or (current_lang not in available_langs):
            new_lang = default_lang

        if new_lang:
            await self.update_user(schemas.UpdateUserData(lang=new_lang))
            current_lang = new_lang

        return current_lang

    async def get_user_allowed_actions(
            self, params: schemas.GetUserAllowedActionsParams
    ):
        scopes = await crud.get_user_scopes(self.user.id, **params.dict())

        actions = list()
        for scope in scopes:
            for action in scope_map.actions_by_scope[scope.scope]:
                if action not in actions:
                    actions.append(action)
        return actions
