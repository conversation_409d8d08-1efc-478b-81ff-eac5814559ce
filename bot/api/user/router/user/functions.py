import schemas
from db.models import Brand, <PERSON><PERSON><PERSON><PERSON>, Customer, User


async def user_to_schema(
        user: User,
        bot: ClientBot | str | None = None,
        brand: Brand | None = None,
) -> schemas.UserSchema:
    customer = None
    if brand or isinstance(bot, ClientBot):
        group_id = brand.group_id if brand else bot.group_id
        customer = await Customer.get(user_id=user.id, profile_id=group_id)

    return schemas.UserSchema(
        id=user.id,
        lang=await user.get_lang(
            bot.id if isinstance(bot, ClientBot) else None,
            fix_if_incorrect=False,
        ),
        email=user.email,
        is_confirmed_email=user.is_confirmed_email,
        is_only_email=user.is_only_email,
        is_messanger=user.is_messanger,
        client=user.client,
        is_anonymous=user.is_anonymous,
        chat_id=user.chat_id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        full_name=user.full_name,
        wa_name=user.wa_name,
        wa_phone=user.wa_phone,
        db_timezone=user.db_timezone,
        is_accepted_agreement=user.is_accepted_agreement,
        is_guest_user=user.is_guest_user,
        photo=user.photo,
        photo_url=user.photo_url,
        is_password=user.is_password,
        has_multiple_ids=user.has_multiple_ids,
        birth_date=user.birth_date,
        name=user.name,
        messangers=user.messangers,
        is_system_user=user.is_system_user,
        incust_external_id=user.incust_external_id,
        marketing_consent=customer.marketing_consent if customer else None,
    )
