from typing import Annotated

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, Query, Security

import schemas
from api.user.router.user_data.service import UserDataServiceApi
from core.user_data.functions import user_data_list_to_schemas, user_data_to_schema

router = APIRouter(
    prefix="/data",
    tags=["user data"]
)


@router.get("/")
async def get_user_data_list(
        params: Annotated[schemas.UserDataListParams, Query()],
        service: Annotated[UserDataServiceApi, Depends()],
) -> list[schemas.UserDataSchema]:
    data_list = await service.get_user_data_list(params)
    return user_data_list_to_schemas(data_list)


@router.post("/")
async def create_user_data(
        data: schemas.CreateUserDataData,
        service: Annotated[UserDataServiceApi, Security(scopes=["me:write"])],
) -> schemas.UserDataSchema:
    user_data = await service.create_user_data(data, ("body", "data"))
    return user_data_to_schema(user_data)


@router.get("/{user_data_id}")
async def get_user_data_by_id(
        user_data_id: int,
        service: Annotated[UserDataServiceApi, Depends()],
) -> schemas.UserDataSchema:
    user_data = await service.get_user_data_by_id(user_data_id)
    return user_data_to_schema(user_data)


@router.put("/{user_data_id}/data")
async def update_user_data_by_id(
        user_data_id: int,
        data: schemas.UpdateUserDataByIdData,
        service: Annotated[UserDataServiceApi, Security(scopes=["me:write"])],
) -> schemas.UserDataSchema:
    user_data = await service.get_user_data_by_id(user_data_id)
    await service.update_user_data(user_data, data, ("body", "new_data"))
    return user_data_to_schema(user_data)


@router.post("/{user_data_id}/move")
async def move_user_data_by_id(
        user_data_id: int,
        data: schemas.MoveUserDataData,
        service: Annotated[UserDataServiceApi, Security(scopes=["me:write"])],
) -> schemas.UserDataSchema:
    user_data = await service.get_user_data_by_id(user_data_id)
    await service.move_user_data(user_data_id, data)
    return user_data_to_schema(user_data)


@router.delete("/{user_data_id}")
async def delete_user_data_by_id(
        user_data_id: int,
        service: Annotated[UserDataServiceApi, Security(scopes=["me:write"])],
) -> schemas.OkResponse:
    user_data = await service.get_user_data_by_id(user_data_id)
    await user_data.delete()
    return schemas.OkResponse()


@router.get("/{target}/{target_id}/{type}")
async def get_user_data_by_target_and_type(
        target: schemas.UserDataTarget,
        target_id: int,
        type: str,
        service: Annotated[UserDataServiceApi, Depends()],
) -> list[schemas.UserDataSchema]:
    params = schemas.UserDataListParams(
        type=type,
        target=target,
        **{f"{target.value}_id": target_id}
    )
    data_list = await service.get_user_data_list(params)
    return user_data_list_to_schemas(data_list)


@router.delete("/{target}/{target_id}/{type}")
async def delete_user_data_by_target_and_type(
        target: schemas.UserDataTarget,
        target_id: int,
        type: str,
        service: Annotated[UserDataServiceApi, Security(scopes=["me:write"])],
) -> schemas.OkResponse:
    await service.delete_user_data_by_target_and_type(target, target_id, type)
    return schemas.OkResponse()
