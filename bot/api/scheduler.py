import logging

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

logger = logging.getLogger("scheduler")
scheduler = AsyncIOScheduler()

_registered_jobs = []


def scheduled(
        *, day: str = None, hour: str = None, minute: str = None, second: str = None
):
    """
    Decorator to register a function as a scheduled job.
    The function will be executed according to the specified cron schedule.
    """

    def decorator(fn):
        _registered_jobs.append(
            (fn, {"day": day, "hour": hour, "minute": minute, "second": second})
        )
        return fn

    return decorator


def start_scheduler():
    """
    Starts the scheduler and registers all scheduled jobs.
    Each job is registered with a cron trigger based on the provided schedule.
    The jobs will be executed asynchronously.
    """
    for fn, cron_args in _registered_jobs:
        trigger = CronTrigger(**{k: v for k, v in cron_args.items() if v is not None})
        scheduler.add_job(
            fn,
            trigger,
            id=fn.__name__,
            replace_existing=True,
        )
        logger.info(f"[Scheduler] registered job {fn.__name__} → {cron_args}")
    scheduler.start()
    logger.info("[Scheduler] started")


def shutdown_scheduler():
    """
    Shuts down the scheduler, stopping all scheduled jobs.
    """
    scheduler.shutdown(wait=False)
    logger.info("[Scheduler] shutdown")
