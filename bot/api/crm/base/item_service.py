from abc import abstractmethod
from typing import Generic

from fastapi import Depends

from core.api.depends import get_lang
from core.auth.depend import get_active_user, get_auth_session_from_access_token
from db.models import AuthSession, User
from utils.type_vars import T


class CRMItemService(Generic[T]):
    @staticmethod
    @abstractmethod
    def get_item(*args, **kwargs) -> T:
        raise NotImplementedError

    def __init_subclass__(cls, **kwargs):
        def __init__(
                self,
                item: T = Depends(cls.get_item),
                user: User = Depends(get_active_user),
                lang: str = Depends(get_lang),
                auth_session: AuthSession | None = Depends(
                    get_auth_session_from_access_token
                )
        ):
            self.item = item
            self.user = user
            self.lang = lang
            self.auth_session = auth_session

        cls.__init__ = __init__

    def __init__(
            self,
            item: T,
            user: User,
            lang: str,
            auth_session: AuthSession | None = None,
    ):
        self.item: T = item
        self.user: User = user
        self.lang: str = lang
        self.auth_session: AuthSession | None = auth_session

    @property
    def auth_session_id(self):
        return self.auth_session.id if self.auth_session else None
