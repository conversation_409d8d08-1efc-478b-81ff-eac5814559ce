import asyncio
from typing import Awaitable, Generic, Protocol, Type, TypeVar

from fastapi import HTT<PERSON>Exception
from psutils.func import check_function_spec
from pydantic import BaseModel
from starlette import status

import schemas
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from db.models import User
from db.types.operation import Operation


class ParamsProtocol(Protocol):
    limit: int | None
    cursor: schemas.Cursor | None


class GetListFunc(Protocol):
    async def __call__(
            self, user_id: int,
            params: ParamsProtocol,
            operation: Operation = "list",
            cursor: schemas.Cursor | None = None
    ) -> list | bool:
        ...


SchemaT = TypeVar("SchemaT", bound=BaseModel)


class CRMListService(Generic[SchemaT]):
    schema_type: Type[SchemaT]
    get_objects_func: GetListFunc

    def __init_subclass__(cls, **kwargs):
        if not getattr(cls, "get_objects_func", None):
            raise TypeError(
                "get_objects_func is not defined. Please, define it either as class "
                "variable or method"
            )

        cls.get_objects_func = staticmethod(cls.get_objects_func)

        super().__init_subclass__(**kwargs)

    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend()
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang

    @classmethod
    def validate_params(cls, params: ParamsProtocol):
        if not params.limit or params.limit > 100:
            raise APIListLimitError()

        if (hasattr(params, "offset") and params.offset) and params.cursor:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Offset parameter cannot be used at the same time.",
            )

    def map_objects(self, objects: list) -> list[SchemaT] | Awaitable[list[SchemaT]]:
        if not hasattr(self, "schema_type"):
            raise TypeError(
                "schema_type is not defined. "
                "Please, either define it as class variable or redefine map_objects "
                "method"
            )

        return list(map(self.schema_type.from_orm, objects))

    # noinspection PyMethodMayBeStatic
    def get_next_cursor(
            self, **kwargs
    ) -> schemas.Cursor | Awaitable[schemas.Cursor]:
        objects = kwargs["objects"]
        return schemas.IDCursor.obj(objects[-1])

    async def get_list(
            self, params: ParamsProtocol,
    ) -> schemas.ListCursorResponse[SchemaT]:
        self.validate_params(params)

        db_objects = await self.get_objects_func(self.user.id, params)

        map_result = self.map_objects(db_objects)

        objects: list[SchemaT]
        objects = await map_result if asyncio.iscoroutine(map_result) else map_result

        if objects:
            safe_kwargs = check_function_spec(
                self.get_next_cursor, {
                    "objects": objects,
                    "params": params,
                }
            )
            cursor_result = self.get_next_cursor(**safe_kwargs)
            cursor = await cursor_result if asyncio.iscoroutine(
                cursor_result
            ) else cursor_result
            is_next_exists = await self.get_objects_func(
                self.user.id, params, "exists", cursor
            )
            if not is_next_exists:
                cursor = None
        else:
            cursor = None

        res = schemas.ListCursorResponse[SchemaT](
            data=objects,
            next=cursor.to_str() if cursor else None,
        )
        return res
