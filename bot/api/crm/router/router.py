from fastapi import APIRouter

from . import (
    chats, ewallet_ext_payment, filters, inbox, invoices, notification_settings, orders,
    receipt, reviews, storage, text_notifications, tickets, users, watsapp_templates,
)

router = APIRouter()

router.include_router(inbox.router)
router.include_router(orders.router)
router.include_router(invoices.router)
router.include_router(reviews.router)
router.include_router(tickets.router)
router.include_router(chats.router)
router.include_router(text_notifications.router)
router.include_router(ewallet_ext_payment.router)
router.include_router(filters.router)
router.include_router(users.router)
router.include_router(notification_settings.router)
router.include_router(watsapp_templates.router)
router.include_router(storage.router)
router.include_router(receipt.router)
