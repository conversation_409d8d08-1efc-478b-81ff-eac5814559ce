import logging

import schemas
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.exceptions import AdminGroupBotNotFoundError
from db import crud
from db.models import ClientBot, User, WAMasterTemplate, WATemplate

debugger = logging.getLogger("debugger")


class CRMWaTemplatesService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    @staticmethod
    def wa_master_list_template_to_schema(
            template: WAMasterTemplate,
            is_active: bool,
            is_pending: bool = False,
    ) -> schemas.CRMWaMasterListTemplate:
        return schemas.CRMWaMasterListTemplate(
            id=template.id,
            name=template.name,
            description=template.description,
            category=template.category,
            is_active=is_active,
            is_pending=is_pending,
        )

    async def get_wa_master_templates(
            self,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.CRMWaMasterListTemplate]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)
        bot = await ClientBot.get(group_id=self.profile_id)

        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        masters = await crud.get_wa_master_templates(
            self.profile_id,
            bot.id,
            self.user.id,
            search_text,
            offset,
            limit,
        )

        result: list[schemas.CRMWaMasterListTemplate] = []
        for master in masters:
            children = await WATemplate.get_list(master_template_id=master.id)

            is_active_flag = bool(children) and all(
                c.template_status == "APPROVED" for c in children
            )

            is_pending_flag = bool(children) and any(
                c.template_status == "PENDING" for c in children
            )

            result.append(
                self.wa_master_list_template_to_schema(
                    master,
                    is_active_flag,
                    is_pending_flag,
                )
            )

        return result

    @staticmethod
    async def get_reply_message_suggestions(
            search: str,
            limit: int | None = 20,
    ) -> list[schemas.CRMReplyMessageSuggestion]:
        suggestions = await crud.get_reply_message_by_message_virtual(
            value=search, limit=limit
        )

        return [
            schemas.CRMReplyMessageSuggestion(
                id=id_,
                message=(content or {}).get("message", "")
            )
            for id_, content in suggestions
        ]
