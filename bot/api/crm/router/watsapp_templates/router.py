from fastapi import APIRouter, Depends, Query

import schemas
from api.admin.router.bots.bots.wa_templates.service import WaTemplatesService
from .service import CRMWaTemplatesService

router = APIRouter(
    prefix="/watsapp-templates/{profile_id}",
    tags=["watsapp-templates"]
)


@router.get("/")
async def get_crm_master_wa_templates(
        search_text: str | None = Query(None),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        service: CRMWaTemplatesService = Depends(),
) -> list[schemas.CRMWaMasterListTemplate]:
    return await service.get_wa_master_templates(search_text, offset, limit)


@router.get("/{master_id}")
async def get_crm_wa_template(
        master_id: int,
        chat_id: int | None = Query(None),
        service: WaTemplatesService = Depends(),
) -> schemas.AdminWaMasterTemplate:

    return await service.get_wa_templates(master_id, chat_id)


@router.get("/template/reply-message-suggestions")
async def get_reply_message_suggestions(
        search: str = Query(min_length=1, description="Search text"),
        limit: int | None = Query(20),
        service: CRMWaTemplatesService = Depends(),
) -> list[schemas.CRMReplyMessageSuggestion]:
    return await service.get_reply_message_suggestions(search, limit)
