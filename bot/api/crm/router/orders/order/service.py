from fastapi import Depends, Path

import schemas
from api.crm.base.item_service import CRMItemService
from core.auth.depend import Action, get_active_user
from core.check.texts import (
    get_address_text, get_payment_info,
    get_shipment_name_and_data,
)
from core.custom_texts.models.group import GroupCustomTextsModel
from core.store.order.service import change_store_order_status
from db import crud
from db.models import (
    Group, Invoice, MenuInStore, OrderProduct, OrderShipment, Store, StoreOrder,
    StoreOrderPayment, User,
)
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class OrderService(CRMItemService[StoreOrder]):
    @staticmethod
    async def get_item(
            order_id: int = Path(ge=1),
            action: str = Action(
                "crm_order:read",
                ("crm_order:read", "crm_order:edit"),
            ),
            user: User = Depends(get_active_user)
    ):
        if not (order := await crud.get_order(order_id, action, user.id)):
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                action,
                {"order_id": order_id},
            )
        return order

    async def order_to_schema(self):
        base_schema = self.item.from_orm_converted(schemas.CRMOrderOrmAttributesSchema)

        store = await Store.get(self.item.store_id)
        group = await Group.get_by_brand(store.brand_id)
        order_user = await User.get_by_id(self.item.user_id)
        invoice = await Invoice.get(self.item.invoice_id) if self.item.invoice_id else None

        shipment: OrderShipment = await crud.get_order_shipment(self.item.id)
        order_payment = await StoreOrderPayment.get(order_id=self.item.id)

        shipment_name, shipment_label_comment = await get_shipment_name_and_data(
            shipment, group, self.lang
        )

        (payment_method_name, payment_method_label_comment, payment_raw_price,
         _) = await get_payment_info(
            shipment, order_payment, group, self.lang
        )

        status_history: list[
            schemas.CRMOrderStatusHistoryObject] = await (
            crud.get_crm_order_status_history(
                self.item.id
            ))

        # there is no situation with statuses < 1
        if len(status_history) == 1:
            current_status_obj = status_history[0]
        else:
            current_status_obj = status_history[-1] if status_history[
                                                           -1].status != "payed" else \
                status_history[-2]

        if current_status_obj.status == "payed":
            current_status = self.item.status
            status_set_datetime = self.item.create_date
            status_comment = None
        else:
            current_status = current_status_obj.status
            status_set_datetime = current_status_obj.set_datetime
            status_comment = current_status_obj.comment

        order_products: list[OrderProduct] = await crud.get_order_products(
            self.item.id,
            load_products=True,
            load_attributes=True,
        )

        menu_in_store = await MenuInStore.get(
            self.item.menu_in_store_id
        ) if self.item.menu_in_store_id else None

        if invoice and invoice.loyalty_settings_id:

            bonuses_added_amount = invoice.bonuses_added_amount
            special_accounts_charges = invoice.special_accounts_charges
            emitted_coupons_batches = [coupon.batch for coupon in
                                       (invoice.emitted_coupons or []) if
                                       coupon.batch]
        else:
            bonuses_added_amount = None
            special_accounts_charges = None
            emitted_coupons_batches = None

        ct_obj = await GroupCustomTextsModel.from_object(group)
        texts = await ct_obj.to_dict(self.lang)
        label_comment = texts['web']['order']['order_comment_label']

        return schemas.CRMOrderSchema(
            **base_schema.dict(),
            invoice_id=self.item.invoice_id if self.item.invoice_id else None,
            photo_url=order_user.photo_url if order_user else None,
            menu_in_store_comment=menu_in_store.comment if menu_in_store else None,
            products=[
                # loaded order_product.product loaded in crud with joinedload
                schemas.CRMOrderProductSchema(
                    id=order_product.id,
                    name=order_product.product._internal_name if
                    order_product.product._internal_name else order_product.name,
                    quantity=order_product.quantity,
                    product_id=order_product.product_id,
                    product_code=order_product.product.product_id,
                    thumbnail_url=order_product.product.thumbnail_media.url
                    if order_product.product.thumbnail_media else None,
                    attributes=[
                        schemas.CRMOrderAttributeSchema(
                            **{
                                **order_attribute.__dict__,
                                "attribute_code":
                                    order_attribute.attribute.attribute_id,
                                "price_impact": round(
                                    order_attribute.price_impact / 100, 2
                                )
                            }
                        )
                        for order_attribute in order_product.attributes
                    ],

                    incust_account=order_product.incust_account,
                    incust_card=order_product.incust_card,
                    charge_percent=order_product.charge_percent,
                    charge_fixed=order_product.charge_fixed,
                    is_topup_error=order_product.is_topup_error,
                    **order_product.converted_sums,
                )
                for order_product in order_products
            ],
            current_status=current_status,
            status_set_datetime=status_set_datetime,
            change_date=status_set_datetime,
            status_comment=status_comment,
            shipment_name=shipment_name,
            shipment_type=shipment.base_type,
            shipment=schemas.CRMOrderShipment(
                name=shipment_name,
                base_type=shipment.base_type,
                price=shipment.price,
                is_paid_separately=shipment.is_paid_separately,
                label_comment=shipment_label_comment,
                comment=shipment.comment,
                delivery_time_mode=shipment.delivery_datetime_mode,
            ),
            map_link=self.item.map_link,
            address_text=await get_address_text(
                self.item, store, shipment, include_link=False
            ),
            payment=schemas.CRMOrderPayment(
                name=payment_method_name,
                price=round(payment_raw_price / 100, 2) if payment_raw_price else 0,
                method=self.item.payment_method or "custom",  # type:ignore
                label_comment=payment_method_label_comment,
                comment=order_payment.comment if order_payment and
                                                 order_payment.comment else None,
            ),
            items_text=",".join(
                [f"{product.name} x{product.quantity}" for product in order_products]
            ),
            store_name=store.name,
            business_name=store.name,
            is_loyalty_rewards=bool(
                bonuses_added_amount or special_accounts_charges or
                emitted_coupons_batches
            ),
            bonuses_added_amount=bonuses_added_amount,
            special_accounts_charges=special_accounts_charges,
            emitted_coupons_batches=emitted_coupons_batches,
            status_history=status_history,
            profile_id=group.id,
            order_comment_label=label_comment,
        )

    def get_order(self):
        return self.order_to_schema()

    async def set_order_status(self, data: schemas.CRMChangeOrderStatusData):
        await change_store_order_status(
            self.item.id, data.status,
            "manager", data.comment,
            "crm", initiated_by_user=self.user,
            ignore_session_id=self.auth_session_id,
        )
        return await self.order_to_schema()
