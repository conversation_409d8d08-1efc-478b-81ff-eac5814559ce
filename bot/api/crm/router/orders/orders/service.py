from sqlalchemy.engine import Row

import schemas
from api.crm.base.list_service import CRMListService
from db import crud
from db.models import StoreOrder
from utils.text import fd


class OrdersService(CRMListService[schemas.CRMOrderListSchema]):
    schema_type = schemas.CRMOrderListSchema
    get_objects_func = crud.get_crm_order_list

    async def map_objects(self, objects: list[StoreOrder]):
        base_shipment_names = await fd(
            {
                "pickup": "store pickup text",
                "delivery": "store delivery text",
                "in_store": "store in store text",
                "no_delivery": "store no delivery text",
            }, self.lang
        )

        def map_order(data: Row):
            schema = schemas.CRMOrderListSchema.from_orm(data)
            if not schema.shipment_name:
                schema.shipment_name = base_shipment_names[schema.shipment_type]
            return schema

        return list(map(map_order, objects))

    async def get_next_cursor(
            self, objects: list,
            params: schemas.CRMOrderListParams,
    ):
        match params.sort:
            case schemas.CRMOrdersSort.ID:
                return schemas.IDCursor.obj(objects[-1])
            case schemas.CRMOrdersSort.DESIRED_DELIVERY_DATE:
                return schemas.DesiredDeliveryDateCursor.obj(objects[-1])
