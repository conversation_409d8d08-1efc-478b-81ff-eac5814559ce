from fastapi import APIRouter, Depends

import schemas
from .service import NotificationSettingsService

router = APIRouter(
    prefix="/notification_settings",
    tags=["notification_settings"]
)


@router.get("/")
async def get_notification_settings(
    params: schemas.CRMNotificationSettingsParams = Depends(),
    service: NotificationSettingsService = Depends()
) -> schemas.CRMNotificationSettingsResponse:
    return await service.get_notification_settings(params)


@router.get("/for_all_profiles")
async def get_notification_settings_for_all_profiles(
    service: NotificationSettingsService = Depends()
) -> schemas.CRMNotificationSettingsAllProfiles:
    return await service.get_notification_settings_all_profiles()


@router.patch("/")
async def update_notification_settings(
    data: list[schemas.CRMUpdateNotificationSettingsItem],
    service: NotificationSettingsService = Depends()
) -> schemas.OkResponse:
    return await service.update_notification_settings(data)


@router.patch("/for_all_profiles")
async def update_notification_settings_for_all_profiles(
    data: schemas.CRMUpdateNotificationSettingsForAllProfiles,
    service: NotificationSettingsService = Depends()
) -> schemas.OkResponse:
    return await service.update_notification_settings_for_all_profiles(data)
