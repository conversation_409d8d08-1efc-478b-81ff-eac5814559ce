import schemas
from schemas import IDCursor
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import MediaObject
from api.exceptions import APIListLimitError


class NotificationSettingsService:
    def __init__(
        self,
        scopes: ScopesCheckerService = ScopesCheckerService.depend()
    ):
        self.user = scopes.user
        self.lang = scopes.lang

    async def get_notification_settings(
        self, params: schemas.CRMNotificationSettingsParams
    ) -> schemas.CRMNotificationSettingsResponse:
        if not params.limit or params.limit > 100:
            raise APIListLimitError(1, 100, 10)

        data = await crud.get_profiles_notification_settings(self.user.id, 'CRM', params)

        if data:
            cursor = IDCursor.obj(data[-1])
            is_next_exists = await crud.get_profiles_notification_settings(
                self.user.id, "CRM", params, "exists", cursor
            )
            if not is_next_exists:
                cursor = None
        else:
            cursor = None

        async def get_item_schema(el):
            media_url = None
            if el.logo_media_id:
                media = await MediaObject.get(el.logo_media_id)
                if media:
                    media_url = media.url
            return schemas.NotificationSettingSchema(
                profile_id=el.id,
                name=el.name,
                logo_url=media_url,
                is_enabled=True if el.user_id != self.user.id else el.is_enabled,
            )

        return schemas.CRMNotificationSettingsResponse(
            data=[await get_item_schema(el) for el in data],
            next=cursor.to_str() if cursor else None,
        )

    async def get_notification_settings_all_profiles(self) -> schemas.CRMNotificationSettingsAllProfiles:
        is_all_disabled = await crud.are_all_notification_settings_disabled(self.user.id, "CRM")
        if is_all_disabled is True:
            is_all_disabled = True
        else:
            is_all_disabled = False

        return schemas.CRMNotificationSettingsAllProfiles(
            is_all_disabled=is_all_disabled
        )

    async def update_notification_settings(
        self, data: list[schemas.CRMUpdateNotificationSettingsItem]
    ) -> schemas.OkResponse:
        await crud.create_or_update_notification_settings(data, self.user.id, "CRM")
        return schemas.OkResponse()

    async def update_notification_settings_for_all_profiles(
        self, data: schemas.CRMUpdateNotificationSettingsForAllProfiles
    ) -> schemas.OkResponse:
        profiles = await crud.get_profiles_notification_settings(self.user.id, "CRM", for_all_profiles=True)
        to_update = []
        for profile in profiles:
            to_update.append(
                schemas.CRMUpdateNotificationSettingsItem(
                    profile_id=profile.id,
                    is_enabled=data.is_enabled
                )
            )

        await crud.create_or_update_notification_settings(to_update, self.user.id, "CRM")

        return schemas.OkResponse()
