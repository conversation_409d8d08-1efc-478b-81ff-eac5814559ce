import base64
import io
from abc import ABC, abstractmethod
from types import SimpleNamespace
from typing import Annotated, Optional

from fastapi import Depends
from jinja2 import Environment, FileSystemLoader, select_autoescape
from xhtml2pdf import pisa

import schemas
import schemas.crm.base
from core.api.depends import get_lang
from core.auth.depend import get_active_user_optional
from core.check.texts import get_payment_info
from db import crud
from db.models import (
    Brand, Group, MediaObject, Store,
    StoreCustomField, StoreOrder, StoreOrderPayment, User,
)
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from utils.text import f


class ReceiptService():
    def __init__(
            self,
            lang: Annotated[str, Depends(get_lang)],
            user: Annotated[User | None, Depends(get_active_user_optional)]
    ):
        self.lang: str = lang
        self.user: User | None = user

    async def print_receipt(
            self,
            object_id: int,
            params: schemas.CRMInvoicePrintReceiptParams
    ) -> schemas.CRMInvoicePrintReceiptResponseSchema:
        if params.data_type == schemas.PrintReceiptDataTypeEnum.order:
            builder = OrderReceiptBuilder(self.user.id, self.lang)
        elif params.data_type == schemas.PrintReceiptDataTypeEnum.invoice:
            builder = InvoiceReceiptBuilder(self.user.id, self.lang)
        else:
            raise ValueError(f"Unknown data type: {params.data_type}")

        receipt = await builder.build(object_id, params)

        html = self.build_receipt_html(
            receipt, params,
            is_pdf=(params.type == schemas.InvoicePrintReceiptTypeEnum.pdf),
            draw_checkboxes=params.draw_checkboxes,
            show_payed_sum=params.data_type == schemas.PrintReceiptDataTypeEnum.order
        )
        if params.type == schemas.InvoicePrintReceiptTypeEnum.html:
            return schemas.CRMInvoicePrintReceiptResponseSchema(
                type=params.type, data=html
            )

        pdf_bytes = self.html_to_pdf(html, params.format)
        data_uri = f"data:application/pdf;base64,{base64.b64encode(pdf_bytes).decode()}"
        return schemas.CRMInvoicePrintReceiptResponseSchema(
            type=params.type, data=data_uri
        )

    @staticmethod
    def build_receipt_html(
            data: schemas.CrmReceiptDataSchema,
            params: schemas.CRMInvoicePrintReceiptParams,
            is_pdf: bool = False,
            draw_checkboxes: bool = False,
            show_payed_sum: bool = True
    ) -> str:

        fmt = getattr(params.format, "value", params.format).lower()

        if fmt == "a4":
            width = "210mm"
        elif fmt == "custom" and params.custom_width:
            width = f"{params.custom_width}mm"
        elif fmt.endswith("mm"):
            width = f"{float(fmt[:-2])}mm"
        else:
            width = "210mm"

        env = Environment(
            loader=FileSystemLoader("templates"),
            autoescape=select_autoescape(["html"])
        )

        template = env.get_template("print_invoice_receipt.html")

        return template.render(
            **data.dict(), is_pdf=is_pdf, width=width, draw_checkboxes=draw_checkboxes,
            show_payed_sum=show_payed_sum,
        )

    @staticmethod
    def html_to_pdf(
            html: str, page_format: schemas.InvoicePrintReceiptFormatEnum
    ) -> bytes:
        """
        Transform HTML in PDF format using xhtml2pdf.
        - page_format: schemas.InvoicePrintReceiptFormatEnum
        """
        if page_format.value.lower().endswith("mm"):
            width_mm = float(page_format[:-2])
            style = (
                f"<style>"
                f"@page {{ size: {width_mm}mm auto; margin: 5mm 5mm; }}"
                f"body {{ margin:0; padding:0; }}"
                f"</style>"
            )
        else:
            print(f"Unknown page format: {page_format}. Using A4.")
            style = (
                "<style>"
                "@page { size: a4 portrait; margin: 10mm; }"
                "body { margin:0; padding:0; }"
                "</style>"
            )

        html_with_style = style + html
        buffer = io.BytesIO()
        result = pisa.CreatePDF(html_with_style, dest=buffer)
        if result.err:
            raise RuntimeError("Error generating PDF from HTML")
        return buffer.getvalue()


class ReceiptContextBuilder:
    def __init__(self, lang: str):
        self.lang = lang

    async def load_group_context(self, group_id: int) -> tuple[
        Group, Brand, Optional[str]]:
        group = await Group.get(group_id)
        brand = group.brand
        logo_url = None
        if brand.logo_media_id:
            m = await MediaObject.get(brand.logo_media_id)
            logo_url = m.url if m else None
        return group, brand, logo_url

    async def load_store_context(self, store_id: Optional[int]) -> tuple[
        Optional[Store], Optional[str], Optional[str], Optional[str]]:
        if not store_id:
            return None, None, None, None
        store = await Store.get(store_id)
        logo_url = None
        if store and store.media_id:
            sm = await MediaObject.get(store.media_id)
            logo_url = sm.url if sm else None
        address_data = await StoreCustomField.get_by_name(
            store.id, "address"
        ) if store else None
        address = address_data.value if address_data else None
        phone_data = await StoreCustomField.get_by_name(
            store.id, "phone_number"
        )
        phone = phone_data.value if phone_data else None
        return store, logo_url, address, phone

    async def build_localised_labels(
            self, group_lang: str
    ) -> schemas.CRMInvoiceReceiptContentLocalesSchema:
        keys = {
            "receipt_label": "print receipt receipt label",
            "address_label": "edit event address button",
            "phone_label": "print receipt phone label",
            "delivery_price_label": "web store make order delivery method cost header",
            "bonuses_and_discounts_label": "print receipt bonuses and discount "
                                           "redeemed label",
            "bonuses_label": "check bonuses redeemed text",
            "tips_label": "admin journals tips sum field name",
            "fees_label": "payer fee receipt list order text",
            "total_for_payment_label": "print receipt pay amount to pay text",
            "payed_sum_label": "web store sum paid label text",
            "position_label": "default invoice position name",
            "count_label": "admin quantity short label",
            "price_label": "web store orders table price text",
            "sum_label": "print receipt position sum label",
            "payment_method_label": "admin forms payment method field label",
            "footer_label_top": "print receipt footer top content",
            "footer_label_bottom": "print receipt footer bottom content",
        }
        translated = {attr: await f(msg, group_lang) for attr, msg in keys.items()}
        return schemas.CRMInvoiceReceiptContentLocalesSchema(**translated)


class AbstractReceiptBuilder(ABC):
    def __init__(self, user_id: int, lang: str):
        """
        :param user_id: ID user, that will be used for permissions check
        :param lang: language for localised labels and texts
        """
        self.user_id = user_id
        self.lang = lang

    @abstractmethod
    async def build(
            self,
            object_id: int,
            params: 'schemas.CRMInvoicePrintReceiptParams'
    ) -> 'schemas.CrmReceiptDataSchema':
        """
        Must be implemented in subclasses to build a receipt

        :param object_id: ID of the object (order or invoice) for which the receipt
        is being built
        :param params: parameters for receipt generation, such as format and type
        :return: schemas.CrmReceiptDataSchema
        """
        pass


class OrderReceiptBuilder(AbstractReceiptBuilder):
    def __init__(self, user_id: int, lang: str):
        super().__init__(user_id, lang)
        self.ctx_builder = ReceiptContextBuilder(lang)

    async def build(self, object_id: int, params):
        order = await StoreOrder.get(object_id)
        if not order:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "store_order:read", {"order_id": object_id}
            )

        group = await Group.get_by_store_id(order.store_id)

        group, brand, brand_logo_url = await self.ctx_builder.load_group_context(
            group.id
        )
        store, store_logo_url, address, phone = await (
            self.ctx_builder.load_store_context(
                order.store_id
            ))
        items = await crud.get_order_products(order.id, load_attributes=True) or []

        shipment = await crud.get_order_shipment(order.id)
        order_payment = await StoreOrderPayment.get(order_id=order.id)
        payment_name = None
        if shipment and order_payment:
            payment_name = \
                (await get_payment_info(shipment, order_payment, group, self.lang))[0]

        labels = await self.ctx_builder.build_localised_labels(group.lang)

        invoice_sum = SimpleNamespace(**order.converted_sums)
        if order and order.status_pay == "payed":
            invoice_sum.paid_sum = round(order.paid_sum / 100, 2)

        return schemas.CrmReceiptDataSchema(
            id=order.id,
            title=None,
            payment_mode=None,
            currency=order.currency,
            time_created=order.time_created,
            **invoice_sum.__dict__,
            shipment_cost=round(
                shipment.price / 100, 2
            ) if shipment.price else 0,
            total_sum_with_extra_fee=round(
                order.total_sum_with_extra_fee / 100, 2
            ) if order.total_sum_with_extra_fee else 0,
            group_info=schemas.CRMInvoiceReceiptGroupInfoSchema(
                name=group.name, phone=group.phone, domain=brand.domain,
                logo_url=brand_logo_url
            ),
            store_info=schemas.CRMInvoiceReceiptStoreInfoSchema(
                name=store.name if store else None,
                address=address,
                phone=phone,
                logo_url=store_logo_url
            ) if store else None,
            items=[schemas.CRMReceiptItemSchema(
                id=x.id,
                name=x.name,
                quantity=x.quantity,
                price=round(x.price / 100, 2),
                unit_discount=round(x.discount_amount / 100, 2),
                unit_bonuses_redeemed=round(x.bonuses_redeemed / 100, 2),
                unit_discount_and_bonuses_redeemed=round(
                    x.discount_and_bonuses_sum / 100, 2
                ),
                final_price=round(x.final_price / 100, 2),
                before_loyalty_sum=round(x.before_loyalty_sum / 100, 2),
                discount=round(x.discount_sum / 100, 2),
                bonuses_redeemed=round(x.bonuses_redeemed_sum / 100, 2),
                discount_and_bonuses_redeemed=round(
                    x.discount_and_bonuses / 100, 2
                ),
                final_sum=round(x.total_sum / 100, 2),
                attributes=[
                    schemas.CRMReceiptItemAttributeSchema(
                        name=attr.name,
                        price=round(float(attr.price_impact) / 100, 2),
                        quantity=attr.quantity if attr.quantity else 1,
                    ) for attr in x.attributes
                ] if x.attributes else None
            ) for x in items],
            localised_labels=labels,
            payment_method=payment_name,
        )


class InvoiceReceiptBuilder(AbstractReceiptBuilder):
    def __init__(self, user_id: int, lang: str):
        super().__init__(user_id, lang)
        self.ctx_builder = ReceiptContextBuilder(lang)

    async def build(
            self,
            object_id: int,
            params: schemas.CRMInvoicePrintReceiptParams
    ) -> schemas.CrmReceiptDataSchema:
        invoice = await crud.get_invoice(object_id, "crm_invoice:read", self.user_id)
        if not invoice:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "crm_invoice:read", {"invoice_id": object_id}
            )

        group, brand, brand_logo_url = await self.ctx_builder.load_group_context(
            invoice.group_id
        )
        store = None
        store_logo_url = store_address = store_phone = None
        if invoice.store_order and invoice.store_order.store_id:
            store, store_logo_url, store_address, store_phone = await (
                self.ctx_builder.load_store_context(
                    invoice.store_order.store_id
                ))

        items = await crud.get_invoice_items(invoice.id) or []

        order = await StoreOrder.get(
            invoice.store_order
        ) if invoice.store_order else None
        invoice_sum = SimpleNamespace(**invoice.converted_sums)

        if order and order.status_pay == "payed":
            invoice_sum.paid_sum = round(order.paid_sum / 100, 2)

        shipment = await crud.get_order_shipment(order.id) if order else None
        order_payment = await StoreOrderPayment.get(
            order_id=order.id
        ) if order else None
        payment_name = None
        if shipment and order_payment:
            payment_name = \
                (await get_payment_info(shipment, order_payment, group, self.lang))[0]

        labels = await self.ctx_builder.build_localised_labels(group.lang)

        return schemas.CrmReceiptDataSchema(
            id=invoice.id,
            title=invoice.title or None,
            payment_mode=invoice.payment_mode or None,
            currency=invoice.currency,
            time_created=invoice.time_created,
            **invoice_sum.__dict__,
            group_info=schemas.CRMInvoiceReceiptGroupInfoSchema(
                name=group.name,
                phone=group.phone,
                domain=brand.domain,
                logo_url=brand_logo_url
            ),
            store_info=schemas.CRMInvoiceReceiptStoreInfoSchema(
                name=store.name if store else None,
                address=store_address,
                phone=store_phone,
                logo_url=store_logo_url
            ) if store else None,
            items=[x.from_orm_converted(schemas.CRMReceiptItemSchema) for x in items],
            localised_labels=labels,
            payment_method=payment_name,
        )
