from fastapi import APIRouter, Depends, Security

import schemas
from api.crm.router.receipt.service import ReceiptService

router = APIRouter(
    prefix="/receipt",
    tags=["receipt"],
)


@router.post("/print/{object_id}")
async def print_receipt(
        object_id: int,
        params: schemas.CRMInvoicePrintReceiptParams = Depends(),
        service: ReceiptService = Security(),
) -> schemas.CRMInvoicePrintReceiptResponseSchema:
    return await service.print_receipt(object_id, params)
