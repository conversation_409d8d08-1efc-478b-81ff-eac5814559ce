import hashlib
import json

from fastapi import Depends, Path

import schemas
from api.crm.base.item_service import CRMItemService
from api.exceptions import MediaNotFoundError
from core.auth.depend import Action, get_active_user
from core.chat.chat_message_sender import ChatMessageSender
from core.media_manager import media_manager
from db import crud
from db.models import (
    Chat, ChatMessage, ClientBot, Group, MediaObject, User,
    UserClientBotActivity, WATemplate,
)
from db.models.bot.reply_buttons import ReplyButton
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from utils.media import make_preview_url


class ChatService(CRMItemService[Chat]):
    @staticmethod
    async def get_item(
            chat_id: int = Path(ge=1),
            action: str = Action(
                "crm_chat:read",
                ("crm_chat:read", "crm_chat:edit"),
            ),
            user: User = Depends(get_active_user)
    ):
        if not (chat := await crud.get_chat(chat_id, action, user.id)):
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                action,
                {"chat_id": chat_id},
            )
        return chat

    async def chat_to_schema(self):
        chat = self.item
        base_schema = schemas.CRMBaseChatSchema.from_orm(self.item)

        group = await Group.get(chat.group_id)
        bot = await ClientBot.get(chat.bot_id)

        chat_user = await User.get_by_id(chat.user_id)

        user_bot_activity = await UserClientBotActivity.get(chat_user, bot, chat_user)

        last_message = await ChatMessage.get(
            chat_id=chat.id,
            is_last=True,
        )

        if last_message:
            last_message_content_type = last_message.content_type
            last_message_text = last_message.text
            last_message_content = last_message.content
            if last_message.media_id:
                media = await MediaObject.get(last_message.media_id)
                last_message_media_url = media.url
                last_message_media_mime_type = media.mime_type
            else:
                last_message_media_url = None
                last_message_media_mime_type = None
        else:
            last_message_content_type = None
            last_message_text = None
            last_message_content = None
            last_message_media_url = None
            last_message_media_mime_type = None

        return schemas.CRMChatSchema(
            **base_schema.dict(),
            crm_tag=chat.crm_tag,
            profile_id=group.id,
            profile_name=group.name,
            business_name=group.name,
            bot_name=bot.display_name if bot else None,
            first_name=chat_user.first_name,
            last_name=chat_user.last_name,
            full_name=chat_user.full_name,
            email=chat_user.email,
            photo_url=chat_user.photo_url,
            items_text=last_message_text or "",
            last_user_bot_activity=user_bot_activity.last_activity if
            user_bot_activity else None,
            is_whatsapp=True if chat_user.wa_phone else False,
            last_message_text=last_message_text,
            last_message_content_type=last_message_content_type,
            last_message_media_url=last_message_media_url,
            last_message_media_mime_type=last_message_media_mime_type,
            last_message_content=last_message_content,
        )

    async def get_chat_message_history(self, params: schemas.ChatMessageHistoryParams):
        data = await crud.get_chat_message_history(
            self.item.id, params.cursor, params.limit
        )

        if data:
            next_cursor = schemas.IDCursor.obj(data[-1])
            is_next_exists = await crud.get_chat_message_history(
                self.item.id, next_cursor, operation="exists"
            )
            if not is_next_exists:
                next_cursor = None
        else:
            next_cursor = None

        res = schemas.ListCursorResponse[schemas.ChatMessageSchema](
            data=data,
            next=next_cursor.to_str() if next_cursor else None,
        )
        return res

    @staticmethod
    def get_hash_from_dict(d: dict) -> str:
        json_string = json.dumps(d, sort_keys=True)
        return hashlib.sha256(json_string.encode("utf-8")).hexdigest()

    async def process_chat_wa_template(
            self,
            wa_master_template_id: int | None,
            wa_template_variables: str | None,
            wa_reply_buttons_data: str | None, data: schemas.SendChatMessageData
    ):
        if wa_master_template_id:
            template = await WATemplate.get(wa_master_template_id)
            if template:
                header_media_object = await WATemplate.get_header_media(template)
                if header_media_object:
                    data.media_id = header_media_object.id
                else:
                    data.media_id = None

        if wa_template_variables:
            try:
                data.wa_template_variables = json.loads(wa_template_variables)
            except json.JSONDecodeError:
                data.wa_template_variables = None

        if wa_reply_buttons_data:
            try:
                wa_reply_buttons_list = json.loads(wa_reply_buttons_data)
                if isinstance(wa_reply_buttons_list, list):
                    processed_reply_buttons = []
                    created_buttons = []

                    params = schemas.TemplateReplyButtonParams(
                        manager_id=self.user.id,
                        chat_id=self.item.id
                    )

                    for reply_button in wa_reply_buttons_list:
                        create_data = {k: v for k, v in reply_button.items() if
                                       k != "text"}

                        create_data['params'] = params.dict()
                        create_data['content']['original_text'] = reply_button.get(
                            "text"
                        )

                        hash_value = self.get_hash_from_dict(create_data)

                        existing_button = await ReplyButton.get(hash=hash_value)

                        if existing_button:
                            reply_button_instance = existing_button
                        else:
                            reply_button_instance = await ReplyButton.create(
                                **create_data, hash=hash_value
                            )

                        created_buttons.append(reply_button_instance)
                        processed_reply_buttons.append(
                            {
                                "text": reply_button.get("text"),
                                "payload": str(reply_button_instance.id),
                            }
                        )

                    data.wa_reply_buttons_data = processed_reply_buttons
                else:
                    data.wa_reply_buttons_data = None
            except json.JSONDecodeError:
                data.wa_reply_buttons_data = None

        return data

    async def send_message(self, data: schemas.SendChatMessageData):
        data = await self.process_chat_wa_template(
            data.wa_master_template_id, data.wa_template_variables,
            data.wa_reply_buttons_data, data
        )

        if data.media_id:
            media = await MediaObject.get(data.media_id)
            if not media:
                raise MediaNotFoundError(data.media_id)
        elif data.media_url:
            media = await media_manager.download_media(
                data.media_url,
            )
        elif data.media_file:
            media = await media_manager.save_from_upload_file(
                data.media_file,
            )
        else:
            media = None

        sender = ChatMessageSender(
            self.item,
            data.content_type,
            data.text,
            media,
            sent_by=schemas.ChatMessageSentByEnum.MANAGER,
            sent_by_user_id=self.user.id,
            crm_ignore_session_id=self.auth_session_id,
            wa_master_template_id=data.wa_master_template_id,
            wa_template_variables=data.wa_template_variables,
            wa_reply_buttons_data=data.wa_reply_buttons_data,
        )
        message = await sender.send()

        sent_by_user_name = None
        sent_by_user_email = None
        sent_by_user_photo_url = None
        vm_name = None

        if message.sent_by in (
                schemas.ChatMessageSentByEnum.USER,
                schemas.ChatMessageSentByEnum.MANAGER,
        ) and message.sent_by_user_id:
            message_user: User = await User.get_by_id(message.sent_by_user_id)
            sent_by_name = message_user.name
            sent_by_user_name = message_user.name
            sent_by_user_email = message_user.email
            sent_by_user_photo_url = message_user.photo_url
        elif message.sent_by == schemas.ChatMessageSentByEnum.VM:
            message_vm = await crud.get_vm_by_vmc(message.vmc_id)
            sent_by_name = message_vm.name
            vm_name = message_vm.name
        else:
            group = await Group.get(self.item.group_id)
            sent_by_name = group.name

        if sender.media:
            media_url = sender.media.url
            media_mime_type = sender.media.mime_type
            media_file_size = sender.media.file_size
            media_original_file_name = sender.media.original_file_name
            media_preview_url = make_preview_url(
                media_mime_type, sender.media.file_path
            )
        else:
            media_url = None
            media_mime_type = None
            media_file_size = None
            media_original_file_name = None
            media_preview_url = None

        return schemas.ChatMessageSchema(
            id=message.id,
            chat_id=message.chat_id,
            sent_by=message.sent_by,
            sent_by_name=sent_by_name,
            sent_by_user_id=message.sent_by_user_id,
            sent_by_user_name=sent_by_user_name,
            sent_by_user_email=sent_by_user_email,
            sent_by_user_photo_url=sent_by_user_photo_url,
            vmc_id=message.vmc_id,
            vm_name=vm_name,
            content_type=message.content_type,
            text=message.text,
            media_id=message.media_id,
            media_url=media_url,
            media_mime_type=media_mime_type,
            media_file_size=media_file_size,
            media_preview_url=media_preview_url,
            media_original_file_name=media_original_file_name,
            content=message.content,
            is_mailing=message.is_mailing,
            time_created=message.time_created,
        )
