from fastapi import APIRouter, Depends

import schemas
from .service import ChatService

router = APIRouter(
    prefix="/{chat_id}",
)


@router.get("/")
async def get_chat(
        service: ChatService = Depends()
) -> schemas.CRMChatSchema:
    return await service.chat_to_schema()


@router.get("/messages")
async def get_chat_message_history(
        params: schemas.ChatMessageHistoryParams = Depends(),
        service: ChatService = Depends(),
) -> schemas.ChatMessagesResponse:
    return await service.get_chat_message_history(params)


@router.post("/messages")
async def send_message(
        data: schemas.SendChatMessageData = Depends(),
        service: ChatService = Depends(),
) -> schemas.ChatMessageSchema:
    return await service.send_message(data)
