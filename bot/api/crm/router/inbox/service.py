import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud


class InboxService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend()
    ):
        self.user = scopes.user
        self.lang = scopes.lang

    async def get_inbox(self, params: schemas.InboxParams):
        data = await crud.get_crm_inbox(self.user.id, params)

        if data:
            cursor = schemas.InboxCursor.obj(data[-1])
            is_next_exists = await crud.get_crm_inbox(
                self.user.id, params, "exists", cursor
            )
            if not is_next_exists:
                cursor = None
        else:
            cursor = None

        def get_item_schema(el):
            match el.inbox_type:
                case "order":
                    return schemas.CRMInboxOrderSchema(
                        id=el.id,
                        type="order",
                        status=el.inbox_status,
                        item=schemas.CRMOrderListSchema.from_orm(el),
                        change_date=el.change_date,
                    )
                case "invoice":
                    return schemas.CRMInboxInvoiceSchema(
                        id=el.id,
                        type="invoice",
                        status=el.inbox_status,
                        item=schemas.CRMInvoiceListSchema.from_orm(el),
                        change_date=el.change_date,
                    )
                case "ticket":
                    return schemas.CRMInboxTicketSchema(
                        id=el.id,
                        type="ticket",
                        status=el.inbox_status,
                        item=schemas.CRMTicketListSchema.from_orm(el),
                        change_date=el.change_date,
                    )
                case "review":
                    return schemas.CRMInboxReviewSchema(
                        id=el.id,
                        type="review",
                        status=el.inbox_status,
                        item=schemas.CRMReviewSchema.from_orm(el),
                        change_date=el.change_date,
                    )
                case "chat":
                    return schemas.CRMInboxChatSchema(
                        id=el.id,
                        type="chat",
                        status=el.inbox_status,
                        item=schemas.CRMChatSchema.from_orm(el),
                        change_date=el.change_date,
                    )
                case "text_notification":
                    return schemas.CRMInboxNotificationSchema(
                        id=el.id,
                        type="text_notification",
                        status=el.inbox_status,
                        item=schemas.CRMTextNotificationSchema.from_orm(el),
                        change_date=el.change_date,
                    )
                case "ewallet_ext_payment":
                    return schemas.CRMInboxEwalletExtPaymentSchema(
                        id=el.id,
                        type="ewallet_ext_payment",
                        status=el.inbox_status,
                        item=schemas.CRMEwalletExtPaymentListSchema.from_orm(el),
                        change_date=el.change_date,
                    )

        return schemas.CRMInboxResponse(
            data=list(map(get_item_schema, data)),
            next=cursor.to_str() if cursor else None,
        )

    async def get_inbox_exists(self, params: schemas.InboxParams):
        is_exists = await crud.get_crm_inbox(self.user.id, params)
        return schemas.ExistsResponse(
            is_exists=bool(is_exists),
        )
