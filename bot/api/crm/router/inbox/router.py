from fastapi import APIRouter, Depends

import schemas
from .service import InboxService

router = APIRouter(
    prefix="/inbox",
    tags=["inbox"]
)


@router.get("/")
async def get_inbox(
        params: schemas.InboxParams = Depends(),
        service: InboxService = Depends()
) -> schemas.CRMInboxResponse:
    return await service.get_inbox(params)


@router.get("/exists")
async def get_inbox_exists(
        params: schemas.InboxParams = Depends(),
        service: InboxService = Depends()
) -> schemas.ExistsResponse:
    return await service.get_inbox_exists(params)
