import schemas
from db import crud
from schemas import CursorDirection
from ...base.list_service import CRMListService


class FiltersService(CRMListService[schemas.CRMFilterObj]):
    schema_type = schemas.CRMFilterObj
    get_objects_func = crud.get_crm_filters

    async def get_next_cursor(self, objects: list[schemas.CRMFilterObj]):
        last_obj = objects[-1]
        return schemas.CRMFiltersCursor(
            direction=CursorDirection.NEXT,
            profile_id=last_obj.profile_id,
            store_id=last_obj.store_id,
            ticket_id=last_obj.ticket_id,
        )
