from datetime import datetime

from fastapi import Depends, Path

import schemas
from api.crm.base.item_service import CRMItemService
from core.auth.depend import Action, get_active_user
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from core.review.notifications import send_review_push_notifications
from db import crud
from db.models import ClientBot, Group, Review, User


class ReviewService(CRMItemService[Review]):
    @staticmethod
    async def get_item(
            review_id: int = Path(ge=1),
            action: str = Action(
                "crm_review:read",
                ("crm_review:read", "crm_review:edit"),
            ),
            user: User = Depends(get_active_user)
    ):
        if not (review := await crud.get_review(review_id, action, user.id)):
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                action,
                {"review_id": review_id},
            )
        return review

    async def review_to_schema(self):
        review = self.item
        base_schema = schemas.CRMBaseReviewSchema.from_orm(self.item)

        group = await Group.get(review.group_id)
        bot = await ClientBot.get(review.bot_id)

        review_user = await User.get_by_id(review.user_id)

        return schemas.CRMReviewSchema(
            **base_schema.dict(),
            crm_tag=review.crm_tag,
            profile_id=group.id,
            profile_name=group.name,
            business_name=group.name,
            bot_name=bot.display_name if bot else None,
            first_name=review_user.first_name,
            last_name=review_user.last_name,
            full_name=review_user.full_name,
            email=review_user.email,
            photo_url=review_user.photo_url,
            items_text=review.mark,
            change_date=review.change_date,
            menu_in_store_comment=review.additional_text,
        )

    async def read(self, data: schemas.CRMReadData):
        await self.item.update(
            is_read=data.read,
            read_by_user_id=self.user.id if data.read else None,
            read_status_change_datetime=datetime.utcnow()
        )
        await send_review_push_notifications(
            self.item, ignore_session_id=self.auth_session_id
        )
        return await self.review_to_schema()
