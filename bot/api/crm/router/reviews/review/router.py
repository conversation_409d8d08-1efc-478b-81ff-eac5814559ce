from fastapi import APIRouter, Depends, Security

import schemas
from .service import ReviewService

router = APIRouter(
    prefix="/{review_id}",
)


@router.get("/")
async def get_review(
        service: ReviewService = Depends()
) -> schemas.CRMReviewSchema:
    return await service.review_to_schema()


@router.post("/read")
async def read_review(
        data: schemas.CRMReadData,
        service: ReviewService = Security(scopes=["me:write", "crm_review:edit"]),
) -> schemas.CRMReviewSchema:
    return await service.read(data)
