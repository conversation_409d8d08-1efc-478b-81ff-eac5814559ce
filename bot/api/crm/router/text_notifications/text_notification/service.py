from datetime import datetime

from fastapi import Depends, Path

import schemas
from api.crm.base.item_service import CRMItemService
from core.auth.depend import Action, get_active_user
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from core.text_notification.notifications import send_text_notification
from db import crud
from db.models import ClientBot, Group, MenuInStore, TextNotification, User


class TextNotificationService(CRMItemService[TextNotification]):
    @staticmethod
    async def get_item(
            text_notification_id: int = Path(ge=1),
            action: str = Action(
                "crm_text_notification:read",
                ("crm_text_notification:read", "crm_text_notification:edit"),
            ),
            user: User = Depends(get_active_user)
    ):
        if not (text_notification := await crud.get_text_notification(
                text_notification_id, action, user.id
        )):
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                action,
                {"text_notification_id": text_notification_id},
            )
        return text_notification

    async def text_notification_to_schema(self):
        text_notification = self.item
        base_schema = schemas.BaseCRMTextNotificationSchema.from_orm(self.item)

        group = await Group.get(text_notification.profile_id)
        bot = await ClientBot.get(text_notification.from_bot_id)
        menu_in_store = await MenuInStore.get(text_notification.menu_in_store_id)

        text_notification_user: User = await User.get_by_id(
            text_notification.from_user_id
        )

        user_data = {
            "first_name": text_notification_user.first_name,
            "last_name": text_notification_user.last_name,
            "full_name": text_notification_user.name,
            "email": text_notification_user.email,
            "photo_url": text_notification_user.photo_url,
        } if text_notification_user else {}

        return schemas.CRMTextNotificationSchema(
            **base_schema.dict(),
            crm_tag=text_notification.crm_tag,
            profile_name=group.name,
            business_name=group.name,
            bot_name=bot.display_name if bot else None,
            **user_data,
            items_text=text_notification.text,
            change_date=text_notification.change_date,
            menu_in_store_comment=menu_in_store.comment if menu_in_store else None,
        )

    async def read(self, data: schemas.CRMReadData):
        await self.item.update(
            is_read=data.read,
            read_by_user_id=self.user.id if data.read else None,
            read_status_change_datetime=datetime.utcnow()
        )
        await send_text_notification(self.item, ignore_session_id=self.auth_session_id)
        return await self.text_notification_to_schema()
