from fastapi import APIRouter, Depends, Security

import schemas
from .service import TextNotificationService

router = APIRouter(
    prefix="/{text_notification_id}",
)


@router.get("/")
async def get_text_notification(
        service: TextNotificationService = Depends()
) -> schemas.CRMTextNotificationSchema:
    return await service.text_notification_to_schema()


@router.post("/read")
async def read_text_notification(
        data: schemas.CRMReadData,
        service: TextNotificationService = Security(
            scopes=["me:write", "crm_text_notification:edit"]
        ),
) -> schemas.CRMTextNotificationSchema:
    return await service.read(data)
