from dataclasses import asdict

import schemas
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.helpers import send_tg_user_link
from db import crud
from db.models import ClientBot, User
from schemas import ChatType<PERSON>num
from utils.media import make_preview_url


class UserService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "crm_user:read", "profile_id", "user_id",
            )
    ):
        self.user = scopes.user
        self.lang = scopes.lang
        self.profile_id = scopes.data.profile_id
        self.profile_user_id = scopes.data.user_id
        self.available_data = asdict(scopes.data)

    async def get_profile_user(self):
        profile_user: User = await User.get_by_id(self.profile_user_id)
        if not profile_user:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "crm_user:read", self.available_data
            )
        return profile_user

    async def get_user_schema(self):
        profile_user = await self.get_profile_user()

        can_chat = False
        chat_id = None

        bot = await ClientBot.get(group_id=self.profile_id)
        if bot and bot.bot_type in profile_user.messangers:
            can_chat = True
            chat = await crud.get_or_create_chat(
                ChatTypeEnum.USER_WITH_GROUP,
                profile_user.id,
                self.profile_id,
                bot.id,
            )
            chat_id = chat.id

        if await crud.check_access_to_action(
                "customer_profiles:read", "profile", self.profile_id
        ):
            owned_profiles = [
                schemas.CRMUserOwnedProfile(
                    id=profile.id,
                    name=profile.name,
                    logo_url=media.url if media else None,
                    logo_thumbnail_url=make_preview_url(
                        media.media_type, media.file_path,
                        max_size=128
                    ) if media else None
                )
                for profile, media in
                await crud.get_user_owned_profiles(self.profile_user_id)
            ]
        else:
            owned_profiles = None

        return schemas.CRMUser(
            profile_id=self.profile_id,
            info=schemas.CRMUserInfo.from_orm(profile_user),
            custom_fields=await crud.get_custom_fields_for_user(
                self.profile_id, self.profile_user_id
            ),
            tags=await crud.get_user_tag_names(self.profile_id, self.profile_user_id),
            chat_id=chat_id,
            can_chat=can_chat,
            owned_profiles=owned_profiles,
        )

    async def send_tg_user_link(self):
        profile_user = await self.get_profile_user()
        bot = await send_tg_user_link(
            self.user, profile_user, self.lang
        )
        return schemas.CRMUserTgLinkSent(sent_to_bot=bot.username)
