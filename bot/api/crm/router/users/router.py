from fastapi import APIRouter, Depends

import schemas
from api.crm.router.users.service import UserService

router = APIRouter(
    prefix="/users/{profile_id}",
    tags=["users"]
)


@router.get("/{user_id}")
async def get_profile_user(
        service: UserService = Depends()
) -> schemas.CRMUser:
    return await service.get_user_schema()


@router.post("/{user_id}/send-tg-user-link")
async def send_tg_user_link(
        service: UserService = Depends(),
) -> schemas.CRMUserTgLinkSent:
    return await service.send_tg_user_link()
