from datetime import datetime

from fastapi import Depends, Path

import schemas
import schemas.crm.base
from api.crm.base.item_service import CRMItemService
from core.auth.depend import Action, get_active_user
from core.invoice.notifications import send_invoice_push_notifications
from core.user.functions import not_anonym
from db import crud
from db.models import (
    ClientBot, Group, Invoice, MenuInStore, User,
)
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class InvoiceService(CRMItemService[Invoice]):
    @staticmethod
    async def get_item(
            invoice_id: int = Path(ge=1),
            action: str = Action(
                "crm_invoice:read",
                ("crm_invoice:read", "crm_invoice:edit"),
            ),
            user: User = Depends(get_active_user)
    ):
        if not (invoice := await crud.get_invoice(invoice_id, action, user.id)):
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                action,
                {"invoice_id": invoice_id},
            )
        return invoice

    async def invoice_to_schema(self):
        invoice = self.item
        base_schema = invoice.from_orm_converted(schemas.CRMInvoiceSchemaORMAttributes)

        group = await Group.get(invoice.group_id)
        bot = await ClientBot.get(invoice.bot_id)
        menu_in_store = await MenuInStore.get(invoice.menu_in_store_id)
        invoice_user = await User.get_by_id(self.item.id)

        if invoice.loyalty_settings_id:
            bonuses_added_amount = invoice.bonuses_added_amount
            special_accounts_charges = invoice.special_accounts_charges
            emitted_coupons_batches = [coupon.batch for coupon in
                                       (invoice.emitted_coupons or []) if
                                       coupon.batch]
        else:
            bonuses_added_amount = None
            special_accounts_charges = None
            emitted_coupons_batches = None

        items = await crud.get_invoice_items(invoice.id)

        return schemas.CRMInvoiceSchema(
            **base_schema.dict(),
            photo_url=invoice_user.photo_url if invoice_user else None,
            status_pay=invoice.status,
            description=invoice.description,
            creator_id=not_anonym(invoice.creator_id),
            payer_id=not_anonym(invoice.payer_id),
            bot_id=invoice.bot_id,
            check_url=invoice.check_url,
            external_transaction_id=invoice.external_transaction_id,
            is_friend=invoice.is_friend,
            user_comment=invoice.user_comment,
            expiration_date=invoice.expiration_datetime,
            live_time=invoice.live_time,
            is_loyalty_rewards=bool(
                bonuses_added_amount or special_accounts_charges or
                emitted_coupons_batches
            ),
            bonuses_added_amount=bonuses_added_amount,
            special_accounts_charges=special_accounts_charges,
            emitted_coupons_batches=emitted_coupons_batches,
            items=list(
                map(lambda x: x.from_orm_converted(schemas.InvoiceItemSchema), items)
            ),
            profile_id=group.id,
            profile_name=group.name,
            business_name=group.name,
            bot_name=bot.display_name if bot else None,
            menu_in_store_comment=menu_in_store.comment if menu_in_store else None,
            items_text=",".join([f"{item.name} x{item.quantity}" for item in items]),
            change_date=invoice.change_date,
            comment=invoice.user_comment,
        )

    async def read(self, data: schemas.CRMReadData):
        await self.item.update(
            is_read=data.read,
            read_by_user_id=self.user.id if data.read else None,
            read_status_change_datetime=datetime.utcnow()
        )
        await send_invoice_push_notifications(
            self.item, ignore_session_id=self.auth_session_id
        )
        return await self.invoice_to_schema()
