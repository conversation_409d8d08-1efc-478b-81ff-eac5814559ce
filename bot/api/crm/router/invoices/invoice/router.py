from fastapi import APIRouter, Depends, Security

import schemas.crm.base
from .service import InvoiceService

router = APIRouter(
    prefix="/{invoice_id}",
)


@router.get("/")
async def get_invoice(
        service: InvoiceService = Depends()
) -> schemas.CRMInvoiceSchema:
    return await service.invoice_to_schema()


@router.post("/read")
async def read_invoice(
        data: schemas.CRMReadData,
        service: InvoiceService = Security(scopes=["me:write", "crm_invoice:edit"]),
) -> schemas.CRMInvoiceSchema:
    return await service.read(data)
