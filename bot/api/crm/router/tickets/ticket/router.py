from fastapi import APIRouter, Depends, Security

import schemas
from .service import TicketService

router = APIRouter(
    prefix="/{ticket_id}",
)


@router.get("/")
async def get_ticket(
        service: TicketService = Depends()
) -> schemas.CRMTicketSchema:
    return await service.ticket_to_schema()


@router.post("/set-status")
async def set_ticket_status(
        data: schemas.CRMChangeTicketStatusData,
        service: TicketService = Security(scopes=["me:write", "crm_ticket:edit"]),
) -> schemas.CRMTicketSchema:
    return await service.set_ticket_status(data)
