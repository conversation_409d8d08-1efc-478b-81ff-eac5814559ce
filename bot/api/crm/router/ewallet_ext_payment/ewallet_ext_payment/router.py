from fastapi import <PERSON>Rout<PERSON>, Depends, Security

import schemas
from .service import EwalletExtPaymentService

router = APIRouter(
    prefix="/{ewallet_ext_payment_id}"
)


@router.get("/")
async def get_ewallet_ext_payment(
        service: EwalletExtPaymentService = Depends()
) -> schemas.CRMEwalletExtPaymentSchema:
    return await service.to_schema()


@router.post("/status/{new_status}")
async def set_ewallet_ext_payment_status(
        new_status: schemas.EWalletExternalPaymentStatus,
        service: EwalletExtPaymentService = Security(
            scopes=["crm_ewallet_ext_payment:edit"]
        )
) -> schemas.CRMEwalletExtPaymentSchema:
    await service.set_status(new_status)
    return await service.to_schema()
