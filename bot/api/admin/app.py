from fastapi import Depends
from psutils.fastapi.api_route_error_groups import APIRouteErrorGroups

from core.api.depends import get_header_lang
from .router import router
from ..custom_fastapi import CustomFastApi as FastAPI
from ..docs_routes import register_app_docs_routes
from ..exception_handlers import register_general_exception_handlers

app = FastAPI(
    title="7Loc Admin API",
    docs_url=None,
    redoc_url=None,
    dependencies=[Depends(get_header_lang)]
)
# noinspection PyUnresolvedReferences
app.router.route_class = APIRouteErrorGroups

register_general_exception_handlers(app)
register_app_docs_routes(app)

app.include_router(router)
