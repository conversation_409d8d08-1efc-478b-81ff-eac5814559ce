from typing import Type

from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.orm import DeclarativeMeta
from sqlalchemy.sql import Select

from api.admin.exceptions import AdminFieldNotUnique, AdminFieldNotUniqueEdit
from db import db_func, sess
from db.models import Brand, Group, StoreAttribute, StoreProduct


class ValidateObjectSchema(BaseModel):
    id: int
    code: str


async def validate_object_id(
        model: Type[DeclarativeMeta], profile_id, object_id: str, id: int | None = None
) -> bool:

    objects = await get_object_by_object_id_and_profile_id(model, object_id, profile_id)
    err = 0
    if objects:
        for obj in objects:
            if not id:
                raise AdminFieldNotUnique(
                    "product_id", object_id,
                )
            if obj.id == id:
                return True
            elif obj.id != id:
                err += 1

        if err:
            raise AdminFieldNotUniqueEdit(
                "product_id", object_id, ""
                # _object.code
            )

    return True


@db_func
def get_object_by_object_id_and_profile_id(
        model: Type[DeclarativeMeta],
        object_id: str, profile_id: int
) -> list[ValidateObjectSchema] | None:

    if not hasattr(model, 'brand'):
        raise Exception("Model does not have 'brand' attribute")

    stmt: Select

    if model == StoreProduct:
        stmt = select(StoreProduct.id, StoreProduct.product_id.label('code'))
        stmt = stmt.where(StoreProduct.product_id == object_id)
    elif model == StoreAttribute:
        stmt = select(StoreAttribute.id, StoreAttribute.attribute_id.label('code'))
        stmt = stmt.where(StoreAttribute.attribute_id == object_id)
    else:
        raise Exception("Model not supported")

    stmt = stmt.join(model.brand)
    stmt = stmt.join(Brand.group)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)

    if hasattr(model, 'is_deleted'):
        stmt = stmt.where(model.is_deleted.is_(False))

    return [
        ValidateObjectSchema(
            id=row.id,
            code=row.code
        ) for row in
        sess().execute(stmt).all()
    ]
