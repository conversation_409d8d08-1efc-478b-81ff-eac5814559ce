from starlette import status

from exceptions import ObjectNotFoundError
from utils.exceptions import ErrorWithHTTPStatus


class AdminFieldCannotBeEmptyError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin field can not be empty error"

    def __init__(self, field_name: str):
        super().__init__(
            field_name=field_name,
            detail_data={
                "error_code": "field_cannot_be_empty",
                "field_name": field_name,
            }
        )


class AdminTranslationSpecifiedForEmptyFieldError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin translation specified for empty field error"

    def __init__(self, field_name: str):
        super().__init__(
            field_name=field_name,
            detail_data={
                "error_code": "translation_specified_for_empty_field",
                "field_name": field_name,
            }
        )


class AdminProductFloatingSumOptionsPatternError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "store import floating price invalid pattern value error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            product_id=product_id,
            product_name=product_name,
            detail_data={
                "error_code": "product_floating_sum_options_pattern_error",
            }
        )


class AdminProductFloatingSumOptionValueError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "store import floating price unknown value error"

    def __init__(self, product_id: str, product_name: str):
        super().__init__(
            product_id=product_id,
            product_name=product_name,
            detail_data={
                "error_code": "product_floating_sum_option_value_error",
            }
        )


class AdminFieldNotUnique(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin field not unique error"

    def __init__(self, field_name: str, value: str):
        super().__init__(
            field_name=field_name,
            value=value,
            detail_data={
                "error_code": "field_not_unique",
                "field_name": field_name,
                "value": value,
            }
        )


class AdminFieldNotUniqueEdit(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin field not unique edit error"

    def __init__(self, field_name: str, value: str, current: str):
        super().__init__(
            field_name=field_name,
            value=value,
            current=current,
            detail_data={
                "error_code": "field_not_unique",
                "field_name": field_name,
                "value": value,
                "current": current,
            }
        )


class AdminProductModifierNotFoundError(ObjectNotFoundError):
    pass
