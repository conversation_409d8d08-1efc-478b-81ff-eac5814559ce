from dataclasses import asdict
from typing import Any

from starlette import status

import schemas
from core.admin_notification.service import notify_all_channels
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import SystemNotification, User
from schemas import OkResponse, SSEChannelTarget
from utils.exceptions import ErrorWithHTTPStatus


class AdminAdminNotificationNotFound(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "admin notification not found error"

    def __init__(self, admin_notification: int):
        super().__init__(
            admin_notification=admin_notification,
            detail_data={
                "error_code": "admin_notification_not_found",
            }
        )


class AdminNotificationService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",  # will be overridden in "write" routes
                "profile_id",
                "admin_notification_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.admin_notification_id: int = scopes.data.admin_notification_id

    async def get_admin_notification(self, ) -> schemas.AdminNotificationSchema:

        admin_notification = await SystemNotification.get(
            id=self.admin_notification_id,
            group_id=self.profile_id,
            is_deleted=False,
        )
        if not admin_notification:
            raise AdminAdminNotificationNotFound(self.admin_notification_id)

        return schemas.AdminNotificationSchema.from_orm(admin_notification)

    async def read_admin_notification(
            self,
            is_read: bool,
    ) -> OkResponse:
        await crud.update_admin_notification(
            self.admin_notification_id, is_read=is_read
        )
        await notify_all_channels(
            SSEChannelTarget.ADMIN_NOTIFICATION, "notification_read_change",
            profile_id=self.profile_id, admin_notification_id=self.admin_notification_id
        )
        return schemas.OkResponse()

    async def delete_admin_notification(self, ) -> schemas.OkResponse:
        await crud.update_admin_notification(
            self.admin_notification_id, is_deleted=True
        )
        await notify_all_channels(
            SSEChannelTarget.ADMIN_NOTIFICATION,
            "notification_delete", self.profile_id,
            admin_notification_id=self.admin_notification_id
        )
        return schemas.OkResponse()
