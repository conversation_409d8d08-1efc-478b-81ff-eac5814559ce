from fastapi import APIRouter, Depends, Security

import schemas
from schemas import AdminNotificationSchema, OkResponse
from .service import AdminNotificationService

router = APIRouter(
    prefix="/{admin_notification_id}"
)


@router.get("/")
async def get_admin_notification(
        service: AdminNotificationService = Depends(),
) -> AdminNotificationSchema:
    return await service.get_admin_notification()


@router.patch("/read")
async def read_admin_notification(
        is_read: bool,
        service: AdminNotificationService = Security(
            scopes=["me:write", "profile:edit"]
        )
) -> OkResponse:
    return await service.read_admin_notification(is_read)


@router.delete("/")
async def delete_admin_notification(
        service: AdminNotificationService = Security(
            scopes=["me:write", "profile:edit"]
        )
) -> schemas.OkResponse:
    return await service.delete_admin_notification()
