from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Query, Security
from sse_starlette import EventSourceResponse
from starlette.requests import Request

import schemas
from core.api.depends import get_lang
from core.auth.depend import get_token
from core.auth.services.scopes_checker import ScopesCheckerService
from core.sse_service.service import Admin<PERSON>EService
from db import DBSession
from schemas import (
    AdminNotificationSchema, NotificationLevel, OkResponse, SSEChannelTarget,
    SystemNotificationCategory, SystemNotificationType,
)
from .service import AdminNotificationsService

router = APIRouter()


@router.get("/")
async def get_admin_notifications(
        exclude: list[int] | None = Query(
            None, description="array of admin_notification ids to exclude"
        ),
        include: list[int] | None = Query(
            None,
            description="array of admin_notification ids to select only in response"
        ),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        level: list[NotificationLevel] = Query(
            None, description="array of types of admin_notification by categories"
        ),
        categories: list[SystemNotificationCategory] = Query(
            None, description="array of types of admin_notification by categories"
        ),
        type_notifications: list[SystemNotificationType] = Query(
            None, description="array of types of admin_notification by type"
        ),
        is_read: bool | None = Query(
            None, description="types of admin_notification by is_read"
        ),
        service: AdminNotificationsService = Depends(),
) -> list[AdminNotificationSchema]:
    return await service.get_admin_notifications(
        offset, limit, include, exclude, False, categories, type_notifications, is_read,
        level
    )


@router.get("/count")
async def get_admin_notifications_count(
        exclude: list[int] | None = Query(
            None, description="array of admin_notification ids to exclude"
        ),
        include: list[int] | None = Query(
            None,
            description="array of admin_notification ids to select only in response"
        ),
        level: list[NotificationLevel] = Query(
            None, description="array of types of admin_notification by categories"
        ),
        categories: list[SystemNotificationCategory] = Query(
            None, description="array of types of admin_notification by categories"
        ),
        type_notifications: list[SystemNotificationType] = Query(
            None, description="array of types of admin_notification by type"
        ),
        is_read: bool | None = Query(
            None, description="types of admin_notification by is_read"
        ),
        service: AdminNotificationsService = Depends(),
) -> int:
    return await service.get_admin_notifications(
        None, None, include, exclude, True,
        categories, type_notifications, is_read,
        level
    )


@router.post("/read")
async def set_read_admin_notifications(
        is_read: bool,
        data: list[int] | None = Query(
            None, description="array of admin_notification ids to cancel"
        ),
        service: AdminNotificationsService = Security(
            scopes=["me:write", "admin_notification:edit"]
        ),
) -> schemas.OkResponse:
    return await service.set_read_admin_notifications(is_read, data)


@router.post("/delete")
async def delete_admin_notifications(
        data: list[int],
        service: AdminNotificationsService = Security(
            scopes=["me:write", "admin_notification:edit"]
        ),
) -> OkResponse:
    return await service.delete_admin_notifications(data)


@router.post("/")
async def create_admin_notifications(
        title: str = Query(),
        content: str = Query(),
        service: AdminNotificationsService = Security(
            scopes=["me:write", "admin_notification:edit"]
        ),
) -> AdminNotificationSchema | None:
    return await service.create_admin_notifications(title, content)


@router.get("/stream")
async def stream_notifications(
        request: Request,
        x_session_id: str = Header(...),
        data: ScopesCheckerService.get_params_dataclass("profile_id") = Depends(),
        token: str = Depends(get_token),
        lang: str = Depends(get_lang),
) -> EventSourceResponse:
    """Endpoint для створення SSE з'єднання"""
    try:
        admin_sse_service = AdminSSEService(
            session_id=x_session_id,
            target=SSEChannelTarget.ADMIN_NOTIFICATION
        )

        with DBSession():
            data_result = await admin_sse_service.get_data(
                request, token, lang, data
            )
    except Exception as e:
        raise e

    return await admin_sse_service.stream_notifications(
        **data_result
    )
