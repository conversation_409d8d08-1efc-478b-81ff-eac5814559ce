import schemas
from core.admin_notification.service import (
    create_system_notification,
    notify_all_channels,
)
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import User
from schemas import (
    AdminNotificationSchema, NotificationLevel, OkResponse, SSEChannelTarget,
    SystemNotificationCategory, SystemNotificationType,
)


class AdminNotificationsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    @classmethod
    async def depend_stream(cls):
        pass

    async def get_admin_notifications(
            self,
            offset: int | None = None,
            limit: int | None = 10,
            include: list[int] | None = None,
            exclude: list[int] | None = None,
            is_count: bool | None = False,
            categories: list[SystemNotificationCategory] | None = None,
            type_notifications: list[SystemNotificationType] | None = None,
            is_read: bool | None = None,
            level: list[NotificationLevel] | None = None,
    ) -> list[AdminNotificationSchema] | int:
        admin_notifications = await crud.get_admin_system_notification_list(
            self.profile_id,
            self.user.id,
            offset=offset,
            limit=limit,
            include=include,
            exclude=exclude,
            is_count=is_count,
            categories=categories,
            type_notifications=type_notifications,
            is_read=is_read,
            level=level
        )

        if is_count:
            return admin_notifications

        return [
            schemas.AdminNotificationSchema.from_orm(admin_notification)
            for admin_notification in admin_notifications
        ]

    async def set_read_admin_notifications(
            self,
            is_read: bool,
            data: list[int] | None = None
    ) -> OkResponse:
        await crud.update_admin_notifications_for_group(self.profile_id, is_read, data)
        await notify_all_channels(
            SSEChannelTarget.ADMIN_NOTIFICATION, "notification_read_change",
            profile_id=self.profile_id
        )
        return schemas.OkResponse()

    async def delete_admin_notifications(
            self,
            data: list[int] | None = None
    ) -> OkResponse:
        await crud.delete_admin_notifications_for_group(self.profile_id, data)
        await notify_all_channels(
            SSEChannelTarget.ADMIN_NOTIFICATION, "notification_delete",
            profile_id=self.profile_id
        )
        return schemas.OkResponse()

    async def create_admin_notifications(
            self,
            title: str,
            content: str,
    ) -> AdminNotificationSchema | None:
        admin_notification = await create_system_notification(
            "profile:edit", self.profile_id,
            SystemNotificationCategory.GENERAL,
            SystemNotificationType.GENERAL_NOTIFICATION,
            title, content, NotificationLevel.INFO,
        )
        if admin_notification:
            return schemas.AdminNotificationSchema.from_orm(admin_notification)
