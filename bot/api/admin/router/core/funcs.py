import schemas

def payment_provider_data_to_schema(
        provider: schemas.BasicProvidersType | str,
        data_dict: dict | None = None,
) -> schemas.PaymentProviderSchema | None:
    if not data_dict:
        return None

    match provider:
        case "incust_pay":
            return schemas.IncustPaySchema(
                type="incust_pay",
                data=schemas.IncustPayPaymentProviderData(**data_dict)
            )
        case "tg_pay":
            return schemas.TelegramPaySchema(
                type="tg_pay",
                data=schemas.TelegramPayPaymentData(**data_dict)
            )
        case "stripe":
            return schemas.StripeSchema(
                type="stripe",
                data=schemas.StripePaymentProviderData(**data_dict)
            )
        case "liqpay":
            return schemas.LiqPaySchema(
                type="liqpay",
                data=schemas.LiqPayPaymentProviderData(**data_dict)
            )
        case "epay":
            return schemas.EPaySchema(
                type="epay",
                data=schemas.EpayPaymentProviderData(**data_dict)
            )
        case "comsa":
            return schemas.ComsaSchema(
                type="comsa",
                data=schemas.ComsaPaymentProviderData(**data_dict)
            )
        case "flutterwave":
            return schemas.FlutterWaveSchema(
                type="flutterwave",
                data=schemas.FlutterWavePaymentProviderData(**data_dict)
            )
        case "fondy":
            return schemas.FondySchema(
                type="fondy",
                data=schemas.FondyPaymentProviderData(**data_dict)
            )
        case "freedompay":
            return schemas.FreedomPaySchema(
                type="freedompay",
                data=schemas.FreedomPayProviderData(**data_dict)
            )
        case "orange":
            return schemas.OrangeSchema(
                type="orange",
                data=schemas.OrangePaymentProviderData(**data_dict)
            )
        case "kpay":
            return schemas.KPaySchema(
                type="kpay",
                data=schemas.KPayPaymentProviderData(**data_dict)
            )
        case "pl24":
            return schemas.PL24Schema(
                type="pl24",
                data=schemas.Pl24PaymentProviderData(**data_dict)
            )
        case "tpay":
            return schemas.TPaySchema(
                type="tpay",
                data=schemas.TPayPaymentProviderData(**data_dict)
            )
        case "unipos":
            return schemas.UniposPaySchema(
                type="unipos",
                data=schemas.UniposPaymentProviderData(**data_dict)
            )
        case "directpay":
            return schemas.DirectpaySchema(
                type="directpay",
                data=schemas.DirectpayPaymentProviderData(**data_dict)
            )
        case "momo":
            return schemas.MomoSchema(
                type="momo",
                data=schemas.MomoPaymentProviderData(**data_dict)
            )
        case "tj":
            return schemas.TjSchema(
                type="tj",
                data=schemas.TjPaymentProviderData(**data_dict)
            )
        case "airtel":
            return schemas.AirTelSchema(
                type="airtel",
                data=schemas.AirTelPaymentProviderData(**data_dict)
            )
        case "wave":
            return schemas.WavePaySchema(
                type="wave",
                data=schemas.WavePaymentProviderData(**data_dict)
            )
        case "ewallet":
            return schemas.EWalletPaySchema(
                type="ewallet",
                data=schemas.EWalletPaymentProviderData(**data_dict)
            )
        case "custom":
            return schemas.CustomPaySchema(
                type="custom",
                data=schemas.PaymentProviderBaseSchema(**data_dict)
            )
