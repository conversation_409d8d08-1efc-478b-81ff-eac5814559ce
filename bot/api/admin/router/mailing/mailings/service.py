import base64
from io import Bytes<PERSON>

from fastapi import UploadFile

import schemas
from api.admin.router.mailing.exceptions import (
    AdminMailingCreateMessagesError,
)
from api.admin.router.mailing.functions import (
    mailing_to_mailin_schema, mailing_to_mailing_list_schema,
)
from core.auth.services.scopes_checker import ScopesCheckerService
from core.marketing.service import MailingService as CoreMailingsService
from core.media_manager import media_manager
from db import crud
from db.models import ClientBot, Group, User


class MailingsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.profile_id: int = scopes.data.profile_id

    async def get_mailings(
            self,
            params: schemas.AdminListParams,
    ) -> list[schemas.AdminMailingListItem]:
        mailings = await crud.get_mailing_list(self.profile_id, self.user.id, params)

        return [mailing_to_mailing_list_schema(mail) for mail in mailings]

    async def get_customers_count_info(
            self,
            params: schemas.AdminCountCustomersListParams
    ) -> list[schemas.AdminMailingCustomersCountInfo]:
        res = []
        if not params.channels:
            return res
        for channel in params.channels:
            count = await crud.get_customers_count_by_channel(self.profile_id, channel)
            res.append(
                schemas.AdminMailingCustomersCountInfo(channel=channel, count=count)
            )
        if "bot" in params.channels:
            bot = await ClientBot.get(group_id=self.profile_id)
            if bot and bot.bot_type == "whatsapp":
                count = await crud.get_customers_count_by_channel(
                    self.profile_id, "bot", wa_without_template=True
                )
                res.append(
                    schemas.AdminMailingCustomersCountInfo(
                        channel="bot", count=count, is_wa_without_template=True
                    )
                )
        return res

    async def create_mailing(
            self, data: schemas.CreateMailingSchema,
            test_user_ids: list[int] | None = None
    ) -> schemas.MailingSchema:
        media_obj = None
        upload_file = None
        if data.media and not data.media_id:
            if data.media.startswith("data:"):
                _, file = data.media.split(",", 1)
                # file_type = file_type.split(";")[0].split(":")[1]
                decoded_bytes = base64.b64decode(file)
                file_bytes = BytesIO(decoded_bytes)
                upload_file = UploadFile(
                    file=file_bytes, filename="filename"
                )
            if upload_file:
                media_obj = await media_manager.save_from_upload_file(upload_file)

        media_id = None
        if data.media_id:
            media_id = data.media_id
        elif media_obj:
            media_id = media_obj.id

        mailing = await crud.create_mailing(
            data, self.profile_id, media_id, bool(test_user_ids)
        )
        bot = await ClientBot.get(group_id=self.profile_id)

        mailing_service = CoreMailingsService()
        mailing_service.bot = bot
        try:
            messages_count = await mailing_service.create_mailing_messages(
                mailing, test_user_ids
            )
            if not messages_count:
                raise Exception("messages count 0")
            await mailing.update(
                sent_info={
                    "total": messages_count,
                    "total_sent": 0,
                    "total_failed": 0,
                }
            )
        except Exception:
            await mailing.delete()
            raise AdminMailingCreateMessagesError()
        return mailing_to_mailin_schema(mailing)

    async def get_test_mailing_users(self, data: schemas.CreateMailingSchema) -> list[
        schemas.MailingCustomerInfo]:
        bot = await ClientBot.get(group_id=self.profile_id)
        group = await Group.get(self.profile_id)
        admins_and_managers = await crud.get_users_for_mailing_test(self.profile_id)
        users = []
        for admin in admins_and_managers:
            if bot and bot.bot_type == "whatsapp" and not admin.wa_phone:
                continue
            user_data = schemas.MailingCustomerInfo(
                id=admin.id,
                accept_agreement=True,
                first_name=admin.first_name,
                last_name=admin.last_name,
                full_name=admin.full_name,
                channels=[],
                is_owner=bool(group.owner_id == admin.id)
            )
            for channel in data.channels:
                if channel.value == "bot":
                    if bot and bot.bot_type == "whatsapp":
                        if admin.wa_phone:
                            user_data.channels.append(
                                schemas.MailingChannelTypeEnum.BOT
                            )
                    elif bot and bot.bot_type == "telegram":
                        if admin.chat_id:
                            user_data.channels.append(
                                schemas.MailingChannelTypeEnum.BOT
                            )
                elif channel.value == "email":
                    if admin.email:
                        user_data.channels.append(schemas.MailingChannelTypeEnum.EMAIL)

            users.append(user_data)

        return users

    async def upload_mailing_media(self, file: UploadFile) -> schemas.MailingMediaItem:
        media_obj = await media_manager.save_from_upload_file(file)

        return schemas.MailingMediaItem(media_id=media_obj.id, media_url=media_obj.url)
