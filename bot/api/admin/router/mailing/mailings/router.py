from fastapi import APIRouter, Depends, UploadFile

import schemas
from api.admin.router.mailing.mailings.service import MailingsService

router = APIRouter()


@router.get("/")
async def get_mailings(
        params: schemas.AdminListParams = Depends(),
        service: MailingsService = Depends()
) -> list[schemas.AdminMailingListItem]:
    return await service.get_mailings(params)


@router.get("/customers_count_info")
async def get_customers_count_info(
        params: schemas.AdminCountCustomersListParams = Depends(),
        service: MailingsService = Depends()
) -> list[schemas.AdminMailingCustomersCountInfo]:
    return await service.get_customers_count_info(params)


@router.post("/")
async def create_mailing(
        data: schemas.CreateMailingSchema,
        service: MailingsService = Depends()
) -> schemas.MailingSchema:
    return await service.create_mailing(data)


@router.post("/test")
async def test_mailing(
        data: schemas.TestCreateMailingSchema,
        service: MailingsService = Depends()
) -> schemas.MailingSchema:
    return await service.create_mailing(
        schemas.CreateMailingSchema(**data.dict()), data.user_ids
    )


@router.post("/test_users")
async def get_test_mailing_users(
        data: schemas.CreateMailingSchema,
        service: MailingsService = Depends()
) -> list[schemas.MailingCustomerInfo]:
    return await service.get_test_mailing_users(data)


@router.post("/upload_mailing_media")
async def upload_mailing_media(
        file: UploadFile,
        service: MailingsService = Depends()
) -> schemas.MailingMediaItem:
    return await service.upload_mailing_media(file)
