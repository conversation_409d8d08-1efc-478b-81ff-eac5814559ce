from fastapi import APIRouter, Depends

import schemas
from api.admin.router.mailing.mailing.service import MailingService

router = APIRouter()


@router.get("/{mailing_id}")
async def get_messages(
    mailing_id: int,
    params: schemas.AdminListParams = Depends(),
    service: MailingService = Depends()
) -> list[schemas.AdminMailingMessageListItem]:
    return await service.get_mailing_messages(params, mailing_id)


@router.get("/{message_id}")
async def get_message(
    message_id: int,
    service: MailingService = Depends()
) -> schemas.MailingMessageSchema:
    return await service.get_mailing_message(message_id)
