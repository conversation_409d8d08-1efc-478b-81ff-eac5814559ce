import schemas
from api.admin.router.mailing.exceptions import (
    AdminMailingMessageNotFoundError, AdminMailingNotFoundError,
)
from api.admin.router.mailing.functions import (
    mailing_message_to_mailing_list_schema, mailing_message_to_schema,
    mailing_to_mailing_schema,
)
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Mailing, MailingMessage, User


class MailingService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
                "mailing_id"
            ),
    ):
        self.user: User = scopes.user
        self.profile_id: int = scopes.data.profile_id
        self.mailing_id: int = scopes.data.mailing_id

    async def get_mailing(
            self,
    ) -> schemas.MailingSchema:
        mailing = await Mailing.get(self.mailing_id, group_id=self.profile_id)
        if not mailing:
            raise AdminMailingNotFoundError()

        return await mailing_to_mailing_schema(mailing)

    async def get_mailing_messages(
            self,
            params: schemas.AdminListParams,
            mailing_id: int,
    ) -> list[schemas.AdminMailingMessageListItem]:
        mailings = await crud.get_mailing_messages_list(
            mailing_id, self.profile_id, self.user.id, params
        )

        return [mailing_message_to_mailing_list_schema(mail) for mail in mailings]

    async def get_mailing_message(
            self,
            mailing_message_id: int,
    ) -> schemas.MailingMessageSchema:
        message = await MailingMessage.get(mailing_message_id)
        if not message:
            raise AdminMailingMessageNotFoundError()

        return mailing_message_to_schema(message)

    async def update_mailing(
            self, data: schemas.AdminUpdateMailing
    ) -> schemas.MailingSchema:
        mailing = await Mailing.get(self.mailing_id)
        if not mailing:
            raise AdminMailingNotFoundError()
        await mailing.update(status=data.status.value.upper())

        return await mailing_to_mailing_schema(mailing)
