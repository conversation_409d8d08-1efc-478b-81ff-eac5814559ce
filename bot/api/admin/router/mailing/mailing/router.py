from fastapi import APIRouter, Depends

import schemas
from api.admin.router.mailing.mailing.service import MailingService

router = APIRouter(
    prefix="/{mailing_id}"
)


@router.get("/")
async def get_mailing(
    service: MailingService = Depends()
) -> schemas.MailingSchema:
    return await service.get_mailing()


@router.post("/")
async def update_mailing(
    data: schemas.AdminUpdateMailing,
    service: MailingService = Depends()
) -> schemas.MailingSchema:
    return await service.update_mailing(data)
