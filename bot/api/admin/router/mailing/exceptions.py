from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class AdminMailingNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin mailing not found error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "mailing_not_found",
            }
        )


class AdminMailingMessageNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin mailing message not found error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "mailing_message_not_found",
            }
        )


class AdminMailingMissingChannelsError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin mailing missing channels error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "mailing_missing_channels",
            }
        )


class AdminMailingCreateMessagesError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin mailing create messages error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "mailing_create_messages",
            }
        )
