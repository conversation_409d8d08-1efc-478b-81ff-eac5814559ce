import schemas
from db.models import Mailing, MailingMessage, MediaObject
from schemas import MailingStatus<PERSON>num


def mailing_to_mailing_list_schema(mailing: Mailing) -> schemas.AdminMailingListItem:
    return schemas.AdminMailingListItem(
        id=mailing.id,
        status=mailing.status,
        sent_info=mailing.sent_info,
        name=mailing.name,
        channels=mailing.channels,
    )


async def mailing_to_mailing_schema(mailing: Mailing) -> schemas.MailingSchema:
    media_url = None
    if mailing.media_id:
        media = await MediaObject.get(mailing.media_id)
        if media:
            media_url = media.url

    if isinstance(mailing.status, str):
        status_str = mailing.status.lower()
    else:
        status_str = mailing.status.value.lower()

    return schemas.MailingSchema(
        id=mailing.id,
        status=MailingStatusEnum(status_str),
        sent_info=mailing.sent_info,
        name=mailing.name,
        description=mailing.description,
        message_info=mailing.message_info,
        filters=mailing.filters,
        channels_settings=mailing.channels_settings,
        channels=mailing.channels,
        last_sent_datetime=mailing.last_sent_datetime,
        message_source_text=mailing.message_source_text if
        mailing.message_source_text else None,
        media_url=media_url,
    )


def mailing_to_mailin_schema(mailing: Mailing) -> schemas.MailingSchema:
    return schemas.MailingSchema(
        id=mailing.id,
        status=mailing.status,
        sent_info=mailing.sent_info,
        name=mailing.name,
        description=mailing.description,
        message_info=mailing.message_info,
        filters=mailing.filters,
        channels_settings=mailing.channels_settings,
        channels=mailing.channels,
        last_sent_datetime=mailing.last_sent_datetime,
    )


def mailing_message_to_mailing_list_schema(
        message: MailingMessage
) -> schemas.AdminMailingMessageListItem:
    return schemas.AdminMailingMessageListItem(
        id=message.id,
        status=message.status,
        channel_type=message.channel_type,
        channel_name=message.channel_name,
    )


def mailing_message_to_schema(message: MailingMessage) -> schemas.MailingMessageSchema:
    return schemas.MailingMessageSchema(
        id=message.id,
        status=message.status,
        created_datetime=message.created_datetime,
        start_datetime=message.start_datetime,
        end_datetime=message.end_datetime,
        error_details=message.error_details,
        retry_info=message.retry_info,
        message=message.message,
        channel_type=message.channel_type,
        channel_name=message.channel_name,
        bot_id=message.bot_id,
        user_id=message.user_id,
        mailing_id=message.mailing_id,
    )
