from fastapi import Depends

import schemas
from core.auth.depend import get_active_user
from db import crud
from db.models import User


class AdminUserService:
    def __init__(self, user: User = Depends(get_active_user)):
        self.user = user

    async def get_admin_user_info(self) -> schemas.AdminUserInfo:
        profiles_count = await crud.get_admin_profiles_list(
            user_id=self.user.id,
            is_count=True
        )
        if profiles_count == 1:
            one_profile_id = (await crud.get_admin_profiles_list(
                user_id=self.user.id,
                limit=1
            ))[0].id
        else:
            one_profile_id = None

        return schemas.AdminUserInfo(
            profiles_count=profiles_count,
            one_profile_id=one_profile_id,
        )
