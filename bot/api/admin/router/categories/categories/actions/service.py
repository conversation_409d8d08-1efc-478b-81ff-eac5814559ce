import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Store, StoreCategory, User


class CategoriesActionsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def mass_delete_categories(
            self, data: schemas.AdminCategoriesActionPayloadSchema,
    ) -> schemas.OkResponse:
        categories = await crud.get_and_validate_access_on_objects(
            "category", StoreCategory,
            data.categories,
            self.user.id, self.profile_id
        )
        categories_ids = [category.id for category in categories]

        await crud.mass_delete_categories(categories_ids)
        return schemas.OkResponse()

    async def mass_up_categories_to_store(
            self, data: schemas.AdminCategoriesActionPayloadSchema, store_id: int
    ) -> schemas.OkResponse:
        categories = await crud.get_and_validate_access_on_objects(
            "category", StoreCategory,
            data.categories,
            self.user.id, self.profile_id
        )
        stores = await crud.get_and_validate_access_on_objects(
            "store", Store,
            [store_id],
            self.user.id, self.profile_id
        )

        store = stores[0]

        await crud.connect_categories_to_store(store, categories)

        return schemas.OkResponse()

    async def mass_remove_categories_from_store(
            self, data: schemas.AdminCategoriesActionPayloadSchema, store_id: int
    ) -> schemas.OkResponse:
        categories = await crud.get_and_validate_access_on_objects(
            "category", StoreCategory,
            data.categories,
            self.user.id, self.profile_id
        )
        stores = await crud.get_and_validate_access_on_objects(
            "store", Store,
            [store_id],
            self.user.id, self.profile_id
        )

        store = stores[0]

        await crud.disconnect_categories_from_store(store, categories)
        return schemas.OkResponse()
