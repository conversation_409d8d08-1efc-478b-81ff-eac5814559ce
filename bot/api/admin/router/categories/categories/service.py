from operator import attrgetter

from itertools import groupby

import schemas
from api.admin.helpers import get_or_create_brand
from api.admin.router.categories.functions import (
    category_to_admin_list_schema,
    category_to_admin_schema,
)
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Group, Store, StoreCategory, User


class CategoriesService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_categories_list(
            self,
            store_ids: list[int] | None = None,
            search_text: str | None = None,
            exclude: list[int] | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminCategoryListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        categories = await crud.get_admin_categories_list(
            self.profile_id, self.user.id,
            store_ids=store_ids,
            search_text=search_text,
            exclude=exclude,
            offset=offset,
            limit=limit,
            need_check_access=True
        ) or []

        return [
            await category_to_admin_list_schema(category, self.profile_id, self.user.id)
            for category in categories]

    async def get_categories_total_count(
            self,
            store_ids: list[int] | None = None,
            search_text: str | None = None,
    ) -> int:
        return await crud.get_admin_categories_list(
            self.profile_id, self.user.id,
            store_ids,
            search_text,
            is_count=True,
        )

    async def get_categories_tree(self) -> list[schemas.AdminTreeCategorySchema]:
        all_categories = await crud.get_admin_categories_list(
            self.profile_id,
            self.user.id,
            sort_by_father_category=True
        )

        categories_by_father_category = {
            father_category_id: [schemas.AdminTreeCategorySchema.from_orm(child) for
                                 child in children]
            for father_category_id, children in
            groupby(all_categories, attrgetter("father_category_id"))
        }

        def make_categories(father_category_id: int | None = None):
            categories = categories_by_father_category.get(father_category_id, [])
            for category in categories:
                category.children = make_categories(category.id)
            return categories

        return make_categories()

    async def create_category(self, data: schemas.AdminCreateCategoryData):
        profile = await Group.get(self.profile_id)
        brand = await get_or_create_brand(profile)

        stores = await crud.get_and_validate_access_on_objects(
            "store", Store, data.stores, self.user.id, self.profile_id,
            scope_name="edit",
        )

        if data.father_category_id is not None:
            await crud.get_and_validate_access_on_objects(
                "category", StoreCategory,
                [data.father_category_id],
                self.user.id, self.profile_id,
                scope_name="read",
            )

        category = await crud.create_category(brand, data, self.user, stores)
        return await category_to_admin_schema(category, profile, self.user.id)
