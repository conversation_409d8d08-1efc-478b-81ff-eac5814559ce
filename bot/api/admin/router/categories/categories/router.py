from fastapi import APIRouter, Depends, Query, Security

import schemas
from .service import CategoriesService

router = APIRouter()


@router.get("/")
async def get_categories_list(
        store_ids: list[int] | None = Query(None),
        search_text: str | None = Query(None),
        exclude: list[int] | None = Query(None, description="array of category ids to exclude"),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        service: CategoriesService = Depends()
) -> list[schemas.AdminCategoryListSchema]:
    return await service.get_categories_list(store_ids, search_text, exclude, offset, limit)


@router.get("/total_count")
async def get_categories_total_count(
        store_ids: list[int] | None = Query(None),
        search_text: str | None = Query(None),
        service: CategoriesService = Depends()
) -> int:
    return await service.get_categories_total_count(store_ids, search_text)


@router.get("/tree")
async def get_categories_tree(
        service: CategoriesService = Depends()
) -> list[schemas.AdminTreeCategorySchema]:
    return await service.get_categories_tree()


@router.post("/")
async def create_category(
        data: schemas.AdminCreateCategoryData,
        service: CategoriesService = Security(scopes=["me:write", "menu:create"])
) -> schemas.AdminCategorySchema:
    return await service.create_category(data)
