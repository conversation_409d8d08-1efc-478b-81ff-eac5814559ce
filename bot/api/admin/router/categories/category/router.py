from fastapi import APIRouter, Depends, Query, Security, UploadFile

import schemas
from .service import CategoryService

router = APIRouter(
    prefix="/{category_id}"
)


@router.get("/")
async def get_category(
        service: CategoryService = Depends()
) -> schemas.AdminCategorySchema:
    category = await service.get_category()
    return await service.category_to_schema(category)


@router.patch("/")
async def update_category(
        data: schemas.AdminUpdateCategoryData,
        service: CategoryService = Security(scopes=["me:write", "category:edit"])
) -> schemas.AdminCategorySchema:
    return await service.update_category(data)


@router.delete("/")
async def delete_category(
        service: CategoryService = Security(scopes=["me:write", "category:edit"])
) -> schemas.OkResponse:
    return await service.delete_category()


@router.post("/update_image")
async def update_category_image(
    file: UploadFile,
    service: CategoryService = Security(scopes=["store:edit", "me:write"])
) -> schemas.AdminCategorySchema:
    return await service.update_category_image(file)


@router.delete("/delete_image")
async def delete_category_image(
    service: CategoryService = Security(scopes=["store:edit", "me:write"])
) -> schemas.AdminCategorySchema:
    return await service.delete_category_image()


@router.get("/products")
async def get_category_products(
        store_ids: list[int] | None = Query(None),
        search_text: str | None = Query(None),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        service: CategoryService = Depends(),
) -> list[schemas.AdminProductListSchema]:
    return await service.get_category_products(store_ids, search_text, offset, limit)


@router.post("/products")
async def connect_products_to_category(
        data: schemas.AdminConnectProductsToObjectData,
        service: CategoryService = Security(scopes=["me:write", "category:edit"])
) -> schemas.AdminProductsConnectedToObjectResult:
    return await service.connect_products_to_category(data)


@router.post("/products/disconnect")
async def delete_products_from_category(
        data: schemas.AdminDisconnectProductsData,
        service: CategoryService = Security(scopes=["me:write", "category:edit"]),
) -> schemas.AdminProductsDisconnectedResult:
    return await service.disconnect_products_from_category(data)


@router.get("/stores")
async def get_category_stores(
        service: CategoryService = Depends(),
) -> list[schemas.AdminStoreListSchema]:
    return await service.get_category_stores()


@router.post("/stores")
async def connect_stores_to_category(
        data: schemas.AdminConnectStoresToCategoryData,
        service: CategoryService = Security(scopes=["me:write"]),
) -> list[schemas.AdminStoreListSchema]:
    return await service.connect_stores_to_category(data)
