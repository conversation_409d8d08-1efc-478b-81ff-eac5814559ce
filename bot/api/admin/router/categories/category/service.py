from dataclasses import asdict
from typing import Any

from fastapi import UploadFile

import schemas
from api.admin.helpers import validate_non_empty_fields_and_translations
from api.admin.router.categories.functions import category_to_admin_schema
from api.admin.router.products.functions import product_to_admin_list_schema
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.media_manager import media_manager
from db import crud
from db.crud import get_and_validate_access_on_objects
from db.models import Group, ProductToCategory, Store, StoreCategory, StoreProduct, User
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class CategoryService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "category:read",  # will be overridden in "write" routes
                "profile_id",
                "category_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.category_id: int = scopes.data.category_id

    # noinspection PyMethodMayBeStatic
    async def category_to_schema(
            self, category: StoreCategory, profile: Group | None = None
    ):
        if not profile:
            profile = await Group.get(self.profile_id)
        return await category_to_admin_schema(category, profile, self.user.id)

    async def get_category(self) -> StoreCategory:
        category = await crud.get_category_by_id_and_profile_id(
            self.category_id, self.profile_id
        )
        if not category:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "category:read", self.available_data,
            )
        return category

    async def update_category(self, data: schemas.AdminUpdateCategoryData):
        category = await self.get_category()
        profile = await Group.get(self.profile_id)

        if data.father_category_id is not None:
            await crud.get_and_validate_access_on_objects(
                "category", StoreCategory,
                [data.father_category_id],
                self.user.id, self.profile_id
            )

        data_only_set = data.dict(exclude_unset=True)
        validate_non_empty_fields_and_translations(data_only_set, category, "name")
        await crud.update_category(category, data, profile.get_langs_list())
        return await self.category_to_schema(category, profile)

    async def delete_category(self):
        await crud.delete_category(self.category_id)
        return schemas.OkResponse()

    async def update_category_image(self, file: UploadFile):
        category = await self.get_category()
        media = await media_manager.save_from_upload_file(file)

        await category.update(media=media)
        return await self.category_to_schema(category)

    async def delete_category_image(self):
        category = await self.get_category()
        await category.update(media_id=None)
        return await self.category_to_schema(category)

    async def get_category_products(
            self,
            store_ids: list[int] | None = None,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminProductListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        # to verify object exists
        category = await self.get_category()

        products = await crud.get_admin_products_list(
            self.profile_id,
            user_id=self.user.id,
            store_ids=store_ids,
            category_ids=[category.id],
            search_text=search_text,
            offset=offset,
            limit=limit,
            need_check_access=False,
        )

        return [
            await product_to_admin_list_schema(product, self.profile_id, self.user.id)
            for product in products]

    async def connect_products_to_category(
            self, data: schemas.AdminConnectProductsToObjectData
    ):
        category = await self.get_category()

        products = await crud.get_and_validate_access_on_objects(
            "product", StoreProduct,
            data.products, self.user.id, self.profile_id,
        )

        await crud.connect_related_objects(category, "products", products, data.replace)
        return schemas.AdminProductsConnectedToObjectResult(
            replaced=data.replace,
            connected_products=[
                await product_to_admin_list_schema(
                    product, self.profile_id, self.user.id,
                )
                for product in products
            ]
        )

    async def disconnect_products_from_category(
            self, data: schemas.AdminDisconnectProductsData
    ):
        category = await self.get_category()

        await crud.disconnect_m2m_related_objects(
            ProductToCategory,
            "category_id", category.id,
            "product_id", data.products,
            data.disconnect_all,
        )
        return schemas.AdminProductsDisconnectedResult(
            is_all_deleted=data.disconnect_all,
            products_ids_disconnected=data.products or [] if not data.disconnect_all
            else None,
        )

    async def get_category_stores(self):
        stores_data = await crud.get_admin_category_stores_list(
            self.profile_id, self.category_id, self.user.id
        )
        return [schemas.AdminStoreListSchema.from_orm(obj) for obj in stores_data]

    async def connect_stores_to_category(
            self, data: schemas.AdminConnectStoresToCategoryData
    ):
        category = await self.get_category()

        stores = await get_and_validate_access_on_objects(
            "store", Store,
            data.stores,
            self.user.id, self.profile_id, "edit",
        )

        await crud.connect_stores_to_category(
            self.profile_id,
            self.user.id,
            category,
            stores,
            data.replace
        )
        return await self.get_category_stores()
