import schemas
from api.admin.helpers import check_object_access, get_translations_schemas_dict
from db import crud
from db.models import Group, StoreCategory


async def category_to_admin_list_schema(category_or_row: StoreCategory, profile_id: int, user_id: int):
    schema = schemas.AdminCategoryListSchema.from_orm(category_or_row)
    if not schema.profile_id:
        schema.profile_id = profile_id

    if not hasattr(category_or_row, "read_allowed"):
        schema.read_allowed = await check_object_access(
            "category",
            category_or_row.id,
            profile_id, user_id,
            scope_name="read",
        )
    if not hasattr(category_or_row, "edit_allowed"):
        schema.edit_allowed = await check_object_access(
            "category",
            category_or_row.id,
            profile_id, user_id,
            scope_name="edit",
        )
    return schema


async def category_to_admin_schema(
        category: StoreCategory,
        profile: Group,
        user_id: int,
        read_allowed: bool = True
):
    if category.father_category_id:
        father_category = await StoreCategory.get(category.father_category_id)
    else:
        father_category = None

    edit_allowed = await check_object_access(
        "category", category.id,
        profile.id, user_id,
        scope_name="edit",
    )

    if edit_allowed:
        read_allowed = True
    elif read_allowed is None:
        read_allowed = await check_object_access(
            "category", category.id,
            profile.id, user_id,
            scope_name="read",
        )

    return schemas.AdminCategorySchema(
        id=category.id,
        profile_id=profile.id,
        name=category.name,
        father_category_id=category.father_category_id,
        father_category_name=father_category.name if father_category else None,
        external_id=category.external_id,
        external_type=category.external_type,
        position=category.position,
        image_url=await category.image_url,
        has_child_categories=await crud.get_has_category_children(category.id),
        filters=await crud.get_category_filters(category.id),
        # Lang has not to be specified. No translations needed
        stores=await crud.get_category_stores(
            category.id, ("id", "name"), "user", user_id, profile.id, True
        ),
        translations=await get_translations_schemas_dict(
            category, profile, schemas.AdminCategoryTranslationSchema
        ),
        read_allowed=read_allowed,
        edit_allowed=edit_allowed,
    )
