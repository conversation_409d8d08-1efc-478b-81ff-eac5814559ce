from typing import Optional

from fastapi import APIRouter, Path, Query, status

from core.auth.services.scopes_checker import ScopesCheckerService
from core.loyalty.services import LoyaltySettingsService
from schemas import LoyaltySettingsObjectType
from schemas.admin.loyalty_settings import (
    LoyaltySettingsConfigSchema, LoyaltySettingsCopySchema, LoyaltySettingsCreateSchema,
    LoyaltySettingsListSchema, LoyaltySettingsResponseSchema,
    LoyaltySettingsUpdateSchema, LoyaltySettingsValidateSchema,
)

router = APIRouter(
    prefix="/{profile_id}/loyalty-settings",
    tags=["loyalty-settings"]
)


@router.get("/config", response_model=LoyaltySettingsConfigSchema)
async def get_loyalty_settings_config(
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:read", "profile_id"),
    profile_id: int = Path(..., description="Profile ID")
) -> LoyaltySettingsConfigSchema:
    """Отримати конфігураційні налаштування лояльності"""
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    return await service.get_config()


@router.get("/", response_model=list[LoyaltySettingsListSchema] | None)
async def get_loyalty_settings_list(
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:read", "profile_id"),
    profile_id: int = Path(..., description="Profile ID"),
    store_id: Optional[int] = Query(None, description="Filter by store ID"),
    invoice_template_id: Optional[int] = Query(None, description="Filter by invoice template ID"),
    product_id: Optional[int] = Query(None, description="Filter by product ID"),
    is_enabled: Optional[bool] = Query(None, description="Filter by enabled status"),
    limit: int = Query(100, ge=1, le=1000, description="Limit"),
    offset: int = Query(0, ge=0, description="Offset"),
) -> list[LoyaltySettingsResponseSchema] | None:
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    return await service.get_loyalty_settings_list(
        store_id=store_id,
        product_id=product_id,
        invoice_template_id=invoice_template_id,
        is_enabled=is_enabled,
        limit=limit,
        offset=offset
    )


@router.get("/{loyalty_settings_id}", response_model=LoyaltySettingsResponseSchema)
async def get_loyalty_settings(
    loyalty_settings_id: int,
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:read", "profile_id"),
    profile_id: int = Path(..., description="Profile ID"),
) -> LoyaltySettingsResponseSchema:
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    return await service.get_loyalty_settings_by_id(loyalty_settings_id)


@router.post("/", response_model=LoyaltySettingsResponseSchema, status_code=status.HTTP_201_CREATED)
async def create_loyalty_settings(
    data: LoyaltySettingsCreateSchema,
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:edit", "profile_id"),
    profile_id: int = Path(..., description="Profile ID"),
) -> LoyaltySettingsResponseSchema:
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    return await service.create_loyalty_settings(data)


@router.patch("/{loyalty_settings_id}", response_model=LoyaltySettingsResponseSchema)
async def update_loyalty_settings(
    loyalty_settings_id: int,
    data: LoyaltySettingsUpdateSchema,
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:read", "profile_id"),
    profile_id: int = Path(..., description="Profile ID"),
) -> LoyaltySettingsResponseSchema:
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    return await service.update_loyalty_settings(loyalty_settings_id, data)


@router.delete("/{loyalty_settings_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_loyalty_settings(
    loyalty_settings_id: int,
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:read", "profile_id"),
    profile_id: int = Path(..., description="Profile ID"),
):
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    await service.delete_loyalty_settings(loyalty_settings_id)
    return None


@router.post("/copy", response_model=LoyaltySettingsResponseSchema, status_code=status.HTTP_201_CREATED)
async def copy_loyalty_settings(
    data: LoyaltySettingsCopySchema,
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:edit", "profile_id"),
    profile_id: int = Path(..., description="Profile ID"),
) -> LoyaltySettingsResponseSchema:
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    return await service.copy_loyalty_settings(data)


@router.post("/validate", response_model=LoyaltySettingsValidateSchema)
async def validate_loyalty_settings(
    data: LoyaltySettingsCreateSchema,
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:read", "profile_id"),
    profile_id: int = Path(..., description="Profile ID"),
) -> LoyaltySettingsValidateSchema:
    """Валідувати налаштування лояльності"""
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    return await service.validate_loyalty_settings(data)


# Нові ендпоінти для роботи з об'єктами
@router.get("/{object_type}/{object_id}", response_model=LoyaltySettingsResponseSchema | None)
async def get_loyalty_settings_by_object(
    object_type: LoyaltySettingsObjectType,
    object_id: int,
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:read", "profile_id"),
    profile_id: int = Path(..., description="Profile ID"),
) -> LoyaltySettingsResponseSchema | None:
    """Отримати налаштування лояльності для конкретного об'єкта"""
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    return await service.get_by_object(object_type, object_id)


@router.put("/{object_type}/{object_id}", response_model=LoyaltySettingsResponseSchema)
async def create_or_update_loyalty_settings_by_object(
    object_type: LoyaltySettingsObjectType,
    object_id: int,
    data: LoyaltySettingsCreateSchema,
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:read", "profile_id"),
    profile_id: int = Path(..., description="Profile ID"),
) -> LoyaltySettingsResponseSchema:
    """Створити або оновити налаштування лояльності для об'єкта"""
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    return await service.create_or_update_by_object(object_type, object_id, data)


@router.delete("/{object_type}/{object_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_loyalty_settings_by_object(
    object_type: LoyaltySettingsObjectType,
    object_id: int,
    scopes: ScopesCheckerService = ScopesCheckerService.depend("profile:read", "profile_id"),
    profile_id: int = Path(..., description="Profile ID"),
):
    """Видалити налаштування лояльності для об'єкта"""
    service = LoyaltySettingsService(user=scopes.user, lang=scopes.lang, profile_id=profile_id)
    await service.delete_by_object(object_type, object_id)
    return None
