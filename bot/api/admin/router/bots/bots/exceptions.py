from abc import ABC

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BaseBotValidateError(ErrorWithHTTPStatus, ABC, base=True):
    groups = ["bots"]


class UnknownValidateBotError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "bot data unknown validation error"

    def __init__(self, message: str):
        self.message = message
        super().__init__(message=message)


class BotInvalidTypeError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "bot data invalid type error"


class BotInvalidWBAIDError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "bot data invalid wba id error"


class BotChangeTypeError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "edit additionally bot update webhook error"


class BotInvalidTokenError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "telegram bot token invalid error"


class BotInvalidTypeDataError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "bot data invalid type data error"

    def __init__(self, field: str):
        self.field = field
        super().__init__(field=field)


class BotInvalidDataError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "bot data invalid data error"

    def __init__(self, detail: str):
        self.detail = detail
        super().__init__(detail=detail)


class BotCreateValidationError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "bot create validation error"

    def __init__(self, message: str):
        self.message = message
        super().__init__(message=message)


class BotCreateDuplicateError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "bot create duplicate error"


class BotCreateUnknownError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "bot create unknown error"

    def __init__(self, message: str):
        self.message = message
        super().__init__(message=message)
