from abc import ABC

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BaseBotValidateError(ErrorWithHTTPStatus, ABC, base=True):
    groups = ["bots"]


class WaMasterTemplateNotFoundError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin wa templates not found master template"


class WaTemplateNotFoundError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin wa templates not found master template"


class WaTemplateNoBusinessAccountIdError(BaseBotValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin wa templates bot business account id"
