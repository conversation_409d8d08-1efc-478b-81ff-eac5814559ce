from fastapi import APIRouter, Depends, Query

import schemas
from core.api.depends import build_form_data_depend
from .service import WaTemplatesService

router = APIRouter(
    prefix="/wa_templates",
)


@router.post("/create_wa_template")
async def create_wa_template(
        form_data: schemas.AdminCreateWaTemplate = Depends(
            build_form_data_depend(schemas.AdminCreateWaTemplate)
        ),
        service: WaTemplatesService = Depends(),
) -> schemas.AdminWaMasterTemplate | None:
    return await service.create_wa_template(form_data)


@router.patch("/edit_wa_template")
async def edit_wa_template(
        data: schemas.EditWaTemplateSchema,
        service: WaTemplatesService = Depends(),
) -> schemas.AdminWaMasterTemplate:
    return await service.edit_wa_template(data)


@router.get("/get_wa_master_templates")
async def get_wa_master_templates(
        search_text: str | None = Query(None),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        active: bool = Query(False),
        service: WaTemplatesService = Depends(),
) -> list[schemas.AdminWaMasterListTemplate]:
    return await service.get_wa_master_templates(search_text, offset, limit, active)


@router.get("/get_wa_templates/{master_id}")
async def get_wa_templates(
        master_id: int,
        service: WaTemplatesService = Depends(),
) -> schemas.AdminWaMasterTemplate:
    return await service.get_wa_templates(master_id, None)


@router.delete("/delete_wa_template/{master_id}/{template_id}")
async def delete_wa_template(
        master_id: int,
        template_id: str,
        service: WaTemplatesService = Depends(),
) -> schemas.OkResponse:
    return await service.delete_template(master_id, template_id)


@router.delete("/delete_wa_template/{master_id}")
async def delete_wa_template_master(
        master_id: int,
        service: WaTemplatesService = Depends(),
) -> schemas.OkResponse:
    return await service.delete_template(master_id)
