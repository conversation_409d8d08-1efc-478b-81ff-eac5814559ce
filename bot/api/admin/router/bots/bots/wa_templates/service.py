import json
import logging

from aiowhatsapp import WhatsappApiClient
from aiowhatsapp.api.exceptions import UnknownWhatsappApiError, WhatsappApiError
from aiowhatsapp.schemas import (
    CreateMessageTemplate, EditMessageTemplate, GetMessageTemplatesParams,
    MessageComponentTextExample, MessageTemplateComponent, MessageTemplateObject,
)
from fastapi import HTTPException

import schemas
from api.admin.router.bots.bots.exceptions import BotInvalidTypeError
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.exceptions import AdminGroupBotNotFoundError
from core.media_manager import media_manager
from db import crud
from db.models import Chat, ClientBot, Group, User, WAMasterTemplate, WATemplate
from .exceptions import (
    WaMasterTemplateNotFoundError, WaTemplateNoBusinessAccountIdError,
    WaTemplateNotFoundError,
)
from .functions import (
    upload_wa_media_example, wa_master_list_template_to_schema,
    wa_master_template_to_schema,
)

debugger = logging.getLogger("debugger")


class WaTemplatesService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def edit_wa_master_template(
            self, data: schemas.EditWaMasterTemplate
    ) -> schemas.AdminWaMasterTemplate:
        master_template = await WAMasterTemplate.get(data.master_template_id)
        if not master_template:
            raise WaMasterTemplateNotFoundError()

        await master_template.update(description=data.description)

        return await self.get_wa_templates(master_template.id, None)

    async def edit_wa_template(
            self, data: schemas.EditWaTemplateSchema
    ) -> schemas.AdminWaMasterTemplate:
        template = None
        await self.edit_wa_master_template(data.master)

        data = data.template
        if data.template_id:
            template = await WATemplate.get(template_id=data.template_id)
            if not template:
                raise WaTemplateNotFoundError()

        bot = await self.__validate_wa_business_account_id()

        api = WhatsappApiClient(
            bot.token,
            bot.whatsapp_business_account_id,
            whatsapp_business_account_id=bot.whatsapp_business_account_id,
        )

        media = None
        wa_master = None
        try:
            wa_master = await WAMasterTemplate.get(data.master_template_id)
            if not wa_master:
                raise WaMasterTemplateNotFoundError()

            file = None
            for component in data.components:
                if component.type == "HEADER":
                    if component.media and "https" not in component.media:
                        file = component.media
                    if file:
                        data_dict = data.dict()
                        c_data = schemas.AdminCreateWaTemplateSchema(
                            data=schemas.CreateMessageTemplateSchema(
                                **data_dict,
                                name=wa_master.name,
                                category=wa_master.category,
                            ),
                            name=wa_master.name,
                            category=wa_master.category,
                            lang=data.language,
                        )
                        upload_file, upload_data = await upload_wa_media_example(
                            data_dict, c_data, bot
                        )
                        header_handle = None
                        if upload_data and upload_file:
                            for comp in upload_data.data.components:
                                if comp.type == "HEADER":
                                    header_handle = comp.example.header_handle
                                    break
                            for comp in data.components:
                                if comp.type == "HEADER" and header_handle:
                                    if not comp.example:
                                        comp.example = MessageComponentTextExample()
                                    comp.example.header_handle = header_handle

                            media = await media_manager.save_from_upload_file(
                                upload_file
                            )

            components: list[MessageTemplateComponent] = []
            if template:
                for comp in data.components:
                    if comp.type == "HEADER":
                        header_handle = template.get_header_handle()
                        if not comp.example:
                            comp.example = MessageComponentTextExample()
                        comp.example.header_handle = header_handle
                    components.append(MessageTemplateComponent(**comp.dict()))
                payload: EditMessageTemplate = EditMessageTemplate(
                    # WhatsApp not allow to edit category for approved templates
                    # category=wa_master.category,
                    components=components,
                )
                await api.edit_message_template(payload, template.template_id)
            else:
                for comp in data.components:
                    components.append(MessageTemplateComponent(**comp.dict()))

                payload: CreateMessageTemplate = CreateMessageTemplate(
                    name=wa_master.name,
                    category=wa_master.category,
                    language=data.language,
                    components=components,
                )
                template_response = await api.create_message_template(payload)
                print("*** TEMPLATE RESPONSE", template_response)

                payload: schemas.CreateMessageTemplateSchema = (
                    schemas.CreateMessageTemplateSchema(
                        name=wa_master.name,
                        category=wa_master.category,
                        language=data.language,
                        components=data.components,
                    ))
                template = await crud.create_wa_template(
                    schemas.AdminCreateWaTemplateSchema(
                        name=wa_master.name,
                        category=wa_master.category,
                        data=payload,
                        master_template_id=wa_master.id,
                        lang=data.language,
                    ),
                    wa_master.id,
                    data.language,
                    template_response.id,
                    template_response.status,
                    header_media_id=media.id if media else None,
                )
        except UnknownWhatsappApiError as ex:
            print(f"**** EX {ex}")
            error_message = (ex.error_data.get("error", {})).get("message", None)
            if error_message:
                raise HTTPException(400, error_message)
            title = ex.error_data.get("error_user_title", None)
            msg = ex.error_data.get("error_user_msg", None)
            text = None
            if title and msg:
                text = f"{title}\n{msg}"
            if text:
                raise HTTPException(400, msg)
        except WhatsappApiError as ex:
            title = ex.error_data.get("error_user_title", None)
            msg = ex.error_data.get("error_user_msg", None)
            text = None
            if title and msg:
                text = f"{title}\n{msg}"
            if text:
                raise HTTPException(400, msg)
        except Exception as ex:
            print(f"**** EX {ex}")
            raise HTTPException(400)

        if template:
            payload: CreateMessageTemplate = CreateMessageTemplate(
                name=wa_master.name,
                category=wa_master.category,
                language=data.language,
                components=data.components,
            )
            payload_dict = payload.dict()
            if media:
                media_id = media.id
            else:
                media_id = await template.get_header_media()
            if media:
                for component in payload_dict.get("components", []):
                    if component.get("type") == "HEADER":
                        component["media_id"] = media_id
                        break

            await template.update(
                template_data=payload_dict,
            )

        return await self.get_wa_templates(wa_master.id, None)

    async def create_wa_template(
            self,
            data: schemas.AdminCreateWaTemplate,
    ) -> schemas.AdminWaMasterTemplate | None:
        data_dict = json.loads(data.data)

        data.data = CreateMessageTemplate.parse_obj(data_dict)  # type: ignore

        bot = await self.__validate_wa_business_account_id()

        api = WhatsappApiClient(
            bot.token,
            bot.whatsapp_business_account_id,
            whatsapp_business_account_id=bot.whatsapp_business_account_id,
        )

        try:
            upload_data_dict = data_dict
            c_data = schemas.AdminCreateWaTemplateSchema(
                data=schemas.CreateMessageTemplateSchema(
                    **data_dict,
                ),
                name="",
                category=data.category,
                lang=data.lang,
            )
            upload_file, upload_data = await upload_wa_media_example(
                upload_data_dict, c_data, bot
            )
            header_handle = None
            if upload_data and upload_file:
                for component in upload_data.data.components:
                    if component.type == "HEADER":
                        header_handle = component.example.header_handle
                        break
                for component in data.data.components:
                    if component.type == "HEADER" and header_handle:
                        if not component.example:
                            component.example = MessageComponentTextExample()
                        component.example.header_handle = header_handle

            print(f"*** HEADER HANDLE {header_handle}")
            print(f"*** UPLOAD DATA {data.data}")
            template_response = await api.create_message_template(data.data)

            master_template_id = data.master_template_id
            if not master_template_id:
                master_template = await crud.create_wa_master_template(
                    data, bot.id, self.user.id,
                )
                master_template_id = master_template.id
            else:
                master_template = await WAMasterTemplate.get(master_template_id)

            if template_response:
                media = None
                if upload_file:
                    media = await media_manager.save_from_upload_file(upload_file)
                    print(f"*** MEDIA {media.id} {media.url}")
                    for com in data_dict.get("components", []):
                        if com.get("type") == "HEADER":
                            com["media_id"] = media.id
                            break

                payload: schemas.CreateMessageTemplateSchema = (
                    schemas.CreateMessageTemplateSchema(
                        name=master_template.name,
                        category=master_template.category,
                        language=data.lang,
                        components=[
                            schemas.MessageTemplateComponentSchema(**comp.dict())
                            for comp in data.data.components],
                    ))
                create_data = schemas.AdminCreateWaTemplateSchema(
                    name=master_template.name,
                    category=master_template.category,
                    data=payload,
                    master_template_id=master_template.id,
                    lang=data.lang,
                )

                template = await crud.create_wa_template(
                    create_data,
                    master_template_id,
                    data.lang,
                    template_response.id,
                    template_response.status,
                    header_media_id=media.id if media else None,
                )

                return await wa_master_template_to_schema(master_template, [template])
            return None
        except UnknownWhatsappApiError as ex:
            title = ex.error_data.get("error_user_title", None)
            msg = ex.error_data.get("error_user_msg", None)
            text = None
            if title and msg:
                text = f"{title}\n{msg}"
            if text:
                raise HTTPException(400, msg)
            return None
        except WhatsappApiError as ex:
            print(f"*** ERROR WA CREATE TEMPLATE {ex}")
            title = ex.error_data.get("error_user_title", None)
            msg = ex.error_data.get("error_user_msg", None)
            text = None
            if title and msg:
                text = f"{title}\n{msg}"
            if text:
                raise HTTPException(400, msg)
            return None
        except Exception as ex:
            print(f"*** ERROR WA CREATE TEMPLATE {ex}")
            raise HTTPException(400, ex)

    async def get_wa_templates(
            self, master_id, chat_id: int | None
    ) -> schemas.AdminWaMasterTemplate:
        lang: str | None = None

        chat = None
        if chat_id:
            chat = await Chat.get(chat_id)

        if chat:
            user = await User.get_by_id(chat.user_id)
            bot = await ClientBot.get(chat.bot_id)
            if user and bot:
                lang = await user.get_lang(bot)
            elif self.profile_id:
                profile = await Group.get(self.profile_id)
                if profile and profile.lang:
                    lang = profile.lang

        master_template = await WAMasterTemplate.get(master_id)

        templates = []
        if lang:
            templates = await WATemplate.get_list(
                master_template_id=master_id, lang=lang
            )

        if not templates:
            templates = await WATemplate.get_list(
                master_template_id=master_id
            )

        templates_list = []
        for template in templates:
            template = await self.__update_template_status(
                template, master_template.name
            )
            templates_list.append(template)

        master_template = await WAMasterTemplate.get(master_id)

        return await wa_master_template_to_schema(master_template, templates_list)

    async def get_wa_master_templates(
            self,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
            active: bool = False,
    ) -> list[schemas.AdminWaMasterListTemplate]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)
        bot = await ClientBot.get(group_id=self.profile_id)

        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        templates = await crud.get_wa_master_templates(
            self.profile_id, bot.id, self.user.id, search_text, offset, limit,
        )

        if active:
            master_templates = []
            for master in templates:
                _templates = await WATemplate.get_list(master_template_id=master.id)
                print(f"*** WA TEMPLATES {_templates}")
                is_any_not_approved = False if _templates else True
                for _template in _templates:
                    if _template.template_status != "APPROVED":
                        is_any_not_approved = True
                if not is_any_not_approved:
                    master_templates.append(master)

            return [wa_master_list_template_to_schema(template) for template in
                    master_templates]

        return [wa_master_list_template_to_schema(template) for template in templates]

    async def get_wa_template_status(self, params: GetMessageTemplatesParams) -> list[
        MessageTemplateObject]:
        bot = await self.__validate_wa_business_account_id()

        api = WhatsappApiClient(
            bot.token,
            bot.whatsapp_business_account_id,
            whatsapp_business_account_id=bot.whatsapp_business_account_id,
        )

        return await api.get_message_templates(params)

    async def delete_template(
            self, master_id: int, template_id: str | None = None
    ) -> schemas.OkResponse:
        try:
            wa_master = await WAMasterTemplate.get(master_id)
            if not wa_master:
                raise WaMasterTemplateNotFoundError()
            bot = await self.__validate_wa_business_account_id()
            api = WhatsappApiClient(
                bot.token,
                bot.whatsapp_business_account_id,
                whatsapp_business_account_id=bot.whatsapp_business_account_id,
            )
            await api.delete_message_template(wa_master.name, template_id)

            if not template_id:
                templates = await WATemplate.get_list(master_template_id=master_id)
                for template in templates:
                    await template.delete()
                await wa_master.delete()
            else:
                template = await WATemplate.get(template_id=template_id)
                if not template:
                    raise WaTemplateNotFoundError()
                await template.delete()

            return schemas.OkResponse()
        except UnknownWhatsappApiError as ex:
            title = ex.error_data.get("error_user_title", None)
            msg = ex.error_data.get("error_user_msg", None)
            text = None
            if title and msg:
                text = f"{title}\n{msg}"
            if text:
                raise HTTPException(400, msg)
        except WhatsappApiError as ex:
            title = ex.error_data.get("error_user_title", None)
            msg = ex.error_data.get("error_user_msg", None)
            text = None
            if title and msg:
                text = f"{title}\n{msg}"
            if text:
                raise HTTPException(400, msg)
        except Exception as ex:
            print(f"*** ERROR WA CREATE TEMPLATE {ex}")
            raise HTTPException(400, ex)

    async def __update_template_status(self, template: WATemplate, template_name: str):
        status_response = await self.get_wa_template_status(
            GetMessageTemplatesParams(
                fields="status,rejected_reason,id",
                name=template_name,
                language=template.lang,
            )
        )
        if status_response:
            wa_template = status_response[0]
            upd_dict: dict = {}
            if wa_template.status != template.template_status:
                upd_dict["template_status"] = wa_template.status
            if wa_template.rejected_reason != template.template_status_reason:
                upd_dict["template_status_reason"] = wa_template.rejected_reason
            if upd_dict:
                return await template.update(**upd_dict)
        return template

    async def __validate_wa_business_account_id(self) -> ClientBot:
        bot = await ClientBot.get(group_id=self.profile_id)

        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        if bot.bot_type != "whatsapp":
            raise BotInvalidTypeError()

        if not bot.whatsapp_business_account_id:
            raise WaTemplateNoBusinessAccountIdError()

        return bot
