import base64
from io import BytesIO
from fastapi import UploadFile

from aiowhatsapp import WhatsappApiClient
from aiowhatsapp.schemas import MessageComponentTextExample

import schemas
from db.models import ClientBot, WATemplate, WAMasterTemplate, MediaObject


async def template_obj_to_schema(data: dict):
    for com in data.get("components", []):
        if com.get("type") == "HEADER":
            if com.get("media_id", None):
                media_id = com.get("media_id")
                media = await MediaObject.get(media_id)
                com["media"] = media.url

    return schemas.AdminCreateMessageTemplate(**data)


async def wa_master_template_to_schema(
    master_template: WAMasterTemplate, templates: list[WATemplate]
) -> schemas.AdminWaMasterTemplate:

    return schemas.AdminWaMasterTemplate(
        id=master_template.id,
        name=master_template.name,
        description=master_template.description,
        category=master_template.category,
        templates=[schemas.AdminWATemplate(
            id=template.id,
            template_id=template.template_id,
            template_status=template.template_status,
            template_status_reason=template.template_status_reason,
            template_obj=await template_obj_to_schema(template.template_data),
            template_obj_update=None,
        ) for template in templates],
    )


def wa_master_list_template_to_schema(template: WAMasterTemplate) -> schemas.AdminWaMasterListTemplate:
    return schemas.AdminWaMasterListTemplate(
        id=template.id,
        name=template.name,
        description=template.description,
        category=template.category
    )


async def upload_wa_media_example(
    data_dict: dict,
    data: schemas.AdminCreateWaTemplateSchema,
    bot: ClientBot,
) -> tuple[UploadFile, schemas.AdminCreateWaTemplateSchema]:
    components = data_dict.get("components", [])
    upload_file = None
    print(f"*** COMPONENTS {components}")
    print(f"*** COMPONENTS {data_dict}")
    for com in components:
        if com.get("type") == "HEADER":
            if com.get("media", None):
                for component in data.data.components:
                    if component.type == "HEADER":
                        photo = com.get("media")
                        c_api = WhatsappApiClient(
                            bot.token,
                            bot.whatsapp_app_id,
                        )
                        if photo.startswith("data:"):
                            file_type, photo = photo.split(",", 1)
                            file_type = file_type.split(";")[0].split(":")[1]
                            missing_padding = len(photo) % 4
                            if missing_padding:
                                photo += '=' * (4 - missing_padding)
                            decoded_bytes = base64.b64decode(photo)
                            file_bytes = BytesIO(decoded_bytes)
                            upload_file = UploadFile(
                                file=file_bytes, filename="filename"
                            )
                            print(f"*** UPLOAD FILE {upload_file}")
                            document_media = await c_api.upload_resumable_media(
                                photo, file_type, len(decoded_bytes)
                            )
                            if not component.example:
                                component.example = MessageComponentTextExample()
                            component.example.header_handle = document_media.h
                            component.text = None
                            break

    return upload_file, data
