import logging
from collections import defaultdict
from http.client import HTTPException

import aiogram
from aiogram.utils.exceptions import ValidationError
from aiowhatsapp import WhatsappApiClient
from aiowhatsapp.api.exceptions import WhatsappApiError
from psutils.translator.schemas import TranslateObjectData
from sqlalchemy.exc import IntegrityError

import schemas
from api.admin.router.bots.bots.exceptions import (
    BotChangeTypeError, BotCreateDuplicateError, BotCreateUnknownError,
    BotCreateValidationError,
    BotInvalidDataError, BotInvalidTokenError, BotInvalidTypeDataError,
    BotInvalidTypeError, BotInvalidWBAIDError,
)
from api.admin.router.bots.bots.funcs import convert_bot_to_schema
from core.auth.services.scopes_checker import ScopesCheckerService
from core.bot.functions import (
    change_bot, create_bot, parse_register_whatsapp_bot,
    register_whatsapp_bot, setup_webhook,
)
from core.exceptions import AdminGroupBotNotFoundError, GroupNotFoundByIdError
from db import crud
from db.models import ClientBot, Group, User
from schemas import (
    AdminBotRegisterResultSchema, AdminBotRegisterStatus, AdminBotSettings,
)
from utils.text import f
from utils.translator import td

debugger = logging.getLogger("debugger")


class BotAdminService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_bot(
            self,
            message: str | None = None,
            no_error: bool = False
    ) -> schemas.AdminBotGetSchema | None:
        bot = await ClientBot.get(group_id=self.profile_id)
        if not bot:
            if no_error:
                return None
            raise AdminGroupBotNotFoundError(self.profile_id)

        return convert_bot_to_schema(bot, message)

    async def create_bot(
            self,
            data: schemas.AdminBotCreateSchema,
    ) -> schemas.AdminBotGetSchema:
        return await self.create_update_bot(data)

    async def update_bot(
            self,
            data: schemas.AdminBotEditSchema,
    ) -> schemas.AdminBotGetSchema:
        bot = await ClientBot.get(group_id=self.profile_id)

        if (data.bot_type == 'whatsapp' and data.whatsapp_business_account_id):
            api = WhatsappApiClient(bot.token, bot.whatsapp_from)
            is_valid = await api.validate_whatsapp_business_account_id(
                data.whatsapp_business_account_id
            )
            if not is_valid:
                raise BotInvalidWBAIDError()

        if bot.bot_type != data.bot_type:
            return await self.change_bot(data, bot)

        if bot.token != data.token:
            return await self.change_bot(data, bot)

        return await self.create_update_bot(data)

    async def change_bot(
            self,
            data: schemas.AdminBotEditSchema | None,
            bot: ClientBot | None = None,
    ) -> schemas.AdminBotGetSchema:
        if not bot:
            bot = await ClientBot.get(group_id=self.profile_id)
        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        bot_data = data.dict()
        bot_data["is_started"] = True

        try:
            message, is_update_error = await change_bot(
                bot.id, data.bot_type, bot, self.lang, bot_data
            )

            if is_update_error:
                raise BotChangeTypeError()

            return await self.get_bot(message)
        except aiogram.utils.exceptions.ValidationError as err:
            debugger.error(str(err), exc_info=True)
            raise BotInvalidDataError(str(err))
        except Exception as err:
            debugger.exception(str(err))
            raise HTTPException(500, "Internal Server Error")

    async def create_update_bot(self, data):
        await self.validate_bot_data(data)
        _data = data.dict()
        group = await Group.get(self.profile_id)
        _data["group"] = group
        _data["owner"] = group.owner
        if "whatsapp_app_name" in _data:
            del _data["whatsapp_app_name"]

        try:
            await create_bot(**_data)
        except ValidationError as e:
            if str(e) == 'Token is invalid!':
                message = await f("telegram bot token invalid error", self.lang)
                raise BotCreateValidationError(message)
            else:
                raise BotCreateValidationError(str(e))
        except IntegrityError as e:
            if e.code == "gkpj":
                logging.error(f"Error create bot\n{_data=}\n", exc_info=True)
                raise BotCreateDuplicateError()
            else:
                logging.error(e, exc_info=True)
                BotCreateUnknownError("database error")
        except WhatsappApiError as e:
            logging.error(e, exc_info=True)
            raise e
        except Exception as e:
            logging.error(e, exc_info=True)
            raise BotCreateUnknownError("")

        return await self.get_bot()

    async def get_bot_settings(
            self,
            group: Group | None = None,
            bot: ClientBot | None = None,
    ) -> schemas.AdminBotSettings:

        if not group:
            group = await Group.get(id=self.profile_id)

        if not group:
            raise GroupNotFoundByIdError(self.profile_id)

        if not bot:
            bot = await ClientBot.get(group_id=self.profile_id)

        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        return AdminBotSettings(
            review_type=group.review_type,
            need_chat_header=bot.need_chat_header,
            need_leave_chat_keyboard=bot.need_leave_chat_keyboard,
            is_show_order_button=bot.is_show_order_button,
            is_auto_answer=bot.is_auto_answer,
            auto_answer_delay=bot.auto_answer_delay,
            on_join_virtual_manager_id=bot.on_join_virtual_manager_id,
        )

    async def update_bot_settings(
            self,
            data: schemas.AdminBotSettings,
    ) -> schemas.AdminBotSettings:
        group = await Group.get(id=self.profile_id)
        bot = await ClientBot.get(group_id=self.profile_id)
        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        if data.review_type != group.review_type:
            await group.update(review_type=data.review_type)

        upd_bot_data = {}
        if data.need_chat_header != bot.need_chat_header:
            upd_bot_data["need_chat_header"] = data.need_chat_header

        if data.need_leave_chat_keyboard != bot.need_leave_chat_keyboard:
            upd_bot_data["need_leave_chat_keyboard"] = data.need_leave_chat_keyboard

        if (data.is_show_order_button != bot.is_show_order_button and bot.bot_type ==
                "telegram"):
            upd_bot_data["is_show_order_button"] = data.is_show_order_button

        if data.is_auto_answer != bot.is_auto_answer:
            upd_bot_data["is_auto_answer"] = data.is_auto_answer

        if data.auto_answer_delay != bot.auto_answer_delay:
            upd_bot_data["auto_answer_delay"] = data.auto_answer_delay

        if data.on_join_virtual_manager_id != bot.on_join_virtual_manager_id:
            upd_bot_data["on_join_virtual_manager_id"] = data.on_join_virtual_manager_id

        if upd_bot_data:
            await bot.update(**upd_bot_data)

        return await self.get_bot_settings(group, bot)

    async def setup_whatsapp_webhook(
            self,
    ) -> schemas.AdminBotResultSchema:
        bot = await ClientBot.get(group_id=self.profile_id)

        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        if bot.bot_type != "whatsapp":
            raise BotInvalidTypeError()
        message = await setup_webhook(bot.id, self.lang)
        return schemas.AdminBotResultSchema(status="ok", detail=message)

    async def register(
            self,
            data: schemas.AdminBotRegisterSchema,
    ) -> AdminBotRegisterResultSchema:
        bot = await ClientBot.get(group_id=self.profile_id)

        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        if bot.bot_type != "whatsapp":
            raise BotInvalidTypeError()

        response = await register_whatsapp_bot(bot.id, data.pin)
        message_text = await parse_register_whatsapp_bot(self.lang, response)

        return schemas.AdminBotRegisterResultSchema(
            status=AdminBotRegisterStatus.FAIL if "error" in response else
            AdminBotRegisterStatus.SUCCESS,
            detail=message_text
        )

    async def renew_app_name(
            self,
    ) -> schemas.AdminBotResultSchema:
        bot = await ClientBot.get(group_id=self.profile_id)

        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        if bot.bot_type != "whatsapp":
            raise BotInvalidTypeError()

        api = WhatsappApiClient(bot.token, bot.whatsapp_from)
        debug_token = await api.debug_token()
        app_name = debug_token.data.application

        await bot.update(whatsapp_app_name=app_name)

        return schemas.AdminBotResultSchema(status="ok", detail=app_name)

    async def validate_bot_data(
            self,
            data: schemas.AdminBotEditSchema | schemas.AdminBotCreateSchema,
    ) -> schemas.AdminBotValidateSchema:
        debugger.debug(f"{self.profile_id=}\nValidating bot {data=}")

        if data.bot_type not in ("whatsapp", "telegram"):
            raise BotInvalidTypeError()

        if not data.token:
            raise BotInvalidTokenError()

        if data.bot_type == "whatsapp":
            required_fields = ['whatsapp_app_secret', 'whatsapp_app_id',
                               'whatsapp_from']
            for field in required_fields:
                if not getattr(data, field, None):
                    raise BotInvalidTypeDataError(field)

        return schemas.AdminBotValidateSchema(result='success')

    async def get_custom_menu_buttons(self) -> list[
        schemas.AdminBotCustomMenuButtonSchema]:
        bot = await ClientBot.get(group_id=self.profile_id)

        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        group = await Group.get(self.profile_id)

        langs_to_translate = group.get_langs_list(False)

        custom_menu_buttons = await crud.get_custom_menu_buttons(
            bot.id, langs_to_translate
        )

        if custom_menu_buttons is None:
            return []

        grouped_buttons = defaultdict(lambda: {"button": None, "translations": {}})

        for button, translation in custom_menu_buttons:
            button_data = grouped_buttons[button.id]
            button_data["button"] = button

            if translation:
                button_data["translations"][translation.lang] = translation

        result = []

        for button_data in grouped_buttons.values():
            button = button_data["button"]
            translations = button_data["translations"]

            to_translate: dict[str, dict] = defaultdict(dict)
            for lang in langs_to_translate:
                to_translate[lang][button] = TranslateObjectData(
                    object=button,
                    translation=translations.get(lang),
                )

            translated = {}

            for lang, to_translate_lang in to_translate.items():
                translated[lang] = await td(
                    to_translate_lang, lang,
                    group.lang,
                    group_id=group.id,
                    is_auto_translate_allowed=group.is_translate,
                    fallback_to_original_on_error=False,
                )

            step_translations = {
                lang: translated_lang[button]
                for lang, translated_lang in translated.items()
            }

            if button is None:
                continue

            schema = schemas.AdminBotCustomMenuButtonSchema(
                **button.as_dict(),
                translations=step_translations
            )
            result.append(schema)

        return result

    async def set_custom_menu_buttons(
            self, data: list[schemas.SetAdminBotCustomMenuButtonSchema]
    ):
        bot = await ClientBot.get(group_id=self.profile_id)

        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        await crud.set_custom_menu_buttons(bot.id, data)

        return await self.get_custom_menu_buttons()

    async def set_whatsapp_business_account_id(
            self, data: schemas.AdminBotSetBusinessAccountIdSchema
    ):
        bot = await ClientBot.get(group_id=self.profile_id)

        if not bot:
            raise AdminGroupBotNotFoundError(self.profile_id)

        if bot.bot_type != "whatsapp":
            raise BotInvalidTypeError()

        if data.whatsapp_business_account_id:
            bot_data = bot.as_dict()
            bot_data["whatsapp_business_account_id"] = data.whatsapp_business_account_id

            update_dict = schemas.AdminBotEditSchema(**bot_data)
            await self.update_bot(update_dict)
            return schemas.OkResponse()
        return None
