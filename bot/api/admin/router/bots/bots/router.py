from fastapi import APIRout<PERSON>, Depends, Security

import schemas
from schemas import AdminBotRegisterResultSchema
from .service import BotAdminService

router = APIRouter()


@router.get("/")
async def get_bot(
        service: BotAdminService = Depends()
) -> schemas.AdminBotResponse:
    return schemas.AdminBotResponse(
        bot=await service.get_bot(no_error=True),
    )


@router.get("/settings")
async def get_bot_settings(
        service: BotAdminService = Depends()
) -> schemas.AdminBotSettings:
    return await service.get_bot_settings()


@router.patch("/settings")
async def update_bot_settings(
        data: schemas.AdminBotSettings,
        service: BotAdminService = Security(scopes=["bot:edit"]),
) -> schemas.AdminBotSettings:
    return await service.update_bot_settings(data)


@router.patch("/whatsapp/webhook")
async def setup_whatsapp_webhook(
        service: BotAdminService = Security(scopes=["bot:edit"]),
) -> schemas.AdminBotResultSchema:
    return await service.setup_whatsapp_webhook()


@router.patch("/whatsapp/register")
async def register_whatsapp_pin(
        data: schemas.AdminBotRegisterSchema,
        service: BotAdminService = Security(scopes=["bot:edit"]),
) -> AdminBotRegisterResultSchema:
    return await service.register(data)


@router.post("/whatsapp/renew_app_name")
async def renew_whatsapp_app_name(
        service: BotAdminService = Security(scopes=["bot:edit"]),
) -> schemas.AdminBotResultSchema:
    return await service.renew_app_name()


@router.patch("/")
async def update_bot(
        form_data: schemas.AdminBotEditSchema,
        service: BotAdminService = Security(scopes=["profile:admin"]),
) -> schemas.AdminBotGetSchema:
    return await service.update_bot(form_data)


@router.post("/")
async def create_bot(
        form_data: schemas.AdminBotCreateSchema,
        service: BotAdminService = Security(scopes=["profile:admin"]),
) -> schemas.AdminBotGetSchema:
    return await service.create_bot(form_data)


@router.patch("/change_type")
async def change_bot_type(
        form_data: schemas.AdminBotEditSchema,
        service: BotAdminService = Security(scopes=["profile:admin"]),
) -> schemas.AdminBotGetSchema:
    return await service.change_bot(form_data)


@router.post("/validate")
async def validate_bot_data(
        data: schemas.AdminBotEditSchema,
        service: BotAdminService = Depends(),
) -> schemas.AdminBotValidateSchema:
    return await service.validate_bot_data(data)


@router.get("/custom_menu_buttons")
async def get_custom_menu_buttons(
        service: BotAdminService = Depends()
) -> list[schemas.AdminBotCustomMenuButtonSchema]:
    return await service.get_custom_menu_buttons()


@router.patch("/custom_menu_buttons")
async def set_custom_menu_buttons(
        data: list[schemas.SetAdminBotCustomMenuButtonSchema],
        service: BotAdminService = Security(scopes=["bot:edit"]),
) -> list[schemas.AdminBotCustomMenuButtonSchema]:
    return await service.set_custom_menu_buttons(data)


@router.patch("/whatsapp/business_account_id")
async def set_whatsapp_business_account_id(
        data: schemas.AdminBotSetBusinessAccountIdSchema,
        service: BotAdminService = Security(scopes=["bot:edit"]),
) -> schemas.OkResponse:
    return await service.set_whatsapp_business_account_id(data)
