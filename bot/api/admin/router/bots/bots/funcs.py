from db.models import ClientBot
from schemas import AdminBotGetSchema


def convert_bot_to_schema(
        bot: ClientBot, message: str | None = None
) -> AdminBotGetSchema:

    bot_schema = AdminBotGetSchema(
        display_name=bot.display_name,
        link=bot.link,
        message=message,
        **bot.as_dict(),
    )
    if bot_schema.token:
        bot_schema.token = None
    if bot_schema.whatsapp_app_id:
        bot_schema.whatsapp_app_id = None
    if bot_schema.whatsapp_app_secret:
        bot_schema.whatsapp_app_secret = None
    if bot_schema.whatsapp_app_name:
        bot_schema.whatsapp_app_name = None
    if bot_schema.whatsapp_from:
        bot_schema.whatsapp_from = None
    if bot_schema.whatsapp_from_phone_number:
        bot_schema.whatsapp_from_phone_number = None

    return bot_schema
