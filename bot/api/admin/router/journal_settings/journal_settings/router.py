from fastapi import APIRouter, Depends, Query

import schemas
from .service import JournalSettingsService

router = APIRouter()


@router.get('/')
async def get_journal_settings(
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit client web pages. Max: 100"),
        service: JournalSettingsService = Depends()
) -> list[schemas.AdminJournalSettingListSchema]:
    return await service.get_journal_settings(offset=offset, limit=limit)


@router.post('/')
async def create_journal_setting(
        data: schemas.AdminJournalSettingCreateSchema,
        service: JournalSettingsService = Depends()
) -> schemas.AdminJournalSettingOneSchema:
    return await service.create_journal_setting(data)


@router.post('/next_suffix')
async def get_next_journal_setting_suffix(
        data: schemas.AdminJournalSettingMaxCountNameSchema,
        service: JournalSettingsService = Depends()
) -> int:
    return await service.get_next_journal_setting_suffix(data)
