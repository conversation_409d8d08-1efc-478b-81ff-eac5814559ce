from fastapi.encoders import jsonable_encoder

import schemas
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import (
    Group, User,
)


class JournalSettingsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_journal_settings(
            self,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminJournalSettingListSchema]:
        profile = await Group.get(self.profile_id)

        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        journal_settings = await crud.get_journal_settings(
            profile.id, self.user.id,
            offset=offset,
            limit=limit,
        )

        return [
            schemas.AdminJournalSettingListSchema(
                id=journal_setting.id,
                name=journal_setting.name,
                type=journal_setting.type,
                settings=journal_setting.settings,
                table_settings=journal_setting.table_settings,
                group_id=journal_setting.group_id,
                user_id=journal_setting.user_id,
            )
            for journal_setting in journal_settings
        ]

    async def create_journal_setting(
            self, data: schemas.AdminJournalSettingCreateSchema
    ) -> schemas.AdminJournalSettingListSchema:
        data.settings = jsonable_encoder(data.settings)
        
        journal_setting = await crud.create_journal_setting(
            self.profile_id, self.user.id, data,
        )

        return schemas.AdminJournalSettingListSchema(
            id=journal_setting.id,
            type=journal_setting.type,
            name=journal_setting.name,
            settings=journal_setting.settings,
            table_settings=journal_setting.table_settings,
            group_id=journal_setting.group_id,
            user_id=journal_setting.user_id,
        )

    async def get_next_journal_setting_suffix(
            self, data: schemas.AdminJournalSettingMaxCountNameSchema
    ) -> int:
        return await crud.get_next_journal_setting_suffix(
            data.type, self.profile_id, self.user.id
        )
