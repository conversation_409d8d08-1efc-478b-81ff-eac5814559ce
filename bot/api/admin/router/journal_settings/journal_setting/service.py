from dataclasses import asdict
from typing import Any

import exceptions
import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import (JournalSetting, User)


class JournalSettingService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",  # will be overridden in "write" routes
                "profile_id",
                "journal_setting_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.journal_setting_id: int = scopes.data.journal_setting_id

    def journal_setting_to_schema(
            self, journal_setting: JournalSetting,

    ):

        return schemas.AdminJournalSettingOneSchema(
            id=journal_setting.id,
            type=journal_setting.type,
            name=journal_setting.name,
            settings=journal_setting.settings,
            table_settings=journal_setting.table_settings,
            group_id=journal_setting.group_id,
            user_id=journal_setting.user_id,
        )

    async def get_journal_setting(self) -> JournalSetting:
        journal_setting = await crud.get_journal_setting(
            self.journal_setting_id, self.profile_id, self.user.id
        )
        if not journal_setting:
            raise exceptions.JournalSettingNotFoundError(self.journal_setting_id)
        return journal_setting

    async def update_journal_setting(
            self, data: schemas.AdminJournalSettingUpdateSchema
    ):
        updated_journal_setting = await crud.update_journal_setting(
            self.journal_setting_id, data
        )
        return self.journal_setting_to_schema(updated_journal_setting)

    async def delete_journal_setting(self):
        await crud.delete_journal_setting(self.journal_setting_id, self.user.id)
        return schemas.OkResponse()
