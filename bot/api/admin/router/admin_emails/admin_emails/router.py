from fastapi import APIRouter, Depends, Security

import schemas
from .service import AdminEmailsService

router = APIRouter()


@router.get("/")
async def get_admin_email_list(
    params: schemas.AdminListParams = Depends(),
    service: AdminEmailsService = Depends()
) -> list[schemas.AdminAdminEmailListSchema]:
    return await service.get_list(params)


@router.post("/")
async def create_admin_email(
    data: schemas.AdminCreateAdminEmailSchema,
    service: AdminEmailsService = Security(scopes=["profile:create", "me:write"])
) -> schemas.AdminAdminEmailSchema:
    return await service.create_admin_email(data)
