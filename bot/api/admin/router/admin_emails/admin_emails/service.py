from sqlalchemy.exc import IntegrityError

import schemas
from api.admin.base.list_service import AdminListService
from api.admin.router.admin_emails.functions import admin_email_to_admin_schema
from db import crud
from db.crud.store.admin_email.exceptions import AdminEmailDuplicateError


class AdminEmailsService(AdminListService[schemas.AdminAdminEmailListSchema]):
    schema_type = schemas.AdminAdminEmailListSchema
    get_objects_func = crud.get_admin_admin_email_list

    async def create_admin_email(self, data: schemas.AdminCreateAdminEmailSchema) -> schemas.AdminAdminEmailSchema:
        try:
            brand = await crud.get_brand_by_group(self.profile_id)

            admin_email = await crud.add_admin_email(brand_id=brand.id, creator_id=self.user.id, **data.dict(), )
            return await admin_email_to_admin_schema(admin_email)
        except IntegrityError as e:
            if "Duplicate entry" in str(e.orig):
                raise AdminEmailDuplicateError()
            raise
