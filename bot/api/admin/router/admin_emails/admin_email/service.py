from dataclasses import asdict
from typing import Any

from sqlalchemy.exc import IntegrityError

import schemas
from api.admin.router.admin_emails.functions import admin_email_to_admin_schema
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.crud.store.admin_email.exceptions import AdminEmailDuplicateError
from db.models import AdminEmail, User
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class AdminEmailService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: Any = asdict(scopes.data)  # dataclass

    async def get_admin_email(self, admin_email_id: int):
        admin_email = await AdminEmail.get(admin_email_id)
        if not admin_email:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "profile:read", self.available_data,
            )
        return admin_email

    async def update_admin_email(
            self, admin_email_id: int, data: schemas.AdminUpdateAdminEmailSchema
    ):
        try:
            brand = await crud.get_brand_by_group(self.profile_id)

            await crud.update_admin_email(
                admin_email_id=admin_email_id, brand_id=brand.id, **data.dict()
            )
            admin_email = await self.get_admin_email(admin_email_id)
            return await admin_email_to_admin_schema(admin_email)
        except IntegrityError as e:
            if "Duplicate entry" in str(e.orig):
                raise AdminEmailDuplicateError()
            raise

    async def delete_admin_email(self, admin_email_id: int):
        admin_email = await self.get_admin_email(admin_email_id)
        await admin_email.delete()
        return schemas.OkResponse()
