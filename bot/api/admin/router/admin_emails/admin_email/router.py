from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Path, Security

import schemas
from .service import AdminEmailService
from ..functions import admin_email_to_admin_schema

router = APIRouter(
    prefix="/{admin_email_id}"
)


@router.get("/")
async def get_admin_email(
    admin_email_id: int = Path(..., description="ID admin email"),
    service: AdminEmailService = Depends()
) -> schemas.AdminAdminEmailSchema:
    admin_email = await service.get_admin_email(admin_email_id)
    return await admin_email_to_admin_schema(admin_email)


@router.patch("/")
async def update_admin_email(
    data: schemas.AdminUpdateAdminEmailSchema,
    admin_email_id: int = Path(..., description="ID admin email"),
    service: AdminEmailService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AdminAdminEmailSchema:
    return await service.update_admin_email(admin_email_id, data)


@router.delete("/")
async def delete_admin_email(
    admin_email_id: int = Path(..., description="ID admin email"),
    service: AdminEmailService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.OkResponse:
    return await service.delete_admin_email(admin_email_id)
