from starlette import status

import schemas
from db.models import AdminEmail
from schemas import TypeAdminEmailEnum
from utils.exceptions import ErrorWithHTTPStatus


class AdminEmailDataError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin admin email data error"


async def admin_email_to_admin_schema(admin_email: AdminEmail):

    base_schema = schemas.AdminAdminEmailListSchema.from_orm(admin_email)
    return schemas.AdminAdminEmailSchema(
        **base_schema.dict(),
    )


def validate_data(data):
    if data.type_email == TypeAdminEmailEnum.STORE and data.store_id is None:
        raise AdminEmailDataError()
