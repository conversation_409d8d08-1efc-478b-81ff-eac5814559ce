from fastapi import APIRouter

import schemas
from core.chat.virtual_manager.interactives import (
    VMInteractive,
    VirtualManagerInteractiveDefinitionSchema,
)
from db import crud
from db.models import VirtualManager
from . import virtual_manager, virtual_managers

router = APIRouter(
    tags=["virtual_managers"]
)


@router.get("/virtual_managers/interactives-definition")
def get_interactives_definition() -> VirtualManagerInteractiveDefinitionSchema:
    return VMInteractive.build_all_definitions()


@router.get("/virtual_managers/{vm_id}/name")
async def get_virtual_manager_name_by_id(vm_id: int | str) -> schemas.VMNameResponse:
    try:
        vm_id_value: int = int(vm_id)
    except ValueError:
        vm_id_value: str = vm_id

    vm: VirtualManager = await crud.get_vm_by_id_or_name_id(vm_id_value)
    return schemas.VMNameResponse(
        data=vm
    )


profile_router = APIRouter(
    prefix="/{profile_id}/virtual_managers",
)

profile_router.include_router(virtual_managers.router)
profile_router.include_router(virtual_manager.router)

router.include_router(profile_router)
