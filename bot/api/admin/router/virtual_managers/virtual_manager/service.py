from dataclasses import asdict
from typing import Any

import exceptions
import schemas
from api.admin.router.virtual_managers.functions import vm_to_admin_schema
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.chat.virtual_manager.deep_link import V<PERSON>eepLink
from core.helpers import create_url_prefix_from_text
from core.media_manager import media_manager
from db import crud
from db.models import ClientBot, Group, User


class VMService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "vm:read",  # will be overridden in "write" routes
                "profile_id",
                "vm_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.vm_id: int = scopes.data.vm_id
        self.available_data: Any = asdict(scopes.data)  # dataclass

    async def get_vm(self, is_search_vm_name: bool = False):
        vm = await crud.get_vm_by_id_and_profile_id(self.vm_id, self.profile_id)
        if not vm:
            if is_search_vm_name:
                return None
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "vm:read", self.available_data,
            )
        return vm

    async def update_virtual_manager(self, data: schemas.AdminUpdateVirtualManagerData):
        vm = await self.get_vm()
        group = await Group.get(self.profile_id)

        data_dict = data.dict(exclude_unset=True)
        if not vm.name_id or "name_id" in data_dict:
            data_dict["name_id"] = await create_url_prefix_from_text(
                data.name_id or data.name or vm.name, group.lang
            )

        await crud.update_vm(vm, data_dict)
        return await vm_to_admin_schema(vm, self.user)

    async def delete_virtual_manager(self):
        vm = await self.get_vm()
        await vm.delete()
        return schemas.OkResponse()

    async def set_virtual_manager_steps(
            self, data: schemas.AdminSetVirtualManagerStepsData
    ):
        vm = await self.get_vm()
        group = await Group.get(self.profile_id)

        media_ids_by_step_id = {
            step.id: step.media_url for step in data.steps
            if
            step.media_url and not step.media_id
        }

        medias = dict(
            zip(
                media_ids_by_step_id.keys(),
                await media_manager.batch_download_media(
                    *media_ids_by_step_id.values(),
                )
            )
        )

        await crud.set_virtual_manager_steps(
            vm.id, data.steps, medias, group.get_langs_list(False)
        )
        return await vm_to_admin_schema(vm, self.user, group)

    async def get_vm_link(self):
        vm = await self.get_vm()
        bot = await ClientBot.get(group_id=self.profile_id)
        if not bot:
            raise exceptions.ProfileBotDoesNotExistError(self.profile_id)

        return schemas.VMLinkResponse(
            virtual_manager_id=vm.id,
            link=VMDeepLink(vm_id=vm.name_id or vm.id).to_str(
                bot.bot_type, bot.id_name
            ),
        )
