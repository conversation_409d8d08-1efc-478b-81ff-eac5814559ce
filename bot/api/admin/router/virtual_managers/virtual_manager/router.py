from fastapi import <PERSON>Router, Depends, Security

import schemas
from .service import VMService
from ..functions import vm_to_admin_schema

router = APIRouter(
    prefix="/{vm_id}"
)


@router.get("/")
async def get_virtual_manager(
        service: VMService = Depends()
) -> schemas.AdminVirtualManagerSchema:
    vm = await service.get_vm()
    return await vm_to_admin_schema(vm, service.user)


@router.patch("/")
async def update_virtual_manager(
        data: schemas.AdminUpdateVirtualManagerData,
        service: VMService = Security(scopes=["vm:edit", "me:write"]),
) -> schemas.AdminVirtualManagerSchema:
    return await service.update_virtual_manager(data)


@router.put("/steps")
async def set_virtual_manager_steps(
        data: schemas.AdminSetVirtualManagerStepsData,
        service: VMService = Security(scopes=["vm:edit", "me:write"]),
) -> schemas.AdminVirtualManagerSchema:
    return await service.set_virtual_manager_steps(data)


@router.delete("/")
async def delete_virtual_manager(
        service: VMService = Security(scopes=["vm:edit", "me:write"]),
) -> schemas.OkResponse:
    return await service.delete_virtual_manager()


@router.get("/link")
async def get_virtual_manager_link(
        service: VMService = Depends(),
) -> schemas.VMLinkResponse:
    return await service.get_vm_link()
