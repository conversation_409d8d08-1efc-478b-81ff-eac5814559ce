import schemas
from api.admin.base.list_service import AdminListService
from api.admin.router.virtual_managers.functions import vm_to_admin_schema
from core.helpers import create_url_prefix_from_text
from db import crud
from db.models import Group, MediaObject, VirtualManagerStep

from ..exceptions import VirtualManagerCreateCopyWithoutIdError


class VMsService(AdminListService[schemas.AdminVirtualManagerListSchema]):
    schema_type = schemas.AdminVirtualManagerListSchema
    get_objects_func = crud.get_admin_vm_list

    async def create_vm(
        self, data: schemas.AdminCreateVirtualManagerData
    ) -> schemas.AdminVirtualManagerSchema:
        profile = await Group.get(self.profile_id)

        if not data.name_id:
            data.name_id = await create_url_prefix_from_text(data.name, profile.lang)

        if data.create_mode == "create":
            vm = await crud.create_vm(profile, data, self.user)
        else:
            copy_id = data.copy_id if data.create_mode == "copy" else data.copy_exist_id
            if not copy_id:
                raise VirtualManagerCreateCopyWithoutIdError()
            vm = await crud.copy_vm(profile, data, copy_id, self.user)
        return await vm_to_admin_schema(vm, self.user, profile)

    async def get_virtual_manager_step(
        self, vm_step_id: int
    ) -> schemas.AdminVirtualManagerStepSchema:
        vm_step = await VirtualManagerStep.get(vm_step_id)
        step_media: MediaObject | None = await MediaObject.get(
            vm_step.media_id
        ) if vm_step.media else None
        return schemas.AdminVirtualManagerStepSchema(
            **schemas.VirtualManagerStepORMSchema.from_orm(vm_step).dict(),
            media_url=step_media.url if step_media else None,
            media_type=step_media.media_type if step_media else None,
        )
