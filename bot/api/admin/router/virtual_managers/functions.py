import schemas
from api.admin.helpers import check_object_access
from core.chat.virtual_manager.functions import get_virtual_manager_steps_schemas
from core.helpers import create_url_prefix_from_text
from db import crud
from db.models import Group, User, VirtualManager


async def vm_to_admin_schema(
        vm: VirtualManager,
        user: User,
        group: Group | None = None
):
    if not group:
        group = await Group.get(vm.group_id)

    if not vm.name_id:
        await crud.update_vm(
            vm, name_id=await create_url_prefix_from_text(vm.name, group.lang)
        )

    edit_allowed = await check_object_access(
        "vm",
        vm.id,
        vm.group_id, user.id,
        scope_name="edit",
    )
    read_allowed = edit_allowed or (
        await check_object_access(
            "vm",
            vm.id,
            vm.group_id, user.id,
            scope_name="read",
        )
    )

    return schemas.AdminVirtualManagerSchema(
        **vm.as_dict(),
        profile_id=vm.profile_id,
        read_allowed=read_allowed,
        edit_allowed=edit_allowed,
        steps=await get_virtual_manager_steps_schemas(vm.id, group),
    )
