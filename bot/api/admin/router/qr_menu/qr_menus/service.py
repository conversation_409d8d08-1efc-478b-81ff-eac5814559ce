import schemas
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.crud.scope.create import grand_scopes_to_created_object_sync
from db.models import MenuInStore, User
from ..functions import validate_invoice_template_and_store
from ..qr_menu.service import QrMenuService


class QrMenusService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_menu_list(
            self,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminQrMenuListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        menus_with_qr = await crud.get_menus_in_store_with_qr_media_objects(
            group_id=self.profile_id,
            search_text=search_text,
            offset=offset,
            limit=limit,
        )

        menu_dict = {}
        for menu, qr_codes in menus_with_qr:
            if menu.id not in menu_dict:
                menu_dict[menu.id] = {
                    "id": menu.id,
                    "comment": menu.comment,
                    "qr_media_urls": [],
                }
            for qr_code in qr_codes:
                if qr_code.media and qr_code.media.url:
                    menu_dict[menu.id]["qr_media_urls"].append(qr_code.media.url)

        return [schemas.AdminQrMenuListSchema(**menu) for menu in menu_dict.values()]

    async def get_menu_total_count(self, search_text: str | None = None) -> int:
        return await MenuInStore.get(
            group_id=self.profile_id,
            search_text=search_text,
            operation="count",
        )

    async def create_menu(
            self, data: schemas.AdminCreateQrMenuData
    ) -> schemas.AdminQrMenuSchema:
        await validate_invoice_template_and_store(
            self.profile_id, data.invoice_template_id, data.store_id
        )
        menu_in_store = await crud.create_menu_in_store(
            group_id=self.profile_id, **data.dict()
        )

        if self.user:
            grand_scopes_to_created_object_sync(
                "qr_menu", menu_in_store, self.user, {
                    "profile_id": self.profile_id,
                }
            )

        return await QrMenuService.menu_to_schema(menu_in_store)
