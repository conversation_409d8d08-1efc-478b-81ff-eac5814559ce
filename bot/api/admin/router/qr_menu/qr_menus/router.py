from fastapi import APIRouter, Depends, Query, Security

import schemas
from .service import QrMenusService

router = APIRouter()


@router.get("/")
async def get_menu_list(
    search_text: str | None = Query(None),
    offset: int | None = Query(None),
    limit: int = Query(10, description="limit menu. Max: 100"),
    service: QrMenusService = Depends()
) -> list[schemas.AdminQrMenuListSchema]:
    return await service.get_menu_list(search_text, offset, limit)


@router.get("/total_count")
async def get_menu_total_count(
    search_text: str | None = Query(None),
    service: QrMenusService = Depends()
) -> int:
    return await service.get_menu_total_count(search_text)


@router.post("/")
async def create_menu(
    data: schemas.AdminCreateQrMenuData,
    service: QrMenusService = Security(scopes=["me:write", "qr_menu:create"])
) -> schemas.AdminQrMenuSchema:
    return await service.create_menu(data)
