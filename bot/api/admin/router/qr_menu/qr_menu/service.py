from dataclasses import asdict
from typing import Any

import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from core.qr_media_object.service import QrMediaService
from db import crud
from db.models import (
    InvoiceTemplate, MenuInStore, MenuInStoreToQrMediaObject, Store, User,
)
from ..exceptions import AdminQrMenuInvalidQrMedia, AdminQrMenuNotFound
from ..functions import validate_invoice_template_and_store


class QrMenuService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "qr_menu:read",  # will be overridden in "write" routes
                "profile_id",
                "qr_menu_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.menu_id: int = scopes.data.qr_menu_id

    async def get_menu(self) -> schemas.AdminQrMenuSchema:
        menu = await MenuInStore.get(self.menu_id)
        if not menu:
            raise AdminQrMenuNotFound(menu_id=self.menu_id)

        return await self.menu_to_schema(menu)

    @classmethod
    async def menu_to_schema(cls, menu: MenuInStore) -> schemas.AdminQrMenuSchema:
        invoice_template = None
        store = None
        if menu.invoice_template_id:
            invoice_template = await InvoiceTemplate.get(menu.invoice_template_id)

        store_id = menu.store_id
        if menu.store_id:
            store = await Store.get(menu.store_id)
            if not store or store.is_deleted or not store.is_enabled:
                store_id = None

        brand = await crud.get_brand_by_group(menu.group_id)
        urls = []

        if brand:
            web_url = f"{brand.domain}qrmenu?qrmenu={menu.id}"
            urls.append(
                schemas.AdminQrMenuUrl(display_name=web_url, url=web_url, type="web")
            )
            bot = await crud.get_bot_by_brand(brand.id)
            if bot:
                bot_url = None
                if bot.bot_type == "telegram":
                    bot_url = f"https://t.me/{bot.username}?start=qrmenu-{menu.id}"
                elif bot.bot_type == "whatsapp":
                    bot_url = (
                        f"https://wa.me/"
                        f"{bot.whatsapp_from_phone_number}?"
                        f"text=qrmenu-{menu.id}"
                    )
                if bot_url:
                    urls.append(
                        schemas.AdminQrMenuUrl(
                            display_name=bot_url, url=bot_url, type="bot"
                        )
                    )

        return schemas.AdminQrMenuSchema(
            id=menu.id,
            comment=menu.comment,
            store_id=store_id,
            store_name=store.name if store else None,
            redirect_type=menu.redirect_type if menu.redirect_type else "web",
            payment_option=menu.payment_option if menu.payment_option else "disabled",
            invoice_template_id=invoice_template.id if invoice_template else None,
            invoice_template_name=invoice_template.title if invoice_template else None,
            is_e_menu=menu.is_e_menu,
            need_save_as_active=menu.need_save_as_active,
            urls=urls,
        )

    async def update_menu(
            self, data: schemas.AdminUpdateQrMenuData
    ) -> schemas.AdminQrMenuSchema:
        await validate_invoice_template_and_store(
            self.profile_id, data.invoice_template_id, data.store_id
        )
        menu = await MenuInStore.get(self.menu_id)
        if menu:
            await menu.update(**data.dict())

            return await self.menu_to_schema(menu)

        raise AdminQrMenuNotFound(menu_id=self.menu_id)

    async def delete_menu(self) -> schemas.OkResponse:
        menu = await MenuInStore.get(self.menu_id)
        if not menu:
            raise AdminQrMenuNotFound(menu_id=self.menu_id)
        await menu.update(is_deleted=True)

        return schemas.OkResponse()

    async def get_qr_media_objects(self) -> list[schemas.QrMediaObjectSchema]:
        qr_media_objects = (
            await crud.get_qr_media_objects_by_menu_in_store(self.menu_id))
        qr_service = QrMediaService()

        return [await qr_service.qr_media_object_to_schema(qr_media) for qr_media in
                qr_media_objects]

    async def __validate_menu_qr_media_object(self, qr_media_id: int) -> None:
        connect = await MenuInStoreToQrMediaObject.get(
            qr_media_object_id=qr_media_id, menu_in_store_id=self.menu_id
        )
        if not connect:
            raise AdminQrMenuInvalidQrMedia(media_id=qr_media_id, menu_id=self.menu_id)

    async def update_qr_media_object(
            self, data: schemas.UpdateQrMediaObject,
            qr_media_id: int,
    ) -> schemas.QrMediaObjectSchema:
        await self.__validate_menu_qr_media_object(qr_media_id)
        service = QrMediaService(qr_media_id=qr_media_id)

        return await service.update_qr_media_object(data)

    async def create_qr_media_object(
            self, data: schemas.CreateQrMediaObject
    ) -> schemas.QrMediaObjectSchema:
        service = QrMediaService()
        qr_media = await service.create_qr_media_object(data)
        await crud.connect_qr_media_object_to_menu_in_store(self.menu_id, qr_media.id)

        return await service.qr_media_object_to_schema(qr_media)

    async def delete_qr_media_object(self, qr_media_id: int) -> schemas.OkResponse:
        await self.__validate_menu_qr_media_object(qr_media_id)
        service = QrMediaService(qr_media_id=qr_media_id)
        await crud.delete_qr_media_object_from_menu_in_store(self.menu_id, qr_media_id)

        return await service.delete_qr_media_object()

    @classmethod
    async def delete_qr_media_additional_object(
            cls, qr_media_id: int, qr_additional_media_id: int
    ) -> schemas.OkResponse:
        service = QrMediaService(
            qr_media_id=qr_media_id, qr_media_additional_id=qr_additional_media_id
        )
        await service.delete_qr_media_additional_object()

        return schemas.OkResponse()
