from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class AdminQrMenuInvalidInvoiceTemplate(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin qr menu invalid invoice template error"

    def __init__(self, invoice_template_id: int):
        super().__init__(
            invoice_template_id=invoice_template_id,
            detail_data={
                "error_code": "qr_menu_invalid_invoice_template",
            }
        )


class AdminQrMenuInvalidStoreTemplate(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin qr menu invalid store error"

    def __init__(self, store_id: int):
        super().__init__(
            store_id=store_id,
            detail_data={
                "error_code": "qr_menu_invalid_store",
            }
        )


class AdminQrMenuNotFound(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "admin qr menu not found error"

    def __init__(self, menu_id: int):
        super().__init__(
            menu_id=menu_id,
            detail_data={
                "error_code": "qr_menu_not_found",
            }
        )


class AdminQrMenuInvalidQrMedia(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin qr menu invalid qr media"

    def __init__(self, media_id: int, menu_id: int):
        super().__init__(
            media_id=media_id,
            menu_id=menu_id,
            detail_data={
                "error_code": "qr_menu_invalid_qr_media",
            }
        )
