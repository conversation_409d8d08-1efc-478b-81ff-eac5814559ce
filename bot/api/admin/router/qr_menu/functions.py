from db.models import InvoiceTemplate, Store
from db import crud

from .exceptions import AdminQrMenuInvalidInvoiceTemplate, AdminQrMenuInvalidStoreTemplate


async def validate_invoice_template_and_store(
    profile_id: int,
    invoice_template_id: int | None,
    store_id: int | None
):
    if invoice_template_id:
        invoice_template = await InvoiceTemplate.get(invoice_template_id)
        if not invoice_template:
            raise AdminQrMenuInvalidInvoiceTemplate(invoice_template_id)
        if invoice_template.group_id != profile_id:
            raise AdminQrMenuInvalidInvoiceTemplate(invoice_template_id)

    if store_id:
        store = await Store.get(store_id)
        if not store:
            raise AdminQrMenuInvalidStoreTemplate(store_id)
        brand = await crud.get_brand_by_group(profile_id)
        if store.brand_id != brand.id:
            raise AdminQrMenuInvalidStoreTemplate(store_id)
