from collections import defaultdict
from decimal import Decimal
from itertools import groupby
from operator import itemgetter

import stripe
from psutils.translator.schemas import TranslateObjectData

import exceptions
import schemas
from config import (
    ADMIN_HOST, BILLING_STRIPE_FIRST_YEAR_COUPON, BILLING_STRIPE_INVOICE_TEMPLATE_ID,
    DEFAULT_TRIAL_PERIOD_DAYS,
    WEB_APP_ADMIN_URL,
)
from core.auth.services.scopes_checker import ScopesCheckerService
from core.billing.functions import (
    calculate_billing_item_amount,
    create_or_update_subscription, get_billing_product_usage, get_tax_ids_info,
    validate_tax_ids,
)
from core.billing.stripe_client import bstripe
from db import crud
from db.crud.billing.subscription_item.helpers import make_db_subscription_item_data
from db.models import (
    BillingProduct, BillingServicePacket, BillingServicePacketItem, BillingSubscription,
    BillingSubscriptionItem,
    Group, Scope, User,
)
from exceptions import (
    AuthNoObjectsOrHaveNotEnoughPermissionsError,
    AuthRoleRequiredError,
)
from loggers import <PERSON><PERSON><PERSON><PERSON>ger
from schemas import BillingRecurringInterval, BillingSubscriptionStatus
from utils.stripe import get_checkout_session_locale
from utils.translator import t, td


class AdminBillingService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:admin",
                "profile_id",
            )
    ):
        self.profile_id: int = scopes.data.profile_id
        self.user: User = scopes.user
        self.lang: str = scopes.lang

        self._profile: Group | None = None

    @property
    async def profile(self):
        if self._profile is None:
            self._profile = await Group.get(self.profile_id, status="enabled")
            if not self._profile:
                raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                    "profile", {
                        "profile_id": self._profile.id
                    }
                )
        return self._profile

    async def subscription_item_to_schema(
            self, item: BillingSubscriptionItem,
            product_code: schemas.BillingProductCode | None = None,
            stripe_product_id: str | None = None,
            packet_item: BillingServicePacketItem | None = None,
            translated: dict[str, str | None] | None = None,
            packet: BillingServicePacket | None = None,
    ):
        if not product_code or not stripe_product_id:
            product = await BillingProduct.get(item.product_id)
            product_code = product.code
            stripe_product_id = product.stripe_id

        if not packet_item:
            packet_item = await BillingServicePacketItem.get(
                item.packet_item_id
            )

        if not translated:
            if not packet:
                packet = await BillingServicePacket.get(packet_item.packet_id)
            translated = await t(
                packet_item,
                self.lang, packet.lang,
                group_id="internal",
                is_auto_translate_allowed=True,
            )

        item_amount = calculate_billing_item_amount(item)

        used_quantity = await get_billing_product_usage(
            self.profile_id, item.product_id, product_code,
        )

        if translated:
            name = translated.get("name") or packet_item.name
            description = translated.get("description") or packet_item.description
        else:
            name = packet_item.name
            description = packet_item.description

        return schemas.BillingSubscriptionItemSchema(
            **item.as_dict(),
            quantity_adjustable=packet_item.quantity_adjustable,
            min_quantity=packet_item.min_quantity,
            max_quantity=packet_item.max_quantity,
            name=name,
            description=description,
            product_code=product_code,
            stripe_product_id=stripe_product_id,
            used_quantity=used_quantity,
            amount=item_amount,
        )

    async def get_current_tariff_plan(self):
        profile = await self.profile
        subscriptions_data = await crud.billing.get_profile_subscription(
            profile.id, self.lang
        )

        subscription: BillingSubscription

        subscriptions = []
        service_packets = {}

        if not subscriptions_data:
            return None

        # key is original language, value is dict with key
        to_translate: dict[str, dict[str, TranslateObjectData]] = defaultdict(dict)

        for (
                _,
                _1,
                packet_item,
                packet_item_translation,
                packet,
                packet_translation,
                _2,
        ) in subscriptions_data:
            packet_key = f"packet-{packet.id}"
            item_key = f"item-{packet_item.id}"

            if packet_key not in to_translate[packet.lang]:
                to_translate[packet.lang][packet_key] = TranslateObjectData(
                    object=packet,
                    translation=packet_translation,
                    result_type="dict",
                )

            if item_key not in to_translate[packet.lang]:
                to_translate[packet.lang][item_key] = TranslateObjectData(
                    object=packet_item,
                    translation=packet_item_translation,
                    result_type="dict",
                )

        translated: dict[str, dict[str, str | None]] = {}

        for original_lang, to_translate_lang in to_translate.items():
            translated.update(
                await td(
                    to_translate_lang,
                    self.lang, original_lang,
                    group_id="internal",
                    is_auto_translate_allowed=True,
                )
            )

        for subscription, rows in groupby(subscriptions_data, key=itemgetter(0)):
            subscription_schema = schemas.BillingSubscriptionSchema(
                **subscription.as_dict(True),
                amount=Decimal(0),
                items=[]
            )

            for (
                    _,
                    subscription_item,
                    packet_item,
                    packet_item_translation,
                    packet,
                    packet_translation,
                    product,
            ) in rows:
                if packet.id not in service_packets:
                    packet_schema = (
                        schemas.BillingCurrentTariffPlanPacketSchema.from_orm(
                            packet
                        )
                    )

                    translated_packet = translated.get(f"packet-{packet.id}", {})

                    for key, value in translated_packet.items():
                        if value:
                            setattr(packet_schema, key, value)

                    service_packets[packet.id] = packet_schema

                item_schema = await self.subscription_item_to_schema(
                    subscription_item, product.code, product.stripe_id,
                    packet_item, translated.get(f"item-{packet_item.id}")
                )

                subscription_schema.amount += item_schema.amount
                subscription_schema.items.append(item_schema)

            subscriptions.append(subscription_schema)

        service_packets = list(
            sorted(
                service_packets.values(),
                key=lambda x: x.position,
            )
        )

        return schemas.BillingCurrentTariffPlanSchema(
            packet=service_packets[0],
            subscription=subscriptions[0]
        )

    async def is_subscribed(self):
        return await crud.billing.check_profile_subscription(self.profile_id, True)

    async def has_subscription_before(self):
        return await crud.billing.check_profile_subscription(self.profile_id, False)

    async def create_stripe_customer_if_not_exist(self) -> stripe.Customer:
        self._profile = await Group.get(
            self.profile_id,
            status="enabled",
            for_update=True,
        )
        profile = await self.profile
        if profile.stripe_customer_id:
            try:
                stripe_customer = await bstripe.customers.retrieve_async(
                    profile.stripe_customer_id
                )
            except Exception as e:
                JSONLogger("billing.stripe-customer").error(
                    "An error occurred while retrieving stripe_customer", e, {
                        "profile": profile
                    }
                )
                stripe_customer = None
            if stripe_customer.get("deleted"):
                stripe_customer = None
        else:
            stripe_customer = None

        if not stripe_customer:
            stripe_customer = await bstripe.customers.create_async(
                {
                    "name": profile.name,
                    "invoice_settings": {
                        "rendering_options": {
                            "template": BILLING_STRIPE_INVOICE_TEMPLATE_ID
                        }
                    },
                    "metadata": {
                        "7loc_profile_id": profile.id,
                    },
                }
            )
        elif (
                BILLING_STRIPE_INVOICE_TEMPLATE_ID and
                (
                        not stripe_customer.invoice_settings or
                        not stripe_customer.invoice_settings.rendering_options or
                        not stripe_customer.invoice_settings.rendering_options.template
                )
        ):
            stripe_customer = await bstripe.customers.update_async(
                stripe_customer.id, {
                    "invoice_settings": {
                        "rendering_options": {
                            "template": BILLING_STRIPE_INVOICE_TEMPLATE_ID
                        }
                    },
                }
            )

        await profile.update(stripe_customer_id=stripe_customer.id)
        return stripe_customer

    async def make_setup_payment_method_session(
            self, data: schemas.MakeSetupPaymentMethodSessionData
    ):
        await self.create_stripe_customer_if_not_exist()
        profile = await self.profile

        session = await bstripe.checkout.sessions.create_async(
            {
                "mode": "setup",
                "currency": data.currency,
                "ui_mode": "embedded",
                "locale": get_checkout_session_locale(self.lang),
                "redirect_on_completion": "if_required",
                "return_url": f"{WEB_APP_ADMIN_URL}/{self.lang}/{self.profile_id}/",
                "billing_address_collection": "required",
                "client_reference_id": self.profile_id,
                "tax_id_collection": {"enabled": True},
                "customer_update": {
                    "name": "auto",
                    "address": "auto",
                },
                "customer": profile.stripe_customer_id,
                "setup_intent_data": {
                    "metadata": {
                        "7loc_profile_id": profile.id,
                    }
                },
            }
        )

        return schemas.StripeCheckoutSessionClientSecretResponse(
            client_secret=session.client_secret,
        )

    @staticmethod
    def payment_method_to_schema(
            payment_method: stripe.PaymentMethod,
            is_detached: bool = False
    ):
        payment_method_data = getattr(payment_method, payment_method.type, {})

        if payment_method.type == "card":
            display_name = payment_method.card.brand.upper()
        else:
            display_name = " ".join(
                map(
                    lambda x: x.capitalize(),
                    payment_method.type.split("_")
                )
            )

        return schemas.BillingPaymentMethodSchema(
            id=payment_method.id,
            type=payment_method.type,
            display_name=display_name,
            billing_details=payment_method.billing_details,
            card_brand=(
                payment_method.card.brand
                if payment_method.type == "card"
                else None
            ),
            exp_year=payment_method_data.get("exp_year"),
            exp_month=payment_method_data.get("exp_month"),
            last4=payment_method_data.get("last4"),
            data=dict(payment_method),
            is_detached=is_detached,
        )

    async def get_payment_methods(self):
        await self.create_stripe_customer_if_not_exist()
        profile = await self.profile

        payment_methods = await bstripe.customers.payment_methods.list_async(
            profile.stripe_customer_id,
            {
                "allow_redisplay": "always",
            }
        )

        return list(map(self.payment_method_to_schema, payment_methods))

    async def detach_payment_method(self, payment_method_id: str):
        await self.profile

        result = await bstripe.payment_methods.detach_async(payment_method_id)
        return self.payment_method_to_schema(result, True)

    async def get_tax_ids_info(self):
        customer = await self.create_stripe_customer_if_not_exist()
        return await get_tax_ids_info(customer.id)

    async def subscribe(self, data: schemas.BillingSubscribeData):
        profile = await self.profile
        is_platform_admin = await crud.check_access_to_action(
            "platform:admin", "user", self.user.id,
        )

        if data.trial_period_days_override and not is_platform_admin:
            raise AuthRoleRequiredError("platform:admin")

        only_public = not is_platform_admin

        to_subscribe_data = await crud.billing.get_to_subscribe_data(
            data, profile, only_public
        )

        subscription_data = to_subscribe_data.subscription_data
        subscriptions_to_cancel = to_subscribe_data.subscriptions_to_cancel
        promo_code = to_subscribe_data.promo_code

        has_subscription_before = await self.has_subscription_before()

        if (
                not is_platform_admin and
                not (
                        subscription_data.packet.trial_allowed and
                        not has_subscription_before
                ) and
                not subscription_data.packet.is_free_plan and
                not data.payment_method
        ):
            raise exceptions.BillingPaymentMethodRequiredError()

        stripe_customer = await self.create_stripe_customer_if_not_exist()
        tax_ids_info, verification_documents = await validate_tax_ids(profile)

        logger = JSONLogger(
            "billing",
            "Subscribe", {
                "input_data": data,
                "to_subscribe_data": to_subscribe_data,
                "profile": profile,
            }
        )

        new_stripe_data = {
            "automatic_tax": {"enabled": bool(stripe_customer.address)},
            "collection_method": "charge_automatically",
            "default_payment_method": data.payment_method,
            "description": subscription_data.packet.name,
            "items": [
                         {
                             "id": item_data.existing_stripe_item_id,
                             "metadata": {
                                 "7loc_item_id": item_data.item.id,
                                 "7loc_packet_id": subscription_data.packet.id,
                             },
                             "price": item_data.item.stripe_price_id,
                             "quantity": (
                                 item_data.quantity
                                 if not item_data.item.meter_id else None
                             ),
                         }
                         for item_data in subscription_data.items
                     ] + [
                         {
                             "id": id_to_delete,
                             "deleted": True,
                         }
                         for id_to_delete in subscription_data.items_to_delete
                     ],
            "metadata": {
                "7loc_profile_id": profile.id,
            },
            "proration_behavior": "always_invoice",
            "payment_behavior": (
                "allow_incomplete"
                if data.payment_method
                else "default_incomplete"
            ),
            "payment_settings": {
                "save_default_payment_method": "on_subscription",
            },
            "expand": ["items.data.price.tiers"],
        }

        if (
                not has_subscription_before and
                BILLING_STRIPE_FIRST_YEAR_COUPON and
                subscription_data.packet.recurring_interval ==
                BillingRecurringInterval.YEAR
        ):
            new_stripe_data["discounts"] = [
                {"coupon": BILLING_STRIPE_FIRST_YEAR_COUPON}
            ]
        elif (
                promo_code and
                promo_code.stripe_coupon and
                subscription_data.packet.recurring_interval !=
                BillingRecurringInterval.YEAR
        ):
            new_stripe_data["discounts"] = [
                {"coupon": promo_code.stripe_coupon}
            ]

        if subscription_data.packet.billing_amount_threshold:
            new_stripe_data["billing_thresholds"] = {
                "amount_gte": subscription_data.packet.billing_amount_threshold,
                "reset_billing_cycle_anchor": False,
            }

        created_subscription = None
        create_or_update = "create"
        try:
            if subscription_data.existing_subscription_stripe_id:
                try:
                    create_or_update = "update"
                    created_subscription = await bstripe.subscriptions.update_async(
                        subscription_data.existing_subscription_stripe_id,
                        new_stripe_data,
                    )
                    need_create = False
                except Exception as e:
                    logger.error(
                        "An error occurred while updating existing subscription", e
                    )
                    subscriptions_to_cancel.append(
                        subscription_data.existing_subscription_stripe_id
                    )
                    need_create = True
            else:
                need_create = True

            if need_create:
                create_or_update = "create"

                if data.disable_trial_period or data.trial_period_days_override == 0:
                    trial_period_days = None
                elif data.trial_period_days_override:
                    trial_period_days = data.trial_period_days_override
                elif promo_code:
                    trial_period_days = promo_code.trial_period_days or None
                elif (
                        not has_subscription_before and
                        subscription_data.packet.trial_allowed
                ):
                    trial_period_days = DEFAULT_TRIAL_PERIOD_DAYS
                else:
                    trial_period_days = None

                new_stripe_data.update(
                    {
                        "currency": subscription_data.packet.currency,
                        "customer": profile.stripe_customer_id,
                        "trial_period_days": trial_period_days,
                        "trial_settings": {
                            "end_behavior": {
                                "missing_payment_method": "create_invoice",
                            }
                        } if trial_period_days else None,
                        "items": [
                            {
                                **item,
                                "id": None,
                                "deleted": None,
                            }
                            for item in new_stripe_data["items"]
                        ]
                    }
                )
                created_subscription = await bstripe.subscriptions.create_async(
                    new_stripe_data
                )
        finally:
            succeeded_or_failed = "succeeded" if created_subscription else "failed"
            logger.debug(
                (
                    f"{create_or_update.capitalize()} subscription "
                    f"{subscription_data.packet.currency}, "
                    f"{subscription_data.packet.recurring_interval}"
                    f"({subscription_data.packet.recurring_interval_count})"
                ),
                succeeded_or_failed.upper(),
                {
                    "subscription_data": subscription_data,
                    "new_stripe_data": new_stripe_data,
                    "created_subscription": created_subscription,
                    "subscriptions_to_cancel": subscriptions_to_cancel,
                }
            )

        for subscription_id in subscriptions_to_cancel:
            try:
                result = await bstripe.subscriptions.cancel_async(subscription_id)
                logger.debug(
                    "Subscription cancelled", {
                        "subscription_id": subscription_id,
                        "result": result,
                    }
                )
            except Exception as e:
                logger.debug(
                    "An error occurred while cancelling subscription", e, {
                        "subscription_id": subscription_id,
                    }
                )

        await create_or_update_subscription(
            created_subscription, logger,
            tax_ids_info, verification_documents
        )
        return await self.get_current_tariff_plan()

    async def update_subscription_payment_method(
            self, subscription_id: int,
            data: schemas.BillingSubscriptionUpdatePaymentMethodData,
    ):
        subscription = await BillingSubscription.get(
            id=subscription_id,
            group_id=self.profile_id,
        )

        if not subscription:
            raise exceptions.BillingSubscriptionNotFoundError(subscription.id)
        if (
                data.resume_subscription and
                subscription.status != BillingSubscriptionStatus.PAUSED
        ):
            raise exceptions.BillingSubscriptionCannotBeResumedError()

        profile = await self.profile
        stripe_customer = await self.create_stripe_customer_if_not_exist()
        tax_ids_info, verification_documents = await validate_tax_ids(profile)

        logger = JSONLogger(
            "billing",
            "update_subscription_default_payment_method", {
                "input_data": data,
                "subscription": subscription,
            }
        )

        result = "error"
        try:
            stripe_subscription = await bstripe.subscriptions.update_async(
                subscription.stripe_id,
                {
                    "default_payment_method": data.payment_method,
                    "automatic_tax": {"enabled": bool(stripe_customer.address)},
                }
            )
            logger.add_data({"stripe_subscription": stripe_subscription})
            result = "success"
        finally:
            logger.debug("update payment method", result)

        if data.resume_subscription:
            result = "error"
            try:
                stripe_subscription = await bstripe.subscriptions.resume_async(
                    subscription.stripe_id,
                )
                logger.add_data({"stripe_subscription": stripe_subscription})
                result = "success"
            finally:
                logger.debug("resume subscription", result)

        stripe_subscription = await bstripe.subscriptions.retrieve_async(
            stripe_subscription.id, {
                "expand": ["latest_invoice"]
            }
        )
        if stripe_subscription.latest_invoice.status == "open":
            error = None
            try:
                await bstripe.invoices.pay_async(
                    stripe_subscription.latest_invoice.id, {
                        "payment_method": data.payment_method,
                    }
                )
                result = "success"
            except Exception as e:
                result = "error"
                error = e
            finally:
                args = ["pay latest invoice", result]
                if error:
                    args.append(error)
                logger.debug(*args)

            stripe_subscription = await bstripe.subscriptions.retrieve_async(
                stripe_subscription.id
            )

        await create_or_update_subscription(
            stripe_subscription, logger, tax_ids_info, verification_documents
        )
        return await self.get_current_tariff_plan()

    async def update_subscription_item(
            self, item_id: int,
            data: schemas.UpdateBillingSubscriptionItemData
    ):
        db_data = await crud.billing.get_subscription_and_item_by_item(
            self.profile_id, item_id
        )
        if not db_data:
            raise exceptions.BillingSubscriptionItemNotFoundError(item_id)
        item, subscription = db_data

        logger = JSONLogger(
            "billing",
            "update_subscription_item", {
                "input_data": data,
                "db_data": db_data,
            }
        )

        result = "error"
        try:
            stripe_item = await bstripe.subscription_items.update_async(
                item.stripe_item_id, {
                    "quantity": data.quantity,
                    "proration_behavior": "always_invoice",
                    "payment_behavior": "error_if_incomplete",
                    "expand": ["price.tiers"]
                },
            )
            logger.add_data({"stripe_item": stripe_item})
            result = "success"
        finally:
            logger.debug(result)

        packet_item = await BillingServicePacketItem.get(item.packet_item_id)

        await item.update(
            make_db_subscription_item_data(
                subscription,
                packet_item,
                stripe_item,
                item.meter_event_name,
            )
        )

        return await self.subscription_item_to_schema(item)

    async def make_billing_portal_session(self):
        customer = await self.create_stripe_customer_if_not_exist()
        profile = await self.profile

        session = await bstripe.billing_portal.sessions.create_async(
            {
                "customer": customer.id,
                "return_url": f"{ADMIN_HOST}/{self.lang}/{profile.id}/billing"
            }
        )

        return schemas.StripeBillingPortalSessionResponse(
            url=session.url,
        )

    async def get_available_units(
            self,
            product_code: schemas.BillingProductCode,
            params: schemas.BillingAvailableUnitsParams,
    ):
        match product_code:
            case schemas.BillingProductCode.PRODUCT:
                required_action = "product:read"
            case schemas.BillingProductCode.STORE:
                required_action = "store:read"
            case schemas.BillingProductCode.VM:
                required_action = "vm:read"
            case _:
                required_action = "profile:admin"

        available_data = {"profile_id": self.profile_id}
        if not await crud.check_access_to_action(
                required_action, "user", self.user.id,
                available_data
        ):
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                required_action, available_data, *available_data.keys()
            )

        item = await crud.billing.get_subscription_item_for_product(
            self.profile_id,
            product_code,
        )

        if item:
            available_quantity = (
                item.quantity
                if item.usage_type == schemas.BillingUsageType.LICENSED
                else "inf"
            )
            product = await BillingProduct.get(item.product_id)
        else:
            available_quantity = 0

            product = await BillingProduct.get(
                code=product_code,
                is_deleted=False,
            )

        used_quantity = await get_billing_product_usage(
            self.profile_id, product.id, product_code
        )

        return schemas.BillingAvailableUnitsResponse(
            available=(
                    available_quantity == "inf" or
                    available_quantity >= used_quantity + params.required_quantity
            ),
            required_quantity=params.required_quantity,
            product_code=product_code,
            info=(
                schemas.BillingAvailableUnitsInfo(
                    item=await self.subscription_item_to_schema(
                        item, product_code, product.stripe_id
                    ),
                    subscription_currency=(
                        (await BillingSubscription.get(item.subscription_id)).currency
                    )
                ) if await crud.check_access_to_action(
                    "profile:admin", "user", self.user.id,
                    {"profile_id": self.profile_id},
                ) and item else None
            )
        )

    async def get_products_usages(self, params: schemas.GetBillingProductsUsagesParams):
        products = await crud.billing.get_products_by_codes(params.products)
        return {
            product.code: await get_billing_product_usage(
                self.profile_id, product.id, product.code
            )
            for product in products
        }

    async def activate_billing_for_profile(self):
        if not await crud.check_access_to_action(
                "billing:tester", "profile", self.profile_id
        ):
            await Scope.create(
                scope="billing:tester",
                target="profile",
                profile_id=self.profile_id,
            )
        return schemas.OkResponse()
