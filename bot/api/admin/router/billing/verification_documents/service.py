import exceptions
import schemas
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.media_manager import media_manager
from db import crud
from db.models import Group, GroupVerificationDocument, MediaObject, User

MAX_DOCUMENT_PER_PROFILE = 10


class VerificationDocumentService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:admin",
                "profile_id",
            )
    ):
        self.profile_id: int = scopes.data.profile_id
        self.user: User = scopes.user
        self.lang: str = scopes.lang

        self._profile: Group | None = None

    @property
    async def profile(self):
        if self._profile is None:
            self._profile = await Group.get(self.profile_id, status="enabled")
            if not self._profile:
                raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                    "profile", {
                        "profile_id": self._profile.id
                    }
                )
        return self._profile

    @classmethod
    def document_to_schema(
            cls,
            document: GroupVerificationDocument,
            media: MediaObject,
    ):
        return schemas.VerificationDocumentSchema(
            id=document.id,
            media_url=media.url,
            mime_type=media.mime_type,
            file_name=media.original_file_name or media.file_name,
            comment=document.comment,
        )

    async def get_document_list(self):
        profile = await self.profile
        documents = await crud.get_verification_document_list(profile.id)

        return [
            self.document_to_schema(*el)
            for el in documents
        ]

    async def add_documents(self, data: schemas.AddVerificationDocumentsData):
        profile = await self.profile

        if await crud.get_verification_document_count(
                profile.id
        ) + len(data.files) > MAX_DOCUMENT_PER_PROFILE:
            raise exceptions.MaximumVerificationDocumentCountReachedError(
                MAX_DOCUMENT_PER_PROFILE
            )

        medias = await media_manager.batch_save_from_upload_file(*data.files)
        documents = await crud.add_verification_documents(profile, medias.values())
        return [
            self.document_to_schema(document, media)
            for document, media in zip(documents, medias.values())
        ]

    async def get_document(self, document_id: int):
        profile = await self.profile
        document = await GroupVerificationDocument.get(
            id=document_id,
            group_id=profile.id,
        )
        if not document:
            raise exceptions.VerificationDocumentNotFoundError(document_id)
        return document

    async def update_document(
            self, document_id: int,
            data: schemas.UpdateVerificationDocumentData,
    ):
        document = await self.get_document(document_id)
        await document.update(data.dict())

        media = await MediaObject.get(document.media_id)
        return self.document_to_schema(document, media)

    async def delete_document(self, document_id: int):
        document = await self.get_document(document_id)
        await document.delete()
        return schemas.OkResponse()
