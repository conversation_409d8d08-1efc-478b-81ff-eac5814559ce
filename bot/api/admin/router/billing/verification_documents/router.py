from fastapi import APIRouter, Depends, Security

import schemas
from api.admin.router.billing.verification_documents.service import \
    VerificationDocumentService

router = APIRouter(
    prefix="/verification_documents",
)


@router.get("/")
async def get_verification_document_list(
        service: VerificationDocumentService = Depends()
) -> list[schemas.VerificationDocumentSchema]:
    return await service.get_document_list()


@router.post("/")
async def add_verification_documents(
        data: schemas.AddVerificationDocumentsData = Depends(),
        service: VerificationDocumentService = Security(scopes=["me:write"]),
) -> list[schemas.VerificationDocumentSchema]:
    return await service.add_documents(data)


@router.patch("/{document_id}")
async def update_verification_document(
        document_id: int,
        data: schemas.UpdateVerificationDocumentData = Depends(),
        service: VerificationDocumentService = Security(scopes=["me:write"]),
) -> schemas.VerificationDocumentSchema:
    return await service.update_document(document_id, data)


@router.delete("/{document_id}")
async def delete_verification_document(
        document_id: int,
        service: VerificationDocumentService = Security(scopes=["me:write"]),
) -> schemas.OkResponse:
    return await service.delete_document(document_id)
