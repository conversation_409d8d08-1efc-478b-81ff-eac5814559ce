from fastapi import APIRouter, Path, Security

import schemas
from .service import SortService

router = APIRouter(
    prefix="/{profile_id}/sort",
    tags=["sort"]
)


@router.patch("/reorder/{object_name}")
async def reorder(
    data: schemas.UpdateObjectPosition,
    service: SortService = Security(scopes=["me:write", "qr_menu:edit"]),
    object_name: schemas.StoreObjectType = Path(),
) -> schemas.OkResponse:
    return await service.reorder(object_name, data)
