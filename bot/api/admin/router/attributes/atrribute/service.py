from dataclasses import asdict
from typing import Any

import schemas
from api.admin.exceptions import AdminFieldCannotBeEmptyError
from api.admin.functions import validate_object_id
from api.admin.helpers import validate_non_empty_fields_and_translations
from api.admin.router.attributes.functions import attribute_to_admin_schema
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Group, StoreAttribute, User
from utils.text import f


class AttributeService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "attribute:read",  # will be overridden in "write" routes
                "profile_id",
                "attribute_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.attribute_id: int = scopes.data.attribute_id

    # noinspection PyMethodMayBeStatic
    async def attribute_to_schema(
            self, attribute: StoreAttribute, profile: Group | None = None
    ):
        if not profile:
            profile = await Group.get(self.profile_id)
        return await attribute_to_admin_schema(attribute, profile, self.user.id)

    async def get_attribute(self) -> StoreAttribute:
        attribute = await crud.get_attribute_by_id_and_profile_id(
            self.attribute_id, self.profile_id
        )
        if not attribute:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "attribute:read", self.available_data,
            )
        return attribute

    async def update_attribute(self, data: schemas.AdminUpdateAttributeData):

        if not data.attribute_id:
            raise AdminFieldCannotBeEmptyError(
                await f("admin forms product id field", lang=self.lang)
            )

        await validate_object_id(
            StoreAttribute, self.profile_id, data.attribute_id, self.attribute_id
        )

        attribute = await self.get_attribute()
        profile = await Group.get(self.profile_id)

        data_only_set = data.dict(exclude_unset=True)
        validate_non_empty_fields_and_translations(data_only_set, attribute, "name")
        await crud.update_attribute(attribute, data, profile.get_langs_list())
        return await self.attribute_to_schema(attribute, profile)

    async def delete_attribute(self):
        attribute = await self.get_attribute()
        await attribute.update(is_deleted=True)
        return schemas.OkResponse()
