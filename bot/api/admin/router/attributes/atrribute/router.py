from fastapi import APIRouter, Depends, Security

import schemas
from .service import AttributeService

router = APIRouter(
    prefix="/{attribute_id}"
)


@router.get("/")
async def get_attribute(
        service: AttributeService = Depends()
) -> schemas.AdminAttributeSchema:
    attribute = await service.get_attribute()
    return await service.attribute_to_schema(attribute)


@router.patch("/")
async def update_attribute(
        data: schemas.AdminUpdateAttributeData,
        service: AttributeService = Security(scopes=["me:write", "attribute:edit"])
) -> schemas.AdminAttributeSchema:
    return await service.update_attribute(data)


@router.delete("/")
async def delete_attribute(
        service: AttributeService = Security(scopes=["me:write", "attribute:edit"])
) -> schemas.OkResponse:
    return await service.delete_attribute()
