import logging

import schemas
from api.admin.exceptions import AdminFieldNotUnique
from api.admin.functions import validate_object_id
from api.admin.helpers import (
    get_or_create_brand,
    validate_non_empty_fields_and_translations,
)
from api.admin.router.attributes.functions import (
    attribute_to_admin_list_schema,
    attribute_to_admin_schema,
)
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.helpers import get_next_object_id
from db import crud
from db.models import Group, StoreAttribute, User
from schemas import AdminValidateObjectIdSchema
from utils.text import f


class AttributesService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_attributes_list(
            self,
            attribute_group_id: int | None = None,
            search_text: str | None = None,
            exclude: list[int] | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminAttributeListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        attributes = (
                await crud.get_admin_attributes_list(
                    self.profile_id,
                    self.user.id,
                    attribute_group_id=attribute_group_id,
                    search_text=search_text,
                    exclude=exclude,
                    offset=offset,
                    limit=limit,
                )
                or []
        )

        return [
            await attribute_to_admin_list_schema(
                attribute, self.profile_id, self.user.id
            )
            for attribute in attributes
        ]

    async def get_attributes_total_count(
            self,
            attribute_group_id: int | None = None,
            search_text: str | None = None,
    ) -> int:
        return await crud.get_admin_attributes_list(
            self.profile_id,
            self.user.id,
            attribute_group_id,
            search_text,
            is_count=True,
        )

    async def create_attribute(self, data: schemas.AdminCreateAttributeData):
        profile = await Group.get(self.profile_id)
        brand = await get_or_create_brand(profile)

        if data.attribute_id:
            await validate_object_id(StoreAttribute, self.profile_id, data.attribute_id)
        else:
            data.attribute_id = await get_next_object_id(
                StoreAttribute, self.profile_id
            )

        attribute = await crud.create_attribute(brand, data, self.user)
        return await attribute_to_admin_schema(attribute, profile, self.user.id)

    async def update_attributes_for_attribute_group(
            self, data: list[schemas.AdminAttributeListSchema]
    ) -> list[schemas.AdminAttributeListSchema]:
        attributes = []
        profile = await Group.get(self.profile_id)
        for attribute_data in data:
            attribute = await crud.get_attribute_by_id_and_profile_id(
                attribute_data.id, self.profile_id
            )
            data_only_set = attribute_data.dict(exclude_unset=True)
            validate_non_empty_fields_and_translations(data_only_set, attribute, "name")
            await crud.update_attribute(
                attribute, attribute_data, profile.get_langs_list()
            )

            attributes.append(
                await attribute_to_admin_schema(attribute, profile, self.user.id)
            )

        return attributes

    async def validate_attribute_id(
            self, attribute_id: str, ) -> AdminValidateObjectIdSchema:
        try:
            await validate_object_id(StoreAttribute, self.profile_id, attribute_id)
            return AdminValidateObjectIdSchema(result=True)
        except AdminFieldNotUnique as err:
            return AdminValidateObjectIdSchema(
                result=False, detail=await f(
                    err.text_variable,
                    lang=self.lang, **err.text_kwargs
                )
            )
        except Exception as err:
            logging.error(err, exc_info=True)
            return AdminValidateObjectIdSchema(result=False, detail=str(err))
