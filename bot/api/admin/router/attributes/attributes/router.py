from fastapi import APIRout<PERSON>, Depends, Query, Security

import schemas
from schemas import AdminValidateObjectIdSchema
from .service import AttributesService

router = APIRouter()


@router.get("/")
async def get_attributes_list(
        attribute_group_id: int = Query(None),
        search_text: str | None = Query(None),
        exclude: list[int] | None = Query(None, description="array of attribute_groups ids to exclude"),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        service: AttributesService = Depends()
) -> list[schemas.AdminAttributeListSchema]:
    return await service.get_attributes_list(attribute_group_id, search_text, exclude, offset, limit)


@router.get("/total_count")
async def get_attributes_total_count(
        attribute_group_id: int = Query(None),
        search_text: str | None = Query(None),
        service: AttributesService = Depends()
) -> int:
    return await service.get_attributes_total_count(attribute_group_id, search_text)


@router.post("/")
async def create_attribute(
        data: schemas.AdminCreateAttributeData,
        service: AttributesService = Security(scopes=["me:write", "attribute:create"])
) -> schemas.AdminAttributeSchema:
    return await service.create_attribute(data)


@router.patch("/update_for_group")
async def update_attributes_for_attributes_group(
        data: list[schemas.AdminAttributeListSchema],
        service: AttributesService = Security(scopes=["me:write", "attribute:edit"])
) -> list[schemas.AdminAttributeListSchema]:
    return await service.update_attributes_for_attribute_group(data)


@router.get("/validate/{attribute_id}")
async def validate_attribute_id(
        attribute_id: str | None = None,
        service: AttributesService = Depends()
) -> AdminValidateObjectIdSchema:
    if not attribute_id:
        return AdminValidateObjectIdSchema(result=True)
    return await service.validate_attribute_id(attribute_id)
