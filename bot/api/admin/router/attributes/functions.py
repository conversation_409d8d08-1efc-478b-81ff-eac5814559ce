import schemas
from api.admin.helpers import check_object_access, get_translations_schemas_dict
from db.models import Group, StoreAttribute


async def attribute_to_admin_list_schema(
        attribute_or_row: StoreAttribute, profile_id: int,
        user_id: int
):
    schema = schemas.AdminAttributeListSchema.from_orm(attribute_or_row)

    if not hasattr(attribute_or_row, "read_allowed"):
        schema.read_allowed = await check_object_access(
            "attribute",
            attribute_or_row.id,
            profile_id, user_id,
            scope_name="read",
        )
    if not hasattr(attribute_or_row, "edit_allowed"):
        schema.edit_allowed = await check_object_access(
            "attribute",
            attribute_or_row.id,
            profile_id, user_id,
            scope_name="edit",
        )

    return schema


async def attribute_to_admin_schema(
        attribute: StoreAttribute,
        profile: Group,
        user_id: int,
        read_allowed: bool = True
) -> schemas.AdminAttributeSchema:

    edit_allowed = await check_object_access(
        "attribute", attribute.id,
        profile.id, user_id,
        scope_name="edit",
    )

    if edit_allowed:
        read_allowed = True
    elif read_allowed is None:
        read_allowed = await check_object_access(
            "attribute", attribute.id,
            profile.id, user_id,
            scope_name="read",
        )

    return schemas.AdminAttributeSchema(
        id=attribute.id,
        attribute_id=attribute.attribute_id,
        name=attribute.name,
        external_id=attribute.external_id,
        external_type=attribute.external_type,
        min=attribute.min,
        max=attribute.max,
        is_available=attribute.is_available,
        selected_by_default=attribute.selected_by_default,
        price_impact=round(attribute.price_impact / 100, 2),
        attribute_group_id=attribute.attribute_group_id,
        # Lang has not to be specified. No translations needed
        translations=await get_translations_schemas_dict(
            attribute, profile, schemas.AdminAttributeTranslationSchema
        ),
        read_allowed=read_allowed,
        edit_allowed=edit_allowed,
    )
