from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class MediaUploadOnlyOneOfUrlAndMediaIdAllowedError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "media upload only one of url and media id allowed error"

    def __init__(
            self,
            media_url_field_name: str,
            media_id_field_name: str,
            media_url: str,
            media_id: int,
    ):
        super().__init__(
            media_url_field_name=media_url_field_name,
            media_id_field_name=media_id_field_name,
            media_url=media_url,
            media_id=media_id,
            detail_data={
                "error_code": "admin media upload only one of url and media id allowed error",
                "media_url_field_name": media_url_field_name,
                "media_id_field_name": media_id_field_name,
                "media_url": media_url,
                "media_id": media_id,
            }
        )


class MediaNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "media not found error"

    def __init__(self, media_id: int):
        super().__init__(
            media_id=media_id,
            detail_data={
                "error_code": "media_not_found",
                "media_id": media_id,
            }
        )
