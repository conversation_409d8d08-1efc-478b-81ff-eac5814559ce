from pydantic import BaseModel

from core.media_manager import media_manager
from db.models import MediaObject
from .exceptions import MediaUploadOnlyOneOfUrlAndMediaIdAllowedError, MediaNotFoundError


async def process_save_media(
        data: BaseModel,
        media_url_field_name: str,
        media_id_field_name: str,
        set_media_id_to_data: bool = True,
) -> MediaObject | None:
    """Saves media if url specified, else if media_id specified, returns it. Else returns None"""
    media_url = getattr(data, media_url_field_name)
    media_id = getattr(data, media_id_field_name)

    if all((media_url, media_id)):
        raise MediaUploadOnlyOneOfUrlAndMediaIdAllowedError(
            media_url_field_name=media_url_field_name,
            media_id_field_name=media_id_field_name,
            media_url=media_url, media_id=media_id,
        )

    if media_id:
        media = await MediaObject.get(media_id)
        if not media:
            raise MediaNotFoundError(media_id)
    elif media_url:
        media = await media_manager.download_media(media_url)
    else:
        media = None

    if set_media_id_to_data:
        data_only_set = data.dict(exclude_unset=True)
        if (
                media_url_field_name in data_only_set or
                media_id_field_name in data_only_set
        ):
            setattr(data, media_id_field_name, media.id if media else None)

    return media
