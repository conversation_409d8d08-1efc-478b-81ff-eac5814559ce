from fastapi import <PERSON>Router

import schemas
from .service import CustomTextsService

router = APIRouter(
    prefix="/{profile_id}/custom_texts",
    tags=["custom_texts"],
)


@router.get("/{object_type}/{object_id}")
async def get_custom_texts(
        service: CustomTextsService = CustomTextsService.depend()
) -> list[schemas.CustomTextSchema]:
    return await service.get_custom_texts()


@router.post("/{object_type}/{object_id}/update/text")
async def update_custom_text_value(
        data: schemas.UpdateCustomTextData,
        service: CustomTextsService = CustomTextsService.depend(scopes=["custom_text:edit", "me:write"]),
) -> list[schemas.CustomTextSchema]:
    return await service.update_custom_text_value(data)
