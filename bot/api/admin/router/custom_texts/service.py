from typing import Sequence

from fastapi import Depends, Path, Security
from psutils.undefined import Undefined

import schemas
from core.api.depends import get_lang
from core.auth.depend import Action, get_active_user
from core.auth.services.scopes_checker import BaseScopesCheckerService
from core.custom_texts.models import CustomTextsModel
from db.models import CustomTextsStorage, Group, User

CUSTOM_TEXTS_OBJECTS_MAPPING = {
    "bot": "ClientBot",
    "profile": "Group",
    "qrmenu": "MenuInStore"
}


class CustomTextsService(BaseScopesCheckerService):
    def __init__(
            self,
            action: str | None,
            user: User,
            lang: str,
            profile_id: int,
            object_type: schemas.CustomTextsObjectTypeLiteral,
            object_id: int
    ):
        self.object_name = CUSTOM_TEXTS_OBJECTS_MAPPING[object_type]

        self.model = CustomTextsModel.detect_model(self.object_name)
        self.action_object_name = self.model.scope_action_object_name

        if not action or action.startswith("custom_text:"):
            if action and action.startswith("custom_text:"):
                tag = action.split(":")[1]
            else:
                tag = "read"
            action = f"{self.action_object_name}:{tag}"

        super().__init__(action, user)
        self.lang: str = lang
        self.profile_id: int = profile_id
        self.object_type = object_type
        self.object_id: int = object_id

    def get_available_data(self):
        return {
            "profile_id": self.profile_id,
            self.action_object_name: self.object_id
        }

    @classmethod
    def depend(
            cls,
            scopes: Sequence[str] | None = None,
            use_cache: bool = True,
    ):
        async def depend_func(
                action: str = Action(),
                user: User = Depends(get_active_user),
                profile_id: int = Path(),
                object_type: schemas.CustomTextsObjectTypeLiteral = Path(),
                object_id: int = Path(),
                lang: str = Depends(get_lang),
        ):
            return await cls(
                action, user, lang, profile_id, object_type, object_id
            ).process()

        return Security(depend_func, scopes=scopes, use_cache=use_cache)

    async def get_custom_texts(self):
        storage_id = CustomTextsStorage.build_id(
            object_name=self.object_name, object_id=self.object_id
        )
        ct_obj = await self.model.from_storage_id(storage_id)

        profile = await Group.get(self.profile_id)
        return await ct_obj.to_schema(profile)

    async def update_custom_text_value(self, data: schemas.UpdateCustomTextData):
        ct_obj = await self.model.from_storage_id(
            CustomTextsStorage.build_id(
                object_name=self.object_name,
                object_id=self.object_id,
            )
        )

        data_dict = data.dict(exclude_unset=True)
        if "translations" in data_dict:
            if data.translations:
                translations = {}
                for lang, value in data.translations.items():
                    value = value.dict(exclude_unset=True).get("value", Undefined)
                    if value is not Undefined:
                        translations[lang] = value
            else:
                translations = data.translations
        else:
            translations = Undefined

        await ct_obj.update_data(
            data.value, *data.path, param_name="value", translations=translations
        )

        profile = await Group.get(self.profile_id)
        return await ct_obj.to_schema(profile)
