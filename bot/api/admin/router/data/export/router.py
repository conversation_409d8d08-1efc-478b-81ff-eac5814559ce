from fastapi import APIRouter, Depends, Path

from core.ext.data_manager import schemas
from schemas import OkResponse
from .service import ExportService

router = APIRouter(
    prefix="/export",
)


@router.post("/")
async def start_export(
        data: schemas.ExternExportData,
        service: ExportService = Depends(),
) -> schemas.ExportResponse:
    return await service.set_data(data)


@router.get("/{export_id}")
async def get_export_status(
        export_id: str = Path(
            description="Export request ID. Returned from start export endpoint",
        ),
        service: ExportService = Depends(),
) -> schemas.StatusResponse:
    return await service.get_status(export_id)


@router.patch("/{export_id}")
async def set_export_task_as_read(
        export_id: str = Path(
            description="Export request ID. Returned from start export endpoint",
        ),
        service: ExportService = Depends(),
) -> OkResponse:
    return await service.set_as_read(export_id)
