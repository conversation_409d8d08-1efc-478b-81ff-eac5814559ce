import copy
from dataclasses import asdict
from typing import Any

from core.auth.services.scopes_checker import ScopesCheckerService
from core.ext.data_manager import PorterService, schemas
from core.ext.types import ActionType, ExternalAPIType, StatusType
from core.google_sheets.functions import validate_google_sheets
from db import crud
from db.models import DataPorter, Group, User
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from schemas import OkResponse
from schemas.group.group import (
    ConfigImportSheetsSettings, GroupConfig,
    GroupConfigImport,
)
from utils.scopes_map import scope_map
from utils.text import f


def get_response(porter: DataPorter, info: str | None = None) -> schemas.ExportResponse:
    return schemas.ExportResponse(export_id=porter.uuid_id, info=info)


class ExportService:

    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: Any = asdict(scopes.data)  # dataclass

    async def set_data(self, data: schemas.ExternExportData) -> schemas.ExportResponse:
        brand = await crud.get_brand_by_group(self.profile_id)
        if not brand:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                scope_map.iter_action_scopes("store:read", self.available_data),
                self.available_data,
            )

        current_tasks = await crud.get_current_tasks(self.profile_id, ActionType.EXPORT)

        profile = await Group.get(self.profile_id)

        await self._upd_profile_export_config(profile, data)

        sheets_document_id = None
        if data.export_source == "sheets" and data.sheets_url:
            sheets_document_id = await validate_google_sheets(
                data.sheets_url, data.user_lang or self.lang
            )

        porter = await DataPorter.create(
            action_type=ActionType.EXPORT,
            external_type=data.export_source,
            status=StatusType.PENDING,
            brand=brand,
            lang=data.lang or profile.lang,
            user_lang=data.user_lang or self.lang,
            source_data=sheets_document_id,
            callback_url=data.callback_url,
        )

        return get_response(
            porter, info=await f(
                "admin import export export exist error text", self.lang
            ) if current_tasks else None
        )

    async def get_status(self, export_id: str) -> schemas.StatusResponse:
        porter = await DataPorter.get(uuid_id=export_id)
        if not porter:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                scope_map.iter_action_scopes("store:read", self.available_data),
                self.available_data,
            )

        return await PorterService.get_status_response(porter)

    async def set_as_read(self, uuid_id: str) -> OkResponse:
        porter = await DataPorter.get(uuid_id=uuid_id)
        if not porter:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                scope_map.iter_action_scopes("store:read", self.available_data),
                self.available_data,
            )

        await porter.update(is_read=True)
        return OkResponse()

    async def _upd_profile_export_config(
            self, profile: Group, data: schemas.ExternExportData
    ):
        if profile.config is None:
            profile.config = GroupConfig()

        if profile.config.import_config is None:
            profile.config.import_config = GroupConfigImport()

        new_import_config = copy.deepcopy(profile.config.import_config)
        is_changed = False

        if new_import_config.export_source != data.export_source:
            new_import_config.export_source = data.export_source
            is_changed = True

        if data.export_source == ExternalAPIType.SHEETS.value and data.sheets_url:
            if new_import_config.sheets_settings is None:
                new_import_config.sheets_settings = ConfigImportSheetsSettings()

            if new_import_config.sheets_settings.export_url != data.sheets_url:
                new_import_config.sheets_settings.export_url = data.sheets_url
                is_changed = True

        if is_changed:
            updated_config = GroupConfig(
                positions_config=profile.config.positions_config,
                import_config=new_import_config,
                openai_config=profile.config.openai_config if profile.config and profile.config.openai_config else None,
            )

            await profile.update_config(config=updated_config)
