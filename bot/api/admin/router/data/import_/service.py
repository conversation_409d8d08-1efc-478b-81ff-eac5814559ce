import copy
import io
import logging
from dataclasses import asdict
from typing import Any

from PIL import Image
from fastapi import HTTPEx<PERSON>, UploadFile
from pillow_heif import register_heif_opener
from starlette import status

from core.auth.services.scopes_checker import ScopesCheckerService
from core.ext.data_manager import PorterService, schemas
from core.ext.data_manager.schemas import ImportResponse
from core.ext.types import ActionType, ExternalAPIType, StatusType
from core.google_sheets.functions import validate_google_sheets
from core.loyalty.incust_api import incust
from core.media_manager import media_manager
from db import crud
from db.models import DataPorter, Group, User
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from schemas import GroupConfig, LoyaltySettingsData, OkResponse
from schemas.group.group import (
    ConfigImportIncustSettings, ConfigImportPosterSettings, ConfigImportPromSettings,
    ConfigImportSheetsSettings,
    GroupConfigImport,
)
from utils.scopes_map import scope_map
from utils.text import f

register_heif_opener()


async def convert_ext_data_to_data(ext_data: schemas.ExternImportExtData):
    base_data = {
        "import_source": ext_data.import_source,
        "prom_file_main_lang": ext_data.prom_file_main_lang,
    }
    match ext_data.import_source:
        case ExternalAPIType.SHEETS.value:
            base_data["sheets_url"] = ext_data.sheets_url
        case ExternalAPIType.PROM.value:
            base_data["prom_url"] = ext_data.prom_url
        case ExternalAPIType.GET_ORDER.value | ExternalAPIType.POSTER.value:
            pass
        case ExternalAPIType.EXCEL.value:
            base_data["excel_file"] = ext_data.files[0]
        case ExternalAPIType.CHOICE.value:
            base_data["choice_file"] = ext_data.files[0]
        case ExternalAPIType.INCUST.value:
            base_data["incust_selected_stores"] = ext_data.incust_selected_stores
        case ExternalAPIType.MENU.value:
            base_data["menu_files"] = []
            for file in ext_data.files:
                file = await process_image(file)
                media = await media_manager.save_from_upload_file(file)
                base_data["menu_files"].append(media.url)
        case _:
            raise ValueError(
                f"Not supported value for import_source: {ext_data.import_source}"
            )
    return base_data


def get_response(porter: DataPorter) -> schemas.ImportResponse:
    return schemas.ImportResponse(import_id=porter.uuid_id)


class ImportService:

    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:edit",
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: Any = asdict(scopes.data)  # dataclass

    async def set_data(self, data: schemas.ExternImportData):
        brand = await crud.get_brand_by_group(self.profile_id)
        if not brand:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                scope_map.iter_action_scopes("store:write", self.available_data),
                self.available_data,
            )

        current_tasks = await crud.get_current_tasks(self.profile_id, ActionType.IMPORT)
        if current_tasks:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=await f(
                    "admin import export import exist error text", self.lang
                )
            )

        profile = await Group.get(self.profile_id)
        if data.sheets_url:
            sheets_document_id = await validate_google_sheets(
                data.sheets_url, self.lang
            )
            data.sheets_url = sheets_document_id

        porter = await DataPorter.create(
            action_type=ActionType.IMPORT,
            external_type=data.import_source,
            status=StatusType.PENDING,
            brand=brand,
            lang=profile.lang,
            user_lang=self.lang,
            prom_file_main_lang=data.prom_file_main_lang,
            source_data=await PorterService.get_source_data(
                data.import_source, data, brand.id
            ),
            callback_url=data.callback_url,
            user=self.user,
        )

        return get_response(porter)

    async def get_status(self, import_id: str) -> schemas.StatusResponse:
        porter = await DataPorter.get(uuid_id=import_id)
        if not porter:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                scope_map.iter_action_scopes("store:read", self.available_data),
                self.available_data,
            )

        return await PorterService.get_status_response(porter)

    async def get_active_imports(self, ) -> list[schemas.StatusResponse]:
        return [await PorterService.get_status_response(porter, True) for porter in
                await crud.get_active_porters(
                    self.profile_id
                )]

    async def set_data_ext(self, ext_data: schemas.ExternImportExtData):
        base_data = await convert_ext_data_to_data(ext_data)

        await self._upd_profile_import_config(ext_data)

        return await self.set_data(schemas.ExternImportData(**base_data))

    async def get_incust_stores(self, ) -> list[schemas.IncustStores]:

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "profile",
            LoyaltySettingsData(profile_id=self.profile_id),
        )
        if not loyalty_settings:
            return []

        async with incust.client.POSApi(loyalty_settings, user=self.user, lang=self.lang) as api:
            incust_stores = await api.pos()

        return [
                schemas.IncustStores(
                    uuid_id=store.id,
                    name=store.description,
                    image_url=store.photos[0].url if store.photos and len(
                        store.photos
                    ) > 0 and store.photos[0].url else None
                    ) for store in incust_stores
                ]

    async def _upd_profile_import_config(self, ext_data: schemas.ExternImportExtData):
        profile = await Group.get(self.profile_id)
        if profile.config is None:
            profile.config = GroupConfig()
        if profile.config.import_config is None:
            profile.config.import_config = GroupConfigImport()

        new_import_config = copy.deepcopy(profile.config.import_config)
        is_changed = False

        if new_import_config.import_source != ext_data.import_source:
            new_import_config.import_source = ext_data.import_source
            is_changed = True

        update_func = getattr(self, f"_update_{ext_data.import_source}_config", None)
        if update_func:
            is_changed = await update_func(new_import_config, ext_data) or is_changed

        if is_changed:
            updated_config = GroupConfig(
                positions_config=profile.config.positions_config,
                import_config=new_import_config,
                openai_config=profile.config.openai_config if profile.config and profile.config.openai_config else None,
            )
            await profile.update_config(config=updated_config)

    async def _update_prom_config(
            self, config: GroupConfigImport, data: schemas.ExternImportExtData
    ) -> bool:
        if data.prom_url is not None and data.is_need_save_url:
            new_prom_settings = ConfigImportPromSettings(
                url=data.prom_url, prom_file_main_lang=data.prom_file_main_lang
            )
            if config.prom_settings != new_prom_settings:
                config.prom_settings = new_prom_settings
                return True
        return False

    async def _update_sheets_config(
            self, config: GroupConfigImport, data: schemas.ExternImportExtData
    ) -> bool:
        if data.sheets_url is not None and data.is_need_save_url:
            new_sheets_settings = ConfigImportSheetsSettings(url=data.sheets_url)
            if config.sheets_settings != new_sheets_settings:
                config.sheets_settings = new_sheets_settings
                return True
        return False

    async def _update_poster_config(
            self, config: GroupConfigImport, data: schemas.ExternImportExtData
    ) -> bool:
        poster_fields = [
            'poster_api_token', 'poster_app_secret', 'poster_skip_stores',
            'poster_skip_desc_product', 'poster_regex_to_skip_category',
            'poster_tips_sku'
        ]
        new_poster_settings = {field: getattr(data, field) for field in poster_fields if
                               getattr(data, field) is not None}

        if new_poster_settings:
            if config.poster_settings:
                updated_poster_settings = config.poster_settings.copy(
                    update=new_poster_settings
                )
            else:
                updated_poster_settings = ConfigImportPosterSettings(
                    **new_poster_settings
                )

            if config.poster_settings != updated_poster_settings:
                config.poster_settings = updated_poster_settings
                return True
        return False

    async def _update_incust_config(
            self, config: GroupConfigImport, data: schemas.ExternImportExtData
    ) -> bool:
        if data.incust_selected_stores is not None:
            new_incust_settings = ConfigImportIncustSettings(
                incust_selected_stores=data.incust_selected_stores
            )
            if config.incust_settings != new_incust_settings:
                config.incust_settings = new_incust_settings
                return True
        return False

    async def set_as_read(self, uuid_id: str) -> OkResponse:
        porter = await DataPorter.get(uuid_id=uuid_id)
        if not porter:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                scope_map.iter_action_scopes("store:read", self.available_data),
                self.available_data,
            )

        await porter.update(is_read=True)
        return OkResponse()

    async def repeat_task(self, uuid_id: str) -> ImportResponse:
        porter = await DataPorter.get(uuid_id=uuid_id)

        await self.validate_data(porter)

        porter = await DataPorter.create(
            action_type=porter.action_type,
            external_type=porter.external_type,
            status=StatusType.PENDING,
            brand=porter.brand,
            lang=porter.lang,
            user_lang=porter.user_lang,
            prom_file_main_lang=porter.prom_file_main_lang,
            source_data=porter.source_data,
            callback_url=porter.callback_url,
            user=porter.user,
        )
        return get_response(porter)

    async def validate_data(self, porter):
        if not porter:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                scope_map.iter_action_scopes("store:read", self.available_data),
                self.available_data,
            )
        if porter.external_type == "json":
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                scope_map.iter_action_scopes("store:read", self.available_data),
                self.available_data,
            )
        if porter.status != StatusType.DONE:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                scope_map.iter_action_scopes("store:read", self.available_data),
                self.available_data,
            )


async def is_heic(file: UploadFile) -> bool:
    return file.filename.lower().endswith(('.heic', '.heif'))


async def convert_heic_to_jpeg(file: UploadFile) -> UploadFile:
    contents = await file.read()

    with Image.open(io.BytesIO(contents)) as img:
        jpeg_io = io.BytesIO()
        img.convert("RGB").save(jpeg_io, "JPEG")
        jpeg_io.seek(0)

    result_file = UploadFile(
        filename=file.filename.rsplit('.', 1)[0] + ".jpg",
        file=jpeg_io
    )
    # result_file.content_type = "image/jpeg"

    return result_file


async def process_image(file: UploadFile) -> UploadFile:
    if await is_heic(file):
        try:
            jpeg_file = await convert_heic_to_jpeg(file)
            return jpeg_file
        except Exception as err:
            logging.error(f"{err}")
    return file
