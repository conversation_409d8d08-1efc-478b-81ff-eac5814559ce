from fastapi import APIRouter, Depends, Path

from core.api.depends import build_form_data_depend
from core.ext.data_manager import schemas
from core.ext.data_manager.schemas import ImportResponse
from schemas import OkResponse
from .service import ImportService

router = APIRouter(
    prefix="/import",
)


@router.post("/")
async def start_import(
        data: schemas.ExternImportData,
        service: ImportService = Depends(),
) -> schemas.ImportResponse:
    return await service.set_data(data)


@router.get("/incust_stores")
async def get_incust_stores(
        service: ImportService = Depends(),
) -> list[schemas.IncustStores]:
    return await service.get_incust_stores()


@router.get("/{import_id}")
async def get_import_status(
        import_id: str = Path(
            description="Import request ID. Returned from start import endpoint",
        ),
        service: ImportService = Depends(),
) -> schemas.StatusResponse:
    return await service.get_status(import_id)


@router.get("/")
async def get_active_imports(
        service: ImportService = Depends(),
) -> list[schemas.StatusResponse]:
    return await service.get_active_imports()


@router.post("/ext")
async def start_import_ext(
        data: schemas.ExternImportExtData = Depends(
            build_form_data_depend(schemas.ExternImportExtData, ["incust_selected_stores", "files"])
        ),
        service: ImportService = Depends(),
) -> schemas.ImportResponse:
    return await service.set_data_ext(data)


@router.patch("/{porter_uuid_id}")
async def set_import_task_as_read(
        porter_uuid_id: str = Path(
            description="Task UUID.",
        ),
        service: ImportService = Depends(),
) -> OkResponse:
    return await service.set_as_read(porter_uuid_id)


@router.post("/{porter_uuid_id}/repeat")
async def repeat_import_task(
        porter_uuid_id: str = Path(
            description="Task UUID",
        ),
        service: ImportService = Depends(),
) -> ImportResponse:
    return await service.repeat_task(porter_uuid_id)