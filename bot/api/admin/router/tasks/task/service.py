from dataclasses import asdict
from typing import Any

import schemas
from api.admin.helpers import task_to_schema
from core.auth.services.scopes_checker import ScopesCheckerService
from db.models import User
from db import crud
from exceptions.task import AdminTaskNotFound
from schemas import OkResponse


class TaskService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "task:read",  # will be overridden in "write" routes
                "profile_id",
                "task_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.task_id: int = scopes.data.task_id

    async def get_task(self,) -> schemas.TaskSchema:

        task = await crud.get_task(self.task_id, self.profile_id, self.user.id)
        if not task:
            raise AdminTaskNotFound(self.task_id)

        return await task_to_schema(task, is_detail=True, lang=self.lang)

    async def cancel_task(
            self,
    ) -> OkResponse:
        await crud.update_task(self.task_id, schemas.TaskStatusEnum.CANCELED)
        return schemas.OkResponse()

    async def delete_task(self, ) -> schemas.OkResponse:
        await crud.update_task(self.task_id, schemas.TaskStatusEnum.DELETED)
        return schemas.OkResponse()
