from copy import deepcopy

from fastapi import HTTPException

from exceptions.task import AdminTaskObjectsRequiredError, OpenAIConfigError
import schemas
from api.admin.helpers import task_to_schema
from core.auth.services.scopes_checker import ScopesCheckerService
from core.kafka.producer.functions import add_task_for_action
from db import crud
from db.models import Group, User
from schemas import (
    GroupConfig, PromptManager, TaskCreateResultResponse, TaskListSchema, TaskMode,
    TaskStatusEnum, TaskTypeTaskEnum,
)


async def save_prompt_to_config(data: schemas.CreateTaskSchema, group: Group, group_config: GroupConfig):
    if data.is_save_last_prompt:
        match data.type_task:
            case TaskTypeTaskEnum.PRODUCT_IMAGE:
                group_config.openai_config.user_profile_image_prompt = data.prompt if (data.prompt !=
                                                                                       PromptManager.AI_PRODUCT_IMAGE_PROMPT
                                                                                       ) else None
            case TaskTypeTaskEnum.PROFILE_IMAGE:
                group_config.openai_config.user_profile_image_prompt = data.prompt if (data.prompt !=
                                                                                       PromptManager.AI_PROFILE_IMAGE_PROMPT
                                                                                       ) else None
            case TaskTypeTaskEnum.PROFILE_LOGO:
                group_config.openai_config.user_profile_image_prompt = data.prompt if (data.prompt !=
                                                                                       PromptManager.AI_PROFILE_LOGO_PROMPT
                                                                                       ) else None
            case TaskTypeTaskEnum.STORE_IMAGE:
                group_config.openai_config.user_profile_image_prompt = data.prompt if (data.prompt !=
                                                                                       PromptManager.AI_STORE_IMAGE_PROMPT
                                                                                       ) else None
            case TaskTypeTaskEnum.STORE_BANNER:
                group_config.openai_config.user_profile_image_prompt = data.prompt if (data.prompt !=
                                                                                       PromptManager.AI_STORE_BANNER_PROMPT
                                                                                       ) else None
            case TaskTypeTaskEnum.VM_STEP_IMAGE:
                group_config.openai_config.user_profile_image_prompt = data.prompt if (data.prompt !=
                                                                                       PromptManager.AI_VM_STEP_IMAGE_PROMPT
                                                                                       ) else None
            case TaskTypeTaskEnum.INVOICE_TEMPLATE_IMAGE:
                group_config.openai_config.user_profile_image_prompt = data.prompt if (data.prompt !=
                                                                                       PromptManager.AI_INVOICE_TEMPLATE_IMAGE_PROMPT
                                                                                       ) else None
            case _:
                raise HTTPException(400, detail=f"Invalid type task {data.type_task}")

        group_config.openai_config.ai_model = data.ai_model.value
    group_config.openai_config.is_save_last_prompt = data.is_save_last_prompt
    await group.update_config(group_config)


class TasksService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_tasks(
            self,
            statuses: list[TaskStatusEnum],
            offset: int | None = None,
            limit: int | None = 10,
            include: list[int] | None = None,
            exclude: list[int] | None = None,
            is_count: bool | None = False,
            object_id: int | None = None,
            type_tasks: list[TaskTypeTaskEnum] | None = None
    ) -> list[TaskListSchema] | int:
        task_objects = await crud.get_admin_task_list(
            self.profile_id,
            self.user.id,
            statuses=statuses,
            offset=offset,
            limit=limit,
            include=include,
            exclude=exclude,
            is_count=is_count,
            object_id=object_id,
            type_tasks=type_tasks,
        )

        if is_count:
            return task_objects

        return [
            await task_to_schema(task_object, lang=self.lang)
            for task_object in task_objects
        ]

    async def create_task(
            self,
            data: schemas.CreateTaskSchema
    ) -> schemas.TaskCreateResultResponse:

        if data.mode == TaskMode.SELECTED and not data.objects:
            raise AdminTaskObjectsRequiredError()

        group = await Group.get(self.profile_id)
        group_config = deepcopy(group.config)

        try:
            _ = group_config.openai_config.data[0]
        except (AttributeError, IndexError):
            raise OpenAIConfigError(group.id)

        if data.type_task == TaskTypeTaskEnum.PRODUCT_IMAGE:
            created, replacement, created_tasks = await crud.create_product_tasks(
                user=self.user, group=group, data=data
            )
        else:
            created, replacement, created_tasks = await crud.create_task(
                user=self.user, group=group, data=data
            )

        # save user prompt and is need to save this prompt
        await save_prompt_to_config(data, group, group_config)

        # add tasks to kafka
        await add_task_for_action(created_tasks)

        return TaskCreateResultResponse(created=created, replacement=replacement)

    async def update_tasks(
            self,
            status: schemas.TaskStatusEnum,
            data: list[int] | None = None
    ) -> list[schemas.TaskSchemaProduct]:
        return [
            schemas.TaskSchemaProduct(id=task.id, status=task.status) for task in (
                await crud.update_tasks_for_group(self.profile_id, status, data)
            )
        ]

