from copy import deepcopy
from dataclasses import asdict
from typing import Any

from api.admin.router.openai.exceptions import OpenAiNotUniqueKeysError
from core.auth.services.scopes_checker import ScopesCheckerService
from db.models import Group, User
from schemas import PromptManager
from schemas.group.group import ConfigOpenAiSettings, ConfigOpenAiSettingsResponse, GroupConfig


class OpenAiService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass

    async def get_openai_data(self) -> ConfigOpenAiSettingsResponse:
        profile = await Group.get(self.profile_id)

        result = PromptManager.get_openai_config({})

        if not profile.config or not profile.config.openai_config or not profile.config.openai_config.data:
            return result

        response_data = PromptManager.get_openai_config(profile.config.openai_config.dict())

        return ConfigOpenAiSettingsResponse(**response_data)

    async def update_openai_data(self, data: list[str]) -> ConfigOpenAiSettingsResponse:

        if len(data) != len(set(data)):
            raise OpenAiNotUniqueKeysError()

        profile = await Group.get(self.profile_id)
        profile.config = profile.config or GroupConfig()
        profile.config.openai_config = profile.config.openai_config or ConfigOpenAiSettings(data=[])

        new_config = deepcopy(profile.config)

        if new_config.openai_config.data != data:
            new_config.openai_config.data = data
            await profile.update_config(config=new_config)

        return ConfigOpenAiSettingsResponse(**PromptManager.get_openai_config(profile.config.openai_config.dict()))
