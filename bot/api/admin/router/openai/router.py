from fastapi import APIRouter, Depends, Security

from schemas.group.group import ConfigOpenAiSettingsResponse

from .service import OpenAiService

router = APIRouter(
    prefix="/{profile_id}/openai",
    tags=["openai"],
)


@router.get("/")
async def get_openai_data(
        service: OpenAiService = Depends(),
) -> ConfigOpenAiSettingsResponse:
    return await service.get_openai_data()


@router.post("/")
async def update_openai_data(
        data: list[str],
        service: OpenAiService = Security(scopes=["me:write", "profile_id:edit"])
) -> ConfigOpenAiSettingsResponse:
    return await service.update_openai_data(data)
