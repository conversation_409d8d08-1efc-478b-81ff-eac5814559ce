from fastapi import APIRouter, Depends

import schemas
from .service import StatsService

router = APIRouter()


@router.get("/transactions_count", response_model=schemas.AdminStatsTransactionsCountSchema)
async def get_transactions_count(
        data: schemas.DateRangeSchema = Depends(),
        service: StatsService = Depends()
) -> schemas.AdminStatsTransactionsCountSchema:
    return await service.get_transactions_count(data)


@router.get("/order_users_count", response_model=schemas.AdminStatsOrderUsersCountSchema)
async def get_order_users_count(
        data: schemas.DateRangeSchema = Depends(),
        service: StatsService = Depends()
) -> schemas.AdminStatsOrderUsersCountSchema:
    return await service.get_order_users_count(data)


@router.get("/turnover", response_model=schemas.AdminStatsTurnoverSchema)
async def get_turnover(
        data: schemas.DateRangeSchema = Depends(),
        service: StatsService = Depends()
) -> schemas.AdminStatsTurnoverSchema:
    return await service.get_turnover(data)


@router.get("/reviews_count", response_model=schemas.AdminStatsReviewsCountSchema)
async def get_reviews_count(
        data: schemas.DateRangeSchema = Depends(),
        service: StatsService = Depends()
) -> schemas.AdminStatsReviewsCountSchema:
    return await service.get_reviews_count(data)


@router.get("/average_review_rating", response_model=schemas.AdminStatsAverageReviewRatingSchema)
async def get_average_review_rating(
        data: schemas.DateRangeSchema = Depends(),
        service: StatsService = Depends()
) -> schemas.AdminStatsAverageReviewRatingSchema:
    return await service.get_average_review_rating(data)
