import math

import schemas
from config import NO_CENT_CURRENCIES
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Group, User


class StatsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_transactions_count(
            self,
            data: schemas.DateRangeSchema,
    ) -> schemas.AdminStatsTransactionsCountSchema:
        return schemas.AdminStatsTransactionsCountSchema(
            total_transactions=await crud.get_transactions_count(self.profile_id, data)
        )

    async def get_order_users_count(
            self,
            data: schemas.DateRangeSchema,
    ) -> schemas.AdminStatsOrderUsersCountSchema:
        return schemas.AdminStatsOrderUsersCountSchema(
            order_users_count=await crud.get_order_users_count(self.profile_id, data)
        )

    async def get_turnover(
            self,
            data: schemas.DateRangeSchema,
    ) -> schemas.AdminStatsTurnoverSchema:
        total_turnover = await crud.get_turnover(self.profile_id, data)
        if not total_turnover:
            total_turnover = 0
        profile = await Group.get(self.profile_id)

        return schemas.AdminStatsTurnoverSchema(
            total_turnover=round(total_turnover / 100, 2) if profile.currency not in NO_CENT_CURRENCIES else
            math.ceil(total_turnover / 100)
        )

    async def get_reviews_count(
            self,
            data: schemas.DateRangeSchema,
    ) -> schemas.AdminStatsReviewsCountSchema:
        return schemas.AdminStatsReviewsCountSchema(
            total_reviews=await crud.get_reviews_count(self.profile_id, data)
        )

    async def get_average_review_rating(
            self,
            data: schemas.DateRangeSchema,
    ) -> schemas.AdminStatsAverageReviewRatingSchema:
        return schemas.AdminStatsAverageReviewRatingSchema(
            average_rating=await crud.get_average_review_rating(self.profile_id, data)
        )
