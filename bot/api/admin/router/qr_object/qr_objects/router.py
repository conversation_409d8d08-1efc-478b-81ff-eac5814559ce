from typing import List

from fastapi import APIRouter, Depends, Path, Query, Security

import schemas
from core.api.depends import build_form_data_depend
from schemas import QrMediaObjectTargetType
from .service import QrObjectsService

router = APIRouter()


@router.get("/{object_name}")
async def get_qr_objects_list(
        search_text: str | None = Query(None),
        service: QrObjectsService = Depends(),
        object_name: schemas.QrObjectType = Path(),
        object_id: int = Query(..., description="The ID of the object"),
        targets: List[QrMediaObjectTargetType] = Query(..., description="The targets of the object")
) -> list[schemas.QrMediaObjectSchema]:
    return await service.get_qr_objects_list(object_name, object_id, targets, search_text)


@router.post("/{object_name}")
async def create_qr_object(
        form_data: schemas.CreateQrMediaObject = Depends(
            build_form_data_depend(
                schemas.CreateQrMediaObject, ["additional_media"], {
                    "additional_media": ["name", "qr_media_file"]
                }
            )
        ),
        service: QrObjectsService = Security(scopes=["me:write", "qr_object:edit"]),
        object_name: schemas.QrObjectType = Path(),
        object_id: int = Query(..., description="The ID of the object")
) -> schemas.QrMediaObjectSchema:
    return await service.create_qr_object(object_name, object_id, form_data)
