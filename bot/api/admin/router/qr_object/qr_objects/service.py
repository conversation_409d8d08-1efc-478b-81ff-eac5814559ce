from typing import List

import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from core.qr_media_object.service import QrMediaService
from db.models import User
from db import crud
from schemas import QrMediaObjectTargetType


class QrObjectsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_qr_objects_list(
            self, object_name, object_id,
            targets: List[QrMediaObjectTargetType],
            search_text: str | None
    ) -> list[schemas.QrMediaObjectSchema]:
        qr_media_objects = await crud.get_qr_media_objects_by_object_id_and_target(
            object_name, object_id, targets
        )
        qr_service = QrMediaService()

        return [
            await qr_service.qr_media_object_to_schema(qr_media_object)
            for qr_media_object in qr_media_objects
        ]

    async def create_qr_object(
            self, object_name: schemas.QrObjectType,
            object_id: int,
            data: schemas.CreateQrMediaObject
    ) -> schemas.QrMediaObjectSchema:
        service = QrMediaService()
        qr_media = await service.create_qr_media_object(data)

        await crud.connect_qr_media_object_to_entity_object(qr_media.id, object_name, object_id)

        return await service.qr_media_object_to_schema(qr_media)
