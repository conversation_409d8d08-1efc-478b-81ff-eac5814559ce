from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class AdminQrObjectNotFound(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "admin qr object not found error"

    def __init__(self, qr_object_id: int):
        super().__init__(
            qr_object_id=qr_object_id,
            detail_data={
                "error_code": "qr_object_id_not_found",
            }
        )


class AdminQrObjectInvalidQrMedia(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin qr object invalid qr media"

    def __init__(self, qr_object_id: int, object_id: int):
        super().__init__(
            qr_object_id=qr_object_id,
            object_id=object_id,
            detail_data={
                "error_code": "qr_menu_invalid_qr_media",
            }
        )
