import schemas
from api.admin.router.sort.exceptions import ReorderError
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import StoreAttributeGroup, StoreCategory, StoreProduct


class QrObjectService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.profile_id: int = scopes.data.profile_id

    async def reorder(
            self, object_name: schemas.StoreObjectType,
            data: schemas.UpdateObjectPosition
    ) -> schemas.OkResponse:
        model_mapping = {
            "Menu": StoreCategory,
            "Product": StoreProduct,
            "AttributeGroup": StoreAttributeGroup,
        }
        model = model_mapping[object_name]
        brand = await crud.get_brand_by_group(self.profile_id)
        res = await crud.reorder_object(
            model, data.object_id, data.new_position, brand.id
        )
        if not res:
            raise ReorderError()

        if "father_category_id" in data.__fields_set__ and object_name == "Category":
            category = await StoreCategory.get(data.object_id)
            await category.update(father_category_id=data.father_category_id)

        return schemas.OkResponse()
