from dataclasses import asdict
from typing import Any

import schemas
from api.admin.router.qr_object.exceptions import AdminQrObjectInvalidQrMedia, AdminQrObjectNotFound
from core.auth.services.scopes_checker import ScopesCheckerService
from core.qr_media_object.service import QrMediaService
from db.models import MenuInStoreToQrMediaObject, User
from db import crud
from db.models.qr_media_object.qr_media_object import ProfileToQrMediaObject
from schemas import QrObjectType


class QrObjectService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "qr_object:read",  # will be overridden in "write" routes
                "profile_id",
                "qr_object_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.qr_object_id: int = scopes.data.qr_object_id

    async def __validate_menu_qr_media_object(self, object_name: QrObjectType, object_id: int) -> None:
        connect = None
        if object_name == 'Menu':
            connect = await MenuInStoreToQrMediaObject.get(
                qr_media_object_id=self.qr_object_id, menu_in_store_id=object_id
            )
        if object_name == "Profile":
            connect = await ProfileToQrMediaObject.get(
                qr_media_object_id=self.qr_object_id, profile_id=object_id
            )
        if not connect:
            raise AdminQrObjectInvalidQrMedia(qr_object_id=self.qr_object_id, object_id=object_id)

    async def get_one_qr_object(self, object_name: QrObjectType, object_id: int) -> schemas.QrMediaObjectSchema:
        await self.__validate_menu_qr_media_object(object_name, object_id)

        qr_object = await crud.get_qr_media_object_by_id(self.qr_object_id)
        if not qr_object:
            raise AdminQrObjectNotFound(qr_object_id=self.qr_object_id)

        service = QrMediaService()

        return await service.qr_media_object_to_schema(qr_object)

    async def update_qr_object(
            self, data: schemas.UpdateQrMediaObject, object_name: QrObjectType, object_id: int
    ) -> schemas.QrMediaObjectSchema:
        await self.__validate_menu_qr_media_object(object_name, object_id)

        service = QrMediaService(qr_media_id=self.qr_object_id)

        return await service.update_qr_media_object(data)

    async def delete_qr_object(self, object_name: QrObjectType, object_id: int) -> schemas.OkResponse:
        await self.__validate_menu_qr_media_object(object_name, object_id)

        service = QrMediaService(qr_media_id=self.qr_object_id)

        await crud.delete_qr_media_object_from_object(self.qr_object_id, object_id, object_name)

        return await service.delete_qr_media_object()
