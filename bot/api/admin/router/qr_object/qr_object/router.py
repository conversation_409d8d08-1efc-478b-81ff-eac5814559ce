from fastapi import APIRouter, Depends, Path, Query, Security

import schemas
from core.api.depends import build_form_data_depend

from .service import QrObjectService

router = APIRouter(
    prefix="/{qr_object_id}"
)


@router.get("/")
async def get_qr_object(
        service: QrObjectService = Depends(),
        object_name: schemas.QrObjectType = Path(),
        object_id: int = Query(..., description="The ID of the object"),
) -> schemas.QrMediaObjectSchema:
    return await service.get_one_qr_object(object_name, object_id)


@router.patch("/{object_name}")
async def update_qr_object(
        form_data: schemas.UpdateQrMediaObject = Depends(
            build_form_data_depend(
                schemas.UpdateQrMediaObject, ["additional_media"], {
                    "additional_media": ["name", "qr_media_file", "id"]
                }
            )
        ),
        object_name: schemas.QrObjectType = Path(),
        object_id: int = Query(..., description="The ID of the object"),
        service: QrObjectService = Security(scopes=["me:write", "qr_object:edit"])
) -> schemas.QrMediaObjectSchema:
    return await service.update_qr_object(form_data, object_name, object_id)


@router.delete("/{object_name}")
async def delete_qr_object(
        object_name: schemas.QrObjectType = Path(),
        object_id: int = Query(..., description="The ID of the object"),
        service: QrObjectService = Security(scopes=["me:write", "qr_object:edit"])
) -> schemas.OkResponse:
    return await service.delete_qr_object(object_name, object_id)
