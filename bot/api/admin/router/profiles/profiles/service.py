import pytz
from babel.numbers import list_currencies
from fastapi import Depends
from psutils.country import Countries

import schemas
from api.admin.router.profiles.exceptions import (
    CountryCodeInvalidError, CurrencyCodeInvalidError, LanguageInvalidError,
    TimezoneInvalidError,
)
from api.admin.router.profiles.profile.profile.functions import profile_to_admin_schema
from core.auth.depend import get_active_user
from core.brand.functions import auto_create_brand
from db import crud
from db.models import User
from utils.translator import Translator


class ProfilesService:
    def __init__(
            self,
            user: User = Depends(get_active_user),
    ):
        self.user = user

    async def get_profiles_list(
            self,
            offset: int | None = None,
            limit: int | None = None,
            search_text: str | None = None,
    ):
        result = await crud.get_admin_profiles_list(
            self.user.id, offset, limit, search_text
        )
        return [schemas.AdminProfileListSchema.from_orm(el) for el in result]

    async def get_profile_count(self):
        return await crud.get_admin_profiles_list(self.user.id, is_count=True)

    async def create_profile(self, data: schemas.AdminCreateProfileData):
        countries = Countries().get_countries()

        if not any(map(lambda x: x.iso_code == data.country_code, countries)):
            raise CountryCodeInvalidError(data.country_code)
        if not any(map(lambda x: x == data.currency, list_currencies())):
            raise CurrencyCodeInvalidError(data.currency)
        if data.timezone not in pytz.all_timezones:
            raise TimezoneInvalidError(data.timezone)
        if data.lang not in await Translator.get_supported_languages("en"):
            raise LanguageInvalidError(data.lang)

        profile = await crud.create_group(
            name=data.name,
            owner=self.user,
            is_accepted_agreement=data.is_accepted_agreement,
            country_iso_code=data.country_code,
            timezone=data.timezone,
            lang=data.lang or self.user.lang,
            currency=data.currency,
        )
        brand = await auto_create_brand(profile)
        return await profile_to_admin_schema(profile, self.user.id, brand)
