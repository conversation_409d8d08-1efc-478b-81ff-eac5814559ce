from fastapi import APIRouter, Depends, Query, Security

import schemas
from api.admin.router.profiles.profiles.service import ProfilesService

router = APIRouter()


@router.get("/")
async def get_profiles(
        service: ProfilesService = Depends(),
        offset: int | None = Query(None),
        limit: int | None = Query(None),
        search_text: str | None = Query(None),
) -> list[schemas.AdminProfileListSchema]:
    return await service.get_profiles_list(offset, limit, search_text)


@router.get("/count")
async def get_profile_count(
        service: ProfilesService = Depends()
) -> int:
    return await service.get_profile_count()


@router.post("/create")
async def create_profile(
        data: schemas.AdminCreateProfileData,
        service: ProfilesService = Security(scopes=["me:write"]),
) -> schemas.AdminProfileSchema:
    return await service.create_profile(data)
