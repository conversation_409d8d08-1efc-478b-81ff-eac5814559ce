from fastapi import APIRouter, Security, Depends

import schemas
from api.admin.router.profiles.profile.storage.service import StorageService

router = APIRouter(
    prefix="/storage"
)


@router.get("/")
async def get_profile_media(
        search_text: str | None = None,
        offset: int | None = None,
        limit: int = 10,
        service: StorageService = Depends(),
) -> list[schemas.StorageSchema]:
    return await service.get_profile_media_storage_list(search_text, offset, limit)


@router.post("/")
async def upload_media_to_storage(
        data: schemas.UploadMediaData = Depends(),
        service: StorageService = Security(scopes=["me:write", "storage:edit"]),
) -> schemas.StorageSchema:
    return await service.upload_media(data)


@router.patch("/{storage_id}")
async def update_media_in_storage(
        storage_id: int,
        data: schemas.UpdateMediaData,
        service: StorageService = Security(scopes=["me:write", "storage:edit"]),
) -> schemas.StorageSchema:
    return await service.update_media_in_storage(storage_id, data)


@router.delete("/{storage_id}")
async def delete_media_from_storage(
        storage_id: int,
        service: StorageService = Security(scopes=["me:write", "storage:edit"]),
) -> schemas.OkResponse:
    return await service.delete_media_from_storage(storage_id)
