import os
from dataclasses import asdict
from typing import Any

from sqlalchemy.engine import Row

import schemas
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.media_manager import media_manager
from db import crud
from db.models import Storage, User
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class StorageService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "storage:read",  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: Any = asdict(scopes.data)  # dataclass

    async def storage_to_schema(self, storage: Storage | Row):
        if not isinstance(storage, Storage):
            return schemas.StorageSchema.from_orm(storage)

        edit_allowed = await crud.check_access_to_action(
            f"storage:edit", "user", self.user.id,
            available_data=self.available_data,
        )
        if edit_allowed:
            read_allowed = True
        else:
            read_allowed = await crud.check_access_to_action(
                f"storage:read", "user", self.user.id,
                available_data=self.available_data,
            )

        media = await storage.get_media()
        return schemas.StorageSchema(
            id=storage.id,
            name=storage.text or os.path.basename(media.url),
            media_id=storage.media_id,
            media_url=media.url,
            media_type=media.media_type,
            media_mime_type=media.mime_type,
            media_file_size=media.file_size,
            is_multi_load=storage.is_multi_load,
            datetime_upload=storage.datetime_upload,
            read_allowed=read_allowed,
            edit_allowed=edit_allowed,
        )

    async def get_profile_media_storage_list(
            self,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10
    ):
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        storage_list = await crud.get_storage_list(
            self.profile_id,
            self.user.id,
            search_text,
            offset, limit
        )

        return [await self.storage_to_schema(storage) for storage in storage_list]

    async def upload_media(self, data: schemas.UploadMediaData, profile_id: int = None):
        media = await media_manager.save_from_upload_file(data.file)
        storage = await Storage.create(
            group_id=profile_id if profile_id else self.profile_id,
            text=data.file.filename,
            media=media,
        )
        return await self.storage_to_schema(storage)

    async def get_storage(self, storage_id: int) -> Storage:
        storage = await Storage.get(storage_id)
        if not storage or not await crud.check_access_to_action(
                "storage:edit", "user", self.user.id,
                available_data=self.available_data,
        ):
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "storage:edit", self.available_data,
            )
        return storage

    async def update_media_in_storage(
            self, storage_id: int, data: schemas.UpdateMediaData
    ):
        storage = await self.get_storage(storage_id)
        await storage.update(data.dict())
        return await self.storage_to_schema(storage)

    async def delete_media_from_storage(self, storage_id: int):
        storage = await self.get_storage(storage_id)
        await storage.delete()
        return schemas.OkResponse()
