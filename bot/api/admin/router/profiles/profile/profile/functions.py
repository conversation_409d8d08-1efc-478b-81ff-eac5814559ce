from psutils.google_sheets import get_service_account_email

import schemas
from config import PATH_TO_GOOGLE_CREDS_JSON
from core.brand.functions import auto_create_brand
from core.store.functions.brand import google_public_key
from db import crud
from db.models import Brand, Group
from schemas import BillingProductCode


async def profile_to_admin_schema(
        profile: Group, user_id: int, brand: Brand | None = None
) -> schemas.AdminProfileSchema:
    schema = schemas.AdminProfileSchema.from_orm(profile)
    schema.all_langs_list = profile.get_langs_list(with_main_lang=True)
    schema.additional_langs_list = profile.get_langs_list(with_main_lang=False)
    schema.is_own = profile.owner_id == user_id
    schema.service_email = get_service_account_email(PATH_TO_GOOGLE_CREDS_JSON)
    schema.is_billing_tester = await crud.check_access_to_action(
        "billing:tester", "profile", profile.id,
    )
    schema.transactions_activated = (
            not schema.is_billing_tester or
            await crud.billing.get_is_item_exist_for_product(
                profile.id,
                BillingProductCode.TRANSACTION,
                BillingProductCode.TRANSACTION_CENT,
            )
    )

    if user_id != profile.owner_id and not await crud.check_access_to_action(
            "platform:admin", "user", user_id
    ):
        schema.owner_id = 0

    if not brand:
        brand = await Brand.get(group_id=profile.id)
        if not brand:
            brand = await auto_create_brand(profile)
    if brand:
        schema.brand_id = brand.id
        schema.domain = brand.domain
        schema.logo_url = await brand.logo_url or None
        schema.google_public_key = google_public_key(brand)
        schema.is_incust = await brand.is_incust
    return schema
