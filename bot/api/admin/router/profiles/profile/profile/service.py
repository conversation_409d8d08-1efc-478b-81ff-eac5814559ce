from babel.numbers import list_currencies

import schemas
from api.admin.router.profiles.exceptions import (
    CurrencyCodeInvalidError,
    PasswordNotMatchError,
)
from api.admin.router.profiles.profile.profile.functions import profile_to_admin_schema
from api.admin.router.users.exceptions import ActualOwnerRequiredError
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Group, User
from exceptions import UserNotFoundError


class ProfileService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile_data:read",  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    def profile_to_schema(self, profile: Group):
        return profile_to_admin_schema(profile, self.user.id)

    async def get_profile(self):
        profile = await Group.get(self.profile_id)
        return await self.profile_to_schema(profile)

    async def update_profile(self, data: schemas.AdminUpdateProfileData):
        profile = await Group.get(self.profile_id)
        data_dict = data.dict(exclude_unset=True)
        if "currency" in data_dict:
            if not any(map(lambda x: x == data_dict["currency"], list_currencies())):
                raise CurrencyCodeInvalidError(data_dict["currency"])

        await profile.update(**data_dict)
        return await self.profile_to_schema(profile)

    async def transfer_profile_ownership(
            self, data: schemas.AdminTransferOwnershipData
    ):
        profile = await Group.get(self.profile_id)

        new_owner = await User.get_by_id(data.new_owner_id)

        if not new_owner:
            raise UserNotFoundError(data.new_owner_id)

        is_superadmin = await crud.check_access_to_action(
            "platform:superadmin", "user", self.user.id
        )

        is_admin = await crud.check_access_to_action(
            "platform:admin", "user", self.user.id
        )

        if self.user.id != profile.owner_id and not is_superadmin and not is_admin:
            raise ActualOwnerRequiredError(self.profile_id)

        if not self.user.verify_password(data.password):
            raise PasswordNotMatchError(self.lang)

        await Group.update(profile, owner=new_owner)

        return schemas.OkResponse()

    async def get_profile_completion(self) -> schemas.AdminProfileCompletionSchema:
        group = await Group.get(self.profile_id)
        brand = await crud.get_brand_by_group(self.profile_id)

        profile_completion = await crud.get_profile_completion(
            self.profile_id, brand.id
        )

        return schemas.AdminProfileCompletionSchema(
            has_profile_logo=bool(brand.logo_media_id),
            has_profile_banner=bool(brand.image_media_id),
            has_own_domain=bool(
                brand.domain
            ) and "7loc.com" not in brand.domain.lower(),
            enable_translations=bool(group.is_translate),
            has_stores=bool(profile_completion["Store"]),
            has_categories=bool(profile_completion["StoreCategory"]),
            has_products=bool(profile_completion["StoreProduct"]),
            has_payment_methods=bool(profile_completion["PaymentSettings"]),
            has_fastpay=bool(profile_completion["QrMediaObject"]),
            has_delivery=bool(profile_completion["BrandCustomSettings"]),

        )
