from fastapi import APIRouter, Depends, Security

import schemas
from .service import ProfileService

router = APIRouter()


@router.get("/")
async def get_profile(
        service: ProfileService = Depends()
) -> schemas.AdminProfileSchema:
    return await service.get_profile()


@router.patch("/")
async def update_profile(
        data: schemas.AdminUpdateProfileData,
        service: ProfileService = Security(scopes=["me:write", "profile_data:edit"])
) -> schemas.AdminProfileSchema:
    return await service.update_profile(data)


@router.post('/transfer_ownership')
async def transfer_profile_ownership(
        data: schemas.AdminTransferOwnershipData,
        service: ProfileService = Security(scopes=["profile:admin"])
) -> schemas.OkResponse:
    await service.transfer_profile_ownership(data)
    return schemas.OkResponse()


@router.get('/profile_completion')
async def get_profile_completion(
        service: ProfileService = Depends()
) -> schemas.AdminProfileCompletionSchema:
    return await service.get_profile_completion()
