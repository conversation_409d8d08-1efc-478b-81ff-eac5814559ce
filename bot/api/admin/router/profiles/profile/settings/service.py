from dataclasses import asdict
from typing import Any

from babel.numbers import list_currencies
from fastapi import HTTPException
from psutils.country import Countries

import schemas
from api.admin.helpers import get_or_create_brand
from api.admin.router.profiles.exceptions import (
    CountryCodeInvalidError,
    CurrencyCodeInvalidError,
)
from config import GOOGLE_MAPS_API_KEY
from core.auth.services.scopes_checker import ScopesCheckerService
from core.google_apikeys.functions import restrict_api_key_http
from core.group.default_color_schema import (
    DefaultGroupColorSchemaDark, DefaultGroupColorSchemaLight,
)
from core.group.functions import appearance_to_schema
from core.media_manager import media_manager
from db import crud
from db.crud import check_access_to_action_sync
from db.models import AuthSetting, Brand, BrandSettings, ColorSchema, Group, User
from exceptions import AuthRoleRequiredError
from loggers import JSONLogger
from utils.commands import issue_ssl_certificate
from .validators import (
    ColorSchemaValueValidator, DomainValidator,
    ImageAspectRatioValidator,
)


class SettingsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: Any = asdict(scopes.data)  # dataclass
        self.user: User = scopes.user
        self.is_platform_admin = check_access_to_action_sync(
            "platform:admin", "user", target_id=self.user.id
        )

    async def get_advanced_settings(self) -> schemas.ProfileOtherSettings:
        group = await Group.get(self.profile_id)
        brand = await Brand.get(group_id=self.profile_id)
        res = schemas.ProfileOtherSettings()
        if brand:
            friend_payment_settings = await crud.get_is_friend_payment(brand.id)
            res.is_friend_payment = True if (friend_payment_settings and
                                             friend_payment_settings.value_data ==
                                             "1") \
                else False

            api_token = group.api_token
            if not api_token or not isinstance(api_token, str):
                api_token = await group.generate_api_token()

            res.api_token = api_token.strip()
            res.is_ai_enabled = brand.is_ai_enabled
            res.additional_head_tags = brand.additional_head_tags
            res.additional_body_tags = brand.additional_body_tags
            res.is_ask_about_birthday = group.is_ask_about_birthday

            analytics_data = brand.analytics_data or {}
            res.analytics_data = schemas.ProfileAnalyticsSettings(
                meta_pixel_access_token=analytics_data.get("meta_pixel_access_token"),
                meta_pixel_id=analytics_data.get('meta_pixel_id'),
                google_analytics_id=analytics_data.get('google_analytics_id'),
                google_tag_manager_id=analytics_data.get('google_tag_manager_id'),
            )

        return res

    async def update_advanced_settings(
            self, data: schemas.ProfileOtherSettings | None, set_default: bool,
    ) -> schemas.ProfileOtherSettings:
        brand = await Brand.get(group_id=self.profile_id)
        if set_default or not data:
            data = schemas.ProfileOtherSettings(is_friend_payment=False)

        to_update_dict = data.dict(exclude_unset=True)

        if "is_friend_payment" in to_update_dict:
            await BrandSettings.create_or_update(
                brand_id=brand.id,
                type_data="is_friend_payment",
                value_data=to_update_dict["is_friend_payment"],
            )

        if "is_ask_about_birthday" in to_update_dict:
            group = await Group.get(self.profile_id)
            await group.update(
                is_ask_about_birthday=to_update_dict.get(
                    "is_ask_about_birthday", False
                )
            )

        if brand:
            await brand.update(**to_update_dict)
            await self.post_update_appearance_settings(to_update_dict, brand.id)

            if "is_ai_enabled" in to_update_dict:
                await crud.update_brand_is_ai_enabled(
                    brand.id, to_update_dict["is_ai_enabled"]
                )

            if "additional_head_tags" in to_update_dict:
                await brand.update(
                    additional_head_tags=to_update_dict["additional_head_tags"]
                )

            if "additional_body_tags" in to_update_dict:
                await brand.update(
                    additional_body_tags=to_update_dict["additional_body_tags"]
                )

        return data

    async def get_auth_settings(self) -> schemas.AuthSettings:
        auth_settings = await AuthSetting.get(group_id=self.profile_id)
        if not auth_settings:
            return schemas.AuthSettings()

        return schemas.AuthSettings.from_orm(auth_settings)

    async def update_auth_settings(
            self,
            data: schemas.UpdateAuthSettings | None,
            set_default: bool
    ) -> schemas.AuthSettings:
        auth_settings = await AuthSetting.get(group_id=self.profile_id)
        if set_default or not data:
            data = schemas.UpdateAuthSettings()

        if not auth_settings:
            auth_settings = await AuthSetting.create(
                group_id=self.profile_id, **data.dict()
            )
        else:
            await auth_settings.update(**data.dict())

        return schemas.AuthSettings.from_orm(auth_settings)

    async def get_basic_settings(
            self, brand: Brand | None = None
    ) -> schemas.ProfileBasicSettings:
        if not brand:
            brand = await get_or_create_brand(self.profile_id)
        group = await Group.get(self.profile_id)

        return schemas.ProfileBasicSettings(
            name=brand.name,
            domain=brand.domain,
            timezone=group.timezone,
            banner_url=brand.image_media.url if brand.image_media else None,
            logo_url=brand.logo_media.url if brand.logo_media else None,
            description=brand.description_media.url if brand.description_media else
            None,
            offer=brand.offer_media.url if brand.offer_media else None,
            currency=group.currency,
            consent_mode=schemas.ConsentModeEnum(
                brand.consent_mode
            ) if brand.consent_mode else None,
            terms_of_use_link=(
                brand._terms_of_use_link
                if self.is_platform_admin
                else None
            ),
            privacy_policy_link=(
                brand._privacy_policy_link
                if self.is_platform_admin
                else None
            ),
            country_code=group.country_code,
        )

    async def validate_brand_domain(
            self, domain: str,
            brand: Brand | None = None,
    ) -> schemas.ValidatedBrandDomainSchema:
        if not brand:
            brand = await Brand.get(group_id=self.profile_id)

        domain_validator = DomainValidator(self.lang, brand, domain)
        return await domain_validator.validate()

    async def validate_aspect_ratio(self, ratio: str) -> bool:
        validator = ImageAspectRatioValidator(ratio)
        res = await validator.validate(self.lang)
        if isinstance(res, str):
            raise HTTPException(status_code=400, detail=res)
        return True

    async def post_update_basic_settings(
            self,
            validated_domain: schemas.ValidatedBrandDomainSchema,
            old_domain: str | None = None,
    ):
        if (validated_domain.is_external_domain and not
        validated_domain.is_equal_current_domain):
            domain = validated_domain.domain.replace("https://", "")
            domain = domain.replace("/", "")
            await issue_ssl_certificate(domain)

            await restrict_api_key_http(
                GOOGLE_MAPS_API_KEY,
                add_referrers=[validated_domain.domain],
                remove_referrers=[old_domain] if old_domain else None,
                debug_data={
                    "profile_id": self.profile_id,
                }
            )

    @classmethod
    async def post_update_appearance_settings(cls, data: dict, brand_id: int):
        if "product_image_aspect_ratio" in data:
            await crud.update_brand_image_aspect_ratio(
                brand_id, data["product_image_aspect_ratio"]
            )

    async def update_basic_settings(
            self, data: schemas.AdminProfileUpdateBasicSettings,
    ) -> schemas.ProfileBasicSettings:
        brand = await Brand.get(group_id=self.profile_id)
        group = await Group.get(self.profile_id)

        is_platform_admin = await crud.check_access_to_action(
            "platform:admin",
            "user", self.user.id,
        )

        logger = JSONLogger(
            "profile.basic_settings", {
                "group_name": group.name,
                "brand_name": brand.name,
                "group_id": group.id,
                "brand_id": brand.id,
            },
        )

        data_dict = data.dict(exclude_unset=True)
        to_update_brand_dict = {}
        to_update_group_dict = {}
        validated_domain = None

        if "name" in data_dict:
            to_update_group_dict["name"] = data.name
            to_update_brand_dict["name"] = data.name

        if "currency" in data_dict:
            if not any(map(lambda x: x == data_dict["currency"], list_currencies())):
                raise CurrencyCodeInvalidError(data_dict["currency"])
            to_update_group_dict["currency"] = data_dict["currency"]

        if "country_code" in data_dict:
            if not is_platform_admin:
                raise AuthRoleRequiredError("platform:admin")

            countries = Countries().get_countries()
            if not any(map(lambda x: x.iso_code == data.country_code, countries)):
                raise CountryCodeInvalidError(data.country_code)

            to_update_group_dict["country_code"] = data_dict["country_code"]

        if "domain" in data_dict:
            validated_domain = await self.validate_brand_domain(data.domain, brand)
            logger.debug(f"updating domain", {"domain": validated_domain.dict()})
            to_update_brand_dict["domain"] = validated_domain.domain

        if "consent_mode" in data_dict:
            to_update_brand_dict["consent_mode"] = data.consent_mode

        if "terms_of_use_link" in data_dict and self.is_platform_admin:
            to_update_brand_dict["terms_of_use_link"] = data.terms_of_use_link

        if "privacy_policy_link" in data_dict and self.is_platform_admin:
            to_update_brand_dict["privacy_policy_link"] = data.privacy_policy_link

        if "logo_file" in data_dict:
            if not data_dict.get("logo_file"):
                to_update_brand_dict["logo_media_id"] = None
            else:
                logo_media = await media_manager.save_from_upload_file(
                    data_dict.get("logo_file")
                )
                to_update_brand_dict["logo_media_id"] = logo_media.id

        if data.clear_logo:
            to_update_brand_dict["logo_media"] = None

        if "banner_file" in data_dict:
            if not data_dict.get("banner_file"):
                to_update_brand_dict["image_media_id"] = None
            else:
                image_media = await media_manager.save_from_upload_file(
                    data_dict.get("banner_file")
                )
                to_update_brand_dict["image_media_id"] = image_media.id

        if "description_file" in data_dict:
            if not data_dict.get("description_file"):
                to_update_brand_dict["description_media_id"] = None
            else:
                description_media = await media_manager.save_from_upload_file(
                    data_dict.get("description_file")
                )
                to_update_brand_dict["description_media_id"] = description_media.id

        if "offer_file" in data_dict:
            if not data_dict.get("offer_file"):
                to_update_brand_dict["offer_media_id"] = None
            else:
                offer_media = await media_manager.save_from_upload_file(
                    data_dict.get("offer_file")
                )
                to_update_brand_dict["offer_media_id"] = offer_media.id

        if "timezone" in data_dict:
            if data_dict.get("timezone"):
                await group.update(timezone=data_dict.get("timezone"))
            del data_dict["timezone"]

        await brand.update(**to_update_brand_dict)
        if to_update_group_dict:
            await group.update(**to_update_group_dict)
        if validated_domain:
            await self.post_update_basic_settings(validated_domain)
            logger.debug(
                f"updating domain post update done", {"domain": validated_domain.dict()}
            )

        return await self.get_basic_settings(brand)

    async def get_appearance_settings(
            self, brand: Brand | None = None
    ) -> schemas.AppearanceSettings:
        return await appearance_to_schema(self.profile_id, brand)

    async def revoke_api_token(self) -> schemas.RevokeApiTokenResponse:
        group = await Group.get(self.profile_id)
        await group.generate_api_token()
        return schemas.RevokeApiTokenResponse(api_token=group.api_token)

    @classmethod
    def __check_is_default_appearance_color(
            cls,
            key: str,
            value: str,
            theme_mode: schemas.ThemeMode
    ) -> None | str:
        default_theme = asdict(
            DefaultGroupColorSchemaLight()
        ) if theme_mode == "light" else (
            asdict(DefaultGroupColorSchemaDark()))

        if value == default_theme[key]:
            return None

        return value

    async def __set_default_appearance_settings(
            self, color_schema_db: ColorSchema | None, brand: Brand
    ):
        color_schema = schemas.AppearanceColorSchemaFormData(
            is_active=True,
            font=None,
            use_telegram_theme=True,
            theme_mode="light",
        )
        if color_schema_db:
            await color_schema_db.update(**color_schema.dict())

        return await self.get_appearance_settings(brand)

    async def __process_and_validate_appearance(
            self,
            brand_dict: dict,
            color_schema_db: ColorSchema | None,
            font: str | None = None,
    ) -> dict:
        colors_dict = {
            "bg_color": brand_dict.get("bg_color", None),
            "error_color": brand_dict.get("error_color", None),
            "primary_color": brand_dict.get("primary_color", None),
            "text_color": brand_dict.get("text_color", None),
            "warning_color": brand_dict.get("warning_color", None),
            "secondary_color": brand_dict.get("secondary_color", None),
            "secondary_bg_color": brand_dict.get("secondary_bg_color", None),
        }

        for key, value in colors_dict.items():
            if value:
                res = self.__check_is_default_appearance_color(
                    key, value,
                    color_schema_db.theme_mode if color_schema_db else "light"
                )
                brand_dict[key] = res
                colors_dict[key] = res

        validator = ColorSchemaValueValidator(colors_dict, font, self.lang)
        validation_result = await validator.validate()
        if isinstance(validation_result, str):
            raise HTTPException(status_code=400, detail=validation_result)
        if isinstance(validation_result, dict):
            brand_dict["font"] = validation_result["font_link"]

        if "product_image_aspect_ratio" in brand_dict:
            if brand_dict["product_image_aspect_ratio"]:
                await self.validate_aspect_ratio(
                    brand_dict["product_image_aspect_ratio"]
                )

        return brand_dict

    @classmethod
    async def __process_appearance_files(
            cls, brand_dict: dict, clear_logo: bool = False
    ) -> dict:
        if "logo_file" in brand_dict:
            if not brand_dict.get("logo_file"):
                brand_dict["logo_media_id"] = None
            else:
                logo_media = await media_manager.save_from_upload_file(
                    brand_dict.get("logo_file")
                )
                brand_dict["logo_media_id"] = logo_media.id
            brand_dict.pop("logo_file")

        if clear_logo:
            brand_dict["logo_media"] = None

        if "banner_file" in brand_dict:
            if not brand_dict.get("banner_file"):
                brand_dict["image_media_id"] = None
            else:
                image_media = await media_manager.save_from_upload_file(
                    brand_dict.get("banner_file")
                )
                brand_dict["image_media_id"] = image_media.id
            brand_dict.pop("banner_file")

        return brand_dict

    async def update_appearance_settings(
            self,
            data: schemas.UpdateAppearanceSettings | None,
            set_default: bool = False,
            set_default_thumbnails_size: bool = False,
    ) -> schemas.AppearanceSettings:
        color_schema_db = await ColorSchema.get(group_id=self.profile_id)
        brand = await Brand.get(group_id=self.profile_id)

        if set_default:
            return await self.__set_default_appearance_settings(color_schema_db, brand)

        if set_default_thumbnails_size:
            await brand.update(thumbnail_size=512)
            return await self.get_appearance_settings(brand)

        brand_dict = data.dict(exclude_unset=True)
        brand_dict = await self.__process_and_validate_appearance(
            brand_dict, color_schema_db, data.font
        )
        brand_dict = await self.__process_appearance_files(brand_dict, data.clear_logo)

        color_schema = schemas.AppearanceColorSchemaFormData(**brand_dict)
        if not color_schema_db:
            color_schema_db = await ColorSchema.create(group_id=self.profile_id)
        color_schema_dict = color_schema.dict(exclude_unset=True)

        await color_schema_db.update(**color_schema_dict)
        await brand.update(**brand_dict)
        await self.post_update_appearance_settings(data.dict(), brand.id)

        return await self.get_appearance_settings(brand)
