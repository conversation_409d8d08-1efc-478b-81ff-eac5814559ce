import logging
import re
import socket

from bs4 import BeautifulSoup
from psutils.convertors import str_to_float
from psutils.forms.validators.base import Validator

import schemas
from api.admin.router.profiles.profile.settings.exceptions import (
    DomainDNSInvalidError, DomainExistsError, DomainForbiddenError,
    DomainInvalidError,
)
from config import FORBIDDEN_DOMAIN_NAMES, LOC7_HOST, LOC7_IP, PAYFORSAY_HOST
from db import crud
from db.models import Brand
from loggers import J<PERSON>NLogger
from utils.text import f


class DomainValidator:
    FORBIDDEN_DOMAIN_NAMES = (
        "api",
        "test",
        "testapi",
        "bot",
        "testbot",
        "servicebot",
        "testservicebot",
        "dev",
        "liplep",
        "webapp",
        "admin",
    )

    def __init__(self, lang: str, brand: Brand, domain: str | None = None):
        self.lang: str = lang
        self.brand: Brand = brand
        self.domain: str | None = domain

    @property
    def platform_hosts(self):
        return LOC7_HOST, PAYFORSAY_HOST

    def is_domain_allowed(self, domain: str):
        if any(map(lambda x: f".dev.{x}/" in domain, self.platform_hosts)):
            return False

        if any(
                map(
                    lambda x: domain.startswith(f"https://dev.{x}/"),
                    self.platform_hosts
                )
        ):
            return False

        for host in self.platform_hosts:
            if any(
                    map(
                        lambda x: domain.startswith(f"https://{x}.{host}/"),
                        FORBIDDEN_DOMAIN_NAMES
                    )
            ):
                return False

            if f"{host}/" in domain and domain.split(f"{host}/", 1)[0].count(".") != 1:
                return False

        return True

    async def validate(self) -> schemas.ValidatedBrandDomainSchema:
        domain = re.search(
            "^(?:https?://)?(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z0-9()]{"
            "1,6}\\b[-a-zA-Z0-9()@:%_+.~#?&/=]*$|^[A-Za-z0-9-_]+$",
            self.domain, flags=re.IGNORECASE
        )

        if not domain:
            raise DomainInvalidError()

        domain = domain.group()

        url = False
        for test in ("https", ":", ".",):
            if test in domain:
                url = True

        if not url:
            domain = f"https://{domain}.{LOC7_HOST}/"
        else:
            if domain[-1] != "/":
                domain += "/"

            if not domain.startswith("http"):
                domain = f"https://{domain}"

        domain = domain.replace("http://", "https://")

        logger = JSONLogger(
            "domain-validator", {
                "domain": domain,
                "input": self.domain,
            }
        )

        if not self.is_domain_allowed(domain):
            logger.debug(
                "Domain is not allowed", {
                    "forbidden_domains": self.FORBIDDEN_DOMAIN_NAMES,
                }
            )
            raise DomainForbiddenError(domain)

        if (
                self.brand.domain.lower() != domain.lower() and
                await crud.check_is_domain_exists(domain)
        ):
            logger.debug(
                "Domain exists", {
                    "current_domain": self.brand.domain,
                }
            )
            raise DomainExistsError(domain)

        is_external_domain = not any(
            map(lambda x: f".{x}/" in domain, self.platform_hosts)
        )

        if is_external_domain:
            try:
                ip = socket.gethostbyname(domain.replace("https://", "")[:-1])
            except Exception as e:
                logger.error(
                    "An error occurred while checking external domain IP", e,
                )
                ip = None

            if ip != LOC7_IP:
                logger.debug(
                    "DNS is invalid", {
                        "required_ip": LOC7_IP,
                        "current_ip": ip,
                    }
                )
                raise DomainDNSInvalidError(domain, LOC7_IP, ip)

        return schemas.ValidatedBrandDomainSchema(
            domain=domain,
            is_external_domain=is_external_domain,
            is_equal_current_domain=domain == self.brand.domain,
        )


class ImageAspectRatioValidator(Validator):
    def __init__(self, ratio: str):
        self.ratio = ratio
        super().__init__("image aspect ratio format invalid error")

    async def validate(self, lang: str) -> bool | str:
        try:
            w, h = self.ratio.split(":")
            str_to_float(w)
            str_to_float(h)
        except Exception as e:
            logging.error(e, exc_info=True)
            return await f("image aspect ratio format invalid error", lang)
        return True


class ColorSchemaValueValidator(Validator):
    def __init__(self, colors: dict, font: str, lang: str):
        self.colors = colors
        self.font = font
        self.lang = lang
        super().__init__("color schema not valid value text")

    color_pattern = re.compile(
        r"#(?:[0-9a-f]{3}){1,2}|rgb|rgba\((?:\d{1,3}(?:\.\d+)?,\s*){2,3}(?:\d{1,"
        r"3}(?:\.\d+)?\s*)?\)",
        flags=re.IGNORECASE
    )

    async def validate(self) -> bool | str | dict:
        response: bool | dict = True

        if self.font:
            res = await self.check_google_font()
            if not res or isinstance(res, str):
                return res or await f(
                    "admin profile appearance settings bad font error", self.lang,
                    font_value=self.font
                )
            response = res

        if self.colors:
            for key, value in self.colors.items():
                if value:
                    res = await self.check_color(value)
                    if not res or isinstance(res, str):
                        return res or await f(
                            "admin profile appearance settings bad color error",
                            self.lang,
                            color_value=value
                        )

        return response

    async def check_color(self, color: str):
        colors = re.findall(self.color_pattern, color)
        if len(colors) == 1:
            return {
                "color": colors[0]
            }
        return await f(
            "admin profile appearance settings bad color error", self.lang,
            color_value=color
        )

    async def check_google_font(self) -> None | dict | str:
        soup = BeautifulSoup(self.font, 'html.parser')
        if not soup:
            return None

        font_link_el = soup.find(
            'link', href=lambda val: 'fonts.googleapis.com' in val, rel='stylesheet'
        )

        if font_link_el:
            return {
                "font_link": font_link_el['href']
            }

        return await f(
            "admin profile appearance settings bad font error", self.lang,
            font_value=self.font
        )
