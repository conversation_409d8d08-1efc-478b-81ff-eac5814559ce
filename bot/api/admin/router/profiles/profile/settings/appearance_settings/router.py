from fastapi import APIRouter, <PERSON>, Depends

import schemas
from api.admin.router.profiles.profile.settings.service import SettingsService

from core.api.depends import build_form_data_depend

router = APIRouter(
    prefix="/appearance_settings"
)


@router.get("/")
async def get_profile_appearance_settings(
        service: SettingsService = Depends(),
) -> schemas.AppearanceSettings:
    return await service.get_appearance_settings()


@router.patch("/")
async def update_profile_appearance_settings(
        form_data: schemas.UpdateAppearanceSettings = Depends(
            build_form_data_depend(schemas.UpdateAppearanceSettings)
        ),
        service: SettingsService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AppearanceSettings:
    return await service.update_appearance_settings(form_data)


@router.patch("/set_default")
async def set_to_default_profile_appearance_settings(
        service: SettingsService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AppearanceSettings:
    return await service.update_appearance_settings(None, True)


@router.patch("/set_default_thumbnail_size")
async def set_to_default_profile_thumbnails_size(
        service: SettingsService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AppearanceSettings:
    return await service.update_appearance_settings(None, False, True)


@router.patch("/revoke_api_token")
async def revoke_api_token(
        service: SettingsService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.RevokeApiTokenResponse:
    return await service.revoke_api_token()
