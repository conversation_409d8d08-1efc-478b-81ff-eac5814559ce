from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class DomainInvalidError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand invalid domain error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "domain_invalid_error"
            }
        )


class DomainForbiddenError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "store domain forbidden error"

    def __init__(self, domain: str):
        super().__init__(
            detail_data={
                "error_code": "domain_forbidden_error",
                "domain": domain,
            }
        )


class DomainExistsError(ErrorWithHTTPStatus):
    status_code = status.HTTP_409_CONFLICT
    text_variable = "store domain exists error"

    def __init__(self, domain: str):
        super().__init__(
            detail_data={
                "error_code": "domain_exists_error",
                "domain": domain,
            }
        )


class DomainDNSInvalidError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store domain dns invalid error"

    def __init__(self, domain: str, required_ip: str, actual_ip: str):
        super().__init__(
            detail_data={
                "error_code": "domain_dns_invalid_error",
                "domain": domain,
                "required_ip": required_ip,
                "actual_ip": actual_ip,
            }
        )
