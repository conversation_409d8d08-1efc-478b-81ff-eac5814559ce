from fastapi import APIRouter, Security, Depends, Query

import schemas
from api.admin.router.profiles.profile.settings.service import SettingsService

router = APIRouter(
    prefix="/auth_settings"
)


@router.get("/")
async def get_profile_auth_settings(
    service: SettingsService = Depends(),
) -> schemas.AuthSettings:
    return await service.get_auth_settings()


@router.patch("/")
async def update_profile_auth_settings(
    data: schemas.UpdateAuthSettings | None = None,
    set_default: bool = Query(False),
    service: SettingsService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AuthSettings:
    return await service.update_auth_settings(data, set_default)
