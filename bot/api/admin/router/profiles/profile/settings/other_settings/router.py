from fastapi import APIRouter, Depends, Query, Security

import schemas
from api.admin.router.profiles.profile.settings.service import SettingsService

router = APIRouter(
    prefix="/advanced_settings"
)


@router.get("/")
async def get_profile_advanced_settings(
    service: SettingsService = Depends(),
) -> schemas.ProfileOtherSettings:
    return await service.get_advanced_settings()


@router.post("/")
async def update_profile_advanced_settings(
    data: schemas.ProfileOtherSettings | None = None,
    set_default: bool = Query(False),
    service: SettingsService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.ProfileOtherSettings:
    return await service.update_advanced_settings(data, set_default)
