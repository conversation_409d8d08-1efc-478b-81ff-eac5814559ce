from fastapi import APIRouter, Security, Depends

import schemas
from api.admin.router.profiles.profile.settings.service import SettingsService
from core.api.depends import build_form_data_depend

router = APIRouter(
    prefix="/basic_settings"
)


@router.get("/")
async def get_basic_settings(
    service: SettingsService = Security(scopes=["profile:edit", "me:write"])
) -> schemas.ProfileBasicSettings:
    return await service.get_basic_settings()


@router.post("/")
async def update_basic_settings(
    form_data: schemas.AdminProfileUpdateBasicSettings = Depends(
        build_form_data_depend(schemas.AdminProfileUpdateBasicSettings)
    ),
    service: SettingsService = Security(scopes=["profile:edit", "me:write"])
) -> schemas.ProfileBasicSettings:
    return await service.update_basic_settings(form_data)


@router.post("/validate_domain")
async def validate_domain(
    data: schemas.ValidateDomainData,
    service: SettingsService = Security()
) -> schemas.ValidatedBrandDomainSchema:
    return await service.validate_brand_domain(data.domain)
