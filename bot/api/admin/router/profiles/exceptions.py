from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class CountryCodeInvalidError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "country code invalid error"

    def __init__(self, country_code: str):
        self.country_code = country_code
        super().__init__(
            country_code=country_code,
            detail_data={
                "error_code": "country_code_invalid",
                "country_code": country_code,
            }
        )


class CurrencyCodeInvalidError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "currency code invalid error"

    def __init__(self, currency_code: str):
        self.country_code = currency_code
        super().__init__(
            country_code=currency_code,
            detail_data={
                "error_code": "currency_code_invalid",
                "currency_code": currency_code,
            }
        )


class TimezoneInvalidError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "timezone invalid error"

    def __init__(self, timezone: str):
        self.timezone = timezone
        super().__init__(
            timezone=timezone,
            detail_data={
                "error_code": "timezone_invalid",
                "timezone": timezone,
            }
        )


class LanguageInvalidError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "language invalid error"

    def __init__(self, language: str):
        self.language = language
        super().__init__(
            language=language,
            detail_data={
                "error_code": "language_invalid",
                "language": language,
            }
        )


class PasswordNotMatchError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "web auth passwords do not match error"

    def __init__(self, language: str):
        self.language = language
        super().__init__(
            language=language,
            detail_data={
                "error_code": "language_invalid",
                "language": language,
            }
        )
