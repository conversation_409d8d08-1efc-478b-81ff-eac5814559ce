from fastapi import APIRouter

from . import (
    admin_emails, admin_notifications, admin_user, attribute_groups, attributes,
    billing, bots, categories, characteristics, client_web_pages, custom_texts, data,
    extra, extra_fee, filters, invoice_templates, journal_settings, loyalty_settings,
    mailing, openai, order_fields, order_settings, payment_settings, products,
    products_groups, profile_media, profiles, qr_menu, qr_object, scan_receipts,
    scopes, sort, stats, stores, tasks, users, virtual_managers, webhooks,
)

router = APIRouter()

router.include_router(admin_user.router)
router.include_router(profiles.router)
router.include_router(users.router)
router.include_router(scopes.router)
router.include_router(stores.router)
router.include_router(categories.router)
router.include_router(products.router)
router.include_router(products_groups.router)
router.include_router(attribute_groups.router)
router.include_router(attributes.router)
router.include_router(characteristics.router)
router.include_router(extra.router)
router.include_router(qr_menu.router)
router.include_router(qr_object.router)
router.include_router(invoice_templates.router)
router.include_router(order_settings.router)
router.include_router(sort.router)
router.include_router(loyalty_settings.router)
router.include_router(order_fields.router)
router.include_router(stats.router)
router.include_router(scan_receipts.router)
router.include_router(bots.router)
router.include_router(data.router)
router.include_router(extra_fee.router)
router.include_router(payment_settings.router)
router.include_router(custom_texts.router)
router.include_router(tasks.router)
router.include_router(openai.router)
router.include_router(virtual_managers.router)
router.include_router(profile_media.router)
router.include_router(webhooks.router)
router.include_router(billing.router)
router.include_router(admin_emails.router)
router.include_router(admin_notifications.router)
router.include_router(mailing.router)
router.include_router(client_web_pages.router)
router.include_router(journal_settings.router)
router.include_router(filters.router)
