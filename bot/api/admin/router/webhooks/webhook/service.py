import uuid
from dataclasses import asdict
from typing import Any
from urllib.parse import urlparse

import schemas
from api.admin.router.webhooks.functions import webhook_to_admin_schema
from core.auth.services.scopes_checker import ScopesCheckerService
from db.models import Webhook

from ..exceptions import AdminWebhookNotFoundError, AdminWebhookInvalidUrlError, AdminWebhookUrlExistError


class WebhookService:
    def __init__(
        self,
        scopes: ScopesCheckerService = ScopesCheckerService.depend(
            "webhook:read",  # will be overridden in "write" routes
            "profile_id",
            "webhook_id",
        ),
    ):
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.webhook_id: int = scopes.data.webhook_id

    async def get_webhook(self):
        webhook = await Webhook.get(id=self.webhook_id, group_id=self.profile_id)
        if not webhook:
            raise AdminWebhookNotFoundError()

        return webhook_to_admin_schema(webhook)

    async def update_webhook(self, data: schemas.AdminUpdateWebhookData) -> schemas.AdminWebhookSchema:
        webhook = await Webhook.get(id=self.webhook_id, group_id=self.profile_id)
        if webhook:
            await self.check_webhook_url(data.endpoint_url, webhook.id, self.profile_id)
            await webhook.update(**data.dict(exclude_unset=True))

            return webhook_to_admin_schema(webhook)
        raise AdminWebhookNotFoundError()

    async def regenerate_webhook_id(self) -> schemas.AdminWebhookSchema:
        webhook = await Webhook.get(id=self.webhook_id, group_id=self.profile_id)

        if webhook:
            await webhook.update(hook_id=uuid.uuid4().hex)
            return webhook_to_admin_schema(webhook)

        raise AdminWebhookNotFoundError()

    async def delete_webhook(self) -> schemas.OkResponse:
        webhook = await Webhook.get(id=self.webhook_id, group_id=self.profile_id)

        if webhook:
            await webhook.update(is_deleted=True)
            return schemas.OkResponse()

        raise AdminWebhookNotFoundError()

    async def blocked_webhook(self) -> schemas.AdminWebhookSchema:
        webhook = await Webhook.get(id=self.webhook_id, group_id=self.profile_id)

        if webhook:
            await webhook.update(retries_count=0)
            return webhook_to_admin_schema(webhook)

        raise AdminWebhookNotFoundError()

    async def secret_webhook(self) -> schemas.AdminWebhookSecretSchema:
        webhook = await Webhook.get(id=self.webhook_id, group_id=self.profile_id)

        if webhook:
            return schemas.AdminWebhookSecretSchema(hook_id=webhook.hook_id)

        raise AdminWebhookNotFoundError()

    @classmethod
    async def check_webhook_url(cls, url: str, webhook_id: int | None = None, profile_id: int | None = None):
        result = urlparse(url)
        valid = all([result.scheme, result.netloc])

        if not valid:
            raise AdminWebhookInvalidUrlError()

        exist_url = await Webhook.get(endpoint_url=url, group_id=profile_id, is_deleted=False)
        if exist_url and webhook_id and exist_url.id == webhook_id:
            return
        if exist_url and not exist_url.is_deleted:
            raise AdminWebhookUrlExistError()
