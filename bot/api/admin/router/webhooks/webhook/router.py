from fastapi import APIRouter, Depends, Security

import schemas
from .service import WebhookService

router = APIRouter(
    prefix="/{webhook_id}"
)


@router.get("/")
async def get_webhook(
    service: WebhookService = Depends()
) -> schemas.AdminWebhookSchema:
    return await service.get_webhook()


@router.patch("/")
async def update_webhook(
    data: schemas.AdminUpdateWebhookData,
    service: WebhookService = Security(scopes=["wh:edit", "me:write"]),
) -> schemas.AdminWebhookSchema:
    return await service.update_webhook(data)


@router.post("/regenerate")
async def regenerate_webhook_id(
    service: WebhookService = Security(scopes=["wh:edit", "me:write"]),
) -> schemas.AdminWebhookSchema:
    return await service.regenerate_webhook_id()


@router.delete("/")
async def delete_webhook(
    service: WebhookService = Security(scopes=["wh:edit", "me:write"]),
) -> schemas.OkResponse:
    return await service.delete_webhook()


@router.patch("/blocked")
async def blocked_webhook(
    service: WebhookService = Security(scopes=["wh:edit", "me:write"]),
) -> schemas.AdminWebhookSchema:
    return await service.blocked_webhook()


@router.post("/secret")
async def secret_webhook(
    service: WebhookService = Security(scopes=["wh:edit", "me:write"]),
) -> schemas.AdminWebhookSecretSchema:
    return await service.secret_webhook()
