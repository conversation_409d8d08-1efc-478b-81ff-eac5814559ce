import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from core.invoice.invoice_to_schema import invoice_to_schema
from core.store.functions.order_to_schema import order_to_schema
from core.webhooks.functions import (
    add_webhook_event, get_webhook_action_by_order_status,
    prepare_data_for_chat_webhook, prepare_data_for_notification_webhook,
    prepare_data_for_order_webhook, prepare_data_for_payment_webhook,
    prepare_data_for_review_webhook, prepare_data_for_ticket_webhook,
)
from db import crud
from db.models import (
    CRMTicket, Chat, ChatMessage, EWalletPayment, Group, Invoice, MediaObject, Review,
    StoreOrder, TextNotification, User,
)


class WebhookJournalService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
                "webhook_id",
            ),
    ):
        self.user: User = scopes.user
        self.profile_id: int = scopes.data.profile_id
        self.webhook_id: int = scopes.data.webhook_id

    async def get_list(
            self, params: schemas.AdminWebhookJournalParams,
    ):
        webhooks = await crud.get_webhook_journal_list(
            self.webhook_id, self.profile_id,
            self.user.id, params,
        )
        return [await self.journal_to_list_schema(webhook) for webhook in webhooks]

    async def resend_event(
            self, data: schemas.AdminResendWebhookEventData
    ) -> schemas.OkResponse:
        profile = await Group.get(self.profile_id)
        args = {
            "group_id": profile.id, "entity": data.entity,
            "entity_id": data.entity_id, "event_uuid": data.event_id
        }

        match data.entity:
            case schemas.WebhookEntityEnum.TICKET:
                ticket = await CRMTicket.get(data.entity_id)
                user = await User.get_by_id(ticket.user_id)
                data = await prepare_data_for_ticket_webhook(ticket, profile, user)
                args["data"] = data.dict()
                args["data_type"] = schemas.WebhookTicketDataSchema
                args["action"] = schemas.WebhookActionEnum.CREATED
            case schemas.WebhookEntityEnum.ORDER:
                store_order = await StoreOrder.get(data.entity_id)
                order_schema = await order_to_schema(store_order, profile.lang)
                webhook_data = await prepare_data_for_order_webhook(
                    order_schema, store_order.timezone, profile.id
                )
                webhook_action = get_webhook_action_by_order_status(
                    store_order.status, False
                )
                args["data"] = webhook_data.dict()
                args["data_type"] = schemas.WebhookOrderDataSchemax
                args["action"] = webhook_action
            case schemas.WebhookEntityEnum.PAYMENT:
                invoice = await Invoice.get(data.entity_id)
                invoice_schema = await invoice_to_schema(invoice, profile.lang, profile)
                webhook_data = await prepare_data_for_payment_webhook(
                    invoice_schema, invoice, profile.id
                )
                args["data"] = webhook_data.dict()
                args["data_type"] = schemas.WebhookPaymentDataSchema
                args[
                    "action"] = schemas.WebhookActionEnum.PAID if (invoice.status ==
                                                                   "payed") else (
                    schemas.WebhookActionEnum.CREATED)
            case schemas.WebhookEntityEnum.EWALLET_PAYMENT:
                ewallet_payment = await EWalletPayment.get(data.entity_id)
                if ewallet_payment.status == "paid":
                    args["action"] = schemas.WebhookActionEnum.PAID
                elif ewallet_payment.status == "failed":
                    args["action"] = schemas.WebhookActionEnum.FAILED
                elif ewallet_payment.status == "created":
                    args["action"] = schemas.WebhookActionEnum.CREATED
                else:
                    raise ValueError("Unknown ewallet payment status")
                args["data"] = ewallet_payment.dict()
                args["data_type"] = schemas.WebhookEWalletPaymentDataSchema
            case schemas.WebhookEntityEnum.TEXT_NOTIFICATION:
                text_notification = await TextNotification.get(data.entity_id)
                webhook_data: schemas.WebhookTextNotificationSchema = await (
                    prepare_data_for_notification_webhook(
                        text_notification
                    ))
                args["data"] = webhook_data.dict()
                args["data_type"] = schemas.WebhookTextNotificationSchema
                args[
                    "action"] = schemas.WebhookActionEnum.CREATED if not (
                    text_notification.is_read) else (
                    schemas.WebhookActionEnum.CHANGE_READ)
            case schemas.WebhookEntityEnum.REVIEW:
                review = await Review.get(data.entity_id)
                review_user = await User.get_by_id(review.user_id)
                webhook_data: schemas.WebhookReviewDataSchema = await (
                    prepare_data_for_review_webhook(
                        review, review_user,
                        profile
                    ))
                args["data"] = webhook_data.dict()
                args["data_type"] = schemas.WebhookReviewDataSchema
                args["action"] = schemas.WebhookActionEnum.CREATED
            case schemas.WebhookEntityEnum.CHAT:
                chat_message = await ChatMessage.get(data.entity_id)
                chat = await Chat.get(chat_message.chat_id)
                chat_user = await User.get_by_id(chat.user_id)

                media = None
                if chat_message.media_id:
                    media = await MediaObject.get(chat_message.media_id)
                webhook_data: schemas.WebhookNewChatMessageSchema = await (
                    prepare_data_for_chat_webhook(
                        chat=chat, message=chat_message, group=profile, user=chat_user,
                        media=media,
                    ))
                args["data"] = webhook_data.dict()
                args["data_type"] = schemas.WebhookNewChatMessageSchema
                args["action"] = schemas.WebhookActionEnum.NEW_MESSAGE

        await add_webhook_event(**args)

        return schemas.OkResponse()

    @classmethod
    async def journal_to_list_schema(
            cls, journal
    ) -> schemas.AdminWebhookJournalListSchema:
        return schemas.AdminWebhookJournalListSchema(
            id=journal.id,
            journal_uuid=journal.journal_uuid,
            entity=journal.entity,
            entity_id=journal.entity_id,
            action=journal.action,
            event_created_datetime=journal.event_created_datetime,
            event_start_datetime=journal.event_start_datetime,
            event_end_datetime=journal.event_end_datetime,
            json_data=journal.json_data,
            status=journal.status,
        )
