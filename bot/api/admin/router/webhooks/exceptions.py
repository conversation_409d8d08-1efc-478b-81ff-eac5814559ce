from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class AdminWebhookInvalidUrlError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin webhooks url not valid error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "webhooks_invalid_url",
            }
        )


class AdminWebhookUrlExistError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin webhooks url exist error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "webhooks_url_exist",
            }
        )


class AdminWebhookNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin webhooks webhook not found"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "webhooks_not_found",
            }
        )
