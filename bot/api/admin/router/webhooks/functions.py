import schemas
from db.models import Webhook


def webhook_to_admin_schema(webhook: Webhook) -> schemas.AdminWebhookSchema:
    return schemas.AdminWebhookSchema(
        id=webhook.id,
        endpoint_url=webhook.endpoint_url,
        blocked=webhook.blocked,
        is_enabled=webhook.is_enabled,
        entities=webhook.entities,
        last_sent_status=webhook.last_sent_status,
        retries_count=webhook.retries_count,
        last_sent_datetime=webhook.last_sent_datetime,
        description=webhook.description,
        persistent_webhook=webhook.persistent_webhook,
    )
