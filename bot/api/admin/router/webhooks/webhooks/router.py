from fastapi import APIRouter, Depends, Security

import schemas
from .service import WebhooksService

router = APIRouter()


@router.get("/")
async def get_webhooks_list(
    params: schemas.AdminListParams = Depends(),
    service: WebhooksService = Depends()
) -> list[schemas.AdminWebhookListSchema]:
    return await service.get_list(params)


@router.post("/")
async def create_webhook(
    data: schemas.AdminCreateWebhookData,
    service: WebhooksService = Security(scopes=["vm:create", "me:write"])
) -> schemas.AdminWebhookSchema:
    return await service.create_webhook(data)
