import uuid

import schemas
from api.admin.router.webhooks.functions import webhook_to_admin_schema
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import User
from config import WEBHOOK_MAX_RETRIES_FOR_BLOCK

from ..webhook.service import WebhookService


class WebhooksService:
    def __init__(
        self,
        scopes: ScopesCheckerService = ScopesCheckerService.depend(
            None,  # will be overridden in "write" routes
            "profile_id",
        ),
    ):
        self.user: User = scopes.user
        self.profile_id: int = scopes.data.profile_id

    async def get_list(self, params: schemas.AdminListParams):
        webhooks = await crud.get_webhook_list(self.profile_id, self.user.id, params)

        return [await self.webhook_to_list_schema(webhook) for webhook in webhooks]

    async def create_webhook(self, data: schemas.AdminCreateWebhookData) -> schemas.AdminWebhookSchema:
        await WebhookService.check_webhook_url(data.endpoint_url, None, self.profile_id)
        hook_id = uuid.uuid4().hex
        webhook = await crud.create_webhook(data, self.profile_id, hook_id)

        return webhook_to_admin_schema(webhook)

    @classmethod
    async def webhook_to_list_schema(cls, webhook) -> schemas.AdminWebhookListSchema:
        return schemas.AdminWebhookListSchema(
            id=webhook.id,
            endpoint_url=webhook.endpoint_url,
            blocked=webhook.retries_count >= WEBHOOK_MAX_RETRIES_FOR_BLOCK,
            is_enabled=webhook.is_enabled,
            entities=webhook.entities,
            description=webhook.description,
        )
