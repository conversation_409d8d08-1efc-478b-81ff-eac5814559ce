from decimal import Decimal, InvalidOperation

import schemas
from api.admin.helpers import check_object_access
from api.admin.router.extra_fee.exceptions import (
    ExtraFeeInvalidNameError, ExtraFeeInvalidPercentError, ExtraFeeInvalidPercentValueTypeError,
    ExtraFeeInvalidValueError,
    ExtraFeeInvalidValueTypeError,
)
from db.models import ExtraFeeSettings, Group


async def extra_fee_to_admin_list_schema(
        extra_fee_or_row: ExtraFeeSettings, profile_id: int,
        user_id: int
):
    schema = schemas.AdminExtraFeeListSchema.from_orm(extra_fee_or_row)

    if not hasattr(extra_fee_or_row, "read_allowed"):
        schema.read_allowed = await check_object_access(
            "extra_fee",
            extra_fee_or_row.id,
            profile_id, user_id,
            scope_name="read",
        )
    if not hasattr(extra_fee_or_row, "edit_allowed"):
        schema.edit_allowed = await check_object_access(
            "extra_fee",
            extra_fee_or_row.id,
            profile_id, user_id,
            scope_name="edit",
        )

    return schema


async def extra_fee_to_admin_schema(
        extra_fee: ExtraFeeSettings,
        profile: Group,
        user_id: int,
        read_allowed: bool = True
) -> schemas.AdminExtraFeeSchema:

    edit_allowed = await check_object_access(
        "extra_fee", extra_fee.id,
        profile.id, user_id,
        scope_name="edit",
    )

    if edit_allowed:
        read_allowed = True
    elif read_allowed is None:
        read_allowed = await check_object_access(
            "extra_fee", extra_fee.id,
            profile.id, user_id,
            scope_name="read",
        )

    return schemas.AdminExtraFeeSchema(
        id=extra_fee.id,
        name=extra_fee.name,
        is_active=extra_fee.is_active,
        extra_fee_percent=extra_fee.extra_fee_percent,
        extra_fee_value=extra_fee.extra_fee_value,
        position=extra_fee.position,
        read_allowed=read_allowed,
        edit_allowed=edit_allowed,
    )


def validate_extra_fee_data(
        data: schemas.AdminCreateExtraFeeData | schemas.AdminUpdateExtraFeeData,
) -> schemas.AdminExtraFeeValidateSchema:

    if not data.name:
        raise ExtraFeeInvalidNameError()

    if not data.extra_fee_value and not data.extra_fee_percent:
        raise ExtraFeeInvalidValueError()

    if data.extra_fee_value:
        try:
            Decimal(data.extra_fee_value)
        except InvalidOperation:
            raise ExtraFeeInvalidValueTypeError()

    if data.extra_fee_percent:
        try:
            percent = Decimal(data.extra_fee_percent)
            if percent < 0 or percent > 100:
                raise ExtraFeeInvalidPercentError()
        except InvalidOperation:
            raise ExtraFeeInvalidPercentValueTypeError()

    return schemas.AdminExtraFeeValidateSchema(detail='success')
