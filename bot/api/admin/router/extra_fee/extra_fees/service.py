import schemas
from api.admin.router.extra_fee.functions import (
    extra_fee_to_admin_list_schema,
    extra_fee_to_admin_schema, validate_extra_fee_data,
)
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Group, User


class ExtraFeesService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_extra_fees_list(
            self,
            search_text: str | None = None,
            exclude: list[int] | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminExtraFeeListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        extra_fees = (
                await crud.get_admin_extra_fees_list(
                    self.profile_id,
                    self.user.id,
                    search_text=search_text,
                    exclude=exclude,
                    offset=offset,
                    limit=limit,
                )
                or []
        )

        return [
            await extra_fee_to_admin_list_schema(
                extra_fee, self.profile_id, self.user.id
            )
            for extra_fee in extra_fees
        ]

    async def get_extra_fees_total_count(
            self,
            search_text: str | None = None,
    ) -> int:
        return await crud.get_admin_extra_fees_list(
            self.profile_id,
            self.user.id,
            search_text,
            is_count=True,
        )

    async def create_extra_fee(self, data: schemas.AdminCreateExtraFeeData):
        profile = await Group.get(self.profile_id)

        validate_extra_fee_data(data)

        extra_fee = await crud.create_extra_fee(profile, data, self.user)
        return await extra_fee_to_admin_schema(extra_fee, profile, self.user.id)
