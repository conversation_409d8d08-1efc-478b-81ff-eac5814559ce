from fastapi import APIRouter, Depends, Query, Security

import schemas
from .service import ExtraFeesService

router = APIRouter()


@router.get("/")
async def get_extra_fees_list(
        search_text: str | None = Query(None),
        exclude: list[int] | None = Query(None, description="array of extra_fee_groups ids to exclude"),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        service: ExtraFeesService = Depends()
) -> list[schemas.AdminExtraFeeListSchema]:
    return await service.get_extra_fees_list(search_text, exclude, offset, limit)


@router.get("/total_count")
async def get_extra_fees_total_count(
        search_text: str | None = Query(None),
        service: ExtraFeesService = Depends()
) -> int:
    return await service.get_extra_fees_total_count(search_text)


@router.post("/")
async def create_extra_fee(
        data: schemas.AdminCreateExtraFeeData,
        service: ExtraFeesService = Security(scopes=["me:write", "extra_fee:create"])
) -> schemas.AdminExtraFeeSchema:
    return await service.create_extra_fee(data)
