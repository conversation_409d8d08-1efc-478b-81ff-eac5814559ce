from abc import ABC

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BaseExtraFeeValidateError(ErrorWithHTTPStatus, ABC, base=True):
    groups = ["extra_fee"]


class UnknownValidateExtraFeeError(BaseExtraFeeValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "extra fee data unknown validation error"

    def __init__(self, message: str):
        self.message = message
        super().__init__(message=message)


class ExtraFeeInvalidNameError(BaseExtraFeeValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "extra fee data invalid name error"


class ExtraFeeInvalidValueError(BaseExtraFeeValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "extra fee data invalid value error"


class ExtraFeeInvalidValueTypeError(BaseExtraFeeValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "extra fee value must be a number"


class ExtraFeeInvalidPercentValueTypeError(BaseExtraFeeValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "extra fee percent value must be a number"


class ExtraFeeInvalidPercentError(BaseExtraFeeValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "extra fee percent must be between 0 and 100"
