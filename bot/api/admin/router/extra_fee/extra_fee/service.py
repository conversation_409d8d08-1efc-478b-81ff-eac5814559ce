from dataclasses import asdict
from typing import Any

import schemas
from api.admin.router.extra_fee.functions import (
    extra_fee_to_admin_schema,
    validate_extra_fee_data,
)
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import ExtraFeeSettings, Group, User
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class ExtraFeeService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "extra_fee:read",  # will be overridden in "write" routes
                "profile_id",
                "extra_fee_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.extra_fee_id: int = scopes.data.extra_fee_id

    # noinspection PyMethodMayBeStatic
    async def extra_fee_to_schema(
            self, extra_fee: ExtraFeeSettings, profile: Group | None = None
    ):
        if not profile:
            profile = await Group.get(self.profile_id)
        return await extra_fee_to_admin_schema(extra_fee, profile, self.user.id)

    async def get_extra_fee(self) -> ExtraFeeSettings:
        extra_fee = await crud.get_extra_fee(self.extra_fee_id, self.profile_id)
        if not extra_fee:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "extra_fee:read", self.available_data,
            )
        return extra_fee

    async def update_extra_fee(self, data: schemas.AdminUpdateExtraFeeData):

        validate_extra_fee_data(data)

        extra_fee = await self.get_extra_fee()
        profile = await Group.get(self.profile_id)

        await crud.update_extra_fee(extra_fee, data, )
        return await self.extra_fee_to_schema(extra_fee, profile)

    async def delete_extra_fee(self):
        extra_fee = await self.get_extra_fee()
        await extra_fee.update(is_deleted=True)
        return schemas.OkResponse()
