import schemas
from api.admin.helpers import check_object_access, get_translations_schemas_dict
from db import crud
from db.models import Group, StoreCharacteristic


async def characteristic_to_admin_list_schema(
        characteristic_or_row: StoreCharacteristic, profile_id: int, user_id: int
):
    schema = schemas.AdminCharacteristicListSchema.from_orm(characteristic_or_row)

    if not hasattr(characteristic_or_row, "read_allowed"):
        schema.read_allowed = await check_object_access(
            "characteristic",
            characteristic_or_row.id,
            profile_id, user_id,
            scope_name="read",
        )
    if not hasattr(characteristic_or_row, "edit_allowed"):
        schema.edit_allowed = await check_object_access(
            "characteristic",
            characteristic_or_row.id,
            profile_id, user_id,
            scope_name="edit",
        )
    return schema


async def characteristic_to_admin_schema(
        characteristic: StoreCharacteristic,
        profile: Group,
        user_id: int,
        read_allowed: bool = True
):

    edit_allowed = await check_object_access(
        "characteristic", characteristic.id,
        profile.id, user_id,
        scope_name="edit",
    )

    if edit_allowed:
        read_allowed = True
    elif read_allowed is None:
        read_allowed = await check_object_access(
            "characteristic", characteristic.id,
            profile.id, user_id,
            scope_name="read",
        )

    return schemas.AdminCharacteristicSchema(
        id=characteristic.id,
        name=characteristic.name,
        external_id=characteristic.external_id,
        external_type=characteristic.external_type,
        position=characteristic.position,
        excel_row_number=characteristic.excel_row_number,
        is_hide=characteristic.is_hide,
        filter_type=characteristic.filter_type,
        filters=await crud.get_characteristic_filters(characteristic.id, None),
        # Lang has not to be specified. No translations needed
        translations=await get_translations_schemas_dict(
            characteristic, profile, schemas.AdminCharacteristicTranslationSchema
        ),
        read_allowed=read_allowed,
        edit_allowed=edit_allowed,
    )
