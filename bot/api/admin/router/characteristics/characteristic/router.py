from fastapi import APIRouter, Depends, Query, Security

import schemas
from .service import CharacteristicService

router = APIRouter(
    prefix="/{characteristic_id}"
)


@router.get("/")
async def get_characteristic(
        service: CharacteristicService = Depends()
) -> schemas.AdminCharacteristicSchema:
    characteristic = await service.get_characteristic()
    return await service.characteristic_to_schema(characteristic)


@router.patch("/")
async def update_characteristic(
        data: schemas.AdminUpdateCharacteristicData,
        service: CharacteristicService = Security(
            scopes=["me:write", "characteristic:edit"]
        )
) -> schemas.AdminCharacteristicSchema:
    return await service.update_characteristic(data)


@router.delete("/")
async def delete_characteristic(
        service: CharacteristicService = Security(
            scopes=["me:write", "characteristic:edit"]
        )
) -> schemas.OkResponse:
    return await service.delete_characteristic()


@router.get("/values")
async def get_characteristic_values(
        product_group_id: int | None = Query(
            None,
            description="Product group id for get characteristic values from group"
        ),
        service: CharacteristicService = Depends()
) -> list[schemas.AdminCharacteristicValueSchema]:
    return await service.get_characteristic_values(product_group_id)
