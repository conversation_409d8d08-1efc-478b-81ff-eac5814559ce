from dataclasses import asdict
from typing import Any

import schemas
from api.admin.helpers import (
    get_translations_schemas_dict,
    validate_non_empty_fields_and_translations,
)
from api.admin.router.characteristics.functions import characteristic_to_admin_schema
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Group, StoreCharacteristic, User


class CharacteristicService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "characteristic:read",  # will be overridden in "write" routes
                "profile_id",
                "characteristic_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.characteristic_id: int = scopes.data.characteristic_id

    # noinspection PyMethodMayBeStatic
    async def characteristic_to_schema(
            self, characteristic: StoreCharacteristic, profile: Group | None = None
    ):
        if not profile:
            profile = await Group.get(self.profile_id)
        return await characteristic_to_admin_schema(
            characteristic, profile, self.user.id
        )

    async def get_characteristic(self) -> StoreCharacteristic:
        characteristic = await crud.get_characteristic_by_id_and_profile_id(
            self.characteristic_id, self.profile_id
        )
        if not characteristic:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "characteristic:read", self.available_data,
            )
        return characteristic

    async def update_characteristic(self, data: schemas.AdminUpdateCharacteristicData):
        characteristic = await self.get_characteristic()
        profile = await Group.get(self.profile_id)

        data_only_set = data.dict(exclude_unset=True)
        validate_non_empty_fields_and_translations(
            data_only_set, characteristic, "name"
        )
        await crud.update_characteristic(characteristic, data, profile.get_langs_list())
        return await self.characteristic_to_schema(characteristic, profile)

    async def delete_characteristic(self):
        characteristic = await self.get_characteristic()
        await crud.delete_characteristic(characteristic)
        return schemas.OkResponse()

    async def get_characteristic_values(self, product_group_id: int | None = None):
        profile = await Group.get(self.profile_id)
        characteristic = await self.get_characteristic()

        characteristic_values = await crud.get_characteristic_values_list(
            self.profile_id,
            characteristic.id,
            product_group_id,
        )

        return [schemas.AdminCharacteristicValueSchema(
            id=characteristic_value.id,
            value=characteristic_value.value,
            translations=await get_translations_schemas_dict(
                characteristic_value, profile,
                schemas.AdminCharacteristicValueTranslationSchema
            ),
        ) for characteristic_value in
            characteristic_values]
