import schemas
from api.exceptions import APIListLimitError
from api.admin.helpers import get_or_create_brand
from api.admin.router.characteristics.functions import (
    characteristic_to_admin_list_schema, characteristic_to_admin_schema,
)
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Group, User


class CharacteristicsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_characteristics_list(
            self,
            search_text: str | None = None,
            exclude: list[int] | None = None,
            include: list[int] | None = None,
            offset: int | None = None,
            limit: int = 10,
            exclude_from_product_group_id: int | None = None,
    ) -> list[schemas.AdminCharacteristicListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        characteristics = await crud.get_admin_characteristics_list(
            self.profile_id, self.user.id,
            search_text=search_text,
            exclude=exclude,
            include=include,
            offset=offset,
            limit=limit,
            need_check_access=True,
            exclude_from_product_group_id=exclude_from_product_group_id,
        ) or []

        return [await characteristic_to_admin_list_schema(characteristic, self.profile_id, self.user.id) for
                characteristic in characteristics]

    async def get_characteristics_total_count(
            self,
            search_text: str | None = None,
    ) -> int:
        return await crud.get_admin_characteristics_list(
            self.profile_id, self.user.id,
            search_text,
            is_count=True,
        )

    async def create_characteristic(
            self, data: schemas.AdminCreateCharacteristicData, add_to_product_group_id: int | None = None
    ) -> schemas.AdminCharacteristicSchema:
        profile = await Group.get(self.profile_id)
        brand = await get_or_create_brand(profile)

        characteristic = await crud.create_characteristic(brand, data, self.user, add_to_product_group_id)
        return await characteristic_to_admin_schema(characteristic, profile, self.user.id)
