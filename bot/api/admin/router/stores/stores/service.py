from dataclasses import asdict
from typing import Any

import schemas
from api.admin.helpers import get_or_create_brand
from api.admin.router.stores.functions import (
    create_store_banner_func,
    store_to_admin_schema,
)
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Group, User


class StoresService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.data: dict[str, Any] = asdict(scopes.data)

    async def get_stores_list(
            self,
            offset: int | None = None,
            limit: int | None = None,
            search_text: str | None = None,
            different_currency_only: bool | None = None,
            excluded_stores_ids: list[int] | None = None,
    ):
        stores = await crud.get_admin_stores_list(
            self.profile_id, self.user.id,
            offset, limit, search_text, False,
            different_currency_only,
            excluded_stores_ids
        ) or []

        store_list = []
        for store in stores:
            store_data = schemas.AdminStoreListSchema.from_orm(store)
            store_data.banners_count = len(store.banners or [])

            store_list.append(store_data)

        return store_list

    async def get_stores_total_count(self, search_text: str | None = None) -> int:
        return await crud.get_admin_stores_list(
            self.profile_id, self.user.id,
            search_text=search_text,
            is_count=True
        )

    async def get_store_banner(self, banner_id: int):
        return await crud.get_store_banner_by_id(banner_id)

    async def create_store(
            self, data: schemas.AdminCreateStoreData
    ) -> schemas.CreateAdminStoreSchema:
        profile = await Group.get(self.profile_id)
        brand = await get_or_create_brand(profile)
        store_banners = data.banners
        store_payment_data = data.payment_data
        del data.banners, data.payment_data

        store = await crud.create_store(brand, data, self.user)

        if store_banners:
            for banner in store_banners or []:
                await create_store_banner_func(store.id, banner)

        extra_data = None

        await crud.create_object_payment_settings_for_object(
            self.profile_id,
            store_id=store.id,
            payment_data=store_payment_data
        )

        return schemas.CreateAdminStoreSchema(
            store=await store_to_admin_schema(store, profile, self.user.id),
            extra_data=extra_data,
        )
