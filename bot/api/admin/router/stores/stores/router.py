from fastapi import APIRouter, Depends, Query, Security

import schemas
from api.admin.router.stores.stores.service import StoresService

router = APIRouter()


@router.get("/")
async def get_stores_list(
        offset: int | None = Query(None),
        limit: int | None = Query(10),
        search_text: str | None = Query(None),
        excluded_stores_ids: list[int] | None = Query(None),
        different_currency_only: bool | None = Query(None),
        service: StoresService = Depends()
) -> list[schemas.AdminStoreListSchema]:
    return await service.get_stores_list(
        offset, limit, search_text, different_currency_only,
        excluded_stores_ids
    )


@router.get("/total_count")
async def get_stores_total_count(
        search_text: str | None = None,
        service: StoresService = Depends()
) -> int:
    return await service.get_stores_total_count(search_text)


@router.get('/banners/{banner_id}')
async def get_one_store_banner(
        banner_id: int,
        service: StoresService = Depends()
) -> schemas.AdminStoreBannerSchema:
    return await service.get_store_banner(banner_id)


@router.post("/")
async def create_store(
        data: schemas.AdminCreateStoreData,
        service: StoresService = Security(scopes=["me:write", "store:create"])
) -> schemas.CreateAdminStoreSchema:
    return await service.create_store(data)
