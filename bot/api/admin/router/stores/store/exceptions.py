from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class StoreInvalidCoordinatesError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "store invalid coordinates error"

    def __init__(self, latitude: str, longitude: str):
        super().__init__(
            latitude=latitude or "-",
            longitude=longitude or "-",
            detail_data={
                "error_code": "invalid_coordinates",
                "latitude": latitude,
                "longitude": longitude,
            }
        )


class StoreTranslationForNotExistingDescriptionError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "store translation for not existing description error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "translation_for_not_existing_description"
            }
        )
