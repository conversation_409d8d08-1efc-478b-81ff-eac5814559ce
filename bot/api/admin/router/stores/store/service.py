import logging
from dataclasses import asdict
from typing import Any

from fastapi import UploadFile

import schemas
from api.admin.helpers import validate_non_empty_fields_and_translations
from api.admin.router.categories.functions import category_to_admin_list_schema
from api.admin.router.core.funcs import payment_provider_data_to_schema
from api.admin.router.payment_settings.exceptions import AdminPaymentValidationError
from api.admin.router.payment_settings.validator import PaymentSettingsValidator
from api.admin.router.products.functions import product_to_admin_list_schema
from api.admin.router.stores.functions import store_to_admin_schema
from api.admin.router.stores.store.exceptions import StoreInvalidCoordinatesError
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.helpers import time_to_utc
from core.media_manager import media_manager
from core.payment.funcs import get_payment_default_name
from db import crud
from db.models import (
    Group, MediaObject, PaymentSettings, ProductToStore, Store, StoreBanner,
    StoreCategory,
    StoreCategoryToStore, StoreProduct, User,
)
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from utils.form_data_to_dict import form_data_to_dict


class StoreService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "store:read",  # will be overridden in "write" routes
                "profile_id",
                "store_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: Any = asdict(scopes.data)  # dataclass
        self.store_id: int = scopes.data.store_id

    async def store_to_schema(self, store: Store, profile: Group | None = None):
        if not profile:
            profile = await Group.get(self.profile_id)
        return await store_to_admin_schema(store, profile, self.user.id)

    async def get_store(self):
        store = await crud.get_store_by_id_and_profile_id(
            self.store_id, self.profile_id
        )
        if not store:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "store:read", self.available_data,
            )
        return store

    async def get_store_banners(self):
        return await crud.get_banners_list(self.store_id)

    async def create_store_banner(self, data: schemas.AdminStoreCreateBannerForm):
        media = await media_manager.save_from_upload_file(data.file)
        banner = await StoreBanner.create(
            store_id=self.store_id,
            name=data.name,
            url=data.url,
            position=data.position,
            is_visible=True,
            media=media
        )
        return schemas.AdminStoreBannerSchema(
            id=banner.id,
            name=banner.name,
            url=banner.url,
            position=banner.position,
            is_visible=True,
            image_url=media.url
        )

    async def update_store_banner(
            self, banner_id: int, data: schemas.AdminStoreUpdateBanner
    ):
        banner = await StoreBanner.get(id=banner_id)

        if not banner:
            return None

        data_to_update = form_data_to_dict(data, True)

        if data_to_update.get("file"):
            media = await media_manager.save_from_upload_file(data.file)
            data_to_update["media_id"] = media.id if media else banner.media_id
            del data_to_update["file"]
        else:
            media = await MediaObject.get(banner.media_id)

        image_url = media.url

        await banner.update(**data_to_update)

        return schemas.AdminStoreBannerSchema(
            id=banner.id,
            name=banner.name,
            url=banner.url,
            position=banner.position,
            is_visible=banner.is_visible,
            image_url=image_url
        )

    async def update_positions_of_store_banners(self, banners_ids: list[int]):
        await crud.update_position_in_store_banners(banners_ids)
        return await crud.get_banners_list(self.store_id)

    async def delete_store_banner(self, banner_id: int):
        return await crud.delete_banner(banner_id)

    async def update_store(self, data: schemas.AdminUpdateStoreData):
        store = await self.get_store()

        if (
                any((data.latitude, data.longitude)) is not
                all((data.latitude, data.longitude))
        ):
            raise StoreInvalidCoordinatesError(
                latitude=data.latitude,
                longitude=data.longitude,
            )

        data_only_set = data.dict(exclude_unset=True)

        validate_non_empty_fields_and_translations(
            data_only_set, store, "name", "currency"
        )

        profile = await Group.get(self.profile_id)

        await crud.update_store(
            store, data, profile.get_langs_list(with_main_lang=False), profile
        )
        return await self.store_to_schema(store, profile)

    async def update_store_image(self, file: UploadFile):
        store = await self.get_store()
        media = await media_manager.save_from_upload_file(file)

        await store.update(media_id=media.id)
        return await self.store_to_schema(store)

    async def delete_store_image(self):
        store = await self.get_store()
        await store.update(media_id=None)
        return await self.store_to_schema(store)

    async def delete_store(self):
        store = await self.get_store()
        await store.update(is_deleted=True)
        return schemas.OkResponse()

    async def get_store_products(
            self,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminProductListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        products = await crud.get_admin_products_list(
            self.profile_id,
            user_id=self.user.id,
            store_id=self.store_id,
            category_ids=category_ids,
            search_text=search_text,
            offset=offset,
            limit=limit,
            need_check_access=False,
        )

        return [
            await product_to_admin_list_schema(product, self.profile_id, self.user.id)
            for product in products
        ]

    async def get_store_products_count(
            self,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
    ) -> int:
        return await crud.get_admin_products_list(
            self.profile_id,
            user_id=self.user.id,
            category_ids=category_ids,
            search_text=search_text,
            need_check_access=False,
            is_count=True,
        )

    async def connect_products_to_store(
            self, data: schemas.AdminConnectProductsToObjectData
    ):
        store = await self.get_store()

        products = await crud.get_and_validate_access_on_objects(
            "product", StoreProduct,
            data.products, self.user.id, self.profile_id,
        )

        await crud.connect_related_objects(store, "products", products, data.replace)
        return schemas.AdminProductsConnectedToObjectResult(
            replaced=data.replace,
            connected_products=[
                await product_to_admin_list_schema(
                    product, self.profile_id, self.user.id,
                )
                for product in products
            ]
        )

    async def disconnect_products_from_store(
            self, data: schemas.AdminDisconnectProductsData
    ):
        store = await self.get_store()

        await crud.disconnect_m2m_related_objects(
            ProductToStore,
            "store_id", store.id,
            "product_id", data.products,
            data.disconnect_all,
        )
        return schemas.AdminProductsDisconnectedResult(
            is_all_deleted=data.disconnect_all,
            products_ids_disconnected=data.products or [] if not data.disconnect_all
            else None,
        )

    async def get_store_categories(
            self,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminCategoryListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        categories = await crud.get_admin_categories_list(
            self.profile_id,
            user_id=self.user.id,
            store_ids=[self.store_id],
            search_text=search_text,
            offset=offset,
            limit=limit,
            need_check_access=False,
        )

        return [
            await category_to_admin_list_schema(category, self.profile_id, self.user.id)
            for category in categories]

    async def connect_categories_to_store(
            self, data: schemas.AdminConnectCategoriesData
    ):
        store = await self.get_store()

        categories = await crud.get_and_validate_access_on_objects(
            "category", StoreCategory,
            data.categories, self.user.id, self.profile_id,
        )

        await crud.connect_related_objects(
            store, "categories", categories, data.replace
        )
        return schemas.AdminCategoriesConnectedResult(
            replaced=data.replace,
            connected_categories=[
                await category_to_admin_list_schema(
                    category, self.profile_id, self.user.id
                )
                for category in categories
            ]
        )

    async def disconnect_categories_from_store(
            self, data: schemas.AdminDisconnectCategoriesData
    ):
        store = await self.get_store()

        await crud.disconnect_m2m_related_objects(
            StoreCategoryToStore,
            "store_id", store.id,
            "category_id", data.categories,
            data.disconnect_all,
        )
        return schemas.AdminCategoriesDisconnectedResult(
            is_all_deleted=data.disconnect_all,
            categories_ids_disconnected=data.categories or [] if not
            data.disconnect_all else None,
        )

    async def update_working_times(self, data: list[schemas.WorkingDaySchema]):
        store = await self.get_store()

        await crud.delete_days_by_store(store.id)
        for day in data:
            if day.slots:
                for slot in day.slots:
                    slot.start_time = await time_to_utc(
                        slot.start_time, self.profile_id
                    )
                    slot.end_time = await time_to_utc(slot.end_time, self.profile_id)
            await crud.create_working_times(store.id, [day])

        return True

    async def get_store_payment_data(
            self,
    ) -> list[schemas.AdminStorePaymentData]:

        invoice_template_payment_data = await crud.get_object_payment_data(
            self.profile_id, store_id=self.store_id
        )

        return [schemas.AdminStorePaymentData(
            store_id=self.store_id,
            payment_settings_id=ps.id,
            is_enabled=pd.is_enabled,
            json_data=payment_provider_data_to_schema(
                ps.payment_method, pd.json_data
            ) if pd.json_data else None,
            name=ps.name,
            default_name=await get_payment_default_name(
                ps.payment_method, self.lang, ps.is_online,
                ewallet_id=ps.json_data.get(
                    "ewallet_id", None
                ) if ps.json_data else None
            ),
        ) for pd, ps in invoice_template_payment_data]

    async def upd_store_payment_data(
            self, payment_data: list[schemas.CreateAdminStorePaymentData]
    ):

        brand = await crud.get_brand_by_group(self.profile_id)
        for pd in payment_data:
            try:
                payment_settings = await PaymentSettings.get(pd.payment_settings_id)
                is_fiscal = False
                json_data = None
                if getattr(payment_data, "json_data", None) and getattr(
                        pd.json_data, "data", None
                ):
                    json_data = pd.json_data.data
                if json_data and payment_settings.payment_method == "liqpay":
                    is_fiscal = getattr(json_data, "is_need_fiscal", False)

                validator = PaymentSettingsValidator(
                    self.lang, payment_settings.payment_method, brand.id, None,
                    json_data.dict() if json_data else None,
                    is_update=True, is_store=True, is_fiscal=is_fiscal,
                )
                processed_json_data = await validator.validate()
                if json_data and processed_json_data:
                    pd.json_data.data = processed_json_data
            except Exception as ex:
                logging.error(ex, exc_info=True)
                raise AdminPaymentValidationError()

        await crud.update_object_payment_settings_for_object(
            self.profile_id,
            store_id=self.store_id, payment_data=payment_data
        )

        return await self.get_store_payment_data()
