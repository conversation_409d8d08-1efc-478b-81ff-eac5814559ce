from fastapi import APIRouter, Depends, Query, Security, UploadFile

import schemas
from api.admin.router.stores.store.service import StoreService
from schemas import CreateAdminStorePaymentData

router = APIRouter(
    prefix="/{store_id}",
)


@router.get("/")
async def get_store(
        service: StoreService = Depends()
) -> schemas.AdminStoreSchema:
    store = await service.get_store()
    return await service.store_to_schema(store)


@router.patch("/")
async def update_store(
        data: schemas.AdminUpdateStoreData,
        service: StoreService = Security(scopes=["store:edit", "me:write"])
) -> schemas.AdminStoreSchema:
    return await service.update_store(data)


@router.get('/banners')
async def get_list_store_banners(
        service: StoreService = Depends()
) -> list[schemas.AdminStoreBannerSchema]:
    return await service.get_store_banners()


@router.post('/banners')
async def create_store_banner(
        data: schemas.AdminStoreCreateBannerForm = Depends(),
        service: StoreService = Depends()
) -> schemas.AdminStoreBannerSchema:
    return await service.create_store_banner(data)


@router.patch("/banners/{banner_id}")
async def update_store_banner(
        banner_id: int,
        data: schemas.AdminStoreUpdateBanner = Depends(),
        service: StoreService = Depends()
) -> schemas.AdminStoreBannerSchema:
    return await service.update_store_banner(banner_id, data)


@router.patch("/banners/positions/update")
async def update_positions_in_store_banners(
        data: schemas.AdminStoreBannersUpdatePositionsSchema,
        service: StoreService = Depends()
) -> list[schemas.AdminStoreBannerSchema]:
    return await service.update_positions_of_store_banners(data.banners_ids)


@router.delete("/banners/{banner_id}")
async def delete_store_banner(
        banner_id: int,
        service: StoreService = Depends()
) -> schemas.OkResponse:
    return await service.delete_store_banner(banner_id)


@router.post("/update_image")
async def update_store_image(
        file: UploadFile,
        service: StoreService = Security(scopes=["store:edit", "me:write"])
) -> schemas.AdminStoreSchema:
    return await service.update_store_image(file)


@router.delete("/delete_image")
async def delete_store_image(
        service: StoreService = Security(scopes=["store:edit", "me:write"])
) -> schemas.AdminStoreSchema:
    return await service.delete_store_image()


@router.delete("/")
async def delete_store(
        service: StoreService = Security(scopes=["store:edit", "me:write"])
) -> schemas.OkResponse:
    return await service.delete_store()


@router.get("/products")
async def get_store_products(
        category_ids: list[int] | None = Query(None),
        search_text: str | None = Query(None),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        service: StoreService = Depends(),
) -> list[schemas.AdminProductListSchema]:
    return await service.get_store_products(category_ids, search_text, offset, limit)


@router.get("/productsCount")
async def get_store_products_count(
        category_ids: list[int] | None = Query(None),
        search_text: str | None = Query(None),
        service: StoreService = Depends(),
) -> int:
    return await service.get_store_products_count(category_ids, search_text)


@router.post("/products")
async def connect_products_to_store(
        data: schemas.AdminConnectProductsToObjectData,
        service: StoreService = Security(scopes=["me:write", "store:edit"])
) -> schemas.AdminProductsConnectedToObjectResult:
    return await service.connect_products_to_store(data)


@router.post("/products/disconnect")
async def disconnect_products_from_store(
        data: schemas.AdminDisconnectProductsData,
        service: StoreService = Security(scopes=["me:write", "store:edit"])
) -> schemas.AdminProductsDisconnectedResult:
    return await service.disconnect_products_from_store(data)


@router.get("/categories")
async def get_store_categories(
        search_text: str | None = Query(None),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit categories. Max: 100"),
        service: StoreService = Depends(),
) -> list[schemas.AdminCategoryListSchema]:
    return await service.get_store_categories(search_text, offset, limit)


@router.post("/categories")
async def connect_categories_to_store(
        data: schemas.AdminConnectCategoriesData,
        service: StoreService = Security(scopes=["me:write", "store:edit"])
) -> schemas.AdminCategoriesConnectedResult:
    return await service.connect_categories_to_store(data)


@router.post("/categories/disconnect")
async def disconnect_categories_from_store(
        data: schemas.AdminDisconnectCategoriesData,
        service: StoreService = Security(scopes=["me:write", "store:edit"])
) -> schemas.AdminCategoriesDisconnectedResult:
    return await service.disconnect_categories_from_store(data)


@router.put("/working_times")
async def update_working_times(
        data: list[schemas.WorkingDaySchema],
        service: StoreService = Security(scopes=["me:write", "store:edit"])
) -> bool:
    return await service.update_working_times(data)


@router.get("/payment_data")
async def get_store_payment_data(
        service: StoreService = Depends()
) -> list[schemas.AdminStorePaymentData]:
    return await service.get_store_payment_data()


@router.patch("/payment_data")
async def update_store_payment_data(
        payment_data: list[CreateAdminStorePaymentData],
        service: StoreService = Depends()
) -> list[schemas.AdminStorePaymentData]:
    return await service.upd_store_payment_data(payment_data)
