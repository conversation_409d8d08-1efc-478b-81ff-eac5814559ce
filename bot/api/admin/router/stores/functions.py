from psutils.convertors import time_to_str
from psutils.date_time import localise_time

import schemas
from api.admin.helpers import check_object_access, get_translations_schemas_dict
from core.helpers import utc_time_to_local
from core.store.functions.billing_settings import get_billing_settings_schema
from core.store.functions.store import banners_to_schema
from db import crud
from db.models import (
    Group, MediaObject, Store, StoreBanner, StoreCustomField,
)


async def store_to_admin_schema(store: Store, profile: Group, user_id: int):
    custom_fields = await StoreCustomField.get_list(store_id=store.id)
    billing_settings = await get_billing_settings_schema(store.id, store.brand_id)
    working_days_db = await crud.get_working_times(store.id)

    timezone = profile.timezone

    working_days_schema = []
    for day_data in working_days_db:
        working_day = day_data.day

        slots_schemas: list[schemas.WorkingSlotSchema] = []
        for slot in day_data.slots:
            slots_schemas.append(
                schemas.WorkingSlotSchema(
                    id=slot.id,
                    start_time_text=time_to_str(
                        localise_time(slot.start_time, timezone, "utc")
                    ),
                    end_time_text=time_to_str(
                        localise_time(slot.end_time, timezone, "utc")
                    ),
                    start_time=utc_time_to_local(
                        slot.start_time, profile.timezone
                    ),
                    end_time=utc_time_to_local(slot.end_time, profile.timezone),
                )
            )

        working_days_schema.append(
            schemas.WorkingDaySchema(
                id=working_day.id,
                day=working_day.day,
                is_weekend=working_day.is_weekend,
                slots=slots_schemas,
            )
        )

    edit_allowed = await check_object_access("store", store.id, profile.id, user_id)

    if edit_allowed:
        read_allowed = True
    else:
        read_allowed = await check_object_access("store", store.id, profile.id, user_id)

    return schemas.AdminStoreSchema(
        id=store.id,
        is_enabled=store.is_enabled,
        profile_id=profile.id,
        name=store.name,
        currency=store.currency,
        external_id=store.external_id,
        external_type=store.external_type,
        description=store.description,
        ai_description=store.ai_description,
        latitude=store.latitude,
        longitude=store.longitude,
        custom_fields=custom_fields,
        banners=await banners_to_schema(await StoreBanner.get_list(store_id=store.id)),
        image_url=await store.image_media_url,
        billing_settings=billing_settings,
        working_days=working_days_schema,
        translations=await get_translations_schemas_dict(
            store, profile, schemas.AdminStoreTranslationSchema
        ),
        read_allowed=read_allowed,
        edit_allowed=edit_allowed,
        is_enabled_emenu=store.is_enabled_emenu,
        task_id=store.task_id,
    )


async def create_store_banner_func(
        store_id: int, data: schemas.AdminStoreCreateBanner
) -> schemas.AdminStoreBannerSchema:
    media = await MediaObject.get(data.media_id)
    banner = await StoreBanner.create(
        store_id=store_id,
        name=data.name,
        url=data.url,
        position=data.position,
        is_visible=True,
        media=media
    )
    return schemas.AdminStoreBannerSchema(
        id=banner.id,
        name=banner.name,
        url=banner.url,
        position=banner.position,
        is_visible=True,
        image_url=media.url
    )
