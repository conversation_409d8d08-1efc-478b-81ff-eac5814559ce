from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class AdminShipmentPriceMinMaxError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments price min amount error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_min_price_amount",
            }
        )


class AdminShipmentNotFound(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments shipment not found error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_shipment_not_found",
            }
        )


class AdminShipmentUpdateStoreError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments update store error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_update_store",
            }
        )


class AdminShipmentUpdatePaymentsError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments update payments error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_update_payments",
            }
        )


class AdminShipmentDeleteError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "shipments_delete_shipment"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "qr_menu_invalid_invoice_template",
            }
        )


class AdminShipmentPriceNotFound(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments price not found error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_price_not_found",
            }
        )


class AdminShipmentDeliveryMinMaxTimeError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments delivery min time error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_delivery_min_interval",
            }
        )


class AdminShipmentInvalidDataType(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments invalid data type error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_data_type",
            }
        )


class AdminShipmentNoDeliveryCantUpdate(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments cant edit shipment error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_no_delivery_edit",
            }
        )


class AdminShipmentNameRequired(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments name required error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_name_required",
            }
        )


class AdminShipmentInvalidType(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments unknown delivery type error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_unknown_type",
            }
        )


class AdminCreateShipmentError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments create shipment error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_create",
            }
        )


class AdminShipmentZoneNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones zone not found error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_not_found",
            }
        )


class AdminShipmentZoneNoPolygonError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones zone no polygon error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_no_polygon",
            }
        )


class AdminShipmentZoneRadiusForPolygonError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones zone radius for polygon error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_radius_for_polygon",
            }
        )


class AdminShipmentZoneNoRadiusError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones zone no radius error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_no_radius",
            }
        )


class AdminShipmentZonePolygonForRadiusError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones zone polygon for radius error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_polygon_for_radius",
            }
        )


class AdminShipmentZoneRadiusAndPolygonError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones zone radius and polygon error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_radius_and_polygon",
            }
        )


class AdminShipmentZoneCreateStoreSettingError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones zone create store custom error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_store_setting",
            }
        )


class AdminShipmentZoneCreateError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones zone create error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_create",
            }
        )


class AdminShipmentZoneDeleteError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones zone delete error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_delete",
            }
        )


class AdminShipmentDeleteBaseError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments delete base error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipments_delete_base",
            }
        )


class AdminShipmentsZonesNoPolygonAndDistanceError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones no polygon and distance error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_no_polygon_and_distance",
            }
        )


class AdminShipmentZonePolygonNameError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin shipments zones polygon name error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "shipment_zone_polygon_name",
            }
        )
