from typing import Type

import schemas
from db.models import BrandCustomSettings, StoreCustomSettings
from utils.type_vars import T
from .exceptions import (
    AdminShipmentDeliveryMinMaxTimeError, AdminShipmentInvalidDataType,
    AdminShipmentInvalidType, AdminShipmentNameRequired, AdminShipmentPriceMinMaxError,
)


async def get_is_disabled_in_any_store(shipment_id: int) -> bool:
    is_disabled_in_any_store = False

    store_settings = await StoreCustomSettings.get_list(custom_settings_id=shipment_id)
    if store_settings:
        for store_setting in store_settings:
            if store_setting.is_enabled is False:
                is_disabled_in_any_store = True
                break

    return is_disabled_in_any_store


async def get_is_enabled_in_any_store(shipment_id: int) -> bool:
    is_enabled_in_any_store = False

    store_settings = await StoreCustomSettings.get_list(custom_settings_id=shipment_id)
    if store_settings:
        for store_setting in store_settings:
            if store_setting.is_enabled is True:
                is_enabled_in_any_store = True
                break

    return is_enabled_in_any_store


def validate_shipment(
        shipment: BrandCustomSettings,
        data: schemas.AdminUpdateCustomShipmentSchema |
              schemas.AdminUpdateBaseShipmentSchema |
              schemas.AdminUpdateCustomShipmentGroupSchema,
        schema: Type[T],
):
    if isinstance(data, schemas.AdminUpdateCustomShipmentSchema) or isinstance(
            data, schemas.AdminUpdateBaseShipmentSchema
    ):
        if data.min_time and data.max_time and data.min_time > data.max_time:
            raise AdminShipmentDeliveryMinMaxTimeError()

    if not isinstance(data, schema):
        raise AdminShipmentInvalidDataType()

    match shipment.custom_type:
        case "shipment":
            exclude_dict = None
            if shipment.base_type == "pickup" or shipment.base_type == "in_store":
                exclude_dict = {
                    "map_countries",
                    "enable_any_address_from_map",
                }

            data_dict = data.dict(exclude=exclude_dict)
            return schema(**data_dict)

        case "custom_shipment":
            if not data.name:
                raise AdminShipmentNameRequired()

            exclude_dict = None
            if data.base_type == "pickup":
                exclude_dict = {
                    "need_address",
                    "map_countries",
                    "enable_any_address_from_map"
                }

            data_dict = data.dict(exclude=exclude_dict)
            return schema(**data_dict)

        case "custom_shipment_group":
            if not data.name:
                raise AdminShipmentNameRequired()

            return data
        case _:
            raise AdminShipmentInvalidType()


def validate_shipment_price(data: schemas.ShipmentPriceBase):
    if data.maximum_order_amount:
        if data.minimum_order_amount > data.maximum_order_amount:
            raise AdminShipmentPriceMinMaxError()
