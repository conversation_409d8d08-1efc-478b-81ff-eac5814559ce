from typing import Type

from fastapi import UploadFile

import schemas
from api.admin.helpers import get_translations_schemas_dict
from core.auth.services.scopes_checker import ScopesCheckerService
from core.media_manager import media_manager
from db import crud
from db.models import (
    BrandCustomSettings, Group, MediaObject, ShipmentPrice, ShipmentZone, Store,
    StoreCustomSettings, User,
)
from db.models.store.custom_settings import PaymentSettingsToShipment
from utils.text import f
from utils.type_vars import T
from ..exceptions import (
    AdminShipmentDeleteBaseError, AdminShipmentDeleteError, AdminShipmentNotFound,
    AdminShipmentPriceNotFound, AdminShipmentUpdateStoreError,
    AdminShipmentZoneCreateError, AdminShipmentZoneCreateStoreSettingError,
    AdminShipmentZoneDeleteError, AdminShipmentZoneNoPolygonError,
    AdminShipmentZoneNoRadiusError, AdminShipmentZoneNotFoundError,
    AdminShipmentZonePolygonForRadiusError, AdminShipmentZonePolygonNameError,
    AdminShipmentZoneRadiusAndPolygonError, AdminShipmentZoneRadiusForPolygonError,
    AdminShipmentsZonesNoPolygonAndDistanceError,
)
from ..functions import (
    get_is_disabled_in_any_store, get_is_enabled_in_any_store, validate_shipment,
    validate_shipment_price,
)


class ShipmentService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_shipment(self, shipment_id: int) -> schemas.AdminShipmentItemSchema:
        brand = await crud.get_brand_by_group(self.profile_id)
        shipment = await BrandCustomSettings.get(id=shipment_id, brand_id=brand.id)

        if not shipment:
            raise AdminShipmentNotFound()

        return await self.shipment_to_schema(shipment)

    async def shipment_to_schema(
            self, shipment: BrandCustomSettings
    ) -> schemas.AdminShipmentItemSchema:
        media = await MediaObject.get(shipment.media_id)
        shipment_time = await crud.get_shipment_time(settings_id=shipment.id)
        shipment_prices = [
            schemas.ShipmentPrice.from_orm(shipment_price)
            for shipment_price in
            await crud.get_shipment_prices(shipment.brand_id, settings_id=shipment.id)
        ]
        profile = await Group.get(self.profile_id)

        label_comment_default_text = None
        if not shipment.label_comment:
            label_comment_default_text = await f(
                "store custom shipment label comment default", profile.lang
            )

        return schemas.AdminShipmentItemSchema(
            description=shipment.description,
            icon_url=media.url if media else None,
            need_comment=shipment.need_comment,
            label_comment=shipment.label_comment or label_comment_default_text,
            need_address=shipment.need_address,
            base_type=shipment.base_type,

            is_paid_separately=shipment.is_paid_separately,
            allow_online_payment=shipment.allow_online_payment,
            allow_cash_payment=shipment.allow_cash_payment,
            delivery_datetime_mode=shipment.delivery_datetime_mode,
            not_working_hours=shipment_time.not_working_hours.value if shipment_time
            else "nothing",
            enabled_tips=shipment.enabled_tips,
            min_time=shipment_time.min_time if shipment_time else None,
            max_time=shipment_time.max_time if shipment_time else None,
            execution_time=shipment_time.order_execution if shipment_time else None,
            is_enabled=shipment.is_enabled,
            map_countries=shipment.map_countries_list or [],
            enabled_any_address_from_map=shipment.enabled_any_address_from_map,

            name=await shipment.get_name(self.lang),
            prices=shipment_prices,

            enabled_store_ids=await self.__get_enabled_store_ids_for_shipment(
                shipment.id
            ),
            custom_payments=await self.__get_custom_payments_for_shipment(
                shipment.payment_settings, shipment.brand_id
            ),

            id=shipment.id,
            is_disabled_in_any_store=await get_is_disabled_in_any_store(shipment.id),
            is_enabled_in_any_store=await get_is_enabled_in_any_store(shipment.id),
            type=shipment.custom_type,

            translations=await get_translations_schemas_dict(
                shipment, profile, schemas.AdminShipmentTranslationSchema
            )
        )

    @classmethod
    async def __get_custom_payments_for_shipment(
            cls, payments_settings_to_shipment: list[PaymentSettingsToShipment],
            brand_id: int
    ) -> list[schemas.AdminShipmentCustomPaymentSchema]:
        custom_payments_schema = []
        enabled_payments_settings_ids = []
        if payments_settings_to_shipment:
            enabled_payments_settings_ids = [payment_setting.id for payment_setting in
                                             payments_settings_to_shipment]

        payment_settings = await crud.get_payment_methods(brand_id, False)

        if payment_settings:
            for payment_setting in payment_settings:
                custom_payments_schema.append(
                    schemas.AdminShipmentCustomPaymentSchema(
                        id=payment_setting.id,
                        name=payment_setting.name,
                        payment_method=payment_setting.payment_method,
                        is_enabled=payment_setting.id in enabled_payments_settings_ids,
                    )
                )
        return custom_payments_schema

    async def __get_enabled_store_ids_for_shipment(self, shipment_id: int) -> list[
        schemas.AdminShipmentStore]:
        brand = await crud.get_brand_by_group(self.profile_id)
        stores = await Store.get_list(brand_id=brand.id)
        res = []
        for store in stores:
            store_setting = await StoreCustomSettings.get(
                custom_settings_id=shipment_id, store_id=store.id
            )
            if not store_setting or store_setting.is_enabled is None:
                value = None
            elif store_setting.is_enabled:
                value = True
            else:
                value = False

            res.append(
                schemas.AdminShipmentStore(
                    store_id=store.id, is_enabled=value,
                )
            )

        return res

    async def update_shipment_main_data(
            self,
            data: schemas.AdminUpdateBaseShipmentSchema |
                  schemas.AdminUpdateCustomShipmentSchema |
                  schemas.AdminUpdateCustomShipmentGroupSchema,
            shipment_id: int,
            schema: Type[T],
    ) -> schemas.AdminShipmentItemSchema:
        profile = await Group.get(self.profile_id)
        brand = await crud.get_brand_by_group(profile.id)
        shipment = await BrandCustomSettings.get(id=shipment_id, brand_id=brand.id)
        if not shipment:
            raise AdminShipmentNotFound()

        data = validate_shipment(shipment, data, schema)

        if (shipment.custom_type == "shipment" or shipment.custom_type ==
                "custom_shipment"):
            if (data.not_working_hours or data.min_time or data.max_time or
                    data.execution_time):
                shipment_time = await crud.get_shipment_time(settings_id=shipment.id)
                if not shipment_time:
                    shipment_time = await crud.create_shipment_time(
                        settings_id=shipment_id
                    )
                await shipment_time.update(
                    not_working_hours=schemas.NotWorkingHours._value2member_map_.get(
                        data.not_working_hours
                    ) if data.not_working_hours else None,
                    min_time=data.min_time if data.min_time else None,
                    max_time=data.max_time if data.max_time else None,
                    order_execution=data.execution_time if data.execution_time else
                    None,
                )

        data_dict = data.dict(
            exclude_unset=True,
            exclude={"not_working_hours", "min_time", "max_time", "execution_time",
                     "map_countries", "translations",
                     "payment_to_shipments"}
        )
        if shipment.custom_type != "custom_shipment_group" and data.map_countries:
            data_dict["_map_countries_list"] = data.map_countries

        await crud.update_shipment_with_translations(
            shipment, data_dict, profile.get_langs_list(), data.translations
        )

        if data.payment_to_shipments:
            for payment_to_shipment in data.payment_to_shipments:
                if payment_to_shipment.payment_id:
                    await crud.update_payment_setting_to_shipment(
                        shipment.id, payment_to_shipment.payment_id,
                        payment_to_shipment.is_enabled
                    )

        return await self.shipment_to_schema(shipment)

    async def update_shipment_stores_data(
            self, data: list[schemas.AdminShipmentStore],
            shipment_id: int
    ) -> schemas.AdminShipmentItemSchema:
        brand = await crud.get_brand_by_group(self.profile_id)
        shipment = await BrandCustomSettings.get(id=shipment_id, brand_id=brand.id)

        for item in data:
            store = await Store.get(item.store_id)
            if store.brand_id != shipment.brand_id:
                raise AdminShipmentUpdateStoreError()

            store_settings = await StoreCustomSettings.get_or_create(
                store_id=item.store_id, custom_settings_id=shipment_id
            )
            value = item.is_enabled
            if shipment.is_enabled == value:
                value = None

            await store_settings.update(is_enabled=value)

        shipment = await BrandCustomSettings.get(id=shipment_id)
        return await self.shipment_to_schema(shipment)

    async def update_shipment_payments_data(
            self, data: schemas.AdminUpdateShipmentPayments,
            shipment_id: int,
    ) -> schemas.AdminShipmentItemSchema:
        brand = await crud.get_brand_by_group(self.profile_id)
        shipment = await BrandCustomSettings.get(id=shipment_id, brand_id=brand.id)

        for payment_setting in data.shipment_payments_settings:
            await crud.update_payment_setting_to_shipment(
                shipment_id, payment_setting.id, is_enabled=payment_setting.is_enabled,
            )

        return await self.shipment_to_schema(shipment)

    async def update_shipment_icon(
            self, shipment_id: int, file: UploadFile
    ) -> schemas.AdminShipmentItemSchema:
        brand = await crud.get_brand_by_group(self.profile_id)
        shipment = await BrandCustomSettings.get(id=shipment_id, brand_id=brand.id)
        if not shipment:
            raise AdminShipmentNotFound()

        media = await media_manager.save_from_upload_file(file)

        await shipment.update(media_id=media.id)
        return await self.shipment_to_schema(shipment)

    async def delete_shipment_icon(
            self, shipment_id: int
    ) -> schemas.AdminShipmentItemSchema:
        brand = await crud.get_brand_by_group(self.profile_id)
        shipment = await BrandCustomSettings.get(id=shipment_id, brand_id=brand.id)
        if not shipment:
            raise AdminShipmentNotFound()

        await shipment.update(media_id=None)

        return await self.shipment_to_schema(shipment)

    async def update_prices(
            self, data: schemas.AdminUpdateShipmentPrices, shipment_id: int
    ) -> schemas.AdminShipmentItemSchema:
        for price in data.prices:
            validate_shipment_price(price)

        brand = await crud.get_brand_by_group(self.profile_id)
        shipment = await BrandCustomSettings.get(id=shipment_id, brand_id=brand.id)
        if not shipment:
            raise AdminShipmentNotFound()

        for price_schema in data.prices:
            if price_schema.id:
                price = await ShipmentPrice.get(id=price_schema.id)
                if not price:
                    raise AdminShipmentPriceNotFound()

                await price.update(**price_schema.dict(exclude={"id"}))
            else:
                if data.zone_id:
                    shipment_price = await crud.create_shipment_price(
                        zone_id=data.zone_id
                    )
                else:
                    shipment_price = await crud.create_shipment_price(shipment_id)
                if shipment_price:
                    await shipment_price.update(**price_schema.dict(exclude={"id"}))

        return await self.shipment_to_schema(shipment)

    async def delete_price(
            self, price_id: int,
            shipment_id: int,
            store_id: int | None = None,
            zone_id: int | None = None
    ) -> schemas.AdminShipmentItemSchema:
        brand = await crud.get_brand_by_group(self.profile_id)
        shipment = await BrandCustomSettings.get(id=shipment_id, brand_id=brand.id)
        if not shipment:
            raise AdminShipmentNotFound()

        args = {
            "brand_id": shipment.brand_id,
            "store_id": store_id,
            "price_id": price_id,
            "zone_id": zone_id,
        }
        if not zone_id and not store_id:
            args["settings_id"] = shipment_id
        await crud.delete_shipment_price(**args)

        return await self.shipment_to_schema(shipment)

    async def delete_shipment(self, shipment_id: int) -> schemas.OkResponse:
        brand = await crud.get_brand_by_group(self.profile_id)
        shipment = await BrandCustomSettings.get(id=shipment_id, brand_id=brand.id)
        if not shipment:
            raise AdminShipmentNotFound()

        if shipment.custom_type == "shipment":
            raise AdminShipmentDeleteBaseError()

        res = await crud.delete_brand_custom_setting(shipment_id)
        if not res:
            raise AdminShipmentDeleteError()

        if shipment.custom_type == "custom_shipment_group":
            group_shipments = await BrandCustomSettings.get_list(
                custom_settings_group_id=shipment_id
            )
            if group_shipments:
                for group_shipment in group_shipments:
                    await crud.delete_brand_custom_setting(group_shipment.id)

        return schemas.OkResponse()

    @classmethod
    async def shipment_zone_to_schema(
            cls, shipment_zone: ShipmentZone,
            brand_id: int
    ) -> schemas.ShipmentZone:
        return schemas.ShipmentZone(
            id=shipment_zone.id,
            name=shipment_zone.name,
            is_distance=shipment_zone.is_distance,
            is_polygon=shipment_zone.is_polygon,
            is_swap_coordinates=shipment_zone.is_swap_coordinates,
            distance=shipment_zone.distance,
            polygon=shipment_zone.polygon,
            prices=[
                schemas.ShipmentPrice.from_orm(price)
                for price in await crud.get_shipment_prices(
                    brand_id=brand_id, zone_id=shipment_zone.id,
                )
            ]
        )

    async def get_shipment_zones(
            self, shipment_id: int, store_id: int
    ) -> schemas.AdminShipmentZones:
        brand = await crud.get_brand_by_group(self.profile_id)
        store_settings = await StoreCustomSettings.get_or_create(
            store_id=store_id, custom_settings_id=shipment_id
        )
        shipment_zones = await crud.get_shipment_zones(
            brand_id=brand.id, store_id=store_id,
            shipment_store_settings_id=store_settings.id,
        )

        return schemas.AdminShipmentZones(
            zones=[
                await self.shipment_zone_to_schema(zone, brand.id)
                for zone in shipment_zones
            ],
            is_enabled=store_settings.is_shipment_zone,
        )

    async def update_shipment_zone(
            self, data: schemas.AdminCreateOrUpdateShipmentZone,
            shipment_id: int, zone_id: int
    ) -> schemas.ShipmentZone:
        brand = await crud.get_brand_by_group(self.profile_id)
        shipment_zone = await ShipmentZone.get(id=zone_id)
        if not shipment_zone:
            raise AdminShipmentZoneNotFoundError()

        if shipment_zone.is_polygon and not data.polygon:
            raise AdminShipmentZoneNoPolygonError()

        if shipment_zone.is_polygon and data.distance:
            raise AdminShipmentZoneRadiusForPolygonError()

        if shipment_zone.is_distance and not data.distance:
            raise AdminShipmentZoneNoRadiusError()

        if shipment_zone.is_distance and data.polygon:
            raise AdminShipmentZonePolygonForRadiusError()

        if data.polygon and not data.name:
            raise AdminShipmentZonePolygonNameError()

        prices = await crud.get_shipment_prices(
            brand_id=brand.id, zone_id=shipment_zone.id,
        )
        if prices:
            store_settings = await StoreCustomSettings.get(shipment_zone.shipment_id)
            if store_settings:
                prices_ids = [price.id for price in data.prices if price.id]
                for price in prices:
                    if price.id not in prices_ids:
                        await self.delete_price(
                            price.id,
                            shipment_id,
                            store_id=store_settings.store_id,
                            zone_id=zone_id
                        )

        await shipment_zone.update(**data.dict(exclude={"prices"}))
        await self.update_prices(
            schemas.AdminUpdateShipmentPrices(prices=data.prices, zone_id=zone_id),
            shipment_id
        )

        return await self.shipment_zone_to_schema(shipment_zone, brand.id)

    async def create_shipment_zone(
            self, data: schemas.AdminCreateOrUpdateShipmentZone,
            shipment_id: int, store_id: int
    ) -> schemas.ShipmentZone:
        if data.distance and data.polygon:
            raise AdminShipmentZoneRadiusAndPolygonError()

        if data.polygon and not data.name:
            raise AdminShipmentZonePolygonNameError()

        brand = await crud.get_brand_by_group(self.profile_id)
        store_settings = await StoreCustomSettings.get_or_create(
            store_id=store_id, custom_settings_id=shipment_id
        )

        if not store_settings:
            raise AdminShipmentZoneCreateStoreSettingError()

        if not data.polygon and not data.distance:
            raise AdminShipmentsZonesNoPolygonAndDistanceError()

        zone = await ShipmentZone.create(shipment_id=store_settings.id, name=data.name)

        if not zone:
            raise AdminShipmentZoneCreateError()

        await zone.update(
            **data.dict(exclude={"prices"}), is_polygon=True if data.polygon else False,
            is_distance=True if data.distance else False
        )
        await crud.create_shipment_time(zone_id=zone.id)
        await self.update_prices(
            schemas.AdminUpdateShipmentPrices(prices=data.prices, zone_id=zone.id),
            shipment_id
        )

        return await self.shipment_zone_to_schema(zone, brand.id)

    async def delete_shipment_zone(
            self, zone_id: int, store_id: int
    ) -> schemas.OkResponse:
        brand = await crud.get_brand_by_group(self.profile_id)
        zone = await ShipmentZone.get(id=zone_id)
        if not zone:
            raise AdminShipmentZoneNotFoundError()

        await crud.delete_shipment_time(zone_id=zone_id)
        await crud.delete_shipment_prices(brand.id, store_id, zone_id=zone_id)

        result = await zone.delete()

        if not result:
            raise AdminShipmentZoneDeleteError()

        return schemas.OkResponse()

    @classmethod
    async def set_shipment_zones_enabled(
            cls, store_id: int, enabled: bool
    ) -> schemas.OkResponse:
        zones = await StoreCustomSettings.get_list(store_id=store_id)
        for zone in zones:
            await zone.update(is_shipment_zone=enabled)

        return schemas.OkResponse()
