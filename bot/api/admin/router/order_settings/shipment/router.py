from fastapi import APIRouter, Depends, Security, UploadFile

import schemas
from .service import ShipmentService

router = APIRouter(
    prefix="/shipments/{shipment_id}"
)


@router.get("/")
async def get_shipment(
        shipment_id: int,
        service: ShipmentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.AdminShipmentItemSchema:
    return await service.get_shipment(shipment_id)


@router.patch("/base_shipment")
async def update_shipment_main_data_base(
        data: schemas.AdminUpdateBaseShipmentSchema,
        shipment_id: int,
        service: ShipmentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.AdminShipmentItemSchema:
    return await service.update_shipment_main_data(
        data, shipment_id, schemas.AdminUpdateBaseShipmentSchema
    )


@router.patch("/custom_shipment")
async def update_shipment_main_data_custom(
        data: schemas.AdminUpdateCustomShipmentSchema,
        shipment_id: int,
        service: ShipmentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.AdminShipmentItemSchema:
    return await service.update_shipment_main_data(
        data, shipment_id, schemas.AdminUpdateCustomShipmentSchema
    )


@router.patch("/custom_shipment_group")
async def update_shipment_main_data_custom_group(
        data: schemas.AdminUpdateCustomShipmentGroupSchema,
        shipment_id: int,
        service: ShipmentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.AdminShipmentItemSchema:
    return await service.update_shipment_main_data(
        data, shipment_id, schemas.AdminUpdateCustomShipmentGroupSchema
    )


@router.patch("/stores")
async def update_shipment_stores(
        data: list[schemas.AdminShipmentStore],
        shipment_id: int,
        service: ShipmentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.AdminShipmentItemSchema:
    return await service.update_shipment_stores_data(data, shipment_id)


@router.patch("/payments")
async def update_shipment_payments(
        data: schemas.AdminUpdateShipmentPayments,
        shipment_id: int,
        service: ShipmentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.AdminShipmentItemSchema:
    return await service.update_shipment_payments_data(data, shipment_id)


@router.post("/prices")
async def update_shipment_prices(
        data: schemas.AdminUpdateShipmentPrices,
        shipment_id: int,
        service: ShipmentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.AdminShipmentItemSchema:
    return await service.update_prices(data, shipment_id)


@router.delete("/prices/{price_id}")
async def delete_shipment_price(
        price_id: int,
        shipment_id: int,
        store_id: int | None = None,
        zone_id: int | None = None,
        service: ShipmentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.AdminShipmentItemSchema:
    return await service.delete_price(price_id, shipment_id, store_id, zone_id)


@router.delete("/")
async def delete_shipment(
        shipment_id: int,
        service: ShipmentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.OkResponse:
    return await service.delete_shipment(shipment_id)


@router.post("/update_image")
async def update_shipment_icon(
        file: UploadFile,
        shipment_id: int,
        service: ShipmentService = Security(scopes=["product:edit", "me:write"]),
) -> schemas.AdminShipmentItemSchema:
    return await service.update_shipment_icon(shipment_id, file)


@router.delete("/delete_image")
async def delete_shipment_icon(
        shipment_id: int,
        service: ShipmentService = Security(scopes=["product:edit", "me:write"]),
) -> schemas.AdminShipmentItemSchema:
    return await service.delete_shipment_icon(shipment_id)


@router.get("/zones")
async def get_shipment_zones(
        shipment_id: int,
        store_id: int,
        service: ShipmentService = Depends(),
) -> schemas.AdminShipmentZones:
    return await service.get_shipment_zones(shipment_id, store_id)


@router.post("/zones/{store_id}")
async def create_shipment_zone(
        shipment_id: int,
        store_id: int,
        data: schemas.AdminCreateOrUpdateShipmentZone,
        service: ShipmentService = Depends(),
) -> schemas.ShipmentZone:
    return await service.create_shipment_zone(data, shipment_id, store_id)


@router.patch("/zones/{zone_id}")
async def update_shipment_zone(
        shipment_id: int,
        zone_id: int,
        data: schemas.AdminCreateOrUpdateShipmentZone,
        service: ShipmentService = Depends(),
) -> schemas.ShipmentZone:
    return await service.update_shipment_zone(data, shipment_id, zone_id)


@router.post("/zones/{store_id}/{zone_id}")
async def delete_shipment_zone(
        shipment_id: int,
        store_id: int,
        zone_id: int,
        service: ShipmentService = Depends(),
) -> schemas.OkResponse:
    return await service.delete_shipment_zone(zone_id, store_id)


@router.patch("/zones/{store_id}/set_enabled")
async def set_enabled_shipment_zones(
        shipment_id: int,
        store_id: int,
        enabled: bool = True,
        service: ShipmentService = Depends(),
) -> schemas.OkResponse:
    return await service.set_shipment_zones_enabled(store_id, enabled)
