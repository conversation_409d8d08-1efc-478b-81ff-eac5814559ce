from fastapi import APIRouter, Depends, Query

import schemas
from .service import ShipmentsService

router = APIRouter(
    prefix="/shipments"
)


@router.get("/")
async def get_shipments(
        search_text: str | None = Query(None),
        shipment_group_id: int | None = Query(None),
        no_groups: bool = Query(False),
        service: ShipmentsService = Depends()
) -> list[schemas.AdminShipmentsListSchema]:
    return await service.get_shipments_list(
        search_text, shipment_group_id, no_groups=no_groups
    )


@router.get("/total_count")
async def get_shipments_total_count(
        search_text: str | None = Query(None),
        service: ShipmentsService = Depends()
) -> int:
    return await service.get_shipments_total_count(search_text)


@router.post("/create")
async def create_shipment(
        data: schemas.AdminCreateShipmentData,
        service: ShipmentsService = Depends()
) -> schemas.AdminShipmentsListSchema:
    return await service.create_shipment(data)
