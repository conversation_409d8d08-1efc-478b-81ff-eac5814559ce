import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Brand, BrandCustomSettings, MediaObject, User
from schemas.store.types import CustomType, ShipmentType
from ..exceptions import AdminCreateShipmentError, AdminShipmentInvalidType
from ..functions import get_is_disabled_in_any_store, get_is_enabled_in_any_store


class ShipmentsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_shipments_list(
            self, search_text: str | None = None,
            shipment_group_id: int | None = None,
            no_groups: bool = False,
    ) -> list[schemas.AdminShipmentsListSchema]:
        brand = await Brand.get(group_id=self.profile_id)
        res = []

        if brand:
            if shipment_group_id:
                brand_settings = await crud.get_custom_shipments(
                    brand.id, with_translations=False,
                    custom_shipment_group_id=shipment_group_id
                )
                for setting in brand_settings:
                    res.append(await self.shipment_to_list_schema(setting))
            else:
                for base_type in ShipmentType:
                    brand_settings = await (
                        BrandCustomSettings.get_or_create_base_shipment(
                            brand.id, base_type.value
                        ))
                    res.append(await self.shipment_to_list_schema(brand_settings))

                rest = await crud.get_all_custom_shipments_and_groups(
                    brand.id, search_text, no_groups=no_groups,
                )

                if no_groups:
                    groups = await BrandCustomSettings.get_list(
                        custom_type=CustomType.CUSTOM_SHIPMENT_GROUP.value,
                        brand_id=brand.id,
                    )
                    for group in groups:
                        group_shipments = await crud.get_custom_shipments(
                            brand.id, with_translations=False,
                            custom_shipment_group_id=group.id
                        )
                        for group_shipment in group_shipments:
                            shipment_group_schema = await self.shipment_to_list_schema(
                                group_shipment
                            )
                            shipment_group_schema.name = (
                                f"{group.name}:{shipment_group_schema.name}"
                            )
                            res.append(shipment_group_schema)

                for settings in rest:
                    res.append(await self.shipment_to_list_schema(settings))

        return res

    async def get_shipments_total_count(self, search_text: str | None = None) -> int:
        count = len(ShipmentType)
        rest_count = await crud.get_all_custom_shipments_and_groups(
            self.profile_id, search_text, operation="count"
        ) or 0
        return count + rest_count

    async def shipment_to_list_schema(
            self, shipment: BrandCustomSettings
    ) -> schemas.AdminShipmentsListSchema:
        is_disabled_in_any_store = await get_is_disabled_in_any_store(shipment.id)
        is_enabled_in_any_store = await get_is_enabled_in_any_store(shipment.id)
        media = await MediaObject.get(shipment.media_id)

        return schemas.AdminShipmentsListSchema(
            id=shipment.id,
            name=await shipment.get_name(self.lang),
            is_enabled=shipment.is_enabled,
            is_disabled_in_any_store=is_disabled_in_any_store,
            is_enabled_in_any_store=is_enabled_in_any_store,
            type=shipment.custom_type,
            base_type=shipment.base_type,
            icon_url=media.url if media else None,
        )

    async def create_shipment(
            self, data: schemas.AdminCreateShipmentData
    ) -> schemas.AdminShipmentsListSchema:
        brand = await Brand.get(group_id=self.profile_id)
        shipment = await BrandCustomSettings.create(
            brand.id,
            custom_type=data.shipment_type,
            base_type=data.base_type,
            name=data.name,
        )
        if not shipment:
            raise AdminCreateShipmentError()

        if data.shipment_type not in ["custom_shipment", "custom_shipment_group"]:
            raise AdminShipmentInvalidType()

        data_dict = {}
        if data.shipment_type == "custom_shipment":
            data_dict = {
                "name": data.name,
                "base_type": data.base_type or "pickup",
                "description": data.description,
                "is_paid_separately": data.is_paid_separately,
                "enabled_tips": data.enabled_tips,
                "is_enabled": data.is_enabled,
            }
            if data.shipment_group_id:
                data_dict["custom_settings_group_id"] = data.shipment_group_id

        elif data.shipment_type == "custom_shipment_group":
            data_dict = {
                "description": data.description, "is_enabled": data.is_enabled,
            }

        await shipment.update(**data_dict)

        if data.payment_to_shipments:
            for payment_to_shipment in data.payment_to_shipments:
                if payment_to_shipment.payment_id:
                    await crud.update_payment_setting_to_shipment(
                        shipment.id, payment_to_shipment.payment_id,
                        payment_to_shipment.is_enabled
                    )
        else:
            payments = await crud.get_payment_methods(
                brand_id=brand.id,
                with_translations=False,
            )
            for payment in payments:
                await crud.update_payment_setting_to_shipment(
                    shipment.id, payment.id, True
                )

        return await self.shipment_to_list_schema(shipment)
