import logging
import re

from psutils.forms.validators.base import Validator

import schemas
from config import INCUST_SERVER_API
from core.loyalty.incust_api import incust
from db.models import LoyaltySettings, PaymentSettings
from utils.text import f


class IncustServerAPIURLValidator(Validator):
    pattern = re.compile(r"\b(?:[a-zA-Z0-9-]+\.)+[a-zA-Z0-9-]+\b")

    async def validate(self, lang: str, value: str) -> dict | str:
        domains = re.findall(self.pattern, value)
        if not domains:
            return await f("incust no server api url found in message error", lang)

        incust_server_api_urls = []
        invalid_urls = []

        for domain in domains:
            server_api_url = f"https://{domain}"
            if (
                    server_api_url in incust_server_api_urls or
                    server_api_url in invalid_urls
            ):
                continue

            try:
                # Створюємо тимчасові налаштування для валідації
                temp_settings = LoyaltySettings(
                    terminal_api_key="",
                    server_url=f"https://{domain}",
                )
                
                async with incust.client.SystemApi(temp_settings, lang=lang) as api:
                    platform_settings = await api.application_settings()
                    
                if hasattr(platform_settings, 'user_default_primary_identifier_type') and platform_settings.user_default_primary_identifier_type in ("phone", "email", "external-id"):
                    incust_server_api_urls.append(server_api_url)
                else:
                    invalid_urls.append(server_api_url)
            except Exception as e:
                debugger = logging.getLogger("debugger")
                debugger.debug(f"an error occurred while validating incust server api url({server_api_url}): {e}")
                invalid_urls.append(server_api_url)

        if not incust_server_api_urls:
            return await f(
                "incust server api url invalid error", lang,
                incust_server_api_urls=", ".join(invalid_urls)
            )

        if len(incust_server_api_urls) > 1:
            return await f(
                "incust multiple server api urls found error", lang,
                incust_server_api_urls=", ".join(incust_server_api_urls)
            )

        return {
            "server_api_url": list(incust_server_api_urls)[0]
        }


class IncustTerminalAPIKeyValidator(Validator):
    async def validate(
        self, lang: str, value: str,
        payment_settings_id: int | None = None,
        api_url: str | None = None,
    ) -> dict | str:
        terminal_api_key = value

        incust_pay_configuration: schemas.IncustPayConfigurationPaymentObject | None = None
        if payment_settings_id:
            payment_settings = await PaymentSettings.get(payment_settings_id)
            incust_pay_configuration = schemas.IncustPayConfigurationPaymentObject(**payment_settings.json_data)

            server_api_url = incust_pay_configuration.server_api_url
        else:
            server_api_url = api_url

        if not server_api_url:
            server_api_url = INCUST_SERVER_API

        temp_settings = LoyaltySettings(
            terminal_api_key=terminal_api_key,
            server_url=server_api_url,
        )

        try:
            async with incust.term.SettingsApi(temp_settings, lang=lang) as api:
                terminal_settings = await api.settings()
        except Exception as e:
            if hasattr(e, 'response') and hasattr(e.response, 'status_code') and e.response.status_code == 401:
                err_message = await f("incust terminal api key invalid error", lang)
            else:
                err_message = await f("incust terminal api key validation error", lang)

            return err_message

        return {
            "terminal_api_key": terminal_api_key,
            "terminal_server_api_url": server_api_url,
            "terminal_title": terminal_settings.title,
            "term_settings": terminal_settings.dict(),
            "incust_pay_configuration": incust_pay_configuration.dict() if incust_pay_configuration else None,
        }
