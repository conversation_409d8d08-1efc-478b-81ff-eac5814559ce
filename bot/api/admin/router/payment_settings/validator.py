import logging

from fastapi import HTTPException

import schemas
from core.loyalty.incust_api import incust
from core.payment.payment_processor.providers.comsa import test_comsa_access
from core.payment.payment_processor.providers.kpay.funcs import get_kpay_token
from core.payment.payment_processor.providers.orange.client import get_orange_token
from core.payment.payment_processor.providers.pl24 import test_access
from core.payment.payment_processor.providers.stripe import \
    create_or_update_stripe_webhook
from core.payment.payment_processor.providers.tpay.client import get_tpay_token
from core.payment.payment_processor.providers.unipos import (
    add_unipos_terminal, get_unipos_token,
)
from db import crud
from db.models import BusinessPaymentSetting, EWallet, LoyaltySettings
from .exceptions import (
    AdminPaymentEWalletDisabledError, AdminPaymentUniposForStoreError,
    AdminPaymentsCustomNameError,
    AdminPaymentsDisabledMethodTypeForCreateError,
    AdminPaymentsMerchantDataDataError,
    AdminPaymentsOnlineProviderDataEmptyError,
    AdminPaymentsProviderValidateDataError,
)
from .incust_validators import (
    IncustServerAPIURLValidator,
    IncustTerminalAPIKeyValidator,
)

logger = logging.getLogger("debugger.payment_settings.validator")

liqpay_tax_codes = {
    "А": "без ПДВ 0%",
    "Б": "ПДВ 20%",
    "В": "ПДВ 7%",
    "Г": "акциз 5%"
}

liqpay_available_tax_codes = liqpay_tax_codes.keys()

disabled_for_create_methods = ["cash"]


def strip_dict_values(d):
    return {k: v.lstrip() if isinstance(v, str) else v for k, v in d.items()}


class PaymentSettingsValidator:
    def __init__(
            self, lang: str,
            payment_method: str | schemas.PaymentSettingsMethodLiteral,
            brand_id: int,
            name: str | None = None,
            json_data_dict: dict | None = None,
            skip_json_data: bool = False,
            is_update: bool = False,
            is_store: bool = False,
            stores_json_data: list[schemas.UpdateAdminPaymentStore] | None = None,
            is_fiscal: bool = False,
    ):
        self.lang: str = lang
        self.payment_method: str | schemas.PaymentSettingsMethodLiteral = payment_method
        self.name: str | None = name
        self.without_validation_methods = ["custom", "friend", "incust_pay"]
        self.json_data_dict: dict | None = json_data_dict
        self.brand_id: int = brand_id
        self.skip_json_data: bool = skip_json_data
        self.is_update: bool = is_update
        self.is_store: bool = is_store
        self.stores_json_data: list[
                                   schemas.UpdateAdminPaymentStore] | None = (
            stores_json_data)
        self.is_fiscal: bool = is_fiscal

    async def validate(self) -> dict | None:
        logger.debug(
            f"self.json_data_dict['merchant_data'] = "
            f"{self.json_data_dict.get('merchant_data') if self.json_data_dict else ''}"
        )
        self.__validate_payment_settings_data()

        processed_json_data = None
        if not self.skip_json_data and self.json_data_dict:
            if self.payment_method not in self.without_validation_methods:
                await self.__validate_method_type_data()
                processed_json_data = await self.__process_payment_provider_data()

            if self.payment_method == "incust_pay":
                processed_json_data = await self.__validate_incust_pay_data(
                    processed_json_data or self.json_data_dict
                )
                processed_json_data["name"] = self.name
                processed_json_data["rules_type"] = "by-all-rules"

            if self.payment_method == "ewallet":
                processed_json_data = await self.__validate_ewallet_data(
                    processed_json_data or self.json_data_dict
                )
                processed_json_data["name"] = self.name
                processed_json_data["rules_type"] = "by-all-rules"

        result = processed_json_data or self.json_data_dict
        return strip_dict_values(result) if result else result

    def __validate_payment_settings_data(self):
        if self.is_fiscal and self.payment_method == "liqpay":
            if self.is_store and self.json_data_dict:
                raise AdminPaymentsOnlineProviderDataEmptyError()

        if (not self.is_store and self.payment_method not in
                self.without_validation_methods):
            if self.payment_method not in ("cash", "friend"):
                if not self.json_data_dict:
                    if not self.stores_json_data or len(self.stores_json_data) == 0:
                        raise AdminPaymentsOnlineProviderDataEmptyError()
                    any_store_data = None
                    for store_data in self.stores_json_data:
                        if store_data.json_data:
                            any_store_data = store_data.json_data
                            break
                    if not any_store_data:
                        raise AdminPaymentsOnlineProviderDataEmptyError()

        if self.payment_method == "unipos" and self.json_data_dict and self.is_store:
            raise AdminPaymentUniposForStoreError()
        if self.payment_method == "custom" and not self.name and not self.is_store:
            raise AdminPaymentsCustomNameError()
        if self.payment_method in disabled_for_create_methods and not self.is_update:
            raise AdminPaymentsDisabledMethodTypeForCreateError()

    async def __validate_method_type_data(self):
        match self.payment_method:
            case "liqpay":
                if self.json_data_dict.get("tax_list") and not self.json_data_dict.get(
                        "tax_list"
                ) in liqpay_available_tax_codes:
                    raise AdminPaymentsProviderValidateDataError("liqpay")
            case "comsa":
                if not await test_comsa_access(
                        self.json_data_dict.get("vendor_id"),
                        self.json_data_dict.get("secret_key"),
                        self.json_data_dict.get("is_sandbox")
                ):
                    raise AdminPaymentsProviderValidateDataError("comsa")
            case "orange":
                if not await get_orange_token(
                        client_id=self.json_data_dict.get("client_id"),
                        client_secret=self.json_data_dict.get("client_secret")
                ):
                    raise AdminPaymentsProviderValidateDataError("orange")
            case "kpay":
                logger.debug(f"self.json_data_dict = {self.json_data_dict}")
                if not await get_kpay_token(
                        self.json_data_dict
                ):
                    raise AdminPaymentsProviderValidateDataError("kpay")
            case "pl24":
                if not await test_access(
                        self.json_data_dict.get("pos_id"),
                        self.json_data_dict.get("secret"),
                        self.json_data_dict.get("is_sandbox")
                ):
                    raise AdminPaymentsProviderValidateDataError("pl24")
            case "tpay":
                if not await get_tpay_token(
                        self.json_data_dict.get("client_id", ""),
                        self.json_data_dict.get("client_secret", "")
                ):
                    raise AdminPaymentsProviderValidateDataError("tpay")

    async def __process_payment_provider_data(self) -> dict:
        match self.payment_method:
            case "comsa" | "pl24" | "directpay" | "momo" | "tj" | "airtel":
                self.json_data_dict["is_sandbox"] = 1 if self.json_data_dict.get(
                    "is_sandbox"
                ) else 0
            case "freedompay":
                self.json_data_dict["is_sandbox"] = True
            case "stripe":
                stripe_endpoint_secret = await crud.get_stripe_webhook_secret(
                    self.json_data_dict.get('secret_key')
                )
                if not stripe_endpoint_secret:
                    stripe_endpoint_secret = await create_or_update_stripe_webhook(
                        self.json_data_dict.get('secret_key')
                    )
                if not stripe_endpoint_secret:
                    raise AdminPaymentsProviderValidateDataError("stripe")
                self.json_data_dict["stripe_endpoint_secret"] = stripe_endpoint_secret
            case "unipos":
                store_id = None
                terminal_data = {
                    "account": self.json_data_dict.get("account"),
                    "filial_code": self.json_data_dict.get("filial_code"),
                    "inn": self.json_data_dict.get("inn"),
                    "name": await crud.get_store_name(store_id)
                    if store_id else await crud.get_brand_name(self.brand_id)
                }

                connect_data = {
                    "login": self.json_data_dict.get("login"),
                    "password": self.json_data_dict.get("password"),
                }

                if unipos_token := await get_unipos_token(
                        self.brand_id, store_id, connect_data, self.lang
                ):
                    if terminal_id := await add_unipos_terminal(
                            self.brand_id, store_id, unipos_token, terminal_data,
                            self.lang
                    ):
                        data = {
                            "token": unipos_token,
                            "terminal_id": terminal_id,
                            "account": terminal_data["account"],
                            "filial_code": terminal_data["filial_code"],
                            "inn": terminal_data["inn"],
                            "name": terminal_data["name"]
                        }
                        return data
            case _:
                return self.json_data_dict

        return self.json_data_dict

    async def __validate_incust_pay_data(self, processed_json_data: dict) -> dict:
        logger.debug(
            f"processed_json_data['merchant_data'] = "
            f"{processed_json_data.get('merchant_data') if processed_json_data else ''}"
        )
        server_validation = IncustServerAPIURLValidator()
        server_api_url = await server_validation.validate(
            self.lang, processed_json_data.get("server_api_url")
        )
        if isinstance(server_api_url, dict):
            processed_json_data["server_api_url"] = server_api_url["server_api_url"]
        else:
            raise HTTPException(status_code=400, detail=server_api_url)

        terminal_validation = IncustTerminalAPIKeyValidator()
        processed_data = await terminal_validation.validate(
            self.lang, processed_json_data.get("terminal_api_key"),
            api_url=processed_json_data.get("server_api_url"),
        )
        if isinstance(processed_data, dict):
            processed_json_data["terminal_api_key"] = processed_data["terminal_api_key"]
            processed_json_data["terminal_server_api_url"] = processed_data[
                "terminal_server_api_url"]
            processed_json_data["terminal_title"] = processed_data["terminal_title"]
            processed_json_data["term_settings"] = processed_data["term_settings"]
            processed_json_data["incust_pay_configuration"] = processed_data[
                "incust_pay_configuration"]
        else:
            raise HTTPException(status_code=400, detail=processed_data)

        temp_settings = LoyaltySettings(
            terminal_api_key=processed_json_data["terminal_api_key"],
            server_url=processed_json_data.get("server_api_url"),
        )

        await self.check_merchant_data(temp_settings, processed_json_data)

        return processed_json_data

    async def __validate_ewallet_data(self, processed_json_data: dict) -> dict:
        logger.debug(
            f"processed_json_data['merchant_data'] = "
            f"{processed_json_data.get('merchant_data') if processed_json_data else ''}"
        )

        ewallet = await EWallet.get(
            processed_json_data.get("ewallet_id"), is_deleted=False
        )

        if not ewallet.is_enabled:
            raise AdminPaymentEWalletDisabledError(ewallet.name)

        profile = await crud.get_group_by_brand_id(self.brand_id)

        if profile.country_code not in ewallet.countries:
            raise HTTPException(
                status_code=400,
                detail=f"Payment provider is not supported in the country with code "
                       f"'{profile.country_code}'. Please choose a different payment "
                       f"method."
            )

        # Створюємо тимчасові налаштування для перевірки merchant data
        temp_settings = LoyaltySettings(
            terminal_api_key=ewallet.terminal_api_key,
            server_url=ewallet.server_api_url,
            loyalty_id="",
            is_enabled=True
        )

        await self.check_merchant_data(
            temp_settings, processed_json_data, ewallet.incust_account_id
        )

        return processed_json_data

    async def check_merchant_data(
            self, temp_settings: LoyaltySettings, processed_json_data: dict,
            incust_account_id: str | None = None
    ):
        specials = None
        try:
            async with incust.client.WalletApi(temp_settings, lang=self.lang) as api:
                wallet_response = await api.wallet()
                specials = wallet_response.special_accounts if hasattr(wallet_response, 'special_accounts') else []
        except Exception:
            # specials not found...
            return []

        if specials:
            business_settings = await BusinessPaymentSetting.get_list(
                is_deleted=False,
                is_enabled=True
            )

            business_lookup = {bs.incust_account_id: True for bs in business_settings}

            merchant_data = processed_json_data.get("merchant_data")

            if not merchant_data:
                return []
            
            # Фільтруємо merchant_data, залишаючи тільки об'єкти з непустими значеннями в data
            filtered_merchant_data = [
                md for md in merchant_data 
                if md.get("data") and any(
                    value and value.strip() if isinstance(value, str) else value
                    for value in md.get("data").values()
                )
            ]
            
            if len(filtered_merchant_data) > 1:
                raise AdminPaymentsMerchantDataDataError()
                
            processed_json_data["merchant_data"] = filtered_merchant_data

            def validate_merchant_data(special_id: str):
                if not filtered_merchant_data:
                    return

                # if special_id not in [md.get("incust_account_id") for md in
                #                       filtered_merchant_data or []]:
                #     logger.error(
                #         f"{special_id=} not found in processed_json_data.get("
                #         f"'merchant_data')"
                #         f"\n{processed_json_data.get('merchant_data')=}"
                #     )
                #     raise AdminPaymentsIncustPayMerchantDataDataError()

            for special in specials:
                if special.id in business_lookup:
                    if not incust_account_id or special.id == incust_account_id:
                        validate_merchant_data(special.id)
        return None
