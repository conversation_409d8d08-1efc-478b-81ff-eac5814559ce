import logging

from fastapi import HTT<PERSON>Exception, UploadFile
from psutils.exceptions.exception_handlers import Unknown<PERSON>rror<PERSON><PERSON><PERSON>
from starlette import status

import schemas
from api.admin.helpers import get_translations_schemas_dict
from config import NOT_FULL_UPD_PAYMENT_METHODS
from core.auth.services.scopes_checker import ScopesCheckerService
from core.external_data.liqpay import export_liqpay_products, import_liqpay_products
from core.media_manager import media_manager
from core.payment.exceptions import BaseLiqpayRroError, LiqpayRroUnknownError
from core.payment.funcs import get_payment_default_name
from core.payment.payment_processor.providers.wave.funcs import \
    create_aggregated_merchant_id
from core.store.functions.payments import get_icon_for_payment
from db import crud
from db.models import (
    Brand, EWallet, Group, InvoiceTemplate, MediaObject, ObjectPaymentSettings,
    PaymentSettings, Store,
    User,
)
from schemas.payment_settings.schemas_add import ObjectPaymentSettingsTarget
from utils.exceptions import ErrorWithHTTPStatus
from ..exceptions import (
    AdminPaymentDeleteError, AdminPaymentNotFoundError,
    AdminPaymentStoreNotInBrandError, AdminPaymentUpdateError,
    AdminPaymentValidationError, AdminPaymentsDeleteBaseMethodError,
)
from ..validator import PaymentSettingsValidator
from ...core.funcs import payment_provider_data_to_schema

handle_liqpay_error = UnknownErrorHandler(LiqpayRroUnknownError, BaseLiqpayRroError)


class PaymentService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_payment(self, payment_id: int) -> schemas.AdminPaymentSchema:
        brand = await crud.get_brand_by_group(self.profile_id)
        if not brand:
            logging.error(
                f"Payment {payment_id} not found for profile {self.profile_id}"
            )
            raise AdminPaymentNotFoundError()
        payment = await PaymentSettings.get(
            id=payment_id,
            brand_id=brand.id,
            is_deleted=False,
        )
        if not payment:
            raise AdminPaymentNotFoundError()

        return await self.payment_settings_to_schema(payment)

    async def payment_settings_to_schema(
            self, payment_settings: PaymentSettings
    ) -> schemas.AdminPaymentSchema:

        ewallet_id = payment_settings.json_data.get(
            "ewallet_id", None
        ) if payment_settings.json_data else None

        media = await MediaObject.get(payment_settings.media_id)
        if not media and ewallet_id:
            ewallet = await EWallet.get(ewallet_id)
            if ewallet:
                media = await MediaObject.get(ewallet.media_id)

        profile = await Group.get(self.profile_id)

        default_name = await get_payment_default_name(
            payment_settings.payment_method, self.lang, payment_settings.is_online,
            ewallet_id=ewallet_id,
        )

        return schemas.AdminPaymentSchema(
            id=payment_settings.id,
            default_name=default_name,
            is_enabled=payment_settings.is_enabled,
            icon_url=media.url if media else get_icon_for_payment(
                payment_settings.payment_method
            ),
            is_online=payment_settings.is_online,
            payment_method=payment_settings.payment_method,
            json_data=payment_provider_data_to_schema(
                payment_settings.payment_method, payment_settings.json_data
            ),
            name=payment_settings.name,
            description=payment_settings.description,
            with_comment=payment_settings.with_comment,
            post_payment_info=payment_settings.post_payment_info,
            label_comment=payment_settings.label_comment,
            translations=await get_translations_schemas_dict(
                payment_settings, profile, schemas.AdminPaymentTranslationSchema
            ),
        )

    async def update_payment_main_data(
            self,
            data: schemas.AdminUpdatePaymentMainDataSchema,
            payment_id: int,
    ) -> schemas.AdminPaymentSchema:
        profile = await Group.get(self.profile_id)
        brand = await crud.get_brand_by_group(self.profile_id)

        try:
            excluded = data.dict(exclude_unset=True)
            skip_json_data = False
            if ("json_data" not in excluded or data.payment_method in
                    NOT_FULL_UPD_PAYMENT_METHODS):
                skip_json_data = True

            validator = PaymentSettingsValidator(
                self.lang, data.payment_method, brand.id, data.name,
                data.json_data.data.dict() if data.json_data else None,
                skip_json_data, is_update=True,
            )
            processed_json_data = await validator.validate()
        except Exception as ex:
            if isinstance(ex, ErrorWithHTTPStatus):
                raise ex
            if isinstance(ex, HTTPException):
                raise ex
            else:
                logging.error("An unexpected error occurred: %s", ex, exc_info=True)
                raise AdminPaymentValidationError() from ex

        payment_settings = await crud.new_update_payment_settings(
            payment_settings_id=payment_id,
            is_enabled=data.is_enabled,
            profile_langs=profile.get_langs_list(),
            name=data.name,
            description=data.description,
            with_comment=data.with_comment,
            data=processed_json_data,
            translations=data.translations,
            label_comment=data.label_comment,
            post_payment_info=data.post_payment_info,
        )

        if not payment_settings:
            raise AdminPaymentUpdateError()

        return await self.payment_settings_to_schema(payment_settings)

    async def update_payment_objects_data(
            self, data: list[
                schemas.UpdateAdminPaymentStore |
                schemas.UpdateAdminPaymentInvoiceTemplate],
            payment_settings_id: int,
    ) -> list[schemas.AdminPaymentStore | schemas.AdminPaymentInvoiceTemplate]:
        brand = await crud.get_brand_by_group(self.profile_id)
        payment_settings = await PaymentSettings.get(
            id=payment_settings_id, brand_id=brand.id
        )
        if not payment_settings:
            raise AdminPaymentNotFoundError()
        target = None
        for item in data:
            if getattr(item, "store_id", None):
                target = ObjectPaymentSettingsTarget.STORE
                store = await Store.get(item.store_id)
                object_id = item.store_id
                if store.brand_id != payment_settings.brand_id:
                    raise AdminPaymentStoreNotInBrandError()
            elif getattr(item, "invoice_template_id", None):
                target = ObjectPaymentSettingsTarget.INVOICE_TEMPLATE
                object_id = item.invoice_template_id
            else:
                raise AdminPaymentNotFoundError()

            try:
                is_fiscal = False
                if payment_settings.payment_method == "liqpay":
                    is_fiscal = payment_settings.json_data.get("is_need_fiscal", False)

                validator = PaymentSettingsValidator(
                    self.lang, payment_settings.payment_method, brand.id, None,
                    item.json_data.data.dict() if item.json_data else None,
                    is_update=True, is_store=True, is_fiscal=is_fiscal,
                )
                processed_json_data = await validator.validate()
            except Exception as ex:
                logging.error(ex, exc_info=True)
                raise AdminPaymentValidationError()

            if target == ObjectPaymentSettingsTarget.STORE:
                object_payment_settings = await ObjectPaymentSettings.get(
                    store_id=object_id,
                    payment_settings_id=payment_settings.id,
                    is_deleted=False
                )
            else:
                object_payment_settings = await ObjectPaymentSettings.get(
                    invoice_template_id=object_id,
                    payment_settings_id=payment_settings.id,
                    is_deleted=False
                )

            if not object_payment_settings:
                raise AdminPaymentNotFoundError()

            # Використовуємо CRUD функцію для оновлення
            await crud.update_object_payment_settings_with_translations(
                object_payment_settings_id=object_payment_settings.id,
                is_enabled=item.is_enabled,
                json_data=processed_json_data,
                post_payment_info=item.post_payment_info,
                translations=getattr(item, 'translations', None),
                profile_id=self.profile_id,
            )

        return await self.get_objects_data(
            payment_id=payment_settings_id, target=target
        )

    async def get_objects_data(
            self, payment_id: int, target: ObjectPaymentSettingsTarget,
    ) -> list[schemas.AdminPaymentStore | schemas.AdminPaymentInvoiceTemplate]:
        payment_settings = await PaymentSettings.get(id=payment_id)
        if not payment_settings:
            raise AdminPaymentNotFoundError()

        object_payment_settings = await crud.get_object_payment_settings_(
            payment_id, target
        )
        if target == ObjectPaymentSettingsTarget.STORE:
            existing_object_payments = {sp.store_id: sp for sp in
                                        object_payment_settings}
            objects = await crud.get_stores_names_by_profile(self.profile_id)
        elif target == ObjectPaymentSettingsTarget.INVOICE_TEMPLATE:
            existing_object_payments = {sp.invoice_template_id: sp for sp in
                                        object_payment_settings}
            objects = await crud.get_invoice_templates_names_by_profile(self.profile_id)
        else:
            raise AdminPaymentNotFoundError()

        res = []
        profile = await Group.get(self.profile_id)

        for target_object in objects:
            object_payment_settings = existing_object_payments.get(target_object.id)

            if not object_payment_settings:
                object_payment_settings = await ObjectPaymentSettings.create(
                    store_id=target_object.id if target ==
                                                 ObjectPaymentSettingsTarget.STORE
                    else None,
                    invoice_template_id=target_object.id if target ==
                                                            ObjectPaymentSettingsTarget.INVOICE_TEMPLATE else None,
                    payment_settings_id=payment_settings.id,
                    json_data=None,
                    is_enabled=False,
                    target=target,
                    post_payment_info=None,
                )

            # Отримуємо переклади для ObjectPaymentSettings
            translations = await get_translations_schemas_dict(
                object_payment_settings, profile, schemas.ObjectPaymentSettingsTranslationSchema
            )

            res.append(
                schemas.AdminPaymentStore(
                    store_id=target_object.id,
                    store_name=target_object.name,
                    is_enabled=object_payment_settings.is_enabled,
                    json_data=payment_provider_data_to_schema(
                        payment_settings.payment_method,
                        object_payment_settings.json_data
                    ),
                    post_payment_info=object_payment_settings.post_payment_info,
                    object_payment_settings_id=object_payment_settings.id,
                    translations=translations,
                ) if target == ObjectPaymentSettingsTarget.STORE else
                schemas.AdminPaymentInvoiceTemplate(
                    invoice_template_id=target_object.id,
                    invoice_template_name=target_object.name,
                    is_enabled=object_payment_settings.is_enabled,
                    json_data=payment_provider_data_to_schema(
                        payment_settings.payment_method,
                        object_payment_settings.json_data
                    ),
                    post_payment_info=object_payment_settings.post_payment_info,
                    invoice_template_payment_settings_id=object_payment_settings.id,
                    translations=translations,
                )
            )

        return res

    async def update_payment_icon(
            self, payment_id: int, file: UploadFile
    ) -> schemas.AdminPaymentSchema:
        brand = await crud.get_brand_by_group(self.profile_id)
        payment_settings = await PaymentSettings.get(id=payment_id, brand_id=brand.id)
        if not payment_settings:
            raise AdminPaymentNotFoundError()

        media = await media_manager.save_from_upload_file(file)

        await payment_settings.update(media_id=media.id)
        return await self.payment_settings_to_schema(payment_settings)

    async def delete_payment_icon(self, payment_id: int) -> schemas.AdminPaymentSchema:
        brand = await crud.get_brand_by_group(self.profile_id)
        payment_settings = await PaymentSettings.get(id=payment_id, brand_id=brand.id)
        if not payment_settings:
            raise AdminPaymentNotFoundError()

        await payment_settings.update(media_id=None)

        return await self.payment_settings_to_schema(payment_settings)

    @classmethod
    async def delete_payment(cls, payment_id: int) -> schemas.OkResponse:
        payment_settings = await PaymentSettings.get(id=payment_id)
        if not payment_settings:
            raise AdminPaymentNotFoundError()
        if payment_settings.payment_method == "cash":
            raise AdminPaymentsDeleteBaseMethodError()

        res = await crud.delete_payment_settings(payment_id)
        if not res:
            raise AdminPaymentDeleteError()

        return schemas.OkResponse()

    async def generate_wave_merchant(
            self, payment_settings_id: int,
            object_payment_settings_id: int | None = None,
            invoice_template_payment_settings_id: int | None = None,
    ) -> str:
        brand = await Brand.get(group_id=self.profile_id)
        store, invoice_template = None, None
        merchant_name = brand.name
        if object_payment_settings_id:
            object_payment_settings = await ObjectPaymentSettings.get(
                object_payment_settings_id
            )
            store = await Store.get(object_payment_settings.store_id)
            merchant_name = store.name
        if invoice_template_payment_settings_id:
            object_payment_settings = await ObjectPaymentSettings.get(
                invoice_template_payment_settings_id
            )
            invoice_template = await InvoiceTemplate.get(
                object_payment_settings.invoice_template_id
            )
            if invoice_template:
                merchant_name = invoice_template.title

        return await create_aggregated_merchant_id(
            merchant_name, payment_settings_id, brand, store.id if store else None,
            invoice_template.id if invoice_template else None
        )

    @handle_liqpay_error
    async def export_liqpay_products(
            self, payment_settings_id: int,
            object_payment_settings_id: int | None = None
    ):
        brand, store_id = await self._get_brand_and_store_id(object_payment_settings_id)
        return await export_liqpay_products(brand.id, payment_settings_id, store_id)

    @handle_liqpay_error
    async def import_liqpay_products(
            self, file: UploadFile, payment_settings_id: int,
            object_payment_settings_id: int | None = None,
    ):
        brand, store_id = await self._get_brand_and_store_id(object_payment_settings_id)

        return await import_liqpay_products(
            payment_settings_id, brand.id, file, store_id, object_payment_settings_id
        )

    async def _get_brand_and_store_id(
            self, object_payment_settings_id: int | None = None
    ):
        brand = await crud.get_brand_by_group(self.profile_id)
        store_id = None
        if object_payment_settings_id:
            object_payment_settings = await ObjectPaymentSettings.get(
                object_payment_settings_id
            )
            if not object_payment_settings:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="store payment settings not found"
                )
            store_id = object_payment_settings.store_id
        return brand, store_id
