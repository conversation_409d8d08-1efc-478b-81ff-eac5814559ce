from fastapi import APIRouter, Security, UploadFile

import schemas
from schemas import Base64EncodedFile, ProductFromLiqPay
from schemas.payment_settings.schemas_add import ObjectPaymentSettingsTarget
from .service import PaymentService

router = APIRouter(
    prefix="/{payment_settings_id}"
)


@router.get("/")
async def get_payment(
    payment_settings_id: int,
    service: PaymentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.AdminPaymentSchema:
    return await service.get_payment(payment_settings_id)


@router.patch("/update")
async def update_payment(
    payment_settings_id: int,
    data: schemas.AdminUpdatePaymentMainDataSchema,
    service: PaymentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.AdminPaymentSchema:
    return await service.update_payment_main_data(data, payment_settings_id)


@router.patch("/update_stores")
async def update_store_payment(
    payment_settings_id: int,
    data: list[schemas.UpdateAdminPaymentStore],
    service: PaymentService = Security(scopes=["me:write", "menu:create"]),
) -> list[schemas.AdminPaymentStore]:
    return await service.update_payment_objects_data(data, payment_settings_id)


@router.patch("/update_invoice_templates")
async def update_invoice_template_payment(
    payment_settings_id: int,
    data: list[schemas.UpdateAdminPaymentInvoiceTemplate],
    service: PaymentService = Security(scopes=["me:write", "menu:create"]),
) -> list[schemas.AdminPaymentInvoiceTemplate]:
    return await service.update_payment_objects_data(data, payment_settings_id)

@router.get("/stores")
async def get_stores_payment(
    payment_settings_id: int,
    service: PaymentService = Security(scopes=["me:write", "menu:create"]),
) -> list[schemas.AdminPaymentStore]:
    return await service.get_objects_data(payment_settings_id, target=ObjectPaymentSettingsTarget.STORE)


@router.get("/invoice_templates")
async def get_invoice_templates_payment(
    payment_settings_id: int,
    service: PaymentService = Security(scopes=["me:write", "menu:create"]),
) -> list[schemas.AdminPaymentInvoiceTemplate]:
    return await service.get_objects_data(payment_settings_id, target=ObjectPaymentSettingsTarget.INVOICE_TEMPLATE)


@router.delete("/delete")
async def delete_payment(
    payment_settings_id: int,
    service: PaymentService = Security(scopes=["me:write", "menu:create"]),
) -> schemas.OkResponse:
    return await service.delete_payment(payment_settings_id)


@router.post("/update_image")
async def update_payment_icon(
    file: UploadFile,
    payment_settings_id: int,
    service: PaymentService = Security(scopes=["product:edit", "me:write"]),
) -> schemas.AdminPaymentSchema:
    return await service.update_payment_icon(payment_settings_id, file)


@router.delete("/delete_image")
async def delete_payment_icon(
    payment_settings_id: int,
    service: PaymentService = Security(scopes=["product:edit", "me:write"]),
) -> schemas.AdminPaymentSchema:
    return await service.delete_payment_icon(payment_settings_id)


@router.patch("/generate_wave_merchant")
async def generate_wave_merchant(
    payment_settings_id: int,
    object_payment_settings_id: int | None = None,
    invoice_template_payment_settings_id: int | None = None,
    service: PaymentService = Security(scopes=["product:edit", "me:write"]),
) -> str:
    return await service.generate_wave_merchant(payment_settings_id, object_payment_settings_id, invoice_template_payment_settings_id)


@router.patch(
    "/liqpay/products",
    description="Method to download products file for liqpay",
    response_model=Base64EncodedFile,
)
async def download_liqpay_products(
    payment_settings_id: int,
    object_payment_settings_id: int | None = None,
    service: PaymentService = Security(scopes=["product:edit", "me:write"]),
) -> Base64EncodedFile:
    return await service.export_liqpay_products(payment_settings_id, object_payment_settings_id)


@router.post(
    "/liqpay/products",
    description="Method to upload liqpay products file",
    response_model=list[ProductFromLiqPay | None]
)
async def upload_liqpay_products(
    file: UploadFile,
    payment_settings_id: int,
    object_payment_settings_id: int | None = None,
    service: PaymentService = Security(scopes=["product:edit", "me:write"]),
) -> list[ProductFromLiqPay | None]:
    return await service.import_liqpay_products(file, payment_settings_id, object_payment_settings_id)
