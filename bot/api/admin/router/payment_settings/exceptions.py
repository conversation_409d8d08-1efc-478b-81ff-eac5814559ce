from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class AdminPaymentsCustomNameError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments custom type name empty error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_custom_name",
            }
        )


class AdminPaymentsOnlineProviderDataEmptyError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments online provider data empty error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_online_provider_data_empty",
            }
        )


class AdminPaymentsCreateError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments create error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_create",
            }
        )


class AdminPaymentsDisabledMethodTypeForCreateError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments disabled method for create error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_disabled_method_type_for_create",
            }
        )


class AdminPaymentsProviderDataError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments provider data error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_provider_data",
            }
        )


class AdminPaymentsProviderValidateDataError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "store brand payment method connect error"

    def __init__(self, provider: str):
        super().__init__(
            provider=provider,
            detail_data={
                "error_code": "payments_provider_data",
                "provider": provider,
            }
        )


class AdminPaymentNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "admin payments payment not found"
    error_code = "payment_not_found"


class AdminPaymentUpdateError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments update error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payment_update",
            }
        )


class AdminPaymentStoreNotInBrandError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments invalid store error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payment_store_not_in_brand",
            }
        )


class AdminPaymentsDeleteBaseMethodError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments delete base error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_delete_base",
            }
        )


class AdminPaymentDeleteError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments delete error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_delete",
            }
        )


class AdminPaymentValidationError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments validation error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_validation",
            }
        )


class AdminPaymentWaveCreateMerchantError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments wave merchants create error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_wave_merchant_create",
            }
        )


class AdminPaymentUniposForStoreError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments unipos for store error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_unipos_store",
            }
        )


class AdminPaymentsIncustPayMerchantDataDataError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments incust pay merchant data error"

    def __init__(self, incust_account_id: str | None = None):
        super().__init__(
            incust_account_id=incust_account_id if incust_account_id else "",
            detail_data={
                "error_code": "merchant_data_error",
                "incust_account_id": incust_account_id,
            }
        )

class AdminPaymentsMerchantDataDataError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments merchant data more then 1 error"

    def __init__(self):
        super().__init__(
            detail_data={
                "error_code": "payments_merchant_data_more_then_1",
            }
        )


class AdminPaymentEWalletDisabledError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "admin payments ewallet disabled error"

    def __init__(self, ewallet_name: str | None = None):
        super().__init__(
            ewallet_name=ewallet_name,
            detail_data={
                "error_code": "ewallet_disabled",
            }
        )
