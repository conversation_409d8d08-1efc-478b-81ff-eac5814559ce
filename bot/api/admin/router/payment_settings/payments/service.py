import logging
from typing import cast

from fastapi import HTTPException

import schemas
from config import (
    ADDITIONAL_PAYMENT_METHODS_ONLINE, INCUST_SERVER_API, P4S_API_URL,
    PAYMENT_DEFAULT_NAMES,
    PAYMENT_METHODS,
    PAYMENT_METHODS_WEBHOOK,
)
from core.auth.services.scopes_checker import ScopesCheckerService
from core.loyalty.incust_api import incust
from core.payment.funcs import get_payment_default_name, get_payment_method_fields
from core.store.functions.payments import get_icon_for_payment
from db import crud
from db.models import (
    Brand, BrandCustomSettings, BusinessPaymentSetting, EWallet, Group, MediaObject,
    PaymentSettings, User,
)
from loggers import JSONLogger
from schemas.payment_settings.schemas_add import (
    AdminSpecialsResponse,
)
from schemas.store.types import ShipmentType
from utils.text import f
from ..exceptions import AdminPaymentValidationError, AdminPaymentsCreateError
from ..validator import PaymentSettingsValidator

logger = JSONLogger("payments.service")


def get_payment_method_special_fields():
    return {
        schemas.BusinessPaymentMethodEnum.WAVE: [
            schemas.PaymentProviderItemFieldSchema(
                type="string",
                is_required=True,
                name="phone",
                field_size=6,
            ),
            schemas.PaymentProviderItemFieldSchema(
                type="string",
                is_required=True,
                name="name",
                field_size=6,
            )
        ],
        schemas.BusinessPaymentMethodEnum.ORANGE: [
            schemas.PaymentProviderItemFieldSchema(
                type="string",
                is_required=True,
                name="phone",
                field_size=6,
            ),
        ],
        schemas.BusinessPaymentMethodEnum.AIRTEL: [
            schemas.PaymentProviderItemFieldSchema(
                type="string",
                is_required=True,
                name="phone",
                field_size=6,
            ),
        ],
        schemas.BusinessPaymentMethodEnum.MOMO: [
            schemas.PaymentProviderItemFieldSchema(
                type="string",
                is_required=True,
                name="phone",
                field_size=6,
            ),
        ],
        schemas.BusinessPaymentMethodEnum.EWALLET: [
            schemas.PaymentProviderItemFieldSchema(
                type="string",
                is_required=True,
                name="ewallet_id",
                field_size=6,
            ),
        ],
        schemas.BusinessPaymentMethodEnum.KPAY: [
            schemas.PaymentProviderItemFieldSchema(
                type="string",
                is_required=True,
                name="phone",
                field_size=6,
            ),
            schemas.PaymentProviderItemFieldSchema(
                type="string",
                is_required=True,
                name="first_name",
                field_size=6,
            ),
            schemas.PaymentProviderItemFieldSchema(
                type="string",
                is_required=True,
                name="last_name",
                field_size=6,
            ),
        ]
    }


class PaymentsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_specials(
            self, terminal_api_key: str, server_api_url: str,
            incust_account_id: str | None = None
    ) -> AdminSpecialsResponse | None:
        # Створюємо тимчасові налаштування для отримання спеціальних рахунків
        from db.models import LoyaltySettings
        
        temp_settings = LoyaltySettings(
            terminal_api_key=terminal_api_key,
            server_url=server_api_url,
            loyalty_id="",
            is_enabled=True
        )

        try:
            async with incust.client.WalletApi(temp_settings, lang=self.lang) as api:
                wallet_response = await api.wallet()
                incust_special_accounts = wallet_response.special_accounts if hasattr(wallet_response, 'special_accounts') else []
        except Exception:
            # specials not found...
            incust_special_accounts = []

        if not incust_special_accounts:
            return None

        async with incust.term.SettingsApi(temp_settings, lang=self.lang) as api:
            terminal_settings = await api.settings()

        # Отримуємо активні бізнес-налаштування оплати
        # Якщо вказано incust_account_id, фільтруємо тільки за ним
        filters = {"is_deleted": False, "is_enabled": True}
        if incust_account_id:
            filters["incust_account_id"] = incust_account_id

        business_settings = await BusinessPaymentSetting.get_list(**filters)

        # Створюємо словник для збереження даних про провайдери платежів для кожного бізнес-налаштування
        business_providers = {}

        # Для кожного бізнес-налаштування отримуємо активні дані платіжних провайдерів
        for bs in business_settings:
            # Отримуємо тільки активні, невидалені дані провайдерів
            payment_data_list = await crud.get_active_business_payment_data(
                business_payment_setting_id=bs.id,
                only_enabled=True  # Отримуємо тільки активні записи
            )

            business_providers[bs.incust_account_id] = {
                "payment_data": payment_data_list,
                "setting": bs
            }

        # Створюємо словник для полів платіжних методів
        payment_method_fields = get_payment_method_special_fields()

        # Створюємо список спеціальних рахунків з методами оплати
        special_accounts_with_methods = []

        # Перебираємо всі отримані рахунки Incust
        for special_account in incust_special_accounts:
            # Перевіряємо валюту
            if special_account.currency != terminal_settings.currency:
                continue

            # Перевіряємо, чи існують бізнес-налаштування для цього incust_account_id
            if special_account.id not in business_providers:
                continue

            # Отримуємо дані про провайдерів для цього рахунку
            provider_data = business_providers[special_account.id]
            payment_data_list = provider_data["payment_data"]

            # Створюємо методи оплати для цього рахунку
            payment_methods = []

            for payment_data in payment_data_list:
                # Додаємо метод оплати з id бізнес-платіжних даних та полями
                payment_methods.append(
                    schemas.payment_settings.schemas_add.PaymentMethodForSpecialAccount(
                        provider=payment_data.payment_method,
                        business_payment_data_id=payment_data.id,
                        fields=payment_method_fields.get(payment_data.payment_method, []),
                        is_enabled=payment_data.is_enabled
                    )
                )

            # Створюємо спеціальний рахунок з методами оплати
            special_account_with_methods = schemas.payment_settings.schemas_add.SpecialAccountWithPaymentMethods(
                **special_account.dict(),
                payment_methods=payment_methods
            )

            # Додаємо до результуючого списку
            special_accounts_with_methods.append(special_account_with_methods)

        # Повертаємо спеціальні рахунки з методами оплати
        return AdminSpecialsResponse(
            specials=special_accounts_with_methods
        )

    async def get_ewallet_specials(
            self, ewallet_id: int,
    ) -> AdminSpecialsResponse | None:
        ewallet = await EWallet.get(id=ewallet_id, is_deleted=False, is_enabled=True)
        if not ewallet:
            raise HTTPException(status_code=404, detail="EWallet not found")
            
        # Викликаємо get_specials з incust_account_id для отримання лише потрібних даних
        return await self.get_specials(
            ewallet.terminal_api_key, 
            ewallet.server_api_url, 
            ewallet.incust_account_id
        )

    async def get_payments_list(
            self, search_text: str | None = None,
    ) -> list[schemas.AdminPaymentSettingsListSchema]:
        brand = await Brand.get(group_id=self.profile_id)
        res = []

        if brand:
            payment_settings = await crud.get_payment_methods(
                brand.id, with_translations=False,
            )
            for setting in payment_settings:
                if setting.payment_method == "ewallet":
                    eWallet = await EWallet.get(id=setting.json_data.get("ewallet_id"), is_deleted=False, is_enabled=True)
                    if not eWallet:
                        continue
                res.append(await self.payment_to_list_schema(setting, self.lang))

        return res

    async def get_payments_total_count(self, search_text: str | None = None) -> int:
        brand = await Brand.get(group_id=self.profile_id)
        rest_count = await crud.get_payment_methods(
            brand.id, search_text, operation="count"
        ) or 0

        return rest_count

    @classmethod
    async def payment_to_list_schema(
            cls, payment_settings: PaymentSettings, lang: str
    ) -> schemas.AdminPaymentSettingsListSchema:

        ewallet_id = payment_settings.json_data.get(
            "ewallet_id", None
        ) if payment_settings.json_data else None

        media = await MediaObject.get(payment_settings.media_id)
        if not media and ewallet_id:
            ewallet = await EWallet.get(ewallet_id)
            if ewallet:
                media = await MediaObject.get(ewallet.media_id)

        default_name = await get_payment_default_name(
                payment_settings.payment_method, lang, payment_settings.is_online,
                ewallet_id=ewallet_id,
            )

        return schemas.AdminPaymentSettingsListSchema(
            id=payment_settings.id,
            position=payment_settings.position,
            name=payment_settings.name,
            default_name=default_name,
            payment_method=payment_settings.payment_method,
            is_enabled=payment_settings.is_enabled,
            is_online=payment_settings.is_online,
            icon_url=media.url if media else get_icon_for_payment(
                payment_settings.payment_method
            ),
            json_data=payment_settings.json_data,
        )

    async def create_payment_setting(
            self, data: schemas.AdminCreatePaymentData
    ) -> schemas.CreateAdminPaymentSettingsListSchema:
        brand = await Brand.get(group_id=self.profile_id)

        logger.debug(f"create_payment_setting",  {"json_data": data.json_data})

        try:
            validator = PaymentSettingsValidator(
                self.lang, data.payment_method, brand.id, data.name,
                data.json_data.data.dict() if data.json_data else None,
                stores_json_data=data.stores_json_data,
            )
            processed_json_data = await validator.validate()
        except Exception as ex:
            if isinstance(ex, HTTPException):
                raise ex
            else:
                logging.error("An unexpected error occurred: %s", ex, exc_info=True)
                raise AdminPaymentValidationError() from ex

        store_json_data = await self.process_stores_payment_data(brand, data)
        invoice_template_json_data = await self.process_invoice_templates_payment_data(
            brand, data
        )

        payment_settings = await crud.new_create_payment_settings(
            payment_method=data.payment_method,
            brand_id=brand.id,
            data=processed_json_data,
            name=data.name,
            description=data.description,
            with_comment=data.with_comment,
            is_enabled=True,
            store_json_data=store_json_data,
            invoice_template_json_data=invoice_template_json_data,
            post_payment_info=data.post_payment_info,
            label_comment=data.label_comment,
        )
        if not payment_settings:
            raise AdminPaymentsCreateError()

        extra_data = None

        if data.payment_to_shipments:
            for payment_to_shipment in data.payment_to_shipments:
                if payment_to_shipment.shipment_id:
                    await crud.update_payment_setting_to_shipment(
                        payment_to_shipment.shipment_id, payment_settings.id,
                        payment_to_shipment.is_enabled
                    )
        else:
            shipments = []
            for base_type in ShipmentType:
                brand_settings = await BrandCustomSettings.get_or_create_base_shipment(
                    brand.id, base_type.value
                )
                shipments.append(brand_settings)

            rest = await crud.get_all_custom_shipments_and_groups(brand.id)
            for settings in rest:
                shipments.append(settings)
            for shipment in shipments:
                await crud.update_payment_setting_to_shipment(
                    shipment.id, payment_settings.id, True
                )

        return schemas.CreateAdminPaymentSettingsListSchema(
            payment_setting=await self.payment_to_list_schema(
                payment_settings, self.lang
            ),
            extra_data=extra_data,
        )

    async def process_stores_payment_data(
            self, brand: Brand, data: schemas.AdminCreatePaymentData
    ):
        store_json_data = None
        if data.stores_json_data:
            store_json_data = {}
            for store_data in data.stores_json_data:
                store_json_data[store_data.store_id] = {}

                if store_data.json_data:
                    try:
                        is_fiscal = False
                        if data.payment_method == "liqpay":
                            is_fiscal = store_data.json_data.data.dict().get(
                                "is_need_fiscal", False
                            )
                        validator = PaymentSettingsValidator(
                            self.lang, data.payment_method, brand.id, data.name,
                            store_data.json_data.data.dict(), is_store=True,
                            is_fiscal=is_fiscal,
                        )
                        processed_store_json_data = await validator.validate()
                    except Exception as ex:
                        if isinstance(ex, HTTPException):
                            raise ex
                        else:
                            logging.error(
                                "An unexpected error occurred: %s", ex, exc_info=True
                            )
                            raise AdminPaymentValidationError() from ex
                    store_json_data[store_data.store_id].update(
                        {
                            "json_data": processed_store_json_data,
                        }
                    )
                store_json_data[store_data.store_id].update(
                    {
                        "is_enabled": store_data.is_enabled if store_data.is_enabled
                                                               is not None else True,
                    }
                )
        return store_json_data

    async def process_invoice_templates_payment_data(
            self, brand: Brand, data: schemas.AdminCreatePaymentData
    ):
        invoice_template_json_data = None
        if data.invoice_templates_json_data:
            invoice_template_json_data = {}
            for invoice_template_data in data.invoice_templates_json_data:
                invoice_template_json_data[
                    invoice_template_data.invoice_template_id] = {}

                if invoice_template_data.json_data:
                    try:
                        is_fiscal = False
                        if data.payment_method == "liqpay":
                            is_fiscal = invoice_template_data.json_data.data.dict().get(
                                "is_need_fiscal", False
                            )
                        validator = PaymentSettingsValidator(
                            self.lang, data.payment_method, brand.id, data.name,
                            invoice_template_data.json_data.data.dict(), is_store=True,
                            is_fiscal=is_fiscal,
                        )
                        processed_store_json_data = await validator.validate()
                    except Exception as ex:
                        if isinstance(ex, HTTPException):
                            raise ex
                        else:
                            logging.error(
                                "An unexpected error occurred: %s", ex, exc_info=True
                            )
                            raise AdminPaymentValidationError() from ex
                    invoice_template_json_data[
                        invoice_template_data.invoice_template_id].update(
                        {
                            "json_data": processed_store_json_data,
                        }
                    )
                invoice_template_json_data[
                    invoice_template_data.invoice_template_id].update(
                    {
                        "is_enabled": invoice_template_data.is_enabled if
                        invoice_template_data.is_enabled
                                                                          is not None else True,
                    }
                )
        return invoice_template_json_data

    @classmethod
    def get_wave_webhook_url(cls, ) -> str:
        return f"{P4S_API_URL}/payments/callback/wave"

    @classmethod
    def get_payment_webhook_url(cls, payment_method: str) -> str:
        return f"{P4S_API_URL}/payments/callback/{payment_method}"

    async def get_payment_methods_schema(self) -> schemas.PaymentProvidersSchema:
        providers = ["liqpay", "stripe", "epay", "comsa", "flutterwave", "fondy",
                     "freedompay", "orange", "pl24",
                     "tpay", "wave", "unipos", "directpay", "momo", "tj", "airtel",
                     "custom", "kpay"]

        items = []
        group = await Group.get(self.profile_id)
        default_names = {}
        for provider in providers:
            item = get_payment_method_fields(cast(schemas.BasicProvidersType, provider))
            if provider in PAYMENT_METHODS_WEBHOOK:
                webhook_url = f"{P4S_API_URL}/payments/callback/{provider}"
                item.fields.insert(
                    0,
                    schemas.PaymentProviderItemFieldSchema(
                        type="string",
                        is_required=False,
                        name="webhook_url",
                        field_size=12,
                        readonly=True,
                        value=webhook_url,
                        is_copyable=True,
                    )
                )
                item.fields.insert(
                    0,
                    schemas.PaymentProviderItemFieldSchema(
                        type="alert",
                        is_required=False,
                        name="webhook_url",
                        field_size=12,
                        readonly=True,
                        description=await f(
                            f"admin payment settings {provider} alert label",
                            self.lang,
                            webhook_url=webhook_url,
                        )
                    )
                )
            items.append(item)
            default_names[provider] = await get_payment_default_name(
                provider, group.lang,
                provider in ADDITIONAL_PAYMENT_METHODS_ONLINE or provider in
                PAYMENT_METHODS
            )

        ewallets = await crud.get_admin_profile_ewallet_list(self.profile_id)
        if ewallets:
            for ewallet in ewallets:
                if not ewallet.is_enabled:
                    continue
                item = get_payment_method_fields(
                    cast(schemas.BasicProvidersType, "ewallet")
                )
                item.provider_id = ewallet.id
                item.provider_name = ewallet.name
                items.append(item)

        for key, _ in PAYMENT_DEFAULT_NAMES.items():
            if key == "online_card":
                continue
            if key not in providers:
                default_names[key] = await get_payment_default_name(
                    key, group.lang,
                    key in ADDITIONAL_PAYMENT_METHODS_ONLINE or key in PAYMENT_METHODS
                )

        extra_data = {
            "default_names": default_names,
            "default_incust_server_url": INCUST_SERVER_API,
        }

        return schemas.PaymentProvidersSchema(
            schemas=items, extra_data=extra_data,
        )
