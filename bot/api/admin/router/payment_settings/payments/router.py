from fastapi import APIRouter, Depends, Path, Query

import schemas
from schemas.payment_settings.schemas_add import AdminSpecialsResponse
from .service import PaymentsService

router = APIRouter(
    prefix=""
)


@router.post("/specials")
async def get_specials(
        terminal_api_key: str = Query(),
        server_api_url: str = Query(),
        service: PaymentsService = Depends()
) -> AdminSpecialsResponse | None:
    return await service.get_specials(terminal_api_key, server_api_url)


@router.post("/ewallet/{ewallet_id}/specials")
async def get_ewallet_specials(
        ewallet_id: int = Path(
            description="eWallet ID",
        ),
        service: PaymentsService = Depends()
) -> AdminSpecialsResponse | None:
    return await service.get_ewallet_specials(ewallet_id)


@router.get("/")
async def get_payments(
        search_text: str | None = Query(None),
        service: PaymentsService = Depends()
) -> list[schemas.AdminPaymentSettingsListSchema]:
    return await service.get_payments_list(search_text)


@router.get("/methods_schema")
async def get_payment_methods_schema(
        service: PaymentsService = Depends()
) -> schemas.PaymentProvidersSchema:
    return await service.get_payment_methods_schema()


@router.get("/total_count")
async def get_payments_total_count(
        search_text: str | None = Query(None),
        service: PaymentsService = Depends()
) -> int:
    return await service.get_payments_total_count(search_text)


@router.post("/create")
async def create_payment(
        data: schemas.AdminCreatePaymentData,
        service: PaymentsService = Depends()
) -> schemas.CreateAdminPaymentSettingsListSchema:
    return await service.create_payment_setting(data)
