import json

import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import (
    Brand, User,
)


class FiltersService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_filters_list(
            self,
            type: schemas.FilterListType = schemas.FilterListType.category,
    ) -> list[schemas.AdminFilterListSchema]:
        if type == schemas.FilterListType.store:
            brand = await Brand.get_by_group(self.profile_id)
            raw = await crud.get_stores_with_filters(brand.id)

            nodes: list[schemas.AdminFilterListSchema] = []
            for row in raw:
                store, char_json = row[0], row[1]

                try:
                    char_list = json.loads(char_json) if char_json else []
                except json.JSONDecodeError:
                    char_list = []

                filters = [
                    schemas.AdminFilterItemSchema(
                        id=ch["id"],
                        name=ch["name"],
                    )
                    for ch in char_list
                ]

                node = schemas.AdminFilterListSchema(
                    id=store.id,
                    name=store.name,
                    connected_filters=filters,
                )
                node.children = None

                nodes.append(node)

            return nodes
        else:
            raw = await crud.get_admin_categories_list(
                self.profile_id,
                self.user.id,
                sort_by_father_category=True,
                with_filters=True,
            )

            grouped: dict[int | None, list] = {}
            for item in raw:
                grouped.setdefault(item.father_category_id, []).append(item)

            def make_categories(father_id: int | None = None):
                out: list[schemas.AdminFilterListSchema] = []
                for child in grouped.get(father_id, []):
                    char_list = []
                    if child.characteristics:
                        try:
                            char_list = json.loads(child.characteristics)
                        except json.JSONDecodeError:
                            char_list = []

                    filters = [
                        schemas.AdminFilterItemSchema(
                            id=ch["id"],
                            name=ch["name"],
                        )
                        for ch in char_list
                    ]

                    node = schemas.AdminFilterListSchema(
                        id=child.id,
                        name=child.name,
                        connected_filters=filters,
                    )
                    node.children = make_categories(child.id)
                    out.append(node)
                return out

            return make_categories()
