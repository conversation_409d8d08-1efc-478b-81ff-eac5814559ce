from fastapi import <PERSON>Rout<PERSON>, Depends, Query, Security

import schemas
from .service import FilterService

router = APIRouter(
    prefix="/{object_id}",
)


@router.get('/')
async def get_filter_setting(
        object_type: schemas.FilterListType = Query(schemas.FilterListType.category),
        service: FilterService = Depends()
) -> schemas.AdminFilterSchema:
    return await service.get_filter_setting(object_type)


@router.patch('/')
async def update_filter_setting(
        data: schemas.AdminFilterUpdateSchema,
        service: FilterService = Security(
            scopes=["me:write", "profile:edit"]
        )
) -> schemas.AdminFilterSchema:
    return await service.update_filter_setting(data)
