from dataclasses import asdict
from typing import Any

import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import (User)
from exceptions import FilterSettingNotFoundError


class FilterService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",  # will be overridden in "write" routes
                "profile_id",
                "object_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.object_id: int = scopes.data.object_id

    async def get_filter_setting(
            self,
            type: schemas.FilterListType
    ) -> schemas.AdminFilterSchema:
        filters: Any = None

        match type:
            case schemas.FilterListType.category:
                entity = await crud.get_category_by_id_and_profile_id(
                    self.object_id, self.profile_id
                )
                filters = await crud.get_admin_characteristic_filters(
                    brand_id=self.profile_id, category_id=entity.id, store_id=None
                )

            case schemas.FilterListType.store:
                entity = await crud.get_store_by_id_and_profile_id(
                    self.object_id, self.profile_id
                )
                filters = await crud.get_admin_characteristic_filters(
                    brand_id=self.profile_id, category_id=None, store_id=entity.id
                )

            case _:
                raise ValueError(f"Unsupported filter type: {type}")

        if entity is None:
            raise FilterSettingNotFoundError(self.object_id)

        return schemas.AdminFilterSchema(
            id=entity.id,
            name=entity.name,
            connected_filters=[
                schemas.AdminFilterItemSchema(
                    id=fi.id,
                    name=fi.name,
                    filter_type=fi.filter_type,
                    is_hide=fi.is_hide,
                )
                for fi in filters
            ],
        )

    async def update_filter_setting(
            self, data: schemas.AdminFilterUpdateSchema
    ):
        if data.type == schemas.FilterListType.category:
            await crud.update_category_filters(
                self.object_id, data.connected_filters or []
            )


        elif data.type == schemas.FilterListType.store:
            await crud.update_store_filters(
                self.profile_id,
                self.object_id, data.connected_filters or []
            )

        return await self.get_filter_setting(data.type)
