from fastapi import APIRouter, Depends, Query

import schemas
from .service import InvoiceTemplatesService

router = APIRouter()


@router.get("/")
async def get_invoice_templates(
        search_text: str | None = Query(None),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit invoice templates. Max: 100"),
        excluded_invoice_template_ids: list[int] | None = Query(None),
        service: InvoiceTemplatesService = Depends()
) -> list[schemas.AdminInvoiceTemplateListSchema]:
    return await service.get_invoice_templates(
        search_text, offset, limit, excluded_invoice_template_ids
    )


@router.get("/total_count")
async def get_invoice_templates_total_count(
        search_text: str | None = Query(None),
        service: InvoiceTemplatesService = Depends()
) -> int:
    return await service.get_invoice_templates_total_count(search_text)


@router.post("/")
async def create_invoice_template(
        data: schemas.AdminCreateInvoiceTemplate,
        service: InvoiceTemplatesService = Depends()
) -> schemas.AdminInvoiceTemplateResponse:
    return await service.create_invoice_template(data)
