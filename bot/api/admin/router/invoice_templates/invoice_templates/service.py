import schemas
from api.admin.router.invoice_templates.functions import \
    (
    invoice_template_to_admin_schema, validate_data,
)
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import User


class InvoiceTemplatesService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_invoice_templates(
            self,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
            excluded_invoice_template_ids: list[int] | None = None,
    ) -> list[schemas.AdminInvoiceTemplateListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        invoice_templates = (
                await crud.get_invoice_templates(
                    group_id=self.profile_id,
                    search_text=search_text,
                    position=offset or 0,
                    limit=limit,
                    excluded_invoice_template_ids=excluded_invoice_template_ids,
                )
                or []
        )

        return [
            schemas.AdminInvoiceTemplateListSchema(
                id=invoice_template.id,
                title=invoice_template.title,
                photo_url=invoice_template.photo_url or None,
            )
            for invoice_template in invoice_templates
        ]

    async def get_invoice_templates_total_count(
            self,
            search_text: str | None = None,
    ) -> int:
        return await crud.get_invoice_templates(
            group_id=self.profile_id,
            position=0,
            limit=None,
            operation="count",
            search_text=search_text,
        )

    async def create_invoice_template(self, data: schemas.AdminCreateInvoiceTemplate):

        _, incust_terminal_id = await validate_data(self.profile_id, data)
        if incust_terminal_id:
            data.incust_terminal_id = incust_terminal_id

        invoice_template = await crud.create_invoice_template(data, self.profile_id)

        await crud.create_object_payment_settings_for_object(
            self.profile_id,
            invoice_template_id=invoice_template.id,
            payment_data=getattr(data, "payment_data", [])
        )

        return await invoice_template_to_admin_schema(invoice_template)
