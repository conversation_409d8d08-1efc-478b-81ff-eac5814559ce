from abc import ABC

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BaseInvoiceTemplateValidateError(ErrorWithHTTPStatus, ABC, base=True):
    groups = ["invoice template"]

class TerminalCurrencyError(BaseInvoiceTemplateValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin invalid currency for incust terminal"

class ProfileLoyaltyError(BaseInvoiceTemplateValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin not loyalty for incust terminal"

class IncustTerminalUnAuthError(BaseInvoiceTemplateValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "admin invalid api incust terminal"