import logging
from dataclasses import asdict
from typing import Any

import schemas
from api.admin.helpers import get_translations_schemas_dict
from api.admin.router.core.funcs import payment_provider_data_to_schema
from api.admin.router.invoice_templates.functions import (
    invoice_template_item_to_admin_schema, invoice_template_items_to_admin_schema,
    invoice_template_to_admin_schema, validate_data,
)
from api.admin.router.payment_settings.exceptions import AdminPaymentValidationError
from api.admin.router.payment_settings.validator import PaymentSettingsValidator
from core.auth.services.scopes_checker import ScopesCheckerService
from core.payment.funcs import get_payment_default_name
from db import crud
from db.models import Group, InvoiceTemplate, PaymentSettings, User
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class InvoiceTemplatesService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass

    async def invoice_template_to_schema(
            self, invoice_template: InvoiceTemplate
    ) -> schemas.AdminInvoiceTemplateResponse:
        return await invoice_template_to_admin_schema(
            invoice_template,
        )

    async def get_invoice_template(
            self,
            invoice_template_id: int,
    ) -> InvoiceTemplate:
        invoice_template = await crud.get_invoice_template(
            invoice_template_id, self.profile_id
        )
        if not invoice_template:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "profile:read", self.available_data,
            )

        return invoice_template

    async def update_invoice_template(
            self,
            invoice_template_id: int,
            data: schemas.AdminUpdateInvoiceTemplateSchema
    ) -> schemas.AdminInvoiceTemplateResponse:
        await self.get_invoice_template(invoice_template_id)

        brand, incust_terminal_id = await validate_data(self.profile_id, data)
        if incust_terminal_id:
            data.incust_terminal_id=incust_terminal_id

        updated_invoice = await crud.update_invoice_template(
            invoice_template_id, data, self.profile_id
        )

        if getattr(data, "payment_data", []):
            for payment_data in data.payment_data:
                try:
                    payment_settings = await PaymentSettings.get(
                        payment_data.payment_settings_id
                    )
                    is_fiscal = False
                    json_data = None
                    if getattr(payment_data, "json_data", None) and getattr(
                            payment_data.json_data, "data", None
                    ):
                        json_data = payment_data.json_data.data
                    if json_data and payment_settings.payment_method == "liqpay":
                        is_fiscal = getattr(json_data, "is_need_fiscal", False)

                    validator = PaymentSettingsValidator(
                        self.lang, payment_settings.payment_method, brand.id, None,
                        json_data.dict() if json_data else None,
                        is_update=True, is_store=True, is_fiscal=is_fiscal,
                    )
                    processed_json_data = await validator.validate()
                    if json_data and processed_json_data:
                        payment_data.json_data.data = processed_json_data
                except Exception as ex:
                    logging.error(ex, exc_info=True)
                    raise AdminPaymentValidationError()

        await crud.update_object_payment_settings_for_object(
            self.profile_id,
            invoice_template_id=invoice_template_id,
            payment_data=getattr(data, "payment_data", [])
        )
        return await self.invoice_template_to_schema(updated_invoice)

    async def delete_invoice_template(self, invoice_template_id: int):
        invoice_template = await self.get_invoice_template(invoice_template_id)

        await invoice_template.update(is_deleted=True)
        return schemas.OkResponse()

    async def create_invoice_template(self, data: schemas.AdminCreateInvoiceTemplate):
        invoice_template = await crud.create_invoice_template(
            data, self.profile_id, self.user
        )

        return invoice_template_to_admin_schema(
            invoice_template
        )

    async def get_invoice_template_items(self, invoice_template_id: int):
        items = await crud.get_invoice_template_items(invoice_template_id, 'list') or []

        return await invoice_template_items_to_admin_schema(items)

    async def create_invoice_template_item(
            self, invoice_template_id: int,
            data: schemas.AdminCreateInvoiceTemplateItemSchema
    ) -> schemas.AdminInvoiceTemplateItemSchema:
        invoice_template = await crud.get_invoice_template(
            invoice_template_id, self.profile_id
        )
        if not invoice_template:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "profile:read", self.available_data,
            )

        item = await crud.create_invoice_template_item(invoice_template_id, data)

        return await invoice_template_item_to_admin_schema(item)

    async def update_invoice_template_item(
            self,
            invoice_template_id: int,
            invoice_template_item_id: int,
            data: schemas.AdminUpdateInvoiceTemplateItemSchema
    ) -> schemas.AdminInvoiceTemplateItemSchema:
        await self.get_invoice_template_item(
            invoice_template_id, invoice_template_item_id
        )

        if data.price is not None:
            data.price = round(data.price * 100)

        item = await crud.update_invoice_template_item(invoice_template_item_id, data)
        return await invoice_template_item_to_admin_schema(item)

    async def get_invoice_template_item(self, _: int, invoice_template_item_id: int):
        item = await crud.get_invoice_template_item(invoice_template_item_id)
        if not item:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "profile:read", self.available_data,
            )

        return item

    async def delete_invoice_template_item(
            self, invoice_template_id: int, invoice_template_item_id: int
    ):
        item = await self.get_invoice_template_item(
            invoice_template_id, invoice_template_item_id
        )

        await item.update(is_deleted=True)
        return schemas.OkResponse()

    async def get_invoice_template_payment_data(
            self, invoice_template_id: int,
    ) -> list[schemas.AdminInvoicePaymentInvoiceTemplateData]:

        invoice_template_payment_data = await crud.get_object_payment_data(
            self.profile_id, invoice_template_id=invoice_template_id
        )

        # Отримуємо групу для перекладів
        group = await Group.get(self.profile_id)

        result = []
        for pd, ps in invoice_template_payment_data:
            # Отримуємо переклади для ObjectPaymentSettings
            translations = await get_translations_schemas_dict(
                pd, group, schemas.ObjectPaymentSettingsTranslationSchema
            )
            
            result.append(schemas.AdminInvoicePaymentInvoiceTemplateData(
                invoice_template_id=invoice_template_id,
                payment_settings_id=ps.id,
                is_enabled=pd.is_enabled,
                json_data=payment_provider_data_to_schema(
                    ps.payment_method, pd.json_data
                ) if pd.json_data else None,
                name=ps.name,
                default_name=await get_payment_default_name(
                    ps.payment_method, self.lang, ps.is_online,
                    ewallet_id=ps.json_data.get(
                        "ewallet_id", None
                    ) if ps.json_data else None
                ),
                post_payment_info=pd.post_payment_info,
                translations=translations if translations else None,
            ))
        
        return result
