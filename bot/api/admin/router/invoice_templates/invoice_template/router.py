from fastapi import APIRouter, Depends, Path

import schemas
from .service import InvoiceTemplatesService
from ..functions import invoice_template_item_to_admin_schema

router = APIRouter(
    prefix="/{invoice_template_id}"
)


@router.get("/")
async def get_invoice_template(
        invoice_template_id: int = Path(..., description="ID invoice template"),
        service: InvoiceTemplatesService = Depends()
) -> schemas.AdminInvoiceTemplateResponse:
    invoice_template = await service.get_invoice_template(invoice_template_id)

    return await service.invoice_template_to_schema(invoice_template)


@router.patch("/")
async def update_invoice_template(
        data: schemas.AdminUpdateInvoiceTemplateSchema,
        invoice_template_id: int = Path(..., description="ID invoice template"),
        service: InvoiceTemplatesService = Depends()
) -> schemas.AdminInvoiceTemplateResponse:
    return await service.update_invoice_template(invoice_template_id, data)


@router.delete("/")
async def delete_invoice_template(
        invoice_template_id: int = Path(..., description="ID invoice template"),
        service: InvoiceTemplatesService = Depends()
) -> schemas.OkResponse:
    return await service.delete_invoice_template(invoice_template_id)


@router.get("/items")
async def get_invoice_template_items(
        invoice_template_id: int = Path(..., description="ID invoice template"),
        service: InvoiceTemplatesService = Depends()
) -> list[schemas.AdminInvoiceTemplateItemSchema]:
    return await service.get_invoice_template_items(invoice_template_id)


@router.post("/items")
async def create_invoice_template_item(
        data: schemas.AdminCreateInvoiceTemplateItemSchema,
        invoice_template_id: int = Path(..., description="ID invoice template"),
        service: InvoiceTemplatesService = Depends()
) -> schemas.AdminInvoiceTemplateItemSchema:
    return await service.create_invoice_template_item(invoice_template_id, data)


@router.patch("/items/{invoice_template_item_id}")
async def update_invoice_template_item(
        data: schemas.AdminUpdateInvoiceTemplateItemSchema,
        invoice_template_id: int = Path(..., description="ID invoice template"),
        invoice_template_item_id: int = Path(..., description="ID invoice template item"),
        service: InvoiceTemplatesService = Depends()
) -> schemas.AdminInvoiceTemplateItemSchema:
    return await service.update_invoice_template_item(invoice_template_id, invoice_template_item_id, data)


@router.get("/items/{invoice_template_item_id}")
async def get_invoice_template_item(
        invoice_template_id: int = Path(..., description="ID invoice template"),
        invoice_template_item_id: int = Path(..., description="ID invoice template item"),
        service: InvoiceTemplatesService = Depends()
) -> schemas.AdminInvoiceTemplateItemSchema:
    invoice_template_item = await service.get_invoice_template_item(invoice_template_id, invoice_template_item_id)

    return await invoice_template_item_to_admin_schema(invoice_template_item)


@router.delete("/items/{invoice_template_item_id}")
async def delete_invoice_template_item(
        invoice_template_id: int = Path(..., description="ID invoice template"),
        invoice_template_item_id: int = Path(..., description="ID invoice template item"),
        service: InvoiceTemplatesService = Depends()
) -> schemas.OkResponse:
    return await service.delete_invoice_template_item(invoice_template_id, invoice_template_item_id)


@router.get("/payment_data")
async def get_invoice_template_payment_data(
        invoice_template_id: int = Path(..., description="ID invoice template"),
        service: InvoiceTemplatesService = Depends()
) -> list[schemas.AdminInvoicePaymentInvoiceTemplateData]:
    return await service.get_invoice_template_payment_data(invoice_template_id)