import schemas
from api.admin.router.invoice_templates.exceptions import (
    IncustTerminalUnAuthError, ProfileLoyaltyError,
)
from config import USE_LOCALISATION
from core.incust.exceptions import IncustTerminalUnauth
from core.loyalty.incust_api import incust
from db import crud
from db.models import Brand, InvoiceTemplate, InvoiceTemplateItem
from schemas import LoyaltySettingsData
from schemas.loyalty_settings import LoyaltySettingsSchema


async def invoice_template_to_admin_schema(
        invoice_template: InvoiceTemplate,
) -> schemas.AdminInvoiceTemplateResponse:
    image_media = await invoice_template.get_media()

    return schemas.AdminInvoiceTemplateResponse(
        id=invoice_template.id,
        title=invoice_template.title,
        description=invoice_template.description,
        currency=invoice_template.currency,
        payment_mode=invoice_template.payment_mode,
        comment_mode=invoice_template.comment_mode,
        comment_label_raw=(
            None
            if invoice_template.comment_label_raw == USE_LOCALISATION else
            invoice_template.comment_label_raw
        ),
        need_name=invoice_template.need_name,
        need_phone_number=invoice_template.need_phone_number,
        need_email=invoice_template.need_email,
        group_id=invoice_template.group_id,
        photo_url=image_media.url if image_media else None,
        disabled_qty=invoice_template.disabled_qty,
        disabled_loyalty=invoice_template.disabled_loyalty,
        task_id=invoice_template.task_id,
        incust_terminal_api_key=invoice_template.incust_terminal_api_key,
        incust_terminal_id=invoice_template.incust_terminal_id,
        plugins=invoice_template.plugins or [],
        product_code=invoice_template.product_code,
        need_auth=invoice_template.need_auth,
        max_bonuses_percent=invoice_template.max_bonuses_percent,
        **invoice_template.converted_sums,
    )


async def invoice_template_items_to_admin_schema(
        items: list[InvoiceTemplateItem],
):
    return [
        await invoice_template_item_to_admin_schema(item)
        for item in items
    ]


async def invoice_template_item_to_admin_schema(
        item: InvoiceTemplateItem,
):
    return schemas.AdminInvoiceTemplateItemSchema(
        id=item.id,
        name=item.name,
        price=item.price / 100,
        quantity=item.quantity,
        item_code=item.item_code,
        category=item.category,
        invoice_template_id=item.invoice_template.id
    )


async def validate_data(
        profile_id: int,
        data: schemas.AdminCreateInvoiceTemplate |
              schemas.AdminUpdateInvoiceTemplateSchema
) -> tuple[Brand, str | None]:
    incust_terminal_id = None
    brand = await crud.get_brand_by_group(profile_id)
    loyalty_settings_profile = await crud.get_loyalty_settings_for_context(
        "profile",
        LoyaltySettingsData(profile_id=profile_id),
    )
    
    if not loyalty_settings_profile and (data.incust_terminal_api_key or data.incust_terminal_id):
        raise ProfileLoyaltyError()

    loyalty_settings = LoyaltySettingsSchema.from_orm(loyalty_settings_profile)
    if data.incust_terminal_api_key:
        loyalty_settings.terminal_api_key = data.incust_terminal_api_key
    if data.incust_terminal_id:
        loyalty_settings.terminal_id = data.incust_terminal_id
    
    if loyalty_settings and not data.disabled_loyalty:
        try:
            async with incust.term.SettingsApi(loyalty_settings) as api:
                terminal_settings = await api.settings()

            if not terminal_settings:
                raise IncustTerminalUnAuthError()
            incust_terminal_id = terminal_settings.id
        except IncustTerminalUnauth:
            raise IncustTerminalUnAuthError()
        except Exception as e:
            raise e

    return brand, incust_terminal_id
