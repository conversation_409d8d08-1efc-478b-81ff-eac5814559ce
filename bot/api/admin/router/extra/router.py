from fastapi import APIRouter

import schemas
from core.card_numbers import CardNumbersService

router = APIRouter(
    prefix="/extra",
)


@router.post("/card_numbers")
async def generate_card_numbers(
        data: schemas.CardNumbersInputData,
) -> schemas.CardNumbersOutputData:
    service = CardNumbersService(**data.dict(exclude={"add_file"}))
    return schemas.CardNumbersOutputData(
        card_numbers=service.card_numbers,
        file_data=service.base64_str if data.add_file else None,
        file_name=service.file_name if data.add_file else None,
    )
