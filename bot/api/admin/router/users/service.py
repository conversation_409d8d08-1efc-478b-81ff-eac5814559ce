from datetime import datetime

from sqlalchemy import exists, select
from sqlalchemy.sql.selectable import Exists

import exceptions
import schemas
from api.admin.router.users.exceptions import (
    ActualOwnerRequiredError, CannotGrantScopeNotToCurrentProfileError,
    CannotSetExpireDatetimeMoreThanYourAccessExpireDatetimeError,
    RequiredFieldsAreMissedForScopeError,
    ScopeNotFoundOrDoNotHaveEnoughPermissionsError,
    ScopeObjectNotExistsOrNotOwnedByProfileError, SystemUserCannotDeleteItselfError,
    SystemUserNotExistsOrNotOwnerByProfileError,
)
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from core.auth.functions import create_user_access_token
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud, db_func, sess
from db.models import ClientBot, Group, Scope, Store, User, UserGroup, VirtualManager
from utils.scopes_map import scope_map


class ProfileUsersService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:admin",
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    def user_to_schema(self, user: User, scope_names: list[str] | None = None):
        schema = schemas.AdminProfileUser.from_orm(user)
        schema.scope_names = scope_names or []
        schema.is_own_system_user = (user.system_user_owned_by_profile_id ==
                                     self.profile_id)
        return schema

    @classmethod
    async def user_group_to_schema(
            cls, user_group: UserGroup, scope_names: list[str] | None = None,
            with_users: bool
            = False
    ):
        if not with_users:
            return schemas.UserGroupSchema.from_orm(user_group)

        schema = schemas.UserGroupSchemaWithUsers.from_orm(user_group)
        schema.scope_names = scope_names or []
        schema.users = [
            schemas.UserGroupUserSchema(
                **dict(zip(("id", "name", "photo", "photo_url"), el))
            )
            for el in await crud.get_user_group_users_list(user_group.id)
        ]
        return schema

    async def get_excluded_scopes(self, profile: Group | None = None):
        if self.user.is_superadmin:
            return []

        excluded_scopes = []

        if not profile:
            profile = await Group.get(self.profile_id)

        if (
                not profile.owner_id == self.user.id and
                not await crud.check_access_to_action(
                    "platform:admin", "user",
                    self.user.id
                )

        ):
            excluded_scopes.append("profile:admin")

        return excluded_scopes

    async def get_users_list(self, params: schemas.AdminGetUsersListParams):
        profile = await Group.get(self.profile_id)

        excluded_scopes = await self.get_excluded_scopes(profile)
        users_list = await crud.get_profile_users_list(
            self.profile_id, params,
            excluded_scopes, False, self.user.id,
            frozenset((self.user.id, profile.owner_id))
        )
        return [
            self.user_to_schema(
                user, [scope for scope in (scope_names_str or "").split(",") if scope]
            )
            for user, scope_names_str in users_list
        ]

    def validate_has_access_to_edit_scope_sync(
            self, scope: str,
            expire_check_datetime: datetime | None = ...
    ):
        if Group.is_exists_sync(self.profile_id, owner_id=self.user.id):
            return "owner"

        is_superadmin = crud.check_access_to_action_sync(
            "platform:admin", "user", self.user.id,
            expire_check_datetime=expire_check_datetime,
        )

        if scope == "profile:admin" and not is_superadmin:
            raise ActualOwnerRequiredError(self.profile_id)

        scope_available_data = {
            "profile_id": self.profile_id
        }

        if not crud.check_access_to_action_sync(
                "profile:admin",
                "user", self.user.id,
                scope_available_data,
                expire_check_datetime=expire_check_datetime,
        ):
            if (
                    scope == "profile:edit" and
                    not crud.check_access_to_action_sync(
                        "profile:admin",
                        "user", self.user.id,
                        scope_available_data,
                    )
            ):
                raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                    "profile:admin", scope_available_data,
                )
            raise CannotSetExpireDatetimeMoreThanYourAccessExpireDatetimeError(
                self.profile_id
            )

        return "owner" if is_superadmin else "admin"

    @db_func
    def validate_has_access_to_edit_scope(
            self, scope: str,
            expire_check_datetime: datetime | None = ...
    ):
        return self.validate_has_access_to_edit_scope_sync(scope, expire_check_datetime)

    @db_func
    def validate_scopes_for_grant(
            self,
            scopes: list[schemas.CreateScopeData],
            replace: bool = False,
            target: schemas.ScopeTarget | None = None,
            target_id: int | None = None,
    ):
        for scope in scopes:
            # set profile id to current if empty
            if not scope.profile_id:
                scope.profile_id = self.profile_id
            # validate profile_id is current profile
            elif scope.profile_id != self.profile_id:
                raise CannotGrantScopeNotToCurrentProfileError(self.profile_id)

            required_fields = scope_map.get_scope(scope.scope).required_fields

            # validator for missing fields
            missed_fields = []
            for required_field in required_fields:
                if not getattr(scope, required_field, None):
                    missed_fields.append(required_field)

            owner_or_admin = self.validate_has_access_to_edit_scope_sync(
                scope.scope, scope.expire_datetime
            )
            if owner_or_admin != "owner" and replace:
                if target is None and target_id:
                    raise ValueError(
                        "target and target_id is required when replace is True"
                    )

                if crud.check_access_to_action_sync(
                        "profile:admin",
                        target, target_id,
                        {"profile_id": self.profile_id},
                        with_groups=False,
                ):
                    raise ActualOwnerRequiredError(self.profile_id)

            if missed_fields:
                raise RequiredFieldsAreMissedForScopeError(scope.scope, missed_fields)

            # remove extra fields
            for field in Scope.OBJECT_ID_FIELDS:
                if field not in required_fields and getattr(scope, field, None):
                    setattr(scope, field, None)

            if scope.bot_id and not ClientBot.is_exists_sync(
                    scope.bot_id, group_id=scope.profile_id
            ):
                raise ScopeObjectNotExistsOrNotOwnedByProfileError(
                    "bot_id", scope.bot_id, scope.profile_id
                )

            if scope.vm_id and not VirtualManager.is_exists_sync(
                    scope.vm_id, group_id=scope.profile_id
            ):
                raise ScopeObjectNotExistsOrNotOwnedByProfileError(
                    "vm_id", scope.vm_id, scope.profile_id
                )

            if scope.store_id:
                stmt: Exists = exists()
                stmt = stmt.where(Store.id == scope.store_id)
                stmt = stmt.where(Store.brand.has(group_id=scope.profile_id))
                is_store_exits_in_profile = sess().scalar(select(stmt))
                if not is_store_exits_in_profile:
                    raise ScopeObjectNotExistsOrNotOwnedByProfileError(
                        "store_id", scope.store_id, scope.profile_id
                    )

    async def grant_scopes(
            self,
            target: schemas.ScopeTarget,
            target_id: int,
            scopes: list[schemas.CreateScopeData],
            replace: bool = False,
            need_validate: bool = True
    ):
        allowed_targets = ("user", "user_group")
        if target not in allowed_targets:
            raise exceptions.NotAllowedScopeTargetError(target, allowed_targets)

        if need_validate:
            await self.validate_scopes_for_grant(scopes, replace, target, target_id)
        return await crud.grant_scopes(
            target, target_id, scopes, replace, replace_for_data={
                "profile_id": self.profile_id,
            }
        )

    # noinspection PyMethodMayBeStatic
    def make_scopes_response(
            self,
            target: schemas.ScopeTarget,
            target_id: int,
            scopes: list[Scope],
    ):
        return schemas.ScopesResponse(
            target=target,
            target_id=target_id,
            scopes=scopes
        )

    async def get_scopes_response(
            self,
            target: schemas.ScopeTarget,
            target_id: int,
            with_expired: bool = False,
    ):
        scopes = await self.get_allowed_scopes(target, target_id, with_expired)
        return self.make_scopes_response(target, target_id, scopes)

    async def create_system_user(
            self, data: schemas.CreateSystemUserData
    ) -> schemas.SystemUserCreated:
        if data.scopes:
            await self.validate_scopes_for_grant(data.scopes)

        system_user = await crud.create_user(
            full_name=data.name,
            is_system_user=True,
            system_user_owned_by_profile_id=self.profile_id,
        )

        if data.scopes:
            scopes = await self.grant_scopes("user", system_user.id, data.scopes)
        else:
            scopes = []

        return schemas.SystemUserCreated(
            user=self.user_to_schema(system_user, [scope.scope for scope in scopes]),
            scopes=scopes,
            token=schemas.Token(
                token_type="bearer",
                token=create_user_access_token(
                    system_user.id, expire=data.token_expire_at
                )
            )
        )

    async def get_allowed_scopes(
            self,
            target: schemas.ScopeTarget,
            target_id: int,
            with_expired: bool = False,
    ):
        """Returns """
        scope_available_data = {"profile_id": self.profile_id}

        excluded_scopes = await self.get_excluded_scopes()
        scopes: list[Scope] = await crud.get_scopes(
            target, target_id,
            include_groups_scopes=False,
            with_expired=with_expired,
            exclude=excluded_scopes,
            **scope_available_data,
        )
        return scopes

    async def get_system_user(self, system_user_id: int) -> User:
        system_user = await crud.get_system_user(system_user_id, self.profile_id)
        if not system_user:
            raise SystemUserNotExistsOrNotOwnerByProfileError(
                system_user_id, self.profile_id
            )

        await self.get_allowed_scopes("user", system_user.id)

        return system_user

    async def update_system_user(
            self, system_user_id: int, data: schemas.UpdateSystemUserData
    ):
        data = data.dict(exclude_unset=True)

        system_user = await self.get_system_user(system_user_id)
        if data:
            await system_user.update(**data)

        scopes = await self.get_allowed_scopes("user", system_user_id)
        return self.user_to_schema(system_user, [scope.scope for scope in scopes])

    async def delete_system_user(self, system_user_id: int):
        system_user = await self.get_system_user(system_user_id)
        if system_user == self.user.id:
            raise SystemUserCannotDeleteItselfError(system_user_id)

        await system_user.set_status("deactivated")
        return schemas.OkResponse()

    async def generate_system_user_token(
            self, system_user_id: int, data: schemas.GenerateSystemUserTokenData
    ):
        system_user = await self.get_system_user(system_user_id)
        return schemas.SystemUserToken(
            user_id=system_user_id,
            token=schemas.Token(
                token_type="bearer",
                token=create_user_access_token(system_user.id, expire=data.expire_at)
            )
        )

    async def delete_scope(self, scope_id: int):
        scope = await Scope.get(scope_id, profile_id=self.profile_id)
        if scope:
            await self.validate_has_access_to_edit_scope(scope.scope)
            if await crud.delete_scope(scope_id, profile_id=self.profile_id):
                return True

        raise ScopeNotFoundOrDoNotHaveEnoughPermissionsError(scope_id)

    async def update_scope(self, scope_id: int, data: schemas.UpdateScopeData):
        scope = await Scope.get(scope_id, profile_id=self.profile_id)
        if not scope:
            raise ScopeNotFoundOrDoNotHaveEnoughPermissionsError(scope_id)

        data_to_update = data.dict(exclude_unset=True)
        if not data_to_update:
            return schemas.ScopeSchema.from_orm(scope)

        await self.validate_has_access_to_edit_scope(
            scope.scope, data_to_update.get("expire_datetime", ...)
        )

        await scope.update(data_to_update)
        return schemas.ScopeSchema.from_orm(scope)

    async def get_user_groups_list(self, params: schemas.AdminGetUserGroupsListParams):
        excluded_scopes = await self.get_excluded_scopes()
        groups = await crud.get_user_groups_list(
            "profile", self.profile_id, params, excluded_scopes
        )
        return [await self.user_group_to_schema(
            group, [scope for scope in (scope_names or "").split(",") if scope],
            with_users=True
        ) for group, scope_names in groups]

    async def create_user_group(self, data: schemas.CreateProfileUserGroupData):
        await self.validate_scopes_for_grant(data.scopes)

        user_group = await UserGroup.create(
            owner_type="profile",
            owner_profile_id=self.profile_id,
            **data.dict(exclude={"scopes", "users_ids"})
        )

        if data.users_ids:
            await crud.add_users_to_user_group(user_group.id, data.users_ids)

        if data.scopes:
            scopes = await self.grant_scopes("user_group", user_group.id, data.scopes)
        else:
            scopes = []

        return schemas.UserGroupCreated(
            user_group=await self.user_group_to_schema(user_group, with_users=True),
            scopes=scopes
        )

    async def get_user_group(self, user_group_id: int):
        user_group = await UserGroup.get(user_group_id)
        if not user_group:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "profile:admin",
                {
                    "profile_id": self.profile_id,
                }
            )

        await self.get_allowed_scopes("user_group", user_group.id)

        return user_group

    async def update_user_group(
            self, user_group_id: int, data: schemas.UpdateUserGroupData
    ):
        user_group = await self.get_user_group(user_group_id)

        data_to_update = data.dict(exclude={"scopes", "users_ids"}, exclude_unset=True)
        if data_to_update:
            await user_group.update(data_to_update)

        if data.users_ids:
            await crud.add_users_to_user_group(user_group.id, data.users_ids)

        if data.scopes:
            scopes = await self.grant_scopes(
                "user_group", user_group.id, data.scopes, replace=True
            )
        else:
            scopes = []
        print(user_group.id)

        return schemas.UserGroupUpdated(
            user_group=await self.user_group_to_schema(user_group, with_users=True),
            scopes=scopes
        )

    async def delete_user_group(self, user_group_id: int):
        user_group = await self.get_user_group(user_group_id)
        await self.grant_scopes('user_group', user_group_id, [], replace=True)
        return await crud.delete_user_group(user_group)

    async def add_users_to_user_group(
            self, user_group_id: int, users_to_add: list[int]
    ):
        user_group = await self.get_user_group(user_group_id)
        await crud.add_users_to_user_group(user_group.id, users_to_add)
        return await self.user_group_to_schema(user_group, with_users=True)

    async def delete_user_group_users(self, user_group_id: int, *users_ids: int):
        user_group = await self.get_user_group(user_group_id)
        await crud.delete_user_groups_users(user_group.id, users_ids)
        return await self.user_group_to_schema(user_group, with_users=True)

    async def get_users_to_add_to_profile(
            self,
            params: schemas.AdminGetUsersListParams,
    ) -> list[schemas.AdminUserForAddToProfileSchema]:
        excluded_scopes = await self.get_excluded_scopes()
        users_list = await crud.get_profile_users_list(
            self.profile_id, params, excluded_scopes, for_add=True
        )

        result = []

        for user, scope_names_str in users_list:
            schema = schemas.AdminUserForAddToProfileSchema.from_orm(user)
            scope_names = [scope for scope in scope_names_str.split(",") if
                           scope] if scope_names_str else None
            if scope_names:
                schema.is_in_profile = bool(scope_names)
                schema.scope_names = scope_names
            result.append(schema)
        return result
