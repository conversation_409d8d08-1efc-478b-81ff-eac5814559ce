from datetime import datetime
from typing import Sequence

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class CannotGrantScopeNotToCurrentProfileError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "cannot grant scope not to current profile error"

    def __init__(self, current_profile_id: int):
        super().__init__(
            detail_data={
                "error_code": "cannot_grant_scope_not_to_current_profile",
                "current_profile_id": current_profile_id,
            }
        )


class RequiredFieldsAreMissedForScopeError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "required fields are missed for scope error"

    def __init__(self, scope: str, missed_fields: Sequence[str]):
        super().__init__(
            scope=scope,
            missed_fields=", ".join(missed_fields),
            detail_data={
                "error_code": "scope_required_fields_missed",
                "scope": scope,
                "missed_fields": list(missed_fields),
            }
        )


class ScopeNotFoundOrDoNotHaveEnoughPermissionsError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "scope not found or do not have enough permissions error"

    def __init__(self, scope_id: int):
        super().__init__(
            scope_id=scope_id,
            detail_data={
                "error_code": "scope_not_found",
                "scope_id": scope_id,
            }
        )


class ScopeObjectNotExistsOrNotOwnedByProfileError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "scope object not exists or not owner by profile error"

    def __init__(self, field: str, object_id: int, current_profile_id: int):
        super().__init__(
            field=field,
            object_id=object_id,
            detail_data={
                "error_code": "scope_object_not_exists_or_not_owner_by_profile",
                "field": field,
                "object_id": object_id,
                "current_profile_id": current_profile_id,
            }
        )


class SystemUserNotExistsOrNotOwnerByProfileError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "system user not exists or not owner by profile error"

    def __init__(self, system_user_id: int, current_profile_id: int):
        super().__init__(
            system_user_id=system_user_id,
            detail_data={
                "error_code": "system_user_not_exists_or_not_owner_by_profile",
                "system_user_id": system_user_id,
                "current_profile_id": current_profile_id,
            }
        )


class SystemUserCannotDeleteItselfError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "system user cannot delete itself error"

    def __init__(self, system_user_id: int):
        super().__init__(
            system_user_id=system_user_id,
            detail_data={
                "error_code": "system_user_cannot_delete_itself",
                "system_user_id": system_user_id,
            }
        )


class ActualOwnerRequiredError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "actual owner required error"

    def __init__(self, profile_id: int):
        super().__init__(
            profile_id=profile_id,
            detail_data={
                "error_code": "actual_owner_required",
                "profile_id": profile_id,
            }
        )


class CannotSetExpireDatetimeMoreThanYourAccessExpireDatetimeError(ErrorWithHTTPStatus):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "cannot set expire datetime more than your access expire datetime error"

    def __init__(
            self, profile_id: int,
            expire_datetime: datetime | None = None
    ):
        super().__init__(
            profile_id=profile_id,
            expire_datetime=expire_datetime or "infinite",
            detail_data={
                "profile_id": profile_id,
                "expire_datetime": expire_datetime,
            }
        )
