from fastapi import APIRouter, Depends, Security, Body

import schemas
from .service import ProfileUsersService

router = APIRouter(
    tags=["users"],
    prefix="/{profile_id}/users",
)


@router.get("/")
async def get_profile_users(
        params: schemas.AdminGetUsersListParams = Depends(),
        service: ProfileUsersService = Depends()
) -> list[schemas.AdminProfileUser]:
    return await service.get_users_list(params)


@router.get("/add/search")
async def get_users_to_add_to_profile(
        params: schemas.AdminGetUsersListToAddParams = Depends(),
        service: ProfileUsersService = Depends()
) -> list[schemas.AdminUserForAddToProfileSchema]:
    return await service.get_users_to_add_to_profile(params)


@router.post("/system")
async def create_system_user(
        data: schemas.CreateSystemUserData,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.SystemUserCreated:
    return await service.create_system_user(data)


@router.patch("/system/{system_user_id}")
async def update_system_user(
        system_user_id: int,
        data: schemas.UpdateSystemUserData,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.AdminProfileUser:
    return await service.update_system_user(system_user_id, data)


@router.delete("/system/{system_user_id}")
async def delete_system_user(
        system_user_id: int,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.OkResponse:
    return await service.delete_system_user(system_user_id)


@router.post("/system/{system_user_id}/generateToken")
async def generate_system_user_token(
        system_user_id: int,
        data: schemas.GenerateSystemUserTokenData,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.SystemUserToken:
    return await service.generate_system_user_token(system_user_id, data)


@router.get("/groups")
async def get_user_groups(
        params: schemas.AdminGetUserGroupsListParams = Depends(),
        service: ProfileUsersService = Depends()
) -> list[schemas.UserGroupSchemaWithUsers]:
    return await service.get_user_groups_list(params)


@router.post("/groups")
async def create_user_group(
        data: schemas.CreateProfileUserGroupData,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.UserGroupCreated:
    return await service.create_user_group(data)


@router.get("/{user_group_id}")
async def get_user_group(
        user_group_id: int,
        service: ProfileUsersService = Depends()
) -> schemas.UserGroupSchemaWithUsers:
    user_group = await service.get_user_group(user_group_id)
    return await service.user_group_to_schema(user_group, with_users=True)


@router.patch("/{user_group_id}")
async def update_user_group(
        user_group_id: int,
        data: schemas.UpdateUserGroupData,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.UserGroupUpdated:
    return await service.update_user_group(user_group_id, data)


@router.delete("/{user_group_id}")
async def delete_user_group(
        user_group_id: int,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.OkResponse:
    await service.delete_user_group(user_group_id)
    return schemas.OkResponse()


@router.post("/{user_group_id}/users")
async def add_users_to_user_group(
        user_group_id: int,
        data: schemas.AddUsersToUserGroupData,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.UserGroupSchemaWithUsers:
    return await service.add_users_to_user_group(user_group_id, data.users)


@router.delete("/user_group_id/users")
async def delete_all_user_group_users(
        user_group_id: int,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.UserGroupSchemaWithUsers:
    return await service.delete_user_group_users(user_group_id)


@router.delete("/{user_group_id}/users/{user_id}")
async def delete_user_from_user_group(
        user_id: int,
        user_group_id: int,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.UserGroupSchemaWithUsers:
    return await service.delete_user_group_users(user_group_id, user_id)


@router.get("/scopes/{target}/{target_id}")
async def get_scopes(
        target: schemas.ScopeTarget,
        target_id: int,
        service: ProfileUsersService = Depends()
) -> schemas.ScopesResponse:
    return await service.get_scopes_response(target, target_id, True)


@router.post("/scopes/{target}/{target_id}")
async def add_scopes(
        target: schemas.ScopeTarget,
        target_id: int,
        scopes: list[schemas.CreateScopeData] = Body(),
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.ScopesResponse:
    await service.grant_scopes(target, target_id, scopes)
    return await service.get_scopes_response(target, target_id, True)


@router.put("/scopes/{target}/{target_id}")
async def set_scopes(
        target: schemas.ScopeTarget,
        target_id: int,
        scopes: list[schemas.CreateScopeData] = Body(),
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.ScopesResponse:
    scopes = await service.grant_scopes(target, target_id, scopes, replace=True)
    return service.make_scopes_response(target, target_id, scopes)


@router.delete("/scopes/{target}/{target_id}")
async def delete_all_scopes(
        target: schemas.ScopeTarget,
        target_id: int,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.ScopesResponse:
    await service.grant_scopes(target, target_id, [], replace=True)
    return service.make_scopes_response(target, target_id, [])


@router.delete("/scopes/{scope_id}")
async def delete_scope(
        scope_id: int,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.OkResponse:
    await service.delete_scope(scope_id)
    return schemas.OkResponse()


@router.patch("/scopes/{scope_id}")
async def update_scope(
        scope_id: int,
        data: schemas.UpdateScopeData,
        service: ProfileUsersService = Security(scopes=["me:write"])
) -> schemas.ScopeSchema:
    return await service.update_scope(scope_id, data)
