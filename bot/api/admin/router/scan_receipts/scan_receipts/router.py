from fastapi import APIRouter, Depends, Security

import schemas
from .service import ScanAdminService

router = APIRouter()


@router.get("/")
async def get_scan_receipts_settings(
        service: ScanAdminService = Depends()
) -> schemas.AdminScanReceiptsSettings:
    return await service.get_scan_receipts_settings()


@router.patch("/")
async def update_scan_receipts_settings(
        form_data: schemas.AdminScanReceiptsSettings,
        service: ScanAdminService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AdminScanReceiptsSettings:
    return await service.update_admin_scan_receipts_settings(form_data)


@router.post("/validate")
async def validate_scan_receipts_settings(
        data: schemas.AdminScanReceiptsSettings,
        service: ScanAdminService = Depends(),
) -> schemas.AdminScanReceiptsValidateSchema:
    return await service.validate_scan_receipts_settings(data)


@router.get("/countries")
def get_scan_receipts_countries(
        service: ScanAdminService = Depends(),
) -> list[schemas.ReceiptCountry]:
    return service.get_countries_with_names()
