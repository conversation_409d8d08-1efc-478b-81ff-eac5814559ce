from abc import ABC

from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class BaseScanReceiptsValidateError(ErrorWithHTTPStatus, ABC, base=True):
    groups = ["scan_receipts"]


class UnknownValidateIncustError(BaseScanReceiptsValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "scan receipts unknown validation error"

    def __init__(self, message: str):
        self.message = message
        super().__init__(message=message)


class CountryScanReceiptError(BaseScanReceiptsValidateError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "scan receipts country validation error"
