from typing import get_args

from psutils.country import Countries

import schemas
from api.admin.router.scan_receipts.scan_receipts.exceptions import CountryScanReceiptError
from api.admin.router.scan_receipts.scan_receipts.funcs import convert_scan_receipts_settings_to_schema
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Brand, User
from schemas import ReceiptCountry, ScanReceiptCountry


class ScanAdminService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_scan_receipts_settings(
            self,
            brand: Brand | None = None
    ) -> schemas.AdminScanReceiptsSettings:
        if not brand:
            brand = await Brand.get(group_id=self.profile_id)

        scan_receipts_settings = await crud.get_brand_settings_by_type(
            brand.id, 'scan_receipts_'
        )

        return convert_scan_receipts_settings_to_schema(scan_receipts_settings)

    async def update_admin_scan_receipts_settings(
            self,
            data: schemas.AdminScanReceiptsSettings | None,
    ) -> schemas.AdminScanReceiptsSettings:

        brand = await Brand.get(group_id=self.profile_id)

        await self.validate_scan_receipts_settings(data)

        await crud.update_brand_settings(brand.id, data, 'scan_receipts_')

        return await self.get_scan_receipts_settings(brand)

    @staticmethod
    async def validate_scan_receipts_settings(
            data: schemas.AdminScanReceiptsSettings | None,
    ) -> schemas.AdminScanReceiptsValidateSchema:

        if data.scan_receipts_country in get_args(ScanReceiptCountry):
            return schemas.AdminScanReceiptsValidateSchema(detail='success')

        raise CountryScanReceiptError()

    def get_countries_with_names(self) -> list[ReceiptCountry]:
        countries_instance = Countries()
        country_codes = get_args(ScanReceiptCountry)
        result = []
        for country_code in country_codes:
            result.append(
                ReceiptCountry(
                    code=country_code,
                    name=countries_instance.get_country_name(country_code, self.lang),
                )
            )
        return result
