import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from db.models import Brand, User


class OrderFieldsService:
    def __init__(
        self,
        scopes: ScopesCheckerService = ScopesCheckerService.depend(
            None,  # will be overridden in "write" routes
            "profile_id",
        ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_order_fields(
        self,
        brand: Brand | None = None
    ) -> schemas.AdminOrderFieldsSchema:
        if not brand:
            brand = await Brand.get(group_id=self.profile_id)

        return schemas.AdminOrderFieldsSchema(
            web_email_mode=schemas.OrderFieldSettingEnum(brand.web_email_mode) if brand.web_email_mode else None,
            web_phone_mode=schemas.OrderFieldSettingEnum(brand.web_phone_mode) if brand.web_phone_mode else None,
            web_order_name_mode=schemas.OrderNameSettingEnum(
                brand.web_order_name_mode
            ) if brand.web_order_name_mode else None,
            messanger_email_mode=schemas.OrderFieldSettingEnum(
                brand.messanger_email_mode
            ) if brand.messanger_email_mode else None,
            messanger_phone_mode=schemas.OrderFieldSettingEnum(
                brand.messanger_phone_mode
            ) if brand.messanger_phone_mode else None,
            messanger_order_name_mode=schemas.OrderNameSettingEnum(
                brand.messanger_order_name_mode
            ) if brand.messanger_order_name_mode else None,
            order_comment_mode=schemas.OrderFieldSettingEnum(
                brand.order_comment_mode
            ) if brand.order_comment_mode else None,
        )

    async def update_order_fields(
        self,
        data: schemas.AdminOrderFieldsSchema | None,
    ) -> schemas.AdminOrderFieldsSchema:

        brand = await Brand.get(group_id=self.profile_id)

        update_data = {}
        if data.web_email_mode is not None:
            update_data['web_email_mode'] = data.web_email_mode
        if data.web_phone_mode is not None:
            update_data['web_phone_mode'] = data.web_phone_mode
        if data.web_order_name_mode is not None:
            update_data['web_order_name_mode'] = data.web_order_name_mode
        if data.messanger_email_mode is not None:
            update_data['messanger_email_mode'] = data.messanger_email_mode
        if data.messanger_phone_mode is not None:
            update_data['messanger_phone_mode'] = data.messanger_phone_mode
        if data.messanger_order_name_mode is not None:
            update_data['messanger_order_name_mode'] = data.messanger_order_name_mode
        if data.order_comment_mode is not None:
            update_data['order_comment_mode'] = data.order_comment_mode

        if update_data:
            await brand.update(update_data)

        return await self.get_order_fields(brand)
