from fastapi import APIRouter, Depends, Security

import schemas
from .service import OrderFieldsService

router = APIRouter()


@router.get("/")
async def get_order_fields(
        service: OrderFieldsService = Depends()
) -> schemas.AdminOrderFieldsSchema:
    return await service.get_order_fields()


@router.patch("/")
async def update_order_fields(
        form_data: schemas.AdminOrderFieldsSchema,
        service: OrderFieldsService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AdminOrderFieldsSchema:
    return await service.update_order_fields(form_data)
