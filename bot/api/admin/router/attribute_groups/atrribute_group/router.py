from fastapi import APIRouter, Depends, Query, Security

import schemas
from .service import AttributeGroupService

router = APIRouter(
    prefix="/{attribute_group_id}"
)


@router.get("/")
async def get_attribute_group(
        service: AttributeGroupService = Depends()
) -> schemas.AdminAttributeGroupSchema:
    attribute_group = await service.get_attribute_group()
    return await service.attribute_group_to_schema(attribute_group)


@router.patch("/")
async def update_attribute_group(
        data: schemas.AdminUpdateAttributeGroupData,
        service: AttributeGroupService = Security(scopes=["me:write", "attribute_group:edit"])
) -> schemas.AdminAttributeGroupSchema:
    return await service.update_attribute_group(data)


@router.delete("/")
async def delete_attribute_group(
        service: AttributeGroupService = Security(scopes=["me:write", "attribute_group:edit"])
) -> schemas.OkResponse:
    return await service.delete_attribute_group()


@router.get("/products")
async def get_attribute_group_products(
        store_ids: list[int] | None = Query(None),
        search_text: str | None = Query(None),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        service: AttributeGroupService = Depends(),
) -> list[schemas.AdminProductListSchema]:
    return await service.get_attribute_group_products(store_ids, search_text, offset, limit)


@router.post("/products")
async def connect_products_to_attribute_group(
        data: schemas.AdminConnectProductsToObjectData,
        service: AttributeGroupService = Security(scopes=["me:write", "attribute_group:edit"])
) -> schemas.AdminProductsConnectedToObjectResult:
    return await service.connect_products_to_attribute_group(data)


@router.post("/products/disconnect")
async def delete_products_from_attribute_group(
        data: schemas.AdminDisconnectProductsData,
        service: AttributeGroupService = Security(scopes=["me:write", "attribute_group:edit"]),
) -> schemas.AdminProductsDisconnectedResult:
    return await service.disconnect_products_from_attribute_group(data)
