from dataclasses import asdict
from typing import Any

import schemas
from api.admin.helpers import validate_non_empty_fields_and_translations
from api.admin.router.attribute_groups.functions import attribute_group_to_admin_schema
from api.admin.router.products.functions import product_to_admin_list_schema
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import (
    AttributeGroupToProduct, Group, StoreAttributeGroup,
    StoreProduct,
    User,
)
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class AttributeGroupService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "attribute_group:read",  # will be overridden in "write" routes
                "profile_id",
                "attribute_group_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.attribute_group_id: int = scopes.data.attribute_group_id

    # noinspection PyMethodMayBeStatic
    async def attribute_group_to_schema(
            self, attribute_group: StoreAttributeGroup, profile: Group | None = None
    ):
        if not profile:
            profile = await Group.get(self.profile_id)
        return await attribute_group_to_admin_schema(
            attribute_group, profile, self.user.id
        )

    async def get_attribute_group(self) -> StoreAttributeGroup:
        attribute_group = await crud.get_attribute_group_by_id_and_profile_id(
            self.attribute_group_id, self.profile_id
        )
        if not attribute_group:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "attribute_group:read", self.available_data,
            )
        return attribute_group

    async def update_attribute_group(self, data: schemas.AdminUpdateAttributeGroupData):
        attribute_group = await self.get_attribute_group()
        profile = await Group.get(self.profile_id)

        data_only_set = data.dict(exclude_unset=True)
        validate_non_empty_fields_and_translations(
            data_only_set, attribute_group, "name"
        )
        await crud.update_attribute_group(
            attribute_group, data, profile.get_langs_list()
        )
        return await self.attribute_group_to_schema(attribute_group, profile)

    async def delete_attribute_group(self):
        attribute_group = await self.get_attribute_group()
        await attribute_group.delete()
        return schemas.OkResponse()

    async def get_attribute_group_products(
            self,
            store_ids: list[int] | None = None,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminProductListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        # to verify object exists
        attribute_group = await self.get_attribute_group()

        products = await crud.get_admin_products_list(
            self.profile_id,
            user_id=self.user.id,
            store_ids=store_ids,
            attribute_group_ids=[attribute_group.id],
            search_text=search_text,
            offset=offset,
            limit=limit,
            need_check_access=False,
        )

        return [
            await product_to_admin_list_schema(
                product, self.profile_id, self.user.id
            )
            for product in products
        ]

    async def connect_products_to_attribute_group(
            self, data: schemas.AdminConnectProductsToObjectData
    ):
        attribute_group = await self.get_attribute_group()

        products = await crud.get_and_validate_access_on_objects(
            "product", StoreProduct,
            data.products, self.user.id, self.profile_id,
        )

        await crud.connect_related_objects(
            attribute_group, "products", products, data.replace
        )
        return schemas.AdminProductsConnectedToObjectResult(
            replaced=data.replace,
            connected_products=[
                await product_to_admin_list_schema(
                    product, self.profile_id, self.user.id,
                )
                for product in products
            ]
        )

    async def disconnect_products_from_attribute_group(
            self, data: schemas.AdminDisconnectProductsData
    ):
        attribute_group = await self.get_attribute_group()

        await crud.disconnect_m2m_related_objects(
            AttributeGroupToProduct,
            "attribute_group_id", attribute_group.id,
            "product_id", data.products,
            data.disconnect_all,
        )
        return schemas.AdminProductsDisconnectedResult(
            is_all_deleted=data.disconnect_all,
            products_ids_disconnected=data.products or [] if not data.disconnect_all
            else None,
        )
