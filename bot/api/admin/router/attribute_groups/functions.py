import schemas
from api.admin.helpers import check_object_access, get_translations_schemas_dict
from db import crud
from db.models import Group, StoreAttributeGroup


async def attribute_group_to_admin_list_schema(
        attribute_group_or_row: StoreAttributeGroup, profile_id: int,
        user_id: int
):
    schema = schemas.AdminAttributeGroupListSchema.from_orm(attribute_group_or_row)

    schema.internal_name = getattr(
        attribute_group_or_row, '_internal_name', None
    ) or attribute_group_or_row.name

    if not hasattr(attribute_group_or_row, "read_allowed"):
        schema.read_allowed = await check_object_access(
            "attribute_group",
            attribute_group_or_row.id,
            profile_id, user_id,
            scope_name="read",
        )
    if not hasattr(attribute_group_or_row, "edit_allowed"):
        schema.edit_allowed = await check_object_access(
            "attribute_group",
            attribute_group_or_row.id,
            profile_id, user_id,
            scope_name="edit",
        )

    return schema


async def attribute_group_to_admin_schema(
        attribute_group: StoreAttributeGroup,
        profile: Group,
        user_id: int,
        read_allowed: bool = True
):

    edit_allowed = await check_object_access(
        "attribute_group", attribute_group.id,
        profile.id, user_id,
        scope_name="edit",
    )

    if edit_allowed:
        read_allowed = True
    elif read_allowed is None:
        read_allowed = await check_object_access(
            "attribute_group", attribute_group.id,
            profile.id, user_id,
            scope_name="read",
        )

    return schemas.AdminAttributeGroupSchema(
        id=attribute_group.id,
        attribute_group_id=attribute_group.attribute_group_id,
        name=attribute_group.name,
        external_id=attribute_group.external_id,
        external_type=attribute_group.external_type,
        position=attribute_group.position,
        # filters=await crud.get_attribute_group_filters(attribute_group.id),
        raw_internal_name=attribute_group.raw_internal_name,
        internal_name=attribute_group.internal_name or None,
        min=attribute_group.min,
        max=attribute_group.max,
        attributes=await crud.get_attribute_group_attributes(
            attribute_group.id,
            fields=("id", "name", "selected_by_default", "price_impact"),
            target="user",
            target_id=user_id,
            profile_id=profile.id,
            need_scopes_allowed=True,
        ),
        # Lang has not to be specified. No translations needed
        translations=await get_translations_schemas_dict(
            attribute_group, profile, schemas.AdminAttributeGroupTranslationSchema
        ),
        read_allowed=read_allowed,
        edit_allowed=edit_allowed,
    )
