from fastapi import APIRout<PERSON>, Depends, Query, Security

import schemas
from .service import AttributeGroupService

router = APIRouter()


@router.get("/")
async def get_attribute_groups_list(
        search_text: str | None = Query(None),
        exclude: list[int] | None = Query(None, description="array of attribute_groups ids to exclude"),
        include: list[int] | None = Query(None, description="array of attribute_groups ids to select only in response"),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        service: AttributeGroupService = Depends()
) -> list[schemas.AdminAttributeGroupListSchema]:
    return await service.get_attribute_groups_list(search_text, exclude, include, offset, limit)


@router.get("/total_count")
async def get__attribute_groups_total_count(
        store_ids: list[int] | None = Query(None),
        search_text: str | None = Query(None),
        service: AttributeGroupService = Depends()
) -> int:
    return await service.get_attribute_groups_total_count(store_ids, search_text)


@router.post("/")
async def create_attribute_group(
        data: schemas.AdminCreateAttributeGroupData,
        service: AttributeGroupService = Security(scopes=["me:write", "menu:create"])
) -> schemas.AdminAttributeGroupSchema:
    return await service.create_attribute_group(data)
