import schemas
from api.exceptions import APIListLimitError
from api.admin.helpers import get_or_create_brand
from api.admin.router.attribute_groups.functions import (
    attribute_group_to_admin_list_schema, attribute_group_to_admin_schema,
)

from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Group, User


class AttributeGroupService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_attribute_groups_list(
            self,
            search_text: str | None = None,
            exclude: list[int] | None = None,
            include: list[int] | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminAttributeGroupListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        attribute_groups = await crud.get_admin_attribute_groups_list(
            self.profile_id, self.user.id,
            search_text=search_text,
            exclude=exclude,
            include=include,
            offset=offset,
            limit=limit,
        ) or []

        return [await attribute_group_to_admin_list_schema(attribute_group, self.profile_id, self.user.id) for
                attribute_group in
                attribute_groups]

    async def get_attribute_groups_total_count(
            self,
            store_ids: list[int] | None = None,
            search_text: str | None = None,
    ) -> int:
        return await crud.get_admin_attribute_groups_list(
            self.profile_id, self.user.id,
            store_ids,
            search_text,
            is_count=True,
        )

    async def create_attribute_group(self, data: schemas.AdminCreateAttributeGroupData):
        profile = await Group.get(self.profile_id)
        brand = await get_or_create_brand(profile)

        attribute_group = await crud.create_attribute_group(brand, data, self.user)
        return await attribute_group_to_admin_schema(attribute_group, profile, self.user.id)
