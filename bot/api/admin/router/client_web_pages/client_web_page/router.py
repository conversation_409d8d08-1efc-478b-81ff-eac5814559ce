from fastapi import APIRouter, Depends, Security

import schemas
from .service import ClientWebPageService

router = APIRouter(
    prefix="/{client_web_page_id}",
)


@router.get("/")
async def get_client_web_page(
        service: ClientWebPageService = Depends()
) -> schemas.AdminClientWebPageOneSchema:
    client_web_page = await service.get_client_web_page()
    return await service.client_web_page_to_schema(client_web_page)


@router.patch("/")
async def update_client_web_page(
        data: schemas.AdminClientWebPageUpdateData,
        service: ClientWebPageService = Security(scopes=["menu:edit", "me:write"])
) -> schemas.AdminClientWebPageOneSchema:
    return await service.update_client_web_page(data)


@router.delete("/")
async def delete_client_web_page(
        service: ClientWebPageService = Security(scopes=["me:write", "menu:edit"])
) -> schemas.OkResponse:
    return await service.delete_client_web_page()
