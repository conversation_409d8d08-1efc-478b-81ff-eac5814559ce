from dataclasses import asdict
from typing import Any

import exceptions
import schemas
from api.admin.helpers import get_translations_schemas_dict
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import (ClientWebPage, Group, User)


class ClientWebPageService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile:read",  # will be overridden in "write" routes
                "profile_id",
                "client_web_page_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.client_web_page_id: int = scopes.data.client_web_page_id

    async def client_web_page_to_schema(
            self, client_web_page: ClientWebPage,

    ):
        profile = await Group.get(self.profile_id)

        translations = await get_translations_schemas_dict(
            client_web_page, profile, schemas.AdminClientWebPageTranslationSchema
        ),

        return schemas.AdminClientWebPageOneSchema(
            id=client_web_page.id,
            title=client_web_page.title,
            slug=client_web_page.slug,
            button_title=client_web_page.button_title,
            is_enabled=client_web_page.is_enabled,
            position=client_web_page.position,
            type=client_web_page.type,
            raw_internal_name=client_web_page.raw_internal_name,
            internal_name=client_web_page.internal_name,
            page_content=client_web_page.page_content,
            stores=client_web_page.stores,
            show_in_navbar=client_web_page.show_in_navbar,
            show_in_profile=client_web_page.show_in_profile,
            translations=translations[0] if translations else None,
            invoice_templates=client_web_page.invoice_templates or [],
            profile_id=client_web_page.group_id,
            custom_container_max_width=client_web_page.custom_container_max_width,
            container_max_width=client_web_page.container_max_width,
        )

    async def get_client_web_page(self) -> ClientWebPage:
        page = await crud.get_client_web_page_by_id_and_profile_id(
            self.client_web_page_id, self.profile_id
        )
        if not page:
            raise exceptions.ClientWebPageNotFoundError(self.client_web_page_id)
        return page

    async def update_client_web_page(self, data: schemas.AdminClientWebPageUpdateData):
        client_web_page = await self.get_client_web_page()

        # check if there are any previous special pages and disable them or remove
        # from objects
        await crud.check_and_disable_previous_special_pages(
            data.type, client_web_page.group_id, data.show_in_profile,
            data.stores or [],
            data.invoice_templates or []
        )

        updated_client_web_page = await crud.update_client_web_page(
            client_web_page, data
        )
        return await self.client_web_page_to_schema(updated_client_web_page)

    async def delete_client_web_page(self):
        client_web_page = await self.get_client_web_page()
        await crud.delete_client_web_page(client_web_page.id, self.profile_id)
        return schemas.OkResponse()
