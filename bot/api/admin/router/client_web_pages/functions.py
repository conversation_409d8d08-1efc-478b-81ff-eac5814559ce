from sqlalchemy.engine import Row

import schemas
from db.models import ClientWebPage


async def client_web_page_to_admin_list_schema(
        client_web_page_or_row: ClientWebPage | Row,
):
    return schemas.AdminClientWebPageListSchema.from_orm(client_web_page_or_row)


async def client_web_page_to_admin_schema(
        client_web_page_or_row: ClientWebPage,
):

    profile_id = client_web_page_or_row.profile_id if hasattr(
        client_web_page_or_row, "profile_id"
    ) else client_web_page_or_row.group_id

    schema = schemas.AdminClientWebPageOneSchema(
        id=client_web_page_or_row.id,
        title=client_web_page_or_row.title,
        slug=client_web_page_or_row.slug,
        button_title=client_web_page_or_row.button_title,
        is_enabled=client_web_page_or_row.is_enabled,
        show_in_profile=client_web_page_or_row.show_in_profile,
        position=client_web_page_or_row.position,
        type=client_web_page_or_row.type,
        internal_name=client_web_page_or_row.internal_name,
        stores=client_web_page_or_row.stores or [],
        invoice_templates=client_web_page_or_row.invoice_templates or [],
        profile_id=profile_id,
        container_max_width=client_web_page_or_row.container_max_width,
        custom_container_max_width=client_web_page_or_row.custom_container_max_width,
    )

    return schema
