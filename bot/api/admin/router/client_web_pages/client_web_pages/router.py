from fastapi import APIRouter, Depends, Query, Security

import schemas
from .service import ClientWebPagesService

router = APIRouter()


@router.get("/")
async def get_client_web_pages(
        store_ids: list[int] | None = Query(None),
        invoice_templates_ids: list[int] | None = Query(None),
        types: list[str] | None = Query(None),
        search_text: str | None = Query(None),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit client web pages. Max: 100"),
        service: ClientWebPagesService = Depends()
) -> list[schemas.AdminClientWebPageListSchema]:
    return await service.get_client_web_pages(
        store_ids, invoice_templates_ids, search_text, types, offset, limit,
    )


@router.post("/is_slug_exist/")
async def is_client_web_page_slug_exist(
        slug: str | None = None,
        exclude_web_page_id: int | None = None,
        service: ClientWebPagesService = Depends()
) -> bool:
    if not slug:
        return False
    return await service.is_client_web_page_slug_exist(slug, exclude_web_page_id)


@router.post('/generate_slug')
async def generate_client_web_page_slug(
        string: str,
        service: ClientWebPagesService = Depends()
) -> str:
    return await service.generate_client_web_page_slug(string)


@router.post("/")
async def create_client_web_page(
        data: schemas.AdminClientWebPageCreateData,
        service: ClientWebPagesService = Security(scopes=["me:write", "menu:create"])
) -> schemas.AdminClientWebPageOneSchema:
    return await service.create_client_web_page(data)
