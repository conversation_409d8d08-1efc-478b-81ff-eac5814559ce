from fastapi import APIRouter, Depends, Security

import schemas
from api.admin.router.profile_media.service import ProfileMediaService

router = APIRouter(
    prefix="/{profile_id}/media",
    tags=["media"]
)


@router.post("/")
async def upload_profile_media(
        data: schemas.UploadMediaData = Depends(),
        service: ProfileMediaService = Security(
            scopes=["profile_media:create", "me:write"]
        )
) -> schemas.MediaInfo:
    return await service.upload_profile_media(data)
