from dataclasses import asdict
from typing import Any

import schemas
from core.auth.services.scopes_checker import ScopesCheckerService
from core.media_manager import media_manager
from db.models import User


class ProfileMediaService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "profile_media:read",  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: Any = asdict(scopes.data)  # dataclass

    async def upload_profile_media(self, data: schemas.UploadMediaData):
        media = await media_manager.save_from_upload_file(data.file)
        return schemas.MediaInfo.from_orm(media)
