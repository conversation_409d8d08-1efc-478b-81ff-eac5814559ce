from dataclasses import asdict
from operator import attrgetter
from typing import Any

from fastapi import UploadFile

import schemas
from api.admin.exceptions import AdminFieldCannotBeEmptyError
from api.admin.functions import validate_object_id
from api.admin.helpers import (
    get_translations_schemas_dict, validate_floating_sum_options,
    validate_non_empty_fields_and_translations,
)
from api.admin.router.media.functions import process_save_media
from api.admin.router.products.functions import product_to_admin_schema
from core.auth.services.scopes_checker import ScopesCheckerService
from core.helpers import get_next_object_id
from core.loyalty.incust_api import incust
from core.media_manager import media_manager
from db import crud
from db.crud import get_and_validate_access_on_objects
from db.models import (
    AttributeGroupToProduct, Brand, GalleryItem, Group, LoyaltySettings, MediaObject,
    Store, StoreAttributeGroup, StoreCategory, StoreCharacteristic,
    StoreCharacteristicValue, StoreProduct, User,
)
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from utils.text import f


class ProductService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "product:read",  # will be overridden in "write" routes
                "profile_id",
                "product_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.product_id: int = scopes.data.product_id

    async def product_to_schema(
            self, product: StoreProduct,
            image_media: MediaObject | None = None,
            thumbnail_media: MediaObject | None = None,

    ):
        profile = await Group.get(self.profile_id)
        return await product_to_admin_schema(
            product, profile, self.user.id,
            image_media, thumbnail_media,
        )

    async def get_product(self) -> StoreProduct:
        product = await crud.get_product_by_id_and_profile_id(
            self.product_id, self.profile_id
        )
        if not product:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "product:read", self.available_data,
            )
        return product

    async def copy_product(self):
        product = await self.get_product()
        profile = await Group.get(self.profile_id)
        brand = await Brand.get(product.brand_id)

        if not product:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "product:read", self.available_data,
            )

        internal_product_id = await get_next_object_id(
            StoreProduct, self.profile_id
        )

        translations = await get_translations_schemas_dict(
            product, profile, schemas.AdminProductTranslationSchema
        )

        copied_product = await crud.copy_product(
            product, self.user, self.profile_id, brand, internal_product_id,
            translations
        )

        await crud.reorder_object(StoreProduct, copied_product.id, 0, brand.id)

        return copied_product

    async def update_product(self, data: schemas.AdminUpdateProductData):
        if data.need_update_stores:
            payload = schemas.AdminConnectStoresToProductData(
                stores=data.stores or [],
                replace=True,
            )
            await self.connect_stores_to_product(payload)
            del data.stores
            del data.need_update_stores

        if not data.product_id:
            raise AdminFieldCannotBeEmptyError(
                await f("admin forms product id field", lang=self.lang)
            )

        await validate_object_id(
            StoreProduct, self.profile_id, data.product_id, self.product_id
        )

        product = await self.get_product()

        image_media = await process_save_media(
            data,
            "image_url",
            "media_id"
        )

        validate_non_empty_fields_and_translations(data, product, "name", "type")
        floating_sum_options = validate_floating_sum_options(
            data.floating_sum_options_str, product.product_id,
            product.name
        )
        data.floating_sum_options = floating_sum_options

        profile = await Group.get(self.profile_id)
        if data.type == "topup" and data.topup_server_api_url and data.topup_terminal_api_key:
            temp_settings = LoyaltySettings(
                terminal_api_key=data.topup_terminal_api_key,
                server_url=data.topup_server_api_url,
            )
            
            async with incust.term.SettingsApi(temp_settings, lang=self.lang) as api:
                terminal_settings = await api.settings()

            if terminal_settings and hasattr(terminal_settings, 'loyalty_settings') and terminal_settings.loyalty_settings:
                data.incust_terminal_id = terminal_settings.loyalty_settings.id if hasattr(terminal_settings.loyalty_settings, 'id') else None

        await crud.update_product(product, data, profile.get_langs_list())

        return await self.product_to_schema(product, image_media)

    async def update_product_image(self, file: UploadFile):
        product = await self.get_product()
        media = await media_manager.save_from_upload_file(file)

        await product.update(media_id=media.id, thumbnail_media_id=None)
        return await self.product_to_schema(product)

    async def delete_product_image(self):
        product = await self.get_product()
        await product.update(media_id=None, thumbnail_media_id=None)
        return await self.product_to_schema(product)

    async def delete_product(self):
        product = await self.get_product()
        await product.update(is_deleted=True)
        return schemas.OkResponse()

    async def get_product_attribute_groups(self) -> list[
        schemas.AdminAttributeGroupListSchema]:

        attribute_groups = await crud.get_admin_attribute_groups_list(
            self.profile_id,
            self.user.id,
            self.product_id,
        )

        return [schemas.AdminAttributeGroupListSchema.from_orm(ag) for ag in
                attribute_groups]

    async def connect_attribute_groups_to_product(
            self, data: schemas.AdminConnectAttributeGroupsData
    ):
        product = await self.get_product()

        attribute_groups = await crud.get_and_validate_access_on_objects(
            "attribute_group", StoreAttributeGroup,
            data.attribute_groups, self.user.id, self.profile_id,
        ) or []

        await crud.connect_related_objects(
            product, "attribute_groups", attribute_groups, data.replace
        )
        return await self.get_product_attribute_groups()

    async def disconnect_attribute_groups_from_product(
            self, data: schemas.AdminDisconnectAttributeGroupsData
    ):
        product = await self.get_product()

        await crud.disconnect_m2m_related_objects(
            AttributeGroupToProduct,
            "product_id", product.id,
            "attribute_group_id", data.attribute_groups,
            data.disconnect_all,
        )
        return await self.get_product_attribute_groups()

    async def get_product_stores(self):
        stores_data = await crud.get_admin_product_stores_list(
            self.profile_id, self.product_id, self.user.id
        )
        return [schemas.AdminProductStoreData.from_orm(obj) for obj in stores_data]

    async def connect_stores_to_product(
            self, data: schemas.AdminConnectStoresToProductData
    ):
        product = await self.get_product()

        stores = await get_and_validate_access_on_objects(
            "store", Store,
            list(map(attrgetter("store_id"), data.stores)),
            self.user.id, self.profile_id, "edit",
        )

        await crud.connect_stores_to_product(
            self.profile_id, self.user.id, product, stores, data
        )
        return await self.get_product_stores()

    async def get_product_categories(self):
        categories_data = await crud.get_admin_categories_list(
            self.profile_id,
            self.user.id,
            product_id=self.product_id
        )
        return [schemas.AdminCategoryListSchema.from_orm(obj) for obj in
                categories_data]

    async def connect_categories_to_product(
            self, data: schemas.AdminConnectCategoriesToProductData
    ):
        product = await self.get_product()

        categories = await get_and_validate_access_on_objects(
            "category", StoreCategory,
            data.categories,
            self.user.id, self.profile_id, "edit",
        )

        await crud.connect_categories_to_product(
            self.profile_id,
            self.user.id,
            product,
            categories or [],
            data.replace
        )
        return await self.get_product_categories()

    async def get_product_characteristics(self) -> list[
        schemas.AdminCharacteristicWithValueListSchema
    ]:
        product = await self.get_product()
        profile = await Group.get(self.profile_id)

        characteristics = await crud.get_admin_product_characteristics_list(
            self.profile_id, self.user.id, product.id
        )

        characteristic_list = []

        for obj in characteristics:
            characteristic_schema = (
                schemas.AdminCharacteristicWithValueListSchema.from_orm(
                    obj
                ))

            characteristic_value = StoreCharacteristicValue()
            characteristic_value.id = obj.value_id
            characteristic_value.value = obj.value

            translations = await get_translations_schemas_dict(
                characteristic_value, profile,
                schemas.AdminCharacteristicValueTranslationSchema
            )

            characteristic_schema.translations = translations

            characteristic_list.append(characteristic_schema)

        return characteristic_list

    async def connect_characteristics_to_product(
            self, data: schemas.AdminConnectCharacteristicsToProductData
    ):
        product = await self.get_product()

        characteristics = await get_and_validate_access_on_objects(
            "characteristic", StoreCharacteristic,
            list(map(attrgetter("characteristic_id"), data.characteristics)),
            self.user.id, self.profile_id, "edit",
        )
        await crud.connect_characteristics_to_product(
            self.profile_id, self.user.id, product, characteristics, data
        )
        return await self.get_product_characteristics()

    async def add_photo_to_gallery(self, data: schemas.AdminProductGalleryItemSchema):
        product = await self.get_product()

        media = await media_manager.save_from_upload_file(data.file)

        await crud.add_product_photo_to_gallery(
            media.id, product.id, product.gallery_id, data.position
        )

        return await self.product_to_schema(product)

    async def update_image_in_gallery(
            self, galley_item_id: int, data: schemas.AdminProductGalleryItemSchema
    ):
        gallery_item = await GalleryItem.get(id=galley_item_id)

        if data.file:
            media = await media_manager.save_from_upload_file(data.file)
        else:
            media = await MediaObject.get(gallery_item.media_id)

        await crud.update_image_in_gallery(gallery_item.id, media.id, data.position)
        return schemas.OkResponse()

    async def update_position_images_in_gallery(self, images_ids: list[int]):
        await crud.update_position_images_in_gallery(images_ids)
        return schemas.OkResponse()

    async def delete_photo_from_gallery(self, gallery_item_id: int):
        product = await self.get_product()

        await crud.delete_product_photo_from_gallery(
            gallery_item_id, product.gallery_id
        )
        return schemas.OkResponse()
