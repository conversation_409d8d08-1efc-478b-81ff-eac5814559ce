from fastapi import APIRouter, Depends, Security, UploadFile

import schemas
from api.admin.router.products.product.service import ProductService

router = APIRouter(
    prefix="/{product_id}",
)


@router.get("/")
async def get_product(
        service: ProductService = Depends()
) -> schemas.AdminProductSchema:
    product = await service.get_product()
    return await service.product_to_schema(product)


@router.patch("/")
async def update_product(
        data: schemas.AdminUpdateProductData,
        service: ProductService = Security(scopes=["product:edit", "me:write"])
) -> schemas.AdminProductSchema:
    return await service.update_product(data)


@router.post("/copy_product")
async def copy_product(
        service: ProductService = Security(scopes=["product:edit", "me:write"])
) -> schemas.AdminProductSchema:
    product = await service.copy_product()
    return await service.product_to_schema(product)


@router.post("/update_image")
async def update_product_image(
        file: UploadFile,
        service: ProductService = Security(scopes=["product:edit", "me:write"])
) -> schemas.AdminProductSchema:
    return await service.update_product_image(file)


@router.delete("/delete_image")
async def delete_product_image(
        service: ProductService = Security(scopes=["product:edit", "me:write"])
) -> schemas.AdminProductSchema:
    return await service.delete_product_image()


@router.delete("/")
async def delete_product(
        service: ProductService = Security(scopes=["me:write", "product:edit"])
) -> schemas.OkResponse:
    return await service.delete_product()


@router.get("/attribute_groups")
async def get_product_attribute_groups(
        service: ProductService = Depends()
) -> list[schemas.AdminAttributeGroupListSchema]:
    return await service.get_product_attribute_groups()


@router.post("/attribute_groups")
async def connect_attribute_groups_to_product(
        data: schemas.AdminConnectAttributeGroupsData,
        service: ProductService = Security(scopes=["me:write", "product:edit"])
) -> list[schemas.AdminAttributeGroupListSchema]:
    return await service.connect_attribute_groups_to_product(data)


@router.post("/attribute_groups/disconnect")
async def disconnect_attribute_groups_from_product(
        data: schemas.AdminDisconnectAttributeGroupsData,
        service: ProductService = Security(scopes=["me:write", "product:edit"])
) -> list[schemas.AdminAttributeGroupListSchema]:
    return await service.disconnect_attribute_groups_from_product(data)


@router.get("/stores")
async def get_product_stores(
        service: ProductService = Depends(),
) -> list[schemas.AdminProductStoreData]:
    return await service.get_product_stores()


@router.post("/stores")
async def connect_stores_to_product(
        data: schemas.AdminConnectStoresToProductData,
        service: ProductService = Security(scopes=["me:write"])
) -> list[schemas.AdminProductStoreData]:
    return await service.connect_stores_to_product(data)


@router.get("/categories")
async def get_product_categories(
        service: ProductService = Depends(),
) -> list[schemas.AdminCategoryListSchema]:
    return await service.get_product_categories()


@router.post("/categories")
async def connect_categories_to_product(
        data: schemas.AdminConnectCategoriesToProductData,
        service: ProductService = Security(scopes=["me:write"])
) -> list[schemas.AdminCategoryListSchema]:
    return await service.connect_categories_to_product(data)


@router.get("/characteristics")
async def get_product_characteristics(
        service: ProductService = Depends(),
) -> list[schemas.AdminCharacteristicWithValueListSchema]:
    return await service.get_product_characteristics()


@router.post("/characteristics")
async def connect_characteristics_to_product(
        data: schemas.AdminConnectCharacteristicsToProductData,
        service: ProductService = Security(scopes=["me:write"])
) -> list[schemas.AdminCharacteristicWithValueListSchema]:
    return await service.connect_characteristics_to_product(data)


@router.post("/gallery")
async def add_image_to_gallery(
        data: schemas.AdminProductGalleryItemSchema = Depends(),
        service: ProductService = Security(scopes=["product:edit", "me:write"])
) -> schemas.AdminProductSchema:
    return await service.add_photo_to_gallery(data)


@router.patch("/gallery/{gallery_item_id}")
async def update_image_in_gallery(
        gallery_item_id: int,
        data: schemas.AdminProductGalleryItemSchema = Depends(),
        service: ProductService = Security(scopes=["product:edit", "me:write"])
) -> schemas.OkResponse:
    return await service.update_image_in_gallery(gallery_item_id, data)


@router.delete("/gallery/{gallery_item_id}")
async def delete_image_from_gallery(
        gallery_item_id: int,
        service: ProductService = Security(scopes=["product:edit", "me:write"])
) -> schemas.OkResponse:
    return await service.delete_photo_from_gallery(gallery_item_id)


@router.patch("/gallery/images/position")
async def update_images_position_in_gallery(
        data: schemas.AdminProductGalleryImagesPositionSchema,
        service: ProductService = Security(scopes=["product:edit", "me:write"])
) -> schemas.OkResponse:
    return await service.update_position_images_in_gallery(data.images_ids)
