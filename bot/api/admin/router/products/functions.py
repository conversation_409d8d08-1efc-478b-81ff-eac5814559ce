from sqlalchemy.engine import Row

import schemas
from api.admin.helpers import (
    check_object_access, get_translations_schemas_dict,
)
from core.store.product.functions import (
    get_or_create_product_thumbnail_media,
    get_product_gallery,
)
from db import crud
from db.models import Group, MediaObject, StoreProduct


async def product_to_admin_list_schema(
        product_or_row: StoreProduct | Row,
        profile_id: int,
        user_id: int,
):
    schema = schemas.AdminProductListSchema.from_orm(product_or_row)
    schema.categories_str = product_or_row.categories_str
    if not schema.profile_id:
        schema.profile_id = profile_id

    if schema.image_url and not schema.thumbnail_url:
        product = await StoreProduct.get(product_or_row.id)
        image_media = await product.get_media()
        thumbnail_media = await get_or_create_product_thumbnail_media(
            product, image_media
        )
        schema.thumbnail_url = thumbnail_media.url if thumbnail_media else None

    schema.price = round(schema.price / 100, 2)
    schema.old_price = round(schema.old_price / 100, 2) if schema.old_price else 0

    if schema.floating_sum_max:
        schema.floating_sum_max = round(schema.floating_sum_max / 100, 2)
    if schema.floating_sum_min:
        schema.floating_sum_min = round(schema.floating_sum_min / 100, 2)

    floating_sum_options_string_list = None
    if schema.floating_sum_options:
        floating_sum_options_string_list = [str(item) for item in
                                            schema.floating_sum_options]
    if floating_sum_options_string_list:
        schema.floating_sum_options_str = ", ".join(
            floating_sum_options_string_list
        ) if floating_sum_options_string_list else None

    if not hasattr(product_or_row, "read_allowed"):
        schema.read_allowed = await check_object_access(
            "product",
            product_or_row.id,
            profile_id, user_id,
            scope_name="read",
        )
    if not hasattr(product_or_row, "edit_allowed"):
        schema.edit_allowed = await check_object_access(
            "product",
            product_or_row.id,
            profile_id, user_id,
            scope_name="edit",
        )

    return schema


async def product_to_admin_schema(
        product: StoreProduct,
        profile: Group,
        user_id: int,
        image_media: MediaObject | None = None,
        thumbnail_media: MediaObject | None = None,
        store_id: int | None = None,
        read_allowed: bool | None = True,
):
    if not image_media:
        image_media = await product.get_media()
    if not thumbnail_media:
        thumbnail_media = await get_or_create_product_thumbnail_media(
            product, image_media
        )

    product_price = product.price
    product_old_price = product.old_price if product.old_price else 0

    if store_id:
        spot_prices = await crud.get_store_product_spot_price(store_id, product.id)
        if spot_prices:
            product_price, product_old_price = spot_prices

    edit_allowed = await check_object_access("product", product.id, profile.id, user_id)

    if edit_allowed:
        read_allowed = True
    elif read_allowed is None:
        read_allowed = await check_object_access(
            "product", product.id, profile.id, user_id, "read"
        )

    floating_sum_options_string_list = None
    if product.floating_sum_options:
        floating_sum_options_string_list = [str(item) for item in
                                            product.floating_sum_options]

    return schemas.AdminProductSchema(
        id=product.id,
        is_enabled=product.is_enabled,
        product_id=product.product_id,
        product_group_id=product.product_group_id,
        profile_id=profile.id,
        type=product.type,
        name=product.name,
        internal_name=product.internal_name,
        raw_internal_name=product.raw_internal_name,
        is_available=product.is_available,
        image_url=image_media.url if image_media else None,
        image_media_id=product.media_id,
        thumbnail_url=thumbnail_media.url if thumbnail_media else None,
        price=round(product_price / 100, 2),
        old_price=round(product_old_price / 100, 2),
        is_weight=product.is_weight,
        weight_unit=product.weight_unit,
        external_id=product.external_id,
        external_type=product.external_type,
        description=product.description,
        gallery=await get_product_gallery(product.id),
        buy_min_quantity=product.buy_min_quantity,
        position=product.position,
        floating_sum_enabled=product.floating_sum_enabled,
        floating_sum_min=round(product.floating_sum_min / 100, 2),
        floating_sum_max=round(product.floating_sum_max / 100, 2),
        floating_sum_options=product.floating_sum_options,
        floating_sum_options_str=", ".join(
            floating_sum_options_string_list
        ) if floating_sum_options_string_list else None,
        floating_sum_user_sum_enabled=product.floating_sum_user_sum_enabled,
        floating_qty_enabled=product.floating_qty_enabled,
        stores=await crud.get_product_stores(
            product.id, ("id", "name"),
            "user", user_id,
            profile.id,
            need_scopes_allowed=True,
        ),
        categories=await crud.get_product_categories(
            product.id,
            fields=("id", "name"),
            target="user",
            target_id=user_id,
            profile_id=profile.id,
            need_scopes_allowed=True,
        ),
        attribute_groups=await crud.get_admin_attribute_groups_list(
            profile.id,
            user_id,
            product.id,
        ),
        pti_info_link=product.pti_info_link,
        pti_info_text=product.pti_info_text,
        translations=await get_translations_schemas_dict(
            product, profile, schemas.AdminProductTranslationSchema
        ),
        read_allowed=read_allowed,
        edit_allowed=edit_allowed,
        liqpay_id=product.liqpay_id,
        liqpay_unit_name=product.liqpay_unit_name,
        liqpay_codifier=product.liqpay_codifier,
        liqpay_tax_list=product.liqpay_tax_list,
        need_auth=product.need_auth,
        task_id=product.task_id,
        media_id=product.media_id or None,
        topup_account_id=product.topup_account_id,
        topup_server_api_url=product.topup_server_api_url,
        topup_terminal_api_key=product.topup_terminal_api_key,
        topup_enabled_card=product.topup_enabled_card,
        incust_terminal_id=product.incust_terminal_id,
    )


async def products_for_add_in_product_group_schema(products):
    result = []
    for product in products:
        logo_url = None
        if product.media_id:
            media = await MediaObject.get(product.media_id)
            logo_url = media.url
        result.append(
            schemas.AdminProductForAddInProductGroupSchema(
                id=product.id,
                name=product.name,
                internal_name=product.internal_name,
                logo_url=logo_url,
                read_allowed=product.read_allowed,
                edit_allowed=product.edit_allowed,
            )
        )

    return result


async def convert_to_admin_product_basic_info_list(products):
    product_info_list = [
        schemas.AdminProductBasicInfoSchema(
            id=product.id,
            product_id=product.id,
            name=product.internal_name,
            price=round(product.price / 100, 2),
            old_price=round(product.old_price / 100, 2) if product.old_price else 0,
        )
        for product in products
    ]
    return product_info_list
