from fastapi import APIRouter, Depends, Query, Security

import schemas
from core.api.depends import build_form_data_depend
from schemas import AdminValidateObjectIdSchema
from .service import ProductsService

router = APIRouter()


@router.get("/")
async def get_products_list(
        store_ids: list[int] | None = Query(None),
        category_ids: list[int] | None = Query(None),
        search_text: str | None = Query(None),
        product_type: str | None = Query(None),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        without_categories: bool = Query(
            False, description="return products without linked category"
        ),
        without_stores: bool = Query(
            False, description="return products without linked store"
        ),
        service: ProductsService = Depends()
) -> list[schemas.AdminProductListSchema]:
    return await service.get_products_list(
        store_ids, category_ids, search_text, offset, limit, without_categories,
        without_stores, product_type
    )


@router.get("/validate/{product_id}")
async def validate_product_id(
        product_id: str | None = None,
        service: ProductsService = Depends()
) -> AdminValidateObjectIdSchema:
    if not product_id:
        return AdminValidateObjectIdSchema(result=True)
    return await service.validate_product_id(product_id)


@router.get("/total_count")
async def get_products_total_count(
        store_ids: list[int] | None = Query(None),
        category_ids: list[int] | None = Query(None),
        search_text: str | None = Query(None),
        product_type: str | None = Query(None),
        without_categories: bool = Query(
            False, description="return products without linked category"
        ),
        without_stores: bool = Query(
            False, description="return products without linked store"
        ),
        service: ProductsService = Depends()
) -> int:
    return await service.get_products_total_count(
        store_ids, category_ids, search_text, without_categories,
        without_stores, product_type
    )


@router.post("/")
async def create_product(
        data: schemas.AdminProductCreateFormSchema = Depends(
            build_form_data_depend(
                schemas.AdminProductCreateFormSchema,
                ['categories', 'floating_sum_options', 'stores', 'gallery_files',
                 'gallery_media_items',
                 'products_group_modifiers', 'characteristics', 'attributes_groups'],
                {
                    "stores": ["price", "old_price", "store_id"],
                    "gallery_files": ['image', 'position'],
                    "gallery_media_items": ['media_id', 'position'],
                    "products_group_modifiers": ['modifier_id', 'modifier_value'],
                    "characteristics": ['characteristic_id', 'value', "translations"]
                }
            )
        ),
        service: ProductsService = Security(scopes=["me:write", "menu:create"])
) -> schemas.AdminProductSchema:
    return await service.create_product(data)


@router.get("/products_groups")
async def products_list_for_add_in_group(
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        search_text: str | None = Query(None),
        service: ProductsService = Depends()
) -> list[schemas.AdminProductForAddInProductGroupSchema]:
    return await service.products_for_add_in_products_group(offset, limit, search_text)
