import schemas
from api.admin.router.products.functions import (
    convert_to_admin_product_basic_info_list,
)
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.ext.types import SelectMode
from db import crud
from db.models import (
    User,
)


class ProductsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_all_products_basic_list(
            self, store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
            without_categories: bool = False,
            without_stores: bool = False,
            mode: SelectMode = SelectMode.SELECTED,
            included: list[int] | None = None,
            excluded: list[int] | None = None,
            product_type: str | None = None
    ):
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        products = await crud.get_all_products_basic_list(
            self.profile_id, self.user.id,
            store_ids=store_ids,
            category_ids=category_ids,
            search_text=search_text,
            offset=offset,
            limit=limit,
            without_categories=without_categories,
            without_stores=without_stores,
            mode=mode,
            included=included,
            excluded=excluded,
            product_type=product_type
        )
        return await convert_to_admin_product_basic_info_list(products)

    async def _get_products_ids(
            self,
            data: schemas.AdminProductsBaseMassActionSchema |
                  schemas.AdminProductsMassChangeStatusSchema,
            mode: SelectMode = SelectMode.SELECTED,
            store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            without_stores: bool = False,
            without_categories: bool = False,
            product_type: str | None = None
    ) -> list[int]:
        products_ids: list[int] = []
        if mode == SelectMode.SELECTED:
            products_ids = data.included or []
        elif mode == SelectMode.ALL:
            products = await crud.get_all_products_basic_list(
                self.profile_id, self.user.id,
                store_ids=store_ids,
                category_ids=category_ids,
                search_text=search_text,
                offset=None,
                limit=None,
                without_categories=without_categories,
                without_stores=without_stores,
                mode=mode,
                included=[],
                excluded=data.excluded or [],
                product_type=product_type
            )

            products_ids = [product.id for product in products]
        return products_ids

    async def add_products_to_store(
            self,
            data: schemas.AdminProductsAddToStorePricesSchema,
            store_id: int,
            mode: SelectMode = SelectMode.SELECTED,
            store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            without_stores: bool = False,
            without_categories: bool = False,
            product_type: str | None = None
    ):

        products: list[schemas.AdminProductAddToStoreSchema] = []
        if mode == SelectMode.SELECTED:
            products = [
                schemas.AdminProductAddToStoreSchema(
                    id=price["id"],
                    price=price["price"],
                    old_price=price.get("old_price")
                )
                for price in data.prices.values()
                if price["id"] in (data.included or [])
            ]
        elif mode == SelectMode.ALL:
            products = await crud.get_all_products_basic_list(
                self.profile_id, self.user.id,
                store_ids=store_ids,
                category_ids=category_ids,
                search_text=search_text,
                offset=None,
                limit=None,
                without_categories=without_categories,
                without_stores=without_stores,
                mode=mode,
                included=[],
                excluded=data.excluded,
                product_type=product_type
            )

        await crud.add_products_to_store(
            store_id, products, data.prices
        )

        return schemas.OkResponse()

    async def remove_products_from_store(
            self, data: schemas.AdminProductsBaseMassActionSchema,
            store_id: int,
            mode: SelectMode = SelectMode.SELECTED,
            store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            without_stores: bool = False,
            without_categories: bool = False,
            product_type: str | None = None
    ):

        products_ids = await self._get_products_ids(
            data, mode, store_ids, category_ids, search_text, without_stores,
            without_categories, product_type
        )

        await crud.remove_products_from_store(
            store_id, products_ids
        )

        return schemas.OkResponse()

    async def mass_delete_products(
            self, data: schemas.AdminProductsBaseMassActionSchema,
            mode: SelectMode = SelectMode.SELECTED,
            store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            without_stores: bool = False,
            without_categories: bool = False,
            product_type: str | None = None
    ):

        products_ids = await self._get_products_ids(
            data, mode, store_ids, category_ids, search_text, without_stores,
            without_categories, product_type
        )

        await crud.mass_delete_products(
            products_ids
        )

        return schemas.OkResponse()

    async def mass_change_active_status_products(
            self, data: schemas.AdminProductsMassChangeActiveStatusSchema,
            mode: SelectMode = SelectMode.SELECTED,
            store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            without_stores: bool = False,
            without_categories: bool = False,
            product_type: str | None = None
    ):

        products_ids = await self._get_products_ids(
            data, mode, store_ids, category_ids, search_text, without_stores,
            without_categories, product_type
        )

        await crud.mass_change_active_status_products(
            products_ids,
            data.status
        )

        return schemas.OkResponse()

    async def mass_change_status_products(
            self, data: schemas.AdminProductsMassChangeStatusSchema,
            mode: SelectMode = SelectMode.SELECTED,
            store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            without_stores: bool = False,
            without_categories: bool = False,
            product_type: str | None = None
    ):

        products_ids = await self._get_products_ids(
            data, mode, store_ids, category_ids, search_text, without_stores,
            without_categories, product_type
        )

        await crud.mass_change_status_products(
            products_ids,
            data.status
        )

        return schemas.OkResponse()

    async def add_products_to_category(
            self,
            data: schemas.AdminProductsBaseMassActionSchema,
            category_id: int,
            mode: SelectMode = SelectMode.SELECTED,
            store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            without_stores: bool = False,
            without_categories: bool = False,
            product_type: str | None = None
    ):

        products_ids = await self._get_products_ids(
            data, mode, store_ids, category_ids, search_text, without_stores,
            without_categories, product_type
        )

        await crud.add_products_to_category(
            products_ids, category_id
        )

        return schemas.OkResponse()

    async def remove_products_from_category(
            self, data: schemas.AdminProductsBaseMassActionSchema,
            category_id: int,
            mode: SelectMode = SelectMode.SELECTED,
            store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            without_stores: bool = False,
            without_categories: bool = False,
            product_type: str | None = None
    ):

        products_ids = await self._get_products_ids(
            data, mode, store_ids, category_ids, search_text, without_stores,
            without_categories, product_type
        )

        await crud.remove_products_from_category(
            products_ids, category_id
        )

        return schemas.OkResponse()
