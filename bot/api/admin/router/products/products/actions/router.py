from fastapi import APIRouter, Depends, Query, Security

import schemas
from core.ext.types import SelectMode
from .service import ProductsService

router = APIRouter()


@router.get("/all_products_basic_list")
async def get_all_products_basic_list(
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit products. Max: 100"),
        search_text: str | None = Query(None, description="search text for products"),
        product_type: str | None = Query(None, description="product type"),
        store_ids: list[int] | None = Query(
            None, description="store ids - filter by stores"
        ),
        category_ids: list[int] | None = Query(
            None, description="category ids - filter by categories"
        ),
        included: list[int] | None = Query(
            None, description="included products in result"
        ),
        excluded: list[int] | None = Query(
            None, description="excluded products from result"
        ),
        without_categories: bool = Query(
            False, description="without categories filter"
        ),
        without_stores: bool = Query(False, description="without stores filter"),
        mode: SelectMode = Query(SelectMode.SELECTED, description="selected or all"),
        service: ProductsService = Depends()
) -> list[schemas.AdminProductBasicInfoSchema]:
    return await service.get_all_products_basic_list(
        store_ids, category_ids, search_text, offset, limit,
        without_categories,
        without_stores, mode,
        included,
        excluded, product_type
    )


@router.post('/add_products_to_store/{store_id}')
async def add_products_to_store(
        store_id: int,
        payload: schemas.AdminProductsAddToStorePricesSchema,
        query_params: schemas.ProductsActionsQueryParams = Depends(),
        service: ProductsService = Security(scopes=["me:write", "product:edit"])
) -> schemas.OkResponse:
    return await service.add_products_to_store(
        payload, store_id, query_params.mode, query_params.store_ids,
        query_params.category_ids,
        query_params.search_text,
        query_params.without_stores,
        query_params.without_categories,
        query_params.product_type
    )


@router.delete('/remove_products_from_store/{store_id}')
async def remove_products_from_store(
        store_id: int,
        payload: schemas.AdminProductsBaseMassActionSchema,
        query_params: schemas.ProductsActionsQueryParams = Depends(),
        service: ProductsService = Security(scopes=["me:write", "product:edit"])
) -> schemas.OkResponse:
    return await service.remove_products_from_store(
        payload, store_id, query_params.mode, query_params.store_ids,
        query_params.category_ids,
        query_params.search_text, query_params.without_stores,
        query_params.without_categories,
        query_params.product_type
    )


@router.delete('/mass_delete_products')
async def mass_delete_products(
        payload: schemas.AdminProductsBaseMassActionSchema,
        query_params: schemas.ProductsActionsQueryParams = Depends(),
        service: ProductsService = Security(scopes=["me:write", "product:edit"])
) -> schemas.OkResponse:
    return await service.mass_delete_products(
        payload, query_params.mode, query_params.store_ids, query_params.category_ids,
        query_params.search_text, query_params.without_stores,
        query_params.without_categories,
        query_params.product_type
    )


@router.delete('/mass_change_active_status_products')
async def mass_change_active_status_products(
        payload: schemas.AdminProductsMassChangeActiveStatusSchema,
        query_params: schemas.ProductsActionsQueryParams = Depends(),
        service: ProductsService = Security(scopes=["me:write", "product:edit"])
) -> schemas.OkResponse:
    return await service.mass_change_active_status_products(
        payload, query_params.mode, query_params.store_ids, query_params.category_ids,
        query_params.search_text, query_params.without_stores,
        query_params.without_categories,
        query_params.product_type
    )


@router.patch('/mass_change_status_products')
async def mass_change_status_products(
        payload: schemas.AdminProductsMassChangeStatusSchema,
        query_params: schemas.ProductsActionsQueryParams = Depends(),
        service: ProductsService = Security(scopes=["me:write", "product:edit"])
) -> schemas.OkResponse:
    return await service.mass_change_status_products(
        payload, query_params.mode, query_params.store_ids, query_params.category_ids,
        query_params.search_text, query_params.without_stores,
        query_params.without_categories,
        query_params.product_type
    )


@router.post('/add_products_to_category/{category_id}')
async def add_products_to_category(
        category_id: int,
        payload: schemas.AdminProductsBaseMassActionSchema,
        query_params: schemas.ProductsActionsQueryParams = Depends(),
        service: ProductsService = Security(scopes=["me:write", "product:edit"])
) -> schemas.OkResponse:
    return await service.add_products_to_category(
        payload, category_id, query_params.mode, query_params.store_ids,
        query_params.category_ids,
        query_params.search_text, query_params.without_stores,
        query_params.without_categories,
        query_params.product_type
    )


@router.delete('/remove_products_from_category/{category_id}')
async def remove_products_from_category(
        category_id: int,
        payload: schemas.AdminProductsBaseMassActionSchema,
        query_params: schemas.ProductsActionsQueryParams = Depends(),
        service: ProductsService = Security(scopes=["me:write", "product:edit"])
) -> schemas.OkResponse:
    return await service.remove_products_from_category(
        payload, category_id, query_params.mode, query_params.store_ids,
        query_params.category_ids,
        query_params.search_text, query_params.without_stores,
        query_params.without_categories,
        query_params.product_type
    )
