import json
import logging
from operator import attrgetter

import schemas
from api.admin.exceptions import AdminFieldNotUnique
from api.admin.functions import validate_object_id
from api.admin.helpers import get_or_create_brand, validate_floating_sum_options
from api.admin.router.products.functions import (
    convert_to_admin_product_basic_info_list, product_to_admin_list_schema,
    product_to_admin_schema,
    products_for_add_in_product_group_schema,
)
from api.exceptions import APIListLimitError
from core.auth.services.scopes_checker import ScopesCheckerService
from core.helpers import get_next_object_id
from core.media_manager import media_manager
from db import crud
from db.models import (
    Group, Store, StoreAttributeGroup, StoreCategory,
    StoreCharacteristic, StoreProduct, User,
)
from schemas import AdminValidateObjectIdSchema
from utils.text import f


class ProductsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_products_list(
            self,
            store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
            without_categories: bool = False,
            without_stores: bool = False,
            product_type: str | None = None,
    ) -> list[schemas.AdminProductListSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        products = await crud.get_admin_products_list(
            self.profile_id, self.user.id,
            store_id=store_ids[0] if store_ids and len(store_ids) == 1 else None,
            store_ids=store_ids if store_ids and len(store_ids) > 1 else None,
            category_ids=category_ids,
            search_text=search_text,
            offset=offset,
            limit=limit,
            without_categories=without_categories,
            without_stores=without_stores,
            product_type=product_type
        )

        return [
            await product_to_admin_list_schema(product, self.profile_id, self.user.id)
            for product in products]

    async def get_products_total_count(
            self,
            store_ids: list[int] | None = None,
            category_ids: list[int] | None = None,
            search_text: str | None = None,
            without_categories: bool = False,
            without_stores: bool = False,
            product_type: str | None = None,
    ) -> int:
        return await crud.get_admin_products_list(
            self.profile_id, self.user.id,
            None, store_ids, category_ids, search_text,
            without_stores=without_stores,
            without_categories=without_categories,
            product_type=product_type,
            is_count=True,
        )

    async def create_product(self, data: schemas.AdminProductCreateFormSchema):
        profile = await Group.get(self.profile_id)
        brand = await get_or_create_brand(profile)

        characteristics_list = data.characteristics or []
        del data.characteristics
        attributes_groups_list = data.attributes_groups or []
        del data.attributes_groups
        gallery_items = data.gallery_media_items or []
        del data.gallery_media_items

        allowed_stores_list = await crud.get_and_validate_access_on_objects(
            "store", Store, list(map(attrgetter("store_id"), data.stores)),
            self.user.id, self.profile_id,
            scope_name="edit",
        )

        categories = await crud.get_and_validate_access_on_objects(
            "category", StoreCategory, data.categories,
            self.user.id, self.profile_id,
            # have to be read, because there is no edit access required for
            # connecting product to category
            scope_name="read",
        )

        if data.translations:
            data.translations = json.loads(data.translations)

        if data.image:
            image_media = await media_manager.save_from_upload_file(data.image)
            if image_media:
                data.media_id = image_media.id
            del data.image

        if data.gallery_files:
            for item in data.gallery_files:
                if item.image:
                    image_media = await media_manager.save_from_upload_file(item.image)
                    if image_media:
                        gallery_items.append(
                            schemas.AdminProductGalleryItemForCreate(
                                media_id=image_media.id, position=item.position
                            )
                        )
        del data.gallery_files

        floating_sum_options = validate_floating_sum_options(
            data.floating_sum_options_str, data.product_id,
            data.name
        )
        data.floating_sum_options = floating_sum_options

        if data.product_id:
            await validate_object_id(StoreProduct, self.profile_id, data.product_id)
        else:
            data.product_id = await get_next_object_id(StoreProduct, self.profile_id)

        product = await crud.create_product(
            brand, data, self.user, allowed_stores_list, categories, gallery_items
        )

        if characteristics_list:
            for characteristic in characteristics_list:
                if isinstance(characteristic.translations, str):
                    try:
                        characteristic.translations = json.loads(
                            characteristic.translations
                        )
                    except json.JSONDecodeError:
                        characteristic.translations = None

            characteristics = await crud.get_and_validate_access_on_objects(
                "characteristic", StoreCharacteristic,
                list(map(attrgetter("characteristic_id"), characteristics_list)),
                self.user.id, self.profile_id, "edit",
            )
            payload = schemas.AdminConnectCharacteristicsToProductData(
                characteristics=characteristics_list,
                replace=False
            )

            await crud.connect_characteristics_to_product(
                brand.group_id, self.user.id, product, characteristics, payload
            )

        if attributes_groups_list:
            attribute_groups = await crud.get_and_validate_access_on_objects(
                "attribute_group", StoreAttributeGroup,
                attributes_groups_list, self.user.id, self.profile_id,
            ) or []

            await crud.connect_related_objects(
                product, "attribute_groups", attribute_groups, False
            )

        await crud.reorder_object(StoreProduct, product.id, 0, brand.id)

        return await product_to_admin_schema(product, profile, self.user.id)

    async def products_for_add_in_products_group(
            self, offset: int, limit: int, search_text: str
    ) -> list[schemas.AdminProductForAddInProductGroupSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        products = await crud.get_products_without_products_group(
            self.profile_id, self.user.id, offset, limit,
            search_text
        )

        return await products_for_add_in_product_group_schema(products)

    async def validate_product_id(
            self, product_id: str,
    ) -> AdminValidateObjectIdSchema:
        try:
            await validate_object_id(StoreProduct, self.profile_id, product_id)
            return AdminValidateObjectIdSchema(result=True)
        except AdminFieldNotUnique as err:
            return AdminValidateObjectIdSchema(
                result=False, detail=await f(
                    err.text_variable,
                    lang=self.lang, **err.text_kwargs
                )
            )
        except Exception as err:
            logging.error(err, exc_info=True)
            return AdminValidateObjectIdSchema(result=False, detail=str(err))

    async def get_all_products_basic_list(self):
        products = await crud.get_all_products_basic_list(self.profile_id)
        return await convert_to_admin_product_basic_info_list(products)
