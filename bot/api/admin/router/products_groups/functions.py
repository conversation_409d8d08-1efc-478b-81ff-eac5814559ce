import json

import schemas
from api.admin.helpers import check_object_access, get_translations_schemas_dict
from db import crud
from db.models import Group, StoreCharacteristicValue, StoreProductGroup


async def products_group_to_admin_schema(
        profile_id: int,
        user_id: int,
        products_group,
        read_allowed: bool | None = True,
) -> schemas.AdminProductGroupOneSchema:
    products = await crud.get_products_for_group_by_id(
        user_id, profile_id, products_group.id
    )
    profile = await Group.get(profile_id)

    product_schemas = []
    for product in products:
        modifiers = []
        for modifier in (
                json.loads(product.modifier_values) if product.modifier_values else []):
            if modifier['id'] is not None:
                modifier_schema = await combine_and_get_modifier_with_translations(
                    modifier, profile
                )
                modifiers.append(modifier_schema)

        product_schemas.append(
            schemas.AdminProductForProductGroupSchema(
                id=product.id,
                name=product.name,
                product_id=product.product_id,
                internal_name=product.internal_name,
                position=product.position,
                modifiers=modifiers,
                read_allowed=product.read_allowed,
                edit_allowed=product.edit_allowed,
            )
        )

    edit_allowed = await check_object_access(
        "product_group", products_group.id, profile_id, user_id
    )
    if edit_allowed:
        read_allowed = True
    elif read_allowed is None:
        read_allowed = await check_object_access(
            "product_group", products_group.id, profile_id, user_id, "read"
        )

    return schemas.AdminProductGroupOneSchema(
        id=products_group.id,
        name=products_group.name,
        external_id=products_group.external_id,
        external_type=products_group.external_type,
        products=product_schemas,
        read_allowed=read_allowed,
        edit_allowed=edit_allowed,
    )


async def combine_and_get_modifier_with_translations(modifier, profile):
    characteristic_value = StoreCharacteristicValue()
    characteristic_value.id = modifier['id']
    characteristic_value.value = modifier['value']

    translations = await get_translations_schemas_dict(
        characteristic_value, profile, schemas.AdminCharacteristicValueTranslationSchema
    )

    return schemas.AdminProductModifierValueSchema(
        modifier_value_id=modifier['id'],
        modifier_value=modifier['value'],
        modifier_id=modifier['modifier_id'],
        modifier_name=modifier['modifier_name'],
        translations=translations
    )


def products_groups_list_to_admin_schema(products_groups: list[StoreProductGroup]):
    return [
        schemas.AdminProductGroupBaseSchema(
            id=group.id,
            name=group.name,
            external_id=group.external_id,
            external_type=group.external_type,
            read_allowed=group.read_allowed,
            edit_allowed=group.edit_allowed,
        )
        for group in products_groups
    ]
