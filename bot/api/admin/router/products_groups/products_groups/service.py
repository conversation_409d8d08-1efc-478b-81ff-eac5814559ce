import schemas
from api.exceptions import APIListLimitError
from db import crud
from db.models import User, Group
from api.admin.helpers import get_or_create_brand
from api.admin.router.products_groups.functions import (
    products_group_to_admin_schema,
    products_groups_list_to_admin_schema,
)

from core.auth.services.scopes_checker import ScopesCheckerService


class ProductsGroupsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                None,  # will be overridden in "write" routes
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def get_products_groups_list(
            self,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
    ) -> list[schemas.AdminProductGroupBaseSchema]:
        if not limit or limit > 100:
            raise APIListLimitError(1, 100, 10)

        products_groups = await crud.get_products_groups_list(
            self.profile_id,
            self.user.id,
            offset, limit, search_text
        )

        return products_groups_list_to_admin_schema(products_groups)

    async def get_products_groups_total_count(self, search_text: str | None = None) -> int:
        return await crud.get_products_groups_list(
            self.profile_id,
            self.user.id,
            search_text=search_text,
            is_count=True,
        )

    async def create_products_group(
            self, data: schemas.AdminProductsGroupCreateSchema
    ) -> schemas.AdminProductGroupOneSchema:
        profile = await Group.get(self.profile_id)
        brand = await get_or_create_brand(profile)

        product_group = await crud.create_products_group(brand, data, self.user)
        if not product_group:
            raise ValueError("Failed to create product group")
        return await products_group_to_admin_schema(self.profile_id, self.user.id, product_group)
