from fastapi import APIRouter, Query, Depends, Security

import schemas

from .service import ProductsGroupsService

router = APIRouter()


@router.get("/")
async def get_products_groups_list(
        search_text: str | None = Query(None, description="Search text for products groups"),
        offset: int | None = Query(None, description="Offset from start of list"),
        limit: int = Query(10, description="limit products groups. Max: 100"),
        service: ProductsGroupsService = Depends()
) -> list[schemas.AdminProductGroupBaseSchema]:
    return await service.get_products_groups_list(search_text, offset, limit)


@router.get("/total_count")
async def get_products_groups_total_count(
        search_text: str | None = Query(None, description="Search text for products groups"),
        service: ProductsGroupsService = Depends()
) -> int:
    return await service.get_products_groups_total_count(search_text)


@router.post("/")
async def create_products_group(
        data: schemas.AdminProductsGroupCreateSchema,
        service: ProductsGroupsService = Security(scopes=["me:write", "menu:create", ])
) -> schemas.AdminProductGroupOneSchema:
    return await service.create_products_group(data)
