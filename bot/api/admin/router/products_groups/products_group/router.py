from fastapi import APIRouter, Query, Depends, Security

import schemas

from .service import ProductsGroupService

router = APIRouter(
    prefix="/{product_group_id}",
)


@router.get("/")
async def get_products_group(
        service: ProductsGroupService = Depends()
) -> schemas.AdminProductGroupOneSchema:
    product_group = await service.get_products_group()
    return await service.products_group_to_schema(product_group)


@router.delete("/")
async def delete_products_group(
        service: ProductsGroupService = Security(scopes=["product_group:edit", "me:write"])
) -> schemas.OkResponse:
    return await service.delete_products_group()


@router.patch("/")
async def update_products_group(
        data: schemas.AdminProductsGroupCreateSchema,
        service: ProductsGroupService = Security(scopes=["product_group:edit", "me:write"])
) -> schemas.AdminProductGroupOneSchema:
    return await service.update_products_group(data)


@router.get("/modifiers")
async def get_products_group_modifiers_list(
        service: ProductsGroupService = Depends()
) -> list[schemas.AdminProductGroupModifierSchema]:
    return await service.get_products_group_modifiers()


@router.delete("/modifiers/{modifier_id}")
async def delete_modifier_from_products_group(
        modifier_id: int,
        service: ProductsGroupService = Security(scopes=["product_group:edit", "me:write"])
) -> list[schemas.AdminProductGroupModifierSchema]:
    return await service.delete_modifier_from_products_group(modifier_id)


@router.get("/modifiers/{modifier_id}")
async def get_modifier_for_products_group(
        modifier_id: int,
        service: ProductsGroupService = Depends()
) -> schemas.AdminProductGroupOneModifierSchema:
    return await service.get_modifier_for_products_group(modifier_id)


@router.patch("/modifiers/{modifier_id}")
async def update_modifier_from_products_group(
        modifier_id: int,
        data: schemas.AdminProductGroupUpdateModifierSchema,
        service: ProductsGroupService = Security(scopes=["product_group:edit", "me:write"])
) -> list[schemas.AdminProductGroupModifierSchema]:
    return await service.update_modifier_for_products_group(modifier_id, data)


@router.post("/modifiers/{modifier_id}")
async def add_modifier_to_products_group(
        modifier_id: int,
        service: ProductsGroupService = Security(scopes=["product_group:edit", "me:write"])
) -> list[schemas.AdminProductGroupModifierSchema]:
    return await service.add_modifier_to_products_group(modifier_id)


@router.patch("/products/modifiers_values")
async def update_products_group_modifier_values_in_products(
        data: list[schemas.AdminProductsGroupUpdateProductsModifierSchema],
        service: ProductsGroupService = Security(scopes=["product_group:edit", "me:write"])
) -> schemas.AdminProductGroupOneSchema:
    return await service.update_products_group_modifier_values_in_products(data)


@router.post("/product")
async def add_product_to_products_group(
        data: list[schemas.AdminProductsGroupUpdateProductsModifierSchema],
        service: ProductsGroupService = Security(scopes=["product_group:edit", "me:write"])
) -> schemas.AdminProductGroupOneSchema:
    return await service.add_product_to_products_group(data)
