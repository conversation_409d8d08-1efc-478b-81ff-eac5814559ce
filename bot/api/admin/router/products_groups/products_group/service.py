from dataclasses import asdict
from typing import Any

import schemas
from api.admin import exceptions
from api.admin.helpers import (
    get_translations_schemas_dict,
    validate_non_empty_fields_and_translations,
)
from api.admin.router.products_groups.functions import products_group_to_admin_schema
from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import Group, StoreProductGroup, User
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class ProductsGroupService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "product_group:read",  # will be overridden in "write" routes
                "profile_id",
                "product_group_id"
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id
        self.available_data: dict[str, Any] = asdict(scopes.data)  # dataclass
        self.product_group_id: int = scopes.data.product_group_id

    async def products_group_to_schema(self, products_group: StoreProductGroup):
        return await products_group_to_admin_schema(
            self.profile_id,
            self.user.id,
            products_group
        )

    async def get_products_group(self) -> StoreProductGroup:
        product_group = await crud.get_product_group_by_id(
            self.user.id, self.profile_id, self.product_group_id
        )
        if not product_group:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "product_group:read", self.available_data,
            )

        return product_group

    async def update_products_group(
            self,
            data: schemas.AdminProductsGroupCreateSchema
    ) -> schemas.AdminProductGroupOneSchema:
        await self.get_products_group()

        updated_product_group = await crud.update_products_group(
            self.product_group_id, data
        )
        if not updated_product_group:
            raise RuntimeError("Failed to update product group")

        return await self.products_group_to_schema(
            updated_product_group
        )

    async def delete_products_group(self) -> schemas.OkResponse:
        await self.get_products_group()

        res = await crud.delete_products_group(self.product_group_id)
        if not res:
            raise RuntimeError("Failed to delete product group")

        return schemas.OkResponse()

    async def get_products_group_modifiers(
            self,
    ) -> list[schemas.AdminProductGroupModifierSchema]:
        modifiers = await crud.get_product_group_modifiers(
            self.user.id, self.profile_id, self.product_group_id
        )
        modifier_schemas = [schemas.AdminProductGroupModifierSchema(
            id=modifier.id,
            product_group_id=self.product_group_id,
            name=modifier.name,
            is_hide=modifier.is_hide,
            show_one_modification=modifier.show_one_modification,
            read_allowed=modifier.read_allowed,
            edit_allowed=modifier.edit_allowed,
        ) for modifier in modifiers]

        return modifier_schemas

    async def delete_modifier_from_products_group(
            self, modifier_id: int
    ) -> list[schemas.AdminProductGroupModifierSchema]:
        res = await crud.delete_modifier_from_products_group(
            self.product_group_id, modifier_id
        )
        if not res:
            raise RuntimeError("Failed to delete modifier from products group")

        return await self.get_products_group_modifiers()

    async def get_modifier_for_products_group(
            self, modifier_id: int
    ) -> schemas.AdminProductGroupOneModifierSchema:
        modifier = await crud.get_product_group_modifier(
            self.user.id, self.profile_id, self.product_group_id,
            modifier_id
        )
        profile = await Group.get(self.profile_id)
        characteristic = await crud.get_characteristic_by_id_and_profile_id(
            modifier_id, self.profile_id
        )
        translations = await get_translations_schemas_dict(
            characteristic, profile, schemas.AdminCharacteristicTranslationSchema
        )
        if not modifier:
            raise exceptions.AdminProductModifierNotFoundError(modifier_id)

        return schemas.AdminProductGroupOneModifierSchema(
            id=modifier_id,
            product_group_id=self.product_group_id,
            name=modifier.name,
            translations=translations,
            is_hide=modifier.is_hide,
            show_one_modification=modifier.show_one_modification,
            read_allowed=modifier.read_allowed,
            edit_allowed=modifier.edit_allowed,
        )

    async def update_modifier_for_products_group(
            self, modifier_id: int, data: schemas.AdminProductGroupUpdateModifierSchema
    ) -> list[schemas.AdminProductGroupModifierSchema]:
        if data.name:
            profile = await Group.get(self.profile_id)

            characteristic = await crud.get_characteristic_by_id_and_profile_id(
                characteristic_id=modifier_id, profile_id=profile.id
            )

            if characteristic:
                data_only_set = data.dict(
                    exclude_unset=True, exclude={"show_one_modification"}
                )
                validate_non_empty_fields_and_translations(
                    data_only_set, characteristic, "name"
                )
                await crud.update_characteristic(
                    characteristic, data, profile.get_langs_list()
                )

        res = await crud.update_modifier_for_products_group(
            self.product_group_id, modifier_id, data.show_one_modification
        )
        if not res:
            raise RuntimeError("Failed to update modifier for products group")

        return await self.get_products_group_modifiers()

    async def add_modifier_to_products_group(
            self, modifier_id: int
    ) -> list[schemas.AdminProductGroupModifierSchema]:
        res = await crud.add_modifier_to_products_group(
            self.product_group_id, modifier_id
        )
        if not res:
            raise RuntimeError("Failed to add modifier to products group")

        return await self.get_products_group_modifiers()

    async def update_products_group_modifier_values_in_products(
            self,
            data: list[schemas.AdminProductsGroupUpdateProductsModifierSchema]
    ) -> schemas.AdminProductGroupOneSchema:
        await self.get_products_group()

        res = await crud.update_modifiers_values_in_products(data)
        if not res:
            raise RuntimeError("Failed to update modifiers values in products")

        updated_groups = await self.get_products_group()
        return await self.products_group_to_schema(updated_groups)

    async def add_product_to_products_group(
            self, data: list[schemas.AdminProductsGroupUpdateProductsModifierSchema]
    ) -> schemas.AdminProductGroupOneSchema:
        await self.get_products_group()

        await crud.add_products_to_products_group_with_modifiers_values(
            self.product_group_id, data
        )
        updated_groups = await self.get_products_group()
        return await self.products_group_to_schema(updated_groups)
