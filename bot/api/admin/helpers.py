import re
from inspect import isclass
from typing import Type

from psutils.translator.models import TranslatorModel
from pydantic import BaseModel

from api.admin.exceptions import (
    AdminFieldCannotBeEmptyError, AdminProductFloatingSumOptionValueError,
    AdminProductFloatingSumOptionsPatternError,
    AdminTranslationSpecifiedForEmptyFieldError,
)
from core.brand.functions import auto_create_brand
from core.helpers import calc_time_processed
from db import crud
from db.mixins import BaseDBModel
from db.models import Brand, Group, MediaObject, Task
from schemas import TaskListSchema, TaskSchema
from utils.translator import t
from utils.type_vars import T


async def get_translations_schemas_dict(
        object: BaseDBModel,
        group: Group,
        translation_schema_cls: Type[T],
) -> dict[str, T]:
    translations = dict(
        await crud.get_translations_for_langs_list(
            object, group.get_langs_list(False),
        )
    )

    return {
        lang: translation_schema_cls(
            **(await t(
                object,
                lang,
                group.lang,
                translation=translations.get(lang),
                fallback_to_original_on_error=False,
                group_id=group.id,
                is_auto_translate_allowed=group.is_translate,
            ))
        )
        for lang in group.get_langs_list(with_main_lang=False)
    }


async def get_or_create_brand(profile_or_id: Group | int):
    profile_id = profile_or_id.id if isinstance(profile_or_id, Group) else profile_or_id
    brand = await Brand.get(group_id=profile_id)
    if brand:
        return brand

    if isinstance(profile_or_id, int):
        profile = await Group.get(profile_or_id)
    else:
        profile = profile_or_id

    return await auto_create_brand(profile)


async def check_object_access(
        object_name: str, object_id: int,
        profile_id: int, user_id: int,
        scope_name: str = "edit",
):
    return await crud.check_access_to_action(
        f"{object_name}:{scope_name}", "user", user_id,
        available_data={
            "profile_id": profile_id,
            f"{object_name}_id": object_id,
        }
    )


def validate_non_empty_fields_and_translations(
        data: BaseModel | dict,
        object_or_object_cls: BaseDBModel,
        *fields: str,
):
    data_dict = data.dict(exclude_unset=True) if isinstance(data, BaseModel) else data

    for field in fields:
        if field in data_dict and not data_dict[field]:
            raise AdminFieldCannotBeEmptyError(field)

    translations: dict[str, BaseModel | None] | None = getattr(
        data, "translations", None
    )
    if not translations:
        return

    if isclass(object_or_object_cls):
        object = None
        object_cls = object_or_object_cls
    else:
        object = object_or_object_cls
        object_cls = object.__class__

    translator_model = TranslatorModel.detect_model(object_cls.__name__)

    for translation in translations.values():
        if not translation:
            continue

        for field in translator_model.fields_names:
            if field in data_dict:
                current_value = data_dict[field]
            elif object:
                current_value = getattr(object, field)
            else:
                current_value = None

            if not current_value and getattr(translation, field):
                raise AdminTranslationSpecifiedForEmptyFieldError(field)


def validate_floating_sum_options(
        floating_sum_options: str | None,
        product_id: str, product_name: str,
) -> list[float] | None:
    if not floating_sum_options:
        return None

    pattern = r'\b\d+(?:\.\d+)?\b'

    floating_sum_options = floating_sum_options.replace(' ', '')
    matches = re.findall(pattern, floating_sum_options)

    if not matches:
        raise AdminProductFloatingSumOptionsPatternError(product_id, product_name)

    numbers = []
    for match in matches:
        if '.' in match:
            try:
                numbers.append(float(match))
            except ValueError:
                raise AdminProductFloatingSumOptionValueError(product_id, product_name)
        else:
            try:
                print(f"*** match {match}")
                numbers.append(int(match))
            except ValueError:
                raise AdminProductFloatingSumOptionValueError(product_id, product_name)

    return numbers


async def task_to_schema(
        task: Task, lang: str, is_detail: bool | None = False
) -> TaskSchema | TaskListSchema:

    if is_detail:
        task_result = TaskSchema.from_orm(task)

        if task.json_data.get("media_id"):
            media_object = await MediaObject.get(task.json_data.get("media_id"))
            task_result.media_url = media_object.url

        if description := task.json_data.get("description"):
            task_result.description = description

    else:
        task_result = TaskListSchema.from_orm(task)

    if task.start_date and task.end_date:
        task_result.time_processed = calc_time_processed(
            task.start_date, task.end_date, lang
        )

    task_result.object_name = task.json_data.get("name", None)

    if task.json_data.get("banner_id"):
        task_result.banner_id = task.json_data.get("banner_id")

    return task_result
