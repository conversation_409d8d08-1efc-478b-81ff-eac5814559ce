from starlette import status

from utils.exceptions import ErrorWithHTTPStatus


class APIListLimitError(ErrorWithHTTPStatus):
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    text_variable = "api list limit error"

    def __init__(
            self,
            min_limit: int = 1,
            max_limit: int = 100,
            default_limit: int = 10,
    ):
        super().__init__(
            min_limit=min_limit,
            max_limit=max_limit,
            default_limit=default_limit,
            detail_data={
                "error_code": "list_limit_error",
                "min_limit": min_limit,
                "max_limit": max_limit,
                "default_limit": default_limit,
            }
        )


class MediaNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "media not found error"

    def __init__(self, media_id: int):
        super().__init__(
            media_id=media_id,
            detail_data={
                "error_code": "media_not_found",
                "media_id": media_id,
            }
        )
