from fastapi import APIRouter, Depends

import schemas
from api.notifications.router.profile.service import ProfileNotificationsService

router = APIRouter(
    prefix="/profile/{profile_id}",
    tags=["profile"],
)


@router.post("/staff")
async def create_notifications_for_staff(
        data: schemas.CreateProfileStaffNotificationData,
        service: ProfileNotificationsService = Depends()
) -> schemas.AdminNotificationSchema:
    return await service.create_staff_notification(data)


@router.post("/user")
async def create_notifications_for_user():
    pass
