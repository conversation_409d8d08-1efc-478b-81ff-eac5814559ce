import schemas
from core.admin_notification.service import create_system_notification
from core.auth.services.scopes_checker import ScopesCheckerService
from db.models import User


class ProfileNotificationsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "notifications:create",
                "profile_id",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        self.profile_id: int = scopes.data.profile_id

    async def create_staff_notification(
            self, data: schemas.CreateProfileStaffNotificationData
    ):
        return await create_system_notification(
            scope=data.scope,
            group_id=self.profile_id,
            category=data.category,
            type_notification=data.type,
            title=data.title,
            content=data.content,
            level=data.level,
        )
