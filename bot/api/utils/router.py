import pytz
from babel.numbers import list_currencies
from fastapi import APIRouter, Depends, HTTPException
from psutils.country import Countries, Country
from starlette import status

import schemas
from api.utils.exceptions import FastPayGenerateLinkException
from core.api.depends import get_lang
from core.fast_pay.generator import FastPayLinkGenerator
from utils.country import get_country_name_from_code, get_currency

router = APIRouter()


@router.get("/currencies")
async def currencies_list() -> list[str]:
    return list(list_currencies())


@router.get("/countries")
async def countries_list(
        lang: str = Depends(get_lang)
) -> list[Country]:
    return Countries(lang).get_countries()


@router.get("/timezones")
async def timezones_list() -> list[str]:
    return pytz.all_timezones


@router.get("/tax_list")
async def tax_list() -> list[dict[str, str]]:
    return [
        {'code': 'А', 'description': 'Без ПДВ 0%'},
        {'code': 'Б', 'description': 'ПДВ 20%'},
        {'code': 'В', 'description': 'ПДВ 7%'},
        {'code': 'Г', 'description': 'Акциз 5%'}
    ]


@router.get("/currency_by_country/{country_code}")
async def currency_by_country(country_code: str) -> schemas.Currency:
    country_name = get_country_name_from_code(country_code)
    if not country_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail={
                "error_code": "invalid_country_code",
                "message": "Invalid country code",
            }
        )
    currency = get_currency(country_name, return_type=None)
    if not currency:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail={
                "error_code": "currency_not_found",
                "message": f"Currency for country {country_name} not found",
                "country_code": country_code,
                "country_name": country_name,
            }
        )
    return schemas.Currency.from_orm(currency)


@router.post("/fastpay_url")
async def generate_fast_pay_url(
        params: schemas.FastPayLinkParams
) -> schemas.FastPayLinkResponse:
    link_generator = FastPayLinkGenerator(
        group_id=params.group_id,
        lang=params.lang,
        mode=params.mode,
        invoice_template_id=params.invoice_template_id,
        entered_amount=params.entered_amount,
        bot_type=params.bot_type,
        bot_id_name=params.bot_id_name,
        nl_key=params.nl_key,
        external_transaction_id=params.external_transaction_id,
        client_redirect_url=params.client_redirect_url,
    )

    link = await link_generator.make_link(params.show_mode)
    if link:
        return schemas.FastPayLinkResponse(link=link)
    raise FastPayGenerateLinkException()
