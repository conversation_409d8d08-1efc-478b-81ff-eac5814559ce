from starlette.responses import StreamingResponse
from starlette.types import Send


class CustomStreamingResponse(StreamingResponse):
    # noinspection PyMissingConstructor
    def __init__(self, original_response: StreamingResponse):
        self.body_iterator = original_response.body_iterator
        self.status_code = original_response.status_code
        self.media_type = original_response.media_type
        self.raw_headers = original_response.raw_headers
        self.body = b""  # will be filling while sending content

    async def stream_response(self, send: Send) -> None:
        await send(
            {
                "type": "http.response.start",
                "status": self.status_code,
                "headers": self.raw_headers,
            }
        )
        async for chunk in self.body_iterator:
            if not isinstance(chunk, bytes):
                chunk = chunk.encode(self.charset)
            self.body += chunk
            await send({"type": "http.response.body", "body": chunk, "more_body": True})

        await send({"type": "http.response.body", "body": b"", "more_body": False})
