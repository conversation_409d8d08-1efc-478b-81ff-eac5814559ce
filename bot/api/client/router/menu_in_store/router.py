import logging
from fastapi import APIRouter, HTTPException, Depends, Path
from starlette import status

from core.api.depends import get_lang
from core.store.depends import get_current_brand
import schemas

from db.models import MenuInStore, Brand, Store

from core.custom_texts.models import MenuInStoreCustomTextsModel
from core.invoice.functions import invoice_template_to_schema

logger = logging.getLogger('debugger')

router = APIRouter(
    prefix="/menuInStore/{menu_in_store_id}",
    tags=["/store/menuInStore"]
)


@router.get("/")
async def get_menu_in_store(
    menu_in_store_id: int = Path(),
    brand: Brand = Depends(get_current_brand),
    lang: str = Depends(get_lang)
) -> schemas.MenuInStoreSchema:
    menu_in_store: MenuInStore = await MenuInStore.get(menu_in_store_id, group_id=brand.group_id)
    if not menu_in_store:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=f"menu in store with id {menu_in_store_id} not found")

    ct_obj = await MenuInStoreCustomTextsModel.from_object(menu_in_store)

    store_id = None
    if menu_in_store.store_id:
        store = await Store.get(id=menu_in_store.store_id, brand_id=brand.id)
        if store and not store.is_deleted and store.is_enabled:
            store_id = store.id

    return schemas.MenuInStoreSchema(
        id=menu_in_store.id,
        group_id=menu_in_store.group_id,
        store_id=store_id,
        comment=menu_in_store.comment,
        redirect_type=menu_in_store.redirect_type,
        need_save_as_active=menu_in_store.need_save_as_active,
        is_e_menu=menu_in_store.is_e_menu,
        texts=await ct_obj.to_dict(lang),
        payment_option=menu_in_store.payment_option,
        invoice_template_id=menu_in_store.invoice_template_id,
        invoice_template=await invoice_template_to_schema(menu_in_store.invoice_template, lang)
        if menu_in_store.invoice_template_id else None,
    )
