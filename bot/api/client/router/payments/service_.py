from email import encoders

import aiofiles
import asyncio
import logging
from email.mime.base import MIMEBase
from psutils.date_time import localise_datetime
from psutils.exceptions.exception_handlers import Unknown<PERSON>rror<PERSON>andler
from psutils.mailing import GmailClient

from config import (
    LOC7_EMAIL_LOGIN, LOC7_EMAIL_PASSWORD,
)
from core import messangers_adapters as ma
from core.invoice.exception import (
    BaseInvoiceError, InvoiceUnknownError,
)
from core.payment.exceptions import (
    PaymentSendToFriendError,
)
from core.store.product.exceptions import UserNotFoundError
from core.templater import templater
from db import crud
from db.models import (
    Brand, ClientBot, Group, Invoice, InvoiceToFriend, MediaObject, Store, StoreOrder,
    User,
)
from schemas.payment.payment import FriendEmailTemplate
from utils.email_funcs import send_with_attachments
from utils.numbers import format_currency
from utils.text import f, html_to_markdown

debugger = logging.getLogger("debugger.payments")
handle_error = UnknownErrorHandler(InvoiceUnknownError, BaseInvoiceError)


async def set_friend_to_invoice_(
        invoice: Invoice,
        order: StoreOrder | None,
        bot: ClientBot | None,
        friend_id: int,
        friend_comment: str | None = None,
        friend_comment_media: MediaObject | None = None
) -> Invoice:

    payer = await User.get_by_id(friend_id)
    if not payer:
        raise UserNotFoundError(user_id=friend_id)
    try:
        invoice_to_friend = await crud.add_friend_to_invoice(
            invoice, friend_id, friend_comment, friend_comment_media, )
        await send_invoice_to_friend(invoice, invoice_to_friend, order, payer, bot)
    except Exception as ex:
        logging.error(str(ex), exc_info=True)
        raise PaymentSendToFriendError(friend_name=payer.name)
    return invoice


async def send_invoice_to_friend(
        invoice: Invoice,
        invoice_to_friend: InvoiceToFriend,
        order: StoreOrder | None,
        payer: User,
        bot: ClientBot | None,
):
    debugger.debug(
        f"send_invoice_to_friend {invoice.id=} | order.id = "
        f"{order.id if order else ''} to "
        f"{payer.wa_phone=}, {payer.chat_id=}, {payer.email=} ..."
    )

    lang = payer.lang

    user: User = await User.get_by_id(invoice.user_id)
    user_info = user.name
    if user.email:
        user_info += f"</br>{user.email}"
    if user.wa_phone:
        user_info += f"</br>{user.wa_phone}"

    btn_text = await f("friend payment button text", lang)

    brand: Brand = await Brand.get_by_group(invoice.group_id)
    group: Group = await Group.get(invoice.group_id)

    if order:
        store = await Store.get(order.store_id)
        store_or_brand_name = store.name
        order_or_invoice = (await f("group web order", lang)).lower()
    else:
        store_or_brand_name = brand.name
        order_or_invoice = (await f("group web invoice", lang)).lower()

    invoice_date = localise_datetime(invoice.time_created, group.timezone, "utc")
    invoice_date = f"{invoice_date:%d.%m.%Y %H:%M:%S}"
    amount = format_currency(
        invoice.converted_sum_to_pay, invoice.currency, locale=group.lang
    )

    if order:
        path = f"{lang}/s/{order.store_id}/orders/{order.id}"
    else:
        path = f"{lang}/s/invoice/{invoice.id}"

    url = await brand.get_short_token_url(
        payer, bot.id if bot else None, lang, path, )

    if invoice_to_friend.comment_media_id:
        comment_media = await MediaObject.get(invoice_to_friend.comment_media_id)
    else:
        comment_media = None

    if bot:
        user_to = ma.get_user_to(payer, bot.bot_type)
        if user_to:
            ma.Bot.set_current(bot)
            messanger_bot = ma.Bot(bot=bot)
            text = await f(
                "web store friend payment text", lang, friend_name=user.name,
                store_or_brand_name=store_or_brand_name,
                order_or_invoice=order_or_invoice, invoice_date=invoice_date,
                amount=amount, )
            if invoice_to_friend.comment:
                text += "\n————————————————————\n" + f"{invoice_to_friend.comment}"

            keyboard = ma.InlineKeyboard()
            keyboard.add_buttons(ma.UrlKeyboardButton(btn_text, url=url))

            if bot.bot_type == "telegram":
                keyboard = keyboard.to_telegram()
            if bot.bot_type == "whatsapp":
                text += f"\n\n{await f('friend payment button wa text', lang)}\n{url}"
                text = html_to_markdown(text)
                keyboard = None

            if comment_media:
                file_type = comment_media.media_type

                if (file_type == "image" and bot.bot_type == "whatsapp" and
                        comment_media.file_path.endswith(
                            ".webp"
                        )):
                    await messanger_bot.bot.send_sticker(user_to, comment_media.url)
                    await messanger_bot.bot.send_message(user_to, text)
                else:
                    kwargs = {}

                    if bot.bot_type == "telegram":
                        if file_type == "image":
                            file_type = "photo"
                        kwargs["parse_mode"] = "HTML"

                    if file_type in ("text", "application"):
                        file_type = "document"
                    sending_method = getattr(messanger_bot.bot, f"send_{file_type}")

                    await sending_method(
                        user_to, comment_media.url, text, reply_markup=keyboard,
                        **kwargs
                    )
            else:
                await messanger_bot.send_message(user_to, text, keyboard=keyboard)

            debugger.debug(
                f"send_invoice_to_friend Ok, {user_to=}, {bot.id=}, {bot.bot_type=}"
            )
            return

    if payer.email:
        text = await f(
            "web store friend payment email text", lang,
            payer_name=payer.name,
            friend_name=user.name,
            store_or_brand_name=store_or_brand_name,
            order_or_invoice=order_or_invoice,
            invoice_date=invoice_date,
            amount=amount,
        )
        template = FriendEmailTemplate(
            url=url, btn_text=btn_text,
            friend_comment=invoice_to_friend.comment,
            message=text
        )
        html = await templater.make_template(template, invoice.group_id)

        attachments = []
        if comment_media:
            subtype = comment_media.mime_type.split("/", 1)[1]

            attachment = MIMEBase(comment_media.media_type, subtype)
            async with aiofiles.open(comment_media.file_path, "rb") as file:
                attachment.set_payload(await file.read())

            encoders.encode_base64(attachment)
            attachment.add_header(
                "Content-Disposition",
                f"attachment; filename=attachment.{comment_media.extension}", )
            attachments.append(attachment)

        gmail = GmailClient(LOC7_EMAIL_LOGIN, LOC7_EMAIL_PASSWORD)
        asyncio.ensure_future(
            send_with_attachments(
                gmail, destination=payer.email, subject=await f(
                    "web store friend subject text", lang, friend_name=user.name
                ), html=html, attachments=attachments, )
        )
    else:
        raise Exception(
            "Send invoice to friend FAILED. Friend not have email and messangers..."
        )
