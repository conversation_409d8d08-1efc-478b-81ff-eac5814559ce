from starlette.requests import Request

from core.invoice import finalize_payment
from core.payment.decorators import payment_error_handler
from core.payment.payment_processor import PaymentProcessor
from db import crud
from loggers import JSONLogger


@payment_error_handler()
async def process_webhook(
        payment_method: str,
        request: Request | None = None,
        data: any = None,
        payment_uuid: str | None = None,
) -> any:
    context = {"payment_method": payment_method}

    logger = JSONLogger(
        "payments.webhook",
        f"{payment_method}",
        {"payment_method": payment_method},
    )

    logger.debug({"payment_uuid": payment_uuid, "data": data})

    try:
        processor = PaymentProcessor(payment_method)

        if not payment_uuid:
            payment_uuid = await processor.get_payment_uuid(data=data, request=request)
            logger.debug({"payment_uuid": payment_uuid})

        payment, invoice, store_order, brand = await crud.get_payment_data_(
            payment_uuid
        )
        context.update(
            {
                'payment_uuid': payment.uuid_id,
                'brand_id': brand.id if brand else None,
                'store_id': store_order.store_id if store_order else None,
                'order_id': store_order.id if store_order else None,
                'invoice_id': invoice.id if invoice else None,
            }
        )

        credentials = await crud.get_payment_credentials(payment)
        kwargs = {
            "request": request, "data": data, "credentials": credentials,
            "payment_uuid": payment_uuid,
            "amount": payment.amount, "currency": payment.currency,
        }

        logger.debug(
            {
                "credentials": credentials, "payment_uuid": payment_uuid,
                "amount": payment.amount, "currency": payment.currency,
                "invoice_id": invoice.id if invoice else None,
                "brand_id": brand.id if brand else None,
                "store_id": store_order.store_id if store_order else None,
            }
        )

        payment_data = await processor.get_payment_result(**kwargs)

        payment_data.payment_method = payment_method
        payment_data.payment_uuid = payment_uuid
        payment_data.payment_settings_id = payment.payment_settings_id

        logger.debug({"payment_data": payment_data.dict(exclude={'callback_data'})})

        # TJ return result in 2 step. 1 - PAYMENT_AUTHORISED, 2 - PAYMENT_SETTLED
        if payment_data.status == "authorized":
            logger.debug("Return Ok response to provider for 1-st step payment")
            return processor.return_status(status="success", payment_uuid=payment_uuid)

        await finalize_payment(payment_data, invoice, payment, brand, store_order)
        return processor.return_status(status="success", payment_uuid=payment_uuid)
    except Exception as e:
        e.context = context
        raise
