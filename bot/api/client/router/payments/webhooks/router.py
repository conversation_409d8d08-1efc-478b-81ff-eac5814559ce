import json
import logging

from fastapi import APIRouter, Depends, Path
from pydantic import ValidationError
from starlette.requests import Request
from starlette.responses import JSONResponse, PlainTextResponse

from .functions import process_webhook
from core.payment.payment_processor.providers.freedompay import (
    FreedompayClient
)
from schemas import (
    AirTelCallback, ComsaPaymentNotification, EpayWebhook, FlutterwaveTransaction,
    FondyHookResponse, FondyWebHookResponse, KPayHookResponse, MomoWebHookData,
    OrangeHookResponse,
    OrangeOtpData, PaymentLiqPayCallBackData, Pl24HookResponse, TjPaymentResult,
    TpayHookResponse, UniposPayment, WaveHookResponse,
)

router = APIRouter(include_in_schema=False)


@router.post(
    '/callback/liqpay/{payment_uuid}',
    description='Method for process callback from liqpay',
    response_model=dict,
    include_in_schema=False,
)
async def callback_liqpay(
        payment_uuid: str = Path(),
        form_data: PaymentLiqPayCallBackData = Depends(),
):
    return await process_webhook("liqpay", payment_uuid=payment_uuid, data=form_data)


@router.post(
    '/callback/stripe',
    description='Method for process callback from stripe payment',
    response_model=dict,
)
async def webhook_stripe(
        data: dict,
        request: Request,
):
    return await process_webhook("stripe", request, data)


@router.post(
    '/callback/unipos/{payment_uuid}',
    description='Method for process callback from unipos payment',
    response_model=dict,
)
async def webhook_unipos(
        payment_uuid: str = Path(),
        data: UniposPayment | None = None,
):
    return await process_webhook("unipos", payment_uuid=payment_uuid, data=data)


@router.post(
    '/callback/wave',
    description='Method for process callback from wave',
    response_model=dict,
)
async def webhook_wave(
        request: Request,
        data: WaveHookResponse | None = None,
):
    return await process_webhook("wave", request=request, data=data)


@router.post(
    '/callback/pl24/{payment_uuid:path}',
    description='Method for process callback from przelewy24 store',
    response_model=dict,
)
async def webhook_pl24(
        request: Request,
        payment_uuid: str = Path(),
        data: Pl24HookResponse | None = None,
):
    return await process_webhook(
        "pl24", request=request, payment_uuid=payment_uuid, data=data
    )


@router.post(
    '/callback/tpay/{payment_uuid:path}',
    description='Method for process callback from tpay',
    response_model=str,
    include_in_schema=False,
)
async def webhook_tpay(
        payment_uuid: str = Path(),
        data: TpayHookResponse = Depends(),
) -> PlainTextResponse:
    return await process_webhook("tpay", payment_uuid=payment_uuid, data=data)


@router.get(
    '/callback/orangeotp/{payment_uuid:path}',
    description='Method for process otp from orange otp',
    response_model=str,
)
async def webhook_orange_otp(
        payment_uuid: str,
        amount: int,
        customer_phone: str,
        currency: str,
        otp: str,
        success_url: str,
):
    data = OrangeOtpData(
        amount=amount,
        customer_phone=customer_phone,
        reference_id=payment_uuid,
        currency=currency,
        otp=otp,
        success_url=success_url
    )
    return await process_webhook("tpay", payment_uuid=payment_uuid, data=data)


@router.post(
    '/callback/orange/{payment_uuid}',
    description='Method for process callback from orange',
    response_model=dict,
)
async def webhook_orange(
        payment_uuid: str,
        data: OrangeHookResponse,
):
    return await process_webhook("orange", payment_uuid=payment_uuid, data=data)


@router.post(
    '/callback/kpay/{payment_uuid}',
    description='Method for process callback from kpay',
)
async def webhook_kpay_store(
        payment_uuid: str,
        request: Request,  # Змінюємо параметр з моделі на Request
):
    logger = logging.getLogger("debugger.webhook.kpay")
    raw_data = await request.json()
    logger.debug(f"Raw webhook data: {json.dumps(raw_data, indent=2)}")

    try:
        if "_doc" in raw_data:
            data = KPayHookResponse(**raw_data["_doc"])
            return await process_webhook("kpay", payment_uuid=payment_uuid, data=data)
    except ValidationError as e:
        logger.error(str(e))
        return JSONResponse(
            status_code=400,
            content={"validation_error": e.errors(), "received_data": raw_data}
        )


@router.post(
    '/callback/fondy/{payment_uuid:path}',
    description='Method for process callback from fondy store',
    status_code=200,
)
async def webhook_fondy(
        request: Request,
        payment_uuid: str = Path(),
        data: FondyWebHookResponse = Depends(),
):
    data = FondyHookResponse(
        **{
            key: value for key, value in data.response.items()
            if value != "" and value is not None
        }
    )
    return await process_webhook(
        "fondy", request=request, payment_uuid=payment_uuid, data=data
    )


@router.post(
    '/callback/freedompay/{payment_uuid:path}',
    description='Method for process callback from freedompay',
    status_code=200,
)
async def webhook_freedompay_store(
        request: Request,
        payment_uuid: str = Path(),
):
    data = FreedompayClient.get_response(await request.body())
    return await process_webhook("freedompay", payment_uuid=payment_uuid, data=data)


@router.post(
    '/callback/flutterwave',
    description='Method for process callback from flutterwave',
    status_code=200,
)
async def webhook_flutterwave_store(
        request: Request,
        data: FlutterwaveTransaction,
):
    return await process_webhook("flutterwave", request=request, data=data)


@router.post(
    '/callback/comsa',
    description='Method for process callback from comsa',
    status_code=200,
)
async def webhook_comsa(
        data: ComsaPaymentNotification,
):
    return await process_webhook("comsa", data=data)


@router.post(
    '/callback/epay/{payment_uuid}',
    description='Method for process callback from epay store',
    status_code=200,
)
async def webhook_epay_store(
        payment_uuid: str,
        data: EpayWebhook = Depends(),
):
    return await process_webhook("epay", payment_uuid=payment_uuid, data=data)


@router.api_route(
    "/callback/directpay", methods=["POST", "PUT", "OPTIONS", "GET"],
    description='Method for process callback from directpay',
    status_code=200,
)
async def webhook_directpay(request: Request):
    return await process_webhook("directpay", request=request)


@router.api_route(
    "/callback/momo",
    methods=["GET"],
    description='Method for test ping from mtn momo',
    status_code=200, )
async def test_webhook_momo():
    return {"status": "OK"}


@router.api_route(
    "/callback/momo/{payment_uuid}",
    methods=["POST", "PUT", "OPTIONS"],
    description='Method for process callback from mtn momo',
    status_code=200, )
async def webhook_momo(data: MomoWebHookData, payment_uuid: str | None = Path(), ):
    return await process_webhook("momo", payment_uuid=payment_uuid, data=data)


@router.api_route(
    "/callback/tj",
    methods=["POST", "PUT", "OPTIONS"],
    description='Method for process callback from transaction junction',
    status_code=200, )
async def webhook_tj(data: TjPaymentResult):
    return await process_webhook("tj", data=data)


@router.api_route(
    "/callback/airtel",
    methods=["POST", "PUT", "OPTIONS"],
    description='Method for process callback from airtel',
    status_code=200, )
async def webhook_airtel(data: AirTelCallback):
    return await process_webhook("airtel", data=data)
