from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Query
from sse_starlette import EventSourceResponse
from starlette.requests import Request

from .service import EWalletPaymentStatusService
from core.admin_notification.service import notify_all_channels
from core.sse_service.service import PaymentSSEService
from db import DBSession
from schemas import OkResponse, SSEChannelTarget

router = APIRouter()


@router.get("/{payment_id}/stream")
async def stream_notifications_for_payment(
        request: Request,
        x_session_id: str = Header(...),
        payment_id: int = Path(...),
        profile_id: int = Query(...),
) -> EventSourceResponse:
    """Endpoint для створення SSE з'єднання"""
    try:
        payment_sse_service = PaymentSSEService(
            session_id=x_session_id,
            target=SSEChannelTarget.PAYMENT_STATUS
        )

        with DBSession():
            data_result = await payment_sse_service.get_data(
                request, payment_id=payment_id, profile_id=profile_id,
            )
    except Exception as e:
        raise e
    external_id = data_result.get("external_id")
    return await payment_sse_service.stream_notifications(
        key=f"{profile_id}-{external_id}",
    )


@router.post("/{payment_id}/cancel")
async def cancel_ewallet_payment(
        payment_id: int = Path(...),
) -> OkResponse:
    """Endpoint для скасування платежу"""
    service = EWalletPaymentStatusService(payment_id)
    return await service.cancel_payment()


# for test only
@router.post("/stream")
async def create_payment_status_notifications(
        payment_id: int = Query(...),
        title: str = Query(...),
        content: str = Query(...),
        profile_id: int = Query(...),
) -> dict:

    await notify_all_channels(
        SSEChannelTarget.PAYMENT_STATUS,
        "paid",
        key=f"{profile_id}-{payment_id}",
        payment_id=payment_id,
        title=title,
        content=content,
        level="info",
    )
    return {"status": "success"}
