from fastapi import HTT<PERSON><PERSON>xception

from db import crud
from db.models import EWalletPayment, Payment
from schemas import EWalletPaymentStatus, OkResponse


class EWalletPaymentStatusService(object):
    def __init__(self, payment_id: int, ):
        self.payment_id = payment_id
        self.user_id = None

    async def cancel_payment(self, ):
        ewallet_payment = await EWalletPayment.get(self.payment_id)
        if not ewallet_payment:
            raise HTTPException(status_code=404, detail="eWallet Payment not found")

        if ewallet_payment.status == EWalletPaymentStatus.PAID:
            raise HTTPException(status_code=400, detail="eWallet Payment already paid")

        if ewallet_payment.status not in (
                EWalletPaymentStatus.PENDING, EWalletPaymentStatus.CREATED):
            return OkResponse()
            # raise HTTPException(status_code=404, detail="eWallet Payment not
            # pending or created")

        payment = await Payment.get(uuid_id=ewallet_payment.external_id)
        if not payment:
            raise HTTPException(status_code=404, detail="Payment not found")

        if payment.status != "pending":
            raise HTTPException(status_code=400, detail="Payment not pending")

        ewallet_payment = await crud.cancel_ewallet_payment(ewallet_payment)

        await payment.cancel()

        return OkResponse()
