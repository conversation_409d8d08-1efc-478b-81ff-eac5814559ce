from fastapi import Depends

from .depend import fetch_order_or_invoice_data
from core.api.depends import get_lang
from core.payment.payments_service import PaymentsService as BasePaymentsService
from core.store.depends import get_current_bot_with_brand, get_current_brand
from db.models import Brand, ClientBot, Invoice, StoreOrder, User


class PaymentsService(BasePaymentsService):
    def __init__(
            self,
            brand: Brand = Depends(get_current_brand),
            lang: str = Depends(get_lang),
            payment_data: tuple[StoreOrder | None, Invoice | None, User]
            = Depends(fetch_order_or_invoice_data(True)),
            bot: ClientBot | str | None = Depends(get_current_bot_with_brand)
    ):
        if not isinstance(bot, ClientBot):
            bot = None

        order, invoice, user = payment_data
        super().__init__(brand, user, lang, order, invoice, bot)
