from typing import Literal

from fastapi import Depends, Path
from fastapi.security import SecurityScopes
from starlette.requests import Request

from core.auth.depend import (
    get_token, get_user_or_token_data, parse_access_token_depend,
)
from exceptions import AuthNotAuthorisedError, AuthNotEnoughPermissionsError
from core.invoice.exception import InvoiceNotFoundError
from core.invoice.functions import is_valid_friend_for_invoice
from core.store.exceptions import OrderNotFoundError
from db.models import Invoice, StoreOrder, StoreOrderPayment, User
from schemas import InvoiceTypeEnum


def fetch_order_or_invoice_data(need_write: bool = False):
    async def fetch(
            request: Request,
            security_scopes: SecurityScopes,
            object_type: Literal["order", "invoice"] = Path(),
            object_id: int = Path(),
            token: str = Depends(get_token),
    ):
        """
        Fetches a payment object and the associated user data based on the given
        parameters.
        The function processes either an "order" or an "invoice" type object, looking it
        up by
        its ID and validates the associated user or token data.
        """
        token_data = parse_access_token_depend(token, security_scopes)
        scopes = token_data.get("scopes", [])

        user_or_token_data = await get_user_or_token_data(
            request, security_scopes, token, None,
        )
        user = user_or_token_data if isinstance(user_or_token_data, User) else None

        if not user:
            if (
                    token_data.get("type") != object_type or
                    token_data.get("sub") != str(object_id)
            ):
                raise AuthNotAuthorisedError()

            if (
                    f"{object_type}:read" not in scopes or
                    (need_write and f"{object_type}:write" not in scopes)
            ):
                raise AuthNotEnoughPermissionsError()

            user = None
        else:
            if scopes and (
                    f"me:read" not in scopes or
                    (need_write and f"me:write" not in scopes)
            ):
                raise AuthNotEnoughPermissionsError()

        order, invoice = None, None
        match object_type:
            case "order":
                order = await StoreOrder.get(object_id)
                if not order:
                    raise OrderNotFoundError()
            case "invoice":
                invoice = await Invoice.get(object_id)
                if not invoice:
                    raise InvoiceNotFoundError()
            case _:
                raise ValueError(f"Unsupported object_type: {object_type}")

        object = order or invoice

        if not user:
            user = await User.get_by_id(object.user_id)

        order_payment = await StoreOrderPayment.get(**{f"{object_type}_id": object.id})
        if (
                # if user is the order/invoice user — no error
                object.user_id != user.id and
                (
                        # if the payment method is friend and the user is selected
                        # friend — no error
                        not (
                                (
                                        order_payment and
                                        order_payment.payment_method == "friend"
                                ) or
                                (invoice and invoice.is_friend)
                        ) or
                        (order and not order.invoice_id) or
                        not (
                                # if the user is selected friend — no error
                                await is_valid_friend_for_invoice(
                                    order.invoice_id if order else invoice.id,
                                    object.user_id, user.id, True,
                                ) or

                                # if the invoice is for integration and
                                # the user is anonymous —
                                # no error
                                (
                                        invoice and
                                        invoice.invoice_type ==
                                        InvoiceTypeEnum.INTEGRATION and
                                        user.is_anonymous
                                )
                        )
                )
        ):
            raise AuthNotAuthorisedError()

        return order, invoice, user

    return fetch
