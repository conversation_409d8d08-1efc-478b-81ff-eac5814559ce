import logging

from fastapi import (
    APIRouter, Depends, HTTPException, Path, Query, Response, Security,
    status,
)
from psutils.exceptions import ErrorWithTextVariable
from psutils.fastapi.api_route_error_groups import APIRouteErrorGroups

import schemas.invoice.invoice
from core.api.depends import get_current_bot, get_lang
from core.auth.depend import get_user_optional, get_user_or_token_data_safe
from core.invoice import InvoiceService, show_invoice
from core.invoice.invoice_to_schema import invoice_to_schema
from db.models import ClientBot, Invoice, User
from utils.text import c, f

logger = logging.getLogger("debugger.payments.invoice")

router = APIRouter(
    prefix="/invoice",
    tags=['payments/invoice'],
    route_class=APIRouteErrorGroups,
)


@router.get(
    "/byToken",
    responses={
        "error_groups": {
            "groups": [
                "token_error",
                "token_scopes_error",
                "invoice_token_error",
            ]
        }
    }
)
async def get_invoice_by_token(
    service: InvoiceService = Depends(InvoiceService.depend_invoice_token),
    lang: str = Depends(get_lang),
) -> schemas.InvoiceSchema:
    return await invoice_to_schema(service.invoice, lang)


@router.get("/{invoice_id}")
async def get_invoice_by_id(
    invoice_id: int = Path(),
    service: InvoiceService = Depends(InvoiceService.depend_user),
    lang: str = Depends(get_lang),
) -> schemas.InvoiceSchema:
    invoice = await service.load_invoice_by_id(invoice_id)
    return await invoice_to_schema(invoice, lang)


@router.post("/create")
async def create_invoice_web(
    data: schemas.CreateInvoiceWebData,
    user: User | None = Security(get_user_optional, scopes=["me:write"]),
    bot: ClientBot | None = Depends(get_current_bot),
    lang: str = Depends(get_lang),
) -> schemas.InvoiceCreatedResult:
    invoice_service = InvoiceService(user=user)
    return await invoice_service.create_invoice_web(data, bot, lang)


@router.post(
    "/send_to_bot/{invoice_id}",
    description="Method to send invoice to client bot for payment",
    status_code=200,
    responses={
        "error_groups": {
            "groups": [
                "token_error",
                "token_scopes_error",
                "invoice_token_error",
            ]
        }
    }
)
async def send_invoice_to_bot(
    invoice_id: int = Path(description="invoice_id"),
    token_data: User | dict | None = Depends(get_user_or_token_data_safe),
    payment_settings_id: int | None = Query(default=None, description="Payment settings ID for Telegram payment"),
    object_payment_settings_id: int | None = Query(default=None, description="Object payment settings ID for Telegram payment"),
    lang: str = Depends(get_lang),
) -> Response:
    """Метод для відправки рахунку клієнту в бот для оплати"""
    
    # Отримуємо invoice - або за токеном, або напряму за ID
    invoice = None
    user = None
    
    if isinstance(token_data, User):
        user = token_data
        invoice_service = InvoiceService(user=user)
        try:
            invoice = await invoice_service.load_invoice_by_id(invoice_id)
        except:
            pass
    elif token_data and token_data.get("type") == "invoice":
        token_invoice_id = token_data.get("sub")
        
        # Перевіряємо, чи токен належить саме цьому рахунку
        if token_invoice_id != str(invoice_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid invoice token"
            )
            
        # Отримуємо рахунок напряму, оскільки токен перевірено
        invoice = await Invoice.get(invoice_id)
    
    # Якщо рахунок не знайдено, спробуємо знайти його напряму
    if not invoice:
        invoice = await Invoice.get(invoice_id)
        
        # Якщо рахунок існує, перевіримо чи має користувач права на нього
        if invoice and user and invoice.user_id != user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this invoice"
            )
    
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    if invoice.status == "payed" or invoice.status == "processing":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invoice already paid or processing"
        )
    
    # Якщо користувач не визначений, отримаємо його з рахунку
    if not user and invoice.user_id:
        user = await User.get_by_id(invoice.user_id)
        if user:
            lang = await user.get_lang()
    
    try:
        # Створюємо payload для show_invoice, який буде містити дані рахунку
        payload = c("pay_for_invoice", invoice_id=invoice.id)
        
        # Відправляємо інвойс на оплату
        await show_invoice(
            invoice, 
            payload,
            payment_settings_id=payment_settings_id,
            object_payment_settings_id=object_payment_settings_id
        )
    except ErrorWithTextVariable as err:
        err_txt = await f(err.text_variable, lang, **err.text_kwargs)
        logger.error(f"show invoice for payment FAILED\n{err_txt}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=err_txt)
    except Exception as ex:
        logger.error(f"show invoice for payment FAILED\n{str(ex)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ex))
    
    return Response(status_code=200)
