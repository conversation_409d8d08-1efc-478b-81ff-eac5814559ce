import aiofiles
from fastapi import APIRouter, HTTPException

import schemas
from db.models import CustomHTMLPage, MediaObject

router = APIRouter(
    prefix="/pages",
    tags=["pages"]
)


@router.get("/{page_name}")
async def get_page_by_name(
        page_name: str
) -> schemas.Page:
    page = await CustomHTMLPage.get(name=page_name)
    if not page:
        raise HTTPException(404, detail="Page not found")

    if page.body:
        body = page.body
    elif page.html_media_id:
        html_media = await MediaObject.get(page.html_media_id)
        async with aiofiles.open(html_media.file_path, "r") as file:
            body = await file.read()
    else:
        body = ""

    return schemas.Page(
        id=page.id,
        name=page.name,
        head=page.head,
        body=body,
    )
