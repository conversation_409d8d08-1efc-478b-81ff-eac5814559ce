from fastapi import APIRouter, Depends, Security

import schemas
from core.bot.functions import bot_to_schema
from core.exceptions import BotNotFoundError
from core.auth.depend import get_active_user
from core.api.depends import get_current_bot, get_lang
from core.store.depends import get_current_brand
from core.referral.functions import share_and_earn
from db.models import ClientBot, Brand, User

router = APIRouter(
    prefix="/bots",
    tags=["bots"]
)


@router.get("/{bot_id}")
async def get_bot_by_id(
    bot_id: int,
) -> schemas.BotSchema:
    bot = await ClientBot.get(bot_id)
    if not bot:
        raise BotNotFoundError(bot_id)
    return await bot_to_schema(bot)


@router.post("/send_share_and_earn")
async def send_share_and_earn(
    bot: ClientBot | str | None = Depends(get_current_bot),
    brand: Brand | None = Depends(get_current_brand),
    user: User = Security(get_active_user, scopes=["me:write"]),
    lang: str = Depends(get_lang),
) -> schemas.OkResponse:
    await share_and_earn(
        user, brand, lang, bot,
    )
    return schemas.OkResponse()
