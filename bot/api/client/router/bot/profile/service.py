import logging

from fastapi import HTTPException, status

import schemas
from core.group.default_color_schema import get_group_color_schema_with_defaults
from db.models import Brand, ClientBot, ColorSchema, Group

logger = logging.getLogger()


class ProfileService:
    @classmethod
    async def get_color_schema(
            cls, bot: ClientBot | str | None, brand: Brand | None
    ) -> schemas.AppearanceColorSchema:
        if brand:
            group_id = brand.group_id
        elif bot:
            if not isinstance(bot, str):
                group_id = bot.group_id
            else:
                group_id = None
        else:
            group_id = None

        group = await Group.get(group_id) if group_id else None

        if not group:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="group not found")

        color_schema: ColorSchema = await ColorSchema.get(group_id=group.id)

        return get_group_color_schema_with_defaults(color_schema)
