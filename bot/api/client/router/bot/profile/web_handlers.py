from fastapi import Depends, APIRouter, HTTPException
from starlette import status

import schemas
from core.store.depends import get_current_brand
from .service import ProfileService
from core.api.depends import get_current_bot
from db.models import ClientBot, Brand

router = APIRouter(
    prefix="/profile",
    tags=["profile"],
)


@router.get(
    "/color_schema",
    response_description="Method to get profile color schema",
    description="Returns color schema",
)
async def get_color_schema(
        bot: ClientBot | str | None = Depends(get_current_bot),
        brand: Brand | None = Depends(get_current_brand),
        service: ProfileService = Depends(),
) -> schemas.AppearanceColorSchema:
    color_schema = await service.get_color_schema(bot, brand)
    if color_schema:
        return color_schema
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="color schema not found"
    )
