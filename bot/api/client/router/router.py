from fastapi import APIRouter

from . import (
    ai_chat, client_web_pages, external, group, loyalty, menu_in_store, pages, payments, reviews,
    store,
    web,
)
from .bot import bot, profile

router = APIRouter()

router.include_router(ai_chat.router)
router.include_router(profile.router)
router.include_router(bot.router)
router.include_router(group.router)
router.include_router(store.router)
router.include_router(external.router)
router.include_router(loyalty.router)
router.include_router(menu_in_store.router)
router.include_router(payments.router)
router.include_router(reviews.router)
router.include_router(pages.router)
router.include_router(web.router)
router.include_router(client_web_pages.router)
