from schemas.base import BaseORMModel
from typing import Optional


class CustomFieldBaseSchema(BaseORMModel):
    name: str | None = None
    value: str | None = None


class CustomFieldSchema(CustomFieldBaseSchema):
    id: int


class ModifyCustomFieldBaseSchema(CustomFieldBaseSchema):
    client_user_id: Optional[int | str]


class SetCustomFieldSchema(ModifyCustomFieldBaseSchema):
    pass


class UpdateCustomFieldSchema(ModifyCustomFieldBaseSchema):
    field_id: int
