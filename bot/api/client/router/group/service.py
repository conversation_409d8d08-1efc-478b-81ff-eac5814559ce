import schemas

from core.group.functions import group_to_schema, appearance_to_schema
from db import crud
from .exceptions import InvalidProfileApiTokenError

from db.models import Group, AuthSetting


class GroupService:
    async def validate_api_token(self, api_token: str, lang: str):
        group = await crud.get_group_by_api_token(api_token)
        if not group:
            raise InvalidProfileApiTokenError()

        return await group_to_schema(group, lang)

    @classmethod
    async def get_auth_settings(cls, group_id: int) -> schemas.AuthSettings:
        res = schemas.AuthSettings()
        group = await Group.get(group_id)
        if not group:
            return res
        auth_settings = await AuthSetting.get(group_id=group_id)
        if auth_settings:
            res = schemas.AuthSettings.from_orm(auth_settings)

        return res

    @classmethod
    async def get_appearance_settings(cls, group_id: int):
        return await appearance_to_schema(group_id)
