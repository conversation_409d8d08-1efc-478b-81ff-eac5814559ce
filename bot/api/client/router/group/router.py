from fastapi import APIRouter, Depends
from psutils.fastapi.api_route_error_groups import APIRouteErrorGroups

import schemas
from core.api.depends import get_api_token, get_lang
from core.exceptions import GroupNotFoundByIdError
from core.group.functions import group_to_schema
from db.models import Group
from .service import GroupService

router = APIRouter(
    prefix="/groups",
    tags=["groups"],
    route_class=APIRouteErrorGroups,
)


@router.get("/{group_id}")
async def get_group_by_id(
        group_id: int,
        lang: str = Depends(get_lang),
) -> schemas.GroupSchema:
    group = await Group.get(group_id)
    if not group:
        raise GroupNotFoundByIdError(group_id)

    return await group_to_schema(group, lang)


@router.get(
    "/byApiToken",
    responses={
        "error_groups": {
            "groups": ["validate_group_api_token"],
        }
    }
)
async def get_group_by_api_key(
        service: GroupService = Depends(),
        api_token: str = Depends(get_api_token),
        lang: str = Depends(get_lang)
) -> schemas.GroupSchema:
    return await service.validate_api_token(api_token, lang)


@router.get("/{group_id}/auth_settings")
async def get_auth_settings(
        group_id: int,
        service: GroupService = Depends(),
) -> schemas.AuthSettings:
    return await service.get_auth_settings(group_id)


@router.get("/{group_id}/appearance_settings")
async def get_appearance_settings(
        group_id: int,
        service: GroupService = Depends(),
) -> schemas.AppearanceSettings:
    return await service.get_appearance_settings(group_id)
