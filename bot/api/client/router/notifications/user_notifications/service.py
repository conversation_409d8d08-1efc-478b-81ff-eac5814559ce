from core.auth.services.scopes_checker import ScopesCheckerService
from db import crud
from db.models import User
from schemas import (
    NotificationLevel, NotificationRecipientType, SystemNotificationCategory,
    SystemNotificationType,
)


class UserNotificationsService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang

    async def get_user_notifications(
            self,
            offset: int = 0,
            limit: int = 10,
            is_read: bool = None,
            categories: list[SystemNotificationCategory] = None,
            type_notifications: list[SystemNotificationType] = None,
            level: list[NotificationLevel] = None,
    ):
        """Get user notifications"""
        notifications = await crud.get_admin_system_notification_list(
            profile_id=None,  # Not filtering by profile_id for user notifications
            user_id=self.user.id,
            offset=offset,
            limit=limit,
            is_read=is_read,
            categories=categories,
            type_notifications=type_notifications,
            level=level,
            recipient_type=NotificationRecipientType.USER,
            recipient_id=self.user.id,
        )
        
        return notifications
