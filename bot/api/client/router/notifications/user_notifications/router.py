from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header
from sse_starlette import EventSourceResponse
from starlette.requests import Request

from core.api.depends import get_lang
from core.auth.depend import get_token
from core.sse_service.user_notification_service import UserSSEService
from db import DBSession

router = APIRouter()


@router.get("/stream")
async def stream_notifications(
        request: Request,
        x_session_id: str = Header(...),
        token: str = Depends(get_token),
        lang: str = Depends(get_lang),
) -> EventSourceResponse:
    """Endpoint for creating SSE connection for user notifications"""
    try:
        user_sse_service = UserSSEService(
            session_id=x_session_id,
        )

        with DBSession():
            data_result = await user_sse_service.get_data(
                request, token, lang, None
            )
    except Exception as e:
        raise e

    return await user_sse_service.stream_notifications(
        **data_result
    )
