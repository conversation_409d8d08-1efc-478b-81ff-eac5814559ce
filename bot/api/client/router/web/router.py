import re

import aiofiles
from fastapi import APIRouter, Depends
from psutils.text import strip_html_tags
from starlette.requests import Request
from starlette.responses import HTMLResponse

from config import WEB_INDEX_HTML_PATH
from core.store.depends import get_current_brand
from db.models import Brand
from .functions import calculate_meta_tags

router = APIRouter(
    prefix="/webapp",
    include_in_schema=False,
)


@router.get("/")
async def webapp_index(
        request: Request,
        brand: Brand | None = Depends(get_current_brand),
):
    if not WEB_INDEX_HTML_PATH:
        return "WEB_INDEX_HTML_PATH is not set in config"

    async with aiofiles.open(WEB_INDEX_HTML_PATH, "r") as file:
        content = await file.read()

    meta_tags = await calculate_meta_tags(request, brand)

    additional_head_tags = brand.additional_head_tags or "" if brand else ""
    additional_body_tags = brand.additional_body_tags or "" if brand else ""

    meta_tags.title = strip_html_tags(meta_tags.title) + " "
    meta_tags.description = strip_html_tags(meta_tags.description) + " "

    ga_id = brand.analytics_data.get(
        "google_analytics_id"
    ) if brand and brand.analytics_data else None
    pixel_id = brand.analytics_data.get(
        "meta_pixel_id"
    ) if brand and brand.analytics_data else None
    meta_access_token = brand.analytics_data.get(
        "meta_pixel_access_token"
    ) if brand and brand.analytics_data else None
    gtm_id = brand.analytics_data.get(
        "google_tag_manager_id"
    ) if brand and brand.analytics_data else None

    analytics_meta_tags = ""
    if ga_id:
        analytics_meta_tags += (
            f'<meta name="ga-measurement-id" content="{ga_id}">\n'
        )
    if pixel_id:
        analytics_meta_tags += (
            f'<meta name="fb-pixel-id" content="{pixel_id}">\n'
        )
    if meta_access_token:
        analytics_meta_tags += (
            f'<meta name="fb-pixel-access-token" content="{meta_access_token}">\n'
        )

    if gtm_id and f"GTM-{gtm_id}" not in additional_head_tags:
        additional_head_tags += f"""
    <!-- Google Tag Manager -->
    <script>
      (function(w,d,s,l,i){{
        w[l]=w[l]||[]; w[l].push({{'gtm.start': new Date().getTime(), event:'gtm.js'}});
        var f=d.getElementsByTagName(s)[0], j=d.createElement(s), dl=l!='dataLayer'? 
        '&l='+l : '';
        j.async=true; j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
        f.parentNode.insertBefore(j,f);
      }})(window,document,'script','dataLayer','{gtm_id}');
    </script>
    <!-- End Google Tag Manager -->
    """

    if ga_id and "gtag(" not in additional_head_tags:
        additional_head_tags += "\n" + (
            f'<script async src="https://www.googletagmanager.com/gtag/js?id='
            f'{ga_id}"></script>\n'
            '<script>\n'
            '  window.dataLayer = window.dataLayer || [];\n'
            '  function gtag(){dataLayer.push(arguments);} \n'
            "  gtag('js', new Date());\n"
            f"  gtag('config', '{ga_id}');\n"
            '</script>\n'
        )

    if pixel_id and "fbq(" not in additional_head_tags:
        additional_head_tags += "\n" + (
            "<!-- Facebook Pixel Code -->\n"
            '<script>\n'
            '  !function(f,b,e,v,n,t,s)\n'
            '  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?\n'
            '  n.callMethod.apply(n,arguments):n.queue.push(arguments)};\n'
            '  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version="2.0";\n'
            f"  n.pixelId = '{pixel_id}';\n"
            '  n.queue=[];t=b.createElement(e);t.async=!0;\n'
            '  t.src=v;s=b.getElementsByTagName(e)[0];\n'
            '  s.parentNode.insertBefore(t,s)}\n'
            '(window, document,"script",\n'
            '"https://connect.facebook.net/en_US/fbevents.js");\n'
            f"  fbq('init', '{pixel_id}');\n"
            "  fbq('track', 'PageView');\n"
            '</script>\n'
            '<noscript><img height="1" width="1" style="display:none"\n'
            f'  src="https://www.facebook.com/tr?id='
            f'{pixel_id}&ev=PageView&noscript=1"\n'
            '/></noscript>\n'
            "<!-- End Facebook Pixel Code -->\n"
        )

    if gtm_id and f"GTM-{gtm_id}" not in additional_body_tags:
        additional_body_tags += f"""
    <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe src="https://www.googletagmanager.com/ns.html?id={gtm_id}"
              height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->
    """

    content = content.format(
        image=meta_tags.image,
        title=meta_tags.title.replace('"', "'"),
        description=meta_tags.description.replace('"', "'"),
        icon=meta_tags.icon,
    )

    if request.headers.get("sec-fetch-dest") != "iframe":
        content = re.sub(
            r'<script>\s*console\.log\("additional_head_tags"\);\s*</script>',
            additional_head_tags,
            content
        )

        content = re.sub(
            r'<script>\s*console\.log\("additional_body_tags"\);\s*</script>',
            additional_body_tags,
            content
        )

        if analytics_meta_tags:
            content = re.sub(
                r'<!--\s*analytics-ids\s*-->',
                analytics_meta_tags,
                content
            )

    return HTMLResponse(content)
