import asyncio

from fastapi import APIRouter, Depends, HTTPException, Query, WebSocket
from psutils.ai_chat.base import DEFAULT_MODEL
from psutils.ai_chat.schemas import AIChunk, AIMessage, MessageRole
from starlette import status
from starlette.websockets import WebSocketDisconnect

import schemas
from config import OPENAI_API_KEY
from core.ai_chat import AI<PERSON>hat
from core.ai_chat.functions import get_system_message, send_message
from core.custom_texts import ct
from core.store.depends import get_current_brand
from db.models import Brand, ClientBot, Group, Store, User
from loggers import JSONLogger

router = APIRouter(
    prefix="/ai_chat",
    tags=["ai_chat"],
)


@router.websocket("/ws")
async def ai_chat_websocket(
        websocket: WebSocket,
        brand: Brand | None = Depends(get_current_brand),
        lang: str = Query(),
        store_id: int | None = Query(),
        model: str = Query(DEFAULT_MODEL),
        temperature: float = Query(None),
        top_p: float = Query(None),
        algo_query: int = Query(1),
        dist_limit_query: float | None = Query(None),
):
    user: User | None = None

    if not brand:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="currently is not allowed to use AI without brand",
        )

    group = await Group.get(brand.group_id)
    bot = await ClientBot.get(group_id=group.id)
    store = await Store.get(store_id)

    logger = JSONLogger(
        "ai", {
            "brand": brand,
            "group": group,
            "bot": bot,
            "store": store,
        }
    )

    try:
        algo = algo_query
        dist_limit = dist_limit_query

        ai_chat = AIChat()

        await websocket.accept()

        async def get_new_system_message():
            return await get_system_message(group, store, lang)

        history: list[AIMessage] = []
        system_message = await get_new_system_message()

        logger.add_data(
            {
                "history": history,
                "system_message": system_message,
            }
        )

        async def restart():
            history.clear()
            logger.debug(f"Starting conversation")

            text = await ct(group, lang, "ai", "start_message")

            content = ""
            words = text.split(" ")
            for i, word in enumerate(words):
                new_content = word
                if i > 0:
                    new_content += " "
                content += new_content
                await send_message(
                    websocket, schemas.AIChatSocketMessage(
                        from_=schemas.AIChatSocketMessageFromEnum.SERVER,
                        type=schemas.AIChatSocketMessageTypeEnum.CHUNK,
                        chunk=AIChunk(
                            role=MessageRole.ASSISTANT,
                            content=content,
                            new_content=new_content,
                            is_finished=i + 1 == len(words),
                            is_new_message=i == 0,
                        ),
                    )
                )
                await asyncio.sleep(0.05)

            history.append(
                AIMessage(
                    role=MessageRole.ASSISTANT,
                    content=text,
                )
            )
            logger.debug("Start message sent")

        async def run_stream(
                __user_message: str,
                __history: list[AIMessage] = None,
                __system_message: str = None,
        ):
            if __history is None:
                __history = history
            if __system_message is None:
                __system_message = system_message

            async with ai_chat.stream(
                    OPENAI_API_KEY, __user_message,
                    __history, __system_message,
                    model,
                    temperature=temperature,
                    top_p=top_p,
                    brand=brand,
                    group=group,
                    user=user,
                    store=store,
                    bot=bot,
                    lang=lang,
                    websocket=websocket,
                    algo=algo,
                    dist_limit=dist_limit,
            ) as stream:
                async for chunk in stream:
                    await send_message(
                        websocket, schemas.AIChatSocketMessage(
                            from_=schemas.AIChatSocketMessageFromEnum.SERVER,
                            type=schemas.AIChatSocketMessageTypeEnum.CHUNK,
                            chunk=chunk,
                        )
                    )

                history.extend(stream.new_messages)
                logger.debug(
                    "Answered", {
                        "new_messages_count": len(stream.new_messages)
                    }
                )

        await restart()

        logger.debug("Started websocket")

        while True:
            try:
                message: schemas.AIChatSocketMessage = (
                    schemas.AIChatSocketMessage.parse_raw(
                        await websocket.receive_text()
                    ))

                logger.debug(
                    "Received message", {
                        "message": message
                    }
                )

                if message.from_ != schemas.AIChatSocketMessageFromEnum.CLIENT.value:
                    await send_message(
                        websocket, schemas.AIChatSocketMessage(
                            from_=schemas.AIChatSocketMessageFromEnum.SERVER,
                            type=schemas.AIChatSocketMessageTypeEnum.ERROR,
                            error="Message is not from client"
                        )
                    )

                if message.type == schemas.AIChatSocketMessageTypeEnum.RESTART.value:
                    system_message = await get_new_system_message()
                    await restart()
                    continue

                if not getattr(message, message.type):  # type: ignore
                    await send_message(
                        websocket, schemas.AIChatSocketMessage(
                            from_=schemas.AIChatSocketMessageFromEnum.SERVER,
                            type=schemas.AIChatSocketMessageTypeEnum.ERROR,
                            error=f"{message.type.value} is required when type is "
                                  f"{message.type}"
                        )
                    )

                if message.type == schemas.AIChatSocketMessageTypeEnum.PARAM.value:
                    if (message.param ==
                            schemas.AIChatSocketMessageParamEnum.ALGO.value and \
                            isinstance(message.param_value, int)):
                        algo = message.param_value
                    elif (message.param ==
                          schemas.AIChatSocketMessageParamEnum.DIST_LIMIT.value and \
                          isinstance(message.param_value, float | int)):
                        dist_limit = float(message.param_value)
                    continue

                await run_stream(message.text)

            except WebSocketDisconnect:
                logger.debug("Websocket disconnected")
                await websocket.close()
                break
            except Exception as e:
                logger.error("An error occurred while processing websocket message", e)
                await send_message(
                    websocket, schemas.AIChatSocketMessage(
                        from_=schemas.AIChatSocketMessageFromEnum.SERVER,
                        type=schemas.AIChatSocketMessageTypeEnum.ERROR,
                        error=f"Error: {str(e)}"
                    )
                )
                continue

    except WebSocketDisconnect:
        logger.debug("Websocket disconnected")
        await websocket.close()
    except Exception as e:
        logger.error("An error occurred with websocket", e)
