from fastapi import APIRouter, Depends, HTTPException, Security
from starlette import status

import schemas
from config import ANONYMOUS_USER_EMAIL
from core.auth.depend import get_active_user_optional
from core.review.functions import create_review
from core.store.depends import get_current_brand
from db.models import Brand, ClientBot, Group, User

router = APIRouter(
    prefix="/reviews",
    tags=["reviews"]
)


@router.post("/make")
async def make_review(
        data: schemas.MakeReviewData,
        brand: Brand | None = Depends(get_current_brand),
        user: User | None = Security(get_active_user_optional, scopes=["me:write"]),
) -> schemas.Review:
    if not brand:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="brand not found"
        )

    group = await Group.get(brand.group_id)
    bot = await ClientBot.get(group_id=group.id)

    if user:
        current_user = user
    else:
        current_user = await User.get_by_email(ANONYMOUS_USER_EMAIL)

    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="user not found error"
        )

    review = await create_review(
        data.type, schemas.ReviewPrivacyEnum.PRIVATE,
        data.review.dict(), current_user, group, bot,
        text=data.text,
        additional_text=data.additional_text,
        utm_labels=data.utm_labels.dict() if data.utm_labels else None,
    )

    return schemas.Review(
        id=review.id,
        review=review.review,
        text=review.text,
        additional_text=review.additional_text,
        media=review.media,
        utm_labels=review.utm_labels if review.utm_labels else None,
    )
