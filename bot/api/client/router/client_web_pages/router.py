from fastapi import APIRouter, Depends
from fastapi.params import Query
from psutils.translator.schemas import TranslateObjectData

import exceptions
import schemas
from core.api.depends import get_lang
from core.client_web_pages.functions import check_and_create_default_pages
from core.store.depends import get_current_brand
from core.store.product.exceptions import BrandNotFoundError
from db import crud
from db.models import Brand, ClientWebPage, Group
from utils.translator import t, td

router = APIRouter(
    prefix="/client_web_pages",
    tags=["client_web_pages"]
)


@router.get("/")
async def get_client_web_pages_for_client(
        store_id: int | None = Query(None),
        invoice_template_id: int | None = Query(None),
        types: list[schemas.ClientWebPageTypeEnum] | None = Query(None),
        lang: str = Depends(get_lang),
        brand: Brand = Depends(get_current_brand),
) -> list[schemas.ClientWebPageListSchema]:

    if not brand:
        return []

    profile = await Group.get(brand.group_id)

    client_web_pages = await crud.get_client_web_pages_for_client(
        profile_id=profile.id, with_translations=profile.is_translate,
        lang=lang, store_id=store_id, invoice_template_id=invoice_template_id,
        types=types,
    )

    default_page_types = []
    if store_id:
        default_page_types.append(schemas.ClientWebPageTypeEnum.MENU)
    elif invoice_template_id:
        default_page_types.append(schemas.ClientWebPageTypeEnum.FASTPAY)
    else:
        default_page_types.append(schemas.ClientWebPageTypeEnum.STORES)

    result = await check_and_create_default_pages(
        client_web_pages, profile, default_page_types
    )
    if result:
        client_web_pages = await crud.get_client_web_pages_for_client(
            profile_id=profile.id, with_translations=profile.is_translate,
            lang=lang, store_id=store_id, invoice_template_id=invoice_template_id,
            types=types
        )

    to_translate: dict[tuple[int, str], TranslateObjectData] = {}

    formatted_pages = []
    for row in client_web_pages:
        client_web_page = ClientWebPage(
            id=row.id,
            title=row.title,
            button_title=row.button_title,
        )

        translation = getattr(row, "translation", None)

        to_translate[(client_web_page.id, "title")] = TranslateObjectData(
            object=client_web_page,
            translation=translation,
            field_name="title",
        )

        to_translate[(client_web_page.id, "button_title")] = TranslateObjectData(
            object=client_web_page,
            translation=translation,
            field_name="button_title",
        )

        formatted_pages.append(client_web_page)

    translated: dict[tuple[int, str], str] = await td(
        to_translate,
        lang,
        profile.lang,
        group_id=profile.id,
        is_auto_translate_allowed=profile.is_translate,
    )

    return [
        schemas.ClientWebPageListSchema(
            id=client_web_page.id,
            type=client_web_page.type,
            title=translated.get((client_web_page.id, "title"), client_web_page.title),
            button_title=translated.get(
                (client_web_page.id, "button_title"), client_web_page.button_title
            ),
            is_enabled=client_web_page.is_enabled,
            position=client_web_page.position,
            slug=client_web_page.slug,
            internal_name=client_web_page.internal_name,
            profile_id=client_web_page.group_id,
            container_max_width=client_web_page.container_max_width,
            custom_container_max_width=client_web_page.custom_container_max_width,
        ) for client_web_page in client_web_pages
    ]


@router.get("/{slug}")
async def get_client_web_page_for_client_by_slug(
        slug: str,
        lang: str = Depends(get_lang),
        brand: Brand = Depends(get_current_brand),
) -> schemas.ClientWebPageOneSchema:
    if not brand:
        raise BrandNotFoundError()

    profile = await Group.get(brand.group_id)

    client_web_page_row = await crud.get_client_web_page_by_slug_and_profile_id(
        profile_id=profile.id,
        slug=slug,
        with_translations=profile.is_translate,
        lang=lang
    )

    if not client_web_page_row:
        raise exceptions.ClientWebPageNotFoundBySlugError(slug)

    client_web_page: ClientWebPage = client_web_page_row[0]
    translation = getattr(client_web_page_row, "Translation", None)

    schema = schemas.ClientWebPageOneSchema(
        **client_web_page.as_dict(),
        stores=await crud.get_client_web_page_store_ids(client_web_page.id),
        invoice_templates=(
            await crud.get_client_web_page_invoice_template_ids(client_web_page.id)
        )
    )

    await t(
        client_web_page,
        lang, profile.lang,
        translation=translation,
        group_id=profile.id,
        is_auto_translate_allowed=profile.is_translate,
        result_obj=schema
    )

    return schema


@router.get("/type/{type}")
async def get_client_web_page_for_client_by_type(
        type: schemas.ClientWebPageTypeEnum,
        store_id: int | None = Query(None),
        invoice_template_id: int | None = Query(None),
        lang: str = Depends(get_lang),
        brand: Brand = Depends(get_current_brand),
) -> schemas.ClientWebPageOneSchema:
    if not brand:
        raise BrandNotFoundError()

    profile = await Group.get(brand.group_id)

    client_web_page_row = await crud.get_client_web_page_by_type_and_profile_id(
        profile_id=profile.id,
        type=type,
        with_translations=profile.is_translate,
        lang=lang,
        store_id=store_id,
        invoice_template_id=invoice_template_id
    )

    if not client_web_page_row:
        raise exceptions.ClientWebPageNotFoundByTypeError(page_type=type.value)

    client_web_page: ClientWebPage = client_web_page_row[0]
    translation = getattr(client_web_page_row, "Translation", None)

    schema = schemas.ClientWebPageOneSchema(
        **client_web_page.as_dict(),
        stores=await crud.get_client_web_page_store_ids(client_web_page.id),
        invoice_templates=(
            await crud.get_client_web_page_invoice_template_ids(client_web_page.id)
        )
    )

    await t(
        client_web_page,
        lang, profile.lang,
        translation=translation,
        group_id=profile.id,
        is_auto_translate_allowed=profile.is_translate,
        result_obj=schema
    )

    return schema
