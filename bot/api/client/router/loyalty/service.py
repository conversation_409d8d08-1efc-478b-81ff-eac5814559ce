from db import crud
from db.models import <PERSON><PERSON><PERSON>t, EWalletMerchant, LoyaltySettings
from schemas import LoyaltySettingsData
from schemas.loyalty_settings import LoyaltySettingsResponseSchema
from .schemas import LoyaltyDetectionRequest, LoyaltyDetectionResponse


class LoyaltyDetectionService:
    """Universal service for determining loyalty settings and product code."""

    @staticmethod
    async def determine_loyalty_settings(
        request: LoyaltyDetectionRequest
    ) -> LoyaltyDetectionResponse:
        """
        Universal function to determine loyalty settings and product code
        based on various parameters.
        
        Priority logic:
        1. If transfer_data + ewallet_id: search merchant by QR codes
           - If merchant found and has loyalty -> use merchant loyalty
           - If merchant found but no loyalty -> use ewallet loyalty + merchant product_code
           - If merchant not found -> use ewallet loyalty + generate product_code
        2. If other params: use existing loyalty detection logic
        """
        
        # Initialize default response
        response = LoyaltyDetectionResponse(
            loyalty_settings_id=None,
            loyalty_settings=None,
            product_code="",
        )
        
        try:
            # Case 1: EWallet payment flow (needs product_code determination)
            if request.is_ewallet_external_payment and request.ewallet_id:
                if request.transfer_data:
                    # QR payment - search merchant
                    return await LoyaltyDetectionService._handle_ewallet_qr_payment(
                        request.ewallet_id, request.transfer_data
                    )
                else:
                    # API payment - use profile-based product_code
                    return await LoyaltyDetectionService._handle_ewallet_api_payment(
                        request.ewallet_id, request.profile_id
                    )
            
            # Case 3: Standard loyalty detection (profile, store, template, etc.)
            else:
                return await LoyaltyDetectionService._handle_standard_detection(request)
                
        except Exception as e:
            response.error_message = f"Error determining loyalty: {str(e)}"
            return response

    @staticmethod
    async def _handle_ewallet_qr_payment(
        ewallet_id: int, transfer_data: str
    ) -> LoyaltyDetectionResponse:
        """Handle loyalty detection for EWallet QR payments."""
        
        # Get EWallet and its bot to get profile_id
        ewallet = await EWallet.get(ewallet_id)
        if not ewallet:
            raise ValueError(f"EWallet {ewallet_id} not found")
        
        # Get ClientBot to access group_id (which is profile_id)
        from db.models import ClientBot
        bot = await ClientBot.get(ewallet.bot_id)
        if not bot:
            raise ValueError(f"Bot {ewallet.bot_id} not found for EWallet {ewallet_id}")
        
        profile_id = bot.group_id
        
        # Search for merchant by QR codes
        merchant = await LoyaltyDetectionService._find_merchant_by_qr(
            ewallet_id, transfer_data
        )
        
        if merchant:
            # Merchant found - check if it has loyalty settings
            merchant_loyalty = await LoyaltySettings.get(ewallet_merchant_id=merchant.id)
            product_code = merchant.incust_check_item_code
            
            if merchant_loyalty:
                # Merchant has loyalty settings
                return LoyaltyDetectionResponse(
                    loyalty_settings_id=merchant_loyalty.id,
                    loyalty_settings=LoyaltyDetectionService._to_schema(merchant_loyalty),
                    product_code=product_code,
                    loyalty_source_type="merchant",
                    loyalty_source_id=merchant.id,
                )
            else:
                # Merchant has no loyalty but has product code, use EWallet loyalty + merchant product code
                ewallet_loyalty = await LoyaltySettings.get(ewallet_id=ewallet.id)
                
                return LoyaltyDetectionResponse(
                    loyalty_settings_id=ewallet_loyalty.id if ewallet_loyalty else None,
                    loyalty_settings=LoyaltyDetectionService._to_schema(ewallet_loyalty),
                    product_code=product_code,
                    loyalty_source_type="ewallet" if ewallet_loyalty else None,
                    loyalty_source_id=ewallet.id if ewallet_loyalty else None,
                )
        else:
            # Merchant not found, use EWallet loyalty + generated product code
            ewallet_loyalty = await LoyaltySettings.get(ewallet_id=ewallet.id)
            
            # Generate product code: спец префікс + код профіля через EWallet→Bot→Group
            product_code = f"ewallet-{profile_id}"
            
            return LoyaltyDetectionResponse(
                loyalty_settings_id=ewallet_loyalty.id if ewallet_loyalty else None,
                loyalty_settings=LoyaltyDetectionService._to_schema(ewallet_loyalty),
                product_code=product_code,
                loyalty_source_type="ewallet" if ewallet_loyalty else None,
                loyalty_source_id=ewallet.id if ewallet_loyalty else None,
            )

    @staticmethod
    async def _handle_ewallet_api_payment(
        ewallet_id: int, profile_id: int | None
    ) -> LoyaltyDetectionResponse:
        """Handle loyalty detection for API-created EWallet payments."""
        
        ewallet = await EWallet.get(ewallet_id, is_deleted=False, is_enabled=True)
        if not ewallet:
            raise ValueError(f"EWallet {ewallet_id} not found")
        
        # Get ClientBot to access group_id (which is profile_id)
        from db.models import ClientBot
        bot = await ClientBot.get(ewallet.bot_id)
        if not bot:
            raise ValueError(f"Bot {ewallet.bot_id} not found for EWallet {ewallet_id}")
        
        # Use EWallet loyalty
        ewallet_loyalty = await LoyaltySettings.get(ewallet_id=ewallet.id)
        
        # Generate product code: спец префікс + код профілю який створює цю еволет оплату
        creator_profile_id = profile_id or bot.group_id
        product_code = f"ewallet-api-{creator_profile_id}"
        
        return LoyaltyDetectionResponse(
            loyalty_settings_id=ewallet_loyalty.id if ewallet_loyalty else None,
            loyalty_settings=LoyaltyDetectionService._to_schema(ewallet_loyalty),
            product_code=product_code,
            loyalty_source_type="ewallet" if ewallet_loyalty else None,
            loyalty_source_id=ewallet.id if ewallet_loyalty else None,
        )

    @staticmethod
    async def _handle_standard_detection(
        request: LoyaltyDetectionRequest
    ) -> LoyaltyDetectionResponse:
        """Handle standard loyalty detection for store/template/product contexts."""
        
        # Use existing loyalty detection logic
        loyalty_data = LoyaltySettingsData(
            ewallet_id=request.ewallet_id,
            brand_id=None,  # Will be determined from context
            store_id=request.store_id,
            product_id=request.product_id,
            invoice_template_id=request.invoice_template_id,
            profile_id=request.profile_id
        )
        
        # Determine context type based on provided parameters

        if request.ewallet_id:
            context_type="ewallet"
        elif request.product_id:
            context_type = "product"
        elif request.invoice_template_id:
            context_type = "invoice_template"
        elif request.store_id:
            context_type = "store"
        elif request.profile_id:
            context_type = "profile"
        else:
            context_type = "brand"
        
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            context_type, loyalty_data
        )
        
        # Generate product code only for EWallet external payments
        if request.is_ewallet_external_payment:
            if request.product_id:
                product_code = f"product-{request.product_id}"
            elif request.store_id:
                product_code = f"store-{request.store_id}"
            elif request.invoice_template_id:
                product_code = f"invoice-template-{request.invoice_template_id}"
            else:
                product_code = f"profile-{request.profile_id or 'unknown'}"
        else:
            # For non-EWallet contexts, product_code is not needed
            product_code = ""
        
        # Визначити тип та ID джерела лояльності
        loyalty_source_type = None
        loyalty_source_id = None
        
        if loyalty_settings:
            if loyalty_settings.product_id:
                loyalty_source_type = "product"
                loyalty_source_id = loyalty_settings.product_id
            elif loyalty_settings.store_id:
                loyalty_source_type = "store"
                loyalty_source_id = loyalty_settings.store_id
            elif loyalty_settings.invoice_template_id:
                loyalty_source_type = "invoice_template"
                loyalty_source_id = loyalty_settings.invoice_template_id
            elif loyalty_settings.ewallet_id:
                loyalty_source_type = "ewallet"
                loyalty_source_id = loyalty_settings.ewallet_id
            elif loyalty_settings.profile_id:
                loyalty_source_type = "profile"
                loyalty_source_id = loyalty_settings.profile_id
            else:
                loyalty_source_type = "brand"
                loyalty_source_id = loyalty_settings.brand_id
        
        return LoyaltyDetectionResponse(
            loyalty_settings_id=loyalty_settings.id if loyalty_settings else None,
            loyalty_settings=LoyaltyDetectionService._to_schema(loyalty_settings),
            product_code=product_code,
            loyalty_source_type=loyalty_source_type,
            loyalty_source_id=loyalty_source_id,
        )

    @staticmethod
    async def _find_merchant_by_qr(
        ewallet_id: int, transfer_data: str
    ) -> EWalletMerchant | None:
        """Find merchant by QR codes in transfer_data."""
        
        # Extract merchant code from transfer_data
        merchant_code = LoyaltyDetectionService._extract_merchant_code_from_qr(transfer_data)
        if not merchant_code:
            return None
        
        # Get all merchants for this EWallet
        merchants = await EWalletMerchant.get_list(
            ewallet_id=ewallet_id, is_deleted=False,
        )
        
        for merchant in merchants:
            if merchant.qrcodes:
                # Check if any QR code from merchant matches extracted merchant code
                # qrcodes is a JSON list of strings
                for qr_code in merchant.qrcodes:
                    if qr_code and qr_code.strip() == merchant_code:
                        return merchant
        
        return None
    
    @staticmethod
    def _extract_merchant_code_from_qr(transfer_data: str) -> str | None:
        """Extract merchant code from QR transfer data."""
        import json
        import urllib.parse
        
        try:
            # Case 1: WAVE URL format - https://pay.wave.com/transfer?merchant=123456
            if transfer_data.startswith("https://pay.wave.com"):
                parsed_url = urllib.parse.urlparse(transfer_data)
                params = urllib.parse.parse_qs(parsed_url.query)
                merchant_param = params.get("merchant", [])
                if merchant_param:
                    return merchant_param[0]
            
            # Case 2: Orange JSON format - {"scope": "MLITE", "id": "45687"}
            elif transfer_data.strip().startswith("{") and transfer_data.strip().endswith("}"):
                data = json.loads(transfer_data)
                if data.get("scope") == "MLITE" and "id" in data:
                    return data["id"]
            
            # Case 3: Simple merchant code (fallback for old format)
            else:
                return transfer_data.strip()
                
        except Exception:
            # If parsing fails, try to use transfer_data as-is
            return transfer_data.strip() if transfer_data else None
        
        return None

    @staticmethod
    def _to_schema(loyalty_settings: LoyaltySettings | None) -> LoyaltySettingsResponseSchema | None:
        """Convert LoyaltySettings model to LoyaltySettingsSchema."""
        if not loyalty_settings:
            return None
        
        return LoyaltySettingsResponseSchema(
            id=loyalty_settings.id,
            type_client_auth=loyalty_settings.type_client_auth,
            prohibit_redeeming_bonuses=loyalty_settings.prohibit_redeeming_bonuses,
            prohibit_redeeming_coupons=loyalty_settings.prohibit_redeeming_coupons,
            loyalty_applicable_type=loyalty_settings.loyalty_applicable_type,
            is_enabled=loyalty_settings.is_enabled,
        )