from pydantic import BaseModel

from schemas.loyalty_settings import (
    LoyaltySettingsResponseSchema,
)


class LoyaltyDetectionRequest(BaseModel):
    ewallet_id: int | None = None
    transfer_data: str | None = None
    profile_id: int | None = None
    store_id: int | None = None
    invoice_template_id: int | None = None
    product_id: int | None = None
    is_ewallet_external_payment: bool = False  # Для визначення чи потрібен product_code


class LoyaltyDetectionResponse(BaseModel):
    loyalty_settings_id: int | None
    loyalty_settings: LoyaltySettingsResponseSchema | None
    product_code: str
    merchant_id: int | None = None
    error_message: str | None = None
    
    # Інформація про джерело лояльності
    loyalty_source_type: str | None = None  # "merchant", "ewallet", "store", "profile", "product", "invoice_template"
    loyalty_source_id: int | None = None  # ID відповідного об'єкта