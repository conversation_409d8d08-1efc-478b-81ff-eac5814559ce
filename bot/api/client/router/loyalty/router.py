import traceback
from fastapi import APIRouter, Query

from loggers import <PERSON><PERSON>NLogger
from .schemas import LoyaltyDetectionRequest, LoyaltyDetectionResponse
from .service import LoyaltyDetectionService

router = APIRouter(prefix="/loyalty", tags=["Loyalty Detection"])


@router.get(
    "/detect",
    response_model=LoyaltyDetectionResponse,
    summary="Detect appropriate loyalty settings",
    description="""
    Universal endpoint to determine appropriate loyalty settings and product code
    based on various parameters.
    
    Use cases:
    1. EWallet QR payments: ewallet_id + transfer_data
    2. EWallet API payments: ewallet_id + profile_id (optional)
    3. Standard contexts: profile_id, store_id, template_id, etc.
    """
)
async def detect_loyalty_settings(
    ewallet_id: int | None = Query(None, description="EWallet ID for payment context"),
    transfer_data: str | None = Query(None, description="QR code data to find merchant"),
    profile_id: int | None = Query(None, description="Profile ID for context"),
    store_id: int | None = Query(None, description="Store ID for context"),
    invoice_template_id: int | None = Query(None, description="Invoice template ID for context"),
    product_id: int | None = Query(None, description="Product ID for context"),
    is_ewallet_external_payment: bool = Query(False, description="True for EWallet external payments that need product_code")
) -> LoyaltyDetectionResponse:
    """
    Detect appropriate loyalty settings based on provided parameters.
    
    Returns loyalty_settings_id, full loyalty settings data, product_code
    and optional merchant_id if found.
    """
    
    request = LoyaltyDetectionRequest(
        ewallet_id=ewallet_id,
        transfer_data=transfer_data,
        profile_id=profile_id,
        store_id=store_id,
        invoice_template_id=invoice_template_id,
        product_id=product_id,
        is_ewallet_external_payment=is_ewallet_external_payment
    )
    
    logger = JSONLogger("loyalty.detect", {
        "request": request.dict(),
    })

    try:
        result = await LoyaltyDetectionService.determine_loyalty_settings(request)
        logger.debug("Successfully detected loyalty settings", {"result": result.dict()})
        return result
    except Exception as e:
        # Логуємо помилку з повним трейсом
        error_details = {
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc(),
        }
        logger.error("Error determining loyalty settings", error_details)

        # Return error response with details
        return LoyaltyDetectionResponse(
            loyalty_settings_id=None,
            loyalty_settings=None,
            product_code=None,  # Змінено з "" на None
            merchant_id=None,
            error_message=f"Error determining loyalty: {str(e)}",
            loyalty_source_type=None,
            loyalty_source_id=None,
        )