from enum import Enum

from pydantic import BaseModel


class PosterWebhookObject(str, Enum):
    INCOMING_ORDER = "incoming_order"
    TRANSACTION = "transaction"
    CLIENT_PAYED_SUM = "client_payed_sum"
    APP = "application"
    CLIENT_BONUS = "client_bonus"


class PosterWebhookAction(str, Enum):
    ADDED = "added"
    CHANGED = "changed"
    CLOSED = "closed"
    PAYED = "payed"
    TEST = "test"


class PosterWebhookData(BaseModel):
    account: str
    object: PosterWebhookObject
    object_id: int
    action: PosterWebhookAction
    time: str
    verify: str
    account_number: str | None = None
    data: str | None = None
