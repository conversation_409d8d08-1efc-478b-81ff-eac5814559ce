import logging

from .poster_funcs import check_client_secret, set_poster_status_order
from .schemas import PosterWebhookData, PosterWebhookObject


async def handler_poster_webhook_(
        brand_id: int,
        post_data: PosterWebhookData
) -> dict:
    logger = logging.getLogger('debugger')
    logger.debug(post_data)

    if not await check_client_secret(brand_id, post_data):
        return {"status": "invalid"}

    if post_data.object.value not in (
            PosterWebhookObject.TRANSACTION.value,
            PosterWebhookObject.INCOMING_ORDER.value,
    ):
        return {"status": "not implement"}

    try:
        poster_status_order = await set_poster_status_order(
            brand_id,
            post_data.object.value,
            post_data.action.value,
            post_data.object_id,
            post_data.data,
        )
    except Exception as err:
        logger.error(err)
        poster_status_order = str(err)
    return {"status": poster_status_order}
