import hashlib
import json
import logging

from .schemas import PosterWebhookAction, PosterWebhookData, PosterWebhookObject
from core.ext.adapters.poster import PosterApiClient
from core.ext.types import ExternalType
from core.store.order.service import change_store_order_status
from db import crud
from db.models import (
    ExternalOrderRef,
)
from schemas import OrderShippingStatusEnum

logger = logging.getLogger()


async def _get_poster_order_by_transaction(brand_id, external_id) -> int:
    external_order_ref = await ExternalOrderRef.get(
        brand_id=brand_id,
        external_id=external_id,
        external_type=ExternalType.POSTER.value,
    )
    if not external_order_ref:
        raise Exception('external order ref not found')
    return external_order_ref.order_id


async def _get_poster_order_ref_id(brand_id: int, external_id: int) -> int:
    poster_settings = await crud.get_poster_settings(brand_id)
    poster_client = PosterApiClient(brand_id, poster_settings.poster_api_token)

    online_order = await poster_client.get_order_status_pos(external_id)
    poster_transaction_id = online_order.get('transaction_id')
    return poster_transaction_id


async def _get_poster_app_setting(brand_id):
    pass


async def check_client_secret(brand_id: int, post_data: PosterWebhookData) -> bool:
    result = False
    try:
        # client_secret = '4e31dc9339dfce1fb56dca17b00a5887'
        client_secret = await crud.get_poster_setting(brand_id, "poster_app_secret")
        if not client_secret:
            raise Exception('poster client secret not found')

        verify_original = post_data.verify
        verify = [
            post_data.account,
            post_data.object.value,
            post_data.object_id,
            post_data.action.value
        ]

        if post_data.data:
            verify.append(post_data.data)

        verify.extend(
            [
                post_data.time,
                client_secret
            ]
        )

        verify_str = ';'.join(str(item) for item in verify)
        verify_hash = hashlib.md5(verify_str.encode()).hexdigest()

        if verify_hash == verify_original:
            result = True
    except Exception as err:
        logger.error(f'{err}')
    finally:
        return result


async def set_poster_status_order(
        brand_id: int,
        type_action: str,
        action: str,
        external_id: int,
        data: str
) -> str:
    result = 'failed'
    order = None

    try:
        data_ = json.loads(data)
    except:
        data_ = {}

    if type_action == PosterWebhookObject.INCOMING_ORDER.value:
        if action == PosterWebhookAction.ADDED.value:
            logger.debug(f'poster order {external_id} added to POS')
            result = 'ok'
            return result
        order = await crud.get_order_by_external_order(
            brand_id,
            external_id,
            ExternalType.POSTER.value,
        )
        if not order:
            raise Exception('order not found')

        match action:
            case PosterWebhookAction.CHANGED.value:
                if data_.get('type') and data_.get('type') == 1:
                    # await order.cancel('poster')
                    await change_store_order_status(
                        order,
                        OrderShippingStatusEnum.CANCELED.value,
                        "external",
                        None,
                        'poster webhook'
                    )
                else:
                    poster_transaction_id = await _get_poster_order_ref_id(
                        brand_id, external_id
                    )
                    await ExternalOrderRef.save(
                        brand_id, external_id, poster_transaction_id,
                        ExternalType.POSTER.value
                    )
                    await change_store_order_status(
                        order,
                        OrderShippingStatusEnum.OPEN_CONFIRMED.value,
                        "external",
                        None,
                        'poster webhook'
                    )
            case PosterWebhookAction.CLOSED.value:
                await change_store_order_status(
                    order,
                    OrderShippingStatusEnum.CLOSED.value,
                    "external",
                    None,
                    'poster webhook'
                )
            case _:
                raise Exception(
                    f'status {PosterWebhookObject.INCOMING_ORDER.value}.{action} not '
                    f'implement'
                )
    elif type_action == PosterWebhookObject.TRANSACTION.value:
        match action:
            case PosterWebhookAction.CLOSED.value:
                try:
                    if data_['transactions_history']['value2'] > 0:
                        external_id = await _get_poster_order_by_transaction(
                            brand_id,
                            external_id,
                        )
                        order = await crud.get_order_by_external_order(
                            brand_id,
                            external_id,
                            ExternalType.POSTER.value,
                        )
                        if not order:
                            raise Exception('order not found')
                        if order.status_pay in ('must_pay', 'processing') \
                                and order.payment_method != 'online':
                            await change_store_order_status(
                                order,
                                OrderShippingStatusEnum.PAYED.value,
                                "external",
                                None,
                                'poster webhook'
                            )
                except Exception as err:
                    err_str = f'handle close poster transaction failed {err}'
                    logger.error(err_str)
                    raise Exception(err_str)
            case PosterWebhookAction.CHANGED.value:
                if 'transactions_history' in data_ and \
                        'type_history' in data_['transactions_history'] \
                        and data_['transactions_history'][
                    'type_history'] != 'changeprocessingstatus':
                    raise Exception(
                        f"for {PosterWebhookObject.TRANSACTION.value}.{action} "
                        f"type_history not changeprocessingstatus"
                    )
                try:
                    external_id = await _get_poster_order_by_transaction(
                        brand_id,
                        external_id,
                    )
                    order = await crud.get_order_by_external_order(
                        brand_id,
                        external_id,
                        ExternalType.POSTER.value,
                    )
                    if not order:
                        raise Exception('order not found')
                    if data_['transactions_history']['value'] == 30:
                        new_delivery_status = (
                            OrderShippingStatusEnum.WAIT_FOR_SHIP.value)
                    elif data_['transactions_history']['value'] == 40:
                        new_delivery_status = OrderShippingStatusEnum.SHIPPED.value
                    elif data_['transactions_history']['value'] == 50:
                        new_delivery_status = OrderShippingStatusEnum.DELIVERED.value
                    else:
                        raise Exception(
                            f"not known value ["
                            f"{data_['transactions_history']['value']}] for "
                            f"hangeprocessingstatus"
                        )
                    await change_store_order_status(
                        order,
                        new_delivery_status,
                        "external",
                        None,
                        'poster webhook'
                    )
                except Exception as err:
                    err_str = (f'handle transactions_history changeprocessingstatus '
                               f'failed {err}')
                    logger.error(err_str)
                    raise Exception(err_str)
            case _:
                raise Exception(
                    f'status {PosterWebhookObject.TRANSACTION.value}.{action} not '
                    f'implement'
                )

    if order:
        result = order.status

    return result
