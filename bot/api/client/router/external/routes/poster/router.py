from fastapi import (
    APIRouter,
    Response,
)

from .schemas import PosterWebhookData
from .service import handler_poster_webhook_

router = APIRouter(tags=['external/poster'])


@router.get(
    '/poster/webhook/brands/{brand_id}',
    description='Method to answer check poster webhook',
)
async def check_poster_webhook(brand_id: int):
    return Response(status_code=200)


@router.post(
    '/poster/webhook/brands/{brand_id}',
    description='Method to get poster webhook',
)
async def handler_poster_webhook(
        brand_id: int,
        data: PosterWebhookData
):
    return await handler_poster_webhook_(brand_id, data)
