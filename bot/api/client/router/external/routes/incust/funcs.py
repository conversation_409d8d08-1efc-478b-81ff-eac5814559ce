import logging

from sqlalchemy import select

from config import (
    TOKEN_SERIALIZER,
    TOKEN_VALID_DAYS,
    TOKEN_VERSION,
)
from core.loyalty.customer_service import get_or_create_incust_customer
from core.loyalty.incust_api import incust
from db import crud, db_func, sess
from db.models import Brand, IncustCustomer, User
from schemas import LoyaltySettingsData

logger = logging.getLogger("debugger.incust.message")


async def renew_invalid_push_token(incust_customer: IncustCustomer):
    """Оновлює неvalidний push токен для InCust customer з використанням нових
    клієнтів API."""

    if incust_customer:
        user = await User.get_by_id(incust_customer.user_id)
        brand = await Brand.get(incust_customer.brand_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            LoyaltySettingsData(brand_id=brand.id),
        )

        if not loyalty_settings:
            raise ValueError(f"Loyalty settings not found for brand {brand.id}")

        incust_customer = await get_or_create_incust_customer(
            user=user,
            loyalty_settings=loyalty_settings,
            lang="en"
        )

        if not incust_customer:
            raise ValueError(f"Incust_customer not found for user: {user.id}")

        return {
            "brand_id": brand.id,
            "user_id": user.id,
            "white_label_id": loyalty_settings.white_label_id,
            "api_url": loyalty_settings.server_url,
            "user_token": incust_customer.token,
            "version": 1,
            "push_token": incust_customer.push_token,
        }


@db_func
def get_customers_from_tokens(invalid_tokens: list):
    """Отримує customers з бази по токенах."""
    stmt = select(IncustCustomer).where(IncustCustomer.push_token.in_(invalid_tokens))
    return sess().scalars(stmt).all()


async def push_token_validate(auth_str: str) -> dict | None:
    """Валідує push токен."""
    # 1. Token time validate
    result_token = TOKEN_SERIALIZER.loads(
        auth_str, max_age=(TOKEN_VALID_DAYS * 24 * 60 * 60)
    )
    result_token["push_token"] = auth_str

    logger.debug(f"{result_token=}")

    # 2. Token version
    if not result_token.get('version', None) or result_token[
        'version'] != TOKEN_VERSION:
        logger.error(
            f"Token version not valid: {result_token.get('version', None)}, {result_token}"
        )
        return {"error": "Token version not valid"}

    return result_token


async def del_incust_push_token(auth_str: str, result_token: dict):
    """Видаляє push токен з InCust через новий API клієнт."""
    wl_id = result_token.get("white_label_id")
    api_url = result_token.get("api_url")
    user_token = result_token.get("user_token")
    brand_id = result_token.get("brand_id")
    user_id = result_token.get("user_id")

    if not all([auth_str, wl_id, api_url, user_token, brand_id, user_id]):
        raise ValueError(
            f"Не встановлено одне з необхідних значень: {wl_id=} {api_url=} {user_token=} {brand_id=} {user_id=}"
        )

    loyalty_settings = await crud.get_loyalty_settings_for_context(
        "brand",
        LoyaltySettingsData(brand_id=brand_id),
    )

    if not loyalty_settings:
        raise ValueError(f"Loyalty settings not found for brand {brand_id}")

    user = await User.get_by_id(user_id)
    if not user:
        raise ValueError(f"User not found: {user_id}")

    try:
        async with incust.client.PushApi(loyalty_settings, user=user, lang="en") as api:
            result = await api.push_token_del(
                token=auth_str,
                platform="android"
            )

        logger.info(f"Successfully deleted push token for user {user_id}")
        return result

    except Exception as e:
        logger.error(f"Failed to delete push token for user {user_id}: {e}")
        raise Exception(f"Push token deletion failed: {str(e)}") from e
