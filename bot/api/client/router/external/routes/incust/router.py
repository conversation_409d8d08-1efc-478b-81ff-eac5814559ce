import asyncio
import base64
import logging
import uuid

import imgkit
from fastapi import (
    API<PERSON><PERSON><PERSON>,
    Body,
    Depends,
    HTTPException,
    Path,
    Query,
    Response,
    Security,
)
from incust_api.api import client, term
from incust_client_api_client import (
    AccountsRec, Coupon, LoyaltySettings,
    ReferralProgramCodeInfo,
)
from incust_client_api_client.models import (
    Coupon as ClientCoupon, CouponClientAddRequest, CouponsAddedResponse, GiftCard,
    MessageResponse,
    ReferralProgramChain, ReferralProgramCode, ReferralProgramInvitationRequest,
    ReferralProgramSummary, Transaction,
)
from incust_terminal_api_client.models import Coupon as TerminalCoupon, Settings

import schemas
from api.functions import get_external_system_by_token
from core.api.depends import get_external_token, get_lang
from core.auth.depend import (
    get_active_user, get_active_user_optional, get_user_optional,
    get_user_or_token_data_safe,
)
from core.auth.parse_token import parse_token
from core.bot.ewallet_handlers import process_single_ewallet_parallel
from core.external_coupon import send_coupon_info
from core.funcs import handle_standard_exceptions
from core.incust.functions import get_user_loyalty_card
from core.incust.message_sender.schemas import InCustMessage
from core.loyalty.incust_api import incust
from core.store.depends import get_current_brand
from core.store.functions.brand import get_brand_scan_receipts_settings
from core.templater import templater
from core.whatsapp.keyboards import send_wa_main_menu
from db import crud
from db.models import (
    Brand,
    ClientBot,
    Group,
    StoreOrder,
    User,
)
from schemas import (
    EWalletCustomerAccount,
    IncustCouponExt, IncustUserCardTemplate,
    IncustWallet, StorePayloadBaseSchema,
)
from schemas.incust.customer_account import GetCustomerAccountsScope
from schemas.incust.transaction import ProcessIncustCheckPayloadSchema
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from .schemas import IncustUserCardQrSchema, VerifyUserPayloadSchema, VerifyUserSchema
from .service import send_message

router = APIRouter(
    tags=['external/incust'],
    prefix="/incust"
)

debugger = logging.getLogger("debugger.external.incust")


class AccountsRecExt(AccountsRec):
    ewallet: EWalletCustomerAccount | None = None


class IncustWalletsExt(IncustWallet):
    accounts: list[AccountsRecExt] | None = None


@router.post(
    '/message',
    description='Method to send incust message',
)
async def send_incust_message(
        data: InCustMessage,
):
    return await send_message(data)


@router.api_route(
    "/verify_user",
    methods=["GET", "POST"],
    response_model=VerifyUserSchema,
    description='Method to verify user for incust',
    include_in_schema=False,
)
async def verify_user_for_incust(
        data: VerifyUserPayloadSchema = Body(),
        external_token: str = Depends(get_external_token),
        lang: str = Depends(get_lang),
):
    try:
        external_system = await get_external_system_by_token(external_token)
        if not external_system:
            logging.error("verify_user_for_incust: Error verify external system")
            raise HTTPException(status_code=400, detail="Error verify external system")

        payload, scopes = parse_token(data.token)
        if not payload:
            logging.error("verify_user_for_incust: Error parse token")
            raise HTTPException(status_code=400, detail="Error parse token")

        if payload.get("type") != "user":
            logging.error("verify_user_for_incust: Invalid token type")
            raise HTTPException(status_code=400, detail="Invalid token type")

        user: User = await User.get_by_id(id=int(payload.get("sub")))
        if not user:
            logging.error("verify_user_for_incust: Error get user")
            raise HTTPException(status_code=400, detail="Error get user")

        brand_id = payload.get("brand_id")

        if brand_id:
            brand = await Brand.get(brand_id)
            if not brand:
                logging.error("verify_user_for_incust: Brand not found")
                raise HTTPException(status_code=400, detail="Brand not found")
        else:
            brand = None

        if user.uuid_token is None:
            await user.update(uuid_token=uuid.uuid4().hex)
            if user.uuid_token is None:
                logging.error("verify_user_for_incust: Error creating external uuid")
                raise HTTPException(
                    status_code=400, detail="Error creating external uuid"
                )

        group = await Group.get(brand.group_id)
        bot = await ClientBot.get(group_id=group.id)

        kwargs = {
            "name": str(user.name),
            "external_id": user.incust_external_id,
            "language": await user.get_lang(bot.id if bot else None),
        }

        if user.email:
            try:
                loyalty_settings = await crud.get_loyalty_settings_for_context(
                    "brand",
                    schemas.LoyaltySettingsData(brand_id=brand_id, profile_id=group.id)
                )

                if loyalty_settings:
                    async with incust.term.CustomerApi(loyalty_settings, lang=lang) as api:
                        card_info = await api.cardinfo(
                            user.email,
                            "email"
                        )

                    if (
                            not card_info or
                            not hasattr(card_info, 'customer') or
                            not card_info.customer or
                            not hasattr(card_info.customer, 'user_email') or
                            not card_info.customer.user_email or
                            (hasattr(card_info.customer, 'user_external_id') and
                             card_info.customer.user_external_id ==
                             user.incust_external_id)
                    ):
                        kwargs["email"] = user.email

            except Exception as e:
                logging.error(
                    f"Failed to get card info for user {user.id}: {e}",
                    exc_info=True
                )

        response = VerifyUserSchema(**kwargs)

        debugger.debug(
            f"*** creating incust user by trusted/token response user name: "
            f"{response.name}, user_id: {user.id}"
        )
        debugger.debug(
            f"*** creating incust user by trusted/token full response: "
            f"{response.dict()}"
        )
        return response
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        logging.error(e, exc_info=True)
        await send_message_to_platform_admins(
            f"An error occurred while verify incust user: {e}\n"
            f"{external_token=}\n{data=}"
        )
        raise HTTPException(status_code=400, detail=str(e))


@router.post(
    "/process_check/{brand_id}",
    response_model=term.m.Check,
    description='Method to process incust check',
)
async def process_check(
        _: int = Path(alias="brand_id"),
        user_data: dict | User = Security(get_user_or_token_data_safe),
        brand: Brand = Depends(get_current_brand),
        lang: str = Depends(get_lang),
        data: ProcessIncustCheckPayloadSchema = Body(),
):

    user = None
    if isinstance(user_data, User):
        user = user_data

    try:
        # If loyalty_settings_id is provided, use it directly
        if data.loyalty_settings_id:
            from db.models import LoyaltySettings
            loyalty_settings = await LoyaltySettings.get(data.loyalty_settings_id)
            if not loyalty_settings:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Loyalty settings with ID {data.loyalty_settings_id} not found"
                )
        else:
            # Use existing context-based detection
            context_type = "invoice_template" if data.invoice_template_id else ("store" if data.store_id else "brand")
            loyalty_settings = await crud.get_loyalty_settings_for_context(
                context_type,
                schemas.LoyaltySettingsData(
                    brand_id=brand.id,
                    store_id=data.store_id,
                    invoice_template_id=data.invoice_template_id,
                    profile_id=brand.group_id
                )
            )

            if not loyalty_settings:
                raise HTTPException(status_code=404, detail="Loyalty settings not found")

        scan_settings = await get_brand_scan_receipts_settings(brand.id)
        if (
                scan_settings.enabled and
                not data.rules_type and
                not scan_settings.enabled_all_rules
        ):
            rules_type = "without-rules"
        else:
            rules_type = data.rules_type

        user_to_pass = user if isinstance(user_data, User) else None
        check_data = data
        check_data.id = user_to_pass.incust_external_id if user_to_pass else None
        check_data.id_type = term.m.IdType("external-id") if user_to_pass else None

        async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
            if rules_type:
                result_check = await api.terminal_process_check_by_rules(
                    rules_type, term.m.Check(
                        **check_data.dict(
                            exclude_unset=True,
                            exclude={"rules_type", "store_id", "invoice_template_id", "loyalty_settings_id"}
                        )
                    )
                )
            else:
                result_check = await api.terminal_process_check(term.m.Check(
                        **check_data.dict(
                            exclude_unset=True,
                            exclude={"rules_type", "store_id", "invoice_template_id", "loyalty_settings_id"}
                        )
                    )
                )

        if not result_check:
            raise HTTPException(status_code=500, detail="Failed to process check")

        if result_check.emitted_coupons:
            for coupon in result_check.emitted_coupons:
                if not coupon.image and coupon.batch.image:
                    coupon.image = coupon.batch.image

        return result_check
    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/settings/{brand_id}",
    response_model=Settings,
    description='Method to get incust settings',
)
async def get_incust_settings(
        _: int = Path(alias="brand_id"),
        brand: Brand = Depends(get_current_brand),
        lang: str = Depends(get_lang),
        store_id: int | None = Query(default=None),
        invoice_template_id: int | None = Query(default=None),
):
    try:
        group = await Group.get(brand.group_id)

        context_type = "invoice_template" if invoice_template_id else ("store" if store_id else "brand")
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            context_type,
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                profile_id=group.id,
                store_id=store_id,
                invoice_template_id=invoice_template_id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this context"
            )

        async with incust.term.SettingsApi(loyalty_settings, lang=lang) as api:
            terminal_settings = await api.settings()

        if not terminal_settings:
            raise HTTPException(status_code=404, detail="Incust settings not found")

        return terminal_settings
    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/user_qr_card/{brand_id}",
    response_model=IncustUserCardQrSchema,
    description='Method to get incust user qr card',
)
async def get_incust_user_card_qr(
        _: int = Path(alias="brand_id"),
        brand: Brand = Depends(get_current_brand),
        user: User = Security(get_active_user, scopes=["me:write"]),
        lang: str = Depends(get_lang),
        store_id: int | None = Query(default=None)
):
    try:
        # Get group for shared function
        group = await Group.get(brand.group_id)
        if not group:
            raise HTTPException(
                status_code=404,
                detail="Group not found for brand"
            )

        # Use shared function and convert BytesIO to base64 for API
        qr_result = await get_user_loyalty_card(
            user=user,
            group=group,
            lang=lang,
            store_id=store_id
        )

        if not qr_result:
            raise HTTPException(
                status_code=404,
                detail="Could not generate user loyalty card"
            )

        # Convert BytesIO to base64 string for API response
        qr_base64 = ""
        if qr_result.photo:
            qr_result.photo.seek(0)  # Reset BytesIO position
            qr_base64 = base64.b64encode(qr_result.photo.read()).decode('utf-8')

        return IncustUserCardQrSchema(
            qr=qr_base64,
            temp_code=qr_result.temp_code or ""
        )

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)
        return None


@router.get(
    "/{group_id}/card_image",
    response_class=Response,
    description='Method to get incust user card image',
)
async def get_incust_user_card_image(
        photo: str | None = Query(None, description='Card photo link'),
        card_code: str = Query(..., description='Card id'),
        username: str = Query(..., description='User name'),
        qr: str = Query(..., description='Qr code base64'),
        title: str = Query(..., description='Card title'),
        group_id: int = Path()
):
    try:
        path_wkhtmltoimage = '/usr/bin/wkhtmltoimage'
        config = imgkit.config(wkhtmltoimage=path_wkhtmltoimage)
        template = IncustUserCardTemplate(
            username=username,
            photo=photo,
            code=card_code,
            qr=f"data:image/png;base64,{qr}",
            title=title,
        )
        html = await templater.make_template(template, group_id)

        options = {
            'format': 'png',
            'crop-h': '500',
            'crop-w': '300',
            'encoding': "UTF-8",
        }

        img = imgkit.from_string(html, False, options=options, config=config)
    except Exception as ex:
        logging.error(ex, exc_info=True)
        raise HTTPException(status_code=400, detail=str(ex))

    return Response(content=img, media_type="image/png")


@router.get(
    "/get_coupon_info/{brand_id}/{coupon_id}",
    response_model=ClientCoupon,
    description='Method to get incust coupon info',
)
async def get_coupon_info(
        brand_id: int = Path(),
        coupon_id: str = Path(),
        lang: str = Depends(get_lang),
):
    try:
        brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        async with incust.client.CouponApi(loyalty_settings, lang=lang) as api:
            coupon = await api.coupon_info(code=coupon_id)

        if not coupon:
            raise HTTPException(status_code=404, detail="Coupon not found")

        return coupon

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/{brand_id}/coupons/{coupon_id}",
    response_model=ClientCoupon,
    description='Method to get incust coupon info by ID',
)
async def get_coupon_by_id(
        brand_id: int = Path(),
        coupon_id: str = Path(),
        user: User | None = Depends(get_user_optional) or None,
        lang: str = Depends(get_lang),
):
    try:
        brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
            coupon = await api.coupon_get(coupon_id=coupon_id)

        if not coupon:
            raise HTTPException(status_code=404, detail="Coupon not found")

        return coupon

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid coupon ID format")
    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.post(
    "/add_coupon_to_wallet/{brand_id}/{coupon_code}",
    response_model=CouponsAddedResponse,
    description='Method to add coupon to user wallet',
)
async def add_coupon_to_wallet(
        brand_id: int = Path(),
        coupon_code: str = Path(),
        user: User = Security(get_active_user, scopes=["me:read"]),
        lang: str = Depends(get_lang),
        data: StorePayloadBaseSchema | None = Body(default=None),
):
    try:
        brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        group = await Group.get(brand.group_id)

        store_id = None
        if data and data.store_id:
            store_id = data.store_id

        context_type = "store" if store_id else "brand"
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            context_type,
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id,
                store_id=store_id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        coupon_body = CouponClientAddRequest(
            code=coupon_code,
            type="other"  # за замовчуванням
        )

        async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
            result = await api.coupon_client_add(body=coupon_body)

        if not result:
            raise HTTPException(
                status_code=400,
                detail=await f("add coupon to wallet failed text", lang)
            )

        return result

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.post(
    "/redeem/{brand_id}/{coupon_id}",
    response_model=MessageResponse,
    description='Method to redeem coupon',
)
async def redeem_voucher(
        brand_id: int = Path(),
        coupon_id: str = Path(),
        user: User = Security(get_active_user, scopes=["me:read"]),
        lang: str = Depends(get_lang),
):
    try:
        brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
            result = await api.coupon_client_redeem(coupon_id=coupon_id)

        if not result:
            raise HTTPException(
                status_code=400,
                detail=await f("redeem coupon failed text", lang)
            )

        if hasattr(result, 'code') and result.code != 0:
            raise HTTPException(
                status_code=400,
                detail=result.description or await f("redeem coupon failed text", lang)
            )

        return result

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid coupon ID format")
    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.post(
    "/redeem_term/{brand_id}/{coupon_id}",
    response_model=list[TerminalCoupon],
    description='Method to redeem coupon via terminal',
)
async def redeem_voucher_term(
        brand_id: int = Path(),
        coupon_id: str = Path(),
        user: User = Security(get_active_user, scopes=["me:read"]),
        lang: str = Depends(get_lang),
):
    try:
        brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        # Використовуємо terminal client для redeem купона (термінальний API)
        # Створюємо об'єкт купона для redeem
        coupon_for_redeem = TerminalCoupon(code=coupon_id)

        # Викупляємо купон через терміналовий API
        try:
            async with incust.term.CouponsApi(loyalty_settings, lang=lang) as api:
                result = await api.customer_coupon_redeem(
                    body=[coupon_for_redeem],
                    user_id_value=user.incust_external_id,
                    user_id_type="external-id"
                )
        except term.ApiException as ex:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to redeem coupon via terminal: {ex.reason}"
            )

        if not result:
            raise HTTPException(
                status_code=400,
                detail=await f("redeem coupon failed text", lang)
            )

        return result

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/get_referral_code/{brand_id}",
    response_model=ReferralProgramCode,
    description='Method to get incust referral code',
)
async def get_referral_code(
        brand_id: int = Path(),
        lang: str = Depends(get_lang),
        user: User = Security(get_active_user, scopes=["me:read"]),
        brand: Brand = Depends(get_current_brand),
):
    try:
        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings or not loyalty_settings.loyalty_id:
            raise HTTPException(status_code=404, detail="incust loyalty id not found")

        async with incust.client.LoyaltyApi(loyalty_settings, user=user, lang=lang) as api:
            ref_code = await api.referral_program_code_get(loyalty_id=loyalty_settings.loyalty_id)

        if not ref_code:
            raise HTTPException(status_code=404, detail="Referral code not found")

        return ref_code

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid loyalty ID format")
    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.post(
    "/accept_invitation/{brand_id}/{code}",
    response_model=MessageResponse,
    description='Method to accept invitation',
)
async def accept_invitation(
        brand_id: int = Path(),
        lang: str = Depends(get_lang),
        user: User = Security(get_active_user, scopes=["me:read"]),
        code: str = Path(),
        brand: Brand = Depends(get_current_brand),
        data: StorePayloadBaseSchema | None = Body(default=None)
):
    is_valid_ref_code = code.startswith('5') and len(code) == 12
    if not is_valid_ref_code:
        raise HTTPException(
            status_code=400,
            detail=await f("incust loyalty not valid referral code", lang)
        )

    if not brand:
        brand = await Brand.get(brand_id)
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")

    store_id = None
    if data and data.store_id:
        store_id = data.store_id

    try:
        group = await Group.get(brand.group_id)

        context_type = "store" if store_id else "brand"
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            context_type,
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id,
                store_id=store_id
            )
        )

        if not loyalty_settings or not loyalty_settings.loyalty_id:
            raise HTTPException(status_code=404, detail="Loyalty settings not found")

        invitation_request = (
            ReferralProgramInvitationRequest(
                action="accept"
            ))

        async with incust.client.LoyaltyApi(loyalty_settings, user=user, lang=lang) as api:
            result = await api.referral_program_invitation(
                code=code,
                referral_program_invitation_request=invitation_request
            )

        if not result:
            raise HTTPException(
                status_code=400,
                detail=await f("accept invitation failed text", lang)
            )

        return result

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/loyalty_settings",
    response_model=LoyaltySettings,
    description='Method to get incust loyalty settings',
)
async def get_loyalty_settings(
        lang: str = Depends(get_lang),
        brand: Brand = Depends(get_current_brand),
):
    try:
        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(status_code=404, detail="incust loyalty id not found")

        async with incust.client.LoyaltyApi(loyalty_settings, lang=lang) as api:
            loyalty_settings_response = await api.loyalty_settings(loyalty_id=loyalty_settings.loyalty_id)

        if not loyalty_settings_response:
            raise HTTPException(status_code=404, detail="Loyalty settings not found")

        return loyalty_settings_response

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/invitation/{brand_id}/{code}",
    response_model=ReferralProgramCodeInfo,
    description='Method to get incust invitation info',
)
async def get_invitation_info(
        brand_id: int = Path(),
        lang: str = Depends(get_lang),
        code: str = Path(),
        brand: Brand = Depends(get_current_brand),
):
    is_valid_ref_code = code.startswith('5') and len(code) == 12
    if not is_valid_ref_code:
        raise HTTPException(
            status_code=400,
            detail=await f("incust loyalty not valid referral code", lang)
        )

    if not brand:
        brand = await Brand.get(brand_id)
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")

    try:
        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings or not loyalty_settings.loyalty_id:
            raise HTTPException(status_code=404, detail="Loyalty settings not found")

        async with incust.client.LoyaltyApi(loyalty_settings, lang=lang) as api:
            invitation_info = await api.referral_program_invitation_info(code=code)

        if not invitation_info:
            raise HTTPException(status_code=404, detail="Invitation not found")

        return invitation_info

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/referral_summary/{brand_id}",
    response_model=ReferralProgramSummary,
    description='Method to get incust user referral summary',
)
async def get_referral_summary(
        brand_id: int = Path(),
        lang: str = Depends(get_lang),
        brand: Brand = Depends(get_current_brand),
        user: User = Security(get_active_user, scopes=["me:read"]),
):
    try:
        if not brand:
            brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings or not loyalty_settings.loyalty_id:
            raise HTTPException(status_code=404, detail="incust loyalty id not found")

        async with incust.client.LoyaltyApi(loyalty_settings, user=user, lang=lang) as api:
            summary = await api.referral_program_summary(loyalty_id=loyalty_settings.loyalty_id)

        if not summary:
            raise HTTPException(status_code=404, detail="Referral summary not found")

        return summary

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/referral_chain/{brand_id}",
    response_model=ReferralProgramChain,
    description='Method to get incust user referral chain',
)
async def get_referral_chain(
        brand_id: int = Path(),
        with_rewards: bool = Query(False),
        lang: str = Depends(get_lang),
        brand: Brand = Depends(get_current_brand),
        user: User = Security(get_active_user, scopes=["me:read"]),
):
    try:
        if not brand:
            brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
            )
        )

        if not loyalty_settings or not loyalty_settings.loyalty_id:
            raise HTTPException(status_code=404, detail="incust loyalty id not found")

        async with incust.client.LoyaltyApi(loyalty_settings, user=user, lang=lang) as api:
            chain = await api.referral_program_chain(
                loyalty_id=loyalty_settings.loyalty_id,
                rewards=with_rewards,
            )

        if not chain:
            raise HTTPException(status_code=404, detail="Referral chain not found")

        return chain

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/{brand_id}/coupons",
    response_model=list[Coupon],
    description='Method to get incust user coupons',
)
async def get_user_coupons(
        brand_id: int = Path(),
        ids: str | None = None,
        user: User = Security(get_active_user, scopes=["me:read"]),
        lang: str = Depends(get_lang),
):
    try:
        brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")
        coupons = []

        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        # Парсимо ids якщо вони передані
        coupon_ids = []
        if ids:
            coupon_ids = [id_str.strip() for id_str in ids.split(',') if id_str.strip()]

        if coupon_ids:
            # Отримуємо конкретні купони по ID
            coupons = []
            for coupon_id in coupon_ids:
                try:
                    async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
                        coupon = await api.coupon_get(coupon_id=coupon_id)
                    if coupon:
                        coupons.append(coupon)
                except client.ApiException as ex:
                    debugger.warning(f"Failed to get coupon {coupon_id}: {ex.reason}")
                    continue
        else:
            async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
                coupons = await api.coupon_get()


        return coupons if coupons else []

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/{brand_id}/transactions/{transaction_id}",
    response_model=Transaction,
    description='Method to get incust check (transaction)',
)
async def get_transaction(
        brand_id: int = Path(),
        transaction_id: str = Path(),
        user: User = Security(get_active_user, scopes=["me:read"]),
        lang: str = Depends(get_lang),
):
    try:
        brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        from incust_client_api_client.api.transaction_api import TransactionApi

        async with incust.client.TransactionApi(loyalty_settings, user=user, lang=lang) as api:
            transaction = await api.get_transaction(transaction_id=transaction_id)

        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")

        return transaction

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/{brand_id}/share/{coupon_id}",
    response_model=MessageResponse,
    description='Method to share coupon',
)
async def share_coupon(
        brand_id: int = Path(),
        coupon_id: str = Path(),
        user: User = Security(get_active_user, scopes=["me:read"]),
        lang: str = Depends(get_lang),
):
    try:
        brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
            result = await api.coupon_client_share(coupon_id=coupon_id)

        if not result:
            raise HTTPException(
                status_code=400,
                detail=await f("share coupon failed text", lang)
            )

        return result

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.delete(
    "/{brand_id}/coupons/{coupon_id}",
    response_model=MessageResponse,
    description='Method to delete coupon',
)
async def delete_coupon(
        brand_id: int = Path(),
        coupon_id: str = Path(),
        user: User = Security(get_active_user, scopes=["me:write"]),
        lang: str = Depends(get_lang),
):
    try:
        brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
            result = await api.coupon_delete(coupon_id=coupon_id)

        if not result:
            raise HTTPException(
                status_code=400,
                detail=await f("delete coupon failed text", lang)
            )

        return result

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/special_account",
    response_model=list[term.m.SpecialAccount],
    description='Method to get special accounts'
)
async def get_special_accounts(
        scope: GetCustomerAccountsScope = "retail",
        brand: Brand = Depends(get_current_brand),
        lang: str = Depends(get_lang),
        store_id: int | None = Query(default=None),
):
    try:
        group = await Group.get(brand.group_id)

        context_type = "store" if store_id else "brand"
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            context_type,
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                profile_id=group.id,
                store_id=store_id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this context"
            )

        async with incust.term.BusinessApi(loyalty_settings, lang=lang) as api:
            accounts = await api.special_accounts(scope=scope)

        return accounts if accounts else []

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/special_accounts_mapping",
    response_model=dict[str, term.m.SpecialAccount],
    description='Method to get special accounts mapping'
)
async def get_special_accounts_mapping(
        scope: GetCustomerAccountsScope = "retail",
        brand: Brand = Depends(get_current_brand),
        lang: str = Depends(get_lang),
        store_id: int | None = Query(default=None),
):
    try:
        group = await Group.get(brand.group_id)

        context_type = "store" if store_id else "brand"
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            context_type,
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                profile_id=group.id,
                store_id=store_id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this context"
            )

        async with incust.term.BusinessApi(loyalty_settings, lang=lang) as api:
            accounts = await api.special_accounts(scope=scope)

        return {
            special_account.id: special_account
            for special_account in accounts
        } if accounts else {}

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/wallet",
    response_model=list[IncustWalletsExt] | None,
    description='Method to get user wallet with ewallets'
)
async def get_user_wallet(
        brand: Brand = Depends(get_current_brand),
        user: User = Security(get_active_user, scopes=["me:write"]),
        lang: str = Depends(get_lang),
):
    try:
        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this context"
            )

        debugger.debug(f"get_user_wallet: user={user.id}, brand={brand.id}")

        ewallet_result_dict = {}

        async with incust.client.WalletApi(loyalty_settings, user=user, lang=lang) as api:
            wallets = await api.wallet(group_by_business=False)


        bot = await crud.get_bot_by_brand(brand.id)
        ewallets = await crud.get_ewallet_list(bot_id=bot.id, user_id=user.id)  if bot else None

        wallets_ext = []

        if ewallets:
            tasks = [process_single_ewallet_parallel(ewallet[0].id, user.id, lang) for ewallet in
                     ewallets]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            for result in results:
                if isinstance(result, BaseException):
                    debugger.error(f"EWallet processing error: {result}")
                    continue
                ewallet_result_dict[
                    result.ewallet.incust_account_id
                ] = EWalletCustomerAccount(
                    ewallet_id=result.ewallet.id,
                    incust_account_id=result.ewallet.incust_account_id,
                    title=result.ewallet.name,
                    invoice_template_id=result.ewallet.invoice_template_id,
                    message=result.message,
                    is_user_can_topup=result.is_user_can_topup,
                    currency=result.ewallet.currency,
                    is_user_special_account=result.is_user_special_account,
                )

            # Track which ewallet_result_dict items have been processed
            processed_ewallet_ids = set()

            for wallet_ in wallets or []:
                wallet = IncustWalletsExt(**wallet_.dict())
                if wallet.accounts:
                    for account in wallet.accounts:
                        if account.id in ewallet_result_dict:
                            if ewallet_result_dict[account.id].invoice_template_id:
                                # account = AccountsRecExt(**account.dict())
                                account.ewallet = ewallet_result_dict[account.id]
                            processed_ewallet_ids.add(account.id)
                wallets_ext.append(wallet)

                # Add any remaining ewallets that weren't matched to existing accounts
                for ewallet_id, ewallet_account in ewallet_result_dict.items():
                    if ewallet_id not in processed_ewallet_ids:
                        # Create a new CustomerSpecialAccount for unmatched ewallet
                        new_account = AccountsRecExt(
                            id=ewallet_account.incust_account_id,
                            title=ewallet_account.title,
                            amount=0,
                            currency=ewallet_account.currency,
                            ewallet=ewallet_account,
                            type="money",
                        )

                        # Initialize accounts list if it doesn't exist
                        if wallet.accounts is None:
                            wallet.accounts = []

                        wallet.accounts.append(new_account)
                        processed_ewallet_ids.add(ewallet_id)

        return wallets_ext
    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/coupons/{coupon_id_or_code}/process",
    response_model=IncustCouponExt,
    description='Method to get incust coupon info',
)
async def process_coupon(
        user: User | None = Security(
            get_active_user_optional, scopes=["me:write"]
        ) or None,
        brand: Brand = Depends(get_current_brand),
        coupon_id_or_code: str = Path(),
        lang: str = Depends(get_lang),
        store_id: int | None = Query(default=None),
        invoice_template_id: int | None = Query(default=None),
        action: str | None = Query(default=None, description="Action to perform: 'apply' for redeem attempt, None for view"),
):
    try:
        context_type = "invoice_template" if invoice_template_id else ("store" if store_id else "brand")
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            context_type,
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                store_id=store_id,
                invoice_template_id=invoice_template_id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this context"
            )

        from core.loyalty.coupon_processor import CouponProcessor

        processor = CouponProcessor(loyalty_settings, user, lang)
        response = await processor.process_coupon(coupon_id_or_code, action=action)
        
        # Логування для дебагу
        debugger.info(
            f"process_coupon: coupon_id={coupon_id_or_code}, "
            f"is_in_wallet={response.is_in_wallet}, "
            f"message={response.message}, "
            f"has_coupon={bool(response.coupon)}"
        )

        # Відправляємо купон користувачу ТІЛЬКИ якщо купон було додано до гаманця (не був там раніше)
        # Якщо is_in_wallet=True, то це означає що купон просто застосовується і не потрібно його відправляти
        # Також перевіряємо що є message (це означає що купон був доданий)
        # Відправляємо сповіщення тільки якщо:
        # 1. Є користувач і у нього є контактні дані
        # 2. Отримано дані купона
        # 3. Купон НЕ був в гаманці раніше (is_in_wallet=False) - це означає що він був доданий зараз
        should_send_notification = (
            user and (user.wa_phone or user.chat_id) and 
            response.coupon and 
            not response.is_in_wallet
        )
        
        debugger.info(
            f"Should send coupon notification: {should_send_notification}, "
            f"reason: user={bool(user)}, "
            f"has_contact={bool(user and (user.wa_phone or user.chat_id)) if user else False}, "
            f"has_coupon={bool(response.coupon)}, "
            f"not_in_wallet={not response.is_in_wallet}"
        )
        
        if should_send_notification:
            
            from core.loyalty import coupon_service

            # Використовуємо coupon_service для збагачення купона
            show_coupon = await coupon_service.prepare_coupon_for_display(
                response.coupon,  # Передаємо оригінальний об'єкт
                brand,
                loyalty_settings,
                user,
                lang,
            )

            bot = await crud.get_bot_by_brand(brand.id)
            if bot and show_coupon:
                debugger.info(f"Sending coupon notification to user {user.id} via {bot.bot_type}")
                await send_coupon_info(
                    show_coupon, user, bot, lang, brand.id, store_id=store_id
                )
                if bot.bot_type == "whatsapp" and user.wa_phone:
                    await send_wa_main_menu(bot, lang, user)
            else:
                debugger.warning(f"Cannot send coupon: bot={bool(bot)}, show_coupon={bool(show_coupon)}")
        else:
            debugger.info("Skipping coupon notification - coupon already in wallet or not added")

        return response

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/coupons/{coupon_id_or_code}/check",
    response_model=IncustCouponExt,
    description='Method to get incust coupon info',
)
async def check_coupon(
        user_or_order_token: User | dict | None = Depends(get_user_or_token_data_safe),
        brand: Brand = Depends(get_current_brand),
        coupon_id_or_code: str = Path(),
        lang: str = Depends(get_lang),
):
    user = None
    debugger.debug(f"check_coupon: {user_or_order_token=}")
    if isinstance(user_or_order_token, User):
        user = user_or_order_token
    elif isinstance(user_or_order_token, dict) and user_or_order_token.get('sub'):
        order = await StoreOrder.get(int(user_or_order_token['sub']))
        if order:
            user = await User.get_by_id(order.user_id)

    debugger.debug(f"check_coupon: {user=} {coupon_id_or_code=}")

    try:
        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this context"
            )

        from core.loyalty.coupon_processor import CouponProcessor

        processor = CouponProcessor(loyalty_settings, user, lang)
        return await processor.check_coupon(coupon_id_or_code)

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    "/gift_card/{gift_card_id}",
    response_model=GiftCard,
    description='Method to get incust gift card info by ID',
)
async def get_gift_card_by_id(
        gift_card_id: str = Path(),
        brand: Brand = Depends(get_current_brand),
        user: User | None = Depends(get_user_optional) or None,
        lang: str = Depends(get_lang),
):
    try:
        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        async with incust.client.GiftCardApi(loyalty_settings, user=user, lang=lang) as api:
            gift_card = await api.gift_card_info(code=gift_card_id)

        if not gift_card:
            raise HTTPException(status_code=404, detail="Gift card not found")

        return gift_card

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.post(
    "/gift_card/{gift_card_code}",
    response_model=CouponsAddedResponse,
    description='Method to add gift card to user',
)
async def redeem_gift_card(
        brand: Brand = Depends(get_current_brand),
        gift_card_code: str = Path(),
        user: User = Security(get_active_user, scopes=["me:read"]),
        lang: str = Depends(get_lang),
):
    try:
        group = await Group.get(brand.group_id)
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
            result = await api.coupon_client_add(
                coupon_client_add_request=CouponClientAddRequest(
                    code=gift_card_code,
                    type="gift-card"
                )
            )

        if not result:
            raise HTTPException(
                status_code=400,
                detail=await f("redeem coupon failed text", lang)
            )

        return result

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    '/{brand_id}/wlclient/coupons',
    response_model=list[Coupon],
    description='Method to get incust coupons by ids',
)
async def get_wlclient_coupons_by_ids(
        brand_id: int = Path(),
        ids: list[str] = Query(...),
        user: User = Security(get_active_user, scopes=["me:read"]),
        lang: str = Depends(get_lang),
):
    try:
        brand = await Brand.get(brand_id)
        if not brand:
            raise HTTPException(status_code=404, detail="Brand not found")

        group = await Group.get(brand.group_id)

        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "brand",
            schemas.LoyaltySettingsData(
                brand_id=brand_id,
                profile_id=group.id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this brand"
            )

        coupons = []
        for coupon_id in ids:
            try:
                async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
                    coupon = await api.coupon_get(coupon_id=coupon_id)
                if coupon:
                    coupons.append(coupon)
            except client.ApiException as ex:
                debugger.warning(f"Failed to get wlclient coupon {coupon_id}: {ex.reason}")
                continue

        return coupons

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get(
    '/all_user_coupons',
    response_model=list[client.m.Coupon],
)
async def get_incust_all_user_coupons(
        user: User = Security(get_active_user, scopes=["me:read"]),
        brand: Brand = Depends(get_current_brand),
        lang: str = Depends(get_lang),
        store_id: int | None = Query(default=None),
        coupon_ids: list[str] | None = Query(default=None, description="List of coupon IDs to filter")
):
    try:

        context_type = "store" if store_id else "brand"
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            context_type,
            schemas.LoyaltySettingsData(
                brand_id=brand.id,
                store_id=store_id
            )
        )

        if not loyalty_settings:
            raise HTTPException(
                status_code=404,
                detail="Loyalty settings not found for this context"
            )

        async with incust.client.CouponApi(loyalty_settings, user=user, lang=lang) as api:
            # Якщо передано список ID - використовуємо coupons_list з фільтром
            coupons = await api.coupons_list(ids=coupon_ids if coupon_ids else None)
        
        return coupons if coupons else []

    except Exception as ex:
        await handle_standard_exceptions(ex, lang)


@router.get("/settings/by_context")
async def get_incust_settings_by_context(
        params: schemas.GetLoyaltySettingsByContextParams = Query(),
) -> schemas.LoyaltySettingsSchema:
    loyalty_settings = await crud.get_loyalty_settings_for_context(
        params.target,
        schemas.LoyaltySettingsData(**params.dict(exclude_unset=True))
    )
    return schemas.LoyaltySettingsSchema.from_orm(loyalty_settings)


# @router.post("/process_check",
#     response_model=term.m.Check,
#     description='Method to process incust check',
#     )
# async def process_check_by_settings(
#         _: int = Path(alias="brand_id"),
#         user_data: dict | User = Security(get_user_or_token_data_safe),
#         brand: Brand = Depends(get_current_brand),
#         lang: str = Depends(get_lang),
#         data: schemas.ProcessIncustCheckPayloadWithSettingsSchema = Body(),
# ):
#     pass
