from collections import defaultdict

from fastapi import HTTPException, status

import schemas
from core.ext.adapters.poster import check_poster_stocks
from core.geo.functions import distance
from core.invoice.functions import is_valid_friend_for_invoice
from core.store.functions.brand import brand_to_schema
from core.store.functions.cart_funcs import get_cart_data_from_token
from core.store.product.exceptions import (
    AttributeNotFoundError,
    IncorrectAttributeGroupError, InvalidTokenOrderError, NoWritePermissionToOrderError,
    NotHaveAccessToOrderError, OrderNotFoundOrderError, TokenNotFoundOrderError,
    UserNotFoundError,
)
from db import crud
from db.crud import get_products_for_check
from db.models import (
    Brand, Store, StoreAttribute, StoreAttributeGroup, StoreCart, StoreFavorite,
    StoreOrder, StoreOrderPayment, StoreProduct, Translation, User,
)
from utils.text import f
from .routes.funcs import check_products_is_available


async def get_cart_from_token(
        token_data: dict | None,
        store_id: int | None = None
) -> StoreCart:
    is_user, obj_id = get_cart_data_from_token(token_data, store_id)
    if not is_user and obj_id:
        cart = await StoreCart.get(obj_id)
    elif is_user and obj_id:
        cart = await StoreCart.get_by_user(obj_id, store_id)
    else:
        cart = None

    return cart


async def get_order_from_token(
        order_id: int,
        token_data: User | dict | None,
        need_write: bool = False,
) -> StoreOrder:
    if not token_data:
        raise TokenNotFoundOrderError()

    order = await StoreOrder.get(order_id)
    if not order:
        raise OrderNotFoundOrderError()

    user: User | None = None
    if isinstance(token_data, User):
        user = token_data
    elif token_data.get("type") == "user":
        user = await User.get_by_id(token_data.get("sub"))
        if not user:
            raise UserNotFoundError(user_id=token_data.get("sub"))
    elif token_data.get("type") == "order" or token_data.get("is_order") is True:
        token_order_id = token_data.get("sub")

        if need_write and "order:write" not in token_data.get("scopes", []):
            raise NoWritePermissionToOrderError()

        if token_order_id != str(order.id):
            raise InvalidTokenOrderError()

    order_payment = await StoreOrderPayment.get(order_id=order.id)
    if user:
        if need_write and "me:write" not in user.get_allowed_scopes():
            raise NoWritePermissionToOrderError()

        if (
                order_payment and order_payment.payment_method == 'friend' and
                order.user_id != user.id
                and not await is_valid_friend_for_invoice(
            order.invoice_id, order.user_id, user.id, need_write
        )):
            raise NotHaveAccessToOrderError()

        if (order_payment and order_payment.payment_method != 'friend' and
                order.user_id != user.id):
            raise NotHaveAccessToOrderError()

    return order


async def get_favorites_from_token(
        token_data: dict,
) -> StoreFavorite | None:
    favorite = None
    if token_data.get("type") == "favorites" or token_data.get(
            "is_favorites", False
    ) is True:
        favorite_id = token_data.get("sub")
        favorite = await StoreFavorite.get(favorite_id)

    return favorite


def is_pydantic(obj: object):
    """Checks whether an object is pydantic."""
    return type(obj).__class__.__name__ == "ModelMetaclass"


def pydantic_to_sqlalchemy_model(schema):
    """
    Iterates through pydantic schema and parses nested schemas
    to a dictionary containing SQLAlchemy models.
    Only works if nested schemas have specified the Meta.orm_model.
    """
    parsed_schema = dict(schema)
    for key, value in parsed_schema.items():
        try:
            if not value:
                continue
            if isinstance(value, list) and len(value) and is_pydantic(value[0]):
                parsed_schema[key] = [
                    item.Meta.orm_model(**pydantic_to_sqlalchemy_model(item))
                    for item in value
                ]
            elif is_pydantic(value):
                parsed_schema[key] = value.Meta.orm_model(
                    **pydantic_to_sqlalchemy_model(value)
                )
        except AttributeError:
            raise AttributeError(
                f"Found nested Pydantic model in {schema.__class__} but "
                f"Meta.orm_model was not specified."
            )
    return parsed_schema


async def validate_cart_attributes(
        product: StoreProduct, attributes: list[schemas.SaveCartAttributeSchema]
):
    required_ag_ids = set(await crud.get_required_attributes_groups(product.id))
    available_ag_ids = await crud.get_available_attributes_groups_ids(product.id)

    attribute_groups_attrs: dict[
        StoreAttributeGroup, list[StoreAttribute]] = defaultdict(list)

    if required_ag_ids:
        if required_ag_ids and len(attributes) < len(required_ag_ids):
            detail = "missing required attribute groups"
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

    if not attributes:
        return

    for attribute_schema in attributes:
        attribute: StoreAttribute = await StoreAttribute.get(
            attribute_schema.attribute_id
        )
        if not attribute:
            raise AttributeNotFoundError(attribute_schema.attribute_id)

        if attribute.attribute_group_id not in available_ag_ids:
            raise IncorrectAttributeGroupError()

        if attribute.attribute_group_id in required_ag_ids:
            required_ag_ids.remove(attribute.attribute_group_id)

        attribute_group: StoreAttributeGroup = await StoreAttributeGroup.get(
            attribute.attribute_group_id
        )
        attribute_groups_attrs[attribute_group].append(attribute)

        if attribute.min and attribute.max:
            if (attribute.min > attribute_schema.quantity or attribute_schema.quantity
                    > attribute.max):
                detail = f"incorrect attribute quantity ({attribute.id})"
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail=detail
                )

    for attribute_group, attributes in attribute_groups_attrs.items():
        if attribute_group.min and attribute_group.max:
            if not (attribute_group.min <= len(attributes) <= attribute_group.max):
                detail = (f"incorrect attributes quantity. attribute group ("
                          f"{attribute_group.id})")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail=detail
                )


async def validate_cart_product(
        data: schemas.SaveCartProductSchema, lang: str, store_id: int | None = None
) -> None:
    product: StoreProduct = await StoreProduct.get(data.product_id)
    if not product:
        detail = await f(
            "product add to cart not found product error", lang,
            product_id=data.product_id
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

    if not product.is_available:
        detail = await f(
            "web store cart not available text", lang, product_id=data.product_id
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

    if product.type == "info" or product.type == "gift":
        detail = await f(
            "product add to cart type error", lang, product_type=product.type
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

    if data.quantity < product.buy_min_quantity:
        detail = await f(
            "product add to cart qty less than allowed error", lang,
            product_name=product.name
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

    if product.attribute_groups:
        await validate_cart_attributes(product, data.cart_attributes)

    if data.floating_sum and not product.floating_sum_enabled:
        detail = await f("product floating sum disabled error", lang)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

    if store_id:
        brand = await crud.get_brand_by_store(store_id)
        store = await Store.get(store_id)
        if await check_products_is_available(
                [data.product_id],
                brand.id, store_id, store.external_type, store.external_id
        ):
            detail = await f(
                "web store cart not available text", lang, product_id=data.product_id
            )
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)


def get_sorted_by_coordinates_stores(
        latitude: float, longitude: float, stores: list[tuple[Store, Translation]]
) -> list[tuple[Store, Translation]]:
    sorted_stores = sorted(
        stores,
        key=lambda x: distance([longitude, latitude], [x[0].longitude, x[0].latitude])
    )

    return sorted_stores


async def try_get_brand(brand: Brand | None) -> schemas.BrandSchema:
    if brand:
        return await brand_to_schema(brand)
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="not found")


async def check_in_stock(
        store_obj: StoreCart | StoreOrder, brand_id: int | None = None,
) -> list[schemas.NotAvailableProduct]:
    invalid_products = []
    products = []

    store = await Store.get(store_obj.store_id)

    if isinstance(store_obj, StoreCart):
        products = [cart_prod.product for cart_prod in store_obj.cart_products]
    if isinstance(store_obj, StoreOrder):
        products = [order_prod.product for order_prod in store_obj.order_products]

    if store.external_type and store.external_type == "poster":
        products_for_poster = await get_products_for_check(
            [product.id for product in products]
        )
        invalid_products = await check_poster_stocks(
            brand_id, store.external_id, products_for_poster
        )

    for product in products:
        if not product.is_available:
            if invalid_products:
                already_added = any(
                    prod.product_id == product.id for prod in invalid_products
                )
                if already_added:
                    continue
            invalid_products.append(
                schemas.NotAvailableProduct(
                    product_id=product.id,
                    name=product.name,
                )
            )

    return invalid_products
