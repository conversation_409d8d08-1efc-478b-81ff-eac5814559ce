import logging
from datetime import datetime
from typing import Literal

from fastapi import (
    APIR<PERSON><PERSON>, Depends, HTTPException, Path, Query, Response, Security, status,
)
from psutils.exceptions import ErrorWithTextVariable

from core.api.depends import get_lang
from core.auth.depend import get_active_user, get_user_or_token_data_safe
from core.ext.adapters.get_order.get_order_funcs import sync_orders
from core.ext.adapters.poster import poster_sync_orders
from core.invoice.bot_funcs import send_shop_invoice_for_pay
from core.store.depends import get_current_brand
from core.store.functions.order import order_to_schema
from core.store.order.service import change_store_order_status
from db import crud
from db.models import (
    BillingSettings, Brand, Customer, ShipmentPrice, Store, StoreOrder, User,
)
from schemas import (
    OrderHistorySchema, OrderID, OrderPaymentStatusSchema, OrderSchema,
    OrderShippingStatusEnum, UpdateOrderSchema,
)
from schemas.base import BaseCountResponse
from utils.text import f
from ..functions import get_order_from_token
from ..routes.create_order.functions import validate_order_shipment_methods

logger = logging.getLogger("debugger.order")

router = APIRouter(
    prefix="/order",
    tags=["store/order"],
)


@router.post(
    "/cancel_order",
    description="Method to cancel order"
)
async def cancel_store_order(
        data: OrderID,
        token_data: User | dict | None = Depends(get_user_or_token_data_safe),
        lang: str = Depends(get_lang),
):
    order_id = data.order_id

    order = await get_order_from_token(order_id, token_data, True)

    initiated_by_user = token_data if isinstance(token_data, User) else None

    if order.status == "open_unconfirmed" and order.status_pay == "must_pay":
        await change_store_order_status(
            order,
            OrderShippingStatusEnum.CANCELED.value,
            "user",
            None,
            "user",
            initiated_by_user=initiated_by_user,
        )
    elif order.status == OrderShippingStatusEnum.NEW.value:
        await order.update(_status=OrderShippingStatusEnum.TERMINATED_BY_USER.value)
    else:
        detail = await f("store brand cant cancel order error", lang)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

    return {"ok": True}


@router.get(
    "/get_by_user",
    description="Method to get user orders list",
    response_model=list[OrderSchema] | BaseCountResponse,
)
async def get_user_orders(
        order_status: Literal[
                          "open_unconfirmed", "open_confirmed", "canceled", "closed",
                          "all",
                      ] | None = Query(default=None, description="Order status"),
        date_start: str | None = Query(default=None, description="Date start"),
        date_end: str | None = Query(default=None, description="Date end"),
        user: User = Security(get_active_user, scopes=["me:write", "me:read"]),
        count: bool = Query(
            default=False, description="Count orders (open_unconfirmed, open_confirmed"
        ),
        store_id: int = Query(description="Store id"),
        lang: str = Depends(get_lang),
):
    if not store_id:
        return []

    orders = await StoreOrder.get_by_user(
        store_id, user.id, date_start, date_end, order_status, count
    )

    if count:
        return BaseCountResponse(count=orders)

    if not orders:
        return []

    store = await Store.get(store_id)
    if store.external_type == 'get_order':
        orders = await sync_orders(orders, store.brand)
    elif store.external_type == 'poster':
        poster_app_secret = await crud.get_poster_setting(
            store.brand.id, "poster_app_secret"
        )
        if not poster_app_secret:
            orders = await poster_sync_orders(orders, store.brand)

    return [await order_to_schema(order, lang) for order in orders]


@router.get(
    "/payment_status/{order_id}",
    description="Method to get order payment status",
    response_model=OrderPaymentStatusSchema,
)
async def get_order_payment_status(
        order_id: int = Path(description="order_id"),
        token_data: User | dict | None = Depends(get_user_or_token_data_safe),
):
    order = await get_order_from_token(order_id, token_data, False)
    return OrderPaymentStatusSchema(payment_status=order.status_pay)


@router.post(
    "/send_invoice_to_bot/{order_id}",
    description="Method to send store order invoice to client bot",
    status_code=200,
)
async def send_order_invoice_to_bot(
        order_id: int = Path(description="order_id"),
        token_data: User | dict | None = Depends(get_user_or_token_data_safe),
        payment_settings_id: int | None = Query(
            default=None, description="Payment settings ID for Telegram payment"
        ),
        object_payment_settings_id: int | None = Query(
            default=None, description="Object payment settings ID for Telegram payment"
        ),
) -> Response:
    order = await get_order_from_token(order_id, token_data, True)

    if order.status_pay == "payed" or order.status_pay == "processing":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Order status pay paid or processing"
        )
    if order.status == "canceled" or order.status == "closed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Order status canceled or closed"
        )

    user = await User.get_by_id(order.user_id)
    lang = await user.get_lang()

    try:
        await send_shop_invoice_for_pay(
            order,
            payment_settings_id=payment_settings_id,
            object_payment_settings_id=object_payment_settings_id
        )
    except ErrorWithTextVariable as err:
        err_txt = await f(err.text_variable, lang, **err.text_kwargs)
        logger.error(f"create invoice FAILED\n{err_txt}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=err_txt)
    except Exception as ex:
        logger.error(f"create invoice FAILED\n{str(ex)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ex))

    return Response(status_code=200)


@router.get(
    "/{order_id}/history",
    description="Method to get order history statuses",
    response_model=list[OrderHistorySchema],
)
async def get_order_history_statuses(
        order_id: int = Path(description="order_id"),
        token_data: User | dict | None = Depends(get_user_or_token_data_safe),
):
    if not order_id:
        return []

    order = await get_order_from_token(order_id, token_data)

    order_statuses = await crud.get_order_statuses(order.id)
    if not order_statuses:
        return []

    return [
        OrderHistorySchema(
            time_created=order_status.time_created,
            status=order_status.status,
            comment=order_status.comment
        ) for order_status in order_statuses
    ]


@router.get(
    "/{order_id}",
    description="Method to get order by order_token or user",
    response_model=OrderSchema,
)
async def get_order(
        order_id: int = Path(description="order_id"),
        user_or_order_token: User | dict | None = Depends(get_user_or_token_data_safe),
        lang: str = Depends(get_lang),
):
    if not order_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Order id not passed"
        )

    order = await get_order_from_token(order_id, user_or_order_token)

    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Order not found"
        )

    return await order_to_schema(
        order, lang, with_token=not isinstance(user_or_order_token, User)
    )


@router.post(
    "/update/{order_id}",
    description="Method to update order by order_token or user",
    response_model=OrderSchema,
)
async def update_store_order(
        data: UpdateOrderSchema,
        order_id: int = Path(description="order_id"),
        user_or_order_token: User | dict | None = Depends(get_user_or_token_data_safe),
        lang: str = Depends(get_lang),
        brand: Brand = Depends(get_current_brand),
):
    if not order_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Order id not passed"
        )

    order = await get_order_from_token(order_id, user_or_order_token)

    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Order not found"
        )

    if data.marketing_consent is not None and brand:
        if isinstance(user_or_order_token, User):
            customer = await Customer.get(
                user_id=user_or_order_token.id, profile_id=brand.group_id
            )
            if customer:
                await customer.update(
                    marketing_consent=data.marketing_consent,
                    updated_date=datetime.utcnow()
                )

    data_dict = data.dict(exclude_unset=True, exclude={"marketing_consent"})
    if data_dict.get("shipment_method_id"):
        shipment_price = None
        if data.price:
            shipment_price = await ShipmentPrice.get(data.price.id)
        store = await Store.get(order.store_id)
        shipment_method = await validate_order_shipment_methods(
            store, lang, form_update=data, order_id=order.id
        )
        billing_settings = await BillingSettings.get_by_store(store_id=order.store_id)
        await crud.update_order(
            order, data, shipment_price, shipment_method, billing_settings
        )
    else:
        await order.update(**data_dict)

    return await order_to_schema(
        order, lang, with_token=not isinstance(user_or_order_token, User)
    )
