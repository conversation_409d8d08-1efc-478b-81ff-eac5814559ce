from fastapi import APIRouter, Body, Depends, Query

import schemas
from core.scanner_service import ScannerService
from core.scanner_service.service import ScannerServiceUnAuth

router = APIRouter(
    prefix="/scan",
    tags=["store/scan"],
)


@router.get(
    "/{receipt_id}",
    response_description="receipt",
    description="Method to get receipt by id",
    response_model=schemas.ReceiptSchema,
)
async def get_receipt(
        receipt_id: int,
        service: ScannerService = Depends(ScannerService),
        store_id: int | None = Query(default=None)
):
    return await service.get_receipt(receipt_id=receipt_id, store_id=store_id)


@router.post(
    "/scan_and_save",
    response_description="created receipt id with check",
    description="Method to process and save scanned receipt",
    response_model=schemas.ScanResponseSchema,
)
async def scan_and_save_receipt(
        data: schemas.ScanPayloadSchema = Body(...),
        service: ScannerService = Depends(ScannerService)
):
    return await service.process(
        data.data, data.with_make_check,
        data.order_id, store_id=data.store_id,
        with_bot_notification=True
    )


@router.post(
    "/",
    response_description="scanned receipt with check",
    description="Method to process scanned receipt",
    response_model=schemas.ScanResponseUnAuthSchema,
)
async def scan_receipt(
        data: schemas.ScanPayloadSchema = Body(...),
        service: ScannerServiceUnAuth = Depends(ScannerServiceUnAuth)
):
    return await service.get_scanned_receipt(data.data, data.store_id)
