import aiofiles
import chardet
import html
from fastapi import APIRouter, Depends, HTTPException, Path, Query
from starlette import status
from typing import Literal

import exceptions
import schemas
from core.api.depends import get_current_domains
from core.store.depends import get_current_brand
from core.store.functions.brand import brand_to_schema
from core.store.product.exceptions import (
    BrandNotFoundError, DocumentNotFoundError,
    StoreNotFoundError,
    UnknownDocumentTypeError,
)
from db import crud
from db.models import Brand, MediaObject, Store
from schemas.base import BaseSimpleTextModel
from ...functions import try_get_brand

router = APIRouter(
    prefix="/brands",
    tags=["/store/brand"],
)


@router.get(
    "/",
    description="Getting all brands",
    response_model=list[schemas.BrandSchema],
)
async def get_brands():
    return [await brand_to_schema(brand) for brand in await Brand.get_list()]


@router.get("/detect")
async def detect_brand(
        _: int | None = Query(None, alias="brand_id"),
        brand: Brand | None = Depends(get_current_brand),
        current_domains: list[str] = Depends(get_current_domains),
) -> schemas.DetectedBrandSchema:
    if not brand:
        raise HTTPException(
            detail="Brand not found", status_code=status.HTTP_404_NOT_FOUND
        )

    if (
            await crud.check_access_to_action(
                "billing:tester", "profile", brand.group_id
            ) and
            not await crud.billing.check_profile_subscription(brand.group_id)):
        raise exceptions.BillingServiceSuspendedError()

    return schemas.DetectedBrandSchema(
        brand=await brand_to_schema(brand),
        is_brand_domain=(
            brand.domain.lower() in current_domains
            if brand.domain else False
        ),
    )


@router.get(
    "/{brand_id}",
    response_description="Method get store brand",
    description="Returns store brand",
    response_model=schemas.BrandSchema
)
async def get_brand(
        brand_id: int | None = Path(description="The ID of the brand"),
):
    brand = await Brand.get(brand_id)
    return await try_get_brand(brand)


@router.get(
    "/{brand_id}/document",
    description="Getting brand document",
    response_model=BaseSimpleTextModel,
)
async def get_document(
        brand_id: int = Path(description="id of brand to get document"),
        store_id: int = Query(description="id of store to get document", default=None),
        document_type: Literal["agreement", "about"] = Query(
            description="type of document"
        ),
) -> BaseSimpleTextModel:
    brand = await Brand.get(brand_id)
    if not brand:
        raise BrandNotFoundError()

    offer_path = ""
    description_path = ""

    if store_id:
        store = await Store.get(store_id)
        if not store:
            raise StoreNotFoundError()
        if store.offer_media_id:
            offer_media = await MediaObject.get(store.offer_media_id)
            offer_path = offer_media.file_path
        if store.description_media_id:
            description_media = await MediaObject.get(store.description_media_id)
            description_path = description_media.file_path

    if not offer_path or not description_path:
        if brand.offer_media_id and not offer_path:
            offer_media = await MediaObject.get(brand.offer_media_id)
            offer_path = offer_media.file_path
        if brand.description_media_id and not description_path:
            description_media = await MediaObject.get(brand.description_media_id)
            description_path = description_media.file_path

    match document_type:
        case "agreement":
            path = offer_path
        case "about":
            path = description_path
        case _:
            raise UnknownDocumentTypeError(document_type)

    if not path:
        raise DocumentNotFoundError()

    try:
        async with aiofiles.open(path, mode='rb') as file:
            result = chardet.detect(await file.read())
        async with aiofiles.open(path, mode='r', encoding=result['encoding']) as file:
            text = await file.read()
            text = text.encode('utf-8').decode('utf-8')
            text = html.unescape(text)
            return BaseSimpleTextModel(text=text)
    except Exception as ex:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ex))
