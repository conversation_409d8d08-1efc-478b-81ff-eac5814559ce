from fastapi import APIRouter, Depends, Path, Query
from psutils.translator.schemas import TranslateObjectData

import schemas
from core.api.depends import get_lang
from core.store.depends import get_current_brand
from core.store.functions.characteristic import characteristics_to_schemas
from core.store.product.exceptions import CreateFiltersSetNoFiltersError
from db import crud
from db.models import Group, StoreCharacteristicValue, Translation
from utils.translator import td

router = APIRouter(
    prefix="/filters",
    tags=["filters"],
)


@router.get("/{store_id}/{characteristic_id}/values")
async def get_filter_values(
        characteristic_id: int = Path(),
        store_id: int = Path(),
        category_id: int | None = Query(None),
        lang: str = Depends(get_lang),
) -> schemas.CharacteristicValues:
    group = await crud.get_group_by_store(store_id)

    values: list[tuple[StoreCharacteristicValue, Translation | None]] = \
        await crud.get_characteristic_values(
            characteristic_id, store_id, lang, category_id
        ) or []

    to_translate: dict[str, TranslateObjectData] = {}

    for value_obj, translation in values:
        if value_obj.value and value_obj.value not in to_translate:
            to_translate[value_obj.value] = TranslateObjectData(
                object=value_obj,
                translation=translation,
                field_name="value",
            )

    translated: dict[str, str] = await td(
        to_translate,
        lang, group.lang,
        group_id=group.id,
        is_auto_translate_allowed=group.is_translate,
    )

    return schemas.CharacteristicValues(
        characteristic_id=characteristic_id,
        store_id=store_id,
        category_id=category_id,
        values=[
            schemas.CharacteristicValue(
                value=value_obj.value,
                display_value=translated.get(value_obj.value) or value_obj.value,
            )
            for value_obj, _ in values
        ]
    )


@router.post("/create", response_model=schemas.FiltersCreated)
async def create_filters(
        data: schemas.CreateFiltersData,
):
    if not any(
            data.filters.values()
    ) and data.min_price is None and data.max_price is None:
        raise CreateFiltersSetNoFiltersError()

    filters_set = await crud.create_filters_set(data)
    return {
        "filters_set_id": filters_set.id
    }


@router.get("/")
async def get_store_filters(
        brand: schemas.BrandSchema = Depends(get_current_brand),
        store_id: int | None = Query(description="The ID of the store", default=None),
        lang: str = Depends(get_lang),
) -> list[schemas.CharacteristicSchema]:
    group = await Group.get_by_brand(brand.id)
    characteristics = await crud.get_characteristic_filters(
        brand.id, store_id, lang
    ) or []
    if store_id and not characteristics:
        characteristics = await crud.get_characteristic_filters(
            brand.id, None, lang
        ) or []

    return await characteristics_to_schemas(characteristics, group, lang)
