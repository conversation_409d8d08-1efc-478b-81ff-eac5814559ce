from fastapi import APIRouter, Depends, Query

import schemas
from core.store.depends import get_current_brand
from db import crud
from schemas import BrandSchema

router = APIRouter(
    prefix="/cities",
    tags=["/store/city"],
)


@router.get(
    "/",
    response_description="Method get cities",
    description="Returns store cities",
    response_model=schemas.CitiesResponse
)
async def get_cities(
        brand: BrandSchema = Depends(get_current_brand),
        is_delivery: bool = Query(
            description="filter cities by delivery", default=False
        ),
        is_pickup: bool = Query(description="filter cities by pickup", default=False),
        is_in_store: bool = Query(
            description="filter cities by in_store", default=False
        ),
):
    cities = await crud.get_cities_list(brand.id, is_delivery, is_pickup, is_in_store)
    return {
        "cities": cities or []
    }
