import logging
from fastapi import APIRouter, Depends, HTTPException, Path, Query
from fastapi.responses import JSONResponse
from starlette import status

import schemas
from core.api.depends import get_lang
from core.geo import get_coordinates
from core.geo.functions import check_in_distance, check_inside
from core.store.depends import get_current_brand
from core.store.functions.store import store_to_schema
from core.store.product.exceptions import BrandNotFoundError
from db import crud
from db.models import Group, Store, Translation
from schemas import BrandSchema
from schemas.base import BaseCountResponse
from ...functions import get_sorted_by_coordinates_stores

router = APIRouter(
    prefix="/stores",
    tags=["/store/store"],
)

logger = logging.getLogger('debugger')


@router.get(
    "/",
    response_description="Returns stores list",
    description="Method get stores",
    response_model=list[schemas.StoreSchema] | BaseCountResponse,
)
async def get_stores(
        brand: BrandSchema = Depends(get_current_brand),
        lang: str = Depends(get_lang),
        allowed_store_ids: list[int] | None = Query(
            default=None,
            description="List of allowed store ids"
        ),
        city: str = Query(
            description="The name of the city to filter stores", default=None
        ),
        is_delivery: bool = Query(
            description="filter stores by delivery", default=False
        ),
        is_pickup: bool = Query(description="filter stores by pickup", default=False),
        is_in_store: bool = Query(
            description="filter stores by in_store", default=False
        ),
        latitude: float = Query(
            description="The latitude of the user address", default=None
        ),
        longitude: float = Query(
            description="The longitude of the user address", default=None
        ),
        count: bool = Query(description="The count of stores", default=False),
):
    stores: list[tuple[Store, Translation]] | int = await crud.get_stores(
        brand.id,
        city,
        is_delivery,
        is_pickup,
        is_in_store,
        operation="count" if count else "all",
        lang=lang,
        allowed_store_ids=allowed_store_ids,
    )

    if count:
        return BaseCountResponse(count=stores)

    if latitude and longitude:
        stores = get_sorted_by_coordinates_stores(
            float(latitude), float(longitude), stores,
        )

    group = await Group.get_by_brand(brand.id)
    return [
        await store_to_schema(
            store, group, lang,
        ) for store, translation in stores
    ]


@router.get(
    "/{store_id}",
    response_description="Method get store",
    description="Returns store",
    response_model=schemas.StoreSchema
)
async def get_store(
        store_id: int = Path(description="The ID of the store"),
        brand: BrandSchema = Depends(get_current_brand),
        lang: str = Depends(get_lang),
):
    if not brand:
        raise BrandNotFoundError()

    store = await Store.get(
        store_id, brand_id=brand.id,
        is_deleted=False, is_enabled=True,
    )
    if not store:
        logger.error(f'Store not found, {brand.id=}, {store_id=}')
        return JSONResponse(content={}, status_code=404)

    group = await Group.get_by_brand(brand.id)
    return await store_to_schema(
        store, group, lang,
    )


@router.get(
    "/by_coordinates_or_address",
    response_description="Method get stores by coordinates or address",
    description="Returns stores list",
    response_model=list[schemas.StoreSchema]
)
async def get_store_by_coordinates_or_address(
        brand: BrandSchema = Depends(get_current_brand),
        lang: str = Depends(get_lang),
        latitude: str | None = Query(default=None),
        longitude: str | None = Query(default=None),
        address: str | None = Query(default=None),
):
    if not address and not (latitude and longitude):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="not enough data to search for stores"
        )

    stores = await crud.get_stores(brand.id)

    if address and not any([latitude, longitude]):
        latitude, longitude = await get_coordinates(address, lang)

    if latitude and longitude:
        point = [latitude, longitude]

        results = list()
        for el in stores:
            store = el[0]
            if (store.is_polygon and store.polygon and
                    check_inside(store.polygon, point, store.is_swap_coordinates)):
                results.append(el)

            if (store.is_distance and
                    check_in_distance(
                        [store.latitude, store.longitude], store.distance, point
                    )):
                results.append(el)

            if not store.is_polygon and not store.is_distance:
                results.append(el)

        group = await Group.get_by_brand(brand.id)
        return [
            await store_to_schema(
                store, group, lang, translation,
            ) for store, translation in stores
        ]

    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="not found")
