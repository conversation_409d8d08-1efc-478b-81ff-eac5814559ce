from fastapi import APIRouter

from . import brands, stores, cities, categories, products, shipments, payments, filters

router = APIRouter(
    tags=["/store/core"],
)

router.include_router(brands.router)
router.include_router(categories.router)
router.include_router(cities.router)
router.include_router(filters.router)
router.include_router(payments.router)
router.include_router(products.router)
router.include_router(shipments.router)
router.include_router(stores.router)
