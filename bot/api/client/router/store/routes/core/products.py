from typing import Annotated

from fastapi import APIRouter, Depends, Query

import schemas
from core.store.product.service import ProductsService

router = APIRouter(
    prefix="/products/{store_id}",
    tags=["/store/products"]
)


@router.get("/")
async def get_products(
        params: Annotated[schemas.ProductsListParams, Query()],
        service: ProductsService = Depends(),
) -> schemas.ProductsListResponse:
    return await service.get_products_list(params)


@router.get("/minMaxPrices")
async def get_min_max_prices(
        service: ProductsService = Depends(),
        data: schemas.GetMinMaxPricesData = Query(),
) -> schemas.ProductsMinMaxPrices:
    return await service.get_min_max_prices(data)


@router.get("/{product_id}")
async def get_product(
        product_id: int,
        service: ProductsService = Depends(),
) -> schemas.ProductSchema:
    return await service.get_product(product_id)


@router.post("/{product_id}/findModification")
async def get_product_modification(
        product_id: int,
        data: schemas.FindProductModificationData,
        service: ProductsService = Depends()
) -> schemas.ProductSchema:
    return await service.find_modification(product_id, data)
