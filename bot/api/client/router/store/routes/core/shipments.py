from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse

import schemas
from core.api.depends import get_lang
from core.store.convertors import ShipmentConvertor
from core.store.depends import get_current_brand
from core.store.exceptions import ShipmentPricesNoPriceForAddressError
from core.store.functions.custom_shipment import (
    get_custom_shipment_groups_list_schemas, get_custom_shipments_list_schemas,
)
from core.store.functions.shipment import get_shipments_data
from db.models import Brand
from utils.text import f

router = APIRouter(
    prefix="/shipments",
    tags=["/store/shipments"],
)


@router.get("/")
async def get_shipments(
        brand: Brand = Depends(get_current_brand),
        store_id: int | None = Query(default=None),
        lang: str = Depends(get_lang),
) -> schemas.ShipmentsData:
    return await get_shipments_data(brand, store_id, lang)


@router.get(
    "/custom_shipments",
    description="Method for getting a list of all custom shipment methods of the store",
    response_model=schemas.CustomShipmentsSchema,
)
async def get_custom_shipments(
        brand: Brand = Depends(get_current_brand),
        store_id: int | None = Query(description="The ID of the store", default=None),
        lang: str = Depends(get_lang),
):
    custom_shipment_groups = await get_custom_shipment_groups_list_schemas(
        brand, store_id, lang
    )
    custom_shipments = await get_custom_shipments_list_schemas(
        brand, store_id, lang, is_rest_shipments=True
    )

    return schemas.CustomShipmentsSchema(
        groups=custom_shipment_groups,
        rest=custom_shipments,
    )


@router.get(
    "/prices",
    description="Method for getting a list of all shipment prices of the order",
    response_model=list[schemas.ShipmentPriceResultData],
)
async def get_shipment_prices(
        brand: Brand = Depends(get_current_brand),
        lang: str = Depends(get_lang),
        data: schemas.ShipmentPriceInputData = Query(),
):
    response = []
    for shipment_id in (data.shipment_ids or []):
        data_schema = schemas.ShipmentPriceInputData(
            **data.dict(exclude={"shipment_ids"})
        )
        convertor = ShipmentConvertor(
            brand,
            lang=lang,
            shipment_id=shipment_id.shipment_id,
            shipment_store_settings_id=shipment_id.store_settings_id,
            **data_schema.dict(exclude={"shipment_ids", "shipment_id"}),
            purpose="prices",
        )
        res = await convertor.get_data()
        response.append(res)

    return response


@router.get(
    "/price",
    description="Method for getting shipment price of the order",
    response_model=schemas.ShipmentPrice,
)
async def get_shipment_price(
        brand: Brand = Depends(get_current_brand),
        lang: str = Depends(get_lang),
        data: schemas.ShipmentPriceInputData = Query(),
):
    convertor = ShipmentConvertor(
        brand, lang=lang,
        shipment_id=(
            data.shipment_id.shipment_id
            if data.shipment_id else None
        ),
        shipment_store_settings_id=(
            data.shipment_id.store_settings_id
            if data.shipment_id else None
        ),
        **data.dict(exclude={"shipment_ids", "shipment_id"}),
        purpose="price"
    )

    try:
        any_prices = await convertor.check_any_prices_exist()
        if not any_prices:
            return schemas.ShipmentPrice(
                id=0,
                cost_delivery=0,
                minimum_order_amount=0,
                maximum_order_amount=0,
            )
        return await convertor.get_price()
    except ShipmentPricesNoPriceForAddressError:
        text = await f('shipment prices address not in zones error', lang)
        return JSONResponse(
            status_code=400, content={"detail": text, "internal_code": "address_error"}
        )
