from fastapi import APIRouter, Depends, Query

import schemas
from core.api.depends import get_lang
from core.store.functions.category import category_to_schema
from db import crud

router = APIRouter(
    prefix="/categories",
    tags=["/store/categories"],
)


@router.get("/tree")
async def get_tree_categories(
        params: schemas.GetCategoryTreeParams = Query(),
        lang: str = Depends(get_lang),
) -> list[schemas.CategorySchema]:
    brand, group = await crud.get_brand_and_group_by_store(params.store_id)

    categories_tree = await crud.get_categories_tree(
        brand.id, params.store_id, lang,
        params.products_search, params.filters_set_id,
    )

    async def process_categories():
        return [
            await category_to_schema(
                category, group, params.store_id, lang,
                params.filters_set_id, params.filters, params.products_search,
            ) for category in categories_tree
        ]

    return await process_categories()


@router.get("/", response_model=list[schemas.CategorySchema])
async def get_categories(
        store_id: int = Query(),
        father_category_id: int | None = Query(None),
        filters_set_id: int | None = Query(None),
        products_search: str | None = Query(None),
        lang: str = Depends(get_lang),
):
    brand, group = await crud.get_brand_and_group_by_store(store_id)

    categories = await crud.get_store_categories(
        brand.id, store_id, father_category_id, lang
    )
    return [
        await category_to_schema(
            category, group, store_id,
            lang, filters_set_id, None,
            products_search, translation,
        )
        for category, translation in categories
    ]
