from fastapi import APIRouter, Depends, HTTPException, Query

import schemas
from core.api.depends import get_lang
from core.auth.depend import get_active_user_optional, get_user_or_token_data_safe
from core.invoice.functions import calc_extra_fee
from core.payment.incust_pay.service import IncustPayService
from core.payment.payment_methods import (
    get_available_payment_methods,
)
from core.store.depends import get_current_brand
from db.models import Brand, Invoice, User
from schemas import ExtraFeeSchema
from utils.text import f
from ...functions import get_order_from_token

router = APIRouter(
    prefix="/payments"
)


@router.get(
    "/",
    response_description="Method to get store available payments",
    description="Returns store available payments",
    response_model=schemas.AvailablePaymentSchema,
)
async def get_available_payments(
        brand: Brand = Depends(get_current_brand),
        user: User = Depends(get_active_user_optional),
        lang: str = Depends(get_lang),
        store_id: int = Query(description="The ID of the store", default=None),
        is_qr_menu: bool = Query(description="Is QR menu", default=False),
        shipment_id: int | None = Query(description="shipment_id", default=None),
        incust_account_id: str | None = Query(
            description="incust_account_id", default=None
        ),
        invoice_template_id: int = Query(
            description="Invoice template ID", default=None
        ),
        only_online: bool = Query(False, description="Only online"),
):

    return await get_available_payment_methods(
        brand, user,
        lang,
        store_id, shipment_id,
        incust_account_id, is_qr_menu,
        invoice_template_id=invoice_template_id,
        only_online=only_online,
    )


@router.get(
    "/incust_pay_data",
    description="Method for getting a list of all incust pay configurations and "
                "accounts",
    response_model=schemas.IncustPayPaymentData,
)
async def get_incust_pay_data(
        store_id: int | None = Query(description="The ID of the store", default=None),
        is_topup: bool | None = Query(
            description="Parameter to return all terminal accounts", default=None
        ),
        product_id: int | None = Query(
            description="The ID of topup product", default=None
        ),
        incust_pay_service: IncustPayService = Depends(),
):
    incust_pay = await incust_pay_service.get_incust_pay_payment_data(
        store_id=store_id,
        none_on_empty=True,
        is_topup=is_topup,
        product_id=product_id,
    )
    if not incust_pay:
        raise HTTPException(status_code=404, detail="Incust Pay data not found")
    return incust_pay


@router.get(
    "/extra_fee",
    response_description="Method to get store available payments fee",
    description="Returns store available payments fee",
)
async def get_calculated_extra_fee(
        brand: Brand = Depends(get_current_brand),
        total_sum: float = Query(),
        currency: str = Query(),
) -> list[ExtraFeeSchema]:
    result = await calc_extra_fee(
        brand.group_id, int(round(total_sum * 100, 0)), currency
    )
    if result and getattr(result, "journal_entries", None):
        return result.journal_entries
    return []


@router.get(
    "/payment_status",
    description="Method to get payment status",
    response_model=schemas.PaymentStatusSchema,
)
async def get_payment_status(
        order_id: int | None = Query(
            default=None, nullable=True, description="order_id"
        ),
        invoice_id: int | None = Query(
            default=None, nullable=True, description="invoice_id"
        ),
        token_data: User | dict | None = Depends(get_user_or_token_data_safe),
        lang: str = Depends(get_lang)
):
    if not order_id and not invoice_id:
        raise HTTPException(
            status_code=400, detail=await f(
                "payments status no invoice and order error", lang
            )
        )

    status = ""
    if order_id:
        order = await get_order_from_token(order_id, token_data, False)
        status = order.status_pay
    elif invoice_id:
        if token_data and isinstance(token_data, dict) and token_data.get(
                "type"
        ) == "invoice":
            invoice_id = token_data.get("sub")
            invoice = await Invoice.get(invoice_id) if invoice_id else None
            if invoice:
                status = invoice.status
        else:
            if isinstance(token_data, User):
                invoice = await Invoice.get(invoice_id) if invoice_id else None
                if invoice and invoice.user_id == token_data.id:
                    status = invoice.status
            else:
                invoice = await Invoice.get(invoice_id)
                if invoice:
                    user = await User.get_by_id(invoice.user_id)
                    if user and user.email == "<EMAIL>":
                        status = invoice.status

    if not status:
        raise HTTPException(
            status_code=400, detail=await f(
                "payments status failed error", lang
            )
        )

    return schemas.PaymentStatusSchema(payment_status=status)
