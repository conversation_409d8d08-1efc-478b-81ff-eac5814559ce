from fastapi import Depends, HTTPException
from starlette import status

from core.auth.depend import get_custom_token_data, get_user_or_token_data_safe
from db import crud
from db.models import StoreFavorite, User
from ..functions import get_favorites_from_token


def get_favorites_obj(
        create_for_anonymous: bool = False,
):
    async def depend_func(
            store_id: int,
            favorites_token_data: dict | None = Depends(
                get_custom_token_data("favorites", optional=True)
            ),
            user_or_token_data: User | dict | None = Depends(
                get_user_or_token_data_safe
            )
    ) -> StoreFavorite:
        if favorites_token_data:
            user = user_or_token_data if isinstance(user_or_token_data, User) else None
        elif isinstance(user_or_token_data, User):
            user = user_or_token_data
        elif isinstance(user_or_token_data, dict):
            if user_or_token_data.get("type") != "favorites":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=f"Invalid token. Expected token of type favorites"
                )

            favorites_token_data = user_or_token_data
            user = None
        elif not create_for_anonymous:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Authorization or X-Favorites-Token Header is required!"
            )
        else:
            user = None

        if favorites_token_data:
            token_favorite = await get_favorites_from_token(favorites_token_data)
        else:
            token_favorite = None

        if user:
            user_favorite = await StoreFavorite.get(
                user_id=user.id,
                store_id=store_id
            )
        else:
            user_favorite = None

        if favorites_token_data and not token_favorite and not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid X-Favorites-Token"
            )

        if user_favorite:
            if not token_favorite:
                return user_favorite

            await crud.sync_favorites(user_favorite, token_favorite)
            return user_favorite
        elif token_favorite and user:
            return await token_favorite.update(user_id=user.id)
        elif token_favorite:
            return token_favorite
        else:
            return await crud.create_favorites(user.id if user else None, store_id)

    return depend_func
