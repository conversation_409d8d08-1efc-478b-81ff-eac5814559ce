import logging

from fastapi import APIRouter, HTTPException, Query, Security, status

import schemas
from core.auth.depend import get_user_or_token_data_safe
from db import models
from db.models import User
from ..functions import check_in_stock, get_cart_from_token, get_order_from_token

router = APIRouter(
    prefix="/check_instock",
    tags=["store/check_instock"],
)


@router.get(
    "/cart",
    description="Method to check products in poster from cart",
    response_model=list[schemas.NotAvailableProduct],
)
async def check_instock_cart(
        user_data: dict | models.User | None = Security(get_user_or_token_data_safe),
        store_id: int = Query(
            default=None, description="Need to get cart if used user token"
        ),
        brand_id: int = Query(default=None, description="Need to get poster token"),
):
    logger = logging.getLogger("debugger")
    logger.debug(user_data)

    if isinstance(user_data, models.User):
        cart = await models.StoreCart.get_by_user(user_data.id, store_id)
    elif user_data:
        cart = await get_cart_from_token(user_data, store_id)
    else:
        cart = None

    if not cart:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="cart not found"
        )

    try:
        invalid_products = await check_in_stock(cart, brand_id)
        return invalid_products

    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/order/{order_id}",
    description="Method to check products in poster from order",
    response_model=list[schemas.NotAvailableProduct],
)
async def check_instock_order(
        order_id: int,
        token_data: dict | User | None = Security(get_user_or_token_data_safe),
):
    store_order = await get_order_from_token(order_id, token_data)
    try:
        invalid_products = await check_in_stock(store_order)
        return invalid_products
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
