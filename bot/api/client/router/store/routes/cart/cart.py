from fastapi import (
    APIRouter, Body, Depends, HTTPException, Header, Query, Response,
    Security, status,
)

import schemas
from config import ANON_CART_TOKEN_EXPIRES
from core.api.depends import get_lang
from core.auth.depend import get_active_user, get_user_or_token_data_safe
from core.auth.parse_token import parse_token
from core.store.functions.cart import cart_to_schema
from db import crud
from db.models import Store, StoreCart, User
from utils.jwt_token import create_jwt_token
from utils.platform_admins import send_message_to_platform_admins
from utils.text import f
from ..funcs import check_products_is_available
from ...functions import get_cart_from_token, validate_cart_product

router = APIRouter(
    prefix="/cart",
    tags=["store/cart"],
)


@router.get(
    "/",
    response_description="Method get user cart",
    description="Returns user cart with related products",
    response_model=schemas.CartSchema
)
async def get_cart(
        store_id: int = Query(
            default=None, description="Need to get cart if used user token"
        ),
        user_or_token_data: dict | User = Security(get_user_or_token_data_safe),
        anon_cart_token: str = Header(None, alias="X-ANON-CART-TOKEN"),
        lang: str = Depends(get_lang),
):
    if not user_or_token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=await f("store cart id or token error", lang)
        )

    if anon_cart_token and isinstance(user_or_token_data, User):
        cart = await crud.sync_user_cart(anon_cart_token, store_id, user_or_token_data)
        if not cart:
            err_text = await f("store cart sync error", lang)
            err_text_details = f"{err_text}\n{anon_cart_token=}, {store_id=}"
            await send_message_to_platform_admins(err_text_details)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=err_text
            )
        is_new_cart = False
    else:
        cart, is_new_cart = await crud.get_cart(user_or_token_data, store_id)

    if not cart:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)

    unavailable_products = None
    if cart.cart_products:
        brand = await crud.get_brand_by_store(store_id)
        store = await Store.get(store_id)

        if _ := await check_products_is_available(
                [product.product_id for product in cart.cart_products],
                brand.id, store_id, store.external_type, store.external_id
        ):
            unavailable_products = _

    cart_schema = await cart_to_schema(cart, lang, unavailable_products)
    if is_new_cart:
        cart_schema.access_token = create_jwt_token(
            data={
                "sub": f"{cart.id}",
                "type": "cart",
                "scopes": ["cart:read", "cart:write"],
            }, expire=ANON_CART_TOKEN_EXPIRES
        )

    return cart_schema


@router.post(
    "/sync_users_cart",
    description="Method to sync user cart with user cart",
    status_code=200,
)
async def sync_users_cart(
        data: schemas.SyncCartSchema = Body(),
        user: User = Security(get_active_user, scopes=["me:write", "me:read"]),
):
    try:
        token_data, scopes = parse_token(data.anon_cart_token)

        from_cart = await StoreCart.get_by_user(user.id, data.store_id)

        if token_data.get("type") == "user":
            if "me:write" not in token_data.get("scopes", []):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN, detail="Action not allowed"
                )
            new_user_id = int(token_data.get("sub"))
            to_cart = await StoreCart.get_by_user(new_user_id, data.store_id)
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="token invalid"
            )

        if to_cart:
            await to_cart.delete()

        await from_cart.attach_cart_to_user(new_user_id)
    except Exception as e:
        print(e)
        raise HTTPException(status_code=400, detail="error while sync users cart")

    return Response(status_code=200)


@router.post(
    "/create",
    description="Method create user cart",
    response_model=schemas.CartCreated
)
async def create_cart(
        data: schemas.SaveCartSchema = Body(),
        lang: str = Depends(get_lang),
):
    if data.cart_products:
        for cart_product in data.cart_products:
            await validate_cart_product(cart_product, lang, data.store_id)

    cart = await StoreCart.save(data.store_id)

    created_cart_products = []
    if data.cart_products:
        for cart_product in data.cart_products:
            created_cart_product = await crud.save_cart_product(cart_product, cart)
            created_cart_products.append(created_cart_product)

    access_token = create_jwt_token(
        data={
            "sub": f"{cart.id}",
            "type": "cart",
            "scopes": ["cart:read", "cart:write"],
        }, expire=ANON_CART_TOKEN_EXPIRES
    )

    cart_schema = await cart_to_schema(cart, lang)

    return {
        "token": access_token,
        "token_type": "bearer",
        "cart": cart_schema,
    }


@router.delete(
    "/delete",
    description="Method delete user cart",
    status_code=200,
)
async def delete_cart(
        store_id: int = Query(
            default=None, description="Need to get cart if used user token"
        ),
        user_data: dict | User = Security(get_user_or_token_data_safe),
):
    if isinstance(user_data, User):
        cart = await StoreCart.get_by_user(user_data.id, store_id)
    else:
        cart = await get_cart_from_token(user_data, store_id)

    if not cart:
        detail = f"incorrect cart_id"
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

    try:
        if not isinstance(user_data, User):
            if user_data.get("is_cart", False) is True:
                await cart.delete()
                return Response(status_code=200)

        for i in cart.cart_products:
            await i.delete()
    except Exception as ex:
        print(ex)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="cart not deleted"
        )

    return Response(status_code=200)


@router.post(
    "/clear",
    description="Method for clear cart",
    response_model=schemas.CartSchema,
)
async def clear_cart(
        data: schemas.ClearCartData,
        user_data: dict | User = Security(get_user_or_token_data_safe),
        lang: str = Depends(get_lang),
):
    if not user_data:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="user data not exsist")

    if isinstance(user_data, User):
        cart = await StoreCart.get_by_user(user_data.id, data.store_id)
    else:
        cart = await get_cart_from_token(user_data, data.store_id)
    await cart.clear()

    return await cart_to_schema(cart, lang)
