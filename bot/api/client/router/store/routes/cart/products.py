from fastapi import APIRouter, Body, Depends, HTTPException, Path, Query, Security
from starlette import status

import schemas
from config import ANON_CART_TOKEN_EXPIRES
from core.api.depends import get_lang
from core.auth.depend import get_user_or_token_data_safe
from core.store.depends import get_current_brand
from core.store.functions.cart import cart_to_schema, convert_cart_attributes_to_schema
from core.store.product.functions import product_to_schema
from db import crud
from db.models import (
    Brand, Group, StoreCart, StoreCartAttribute, StoreCartProduct,
    User,
)
from utils.jwt_token import create_jwt_token
from utils.text import f
from ...functions import get_cart_from_token, validate_cart_product

# from core.store.functions.cart_funcs import
# validate_cart_product_with_another_attributes

router = APIRouter(
    prefix="/cart/products",
    tags=["/store/cart"],
)


@router.post(
    "/add",
    description="Method add product to user cart",
    response_model=schemas.CartProductResponseSchema
)
async def add_product_to_cart(
        user_data: dict | User = Security(get_user_or_token_data_safe),
        data: schemas.AddCartProduct = Body(),
        brand: Brand = Depends(get_current_brand),
        lang: str = Depends(get_lang),
):
    await validate_cart_product(data.product, lang, data.store_id)
    cart, created_product, err_status, is_new_cart = await crud.add_product_with_cart(
        user_data, data, True
    )
    if err_status:
        if err_status == 409:
            raise HTTPException(
                status.HTTP_409_CONFLICT,
                detail=await f("product is already in cart error", lang)
            )
        else:
            raise HTTPException(
                err_status, detail="unknown cart error"
            )  # TODO: add localization

    if (created_product.product.is_weight and created_product.product.external_type ==
            'poster'):
        created_product.product.unit = await f(
            'store product poster weight unit name',
            cart.STORE.brand.group.lang
        )

    access_token = None
    if is_new_cart:
        access_token = create_jwt_token(
            data={
                "sub": f"{cart.id}",
                "type": "cart",
                "scopes": ["cart:read", "cart:write"],
            }, expire=ANON_CART_TOKEN_EXPIRES
        )

    converted_product = await product_to_schema(
        created_product.product,
        created_product.store_id,
        lang,
    )

    group = await Group.get(brand.group_id)
    converted_cart_attributes = await convert_cart_attributes_to_schema(
        created_product.cart_attributes, lang, group,
    )

    cart_product = schemas.CartProductSchema(
        id=created_product.id,
        quantity=created_product.quantity,
        product=converted_product,
        cart_attributes=converted_cart_attributes,
        floating_sum=round(created_product.floating_sum / 100, 2),
    )

    response = schemas.CartProductResponseSchema(
        cart_product=cart_product,
        access_token=access_token,
        cart=await cart_to_schema(cart, lang),
    )

    return response


@router.delete(
    "/{cart_product_or_product_id}",
    description="Method delete product from user cart",
    response_model=schemas.CartSchema,
)
async def delete_product_from_cart(
        store_id: int = Query(
            default=None, description="Need to get cart if used user token"
        ),
        user_data: dict | User = Security(get_user_or_token_data_safe),
        cart_product_or_product_id: int = Path(
            description="The ID of the cart product or product"
        ),
        is_cart_product_id: bool = Query(
            default=True, description="If True, then cart_product_id, else product_id"
        ),
        lang: str = Depends(get_lang),
):
    if isinstance(user_data, User):
        cart = await StoreCart.get_by_user(user_data.id, store_id)
    else:
        cart = await get_cart_from_token(user_data, store_id)

    if not cart:
        detail = "incorrect cart_id"
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

    if is_cart_product_id:
        cart_product = await StoreCartProduct.get(cart_product_or_product_id)

        if cart_product not in cart.cart_products:
            detail = "incorrect cart_product_id"
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

        await cart_product.delete()
    else:
        cart_products = await StoreCartProduct.get_list(
            product_id=cart_product_or_product_id, cart_id=cart.id
        )
        if cart_products:
            for cart_prod in cart_products:
                if cart_prod not in cart.cart_products:
                    detail = "incorrect cart_product_id"
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST, detail=detail
                    )
                await cart_prod.delete()
        else:
            detail = "incorrect cart_product_id"
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

    return await cart_to_schema(cart, lang)


@router.patch(
    "/{cart_product_id}",
    description="Method to update product in cart",
    response_model=schemas.CartSchema,
)
async def update_product_in_cart(
        store_id: int = Query(
            default=None, description="Need to get cart if used user token"
        ),
        user_data: dict | User = Security(get_user_or_token_data_safe),
        cart_product_id: int = Path(description="The ID of the cart product"),
        data: schemas.UpdateCartProductRequestSchema = Body(),
        lang: str = Depends(get_lang),
):
    if isinstance(user_data, User):
        cart = await StoreCart.get_by_user(user_data.id, store_id)
    else:
        cart = await get_cart_from_token(user_data, store_id)

    attributes_for_validate = []
    product = await StoreCartProduct.get(cart_product_id)
    exist_attributes = await StoreCartAttribute.get_list(
        cart_product_id=cart_product_id
    )
    del_ids = []
    added_ids = []
    if data.attributes:
        for attr in data.attributes:
            if attr.delete:
                del_ids.append(attr.attribute_id)
            else:
                added_ids.append(attr.attribute_id)
                attributes_for_validate.append(attr)

    if exist_attributes:
        for attr in exist_attributes:
            if attr.attribute_id not in del_ids and attr.attribute_id not in added_ids:
                attributes_for_validate.append(
                    schemas.SaveCartAttributeSchema(
                        quantity=attr.quantity,
                        attribute_id=attr.attribute_id,
                    )
                )

    cart_product_schema = schemas.SaveCartProductSchema(
        quantity=data.quantity,
        cart_attributes=attributes_for_validate,
        product_id=product.product_id,
        floating_sum=product.floating_sum,
    )

    await validate_cart_product(cart_product_schema, lang)

    if not cart:
        # TODO: add localization
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="cart not found")

    res = await crud.update_cart_product(cart.id, cart_product_id, data)
    if not res:
        # TODO: add localization
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="error update cart product data"
        )

    return await cart_to_schema(cart, lang)
