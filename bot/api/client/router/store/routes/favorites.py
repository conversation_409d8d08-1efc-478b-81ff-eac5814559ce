from fastapi import (
    APIRouter, Depends, Path,
)

import schemas
from core.api.depends import get_lang
from core.store.product.functions import product_to_schema
from db import crud
from db.models import (
    StoreFavorite, StoreProduct,
)
from .depends import get_favorites_obj
from .funcs import get_favorite_token_if_anonymous

router = APIRouter(
    prefix="/favorites/{store_id}",
    tags=["store/favorites"],
)


@router.get(
    "/",
    response_description="Method to get favorites list",
    description="Returns favorites list",
    response_model=schemas.FavoritesSchema,
)
async def get_favorites(
        favorite: StoreFavorite = Depends(get_favorites_obj()),
        lang: str = Depends(get_lang),
):
    brand = await crud.get_brand_by_store(favorite.store_id)

    favorite_products = [
        schemas.FavoriteProductSchema(
            id=fp.id, product=await product_to_schema(
                fp.product, favorite.store_id, lang,
                brand=brand,
            )
        ) for fp in favorite.favorite_products
    ]

    return schemas.FavoritesSchema(
        id=favorite.id,
        favorite_products=favorite_products,
        token=get_favorite_token_if_anonymous(favorite),
    )


@router.post("/{product_id}/toggle")
async def toggle_product_favorite(
        store_id: int = Path(),
        product_id: int = Path(),
        favorite: StoreFavorite = Depends(get_favorites_obj(True)),
        lang: str = Depends(get_lang),
) -> schemas.ToggleFavoriteProductResult:
    favorite_product = await crud.toggle_product_favorite(favorite.id, product_id)

    result = schemas.ToggleFavoriteProductResult(
        is_deleted=not favorite_product,
        token=get_favorite_token_if_anonymous(favorite),
    )

    if favorite_product:
        db_product = await StoreProduct.get(product_id)
        product = await product_to_schema(
            db_product, store_id, lang,
        )

        result.favorite_product = schemas.FavoriteProductSchema(
            id=favorite_product.id,
            product=product,
        )

    return result
