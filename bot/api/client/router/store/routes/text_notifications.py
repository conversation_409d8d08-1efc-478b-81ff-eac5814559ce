from fastapi import Security, Depends

from core.auth.depend import get_active_user_optional
from core.text_notification.functions import create_and_send_text_notification
from db.models import Group, MenuInStore, User, ClientBot, Brand, Store

from fastapi import APIRouter

import schemas
from core.store.depends import get_current_brand, get_current_bot_with_brand

router = APIRouter(
    prefix="/text_notifications",
    tags=["text_notifications"],
)


@router.post(
    "/send",
    description="Method to send text notification",
)
async def send_text_notification(
        data: schemas.SendTextNotificationData,
        brand: Brand | None = Depends(get_current_brand),
        bot: ClientBot | None = Depends(get_current_bot_with_brand),
        user: User | None = Security(get_active_user_optional, scopes=["me:write"]),
):
    profile = await Group.get(brand.group_id)
    store = await Store.get(data.store_id)
    menu_in_store = await MenuInStore.get(data.menu_in_store_id)
    await create_and_send_text_notification(
        data.target,
        data.type,
        profile,
        store,
        user,
        bot,
        menu_in_store,
    )
    return {"status": "ok"}
