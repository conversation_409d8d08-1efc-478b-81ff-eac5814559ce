import logging

import schemas
from config import ANON_CART_TOKEN_EXPIRES
from core.ext.adapters.poster import check_poster_stocks
from core.store.functions.order import make_invoice_for_order
from db import crud
from db.models import StoreFavorite, StoreOrder
from utils.jwt_token import create_jwt_token

debugger = logging.getLogger('debugger')


async def process_store_order_full_bonuses_payment(
        store_order: StoreOrder, lang: str, brand_id: int,
) -> StoreOrder:
    # Invoice вже створений при створенні замовлення, не потрібно створювати новий
    invoice = store_order.invoice
    if not invoice:
        debugger.error(f"No invoice found for order {store_order.id}")
        return store_order
    
    # Фіналізуємо транзакцію лояльності для повної оплати бонусами
    if invoice.incust_transaction_id:
        from core.invoice_loyalty.service import InvoiceLoyaltyService
        invoice_loyalty_service = InvoiceLoyaltyService()
        await invoice_loyalty_service.finalize_invoice_loyalty(
            invoice, is_cancel=False
        )
        debugger.debug(f"Loyalty finalized for full bonuses payment, order {store_order.id}")
    else:
        debugger.debug(f"No loyalty transaction to finalize for order {store_order.id}")
    
    # Ставимо статус оплачений після успішної фіналізації
    await invoice.payed()

    return store_order


async def check_products_is_available(
        products: list[int],
        brand_id: int,
        store_id: int,
        store_external_type: str,
        store_external_id: str,
) -> list[schemas.NotAvailableProduct]:
    invalid_products = await crud.get_invalid_products(products, store_id, )

    invalid_products_set = set(
        (product.id, product.name) for product in invalid_products
    )

    if store_external_type == schemas.ExternalSystemTypes.poster.value:
        store_products = await crud.get_products_for_check(products)
        invalid_products_from_poster = await check_poster_stocks(
            brand_id, store_external_id, store_products
        )
        for product in invalid_products_from_poster:
            invalid_products_set.add((product.product_id, product.name))

    invalid_products_schemas = [
        schemas.NotAvailableProduct(product_id=product_id, name=name)
        for product_id, name in invalid_products_set
    ]

    return invalid_products_schemas


def get_favorite_token_if_anonymous(favorite: StoreFavorite):
    if favorite.user_id:
        return None

    return schemas.Token(
        token_type="Bearer",
        token=create_jwt_token(
            data={
                "sub": f"{favorite.id}",
                "type": "favorites",
            },
            expire=ANON_CART_TOKEN_EXPIRES
        )
    )
