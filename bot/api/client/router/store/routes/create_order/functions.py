from fastapi import HTTPException
from starlette import status

import schemas
from core.store.convertors import ShipmentConvertor
from core.store.types import ShipmentInfo
from db import crud
from db.models import (
    BillingSettings, Brand, BrandCustomSettings, Store, StoreAttribute,
    StoreCustomSettings, StoreOrder, StoreProduct, User,
)
from schemas.store.types import ShipmentType
from .exceptions import (
    CreateOrderDeliveryAddressRequiredError,
    CreateOrderDeliveryGetOrderInvalidDataError,
    CreateOrderDeliveryMethodMaxAmountError, CreateOrderDeliveryMethodMinAmountError,
    CreateOrderDeliveryMethodNotAvailablePriceError,
    CreateOrderDeliveryMethodNotPriceError,
    CreateOrderEmailIsRequiredError, CreateOrderFirstNameIsRequiredError,
    CreateOrderInStoreMenuInStoreIdIsRequiredError, <PERSON>reate<PERSON>rderLastNameIsRequiredError,
    CreateOrderPhoneIsRequiredError, CreateOrderShipmentCommentRequiredError,
    CreateOrderShipmentMethodDisabledError,
    CreateOrderShipmentMethodNotFoundError, CreateOrderTotalSumError,
    CreateOrderWrongShipmentMethodForServiceProducts,
)


async def validate_billing_address(
        store: Store, billing_address: schemas.BillingAddress | None
):
    billing_settings = await BillingSettings.get(store.__class__.__name__, store.id)
    if not billing_settings:
        billing_settings = await BillingSettings.get(
            store.brand.__class__.__name__, store.brand.id
        )

    if billing_settings and billing_settings.is_enable:
        if billing_settings.is_require and not billing_address:
            detail = "billing address must be require"
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

        if billing_address:
            if billing_settings.need_person:
                billing_address.counterparty_type = "person"
                if not all([billing_address.first_name, billing_address.last_name]):
                    detail = "billing address must contain first_name and last_name"
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST, detail=detail
                    )

            elif billing_settings.need_organisation:
                billing_address.counterparty_type = "organisation"
                if not billing_address.company_name and \
                        not any(
                            [billing_address.vat_number,
                             billing_address.registration_number]
                        ):
                    detail = ("billing address must contain company_name and "
                              "vat_number or registration_number")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST, detail=detail
                    )


async def validate_order_shipment_methods(
        store: Store,
        lang: str,
        form_create: schemas.CreateOrderSchema | None = None,
        form_update: schemas.UpdateOrderSchema | None = None,
        order_id: int | None = None,
) -> BrandCustomSettings:
    if not form_create and not form_update:
        raise Exception("form_create or form_update must be specified")

    shipment_method_id = form_create.shipment_method_id if form_create else (
        form_update.shipment_method_id)
    shipment_method = await get_shipment_info(
        shipment_method_id, store.brand_id, store.id
    )

    if form_update and order_id:
        order_products = await crud.get_order_products(order_id)
        products = [
            schemas.CreateOrderProduct(
                quantity=order_product.quantity,
                product_id=order_product.product.id,
                floating_sum=order_product.total_sum if
                order_product.product.floating_sum_value else 0,
                attributes=[schemas.CreateOrderAttribute(
                    quantity=attribute.quantity,
                    attribute_id=attribute.attribute_id,
                    price_impact=attribute.attribute.price_impact,
                ) for attribute in order_product.attributes]
            ) for order_product in order_products
        ]
    else:
        products = form_create.products

    # validate shipment_method enabled
    if not shipment_method.is_enabled:
        # localised only base types!
        raise CreateOrderShipmentMethodDisabledError(
            await shipment_method.settings.get_name(lang)
        )

    # validate shipment_method for only service or topup products order
    service_count = 0
    for product in products:
        db_product = await StoreProduct.get(product.product_id)
        if db_product.type == "service":
            service_count += 1
    if len(products) == service_count:
        if not shipment_method.settings.base_type == ShipmentType.NO_DELIVERY.value:
            raise CreateOrderWrongShipmentMethodForServiceProducts(
                await shipment_method.settings.get_name(lang)
            )

    custom_shipment_comment = form_create.custom_shipment_comment if form_create else \
        form_update.custom_shipment_comment
    # validate custom shipment_method
    if shipment_method.is_custom:
        # validate comment specified if needed
        if shipment_method.settings.need_comment and not custom_shipment_comment:
            raise CreateOrderShipmentCommentRequiredError()
    # validate base shipment_method
    else:
        match shipment_method.settings.base_type:
            case ShipmentType.IN_STORE.value:
                menu_in_store_id = form_create.menu_in_store_id if form_create else (
                    form_update.menu_in_store_id)
                if not menu_in_store_id:
                    raise CreateOrderInStoreMenuInStoreIdIsRequiredError()
            case ShipmentType.DELIVERY.value:
                # validate get order delivery data
                address_street = form_create.address_street if form_create else (
                    form_update.address_street)
                address_house = form_create.address_house if form_create else (
                    form_update.address_house)
                address_flat = form_create.address_flat if form_create else (
                    form_update.address_flat)
                address_floor = form_create.address_floor if form_create else (
                    form_update.address_floor)
                address_entrance = form_create.address_entrance if form_create else (
                    form_update.address_entrance)
                delivery_address = form_create.delivery_address if form_create else (
                    form_update.delivery_address)
                if store.get_order_id:
                    if not all(
                            (
                                    address_street,
                                    address_house,
                                    address_flat,
                                    address_floor,
                                    address_entrance,
                            )
                    ):
                        raise CreateOrderDeliveryGetOrderInvalidDataError()
                elif not delivery_address:
                    raise CreateOrderDeliveryAddressRequiredError()

    if form_update and order_id:
        order = await StoreOrder.get(order_id)
        price_after_loyalty = order.before_loyalty_sum
    else:
        price_after_loyalty = form_create.price_after_loyalty

    products_total_sum = await calculate_order_products_total(
        products, price_after_loyalty
    )

    brand = await Brand.get(store.brand_id)

    price = form_create.price if form_create else form_update.price
    delivery_address = form_create.delivery_address if form_create else (
        form_update.delivery_address)
    address_street = form_create.address_street if form_create else (
        form_update.address_street)
    address_house = form_create.address_house if form_create else (
        form_update.address_house)
    if price:
        address = delivery_address or (
            ", ".join([address_street, address_house])
            if address_street and address_house else None
        )
        products_total_sum_float = round(products_total_sum / 100, 2)
        address_lat = form_create.address_lat if form_create else (
            form_update.address_lat)
        address_lng = form_create.address_lng if form_create else (
            form_update.address_lng)
        convertor = ShipmentConvertor(
            brand, store_id=store.id,
            shipment_id=shipment_method.settings.id,
            shipment_store_settings_id=shipment_method.store_settings.id,
            order_sum=products_total_sum_float,
            address=address, lang=lang,
            purpose="validate",
            address_lat=address_lat,
            address_lng=address_lng,
        )

        price = await convertor.get_price()
        available_prices = await convertor.get_available_prices()
        if not any([price, available_prices]):
            raise CreateOrderDeliveryMethodNotPriceError()
        available_prices_ids = [price.id] if price else []
        available_prices_ids.extend(
            [available_price.id for available_price in available_prices]
        )
        price_id = form_create.price.id if form_create else form_update.price.id
        if price_id not in available_prices_ids:
            raise CreateOrderDeliveryMethodNotAvailablePriceError()

        if not price or price_id != price.id:
            for available_price in available_prices:
                if price_id == available_price.id:
                    price = available_price
                    break

        # validate min price
        if ((
                min_price := price.minimum_order_amount) and products_total_sum_float <
                min_price):
            raise CreateOrderDeliveryMethodMinAmountError(
                min_price, products_total_sum_float
            )
        # validate max price
        if ((
                max_price := price.maximum_order_amount) and products_total_sum_float >
                max_price):
            raise CreateOrderDeliveryMethodMaxAmountError(
                max_price, products_total_sum_float
            )
    else:
        menu_in_store_id = form_create.menu_in_store_id if form_create else (
            form_update.menu_in_store_id)
        free_shipment = form_create.free_shipment if form_create else (
            form_update.free_shipment)
        if not menu_in_store_id:
            if free_shipment:
                need_prices = True
                if len(products) == 1:
                    db_product = await StoreProduct.get(products[0].product_id)
                    if db_product.type == "gift" or db_product.type == "topup":
                        need_prices = False
                if need_prices:
                    prices = await crud.get_shipment_prices(
                        brand_id=brand.id, settings_id=shipment_method.settings.id
                    )
                    if prices:
                        raise CreateOrderDeliveryMethodNotPriceError()
            else:
                raise CreateOrderDeliveryMethodNotPriceError()

    return shipment_method.settings


async def calculate_order_products_total(
        products: list[schemas.CreateOrderProduct],
        price_after_loyalty: float | int | None = None
):
    total: int = 0

    for product_schema in products:
        product_schema = schemas.CreateOrderProduct(**product_schema.dict())
        product = await StoreProduct.get(product_schema.product_id)

        product_price = product.price

        if product_schema.attributes:
            for attribute_schema in product_schema.attributes:
                attribute = await StoreAttribute.get(attribute_schema.attribute_id)
                product_price += attribute.price_impact * attribute_schema.quantity

        total += product_price * product_schema.quantity

    if total < 0 or (price_after_loyalty and price_after_loyalty < 0):
        raise CreateOrderTotalSumError()

    return total


async def get_shipment_info(
        shipment_method_id: int,
        brand_id: int,
        store_id: int | None = None,
) -> ShipmentInfo:
    store_settings: StoreCustomSettings | None

    settings = await BrandCustomSettings.get(shipment_method_id, brand_id=brand_id)
    if not settings:
        raise CreateOrderShipmentMethodNotFoundError(shipment_method_id)

    store_settings = await StoreCustomSettings.get_or_create(
        store_id=store_id,
        custom_settings_id=settings.id
    )

    return ShipmentInfo(
        settings,
        store_settings,
    )


async def validate_order_personal_data(
        form: schemas.CreateOrderSchema,
        shipment_method: BrandCustomSettings,
        user: User, brand: Brand,
):
    if shipment_method.base_type == ShipmentType.IN_STORE.value:
        return

    user_client = user.client
    order_name_mode = brand.get_field_mode(
        user_client, schemas.OrderFieldEnum.ORDER_NAME
    )
    if form.type != "gift":
        email_mode = brand.get_field_mode(user_client, schemas.OrderFieldEnum.EMAIL)
        phone_mode = brand.get_field_mode(user_client, schemas.OrderFieldEnum.PHONE)

        match email_mode, bool(form.email):
            case schemas.OrderFieldSettingEnum.REQUIRED, False:
                raise CreateOrderEmailIsRequiredError()

            # not need this condition if email required for payment provider
            # case schemas.OrderFieldSettingEnum.DISABLED, True:
            #     form.email = None

        match phone_mode, bool(form.phone):
            case schemas.OrderFieldSettingEnum.REQUIRED, False:
                raise CreateOrderPhoneIsRequiredError()
            case schemas.OrderFieldSettingEnum.DISABLED, True:
                form.phone = None

        match order_name_mode, bool(form.last_name):
            case schemas.OrderNameSettingEnum.FIRST_AND_LAST, False:
                raise CreateOrderLastNameIsRequiredError()

        # if not form.last_name:
        #     raise CreateOrderLastNameIsRequiredError()
    else:
        form.phone = None
        form.email = None

    match order_name_mode, bool(form.first_name):
        case schemas.OrderNameSettingEnum.FIRST_AND_LAST, False:
            raise CreateOrderFirstNameIsRequiredError()
        case schemas.OrderNameSettingEnum.ONLY_FIRST, False:
            raise CreateOrderFirstNameIsRequiredError()
    # if not form.first_name:
    #     raise CreateOrderFirstNameIsRequiredError()
