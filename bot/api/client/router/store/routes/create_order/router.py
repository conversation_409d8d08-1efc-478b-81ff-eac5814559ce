from fastapi import (APIRouter, Depends, Security)

import schemas
from core.api.depends import get_lang
from core.auth.depend import get_user_optional, get_user_or_token_data_safe
from core.store.depends import get_current_brand
from db.models import Brand, BrandCustomSettings, User
from schemas.store.types import ShipmentType
from .service import CreateOrderService

router = APIRouter(
    prefix="/order",
    tags=["store/order"],
)


@router.post(
    "/create",
    description="Method to create order",
)
async def create_order(
        service: CreateOrderService = Depends(),
) -> schemas.OrderSchema:
    return await service.process()


@router.post(
    "/createInStore",
    description="Method to create order in sore"
)
async def create_order_in_store(
        data: schemas.CreateOrderInStoreSchema,
        brand: Brand = Depends(get_current_brand),
        user: User | None = Security(get_user_optional),
        token_data: dict = Depends(get_user_or_token_data_safe),
        lang: str = Depends(get_lang),
) -> schemas.OrderSchema:
    shipment_method = await BrandCustomSettings.get_or_create_base_shipment(
        brand.id, ShipmentType.IN_STORE.value,
    )

    create_order_service = CreateOrderService(
        schemas.CreateOrderSchema(
            first_name=user.first_name if user else None,
            last_name=user.last_name if user else None,
            email=user.email if user else None,
            shipment_method_id=shipment_method.id,
            comment=data.comment,
            menu_in_store_id=data.menu_in_store_id,
            loyalty_type=data.loyalty_type,
            price_after_loyalty=data.price_after_loyalty,
            bonuses_redeemed=data.bonuses_redeemed,
            discount=data.discount,
            store_id=data.store_id,
            products=data.products,
            incust_check=data.incust_check,
            incust_terminal_id=data.incust_terminal_id,
            marketing_consent=data.marketing_consent,
            is_accepted_agreement=data.is_accepted_agreement,
            tips_sum=data.tips_sum,
            utm_labels=data.utm_labels if data.utm_labels else None,
        ), brand, user, token_data, lang
    )
    return await create_order_service.process()
