from abc import ABC

from starlette import status

import schemas
from utils.exceptions import ErrorWithHTTPStatus


class BaseCreateOrderError(ErrorWithHTTPStatus, ABC, base=True):
    def __init__(self, err_message: str = "CreateOrder error", **kwargs):
        self.message = err_message
        super().__init__(**kwargs)

    def __repr__(self):
        return f"CreateOrder error: {self.message}"


class CreateOrderUnknownError(BaseCreateOrderError):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    text_variable = "store brand create order unknown error"

    def __init__(self, **kwargs):
        super().__init__("CreateOrde<PERSON> failed with unknown error", **kwargs)


class StoreFinanceSystemError(BaseCreateOrderError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "store must have finance system error"

    def __init__(self, store_id: int):
        super().__init__(
            f"{store_id=} not set finance system",
            store_id=store_id,
        )


class CreateOrderValidateDataError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand create order validation error"


class CreateOrderNotProductDataError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand create order not products error"


class CreateOrderCreateError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand create order unknown error"


class CreateOrderNotInStockDataError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand create order not in stock error"

    def __init__(self, products: list[schemas.NotAvailableProduct]):
        super().__init__(
            "store brand create order not in stock error",
            products="<br>".join([f"{product.product_id} - {product.name}" for product in products])
        )


class CreateOrderCartError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "store brand create order cart error"


class CreateOrderLoyaltyError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "external loyalty error"

    def __init__(self, message: str):
        super().__init__(message=message)


class CreateOrderAgreementIsNotAcceptedError(BaseCreateOrderError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "crate order agreement is not accepted error"


class CreateOrderInStoreMenuInStoreIdIsRequiredError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "order in store menu in store id is required error"


class CreateOrderShipmentCommentRequiredError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "order shipment comment required error"


class CreateOrderWrongShipmentMethodForServiceProducts(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order wrong shipment method for service error"

    def __init__(self, shipment_name: str):
        super().__init__(shipment_name=shipment_name)


class CreateOrderShipmentMethodNotFoundError(BaseCreateOrderError):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "order shipment method not found error"

    def __init__(self, shipment_id: int):
        super().__init__(shipment_id=shipment_id)


class CreateOrderShipmentMethodDisabledError(BaseCreateOrderError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "order shipment method disabled error"

    def __init__(self, shipment_method: str):
        self.shipment_method = shipment_method
        super().__init__(shipment_method=shipment_method)


class CreateOrderDeliveryGetOrderInvalidDataError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "order delivery get order invalid data error"


class CreateOrderDeliveryAddressRequiredError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "order delivery address required error"


class CreateOrderDeliveryMethodMinAmountError(BaseCreateOrderError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "order delivery method min amount error"

    def __init__(self, min_amount: int | float, actual_amount: int | float):
        super().__init__(
            min_amount=min_amount,
            actual_amount=actual_amount,
        )


class CreateOrderDeliveryMethodMaxAmountError(BaseCreateOrderError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "order delivery method max amount error"

    def __init__(self, max_amount: int | float, actual_amount: int | float):
        super().__init__(
            max_amount=max_amount,
            actual_amount=actual_amount,
        )


class CreateOrderConflictPaymentMethodsError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "order conflict payment methods error"


class CreateOrderPaymentMethodNotFoundError(BaseCreateOrderError):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "order payment method not found error"

    def __init__(self, payment_method: int):
        self.payment_method: int = payment_method
        super().__init__(payment_method=payment_method)


class CreateOrderPaymentMethodDisabledError(BaseCreateOrderError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "order payment method disabled error"

    def __init__(self, payment_method: str):
        self.payment_method = payment_method
        super().__init__(payment_method=payment_method)


class CreateOrderCustomPaymentCommentRequiredError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "order custom payment comment required error"


class CreateOrderCommentRequiredError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "order order comment required error"


class CreateOrderTotalSumError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "order total sum error"


class CreateOrderEmailIsRequiredError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order email is required error"


class CreateOrderPhoneIsRequiredError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order phone is required error"


class CreateOrderFirstNameIsRequiredError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order first name is required error"


class CreateOrderLastNameIsRequiredError(BaseCreateOrderError):
    status_code = status.HTTP_400_BAD_REQUEST
    text_variable = "create order last name is required error"


class CreateOrderDeliveryMethodNotPriceError(BaseCreateOrderError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "order delivery method not price error"


class CreateOrderDeliveryMethodNotAvailablePriceError(BaseCreateOrderError):
    status_code = status.HTTP_403_FORBIDDEN
    text_variable = "order delivery method not available prices error"
