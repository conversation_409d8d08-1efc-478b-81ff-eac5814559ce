from fastapi import APIRouter

from . import (
    cart, check_instock, core, create_order, favorites, order, scan, text_notifications,
)

# from .create_order.handler import router as create_order_router

router = APIRouter(
    tags=["store"],
    prefix="/store",
)

router.include_router(cart.router)
router.include_router(check_instock.router)
router.include_router(core.router)
router.include_router(text_notifications.router)
router.include_router(order.router)
router.include_router(create_order.router)
router.include_router(favorites.router)
router.include_router(scan.router)
