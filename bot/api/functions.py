import logging

from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt

from config import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SECRET_KEY, SERVICE_BOT_API_TOKEN
from db.models import ClientBot, ExternalSystemToken


async def get_bot_token(bot_id: int | str | None):
    if bot_id == "service":
        bot_token = SERVICE_BOT_API_TOKEN
    elif bot_id:
        bot = await ClientBot.get(bot_id)
        bot_token = bot.token if bot else None
    else:
        bot_token = None

    return bot_token


async def get_external_system_by_token(
        external_token: str
) -> ExternalSystemToken | None:
    # for local not valid token or secret_key
    # return await ExternalSystemToken.get(id=1)
    try:
        payload = jwt.decode(external_token, SECRET_KEY, algorithms=[ALGORITHM])
        external_system_id = payload.get("id")
        if external_system_id:
            external_system = await ExternalSystemToken.get(id=external_system_id)
            if external_system:
                return external_system
        return None
    except <PERSON><PERSON><PERSON><PERSON>r as e:
        logging.error(e, exc_info=True)
        return None
