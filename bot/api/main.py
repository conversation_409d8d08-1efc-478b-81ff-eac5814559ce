import logging
import os.path
from contextlib import asynccontextmanager

from aiogram import Dispatcher
from fastapi import Depends
from fastapi.staticfiles import StaticFiles
from psutils.fastapi.api_route_error_groups import APIRouteErrorGroups
from psutils.fastapi.logging import setup_uvicorn_loggers
from psutils.local import setup_psutils_localisation

from client.init import bot, dp
from config import FASTAPI_DEBUG, LOGS_FOLDER
from core.api.depends import get_header_lang
from core.api.middlewares import (
    CustomCORSMiddleware, LangMiddleware,
    SessionMiddleware, WebAppMiddleware,
)
from core.api.middlewares.http_log import HTTPLogMiddleware
from core.api.middlewares.request_received_datetime import \
    RequestReceivedDateTimeMiddleware
from core.api.middlewares.unhandled_exception import UnhandledExceptionMiddleware
from core.kafka.producer.producer_instance import producer
from utils.exceptions import ErrorWithHTTPStatus
from utils.localisation import localisation
from utils.logger import setup_logger, syslog_formatter
from utils.redefined_classes import Bot
from . import (
    ad, admin, auth, billing, client, crm, ewallet, external_data, integration,
    localisation as localisation_api, maps, notifications, platform, reports, shortener,
    user, utils, webhook_demo,
)
from .custom_fastapi import CustomFastApi as FastAPI
from .docs_routes import register_app_docs_routes
from .exception_handlers import register_general_exception_handlers
from .scheduler import shutdown_scheduler, start_scheduler

Dispatcher.set_current(dp)
Bot.set_current(bot)


@asynccontextmanager
async def lifespan(_):
    await setup_psutils_localisation()
    await producer.initialise()
    await localisation.load_data_to_local()
    APIRouteErrorGroups.set_error_with_http_status_cls(ErrorWithHTTPStatus)

    setup_logger("api")
    setup_uvicorn_loggers(
        os.path.join(LOGS_FOLDER, "api"), "7loc.log", errors_handlers_formatters={
            "syslog": syslog_formatter
        }
    )
    start_scheduler()

    yield
    try:
        await producer.stop()
        shutdown_scheduler()

    except Exception as e:
        logging.getLogger("error.kafka.producer").error(
            f"An error occurred while shutting down kafka producer:{repr(e)} "
        )


app = FastAPI(
    title="7Loc Client API",
    debug=FASTAPI_DEBUG,
    dependencies=[Depends(get_header_lang)],
    docs_url=None,
    redoc_url=None,
    lifespan=lifespan,
)
# noinspection PyUnresolvedReferences
app.router.route_class = APIRouteErrorGroups

RequestReceivedDateTimeMiddleware().setup(app)
# noinspection PyTypeChecker
app.add_middleware(CustomCORSMiddleware)

SessionMiddleware.setup(app)
WebAppMiddleware().setup(app)
LangMiddleware().setup(app)
app.add_middleware(UnhandledExceptionMiddleware)
HTTPLogMiddleware().setup(app)

app.mount("/admin", admin.app)
app.mount("/auth", auth.app)
app.mount("/user", user.app)
app.mount("/client", client.app)
app.mount("/crm_new", crm.app)
app.mount("/data", external_data.app)
app.mount("/integration", integration.app)
app.mount("/localisation", localisation_api.app)
app.mount("/utils", utils.app)
app.mount("/shortener", shortener.app)
app.mount("/platform", platform.app)
app.mount("/webhooks_demo", webhook_demo.app)
app.mount("/billing", billing.app)
app.mount("/static", StaticFiles(directory="../bot/static"), name="static")
app.mount("/ewallet", ewallet.app)
app.mount("/reports", reports.app)
app.mount("/notifications", notifications.app)
app.mount("/maps", maps.app)
app.mount("/ad", ad.app)

app.include_router(client.router)  # client api

register_general_exception_handlers(app)
register_app_docs_routes(app)


@app.get("/health")
def health():
    return "OK"
