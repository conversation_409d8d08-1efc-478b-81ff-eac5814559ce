from pprint import pformat

from fastapi import APIRouter, Depends
from psutils.fastapi.api_route_error_groups import APIRouteErrorGroups

import schemas
from core.api.depends import get_lang
from core.invoice import InvoiceService
from utils.platform_admins import send_message_to_platform_admins

router = APIRouter(
    prefix="/payments",
    tags=["payments"],
    route_class=APIRouteErrorGroups,
)


@router.post(
    "/validateProfileToken",
    responses={
        "error_groups": {
            "groups": [
                "validate_group_for_integration"
            ]
        }
    }
)
async def validate_profile_token(
        service: InvoiceService = Depends(InvoiceService.depend_group_for_integration)
) -> schemas.ProfileTokenForIntegrationValid:
    return await service.validate_group_token_for_integration()


@router.post(
    "/validateUserToken",
    responses={
        "error_groups": {
            "groups": [
                "token_error",
                "user_token_error",
                "validate_user_for_integration"
            ]
        }
    }
)
async def validate_user_token(
        service: InvoiceService = Depends(InvoiceService.depend_user_for_integration)
) -> schemas.UserTokenForIntegrationValid:
    return await service.validate_user_token_for_integration()


@router.get(
    "/status",
    description="Method to check status invoice. "
                "Requires the usage of Authorization: Bearer {invoice_token} in the header",
    responses={
        "error_groups": {
            "groups": [
                "token_error",
                "token_scopes_error",
                "invoice_token_error",
            ]
        }
    }
)
async def check_status(
        service: InvoiceService = Depends(InvoiceService.depend_invoice_id_with_group),
) -> schemas.InvoiceStatus:
    return await service.check_status()


@router.post(
    "",
    description="Method to create invoice and generate url for payment. "
                "To test with an authorized buyer, you need to log in via the Authorize button. "
                "This add Authorization: Bearer {user_token} to the header.",
    responses={
        "error_groups": {
            "groups": [
                "token_error",
                "user_token_error",
                "validate_group_for_integration",
                "validate_user_for_integration"
                "create_invoice_for_integration",
            ],
        }
    }
)
async def create_ext_invoice(
        data: schemas.CreateInvoiceWithUrlData,
        service: InvoiceService = Depends(InvoiceService.depend_group_and_anonymous_user),
        lang: str = Depends(get_lang)
) -> schemas.InvoiceCreatedResult:
    return await service.create_invoice_with_url(data, lang)


@router.post("/WebhookTest", include_in_schema=False)
async def webhook_test(
        data: schemas.InvoiceSchema,
) -> dict:
    await send_message_to_platform_admins(
        f"Received webhook with data: {pformat(data.dict(), indent=4)}"
    )

    return {
        "success": True,
    }
