from fastapi import APIRouter, FastAPI
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from starlette.requests import Request

from config import FAVICON_URL

router = APIRouter(
    include_in_schema=False,
    tags=["docs"]
)


def docs(request: Request):
    root_path = request.scope.get("root_path", "").rstrip("/")
    openapi_url = root_path + request.app.openapi_url

    return get_swagger_ui_html(
        openapi_url=openapi_url,
        title=request.app.title,
        swagger_favicon_url=FAVICON_URL,
    )


def redoc(request: Request):
    root_path = request.scope.get("root_path", "").rstrip("/")
    openapi_url = root_path + request.app.openapi_url

    return get_redoc_html(
        openapi_url=openapi_url,
        title=request.app.title,
        redoc_favicon_url=FAVICON_URL,
    )


def register_app_docs_routes(app: FastAPI):
    app.get("/docs", include_in_schema=False)(docs)
    app.get("/redoc", include_in_schema=False)(redoc)
