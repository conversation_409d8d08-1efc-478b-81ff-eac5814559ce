from datetime import datetime
from fastapi import APIRouter, Body, Depends, Query, Request, Security
from fastapi.security import SecurityScopes
from psutils.fastapi.api_route_error_groups import APIRouteErrorGroups

import schemas
from config import ACCESS_TOKEN_EXPIRES
from core.api.depends import get_lang
from core.auth.depend import (
    get_active_user, get_auth_session_by_access_token,
    get_auth_session_by_refresh_token, get_current_tg_user_safe,
    get_required_auth_session_from_access_token, parse_access_token_depend,
    parse_access_token_safe,
)
from core.auth.functions import (
    create_auth_session_and_get_tokens, create_check_is_email_confirmed_token,
    create_user_access_token,
    create_user_auth_token, get_marketing_info as get_marketing_info_func,
    parse_access_token,
)
from core.auth.parse_token import parse_token
from core.auth.services import AuthService
from core.store.depends import get_current_brand
from db.models import AuthSession, <PERSON>, Customer, Group, User
from exceptions import (
    AuthNotAuthorisedError, AuthTokenExpiredError,
)
from utils.date_time import utcnow

router = APIRouter(
    tags=["auth"],
    route_class=APIRouteErrorGroups,
)


@router.post(
    "/validateToken",
    responses={
        "error_groups": {
            "groups": ["token_error"],
        }
    }
)
async def validate_token(
        token_data: dict = Depends(parse_access_token_depend),
) -> schemas.ValidateTokenResult:
    return schemas.ValidateTokenResult(
        sub=token_data.get("sub"),
        type=token_data.get("type"),
        exp=token_data.get("exp"),
        scopes=token_data.get("scopes"),
    )


@router.post("/sendConfirmEmail")
async def send_confirm_email(
        data: schemas.SendConfirmEmailData,
        service: AuthService = Depends(),
) -> schemas.ConfirmEmailRequestSent:
    request = await service.send_confirm_email(
        data.email, data.purpose, data.user_id, data.is_external_user,
    )
    token = create_check_is_email_confirmed_token(request)

    return schemas.ConfirmEmailRequestSent(
        sent=True,
        email=data.email,
        token=token,
        token_type="bearer"
    )


@router.post("/confirmEmail")
async def confirm_email(
        data: schemas.ConfirmEmailData | None | dict = Body(default=None),
        service: AuthService = Depends(),
        token_data: dict | None = Security(
            parse_access_token_safe, scopes=["emailConfirm"]
        ),
) -> schemas.ConfirmEmailResult:
    if data and data.token:
        exception = AuthNotAuthorisedError()
        exp_exception = AuthTokenExpiredError()
        payload, token_scopes = parse_token(data.token, exception, exp_exception)
        if "emailConfirm" not in token_scopes:
            raise exception
        token_data = payload
    else:
        if not token_data:
            raise AuthNotAuthorisedError()

    request = await service.confirm_email(token_data)

    return schemas.ConfirmEmailResult(
        confirmed=True,
        email=request.email,
        purpose=request.purpose,
    )


@router.get("/checkIsEmailConfirmed")
async def check_is_email_confirmed(
        service: AuthService = Depends(),
        token_data: dict = Security(
            parse_access_token_depend, scopes=["checkIsEmailConfirmed"]
        )
) -> schemas.IsEmailConfirmedData:
    return await service.check_is_email_confirmed(token_data)


@router.post("/login")
async def login(
        data: schemas.LoginData,
        service: AuthService = Depends()
) -> schemas.Token:
    return await service.login(data)


@router.post("/new_login")
async def new_login(
        data: schemas.NewLoginData,
        service: AuthService = Depends(),
) -> schemas.AuthorisedResponse:
    return await service.new_login(data)


@router.post("/logout")
async def logout(
        auth_session: AuthSession = Depends(get_auth_session_by_access_token),
        service: AuthService = Depends(),
) -> schemas.OkResponse:
    return await service.logout(auth_session)


@router.post("/set_push_token")
async def set_push_token(
        data: schemas.SetPushTokenData,
        auth_session: AuthSession = Depends(get_auth_session_by_access_token),
        service: AuthService = Depends(),
) -> schemas.OkResponse:
    return await service.set_push_token(data.push_token, auth_session)


@router.post("/refresh_token")
async def refresh_token(
        auth_session: AuthSession = Depends(get_auth_session_by_refresh_token),
        service: AuthService = Depends(),
) -> schemas.AuthorisedResponse:
    return await service.refresh_token(auth_session)


@router.post(
    "/login/tg_webview",
    response_description="Method to login with tg webview data",
    description="Returns auth token",
)
async def get_tg_webview_token(
        data: schemas.LoginByWebViewData | None = None,
        user: User | None = Depends(get_current_tg_user_safe),
        service: AuthService = Depends()
) -> schemas.Token:
    if not user:
        user = await service.login_by_tg_web_data(
            data.web_view_token if data else None, data.bot_id if data else None
        )

    token = create_user_access_token(user.id, ACCESS_TOKEN_EXPIRES)
    return schemas.Token(
        token=token,
        token_type="bearer",
    )


@router.post(
    "/new_login/tg_webview",
    response_description="Method to login with tg webview data",
    description="Returns auth token",
)
async def new_get_tg_webview_token(
        data: schemas.NewLoginByWebViewData | None = None,
        user: User | None = Depends(get_current_tg_user_safe),
        service: AuthService = Depends()
) -> schemas.AuthorisedResponse:
    if not user:
        user = await service.login_by_tg_web_data(
            data.web_view_token if data else None, data.bot_id if data else None
        )

    return await create_auth_session_and_get_tokens(
        user, data.auth_source, data.device_info
    )


@router.post("/login/tg_webview_new")
async def login_by_tg_webview_new(
        data: schemas.LoginByWebViewNewData,
        service: AuthService = Depends()
) -> schemas.AuthorisedResponse:
    return await service.login_by_tg_web_data_new(data)


@router.post(
    "/login/by_short_token",
    response_description="Method to login with short token",
    description="Returns auth token",
)
async def get_token_by_short_token(
        data: schemas.LoginByShortTokenData,
        service: AuthService = Depends()
) -> schemas.LoginByShortTokenResult:
    return await service.login_by_short_token(data)


@router.post(
    "/login/by_google",
    response_description="Method to login with google",
    description="Returns auth token",
)
async def get_token_by_google(
        data: schemas.OAuthLoginToken,
        service: AuthService = Depends()
) -> schemas.OAuthToken | schemas.NewOAuthToken:
    return await service.login_by_google(
        data, lang=service.lang, new_login=data.is_new_login,
        new_login_func=create_auth_session_and_get_tokens,
        profile_id=service.brand.group_id if service.brand else None,
    )


@router.post(
    "/login/by_apple",
    response_description="Method to login with apple",
    description="Returns auth token",
)
async def get_token_by_apple(
        data: schemas.OAuthAppleLoginToken,
        service: AuthService = Depends()
) -> schemas.OAuthToken | schemas.NewOAuthToken:
    return await service.login_by_apple(
        data, lang=service.lang, new_login=data.is_new_login,
        new_login_func=create_auth_session_and_get_tokens,
        profile_id=service.brand.group_id if service.brand else None,
    )


@router.post("/login/by_auth_token")
async def login_by_auth_token(
        data: schemas.LoginByAuthTokenData,
) -> schemas.AuthorisedResponse:
    token_data = parse_access_token_depend(data.token, SecurityScopes(["auth"]))
    if not token_data.get("type") == "auth" or not (
            user := await User.get_by_id(token_data.get("sub"))):
        raise AuthNotAuthorisedError()
    return await create_auth_session_and_get_tokens(
        user, data.auth_source, data.device_info
    )


@router.post("/generateAuthToken")
async def generate_user_auth_token(
        user: User = Security(get_active_user, scopes=["me:write"])
) -> schemas.Token:
    return schemas.Token(
        token_type="bearer",
        token=create_user_auth_token(user.id)
    )


@router.post("/register")
async def register(
        data: schemas.RegisterData,
        service: AuthService = Depends(),
) -> schemas.Token:
    return await service.register(data)


@router.post("/new_register")
async def new_register(
        data: schemas.NewRegisterData,
        service: AuthService = Depends(),
) -> schemas.AuthorisedResponse:
    return await service.new_register(data)


@router.post("/register/oauth")
async def register_oauth(
        data: schemas.OAuthRegisterToken,
        service: AuthService = Depends(),
) -> schemas.Token:
    return await service.register_by_oauth_token(
        data, service.lang, service.brand.group_id if service.brand else
        None
    )


@router.post("/customer_consent/oauth")
async def customer_consent(
        data: schemas.OAuthRegisterToken,
        service: AuthService = Depends(),
) -> schemas.Token:
    return await service.customer_consent(
        data, service.lang, service.brand.group_id if service.brand else
        None
    )


@router.post("/new_customer_consent/oauth")
async def new_customer_consent(
        data: schemas.NewOauthRegisterToken,
        service: AuthService = Depends(),
) -> schemas.AuthorisedResponse:
    return await service.new_customer_consent(
        data, service.lang, (
            service.brand.group_id
            if service.brand
            else None
        )
    )


@router.post("/new_register/oauth")
async def new_register_oauth(
        data: schemas.NewOauthRegisterToken,
        service: AuthService = Depends(),
) -> schemas.AuthorisedResponse:
    return await service.new_register_by_oauth_token(
        data, service.lang, service.brand.group_id if service.brand else
        None
    )


@router.post("/checkIsEmailExists/{email}")
async def check_is_email_exists(
        email: str,
        service: AuthService = Depends(),
) -> schemas.IsEmailExists:
    return await service.check_is_email_exists(
        email, service.brand.group_id if service.brand else None
    )


@router.get("/checkIsChatIdExists")
async def check_is_chat_id_exists(
        chat_id: int = Query(),
        service: AuthService = Depends()
) -> schemas.IsChatIDExists:
    return await service.check_is_chat_id_exists(chat_id)


@router.post("/resetPassword")
async def reset_password(
        data: schemas.ResetPasswordData,
        service: AuthService = Depends(),
        token_data: dict | None = Security(
            parse_access_token_safe, scopes=["resetPassword"]
        ),
):
    if not token_data:
        if data.token is None:
            raise AuthNotAuthorisedError()
        token_data = parse_access_token(data.token, SecurityScopes())

    await service.confirm_email(token_data)
    return await service.reset_user_password(data)


@router.post("/changeEmail")
async def change_email(
        data: schemas.ChangeEmailData,
        service: AuthService = Depends(),
        token: dict | None = Security(parse_access_token_safe, scopes=["changeEmail"]),
):

    request_id = None
    if data.confirm_token:
        exception = AuthNotAuthorisedError()
        exp_exception = AuthTokenExpiredError()
        payload, token_scopes = parse_token(
            data.confirm_token, exception, exp_exception
        )
        if "changeEmail" not in token_scopes:
            raise exception
        user_id = payload.get("user_id")
        request_id = payload.get("request_id", None)
    else:
        if not token:
            raise AuthNotAuthorisedError()
        user_id = token.get("user_id")

    return await service.change_user_email(data, user_id, request_id)


@router.post("/apple_oauth")
async def apple_oauth_callback(
        request: Request,
        service: AuthService = Depends(),
):
    form = await request.form()
    form_dict = dict(form.items())
    data_schema = schemas.AppleFormData(**form_dict)
    return await service.process_apple_oauth_callback(data_schema, lang=service.lang)


@router.post("/oauth/user_info")
async def get_user_info_by_oauth_token(
        data: schemas.OAuthRegisterToken,
        service: AuthService = Depends(),
) -> schemas.OAuthRegisterData:
    return service.get_user_data_by_oauth_token(data.oauth_token)


@router.get("/heartbeat")
async def heartbeat(
        activity_datetime: datetime = Query(description="Datetime in UTC ISO format"),
        service: AuthService = Depends(),
        auth_session: AuthSession = Depends(get_required_auth_session_from_access_token)
) -> schemas.OkResponse:
    return await service.heartbeat(activity_datetime, auth_session)


@router.get(
    "/marketing_info",
    response_description="Method to get marketing info",
    description="Returns marketing info",
    response_model=schemas.MarketingInfo,
)
async def get_marketing_info(
        lang: str = Depends(get_lang),
        brand: Brand = Depends(get_current_brand),
):
    profile = await Group.get(brand.group_id)
    return get_marketing_info_func(lang, profile.name)


@router.post(
    "/update_marketing_consent",
    response_description="Method to update customer marketing consent",
    response_model=schemas.OkResponse,
)
async def update_marketing_consent(
        data: schemas.MarketingConsentSchema,
        user: User = Security(get_active_user, scopes=["me:write"]),
        brand: Brand = Depends(get_current_brand),
):
    customer = await Customer.get(user_id=user.id, profile_id=brand.group_id)
    if customer:
        await customer.update(
            marketing_consent=data.marketing_consent, updated_date=utcnow()
        )

    return schemas.OkResponse()
