from fastapi import APIRouter, Depends

import schemas
from core.auth.services.external_login import ExternalLoginService

router = APIRouter(
    prefix="/externalLogin",
    tags=["external-login"]
)


@router.post("/create")
async def create_external_login(
        data: schemas.CreateExternalLoginData,
        service: ExternalLoginService = Depends(),
) -> schemas.ExternalLoginSchema:
    return await service.create(data)


@router.get("/{uuid}")
async def get_external_login(
        uuid: str,
        service: ExternalLoginService = Depends(),
) -> schemas.ExternalLoginSchema:
    return await service.get_external_login(uuid)


@router.post("/{uuid}/cancel")
async def cancel_external_login(
        uuid: str,
        service: ExternalLoginService = Depends(),
) -> schemas.ExternalLoginSchema:
    await service.load_request(uuid)
    return await service.set_external_login_status(
        schemas.ExternalLoginRequestStatusEnum.CANCELED
    )
