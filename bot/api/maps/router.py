from fastapi import APIRouter, Body, Depends

import schemas
from core.google_maps.services import MapsService

router = APIRouter()


@router.get(
    "/autocomplete",
    description="Method to get autocomplete data for search query in google places",
    response_model=schemas.PredictionsResponse,
)
async def maps_autocomplete(
        service: MapsService = Depends(),
        data: schemas.AutocompleteParamsSchema = Depends(),
):
    return await service.autocomplete(data)


@router.get(
    "/details",
    description="Method to get details for place by place_id",
    response_model=schemas.PlaceDetails,
)
async def get_maps_place_details(
        service: MapsService = Depends(),
        data: schemas.PlaceDetailsParamsSchema = Depends(),
):
    return await service.place_details(data)


@router.get(
    "/reverse_geocoding",
    description="Method to get addresses by coordinates",
    response_model=schemas.GeocodingResultSchema,
)
async def maps_reverse_geocoding(
        service: MapsService = Depends(),
        data: schemas.ReverseGeocodingParamsSchema = Depends(),
):
    return await service.reverse_geocoding(data)


@router.get(
    "/geocoding",
    description="Method to get coordinates by addresses",
    response_model=schemas.GeocodingResultSchema,
)
async def maps_geocoding(
        service: MapsService = Depends(),
        data: schemas.GeocodingParamsSchema = Depends(),
):
    return await service.geocoding(data)


@router.get(
    "/maps_key",
    description="Method to get api key for Google Maps Javascript API",
    response_model=schemas.MapsKeySchema,
)
async def get_maps_key(
        service: MapsService = Depends(),
):
    return await service.get_maps_key()


@router.post(
    "/validate_address",
    description="Method to validate address by google address_components",
    response_model=schemas.AddressValidSchema,
)
async def validate_address(
        service: MapsService = Depends(),
        data: list[schemas.AddressComponent] = Body(...),
):
    return await service.validate_address(data)
