from typing import Annotated

from fastapi import Depends

import exceptions
import schemas
from core.auth.depend import get_active_user
from db import crud
from db.models import Ad, User
from .deps import get_ad_by_id


class AdUnitsService:
    def __init__(
            self,
            ad: Annotated[Ad, Depends(get_ad_by_id)],
            user: Annotated[User, Depends(get_active_user)],
    ):
        self.ad = ad
        self.user = user

    async def get_next(self):
        next_unit = await crud.get_next_ad_for_user(self.ad.id, self.user.id)
        if not next_unit:
            raise exceptions.NextAdUnitNotFoundError()

        return schemas.AdUnitSchema(
            **next_unit.unit.as_dict(),
            horizontal_video_url=next_unit.get_video_url("horizontal"),
            vertical_video_url=next_unit.get_video_url("vertical"),
        )

    async def set_unit_as_shown(self, ad_unit_id: int):
        await crud.set_ad_unit_as_shown(
            self.ad.id,
            ad_unit_id,
            self.user.id,
        )
        return schemas.AdUnitSetAsShownResult(ad_unit_id=ad_unit_id)
