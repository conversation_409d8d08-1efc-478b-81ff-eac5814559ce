from fastapi import APIRouter, Depends

import schemas
from .service import AdUnitsService

router = APIRouter(
    prefix="/{ad_id}/ad-units",
    tags=["ad"]
)


@router.get("/next")
async def get_next_ad(
        service: AdUnitsService = Depends()
) -> schemas.AdUnitSchema:
    return await service.get_next()


@router.post("/{ad_unit_id}/shown")
async def set_ad_unit_as_shown(
        ad_unit_id: int,
        service: AdUnitsService = Depends(),
) -> schemas.AdUnitSetAsShownResult:
    return await service.set_unit_as_shown(ad_unit_id)
