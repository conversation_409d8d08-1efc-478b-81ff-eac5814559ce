import sys
import traceback
from functools import wraps
from typing import Awaitable, Callable

from starlette.requests import Request
from starlette.responses import Response


def api_error_handler(func) -> Callable[[Request, Exception], Awaitable[Response]]:
    @wraps(func)
    async def wrapper(request: Request, error: Exception):
        request.state.exception_info = {
            "message": f"{error.__class__.__name__}: {str(error)}",
            "info": "".join(traceback.format_exception(*sys.exc_info())),
        }
        return await func(request, error)

    return wrapper
