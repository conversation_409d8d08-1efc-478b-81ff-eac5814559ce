import http

from aiowhatsapp.api.exceptions import WhatsappApiError
from fastapi import FastAPI, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.utils import is_body_allowed_for_status_code
from psutils.exceptions import ErrorWithTextVariable
from starlette import status
from starlette.requests import Request
from starlette.responses import JSONResponse, Response

from api.decorators import api_error_handler
from config import DEFAULT_LANG, LOG_ERRORS_IN_API_EXCEPTION_HANDLERS
from loggers import <PERSON><PERSON><PERSON>ogger
from utils.accept_language import parse_accept_language
from utils.exceptions import ErrorWithHTTPStatus
from utils.text import f

logger = JSONLogger("api")


@api_error_handler
async def error_with_text_variable_handler(
        request: Request, error: ErrorWithTextVariable
):

    lang = parse_accept_language(request.headers.get("Accept-Language", DEFAULT_LANG))

    if isinstance(error, ErrorWithHTTPStatus):
        status_code = error.status_code
        detail_data = error.detail_data
    else:
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        detail_data = None

    try:
        detail_text = await f(error.text_variable, lang, **error.text_kwargs)
    except Exception as e:
        logger.error(
            "An error occurred while retrieving detail_text for ErrorWithTextVariable",
            e
        )
        detail_text = str(error)

    if LOG_ERRORS_IN_API_EXCEPTION_HANDLERS:
        logger.error(
            error, {
                "detail_text": detail_text,
            }
        )

    headers = getattr(error, "headers", None)
    if not is_body_allowed_for_status_code(status_code):
        return Response(status_code=status_code, headers=headers)

    content = {
        "detail": detail_text or ""
    }
    if detail_data:
        content["detail_data"] = detail_data

    return JSONResponse(
        content, status_code=status_code, headers=headers
    )


@api_error_handler
async def http_exception_handler(_: Request, error: HTTPException):
    if LOG_ERRORS_IN_API_EXCEPTION_HANDLERS:
        logger.error(error)

    headers = getattr(error, "headers", None)
    if not is_body_allowed_for_status_code(error.status_code):
        return Response(status_code=error.status_code, headers=headers)

    return JSONResponse(
        {"detail": error.detail}, status_code=error.status_code, headers=headers
    )


@api_error_handler
async def request_validation_error(_: Request, error: RequestValidationError):
    if LOG_ERRORS_IN_API_EXCEPTION_HANDLERS:
        logger.error(error)

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": repr(error)}
    )


@api_error_handler
async def unhandled_exception_handler(_: Request, error: Exception):
    if LOG_ERRORS_IN_API_EXCEPTION_HANDLERS:
        logger.error(error)

    detail = http.HTTPStatus(500).phrase
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": detail},
    )


@api_error_handler
async def whatsapp_exception_handler(request: Request, error: WhatsappApiError):
    lang = parse_accept_language(request.headers.get("Accept-Language", DEFAULT_LANG))
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

    if getattr(error, "error_data", None):
        error.text_kwargs["message"] = error.error_data.get("message", None)

    try:
        detail_text = await f(error.text_variable, lang, **error.text_kwargs)
    except Exception as e:
        logger.error(
            "An error occurred while retrieving detail_text for ErrorWithTextVariable",
            e
        )
        detail_text = str(error)

    if LOG_ERRORS_IN_API_EXCEPTION_HANDLERS:
        if status_code >= 500:
            logger.error(
                error, {
                    "detail_text": detail_text,
                }
            )

    headers = getattr(error, "headers", None)

    content = {
        "detail": detail_text or ""
    }

    return JSONResponse(
        content, status_code=status_code, headers=headers
    )


def register_general_exception_handlers(app: FastAPI):
    app.add_exception_handler(WhatsappApiError, whatsapp_exception_handler)
    app.add_exception_handler(ErrorWithTextVariable, error_with_text_variable_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, request_validation_error)
    app.add_exception_handler(Exception, unhandled_exception_handler)
