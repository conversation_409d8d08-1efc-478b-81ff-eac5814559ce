from db.models import Brand, Group, Invoice, InvoiceItem
from db.query_builder.query_builder import DBQueryBuilder
from schemas import (
    ExtraFilterData, FilterType, QueryBuilderObjConf, QueryBuilderSettings,
    ReportsTransactionRowsSchema,
    SortData,
)

transactions_query = DBQueryBuilder(
    QueryBuilderSettings(
        objects={
            'invoice': QueryBuilderObjConf(
                model=Invoice,
                select_from=True,
                default_obj=True,
                fields=[
                    "id",
                    "status",
                    'currency',
                    "title",
                    "description",
                    "photo",
                    "first_name",
                    "last_name",
                    "email",
                    "phone",
                    "incust_check",
                    "shipment_cost",
                    "custom_payment_cost",
                    "before_loyalty_sum",
                    "discount",
                    "bonuses_redeemed",
                    "discount_and_bonuses_redeemed",
                    "total_sum",
                    "tips_sum",
                    "sum_to_pay",
                    "payer_fee",
                    "paid_sum",
                    "external_transaction_id",
                    "client_redirect_url",
                    "webhook_result",
                    "incust_vouchers",
                    "user_id",
                    "payer_id",
                    "is_friend",
                    "user_comment_label",
                    "user_comment",
                    "payed_in_bot_id",
                    "time_created",
                    "invoice_type",
                    "extraFee",
                    "total_sum_with_extra_fee",
                    "extra_params",
                    "utm_labels",
                ],
                default_fields={
                    "id",
                    "status",
                    "title",
                    "description",
                },
            ),
            'invoice_item': QueryBuilderObjConf(
                model=InvoiceItem,

                fields=[
                    "id",
                    "name",
                    "quantity",
                    "item_code",
                    "price",
                    "unit_discount",
                    "unit_bonuses_redeemed",
                    "unit_discount_and_bonuses_redeemed",
                    "final_price",
                    "before_loyalty_sum",
                    "discount",
                    "bonuses_redeemed",
                    "discount_and_bonuses_redeemed",
                    "final_sum",
                ],
                default_fields={
                    "id",
                    "name",
                    "quantity",
                    "price"
                },
                join_expr=Invoice.id == InvoiceItem.invoice_id,
                depends_on=("invoice",),
            ),
            "group": QueryBuilderObjConf(
                model=Group,
                fields=[
                    "id",
                    "name",
                    "status",
                    "lang",
                ],
                default_fields={
                    "id",
                    "lang",
                },
                join_expr=Invoice.group_id == Group.id,
                depends_on=("invoice",),
            ),
            "brand": QueryBuilderObjConf(
                model=Brand,
                fields=[
                    "id",
                    "name",
                    "domain",
                ],
                default_fields={
                    "id",
                },
                join_expr=Group.id == Brand.group_id,
                depends_on=("group",),
            ),
        },
        extra_filters=[
            ExtraFilterData(
                type=FilterType.ONE_OF,
                field="group.id",
                name="profile_id",
            ),
            ExtraFilterData(
                type=FilterType.EQUAL,
                field="group.status",
                name="profile_status",
            )
        ],
        default_sort=[
            SortData(field="invoice.id", desc=True),
        ],
        row_data_model=ReportsTransactionRowsSchema
    )
)
