from typing import Annotated

from fastapi import APIRouter, Query

import schemas
from .query import transactions_query

router = APIRouter(
    prefix="/transactions",
    tags=["transactions"]
)


@router.get("/", response_model_exclude_unset=True)
async def get_transactions_query_reports(
        params: Annotated[schemas.ReportsParams, Query()],
) -> schemas.ListCursorResponse[schemas.ReportsTransactionRowsSchema]:
    return await transactions_query.make_list_response(
        params,
        profile_id=params.profile_ids, status="enabled"
    )


@router.get("/fields")
async def get_transactions_query_fields() -> dict[str, list[str]]:
    return transactions_query.settings.fields
