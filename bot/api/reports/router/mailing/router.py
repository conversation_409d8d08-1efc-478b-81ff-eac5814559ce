from typing import Annotated

from fastapi import APIRouter, Query

import schemas
from api.reports.router.mailing.query import mailing_query

router = APIRouter(
    prefix="/mailing",
    tags=["mailing"]
)


@router.get("/", response_model_exclude_unset=True)
async def get_mailing_reports(
        params: Annotated[schemas.ReportsParams, Query()],
) -> schemas.ListCursorResponse[schemas.ReportsMailingRowsSchema]:
    return await mailing_query.make_list_response(
        params,
        profile_id=params.profile_ids, status="enabled"
    )


@router.get("/fields")
async def get_mailing_fields() -> dict[str, list[str]]:
    return mailing_query.settings.fields
