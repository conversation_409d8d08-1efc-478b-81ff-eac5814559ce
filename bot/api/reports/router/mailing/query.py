from db.models import Brand, Group, Mailing, MailingMessage
from db.query_builder.query_builder import DBQueryBuilder
from schemas import (
    ExtraFilterData, FilterType, QueryBuilderObjConf, QueryBuilderSettings,
    ReportsMailingRowsSchema, SortData,
)

mailing_query = DBQueryBuilder(
    QueryBuilderSettings(
        objects={
            'mailing_message': QueryBuilderObjConf(
                model=MailingMessage,
                fields=[
                    "id",
                    "email",
                    'phone',
                    'chat_id',
                    'user_name',
                    'message',
                    'channel_type',
                    "channel_name",
                    'status',
                    'lang',
                    'mailing_id',
                    'bot_id',
                    'user_id',
                    "created_datetime",
                    "start_datetime",
                    "end_datetime",
                    "error_details",
                    "retry_info",
                ],
                default_fields={
                    "id",
                    "email",
                    "phone",
                    "user_name",
                    "message",
                },
                join_expr=Mailing.id == MailingMessage.mailing_id,
                depends_on=("mailing",),
            ),
            'mailing': QueryBuilderObjConf(
                model=Mailing,
                select_from=True,
                default_obj=True,
                fields=["id",
                        "status",
                        "channels",
                        "last_sent_datetime",
                        "description",
                        "name",
                        "sent_info",
                        "message_info",
                        "is_test",
                        "message_source_text",
                        "email_media_as_attachment",
                        ],
                default_fields={
                    "id",
                    "status",
                    "name",
                },
                # join_expr=Group.id == Mailing.id,
                # depends_on=("group",),
            ),
            "group": QueryBuilderObjConf(
                model=Group,
                fields=[
                    "id",
                    "name",
                    "status",
                    "lang",
                ],
                default_fields={
                    "id",
                    "lang",
                },
                join_expr=Mailing.group_id == Group.id,
                depends_on=("mailing",),
            ),
            "brand": QueryBuilderObjConf(
                model=Brand,
                fields=[
                    "id",
                    "name",
                    "domain",
                ],
                default_fields={
                    "id",
                },
                join_expr=Group.id == Brand.group_id,
                depends_on=("group",),
            ),
        },
        extra_filters=[
            ExtraFilterData(
                type=FilterType.ONE_OF,
                field="group.id",
                name="profile_id",
            ),
            ExtraFilterData(
                type=FilterType.EQUAL,
                field="group.status",
                name="profile_status",
            )
        ],
        default_sort=[
            SortData(field="mailing.id", desc=True),
        ],
        row_data_model=ReportsMailingRowsSchema
    )
)
