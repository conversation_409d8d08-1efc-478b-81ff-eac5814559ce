from typing import Annotated, Literal

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse

import schemas
from . import mailing, orders, tickets, transactions
from .helper import export_journal_in_csv, export_journal_in_excel
from .mailing.query import mailing_query
from .orders.query import orders_positions_query, orders_query
from .tickets.query import tickets_query
from .transactions.query import transactions_query

router = APIRouter()

router.include_router(orders.router)
router.include_router(mailing.router)
router.include_router(transactions.router)
router.include_router(tickets.router)

_QUERY_MAP = {
    "orders_journal": orders_query,
    "orders_by_positions": orders_positions_query,
    "mailing": mailing_query,
    "transactions": transactions_query,
    "tickets": tickets_query,
}


async def _fetch_rows(params: schemas.ExportReportsParams):
    """Response for export_journal_in_csv and export_journal_in_excel"""
    params.limit = 1000000
    query = _QUERY_MAP.get(params.reportType)
    if not query:
        raise HTTPException(400, detail="Invalid reportType")
    data, _ = await query.make_list_response(
        params,
        profile_id=params.profile_ids,
        status="enabled",
    )
    _, rows = data
    return rows


def _build_streaming_response(
        *,
        exporter,
        rows,
        fields: list[str],
        report_type: str,
) -> StreamingResponse:
    """
    exporter waiting for List[dict], or List[Pydantic] —
    export_journal_in_csv or export_journal_in_excel
    """
    output = exporter(rows, fields)
    content_type = (
        "text/csv; charset=utf-8"
        if exporter is export_journal_in_csv
        else "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
    ext = "csv" if exporter is export_journal_in_csv else "xlsx"
    return StreamingResponse(
        output,
        media_type=content_type,
        headers={
            "Content-Disposition": f'attachment; filename="{report_type}_journal.{ext}"'
        },
    )


@router.get("/export/{fmt}")
async def export_journal(
        fmt: Literal["csv", "excel"],
        params: Annotated[schemas.ExportReportsParams, Query()],
) -> StreamingResponse:
    rows = await _fetch_rows(params)

    if fmt == "csv":
        dicts = [row.dict() for row in rows]

        return _build_streaming_response(
            exporter=export_journal_in_csv,
            rows=dicts,
            fields=params.fields,
            report_type=params.reportType,
        )
    else:
        return _build_streaming_response(
            exporter=export_journal_in_excel,
            rows=rows,
            fields=params.fields,
            report_type=params.reportType,
        )
