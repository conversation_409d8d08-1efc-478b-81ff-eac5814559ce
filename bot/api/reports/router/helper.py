import csv
import io
from io import Bytes<PERSON>
from typing import Dict, Iterator, List

from openpyxl import Workbook
from openpyxl.styles import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, PatternFill


def _get_nested_value(obj: dict, path: str):
    """
    Get a nested value from a dictionary using a dot-separated path.
    For example, given the dictionary {'a': {'b': {'c': 1}}} and the path 'a.b.c',
    """
    val = obj
    for key in path.split("."):
        if not isinstance(val, dict):
            return None
        val = val.get(key)
        if val is None:
            return None

    # if val is a list, convert it to a string
    if isinstance(val, (str, int, float, bool)) or val is None:
        return val
    return str(val)


def _prettify_header(field_path: str) -> str:
    # transform "order.id" to "Order Id"
    parts = field_path.split(".")
    return " - ".join(part.replace("_", " ").title() for part in parts)


def export_journal_in_excel(
        data: any, headers: list[str]
) -> BytesIO:
    """
    Export journal data to excel file
    """
    dicts = [row.dict() for row in data]

    output = io.BytesIO()
    wb = Workbook()
    ws = wb.active

    pretty_headers = [_prettify_header(f) for f in headers]
    ws.append(pretty_headers)

    # set column widths for headers
    ws.row_dimensions[1].height = 21

    header_alignment = Alignment(horizontal="center", vertical="center")

    # set column widths for headers
    header_fill = PatternFill(
        start_color="FFBDD7EE",
        end_color="FFBDD7EE",
        fill_type="solid",
    )
    header_font = Font(bold=True, color="FF000000")
    for cell in ws[1]:  # this is the first row
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = header_alignment

    for d in dicts:
        ws.append(
            [
                _get_nested_value(d, field)
                for field in headers
            ]
        )

    wb.save(output)
    output.seek(0)
    return output


def export_journal_in_csv(dicts: List[Dict], fields: List[str]) -> Iterator[str]:
    """
    Export journal data to csv file

    """
    yield b"\xef\xbb\xbf"
    buffer = io.StringIO()
    writer = csv.writer(buffer, delimiter=";", lineterminator="\r\n")

    # шапка
    headers = [_prettify_header(f) for f in fields]
    writer.writerow(headers)
    chunk = buffer.getvalue().encode("utf-8")
    buffer.seek(0);
    buffer.truncate(0)
    yield chunk

    # рядки даних
    for d in dicts:
        row = [_get_nested_value(d, f) for f in fields]
        writer.writerow(row)
        chunk = buffer.getvalue().encode("utf-8")
        buffer.seek(0);
        buffer.truncate(0)
        yield chunk
