from typing import Annotated

from fastapi import APIRouter, Query

import schemas
from .query import tickets_query

router = APIRouter(
    prefix="/tickets",
    tags=["tickets"]
)


@router.get("/", response_model_exclude_unset=True)
async def get_tickets_reports(
        params: Annotated[schemas.ReportsParams, Query()],
) -> schemas.ListCursorResponse[schemas.ReportsTicketRowsSchema]:
    return await tickets_query.make_list_response(
        params,
        profile_id=params.profile_ids, status="enabled"
    )


@router.get("/fields")
async def get_tickets_fields() -> dict[str, list[str]]:
    return tickets_query.settings.fields
