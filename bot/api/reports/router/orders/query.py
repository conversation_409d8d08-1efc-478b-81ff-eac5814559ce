from sqlalchemy.engine import Row

from db.models import (
    Brand, BrandCustomSettings, Group, MenuInStore, OrderProduct, OrderShipment,
    PaymentSettings, Store,
    StoreOrder,
    StoreOrderPayment, StoreProduct, User,
)
from db.query_builder.query_builder import DBQueryBuilder
from schemas import (
    ExtraFilterData, FilterType, QueryBuilderObjConf,
    QueryBuilderSettings, ReportsOrderPositionsRowSchema, ReportsOrderRowSchema,
    SortData,
)
from utils.text import f


async def get_shipment_name(field_value: str, row: Row):
    if field_value:
        return field_value

    base_type = row["delivery_method.base_type"]
    group_lang = row["group.lang"]
    return await f(
        f"store {base_type.replace('_', ' ')} text",
        group_lang,
    )


orders_query = DBQueryBuilder(
    QueryBuilderSettings(
        objects={
            "order": QueryBuilderObjConf(
                model=StoreOrder,
                select_from=True,
                fields=[
                    "id",
                    "type",
                    "currency",
                    "create_date",
                    "status",
                    "status_pay",
                    "first_name",
                    "last_name",
                    "phone",
                    "email",
                    "delivery_address",
                    "address_coordinates",
                    "address_place_id",
                    "map_link",
                    "address_comment",
                    "desired_delivery_date",
                    "comment",
                    "before_loyalty_sum",
                    "total_sum",
                    "tips_sum",
                    "bonuses_redeemed",
                    "discount",
                    "discount_and_bonuses",
                    "total_sum_with_extra_fee",
                    "sum_to_pay",
                    "payer_fee",
                    "paid_sum",
                    "date_sent_to_friend",
                    "utm_labels"
                ],
                amount_fields={
                    "before_loyalty_sum",
                    "total_sum",
                    "tips_sum",
                    "bonuses_redeemed",
                    "discount",
                    "discount_and_bonuses",
                    "total_sum_with_extra_fee",
                    "sum_to_pay",
                    "payer_fee",
                    "paid_sum",
                },
                default_fields={
                    "id",
                    "type",
                    "currency",
                },
            ),
            "store": QueryBuilderObjConf(
                model=Store,
                fields=[
                    "id",
                    "name",
                    "description",
                ],
                default_fields={
                    "id",
                },
                join_expr=StoreOrder.store_id == Store.id,
            ),
            "brand": QueryBuilderObjConf(
                model=Brand,
                fields=[
                    "id",
                    "name",
                    "domain",
                ],
                default_fields={
                    "id",
                },
                join_expr=Store.brand_id == Brand.id,
                depends_on=("store",),
            ),
            "group": QueryBuilderObjConf(
                model=Group,
                fields=[
                    "id",
                    "name",
                    "status",
                    "lang",
                ],
                default_fields={
                    "id",
                    "lang",
                },
                join_expr=Brand.group_id == Group.id,
                default_obj=True,
                depends_on=("brand",),
            ),
            "user": QueryBuilderObjConf(
                model=User,
                fields=[
                    "id",
                    "uuid_token",
                    "incust_external_id",
                    "date_joined",
                    "birth_date",
                    "name",
                    "first_name",
                    "last_name",
                    "full_name",
                    "is_guest_user",
                    "email",
                    "chat_id",
                    "username",
                    "wa_phone",
                    "wa_name",
                    "photo_url",
                ],
                default_fields={
                    "id",
                },
                join_expr=StoreOrder.user_id == User.id,
            ),
            "menu_in_store": QueryBuilderObjConf(
                model=MenuInStore,
                fields=[
                    "id",
                    "comment",
                ],
                default_fields={
                    "id",
                },
                join_expr=StoreOrder.menu_in_store_id == MenuInStore.id,
                outerjoin=True,
            ),
            "shipment": QueryBuilderObjConf(
                model=OrderShipment,
                fields=[
                    "price",
                    "is_paid_separately",
                ],
                amount_fields={
                    "price",
                },
                join_expr=OrderShipment.store_order_id == StoreOrder.id,
            ),
            "delivery_method": QueryBuilderObjConf(
                model=BrandCustomSettings,
                fields=[
                    "id",
                    "name",
                    "description",
                    "base_type",
                ],
                default_fields={
                    "id",
                    "base_type",
                },
                fields_processors={
                    "name": get_shipment_name,
                },
                join_expr=OrderShipment.settings_id == BrandCustomSettings.id,
                outerjoin=True,
                depends_on=("shipment",),
            ),
            "payment": QueryBuilderObjConf(
                model=StoreOrderPayment,
                fields=[
                    "comment",
                    "price",
                ],
                amount_fields={
                    "price"
                },
                join_expr=StoreOrderPayment.order_id == StoreOrder.id,
            ),
            "payment_method": QueryBuilderObjConf(
                model=PaymentSettings,
                fields=[
                    "id",
                    "name",
                    "description",
                    "label_comment",
                    "post_payment_info",
                ],
                default_fields={
                    "id",
                },
                join_expr=StoreOrderPayment.payment_settings_id == PaymentSettings.id,
                depends_on=("payment",),
            )
        },
        extra_filters=[
            ExtraFilterData(
                type=FilterType.ONE_OF,
                field="group.id",
                name="profile_id",
            ),
            ExtraFilterData(
                type=FilterType.EQUAL,
                field="group.status",
                name="profile_status",
            )
        ],
        default_sort=[
            SortData(field="order.id", desc=True),
        ],
        row_data_model=ReportsOrderRowSchema,
    ),
)

orders_positions_query = DBQueryBuilder(
    QueryBuilderSettings(
        objects={
            "order": QueryBuilderObjConf(
                model=StoreOrder,
                select_from=True,
                fields=[
                    "id",
                    "type",
                    "currency",
                    "create_date",
                    "status",
                    "status_pay",
                    "first_name",
                    "last_name",
                    "phone",
                    "email",
                    "delivery_address",
                    "address_coordinates",
                    "address_place_id",
                    "map_link",
                    "address_comment",
                    "desired_delivery_date",
                    "comment",
                    "before_loyalty_sum",
                    "total_sum",
                    "tips_sum",
                    "bonuses_redeemed",
                    "discount",
                    "discount_and_bonuses",
                    "total_sum_with_extra_fee",
                    "sum_to_pay",
                    "payer_fee",
                    "paid_sum",
                    "date_sent_to_friend",
                    "utm_labels"
                ],
                amount_fields={
                    "before_loyalty_sum",
                    "total_sum",
                    "tips_sum",
                    "bonuses_redeemed",
                    "discount",
                    "discount_and_bonuses",
                    "total_sum_with_extra_fee",
                    "sum_to_pay",
                    "payer_fee",
                    "paid_sum",
                },
                default_fields={
                    "id",
                    "type",
                    "currency",
                },
            ),
            "store": QueryBuilderObjConf(
                model=Store,
                fields=[
                    "id",
                    "name",
                    "description",
                ],
                default_fields={
                    "id",
                },
                join_expr=StoreOrder.store_id == Store.id,
            ),
            "brand": QueryBuilderObjConf(
                model=Brand,
                fields=[
                    "id",
                    "name",
                    "domain",
                ],
                default_fields={
                    "id",
                },
                join_expr=Store.brand_id == Brand.id,
                depends_on=("store",),
            ),
            "group": QueryBuilderObjConf(
                model=Group,
                fields=[
                    "id",
                    "name",
                    "status",
                    "lang",
                ],
                default_fields={
                    "id",
                    "lang",
                },
                join_expr=Brand.group_id == Group.id,
                default_obj=True,
                depends_on=("brand",),
            ),
            "order_product": QueryBuilderObjConf(
                model=OrderProduct,
                fields=[
                    "id",
                    "quantity",
                    "price",
                    "price_with_attributes",
                    "final_price",
                    "discount_amount",
                    "discount_sum",
                    "bonuses_redeemed",
                    "bonuses_redeemed_sum",
                    "discount_and_bonuses",
                    "discount_and_bonuses_sum",
                    "price_after_loyalty",
                    "total_sum",
                    "incust_account",
                    "incust_card",
                    "topup_charge",
                    "charge_percent",
                    "charge_fixed",
                    "is_topup_error",
                ],
                amount_fields={
                    "price",
                    "price_with_attributes",
                    "final_price",
                    "discount_amount",
                    "discount_sum",
                    "bonuses_redeemed",
                    "bonuses_redeemed_sum",
                    "discount_and_bonuses",
                    "discount_and_bonuses_sum",
                    "price_after_loyalty",
                    "total_sum",
                    "topup_charge",
                },
                default_fields={
                    "id",
                },
                join_expr=OrderProduct.store_order_id == StoreOrder.id,
            ),
            "product": QueryBuilderObjConf(
                model=StoreProduct,
                fields=[
                    "id",
                    "product_id",
                    "name",
                    "description",
                    "type",
                ],
                default_fields={
                    "id",
                },
                join_expr=OrderProduct.product_id == StoreProduct.id,
                depends_on=("order_product",),
            ),
            "user": QueryBuilderObjConf(
                model=User,
                fields=[
                    "id",
                    "uuid_token",
                    "incust_external_id",
                    "date_joined",
                    "birth_date",
                    "name",
                    "first_name",
                    "last_name",
                    "full_name",
                    "is_guest_user",
                    "email",
                    "chat_id",
                    "username",
                    "wa_phone",
                    "wa_name",
                    "photo_url",
                ],
                default_fields={
                    "id",
                },
                join_expr=StoreOrder.user_id == User.id,
            ),
            "menu_in_store": QueryBuilderObjConf(
                model=MenuInStore,
                fields=[
                    "id",
                    "comment",
                ],
                default_fields={
                    "id",
                },
                join_expr=StoreOrder.menu_in_store_id == MenuInStore.id,
                outerjoin=True,
            ),
            "shipment": QueryBuilderObjConf(
                model=OrderShipment,
                fields=[
                    "price",
                    "is_paid_separately",
                ],
                amount_fields={
                    "price",
                },
                join_expr=OrderShipment.store_order_id == StoreOrder.id,
            ),
            "delivery_method": QueryBuilderObjConf(
                model=BrandCustomSettings,
                fields=[
                    "id",
                    "name",
                    "description",
                    "base_type",
                ],
                default_fields={
                    "id",
                    "base_type",
                },
                fields_processors={
                    "name": get_shipment_name,
                },
                join_expr=OrderShipment.settings_id == BrandCustomSettings.id,
                outerjoin=True,
                depends_on=("shipment",),
            ),
            "payment": QueryBuilderObjConf(
                model=StoreOrderPayment,
                fields=[
                    "comment",
                    "price",
                ],
                amount_fields={
                    "price"
                },
                join_expr=StoreOrderPayment.order_id == StoreOrder.id,
            ),
            "payment_method": QueryBuilderObjConf(
                model=PaymentSettings,
                fields=[
                    "id",
                    "name",
                    "description",
                    "label_comment",
                    "post_payment_info",
                ],
                default_fields={
                    "id",
                },
                join_expr=StoreOrderPayment.payment_settings_id == PaymentSettings.id,
                depends_on=("payment",),
            )
        },
        extra_filters=[
            ExtraFilterData(
                type=FilterType.ONE_OF,
                field="group.id",
                name="profile_id",
            ),
            ExtraFilterData(
                type=FilterType.EQUAL,
                field="group.status",
                name="profile_status",
            )
        ],
        default_sort=[
            SortData(field="order.id", desc=True),
            SortData(field="order_product.id", if_joined=True),
        ],
        row_data_model=ReportsOrderPositionsRowSchema,
    ),
)
