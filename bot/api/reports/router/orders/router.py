from typing import Annotated

from fastapi import APIRouter, Query
from fastapi.responses import StreamingResponse

import schemas
from api.reports.router.orders.query import orders_positions_query, orders_query
from config import config
from ..helper import export_journal_in_csv

router = APIRouter(
    prefix="/orders",
    tags=["orders"]
)


@router.get("/", response_model_exclude_unset=True)
async def get_orders(
        params: Annotated[schemas.ReportsParams, Query()],
) -> schemas.ListCursorResponse[schemas.ReportsOrderRowSchema]:
    return await orders_query.make_list_response(
        params,
        profile_id=params.profile_ids,
        status="enabled"
    )


@router.get("/orders_positions", response_model_exclude_unset=True)
async def get_orders_positions(
        params: Annotated[schemas.ReportsParams, Query()],
) -> schemas.ListCursorResponse[schemas.ReportsOrderPositionsRowSchema]:
    return await orders_positions_query.make_list_response(
        params,
        profile_id=params.profile_ids,
        status="enabled"
    )


@router.get('/export/csv')
async def export_orders_to_csv(
        params: Annotated[schemas.ReportsParams, Query()]
) -> StreamingResponse:
    params.limit = config.REPORTS_LIMIT_FOR_EXPORT

    data, _ = await orders_query.make_list_response(
        params,
        profile_id=params.profile_ids,
        status="enabled",
    )
    _, rows = data

    dicts = [row.dict() for row in rows]

    return StreamingResponse(
        export_journal_in_csv(dicts, params.fields),
        media_type="text/csv; charset=utf-8",
        headers={"Content-Disposition": 'attachment; filename="orders_journal.csv"'},
    )


@router.get('/export/csv/orders_positions')
async def export_orders_positions_to_csv(
        params: Annotated[schemas.ReportsParams, Query()]
) -> StreamingResponse:
    params.limit = config.REPORTS_LIMIT_FOR_EXPORT

    data, _ = await orders_positions_query.make_list_response(
        params,
        profile_id=params.profile_ids,
        status="enabled",
    )
    _, rows = data

    dicts = [row.dict() for row in rows]

    return StreamingResponse(
        export_journal_in_csv(dicts, params.fields),
        media_type="text/csv; charset=utf-8",
        headers={
            "Content-Disposition": 'attachment; filename="orders_positions_journal.csv"'
        },
    )


@router.get("/fields")
async def get_orders_fields() -> dict[str, list[str]]:
    return orders_query.settings.fields


@router.get("/fields/orders_positions")
async def get_orders_positions_fields() -> dict[str, list[str]]:
    return orders_positions_query.settings.fields
