from fastapi import APIRouter, Depends, Query

from core.external_data import ExternDataService, schemas, types

from ..dependencies import get_filters


router = APIRouter(
    prefix="/orders",
    tags=["orders"],
)


@router.get(
    "/",
    description="Method for reading orders",
    responses={
        200: {"model": schemas.OrderResponseSchema},
    },
)
async def get_orders(
        filters: schemas.ExternDataFilters = Depends(get_filters),
        statuses: list[schemas.ShipmentStatusLiteral] | None = Query(
            None, description="list statuses for filter load data"
        ),
        service: ExternDataService = Depends(ExternDataService.depend)
):
    return await service.read_data(types.ExternDataType.ORDER.value, filters, statuses)
