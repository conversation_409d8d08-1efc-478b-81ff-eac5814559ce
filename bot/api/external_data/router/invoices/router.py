from fastapi import APIRouter, Depends, Query

from core.external_data import ExternDataService, schemas, types

from ..dependencies import get_filters


router = APIRouter(
    prefix="/invoices",
    tags=["invoices"]
)


@router.get(
    "/",
    description="Method for reading invoices",
    responses={
        200: {"model": schemas.InvoiceResponseSchema},
    },
)
async def get_invoices(
        filters: schemas.ExternDataFilters = Depends(get_filters),
        statuses: list[schemas.InvoiceStatusLiteral] | None = Query(
            None, description="list statuses for filter load data"
        ),
        service: ExternDataService = Depends(ExternDataService.depend)
):
    return await service.read_data(types.ExternDataType.INVOICE.value, filters, statuses)
