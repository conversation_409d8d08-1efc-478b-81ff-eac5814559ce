from fastapi import APIRouter, Depends

from core.external_data import ExternDataService, schemas

from . import invoices
from . import orders


router = APIRouter()


router.include_router(invoices.router)
router.include_router(orders.router)


@router.get(
    "/testAPI",
    description="Method to check if the api is available and if the auth token works",
    responses={
        200: {"model": schemas.TestAPIResult},
    },
    tags=["test API"],
)
async def test_api(service: ExternDataService = Depends(ExternDataService.depend)):
    return service.test_api()
