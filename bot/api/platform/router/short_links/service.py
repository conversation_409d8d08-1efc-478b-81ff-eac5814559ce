import exceptions
import schemas
from api.platform.base import PlatformAdminService
from db import crud
from db.models import ShortLink


class ShortLinksService(PlatformAdminService):
    async def create_short_link(self, data: schemas.CreateShortLinkData):
        link = await crud.create_short_link(
            data.type,
            data.url,
            data.max_uses,
            data.expiration_date,
            data.display_id
        )
        return schemas.ShortLinkSchema.from_orm(link)

    async def get_short_link_by_id(self, link_id: str):
        link = await ShortLink.get(link_id)
        if not link:
            raise exceptions.ShortLinkNotFoundError("id", link)
        return schemas.ShortLinkSchema.from_orm(link)

    async def get_short_link_by_url(self, url: str):
        link = await ShortLink.get(url=url)
        if not link:
            raise exceptions.ShortLinkNotFoundError("url", link)
        return schemas.ShortLinkSchema.from_orm(link)
