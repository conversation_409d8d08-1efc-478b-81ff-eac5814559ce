from fastapi import APIRouter, Depends, Security

import schemas
from api.platform.router.short_links.service import ShortLinksService

router = APIRouter(
    prefix="/short_links",
    tags=["short_links"],
)


@router.post("/")
async def create_short_link(
        data: schemas.CreateShortLinkData,
        service: ShortLinksService = Security(scopes=["me:write"])
) -> schemas.ShortLinkSchema:
    return await service.create_short_link(data)


@router.get("/{link_id}")
async def get_short_link_by_id(
        link_id: str,
        service: ShortLinksService = Depends()
) -> schemas.ShortLinkSchema:
    return await service.get_short_link_by_id(link_id)


@router.get("/{url:path}")
async def get_short_link_by_url(
        url: str,
        service: ShortLinksService = Depends(),
) -> schemas.ShortLinkSchema:
    return await service.get_short_link_by_url(url)
