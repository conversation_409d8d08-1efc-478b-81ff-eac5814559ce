from collections import defaultdict

from psutils.translator.schemas import TranslateObjectData

import schemas
from api.platform.base import PlatformAdminService
from db import crud
from utils.translator import td


# noinspection PyMethodMayBeStatic
class BillingServicePacketService(PlatformAdminService):
    async def get_catalog(self):
        packets_data = await crud.billing.get_catalog_packets_data(
            only_public=False,
        )

        # key is tuple of lang and original lang, value is to translate data
        to_translate: dict[
            tuple[str, str],
            dict[
                str,
                TranslateObjectData
            ]
        ] = defaultdict(dict)

        packets = []

        for packet_data in packets_data:
            packet = packet_data.packet

            # adding to translate data for packets
            for lang in packet.langs_list:
                to_translate[(lang, packet.lang)][f"packet-{packet.id}"] = (
                    TranslateObjectData(
                        object=packet,
                        translation=packet_data.translations.get(lang),
                        result_type="dict",
                    )
                )

            items = []

            for item_data in packet_data.items:
                item = item_data.item

                # adding to translate data for packet items
                for lang in packet.langs_list:
                    to_translate[(lang, packet.lang)][f"item-{item.id}"] = (
                        TranslateObjectData(
                            object=item,
                            translation=item_data.translations.get(lang),
                            result_type="dict",
                        )
                    )

                items.append(
                    schemas.AdminBillingServicePacketItemCatalogSchema(
                        **item.as_dict(),
                        product_code=item_data.product.code,
                        translations={
                            lang: (
                                schemas.AdminBillingServicePacketItemTranslationSchema()
                            )
                            for lang in packet.langs_list
                        }
                    )
                )

            packets.append(
                schemas.AdminBillingServicePacketCatalogSchema(
                    **packet.as_dict(),
                    langs_list=packet.langs_list,
                    items=items,
                    translations={
                        lang: schemas.AdminBillingServicePacketTranslationSchema()
                        for lang in packet.langs_list
                    }
                )
            )

        translated = {}

        for langs, to_translate_lang in to_translate.items():
            lang, original_lang = langs
            translated[lang] = await td(
                to_translate_lang,
                lang, original_lang,
                group_id="internal",
                is_auto_translate_allowed=True,
            )

        for packet in packets:
            for lang in packet.translations.keys():
                translated_packet = translated[lang].get(f"packet-{packet.id}", {})
                for key, value in translated_packet.items():
                    setattr(packet.translations[lang], key, value)

            for item in packet.items:
                for lang in packet.translations.keys():
                    translated_item = translated[lang].get(f"item-{item.id}", {})
                    for key, value in translated_item.items():
                        setattr(item.translations[lang], key, value)

        return schemas.AdminBillingCatalogSchema(packets=packets)

    async def update_catalog(self, data: schemas.UpdateBillingCatalogData):
        await crud.billing.update_catalog(data)
        return await self.get_catalog()
