from fastapi import APIRouter, Depends, Security

import schemas
from .service import BillingServicePacketService

router = APIRouter(
    prefix="/service_packets",
    tags=["billing-service-packets"],
)


@router.get("/catalog")
async def get_platform_billing_catalog(
        service: BillingServicePacketService = Depends()
) -> schemas.AdminBillingCatalogSchema:
    return await service.get_catalog()


@router.put("/catalog")
async def set_platform_billing_catalog(
        data: schemas.UpdateBillingCatalogData,
        service: BillingServicePacketService = Security(scopes=["me:write"]),
) -> schemas.AdminBillingCatalogSchema:
    return await service.update_catalog(data)
