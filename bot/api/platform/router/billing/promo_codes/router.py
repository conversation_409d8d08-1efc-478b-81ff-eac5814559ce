from fastapi import APIRouter, Depends

import schemas
from .service import BillingPromoCodesService

router = APIRouter(
    prefix="/promo_codes",
    tags=["billing-promo-codes"],
)


@router.get("/")
async def get_billing_promo_codes(
        params: schemas.GetBillingPromoCodesParams = Depends(),
        service: BillingPromoCodesService = Depends()
) -> list[schemas.AdminBillingPromoCodeSchema]:
    return await service.get_list(params)


@router.post("/")
async def create_promo_code(
        data: schemas.CreateBillingPromoCodeData,
        service: BillingPromoCodesService = Depends(),
) -> schemas.AdminBillingPromoCodeSchema:
    return await service.create(data)


@router.get("/{promo_code_id}")
async def get_billing_promo_code(
        promo_code_id: int,
        service: BillingPromoCodesService = Depends()
) -> schemas.AdminBillingPromoCodeSchema:
    return await service.get_one(promo_code_id)


@router.patch("/{promo_code_id}")
async def update_billing_promo_code(
        promo_code_id: int,
        data: schemas.UpdateBillingPromoCodeData,
        service: BillingPromoCodesService = Depends(),
) -> schemas.AdminBillingPromoCodeSchema:
    return await service.update(promo_code_id, data)


@router.delete("/{promo_code_id}")
async def delete_billing_promo_code(
        promo_code_id: int,
        service: BillingPromoCodesService = Depends(),
) -> schemas.OkResponse:
    return await service.delete(promo_code_id)


@router.post('/generate_unique_promo_code')
async def generate_promo_code(
        service: BillingPromoCodesService = Depends(),
) -> schemas.AdminBillingUniquePromoCode:
    return await service.generate_unique_promo_code()
