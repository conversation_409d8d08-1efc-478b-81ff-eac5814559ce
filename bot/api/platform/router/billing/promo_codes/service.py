import exceptions
import schemas
from api.platform.base import PlatformAdminService
from core.billing.stripe_client import bstripe
from db import crud
from db.models import BillingPromoCode, BillingServicePacket


# noinspection PyMethodMayBeStatic
class BillingPromoCodesService(PlatformAdminService):
    def promo_code_to_schema(
            self, promo_code: BillingPromoCode,
            packet: BillingServicePacket | None,
    ):
        return schemas.AdminBillingPromoCodeSchema(
            **promo_code.as_dict(by_columns=True),
            packet=packet,
        )

    async def get_list(self, params: schemas.GetBillingPromoCodesParams):
        data = await crud.billing.get_promo_code_list(params)
        return [self.promo_code_to_schema(*row) for row in data]

    async def validate_stripe_coupon(
            self, stripe_coupon_id: str,
            packet: BillingServicePacket | None,
    ):
        stripe_coupon = await bstripe.coupons.retrieve_async(stripe_coupon_id)
        if (
                packet and
                stripe_coupon.amount_off and
                stripe_coupon.currency != packet.currency.lower() and
                (
                        not stripe_coupon.currency_options or
                        packet.currency.lower()
                        not in stripe_coupon.currency_options
                )
        ):
            coupon_currencies = [stripe_coupon.currency]
            if stripe_coupon.currency_options:
                coupon_currencies.extend(stripe_coupon.currency_options.keys())
            raise exceptions.BillingPromoCodeInvalidCurrencyError(
                packet.currency.upper(), [cur.upper() for cur in coupon_currencies]
            )

    async def create(self, data: schemas.CreateBillingPromoCodeData):
        if data.packet_id:
            packet = await BillingServicePacket.get(data.packet_id, is_deleted=False)
            if not packet:
                raise exceptions.BillingServicePacketNotFoundError(data.packet_id)
        else:
            data.packet_id = None
            packet = None

        if data.stripe_coupon:
            await self.validate_stripe_coupon(data.stripe_coupon, packet)
        else:
            data.stripe_coupon = None

        promo_code = await crud.billing.create_promo_code(data, packet)
        return self.promo_code_to_schema(promo_code, packet)

    async def get_promo_code(self, promo_code_id: int):
        promo_code = await BillingPromoCode.get(promo_code_id)
        if not promo_code:
            raise exceptions.BillingPromoCodeNotFoundError(promo_code_id)
        return promo_code

    async def generate_unique_promo_code(self):
        code = crud.billing.generate_new_promo_code()

        if code:
            return schemas.AdminBillingUniquePromoCode(code=code)
        else:
            return exceptions.BillingGenerateUniquePromoCodeError()

    async def get_one(self, promo_code_id: int):
        promo_code = await self.get_promo_code(promo_code_id)
        packet = (
            await BillingServicePacket.get(promo_code.packet_id, is_deleted=False)
            if promo_code.packet_id else None
        )
        return self.promo_code_to_schema(promo_code, packet)

    async def update(
            self, promo_code_id: int, data: schemas.UpdateBillingPromoCodeData
    ):
        promo_code = await self.get_promo_code(promo_code_id)

        data_to_update = data.dict(exclude_unset=True)

        # if the value is empty or None, removing key from data_to_update,
        # so required fields won't be set empty in the database
        for key in ("name", "code"):
            if key in data_to_update and not data_to_update[key]:
                del data_to_update[key]

        if "packet_id" in data_to_update:
            if data.packet_id:
                packet = await BillingServicePacket.get(
                    data.packet_id, is_deleted=False
                )
                if not packet:
                    raise exceptions.BillingServicePacketNotFoundError(data.packet_id)
            else:
                packet = None
            data_to_update["packet"] = packet
        elif promo_code.packet_id:
            packet = await BillingServicePacket.get(
                promo_code.packet_id, is_deleted=False
            )
        else:
            packet = None

        if "stripe_coupon" in data_to_update:
            if data.stripe_coupon:
                await self.validate_stripe_coupon(data.stripe_coupon, packet)
            else:
                data_to_update["stripe_coupon"] = None
                data.stripe_coupon = None

        if (
                "trial_period_days" in data_to_update and
                data_to_update["trial_period_days"] is None
        ):
            data_to_update["trial_period_days"] = 0

        await promo_code.update(**data_to_update)
        return self.promo_code_to_schema(promo_code, packet)

    async def delete(self, promo_code_id: int):
        promo_code = await self.get_promo_code(promo_code_id)
        await promo_code.delete()
        return schemas.OkResponse()
