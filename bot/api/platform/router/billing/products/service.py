from api.platform.base import PlatformAdminService
from core.billing.functions import sync_billing_products
from db import crud


# noinspection PyMethodMayBeStatic
class BillingProductsService(PlatformAdminService):
    async def sync_products(self):
        await sync_billing_products()
        return await self.get_products()

    async def get_products(self):
        return await crud.billing.get_products()
