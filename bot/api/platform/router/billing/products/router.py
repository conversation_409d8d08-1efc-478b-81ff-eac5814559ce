from fastapi import APIRouter, Depends

import schemas
from .service import BillingProductsService

router = APIRouter(
    prefix="/products",
    tags=["billing-products"],
)


@router.get("/")
async def get_billing_products_list(
        service: BillingProductsService = Depends()
) -> list[schemas.BillingProductSchema]:
    return await service.get_products()


@router.post("/sync")
async def sync_billing_products(
        service: BillingProductsService = Depends(),
) -> list[schemas.BillingProductSchema]:
    return await service.sync_products()
