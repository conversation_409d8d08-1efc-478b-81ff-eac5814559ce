from fastapi import APIRouter, Depends, Security

import schemas
from api.platform.router.media.service import PlatformMediaService

router = APIRouter(
    prefix="/media",
    tags=["media"],
)


@router.post("/")
async def upload_platform_media(
        data: schemas.UploadMediaData = Depends(),
        service: PlatformMediaService = Security(scopes=["me:write"])
) -> schemas.MediaInfo:
    return await service.upload(data)
