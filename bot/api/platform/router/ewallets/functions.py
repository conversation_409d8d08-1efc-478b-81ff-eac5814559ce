from fastapi import HTTPException

import exceptions
import schemas
from core.ad.functions import ad_to_schema
from core.ewallet_users.deep_links import EwalletUserDeepLink
from core.loyalty.incust_api import incust
from core.store.functions.payments import get_icon_for_payment
from db import crud
from db.models import ClientBot, EWallet, LoyaltySettings, MediaObject
from utils.text import f


async def ewallet_to_admin_schema(ewallet_or_id: EWallet | int):
    ewallet, translations_dict = await crud.get_ewallet_with_translations(
        ewallet_or_id
    )
    if not ewallet:
        raise exceptions.AuthNoObjectsOrHaveNotEnoughPermissionsError("profile:read")

    translations = {}
    for lang, translation in translations_dict.items():
        if translation and translation.data:
            translations[lang] = schemas.platform.ewallets.EWalletTranslationSchema(
                description=translation.data.get('description'),
                info=translation.data.get('info')
            )

    media = await MediaObject.get(ewallet.media_id) if ewallet.media_id else None
    icon_url = media.url if media else get_icon_for_payment("ewallet")
    deep_link = await get_ewallet_deep_link(ewallet)

    langs_list = [ewallet.lang]
    if ewallet.langs_list:
        langs_list = [ewallet.lang] + ewallet.langs_list

    if ewallet.ad_id:
        ad = ad_to_schema(await crud.get_ad(ewallet.ad_id))
    else:
        ad = None

    return schemas.AdminEWalletSchema(
        **ewallet.as_dict(True),
        countries=ewallet.countries,  # Explicitly set countries property
        icon_url=icon_url,
        deep_link=deep_link,
        langs_list=langs_list,
        ad=ad,
    )




async def get_ewallet_deep_link(ewallet: EWallet) -> str:
    bot = await ClientBot.get(ewallet.bot_id)
    return EwalletUserDeepLink(
        ewallet_uuid_id=ewallet.uuid_id,
    ).to_str(bot.bot_type, bot.id_name)
