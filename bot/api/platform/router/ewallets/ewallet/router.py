from fastapi import APIRouter, Depends, Path, Query, Security, UploadFile

import schemas
from db.types.operation import Operation
from schemas import AdminEWalletUserListSchema
from .service import EWalletService

router = APIRouter(
    prefix="/{ewallet_id}"
)


@router.get("/")
async def get_ewallet(
        ewallet_id: int = Path(..., description="ID eWallet"),
        service: EWalletService = Depends()
) -> schemas.AdminEWalletSchema:
    return await service.get_ewallet(ewallet_id)


@router.patch("/")
async def update_ewallet(
        data: schemas.AdminEWalletUpdateSchema,
        ewallet_id: int = Path(..., description="ID eWallet"),
        service: EWalletService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AdminEWalletSchema:
    return await service.update_ewallet(ewallet_id, data)


@router.delete("/")
async def delete_ewallet(
        ewallet_id: int = Path(..., description="ID eWallet"),
        service: EWalletService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.OkResponse:
    return await service.delete_ewallet(ewallet_id)


@router.post("/update_image")
async def update_ewallet_icon(
        file: UploadFile,
        ewallet_id: int,
        service: EWalletService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AdminEWalletSchema:
    return await service.update_payment_icon(ewallet_id, file)


@router.delete("/delete_image")
async def delete_ewallet_icon(
        ewallet_id: int,
        service: EWalletService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AdminEWalletSchema:
    return await service.delete_payment_icon(ewallet_id)


@router.post("/update_deeplink")
async def update_deeplink(
        ewallet_id: int,
        service: EWalletService = Security(scopes=["me:write", "profile:edit"]),
) -> schemas.AdminEWalletDeepLinkSchema:
    return await service.update_ewallet_uuid_id(ewallet_id)


@router.get("/users")
async def get_ewallet_users(
        ewallet_id: int,
        service: EWalletService = Security(scopes=["me:write", "profile:edit"]),
) -> list[AdminEWalletUserListSchema]:
    return await service.get_ewallet_users(
        ewallet_id=ewallet_id,
    )


@router.delete("/users")
async def delete_ewallet_user(
        ewallet_id: int,
        ewallet_user_id: int,
        service: EWalletService = Security(scopes=["me:write", "profile:edit"]),
) -> list[AdminEWalletUserListSchema]:
    return await service.delete_ewallet_user(
        ewallet_id=ewallet_id,
        user_id=ewallet_user_id,
    )


@router.post("/users")
async def create_ewallet_user(
        ewallet_id: int,
        ewallet_user_id: int,
        service: EWalletService = Security(scopes=["me:write", "profile:edit"]),
) -> list[AdminEWalletUserListSchema]:
    return await service.create_ewallet_user(
        ewallet_id=ewallet_id,
        user_id=ewallet_user_id,
    )


@router.get("/invoice_templates")
async def get_ewallet_invoice_templates(
        ewallet_id: int,
        bot_id: int = Query(..., description="ID бота"),
        search_text: str | None = Query(None),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit client bots. Max: 100"),
        operation: Operation = Query(None, nullable=True, description="operation"),
        service: EWalletService = Security(scopes=["me:write", "profile:edit"]),
) -> list[schemas.platform.ewallets.AdminEWalletInvoiceTemplateListSchema]:
    return await service.get_ewallet_invoice_templates(
        ewallet_id,
        bot_id,
        search_text=search_text,
        offset=offset,
        limit=limit,
        operation=operation,
    )
