import uuid
from fastapi import UploadFile

import schemas
from api.platform.base import PlatformAdminService
from api.platform.router.ewallets.functions import (
    ewallet_to_admin_schema, get_ewallet_deep_link,
)
from core.media_manager import media_manager
from core.store.functions.payments import get_icon_for_payment
from db import crud
from db.models import ClientBot, EWallet, MediaObject
from db.types.operation import Operation
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class EWalletService(PlatformAdminService):
    async def get_ewallet(
            self, ewallet_id: int, is_db_object: bool | None = None
    ) -> EWallet | schemas.AdminEWalletSchema:
        if is_db_object:
            ewallet = await EWallet.get(id=ewallet_id, is_deleted=False)
            if not ewallet:
                raise AuthNoObjectsOrHaveNotEnoughPermissionsError("profile:read")
            return ewallet

        return await ewallet_to_admin_schema(ewallet_id)

    async def update_ewallet(
            self, ewallet_id: int, data: schemas.AdminEWalletUpdateSchema
    ) -> schemas.AdminEWalletSchema:

        if data.invoice_template_id == 0:
            data.invoice_template_id = None

        ewallet = await self.get_ewallet(ewallet_id, True)
        if not ewallet.media_id:
            media = await media_manager.download_media(
                get_icon_for_payment("ewallet")
            )
        else:
            media = await MediaObject.get(ewallet.media_id)

        data_dict = data.dict(
            exclude={"translations", "langs_list", "countries"},
            exclude_unset=True,
        )

        if "discount_percent" in data_dict and not data_dict["discount_percent"]:
            data_dict["discount_percent"] = 0

        # if ad is specified in response — take the original value, instead of converted
        if "ad" in data_dict:
            data_dict["ad"] = data.ad

        langs_list = data.langs_list if data.langs_list else [self.lang]
        if self.lang not in langs_list:
            langs_list.append(self.lang)

        if data.translations:
            for lang_code in data.translations.keys():
                if lang_code not in langs_list:
                    langs_list.append(lang_code)

        await crud.update_ewallet(
            ewallet_id=ewallet_id,
            media_id=media.id,
            lang=self.lang,
            langs_list=langs_list,
            countries=data.countries,
            translations=data.translations,
            **data_dict
        )

        return await self.get_ewallet(ewallet_id)

    async def delete_ewallet(self, ewallet_id: int):
        ewallet = await self.get_ewallet(ewallet_id, True)
        await ewallet.delete()
        return schemas.OkResponse()

    async def update_payment_icon(
            self, ewallet_id: int, file: UploadFile
    ) -> schemas.AdminEWalletSchema:

        media = await media_manager.save_from_upload_file(file)

        ewallet = await self.get_ewallet(ewallet_id, True)

        await ewallet.update(media_id=media.id)
        return await ewallet_to_admin_schema(ewallet)

    async def delete_payment_icon(
            self, ewallet_id: int
    ) -> schemas.AdminEWalletSchema:
        ewallet = await self.get_ewallet(ewallet_id, True)

        await ewallet.update(media_id=None)

        return await ewallet_to_admin_schema(ewallet)

    async def update_ewallet_uuid_id(
            self, ewallet_id: int, ) -> schemas.AdminEWalletDeepLinkSchema:

        ewallet = await self.get_ewallet(ewallet_id, True)

        await ewallet.update(uuid_id=uuid.uuid4().hex)

        deep_link = await get_ewallet_deep_link(ewallet)

        return schemas.AdminEWalletDeepLinkSchema(deep_link=deep_link)

    async def get_ewallet_users(
            self, ewallet_id: int, operation: Operation | None = None, ) -> list[
        schemas.AdminEWalletUserListSchema]:

        return [schemas.AdminEWalletUserListSchema.from_orm(
            ewallet_user
        ) for ewallet_user in await crud.get_ewallet_users_list(
            ewallet_id=ewallet_id, operation=operation
        )]

    async def delete_ewallet_user(
            self, ewallet_id: int, user_id: int, ) -> list[
        schemas.AdminEWalletUserListSchema]:

        await crud.delete_ewallet_user(ewallet_id=ewallet_id, user_id=user_id)

        return await self.get_ewallet_users(ewallet_id=ewallet_id, )

    async def create_ewallet_user(
            self, ewallet_id: int, user_id: int, ) -> list[
        schemas.AdminEWalletUserListSchema]:

        await crud.create_ewallet_user(ewallet_id=ewallet_id, user_id=user_id)

        return await self.get_ewallet_users(ewallet_id=ewallet_id, )

    async def get_ewallet_invoice_templates(
            self,
            ewallet_id: int,
            bot_id: int | None = None,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
            operation: Operation = "list",
    ) -> list[schemas.platform.ewallets.AdminEWalletInvoiceTemplateListSchema]:
        ewallet = await self.get_ewallet(ewallet_id, True)
        bot = await ClientBot.get(bot_id)
        return [
            schemas.platform.ewallets.AdminEWalletInvoiceTemplateListSchema.from_orm(
                template
            ) for template in
            await crud.get_invoice_templates(
                bot.group_id,
                position=offset or 0,
                limit=limit,
                operation=operation,
                search_text=search_text,
                currency=ewallet.currency,
            )
        ]
