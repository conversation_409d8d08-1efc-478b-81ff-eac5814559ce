from fastapi import APIRouter, Depends, Query, Security

import schemas.platform.ewallets
from db.types.operation import Operation
from .service import EWalletsService

router = APIRouter()


@router.get("/")
async def get_ewallet_list(
        params: schemas.AdminListParams = Depends(),
        service: EWalletsService = Depends(),
        operation: Operation = Query(None, nullable=True, description="operation"),
) -> list[schemas.AdminEWalletSchema]:
    return await service.get_list(params, operation=operation)


@router.post("/")
async def create_ewallet(
        data: schemas.AdminEWalletCreateSchema,
        service: EWalletsService = Security(scopes=["profile:create", "me:write"])
) -> schemas.AdminEWalletSchema:
    return await service.create_ewallet(data)


@router.get("/client_bots")
async def get_ewallet_client_bots(
        service: EWalletsService = Security(scopes=["me:write", "profile:edit"]),
        offset: int | None = Query(None),
        limit: int = Query(10, description="limit client bots. Max: 100"),
        search_text: str | None = Query(None),
        operation: Operation = Query(None, nullable=True, description="operation"),
) -> list[schemas.platform.ewallets.AdminEWalletClientBotListSchema]:
    return await service.get_ewallet_client_bots(
        search_text=search_text,
        offset=offset,
        limit=limit,
        operation=operation,
    )
