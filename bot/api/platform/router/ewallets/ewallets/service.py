import schemas
import schemas.platform.ewallets
from api.platform.base import PlatformAdminService
from api.platform.router.ewallets.functions import (
    ewallet_to_admin_schema,
)
from core.media_manager import media_manager
from core.store.functions.payments import get_icon_for_payment
from db import crud
from db.types.operation import Operation


class EWalletsService(PlatformAdminService):

    async def create_ewallet(
            self, data: schemas.AdminEWalletCreateSchema
    ) -> schemas.AdminEWalletSchema:
        _, terminal_id = await get_terminal_data(self.lang, data)
        if terminal_id:
            data.incust_terminal_id = terminal_id

        if data.invoice_template_id == 0:
            data.invoice_template_id = None

        media = await media_manager.download_media(
            get_icon_for_payment("ewallet")
        )

        langs_list = data.langs_list if data.langs_list else [self.lang]
        if self.lang not in langs_list:
            langs_list.append(self.lang)

        ewallet = await crud.create_ewallet(
            creator_id=self.user.id,
            media_id=media.id,
            lang=self.lang,
            langs_list=langs_list,
            **data.dict(exclude={"langs_list", "translations"}),
        )

        return await ewallet_to_admin_schema(ewallet)

    async def get_list(
            self,
            params: schemas.AdminListParams,
            operation: Operation,
    ) -> list[schemas.AdminEWalletSchema]:
        return [
            await ewallet_to_admin_schema(ewallet) for ewallet in
            await crud.get_admin_ewallet_list(
                params=params,
                user_id=self.user.id,
                operation=operation,
            )
        ]

    async def get_ewallet_client_bots(
            self,
            search_text: str | None = None,
            offset: int | None = None,
            limit: int = 10,
            operation: Operation = "list",
    ) -> list[schemas.platform.ewallets.AdminEWalletClientBotListSchema]:
        if not limit or limit > 100:
            limit = 10

        client_bots = await crud.get_admin_client_bots(
            search_text=search_text,
            position=offset or 0,
            limit=limit,
            operation=operation,
        ) or []

        return [
            schemas.platform.ewallets.AdminEWalletClientBotListSchema(
                id=bot.id,
                name=bot.display_name,
                group_id=bot.group_id,
                bot_type=bot.bot_type,
            )
            for bot in client_bots
        ]
