from typing import Optional

from core.auth.services.scopes_checker import ScopesCheckerService
from core.loyalty.services import LoyaltySettingsService
from db.models import User
from schemas import LoyaltySettingsObjectType
from schemas.admin.loyalty_settings import (
    LoyaltySettingsConfigSchema, LoyaltySettingsCopySchema, LoyaltySettingsCreateSchema,
    LoyaltySettingsResponseSchema, LoyaltySettingsUpdateSchema,
    LoyaltySettingsValidateSchema,
)


class LoyaltySettingsPlatformService:
    """
    Platform-level service for loyalty settings management.
    Handles ewallet settings and other platform-scoped resources.
    """

    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "platform:admin",
            ),
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
        
        # Initialize the core service without profile_id for platform operations
        self._service = LoyaltySettingsService(
            user=self.user,
            lang=self.lang,
            profile_id=None  # Platform operations don't require profile_id
        )

    async def get_config(self) -> LoyaltySettingsConfigSchema:
        """Отримати конфігураційні налаштування лояльності"""
        return await self._service.get_config()

    async def get_loyalty_settings_list(
            self,
            ewallet_id: Optional[int] = None,
            is_enabled: Optional[bool] = None,
            limit: int = 100,
            offset: int = 0
    ) -> list[LoyaltySettingsResponseSchema]:
        """Get loyalty settings list for platform scope (primarily ewallet settings)"""
        return await self._service.get_loyalty_settings_list(
            ewallet_id=ewallet_id,
            is_enabled=is_enabled,
            limit=limit,
            offset=offset
        )

    async def get_loyalty_settings_by_id(
            self,
            loyalty_settings_id: int,
    ) -> LoyaltySettingsResponseSchema:
        """Get loyalty settings by ID"""
        return await self._service.get_loyalty_settings_by_id(loyalty_settings_id)

    async def create_loyalty_settings(
            self,
            data: LoyaltySettingsCreateSchema
    ) -> LoyaltySettingsResponseSchema:
        """Create new loyalty settings"""
        return await self._service.create_loyalty_settings(data)

    async def update_loyalty_settings(
            self,
            loyalty_settings_id: int,
            data: LoyaltySettingsUpdateSchema
    ) -> LoyaltySettingsResponseSchema:
        """Update existing loyalty settings"""
        return await self._service.update_loyalty_settings(loyalty_settings_id, data)

    async def delete_loyalty_settings(
            self,
            loyalty_settings_id: int
    ):
        """Delete loyalty settings"""
        return await self._service.delete_loyalty_settings(loyalty_settings_id)

    async def copy_loyalty_settings(
            self,
            data: LoyaltySettingsCopySchema
    ) -> LoyaltySettingsResponseSchema:
        """Copy loyalty settings"""
        return await self._service.copy_loyalty_settings(data)

    async def validate_loyalty_settings(
            self,
            data: LoyaltySettingsCreateSchema | LoyaltySettingsUpdateSchema
    ) -> LoyaltySettingsValidateSchema:
        """Validate loyalty settings configuration"""
        return await self._service.validate_loyalty_settings(data)
    
    async def get_by_ewallet(
            self,
            ewallet_id: int
    ) -> Optional[LoyaltySettingsResponseSchema]:
        """Get loyalty settings for ewallet"""
        return await self._service.get_by_object(
            LoyaltySettingsObjectType.EWALLET,
            ewallet_id
        )
    
    async def create_or_update_by_ewallet(
            self,
            ewallet_id: int,
            data: LoyaltySettingsCreateSchema
    ) -> LoyaltySettingsResponseSchema:
        """Create or update loyalty settings for ewallet"""
        return await self._service.create_or_update_by_object(
            LoyaltySettingsObjectType.EWALLET,
            ewallet_id,
            data
        )
    
    async def delete_by_ewallet(
            self,
            ewallet_id: int
    ):
        """Delete loyalty settings for ewallet"""
        return await self._service.delete_by_object(
            LoyaltySettingsObjectType.EWALLET,
            ewallet_id
        )