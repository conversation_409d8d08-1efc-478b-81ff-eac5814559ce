from typing import Optional

from fastapi import APIRouter, Depends, Query

from api.platform.router.loyalty_settings.service import LoyaltySettingsPlatformService
from schemas.admin.loyalty_settings import (
    LoyaltySettingsConfigSchema, LoyaltySettingsCopySchema, LoyaltySettingsCreateSchema,
    LoyaltySettingsResponseSchema, LoyaltySettingsUpdateSchema,
    LoyaltySettingsValidateSchema,
)

router = APIRouter(
    prefix="/loyalty-settings",
    tags=["Platform Loyalty Settings"],
)


@router.get("/config", response_model=LoyaltySettingsConfigSchema)
async def get_config(
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Get loyalty settings configuration"""
    return await service.get_config()


@router.get("", response_model=list[LoyaltySettingsResponseSchema])
async def get_loyalty_settings_list(
        ewallet_id: Optional[int] = Query(None),
        is_enabled: Optional[bool] = Query(None),
        limit: int = Query(100, ge=1, le=1000),
        offset: int = Query(0, ge=0),
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Get list of loyalty settings for platform scope"""
    return await service.get_loyalty_settings_list(
        ewallet_id=ewallet_id,
        is_enabled=is_enabled,
        limit=limit,
        offset=offset
    )


@router.get("/{loyalty_settings_id}", response_model=LoyaltySettingsResponseSchema)
async def get_loyalty_settings_by_id(
        loyalty_settings_id: int,
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Get loyalty settings by ID"""
    return await service.get_loyalty_settings_by_id(loyalty_settings_id)


@router.post("", response_model=LoyaltySettingsResponseSchema)
async def create_loyalty_settings(
        data: LoyaltySettingsCreateSchema,
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Create new loyalty settings"""
    return await service.create_loyalty_settings(data)


@router.put("/{loyalty_settings_id}", response_model=LoyaltySettingsResponseSchema)
async def update_loyalty_settings(
        loyalty_settings_id: int,
        data: LoyaltySettingsUpdateSchema,
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Update existing loyalty settings"""
    return await service.update_loyalty_settings(loyalty_settings_id, data)


@router.delete("/{loyalty_settings_id}")
async def delete_loyalty_settings(
        loyalty_settings_id: int,
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Delete loyalty settings"""
    return await service.delete_loyalty_settings(loyalty_settings_id)


@router.post("/copy", response_model=LoyaltySettingsResponseSchema)
async def copy_loyalty_settings(
        data: LoyaltySettingsCopySchema,
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Copy loyalty settings"""
    return await service.copy_loyalty_settings(data)


@router.post("/validate", response_model=LoyaltySettingsValidateSchema)
async def validate_loyalty_settings(
        data: LoyaltySettingsCreateSchema,
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Validate loyalty settings configuration"""
    return await service.validate_loyalty_settings(data)


# EWallet-specific endpoints
@router.get("/ewallet/{ewallet_id}", response_model=LoyaltySettingsResponseSchema)
async def get_loyalty_settings_by_ewallet(
        ewallet_id: int,
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Get loyalty settings for ewallet"""
    return await service.get_by_ewallet(ewallet_id)


@router.put("/ewallet/{ewallet_id}", response_model=LoyaltySettingsResponseSchema)
async def create_or_update_loyalty_settings_by_ewallet(
        ewallet_id: int,
        data: LoyaltySettingsCreateSchema,
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Create or update loyalty settings for ewallet"""
    return await service.create_or_update_by_ewallet(ewallet_id, data)


@router.delete("/ewallet/{ewallet_id}")
async def delete_loyalty_settings_by_ewallet(
        ewallet_id: int,
        service: LoyaltySettingsPlatformService = Depends(LoyaltySettingsPlatformService)
):
    """Delete loyalty settings for ewallet"""
    return await service.delete_by_ewallet(ewallet_id)