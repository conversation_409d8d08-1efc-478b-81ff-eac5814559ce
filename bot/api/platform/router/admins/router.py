from fastapi import APIRouter, Depends, Security

import schemas
from .service import PlatformAdminsService

router = APIRouter(
    prefix="/admins",
    tags=["admins"]
)


@router.get("/")
async def get_platform_admins(
        service: PlatformAdminsService = Depends()
) -> list[schemas.PlatformAdminUserSchema]:
    return await service.get_profile_admins()


@router.get('/users_list')
async def get_users_to_add_to_platform_admins(
        params: schemas.AdminGetUsersListToAddParams = Depends(),
        service: PlatformAdminsService = Security(scopes=["me:read"])
) -> list[schemas.PlatformAdminUserSchema]:
    return await service.get_users_to_add_to_platform_admins(params)


@router.post("/")
async def add_platform_admin(
        data: schemas.AddPlatformAdminData,
        service: PlatformAdminsService = Security(scopes=["me:write"])
) -> schemas.PlatformAdminUserSchema:
    return await service.add_platform_admin(data)


@router.delete("/{user_id}")
async def delete_platform_admin(
        user_id: int,
        service: PlatformAdminsService = Security(scopes=["me:write"]),
) -> schemas.OkResponse:
    await service.delete_platform_admin(user_id)
    return schemas.OkResponse()
