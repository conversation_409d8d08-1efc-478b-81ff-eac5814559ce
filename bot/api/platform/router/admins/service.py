import exceptions
import schemas
from api.platform.base import PlatformSuperadminService
from db import crud
from db.models import User
from schemas import CreateScopeData


# noinspection PyMethodMayBeStatic
class PlatformAdminsService(PlatformSuperadminService):

    async def get_profile_admins(self):
        return await crud.get_users_for_action(
            "platform:admin", exclude_scopes=["platform:superadmin"]
        )

    async def get_users_to_add_to_platform_admins(
            self,
            params: schemas.AdminGetUsersListParams,
    ):
        exclude_scopes = ["platform:superadmin", "platform:admin"]

        users_list = await crud.get_platform_users_list(
            params, exclude_scopes, for_add=True
        )

        result = []

        for user in users_list:
            schema = schemas.PlatformAdminUserSchema.from_orm(user)
            result.append(schema)
        return result

    async def add_platform_admin(self, data: schemas.AddPlatformAdminData):
        user_to_add = await User.get_by_id(data.user_id)
        if not user_to_add:
            raise exceptions.UserNotFoundError(data.user_id)

        await crud.grant_scopes(
            "user", user_to_add.id, [CreateScopeData(scope="platform:admin")]
        )

        return user_to_add

    async def delete_platform_admin(self, user_id: int):
        await crud.delete_scope(
            target="user",
            user_id=user_id,
            scope="platform:admin",
        )
