import schemas
from api.platform.base import PlatformAdminService
from db import crud
from db.models.ewallet.ewallet_merchant import EWalletMerchant
from db.types.operation import Operation


class EWalletMerchantsService(PlatformAdminService):
    async def get_list(
            self,
            params: schemas.EwalletMerchantsListParams,
            operation: Operation = "list",
    ) -> list[schemas.EWalletMerchant]:
        merchants = await crud.get_platform_ewallet_merchant_list(
            params=params,
            operation=operation,
        )
        return [schemas.EWalletMerchant.from_orm(merchant) for merchant in merchants]

    async def create_ewallet_merchant(
        self, data: schemas.EWalletMerchantCreate
    ) -> EWalletMerchant:
        return await EWalletMerchant.create(**data.dict())

    async def get_ewallet_merchant(self, merchant_id: int) -> EWalletMerchant:
        return await EWalletMerchant.get(merchant_id)

    async def update_ewallet_merchant(
        self, merchant_id: int, data: schemas.EWalletMerchantUpdate
    ) -> EWalletMerchant:
        merchant = await EWalletMerchant.get(merchant_id)
        return await merchant.update(data.dict(exclude_unset=True))

    async def delete_ewallet_merchant(self, merchant_id: int) -> None:
        merchant = await EWalletMerchant.get(merchant_id)
        await merchant.delete()