from fastapi import APIRouter, Depends

import schemas
from .service import EWalletMerchantsService

router = APIRouter(
    prefix="/ewallet_merchants",
    tags=["E-Wallet Merchants"],
)



@router.get("/")
async def get_ewallet_merchant_list(
    params: schemas.EwalletMerchantsListParams = Depends(),
    service: EWalletMerchantsService = Depends(),
) -> list[schemas.EWalletMerchant]:
    return await service.get_list(params)


@router.post("/")
async def create_ewallet_merchant(
    data: schemas.EWalletMerchantCreate,
    service: EWalletMerchantsService = Depends(),
) -> schemas.EWalletMerchant:
    return await service.create_ewallet_merchant(data)


@router.get("/{merchant_id}")
async def get_ewallet_merchant(
    merchant_id: int,
    service: EWalletMerchantsService = Depends(),
) -> schemas.EWalletMerchant:
    return await service.get_ewallet_merchant(merchant_id)


@router.put("/{merchant_id}")
async def update_ewallet_merchant(
    merchant_id: int,
    data: schemas.EWalletMerchantUpdate,
    service: EWalletMerchantsService = Depends(),
) -> schemas.EWalletMerchant:
    return await service.update_ewallet_merchant(merchant_id, data)


@router.delete("/{merchant_id}")
async def delete_ewallet_merchant(
    merchant_id: int,
    service: EWalletMerchantsService = Depends(),
) -> None:
    await service.delete_ewallet_merchant(merchant_id)
