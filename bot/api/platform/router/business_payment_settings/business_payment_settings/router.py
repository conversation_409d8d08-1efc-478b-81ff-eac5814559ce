from fastapi import APIRouter, Depends, Query, Security

import schemas
from db.types.operation import Operation
from .service import BusinessPaymentSettingsService

router = APIRouter()


@router.get("/")
async def get_business_payment_setting_list(
    params: schemas.AdminListParams = Depends(),
    service: BusinessPaymentSettingsService = Depends(),
    operation: Operation = Query(None, nullable=True, description="operation"),
) -> list[schemas.AdminBusinessPaymentSettingListSchema]:
    return await service.get_list(params, operation=operation)


@router.post("/")
async def create_business_payment_setting(
    data: schemas.AdminBusinessPaymentSettingCreateSchema,
    service: BusinessPaymentSettingsService = Security(scopes=["profile:create", "me:write"])
) -> schemas.AdminBusinessPaymentSettingListSchema:
    return await service.create_business_payment_setting(data)


@router.get("/methods_schema")
async def get_payment_methods_schema(
    service: BusinessPaymentSettingsService = Security(scopes=["profile:create", "me:write"])
) -> schemas.PaymentProvidersSchema:
    return await service.get_payment_methods_schema()
