from typing import cast

import schemas
from api.platform.base import PlatformAdminService
from api.platform.router.business_payment_settings.functions import \
    (
    _process_payment_data, bps_to_admin_schema,
)
from config import (
    ADDITIONAL_PAYMENT_METHODS_ONLINE, INCUST_SERVER_API, PAYMENT_DEFAULT_NAMES,
    PAYMENT_METHODS,
)
from core.payment.funcs import get_payment_default_name, get_payment_method_fields
from db import crud
from db.types.operation import Operation
from loggers import JSONLogger

debugger = JSONLogger("business_payment_settings")


class BusinessPaymentSettingsService(PlatformAdminService):

    async def create_business_payment_setting(
        self, data: schemas.AdminBusinessPaymentSettingCreateSchema
    ) -> schemas.AdminBusinessPaymentSettingListSchema:
        # Обробляємо дані платежу перед збереженням
        payment_data = await _process_payment_data(data.payment_data)
        
        business_payment_setting = await crud.create_business_payment_setting(
            creator_id=self.user.id,
            incust_account_id=data.incust_account_id,
            name=data.name,
            description=data.description,
            is_enabled=data.is_enabled,
            payment_data=payment_data
        )

        return bps_to_admin_schema(business_payment_setting)

    async def get_list(
        self,
        params: schemas.AdminListParams,
        operation: Operation,
    ) -> list[schemas.AdminBusinessPaymentSettingListSchema]:
        return [bps_to_admin_schema(bps) for bps in await crud.get_admin_business_payment_setting_list(
            params=params,
            user_id=self.user.id,
            operation=operation,
        )]

    async def get_payment_methods_schema(self) -> schemas.PaymentProvidersSchema:
        providers = ["orange", "wave", "airtel", "momo", "kpay"]

        items = []
        default_names = {}

        for provider in providers:
            item = get_payment_method_fields(cast(schemas.BasicProvidersType, provider), True)

            items.append(item)
            default_names[provider] = await get_payment_default_name(
                provider, self.lang,
                provider in ADDITIONAL_PAYMENT_METHODS_ONLINE or provider in PAYMENT_METHODS
            )

        for key, _ in PAYMENT_DEFAULT_NAMES.items():
            if key == "online_card":
                continue
            if key not in providers:
                default_names[key] = await get_payment_default_name(
                    key, self.lang,
                    key in ADDITIONAL_PAYMENT_METHODS_ONLINE or key in PAYMENT_METHODS
                )

        extra_data = {
            "default_names": default_names,
            "default_incust_server_url": INCUST_SERVER_API,
        }

        return schemas.PaymentProvidersSchema(schemas=items, extra_data=extra_data)
