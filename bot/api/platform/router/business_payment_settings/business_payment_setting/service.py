import schemas
from api.platform.base import PlatformAdminService
from api.platform.router.business_payment_settings.functions import \
    (
    _process_payment_data,
)
from db import crud
from db.models import BusinessPaymentSetting
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError


class BusinessPaymentSettingService(PlatformAdminService):
    async def get_business_payment_setting(self, business_payment_setting_id: int):
        business_payment_setting = await BusinessPaymentSetting.get(
            business_payment_setting_id
        )
        if not business_payment_setting:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "profile:read",
            )

        payment_data = await crud.get_active_business_payment_data(
            business_payment_setting_id=business_payment_setting_id
        )

        return (business_payment_setting, payment_data)

    async def update_business_payment_setting(
            self,
            business_payment_setting_id: int,
            data: schemas.AdminBusinessPaymentSettingUpdateSchema
    ):
        payment_data = await _process_payment_data(data.payment_data)
        await crud.update_business_payment_setting(
            business_payment_setting_id=business_payment_setting_id,
            creator_id=self.user.id,
            incust_account_id=data.incust_account_id,
            name=data.name,
            description=data.description,
            is_enabled=data.is_enabled,
            payment_data=payment_data
        )
        return await self.get_business_payment_setting(business_payment_setting_id)

    async def delete_business_payment_setting(self, business_payment_setting_id: int):
        business_payment_setting = await self.get_business_payment_setting(
            business_payment_setting_id
        )
        setting, _ = business_payment_setting
        await setting.delete()
        return schemas.OkResponse()

    async def delete_business_payment_data(self, business_payment_data_id: int):
        """
        Позначає провайдер бізнес-платежу як видалений
        """
        result = await crud.delete_business_payment_data(business_payment_data_id)
        if not result:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "profile:edit",
            )
        return schemas.OkResponse()

    async def update_business_payment_data_status(self, business_payment_data_id: int, is_enabled: bool):
        """
        Оновлює статус активності (is_enabled) провайдера бізнес-платежу
        """
        result = await crud.update_business_payment_data_enabled(business_payment_data_id, is_enabled)
        if not result:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                "profile:edit",
            )
        return schemas.OkResponse()
