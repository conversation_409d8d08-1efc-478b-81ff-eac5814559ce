from fastapi import APIRouter, Depends, Path, Security

import schemas
from .service import BusinessPaymentSettingService
from ..functions import bps_to_admin_schema

router = APIRouter(
    prefix="/{business_payment_setting_id}"
)


@router.get("/")
async def get_business_payment_setting(
        business_payment_setting_id: int = Path(..., description="ID admin email"),
        service: BusinessPaymentSettingService = Depends()
) -> schemas.AdminBusinessPaymentSettingListSchema:
    business_payment_setting_data = await service.get_business_payment_setting(
        business_payment_setting_id
    )
    return bps_to_admin_schema(business_payment_setting_data)


@router.patch("/")
async def update_business_payment_setting(
        data: schemas.AdminBusinessPaymentSettingUpdateSchema,
        business_payment_setting_id: int = Path(..., description="ID admin email"),
        service: BusinessPaymentSettingService = Security(
            scopes=["me:write", "profile:edit"]
        ),
) -> schemas.AdminBusinessPaymentSettingListSchema:
    business_payment_setting_data = await service.update_business_payment_setting(
        business_payment_setting_id, data
    )
    return bps_to_admin_schema(business_payment_setting_data)


@router.delete("/")
async def delete_business_payment_setting(
        business_payment_setting_id: int = Path(..., description="ID admin email"),
        service: BusinessPaymentSettingService = Security(
            scopes=["me:write", "profile:edit"]
        ),
) -> schemas.OkResponse:
    return await service.delete_business_payment_setting(business_payment_setting_id)


@router.delete("/payment_data/{business_payment_data_id}")
async def delete_business_payment_data(
        business_payment_setting_id: int = Path(
            ..., description="ID business payment setting"
        ),
        business_payment_data_id: int = Path(
            ..., description="ID business payment data"
        ),
        service: BusinessPaymentSettingService = Security(
            scopes=["me:write", "profile:edit"]
        ),
) -> schemas.OkResponse:
    """Позначити провайдер платежу як видалений"""
    return await service.delete_business_payment_data(business_payment_data_id)


@router.patch("/payment_data/{business_payment_data_id}")
async def update_business_payment_data_status(
        data: schemas.BusinessPaymentDataStatusUpdateSchema,
        business_payment_setting_id: int = Path(
            ..., description="ID business payment setting"
        ),
        business_payment_data_id: int = Path(
            ..., description="ID business payment data"
        ),
        service: BusinessPaymentSettingService = Security(
            scopes=["me:write", "profile:edit"]
        ),
) -> schemas.OkResponse:
    """Оновити статус активності (is_enabled) провайдера платежу"""
    return await service.update_business_payment_data_status(
        business_payment_data_id, data.is_enabled
    )
