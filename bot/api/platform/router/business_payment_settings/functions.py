from typing import Any, <PERSON>, Tuple, Union

import schemas
from core.payment.payment_processor.providers.orange.client import (
    encrypt_pin_with_public_key, get_orange_token, get_public_key,
)
from db.models import BusinessPaymentData, BusinessPaymentSetting
from loggers import JSONLogger
from schemas import BusinessPaymentDataCreateSchema, BusinessPaymentMethodEnum

debugger = JSONLogger("business_payment_settings")

def bps_to_admin_schema(business_payment_setting_data: Union[Tuple[Any, List[BusinessPaymentData]], BusinessPaymentSetting]):
    if isinstance(business_payment_setting_data, tuple):
        business_payment_setting, payment_data = business_payment_setting_data
    else:
        business_payment_setting = business_payment_setting_data
        payment_data = getattr(business_payment_setting, "payment_data", [])
    
    base_schema = schemas.AdminBusinessPaymentSettingListSchema.from_orm(business_payment_setting)

    payment_data_list = []
    for pd in payment_data:
        if isinstance(pd, BusinessPaymentData) and not pd.is_deleted:
            json_data = dict(pd.json_data) if pd.json_data else {}

            if pd.payment_method == BusinessPaymentMethodEnum.ORANGE and "encrypted_pin_code" in json_data:
                del json_data["encrypted_pin_code"]
                json_data["pin_code"] = ""
            
            payment_data_list.append(schemas.BusinessPaymentDataSchema(
                id=pd.id,
                business_payment_setting_id=pd.business_payment_setting_id,
                payment_method=pd.payment_method,
                json_data=json_data,
                is_enabled=pd.is_enabled
            ))

    return schemas.AdminBusinessPaymentSettingListSchema(
        **base_schema.dict(exclude={"payment_data"}),
        payment_data=payment_data_list
    )


async def _process_orange_payment_data(json_data: dict) -> dict | None:
    """
    Обробляє дані платежу Orange, шифруючи PIN-код.

    Args:
        payment_data: Дані платежу Orange
    """

    pin_code = json_data.get("pin_code")
    if pin_code:
        try:
            client_id = json_data.get("client_id")
            client_secret = json_data.get("client_secret")

            if client_id and client_secret:
                token = await get_orange_token(client_id, client_secret)
                public_key = await get_public_key(token)

                encrypted_pin_code = encrypt_pin_with_public_key(pin_code, public_key)

                json_data["encrypted_pin_code"] = encrypted_pin_code

                del json_data["pin_code"]

                debugger.debug(
                    "Orange PIN code successfully encrypted",
                    {"client_id": client_id}
                )
            else:
                debugger.error(
                    "Cannot encrypt Orange PIN code: missing client_id or client_secret"
                )
        except Exception as e:
            debugger.error(f"Error encrypting Orange PIN code: {e}")
            if "pin_code" in json_data:
                del json_data["pin_code"]
            raise e

    return json_data


async def _process_payment_data(payment_data: list[schemas.BusinessPaymentDataCreateSchema | schemas.BusinessPaymentDataUpdateSchema]) -> list[BusinessPaymentDataCreateSchema]:
    """
    Обробляє дані платежу перед збереженням.
    Для Orange шифрує PIN-код та зберігає зашифроване значення.

    Args:
        payment_data: Оригінальні дані платежу (список об'єктів)

    Returns:
        Оброблені дані платежу (список об'єктів)
    """
    if not payment_data:
        return payment_data

    for item in payment_data:

        if item.payment_method == BusinessPaymentMethodEnum.ORANGE:
            item.json_data = await _process_orange_payment_data(item.json_data)

    return payment_data
