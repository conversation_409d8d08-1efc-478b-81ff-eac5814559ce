from fastapi import FastAPI
from starlette.responses import JSONResponse
from stripe import StripeError

from api.decorators import api_error_handler
from config import LOG_ERRORS_IN_API_EXCEPTION_HANDLERS
from loggers import JSONLogger

logger = J<PERSON>NLogger("api")


@api_error_handler
async def stripe_error(_, error: StripeError):
    if LOG_ERRORS_IN_API_EXCEPTION_HANDLERS:
        logger.error("stripe_error", error)

    return JSONResponse(
        status_code=error.http_status,
        content={"detail": error.user_message}
    )


def register_platform_exception_handlers(app: FastAPI):
    app.add_exception_handler(StripeError, stripe_error)
