from core.auth.services.scopes_checker import ScopesCheckerService
from db.models import User


class PlatformAdminService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "platform:admin"
            )
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang


class PlatformSuperadminService:
    def __init__(
            self,
            scopes: ScopesCheckerService = ScopesCheckerService.depend(
                "platform:superadmin"
            )
    ):
        self.user: User = scopes.user
        self.lang: str = scopes.lang
