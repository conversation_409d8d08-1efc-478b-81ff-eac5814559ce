from fastapi import APIRouter, Depends

import schemas
from api.billing.router.catalog.service import BillingCatalogService

router = APIRouter(
    prefix="/catalog/{country_code}",
    tags=["catalog"]
)


@router.get("/")
async def get_billing_catalog(
        params: schemas.BillingCatalogParams = Depends(),
        service: BillingCatalogService = Depends(),
) -> schemas.BillingCatalogSchema:
    return await service.get_catalog(params)


@router.get("/promo_code/{promo_code}")
async def get_billing_promo_code_info(
        promo_code: str,
        service: BillingCatalogService = Depends(),
) -> schemas.BillingPromoCodeInfoSchema:
    return await service.get_promo_code_info(promo_code)
