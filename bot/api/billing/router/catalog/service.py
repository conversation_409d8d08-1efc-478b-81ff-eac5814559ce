from collections import defaultdict
from decimal import Decimal
from typing import <PERSON><PERSON><PERSON><PERSON>

from fastapi import Depends
from psutils.translator.schemas import TranslateObjectData
from stripe import StripeError

import exceptions
import schemas
from core.api.depends import get_lang
from core.auth.depend import get_active_user_optional
from core.billing.stripe_client import bstripe
from db import crud
from db.models import BillingPromoCode, User
from loggers import JSONLogger
from utils.platform_admins import send_message_to_platform_admins
from utils.translator import td

ToTranslateType: TypeAlias = dict[
    str,
    dict[
        str,
        TranslateObjectData
    ]
]
TranslatedType: TypeAlias = dict[str, dict[str, str | None]]


class BillingCatalogService:
    def __init__(
            self,
            country_code: str,
            user: User | None = Depends(get_active_user_optional),
            lang: str = Depends(get_lang)
    ):
        self.country_code: str = country_code
        self.user: User | None = user
        self.lang: str = lang

    async def get_catalog(self, params: schemas.BillingCatalogParams):
        if not params.only_public and (
                not self.user or
                not await crud.check_access_to_action(
                    "platform:admin", "user", self.user.id,
                )
        ):
            raise exceptions.BillingRetrievePrivatePacketsForbiddenError()

        packets_data = await crud.billing.get_catalog_packets_data(
            self.country_code,
            params.only_public,
            params.recurring_interval,
            self.lang,
        )

        # key is original lang, value is to translate data
        to_translate: ToTranslateType = defaultdict(dict)

        packets = [
            self.process_packet_data(packet_data, to_translate)
            for packet_data in packets_data
        ]

        translated = await self.translate_packets(to_translate)

        for packet in packets:
            self.set_packet_translations(packet, translated)

        return schemas.BillingCatalogSchema(
            packets=packets,
        )

    def process_packet_data(
            self, packet_data: crud.billing.PacketData,
            to_translate: ToTranslateType
    ):
        packet = packet_data.packet

        to_translate[packet.lang][f"packet-{packet.id}"] = (
            TranslateObjectData(
                object=packet,
                translation=packet_data.translations.get(self.lang),
                result_type="dict",
            )
        )

        items = []

        for item_data in packet_data.items:
            item = item_data.item

            to_translate[packet.lang][f"item-{item.id}"] = (
                TranslateObjectData(
                    object=item,
                    translation=item_data.translations.get(self.lang),
                    result_type="dict",
                )
            )

            items.append(
                schemas.BillingServicePacketItemCatalogSchema(
                    **item.as_dict(),
                    stripe_product_id=item_data.product.stripe_id,
                    product_code=item_data.product.code,
                )
            )

        packet_schema = schemas.BillingServicePacketCatalogSchema(
            **packet.as_dict(),
            items=items,
        )
        return packet_schema

    async def translate_packets(self, to_translate: ToTranslateType):
        translated: TranslatedType = {}

        for original_lang, to_translate_lang in to_translate.items():
            translated_lang = await td(
                to_translate_lang,
                self.lang, original_lang,
                group_id="internal",
                is_auto_translate_allowed=True,
            )
            translated.update(translated_lang)

        return translated

    def set_packet_translations(
            self, packet: schemas.BillingServicePacketCatalogSchema,
            translated: TranslatedType
    ):
        translated_packet = translated.get(f"packet-{packet.id}", {})
        for key, value in translated_packet.items():
            if value:
                setattr(packet, key, value)

        for item in packet.items:
            translated_item = translated.get(f"item-{item.id}", {})
            for key, value in translated_item.items():
                if value:
                    setattr(item, key, value)

    async def get_promo_code_info(self, promo_code: str):
        promo_code_obj = await BillingPromoCode.get(code=promo_code)
        if not promo_code_obj:
            raise exceptions.BillingPromoCodeNotFoundError(promo_code)

        if (
                promo_code_obj.packet_id and
                (
                        packet_data := await crud.billing.get_packet_catalog_data(
                            promo_code_obj.packet_id, True
                        )
                )
        ):
            to_translate: ToTranslateType = defaultdict(dict)
            packet_schema = self.process_packet_data(packet_data, to_translate)
            translated = await self.translate_packets(to_translate)
            self.set_packet_translations(packet_schema, translated)
        else:
            packet_schema = None

        return schemas.BillingPromoCodeInfoSchema(
            **promo_code_obj.as_dict(True),
            packet=packet_schema,
            coupon=await self.get_stripe_coupon_info(promo_code_obj.stripe_coupon),
        )

    async def get_stripe_coupon_info(
            self, stripe_coupon: str | None,
            promo_code: str | None = None
    ):
        if not stripe_coupon:
            return None

        try:
            stripe_coupon_obj = await bstripe.coupons.retrieve_async(
                stripe_coupon, {
                    "expand": ["applies_to", "currency_options"]
                }
            )
        except StripeError as e:
            JSONLogger(
                "stripe", "Retrieve coupon", {
                    "stripe_coupon": stripe_coupon,
                    "error": dict(e.error),
                    "message": e.user_message,
                }
            ).error("FAILED", e)
            await send_message_to_platform_admins(
                f"Retrieve stripe coupon failed!\n"
                f"Promo code: {promo_code}\n"
                f"Stripe coupon: {stripe_coupon}\n"
                f"Error: {e.user_message}"
            )
            return

        currencies_amounts_off: dict[str, Decimal] | None

        if stripe_coupon_obj.amount_off:
            currencies_amounts_off = {
                stripe_coupon_obj.currency: Decimal.from_float(
                    stripe_coupon_obj.amount_off / 100
                ),
            }
            if stripe_coupon_obj.currency_options:
                currencies_amounts_off.update(
                    {
                        currency: Decimal.from_float(options.amount_off / 100)
                        for currency, options in
                        stripe_coupon_obj.currency_options.items()
                    }
                )
        else:
            currencies_amounts_off = None

        return schemas.BillingPromoCodeCouponInfoSchema(
            **dict(stripe_coupon_obj),
            currencies_amounts_off=currencies_amounts_off,
        )
