import re
from typing import Any, Dict

from unidecode import unidecode

CYRILLIC_RE = re.compile(r'[\u0400-\u04FF]')


def contains_cyrillic(text: str) -> bool:
    """
    Check if the given string contains at least one Cyrillic character.

    :param text: Input string to inspect.
    :return: True if any Cyrillic character is found, otherwise False.
    """
    return bool(CYRILLIC_RE.search(text))


def transliterate_string(text: str) -> str:
    """
    Transliterate a Unicode string to closest ASCII representation.

    :param text: Input string (may contain Cyrillic).
    :return: ASCII-only transliterated string.
    """
    return unidecode(text)


def build_transliterated_update(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Given a Stripe `customer.updated` webhook payload, identify which fields changed
    and contain Cyrillic, transliterate them, and return a dict suitable for
    passing to stripe.Customer.modify().

    This version **merges** any transliterated address sub-fields with the existing
    address object, so you don't clobber non-Cyrillic parts.

    :param payload: The webhook payload (the "payload" object from Stripe).
    :return: Dict of fields to update, with Cyrillic values transliterated.
    """
    data = payload["data"]
    customer = data["object"]
    prev_attrs = data.get("previous_attributes", {})

    update_fields: Dict[str, Any] = {}

    for field in prev_attrs:
        new_value = customer.get(field)

        # Merge transliterated address subfields into full address
        if field == "address" and isinstance(new_value, dict):
            original_address = new_value
            transliterated_subfields: Dict[str, str] = {}
            for key, val in original_address.items():
                if isinstance(val, str) and contains_cyrillic(val):
                    transliterated_subfields[key] = transliterate_string(val)

            if transliterated_subfields:
                # Merge so non-Cyrillic parts stay intact
                merged_address = {**original_address, **transliterated_subfields}
                update_fields["address"] = merged_address

        # Top-level string fields
        elif isinstance(new_value, str) and contains_cyrillic(new_value):
            update_fields[field] = transliterate_string(new_value)

    return update_fields
