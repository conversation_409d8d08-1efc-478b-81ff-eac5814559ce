from typing import Annotated

import stripe
from fastapi import Head<PERSON>
from starlette.requests import Request

from api.billing.router.stripe.helper import build_transliterated_update
from config import BILLING_STRIPE_SECRET_KEY, BILLING_STRIPE_WEBHOOK_SIGNATURE
from core.billing.functions import (
    create_or_update_subscription,
    send_invoice_info_to_group,
)
from core.billing.stripe_client import bstripe
from db.models import Group
from loggers import JSONLogger


class BillingStripeService:
    def __init__(
            self,
            request: Request,
            signature: Annotated[str, Header(alias="stripe-signature")],
    ):
        self.request = request
        self.signature = signature
        self._event: stripe.Event | None = None

    async def get_event(self):
        if self._event:
            return self._event
        payload = await self.request.body()
        self._event = bstripe.construct_event(
            payload, self.signature,
            BILLING_STRIPE_WEBHOOK_SIGNATURE
        )
        return self._event

    async def process_subscription(self):
        event = await self.get_event()
        subscription_data = await bstripe.subscriptions.retrieve_async(
            event.data["object"]["id"],
            {
                "expand": ["items.data.price.tiers"]
            }
        )

        logger = JSONLogger(
            "billing.webhook", "Subscription", {
                "signature": self.signature,
                "payload": dict(event),
                "subscription_data": subscription_data,
            }
        )

        logger.debug("Stripe subscription webhook received")

        await create_or_update_subscription(subscription_data, logger)

    async def process_customer(self):
        event = await self.get_event()
        customer = await bstripe.customers.retrieve_async(event.data["object"]["id"])

        logger = JSONLogger(
            "billing.webhook", "Customer", {
                "signature": self.signature,
                "payload": dict(event),
                "customer": customer,
            }
        )

        logger.debug("Stripe customer webhook received")

        if customer.get("deleted"):
            group = await Group.get(stripe_customer_id=customer.id)
            if group:
                await group.update(stripe_customer_id=None)
                logger.debug(
                    "Profile customer is deleted. Cleared customer_id in profile", {
                        "profile": group
                    }
                )
        elif customer.get("metadata") and (
                profile_id := customer.metadata.get("7loc_profile_id")
        ):
            group = await Group.get(profile_id)
            if not group:
                logger.debug(
                    f"Profile for customer not found: {profile_id}", {
                        "profile_id": profile_id
                    }
                )
            elif not group.stripe_customer_id:
                await group.update(stripe_customer_id=customer.id)
                logger.debug(
                    "Detected new customer for profile. New customer id set to profile",
                    customer.id, {
                        "profile": group
                    }
                )

            if event.type and event.type.startswith("customer.update"):
                await self.process_transliterate()

    async def process_invoice(self):
        event = await self.get_event()
        invoice = await bstripe.invoices.retrieve_async(
            event.data["object"]["id"], {
                "expand": ["subscription"]
            }
        )

        logger = JSONLogger(
            "billing.webhook", {
                "signature": self.signature,
                "payload": dict(event),
                "invoice": invoice,
            }
        )

        logger.debug("Stripe invoice webhook received")

        invoice_id = invoice["id"]

        if event.type.startswith('invoice.marked_uncollectible') and invoice_id:
            stripe.api_key = BILLING_STRIPE_SECRET_KEY

            await stripe.Invoice.void_invoice_async(invoice_id)

            logger.debug(f"Stripe invoice with ID:{invoice_id} voided invoice")

        else:
            await send_invoice_info_to_group(invoice, event.type)

    async def process_transliterate(self):
        event = await self.get_event()
        update_fields = build_transliterated_update(event)

        if not update_fields:
            return

        customer_id: str = event["data"]["object"]["id"]

        stripe.api_key = BILLING_STRIPE_SECRET_KEY

        await stripe.Customer.modify_async(
            customer_id,
            **update_fields
        )

    async def process_webhook(self):
        event = await self.get_event()

        if event.type.startswith("customer.subscription."):
            await self.process_subscription()
        elif event.type.startswith("customer."):
            await self.process_customer()
        elif event.type in ("invoice.finalized", "invoice.paid",
                            "invoice.marked_uncollectible"):
            await self.process_invoice()
