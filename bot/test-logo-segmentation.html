<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Color Range Picker</title>
	<style>
        .container > div {
            max-width: 50%;
            overflow: hidden;
        }

        .container > canvas {
            max-width: 100%;
        }

        .slider-container {
            margin-top: 10px;
        }
	</style>
</head>
<body>
<h2>Завантажте зображення для обробки:</h2>
<input type="file" id="upload" accept="image/*">

<h3>Виберіть основний колір фону:</h3>
<input type="color" id="colorPicker" value="#ffffff">

<div class="slider-container">
	<label for="darkerRange">Наскільки темніше:</label>
	<input type="range" id="darkerRange" min="0" max="255" value="50">
</div>

<div class="slider-container">
	<label for="lighterRange">Наскільки світліше:</label>
	<input type="range" id="lighterRange" min="0" max="255" value="50">
</div>

<div style="display: flex; gap: 8px">
	<div style="flex: 1; overflow: hidden">
		<h3>Оригінальне зображення:</h3>
		<canvas id="originalCanvas" style="max-width: 100%"></canvas>
	</div>

	<div style="flex: 1; overflow: hidden">
		<h3>Логотип на прозорому фоні:</h3>
		<canvas id="outputCanvas" style="max-width: 100%"></canvas>
	</div>
</div>

<script async src="https://docs.opencv.org/3.4.0/opencv.js" type="text/javascript"></script>
<script>
	let img = null;

	// Завантаження зображення
	document.getElementById("upload").addEventListener("change", function(event) {
		const file = event.target.files[0];
		if (!file) return;

		img = new Image();
		img.src = URL.createObjectURL(file);
		img.onload = () => {
			detectAndSetBackgroundColor(); // Автоматичне визначення фону та встановлення слайдерів
			processImage(); // Оновлюємо зображення при першому завантаженні
		};
	});

	// Слайдери для темнішого і світлішого відтінків
	document.getElementById("darkerRange").addEventListener("input", processImage);
	document.getElementById("lighterRange").addEventListener("input", processImage);

	// Функція для автоматичного визначення кольору фону і налаштування діапазону
	function detectAndSetBackgroundColor() {
		const originalCanvas = document.getElementById("originalCanvas");
		const ctx = originalCanvas.getContext("2d");
		originalCanvas.width = img.width;
		originalCanvas.height = img.height;
		ctx.drawImage(img, 0, 0);

		// Отримуємо пікселі з країв зображення
		const topEdge = Array.from(ctx.getImageData(0, 0, img.width, 1).data); // Верхній рядок пікселів
		const bottomEdge = Array.from(ctx.getImageData(0, img.height - 1, img.width, 1).data); // Нижній рядок
		const leftEdge = Array.from(ctx.getImageData(0, 0, 1, img.height).data); // Лівий стовпчик
		const rightEdge = Array.from(ctx.getImageData(img.width - 1, 0, 1, img.height).data); // Правий стовпчик

		// Об'єднуємо всі пікселі з країв
		const edgePixels = [...topEdge, ...bottomEdge, ...leftEdge, ...rightEdge];
		const rgbValues = [];

		// Витягуємо кожен піксель (RGB)
		for (let i = 0; i < edgePixels.length; i += 4) {
			rgbValues.push([edgePixels[i], edgePixels[i + 1], edgePixels[i + 2]]); // RGB без альфа-каналу
		}

		// Знаходимо найпоширеніший колір серед пікселів країв
		const mostCommonColor = findMostCommonColor(rgbValues);

		// Перетворюємо RGB в HEX для Color Picker
		const bgColorHex = rgbToHex(mostCommonColor[0], mostCommonColor[1], mostCommonColor[2]);
		document.getElementById("colorPicker").value = bgColorHex;

		// Тепер встановлюємо розумні значення для діапазону світлішого і темнішого кольорів
		const hsv = rgbToHSV(mostCommonColor[0], mostCommonColor[1], mostCommonColor[2]);

		const darkerRange = document.getElementById("darkerRange");
		const lighterRange = document.getElementById("lighterRange");

		// Якщо фон світлий, дозволимо більше затемнення; якщо темний, більше освітлення
		if (hsv.v > 180) { // Світлий фон
			darkerRange.value = 60; // Більше затемнення
			lighterRange.value = 30; // Менше освітлення
		} else if (hsv.v < 70) { // Темний фон
			darkerRange.value = 30; // Менше затемнення
			lighterRange.value = 60; // Більше освітлення
		} else { // Середня яскравість
			darkerRange.value = 50;
			lighterRange.value = 50;
		}
	}

	// Знаходження найпоширенішого кольору серед пікселів країв
	function findMostCommonColor(colors) {
		const colorCount = {};
		let maxCount = 0;
		let mostCommon = colors[0];

		colors.forEach(color => {
			const key = color.join(",");
			colorCount[key] = (colorCount[key] || 0) + 1;

			if (colorCount[key] > maxCount) {
				maxCount = colorCount[key];
				mostCommon = color;
			}
		});

		return mostCommon;
	}

	// Перетворення RGB в HEX
	function rgbToHex(r, g, b) {
		return "#" + [r, g, b].map(x => x.toString(16).padStart(2, "0")).join("");
	}

	// Перетворення RGB в HSV
	function rgbToHSV(r, g, b) {
		const rNorm = r / 255, gNorm = g / 255, bNorm = b / 255;
		const max = Math.max(rNorm, gNorm, bNorm), min = Math.min(rNorm, gNorm, bNorm);
		let h, s, v = max;

		const d = max - min;
		s = max === 0 ? 0 : d / max;

		if (max === min) {
			h = 0; // Без кольору
		} else {
			switch (max) {
				case rNorm:
					h = (gNorm - bNorm) / d + (gNorm < bNorm ? 6 : 0);
					break;
				case gNorm:
					h = (bNorm - rNorm) / d + 2;
					break;
				case bNorm:
					h = (rNorm - gNorm) / d + 4;
					break;
			}
			h /= 6;
		}

		return {
			h: Math.round(h * 360),
			s: Math.round(s * 100),
			v: Math.round(v * 255),
		};
	}

	// Процес обробки зображення
	function processImage() {
		if (!img) return;

		const originalCanvas = document.getElementById("originalCanvas");
		const outputCanvas = document.getElementById("outputCanvas");
		const ctx = originalCanvas.getContext("2d");

		// Налаштовуємо Canvas для розміру зображення
		originalCanvas.width = img.width;
		originalCanvas.height = img.height;
		outputCanvas.width = img.width;
		outputCanvas.height = img.height;

		// Малюємо оригінальне зображення на Canvas
		ctx.drawImage(img, 0, 0);

		// Використовуємо OpenCV для обробки зображення
		let src = cv.imread(originalCanvas);
		let dst = new cv.Mat();

		// Перетворення в колірний простір HSV
		cv.cvtColor(src, dst, cv.COLOR_RGBA2RGB, 0);
		cv.cvtColor(dst, dst, cv.COLOR_RGB2HSV, 0);

		// Отримуємо обраний основний колір з Color Picker
		const baseColor = document.getElementById("colorPicker").value;
		const baseHSV = hexToHSV(baseColor);

		// Отримуємо значення слайдерів для темнішого і світлішого відтінків
		const darker = parseInt(document.getElementById("darkerRange").value);
		const lighter = parseInt(document.getElementById("lighterRange").value);

		// Визначаємо нижню та верхню межу для кольору (додаємо альфа-канал)
		const lowerBg = [Math.max(baseHSV.h - darker, 0), Math.max(baseHSV.s - darker, 0), Math.max(baseHSV.v - darker, 0), 0];
		const upperBg = [Math.min(baseHSV.h + lighter, 180), Math.min(baseHSV.s + lighter, 255), Math.min(baseHSV.v + lighter, 255), 255];

		// Створюємо маску для фону
		let lowerBound = new cv.Mat(dst.rows, dst.cols, dst.type(), lowerBg);
		let upperBound = new cv.Mat(dst.rows, dst.cols, dst.type(), upperBg);
		let mask = new cv.Mat();
		cv.inRange(dst, lowerBound, upperBound, mask);

		// Інвертуємо маску, щоб фон був прозорим, а логотип залишився
		let maskInv = new cv.Mat();
		cv.bitwise_not(mask, maskInv);

		// Створюємо прозоре зображення
		let rgbaSrc = new cv.Mat();
		cv.cvtColor(src, rgbaSrc, cv.COLOR_RGB2RGBA);
		for (let i = 0; i < maskInv.rows; i++) {
			for (let j = 0; j < maskInv.cols; j++) {
				rgbaSrc.data[i * maskInv.cols * 4 + j * 4 + 3] = maskInv.data[i * maskInv.cols + j]; // Альфа-канал
			}
		}

		// Виводимо результат на Canvas
		cv.imshow(outputCanvas, rgbaSrc);

		// Очищуємо пам'ять
		src.delete();
		dst.delete();
		mask.delete();
		maskInv.delete();
		rgbaSrc.delete();
		lowerBound.delete();
		upperBound.delete();
	}

	// Перетворення HEX в HSV для OpenCV
	function hexToHSV(hex) {
		const r = parseInt(hex.slice(1, 3), 16);
		const g = parseInt(hex.slice(3, 5), 16);
		const b = parseInt(hex.slice(5, 7), 16);
		return rgbToHSV(r, g, b);
	}
</script>
</body>
</html>
