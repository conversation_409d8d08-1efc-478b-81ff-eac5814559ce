import asyncio

from core.chat.virtual_manager.messages_sender import VmMessagesWorker
from core.chat.virtual_manager.reminders_sender import VmR<PERSON>indersWorker
from core.kafka.producer import producer
from utils.logger import setup_logger

setup_logger("vm")


async def main():
    await producer.initialise()
    print("VM workers starting...")
    await asyncio.gather(
        VmMessagesWorker().start(),
        VmRemindersWorker().start(),
    )


if __name__ == "__main__":
    asyncio.run(main())
