import asyncio

from core.billing.usage_report_worker import StripeUsageReportWorker
from core.kafka.producer import producer
from utils.logger import setup_logger

setup_logger("stripe_usage_reporter")


async def main():
    await producer.initialise()
    print("stripe usage report worker starting...")
    await asyncio.gather(
        StripeUsageReportWorker().start(),
    )


if __name__ == "__main__":
    asyncio.run(main())
