import enum
import logging
import os
from datetime import timedelta

from pydantic import BaseSettings

STATIC_DIR = "static"
UPLOADS_DIR = "uploads"
TEMP_DIR = "temp"
MAX_MEDIA_SIZE = 500 * 1024 ** 2

STATIC_DB = "static/static_db"

# bots
ROOT_BOT = "run_root_bot.py"
WHATSAPP_BOT = "run_whatsapp_bot.py"
LIP_LEP_BOT = "run_lip_lep_bot.py"
SERVICE_BOT = "run_service_bot.py"
CLIENT_BOT = "run_client_bots.py"
MONITORING_BOT = "monitor_main.py"
FRIENDLY_BOT = "run_friendly_bots.py"
API_HANDLER = "run_fastapi.py"
PORTER = "run_porter.py"
NEW_ORDERS_CHECKER = "run_new_orders_checker.py"
MAILING = "run_mailing.py"
RUN_VM_WORKERS = "run_vm_workers.py"
RUN_STRIPE_USAGE_REPORT_WORKER = "run_stripe_usage_report_worker.py"
KAFKA_WORKER = "run_kafka.py"
# ID документов
TEXT_VARIABLES = "1078dIetVHmmHbTUqDkqgACALuYhRHPKtJtvAmvvlMAo"

RUN_WHATSAPP = True

ADMIN_HOST = ""
CRM_HOST = ""
MY_7LOC_HOST = ""
CRM_ICON_PATH = "static/images/crm-icon.png"

# Кол-во выводящихся на экран событий
HOW_MANY_SCROLLS = 2
# Кол-во строк для повторного вывода инструкций
REPEAT_SCROLL_COUNT = 5

CLIENT_BOT_GROUPS_LIST_PAGE_SIZE = 10

LANGS_LIST_PAGE_SIZE = 10
LOCATIONS_LIST_PAGE_SIZE = 10

# кнопки пагинации списка отзывов
REVIEWS_LIST_PAGINATION_BUTTONS = [1, 5, 20, 50]
# кнопки пагинации истории сообщений
MESSAGE_HISTORY_PAGINATION_BUTTONS = [1, 5, 20, 50]
# кнопки пагинации сервисного бота
SERVICE_BOT_PAGINATION_BUTTONS = [1, 5, 20, 50]
# кнопки пагинации события в админ боте
CHATS_SUBSCRIBE_LIST_PAGE_SIZE = 10

FINANCE_SYSTEMS_PAGE_SIZE = 10

# Нотификации в сервисном боте

SERVICE_BOT_NOTIFICATIONS_DELAY = 1800

# параметры группы
GROUP_SYSTEM_ARGS = []
# параметры бота
BOT_SYSTEM_ARGS = ["is_pay4say", "is_friendly"]
# папка для логов
LOGS_FOLDER = "logs"
DEBUG_SQLALCHEMY = False
ERRORS_LOG_LEVEL = logging.ERROR
SETUP_DEBUG_LOGGER = True
LOG_GET_OBJECTS_EXECUTING_TIME = False
# Путь к json файлу от google
PATH_TO_GOOGLE_CREDS_JSON = "config/PythonProject.json"
PATH_TO_GOOGLE_API_MANAGE_CREDS_JSON = "config/ManageApiKeys.json"
PATH_TO_FCM_CREDS_JSON = "config/FCM-google-file.json"

# путь к локально лежащим переменным
PATH_TO_LOCAL_DATA = f"../local_data.json"

REFERRALS_PERCENTS = (20, 10, 5, 2.5, 0)

DEFAULT_FINANCE_SYSTEM_ID = 1

DEFAULT_TIME_ZONE = "Europe/Kiev"

# Варианты напоминаний о событии
REMINDER_TIME_INTERVALS = [
    [
        timedelta(days=0, hours=0, minutes=10),
        timedelta(days=0, hours=2, minutes=0),
        timedelta(days=1, hours=0, minutes=0),
        timedelta(days=2, hours=0, minutes=0),
    ],
]

# Варианты напоминаний о событии
AUTO_REMINDER_TIME_INTERVALS = [
    timedelta(days=0, hours=2, minutes=0),
    timedelta(days=1, hours=0, minutes=0),
]

MEDIA_WITH_CAPTION = ["photo", "video", "voice", "audio", "document", "animation",
                      "image"]

SUPPORTED_MEDIA_TYPES = ["sticker", "location", "contact"] + MEDIA_WITH_CAPTION

SUPPORTED_MESSAGE_TYPES = ["text"] + SUPPORTED_MEDIA_TYPES

# интервал между проверками новых напоминаний
CHECK_CURRENT_REMINDERS_DELAY = 1  # в секундах

MESSAGE_SENDER_SLEEP_TIME = 1

TIME_FORMAT = "%H:%M"

DATETIME_FORMAT = "%d.%m.%Y %H:%M"
DATETIME_FORMAT_SECONDS = "%d.%m.%Y %H:%M:%S"

DATETIME_SHORT_FORMAT = "%d.%m %H:%M"

DATETIME_WITH_SEC_FORMAT = "%d.%m.%Y %H:%M:%S"

DATE_FORMAT = "%d.%m.%Y"

URL_RE_PATTERN = (r"(http(s)?://)?[a-zA-Z0-9-]+(\.[a-zA-Z0-9-])+["
                  r"a-zA-Z0-9а-яА-Яа-яА-Я-%._~:/?#\[\]@!$&\"()*+,;=]+")

YOUTUBE_PATTERN = r"(https://)?(m\.)?youtube.com/watch?v=.+"

DEFAULT_LANG = "en"

DEFAULT_CURRENCY = "USD"

ANONYMOUS_IMAGE_PATH = "static/images/anonymous.jpg"

MIN_TG_PHOTO_WIDTH = 512

CHANNELS_LIST_PAGE_SIZE = 20

FIELDS_LIST_PAGE_SIZE = 10

DEVELOPERS_CHAT_IDS = [*********, *********, *********, *********]

SUPPORTED_HTML_TAGS = (
    "b",
    "strong",
    "i",
    "em",
    "u",
    "ins",
    "s",
    "strike",
    "del",
    "span",
    "tg-spoiler",
    "code",
    "pre",
    "a",
)

SEND_POLL_TIMER = 3

QRCODE_SCANNER_URL = "https://qr.incust.com"

APISCAN_HOST = "https://bot.payforsay.com"

API_RECEIPT_URL = "https://ekasa.financnasprava.sk/mdu/api/v1/opd/receipt/find"

API_RECEIPT_UA_URL = "https://cabinet.tax.gov.ua/ws/api_public/rro/chkAll"

API_ORG_URL = "https://cabinet.tax.gov.ua/ws/api/public/registers/rro"

RECEIPT_UA_TOKEN = "cf62f056-840a-4f82-962d-2c04489dee6b"

DEFAULT_PROMOCODE_LENGTH = 6

USE_LOCALISATION = "**localisation**"

# интервал между отправкой запросов
MONITORING_REQUESTS_DELAY = timedelta(minutes=10).total_seconds()
MONITORING_SEND_MESSAGES_DELAY = timedelta(seconds=1).total_seconds()
MONITORING_CONFIRMATION_DELAY = timedelta(seconds=15).total_seconds()

MONITORING_CHAT_IDS = [-1001507900639]

INTERVAL_IN_COEF = {
    "hours": 3600,
    "minutes": 60,
    "seconds": 1,
}

INTERVAL_IN_TYPES = ("seconds", "minutes", "hours")
INTERVAL_IN_TYPES_DAYS = ("minutes", "hours", "days")

GET_BOT_API_DOMAIN = "a.payforsay.com"

BEFORE_DELETE_SLEEP = 1

DELAY_BETWEEN_CHECK_VIRTUAL_MANAGERS_MESSAGES = 1
VIRTUAL_MANAGERS_TO_SEND_ITERATION_LIMIT = 20

DELAY_BETWEEN_CHECK_RESET_STATE = timedelta(seconds=10).total_seconds()

DELAY_BEFORE_RESET_STATE = timedelta(minutes=2).total_seconds()

DEFAULT_DELAY_BEFORE_CONTINUE = timedelta(seconds=5).total_seconds()

MAILING_GROUPS_LIST_PAGE_SIZE = 10

LABEL_DISTANCE = 1  # в процентах

LABEL_DISTANCE_RECTANGLE = 120  # в процентах от размера текста
LABEL_FONT = os.path.join("static", "fonts", "arial.ttf")

LABEL_PATH = "static/images/bonus_sticker.png"

PERCENT_TEXT_LEN_LESS_THEN_3_WIDTH_PERCENT = 2.3

PERCENT_TEXT_LEN_3_WIDTH_PERCENT = 3

PERCENT_TEXT_LEN_GREATER_THEN_3_WIDTH_PERCENT = 3.5

PERCENT_TEXT_WIDTH_POSITION = 50

PERCENT_TEXT_HEIGHT_POSITION = 60

LABEL_TEXT_WIDTH_PERCENT = 50

LABEL_TEXT_HEIGHT_PERCENT_OF_PERCENT_TEXT = 2

SINGLE_LINE_LABEL_TEXT_PERCENT = 18

MULTI_LINE_LABEL_TEXT_PERCENT = 12

ONE_DELETING_FRIENDLY_OLD_MESSAGES_ITERATION = 10

FRIENDLY_USERS_CORRESPONDENCE_LIST_PAGE_SIZE = 10

FRIENDLY_USERS_CORRESPONDENCE_TEXT_MESSAGE_SIZE = 10

FRIENDLY_SCHEDULE_LIST_PAGE_SIZE = 10
FRIENDLY_SCHEDULE_COUNT_DAYS_BUTTONS = [1, 2, 7]
FRIENDLY_SCHEDULE_COUNT_OF_TIME_PER_DAY_BUTTONS = [1, 2, 5, 10, 12, 24]
FRIENDLY_SCHEDULE_STOPPED_TIMEOUT = timedelta(hours=24).total_seconds()

FRIENDLY_MESSAGE_LIMIT_BEFORE_RULES_BUTTONS = [0, 2, 5, 10, 15, 20]

POLLS_LIST_PAGE_SIZE = 10

FREQUENCY_OF_SENDING_INTEREST_POSTS = timedelta(seconds=1).total_seconds()
INTERESTS_ITERATION_DELAY = 0.5

VIRTUAL_MANAGER_CHAT_IS_EXPIRED_AFTER = timedelta(seconds=120).total_seconds()

CHECK_OLD_FRIENDLY_MESSAGES_DELAY = timedelta(seconds=15).total_seconds()
TIMEOUT_DELETE_RESTRICTION_MESSAGES = timedelta(seconds=60)
OLD_MESSAGES_QUERY_LIMIT = 50  # for each query in union(total query count * value)
ORDER_REPLY_TIMEOUT_KEYBOARD_VALUES = [1, 2, 3, 5, 10, 15, 20, 30]
UNPROCESSED_ORDERS_CHECK_DELAY = 5

OASIS_API_ID = "must_be_specified"
OASIS_API_HASH = "must_be_specified"
OASIS_SESSION = "payforsay"

# параметры для парсинга
PARSING_COUNT_REQUESTS = 100
PARSING_COUNT_OBJECTS_PAUSE = 500
PARSING_TIMEOUT = timedelta(seconds=1 / 10).total_seconds()
PARSING_TIMEOUT_RECONNECT = timedelta(seconds=1).total_seconds()
STATIC_IMAGES_DIR = "static/static_db/parsers"

DRAW_CLIENT_BOT_LIST_PAGE_SIZE = 10
FRIENDLY_DRAW_LIST_PAGE_SIZE = 10
FRIENDLY_DRAW_WINNERS_LIST_PAGE_SIZE = 10

FRIENDLY_FOOTER_LIST_PAGE_SIZE = 10
FRIENDLY_LIMIT_TOTAL_BUTTONS_COUNT = 100
FRIENDLY_LIMIT_BUTTONS_COUNT_IN_ROW = 8
FRIENDLY_LIMIT_LENGHT_MESSAGE_TEXT = 4096
FRIENDLY_LIMIT_LENGHT_MESSAGE_CAPTION = 1024

CLIENT_BOT_LIST_PAGE_SIZE = 10

APP_NAME = "payforsay"
LOCATION_TIMEOUT = timedelta(seconds=5).total_seconds()

LOCALHOST = "localhost"
DEFAULT_PORT = "8081"

COUNTRY_CURRENCY = {
    "Slovensko": "SVK",
    "Ukraine": "грн.",
}

WEB_APP_DATA_LIFE_TIME = timedelta(days=1)

WEBSERVER_HOST = "localhost"

WEBHOOK_HOST = "localhost"

OPEN_WEBAPP_IMG_LINK_EN = "static/images/OK_eng.jpg"
OPEN_WEBAPP_IMG_LINK_UA = "static/images/OK_ua.jpg"
OPEN_WEBAPP_IMG_LINK_RU = "static/images/OK_ru.jpg"

GET_ORDER_PROVIDER_ID = 10

SECRET_KEY = None  # Make your key with openssl rand -hex 32
TOKEN_VALID_DAYS = 365
TOKEN_VERSION = 1
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRES = timedelta(weeks=1)
USER_CART_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
GROUP_TOKEN_EXPIRES = timedelta(days=365)
MIN_PASSWORD_LENGTH = 8
CONFIRMED_EMAIL_EXPIRES = timedelta(minutes=2)
CONFIRM_EMAIL_TOKEN_EXPIRES = timedelta(days=1)
ANON_CART_TOKEN_EXPIRES = timedelta(days=365)
ANON_ORDER_TOKEN_EXPIRES = timedelta(days=365)
INCUST_PAY_TOKEN_EXPIRES = timedelta(hours=1)
USER_VERIFICATION_TOKEN_EXPIRE = timedelta(minutes=10)
USER_AUTH_TOKEN_EXPIRE = timedelta(minutes=5)

INVOICE_TOKEN_EXPIRES = timedelta(hours=1)

STORE_BRAND_IMPORT_UPDATE_STATUS = timedelta(seconds=30).total_seconds()
STORE_BRAND_STATIC_PATH_FOR_ICONS = "static/store_cf_icons"
STORE_BRAND_STATIC_PATH_FOR_EXCEL = "static/store_xlsx_files"
STORE_BRAND_SHOPS_LIST_PAGE_SIZE = 10
STORE_BRAND_SHOPS_SCHEDULES_LIST_PAGE_SIZE = 10
STORE_BRAND_FILTERS_LIST_PAGE_SIZE = 10

PAYFORSAY_HOST = "payforsay.com"
LOC7_HOST = "7loc.com"
LOC7_IP = "***********"

STORAGE_PAYFORSAY_HOST = f"https://storage.{PAYFORSAY_HOST}"
STORAGE_STATIC_DIR = "static/storage"
STORAGE_LIST_PAGE_SIZE = 10

MENU_IN_STORE_LINK_LIST_PAGE_SIZE = 10
CUSTOM_PAYMENT_LIST_PAGE_SIZE = 10
CUSTOM_SHIPMENT_LIST_PAGE_SIZE = 10
CUSTOM_SHIPMENT_GROUP_LIST_PAGE_SIZE = 10

MANAGER_PROFILE_LIST_PAGE_SIZE = 10

POSTER_REGEX_TO_SKIP_CATEGORY = r'^\*+'

AUTO_ANSWER_CHECK_DELAY = 30

INCUST_CLIENT_DOMAIN = 'https://incust.com'

REDIS_PREFIX = None

LOC7_EMAIL_LOGIN = "<EMAIL>"
LOC7_EMAIL_PASSWORD = "puihmxljyidzzmfx"

TRANSLATION_LOCALISATION_PART = 256
TRANSLATION_LOCALISATION_DELAY = 0

AUTO_TRANSLATION_BACKGROUND_COLOR = {"red": 1.0, "green": 1.0, "blue": 0.0}
AUTO_TRANSLATION_FOREGROUND_COLOR = {"red": 0.0, "green": 0.0, "blue": 1.0}

DEFAULT_SELECT1_BACKGROUND_COLOR = {
    "rgb": {"red": 0.93, "green": 0.93, "blue": 0.93},
    "hex": "EFEFEF",
}
DEFAULT_SELECT2_BACKGROUND_COLOR = {
    "rgb": {"red": 1.0, "green": 1.0, "blue": 1.0},
    "hex": "FFFFFF",
}

LOCALISATION_REDIS_DB = 4
LIMITER_REDIS_DB = 5
KAFKA_REDIS_DB = 6

TEST_IMPORT_BRAND_ID = 10
TEST_IMPORT_EXCEL_FILE_NAME = "test.xlsx"

TRANSLATOR_MONITORING_GROUP_ID = -923708477

IS_LOCALISATION_AUTO_TRANSLATE_ENABLED = False  # it overrides in prod_config.pt

INCUST_EXTERNAL_ID_PREFIX = "7loc-"

INTERVAL_BETWEEN_TRANSLATOR_LIMIT_NOTIFICATIONS = timedelta(hours=1)

DEFAULT_QUANTITATIVE_SERVICES = {
    "t": 100000,
}

TELEGRAM_API_SERVER_BASE = "https://tg-api.payforsay.com"

ISSUE_CERTIFICATE_COMMAND = "echo issuing certificate for domain {domain}"

PAYMENT_METHODS = ['liqpay', 'stripe', 'unipos', 'wave', 'pl24', 'tpay', 'orange',
                   'fondy', 'freedompay', 'flutterwave',
                   'comsa', 'epay', 'directpay', 'momo', 'tj', 'airtel', 'ewallet',
                   'kpay']
ADDITIONAL_PAYMENT_METHODS = ['custom', 'friend', 'cash']
ADDITIONAL_PAYMENT_METHODS_ONLINE = ['incust_pay', 'tg_pay']
ALL_PAYMENT_METHODS = (PAYMENT_METHODS + ADDITIONAL_PAYMENT_METHODS +
                       ADDITIONAL_PAYMENT_METHODS_ONLINE)

# Payment provider that need to set up webhook on merchant profile
PAYMENT_METHODS_WEBHOOK = ['wave', 'flutterwave', 'comsa', 'directpay', 'tj', 'airtel']

HIDDEN_PAYMENTS = ["bonuses"]

PAYMENT_DEFAULT_NAMES = {
    "orange": {
        "text": "Orange"
    },
    "kpay": {
        "text": "KPay"
    },
    "wave": {
        "text": "Wave"
    },
    "directpay": {
        "text": "Directpay"
    },
    "momo": {
        "text": "Momo"
    },
    "tj": {
        "text": "Tj"
    },
    "airtel": {
        "text": "AirTel"
    },
    "flutterwave": {
        "text": "Flutterwave"
    },
    "friend": {
        "var": "store payment friend text"
    },
    "cash": {
        "var": "store payment in store text"
    },
    "tg_pay": {
        "text": "Telegram"
    },
    "incust_pay": {
        "text": "inCust Pay"
    },
    "online_card": {
        "var": "payments card payment method text"
    }
}

QR_PAYMENT_METHODS = ['__unipos__']

PAYMENT_METHOD_CURRENCIES = {
    'unipos': ['UZS'],
    'liqpay': ['UAH', 'USD', 'EUR', 'PLN'],
    'wave': ['XOF'],
    'pl24': ['PLN', 'EUR'],
    'tpay': ['PLN', 'EUR'],
    'orange': ['XOF'],
    'kpay': ['XOF'],
    'freedompay': ['USD', 'EUR', 'KGS'],
    'comsa': ['UZS', 'RUB', 'USD', 'EUR', 'KZT'],
    'tj': ['ZMW', 'ZAR', 'NAD', 'USD', 'SZL', 'LSL', 'BWP']
}

# for open payment checkout page in external browser (app)
EXTERNAL_PAYMENT_METHODS = ['unipos', 'wave', 'orange', 'liqpay', 'stripe', 'fondy',
                            'kpay']

PAYMENT_METHODS_EMAIL_REQUIRED = [
    'pl24',
    # 'stripe',
    'freedompay',
    'flutterwave',
]

# OPENAI_API_KEY = "**************************************5N1GDUsDPGpej"
OPENAI_API_KEY = (
    "sk-proj-oPN2gkg2rqayda-5"
    "-4kd6i7mTFh9i6kiVhZB9iwH"
    "JHtJ2bRJ610wuFhpCKXur8vP8S"
    "gwGq6jwNT3BlbkFJOK78HTrEc1f"
    "CbbabmnC6CbdRgeuDELtmUEg6laf"
    "Q66g5twZLUB9E09PCd0xW7pjyXu5S1V4bUA"
)

INCUST_SUPPORT_LANGUAGES = ["en", "uk", "az", "de", "hy", "pl", "ru", "sk"]

USE_RECOMMENDATIONS_AI = False

API_RESTART = True
PULL_API_COMMAND = None  # will be specified for prod in prod_config
RELOAD_API_COMMAND = None  # will be specified for prod in prod_config
ROOT_BOT_DEPLOY_ADMIN_WEB_COMMAND = None  # will be specified for prod in prod_config
ROOT_BOT_DEPLOY_MY_7LOC_WEB_COMMAND = None  # will be specified for prod in prod_config
ROOT_BOT_DEPLOY_CRM_WEB_COMMAND = None  # will be specified for prod in prod_config

WEB_INDEX_HTML_PATH = None

ALLOWED_IMAGE_EXTENSIONS = [
    "jpg",
    "jpeg",
    "png",
    "svg",
    "heif",
    "webp",
    "gif",
    "heic",
    "heif",
]

ALLOWED_VIDEO_EXTENSIONS = [
    "avi",
    "webm",
    "mp4",
    "mov",
]

GALLERY_ITEM_EXTENSIONS = ALLOWED_IMAGE_EXTENSIONS + ALLOWED_VIDEO_EXTENSIONS

THUMBNAIL_SIZE = 1024

MONITORING_GROUP_CHAT_ID = None
ROOT_BOT_DEPLOY_WEB_COMMAND = None

PL24API = "https://sandbox.przelewy24.pl"
PL24_PMT_PAGE = "https://sandbox.przelewy24.pl/trnRequest"

ANONYMOUS_USER_EMAIL = "<EMAIL>"
ANONYMOUS_USER_NAME = "Anonymous user"

# TPAY_PMT_PAGE = "https://secure.tpay.com"
# TPAY_TRN_REQ = "https://secure.tpay.com/trnRequest"
# TPAY_OPENAPI = "https://api.tpay.com"

TPAY_OPENAPI = "https://openapi.sandbox.tpay.com"
TPAY_PMT_PAGE = "https://secure.sandbox.tpay.com"
TPAY_TRN_REQ = "https://secure.sandbox.tpay.com/trnRequest"

MOMO_API = "https://sandbox.momodeveloper.mtn.com"

ORANGE_API_URL = 'https://api.orange-sonatel.com'
# ORANGE_API_URL = 'https://api.sandbox.orange-sonatel.com'
ORANGE_DIAL_FOR_GET_OTP = '#144#391#'

DEFAULT_FOOTER_SIGN = ("Powered by <a href=\"https://7loc.com\" class=\"theme-link\" "
                       "target=\"_blank\">7Loc</a>")
DEFAULT_TERMS_OF_USE_LINK = "https://7loc.com/{lang}/termsofuse"

DEFAULT_PRIVACY_POLICY_LINK = "https://7loc.com/{lang}/privacy-policy"

RENEW_INCUST_TOKEN_DATETIME = timedelta(days=1)

MESSANGERS_NAMES = {
    "telegram": "Telegram",
    "whatsapp": "WhatsApp",
}

PROCESSES_POOL_SIZE_LIMITS = {
    ROOT_BOT: 20,
    SERVICE_BOT: 5,
    LIP_LEP_BOT: 5,
    PORTER: 10,
    "gunicorn": 20,
    RUN_VM_WORKERS: 2,
    RUN_STRIPE_USAGE_REPORT_WORKER: 1,
    KAFKA_WORKER: 30,
    "default": 10,
}

PROCESSES_MAX_OVERFLOW_LIMITS = {
    ROOT_BOT: 10,
    FRIENDLY_BOT: 50,
    "gunicorn": 50,
    CLIENT_BOT: 20,
    WHATSAPP_BOT: 20,
    PORTER: 20,
    KAFKA_WORKER: 10,
    "default": 5,
}

FASTAPI_RELOAD_EXCLUDES = ["logs", "static", "tests", "venv", "web_app"]

GOOGLE_PLACES_GEOCODING_API_KEY = ""
GOOGLE_MAPS_API_KEY = ""

DATA_PORTER_LIMIT = 2
DELAY_DATA_PORTER = 5

DEBUG = False

MESSANGERS_MEDIA_TYPES = [
    'audio/aac', 'audio/mp4', 'audio/mpeg', 'audio/amr', 'audio/ogg',  # audio
    'text/plain', 'application/pdf', 'application/vnd.ms-powerpoint',
    'application/msword', 'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',  # document
    'image/jpeg', 'image/png',  # image
    'video/mp4', 'video/3gp',  # video
    'image/webp'  # sticker
]

MESSANGERS_MEDIA_SIZE_LIMITS = {
    "audio": 16 * 1024 ** 2,
    "document": 100 * 1024 ** 2,
    "image": 5 * 1024 ** 2,
    "video": 16 * 1024 ** 2,
    "image/webp": 100 * 1024,
}

FAVICON_URL = "https://api.7loc.com/static/images/7loc-api-icon.png"

PLATFORM_MAIN_LANGS = [
    "en",
    "uk",
    "ru",
]
PLATFORM_ADD_GOOGLE_LANGS = True

APPLE_AUTH_KEY_ID = "5TNN2AHG65"
APPLE_AUTH_TEAM_ID = "SDDL925WN7"
APPLE_AUTH_CLIENT_ID = "com.7loc.authid"
APPLE_AUTH_PRIVATE_KEY = '''******************************************************************************************************************************************************************************************************************************************************************'''
APPLE_AUTH_REDIRECT_URL = "https://webapp.7loc.com"
APPLE_AUTH_API_REDIRECT_URL = "https://api.7loc.com/auth/apple_oauth"

DEBUG_INCUST = True

ADDITIONAL_CORS_ORIGINS = ()

SHORT_LINK_START = "1A0"
MAX_SHORT_LINK_ID_LENGTH = 6

SHORT_LINKS_BASE_URL = "https://api.ms.dev.payforsay.com/shortener"

WEB_APP_ADMIN_URL = "https://admin.7loc.com"

PMT_EXCLUDE_METHODS = ['unipos', 'comsa_']

NOT_LANG_FIELDS = ['liqpay_id']

NO_CENT_CURRENCIES = ["XOF", "UZS", "IDR"]

FLUTTERWAVE_API = "https://api.flutterwave.com/v3"
FLUTTERWAVE_API_TEST = "https://ravesandboxapi.flutterwave.com/v3"

TJ_API = "https://pg-api.transactionjunction.com/uat/uat/ipgw/gateway/v1"
TJ_API_TEST = "https://uat-pg-api.transactionjunction.com/uat/ipgw/gateway/v1"

TJ_API_TOKEN = "https://prod-ipgw-oauth.auth.eu-west-1.amazoncognito.com/oauth2/token"
TJ_API_TOKEN_TEST = ("https://uat-ipgw-oauth.auth.eu-west-1.amazoncognito.com/oauth2"
                     "/token")

AIRTEL_API = "https://openapi.airtel.africa"
AIRTEL_API_TEST = "https://openapiuat.airtel.africa"
AIRTEL_PUBLIC_KEY = """MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCkq3XbDI1s8Lu7SpUBP+bqOs
/MC6PKWz6n
/0UkqTiOZqKqaoZClI3BUDTrSIJsrN1Qx7ivBzsaAYfsB0CygSSWay4iyUcnMVEDrNVOJwtWvHxpyWJC5RfKBrweW9b8klFa/CfKRtkK730apy0Kxjg+7fF0tB4O3Ic9Gxuv4pFkbQIDAQAB"""

COMSA_API = "https://baas.comsa.uz"

ANONYMOUS_USER_ID = None  # fill in prod/local configs

INBOX_MAX_AGE = timedelta(weeks=1)

FORBIDDEN_DOMAIN_NAMES = (
    "api",
    "test",
    "testapi",
    "bot",
    "testbot",
    "servicebot",
    "testservicebot",
    "dev",
    "liplep",
)

MAX_POSITION_VALUE = 65500

KAFKA_SERVER = "localhost:9092"

REDIS_HOST = "localhost"
REDIS_PORT = 6379

NEW_ACCESS_TOKEN_EXPIRE = timedelta(minutes=15)
AUTH_SESSION_EXPIRE = timedelta(days=365)

REVIEW_STARS_COUNT = 5
REVIEW_STAR = "⭐️"
REVIEW_EMOJIS = "😍🙂😶😟😡"
REVIEW_NUMBERS_COUNT = 10

IMAGE_PREVIEW_URL = None
VIDEO_PREVIEW_URL = None

LOC7_API_URL = None

HTTP_LOG_KAFKA_TOPIC = "sevenloc-prod-api-http-log"

NOT_FULL_UPD_PAYMENT_METHODS = ['unipos']

TEST_UNIPOS_DESCRIPT = False

DEBUG_WHATSAPP_WEBHOOK = True

WEBHOOK_MAX_RETRIES = 10
WEBHOOK_MAX_RETRIES_FOR_BLOCK = 100

BILLING_STRIPE_PUBLIC_KEY = ""
BILLING_STRIPE_SECRET_KEY = ""
BILLING_STRIPE_WEBHOOK_SIGNATURE = ""
BILLING_STRIPE_INVOICE_TEMPLATE_ID = None
BILLING_STRIPE_FIRST_YEAR_COUPON = None
BILLING_FIRST_YEAR_SALE_PERCENT = None
BILLING_ANNUAL_FREE_MOUNTS_COUNT = 2

PATH_TO_MARKETING_CONSENT_JSON = "config/marketing_consent.json"


class OnTimeoutBehaviour(enum.Enum):
    THROTTLE = "throttle"
    SILENT = "silent"


class NotificationSettings(BaseSettings):
    # Ключ формату "категорія:тип"
    timeouts_per_key: dict[str, timedelta]

    # ключі, за якими наступні сповіщення протягом інтервалу не повинні створюватись
    on_timeout_behaviour: dict[str, OnTimeoutBehaviour]


notification_settings = NotificationSettings(
    timeouts_per_key={
        "payment:make_payment_error": timedelta(minutes=30),
        "payment:liqpay_error": timedelta(minutes=5),
        "webhook:webhook_error": timedelta(minutes=60),
        # дефолтне значення для всіх payment нотифікацій
        "payment": timedelta(minutes=50),
        "billing:quota_exceeded": timedelta(days=1),
        "default": timedelta(minutes=60),
        "billing:transaction_currency_mismatch": timedelta(seconds=0),
        "order:change_status": timedelta(seconds=0),
    },
    on_timeout_behaviour={
        "billing:quota_exceeded": OnTimeoutBehaviour.THROTTLE,
        "default": OnTimeoutBehaviour.SILENT
    }
)

LOG_ERRORS_IN_API_EXCEPTION_HANDLERS = True

DEFAULT_TRIAL_PERIOD_DAYS = 14

BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID = None
BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_TIMEZONE = "Europe/Berlin"

MAKE_HOOK_DOMAIN = "make.com"
MAKE_HOOK_ENTITY_BY_NAME = True
ZAPIER_HOOK_DOMAIN = "zapier.com"
ZAPIER_HOOK_ENTITY_BY_NAME = True

GENERATED_PROMO_CODE_LENGTH = 6
MAX_GENERATE_PROMO_CODE_TRIES = 100

MAX_ALLOWED_PRICE = 9223372036854775807

KPAY_SERVICE_API = "https://transfert-test.kpay-api.com/v1"

KPAY_MERCHANT_API = "https://encaissements-test.kpay-api.com/v1"

KPAY_APP_TEST_LINK = "https://play.google.com/apps/testing/sn.kpay"

KPAY_APP_LINK = "https://play.google.com/store/apps/details?id=sn.kpay"

# Налаштування для тестування зовнішнього логіну
TEST_EXTERNAL_LOGIN_BOT_ID = 151  # ID бота @TopBurgerBot для продакшену

REPORTS_LIMIT_FOR_EXPORT = 1000000

BILLING_TIMEZONE = "Europe/Bratislava"
