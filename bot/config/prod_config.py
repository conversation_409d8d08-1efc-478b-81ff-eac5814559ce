# system
PYTHON = "/root/Env/payforsay/bin/python"

# Токен бота
SERVICE_BOT_API_TOKEN = "1290023075:AAFhQ5x-gwMwXrDiUYfRecLzVR9Y2tgLWhw"
ROOT_BOT_API_TOKEN = "1799819104:AAEd3z383pMAatmmqeIEOtQRM0J9CHjMk_o"
BUTTONS_TESTER_BOT_API_TOKEN = "6336941272:AAG7mN1vSnxsj1sDeDCu8boa1glzULNY-eU"
# username ботов
SERVICE_BOT_USERNAME = "PayForSayServiceBot"
ROOT_BOT_USERNAME = "PayForSayRootBot"
DEFAULT_PAY4SAY_BOT_USERNAME = "PayForSayBot"
# Данные для подключения к бд
HOST = "localhost"
USER = "payforsay"
PASSWD = "PaY4SaY"
DATABASE = "payforsay"
# пароль для супер бота
SUPER_BOT_PASSWORD = "sayforpay261"

PLATFORM_ADMINS = {
    *********: True,  # dev Max-semka
    315373186: True,  # dev Ruslan
    394456075: False,  # Guy Secret
    397122680: False,  # Maxim Ronshin
    327721042: True,  # dev Eugene
    190722186: True,  # dev Vik
}

P4S_API_URL = "https://api.payforsay.com"
LOC7_API_URL = "https://api.7loc.com"

TIME_SLEEP_BETWEEN_STARTING_BOTS = 0

# API мониторинг клиента

session = "dev_pysariev"
api_id = 7752172
api_hash = "f605d66c8313e25b2a4b4cb223557768"

RUN_MONITORING = False

ROOT_BOT_PORT = 1909

DB_URL = f"mysql+pymysql://{USER}:{PASSWD}@{HOST}/{DATABASE}"

phone = "+************"

# переопределённые переменные

REPEAT_SCROLL_COUNT = 0

OASIS_SESSION = "oasis/account"
OASIS_API_ID = "********"
OASIS_API_HASH = "bdbda6812e001e64e99d188102a2b577"

WEB_APP_HOST = "https://webapp.7loc.com"
WEB_APP_PATH = WEB_APP_HOST

ADMIN_HOST = "https://admin.7loc.com"
CRM_HOST = "https://crm.7loc.com"
MY_7LOC_HOST = "https://my.7loc.com"

LIP_LEP_API_HOST = "http://localhost"
LIP_LEP_API_PORT = 1916

LIP_LEP_BOT_URL = "http://127.0.0.1:1915"
LIP_LEP_WEB_APP_PATH = "https://app.liplep.com"

# don't run fastapi reload server on prod
FASTAPI_RELOAD = False
FASTAPI_DEBUG = False

TESTER_USERS = {
    "1": {
        "query_id": "1",
        "user": {
            "id": *********, "first_name": "Максим🇺🇦",
            "last_name": "Скуйбіда", "username": "Semka_96",
            "language_code": "uk",
        },
    },
}

WHATSAPP_BOT_TOKEN = "****************|gUu-7O2fDn9eQxhYcNWJipj4pa0"
WHATSAPP_BOT_FROM = "***************"
WHATSAPP_APP_ID = "****************"
WHATSAPP_APP_SECRET = "aabf79b85109a9d00c9c4fb3ce391566"

RUN_WHATSAPP = True

CROCKFORD_SECRET = 274254619704377095712331706622007933481
CROCKFORD_SHIFT = 55

WHATSAPP_WEBHOOK_URL = "https://whatsapp.payforsay.com/whatsapp/{app_id}/"
WHATSAPP_WEBHOOK_URL_TOKEN = "payforsay"

IS_LOCALISATION_AUTO_TRANSLATE_ENABLED = True

INCUST_EXTERNAL_SYSTEM_ID = "7loc"

ISSUE_CERTIFICATE_COMMAND = "/usr/local/sbin/issue-cert-local {domain}"

DEFAULT_TIMEZONE = "Europe/Kiev"

USE_RECOMMENDATIONS_AI = False

INCUST_SERVER_API = 'https://api.incust.com'
INCUST_CLIENT_URL = 'https://my.incust.com'

API_RESTART = False
RELOAD_API_COMMAND = "superbot apireload"
PULL_API_COMMAND = "git pull"

WEB_INDEX_HTML_PATH = "/var/www/payforsay_web/build/index.html"

MONITORING_GROUP_CHAT_ID = -4069853257

ROOT_BOT_DEPLOY_WEB_COMMAND = "p4s_client_web prod"
ROOT_BOT_DEPLOY_ADMIN_WEB_COMMAND = "p4s_admin_web prod"
ROOT_BOT_DEPLOY_MY_7LOC_WEB_COMMAND = "p4s_my_web prod"
ROOT_BOT_DEPLOY_CRM_WEB_COMMAND = "p4s_crm_web prod"

# PL24API = "https://sandbox.przelewy24.pl"
# PL24_PMT_PAGE = "https://sandbox.przelewy24.pl/trnRequest"
PL24API = "https://secure.przelewy24.pl"
PL24_PMT_PAGE = "https://secure.przelewy24.pl/trnRequest"

# TPAY_PMT_PAGE = "https://secure.tpay.com"
# TPAY_TRN_REQ = "https://secure.tpay.com/trnRequest"
# TPAY_OPENAPI = "https://api.tpay.com"
TPAY_OPENAPI = "https://openapi.sandbox.tpay.com"

TPAY_PMT_PAGE = "https://secure.sandbox.tpay.com"
TPAY_TRN_REQ = "https://secure.sandbox.tpay.com/trnRequest"

SECRET_KEY = "ebffc4a4052498416bb1dbce7b51440c14e1bc747e8dce6d739b1e0cbd87c5ef"

SYSTEM_NAME = "7loc_prod"

GOOGLE_PLACES_GEOCODING_API_KEY = "AIzaSyDnl0y2fEN1xSnSL1BUgA3mrhRepfKUEWQ"
GOOGLE_MAPS_API_KEY = "AIzaSyDE7KOHs6GCsHyZ1o4qI3TgdAtk_ln4GdI"

APPLE_AUTH_KEY_ID = "5TNN2AHG65"
APPLE_AUTH_TEAM_ID = "SDDL925WN7"
# APPLE_AUTH_CLIENT_ID = "com.7loc.authid"
APPLE_AUTH_CLIENT_ID = "com.7loc.auth.client"
APPLE_AUTH_PRIVATE_KEY = '''******************************************************************************************************************************************************************************************************************************************************************'''

DEBUG = False

SHORT_LINKS_BASE_URL = "https://7lc.co"

IMAGE_PREVIEW_URL = ("https://api.7loc.com/resize/{file_dir}/{max_size}x{max_size}/{"
                     "file_name}")
VIDEO_PREVIEW_URL = ("https://api.7loc.com/video-thumbnail/{max_size}/path/{"
                     "file_path}/thumbnail.jpg")

ANONYMOUS_USER_ID = 1117595

LOG_ERRORS_IN_API_EXCEPTION_HANDLERS = False

BILLING_STRIPE_PUBLIC_KEY = \
    "pk_live_51NafyxFnZdSOgykShaI4DALvRQhRuit25scsQgSWcAaDD4hgEPTJQcYdSQYFUNADZC5YFCJD0xo6KVISCjHrGu7z00Z1DCIlsf"
BILLING_STRIPE_SECRET_KEY = \
    "***********************************************************************************************************"
BILLING_STRIPE_WEBHOOK_SIGNATURE = "whsec_hblbDeBQtuvGBDtfLJfTTPyq4nxJH90v"
BILLING_STRIPE_INVOICE_TEMPLATE_ID = "inrtem_1QU6w3FnZdSOgykSDpQvD5H9"
BILLING_STRIPE_FIRST_YEAR_COUPON = "first-year-sale"
BILLING_FIRST_YEAR_SALE_PERCENT = 60

BILLING_SUBSCRIPTIONS_NOTIFICATIONS_GROUP_ID = -4752449907
