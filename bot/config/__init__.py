from itsdangerous import URLSafeTimedSerializer

from .config import *

try:
    from .local_config import *
except:
    from .prod_config import *

STATIC_URL = f"{P4S_API_URL}/{STATIC_DB}"

PMT_SERVER_CALLBACK_URL = f'{P4S_API_URL}/payments/callback'
PMT_CLIENT_REDIRECT_CALLBACK_URL = f'{P4S_API_URL}/payments/client_redirect'

if not SECRET_KEY:
    logging.error(
        "SECRET_KEY was removed from config.py. "
        "Please, create your own secret key with openssl rand -hex 32 "
        "and set it in local_config.py to variable SECRET_KEY"
    )
    exit(1)

TOKEN_SERIALIZER = URLSafeTimedSerializer(SECRET_KEY)

if LOC7_API_URL is None:
    LOC7_API_URL = P4S_API_URL
