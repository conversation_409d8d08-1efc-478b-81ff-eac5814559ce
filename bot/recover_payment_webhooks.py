#!/usr/bin/env python3

import asyncio
import hashlib
import hmac
import json
import os
import sys
import time
from datetime import datetime
from typing import Any, Dict, List

# Встановлюємо робочу директорію
os.chdir('/home/<USER>/pay4say/bot')
sys.path.insert(0, os.getcwd())

from db import DBSession, db_func
from db.models import WebhookJournal, InvoiceTemplate
from sqlalchemy import text


class PaymentWebhookRecovery:
    def __init__(self):
        self.kpay_webhook_url = "https://kpay-api.7loc.com/webhooks/7loc-invoice"
        self.webhook_secret = "a2f117df22804e9f8d79c0a4670c1e32"
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.skipped_count = 0
        self.skipped_payments = []
        
    def make_webhook_signature(self, secret: str, payload: str) -> str:
        """Генерує webhook сігнатуру"""
        timestamp = str(time.time())
        message_with_timestamp = timestamp + str(payload)
        signature = hmac.new(
            secret.encode(),
            msg=message_with_timestamp.encode(),
            digestmod=hashlib.sha256
        ).hexdigest()
        return f"t={timestamp},s={signature}"
    
    def should_skip_payment(self, payment_data: Dict[str, Any], webhook: WebhookJournal) -> tuple[bool, str]:
        """Перевіряє чи потрібно пропустити платіж"""
        # Перевіряємо чи є invoice_template_id
        template_id = payment_data.get('invoice_template_id')
        if not template_id:
            return True, "No invoice template"
        
        # Перевіряємо чи шаблон має extra_params
        template_title = getattr(webhook, '_template_title', 'N/A')
        if template_title == 'N/A':
            return True, "Template not found"
            
        # Перевіряємо чи платіж має extra_params
        extra_params = payment_data.get('extra_params')
        if not extra_params or extra_params == {}:
            return True, "No extra params"
        
        # Пропускаємо тестових користувачів
        user_info = payment_data.get('user', {}).get('info', {})
        user_name = user_info.get('name', '')
        test_users = ['Andrii Petlovany', 'Andrii Petlovanyi', 'Vik', 'Madauky Sow', 'Maksym Skuibida', 'Marie']
        
        if user_name in test_users:
            return True, "Test user"
            
        return False, ""
    
    def convert_notification_to_payment_webhook(self, json_data) -> Dict[str, Any]:
        """Конвертує JSON вебхука сповіщення в JSON для вебхука оплати"""
        # Беремо дані з notification webhook
        original_json = json_data.request.data['json']
        
        # Формуємо payload для payment webhook (той самий формат)
        payment_payload = {
            "entity_id": original_json['entity_id'],
            "action": original_json['action'],
            "event_id": original_json['event_id'],
            "profile_id": original_json['profile_id'],
            "profile_name": original_json['profile_name'],
            "profile_lang": original_json['profile_lang'],
            "entity": original_json['entity'],
            "data": original_json['data']
        }
        
        return payment_payload
    
    async def send_webhook_to_kpay(self, payload: Dict[str, Any], event_uuid: str, webhook: WebhookJournal) -> bool:
        """Відправляє вебхук на kpay-api"""
        try:
            # Конвертуємо в JSON
            payload_json = json.dumps(payload, indent=2)
            
            # Генеруємо сігнатуру з секретним ключем
            signature = self.make_webhook_signature(self.webhook_secret, payload_json)
            
            # Заголовки
            headers = {
                "X-Webhook-Event-Id": event_uuid,
                "X-Webhook-Signature": signature,
                "Content-Type": "application/json"
            }
            
            # Записуємо основні дані для перевірки
            payment_data = payload['data']
            user_info = payment_data.get('user', {}).get('info', {})
            
            # Беремо назву сервісу з title (там SenEau, Woyofal тощо)
            service_name = payment_data.get('title', 'N/A')
            
            # Шукаємо Company в custom_fields
            company = "N/A"
            user_data = payment_data.get('user', {})
            custom_fields = user_data.get('custom_fields', [])
            for field in custom_fields:
                if field.get('name') == 'Company':
                    company = field.get('value', 'N/A')
                    break
            
            amount = payment_data.get('sum_to_pay', payment_data.get('total_sum', 0))
            
            # Форматуємо дату без мілісекунд
            paid_datetime = payment_data.get('paid_datetime', 'N/A')
            if paid_datetime != 'N/A':
                # Обрізаємо мілісекунди
                paid_datetime = paid_datetime.split('.')[0]
            
            # Отримуємо extra_params інформацію для CSV
            extra_params = payment_data.get('extra_params', {})
            extra_info = ""
            if extra_params:
                # Беремо перший ключ з extra_params та його значення
                for key, values in extra_params.items():
                    if isinstance(values, dict):
                        extra_info = f"{key}: {', '.join([f'{k}={v}' for k, v in values.items()])}"
                    else:
                        extra_info = f"{key}: {values}"
                    break
            
            # Отримуємо назву шаблону та ID
            template_title = getattr(webhook, '_template_title', 'N/A')
            template_id = payment_data.get('invoice_template_id', 'N/A')
            
            # Перевіряємо чи існує вебхук оплати для CSV
            payment_id = payload['data']['id']
            has_payment_webhook = await self.check_payment_webhook_exists(payment_id)
            payment_webhook_status = "Yes" if has_payment_webhook else "No"
            
            # CSV формат для Excel
            csv_line = f'''"{paid_datetime}","{user_info.get('name', 'N/A')}","{user_info.get('wa_phone', 'N/A')}","{company}","{service_name}","{template_title}",{template_id},{amount},"{payment_data.get('currency', 'N/A')}",{payment_data['id']},"{extra_info}","{payment_webhook_status}"\n'''
            
            with open("webhook_recovery_log.csv", "a", encoding="utf-8") as f:
                f.write(csv_line)
            
            # ЗАКОМЕНТОВАНИЙ ЗАПИТ - для тестування
            # timeout = ClientTimeout(total=30)
            # async with ClientSession(timeout=timeout) as session:
            #     async with session.post(
            #         self.kpay_webhook_url,
            #         json=payload,
            #         headers=headers
            #     ) as resp:
            #         response_text = await resp.text()
            #         
            #         if resp.status >= 400:
            #             print(f"❌ Error {resp.status}: {response_text}")
            #             return False
            #         else:
            #             print(f"✅ Success {resp.status}: {response_text}")
            #             return True
            return True
                        
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
            return False
    
    @db_func
    def get_notification_webhooks(self) -> List[WebhookJournal]:
        """Отримує вебхуки сповіщень з БД"""
        with DBSession() as sess:
            # Запит з фільтрацією по URL в SQL
            start_date = datetime(2025, 5, 26, 8, 28, 54, 544158)
            end_date = datetime(2025, 7, 12, 0, 0, 0, 0)
            
            # Використовуємо SQL з JSON фільтрацією - всі вебхуки сповіщень для профілю 7loc Credit
            sql = text("""
                SELECT * FROM webhook_journals 
                WHERE 1=1 
                    /*and event_created_datetime > :start_date
                    AND event_created_datetime <= :end_date*/
                    AND entity = 'PAYMENT'
                    AND action = 'PAID'
                    AND JSON_EXTRACT(json_data, '$.request.data.json.profile_name') = '7loc Credit'
                    AND JSON_EXTRACT(json_data, '$.request.data.url') LIKE '%sendpulsehook.7loc.com/sevenloccredit%'
                ORDER BY event_created_datetime ASC
            """)
            
            result = sess.execute(sql, {'start_date': start_date, 'end_date': end_date})
            webhook_rows = result.fetchall()
            
            # Конвертуємо результати в об'єкти WebhookJournal та додаємо template інформацію
            webhooks = []
            for row in webhook_rows:
                webhook = sess.get(WebhookJournal, row.id)
                if webhook:
                    # Додаємо інформацію про шаблон рахунку
                    try:
                        json_data = webhook.json_data
                        if json_data and json_data.request and json_data.request.data:
                            payment_data = json_data.request.data['json']['data']
                            template_id = payment_data.get('invoice_template_id')
                            if template_id:
                                template = sess.get(InvoiceTemplate, template_id)
                                if template:
                                    # Додаємо template як атрибут для використання пізніше
                                    webhook._template_title = template.title
                                else:
                                    webhook._template_title = "N/A"
                            else:
                                webhook._template_title = "N/A"
                    except:
                        webhook._template_title = "N/A"
                    
                    webhooks.append(webhook)
            
            print(f"📋 Знайдено {len(webhooks)} записів з правильним URL")
            
            return webhooks
    
    @db_func
    def check_payment_webhook_exists(self, entity_id: int) -> bool:
        """Перевіряє чи існує вебхук оплати для даного платежу"""
        with DBSession() as sess:
            sql = text("""
                SELECT COUNT(*) as count
                FROM webhook_journals 
                WHERE entity_id = :entity_id
                  AND entity = 'PAYMENT'
                  AND action = 'PAID'
                  AND JSON_EXTRACT(json_data, '$.request.data.url') LIKE '%kpay-api.7loc.com/webhooks/7loc-invoice%'
            """)
            result = sess.execute(sql, {'entity_id': entity_id})
            count = result.scalar()
            return count > 0

    async def process_single_webhook(self, webhook: WebhookJournal) -> bool:
        """Обробляє один вебхук"""
        try:
            json_data = webhook.json_data
            
            # Перевіряємо структуру JSON
            if not json_data or not json_data.request or not json_data.request.data:
                print(f"⚠️  Пропускаємо webhook ID {webhook.id}: неправильна структура JSON")
                return True
            
            # Отримуємо дані платежу
            payment_data = json_data.request.data['json']['data']
            
            # Перевіряємо чи є основні поля
            if not payment_data or 'id' not in payment_data:
                print(f"⚠️  Пропускаємо webhook ID {webhook.id}: немає ID платежу в JSON")
                return True
            
            # Перевіряємо чи потрібно пропустити
            should_skip, skip_reason = self.should_skip_payment(payment_data, webhook)
            if should_skip:
                self.skipped_count += 1
                user_info = payment_data.get('user', {}).get('info', {})
                
                skipped_info = {
                    'payment_id': payment_data['id'],
                    'title': payment_data.get('title', 'N/A'),
                    'amount': payment_data.get('sum_to_pay', 0),
                    'currency': payment_data.get('currency', 'N/A'),
                    'user': user_info.get('name', 'N/A'),
                    'phone': user_info.get('wa_phone', 'N/A'),
                    'date': payment_data.get('paid_datetime', 'N/A'),
                    'skip_reason': skip_reason
                }
                self.skipped_payments.append(skipped_info)
                print(f"⏭️  Пропускаємо платіж ID {payment_data['id']}: {payment_data.get('title', '')} (Причина: {skip_reason})")
                return True
            
            # Перевіряємо чи існує вебхук оплати
            payment_id = payment_data['id']
            has_payment_webhook = await self.check_payment_webhook_exists(payment_id)
            
            # Конвертуємо в payload для kpay
            payment_payload = self.convert_notification_to_payment_webhook(json_data)
            
            # Отримуємо event_uuid
            event_uuid = json_data.request.data['json']['event_id']
            
            # Відправляємо
            # Виводимо те саме що і в лог
            service_name = payment_data.get('title', 'N/A')
            amount = payment_data.get('sum_to_pay', payment_data.get('total_sum', 0))
            
            # Отримуємо extra_params інформацію
            extra_params = payment_data.get('extra_params', {})
            extra_info = ""
            if extra_params:
                # Беремо перший ключ з extra_params та його значення
                for key, values in extra_params.items():
                    if isinstance(values, dict):
                        extra_info = f"{key}: {', '.join([f'{k}={v}' for k, v in values.items()])}"
                    else:
                        extra_info = f"{key}: {values}"
                    break
            
            # Отримуємо назву шаблону та ID
            template_title = getattr(webhook, '_template_title', 'N/A')
            template_id = payment_data.get('invoice_template_id', 'N/A')
            
            # Статус вебхука оплати
            payment_webhook_status = "✅ Є" if has_payment_webhook else "❌ Немає"
            
            print(f"📤 Відправляємо платіж ID {payment_data['id']}: {service_name} ({template_title} #{template_id}) - {amount} {payment_data.get('currency', 'N/A')} | {extra_info} | Payment webhook: {payment_webhook_status}")
            success = await self.send_webhook_to_kpay(payment_payload, event_uuid, webhook)
            
            if success:
                self.success_count += 1
            else:
                self.error_count += 1
                
            return success
            
        except Exception as e:
            print(f"❌ Помилка обробки вебхука: {str(e)}")
            self.error_count += 1
            return False
    
    async def run_recovery(self):
        """Головна функція відновлення вебхуків оплати"""
        print("🚀 Розпочинаємо відновлення вебхуків оплати...")
        
        # Створюємо CSV файл з заголовками
        with open("webhook_recovery_log.csv", "w", encoding="utf-8") as f:
            f.write("Date,User,Phone,Company,Service,Template,Template ID,Amount,Currency,Payment ID,Extra Params,Has Payment Webhook\n")
        
        # Отримуємо список вебхуків сповіщень
        print("📋 Отримуємо список вебхуків сповіщень...")
        webhooks = await self.get_notification_webhooks()
        
        print(f"📊 Знайдено {len(webhooks)} вебхуків сповіщень")
        
        # Обробляємо кожен вебхук
        for i, webhook in enumerate(webhooks, 1):
            print(f"\n📍 Обробляємо {i}/{len(webhooks)} (ID: {webhook.id})")
            
            await self.process_single_webhook(webhook)
            self.processed_count += 1
            
            # Невелика затримка між запитами
            await asyncio.sleep(0.5)
        
        # Підсумок
        print(f"\n🎯 Підсумок відновлення:")
        print(f"   📊 Оброблено: {self.processed_count}")
        print(f"   ✅ Успішно: {self.success_count}")
        print(f"   ❌ Помилок: {self.error_count}")
        print(f"   ⏭️  Пропущено: {self.skipped_count}")
        
        success_rate = (self.success_count / self.processed_count * 100) if self.processed_count > 0 else 0
        print(f"   📈 Успішність: {success_rate:.1f}%")
        
        # Список пропущених платежів
        if self.skipped_payments:
            print(f"\n📋 Пропущені платежі:")
            with open("skipped_payments.txt", "w", encoding="utf-8") as f:
                f.write("Пропущені платежі:\n")
                f.write("=" * 50 + "\n")
                for payment in self.skipped_payments:
                    line = (f"ID: {payment['payment_id']} | "
                           f"Title: {payment['title']} | "
                           f"Amount: {payment['amount']} {payment['currency']} | "
                           f"User: {payment['user']} ({payment['phone']}) | "
                           f"Date: {payment['date']} | "
                           f"Reason: {payment['skip_reason']}\n")
                    f.write(line)
                    print(f"   - {line.strip()}")
            print(f"   💾 Збережено в skipped_payments.txt")


async def main():
    """Головна функція"""

    recovery = PaymentWebhookRecovery()
    await recovery.run_recovery()


if __name__ == "__main__":
    asyncio.run(main())