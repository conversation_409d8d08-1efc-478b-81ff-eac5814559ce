from db.connection import Base
from db.mixins import BaseDBModel
from sqlalchemy import Column, String, DateTime
from datetime import datetime

from db import sess, db_func


class ExternalSystemToken(Base, BaseDBModel):
    external_system_type = Column(String(20), nullable=False)
    expire = Column(DateTime, nullable=True, default=None)

    @classmethod
    @db_func
    def create(cls, external_system_type: str, expire: datetime | None = None):
        system = cls(external_system_type=external_system_type, expire=expire)
        sess().add(system)
        sess().commit()

        return system

    @classmethod
    async def create_or_update(cls, external_system_type: str, expire: datetime | None = None):
        system = await cls.get(external_system_type=external_system_type)
        if system:
            return await system.update(
                external_system_type=external_system_type,
                expire=expire,
            )

        system = await cls.create(external_system_type, expire)
        return system


