from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Enum, String
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import BillingProductCode


class BillingProduct(Base, BaseDBModel, TimeCreatedMixin):
    id: int = Column(SMALLINT, autoincrement=True, primary_key=True)
    is_deleted: bool = Column(Boolean, default=False)

    position: int = Column(SMALLINT(unsigned=True), nullable=False, default=0)

    code: BillingProductCode = Column(
        Enum(BillingProductCode),
        nullable=False, unique=True
    )

    name: str = Column(String(128), nullable=False)
    description: str = Column(String(1024), nullable=False)
    tax_code: str = Column(String(13), nullable=False)

    packets_items: list["models.BillingServicePacketItem"] = relationship(
        "BillingServicePacketItem", back_populates="product"
    )
    subscription_items: list["models.BillingSubscriptionItem"] = relationship(
        "BillingSubscriptionItem", back_populates="product"
    )

    stripe_id: str = Column(String(255), nullable=False, unique=True)
