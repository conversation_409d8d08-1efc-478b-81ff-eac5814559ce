from datetime import datetime

from sqlalchemy import (
    BigInteger, Column, DateTime, ForeignKey, SMALLINT,
    UniqueConstraint,
)
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class BillingUsageRecord(Base, BaseDBModel, TimeCreatedMixin):
    group_id: int = Column(
        BigInteger,
        ForeignKey("groups.id", ondelete="RESTRICT"),
        nullable=False,
    )
    group: "models.Group" = relationship("Group", backref="usage_records")

    product_id: int = Column(
        SMALLINT,
        ForeignKey("billing_products.id", ondelete="RESTRICT"),
        nullable=False,
    )
    product: "models.BillingProduct" = relationship(
        "BillingProduct",
        backref="usage_records",
    )

    used_quantity: int = Column(
        BIGINT(unsigned=True),
        nullable=False, default=0,
    )
    reported_quantity: int = Column(
        BIGINT(unsigned=True),
        nullable=False, default=0,
    )

    last_record_datetime: datetime | None = Column(DateTime, nullable=True)
    last_report_datetime: datetime | None = Column(DateTime, nullable=True)

    __table_args__ = (
        UniqueConstraint("group_id", "product_id"),
    )
