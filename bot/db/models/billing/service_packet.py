from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Enum, String
from sqlalchemy.dialects.mysql import INTEGER, SMALLINT
from sqlalchemy.orm import relationship
from sqlalchemy_json import MutableJson, NestedMutableJson

import schemas
from config import DEFAULT_LANG
from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import ServicePacketCountryMode


class BillingServicePacket(Base, BaseDBModel, TimeCreatedMixin):
    id: int = Column(SMALLINT, autoincrement=True, primary_key=True)
    is_deleted: bool = Column(Boolean, default=False)
    position: int = Column(SMALLINT(unsigned=True), nullable=False)

    name: str = Column(String(128), nullable=False)
    subtitle: str = Column(String(128), nullable=False)
    description: str = Column(String(1024), nullable=False)

    recurring_interval: schemas.BillingRecurringInterval = (
        Column(Enum(schemas.BillingRecurringInterval))
    )
    recurring_interval_count: int = Column(SMALLINT(unsigned=True), default=1)

    country_mode: ServicePacketCountryMode = Column(
        Enum(
            ServicePacketCountryMode
        ),
        nullable=False,
    )
    # if country_mode is SPECIFIC
    countries: list[str] | None = Column(MutableJson, nullable=True)

    currency: str = Column(String(3), nullable=False)

    is_public: bool = Column(Boolean, nullable=False, default=False)

    items: list["models.BillingServicePacketItem"] = relationship(
        "BillingServicePacketItem",
        back_populates="packet",
    )

    billing_amount_threshold: int = Column(
        INTEGER(unsigned=True),
        nullable=True,
        default=None,
    )

    # Determines if the packet is a trial plan.
    # Only one trial plan can be at the same time per interval per country
    # It is the plan, selected when a user presses try for free button
    trial_allowed: bool = Column(Boolean, nullable=False, default=False)

    # Determines if the packet is a free plan.
    # If column value is true, user won't have a subscription form
    is_free_plan: bool = Column(Boolean, nullable=False, default=False)

    lang: str = Column(String(2), nullable=False, default=DEFAULT_LANG)
    _langs_list: list[str] | None = Column(NestedMutableJson, default=None)

    @property
    def langs_list(self):
        if not isinstance(self._langs_list, list):
            return []

        return [lang for lang in self._langs_list if lang != self.lang]

    @langs_list.setter
    def langs_list(self, value: list[str]):
        self._langs_list = value
