from typing import Optional

from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.mysql import SM<PERSON><PERSON>INT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import TimeCreatedMixin
from db.mixins.base_model import BaseDBModel


class BillingPromoCode(Base, BaseDBModel, TimeCreatedMixin):
    id: int = Column(Integer, autoincrement=True, primary_key=True)
    name: str = Column(String(255), nullable=False)
    code: str = Column(String(24), nullable=False, unique=True)

    trial_period_days: int = Column(SMALLINT(unsigned=True), nullable=False, default=0)
    stripe_coupon: str | None = Column(String(255), nullable=True)

    packet_id: int | None = Column(
        SMALLINT,
        ForeignKey(
            "billing_service_packets.id",
            ondelete="RESTRICT"
        ),
        nullable=True,
    )
    packet: Optional["models.BillingServicePacket"] = relationship(
        "BillingServicePacket", backref="promo_codes"
    )
