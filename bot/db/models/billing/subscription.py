from datetime import datetime
from typing import Type

from sqlalchemy import (
    BigI<PERSON><PERSON>, <PERSON>ole<PERSON>, Column, DateTime, Enum, Foreign<PERSON>ey, Integer,
    String,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

import schemas
from db import models
from db.connection import Base
from db.custom_column_types.pydantic_json import <PERSON>ydanticJSON
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import BillingSubscriptionStatus


class BillingSubscription(Base, BaseDBModel, TimeCreatedMixin):
    id: int = Column(Integer, autoincrement=True, primary_key=True)

    group_id: int = Column(
        BigInteger,
        ForeignKey(
            "groups.id",
            ondelete="RESTRICT",
        ),
        nullable=False,
    )
    group: "models.Group" = relationship(
        "Group",
        backref="subscriptions",
    )

    currency: str = Column(String(3), nullable=False)
    status: schemas.BillingSubscriptionStatus = Column(
        Enum(schemas.BillingSubscriptionStatus),
        nullable=False
    )

    description: str | None = Column(String(512), nullable=True)

    stripe_id: str = Column(String(255), nullable=False, unique=True)

    cancel_at: datetime | None = Column(DateTime(timezone=True), nullable=True)
    cancel_at_period_end: bool = Column(Boolean)
    canceled_at: datetime | None = Column(DateTime(timezone=True), nullable=True)
    cancellation_details: schemas.BillingSubscriptionCancellationDetails | None = (
        Column(
            PydanticJSON(schemas.BillingSubscriptionCancellationDetails), nullable=True
        )
    )

    trial_start: datetime | None = Column(DateTime(timezone=True), nullable=True)
    trial_end: datetime | None = Column(DateTime(timezone=True), nullable=True)

    items: list["models.BillingSubscriptionItem"] = relationship(
        "BillingSubscriptionItem",
        back_populates="subscription",
    )

    stripe_created: datetime = Column(DateTime, nullable=False)
    start_date: datetime = Column(DateTime, nullable=False)

    current_period_start: datetime = Column(DateTime, nullable=False)
    current_period_end: datetime = Column(DateTime, nullable=False)

    ended_at: datetime | None = Column(DateTime, nullable=True)

    livemode: bool = Column(Boolean, nullable=False)

    ACTIVE_STATUSES = (
        BillingSubscriptionStatus.ACTIVE,
        BillingSubscriptionStatus.TRIALING,
        BillingSubscriptionStatus.PAST_DUE,
    )

    @hybrid_property
    def is_active(self):
        return self.status in self.ACTIVE_STATUSES

    # noinspection PyMethodParameters
    @is_active.expression
    def is_active(cls: Type["BillingSubscription"]):
        return cls.status.in_(cls.ACTIVE_STATUSES)
