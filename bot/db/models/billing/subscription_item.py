from decimal import Decimal

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, Numeric, String
from sqlalchemy.dialects.mysql import INTEGER, SMALLINT
from sqlalchemy.orm import relationship

import schemas
from db import models
from db.connection import Base
from db.custom_column_types.pydantic_json import <PERSON>ydanticJSON, PydanticListJSON
from db.mixins import BaseDBModel, TimeCreatedMixin


class BillingSubscriptionItem(Base, BaseDBModel, TimeCreatedMixin):
    id: int = Column(Integer, autoincrement=True, primary_key=True)
    is_deleted: bool = Column(Boolean, default=False)

    subscription_id: int = Column(
        Integer, ForeignKey("billing_subscriptions.id"),
        nullable=False
    )
    subscription: "models.BillingSubscription" = relationship(
        "BillingSubscription", back_populates="items"
    )

    product_id: int = Column(
        SMALLINT,
        ForeignKey("billing_products.id"),
        nullable=False
    )
    product: "models.BillingProduct" = relationship(
        "BillingProduct",
        back_populates="subscription_items"
    )

    packet_item_id: int = Column(
        SMALLINT,
        ForeignKey(
            "billing_service_packet_items.id",
            ondelete="RESTRICT",
        ),
        nullable=False
    )
    packet_item: "models.BillingServicePacketItem" = relationship(
        "BillingServicePacketItem",
        backref="subscription_items"
    )

    # next quantity fields only used when price.usage_tipe is licensed
    quantity: int = Column(INTEGER(unsigned=True), nullable=False, default=0)

    billing_scheme: schemas.BillingScheme = Column(
        Enum(schemas.BillingScheme), nullable=False,
        default=schemas.BillingScheme.PER_UNIT
    )
    tiers_mode: schemas.BillingTiersMode | None = Column(
        Enum(schemas.BillingTiersMode), nullable=True,
    )
    tiers: list[schemas.BillingTierSchema] | None = Column(
        PydanticListJSON(schemas.BillingTierSchema),
        nullable=True
    )

    unit_amount: Decimal = Column(Numeric(precision=24, scale=12), nullable=False)

    recurring_interval: schemas.BillingRecurringInterval = (
        Column(Enum(schemas.BillingRecurringInterval))
    )
    recurring_interval_count: int = Column(SMALLINT(unsigned=True), default=1)

    usage_type: schemas.BillingUsageType = Column(
        Enum(schemas.BillingUsageType),
        nullable=False,
        default=schemas.BillingUsageType.LICENSED,
    )
    meter_id: str | None = Column(String(255), nullable=True)
    meter_event_name: str | None = Column(String(20), nullable=True)

    transform_quantity: schemas.BillingTransformQuantity | None = Column(
        PydanticJSON(
            schemas.BillingTransformQuantity
        ),
        nullable=True,
    )

    stripe_item_id: str = Column(String(255), nullable=False)
    stripe_price_id: str = Column(String(255), nullable=False)
