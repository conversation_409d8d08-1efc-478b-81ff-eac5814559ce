from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.orm import backref, relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import SSEChannelTarget


class SSEChannel(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = "sse_channels"

    id = models.Column(models.BigInteger, primary_key=True)
    session_id: str = Column(String(36), nullable=False)
    target: SSEChannelTarget = Column(
        Enum(SSEChannelTarget), nullable=False, default="ADMIN_NOTIFICATION"
    )

    profile_id: int = Column(
        BigInteger, ForeignKey("groups.id", ondelete="CASCADE"), nullable=True
    )
    group: "models.Group" = relationship("Group", backref=backref("sse_channels"))

    key: str = Column(String(99), nullable=True)
