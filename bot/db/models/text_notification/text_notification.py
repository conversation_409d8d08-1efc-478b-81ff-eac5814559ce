from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>olean, Column, DateTime, Enum, ForeignKey, Text, func
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from config import INBOX_MAX_AGE
from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import TextNotificationTargetEnum, TextNotificationTypeEnum


class TextNotification(Base, BaseDBModel, TimeCreatedMixin):
    target: TextNotificationTargetEnum = Column(Enum(TextNotificationTargetEnum), nullable=False)
    type: TextNotificationTypeEnum = Column(Enum(TextNotificationTypeEnum), nullable=False)

    profile_id: int = Column(BigInteger, ForeignKey("groups.id"), nullable=False)
    profile: "models.Group" = relationship("Group", backref="text_notifications")

    store_id: int | None = Column(BigInteger, ForeignKey("stores.id"), nullable=True)
    store: "models.Store | None" = relationship("Store", backref="text_notifications")

    from_user_id: int | None = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    from_user: "models.User | None" = relationship("User", backref="text_notifications", foreign_keys=from_user_id)

    from_bot_id: int | None = Column(BigInteger, ForeignKey("bots.id"), nullable=True)
    from_bot: "models.ClientBot | None" = relationship("ClientBot", backref="text_notifications")

    menu_in_store_id: int | None = Column(BigInteger, ForeignKey("menus_in_store.id"), nullable=True)
    menu_in_store: "models.MenuInStore | None" = relationship("MenuInStore", backref="text_notifications")

    text: str = Column(Text, nullable=False)

    _is_read: bool = Column(Boolean, default=False)
    read_by_user_id: int | None = Column(BigInteger, ForeignKey("users.id", ondelete="SET NULL"))
    read_by_user: "models.User | None" = relationship(
        "User", backref="text_read_notifications",
        foreign_keys=read_by_user_id,
    )
    read_status_change_datetime: datetime | None = Column(DateTime(timezone=True), nullable=True)

    @hybrid_property
    def change_date(self):
        return self.read_status_change_datetime or self.time_created

    @change_date.expression
    def change_date(self):
        return func.IFNULL(self.read_status_change_datetime, self.time_created)

    @hybrid_property
    def is_read(self):
        return self._is_read or self.change_date < datetime.utcnow() - INBOX_MAX_AGE

    @is_read.setter
    def is_read(self, value: bool):
        self._is_read = value

    @is_read.expression
    def is_read(self):
        return func.IF(self.change_date < datetime.utcnow() - INBOX_MAX_AGE, True, self._is_read)

    @hybrid_property
    def crm_tag(self):
        return f"notification-{self.id}"

    @crm_tag.expression
    def crm_tag(self):
        return func.concat("notification-", self.id)

    @hybrid_property
    def user_id(self):
        """For integrity with other CRM models"""
        return self.from_user_id
