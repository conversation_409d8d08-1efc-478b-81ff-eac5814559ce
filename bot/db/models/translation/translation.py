from sqlalchemy import Column, String, func, select
from typing import Any, Type
from typing_extensions import Literal

from db.connection import Base
from db.decorators import db_func
from db.helpers import hybrid_method, sess
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson


class Translation(Base, BaseDBModel):
    id: str = Column(String(50), primary_key=True, unique=True)

    obj_type: str = Column(String(32), nullable=True)
    obj_id: str = Column(String(24), nullable=True)
    lang: str = Column(String(2), nullable=True)

    data: dict[str, Any] = Column(NestedMutableJson)

    def __init__(
            self,
            obj_type: str,
            obj_id: str,
            lang: str,
            data: dict[str, Any] | None = None,
            **kwargs,
    ):
        id = kwargs.pop("id", self.make_id(obj_type, obj_id, lang))
        super().__init__(
            id=id,
            obj_type=obj_type,
            obj_id=obj_id,
            lang=lang,
            data=data or {},
            **kwargs,
        )

    @property
    def obj_name(self):
        return self.obj_type

    @classmethod
    def make_id(cls, object_type: str | Base, object_id: int | str, lang: str):
        if not isinstance(object_type, str):
            object_type = object_type.__class__.__name__
        return f"{object_type}-{lang}-{object_id}"

    @classmethod
    @db_func
    def get(cls, object_type: str | Base, object_id: int, lang: str) -> "Translation":
        translation_id = cls.make_id(object_type, object_id, lang)
        query = sess().query(cls)
        query = query.filter(cls.id == translation_id)
        return query.one()

    @classmethod
    async def get_by_obj(cls, obj: Base, lang: str) -> "Translation":
        return await cls.get(obj, obj.id, lang)

    @classmethod
    def create_sync(
            cls,
            object_type: str,
            object_id: int,
            lang: str,
            no_commit: bool = False,
    ) -> "Translation":
        translation_id = cls.make_id(object_type, object_id, lang)

        stmt = select(cls)
        stmt = stmt.where(cls.id == translation_id)
        stmt = stmt.with_for_update()
        translation = sess().scalar(stmt)

        if not translation:
            translation = cls(
                obj_type=object_type,
                obj_id=str(object_id),
                lang=lang,
            )
            sess().add(translation)

        if not no_commit:
            sess().commit()

        return translation

    @classmethod
    @db_func
    def create(cls, object_type: str, object_id: int, lang: str) -> "Translation":
        return cls.create_sync(object_type, object_id, lang)

    @staticmethod
    @db_func
    def get_languages(object_type: str, object_id: int) -> [str]:
        query = sess().query(Translation.id)

        query = query.filter(Translation.id.startswith(f"{object_type}-"))
        query = query.filter(Translation.id.endswith(f"-{object_id}"))

        translations = query.all()
        languages = [translation[0].split("-")[1] for translation in translations]
        return languages

    def clear_sync(self, *keys: str | int) -> bool:
        if not keys:
            raise ValueError("you must specify at least one key")

        current = self.data
        for key in keys[:-1]:
            if isinstance(current, dict):
                current = current.get(key)
            elif isinstance(current, list):
                if not isinstance(key, int):
                    raise ValueError(f"List index must be an integer, not {type(key)}")
                if key >= len(current):
                    current = None
                else:
                    current = current[key]
            elif current is None:
                break
            else:
                raise ValueError("invalid number of keys")

        key = keys[-1]
        if isinstance(current, dict):
            current.pop(key, None)
        elif isinstance(current, list):
            if not isinstance(key, int):
                raise ValueError(f"List index must be an integer, not {type(key)}")

            if key < len(current):
                current.pop(key)
        elif current is not None:
            raise ValueError("invalid number of keys")

        return True

    @classmethod
    @db_func
    def clear(cls, object_type: str, object_id: int, lang: str, *keys):
        translation = cls.get_sync(f"{object_type}-{lang}-{object_id}")
        translation.clear_sync(*keys)
        sess().commit()

    @classmethod
    @db_func
    def clear_full(cls, object_type: str, object_id: int, *keys) -> bool:
        query = sess().query(cls)
        query = query.filter(cls.id.startswith(object_type))
        query = query.filter(cls.id.endswith(str(object_id)))
        translations = query.all()

        for translation in translations:
            translation.clear_sync(*keys)
        sess().commit()
        return True

    @staticmethod
    @db_func
    def delete(object_type: str, object_id: int, lang: str) -> bool:
        translation_id = f"{object_type}-{lang}-{object_id}"
        query = sess().query(Translation)
        query.filter(Translation.id == translation_id).delete()
        sess().commit()
        return True

    @staticmethod
    @db_func
    def delete_full(object_type: str, object_id: int) -> bool:
        query = sess().query(Translation)
        query = query.filter(Translation.id.startswith(object_type))
        query = query.filter(Translation.id.endswith(str(object_id)))
        translations = query.all()

        for translation in translations:
            sess().delete(translation)

        sess().commit()
        return True

    @staticmethod
    def build_id(
            object_model: Type[Base] | Base,
            lang: str,
            id_expression: Any = None,
            object_name: str | None = None,
    ):
        if not object_name:
            if isinstance(object_model, Base):
                object_name = object_model.__class__.__name__
            else:
                object_name = object_model.__name__
        base_id = f"{object_name}-{lang}-"
        object_id = func.CONCAT(base_id, id_expression or object_model.id)
        return object_id

    @hybrid_method
    def filter(
            self,
            object_model: Type[Base] | Base,
            lang: str,
            id_expression: Any = None,
            object_name: str | None = None,
    ):
        return self.id == self.build_id(object_model, lang, id_expression, object_name)

    @hybrid_method
    def get_translation(
            self: Type["Translation"],
            object_model: Base,
            lang: str,
            id_expression: Any = None,
    ):
        query = sess().query(self)
        query = query.filter(
            self.id == self.build_id(object_model, lang, id_expression)
        )  # type: ignore
        return query.scalar_subquery()

    @hybrid_method
    def get_field_expression(self, field_name: str):
        return func.TRIM(func.LOWER(func.JSON_UNQUOTE(self.data[field_name])))

    @hybrid_method
    def filter_by_translation(
            self: Type["Translation"],
            object_model: Base,
            lang: str,
            field_name: str,
            search_value: str,
            equal_or_contains: Literal["equal", "contains"] = "contains",
            id_expression: Any = None,
            object_name: str | None = None,
    ):
        query = sess().query(self.id)
        query = query.filter(
            self.id == self.build_id(object_model, lang, id_expression, object_name)
        )  # type: ignore

        field_expr = self.get_field_expression(field_name)
        search_value = search_value.lower().strip()

        if equal_or_contains == "contains":
            query = query.filter(field_expr.contains(search_value))
        else:
            query = query.filter(field_expr == search_value)
        return query.exists()
