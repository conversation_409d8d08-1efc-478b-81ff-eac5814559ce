from datetime import datetime

from sqlalchemy import Column, String, DateTime

from db.connection import Base
from db import sess, db_func
from db.mixins import BaseDBModel


class TranslationBackground(Base, BaseDBModel):
    lang = Column(String(2), unique=True)

    # statuses: ["enabled", "finished"]
    status = Column(String(10), default="enabled")

    create_date = Column(DateTime, default=datetime.utcnow)
    update_date = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, lang: str):
        assert lang
        super().__init__(lang=lang)

    @property
    def is_finished(self):
        return self.status == "finished"

    @classmethod
    @db_func
    def save(cls, lang: str) -> "TranslationBackground":
        background = cls(lang)
        sess().add(background)
        sess().commit()
        return background

    @classmethod
    async def create(cls, lang: str) -> "TranslationBackground":
        background = await cls.get(lang=lang)
        if background:
            await background.update(status="enabled", update_date=datetime.utcnow())
            return background

        background = await cls.save(lang)
        return background

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()
        return True

    @db_func
    def finish(self) -> bool:
        self.status = "finished"
        sess().commit()
        return True
