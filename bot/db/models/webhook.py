from datetime import datetime, timedelta

from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, Enum, Foreign<PERSON>ey, Integer, String,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from config import WEBHOOK_MAX_RETRIES_FOR_BLOCK
from db import models
from db.connection import Base
from db.custom_column_types.pydantic_json import <PERSON><PERSON>anticJSON
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson
from schemas import (
    WebhookActionEnum, WebhookEntityEnum, WebhookJournalDataSchema,
    WebhookJournalStatusEnum, WebhookSentStatusEnum,
)


class Webhook(Base, BaseDBModel):
    id: int = Column(Integer, primary_key=True, autoincrement=True)
    endpoint_url: str = Column(String(512), nullable=False)
    hook_id: str = Column(String(36), nullable=False)
    entities: list[str] = Column(NestedMutableJson, nullable=False)
    is_enabled: bool = Column(Boolean, default=True)
    last_sent_status: WebhookSentStatusEnum = Column(
        Enum(WebhookSentStatusEnum), default=WebhookSentStatusEnum.IDLE
    )
    retries_count: int = Column(Integer, default=0)
    last_sent_datetime: datetime | None = Column(DateTime, nullable=True)
    is_deleted: bool = Column(Boolean, default=False)
    description: str | None = Column(String(512), nullable=True)

    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: "models.Group" = relationship("Group", foreign_keys=group_id)
    persistent_webhook: bool = Column(Boolean, default=False)

    @hybrid_property
    def blocked(self):
        return self.retries_count >= WEBHOOK_MAX_RETRIES_FOR_BLOCK


class WebhookJournal(Base, BaseDBModel):
    id: int = Column(Integer, primary_key=True, autoincrement=True)
    journal_uuid: str = Column(String(36), nullable=False)
    entity: WebhookEntityEnum = Column(Enum(WebhookEntityEnum), nullable=False)
    entity_id: int = Column(BigInteger, nullable=False)
    action: WebhookActionEnum = Column(
        Enum(WebhookActionEnum), default=None, nullable=True
    )
    event_created_datetime: datetime = Column(DateTime, default=datetime.utcnow)
    event_start_datetime: datetime = Column(DateTime, nullable=True, default=None)
    event_end_datetime: datetime = Column(DateTime, nullable=True, default=None)
    json_data: WebhookJournalDataSchema = Column(
        PydanticJSON(WebhookJournalDataSchema), default=None, nullable=True
    )
    status: WebhookJournalStatusEnum = Column(
        Enum(WebhookJournalStatusEnum), default=WebhookJournalStatusEnum.CREATED
    )

    webhook_id: int = Column(Integer, ForeignKey("webhooks.id", ondelete="CASCADE"))
    webhook: "models.Webhook" = relationship("Webhook", foreign_keys=webhook_id)

    @hybrid_property
    def duration(self) -> timedelta | None:
        if not self.event_end_datetime or not self.event_start_datetime:
            return None
        return self.event_end_datetime - self.event_start_datetime
