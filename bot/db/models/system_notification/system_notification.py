from datetime import datetime

from sqlalchemy import (
    BigInteger, <PERSON>olean, Column, DateTime, Enum, ForeignKey,
    String, \
    Text,
)
from sqlalchemy.orm import backref, relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import (
    NotificationLevel, NotificationRecipientType, SystemNotificationCategory,
    SystemNotificationType,
)


class SystemNotification(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = 'system_notifications'
    scope: str = Column(String(1024), nullable=False)
    category: SystemNotificationCategory = Column(
        Enum(SystemNotificationCategory), nullable=False,
    )
    type_notification: SystemNotificationType = Column(
        Enum(SystemNotificationType), nullable=False,
    )
    level: NotificationLevel = Column(
        Enum(NotificationLevel), nullable=False,
        default=NotificationLevel.ERROR
    )
    title: str = Column(String(1024), nullable=False)
    content: str = Column(Text(collation="utf8mb4_unicode_ci"), nullable=False)
    group_id: int = Column(
        BigInteger, ForeignKey("groups.id", ondelete="RESTRICT"), nullable=False
    )
    group: "models.Group" = relationship(
        "Group",
        backref=backref("admin_notifies"),
        foreign_keys=group_id
    )
    change_date: datetime = Column(DateTime, nullable=False, default=datetime.utcnow)
    is_read: bool = Column(Boolean, nullable=False, default=False)
    is_deleted: bool = Column(Boolean, nullable=False, default=False)

    recipient_type: NotificationRecipientType = Column(
        Enum(NotificationRecipientType),
        nullable=False,
        default=NotificationRecipientType.ADMIN,
    )
    recipient_id: int = Column(BigInteger, ForeignKey("users.id"), nullable=True)
