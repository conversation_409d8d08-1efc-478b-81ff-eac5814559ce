from typing import List

from sqlalchemy import (
    BigInteger, Column, <PERSON>um, Foreign<PERSON>ey, JSON, String,
)
from sqlalchemy.orm import relationship

import schemas
from db import db_func, models, sess
from db.connection import Base
from ..mixins import BaseDBModel


class JournalSetting(Base, BaseDBModel):

    name: str | None = Column(String(255), nullable=True, default=None)

    type: schemas.JournalSettingTypeEnum = Column(Enum(schemas.JournalSettingTypeEnum))

    settings: list = Column(JSON, nullable=True)

    table_settings: list = Column(JSON, nullable=True)

    group_id: int = Column(
        BigInteger, ForeignKey("groups.id", ondelete="CASCADE"), nullable=False
    )
    group: "models.Group" = relationship("Group", foreign_keys=group_id)

    user_id: int = Column(
        BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )
    user: "models.User" = relationship("User", foreign_keys=user_id)

    @classmethod
    @db_func
    def get_all(cls, group_id: int, user_id: int) -> List["JournalSetting"]:
        journal_settings = sess().query(cls).filter(
            cls.group_id == group_id,
            cls.user_id == user_id
        ).all()
        return journal_settings
