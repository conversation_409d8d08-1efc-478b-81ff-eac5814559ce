import enum
from datetime import datetime
from typing import Type

from sqlalchemy import <PERSON>um<PERSON>, Enum, DateTime, BigInteger, ForeignKey, func
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class QuantitativeServiceTypeEnum(enum.Enum):
    TRANSLATE = "t"


class QuantitativeService(Base, BaseDBModel, TimeCreatedMixin):
    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="RESTRICT"))
    group: "models.Group" = relationship("Group", back_populates="quantitative_services")

    service_type: QuantitativeServiceTypeEnum = Column(Enum(QuantitativeServiceTypeEnum), nullable=False)
    quantity: int | None = Column(INTEGER(unsigned=True), nullable=True)  # None means unlimited
    used: int = Column(INTEGER(unsigned=True), nullable=False, default=0)

    expire_at: datetime | None = Column(DateTime, nullable=True, default=None)

    @hybrid_property
    def available(self):
        if not self.quantity:
            return None
        return self.quantity - self.used

    @available.expression
    def available(self: Type["QuantitativeService"]):
        return func.IF(self.quantity.is_not(None), self.quantity - self.used, None)
