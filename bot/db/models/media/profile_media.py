from sqlalchemy import BigInteger, Column, ForeignKey
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class ProfileMedia(Base, BaseDBModel, TimeCreatedMixin):
    profile_id: int = Column(BigInteger, ForeignKey("groups.id"), nullable=False)
    profile: "models.Group" = relationship("Group", backref="medias")

    media_id: int = Column(BigInteger, ForeignKey("media_objects.id"), nullable=False)
    media: "models.MediaObject" = relationship("MediaObject", backref="profile_medias")
