from sqlalchemy import <PERSON>umn, BigInteger, ForeignKey
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class SizedMedia(Base, BaseDBModel, TimeCreatedMixin):
    media_pack_id: int = Column(
        BigInteger,
        ForeignKey(
            "media_packs.id",
            ondelete="RESTRICT",
        ),
    )
    media_pack: "models.MediaPack" = relationship(
        "MediaPack",
        back_populates="sized_medias"
    )

    media_id: int = Column(
        BigInteger,
        ForeignKey(
            "media_objects.id",
            ondelete="RESTRICT",
        )
    )
    media: "models.MediaObject" = relationship(
        "MediaObject",
        backref="sized_medias"
    )
