from sqlalchemy import <PERSON>umn, BigInteger, ForeignKey
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class MediaPack(Base, BaseDBModel, TimeCreatedMixin):
    """
    Full media pack. Change in products and banners later
    """

    media_id: int = Column(
        BigInteger,
        ForeignKey(
            "media_objects.id",
            ondelete="RESTRICT",
        ),
    )
    media: "models.MediaObject" = relationship(
        "MediaObject",
        backref="media_packs_medias",
        foreign_keys=[media_id],
    )

    thumbnail_media_id: int = Column(
        BigInteger,
        ForeignKey(
            "media_objects.id",
            ondelete="RESTRICT",
        ))
    thumbnail_media: "models.MediaObject | None" = relationship(
        "MediaObject",
        backref="media_packs_thumbnails",
        foreign_keys=[thumbnail_media_id]
    )

    sized_medias: list["models.SizedMedia"] = relationship(
        "SizedMedia",
        back_populates="media_pack",
    )
