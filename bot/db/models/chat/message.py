from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from db.my_columns import NestedMutableJson
from schemas import ChatMessageSentByEnum, MessageContentTypeEnum


class ChatMessage(Base, BaseDBModel, TimeCreatedMixin):
    chat_id: int = Column(
        BigInteger, ForeignKey("chats.id", ondelete="CASCADE"), nullable=False
    )
    chat: "models.Chat" = relationship(
        "Chat", back_populates="messages", foreign_keys=chat_id
    )

    sent_by: ChatMessageSentByEnum = Column(Enum(ChatMessageSentByEnum), nullable=False)

    sent_by_user_id: int | None = Column(
        BigInteger, ForeignKey("users.id", ondelete="CASCADE")
    )
    sent_by_user: "models.User | None" = relationship("User", backref="sent_messages")

    vmc_id: int | None = Column(
        BigInteger, ForeignKey("virtual_manager_chats.id", ondelete="CASCADE")
    )
    vmc: "models.VirtualManagerChat | None" = relationship(
        "VirtualManagerChat", backref="sent_messages"
    )

    content_type: MessageContentTypeEnum = Column(
        Enum(MessageContentTypeEnum), nullable=False
    )

    text: str | None = Column(Text, nullable=True)
    media_id: int | None = Column(
        BigInteger,
        ForeignKey("media_objects.id", ondelete="RESTRICT"),
        nullable=True,
    )
    media: "models.MediaObject | None" = relationship(
        "MediaObject", backref="chat_messages"
    )
    content: dict | None = Column(NestedMutableJson, nullable=True)

    menu_in_store_id: int | None = Column(
        BigInteger, ForeignKey("menus_in_store.id", ondelete="SET NULL")
    )
    menu_in_store: "models.MenuInStore | None" = relationship(
        "MenuInStore", backref="chat_messages"
    )

    wa_master_template_id: int | None = Column(
        BigInteger, ForeignKey("wa_master_templates.id", ondelete="SET NULL")
    )
    wa_master_template: "models.WAMasterTemplate | None" = relationship(
        "WAMasterTemplate", backref='chat_messages'
    )
    wa_template_variables: list[dict] | None = Column(NestedMutableJson, nullable=True)

    is_mailing = Column(Boolean, default=False)
    is_last: bool = Column(Boolean, default=False)

    @hybrid_property
    def hide_from_user(self):
        return self.content_type == MessageContentTypeEnum.PROCESSED
