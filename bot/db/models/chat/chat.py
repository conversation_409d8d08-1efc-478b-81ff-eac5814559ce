from datetime import datetime

from sqlalchemy import (
    BigInteger, <PERSON>olean, Column, DateTime, Enum, ForeignKey,
    UniqueConstraint, func,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from config import INBOX_MAX_AGE
from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import ChatTypeEnum


class Chat(Base, BaseDBModel, TimeCreatedMixin):
    type: ChatTypeEnum = Column(Enum(ChatTypeEnum), nullable=False)

    user_id: int = Column(
        BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )
    user: "models.User" = relationship("User")

    group_id: int = Column(
        BigInteger, ForeignKey("groups.id", ondelete="CASCADE"), nullable=False
    )
    group: "models.Group" = relationship("Group")

    bot_id: int | None = Column(
        BigInteger, Foreign<PERSON>ey("bots.id", ondelete="CASCADE"), nullable=True
    )
    bot: "models.ClientBot | None" = relationship("ClientBot", backref="chats")

    _is_pending: bool = Column(Boolean, default=False)
    # last datetime when pending state was set
    last_pending_set_datetime: datetime | None = Column(
        DateTime(timezone=True), nullable=True
    )
    change_date: datetime = Column(
        DateTime(timezone=True), nullable=False, default=datetime.utcnow
    )

    messages: list["models.ChatMessage"] = relationship(
        "ChatMessage", foreign_keys="ChatMessage.chat_id",
        back_populates="chat",
    )

    __table_args__ = (
        UniqueConstraint("type", "user_id", "group_id", "bot_id"),
    )

    @hybrid_property
    def is_pending(self):
        return (self._is_pending and self.last_pending_set_datetime >=
                datetime.utcnow() - INBOX_MAX_AGE)

    @is_pending.setter
    def is_pending(self, value: bool):
        self._is_pending = value

    @is_pending.expression
    def is_pending(self):
        return func.IF(
            self._is_pending.is_(False), False,
            self.last_pending_set_datetime < datetime.utcnow()
        )

    @hybrid_property
    def crm_tag(self):
        return f"chat-{self.id}"

    @crm_tag.expression
    def crm_tag(self):
        return func.concat("chat-", self.id)
