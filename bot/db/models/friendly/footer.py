import logging
import re

import config as cfg

from datetime import datetime, timedelta, date
from typing import List, Dict, Literal, Tuple

from sqlalchemy import Column, Integer, String, BigInteger, Text, Boolean, DateTime
from sqlalchemy import ForeignKey, func, Date, Time
from sqlalchemy.orm import relationship, backref

from db import models
from db.connection import Base
from db.helpers import sess
from db.decorators import db_func

from utils.media import delete_file

from utils.redefined_classes import InlineKb, InlineBtn

from utils.text import f


class Footer(Base):

    __tablename__ = "footers"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    channel_id = Column(BigInteger, ForeignKey("channels.id", ondelete="CASCADE"))
    channel = relationship("Channel", foreign_keys=channel_id)

    content_type = Column(String(15))
    text = Column(Text(collation="utf8mb4_unicode_ci"), default=None, nullable=True)
    media_path = Column(String(512), default=None, nullable=True)
    buttons = Column(Text(collation="utf8mb4_unicode_ci"), default=None, nullable=True)
    full_text = Column(Text(collation="utf8mb4_unicode_ci"), default=None, nullable=True)

    need_concatenation = Column(Boolean, default=True)
    SUPPORTED_MESSAGE_TYPES = cfg.MEDIA_WITH_CAPTION + ["text", "sticker"]
    KEYS_FOR_DICT = ("content_type", "text", "media_path", "buttons", "need_concatenation",)

    def __init__(
            self,
            channel_id: int,
            content_type: str,
            full_text: str,
            text: str = None,
            media_path: str = None,
            buttons: str = None
    ):
        assert channel_id
        assert content_type
        assert full_text
        assert text or media_path or buttons

        self.channel_id = channel_id
        self.content_type = content_type
        self.full_text = full_text
        self.text = text
        self.media_path = media_path
        self.buttons = buttons

    @classmethod
    @db_func
    def save(
            cls,
            channel_id: int,
            need_concatenation: bool,
            content_type: str,
            full_text: str,
            text: str = None,
            media_path: str = None,
            buttons: str = None,
    ) -> "Footer":
        result = cls._check_buttons(buttons)
        if isinstance(result, str):
            return result

        footer = cls(channel_id, content_type, full_text, text, media_path, buttons)
        footer.need_concatenation = need_concatenation

        sess().add(footer)
        sess().commit()
        return footer

    @classmethod
    async def create(
            cls,
            channel_id: int,
            need_concatenation: bool,
            content_type: str,
            text: str = None,
            media_path: str = None,
    ) -> "Footer":
        if not text and not media_path:
            raise ValueError(f"one of text and media_path must be specified. Now: {text = }, {media_path = }")

        full_text = text
        text, buttons = await cls.parse(full_text)
        if not text:
            need_concatenation = True
        footer = await cls.save(channel_id, need_concatenation, content_type, full_text, text, media_path, buttons)

        if isinstance(footer, str):
            logger = logging.getLogger()
            logger.error(f"Error: {footer}")

        return footer

    async def change(
            self,
            need_concatenation: bool,
            content_type: str,
            text: str = None,
            media_path: str = None
    ) -> "Footer":
        full_text = text
        text, buttons = await Footer.parse(full_text)
        result = Footer._check_buttons(buttons)
        if isinstance(result, str):
            return result

        self.content_type = content_type
        self.full_text = full_text
        self.text = text
        self.media_path = media_path
        self.buttons = buttons
        self.need_concatenation = need_concatenation

        sess().commit()
        return result

    @classmethod
    @db_func
    def parse(cls, text: str) -> Tuple[str, str]:
        try:
            found_buttons_rows = re.findall(r"<buttons.*>", text)
        except:
            found_buttons_rows = None

        if not found_buttons_rows:
            found_buttons_rows = list()

        for row_text in text.split("\n"):
            row_buttons = "".join(re.findall(r"(\[[^\[\]]+])", row_text))

            if row_buttons:
                found_buttons_rows.append(row_buttons)

        if not found_buttons_rows:
            return text, None

        for row in found_buttons_rows:
            text = text.replace(row, "")

        buttons = "\n".join(found_buttons_rows)
        return text, buttons

    def get_keyboard(self) -> InlineKb | None:
        if not self.buttons:
            return None
        keyboard = InlineKb(row_width=1)
        buttons = Footer._get_buttons(self.buttons)
        if isinstance(buttons, str):
            return None

        for row in buttons:
            keyboard.add(*row)
        return keyboard

    @classmethod
    def _get_buttons(cls, buttons_str: str) -> list | str:
        buttons = list()
        for buttons_row in buttons_str.split("\n"):
            row = list()
            buttons_row = re.findall(r"(\[[^\[\]]+])", buttons_row)
            for button in buttons_row:
                button = button[1:-1]
                try:
                    text, url = button.split("=", 1)
                    text = text.strip()
                    url = url.strip()
                except:
                    return "error parsing button text"
                row.append(InlineBtn(text, url=url))

            if len(row) > cfg.FRIENDLY_LIMIT_BUTTONS_COUNT_IN_ROW:
                return "error buttons count in row"
            buttons.append(row)

        if len(buttons) > cfg.FRIENDLY_LIMIT_TOTAL_BUTTONS_COUNT:
            return "error total buttons count"
        return buttons

    @classmethod
    def _check_buttons(cls, buttons_str: str) -> bool | str:
        if not buttons_str:
            return

        error = cls._get_buttons(buttons_str)
        if isinstance(error, str):
            return error
        return True

    @staticmethod
    @db_func
    def get(footer_id: int) -> "Footer":
        return sess().query(Footer).filter(Footer.id == footer_id).one_or_none()

    @property
    def kwargs_for_send(self) -> Dict[str, str]:
        content_type = self.content_type
        kwargs = dict(content_type=content_type)
        kwargs.update(text=self.text)
        if content_type != "text":
            kwargs[content_type] = self.media_path
        kwargs.update(keyboard=self.get_keyboard())
        return kwargs

    @property
    def kwargs_for_analitic(self) -> Dict[str, str]:
        kwargs = {f"footer_{k}": getattr(self, k) for k in Footer.KEYS_FOR_DICT}
        return kwargs

    @db_func
    def delete(self) -> bool:
        if self.media_path:
            delete_file(self.media_path)

        sess().delete(self)
        sess().commit()
        return True

    @db_func
    def _update(self, **kwargs) -> bool:
        for field_name, value in kwargs.items():
            if field_name not in dir(self):
                continue
            setattr(self, field_name, value)
        sess().commit()
        return True

    async def update(self, **kwargs) -> bool:
        result = await self._update(**kwargs)
        if not result:
            return result

        return True
