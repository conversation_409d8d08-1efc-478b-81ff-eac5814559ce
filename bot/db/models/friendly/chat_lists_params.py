from typing import Any, Dict

from sqlalchemy import Column, BigInteger, Foreign<PERSON>ey, String
from sqlalchemy.orm import relationship

from db import sess, db_func, models
from db.connection import Base
from db.my_columns import NestedMutableJson


class FriendlyChatListParams(Base):
    __tablename__ = "friendly_chat_list_params"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user = relationship("User")
    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot = relationship("ClientBot")
    channels_mode = Column(String(50, collation="utf8mb4_unicode_ci"), default=None)
    subscriptions = Column(NestedMutableJson, default=dict(
        position=0,
        is_active_chats_filter=False,
        is_active_channels_filter=False,
        is_active_is_admin_filter=False,
        is_active_is_not_admin_filter=False,
        location_id_filter=None,
    ))

    admin = Column(NestedMutableJson, default=dict(
        position=0,
        is_active_chats_filter=False,
        is_active_channels_filter=False,
        is_active_is_admin_filter=False,
        is_active_is_not_admin_filter=False,
        location_id_filter=None,
    ))

    super_admin = Column(NestedMutableJson, default=dict(
        position=0,
        is_active_chats_filter=False,
        is_active_channels_filter=False,
        is_active_is_admin_filter=False,
        is_active_is_not_admin_filter=False,
        location_id_filter=None,
    ))

    def __init__(self, user: "models.User", bot: "models.ClientBot", channels_mode: str | None):
        assert user
        assert bot

        self.user = user
        self.bot = bot
        self.channels_mode = channels_mode

    @classmethod
    @db_func
    def create(cls, user: "models.User", bot: "models.ClientBot", channels_mode: str):
        list_params = cls(user, bot, channels_mode)
        sess().add(list_params)
        sess().commit()
        return list_params

    @classmethod
    async def create_or_update(
            cls, user: "models.User",
            channels_mode: str | None,
            **kwargs,
    ) -> Dict[str, Dict[
        str, str | int | bool
    ]]:

        bot = await models.ClientBot.get_current()
        list_params = await cls.get(user.id, bot.id)

        if not list_params:
            if channels_mode is None:
                raise ValueError(
                    "Channels mode can be None only "
                    "if FriendlyChatListParams object has already been created for user",
                )

            list_params = await cls.create(user, bot, channels_mode)
        elif channels_mode is None:
            channels_mode = list_params.channels_mode

        await list_params.update(channels_mode, **kwargs)
        result = getattr(list_params, channels_mode)
        return result

    @classmethod
    @db_func
    def get(cls, user_id: int, bot_id: int) -> "FriendlyChatListParams":
        query = sess().query(cls)
        query = query.filter(cls.user_id == user_id)
        query = query.filter(cls.bot_id == bot_id)
        return query.one()

    @db_func
    def update(self, channels_mode: str, **kwargs):
        self.channels_mode = channels_mode
        if not kwargs:
            sess().commit()
            return

        list_params = self.current_params
        new_list_params = dict(list_params, **kwargs)
        setattr(self, channels_mode, new_list_params)
        sess().commit()
        return

    @property
    def current_params(self) -> Dict[str, Any]:
        return getattr(self, self.channels_mode)

    async def update_filter(self, filter_type: str):
        if not filter_type:
            raise ValueError(f"filter_type argument must be str, not {type(filter_type)}")
        current_params = self.current_params
        filter_state = current_params.get(filter_type, True)
        await self.update(self.channels_mode, **{filter_type: not filter_state}, position=0)

    async def update_location_filter(self, location_id: int | None):
        await self.update(self.channels_mode, **{"location_id_filter": location_id}, position=0)
