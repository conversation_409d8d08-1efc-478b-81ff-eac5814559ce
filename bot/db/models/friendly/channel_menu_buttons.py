import asyncio
import logging
import re

from datetime import datetime, date, timedelta
from typing import Any, Dict, Literal, List, Type, Callable

from aiogram.utils.exceptions import ChatNotFound
from sqlalchemy import Column, Integer, String, BigInteger, Text, Boolean, DateTime
from sqlalchemy import Foreign<PERSON>ey, UniqueConstraint, func, JSON, Float
from sqlalchemy.orm import relationship

from aiogram import types as aiogram_types, types

from db import models
from db.connection import Base
from db.helpers import T, sess, DBSession
from db.my_columns import NestedMutableJson
from db.decorators import db_func, own_session
from .channel import Channel

from utils.text import f, c
from utils.redefined_classes import Bot, InlineBtn


class ChannelMenuButton(Base):

    __tablename__ = "channel_menu_buttons"

    id = Column(BigInteger, primary_key=True, autoincrement=True)

    channel_id = Column(ForeignKey(Channel.id))
    channel = relationship(Channel, foreign_keys=channel_id)

    name_button = Column(String(20))
    is_new_line = Column(Boolean, default=False)
    message = Column(Text, default="")
    media = Column(Text, default="")
    content_type = Column(Text, default="")
    button_number = Column(BigInteger, nullable=True)

    __table_args__ = (
        UniqueConstraint("channel_id", "name_button"),
    )

    def __init__(
            self,
            channel: "models.Channel",
            name_button: str,
            message: str,
            media: str,
            content_type: str,
            button_number: int | None,
            is_new_line: bool = False,
    ):
        assert channel

        self.id: int
        self.channel = channel
        self.name_button = name_button
        self.is_new_line = is_new_line
        self.message = message
        self.media = media
        self.content_type = content_type
        self.button_number = button_number

        super().__init__()

    @classmethod
    @db_func
    def create(
            cls: Type[T],
            channel: "models.Channel",
            name_button: str,
            message: str,
            media: str,
            content_type: str,
            is_new_line: bool = False,
    ) -> T:
        button_count = sess().query(func.max(ChannelMenuButton.button_number)).filter_by(channel_id=channel.id).scalar()
        if button_count is None:
            button_count = 0
        channel_button = ChannelMenuButton(
            channel,
            name_button,
            message,
            media,
            content_type,
            button_count+1,
            is_new_line,)
        sess().add(channel_button)
        sess().commit()
        return channel_button

    @classmethod
    @db_func
    def get(cls: Type[T], id: int) -> "ChannelMenuButton":
        button = sess().query(ChannelMenuButton).filter(ChannelMenuButton.id == id).one()
        return button

    @db_func
    def edit_name_button(self, name_button: str):
        self.name_button = name_button
        sess().commit()
        return True

    @db_func
    def edit_content_button(self, text: str, content_type: str, media: str):
        self.message = text
        self.content_type = content_type
        self.media = media
        sess().commit()
        return True

    @db_func
    def update_is_new_line(self):
        self.is_new_line = not self.is_new_line
        sess().commit()
        return True

    @db_func
    def delete(self):
        sess().delete(self)
        sess().commit()
        return True
