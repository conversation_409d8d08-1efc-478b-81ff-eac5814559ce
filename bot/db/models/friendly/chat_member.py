from datetime import datetime
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Type, Union

from psutils.date_time import get_midnight
from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, ForeignKey, Integer, func,
    or_,
)
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.decorators import db_func
from db.helpers import T, sess
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson
from utils.redefined_classes import Bot
from utils.text import empty_value, f


class ChatMember(Base, BaseDBModel):
    __tablename__ = 'chats_members'

    id = Column(BigInteger, primary_key=True, autoincrement=True)

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    user = relationship("User", backref="chats_members")

    channel_id = Column(BigInteger, ForeignKey("channels.id", ondelete='CASCADE'))
    channel = relationship(
        "Channel", cascade="all, delete", back_populates="chat_members"
    )

    inviter_id = Column(
        BigInteger, ForeignKey("chats_members.id", ondelete="RESTRICT"), default=None,
        unique=False
    )
    inviter = relationship(
        "ChatMember",
        uselist=False,
        primaryjoin=inviter_id == id,
        foreign_keys=inviter_id, remote_side=id,
        backref="invited_members"
    )

    exception_ratio = Column(NestedMutableJson, default=None)
    messages_sent_without_rules = Column(Integer, default=0)

    limited_datetime = Column(DateTime(timezone=True), default=None)
    joined_datetime = Column(DateTime(timezone=True), default=datetime.utcnow)
    left_datetime = Column(DateTime(timezone=True), default=None)

    is_agreed_rules = Column(Boolean, default=False)
    ban_user_status = Column(Boolean, default=False)
    rules_start_datetime = Column(DateTime(timezone=True), default=None, nullable=True)

    is_favorite = Column(Boolean, default=False)
    max_message_limit = Column(BigInteger, default=0)

    def __init__(
            self: T,
            user: "models.User",
            channel: "models.Channel",
            inviter: T | None,
    ):
        assert user
        assert channel

        self.user = user
        self.channel = channel
        self.inviter = inviter

    @classmethod
    @db_func
    def _create(
            cls: Type[T],
            user: "models.User",
            channel: "models.Channel",
            inviter: T | None,
    ) -> T:
        chat_member = cls(user, channel, inviter)
        sess().add(chat_member)
        sess().commit()
        return chat_member

    @classmethod
    async def create_or_get(
            cls,
            user: "models.User",
            channel: "models.Channel",
            inviter: T | None = None,
    ) -> Optional["ChatMember"]:
        if not channel or not user:
            return

        inviter_id = inviter.id if inviter else None
        chat_member = await cls.get(user.id, channel.id)
        if chat_member is None:
            chat_member = await cls._create(user, channel, inviter)
            await models.FriendlyBotAnalyticAction.save_user_joined(
                member_id=chat_member.id, recommender_id=inviter_id
            )

        elif (chat_member.left_datetime is not None and chat_member.left_datetime >
              chat_member.joined_datetime):
            await chat_member.returned()
            await models.FriendlyBotAnalyticAction.save_user_returned(
                member_id=chat_member.id, recommender_id=inviter_id
            )
        return chat_member

    @classmethod
    @db_func
    def get(
            cls: Type[T], user_id: int = None, channel_id: int = None,
            chat_member_id: int = None
    ) -> T:
        query = sess().query(ChatMember)
        if chat_member_id:
            query = query.filter(ChatMember.id == chat_member_id)
        else:
            query = query.filter(
                ChatMember.user_id == user_id, ChatMember.channel_id == channel_id
            )
        member = query.one_or_none()
        return member

    @db_func
    def is_ban_limit_surpassed(self) -> bool:
        now = datetime.utcnow().timestamp()
        check_interval = (func.unix_timestamp(
            models.FriendlyBotAnalyticAction.datetime
        ) + self.channel.ban_interval)
        within_check_interval = check_interval >= now

        query = sess().query(func.count(models.FriendlyBotAnalyticAction.id))
        query = query.filter(
            models.FriendlyBotAnalyticAction.type == "message_deleted_by_bot"
        )
        query = query.filter(models.FriendlyBotAnalyticAction.member_id == self.id)
        query = query.filter(within_check_interval)
        count = query.scalar()
        return count > self.channel.ban_threshold

    async def ban(self):
        bot = Bot.get_current()
        await bot.ban_chat_member(self.channel.chat_id, self.user.chat_id)

    async def ro(self):
        bot = Bot.get_current()
        await bot.restrict_chat_member(
            self.channel.chat_id, self.user.chat_id, can_send_messages=False
        )

    @property
    async def ratio_str(self) -> str:
        channel = self.channel
        messages_for_friends = channel.messages_for_friends(self)
        friends_required = channel.friends_required(self)
        return await f(
            "ratio text", messages_for_friends=messages_for_friends,
            friends_required=friends_required
        )

    @classmethod
    @db_func
    def is_existing(cls, user_id: int, channel_id: int) -> bool:
        query = sess().query(ChatMember.id)
        query = query.filter(
            ChatMember.user_id == user_id, ChatMember.channel_id == channel_id
        )
        exists = bool(query.scalar())
        return exists

    @property
    def name(self):
        user_info = list()
        user_info.append(self.user.username)
        if self.user.name is not None:
            user_info.append(self.user.name)
        return user_info[-1]

    def is_ratio_worse_then_channel(self, ratio_dict: dict = None):
        ratio_dict = ratio_dict or self.exception_ratio
        if not ratio_dict:
            return False

        channel_ratio = self.channel.ratio()

        if channel_ratio is None:
            return True

        messages_for_friends = ratio_dict["messages_for_friends"]
        friends_required = ratio_dict["friends_required"]

        ratio = round(messages_for_friends / friends_required, 2)
        return ratio < channel_ratio

    async def get_messages_left(self):
        channel = self.channel

        if self.is_ratio_worse_then_channel():
            messages_given = 0
        else:
            messages_given = channel.messages_given or "infinite"

        messages_for_friends = channel.messages_for_friends(self)
        friends_required = channel.friends_required(self)

        if messages_given == "infinite" or (
                not friends_required and messages_for_friends):
            return "infinite"

        if not messages_for_friends:
            return 0

        ratio = round((messages_for_friends / friends_required), 2)

        if not ratio:
            return 0

        friends_added_total = await self.get_invites_count()
        messages_sent = await self.messages_sent

        allowed_messages = self.max_message_limit + \
                           messages_given + \
                           round(friends_added_total * ratio) - \
                           messages_sent

        if allowed_messages < 0:
            allowed_messages = 0

        return allowed_messages

    async def get_limits_info_texts(self) -> Tuple[str | int, str | int, str | int]:
        """
        Method to get info about allowed messages, messages for friends and friends
        required

        :return: Tuple of allowed messages, messages for friends and friends required
        :rtype: Tuple[str | int, str | int, str | int]
        """
        lang = await self.user.get_lang()

        channel = self.channel

        friends_required = channel.friends_required(self)
        messages_for_friends = channel.messages_for_friends(self)

        if all([friends_required, messages_for_friends]):
            allowed_messages = await self.get_messages_left()
        else:
            if not friends_required:
                allowed_messages = models.Channel.INFINITE_MESSAGES
            else:
                allowed_messages = 0
            messages_for_friends, friends_required = await empty_value(
                lang
            ), await empty_value(lang)

        return allowed_messages, messages_for_friends, friends_required

    @property
    def user_fullname(self):
        return self.user.name

    @property
    def channel_fullname(self):
        return self.channel.name

    @staticmethod
    @db_func
    def get_member_by_username(username: str, channel_id: int):
        query = sess().query(ChatMember)
        query = query.join(models.User, ChatMember.user_id == models.User.id)
        query = query.join(models.Channel, ChatMember.channel_id == models.Channel.id)
        chat_member = query.filter(
            models.User.username == username, models.Channel.id == channel_id
        ).one_or_none()
        return chat_member

    @db_func
    def update_messages_sent_without_rules(self):
        self.messages_sent_without_rules += 1
        sess().commit()

    @db_func
    def update_limit(self, new_ratio: dict):
        self.exception_ratio = new_ratio
        if new_ratio:
            self.limited_datetime = datetime.utcnow()
        sess().commit()

    async def set_limit(self, ratio: dict | None) -> bool:
        if not ratio:
            ratio = None

        if ratio and not all(
                key in ratio.keys() for key in
                ["messages_for_friends", "friends_required"]
        ):
            raise ValueError(
                "Ratio must contain keys \"messages_for_friends\" and "
                "\"friends_required\" or be empty"
            )

        await self.update_limit(ratio)
        if ratio:
            await models.FriendlyBotAnalyticAction.save_user_limited(member_id=self.id)

        return True

    async def increase_limit(self):
        ratio_dict = self.channel.ratio_dict(self)
        ratio_dict["friends_required"] += 1
        return await self.set_limit(ratio_dict)

    async def delete_limit(self) -> bool:
        return await self.set_limit(None)

    @property
    def messages_for_friends(self) -> int | None:
        exception_ratio = self.exception_ratio
        if exception_ratio:
            return exception_ratio.get("messages_for_friends")

    @property
    def friends_required(self) -> int | None:
        exception_ratio = self.exception_ratio
        if exception_ratio:
            return exception_ratio.get("friends_required")

    @property
    def ratio_dict(self) -> Dict[str, int | float | None]:
        ratio_dict = dict()
        ratio_dict["messages_for_friends"] = self.channel.messages_for_friends(self)
        ratio_dict["friends_required"] = self.channel.friends_required(self)
        return ratio_dict

    @db_func
    def _get_friends_added(self, *, today: bool = False) -> int:
        """
        This method created to make db queries for properties

        :rtype: int
        """

        query = sess().query(func.count(models.FriendlyBotAnalyticAction.id))

        query = query.filter(models.FriendlyBotAnalyticAction.recommender_id == self.id)
        query = query.filter(models.FriendlyBotAnalyticAction.type == "user_joined")

        if today:
            midnight = get_midnight(self.channel.timezone)
            query = query.filter(models.FriendlyBotAnalyticAction.datetime >= midnight)

        count = query.scalar()
        return count

    @property
    def refresh_datetime(self):
        return self.channel.refresh_datetime

    @db_func
    def _get_messages_sent(self, with_unlimited: bool = False) -> int:
        """
        This method created to make db queries for properties

        :rtype: int
        """

        query = sess().query(func.count(models.FriendlyBotAnalyticAction.id))

        if with_unlimited:
            query = query.filter(
                or_(
                    models.FriendlyBotAnalyticAction.type == "message_sent",
                    models.FriendlyBotAnalyticAction.type == "message_sent∞",
                )
            )
        else:
            query = query.filter(
                models.FriendlyBotAnalyticAction.type == "message_sent"
            )

        query = query.filter(models.FriendlyBotAnalyticAction.member_id == self.id)
        query = query.filter(models.FriendlyBotAnalyticAction.is_deleted.is_(False))

        query = query.filter(
            models.FriendlyBotAnalyticAction.datetime >= self.refresh_datetime
        )

        if self.channel.refresh_messages_daily:
            today_date = datetime.utcnow().date()
            query = query.filter(
                func.date(models.FriendlyBotAnalyticAction.datetime) == today_date
            )

        messages_sent = query.scalar()
        return messages_sent

    @property
    async def messages_sent_with_unlimited(self) -> int:
        return await self._get_messages_sent(with_unlimited=True)

    @property
    async def messages_sent(self) -> int:
        return await self._get_messages_sent()

    @property
    async def today_friends_added(self):
        return await self._get_friends_added(today=True)

    @db_func
    def left(self):
        self.left_datetime = datetime.utcnow()
        sess().commit()
        return self

    @db_func
    def returned(self):
        self.joined_datetime = datetime.utcnow()
        sess().commit()
        return self

    @db_func
    def agreed_rules(self):
        self.is_agreed_rules = True
        sess().commit()

    @classmethod
    @db_func
    def set_ban_users_status(cls, users: Union[List[int], int], status: bool) -> bool:
        query = sess().query(cls)
        if isinstance(users, list):
            query = query.filter(cls.id.in_(users))
        elif isinstance(users, int):
            query = query.filter(cls.id == users)

        for chat_member in query.all():
            chat_member.ban_user_status = status
        sess().commit()
        return True

    @db_func
    def set_rules_start_datetime(self):
        self.rules_start_datetime = datetime.utcnow()
        sess().commit()
        return self

    @db_func
    def get_invites_count(self) -> int:
        query = sess().query(func.count(ChatMember.id))
        query = query.filter(ChatMember.inviter_id == self.id)
        if self.is_ratio_worse_then_channel() and self.limited_datetime:
            query = query.filter(
                ChatMember.user.has(models.User.date_joined >= self.limited_datetime)
            )
        count = query.scalar()
        return count

    def check_rules_agreement(self):
        if self.is_agreed_rules:
            return True

        channel = self.channel

        if not channel.need_agree_rules:
            return True

        if not channel.is_chat_rules_ban:
            return True

        # Функционал пока отложили
        # if channel.is_chat_rules_ban_new and self.joined_datetime <
        # channel.need_agree_rules_set_datetime:
        #     return True

        return False

    @db_func
    def change_favorite(self) -> bool:
        self.is_favorite = not self.is_favorite
        sess().commit()
        return True

    @db_func
    def set_max_message_limit(self, max_message_limit: int) -> bool:
        self.max_message_limit = max_message_limit
        sess().commit()
        return True
