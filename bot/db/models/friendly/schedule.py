import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Literal

from psutils.convertors import time_period_to_str
from sqlalchemy import (
    BigInteger, Boolean, Column, Date, DateTime, ForeignKey, Integer,
    String, Text, Time, func,
)
from sqlalchemy.orm import backref, relationship

import config as cfg
from db import models
from db.connection import Base
from db.decorators import db_func
from db.helpers import sess
from utils.media import delete_file


class Post(Base):

    __tablename__ = "posts"

    id = Column(BigInteger, primary_key=True, autoincrement=True)

    schedule_id = Column(
        BigInteger, ForeignKey("schedules.id", ondelete="CASCADE"), nullable=False
    )
    schedule = relationship(
        "Schedule", backref=backref("posts", cascade="all,delete"), passive_deletes=True
    )

    creator_id = Column(
        BigInteger, ForeignKey("users.id", ondelete="RESTRICT"), nullable=False
    )
    creator = relationship("User", backref="created_posts")

    time_created = Column(DateTime(timezone=True), default=datetime.utcnow)

    content_type = Column(String(15))

    text = Column(Text(collation="utf8mb4_unicode_ci"), default=None)
    media_path = Column(String(512), default=None, nullable=True)

    need_contact_button = Column(Boolean, default=True)
    is_show_web_page_preview = Column(Boolean, default=False)
    is_show_html_tag = Column(Boolean, default=False)

    footer_id = Column(
        BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True,
        default=None
    )
    footer = relationship("Footer", foreign_keys=footer_id, backref=backref("posts"))

    SUPPORTED_MESSAGE_TYPES = cfg.MEDIA_WITH_CAPTION + ["text", "sticker"]

    def __init__(
            self,
            schedule: "Schedule",
            creator: "models.User",
            content_type: str,
            text: str = None,
            media_path: str = None,
    ):
        assert schedule
        assert creator
        assert content_type
        assert text or media_path

        self.schedule = schedule
        self.creator = creator
        self.content_type = content_type
        self.text = text
        self.media_path = media_path

    @classmethod
    @db_func
    def create(
            cls,
            schedule: "Schedule",
            creator: "models.User",
            content_type: str, text: str = None,
            media_path: str = None,
            **kwargs
    ):

        if not isinstance(schedule, Schedule):
            raise ValueError(f"schedule must be typeof Schedule, not {type(schedule)}")

        if not text and not media_path:
            raise ValueError(
                f"one of text and media_path must be specified. Now: {text = }, "
                f"{media_path = }"
            )

        post = cls(schedule, creator, content_type, text, media_path)
        sess().add(post)
        sess().commit()
        return post

    @staticmethod
    @db_func
    def get(post_id: int) -> "Post":
        return sess().query(Post).filter(Post.id == post_id).one()

    @property
    def channel(self) -> "models.Channel":
        return self.schedule.channel

    @property
    def channel_id(self) -> int:
        return self.schedule.channel.id

    @property
    def kwargs_for_send(self) -> Dict[str, str]:
        content_type = self.content_type
        kwargs = dict(content_type=content_type)
        kwargs.update(text=self.text)
        kwargs.update(disable_web_page_preview=not self.is_show_web_page_preview)
        if content_type != "text":
            kwargs[content_type] = self.media_path
        return kwargs

    @property
    def kwargs_for_copy(self) -> Dict[str, str]:
        content_type = self.content_type
        kwargs = dict(content_type=content_type)
        kwargs.update(text=self.text)
        if content_type != "text":
            kwargs.update(media_path=self.media_path)
        return kwargs

    @db_func
    def is_show_web_page_preview_button(self):
        self.is_show_web_page_preview = not self.is_show_web_page_preview
        sess().commit()
        return True

    @db_func
    def is_show_html_tags_button(self):
        self.is_show_html_tag = not self.is_show_html_tag
        sess().commit()
        return True

    @db_func
    def delete(self) -> bool:
        if self.media_path:
            delete_file(self.media_path)

        sess().delete(self)
        sess().commit()
        return True

    @db_func
    def set_footer(self, footer: "models.Footer") -> bool:
        if footer is None or self.footer_id == footer.id:
            self.footer_id = None
        else:
            self.footer_id = footer.id
        sess().commit()
        return True


class Schedule(Base):

    __tablename__ = "schedules"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    _name = Column(String(20), default=None)
    status = Column(String(20), default="active")

    # Варианты типов расписаний: ("by_time", "between_posts",)
    schedule_type = Column(String(20))

    start_date = Column(Date, default=date.today)
    # эти поля хранятся в Channel.timezone
    start_time = Column(Time)
    end_time = Column(Time)

    count_days = Column(Integer, nullable=True, default=None)
    frequency_message = Column(Integer, default=100)
    count_messages = Column(Integer, default=0)
    index_send_message = Column(Integer, default=0)

    last_sent_post_datetime = Column(DateTime(timezone=True), default=datetime.utcnow)

    channel_id = Column(BigInteger, ForeignKey("channels.id", ondelete="CASCADE"))
    channel = relationship("Channel", back_populates="schedules")

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    user = relationship("User", backref="schedules")

    friendly_chat_message = relationship(
        "FriendlyChatMessage", back_populates="schedule", uselist=False
    )

    footer_id = Column(
        BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True,
        default=None
    )
    footer = relationship(
        "Footer", foreign_keys=footer_id, backref=backref("schedules")
    )

    is_public = Column(Boolean, default=False)
    related_channels = relationship(
        "Channel",
        secondary="schedules_channels_association",
        back_populates="related_schedules",
    )

    FIELDS_TO_UPDATE = ["status", "schedule_type", "name", "start_time", "end_time",
                        "count_days", "frequency_message"]

    @staticmethod
    @db_func
    def create(
            start_time: datetime.time, end_time: datetime.time,
            frequency_message: int, channel_id: int, user_id: int,
            schedule_type: Literal["between_posts", "by_time"],
            name: str = None, count_days: int = None,
            start_date: datetime.date = None
    ) -> "Schedule":
        schedule = Schedule(
            schedule_type=schedule_type,
            count_days=count_days,
            frequency_message=frequency_message,
            start_date=start_date,
            start_time=start_time,
            end_time=end_time,
            channel_id=channel_id,
            user_id=user_id,
            _name=name
        )
        sess().add(schedule)
        sess().commit()
        return schedule

    @property
    def lang(self) -> str:
        return self.channel.group.lang

    @property
    def publish_time(self) -> str:
        period = time_period_to_str(self.lang, self.start_time, self.end_time)
        return period

    @property
    def name(self) -> str:
        name = self._name
        if not name:
            name = time_period_to_str(self.lang, self.start_time, self.end_time)
        return name

    @name.setter
    def name(self, value: str):
        self._name = value

    @db_func
    def counter_messages(
            self,
            add_messages: int | None = None,
            reset: bool = False,
            association: "models.SchedulesChannelsAssociation" = None
    ) -> bool:
        object = association if association else self

        if reset:
            if not association:
                models.SchedulesChannelsAssociation.reset(self.id)

            object.count_messages = 0
            object.index_send_message = 0
        elif not add_messages:
            object.count_messages += 1
        else:
            object.count_messages = add_messages * (object.index_send_message + 1)
        sess().commit()
        return True

    @db_func
    def _update(self, **kwargs) -> bool:
        for field_name, value in kwargs.items():
            if field_name not in dir(self):
                continue
            setattr(self, field_name, value)
        sess().commit()
        return True

    async def update(self, **kwargs) -> bool:
        posts = kwargs.pop("posts", None)

        if posts:
            logger = logging.getLogger("warning")
            logger.warning("Posts specified in Schedule.update", exc_info=True)

        result = await self._update(**kwargs)
        if not result:
            return result

        return True

    async def add_posts(
            self, *posts_data: Dict[str, str] | List[Dict[str, str]],
            creator: "models.User"
    ) -> List[Post]:
        if type(posts_data[0]) is list and len(posts_data) != 1:
            raise ValueError(
                "You can provide only one argument as list of post_data or many "
                "params as post_data dict"
            )

        if type(posts_data[0]) is list:
            posts_data = posts_data[0]

        posts = [await self.add_post(**post_data, creator=creator) for post_data in
                 posts_data]

        return posts

    async def add_post(self, content_type: str, **post_data: Dict[str, str]) -> Post:
        creator = post_data.pop("creator", None)
        post = await Post.create(self, creator, content_type, **post_data)
        return post

    @property
    def posts_count(self):
        return len(self.posts)

    def get_post(
            self, last: bool = False,
            association: "models.SchedulesChannelsAssociation" = None
    ) -> Post | None:
        object = association if association else self

        if not self.posts:
            return

        index = -1 if last else object.index_send_message

        if index == -1:
            return self.posts[index]

        elif index >= self.posts_count:
            index = 0

        return self.posts[index]

    @db_func
    def get_old_posts_from_chat(self) -> List[int]:
        now = datetime.utcnow().timestamp()
        deletion_window = timedelta(hours=47).total_seconds()
        query = sess().query(models.FriendlyChatMessage.id)
        query = query.filter(models.FriendlyChatMessage.schedule_id == self.id)
        query = query.filter(
            (func.unix_timestamp(
                models.FriendlyChatMessage.datetime
            ) + deletion_window) >= now
            )
        schedule_posts = query.all()
        schedule_posts = [el[0] for el in schedule_posts]
        return schedule_posts

    @db_func
    def activate(self):
        self.status = "active"
        self.start_date = date.today()
        sess().commit()

    @db_func
    def stop(self):
        self.status = "stopped"
        sess().commit()
        return True

    @db_func
    def set_type(self, _type: Literal["between_posts", "by_time"]):
        self.schedule_type = _type
        sess().commit()

    @db_func
    def set_start_date(self, start_date: date | datetime):
        if type(start_date) is datetime:
            start_date = start_date.date()
        self.start_date = start_date

        if self.end_time:
            self.end_time = None
        sess().commit()
        return True

    @db_func
    def set_start_date_time_now(self):
        now = datetime.utcnow()

        self.start_date = now.date()
        self.start_time = now.time()

        if self.end_time:
            self.end_time = None

        sess().commit()

    @staticmethod
    @db_func
    def get(schedule_id: int):
        schedule = sess().query(Schedule).filter(
            Schedule.id == schedule_id
            ).one_or_none()
        return schedule

    @staticmethod
    @db_func
    def get_channel_schedules(
            channel_id: int,
            position: int = 0, limit: int = None,
            operation: Literal["all", "count"] = "all",
            search_text: str = None,
            public: bool = False
    ) -> List["Schedule"] | int:

        query = sess().query(Schedule)

        if public:
            query = query.filter(Schedule.is_public.is_(True))
        else:
            query = query.filter(Schedule.channel_id == channel_id)

        if search_text:
            query = query.filter(
                Schedule._name.contains(search_text)
            )

        if operation == "count":
            return query.with_entities(func.count(Schedule.id)).scalar()

        slice_args = [position, None]
        if limit:
            slice_args[1] = position + limit
        query = query.slice(*slice_args)
        return query.all()

    @db_func
    def post_sent(
            self, last: bool = False,
            association: "models.SchedulesChannelsAssociation" = None
    ):
        object = association if association else self

        object.last_sent_post_datetime = datetime.utcnow()
        index = 0 if last else object.index_send_message + 1
        if index >= self.posts_count:
            object.index_send_message = 0
        else:
            object.index_send_message = index

        if "by_time" == self.schedule_type:
            object.count_messages += 1
        elif "between_posts" == self.schedule_type:
            object.count_messages = 0

        sess().commit()
        return True

    @db_func
    def get_media_count(self) -> int:
        count = sess().query(Post).join(
            Schedule, Post.schedule_id == self.id
                      ).distinct().count()
        return count

    @db_func
    def _delete_posts(self):
        query = sess().query(Post)
        query = query.filter(Post.schedule_id == self.id)
        query.delete(synchronize_session="fetch")
        sess().commit()

    @db_func
    def _delete_friendly_chat_messages(self):
        query = sess().query(models.FriendlyChatMessage)
        query = query.filter(models.FriendlyChatMessage.schedule_id == self.id)
        query.delete(synchronize_session="fetch")
        sess().commit()

    @db_func
    def _delete(self):
        sess().delete(self)
        sess().commit()

    async def delete(self):
        await self._delete_posts()
        await self._delete_friendly_chat_messages()
        await self._delete()
        return True

    @property
    def posts_for_copy(self) -> List[Dict[str, str]]:
        return list(map(lambda post: post.kwargs_for_copy, self.posts))

    @db_func
    def set_footer(self, footer: "models.Footer") -> bool:
        if footer is None or self.footer_id == footer.id:
            self.footer_id = None
        else:
            self.footer_id = footer.id
        sess().commit()
        return True
