from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON>umn, BigInteger, String, DateTime, Index
from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy.orm import relationship

from aiogram import types

from db import models
from db.connection import Base
from db.helpers import sess
from db.decorators import db_func


class FriendlyChatMessage(Base):

    __tablename__ = "friendly_chat_messages"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    datetime = Column(DateTime(timezone=True), default=datetime.utcnow)

    message_type = Column(String(29))

    chat_id = Column(BigInteger)
    message_id = Column(BigInteger)
    user_first_name = Column(String(256, collation="utf8mb4_unicode_ci"), default=None)

    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot = relationship("ClientBot")

    schedule_id = Column(BigInteger, ForeignKey("schedules.id", ondelete="CASCADE"))
    schedule = relationship("Schedule", back_populates="friendly_chat_message", uselist=False)

    member_id = Column(BigInteger, ForeignKey("chats_members.id", ondelete="CASCADE"))
    member = relationship("ChatMember", foreign_keys=member_id)

    __table_args__ = (
        Index('fcm_message_type_chat_id_dt', 'message_type', 'chat_id', 'datetime'),
    )

    def __lt__(self, other):
        return self.bot_id < other.bot_id

    def __init__(
            self,
            message_id: int,
            chat_id: int,
            message_type: str,
            bot: "models.ClientBot",
            schedule: Optional["models.Schedule"],
            member: Optional["models.ChatMember"],
            user_first_name: str | None,
    ):
        assert type(message_id) is int
        assert type(chat_id) is int
        assert type(message_type) is str
        assert isinstance(bot, models.ClientBot)
        assert isinstance(schedule, models.Schedule | None)
        assert isinstance(member, models.ChatMember | None)
        assert isinstance(user_first_name, str | None)

        self.message_id = message_id
        self.chat_id = chat_id
        self.message_type = message_type
        self.bot_id = bot.id
        self.schedule_id = schedule.id if schedule else None
        self.member_id = member.id if member else None
        self.user_first_name = user_first_name

    @classmethod
    @db_func
    def save(cls,
             message: types.Message,
             message_type: str,
             bot: "models.ClientBot",
             schedule: "models.Schedule" = None,
             member: "models.ChatMember" = None,
             user_first_name: str = None,
             ):
        if not isinstance(message, types.Message):
            return
        system_message = cls(
            message.message_id,
            message.chat.id,
            message_type,
            bot, schedule, member,
            user_first_name,
        )
        sess().add(system_message)
        sess().commit()

    @classmethod
    @db_func
    def get(cls, friendly_chat_message_id: int) -> Optional["FriendlyChatMessage"]:
        friendly_chat_message = sess().query(cls).filter_by(id=friendly_chat_message_id).one()
        return friendly_chat_message

    @classmethod
    @db_func
    def get_by_telegram_message_id(cls, message_id: int) -> "FriendlyChatMessage":
        query = sess().query(FriendlyChatMessage)
        query = query.filter(FriendlyChatMessage.message_id == message_id)
        return query.one()

    @classmethod
    async def get_chat_message(cls, message: types.Message) -> Optional["FriendlyChatMessage"]:
        message = message.reply_to_message
        if not message:
            return

        if not message.from_user.is_bot:
            return

        bot_from_db = await models.ClientBot.get_current()
        bot_username = bot_from_db.username
        telegram_bot = await message.bot.get_me()
        if telegram_bot.username != bot_username:
            return

        return await cls.get_by_telegram_message_id(message.message_id)

    @db_func
    def delete(self):
        sess().delete(self)
        sess().commit()
        return True
