from datetime import datetime

from sqlalchemy import <PERSON>umn, BigInteger, Foreign<PERSON>ey, Integer, DateTime, String

from db.connection import Base
from db import db_func, sess


class SchedulesChannelsAssociation(Base):

    __tablename__ = "schedules_channels_association"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    schedule_id = Column(BigInteger, ForeignKey("schedules.id", ondelete="CASCADE"))
    channel_id = Column(BigInteger, ForeignKey("channels.id", ondelete="CASCADE"))

    count_messages = Column(Integer, default=0)
    index_send_message = Column(Integer, default=0)
    last_sent_post_datetime = Column(DateTime(timezone=True), default=None, nullable=True)

    status = Column(String(20), default="active")

    @staticmethod
    @db_func
    def get(schedule_id: int, channel_id: int) -> "SchedulesChannelsAssociation":
        query = sess().query(SchedulesChannelsAssociation)

        query = query.filter(SchedulesChannelsAssociation.schedule_id == schedule_id)
        query = query.filter(SchedulesChannelsAssociation.channel_id == channel_id)

        return query.one_or_none()

    @db_func
    def stop(self):
        self.status = "stopped"
        sess().commit()

    @db_func
    def activate(self):
        self.status = "active"
        sess().commit()

    @staticmethod
    def reset(schedule_id: int) -> bool:
        query = sess().query(SchedulesChannelsAssociation)
        query = query.filter(SchedulesChannelsAssociation.schedule_id == schedule_id)
        objects = query.all()

        for object in objects:
            object.count_messages = 0
            object.index_send_message = 0
        sess().commit()

        return True
