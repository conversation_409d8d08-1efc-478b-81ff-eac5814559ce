import asyncio
import logging
import re

from collections import deque
from datetime import datetime, date, timedelta
from typing import Any, Dict, Literal, List, Callable

from aiogram.utils.exceptions import ChatNotFound
from sqlalchemy import Column, Integer, String, BigInteger, Text, Boolean, DateTime
from sqlalchemy import ForeignKey, UniqueConstraint, func, JSON, Float
from sqlalchemy.orm import relationship

from aiogram import types as aiogram_types, types

from db import models
from db.connection import Base
from db.helpers import sess, DBSession
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson
from db.decorators import db_func, own_session

from utils.text import f, c
from utils.redefined_classes import Bot, InlineBtn
from utils.message import send_tg_message

from config import USE_LOCALISATION


class Channel(Base, BaseDBModel):

    __tablename__ = "channels"

    id = Column(BigInteger, primary_key=True, autoincrement=True)

    name = Column(String(256, collation="utf8mb4_unicode_ci"))
    username = Column(String(256, collation="utf8mb4_unicode_ci"), default=None)
    chat_id = Column(BigInteger)  # removed unique=true

    admins = relationship(
        "User",
        secondary="admins_channels_association",
        back_populates="admined_channels"
    )

    chat_members = relationship("ChatMember", back_populates="channel")

    schedules = relationship("Schedule", cascade="all, delete", back_populates="channel")

    chat_type = Column(String(20))
    messages_given = Column(Integer, default=2)
    allowed_messages_given_without_rules = Column(Integer, default=1)
    max_messages_length = Column(Integer, default=0)
    message_ratio = Column(JSON, default={"friends_required": 1, "messages_for_friends": 1})
    pending_exceptions = Column(Text, default='{}')
    alert_threshold = Column(Integer, default=1)
    refresh_datetime = Column(DateTime(timezone=True), default=datetime.utcnow)
    _info_message = Column(NestedMutableJson, default=None)
    _welcome_message = Column(NestedMutableJson, default=None)
    _deletion_message = Column(NestedMutableJson, default=None)
    _restriction_message = Column(NestedMutableJson, default=None)
    _alert_message = Column(NestedMutableJson, default=None)
    _ban_sender_chat_message = Column(NestedMutableJson, default=None)
    _rules_message = Column(NestedMutableJson, default=dict)
    _rules_ban_message = Column(NestedMutableJson, default=None)
    _required_subscription_message = Column(NestedMutableJson, default=None)
    _channel_menu_text_message = Column(NestedMutableJson, default=None)
    rules_button_text = Column(Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION)
    info_button_text = Column(Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION)
    contact_admin_button_text = Column(Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION)
    refresh_messages_daily = Column(Boolean, default=True)
    system_message_on_screen_time = Column(Float, default=60)
    limited_message_on_screen_time = Column(Float, default=60)
    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: "models.Group" = relationship("Group", uselist=False, back_populates="channel")
    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="RESTRICT"))
    bot = relationship("ClientBot", backref="channels")
    attach_datetime = Column(DateTime(timezone=True), default=datetime.utcnow)
    need_delete_join_notification = Column(Boolean, default=True)
    need_delete_leave_notification = Column(Boolean, default=True)
    filter_words_username = Column(NestedMutableJson, default=None)
    filter_words_message = Column(NestedMutableJson, default=None)
    need_agree_rules = Column(String(10), default="nobody")  # "new", "all", "nobody"
    need_agree_rules_set_datetime = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=True)
    limited_agree_rules_time = Column(Integer, default=10, nullable=True)
    is_bot_admin = Column(Boolean, default=True)
    ban_threshold = Column(Integer, default=10)
    ban_interval = Column(Float, default=20)
    need_delete_restriction_notification = Column(Boolean, default=True)
    required_resource_subscription_chat_id = Column(Integer, default=0)
    last_required_resource_msg_id = Column(Integer, nullable=True, default=None)

    _status_info_message = Column(String(10), default="show")
    _status_welcome_message = Column(String(10), default="disabled")
    _status_deletion_message = Column(String(10), default="show")
    _status_restriction_message = Column(String(10), default="show")
    _status_alert_message = Column(String(10), default="show")
    _status_ban_sender_chat_message = Column(String(10), default="show")
    _status_rules_message = Column(String(10), default="disabled")
    _status_rules_ban_message = Column(String(10), default="show")
    _status_required_subscription_message = Column(String(10), default="show")

    welcome_message_rate_limit = Column(Float, default=7)
    alert_message_rate_limit = Column(Float, default=7)
    deletion_message_rate_limit = Column(Float, default=7)
    restriction_message_rate_limit = Column(Float, default=7)
    required_subscription_message_rate_limit = Column(Float, default=10)

    welcome_message_last_sent_datetime = Column(DateTime(timezone=True))
    alert_message_last_sent_datetime = Column(DateTime(timezone=True))
    deletion_message_last_sent_datetime = Column(DateTime(timezone=True))
    restriction_message_last_sent_datetime = Column(DateTime(timezone=True))
    required_subscription_message_last_sent_datetime = Column(DateTime(timezone=True))

    _info_footer_id = Column(BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True, default=None)
    _info_footer = relationship("Footer", foreign_keys=_info_footer_id)
    _welcome_footer_id = Column(BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True, default=None)
    _welcome_footer = relationship("Footer", foreign_keys=_welcome_footer_id)
    _deletion_footer_id = Column(BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True, default=None)
    _deletion_footer = relationship("Footer", foreign_keys=_deletion_footer_id)
    _restriction_footer_id = Column(
        BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True, default=None
    )
    _restriction_footer = relationship("Footer", foreign_keys=_restriction_footer_id)
    _alert_footer_id = Column(BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True, default=None)
    _alert_footer = relationship("Footer", foreign_keys=_alert_footer_id)
    _ban_sender_chat_footer_id = Column(
        BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True, default=None
    )
    _ban_sender_chat_footer = relationship("Footer", foreign_keys=_ban_sender_chat_footer_id)
    _rules_footer_id = Column(BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True, default=None)
    _rules_footer = relationship("Footer", foreign_keys=_rules_footer_id)
    _rules_ban_footer_id = Column(
        BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True, default=None
    )
    _rules_ban_footer = relationship("Footer", foreign_keys=_rules_ban_footer_id)
    _required_subscription_footer_id = Column(
        BigInteger, ForeignKey("footers.id", ondelete="SET NULL"), nullable=True, default=None
    )
    _required_subscription_footer = relationship("Footer", foreign_keys=_required_subscription_footer_id)

    max_count_messages_before_required_resource = Column(Integer, default=10)

    need_welcome_system_keyboard = Column(Boolean, default=True)

    _logs = Column(NestedMutableJson, default=list)

    related_schedules = relationship(
        "Schedule",
        secondary="schedules_channels_association",
        back_populates="related_channels",
    )

    __table_args__ = (
        UniqueConstraint('bot_id', 'chat_id'),
    )

    NOTIFICATION_TYPES: List[Literal["join", "leave"]] = ["join", "leave"]
    FILTER_CONTENT_TYPES: List[Literal["username", "message"]] = ["username", "message"]
    EDITABLE_MESSAGE_TYPES = ["welcome", "deletion", "info", "restriction", "alert", "rules", "rules_ban"]
    EDITABLE_MESSAGE_TYPES_FILTER: List[str] = list(map(lambda x: f"{x}_message", EDITABLE_MESSAGE_TYPES))

    INFINITE_MESSAGES = "∞"

    def __init__(
            self,
            chat_id: int,
            chat_type: str,
            name: str,
            bot: "models.ClientBot",
            username: str | None,
    ):
        assert chat_id
        assert chat_type
        assert name
        assert bot

        self.id: int
        self.chat_id = chat_id
        self.chat_type = chat_type
        self.name = name
        self.bot = bot
        self.username = username

        super().__init__()

    @db_func
    def set_button_text(
            self,
            button_type: Literal[
                "rules", "info", "contact_admin"
            ],
            value: str
    ):
        setattr(self, f"{button_type}_button_text", value)
        sess().commit()

    @db_func
    def set_ban_threshold(self, value: int):
        self.ban_threshold = value
        sess().commit()

    @db_func
    def set_ban_interval(self, value: float):
        self.ban_interval = value
        sess().commit()

    @db_func
    def set_limited_agree_rules_time(self, value: int):
        self.limited_agree_rules_time = value
        sess().commit()

    @classmethod
    def get_sync(
            cls,
            id: int | None = None, *,
            for_update: bool = False,
            release_on_exists: bool = False,
            **kwargs,
    ) -> "Channel | None":
        if not kwargs.get("bot_id"):
            kwargs["bot_id"] = models.ClientBot.get_current_bot_id()
        return super().get_sync(id, for_update=for_update, release_on_exists=release_on_exists, **kwargs)

    @staticmethod
    @db_func
    def get_all() -> List["Channel"]:
        return sess().query(Channel).all()

    @db_func
    def delete_user_from_admins(self, user: "models.User"):
        if user in self.admins:
            self.admins.remove(user)
            sess().commit()

    @db_func
    def add_user_to_admins(self, user: "models.User"):
        if user not in self.admins:
            self.admins.append(user)
            sess().commit()

    @property
    def timezone(self) -> str:
        return self.group.timezone

    @property
    def lang(self) -> str:
        return self.group.lang

    @db_func
    def get_members_count_by_action(
            self, types: List[str] | str,
            from_date: date,
            to_date: date = None
    ) -> int:
        if not isinstance(types, list):
            types = [types]

        query = sess().query(func.count(models.FriendlyBotAnalyticAction.id))
        query = query.filter(models.FriendlyBotAnalyticAction.channel_id == self.id)
        query = query.filter(models.FriendlyBotAnalyticAction.type.in_(types))

        if to_date:
            query = query.filter(func.date(models.FriendlyBotAnalyticAction.datetime <= to_date))
        query = query.filter(func.date(models.FriendlyBotAnalyticAction.datetime) >= from_date)

        result = query.scalar()
        return result

    async def get_members_joined(self, from_date: date, to_date: date = None) -> int:
        types = ["user_joined", "user_return"]
        members_joined = await self.get_members_count_by_action(types, from_date, to_date)
        return members_joined

    async def get_members_left(self, from_date: date, to_date: date = None) -> int:
        members_joined = await self.get_members_count_by_action("user_left", from_date, to_date)
        return members_joined

    async def get_limited_members(self, operation: str = "all"):
        return await models.ChatMember.get_with_ratio(channel_id=self.id, operation=operation)

    def get_info_link(self, bot_username: str) -> str:
        return f"https://t.me/{bot_username}?start=info_chid-{self.id}"

    async def get_saldo(self, from_date: date, to_date: date = None):
        async def run_func(channel_id: int, method: Callable):
            with DBSession():
                channel = await Channel.get(channel_id)
                return await method(channel, from_date, to_date)

        coros = run_func(self.id, Channel.get_members_joined), run_func(self.id, Channel.get_members_left)
        results: List[int] = list(await asyncio.gather(*coros))
        joined, left = results
        saldo = joined - left
        return saldo

    async def get_message(
            self,
            message_type: Literal[
                "welcome", "deletion",
                "info", "restriction", "alert",
                "ban_sender_chat",
                "rules", "rules_ban",
            ],
            add_at_to_username: bool = False,
            **kwargs
    ) -> dict:
        if "user_first_name" in kwargs:
            user_first_name = await self.validate_user_first_name(kwargs.pop("user_first_name"))
            kwargs.update(user_first_name=user_first_name)

        if self.get_chat_message_status(message_type, "default"):
            text = await f(f"{message_type}_message", self.lang, **kwargs)
            return {"text": text, "content_type": "text"}

        message_type = f"{message_type}_message"

        message_data = getattr(self, "_" + message_type)

        if not message_data:
            message_text = await f(message_type, self.lang, **kwargs)
            message_data = {"text": message_text}
        else:
            message_data: dict = dict(**message_data)
            message_text = message_data.get("text", "")
            for k, v in kwargs.items():
                key = "{" + k + "}"
                replace_value = f"@{str(v)}" if add_at_to_username and key == "{username}" else str(v)
                message_text = message_text.replace(key, replace_value)
            message_data["text"] = message_text

        text = message_data.get("text", "")
        if text:
            text = re.sub(r"(?<=<a href) ?= ?(?=\")", "=", text)

        message_data["text"] = text

        message_data.update(content_type=list(message_data.keys())[-1], disable_web_page_preview=True)
        return message_data

    def friends_required(self, chat_member: "models.ChatMember" = None) -> int:
        if chat_member is None or not chat_member.exception_ratio:
            value = self.message_ratio.get("friends_required")
        else:
            value = chat_member.friends_required
        return value or 0

    def messages_for_friends(self, chat_member: "models.ChatMember" = None) -> int:
        if chat_member is None or not chat_member.exception_ratio:
            value = self.message_ratio.get("messages_for_friends")
        else:
            value = chat_member.messages_for_friends
        return value or 0

    def ratio(self, chat_member: "models.ChatMember" = None) -> float | int | None:
        messages_for_friends = self.messages_for_friends(chat_member)
        friends_required = self.friends_required(chat_member)
        if not friends_required:
            return

        return round(messages_for_friends / friends_required, 2) if friends_required else 0

    def ratio_dict(self, chat_member: "models.ChatMember" = None) -> Dict[str, int]:
        messages_for_friends = self.messages_for_friends(chat_member)
        friends_required = self.friends_required(chat_member)
        return dict(messages_for_friends=messages_for_friends, friends_required=friends_required)

    def get_filter(self, filter_type: Literal["username", "message"]) -> List[str]:
        filters = getattr(self, f"filter_words_{filter_type}")
        return filters if filters else list()

    @db_func
    def set_message(
            self,
            message_type: Literal[
                "welcome", "deletion",
                "info", "restriction", "alert",
                "ban_sender_chat", "rules", "rules_ban",
            ],
            new_message_data: dict
    ):
        setattr(self, f"_{message_type}_message", new_message_data)
        sess().commit()

    async def is_user_has_permission(self, user: "models.User", bot_from_db_id: int):
        return await user.is_friendly_super_admin(bot_from_db_id) or user in self.admins

    async def get_channel_button(self, lang: str) -> InlineBtn:

        button_text = await f("friendly chat link button", lang, chat_name=self.name)

        username = self.username
        if not username:
            bot = Bot.get_current()
            try:
                chat = await bot.get_chat(chat_id=self.chat_id)
            except ChatNotFound:
                username = None
                await self.set_bot_is_not_admin()
            except Exception as e:
                logger = logging.getLogger()
                logger.error(e, exc_info=True)
                username = None
            else:
                username = chat.username

        if username:
            return InlineBtn(button_text, url=f"t.me/{username}")
        else:
            callback_data = c("channel_info", channel_id=self.id)
            return InlineBtn(button_text, callback_data=callback_data)

    async def get_button_text(
            self,
            button_type: Literal[
                "rules", "info", "contact_admin"
            ],
            lang: str
    ) -> str:
        button_text = getattr(self, f"{button_type}_button_text")
        if button_text == USE_LOCALISATION:
            button_text = await f(f"channel {button_type} button", lang)
        return button_text

    @property
    def is_chat_rules_ban_all(self) -> bool:
        return True if self.need_agree_rules == "all" else False

    # Функционал пока отложили
    # @property
    # def is_chat_rules_ban_new(self) -> bool:
    #     return True if self.need_agree_rules == "new" else False

    @property
    def is_chat_rules_ban(self) -> bool:
        return True if self.need_agree_rules != "nobody" else False

    def get_deleting_notification_status(self, notification_type: Literal["join", "leave", "restriction"]) -> bool:
        return getattr(self, f"need_delete_{notification_type}_notification")

    @db_func
    def change_deleting_notification_status(self, notification_type: Literal["join", "leave", "restriction"]) -> bool:
        old_value = self.get_deleting_notification_status(notification_type)
        field = f"need_delete_{notification_type}_notification"
        setattr(self, field, not old_value)
        sess().commit()
        return True

    @db_func
    def change_chat_rules_status(self, users_type: Literal["all", "new", "nobody"]):
        if self.need_agree_rules == users_type:
            users_type = "nobody"
        # Пока не используем, отложили
        # elif not self.need_agree_rules_set_datetime or users_type == "all":
        #     self.need_agree_rules_set_datetime = datetime.utcnow()
        self.need_agree_rules = users_type
        sess().commit()

    def get_chat_message_status(self, message_type: str, status_type: str) -> bool:
        status_field_name = f"_status_{message_type}_message"
        status_field = getattr(self, status_field_name, "show")

        if status_field == status_type:
            return True

        if status_field == "all" and status_type in ("show", "default"):
            return True

        return False

    @db_func
    def change_chat_message_status(
            self, message_type: Literal[
                "info", "welcome", "deletion",
                "restriction", "alert", "ban_sender_chat",
                "rules", "rules_ban"
            ], status_type: Literal[
                "show", "default"
            ]
    ):
        status_field_name = f"_status_{message_type}_message"
        status_field = getattr(self, status_field_name)

        if status_field == "all":
            value = "show" if status_type == "default" else "disabled"
        elif status_field == "disabled":
            value = status_type
        elif status_field == status_type:
            value = "disabled" if status_type == "show" else "show"
        else:
            value = "all"
        setattr(self, status_field_name, value)
        sess().commit()

    @db_func
    def set_system_message_on_screen_time(self, delay: float) -> bool:
        self.system_message_on_screen_time = delay
        sess().commit()
        return True

    @db_func
    def set_new_alert_threshold(self, new_alert_threshold: int) -> bool:
        self.alert_threshold = new_alert_threshold
        sess().commit()
        return True

    @db_func
    def set_new_allowed_messages(self, new_allowed_messages: int = 2) -> bool:
        self.messages_given = new_allowed_messages
        sess().commit()
        return True

    @db_func
    def set_new_allowed_messages_given_without_rules(self, new_allowed_messages: int = 1) -> bool:
        self.allowed_messages_given_without_rules = new_allowed_messages
        sess().commit()
        return True

    @db_func
    def set_max_messages_length(self, max_messages_length: int = 2) -> bool:
        self.max_messages_length = max_messages_length
        sess().commit()
        return True

    @db_func
    def set_new_ratio(self, messages_for_friends: int, friends_required: int):
        new_ratio = {"messages_for_friends": messages_for_friends, "friends_required": friends_required}
        self.message_ratio = new_ratio
        sess().commit()
        return True

    async def append_filter(self, filter_type: Literal["username", "message"], texts_input: str):
        texts_list = [word.strip() for word in texts_input.split(",") if word.strip()]

        old_list = self.get_filter(filter_type)
        if not old_list:
            old_list = list()

        new_list = old_list + texts_list
        new_list = list(set(new_list))

        await self.update({f"filter_words_{filter_type}": new_list})

        await models.FriendlyBotAnalyticAction.save_filter_added(
            channel_id=self.id, filter_type=filter_type,
            filter_triggered=texts_input,
        )
        return True

    @db_func
    def set_filter(self, filter_type: Literal["username", "message"], new_list: list) -> bool:
        setattr(self, f"filter_words_{filter_type}", new_list)
        sess().commit()
        return True

    @db_func
    def set_limited_message_on_screen_time(self, value: (float, int)):
        self.limited_message_on_screen_time = value
        sess().commit()

    async def validate_user_first_name(self, user_first_name: str) -> str:
        filter_words = self.get_filter("username")
        filter_words_in_name = any(word in user_first_name for word in filter_words) if filter_words else None
        if filter_words_in_name:
            user_first_name = await f("anonymous user", self.lang)
            analytic_kwargs = {
                "filter_trigger": user_first_name,
                "filter_triggered": ", ".join(filter_words),
                "filter_type": "username",
            }
            save_filter_triggered = own_session(models.FriendlyBotAnalyticAction.save_filter_triggered)
            asyncio.ensure_future(save_filter_triggered(**analytic_kwargs))
        return user_first_name

    async def validate_max_length_message(self, message: aiogram_types.Message):
        text = message.text if message.content_type == "text" else message.caption

        if not text:
            return True

        max_messages_length = self.max_messages_length

        if not max_messages_length:
            return True

        if len(text) <= max_messages_length:
            return True
        return False

    async def validate_user_message_is_safe(
            self,
            message: aiogram_types.Message,
            chat_member: "models.ChatMember",
    ) -> bool | str:
        text = message.text if message.content_type == "text" else message.caption
        if not text:
            return True

        filter_words = self.get_filter("message")
        if not filter_words:
            return True
        for word in filter_words:
            if not word.strip():
                continue

            word = re.escape(word)
            if re.fullmatch(r"[\"'][\s\S]+[\"']", word):  # слово в кавычках тригерится только целиком
                word = re.sub(r"[\"']", "", word)
                filter_words_found = re.search(rf"(\s|^){word}(\s|$)", text, flags=re.IGNORECASE)
            else:
                filter_words_found = re.search(rf"{word}", text, flags=re.IGNORECASE)

            if filter_words_found:
                analytic_kwargs = {
                    "member_id": chat_member.id,
                    "filter_trigger": text,
                    "filter_triggered": word,
                    "filter_type": "message",
                }
                await models.FriendlyBotAnalyticAction.save_filter_triggered(**analytic_kwargs)
                return word
        return True

    @db_func
    def refresh_messages(self) -> bool:
        self.refresh_datetime = datetime.utcnow()
        sess().commit()
        return True

    @db_func
    def change_refresh_status(self) -> bool:
        self.refresh_messages_daily = not self.refresh_messages_daily
        sess().commit()
        return True

    @db_func
    def update(self, data: dict = None, **kwargs: Any):
        if data:
            data.update(**kwargs)
        else:
            data = kwargs

        for key, value in data.items():
            if key not in dir(self):
                continue
            setattr(self, key, value)
        sess().commit()
        return True

    @db_func
    def get_menu_buttons(self, button_text: str = None):
        query = sess().query(models.ChannelMenuButton)
        query = query.filter(models.ChannelMenuButton.channel_id == self.id)

        if button_text:
            query = query.filter(models.ChannelMenuButton.name_button == button_text)
            return query.one()

        query = query.order_by(models.ChannelMenuButton.button_number)
        return query.all()

    @db_func
    def move_menu_buttons(self, button_id_1: int, button_id_2: int):
        button1 = sess().query(models.ChannelMenuButton).filter(models.ChannelMenuButton.id == button_id_1).one()
        button2 = sess().query(models.ChannelMenuButton).filter(models.ChannelMenuButton.id == button_id_2).one()
        button1.is_new_line, button2.is_new_line = button2.is_new_line, button1.is_new_line
        button1.button_number, button2.button_number = button2.button_number, button1.button_number
        sess().commit()
        return True

    async def update_chat_info(self, telegram_chat: types.Chat):
        update_kwargs = {}

        if self.name != telegram_chat.title:
            update_kwargs.update(name=telegram_chat.title)
        if self.username != telegram_chat.username:
            update_kwargs.update(username=telegram_chat.username)
        if self.chat_id != telegram_chat.id:
            update_kwargs.update(chat_id=telegram_chat.id)

        if update_kwargs:
            await self.update(**update_kwargs)

    async def set_bot_is_admin(self):
        await self.update(is_bot_admin=True)

    async def set_bot_is_not_admin(self):
        await self.update(is_bot_admin=False, admins=list())

    def get_link(self) -> str:
        return f'<a href="https://t.me/{self.username}">{self.name}</a>'

    async def save_sent_rate_limited_message(self, message_type: str):
        field = f"{message_type}_message_last_sent_datetime"
        return await self.update({field: datetime.utcnow()})

    async def set_required_resource_subscription_chat_id(self, chat_id: int):
        self.required_resource_subscription_chat_id = chat_id
        sess().commit()

    def check_is_allowed_to_send(self, message_type: str):
        last_sent = getattr(self, f"{message_type}_message_last_sent_datetime")
        rate_limit = getattr(self, f"{message_type}_message_rate_limit")

        if not last_sent or not rate_limit:
            return True

        return (last_sent + timedelta(seconds=rate_limit)) < datetime.utcnow()

    @db_func
    def check_footer(self, footer_id: int):
        footer_types = ["required_subscription"]
        footer_types.extend(Channel.EDITABLE_MESSAGE_TYPES)
        results = list()

        for footer_type in footer_types:
            field_name = f"_{footer_type}_footer_id"
            if getattr(self, field_name) == footer_id:
                results.append(footer_type)

        return results

    @db_func
    def set_footer(
            self,
            footer_type: Literal[
                "welcome", "deletion",
                "info", "restriction", "alert",
                "ban_sender_chat", "rules", "rules_ban",
                "required_subscription",
            ],
            footer_id: int | None
    ) -> bool:
        field_name = f"_{footer_type}_footer_id"
        if getattr(self, field_name) == footer_id:
            footer_id = None

        setattr(self, field_name, footer_id)
        sess().commit()
        return True

    @db_func
    def get_footer(
            self,
            footer_type: Literal[
                "welcome", "deletion",
                "info", "restriction", "alert",
                "ban_sender_chat", "rules", "rules_ban",
                "required_subscription",
            ]
    ) -> "models.Footer":
        footer = getattr(self, f"_{footer_type}_footer")
        return footer

    @property
    def logs(self) -> list[str]:
        if isinstance(self._logs, list):
            return self._logs
        return []

    async def add_log(self, text: str | None):
        if text:
            for admin in self.admins:
                try:
                    await send_tg_message(admin.chat_id, "text", text=text)
                except:
                    pass

        await self.save_log(text)

    @db_func
    def save_log(self, text: str | None):
        logs = self._logs if self._logs else []
        logs = deque(logs, maxlen=10)

        if text:
            logs.append(text)
        else:
            logs.clear()

        self._logs = list(logs)
        sess().commit()

    async def get_logs_text(self, lang: str, type: Literal["data", "count"]) -> str:
        if not self._logs:
            return await f("friendly logs not error text", lang)

        if type == "count":
            numbers = (await f("friendly logs numbers", lang)).split(";")
            count = len(self._logs)
            number = numbers[count - 1] if count <= len(numbers) else str(count)
            return await f("friendly logs count error text", lang, count=number)

        return "\n".join([row for row in self.logs])

    async def delete_schedule_from_related_schedules(self, schedule: "models.Schedule"):
        if schedule in self.related_schedules:
            related_schedule = await models.SchedulesChannelsAssociation.get(schedule.id, self.id)
            await related_schedule.stop()
            self.related_schedules.remove(schedule)
            sess().commit()

    async def add_schedule_to_related_schedules(self, schedule: "models.Schedule"):
        if schedule not in self.related_schedules:
            self.related_schedules.append(schedule)
            sess().commit()

            related_schedule = await models.SchedulesChannelsAssociation.get(schedule.id, self.id)
            await related_schedule.activate()

    @db_func
    def check_schedule_in_related_schedules(self, schedule: "models.Schedule") -> bool:
        return bool(schedule in self.related_schedules)

    async def get_status_related_schedule(self, schedule: "models.Schedule") -> str:
        related_schedule = await models.SchedulesChannelsAssociation.get(schedule.id, self.id)
        if related_schedule:
            return related_schedule.status
        return "stopped"

    @db_func
    def change_need_welcome_system_keyboard_status(self) -> bool:
        self.need_welcome_system_keyboard = not self.need_welcome_system_keyboard
        sess().commit()
        return True
