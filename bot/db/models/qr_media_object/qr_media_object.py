import schemas

from sqlalchemy import Column, String, BigInteger, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship

from db.my_columns import NestedMutableJson
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from db import models


class QrMediaObject(Base, BaseDBModel, TimeCreatedMixin):
    name: str = Column(String(255), nullable=True, default=None)
    url: str = Column(String(2048))
    media_id: int | None = Column(BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL"))
    media: "models.MediaObject" = relationship("MediaObject", foreign_keys=media_id, backref="qr_media_objects")
    target: schemas.QrMediaObjectTargetType = Column(String(255))
    json_data = Column(NestedMutableJson, nullable=True, default=None)


class MenuInStoreToQrMediaObject(Base, BaseDBModel):
    menu_in_store_id: int = Column(BigInteger, Foreign<PERSON>ey("menus_in_store.id"), nullable=False)
    qr_media_object_id: int = Column(BigInteger, ForeignKey("qr_media_objects.id"), nullable=False)


class ProfileToQrMediaObject(Base, BaseDBModel):
    profile_id: int = Column(BigInteger, ForeignKey("groups.id"), nullable=False)
    qr_media_object_id: int = Column(BigInteger, ForeignKey("qr_media_objects.id"), nullable=False)


class QrMediaAdditionalObject(Base, BaseDBModel):
    name: str = Column(String(100))
    media_id: int | None = Column(BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL"))


class QrMediaObjectToQrMediaAdditionalObject(Base, BaseDBModel):
    qr_media_object_id: int = Column(BigInteger, ForeignKey("qr_media_objects.id"), nullable=False)
    qr_media_additional_object_id: int = Column(
        BigInteger, ForeignKey("qr_media_additional_objects.id"), nullable=False
    )
