from datetime import datetime
from typing import Type

from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, Enum, Foreign<PERSON>ey, Integer,
    JSON, Text, case, func, not_,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import backref, relationship

from config import INBOX_MAX_AGE, REVIEW_NUMBERS_COUNT, REVIEW_STAR
from db import models
from db.connection import Base
from db.decorators import db_func
from db.helpers import T, sess
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson
from schemas import ReviewPrivacyEnum, ReviewTypeEnum
from utils.text import f


class Review(Base, BaseDBModel):
    id: int = Column(Integer, primary_key=True, autoincrement=True)

    bot_id: int | None = Column(BigInteger, ForeignKey("bots.id", ondelete="SET NULL"))
    bot: "models.ClientBot | None" = relationship(
        "ClientBot", backref=backref("reviews")
    )

    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: "models.Group" = relationship("Group", backref=backref("reviews"))

    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="SET NULL"))
    user: "models.User" = relationship(
        "User", backref=backref("reviews"), foreign_keys=user_id
    )

    privacy: ReviewPrivacyEnum = Column(Enum(ReviewPrivacyEnum), default="public")

    media: dict = Column(NestedMutableJson)

    type: ReviewTypeEnum = Column(Enum(ReviewTypeEnum))
    review: dict = Column(NestedMutableJson)

    text: str = Column(Text(collation="utf8mb4_unicode_ci"), default=None)
    additional_text: str = Column(Text(collation="utf8mb4_unicode_ci"), default=None)

    time: datetime = Column(DateTime(timezone=True), default=datetime.utcnow)

    _is_read: bool = Column(Boolean, default=False)
    read_by_user_id: int | None = Column(
        BigInteger, ForeignKey("users.id", ondelete="SET NULL")
    )
    read_by_user: "models.User | None" = relationship(
        "User", backref="read_reviews", foreign_keys=read_by_user_id
    )
    read_status_change_datetime: datetime | None = Column(
        DateTime(timezone=True), nullable=True
    )

    utm_labels = Column(
        JSON, nullable=True, default=None
    )  # UTM labels for the review

    def __init__(
            self,
            type: ReviewTypeEnum,
            review: dict,
            user: "models.User",
            group: "models.Group",
            bot: "models.ClientBot | None",
            privacy: ReviewPrivacyEnum,
            text: str,
            additional_text: str,
            media: dict,
            utm_labels: dict | None = None,
    ):
        assert type
        assert review
        assert user
        assert group
        assert privacy

        self.type = type
        self.user = user
        self.group = group
        self.bot = bot
        self.privacy = privacy
        self.text = text
        self.additional_text = additional_text
        self.review = review
        self.media = media
        self.utm_labels = utm_labels if utm_labels else {}

    @classmethod
    @db_func
    def create(
            cls: Type[T],
            type: ReviewTypeEnum,
            review: dict,
            user: "models.User",
            group: "models.Group",
            bot: "models.ClientBot | None",
            privacy: ReviewPrivacyEnum,
            text: str = "",
            additional_text: str = "",
            media: dict = None,
            utm_labels: dict | None = None,
    ) -> T:
        if media is None:
            media = dict()

        new_review = cls(
            type, review, user, group, bot, privacy, text, additional_text, media,
            utm_labels
        )
        sess().add(new_review)
        sess().commit()
        return new_review

    @staticmethod
    @db_func
    def get(review_id: int) -> "Review":
        review = sess().query(Review).filter_by(id=review_id).one()
        return review

    @hybrid_property
    def mark(self):
        rating = self.review.get(self.type.value)
        match self.type:
            case ReviewTypeEnum.STARS:
                return REVIEW_STAR * rating
            case ReviewTypeEnum.NUMBER:
                return f"{rating}/{REVIEW_NUMBERS_COUNT}"
            case ReviewTypeEnum.EMOJI:
                return rating

    @hybrid_property
    def time_created(self):
        return self.time

    @mark.expression
    def mark(self):
        review_value = func.JSON_UNQUOTE(
            func.JSON_EXTRACT(self.review, func.CONCAT('$.', func.lower(self.type)))
        )

        return case(
            [
                (
                    self.type == ReviewTypeEnum.STARS,
                    func.repeat(REVIEW_STAR, review_value)
                ),
                (
                    self.type == ReviewTypeEnum.NUMBER,
                    func.concat(f"{REVIEW_NUMBERS_COUNT}/", review_value)
                ),
                (
                    self.type == ReviewTypeEnum.EMOJI,
                    review_value
                ),
            ]
        )

    @property
    def stars(self) -> int:
        return self.review.get("stars")

    @property
    def emoji(self) -> str:
        return self.review.get("emoji")

    @property
    def number(self) -> int:
        return self.review.get("number")

    @property
    def review_type(self) -> str:
        return list(self.review.keys())[0] if self.review else None

    async def review_view(self, lang: str) -> str:
        if self.review_type == "stars":
            review = await f("review star", lang) * self.stars
        else:
            review = getattr(self, self.review_type)
        return await f(
            f"review_{self.review_type}_info", lang, **{self.review_type: review}
        )

    @property
    def content_type(self) -> str:
        return list(self.media.keys())[0] if self.media.keys() else "text"

    @classmethod
    async def stars_count(
            cls, group_id: int, bot_id: int, stars_count: int = None
    ) -> int:
        stars_stats = await Review.stars_stats(group_id, bot_id)
        if stars_count:
            result = stars_stats.get(stars_count)
        else:
            result = sum(stars_stats.values())
        return result

    @staticmethod
    @db_func
    def get_mark(group_id: int, bot_id: int) -> float:
        mark = sess().query(func.avg(Review.review["stars"])). \
            filter(
            not_(Review.review["stars"].is_(None)),
            Review.group_id == group_id, Review.bot_id == bot_id
        ).scalar()
        if mark is None:
            mark = 0
        return mark

    @staticmethod
    async def get_mark_text(mark: float, lang: str):
        full_symbol = await f("review star", lang)
        from_1_to_3_symbol = await f("review from point 1 to point 3", lang)
        from_4_to_6_symbol = await f("review from point 4 to point 6", lang)
        from_7_to_9_symbol = await f("review from point 7 to point 9", lang)
        point = round(mark % 1 * 10)
        int_mark = int(mark)
        mark_text = "".join([full_symbol] * int_mark)
        if 1 <= point <= 3:
            mark_text += from_1_to_3_symbol
        elif 4 <= point <= 6:
            mark_text += from_4_to_6_symbol
        elif 7 <= point <= 9:
            mark_text += from_7_to_9_symbol
        return mark_text

    @staticmethod
    @db_func
    def stars_stats(group_id: int, bot_id: int) -> dict:
        result = sess().query(
            Review.review["stars"], func.count(
                Review.id
            )
        ).distinct(). \
            filter(Review.group_id == group_id, Review.bot_id == bot_id).group_by(
            Review.review["stars"]
        ).all()
        result = dict(result)
        return result

    @hybrid_property
    def change_date(self):
        return self.read_status_change_datetime or self.time_created

    @change_date.expression
    def change_date(self):
        return func.IFNULL(self.read_status_change_datetime, self.time_created)

    @hybrid_property
    def is_read(self):
        return self._is_read or self.change_date < datetime.utcnow() - INBOX_MAX_AGE

    @is_read.setter
    def is_read(self, value: bool):
        self._is_read = value

    @is_read.expression
    def is_read(self):
        return func.IF(
            self.change_date < datetime.utcnow() - INBOX_MAX_AGE, True, self._is_read
        )

    @hybrid_property
    def crm_tag(self):
        return f"review-{self.id}"

    @crm_tag.expression
    def crm_tag(self):
        return func.concat("review-", self.id)
