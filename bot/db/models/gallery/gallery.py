from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class Gallery(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = "galleries"

    gallery_items: list["models.GalleryItem"] = relationship("GalleryItem", back_populates="gallery")

    async def get_items(self):
        return await models.GalleryItem.get_list(gallery_id=self.id)
