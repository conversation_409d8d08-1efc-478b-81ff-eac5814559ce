from sqlalchemy import <PERSON>umn, BigInteger, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class GalleryItem(Base, BaseDBModel, TimeCreatedMixin):

    gallery_id: int = Column(BigInteger, ForeignKey("galleries.id", ondelete="RESTRICT"))
    gallery: "models.Gallery" = relationship("Gallery", back_populates="gallery_items")

    media_id: int = Column(BigInteger, ForeignKey("media_objects.id"))
    media: "models.MediaObject" = relationship("MediaObject", backref="gallery_items")

    position: int = Column(SMALLINT(unsigned=True), nullable=False)

    __table_args__ = (
        UniqueConstraint("gallery_id", "media_id"),
    )

    async def get_media(self):
        return await models.MediaObject.get(self.media_id) if self.media_id else None
