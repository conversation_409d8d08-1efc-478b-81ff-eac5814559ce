from datetime import datetime
from typing import List

from sqlalchemy import <PERSON>umn, BigInteger, Text, DateTime, ForeignKey, String, Integer, Boolean
from sqlalchemy.orm import relationship

from db import db_func, sess, models
from db.connection import Base
from db.my_columns import NestedMutableJson

from utils.redefined_classes import InlineKb


class MailingArgs(Base):
    __tablename__ = "mailings_args"

    id = Column(BigInteger, autoincrement=True, primary_key=True)

    is_finished = Column(Boolean, default=False)

    text = Column(Text(collation="utf8mb4_unicode_ci"))  # message.text или message.caption
    content_type = Column(String(20))
    file_path = Column(String(100))
    keyboard = Column(NestedMutableJson)

    creator_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    creator = relationship("User")

    created_from_bot_token = Column(String(256))

    sender_group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    sender_group = relationship("Group")

    start_count = Column(Integer, default=0)

    sent = Column(Integer, default=0)
    turned_off = Column(Integer, default=0)
    blocked = Column(Integer, default=0)
    not_entered = Column(Integer, default=0)
    error = Column(Integer, default=0)
    unknown = Column(Integer, default=0)

    bots = Column(Text, default="")
    groups = Column(Text, default="")

    created_datetime = Column(DateTime(timezone=True), default=datetime.utcnow)

    def __init__(
            self,
            creator: "models.User",
            created_from_bot_token: str,
            sender_group_id: int,
            text: str, content_type: str,
            file_path: str = None,
            keyboard: InlineKb = None,
            groups: str = "",
            bots: str = "",
            sent: int = 0,
            turned_off: int = 0,
            blocked: int = 0,
            error: int = 0,
            unknown: int = 0,
    ):
        self.creator = creator
        self.created_from_bot_token = created_from_bot_token
        self.sender_group_id = sender_group_id
        self.text = text
        self.content_type = content_type
        self.file_path = file_path
        self.keyboard = keyboard.to_python()
        self.groups = groups
        self.bots = bots
        self.sent = sent
        self.turned_off = turned_off
        self.blocked = blocked
        self.error = error
        self.unknown = unknown

    @classmethod
    @db_func
    def create(
            cls,
            creator: "models.User",
            created_from_bot_token: str,
            sender_group_id: int,
            text: str,
            content_type: str,
            file_path: str = None,
            keyboard: InlineKb = None,
            groups: str = "",
            bots: str = "",
            sent: int = 0,
            turned_off: int = 0,
            blocked: int = 0,
            error: int = 0,
            unknown: int = 0,
    ) -> "MailingArgs":
        mailing_args = MailingArgs(
            creator,
            created_from_bot_token,
            sender_group_id,
            text, content_type,
            file_path, keyboard,
            groups, bots,
            sent, turned_off,
            blocked, error, unknown,
        )

        sess().add(mailing_args)
        sess().commit()
        return mailing_args

    @classmethod
    @db_func
    def get(cls, id: int) -> "MailingArgs":
        return sess().query(MailingArgs).filter_by(id=id).one()

    @db_func
    def finished(self):
        self.is_finished = True
        sess().commit()

    @classmethod
    @db_func
    def get_mailings_for_user(cls, user_id: int, bot_token: str, operation: str = "all") -> List["MailingArgs"] | bool:
        query = sess().query(cls)

        query = query.filter(cls.creator_id == user_id)
        query = query.filter(cls.is_finished.is_(False))
        query = query.filter(cls.created_from_bot_token == bot_token)

        if operation == "exists":
            return sess().query(query.exists()).scalar()

        query = query.order_by(cls.created_datetime)

        return query.all()
