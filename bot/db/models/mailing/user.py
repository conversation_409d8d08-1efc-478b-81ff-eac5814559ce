from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, DateTime, Foreign<PERSON>ey, String
from sqlalchemy.orm import relationship
from typing import List, Tuple

from db import db_func, models, sess
from db.connection import Base


class MailingUser(Base):
    __tablename__ = "mailing_users"

    id = Column(BigInteger, autoincrement=True, primary_key=True)

    mailing_args_id = Column(
        BigInteger, ForeignKey("mailings_args.id", ondelete="CASCADE")
    )
    mailing_args = relationship("MailingArgs")

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user = relationship("User", foreign_keys=user_id)

    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot = relationship("ClientBot")

    status = Column(String(11), default="pending")  # sent_no_sound | blocked | sent
    created_datetime = Column(DateTime(timezone=True), default=datetime.utcnow)

    def __init__(
            self, mailing_args_id: int, user_id: int, bot_id: int,
            mailing_mode: str | None
    ):
        self.mailing_args_id = mailing_args_id
        self.user_id = user_id
        self.bot_id = bot_id

        if mailing_mode == "blocked":
            self.status = "turned_off"

    @classmethod
    @db_func
    def create(
            cls,
            mailing_args_id: int,
            users_and_data: List[Tuple[int, int, str | None]],
    ) -> List["MailingUser"]:
        mailing_users = list(
            map(
                lambda user_and_data: cls(mailing_args_id, *user_and_data),
                users_and_data
            )
        )
        sess().add_all(mailing_users)
        sess().query(models.MailingArgs).filter_by(id=mailing_args_id).update(
            {"start_count": len(mailing_users)}
        )
        sess().commit()
        return mailing_users
