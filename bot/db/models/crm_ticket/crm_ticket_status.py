from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import CRMTicketStatusEnum, CRMTicketStatusInitiatedByEnum


class CRMTicketStatus(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = "crm_ticket_statuses"
    status: CRMTicketStatusEnum = Column(Enum(CRMTicketStatusEnum), nullable=False)

    initiated_by: CRMTicketStatusInitiatedByEnum = Column(
        Enum(CRMTicketStatusInitiatedByEnum), nullable=False
    )
    initiated_by_user_id: int | None = Column(
        BigInteger, ForeignKey("users.id", ondelete="RESTRICT")
    )
    initiated_by_user: "models.User | None" = relationship(
        "User", backref="ticket_statuses"
    )

    ticket_id: int = Column(
        BigInteger, Foreign<PERSON>ey("crm_tickets.id", ondelete="CASCADE"), nullable=False
    )
    ticket: "models.CRMTicket" = relationship("CRMTicket", back_populates="statuses")

    header: str | None = Column(String(124), nullable=True)
    message: str | None = Column(String(1024), nullable=True)

    internal_comment: str | None = Column(String(124), nullable=True)
