from datetime import datetime

from sqlalchemy import (
    BigInteger, Column, DateTime, Enum, ForeignKey, String, func,
    select,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import aliased, relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import CRMTicketSourceEnum, CRMTicketStatusEnum


class CRMTicket(Base, BaseDBModel, TimeCreatedMixin):
    id: int
    source: CRMTicketSourceEnum = Column(
        Enum(CRMTicketSourceEnum), nullable=False,
    )
    status: CRMTicketStatusEnum = Column(
        Enum(CRMTicketStatusEnum), nullable=False,
        default=CRMTicketStatusEnum.OPEN_UNCONFIRMED
    )
    change_date: datetime = Column(DateTime, nullable=False, default=datetime.utcnow)

    title: str = Column(String(50), nullable=False)

    group_id: int = Column(
        BigInteger, ForeignKey("groups.id", ondelete="RESTRICT"), nullable=False
    )
    group: "models.Group" = relationship("Group", backref="crm_tickets")

    bot_id: int | None = Column(BigInteger, ForeignKey("bots.id", ondelete="RESTRICT"))
    bot: "models.ClientBot | None" = relationship("ClientBot", backref="crm_tickets")

    user_id: int | None = Column(
        BigInteger, ForeignKey("users.id", ondelete="RESTRICT")
    )
    user: "models.User | None" = relationship("User", backref="crm_tickets")

    statuses: list["models.CRMTicketStatus"] = relationship(
        "CRMTicketStatus", back_populates="ticket"
    )

    internal_comment: str | None = Column(String(1024), nullable=True)

    @hybrid_property
    def crm_tag(self):
        return f"ticket-{self.id}"

    @crm_tag.expression
    def crm_tag(self):
        return func.concat("ticket-", self.id)

    @hybrid_property
    def first_title_id(self):
        min_id = aliased(CRMTicket)

        return select(
            func.min(min_id.id)
        ).where(
            min_id.title == self.title,
            min_id.group_id == self.group_id
        ).scalar_subquery()
