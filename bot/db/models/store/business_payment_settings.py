from datetime import datetime

from sqlalchemy import (
    BigInteger, <PERSON>olean, Column, DateTime, Enum, ForeignKey, String, UniqueConstraint,
)
from sqlalchemy.dialects.mysql import JSON as JSONType
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas.platform import BusinessPaymentMethodEnum


class BusinessPaymentData(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = 'business_payment_data'

    business_payment_setting_id: int = Column(
        BigInteger, ForeignKey("business_payment_settings.id", ondelete="CASCADE"),
        nullable=False
    )
    
    payment_method: BusinessPaymentMethodEnum = Column(Enum(BusinessPaymentMethodEnum), nullable=False)
    json_data = Column(JSONType, nullable=True, default=None)
    is_enabled = Column(Boolean, default=True)
    
    business_payment_setting: "models.BusinessPaymentSetting" = relationship(
        "BusinessPaymentSetting", back_populates="payment_data"
    )
    
    update_date = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False)


class BusinessPaymentSetting(Base, BaseDBModel, TimeCreatedMixin):

    __table_args__ = (
        UniqueConstraint('incust_account_id', name='uq_incust_account_id'),
    )

    incust_account_id: str = Column(String(36), nullable=False)

    is_enabled = Column(Boolean, default=True)
    name = Column(String(255), nullable=True)
    description = Column(String(512), nullable=True)

    # for migration only
    payment_method: BusinessPaymentMethodEnum = Column(Enum(BusinessPaymentMethodEnum), nullable=True)
    json_data = Column(JSONType, nullable=True, default=None)
    # for migration only

    is_deleted = Column(Boolean, default=False)

    creator_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    creator: "models.User" = relationship("User", foreign_keys=creator_id)

    # Додаємо зв'язок з платіжними даними
    payment_data: list["models.BusinessPaymentData"] = relationship(
        "BusinessPaymentData", back_populates="business_payment_setting",
        cascade="all, delete-orphan"
    )

    update_date = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
