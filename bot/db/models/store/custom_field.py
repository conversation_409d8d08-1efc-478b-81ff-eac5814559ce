from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>n, Foreign<PERSON><PERSON>, String
from sqlalchemy.orm import relationship
from typing import List

from db import db_func, sess
from db.connection import Base
from db.mixins import BaseDBModel


class StoreCustomField(Base, BaseDBModel):
    name: str = Column(String(255))
    value: str = Column(String(255), nullable=True)

    store_id = Column(
        BigInteger, ForeignKey("stores.id", ondelete="CASCADE")
    )
    store = relationship(
        "Store", foreign_keys=store_id, back_populates="custom_fields"
    )

    def __init__(
            self,
            name: str,
            value: str,
            store_id: int | None = None,  # temp fix
    ):
        self.name = name.strip()
        self.value = value
        if store_id:
            self.store_id = store_id

        super().__init__()

    AVAILABLE_CUSTOM_FIELDS = [
        "address", "email", "phone_number", "wifi",
        "facebook", "instagram", "pinterest", "telegram",
        "twitter", "viber", "whatsapp", "youtube", "tiktok",
    ]

    @classmethod
    @db_func
    def save(
            cls,
            name: str,
            value: str,
            store_id: int
    ) -> "StoreCustomField":
        custom_field = cls(
            name,
            value,
            store_id
        )
        sess().add(custom_field)
        sess().commit()
        return custom_field

    @classmethod
    @db_func
    def get_all(cls, store_id: int) -> List["StoreCustomField"]:
        store = sess().query(cls).filter(cls.store_id == store_id).all()
        return store

    @classmethod
    @db_func
    def get_by_name(cls, store_id: int, name: str) -> "StoreCustomField":
        query = sess().query(cls)
        query = query.filter(cls.store_id == store_id)
        query = query.filter(cls.name == name)
        return query.one_or_none()

    @classmethod
    @db_func
    def get_or_create_by_name(cls, store_id: int, name: str):
        query = sess().query(cls)
        query = query.filter(cls.store_id == store_id)
        query = query.filter(cls.name == name)

        res = query.one_or_none()
        if res:
            return res
        else:
            if name in cls.AVAILABLE_CUSTOM_FIELDS:
                custom_field = cls(
                    name,
                    None,
                    store_id
                )
                sess().add(custom_field)
                sess().commit()
                return custom_field
            return None

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k not in dir(self) or k == "id":
                continue

            setattr(self, k, v)

        sess().commit()
        return True
