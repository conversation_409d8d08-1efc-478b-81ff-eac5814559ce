from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, BigInteger, ForeignKey
from sqlalchemy.orm import relationship

from db.connection import Base
from db.mixins import BaseDBModel
from db import models


class AuthSetting(Base, BaseDBModel):
    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: "models.Group" = relationship("Group", foreign_keys=group_id)

    is_auth_email_enabled: bool = Column(Boolean, default=True)
    is_auth_messanger_enabled: bool = Column(Boolean, default=True)
    is_auth_google_enabled: bool = Column(Boolean, default=True)
    is_auth_apple_enabled: bool = Column(Boolean, default=True)
    is_auth_for_orders_enabled: bool = Column(Boolean, default=False)
