from sqlalchemy import (BigInteger, Column, Foreign<PERSON>ey, Integer, Text, and_)
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.orm import relationship

from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel


class StoreCartProduct(Base, BaseDBModel):
    __tablename__ = "store_cart_product"

    quantity: int = Column(Integer)
    store_id: int = Column(BigInteger, ForeignKey("stores.id", ondelete="CASCADE"))
    store: "models.Store" = relationship(
        "Store", foreign_keys=store_id, backref="cart_products"
    )

    product_id: int = Column(
        BigInteger,
        ForeignKey("store_products.id", ondelete="CASCADE")
    )
    product: "models.StoreProduct" = relationship(
        "StoreProduct",
        foreign_keys=product_id,
        backref="cart_product"
    )

    cart_attributes: list["models.StoreCartAttribute"] = relationship(
        "StoreCartAttribute",
        back_populates="cart_product",
    )

    cart_id: int = Column(
        BigInteger,
        ForeignKey("store_cart.id", ondelete="CASCADE")
    )
    cart: "models.StoreCart" = relationship(
        "StoreCart",
        foreign_keys=cart_id,
        back_populates="cart_products"
    )
    display_name: str = Column(Text(collation="utf8mb4_unicode_ci"), default=None)
    display_description: str = Column(
        Text(collation="utf8mb4_unicode_ci"), default=None
    )
    floating_sum: int = Column(
        BIGINT, default=0
    )  # в бд умножена на 100 для учёта копеек

    # __table_args__ = (
    #     UniqueConstraint("cart_id", "product_id"),
    # )

    @classmethod
    @db_func
    def get(cls, cart_product_id: int) -> "StoreCartProduct":
        cart_product = sess().query(cls).filter(cls.id == cart_product_id)
        return cart_product.one()

    @classmethod
    @db_func
    def get_product(cls, cart_id: int, cart_product_id: int) -> "StoreCartProduct":
        cart_product = sess().query(cls).filter(
            and_(cls.cart_id == cart_id, cls.product_id == cart_product_id)
        )
        return cart_product.one()

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()
        return True

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k not in dir(self) or k == "id":
                continue

            setattr(self, k, v)

        sess().commit()
        return True

    @db_func
    def attach_product_to_cart(self, cart_id: int) -> bool:
        return self.attach_product_to_cart_sync(cart_id)

    def attach_product_to_cart_sync(
            self, cart_id: int, no_commit: bool = False
    ) -> bool:
        self.cart_id = cart_id
        if not no_commit:
            sess().commit()
        return True
