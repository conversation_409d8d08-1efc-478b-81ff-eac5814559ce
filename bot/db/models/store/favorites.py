from sqlalchemy import <PERSON>Integer, Column, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel


class StoreFavorite(Base, BaseDBModel):
    favorite_products: list["models.StoreFavoritesProduct"] = relationship(
        "StoreFavoritesProduct",
        back_populates="favorite"
    )

    user_id: int | None = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user: "models.User | None" = relationship(
        "User", foreign_keys=user_id, backref="favorite"
    )

    store_id: int = Column(BigInteger, ForeignKey("stores.id", ondelete="CASCADE"))
    store: "models.Store" = relationship(
        "Store", foreign_keys=store_id, backref="favorites"
    )

    __table_args__ = (
        UniqueConstraint("store_id", "user_id"),
    )

    def is_product_in_favorites(self, product_id: int) -> bool:
        if self.favorite_products:
            has_product = any(
                fav_prod.product.id == product_id for fav_prod in self.favorite_products
            )
            return has_product
        return False
