from typing import Literal

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, String
from sqlalchemy.orm import relationship

from db import db_func, sess, models
from db.connection import Base
from db.mixins import BaseDBModel


class StoreOrderBillingAddress(Base, BaseDBModel):

    order_id: int = Column(BigInteger, ForeignKey("store_orders.id", ondelete="CASCADE"))
    order: "models.StoreOrder" = relationship("StoreOrder", uselist=False, back_populates="billing_address", foreign_keys=order_id)
    counterparty_type: Literal["person", "organisation"] = Column(String(15), default="organisation")

    first_name: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)
    last_name: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)

    company_name: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)
    vat_number: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)
    registration_number: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)

    country: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)
    state: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)
    city: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)
    zip_code: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)

    address_1: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)
    address_2: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)
    phone_number: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=True, default=None)

    @classmethod
    @db_func
    def create(
            cls, order: "models.StoreOrder",
            counterparty_type: Literal["person", "organisation"],
            **kwargs
    ) -> "StoreOrderBillingAddress | None":
        if not any(kwargs.values()):
            return

        billing_address = cls(
            order, counterparty_type,
            **kwargs,
        )
        sess().add(billing_address)
        sess().commit()
        return billing_address
