import datetime
import logging
import uuid
from decimal import Decimal
from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, ForeignKey, Numeric,
    String,
)
from sqlalchemy.dialects.mysql import JSON as JSONType
from sqlalchemy.orm import relationship

from db import db_func, sess
from db.connection import Base
from db.mixins import BaseDBModel
from db.models.store.store_order import StoreOrder
from db.my_columns import NestedMutableJson
from schemas import PmtExtTypes
from utils.date_time import utcnow


class Payment(Base, BaseDBModel):
    __tablename__ = 'payments'

    id = Column(BigInteger, primary_key=True)
    uuid_id = Column(
        String(36), unique=True, default=lambda: str(uuid.uuid4()), nullable=False
    )

    invoice_id = Column(BigInteger, ForeignKey('invoices.id'))
    invoice = relationship('Invoice', foreign_keys=invoice_id)

    currency = Column(String(3))
    amount = Column(BigInteger, nullable=True)

    status = Column(String(99))

    payment_method = Column(String(99), nullable=True)
    card_type = Column(String(99), nullable=True)
    card_mask = Column(String(99), nullable=True)
    create_date = Column(DateTime, default=datetime.datetime.utcnow)
    external_id = Column(String(99), nullable=True)
    payment_ext = relationship('PaymentExt', back_populates='payment', uselist=False)
    return_url = Column(String(999), nullable=True, default=None)
    user_id = Column(
        BigInteger, ForeignKey("users.id", ondelete="RESTRICT"), nullable=True,
        default=None
    )
    user = relationship("User", foreign_keys=user_id)
    payment_settings_id = Column(
        BigInteger, ForeignKey('payment_settings.id'), nullable=True, default=None
    )
    object_payment_settings_id = Column(
        BigInteger, ForeignKey('object_payment_settings.id'), nullable=True,
        default=None
    )
    is_sandbox = Column(Boolean, nullable=False, default=False)
    payment_data = Column(JSONType, nullable=True, default=None)

    ewallet_discount_percent: Decimal = Column(
        Numeric(precision=4, scale=2), nullable=False, default=0
    )
    ewallet_discount_amount: Decimal = Column(
        Numeric(precision=12, scale=2), nullable=False, default=0
    )

    @property
    def get_status(self):
        return self.status

    @classmethod
    async def create(
            cls,
            invoice_id: int,
            currency: str,
            amount: int,
            status: str,
            payment_method: str = '',
            return_url: str | None = None,
            user_id: int | None = None,
            brand_id: int | None = None,
            store_id: int | None = None,
            **kwargs,
    ) -> "Payment":
        payment = cls(
            invoice_id=invoice_id,
            currency=currency,
            amount=amount,
            status=status,
            payment_method=payment_method,
            return_url=return_url,
            user_id=user_id,
            **kwargs,
        )
        sess().add(payment)
        sess().commit()
        if payment:
            json_data = dict(
                pmt_init=dict(
                    invoice_id=invoice_id,
                    currency=currency,
                    amount=amount,
                    status=status,
                    payment_method=payment_method,
                    brand_id=brand_id,
                    store_id=store_id,
                )
            )

            await PaymentExt.create(payment_id=payment.id, json_data=json_data, )
        return payment

    @db_func
    def save_pmt_data(self, data: dict):
        try:
            current_dict = self.payment_ext.json_data or {}
            updated_dict = {**current_dict, **data}
            self.payment_ext.json_data = updated_dict

            sess().commit()
        except Exception as e:
            logging.error(e, exc_info=True)
            pass

    @classmethod
    @db_func
    def get_by_invoice(
            cls,
            invoice_id: int,
    ) -> "Payment | None":
        payment = sess().query(cls).filter(cls.invoice_id == invoice_id) \
            .order_by(cls.create_date.desc()) \
            .first()
        return payment

    @classmethod
    async def get_pmt_info(cls, order_id: int):
        pmt_info = {}
        order = await StoreOrder.get(order_id)
        payment = await cls.get_by_invoice(order.invoice_id)
        if not payment:
            return pmt_info
        pmt_info['method'] = payment.payment_method
        if payment.payment_method == PmtExtTypes.liqpay.value:
            pmt_result = payment.payment_ext.json_data.get('pmt_callback', {}) or {}
            pmt_info['status'] = pmt_result.get('status', '')
            try:
                pmt_info['date'] = f"""{datetime.datetime.fromtimestamp(
                    pmt_result.get('end_date') / 1000.0
                ):%d.%m.%Y %H:%M:%S}"""
            except:
                pass
            pmt_info['card_mask'] = pmt_result.get('sender_card_mask2', '')
            pmt_info['amount'] = pmt_result.get('amount', '')
            pmt_info['description'] = pmt_result.get('err_description', '')
            pmt_info['type'] = pmt_result.get('paytype', '')
            pmt_info['currency'] = pmt_result.get('currency', '')
            pmt_info['ext_pmt_id'] = payment.external_id

        return pmt_info

    @db_func
    def confirm(self):
        self.status = 'payed'
        sess().commit()
        return True

    @db_func
    def cancel(self):
        self.status = 'canceled'
        sess().commit()
        return True

    @db_func
    def fail(self):
        self.status = 'failed'
        sess().commit()
        return True

    @db_func
    def closed(self):
        self.status = 'closed'
        sess().commit()
        return True

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k not in dir(self) or k == "id":
                continue

            setattr(self, k, v)

        sess().commit()
        return True


class PaymentExt(Base):
    __tablename__ = 'payments_ext'

    id = Column(BigInteger, primary_key=True)
    payment_id = Column(BigInteger, ForeignKey('payments.id', ondelete='CASCADE'))
    create_date = Column(DateTime, default=utcnow)
    json_data = Column(JSONType, nullable=True)

    payment = relationship('Payment', back_populates='payment_ext', uselist=False)

    def __init__(
            self, payment_id: BigInteger,
            json_data: NestedMutableJson = None
    ):
        self.payment_id = payment_id
        self.json_data = json_data

    @classmethod
    @db_func
    def get(cls, payment_ext_id: int) -> "PaymentExt":
        return sess().query(cls).filter(cls.id == payment_ext_id).one()

    @classmethod
    @db_func
    def get_by_pmt_id(cls, payment_id: int) -> "PaymentExt | None":
        return sess().query(cls).filter(cls.payment_id == payment_id).first()

    @classmethod
    async def create(
            cls, payment_id, json_data: NestedMutableJson = None
    ) -> "PaymentExt":
        payment_ext = cls(payment_id=payment_id, json_data=json_data)
        sess().add(payment_ext)
        sess().commit()
        return payment_ext
