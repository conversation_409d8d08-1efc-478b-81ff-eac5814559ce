from typing import List

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.orm import relationship

from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel


class StoreAttribute(Base, BaseDBModel):
    attribute_id: str = Column(String(255))

    external_id: str = Column(String(255))
    external_type: str = Column(String(99))

    is_available: bool = Column(Boolean, default=True)
    is_deleted: bool = Column(Boolean, default=False)

    name: str = Column(String(255))

    min: int | None = Column(Integer)
    max: int | None = Column(Integer)
    price_impact: int = Column(BIGINT)  # в бд умножена на 100 для учёта копеек

    selected_by_default: bool = Column(Boolean, default=False)

    attribute_group_id: int = Column(BigInteger, Foreign<PERSON><PERSON>("store_attribute_groups.id", ondelete="CASCADE"))
    attribute_group: "models.StoreAttributeGroup" = relationship("StoreAttributeGroup", back_populates="attributes")

    brand_id: int = Column(BigInteger, ForeignKey("brands.id", ondelete="CASCADE"))
    brand: "models.Brand" = relationship("Brand", foreign_keys=brand_id)

    @classmethod
    @db_func
    def get_all(cls, brand_id: int) -> List["StoreAttribute"]:
        attributes = sess().query(cls).filter(cls.brand_id == brand_id).all()
        return attributes

    @classmethod
    @db_func
    def get_by_attribute_group(cls, attribute_group_id: int) -> List["StoreAttribute"]:
        attributes = sess().query(cls).filter(cls.attribute_group_id == attribute_group_id)
        attributes = attributes.filter(cls.is_deleted.is_(False))
        attributes = attributes.filter(cls.is_available.is_(True)).all()
        return attributes

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k not in dir(self) or k == "id":
                continue

            setattr(self, k, v)

        sess().commit()
        return True
