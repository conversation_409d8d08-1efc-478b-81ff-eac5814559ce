from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, Enum as S<PERSON><PERSON><PERSON>, ForeignKey, Integer, String,
)

from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from db.my_columns import NestedMutableJson
from schemas import LoyaltySettingsApplicableTypes, LoyaltySettingsTypeClientAuth
from utils.date_time import utcnow


class LoyaltySettings(Base, BaseDBModel, TimeCreatedMixin):

    __tablename__ = "loyalty_settings"

    ewallet_id: int | None = Column(
        BigInteger, ForeignKey("ewallets.id", ondelete="CASCADE"), index=True
    )

    profile_id: int | None = Column(
        BigInteger, ForeignKey("groups.id", ondelete="CASCADE"), index=True
    )

    brand_id: int | None = Column(
        BigInteger, ForeignKey("brands.id", ondelete="CASCADE"), index=True
    )

    invoice_template_id = Column(
        BigInteger, ForeignKey("invoice_templates.id", ondelete="CASCADE"), index=True
    )

    store_id: int | None = Column(
        BigInteger, ForeignKey("stores.id", ondelete="CASCADE"), index=True
    )

    product_id: int | None = Column(
        BigInteger, ForeignKey("store_products.id", ondelete="CASCADE"), index=True
    )

    # Основні налаштування InCust
    terminal_api_key = Column(String(255), nullable=True, default=None)
    server_url = Column(String(255), nullable=True, default=None)

    white_label_id = Column(String(255), nullable=True, default=None)
    terminal_id = Column(String(255), nullable=True, default=None)
    loyalty_id = Column(String(255), nullable=True, default=None)

    # Налаштування клієнта
    type_client_auth = Column(SQLEnum(LoyaltySettingsTypeClientAuth), nullable=True, default=None)

    prohibit_redeeming_bonuses = Column(Boolean, default=False, nullable=False)
    prohibit_redeeming_coupons = Column(Boolean, default=False, nullable=False)

    # Налаштування застосування лояльності
    loyalty_applicable_type = Column(
        SQLEnum(LoyaltySettingsApplicableTypes),
        nullable=False,
        default=LoyaltySettingsApplicableTypes.FOR_PARTICIPANTS,
    )

    priority = Column(Integer, default=0, nullable=False)

    is_enabled = Column(Boolean, default=True, nullable=False)

    # Назва для адмін панелі
    name = Column(String(255), nullable=True, default=None)
    description = Column(String(1000), nullable=True, default=None)

    time_updated = Column(DateTime, default=utcnow, nullable=False)

    json_data = Column(NestedMutableJson, nullable=True, default=None)

    # Метод для копіювання налаштувань
    def copy_from(self, source: "LoyaltySettings") -> None:
        """Копіює всі налаштування з іншого об'єкта"""
        skip_fields = {
            "id", "ewallet_id", "profile_id", "store_id", "invoice_template_id",
            "product_id", "name", "description"
        }

        for column in self.__table__.columns:
            field_name = column.name
            if field_name not in skip_fields:
                setattr(self, field_name, getattr(source, field_name))
