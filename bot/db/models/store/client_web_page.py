from typing import List, Type

from sqlalchemy import (
    BigI<PERSON>ger, <PERSON>olean, Column, Enum, ForeignKey, JSON, String,
    UniqueConstraint, func, or_,
)
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.ext.hybrid import hybrid_method, hybrid_property
from sqlalchemy.orm import relationship

import schemas
from db import db_func, models, sess
from db.connection import Base
from ...mixins import BaseDBModel


class ClientWebPage(Base, BaseDBModel):
    type: schemas.ClientWebPageTypeEnum = Column(Enum(schemas.ClientWebPageTypeEnum))

    container_max_width: schemas.ClientWebPageContainerMaxWidthEnum = Column(
        Enum(schemas.ClientWebPageContainerMaxWidthEnum), default='xs'
    )

    custom_container_max_width: int | None = Column(
        SMALLINT, nullable=True, default=None
    )

    is_enabled: bool = Column(Boolean, default=False)

    show_in_profile: bool = Column(Boolean, default=False)

    show_in_navbar: bool = Column(Boolean, default=False)

    title: str | None = Column(String(255), nullable=True, default=None)

    _internal_name = Column(String(255), nullable=True, default=None)

    button_title: str | None = Column(String(255), nullable=True, default=None)

    slug: str = Column(String(255))

    page_content: dict = Column(JSON, nullable=True)

    position: int = Column(SMALLINT(unsigned=True))

    group_id: int = Column(
        BigInteger, ForeignKey("groups.id", ondelete="CASCADE"), nullable=False
    )
    group: "models.Group" = relationship("Group", foreign_keys=group_id)

    stores: list["models.Store"] = relationship(
        "Store",
        secondary="client_web_page_to_stores",
        back_populates="client_web_pages",
    )

    invoice_templates: list["models.InvoiceTemplate"] = relationship(
        "InvoiceTemplate",
        secondary="client_web_page_to_invoice_templates",
        back_populates="client_web_pages",
    )

    @hybrid_property
    def internal_name(self):
        return self._internal_name or self.title

    @internal_name.expression
    def internal_name(self):
        return func.IFNULL(self._internal_name, self.title)

    @hybrid_property
    def raw_internal_name(self):
        return self._internal_name

    @raw_internal_name.setter
    def raw_internal_name(self, value):
        self._internal_name = value

    @classmethod
    @db_func
    def get_all(cls, brand_id: int) -> List["ClientWebPage"]:
        custom_pages = sess().query(cls).filter(cls.brand_id == brand_id).all()
        return custom_pages

    @classmethod
    @hybrid_method
    def search(
            cls,
            search_text: str,
            lang: str | None = None,
            is_translation_joined: bool = False,
            translation_cls: "Type[models.Translation]" = None,
    ):
        search_text = search_text.strip().lower()
        conditions = [
            cls.title.contains(search_text),
            cls._internal_name.contains(search_text),
            cls.button_title.contains(search_text),
        ]

        # if lang:
        #     if is_translation_joined:
        #         if translation_cls is None:
        #             translation_cls = models.Translation
        #         conditions += [
        #             translation_cls.get_field_expression("name").contains(
        #             search_text),
        #             translation_cls.get_field_expression("description").contains(
        #                 search_text
        #             ),
        #         ]
        #     else:
        #         if translation_cls is None:
        #             translation_cls = aliased(models.Translation)
        #         conditions += [
        #             translation_cls.filter_by_translation(
        #                 cls, lang, "name",
        #                 search_text, id_expression=cls.id,
        #                 object_name=StoreProduct.__name__,
        #             ),
        #             translation_cls.filter_by_translation(
        #                 cls, lang, "description",
        #                 search_text, id_expression=cls.id,
        #                 object_name=StoreProduct.__name__,
        #             ),
        #         ]

        return or_(*conditions)


class ClientWebPageToStore(Base, BaseDBModel):
    client_web_page_id: int = Column(
        BigInteger, ForeignKey("client_web_pages.id"), nullable=False
    )
    store_id: int = Column(BigInteger, ForeignKey("stores.id"), nullable=False)

    __table_args__ = (
        UniqueConstraint(
            'client_web_page_id', 'store_id', name='uq_client_web_page_store'
        ),
    )


class ClientWebPageToInvoiceTemplate(Base, BaseDBModel):
    client_web_page_id: int = Column(
        BigInteger, ForeignKey("client_web_pages.id"), nullable=False
    )
    invoice_template_id: int = Column(
        BigInteger, ForeignKey("invoice_templates.id"), nullable=False
    )

    __table_args__ = (
        UniqueConstraint(
            'client_web_page_id', 'invoice_template_id',
            name='uq_client_web_page_invoice_template'
        ),
    )
