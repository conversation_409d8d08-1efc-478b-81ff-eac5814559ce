from datetime import datetime

from sqlalchemy import <PERSON>umn, <PERSON>olean, String, DateTime, UniqueConstraint

import schemas
from config import CONFIRMED_EMAIL_EXPIRES
from db import sess, db_func
from db.connection import Base
from db.mixins import BaseDBModel


class ConfirmEmailRequest(Base, BaseDBModel):
    is_confirmed: bool = Column(Boolean, default=False, nullable=False)

    email: str = Column(String(255), nullable=False)
    purpose: schemas.ConfirmEmailPurposeLiteral = Column(String(16), nullable=False)

    datetime_sent: datetime = Column(DateTime, default=None)
    datetime_confirmed: datetime_sent = Column(DateTime, default=None)

    __table_args__ = (
        UniqueConstraint("email", "purpose"),
    )

    @classmethod
    @db_func
    def delete_confirm_email_request(
            cls,
            email: str,
            purpose: str,
    ):
        stmt = sess().query(cls).filter(cls.email == email)
        stmt = stmt.filter(cls.purpose == purpose).one()

        sess().delete(stmt)
        sess().commit()

    @classmethod
    @db_func
    def check_is_confirmed_email(cls, email: str, purpose: str) -> bool:
        query = sess().query(cls).filter(cls.email == email)
        query = query.filter(cls.is_confirmed == True)
        query = query.filter(cls.purpose == purpose)
        query = query.filter(
            cls.datetime_confirmed > datetime.utcnow() - CONFIRMED_EMAIL_EXPIRES
        )

        query = sess().query(query.exists())
        return query.scalar()
