from sqlalchemy import <PERSON>Integer, Column, Float, Foreign<PERSON>ey, String
from sqlalchemy.orm import relationship
from typing import Literal

from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class PayerFee(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = 'payer_fees'

    invoice_id: int = Column(BigInteger, ForeignKey('invoices.id'))
    invoice: "models.Invoice | None" = relationship('Invoice', foreign_keys=invoice_id)

    status: Literal["pending", "payed"] = Column(String(99))

    type: Literal["payer_method"] = Column(String(99))
    payment_method: str = Column(String(99))

    payer_fee_percent: str = Column(String(99), nullable=True, default=None)
    payer_fee_value: str = Column(String(99), nullable=True, default=None)

    fee_percent: float = Column(Float, default=0)
    fee_value: float = Column(Float, default=0)

    fee: int = Column(BigInteger)
