from sqlalchemy import (
    BigInteger, <PERSON><PERSON>an, Column, ForeignKey, Index, Integer, String, Text,
    UniqueConstraint, func,
    or_,
)
from sqlalchemy.dialects.mysql import BIGINT, SMALLINT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import aliased, relationship
from typing import List, Type

import core.ext.types
import schemas
from db import db_func, models, sess
from db.connection import Base
from db.my_columns import NestedMutableJson
from ...helpers import hybrid_method
from ...mixins import BaseDBModel


class StoreProduct(Base, BaseDBModel):
    is_enabled: bool = Column(Boolean, default=True)
    product_id: str = Column(String(255))

    product_group_id: int = Column(
        BigInteger, ForeignKey("store_product_groups.id", ondelete="CASCADE")
    )
    product_group: "models.StoreProductGroup | None" = relationship(
        "StoreProductGroup", back_populates="products"
    )

    get_order_id: int = Column(Integer)

    price: int = Column(BIGINT, default=0)  # в бд умножена на 100 для учёта копеек
    old_price: int = Column(BIGINT, default=0)  # в бд умножена на 100 для учёта копеек

    name: str = Column(String(255))

    description: str = Column(Text(collation="utf8mb4_unicode_ci"), default=None)

    media_id: int | None = Column(
        BigInteger,
        ForeignKey("media_objects.id", ondelete="RESTRICT"),
    )
    media: "models.MediaObject" = relationship(
        "MediaObject", backref="thumbnails_store_products",
        foreign_keys=media_id
    )

    thumbnail_media_id: int | None = Column(
        BigInteger,
        ForeignKey("media_objects.id", ondelete="RESTRICT"),
    )
    thumbnail_media: "models.MediaObject" = relationship(
        "MediaObject", backref="store_products",
        foreign_keys=thumbnail_media_id
    )

    gallery_id: int | None = Column(
        BigInteger,
        ForeignKey("galleries.id", ondelete="RESTRICT")
    )
    gallery: "models.Gallery | None" = relationship(
        "Gallery", backref="store_products"
    )

    is_available: bool = Column(Boolean, default=True)
    is_deleted: bool = Column(Boolean, default=False)

    buy_min_quantity: int = Column(Integer, default=1)

    position: int = Column(SMALLINT(unsigned=True))

    attribute_groups: list["models.StoreAttributeGroup"] = relationship(
        "StoreAttributeGroup",
        secondary="attribute_groups_to_products",
        back_populates="products"
    )

    categories: list["models.StoreCategory"] = relationship(
        "StoreCategory",
        secondary="products_to_categories",
        back_populates="products",
    )

    stores: list["models.Store"] = relationship(
        "Store",
        secondary="product_to_stores",
        back_populates="products",
    )

    spots_prices: list["StoreProductSpotPrice"] = relationship(
        "StoreProductSpotPrice", back_populates="product"
    )

    brand_id: int = Column(
        BigInteger, ForeignKey("brands.id", ondelete="CASCADE"), nullable=False
    )
    brand: "models.Brand" = relationship("Brand", foreign_keys=brand_id)

    characteristics: list["models.StoreCharacteristicValue"] = relationship(
        "StoreCharacteristicValue", back_populates="product"
    )

    external_id: str | None = Column(String(255))
    external_type: core.ext.types.ExternalTypeLiteral | None = Column(String(99))

    is_weight: bool = Column(Boolean, default=False)
    weight_unit: str = Column(String(59))

    floating_sum_enabled: bool = Column(Boolean, default=False)
    floating_sum_min: int | None = Column(
        BIGINT, default=0
    )  # в бд умножена на 100 для учёта копеек
    floating_sum_max: int | None = Column(
        BIGINT, default=0
    )  # в бд умножена на 100 для учёта копеек
    floating_sum_options: list[float] | None = Column(NestedMutableJson, default=None)
    floating_sum_user_sum_enabled: bool = Column(Boolean, default=True)
    floating_sum_value: str | None = Column(String(255), default=None, nullable=True)

    type: schemas.ProductTypeLiteral = Column(String(255), default="goods")

    pti_info_text: str | None = Column(String(255), default=None, nullable=True)
    pti_info_link: str | None = Column(String(255), default=None, nullable=True)

    liqpay_id: str | None = Column(String(99), default=None, nullable=True)
    liqpay_unit_name: schemas.UnitName | None = Column(
        String(99), default=None, nullable=True
    )
    liqpay_codifier: str | None = Column(String(29), default=None, nullable=True)
    liqpay_tax_list: schemas.TaxList | None = Column(
        String(1), default=None, nullable=True
    )

    need_auth: bool = Column(Boolean, default=False)
    floating_qty_enabled: bool = Column(Boolean, default=False)

    MAX_NAME_LENGTH = 255

    _internal_name = Column(String(255), nullable=True, default=None)

    task_id: int | None = Column(
        BigInteger,
        ForeignKey("tasks.id", ondelete="SET NULL"),
    )
    task: "models.Task" = relationship(
        "Task", backref="store_product_task",
        foreign_keys=task_id
    )

    topup_server_api_url: str | None = Column(String(100), nullable=True, default=None)
    topup_terminal_api_key: str = Column(String(100), nullable=True, default=None)
    topup_account_id: str | None = Column(String(36), nullable=True, default=None)
    topup_enabled_card: bool | None = Column(Boolean, nullable=True, default=None)
    incust_terminal_id: str | None = Column(String(36), nullable=True, default=None)

    group_hash: str | None = Column(String(64), nullable=True, default=None)

    __table_args__ = (
        Index('idx_store_products_id_deleted', product_id, is_deleted),
    )

    @hybrid_property
    def internal_name(self):
        return self._internal_name or self.name

    @internal_name.expression
    def internal_name(self):
        return func.IFNULL(self._internal_name, self.name)

    @hybrid_property
    def raw_internal_name(self):
        return self._internal_name

    @raw_internal_name.setter
    def raw_internal_name(self, value):
        self._internal_name = value

    @classmethod
    @db_func
    def get_all(cls, brand_id: int) -> List["StoreProduct"]:
        products = sess().query(cls).filter(cls.brand_id == brand_id).all()
        return products

    @classmethod
    @hybrid_method
    def search(
            cls,
            search_text: str,
            lang: str | None = None,
            is_translation_joined: bool = False,
            translation_cls: "Type[models.Translation]" = None,
            product_id_contains: bool = False,
    ):
        search_text = search_text.strip().lower()
        conditions = [
            cls.name.contains(search_text),
            cls._internal_name.contains(search_text),
            cls.description.contains(search_text),
        ]
        if product_id_contains:
            conditions.append(cls.product_id.contains(search_text))
        else:
            conditions.append(cls.product_id == search_text)

        if lang:
            if is_translation_joined:
                if translation_cls is None:
                    translation_cls = models.Translation
                conditions += [
                    translation_cls.get_field_expression("name").contains(search_text),
                    translation_cls.get_field_expression("description").contains(
                        search_text
                    ),
                ]
            else:
                if translation_cls is None:
                    translation_cls = aliased(models.Translation)
                conditions += [
                    translation_cls.filter_by_translation(
                        cls, lang, "name",
                        search_text, id_expression=cls.id,
                        object_name=StoreProduct.__name__,
                    ),
                    translation_cls.filter_by_translation(
                        cls, lang, "description",
                        search_text, id_expression=cls.id,
                        object_name=StoreProduct.__name__,
                    ),
                ]

        return or_(*conditions)

    async def get_media(self) -> "models.MediaObject | None":
        return await models.MediaObject.get(self.media_id) if self.media_id else None

    async def get_thumbnail_media(self) -> "models.MediaObject | None":
        return await models.MediaObject.get(
            self.thumbnail_media_id
        ) if self.thumbnail_media_id else None

    async def get_gallery(self) -> "models.Gallery | None":
        return await models.Gallery.get(self.gallery_id) if self.gallery_id else None

    @property
    async def media_url(self):
        media = await self.get_media()
        return media.url if media else None

    @property
    async def thumbnail_media_url(self):
        media = await self.get_thumbnail_media()
        return media.url if media else None

    @hybrid_method
    def price_expressions(
            self, converted: bool = True, spot_price_cls: Type | None = None
    ):
        if spot_price_cls is None:
            spot_price_cls = models.StoreProductSpotPrice

        def convert_price(expr):
            if converted:
                return func.ROUND(expr / 100, 2)
            return expr

        return (
            convert_price(
                func.IFNULL(
                    spot_price_cls.price,
                    self.price
                )
            ).label("price"),
            convert_price(
                func.IFNULL(
                    spot_price_cls.old_price,
                    self.old_price
                )
            ).label("old_price"),
        )


class StoreProductSpotPrice(Base, BaseDBModel):
    product_id: int = Column(
        BigInteger, ForeignKey("store_products.id", ondelete="CASCADE"), nullable=False
    )
    product: "StoreProduct" = relationship(StoreProduct, back_populates="spots_prices")

    store_id: int = Column(
        BigInteger, ForeignKey("stores.id", ondelete="CASCADE"), nullable=False
    )
    store: "models.Store" = relationship("Store", backref="spots_prices")

    price: int = Column(BIGINT)  # в бд умножена на 100 для учёта копеек
    old_price: int = Column(
        BIGINT, default=0, nullable=True
    )  # в бд умножена на 100 для учёта копеек

    __table_args__ = (
        UniqueConstraint("product_id", "store_id"),
    )

    @classmethod
    @db_func
    def get_list_by_store_ids(cls, store_ids: list[int]) -> list[
        "StoreProductSpotPrice"]:
        query = cls.get_expression()
        query = query.filter(cls.store_id.in_(store_ids))
        return query.all()


class ProductToCategory(Base):
    __tablename__ = "products_to_categories"

    id: int = Column(BigInteger, primary_key=True, autoincrement=True)
    product_id: int = Column(
        BigInteger, ForeignKey("store_products.id"), nullable=False
    )
    category_id: int = Column(
        BigInteger, ForeignKey("store_categories.id"), nullable=False
    )

    __table_args__ = (
        UniqueConstraint('category_id', 'product_id', name='_product_category_uc'),
    )


class ProductToStore(Base, BaseDBModel):

    product_id: int = Column(
        BigInteger, ForeignKey("store_products.id"), nullable=False
    )
    store_id: int = Column(BigInteger, ForeignKey("stores.id"), nullable=False)

    __table_args__ = (
        UniqueConstraint('product_id', 'store_id', name='uq_product_store'),
    )
