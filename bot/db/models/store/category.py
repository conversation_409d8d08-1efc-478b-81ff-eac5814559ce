from sqlalchemy import (
    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String,
    UniqueConstraint, or_,
)
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.ext.hybrid import hybrid_method
from sqlalchemy.orm import relationship
from typing import Optional, Type

import core.ext.types
from db import models, sess
from db.connection import Base
from db.mixins import BaseDBModel


class StoreCategory(Base, BaseDBModel):
    __tablename__ = "store_categories"

    is_deleted: bool = Column(Boolean, default=False, nullable=False)
    is_default: bool = Column(Boolean, default=False, nullable=False)

    get_order_id: int | None = Column(Integer)

    external_id: str | None = Column(String(255))
    external_type: core.ext.types.ExternalTypeLiteral | None = Column(String(20))

    name: str = Column(String(255))
    position: int = Column(SMALLINT(unsigned=True))

    media_id: int | None = Column(
        BigInteger, ForeignK<PERSON>("media_objects.id", ondelete="SET NULL")
    )
    media: "models.MediaObject | None" = relationship(
        "MediaObject", backref="categories"
    )

    stores: list["models.Store"] = relationship(
        "Store",
        secondary="store_categories_to_stores",
        back_populates="categories",
    )

    brand_id: int = Column(BigInteger, ForeignKey("brands.id", ondelete="CASCADE"))
    brand: "models.Brand" = relationship("Brand", backref="categories")

    father_category_id: int | None = Column(
        BigInteger, ForeignKey("store_categories.id", ondelete="CASCADE")
    )
    father_category: Optional["StoreCategory"] = relationship(
        "StoreCategory",
        primaryjoin="StoreCategory.father_category_id == StoreCategory.id",
        foreign_keys=father_category_id,
        remote_side="StoreCategory.id",
        backref="child_categories",
    )

    products: list["models.StoreProduct"] = relationship(
        "StoreProduct",
        secondary="products_to_categories",
        back_populates="categories",
    )

    filters: list["models.StoreCharacteristic"] = relationship(
        "StoreCharacteristic",
        secondary="store_category_filters",
        backref="categories",
    )

    @hybrid_method
    def get_recursive_cte(self: Type["StoreCategory"], category_id: int, cte_name: str):
        top_query = sess().query(self.id)
        top_query = top_query.filter(self.id == category_id)
        top_query = top_query.cte(cte_name, recursive=True)

        bottom_query = sess().query(self.id)
        bottom_query = bottom_query.join(
            top_query, self.father_category_id == top_query.c.id
        )

        return top_query.union(bottom_query)

    @hybrid_method
    def get_child_categories_ids_query(
            self: Type["StoreCategory"],
            category_id: int,
            cte_name: str = "cte",
            exclude_current_category: bool = True,
    ):
        categories = self.get_recursive_cte(category_id, cte_name)
        query = sess().query(categories)
        if exclude_current_category:
            query = query.filter(categories.c.id != category_id)
        return query

    @hybrid_method
    def filter_recursive(self: Type["StoreCategory"], category_id: int):
        query = self.get_child_categories_ids_query(category_id)
        return or_(self.id.in_(query), self.id == category_id)

    async def get_image_media(self):
        if media := await models.MediaObject.get(self.media_id):
            return media
        return None

    @property
    async def image_url(self):
        if media := await self.get_image_media():
            return media.url
        return None


class StoreCategoryToStore(Base):
    __tablename__ = "store_categories_to_stores"

    id: int = Column(BigInteger, autoincrement=True, primary_key=True)
    category_id: int = Column(BigInteger, ForeignKey("store_categories.id"))
    store_id: int = Column(BigInteger, ForeignKey("stores.id"))

    __table_args__ = (
        UniqueConstraint('category_id', 'store_id', name='uq_category_store'),
    )


class StoreCategoryFilter(Base, BaseDBModel):
    category_id: int = Column(
        BigInteger, ForeignKey("store_categories.id", ondelete="CASCADE")
    )
    characteristic_id: int = Column(
        BigInteger, ForeignKey("store_characteristics.id", ondelete="CASCADE")
    )
