import base64
import logging
import re
from datetime import datetime
from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, ForeignKey, Integer, String, and_, desc,
    func,
)
from sqlalchemy.dialects.mysql import BIGINT, JSON
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship
from typing import Any, Type
from urllib.parse import quote_plus

import schemas
from config import DEFAULT_TIME_ZONE
from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel
from schemas import OrderShippingStatusEnum
from utils.date_time import utcnow
from utils.type_vars import T

logger = logging.getLogger('debugger')


class StoreOrder(Base, BaseDBModel):
    type: schemas.OrderType = Column(String(255), default="regular")
    currency: str = Column(String(3))
    create_date = Column(DateTime, default=utcnow)

    _status: str = Column(String(100))
    _status_pay: str = Column(String(100))

    first_name = Column(String(1024), nullable=True)
    last_name = Column(String(1024), nullable=True)
    phone = Column(String(1024), nullable=True)
    email = Column(String(1024), nullable=True)

    delivery_address = Column(String(1024), nullable=True)

    address_lat = Column(String(30), nullable=True)
    address_lng = Column(String(30), nullable=True)
    address_place_id = Column(String(1024), nullable=True)

    address_comment = Column(String(1024), nullable=True)

    address_street = Column(String(100), nullable=True)
    address_house = Column(String(100), nullable=True)
    address_flat = Column(String(100), nullable=True)
    address_floor = Column(Integer, nullable=True)
    address_entrance = Column(Integer, nullable=True)

    desired_delivery_date = Column(DateTime, nullable=True)
    desired_delivery_time = Column(String(10), nullable=True)

    comment = Column(String(1024), nullable=True)

    timezone = Column(String(63), default=DEFAULT_TIME_ZONE)

    before_loyalty_sum = Column(
        BIGINT, nullable=False, default=0
    )  # total sum, tips excluded, before loyalty
    total_sum: int = Column(
        BIGINT, nullable=True
    )  # total sum (items_total_sum + shipment_cost + custom_payment_cost) ,
    # tips excluded
    tips_sum = Column(Integer, nullable=False, default=0)  # tips sum

    bonuses_redeemed = Column(
        BIGINT, nullable=False, default=0
    )  # бонусы списанные с лояльности
    discount = Column(BIGINT, nullable=False, default=0)  # примененная скидка
    discount_and_bonuses = Column(
        BIGINT, nullable=False, default=0
    )  # сума знижки разом із бонусами

    is_ewallet_discount: bool = Column(Boolean, default=False)

    total_sum_with_extra_fee: int = Column(
        BIGINT, nullable=False, default=0
    )  # total sum, after extra fee
    sum_to_pay: int = Column(
        BIGINT, nullable=True,
    )  # total sum, tips & extra_fee included

    payer_fee: int = Column(BIGINT, default=0)
    paid_sum: int = Column(BIGINT, default=0)  # paid sum (sum_to_pay + payer_fee)

    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user: "models.User" = relationship("User", foreign_keys=user_id)

    store_id: int = Column(BigInteger, ForeignKey("stores.id", ondelete="CASCADE"))
    store: "models.Store" = relationship("Store", foreign_keys=store_id)

    brand_name: str = Column(String(1024))

    order_products: list["models.OrderProduct"] = relationship(
        "OrderProduct", back_populates="store_order"
    )

    menu_in_store_id: int | None = Column(
        BigInteger,
        ForeignKey("menus_in_store.id", ondelete="RESTRICT"),
        nullable=True,
    )
    menu_in_store: "models.MenuInStore | None" = relationship(
        "MenuInStore", backref="store_orders"
    )

    invoice_id: int = Column(
        BigInteger, ForeignKey("invoices.id", ondelete="CASCADE"), nullable=True
    )
    invoice: "models.Invoice | None" = relationship(
        "Invoice", back_populates="store_order"
    )

    delivery_method = Column(String(1024), nullable=True)
    payment_method = Column(String(1024), nullable=True)  # TODO: payments, remove

    get_order_id = Column(BigInteger, nullable=True)

    external_order = relationship(
        "ExternalOrder",
        primaryjoin="ExternalOrder.order_id==StoreOrder.id",
        uselist=False
    )

    loyalty_type = Column(String(100), nullable=True, default=None)  # тип лояльности

    # DEPRECATED: Наступні поля більше не використовуються, лояльність обробляється через Invoice
    # Залишені для сумісності з існуючими даними, будуть видалені в майбутніх версіях
    incust_export = Column(
        JSON, nullable=True, default=None
    )  # DEPRECATED: дані експорту чека в inCust
    incust_loyalty_check = Column(
        JSON, nullable=True, default=None
    )  # DEPRECATED: чек лояльности
    original_incust_loyalty_check = Column(
        JSON, nullable=True, default=None
    )  # DEPRECATED: оригинальный чек лояльности
    auto_cancelled_loyalty_transaction: bool = Column(Boolean, default=False)  # DEPRECATED

    shipping_statuses = relationship(
        "OrderShippingStatus", back_populates="store_order"
    )  # зв'язок із статусами замовлення

    billing_address = relationship(
        "StoreOrderBillingAddress", uselist=False,
        back_populates="order", foreign_keys="StoreOrderBillingAddress.order_id"
    )

    shipment: "models.OrderShipment" = relationship(
        "OrderShipment", uselist=False,
        back_populates="store_order", foreign_keys="OrderShipment.store_order_id"
    )
    order_payment: "models.StoreOrderPayment" = relationship(
        "StoreOrderPayment", uselist=False,
        back_populates="order", foreign_keys="StoreOrderPayment.order_id"
    )

    custom_payment: "models.OrderCustomPayment" = relationship(
        "OrderCustomPayment", uselist=False,
        back_populates="store_order", foreign_keys="OrderCustomPayment.store_order_id"
    )  # TODO: payments, remove

    store_custom_payment_id: int = Column(
        BigInteger,
        ForeignKey("brand_custom_settings.id", ondelete="SET NULL"),
        nullable=True,
    )  # TODO: payments, remove
    store_custom_payment: "models.BrandCustomSettings" = relationship(
        "BrandCustomSettings",
        foreign_keys=store_custom_payment_id
    )  # TODO: payments, remove

    friend_id: int = Column(
        BigInteger,
        ForeignKey("friends.id", ondelete="SET NULL"),
        nullable=True,
    )
    date_sent_to_friend = Column(DateTime, default=None, nullable=True)

    cart_id: int = Column(
        BigInteger, ForeignKey("store_cart.id", ondelete="CASCADE"), nullable=True
    )
    cart: "models.StoreCart" = relationship("StoreCart", foreign_keys=cart_id)
    token: str = Column(String(1024), nullable=True)

    utm_labels = Column(
        JSON, nullable=True, default=None
    )  # UTM labels for the order

    def __init__(
            self,
            brand_name: str,
            user: "models.User",
            store: "models.Store",
            status: str,
            status_pay: str,
            currency: str,
            delivery_address: str = None,
            payment_method: str = None,
            first_name: str = None,
            last_name: str = None,
            phone: str = None,
            email: str = None,
            address_comment: str = None,
            desired_delivery_date: datetime = None,
            desired_delivery_time: str = None,
            address_street: str = None,
            address_house: str = None,
            address_flat: str = None,
            address_floor: int = None,
            address_entrance: int = None,
            comment: str = None,
            bonuses_redeemed: int = 0,
            loyalty_type: str = None,
            discount: int = 0,
            discount_and_bonuses: int = 0,
            menu_in_store_id: int | None = None,
            tips_sum: int = 0,
            friend_id: int | None = None,
            address_lat: str | None = None,
            address_lng: str | None = None,
            address_place_id: str | None = None,
            type: schemas.OrderType = "regular",
            timezone: str = DEFAULT_TIME_ZONE,
            cart_id: int | None = None,
            utm_labels: dict | None = None,
    ):
        self.brand_name = brand_name
        self.user = user
        self.store = store
        self._status = status
        self._status_pay = status_pay
        self.delivery_address = delivery_address
        self.payment_method = payment_method
        self.first_name = first_name
        self.last_name = last_name
        self.phone = phone
        self.email = email
        self.address_comment = address_comment
        self.desired_delivery_date = desired_delivery_date
        self.desired_delivery_time = desired_delivery_time

        self.address_street = address_street
        self.address_house = address_house
        self.address_flat = address_flat
        self.address_floor = address_floor
        self.address_entrance = address_entrance
        self.comment = comment

        self.bonuses_redeemed = float(bonuses_redeemed) * 100 if bonuses_redeemed else 0
        self.loyalty_type = loyalty_type
        self.discount = float(discount) * 100 if discount else 0

        self.discount_and_bonuses = float(
            discount_and_bonuses
        ) * 100 if discount_and_bonuses else 0
        self.currency = currency
        self.menu_in_store_id = menu_in_store_id

        self.tips_sum = float(tips_sum) * 100 if tips_sum else 0

        self.friend_id = friend_id

        self.address_lat = address_lat
        self.address_lng = address_lng
        self.address_place_id = address_place_id

        self.type = type
        self.timezone = timezone

        self.cart_id = cart_id

        self.utm_labels = utm_labels or {}

        super().__init__()

    @property
    def time_created(self):
        return self.create_date

    @hybrid_property
    def full_name(self):
        return f"{self.first_name} {self.last_name}" if all(
            (self.first_name, self.last_name)
        ) else self.first_name or self.last_name

    @full_name.expression
    def full_name(self):
        return func.TRIM(
            func.IFNULL(
                func.concat(self.first_name, ' ', self.last_name),
                func.IFNULL(self.first_name, self.last_name)
            )
        ).label("full_name")

    @hybrid_property
    def status(self):
        return self._status

    @status.expression
    def status(self):
        return self._status

    @hybrid_property
    def status_pay(self):
        return self._status_pay

    @property
    def shipment_status(self):
        shipment_status = sess().query(
            models.OrderShippingStatus.status
        ).filter(
            models.OrderShippingStatus.store_order_id == self.id
        ).filter(
            models.OrderShippingStatus.status.in_(
                [
                    OrderShippingStatusEnum.WAIT_FOR_SHIP.value,
                    OrderShippingStatusEnum.SHIPPED.value,
                    OrderShippingStatusEnum.IN_TRANSIT.value,
                    OrderShippingStatusEnum.DELIVERED.value,
                ]
            )
        ).order_by(desc(models.OrderShippingStatus.time_created)).first()
        if shipment_status:
            return shipment_status.status
        return None

    @db_func
    def add_product(self, order_product: "models.OrderProduct"):
        self.order_products.append(order_product)
        sess().commit()
        return True

    @classmethod
    @db_func
    def get_by_user(
            cls,
            store_id: int,
            user_id: int,
            date_start: datetime = None,
            date_end: datetime = None,
            order_status: str = None,
            count: bool = False,
    ):
        if count:
            res = sess().query(func.count(cls.id)).filter(cls.user_id == user_id)
            res = res.filter(cls.store_id == store_id)
            res = res.filter(
                cls._status == OrderShippingStatusEnum.OPEN_CONFIRMED.value
                or cls._status == OrderShippingStatusEnum.OPEN_UNCONFIRMED.value
            )

            return res.scalar()

        res = sess().query(cls).filter(cls.user_id == user_id)
        res = res.filter(cls.store_id == store_id)
        res = res.filter(cls._status != OrderShippingStatusEnum.NEW.value)
        res = res.filter(
            cls._status != OrderShippingStatusEnum.TERMINATED_BY_USER.value
        )
        if date_start and date_end:
            res = res.filter(cls.create_date.between(date_start, date_end))
        if order_status:
            if order_status != "all":
                res = res.filter(cls._status == order_status)
        res = res.order_by(cls.create_date.desc())

        return res.all()

    @db_func
    def confirm(self):
        self._status = OrderShippingStatusEnum.OPEN_CONFIRMED.value
        sess().commit()
        return True

    @db_func
    def confirm_pay(self):
        self._status_pay = OrderShippingStatusEnum.PAYED.value
        sess().commit()
        return True

    @db_func
    def cancel(self, ):
        self._status = OrderShippingStatusEnum.CANCELED.value
        sess().commit()
        return True

    @db_func
    def closed(self):
        self._status = OrderShippingStatusEnum.CLOSED.value
        self._status_pay = OrderShippingStatusEnum.PAYED.value
        sess().commit()
        return True

    @classmethod
    @db_func
    def get_by_invoice(
            cls,
            invoice_id: int,
    ) -> "StoreOrder | None":
        order = sess().query(cls).filter(cls.invoice_id == invoice_id).one()
        return order

    @classmethod
    @db_func
    def get_by_pmt_uuid_id(cls, uuid_id: str) -> "StoreOrder | None":
        order = sess().query(cls). \
            join(models.Payment, models.Payment.invoice_id == StoreOrder.invoice_id). \
            filter(models.Payment.uuid_id == uuid_id).one()
        return order

    @db_func
    def set_get_order_id(self, get_order_id: int):
        self.get_order_id = get_order_id
        sess().commit()

    @staticmethod
    def extract_address_from_google_place_id(encoded_str: str) -> str | None:
        try:
            padded = encoded_str + '=' * (-len(encoded_str) % 4)
            decoded_bytes = base64.urlsafe_b64decode(padded)
            decoded_str = decoded_bytes.decode("utf-8", errors="ignore")
            match = re.search(r"([А-Яа-яA-Za-z0-9\s.,-]{10,})", decoded_str)
            if match:
                return match.group(1).strip()
        except Exception:
            pass
        return None

    @hybrid_property
    def map_link(self) -> str | None:
        map_link = None
        if (self.address_lat and self.address_lng) or self.address_place_id:
            if self.address_place_id:
                if self.address_place_id.startswith("ChI"):
                    return (
                        f"https://www.google.com/maps/place/?"
                        f"q=place_id:{self.address_place_id}"
                    )
                else:
                    # Added for working with Google Place IDs in a different format
                    extracted = self.extract_address_from_google_place_id(
                        self.address_place_id
                    )
                    query = extracted or self.address_place_id
                    encoded = quote_plus(query)
                    return f"https://www.google.com/maps/search/?api=1&query={encoded}"

            return (
                f"https://maps.google.com/maps/?"
                f"q={self.address_lat},{self.address_lng}"
            )
        return map_link

    @map_link.expression
    def map_link(self):
        return func.IF(
            self.address_place_id.is_not(None),
            func.CONCAT(
                f"https://www.google.com/maps/place/?q=place_id:",
                self.address_place_id,
            ),
            func.IF(
                and_(
                    self.address_lat.is_not(None),
                    self.address_lng.is_not(None),
                ),
                func.CONCAT(
                    f"https://maps.google.com/maps/?q=",
                    self.address_lat,
                    ",",
                    self.address_lng,
                ),
                None,
            )
        )

    @hybrid_property
    def address_coordinates(self):
        if self.address_lat and self.address_lng:
            return f"{self.address_lat},{self.address_lng}"
        return None

    @address_coordinates.expression
    def address_coordinates(self):
        return func.IF(
            and_(
                self.address_lat.is_not(None),
                self.address_lng.is_not(None),
            ),
            func.CONCAT(
                self.address_lat,
                ",",
                self.address_lng,
            ),
            None,
        )

    @property
    def converted_sums(self):
        return dict(
            before_loyalty_sum=round(self.before_loyalty_sum / 100, 2),
            total_sum=round(self.total_sum / 100, 2),
            tips_sum=round(self.tips_sum / 100, 2),
            sum_to_pay=round(self.sum_to_pay / 100, 2),
            bonuses_redeemed=round(self.bonuses_redeemed / 100, 2),
            discount=round(self.discount / 100, 2),
            discount_and_bonuses_redeemed=round(self.discount_and_bonuses / 100, 2),
            payer_fee=round(self.payer_fee / 100, 2),
            paid_sum=round(self.paid_sum / 100, 2),
        )

    @property
    def converted_dict(self):
        return {
            **self.__dict__,
            **self.converted_sums,
        }

    def set_converted_sums(self, obj: Any):
        for key, value in self.converted_sums.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
        return obj

    def from_orm_converted(self, model: Type[T]) -> T:
        return self.set_converted_sums(model.from_orm(self))

    @hybrid_property
    def discount_and_bonuses_redeemed(self):
        return self.discount_and_bonuses

    @hybrid_property
    def time_created(self):
        return self.create_date

    @hybrid_property
    def crm_tag(self):
        return f"order-{self.id}"

    @crm_tag.expression
    def crm_tag(self):
        return func.concat("order-", self.id)
