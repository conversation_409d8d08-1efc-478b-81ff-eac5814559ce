from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.orm import relationship

from db import db_func, sess, models
from db.connection import Base
from db.mixins import BaseDBModel


class StoreCartAttribute(Base, BaseDBModel):
    quantity: int = Column(Integer)

    attribute_id: int = Column(
        BigInteger,
        ForeignKey("store_attributes.id", ondelete="CASCADE")
    )
    attribute: "models.StoreAttribute" = relationship(
        "StoreAttribute",
        foreign_keys=attribute_id,
        backref="cart_attributes"
    )

    cart_product_id: int = Column(
        BigInteger,
        ForeignKey("store_cart_product.id", ondelete="CASCADE")
    )
    cart_product: "models.StoreCartProduct" = relationship(
        "StoreCartProduct",
        back_populates="cart_attributes"
    )

    @classmethod
    @db_func
    def save(
            cls,
            quantity: int,
            attribute_id: int,
            store_id: int
    ) -> "StoreCartAttribute":
        cart_attribute = cls(
            quantity=quantity,
            attribute_id=attribute_id,
            store_id=store_id
        )
        sess().add(cart_attribute)
        sess().commit()
        return cart_attribute

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k not in dir(self) or k == "id":
                continue

            setattr(self, k, v)

        sess().commit()
        return True

    @classmethod
    @db_func
    def get(cls, cart_attribute_id: int) -> "StoreCartAttribute":
        brand = sess().query(cls).filter(cls.id == cart_attribute_id).one()
        return brand

    @db_func
    def delete(self):
        sess().delete(self)
        sess().commit()
        return True
