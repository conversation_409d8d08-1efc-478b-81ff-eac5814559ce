from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, func
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship
from typing import List, Type

import core.ext.types
from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson


class Store(Base, BaseDBModel):
    is_enabled: bool = Column(Boolean, default=True)
    is_deleted: bool = Column(Boolean, default=False)

    external_id: str | None = Column(String(255))
    external_type: core.ext.types.ExternalTypeLiteral | None = Column(String(99))

    get_order_id: int | None = Column(Integer)

    location_get_order_id: int | None = Column(Integer)
    location_get_order_unique_id: str | None = Column(String(255))

    name: str = Column(String(255), nullable=False)
    description: str | None = Column(String(1024))
    ai_description: str | None = Column(String(4096))

    media_id: int | None = Column(
        BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL")
    )
    media: "models.MediaObject" = relationship(
        "MediaObject", foreign_keys=media_id, backref="stores"
    )

    position: int | None = Column(SMALLINT(unsigned=True), nullable=True)
    excel_row_number: int | None = Column(
        SMALLINT(unsigned=True), nullable=True
    )  # for soring by Excel order

    is_distance: bool = Column(Boolean, default=False)
    is_polygon: bool = Column(Boolean, default=False)
    is_swap_coordinates: bool = Column(Boolean, default=True)

    latitude: str | None = Column(String(255))
    longitude: str | None = Column(String(255))
    polygon: dict | None = Column(NestedMutableJson, default=None, nullable=True)
    distance: int = Column(Integer, default=0)

    organisation_id: int | None = Column(Integer)

    _currency: str = Column(String(255), default="UAH", nullable=False)

    custom_fields: list["models.StoreCustomField"] = relationship(
        "StoreCustomField", back_populates="store"
    )

    company: str | None = Column(String(255), nullable=True, default=None)
    company_url: str | None = Column(String(255), nullable=True, default=None)
    data_url: str | None = Column(String(1024), nullable=True, default=None)

    description_media_id: int | None = Column(
        BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL")
    )
    description_media: "models.MediaObject | None" = relationship(
        "MediaObject",
        foreign_keys=description_media_id,
        backref="store_descriptions"
    )

    offer_media_id: int | None = Column(
        BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL")
    )
    offer_media: "models.MediaObject | None" = relationship(
        "MediaObject",
        foreign_keys=offer_media_id,
        backref="store_offers"
    )

    banners: list | None = Column(NestedMutableJson, default=None)

    city: str = Column(String(255))

    brand_id: int = Column(BigInteger, ForeignKey("brands.id", ondelete="CASCADE"))
    brand: "models.Brand" = relationship("Brand", foreign_keys=brand_id)

    products: list["models.StoreProduct"] = relationship(
        "StoreProduct",
        secondary="product_to_stores",
        back_populates="stores",
    )

    client_web_pages: list["models.ClientWebPage"] = relationship(
        "ClientWebPage",
        secondary="client_web_page_to_stores",
        back_populates="stores",
    )

    categories: list["models.StoreCategory"] = relationship(
        "StoreCategory",
        secondary="store_categories_to_stores",
        back_populates="stores",
    )

    tg_payment_token = Column(String(256), nullable=True, default=None)

    working_days = relationship('WorkingDay', back_populates='store')

    is_enabled_emenu: bool = Column(Boolean, default=False)

    incust_terminal_api_key: str | None = Column(
        String(512), nullable=True, default=None
    )

    incust_terminal_id: str | None = Column(
        String(512), nullable=True, default=None
    )

    task_id: int | None = Column(
        BigInteger,
        ForeignKey("tasks.id", ondelete="SET NULL"),
    )
    task: "models.Task" = relationship(
        "Task", backref="store_task",
        foreign_keys=task_id
    )

    @property
    def is_translate(self):
        return self.brand.group.is_translate

    @classmethod
    @db_func
    def get_all(cls, brand_id: int) -> List["Store"]:
        stores = sess().query(cls).filter(cls.brand_id == brand_id).all()
        stores = stores if stores else list()
        return stores

    def get_banner_by_name(
            self, banner_name: str | None,
    ) -> bool | dict:
        if not banner_name:
            return False

        for banner in self.banners:
            if str(banner.get("name")) == str(banner_name):
                return banner

        return False

    @db_func
    def delete_banner_by_name(self, banner_name: str | None):
        if not banner_name:
            return False
        self.banners = [item for item in self.banners if
                        str(item.get("name")) != str(banner_name)]
        sess().commit()

        return True

    @hybrid_property
    def currency(self) -> str:
        return self._currency.upper()

    @currency.expression
    def currency(self):
        return func.upper(self._currency)

    @currency.setter
    def currency(self, value: str):
        self._currency = value.upper()

    async def get_image_media(self):
        if self.media_id:
            return await models.MediaObject.get(self.media_id)
        return None

    @hybrid_property
    async def image_media_url(self):
        if media := await self.get_image_media():
            return media.url
        return None

    @image_media_url.expression
    def image_media_url(self: Type["Store"]):
        """Store.media have to be joined"""
        return models.MediaObject.url
