from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel


class StoreCart(Base, BaseDBModel):
    __tablename__ = "store_cart"
    cart_products: list["models.StoreCartProduct"] = relationship(
        "StoreCartProduct",
        back_populates="cart"
    )

    user_id: int | None = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user: "models.User | None" = relationship("User", foreign_keys=user_id, backref="cart")

    store_id: int = Column(BigInteger, ForeignKey("stores.id", ondelete="CASCADE"))
    store: "models.Store" = relationship("Store", foreign_keys=store_id, backref="carts")

    __table_args__ = (
        UniqueConstraint("store_id", "user_id"),
    )

    @classmethod
    @db_func
    def get(cls, cart_id: int) -> "StoreCart":
        cart = sess().query(cls).filter(cls.id == cart_id).one()
        return cart

    @classmethod
    @db_func
    def get_by_user(cls, user_id: int, store_id: int):
        cart = sess().query(cls).filter(cls.user_id == user_id)
        cart = cart.filter(cls.store_id == store_id).one()
        return cart

    @classmethod
    def save_sync(cls, store_id: int, user: Optional["models.User"] = None, no_commit: bool = False):
        if user:
            try:
                is_exist = (sess().query(cls)
                            .filter(cls.store_id == store_id, cls.user_id == user.id)
                            .with_for_update(nowait=True)
                            .one_or_none())
                if is_exist:
                    return is_exist
            except Exception:
                is_exist = (sess().query(cls)
                            .filter(cls.store_id == store_id, cls.user_id == user.id)
                            .one_or_none())
                if is_exist:
                    return is_exist

            cart = cls(
                store_id=store_id,
                user=user
            )
        else:
            cart = cls(
                store_id=store_id,
            )
        sess().add(cart)

        if not no_commit:
            try:
                sess().commit()
            except Exception as e:
                sess().rollback()
                if user:
                    is_exist = (sess().query(cls)
                                .filter(cls.store_id == store_id, cls.user_id == user.id)
                                .one_or_none())
                    if is_exist:
                        return is_exist
                raise e
        return cart

    @classmethod
    @db_func
    def save(cls, store_id: int, user: Optional["models.User"] = None):
        return cls.save_sync(store_id, user)

    @db_func
    def delete(self) -> bool:
        return self.delete_sync()
    
    def delete_sync(self, no_commit: bool = False) -> bool:
        sess().delete(self)
        if not no_commit:
            sess().commit()
        return True

    @db_func
    def attach_cart_to_user(self, user_id: int) -> bool:
        return self.attach_cart_to_user_sync(user_id)
    
    def attach_cart_to_user_sync(self, user_id: int, no_commit: bool = False) -> bool:
        self.user_id = user_id
        if not no_commit:
            sess().commit()
        return True

    @db_func
    def clear(self):
        for cart_product in self.cart_products:
            for attribute in cart_product.cart_attributes:
                sess().delete(attribute)
            sess().delete(cart_product)
        sess().commit()
        return True

    def is_product_in_cart(self, product_id: int) -> bool:
        if self.cart_products:
            has_product = any(i.product_id == product_id for i in self.cart_products)
            return has_product
        return False
