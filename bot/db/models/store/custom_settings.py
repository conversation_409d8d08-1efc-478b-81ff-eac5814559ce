from sqlalchemy import (
    <PERSON><PERSON><PERSON><PERSON>, <PERSON>olean, <PERSON>umn, Foreign<PERSON>ey, String, Text,
    select,
)
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship
from sqlalchemy.sql import Select

from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson
from schemas.store.types import (
    CustomType, CustomTypeLiteral, DeliveryTimeTypeLiteral,
    ShipmentBaseTypeLiteral,
)
from utils.text import f


class PaymentToShipment(Base, BaseDBModel):
    __tablename__ = "payments_to_shipments"

    payment_id = Column(BigInteger, ForeignKey("brand_custom_settings.id"))
    shipment_id = Column(BigInteger, ForeignKey("brand_custom_settings.id"))


class PaymentSettingsToShipment(Base, BaseDBModel):
    __tablename__ = "payments_settings_to_shipments"

    payment_settings_id = Column(BigInteger, Foreign<PERSON>ey("payment_settings.id"))
    shipment_id = Column(BigInteger, ForeignKey("brand_custom_settings.id"))


class BrandCustomSettings(Base, BaseDBModel):
    __tablename__ = "brand_custom_settings"

    id: int = Column(BigInteger, autoincrement=True, primary_key=True)
    brand_id: int = Column(
        BigInteger, ForeignKey("brands.id", ondelete="CASCADE"), nullable=False
    )

    _name: str = Column(String(255, collation="utf8mb4_unicode_ci"), nullable=False)
    description: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    icon_path: str | None = Column(String(255), nullable=True, default=None)
    _price: int = Column(BIGINT, default=0)

    need_comment: bool = Column(Boolean, default=True)
    label_comment: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    info: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    custom_settings_group_id: int | None = Column(
        BigInteger, ForeignKey("brand_custom_settings.id", ondelete="SET NULL"),
        nullable=True, default=None, unique=False,
    )
    custom_settings_group: "BrandCustomSettings | None" = relationship(
        "BrandCustomSettings",
        uselist=False,
        primaryjoin=custom_settings_group_id == id,
        foreign_keys=custom_settings_group_id, remote_side=id,
        backref="custom_settings",
    )

    payments: list["BrandCustomSettings"] = relationship(
        "BrandCustomSettings",
        secondary="payments_to_shipments",
        primaryjoin=id == PaymentToShipment.shipment_id,
        secondaryjoin=id == PaymentToShipment.payment_id,
    )

    payment_settings: list["models.PaymentSettings"] = relationship(
        "PaymentSettings",
        secondary="payments_settings_to_shipments",
        primaryjoin=id == PaymentSettingsToShipment.shipment_id,
        secondaryjoin="PaymentSettings.id == "
                      "PaymentSettingsToShipment.payment_settings_id",
    )

    custom_type: CustomTypeLiteral = Column(
        String(50, collation="utf8mb4_unicode_ci"), nullable=False
    )
    base_type: ShipmentBaseTypeLiteral | None = Column(
        String(255, collation="utf8mb4_unicode_ci"),
        nullable=True, default=None
    )

    is_paid_separately: bool = Column(Boolean, default=False)
    _min_price: int = Column(BIGINT, default=0)
    _max_price: int = Column(BIGINT, default=0)

    delivery_time_enabled: bool = Column(Boolean, default=True)
    delivery_datetime_mode: DeliveryTimeTypeLiteral = Column(
        String(50, collation="utf8mb4_unicode_ci"), nullable=False, default="datetime"
    )
    delivery_time_warning_enabled: bool = Column(Boolean, default=False)

    allow_online_payment: bool = Column(Boolean, default=True)
    allow_cash_payment: bool = Column(Boolean, default=True)

    is_enabled: bool = Column(Boolean, nullable=False, default=True)

    enabled_tips: bool = Column(Boolean, default=False)
    need_address: bool = Column(Boolean, default=True)
    _map_countries_list = Column(NestedMutableJson, default=None)
    enabled_any_address_from_map: bool = Column(Boolean, default=False)

    liqpay_id: str | None = Column(String(255), nullable=True, default=None)

    media_id: int | None = Column(
        BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL")
    )
    media: "models.MediaObject" = relationship(
        "MediaObject", foreign_keys=media_id, backref="brand_custom_settings"
    )

    is_deleted: bool = Column(Boolean, default=False)

    def __init__(
            self,
            brand_id: int,
            name: str,
            custom_type: CustomTypeLiteral,
            base_type: ShipmentBaseTypeLiteral | None = None,
            custom_settings_group_id: int | None = None,
    ):
        super().__init__(
            brand_id=brand_id,
            name=name,
            custom_type=custom_type,
            base_type=base_type,
            custom_settings_group_id=custom_settings_group_id,
        )

    @classmethod
    @db_func
    def create(
            cls, brand_id: int, name: str,
            custom_type: CustomTypeLiteral,
            base_type: ShipmentBaseTypeLiteral | None = None,
            custom_settings_group_id: int | None = None,
    ) -> "BrandCustomSettings":
        custom_settings = cls(
            brand_id=brand_id,
            name=name,
            custom_type=custom_type,
            base_type=base_type,
            custom_settings_group_id=custom_settings_group_id,

        )

        sess().add(custom_settings)
        sess().commit()
        return custom_settings

    @classmethod
    @db_func
    def get_or_create_base_shipment(
            cls, brand_id: int,
            base_type: ShipmentBaseTypeLiteral | None = None,
    ) -> "BrandCustomSettings":
        stmt: Select = select(cls)
        stmt = stmt.where(BrandCustomSettings.brand_id == brand_id)
        stmt = stmt.where(BrandCustomSettings.custom_type == CustomType.SHIPMENT.value)
        stmt = stmt.where(BrandCustomSettings.base_type == base_type)
        stmt = stmt.with_for_update()

        settings: BrandCustomSettings = sess().scalar(stmt)
        if not settings:
            settings = cls.create_sync(
                brand_id=brand_id,
                name="",
                custom_type=CustomType.SHIPMENT.value,
                base_type=base_type,
            )
            sess().add(settings)
        sess().commit()
        return settings

    @property
    def is_base_shipment(self):
        return self.custom_type == CustomType.SHIPMENT.value

    @property
    def is_custom_shipment(self):
        return self.custom_type == CustomType.CUSTOM_SHIPMENT.value

    @property
    def is_custom_payment(self):
        return self.custom_type == CustomType.CUSTOM_PAYMENT.value

    @property
    def is_custom_shipment_group(self):
        return self.custom_type == CustomType.CUSTOM_SHIPMENT_GROUP.value

    @hybrid_property
    def name(self) -> str:
        return self._name

    @name.setter
    def name(self, value: str):
        self._name = value

    async def get_name(
            self, lang: str, base_type: ShipmentBaseTypeLiteral | None = None
    ) -> str:
        if self.is_base_shipment:
            if not base_type:
                base_type = self.base_type
            return await f(f"store {base_type.replace('_', ' ')} text", lang)

        return self._name

    async def get_label_comment(
            self, lang: str,
            custom_type: CustomTypeLiteral | None = None,
    ) -> str:
        if not custom_type:
            custom_type = self.custom_type
        if self.label_comment:
            return self.label_comment
        return await f(f"store {custom_type} label comment default", lang)

    async def get_info(
            self, lang: str,
            custom_type: CustomTypeLiteral | None = None,
    ) -> str:
        if not custom_type:
            custom_type = self.custom_type
        if self.info:
            return self.info
        # return await f(f"store {custom_type} info default", lang)
        return ""

    @property
    def label_default(self) -> bool:
        return self.label_comment is None

    @property
    def info_default(self) -> bool:
        return self.info is None

    @property
    def price(self) -> float:
        if not self._price:
            return 0.0
        return round(self._price / 100, 2)

    @price.setter
    def price(self, value: float):
        self._price = round(value * 100)

    @property
    def raw_price(self):
        return self._price

    @property
    def min_price(self) -> float:
        if self._min_price == 0:
            return float(self._min_price)
        return round(self._min_price / 100, 2)

    @min_price.setter
    def min_price(self, value: float):
        self._min_price = round(value * 100)

    @property
    def max_price(self) -> float:
        if self._max_price == 0:
            return float(self._max_price)
        return round(self._max_price / 100, 2)

    @max_price.setter
    def max_price(self, value: float):
        self._max_price = round(value * 100)

    @db_func
    def add_custom_settings(self, custom_settings: "BrandCustomSettings") -> bool:
        if custom_settings in self.custom_settings:
            self.custom_settings.remove(custom_settings)
        else:
            self.custom_settings.append(custom_settings)
        sess().commit()
        return True

    @property
    def count_custom_settings(self) -> int:
        return len(self.custom_settings)

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()
        return True

    @property
    def map_countries_list(self):
        return self._map_countries_list

    @map_countries_list.setter
    def map_countries_list(self, value: list[str]):
        self._map_countries_list = value

    @hybrid_property
    async def media_url(self) -> str | None:
        if self.media_id:
            media = await models.MediaObject.get(self.media_id)
            if media:
                return media.url

        return None


class StoreCustomSettings(Base, BaseDBModel):
    __tablename__ = "store_custom_settings"

    store_id: int = Column(
        BigInteger, ForeignKey("stores.id", ondelete="CASCADE"), nullable=False
    )

    custom_settings_id: int = Column(
        BigInteger, ForeignKey("brand_custom_settings.id", ondelete="CASCADE"),
        nullable=False
    )
    custom_settings: "BrandCustomSettings" = relationship(
        "BrandCustomSettings", foreign_keys=custom_settings_id
    )

    is_enabled: bool = Column(Boolean, nullable=True, default=None)
    is_shipment_zone: bool = Column(Boolean, default=False)
    _map_countries_list = Column(NestedMutableJson, default=None)

    @classmethod
    @db_func
    def create(cls, store_id: int, custom_settings_id: int) -> "StoreCustomSettings":
        settings = cls(store_id=store_id, custom_settings_id=custom_settings_id)

        sess().add(settings)
        sess().commit()
        return settings

    @classmethod
    async def get_or_create(
            cls,
            store_id: int | None = None,
            custom_settings_id: int | None = None,
    ) -> "StoreCustomSettings":
        custom_settings = await BrandCustomSettings.get(custom_settings_id)

        settings = await cls.get(
            store_id=store_id, custom_settings_id=custom_settings.id
        )

        if not settings and custom_settings:
            settings = await cls.create(store_id, custom_settings.id)
        return settings

    @property
    def is_base_shipment(self):
        return self.custom_settings.custom_type == CustomType.SHIPMENT.value

    @property
    def is_custom_shipment(self):
        return self.custom_settings.custom_type == CustomType.CUSTOM_SHIPMENT.value

    @property
    def custom_type(self):
        return self.custom_settings.custom_type

    @property
    def base_type(self):
        return self.custom_settings.base_type

    @property
    def map_countries_list(self):
        return self._map_countries_list

    @map_countries_list.setter
    def map_countries_list(self, value: list[str]):
        self._map_countries_list = value
