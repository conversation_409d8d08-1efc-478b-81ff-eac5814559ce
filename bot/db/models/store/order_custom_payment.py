from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON>, String, Text
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.orm import relationship

from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel


class OrderCustomPayment(Base, BaseDBModel):
    name: str = Column(
        String(255, collation="utf8mb4_unicode_ci"), nullable=False
    )  # имя способа оплаты на момент заказа
    _price: int = Column(BIGINT, default=0)  # цена способа оплаты на момент заказа
    comment: str = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )  # комментарий к способу оплаты

    # связь с ордером
    store_order_id: int = Column(BigInteger, ForeignKey("store_orders.id", ondelete="CASCADE"))
    store_order: "models.StoreOrder" = relationship("StoreOrder", foreign_keys=store_order_id)

    label_comment: str = Column(Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None)

    info: str = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    @classmethod
    @db_func
    def create(
            cls, order: "models.StoreOrder",
            name: str,
            price: float = 0.0,
            comment: str | None = None,
    ) -> "models.OrderCustomPayment":
        custom_payment = cls(
            store_order=order, name=name,
            price=price, comment=comment,
        )
        sess().add(custom_payment)
        sess().commit()
        return custom_payment

    @property
    def raw_price(self) -> float:
        if self._price == 0:
            return float(self._price)
        return self._price

    @property
    def price(self) -> float:
        if self._price == 0:
            return float(self._price)
        return round(self._price / 100, 2)

    @price.setter
    def price(self, value: float):
        self._price = round(value * 100)
