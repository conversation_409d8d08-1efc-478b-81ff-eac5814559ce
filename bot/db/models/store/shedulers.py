import enum
from datetime import datetime
from typing import Literal

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, DateTime, Enum, ForeignKey, String, Time, func
from sqlalchemy.dialects.mysql import JSON
from sqlalchemy.orm import relationship

from config import DEFAULT_TIME_ZONE
from core.ext.types import ExternalTypeLiteral
from db import db_func, sess
from db.connection import Base
from db.mixins import BaseDBModel


class DayOfWeek(enum.Enum):
    MONDAY = 'Monday'
    TUESDAY = 'Tuesday'
    WEDNESDAY = 'Wednesday'
    THURSDAY = 'Thursday'
    FRIDAY = 'Friday'
    SATURDAY = 'Saturday'
    SUNDAY = 'Sunday'


class StoreScheduler(Base, BaseDBModel):
    __tablename__ = 'store_schedulers'

    brand_id = Column(BigInteger, ForeignKey('brands.id'))

    type_data: ExternalTypeLiteral = Column(String(50), nullable=False, default='poster')
    import_source: ExternalTypeLiteral = Column(String(50), nullable=False, default='poster')
    day_of_week = Column(Enum(DayOfWeek), nullable=False, default='Monday')
    time_value = Column(Time, nullable=False)
    time_zone = Column(String(63), default=DEFAULT_TIME_ZONE)

    create_date = Column(DateTime, default=datetime.utcnow)
    update_date = Column(DateTime, default=None, nullable=True)
    status = Column(String(50), nullable=True)

    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user = relationship("User", foreign_keys=user_id)
    json_data = Column(JSON, nullable=True, default=None,)

    def __repr__(self):
        return '<Schedulers %r>' % self.id

    @classmethod
    @db_func
    def get_list_drawer(
            cls,
            import_source: ExternalTypeLiteral | None = None,
            id: int = None,
            brand_id: int = None,
            day_of_week: DayOfWeek = None,
            position: int = 0, limit: int = None,
            operation: Literal["all", "count"] = "all"
    ) -> list["StoreScheduler"]:
        query = sess().query(cls)
        if id:
            query = query.filter(cls.id == id)
            return query.one_or_none()

        query = query.filter(cls.import_source == import_source)

        if brand_id:
            query = query.filter(cls.brand_id == brand_id)

        if day_of_week:
            query = query.filter(cls.day_of_week == day_of_week)

        if operation == "count":
            return query.with_entities(func.count(cls.id)).scalar()

        slice_args = [position, None]
        if limit:
            slice_args[1] = position + limit
        query = query.slice(*slice_args)

        return query.all()
