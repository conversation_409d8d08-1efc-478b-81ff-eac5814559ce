from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON>Integer, Foreign<PERSON>ey, <PERSON>olean, Float
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

import schemas
from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class IncustPayConfiguration(Base, BaseDBModel, TimeCreatedMixin):
    brand_id: int = Column(BigInteger, ForeignKey("brands.id", ondelete="CASCADE"))
    brand: "models.Brand" = relationship("Brand", backref="incust_pay_configurations")

    store_id: int | None = Column(BigInteger, ForeignKey("stores.id", ondelete="CASCADE"), nullable=True)
    store: "models.Store | None" = relationship("Store", backref="incust_pay_configurations")

    name: str = Column(String(255), nullable=False)
    server_api_url: str | None = Column(String(100), nullable=True)

    rules_type: schemas.RulesTypeLiteral = Column(
        String(20),
        default="by-all-rules",
    )

    terminal_title: str = Column(String(255), nullable=False)
    terminal_api_key: str = Column(String(100), nullable=False)
    terminal_server_api_url: str = Column(String(100), nullable=False)

    card_payment_enabled: bool = Column(Boolean, nullable=False, default=False)

    charge_percent: float = Column(Float, default=0.0)
    charge_fixed: float = Column(Float, default=0.0)

    @hybrid_property
    def is_enabled(self):
        return self.terminal_server_api_url == self.server_api_url

    @hybrid_property
    def is_name_match_terminal_title(self):
        return self.name == self.terminal_title
