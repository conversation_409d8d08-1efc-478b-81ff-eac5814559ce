from sqlalchemy import (
    Big<PERSON><PERSON><PERSON>, <PERSON>olean, <PERSON>umn, Float, Foreign<PERSON>ey, JSON, String,
    UniqueConstraint,
)
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.orm import relationship

from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel


class StoreCharacteristic(Base, BaseDBModel):
    external_id: str = Column(String(255), nullable=True, default=None)
    external_type: str = Column(String(9), nullable=True, default=None)
    is_deleted: bool = Column(Boolean, nullable=False, default=False)

    brand_id: int = Column(BigInteger, ForeignKey("brands.id", ondelete="CASCADE"))
    brand: "models.Brand" = relationship("Brand", backref="store_characteristics")

    name: str = Column(String(255), nullable=False)
    filter_type: str = Column(String(2), nullable=False, default="v")
    is_hide: bool = Column(<PERSON>olean, nullable=False, default=False)

    position: int = Column(SMALLINT(unsigned=True), nullable=True)
    excel_row_number: int | None = Column(
        SMALLINT(unsigned=True), nullable=True
    )  # for soring by Excel order


class StoreCharacteristicValue(Base, BaseDBModel):
    MAX_VALUE_LEN = 100

    characteristic_id: int = Column(
        BigInteger, ForeignKey("store_characteristics.id", ondelete="CASCADE")
    )
    characteristic: "StoreCharacteristic" = relationship(
        "StoreCharacteristic", backref="values"
    )

    product_id: int = Column(
        BigInteger, ForeignKey("store_products.id", ondelete="CASCADE")
    )
    product: "models.StoreProduct" = relationship(
        "StoreProduct", back_populates="characteristics"
    )

    value: str = Column(String(MAX_VALUE_LEN))

    __table_args__ = (
        UniqueConstraint("characteristic_id", "product_id"),
    )


class StoreCharacteristicFiltersSet(Base, BaseDBModel):
    filters: list["StoreCharacteristicFilter"] = relationship(
        "StoreCharacteristicFilter", back_populates="set"
    )

    min_price: float | None = Column(Float, nullable=True)
    max_price: float | None = Column(Float, nullable=True)


class StoreCharacteristicFilter(Base, BaseDBModel):
    set_id: int = Column(
        BigInteger,
        ForeignKey("store_characteristic_filters_sets.id", ondelete="CASCADE")
    )
    set: StoreCharacteristicFiltersSet = relationship(
        StoreCharacteristicFiltersSet, back_populates="filters"
    )

    characteristic_id: int = Column(
        BigInteger, ForeignKey("store_characteristics.id", ondelete="CASCADE")
    )
    characteristic: "StoreCharacteristic" = relationship(
        "StoreCharacteristic", backref="filters"
    )

    filter_type: str = Column(String(2), nullable=False, default="v")

    value: str | None = Column(String(100), nullable=True)
    values_list: list[str] | None = Column(JSON(none_as_null=True), nullable=True)

    range_min: float | None = Column(Float, nullable=True)
    range_max: float | None = Column(Float, nullable=True)


class StoreCharacteristicFilterSetting(Base, BaseDBModel):
    brand_id: int = Column(BigInteger, ForeignKey("brands.id", ondelete="CASCADE"))
    store_id: int = Column(
        BigInteger, ForeignKey("stores.id", ondelete="CASCADE"), nullable=True
    )
    characteristic_id: int = Column(
        BigInteger, ForeignKey("store_characteristics.id", ondelete="CASCADE")
    )

    @classmethod
    @db_func
    def create(
            cls, brand_id: int, store_id: int | None, characteristic_id: int
    ) -> "StoreCharacteristicFilterSetting":
        setting = cls(
            brand_id=brand_id, store_id=store_id, characteristic_id=characteristic_id
        )
        sess().add(setting)
        sess().commit()
        return setting

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()
        return True
