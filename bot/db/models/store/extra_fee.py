from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Boolean, Column, Float, Foreign<PERSON>ey, String
from sqlalchemy.dialects.mysql import SM<PERSON><PERSON>INT

from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class ExtraFeeSettings(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = 'extra_fee_settings'

    group_id: int = Column(BigInteger, ForeignKey('groups.id'))

    name: str = Column(String(255))

    extra_fee_percent: str | None = Column(String(255), nullable=True, default=None)
    extra_fee_value: str | None = Column(String(255), nullable=True, default=None)
    is_active: bool = Column(Boolean, default=True)
    position: int = Column(SMALLINT(unsigned=True), nullable=False, default=0)
    is_deleted: bool = Column(Boolean, nullable=True, default=False)


class ExtraFeeJournal(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = 'extra_fee_journal'

    extra_fee_id: int = Column(BigInteger, ForeignKey('extra_fee_settings.id'))

    invoice_id: int | None = Column(
        BigInteger, ForeignKey('invoices.id'), nullable=True
    )

    order_id: int | None = Column(
        BigInteger, ForeignKey('store_orders.id'), nullable=True
    )

    name: str = Column(String(255))

    extra_fee_percent: str | None = Column(String(255), nullable=True, default=None)
    extra_fee_value: str | None = Column(String(255), nullable=True, default=None)

    applied_amount: int = Column(BigInteger, default=0)
    applied_amount_float: float = Column(Float, default=0)
