from copy import deepcopy
from datetime import datetime

from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, Enum, ForeignKey, String, Text,
    UniqueConstraint, func,
)
from sqlalchemy.dialects.mysql import BIGINT, JSON as JSONType, SMALLINT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

import schemas
from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel
from schemas.payment_settings.schemas_add import ObjectPaymentSettingsTarget
from utils.date_time import utcnow


class StoreOrderPayment(Base, BaseDBModel):
    id = Column(BigInteger, primary_key=True)
    json_data = Column(JSONType, nullable=True)
    payment_method = Column(String(50), nullable=False)

    name = Column(String(255), nullable=True)
    description = Column(String(512), nullable=True)

    incust_account_name = Column(String(255), nullable=True)
    incust_card_name = Column(String(255), nullable=True)
    incust_account_id = Column(String(255), nullable=True)
    incust_card_id = Column(String(255), nullable=True)

    comment = Column(String(512), nullable=True)

    post_payment_info: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )
    label_comment: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    _price: int = Column(BIGINT, default=0)

    order_id: int = Column(
        BigInteger, ForeignKey("store_orders.id", ondelete="CASCADE")
    )
    order: "models.StoreOrder" = relationship(
        "StoreOrder", uselist=False, foreign_keys=order_id
    )

    invoice_id: int | None = Column(
        BigInteger, ForeignKey("invoices.id", ondelete="CASCADE"), nullable=True
    )
    invoice: "models.Invoice" = relationship(
        "Invoice", uselist=False, foreign_keys=invoice_id
    )

    payment_settings_id: int = Column(
        BigInteger,
        ForeignKey("payment_settings.id", ondelete="CASCADE"),
    )
    payment_settings: "models.PaymentSettings" = relationship(
        "PaymentSettings", foreign_keys=payment_settings_id
    )

    create_date = Column(DateTime, default=utcnow)
    update_date = Column(DateTime, default=utcnow, onupdate=utcnow)
    confirmed_datetime: datetime = Column(DateTime(timezone=True))

    business_payment_setting_id: int | None = Column(
        BigInteger, ForeignKey("business_payment_settings.id", ondelete="CASCADE"),
        nullable=True
    )
    business_payment_settings: "models.BusinessPaymentSetting" = relationship(
        "BusinessPaymentSetting", foreign_keys=business_payment_setting_id
    )
    # Дані мерчанта, що використовувались при оплаті
    business_payment_merchant_data = Column(JSONType, nullable=True)

    @hybrid_property
    def price(self) -> float:
        if not self._price:
            return 0.0
        return round(self._price / 100, 2)

    @price.expression
    def price(self):
        return func.ROUND(self._price, 2)

    @price.setter
    def price(self, value: float):
        self._price = round(value * 100)


class ObjectPaymentSettings(Base, BaseDBModel):
    __tablename__ = 'object_payment_settings'

    id = Column(BigInteger, primary_key=True)

    target: ObjectPaymentSettingsTarget = Column(
        Enum(ObjectPaymentSettingsTarget), nullable=False,
        default=ObjectPaymentSettingsTarget.STORE
    )
    store_id: int = Column(
        BigInteger, ForeignKey("stores.id", ondelete="CASCADE"), nullable=False
    )
    invoice_template_id: int = Column(
        BigInteger, ForeignKey("invoice_templates.id", ondelete="CASCADE"),
        nullable=False
    )

    json_data = Column(JSONType, nullable=True)
    is_enabled = Column(Boolean, default=None, nullable=True)
    
    post_payment_info: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    payment_settings_id: int = Column(
        BigInteger, ForeignKey("payment_settings.id", ondelete="CASCADE")
    )
    payment_settings: "models.PaymentSettings" = relationship(
        "PaymentSettings", foreign_keys=payment_settings_id
    )

    is_deleted = Column(Boolean, default=False)

    create_date = Column(DateTime, default=utcnow)
    update_date = Column(DateTime, default=utcnow, onupdate=utcnow)

    __table_args__ = (
        UniqueConstraint(
            'target', 'payment_settings_id', 'store_id', 'invoice_template_id',
        ),
    )


class PaymentSettings(Base, BaseDBModel):
    __tablename__ = 'payment_settings'

    id = Column(BigInteger, primary_key=True)
    brand_id = Column(BigInteger, ForeignKey('brands.id'))
    store_id = Column(BigInteger, ForeignKey('stores.id'))  # TODO: payments, remove
    payment_method = Column(String(50), nullable=False)
    json_data = Column(JSONType, nullable=True, default=None)

    is_enabled = Column(Boolean, default=True)
    name = Column(String(255), nullable=True)
    description = Column(String(512), nullable=True)
    with_comment: schemas.WithCommentStateLiteral = Column(
        String(8), default="disabled"
    )
    label_comment: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )
    is_online = Column(Boolean, default=None, nullable=True)

    post_payment_info: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    media_id: int | None = Column(
        BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL")
    )
    media: "models.MediaObject" = relationship(
        "MediaObject", foreign_keys=media_id, backref="payment_settings"
    )

    is_deleted = Column(Boolean, default=False)
    position: int = Column(
        SMALLINT(unsigned=True), nullable=True, default=None
    )  # TODO: payments, remove nullable

    object_payment_settings: list["models.ObjectPaymentSettings"] = relationship(
        "ObjectPaymentSettings",
        back_populates="payment_settings"
    )

    create_date = Column(DateTime, default=utcnow)
    update_date = Column(DateTime, default=utcnow, onupdate=utcnow)

    def __repr__(self):
        return '<PaymentSettings %r>' % self.id

    @classmethod
    @db_func
    def create(
            cls, payment_method: str, json_data: dict, brand_id: int | None = None,
            store_id: int | None = None
    ) -> "PaymentSettings":
        payment_settings = cls(
            brand_id=brand_id, store_id=store_id, payment_method=payment_method,
            json_data=json_data
        )
        sess().add(payment_settings)
        sess().commit()
        return payment_settings

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k not in dir(self) or k == 'id':
                continue
            setattr(self, k, v)
        sess().commit()
        return True

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()
        return True

    @classmethod
    @db_func
    def get_by_brand_and_pmt_method(
            cls, brand_id: int, payment_method: str
    ) -> "PaymentSettings":
        payment_settings = sess().query(cls) \
            .filter_by(brand_id=brand_id) \
            .filter_by(payment_method=payment_method) \
            .filter_by(store_id=None).one_or_none()
        return payment_settings

    @classmethod
    @db_func
    def get_by_store_and_pmt_method(
            cls, store_id: int, payment_method: str
    ) -> "PaymentSettings":
        payment_settings = sess().query(cls) \
            .filter_by(store_id=store_id) \
            .filter_by(payment_method=payment_method) \
            .first()
        return payment_settings

    @db_func
    def update_json_data(self, json_data: dict) -> bool:
        data = deepcopy(self.json_data)
        data.update(json_data)
        self.json_data = data
        sess().commit()
        return True

    @classmethod
    async def create_or_update(
            cls, payment_method: str, json_data: dict, brand_id: int | None = None,
            store_id: int | None = None
    ) -> bool:
        setting = None
        if store_id:
            setting = await cls.get_by_store_and_pmt_method(store_id, payment_method)
        elif brand_id:
            setting = await cls.get_by_brand_and_pmt_method(brand_id, payment_method)
        if setting:
            return await setting.update_json_data(json_data)

        result = await cls.create(
            payment_method=payment_method, json_data=json_data, brand_id=brand_id,
            store_id=store_id
        )
        return bool(result)

    @classmethod
    async def create_or_replace(
            cls, payment_method: str, json_data: dict, brand_id: int | None = None,
            store_id: int | None = None
    ) -> bool:
        # method to replace json_data in payment_settings
        setting = None
        if store_id:
            setting = await cls.get_by_store_and_pmt_method(store_id, payment_method)
        elif brand_id:
            setting = await cls.get_by_brand_and_pmt_method(brand_id, payment_method)
        if setting:
            await setting.update(json_data=json_data)
            return True

        result = await cls.create(
            payment_method=payment_method, json_data=json_data, brand_id=brand_id,
            store_id=store_id
        )
        return bool(result)


class PaymentSettingsMerchantData(Base, BaseDBModel):
    __tablename__ = 'payment_settings_merchant_data'

    id = Column(BigInteger, primary_key=True)

    business_payment_data_id: int | None = Column(
        BigInteger, ForeignKey("business_payment_data.id", ondelete="CASCADE"),
        nullable=True
    )
    business_payment_data: "models.BusinessPaymentData" = relationship(
        "BusinessPaymentData", foreign_keys=business_payment_data_id
    )

    json_data = Column(JSONType, nullable=True, default=None)

    is_enabled = Column(Boolean, default=True)
    is_deleted = Column(Boolean, default=False)
    create_date = Column(DateTime, default=utcnow)
    update_date = Column(DateTime, default=utcnow, onupdate=utcnow)
