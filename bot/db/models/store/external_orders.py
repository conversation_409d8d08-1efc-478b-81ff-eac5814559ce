from datetime import datetime
from typing import Any

from sqlalchemy import <PERSON>umn, BigInteger, Foreign<PERSON>ey, String, DateTime
from sqlalchemy.dialects.mysql import JSON as JSONType

from db.connection import Base
from db import sess, db_func
from db.mixins import BaseDBModel


class ExternalOrder(Base, BaseDBModel):
    brand_id: int = Column(BigInteger, ForeignKey('brands.id'), nullable=True)
    order_id: int = Column(BigInteger, ForeignKey('store_orders.id'))

    external_order_id: str = Column(String(36), nullable=True, index=True)
    external_type: str = Column(String(50), nullable=False)

    json_data: Any = Column(JSONType, nullable=True)
    status: str = Column(String(50), nullable=True)

    create_date: datetime = Column(DateTime, default=datetime.utcnow)
    update_date: datetime = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return '<ExternalOrder %r>' % self.id

    @classmethod
    @db_func
    def create(
        cls,
        brand_id: int,
        order_id: int,
        external_order_id: str,
        external_type: str,
        json_data: dict | None = None,
        status: str | None = None
    ):
        external_order = cls(
            brand_id=brand_id,
            order_id=order_id,
            external_order_id=external_order_id,
            external_type=external_type,
            json_data=json_data,
            status=status
        )
        sess().add(external_order)
        sess().commit()
        return external_order

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k not in dir(self) or k == 'id':
                continue
            setattr(self, k, v)
        sess().commit()
        return True

    @classmethod
    async def create_or_update(
        cls,
        brand_id: int,
        order_id: int,
        external_order_id: str,
        external_type: str,
        json_data: dict | None = None,
        status: str | None = None
    ):
        external_order = None
        if order_id:
            external_order = await cls.get_by_order_external_type(order_id, external_type)
        elif external_order_id:
            external_order = await cls.get_by_external_order_external_type(brand_id, external_order_id, external_type)
        if external_order:
            return await external_order.update(
                brand_id=brand_id,
                order_id=order_id,
                external_order_id=external_order_id,
                external_type=external_type,
                json_data=json_data,
                status=status,
            )
        await cls.create(
            brand_id=brand_id,
            order_id=order_id,
            external_order_id=external_order_id,
            external_type=external_type,
            json_data=json_data,
            status=status,
        )

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()
        return True

    @classmethod
    @db_func
    def get(cls, id: int) -> "ExternalOrder":
        external_order = sess().query(cls).filter_by(id=id).one()
        return external_order

    @classmethod
    @db_func
    def get_by_store_order_id(cls, order_id: int) -> "ExternalOrder":
        external_order = sess().query(cls).\
            filter_by(order_id=order_id).one_or_none()
        return external_order

    @classmethod
    @db_func
    def get_by_order_external_type(cls, order_id: int, external_type: str) -> "ExternalOrder":
        external_order = sess().query(cls).\
            filter_by(order_id=order_id).\
            filter_by(external_type=external_type).one_or_none()
        return external_order

    @classmethod
    @db_func
    def get_by_external_order_external_type(cls, brand_id: int, external_order_id: int, external_type: str) -> "ExternalOrder":
        external_order = sess().query(cls).\
            filter_by(brand_id=brand_id).\
            filter_by(external_order_id=external_order_id).\
            filter_by(external_type=external_type).first()
        return external_order
