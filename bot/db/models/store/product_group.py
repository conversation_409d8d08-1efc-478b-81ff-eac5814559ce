from __future__ import annotations

from sqlalchemy import Big<PERSON><PERSON>ger, <PERSON><PERSON><PERSON>, <PERSON>umn, Foreign<PERSON>ey, String
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel


class StoreProductGroup(Base, BaseDBModel):
    external_id: str | None = Column(String(255), nullable=True)
    external_type: str = Column(String(9), nullable=True)

    name: str = Column(String(255), nullable=False)

    is_deleted: bool = Column(Boolean, nullable=False, default=False)

    brand_id: int = Column(BigInteger, ForeignKey("brands.id", ondelete="CASCADE"))
    brand: "models.Brand" = relationship("Brand", backref="product_groups")

    products: list["models.StoreProduct"] = relationship("StoreProduct")
    characteristics: list[StoreProductGroupCharacteristic] = relationship(
        "StoreProductGroupCharacteristic"
    )


class StoreProductGroupCharacteristic(Base, BaseDBModel):
    product_group_id: int = Column(
        BigInteger, ForeignKey("store_product_groups.id", ondelete="CASCADE")
    )
    product_group: StoreProductGroup = relationship(
        StoreProductGroup, back_populates="characteristics"
    )

    characteristic_id: int = Column(
        BigInteger, ForeignKey("store_characteristics.id", ondelete="CASCADE")
    )
    characteristic: "models.StoreCharacteristic" = relationship(
        "StoreCharacteristic", backref="product_groups",
    )

    is_modifier: bool = Column(Boolean, default=False, nullable=False)
    show_one_modification: bool = Column(Boolean, default=False, nullable=False)
