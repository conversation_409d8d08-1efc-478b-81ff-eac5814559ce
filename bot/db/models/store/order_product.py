from sqlalchemy import (
    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, Float, <PERSON><PERSON><PERSON>, Integer, String,
    Text,
)
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson


class OrderProduct(Base, BaseDBModel):
    name: str = Column(String(1024))  # имя продукта на момент заказа
    quantity: int = Column(Integer)  # количество продукта на момент заказа

    price: int = Column(BIGINT)  # цена на момент заказа
    price_with_attributes: int = Column(
        BIGINT, nullable=False, default=0
    )  # цена с учетом атрибутов
    final_price: int = Column(BIGINT, nullable=False, default=0)

    discount_amount = Column(BIGINT, nullable=False, default=0)  # сумма скидки(unit)
    price_after_loyalty = Column(
        BIGINT, nullable=False, default=0
    )  # цена с учетом лояльности(unit)
    before_loyalty_sum = Column(
        BIGINT, nullable=False, default=0
    )  # сумма позиции с атрибутами, до лояльности
    total_sum = Column(
        BIGINT, nullable=False, default=0
    )  # финальная сумма позиции, с учетом лояльности
    discount_sum = Column(BIGINT, nullable=False, default=0)  # сумма скидки по позиции

    bonuses_redeemed = Column(
        BIGINT, nullable=False, default=0
    )  # бонусы списанные с лояльности(unit)
    bonuses_redeemed_sum = Column(
        BIGINT, nullable=False, default=0
    )  # бонусы списанные с лояльности по позиции
    discount_and_bonuses = Column(
        BIGINT, nullable=False, default=0
    )  # бонусы + скидка с лояльности(unit)
    discount_and_bonuses_sum = Column(
        BIGINT, nullable=False, default=0
    )  # бонусы + скидка с лояльности по позиции

    # связь с ордером
    store_order_id: int = Column(
        BigInteger, ForeignKey("store_orders.id", ondelete="CASCADE")
    )
    store_order: "models.StoreOrder" = relationship(
        "StoreOrder", foreign_keys=store_order_id
    )

    # связь с продуктом
    product_id: int = Column(
        BigInteger, ForeignKey("store_products.id", ondelete="CASCADE")
    )
    product: "models.StoreProduct" = relationship(
        "StoreProduct", foreign_keys=product_id
    )

    attributes: list["models.OrderAttribute"] = relationship(
        "OrderAttribute", back_populates="order_product",
    )
    display_name: str = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )
    display_description: str = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    incust_pay_term_api_key: str = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )
    incust_pay_term_url: str = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )
    incust_account: dict | None = Column(NestedMutableJson, default=None)
    incust_card: str | None = Column(Text(collation="utf8mb4_unicode_ci"), default=None)
    incust_user_transaction: dict | None = Column(NestedMutableJson, default=None)
    incust_owner_transaction: dict | None = Column(NestedMutableJson, default=None)
    topup_charge: int = Column(BIGINT, nullable=False, default=0)
    charge_percent: float = Column(Float, default=0.0)
    charge_fixed: float = Column(Float, default=0.0)
    is_topup_error: bool = Column(Boolean, nullable=False, default=False)
    
    # loyalty settings для topup продуктів
    loyalty_settings_id: int = Column(BigInteger, ForeignKey("loyalty_settings.id", ondelete="SET NULL"), nullable=True, default=None)

    def __init__(
            self, name: str, store_order: "models.OrderProduct",
            quantity: int, price: int, product: "models.StoreProduct",
            price_with_attributes: int = 0, final_price: int = 0,
            discount_amount: int = 0, price_after_loyalty: int = 0,
            bonuses_redeemed: int = 0, before_loyalty_sum: int = 0,
            total_sum: int = 0,
            discount_sum: int = 0, bonuses_redeemed_sum: int = 0,
            discount_and_bonuses: int = 0, discount_and_bonuses_sum: int = 0,
            display_name: str | None = None, display_description: str | None = None,
            incust_pay_term_api_key: str | None = None,
            incust_pay_term_url: str | None = None,
            incust_card: str | None = None,
            incust_account: dict | None = None,
            topup_charge: int = 0,
            charge_percent: float = 0.0,
            charge_fixed: float = 0.0,
            loyalty_settings_id: int | None = None,
    ):
        self.name = name
        self.store_order = store_order
        self.quantity = quantity
        self.price = price
        self.product = product
        self.price_with_attributes = price_with_attributes
        self.final_price = final_price
        self.discount_amount = discount_amount
        self.price_after_loyalty = price_after_loyalty
        self.bonuses_redeemed = bonuses_redeemed

        self.before_loyalty_sum = before_loyalty_sum
        self.total_sum = total_sum
        self.discount_sum = discount_sum
        self.bonuses_redeemed_sum = bonuses_redeemed_sum

        self.discount_and_bonuses = discount_and_bonuses
        self.discount_and_bonuses_sum = discount_and_bonuses_sum
        self.display_name = display_name
        self.display_description = display_description

        self.incust_pay_term_api_key = incust_pay_term_api_key
        self.incust_pay_term_url = incust_pay_term_url
        self.incust_card = incust_card
        self.incust_account = incust_account
        self.topup_charge = topup_charge
        self.charge_percent = charge_percent
        self.charge_fixed = charge_fixed
        self.loyalty_settings_id = loyalty_settings_id

        super().__init__()

    @property
    def converted_sums(self):
        return dict(
            price=round(self.price / 100, 2) if self.price else 0,
            price_with_attributes=round(self.price_with_attributes / 100, 2),
            discount_amount=round(self.discount_amount / 100, 2),
            before_loyalty_sum=round(self.before_loyalty_sum / 100, 2),
            price_after_loyalty=round(self.price_after_loyalty / 100, 2),
            bonuses_redeemed=round(self.bonuses_redeemed / 100, 2),
            discount_sum=round(self.discount_sum / 100, 2),
            topup_charge=round(self.topup_charge / 100, 2),
            total_sum=round(self.total_sum / 100, 2),
        )

    @hybrid_property
    def final_sum(self):
        return self.total_sum
