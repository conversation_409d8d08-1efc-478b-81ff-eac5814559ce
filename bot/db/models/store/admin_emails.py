from datetime import datetime

from sqlalchemy import BigInteger, Boolean, Column, DateTime, Enum, ForeignKey, String, UniqueConstraint

from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import TypeAdminEmailEnum


class AdminEmail(Base, BaseDBModel, TimeCreatedMixin):
    brand_id: int = Column(BigInteger, ForeignKey('brands.id'), nullable=False)
    store_id: int | None = Column(BigInteger, ForeignKey('stores.id'), default=None, nullable=True)
    type_email: TypeAdminEmailEnum = Column(Enum(TypeAdminEmailEnum), nullable=False)
    email: str = Column(String(320), nullable=False)
    update_date: datetime = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    creator_id: int | None = Column(BigInteger, ForeignKey('users.id'), default=None, nullable=True)
    is_deleted: bool | None = Column(Boolean, nullable=True, default=False)

    __table_args__ = (
        UniqueConstraint("brand_id", "store_id", "email"),
    )

    def __repr__(self):
        return '<AdminEmail %r>' % self.id
