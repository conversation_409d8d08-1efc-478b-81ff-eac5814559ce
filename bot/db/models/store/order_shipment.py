from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>umn, ForeignKey, String, Text, func
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel
from schemas import ShipmentBaseTypeLiteral
from schemas.store.types import DeliveryTimeTypeLiteral


class OrderShipment(Base, BaseDBModel):
    base_type: ShipmentBaseTypeLiteral = Column(String(8))

    name: str = Column(
        String(255, collation="utf8mb4_unicode_ci"), nullable=False
    )  # имя способа доставки на момент заказа
    _price: int = Column(BIGINT, default=0)  # цена способа доставки на момент заказа
    comment: str = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )  # комментарий к способу доставки
    is_paid_separately = Column(Boolean, default=False)

    # связь с ордером
    store_order_id: int = Column(
        BigInteger, ForeignKey("store_orders.id", ondelete="CASCADE")
    )
    store_order: "models.StoreOrder" = relationship(
        "StoreOrder", foreign_keys=store_order_id
    )

    settings_id: int = Column(
        BigInteger, ForeignKey("brand_custom_settings.id", ondelete="RESTRICT")
    )
    settings: "models.BrandCustomSettings" = relationship(
        "BrandCustomSettings", backref="order_shipments"
    )

    label_comment: str = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    delivery_datetime_mode: DeliveryTimeTypeLiteral = Column(
        String(50, collation="utf8mb4_unicode_ci"), nullable=False, default="datetime"
    )

    @property
    def raw_price(self) -> float:
        if self._price == 0:
            return float(self._price)
        return self._price

    @hybrid_property
    def price(self) -> float:
        if self._price == 0:
            return float(self._price)
        return round(self._price / 100, 2)

    @price.expression
    def price(self):
        return func.ROUND(self._price, 2)

    @price.setter
    def price(self, value: float):
        self._price = round(value * 100)
