from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>umn, Foreign<PERSON><PERSON>, String
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import StatusChangeInitiatedBy


class OrderShippingStatus(Base, TimeCreatedMixin, BaseDBModel):
    __tablename__ = "order_shipping_statuses"

    status: str = Column(String(20), nullable=False)
    comment: str | None = Column(String(255), nullable=True, default=None)
    source: str | None = Column(String(39))
    store_order_id: int = Column(
        BigInteger, ForeignKey('store_orders.id'), nullable=False
    )
    store_order: "models.StoreOrder" = relationship(
        "StoreOrder", back_populates="shipping_statuses"
    )

    initiated_by: StatusChangeInitiatedBy = Column(
        String(8), nullable=False, default="unknown"
    )

    initiated_by_user_id: int | None = Column(BigInteger, ForeignKey("users.id"))
    initiated_by_user: "models.User" = relationship("User", backref="shipping_statuses")

    def __init__(
            self,
            store_order_id: int,
            status: str,
            initiated_by: StatusChangeInitiatedBy = "unknown",
            initiated_by_user_id: int | None = None,
            initiated_by_user: "models.User" = None,
            comment: str = None,
            source: str = None,
    ):
        self.store_order_id = store_order_id
        self.status = status
        self.initiated_by = initiated_by
        self.initiated_by_user_id = initiated_by_user_id
        self.initiated_by_user = initiated_by_user
        self.comment = comment
        self.source = source

        super().__init__()
