from sqlalchemy import Column, BigInteger, ForeignKey
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel


class StoreFavoritesProduct(Base, BaseDBModel):
    product_id: int = Column(
        BigInteger,
        ForeignKey("store_products.id", ondelete="CASCADE")
    )
    product: "models.StoreProduct" = relationship(
        "StoreProduct",
        foreign_keys=product_id,
        backref="favorite_product"
    )
    favorite_id: int = Column(
        BigInteger,
        ForeignKey("store_favorites.id", ondelete="CASCADE")
    )
    favorite: "models.StoreCart" = relationship(
        "StoreFavorite",
        foreign_keys=favorite_id,
        back_populates="favorite_products"
    )
