from sqlalchemy import Column, Boolean, String, Time, BigInteger, ForeignKey
from sqlalchemy.orm import relationship

from db.connection import Base
from db.mixins import BaseDBModel


class WorkingDay(Base, BaseDBModel):
    day: str = Column(String(10), nullable=False)

    store_id: int = Column(BigInteger, ForeignKey("stores.id", ondelete="CASCADE"))
    store = relationship('Store', back_populates='working_days')

    slots = relationship('WorkingSlot', back_populates='working_day')
    is_weekend: bool = Column(Boolean, default=False)


class WorkingSlot(Base, BaseDBModel):
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)

    working_day_id = Column(BigInteger, ForeignKey('working_days.id'))
    working_day = relationship('WorkingDay', back_populates='slots')
