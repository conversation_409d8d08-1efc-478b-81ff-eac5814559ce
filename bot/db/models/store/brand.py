from __future__ import annotations

from typing import Type

from psutils.convertors import str_to_float, str_to_int
from sqlalchemy import (
    BigInteger, Boolean, Column, Enum, ForeignKey, JSON, String, Text, and_, func,
)
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

import schemas
from config import (
    DEFAULT_FOOTER_SIGN, DEFAULT_PRIVACY_POLICY_LINK, DEFAULT_TERMS_OF_USE_LINK,
    INCUST_CLIENT_URL,
    INCUST_SERVER_API, WEB_APP_PATH,
)
from db import db_func, models, sess
from db.connection import Base
from db.mixins import BaseDBModel
from schemas import (
    ConsentModeEnum, ExtSysSetTypes, MobileCartButtonMode, OrderFieldSettingEnum,
    OrderNameSettingEnum, ThumbnailsModeEnum, ViewType,
)
from utils.google import get_sheet_url


class Brand(Base, BaseDBModel):
    name = Column(String(255))

    logo_media_id: int | None = Column(
        BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL")
    )
    logo_media: "models.MediaObject | None" = relationship(
        "MediaObject",
        foreign_keys=logo_media_id,
        backref="brand_logos",
    )

    image_media_id: int | None = Column(
        BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL")
    )
    image_media: "models.MediaObject | None" = relationship(
        "MediaObject",
        foreign_keys=image_media_id,
        backref="brand_images"
    )

    is_get_order = Column(Boolean, default=False)
    get_order_username = Column(String(255), nullable=True, default=None)
    get_order_password = Column(String(255), default=None)

    sheets = Column(String(255), nullable=True, default=None)
    is_sheets_update = Column(Boolean, default=True)

    stores: list["models.Store"] = relationship("Store", back_populates="brand")

    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: "models.Group" = relationship("Group", back_populates="brand", uselist=False)

    domain = Column(String(255), nullable=True, default=None)

    description_media_id: int | None = Column(
        BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL")
    )
    description_media: "models.MediaObject | None" = relationship(
        "MediaObject",
        foreign_keys=description_media_id,
        backref="brand_descriptions"
    )

    offer_media_id: int | None = Column(
        BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL")
    )
    offer_media: "models.MediaObject | None" = relationship(
        "MediaObject",
        foreign_keys=offer_media_id,
        backref="brand_offers"
    )

    is_poster_update = Column(Boolean, default=True)

    is_filter_by_price: bool = Column(Boolean, default=True)
    is_filter_sort: bool = Column(Boolean, default=True)
    is_filter_search: bool = Column(Boolean, default=True)

    image_height: int | None = Column(SMALLINT(unsigned=True), nullable=True)
    product_image_aspect_ratio: str | None = Column(String(20), nullable=True)

    desktop_view: ViewType = Column(String(12), default="default_grid")
    mobile_view: ViewType = Column(String(12), default="default_grid")

    thumbnails_mode: ThumbnailsModeEnum = Column(
        Enum(ThumbnailsModeEnum), default=ThumbnailsModeEnum.SEVEN_LOC
    )
    thumbnail_size: int = Column(SMALLINT(unsigned=True), nullable=True, default=512)

    mobile_cart_button_mode: MobileCartButtonMode = Column(
        Enum(MobileCartButtonMode), default=MobileCartButtonMode.BOTTOM
    )

    web_email_mode: OrderFieldSettingEnum = Column(
        Enum(OrderFieldSettingEnum),
        default=OrderFieldSettingEnum.REQUIRED,
    )
    web_phone_mode: OrderFieldSettingEnum = Column(
        Enum(OrderFieldSettingEnum),
        default=OrderFieldSettingEnum.OPTIONAL,
    )

    messanger_email_mode: OrderFieldSettingEnum = Column(
        Enum(OrderFieldSettingEnum),
        default=OrderFieldSettingEnum.OPTIONAL,
    )
    messanger_phone_mode: OrderFieldSettingEnum = Column(
        Enum(OrderFieldSettingEnum),
        default=OrderFieldSettingEnum.OPTIONAL,
    )
    order_comment_mode: OrderFieldSettingEnum = Column(
        Enum(OrderFieldSettingEnum),
        default=OrderFieldSettingEnum.OPTIONAL,
    )

    web_order_name_mode: OrderNameSettingEnum = Column(
        Enum(OrderNameSettingEnum),
        default=OrderNameSettingEnum.FIRST_AND_LAST,
    )

    messanger_order_name_mode: OrderNameSettingEnum = Column(
        Enum(OrderNameSettingEnum),
        default=OrderNameSettingEnum.FIRST_AND_LAST,
    )

    _footer_sign: str | None = Column(String(256), nullable=True)
    _terms_of_use_link: str | None = Column(String(256), nullable=True)
    _privacy_policy_link: str | None = Column(String(256), nullable=True)

    additional_head_tags: str | None = Column(Text, nullable=True)
    additional_body_tags: str | None = Column(Text, nullable=True)

    analytics_data: dict | None = Column(JSON, nullable=True)

    is_categories_count_view: bool = Column(Boolean, default=True)

    google_maps_api_key_backend: str | None = Column(
        String(256), nullable=True, default=None
    )
    google_maps_api_key_frontend: str | None = Column(
        String(256), nullable=True, default=None
    )
    google_maps_7loc_keys_enabled: bool = Column(Boolean, default=True)

    is_ai_enabled: bool = Column(Boolean, default=False)

    show_more_infinite: bool = Column(Boolean, default=False)
    products_limit: int = Column(SMALLINT(unsigned=True), default=20)

    task_id: int | None = Column(
        BigInteger,
        ForeignKey("tasks.id", ondelete="SET NULL"),
    )
    task: "models.Task" = relationship(
        "Task", backref="brand_task",
        foreign_keys=task_id
    )

    consent_mode: ConsentModeEnum = Column(
        Enum(ConsentModeEnum),
        default=ConsentModeEnum.PER_ORDER,
    )

    SUPPORTED_IMAGE_TYPES = ["document", "photo", "text"]

    @hybrid_property
    def footer_sign(self):
        return self._footer_sign or DEFAULT_FOOTER_SIGN

    @footer_sign.setter
    def footer_sign(self, value: str | None):
        self._footer_sign = value

    @footer_sign.expression
    def footer_sign(self: Type[Brand]):
        return func.IF(
            self._footer_sign.is_not(None), self._footer_sign, DEFAULT_FOOTER_SIGN
        )

    @hybrid_property
    def terms_of_use_link(self):
        return self._terms_of_use_link or DEFAULT_TERMS_OF_USE_LINK

    @terms_of_use_link.setter
    def terms_of_use_link(self, value: str | None):
        self._terms_of_use_link = value

    @terms_of_use_link.expression
    def terms_of_use_link(self: Type[Brand]):
        return func.IF(
            self._terms_of_use_link.is_not(None), self._terms_of_use_link,
            DEFAULT_TERMS_OF_USE_LINK
        )

    @hybrid_property
    def privacy_policy_link(self):
        return self._privacy_policy_link or DEFAULT_PRIVACY_POLICY_LINK

    @privacy_policy_link.setter
    def privacy_policy_link(self, value: str | None):
        self._privacy_policy_link = value

    @privacy_policy_link.expression
    def privacy_policy_link(self: Type[Brand]):
        return func.IF(
            self._privacy_policy_link.is_not(None), self._privacy_policy_link,
            DEFAULT_PRIVACY_POLICY_LINK
        )

    def calculate_domain_and_params(
            self,
            menu_in_store_id: int | None = None,
            bot_id: int | None = None,
            short_token: models.ShortToken | str | None = None,
            **kwargs,
    ):
        params = {
            key: value for key, value in kwargs.items()
            if value not in (None, "")
        }

        if short_token:
            if isinstance(short_token, models.ShortToken):
                short_token = short_token.token
            params["st"] = short_token

        if menu_in_store_id:
            params["qrmenu"] = menu_in_store_id

        if bot_id:
            params["bot_id"] = bot_id

        if self.domain:
            domain = self.domain
        else:
            domain = WEB_APP_PATH
            params["brand_id"] = self.id

        if not domain.endswith("/"):
            domain += "/"

        return domain, params

    def get_url(
            self, path: str = "",
            menu_in_store_id: int | None = None,
            bot_id: int | None = None,
            short_token: models.ShortToken | str | None = None,
            **kwargs,
    ):
        domain, params = self.calculate_domain_and_params(
            menu_in_store_id, bot_id, short_token, **kwargs
        )

        url_path = f"{domain}{path}"

        if params:
            url_path = url_path + "?" + "&".join(
                [f"{key}={value}" for key, value in params.items() if value is not None]
            )
        return url_path

    async def get_short_token_url(
            self,
            user_or_id: "models.User | int",
            bot_id: int,
            lang: str | None = None,
            path: str = "",
            menu_in_store_id: int | None = None,
            scopes: str | None = None,
            **kwargs,
    ):
        if isinstance(user_or_id, models.User):
            user_id = user_or_id.id
            scopes = user_or_id.allowed_scopes_str
        else:
            user_id = user_or_id
            if not scopes:
                raise ValueError(
                    "user_or_id must be an instance of models.User or scopes must be "
                    "specified"
                )

        domain, params = self.calculate_domain_and_params(
            menu_in_store_id=menu_in_store_id,
            bot_id=None,  # specified in token
            **kwargs
        )

        short_token = await models.ShortToken.create(
            user_id, scopes, bot_id,
            lang, path, params,
        )

        return f"{domain}st/{short_token.token}"

    def get_field_mode(
            self,
            client: schemas.ClientEnum,
            field: schemas.OrderFieldEnum,
    ) -> schemas.OrderFieldSettingEnum:
        field_name = f"{client.value}_{field.value}_mode"
        return getattr(self, field_name)

    @property
    def product_image_aspect_ratio_converted(self) -> (tuple[
                                                           float | int, float | int] |
                                                       None):
        if not self.product_image_aspect_ratio:
            return None

        try:
            split = self.product_image_aspect_ratio.split(":")
            if split[0].isdecimal():
                w = str_to_int(split[0])
            else:
                w = str_to_float(split[0])

            if split[1].isdecimal():
                h = str_to_int(split[1])
            else:
                h = str_to_float(split[1])

            return w, h
        except:
            return None

    @product_image_aspect_ratio_converted.setter
    def product_image_aspect_ratio_converted(
            self, value: tuple[float | int, float | int] | None
    ):
        if not value:
            self.product_image_aspect_ratio = None
        else:
            self.product_image_aspect_ratio = f"{value[0]}:{value[1]}"

    @classmethod
    @db_func
    def get_brands(cls):
        brands = sess().query(cls).all()
        return brands

    @classmethod
    @db_func
    def get_by_group(cls, group_id: int) -> "Brand":
        brand = sess().query(cls).filter(cls.group_id == group_id).first()
        return brand

    @classmethod
    @db_func
    def get_by_domain(cls, domain: str) -> Brand:
        brand = sess().query(cls).filter(cls.domain == domain).first()
        return brand

    @db_func
    def update(self: Brand | int, data: dict | None = None, **kwargs) -> bool:
        if isinstance(self, Brand):
            obj = self
        else:
            obj = Brand.get_sync(self, for_update=True)

        if data is None:
            data = {}
        data.update(kwargs)

        for k, v in data.items():
            if k not in dir(obj) or k == "id":
                continue

            setattr(obj, k, v)

        sess().commit()
        return True

    @property
    def offer(self) -> dict[str, str]:
        content_type = "document"
        kwargs = dict(content_type=content_type)
        if self.offer_media_id:
            media = models.MediaObject.get_sync(self.offer_media_id)
            if media:
                kwargs.update(**{content_type: media.file_path})
        return kwargs

    @property
    def description(self) -> dict[str, str]:
        content_type = "document"
        kwargs = dict(content_type=content_type)
        if self.offer_media_id:
            media = models.MediaObject.get_sync(self.description_media_id)
            if media:
                kwargs.update(**{content_type: media.file_path})
        return kwargs

    async def get_logo_media(self):
        if media := await models.MediaObject.get(self.logo_media_id):
            return media
        return None

    @property
    async def logo(self):
        """For admin bot interfaces"""
        return {
            "content_type": "photo",
            "photo": await self.logo_url,
        }

    @logo.setter
    def logo(self, value: int | None = None):
        self.logo_media_id = value

    @property
    async def logo_url(self):
        if media := await self.get_logo_media():
            return media.url
        return None

    async def get_image_media(self):
        if media := await models.MediaObject.get(self.image_media_id):
            return media
        return None

    @property
    async def image_url(self):
        if media := await self.get_image_media():
            return media.url
        return None

    @property
    async def image(self):
        """For admin bot interfaces"""
        return {
            "content_type": "photo",
            "photo": await self.image_url,
        }

    @image.setter
    def image(self, value: int | None = None):
        self.image_media_id = value

    def get_is_incust_sync(self):
        required_settings = ["incust_loyalty_id", "incust_term_api",
                             "incust_white_label_id"]
        incust_settings = sess().query(
            models.BrandSettings.type_data,
            models.BrandSettings.value_data
        ).filter(
            and_(
                models.BrandSettings.brand_id == self.id,
                models.BrandSettings.type_data.in_(required_settings)
            )
        ).all()

        incust_settings_dict = {setting.type_data: setting.value_data for setting in
                                incust_settings}
        for required_setting in required_settings:
            if required_setting not in incust_settings_dict or incust_settings_dict[
                required_setting] is None or \
                    incust_settings_dict[required_setting] == "":
                return False
        return True

    @property
    @db_func
    def is_incust(self):
        return self.get_is_incust_sync()

    @property
    @db_func
    def incust_server_api(self):
        result = sess().query(
            models.BrandSettings.value_data,
        ).filter(
            models.BrandSettings.brand_id == self.id,
            models.BrandSettings.type_data == ExtSysSetTypes.incust_server_api.value
        ).scalar()
        if not result:
            result = INCUST_SERVER_API
        return result

    @property
    @db_func
    def incust_client_url(self):
        result = sess().query(
            models.BrandSettings.value_data
        ).filter(
            models.BrandSettings.brand_id == self.id,
            models.BrandSettings.type_data == 'incust_client_url'
        ).scalar()
        if not result:
            result = INCUST_CLIENT_URL
        return result

    @property
    @db_func
    def incust_type_client_auth(self):
        result = sess().query(
            models.BrandSettings.value_data
        ).filter(
            models.BrandSettings.brand_id == self.id,
            models.BrandSettings.type_data ==
            ExtSysSetTypes.incust_type_client_auth.value
        ).scalar()
        if not result:
            result = 'web'
        return result

    @property
    def sheets_url(self) -> str:
        return get_sheet_url(self.sheets) if self.sheets else None
