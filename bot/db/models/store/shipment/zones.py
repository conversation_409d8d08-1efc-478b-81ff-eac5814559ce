from db import sess, db_func
from db.connection import Base
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson

from sqlalchemy import Column, Boolean, String, BigInteger, ForeignKey, Integer


class ShipmentZone(Base, BaseDBModel):
    shipment_id: int = Column(
        BigInteger,
        ForeignKey("store_custom_settings.id", ondelete="CASCADE"),
        nullable=False,
    )
    name: str = Column(String(255), nullable=False)

    is_distance: bool = Column(Boolean, default=False)
    is_polygon: bool = Column(Boolean, default=False)
    is_swap_coordinates: bool = Column(Boolean, default=True)

    polygon: dict | None = Column(NestedMutableJson, default=None, nullable=True)
    distance: int = Column(Integer, default=0)

    @classmethod
    @db_func
    def create(cls, shipment_id: int, name: str) -> "ShipmentZone":
        shipment_zone = ShipmentZone(shipment_id=shipment_id, name=name)
        sess().add(shipment_zone)
        sess().commit()
        return shipment_zone

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()
        return True
