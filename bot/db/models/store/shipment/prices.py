from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>umn, ForeignKey
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.orm import relationship

from db.connection import Base
from db.mixins import BaseDBModel


class ShipmentPrice(Base, BaseDBModel):
    _cost_delivery: int = Column(BIGINT, default=0)
    _minimum_order_amount: int = Column(BIGINT, default=0)
    _maximum_order_amount: int = Column(BIGINT, default=0)

    @property
    def raw_price(self) -> int:
        return self._cost_delivery

    @property
    def price(self) -> float:
        return self.cost_delivery

    @property
    def min_price(self) -> float:
        return self.minimum_order_amount

    @property
    def max_price(self) -> float:
        return self.maximum_order_amount

    @property
    def cost_delivery(self) -> float:
        if not self._cost_delivery:
            return 0.0
        return round(self._cost_delivery / 100, 2)

    @cost_delivery.setter
    def cost_delivery(self, value: float):
        self._cost_delivery = round(value * 100)

    @property
    def minimum_order_amount(self) -> float:
        if not self._minimum_order_amount:
            return 0.0
        return round(self._minimum_order_amount / 100, 2)

    @minimum_order_amount.setter
    def minimum_order_amount(self, value: float):
        self._minimum_order_amount = round(value * 100)

    @property
    def maximum_order_amount(self) -> float:
        if not self._maximum_order_amount:
            return 0.0
        return round(self._maximum_order_amount / 100, 2)

    @maximum_order_amount.setter
    def maximum_order_amount(self, value: float):
        self._maximum_order_amount = round(value * 100)


class ShipmentPriceToSettings(Base, BaseDBModel):
    __tablename__ = "shipment_price_to_settings"

    shipment_price_id: int = Column(
        BigInteger,
        ForeignKey("shipment_prices.id", ondelete="CASCADE"),
        nullable=False,
    )
    shipment_price: ShipmentPrice = relationship(ShipmentPrice, foreign_keys=shipment_price_id)

    settings_id: int = Column(
        BigInteger,
        ForeignKey("brand_custom_settings.id", ondelete="CASCADE"),
        nullable=True, default=None,
    )
    zone_id: int = Column(
        BigInteger,
        ForeignKey("shipment_zones.id", ondelete="CASCADE"),
        nullable=True, default=None,
    )
