from datetime import timedelta

from schemas import NotWorkingHours

from db.connection import Base
from db.mixins import BaseDBModel

from schemas.store.types import DeliveryTimeTypeLiteral

from sqlalchemy import Column, BigInteger, ForeignKey, Float, Enum, String
from sqlalchemy.orm import relationship


class ShipmentTime(Base, BaseDBModel):
    delivery_datetime_mode: DeliveryTimeTypeLiteral = Column(
        String(50, collation="utf8mb4_unicode_ci"), nullable=False, default="datetime"
    )
    not_working_hours: NotWorkingHours = Column(Enum(NotWorkingHours), nullable=False, default=NotWorkingHours.NOTHING)

    _min_time: float | None = Column(Float, nullable=True)
    _max_time: float | None = Column(Float, nullable=True)
    _order_execution: float | None = Column(Float, nullable=True)

    @property
    def min_time(self) -> timedelta:
        if self._min_time:
            return timedelta(seconds=self._min_time)
        return None

    @min_time.setter
    def min_time(self, value: float | timedelta):
        self._min_time = value if isinstance(value, float) or value == None else value.total_seconds()

    @property
    def max_time(self) -> timedelta:
        if self._max_time:
            return timedelta(seconds=self._max_time)
        return None

    @max_time.setter
    def max_time(self, value: float | timedelta):
        self._max_time = value if isinstance(value, float) or value == None else value.total_seconds()

    @property
    def order_execution(self) -> timedelta:
        if self._order_execution:
            return timedelta(seconds=self._order_execution)
        return None

    @order_execution.setter
    def order_execution(self, value: float | timedelta):
        self._order_execution = value if isinstance(value, float) or value == None else value.total_seconds()


class ShipmentTimeToSettings(Base, BaseDBModel):
    __tablename__ = "shipment_time_to_settings"

    shipment_time_id: int = Column(
        BigInteger,
        ForeignKey("shipment_times.id", ondelete="CASCADE"),
        nullable=False,
    )
    shipment_time: ShipmentTime = relationship(ShipmentTime, foreign_keys=shipment_time_id, uselist=False)

    settings_id: int = Column(
        BigInteger,
        ForeignKey("brand_custom_settings.id", ondelete="CASCADE"),
        nullable=True, default=None,
    )
    zone_id: int = Column(
        BigInteger,
        ForeignKey("shipment_zones.id", ondelete="CASCADE"),
        nullable=True, default=None,
    )
