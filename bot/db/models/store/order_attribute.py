from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>n, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel


class OrderAttribute(Base, BaseDBModel):
    name = Column(String(1024))  # имя атрибута на момент заказа
    group_name = Column(String(1024))  # имя группы атрибутов на момент заказа
    price_impact = Column(BIGINT)  # в бд умножена на 100 для учёта копеек
    quantity = Column(Integer)  # количество атрибута на момент заказа

    # связь с продуктом
    order_product_id = Column(
        BigInteger, ForeignKey("order_products.id", ondelete="CASCADE"), nullable=False
    )
    order_product = relationship("OrderProduct", foreign_keys=order_product_id)

    # связь с атрибутом
    attribute_id = Column(
        BigInteger, ForeignKey("store_attributes.id", ondelete="CASCADE")
    )
    attribute = relationship("StoreAttribute", foreign_keys=attribute_id)

    # связь с групой атрибутов
    group_attribute_id = Column(
        BigInteger, ForeignKey("store_attribute_groups.id", ondelete="CASCADE")
    )
    group_attribute = relationship(
        "StoreAttributeGroup", foreign_keys=group_attribute_id
    )

    def __init__(
            self,
            name: str,
            group_name: str,
            quantity: int,
            price_impact: int,
            attribute: "models.StoreAttribute",
            group_attribute: "models.StoreAttributeGroup",
    ):
        self.name = name
        self.group_name = group_name
        self.quantity = quantity
        self.price_impact = price_impact
        self.attribute = attribute
        self.group_attribute = group_attribute
