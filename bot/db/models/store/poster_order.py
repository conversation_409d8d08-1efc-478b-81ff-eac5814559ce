from sqlalchemy import BigInteger, Column, ForeignKey, String, UniqueConstraint

from db import db_func, sess
from db.connection import Base
from ...mixins import BaseDBModel, TimeCreatedMixin


class ExternalOrderRef(Base, TimeCreatedMixin, BaseDBModel):

    brand_id: int = Column(
        BigInteger, ForeignKey("brands.id", ondelete="CASCADE"), nullable=False
    )
    order_id: int = Column(
        BigInteger, nullable=False
    )
    external_id: int = Column(BigInteger, nullable=False)
    external_type: str = Column(String(99))
    __table_args__ = (
        UniqueConstraint("brand_id", "order_id"),
    )

    def __init__(
            self, brand_id: int, order_id: int, external_id: int, external_type: str
    ):
        self.brand_id = brand_id
        self.order_id = order_id
        self.external_id = external_id
        self.external_type = external_type
        super().__init__()

    @classmethod
    @db_func
    def save(
            cls,
            brand_id: int,
            order_id: int,
            external_id: int,
            external_type: str,
    ) -> "ExternalOrderRef":
        external_order_ref = cls(
            brand_id,
            order_id,
            external_id,
            external_type,
        )
        sess().add(external_order_ref)
        sess().commit()
        return external_order_ref
