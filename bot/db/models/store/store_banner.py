from sqlalchemy import Column, BigInteger, <PERSON>teger, <PERSON>olean, Foreign<PERSON>ey, String, func
from sqlalchemy.orm import relationship

from db.connection import Base
from db.mixins import BaseDBModel
from db import models


class StoreBanner(Base, BaseDBModel):
    media_id: int | None = Column(BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL"))
    media: "models.MediaObject" = relationship("MediaObject", backref="store_banners")
    store_id: int = Column(BigInteger, ForeignKey("stores.id", ondelete="CASCADE"))
    store: "models.Store" = relationship("Store", foreign_keys=store_id)
    name: str = Column(String(255), nullable=True, default=None)
    url: str | None = Column(String(1024), nullable=True)
    position: int | None = Column(Integer, nullable=True)
    is_visible: bool = Column(Boolean, default=True)
    task_id: int | None = Column(BigInteger, ForeignKey("tasks.id", ondelete="SET NULL"), nullable=True, default=None)
