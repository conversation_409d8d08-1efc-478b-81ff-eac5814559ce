from datetime import datetime
from typing import List

from sqlalchemy import <PERSON>umn, BigInteger, ForeignKey, String, Boolean, DateTime, Text, UniqueConstraint, not_, and_
from sqlalchemy.orm import relationship, aliased

from db import models, sess, db_func
from db.connection import Base
from db.helpers import get_query_by_operation, order_by_slice_and_result, hybrid_method
from db.mixins import BaseDBModel, TimeCreatedMixin


class CustomField(Base, BaseDBModel, TimeCreatedMixin):
    is_deleted = Column(Boolean, default=False)

    name = Column(String(25))

    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group = relationship("Group", back_populates="custom_fields")

    values = relationship("CustomFieldValue", back_populates="field")

    __table_args__ = (
        UniqueConstraint("name", "group_id"),
    )

    @classmethod
    @db_func
    def create(cls, group: "models.Group", name: str):
        name = name.strip()

        existing_field = sess().query(cls)
        existing_field = existing_field.filter_by(name=name, group_id=group.id)
        existing_field = existing_field.one_or_none()

        if existing_field:
            if existing_field.is_deleted:
                cls.is_deleted = False
                sess().commit()
            return existing_field

        field = CustomField(
            name=name,
            group=group,
        )
        sess().add(field)
        sess().commit()
        return field

    @classmethod
    async def get_or_create(
            cls,
            group: "models.Group",
            field_name: str,
    ):
        field = await cls.get(name=field_name, group_id=group.id)
        if not field:
            field = await cls.create(group, field_name)
        elif field.is_deleted:
            await field.recover()

        return field

    @classmethod
    @db_func
    def get_list_for_group(
            cls, group_id: int,
            position: int = 0,
            limit: int = None,
            operation: str = "all",
    ) -> List["CustomField"]:
        query = get_query_by_operation(cls, operation)

        query = query.filter_by(group_id=group_id)
        query = query.filter(cls.is_deleted.is_(False))

        return order_by_slice_and_result(query, position, limit, cls.time_created.desc(), operation)

    @db_func
    def get_last_value(self, user_id: int, *, instance: bool = False, none_as_empty_str: bool = True) -> str:
        query_object = CustomFieldValue if instance else CustomFieldValue.value
        query = sess().query(query_object)

        query = query.filter_by(field_id=self.id)
        query = query.filter_by(user_id=user_id)

        query = query.order_by(CustomFieldValue.time_created.desc())
        query = query.limit(1)

        result = query.scalar()
        if instance:
            return result

        if result is None and none_as_empty_str:
            return ""

        return result

    @db_func
    def update_name(self, new_name: str):
        new_name = new_name.strip()

        self.name = new_name
        sess().commit()
        return self.name

    @db_func
    def delete(self):
        self.is_deleted = True
        sess().commit()
        return True

    @db_func
    def recover(self):
        self.is_deleted = False
        self.time_created = datetime.utcnow()
        sess().commit()
        return True


class CustomFieldValue(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = "custom_fields_values"

    value = Column(Text(collation="utf8mb4_unicode_ci"))

    field_id = Column(BigInteger, ForeignKey("custom_fields.id", ondelete="CASCADE"))
    field = relationship("CustomField", back_populates="values")

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    user = relationship("User", backref="custom_fields_values", foreign_keys=user_id)

    manager_user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    manager_user = relationship("User", backref="created_custom_fields", foreign_keys=manager_user_id)

    vm_id = Column(BigInteger, ForeignKey("virtual_managers.id", ondelete="CASCADE"))
    vm = relationship("VirtualManager", backref="created_custom_fields")

    @classmethod
    @db_func
    def set(
            cls,
            user: "models.User",
            field: "CustomField",
            value: str,
            manager_user: "models.User" = None,
            vm: "models.VirtualManager" = None,
    ) -> str:
        if not any([manager_user, vm]):
            raise ValueError("One of (manager_user, vm) is required")

        field_value = CustomFieldValue(
            user=user,
            value=value,
            manager_user_id=manager_user.id if manager_user else None,
            vm_id=vm.id if vm else None,
        )
        field.values.append(field_value)
        sess().add(field_value)
        sess().commit()
        return field_value.value

    async def restore(
            self,
            manager_user: "models.User" = None,
            vm: "models.VirtualManager" = None,
    ):
        return await self.set(self.user, self.field, self.value, manager_user, vm)

    @property
    def setter(self):
        return self.vm.name if self.vm else self.manager_user.name

    @hybrid_method
    def search(cls, search_text: str):
        subquery_value = aliased(cls)
        subquery = sess().query(subquery_value)
        subquery = subquery.filter(subquery_value.field_id == cls.field_id)
        subquery = subquery.filter(subquery_value.user_id == cls.user_id)
        subquery = subquery.filter(subquery_value.time_created > cls.time_created)

        return and_(
            not_(subquery.exists()),
            cls.value.contains(search_text)
        )
