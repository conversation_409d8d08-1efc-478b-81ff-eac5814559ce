from typing import Type

from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON><PERSON>, String, ForeignKey
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from db.decorators import db_func
from db.connection import Base
from db.helpers import sess, T


class Tag(Base):

    __tablename__ = "tags"

    id = Column(BigInteger, autoincrement=True, primary_key=True)
    handle = Column(String(20, collation="utf8mb4_unicode_ci"))
    users = relationship(
        "User",
        secondary="tags_users_associations",
        back_populates="tags",
    )
    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group = relationship("Group", back_populates="tags")

    @hybrid_property
    def name(self) -> str:
        return self.handle

    @name.setter
    def name(self, value: str):
        self.handle = value

    @classmethod
    @db_func
    def get(cls: Type[T], id: int = None, name: str = None, group_id: int = None) -> T:
        query = sess().query(Tag)
        if id:
            query = query.filter(Tag.id == id)
        if name:
            query = query.filter(Tag.handle == name)
        if group_id:
            query = query.filter(Tag.group_id == group_id)
        if name or group_id:
            query = query.limit(1)
        tag = query.one()
        return tag

    @classmethod
    async def get_by_name(cls: Type[T], group_id: int, handle: str, create: bool = False) -> T:
        tag = await cls.get(name=handle, group_id=group_id)
        if not tag and create:
            tag = await Tag.create(group_id, handle)
        return tag

    @classmethod
    @db_func
    def create(cls, group_id: int, handle: str) -> "Tag":
        new_tag = cls(group_id=group_id, handle=handle)
        sess().add(new_tag)
        sess().commit()
        return new_tag

    # достать метки по группе и пользователю
    @staticmethod
    @db_func
    def get_selected_tags(group_id: int, user_id: int, *, only_ids: bool = False, only_names: bool = False):

        if only_ids:
            query_object = Tag.id
        elif only_names:
            query_object = Tag.handle
        else:
            query_object = Tag

        query = sess().query(query_object)

        query = query.filter(Tag.group_id == group_id)
        query = query.filter(Tag.users.any(id=user_id))

        if only_ids or only_names:
            tags = sess().scalars(query).all()
        else:
            tags = query.all()

        return tags

    @db_func
    def update_name(self, new_name: str):
        self.handle = new_name
        sess().commit()

    @db_func
    def delete(self):
        self.users.clear()
        sess().commit()
        sess().delete(self)
        sess().commit()
