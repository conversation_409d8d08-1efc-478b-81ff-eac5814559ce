from datetime import datetime

import os
from typing import Dict, List, Literal, Union

from sqlalchemy import Column, func
from sqlalchemy import BigInteger, Boolean, ForeignKey, Text, DateTime
from sqlalchemy.orm import relationship, backref

import config as cfg

from db import sess, db_func, models
from db.connection import Base
from db.mixins import BaseDBModel


class Storage(Base, BaseDBModel):
    __tablename__ = "storage"

    text: str | None = Column(Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None)

    media_id: int | None = Column(
        BigInteger,
        ForeignKey("media_objects.id", ondelete="RESTRICT"),
    )
    media: "models.MediaObject | None" = relationship("MediaObject", backref="storages")

    datetime_upload = Column(DateTime(timezone=False), default=datetime.utcnow)

    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group = relationship("Group", foreign_keys=group_id, backref=backref("storage"))

    is_multi_load = Column(Boolean, default=False)

    SUPPORTED_TYPES = cfg.MEDIA_WITH_CAPTION

    def __init__(
            self, group_id: int,
            text: str = None,
            media: "models.MediaObject | None" = None,
            is_multi_load: bool = False
    ):
        self.group_id = group_id
        self.text = text
        self.media = media
        self.is_multi_load = is_multi_load

        super().__init__()

    @classmethod
    @db_func
    def get(
        cls,
        storage_id: int = None,
        group_id: int = None,
        position: int = 0,
        limit: int = None,
        operation: Literal["all", "count"] = "all"
    ) -> Union["Storage", List["Storage"]]:
        query = sess().query(cls)
        if storage_id:
            query = query.filter(cls.id == storage_id)
            return query.one_or_none()

        if group_id:
            query = query.filter(cls.group_id == group_id)

        if operation == "count":
            return query.with_entities(func.count(cls.id)).scalar()

        slice_args = [position, None]
        query = query.order_by(cls.id.desc())
        if limit:
            slice_args[1] = position + limit
        query = query.slice(*slice_args)

        return query.all()

    @db_func
    def delete(self) -> bool:
        sess().delete(self)
        sess().commit()

        return True

    async def get_media(self):
        return await models.MediaObject.get(self.media_id) if self.media_id else None

    @property
    async def media_url(self):
        media = await self.get_media()
        return media.url if media else None

    @property
    async def kwargs_for_send(self) -> Dict[str, str]:
        media = await self.get_media()
        if media.media_type in ("text", "application"):
            content_type = "document"
        elif media == "image":
            content_type = "photo"
        else:
            content_type = media.media_type

        kwargs = dict(content_type=content_type)

        kwargs[content_type] = media.url
        kwargs.update(text=media.url)

        return kwargs

    @property
    async def name(self) -> str:
        if self.text:
            return self.text

        if media_url := await self.media_url:
            return os.path.basename(media_url)

        return self.id

    @name.setter
    def name(self, value: str):
        self.text = value
