from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>n, Foreign<PERSON><PERSON>, String
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import TimeCreatedMixin
from db.mixins.base_model import BaseDBModel


class GroupVerificationDocument(Base, BaseDBModel, TimeCreatedMixin):
    group_id: int = Column(BigInteger, ForeignKey("groups.id"), nullable=False)
    group: "models.Group" = relationship("Group", backref="verification_documents")

    media_id: int = Column(
        BigInteger,
        ForeignKey(
            "media_objects.id",
            ondelete="RESTRICT",
        ),
        nullable=False
    )
    media: "models.MediaObject" = relationship(
        "MediaObject",
        backref="group_verification_documents",
    )
    comment: str | None = Column(String(512), nullable=True)
