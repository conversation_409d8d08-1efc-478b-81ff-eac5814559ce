from datetime import datetime
from typing import Any, Type, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, BigInteger, ForeignKey, Boolean, DateTime
from sqlalchemy import UniqueConstraint
from sqlalchemy.orm import relationship

from db import models
from db.decorators import db_func
from db.connection import Base
from db.helpers import sess, T
from db.mixins import BaseDBModel


class UserInGroup(Base, BaseDBModel):
    __tablename__ = "users_in_groups"

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user = relationship("User")
    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"), default=None)
    group = relationship("Group")
    is_wrote = Column(Boolean, default=False)
    is_user_blocked = Column(Boolean, default=False)

    last_activity: datetime | None = Column(DateTime, nullable=True, default=datetime.utcnow)

    __table_args__ = (
        UniqueConstraint("user_id", "group_id"),
    )

    def __init__(self, user: "models.User", group: "models.Group"):
        assert user
        assert group

        self.user = user
        self.group = group

    @classmethod
    @db_func
    def save(cls: Type[T], user: "models.User", group: "models.Group") -> T:
        user_group_activity = UserInGroup(user, group)
        sess().add(user_group_activity)
        sess().commit()
        return user_group_activity

    @classmethod
    @db_func
    def get(cls: Type[T], user_chat_id: int, group_id: int) -> T:
        query = sess().query(UserInGroup)
        query = query.join(models.User, UserInGroup.user_id == models.User.id)
        query = query.filter(models.User.chat_id == user_chat_id)
        query = query.filter(UserInGroup.group_id == group_id)
        user_group_activity = query.one_or_none()
        return user_group_activity

    @classmethod
    async def get_or_create(cls: Type[T], user: "models.User", group: "models.Group") -> T:
        user_group_activity = await cls.get(user.chat_id, group.id)
        if user_group_activity is None:
            user_group_activity = await cls.save(user, group)
        return user_group_activity

    @db_func
    def update(self, **kwargs: Any):
        for key, value in kwargs.items():
            if key not in dir(self):
                continue
            setattr(self, key, value)
        sess().commit()

    @db_func
    def block(self):
        self.is_user_blocked = True
        sess().commit()

    @db_func
    def unblock(self):
        self.is_user_blocked = False
        sess().commit()

    @db_func
    def wrote(self):
        self.is_wrote = True
        sess().commit()
