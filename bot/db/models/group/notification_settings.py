from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>umn, Foreign<PERSON>ey, Text
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel
from schemas.group.group import NotificationsTarget


class NotificationSetting(Base, BaseDBModel):

    id = Column(BigInteger, autoincrement=True, primary_key=True)

    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: "models.Group" = relationship(
        "Group", foreign_keys=group_id, backref="notification_settings"
    )

    is_enabled: bool = Column(Boolean, default=True, nullable=False)
    target: NotificationsTarget = Column(Text, default="CRM")

    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user: "models.User" = relationship("User", foreign_keys=user_id)
