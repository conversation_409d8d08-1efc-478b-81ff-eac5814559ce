from sqlalchemy import <PERSON>um<PERSON>, BigInteger, Foreign<PERSON>ey, String, Boolean
from db.connection import Base
from db import sess, db_func
from db.mixins import BaseDBModel


class ColorSchema(Base, BaseDBModel):
    is_active = Column(Boolean, default=True)
    group_id = Column(BigInteger, ForeignKey('groups.id'))

    theme_mode = Column(String(5), nullable=False, default="light")

    bg_color = Column(String(22), nullable=True)
    secondary_bg_color = Column(String(22), nullable=True)

    primary_color = Column(String(22), nullable=True)
    secondary_color = Column(String(22), nullable=True)
    error_color = Column(String(22), nullable=True)
    warning_color = Column(String(22), nullable=True)

    text_color = Column(String(22), nullable=True)
    font = Column(String(255), nullable=True)

    use_telegram_theme: bool = Column(Boolean, nullable=False, default=True)

    def __repr__(self):
        return '<ColorSchema %r>' % self.id

    @classmethod
    @db_func
    def create(
            cls,
            group_id: int,
            bg_color: str = None,
            primary_color: str = None,
            secondary_bg_color: str = None,
            secondary_color: str = None,
            text_color: str = None,
            warning_color: str = None,
            error_color: str = None,
            font: str = None,
            theme_mode: str | None = None,
            use_telegram_theme: bool = False
    ) -> "ColorSchema":
        color_schema = cls(
            group_id=group_id,
            bg_color=bg_color,
            primary_color=primary_color,
            secondary_bg_color=secondary_bg_color,
            secondary_color=secondary_color,
            text_color=text_color,
            warning_color=warning_color,
            error_color=error_color,
            font=font,
            theme_mode=theme_mode,
            use_telegram_theme=use_telegram_theme,
        )
        sess().add(color_schema)
        sess().commit()

        return color_schema
