import re
from datetime import datetime, time, timedelta
from typing import List, Optional
from uuid import uuid4

from psutils.convertors import time_period_to_str
from psutils.text import replace_variables_in_text
from pytz import country_timezones
from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, ForeignKey, String,
    Text, Time, func,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import backref, relationship

import config as cfg
from config import USE_LOCALISATION
from db import models
from db.connection import Base
from db.custom_column_types.pydantic_json import PydanticJSON
from db.decorators import db_func
from db.helpers import hybrid_method, sess
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson
from loggers import JSONLogger
from schemas import GroupConfig, GuestUserData
from utils.date_time import utcnow
from utils.media import delete_file
from utils.text import empty_value, f


class Group(Base, BaseDBModel):
    status: str = Column(String(10), default="enabled")

    is_accepted_agreement: bool = Column(Boolean, nullable=False, default=False)
    accept_agreement_datetime: datetime | None = Column(
        DateTime, nullable=True, default=None
    )

    name: str = Column(String(256, collation="utf8mb4_unicode_ci"), default=None)
    location: str = Column(String(256, collation="utf8mb4_unicode_ci"), default="WWW")

    owner_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    owner: "models.User" = relationship(
        "User", backref=backref("own_groups"), foreign_keys=owner_id
    )
    creator_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    creator: "models.User" = relationship(
        "User", backref=backref("creator_groups"), foreign_keys=creator_id
    )

    admins: list["models.Admin"] = relationship("Admin", back_populates="group")

    tags = relationship("Tag", back_populates="group")
    bot = relationship(
        "ClientBot", uselist=False, back_populates="group",
        foreign_keys="ClientBot.group_id"
    )
    channel = relationship(
        "Channel", uselist=False, cascade="all,delete", back_populates="group"
    )

    # пусто, если создано не из клиентского бота
    created_from_bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="RESTRICT"))
    created_from_bot = relationship(
        "ClientBot",
        uselist=False, backref="groups_created",
        foreign_keys=created_from_bot_id,
    )

    group_tags: list["models.GroupTag"] = relationship(
        "GroupTag", secondary="group_tags_to_groups",
        back_populates="groups",
    )

    _menu = Column(String(128), default=None)
    _menu_gallery = Column(
        NestedMutableJson, default=None
    )  # пути к файлам меню галереи в JSON

    work_time_details = Column(
        NestedMutableJson, default=None
    )  # Детальное время работы по дням недели
    opening_time = Column(Time)
    closing_time = Column(Time)

    review_type = Column(String(20), default=None)

    photo = Column(String(100))
    gallery = Column(NestedMutableJson, default=None)  # пути к файлам галереи в JSON

    coordinates = Column(String(100))
    address = Column(Text(collation="utf8mb4_unicode_ci"))
    links = Column(Text(collation="utf8mb4_unicode_ci"))
    contacts = Column(Text(collation="utf8mb4_unicode_ci"))
    about = Column(Text(collation="utf8mb4_unicode_ci"))

    log_chat_id = Column(BigInteger, nullable=True, unique=True)
    log_chat_name = Column(String(256, collation="utf8mb4_unicode_ci"), nullable=True)

    time_created = Column(DateTime(timezone=True), default=utcnow)

    is_onboard_guide_completed = Column(Boolean, default=False)

    is_from_parsing = Column(Boolean, default=False)
    last_parsed_datetime = Column(DateTime(timezone=True), nullable=True)

    custom_fields = relationship("CustomField", back_populates="group")

    new_order_notification = Column(
        Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION
    )
    details_added_order_notification = Column(
        Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION
    )
    user_shows_reserve_order_notification = Column(
        Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION
    )
    confirmed_order_notification = Column(
        Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION
    )
    payed_order_notification = Column(
        Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION
    )
    canceled_order_notification = Column(
        Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION
    )
    closed_order_notification = Column(
        Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION
    )

    from_user_message_notification = Column(
        Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION
    )
    to_user_message_notification = Column(
        Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION
    )

    api_token = Column(String(36), default=None)

    _lang = Column(
        String(10, collation="utf8mb4_unicode_ci"), default=cfg.DEFAULT_LANG,
        nullable=False
    )
    _langs_list = Column(NestedMutableJson, default=None)

    force_use_lang = Column(String(2))

    is_translate = Column(Boolean, default=False)
    allow_all_google_langs: bool = Column(Boolean, default=True)

    country = Column(String(256, collation="utf8mb4_unicode_ci"))
    country_code: str | None = Column(String(2), nullable=True)
    currency: str = Column(String(3), nullable=False)

    email: str | None = Column(String(256), nullable=True)
    phone: str | None = Column(String(50), nullable=True)
    last_contact_information_asked_datetime: datetime | None = Column(
        DateTime, nullable=True
    )

    timezone = Column(String(63), default=cfg.DEFAULT_TIME_ZONE)

    last_orders_export_sheet_id = Column(String(255), nullable=True, default=None)

    brand: Optional["models.Brand"] = relationship(
        "Brand", back_populates="group", uselist=False
    )

    quantitative_services: list["models.QuantitativeService"] = relationship(
        "QuantitativeService", back_populates="group",
    )

    last_translator_limit_notification: datetime | None = Column(
        DateTime, nullable=True, default=None
    )
    is_friends: bool = Column(Boolean, nullable=False, default=False)
    is_ask_about_birthday = Column(Boolean, default=False)

    config: GroupConfig = Column(PydanticJSON(GroupConfig), default=None, nullable=True)

    image_task_id = Column(
        BigInteger, ForeignKey("tasks.id", ondelete="RESTRICT"), nullable=True
    )
    image_task = relationship(
        "Task",
        foreign_keys=image_task_id,
        backref=backref("image_group", uselist=False)
    )

    logo_task_id = Column(
        BigInteger, ForeignKey("tasks.id", ondelete="RESTRICT"), nullable=True
    )
    logo_task = relationship(
        "Task",
        foreign_keys=logo_task_id,
        backref=backref("logo_group", uselist=False)
    )

    users_data: list["models.UserData"] = relationship(
        "UserData", back_populates="group",
    )

    stripe_customer_id: str | None = Column(String(255), nullable=True)

    TIMEOUT_BETWEEN_ASK_CONTACT_INFORMATION = timedelta(weeks=1)

    def __init__(
            self,
            owner: "models.User",
            name: str,
            from_bot: "models.ClientBot" = None,
            is_accepted_agreement: bool = False,
            country_iso_code: str | None = None,
            timezone: str | None = None,
            lang: str | None = None,
            currency: str | None = None,
    ):
        super().__init__(
            owner=owner,
            creator=owner,
            name=name,
            created_from_bot=from_bot,
            is_accepted_agreement=is_accepted_agreement,
            accept_agreement_datetime=utcnow() if is_accepted_agreement else
            None,
            country_code=country_iso_code,
            timezone=timezone,
            lang=lang or owner.lang,
            currency=currency or cfg.DEFAULT_CURRENCY,
        )

    @classmethod
    @db_func
    def save(
            cls,
            name: str,
            owner: "models.User",
            from_bot: "models.ClientBot" = None,
            is_accepted_agreement: bool = False,
            country_iso_code: str | None = None,
            timezone: str | None = None,
            lang: str | None = None,
            currency: str | None = None,
    ) -> "Group":
        group = cls(
            owner, name,
            from_bot,
            is_accepted_agreement,
            country_iso_code, timezone,
            lang,
            currency,
        )
        sess().add(group)
        sess().commit()
        return group

    @classmethod
    async def create(
            cls,
            name: str,
            owner: "models.User",
            from_bot: "models.ClientBot" = None,
            is_accepted_agreement: bool = False,
            country_iso_code: str | None = None,
            timezone: str | None = None,
            lang: str | None = None,
    ) -> "Group":

        if not timezone:
            timezones = country_timezones.get(
                country_iso_code
            ) if country_iso_code else None

            if timezones and len(timezones) == 1:
                timezone = timezones[0]

        group = await cls.save(
            name, owner, from_bot,
            is_accepted_agreement,
            country_iso_code, timezone, lang,
        )
        await models.AdminBotAnalyticAction.save_group_created(group.id)
        return group

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k not in dir(self) or k == "id":
                continue

            if k == "photo" and self.photo:
                if self.photo != v:
                    delete_file(self.photo)

            setattr(self, k, v)

        sess().commit()
        return True

    @property
    def work_time(self) -> str:
        if self.opening_time:
            return time_period_to_str(
                self.lang, self.opening_time, self.closing_time or "24:00"
            )

    @property
    def when_ask_contact_information(self):
        return (self.last_contact_information_asked_datetime +
                self.TIMEOUT_BETWEEN_ASK_CONTACT_INFORMATION)

    @property
    def is_country_code(self):
        return bool(self.country_code)

    @property
    def is_timezone(self):
        return bool(self.timezone)

    @property
    def is_need_ask_contact_information(self):
        if self.email or self.phone:
            return False

        if not self.last_contact_information_asked_datetime:
            return True

        return self.when_ask_contact_information <= utcnow()

    @db_func
    def save_work_time_details(self, work_time: list) -> bool:
        self.work_time_details = work_time

        wednesday = work_time[2]
        time_from = str(wednesday.get("from")).split(".")
        time_to = str(wednesday.get("to")).split(".")

        try:
            hours = 0 if time_from[0] == "24" else int(time_from[0])
        except:
            hours = None
        minutes = 0 if len(time_from) == 1 else int(time_from[1]) * 6
        if hours:
            self.opening_time = time(hours, minutes)

        try:
            hours = 0 if time_to[0] == "24" else int(time_to[0])
        except:
            hours = None
        minutes = 0 if len(time_to) == 1 else int(time_to[1]) * 6
        if hours:
            self.closing_time = time(hours, minutes)

        sess().commit()
        return True

    def get_chat_link(self, bot_username: str = None):
        if bot_username is None:
            if self.bot:
                bot_username = self.bot.username
            else:
                bot_username = cfg.DEFAULT_PAY4SAY_BOT_USERNAME
        return f"https://t.me/{bot_username}?start=chat_Gr-{self.id}"

    @db_func
    def get_tags(
            self, selected: list = None,
            position: int = 0, limit: int = None,
            operation: str = "all",
    ):
        query_object = func.count(models.Tag.id) if operation == "count" else models.Tag
        query = sess().query(query_object)

        query = query.filter(models.Tag.group_id == self.id)

        if operation == "count":
            return query.scalar() - position

        if selected:
            query = query.order_by(models.Tag.id.not_in(selected))
        query = query.order_by(models.Tag.id.desc())

        if position:
            query = query.offset(position)
        if limit:
            query = query.limit(limit)

        return query.all()

    @property
    def langs_list(self):
        return self.get_langs_list(False, True)

    @langs_list.setter
    def langs_list(self, value: list[str]):
        self._langs_list = value

    def get_langs_list(
            self, with_main_lang: bool = True,
            ignore_force_use_lang: bool = False,
    ) -> List[str]:
        if self.force_use_lang and not ignore_force_use_lang:
            return [self.force_use_lang]

        langs = [
            el for el in
            (self._langs_list if isinstance(self._langs_list, list) else [])
            if el and el != self.lang
        ]

        if with_main_lang:
            langs.insert(0, self.lang)

        return langs

    @property
    def lang(self) -> str:
        if self.force_use_lang:
            return self.force_use_lang

        return self._lang if self._lang else cfg.DEFAULT_LANG

    @lang.setter
    def lang(self, value: str):
        self._lang = value

    async def get_user_activity(self, user_chat_id: int) -> "models.UserInGroup":
        user = await models.User.get(user_chat_id)
        user_group_activity = await models.UserInGroup.get_or_create(user, self.id)
        return user_group_activity

    @classmethod
    @db_func
    def get_by_brand(cls, brand_id: int):
        return sess().query(cls).filter(cls.brand.has(id=brand_id)).one_or_none()

    @classmethod
    @db_func
    def get_by_bot(cls, bot_id: int):
        return sess().query(cls).filter(cls.bot.has(id=bot_id)).one_or_none()

    @classmethod
    @db_func
    def get_by_menu_in_store(cls, menu_in_store_id: int):
        return sess().query(cls).filter(
            cls.menus_in_store.any(id=menu_in_store_id)
        ).one_or_none()

    @classmethod
    @db_func
    def get_by_store_id(cls, store_id: int):
        return (
            sess()
            .query(models.Group)
            .join(models.Brand, models.Brand.group_id == models.Group.id)
            .join(models.Store, models.Store.brand_id == models.Brand.id)
            .filter(models.Store.id == store_id)
            .one_or_none()
        )

    @property
    def is_friendly(self) -> bool:
        return self.bot and self.bot.is_friendly

    @db_func
    def generate_api_token(self):
        self.api_token = str(uuid4())
        sess().commit()
        return self.api_token

    @db_func
    def get_internal_currency(self):
        query = sess().query(models.InvoiceTemplate.currency)
        query = query.join(Group, models.InvoiceTemplate.group_id == self.id)
        query = query.filter(
            models.InvoiceTemplate.currency.in_(["rcoins", "rcash"]),
            Group.id == self.id
        )
        currency = query.limit(1).scalar()
        return currency

    # noinspection PyMethodOverriding
    async def delete(self, user: "models.User") -> bool:
        logger = JSONLogger(
            "delete-group", {
                "group": self
            }, full_db_objects=True
        )

        if not user.is_superadmin and user.id != self.owner_id:
            raise Exception("Group can be deleted only by superadmin or owner")

        bot = await models.ClientBot.get(group_id=self.id)
        if bot:
            logger.debug(
                "Deleting group bot", {
                    "bot": bot
                }
            )
            result = await bot.update(status="disabled")
            if not result:
                return result

            await models.AdminBotAnalyticAction.save_bot_deleted(
                user, bot.username, self.id
            )

        result = await self.update(status="disabled")
        if not result:
            return False

        logger.debug("Group deleted")

        await models.AdminBotAnalyticAction.save_group_deleted(user, self.id)
        return True

    async def enable(self, user_chat_id: int) -> bool:
        if self.bot:
            result = await self.bot.update(status="enabled")
            if not result:
                return result

            await models.AdminBotAnalyticAction.save_bot_enabled(
                user_chat_id, self.bot.username, self.id
            )

        result = await self.update(status="enabled")
        if not result:
            return False

        await models.AdminBotAnalyticAction.save_group_enabled(user_chat_id, self.id)
        return True

    @db_func
    def save_menu(self, menu: str | list) -> bool:
        if isinstance(menu, str):
            self._menu = menu
        elif isinstance(menu, list):
            if self._menu_gallery:
                for image in self._menu_gallery:
                    if image not in menu:
                        delete_file(image)

            self._menu_gallery = menu
        sess().commit()
        return True

    @property
    def menu(self) -> str | list:
        menu = None
        if self._menu:
            menu = self._menu
        elif self._menu_gallery:
            menu = self._menu_gallery
        return menu

    @db_func
    def change_review_type(self):
        cur_type = "off" if self.review_type is None else self.review_type
        review_types = ["stars", "emojis", "numbers", "off"]
        self.review_type = review_types[review_types.index(cur_type) - 1]
        sess().commit()
        return True

    @db_func
    def set_simplified_chat(self, chat_id: int, chat_name: str) -> bool:
        query = sess().query(Group.id)

        query = query.filter(Group.status == "enabled")
        query = query.filter(Group.log_chat_name == chat_name)
        query = query.filter(Group.log_chat_id == chat_id)

        is_exists = sess().query(query.exists()).scalar()

        if is_exists:
            return False

        return True

    @classmethod
    @db_func
    def check_simplified_chat(cls, chat_id: int) -> bool:
        query = sess().query(cls.log_chat_id)
        query = query.filter(cls.status == "enabled")
        query = query.filter(cls.log_chat_id == chat_id)
        return sess().query(query.exists()).scalar()

    @hybrid_method
    def search(self, search_text: str):
        return self.name.contains(search_text.strip())

    @hybrid_property
    def is_enabled(self):
        return self.status == "enabled"

    async def get_notification_text(
            self, notification_type: str, lang: str,
            user: "models.User" = None,
            vmc: "" = None,
            **text_kwargs,
    ):
        field_name = f"{notification_type}_notification"
        return await self.get_service_text(field_name, lang, user, vmc, **text_kwargs)

    async def get_service_text(
            self, field_name: str, lang: str,
            user: "models.User" = None,
            vmc: "" = None, **text_kwargs,
    ):
        if field_name not in dir(self):
            raise ValueError("Unknown field: %s", field_name)

        allowed_keys = {
            "user": ("first_name", "last_name", "full_name", "username", "name"),
            "vm": ("name",),
            "group": ("name",)
        }

        if not user:
            guest_user_text = await f("global guest user", lang)
            user = GuestUserData.fill(guest_user_text)

        objects = {
            "user": user,
            "vm": vmc.virtual_manager if vmc else None,
            "group": self,
        }

        for object_type, obj in objects.items():
            if not obj:
                continue

            for key in allowed_keys.get(object_type, []):
                variable = f"{object_type}_{key}"

                if not text_kwargs.get(variable):
                    text_kwargs[variable] = getattr(obj, key)

        shortcuts = {
            "firstname": "user_first_name",
            "lastname": "user_last_name",
            "fullname": "user_name",
            "username": "user_username",
            "user_full_name": "user_name",
        }

        for shortcut, full_key in shortcuts.items():
            text_kwargs[shortcut] = text_kwargs.get(full_key)

        value = getattr(self, field_name)

        if value == USE_LOCALISATION:
            variable = "crm_messages_list_message_info" if (field_name ==
                                                            "crm_message_info") else (
                field_name)
            return await f(variable, lang, **text_kwargs)

        if user:
            custom_fields_tags = re.findall(r"\{var:[^{}]+}", value, flags=re.I | re.X)
            for custom_field_tag in custom_fields_tags:
                name = custom_field_tag.split(":", maxsplit=1)[-1][:-1].strip()
                custom_field = await models.CustomField.get(name=name, group_id=self.id)

                if not custom_field:
                    field_value = ""
                else:
                    field_value = await custom_field.get_last_value(user.id)
                value = value.replace(
                    custom_field_tag, field_value or await empty_value(lang)
                )

        return replace_variables_in_text(value, text_kwargs)

    def __str__(self):
        return f"<Group: {self.name}>"

    @db_func
    def update_config(self, config: GroupConfig) -> bool:
        self.config = config
        sess().commit()
        return True
