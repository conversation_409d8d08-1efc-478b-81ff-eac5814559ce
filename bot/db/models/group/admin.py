from sqlalchemy import <PERSON><PERSON><PERSON>, Big<PERSON><PERSON>ger, Foreign<PERSON><PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship

from db import models
from db.decorators import db_func
from db.connection import Base
from db.helpers import sess


class Admin(Base):
    __tablename__ = "admins"

    id = Column(BigInteger, primary_key=True, autoincrement=True)

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user = relationship("User", back_populates="admin_roles")

    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group = relationship("Group", back_populates="admins")

    is_superadmin_friendly_bot = Column(Boolean, default=False)

    def __init__(self, user: "models.User", group: "models.Group"):
        assert user
        assert group

        self.user = user
        self.group = group

    @classmethod
    @db_func
    def get(cls, user_id: int, group_id: int) -> "Admin":
        query = sess().query(cls)
        query = query.filter(Admin.user_id == user_id)
        query = query.filter(Admin.group_id == group_id)
        return query.first()

    @db_func
    def update(self, **kwargs):
        for key, value in kwargs.items():
            if key not in dir(self):
                continue
            setattr(self, key, value)
        sess().commit()

    @db_func
    def _delete(self):
        sess().delete(self)
        sess().commit()

    async def delete(self):
        group_id, user_chat_id = self.group_id, self.user.chat_id
        await self._delete()
        await models.AdminBotAnalyticAction.save_admin_deleted(group_id, user_chat_id)

    @db_func
    def change_is_superadmin(self):
        self.is_superadmin_friendly_bot = not self.is_superadmin_friendly_bot
        sess().commit()
