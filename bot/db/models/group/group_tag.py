from sqlalchemy import <PERSON>umn, String, BigI<PERSON>ger, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class GroupTag(Base, BaseDBModel, TimeCreatedMixin):
    tag_name: str = Column(String(20), unique=True)
    groups: list["models.Group"] = relationship(
        "Group", secondary="group_tags_to_groups",
        back_populates="group_tags",
    )


class GroupTagToGroup(Base):
    __tablename__ = "group_tags_to_groups"

    id: int = Column(
        Integer,
        primary_key=True,
        autoincrement=True,
    )
    group_id: int = Column(
        BigInteger,
        ForeignKey("groups.id", ondelete="CASCADE"),
        primary_key=True,
    )
    group_tag_id: int = Column(
        BigInteger,
        ForeignKey("group_tags.id", ondelete="CASCADE"),
        primary_key=True,
    )
