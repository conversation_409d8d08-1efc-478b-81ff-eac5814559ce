from typing import Any
import datetime

from sqlalchemy import <PERSON>umn, BigInteger, Foreign<PERSON>ey, DateTime
from sqlalchemy.orm import relationship, backref

from db.mixins import BaseDBModel, TimeCreatedMixin
from db.connection import Base


class Friend(Base, BaseDBModel, TimeCreatedMixin):

    id = Column(BigInteger, autoincrement=True, primary_key=True)

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), unique=True)
    user = relationship("User", foreign_keys=user_id, backref=backref("friend", uselist=False))

    friend_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), unique=True)
    friend = relationship("User", foreign_keys=friend_id, backref=backref("user_friend", uselist=False))
    datetime_used = Column(DateTime(timezone=True), default=datetime.datetime.utcnow)

    def __init__(self, user_id: int, friend_id: int, *args: Any, **kwargs: Any):

        super().__init__(*args, **kwargs)
        self.user_id = user_id
        self.friend_id = friend_id
