from datetime import datetime, timed<PERSON>ta
from sqlalchemy import (
    BigInteger, <PERSON>olean, Column, DateTime, Enum, Foreign<PERSON>ey, Integer, String,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.custom_column_types.pydantic_json import <PERSON>ydanticJ<PERSON><PERSON>
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson
from schemas import (
    MailingChannelTypeEnum, MailingMediaTypeEnum, MailingMessageStatusEnum,
    MailingSentInfo, MailingStatusEnum,
)


class Mailing(Base, BaseDBModel):
    id: int = Column(Integer, primary_key=True, autoincrement=True)
    status: MailingStatusEnum = Column(Enum(MailingStatusEnum), nullable=False)
    channels: list[str] = Column(NestedMutableJson, nullable=False)
    last_sent_datetime: datetime | None = Column(DateTime, nullable=True)
    description: str | None = Column(String(512), nullable=True)
    name: str = Column(String(255), nullable=False)

    sent_info: MailingSentInfo = Column(
        PydanticJSON(MailingSentInfo), default=None, nullable=True
    )
    message_info: dict = Column(NestedMutableJson, nullable=False)
    filters: dict = Column(NestedMutableJson, nullable=True)
    channels_settings: dict = Column(NestedMutableJson, nullable=True)
    preferred_channel: MailingChannelTypeEnum = Column(
        Enum(MailingChannelTypeEnum), nullable=True
    )

    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: "models.Group" = relationship("Group", foreign_keys=group_id)

    is_test: bool = Column(Boolean, default=False)
    message_source_text: dict = Column(NestedMutableJson, nullable=True)

    media_type: MailingMediaTypeEnum = Column(
        Enum(MailingMediaTypeEnum), nullable=False
    )
    media_id: int = Column(BigInteger, ForeignKey("media_objects.id"), nullable=False)

    email_media_as_attachment: bool = Column(Boolean, default=True)


class MailingMessage(Base, BaseDBModel):
    id: int = Column(Integer, primary_key=True, autoincrement=True)
    status: MailingMessageStatusEnum = Column(
        Enum(MailingMessageStatusEnum), nullable=False
    )
    created_datetime: datetime = Column(DateTime, default=datetime.utcnow)
    start_datetime: datetime = Column(DateTime, nullable=True, default=None)
    end_datetime: datetime = Column(DateTime, nullable=True, default=None)

    mailing_id: int = Column(Integer, ForeignKey("mailings.id", ondelete="CASCADE"))
    mailing: "models.Mailing" = relationship("Mailing", foreign_keys=mailing_id)

    bot_id: int = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot: "models.ClientBot" = relationship("ClientBot", foreign_keys=bot_id)
    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user: "models.User" = relationship("User", foreign_keys=user_id)
    email: str | None = Column(String(255), nullable=True)
    chat_id: int | None = Column(BigInteger, nullable=True)
    phone: str | None = Column(String(255), nullable=True)
    user_name: str | None = Column(String(255), nullable=True)

    message: dict = Column(NestedMutableJson, nullable=False)
    error_details: dict = Column(NestedMutableJson, nullable=True)
    retry_info: dict = Column(NestedMutableJson, nullable=True)

    channel_type: MailingChannelTypeEnum = Column(
        Enum(MailingChannelTypeEnum), nullable=False
    )
    channel_name: str = Column(String(255), nullable=False)

    lang = Column(String(10, collation="utf8mb4_unicode_ci"), nullable=True)

    @hybrid_property
    def duration(self) -> timedelta | None:
        if not self.event_end_datetime or not self.event_start_datetime:
            return None
        return self.event_end_datetime - self.event_start_datetime
