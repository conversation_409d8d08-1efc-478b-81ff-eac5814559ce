from sqlalchemy import <PERSON>umn, BigInteger, ForeignKey
from db.connection import Base


class TagsUsersAssociation(Base):

    __tablename__ = "tags_users_associations"

    id = Column(BigInteger, autoincrement=True, primary_key=True)
    tag_id = Column(BigInteger, ForeignKey("tags.id", ondelete="CASCADE"))
    telegramuser_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))


class AdminsChannelsAssociation(Base):

    __tablename__ = "admins_channels_association"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    telegramuser_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    channel_id = Column(BigInteger, ForeignKey("channels.id", ondelete='CASCADE'))
