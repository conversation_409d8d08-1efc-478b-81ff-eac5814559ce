from datetime import datetime

from sqlalchemy import BigInteger, Column, DateTime, ForeignKey, SMALLINT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import TimeCreatedMixin
from db.mixins.base_model import BaseDBModel
from db.my_columns import NestedMutableJson


class AdUserState(Base, BaseDBModel, TimeCreatedMixin):
    ad_id: int = Column(
        SMALLINT,
        ForeignKey("ads.id", ondelete="CASCADE"),
        nullable=False,
    )

    ad: "models.Ad" = relationship("Ad", backref="user_states")

    user_id: int = Column(
        BigInteger,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
    )
    user: "models.User" = relationship("User", backref="ad_states")

    shown_units: list[int] = Column(NestedMutableJson, nullable=False)

    last_shown_date: datetime = Column(DateTime, nullable=False)
