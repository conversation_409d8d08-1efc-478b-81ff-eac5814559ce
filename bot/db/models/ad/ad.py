from sqlalchemy import Column, SMALLINT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import TimeCreatedMixin
from db.mixins.base_model import BaseDBModel


class Ad(Base, BaseDBModel, TimeCreatedMixin):
    id: int = Column(SMALLINT, autoincrement=True, primary_key=True)

    units: list["models.AdUnit"] = relationship(
        "AdUnit", back_populates="ad"
    )
