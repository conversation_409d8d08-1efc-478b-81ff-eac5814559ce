from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>ger, Column, ForeignKey, SMALLINT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import TimeCreatedMixin
from db.mixins.base_model import BaseDBModel


class AdUnit(Base, BaseDBModel, TimeCreatedMixin):
    id: int = Column(SMALLINT, autoincrement=True, primary_key=True)

    ad_id: int = Column(SMALLINT, ForeignKey("ads.id", ondelete="CASCADE"))
    ad: "models.Ad" = relationship("Ad", back_populates="units")

    horizontal_video_id: int | None = Column(
        BigInteger,
        ForeignKey("media_objects.id", ondelete="RESTRICT"),
        nullable=True,
    )
    horizontal_video: Optional["models.MediaObject"] = relationship(
        "MediaObject", backref="horizontal_ad_units",
        foreign_keys=horizontal_video_id,
    )

    vertical_video_id: int | None = Column(
        BigInteger,
        ForeignKey("media_objects.id", ondelete="RESTRICT"),
        nullable=True,
    )
    vertical_video: Optional["models.MediaObject"] = relationship(
        "MediaObject", backref="vertical_ad_units",
        foreign_keys=vertical_video_id,
    )

    position: int = Column(SMALLINT, nullable=False)
