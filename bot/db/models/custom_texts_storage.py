from __future__ import annotations

from typing import Any, TypedDict, Literal

from sqlalchemy import Column, String

from db import db_func, sess
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from db.my_columns import NestedMutableJson


class AdditionalParam(TypedDict):
    type: Literal["vm"]
    virtual_manager_id: int


class CustomTextsStorage(Base, BaseDBModel, TimeCreatedMixin):
    id: str = Column(String(25), unique=True, primary_key=True)
    data: dict[str, Any] = Column(NestedMutableJson, nullable=False)

    def __init__(
            self, id: str,
            data: dict[str, Any] | None = None,
    ):
        if data is None:
            data = {}
        super().__init__(id=id, data=data)

    @classmethod
    def build_id(
            cls, object_: Base | None = None,
            object_name: str | None = None,
            object_id: int | None = None,
    ):
        if object_:
            object_name = object_.__class__.__name__
            object_id = object_.id
        elif not all((object_name, object_id)):
            raise ValueError("object_ or (object_name, object_id) must be specified")

        return f"{object_name}-{object_id}"

    def extract_id(self):
        return self.id.split("-")

    @classmethod
    def create_sync(
            cls, object_: Base | None = None,
            id: str | None = None,
            data: dict | None = None,
    ) -> CustomTextsStorage:
        if id is None:
            id = cls.build_id(object_)

        custom_texts = cls(id, data)
        sess().add(custom_texts)
        sess().commit()
        return custom_texts

    @classmethod
    @db_func
    def create(
            cls, object_: Base | None = None,
            id: str | None = None,
            data: dict | None = None,
    ) -> CustomTextsStorage:
        return cls.create_sync(object_, data)

    @classmethod
    def get_or_create_sync(
            cls, object_: Base | None = None,
            id: str | None = None,
    ):
        if id is None:
            id = cls.build_id(object_)

        if obj := cls.get_expression(for_update=True, id=id).one_or_none():
            sess().commit()
            return obj
        return cls.create_sync(id=id)

    @classmethod
    @db_func
    def get_or_create(
            cls, object_: Base | None = None,
            id: str | None = None,
    ):
        return cls.get_or_create_sync(object_, id)
