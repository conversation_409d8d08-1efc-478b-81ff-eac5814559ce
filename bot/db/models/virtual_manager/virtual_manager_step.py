from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import VMStepReminderMode


class VirtualManagerStep(Base, BaseDBModel, TimeCreatedMixin):
    is_deleted: bool = Column(Boolean, default=False, nullable=False)

    position: int = Column(SMALLINT(unsigned=True), nullable=False)

    virtual_manager_id: int = Column(
        BigInteger,
        ForeignKey("virtual_managers.id", ondelete="CASCADE"),
        nullable=False,
    )
    virtual_manager: "models.VirtualManager" = relationship(
        "VirtualManager", back_populates="steps"
    )
    interactives: list["models.VirtualManagerInteractive"] = relationship(
        "VirtualManagerInteractive",
        back_populates="step",
    )

    text: str | None = Column(String(4096), nullable=True)
    media_id: int | None = Column(
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>ey("media_objects.id", ondelete="RESTRICT"),
        nullable=True,
    )
    media: "models.MediaObject | None" = relationship("MediaObject", backref="vm_steps")

    reminder_mode: VMStepReminderMode = Column(
        Enum(VMStepReminderMode),
        nullable=False,
        default=VMStepReminderMode.DEFAULT,
    )
    # If none, vale from VirtualManager will be used
    reminder_delay: int | None = Column(SMALLINT(unsigned=True))
    # If none, vale from VirtualManager will be used
    reminds_count: int | None = Column(SMALLINT(unsigned=True))

    task_id: int | None = Column(
        BigInteger,
        ForeignKey("tasks.id", ondelete="SET NULL"),
    )
    task: "models.Task" = relationship(
        "Task", backref="vm_step_task",
        foreign_keys=task_id
    )
