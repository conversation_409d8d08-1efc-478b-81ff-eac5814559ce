from typing import Any

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from db.my_columns import NestedMutableJson
from schemas import VMInteractiveType


class VirtualManagerInteractive(Base, BaseDBModel, TimeCreatedMixin):
    is_deleted = Column(Boolean, default=False, nullable=False)
    type: VMInteractiveType = Column(
        Enum(VMInteractiveType),
        nullable=False,
    )
    subtype: str = Column(String(18), nullable=False)  # subtype. Button/action etc. type

    position: int = Column(SMALLINT(unsigned=True), nullable=False)

    step_id: int = Column(BigInteger, ForeignKey("virtual_manager_steps.id", ondelete="CASCADE"), nullable=False)
    step: "models.VirtualManagerStep" = relationship(
        "VirtualManagerStep",
        back_populates="interactives",
    )

    params: dict[str, Any] | None = Column(NestedMutableJson)
