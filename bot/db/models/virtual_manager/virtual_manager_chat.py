from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Foreign<PERSON>ey
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel


class VirtualManagerChat(Base, BaseDBModel):
    is_deleted: bool = Column(Boolean, default=False)

    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user: "models.User" = relationship("User", backref="chats_with_virtual_managers")

    bot_id: int = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot: "models.ClientBot" = relationship("ClientBot", backref="virtual_manager_chats")

    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: "models.Group" = relationship("Group", backref="virtual_manager_chats")

    virtual_manager_id: int = Column(
        BigInteger, <PERSON><PERSON><PERSON>("virtual_managers.id", ondelete="CASCADE")
    )
    virtual_manager: "models.VirtualManager" = relationship(
        "VirtualManager", backref="chats_with_users"
    )

    started_datetime: datetime = Column(
        DateTime(timezone=True), default=datetime.utcnow
    )

    last_sent_message_datetime: datetime | None = Column(DateTime(timezone=True))
    last_answer_datetime: datetime | None = Column(DateTime(timezone=True))

    when_send_message: datetime | None = Column(DateTime(timezone=True))

    current_step_id: int | None = Column(
        BigInteger,
        ForeignKey(
            "virtual_manager_steps.id",
            ondelete="SET NULL",
        ),
    )
    current_step: "models.VirtualManagerStep | None" = relationship(
        "VirtualManagerStep",
        backref="virtual_managers_chat_with_active_step",
        foreign_keys=current_step_id,
    )

    continue_to_step_id: int | None = Column(
        BigInteger,
        ForeignKey(
            "virtual_manager_steps.id",
            ondelete="SET NULL",
        ),
    )
    continue_to_step: "models.VirtualManagerStep | None" = relationship(
        "VirtualManagerStep",
        backref="virtual_managers_chat_with_continue_to_step",
        foreign_keys=continue_to_step_id,
    )

    is_last_answer_for_remind: bool = Column(Boolean, default=False)
    need_send_unhandled_message: bool = Column(Boolean, default=False)

    is_phone_asked: bool = Column(Boolean, default=False)
    is_location_asked: bool = Column(Boolean, default=False)
    last_msg_id_for_set_menu_in_vm: int | None = Column(BigInteger, default=None)

    ticket_id: int | None = Column(
        BigInteger, ForeignKey("crm_tickets.id", ondelete="RESTRICT")
    )
    ticket: "models.CRMTicket | None" = relationship(
        "CRMTicket", backref="virtual_manager_chats"
    )

    is_usage_recorded: bool = Column(Boolean, default=False)
