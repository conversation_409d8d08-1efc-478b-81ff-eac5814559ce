from sqlalchemy import (
    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>umn, Float, <PERSON><PERSON><PERSON>, Integer, String,
    func,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import backref, relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class VirtualManager(Base, BaseDBModel, TimeCreatedMixin):
    name: str = Column(String(100, collation="utf8mb4_unicode_ci"))
    name_id: str | None = Column(
        String(100, collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    is_deleted: bool = Column(Boolean, default=False)
    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: "models.Group" = relationship("Group", backref="virtual_managers")

    questionnaire_id: int | None = Column(
        BigInteger, ForeignKey("questionnaires.id", ondelete="SET NULL")
    )
    questionnaire: "models.Questionnaire | None" = relationship(
        "Questionnaire",
        backref=backref("virtual_manager", uselist=False),
        foreign_keys=questionnaire_id
    )

    steps: list["models.VirtualManagerStep"] = relationship(
        "VirtualManagerStep",
        back_populates="virtual_manager",
    )

    creator_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    creator: "models.User" = relationship("User", backref="created_virtual_managers")

    allow_click_old_messages: bool = Column(Boolean, default=True)
    start_only_in_owner_profile = Column(Boolean, default=False)

    on_start_delay: float = Column(Float, default=0)
    message_delay: float = Column(Float, default=0)

    is_reminder_enabled: bool = Column(Boolean, default=True)
    reminder_delay: float = Column(Float, default=60.0 * 60.0)
    reminds_count: int = Column(Integer, default=3)

    bot_hello_message_enabled: bool = Column(Boolean, default=True, nullable=False)

    @hybrid_property
    def profile_id(self):
        return self.group_id

    @hybrid_property
    def repeat_message_after(self):
        return round(self.reminder_delay, 2)

    @repeat_message_after.expression
    def repeat_message_after(self):
        return func.round(self.reminder_delay, 2)

    @repeat_message_after.setter
    def repeat_message_after(self, value: float):
        self.reminder_delay = value

    @property
    def lang(self) -> str:
        return self.group.lang
