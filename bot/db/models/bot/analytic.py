from typing import List, Literal, <PERSON>ple

from aiowhatsapp import WhatsappBot
from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, Float, <PERSON><PERSON>ey, Integer, String, Text,
)
from sqlalchemy.orm import relationship

import config as cfg
from db import models
from db.connection import Base
from db.decorators import db_func
from db.helpers import sess
from db.my_columns import NestedMutableJson
from utils.date_time import utcnow
from utils.helpers import get_running_file_name
from utils.redefined_classes import Bot


class UserAnalyticAction(Base):
    __tablename__ = "user_analytic_actions"

    id = Column(BigInteger, autoincrement=True, primary_key=True)
    type = Column(String(50), nullable=False)
    datetime = Column(DateTime(timezone=True), default=utcnow)

    user_id = Column(
        BigInteger, ForeignKey("users.id", ondelete="RESTRICT"), nullable=False
    )
    user = relationship("User", foreign_keys=user_id)

    bot_username = Column(String(256, collation="utf8mb4_unicode_ci"), nullable=False)

    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="RESTRICT"))
    bot = relationship("ClientBot")

    manager_chat_id = Column(BigInteger)
    virtual_manager_chat_id = Column(
        BigInteger, ForeignKey("virtual_manager_chats.id", ondelete="RESTRICT")
    )
    virtual_manager_chat = relationship(
        "VirtualManagerChat", foreign_keys=virtual_manager_chat_id
    )

    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group = relationship("Group")

    recommender_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    recommender = relationship("User", foreign_keys=recommender_id)

    message_text_field = Column(Text(collation="utf8mb4_unicode_ci"))
    message_content_type = Column(String(30))
    message_content = Column(NestedMutableJson)

    receiver_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    receiver = relationship("User", foreign_keys=receiver_id)

    virtual_manager_id = Column(
        BigInteger, ForeignKey("virtual_managers.id", ondelete="RESTRICT")
    )
    virtual_manager = relationship("VirtualManager", backref="analytic_actions")

    invoice_id = Column(BigInteger, ForeignKey("invoices.id", ondelete="RESTRICT"))
    invoice = relationship("Invoice", backref="analytic_actions")

    reminder_interval = Column(Float)
    pagination_count = Column(Integer)

    game_type = Column(String(30, collation="utf8mb4_unicode_ci"))
    game_score = Column(Integer)

    @classmethod
    @db_func
    def _save(cls, **kwargs_for_save):
        analytic_action = cls(**kwargs_for_save)
        sess().add(analytic_action)
        sess().commit()
        return analytic_action

    @classmethod
    async def save(cls, **kwargs_for_save) -> "UserAnalyticAction":
        user = kwargs_for_save.get("user")
        if not user:
            raise ValueError("user cant be None")

        bot: models.ClientBot | None = kwargs_for_save.get("bot")
        bot_username = kwargs_for_save.get("bot_username")
        if not bot and not bot_username:
            raise ValueError("One of (bot, bot_username) must be specified")

        if not bot:
            if bot_username not in (
                    None, cfg.SERVICE_BOT_USERNAME,
                    cfg.ROOT_BOT_USERNAME
            ):
                bot = await models.ClientBot.get(username=bot_username)
                kwargs_for_save.update(bot=bot)
        elif not bot_username:
            kwargs_for_save["bot_username"] = bot.display_name

        analytic_action = await cls._save(**kwargs_for_save)
        return analytic_action

    @classmethod
    async def save_user_joined(cls, user: "models.User"):
        file_name = get_running_file_name()
        if file_name == cfg.SERVICE_BOT:
            bot_username = cfg.SERVICE_BOT_USERNAME
        elif file_name == cfg.ROOT_BOT:
            bot_username = cfg.ROOT_BOT_USERNAME
        elif file_name == cfg.WHATSAPP_BOT:
            whatsapp_bot = WhatsappBot.get_current()
            token_info = await whatsapp_bot.api.debug_token()
            bot_username = token_info.data.application
        else:
            telegram_bot = Bot.get_current()
            if not telegram_bot:
                return
            telegram_bot = await telegram_bot.me
            if telegram_bot.username == cfg.ROOT_BOT_USERNAME:
                return
            bot_username = telegram_bot.username
        kwargs = dict(type="user_joined", user=user, bot_username=bot_username)
        return await cls.save(**kwargs)

    @classmethod
    async def save_user_joined_to_bot(
            cls, user: "models.User", bot: "models.ClientBot"
    ):
        kwargs = dict(type="user_joined_to_bot", user=user, bot=bot)
        return await cls.save(**kwargs)

    @classmethod
    async def save_button_click(
            cls, user: "models.User", bot: "models.ClientBot | None", button_name: str,
            **kwargs
    ):
        kwargs.update(type=f"click_on_{button_name}_button", user=user, bot=bot)
        return await cls.save(**kwargs)

    @classmethod
    async def save_link_following(
            cls, user_chat_id: int, bot: "models.ClientBot", link_name: str, **kwargs
    ):
        user = await models.User.get(user_chat_id)
        kwargs.update(type=f"follow_{link_name}_link", user=user, bot=bot)
        return await cls.save(**kwargs)

    @classmethod
    async def save_played_game(
            cls,
            user_chat_id: int, bot: "models.ClientBot",
            game_type: str, game_score: int,
            **kwargs
    ):
        user = await models.User.get(user_chat_id)
        kwargs.update(
            user=user, bot=bot,
            game_type=game_type, game_score=game_score,
            type="played_mini_game"
        )
        return await cls.save(**kwargs)

    @classmethod
    async def save_user_blocked_service_bot(cls, user_chat_id: int):
        user = await models.User.get(user_chat_id)
        bot_username = cfg.SERVICE_BOT_USERNAME
        return await cls.save(
            type="user_blocked_service_bot", user=user, bot_username=bot_username
        )


class AdminBotAnalyticAction(Base):
    __tablename__ = "admin_bot_analytic_actions"

    id = Column(BigInteger, autoincrement=True, primary_key=True)
    type = Column(String(50), nullable=False)
    datetime = Column(DateTime(timezone=True), default=utcnow)
    user_id = Column(
        BigInteger, ForeignKey("users.id", ondelete="RESTRICT"), nullable=False
    )
    user = relationship("User", foreign_keys=user_id)
    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="RESTRICT"))
    group = relationship("Group")
    bot_username = Column(String(256, collation="utf8mb4_unicode_ci"))
    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="RESTRICT"))
    bot = relationship("ClientBot")

    @classmethod
    @db_func
    def _save(cls, **kwargs_for_save):
        analytic_action = AdminBotAnalyticAction(**kwargs_for_save)
        sess().add(analytic_action)
        sess().commit()
        return analytic_action

    @classmethod
    async def save(cls, **kwargs_for_save) -> "AdminBotAnalyticAction":
        bot = await models.ClientBot.get(username=kwargs_for_save.get("bot_username"))
        kwargs_for_save.update(bot=bot)
        analytic_action = await cls._save(**kwargs_for_save)
        return analytic_action

    @classmethod
    async def save_group_created(cls, group_id: int):
        group = await models.Group.get(group_id)
        kwargs_for_save = dict(group=group, user=group.owner, type="group_created")
        return await cls.save(**kwargs_for_save)

    @classmethod
    async def save_bot_created(cls, user_chat_id: int, bot_id: int, group_id: int):
        bot = await models.ClientBot.get(bot_id)
        group = await models.Group.get(group_id)
        user = await models.User.get(user_chat_id)
        kwargs_for_save = dict(
            bot_username=bot.display_name, user=user, type="bot_created", group=group
        )
        return await cls.save(**kwargs_for_save)

    @classmethod
    async def save_bot_recreated(cls, user_chat_id: int, bot_id: int, group_id: int):
        bot = await models.ClientBot.get(bot_id)
        group = await models.Group.get(group_id)
        user = await models.User.get(user_chat_id)
        kwargs_for_save = dict(
            bot_username=bot.display_name, user=user, type="bot_recreated", group=group
        )
        return await cls.save(**kwargs_for_save)

    @classmethod
    async def save_manager_became(cls, group_id: int, manager_chat_id: int):
        group = await models.Group.get(group_id)
        manager_user = await models.User.get(manager_chat_id)
        kwargs_for_save = dict(group=group, user=manager_user, type="manager_became")
        return await cls.save(**kwargs_for_save)

    @classmethod
    async def save_admin_became(cls, group_id: int, admin_chat_id: int):
        group = await models.Group.get(group_id)
        admin_user = await models.User.get(admin_chat_id)
        kwargs_for_save = dict(group=group, user=admin_user, type="admin_became")
        return await cls.save(**kwargs_for_save)

    @classmethod
    async def save_bot_enabled(
            cls, user_chat_id: int, bot_username: str, group_id: int
    ):
        group = await models.Group.get(group_id)
        user = await models.User.get(user_chat_id)
        kwargs_for_save = dict(
            bot_username=bot_username, user=user, type="bot_enabled", group=group
        )
        return await cls.save(**kwargs_for_save)

    @classmethod
    async def save_group_enabled(cls, user_chat_id: int, group_id: int):
        user = await models.User.get(user_chat_id)
        group = await models.Group.get(group_id)
        kwargs_for_save = dict(user=user, group=group, type="group_enabled")
        return await cls.save(**kwargs_for_save)

    @classmethod
    async def save_group_deleted(cls, user: "models.User", group_id: int):
        group = await models.Group.get(group_id)
        kwargs_for_save = dict(user=user, group=group, type="group_deleted")
        return await cls.save(**kwargs_for_save)

    @classmethod
    async def save_bot_deleted(
            cls, user: "models.User", bot_username: str, group_id: int
    ):
        group = await models.Group.get(group_id)
        kwargs_for_save = dict(
            bot_username=bot_username, user=user, type="bot_deleted", group=group
        )
        return await cls.save(**kwargs_for_save)

    @classmethod
    async def save_manager_deleted(cls, group_id: int, manager_chat_id: int):
        group = await models.Group.get(group_id)
        manager_user = await models.User.get(manager_chat_id)
        kwargs_for_save = dict(group=group, user=manager_user, type="manager_deleted")
        return await cls.save(**kwargs_for_save)

    @classmethod
    async def save_admin_deleted(cls, group_id: int, admin_chat_id: int):
        group = await models.Group.get(group_id)
        admin_user = await models.User.get(admin_chat_id)
        kwargs_for_save = dict(group=group, user=admin_user, type="admin_deleted")
        return await cls.save(**kwargs_for_save)


class FriendlyBotAnalyticAction(Base):
    __tablename__ = "friendly_bot_analytic_actions"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    type = Column(String(50), nullable=False)
    datetime = Column(DateTime(timezone=True), default=utcnow)
    member_id = Column(
        BigInteger, ForeignKey("chats_members.id", ondelete="RESTRICT"), nullable=False
    )
    member = relationship("ChatMember", foreign_keys=member_id)
    channel_id = Column(BigInteger, ForeignKey("channels.id", ondelete="RESTRICT"))
    channel = relationship("Channel", foreign_keys=channel_id)
    recommender_id = Column(
        BigInteger, ForeignKey("chats_members.id", ondelete="RESTRICT"), default=None
    )
    recommender = relationship("ChatMember", foreign_keys=recommender_id)
    message_content_type = Column(String(30))
    filter_trigger = Column(Text)
    filter_triggered = Column(Text)
    filter_type = Column(Text)
    reason = Column(String(20))
    is_deleted = Column(Boolean, default=False)
    is_broke_length_rule = Column(Boolean, default=False)
    max_length = Column(Integer)

    schedule_name = Column(String(20), default=None, nullable=True)
    schedule_type = Column(String(20), nullable=True)
    count_days = Column(Integer, nullable=True)
    frequency_message = Column(Integer, nullable=True)

    post_content_type = Column(String(15), nullable=True)
    post_text = Column(
        Text(collation="utf8mb4_unicode_ci"), default=None, nullable=True
    )
    post_media_path = Column(String(512), default=None, nullable=True)

    footer_content_type = Column(String(15), nullable=True)
    footer_text = Column(
        Text(collation="utf8mb4_unicode_ci"), default=None, nullable=True
    )
    footer_media_path = Column(String(512), default=None, nullable=True)
    footer_buttons = Column(
        Text(collation="utf8mb4_unicode_ci"), default=None, nullable=True
    )
    footer_need_concatenation = Column(Boolean, default=False)

    required_resource_type = Column(String(20), nullable=True, default=None)
    required_resource_id = Column(BigInteger, nullable=True, default=None)
    user_send_messages = Column(Integer, nullable=True, default=None)

    @classmethod
    @db_func
    def _save(cls, **kwargs_for_save):
        analytic_action = cls(**kwargs_for_save)
        sess().add(analytic_action)
        sess().commit()
        return analytic_action

    @classmethod
    async def save(cls, **kwargs_for_save) -> "FriendlyBotAnalyticAction":
        chat_member = await models.ChatMember.get(
            chat_member_id=kwargs_for_save.get("member_id")
        )
        if chat_member:
            kwargs_for_save.update(channel_id=chat_member.channel_id)
        analytic_action = await cls._save(**kwargs_for_save)
        return analytic_action

    @classmethod
    async def save_user_joined(cls, member_id: int, recommender_id: int = None):
        kwargs = dict(
            type="user_joined", member_id=member_id, recommender_id=recommender_id
        )
        return await cls.save(**kwargs)

    @classmethod
    async def save_user_left(cls, member_id: int):
        kwargs = dict(type="user_left", member_id=member_id)
        return await cls.save(**kwargs)

    @classmethod
    async def save_message_sent(
            cls,
            member_id: int,
            message_type: str,
            unlimited: bool = False,
            is_deleted: bool = False,
            reason: str = None,
            max_length: int = None,
    ):
        unlimited_tag = models.Channel.INFINITE_MESSAGES if unlimited else ""
        action_type = "message_sent" + unlimited_tag
        kwargs = dict(
            type=action_type,
            member_id=member_id,
            message_content_type=message_type,
            is_deleted=is_deleted,
            reason=reason,
            max_length=max_length,
        )
        return await cls.save(**kwargs)

    @classmethod
    async def save_schedule_post_sent(
            cls, member_id: int, channel_id: int,
            schedule_name: str, schedule_type: str,
            count_days: int, frequency_message: int, post_content_type: str,
            post_text: str = None, post_media_path: str = None,
            footer_content_type: str = None, footer_text: str = None,
            footer_media_path: str = None, footer_buttons: str = None,
            footer_need_concatenation: bool = None
    ):
        kwargs = dict(
            type="schedule_post",
            member_id=member_id, channel_id=channel_id,
            schedule_name=schedule_name, schedule_type=schedule_type,
            count_days=count_days, frequency_message=frequency_message,
            post_content_type=post_content_type,
            post_text=post_text, post_media_path=post_media_path,
            footer_content_type=footer_content_type, footer_text=footer_text,
            footer_media_path=footer_media_path, footer_buttons=footer_buttons,
            footer_need_concatenation=footer_need_concatenation,
        )
        return await cls.save(**kwargs)

    @classmethod
    async def save_subscribe_required_resource(
            cls,
            member_id: int,
            channel_id: int,
            required_resource_id: int,
            required_resource_type: str,
            user_send_messages: int,
    ):
        kwargs = dict(
            type="required_resource",
            reason="not_subscribed",
            is_deleted=True,
            member_id=member_id,
            channel_id=channel_id,
            required_resource_id=required_resource_id,
            required_resource_type=required_resource_type,
            user_send_messages=user_send_messages,
        )
        return await cls.save(**kwargs)

    @classmethod
    async def save_contacted_from_chat(cls, member_id: int):
        kwargs = dict(type="contacted_from_chat", member_id=member_id)
        return await cls.save(**kwargs)

    @classmethod
    async def save_contacted_from_bot(cls, member_id: int):
        kwargs = dict(type="contacted_from_bot", member_id=member_id)
        return await cls.save(**kwargs)

    @classmethod
    async def save_followed_instructions_link(cls, member_id: int):
        kwargs = dict(type="followed_instructions_link", member_id=member_id)
        return await cls.save(**kwargs)

    @classmethod
    async def save_message_deleted_by_bot(
            cls, member_id: int, is_broke_length_rule: bool, max_length: int
    ):
        kwargs = dict(
            type="message_deleted_by_bot",
            member_id=member_id,
            is_broke_length_rule=is_broke_length_rule,
            max_length=max_length,
        )
        return await cls.save(**kwargs)

    @classmethod
    async def save_message_deleted_by_restriction(
            cls,
            member_id: int,
            message_type: str,
    ):
        kwargs = dict(
            type="message_deleted_by_restriction", member_id=member_id,
            message_content_type=message_type
        )
        return await cls.save(**kwargs)

    @classmethod
    async def save_message_deleted_by_rules(cls, member_id: int):
        kwargs = dict(type="message_deleted_by_rules", member_id=member_id)
        return await cls.save(**kwargs)

    @classmethod
    async def save_filter_triggered(
            cls,
            filter_trigger: str,
            filter_triggered: str,
            filter_type: str,
            member_id: int = None,
    ):
        kwargs = dict(
            type="message_deleted_by_message_filter",
            member_id=member_id,
            filter_trigger=filter_trigger,
            filter_triggered=filter_triggered,
            filter_type=filter_type,
        )
        return await cls.save(**kwargs)

    @classmethod
    def save_user_returned(cls, member_id: int, recommender_id: int = None):
        kwargs = dict(
            type="user_returned", member_id=member_id, recommender_id=recommender_id
        )
        return cls.save(**kwargs)

    @classmethod
    async def save_user_hit_threshold(cls, member_id: int):
        kwargs = dict(type="user_hit_threshold", member_id=member_id)
        return await cls.save(**kwargs)

    @classmethod
    async def save_user_limited(cls, member_id: int, message_type: str = None):
        kwargs = dict(
            type="user_limited", member_id=member_id, message_content_type=message_type
        )
        return await cls.save(**kwargs)

    @classmethod
    async def save_filter_added(
            cls, channel_id: int, filter_type: str, filter_triggered: str
    ):
        kwargs = dict(
            type="filter_added",
            channel_id=channel_id,
            filter_type=filter_type,
            filter_triggered=filter_triggered,
        )
        return await cls.save(**kwargs)

    @staticmethod
    async def save_user_banned(
            member_id: int, channel_id: int
    ) -> "models.FriendlyBotAnalyticAction":
        kwargs = dict(type="user_banned", member_id=member_id, channel_id=channel_id)
        return await models.FriendlyBotAnalyticAction.save(**kwargs)

    @classmethod
    @db_func
    def get_member_last_setlimit(cls, member_id: int):
        query = sess().query(cls)
        query = query.filter(cls.type == "user_limited")
        query = query.filter(cls.member_id == member_id)

        result = query.order_by(cls.datetime.desc()).first()
        return result

    @classmethod
    @db_func
    def get_member_last_setlimit_reason(cls, member_id: int):
        setlimit_reasons = ["message_deleted_by_message_filter",
                            "message_deleted_by_restriction"]
        query = sess().query(cls)
        query = query.filter(cls.type.in_(setlimit_reasons))
        query = query.filter(cls.member_id == member_id)

        result = query.order_by(cls.datetime.desc()).first()
        if result:
            return result.type, result.filter_triggered, result.filter_trigger

        return None, None

    @classmethod
    @db_func
    def get_member_setlimit_reasons(
            cls, member_id: int,
            reason: Literal["restriction", "message_filter"],
    ) -> List[Tuple[str, str]] | None:
        setlimit_reason = f"message_deleted_by_{reason}"

        query = sess().query(cls)
        query = query.filter(cls.type == setlimit_reason)
        query = query.filter(cls.member_id == member_id)

        result = query.order_by(cls.datetime.desc()).all()
        if result:
            return [(row.filter_triggered, row.filter_trigger,) for row in result]

        return None
