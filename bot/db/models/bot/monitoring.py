from typing import List

import config as cfg

from datetime import datetime
from sqlalchemy import Column, BigInteger, DateTime, func

from db import db_func
from db.connection import Base
from db.helpers import sess
from db.my_columns import NestedMutableJson


# Monitoring models
class ClientBotStatuses(Base):

    __tablename__ = "bots_statuses"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    chat_id = Column(BigInteger, unique=True)
    status = Column(NestedMutableJson, default=dict)
    checked_at = Column(DateTime(timezone=True), default=datetime.utcnow)

    @staticmethod
    @db_func
    def get_and_update(chat_id: int):
        bot_status_record = sess().query(ClientBotStatuses)\
            .filter(ClientBotStatuses.chat_id == chat_id).one_or_none()
        if bot_status_record is None:
            bot_status_record = ClientBotStatuses(chat_id=chat_id, status={"status": "alive"})
            sess().add(bot_status_record)
        bot_status_record.checked_at = datetime.utcnow()
        sess().commit()
        return bot_status_record

    @staticmethod
    @db_func
    def set_status(chat_ids: int | List[int], status: str):
        if isinstance(chat_ids, int):
            chat_ids = [chat_ids]
        query = sess().query(ClientBotStatuses).filter(ClientBotStatuses.chat_id.in_(chat_ids))
        query.update({ClientBotStatuses.status: {"status": status}})
        sess().commit()

    @classmethod
    @db_func
    def _save_status_details(cls, chat_id: int, detail_data: dict):
        query = sess().query(cls)
        bot_status_record = query.filter(cls.chat_id == chat_id).one_or_none()

        bot_status_record.status.update(**detail_data)
        bot_status_record.checked_at = datetime.utcnow()
        sess().commit()

    @classmethod
    async def add_details_to_status(cls, chat_id: int, detail_data: dict):
        await ClientBotCheckRequests.delete_bot_check_requests(chat_id)
        await cls._save_status_details(chat_id, detail_data)


class ClientBotCheckRequests(Base):

    __tablename__ = "monitoring_check_requests"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    chat_id = Column(BigInteger)
    checked_at = Column(DateTime(timezone=True), default=datetime.utcnow)

    @staticmethod
    @db_func
    def create(chat_id: int):
        check_request = ClientBotCheckRequests(chat_id=chat_id)
        sess().add(check_request)
        sess().commit()

    @classmethod
    @db_func
    def get_unanswered_bots_chat_ids(cls):
        unanswered_bots_chat_ids = sess().query(cls.chat_id).distinct() \
            .filter((func.unix_timestamp(cls.checked_at) + cfg.MONITORING_CONFIRMATION_DELAY)
                    < datetime.utcnow().timestamp()).all()
        unanswered_bots_chat_ids = [_[0] for _ in unanswered_bots_chat_ids]
        return unanswered_bots_chat_ids

    @classmethod
    @db_func
    def delete_bot_check_requests(cls, chat_id: int):
        bot_requests = sess().query(cls).filter(cls.chat_id == chat_id)
        bot_requests.delete()
        sess().commit()

    @classmethod
    @db_func
    def _confirm_bot_active(cls, chat_id: int):
        query = sess().query(ClientBotStatuses)
        query = query.filter(ClientBotStatuses.chat_id == chat_id)
        query.update({ClientBotStatuses.status: {"status": "alive"}})
        sess().commit()

    @classmethod
    async def confirm_bot_active(cls, chat_id: int):
        await cls.delete_bot_check_requests(chat_id)
        await cls._confirm_bot_active(chat_id)
