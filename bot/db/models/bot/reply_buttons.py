import logging

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, CHAR, Column, Comp<PERSON>, <PERSON><PERSON>, Foreign<PERSON>ey, String
from sqlalchemy.orm import relationship

from db.connection import Base
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson
from schemas import MessageContentTypeEnum

logger = logging.getLogger("debugger")


class ReplyButton(Base, BaseDBModel):
    id: int = Column(BigInteger, autoincrement=True, primary_key=True)
    media_id: int | None = Column(
        BigInteger,
        ForeignKey("media_objects.id", ondelete="RESTRICT"),
        nullable=True,
    )
    media: "models.MediaObject | None" = relationship(
        "MediaObject", backref="reply_buttons"
    )
    content_type: MessageContentTypeEnum = Column(
        Enum(MessageContentTypeEnum), nullable=False
    )

    message_virtual: str | None = Column(
        String(700),
        Computed(
            "LEFT(JSON_UNQUOTE(JSON_EXTRACT(content, '$.message')), 700)",
            persisted=True
        ),
        nullable=True,
    )

    hash: str = Column(
        CHAR,
        nullable=False,
        unique=True,
        index=True,
    )
    content: dict | None = Column(NestedMutableJson, nullable=True)
    params: dict | None = Column(NestedMutableJson, nullable=True)

    async def get_media_url(self):
        if not self.media:
            return None
        return await self.media.get_url()

    def get_media(self):
        if not self.media_id:
            return None
        return self.media
