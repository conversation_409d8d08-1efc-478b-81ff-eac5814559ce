import logging
from contextlib import contextmanager
from contextvars import Context<PERSON><PERSON>, Token
from datetime import datetime
from typing import List, Optional, Type

from aiogram import types
from dateutil import tz
from psutils.text import make_deep_link_data
from pytz import utc
from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, ForeignKey, Integer,
    String, case, cast, func,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

import config as cfg
import schemas
from config import WEB_APP_PATH
from db import models
from db.connection import Base
from db.decorators import db_func
from db.helpers import T, sess
from db.mixins import BaseDBModel, ContextMixin
from schemas import BotTypeLiteral
from utils.date_time import utcnow
from utils.helpers import get_running_file_name
from utils.redefined_classes import Bot

logger = logging.getLogger('debugger')


class ClientBot(Base, BaseDBModel, ContextMixin):
    __tablename__ = "bots"

    id: int = Column(BigInteger, autoincrement=True, primary_key=True)
    status = Column(String(10), default="enabled")
    is_started = Column(Boolean, default=False)

    bot_type: schemas.BotTypeLiteral = Column(
        String(8), nullable=False, default="telegram"
    )
    username: str | None = Column(String(256), unique=True, nullable=True)

    token = Column(String(256), unique=True, nullable=True)
    whatsapp_app_id: str = Column(String(50), nullable=True, unique=True)
    whatsapp_app_secret: str = Column(String(50), nullable=True)
    whatsapp_app_name: str = Column(String(256), nullable=True)
    whatsapp_from: str = Column(String(50), nullable=True, unique=True)
    whatsapp_from_phone_number: str = Column(String(50), nullable=True, unique=True)
    whatsapp_business_account_id: str = Column(String(50), nullable=True)

    is_pay4say = Column(Boolean, default=False)
    is_friendly = Column(Boolean, default=False)
    is_lip_lep = Column(Boolean, default=False)

    ask_lang = Column(Boolean, default=True)

    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="RESTRICT"))
    group = relationship(
        "Group", uselist=False, back_populates="bot", foreign_keys=group_id
    )

    last_set_menu_message_id = Column(BigInteger, default=False)

    args = Column(String(256), default=None)

    users_settings = relationship("UserSettings", cascade="all,delete")
    users_activities = relationship("UserClientBotActivity", cascade="all,delete")
    time_created = Column(DateTime(timezone=True), default=utcnow)

    need_chat_header = Column(Boolean, default=True)
    need_leave_chat_keyboard = Column(Boolean, default=False)

    on_join_virtual_manager_id = Column(
        BigInteger, ForeignKey("virtual_managers.id", ondelete="SET NULL")
    )
    on_join_virtual_manager = relationship(
        "VirtualManager", foreign_keys=on_join_virtual_manager_id
    )

    share_bot_vm_id = Column(
        BigInteger, ForeignKey("virtual_managers.id", ondelete="SET NULL")
    )
    share_bot_vm = relationship(
        "VirtualManager", backref="connected_for_share_bots",
        foreign_keys=share_bot_vm_id
    )

    _ctx_bot_id = ContextVar("current_bot_id")

    is_auto_answer = Column(Boolean, default=False)
    auto_answer_delay = Column(Integer, default=5)

    is_show_order_button = Column(Boolean, default=True)

    custom_menu_buttons: list["models.CustomMenuButton"] = relationship(
        "CustomMenuButton", back_populates="bot"
    )

    def __init__(
            self, group: "models.Group",
            bot_type: BotTypeLiteral,  # telegram | whatsapp
            token: str, username: str | None = None,
            whatsapp_from: str | None = None,
            whatsapp_from_phone_number: str | None = None,
            whatsapp_app_id: str | None = None,
            whatsapp_app_secret: str | None = None,
            whatsapp_app_name: str | None = None,
            whatsapp_business_account_id: str | None = None,
    ):
        assert group
        assert token
        if bot_type == "telegram":
            assert username
        else:
            assert whatsapp_from
            assert whatsapp_app_id
            assert whatsapp_app_secret

        self.bot_type = bot_type
        self.group = group
        self.token = token
        self.username = username
        self.whatsapp_from = whatsapp_from
        self.whatsapp_from_phone_number = whatsapp_from_phone_number
        self.whatsapp_app_id = whatsapp_app_id
        self.whatsapp_app_secret = whatsapp_app_secret
        self.whatsapp_app_name = whatsapp_app_name
        self.whatsapp_business_account_id = whatsapp_business_account_id

        super().__init__()

    @hybrid_property
    def display_name(self):
        if self.bot_type == "telegram":
            return self.username
        elif self.bot_type == "whatsapp":
            return self.whatsapp_app_name

        raise ValueError("Unknown bot type: %s" % self.bot_type)

    @display_name.expression
    def display_name(self):
        return cast(
            case(
                [
                    (self.bot_type == "telegram", self.username),
                    (self.bot_type == "whatsapp", self.whatsapp_app_name),
                ], else_=func.concat("id: ", self.id)
            ), String
        )

    @property
    def id_name(self):
        if self.bot_type == "telegram":
            return self.username
        elif self.bot_type == "whatsapp":
            return self.whatsapp_from_phone_number

        raise ValueError("Unknown bot type: %s" % self.bot_type)

    def telegram_link(self, deep_link_mode: str | None = None, **kwargs):
        if self.bot_type == "telegram":
            if deep_link_mode:
                deep_link_data = make_deep_link_data(deep_link_mode, **kwargs)
            elif kwargs:
                raise ValueError(
                    "deep_link_mode must be specified when kwargs is specified"
                )
            else:
                deep_link_data = None

            link = f"https://t.me/{self.username}"
            if deep_link_data:
                link += f"?start={deep_link_data}"

            return link

    def whatsapp_link(self, text: str = "start"):
        if self.bot_type == "whatsapp":
            return f"https://wa.me/{self.whatsapp_from_phone_number}?text={text}"

    @property
    def link(self):
        if self.bot_type == "telegram":
            return self.telegram_link()

        if self.bot_type == "whatsapp":
            return self.whatsapp_link()

    @property
    def display_link(self):
        if self.bot_type == "telegram":
            return f"@{self.username}"
        elif self.bot_type == "whatsapp":
            return f"<a href=\"{self.whatsapp_link()}\">{self.whatsapp_app_name}</a>"

    def get_webapp_link(self, path: str):
        params = {
            "bot_id": self.id
        }
        if self.group.brand:
            params["brand_id"] = self.group.brand.id

        params_str = "&".join([f"{key}={value}" for key, value in params.items()])
        return f"{WEB_APP_PATH}/{path}?{params_str}"

    @classmethod
    @db_func
    def check_exists(cls, token: str) -> bool:
        query = sess().query(ClientBot.id).filter_by(
            token=token, status="enabled"
        ).exists()
        result = sess().query(query).scalar()
        return bool(result)

    @classmethod
    async def get_bot_info(cls, token: str) -> types.User:
        current_bot = Bot.get_current()
        with current_bot.with_token(token):
            bot_user = await current_bot.get_me()
        return bot_user

    @db_func
    def change_show_order_button(self):
        self.is_show_order_button = not self.is_show_order_button
        sess().commit()

    @property
    def telegram_bot_id(self):
        return int(self.token.split(sep=":")[0])

    @classmethod
    def set_current_bot_id(cls, value: int | None):
        return cls._ctx_bot_id.set(value)

    @classmethod
    def get_current_bot_id(cls, no_error: bool = True):
        if no_error:
            return cls._ctx_bot_id.get(None)
        return cls._ctx_bot_id.get()

    @classmethod
    def reset_current_bot_id(cls, reset_token: Token):
        return cls._ctx_bot_id.reset(reset_token)

    @classmethod
    def get_bot_id_for_logging(cls):
        file_name = get_running_file_name()
        if file_name == cfg.SERVICE_BOT:
            return cfg.SERVICE_BOT_USERNAME
        elif file_name in [cfg.CLIENT_BOT, cfg.FRIENDLY_BOT]:
            return cls.get_current_bot_id()

    @classmethod
    async def get_current_bot_token(cls) -> str | None:
        file_name = get_running_file_name()
        if file_name == cfg.SERVICE_BOT:
            return cfg.SERVICE_BOT_API_TOKEN
        elif file_name in [cfg.CLIENT_BOT, cfg.FRIENDLY_BOT]:
            current_bot = await cls.get_current(no_error=False)
            return current_bot.token

    @classmethod
    async def get_current(cls, no_error: bool = True) -> Optional["ClientBot"]:
        current_bot_id = cls.get_current_bot_id(no_error)
        if not current_bot_id:
            return None

        if no_error:
            bot = cls._ctx_instance.get(None)
        else:
            bot = cls._ctx_instance.get()

        if bot and bot not in sess():
            bot = None
            cls.set_current(None)

        if bot is None:
            bot = await cls._get(current_bot_id)
            cls.set_current(bot)

        elif bot.id != current_bot_id:
            cls.set_current(None)
            return await cls.get_current()

        return bot

    @classmethod
    def set_current(cls: Type[T], value: T | None):
        if not isinstance(value, cls | None):
            raise TypeError(
                f"Value should be instance of {cls.__name__!r} or None not "
                f"{type(value).__name__!r}"
            )
        cls._ctx_instance.set(value)

    @classmethod
    async def get_current_bot_username(cls, *, no_error: bool = True) -> str:
        bot = await models.ClientBot.get_current(no_error=no_error)
        file_name = get_running_file_name()
        if bot:
            bot_username = bot.display_name
        elif file_name == cfg.SERVICE_BOT:
            bot_username = cfg.SERVICE_BOT_USERNAME
        else:
            bot_username = None
        return bot_username

    @classmethod
    @contextmanager
    def with_id(cls, bot_id: int | None):
        token = cls._ctx_bot_id.set(bot_id)
        try:
            yield
        finally:
            cls._ctx_bot_id.reset(token)

    @db_func
    def started(self) -> bool:
        self.is_started = True
        sess().commit()
        return True

    @db_func
    def stopped(self) -> bool:
        self.is_started = False
        sess().commit()
        return True

    @staticmethod
    @db_func
    def get_bots_usernames(status: str = "enabled"):
        bots_usernames = sess().query(ClientBot.username).filter(
            ClientBot.status == status
        ).all()
        bots_usernames = [_[0] for _ in bots_usernames]
        return bots_usernames

    def get_link_for_share(self, recommender_id: int):
        return f"t.me/{self.username}?start=rb_r-{recommender_id}"  # rb = recommend_bot

    @property
    def mode(self) -> str:
        if self.is_friendly:
            return "is_friendly"
        elif self.is_pay4say:
            return "is_pay4say"
        else:
            return "default"

    @staticmethod
    def get_texts_for_edit() -> list:
        return [c_.name for c_ in ClientBot.__table__.c if
                c_.name.endswith("_button") or c_.name.endswith("_text")]

    @property
    def owner(self) -> "models.User":
        return self.group.owner

    @classmethod
    async def _get(
            cls, bot_id: int = None,
            username: str = None,
            token: str = None,
            whatsapp_from: str | None = None,
            **kwargs,
    ) -> "ClientBot | None":
        if bot_id:
            kwargs["id"] = bot_id
        if username:
            kwargs["username"] = username
        if token:
            kwargs["token"] = token
        if whatsapp_from:
            kwargs["whatsapp_from"] = whatsapp_from

        if kwargs:
            kwargs["status"] = "enabled"
            return await super().get(**kwargs)

    @classmethod
    async def get(
            cls, bot_id: "ClientBot | int | None" = None,
            username: str = None,
            token: str = None,
            whatsapp_from: str | None = None,
            **kwargs,
    ) -> Optional["ClientBot"]:
        if isinstance(bot_id, cls):
            if (
                    not username and
                    not token and
                    not whatsapp_from and
                    not kwargs
            ):
                return bot_id
            bot_id = bot_id.id

        if bot_id == cls.get_current_bot_id() \
                and username is None \
                and token is None \
                and whatsapp_from is None \
                and not kwargs:
            return await cls.get_current()
        return await cls._get(bot_id, username, token, whatsapp_from, **kwargs)

    @db_func
    def update(self, *, set_all: bool = False, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k in dir(self) and (
                    set_all or k not in ["group_id", "token", "username"]):
                setattr(self, k, v)
        sess().commit()
        return True

    @property
    def timezone(self) -> str:
        return self.group.timezone

    @property
    def local_time(self) -> datetime:
        time_zone = tz.gettz(self.timezone)
        return utc.localize(utcnow()).astimezone(time_zone)

    async def get_user_activity(
            self, user: "models.User"
    ) -> "models.UserClientBotActivity":
        user_bot_activity = await models.UserClientBotActivity.get(user, self)
        return user_bot_activity

    @property
    def lang(self) -> str:
        return self.group.lang

    @property
    def country(self) -> str:
        return self.group.country

    @classmethod
    @db_func
    def get_list(
            cls, status: str = "enabled", is_started: bool = None,
            is_pay4say: bool = None, is_friendly: bool = None,
            position: int = 0, limit: int = None,
            bot_type: schemas.BotTypeLiteral | None = None,
            operation: str = "all"
    ) -> List["ClientBot"] | int:

        if operation == "count":
            query = sess().query(func.count(cls.id))
        else:
            query = sess().query(cls)

        if bot_type:
            query = query.filter(cls.bot_type == bot_type)

        if status != "all":
            query = query.filter(cls.status == status)
        if is_pay4say is not None:
            query = query.filter(cls.is_pay4say.is_(is_pay4say))
        if is_friendly is not None:
            query = query.filter(cls.is_friendly.is_(is_friendly))
        if is_started is not None:
            query = query.filter(cls.is_started.is_(is_started))

        if operation == "count":
            return query.scalar()

        count_subquery = sess().query(
            func.count(
                models.UserClientBotActivity.id
            )
        ).filter(
            models.UserClientBotActivity.bot_id == cls.id
        )
        query = query.order_by(count_subquery.scalar_subquery())

        slice_args = [position, None]
        if limit:
            slice_args[1] = position + limit
        query = query.slice(*slice_args)

        return query.all()

    async def delete(self, user: "models.User"):
        return await self.group.delete(user)

    @classmethod
    @db_func
    def get_lip_lep_bot(cls) -> "ClientBot":
        query = sess().query(cls)
        query = query.filter(cls.is_lip_lep.is_(True))
        bots = query.all()

        if not bots:
            raise ValueError("lip lep bots was not found")

        if len(bots) > 1:
            raise ValueError("multiple lip lep bots was found")

        return bots[0]
