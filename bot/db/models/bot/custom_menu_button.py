from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Foreign<PERSON><PERSON>, String
from sqlalchemy.dialects.mysql import SMALLINT
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import CustomMenuButtonActionTypeEnum


class CustomMenuButton(Base, BaseDBModel, TimeCreatedMixin):
    bot_id: int = Column(ForeignKey("bots.id", ondelete="CASCADE"), nullable=False)
    bot: "models.ClientBot" = relationship(
        "ClientBot", back_populates="custom_menu_buttons"
    )

    action_type: CustomMenuButtonActionTypeEnum = Column(
        Enum(CustomMenuButtonActionTypeEnum), nullable=False
    )

    text: str = Column(String(100), nullable=False)

    vm_id: int | None = Column(ForeignKey("virtual_managers.id", ondelete="CASCADE"))
    vm: "models.VirtualManager" = relationship(
        "VirtualManager", backref="custom_menu_buttons"
    )

    position: int = Column(SMALLINT(unsigned=True))

    def as_dict(self):
        """Return object as dictionary."""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
