from typing import List

from sqlalchemy import <PERSON><PERSON>n, BigInteger, Foreign<PERSON>ey, Integer
from sqlalchemy.orm import relationship

from db import db_func, sess
from db.connection import Base


class InterestSetting(Base):

    __tablename__ = "interest_settings"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot = relationship("ClientBot", foreign_keys=bot_id)
    frequency_for_user = Column(Integer, default=60*60*24)
    lifetime_post = Column(Integer, default=60*60*2)

    def __init__(self, bot_id: int):
        self.bot_id = bot_id

    @classmethod
    def sync_create(cls, bot_id: int):
        """
        This method is not decorated by @db_func, and it is blocking.
        Use it only in functions decorated by @db_func or run_in_executor

        """
        setting = cls(bot_id)
        sess().add(setting)
        sess().commit()
        return setting

    @classmethod
    @db_func
    def create(cls, bot_id: int):
        setting = cls.sync_create(bot_id)
        return setting

    @classmethod
    def sync_get(cls, bot_id: int) -> "InterestSetting":
        """
        This method is not decorated by @db_func, and it is blocking.
        Use it only in functions decorated by @db_func or run_in_executor

        """
        return sess().query(InterestSetting).filter(InterestSetting.bot_id == bot_id).one()

    @classmethod
    @db_func
    def get(cls, bot_id: int):
        settings = cls.sync_get(bot_id)
        if not settings:
            settings = InterestSetting.sync_create(bot_id)
        return settings

    @classmethod
    @db_func
    def get_bots(cls) -> List[int]:
        query = sess().query(cls.bot_id)
        result = [el[0] for el in query.all()]
        return result

    @db_func
    def update(self, field_name: str, value: int) -> bool:
        """Update fields: [
            frequency_for_user,
            live_time_post]
        """
        setattr(self, field_name, value)
        sess().commit()
        return True
