from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import List, Tuple

from sqlalchemy import <PERSON>umn, BigInteger, DateTime, ForeignKey, Text, not_
from sqlalchemy.orm import relationship

from db import db_func, sess, models
from db.connection import Base
from db.my_columns import NestedMutableJson


class InterestPost(Base):

    __tablename__ = "interest_posts"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    text = Column(Text(collation="utf8mb4_unicode_ci"))
    owner_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    owner = relationship("User", foreign_keys=owner_id)
    created_on = Column(DateTime(timezone=True), default=datetime.utcnow)
    interests = Column(NestedMutableJson)

    def __init__(self, text: str, owner: "models.User", interests: List[str]):
        assert type(text) is text
        assert owner

        self.text = text
        self.owner = owner
        self.interests = interests

    @classmethod
    @db_func
    def create(
            cls,
            text: str,
            user: "models.User",
            interests: List[str] = None
    ) -> "InterestPost":
        if interests is None:
            interests = list()

        post = InterestPost(text, user, interests)
        sess().add(post)
        sess().commit()
        return post

    @classmethod
    @db_func
    def get(cls, post_id: int) -> "InterestPost":
        return sess().query(cls).filter(cls.id == post_id).one()

    @classmethod
    @db_func
    def get_list(cls) -> List["InterestPost"]:
        return sess().query(cls).all()

    @classmethod
    @db_func
    def get_posts_for_users(cls, bot_id: int) -> List[Tuple[int, int]]:
        settings = models.InterestSetting.sync_get(bot_id)

        td_lifetime_post = timedelta(seconds=settings.lifetime_post)
        td_frequency_for_user = timedelta(seconds=settings.frequency_for_user)
        time_post_ago = datetime.utcnow() - td_lifetime_post
        time_user_ago = datetime.utcnow() - td_frequency_for_user

        subquery = sess().query(models.InterestStatistic.id)
        subquery = subquery.join(models.UserClientBotActivity, models.UserClientBotActivity.bot_id == bot_id)
        subquery = subquery.join(cls, cls.created_on > time_post_ago)

        subquery = subquery.filter(models.InterestStatistic.bot_id == bot_id)
        subquery = subquery.filter(models.UserClientBotActivity.is_entered_bot.is_(True))
        subquery = subquery.filter(models.InterestStatistic.user_id == models.UserClientBotActivity.user_id)
        subquery = subquery.filter(models.InterestStatistic.post_id == cls.id)
        subquery = subquery.filter(models.InterestStatistic.datetime > time_user_ago)

        query = sess().query(models.User.chat_id, cls.id)
        query = query.join(models.User)
        query = query.join(models.UserClientBotActivity, models.UserClientBotActivity.bot_id == bot_id)

        query = query.filter(cls.created_on > time_post_ago)
        query = query.filter(models.UserClientBotActivity.is_entered_bot.is_(True))
        query = query.filter(models.User.id == models.UserClientBotActivity.user_id)
        query = query.filter(not_(subquery.exists()))

        return query.all()

    def get_interests(self) -> List[str]:
        return [interest for interest in self.interests]
