from datetime import datetime
from typing import List, Union

from sqlalchemy import <PERSON>umn, BigInteger, DateTime, ForeignKey, SmallInteger, String
from sqlalchemy.orm import relationship

from db import db_func, sess, models
from db.connection import Base


class InterestStatistic(Base):

    __tablename__ = "interest_statistics"

    id = Column(BigInteger, primary_key=True, autoincrement=True)

    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot = relationship("ClientBot", foreign_keys=bot_id)

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user = relationship("User", foreign_keys=user_id)

    post_id = Column(BigInteger, ForeignKey("interest_posts.id", ondelete="SET NULL"), nullable=True)
    post = relationship("InterestPost", foreign_keys=post_id)

    interest = Column(String(256, collation="utf8mb4_unicode_ci"), default="")
    status = Column(SmallInteger, default=0)

    datetime = Column(DateTime(timezone=True), default=datetime.utcnow)

    def __init__(self, bot: "models.ClientBot", user: "models.User", post: "models.InterestPost"):
        assert bot
        assert user
        assert post

        self.bot = bot
        self.user = user
        self.post = post

    @classmethod
    @db_func
    def create(cls, bot: "models.ClientBot", user: "models.User", post: "models.InterestPost"):
        result = cls(bot, user, post)
        sess().add(result)
        sess().commit()
        return result

    @classmethod
    @db_func
    def get(cls, statistic_id: int):
        return sess().query(cls).filter(cls.id == statistic_id).first()

    @classmethod
    @db_func
    def get_user_statistic(
            cls,
            bot_id: int,
            user_id: int = None,
            post_id: int = None,
            interest: str = None,
            status: int = None,
            date_begin: datetime = None,
            date_end: datetime = None
    ) -> Union[List["InterestStatistic"], "InterestStatistic"]:
        query = sess().query(InterestStatistic)
        query = query.filter(InterestStatistic.bot_id == bot_id)
        if user_id:
            query = query.filter(InterestStatistic.user_id == user_id)
        if post_id:
            query = query.filter(InterestStatistic.post_id == post_id)
            return query.one()

        if interest:
            query = query.filter(InterestStatistic.interest == interest)
        if status is not None:
            query = query.filter(InterestStatistic.status == status)
        if date_begin:
            query = query.filter(InterestStatistic.datetime >= date_begin)
        if date_end:
            query = query.filter(InterestStatistic.datetime <= date_end)
        return query.all()

    @db_func
    def update(self, interest: str | None, status: int) -> bool:
        if interest:
            self.interest = interest
        self.status = status
        sess().commit()
        return True

    @property
    def active(self) -> str | int:
        return self.interest or self.status
