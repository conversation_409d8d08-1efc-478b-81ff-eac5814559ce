from datetime import datetime

from sqlalchemy import <PERSON>umn, BigInteger, Foreign<PERSON>ey, Boolean, String, DateTime, JSON
from sqlalchemy.orm import relationship

from config import WEB_APP_PATH
from db import models, db_func, sess
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from utils.exceptions.short_token import InvalidShortTokenError
from utils.short_token_creator import make_short_token, decode_short_token


class ShortToken(Base, BaseDBModel, TimeCreatedMixin):
    is_used: bool = Column(Boolean, nullable=False, default=False)
    used_datetime: datetime | None = Column(DateTime, nullable=True, default=None)

    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    user: "models.User" = relationship("User", backref="short_tokens")

    bot_id: int = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"), nullable=True, default=None)
    bot: "models.ClientBot" = relationship("ClientBot", backref="short_tokens")

    lang: str = Column(String(2), nullable=True)
    url_path: str = Column(String(255), nullable=True)
    params: dict = Column(JSON, nullable=True)

    scopes: str = Column(String(100), nullable=False)  # example: "me:read, me:write"

    @classmethod
    @db_func
    def _save(
            cls, user_id: int,
            scopes: str,
            bot_id: int | None = None,
            lang: str | None = None,
            url_path: str | None = None,
            params: dict | None = None,
    ) -> "ShortToken":
        token_obj = ShortToken(
            user_id=user_id,
            bot_id=bot_id,
            lang=lang,
            url_path=url_path,
            params=params,
            scopes=scopes,
        )
        sess().add(token_obj)
        sess().commit()
        return token_obj

    @classmethod
    async def create(
            cls, user_id: int,
            scopes: str,
            bot_id: int | None = None,
            lang: str | None = None,
            url_path: str | None = None,
            params: dict | None = None,
    ) -> "ShortToken":
        return await cls._save(user_id, scopes, bot_id, lang, url_path, params)

    @classmethod
    async def use(cls, token: str) -> tuple["ShortToken", bool]:
        try:
            token_id = decode_short_token(token)
        except Exception as e:
            raise InvalidShortTokenError() from e

        return await cls._use(token_id)

    @classmethod
    @db_func
    def _use(cls, token_id: int):
        token: ShortToken = cls.get_sync(token_id, for_update=True)
        if not token:
            raise InvalidShortTokenError()

        if token.is_used:
            sess().commit()
            return token, False
        else:
            token.is_used = True
            token.used_datetime = datetime.utcnow()
            sess().commit()
            return token, True

    @property
    def token(self):
        return make_short_token(self.id)

    def get_webapp_link(self, path: str):
        if path.startswith("/"):
            path = path[1:]
        return f"{WEB_APP_PATH}/{path}?st={self.token}"
