from sqlalchemy import Column, String

from db import db_func, sess
from db.connection import Base
from utils.incrementor import increment_string


class CompressedLink(Base):
    __tablename__ = "compressed_links"

    id = Column(String(6, collation="utf8_bin"), primary_key=True)
    payload = Column(String(100))

    def __init__(self, id: str, payload: str):
        self.id = id
        self.payload = payload
        super().__init__()

    def __str__(self):
        return self.id

    @classmethod
    @db_func
    def save(cls, id: str, payload: str):
        link = cls(id, payload)
        sess().add(link)
        sess().commit()
        return link

    @classmethod
    @db_func
    def __get_last_id(cls) -> str:
        query = sess().query(cls.id)
        query = query.order_by(cls.id.desc())
        query = query.limit(1)
        return query.scalar() or "100"

    @classmethod
    @db_func
    def __get_existing(cls, payload: str):
        query = sess().query(cls)
        query = query.filter_by(payload=payload)
        query = query.order_by(cls.id.desc())
        return query.one_or_none()

    @classmethod
    @db_func
    def __check_id_exists(cls, id: str):
        return sess().query(sess().query(cls.id).filter_by(id=id).exists()).scalar()

    @classmethod
    async def generate_id(cls) -> str:
        last_id = await cls.__get_last_id()

        while True:
            new_id = increment_string(last_id)
            if not await cls.__check_id_exists(new_id):
                return new_id

    @classmethod
    async def create(cls, payload: str) -> "CompressedLink":
        existing = await cls.__get_existing(payload)
        if existing:
            return existing

        id = await cls.generate_id()
        return await cls.save(id, payload)

    @classmethod
    async def compress(cls, link: str) -> str:
        split_link = link.split("?start=", maxsplit=1)
        if len(split_link) != 2:
            raise ValueError(f"invalid link: {link}")

        link, payload = split_link

        compressed_link = await cls.create(payload)
        return f"{link}?start={compressed_link}"

    @classmethod
    @db_func
    def get(cls, id: str) -> "CompressedLink":
        return sess().query(cls).filter_by(id=id).one()
