from sqlalchemy import <PERSON>umn, BigInteger, Foreign<PERSON>ey, Integer

from db.connection import Base


class DrawsChannelsSubscriptionAssociation(Base):

    __tablename__ = "draws_channels_subscription_associations"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    draw_id = Column(BigInteger, ForeignKey("draws.id", ondelete="CASCADE"))
    channel_id = Column(BigInteger, ForeignKey("channels.id", ondelete="CASCADE"))


class DrawResultsChannelsSubscriptedAssociation(Base):

    __tablename__ = "draw_results_channels_subscripted_associations"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    drawresult_id = Column(BigInteger, ForeignKey("draw_results.id", ondelete="CASCADE"))
    channel_id = Column(BigInteger, ForeignKey("channels.id", ondelete="CASCADE"))


class DrawResultsInviteFriendsAssociation(Base):

    __tablename__ = "draw_results_invite_friends_associations"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    drawresult_id = Column(BigInteger, ForeignKey("draw_results.id", ondelete="CASCADE"))
    telegramuser_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))


class DrawResultsFollowUsersAssociation(Base):

    __tablename__ = "draw_results_follow_users_associations"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    drawresult_id = Column(BigInteger, ForeignKey("draw_results.id", ondelete="CASCADE"))
    telegramuser_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
