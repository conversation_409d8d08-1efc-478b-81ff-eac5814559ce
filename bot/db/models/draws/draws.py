import config as cfg
from datetime import datetime
from typing import List, Dict

from sqlalchemy import Column, Integer, String, BigInteger, Text, Boolean, DateTime
from sqlalchemy import ForeignKey
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.helpers import sess
from db.decorators import db_func
from db.my_columns import NestedMutableJson

from utils.media import delete_file

from utils.redefined_classes import Bot


class Draw(Base):

    __tablename__ = "draws"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    is_simple_mode = Column(Boolean, default=True)

    name = Column(Text(collation="utf8mb4_unicode_ci"))
    status = Column(String(10), default="active")  # ("active", "stopped",)

    creator_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"), nullable=False)
    creator = relationship("User", foreign_keys=creator_id, backref="created_draws")

    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot = relationship("ClientBot", foreign_keys=bot_id, backref="draws")

    created_by_bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    created_by_bot = relationship("ClientBot", foreign_keys=created_by_bot_id)

    content_type = Column(String(15))
    media_path = Column(String(512), default=None, nullable=True)
    description = Column(Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None)
    terms = Column(Text(collation="utf8mb4_unicode_ci"))

    time_spending = Column(DateTime(timezone=True), nullable=False)
    time_created = Column(DateTime(timezone=True), default=datetime.utcnow)

    channels_for_subscription = relationship("Channel", secondary="draws_channels_subscription_associations")

    channel_to_invite_friends_id = Column(BigInteger, ForeignKey("channels.id", ondelete="SET NULL"), nullable=True, default=None)
    channel_to_invite_friends = relationship("Channel", foreign_keys=channel_to_invite_friends_id)
    count_invite_friends = Column(Integer, default=0)

    follow_link = Column(Text(), nullable=True, default=None)
    count_followed_link = Column(Integer, default=0)

    channel_for_writing_messages_id = Column(BigInteger, ForeignKey("channels.id", ondelete="SET NULL"), nullable=True, default=None)
    channel_for_writing_messages = relationship("Channel", foreign_keys=channel_for_writing_messages_id)
    count_needed_messages = Column(Integer, default=0)

    instructions_for_writing_messages = Column(Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None)
    filter_words_message = Column(NestedMutableJson, default=None)

    is_public = Column(Boolean, default=True)
    count_notifications = Column(Integer, default=3)

    contact_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    contact = relationship("User", foreign_keys=contact_id, backref="draws")

    award_categories = relationship("AwardCategory", back_populates="draw")
    results: list["models.DrawResult"] = relationship("DrawResult", back_populates="draw")

    SUPPORTED_MESSAGE_TYPES = cfg.MEDIA_WITH_CAPTION + ["text"]

    def __init__(
            self,
            name: str,
            bot_id: int,
            created_by_bot_id: int,
            creator_id: int,
            contact_id: int,
            terms: str,
            time_spending: datetime,
            content_type: str,
            description: str = None,
            media_path: str = None,
    ):
        assert name
        assert bot_id
        assert created_by_bot_id
        assert creator_id
        assert contact_id
        assert terms
        assert time_spending
        assert content_type
        assert description or media_path

        self.name = name
        self.bot_id = bot_id
        self.created_by_bot_id = created_by_bot_id
        self.creator_id = creator_id
        self.contact_id = contact_id
        self.terms = terms
        self.time_spending = time_spending
        self.content_type = content_type
        self.description = description
        self.media_path = media_path

    @classmethod
    @db_func
    def create(
            cls,
            name: str,
            bot_id: int,
            created_by_bot_id: int,
            creator_id: int,
            contact_id: int,
            terms: str,
            time_spending: datetime,
            content_type: str,
            description: str = None,
            media_path: str = None,
    ):
        if not description and not media_path:
            raise ValueError(
                f"one of description and media_path must be specified. Now: {description = }, {media_path = }"
            )

        draw = cls(
            name,
            bot_id,
            created_by_bot_id,
            creator_id,
            contact_id,
            terms,
            time_spending,
            content_type,
            description,
            media_path
        )
        sess().add(draw)
        sess().commit()
        return draw

    @staticmethod
    @db_func
    def get(draw_id: int) -> "Draw":
        query = sess().query(Draw)
        query = query.filter(Draw.id == draw_id)
        draw = query.one_or_none()
        return draw

    @classmethod
    @db_func
    def get_by_group_id(cls, group_id: int) -> list["Draw"]:
        query = sess().query(Draw)
        query = query.join(models.ClientBot, models.ClientBot.id == Draw.bot_id)
        query = query.join(models.Group, models.Group.id == models.ClientBot.group_id)

        query = query.filter(models.Group.id == group_id)
        query = query.filter(Draw.status == "active")

        return query.all()

    @property
    def kwargs_for_send(self) -> Dict[str, str]:
        content_type = self.content_type
        kwargs = dict(content_type=content_type)
        kwargs.update(text=self.description)
        if content_type != "text":
            kwargs[content_type] = self.media_path
        return kwargs

    @db_func
    def delete_channel_from_subscription(self, channel: "models.Channel"):
        if channel in self.channels_for_subscription:
            self.channels_for_subscription.remove(channel)
            sess().commit()

    @db_func
    def add_channel_to_subscription(self, channel: "models.Channel"):
        if channel not in self.channels_for_subscription:
            self.channels_for_subscription.append(channel)
            sess().commit()

    @db_func
    def set_channel_to_invite_friends(self, channel: "models.Channel", count_invite_friends: int = 0) -> bool:
        if channel is None or self.channel_to_invite_friends_id == channel.id:
            self.channel_to_invite_friends_id = None
            self.count_invite_friends = 0
        else:
            self.channel_to_invite_friends_id = channel.id
            self.count_invite_friends = count_invite_friends
        sess().commit()
        return True

    @db_func
    def set_follow_link(self, link: str, count_followed_link: int = 0) -> bool:
        self.follow_link = link
        self.count_followed_link = count_followed_link
        sess().commit()
        return True

    @db_func
    def set_channel_for_writing_messages(self, channel: "models.Channel", count_needed_messages: int = 0, instructions_for_writing_messages: str = None) -> bool:
        if channel is None or self.channel_for_writing_messages_id == channel.id:
            self.channel_for_writing_messages_id = None
            self.count_needed_messages = 0
            self.instructions_for_writing_messages = None
        else:
            self.channel_for_writing_messages_id = channel.id
            self.count_needed_messages = count_needed_messages
            self.instructions_for_writing_messages = instructions_for_writing_messages
        sess().commit()
        return True

    def get_filter(self) -> List[str]:
        filters = self.filter_words_message
        return filters if filters else list()

    async def append_filter(self, texts_input: str):
        words_list = [word.strip() for word in texts_input.split(",") if word.strip()]

        old_list = self.get_filter()
        if not old_list:
            old_list = list()

        new_list = old_list + words_list
        new_list = list(set(new_list))

        await self.update(filter_words_message=new_list)
        return True

    @db_func
    def set_filter(self, new_list: list) -> bool:
        self.filter_words_message = new_list
        sess().commit()
        return True

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k in dir(self) and k != "id":
                setattr(self, k, v)

        sess().commit()
        return True

    async def delete(self) -> bool:
        if self.media_path:
            delete_file(self.media_path)

        for result in self.results:
            await result.delete()

        for award_category in self.award_categories:
            await award_category.delete()

        sess().delete(self)
        sess().commit()
        return True

    @property
    def count_channels_for_subscription(self) -> int:
        return len(self.channels_for_subscription)


class DrawResult(Base):

    __tablename__ = "draw_results"

    id = Column(BigInteger, primary_key=True, autoincrement=True)

    draw_id = Column(BigInteger, ForeignKey("draws.id", ondelete="CASCADE"))
    draw = relationship("Draw", back_populates="results")

    time_created = Column(DateTime(timezone=True), default=datetime.utcnow)

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user = relationship("User")
    user_link = Column(Text())

    count_writing_messages = Column(Integer, default=0)

    channels_subscripted = relationship("Channel", secondary="draw_results_channels_subscripted_associations")
    invite_friends = relationship("User", secondary="draw_results_invite_friends_associations")
    follow_users = relationship("User", secondary="draw_results_follow_users_associations")

    award_category_id = Column(BigInteger, ForeignKey("award_categories.id", ondelete="CASCADE"), nullable=True, default=None)
    award_category = relationship("AwardCategory")

    count_notifications = Column(Integer, default=0)
    time_need_notification = Column(DateTime(timezone=True), nullable=True, default=None)

    @staticmethod
    @db_func
    def save(
            draw_id: int,
            user_id: int,
            user_link: str,
    ):
        result = DrawResult(
            draw_id=draw_id,
            user_id=user_id,
            user_link=user_link,
        )

        sess().add(result)
        sess().commit()
        return result

    @staticmethod
    async def create(
            draw_id: int,
            user_id: int,
            user_link: str,
    ) -> "DrawResult":
        result = await DrawResult.save(draw_id, user_id, user_link)

        await result.sync_channels_subscripted()
        return result

    async def sync_channels_subscripted(self):
        bot = Bot.get_current()
        for channel in self.draw.channels_for_subscription:
            user_info = await bot.get_chat_member(channel.chat_id, self.user.chat_id)
            if user_info.status in ["member", "creator"]:
                await self.add_channel_to_subscripted(channel)

    @db_func
    def delete_channel_from_subscripted(self, channel: "models.Channel"):
        if channel in self.channels_subscripted:
            self.channels_subscripted.remove(channel)
            sess().commit()

    @db_func
    def add_channel_to_subscripted(self, channel: "models.Channel"):
        if channel not in self.channels_subscripted:
            self.channels_subscripted.append(channel)
            sess().commit()

    @db_func
    def delete_invite_friend(self, user: "models.User"):
        if user in self.invite_friends:
            self.invite_friends.remove(user)
            sess().commit()

    @db_func
    def add_invite_friend(self, user: "models.User"):
        if user not in self.invite_friends:
            self.invite_friends.append(user)
            sess().commit()

    @db_func
    def delete_follow_user(self, user: "models.User"):
        if user in self.follow_users:
            self.follow_users.remove(user)
            sess().commit()

    @db_func
    def add_follow_user(self, user: "models.User"):
        if user not in self.follow_users:
            self.follow_users.append(user)
            sess().commit()

    @db_func
    def set_award_category(self, award_category: "models.AwardCategory") -> bool:
        if award_category:
            award_category = award_category.id
        self.award_category_id = award_category

        sess().commit()
        return True

    @db_func
    def update(self, **kwargs) -> bool:
        for field_name, value in kwargs.items():
            if field_name not in dir(self) and field_name in ["draw_id", "user_id"]:
                continue
            setattr(self, field_name, value)
        sess().commit()
        return True

    @staticmethod
    @db_func
    def get(draw_result_id: int = None, user_link: str = None, user_id: int = None, draw_id: int = None) -> "DrawResult":
        query = sess().query(DrawResult)

        if draw_result_id:
            query = query.filter(DrawResult.id == draw_result_id)
        elif user_link:
            query = query.filter(DrawResult.user_link == user_link)
        elif draw_id and user_id:
            query = query.filter(DrawResult.draw_id == draw_id)
            query = query.filter(DrawResult.user_id == user_id)

        return query.one_or_none()

    @staticmethod
    async def get_or_create(draw_id: int, user_id: int, user_link: str) -> "DrawResult":
        result = await DrawResult.get(user_link=user_link)
        if not result:
            result = await DrawResult.create(draw_id, user_id, user_link)
        return result

    @staticmethod
    @db_func
    def get_draws_by_users(user_id: int):
        query = sess().query(DrawResult)
        query = query.filter(DrawResult.user_id == user_id)
        draws_result = query.all()
        return draws_result

    @db_func
    def write_message(self):
        self.count_writing_messages += 1
        sess().commit()

    @db_func
    def delete(self):
        sess().delete(self)
        sess().commit()

    @property
    def count_channels_subscripted(self) -> int:
        return len(self.channels_subscripted)

    @property
    def count_invite_friends(self) -> int:
        return len(self.invite_friends)

    @property
    def count_follow_users(self) -> int:
        return len(self.follow_users)
