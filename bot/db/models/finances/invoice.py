from __future__ import annotations

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Any, TYPE_CHECKING, Type
from uuid import uuid4

from aiogram import types
from incust_api.api import term
from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, Enum, Float, <PERSON><PERSON>ey, Integer, JSON,
    String, Text,
    func,
)
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

import config as cfg
from config import INBOX_MAX_AGE
from db import models
from db.connection import Base
from db.decorators import db_func
from db.helpers import T, sess
from db.mixins import BaseDBModel, TimeCreatedMixin
from db.models.store.payer_fee import PayerFee
from db.my_columns import NestedMutableJson
from schemas import (
    InvoicePaymentModeLiteral, InvoiceQrMode, InvoiceStatusLiteral,
    InvoiceTypeEnum,
)
from utils.date_time import utcnow

if TYPE_CHECKING:
    from db.models import (
        MenuInStore, User, ClientBot, Group, StoreOrder,
        InvoiceTemplate,
    )

logger = logging.getLogger('debugger')


class Invoice(Base, BaseDBModel, TimeCreatedMixin):
    id: int
    status: InvoiceStatusLiteral = Column(String(20), default="not_payed")

    payment_mode: InvoicePaymentModeLiteral = Column(String(20))
    invoice_type: InvoiceTypeEnum = Column(Enum(InvoiceTypeEnum))

    invoice_template_id: int | None = Column(
        BigInteger, ForeignKey("invoice_templates.id", ondelete="SET NULL")
    )
    invoice_template: InvoiceTemplate | None = relationship(
        "InvoiceTemplate", backref="invoices"
    )

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    user: User = relationship("User", foreign_keys=user_id)

    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="RESTRICT"))
    bot: ClientBot | None = relationship("ClientBot", foreign_keys=bot_id)

    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="RESTRICT"))
    group: Group = relationship("Group")

    creator_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    creator: User = relationship("User", foreign_keys=creator_id)

    menu_in_store_id: int = Column(
        BigInteger,
        ForeignKey("menus_in_store.id", ondelete="SET NULL"),
        nullable=True, default=None,
    )
    menu_in_store: MenuInStore | None = relationship(
        "MenuInStore", foreign_keys=menu_in_store_id
    )

    store_order: StoreOrder | None = relationship(
        "StoreOrder", back_populates="invoice", uselist=False
    )

    payment_bot_menu_id: int = Column(
        BigInteger, ForeignKey("bots.id", ondelete="RESTRICT")
    )
    payment_bot_menu: ClientBot | None = relationship(
        "ClientBot", foreign_keys=payment_bot_menu_id
    )

    payment_bot_id: int = Column(BigInteger, ForeignKey("bots.id", ondelete="RESTRICT"))
    payment_bot: ClientBot | None = relationship(
        "ClientBot", foreign_keys=payment_bot_id
    )

    currency: str = Column(String(20))
    _prices: list[dict] = Column(NestedMutableJson)
    items: list["InvoiceItem"] = relationship("InvoiceItem", back_populates="invoice")
    count: int = Column(Integer, default=1)

    title: str = Column(Text(collation="utf8mb4_unicode_ci"))
    description: str = Column(
        Text(collation="utf8mb4_unicode_ci"), default=None, nullable=True, )
    photo = Column(String(255))  # migrate to media object

    need_name: bool = Column(Boolean, default=False)
    need_phone_number: bool = Column(Boolean, default=False)
    need_email: bool = Column(Boolean, default=False)

    first_name: str | None = Column(String(256))
    last_name: str | None = Column(String(256))
    email: str | None = Column(String(256))
    phone: str | None = Column(String(256))

    expiration_datetime = Column(DateTime(timezone=True))
    _live_time = Column(Float)

    order_info = Column(NestedMutableJson)

    incust_check: dict | None = Column(NestedMutableJson, default=None)
    check_url = Column(Text(collation="utf8mb4_unicode_ci"))

    message_id = Column(BigInteger, nullable=True, default=None)

    external_transaction_id: str | None = Column(String(100), nullable=True)
    incust_transaction_id: str | None = Column(String(100), nullable=True)
    loyalty_settings_id: int | None = Column(BigInteger, ForeignKey("loyalty_settings.id"), nullable=True)
    
    # Додаткові поля для лояльності (замість JSON зберігання)
    bonuses_added_amount: int = Column(BIGINT, default=0)  # Нараховані бонуси
    loyalty_coupons_data: list | None = Column(NestedMutableJson, default=None)  # Список купонів
    special_accounts_charges: list | None = Column(NestedMutableJson, default=None)  # Список спеціальних рахунків
    loyalty_skip_message: bool = Column(Boolean, default=False)  # Пропуск повідомлень
    
    # Додаткові поля для повного функціоналу лояльності
    loyalty_discount_amount: int = Column(BIGINT, default=0)  # Знижка в копійках 
    loyalty_amount: int = Column(BIGINT, default=0)  # Загальна сума чеку в копійках
    loyalty_amount_to_pay: int = Column(BIGINT, default=0)  # Сума до оплати в копійках
    loyalty_transaction_data: dict | None = Column(NestedMutableJson, default=None)  # Дані транзакції
    loyalty_implemented_rules: list | None = Column(NestedMutableJson, default=None)  # Застосовані правила лояльності
    is_loyalty_transaction_completed: bool = Column(Boolean, default=False)  # Транзакція лояльності завершена
    
# Поля loyalty_first_stage_data та loyalty_second_stage_data видалені як непотрібні

    client_redirect_url: str | None = Column(Text(collation="utf8mb4_unicode_ci"))
    successful_payment_callback_url: str | None = Column(
        Text(collation="utf8mb4_unicode_ci")
    )

    webhook_result: dict | None = Column(JSON(none_as_null=True), nullable=True)

    uuid_id: str | None = Column(
        String(36), default=lambda: str(uuid4()), nullable=True
    )

    payer_id: int | None = Column(
        BigInteger, ForeignKey("users.id", ondelete="RESTRICT")
    )
    payer: "models.User | None" = relationship("User", foreign_keys=payer_id)

    is_friend: bool | None = Column(Boolean, default=None, nullable=True)

    user_comment: str | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True
    )

    qr_mode: InvoiceQrMode | None = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )

    shipment_cost: int = Column(BIGINT, default=0)
    custom_payment_cost: int = Column(
        BIGINT, default=0
    )  # will be removed on payments refactor

    before_loyalty_sum: int = Column(BIGINT)
    discount: int = Column(BIGINT, default=0)

    bonuses_redeemed: int = Column(BIGINT, default=0)
    discount_and_bonuses_redeemed: int = Column(BIGINT, default=0)

    is_ewallet_discount: bool = Column(Boolean, default=False)

    total_sum: int = Column(BIGINT, default=0)
    # sum_before_extra_fee: int = Column(BIGINT, default=0)
    total_sum_with_extra_fee: int = Column(BIGINT, default=0)
    sum_to_pay: int = Column(BIGINT, default=0)
    tips_sum: int = Column(BIGINT, default=0)
    payer_fee: int = Column(BIGINT, default=0)
    paid_sum: int = Column(BIGINT, default=0)

    _is_read: bool = Column(Boolean, default=False)
    read_by_user_id: int | None = Column(
        BigInteger, ForeignKey("users.id", ondelete="SET NULL")
    )
    read_by_user: "models.User | None" = relationship(
        "User", backref="read_invoices", foreign_keys=read_by_user_id
    )
    read_status_change_datetime: datetime | None = Column(
        DateTime(timezone=True), nullable=True
    )

    paid_datetime: datetime | None = Column(DateTime(timezone=True))

    ewallet_id: int | None = Column(
        BigInteger, ForeignKey("ewallets.id", ondelete="RESTRICT")
    )

    extra_params: dict[Any, Any] | None = Column(JSON(none_as_null=True), nullable=True)

    utm_labels = Column(
        JSON, nullable=True, default=None
    )  # UTM labels for the invoice

    def __init__(
            self,
            invoice_type: InvoiceTypeEnum,
            payment_mode: InvoicePaymentModeLiteral,
            user: "models.User",
            creator: "models.User", bot: "models.ClientBot" = None,
            invoice_template: "models.InvoiceTemplate" = None,
            count: int = 1,
            currency: str | None = None,
            title: str | None = None,
            description: str | None = None,
            photo_path: str | None = None,
            live_time: float | None = None,
            expiration_datetime: datetime | None = None,
            brand_group: "models.Group" = None,
            incust_check: dict | None = None,
            payment_bot_menu: "models.ClientBot" = None,
            menu_in_store: "models.MenuInStore" = None,
            store_order: "models.StoreOrder" = None,
            first_name: str | None = None,
            last_name: str | None = None,
            email: str | None = None,
            phone: str | None = None,
            **kwargs,
    ):
        if not count:
            count = 1

        super().__init__(**kwargs)

        currency = invoice_template.currency if (invoice_template and
                                                 invoice_template.currency) else (
            currency)
        title = title if title is not None else invoice_template.title
        description = description if description is not None else (
            invoice_template.description)
        photo = photo_path or invoice_template.photo

        if not all([count, currency]):
            raise ValueError(
                f"Arguments count, description and currency "
                f"must be specified or be filled in invoice_template"
            )

        if not live_time:
            live_time = invoice_template.db_live_time if invoice_template else None
        if not expiration_datetime:
            expiration_datetime = invoice_template.expiration_datetime if (
                invoice_template) else None

        group = brand_group

        self.invoice_type = invoice_type
        self.user = user
        self.creator = creator

        self.group = group
        self.bot = bot

        self.currency = currency
        self.count = 0
        self._prices = []

        self.title = title
        self.description = description
        self.photo = photo

        self._live_time = live_time
        self.expiration_datetime = expiration_datetime

        self.incust_check = incust_check
        self.payment_bot_menu = payment_bot_menu
        self.menu_in_store = menu_in_store
        self.payment_mode = payment_mode

        self.store_order = store_order

        self.first_name = first_name
        self.last_name = last_name
        self.email = email
        self.phone = phone

    @hybrid_property
    def live_time(self) -> timedelta:
        ...

    @live_time.expression
    def live_time(self):
        return self._live_time

    @live_time.getter
    def live_time(self) -> timedelta:
        if self._live_time:
            return timedelta(seconds=self._live_time)

    @live_time.setter
    def live_time(self, value: float):
        self._live_time = value

    @hybrid_property
    def full_name(self):
        return f"{self.first_name} {self.last_name}" if all(
            (self.first_name, self.last_name)
        ) else self.first_name or self.last_name

    @full_name.expression
    def full_name(self):
        return func.TRIM(
            func.IFNULL(
                func.concat(self.first_name, ' ', self.last_name),
                func.IFNULL(self.first_name, self.last_name)
            )
        ).label("full_name")

    @hybrid_property
    def prices(self) -> list[dict]:
        return self._prices

    @hybrid_property
    def converted_sum_to_pay(self) -> float:
        return round(self.sum_to_pay / 100, 2)

    @property
    async def lang(self) -> str:
        return await self.user.get_lang(self.bot_id) if self.user else self.group.lang

    @db_func
    def set_order_info(self, order_info: types.OrderInfo | dict):
        self.order_info = order_info.to_python() if isinstance(
            order_info, types.OrderInfo
        ) else order_info
        sess().commit()

    @property
    def photo_url(self) -> str | None:
        if not self.photo:
            return None

        base_url = f"{cfg.STATIC_URL.replace('/static_db', '')}"
        if self.photo and (
                self.photo.startswith("/static") or self.photo.startswith("static")):
            base_url = base_url.replace("/static", "")
        return f"{base_url}/{self.photo}"

    @property
    def photo_params(self) -> dict:
        from PIL import Image
        try:
            photo = self.photo
            if not photo.startswith("/static") and not photo.startswith("static"):
                photo = f"static/{photo}"
            photo = Image.open(photo)
        except:
            return {}
        width, height = photo.size
        return {"photo_width": width, "photo_height": height}

    @db_func
    def payed(
            self, payment_method: str | None = None,
            payer_fee: int | None = None,
    ):
        self.status = "payed"
        self.paid_datetime = utcnow()

        if self.is_friend:
            invoice_to_friend = models.InvoiceToFriend.get_sync(
                invoice_id=self.id, friend_id=self.payer_id, status='pending'
            )
            if invoice_to_friend:
                invoice_to_friend.status = 'payed'

        if payer_fee:
            self.payer_fee = payer_fee
        elif payment_method:
            payer_fee = PayerFee.get_sync(
                invoice_id=self.id, payment_method=payment_method, type="payment_method"
            )
            if payer_fee:
                payer_fee.status = 'payed'
                self.payer_fee = payer_fee.fee
        else:
            self.payer_fee = 0

        self.paid_sum = self.sum_to_pay + self.payer_fee

        sess().commit()
        return True

    @property
    def converted_sums(self):
        return dict(
            shipment_cost=round(self.shipment_cost / 100, 2),
            custom_payment_cost=round(self.custom_payment_cost / 100, 2),
            before_loyalty_sum=round(self.before_loyalty_sum / 100, 2),
            discount=round(self.discount / 100, 2),
            bonuses_redeemed=round(self.bonuses_redeemed / 100, 2),
            discount_and_bonuses_redeemed=round(
                self.discount_and_bonuses_redeemed / 100, 2
            ),
            total_sum=round(self.total_sum / 100, 2),
            total_sum_with_extra_fee=round(self.total_sum_with_extra_fee / 100, 2),
            tips_sum=round(self.tips_sum / 100, 2),
            sum_to_pay=round(self.sum_to_pay / 100, 2),
            payer_fee=round(self.payer_fee / 100, 2),
            paid_sum=round(self.paid_sum / 100, 2),
        )

    @property
    def converted_dict(self):
        return {
            **self.__dict__,
            **self.converted_sums,
        }

    def set_converted_sums(self, obj: T) -> T:
        for key, value in self.converted_sums.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
        return obj

    def from_orm_converted(self, model: Type[T]) -> T:
        return self.set_converted_sums(model.from_orm(self))

    @property
    def incust_check_schema(self):
        if self.incust_check:
            return term.m.Check.parse_obj(self.incust_check)

    @hybrid_property
    def change_date(self):
        return (self.read_status_change_datetime or self.paid_datetime or
                self.time_created)

    @change_date.expression
    def change_date(self):
        return func.IFNULL(
            self.read_status_change_datetime,
            func.IFNULL(self.paid_datetime, self.time_created)
        )

    @hybrid_property
    def is_read(self):
        return self._is_read or self.change_date < utcnow() - INBOX_MAX_AGE

    @is_read.setter
    def is_read(self, value: bool):
        self._is_read = value

    @is_read.expression
    def is_read(self):
        return func.IF(
            self.change_date < utcnow() - INBOX_MAX_AGE, True, self._is_read
        )

    @hybrid_property
    def crm_tag(self):
        return f"invoice-{self.id}"

    @crm_tag.expression
    def crm_tag(self):
        return func.concat("invoice-", self.id)


class InvoiceItem(Base, BaseDBModel, TimeCreatedMixin):
    name: str = Column(String(512), nullable=False)
    category: str = Column(String(255), default="uncategorized", nullable=False)
    quantity: int = Column(Integer, nullable=False, default=1)

    item_code: str | None = Column(String(255), nullable=True)

    # unit
    price: int = Column(BIGINT, nullable=False)
    unit_discount: int = Column(BIGINT, nullable=False, default=0)
    unit_bonuses_redeemed: int = Column(BIGINT, nullable=False, default=0)
    unit_discount_and_bonuses_redeemed: int = Column(BIGINT, nullable=False, default=0)
    final_price: int = Column(BIGINT, nullable=False)

    # unit * quantity
    before_loyalty_sum: int = Column(BIGINT, nullable=False)
    discount: int = Column(BIGINT, nullable=False, default=0)
    bonuses_redeemed: int = Column(BIGINT, nullable=False, default=0)
    discount_and_bonuses_redeemed: int = Column(BIGINT, nullable=False, default=0)
    final_sum: int = Column(BIGINT, nullable=False)

    invoice_id: int = Column(
        BigInteger,
        ForeignKey("invoices.id", ondelete="RESTRICT"),
        nullable=False,
    )
    invoice: "Invoice" = relationship("Invoice", back_populates="items")

    @property
    def converted_sums(self):
        return dict(
            price=round(self.price / 100, 2),
            unit_discount=round(self.unit_discount / 100, 2),
            unit_bonuses_redeemed=round(self.unit_bonuses_redeemed / 100, 2),
            unit_discount_and_bonuses_redeemed=round(
                self.unit_discount_and_bonuses_redeemed / 100, 2
            ),
            final_price=round(self.final_price / 100, 2),
            before_loyalty_sum=round(self.before_loyalty_sum / 100, 2),
            discount=round(self.discount / 100, 2),
            bonuses_redeemed=round(self.bonuses_redeemed / 100, 2),
            discount_and_bonuses_redeemed=round(
                self.discount_and_bonuses_redeemed / 100, 2
            ),
            final_sum=round(self.final_sum / 100, 2)
        )

    @property
    def converted_dict(self):
        return {
            **self.__dict__,
            **self.converted_sums,
        }

    def set_converted_sums(self, obj: T) -> T:
        for key, value in self.converted_sums.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
        return obj

    def from_orm_converted(self, model: Type[T]) -> T:
        return self.set_converted_sums(model.from_orm(self))
