from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from typing import Type

from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, Enum, Float, ForeignKey,
    Integer, Numeric, String, Text, func,
)
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

import schemas
from config import USE_LOCALISATION
from db import models
from db.connection import Base
from db.custom_column_types.pydantic_json import PydanticListJSON
from db.helpers import T
from db.mixins import BaseDBModel, TimeCreatedMixin
from db.my_columns import NestedMutableJson
from schemas import InvoiceTemplatePaymentModeEnum


class InvoiceTemplate(Base, BaseDBModel):
    is_deleted = Column(Boolean, default=False, nullable=False)

    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group: "models.Group" = relationship("Group", backref="invoice_templates")

    currency: str = Column(String(20))

    _prices: list[dict] = Column(NestedMutableJson, nullable=True)

    payment_mode: InvoiceTemplatePaymentModeEnum = Column(
        Enum(InvoiceTemplatePaymentModeEnum), nullable=True
    )
    items: list["InvoiceTemplateItem"] = relationship(
        "InvoiceTemplateItem", back_populates="invoice_template"
    )

    title: str = Column(Text(collation="utf8mb4_unicode_ci"))
    description: str = Column(
        Text(collation="utf8mb4_unicode_ci"), nullable=True, default=None
    )
    photo: str = Column(String(100))

    media_id: int | None = Column(
        BigInteger,
        ForeignKey("media_objects.id", ondelete="RESTRICT"),
    )
    media: "models.MediaObject" = relationship(
        "MediaObject", backref="thumbnails_invoice_templates",
        foreign_keys=media_id
    )

    comment_mode: schemas.InvoiceTemplateLabelCommentMode = Column(
        String(8), default="optional"
    )
    comment_label_raw: str = Column(
        Text(collation="utf8mb4_unicode_ci"), default=USE_LOCALISATION
    )

    client_web_pages: list["models.ClientWebPage"] = relationship(
        "ClientWebPage",
        secondary="client_web_page_to_invoice_templates",
        back_populates="invoice_templates",
    )

    need_name = Column(Boolean, default=True)
    need_phone_number = Column(Boolean, default=True)
    need_email = Column(Boolean, default=True)

    plugins: list[schemas.InvoiceTemplatePluginSchema] | None = Column(
        PydanticListJSON(schemas.InvoiceTemplatePluginSchema),
        nullable=True
    )

    disabled_qty = Column(Boolean, default=False)
    disabled_loyalty = Column(Boolean, default=False)

    expiration_datetime = Column(DateTime(timezone=True))
    _live_time = Column(Float)

    task_id: int | None = Column(
        BigInteger,
        ForeignKey("tasks.id", ondelete="SET NULL"),
    )
    task: "models.Task" = relationship(
        "Task", backref="invoice_template_task",
        foreign_keys=task_id
    )
    incust_terminal_api_key: str | None = Column(
        String(512), nullable=True, default=None
    )

    incust_terminal_id: str | None = Column(
        String(512), nullable=True, default=None
    )

    min_amount: int = Column(BIGINT(unsigned=True), nullable=True)
    max_amount: int = Column(BIGINT(unsigned=True), nullable=True)

    max_bonuses_percent: Decimal | None = Column(
        Numeric(precision=12, scale=2),
        nullable=True, default=None,
    )

    product_code: str | None = Column(String(255), nullable=True, default=None)

    need_auth: bool = Column(Boolean, default=False)

    async def get_media(self) -> "models.MediaObject | None":
        return await models.MediaObject.get(self.media_id) if self.media_id else None

    @property
    async def photo_url(self):
        media = await self.get_media()
        return media.url if media else None

    @property
    async def file_path(self):
        media = await self.get_media()
        return media.file_path if media else None

    @property
    def live_time(self) -> timedelta | None:
        if self._live_time:
            return timedelta(seconds=self._live_time)
        return None

    @live_time.setter
    def live_time(self, value: float | timedelta):
        self._live_time = value if isinstance(value, float) else value.total_seconds()

    @property
    def db_live_time(self) -> float | None:
        return self._live_time

    @property
    def db_prices(self) -> list[dict[str, str | int | float]] | None:
        """Left for migration"""
        return self._prices

    @hybrid_property
    def total_amount(self):
        return func.sum(InvoiceTemplateItem.price * InvoiceTemplateItem.quantity).label(
            "total_amount"
        )

    @hybrid_property
    def total_amount_converted(self):
        return func.round(self.total_amount).label("total_amount_converted")

    @property
    def converted_sums(self):
        return dict(
            min_amount=round(self.min_amount / 100, 2) if self.min_amount else None,
            max_amount=round(self.max_amount / 100, 2) if self.max_amount else None,
        )

    @property
    def converted_dict(self):
        return {
            **self.__dict__,
            **self.converted_sums,
        }

    def set_converted_sums(self, obj: T) -> T:
        for key, value in self.converted_sums.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
        return obj

    def from_orm_converted(self, model: Type[T]) -> T:
        return self.set_converted_sums(model.from_orm(self))

    def __str__(self):
        return f"{self.title}"


class InvoiceTemplateItem(Base, BaseDBModel, TimeCreatedMixin):
    name: str = Column(String(255), nullable=False)
    category: str = Column(String(255), nullable=False, default="uncategorized")
    price: int = Column(BIGINT, nullable=False)  # in cents
    quantity: int = Column(Integer, nullable=False, default=1)
    item_code: str | None = Column(String(255), nullable=True)

    is_deleted = Column(Boolean, default=False, nullable=False)

    invoice_template_id: int = Column(
        BigInteger,
        ForeignKey("invoice_templates.id", ondelete="RESTRICT"),
        nullable=False,
    )
    invoice_template: "InvoiceTemplate" = relationship(
        "InvoiceTemplate", back_populates="items"
    )

    @property
    def converted_sums(self):
        return dict(
            price=round(self.price / 100, 2),
        )

    @property
    def converted_dict(self):
        return {
            **self.__dict__,
            **self.converted_sums,
        }

    def set_converted_sums(self, obj: T) -> T:
        for key, value in self.converted_sums.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
        return obj

    def from_orm_converted(self, model: Type[T]) -> T:
        return self.set_converted_sums(model.from_orm(self))
