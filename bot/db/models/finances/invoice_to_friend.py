from __future__ import annotations

from datetime import datetime
from typing import TYPE_CHECKING, Literal

from sqlalchemy import <PERSON>umn, BigInteger, DateTime, ForeignKey, String, Text
from sqlalchemy.orm import relationship

from db.connection import Base
from db.mixins import BaseDBModel

if TYPE_CHECKING:
    from db.models import User, Invoice, MediaObject


class InvoiceToFriend(Base, BaseDBModel):
    status: Literal["pending", "accepted", "rejected"] = Column(String(20), default="pending")

    invoice_id: int = Column(BigInteger, ForeignKey("invoices.id", ondelete="CASCADE"))
    invoice: Invoice = relationship("Invoice")

    friend_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    friend: User = relationship("User", foreign_keys=friend_id)

    comment: str | None = Column(Text, nullable=True)
    comment_media_id: int | None = Column(
        BigInteger,
        Foreign<PERSON><PERSON>("media_objects.id", ondelete="RESTRICT"),
        nullable=True
    )
    comment_media: MediaObject | None = relationship("MediaObject")

    date_sent_to_friend: datetime = Column(DateTime, default=datetime.utcnow)
