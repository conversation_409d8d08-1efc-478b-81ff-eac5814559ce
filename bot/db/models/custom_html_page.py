from sqlalchemy import Column, String, Text, BigInteger, ForeignKey
from sqlalchemy.orm import relationship

from db.connection import Base
from db.mixins import BaseDBModel


class CustomHTMLPage(Base, BaseDBModel):
    __tablename__ = "custom_html_pages"

    name: str = Column(String(50), unique=True, nullable=False)
    head: str | None = Column(Text, nullable=True)
    body: str | None = Column(Text, nullable=True)

    html_media_id: int | None = Column(
        BigInteger,
        ForeignKey("media_objects.id", ondelete="RESTRICT"),
        nullable=True,
    )
    html_media: "models.MediaObject | None" = relationship("MediaObject", backref="custom_html_pages")
