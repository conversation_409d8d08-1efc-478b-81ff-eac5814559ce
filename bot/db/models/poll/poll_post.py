from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from db import db_func, models, sess
from db.connection import Base


class PollPost(Base):
    __tablename__ = "poll_posts"

    id = Column(BigInteger, primary_key=True)
    post_date = Column(DateTime, default=datetime.utcnow)

    poll_id = Column(
        BigInteger, ForeignKey('polls.id', ondelete="CASCADE"), nullable=False
    )
    poll = relationship("Poll", foreign_keys=poll_id)

    message_id = Column(BigInteger)
    chat_id = Column(BigInteger)

    def __init__(self, poll: "models.Poll", chat_id: int, message_id: int):
        assert poll
        assert chat_id
        assert message_id

        self.poll = poll
        self.chat_id = chat_id
        self.message_id = message_id

    @classmethod
    @db_func
    def create(cls, poll: "models.Poll", chat_id: int, message_id: int):
        poll_post = cls(poll, chat_id, message_id)
        sess().add(poll_post)
        sess().commit()
        return poll_post

    @property
    def user(self):
        return self.poll.user

    @property
    def name(self):
        return self.poll.name

    @property
    def question(self):
        return self.poll.question

    @property
    def is_anonymous(self):
        return self.poll.is_anonymous

    @property
    def content_type(self):
        return self.poll.content_type

    @property
    def file_path(self):
        return self.poll.file_path

    @classmethod
    @db_func
    def get_poll_by_post_id(cls, poll_post_id: int) -> "models.Poll":
        query = sess().query(models.Poll)
        query = query.join(cls)
        query = query.filter(cls.id == poll_post_id)
        return query.one()

    @classmethod
    @db_func
    def get(cls, id: int) -> "PollPost":
        return sess().query(cls).filter(cls.id == id).one()
