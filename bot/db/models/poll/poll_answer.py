from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, exists
from sqlalchemy.orm import relationship

from db import db_func, models, sess
from db.connection import Base


class PollAnswer(Base):
    __tablename__ = "poll_answers"

    id = Column(BigInteger, primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False)
    user = relationship("User")

    is_anonymous = Column(Boolean, nullable=False)

    option_id = Column(
        BigInteger, ForeignKey("poll_options.id", ondelete="CASCADE"), nullable=False
    )
    option = relationship("PollOption", foreign_keys=option_id)

    poll_post_id = Column(
        BigInteger, ForeignKey('poll_posts.id', ondelete="CASCADE"), nullable=False
    )
    poll_post = relationship("PollPost", foreign_keys=poll_post_id)

    def __init__(
            self, user: "models.User", option_id: int, poll_post_id: int,
            is_anonymous: bool = False
    ):
        self.user = user
        self.option_id = option_id
        self.poll_post_id = poll_post_id
        self.is_anonymous = is_anonymous

    @classmethod
    @db_func
    def add_vote(
            cls, user: "models.User", option_id: int, poll_post_id: int,
            is_anonymous: bool = False
    ):
        vote = cls(user, option_id, poll_post_id, is_anonymous)
        sess().add(vote)
        sess().commit()
        return vote

    @classmethod
    @db_func
    def check_user_vote(cls, user_id: int, poll_post_id: int) -> bool:
        stmt = exists().where(cls.user_id == user_id)
        stmt = stmt.where(cls.poll_post_id == poll_post_id)
        return sess().query(stmt).scalar()
