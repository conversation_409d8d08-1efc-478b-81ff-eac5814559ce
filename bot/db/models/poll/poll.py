from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, String
from sqlalchemy.orm import relationship
from typing import List

import config as cfg
from db import db_func, models, sess
from db.connection import Base
from utils.media import delete_file
from utils.text import f


class Poll(Base):
    __tablename__ = "polls"

    id = Column(BigInteger, primary_key=True)
    time_created = Column(DateTime(timezone=True), default=datetime.utcnow)

    user_id = Column(BigInteger, ForeignKey("users.id"))
    user = relationship("User")

    name = Column(String(255))
    question = Column(String(100))

    content_type = Column(String(255))
    file_path = Column(String(255))

    is_anonymous = Column(Boolean, default=True)
    is_deleted = Column(Boolean, default=False)

    def __init__(
            self,
            user: "models.User",
            name: str,
            question: str,
            content_type: str,
            file_path: str,
    ):
        assert user
        assert name
        assert content_type
        assert content_type or question

        self.user = user
        self.name = name
        self.question = question
        self.content_type = content_type
        self.file_path = file_path

    @classmethod
    @db_func
    def save_poll(
            cls,
            user: "models.User",
            name: str,
            question: str,
            content_type: str,
            file_path: str,
    ):
        poll = cls(user, name, question, content_type, file_path)
        sess().add(poll)
        sess().commit()
        return poll

    @staticmethod
    async def generate_name(lang: str) -> str:
        month = await f(datetime.now().strftime("%B"), lang)
        month_day = datetime.now().strftime("%-d")
        weekday = await f(datetime.now().strftime("%A"), lang)
        time = datetime.now().strftime("%H:%-M")
        name = await f(
            "poll auto generated name", lang,
            month=month,
            month_day=month_day,
            weekday=weekday,
            time=time,
        )
        return name

    async def add_poll_option(self, option: str) -> "models.PollOption":
        last_index = await models.PollOption.get_last_index(self.id)
        return await models.PollOption.create(self, option, last_index + 1)

    @db_func
    def update_question(
            self, content_type: str, question: str = None, file_path: str = None
    ):
        if self.file_path and file_path:
            delete_file(content_type)

        self.question = question
        self.content_type = content_type
        self.file_path = file_path
        sess().commit()

    @db_func
    def change_anonymity(self):
        self.is_anonymous = not self.is_anonymous
        sess().commit()

    @classmethod
    @db_func
    def get(cls, poll_id: int) -> "Poll":
        return sess().query(cls).filter(cls.id == poll_id).first()

    @db_func
    def delete(self):
        self.is_deleted = True
        sess().commit()

    async def get_option(self, option_index: int) -> "models.PollOption":
        return await models.PollOption.get(self.id, option_index)

    async def get_options(self, is_sorted: bool = False, sort_type_asc: bool = None) \
            -> \
    List["models.PollOption"]:
        return await models.PollOption.get_list(self.id, is_sorted, sort_type_asc)

    AVAILABLE_CONTENT_TYPES = cfg.MEDIA_WITH_CAPTION + ["text"]
