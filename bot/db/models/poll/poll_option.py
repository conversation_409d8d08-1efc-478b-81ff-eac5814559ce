from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import relationship
from typing import List

from db import db_func, models, sess
from db.connection import Base


class PollOption(Base):
    __tablename__ = "poll_options"

    id = Column(BigInteger, primary_key=True)
    index = Column(Integer)
    text = Column(String(255))
    is_deleted = Column(Boolean, default=False)
    poll_id = Column(
        BigInteger, ForeignKey("polls.id", ondelete="CASCADE"), nullable=False
    )
    poll = relationship("Poll", backref="options", foreign_keys=poll_id)

    def __init__(self, poll: "models.Poll", text: str, index: int):
        assert poll
        assert text

        self.poll = poll
        self.text = text
        self.index = index

    @classmethod
    @db_func
    def create(cls, poll: "models.Poll", text: str, index: int) -> "PollOption":
        poll_option = cls(poll, text, index)
        sess().add(poll_option)
        sess().commit()
        return poll_option

    @classmethod
    @db_func
    def get_last_index(cls, poll_id: int) -> int:
        query = sess().query(cls.index)
        query = query.filter(cls.poll_id == poll_id)
        query = query.filter(cls.is_deleted.is_(False))
        query = query.order_by(cls.index.desc())
        last_index = query.limit(1).one_or_none()
        last_index = last_index[0] if last_index is not None else -1
        return last_index

    @classmethod
    async def save_poll_options(cls, options: list, poll: "models.Poll"):
        last_index = await cls.get_last_index(poll.id) + 1
        [await cls.create(poll, option, index) for index, option in
         enumerate(options, start=last_index)]

    @classmethod
    async def delete_poll_option(cls, poll_id: int, option_index: int):
        poll_option = await cls.get(poll_id, option_index)
        await poll_option.update(is_deleted=True)

    @classmethod
    @db_func
    def get_list(
            cls,
            poll_id: int,
            is_sorted: bool = False,
            sort_type_asc: bool = None
    ) -> List["PollOption"]:

        query = sess().query(cls)
        query = query.filter(cls.poll_id == poll_id)
        query = query.filter(cls.is_deleted.is_(False))

        if is_sorted and sort_type_asc is not None:
            sort_type = cls.index.asc() if sort_type_asc else cls.index.desc()
            query = query.order_by(sort_type)

        return query.all()

    @classmethod
    @db_func
    def get(cls, poll_id: int, option_index: int | str) -> "PollOption":
        query = sess().query(cls)
        query = query.filter(cls.poll_id == poll_id)
        query = query.filter(cls.is_deleted.is_(False))
        if option_index == "first":
            query = query.order_by(cls.index).limit(1)
        elif option_index == "last":
            query = query.order_by(cls.index.desc()).limit(1)
        else:
            query = query.filter(cls.index == option_index)
        return query.one()

    async def edit_text(self, new_text: str):
        await self.update(text=new_text)

    @db_func
    def update(self, **kwargs):
        for key, value in kwargs.items():
            if key not in kwargs:
                continue
            setattr(self, key, value)
        sess().commit()
        return True

    @classmethod
    @db_func
    def swap(cls, option_1: "PollOption", option_2: "PollOption"):
        option_1.index = option_2.index
        option_2.index = option_1.index
        sess().commit()
        return True

    @classmethod
    async def swap_left(cls, poll_id: int, option_index: int) -> int:
        this_option = await cls.get(poll_id, option_index)
        last_option = await cls.get(poll_id, "last")
        if option_index == 0:
            await cls.swap(this_option, last_option)
            return this_option.index

        prev_option = await cls.get(poll_id, option_index - 1)
        if not prev_option:
            await cls.swap(this_option, last_option)
        else:
            await cls.swap(this_option, prev_option)
        return this_option.index

    @classmethod
    async def swap_right(cls, poll_id: int, option_index: int) -> int:
        this_option = await cls.get(poll_id, option_index)
        next_option = await cls.get(poll_id, option_index + 1)
        if not next_option:
            first_option = await cls.get(poll_id, "first")
            await cls.swap(this_option, first_option)
        else:
            await cls.swap(this_option, next_option)
        return this_option.index
