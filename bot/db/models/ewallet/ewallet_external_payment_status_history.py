from sqlalchemy import (
    BigInteger, <PERSON>umn, <PERSON>um, ForeignKey,
)
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import (
    EWalletExtPaymentStatusInitiatedBy,
    EWalletExternalPaymentStatus,
)


class EWalletExternalPaymentStatusHistory(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = "ewallet_external_payment_status_history"

    payment_id: int = Column(
        BigInteger, ForeignKey("ewallet_external_payments.id", ondelete="RESTRICT"),
        nullable=False
    )
    payment: "models.EWalletExternalPayment" = relationship(
        "EWalletExternalPayment",
        backref="history",
    )

    initiated_by: EWalletExtPaymentStatusInitiatedBy = Column(
        Enum(EWalletExtPaymentStatusInitiatedBy),
        nullable=False,
    )

    initiated_by_user_id: int = Column(
        BigInteger,
        ForeignKey("users.id", ondelete="RESTRICT"),
        nullable=False,
    )
    initiated_by_user: "models.User" = relationship(
        "User",
        foreign_keys=initiated_by_user_id,
        backref="ewallet_external_payment_status_history",
    )

    status: EWalletExternalPaymentStatus = Column(
        Enum(EWalletExternalPaymentStatus),
        default=EWalletExternalPaymentStatus.CREATED,
        nullable=False,
    )
