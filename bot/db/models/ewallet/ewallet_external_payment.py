import uuid
from datetime import datetime
from decimal import Decimal
from sqlalchemy import (
    BigInteger, Column, DateTime, Enum, ForeignKey, Numeric, String,
    func,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship
from typing import Optional

from db import models
from db.connection import Base
from db.custom_column_types.pydantic_json import PydanticJSON
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import (
    BotTypeLiteral, EWalletExternalPaymentStatus,
    EWalletExternalPaymentTransferData,
    EWalletExternalPaymentTransferType,
)
from utils.date_time import utcnow


class EWalletExternalPayment(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = "ewallet_external_payments"

    uuid_id: str = Column(
        String(36),
        default=lambda: str(uuid.uuid4()),
        nullable=False,
        unique=True
    )

    ewallet_id: int = Column(
        BigInteger, ForeignKey("ewallets.id", ondelete="RESTRICT"),
        nullable=False
    )
    ewallet: "models.EWallet" = relationship(
        "EWallet",
        backref="external_payments",
    )

    profile_id: int = Column(
        BigInteger, ForeignKey("groups.id", ondelete="RESTRICT"),
        nullable=False,
    )
    profile: "models.Group" = relationship(
        "Group",
        backref="ewallet_external_payments",
    )

    user_id: int = Column(
        BigInteger,
        ForeignKey("users.id", ondelete="RESTRICT"),
        nullable=False,
    )
    user: "models.User" = relationship(
        "User",
        foreign_keys=user_id,
        backref="ewallet_external_payments",
    )

    payer_id: int | None = Column(
        BigInteger,
        ForeignKey("users.id", ondelete="RESTRICT"),
        nullable=True,
    )
    payer: Optional["models.User"] = relationship(
        "User",
        foreign_keys=payer_id,
        backref="payers_ewallet_external_payments",
    )

    status: EWalletExternalPaymentStatus = Column(
        Enum(EWalletExternalPaymentStatus),
        default=EWalletExternalPaymentStatus.CREATED,
        nullable=False,
    )

    amount: Decimal = Column(Numeric(precision=12, scale=2), nullable=False)
    fee: Decimal = Column(Numeric(precision=12, scale=2), nullable=False, default=0)

    discount_percent: Decimal = Column(
        Numeric(precision=4, scale=2), nullable=False, default=0
    )
    discount_amount: Decimal = Column(
        Numeric(precision=12, scale=2), nullable=False, default=0
    )

    currency = Column(String(3), nullable=False)

    incust_transaction_id: str = Column(String(256), nullable=False, unique=True)

    transfer_type: EWalletExternalPaymentTransferType = Column(
        Enum(EWalletExternalPaymentTransferType),
        nullable=False
    )
    transfer_data: EWalletExternalPaymentTransferData = Column(
        PydanticJSON(EWalletExternalPaymentTransferData),
        nullable=False,
    )

    messanger_type: BotTypeLiteral | None = Column(String(8), nullable=True)
    message_id: str | None = Column(String(256), nullable=True)

    time_updated: datetime = Column(DateTime, default=utcnow)

    @hybrid_property
    def change_data(self):
        return self.time_updated

    @hybrid_property
    def crm_tag(self):
        return f"ewallet_ext_payment-{self.id}"

    @crm_tag.expression
    def crm_tag(self):
        return func.concat("ewallet_ext_payment-", self.id)

    @hybrid_property
    def total_amount(self):
        return self.amount + self.fee - self.discount_amount
