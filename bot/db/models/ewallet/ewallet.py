import uuid
from datetime import datetime, timezone
from decimal import Decimal
from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, ForeignKey, Numeric, SMALLINT, String,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship
from sqlalchemy_json import NestedMutableJson
from typing import List, Optional

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class EWallet(Base, BaseDBModel, TimeCreatedMixin):

    __tablename__ = 'ewallets'

    __table_args__ = (
        UniqueConstraint('incust_account_id', name='uq_incust_account_id'),
    )

    incust_account_id: str = Column(String(36), nullable=False)
    terminal_api_key: str = Column(String(255), nullable=False)
    server_api_url: str = Column(String(255), nullable=False)
    incust_terminal_id: str = Column(String(512), nullable=True, default=None)

    name = Column(String(255), nullable=True)
    description = Column(String(512), nullable=True)
    info = Column(String(1024), nullable=True)

    is_enabled = Column(Boolean, default=True)
    is_deleted = Column(Boolean, default=False)

    creator_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    creator: "models.User" = relationship("User", foreign_keys=creator_id)

    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="RESTRICT"))
    bot: "models.ClientBot" = relationship("ClientBot", foreign_keys=bot_id)

    ad_id: int | None = Column(SMALLINT, ForeignKey("ads.id", ondelete="SET NULL"))
    ad: Optional["models.Ad"] = relationship("Ad", backref="ewallets")

    update_date = Column(
        DateTime, default=datetime.now(timezone.utc),
        onupdate=datetime.now(timezone.utc)
    )

    _countries = Column(NestedMutableJson, default=None)

    currency = Column(String(3), nullable=False, default="USD")

    # Мова для перекладів
    lang = Column(String(5), nullable=False, default="en")
    _langs_list: list[str] | None = Column(NestedMutableJson, default=None)

    min_amount = Column(BigInteger, nullable=True)

    invoice_template_id = Column(
        BigInteger,
        ForeignKey("invoice_templates.id", ondelete="RESTRICT"),
        default=None,
        nullable=True,
    )

    media_id: int | None = Column(
        BigInteger, ForeignKey("media_objects.id", ondelete="SET NULL")
    )
    media: "models.MediaObject" = relationship(
        "MediaObject", foreign_keys=media_id, backref="ewallets"
    )
    is_private = Column(Boolean, default=False)
    uuid_id = Column(
        String(36),
        default=lambda: str(uuid.uuid4().hex),
        nullable=False,
        unique=True
    )

    ext_payment_fee_value: Decimal | None = Column(
        Numeric(precision=12, scale=2), nullable=True
    )
    ext_payment_fee_percent: Decimal | None = Column(
        Numeric(precision=4, scale=2), nullable=True
    )

    discount_percent: Decimal = Column(
        Numeric(precision=4, scale=2),
        nullable=False,
        default=0,
    )

    @property
    def langs_list(self):
        if not isinstance(self._langs_list, list):
            return []

        return [lang for lang in self._langs_list if lang != self.lang]

    @langs_list.setter
    def langs_list(self, value: list[str]):
        self._langs_list = value

    @property
    def countries(self):
        return self._countries or []

    @countries.setter
    def countries(self, value: list[str]):
        self._countries = value

    def get_countries(self) -> List[str]:

        countries = self._countries
        if not isinstance(countries, list):
            countries = []
        else:
            countries = [el for el in self._countries]

        countries = [el for el in countries if el]
        if self.country in countries:
            countries.remove(self.country)

        return countries
