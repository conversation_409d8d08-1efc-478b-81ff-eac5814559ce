from datetime import datetime, timezone

from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, ForeignKey,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class EWalletUser(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = 'ewallet_users'
    __table_args__ = (
        UniqueConstraint('ewallet_id', 'user_id', name='uq_ewallet_user'),
    )

    ewallet_id = Column(BigInteger, ForeignKey('ewallets.id'), nullable=False)
    ewallet: "models.EWallet" = relationship("EWallet", backref="users")
    user_id = Column(BigInteger, ForeignKey('users.id'), nullable=False)

    update_date = Column(
        DateTime, default=datetime.now(timezone.utc),
        onupdate=datetime.now(timezone.utc)
    )
    is_deleted = Column(<PERSON>olean, default=False)
