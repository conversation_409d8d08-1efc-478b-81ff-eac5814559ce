from sqlalchemy import <PERSON><PERSON>nteger, Boolean, Column, ForeignKey, JSON, String

from db.connection import Base
from db.mixins import TimeCreatedMixin
from db.mixins.base_model import BaseDBModel


class EWalletMerchant(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = "ewallet_merchants"
    
    name: str = Column(String(255), nullable=False)
    description: str | None = Column(String(1024), nullable=True)

    ewallet_id: int = Column(BigInteger, ForeignKey("ewallets.id"), nullable=False)

    qrcodes: list[str] = Column(JSON, nullable=False)

    incust_check_item_code: str | None = Column(String(255), nullable=True)
    incust_check_item_category: str | None = Column(String(255), nullable=True)
    is_pay_fee_self: bool = Column(Boolean, default=False, nullable=False)
    is_deleted: bool = Column(Boolean, default=False, nullable=False)
