import re
from datetime import datetime, timezone

from aiowhatsapp.schemas import MessageTemplateCategoryType, SendTemplateComponent
from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, ForeignKey, String,
)
from sqlalchemy.dialects.mysql import <PERSON><PERSON><PERSON> as JSONType
from sqlalchemy.orm import relationship

from core.whatsapp.callback_data import TemplateReplyCallbackData
from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class WATemplate(Base, BaseDBModel, TimeCreatedMixin):

    __tablename__ = 'wa_templates'

    template_id = Column(String(255), nullable=False, unique=True)
    template_status = Column(String(255), nullable=False)
    template_status_reason = Column(String(255), nullable=True)
    template_data = Column(JSONType, nullable=True, default=None)
    master_template_id = Column(
        BigInteger, ForeignKey("wa_master_templates.id", ondelete="RESTRICT")
    )
    master_template: "models.WAMasterTemplate" = relationship(
        "WAMasterTemplate", foreign_keys=master_template_id
    )
    lang = Column(String(10, collation="utf8mb4_unicode_ci"), nullable=True)
    is_deleted = Column(Boolean, default=False)

    update_date = Column(
        DateTime, default=datetime.now(timezone.utc),
        onupdate=datetime.now(timezone.utc)
    )

    async def get_header_media(self) -> models.MediaObject | None:
        for component in self.template_data.get("components", []):
            if component.get("type", None) == "HEADER":
                if component.get("media", None):
                    media_id = component.get("media")
                    media = await models.MediaObject.get(media_id)
                    return media
                if component.get("media_id", None):
                    media_id = component.get("media_id")
                    media = await models.MediaObject.get(media_id)
                    return media

        return None

    def get_header_handle(self) -> str | None:
        for component in self.template_data.get("components", []):
            if component.get("type", None) == "HEADER":
                example = component.get("example", None)
                if example:
                    return example.get("header_handle", None)
        return None

    def get_variables_by_component(self, component_type: str) -> list[dict]:
        variables = []
        for component in self.template_data.get("components", []):
            if component.get("type", None) == component_type and (
                    component.get("format", None) == "TEXT" or
                    component.get("type", None) == "BODY"):
                text = component.get("text", "")
                return re.findall(r"\{\{(.*?)\}\}", text)

        return variables

    def build_send_template_components(
            self, variables: list[dict] | None = None,
            header_media_url: str | None = None,
            reply_buttons_data: list[dict] | None = None,
    ) -> \
            list[SendTemplateComponent]:
        components = self.template_data.get("components", [])
        send_components = []
        for component in components:
            component_type = component.get("type")
            parameters = []
            if component_type == "BUTTONS" and reply_buttons_data:
                parameters = []
                for index, reply_button in enumerate(reply_buttons_data):
                    text = reply_button.get("text")
                    payload = reply_button.get("payload")
                    if text and payload:
                        send_template_component = SendTemplateComponent(
                            type="BUTTON",
                            index=str(index),
                            sub_type='QUICK_REPLY',
                            parameters=[{
                                "type": "payload",
                                "payload": TemplateReplyCallbackData(
                                    id=payload
                                ).to_str(),
                                "text": text,
                            }],
                        )
                        send_components.append(send_template_component)
            elif component_type == "HEADER":
                if component.get("format", "") != "TEXT":
                    if component.get("format", "") == "IMAGE":
                        component_media = component.get("media")
                        parameters.append(
                            {
                                "type": "image",
                                "image": {
                                    "link": component_media if component_media else
                                    header_media_url,
                                }
                            }
                        )
                elif variables:
                    _vars = self.get_variables_by_component(component_type)
                    first_var_values = [item for item in variables if
                                        item.get("var") == "1" and item.get(
                                            "value"
                                        ) is not None]
                    first_var_value_header = first_var_values[0][
                        'value'] if first_var_values else None

                    for var in _vars:
                        if var == "1" and first_var_value_header:
                            parameters.append(
                                {
                                    "type": "text",
                                    "text": first_var_value_header,
                                }
                            )
                        elif var != "1":
                            matching_vars = [item for item in variables if
                                             item.get("var") == var and item.get(
                                                 "value"
                                             ) is not None]
                            for var_item in matching_vars:
                                parameters.append(
                                    {
                                        "type": "text",
                                        "text": var_item.get("value"),
                                    }
                                )
            elif component_type == "BODY" and variables:
                _vars = self.get_variables_by_component(component_type)
                first_var_values = [item for item in variables if
                                    item.get("var") == "1" and item.get(
                                        "value"
                                    ) is not None]
                take_second_one_var_1_for_body = len(first_var_values) >= 2

                for var in _vars:
                    matching_vars = [item for item in variables if
                                     item.get("var") == var and item.get(
                                         "value"
                                     ) is not None]
                    if var == "1" and take_second_one_var_1_for_body and len(
                            matching_vars
                    ) >= 2:
                        parameters.append(
                            {
                                "type": "text",
                                "text": matching_vars[1].get("value"),
                            }
                        )
                    else:
                        for var_item in matching_vars:
                            parameters.append(
                                {
                                    "type": "text",
                                    "text": var_item.get("value"),
                                }
                            )

            if parameters and component_type != 'BUTTONS':
                send_template_component = SendTemplateComponent(
                    type=component_type,
                    parameters=parameters,
                )
                send_components.append(send_template_component)

        return send_components


class WAMasterTemplate(Base, BaseDBModel, TimeCreatedMixin):

    __tablename__ = 'wa_master_templates'

    name = Column(String(255), nullable=True)
    description = Column(String(512), nullable=True)

    is_deleted = Column(Boolean, default=False)

    creator_id = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    creator: "models.User" = relationship("User", foreign_keys=creator_id)

    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="RESTRICT"))
    bot: "models.ClientBot" = relationship("ClientBot", foreign_keys=bot_id)

    category: MessageTemplateCategoryType = Column(String(255), nullable=True)

    update_date = Column(
        DateTime, default=datetime.now(timezone.utc),
        onupdate=datetime.now(timezone.utc)
    )
