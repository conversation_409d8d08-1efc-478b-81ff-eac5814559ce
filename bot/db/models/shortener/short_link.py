import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, String, func
from sqlalchemy.dialects.mysql import BIGINT, SMALLINT
from sqlalchemy.ext.hybrid import hybrid_property

import schemas
from config import MAX_SHORT_LINK_ID_LENGTH, SHORT_LINKS_BASE_URL
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class ShortLink(Base, BaseDBModel, TimeCreatedMixin):
    id: uuid.UUID = Column(String(36), primary_key=True, default=uuid.uuid4)
    display_id: str = Column(
        String(
            MAX_SHORT_LINK_ID_LENGTH,
            collation="utf8mb4_bin",
        ),
        nullable=False,
    )

    type: schemas.ShortLinkType = Column(String(3), nullable=False)
    url: str | None = Column(String(2048), nullable=True)

    max_uses: int | None = Column(SMALLINT(unsigned=True), nullable=True)
    uses: int = Column(BIGINT(unsigned=True), nullable=False, default=0)

    expiration_date: datetime | None = Column(DateTime, nullable=True)

    @property
    def is_expired(self):
        return self.expiration_date and datetime.utcnow() > self.expiration_date

    @hybrid_property
    def available_uses(self):
        return self.max_uses - self.uses if self.max_uses else None

    @available_uses.expression
    def available_usages(self):
        return self.max_uses - self.uses

    @property
    def is_usage_available(self):
        return self.available_uses is None or self.available_uses > 0

    @hybrid_property
    def short_url(self):
        return f"{SHORT_LINKS_BASE_URL}/{self.display_id}"

    @short_url.expression
    def short_url(self):
        return func.concat(SHORT_LINKS_BASE_URL, "/", self.display_id)
