from datetime import datetime

from sqlalchemy import BigInteger, <PERSON>olean, Column, DateTime, ForeignKey, String
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class Customer(Base, BaseDBModel, TimeCreatedMixin):
    lang = Column(String(10, collation="utf8mb4_unicode_ci"), nullable=True)
    marketing_consent: bool | None = Column(Boolean, default=None, nullable=True)
    is_accept_agreement: bool | None = Column(Boolean, default=None, nullable=True)

    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    user: "models.User" = relationship("User", foreign_keys=user_id)

    profile_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    profile: "models.Group" = relationship("Group", foreign_keys=profile_id)

    updated_date: datetime | None = Column(DateTime, nullable=True)
