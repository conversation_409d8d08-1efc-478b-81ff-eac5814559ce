from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship, RelationshipProperty

import schemas
from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin


class UserGroup(Base, BaseDBModel, TimeCreatedMixin):
    title: str = Column(String(50), nullable=False)

    owner_type: schemas.UserGroupOwnerType = Column(String(7), nullable=False)

    owner_profile_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    owner_profile: "models.Group" = relationship("Group", backref="user_groups")

    users: list["models.User"] = relationship(
        "User",
        secondary="user_to_user_groups",
        backref="user_groups",
    )


class UserToUserGroup(Base, BaseDBModel):
    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user_group_id: int = Column(BigInteger, <PERSON><PERSON><PERSON>("user_groups.id", ondelete="CASCADE"))
