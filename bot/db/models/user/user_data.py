from typing import Any, Optional

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON>, ForeignKey, JSON, String
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.orm import relationship

import schemas
from db import models
from db.connection import Base
from db.mixins import TimeCreatedMixin
from db.mixins.base_model import BaseDBModel


class UserData(Base, BaseDBModel, TimeCreatedMixin):
    __tablename__ = "user_data"

    user_id: int = Column(
        BigInteger,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False
    )
    user: "models.User" = relationship("User", back_populates="data")

    type: str = Column(String(255), nullable=False)

    target: schemas.UserDataTarget = Column(
        Enum(schemas.UserDataTarget),
        nullable=False,
    )

    group_id: int | None = Column(
        BigInteger,
        ForeignKey("groups.id", ondelete="CASCADE"),
        nullable=True
    )
    group: Optional["models.Group"] = relationship("Group", back_populates="users_data")

    data: Any = Column(JSON(none_as_null=True), nullable=False)

    position: int = Column(INTEGER(unsigned=True), nullable=False)
