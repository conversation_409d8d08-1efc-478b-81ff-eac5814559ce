import logging
import re
from datetime import datetime, timedelta
from typing import List, Literal, Optional, Type, Union

from aiogram import types
from dateutil import tz
from passlib.handlers.pbkdf2 import pbkdf2_sha256
from pytz import utc
from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, ForeignKey, String, Text, UniqueConstraint,
    func,
)
from sqlalchemy.dialects.mysql import match
from sqlalchemy.exc import MultipleResultsFound
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

import config as cfg
import schemas
from config import (
    ANONYMOUS_IMAGE_PATH, ANONYMOUS_USER_EMAIL, DEFAULT_LANG, DEFAULT_TIME_ZONE,
    INCUST_EXTERNAL_ID_PREFIX, P4S_API_URL,
)
from db import models
from db.connection import Base
from db.decorators import db_func
from db.helpers import T, hybrid_method, sess
from db.mixins import BaseDBModel, ContextMixin
from db.models import Group
from db.models.associations import TagsUsersAssociation
from utils.date_time import utcnow
from utils.localisation import localisation
from utils.media import delete_file, save_file


# основные классы
class User(Base, ContextMixin, BaseDBModel):
    _status: str = Column(String(1), default="a")

    uuid_token = Column(String(36), nullable=True, default=None)
    date_joined = Column(DateTime(timezone=True), default=utcnow)
    birth_date = Column(DateTime, nullable=True)

    db_timezone = Column(String(63), default=cfg.DEFAULT_TIME_ZONE)
    _lang = Column(String(10, collation="utf8mb4_unicode_ci"), nullable=True)

    is_guest_user: bool = Column(Boolean, default=False)
    email: str = Column(String(320), nullable=True, unique=True)
    is_confirmed_email: bool = Column(Boolean, default=False)
    hashed_password: str = Column(Text, nullable=True)

    chat_id: int | None = Column(BigInteger, default=None, nullable=True, unique=True)
    username: str | None = Column(String(256, collation="utf8mb4_unicode_ci"))

    wa_phone: str = Column(
        String(50), default=None, nullable=True, unique=True
    )  # whatsapp phone number
    wa_name: str | None = Column(String(256), default=None, nullable=True)

    _first_name = Column(String(256, collation="utf8mb4_unicode_ci"))
    _last_name = Column(String(256, collation="utf8mb4_unicode_ci"))
    full_name = Column(String(256, collation="utf8mb4_unicode_ci"))

    photo = Column(String(1024), default=None, nullable=True)
    telegram_photo_id = Column(BigInteger, default=None, nullable=True)

    last_data_update_datetime = Column(DateTime, default=utcnow)

    is_accepted_agreement: bool = Column(Boolean, nullable=False, default=False)
    accept_agreement_datetime: datetime = Column(DateTime, nullable=True)

    is_system_user: bool = Column(Boolean, nullable=False, default=False)
    system_user_owned_by_profile_id: int | None = Column(
        BigInteger, ForeignKey("groups.id", ondelete="SET NULL"),
        nullable=True
    )
    system_user_owned_by_profile: "models.Group | None" = relationship(
        "Group",
        foreign_keys=system_user_owned_by_profile_id,
        backref="system_users",
    )

    is_auth_google: bool = Column(Boolean, default=False)
    is_auth_apple: bool = Column(Boolean, default=False)

    is_superadmin: bool = Column(Boolean, nullable=False, default=False)
    need_delete_friendly_posts = Column(Boolean, default=True)

    admin_roles = relationship("Admin", back_populates="user")
    store_orders = relationship("StoreOrder", back_populates="user")

    tags = relationship(
        "Tag",
        secondary="tags_users_associations",
        back_populates="users",
    )

    admined_channels = relationship(
        "Channel",
        secondary="admins_channels_association",
        back_populates="admins",
    )

    data: list["models.UserData"] = relationship("UserData", back_populates="user")

    STATUSES: dict[str, str] = {
        "a": "active",
        "r": "readonly",
        "d": "deactivated"
    }
    TIME_BETWEEN_UPDATES = timedelta(hours=1)

    @hybrid_property
    def name(self):
        if self.full_name:
            return self.full_name
        if self.first_name:
            if not self.last_name:
                return self.first_name
            else:
                return f'{self.first_name} {self.last_name}'
        if self.last_name:
            return self.last_name
        if self.username:
            return f"@{self.username}"
        if self.wa_name:
            return self.wa_name
        if self.email:
            return self.email
        return f"#{self.id}"

    # noinspection PyMethodParameters
    @name.expression
    def name(cls: Type["User"]):
        return func.IFNULL(
            cls.full_name,
            func.IF(
                cls._first_name.is_not(None),
                func.IF(
                    cls._last_name.is_not(None),
                    func.CONCAT(cls._first_name, " ", cls._last_name),
                    cls._first_name,
                ),
                func.IFNULL(
                    cls._last_name,
                    func.IF(
                        cls.username,
                        func.CONCAT('@', cls.username),
                        func.IFNULL(
                            cls.wa_name,
                            func.IFNULL(cls.email, func.concat("#", cls.id))
                        )
                    )
                )
            ),
        )

    def get_info(self, bot_type: schemas.BotTypeLiteral):
        parts = []

        if self.wa_name and self.wa_phone and bot_type == "whatsapp":
            parts.append(f"{self.wa_name}: {self.wa_phone}")
        elif self.full_name:
            parts.append(self.full_name)
        elif self.first_name or self.last_name:
            parts.append(
                " ".join([el for el in (self.first_name, self.last_name) if el])
            )

        if self.username and bot_type == "telegram":
            parts.append(f"@{self.username}")

        if self.email:
            parts.append(self.email)

        return "\n".join(parts)

    @property
    def is_password(self):
        return bool(self.hashed_password)

    @hybrid_property
    def is_anonymous(self):
        return self.email == ANONYMOUS_USER_EMAIL

    @property
    def client(self):
        if self.is_anonymous or self.is_only_email:
            return schemas.ClientEnum.WEB
        return schemas.ClientEnum.MESSANGER

    def verify_password(self, password: str):
        if not password or not self.hashed_password:
            return False

        return pbkdf2_sha256.verify(password, self.hashed_password)

    @property
    def is_messanger(self):
        return bool(self.wa_phone or self.chat_id)

    @property
    def messangers(self) -> list[str]:
        messangers = []
        if self.wa_phone:
            messangers.append('whatsapp')
        if self.chat_id:
            messangers.append('telegram')

        return messangers

    @property
    def is_only_email(self):
        return not self.is_messanger

    @classmethod
    def get_status_by_short(cls, shortcut: str | None):
        if not shortcut:  # TODO: temp, for testing purposes (because of existing tg
            # users not have status)
            return cls.STATUSES["a"]
        if shortcut not in cls.STATUSES:
            raise ValueError(
                f"Unknown short status: {shortcut}. available: "
                f"{list(cls.STATUSES.keys())}"
            )

        return cls.STATUSES[shortcut]

    @classmethod
    def get_short_by_status(cls, status: str):
        statuses = dict([(long, short) for short, long in cls.STATUSES.items()])
        if status not in statuses:
            raise ValueError(
                f"Unknown status: {status}. Available: {list(statuses.keys())}"
            )

        return statuses[status]

    def get_allowed_scopes(self):
        scopes = []
        status = self.get_status_by_short(self._status)
        if status in ("active", "readonly"):
            scopes.append("me:read")

        if status == "active":
            scopes.append("me:write")

        return scopes

    def is_scope_allowed(self, scope: str):
        if scope.startswith("me:"):
            return scope in self.get_allowed_scopes()
        return True

    @property
    def allowed_scopes_str(self):
        return ",".join(self.get_allowed_scopes())

    @hybrid_property
    def photo_url(self):
        photo = self.photo or ANONYMOUS_IMAGE_PATH
        return "/".join([P4S_API_URL, photo])

    @photo_url.expression
    def photo_url(self):
        return func.concat(
            f"{P4S_API_URL}/", func.IFNULL(self.photo, ANONYMOUS_IMAGE_PATH)
        )

    @hybrid_property
    def is_photo_placeholder(self):
        return self.photo is None

    @is_photo_placeholder.expression
    def is_photo_placeholder(self):
        return self.photo.is_(None)

    @property
    async def is_content_admin(self) -> bool:
        admin_bot_activity = await self.activity_in_admin_bot
        return admin_bot_activity.is_content_admin

    @classmethod
    @db_func
    def save(
            cls,
            chat_id: int | None,
            username: str | None,
            first_name: str | None,
            last_name: str | None,
            full_name: str | None,
            wa_phone: str | None,
            wa_name: str | None,
            lang: str | None,
            timezone: str | None,
    ) -> "User":
        user = User(
            chat_id=chat_id,
            username=username,
            _first_name=first_name,
            _last_name=last_name,
            full_name=full_name,
            wa_phone=wa_phone,
            wa_name=wa_name,
            _lang=lang,
            db_timezone=timezone,
        )
        sess().add(user)
        sess().commit()
        return user

    @classmethod
    async def create(
            cls,
            chat_id: int = None, username: str = None,
            first_name: str = None, last_name: str = None,
            full_name: str = None,
            wa_phone: str | None = None,
            wa_name: str | None = None,
            lang: str = None,
            timezone: str | None = None
    ) -> "User":
        user = await cls.save(
            chat_id, username, first_name, last_name, full_name, wa_phone, wa_name,
            lang, timezone
        )
        return user

    @hybrid_property
    def first_name(self):
        if self.wa_name:
            return self.wa_name.split(" ", 1)[0]
        return self._first_name or ""

    @first_name.expression
    def first_name(self):
        return self._first_name

    @first_name.setter
    def first_name(self, value: str):
        self._first_name = value

    @hybrid_property
    def last_name(self) -> str:
        if self.wa_name:
            names_list = self.wa_name.split(" ", 1)
            return names_list[1] if len(names_list) > 1 else ""
        return self._last_name or ""

    @last_name.expression
    def last_name(self):
        return self._last_name

    @last_name.setter
    def last_name(self, value: str):
        self._last_name = value

    @property
    async def last_bot(self):
        return await models.UserClientBotActivity.get_last_bot(self.chat_id)

    @property
    def user_url(self):
        return f"<a href=\"tg://user?id={self.chat_id}\">{self.full_name}</a>"

    def get_local_time(self, default_time_zone: str = None) -> datetime:
        time_zone = tz.gettz(self.get_timezone(default_time_zone))
        return utc.localize(utcnow()).astimezone(time_zone)

    @property
    def lang(self) -> str:
        return self._lang if self._lang else cfg.DEFAULT_LANG

    @property
    def is_lang(self) -> bool:
        return bool(self._lang)

    @lang.setter
    def lang(self, value):
        self._lang = value

    @classmethod
    @db_func
    def _get(cls, chat_id: int) -> Optional["User"]:
        if not chat_id:
            return None

        return sess().query(User).filter_by(chat_id=chat_id).one()

    # noinspection PyMethodOverriding
    @classmethod
    async def get(cls, chat_id: int) -> Optional["User"]:
        if not chat_id:
            return None

        current_telegram_user = types.User.get_current()
        if current_telegram_user and current_telegram_user.id == chat_id:
            return await cls.get_current()

        return await cls._get(chat_id)

    @classmethod
    @db_func
    def get_by_username(cls, username: str) -> "User":
        return sess().query(User).filter_by(username=username).one()

    @classmethod
    def get_by_id_sync(cls, id: "User | int", status: str | None = None):
        if not id:
            return None
        if isinstance(id, cls):
            if not status:
                return id
            id = id.id

        query = sess().query(User).filter_by(id=id)
        if status:
            query = query.filter(User.status == status)
        return query.one()

    @classmethod
    @db_func
    def get_by_id(cls, id: "User | int"):
        return cls.get_by_id_sync(id)

    @classmethod
    @db_func
    def get_id_by_chat_id(cls, chat_id: int) -> int:
        return sess().query(cls.id).filter(cls.chat_id == chat_id).scalar()

    @classmethod
    @db_func
    def get_by_wa_phone(cls, wa_phone: str) -> Optional["User"]:
        return sess().query(User).filter_by(wa_phone=wa_phone).one_or_none()

    @classmethod
    @db_func
    def get_by_email(cls, email: str) -> Optional["User"]:
        return sess().query(User).filter_by(email=email).one_or_none()

    @classmethod
    @db_func
    def get_chat_id_by_id(cls, id: int) -> int:
        return sess().query(cls.chat_id).filter(cls.id == id).scalar()

    @classmethod
    async def get_current(cls, no_error: bool = True) -> Optional["User"]:
        telegram_user = types.User.get_current(no_error=no_error)
        if not telegram_user:
            return

        user_chat_id = telegram_user.id

        if no_error:
            user = cls._ctx_instance.get(None)
        else:
            user = cls._ctx_instance.get()

        if user and user not in sess():
            user = None
            cls.set_current(None)

        if user and user.chat_id == user_chat_id:
            return user

        try:
            user = await cls._get(chat_id=user_chat_id)
        except MultipleResultsFound:
            logger = logging.getLogger()
            logger.critical(f"Multiple users found in get_current")
            raise
        else:
            cls.set_current(user)
            return user

    @classmethod
    def set_current(cls: Type[T], value: T | None):
        if not isinstance(value, cls | None):
            raise TypeError(
                f"Value should be instance of {cls.__name__!r} or None not "
                f"{type(value).__name__!r}"
            )
        cls._ctx_instance.set(value)

    async def get_lang(
            self: Union[int, "User"] = None,
            bot_or_id: int | None = None,
            bot_id: int | None = None,
            fix_if_incorrect: bool = True
    ) -> str | None:
        if not bot_or_id and bot_id:
            bot_or_id = bot_id

        if self is None:
            user = await User.get_current()
            if not user:
                return None
            return await User.get_lang(user, bot_or_id)

        if isinstance(bot_or_id, models.ClientBot):
            bot = bot_or_id
        else:
            bot_id = bot_or_id if bot_or_id else models.ClientBot.get_current_bot_id()
            bot = await models.ClientBot.get(bot_id)

        if bot:
            bot_group: Group = await Group.get_by_bot(bot.id)

            user = self
            if not isinstance(self, User):
                user = await User.get(self)

            user_bot_activity = await models.UserClientBotActivity.get(user, bot)
            lang = user_bot_activity.lang or self.lang
            if not fix_if_incorrect:
                return lang or DEFAULT_LANG

            if bot_group.is_translate and bot_group.allow_all_google_langs:
                from utils.translator import Translator
                langs_list = list(
                    (await Translator.get_supported_languages(DEFAULT_LANG)).keys()
                )
            else:
                langs_list = bot_group.get_langs_list()

            if not lang or (lang not in langs_list):
                await user_bot_activity.set_lang(bot_group.lang)

            return user_bot_activity.lang

        if not isinstance(self, User):
            user = await User.get(self)
            if not user:
                raise ValueError(f"User not found by {self}")

        else:
            user = self

        lang = user.lang

        if lang not in await localisation.langs:
            await user.set_lang(cfg.DEFAULT_LANG)

        return user.lang

    def get_timezone(self, default: str = None) -> str:
        return self.db_timezone or default or DEFAULT_TIME_ZONE

    @db_func
    def update(self, with_chat_id: bool = False, **kwargs) -> bool:
        is_client_bot = False
        if "is_client_bot" in kwargs:
            is_client_bot = kwargs.pop("is_client_bot")

        if "full_name" in kwargs:
            kwargs["full_name"] = kwargs.pop("full_name")
        for k, v in kwargs.items():
            if k == 'lang':
                if is_client_bot:
                    continue

            if k == "id" or (k == "chat_id" and self.chat_id and not with_chat_id):
                continue
            setattr(self, k, v)
        sess().commit()
        return True

    @property
    async def activity_in_service_bot(self) -> "models.UserServiceBotActivity":
        return await models.UserServiceBotActivity.get_or_create(self)

    @property
    async def activity_in_admin_bot(self) -> "models.UserAdminBotActivity":
        return await models.UserAdminBotActivity.get_or_create(self)

    @property
    def need_update(self) -> bool:
        return (
                self.last_data_update_datetime + User.TIME_BETWEEN_UPDATES <= utcnow()
        )

    @db_func
    def set_photo(self, photo_path: str, photo_unique_id: str) -> str | None:
        if self.telegram_photo_id == photo_unique_id:
            return

        self.photo: str

        old_file_path = self.photo
        new_file_path = save_file(photo_path)

        if not new_file_path:
            return

        self.photo = new_file_path
        self.telegram_photo_id = photo_unique_id

        sess().commit()
        delete_file(old_file_path)
        return new_file_path

    async def is_friendly_super_admin(self, bot_id: int):
        bot = await models.ClientBot.get(bot_id)
        group = bot.group
        if group.owner.id == self.id:
            return True

        admin = await models.Admin.get(self.id, group.id)
        if admin is None:
            return False

        if admin.is_superadmin_friendly_bot:
            return True
        return False

    @db_func
    def set_lang(self, lang: str) -> str:
        self._lang = lang
        sess().commit()
        return self.lang

    async def get_shown_client_bot_menu(self, bot_id: int) -> bool:
        bot = await models.ClientBot.get(bot_id)
        user_bot_activity = await models.UserClientBotActivity.get(self, bot)
        return user_bot_activity.show_full_menu

    @db_func
    def check_tag(self, tag_handle: str, group_id: int) -> bool:
        query = sess().query(models.Tag.id)
        query = query.join(
            TagsUsersAssociation, TagsUsersAssociation.tag_id == models.Tag.id
        )
        query = query.join(User, TagsUsersAssociation.telegramuser_id == User.id)
        query = query.join(models.Group, models.Tag.group_id == models.Group.id)
        query = query.filter(
            models.Tag.handle == tag_handle, User.id == self.id,
            models.Group.id == group_id
        )
        result = bool(query.scalar())
        return result

    @property
    async def activity_in_bot(self) -> "models.UserClientBotActivity":
        bot = await models.ClientBot.get_current()
        user_bot_activity = await models.UserClientBotActivity.get(self, bot)
        return user_bot_activity

    @property
    async def friendly_channel_lists_params(self) -> "models.FriendlyChatListParams":
        bot_id = models.ClientBot.get_current_bot_id()
        list_params = await models.FriendlyChatListParams.get(self.id, bot_id)
        return list_params

    async def update_friendly_channel_lists_params(
            self, channels_mode: str = None, **kwargs
    ):
        list_params = await models.FriendlyChatListParams.create_or_update(
            self, channels_mode, **kwargs
        )
        return list_params

    @hybrid_property
    def status(self) -> str:
        return self.get_status_by_short(self._status)

    @status.setter
    def status(self, value: str):
        if len(value) == 1:
            self.get_status_by_short(value)  # check if status valid
            self._status = value
        else:
            self._status = self.get_short_by_status(value)

    @status.expression
    def status(self):
        return self._status

    @hybrid_method
    def filter_by_status(
            self, status: str, operation: Literal["equal", "not_equal"] = "equal"
    ):
        if len(status) > 1:
            status = self.get_short_by_status(status)
        if operation == "equal":
            return self._status == status
        if operation == "not_equal":
            return self._status != status

    @hybrid_property
    def not_deactivated(self):
        return self._status != "d"

    @db_func
    def set_status(self, status: str) -> str:
        self.status = status
        sess().commit()
        return self.status

    @db_func
    def set_agreement(self, status: bool) -> bool:
        self.is_accepted_agreement = status
        if status:
            self.accept_agreement_datetime = utcnow()
        sess().commit()
        return self.is_accepted_agreement

    @db_func
    def add_tag(self, tag: "models.Tag") -> bool:
        if tag is None or tag in self.tags:
            return False
        self.tags.append(tag)
        sess().commit()
        return True

    @db_func
    def remove_tag(self, tag: "models.Tag") -> bool:
        if tag is None or tag not in self.tags:
            return False
        self.tags.remove(tag)
        sess().commit()
        return True

    @hybrid_method
    def search(self: Type["User"], search_text: str):
        email_pattern = re.compile(r"^([\w.-]{1,64})@([\w.-]+\.[a-zA-Z]{2,})$")
        match_email = email_pattern.match(search_text)

        if match_email:
            local_part = match_email.group(1)
            if len(local_part) < 3:
                return self.email.ilike(f"%{search_text}%")

        return match(
            self.username,
            self._first_name,
            self._last_name,
            self.full_name,
            self.wa_name,
            self.email,
            against=f'"+{search_text.strip()}"',
        ).in_boolean_mode()

    @hybrid_property
    def incust_external_id(self) -> str | None:
        if self.uuid_token:
            return INCUST_EXTERNAL_ID_PREFIX + self.uuid_token
        return None

    @incust_external_id.expression
    def incust_external_id(self):
        return func.CONCAT(INCUST_EXTERNAL_ID_PREFIX, self.uuid_token)

    @property
    def wa_link(self):
        if self.wa_phone and self.wa_name:
            return f'<a href="https://wa.me/{self.wa_phone}">{self.wa_name}</a>'
        return None

    @property
    def has_multiple_ids(self) -> bool:
        if self.email:
            if self.chat_id:
                return True
            if self.wa_phone:
                return True

        return False

    @property
    def id_if_not_anonymous(self):
        if not self.is_anonymous:
            return self.id


class UserSettings(Base):

    __tablename__ = "users_settings"

    id = Column(BigInteger, autoincrement=True, primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user = relationship("User")
    bot_id = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot = relationship("ClientBot", back_populates="users_settings")
    # шаблон имён полей для настроек нотификаций r"[a-z0-9_]+_notification"
    accept_voucher_notification = Column(Boolean, default=True)
    make_order_by_recommendation_notification = Column(Boolean, default=True)
    close_order_by_recommendation_notification = Column(Boolean, default=True)

    @db_func
    def edited_mailing_mode(self) -> List["UserGroupSettings"]:
        query = sess().query(UserGroupSettings)
        query = query.filter(UserGroupSettings.user_settings_id == self.id)
        query = query.filter(UserGroupSettings.was_changed_mailing_mode.is_(True))

        query = query.order_by(UserGroupSettings.id.desc())
        return query.all()

    @staticmethod
    @db_func
    def get(user: "models.User", bot: "models.ClientBot") -> Optional["UserSettings"]:
        if not user or not bot:
            return

        query = sess().query(UserSettings)
        query = query.filter(UserSettings.user_id == user.id)
        query = query.filter(UserSettings.bot_id == bot.id)

        user_settings = query.one_or_none()
        if user_settings:
            return user_settings

        user_settings = UserSettings(user=user, bot=bot)
        sess().add(user_settings)
        sess().commit()
        return user_settings

    @db_func
    def update(self, **kwargs) -> bool:
        for k, v in kwargs.items():
            if k in dir(self):
                setattr(self, k, v)
        if kwargs.get("timezone"):
            self.user.db_timezone = kwargs.get("timezone")
        sess().commit()
        return True


class UserGroupSettings(Base):

    __tablename__ = "users_groups_settings"

    id = Column(BigInteger, autoincrement=True, primary_key=True)
    user_settings_id = Column(
        BigInteger, ForeignKey("users_settings.id", ondelete="CASCADE")
    )
    user_settings = relationship("UserSettings")
    group_id = Column(BigInteger, ForeignKey("groups.id", ondelete="CASCADE"))
    group = relationship("Group")
    mailing_mode = Column(
        String(30), default="with_sound"
    )  # бывает with_sound, blocked
    was_changed_mailing_mode = Column(Boolean, default=False)

    __table_args__ = (
        UniqueConstraint("user_settings_id", "group_id"),
    )

    def __init__(self, user_settings: "UserSettings", group: "models.Group"):
        assert user_settings
        assert group

        self.user_settings = user_settings
        self.group = group

        super().__init__()

    @classmethod
    @db_func
    def create(
            cls, user_settings: "UserSettings", group: "models.Group"
    ) -> "UserGroupSettings":
        user_settings = cls(user_settings, group)
        sess().add(user_settings)
        sess().commit()
        return user_settings

    @classmethod
    @db_func
    def _get(cls, user_settings_id: int, group_id: int) -> "UserGroupSettings":
        query = sess().query(UserGroupSettings)
        query = query.filter(UserGroupSettings.user_settings_id == user_settings_id)
        query = query.filter(UserGroupSettings.group_id == group_id)
        return query.one()

    @classmethod
    async def get(
            cls, user: "models.User", bot: "models.ClientBot", group: "models.Group"
    ) -> "UserGroupSettings":
        user_settings = await UserSettings.get(user, bot)

        user_group_settings = await cls._get(user_settings.id, group.id)
        if not user_group_settings:
            return await cls.create(user_settings, group)

        return user_group_settings

    @staticmethod
    @db_func
    def get_by_id(user_group_settings_id: int) -> "models.UserGroupSettings":
        user_group_settings = sess().query(UserGroupSettings).filter_by(
            id=user_group_settings_id
        ).one()
        return user_group_settings

    @db_func
    def update(self, **kwargs) -> bool:
        if "mailing_mode" in kwargs and not self.was_changed_mailing_mode:
            kwargs["was_changed_mailing_mode"] = True

        for k, v in kwargs.items():
            if k in dir(self):
                setattr(self, k, v)
        sess().commit()
        return self
