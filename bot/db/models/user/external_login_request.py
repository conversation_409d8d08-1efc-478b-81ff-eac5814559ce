import uuid
from sqlalchemy import BigInteger, Column, Enum, ForeignKey, JSON, String
from sqlalchemy.orm import relationship
from typing import Any

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import AuthSourceEnum
from schemas.auth.external_login import (
    ExternalLoginRequestPurposeEnum, ExternalLoginRequestStatusEnum,
    ExternalLoginRequestTypeEnum,
)


class ExternalLoginRequest(Base, BaseDBModel, TimeCreatedMixin):
    uuid: str = Column(String(36), nullable=False, default=lambda: uuid.uuid4().hex)

    status: ExternalLoginRequestStatusEnum = Column(
        Enum(ExternalLoginRequestStatusEnum), nullable=False,
        default=ExternalLoginRequestStatusEnum.CREATED,
    )

    type: ExternalLoginRequestTypeEnum = Column(
        Enum(ExternalLoginRequestTypeEnum), nullable=False
    )
    purpose: ExternalLoginRequestPurposeEnum = Column(
        Enum(ExternalLoginRequestPurposeEnum)
    )

    auth_source: AuthSourceEnum | None = Column(
        Enum(AuthSourceEnum), nullable=True
    )
    device_info: str | None = Column(String(255), nullable=True)

    bot_id: int | None = Column(BigInteger, ForeignKey("bots.id"))
    bot: "models.ClientBot" = relationship(
        "ClientBot", backref="external_login_requests"
    )

    user_id: int | None = Column(BigInteger, ForeignKey("users.id"))
    user: "models.User | None" = relationship("User", backref="external_login_requests")

    lang: str | None = Column(String(2), nullable=True)

    extra_data: dict[str, Any] | None = Column(JSON(none_as_null=True))

    continue_url: str | None = Column(String(124), nullable=True)
