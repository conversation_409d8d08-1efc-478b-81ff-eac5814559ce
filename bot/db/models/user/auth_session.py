from datetime import datetime, timedelta

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>olean, Column, DateTime, Enum, Foreign<PERSON>ey, String, and_
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from config import AUTH_SESSION_EXPIRE
from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import AuthSourceEnum


class AuthSession(Base, BaseDBModel, TimeCreatedMixin):
    is_active: bool = Column(Boolean, default=True)

    auth_source: AuthSourceEnum = Column(Enum(AuthSourceEnum), nullable=False)
    device_info: str = Column(String(255), nullable=False)

    user_id: int = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    user: "models.User" = relationship("User", backref="auth_sessions")

    last_refresh_datetime: datetime | None = Column(DateTime, nullable=True)
    expire_datetime: datetime = Column(DateTime, nullable=False)

    push_token: str | None = Column(String(512), nullable=True, unique=True)
    push_token_update_datetime: datetime | None = Column(DateTime, nullable=True)

    last_activity: datetime | None = Column(DateTime, nullable=True)

    @classmethod
    def make_expire_datetime(cls, expire_delta: timedelta = AUTH_SESSION_EXPIRE):
        return datetime.utcnow() + expire_delta

    @hybrid_property
    def is_expired(self):
        return datetime.utcnow() > self.expire_datetime

    @hybrid_property
    def is_valid(self):
        return self.is_active and not self.is_expired

    @is_valid.expression
    def is_valid(self):
        return and_(self.is_active.is_(True), self.is_expired.is_(False))
