from __future__ import annotations

import logging
from datetime import datetime
from typing import Any, TYPE_CHECKING, Type

from sqlalchemy import (
    BigInteger, Boolean, Column, DateTime, ForeignKey, Index,
    Integer, String, UniqueConstraint,
)
from sqlalchemy.exc import IntegrityError, NoResultFound
from sqlalchemy.orm import relationship

from db import models
from db.connection import Base
from db.decorators import db_func
from db.helpers import T, sess
from db.mixins import BaseDBModel, ContextMixin
from db.my_columns import NestedMutableJson

if TYPE_CHECKING:
    from db.models import MenuInStore, User, ClientBot, VirtualManagerChat


class UserClientBotActivity(Base, BaseDBModel, ContextMixin):
    __tablename__ = "users_client_bots_activity"

    id: int = Column(BigInteger, autoincrement=True, primary_key=True)
    is_active: bool = Column(Boolean, default=True)

    user_id: int = Column(BigInteger, Foreign<PERSON>ey("users.id", ondelete="CASCADE"))
    user: User = relationship(
        "User", foreign_keys=user_id, backref="client_bot_activities"
    )

    bot_id: int = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot: ClientBot = relationship("ClientBot", back_populates="users_activities")

    recommender_id: int = Column(
        BigInteger, ForeignKey("users.id", ondelete="SET NULL")
    )
    recommender: User | None = relationship("User", foreign_keys=recommender_id)

    last_activity: datetime = Column(DateTime(timezone=True), default=datetime.utcnow)
    show_full_menu: bool = Column(Boolean, default=False)
    is_entered_bot: bot = Column(Boolean, default=False)

    active_chat_id: int | None = Column(
        BigInteger, ForeignKey("chats.id", ondelete="SET NULL")
    )
    active_chat: "models.Chat | None" = relationship(
        "Chat", backref="activities_in_chats"
    )

    liplep_receipt_lists_params: dict = Column(NestedMutableJson, default=None)

    remind_about_vm_chat_id: int = Column(
        BigInteger,
        ForeignKey("virtual_manager_chats.id", ondelete="SET NULL"),
    )
    remind_about_vm_chat: VirtualManagerChat | None = relationship(
        "VirtualManagerChat", backref="bot_activity_reminder_set",
    )

    vm_reminded_count: int = Column(Integer, default=0)
    vm_when_remind: datetime | None = Column(DateTime)

    is_click_button_webapp: bool = Column(Boolean, default=False)
    is_open_webapp: bool = Column(Boolean, default=False)

    _lang: str | None = Column(
        String(10, collation="utf8mb4_unicode_ci"), default=None, nullable=True
    )

    active_menu_in_store_id: int = Column(
        BigInteger,
        ForeignKey("menus_in_store.id", ondelete="SET NULL"),
        nullable=True, default=None,
    )
    active_menu_in_store: MenuInStore | None = relationship(
        "MenuInStore", backref="active_users",
    )

    is_accept_agreement: bool | None = Column(Boolean, default=None, nullable=True)
    accepted_agreement_date: datetime | None = Column(DateTime, nullable=True)
    answered_marketing: bool | None = Column(Boolean, default=None, nullable=True)

    __table_args__ = (
        UniqueConstraint("user_id", "bot_id"),
        Index('vm_when_remind_vm_reminded_cnt', 'vm_when_remind', 'vm_reminded_count'),
    )

    def __init__(
            self, user: User, bot: ClientBot, recommender: User | None = None, **kwargs
    ):
        assert user
        assert bot

        super().__init__(user=user, bot=bot, recommender=recommender, **kwargs)

    @classmethod
    @db_func
    def create(
            cls,
            user: User,
            bot: ClientBot,
            recommender: User | None = None,
    ) -> UserClientBotActivity:
        user_bot_activity = cls(user, bot, recommender)
        sess().add(user_bot_activity)
        sess().commit()
        return user_bot_activity

    @classmethod
    @db_func
    def _get(cls, user_id: int, bot_id: int) -> UserClientBotActivity | None:
        sess().commit()
        query = sess().query(cls)
        query = query.filter(cls.user_id == user_id)
        query = query.filter(cls.bot_id == bot_id)
        try:
            return query.one()
        except Exception as e:
            if isinstance(e, NoResultFound):
                return

            logger = logging.getLogger()
            logger.error("error in UserClientBotActivity._get")
            logger.error(e, exc_info=True)
            return

    # noinspection PyMethodOverriding
    @classmethod
    async def get(
            cls,
            user: User,
            bot: ClientBot,
            recommender: User | None = None,
            create: bool = True, *,
            get_from_context: bool = True,
            return_is_created: bool = False,
    ) -> UserClientBotActivity | None:
        if get_from_context and not return_is_created:
            current_user = await models.User.get_current()
            current_bot = await models.ClientBot.get_current()
            if current_user and current_bot \
                    and user.id == current_user.id \
                    and bot.id == current_bot.id:
                return await cls.get_current()

        if not user:
            raise ValueError("user is not specified")

        if not bot:
            raise ValueError("bot is not specified")

        user_bot_activity = await cls._get(user.id, bot.id)

        if not create:
            return user_bot_activity

        if not user_bot_activity and create:
            is_created = True
            try:
                user_bot_activity = await cls.create(user, bot, recommender)
            except IntegrityError:
                user_bot_activity = await cls._get(user.id, bot.id)

            except Exception as e:
                logging.error(e, exc_info=True)
            if user_bot_activity:
                await models.UserAnalyticAction.save_user_joined_to_bot(user, bot)
        else:
            is_created = False

        return (
            user_bot_activity, is_created) if return_is_created else user_bot_activity

    @classmethod
    async def get_current(cls, no_error: bool = True) -> UserClientBotActivity | None:
        user = await models.User.get_current(no_error)
        if not user:
            return

        client_bot = await models.ClientBot.get_current(no_error)
        if not client_bot:
            return

        if no_error:
            user_bot_activity = cls._ctx_instance.get(None)
        else:
            user_bot_activity = cls._ctx_instance.get()

        if user_bot_activity and user_bot_activity not in sess():
            user_bot_activity = None
            cls.set_current(None)

        if user_bot_activity \
                and user_bot_activity.user_id == user.id \
                and user_bot_activity.bot_id == client_bot.id:
            return user_bot_activity

        user_bot_activity = await cls.get(user, client_bot, get_from_context=False)
        cls.set_current(user_bot_activity)

        return user_bot_activity

    @classmethod
    def set_current(cls: Type[T], value: T | None):
        if not isinstance(value, cls | None):
            raise TypeError(
                f"Value should be instance of {cls.__name__!r} or None, not "
                f"{type(value).__name__!r}"
            )
        cls._ctx_instance.set(value)

    @classmethod
    @db_func
    def get_by_id(cls, user_bot_activity_id: int) -> UserClientBotActivity | None:
        user_bot_activity = sess().query(cls).filter_by(id=user_bot_activity_id).one()
        return user_bot_activity

    @db_func
    def deactivate(self):
        self.is_active = False
        sess().commit()

    @staticmethod
    @db_func
    def get_last_bot(user_chat_id: int = None) -> ClientBot | None:
        query = sess().query(models.ClientBot)
        conditions = [models.ClientBot.status == "enabled"]
        query = query.join(
            UserClientBotActivity, UserClientBotActivity.bot_id == models.ClientBot.id
        )
        if user_chat_id:
            query = query.join(
                models.User, UserClientBotActivity.user_id == models.User.id
            )
            conditions.append(models.User.chat_id == user_chat_id)
        bot = query.filter(*conditions).order_by(
            UserClientBotActivity.last_activity.desc()
        ).first()
        return bot

    @property
    def lang(self) -> str:
        return self._lang if self._lang else self.user.lang

    @lang.setter
    def lang(self, value: str):
        self._lang = value

    @property
    def is_lang(self):
        return bool(self._lang)

    @db_func
    def set_recommender(self, recommender: User):
        self.recommender = recommender
        sess().commit()
        return True

    @db_func
    def delete(self):
        sess().delete(self)
        sess().commit()
        return True

    @db_func
    def update_last_activity(self) -> bool:
        self.last_activity = datetime.utcnow()
        return True

    @db_func
    def set_menu_type(self, *, show_full_menu: bool) -> bool:
        self.show_full_menu = show_full_menu
        sess().commit()
        return True

    @db_func
    def entered_bot(self):
        self.is_entered_bot = True
        sess().commit()

    @db_func
    def set_liplep_receipt_lists_params(
            self,
            receipt_list_page: int = None,
            items_list_page: int = None,
            search_text: str = None,
    ):
        params = self.liplep_receipt_lists_params
        if params:
            receipt_list_page = params.get(
                "receipt_list_page", 0
            ) if receipt_list_page is None else receipt_list_page
            items_list_page = params.get(
                "items_list_page", 0
            ) if items_list_page is None else items_list_page
            search_text = params.get(
                "search_text", ""
            ) if search_text is None else search_text

        else:
            receipt_list_page = receipt_list_page or 0
            items_list_page = items_list_page or 0
            search_text = search_text or ""

        data = dict(
            receipt_list_page=receipt_list_page,
            items_list_page=items_list_page,
            search_text=search_text,
        )

        self.liplep_receipt_lists_params = data
        sess().commit()

    def get_liplep_receipt_lists_page(self, mode: str) -> int:
        params = self.liplep_receipt_lists_params
        if not params:
            return 0

        if mode == "receipt_list_page":
            return params["receipt_list_page"]
        elif mode == "items_list_page":
            return params["items_list_page"]
        else:
            raise ValueError(
                "Argument mode must be either receipt_list_page or items_list_page"
            )

    def get_liplep_receipt_list_search_text(self) -> str:
        params = self.liplep_receipt_lists_params
        if not params:
            return ""
        return params.get("search_text", "")

    @db_func
    def reset_vm_remind(self):
        self.remind_about_vm_chat = None
        self.vm_when_remind = None
        self.vm_reminded_count = 0
        sess().commit()

    @db_func
    def update(self, **kwargs: Any):
        for key, value in kwargs.items():
            if key not in dir(self):
                continue
            setattr(self, key, value)
        sess().commit()

    @db_func
    def set_lang(self, lang: str):
        self._lang = lang
        sess().commit()
        return True
