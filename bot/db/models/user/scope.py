from datetime import datetime
from typing import Any, Iterable, Literal

from sqlalchemy import (
    BigInteger, Column, DateTime, ForeignKey, String, and_, exists,
    func, or_, select,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import InstrumentedAttribute, aliased, relationship
from sqlalchemy.sql import label

import schemas
from db import models
from db.connection import Base
from db.helpers import hybrid_method
from db.mixins import BaseDBModel, TimeCreatedMixin
from utils.scopes_map import scope_map


class Scope(Base, BaseDBModel, TimeCreatedMixin):
    target: schemas.ScopeTarget = Column(String(10), nullable=False)

    user_id: int | None = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"))
    user: "models.User" = relationship("User", backref="scopes")

    user_group_id: int | None = Column(
        BigInteger, ForeignKey("user_groups.id", ondelete="CASCADE")
    )
    user_group: "models.UserGroup | None" = relationship("UserGroup", backref="scopes")

    scope: str = Column(String(50), nullable=False)

    profile_id: int | None = Column(
        BigInteger, ForeignKey("groups.id", ondelete="CASCADE")
    )
    profile: "models.Group | None" = relationship("Group", backref="scopes")

    bot_id: int | None = Column(BigInteger, ForeignKey("bots.id", ondelete="CASCADE"))
    bot: "models.ClientBot | None" = relationship("ClientBot", backref="scopes")

    vm_id: int | None = Column(
        BigInteger, ForeignKey("virtual_managers.id", ondelete="CASCADE")
    )
    vm: "models.VirtualManager | None" = relationship(
        "VirtualManager", backref="scopes"
    )

    store_id: int | None = Column(
        BigInteger, ForeignKey("stores.id", ondelete="CASCADE")
    )
    store: "models.Store | None" = relationship("Store", backref="scopes")

    category_id: int | None = Column(
        BigInteger, ForeignKey("store_categories.id", ondelete="CASCADE")
    )
    category: "models.StoreCategory | None" = relationship(
        "StoreCategory", backref="scopes"
    )

    product_id: int | None = Column(
        BigInteger, ForeignKey("store_products.id", ondelete="CASCADE")
    )
    product: "models.Product | None" = relationship("StoreProduct", backref="scopes")

    product_group_id: int | None = Column(
        BigInteger, ForeignKey("store_product_groups.id", ondelete="CASCADE")
    )
    product_group: "models.StoreProductGroup | None" = relationship(
        "StoreProductGroup", backref="scopes"
    )

    attribute_group_id: int | None = Column(
        BigInteger, ForeignKey("store_attribute_groups.id", ondelete="CASCADE")
    )
    attribute_group: "models.StoreAttributeGroup | None" = relationship(
        "StoreAttributeGroup", backref="scopes"
    )

    characteristic_id: int | None = Column(
        BigInteger, ForeignKey("store_characteristics.id", ondelete="CASCADE")
    )
    characteristic: "models.Characteristic | None" = relationship(
        "StoreCharacteristic", backref="scopes"
    )

    expire_datetime: datetime | None = Column(DateTime, nullable=True)

    attribute_id: int | None = Column(
        BigInteger, ForeignKey("store_attributes.id", ondelete="CASCADE")
    )
    attribute: "models.StoreAttribute | None" = relationship(
        "StoreAttribute", backref="scopes"
    )

    qr_menu_id: int | None = Column(
        BigInteger, ForeignKey("menus_in_store.id", ondelete="CASCADE")
    )
    qr_menu: "models.MenuInStore | None" = relationship("MenuInStore", backref="scopes")

    extra_fee_id: int | None = Column(
        BigInteger, ForeignKey("extra_fee_settings.id", ondelete="CASCADE")
    )
    extra_fee: "models.ExtraFeeSettings | None" = relationship(
        "ExtraFeeSettings", backref="scopes"
    )

    task_id: int | None = Column(BigInteger, ForeignKey("tasks.id", ondelete="CASCADE"))
    task: "models.Task | None" = relationship("Task", backref="scopes")

    OBJECT_ID_FIELDS = (
        "profile_id",
        "bot_id",
        "vm_id",
        "store_id",
        "product_id",
        "category_id",
        "attribute_id",
        "characteristic_id",
        "qr_menu_id",
        "extra_fee_id",
        "task_id",
    )

    @classmethod
    @hybrid_method
    def filter_by_target(
            cls, or_expressions: Iterable[Any],
            target: schemas.ScopeTarget,
            target_id: Any,
            with_groups: bool = True,
            expire_check_datetime: datetime | None = ...,
            exists_or_conditions: Literal["exists", "conditions"] = "exists",
    ):
        """
        Method to filter by target.
        @param or_expressions: Or expressions for a filter
        @param target: Target object.
        @param target_id: Target's id
        @param with_groups: Include UserGroup scopes.
        Only for target user
        @param expire_check_datetime: datetime to check expire more than.
        If None, current datetime will be used to.
        @param exists_or_conditions:  Execute "exists" query or return conditions(
        default exists).
        @return: Expression
        """
        if expire_check_datetime is ...:
            expire_check_datetime = datetime.utcnow()

        conditions = [
            or_(*or_expressions),
        ]

        target_conditions = and_(
            getattr(cls, f"{target}_id") == target_id,
            cls.target == target,
        )

        if with_groups and target == "user":
            user_to_user_group_aliased = aliased(models.UserToUserGroup)

            correlate_clauses = [cls]
            if isinstance(target_id, InstrumentedAttribute) and target_id.class_:
                correlate_clauses.append(target_id.class_)

            user_group_id_exists = exists(1).select_from(
                user_to_user_group_aliased
            ).where(
                user_to_user_group_aliased.user_group_id == cls.user_group_id,
                user_to_user_group_aliased.user_id == target_id
            ).correlate(*correlate_clauses)

            conditions.append(
                or_(
                    target_conditions,
                    and_(
                        cls.target == "user_group",
                        user_group_id_exists,
                    )
                )
            )
        else:
            conditions.append(target_conditions)

        if expire_check_datetime is None:
            conditions.append(cls.expire_datetime.is_(None))
        else:
            conditions.append(
                or_(
                    cls.expire_datetime.is_(None),
                    cls.expire_datetime > expire_check_datetime
                )
            )

        if exists_or_conditions == "exists":
            return select(1).select_from(cls).where(*conditions).exists()

        return and_(*conditions)

    @classmethod
    @hybrid_method
    def labeled_scope_allowed(
            cls,
            scope_name: str,
            object_name: str,
            target: schemas.ScopeTarget,
            target_id: Any,
            available_data: dict[str, Any],
    ):
        return label(
            f"{scope_name}_allowed", Scope.filter_for_action(
                f"{object_name}:{scope_name}", target, target_id,
                available_data=available_data,
            )
        )

    @classmethod
    @hybrid_method
    def allowed_scopes_list(
            cls, *scope_names: str,
            object_name: str,
            target: schemas.ScopeTarget,
            target_id: Any,
            available_data: dict[str, Any],
    ):
        return [
            cls.labeled_scope_allowed(
                scope_name, object_name,
                target, target_id,
                available_data,
            )
            for scope_name in scope_names
        ]

    @classmethod
    @hybrid_method
    def filter_for_action(
            cls,
            action: str,
            target: schemas.ScopeTarget,
            target_id: Any,
            available_data: dict[str, Any] | None = None,
            with_groups: bool = True,
            expire_check_datetime: datetime | None = ...,
            exclude_scopes: Iterable[str] | None = None,
            exists_or_conditions: Literal["exists", "conditions"] = "exists",
    ):
        """
        Method to filter by scopes for an action.
        @param action: Action from ScopesMap
        @param target: Target object.
        @param target_id: Target's id
        @param available_data:
        Data available for scopes.
        Available scopes will be filtered by existing required fields in data.
        @param with_groups: Include UserGroup scopes.
        Only for target user
        @param expire_check_datetime: datetime to check expire more than.
        If ..., current datetime will be used to.
        @param exclude_scopes: Scopes to exclude from allowed in this filter
        @param exists_or_conditions:  Execute "exists" query or return conditions(
        default exists).
        @return: Expression
        """
        if not available_data:
            available_data = {}

        if exclude_scopes is None:
            exclude_scopes = ()

        action_scopes = scope_map.minified_action_scopes(action, available_data.keys())

        expressions = []
        for scopes, required_fields in action_scopes:
            current_expressions = []

            for field in required_fields:
                if field in dir(cls):
                    current_expressions.append(
                        getattr(cls, field) == available_data[field]
                    )

            expressions.append(
                and_(
                    *current_expressions,
                    Scope.scope.in_(
                        [
                            scope for scope in scopes
                            if scope not in exclude_scopes
                        ]
                    ),
                )
            )

        return cls.filter_by_target(
            expressions,
            target, target_id, with_groups,
            expire_check_datetime,
            exists_or_conditions,
        )

    @classmethod
    @hybrid_method
    def filter_for_object(
            cls,
            object_name: str,
            object_id: Any,
            target: schemas.ScopeTarget,
            target_id: Any,
            with_groups: bool = True,
            expire_check_datetime: datetime | None = ...,
            include_scopes: Iterable[str] | None = None,
            exclude_scopes: Iterable[str] | None = None,
            exists_or_conditions: Literal["exists", "conditions"] = "exists",
    ):
        """
        Method to filter by scopes for an action.
        @param object_name: Object's name.
        @param object_id:  Object's id.
        @param target: Target object.
        @param target_id: Target's id
        @param with_groups: Include UserGroup scopes.
        Only for target user
        @param expire_check_datetime: datetime to check expire more than.
        If None, current datetime will be used to.
        @param include_scopes: Allowed scope list
        @param exclude_scopes: Scopes to exclude from allowed in this filter
        @param exists_or_conditions:  Execute "exists" query or return conditions(
        default exists).
        @return: Expression
        """
        if all((include_scopes, exclude_scopes)):
            raise ValueError(
                "Only one of include_scopes or exclude_scopes can be specified"
            )

        conditions = [
            getattr(cls, f"{object_name}_id") == object_id
        ]
        if exclude_scopes:
            conditions.append(cls.scope.not_in(exclude_scopes))
        elif include_scopes:
            conditions.append(cls.scope.in_(include_scopes))

        return cls.filter_by_target(
            [and_(*conditions)],
            target, target_id, with_groups, expire_check_datetime, exists_or_conditions
        )

    @hybrid_property
    def is_expired(self):
        return (self.expire_datetime is not None and self.expire_datetime <=
                datetime.utcnow())

    @is_expired.expression
    def is_expired(self):
        return and_(
            Scope.expire_datetime.is_not(None),
            Scope.expire_datetime <= func.utc_timestamp()
        )
