import config as cfg

from datetime import datetime

from sqlalchemy import <PERSON>um<PERSON>, BigInteger, DateTime, ForeignKey, Boolean, Integer
from sqlalchemy.orm import relationship, backref

from db import models
from db.mixins import BaseDBModel
from db.my_columns import NestedMutableJson
from db.connection import Base
from db.helpers import sess
from db.decorators import db_func


class UserServiceBotActivity(Base, BaseDBModel):
    __tablename__ = "users_service_bots_activity"

    id = Column(BigInteger, autoincrement=True, primary_key=True)
    is_active = Column(Boolean, default=True)

    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), unique=True)
    user = relationship("User", foreign_keys=user_id, backref=backref("service_bot_activity", uselist=False))

    pagination = Column(Integer, default=cfg.HOW_MANY_SCROLLS)
    show_full_menu = Column(<PERSON>olean, default=False)

    filters = Column(NestedMutableJson)
    friendly_filters = Column(NestedMutableJson)

    last_activity = Column(DateTime(timezone=True), default=datetime.utcnow)
    last_reset_state = Column(DateTime(timezone=True))
    last_sent_crm_notification_datetime = Column(DateTime(timezone=True))

    need_messages_notifications = Column(Boolean, default=True)
    need_orders_notifications = Column(Boolean, default=True)
    need_vm_notify_notifications = Column(Boolean, default=True)

    def __init__(self, user: "models.User"):
        assert user
        self.user = user

        super().__init__()

    @classmethod
    @db_func
    def create(cls, user: "models.User") -> "UserServiceBotActivity":
        user_service_bot_activity = cls(user)
        sess().add(user_service_bot_activity)
        sess().commit()
        return user_service_bot_activity

    @classmethod
    @db_func
    def get(cls, user_id: int) -> "UserServiceBotActivity":
        return sess().query(cls).filter_by(user_id=user_id).one_or_none()

    @classmethod
    async def get_or_create(cls, user: "models.User", return_is_created: bool = False) -> "UserServiceBotActivity":
        user_service_bot_activity = await cls.get(user.id)
        if user_service_bot_activity:
            is_created = False
        else:
            is_created = True
            user_service_bot_activity = await cls.create(user)
        return (user_service_bot_activity, is_created) if return_is_created else user_service_bot_activity

    @db_func
    def update_last_activity(self):
        self.last_activity = datetime.utcnow()
        sess().commit()

    @db_func
    def update(self, data: dict = None, **kwargs):
        if data:
            data.update(**kwargs)
        else:
            data = kwargs
        for key, value in data.items():
            if key in dir(self):
                setattr(self, key, value)
        sess().commit()

    @db_func
    def crm_notification_sent(self):
        self.last_sent_crm_notification_datetime = datetime.utcnow()
        sess().commit()

    @db_func
    def deactivate(self):
        self.is_active = False
        sess().commit()
