from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship

from db import db_func, sess, models
from db.connection import Base


class ReceiptItemToTag(Base):
    __tablename__ = "receipt_items_to_tags"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    receipt_item_id = Column(BigInteger, ForeignKey("receipt_items.id"))
    tag_id = Column(BigInteger, ForeignKey("receipt_item_tags.id"))

    def __init__(self, receipt_item: "models.ReceiptItem", tag: "ReceiptItemTag"):
        assert receipt_item
        assert tag

        self.receipt_item = receipt_item
        self.tag = tag


class ReceiptItemTag(Base):
    __tablename__ = "receipt_item_tags"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(50), unique=True, default=None)

    receipt_items = relationship(
        "ReceiptItem",
        secondary="receipt_items_to_tags",
        back_populates="tags",
    )

    def __init__(self, name: str):
        self.name = name

    @classmethod
    @db_func
    def create(cls, tag_name: str):
        item_tag = cls(tag_name)
        sess().add(item_tag)
        sess().commit()
        return item_tag

    @classmethod
    @db_func
    def get(cls, id: int):
        tag = sess().query(cls).filter_by(id=id).one()
        return tag

    @classmethod
    @db_func
    def get_by_name(cls, tag_name: str):
        tag = sess().query(cls).filter_by(name=tag_name).one()
        return tag
