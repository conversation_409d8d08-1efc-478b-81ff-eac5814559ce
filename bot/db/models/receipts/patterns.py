from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, String, Text

from db import sess, db_func
from db.connection import Base
from datetime import datetime

import re


class ReceiptQrPattern(Base):
    __tablename__ = "receipt_qr_pattern"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    pattern = Column(String(256))
    receipt_id_re = Column(String(256))
    receipt_ppo_re = Column(String(256))
    receipt_date_re = Column(String(256))
    receipt_date_format = Column(String(256))
    description = Column(Text)

    @staticmethod
    @db_func
    def get_pattern(qr_data):
        patterns = sess().query(ReceiptQrPattern).all()
        for pattern in patterns:
            if re.search(pattern.pattern, qr_data):
                return pattern
        return None

    def get_pattern_data(self, qr_data):
        result = {}

        if self.receipt_id_re:
            result["id"] = re.search(self.receipt_id_re, qr_data).group()
        if self.receipt_ppo_re:
            result["fn"] = re.search(self.receipt_ppo_re, qr_data).group()
        if self.receipt_date_re:
            date_string = re.search(self.receipt_date_re, qr_data).group()
            date = datetime.strptime(date_string, self.receipt_date_format)
            result["date"] = str(date)
        return result
