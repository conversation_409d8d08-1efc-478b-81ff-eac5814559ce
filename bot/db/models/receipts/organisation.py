import json

from sqlalchemy import Column, BigInteger, String, UniqueConstraint, func
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from db import db_func, models, sess
from db.connection import Base

from utils.text import f


class Organisation(Base):
    __tablename__ = "organisations"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    country = Column(String(256))
    ico_code = Column(BigInteger)
    _name = Column(String(256))
    municipality = Column(String(256))
    postal_code = Column(BigInteger)
    street = Column(String(256))
    brand = Column(String(256))
    inn = Column(String(256))

    __table_args__ = (
        UniqueConstraint("country", "ico_code"),
    )

    def __init__(
            self,
            name: str,
            country: str,
            ico_code: int,
            municipality: str,
            postal_code: int,
            street: str,
            inn: str = None
    ):
        self.name = name
        self.country = country
        self.ico_code = ico_code
        self.municipality = municipality
        self.postal_code = postal_code
        self.street = street
        self.inn = inn

    @classmethod
    @db_func
    def create_by_json(cls, organisation_json_data: dict):
        params = {
            "country": organisation_json_data["country"],
            "ico_code": organisation_json_data["ico"],
            "name": organisation_json_data["name"],
            "municipality": organisation_json_data["municipality"],
            "postal_code": organisation_json_data["postalCode"],
            "street": organisation_json_data["streetName"],
            "inn": None if "inn" not in organisation_json_data else organisation_json_data["inn"]
        }

        organisation = cls(**params)
        sess().add(organisation)
        sess().commit()
        return organisation

    @classmethod
    @db_func
    def get_by_id(cls, org_id: int) -> "Organisation":
        organisation = sess().query(cls).filter(cls.id == org_id).one()
        return organisation

    @hybrid_property
    def name(self) -> str:
        if self.brand:
            return self.brand
        return self._name

    @name.expression
    def name(self):
        return func.IF(self.brand.is_not(None), self.brand, self._name)

    @name.setter
    def name(self, value: str):
        self._name = value

    @name.getter
    def name(self):
        if self.brand:
            return self.brand
        return self._name

    @classmethod
    @db_func
    def get(cls, ico_code: str, country: str):
        query = sess().query(cls)
        organisation = query.filter(cls.country == country, cls.ico_code == ico_code).one()
        return organisation

    @classmethod
    @db_func
    def get_ua(cls, country: str, *, inn: str = None, name: str = None):
        if inn is not None and name is not None:
            raise ValueError

        query = sess().query(cls)
        organisation = query.filter(cls.country == country)

        if inn:
            organisation = query.filter(cls.inn == inn)
        elif name:
            organisation = query.filter(cls._name == name)

        return organisation.one()

    async def get_info_str(self, lang: str):
        list_params = [self.country, self.municipality, self.postal_code, self.street]
        list_params = [str(x) for x in list_params if x]
        name = self.name if self.name else await f("receipt name text default")
        organisation_info = f"{name}\n{', '.join(list_params)}"
        text = await f("organisation view", lang=lang, organisation_info=organisation_info)
        return text
