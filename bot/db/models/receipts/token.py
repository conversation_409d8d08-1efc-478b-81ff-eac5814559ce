from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, String, DateTime, or_
from db import sess, db_func
from db.connection import Base
import datetime
import logging

class UaApiToken(Base):
    __tablename__ = "ua_api_token"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    token = Column(String(256))
    requests_count = Column(BigInteger)
    last_use = Column(DateTime(timezone=True), default=datetime.datetime.utcnow)

    @classmethod
    @db_func
    def get_token(cls):
        now = datetime.datetime.utcnow()

        query = sess().query(UaApiToken)
        query = query.filter(
            or_(
                UaApiToken.requests_count < 950,
                UaApiToken.last_use < (now - datetime.timedelta(days=1))
            )
        )
        query = query.order_by(UaApiToken.last_use)
        query = query.limit(1)
        token = query.one()

        if token.last_use < now - datetime.timedelta(days=1):
            token.requests_count = 1
        else:
            token.requests_count += 1

        token.last_use = now
        sess().commit()

        return token.token
