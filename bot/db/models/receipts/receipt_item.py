from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, Float, Foreign<PERSON>ey, String
from sqlalchemy.orm import relationship

import config as cfg
from db import db_func, models, sess
from db.connection import Base
from utils.numbers import format_currency
from utils.text import f


class ReceiptItem(Base):
    __tablename__ = "receipt_items"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(50))
    quantity = Column(Float, default=0.0)
    price = Column(Float, default=0.0)
    product_code = Column(String(255), default=None, nullable=True)

    receipt_id = Column(BigInteger, ForeignKey("receipts.id"))
    receipt = relationship("Receipt", foreign_keys=receipt_id, back_populates="items")

    tags = relationship(
        "ReceiptItemTag",
        secondary="receipt_items_to_tags",
        back_populates="receipt_items",
    )

    @classmethod
    @db_func
    def get(cls, item_id: int):
        item = sess().query(cls).filter(cls.id == item_id).one()
        return item

    @classmethod
    @db_func
    def create_items_by_list(cls, receipt_id: int, items_json_data: dict):
        for item_json in items_json_data:
            name = ' '.join(item_json["name"].split())
            data_item = dict(
                name=name,
                quantity=item_json["quantity"],
                price=item_json["price"],
                receipt_id=receipt_id,
            )
            item = cls(**data_item)
            sess().add(item)
        sess().commit()

    async def get_info_str(self, lang):
        country = self.receipt.organisation.country if self.receipt.organisation else "Ukraine"
        currency = cfg.COUNTRY_CURRENCY[country]
        quantity = int(self.quantity) if self.quantity % 1 == 0 else self.quantity
        price = format_currency(self.price, currency, locale=lang)
        total_price = round(self.price * quantity, 2)
        total_price = format_currency(total_price, currency, locale=lang)
        text = await f("receipt item view", lang=lang, name=self.name, count=quantity, price=price, total=total_price)
        return text

    @db_func
    def append_tag(self, tag: "models.ReceiptItemTag"):
        if tag in self.tags:
            return False
        self.tags.append(tag)
        sess().commit()
        return True

    @db_func
    def remove_tag(self, tag: "models.ReceiptItemTag"):
        if tag not in self.tags:
            return False
        self.tags.remove(tag)
        sess().commit()
        return True

    async def add_tag(self, tag_name: str) -> bool:
        item_tag = await models.ReceiptItemTag.get_by_name(tag_name)
        if item_tag is None:
            item_tag = await models.ReceiptItemTag.create(tag_name)
        return await self.append_tag(item_tag)

    async def del_tag(self, tag_id: int):
        tag = await models.ReceiptItemTag.get(tag_id)
        if not tag:
            return False
        return await self.remove_tag(tag)

    def get_tags_list_str(self):
        return ", ".join(map(lambda x: x.name, self.tags))
