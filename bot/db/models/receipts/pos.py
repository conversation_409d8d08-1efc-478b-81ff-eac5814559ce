from sqlalchemy import BigInteger, Column, Foreign<PERSON>ey, String, UniqueConstraint

from db import sess, db_func
from db.connection import Base
from utils.text import f


class Pos(Base):
    __tablename__ = "pos"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    country = Column(String(50))
    name = Column(String(50))
    municipality = Column(String(50))
    postal_code = Column(BigInteger)
    street = Column(String(150))
    cash_register_code = Column(BigInteger)
    organisation_id = Column(BigInteger, ForeignKey("organisations.id"))

    __table_args__ = (
        UniqueConstraint("country", "name", "municipality", "postal_code", "street"),
    )

    @staticmethod
    @db_func
    def get(json_data: dict) -> "Pos":
        query = sess().query(Pos)
        query = query.filter(Pos.country == json_data["country"])
        query = query.filter(Pos.name == json_data["name"])
        query = query.filter(Pos.municipality == json_data["municipality"])
        query = query.filter(Pos.postal_code == json_data["postalCode"])
        query = query.filter(Pos.street == json_data["streetName"])
        query = query.filter(Pos.cash_register_code == json_data["cashRegisterCode"])
        return query.one()

    @classmethod
    @db_func
    def create_by_json(cls, json_data: dict, organisation_id: int):
        params = {
            "country": json_data["country"],
            "name": json_data["name"],
            "street": json_data["streetName"],
            "municipality": json_data["municipality"],
            "postal_code": json_data["postalCode"],
            "organisation_id": organisation_id,
            "cash_register_code": json_data["cashRegisterCode"],
        }
        pos = Pos(**params)
        sess().add(pos)
        sess().commit()
        return pos

    async def get_info_str(self, lang: str):
        list_params = [self.country, self.municipality, self.postal_code, self.street]
        list_params = [str(x) for x in list_params if x]
        name = self.name if self.name else await f("receipt name text default")
        pos_info = f"{name}\n{', '.join(list_params)}"
        text = await f("pos view", lang=lang, post_info=pos_info)
        return text
