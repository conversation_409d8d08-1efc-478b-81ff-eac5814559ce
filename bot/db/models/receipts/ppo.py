from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, String, DateTime
from datetime import datetime

from db import sess, db_func
from db.connection import Base


class Ppo(Base):
    __tablename__ = "ppo"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    ppo_id = Column(String(256))
    name = Column(String(256))
    inn = Column(String(256))
    date_of_ppo_registration = Column(DateTime(timezone=True), default=datetime.utcnow)
    scope = Column(String(256))
    number_of_the_last_book_of_opo = Column(String(256))
    date_of_registration_of_the_last_opo_book = Column(DateTime(timezone=True), default=datetime.utcnow)
    date_of_cancellation_of_ppo_registration = Column(DateTime(timezone=True), default=datetime.utcnow)
    address = Column(String(256))

    def __init__(
            self,
            ppo_id: str,
            name: str,
            inn: str,
            date_of_ppo_registration: datetime,
            scope: str,
            number_of_the_last_book_of_opo: datetime,
            date_of_cancellation_of_ppo_registration: datetime,
            address: str,
    ):
        self.ppo_id = ppo_id
        self.name = name
        self.inn = inn
        self.date_of_ppo_registration = date_of_ppo_registration
        self.scope = scope
        self.number_of_the_last_book_of_opo = number_of_the_last_book_of_opo
        self.date_of_cancellation_of_ppo_registration = date_of_cancellation_of_ppo_registration
        self.address = address

    @classmethod
    @db_func
    def create(
            cls,
            ppo_id: str,
            name: str,
            inn: str,
            date_of_ppo_registration: datetime,
            scope: str,
            number_of_the_last_book_of_opo: datetime,
            date_of_cancellation_of_ppo_registration: datetime,
            address: str,
    ):
        ppo = cls(
            ppo_id,
            name,
            inn,
            date_of_ppo_registration,
            scope,
            number_of_the_last_book_of_opo,
            date_of_cancellation_of_ppo_registration,
            address
        )
        sess().add(ppo)
        sess().commit()
        return ppo

    @classmethod
    @db_func
    def get(cls, ppo_id: str):
        ppo = sess().query(cls).filter_by(ppo_id=ppo_id).one()
        return ppo