from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, Date<PERSON><PERSON>, <PERSON><PERSON>, ForeignKey, String
from sqlalchemy.orm import backref, relationship
from sqlalchemy.dialects.mysql import JSON as JSONType

from db import models
from db.connection import Base
from db.mixins import BaseDBModel, TimeCreatedMixin
from schemas import TaskTypeTaskEnum, TaskTypeEnum, TaskStatusEnum, TaskAiModelTypeEnum


class Task(Base, BaseDBModel, TimeCreatedMixin):
    type: TaskTypeEnum = Column(
        Enum(TaskTypeEnum), nullable=False,
    )
    type_task: TaskTypeTaskEnum = Column(
        Enum(TaskTypeTaskEnum), nullable=False,
    )
    status: TaskStatusEnum = Column(
        Enum(TaskStatusEnum), nullable=False,
        default=TaskStatusEnum.PENDING
    )

    ai_model: TaskAiModelTypeEnum = Column(
        Enum(TaskAiModelTypeEnum), nullable=False,
        default=TaskAiModelTypeEnum.DALLE2
    )

    prompt: str = Column(String(4096), nullable=False)
    dalle_prompt: str = Column(String(1024), nullable=True, default=None)

    group_id: int = Column(BigInteger, ForeignKey("groups.id", ondelete="RESTRICT"), nullable=False)
    group: "models.Group" = relationship(
        "Group",
        backref=backref("related_task"),
        foreign_keys=group_id
    )

    user_id: int | None = Column(BigInteger, ForeignKey("users.id", ondelete="RESTRICT"))
    user: "models.User | None" = relationship("User", backref="tasks")

    object_id: int = Column(BigInteger, nullable=False)

    change_date: datetime = Column(DateTime, nullable=False, default=datetime.utcnow)

    is_deleted: bool = Column(Boolean, nullable=False, default=False)

    json_data: dict = Column(JSONType, nullable=True, default=None)

    start_date: datetime = Column(DateTime, nullable=True, default=None)
    end_date: datetime = Column(DateTime, nullable=True, default=None)
    cancel_date: datetime = Column(DateTime, nullable=True, default=None)

    error: str = Column(String(1024), nullable=True, default=None)

