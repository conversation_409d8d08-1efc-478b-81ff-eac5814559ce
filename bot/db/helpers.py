from contextvars import ContextVar, Token
from typing import TypeVar

from sqlalchemy import distinct, func
from sqlalchemy.dialects import mysql
from sqlalchemy.exc import (
    IntegrityError, InterfaceError, InvalidRequestError, OperationalError,
    StatementError,
)
from sqlalchemy.ext.hybrid import hybrid_method as __hybrid_method
from sqlalchemy.orm import Query, Session
from sqlalchemy.orm.exc import DetachedInstanceError

from utils.type_vars import FuncT
from .connection import session_maker

ERRORS_FOR_RECONNECT = (
    OperationalError, StatementError, InterfaceError, IntegrityError)

ERRORS_CATCHING_BY_DB_ERROR = (
    OperationalError, StatementError, InterfaceError,
    IntegrityError, InvalidRequestError, DetachedInstanceError
)


class DBSession:

    _ctx_session = ContextVar("sa_session")
    session_maker = session_maker

    def __init__(self):
        self.token = None

    @classmethod
    def create_session(cls):
        session = cls.session_maker()
        return cls._ctx_session.set(session)

    @classmethod
    def close_session(cls, token: Token | None = None):
        session = cls.get_session()
        cls._ctx_session.reset(token)
        try:
            session.close()
        except:
            session.invalidate()

    def __enter__(self):
        token = self.create_session()
        self.token = token
        return self.get_session()

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close_session(self.token)

    @classmethod
    def get_session(cls) -> Session:
        return cls._ctx_session.get(None)


sess = DBSession.get_session

T = TypeVar("T")


def get_query_by_operation(obj, operation: str):
    if operation == "count":
        query = sess().query(func.count(distinct(obj.id)))
    elif operation == "exists":
        query = sess().query(obj.id)
    else:
        query = sess().query(obj).distinct()

    return query


def order_by_slice_and_result(
        query: Query, position: int | None = None, limit: int | None = None,
        order_by = None, operation: str = "list"
):
    if operation == "count":
        result = query.scalar()
        if position:
            result -= position
        return result

    if isinstance(order_by, tuple):
        query = query.order_by(*order_by)
    elif order_by is not None:
        query = query.order_by(order_by)

    if position:
        query = query.offset(position)
    if limit:
        query = query.limit(limit)

    if operation == "exists":
        return sess().query(query.exists()).scalar()

    return query.all()


def hybrid_method(method: FuncT, expression = None) -> FuncT:
    return __hybrid_method(method, expression)


def query_to_str(query: Query):
    return statement_to_str(query.statement)


def print_query(query: Query):
    print(query_to_str(query))


def statement_to_str(statement):
    return statement.compile(
        dialect=mysql.dialect(),
        compile_kwargs={"literal_binds": True}
    )
