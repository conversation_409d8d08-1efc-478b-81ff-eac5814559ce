import logging
from contextvars import ContextVar

from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker

import config as cfg
from config import PROCESSES_MAX_OVERFLOW_LIMITS, PROCESSES_POOL_SIZE_LIMITS
from utils.helpers import get_running_file_name

running_file_name = get_running_file_name()

pool_size = PROCESSES_POOL_SIZE_LIMITS.get(
    running_file_name, PROCESSES_POOL_SIZE_LIMITS.get("default")
)
max_overflow = PROCESSES_MAX_OVERFLOW_LIMITS.get(
    running_file_name, PROCESSES_MAX_OVERFLOW_LIMITS.get("default")
)

logger = logging.getLogger("debugger")
logger.debug(
    f"Connecting db for: {running_file_name}. {pool_size = }, {max_overflow = }"
)

engine = create_engine(
    cfg.DB_URL,
    encoding="utf8",
    echo=cfg.DEBUG_SQLALCHEMY,
    pool_size=pool_size,
    max_overflow=max_overflow,
    pool_pre_ping=True,
)

ctx_session = ContextVar("sa_session")

session_maker = sessionmaker(bind=engine, autoflush=False, expire_on_commit=False)

Base = declarative_base(bind=engine)
