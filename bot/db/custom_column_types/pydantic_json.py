import json
from typing import Optional, Type, Union

from pydantic import BaseModel
from sqlalchemy.types import JSON, TypeDecorator


class BasePydanticJSON(TypeDecorator):
    impl = JSON(none_as_null=True)
    cache_ok = True

    def __init__(self, pydantic_model: Type[BaseModel], *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.pydantic_model = pydantic_model

    def copy(self, **kwargs):
        return self.__class__(self.pydantic_model)


class PydanticJSON(BasePydanticJSON):
    def process_bind_param(self, value: Optional[Union[BaseModel, dict]], dialect):
        if value is None:
            return value
        if isinstance(value, dict):
            value = self.pydantic_model(**value)
        # this is used instead of value.dict() to convert enums to string
        return json.loads(value.json())

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        return self.pydantic_model.parse_obj(value)


class PydanticListJSON(BasePydanticJSON):
    def process_bind_param(self, value: list[BaseModel | dict] | None, dialect):
        if value is None:
            return value

        value = [
            # this is used instead of value.dict() to convert enums to string
            json.loads(
                (
                    self.pydantic_model(**el)
                    if isinstance(el, dict) else el
                ).json()
            )
            for el in value
        ]
        return value

    def process_result_value(self, value, dialect):
        if value is None:
            return value

        value_list = json.loads(value) if isinstance(value, str) else value
        return [self.pydantic_model(**el) for el in value_list]
