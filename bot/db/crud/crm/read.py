from sqlalchemy import and_, desc, literal_column, or_, select, text, union_all

import schemas
from db import db_func, sess
from db.models import Brand, CRMTicket, Group, Scope, Store
from db.types.operation import Operation
from utils.scopes_map import crm


@db_func
def get_crm_filters(
        user_id: int,
        params: schemas.CRMFilterParams | None = None,
        operation: Operation = "list",
        cursor: schemas.CRMFiltersCursor | None = None,
):
    if operation == "count":
        raise ValueError("count operation not supported")

    if not cursor:
        cursor = params.cursor

    statements = []

    profile_stmt = select(
        literal_column(f"'{schemas.CRMFilterTypeEnum.PROFILE.value}'").label("type"),
        Group.id.label("profile_id"),
        Group.name.label("profile_name"),
        literal_column("NULL").label("store_id"),
        literal_column("NULL").label("store_name"),
        literal_column("NULL").label("ticket_id"),
        literal_column("NULL").label("ticket_title"),
    ).select_from(Group)
    profile_stmt = profile_stmt.where(
        Group.status == "enabled",
        Scope.filter_for_object(
            "profile", Group.id,
            "user", user_id,
            include_scopes=list(map(lambda x: x.scope, crm.scopes))
        )
    )
    if params and params.search_text:
        profile_stmt = profile_stmt.outerjoin(Brand, Brand.group_id == Group.id)
        profile_stmt = profile_stmt.outerjoin(Store, Store.brand_id == Brand.id)
        profile_stmt = profile_stmt.outerjoin(
            CRMTicket, CRMTicket.group_id == Group.id
        )
        profile_stmt = profile_stmt.where(
            or_(
                Group.name.contains(params.search_text.strip()),
                and_(
                    Store.name.contains(params.search_text.strip()),
                    Store.is_deleted.is_(False),
                ),
                CRMTicket.title.contains(params.search_text.strip()),
            )
        )
    if cursor:
        profile_stmt = profile_stmt.where(Group.id < cursor.profile_id)
    profile_stmt = profile_stmt.order_by(
        Group.id.desc(),
    )
    statements.append(profile_stmt)

    if params and params.with_stores and (not cursor or not cursor.ticket_id):
        store_stmt = select(
            literal_column(f"'{schemas.CRMFilterTypeEnum.STORE.value}'").label("type"),
            Group.id.label("profile_id"),
            Group.name.label("profile_name"),
            Store.id.label("store_id"),
            Store.name.label("store_name"),
            literal_column("NULL").label("ticket_id"),
            literal_column("NULL").label("ticket_title"),
        ).select_from(Group)
        store_stmt = store_stmt.join(Store, Store.brand.has(group_id=Group.id))
        store_stmt = store_stmt.where(
            Store.is_deleted.is_(False),
            Scope.filter_for_action(
                "crm_store:read",
                "user", user_id,
                available_data={
                    "profile_id": Group.id,
                    "store_id": Store.id,
                }
            )
        )
        if params and params.search_text:
            store_stmt = store_stmt.where(
                or_(
                    Group.name.contains(params.search_text.strip()),
                    Store.name.contains(params.search_text.strip()),
                )
            )
        if cursor:
            if cursor.store_id:
                store_stmt = store_stmt.where(
                    or_(
                        Group.id < cursor.profile_id,
                        and_(
                            Group.id == cursor.profile_id,
                            Store.id < cursor.store_id,
                        )
                    )
                )
            else:
                store_stmt = store_stmt.where(Group.id <= cursor.profile_id)
        store_stmt = store_stmt.order_by(
            Group.id.desc(),
            Store.id.desc()
        )
        statements.append(store_stmt)

    if params and params.with_tickets:
        ticket_stmt = select(
            literal_column(f"'{schemas.CRMFilterTypeEnum.TICKET.value}'").label("type"),
            Group.id.label("profile_id"),
            Group.name.label("profile_name"),
            literal_column("NULL").label("store_id"),
            literal_column("NULL").label("store_name"),
            CRMTicket.first_title_id.label("ticket_id"),
            CRMTicket.title.label("ticket_title"),
        ).select_from(Group)
        ticket_stmt = ticket_stmt.join(CRMTicket, CRMTicket.group_id == Group.id)
        ticket_stmt = ticket_stmt.where(
            Scope.filter_for_action(
                "crm_ticket:read",
                "user", user_id,
                available_data={
                    "profile_id": Group.id,
                }
            )
        )
        if params and params.search_text:
            ticket_stmt = ticket_stmt.where(
                or_(
                    Group.name.contains(params.search_text.strip()),
                    CRMTicket.title.contains(params.search_text.strip()),
                )
            )
        if cursor:
            if cursor.ticket_id:
                ticket_stmt = ticket_stmt.where(
                    or_(
                        Group.id < cursor.profile_id,
                        and_(
                            Group.id == cursor.profile_id,
                            CRMTicket.first_title_id < cursor.ticket_id,  # type:ignore
                        )
                    )
                )
            else:
                ticket_stmt = ticket_stmt.where(Group.id <= cursor.profile_id)
        ticket_stmt = ticket_stmt.order_by(
            Group.id.desc(),
            desc(text("ticket_id"))
        )
        statements.append(ticket_stmt)

    for i, statement in enumerate(statements):
        if params and params.limit:
            statement = statement.limit(params.limit)
        statements[i] = statement

    if len(statements) == 1:
        query = statements[0].subquery()
    else:
        query = union_all(*statements).subquery()

    stmt = select(query)

    stmt = stmt.group_by(query.c.profile_id, query.c.store_id, query.c.ticket_id)

    if operation == "exists":
        return sess().scalar(select(stmt.exists()))

    stmt = stmt.order_by(query.c.profile_id.desc())
    stmt = stmt.order_by(query.c.type != schemas.CRMFilterTypeEnum.PROFILE.value)
    stmt = stmt.order_by(query.c.store_id.is_(None))
    stmt = stmt.order_by(query.c.store_id.desc())
    stmt = stmt.order_by(query.c.ticket_id.is_(None))
    stmt = stmt.order_by(query.c.ticket_id.desc())

    if params and params.limit:
        stmt = stmt.limit(params.limit)

    return sess().execute(stmt).fetchall()
