from sqlalchemy import or_, select

from db import db_func, sess
from db.models import CustomMenuButton, Translation


@db_func
def get_custom_menu_button_by_text(bot_id: int, text: str, lang: str):
    stmt = select(CustomMenuButton).where(
        CustomMenuButton.bot_id == bot_id,
    )

    stmt = stmt.where(
        or_(
            CustomMenuButton.text == text,
            Translation.filter_by_translation(
                CustomMenuButton, lang, "text", text,
                equal_or_contains="equal",
            )
        )
    )

    stmt = stmt.order_by(CustomMenuButton.position)
    stmt = stmt.order_by(CustomMenuButton.time_created.desc())
    stmt = stmt.limit(1)
    return sess().scalar(stmt)
