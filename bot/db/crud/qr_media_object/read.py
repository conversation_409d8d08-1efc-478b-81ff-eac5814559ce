from sqlalchemy import select

from db import db_func, sess
from db.models import QrMediaAdditionalObject, QrMediaObjectToQrMediaAdditionalObject
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def get_qr_media_additional_objects_by_qr_media_object(qr_media_object_id: int) -> list[QrMediaAdditionalObject]:
    stmt = select(QrMediaAdditionalObject)
    stmt = stmt.join(
        QrMediaObjectToQrMediaAdditionalObject,
        QrMediaObjectToQrMediaAdditionalObject.qr_media_additional_object_id == QrMediaAdditionalObject.id
    )
    stmt = stmt.where(QrMediaObjectToQrMediaAdditionalObject.qr_media_object_id == qr_media_object_id)

    return sess().scalars(stmt).all()
