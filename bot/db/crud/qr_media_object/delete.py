from db import db_func, sess
from db.models import QrMediaObject, QrMediaAdditionalObject, QrMediaObjectToQrMediaAdditionalObject
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def delete_qr_media_object(qr_media_id: int) -> bool:
    qr_media = sess().query(QrMediaObject).get(qr_media_id)
    if not qr_media:
        return True
    sess().delete(qr_media)
    sess().commit()

    return True


@catch_error_with_text_variable
@db_func
def delete_qr_media_additional_object(qr_media_additional_object_id: int) -> bool:
    qr_media = sess().query(QrMediaAdditionalObject).get(qr_media_additional_object_id)
    if not qr_media:
        return True
    sess().delete(qr_media)
    sess().commit()

    return True


@catch_error_with_text_variable
@db_func
def delete_qr_media_additional_object_from_qr_media_object(qr_media_object_id: int, qr_media_additional_object_id: int):
    stmt = sess().query(QrMediaObjectToQrMediaAdditionalObject).filter_by(
        qr_media_object_id=qr_media_object_id, qr_media_additional_object_id=qr_media_additional_object_id
    )
    stmt.delete()
    sess().commit()
