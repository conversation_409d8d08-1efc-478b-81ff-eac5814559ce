import json

import schemas
from db import db_func, sess
from db.models import QrMediaObject, QrMediaAdditionalObject
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def create_qr_media_object(
    target: schemas.QrMediaObjectTargetType,
    url: str,
    name: str | None = None,
    media_id: int | None = None,  # TODO: temporary for test purposes
    json_data: str | None = None,
):
    qr_media = QrMediaObject(
        name=name,
        target=target,
        url=url,
        media_id=media_id,
        json_data=json.loads(json_data) if json_data else None,
    )
    sess().add(qr_media)
    sess().commit()

    return qr_media


@catch_error_with_text_variable
@db_func
def create_qr_media_additional_object(name: str, media_id: int | None = None):
    qr_media_additional_object = QrMediaAdditionalObject(name=name, media_id=media_id)
    sess().add(qr_media_additional_object)
    sess().commit()

    return qr_media_additional_object
