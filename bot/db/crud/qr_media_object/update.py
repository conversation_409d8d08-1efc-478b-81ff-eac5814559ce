from db import db_func, sess
from db.models import QrMediaObjectToQrMediaAdditionalObject
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def connect_qr_media_object_to_qr_media_additional_object(qr_media_object_id: int, qr_media_additional_object_id: int):
    connect_object = QrMediaObjectToQrMediaAdditionalObject(
        qr_media_object_id=qr_media_object_id,
        qr_media_additional_object_id=qr_media_additional_object_id,
    )

    sess().add(connect_object)
    sess().commit()
