from typing import Iterable

from sqlalchemy import func, select, not_, exists
from sqlalchemy.sql import Select

import schemas
from db import db_func, sess
from db.models import UserGroup, Scope, User


@db_func
def get_user_groups_list(
        owner_type: schemas.UserGroupOwnerType,
        owner_object_id: int,
        params: schemas.AdminGetUserGroupsListParams,
        exclude_scopes: Iterable[str] | None = None,
) -> list[UserGroup]:

    subquery = (
        select(
            UserGroup.id.label("user_group_id"),
            Scope.id.label("scope_id"),
            Scope.scope.label("scope_name")
        )
        .distinct()
        .join(Scope, Scope.user_group_id == UserGroup.id)
        .where(Scope.target == 'user_group')
        .where(UserGroup.owner_type == owner_type)
        .where(getattr(UserGroup, f"owner_{owner_type}_id") == owner_object_id)
        .subquery()
    )

    stmt = (
        select(
            UserGroup,
            func.group_concat(
                subquery.c.scope_name.distinct().op("ORDER BY")(subquery.c.scope_id)
            ).label("scope_names")
        )
        .join(subquery, subquery.c.user_group_id == UserGroup.id)
        .group_by(UserGroup.id)
        .distinct()
    )

    if exclude_scopes:
        excluded_exists_stmt = (
            select(1)
            .select_from(Scope)
            .where(
                Scope.target == "user_group",
                Scope.user_group_id == UserGroup.id,
                Scope.scope.in_(exclude_scopes),
                not_(Scope.is_expired)
            )
            .exists()
        )
        stmt = stmt.where(not_(excluded_exists_stmt))

    if params.search_text:
        stmt = stmt.where(UserGroup.search(params.search_text.strip()))

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    stmt = stmt.order_by(UserGroup.id)

    return sess().execute(stmt).fetchall()


@db_func
def get_user_group_users_list(user_group_id: int) -> list[tuple[int, str, str]]:
    stmt: Select = select(User.id, User.name, User.photo)
    stmt = stmt.join(User.user_groups)
    stmt = stmt.where(User.not_deactivated)
    stmt = stmt.where(UserGroup.id == user_group_id)
    return sess().execute(stmt).fetchall()
