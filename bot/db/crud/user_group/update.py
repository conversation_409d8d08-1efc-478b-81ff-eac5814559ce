from typing import Iterable

from sqlalchemy import select, delete

from db import db_func, sess
from db.models import UserToUserGroup, UserGroup


def add_users_to_user_group_sync(
        user_group_id: int,
        users_to_add: list[int],
        commit: bool = True
) -> list[UserToUserGroup]:
    # noinspection PyTypeChecker
    existing_group_users: dict[int, UserToUserGroup] = dict(sess().execute(
        select(UserToUserGroup.user_id, UserToUserGroup)
        .where(UserToUserGroup.user_id.in_(users_to_add))
        .where(UserToUserGroup.user_group_id == user_group_id)
    ).fetchall())

    result: list[UserToUserGroup] = []
    new: list[UserToUserGroup] = []

    for user_id in users_to_add:
        user_to_user_group = existing_group_users.get(user_id)
        if not user_to_user_group:
            user_to_user_group = UserToUserGroup(
                user_id=user_id,
                user_group_id=user_group_id,
            )
            new.append(user_to_user_group)

    if new:
        sess().add_all(new)
        if commit:
            sess().commit()

    return result


@db_func
def add_users_to_user_group(user_group_id: int, users_to_add: list[int]) -> list[UserToUserGroup]:
    return add_users_to_user_group_sync(user_group_id, users_to_add)


@db_func
def delete_user_groups_users(
        user_group_id: int,
        user_ids: Iterable[int] | None = None,  # if empty, all users will be deleted
):
    stmt = delete(UserToUserGroup)
    stmt = stmt.execution_options(synchronize_session=False)
    stmt = stmt.where(UserToUserGroup.user_group_id == user_group_id)
    if user_ids:
        stmt = stmt.where(UserToUserGroup.user_id.in_(user_ids))
    sess().execute(stmt)
    sess().commit()
