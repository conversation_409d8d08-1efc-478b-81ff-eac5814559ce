from sqlalchemy import delete

from db import db_func, sess
from db.models import UserGroup, UserToUserGroup, <PERSON><PERSON>


@db_func
def delete_user_group(user_group: UserGroup):
    sess().execute(
        delete(UserToUserGroup).execution_options(synchronize_session=False).where(
            UserToUserGroup.user_group_id == user_group.id,
        )
    )

    sess().execute(
        delete(UserGroup).execution_options(synchronize_session=False).where(
            UserGroup.id == user_group.id,
        )
    )

    sess().delete(user_group)

    sess().flush()
    sess().commit()
    return True
