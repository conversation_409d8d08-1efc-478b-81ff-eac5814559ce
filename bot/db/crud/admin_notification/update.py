from datetime import datetime

from sqlalchemy import update

from db import db_func, sess
from db.models import SystemNotification
from schemas import NotificationRecipientType


@db_func
def update_admin_notification(
    admin_notification_id: int,
    is_read: bool | None = None,
    is_deleted: bool | None = None,
) -> bool:
    admin_notification = sess().query(SystemNotification).get(admin_notification_id)

    if admin_notification:
        values = {"change_date": datetime.utcnow()}

        if is_read is not None:
            values["is_read"] = is_read

        if is_deleted is not None:
            values["is_deleted"] = is_deleted

        sess().query(SystemNotification).filter(
            SystemNotification.id == admin_notification_id
        ).update(values)

        sess().commit()

    return True


@db_func
def update_admin_notifications_for_group(
    group_id: int,
    is_read: bool,
    notification_ids: list[int] | None = None,
    recipient_type: NotificationRecipientType | None = NotificationRecipientType.ADMIN,
) -> bool:
    update_query = (
        update(SystemNotification)
        .where(
            SystemNotification.group_id == group_id,
            SystemNotification.is_deleted.is_(False),
            SystemNotification.is_read != is_read,
            SystemNotification.recipient_type == recipient_type,
        )
    )

    if notification_ids:
        update_query = update_query.where(SystemNotification.id.in_(notification_ids))

    sess().execute(
        update_query.values(
            is_read=is_read,
            change_date=datetime.utcnow()
        )
    )

    sess().commit()
    return True


@db_func
def delete_admin_notifications_for_group(
    group_id: int,
    notification_ids: list[int] | None = None,
    recipient_type: NotificationRecipientType | None = NotificationRecipientType.ADMIN,
) -> bool:
    update_query = (
        update(SystemNotification)
        .where(
            SystemNotification.group_id == group_id,
            SystemNotification.is_deleted.is_(False),
            SystemNotification.recipient_type == recipient_type,
        )
    )

    if notification_ids:
        update_query = update_query.where(SystemNotification.id.in_(notification_ids))

    sess().execute(
        update_query.values(
            is_deleted=True,
            change_date=datetime.utcnow()
        )
    )

    sess().commit()
    return True


@db_func
def update_user_notifications(
    user_id: int,
    is_read: bool,
    notification_ids: list[int] | None = None,
) -> bool:
    """Update user notifications"""
    update_query = (
        update(SystemNotification)
        .where(
            SystemNotification.recipient_id == user_id,
            SystemNotification.is_deleted.is_(False),
            SystemNotification.is_read != is_read,
            SystemNotification.recipient_type == NotificationRecipientType.USER,
        )
    )

    if notification_ids:
        update_query = update_query.where(SystemNotification.id.in_(notification_ids))

    sess().execute(
        update_query.values(
            is_read=is_read,
            change_date=datetime.utcnow()
        )
    )

    sess().commit()
    return True


@db_func
def delete_user_notifications(
    user_id: int,
    notification_ids: list[int] | None = None,
) -> bool:
    """Delete user notifications"""
    update_query = (
        update(SystemNotification)
        .where(
            SystemNotification.recipient_id == user_id,
            SystemNotification.is_deleted.is_(False),
            SystemNotification.recipient_type == NotificationRecipientType.USER,
        )
    )

    if notification_ids:
        update_query = update_query.where(SystemNotification.id.in_(notification_ids))

    sess().execute(
        update_query.values(
            is_deleted=True,
            change_date=datetime.utcnow()
        )
    )

    sess().commit()
    return True
