from datetime import datetime

from sqlalchemy import distinct, func, select, text
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

from db import db_func, sess
from db.decorators.other import safe_deadlock_handler
from db.models import Group, Scope, SystemNotification
from schemas import (
    NotificationLevel, NotificationRecipientType, SystemNotificationCategory,
    SystemNotificationType,
)


@db_func
def get_admin_notification(
        admin_notification_id: int,
        profile_id: int,
):
    stmt = select(SystemNotification)
    stmt = stmt.where(SystemNotification.id == admin_notification_id, is_deleted=False)

    stmt = stmt.join(SystemNotification.group)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(SystemNotification.group_id == profile_id)

    stmt = stmt.group_by(SystemNotification.id)

    return sess().scalar(stmt)


@db_func
def get_admin_system_notification_list(
        profile_id: int,
        user_id: int,
        offset: int | None = None,
        limit: int | None = None,
        include: list[int] | None = None,
        exclude: list[int] | None = None,
        need_check_access: bool = False,
        is_count: bool = False,
        categories: list[SystemNotificationCategory] | None = None,
        type_notifications: list[SystemNotificationType] | None = None,
        is_read: bool | None = None,
        level: list[NotificationLevel] | None = None,
        recipient_type: NotificationRecipientType | None = NotificationRecipientType.ADMIN,
        recipient_id: int | None = None,
) -> list[Row] | int:
    if is_count:
        stmt: Select = select(func.count(distinct(SystemNotification.id)))
    else:
        stmt: Select = select(
            SystemNotification.id,
            *Scope.allowed_scopes_list(
                "read",
                object_name="profile",
                target="user",
                target_id=user_id,
                available_data={
                    "profile_id": profile_id,
                }
            ),
            SystemNotification.category,
            SystemNotification.type_notification,
            SystemNotification.level,
            SystemNotification.change_date,
            SystemNotification.time_created,
            SystemNotification.title,
            SystemNotification.is_read,
            SystemNotification.content,
            SystemNotification.recipient_id,
        )

        stmt = stmt.distinct()

    stmt = stmt.join(Group, SystemNotification.group_id == Group.id)

    stmt = stmt.where(SystemNotification.is_deleted.is_(False))

    # For admin notifications, filter by profile_id
    if recipient_type == NotificationRecipientType.ADMIN:
        stmt = stmt.where(Group.id == profile_id)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(SystemNotification.recipient_type == recipient_type)

    # For user notifications, filter by recipient_id
    if recipient_type == NotificationRecipientType.USER and recipient_id is not None:
        stmt = stmt.where(SystemNotification.recipient_id == recipient_id)

    if need_check_access:
        stmt = stmt.where(text("read_allowed IS TRUE"))

    if exclude:
        stmt = stmt.where(SystemNotification.id.not_in(exclude))

    if include:
        stmt = stmt.where(SystemNotification.id.in_(include))

    if categories:
        stmt = stmt.where(SystemNotification.category.in_(categories))

    if type_notifications:
        stmt = stmt.where(SystemNotification.type_notification.in_(type_notifications))

    if is_read is not None:
        stmt = stmt.where(SystemNotification.is_read.is_(is_read))

    if level:
        stmt = stmt.where(SystemNotification.level.in_(level))

    if is_count:
        return sess().scalar(stmt)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    stmt = stmt.order_by(SystemNotification.time_created.desc())

    return sess().execute(stmt).fetchall()


@safe_deadlock_handler
def get_last_created_admin_notification_sync(
        profile_id: int,
        category: SystemNotificationCategory,
        type_notification: SystemNotificationType,
        recipient_type: NotificationRecipientType | None = NotificationRecipientType.ADMIN,
        recipient_id: int | None = None,
) -> datetime:

    stmt: Select = select(
        func.max(SystemNotification.time_created)
    )
    stmt = stmt.with_for_update()

    stmt = stmt.join(Group, SystemNotification.group_id == Group.id)

    stmt = stmt.where(
        SystemNotification.is_deleted.is_(False),
        SystemNotification.recipient_type == recipient_type,
    )

    # For admin notifications, filter by profile_id
    if recipient_type == NotificationRecipientType.ADMIN:
        stmt = stmt.where(Group.id == profile_id)

    # For user notifications, filter by recipient_id
    if recipient_type == NotificationRecipientType.USER and recipient_id is not None:
        stmt = stmt.where(SystemNotification.recipient_id == recipient_id)

    stmt = stmt.where(Group.status == "enabled")

    if category:
        stmt = stmt.where(SystemNotification.category == category)

    if type_notification:
        stmt = stmt.where(SystemNotification.type_notification == type_notification)

    return sess().execute(stmt).scalar()
