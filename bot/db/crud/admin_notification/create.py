from datetime import datetime, timezone

from config import OnTimeoutBehaviour, notification_settings
from db import db_func, sess
from db.crud.admin_notification.read import get_last_created_admin_notification_sync
from db.models import SystemNotification
from schemas import (
    NotificationLevel, NotificationRecipientType, SystemNotificationCategory,
    SystemNotificationType,
)


@db_func
def creat_system_notification(
    scope: str,
    group_id: int, category: SystemNotificationCategory,
    type_notification: SystemNotificationType, title: str,
    content: str,
    level: NotificationLevel | None = NotificationLevel.ERROR,
    recipient_type: str | None = NotificationRecipientType.ADMIN,
    recipient_id: int | None = None,
) -> tuple[SystemNotification | None, bool]:
    last_time = get_last_created_admin_notification_sync(
        profile_id=group_id,
        category=category,
        type_notification=type_notification,
        recipient_type=recipient_type,
        recipient_id=recipient_id,
    )

    now = datetime.now(timezone.utc)
    now = now.replace(tzinfo=None)
    composite_key = f"{category.value}:{type_notification.value}"

    timeout = notification_settings.timeouts_per_key.get(
        composite_key,
        notification_settings.timeouts_per_key.get(
            category.value,
            notification_settings.timeouts_per_key["default"]
        )
    )
    on_timeout_behaviour = notification_settings.on_timeout_behaviour.get(
        composite_key,
        notification_settings.on_timeout_behaviour.get(
            category.value,
            notification_settings.on_timeout_behaviour["default"]
        )
    )

    is_restricted = last_time and (now - last_time) < timeout

    # якщо сповіщення (або категорія) має пропускатись, виходимо
    if is_restricted and on_timeout_behaviour == OnTimeoutBehaviour.THROTTLE:
        sess().commit()  # коміт потрібен, щоб зняти блокування (with_for_update)
        return None, True

    new_system_notification = SystemNotification.create_sync(
        scope=scope,
        group_id=group_id,
        category=category,
        type_notification=type_notification,
        title=title, content=content,
        time_created=now,
        level=level,
        recipient_type=recipient_type,
        recipient_id=recipient_id,
    )

    return new_system_notification, is_restricted
