from db import db_func, sess
from db.models import MenuInStoreToQrMediaObject
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def connect_qr_media_object_to_menu_in_store(menu_in_store_id: int, qr_media_object_id: int):
    connect_object = MenuInStoreToQrMediaObject(
        menu_in_store_id=menu_in_store_id,
        qr_media_object_id=qr_media_object_id,
    )

    sess().add(connect_object)
    sess().commit()
