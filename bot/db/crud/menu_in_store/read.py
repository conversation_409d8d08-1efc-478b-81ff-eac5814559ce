from sqlalchemy import outerjoin, select

from db import db_func, sess
from db.models import MenuInStore, MenuInStoreToQrMediaObject, QrMediaObject
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def get_qr_media_objects_by_menu_in_store(menu_in_store_id: int) -> list[QrMediaObject]:
    stmt = select(QrMediaObject)
    stmt = stmt.join(MenuInStoreToQrMediaObject, MenuInStoreToQrMediaObject.qr_media_object_id == QrMediaObject.id)
    stmt = stmt.where(MenuInStoreToQrMediaObject.menu_in_store_id == menu_in_store_id)

    return sess().scalars(stmt).all()


@catch_error_with_text_variable
@db_func
def get_menus_in_store_with_qr_media_objects(
        group_id: int,
        search_text: str | None = None,
        offset: int | None = None,
        limit: int = 10,
) -> list[tuple[MenuInStore, list[QrMediaObject]]]:
    stmt = (
        select(MenuInStore, QrMediaObject)
        .select_from(
            outerjoin(
                MenuInStore,
                MenuInStoreToQrMediaObject,
                MenuInStore.id == MenuInStoreToQrMediaObject.menu_in_store_id
            ).outerjoin(
                QrMediaObject,
                MenuInStoreToQrMediaObject.qr_media_object_id == QrMediaObject.id
            )
        )
        .where(MenuInStore.group_id == group_id, MenuInStore.is_deleted.is_(False))
    )

    if search_text:
        stmt = stmt.where(MenuInStore.comment.ilike(f"%{search_text}%"))

    if offset is not None:
        stmt = stmt.offset(offset)

    if limit:
        stmt = stmt.limit(limit)

    result = sess().execute(stmt).all()

    menu_dict = {}
    for menu, qr_code in result:
        if menu.id not in menu_dict:
            menu_dict[menu.id] = (menu, [])

        if qr_code:
            menu_dict[menu.id][1].append(qr_code)

    return list(menu_dict.values())
