from db import db_func, sess
from db.models import MenuInStoreToQrMediaObject
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def delete_qr_media_object_from_menu_in_store(menu_in_store_id: int, qr_media_object_id: int):
    stmt = sess().query(MenuInStoreToQrMediaObject).filter_by(
        menu_in_store_id=menu_in_store_id, qr_media_object_id=qr_media_object_id
    )
    stmt.delete()
    sess().commit()
