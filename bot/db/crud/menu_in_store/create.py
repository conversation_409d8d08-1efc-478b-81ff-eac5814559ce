import schemas
from db import db_func, sess
from db.models import MenuInStore
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def create_menu_in_store(
    group_id: int,
    comment: str,
    redirect_type: schemas.AdminQrMenuRedirectTypeLiteral = "web",
    payment_option: schemas.AdminQrMenuPaymentOption = "disabled",
    store_id: int | None = None,
    invoice_template_id: int | None = None,
    is_e_menu: bool = False,
    need_save_as_active: bool = True,
):
    menu_in_store = MenuInStore(
        comment=comment,
        store_id=store_id,
        redirect_type=redirect_type,
        payment_option=payment_option,
        invoice_template_id=invoice_template_id,
        is_e_menu=is_e_menu,
        need_save_as_active=need_save_as_active,
        group_id=group_id,
    )

    sess().add(menu_in_store)
    sess().commit()

    return menu_in_store
