from typing import Type

from sqlalchemy import delete

from db import db_func, sess
from db.connection import Base
from utils.type_vars import T


def connect_related_objects_sync(
        object: T, object_name_plural: str,
        objects: list[Base], replace: bool = False,
        commit: bool = True,
) -> T:
    if replace:
        setattr(object, object_name_plural, objects)
    else:
        getattr(object, object_name_plural).extend(objects)

    if commit:
        sess().commit()
    return object


@db_func
def connect_related_objects(object: T, object_name_plural: str, objects: list[Base], replace: bool = False) -> T:
    return connect_related_objects_sync(object, object_name_plural, objects, replace)


def disconnect_m2m_related_objects_sync(
        relation_model: Type[Base],
        current_object_field: str,
        current_object_id: int,
        related_field: str,
        related_object_ids: list[int] | None = None,
        disconnect_all: bool = False,
        commit: bool = True,
) -> bool:
    stmt = delete(relation_model)
    stmt = stmt.where(
        getattr(relation_model, current_object_field) == current_object_id,

    )
    if not disconnect_all:
        if not related_object_ids:
            return False
        stmt = stmt.where(getattr(relation_model, related_field).in_(related_object_ids))

    sess().execute(stmt)
    if commit:
        sess().commit()
    return True


@db_func
def disconnect_m2m_related_objects(
        relation_model: Type[Base],
        current_object_field: str,
        current_object_id: int,
        related_field: str,
        related_object_ids: list[int] | None = None,
        disconnect_all: bool = False
) -> bool:
    return disconnect_m2m_related_objects_sync(
        relation_model,
        current_object_field,
        current_object_id,
        related_field,
        related_object_ids,
        disconnect_all
    )
