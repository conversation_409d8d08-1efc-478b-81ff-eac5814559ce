import logging

from db import db_func, sess
from db.models import SSEChannel
from schemas import SSEChannelTarget


@db_func
def get_active_sse_channels(
        target: SSEChannelTarget,
        profile_id: int | None = None,
        key: str | None = None,
) -> list[SSEChannel]:
    logging.debug(f"{profile_id=}, {key=}")
    query = sess().query(SSEChannel)
    query = query.filter(
        SSEChannel.target == target,
    )
    if profile_id:
        query = query.filter(SSEChannel.profile_id == profile_id)
    if key:
        query = query.filter(SSEChannel.key == key)
    logging.debug(f"query: {query}")
    return query.all()
