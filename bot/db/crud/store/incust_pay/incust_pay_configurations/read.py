from sqlalchemy import select

import schemas
from db import db_func, sess
from db.models import ObjectPaymentSettings, PaymentSettings
from loggers import JSONLogger


@db_func
def get_incust_pay_configurations_list(
    brand_id: int | None = None,
    store_id: int | None = None,
    with_disabled: bool = False,
    payment_settings_id: int | None = None,
    object_payment_settings_id: int | None = None,
) -> list[schemas.IncustPayConfigurationPaymentObject] | int:
    return get_incust_pay_configurations_list_sync(
        brand_id,
        store_id,
        with_disabled,
        payment_settings_id,
        object_payment_settings_id,
    )


def get_incust_pay_configurations_list_sync(
    brand_id: int | None = None,
    store_id: int | None = None,
    with_disabled: bool = False,
    payment_settings_id: int | None = None,
    object_payment_settings_id: int | None = None,
) -> list[schemas.IncustPayConfigurationPaymentObject] | int:
    logger = JSONLogger(
        "incust.incust_pay", {
            "brand_id": brand_id,
            "store_id": store_id,
            "with_disabled": with_disabled,
            "payment_settings_id": payment_settings_id,
        },
    )

    query_objects = [PaymentSettings]
    if store_id:
        query_objects.append(ObjectPaymentSettings)
    elif object_payment_settings_id:
        query_objects.append(ObjectPaymentSettings)

    stmt = select(*query_objects).distinct()

    if brand_id:
        stmt = stmt.where(PaymentSettings.brand_id == brand_id)
    stmt = stmt.where(PaymentSettings.payment_method == "incust_pay")
    if payment_settings_id:
        stmt = stmt.where(PaymentSettings.id == payment_settings_id)

    if store_id:
        stmt = stmt.join(ObjectPaymentSettings, ObjectPaymentSettings.payment_settings_id == PaymentSettings.id)
        stmt = stmt.where(ObjectPaymentSettings.store_id == store_id)
        stmt = stmt.where(ObjectPaymentSettings.is_enabled.is_(True))
    elif object_payment_settings_id:
        stmt = stmt.join(ObjectPaymentSettings, ObjectPaymentSettings.payment_settings_id == PaymentSettings.id)
        stmt = stmt.where(ObjectPaymentSettings.id == object_payment_settings_id)
        stmt = stmt.where(ObjectPaymentSettings.is_enabled.is_(True))

    if not with_disabled:
        stmt = stmt.where(PaymentSettings.is_enabled.is_(True))

    result = sess().execute(stmt)
    result = result.scalars().all()

    data_dicts = []
    for item in result:
        if isinstance(item, tuple):
            data_dicts.append(
                {
                    "json_data": item[1].json_data if item[1].json_data else item[0].json_data,
                    "payment_settings_id": item[0].id
                }
            )
            continue
        data_dicts.append(
            {
                "json_data": item.json_data,
                "payment_settings_id": item.id
            }
        )
    result = [schemas.IncustPayConfigurationPaymentObject(
        **item.get("json_data"),
        payment_settings_id=item.get("payment_settings_id"),
    ) for item in data_dicts if item and item.get("json_data")]

    return result
