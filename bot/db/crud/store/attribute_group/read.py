from typing import Iterable

from sqlalchemy import and_, distinct, func, select, text
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

import schemas
from db import db_func, sess
from db.models import (
    AttributeGroupToProduct, Brand, Group, Scope,
    StoreAttributeGroup, \
    StoreProduct, Translation,
)


@db_func
def get_attribute_groups(
        brand_id: int,
        product_id: int | None = None,
        lang: str | None = None,
        sort: schemas.AttributeGroupSortEnum | None = None,
):
    return get_attribute_groups_sync(brand_id, product_id, lang, sort)


@db_func
def get_product_attribute_groups(
        product_id: int,
        fields: Iterable[str] | None = None,
) -> list[StoreAttributeGroup] | list[Row]:
    return get_product_attribute_groups_sync(product_id, fields)


def get_product_attribute_groups_sync(
        product_id: int,
        fields: Iterable[str] | None = None,
        with_lock: bool = False,
) -> list[StoreAttributeGroup] | list[Row]:
    if fields:
        query_objects = [getattr(StoreAttributeGroup, field) for field in fields]
    else:
        query_objects = [StoreAttributeGroup]

    query = sess().query(*query_objects)
    query = query.join(
        AttributeGroupToProduct,
        AttributeGroupToProduct.attribute_group_id == StoreAttributeGroup.id
        )

    query = query.filter(StoreAttributeGroup.is_deleted.is_(False))
    query = query.filter(AttributeGroupToProduct.product_id == product_id)

    if with_lock:
        query = query.populate_existing()
        query = query.with_for_update()

    if len(query_objects) == 1:
        return sess().scalars(query).all()

    return query.all()


@db_func
def get_admin_attribute_groups_list(
        profile_id: int,
        user_id: int,
        product_id: int | None = None,
        search_text: str | None = None,
        exclude: list[int] | None = None,
        include: list[int] | None = None,
        offset: int | None = None,
        limit: int | None = None,
        need_check_access: bool = False,
        is_count: bool = False,
) -> list[Row]:
    if is_count:
        stmt: Select = select(func.count(distinct(StoreAttributeGroup.id)))
    else:
        stmt: Select = select(
            StoreAttributeGroup.id,
            StoreAttributeGroup.name,
            StoreAttributeGroup._internal_name,
            *Scope.allowed_scopes_list(
                "read", "edit",
                object_name="attribute_group",
                target="user",
                target_id=user_id,
                available_data={
                    "profile_id": profile_id,
                    "attribute_group_id": StoreAttributeGroup.id,
                }
            ),
            StoreAttributeGroup.position,
        )

        stmt = stmt.distinct()

    stmt = stmt.join(StoreAttributeGroup.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")

    stmt = stmt.where(StoreAttributeGroup.is_deleted.is_(False))

    if product_id:
        stmt = stmt.where(
            StoreAttributeGroup.products.any(
                and_(
                    StoreProduct.id == product_id,
                    Scope.filter_for_action(
                        "product:read",
                        "user", user_id,
                        available_data={
                            "profile_id": profile_id,
                            "product_id": product_id,
                        }
                    )
                )
            )
        )

    if need_check_access:
        stmt = stmt.where(text("read_allowed IS TRUE"))

    if exclude:
        stmt = stmt.where(StoreAttributeGroup.id.not_in(exclude))

    if include:
        stmt = stmt.where(StoreAttributeGroup.id.in_(include))

    if search_text:
        stmt = stmt.where(StoreAttributeGroup.name.contains(search_text))

    if is_count:
        return sess().scalar(stmt)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    stmt = stmt.order_by(StoreAttributeGroup.position.is_(None))
    stmt = stmt.order_by(StoreAttributeGroup.position)

    stmt = stmt.order_by(StoreAttributeGroup.id.desc())

    return sess().execute(stmt).fetchall()


@db_func
def get_required_attributes_groups(product_id: int) -> list[int]:
    query = sess().query(distinct(StoreAttributeGroup.id))
    query = query.join(StoreProduct, StoreAttributeGroup.products)
    query = query.filter(StoreAttributeGroup.products.any(id=product_id))
    query = query.filter(StoreAttributeGroup.min > 0)
    return list(sess().scalars(query))


@db_func
def get_available_attributes_groups_ids(product_id: int) -> list[int]:
    query = sess().query(distinct(StoreAttributeGroup.id))
    query = query.join(StoreProduct, StoreAttributeGroup.products)
    query = query.filter(StoreAttributeGroup.products.any(id=product_id))
    return list(sess().scalars(query))


@db_func
def get_attribute_group_by_id_and_profile_id(
        attribute_group_id: int, profile_id: int
) -> StoreAttributeGroup | None:
    stmt: Select = select(StoreAttributeGroup)
    stmt = stmt.join(StoreAttributeGroup.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(StoreAttributeGroup.id == attribute_group_id)
    stmt = stmt.where(StoreAttributeGroup.is_deleted.is_(False))

    return sess().scalar(stmt)


def get_attribute_groups_sync(
        brand_id: int,
        product_id: int | None = None,
        lang: str | None = None,
        sort: schemas.AttributeGroupSortEnum | None = None,
        with_lock: bool = False,
) -> list[tuple[StoreAttributeGroup, Translation | None]]:
    if lang:
        query = sess().query(StoreAttributeGroup, Translation)
        query = query.outerjoin(
            Translation, Translation.filter(StoreAttributeGroup, lang)
        )
    else:
        query = sess().query(StoreAttributeGroup)
    query = query.filter(StoreAttributeGroup.is_deleted.is_(False))
    query = query.filter(StoreAttributeGroup.brand_id == brand_id)

    if product_id:
        query = query.join(
            AttributeGroupToProduct,
            AttributeGroupToProduct.attribute_group_id == StoreAttributeGroup.id,
        )
        query = query.filter(AttributeGroupToProduct.product_id == product_id)

    match sort:
        case schemas.AttributeGroupSortEnum.FOR_EXPORT:
            query = query.order_by(StoreAttributeGroup.position.is_(None))
            query = query.order_by(StoreAttributeGroup.position)
            query = query.order_by(StoreAttributeGroup.name)
            query = query.order_by(StoreAttributeGroup.id)

        case _:
            query = query.order_by(StoreAttributeGroup.position.is_(None))
            query = query.order_by(StoreAttributeGroup.position)
            query = query.order_by(StoreAttributeGroup.name)

    if with_lock:
        query = query.populate_existing()
        query = query.with_for_update()

    return query.all()
