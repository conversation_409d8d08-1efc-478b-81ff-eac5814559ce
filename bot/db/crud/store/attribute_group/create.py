import schemas
from sqlalchemy import func, select

from db import db_func, sess
from db.crud.scope.create import grand_scopes_to_created_object_sync
from db.models import Brand, StoreAttributeGroup, User
from config import MAX_POSITION_VALUE
from core.exceptions import MaxObjectPositionError


@db_func
def create_attribute_group(
    brand: Brand,
    data: schemas.AdminCreateAttributeGroupData,
    creator: User | None = None,
) -> StoreAttributeGroup:
    stmt = select(func.max(StoreAttributeGroup.position)).where(
        StoreAttributeGroup.brand_id == brand.id
    )
    last_position = sess().execute(stmt).scalar_one_or_none()
    position = last_position + 1 if isinstance(last_position, int) else 0
    if position > MAX_POSITION_VALUE:
        raise MaxObjectPositionError(object_name="attribute_group")

    attribute_group = StoreAttributeGroup(
        brand=brand,
        **data.dict(exclude_unset=True, exclude={"translations"}),
        position=position,
    )
    sess().add(attribute_group)

    if creator:
        grand_scopes_to_created_object_sync(
            "attribute_group",
            attribute_group, creator,
            {
                "profile_id": brand.group_id,
            }
        )

    sess().commit()
    return attribute_group
