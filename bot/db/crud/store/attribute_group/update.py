import schemas
from db import db_func, sess
from db.crud.translation.update import (
    clear_object_updated_fields_translations_sync,
    update_object_translations_sync,
)
from db.models import StoreAttributeGroup


@db_func
def update_attribute_group(
        attribute_group: StoreAttributeGroup,
        data: schemas.AdminCreateAttributeGroupData,
        profile_langs: list[str],
):
    attribute_group_object_data = data.dict(
        exclude_unset=True, exclude={"translations"}
    )

    if data.raw_internal_name:
        attribute_group_object_data["_internal_name"] = data.raw_internal_name

    if attribute_group_object_data:
        clear_object_updated_fields_translations_sync(
            attribute_group, attribute_group_object_data, profile_langs
        )
        attribute_group.update_sync(attribute_group_object_data, no_commit=True)

    if data.translations:
        update_object_translations_sync(attribute_group, data.translations)

    sess().commit()

    return attribute_group
