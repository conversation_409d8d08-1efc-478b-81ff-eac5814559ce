from sqlalchemy import or_, select

import exceptions
from db import db_func, sess
from db.models import Brand, BrandSettings, Group
from schemas import ExtSysSetTypes


@db_func
def check_has_user_brands(user_id: int) -> bool:
    query = sess().query(Brand.id)
    query = query.filter(Brand.group.has(owner_id=user_id, status="enabled"))
    return sess().query(query.exists()).scalar()


def check_is_domain_exists_sync(domain: str, for_update: bool = True) -> bool:
    query = sess().query(Brand.id)
    if for_update:
        query = query.populate_existing()
        query = query.with_for_update()

    query = query.filter(Brand.domain == domain)
    return sess().query(query.exists()).scalar()


@db_func
def check_is_domain_exists(domain: str, for_update: bool = True) -> bool:
    return check_is_domain_exists_sync(domain, for_update)


@db_func
def get_brand_name(brand_id: int) -> str:
    brand_name = None
    query = sess().query(Brand.name)
    query = query.filter(Brand.id == brand_id)
    result = query.one_or_none()
    if result:
        brand_name = result.name
    return brand_name


@db_func
def get_brand_by_group(group_id: int) -> Brand:
    query = sess().query(Brand)
    query = query.filter(Brand.group_id == group_id)

    return query.one_or_none()


@db_func
def get_brand_by_store(store_id: int) -> Brand | None:
    query = sess().query(Brand)
    query = query.filter(Brand.stores.any(id=store_id))
    return query.one_or_none()


@db_func
def get_brand_and_group_by_store(store_id: int) -> tuple[Brand, Group]:
    query = sess().query(Brand, Group)
    query = query.join(Brand.group)

    query = query.filter(Brand.stores.any(id=store_id))
    query = query.filter(Group.status == "enabled")

    return query.one()


@db_func
def get_is_friend_payment(brand_id: int, ) -> BrandSettings | None:
    query = sess().query(BrandSettings)
    query = query.filter(
        BrandSettings.type_data == "is_friend_payment",
        BrandSettings.brand_id == brand_id, )
    return query.one_or_none()


@db_func
def get_brand_scan_receipts_settings(brand_id: int) -> list[BrandSettings]:
    query = sess().query(BrandSettings)
    query = query.filter(BrandSettings.brand_id == brand_id)
    query = query.filter(
        or_(
            BrandSettings.type_data == "scan_receipts_bin_codes",
            BrandSettings.type_data == "scan_receipts_country",
            BrandSettings.type_data == "scan_receipts_enabled",
            BrandSettings.type_data == "scan_receipts_demo_mode",
            BrandSettings.type_data == "scan_receipts_enabled_all_rules",
        )
    )

    return query.all()


@db_func
def get_brand_settings_by_type(brand_id: int, type_data: str, is_not_convert: bool | None = None) -> list[BrandSettings]:
    values = ExtSysSetTypes.filter_values(type_data)
    query = sess().query(BrandSettings)
    query = query.filter(BrandSettings.brand_id == brand_id)
    query = query.filter(BrandSettings.type_data.in_(values))
    results = query.all()

    # Convert literal values to boolean fields
    if type_data == 'incust_' and is_not_convert is None:
        for setting in results:
            if setting.type_data == 'incust_type_client_auth':
                setting.value_data = True if setting.value_data == 'bot' else False
            elif setting.type_data == 'incust_loyalty_applicable_type':
                setting.value_data = True if setting.value_data == 'for_all' else False

    return results


@db_func
def detect_brand(
        brand_id: int | None = None,
        domains: list[str] | None = None,
):
    for condition in (
            Brand.id == brand_id,
            *[Brand.domain == domain for domain in domains],
    ):
        res = sess().execute(
            select(Brand, Group)
            .join(Brand.group)
            .where(condition)
        ).fetchone()
        if not res:
            continue

        brand, group = res
        if group.status == "disabled":
            raise exceptions.BrandProfileWasDeletedError()

        return brand
