from db import db_func, sess
from db.models import Brand, BrandSettings, Store, StoreProduct
from schemas import AdminIncustSettingsSchema, AdminScanReceiptsSettings, ExtSysSetTypes


@db_func
def update_brand_image_aspect_ratio(
        brand_id: int,
        new_aspect_ratio_str: str,
):
    brand = Brand.get_sync(brand_id)
    brand.product_image_aspect_ratio = new_aspect_ratio_str

    query = sess().query(StoreProduct)
    query = query.filter(StoreProduct.brand_id == brand_id)
    query = query.filter(StoreProduct.thumbnail_media_id.is_not(None))
    query.update({"thumbnail_media_id": None}, synchronize_session=False)

    sess().commit()


@db_func
def update_brand_categories_count_view(
        brand_id: int,
):
    brand = Brand.get_sync(brand_id)
    brand.is_categories_count_view = not brand.is_categories_count_view

    sess().commit()


@db_func
def update_brand_is_ai_enabled(brand_id: int, is_ai_enabled: bool | None = None):
    brand = Brand.get_sync(brand_id)
    if is_ai_enabled is not None:
        brand.is_ai_enabled = is_ai_enabled
    else:
        brand.is_ai_enabled = not brand.is_ai_enabled

    sess().commit()


@db_func
def update_friend_payment_settings(brand_id: int, payment_settings_value: bool | None):
    friend_payment_settings: BrandSettings = sess().query(BrandSettings).filter(
        BrandSettings.brand_id == brand_id,
        BrandSettings.type_data == "is_friend_payment"
    ).one_or_none()
    friend_payment_settings.value_data = "1" if payment_settings_value == "0" else "0"
    sess().commit()


@db_func
def update_brand_settings(
        brand_id: int, data: AdminIncustSettingsSchema | AdminScanReceiptsSettings,
        type_data: str | None = None
):
    if data is None:
        return
    if type_data is None:
        return

    session = sess()
    current_settings = session.query(BrandSettings).filter(
        BrandSettings.brand_id == brand_id,
        BrandSettings.type_data.in_(ExtSysSetTypes.filter_values(type_data))
    ).all()
    current_settings_dict = {setting.type_data: setting for setting in current_settings}

    # Update brand settings
    for key, value in data.dict(
            exclude_unset=True, exclude={"stores_settings"}
    ).items():
        if value is not None:
            if type_data == 'incust_':
                if key == 'incust_type_client_auth':
                    value = 'bot' if value else 'web'
                elif key == 'incust_loyalty_applicable_type':
                    value = 'for_all' if value else 'for_participants'
                elif key == 'incust_prohibit_redeeming_bonuses':
                    value = '1' if value else '0'
                elif key == 'incust_prohibit_redeeming_coupons':
                    value = '1' if value else '0'

            setting_type = f"{type_data}{key}" if not key.startswith(type_data) else key
            if setting_type in current_settings_dict:
                current_settings_dict[setting_type].value_data = str(value)
            else:
                setting = BrandSettings(
                    brand_id=brand_id,
                    type_data=setting_type,
                    value_data=str(value)
                )
                session.add(setting)

    # Update store settings
    if hasattr(data, 'stores_settings') and data.stores_settings:
        for store_settings in data.stores_settings:
            store = session.query(Store).filter(Store.id == store_settings.store_id).first()
            if store:
                if store_settings.terminal_api_key is not None:
                    store.incust_terminal_api_key = store_settings.terminal_api_key
                if store_settings.terminal_id is not None:
                    store.incust_terminal_id = store_settings.terminal_id

    session.commit()
