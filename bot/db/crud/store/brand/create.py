from config import LOC7_HOST
from db import db_func, sess
from db.crud.store.brand.read import check_is_domain_exists_sync
from db.models import Group, Brand, BrandSettings


@db_func
def auto_create_brand(group: Group, domain_prefix: str) -> Brand:
    brand = Brand.get_sync(group_id=group.id, for_update=True)
    if not brand:
        domain = None
        i = 0
        while not domain:
            temp_domain_prefix = domain_prefix if not i else f"{domain_prefix}-{i}"

            domain = f"https://{temp_domain_prefix}.{LOC7_HOST}/"
            if check_is_domain_exists_sync(domain):
                domain = None

            i += 1

        brand = Brand(
            name=group.name,
            group=group,
            domain=domain,
        )
        sess().add(brand)

    sess().commit()
    return brand


@db_func
def add_friend_payment_settings(brand_id: int,):
    friend_payment_settings = BrandSettings(
        brand_id=brand_id,
        type_data="is_friend_payment",
        value_data="1"
    )
    sess().add(friend_payment_settings)
    sess().commit()
