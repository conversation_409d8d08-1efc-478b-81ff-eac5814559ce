from dataclasses import dataclass, field
from itertools import groupby
from typing import Iterable, Literal

from sqlalchemy import and_, distinct, func, select
from sqlalchemy.engine import Row
from sqlalchemy.orm import aliased
from sqlalchemy.sql import Select, label

import schemas
from db import db_func, sess
from db.crud.store.product.read import (
    filter_products_by_filters_set,
)
from db.models import (
    Brand, Group, ProductToCategory, ProductToStore, Scope, Store, StoreCategory,
    StoreCategoryFilter, StoreCategoryToStore, StoreCharacteristic, StoreProduct,
    Translation,
)

NOT_SPECIFIED = "**not_specified**"


def get_store_categories_sync(
        brand_id: int,
        store_id: int | None = None,
        father_category_id: int | None = NOT_SPECIFIED,
        lang: str | None = None,
        with_translations: bool = True,
        for_catalog: bool = False,
        is_deleted: bool = False,
) -> list[StoreCategory] | list[tuple[StoreCategory, Translation | None]]:
    query_objects = [StoreCategory]

    if with_translations and lang:
        query_objects.append(Translation)

    if for_catalog:
        child_category = aliased(StoreCategory)
        has_child_categories = sess().query(child_category).where(
            child_category.father_category_id == StoreCategory.id,
            child_category.is_deleted.is_(False),
        )
        if store_id:
            has_child_categories = has_child_categories.where(
                child_category.stores.any(id=store_id)
            )
        query_objects.append(
            label("has_child_categories", has_child_categories.exists())
        )

    query = sess().query(*query_objects).distinct()

    if with_translations and lang:
        query = query.outerjoin(Translation, Translation.filter(StoreCategory, lang))

    if not is_deleted:
        query = query.filter(StoreCategory.is_deleted.is_(False))
    query = query.filter(StoreCategory.brand_id == brand_id)

    if store_id:
        query = query.join(
            StoreCategoryToStore, StoreCategoryToStore.category_id == StoreCategory.id
        )
        query = query.filter(StoreCategoryToStore.store_id == store_id)

    if father_category_id != NOT_SPECIFIED:
        query = query.filter(StoreCategory.father_category_id == father_category_id)

    query = query.order_by(StoreCategory.position.is_(None))
    query = query.order_by(StoreCategory.position)

    query = query.order_by(StoreCategory.name)

    query = query.group_by(StoreCategory.id)

    # noinspection PyTypeChecker
    return query.all()


@db_func
def get_store_categories(
        brand_id: int,
        store_id: int | None = None,
        father_category_id: int | None = NOT_SPECIFIED,
        lang: str | None = None,
        with_translations: bool = True,
        is_deleted: bool = False,
) -> list[StoreCategory] | list[tuple[StoreCategory, Translation | None]]:
    return get_store_categories_sync(
        brand_id, store_id, father_category_id,
        lang, with_translations, False,
        is_deleted,
    )


@dataclass
class TreeCategory:
    category: StoreCategory
    translation: Translation | None
    has_child_categories: bool = False
    children: list["TreeCategory"] = field(default_factory=list)
    products_count: int = 0
    filters: list[tuple[StoreCharacteristic, Translation | None]] = field(
        default_factory=list
    )


@db_func
def get_categories_tree(
        brand_id: int,
        store_id: int,
        lang: str,
        products_search: str | None = None,
        filters_set_id: int | None = None,
        filters_data: schemas.ProductListFiltersData | None = None,
):
    def get_products_subq():
        aliased_product = aliased(StoreProduct)
        products_q = select(
            aliased_product.id.label("product_id"),
            ProductToCategory.category_id.label("category_id"),
        )
        products_q = products_q.join(
            ProductToStore, ProductToStore.product_id == aliased_product.id
        )
        products_q = products_q.join(
            ProductToCategory, ProductToCategory.product_id == aliased_product.id
        )

        products_q = products_q.where(
            aliased_product.brand_id == brand_id,
            ProductToStore.store_id == store_id,
            aliased_product.is_deleted.is_(False),
            aliased_product.is_enabled.is_(True),
            aliased_product.is_available.is_(True),
            *filter_products_by_filters_set(
                None, store_id,
                filters_set_id, filters_data,
                product_cls=aliased_product
            )
        )

        if products_search:
            if not lang:
                raise ValueError("lang argument is required for search")
            products_q = products_q.where(aliased_product.search(products_search, lang))

        products_q = products_q.group_by(
            aliased_product.id, ProductToCategory.category_id
        )

        return products_q.subquery()

    def get_categories():
        category_translation = aliased(Translation)
        filter_translation = aliased(Translation)

        top_q: Select = select(
            StoreCategory.id.label("category_id"),
            StoreCategory.id.label("tree_id"),
        )
        top_q = top_q.where(StoreCategory.brand_id == brand_id)
        top_q = top_q.where(StoreCategory.is_deleted.is_(False))
        top_q = top_q.where(StoreCategory.stores.any(id=store_id))
        top_q = top_q.cte("trees", recursive=True)

        bottom_q: Select = select(
            StoreCategory.id.label("category_id"),
            top_q.c.tree_id
        )
        bottom_q = bottom_q.join(
            top_q, StoreCategory.father_category_id == top_q.c.category_id
        )
        bottom_q = bottom_q.where(StoreCategory.brand_id == brand_id)
        bottom_q = bottom_q.where(StoreCategory.is_deleted.is_(False))
        bottom_q = bottom_q.where(StoreCategory.stores.any(id=store_id))

        trees = top_q.union(bottom_q)
        products_q = get_products_subq()

        stmt = select(
            StoreCategory,
            category_translation,
            func.count(StoreProduct.group_hash.distinct()),
            StoreCharacteristic,
            filter_translation,
        )
        stmt = stmt.select_from(StoreCategory)

        stmt = stmt.join(trees, trees.c.tree_id == StoreCategory.id)
        stmt = stmt.outerjoin(
            products_q, products_q.c.category_id == trees.c.category_id
        )
        stmt = stmt.outerjoin(StoreProduct, products_q.c.product_id == StoreProduct.id)

        stmt = stmt.outerjoin(StoreCategory.filters)
        stmt = stmt.outerjoin(
            category_translation, category_translation.filter(StoreCategory, lang)
        )
        stmt = stmt.outerjoin(
            filter_translation, filter_translation.filter(StoreCharacteristic, lang)
        )

        stmt = stmt.join(
            StoreCategoryToStore, StoreCategoryToStore.category_id == StoreCategory.id
        )

        stmt = stmt.where(StoreCategory.is_deleted.is_(False))
        stmt = stmt.where(StoreCategory.brand_id == brand_id)
        stmt = stmt.where(StoreCategory.stores.any(id=store_id))

        stmt = stmt.order_by(StoreCategory.father_category_id.is_not(None))
        stmt = stmt.order_by(StoreCategory.father_category_id)

        stmt = stmt.order_by(StoreCategory.position.is_(None))
        stmt = stmt.order_by(StoreCategory.position)

        stmt = stmt.order_by(StoreCategory.name)
        stmt = stmt.order_by(StoreCategory.id)

        stmt = stmt.group_by(StoreCategory.id, StoreCharacteristic.id)

        return sess().execute(stmt).fetchall()

    @dataclass
    class CategoryData:
        category: StoreCategory
        translation: Translation | None
        products_count: int
        filters: list[tuple[StoreCharacteristic, Translation | None]]

    def process_category(data: list[Row]):
        category: StoreCategory | None = None
        translation: Translation | None = None
        products_count: int = 0

        filters: list[tuple[StoreCharacteristic, Translation | None]] = []

        for el in data:
            category, translation, products_count = el[0], el[1], el[2]
            if el[3]:
                filters.append((el[3], el[4]))

        return CategoryData(
            category=category,
            translation=translation,
            products_count=products_count,
            filters=filters,
        )

    all_categories = get_categories()

    categories_by_father_category = {
        father_category_id: [
            process_category(category_data)
            for _, category_data in groupby(categories_data, lambda x: x[0].id)
        ]
        for father_category_id, categories_data in
        groupby(all_categories, lambda x: x[0].father_category_id)
    }

    def make_categories(father_category_id: int | None = None):
        result: list[TreeCategory] = []
        for category_data in categories_by_father_category.get(father_category_id, []):
            children = make_categories(category_data.category.id)

            result.append(
                TreeCategory(
                    category=category_data.category,
                    translation=category_data.translation,
                    has_child_categories=bool(children),
                    children=children,
                    products_count=category_data.products_count,
                    filters=category_data.filters,
                )
            )

        return result

    return make_categories()


@db_func
def get_product_categories(
        product_id: int,
        store_id: int | None = None,
        fields: Iterable[str] | None = None,
        target: schemas.ScopeTarget | None = None,
        target_id: int | None = None,
        profile_id: int | None = None,
        need_scopes_allowed: bool = False
) -> list[int] | list[StoreCategory]:
    if fields:
        query_objects = [getattr(StoreCategory, field) for field in fields]
    else:
        query_objects = [StoreCategory]

    if need_scopes_allowed:
        if target is None or target_id is None or profile_id is None:
            raise ValueError(
                "WHen need_scopes_allowed is True, (target, target_id, profile_id) "
                "are required"
            )

        query_objects.extend(
            Scope.allowed_scopes_list(
                "read", "edit",
                object_name="category",
                target=target,
                target_id=target_id,
                available_data={
                    "profile_id": profile_id,
                    "category_id": StoreCategory.id
                }
            )
        )

    query = sess().query(*query_objects)

    query = query.join(
        ProductToCategory, ProductToCategory.category_id == StoreCategory.id
    )
    query = query.filter(ProductToCategory.product_id == product_id)

    if store_id:
        query = query.filter(StoreCategory.stores.any(id=store_id))
    query = query.filter(StoreCategory.is_deleted.is_(False))

    if target is not None and target_id is not None and profile_id is not None:
        query = query.filter(
            Scope.filter_for_action(
                "category:read",
                target, target_id,
                available_data={
                    "profile_id": profile_id,
                    "category_id": StoreCategory.id,
                }
            )
        )

    query = query.order_by(StoreCategory.position)
    query = query.order_by(StoreCategory.name)

    if len(query_objects) == 1:
        return sess().scalars(query).all()
    else:
        return query.all()  # type: ignore


@db_func
def get_product_main_category(
        product_id: int,
        field: Literal["id", "name", "all"] = "all",
        store_id: int | None = None,
) -> StoreCategory | str | int:
    stmt: Select = select(
        StoreCategory if field == "all" else getattr(StoreCategory, field)
    )
    stmt = stmt.join(
        ProductToCategory, ProductToCategory.category_id == StoreCategory.id
    )
    if store_id:
        stmt = stmt.where(StoreCategory.stores.any(id=store_id))
    stmt = stmt.where(ProductToCategory.product_id == product_id)

    stmt = stmt.order_by(ProductToCategory.id)
    stmt = stmt.limit(1)

    return sess().scalar(stmt)


@db_func
def get_has_category_children(category_id: int, store_id: int | None = None) -> bool:
    query = sess().query(StoreCategory.id)

    if store_id:
        query = query.filter(StoreCategory.stores.any(id=store_id))
    query = query.filter(StoreCategory.father_category_id == category_id)
    query = query.filter(StoreCategory.is_deleted.is_(False))
    return sess().query(query.exists()).scalar()


@db_func
def get_admin_categories_list(
        profile_id: int,
        user_id: int,
        store_ids: Iterable[int] | None = None,
        product_id: int | None = None,
        search_text: str | None = None,
        exclude: list[int] | None = None,
        offset: int | None = None,
        limit: int | None = None,
        need_check_access: bool = False,
        is_count: bool = False,
        sort_by_father_category: bool = False,
        with_filters: bool = False,
) -> list[Row] | int:
    if is_count:
        stmt: Select = select(func.count(distinct(StoreCategory.id)))
        if need_check_access:
            stmt = stmt.where(
                Scope.filter_for_action(
                    "category:read",
                    "user", user_id,
                    {"profile_id": profile_id, "category_id": StoreCategory.id}
                )
            )
    else:
        father_category = aliased(StoreCategory)
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="category",
            target="user",
            target_id=user_id,
            available_data={"profile_id": profile_id, "category_id": StoreCategory.id}
        )
        stmt: Select = select(
            StoreCategory.id,
            StoreCategory.name,
            Group.id.label("profile_id"),
            StoreCategory.father_category_id,
            label("father_category_name", father_category.name),
            StoreCategory.position,
            read_allowed,
            edit_allowed,
        ).distinct().outerjoin(
            father_category, StoreCategory.father_category_id == father_category.id
        )
        if need_check_access:
            stmt = stmt.where(read_allowed.is_(True))

    stmt = stmt.join(StoreCategory.brand).join(Brand.group)
    stmt = stmt.where(Group.id == profile_id, Group.status == "enabled")
    stmt = stmt.where(StoreCategory.is_deleted.is_(False))

    if exclude:
        stmt = stmt.where(StoreCategory.id.not_in(exclude))
    if store_ids:
        stmt = stmt.where(
            StoreCategory.stores.any(
                and_(
                    Store.id.in_(store_ids),
                    Scope.filter_for_action(
                        "store:read",
                        "user", user_id,
                        {"profile_id": profile_id, "store_id": Store.id}
                    )
                )
            )
        )
    if product_id:
        stmt = stmt.where(StoreCategory.products.any(id=product_id))
    if search_text:
        stmt = stmt.where(StoreCategory.name.contains(search_text))

    if is_count:
        return sess().scalar(stmt)

    if with_filters:
        char_subq = (
            select(
                StoreCategoryFilter.category_id.label("category_id"),
                func.JSON_ARRAYAGG(
                    func.JSON_OBJECT(
                        'id', StoreCharacteristic.id,
                        'name', StoreCharacteristic.name
                    )
                ).label("characteristics")
            )
            .select_from(StoreCategoryFilter)
            .join(
                StoreCharacteristic,
                StoreCharacteristic.id == StoreCategoryFilter.characteristic_id
            )
            .group_by(StoreCategoryFilter.category_id)
            .subquery()
        )
        stmt = stmt.outerjoin(
            char_subq, StoreCategory.id == char_subq.c.category_id
        ).add_columns(char_subq.c.characteristics)

    if sort_by_father_category:
        stmt = stmt.order_by(StoreCategory.father_category_id)
    stmt = stmt.order_by(StoreCategory.position.is_(None))
    stmt = stmt.order_by(StoreCategory.position)
    stmt = stmt.order_by(StoreCategory.id)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    return sess().execute(stmt).fetchall()


@db_func
def get_category_by_id_and_profile_id(
        category_id: int, profile_id: int
) -> StoreCategory | None:
    stmt: Select = select(StoreCategory)
    stmt = stmt.join(StoreCategory.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(StoreCategory.id == category_id)
    stmt = stmt.where(StoreCategory.is_deleted.is_(False))

    return sess().scalar(stmt)


@db_func
def get_categories_by_ids(stores_categories_ids: Iterable[int]) -> list[Store | None]:
    # noinspection PyTypeChecker
    db_stores: dict[int, Store] = dict(
        sess().execute(
            select(StoreCategory.id, StoreCategory)
            .where(
                StoreCategory.id.in_(stores_categories_ids),
                StoreCategory.is_deleted.is_(False)
            )
        ).fetchall()
    )

    return [db_stores.get(id) for id in stores_categories_ids]
