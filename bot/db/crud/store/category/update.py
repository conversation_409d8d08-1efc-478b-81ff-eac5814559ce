from operator import attrgetter

from sqlalchemy import select

import schemas
from db import db_func, sess
from db.crud.base.update import disconnect_m2m_related_objects_sync
from db.crud.translation.update import clear_object_updated_fields_translations_sync, update_object_translations_sync
from db.models import Scope, Store, StoreCategory, StoreCategoryToStore


@db_func
def update_category(
        category: StoreCategory,
        data: schemas.AdminUpdateCategoryData,
        profile_langs: list[str],
):
    category_object_data = data.dict(exclude_unset=True, exclude={"translations"})

    if category_object_data:
        clear_object_updated_fields_translations_sync(category, category_object_data, profile_langs)
        category.update_sync(category_object_data, no_commit=True)

    if data.translations:
        update_object_translations_sync(category, data.translations)

    sess().commit()

    return category


@db_func
def connect_stores_to_category(
        profile_id: int,
        user_id: int,
        category: StoreCategory,
        stores: list[Store] = None,
        replace: bool = False,
):

    stores = stores or []

    if replace:
        stores_to_disconnect_ids = sess().scalars(
            select(Store.id).where(
                Store.categories.any(id=category.id),
                Store.id.not_in(map(attrgetter("id"), stores)),
                Scope.filter_for_action(
                    "store:edit", "user", user_id, {
                        "profile_id": profile_id,
                        "store_id": Store.id,
                    }
                ),
            )
        ).all()

        disconnect_m2m_related_objects_sync(
            StoreCategoryToStore,
            "category_id", category.id,
            "store_id", stores_to_disconnect_ids,
            commit=False,
        )

    for store in stores:
        if store not in category.stores:
            category.stores.append(store)

    sess().commit()
    return True


@db_func
def connect_categories_to_store(
        store: Store,
        categories: list[StoreCategory] = None,
):
    categories = categories or []

    for category in categories:
        if category not in store.categories:
            store.categories.append(category)

    sess().commit()
    return True


@db_func
def disconnect_categories_from_store(
        store: Store,
        categories: list[StoreCategory] = None,
):
    categories = categories or []

    for category in categories:
        if category in store.categories:
            store.categories.remove(category)

    sess().commit()
    return True
