from sqlalchemy import update

from db import db_func, sess
from db.models import StoreCategory


@db_func
def delete_category(category_id: int):
    sess().execute(
        update(StoreCategory).values({"is_deleted": True}).where(
            StoreCategory.filter_recursive(category_id)
        ).execution_options(synchronize_session="fetch")
    )
    sess().commit()


@db_func
def mass_delete_categories(category_ids: list[int]):
    ids_to_update = set(category_ids)
    for category_id in category_ids:
        child_ids = sess().execute(
            StoreCategory.get_child_categories_ids_query(category_id)
        ).scalars().all()
        ids_to_update.update(child_ids)

    sess().execute(
        update(StoreCategory)
        .values({"is_deleted": True})
        .where(StoreCategory.id.in_(ids_to_update))
        .execution_options(synchronize_session="fetch")
    )
    sess().commit()
