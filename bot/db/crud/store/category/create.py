from sqlalchemy import func, select

import schemas
from config import MAX_POSITION_VALUE
from core.exceptions import MaxObjectPositionError
from db import db_func, sess
from db.crud.scope.create import grand_scopes_to_created_object_sync
from db.crud.translation.update import update_object_translations_sync
from db.models import Brand, Store, StoreCategory, User


@db_func
def create_category(
        brand: Brand,
        data: schemas.AdminCreateCategoryData,
        creator: User | None = None,
        stores: list[Store] | None = None,
) -> StoreCategory:
    stmt = select(func.max(StoreCategory.position)).where(
        StoreCategory.brand_id == brand.id
    )
    last_position = sess().execute(stmt).scalar_one_or_none()
    position = last_position + 1 if (last_position or last_position == 0) else 0
    if position > MAX_POSITION_VALUE:
        raise MaxObjectPositionError(object_name="category")

    category = StoreCategory(
        brand=brand,
        stores=stores or [],
        **data.dict(exclude_unset=True, exclude={"stores", "translations"}),
        position=position,
    )
    sess().add(category)
    sess().flush()

    if data.translations:
        updated_translations = {}
        for key, value in data.translations.items():
            if value is not None:
                updated_translations[key] = schemas.AdminCategoryTranslationSchema(
                    **value
                )
            else:
                updated_translations[key] = None

        update_object_translations_sync(category, updated_translations)

    if creator:
        grand_scopes_to_created_object_sync(
            "category", category, creator, {
                "profile_id": brand.group_id,
            }
        )

    sess().commit()
    return category
