import schemas
from core.store.functions.cart_funcs import validate_cart_product_with_another_attributes
from db import db_func, sess
from db.decorators.other import safe_deadlock_handler
from db.models import StoreCart, StoreCartAttribute, StoreCartProduct, User
from .read import check_is_product_in_cart, get_cart_sync
from ..attribute_group.read import get_attribute_groups_sync


@safe_deadlock_handler
@db_func
def save_cart_product(product: schemas.SaveCartProductSchema, cart: StoreCart) -> StoreCartProduct:
    return save_cart_product_sync(product, cart)


def save_cart_product_sync(product: schemas.SaveCartProductSchema, cart: StoreCart, no_commit: bool = False) -> (
        StoreCartProduct):
    cart_product = StoreCartProduct(
        store_id=cart.store_id,
        **product.dict(exclude={"cart_attributes", "floating_sum"})
    )
    if cart.id:
        cart_product.cart_id = cart.id
    else:
        cart.cart_products = [cart_product]

    if product.floating_sum:
        cart_product.floating_sum = product.floating_sum * 100

    attributes = []
    if product.cart_attributes:
        for attribute_schema in product.cart_attributes:
            attributes.append(
                StoreCartAttribute(
                    quantity=attribute_schema.quantity,
                    attribute_id=attribute_schema.attribute_id,
                )
            )
    sess().add_all(attributes)
    cart_product.cart_attributes = attributes

    sess().add(cart_product)
    if not no_commit:
        sess().commit()
    return cart_product


@db_func
def add_attribute_to_cart_product(
        cart_product_id: int,
        attribute_id: int, quantity: int,
) -> StoreCartAttribute:
    cart_attr = StoreCartAttribute(
        quantity=quantity,
        attribute_id=attribute_id,
        cart_product_id=cart_product_id,
    )
    sess().add(cart_attr)
    sess().commit()
    return cart_attr


@safe_deadlock_handler
@db_func
def add_product_with_cart(
        user_data: dict | User,
        data: schemas.AddCartProduct,
        with_lock: bool = False,
) -> tuple[StoreCart | None, StoreCartProduct | None, int | None, bool]:
    duplicate_prod = None
    cart, is_new_cart = get_cart_sync(user_data, data.store_id, with_lock, no_commit=True)
    if not is_new_cart:
        if check_is_product_in_cart(cart.id, data.product.product_id, True):
            attr_groups = get_attribute_groups_sync(data.product.product_id, with_lock=True)
            if data.product.cart_attributes or (not data.product.cart_attributes and attr_groups):
                duplicate_prod = validate_cart_product_with_another_attributes(data.product, cart.id)
            else:
                sess().commit()
                return None, None, 409, is_new_cart

    if duplicate_prod:
        duplicate_prod.update_sync(quantity=duplicate_prod.quantity + 1, for_update=True, no_commit=True)
        created_product: StoreCartProduct | None = duplicate_prod
    else:
        created_product: StoreCartProduct | None = save_cart_product_sync(data.product, cart, no_commit=True)

    sess().commit()

    return cart, created_product, None, is_new_cart
