from itertools import groupby
from sqlalchemy import delete, select

from core.store.functions.cart_funcs import get_cart_data_from_token
from db import db_func, sess
from db.decorators.other import safe_deadlock_handler
from db.models import (
    StoreCart, StoreCartAttribute, StoreCartProduct, StoreProduct,
    User,
)


def check_is_product_in_cart(
        cart_id: int, product_id: int, with_lock: bool = False
) -> bool:
    query = sess().query(StoreCartProduct)

    query = query.filter(StoreCartProduct.cart_id == cart_id)
    query = query.filter(StoreCartProduct.product_id == product_id)

    if with_lock:
        query = query.populate_existing()
        query = query.with_for_update()

    return sess().query(query.exists()).scalar()


@db_func
def check_is_product_in_card_db_func(
        cart_id: int, product_id: int, with_lock: bool = False
):
    return check_is_product_in_cart(cart_id, product_id, with_lock)


@db_func
@safe_deadlock_handler
def get_cart(
        user_data: dict | User,
        store_id: int,
        with_lock: bool = False,
) -> tuple[StoreCart, bool]:
    return get_cart_sync(user_data, store_id, with_lock)


def get_cart_sync(
        user_or_token_data: dict | User, store_id: int,
        with_lock: bool = False, no_commit: bool = False
) -> tuple[StoreCart, bool]:
    cart = None
    is_new_cart = False
    if isinstance(user_or_token_data, User):
        query = sess().query(StoreCart)
        query = query.filter(StoreCart.user_id == user_or_token_data.id)
        query = query.filter(StoreCart.store_id == store_id)
        query = query.populate_existing()
        query = query.with_for_update()
        try:
            cart = query.one_or_none()
        except Exception as e:
            if with_lock:
                raise e
            query = sess().query(StoreCart)
            query = query.filter(StoreCart.user_id == user_or_token_data.id)
            query = query.filter(StoreCart.store_id == store_id)
            cart = query.one_or_none()
    elif user_or_token_data:
        is_user, obj_id = get_cart_data_from_token(user_or_token_data, store_id)
        if not is_user and obj_id:
            query = sess().query(StoreCart)
            query = query.filter(StoreCart.id == int(obj_id))
            if with_lock:
                query = query.populate_existing()
                query = query.with_for_update()
            cart = query.one_or_none()

    if not cart:
        is_new_cart = True
        db_user = None
        if isinstance(user_or_token_data, User):
            db_user = user_or_token_data
            try:
                existing_cart = sess().query(StoreCart)\
                    .filter(StoreCart.user_id == db_user.id)\
                    .filter(StoreCart.store_id == store_id)\
                    .with_for_update(nowait=True)\
                    .one_or_none()
                if existing_cart:
                    return existing_cart, False
            except Exception:
                pass
        
        try:
            cart = StoreCart.save_sync(store_id, user=db_user, no_commit=no_commit)
        except Exception as e:
            if isinstance(user_or_token_data, User):
                sess().rollback()
                existing_cart = sess().query(StoreCart)\
                    .filter(StoreCart.user_id == user_or_token_data.id)\
                    .filter(StoreCart.store_id == store_id)\
                    .one_or_none()
                if existing_cart:
                    return existing_cart, False
            raise e

    return cart, is_new_cart


@db_func
@safe_deadlock_handler
def get_cart_products_data(
        cart_id: int,
        unavailable_product_ids: list[int] | None = None
) -> list[tuple[StoreCartProduct, StoreProduct, list[StoreCartAttribute]]]:
    stmt = (
        select(
            StoreCartProduct,
            StoreProduct,
            StoreCartAttribute,
        )
        .outerjoin(StoreCartProduct.product)
        .outerjoin(StoreCartProduct.cart_attributes)
        .where(StoreCartProduct.cart_id == cart_id)
        .order_by(StoreCartProduct.id, StoreCartAttribute.id)
    )

    if unavailable_product_ids:
        stmt = stmt.with_for_update()

    rows = sess().execute(stmt).fetchall()

    result = []

    cart_products_to_delete = []

    for _, attr_rows in groupby(rows, lambda x: x[0].id):
        attributes = []

        cart_product = None
        product = None
        for row in attr_rows:
            cart_product, product, attribute = row

            if attribute:
                attributes.append(attribute)

        if unavailable_product_ids and product.id in unavailable_product_ids:
            cart_products_to_delete.append(cart_product.id)
            continue

        if not cart_product:
            raise RuntimeError("Impossible situation")

        result.append((cart_product, product, attributes))

    if cart_products_to_delete:
        sess().execute(
            delete(StoreCartAttribute).where(
                StoreCartAttribute.cart_product_id.in_(cart_products_to_delete),
            )
        )
        sess().execute(
            delete(StoreCartProduct).where(
                StoreCartProduct.id.in_(cart_products_to_delete),
            )
        )

    if unavailable_product_ids:
        sess().commit()
    return result
