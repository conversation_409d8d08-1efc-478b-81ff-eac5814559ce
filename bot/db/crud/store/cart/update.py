import schemas
from core.auth.parse_token import parse_token
from core.store.functions.cart_funcs import (
    get_cart_data_from_token,
    validate_cart_product_with_another_attributes,
)
from db import db_func, sess
from db.decorators.other import safe_deadlock_handler
from db.models import <PERSON>Cart, StoreCartAttribute, StoreCartProduct, User
from .read import get_cart_sync
from ..attribute_group import get_product_attribute_groups_sync


@db_func
def update_cart_product(
        cart_id: int, cart_product_id: int, data: schemas.UpdateCartProductRequestSchema
) -> bool:
    cart_product = StoreCartProduct.get_sync(
        cart_product_id, for_update=True, cart_id=cart_id
    )
    if not cart_product:
        return False

    obj = {}
    if cart_product.quantity != data.quantity:
        obj["quantity"] = data.quantity

    if cart_product.floating_sum and round(
            cart_product.floating_sum / 100, 2
    ) != data.floating_sum:
        obj["floating_sum"] = data.floating_sum * 100

    if obj:
        cart_product.update_sync(obj)

    if data.attributes:
        for attribute in data.attributes:
            cart_attribute = StoreCartAttribute.get_sync(
                cart_product_id=cart_product_id,
                attribute_id=attribute.attribute_id,
                for_update=True,
            )
            if cart_attribute:
                if attribute.delete:
                    sess().delete(cart_attribute)
                else:
                    cart_attribute.update_sync(
                        no_commit=True, quantity=attribute.quantity
                    )
            else:
                StoreCartAttribute.create_sync(
                    cart_product_id=cart_product_id,
                    attribute_id=attribute.attribute_id,
                    quantity=attribute.quantity,
                )

    sess().commit()
    return True


@db_func
@safe_deadlock_handler
def sync_user_cart(anon_cart_token: str, store_id: int, user: User) -> StoreCart | None:
    cart_data, _ = parse_token(anon_cart_token)
    is_user, obj_id = get_cart_data_from_token(cart_data, store_id)
    cart = None
    if not is_user and obj_id:
        cart = StoreCart.get_sync(obj_id, for_update=True)
        if not cart or (cart and cart.user_id):
            user_cart, _ = get_cart_sync(user, store_id, with_lock=True, no_commit=True)
            sess().commit()

            return user_cart

    if not cart:
        return

    user_cart, _ = get_cart_sync(user, store_id, with_lock=True)
    if not user_cart:
        return

    user_cart_products = StoreCartProduct.get_list_sync(cart_id=user_cart.id)
    if not user_cart_products:
        user_cart.delete_sync()
        cart.attach_cart_to_user_sync(user.id, no_commit=True)
        sess().commit()
        return cart

    anon_cart_products = StoreCartProduct.get_list_sync(cart_id=cart.id)
    for cart_product in anon_cart_products:
        is_in_cart = user_cart.is_product_in_cart(cart_product.product_id)
        if is_in_cart:
            attr_groups = get_product_attribute_groups_sync(
                cart_product.product_id, with_lock=True,
            )
            if attr_groups:
                prod_schema = schemas.SaveCartProductSchema(
                    quantity=cart_product.quantity,
                    product_id=cart_product.product_id,
                    cart_attributes=[schemas.SaveCartAttributeSchema(
                        quantity=attr.quantity,
                        attribute_id=attr.attribute_id,
                    ) for attr in cart_product.cart_attributes],
                    floating_sum=cart_product.floating_sum,
                )
                duplicated_product = (
                    validate_cart_product_with_another_attributes(
                        prod_schema, cart.id, cart_product.id,
                    ))
                if not duplicated_product:
                    is_in_cart = False

        if not is_in_cart:
            cart_product.attach_product_to_cart_sync(
                user_cart.id, no_commit=True
            )
    sess().commit()
    cart.delete_sync()

    return user_cart


@db_func
def set_user_to_cart(cart: StoreCart, user_id: int):
    sql_delete_children = """
    DELETE sca, scp
    FROM store_cart_product AS scp
    LEFT JOIN store_cart_attributes AS sca ON scp.id = sca.cart_product_id
    WHERE scp.cart_id IN (
        SELECT id FROM store_cart
        WHERE id != :cart_id AND user_id = :user_id AND store_id = :store_id
    );
    """

    sql_delete_parent = """
    DELETE sc
    FROM store_cart AS sc
    WHERE sc.id != :cart_id AND sc.user_id = :user_id AND sc.store_id = :store_id;
    """

    sess().execute(
        sql_delete_children, {
            "cart_id": cart.id,
            "user_id": user_id,
            "store_id": cart.store_id
        }
    )

    sess().execute(
        sql_delete_parent, {
            "cart_id": cart.id,
            "user_id": user_id,
            "store_id": cart.store_id
        }
    )

    sess().flush()

    cart.user_id = user_id
    return cart
