from typing import List

from db import db_func, sess
from db.models import (
    StoreBanner,
    MediaObject,
)


@db_func
def get_banners_list(store_id: int) -> List[StoreBanner]:

    banners = sess().query(
        StoreBanner.id,
        StoreBanner.url,
        StoreBanner.name,
        StoreBanner.position,
        StoreBanner.is_visible,
        MediaObject.url.label("image_url"),
        StoreBanner.task_id,
    ).outerjoin(StoreBanner.media).filter(StoreBanner.store_id == store_id).order_by(
        StoreBanner.position.desc(), StoreBanner.id
    ).all()
    return banners


@db_func
def get_store_banner_by_id(banner_id: int) -> StoreBanner:
    banner = sess().query(
        StoreBanner.id,
        StoreBanner.url,
        StoreBanner.name,
        StoreBanner.position,
        StoreBanner.is_visible,
        MediaObject.url.label("image_url"),
        StoreBanner.task_id,
    ).outerjoin(StoreBanner.media).filter(StoreBanner.id == banner_id).first()
    return banner
