from sqlalchemy.orm import Session
from datetime import datetime

import schemas
from config import CONFIRMED_EMAIL_EXPIRES
from db import db_func
from db.models import ConfirmEmailRequest, User


# Read

@db_func
def check_is_confirmed_email(db: Session, email: str, purpose: str) -> bool:
    query = db.query(ConfirmEmailRequest).filter(ConfirmEmailRequest.email == email)
    query = query.filter(ConfirmEmailRequest.is_confirmed == True)
    query = query.filter(ConfirmEmailRequest.purpose == purpose)
    query = query.filter(
        ConfirmEmailRequest.datetime_confirmed > datetime.utcnow() - CONFIRMED_EMAIL_EXPIRES
    )

    query = db.query(query.exists())
    return query.scalar()


@db_func
def get_confirm_email_request(db: Session, request_id: int) -> ConfirmEmailRequest:
    return db.get(ConfirmEmailRequest, request_id)


# Create

@db_func
def create_confirm_email_request(
    db: Session, email: str,
    purpose: schemas.ConfirmEmailPurposeLiteral,
) -> ConfirmEmailRequest:
    query = db.query(ConfirmEmailRequest)
    query = query.filter(ConfirmEmailRequest.email == email)
    query = query.filter(ConfirmEmailRequest.purpose == purpose)
    result = db.scalar(query)

    if result:
        db.delete(result)
        db.commit()

    request = ConfirmEmailRequest(email=email, purpose=purpose)
    db.add(request)
    db.commit()
    return request


# Update

@db_func
def confirm_email_request_sent(db: Session, request: ConfirmEmailRequest) -> None:
    request.datetime_sent = datetime.utcnow()
    db.commit()


@db_func
def confirm_email(
    db: Session,
    request_id: int,
) -> ConfirmEmailRequest | None:
    request = ConfirmEmailRequest.get_sync(request_id)
    if not request:
        return

    if request.purpose != "change_email" and request.purpose != "set_email_admin":
        user = User.get_sync(email=request.email)
        if not user:
            return

        user.is_confirmed_email = True
        user.is_guest_user = False

    request.is_confirmed = True
    request.datetime_confirmed = datetime.utcnow()

    db.commit()
    return request


@db_func
def confirm_user_email(
    db: Session,
    user: User,
) -> bool | None:
    user.is_confirmed_email = True
    user.is_guest_user = False

    db.commit()
    return True


# Delete

def delete_confirm_email_request_sync(
    db: Session,
    email: str,
    purpose: schemas.ConfirmEmailPurposeLiteral,
    no_commit: bool = True,
):
    query = db.query(ConfirmEmailRequest)
    query = query.filter(ConfirmEmailRequest.email == email)
    query = query.filter(ConfirmEmailRequest.purpose == purpose)
    if not no_commit:
        db.delete(query)


@db_func
def delete_confirm_email_request(
    db: Session,
    email: str,
    purpose: schemas.ConfirmEmailPurposeLiteral,
):
    delete_confirm_email_request_sync(db, email, purpose, False)


__all__ = [
    "check_is_confirmed_email",
    "create_confirm_email_request",
    "confirm_email_request_sent",
    "get_confirm_email_request",
    "confirm_email",
    "delete_confirm_email_request",
    "delete_confirm_email_request_sync",
    "confirm_user_email",
]
