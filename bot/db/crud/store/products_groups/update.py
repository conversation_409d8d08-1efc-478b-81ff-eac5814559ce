from sqlalchemy import delete, insert, select, update

import schemas
from db import db_func, sess
from db.crud.store.product.update import update_products_group_hashes_sync
from db.crud.translation.update import (
    clear_object_fields_translations_sync,
    update_object_translations_sync,
)
from db.models import (
    StoreCharacteristicValue, StoreProduct, StoreProductGroup,
    StoreProductGroupCharacteristic,
)


@db_func
def update_modifiers_values_in_products(
        data: list[schemas.AdminProductsGroupUpdateProductsModifierSchema]
) -> True:
    product_ids = []
    for item in data:
        product = sess().query(StoreProduct).filter_by(id=item.product_id).first()
        if product:
            update_or_add_modifier_value(
                item.product_id, item.modifier_id, item.modifier_value,
                item.translations
            )
        else:
            print(f"Product with ID {item.product_id} not found.")
        product_ids.append(item.product_id)

    sess().flush()
    update_products_group_hashes_sync(
        product_ids=product_ids,
    )

    sess().commit()
    return True


@db_func
def update_products_group(
        product_group_id: int, data: schemas.AdminProductsGroupCreateSchema
) -> StoreProductGroup | None:
    group = sess().query(StoreProductGroup).get(product_group_id)
    if group:
        group.external_id = data.external_id
        group.external_type = data.external_type
        group.name = data.name
        sess().commit()
        return group
    return None


@db_func
def delete_products_group(products_group_id: int) -> bool:
    products_with_group_id = sess().scalars(
        select(StoreProduct.id)
        .where(StoreProduct.product_group_id == products_group_id)
    ).all()

    sess().execute(
        update(StoreProductGroup)
        .where(StoreProductGroup.id == products_group_id)
        .values({"is_deleted": True})
    )

    if products_with_group_id:
        sess().flush()

        update_products_group_hashes_sync(
            product_ids=products_with_group_id,
        )

    sess().commit()
    return True


@db_func
def add_products_to_products_group_with_modifiers_values(
        group_id: int,
        data: list[schemas.AdminProductsGroupUpdateProductsModifierSchema]
) -> bool:
    product_ids = [item.product_id for item in data]
    products = sess().query(StoreProduct).filter(StoreProduct.id.in_(product_ids)).all()

    product_ids_to_set_group = [
        product.id for product in products
        if product.product_group_id != group_id
    ]
    if product_ids_to_set_group:
        sess().execute(
            update(StoreProduct)
            .values({"product_group_id": group_id})
            .where(StoreProduct.id.in_(product_ids_to_set_group))
        )

    products_dict = {product.id: product for product in products}

    for item in data:
        product = products_dict.get(item.product_id)
        if not product:
            continue
        update_or_add_modifier_value(
            item.product_id, item.modifier_id, item.modifier_value, item.translations
        )

    sess().flush()
    update_products_group_hashes_sync(product_ids=products_dict.keys())

    sess().commit()
    return True


@db_func
def delete_modifier_from_products_group(
        product_group_id: int, modifier_id: int
) -> bool:
    stmt = delete(StoreProductGroupCharacteristic).where(
        StoreProductGroupCharacteristic.product_group_id == product_group_id,
        StoreProductGroupCharacteristic.characteristic_id == modifier_id
    )
    sess().execute(stmt)

    sess().flush()
    update_products_group_hashes_sync(
        product_group_ids=(product_group_id,),
    )

    sess().commit()
    return True


@db_func
def update_modifier_for_products_group(
        product_group_id: int, modifier_id: int, show_one_modification: bool
) -> bool:
    stmt = update(StoreProductGroupCharacteristic).where(
        StoreProductGroupCharacteristic.product_group_id == product_group_id,
        StoreProductGroupCharacteristic.characteristic_id == modifier_id
    ).values(show_one_modification=show_one_modification)
    sess().execute(stmt)

    sess().flush()
    update_products_group_hashes_sync(product_group_ids=(product_group_id,))

    sess().commit()
    return True


@db_func
def add_modifier_to_products_group(product_group_id: int, modifier_id: int) -> bool:
    stmt = insert(StoreProductGroupCharacteristic).values(
        product_group_id=product_group_id,
        characteristic_id=modifier_id,
        is_modifier=True
    )
    sess().execute(stmt)

    sess().flush()
    update_products_group_hashes_sync(product_group_ids=(product_group_id,))

    sess().commit()
    return True


def update_or_add_modifier_value(
        product_id: int, modifier_id: int, modifier_value: str,
        translations: dict[str, dict | None] | None = None
) -> None:
    existing_modifier = sess().query(StoreCharacteristicValue).filter_by(
        characteristic_id=modifier_id,
        product_id=product_id
    ).first()

    if existing_modifier:
        existing_modifier.value = modifier_value
        characteristic_for_translate = existing_modifier
    else:
        new_modifier = StoreCharacteristicValue(
            characteristic_id=modifier_id,
            product_id=product_id,
            value=modifier_value
        )
        sess().add(new_modifier)
        sess().flush()
        characteristic_for_translate = new_modifier

    if characteristic_for_translate and translations:
        updated_translations = {}
        for key, value in translations.items():
            if isinstance(value, schemas.AdminCharacteristicValueTranslationSchema):
                updated_translations[key] = value
            elif value is not None:
                updated_translations[
                    key] = schemas.AdminCharacteristicValueTranslationSchema(
                    **value
                )
            else:
                updated_translations[key] = None

        update_object_translations_sync(
            characteristic_for_translate, updated_translations
        )
    else:
        clear_object_fields_translations_sync(characteristic_for_translate, 'value')
