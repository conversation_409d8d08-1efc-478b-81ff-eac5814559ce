import schemas
from db import db_func, sess
from db.models import (
    Brand, StoreProductGroup,
    User,
)
from ...scope.create import grand_scopes_to_created_object_sync


@db_func
def create_products_group(
        brand: Brand,
        data: schemas.AdminProductsGroupCreateSchema,
        creator: User | None = None,
) -> StoreProductGroup:
    product_group = StoreProductGroup(
        brand=brand,
        **data.dict()
    )
    sess().add(product_group)

    if creator:
        grand_scopes_to_created_object_sync(
            "product_group",
            product_group, creator,
            {"profile_id": brand.group_id},
        )
    sess().commit()
    return product_group
