from typing import Literal

from sqlalchemy import and_, distinct, func, or_, select
from sqlalchemy.sql import Select

from db import db_func, sess
from db.models import (
    BrandCustomSettings, PaymentToShipment, StoreCustomSettings,
    Translation,
)
from schemas.store.types import CustomType, ShipmentType

CustomSettingsWithStoreType = tuple[BrandCustomSettings, StoreCustomSettings | None]
CustomSettingsWithStoreAndTranslationType = tuple[
    BrandCustomSettings, StoreCustomSettings | None, Translation | None]

CustomSettingsElType = BrandCustomSettings | CustomSettingsWithStoreType
CustomSettingsElWithTranslationType = (BrandCustomSettings |
                                       CustomSettingsWithStoreType | \
                                       CustomSettingsWithStoreAndTranslationType)


def get_statement(
        store_id: int | None = None,
        search_text: str | None = None,
        operation: str = "all",
        lang: str | None = None,
        with_translations: bool = True,
        with_store_settings: bool = False,
) -> Select:
    query_objects = []
    if operation == "count":
        query_objects.append(func.count(BrandCustomSettings.id))
    else:
        query_objects.append(BrandCustomSettings)
        if store_id and with_store_settings:
            query_objects.append(StoreCustomSettings)
        if lang and (with_translations or search_text):
            query_objects.append(Translation)

    stmt = select(*query_objects)

    if store_id:
        stmt = stmt.outerjoin(
            StoreCustomSettings,
            and_(
                StoreCustomSettings.store_id == store_id,
                BrandCustomSettings.id == StoreCustomSettings.custom_settings_id,
            )
        )

    if lang and (with_translations or search_text):
        stmt = stmt.outerjoin(
            Translation, Translation.filter(BrandCustomSettings, lang)
        )

    return stmt


def filter_and_execute_statement(
        stmt: Select,
        brand_id: int,
        custom_type: CustomType | tuple[CustomType, ...],
        store_id: int | None = None,
        position: int | None = None,
        limit: int | None = None,
        search_text: str | None = None,
        lang: str | None = None,
        operation: str = "all",
        with_disabled: bool = False,
):
    stmt = stmt.where(
        BrandCustomSettings.brand_id == brand_id,
        BrandCustomSettings.is_deleted.is_(False)
    )
    if isinstance(custom_type, tuple):
        stmt = stmt.where(
            BrandCustomSettings.custom_type.in_(
                (
                    el.value
                    for el in custom_type
                )
            )
        )
    else:
        stmt = stmt.where(BrandCustomSettings.custom_type == custom_type.value)

    if not with_disabled and store_id:
        stmt = stmt.where(
            or_(
                StoreCustomSettings.is_enabled.is_(True),
                and_(
                    BrandCustomSettings.is_enabled.is_(True),
                    StoreCustomSettings.is_enabled.is_(None),
                ),
            )
        )

    if search_text:
        if lang:
            stmt = stmt.where(
                or_(
                    BrandCustomSettings.name.contains(search_text),
                    Translation.get_field_expression("_name").contains(search_text),
                )
            )
        else:
            stmt = stmt.where(BrandCustomSettings.name.contains(search_text))

    if operation == "count":
        result = sess().scalar(stmt)
        if position:
            result -= position
        return result

    stmt = stmt.group_by(BrandCustomSettings.id)
    stmt = stmt.order_by(BrandCustomSettings.id)

    if position:
        stmt = stmt.offset(position)
    if limit:
        stmt = stmt.limit(limit)

    if operation == "one":
        return sess().scalar(stmt)

    res = sess().execute(stmt).all()
    if res and len(res[0]) == 1:
        res = [el[0] for el in res]
    return res


@db_func
def get_base_shipments(
        brand_id: int, store_id: int | None = None,
        with_disabled: bool = False,
        with_store_settings: bool = False,
) -> list[CustomSettingsElType]:
    stmt = get_statement(store_id, with_store_settings=with_store_settings)
    return filter_and_execute_statement(
        stmt,
        brand_id,
        CustomType.SHIPMENT,
        store_id,
        with_disabled=with_disabled,
    )


@db_func
def get_custom_payments(
        brand_id: int,
        shipment_id: int | None = None,
        search_text: str | None = None,
        position: int | None = None,
        limit: int | None = None,
        operation: Literal["all", "count"] = "all",
        lang: str | None = None,
        with_translations: bool = True,
) -> list[CustomSettingsElWithTranslationType] | int:
    stmt = get_statement(None, search_text, operation, lang, with_translations)

    if shipment_id:
        stmt = stmt.join(
            PaymentToShipment, BrandCustomSettings.id == PaymentToShipment.payment_id
        )
        stmt = stmt.filter(PaymentToShipment.shipment_id == shipment_id)

    return filter_and_execute_statement(
        stmt,
        brand_id,
        CustomType.CUSTOM_PAYMENT,
        None,
        position,
        limit,
        search_text,
        lang,
        operation,
    )


@db_func
def get_custom_payment(
        custom_payment_id: int,
        brand_id: int,
) -> CustomSettingsElType | None:
    stmt = get_statement()
    stmt = stmt.where(BrandCustomSettings.id == custom_payment_id)

    return filter_and_execute_statement(
        stmt,
        brand_id,
        CustomType.CUSTOM_PAYMENT,
        operation="one",
        with_disabled=True,
    )


@db_func
def get_custom_shipments(
        brand_id: int,
        store_id: int | None = None,
        search_text: str | None = None,
        custom_shipment_group_id: int | None = None,
        is_rest_shipments: bool = False,
        position: int | None = None,
        limit: int | None = None,
        operation: Literal["all", "count"] = "all",
        lang: str | None = None,
        with_translations: bool = True,
        with_disabled: bool = False,
        with_store_settings: bool = False,
) -> list[CustomSettingsElWithTranslationType] | int:

    if is_rest_shipments and custom_shipment_group_id:
        raise ValueError(
            "Please, specify one of is_rest_shipments, custom_shipment_group_id"
        )

    stmt = get_statement(
        store_id, search_text, operation, lang, with_translations, with_store_settings
    )
    stmt = stmt.where(BrandCustomSettings.is_deleted.is_(False))

    if custom_shipment_group_id:
        stmt = stmt.where(
            BrandCustomSettings.custom_settings_group_id == custom_shipment_group_id
        )
    if is_rest_shipments:
        stmt = stmt.where(
            BrandCustomSettings.custom_settings_group_id.is_(None),
            BrandCustomSettings.is_deleted.is_(False)
        )

    return filter_and_execute_statement(
        stmt,
        brand_id,
        CustomType.CUSTOM_SHIPMENT,
        store_id,
        position,
        limit,
        search_text,
        lang,
        operation,
        with_disabled,
    )


@db_func
def get_custom_shipment_groups(
        brand_id: int,
        store_id: int | None = None,
        search_text: str | None = None,
        position: int | None = None,
        limit: int | None = None,
        operation: Literal["all", "count"] = "all",
        lang: str | None = None,
        with_translations: bool = True,
        with_disabled: bool = False,
        with_store_settings: bool = False,
) -> list[CustomSettingsElWithTranslationType] | int:
    stmt = get_statement(
        store_id, search_text, operation, lang, with_translations, with_store_settings
    )
    return filter_and_execute_statement(
        stmt,
        brand_id,
        CustomType.CUSTOM_SHIPMENT_GROUP,
        store_id,
        position,
        limit,
        search_text,
        lang,
        operation,
        with_disabled,
    )


@db_func
def get_shipment_custom_payments_ids(shipment_id: int) -> list[int]:
    stmt = select(BrandCustomSettings.id)
    stmt = stmt.join(
        PaymentToShipment, PaymentToShipment.payment_id == BrandCustomSettings.id
    )
    stmt = stmt.where(PaymentToShipment.shipment_id == shipment_id)
    return sess().scalars(stmt).fetchall()


@db_func
def get_shipment_methods(brand_id: int) -> set[
    Literal["delivery", "pickup", "in_store"]]:
    stmt = select(StoreCustomSettings)
    stmt = stmt.join(
        BrandCustomSettings,
        BrandCustomSettings.id == StoreCustomSettings.custom_settings_id
    )

    stmt = stmt.filter(BrandCustomSettings.brand_id == brand_id)
    stmt = stmt.filter(BrandCustomSettings.custom_type == CustomType.SHIPMENT.value)

    result = sess().scalars(stmt).fetchall()

    shipment_methods: set[
        Literal["delivery", "pickup", "in_store"]] = set()  # type: ignore

    for shipment in result:
        if shipment.custom_settings.base_type == ShipmentType.DELIVERY.value and (
                shipment.is_enabled or (
                shipment.is_enabled is None and shipment.custom_settings.is_enabled)
        ):
            shipment_methods.add("delivery")
        if shipment.custom_settings.base_type == ShipmentType.PICKUP.value and (
                shipment.is_enabled or (
                shipment.is_enabled is None and shipment.custom_settings.is_enabled)
        ):
            shipment_methods.add("pickup")
        if shipment.custom_settings.base_type == ShipmentType.IN_STORE.value and (
                shipment.is_enabled or (
                shipment.is_enabled is None and shipment.custom_settings.is_enabled)
        ):
            shipment_methods.add("in_store")

    return shipment_methods


@db_func
def get_all_custom_shipments_and_groups(
        brand_id: int,
        search_text: str | None = None,
        operation: Literal["all", "count"] = "all",
        no_groups: bool = False,
) -> list[BrandCustomSettings] | int:
    if operation == "count":
        stmt = select(func.count(distinct(BrandCustomSettings.id)))
    else:
        stmt = select(BrandCustomSettings)
    stmt = stmt.where(
        BrandCustomSettings.custom_type.in_(
            (CustomType.CUSTOM_SHIPMENT.value, CustomType.CUSTOM_SHIPMENT_GROUP.value)
        )
    )
    stmt = stmt.where(BrandCustomSettings.brand_id == brand_id)
    stmt = stmt.where(BrandCustomSettings.is_deleted.is_(False))
    if no_groups:
        stmt = stmt.where(
            BrandCustomSettings.custom_type != CustomType.CUSTOM_SHIPMENT_GROUP.value
        )
    stmt = stmt.where(BrandCustomSettings.custom_settings_group_id.is_(None))

    if search_text:
        stmt = stmt.where(BrandCustomSettings._name.contains(search_text))

    if operation == "count":
        return sess().scalar(stmt)

    return sess().scalars(stmt).all()
