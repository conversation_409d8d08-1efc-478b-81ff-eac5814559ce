from sqlalchemy import select

from db import db_func, sess
from db.models import StoreCustomSettings, ShipmentTimeToSettings, ShipmentTime, BrandCustomSettings


@db_func
def delete_brand_custom_setting(brand_custom_setting_id: int) -> bool:
    stmt = select(StoreCustomSettings)
    stmt = stmt.where(StoreCustomSettings.custom_settings_id == brand_custom_setting_id)
    store_settings = sess().scalars(stmt).fetchall()
    if store_settings:
        for store_setting in store_settings:
            sess().delete(store_setting)
            sess().commit()

    stmt = select(ShipmentTimeToSettings)
    stmt = stmt.where(ShipmentTimeToSettings.settings_id == brand_custom_setting_id)
    shipment_to_times = sess().scalars(stmt).fetchall()
    if shipment_to_times:
        for shipment_to_time in shipment_to_times:
            stmt = select(ShipmentTime)
            stmt = stmt.where(ShipmentTime.id == shipment_to_time.shipment_time_id)
            shipment_times = sess().scalars(stmt).fetchall()
            if shipment_times:
                for shipment_time in shipment_times:
                    sess().delete(shipment_time)
            sess().delete(shipment_to_time)
            sess().commit()

    stmt = select(BrandCustomSettings)
    stmt = stmt.where(BrandCustomSettings.id == brand_custom_setting_id)
    brand_settings = sess().scalar(stmt)

    if brand_settings:
        brand_settings.is_deleted = True
        sess().commit()
        return True
