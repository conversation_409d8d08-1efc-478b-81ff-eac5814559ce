import schemas
from db import db_func, sess
from db.models import PaymentToShipment, BrandCustomSettings
from db.crud.translation.update import clear_object_updated_fields_translations_sync, update_object_translations_sync
from db.models.store.custom_settings import PaymentSettingsToShipment


@db_func
def update_payment_to_shipment(shipment_id: int, payment_id: int, is_enabled: bool | None = None):
    try:
        query = sess().query(PaymentSettingsToShipment)
        query = query.filter(PaymentSettingsToShipment.shipment_id == shipment_id)
        query = query.filter(PaymentSettingsToShipment.payment_settings_id == payment_id)
        payment_to_shipment = query.one_or_none()

        if payment_to_shipment or (is_enabled is not None and not is_enabled):
            if payment_to_shipment:
                sess().delete(payment_to_shipment)
        else:
            payment_to_shipment = PaymentSettingsToShipment(payment_settings_id=payment_id, shipment_id=shipment_id)
            sess().add(payment_to_shipment)

        sess().commit()
    except Exception as ex:
        print(f"*** update_payment_to_shipment {ex}")


@db_func
def update_payment_setting_to_shipment(shipment_id: int, payment_setting_id: int, is_enabled: bool | None = None):
    try:
        query = sess().query(PaymentSettingsToShipment)
        query = query.filter(PaymentSettingsToShipment.shipment_id == shipment_id)
        query = query.filter(PaymentSettingsToShipment.payment_settings_id == payment_setting_id)
        payment_to_shipment = query.all()

        if is_enabled:
            if not payment_to_shipment:
                payment_to_shipment = PaymentSettingsToShipment(
                    payment_settings_id=payment_setting_id, shipment_id=shipment_id
                )
                sess().add(payment_to_shipment)
        else:
            if payment_to_shipment:
                for pts in payment_to_shipment:
                    sess().delete(pts)

        sess().commit()
    except Exception as ex:
        print(f"*** update_payment_to_shipment {ex}")


@db_func
def update_shipment_with_translations(
    shipment: BrandCustomSettings,
    data_dict: dict,
    profile_langs: list[str],
    translations: dict[str, schemas.AdminShipmentTranslationSchema | None] | None = None
):
    if data_dict:
        clear_object_updated_fields_translations_sync(shipment, data_dict, profile_langs)
        shipment.update_sync(data_dict, no_commit=True)

    if translations:
        update_object_translations_sync(shipment, translations)

    sess().commit()
