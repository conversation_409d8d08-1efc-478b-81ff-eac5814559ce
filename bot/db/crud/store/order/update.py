import logging
from typing import Literal

from incust_api.api import term

import schemas
from db import db_func, sess
from db.models import (
    BillingSettings, BrandCustomSettings, ExternalOrder, Invoice, OrderShipment,
    ShipmentPrice, StoreOrder, StoreOrderBillingAddress,
)
from .create import create_custom_shipment

logger = logging.getLogger('debugger')


@db_func
def update_incust_check(order_id: int, check: term.m.Check | dict) -> bool:
    order = sess().query(StoreOrder).filter(StoreOrder.id == order_id).one_or_none()
    if order:
        order.incust_loyalty_check = check.dict() if isinstance(
            check, term.m.Check
        ) else check
        sess().commit()
        return True

    return False


@db_func
def update_incust_export_data(
        order_id: int, response: dict | str, is_first_stage: bool,
        actual_check: term.m.Check | None = None
) -> bool:
    order = sess().query(StoreOrder).filter(StoreOrder.id == order_id).one_or_none()
    if order:
        if actual_check:
            order.original_incust_loyalty_check = actual_check.dict(exclude_unset=True)
        if not order.incust_export:
            export_data = {
                "first_stage": response if is_first_stage else "",
                "second_stage": response if not is_first_stage else "",
            }
            order.incust_export = export_data
        else:
            export_data = {
                "first_stage": order.incust_export["first_stage"],
                "second_stage": order.incust_export["second_stage"],
            }
            if is_first_stage:
                export_data["first_stage"] = response
            else:
                export_data["second_stage"] = response
            order.incust_export = export_data

        if not is_first_stage and isinstance(response, dict):
            invoice = sess().query(Invoice).filter(
                Invoice.id == order.invoice_id
            ).one_or_none()
            if invoice:
                check = actual_check.dict(
                    exclude_unset=True
                ) if actual_check else invoice.incust_check
                if not check:
                    check = order.original_incust_loyalty_check
                    if not check:
                        check = {}
                check.update(transaction=response)
                invoice.incust_check = check

        sess().commit()

        return True

    return False


@db_func
def save_store_order_token(order_id: int, order_token: str) -> bool:
    order = sess().query(StoreOrder).filter(StoreOrder.id == order_id).one_or_none()
    if order:
        order.token = order_token
        sess().commit()
        return True
    return False


@db_func
def set_invoice_to_store_order(order_id: int, invoice: Invoice) -> bool:
    order = sess().query(StoreOrder).filter(StoreOrder.id == order_id).one_or_none()
    if order:
        order.item = invoice
        order.total_sum_with_extra_fee = invoice.total_sum_with_extra_fee
        order.sum_to_pay = invoice.sum_to_pay
        sess().commit()
        return True
    return False


async def set_external_order(
        store_order: StoreOrder,
        external_order_id: str,
        external_type: Literal['get_order', 'poster'],
        json_data: dict | None = None,
        status: str | None = None,
):

    external_order = await ExternalOrder.create_or_update(
        brand_id=store_order.store.brand_id,
        order_id=store_order.id,
        external_order_id=external_order_id,
        external_type=external_type,
        json_data=json_data,
        status=status,
    )

    if external_order:
        return True
    return False


@db_func
def update_order(
        store_order: StoreOrder,
        data: schemas.UpdateOrderSchema,
        shipment_price: ShipmentPrice | None,
        shipment_method: BrandCustomSettings,
        billing_settings: BillingSettings | None,
) -> tuple[StoreOrder, StoreOrderBillingAddress | None, OrderShipment | None]:
    billing_address = data.billing_address

    data_dict = data.dict(exclude_unset=True)
    print(f"*** DATA DICT {data_dict}")
    store_order.update_sync(
        delivery_address=data.delivery_address,
        address_comment=data.address_comment,
        desired_delivery_date=data.desired_delivery_date,
        desired_delivery_time=data.desired_delivery_time,
        address_street=data.address_street,
        address_house=data.address_house,
        address_flat=data.address_flat,
        address_floor=data.address_floor,
        address_entrance=data.address_entrance,
        comment=data.comment,
        address_lat=data.address_lat,
        address_lng=data.address_lng,
        address_place_id=data.address_place_id,
    )

    current_order_shipment = OrderShipment.get_sync(store_order_id=store_order.id)
    if current_order_shipment.settings_id == shipment_method.id:
        if current_order_shipment.comment != data.custom_shipment_comment:
            current_order_shipment.comment = data.custom_shipment_comment
    else:
        shipment_method = create_custom_shipment(
            store_order, shipment_price,
            shipment_method,
            data.custom_shipment_comment,
        )
        if current_order_shipment:
            current_order_shipment.delete_sync()

    if billing_address:
        if billing_settings and billing_settings.is_require:
            current_billing_address = StoreOrderBillingAddress.get_sync(
                order_id=store_order.id
            )
            if current_billing_address:
                current_billing_address.update_sync(**billing_address.dict())
            else:
                billing_address = StoreOrderBillingAddress(
                    order=store_order,
                    **billing_address.dict()
                )

    modified_price_after_loyalty: int = 0
    order_total_sum: int = 0

    if shipment_method and not shipment_method.is_paid_separately and shipment_price:
        modified_price_after_loyalty += shipment_price.raw_price
        order_total_sum += shipment_price.raw_price

    store_order.before_loyalty_sum = order_total_sum

    store_order.sum_to_pay = store_order.total_sum
    if store_order.tips_sum:
        store_order.sum_to_pay += store_order.tips_sum

    return store_order, billing_address, shipment_method
