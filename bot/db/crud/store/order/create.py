import logging

from incust_api.api import term

import schemas
from config import DEFAULT_TIME_ZONE
from db import crud, db_func, sess
from db.crud.store.product.read import get_store_product_spot_price_sync
from db.models import (
    BrandCustomSettings, OrderAttribute, OrderProduct, OrderShipment,
    ShipmentPrice, Store, StoreAttribute, StoreCartProduct, StoreOrder,
    StoreOrderBillingAddress, StoreProduct, User,
)
from schemas import CreateOrderProduct

logger = logging.getLogger('debugger.order_create')


async def create_order_object(
        user: User,
        store: Store,
        data: schemas.CreateOrderSchema,
        shipment_price: ShipmentPrice | None,
        shipment_method: BrandCustomSettings,
        timezone: str = DEFAULT_TIME_ZONE,
        cart_id: int | None = None,
) -> tuple[StoreOrder, StoreOrderBillingAddress | None, OrderShipment | None]:
    billing_address = data.billing_address

    o_bonuses = data.bonuses_redeemed
    o_discount = data.discount
    b_and_d = 0
    if o_bonuses and o_discount:
        b_and_d = o_bonuses + o_discount
    else:
        if o_bonuses:
            b_and_d = o_bonuses
        if o_discount:
            b_and_d = o_discount

    if not user.is_anonymous:
        cart_id = None

    store_order = StoreOrder(
        brand_name=store.brand.name,
        delivery_address=data.delivery_address,
        first_name=data.first_name,
        last_name=data.last_name,
        phone=data.phone,
        email=data.email,
        user=user,
        store=store,
        status="open_unconfirmed",
        status_pay="must_pay",
        address_comment=data.address_comment,
        desired_delivery_date=data.desired_delivery_date,
        desired_delivery_time=data.desired_delivery_time,
        address_street=data.address_street,
        address_house=data.address_house,
        address_flat=data.address_flat,
        address_floor=data.address_floor,
        address_entrance=data.address_entrance,
        comment=data.comment,
        bonuses_redeemed=data.bonuses_redeemed or 0,
        loyalty_type=data.loyalty_type,
        discount=data.discount or 0,
        discount_and_bonuses=b_and_d,
        currency=store.currency,
        menu_in_store_id=data.menu_in_store_id,
        tips_sum=data.tips_sum or 0,
        address_lat=data.address_lat,
        address_lng=data.address_lng,
        address_place_id=data.address_place_id,
        type=data.type,
        timezone=timezone,
        cart_id=cart_id,
        utm_labels=data.utm_labels.dict() if data.utm_labels else None,
    )

    shipment_method = create_custom_shipment(
        store_order, shipment_price,
        shipment_method,
        data.custom_shipment_comment,
    )

    if billing_address:
        billing_address = StoreOrderBillingAddress(
            order=store_order,
            **billing_address.dict()
        )

    modified_price_after_loyalty: int = 0
    order_total_sum: int = 0

    if shipment_method and not shipment_method.is_paid_separately and shipment_price:
        modified_price_after_loyalty += shipment_price.raw_price
        order_total_sum += shipment_price.raw_price

    for product_schema in data.products:
        product_schema = CreateOrderProduct(**product_schema.dict())
        product = StoreProduct.get_sync(product_schema.product_id)

        order_product = await make_order_product(
            product,
            product_schema,
            store_order,
            data.incust_check if data.incust_check else None,
        )

        if product_schema.attributes:
            for attribute_schema in product_schema.attributes:
                attribute = await StoreAttribute.get(attribute_schema.attribute_id)
                attribute_group = attribute.attribute_group

                order_product.attributes.append(
                    OrderAttribute(
                        name=attribute.name,
                        group_name=attribute_group.name,
                        price_impact=attribute.price_impact,
                        quantity=attribute_schema.quantity,
                        attribute=attribute,
                        group_attribute=attribute_group,
                    )
                )

        store_order.order_products.append(order_product)
        order_total_sum += order_product.before_loyalty_sum

    if data.price_after_loyalty or data.price_after_loyalty == 0:
        store_order.total_sum = int(
            data.incust_check.amount_to_pay * 100 + modified_price_after_loyalty
        )
    else:
        store_order.total_sum = order_total_sum

    store_order.before_loyalty_sum = order_total_sum

    store_order.sum_to_pay = store_order.total_sum

    return store_order, billing_address, shipment_method


async def make_order_product(
        product: StoreProduct,
        cart_product: CreateOrderProduct | StoreCartProduct,
        store_order: StoreOrder,
        incust_check: term.m.Check | None = None,
) -> OrderProduct:
    if spot_prices := get_store_product_spot_price_sync(
            store_order.store.id, product.id
    ):
        (product.price, product.old_price) = spot_prices

    price_with_attributes = cart_product.floating_sum or product.price

    for attr in cart_product.attributes:
        price_with_attributes += attr.price_impact * attr.quantity

    # loyalty logic start
    discount_amount = 0
    product_price_after_loyalty = 0
    product_bonuses_redeemed = 0
    discount_sum = 0
    product_bonuses_redeemed_sum = 0

    discount_and_bonuses = 0
    discount_and_bonuses_sum = 0
    if incust_check:
        for incust_product in incust_check.check_items:
            # check price if multiple products with the same code
            price_equal_check = True
            if product.attribute_groups and len(product.attribute_groups) > 0:
                if not round(price_with_attributes / 100, 2) == incust_product.price:
                    price_equal_check = False
            if incust_product.code == product.product_id and price_equal_check:
                discount_amount = incust_product.calculated_unit_discount_amount * 100
                product_price_after_loyalty = incust_product.calculated_price * 100
                discount_sum = incust_product.calculated_discount_amount * 100

                if incust_product.calculated_unit_bonuses_redeemed_amount:
                    product_bonuses_redeemed = (
                            incust_product.calculated_unit_bonuses_redeemed_amount *
                            100)
                if incust_product.calculated_bonuses_redeemed_amount:
                    product_bonuses_redeemed_sum = (
                            incust_product.calculated_bonuses_redeemed_amount * 100)

                discount_and_bonuses = product_bonuses_redeemed + discount_amount
                discount_and_bonuses_sum = ((
                                                    product_bonuses_redeemed +
                                                    discount_amount) *
                                            cart_product.quantity)
    # loyalty logic end

    incust_card_number = None
    incust_account = None
    loyalty_settings_id = None
    topup_charge = 0
    charge_percent = 0.0  # TODO: temporary disabled
    charge_fixed = 0.0  # TODO: temporary disabled
    if product.type == "topup":
        # Отримуємо loyalty_settings для продукту topup
        group = await crud.get_group_by_brand_id(product.brand_id)
        loyalty_settings = await crud.get_loyalty_settings_for_context(
            "product",
            schemas.LoyaltySettingsData(product_id=product.id, profile_id=group.id),
        )
        if loyalty_settings:
            loyalty_settings_id = loyalty_settings.id
            incust_card_number = cart_product.incust_card
            incust_account = cart_product.incust_account

    final_price = (
            price_with_attributes - discount_amount - product_bonuses_redeemed)
    # + topup_charge

    order_product = OrderProduct(
        name=product.name,
        quantity=cart_product.quantity,
        price=cart_product.floating_sum or product.price,
        product=product,
        store_order=store_order,
        price_with_attributes=price_with_attributes,  # topup_charge
        final_price=final_price,
        discount_amount=discount_amount,
        price_after_loyalty=product_price_after_loyalty - product_bonuses_redeemed,
        bonuses_redeemed=product_bonuses_redeemed,
        before_loyalty_sum=price_with_attributes * cart_product.quantity,
        # topup_charge
        discount_sum=discount_sum,
        bonuses_redeemed_sum=product_bonuses_redeemed_sum,
        total_sum=final_price * cart_product.quantity,
        discount_and_bonuses=discount_and_bonuses,
        discount_and_bonuses_sum=discount_and_bonuses_sum,
        display_name=cart_product.display_name,
        display_description=cart_product.display_description,
        incust_card=incust_card_number,
        incust_account=incust_account.dict() if incust_account else incust_account,
        topup_charge=0,  # topup_charge,
        charge_percent=0,
        charge_fixed=0,
        loyalty_settings_id=loyalty_settings_id,
    )

    return order_product


def create_custom_shipment(
        store_order: StoreOrder,
        shipment_price: ShipmentPrice | None,
        shipment: BrandCustomSettings,
        shipment_comment: str | None = None,
) -> OrderShipment:
    created_shipment = None

    if shipment:
        created_shipment = OrderShipment(
            store_order=store_order,
            settings=shipment,
            name=shipment.name,
            base_type=shipment.base_type,
            price=shipment_price.price if shipment_price else 0,
            comment=shipment_comment,
            is_paid_separately=shipment.is_paid_separately,
            delivery_datetime_mode=shipment.delivery_datetime_mode,
        )

    return created_shipment


@db_func
def save_store_order(
        store_order: StoreOrder,
        billing_address: StoreOrderBillingAddress | None,
        shipment: OrderShipment | None
):
    sess().add(store_order)

    if billing_address:
        sess().add(billing_address)
    if shipment:
        sess().add(shipment)

    sess().commit()
    return store_order
