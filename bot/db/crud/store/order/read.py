import logging
from datetime import date, datetime, timedelta
from typing import Any, Literal

from sqlalchemy import (
    and_, case, desc, func, literal, literal_column, not_, or_,
    select, text,
)
from sqlalchemy.engine import Row
from sqlalchemy.orm import Query, aliased, joinedload
from sqlalchemy.sql import label

import schemas
from config import INBOX_MAX_AGE
from core.payment.exceptions import (
    LiqpayRroIdProductError, LiqpayRroIdShipmentError,
    LiqpayRroIdTipsError,
)
from db import db_func, sess
from db.crud.helpers import (
    crm_params_filter, get_inbox_status_sort_stmt,
    user_field_if_not_anonymous,
)
from db.crud.helpers.crm import (
    crm_filter_by_inbox_cursor, crm_scope_filter, get_crm_list,
    get_inbox_status_sort_value,
    get_inbox_type_sort_stmt,
)
from db.models import (
    Brand, BrandCustomSettings, ClientBot, ExternalOrder, Group, MenuInStore,
    OrderAttribute,
    OrderCustomPayment,
    OrderProduct, OrderShipment, OrderShippingStatus, PaymentSettings, Review, Scope,
    Store, StoreAttribute, StoreOrder,
    StoreProduct, Tag, User,
)
from db.types.operation import Operation
from schemas import DesiredDeliveryDateCursor, InboxCursor
from schemas.db.cursor import IDCursor

liqpay_debugger = logging.getLogger('debugger.liqpay.fiscal')


@db_func
def get_order(
        order_id: int,
        action: str | None = None,
        user_id: int | None = None
) -> StoreOrder | None:
    stmt = select(StoreOrder)
    stmt = stmt.where(StoreOrder.id == order_id)
    stmt = stmt.where(StoreOrder._status != "new")
    stmt = stmt.where(StoreOrder._status != "terminated_by_user")

    if any((action, user_id)) and not all((action, user_id)):
        raise ValueError("Specify both action and user_id or none of them")

    if action and user_id:
        stmt = stmt.join(
            Group, StoreOrder.store.has(Store.brand.has(group_id=Group.id))
        )
        stmt = stmt.where(Group.status == "enabled")
        stmt = stmt.where(
            Scope.filter_for_action(
                action, "user", user_id,
                {
                    "profile_id": Group.id,
                    "store_id": StoreOrder.store_id,
                    "order_id": StoreOrder.id,
                }
            )
        )

    stmt = stmt.group_by(StoreOrder.id)

    return sess().scalar(stmt)


@db_func
def get_order_products(
        order_id: int,
        with_products: bool = False,
        load_products: bool = False,
        load_product_thumbnail_media: bool = False,
        load_attributes: bool = False,
) -> list[OrderProduct] | list[tuple[OrderProduct, StoreProduct]]:
    query_objects = [OrderProduct]
    if with_products:
        query_objects.append(StoreProduct)
    query: Query = sess().query(*query_objects)

    if with_products:
        query = query.join(StoreProduct, OrderProduct.product_id == StoreProduct.id)

    # loading related records to use them by orm properties later

    if load_products or load_product_thumbnail_media:
        query = query.options(joinedload(OrderProduct.product))
        if load_product_thumbnail_media:
            query = query.options(joinedload(StoreProduct.thumbnail_media))

    if load_attributes:
        query = query.options(
            joinedload(OrderProduct.attributes).joinedload(OrderAttribute.attribute)
        )

    query = query.filter(OrderProduct.store_order_id == order_id)
    return query.all()  # type: ignore


@db_func
def get_order_product_attributes(order_product_id: int) -> list[
    tuple[OrderAttribute, StoreAttribute]]:
    query = sess().query(OrderAttribute, StoreAttribute)
    query = query.join(StoreAttribute, OrderAttribute.attribute_id == StoreAttribute.id)
    query = query.filter(OrderAttribute.order_product_id == order_product_id)
    return query.all()


@db_func
def get_order_by_external_order(
        brand_id,
        ext_order_number,
        type_external_order,
) -> ExternalOrder:

    order = sess().query(StoreOrder).join(
        ExternalOrder, ExternalOrder.order_id == StoreOrder.id
    ). \
        filter(ExternalOrder.external_order_id == ext_order_number). \
        filter(ExternalOrder.external_type == type_external_order). \
        filter(ExternalOrder.brand_id == brand_id).one_or_none()

    if not order:
        raise Exception(
            f'Order for brand_id {brand_id} and external order {ext_order_number} not '
            f'found'
        )

    return order


@db_func
def get_store_order_id_by_invoice(
        invoice_id: int,
) -> int | None:

    query = sess().query(StoreOrder.id)
    query = query.filter(StoreOrder.invoice_id == invoice_id)
    return query.scalar()


@db_func
def get_order_shipment(order_id: int) -> OrderShipment:
    stmt = select(OrderShipment)
    stmt = stmt.where(OrderShipment.store_order_id == order_id)
    return sess().scalar(stmt)


@db_func
def get_order_custom_payment(order_id: int) -> OrderCustomPayment:
    stmt = select(OrderCustomPayment)
    stmt = stmt.where(OrderCustomPayment.store_order_id == order_id)
    return sess().scalar(stmt)


@db_func
def get_order_fiscal_items(
        brand_id: int,
        order_id: int,
) -> list[dict[str, Any]]:
    query = sess().query(
        StoreProduct.liqpay_id,
        OrderProduct.quantity,
        OrderProduct.final_price,
        OrderProduct.total_sum,
        StoreOrder.sum_to_pay,
        OrderProduct.name,
    )

    query = query.join(StoreProduct, OrderProduct.product_id == StoreProduct.id)
    query = query.join(StoreOrder, StoreOrder.id == OrderProduct.store_order_id)

    query = query.filter(
        OrderProduct.store_order_id == order_id,
        # StoreProduct.liqpay_id.is_not(None),
        StoreOrder.id == order_id,
        OrderProduct.final_price.is_not(None),
        OrderProduct.final_price > 0,
    )

    products = []
    for liqpay_id, quantity, final_price, total_sum, sum_to_pay, name in query.all():
        if not liqpay_id:
            raise LiqpayRroIdProductError(name)
        products.append(
            {
                "id": int(liqpay_id) if liqpay_id else None,
                "amount": quantity,
                "price": round(final_price / 100, 2),
                "cost": round(total_sum / 100, 2)
            }
        )
        liqpay_debugger.debug(f"{products=}")

    query = sess().query(
        BrandCustomSettings.liqpay_id,
        OrderShipment._price,
        func.concat(
            OrderShipment.base_type,
            case(
                [(OrderShipment.name != '', literal(': '))],
                else_=''
            ),
            OrderShipment.name
        ).label('name_base_type')
    )

    query = query.join(
        BrandCustomSettings, BrandCustomSettings.id == OrderShipment.settings_id
    )

    query = query.filter(
        OrderShipment._price > 0,
        OrderShipment.store_order_id == order_id,
        # BrandCustomSettings.liqpay_id.is_not(None),
        BrandCustomSettings.allow_online_payment == 1,
        OrderShipment.is_paid_separately == 0,
        OrderShipment._price.is_not(None),
        OrderShipment._price > 0,
    )

    deliveries = []
    for liqpay_id, _price, name in query.all():
        if not liqpay_id:
            raise LiqpayRroIdShipmentError(name)
        deliveries.append(
            {
                "id": int(liqpay_id) if liqpay_id else None,
                "amount": 1,
                "price": round(_price / 100, 2),
                "cost": round(_price / 100, 2),
            }
        )
        liqpay_debugger.debug(f"{deliveries=}")

    tips = []
    order_tips: StoreOrder = (sess().query(
        StoreOrder.tips_sum
    ).filter(
        StoreOrder.id == order_id,
        StoreOrder.tips_sum.is_not(None),
        StoreOrder.tips_sum > 0,
    )).one_or_none()

    if order_tips:
        payment_settings: PaymentSettings = (sess().query(PaymentSettings).filter(
            PaymentSettings.payment_method == 'liqpay',
            PaymentSettings.brand_id == brand_id,
        )).one_or_none()
        if payment_settings:
            liqpay_id = payment_settings.json_data.get('tips_liqpay_id')
            if not liqpay_id:
                raise LiqpayRroIdTipsError()

            tips.append(
                {
                    "id": int(liqpay_id),
                    "amount": 1,
                    "price": round(order_tips.tips_sum / 100, 2),
                    "cost": round(order_tips.tips_sum / 100, 2),
                }

            )
            liqpay_debugger.debug(f"{tips=}")

    return products + deliveries + tips


def get_crm_order_list_statement(
        user_id: int,
        params: schemas.CRMOrderListParams | None = None,
        operation: Operation = "list",
        cursor: IDCursor | DesiredDeliveryDateCursor | None = None,
        inbox_cursor: InboxCursor | None = None,
        for_inbox: bool = False,
        is_platform_admin: bool | None = None,
):
    inbox_status = case(
        [
            (OrderShippingStatus.status == "open_unconfirmed",
             text(f'"{schemas.InboxStatus.NEW.value}"')),
            (OrderShippingStatus.status.in_(
                (
                    "open_confirmed",
                    "wait_for_ship"
                )
            ),
             text(f'"{schemas.InboxStatus.IN_PROGRESS.value}"')),
            (OrderShippingStatus.status.in_(
                (
                    "shipped",
                    "in_transit",
                    "delivered"
                )
            ), text(f'"{schemas.InboxStatus.DELIVERING.value}"')),
            (OrderShippingStatus.status.in_(
                (
                    "closed",
                    "canceled",
                )
            ), text(f'"{schemas.InboxStatus.RECENT.value}"'))
        ],
    )
    inbox_status_sort = get_inbox_status_sort_stmt(inbox_status)

    if operation == "count":
        stmt = select(
            func.count(StoreOrder.id)
        )
    else:
        stmt = select(
            literal_column(f"'{schemas.InboxType.ORDER.value}'").label("inbox_type"),
            inbox_status.label("inbox_status"),
            inbox_status_sort,
            get_inbox_type_sort_stmt(schemas.InboxType.ORDER),
            OrderShippingStatus.time_created.label("change_date"),
            StoreOrder.desired_delivery_date,
            StoreOrder.id,
            literal_column("NULL").label("is_pending"),
            StoreOrder.type,
            literal_column("NULL").label("privacy"),
            literal_column("NULL").label("text"),
            literal_column("NULL").label("additional_text"),
            literal_column("NULL").label("media"),
            StoreOrder.crm_tag.label("crm_tag"),
            literal_column("NULL").label("invoice_type"),
            StoreOrder.status,
            StoreOrder.status_pay,
            OrderShippingStatus.status.label("current_status"),
            OrderShipment.name.label("shipment_name"),
            OrderShipment.base_type.label("shipment_type"),
            StoreOrder.first_name,
            StoreOrder.last_name,
            StoreOrder.full_name,
            StoreOrder.email,
            StoreOrder.phone,
            user_field_if_not_anonymous(User.id, "photo_url", User.photo_url),
            StoreOrder.currency,
            user_field_if_not_anonymous(StoreOrder.user_id).label("user_id"),
            Store.id.label("store_id"),
            Store.name.label("store_name"),
            literal_column("NULL").label("ticket_title"),
            Group.id.label("profile_id"),
            Group.name.label("profile_name"),
            Store.name.label("business_name"),
            literal_column("NULL").label("bot_name"),
            func.round(StoreOrder.before_loyalty_sum / 100, 2).label(
                "before_loyalty_sum"
            ),
            func.round(StoreOrder.discount / 100, 2).label("discount"),
            func.round(StoreOrder.bonuses_redeemed / 100, 2).label("bonuses_redeemed"),
            func.round(StoreOrder.discount_and_bonuses / 100, 2).label(
                "discount_and_bonuses_redeemed"
            ),
            func.round(StoreOrder.total_sum / 100, 2).label("total_sum"),
            func.round(StoreOrder.tips_sum / 100, 2).label("tips_sum"),
            func.round(StoreOrder.sum_to_pay / 100, 2).label("sum_to_pay"),
            func.round(StoreOrder.payer_fee / 100, 2).label("payer_fee"),
            func.round(StoreOrder.paid_sum / 100, 2).label("paid_sum"),
            StoreOrder.menu_in_store_id,
            MenuInStore.comment.label("menu_in_store_comment"),
            literal_column("NULL").label("title"),
            literal_column("NULL").label("mark"),
            label(
                "items_text", (
                    select(
                        func.group_concat(
                            func.concat(
                                OrderProduct.name, " x", OrderProduct.quantity
                            ).op(
                                "SEPARATOR"
                            )(text('","')),
                        )
                    ).select_from(OrderProduct)
                    .where(OrderProduct.store_order_id == StoreOrder.id)
                )
            ),
            literal_column("NULL").label("last_message_text"),
            literal_column("NULL").label("last_message_content_type"),
            literal_column("NULL").label("last_message_media_url"),
            literal_column("NULL").label("last_message_media_mime_type"),
            literal_column("NULL").label("last_message_content"),
            literal_column("NULL").label("is_read"),
            literal_column("NULL").label("read_by_user_id"),
            StoreOrder.comment.label("user_comment"),
            StoreOrder.time_created.label("time_created"),
        )
        stmt = stmt.outerjoin(User, StoreOrder.user_id == User.id)
        stmt = stmt.outerjoin(
            MenuInStore, StoreOrder.menu_in_store_id == MenuInStore.id
        )
        stmt = stmt.join(OrderShipment, OrderShipment.store_order_id == StoreOrder.id)

    stmt = stmt.join(
        OrderShippingStatus, OrderShippingStatus.store_order_id == StoreOrder.id
    )

    aliased_status = aliased(OrderShippingStatus)
    last_order_shipping_status_date = select(
        func.max(aliased_status.time_created)
    ).where(
        aliased_status.store_order_id == StoreOrder.id,
        aliased_status.status != "payed",
    ).scalar_subquery()
    stmt = stmt.where(
        OrderShippingStatus.time_created == last_order_shipping_status_date
    )

    stmt = stmt.join(Store, StoreOrder.store_id == Store.id)
    stmt = stmt.join(Brand, Store.brand_id == Brand.id)
    stmt = stmt.join(Group, Brand.group_id == Group.id)

    stmt = stmt.filter(Group.status == "enabled")

    stmt = crm_filter_by_inbox_cursor(
        stmt, schemas.InboxType.ORDER, inbox_cursor,
        OrderShippingStatus.time_created, StoreOrder.id, inbox_status_sort
    )

    if inbox_cursor:
        if inbox_cursor.type == schemas.InboxType.ORDER:
            object_conditions = (
                OrderShippingStatus.time_created <= inbox_cursor.change_date,
                StoreOrder.id < inbox_cursor.id,
            )
        else:
            object_conditions = (
                OrderShippingStatus.time_created < inbox_cursor.change_date,
            )

        stmt = stmt.where(
            or_(
                inbox_status_sort > get_inbox_status_sort_value(inbox_cursor.status),
                and_(
                    inbox_status_sort == get_inbox_status_sort_value(
                        inbox_cursor.status
                    ),
                    *object_conditions,
                )
            ),
        )

    if for_inbox:
        stmt = stmt.where(
            OrderShippingStatus.time_created >= datetime.utcnow() - INBOX_MAX_AGE
        )

    stmt = crm_scope_filter(
        stmt, "order", user_id,
        StoreOrder.id, is_platform_admin=is_platform_admin,
    )

    stmt = crm_params_filter(
        stmt, StoreOrder, params, cursor, operation, filter_by_statuses=False
    )
    if params:
        if params.statuses:
            stmt = stmt.where(
                OrderShippingStatus.status.in_(params.statuses)
            )
        if params.is_paid is True:
            stmt = stmt.where(StoreOrder.status_pay == "payed")
        elif params.is_paid is False:
            stmt = stmt.where(StoreOrder.status_pay != "payed")
    return stmt


@db_func
def get_crm_order_list(
        user_id: int,
        params: schemas.CRMOrderListParams | None = None,
        operation: Operation = "list",
        cursor: IDCursor | DesiredDeliveryDateCursor | None = None,
) -> list[Row] | int:
    stmt = get_crm_order_list_statement(user_id, params, operation, cursor)

    order_by_direction: Literal["asc", "desc"]
    match params.sort:
        case schemas.CRMOrdersSort.ID:
            order_by_fields = "id"
            order_by_direction = "desc"
        case schemas.CRMOrdersSort.DESIRED_DELIVERY_DATE:
            order_by_fields = ("desired_delivery_date", "id")
            order_by_direction = "asc"
        case _:
            raise ValueError("Invalid sort value")

    return get_crm_list(
        stmt, StoreOrder,
        operation, params, cursor,
        order_by_fields, order_by_direction,
    )


@db_func
def get_store_orders_for_export(
        manager_user_id: int,
        store_order_status: list[str] | None = None,
        locations: list[str] = None,
        bots: list[int] = None,
        groups: list[int] = None,
        tags: list[int] = None,
        search_text: str = None,
        user_id: int = None,
        group_id: int = None,
        date_from: date | None = None,
        date_to: date | None = None,
) -> list[tuple[StoreOrder, tuple]] | list[StoreOrder] | StoreOrder | int:
    query = sess().query(StoreOrder).distinct()

    query = query.join(Store, StoreOrder.store)
    query = query.join(Brand, Store.brand)
    query = query.join(Group, Brand.group)

    query = query.filter(
        Scope.filter_for_action(
            "crm_order:read",
            "user", manager_user_id,
            {
                "order_id": StoreOrder.id,
                "group_id": Group.id,
                "store_id": Store.id,
            }
        )
    )

    query = query.filter(Group.status == "enabled")
    query = query.filter(~Store.external_type.in_(["poster", "get_order"]))

    if store_order_status:
        query = query.filter(StoreOrder._status.in_(store_order_status))
    else:
        query = query.filter(~StoreOrder._status.in_(["closed", "canceled"]))

    if date_from:
        query = query.filter(func.date(StoreOrder.create_date) >= date_from)
    if date_to:
        query = query.filter(func.date(StoreOrder.create_date) <= date_to)

    if user_id:
        query = query.filter(StoreOrder.user_id == user_id)

    if group_id:
        query = query.filter(Group.id == group_id)

    if groups:
        query = query.filter(Group.id.in_(groups))

    if locations:
        query = query.filter(Group.location.in_(locations))

    if bots:
        query = query.join(ClientBot, Brand.group_id == ClientBot.group_id)
        query = query.filter(ClientBot.status == "enabled")
        query = query.filter(ClientBot.id.in_(bots))

    if tags or search_text:
        query = query.join(StoreOrder.user)

        if tags:
            query = query.join(User.tags)
            query = query.filter(Tag.id.in_(tags))

        if search_text:
            query = query.filter(
                or_(
                    StoreOrder.id == search_text,
                    User.search(search_text),
                )
            )

    if search_text:
        order_by_condition = case(
            [
                (StoreOrder.id == search_text, 5),
                (User.search(search_text), 3),
            ]
        )
        query = query.order_by(desc(order_by_condition))

    query = query.order_by(StoreOrder.create_date.desc())

    return query.all()


@db_func
def get_transactions_count(profile_id, data: schemas.DateRangeSchema) -> int | None:
    stmt = select(
        func.count(StoreOrder.id)
    ).where(
        StoreOrder.status != "canceled",
        or_(StoreOrder.status_pay == "payed", StoreOrder.status == "closed"),
        StoreOrder.create_date >= data.start_date,
        StoreOrder.create_date <= data.end_date,
    )
    stmt = stmt.join(StoreOrder.store)
    stmt = stmt.join(Store.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.filter(Group.status == "enabled", Group.id == profile_id)
    return sess().scalar(stmt)


@db_func
def get_turnover(profile_id, data: schemas.DateRangeSchema) -> int | float | None:
    stmt = select(
        func.sum(StoreOrder.sum_to_pay)
    ).where(
        StoreOrder.status != "canceled",
        or_(StoreOrder.status_pay == "payed", StoreOrder.status == "closed"),
        StoreOrder.create_date >= data.start_date,
        StoreOrder.create_date <= data.end_date,
    )
    stmt = stmt.join(StoreOrder.store)
    stmt = stmt.join(Store.brand)
    stmt = stmt.join(Brand.group)
    stmt = stmt.filter(Group.status == "enabled", Group.id == profile_id)

    return sess().scalar(stmt)


@db_func
def get_reviews_count(
        group_id: int, data: schemas.DateRangeSchema
) -> int | None:
    stmt = select(func.count(Review.id)).where(
        Review.group_id == group_id,
        # Review.privacy == "public",
        Review.time >= data.start_date,
        Review.time <= data.end_date,
    )

    return sess().scalar(stmt)


@db_func
def get_average_review_rating(
        group_id: int, data: schemas.DateRangeSchema
) -> float:

    avg_rating = sess().query(func.avg(Review.review["stars"])). \
        filter(
        not_(Review.review["stars"].is_(None)),
        Review.group_id == group_id,
        # Review.privacy == "public",
        Review.time >= data.start_date,
        Review.time <= data.end_date,
    ).scalar()

    if avg_rating is None:
        avg_rating = 0.0

    return avg_rating


@db_func
def get_order_users_count(
        profile_id, data: schemas.DateRangeSchema
) -> int | float | None:
    stmt = select(
        func.count(func.distinct(StoreOrder.user_id))
    ).where(
        StoreOrder.status != "canceled",
        StoreOrder.status != "new",
        StoreOrder.status != "terminated_by_user",
        or_(StoreOrder.status_pay == "payed", StoreOrder.status == "closed"),
        StoreOrder.create_date >= data.start_date,
        StoreOrder.create_date <= data.end_date,
    )
    stmt = stmt.join(StoreOrder.store)
    stmt = stmt.join(Store.brand)
    stmt = stmt.join(Brand.group)
    stmt = stmt.filter(Group.status == "enabled", Group.id == profile_id)

    return sess().scalar(stmt)


@db_func
def get_new_orders_for_cancel_transaction(limit: int, hours: int) -> list["StoreOrder"]:
    hours_ago = datetime.utcnow() - timedelta(hours=hours)

    stmt = select(StoreOrder)

    stmt = stmt.where(StoreOrder.status == "new")
    stmt = stmt.where(StoreOrder.loyalty_type == "incust")
    stmt = stmt.where(StoreOrder.create_date < hours_ago)
    stmt = stmt.where(StoreOrder.auto_cancelled_loyalty_transaction.is_(False))

    stmt = stmt.limit(limit)

    return sess().scalars(stmt).all()


@db_func
def get_order_data(store_order: StoreOrder):
    """Оптимізована версія, яка зменшує кількість запитів до БД."""

    # Виконуємо запит з JOIN-ами для отримання всіх необхідних даних одночасно
    query = sess().query(
        Store, Brand, Group, ClientBot, User, MenuInStore
    ).outerjoin(
        Brand, Store.brand_id == Brand.id
    ).outerjoin(
        Group, Brand.group_id == Group.id
    ).outerjoin(
        ClientBot, Group.id == ClientBot.group_id
    ).outerjoin(
        User, User.id == store_order.user_id
    ).outerjoin(
        MenuInStore, MenuInStore.id == store_order.menu_in_store_id
    ).filter(
        Store.id == store_order.store_id
    )

    result = query.first()

    if not result:
        store = Store.get_sync(store_order.store_id)
        brand = Brand.get_sync(store.brand_id)
        group = Group.get_sync(brand.group_id)
        bot = ClientBot.get_sync(group_id=group.id)
        user = User.get_sync(store_order.user_id)
        menu_in_store = MenuInStore.get_sync(
            store_order.menu_in_store_id
        ) if store_order.menu_in_store_id else None
        return bot, brand, group, menu_in_store, store, user

    store, brand, group, bot, user, menu_in_store = result
    return bot, brand, group, menu_in_store, store, user
