from typing import Iterable

from sqlalchemy import desc, select

import schemas
from db import db_func, sess
from db.models import OrderShippingStatus, User


@db_func
def get_order_statuses(order_id: int) -> list[OrderShippingStatus]:
    query = sess().query(OrderShippingStatus)
    query = query.filter(OrderShippingStatus.store_order_id == order_id)
    query = query.order_by(desc(OrderShippingStatus.time_created))
    return query.all()


@db_func
def get_crm_order_status_history(
        order_id: int,
) -> list[schemas.CRMOrderStatusHistoryObject]:
    stmt = select(
        OrderShippingStatus.id,
        OrderShippingStatus.status,
        OrderShippingStatus.comment,
        OrderShippingStatus.source,
        OrderShippingStatus.initiated_by,
        OrderShippingStatus.initiated_by_user_id,
        OrderShippingStatus.time_created.label("set_datetime"),
        User.name.label("initiated_by_user_name"),
        User.email.label("initiated_by_email"),
    )
    stmt = stmt.outerjoin(OrderShippingStatus.initiated_by_user)
    stmt = stmt.where(OrderShippingStatus.store_order_id == order_id)
    stmt = stmt.order_by(OrderShippingStatus.time_created)
    stmt = stmt.order_by(OrderShippingStatus.id)

    data = sess().execute(stmt).fetchall()
    return [schemas.CRMOrderStatusHistoryObject.from_orm(el) for el in data]


@db_func
def get_last_order_status(
        order_id: int, exclude: Iterable[str] | None = None
) -> OrderShippingStatus | None:
    query = sess().query(OrderShippingStatus)
    query = query.filter(OrderShippingStatus.store_order_id == order_id)
    if exclude:
        query = query.filter(OrderShippingStatus.status.not_in(exclude))

    query = query.order_by(desc(OrderShippingStatus.time_created))
    return query.first()
