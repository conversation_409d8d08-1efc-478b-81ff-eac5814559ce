from sqlalchemy import select

from db import db_func, sess
from db.crud.store.product.update import update_products_group_hashes_sync
from db.models import StoreCharacteristic, StoreProduct


@db_func
def delete_characteristic(characteristic: StoreCharacteristic):
    characteristic.is_deleted = True

    product_ids = sess().scalars(
        select(StoreProduct.id)
        .where(StoreProduct.characteristics.any(characteristic_id=characteristic.id))
    )

    if product_ids:
        sess().flush()
        update_products_group_hashes_sync(
            product_ids=product_ids,
        )
    sess().commit()
