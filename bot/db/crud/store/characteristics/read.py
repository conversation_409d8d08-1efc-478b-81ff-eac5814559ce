from typing import Iterable

from sqlalchemy import and_, distinct, exists, func, not_, or_, select
from sqlalchemy.engine import Row
from sqlalchemy.orm import aliased
from sqlalchemy.sql import Select

from db import db_func, sess
from db.mixins import BaseDBModel
from db.models import (
    Brand, Group, ProductToCategory, ProductToStore, Scope, StoreCategory,
    StoreCategoryFilter, StoreCharacteristic,
    StoreCharacteristicFilterSetting, StoreCharacteristicValue, StoreProduct,
    StoreProductGroup,
    StoreProductGroupCharacteristic, Translation,
)


@db_func
def get_characteristic_values(
        characteristic_id: int, store_id: int, lang: str,
        category_id: int | None = None,
) -> list[StoreCharacteristicValue] | list[
    tuple[StoreCharacteristicValue, Translation]]:
    query = sess().query(StoreCharacteristicValue, Translation).distinct()
    query = query.outerjoin(
        Translation, Translation.filter(StoreCharacteristicValue, lang)
    )

    query = query.filter(StoreCharacteristicValue.value.is_not(None))
    query = query.filter(
        StoreCharacteristicValue.characteristic_id == characteristic_id
    )

    query = query.join(StoreCharacteristicValue.product)
    query = query.filter(StoreProduct.is_deleted.is_(False))
    # changed from `StoreProduct.is_available.is_(True)` to
    # `StoreProduct.is_enabled.is_(True)`
    query = query.filter(StoreProduct.is_enabled.is_(True))

    query = query.join(ProductToStore, ProductToStore.product_id == StoreProduct.id)
    query = query.filter(ProductToStore.store_id == store_id)

    if category_id:
        query = query.join(
            ProductToCategory, ProductToCategory.product_id == StoreProduct.id
        )
        query = query.filter(
            or_(
                ProductToCategory.category_id == category_id,
                ProductToCategory.category_id.in_(
                    StoreCategory.get_child_categories_ids_query(category_id)
                )
            )
        )

    query = query.group_by(StoreCharacteristicValue.value)

    return query.all()


@db_func
def get_modifier_values(
        product_group_id: int,
        characteristic_id: int,
        store_id: int,
        lang: str | None = None,
        with_translations: bool = True,
) -> list[tuple[StoreCharacteristicValue, bool]] | \
     list[tuple[StoreCharacteristicValue, bool, Translation]]:
    query_objects = [StoreCharacteristicValue, func.max(StoreProduct.is_available)]
    if lang and with_translations:
        query_objects.append(Translation)

    query = sess().query(*query_objects).distinct()

    if lang and with_translations:
        query = query.outerjoin(
            Translation, Translation.filter(StoreCharacteristicValue, lang)
        )

    query = query.filter(
        StoreCharacteristicValue.characteristic_id == characteristic_id
    )

    query = query.join(StoreCharacteristicValue.product)
    query = query.filter(StoreProduct.product_group_id == product_group_id)
    query = query.filter(StoreProduct.is_deleted.is_(False))

    query = query.filter(StoreProduct.stores.any(id=store_id))

    query = query.order_by(StoreProduct.position.is_(None))
    query = query.order_by(StoreProduct.position)

    query = query.group_by(StoreCharacteristicValue.value)

    return query.all()  # type: ignore


def get_product_characteristics_sync(
        product_id: int | Iterable[int],
        lang: str | None = None,
        modifiers: bool = False,
        with_translations: bool = True,
        is_hide: bool = False,
        for_list: bool = False,
) -> list[
         tuple[
             StoreCharacteristic,
             StoreCharacteristicValue,
             Translation | None,
             Translation | None
         ]
     ] | list[
         tuple[
             StoreProduct,
             StoreCharacteristic,
             StoreCharacteristicValue,
             Translation | None,
             Translation | None
         ]
     ] | list[tuple[StoreCharacteristic, StoreCharacteristicValue]]:
    characteristic_translation = aliased(Translation)
    characteristic_value_translation = aliased(Translation)

    query_objects = []
    if for_list:
        query_objects.append(StoreProduct)

    query_objects.extend((StoreCharacteristic, StoreCharacteristicValue))

    if lang and with_translations:
        query_objects += [characteristic_translation, characteristic_value_translation]

    query = sess().query(*query_objects)
    query = query.filter(StoreCharacteristic.is_hide.is_(is_hide))

    query = query.join(StoreCharacteristic.values)
    query = query.join(StoreCharacteristicValue.product)

    if lang and with_translations:
        query = query.outerjoin(
            characteristic_translation,
            characteristic_translation.filter(StoreCharacteristic, lang),
        )
        query = query.outerjoin(
            characteristic_value_translation,
            characteristic_value_translation.filter(StoreCharacteristicValue, lang),
        )

    if isinstance(product_id, int):
        query = query.filter(StoreProduct.id == product_id)
    elif isinstance(product_id, list):
        query = query.filter(StoreProduct.id.in_(product_id))

    query = query.filter(StoreProduct.is_deleted.is_(False))
    query = query.filter(StoreCharacteristic.is_deleted.is_(False))

    if modifiers:
        query = query.join(StoreProduct.product_group)
        query = query.join(
            StoreProductGroupCharacteristic, and_(
                StoreProductGroupCharacteristic.characteristic_id ==
                StoreCharacteristic.id,
                StoreProductGroupCharacteristic.product_group_id ==
                StoreProductGroup.id,
            )
        )
        query = query.filter(StoreProductGroupCharacteristic.is_modifier.is_(True))
    else:
        subq = sess().query(StoreProductGroupCharacteristic.id)
        subq = subq.filter(
            StoreProductGroupCharacteristic.characteristic_id == StoreCharacteristic.id
        )
        subq = subq.filter(
            StoreProductGroupCharacteristic.product_group_id ==
            StoreProduct.product_group_id
        )
        subq = subq.filter(StoreProductGroupCharacteristic.is_modifier.is_(True))

        query = query.filter(not_(subq.exists()))

    query = query.group_by(StoreCharacteristic.id)

    query = query.order_by(StoreCharacteristic.position.is_(None))
    query = query.order_by(StoreCharacteristic.position)
    query = query.order_by(StoreCharacteristic.name)
    query = query.order_by(StoreCharacteristic.id)

    return query.all()  # type: ignore


@db_func
def get_product_characteristics(
        product_id: int | None = None,
        lang: str | None = None,
        modifiers: bool = False,
        with_translations: bool = True,
        is_hide: bool = False,
        for_list: bool = False,
) -> list[
         tuple[
             StoreCharacteristic,
             StoreCharacteristicValue,
             Translation | None,
             Translation | None
         ]
     ] | list[
         tuple[
             StoreProduct,
             StoreCharacteristic,
             StoreCharacteristicValue,
             Translation | None,
             Translation | None
         ]
     ] | list[tuple[StoreCharacteristic, StoreCharacteristicValue]]:
    return get_product_characteristics_sync(
        product_id, lang, modifiers, with_translations, is_hide, for_list
    )


def get_category_filters_sync(
        category_id: int,
        lang: str | None = None,
        with_translations: bool = True
) -> list[StoreCharacteristic] | list[tuple[StoreCharacteristic, Translation | None]]:
    if lang and with_translations:
        query = sess().query(StoreCharacteristic, Translation).distinct()
        query = query.outerjoin(
            Translation, Translation.filter(StoreCharacteristic, lang)
        )
    else:
        query = sess().query(StoreCharacteristic).distinct()

    query = query.filter(StoreCharacteristic.categories.any(id=category_id))

    query = query.filter(StoreCharacteristic.is_deleted.is_(False))

    query = query.order_by(StoreCharacteristic.position.is_(None))
    query = query.order_by(StoreCharacteristic.position)
    query = query.order_by(StoreCharacteristic.excel_row_number)
    query = query.order_by(StoreCharacteristic.name)

    return query.all()


@db_func
def get_category_filters(
        category_id: int,
        lang: str | None = None,
        with_translations: bool = True
) -> list[StoreCharacteristic] | list[tuple[StoreCharacteristic, Translation | None]]:
    return get_category_filters_sync(category_id, lang, with_translations)


@db_func
def get_product_group_characteristics(
        product_group_id: int,
        is_modifier: bool | None = None,
        show_one_modification: bool | None = None,
):
    query = sess().query(StoreCharacteristic).distinct()
    query = query.join(
        StoreProductGroupCharacteristic,
        StoreProductGroupCharacteristic.characteristic_id
        == StoreCharacteristic.id,
    )

    if is_modifier is not None:
        query = query.filter(
            StoreProductGroupCharacteristic.is_modifier.is_(is_modifier)
        )
    if show_one_modification is not None:
        query = query.filter(
            StoreProductGroupCharacteristic.show_one_modification.is_(
                show_one_modification
            )
        )

    query = query.filter(StoreCharacteristic.is_deleted.is_(False))

    query = query.filter(
        StoreProductGroupCharacteristic.product_group_id == product_group_id
    )

    query = query.order_by(StoreCharacteristic.position.is_(None))
    query = query.order_by(StoreCharacteristic.position)
    query = query.order_by(StoreCharacteristic.excel_row_number)
    query = query.order_by(StoreCharacteristic.name)

    return query.all()


@db_func
def get_characteristic_filters(
        brand_id: int, store_id: int | None,
        lang: str | None = None,
        with_translations: bool = True
) -> list[StoreCharacteristic] | list[tuple[StoreCharacteristic, Translation | None]]:
    if lang and with_translations:
        query = sess().query(StoreCharacteristic, Translation).distinct()
        query = query.outerjoin(
            Translation, Translation.filter(StoreCharacteristic, lang)
        )
    else:
        query = sess().query(StoreCharacteristic).distinct()

    query = query.join(
        StoreCharacteristicFilterSetting,
        StoreCharacteristicFilterSetting.characteristic_id == StoreCharacteristic.id
    )

    query = query.filter(StoreCharacteristic.brand_id == brand_id)
    query = query.filter(StoreCharacteristic.is_deleted.is_(False))

    if store_id:
        query = query.filter(StoreCharacteristicFilterSetting.store_id == store_id)
    else:
        query = query.filter(StoreCharacteristicFilterSetting.store_id.is_(None))

    query = query.order_by(StoreCharacteristic.position.is_(None))
    query = query.order_by(StoreCharacteristic.position)
    query = query.order_by(StoreCharacteristic.excel_row_number)
    query = query.order_by(StoreCharacteristic.name)

    return query.all()


@db_func
def get_admin_characteristic_filters(
        brand_id: int,
        store_id: int | None,
        category_id: int | None = None,
) -> list[StoreCharacteristic]:
    query = sess().query(StoreCharacteristic).distinct()

    query = query.filter(
        StoreCharacteristic.brand_id == brand_id,
        StoreCharacteristic.is_deleted.is_(False)
    )

    if category_id is not None:
        query = query.join(
            StoreCategoryFilter,
            StoreCategoryFilter.characteristic_id == StoreCharacteristic.id
        ).filter(
            StoreCategoryFilter.category_id == category_id
        )

    elif store_id is not None:
        query = query.join(
            StoreCharacteristicFilterSetting,
            StoreCharacteristicFilterSetting.characteristic_id ==
            StoreCharacteristic.id
        ).filter(
            StoreCharacteristicFilterSetting.store_id == store_id
        )

    else:
        query = query.join(
            StoreCharacteristicFilterSetting,
            StoreCharacteristicFilterSetting.characteristic_id == StoreCharacteristic.id
        )
        if store_id is not None:
            query = query.filter(
                StoreCharacteristicFilterSetting.store_id == store_id
            )
        else:
            query = query.filter(
                StoreCharacteristicFilterSetting.store_id.is_(None)
            )

    query = query.order_by(
        StoreCharacteristic.position.is_(None),
        StoreCharacteristic.position,
        StoreCharacteristic.excel_row_number,
        StoreCharacteristic.name
    )

    return query.all()


@db_func
def get_characteristic_filters_with_limit(
        brand_id: int,
        offset: int | None = None,
        limit: int | None = None,
        search_text: str = "",
) -> list[StoreCharacteristic]:

    query = sess().query(StoreCharacteristic).distinct().filter(
        StoreCharacteristic.brand_id == brand_id,
        StoreCharacteristic.is_deleted.is_(False),
    )

    if search_text:
        query = query.filter(StoreCharacteristic.name.ilike(f"%{search_text}%"))

    query = query.order_by(
        StoreCharacteristic.position.is_(None),
        StoreCharacteristic.position,
        StoreCharacteristic.excel_row_number,
        StoreCharacteristic.name
    )

    if offset is not None:
        query = query.offset(offset)
    if limit is not None:
        query = query.limit(limit)

    return query.all()


@db_func
def get_characteristic_by_id_and_profile_id(
        characteristic_id: int, profile_id: int
) -> StoreCharacteristic | None:
    stmt: Select = select(StoreCharacteristic)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(StoreCharacteristic.id == characteristic_id)
    stmt = stmt.where(StoreCharacteristic.is_deleted.is_(False))

    return sess().scalar(stmt)


@db_func
def get_admin_characteristics_list(
        profile_id: int,
        user_id: int,
        search_text: str | None = None,
        exclude: list[int] | None = None,
        include: list[int] | None = None,
        offset: int | None = None,
        limit: int | None = None,
        need_check_access: bool = False,
        is_count: bool = False,
        exclude_from_product_group_id: int | None = None,
) -> list[Row] | int:
    if is_count:
        stmt: Select = select(func.count(distinct(StoreCharacteristic.id)))
        if need_check_access:
            stmt = stmt.where(
                Scope.filter_for_action(
                    "characteristic:read",
                    "user", user_id,
                    {
                        "profile_id": profile_id,
                        "characteristic_id": StoreCategory.id,
                    }
                )
            )

    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="characteristic",
            target="user",
            target_id=user_id,
            available_data={
                "profile_id": profile_id,
                "characteristic_id": StoreCharacteristic.id,
            }
        )

        stmt: Select = select(
            StoreCharacteristic.id,
            StoreCharacteristic.name,
            StoreCharacteristic.position,
            StoreCharacteristic.is_hide,
            StoreCharacteristic.filter_type,
            StoreCharacteristic.excel_row_number,
            read_allowed, edit_allowed,
        )
        stmt = stmt.distinct()

        if need_check_access:
            stmt = stmt.where(read_allowed.is_(True))

    stmt = stmt.join(StoreCharacteristic.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")

    stmt = stmt.where(StoreCharacteristic.is_deleted.is_(False))

    if exclude_from_product_group_id:
        stmt = stmt.where(
            not_(
                exists().where(
                    and_(
                        StoreProductGroupCharacteristic.characteristic_id ==
                        StoreCharacteristic.id,
                        StoreProductGroupCharacteristic.product_group_id ==
                        exclude_from_product_group_id,
                    )
                )
            )
        )

    if exclude:
        stmt = stmt.where(StoreCharacteristic.id.not_in(exclude))

    if include:
        stmt = stmt.where(StoreCharacteristic.id.in_(include))

    if search_text:
        stmt = stmt.where(StoreCharacteristic.name.contains(search_text))

    if is_count:
        return sess().scalar(stmt)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    stmt = stmt.order_by(StoreCharacteristic.position.is_(None))
    stmt = stmt.order_by(StoreCharacteristic.position)

    stmt = stmt.order_by(StoreCharacteristic.excel_row_number.is_(None))
    stmt = stmt.order_by(StoreCharacteristic.excel_row_number)

    stmt = stmt.order_by(StoreCharacteristic.id.desc())

    return sess().execute(stmt).fetchall()


@db_func
def get_admin_product_characteristics_list(
        profile_id: int,
        user_id: int,
        product_id: int,
) -> list[Row]:
    read_allowed, edit_allowed = Scope.allowed_scopes_list(
        "read", "edit",
        object_name="characteristic",
        target="user",
        target_id=user_id,
        available_data={
            "profile_id": profile_id,
            "characteristic_id": StoreCharacteristic.id,
        }
    )

    stmt: Select = select(
        StoreCharacteristic.id,
        StoreCharacteristic.name,
        StoreCharacteristic.position,
        StoreCharacteristic.filter_type,
        StoreCharacteristic.is_hide,
        StoreCharacteristic.excel_row_number,
        read_allowed, edit_allowed,
    )
    stmt = stmt.distinct()

    stmt = stmt.join(StoreCharacteristic.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")

    stmt = stmt.join(StoreCharacteristicValue)
    stmt = stmt.add_columns(
        StoreCharacteristicValue.id.label("value_id"),
        StoreCharacteristicValue.value
    )

    stmt = stmt.where(StoreCharacteristicValue.product_id == product_id)

    stmt = stmt.where(StoreCharacteristic.is_deleted.is_(False))

    stmt = stmt.order_by(StoreCharacteristic.position.is_(None))
    stmt = stmt.order_by(StoreCharacteristic.position)

    stmt = stmt.order_by(StoreCharacteristic.excel_row_number.is_(None))
    stmt = stmt.order_by(StoreCharacteristic.excel_row_number)

    stmt = stmt.order_by(StoreCharacteristic.id.desc())

    return sess().execute(stmt).fetchall()


@db_func
def get_characteristic_values_list(
        profile_id: int,
        characteristic_id: int,
        product_group_id: int | None = None
) -> list[BaseDBModel]:
    stmt = select(StoreCharacteristicValue).group_by(StoreCharacteristicValue.value)

    if product_group_id:
        stmt = stmt.join(
            StoreProductGroupCharacteristic,
            StoreCharacteristicValue.characteristic_id ==
            StoreProductGroupCharacteristic.characteristic_id
        ).join(StoreCharacteristic.brand).join(Brand.group)

        stmt = stmt.where(
            Group.status == 'enabled',
            Group.id == profile_id,
            StoreProductGroupCharacteristic.product_group_id == product_group_id
        )
    else:
        stmt = stmt.join(StoreCharacteristic.brand).join(Brand.group)
        stmt = stmt.where(
            Group.status == 'enabled',
            Group.id == profile_id,
            StoreCharacteristicValue.characteristic_id == characteristic_id
        )

    stmt = stmt.where(StoreCharacteristicValue.value.isnot(None))

    result = sess().execute(stmt).scalars().all()
    return result
