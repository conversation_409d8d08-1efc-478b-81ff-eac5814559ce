import json

import schemas
from db import db_func, sess
from db.crud.scope.create import grand_scopes_to_created_object_sync
from db.crud.store.product.update import update_products_group_hashes_sync
from db.models import (
    Brand, StoreCharacteristic, StoreCharacteristicFilter,
    StoreCharacteristicFiltersSet, StoreProductGroupCharacteristic, User,
)


@db_func
def create_filters_set(
        data: schemas.CreateFiltersData
) -> StoreCharacteristicFiltersSet | None:
    if not any(
            data.filters.values()
    ) and data.min_price is None and data.max_price is None:
        return None

    query = sess().query(StoreCharacteristicFiltersSet)
    query = query.join(StoreCharacteristicFiltersSet.filters)

    for characteristic_id, filter_data in data.filters.items():
        if not filter_data:
            continue

        query = query.filter(
            StoreCharacteristicFilter.filter_type == filter_data.filter_type
        )
        query = query.filter(StoreCharacteristicFilter.value == filter_data.value)
        query = query.filter(
            StoreCharacteristicFilter.values_list == json.dumps(filter_data.values_list)
        )
        query = query.filter(
            StoreCharacteristicFilter.range_min == filter_data.range_min
        )
        query = query.filter(
            StoreCharacteristicFilter.range_max == filter_data.range_max
        )

    if data.min_price is not None:
        query = query.filter(StoreCharacteristicFiltersSet.min_price == data.min_price)
    else:
        query = query.filter(StoreCharacteristicFiltersSet.min_price.is_(None))

    if data.max_price is not None:
        query = query.filter(StoreCharacteristicFiltersSet.max_price == data.max_price)
    else:
        query = query.filter(StoreCharacteristicFiltersSet.max_price.is_(None))

    filters_set = query.all()

    if not filters_set:
        filters_set = StoreCharacteristicFiltersSet(
            filters=[
                StoreCharacteristicFilter(
                    characteristic_id=characteristic_id,
                    **json.loads(filter_data.json())
                )
                for characteristic_id, filter_data in data.filters.items()
                if filter_data
            ],
            min_price=data.min_price,
            max_price=data.max_price,
        )
        sess().add(filters_set)
        sess().commit()

    if filters_set:
        return filters_set[0] if isinstance(filters_set, list) else filters_set
    return None


@db_func
def create_characteristic(
        brand: Brand,
        data: schemas.AdminCreateCharacteristicData,
        creator: User | None = None,
        add_to_product_group_id: int | None = None,
) -> StoreCharacteristic:
    characteristic = StoreCharacteristic(
        brand=brand,
        **data.dict(exclude_unset=True, exclude={"stores", "translations"}),
    )
    sess().add(characteristic)

    if creator:
        grand_scopes_to_created_object_sync(
            "characteristic", characteristic, creator, {
                "profile_id": brand.group_id,
            }
        )
    sess().commit()

    if add_to_product_group_id:
        group_characteristic = StoreProductGroupCharacteristic(
            product_group_id=add_to_product_group_id,
            characteristic_id=characteristic.id,
            is_modifier=True,
            show_one_modification=False,
        )
        sess().add(group_characteristic)

        sess().flush()
        update_products_group_hashes_sync(product_group_ids=(add_to_product_group_id,))

        sess().commit()

    return characteristic
