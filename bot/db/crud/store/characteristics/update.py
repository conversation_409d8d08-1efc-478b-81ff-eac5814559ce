import schemas
from db import db_func, sess
from db.crud.translation.update import (
    clear_object_updated_fields_translations_sync,
    update_object_translations_sync,
)
from db.models import (
    StoreCategoryFilter, StoreCharacteristic,
)


@db_func
def update_characteristic(
        characteristic: StoreCharacteristic,
        data: schemas.AdminUpdateCharacteristicData,
        profile_langs: list[str],
):
    characteristic_object_data = data.dict(
        exclude_unset=True, exclude={"translations", "show_one_modification"}
    )
    if hasattr(data, 'filter_type') and data.filter_type is not None:
        filter_type_str = data.filter_type.value[-1] if data.filter_type else None
        characteristic_object_data["filter_type"] = filter_type_str

    if characteristic_object_data:
        clear_object_updated_fields_translations_sync(
            characteristic, characteristic_object_data, profile_langs
        )
        characteristic.update_sync(characteristic_object_data, no_commit=True)

    if hasattr(data, 'translations') and data.translations is not None:
        update_object_translations_sync(characteristic, data.translations)

    sess().commit()

    return characteristic


@db_func
def update_category_filters(
        category_id: int,
        connected_filters: list[schemas.AdminFilterItemSchema] | None,
):
    new_items = connected_filters or []
    incoming_ids = {item.id for item in new_items}

    existing_links = (
        sess()
        .query(StoreCategoryFilter)
        .filter_by(category_id=category_id)
        .all()
    )
    existing_ids = {link.characteristic_id for link in existing_links}

    for link in existing_links:
        if link.characteristic_id not in incoming_ids:
            sess().delete(link)

    for item in new_items:
        if item.id not in existing_ids:
            sess().add(
                StoreCategoryFilter(
                    category_id=category_id,
                    characteristic_id=item.id
                )
            )

    if incoming_ids:
        characteristics = (
            sess()
            .query(StoreCharacteristic)
            .filter(StoreCharacteristic.id.in_(incoming_ids))
            .all()
        )
        char_map = {c.id: c for c in characteristics}

        for item in new_items:
            ch = char_map.get(item.id)
            if not ch:
                continue

            if item.filter_type and ch.filter_type != item.filter_type.value:
                ch.filter_type = item.filter_type.value

            if ch.is_hide != item.is_hide:
                ch.is_hide = item.is_hide

    sess().commit()
