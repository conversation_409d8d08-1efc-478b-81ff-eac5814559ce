import logging
from copy import deepcopy

from sqlalchemy import select

import schemas
from config import NOT_FULL_UPD_PAYMENT_METHODS
from db import db_func, sess
from db.crud.translation.update import clear_object_updated_fields_translations_sync, update_object_translations_sync
from db.models import PaymentSettings

logger = logging.getLogger()


@db_func
def update_payment_settings(
    payment_settings: PaymentSettings,
    data: dict | None = None,
) -> PaymentSettings:
    payment_settings.json_data = data
    sess().commit()
    return payment_settings


@db_func
def new_update_payment_settings(
    payment_settings_id: int,
    is_enabled: bool,
    profile_langs: list[str],
    name: str | None = None,
    description: str | None = None,
    with_comment: bool = False,
    data: dict | None = None,
    label_comment: str | None = None,
    post_payment_info: str | None = None,
    translations: dict[str, schemas.AdminShipmentTranslationSchema | None] | None = None
) -> PaymentSettings:
    stmt = select(PaymentSettings).where(PaymentSettings.id == payment_settings_id)
    result = sess().execute(stmt)
    payment_settings = result.scalar()

    if payment_settings.payment_method in NOT_FULL_UPD_PAYMENT_METHODS:
        json_data_for_upd = deepcopy(payment_settings.json_data)
        json_data_for_upd.update({k: v for k, v in data.items() if v is not None and v != ""})
    else:
        json_data_for_upd = data

    payment_settings.update_sync(
        is_enabled=is_enabled,
        name=name,
        description=description,
        with_comment=with_comment,
        json_data=json_data_for_upd,
        label_comment=label_comment,
        post_payment_info=post_payment_info,
        no_commit=True,
    )

    data_dict: dict = {
        "name": name,
        "description": description,
    }
    clear_object_updated_fields_translations_sync(payment_settings, data_dict, profile_langs)

    if translations:
        update_object_translations_sync(payment_settings, translations)

    sess().commit()

    return payment_settings
