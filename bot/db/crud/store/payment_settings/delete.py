from sqlalchemy import select

from db import db_func, sess
from db.models import ObjectPaymentSettings, PaymentSettings


@db_func
def delete_payment_settings(payment_settings_id: int):
    stmt = select(PaymentSettings).where(PaymentSettings.id == payment_settings_id)
    result = sess().execute(stmt)
    payment_settings = result.scalar()

    payment_settings.update_sync(is_deleted=True, no_commit=True)

    stmt = select(ObjectPaymentSettings).where(ObjectPaymentSettings.payment_settings_id == payment_settings_id)
    result = sess().execute(stmt)
    object_payment_settings = result.scalars().all()

    for object_payment_setting in object_payment_settings:
        object_payment_setting.update_sync(is_deleted=True, no_commit=True)

    sess().commit()

    return True
