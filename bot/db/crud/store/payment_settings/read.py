import logging
from typing import Literal

from sqlalchemy import select
from sqlalchemy.sql import func

from config import ALL_PAYMENT_METHODS, HIDDEN_PAYMENTS
from core.payment.exceptions import PaymentSettingsNotFoundError
from db import db_func, sess
from db.models import (
    InvoiceTemplate, ObjectPaymentSettings, Payment, PaymentSettings,
    PaymentSettingsMerchantData, PaymentSettingsToShipment, Translation,
)
from db.models.store.store import Store
from schemas.payment_settings.schemas_add import ObjectPaymentSettingsTarget

logger = logging.getLogger("debugger.payments")


@db_func
def get_payment_methods(
        brand_id: int,
        with_translations: bool = False,
        store_id: int | None = None,
        lang: str | None = None,
        operation: Literal["all", "count"] = "all",
        for_payment_list: bool | None = False,
        payment_settings_id: int | None = None,
        show_hidden: bool = False,
        shipment_id: int | None = None,
        invoice_template_id: int | None = None,
        only_online: bool = False,
) -> list[PaymentSettings] | list[tuple[PaymentSettings, ObjectPaymentSettings]] | list[
    tuple[PaymentSettings, Translation | None]] | list[
         tuple[PaymentSettings, Translation | None, ObjectPaymentSettings]] | int:
    query_objects = [PaymentSettings]
    if with_translations and lang:
        query_objects.append(Translation)
    if store_id or invoice_template_id:
        query_objects.append(ObjectPaymentSettings)
    else:
        query_objects.append(None)

    if store_id and invoice_template_id:
        store_id = None

    stmt = select(*query_objects).distinct()

    if with_translations and lang:
        stmt = stmt.outerjoin(Translation, Translation.filter(PaymentSettings, lang))

    if store_id or invoice_template_id:
        stmt = stmt.outerjoin(
            ObjectPaymentSettings,
            ObjectPaymentSettings.payment_settings_id == PaymentSettings.id
        )

    if shipment_id:
        stmt = stmt.outerjoin(
            PaymentSettingsToShipment,
            PaymentSettingsToShipment.payment_settings_id == PaymentSettings.id,
            PaymentSettingsToShipment.shipment_id == shipment_id
        )
        stmt = stmt.where(
            PaymentSettingsToShipment.shipment_id == shipment_id
        )

    stmt = stmt.where(
        PaymentSettings.is_deleted.is_(False),
        PaymentSettings.brand_id == brand_id,
        PaymentSettings.payment_method.in_(ALL_PAYMENT_METHODS),
    )

    if only_online:
        stmt = stmt.where(PaymentSettings.is_online.is_(True))

    if for_payment_list:
        stmt = stmt.where(
            PaymentSettings.is_enabled.is_(True),
        )

    if payment_settings_id:
        stmt = stmt.where(
            PaymentSettings.id == payment_settings_id
        )
    if not show_hidden:
        stmt = stmt.where(
            PaymentSettings.payment_method not in HIDDEN_PAYMENTS,
        )

    if store_id:
        stmt = stmt.where(
            ObjectPaymentSettings.store_id == store_id
        )
        if for_payment_list:
            stmt = stmt.where(
                ObjectPaymentSettings.is_enabled.is_(True),
            )
    if invoice_template_id:
        stmt = stmt.where(
            ObjectPaymentSettings.invoice_template_id == invoice_template_id
        )
        if for_payment_list:
            stmt = stmt.where(
                ObjectPaymentSettings.is_enabled.is_(True),
            )

    stmt = stmt.order_by(PaymentSettings.position)
    stmt = stmt.order_by(PaymentSettings.name)
    stmt = stmt.group_by(PaymentSettings.id)

    result = sess().execute(stmt)
    if operation == "count":
        return len(result.scalars().all())

    if not store_id and not with_translations:
        return result.scalars().all()

    return result.all()


@db_func
def get_payment_data(
        payment_settings_id: int,
        payment_method: str | None = None,
        brand_id: int | None = None,
        store_id: int | None = None,
        invoice_template_id: int | None = None,
) -> dict:
    query = sess().query(
        PaymentSettings.json_data, PaymentSettings.payment_method
    ).filter(
        PaymentSettings.id == payment_settings_id,
        # func.json_length(PaymentSettings.json_data) > 0
    )

    if brand_id:
        query = query.filter(PaymentSettings.brand_id == brand_id, )
    if payment_method:
        query = query.filter(PaymentSettings.payment_method == payment_method, )

    payment_setting = query.one_or_none()

    if not payment_setting:
        raise PaymentSettingsNotFoundError(f"{payment_settings_id=}, {payment_method=}")

    result = payment_setting.json_data

    if not store_id and not invoice_template_id:
        return result or {}

    query = sess().query(
        ObjectPaymentSettings.json_data
    ).filter(
        ObjectPaymentSettings.payment_settings_id == payment_settings_id,
        # func.json_length(ObjectPaymentSettings.json_data) > 0
    )

    if store_id:
        query = query.filter(ObjectPaymentSettings.store_id == store_id)
    if invoice_template_id:
        query = query.filter(
            ObjectPaymentSettings.invoice_template_id == invoice_template_id
        )

    object_payment_setting = query.one_or_none()

    if object_payment_setting and object_payment_setting.json_data:
        result = object_payment_setting.json_data

    return result or {}


@db_func
def get_store_payment_data(
        payment_type: str,
        store_id: int
) -> dict | None:
    payment_data = None
    try:
        query = sess().query(
            ObjectPaymentSettings.json_data
        ).filter(
            ObjectPaymentSettings.payment_method == payment_type,
            func.json_length(ObjectPaymentSettings.json_data) > 0
        )
        query = query.filter(ObjectPaymentSettings.store_id == store_id)
        result = query.one_or_none()
        if result:
            payment_data = result.json_data
    except Exception as err:
        logger.error(f'{err}', exc_info=True)
    return payment_data


@db_func
def get_stripe_webhook_secret(secret_key: str) -> str:
    result = None
    try:
        res = sess().query(PaymentSettings.json_data). \
            filter(
            PaymentSettings.payment_method == 'stripe',
            func.json_extract(PaymentSettings.json_data, '$.secret_key') == secret_key,
            ~func.json_extract(
                PaymentSettings.json_data, '$.stripe_endpoint_secret'
            ).is_(None)
        ).first()
        if res:
            result = res[0].get('stripe_endpoint_secret')
    except Exception as err:
        logger.error(f'{err}', exc_info=True)
    return result


@db_func
def get_object_payment_settings_(
        settings_id: int,
        target: ObjectPaymentSettingsTarget,
) -> list[ObjectPaymentSettings]:
    query = (sess().query(
        ObjectPaymentSettings
    ))

    if target == ObjectPaymentSettingsTarget.STORE:
        query = query.join(Store).filter(Store.is_deleted.is_(False))
    elif target == ObjectPaymentSettingsTarget.INVOICE_TEMPLATE:
        query = query.join(InvoiceTemplate).filter(
            InvoiceTemplate.is_deleted.is_(False)
        )

    query = query.filter(
        ObjectPaymentSettings.payment_settings_id == settings_id,
        ObjectPaymentSettings.is_deleted.is_(False),
    )
    object_payment_settings = query.all()

    return object_payment_settings


async def get_payment_credentials(payment: Payment) -> dict:
    # Детальне логування для діагностики
    logger.debug(f"Getting payment credentials for payment: id={payment.id}, uuid_id={payment.uuid_id}, method={payment.payment_method}, payment_settings_id={payment.payment_settings_id}, object_payment_settings_id={payment.object_payment_settings_id}, is_sandbox={payment.is_sandbox}")

    # filtered_add_json_data = None
    ps = await PaymentSettings.get(payment.payment_settings_id)

    if not ps:
        logger.error(f"Payment settings not found: {payment.payment_method=}, {payment.id=}, {payment.payment_settings_id=}, {payment.object_payment_settings_id=}")
        raise PaymentSettingsNotFoundError(
            f"{payment.payment_method=}, {payment.id=}, "
            f"{payment.payment_settings_id=}, {payment.object_payment_settings_id=}"
        )

    logger.debug(f"Found payment settings: id={ps.id}, method={ps.payment_method}, brand_id={ps.brand_id}, store_id={ps.store_id}")
    logger.debug(f"Payment settings json_data: {ps.json_data}")

    if not payment.object_payment_settings_id:
        logger.debug(f"No object_payment_settings_id, returning payment settings json_data")
        return ps.json_data

    sps = await ObjectPaymentSettings.get(
        payment.object_payment_settings_id
    )

    if sps and sps.json_data:
        logger.debug(f"Found object payment settings: id={sps.id}, target={sps.target}, store_id={sps.store_id}, invoice_template_id={sps.invoice_template_id}")
        logger.debug(f"Object payment settings json_data: {sps.json_data}")
        return sps.json_data

    logger.debug(f"No object payment settings json_data, returning payment settings json_data")
    return ps.json_data


@db_func
def get_payment_settings_merchant_data(
        business_payment_data_id: int,
) -> "models.PaymentSettingsMerchantData":
    """
    Отримати дані мерчанта для конкретного BusinessPaymentData
    """
    return sess().query(PaymentSettingsMerchantData).filter(
        PaymentSettingsMerchantData.business_payment_data_id ==
        business_payment_data_id,
        PaymentSettingsMerchantData.is_deleted.is_(False)
    ).first()
