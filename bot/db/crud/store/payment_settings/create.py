import logging

from sqlalchemy import func, select

import schemas
from config import MAX_POSITION_VALUE
from core.exceptions import MaxObjectPositionError
from db import db_func, sess
from db.models import (
    Brand, Group, InvoiceTemplate, ObjectPaymentSettings, PaymentSettings, Store,
    StoreOrderPayment,
)
from schemas.payment_settings.schemas_add import ObjectPaymentSettingsTarget

logger = logging.getLogger()


@db_func
def create_payment_settings(
        payment_method: str,
        brand_id: int | None = None,
        store_id: int | None = None,
        data: dict | None = None,
) -> PaymentSettings:
    payment_settings = PaymentSettings(
        payment_method=payment_method, brand_id=brand_id, store_id=store_id,
        json_data=data
    )
    sess().add(payment_settings)

    sess().commit()
    return payment_settings


@db_func
def new_create_payment_settings(
        payment_method: schemas.PaymentSettingsMethodLiteral | str,
        brand_id: int,
        data: dict | None = None,
        name: str | None = None,
        description: str | None = None,
        with_comment: bool = False,
        media_id: int | None = None,
        is_enabled: bool = True,
        no_commit: bool = False,
        force_position: int | None = None,  # TODO: payments, temporary
        store_json_data: dict[int, dict] | None = None,
        invoice_template_json_data: dict[int, dict] | None = None,
        label_comment: str | None = None,
        post_payment_info: str | None = None,
) -> PaymentSettings:
    stmt = select(func.max(PaymentSettings.position)).where(
        PaymentSettings.brand_id == brand_id
    )
    last_position = sess().execute(stmt).scalar_one_or_none()
    position = last_position + 1 if (last_position or last_position == 0) else 0
    if position > MAX_POSITION_VALUE:
        raise MaxObjectPositionError(object_name="payment_settings")

    payment_settings = PaymentSettings(
        payment_method=payment_method,
        brand_id=brand_id,
        json_data=data,
        name=name,
        description=description,
        with_comment=with_comment,
        media_id=media_id,
        is_online=True if payment_method != "cash" and payment_method != "custom"
        else False,
        is_enabled=is_enabled,
        label_comment=label_comment,
        post_payment_info=post_payment_info,
        position=force_position or position,  # TODO: payments, temporary
    )

    sess().add(payment_settings)

    stmt = select(Store).where(Store.brand_id == brand_id)
    result = sess().execute(stmt)
    stores = result.scalars().all()

    for store in stores:
        store_post_payment_info = None
        if (store_json_data and store_json_data.get(store.id) and
            store_json_data.get(store.id).get("post_payment_info")):
            store_post_payment_info = store_json_data[store.id]["post_payment_info"]
            
        object_payment_settings = ObjectPaymentSettings(
            target=ObjectPaymentSettingsTarget.STORE,
            store_id=store.id,
            json_data=store_json_data[store.id][
                "json_data"] if store_json_data and store_json_data.get(
                store.id
            ) and
                                store_json_data.get(
                                    store.id, None
                                ) and store_json_data.get(
                store.id, None
            ).get(
                "json_data", None
            ) else None,
            is_enabled=store_json_data[store.id][
                "is_enabled"] if store_json_data and store_json_data.get(
                store.id, None
            ) and store_json_data.get(store.id, None).get(
                "is_enabled", None
            ) is not None else True,
            payment_settings=payment_settings,
            post_payment_info=store_post_payment_info,
        )
        sess().add(object_payment_settings)

    stmt = select(InvoiceTemplate).join(
        Group, InvoiceTemplate.group_id == Group.id
    ).join(Brand, Brand.group_id == Group.id).where(
        InvoiceTemplate.is_deleted.is_(False),
    )
    result = sess().execute(stmt)
    invoice_templates = result.scalars().all()

    for invoice_template in invoice_templates:
        invoice_post_payment_info = None
        if (invoice_template_json_data and invoice_template_json_data.get(invoice_template.id) and
            invoice_template_json_data.get(invoice_template.id).get("post_payment_info")):
            invoice_post_payment_info = invoice_template_json_data[invoice_template.id]["post_payment_info"]
            
        object_payment_settings = ObjectPaymentSettings(
            target=ObjectPaymentSettingsTarget.INVOICE_TEMPLATE,
            invoice_template_id=invoice_template.id,
            json_data=invoice_template_json_data[invoice_template.id][
                "json_data"] if invoice_template_json_data and
                                invoice_template_json_data.get(
                                    invoice_template.id
                                ) and
                                invoice_template_json_data.get(
                                    invoice_template.id, None
                                ) and invoice_template_json_data.get(
                invoice_template.id, None
            ).get(
                "json_data", None
            ) else None,
            is_enabled=invoice_template_json_data[invoice_template.id][
                "is_enabled"] if invoice_template_json_data and
                                 invoice_template_json_data.get(
                                     invoice_template.id, None
                                 ) and invoice_template_json_data.get(
                invoice_template.id, None
            ).get(
                "is_enabled", None
            ) is not None else True,
            payment_settings=payment_settings,
            post_payment_info=invoice_post_payment_info,
        )
        sess().add(object_payment_settings)

    if not no_commit:
        sess().commit()
    return payment_settings


@db_func
def create_store_order_payment(
        payment_method: schemas.PaymentSettingsMethodLiteral | str,
        order_id: int | None = None,
        invoice_id: int | None = None,
        comment: str | None = None,
        json_data: dict | None = None,
        name: str | None = None,
        description: str | None = None,
        payment_settings_id: int | None = None,
        label_comment: str | None = None,
        post_payment_info: str | None = None,
        incust_account_name: str | None = None,
        incust_account_id: str | None = None,
        incust_card_name: str | None = None,
        incust_card_id: str | None = None,
        business_payment_setting_id: int | None = None,
        business_payment_merchant_data: dict | None = None
) -> StoreOrderPayment:
    store_order_payment = StoreOrderPayment(
        order_id=order_id,
        invoice_id=invoice_id,
        comment=comment,
        payment_method=payment_method,
        json_data=json_data,
        name=name,
        description=description,
        payment_settings_id=payment_settings_id,
        label_comment=label_comment,
        post_payment_info=post_payment_info,
        incust_account_name=incust_account_name,
        incust_account_id=incust_account_id,
        incust_card_name=incust_card_name,
        incust_card_id=incust_card_id,
        business_payment_setting_id=business_payment_setting_id,
        business_payment_merchant_data=business_payment_merchant_data,
    )

    sess().add(store_order_payment)
    sess().commit()

    return store_order_payment


@db_func
def create_payment_settings_merchant_data(
    business_payment_data_id: int | None = None,
    json_data: dict | None = None,
    is_enabled: bool = True
) -> "models.PaymentSettingsMerchantData":
    """
    Створити запис PaymentSettingsMerchantData
    """
    from db.models import PaymentSettingsMerchantData
    
    merchan_data = PaymentSettingsMerchantData(
        business_payment_data_id=business_payment_data_id,
        json_data=json_data,
        is_enabled=is_enabled
    )
    
    sess().add(merchan_data)
    sess().commit()
    
    return merchan_data
