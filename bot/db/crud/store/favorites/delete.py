from db import db_func, sess, models


@db_func
def delete_favorite_product(favorite_product_id: int) -> bool:
    favorite_product = sess().query(models.StoreFavoritesProduct)\
        .filter(models.StoreFavoritesProduct.id == favorite_product_id).one_or_none()
    sess().delete(favorite_product)
    sess().commit()

    return True


@db_func
def delete_favorites(favorite_id: int) -> bool:
    favorite = sess().query(models.StoreFavorite)\
        .filter(models.StoreFavorite.id == favorite_id).one_or_none()
    sess().delete(favorite)
    sess().commit()

    return True
