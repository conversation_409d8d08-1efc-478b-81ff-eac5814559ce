from sqlalchemy import delete, select, update

from db import db_func, sess
from db.models import StoreFavorite, StoreFavoritesProduct


@db_func
def clear_favorites(favorite_id: int) -> StoreFavorite | None:
    favorite = sess().query(StoreFavorite).filter(
        StoreFavorite.id == favorite_id
    ).one_or_none()
    if not favorite:
        return None

    for prod in favorite.favorite_products:
        sess().delete(prod)
    sess().commit()

    return favorite


@db_func
def sync_favorites(
        favorite_to: StoreFavorite,
        favorite_from: StoreFavorite,
):
    to_product_ids = sess().scalars(
        select(StoreFavoritesProduct.product_id)
        .where(StoreFavoritesProduct.favorite_id == favorite_to.id)
    ).all()

    sess().execute(
        delete(StoreFavoritesProduct)
        .where(
            StoreFavoritesProduct.favorite_id == favorite_from.id,
            StoreFavoritesProduct.product_id.in_(to_product_ids)
        )
    )

    sess().flush()

    sess().execute(
        update(StoreFavoritesProduct)
        .values({"favorite_id": favorite_to.id})
        .where(StoreFavoritesProduct.favorite_id == favorite_from.id)
    )
    sess().flush()
    sess().delete(favorite_from)
    sess().commit()
    return favorite_to
