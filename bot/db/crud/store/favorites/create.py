from db import db_func, sess
from db.models import StoreFavorite, StoreFavoritesProduct, StoreProduct


@db_func
def create_favorites(
        user_id: int | None,
        store_id: int,
        products: list[int] | None = None
) -> StoreFavorite:
    favorite = StoreFavorite(user_id=user_id, store_id=store_id)

    if products:
        for prod_id in products:
            product = StoreProduct.get_sync(prod_id)
            favorite_product = StoreFavoritesProduct(
                product=product, favorite=favorite
            )
            favorite.favorite_products.append(favorite_product)

    sess().add(favorite)
    sess().commit()

    return favorite


@db_func
def add_product_to_favorites(
        favorite_id: int, product_id: int
) -> StoreFavoritesProduct:
    favorite = sess().query(StoreFavorite).filter(
        StoreFavorite.id == favorite_id
    ).one_or_none()
    product = StoreProduct.get_sync(product_id)
    favorite_product = StoreFavoritesProduct(product=product, favorite=favorite)
    favorite.favorite_products.append(favorite_product)

    sess().commit()
    return favorite_product


@db_func
def toggle_product_favorite(
        favorite_id: int,
        product_id: int
) -> StoreFavoritesProduct | None:
    favorite_product = StoreFavoritesProduct.get_sync(
        product_id=product_id,
        favorite_id=favorite_id
    )

    if favorite_product:
        sess().delete(favorite_product)
        favorite_product = None
    else:
        favorite_product = StoreFavoritesProduct(
            product_id=product_id,
            favorite_id=favorite_id
        )
        sess().add(favorite_product)
    sess().commit()
    return favorite_product
