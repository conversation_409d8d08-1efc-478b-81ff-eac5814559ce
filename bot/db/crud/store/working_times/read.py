from dataclasses import dataclass

from itertools import groupby
from sqlalchemy import select

from db import db_func, sess
from db.models import WorkingDay, WorkingSlot


@dataclass
class WorkingDayData:
    day: WorkingDay
    slots: list[WorkingSlot]


@db_func
def get_working_times(store_id: int) -> list[WorkingDayData]:
    stmt = (
        select(
            WorkingDay,
            WorkingSlot
        )
        .outerjoin(WorkingDay.slots)
        .where(WorkingDay.store_id == store_id)
        .order_by(WorkingDay.id, WorkingSlot.id)
    )
    data = sess().execute(stmt).fetchall()

    result: list[WorkingDayData] = []

    for _, rows in groupby(data, lambda x: x[0].id):
        row_data: WorkingDayData | None = None
        for row in rows:
            if not row_data:
                row_data = WorkingDayData(row[0], [])
            if row[1]:
                row_data.slots.append(row[1])

        if row_data:
            result.append(row_data)

    return result
