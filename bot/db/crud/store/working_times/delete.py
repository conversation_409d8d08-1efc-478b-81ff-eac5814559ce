from db import db_func, sess, models


@db_func
def delete_time_slot(slot_id: int) -> bool:
    slot = sess().query(models.WorkingSlot) \
        .filter(models.WorkingSlot.id == slot_id).one_or_none()
    sess().delete(slot)
    sess().commit()

    return True


@db_func
def delete_days_by_store(store_id: int) -> bool:
    query = sess().query(models.WorkingDay) \
        .filter(models.WorkingDay.store_id == store_id)
    days = query.all()

    for day in days:
        for slot in day.slots:
            sess().delete(slot)
        sess().delete(day)

    sess().commit()

    return True
