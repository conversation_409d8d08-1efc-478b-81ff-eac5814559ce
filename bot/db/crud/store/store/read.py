import logging
from typing import Iterable, Type

from sqlalchemy import and_, desc, distinct, func, join, not_, or_, select
from sqlalchemy.engine import Row
from sqlalchemy.orm import Query, aliased
from sqlalchemy.sql import Select, label

import schemas
from db import db_func, sess
from db.decorators import process_query_with_operation
from db.helpers import get_query_by_operation, order_by_slice_and_result
from db.models import (
    Brand, BrandCustomSettings, Group, InvoiceTemplate, MediaObject, ProductToStore,
    Scope, Store, StoreCategoryToStore, StoreCharacteristic,
    StoreCharacteristicFilterSetting,
    StoreCustomSettings, StoreProduct,
    StoreProductSpotPrice, Translation,
)
from schemas.store.types import CustomType, ShipmentType

debugger = logging.getLogger('debug.stores')


@db_func
def get_cities_list(
        brand_id: int,
        is_delivery: bool = False,
        is_pickup: bool = False,
        is_in_store: bool = False,
) -> list[str]:
    query = sess().query(Store.city).distinct()
    query = query.filter(Store.brand_id == brand_id)

    if any([is_delivery, is_pickup, is_in_store]):
        query = query.outerjoin(
            StoreCustomSettings, StoreCustomSettings.store_id == Store.id
        )
        query = query.join(
            BrandCustomSettings,
            BrandCustomSettings.id == StoreCustomSettings.custom_settings_id
        )
        query = query.filter(
            BrandCustomSettings.custom_type == CustomType.SHIPMENT.value
        )
    if is_delivery:
        query = query.filter(
            BrandCustomSettings.base_type == ShipmentType.DELIVERY.value
        )
        query = query.filter(
            or_(
                StoreCustomSettings.is_enabled.is_(True),
                and_(
                    BrandCustomSettings.is_enabled.is_(True),
                    StoreCustomSettings.is_enabled.is_(None),
                ),
            )
        )
    if is_pickup:
        query = query.filter(BrandCustomSettings.base_type == ShipmentType.PICKUP.value)
        query = query.filter(
            or_(
                StoreCustomSettings.is_enabled.is_(True),
                and_(
                    BrandCustomSettings.is_enabled.is_(True),
                    StoreCustomSettings.is_enabled.is_(None),
                ),
            )
        )
    if is_in_store:
        query = query.filter(
            BrandCustomSettings.base_type == ShipmentType.IN_STORE.value
        )
        query = query.filter(
            or_(
                StoreCustomSettings.is_enabled.is_(True),
                and_(
                    BrandCustomSettings.is_enabled.is_(True),
                    StoreCustomSettings.is_enabled.is_(None),
                ),
            )
        )

    query = query.filter(Store.is_deleted.is_(False))
    query = query.filter(Store.city.is_not(None))

    return sess().scalars(query).all()


def get_stores_sync(
        brand_id: int,
        city: str | None = None,
        is_delivery: bool = False,
        is_pickup: bool = False,
        is_in_store: bool = False,
        position: int | None = None,
        limit: int | None = None,
        search_text: str | None = None,
        operation: str = "all",
        lang: str | None = None,
        with_translation: bool = True,
        allowed_store_ids: list[int] | None = None,
) -> list[tuple[Store, Translation]] | list[Store] | int:
    if lang and with_translation and operation != "count":
        query = sess().query(Store, Translation)
        query = query.outerjoin(Translation, Translation.filter(Store, lang))
    else:
        query = get_query_by_operation(Store, operation).distinct()

    query = query.filter(Store.brand_id == brand_id)
    query = query.filter(Store.is_deleted.is_(False))
    query = query.filter(Store.is_enabled.is_(True))

    if allowed_store_ids:
        query = query.filter(Store.id.in_(allowed_store_ids))

    if city:
        query = query.filter(Store.city == city)

    if any([is_delivery, is_pickup, is_in_store]):
        query = query.join(
            BrandCustomSettings, BrandCustomSettings.brand_id == Store.brand_id
        )
        query = query.outerjoin(
            StoreCustomSettings,
            and_(
                StoreCustomSettings.custom_settings_id == BrandCustomSettings.id,
                StoreCustomSettings.store_id == Store.id
            )
        )

        if is_delivery:
            query = query.filter(
                BrandCustomSettings.base_type == ShipmentType.DELIVERY.value
            )
            query = query.filter(
                or_(
                    StoreCustomSettings.is_enabled.is_(True),
                    and_(
                        BrandCustomSettings.is_enabled.is_(True),
                        StoreCustomSettings.is_enabled.is_(None),
                    ),
                )
            )
        if is_pickup:
            query = query.filter(
                BrandCustomSettings.base_type == ShipmentType.PICKUP.value
            )
            query = query.filter(
                or_(
                    StoreCustomSettings.is_enabled.is_(True),
                    and_(
                        BrandCustomSettings.is_enabled.is_(True),
                        StoreCustomSettings.is_enabled.is_(None),
                    ),
                )
            )
        if is_in_store:
            query = query.filter(
                BrandCustomSettings.base_type == ShipmentType.IN_STORE.value
            )
            query = query.filter(
                or_(
                    StoreCustomSettings.is_enabled.is_(True),
                    and_(
                        BrandCustomSettings.is_enabled.is_(True),
                        StoreCustomSettings.is_enabled.is_(None),
                    ),
                )
            )

    if search_text:
        query = query.filter(Store.name.contains(search_text))

    return order_by_slice_and_result(
        query, position, limit,
        (desc(-Store.position), Store.excel_row_number, Store.name, Store.id), operation
    )


@db_func
def get_stores(
        brand_id: int,
        city: str | None = None,
        is_delivery: bool = False,
        is_pickup: bool = False,
        is_in_store: bool = False,
        position: int | None = None,
        limit: int | None = None,
        search_text: str | None = None,
        operation: str = "all",
        lang: str | None = None,
        with_translation: bool = True,
        allowed_store_ids: list[int] | None = None,
) -> list[tuple[Store, Translation]] | list[Store] | int:
    return get_stores_sync(
        brand_id, city, is_delivery, is_pickup, is_in_store, position, limit,
        search_text, operation, lang, with_translation, allowed_store_ids
    )


@db_func
def get_product_stores(
        product_id: int,
        fields: Iterable[str] | None = None,
        target: schemas.ScopeTarget | None = None,
        target_id: int | None = None,
        profile_id: int | None = None,
        need_scopes_allowed: bool = False,
) -> list[Store] | list[Row]:
    stmt: Select

    if fields:
        select_fields = [getattr(Store, field) for field in fields]
    else:
        select_fields = [Store]

    if need_scopes_allowed:
        if target is None or target_id is None or profile_id is None:
            raise ValueError(
                "When need_scopes_allowed is True, (target, target_id, profile_id) "
                "are required"
            )

        select_fields.extend(
            Scope.allowed_scopes_list(
                "read", "edit",
                object_name="store",
                target=target,
                target_id=target_id,
                available_data={
                    "profile_id": profile_id,
                    "store_id": Store.id
                }
            )
        )

    stmt = select(*select_fields)
    stmt = stmt.join(ProductToStore, ProductToStore.store_id == Store.id)

    stmt = stmt.where(Store.is_deleted.is_(False))
    stmt = stmt.where(ProductToStore.product_id == product_id)

    if target is not None and target_id is not None and product_id is not None:
        stmt = stmt.where(
            Scope.filter_for_action(
                "store:read",
                target, target_id,
                available_data={
                    "profile_id": profile_id,
                    "store_id": Store.id,
                }
            )
        )

    if len(select_fields) == 1:
        return sess().scalars(stmt).all()
    return sess().execute(stmt).fetchall()


@db_func
def get_category_stores(
        category_id: int,
        fields: Iterable[str] | None = None,
        target: schemas.ScopeTarget | None = None,
        target_id: int | None = None,
        profile_id: int | None = None,
        need_scopes_allowed: bool = False,
) -> list[Store] | list[Row]:
    stmt: Select

    if fields:
        select_fields = [getattr(Store, field) for field in fields]
    else:
        select_fields = [Store]

    if need_scopes_allowed:
        if target is None or target_id is None or profile_id is None:
            raise ValueError(
                "When need_scopes_allowed is True, (target, target_id, profile_id) "
                "are required"
            )

        select_fields.extend(
            Scope.allowed_scopes_list(
                "read", "edit",
                object_name="store",
                target=target,
                target_id=target_id,
                available_data={
                    "profile_id": profile_id,
                    "store_id": Store.id
                }
            )
        )

    stmt = select(*select_fields)
    stmt = stmt.join(StoreCategoryToStore, StoreCategoryToStore.store_id == Store.id)

    stmt = stmt.where(Store.is_deleted.is_(False))
    stmt = stmt.where(StoreCategoryToStore.category_id == category_id)

    if target is not None and target_id is not None and profile_id is not None:
        stmt = stmt.where(
            Scope.filter_for_action(
                "store:read",
                target, target_id,
                available_data={
                    "profile_id": profile_id,
                    "store_id": Store.id,
                }
            )
        )

    if len(select_fields) == 1:
        return sess().scalars(stmt).all()
    return sess().execute(stmt).fetchall()


@db_func
def get_store_by_group_if_one(group_id: int) -> Store | None:
    store_2: Type[Store] = aliased(Store)

    query = sess().query(Store)
    query = query.filter(Store.is_deleted.is_(False))
    query = query.filter(Store.brand.has(group_id=group_id))

    subq = sess().query(store_2)

    subq = subq.filter(store_2.is_deleted.is_(False))
    subq = subq.filter(store_2.brand_id == Store.brand_id)
    subq = subq.filter(store_2.id != Store.id)

    query = query.filter(not_(subq.exists()))

    return query.one_or_none()


# noinspection PyUnusedLocal
@db_func
@process_query_with_operation(Store)
def get_group_stores(
        query: Query,
        group_id: int,
        position: int | None = None,
        limit: int | None = None,
        operation: str = "all",
):
    return query.filter(Store.brand.has(Brand.group.has(id=group_id)))


@db_func
def get_stores_names_by_ids(stores_ids: list[int]) -> list[str]:
    query = sess().query(Store.id, Store.name)
    query = query.filter(Store.id.in_(stores_ids))
    ids_names: dict[int, str] = dict(query.all())  # type: ignore

    return [ids_names.get(id) for id in stores_ids if id in ids_names]


@db_func
def check_is_store_owner(store_id: int, user_id: int) -> bool:
    query = sess().query(Store.brand.has(Brand.group.has(Group.owner_id == user_id)))
    query = query.filter(Store.id == store_id)
    return query.scalar()


@db_func
def get_store_name(store_id: int) -> str:
    store_name = None
    query = sess().query(Store.name)
    query = query.filter(Store.id == store_id)

    result = query.one_or_none()
    if result:
        store_name = result.name
    return store_name


@db_func
def get_admin_stores_list(
        profile_id: int,
        user_id: int,
        offset: int | None = None,
        limit: int = None,
        search_text: str | None = None,
        is_count: bool = False,
        different_currency_only: bool | None = None,
        excluded_stores_ids: list[int] | None = None,

) -> list[Row] | int:
    stmt: Select

    available_data = {
        "profile_id": profile_id,
        "store_id": Store.id,
    }

    if is_count:
        stmt = select(func.count(distinct(Store.id)))
        stmt = stmt.where(
            Scope.filter_for_action(
                "store:read",
                "user", user_id,
                available_data,
            )
        )
    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="store",
            target="user",
            target_id=user_id,
            available_data=available_data
        )

        stmt = (
            select(
                Store.id,
                Group.id.label("profile_id"),
                Store.is_enabled,
                Store.name,
                Store.currency,
                Store.banners,
                label("image_url", Store.image_media_url),
                read_allowed, edit_allowed,
            )
            .outerjoin(Store.media)
            .where(read_allowed.is_(True))
        )

    stmt = (
        stmt
        .join(Store.brand)
        .join(Brand.group)
        .where(Group.id == profile_id)
        .where(Group.status == "enabled")
        .where(Store.is_deleted.is_(False))
    )

    if different_currency_only:
        stmt = stmt.where(Store.currency != Group.currency)

    if excluded_stores_ids:
        stmt = stmt.where(Store.id.notin_(excluded_stores_ids))

    if search_text:
        stmt = stmt.where(
            or_(
                Store.name.contains(search_text),
                Store.description.contains(search_text)
            )
        )

    if is_count:
        return sess().scalar(stmt)

    stmt = stmt.order_by(
        Store.position.is_(None),
        Store.position,
        Store.excel_row_number.is_(None),
        Store.excel_row_number,
        Store.name,
        Store.id,
    )

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    return sess().execute(stmt).fetchall()


@db_func
def get_admin_product_stores_list(
        profile_id: int,
        product_id: int,
        user_id: int,
) -> list[Row]:
    read_allowed, edit_allowed = Scope.allowed_scopes_list(
        "read", "edit",
        object_name="store",
        target="user",
        target_id=user_id,
        available_data={
            "profile_id": profile_id,
            "store_id": Store.id,
        }
    )

    stmt = select(
        label("store_id", Store.id),
        label("store_name", Store.name),
        label("store_image_url", MediaObject.url),
        *StoreProduct.price_expressions(),
        read_allowed,
        edit_allowed,
    )
    stmt = stmt.outerjoin(Store.media)
    stmt = stmt.join(Store.products)
    stmt = stmt.outerjoin(
        StoreProductSpotPrice, and_(
            StoreProductSpotPrice.product_id == StoreProduct.id,
            StoreProductSpotPrice.store_id == Store.id,
        )
    )

    stmt = stmt.join(Store.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Store.is_deleted.is_(False))

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")

    stmt = stmt.where(StoreProduct.id == product_id)

    stmt = stmt.order_by(Store.position.is_(None))
    stmt = stmt.order_by(Store.position)

    stmt = stmt.order_by(Store.excel_row_number.is_(None))
    stmt = stmt.order_by(Store.excel_row_number)

    stmt = stmt.order_by(Store.name)
    stmt = stmt.order_by(Store.id)

    return sess().execute(stmt).fetchall()


@db_func
def get_admin_category_stores_list(
        profile_id: int,
        category_id: int,
        user_id: int,
) -> list[Row]:
    read_allowed, edit_allowed = Scope.allowed_scopes_list(
        "read", "edit",
        object_name="store",
        target="user",
        target_id=user_id,
        available_data={
            "profile_id": profile_id,
            "store_id": Store.id,
        }
    )

    stmt = select(
        Store.id,
        Group.id.label("profile_id"),
        Store.is_enabled,
        Store.name,
        Store.currency,
        label("image_url", MediaObject.url),
        read_allowed,
        edit_allowed,
    )
    stmt = stmt.outerjoin(Store.media)

    stmt = stmt.join(Store.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")

    stmt = stmt.where(Store.categories.any(id=category_id))

    stmt = stmt.order_by(Store.position.is_not(None))
    stmt = stmt.order_by(Store.position)

    stmt = stmt.order_by(Store.excel_row_number.is_not(None))
    stmt = stmt.order_by(Store.excel_row_number)

    stmt = stmt.order_by(Store.name)
    stmt = stmt.order_by(Store.id)

    return sess().execute(stmt).fetchall()


@db_func
def get_store_by_id_and_profile_id(
        store_id: int,
        profile_id: int
) -> Store | None:
    stmt = select(Store)
    stmt = stmt.where(Store.id == store_id)

    stmt = stmt.join(Store.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Store.is_deleted.is_(False))

    return sess().scalar(stmt)


@db_func
def get_stores_by_ids(stores_ids: Iterable[int]) -> list[Store | None]:
    # noinspection PyTypeChecker
    db_stores: dict[int, Store] = dict(
        sess().execute(
            select(Store.id, Store).where(
                Store.id.in_(stores_ids),
                Store.is_deleted.is_(False)
            )
        ).fetchall()
    )

    return [db_stores.get(id) for id in stores_ids]


@db_func
def get_stores_names_by_profile(profile_id: int) -> list[Row]:
    stmt = select(Store.id, Store.name).select_from(
        join(Store, Brand, Store.brand_id == Brand.id)
        .join(Group, Brand.group_id == Group.id)
    ).where(
        Group.id == profile_id,
        Store.is_deleted.is_(False)
    )
    return sess().execute(stmt).all()


@db_func
def get_invoice_templates_names_by_profile(profile_id: int) -> list[Row]:
    stmt = select(
        InvoiceTemplate.id,
        InvoiceTemplate.title.label('name')
    ).where(
        InvoiceTemplate.group_id == profile_id,
        InvoiceTemplate.is_deleted.is_(False)
    )
    return sess().execute(stmt).all()


def get_stores_to_disable(brand_id: int) -> list[Store]:
    stmt = select(Store)
    stmt = stmt.where(
        Store.brand_id == brand_id,
        Store.is_deleted.is_(False),
        Store.is_enabled.is_(True)
    )
    stmt = stmt.order_by(
        Store.position.desc(),
        Store.excel_row_number.desc(),
        Store.name.desc(),
        Store.id.desc()
    )

    return sess().scalars(stmt).all()


@db_func
def get_stores_with_filters(brand_id: int) -> list[Row]:
    char_subq = (
        select(
            StoreCharacteristicFilterSetting.store_id.label("store_id"),
            func.JSON_ARRAYAGG(
                func.JSON_OBJECT(
                    'id', StoreCharacteristic.id,
                    'name', StoreCharacteristic.name,
                    'filter_type', StoreCharacteristic.filter_type
                )
            ).label("characteristics")
        )
        .select_from(StoreCharacteristicFilterSetting)
        .join(
            StoreCharacteristic,
            StoreCharacteristic.id == StoreCharacteristicFilterSetting.characteristic_id
        )
        .where(StoreCharacteristicFilterSetting.brand_id == brand_id)
        .group_by(StoreCharacteristicFilterSetting.store_id)
        .subquery()
    )

    stmt = (
        select(
            Store,
            char_subq.c.characteristics
        )
        .outerjoin(char_subq, Store.id == char_subq.c.store_id)
        .where(
            Store.brand_id == brand_id,
            Store.is_deleted.is_(False),
        )
        .order_by(
            Store.position.desc(),
            Store.excel_row_number.desc(),
            Store.name.desc(),
            Store.id.desc()
        )
    )

    return sess().execute(stmt).all()
