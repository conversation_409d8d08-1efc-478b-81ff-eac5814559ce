from sqlalchemy import and_, delete

import schemas
from db import db_func, sess
from db.crud.translation.update import (
    clear_object_updated_fields_translations_sync, update_object_translations_sync,
)
from db.models import (
    ClientWebPage, Group, Store, StoreCharacteristic, StoreCharacteristicFilterSetting,
    StoreCustomField,
)


@db_func
def update_store(
        store: Store,
        data: schemas.AdminUpdateStoreData,
        profile_langs: list[str],
        group: Group,
) -> Store:
    client_web_pages_ids = data.client_web_pages or []

    store_object_data = data.dict(
        exclude_unset=True,
        exclude={"custom_fields", "translations", "client_web_pages"}
    )
    if store_object_data:
        clear_object_updated_fields_translations_sync(
            store, store_object_data, profile_langs
        )
        store.update_sync(store_object_data, no_commit=True)

    if client_web_pages_ids:
        store.client_web_pages.clear()

        client_web_pages_objects = sess().query(ClientWebPage).filter(
            and_(
                ClientWebPage.id.in_(client_web_pages_ids),
                ClientWebPage.group_id == group.id
            )
        ).all()

        store.client_web_pages.extend(client_web_pages_objects)

    #  update custom fields
    if data.custom_fields:
        update_custom_fields(store.id, data.custom_fields)

    if data.translations:
        update_object_translations_sync(store, data.translations)

    sess().commit()

    return store


def update_custom_fields(
        store_id: int,
        custom_fields: dict[str, str | list | None],
):
    new_objects = []
    for field_name, field_value in custom_fields.items():
        if field_value is None:
            sess().execute(
                delete(StoreCustomField).where(
                    StoreCustomField.store_id == store_id,
                    StoreCustomField.name == field_name,
                )
            )
        else:
            field = StoreCustomField.get_sync(
                store_id=store_id,
                name=field_name,
            )
            if field:
                field.update_sync(value=field_value)
            else:
                field = StoreCustomField(
                    name=field_name,
                    value=field_value,
                    store_id=store_id,
                )
                new_objects.append(field)

    sess().add_all(new_objects)


@db_func
def update_store_filters(
        brand_id: int,
        store_id: int,
        connected_filters: list[schemas.AdminFilterItemSchema] | None,
):
    new_items = connected_filters or []
    incoming_ids = {item.id for item in new_items}

    existing_links = (
        sess()
        .query(StoreCharacteristicFilterSetting)
        .filter_by(store_id=store_id)
        .all()
    )
    existing_ids = {link.characteristic_id for link in existing_links}

    for link in existing_links:
        if link.characteristic_id not in incoming_ids:
            sess().delete(link)

    for item in new_items:
        if item.id not in existing_ids:
            sess().add(
                StoreCharacteristicFilterSetting(
                    store_id=store_id,
                    characteristic_id=item.id,
                    brand_id=brand_id,
                )
            )

    if incoming_ids:
        characteristics = (
            sess()
            .query(StoreCharacteristic)
            .filter(StoreCharacteristic.id.in_(incoming_ids))
            .all()
        )
        char_map = {c.id: c for c in characteristics}

        for item in new_items:
            ch = char_map.get(item.id)
            if not ch:
                continue

            if item.filter_type and ch.filter_type != item.filter_type.value:
                ch.filter_type = item.filter_type.value

            if ch.is_hide != item.is_hide:
                ch.is_hide = item.is_hide

    sess().commit()
