from db import db_func, sess
from db.models import (
    StoreAttribute,
    StoreAttributeGroup,
    StoreProduct,
    AttributeGroupToProduct,
    Store,
    StoreCart,
    StoreCartProduct,
    StoreOrder,
    OrderProduct,
    OrderAttribute,
    ExternalOrder,
    StoreCategory,
    StoreCategoryFilter,
    StoreCustomField,
    StoreCharacteristicValue,
    OrderShipment,
    OrderCustomPayment,
    StoreFavorite,
    StoreFavoritesProduct,
    WorkingDay,
    WorkingSlot,
    OrderShippingStatus,
    StoreProductSpotPrice,
)
from db.models.store.category import StoreCategoryToStore
from db.models.store.product import ProductToCategory, ProductToStore


# TODO: rewrite
@db_func
def delete_brand(brand_id):
    print('get stores...')
    stores = sess().query(Store).filter(Store.brand_id == brand_id)

    for store in stores:
        print(f'store.id: {store.id}')
        # sess().query(StoreCartAttribute).filter(StoreCartAttribute.store_id == store.id).delete()
        # print('StoreCartAttribute deleted')
        print('\nDelete StoreCartProduct')
        sess().query(StoreCartProduct).filter(StoreCartProduct.store_id == store.id).delete()
        print('StoreCartProduct deleted')

        print('\nStoreCart')
        sess().query(StoreCart).filter(StoreCart.store_id == store.id).delete()
        print('StoreCart deleted')

        print('\nDelete StoreOrder')
        orders = sess().query(StoreOrder).filter(StoreOrder.store_id == store.id)
        for order in orders:
            sess().query(ExternalOrder).filter(ExternalOrder.order_id == order.id).delete()
            print('ExternalOrder deleted')

            sess().query(OrderShippingStatus).filter(OrderShippingStatus.store_order_id == order.id).delete()
            print('OrderShippingStatus deleted')

            sess().query(OrderShipment).filter(OrderShipment.store_order_id == order.id).delete()
            print('OrderShipment deleted')

            sess().query(OrderCustomPayment).filter(OrderCustomPayment.store_order_id == order.id).delete()
            print('OrderCustomPayment deleted')

            print('get OrderProduct')
            o_products = sess().query(OrderProduct).filter(OrderProduct.store_order_id == order.id)
            for o_product in o_products:
                print('get OrderAttribute')
                sess().query(OrderAttribute).filter(
                    OrderAttribute.order_product_id == o_product.id
                ).delete()
            print('OrderAttribute deleted')
            o_products.delete()
            print('o_products deleted')
        orders.delete()
        print('StoreOrder deleted')

        print('\nDelete ProductToStore')
        products_to_store = sess().query(ProductToStore).filter(ProductToStore.store_id == store.id)
        products_to_store.delete()
        print('ProductToStore deleted')

        print('\nDelete CategoryToStore')
        cat_to_store = sess().query(StoreCategoryToStore).filter(StoreCategoryToStore.store_id == store.id)
        cat_to_store.delete()
        print('CategoryToStore deleted')

        print('\nDelete StoreCustomField')
        cf_store = sess().query(StoreCustomField).filter(StoreCustomField.store_id == store.id)
        cf_store.delete()
        print('StoreCustomField deleted')

        print('\nDelete StoreFavorite')
        favorites = sess().query(StoreFavorite).filter(StoreFavorite.store_id == store.id)
        for fs in favorites:
            sess().query(StoreFavoritesProduct).filter(StoreFavoritesProduct.favorite_id == fs.id).delete()

        favorites.delete()
        print('StoreFavorite deleted')

        print('\nDelete WorkingDay')
        wds = sess().query(WorkingDay).filter(WorkingDay.store_id == store.id)
        for wd in wds:
            sess().query(WorkingSlot).filter(WorkingSlot.working_day_id == wd.id).delete()

        wds.delete()
        print('WorkingDay deleted')

    print('\nDelete StoreAttribute')
    sess().query(StoreAttribute).filter(StoreAttribute.brand_id == brand_id).delete()
    print('StoreAttribute deleted')

    print('\nDelete StoreAttributeGroup')
    attr_groups = sess().query(StoreAttributeGroup).filter(StoreAttributeGroup.brand_id == brand_id)
    print('For group in groups')
    for attr_group in attr_groups:
        sess().query(AttributeGroupToProduct).filter(
            AttributeGroupToProduct.attribute_group_id == attr_group.id
        ).delete()
        print('AttributeGroupToProduct deleted')
    attr_groups.delete()
    print('StoreAttributeGroup deleted')

    products = sess().query(StoreProduct).filter(StoreProduct.brand_id == brand_id)

    print('\nDelete ProductToCategory')
    for product in products:
        sess().query(ProductToCategory).filter(ProductToCategory.product_id == product.id).delete()
    print('ProductToCategory deleted')

    print('\nDelete StoreProductSpotPrice')
    for product in products:
        sess().query(StoreProductSpotPrice).filter(StoreProductSpotPrice.product_id == product.id).delete()
    print('StoreProductSpotPrice deleted')

    print('\nDelete StoreCharacteristicValue')
    for product in products:
        sess().query(StoreCharacteristicValue).filter(StoreCharacteristicValue.product_id == product.id).delete()
    print('StoreCharacteristicValue deleted')

    print('\nDelete StoreProduct...')
    sess().query(StoreProduct).filter(StoreProduct.brand_id == brand_id).delete()
    print('StoreProduct deleted')

    categories = sess().query(StoreCategory).filter(StoreCategory.brand_id == brand_id).all()
    print('\nDelete StoreCategoryFilter...')
    for category in categories:
        sess().query(StoreCategoryFilter).filter(StoreCategoryFilter.category_id == category.id,).delete()
    print('\nStoreCategoryFilter deleted')

    print('\nDelete StoreCategory...')
    print('\nDelete father_category_id.in_(subquery)')

    all_deleted = False
    while not all_deleted:
        subquery = sess().query(StoreCategory.id).filter_by(brand_id=brand_id)
        sub_categories = sess().query(StoreCategory).filter(
            StoreCategory.brand_id == brand_id,
            StoreCategory.father_category_id.in_(subquery)
        ).all()
        del_error = False
        for sub_category in sub_categories:
            try:
                sess().query(StoreCategory).filter(StoreCategory.id == sub_category.id).delete()
            except:
                del_error = True
        if not del_error:
            all_deleted = True
            break

    print('\nDelete father_category_id is not NULL')
    sess().query(StoreCategory).filter(StoreCategory.brand_id == brand_id).\
        filter(~StoreCategory.father_category_id.is_(None)).delete(synchronize_session=False)
    print('\nDelete main categories')
    sess().query(StoreCategory).filter(StoreCategory.brand_id == brand_id).delete()
    print('StoreCategory deleted')

    sess().commit()
