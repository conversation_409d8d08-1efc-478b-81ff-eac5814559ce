from babel.numbers import list_currencies
from sqlalchemy import and_

import schemas
from core.exceptions import InvalidCurrencyError
from db import db_func, sess
from db.models import Brand, ClientWebPage, Store, User
from ...scope.create import grand_scopes_to_created_object_sync


@db_func
def create_store(
        brand: Brand,
        data: schemas.AdminCreateStoreData,
        creator: User | None = None,
) -> Store:
    if data.currency not in list_currencies():
        raise InvalidCurrencyError(data.currency)

    client_web_pages_ids = data.client_web_pages or []

    store = Store(
        brand=brand,
        **data.dict(exclude_unset=True, exclude={"client_web_pages"}),
    )
    sess().add(store)
    sess().flush()

    if client_web_pages_ids:
        client_web_pages_objects = sess().query(ClientWebPage).filter(
            and_(
                ClientWebPage.id.in_(client_web_pages_ids),
                ClientWebPage.group_id == brand.group_id
            )
        ).all()

        store.client_web_pages.extend(client_web_pages_objects)

    if creator:
        grand_scopes_to_created_object_sync(
            "store", store, creator, {
                "profile_id": brand.group_id,
            }
        )

    sess().commit()
    return store
