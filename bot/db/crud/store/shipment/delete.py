from db import db_func, sess
from db.models import ShipmentPrice, ShipmentPriceToSettings, ShipmentTime, ShipmentTimeToSettings

from .read import get_shipment_time_to_settings, get_shipment_prices_to_settings


@db_func
def delete_shipment_time(settings_id: int | None = None, zone_id: int | None = None) -> bool:
    shipment_time_to_settings = get_shipment_time_to_settings(settings_id, zone_id)

    if shipment_time_to_settings:
        shipment_time_id = shipment_time_to_settings.shipment_time_id
        query = sess().query(ShipmentTimeToSettings)
        query = query.filter(ShipmentTimeToSettings.id == shipment_time_to_settings.id)
        query.delete()

        query = sess().query(ShipmentTime)
        query = query.filter(ShipmentTime.id == shipment_time_id)
        query.delete()

        sess().commit()
    return True


@db_func
def delete_shipment_prices(
        brand_id: int, store_id: int | None = None,
        settings_id: int | None = None, zone_id: int | None = None
) -> bool:
    shipment_prices_to_settings = get_shipment_prices_to_settings(brand_id, store_id, settings_id, zone_id)

    if shipment_prices_to_settings:
        shipment_prices_ids = [item.shipment_price_id for item in shipment_prices_to_settings]
        query = sess().query(ShipmentPriceToSettings)
        query = query.filter(ShipmentPriceToSettings.id.in_([item.id for item in shipment_prices_to_settings]))
        query.delete()

        query = sess().query(ShipmentPrice)
        query = query.filter(ShipmentPrice.id.in_(shipment_prices_ids))
        query.delete()

        sess().commit()
    return True


@db_func
def delete_shipment_price(
        brand_id: int, store_id: int | None,
        price_id: int, settings_id: int | None = None, zone_id: int | None = None
) -> bool:
    shipment_prices_to_settings = get_shipment_prices_to_settings(brand_id, store_id, settings_id, zone_id)

    for item in shipment_prices_to_settings:
        if item.shipment_price_id == price_id:
            shipment_price_to_settings_id = item.id
            break
    else:
        shipment_price_to_settings_id = None

    if shipment_price_to_settings_id:
        query = sess().query(ShipmentPriceToSettings)
        query = query.filter(ShipmentPriceToSettings.id == shipment_price_to_settings_id)
        query.delete()

        query = sess().query(ShipmentPrice)
        query = query.filter(ShipmentPrice.id == price_id)
        query.delete()

        sess().commit()
    return True
