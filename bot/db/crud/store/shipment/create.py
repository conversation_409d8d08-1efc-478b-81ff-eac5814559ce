from db import db_func, sess
from db.models import ShipmentPrice, ShipmentPriceToSettings, ShipmentTime, ShipmentTimeToSettings


@db_func
def create_shipment_time(settings_id: int | None = None, zone_id: int | None = None) -> ShipmentTime:
    if all([settings_id, zone_id]):
        raise ValueError("Only one parameter must be specified: settings_id or zone_id")
    if not any([settings_id, zone_id]):
        raise ValueError("Must specify one of the parameters: settings_id or zone_id")

    shipment_time = ShipmentTime()
    sess().add(shipment_time)
    sess().flush()

    if settings_id:
        shipment_time_to_settings = ShipmentTimeToSettings(shipment_time_id=shipment_time.id, settings_id=settings_id)
    else:
        shipment_time_to_settings = ShipmentTimeToSettings(shipment_time_id=shipment_time.id, zone_id=zone_id)
    sess().add(shipment_time_to_settings)

    sess().commit()
    return shipment_time


@db_func
def create_shipment_price(settings_id: int | None = None, zone_id: int | None = None) -> ShipmentPrice:
    if all([settings_id, zone_id]):
        raise ValueError("Only one parameter must be specified: settings_id or zone_id")
    if not any([settings_id, zone_id]):
        raise ValueError("Must specify one of the parameters: settings_id or zone_id")

    shipment_price = ShipmentPrice()
    sess().add(shipment_price)
    sess().flush()

    if settings_id:
        shipment_price_to_settings = ShipmentPriceToSettings(
            shipment_price_id=shipment_price.id,
            settings_id=settings_id
        )
    else:
        shipment_price_to_settings = ShipmentPriceToSettings(shipment_price_id=shipment_price.id, zone_id=zone_id)
    sess().add(shipment_price_to_settings)

    sess().commit()
    return shipment_price
