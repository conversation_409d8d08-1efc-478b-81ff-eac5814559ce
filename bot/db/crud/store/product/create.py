from sqlalchemy import func, select
from sqlalchemy.exc import IntegrityError

import schemas
from config import MAX_POSITION_VALUE
from core.exceptions import MaxObjectPositionError
from db import db_func, sess
from db.models import (
    Brand, Gallery, GalleryItem, Store, StoreCategory, StoreCharacteristicValue,
    StoreProduct,
    StoreProductSpotPrice, User,
)
from schemas import AdminProductGalleryItemForCreate
from .update import update_products_group_hashes_sync
from ..products_groups.update import update_or_add_modifier_value
from ...scope.create import grand_scopes_to_created_object_sync
from ...translation.update import update_object_translations_sync


@db_func
def create_product(
        brand: Brand,
        data: schemas.AdminCreateProductData,
        creator: User | None = None,
        stores: list[Store] | None = None,
        categories: list[StoreCategory] | None = None,
        gallery_media_items: list[AdminProductGalleryItemForCreate] | None = None,
) -> StoreProduct:
    stores_by_id: dict[int, Store] = dict(map(lambda x: (x.id, x), stores or []))

    if data.price:
        data.price = round(data.price * 100)
    if data.old_price:
        data.old_price = round(data.old_price * 100)

    if data.floating_sum_max:
        data.floating_sum_max = round(data.floating_sum_max * 100)
    if data.floating_sum_min:
        data.floating_sum_min = round(data.floating_sum_min * 100)

    stmt = select(func.max(StoreProduct.position)).where(
        StoreProduct.brand_id == brand.id
    )
    last_position = sess().execute(stmt).scalar_one_or_none()
    position = last_position + 1 if isinstance(last_position, int) else 0
    if position > MAX_POSITION_VALUE:
        raise MaxObjectPositionError(object_name="product")

    product = StoreProduct(
        brand=brand,
        stores=stores or [],
        categories=categories or [],
        _internal_name=data.raw_internal_name,
        **data.dict(
            exclude_unset=True,
            exclude={"image_url", "stores", "categories", "floating_sum_options_str",
                     'products_group_modifiers',
                     'raw_internal_name', 'translations'}
        ),
        position=position,
    )
    sess().add(product)
    sess().flush()

    if data.translations:
        updated_translations = {}
        for key, value in data.translations.items():
            if value is not None:
                updated_translations[key] = schemas.AdminProductTranslationSchema(
                    **value
                )
            else:
                updated_translations[key] = None

        update_object_translations_sync(product, updated_translations)

    if stores:
        for store_data in data.stores:
            store = stores_by_id.get(store_data.store_id)

            price = round(
                store_data.price * 100
            ) if store_data.price is not None else product.price
            old_price = round(
                store_data.old_price * 100
            ) if store_data.old_price is not None else product.old_price

            if price != product.price or old_price != product.old_price:
                spot_price = StoreProductSpotPrice(
                    product=product,
                    store=store,
                    price=price,
                    old_price=old_price,
                )
                sess().add(spot_price)

    if creator:
        grand_scopes_to_created_object_sync(
            "product", product, creator, {
                "profile_id": brand.group_id,
            }
        )

    if data.product_group_id and data.products_group_modifiers:
        for item in data.products_group_modifiers:
            update_or_add_modifier_value(
                product.id, item.modifier_id, item.modifier_value
            )

    if gallery_media_items:
        gallery = Gallery()
        sess().add(gallery)

        product.gallery = gallery

        unique_gallery_media_items = list(
            {item.media_id: item for item in gallery_media_items}.values()
        )

        for item in unique_gallery_media_items:
            gallery_item = GalleryItem(
                gallery=gallery,
                media_id=item.media_id,
                position=item.position
            )
            sess().add(gallery_item)

    sess().flush()
    update_products_group_hashes_sync(
        product_ids=(product.id,),
    )

    sess().commit()
    return product


@db_func
def copy_product(
        product: StoreProduct, user: User, profile_id: int, brand: Brand,
        internal_product_id: str,
        translations: dict[str, dict | None] | None = None,
) -> StoreProduct:
    new_product_data = {
        key: value
        for key, value in product.as_dict(by_columns=True).items()
        if
        key not in (
            "id", "product_id", "gallery_id", "position", "_sa_instance_state", "task",
            "task_id"
        )
    }
    new_product_data["product_id"] = internal_product_id
    stmt = select(func.max(StoreProduct.position)).where(
        StoreProduct.brand_id == brand.id
    )
    last_position = sess().execute(stmt).scalar_one_or_none()
    position = last_position + 1 if isinstance(last_position, int) else 0
    if position > MAX_POSITION_VALUE:
        raise MaxObjectPositionError(object_name="product")

    new_product = StoreProduct(
        **new_product_data,
        position=position,
    )

    if product.gallery:
        new_gallery = Gallery()
        sess().add(new_gallery)
        new_product.gallery = new_gallery

        for item in product.gallery.gallery_items:
            new_gallery_item = GalleryItem(
                gallery=new_gallery,
                media_id=item.media_id,
                position=item.position
            )
            sess().add(new_gallery_item)

    new_product.categories = product.categories[:]

    new_product.stores = product.stores[:]

    new_product.attribute_groups = product.attribute_groups[:]

    for characteristic_value in product.characteristics:
        new_characteristic_value = StoreCharacteristicValue(
            characteristic=characteristic_value.characteristic,
            product=new_product,
            value=characteristic_value.value,
        )
        sess().add(new_characteristic_value)

    for spot_price in product.spots_prices:
        new_spot_price = StoreProductSpotPrice(
            product=new_product,
            store=spot_price.store,
            price=spot_price.price,
            old_price=spot_price.old_price
        )
        sess().add(new_spot_price)

    sess().add(new_product)
    sess().flush()

    if translations:
        updated_translations = {}
        for key, value in translations.items():
            if isinstance(value, schemas.AdminProductTranslationSchema):
                updated_translations[key] = value
            elif value is not None:
                updated_translations[key] = schemas.AdminProductTranslationSchema(
                    **value
                )
            else:
                updated_translations[key] = None

        update_object_translations_sync(new_product, updated_translations)

    sess().flush()
    update_products_group_hashes_sync(
        product_ids=(new_product.id,)
    )

    grand_scopes_to_created_object_sync(
        "product", new_product, user, {"profile_id": profile_id}
    )

    sess().commit()

    return new_product


@db_func
def add_product_photo_to_gallery(
        media_id: int, product_id: int, gallery_id: int | None = None,
        position: int | None = 0
) -> bool:
    try:
        gallery = sess().query(Gallery).filter_by(id=gallery_id).first()

        if not gallery:
            gallery = Gallery()
            sess().add(gallery)
            sess().flush()

            if product_id:
                sess().query(StoreProduct).filter_by(id=product_id).update(
                    {"gallery_id": gallery.id}
                )

        gallery_item = GalleryItem(
            gallery_id=gallery.id,
            media_id=media_id,
            position=position
        )

        sess().add(gallery_item)
        sess().commit()
        return True
    except IntegrityError:
        sess().rollback()
        return False
