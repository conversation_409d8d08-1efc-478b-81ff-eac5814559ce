from copy import deepcopy
from csv import DictReader
from operator import attrgetter
from typing import Dict, Iterable

from sqlalchemy import delete, select, update
from sqlalchemy.dialects.mysql import insert
from sqlalchemy.exc import IntegrityError, NoResultFound

import schemas
from db import db_func, sess
from db.crud.base.update import disconnect_m2m_related_objects_sync
from db.crud.store.product.read import calculate_product_group_hash
from db.crud.translation.update import (
    clear_object_updated_fields_translations_sync,
    update_object_translations_sync,
)
from db.decorators.other import safe_deadlock_handler
from db.models import (
    BrandCustomSettings, GalleryItem, ObjectPaymentSettings, PaymentSettings,
    ProductToCategory, ProductToStore, Scope, Store, StoreCategory, StoreCharacteristic,
    StoreCharacteristicValue, StoreProduct, StoreProductSpotPrice,
)


@db_func
def update_product(
        product: StoreProduct,
        data: schemas.AdminUpdateProductData,
        profile_langs: list[str],
):
    if data.price:
        data.price = round(data.price * 100)
    if data.old_price:
        data.old_price = round(data.old_price * 100)

    if data.floating_sum_max:
        data.floating_sum_max = round(data.floating_sum_max * 100)
    if data.floating_sum_min:
        data.floating_sum_min = round(data.floating_sum_min * 100)

    product_object_data = data.dict(
        exclude_unset=True, exclude={
            "translations",
            "image_url",
            "floating_sum_options_str",
        }
    )

    if product_object_data:
        if (
                "media_id" in product_object_data and
                product_object_data["media_id"] != product.media_id
        ):
            product_object_data["thumbnail_media_id"] = None

        clear_object_updated_fields_translations_sync(
            product, product_object_data, profile_langs
        )

        for key, value in product_object_data.items():
            if key != 'raw_internal_name':
                setattr(product, key, value)

        if data.raw_internal_name:
            product._internal_name = product_object_data['raw_internal_name']
        else:
            product._internal_name = None
        # product.update_sync(product_object_data, no_commit=True)

    if data.translations:
        update_object_translations_sync(product, data.translations)

    if data.price or data.old_price:
        price_to_check = data.price
        old_price_to_check = data.old_price

        stores_with_product = sess().execute(
            select(Store).join(Store.products).where(StoreProduct.id == product.id)
        ).scalars().all()

        for store in stores_with_product:
            spot_price = StoreProductSpotPrice.get_sync(
                product_id=product.id, store_id=store.id
            )
            if spot_price:
                if (price_to_check and spot_price.price == price_to_check) and (
                        old_price_to_check and spot_price.old_price ==
                        old_price_to_check):
                    sess().delete(spot_price)

    sess().commit()

    return product


@db_func
def update_product_to_liqpay(
        payment_settings_id: int,
        brand_id: int,
        data: DictReader,
        store_id: int | None = None,
        object_payment_settings_id: int | None = None,
) -> list[schemas.ProductFromLiqPay | None]:
    not_found = []
    for row in data:
        if row.get('vndcode').startswith('G'):
            query = sess().query(StoreProduct).filter(
                StoreProduct.brand_id == brand_id,
                StoreProduct.is_deleted.is_(False),
                StoreProduct.product_id == row.get('vndcode', None).replace('G', '')
            )

            if store_id:
                query = query.join(
                    ProductToStore, ProductToStore.product_id == StoreProduct.id
                )

            if store_id is not None:
                query = query.filter(ProductToStore.store_id == store_id)

            product = query.one_or_none()
            if product:
                product.liqpay_id = row.get('id', None)
            else:
                not_found.append(schemas.ProductFromLiqPay(**row))
        elif row.get('vndcode').startswith('S'):
            query = sess().query(BrandCustomSettings).filter(
                BrandCustomSettings.brand_id == brand_id,
                BrandCustomSettings.id == int(row.get('vndcode', None).replace('S', ''))
            )

            bcs = query.one_or_none()
            if bcs:
                bcs.liqpay_id = row.get('id', None)
            else:
                not_found.append(schemas.ProductFromLiqPay(**row))
        elif row.get('vndcode').startswith('T'):
            if object_payment_settings_id:
                payment_settings = ObjectPaymentSettings.get_sync(
                    object_payment_settings_id
                )
            else:
                payment_settings = PaymentSettings.get_sync(payment_settings_id)

            if payment_settings:
                data = deepcopy(payment_settings.json_data)
                data.update({"tips_liqpay_id": row.get('id')})
                payment_settings.json_data = data
            else:
                not_found.append(schemas.ProductFromLiqPay(**row))
        else:
            not_found.append(schemas.ProductFromLiqPay(**row))

    sess().commit()
    return not_found


def update_products_group_hashes_sync(
        product_ids: Iterable[int] | None = None,
        product_group_ids: Iterable[int] | None = None,
        conditions: list | None = None,
        commit: bool = False
):
    stmt = (
        update(StoreProduct)
        .values({"group_hash": calculate_product_group_hash()})
    )
    if product_ids:
        stmt = stmt.where(StoreProduct.id.in_(product_ids))
    if product_group_ids:
        stmt = stmt.where(StoreProduct.product_group_id.in_(product_group_ids))
    if conditions:
        stmt = stmt.where(*conditions)
    sess().execute(stmt)
    if commit:
        sess().commit()
    return True


@db_func
def update_products_group_hashes(
        product_ids: Iterable[int] | None = None,
        product_group_ids: Iterable[int] | None = None,
        conditions: list | None = None,
        commit: bool = True
):
    return update_products_group_hashes_sync(
        product_ids=product_ids,
        product_group_ids=product_group_ids,
        conditions=conditions,
        commit=commit,
    )


@db_func
@safe_deadlock_handler
def connect_stores_to_product(
        profile_id: int,
        user_id: int,
        product: StoreProduct,
        stores: list[Store],
        data: schemas.AdminConnectStoresToProductData,
):
    stores_by_id: dict[int, Store] = dict(map(lambda x: (x.id, x), stores or []))

    if data.replace:
        stores_to_disconnect_ids = sess().scalars(
            select(Store.id).where(
                Store.products.any(id=product.id),
                Store.id.not_in(stores_by_id),
                Scope.filter_for_action(
                    "store:edit", "user", user_id, {
                        "profile_id": profile_id,
                        "store_id": Store.id,
                    }
                ),
            )
        ).all()

        disconnect_m2m_related_objects_sync(
            ProductToStore,
            "product_id", product.id,
            "store_id", stores_to_disconnect_ids,
            commit=False,
        )

        sess().execute(
            delete(StoreProductSpotPrice).where(
                StoreProductSpotPrice.product_id == product.id,
                StoreProductSpotPrice.store_id.in_(stores_to_disconnect_ids)
            )
        )

    for store_data in data.stores:
        store = stores_by_id.get(store_data.store_id)

        if store not in product.stores:
            product.stores.append(store)

        spot_price = StoreProductSpotPrice.get_sync(
            product_id=product.id, store_id=store.id,
            for_update=True
        )

        price = round(
            store_data.price * 100
        ) if store_data.price is not None else product.price
        old_price = round(
            store_data.old_price * 100
        ) if store_data.old_price is not None else product.old_price

        if price != product.price or old_price != product.old_price:
            if not spot_price:
                spot_price = StoreProductSpotPrice(
                    product=product,
                    store=store,
                    price=price,
                    old_price=old_price,
                )
                sess().add(spot_price)
            else:
                spot_price.price = price
                spot_price.old_price = old_price
        elif spot_price:
            sess().delete(spot_price)

    sess().commit()
    return True


@db_func
def connect_categories_to_product(
        profile_id: int,
        user_id: int,
        product: StoreProduct,
        categories: list[StoreCategory],
        replace: bool = False,
):
    if replace:
        categories_to_disconnect_ids = sess().scalars(
            select(StoreCategory.id).where(
                StoreCategory.products.any(id=product.id),
                StoreCategory.id.not_in(map(attrgetter("id"), categories)),
                Scope.filter_for_action(
                    "category:edit", "user", user_id, {
                        "profile_id": profile_id,
                        "category_id": StoreCategory.id,
                    }
                ),
            )
        ).all()

        disconnect_m2m_related_objects_sync(
            ProductToCategory,
            "product_id", product.id,
            "category_id", categories_to_disconnect_ids,
            commit=False,
        )

    for category in categories:
        if category not in product.categories:
            product.categories.append(category)

    sess().commit()
    return True


@db_func
def remove_product_from_products_group(product_id: int):
    product = sess().query(StoreProduct).filter_by(id=product_id).first()
    if product:
        product.product_group_id = None
        sess().commit()
        return True


@db_func
def connect_characteristics_to_product(
        profile_id: int,
        user_id: int,
        product: StoreProduct,
        characteristics: list[StoreCharacteristic],
        data: schemas.AdminConnectCharacteristicsToProductData,
):

    characteristic_ids_to_update: dict[int, StoreCharacteristic] = dict(
        map(lambda x: (x.id, x), characteristics or [])
    )

    if data.replace:
        stmt = (
            select(StoreCharacteristicValue.id)
            .where(
                StoreCharacteristicValue.product_id == product.id,
                StoreCharacteristicValue.characteristic_id.notin_(
                    characteristic_ids_to_update.keys()
                ),
                Scope.filter_for_action(
                    "characteristic:read",
                    "user", user_id,
                    {
                        "profile_id": profile_id,
                        "characteristic_id": StoreCharacteristicValue.characteristic_id,
                    },
                )
            )
        )
        result = sess().execute(stmt)

        ids_to_delete = [row[0] for row in result]
        delete_stmt = delete(StoreCharacteristicValue).where(
            StoreCharacteristicValue.id.in_(ids_to_delete)
        )
        sess().execute(delete_stmt)

    for char_data in data.characteristics:
        if char_data.characteristic_id in characteristic_ids_to_update:
            upsert_stmt = insert(StoreCharacteristicValue).values(
                characteristic_id=char_data.characteristic_id,
                product_id=product.id,
                value=char_data.value
            ).on_duplicate_key_update(
                value=char_data.value
            )
            sess().execute(upsert_stmt)
            sess().flush()

            record = sess().query(StoreCharacteristicValue).filter_by(
                product_id=product.id,
                characteristic_id=char_data.characteristic_id
            ).first()
            if char_data.translations:
                update_object_translations_sync(record, char_data.translations)

    sess().flush()
    update_products_group_hashes_sync(product_ids=(product.id,))

    sess().commit()
    return True


@db_func
def update_image_in_gallery(
        gallery_item_id: int,
        media_id: int,
        position: int | None,
) -> bool:
    try:
        gallery_item = sess().query(GalleryItem).filter_by(id=gallery_item_id).one()

        gallery_item.media_id = media_id

        if position is not None:
            gallery_item.position = position

        sess().commit()
        return True
    except NoResultFound:
        sess().rollback()
        return False
    except IntegrityError as e:
        sess().rollback()
        return False


@db_func
def update_position_images_in_gallery(
        images_ids: list[int],
):
    for index, image_id in enumerate(images_ids):
        stmt = update(GalleryItem).where(GalleryItem.id == image_id).values(
            position=index
        )
        sess().execute(stmt)

    sess().commit()


@db_func
def add_products_to_store(
        store_id: int,
        products: list[schemas.AdminProductAddToStoreSchema],
        prices: Dict[int, Dict[str, float | int]] | None = None,
):
    store: Store = sess().get(Store, store_id)
    if not store:
        raise ValueError("Store not found")

    product_ids = [p.id for p in products]
    existing_links = sess().scalars(
        select(ProductToStore)
        .where(
            ProductToStore.store_id == store_id,
            ProductToStore.product_id.in_(product_ids),
        )
    ).all()
    existing_product_ids = {link.product_id for link in existing_links}

    new_links = []
    new_spot_prices = []
    added_link_ids: set[int] = set()
    added_spot_price_ids: set[int] = set()

    for product_data in products:
        pid = product_data.id

        if pid not in existing_product_ids and pid not in added_link_ids:
            new_links.append(
                ProductToStore(product_id=pid, store_id=store_id)
            )
            added_link_ids.add(pid)

        if prices and pid in prices:
            pd = prices[pid]
            price = round(pd.get('price', 0) * 100) if pd.get(
                'price'
            ) else product_data.price or 0
            old_price = (round(pd.get('old_price', 0) * 100)
                         if pd.get('old_price') else product_data.old_price or None)
        else:
            price = round(product_data.price) if product_data.price else 0
            old_price = round(
                product_data.old_price
            ) if product_data.old_price else None

        if pid not in added_spot_price_ids:
            spot_price = StoreProductSpotPrice.get_sync(
                product_id=pid, store_id=store_id
            )
            if not spot_price:
                new_spot_prices.append(
                    StoreProductSpotPrice(
                        product_id=pid,
                        store_id=store_id,
                        price=price,
                        old_price=old_price
                    )
                )
            else:
                spot_price.price = price
                spot_price.old_price = old_price

            added_spot_price_ids.add(pid)

    if new_links:
        sess().bulk_save_objects(new_links)
    if new_spot_prices:
        sess().bulk_save_objects(new_spot_prices)

    sess().commit()
    return True


@db_func
def remove_products_from_store(
        store_id: int,
        products: list[int],
):
    sess().query(ProductToStore).filter(
        ProductToStore.store_id == store_id,
        ProductToStore.product_id.in_(products)
    ).delete(synchronize_session=False)

    sess().query(StoreProductSpotPrice).filter(
        StoreProductSpotPrice.store_id == store_id,
        StoreProductSpotPrice.product_id.in_(products)
    ).delete(synchronize_session=False)

    sess().commit()
    return True


@db_func
def mass_delete_products(
        products: list[int],
):
    sess().execute(
        update(StoreProduct).where(
            StoreProduct.id.in_(products)
        ).values(
            is_deleted=True
        )
    )
    sess().commit()
    return True


@db_func
def mass_change_active_status_products(
        products: list[int],
        status: bool,
):
    sess().execute(
        update(StoreProduct).where(
            StoreProduct.id.in_(products)
        ).values(
            is_enabled=status
        )
    )
    sess().commit()
    return True


@db_func
def mass_change_status_products(
        products: list[int],
        status: bool,
):
    sess().execute(
        update(StoreProduct).where(
            StoreProduct.id.in_(products)
        ).values(
            is_available=status
        )
    )
    sess().commit()
    return True


@db_func
def add_products_to_category(product_ids: list[int], category_id: int):
    category = sess().execute(
        select(StoreCategory).where(StoreCategory.id == category_id)
    ).scalar_one_or_none()

    if not category:
        raise ValueError(f"Category with ID {category_id} not found.")

    products = sess().scalars(
        select(StoreProduct).where(StoreProduct.id.in_(product_ids))
    ).all()

    for product in products:
        if category not in product.categories:
            product.categories.append(category)

    sess().commit()
    return True


@db_func
def remove_products_from_category(product_ids: list[int], category_id: int):
    category = sess().execute(
        select(StoreCategory).where(StoreCategory.id == category_id)
    ).scalar_one_or_none()

    if not category:
        raise ValueError(f"Category with ID {category_id} not found.")

    sess().execute(
        delete(ProductToCategory)
        .where(ProductToCategory.category_id == category_id)
        .where(ProductToCategory.product_id.in_(product_ids))
    )

    sess().commit()
    return True
