import logging
from dataclasses import dataclass
from decimal import Decimal
from operator import itemgetter
from typing import Any, Iterable, Optional, Type

from itertools import groupby
from pydantic import BaseModel, ValidationError, validator
from sqlalchemy import (
    and_, desc, distinct, not_, or_, select, text, union, String,
)
from sqlalchemy.engine import Row
from sqlalchemy.orm import Query, aliased
from sqlalchemy.sql import Select, label
from sqlalchemy.sql.expression import exists, func

import schemas
from core.ext.types import SelectMode
from core.payment.exceptions import (
    LiqpayUnitNameEmptyError, LiqpayUnitNameError,
    LiqpayVndCodeError,
)
from db import db_func, sess
from db.helpers import statement_to_str
from db.models import (
    Brand, Gallery, GalleryItem, Group, MediaObject, ProductToCategory, ProductToStore,
    Scope, Store,
    StoreAttribute, StoreAttributeGroup, StoreCategory,
    StoreCategoryToStore, StoreCharacteristic, StoreCharacteristicFiltersSet,
    StoreCharacteristicValue,
    StoreProduct, StoreProductGroup,
    StoreProductGroupCharacteristic, StoreProductSpotPrice, Translation,
)
from loggers import JSONLogger
from schemas import CharacteristicFilterType, StoreProductPosterCheck
from schemas.payment_settings.liqpay.schemas import LiqpayItem

debugger = logging.getLogger('debugger.product.read')


def calculate_product_group_hash(product = StoreProduct, c_name = "c"):
    pg: Type[StoreProductGroup] = aliased(StoreProductGroup)
    pgc: Type[StoreProductGroupCharacteristic] = aliased(
        StoreProductGroupCharacteristic
    )
    c: Type[StoreCharacteristic] = aliased(StoreCharacteristic, name=c_name)
    cv: Type[StoreCharacteristicValue] = aliased(StoreCharacteristicValue)

    value = (
        func.IFNULL(
            func.CONCAT(
                product.product_group_id,
                "|%|",
                (
                    select(
                        func.CONCAT(
                            pgc.characteristic_id,
                            "|%|",
                            func.GROUP_CONCAT(
                                cv.value.distinct().op("ORDER BY")(
                                    text(
                                        f"{c_name}.position IS NULL, "
                                        f"{c_name}.position, "
                                        f"{c_name}.excel_row_number IS NULL, "
                                        f"{c_name}.excel_row_number, "
                                        f"{c_name}.id "
                                        "SEPARATOR \"|%|\"",
                                    )
                                )
                            )
                        )
                    ).select_from(cv)
                    .join(c, cv.characteristic_id == c.id)
                    .join(pgc, pgc.characteristic_id == c.id)
                    .join(pg, pgc.product_group_id == pg.id)
                    .where(
                        cv.product_id == product.id,
                        pgc.product_group_id == product.product_group_id,
                        pgc.show_one_modification.is_(False),
                        pgc.is_modifier.is_(True),
                        pg.is_deleted.is_(False),
                        c.is_deleted.is_(False),
                    )
                    .group_by(pgc.product_group_id)
                ).scalar_subquery()
            ),
            func.IFNULL(product.product_group_id, product.id)
        )
    )

    return func.SHA2(value, 256)


def filter_products_by_filters_set(
        query_or_stmt: Query | Select | None,
        store_id: Any,
        filters_set_or_id: int | None = None,
        filters_data: schemas.ProductListFiltersData | None = None,
        is_spot_prices_joined: bool = False,
        filter_prices: bool = True,
        product_cls: Type[StoreProduct] = StoreProduct,
        spot_price_cls: Type[StoreProductSpotPrice] = StoreProductSpotPrice,
):
    conditions = []

    if filters_data and filters_set_or_id:
        raise ValueError(
            "Only one of filters_data or filters_set_or_id can be specified"
        )

    if filters_set_or_id:
        filters_set: StoreCharacteristicFiltersSet | None

        if isinstance(filters_set_or_id, StoreCharacteristicFiltersSet):
            filters_set = filters_set_or_id
        else:
            filters_set = (
                sess()
                .query(StoreCharacteristicFiltersSet)
                .filter_by(id=filters_set_or_id)
                .one_or_none()
            )
            if not filters_set:
                conditions.append(text("0") == 1)
                return query_or_stmt.where(*conditions) if query_or_stmt else conditions

        filters_data = schemas.ProductListFiltersData(
            min_price=filters_set.min_price,
            max_price=filters_set.max_price,
        )

        for filter in filters_set.filters:
            filters_data.filters.append(
                schemas.ProductListFilterData(
                    characteristic_id=filter.characteristic_id,
                    filter_type=schemas.CharacteristicFilterType(filter.filter_type),
                    value=filter.value,
                    values_list=filter.values_list,
                    range_min=filter.range_min,
                    range_max=filter.range_max,
                )
            )

    if not filters_data:
        return query_or_stmt if query_or_stmt else conditions

    if filter_prices:
        if (
                store_id
                and not is_spot_prices_joined
                and query_or_stmt is not None
                and (
                filters_data.min_price is not None or
                filters_data.max_price is not None
        )
        ):
            query_or_stmt = query_or_stmt.outerjoin(
                spot_price_cls,
                and_(
                    spot_price_cls.product_id == product_cls.id,
                    spot_price_cls.store_id == store_id,
                ),
            )

        if store_id:
            product_price_expr = func.IFNULL(
                spot_price_cls.price, product_cls.price
            )

        else:
            product_price_expr = product_cls.price

        if filters_data.min_price is not None:
            conditions.append(
                product_price_expr >= round(filters_data.min_price * 100)
            )
        if filters_data.max_price is not None:
            conditions.append(
                product_price_expr <= round(filters_data.max_price * 100)
            )

    for filter in filters_data.filters:
        cv = aliased(StoreCharacteristicValue)
        filter_subq = sess().query(cv)
        filter_subq = filter_subq.filter(
            cv.product_id == product_cls.id
        )
        filter_subq = filter_subq.filter(
            cv.characteristic_id == filter.characteristic_id
        )

        match filter.filter_type:
            case (
            CharacteristicFilterType.VALUE
            | CharacteristicFilterType.INTEGER
            | CharacteristicFilterType.FLOAT
            ):
                filter_subq = filter_subq.filter(
                    cv.value == filter.value
                )
            case CharacteristicFilterType.MULTI:
                if filter.values_list:
                    filter_subq = filter_subq.filter(
                        cv.value.in_(filter.values_list)
                    )
            case CharacteristicFilterType.RANGE_INTEGER:
                if filter.range_min:
                    filter_subq = filter_subq.filter(
                        cv.value >= filter.range_min
                    )
                if filter.range_max:
                    filter_subq = filter_subq.filter(
                        cv.value <= filter.range_max
                    )
            case filter_type:
                raise ValueError(f"Currency not supported filter type {filter_type}")

        conditions.append(filter_subq.exists())

    if query_or_stmt is not None:
        return query_or_stmt.where(*conditions)
    return conditions


@db_func
def get_store_products(
        brand_id: int,
        store_id: int | None = None,
        params: schemas.ProductsListParams | None = None,
        cursor:
        schemas.ProductsCategoriesCursor |
        schemas.ProductPriceCursor | None = None,
        lang: str | None = None,
        hide_modification: bool = True,
        with_translations: bool = True,
        is_exists: bool = False,
        sort_by_availability: bool = True
) -> (
        list[tuple[StoreProduct, StoreCategory, Translation | None]] |
        list[tuple[StoreProduct, StoreCategory]] |
        list[tuple[StoreProduct, Translation | None]] |
        list[StoreProduct] |
        bool
):
    query_objects = [StoreProduct]

    if lang and with_translations and not is_exists:
        query_objects.append(Translation)

    is_categories_sort = params and params.sort == schemas.ProductsSortEnum.CATEGORIES

    if is_categories_sort or (params and params.category_id):
        category_conditions = [
            StoreCategory.is_deleted.is_(False),
            StoreCategory.brand_id == brand_id,
        ]
        if store_id:
            category_conditions.append(
                StoreCategoryToStore.store_id == store_id
            )

        top_stmt = (
            select(
                StoreCategory.id.label("category_id"),
                StoreCategory.id.label("tree_id"),
            )
            .where(*category_conditions)
        )
        if store_id:
            top_stmt = top_stmt.join(
                StoreCategoryToStore,
                StoreCategoryToStore.category_id == StoreCategory.id
            )
        if params.category_id:
            top_stmt = top_stmt.where(
                or_(
                    StoreCategory.father_category_id == params.category_id,
                    StoreCategory.id == params.category_id,
                )
            )
        else:
            top_stmt = top_stmt.where(
                StoreCategory.father_category_id.is_(None)
            )

        categories_tree = top_stmt.cte("categories_tree", recursive=True)

        bottom_stmt = (
            select(
                StoreCategory.id.label("category_id"),
                categories_tree.c.tree_id,
            )
            .join(
                categories_tree,
                StoreCategory.father_category_id == categories_tree.c.category_id,
            )
            .where(*category_conditions)
        )
        if store_id:
            bottom_stmt = bottom_stmt.join(
                StoreCategoryToStore,
                StoreCategoryToStore.category_id == StoreCategory.id
            )

        categories_tree = categories_tree.union(bottom_stmt)

        query_objects.append(StoreCategory)
    else:
        categories_tree = None

    stmt = select(*query_objects)

    if (
            lang and
            not is_exists and
            (with_translations or (params and params.search_text))
    ):
        stmt = stmt.outerjoin(
            Translation,
            and_(
                Translation.obj_type == 'StoreProduct',
                Translation.obj_id == func.cast(StoreProduct.id, String),
                Translation.lang == lang
            )
        )

    if is_categories_sort:
        ptc_conditions = [
            ProductToCategory.product_id == StoreProduct.id
        ]
        if not params.category_id:
            ptc_conditions.append(StoreProduct.is_available.is_(True))

        stmt = (
            stmt
            .outerjoin(
                ProductToCategory,
                and_(*ptc_conditions)
            )
            .outerjoin(
                categories_tree,
                categories_tree.c.category_id == ProductToCategory.category_id,
            )
            .outerjoin(
                StoreCategory,
                StoreCategory.id == categories_tree.c.tree_id,
            )
        )

    def apply_filters(
            stmt_: Select,
            p: Type[StoreProduct],
            sp: Type[StoreProductSpotPrice],
            is_spot_prices_joined: bool = False,
    ):
        pts = aliased(ProductToStore)

        stmt_ = stmt_.where(
            p.brand_id == brand_id,
            p.is_deleted.is_(False),
            p.is_enabled.is_(True)
        )
        if store_id:
            stmt_ = (
                stmt_.join(pts, pts.product_id == p.id)
                .where(pts.store_id == store_id)
            )

        if params:
            if params.filters_set_id or params.filters:
                stmt_ = filter_products_by_filters_set(
                    stmt_, store_id,
                    params.filters_set_id,
                    params.filters,
                    product_cls=p,
                    spot_price_cls=sp,
                    is_spot_prices_joined=is_spot_prices_joined,
                )
            if params.search_text:
                stmt_ = stmt_.where(
                    p.search(
                        params.search_text, lang,
                        is_translation_joined=True,
                    )
                )

        return stmt_

    stmt = apply_filters(
        stmt,
        StoreProduct,
        StoreProductSpotPrice,
    )

    order_by = []

    # m_stmt is a modification statement
    if hide_modification:
        p = aliased(StoreProduct)
        sp = aliased(StoreProductSpotPrice)
        m_stmt = (
            select(func.MIN(p.position))
        )
        m_stmt = apply_filters(
            m_stmt,
            p,
            sp,
        )

        m_stmt = m_stmt.where(
            StoreProduct.group_hash == p.group_hash
        )

        if params and params.category_id:
            ptc = aliased(ProductToCategory)
            ct = aliased(categories_tree)
            m_stmt = m_stmt.where(
                exists().where(
                    ptc.product_id == p.id,
                    ptc.category_id == ct.c.category_id,
                )
            )

        stmt = stmt.where(
            StoreProduct.position == m_stmt.scalar_subquery()
        )

    if params:
        if params.sort == schemas.ProductsSortEnum.CATEGORIES:
            if params.category_id:
                categories_tree_2 = aliased(categories_tree)
                ptc = aliased(ProductToCategory)

                stmt = stmt.where(
                    or_(
                        and_(
                            StoreCategory.father_category_id == params.category_id,
                            StoreProduct.is_available.is_(True)
                        ),
                        and_(
                            StoreCategory.id == params.category_id,
                            or_(
                                StoreProduct.is_available.is_(False),
                                not_(
                                    exists().where(
                                        categories_tree_2.c.tree_id == StoreCategory.id,
                                        categories_tree_2.c.category_id !=
                                        StoreCategory.id,
                                        ptc.category_id ==
                                        categories_tree_2.c.category_id,
                                        ptc.product_id == StoreProduct.id,
                                    )
                                )
                            )
                        )
                    )
                )
            else:
                stmt = stmt.where(
                    StoreCategory.father_category_id.is_(None)
                )
        elif params.category_id:
            stmt = stmt.where(
                StoreProduct.categories.any(
                    StoreCategory.filter_recursive(params.category_id)
                )
            )

        if sort_by_availability:
            order_by.append(StoreProduct.is_available.is_(False))

        if cursor:
            cursor_condition = StoreProduct.position > cursor.product_position
        else:
            cursor_condition = None

        match params.sort:
            case schemas.ProductsSortEnum.CATEGORIES:
                if cursor:
                    if cursor.category_position is None:
                        cursor_condition = and_(
                            StoreCategory.position.is_(None),
                            cursor_condition,
                        )
                    else:
                        cursor_condition = and_(
                            or_(
                                StoreCategory.position >
                                cursor.category_position,
                                StoreCategory.position.is_(None),
                                and_(
                                    StoreCategory.position ==
                                    cursor.category_position,
                                    cursor_condition,
                                )
                            )
                        )

                order_by.extend(
                    (
                        StoreCategory.position.is_(None),
                        StoreCategory.position,
                    )
                )
            case schemas.ProductsSortEnum.LOW_PRICE:
                if cursor:
                    cursor_condition = and_(
                        or_(
                            StoreProduct.price > cursor.price,
                            and_(
                                StoreProduct.price == cursor.price,
                                cursor_condition
                            )
                        )
                    )

                order_by.append(StoreProduct.price)
            case schemas.ProductsSortEnum.HIGH_PRICE:
                if cursor:
                    cursor_condition = and_(
                        or_(
                            StoreProduct.price < cursor.price,
                            and_(
                                StoreProduct.price == cursor.price,
                                cursor_condition
                            )
                        )
                    )

                order_by.append(StoreProduct.price.desc())

        if cursor_condition is not None:
            if cursor.is_available:
                stmt = stmt.where(
                    or_(
                        StoreProduct.is_available.is_(False),
                        cursor_condition
                    )
                )
            else:
                stmt = stmt.where(
                    StoreProduct.is_available.is_(False),
                    cursor_condition,
                )

    if is_exists:
        return sess().scalar(select(stmt.exists()))

    if is_categories_sort:
        stmt = stmt.group_by(
            StoreProduct.id,
            StoreCategory.id,
        )
    else:
        stmt = stmt.group_by(StoreProduct.id)

    stmt = stmt.order_by(
        *order_by,
        StoreProduct.position,
        StoreProduct.id,
    )

    if params and params.limit and not params.is_export:
        stmt = stmt.limit(params.limit)

    JSONLogger("get_store_products").debug(
        "products_query", f"brand={brand_id}, store={store_id}", {
            "brand_id": brand_id,
            "store_id": store_id,
            "params": params,
            "statement": str(statement_to_str(stmt)),
            "cursor": cursor,
        }
    )

    if len(query_objects) == 1:
        return sess().scalars(stmt).all()

    return sess().execute(stmt).fetchall()


def get_store_product_spot_price_sync(
        store_id: int, product_id: int
) -> tuple[int, int] | None:
    query = sess().query(StoreProductSpotPrice.price, StoreProductSpotPrice.old_price)
    query = query.filter(StoreProductSpotPrice.store_id == store_id)
    query = query.filter(StoreProductSpotPrice.product_id == product_id)
    return query.one_or_none()


@db_func
def get_store_product_spot_price(
        store_id: int, product_id: int
) -> tuple[int, int] | None:
    return get_store_product_spot_price_sync(store_id, product_id)


@db_func
def get_category_products_count(
        category_id: int,
        store_id: int,
        filters_set_id: int | None = None,
        filters_data: schemas.ProductListFiltersData | None = None,
        lang: str | None = None,
        search_text: str | None = None,
) -> int:
    query = sess().query(func.count(distinct(calculate_product_group_hash())))
    query = query.select_from(StoreProduct)

    query = query.filter(StoreProduct.stores.any(id=store_id))

    query = query.filter(StoreProduct.is_deleted.is_(False))
    query = query.filter(StoreProduct.is_available.is_(True))

    query = query.filter(
        StoreProduct.categories.any(
            or_(
                StoreCategory.id == category_id,
                StoreCategory.id.in_(
                    StoreCategory.get_child_categories_ids_query(category_id)
                )
            )
        )
    )

    if search_text:
        if not lang:
            raise ValueError("lang argument is required for search")
        query = query.filter(StoreProduct.search(search_text, lang))

    query = filter_products_by_filters_set(
        query, store_id, filters_set_id, filters_data
    )

    return query.scalar()


@db_func
def get_products_min_max_prices(
        store_id: int,
        category_id: int | None = None,
        filters_set_id: int | None = None,
        filters_data: schemas.ProductListFiltersData | None = None,
) -> list[StoreProduct]:
    query = sess().query(
        func.min(func.IFNULL(StoreProductSpotPrice.price, StoreProduct.price)),
        func.max(func.IFNULL(StoreProductSpotPrice.price, StoreProduct.price)),
    )
    query = query.select_from(StoreProduct)

    query = query.filter(StoreProduct.is_deleted.is_(False))
    query = query.filter(StoreProduct.is_available.is_(True))
    query = query.filter(StoreProduct.is_enabled.is_(True))

    query = query.join(ProductToStore, ProductToStore.product_id == StoreProduct.id)
    query = query.filter(ProductToStore.store_id == store_id)

    query = query.outerjoin(
        StoreProductSpotPrice,
        and_(
            StoreProductSpotPrice.product_id == StoreProduct.id,
            StoreProductSpotPrice.store_id == ProductToStore.store_id,
        ),
    )

    if category_id:
        query = query.join(
            ProductToCategory, ProductToCategory.product_id == StoreProduct.id
        )
        query = query.filter(
            or_(
                ProductToCategory.category_id.in_(
                    StoreCategory.get_child_categories_ids_query(category_id)
                ),
                ProductToCategory.category_id == category_id,
            )
        )

    query = filter_products_by_filters_set(
        query, store_id, filters_set_id, filters_data, filter_prices=False
    )

    return query.one_or_none()


@db_func
def find_product_modification(
        product_id: int,
        data: schemas.FindProductModificationData,
) -> StoreProduct | None:
    product: StoreProduct = (
        sess().query(StoreProduct).filter_by(id=product_id).one_or_none()
    )
    if not product or not product.product_group_id:
        return None

    query = sess().query(StoreProduct)
    query = query.join(StoreProduct.characteristics)

    query = query.filter(StoreProduct.product_group_id == product.product_group_id)

    query = query.filter(
        StoreCharacteristicValue.characteristic_id == data.find_for_characteristic_id
    )
    query = query.filter(
        StoreCharacteristicValue.value == data.filters[data.find_for_characteristic_id]
    )

    query = query.filter(StoreProduct.is_deleted.is_(False))
    query = query.filter(StoreProduct.is_available.is_(True))
    query = query.filter(StoreProduct.is_enabled.is_(True))

    alias = aliased(StoreCharacteristicValue)
    subq = sess().query(func.count(alias.id.distinct()))
    subq = subq.filter(alias.product_id == StoreProduct.id)
    subq = subq.filter(
        or_(
            *[
                and_(
                    alias.characteristic_id == characteristic_id,
                    alias.value == characteristic_value,
                )
                for characteristic_id, characteristic_value in data.filters.items()
            ]
        )
    )

    query = query.order_by(desc(subq.scalar_subquery()))

    query = query.order_by(StoreProduct.position)
    query = query.order_by(StoreProduct.name)
    query = query.order_by(StoreProduct.product_id)

    query = query.limit(1)

    return query.one_or_none()


@db_func
def get_product_groups(brand_id: int) -> list[StoreProductGroup]:
    query = sess().query(StoreProductGroup)
    query = query.filter(StoreProductGroup.is_deleted.is_(False))
    query = query.filter(StoreProductGroup.brand_id == brand_id)
    return query.all()


@db_func
def get_products_for_check(product_ids: list[int]):
    query = sess().query(
        StoreProduct.id,
        StoreProduct.name,
        StoreProduct.external_type,
        StoreProduct.external_id,
    )
    result = query.filter(StoreProduct.id.in_(product_ids)).all()
    return [
        StoreProductPosterCheck(
            product_id=id,
            name=name,
            external_type=external_type,
            external_id=external_id
        ) for id, name, external_type, external_id in result]


@db_func
def get_invalid_products(product_ids: list[int], store_id: int, ):
    unavailable_products_query = select(
        StoreProduct.id,
        StoreProduct.name,
        StoreProduct.external_type,
    ).filter(
        StoreProduct.id.in_(product_ids),
        (StoreProduct.is_available == False) | (StoreProduct.is_deleted == True)
    )

    products_not_in_store_query = select(
        StoreProduct.id,
        StoreProduct.name,
        StoreProduct.is_available
    ).filter(
        StoreProduct.id.in_(product_ids)
    ).filter(
        ~StoreProduct.id.in_(
            select(ProductToStore.product_id).filter(
                ProductToStore.store_id == store_id
            )
        )
    )

    combined_query = union(unavailable_products_query, products_not_in_store_query)

    result = sess().execute(combined_query)
    return result.fetchall()


@db_func
def get_product_as_tips(brand_id: int, tips_sku: str) -> list[StoreProductGroup]:
    query = sess().query(StoreProduct)
    query = query.filter(
        StoreProduct.is_deleted.is_(False),
        StoreProduct.brand_id == brand_id,
        StoreProduct.product_id == tips_sku,
    )
    return query.one_or_none()


@db_func
def get_admin_products_list(
        profile_id: int,
        user_id: int,  # filter by user's access
        store_id: int | None = None,
        # When shown for specific store. Store prices will be returned
        store_ids: Iterable[int] | None = None,  # Just filter
        category_ids: Iterable[int] | None = None,
        search_text: str | None = None,
        offset: int | None = None,
        limit: int | None = None,
        need_check_access: bool = True,
        is_count: bool = False,
        without_categories: bool = False,
        without_stores: bool = False,
        product_type: str | None = None,
) -> list[Row]:
    if store_id:
        if store_ids:
            raise ValueError("Only one of store_ids or store_ids can be specified")
        store_ids = [store_id]

    product_media = aliased(MediaObject)
    product_thumbnail_media = aliased(MediaObject)

    available_data = {
        "profile_id": profile_id,
        "product_id": StoreProduct.id,
    }

    if is_count:
        stmt: Select = select(func.count(distinct(StoreProduct.id)))
        if need_check_access:
            stmt = stmt.where(
                Scope.filter_for_action(
                    "product:read",
                    "user", user_id,
                    available_data,
                )
            )
    else:
        if store_id:
            price_expr = func.IFNULL(
                StoreProductSpotPrice.price,
                StoreProduct.price
            )
            old_price_expr = func.IFNULL(
                StoreProductSpotPrice.old_price,
                StoreProduct.old_price
            )
        else:
            price_expr = StoreProduct.price
            old_price_expr = StoreProduct.old_price

        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="product",
            target="user",
            target_id=user_id,
            available_data=available_data,
        )

        subquery = select(StoreCategory.name).where(
            and_(
                StoreCategory.id == ProductToCategory.category_id,
                ProductToCategory.product_id == StoreProduct.id
            )
        ).limit(1).as_scalar()

        stmt: Select = select(
            StoreProduct.id,
            StoreProduct.type,
            StoreProduct.product_id,
            StoreProduct.is_enabled,
            StoreProduct.product_group_id,
            StoreProduct.external_id,
            StoreProduct.external_type,
            StoreProduct.name,
            StoreProduct.is_available,
            label("image_url", product_media.url),
            label("thumbnail_url", product_thumbnail_media.url),
            label("price", price_expr),
            label("old_price", old_price_expr),
            StoreProduct.is_weight,
            StoreProduct.weight_unit,
            StoreProduct.description,
            StoreProduct.internal_name,
            StoreProduct.buy_min_quantity,
            StoreProduct.position,
            StoreProduct.floating_sum_enabled,
            StoreProduct.floating_sum_min,
            StoreProduct.floating_sum_max,
            StoreProduct.floating_sum_options,
            StoreProduct.floating_sum_user_sum_enabled,
            StoreProduct.floating_qty_enabled,
            StoreProduct.pti_info_text,
            StoreProduct.pti_info_link,
            read_allowed, edit_allowed,
            StoreProduct.liqpay_id,
            StoreProduct.liqpay_unit_name,
            StoreProduct.liqpay_codifier,
            StoreProduct.liqpay_tax_list,
            StoreProduct.need_auth,
            Group.id.label("profile_id"),
            subquery.label("categories_str"),
        ).distinct()

        stmt = stmt.outerjoin(product_media, StoreProduct.media_id == product_media.id)
        stmt = stmt.outerjoin(
            product_thumbnail_media,
            StoreProduct.thumbnail_media_id == product_thumbnail_media.id,
        )

        if store_id:
            stmt = stmt.outerjoin(
                StoreProductSpotPrice,
                and_(
                    StoreProductSpotPrice.product_id == StoreProduct.id,
                    StoreProductSpotPrice.store_id == store_id,
                ),
            )

        if need_check_access:
            stmt = stmt.where(read_allowed.is_(True))

    stmt = stmt.join(StoreProduct.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Brand.group_id == profile_id)

    stmt = stmt.where(StoreProduct.is_deleted.is_(False))

    if store_ids:
        stmt = stmt.where(
            StoreProduct.stores.any(
                and_(
                    Store.id.in_(store_ids),
                    Scope.filter_for_action(
                        "store:read",
                        "user",
                        user_id,
                        {
                            "profile_id": profile_id,
                            "store_id": Store.id,
                        },
                    ),
                )
            )
        )
    if category_ids and not without_categories:
        stmt = stmt.where(
            StoreProduct.categories.any(
                and_(
                    StoreCategory.id.in_(category_ids),
                    Scope.filter_for_action(
                        "category:read",
                        "user",
                        user_id,
                        {
                            "profile_id": profile_id,
                            "category_id": StoreCategory.id,
                        },
                    ),
                )
            )
        )

    if without_categories:
        stmt = stmt.where(not_(StoreProduct.categories.any()))

    if without_stores:
        stmt = stmt.where(not_(StoreProduct.stores.any()))

    if search_text:
        stmt = stmt.where(StoreProduct.search(search_text, product_id_contains=True))

    if product_type:
        stmt = stmt.where(StoreProduct.type == product_type)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    if is_count:
        return sess().scalar(stmt)

    stmt = stmt.order_by(StoreProduct.position.is_(None))
    stmt = stmt.order_by(StoreProduct.position)

    stmt = stmt.order_by(StoreProduct.id)

    return sess().execute(stmt).fetchall()


@db_func
def get_product_by_id_and_profile_id(
        product_id: int, profile_id: int
) -> StoreProduct | None:
    stmt: Select = select(StoreProduct)
    stmt = stmt.join(StoreProduct.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)

    stmt = stmt.where(StoreProduct.id == product_id)
    stmt = stmt.where(StoreProduct.is_deleted.is_(False))

    return sess().scalar(stmt)


class LiqpayProduct(BaseModel):
    name: str
    price: Decimal
    unit_name: str
    vndcode: str
    codifier: Optional[str] = None
    tax_list: Optional[str] = None
    category_name: str = "7loc"
    barcode: str = ""
    editable_price: str = "T"
    weight_product: str = ""

    @validator('price')
    def validate_price(cls, value):
        if not value or value == 0:
            return Decimal("0.01")
        return value

    @validator('name')
    def validate_name(cls, value):
        return value.strip().replace("\n", " ").replace("\r", " ").replace("\t", " ")


@db_func
def get_products_for_liqpay(
        brand_id: int,
        store_id: int | None = None,
) -> list[LiqpayProduct] | None:
    stmt = (
        select(
            StoreProduct.name,
            func.round(StoreProduct.price / 100, 2).label("price"),
            StoreProduct.liqpay_unit_name.label("unit_name"),
            func.concat("G", StoreProduct.product_id).label("vndcode"),
            StoreProduct.liqpay_codifier.label("codifier"),
            StoreProduct.liqpay_tax_list.label("tax_list")
        )
        .join(StoreProduct.brand)
        .join(Brand.group)
    )

    if store_id:
        stmt = stmt.join(ProductToStore, ProductToStore.product_id == StoreProduct.id)

    stmt = stmt.where(
        Group.status == "enabled",
        Group.id == Brand.group_id,
        StoreProduct.brand_id == brand_id,
        StoreProduct.is_deleted == 0,
        StoreProduct.type.in_(["goods", "service"]),
        StoreProduct.product_id.is_not(None),
    )

    if store_id:
        stmt = stmt.where(ProductToStore.store_id == store_id)

    result = sess().execute(stmt).fetchall()

    processed_results = []

    for r in result:
        if r["vndcode"] == "G":
            debugger.debug(
                f'get_products_for_liqpay -> liqpay vndcode not filled: {brand_id=}, '
                f'{r["name"]=}'
            )
            raise LiqpayVndCodeError(r["name"])

        if not r["unit_name"]:
            debugger.debug(
                f'get_products_for_liqpay -> liqpay unit_name not filled: '
                f'{brand_id=}, {r["unit_name"]=}'
            )
            raise LiqpayUnitNameEmptyError(r["vndcode"], r["unit_name"])

        try:
            _ = LiqpayItem(liqpay_unit_name=r["unit_name"])
        except ValidationError as err:
            logging.error(err, exc_info=True)
            logging.error(
                f'Not valid liqpay unit name: {brand_id=}, {r["vndcode"]=}, '
                f'{r["unit_name"]=}',
                exc_info=True
            )
            raise LiqpayUnitNameError(r["vndcode"], r["unit_name"])

        processed_results.append(
            LiqpayProduct(
                name=r["name"],
                price=r["price"],
                unit_name=r["unit_name"],
                vndcode=r["vndcode"],
                codifier=r["codifier"],
                tax_list=r["tax_list"]
            )
        )

    return processed_results


@dataclass
class LoadedModifierOption:
    value_obj: StoreCharacteristicValue
    translation: Translation | None
    is_available: bool


@dataclass
class LoadedModifier:
    modifier: StoreCharacteristic
    value_obj: StoreCharacteristicValue
    modifier_translation: Translation | None
    value_translation: Translation | None
    options: list[LoadedModifierOption]


@dataclass
class ProductMediaData:
    media: MediaObject | None = None
    thumbnail_media: MediaObject | None = None


@dataclass
class ProductAttributeGroupData:
    attribute_group: StoreAttributeGroup
    translation: Translation | None
    attributes: list[tuple[StoreAttribute, Translation | None]]


@dataclass
class ProductObjectsData:
    modifiers: dict[int, LoadedModifier]
    characteristics: list[tuple[
        StoreCharacteristic, StoreCharacteristicValue, Translation | None,
                                                       Translation | None]]
    spot_prices: tuple[float, float | None] | None
    categories: list[int]
    media_data: ProductMediaData
    gallery_items: list[tuple[GalleryItem, MediaObject]]
    attribute_groups: list[ProductAttributeGroupData]


@db_func
def get_products_objects(product_ids: list[int], store_id: int, lang: str):
    def get_modifiers():
        product_modification = aliased(StoreProduct)
        modifier_value = aliased(StoreCharacteristicValue)
        modifier_value_translation = aliased(Translation)

        characteristic_translation = aliased(Translation)
        characteristic_value_translation = aliased(Translation)

        query = sess().query(
            StoreProduct.id,
            StoreCharacteristic,
            StoreCharacteristicValue,
            characteristic_translation,
            characteristic_value_translation,
            modifier_value,
            modifier_value_translation,
            label("modifier_is_available", func.max(product_modification.is_available)),
        ).select_from(StoreProduct).distinct()

        query = query.join(StoreProduct.characteristics)
        query = query.join(StoreCharacteristicValue.characteristic)
        query = query.outerjoin(
            characteristic_translation,
            and_(
                characteristic_translation.obj_type == 'StoreCharacteristic',
                characteristic_translation.obj_id == func.cast(StoreCharacteristic.id, String),
                characteristic_translation.lang == lang
            )
        )
        query = query.outerjoin(
            characteristic_value_translation,
            and_(
                characteristic_value_translation.obj_type == 'StoreCharacteristicValue',
                characteristic_value_translation.obj_id == func.cast(StoreCharacteristicValue.id, String),
                characteristic_value_translation.lang == lang
            )
        )

        query = query.join(
            product_modification,
            and_(
                product_modification.product_group_id == StoreProduct.product_group_id,
                product_modification.stores.any(id=store_id)
            )
        )
        query = query.join(
            modifier_value,
            and_(
                modifier_value.product_id == product_modification.id,
                modifier_value.characteristic_id == StoreCharacteristic.id
            )
        )
        query = query.outerjoin(
            modifier_value_translation,
            and_(
                modifier_value_translation.obj_type == 'StoreCharacteristicValue',
                modifier_value_translation.obj_id == func.cast(modifier_value.id, String),
                modifier_value_translation.lang == lang
            )
        )

        query = query.filter(StoreProduct.id.in_(product_ids))
        query = query.filter(StoreProduct.product_group_id.isnot(None))
        query = query.filter(StoreProduct.is_deleted.is_(False))
        query = query.filter(StoreCharacteristic.is_deleted.is_(False))
        query = query.filter(
            StoreCharacteristic.product_groups.any(
                and_(
                    StoreProductGroupCharacteristic.product_group_id ==
                    StoreProduct.product_group_id,
                    StoreProductGroupCharacteristic.is_modifier.is_(True)
                )
            )
        )
        query = query.filter(product_modification.is_deleted.is_(False))

        query = query.order_by(StoreProduct.id)
        query = query.order_by(StoreCharacteristic.position.is_(None))
        query = query.order_by(StoreCharacteristic.position)
        query = query.order_by(StoreCharacteristic.name)
        query = query.order_by(StoreCharacteristic.id)

        query = query.order_by(product_modification.position.is_(None))
        query = query.order_by(product_modification.position)

        query = query.group_by(
            StoreProduct.id, StoreCharacteristic.id, modifier_value.value
        )

        def make_modifier(
                modifier: StoreCharacteristic, modifier_data: Iterable[tuple]
        ):
            value_obj: StoreCharacteristicValue | None = None
            modifier_translation: Translation | None = None
            value_translation: Translation | None = None
            options = []

            for el in modifier_data:
                if not value_obj:
                    value_obj = el[2]
                if not modifier_translation:
                    modifier_translation = el[3]
                if not value_translation:
                    value_translation = el[4]
                options.append(
                    LoadedModifierOption(
                        value_obj=el[5],
                        translation=el[6],
                        is_available=el[7],
                    )
                )

            return LoadedModifier(
                modifier=modifier,
                value_obj=value_obj,
                modifier_translation=modifier_translation,
                value_translation=value_translation,
                options=options,
            )

        return {
            product_id: {
                modifier.id: make_modifier(modifier, modifier_data_raw)
                for modifier, modifier_data_raw
                in groupby(product_data, key=itemgetter(1))
            }
            for product_id, product_data in groupby(query.all(), key=itemgetter(0))
        }

    def get_characteristics():
        characteristic_translation = aliased(Translation)
        characteristic_value_translation = aliased(Translation)
        query = sess().query(
            StoreProduct.id,
            StoreCharacteristic,
            StoreCharacteristicValue,
            characteristic_translation,
            characteristic_value_translation,
        )
        query = query.join(StoreProduct.characteristics)
        query = query.join(StoreCharacteristicValue.characteristic)
        query = query.outerjoin(
            characteristic_translation,
            and_(
                characteristic_translation.obj_type == 'StoreCharacteristic',
                characteristic_translation.obj_id == func.cast(StoreCharacteristic.id, String),
                characteristic_translation.lang == lang
            )
        )
        query = query.outerjoin(
            characteristic_value_translation,
            and_(
                characteristic_value_translation.obj_type == 'StoreCharacteristicValue',
                characteristic_value_translation.obj_id == func.cast(StoreCharacteristicValue.id, String),
                characteristic_value_translation.lang == lang
            )
        )

        query = query.filter(StoreProduct.id.in_(product_ids))
        query = query.filter(StoreCharacteristic.is_hide.is_(False))
        query = query.filter(StoreCharacteristic.is_deleted.is_(False))

        subq = sess().query(StoreProductGroupCharacteristic.id)
        subq = subq.filter(
            StoreProductGroupCharacteristic.characteristic_id == StoreCharacteristic.id
        )
        subq = subq.filter(
            StoreProductGroupCharacteristic.product_group_id ==
            StoreProduct.product_group_id
        )
        subq = subq.filter(StoreProductGroupCharacteristic.is_modifier.is_(True))

        query = query.filter(not_(subq.exists()))

        query = query.group_by(StoreProduct.id, StoreCharacteristic.id)

        query = query.order_by(StoreProduct.id)
        query = query.order_by(StoreCharacteristic.position.is_(None))
        query = query.order_by(StoreCharacteristic.position)
        query = query.order_by(StoreCharacteristic.name)
        query = query.order_by(StoreCharacteristic.id)

        return {
            product_id: [el[1:] for el in product_data]
            for product_id, product_data in groupby(query.all(), key=itemgetter(0))
        }

    def get_attributes():
        attribute_group_translation = aliased(Translation)
        attribute_translation = aliased(Translation)

        query = sess().query(
            StoreProduct.id,
            StoreAttributeGroup,
            attribute_group_translation,
            StoreAttribute,
            attribute_translation,
        )

        query = query.join(StoreProduct.attribute_groups)
        query = query.join(StoreAttributeGroup.attributes)
        query = query.outerjoin(
            attribute_group_translation,
            and_(
                attribute_group_translation.obj_type == 'StoreAttributeGroup',
                attribute_group_translation.obj_id == func.cast(StoreAttributeGroup.id, String),
                attribute_group_translation.lang == lang
            )
        )
        query = query.outerjoin(
            attribute_translation,
            and_(
                attribute_translation.obj_type == 'StoreAttribute',
                attribute_translation.obj_id == func.cast(StoreAttribute.id, String),
                attribute_translation.lang == lang
            )
        )

        query = query.filter(StoreProduct.id.in_(product_ids))
        query = query.filter(StoreAttributeGroup.is_deleted.is_(False))

        query = query.order_by(StoreProduct.id)

        query = query.order_by(StoreAttributeGroup.position.is_(None))
        query = query.order_by(StoreAttributeGroup.position)

        query = query.order_by(StoreAttributeGroup.name)
        query = query.order_by(StoreAttributeGroup.id)

        query = query.filter(StoreAttribute.is_deleted.is_(False))
        query = query.order_by(StoreAttribute.is_available.desc())
        query = query.order_by(StoreAttribute.name)
        query = query.order_by(StoreAttribute.id)

        query = query.group_by(
            StoreProduct.id, StoreAttributeGroup.id, StoreAttribute.id
        )

        def make_attribute_group(
                attribute_group: StoreAttributeGroup,
                attribute_group_data: Iterable[tuple]
        ):
            translation: Translation | None = None
            attributes = []

            for el in attribute_group_data:
                if not translation:
                    translation = el[2]
                attributes.append(el[3:5])

            return ProductAttributeGroupData(
                attribute_group=attribute_group,
                translation=translation,
                attributes=attributes
            )

        return {
            product_id: [
                make_attribute_group(attribute_group, attribute_group_data)
                for attribute_group, attribute_group_data
                in groupby(product_data, key=itemgetter(1))
            ]
            for product_id, product_data in groupby(query.all(), key=itemgetter(0))
        }

    def get_spot_prices():
        query = sess().query(
            StoreProductSpotPrice.product_id,
            StoreProductSpotPrice.price,
            StoreProductSpotPrice.old_price
        )
        query = query.filter(StoreProductSpotPrice.product_id.in_(product_ids))
        query = query.filter(StoreProductSpotPrice.store_id == store_id)

        return {
            product_id: list(product_data)[0][1:]
            for product_id, product_data in groupby(query.all(), key=itemgetter(0))
        }

    def get_categories():
        query = sess().query(
            ProductToCategory.product_id,
            StoreCategory.id
        )
        query = query.join(
            StoreCategory,
            ProductToCategory.category_id == StoreCategory.id
        )

        query = query.filter(ProductToCategory.product_id.in_(product_ids))
        query = query.filter(StoreCategory.is_deleted.is_(False))
        query = query.filter(StoreCategory.stores.any(id=store_id))

        query = query.order_by(ProductToCategory.product_id)

        query = query.order_by(StoreCategory.position.is_(None))
        query = query.order_by(StoreCategory.position)

        query = query.order_by(StoreCategory.name)
        query = query.order_by(StoreCategory.id)

        query = query.group_by(ProductToCategory.product_id, StoreCategory.id)

        return {
            product_id: list(map(itemgetter(1), product_data))
            for product_id, product_data in groupby(query.all(), key=itemgetter(0))
        }

    def get_media():
        media_cls = aliased(MediaObject)
        thumbnail_media_cls = aliased(MediaObject)

        query = sess().query(
            StoreProduct.id,
            media_cls,
            thumbnail_media_cls,
        )
        query = query.join(media_cls, StoreProduct.media_id == media_cls.id)
        query = query.outerjoin(
            thumbnail_media_cls,
            StoreProduct.thumbnail_media_id == thumbnail_media_cls.id
        )

        query = query.filter(StoreProduct.id.in_(product_ids))

        query = query.group_by(StoreProduct.id)

        # noinspection PyTypeChecker
        return {
            product_id: ProductMediaData(media, thumbnail_media)
            for product_id, media, thumbnail_media in query.all()
        }

    def get_gallery_items():
        query = sess().query(
            StoreProduct.id,
            GalleryItem,
            MediaObject
        )
        query = query.join(StoreProduct.gallery)
        query = query.join(Gallery.gallery_items)
        query = query.join(GalleryItem.media)

        query = query.filter(StoreProduct.id.in_(product_ids))

        query = query.order_by(StoreProduct.id)
        query = query.order_by(GalleryItem.position)

        return {
            product_id: [el[1:] for el in product_data]
            for product_id, product_data in groupby(query.all(), key=itemgetter(0))
        }

    modifiers = get_modifiers()
    characteristics = get_characteristics()
    spot_prices = get_spot_prices()
    categories = get_categories()
    medias = get_media()
    gallery_items = get_gallery_items()
    attribute_groups = get_attributes()

    return {
        product_id: ProductObjectsData(
            modifiers.get(product_id, {}),
            characteristics.get(product_id, []),
            spot_prices.get(product_id),
            categories.get(product_id, []),
            medias.get(product_id, ProductMediaData()),
            gallery_items.get(product_id, []),
            attribute_groups.get(product_id, []),
        )
        for product_id in product_ids
    }


@db_func
def get_all_products_basic_list(
        profile_id: int,
        user_id: int,
        store_id: int | None = None,
        store_ids: Iterable[int] | None = None,
        category_ids: Iterable[int] | None = None,
        search_text: str | None = None,
        offset: int | None = None,
        limit: int | None = None,
        need_check_access: bool = True,
        is_count: bool = False,
        without_categories: bool = False,
        without_stores: bool = False,
        mode: SelectMode = SelectMode.SELECTED,
        included: list[int] | None = None,
        excluded: list[int] | None = None,
        product_type: str | None = None
) -> list[Row]:
    if store_id:
        if store_ids:
            raise ValueError("Only one of store_id or store_ids can be specified")
        store_ids = [store_id]

    available_data = {
        "profile_id": profile_id,
        "product_id": StoreProduct.id,
    }

    if is_count:
        stmt: Select = select(func.count(distinct(StoreProduct.id)))
        if need_check_access:
            stmt = stmt.where(
                Scope.filter_for_action(
                    "product:read",
                    "user", user_id,
                    available_data,
                )
            )
    else:
        stmt: Select = select(
            StoreProduct.id,
            StoreProduct.internal_name,
            StoreProduct.price,
            StoreProduct.old_price,
        )

        if need_check_access:
            stmt = stmt.where(
                Scope.filter_for_action(
                    "product:read",
                    "user", user_id,
                    available_data,
                )
            )

        stmt = stmt.join(StoreProduct.brand)
        stmt = stmt.join(Brand.group)
        stmt = stmt.where(Group.status == 'enabled')
        stmt = stmt.where(Group.id == profile_id)
        stmt = stmt.where(StoreProduct.is_deleted.is_(False))

        if without_categories:
            stmt = stmt.where(not_(StoreProduct.categories.any()))

        if without_stores:
            stmt = stmt.where(not_(StoreProduct.stores.any()))

        if search_text:
            stmt = stmt.where(StoreProduct.internal_name.ilike(f"%{search_text}%"))

        if product_type:
            stmt = stmt.where(StoreProduct.type == product_type)

        if store_ids:
            stmt = stmt.join(
                ProductToStore, ProductToStore.product_id == StoreProduct.id
            )
            stmt = stmt.where(ProductToStore.store_id.in_(store_ids))

        if category_ids:
            stmt = stmt.join(
                ProductToCategory, ProductToCategory.product_id == StoreProduct.id
            )
            stmt = stmt.where(ProductToCategory.category_id.in_(category_ids))

        if mode == SelectMode.ALL and excluded:
            stmt = stmt.where(~StoreProduct.id.in_(excluded))
        elif mode == SelectMode.SELECTED and included:
            stmt = stmt.where(StoreProduct.id.in_(included))

        stmt = stmt.order_by(StoreProduct.position.is_(None))
        stmt = stmt.order_by(StoreProduct.position)
        stmt = stmt.order_by(StoreProduct.id)

        if offset is not None:
            stmt = stmt.offset(offset)

        if limit is not None:
            stmt = stmt.limit(limit)

    return sess().execute(stmt).all()


def get_products_to_disable(brand_id: int) -> list[StoreProduct]:
    stmt = select(StoreProduct)

    stmt = stmt.where(
        StoreProduct.brand_id == brand_id,
        StoreProduct.is_deleted.is_(False),
        StoreProduct.is_enabled.is_(True),
    )

    stmt = stmt.order_by(
        StoreProduct.position.desc(),
        StoreProduct.id.desc(),
    )
    return sess().scalars(stmt).all()
