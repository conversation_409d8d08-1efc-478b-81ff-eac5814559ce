from sqlalchemy.exc import IntegrityError

from db import db_func, sess
from db.models import GalleryItem


@db_func
def delete_product_photo_from_gallery(gallery_item_id: int, gallery_id: int) -> bool:
    try:
        gallery_item = sess().query(GalleryItem).filter_by(id=gallery_item_id, gallery_id=gallery_id).first()

        if gallery_item:
            sess().delete(gallery_item)
            sess().commit()
            return True
        else:
            return False
    except IntegrityError:
        sess().rollback()
        return False
