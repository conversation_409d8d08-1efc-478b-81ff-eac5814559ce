import schemas
from db import db_func, sess
from db.crud.translation.update import update_object_translations_sync
from db.models import (
    ClientWebPage, InvoiceTemplate, Store,
)


@db_func
def update_client_web_page(
        client_web_page: ClientWebPage,
        data: schemas.AdminClientWebPageUpdateData,
):

    client_web_page_object_data = data.dict(
        exclude_unset=True, exclude={
            "translations", "stores", "invoice_templates",
        }
    )

    if client_web_page_object_data:
        for key, value in client_web_page_object_data.items():
            if key != 'raw_internal_name':
                setattr(client_web_page, key, value)

        if data.raw_internal_name:
            client_web_page._internal_name = client_web_page_object_data[
                'raw_internal_name']
        else:
            client_web_page._internal_name = None

    if data.translations:
        update_object_translations_sync(client_web_page, data.translations)

    if data.stores is not None:
        current_store_ids = {store.id for store in client_web_page.stores}
        new_store_ids = set(data.stores)

        stores_to_add = new_store_ids - current_store_ids
        if stores_to_add:
            new_stores = sess().query(Store).filter(
                Store.id.in_(stores_to_add)
            ).all()
            client_web_page.stores.extend(new_stores)

        stores_to_remove = current_store_ids - new_store_ids
        if stores_to_remove:
            client_web_page.stores = [
                store for store in client_web_page.stores if
                store.id not in stores_to_remove
            ]

    if data.invoice_templates is not None:
        current_template_ids = {template.id for template in
                                client_web_page.invoice_templates}
        new_template_ids = set(data.invoice_templates)

        templates_to_add = new_template_ids - current_template_ids
        if templates_to_add:
            new_templates = sess().query(InvoiceTemplate).filter(
                InvoiceTemplate.id.in_(templates_to_add)
            ).all()
            client_web_page.invoice_templates.extend(new_templates)

        templates_to_remove = current_template_ids - new_template_ids
        if templates_to_remove:
            client_web_page.invoice_templates = [
                template for template in client_web_page.invoice_templates if
                template.id not in templates_to_remove
            ]

    sess().commit()

    return client_web_page


@db_func
def check_and_disable_previous_special_pages(
        page_type: schemas.ClientWebPageTypeEnum,
        group_id: int,
        show_in_profile: bool = False,
        store_ids: list[int] | None = None,
        template_ids: list[int] | None = None,
):
    if store_ids:
        pages = (
            sess().query(ClientWebPage)
            .filter(
                ClientWebPage.group_id == group_id,
                ClientWebPage.type == page_type,
                ClientWebPage.stores.any(Store.id.in_(store_ids)),
            )
            .all()
        )
        for p in pages:
            p.stores = [s for s in p.stores if s.id not in store_ids]

    if template_ids:
        pages = (
            sess().query(ClientWebPage)
            .filter(
                ClientWebPage.group_id == group_id,
                ClientWebPage.type == page_type,
                ClientWebPage.invoice_templates.any(
                    InvoiceTemplate.id.in_(template_ids)
                ),
            )
            .all()
        )
        for p in pages:
            p.invoice_templates = [
                t for t in p.invoice_templates if t.id not in template_ids
            ]
    if show_in_profile:
        pages = (
            sess().query(ClientWebPage)
            .filter(
                ClientWebPage.group_id == group_id,
                ClientWebPage.type == page_type,
                ClientWebPage.show_in_profile.is_(True),
            )
            .all()
        )
        for p in pages:
            p.show_in_profile = False
