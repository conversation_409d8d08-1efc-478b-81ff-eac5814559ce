import logging
from typing import Iterable

from sqlalchemy import (
    and_, distinct, not_, or_, select,
)
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select
from sqlalchemy.sql.expression import func

from db import db_func, sess
from db.models import (
    ClientWebPage, Group, InvoiceTemplate, Scope, Store,
    StoreProduct, Translation,
)
from schemas import ClientWebPageTypeEnum

debugger = logging.getLogger('debugger.client_web_page.read')


@db_func
def is_client_web_page_slug_exist(
        slug: str, profile_id: int, exclude_client_web_page_id: int | None = None
) -> bool:
    stmt: Select = select(func.count(ClientWebPage.id))
    stmt = stmt.where(ClientWebPage.slug == slug)
    stmt = stmt.where(ClientWebPage.group_id == profile_id)

    if exclude_client_web_page_id:
        stmt = stmt.where(ClientWebPage.id != exclude_client_web_page_id)

    return sess().scalar(stmt) > 0


@db_func
def get_admin_client_web_pages(
        profile: Group,
        user_id: int,  # filter by user's access
        store_ids: Iterable[int] | None = None,  # Just filter
        invoice_templates_ids: Iterable[int] | None = None,
        search_text: str | None = None,
        types: Iterable[str] | None = None,
        offset: int | None = None,
        limit: int | None = None,
        is_count: bool = False,
) -> list[Row] | int:

    if is_count:
        stmt: Select = select(func.count(distinct(ClientWebPage.id)))
    else:
        stmt: Select = select(
            ClientWebPage.id,
            ClientWebPage.type,
            ClientWebPage.position,
            ClientWebPage.is_enabled,
            ClientWebPage.show_in_profile,
            ClientWebPage.title,
            ClientWebPage.slug,
            ClientWebPage.button_title,
            ClientWebPage.internal_name,
            ClientWebPage.custom_container_max_width,
            ClientWebPage.container_max_width,
            ClientWebPage.group_id.label('profile_id'),
        ).distinct()

    stmt = stmt.join(ClientWebPage.group)

    stmt = stmt.where(Group.id == profile.id)

    if store_ids:
        stmt = stmt.where(
            ClientWebPage.stores.any(
                and_(
                    Store.id.in_(store_ids),
                    Scope.filter_for_action(
                        "store:read",
                        "user",
                        user_id,
                        {
                            "profile_id": profile.id,
                            "store_id": Store.id,
                        },
                    ),
                )
            )
        )
    if invoice_templates_ids:
        stmt = stmt.where(
            ClientWebPage.invoice_templates.any(
                and_(
                    InvoiceTemplate.id.in_(invoice_templates_ids),
                    Scope.filter_for_action(
                        "invoice_template:read",
                        "user",
                        user_id,
                        {
                            "profile_id": profile.id,
                            "invoice_template_id": InvoiceTemplate.id,
                        },
                    ),
                )
            )
        )

    if search_text:
        stmt = stmt.where(ClientWebPage.search(search_text))

    if types:
        stmt = stmt.where(ClientWebPage.type.in_(types))

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    if is_count:
        return sess().scalar(stmt)

    stmt = stmt.order_by(ClientWebPage.position.is_(None))
    stmt = stmt.order_by(ClientWebPage.position)

    stmt = stmt.order_by(ClientWebPage.id)

    return sess().execute(stmt).fetchall()


@db_func
def get_client_web_pages_for_client(
        profile_id: int,
        with_translations: bool = True,
        lang: str | None = None,
        store_id: int | None = None,
        invoice_template_id: int | None = None,
        types: list[ClientWebPageTypeEnum] | None = None,
) -> list[Row]:
    documents_types = ["agreement", "about"]

    types_values = None
    if types is not None:
        types_values = [
            t.value if hasattr(t, "value") else t
            for t in types
        ]

    query_objects = [
        ClientWebPage.id,
        ClientWebPage.type,
        ClientWebPage.position,
        ClientWebPage.title,
        ClientWebPage.slug,
        ClientWebPage.is_enabled,
        ClientWebPage.button_title,
        ClientWebPage.internal_name,
        ClientWebPage.group_id,
        ClientWebPage.container_max_width,
        ClientWebPage.custom_container_max_width,
    ]

    if with_translations and lang:
        query_objects.append(Translation)

    stmt = select(*query_objects).distinct()

    stmt = stmt.join(ClientWebPage.group)

    if with_translations and lang:
        stmt = stmt.outerjoin(Translation, Translation.filter(ClientWebPage, lang))

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(ClientWebPage.is_enabled.is_(True))

    if store_id and invoice_template_id:
        stmt = stmt.where(
            ClientWebPage.stores.any(Store.id == store_id),
            ClientWebPage.invoice_templates.any(
                InvoiceTemplate.id == invoice_template_id
            )
        )
    elif store_id:
        stmt = stmt.where(
            ClientWebPage.stores.any(Store.id == store_id),
        )
    elif invoice_template_id:
        stmt = stmt.where(
            ClientWebPage.invoice_templates.any(
                InvoiceTemplate.id == invoice_template_id
            ),
        )
    else:
        stmt = stmt.where(ClientWebPage.show_in_profile.is_(True))

    if types_values:
        stmt = stmt.where(ClientWebPage.type.in_(types_values))
    else:
        stmt = stmt.where(
            or_(
                not_(ClientWebPage.type.in_(documents_types)),
                ClientWebPage.show_in_navbar.is_(True),
            )
        )

    stmt = stmt.order_by(ClientWebPage.position.is_(None))
    stmt = stmt.order_by(ClientWebPage.position)
    stmt = stmt.order_by(ClientWebPage.id)

    print(stmt.compile(compile_kwargs={"literal_binds": True}))

    result = sess().execute(stmt).fetchall()
    return result


@db_func
def get_client_web_page_by_id_and_profile_id(
        client_web_page_id: int, profile_id: int,
) -> StoreProduct | None:
    stmt: Select = select(ClientWebPage)
    stmt = stmt.join(ClientWebPage.group)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)

    stmt = stmt.where(ClientWebPage.id == client_web_page_id)

    return sess().scalar(stmt)


@db_func
def get_client_web_page_by_slug_and_profile_id(
        slug: str, profile_id: int,
        with_translations: bool = True,
        lang: str | None = None
) -> Row | None:
    query_objects = [
        ClientWebPage
    ]

    if with_translations and lang:
        query_objects.append(Translation)

    stmt = select(*query_objects).distinct()
    stmt = stmt.join(ClientWebPage.group)

    if with_translations and lang:
        stmt = stmt.outerjoin(Translation, Translation.filter(ClientWebPage, lang))

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(ClientWebPage.slug == slug)

    stmt = stmt.where(ClientWebPage.is_enabled == True)

    result = sess().execute(stmt).first()
    return result


@db_func
def get_client_web_page_by_type_and_profile_id(
        profile_id: int,
        type: ClientWebPageTypeEnum,
        with_translations: bool = True,
        lang: str | None = None,
        store_id: int | None = None,
        invoice_template_id: int | None = None,
) -> Row | None:
    query_objects = [
        ClientWebPage
    ]

    if with_translations and lang:
        query_objects.append(Translation)

    stmt = select(*query_objects).distinct()
    stmt = stmt.join(ClientWebPage.group)

    if with_translations and lang:
        stmt = stmt.outerjoin(Translation, Translation.filter(ClientWebPage, lang))

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(ClientWebPage.type == type)

    if store_id:
        stmt = stmt.where(
            ClientWebPage.stores.any(Store.id == store_id),
        )
    if invoice_template_id:
        stmt = stmt.where(
            ClientWebPage.invoice_templates.any(
                InvoiceTemplate.id == invoice_template_id
            ),
        )

    stmt = stmt.where(ClientWebPage.is_enabled == True)

    result = sess().execute(stmt).first()
    return result


@db_func
def get_client_web_page_store_ids(page_id: int) -> list[int]:
    return sess().scalars(
        select(Store.id)
        .where(
            Store.client_web_pages.any(id=page_id),
            Store.is_enabled.is_(True),
            Store.is_deleted.is_(False),
        )
        .order_by(Store.position, Store.id)
    ).all()


@db_func
def get_client_web_page_invoice_template_ids(page_id: int) -> list[int]:
    return sess().scalars(
        select(InvoiceTemplate.id)
        .where(
            InvoiceTemplate.client_web_pages.any(id=page_id),
            InvoiceTemplate.is_deleted.is_(False),
        )
        .order_by(InvoiceTemplate.id.desc())
    ).all()
