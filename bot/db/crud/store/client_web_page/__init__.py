from .create import (
    create_client_web_page, create_default_pages,
)
from .delete import delete_client_web_page
from .read import (
    get_admin_client_web_pages, get_client_web_page_by_id_and_profile_id,
    get_client_web_page_by_slug_and_profile_id,
    get_client_web_page_by_type_and_profile_id,
    get_client_web_page_invoice_template_ids, get_client_web_page_store_ids,
    get_client_web_pages_for_client, is_client_web_page_slug_exist,
)
from .update import (check_and_disable_previous_special_pages, update_client_web_page)
