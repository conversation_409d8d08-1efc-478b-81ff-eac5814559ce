from db import db_func, sess
from db.models import (
    ClientWebPage, ClientWebPageToInvoiceTemplate,
    ClientWebPageToStore,
)


@db_func
def delete_client_web_page(client_web_page_id: int, profile_id: int) -> bool:
    related_models = [ClientWebPageToInvoiceTemplate, ClientWebPageToStore]

    for model in related_models:
        sess().query(model).filter_by(client_web_page_id=client_web_page_id).delete(
            synchronize_session=False
        )

    rows_deleted = (
        sess()
        .query(ClientWebPage)
        .filter_by(id=client_web_page_id, group_id=profile_id)
        .delete(synchronize_session=False)
    )

    sess().commit()

    if rows_deleted > 0:
        return True
    else:
        sess().rollback()
        return False
