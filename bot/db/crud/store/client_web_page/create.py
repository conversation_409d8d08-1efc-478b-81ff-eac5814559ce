from sqlalchemy import not_, select

import schemas
from db import db_func, sess
from db.crud.sort.update import reorder_object_sync
from db.crud.translation.update import update_object_translations_sync
from db.decorators.other import safe_deadlock_handler
from db.models import (
    ClientWebPage, Group, InvoiceTemplate, Store,
)


def create_client_web_page_sync(
        group: Group,
        data: schemas.AdminClientWebPageCreateData,
        stores: list[Store] | None = None,
        invoice_templates: list[InvoiceTemplate] | None = None,
        commit: bool = True,
) -> ClientWebPage:
    client_web_page = ClientWebPage(
        group=group,
        stores=stores or [],
        invoice_templates=invoice_templates or [],
        _internal_name=data.raw_internal_name,
        **data.dict(
            exclude_unset=True,
            exclude={
                "stores",
                "invoice_templates",
                "raw_internal_name",
                "translations",
                "button_title",
                "section",
            }
        ),
        position=0,
        button_title=data.button_title or data.title,
    )

    sess().add(client_web_page)
    sess().flush()

    reorder_object_sync(
        ClientWebPage,
        client_web_page.id,
        0,
        group_id=group.id,
        commit=False,
    )

    if data.translations:
        updated_translations = {}
        for key, value in data.translations.items():
            if value is not None:
                updated_translations[key] = value
            else:
                updated_translations[key] = None

        update_object_translations_sync(client_web_page, updated_translations)

    if commit:
        sess().commit()

    return client_web_page


@db_func
def create_client_web_page(
        group: Group,
        data: schemas.AdminClientWebPageCreateData,
        stores: list[Store] | None = None,
        invoice_templates: list[InvoiceTemplate] | None = None,
) -> ClientWebPage:
    return create_client_web_page_sync(
        group, data, stores, invoice_templates,
    )


DEFAULT_SLUGS = {
    schemas.ClientWebPageTypeEnum.MENU: "menu",
    schemas.ClientWebPageTypeEnum.STORES: "select",
    schemas.ClientWebPageTypeEnum.FASTPAY: "/"
}


@db_func
@safe_deadlock_handler
def create_default_pages(
        group: Group,
        types_to_create: list[schemas.ClientWebPageTypeEnum],
        titles: dict[str, str],
):
    pages = []

    for type_ in types_to_create:
        page = sess().scalar(
            select(ClientWebPage)
            .where(
                ClientWebPage.type == type_,
                ClientWebPage.is_enabled.is_(True),
                ClientWebPage.group_id == group.id,
            )
            .order_by(
                ClientWebPage.is_enabled.desc(),
                ClientWebPage.position,
            )
            .limit(1)
            .with_for_update(),
        )

        if not page and type_ in DEFAULT_SLUGS:
            page = create_client_web_page_sync(
                group, schemas.AdminClientWebPageCreateData(
                    title=titles[type_.value],
                    is_enabled=True,
                    show_in_profile=type_ == schemas.ClientWebPageTypeEnum.STORES,
                    slug=DEFAULT_SLUGS[type_],
                    type=type_,
                    container_max_width=schemas.ClientWebPageContainerMaxWidthEnum.xl,
                ),
                commit=False
            )

        pages.append(page)

        match type_:
            case schemas.ClientWebPageTypeEnum.MENU:
                page.stores += sess().scalars(
                    select(Store)
                    .where(
                        Store.is_deleted.is_(False),
                        Store.brand.has(group_id=group.id),
                        not_(
                            Store.client_web_pages.any(
                                type=type_,
                                is_enabled=True
                            )
                        )
                    )
                ).all()
            case schemas.ClientWebPageTypeEnum.FASTPAY:
                page.invoice_templates += sess().scalars(
                    select(InvoiceTemplate)
                    .where(
                        InvoiceTemplate.is_deleted.is_(False),
                        InvoiceTemplate.group_id == group.id,
                        not_(
                            InvoiceTemplate.client_web_pages.any(
                                type=type_,
                                is_enabled=True
                            )
                        )
                    )
                )
            case schemas.ClientWebPageTypeEnum.STORES:
                page.show_in_profile = True
    sess().commit()

    return pages
