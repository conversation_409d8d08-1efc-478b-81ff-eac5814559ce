from typing import Iterable
from sqlalchemy import distinct, func, select, text
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

import schemas
from db import db_func, sess
from db.models import (Brand, Group, Scope, StoreAttribute, Translation)


@db_func
def get_attributes(
        attribute_group_id: int,
        lang: str | None = None,
) -> list[StoreAttribute] | list[tuple[StoreAttribute, Translation]]:
    if lang:
        query = sess().query(StoreAttribute, Translation)
        query = query.outerjoin(Translation, Translation.filter(StoreAttribute, lang))
    else:
        query = sess().query(StoreAttribute)

    query = query.filter(StoreAttribute.is_deleted.is_(False))
    query = query.filter(StoreAttribute.attribute_group_id == attribute_group_id)
    query = query.order_by(StoreAttribute.is_available.desc())

    return query.all()


@db_func
def get_attributes_by_ids(attr_ids: list[int]):
    query = sess().query(StoreAttribute)
    query = query.filter(StoreAttribute.id.in_(attr_ids))
    return query.all()


@db_func
def get_attribute_by_id_and_profile_id(attribute_group_id: int, profile_id: int) -> StoreAttribute | None:
    stmt: Select = select(StoreAttribute)
    stmt = stmt.join(StoreAttribute.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(StoreAttribute.id == attribute_group_id)
    stmt = stmt.where(StoreAttribute.is_deleted.is_(False))

    return sess().scalar(stmt)


@db_func
def get_admin_attributes_list(
        profile_id: int,
        user_id: int,
        attribute_group_id: int | None = None,
        search_text: str | None = None,
        exclude: list[int] | None = None,
        offset: int | None = None,
        limit: int | None = None,
        need_check_access: bool = False,
        is_count: bool = False,
) -> list[Row]:
    if is_count:
        stmt: Select = select(func.count(distinct(StoreAttribute.id)))
    else:
        stmt: Select = select(
            StoreAttribute.id,
            StoreAttribute.attribute_id,
            StoreAttribute.name,
            *Scope.allowed_scopes_list(
                "read", "edit",
                object_name="attribute",
                target="user",
                target_id=user_id,
                available_data={
                    "profile_id": profile_id,
                    "attribute_id": StoreAttribute.id,
                }
            ),
            StoreAttribute.is_available,
            StoreAttribute.price_impact,
            StoreAttribute.attribute_group_id,
            StoreAttribute.selected_by_default,
        )

        stmt = stmt.distinct()

    stmt = stmt.join(StoreAttribute.brand)
    stmt = stmt.join(Brand.group)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")

    stmt = stmt.where(StoreAttribute.is_deleted.is_(False))

    if attribute_group_id:
        stmt = stmt.where(StoreAttribute.attribute_group_id == attribute_group_id)

    if need_check_access:
        stmt = stmt.where(text("read_allowed IS TRUE"))

    if exclude:
        stmt = stmt.where(StoreAttribute.id.not_in(exclude))

    if search_text:
        stmt = stmt.where(StoreAttribute.name.contains(search_text))

    if is_count:
        return sess().scalar(stmt)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    stmt = stmt.order_by(StoreAttribute.id.desc())

    return sess().execute(stmt).fetchall()


@db_func
def get_attribute_group_attributes(
        attribute_group_id: int,
        fields: Iterable[str] | None = None,
        target: schemas.ScopeTarget | None = None,
        target_id: int | None = None,
        profile_id: int | None = None,
        need_scopes_allowed: bool = False,
) -> list[StoreAttribute] | list[Row]:
    stmt: Select

    if fields:
        select_fields = [getattr(StoreAttribute, field) for field in fields]
    else:
        select_fields = [StoreAttribute]

    if need_scopes_allowed:
        if target is None or target_id is None or profile_id is None:
            raise ValueError("When need_scopes_allowed is True, (target, target_id, profile_id) are required")

        select_fields.extend(
            Scope.allowed_scopes_list(
                "read", "edit",
                object_name="attribute",
                target=target,
                target_id=target_id,
                available_data={
                    "profile_id": profile_id,
                    "store_id": StoreAttribute.id
                }
            )
        )

    stmt = select(*select_fields)
    stmt = stmt.where(StoreAttribute.attribute_group_id == attribute_group_id)

    stmt = stmt.where(StoreAttribute.is_deleted.is_(False))

    if target is not None and target_id is not None and attribute_group_id is not None:
        stmt = stmt.where(
            Scope.filter_for_action(
                "attribute:read",
                target, target_id,
                available_data={
                    "profile_id": profile_id,
                    "attribute_id": StoreAttribute.id,
                }
            )
        )

    if len(select_fields) == 1:
        return sess().scalars(stmt).all()
    return sess().execute(stmt).fetchall()
