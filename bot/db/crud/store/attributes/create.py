import schemas
from db import db_func, sess
from db.crud.scope.create import grand_scopes_to_created_object_sync
from db.models import Brand, StoreAttribute, User


@db_func
def create_attribute(
        brand: Brand,
        data: schemas.AdminCreateAttributeData,
        creator: User | None = None,
) -> StoreAttribute:
    attribute_data = data.dict(exclude_unset=True, exclude={"translations"})

    if "price_impact" in attribute_data:
        attribute_data["price_impact"] = round(attribute_data["price_impact"] * 100)

    attribute = StoreAttribute(
        brand=brand,
        **attribute_data,
    )
    sess().add(attribute)

    if creator:
        grand_scopes_to_created_object_sync(
            "attribute", attribute, creator, {
                "profile_id": brand.group_id,
            }
        )

    sess().commit()
    return attribute
