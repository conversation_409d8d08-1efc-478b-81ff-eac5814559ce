import schemas
from db import db_func, sess
from db.crud.translation.update import clear_object_updated_fields_translations_sync, update_object_translations_sync
from db.models import StoreAttribute


@db_func
def update_attribute(
        attribute: StoreAttribute,
        data: schemas.AdminUpdateAttributeData | schemas.AdminAttributeListSchema,
        profile_langs: list[str],
):
    attribute_object_data = data.dict(
        exclude_unset=True, exclude={"translations", "id", "read_allowed", "edit_allowed"}
    )

    if "price_impact" in attribute_object_data:
        attribute_object_data["price_impact"] = round(attribute_object_data["price_impact"] * 100)

    if attribute_object_data:
        clear_object_updated_fields_translations_sync(attribute, attribute_object_data, profile_langs)
        attribute.update_sync(attribute_object_data, no_commit=True)

    if hasattr(data, "translations"):
        update_object_translations_sync(attribute, data.translations)

    sess().commit()

    return attribute
