from db import sess
from db.crud.store.admin_email.exceptions import AdminEmailDuplicateError
from db.models import AdminEmail
from schemas import TypeAdminEmailEnum


def check_duplicate_email(
    brand_id: int, email: str, store_id: int | None = None,
):
    query = sess().query(AdminEmail.id).filter(
        AdminEmail.brand_id == brand_id,
        AdminEmail.email == email,
        AdminEmail.is_deleted.is_(False),
    )
    if store_id is None:
        type_email = TypeAdminEmailEnum.BRAND
        query = query.filter(AdminEmail.store_id.is_(None), )
    else:
        type_email = TypeAdminEmailEnum.STORE
        query = query.filter(AdminEmail.store_id == store_id)
    result = query.all()
    if result:
        raise AdminEmailDuplicateError()
    return type_email
