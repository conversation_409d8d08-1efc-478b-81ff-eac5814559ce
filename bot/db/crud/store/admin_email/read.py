from sqlalchemy import distinct, func, or_, select
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

import schemas
from db import db_func, sess
from db.models import AdminEmail, Brand, Group, Scope
from db.types.operation import Operation


@db_func
def get_admin_email(
    brand_id: int,
    store_id: int | None = None,
) -> list[str]:
    query = sess().query(AdminEmail.email)
    query = query.filter(AdminEmail.brand_id == brand_id, AdminEmail.is_deleted.is_(False))
    if store_id:
        query = query.filter(AdminEmail.store_id == store_id)

    return query.one_or_none()


@db_func
def get_admin_emails(
    brand_id: int,
    store_id: int | None = None,
) -> list[str]:
    query = sess().query(AdminEmail.email)
    query = query.filter(AdminEmail.brand_id == brand_id, AdminEmail.is_deleted.is_(False))
    if store_id:
        query = query.filter(AdminEmail.store_id == store_id)
        result = query.all()
        if result:
            return [row.email for row in result]
    else:
        query = query.filter(AdminEmail.store_id.is_(None))

    return sess().scalars(query).all()


@db_func
def get_admin_admin_email_list(
    profile_id: int,
    user_id: int,
    params: schemas.AdminListParams,
    operation: Operation = "list",
) -> list[Row]:
    stmt: Select

    available_data = {
        "profile_id": profile_id,
        "admin_email_id": AdminEmail.id,
    }

    if operation == "count":
        stmt = select(func.count(distinct(AdminEmail.id)))
        stmt = stmt.where(
            Scope.filter_for_action(
                "profile:read",
                "user", user_id,
                available_data,
            )
        )
    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="profile",
            target="user",
            target_id=user_id,
            available_data=available_data
        )

        stmt = select(
            AdminEmail.id,
            Group.id.label("profile_id"),
            AdminEmail.creator_id,
            AdminEmail.email,
            AdminEmail.type_email,
            AdminEmail.store_id,
            AdminEmail.brand_id,
            read_allowed, edit_allowed,
        )
        stmt = stmt.where(read_allowed.is_(True))

    stmt = stmt.join(Brand, Brand.id == AdminEmail.brand_id)
    stmt = stmt.join(Group, Group.id == Brand.group_id)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(AdminEmail.is_deleted.is_(False))

    if params.store_id:
        stmt = stmt.where(AdminEmail.store_id == params.store_id)

    if params.search_text:
        params.search_text = params.search_text.strip()
        stmt = stmt.where(
            or_(
                AdminEmail.email.contains(params.search_text),
                AdminEmail.id == params.search_text,
            )
        )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(AdminEmail.time_created.desc(), AdminEmail.id.desc())

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    return sess().execute(stmt).fetchall()
