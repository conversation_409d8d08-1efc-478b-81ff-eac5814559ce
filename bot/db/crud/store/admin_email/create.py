from db import db_func, sess
from db.crud.store.admin_email.funcs import check_duplicate_email
from db.models import AdminEmail


@db_func
def add_admin_email(
    brand_id: int, email: str, store_id: int | None = None,
    creator_id: int | None = None
) -> AdminEmail:

    type_email = check_duplicate_email(brand_id, email, store_id)

    admin_email = AdminEmail(
        brand_id=brand_id,
        store_id=store_id,
        type_email=type_email,
        email=email,
        creator_id=creator_id,
    )
    sess().add(admin_email)
    sess().commit()
    return admin_email
