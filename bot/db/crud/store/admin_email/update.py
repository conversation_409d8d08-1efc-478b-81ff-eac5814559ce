from sqlalchemy import update

from db import db_func, sess
from db.crud.store.admin_email.funcs import check_duplicate_email
from db.models import AdminEmail


@db_func
def update_admin_email(
    admin_email_id: int, brand_id: int, email: str, store_id: int | None
):

    type_email = check_duplicate_email(brand_id, email, store_id)

    stmt = update(AdminEmail).where(AdminEmail.id == admin_email_id).values(
        brand_id=brand_id, type_email=type_email, store_id=store_id, email=email
    )
    sess().execute(stmt)
    sess().commit()
