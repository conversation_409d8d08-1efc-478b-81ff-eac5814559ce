from sqlalchemy import distinct, func, or_, select
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

import schemas
from config import WEBHOOK_MAX_RETRIES_FOR_BLOCK
from db import db_func, sess
from db.models import Group, Scope, Webhook, WebhookJournal
from db.types.operation import Operation


@db_func
def get_webhooks_by_entity(
    group_id: int,
    entity: schemas.WebhookEntityEnum,
) -> list[Webhook]:
    stmt = select(Webhook)
    stmt = stmt.where(
        Webhook.group_id == group_id,
        func.JSON_CONTAINS(Webhook.entities,  f'"{entity.value.upper()}"'),
        # Webhook.entities.contains(entity.value.upper()),
        Webhook.retries_count < WEBHOOK_MAX_RETRIES_FOR_BLOCK,
    )

    return sess().execute(stmt).scalars().all()


@db_func
def get_webhook_list(
    profile_id: int,
    user_id: int,
    params: schemas.AdminListParams,
    operation: Operation = "list",
) -> list[Row] | int:
    stmt: Select

    available_data = {
        "profile_id": profile_id,
    }

    if operation == "count":
        stmt = select(func.count(distinct(Webhook.id)))
        stmt = stmt.where(
            Scope.filter_for_action(
                "webhook:read",
                "user", user_id,
                available_data,
            )
        )
    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="webhook",
            target="user",
            target_id=user_id,
            available_data=available_data
        )

        stmt = select(
            Group.id.label("profile_id"),
            Webhook.id,
            Webhook.endpoint_url,
            Webhook.hook_id,
            Webhook.entities,
            Webhook.is_enabled,
            Webhook.last_sent_status,
            Webhook.retries_count,
            Webhook.last_sent_datetime,
            Webhook.description,
            read_allowed,
            edit_allowed
        )
        stmt = stmt.where(read_allowed.is_(True))

    stmt = stmt.join(Webhook.group)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Webhook.is_deleted.is_(False))

    if params.search_text:
        params.search_text = params.search_text.strip()
        stmt = stmt.where(
            or_(
                Webhook.endpoint_url.contains(params.search_text),
                Webhook.id == params.search_text,
            )
        )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(Webhook.id.desc())

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    return sess().execute(stmt).fetchall()


@db_func
def get_webhook_journal_list(
    webhook_id: int,
    profile_id: int,
    user_id: int,
    params: schemas.AdminWebhookJournalParams,
    operation: Operation = "list",
) -> list[Row] | int:
    stmt: Select

    available_data = {
        "profile_id": profile_id,
    }

    if operation == "count":
        stmt = select(func.count(distinct(Webhook.id)))
        stmt = stmt.where(
            Scope.filter_for_action(
                "webhook:read",
                "user", user_id,
                available_data,
            )
        )
    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="webhook",
            target="user",
            target_id=user_id,
            available_data=available_data
        )

        stmt = select(
            WebhookJournal.id,
            WebhookJournal.journal_uuid,
            WebhookJournal.entity,
            WebhookJournal.entity_id,
            WebhookJournal.action,
            WebhookJournal.event_created_datetime,
            WebhookJournal.event_start_datetime,
            WebhookJournal.event_end_datetime,
            WebhookJournal.json_data,
            WebhookJournal.status,
            read_allowed,
            edit_allowed
        )
        stmt = stmt.where(read_allowed.is_(True))
        stmt = stmt.where(WebhookJournal.webhook_id == webhook_id)

        if params.date_start and params.date_end:
            stmt = stmt.where(WebhookJournal.event_start_datetime.between(params.date_start, params.date_end))

    if params.search_text:
        params.search_text = params.search_text.strip()
        stmt = stmt.where(
            or_(
                WebhookJournal.id == params.search_text,
                WebhookJournal.journal_uuid == params.search_text,
            )
        )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(WebhookJournal.id.desc())

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    return sess().execute(stmt).fetchall()
