from datetime import datetime

import schemas
from db import db_func, sess
from db.models import Webhook, WebhookJournal


@db_func
def create_webhook(
    data: schemas.AdminCreateWebhookData,
    group_id: int,
    hook_id: str,
) -> Webhook:
    webhook = Webhook(
        group_id=group_id,
        hook_id=hook_id,
        **data.dict(exclude_none=True),
    )
    sess().add(webhook)
    sess().commit()

    return webhook


@db_func
def create_webhook_journal(
    entity: schemas.WebhookEntityEnum,
    entity_id: int,
    action: schemas.WebhookActionEnum,
    webhook_id: int,
    journal_uuid: str,
    event_created_datetime: datetime,
    event_start_datetime: datetime,
    event_end_datetime: datetime,
    status: schemas.WebhookJournalStatusEnum,
    json_data: schemas.WebhookJournalDataSchema,
) -> WebhookJournal:
    journal = WebhookJournal(
        entity=entity,
        entity_id=entity_id,
        action=action,
        webhook_id=webhook_id,
        journal_uuid=journal_uuid,
        event_created_datetime=event_created_datetime,
        event_start_datetime=event_start_datetime,
        event_end_datetime=event_end_datetime,
        status=status,
        json_data=json_data,
    )
    sess().add(journal)
    sess().commit()

    return journal
