import schemas
from db import db_func, sess
from db.models import WATemplate, WAMasterTemplate


@db_func
def create_wa_master_template(
    data: schemas.AdminCreateWaTemplate,
    bot_id: int,
    creator_id: int,
) -> WAMasterTemplate:
    template = WAMasterTemplate(
        bot_id=bot_id,
        creator_id=creator_id,
        name=data.name,
        description=data.description,
        category=data.category,
    )
    sess().add(template)
    sess().commit()

    return template


@db_func
def create_wa_template(
    data: schemas.AdminCreateWaTemplateSchema,
    master_template_id: int,
    lang: str,
    template_id: str,
    template_status: str,
    header_media_id: int | None = None,
) -> WATemplate:
    data_dict = data.data.dict()
    if header_media_id:
        for com in data_dict.get("components", []):
            if com.get("type") == "HEADER":
                com["media_id"] = header_media_id
                break
    template = WATemplate(
        template_id=template_id,
        template_status=template_status,
        template_data=data_dict,
        master_template_id=master_template_id,
        lang=lang,
    )
    sess().add(template)
    sess().commit()

    return template
