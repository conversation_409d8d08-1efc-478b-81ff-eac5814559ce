from sqlalchemy import case, distinct, func, select, text
from sqlalchemy.sql import Select

from db import db_func, sess
from db.models import (Scope, WAMasterTemplate)
from db.models.bot.reply_buttons import Reply<PERSON><PERSON>on


@db_func
def get_wa_master_templates(
        profile_id: int,
        bot_id: int,
        user_id: int,
        search_text: str | None = None,
        offset: int | None = None,
        limit: int | None = None,
        is_count: bool = False,
        need_check_access: bool = False,
):
    if is_count:
        stmt: Select = select(func.count(distinct(WAMasterTemplate.id)))
    else:
        stmt: Select = select(
            WAMasterTemplate.id,
            WAMasterTemplate.description,
            WAMasterTemplate.name,
            WAMasterTemplate.category,
            *Scope.allowed_scopes_list(
                "read", "edit",
                object_name="profile",  # TODO: wa_templates
                target="user",
                target_id=user_id,
                available_data={
                    "profile_id": profile_id,
                    "bot_id": bot_id,
                }
            ),
        )

        stmt = stmt.distinct()

    stmt = stmt.where(WAMasterTemplate.bot_id == bot_id)

    stmt = stmt.where(WAMasterTemplate.is_deleted.is_(False))

    if need_check_access:
        stmt = stmt.where(text("read_allowed IS TRUE"))

    if search_text:
        stmt = stmt.where(WAMasterTemplate.name.contains(search_text))

    if is_count:
        return sess().scalar(stmt)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    stmt = stmt.order_by(WAMasterTemplate.id.desc())

    return sess().execute(stmt).fetchall()


@db_func
def get_reply_message_by_message_virtual(
        value: str,
        exact_match: bool = False,
        limit: int = 20,
) -> list[ReplyButton]:
    """
    Perform fast search using the indexed `message_virtual` column.

    :param value: The value to search for.
    :param exact_match: If True, performs an exact match. If False, uses LIKE for
    partial matching.
    :param limit: Maximum number of results to return.
    :return: A list of ReplyButton records matching the search criteria.
    """
    column = ReplyButton.message_virtual

    if exact_match:
        condition = column == value
        ordering = None
    else:
        condition = column.like(f"%{value}%")

        ordering = case(
            (column == value, 1),
            (column.like(f"{value}%"), 2),
            (column.like(f"%{value}%"), 3),
            else_=4
        )

    stmt = select(ReplyButton.id, ReplyButton.content).where(condition)

    if ordering is not None:
        stmt = stmt.order_by(ordering)

    stmt = stmt.limit(limit)

    return sess().execute(stmt).all()
