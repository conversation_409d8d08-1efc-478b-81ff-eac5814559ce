from datetime import datetime

from sqlalchemy import delete, update

from db import db_func, sess
from db.models import AuthSession


@db_func
def set_auth_session_push_token(auth_session: AuthSession, push_token: str):
    sess().execute(delete(AuthSession).where(AuthSession.push_token == push_token, AuthSession.id != auth_session.id))
    auth_session.push_token = push_token
    auth_session.push_token_update_datetime = datetime.utcnow()
    sess().commit()


@db_func
def remove_push_token(push_token: str):
    sess().execute(
        update(AuthSession).values(
            {
                "push_token": None,
                "push_token_update_datetime": None,
            }
        ).where(
            AuthSession.push_token == push_token
        )
    )
    sess().commit()
