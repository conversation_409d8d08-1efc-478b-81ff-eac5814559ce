from typing import Iterable

from sqlalchemy import select

import schemas
from core.auth.statements import get_notification_settings_statement
from db import db_func, sess
from db.models import AuthSession, Scope, User


@db_func
def get_users_with_push_tokens_for_action(
        auth_sources: Iterable[schemas.AuthSourceEnum],
        action: str,
        available_data: dict,
        ignore_session_id: int | None = None,
) -> list[tuple[User, str]]:
    stmt = select(User, AuthSession.push_token)
    stmt = get_notification_settings_statement(
        stmt, available_data.get("profile_id", None), "CRM", with_auth_session=True
    )
    stmt = stmt.join(User.auth_sessions)
    stmt = stmt.where(AuthSession.is_valid)
    stmt = stmt.where(AuthSession.push_token.is_not(None))
    stmt = stmt.where(User.not_deactivated)
    if auth_sources:
        stmt = stmt.where(AuthSession.auth_source.in_(auth_sources))

    if ignore_session_id:
        stmt = stmt.where(AuthSession.id != ignore_session_id)

    stmt = stmt.where(
        Scope.filter_for_action(
            action,
            "user",
            AuthSession.user_id,
            available_data
        )
    )

    return sess().execute(stmt).fetchall()
