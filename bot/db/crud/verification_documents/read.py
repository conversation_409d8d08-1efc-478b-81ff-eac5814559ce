from sqlalchemy import func, select

from db import db_func, sess
from db.models import GroupVerificationDocument, MediaObject


@db_func
def get_verification_document_list(
        group_id: int
) -> list[
    tuple[
        GroupVerificationDocument,
        MediaObject
    ]
]:
    result = sess().execute(
        select(
            GroupVerificationDocument,
            MediaObject,
        )
        .join(GroupVerificationDocument.media)
        .where(
            GroupVerificationDocument.group_id == group_id,
        )
        .order_by(
            GroupVerificationDocument.time_created,
            GroupVerificationDocument.id,
        )
    ).fetchall()
    return result  # type: ignore


@db_func
def get_verification_document_count(group_id: int) -> int:
    return sess().scalar(
        select(func.count(GroupVerificationDocument.id))
        .where(GroupVerificationDocument.group_id == group_id)
    )
