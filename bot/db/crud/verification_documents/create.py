from typing import Iterable

from db import db_func, sess
from db.models import Group, GroupVerificationDocument, MediaObject


@db_func
def add_verification_documents(
        group: Group,
        medias: Iterable[MediaObject],
) -> list[GroupVerificationDocument]:
    documents = [
        GroupVerificationDocument(
            group=group,
            media=media
        )
        for media in medias
    ]
    sess().add_all(documents)
    sess().commit()
    return documents
