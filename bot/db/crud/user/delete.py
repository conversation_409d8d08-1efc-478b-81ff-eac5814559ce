from sqlalchemy import delete, update

from db import db_func, sess
from db.models import AuthSession, ClientBot, Group, IncustCustomer, User


@db_func
def delete_user_account(user: User, is_need_delete_incust_customer: bool | None = False):
    disable_groups_statement = (
        update(Group)
        .values({"status": "disabled"})
        .where(Group.owner_id == user.id)
        .execution_options(synchronize_session="fetch")
    )
    sess().execute(disable_groups_statement)

    disable_bots_statement = (
        update(ClientBot)
        .values({"status": "disabled"})
        .where(ClientBot.group.has(owner_id=user.id))
        .execution_options(synchronize_session="fetch")
    )
    sess().execute(disable_bots_statement)

    delete_auth_sessions = (
        delete(AuthSession)
        .where(AuthSession.user_id == user.id)
        .execution_options(synchronize_session="fetch")
    )
    sess().execute(delete_auth_sessions)

    # Видалення запису з IncustCustomer, якщо потрібно
    if is_need_delete_incust_customer:
        delete_incust_customer_statement = (
            delete(IncustCustomer)
            .where(IncustCustomer.user_id == user.id)
            .execution_options(synchronize_session="fetch")
        )
        sess().execute(delete_incust_customer_statement)

    user.chat_id = None
    user.wa_phone = None
    user.wa_name = None
    user.username = None
    user.first_name = f"Deleted account #{user.id}"
    user.last_name = None
    user.full_name = None
    user.photo = None
    user.telegram_photo_id = None
    user.email = None
    user.hashed_password = None
    user.is_confirmed_email = False
    user.db_timezone = None
    user.lang = None
    user.is_auth_google = False
    user.is_auth_apple = False
    user.is_superadmin = False
    user.status = "deactivated"

    sess().commit()
