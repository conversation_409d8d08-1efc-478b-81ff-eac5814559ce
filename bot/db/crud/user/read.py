from typing import Iterable

from sqlalchemy import desc, func, or_, select, union
from sqlalchemy.orm import aliased
from sqlalchemy.sql import Select

import schemas
from db import db_func, sess
from db.models import Scope, User


@db_func
def get_profile_users_list(
        profile_id: int,
        params: schemas.AdminGetUsersListParams,
        exclude_scopes: Iterable[str] | None = None,
        for_add: bool = False,
        current_user_id: int | None = None,
        pin_users_to_top: Iterable[int] | None = None,
) -> list[tuple[User, str | None]]:
    aliased_user = aliased(User)
    aliased_scope = aliased(Scope)

    users_with_access_stmt: Select = select(
        aliased_user.id.label("user_id"),
        aliased_scope.id.label("scope_id"),
        aliased_scope.scope.label("scope_name"),
        aliased_scope.time_created.label("scope_time_created"),
    ).distinct()
    users_with_access_stmt = users_with_access_stmt.join(
        aliased_scope, aliased_scope.user_id == aliased_user.id
    )

    users_with_access_stmt = users_with_access_stmt.where(aliased_user.not_deactivated)

    users_with_access_stmt = users_with_access_stmt.where(
        aliased_scope.target == "user"
    )
    users_with_access_stmt = users_with_access_stmt.where(
        aliased_scope.profile_id == profile_id
    )
    if exclude_scopes:
        users_with_access_stmt = users_with_access_stmt.where(
            or_(
                aliased_user.id == current_user_id,
                aliased_scope.scope.not_in(exclude_scopes)
            )
        )

    system_user = aliased(User)
    system_user_scope = aliased(Scope)

    own_system_users_stmt: Select = select(
        system_user.id.label("user_id"),
        system_user_scope.id.label("scope_id"),
        system_user_scope.scope.label("scope_name"),
        system_user_scope.time_created.label("scope_time_created"),
    ).distinct()

    own_system_users_stmt = own_system_users_stmt.outerjoin(
        system_user_scope, system_user_scope.user_id == system_user.id
    )
    own_system_users_stmt = own_system_users_stmt.where(
        system_user_scope.target == "user"
    )
    own_system_users_stmt = own_system_users_stmt.where(
        system_user_scope.profile_id == profile_id
    )
    if exclude_scopes:
        users_with_access_stmt = users_with_access_stmt.where(
            system_user_scope.scope.not_in(exclude_scopes)
        )

    own_system_users_stmt = own_system_users_stmt.where(
        system_user.is_system_user.is_(True)
    )
    own_system_users_stmt = own_system_users_stmt.where(system_user.not_deactivated)
    own_system_users_stmt = own_system_users_stmt.where(
        system_user.system_user_owned_by_profile_id == profile_id
    )

    users_stmt = union(users_with_access_stmt, own_system_users_stmt).subquery()

    stmt: Select = select(
        User,
        func.group_concat(
            users_stmt.c.scope_name.distinct().op("ORDER BY")(
                users_stmt.c.scope_id
            )
        ).label(
            "scope_names"
        )
    )

    if for_add:
        stmt = stmt.outerjoin(users_stmt, users_stmt.c.user_id == User.id)
    else:
        stmt = stmt.join(users_stmt, users_stmt.c.user_id == User.id)

    stmt = stmt.where(
        or_(
            User.is_system_user.is_(False),
            User.system_user_owned_by_profile_id == profile_id,
            users_stmt.c.scope_id.is_not(None),
        )
    )

    if params.search_text:
        stmt = stmt.where(User.search(params.search_text))

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    stmt = stmt.group_by(User.id)

    if pin_users_to_top:
        stmt = stmt.order_by(User.id.not_in(pin_users_to_top))

    if not for_add:
        stmt = stmt.order_by(users_stmt.c.scope_time_created.is_not(None))
        stmt = stmt.order_by(desc(func.min(users_stmt.c.scope_time_created)))

    stmt = stmt.order_by(User.date_joined.desc())

    return sess().execute(stmt).fetchall()


@db_func
def get_platform_users_list(
        params: schemas.AdminGetUsersListParams,
        exclude_scopes: Iterable[str] | None = None,
        for_add: bool = False,
        pin_users_to_top: Iterable[int] | None = None,
) -> list[User]:
    aliased_user = aliased(User)
    aliased_scope = aliased(Scope)

    users_with_access_stmt = select(
        aliased_user.id.label("user_id"),
        aliased_scope.scope.label("scope_name"),
        aliased_scope.id.label("scope_id")
    ).distinct()
    users_with_access_stmt = users_with_access_stmt.join(
        aliased_scope, aliased_scope.user_id == aliased_user.id
    )
    users_with_access_stmt = users_with_access_stmt.where(aliased_user.not_deactivated)
    users_with_access_stmt = users_with_access_stmt.where(
        aliased_scope.target == "user"
    )

    if exclude_scopes:
        excluded_users_stmt = select(aliased_user.id).join(
            aliased_scope, aliased_scope.user_id == aliased_user.id
        ).where(aliased_scope.scope.in_(exclude_scopes)).distinct()
    else:
        excluded_users_stmt = select().where(False)

    stmt = select(
        User,
        func.group_concat(
            users_with_access_stmt.c.scope_name.distinct().op("ORDER BY")(
                users_with_access_stmt.c.scope_id
            )
        ).label("scope_names")
    )

    users_with_access_subquery = users_with_access_stmt.alias()

    if for_add:
        stmt = stmt.outerjoin(
            users_with_access_subquery,
            users_with_access_subquery.c.user_id == User.id
        )
    else:
        stmt = stmt.join(
            users_with_access_subquery,
            users_with_access_subquery.c.user_id == User.id
        )

    stmt = stmt.where(User.is_system_user.is_(False))

    if params.search_text:
        stmt = stmt.where(User.search(params.search_text))

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    stmt = stmt.group_by(User.id)

    if pin_users_to_top:
        stmt = stmt.order_by(User.id.not_in(pin_users_to_top))

    stmt = stmt.order_by(User.date_joined.desc())

    stmt = stmt.where(User.id.not_in(excluded_users_stmt))

    result = sess().execute(stmt).fetchall()

    return [user for user, _ in result]
