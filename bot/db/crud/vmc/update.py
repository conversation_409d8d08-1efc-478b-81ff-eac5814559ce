from datetime import datetime, timedelta

from sqlalchemy import exists, not_, select

from db import db_func, sess
from db.crud.vmc.delete import execute_delete_currently_running_vm_chats
from db.models import (
    UserClientBotActivity, VirtualManager, VirtualManagerChat,
    VirtualManagerStep,
)
from schemas import VMStepReminderMode
from utils.date_time import utcnow


@db_func
def vmc_answered(
        vmc: VirtualManagerChat,
        vm: VirtualManager,
        current_step: VirtualManagerStep,
        user_bot_activity: UserClientBotActivity,
        go_to_step_id: int | None = None,
        stop_vm: bool = False,
        pause_vm: bool = False,
        is_answer_for_remind: bool = False,
        send_next_message_delay: float | None = None,
        started_vmc_id: int | None = None
):
    set_step_id = current_step.id

    if not stop_vm and not pause_vm:
        if not go_to_step_id and (
                not vmc.last_sent_message_datetime or
                not vmc.last_answer_datetime or
                vmc.last_answer_datetime < vmc.last_sent_message_datetime
        ):
            go_to_step_id = sess().scalar(
                select(
                    VirtualManagerStep.id
                ).where(
                    VirtualManagerStep.virtual_manager_id == vm.id,
                    VirtualManagerStep.position > current_step.position,
                    VirtualManagerStep.is_deleted.is_(False),
                ).order_by(
                    VirtualManagerStep.position,
                    VirtualManagerStep.id,
                ).limit(1)
            )

        if go_to_step_id:
            set_step_id = go_to_step_id

            if send_next_message_delay is None:
                send_next_message_delay = vm.message_delay
            vmc.when_send_message = datetime.utcnow() + timedelta(
                seconds=send_next_message_delay
            )

    exclude_vmc_delete = []
    if started_vmc_id:
        exclude_vmc_delete.append(started_vmc_id)
    else:
        exclude_vmc_delete.append(vmc.id)

        vmc.current_step_id = set_step_id
        vmc.is_deleted = False

    execute_delete_currently_running_vm_chats(
        vmc.user_id, vmc.bot_id,
        exclude=exclude_vmc_delete
    )

    vmc.is_last_answer_for_remind = is_answer_for_remind
    vmc.continue_to_question = None
    vmc.last_answer_datetime = utcnow()

    if not is_answer_for_remind:
        user_bot_activity.remind_about_vm_chat = None
        user_bot_activity.vm_when_remind = None
        user_bot_activity.vm_reminded_count = 0

    sess().commit()

    return vmc


@db_func
def vmc_message_sent(
        vmc: VirtualManagerChat,
        vm: VirtualManager,
        step: VirtualManagerStep,
        user_bot_activity: UserClientBotActivity,
):
    vmc.current_step = step
    vmc.last_sent_message_datetime = datetime.utcnow()
    vmc.continue_to_step_id = None
    vmc.when_send_message = None

    is_last_step = sess().scalar(
        select(
            not_(
                exists().where(
                    VirtualManagerStep.virtual_manager_id == vmc.virtual_manager_id,
                    VirtualManagerStep.position > step.position,
                    VirtualManagerStep.is_deleted.is_(False),
                )
            )
        )
    )

    if is_last_step:
        vmc.is_deleted = True

    reminder_delay = step.reminder_delay or vm.reminder_delay
    reminds_count = step.reminds_count or vm.reminds_count

    reminded_count = user_bot_activity.vm_reminded_count
    if (user_bot_activity.remind_about_vm_chat_id == vmc.id and not
    vmc.is_last_answer_for_remind):
        reminded_count += 1
    else:
        reminded_count = 0

    if not is_last_step and step.reminder_mode == VMStepReminderMode.REMIND or (
            step.reminder_mode == VMStepReminderMode.DEFAULT and
            vm.is_reminder_enabled and reminder_delay and reminds_count and (
                    not vmc.is_last_answer_for_remind or
                    reminded_count < reminds_count
            )
    ):
        if vmc.is_last_answer_for_remind:
            vmc.is_last_answer_for_remind = False
        user_bot_activity.vm_reminded_count = reminded_count
        reminder_delay = reminder_delay * 2 ** reminded_count
        vmc.is_last_answer_for_remind = False

        user_bot_activity.remind_about_vm_chat = vmc
        user_bot_activity.vm_when_remind = datetime.utcnow() + timedelta(
            seconds=reminder_delay
        )

    else:
        user_bot_activity.remind_about_vm_chat = None
        user_bot_activity.vm_when_remind = None
        user_bot_activity.vm_reminded_count = 0

    sess().commit()


@db_func
def vmc_set_continue(
        vmc: VirtualManagerChat, continue_delay: float, continue_to_step_id: int
):
    vmc.when_send_message = datetime.utcnow() + timedelta(seconds=continue_delay)
    vmc.continue_to_step_id = continue_to_step_id
    sess().commit()
