from typing import Sequence

from sqlalchemy import update

from config import DEBUG
from db import db_func, sess
from db.models import VirtualManagerChat
from loggers import JSONLogger


def execute_delete_currently_running_vm_chats(
        user_id: int, bot_id: int,
        exclude: Sequence[int] | None = None
):
    stmt = (
        update(VirtualManagerChat).where(
            VirtualManagerChat.user_id == user_id,
            VirtualManagerChat.bot_id == bot_id,
            VirtualManagerChat.is_deleted.is_(False)
        )
    )

    if exclude:
        stmt = stmt.where(VirtualManagerChat.id.not_in(exclude))

    return sess().execute(
        stmt.values({"is_deleted": True})
        .execution_options(synchronize_session='fetch')
    )


@db_func
def delete_currently_running_vm_chats(user_id: int, bot_id: int):
    logger = JSONLogger(
        "vm", {
            "user_id": user_id,
            "bot_id": bot_id,
        }
    )

    execute_delete_currently_running_vm_chats(user_id, bot_id)
    sess().commit()

    if DEBUG:
        logger.debug(f"Deleted currently running chats")
    return True
