from sqlalchemy import select

import exceptions
from db import db_func, sess
from db.models import (
    ClientBot, Group, MediaObject, User, VirtualManager, VirtualManagerChat,
    VirtualManagerInteractive, VirtualManagerStep,
)
from loggers import J<PERSON>NLogger


@db_func
def get_active_vmc(
        user_id: int, bot_id: int, group_id: int | None = None
) -> VirtualManagerChat | None:
    stmt = select(VirtualManagerChat)
    stmt = stmt.where(
        VirtualManagerChat.user_id == user_id,
        VirtualManagerChat.bot_id == bot_id,
        VirtualManagerChat.is_deleted.is_(False)
    )
    if group_id:
        stmt = stmt.where(VirtualManagerChat.group_id == group_id)
    stmt = stmt.order_by(VirtualManagerChat.last_sent_message_datetime.desc())
    stmt = stmt.limit(1)
    return sess().scalar(stmt)


@db_func
def get_vmc_data_for_send_message(vmc: VirtualManagerChat) -> tuple[
    VirtualManager,
    User,
    Group,
    ClientBot,
    VirtualManagerStep | None,
    MediaObject | None
]:
    vm = VirtualManager.get_sync(vmc.virtual_manager_id, is_deleted=False)
    user = User.get_by_id_sync(vmc.user_id, status="a")
    group = Group.get_sync(vmc.group_id, status="enabled")
    bot = ClientBot.get_sync(vmc.bot_id, status="enabled")

    step = VirtualManagerStep.get_sync(
        vmc.continue_to_step_id or vmc.current_step_id, is_deleted=False
    )
    step_media = MediaObject.get_sync(step.media_id) if step and step.media_id else None

    return vm, user, group, bot, step, step_media


@db_func
def get_vmc_data_for_process_answer(
        vmc_or_id: VirtualManagerChat | int,
        logger: JSONLogger,
        interactive_id: int | None = None,
        step_id: int | None = None
) -> tuple[
    VirtualManagerChat,
    VirtualManager,
    VirtualManagerStep,
    VirtualManagerInteractive | None,
    Group
]:
    db_interactive = VirtualManagerInteractive.get_sync(
        interactive_id, is_deleted=False
    ) if interactive_id else None
    if interactive_id and not db_interactive:
        logger.error("VM interactive not found")
        raise exceptions.VirtualManagerButtonNotFoundError(interactive_id)

    vmc = VirtualManagerChat.get_sync(vmc_or_id)
    if not vmc:
        logger.error("VM Chat not found")
        raise exceptions.VirtualManagerChatNotFoundError(vmc)

    vm = VirtualManager.get_sync(vmc.virtual_manager_id, is_deleted=False)

    if not step_id:
        step_id = db_interactive.step_id if db_interactive else vmc.current_step_id
    current_step = VirtualManagerStep.get_sync(step_id, is_deleted=False)
    if not current_step:
        vmc.is_deleted = True
        sess().commit()

        logger.error("VM Step not found")
        raise exceptions.VirtualManagerStepNotFoundError(
            VirtualManagerChat.current_step_id
        )

    group = Group.get_sync(vmc.group_id, status="enabled")

    return vmc, vm, current_step, db_interactive, group
