from datetime import datetime, timed<PERSON>ta

from sqlalchemy import select

import exceptions
from config import DEBUG
from db import db_func, sess
from db.crud.vmc.delete import execute_delete_currently_running_vm_chats
from db.models import (
    ClientBot, Group, User, VirtualManager, VirtualManagerChat,
    VirtualManagerStep,
)
from loggers import J<PERSON>NLogger


@db_func
def start_virtual_manager_chat(
        user: User,
        vm: VirtualManager,
        group_or_id: Group | int | None = None,
        bot_or_id: ClientBot | int | None = None,
        on_start_delay: float | None = None,
        step: VirtualManagerStep | int | None = None,
):
    logger = JSONLogger(
        "vm", {
            "vm": vm,
            "user_id": user.id,
            "params_group": group_or_id,
            "params_bot": bot_or_id,
        }
    )

    bot = ClientBot.get_sync(bot_or_id)
    if not bot:
        logger.error("Bot not found")
        raise exceptions.BotNotFoundError(bot_or_id)

    if vm.start_only_in_owner_profile:
        if not isinstance(group_or_id, Group) or group_or_id.id != vm.group_id:
            group_or_id = vm.group_id
    elif not group_or_id:
        group_or_id = Group.get_sync(bot.group_id)

    logger.add_data({"vm": vm})

    group = Group.get_sync(group_or_id)
    if not group:
        logger.error("Group not found")
        raise exceptions.ProfileNotFoundError(group_or_id)

    logger.add_data({"group": group, "bot": bot})

    execute_delete_currently_running_vm_chats(user.id, bot.id)

    if DEBUG:
        logger.debug(f"Deleted currently running chats")

    if isinstance(step, VirtualManagerStep):
        if step.virtual_manager_id != vm.id:
            step = None
        step_id = step.id
    elif step:
        step_id = step
        step = VirtualManagerStep.get_sync(
            step, virtual_manager_id=vm.id, is_deleted=False
        )
    else:
        step_id = "first"
        step = sess().scalar(
            select(
                VirtualManagerStep,
            ).where(
                VirtualManagerStep.virtual_manager_id == vm.id,
                VirtualManagerStep.is_deleted.is_(False),
            ).order_by(VirtualManagerStep.position).limit(1)
        )

    if not step:
        raise exceptions.VirtualManagerStepNotFoundError(step_id)

    on_start_delay = (
        timedelta(seconds=on_start_delay)
        if on_start_delay else
        timedelta(seconds=vm.on_start_delay)
    )
    when_send_message = datetime.utcnow() + on_start_delay

    create_params = dict(
        user=user,
        bot=bot,
        group=group,
        virtual_manager=vm,
        when_send_message=when_send_message,
        current_step=step,
    )

    logger.add_data(
        {
            "create_params": create_params
        }
    )

    try:
        vmc = VirtualManagerChat(**create_params)
        sess().add(vmc)
        sess().commit()
    except Exception as e:
        logger.error("Start virtual manager chat FAILED", e)
        raise exceptions.StartVirtualManagerChatFailedError() from e
    else:
        logger.debug(
            "Virtual manager chat successfully started!",
            {"started_vmc_id": vmc.id, "is_deleted_vmc": vmc.is_deleted}
        )
    return vmc
