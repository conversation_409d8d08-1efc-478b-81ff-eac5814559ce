from pytz import country_timezones

from db import db_func, sess
from db.models import ClientBot, Group, GroupTag, Scope, User


@db_func
def create_group_tag(tag_name: str, *groups: Group) -> GroupTag:
    group_tag = GroupTag.get_sync(tag_name=tag_name, for_update=True)

    if not group_tag:
        group_tag = GroupTag(
            tag_name=tag_name,
            groups=list(groups),
        )
        sess().add(group_tag)
    else:
        group_tag.groups.extend(groups)

    sess().commit()
    return group_tag


def create_group_sync(
        name: str,
        owner: User,
        from_bot: ClientBot = None,
        is_accepted_agreement: bool = False,
        country_iso_code: str | None = None,
        timezone: str | None = None,
        lang: str | None = None,
        currency: str | None = None, *,
        no_commit: bool = False,

) -> Group:
    if not timezone:
        timezones = country_timezones.get(
            country_iso_code
        ) if country_iso_code else None

        if timezones and len(timezones) == 1:
            timezone = timezones[0]

    group = Group(
        owner, name,
        from_bot,
        is_accepted_agreement,
        country_iso_code, timezone,
        lang,
        currency,
    )
    sess().add(group)

    scope = Scope(
        target="user",
        user=owner,
        scope="profile:admin",
        profile=group,
    )
    sess().add(scope)

    Scope.create_sync(
        scope="billing:tester",
        target="profile",
        profile=group,
    )

    if not no_commit:
        sess().commit()
    return group


@db_func
def create_group(
        name: str,
        owner: User,
        from_bot: ClientBot = None,
        is_accepted_agreement: bool = False,
        country_iso_code: str | None = None,
        timezone: str | None = None,
        lang: str | None = None,
        currency: str | None = None,
) -> Group:
    return create_group_sync(
        name, owner, from_bot, is_accepted_agreement, country_iso_code, timezone, lang,
        currency
    )
