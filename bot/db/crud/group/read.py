import re
from sqlalchemy import and_, distinct, func, or_, select
from sqlalchemy.orm import Query, aliased
from sqlalchemy.sql import Select, label
from starlette import status
from typing import Type

from core.ext.types import ExternalAPIType
from db import db_func, sess
from db.crud.scope.read import check_access_to_action_sync
from db.decorators import process_query_with_operation
from db.helpers import get_query_by_operation, order_by_slice_and_result
from db.models import (
    Brand, BrandCustomSettings, ClientBot, Group, GroupTag, MediaObject,
    PaymentSettings,
    Scope, Store,
    StoreCategory,
    StoreProduct, User,
)
from db.models.qr_media_object.qr_media_object import (
    ProfileToQrMediaObject,
    QrMediaObject,
)
from schemas import GroupConfig
from schemas.group.group import (
    ConfigImportGetOrderSettings, ConfigImportIncustSettings,
    ConfigImportPosterSettings,
    ConfigImportPromSettings, ConfigImportSheetsSettings,
)
from utils.exceptions import ErrorWithHTTPStatus


# noinspection PyUnusedLocal
# because parameters needed in decorator
@db_func
@process_query_with_operation(Group, Group.time_created.desc())
def get_user_own_groups(
        query: Query,
        user_id: int,
        position: int | None = None,
        limit: int | None = None,
        search_text: str | None = None,
        operation: str = "all",
) -> list[Group] | int | bool:
    query = query.filter(Group.owner_id == user_id)
    if search_text:
        query = query.filter(Group.search(search_text))
    return query


@db_func
def get_admin_profiles_list(
        user_id: int,
        offset: int | None = None,
        limit: int = None,
        search_text: str | None = None,
        is_count: bool = False,
        is_superadmin: bool | None = None,
) -> list[tuple[int, str]] | int:
    if is_superadmin is None:
        is_superadmin = check_access_to_action_sync(
            "platform:admin", "user", user_id,
        )

    stmt: Select
    if is_count:
        stmt = select(func.count(Group.id.distinct()))
    else:
        stmt = select(
            Group.id,
            Group.name,
            Brand.domain,
            label("logo_url", MediaObject.url)
        )

    stmt = stmt.outerjoin(Group.brand)
    if not is_count:
        stmt = stmt.outerjoin(Brand.logo_media)

    stmt = stmt.where(Group.status == "enabled")
    if not is_superadmin:
        stmt = stmt.where(
            Scope.filter_by_target(
                (
                    Scope.profile_id == Group.id,
                ),
                "user", user_id,
            )
        )

    if search_text and search_text.strip():
        search_text = search_text.strip()

        stmt = stmt.outerjoin(
            ClientBot,
            ClientBot.group_id == Group.id
        )

        if search_text.startswith("@"):
            username_search = search_text[1:]
        elif username_search_math := re.fullmatch(
                r"(?:https?://)?t.me/([0-9a-z_]+)(?:[/?].*)?",
                search_text,
                re.IGNORECASE,
        ):
            username_search = username_search_math.group(1)
        else:
            username_search = search_text

        base_conditions = (
            Group.search(search_text),
            Brand.domain.contains(search_text),
            ClientBot.username == username_search,
            ClientBot.whatsapp_from_phone_number == "".join(
                re.findall(r"\d", search_text)
            ),
            ClientBot.whatsapp_app_name == search_text,
        )

        if is_superadmin:

            owner: Type[User] = aliased(User)
            stmt = stmt.join(
                owner,
                Group.owner_id == owner.id
            )

            stmt = stmt.where(
                or_(
                    *base_conditions,
                    owner.search(search_text),
                    Group.owner_id == search_text,
                )
            )
        else:
            stmt = stmt.where(or_(*base_conditions))

    if is_count:
        return sess().scalar(stmt) or 0

    stmt = stmt.order_by(Group.time_created.desc())

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    return sess().execute(stmt).fetchall()


@db_func
def get_groups_names_by_ids(groups_ids: list[int]) -> list[str]:
    query = sess().query(Group.id, Group.name)
    query = query.filter(Group.id.in_(groups_ids))
    ids_names: dict[int, str] = dict(query.all())  # type: ignore

    return [ids_names.get(id) for id in groups_ids if id in ids_names]


@db_func
def get_group_by_store(store_id: int) -> Group | None:
    query = sess().query(Group)
    query = query.filter(Group.status == "enabled")
    query = query.filter(Group.brand.has(Brand.stores.any(id=store_id)))
    return query.one_or_none()


@db_func
def get_group_by_api_token(api_token: str) -> Group:
    query = sess().query(Group)
    query = query.filter(Group.api_token == api_token)
    return query.one()


@db_func
def get_group_tags(
        group_id: int,
        position: int = 0,
        limit: int | None = None,
        operation: str = "all"
) -> list[GroupTag] | int:
    query = get_query_by_operation(GroupTag, operation)
    query = query.filter(GroupTag.groups.any(id=group_id))
    return order_by_slice_and_result(
        query, position, limit, GroupTag.time_created.desc(), operation
    )


@db_func
def get_accessed_groups(
        user_id: int,
        position: int | None = None,
        limit: int | None = None,
        search_text: str | None = None,
        operation: str = "all",
) -> list[Group]:
    query = get_query_by_operation(Group, operation)

    query = query.filter(
        or_(
            Group.admins.any(user_id=user_id),
            Group.owner.has(id=user_id),
        )
    )
    query = query.filter(Group.status == "enabled")

    if search_text:
        query = query.filter(Group.name.contains(search_text))

    query = order_by_slice_and_result(
        query, position, limit, Group.time_created.desc(), operation
    )

    return query


@db_func
def get_crm_profiles_list(
        user_id: int,
        offset: int | None = None,
        limit: int = None,
        search_text: str | None = None,
        is_count: bool = False,
) -> list[tuple[int, str]] | int:
    stmt: Select
    if is_count:
        stmt = select(func.count(Group.id.distinct()))
    else:
        stmt = select(
            Group.id,
            Group.name,
            label("logo_url", MediaObject.url)
        ).distinct()

    stmt = stmt.outerjoin(Group.brand)
    stmt = stmt.outerjoin(Brand.logo_media)

    stmt = stmt.outerjoin(Brand.stores)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(
        Scope.filter_for_action(
            "crm_all:read",
            "user", user_id,
            available_data={
                "profile_id": Group.id,
                "store_id": Store.id,
            }
        )
    )

    if search_text:
        stmt = stmt.where(Group.search(search_text))

    if is_count:
        return sess().scalar(stmt) or 0

    stmt = stmt.order_by(Group.time_created.desc())

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    return sess().execute(stmt).fetchall()


@db_func
def get_poster_setting(brand_id: int, setting_name: str) -> str | None:
    query = select(Group).join(Brand).where(Brand.id == brand_id)
    result = sess().execute(query)
    group = result.scalar_one_or_none()

    if group and group.config:
        config: GroupConfig = group.config
        if config.import_config and config.import_config.poster_settings:
            return getattr(config.import_config.poster_settings, setting_name, None)
    return None


class PosterSettingsNotFoundError(ErrorWithHTTPStatus):
    status_code = status.HTTP_404_NOT_FOUND
    text_variable = "poster settings not found error"

    def __init__(self, brand_id: int):
        super().__init__(
            brand_id=brand_id,
            detail_data={
                "error_code": "poster_settings_not_found",
                "brand_id": brand_id,
            }
        )


@db_func
def get_poster_settings(brand_id: int, ) -> ConfigImportPosterSettings | None:
    query = select(Group).join(Brand).where(Brand.id == brand_id)
    result = sess().execute(query)
    group = result.scalar_one_or_none()

    if group and group.config:
        config: GroupConfig = group.config
        if config.import_config and config.import_config.poster_settings:
            return config.import_config.poster_settings

    raise PosterSettingsNotFoundError(brand_id)


@db_func
def get_group_import_setting(
        brand_id: int, import_source: ExternalAPIType, setting_name: str
) -> str | None:
    query = select(Group).join(Brand).where(Brand.id == brand_id)
    result = sess().execute(query)
    group: Group = result.scalar_one_or_none()

    if group and group.config:
        config: GroupConfig = group.config
        if config.import_config:
            settings_attr = f"{import_source}_settings"
            if hasattr(config.import_config, settings_attr):
                settings = getattr(config.import_config, settings_attr)
                return getattr(settings, setting_name, None)
    return None


@db_func
def get_group_import_settings(brand_id: int, import_source: ExternalAPIType) -> (
        ConfigImportPosterSettings |
        ConfigImportIncustSettings |
        ConfigImportGetOrderSettings |
        ConfigImportPromSettings |
        ConfigImportSheetsSettings |
        None
):
    query = select(Group).join(Brand).where(Brand.id == brand_id)
    result = sess().execute(query)
    group: Group = result.scalar_one_or_none()

    if group and group.config:
        config: GroupConfig = group.config
        if config.import_config:
            settings_attr = f"{import_source}_settings"
            if hasattr(config.import_config, settings_attr):
                return getattr(config.import_config, settings_attr)
    return None


@db_func
def get_group_config(group_id: int) -> GroupConfig | None:
    query = select(Group.config).where(Group.id == group_id)
    result = sess().execute(query)
    group_config: GroupConfig = result.scalar_one_or_none()

    return group_config


@db_func
def get_user_owned_profiles(user_id: int) -> list[tuple[Group, MediaObject | None]]:
    # noinspection PyTypeChecker
    return sess().execute(
        select(
            Group,
            MediaObject,
        )
        .outerjoin(Group.brand)
        .outerjoin(Brand.logo_media)
        .where(
            Group.owner_id == user_id,
            Group.status == "enabled"
        )
        .order_by(
            Group.time_created.desc(),
            Group.id.desc()
        )
    ).fetchall()


@db_func
def get_group_by_brand_id(brand_id: int) -> Group | None:
    stmt = (
        select(Group)
        .join(Brand)
        .where(Group.status == "enabled", Brand.id == brand_id)
    )
    result = sess().scalars(stmt).one_or_none()
    return result


@db_func
def get_profile_completion(group_id: int | None, brand_id: int | None) -> dict[
    str, bool]:
    entities = {
        "Store": Store,
        "StoreCategory": StoreCategory,
        "StoreProduct": StoreProduct,
        "PaymentSettings": PaymentSettings,
        "BrandCustomSettings": BrandCustomSettings,
    }

    result = {}

    for key, model in entities.items():
        conditions = []
        if group_id is not None and hasattr(model, "group_id"):
            conditions.append(model.group_id == group_id)
        if group_id is not None and hasattr(model, "profile_id"):
            conditions.append(model.profile_id == group_id)
        if brand_id is not None and hasattr(model, "brand_id"):
            conditions.append(model.brand_id == brand_id)
        if hasattr(model, "is_deleted"):
            conditions.append(model.is_deleted == False)
        if hasattr(model, 'payment_method'):
            conditions.append(model.payment_method != 'cash')

        if not conditions:
            result[key] = False
            continue

        stmt = select(model.id).where(and_(*conditions)).limit(1)
        exists = sess().execute(stmt).scalar() is not None
        result[key] = exists

    if group_id is not None:
        subquery = select(QrMediaObject.id).join(
            ProfileToQrMediaObject,
            ProfileToQrMediaObject.qr_media_object_id == QrMediaObject.id
        ).where(
            ProfileToQrMediaObject.profile_id == group_id,
            QrMediaObject.target == "fastpay"
        ).limit(1)

        fastpay_exists = sess().execute(subquery).scalar() is not None
        result["QrMediaObject"] = fastpay_exists

    return result


@db_func
def get_locations(
        search_text: str = None,
        position: int = 0,
        limit: int = None,
        operation: str = "all",
):
    if operation == "count":
        query = sess().query(func.count(distinct(Group.location)))
    else:
        query = sess().query(Group.location).distinct()

    query = query.filter(Group.status == "enabled")

    if search_text:
        query = query.filter(Group.location.contains(search_text))

    if operation == "count":
        return query.scalar() - position

    slice_args = [position, None]
    if limit:
        slice_args[1] = position + limit
    query = query.slice(*slice_args)

    return sess().scalars(query).all()
