from db import db_func, sess
from ..scope.read import check_access_to_action_sync
from .admins.create import add_admin_sync
from db.models import Group, Scope, User


def change_group_owner_sync(
        group: Group,
        new_owner: User,
        no_commit: bool = False,
) -> User:
    add_admin_sync(group.owner, group, no_commit=True)
    group.owner = new_owner
    if not check_access_to_action_sync(
            "profile:admin",
            "user", new_owner.id,
            available_data={
                "profile_id": group.id,
            }
    ):
        sess().add(
            Scope(
                target="user",
                user=new_owner,
                scope="profile:admin",
                profile=group,
            )
        )
    if not no_commit:
        sess().commit()
    return group.owner


@db_func
def change_group_owner(
        group: Group,
        new_owner: User,
        no_commit: bool = False,
) -> User:
    return change_group_owner_sync(group, new_owner, no_commit)
