from sqlalchemy import or_
from typing import Literal

from db import db_func, sess
from db.models import Admin, User, Group
from db.helpers import get_query_by_operation, order_by_slice_and_result


@db_func
def get_admins_list(group_id: int) -> list[Admin]:
    query = sess().query(Admin)
    query = query.filter(Admin.group_id == group_id)
    return query.all()


@db_func
def get_admin_users_list(group_id: int) -> list[User]:
    query = sess().query(User).distinct()
    query = query.filter(or_(
        User.own_groups.any(id=group_id),
        User.admin_roles.any(group_id=group_id)
    ))
    return query.all()


@db_func
def check_is_user_group_admin(group_id: int, user_id: int) -> bool:
    query = sess().query(Admin)
    query = query.filter(Admin.group_id == group_id)
    query = query.filter(Admin.user_id == user_id)
    return sess().query(query.exists()).scalar()


@db_func
def get_groups_admin(
        admin_user_id: int,
        operation: Literal["all", "count"] = "all",
) -> list[Group] | int:
    query = get_query_by_operation(Group, operation)

    query = query.join(Admin, Admin.group_id == Group.id)

    query = query.filter(Admin.user_id == admin_user_id)
    query = query.filter(Group.status == "enabled")

    return order_by_slice_and_result(query, order_by=Group.name, operation=operation)
