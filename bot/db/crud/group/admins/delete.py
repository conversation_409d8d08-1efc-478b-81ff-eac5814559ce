from sqlalchemy import delete

from db import db_func, sess
from db.models import Ad<PERSON>, <PERSON><PERSON>


@db_func
def delete_admin(group_id: int, user_id: int):
    sess().execute(delete(Admin).where(Admin.group_id == group_id, Admin.user_id == user_id))
    sess().execute(
        delete(Scope).where(
            Scope.scope == "profile:edit", Scope.target == "user", Scope.user_id == user_id,
            Scope.profile_id == group_id
        )
    )
    sess().commit()
