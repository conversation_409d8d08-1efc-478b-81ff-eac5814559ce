from dataclasses import dataclass
from typing import Literal

from db.models import Ad, AdUnit, MediaObject


@dataclass
class AdUnitData:
    unit: AdUnit
    horizontal_video: MediaObject | None
    vertical_video: MediaObject | None

    def get_video_url(self, key: Literal["horizontal", "vertical"]):
        video: MediaObject | None = getattr(self, f"{key}_video", None)
        if video:
            return video.url
        return None


@dataclass
class AdData:
    ad: Ad
    units: list[AdUnitData]
