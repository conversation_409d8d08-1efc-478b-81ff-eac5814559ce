import schemas
from db import db_func, sess
from db.models import Ad, AdUnit
from .data import AdData, AdUnitData
from .helpers import get_media_objects


def create_ad_sync(
        data: schemas.AdMutationData,
        commit: bool = True
) -> AdData:
    ad = Ad(**data.dict(exclude={"units"}))

    media_objects = get_media_objects(data)

    units = []
    units_data = []

    for position, unit_data in enumerate(data.units):
        horizontal_video = media_objects.get(unit_data.horizontal_video_id)
        vertical_video = media_objects.get(unit_data.vertical_video_id)

        unit = AdUnit(
            **unit_data.dict(exclude={"id"}),
            position=position,
            horizontal_video=horizontal_video,
            vertical_video=vertical_video,
            ad=ad
        )
        units.append(unit)

        units_data.append(
            AdUnitData(
                unit,
                horizontal_video,
                vertical_video
            ),
        )

    sess().add_all([ad, *units])
    if commit:
        sess().commit()
    return AdData(ad, units_data)


@db_func
def create_ad(
        data: schemas.AdMutationData,
        commit: bool = True
) -> AdData:
    return create_ad_sync(data, commit)
