from sqlalchemy import delete, exists, select

import exceptions
import schemas
from db import db_func, sess
from db.crud.helpers import check_items
from db.models import Ad, AdUnit, AdUserState
from utils.date_time import utcnow
from .data import AdData, AdUnitData
from .helpers import get_media_objects


def update_ad_sync(
        ad_id: int,
        data: schemas.AdMutationData,
        commit: bool = True,
) -> AdData:
    ad = Ad.get_sync(ad_id)
    if not ad:
        raise exceptions.AdNotFoundError(ad_id)

    ad.update_sync(data.dict(exclude={"units"}), no_commit=True)

    existing_unit_ids = tuple(unit.id for unit in data.units if unit.id)
    existing_units = check_items(
        existing_unit_ids,
        AdUnit,
        exceptions.AdUnitsIdNotUniqueError,
        exceptions.AdUnitsNotFoundError,
    )

    media_objects = get_media_objects(data)

    sess().execute(
        delete(AdUnit)
        .where(
            AdUnit.ad_id == ad_id,
            AdUnit.id.not_in(existing_unit_ids),
        )
    )

    new_units = []
    all_units = []

    for position, unit_data in enumerate(data.units):
        unit = existing_units.get(unit_data.id)

        horizontal_video = media_objects.get(unit_data.horizontal_video_id)
        vertical_video = media_objects.get(unit_data.vertical_video_id)

        unit_data = unit_data.dict(exclude={"id"})
        unit_data["position"] = position
        unit_data["horizontal_video"] = horizontal_video
        unit_data["vertical_video"] = vertical_video

        if unit:
            unit.update_sync(
                unit_data,
                no_commit=True
            )
        else:
            unit = AdUnit(
                **unit_data,
                ad=ad,
            )
            new_units.append(unit)

        all_units.append(
            AdUnitData(
                unit, horizontal_video, vertical_video
            )
        )

    if new_units:
        sess().add_all(new_units)

    if commit:
        sess().commit()

    return AdData(ad, all_units)


@db_func
def update_ad(
        ad_id: int,
        data: schemas.AdMutationData,
        commit: bool = True,
) -> AdData:
    return update_ad_sync(ad_id, data, commit)


@db_func
def set_ad_unit_as_shown(
        ad_id: int,
        ad_unit_id: int,
        user_id: int,
):
    if not AdUnit.is_exists_sync(ad_unit_id, ad_id=ad_id):
        raise exceptions.AdUnitNotFoundError(ad_unit_id)

    user_state = AdUserState.get_sync(
        ad_id=ad_id,
        user_id=user_id,
        for_update=True,
    )
    if not user_state:
        user_state = AdUserState(
            ad_id=ad_id,
            user_id=user_id,
            shown_units=[ad_unit_id],
            last_shown_date=utcnow()
        )
        sess().add(user_state)
    else:
        if ad_unit_id not in user_state.shown_units:
            user_state.shown_units.append(ad_unit_id)
        user_state.last_shown_date = utcnow()

    if not (
            sess().scalar(
                select(
                    exists().where(
                        AdUnit.ad_id == ad_id,
                        AdUnit.id.not_in(user_state.shown_units),
                    )
                )
            )
    ):
        user_state.shown_units = []

    sess().commit()
    return True
