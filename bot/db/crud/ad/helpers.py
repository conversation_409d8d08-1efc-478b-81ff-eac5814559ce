import exceptions
import schemas
from db.crud.helpers import check_items
from db.models import MediaObject


def get_media_objects(data: schemas.AdMutationData):
    ids = []

    for unit in data.units:
        if unit.horizontal_video_id:
            ids.append(unit.horizontal_video_id)
        if unit.vertical_video_id:
            ids.append(unit.vertical_video_id)

    return check_items(
        ids, MediaObject,
        None,
        exceptions.MediaObjectsNotFoundError,
    )
