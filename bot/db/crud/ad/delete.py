from sqlalchemy import delete, update

from db import db_func, sess
from db.models import Ad, AdUnit, EWallet


def delete_ad_sync(ad_id: int, commit: bool = True):
    sess().execute(
        update(EWallet)
        .values(ad_id=None)
        .where(EWallet.ad_id == ad_id)
    )
    
    sess().execute(delete(AdUnit).where(AdUnit.ad_id == ad_id))

    sess().flush()
    sess().execute(delete(Ad).where(Ad.id == ad_id))

    if commit:
        sess().commit()

    return True


@db_func
def delete_ad(ad_id: int, commit: bool = True):
    return delete_ad_sync(ad_id, commit)
