from sqlalchemy import select
from sqlalchemy.orm import aliased

import exceptions
from db import db_func, sess
from db.models import Ad, AdUnit, AdUserState, MediaObject
from .data import AdData, AdUnitData


@db_func
def get_ad(ad_id: int):
    ad = Ad.get_sync(ad_id)
    if not ad:
        raise exceptions.AdNotFoundError(ad_id)

    hv = aliased(MediaObject)
    vv = aliased(MediaObject)

    units = sess().execute(
        select(AdUnit, hv, vv)
        .outerjoin(hv, AdUnit.horizontal_video_id == hv.id)
        .outerjoin(vv, AdUnit.vertical_video_id == vv.id)
        .where(AdUnit.ad_id == ad_id)
        .order_by(AdUnit.position, AdUnit.id.desc())
    ).fetchall()

    return AdData(
        ad, [
            AdUnitData(*data)
            for data in units
        ]
    )


@db_func
def get_next_ad_for_user(ad_id: int, user_id: int) -> AdUnitData | None:
    user_state = AdUserState.get_sync(ad_id=ad_id, user_id=user_id)
    shows_units = user_state.shown_units if user_state else []

    hv = aliased(MediaObject)
    vv = aliased(MediaObject)

    res = sess().execute(
        select(AdUnit, hv, vv)
        .outerjoin(hv, AdUnit.horizontal_video_id == hv.id)
        .outerjoin(vv, AdUnit.vertical_video_id == vv.id)
        .where(
            AdUnit.ad_id == ad_id,
            AdUnit.id.not_in(shows_units),
        )
        .order_by(
            AdUnit.position,
            AdUnit.id.desc()
        )
    ).fetchone()

    if res:
        return AdUnitData(*res)
    return None
