from sqlalchemy import select

from db import db_func, sess
from db.models import Translation, VirtualManagerInteractive
from schemas import VMInteractiveType


@db_func
def get_vm_interactive_list(
        step_id: int,
        type: VMInteractiveType | None = None,
        subtype: str | None = None,
        with_translations: bool = False,
        lang: str | None = None
) -> (
        list[VirtualManagerInteractive] |
        list[tuple[VirtualManagerInteractive, Translation | None]]
):
    if with_translations and lang:
        stmt = select(
            VirtualManagerInteractive,
            Translation,
        )
        stmt = stmt.outerjoin(
            Translation, Translation.filter(
                VirtualManagerInteractive, lang,
            )
        )
    else:
        stmt = select(
            VirtualManagerInteractive
        )
    stmt = stmt.where(
        VirtualManagerInteractive.step_id == step_id,
        VirtualManagerInteractive.is_deleted.is_(False)
    )
    if type is not None:
        stmt = stmt.where(VirtualManagerInteractive.type == type)
    if subtype:
        stmt = stmt.where(VirtualManagerInteractive.subtype == subtype)

    stmt = stmt.order_by(VirtualManagerInteractive.position)

    if with_translations and lang:
        return sess().execute(stmt).fetchall()

    return sess().scalars(stmt).all()


@db_func
def get_first_vm_interactive(
        step_id: int,
        type: VMInteractiveType | None = None,
        subtype: str | None = None,
) -> VirtualManagerInteractive | None:
    stmt = select(VirtualManagerInteractive)
    stmt = stmt.where(
        VirtualManagerInteractive.step_id == step_id,
        VirtualManagerInteractive.is_deleted.is_(False)
    )
    if type:
        stmt = stmt.where(VirtualManagerInteractive.type == type)
    if subtype:
        stmt = stmt.where(VirtualManagerInteractive.subtype == subtype)

    stmt = stmt.order_by(
        VirtualManagerInteractive.position,
        VirtualManagerInteractive.id
    )
    stmt = stmt.limit(1)

    return sess().scalar(stmt)
