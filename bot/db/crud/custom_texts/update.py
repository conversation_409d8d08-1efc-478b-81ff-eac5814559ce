from copy import deepcopy
from psutils.undefined import Undefined, UndefinedType
from sqlalchemy import case, func, update
from typing import Any, Sequence

from config import USE_LOCALISATION
from db import db_func, sess
from db.models import CustomTextsStorage, Translation


@db_func
def update_custom_text(
        storage: CustomTextsStorage,
        path: tuple[str, ...],
        new_value: str | None,
        new_field_data: Any,
        is_value_changed: bool,
        translations: dict[str, str | None] | None | UndefinedType = Undefined,
        # if None specified — translations will be reset
        auto_reset_translations_exclude_langs: Sequence[str] | None = None
):
    object_name = CustomTextsStorage.__name__
    object_id = storage.id

    field_name = path[0]
    storage.data = {
        **{key: deepcopy(val) for key, val in storage.data.items() if
           key != field_name},
        field_name: new_field_data
    }

    filter_translations_conditions = []
    set_translation_data_expr = None

    translation_data_key = "$.data." + ".".join(path)

    if is_value_changed or translations is None or new_value == USE_LOCALISATION:
        filter_translations_conditions.extend(
            (
                Translation.obj_type == object_name,
                Translation.obj_id == object_id,
            )
        )
        if auto_reset_translations_exclude_langs:
            filter_translations_conditions.append(
                Translation.lang.not_in(auto_reset_translations_exclude_langs),
            )
        set_translation_data_expr = func.JSON_REMOVE(
            Translation.data,
            translation_data_key,
        )
    elif translations:
        remove_langs = [lang for lang, value in translations.items() if not value]
        case_conditions = [
                              (
                                  Translation.id == f"{object_name}-{lang}-{object_id}",
                                  func.JSON_SET(
                                      Translation.data,
                                      translation_data_key + ".value",
                                      value
                                  ))
                              for lang, value in translations.items() if value
                          ] + [
                              (Translation.id.in_(
                                  tuple(
                                      f"{object_name}-{lang}-{object_id}"
                                      for lang in remove_langs
                                  )
                              ), func.JSON_REMOVE(
                                  Translation.data,
                                  translation_data_key,
                              ))
                          ]
        set_translation_data_expr = case_conditions[0][1] if len(
            case_conditions
        ) == 1 else case(case_conditions)
        filter_translations_conditions.extend(
            (
                Translation.obj_type == object_name,
                Translation.obj_id == object_id,
                Translation.lang.in_(translations),
            ),
        )

    if filter_translations_conditions and set_translation_data_expr is not None:
        stmt = update(Translation)
        stmt = stmt.execution_options(synchronize_session=False)

        stmt = stmt.values(
            {
                "data": set_translation_data_expr
            }
        )

        stmt = stmt.where(*filter_translations_conditions)
        sess().execute(stmt)

    sess().commit()

    return storage
