import schemas
from db import db_func, sess
from db.models import JournalSetting


@db_func
def update_journal_setting(
        journal_setting_id: int,
        data: schemas.AdminJournalSettingUpdateSchema,
):
    journal_setting = sess().query(JournalSetting).filter(
        JournalSetting.id == journal_setting_id
    ).first()

    if not journal_setting:
        raise ValueError("JournalSetting not found")

    journal_setting_data = data.dict(exclude_unset=True)

    for key, value in journal_setting_data.items():
        setattr(journal_setting, key, value)

    sess().commit()

    return journal_setting
