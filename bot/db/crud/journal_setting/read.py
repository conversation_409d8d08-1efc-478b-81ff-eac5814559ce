import re

from sqlalchemy import (
    select,
)
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

from db import db_func, sess
from db.models import (
    Group, JournalSetting,
)
from schemas import JournalSettingTypeEnum


# debugger = logging.getLogger('debugger.journal_settings.read')


@db_func
def get_journal_settings(
        profile_id: int,
        user_id: int,
        offset: int | None = None,
        limit: int | None = None,
) -> list[Row] | int:

    stmt: Select = select(
        JournalSetting.id,
        JournalSetting.name,
        JournalSetting.type,
        JournalSetting.settings,
        JournalSetting.table_settings,
        JournalSetting.group_id,
        JournalSetting.user_id,
    ).distinct()

    stmt = stmt.join(JournalSetting.group)
    stmt = stmt.join(JournalSetting.user)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(JournalSetting.user_id == user_id)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    stmt = stmt.order_by(JournalSetting.id)

    return sess().execute(stmt).fetchall()


@db_func
def get_journal_setting(
        journal_setting_id: int, profile_id: int, user_id: int
) -> JournalSetting | None:
    stmt: Select = select(JournalSetting)
    stmt = stmt.join(JournalSetting.group)
    stmt = stmt.join(JournalSetting.user)

    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(JournalSetting.id == journal_setting_id)
    stmt = stmt.where(JournalSetting.user_id == user_id)

    return sess().scalar(stmt)


@db_func
def get_next_journal_setting_suffix(
        setting_type: JournalSettingTypeEnum, profile_id: int, user_id: int
) -> int:
    settings = sess().query(JournalSetting).filter(
        JournalSetting.type == setting_type.value,
        JournalSetting.group_id == profile_id,
        JournalSetting.user_id == user_id,
    ).all()

    if not settings:
        return 0

    max_suffix = 0
    has_number = False

    for s in settings:
        match = re.search(r'(\d+)$', s.name)
        if match:
            has_number = True
            num = int(match.group(1))
            if num > max_suffix:
                max_suffix = num

    if has_number:
        return max_suffix + 1
    return 1
