import schemas
from db import db_func, sess
from db.models import (
    JournalSetting,
)


@db_func
def create_journal_setting(
        profile_id: int,
        user_id: int,
        data: schemas.AdminJournalSettingCreateSchema,
) -> JournalSetting:
    journal_setting = JournalSetting(
        group_id=profile_id,
        user_id=user_id,
        name=data.name,
        type=data.type,
        settings=data.settings,
    )
    sess().add(journal_setting)
    sess().commit()
    return journal_setting
