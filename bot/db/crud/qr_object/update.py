from db import db_func, sess
from db.models import MenuInStoreToQrMediaObject
from db.models.qr_media_object.qr_media_object import ProfileToQrMediaObject
from schemas import QrObjectType
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def connect_qr_media_object_to_entity_object(qr_media_object_id: int, object_name: QrObjectType, object_id: int):
    connect_object = None

    if object_name == "Menu":
        connect_object = MenuInStoreToQrMediaObject(
            menu_in_store_id=object_id,
            qr_media_object_id=qr_media_object_id,
        )

    elif object_name == "Profile":
        connect_object = ProfileToQrMediaObject(
            profile_id=object_id,
            qr_media_object_id=qr_media_object_id,
        )

    if connect_object:
        sess().add(connect_object)
        sess().commit()
    else:
        raise ValueError(f"Unsupported object name: {object_name}")
