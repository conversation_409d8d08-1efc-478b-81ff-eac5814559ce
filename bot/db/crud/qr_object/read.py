from typing import List

from sqlalchemy import select

from db import db_func, sess
from db.models import (
    MenuInStoreToQrMediaObject, QrMediaAdditionalObject, QrMediaObject,
)
from db.models.qr_media_object.qr_media_object import ProfileToQrMediaObject
from schemas import QrObjectType
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def get_qr_media_objects_by_object_id_and_target(
        object_name: QrObjectType,
        object_id: int,
        targets: List[str]
) -> list[QrMediaAdditionalObject]:
    if object_name == "Menu":
        stmt = select(QrMediaObject).join(
            MenuInStoreToQrMediaObject,
            MenuInStoreToQrMediaObject.qr_media_object_id == QrMediaObject.id
        ).where(MenuInStoreToQrMediaObject.menu_in_store_id == object_id)

    elif object_name == "Profile":
        stmt = select(QrMediaObject).join(
            ProfileToQrMediaObject,
            ProfileToQrMediaObject.qr_media_object_id == QrMediaObject.id
        ).where(ProfileToQrMediaObject.profile_id == object_id)

    else:
        raise ValueError(f"Unsupported object name: {object_name}")

    if targets:
        stmt = stmt.where(QrMediaObject.target.in_(targets))

    return sess().scalars(stmt).all()


@catch_error_with_text_variable
@db_func
def get_qr_media_object_by_id(qr_object_id: int) -> QrMediaObject:
    stmt = select(QrMediaObject).where(QrMediaObject.id == qr_object_id)
    return sess().scalar(stmt)
