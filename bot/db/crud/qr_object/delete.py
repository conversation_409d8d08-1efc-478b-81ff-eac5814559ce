from db import db_func, sess
from db.models import (
    MenuInStoreToQrMediaObject
)
from db.models.qr_media_object.qr_media_object import ProfileToQrMediaObject
from schemas import QrObjectType
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def delete_qr_media_object_from_object(qr_media_object_id: int, object_id: int, object_name: QrObjectType):

    if object_name == "Menu":
        stmt = sess().query(MenuInStoreToQrMediaObject).filter_by(
            menu_in_store_id=object_id,
            qr_media_object_id=qr_media_object_id
        )
    elif object_name == "Profile":
        stmt = sess().query(ProfileToQrMediaObject).filter_by(
            profile_id=object_id,
            qr_media_object_id=qr_media_object_id
        )
    else:
        raise ValueError(f"Unsupported object name: {object_name}")

    stmt.delete()
    sess().commit()
