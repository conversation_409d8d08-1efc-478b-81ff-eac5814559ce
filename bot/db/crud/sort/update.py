from typing import Type

from sqlalchemy import select, update

from db import db_func, sess
from db.mixins import BaseDBModel


def reorder_object_sync(
        model: Type[BaseDBModel],
        object_id: int,
        new_position: int,
        brand_id: int | None = None,
        group_id: int | None = None,
        commit: bool = True,
        **kwargs,
) -> bool:
    if not hasattr(model, "position"):
        raise TypeError("Model must have position")

    base_conditions = []

    if hasattr(model, "brand_id"):
        if not brand_id:
            raise ValueError(
                "Argument brand_id is required when model has brand_id column"
            )

        base_conditions.append(
            model.brand_id == brand_id
        )

    if hasattr(model, "group_id"):
        if not group_id:
            raise ValueError(
                "Argument group_id is required when model has group_id column"
            )

        base_conditions.append(
            model.group_id == group_id
        )

    for key, value in kwargs.items():
        if hasattr(model, key):
            base_conditions.append(getattr(model, key) == value)

    item = sess().execute(
        select(model).where(
            *base_conditions,
            model.id == object_id
        )
    ).scalar_one_or_none()

    if not item:
        return False

    current_position = item.position

    if current_position < new_position:
        sess().execute(
            update(model)
            .where(
                *base_conditions,
                model.position.between(current_position + 1, new_position)
            )
            .values(position=model.position - 1)
            .execution_options(synchronize_session='fetch')
        )
    elif current_position >= new_position:
        sess().execute(
            update(model)
            .where(
                *base_conditions,
                (
                    model.position == current_position
                    if current_position == new_position else
                    model.position.between(
                        new_position, current_position - 1
                    )
                )
            )
            .values(position=model.position + 1)
            .execution_options(synchronize_session='fetch')
        )

    item.position = new_position

    if commit:
        sess().commit()

    return True


@db_func
def reorder_object(
        model: Type[BaseDBModel],
        object_id: int,
        new_position: int,
        brand_id: int | None = None,
        group_id: int | None = None,
        **kwargs,
) -> bool:
    return reorder_object_sync(
        model, object_id, new_position, brand_id, group_id, **kwargs
    )
