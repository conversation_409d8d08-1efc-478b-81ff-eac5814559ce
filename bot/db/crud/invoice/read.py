from datetime import datetime

from sqlalchemy import case, func, literal_column, select, text
from sqlalchemy.orm import joinedload
from sqlalchemy.sql import label

import schemas
from config import INBOX_MAX_AGE
from core.payment.exceptions import PaymentInvoiceNotFoundError
from db import db_func, sess
from db.crud.helpers import (
    crm_params_filter, get_inbox_status_sort_stmt,
    user_field_if_not_anonymous,
)
from db.crud.helpers.crm import (
    crm_filter_by_inbox_cursor, crm_scope_filter, get_crm_list,
    get_inbox_type_sort_stmt,
)
from db.models import (
    Brand, ClientBot, Group, Invoice, InvoiceItem, InvoiceToFriend, MenuInStore, Scope,
    StoreOrder,
    User,
)
from db.types.operation import Operation
from schemas import IDCursor, InvoiceTypeEnum


@db_func
def get_pending_invoice_to_friend(invoice_id: int) -> InvoiceToFriend | None:

    invoice_to_friend: InvoiceToFriend = sess().query(InvoiceToFriend).filter(
        InvoiceToFriend.invoice_id == invoice_id,
        InvoiceToFriend.status == "pending"
    ).one_or_none()
    if invoice_to_friend:
        return invoice_to_friend

    return None


def get_crm_invoice_list_statement(
        user_id: int,
        params: schemas.CRMInvoiceListParams | None = None,
        operation: Operation = "list",
        cursor: schemas.IDCursor | None = None,
        inbox_cursor: schemas.InboxCursor | None = None,
        for_inbox: bool = False,
        is_platform_admin: bool | None = None,
):
    inbox_status = case(
        [
            (Invoice.is_read.is_(False),
             literal_column(f"'{schemas.InboxStatus.NEW.value}'")),
            (Invoice.is_read.is_(True),
             literal_column(f"'{schemas.InboxStatus.RECENT.value}'"))
        ]
    )

    inbox_status_sort = get_inbox_status_sort_stmt(inbox_status)

    if operation == "count":
        stmt = select(
            func.count(Invoice.id)
        )
    else:
        stmt = select(
            literal_column(f"'{schemas.InboxType.INVOICE.value}'").label("inbox_type"),
            inbox_status.label("inbox_status"),
            inbox_status_sort,
            get_inbox_type_sort_stmt(schemas.InboxType.INVOICE),
            Invoice.change_date.label("change_date"),
            literal_column("NULL").label("desired_delivery_date"),
            Invoice.id,
            literal_column("NULL").label("is_pending"),
            literal_column("'invoice'").label("type"),
            literal_column("NULL").label("privacy"),
            literal_column("NULL").label("text"),
            literal_column("NULL").label("additional_text"),
            literal_column("NULL").label("media"),
            Invoice.crm_tag.label("crm_tag"),
            Invoice.invoice_type,
            Invoice.status,
            Invoice.status.label("status_pay"),
            Invoice.status.label("current_status"),
            literal_column("NULL").label("shipment_name"),
            literal_column("NULL").label("shipment_type"),
            Invoice.first_name,
            Invoice.last_name,
            func.IFNULL(
                Invoice.full_name,
                func.concat(Invoice.first_name, " ", Invoice.last_name)
            ).label("full_name"),
            Invoice.email,
            Invoice.phone,
            user_field_if_not_anonymous(User.id, "photo_url", User.photo_url),
            Invoice.currency,
            user_field_if_not_anonymous(Invoice.user_id),
            literal_column("NULL").label("store_id"),
            literal_column("NULL").label("store_name"),
            literal_column("NULL").label("ticket_title"),
            Group.id.label("profile_id"),
            Group.name.label("profile_name"),
            Group.name.label("business_name"),
            ClientBot.display_name.label("bot_name"),
            func.round(Invoice.before_loyalty_sum / 100, 2).label("before_loyalty_sum"),
            func.round(Invoice.discount / 100, 2).label("discount"),
            func.round(Invoice.bonuses_redeemed / 100, 2).label("bonuses_redeemed"),
            func.round(Invoice.discount_and_bonuses_redeemed / 100, 2).label(
                "discount_and_bonuses_redeemed"
            ),
            func.round(Invoice.total_sum / 100, 2).label("total_sum"),
            func.round(Invoice.tips_sum / 100, 2).label("tips_sum"),
            func.round(Invoice.sum_to_pay / 100, 2).label("sum_to_pay"),
            func.round(Invoice.payer_fee / 100, 2).label("payer_fee"),
            func.round(Invoice.paid_sum / 100, 2).label("paid_sum"),
            Invoice.menu_in_store_id,
            MenuInStore.comment.label("menu_in_store_comment"),
            Invoice.title,
            literal_column("NULL").label("mark"),
            label(
                "items_text", (
                    select(
                        func.group_concat(
                            func.concat(
                                InvoiceItem.name, " x", InvoiceItem.quantity
                            ).op(
                                "SEPARATOR"
                            )(text('","')),
                        )
                    ).select_from(InvoiceItem)
                    .where(InvoiceItem.invoice_id == Invoice.id)
                )
            ),
            literal_column("NULL").label("last_message_text"),
            literal_column("NULL").label("last_message_content_type"),
            literal_column("NULL").label("last_message_media_url"),
            literal_column("NULL").label("last_message_media_mime_type"),
            literal_column("NULL").label("last_message_content"),
            Invoice.is_read.label("is_read"),
            Invoice.read_by_user_id,
            Invoice.user_comment.label("comment"),
            Invoice.time_created,
        )

        stmt = stmt.outerjoin(MenuInStore, Invoice.menu_in_store_id == MenuInStore.id)
        stmt = stmt.join(User, Invoice.user_id == User.id)

    stmt = stmt.join(Group, Invoice.group_id == Group.id)
    stmt = stmt.outerjoin(ClientBot, Invoice.bot_id == ClientBot.id)
    stmt = stmt.where(Group.status == "enabled")

    stmt = stmt.where(Invoice.invoice_type != InvoiceTypeEnum.STORE_ORDER)

    stmt = crm_filter_by_inbox_cursor(
        stmt, schemas.InboxType.INVOICE, inbox_cursor,
        Invoice.change_date, Invoice.id, inbox_status_sort,
    )
    if for_inbox:
        stmt = stmt.where(Invoice.change_date >= datetime.utcnow() - INBOX_MAX_AGE)

    stmt = crm_scope_filter(
        stmt, "invoice", user_id,
        Invoice.id, is_platform_admin=is_platform_admin,
    )

    stmt = crm_params_filter(stmt, Invoice, params, cursor, operation)
    if params:
        if params.types:
            stmt = stmt.where(Invoice.invoice_type.in_(params.types))
        if params.is_read is not None:
            stmt = stmt.where(Invoice.is_read.is_(params.is_read))

    return stmt


@db_func
def get_crm_invoice_list(
        user_id: int,
        params: schemas.CRMInvoiceListParams | None = None,
        operation: Operation = "list",
        cursor: IDCursor | None = None,
):
    stmt = get_crm_invoice_list_statement(user_id, params, operation, cursor)
    return get_crm_list(stmt, Invoice, operation, params, cursor)


@db_func
def get_invoice_items(invoice_id: int) -> list[InvoiceItem]:
    return sess().query(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id).all()


@db_func
def get_invoice(
        invoice_id: int,
        action: str | None = None,
        user_id: int | None = None
):
    stmt = select(Invoice)
    stmt = stmt.where(Invoice.id == invoice_id)

    if any((action, user_id)) and not all((action, user_id)):
        raise ValueError("Specify both action and user_id or none of them")

    if action and user_id:
        stmt = stmt.join(Invoice.group)
        stmt = stmt.where(Group.status == "enabled")
        stmt = stmt.where(
            Scope.filter_for_action(
                action, "user", user_id,
                {
                    "profile_id": Group.id,
                    "invoice_id": invoice_id,
                }
            )
        )

    stmt = stmt.group_by(Invoice.id)

    return sess().scalar(stmt)


@db_func
def get_payment_data_by_invoice_id(invoice_id: str) -> tuple[
    Invoice, StoreOrder, Brand]:
    query = (
        sess().query(Invoice, StoreOrder, Brand)
        .join(Group, Group.id == Invoice.group_id)
        .join(Brand, Brand.group_id == Group.id)
        .outerjoin(StoreOrder, StoreOrder.invoice_id == Invoice.id)
        .filter(Invoice.id == invoice_id)
        .options(joinedload(Invoice.store_order))
        .options(joinedload(Invoice.group).joinedload(Group.brand))
    )

    result = query.one_or_none()
    if not result:
        raise PaymentInvoiceNotFoundError(f"{invoice_id=}")

    invoice, store_order, brand = result

    return invoice, store_order, brand
