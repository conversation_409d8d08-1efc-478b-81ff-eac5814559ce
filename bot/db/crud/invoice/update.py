import logging
from decimal import Decimal

from incust_api.api import term
from sqlalchemy import select

from db import db_func, sess
from db.models import Invoice, InvoiceItem, OrderProduct, StoreOrder
from utils.numbers import calculate_amount_modifiers

logger = logging.getLogger('debugger')


@db_func
def save_invoice_message_id(invoice_id: int, message_id: int):
    logger.debug('start save_invoice_message_id')
    if not isinstance(message_id, int):
        logger.error(f'Not valid message_id\n{message_id=}')
        return False
    invoice: Invoice = sess().query(Invoice).get(invoice_id)
    if not invoice:
        logger.error(
            f'Not find invoice for set message_id\n{invoice_id=}, {message_id=}'
        )

        return False
    invoice.message_id = message_id
    sess().commit()
    return True


@db_func
def save_incust_transaction(invoice_id: int, transaction: term.m.Transaction | dict):
    logger.debug('save_incust_transaction:')
    invoice = sess().query(Invoice).get(invoice_id)
    if not invoice:
        logging.error(f'Not find invoice for save_incust_transaction\n{invoice_id=}')
        return False
    check = invoice.incust_check
    if not check:
        check = {}
    check.update(
        transaction=transaction.dict() if isinstance(
            transaction, term.m.Transaction
        ) else transaction
    )
    invoice.incust_check = check
    
    # Встановлюємо прапор завершення транзакції лояльності
    invoice.is_loyalty_transaction_completed = True
    
    sess().commit()
    return True


@db_func
def override_invoice_discount_amount(
        invoice: Invoice,
        order: StoreOrder | None,
        discount_percent: Decimal | float | int,
        discount_amount: Decimal | float | int,
        is_ewallet_discount: bool = False
):
    items: list[InvoiceItem] = sess().scalars(
        select(InvoiceItem)
        .where(InvoiceItem.invoice_id == invoice.id)
    ).all()
    order_products: list[OrderProduct] = sess().scalars(
        select(OrderProduct)
        .where(OrderProduct.store_order_id == order.id)
    ).all() if order else [None] * len(items)

    items_total_sum = 0

    for item, product in zip(items, order_products):
        item.unit_discount = calculate_amount_modifiers(item.price, discount_percent)
        item.unit_discount_and_bonuses_redeemed = (
                item.unit_discount +
                item.unit_bonuses_redeemed
        )
        item.final_price = item.price - item.unit_discount_and_bonuses_redeemed

        item.discount = item.unit_discount * item.quantity
        item.discount_and_bonuses_redeemed = (
                item.unit_discount_and_bonuses_redeemed *
                item.quantity
        )
        item.final_sum = item.final_price * item.quantity

        if product:
            product.discount_amount = item.unit_discount
            product.discount_and_bonuses = item.discount_and_bonuses_redeemed
            product.price_after_loyalty = item.final_price

            product.discount_sum = item.discount
            product.discount_and_bonuses_sum = item.discount_and_bonuses_redeemed
            product.total_sum = item.final_sum

        items_total_sum += item.final_sum

    invoice.discount = int(discount_amount * 100)
    invoice.discount_and_bonuses_redeemed = invoice.discount + invoice.bonuses_redeemed
    new_total_sum = (
            items_total_sum + invoice.shipment_cost
    )
    invoice.total_sum_with_extra_fee = (
            new_total_sum + invoice.total_sum_with_extra_fee - invoice.total_sum
    )
    invoice.total_sum = new_total_sum
    invoice.sum_to_pay = invoice.total_sum_with_extra_fee + invoice.tips_sum
    invoice.is_ewallet_discount = is_ewallet_discount

    if order:
        order.discount = invoice.discount
        order.discount_and_bonuses = invoice.discount_and_bonuses_redeemed
        order.total_sum_with_extra_fee = invoice.total_sum_with_extra_fee
        order.total_sum = invoice.total_sum
        order.sum_to_pay = invoice.sum_to_pay
        order.is_ewallet_discount = invoice.is_ewallet_discount

    sess().commit()

    return invoice
