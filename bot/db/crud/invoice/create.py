from datetime import datetime
from sqlalchemy import and_, or_

import schemas
from db import db_func, sess
from db.models import (
    ClientBot, Friend, Group, Invoice, InvoiceItem, InvoiceTemplate, InvoiceToFriend,
    MediaObject,
    MenuInStore,
    StoreOrder,
    User,
)
from schemas import InvoicePaymentModeLiteral, InvoiceQrMode, InvoiceTypeEnum
from utils.decorators import catch_error_with_text_variable


@catch_error_with_text_variable
@db_func
def create_invoice_db(
        invoice_type: InvoiceTypeEnum,
        user: User,
        creator: User,
        payment_mode: InvoicePaymentModeLiteral,
        items: list[schemas.CreateInvoiceItemData],
        bot: ClientBot = None,
        count: int = 1,
        currency: str | None = None,
        title: str | None = None,
        description: str | None = None,
        photo_path: str | None = None,
        live_time: float | None = None,
        expiration_datetime: datetime | None = None,
        brand_group: Group = None,
        incust_check: dict | None = None,
        payment_bot_menu: ClientBot = None,
        menu_in_store: MenuInStore = None,
        invoice_template: InvoiceTemplate | None = None,
        store_order: StoreOrder | None = None,
        external_transaction_id: str | None = None,
        client_redirect_url: str | None = None,
        successful_payment_callback_url: str | None = None,
        first_name: str | None = None,
        last_name: str | None = None,
        email: str | None = None,
        phone: str | None = None,
        user_comment: str | None = None,
        qr_mode: InvoiceQrMode | None = None,
        shipment_cost: int = 0,
        tips_sum: int = 0,
        utm_labels: dict | None = None,
        **kwargs,
) -> Invoice:
    invoice = Invoice(
        invoice_type=invoice_type,
        user=user,
        creator=creator,
        bot=bot,
        count=count,
        currency=currency,
        title=title,
        description=description,
        photo_path=photo_path,
        live_time=live_time,
        expiration_datetime=expiration_datetime,
        brand_group=brand_group,
        incust_check=incust_check,
        payment_bot_menu=payment_bot_menu,
        menu_in_store=menu_in_store,
        invoice_template=invoice_template,
        invoice_template_id=invoice_template.id if invoice_template else None,
        payment_mode=payment_mode,
        store_order=store_order,
        external_transaction_id=external_transaction_id,
        client_redirect_url=client_redirect_url,
        successful_payment_callback_url=successful_payment_callback_url,
        first_name=first_name,
        last_name=last_name,
        email=email,
        phone=phone,
        user_comment=user_comment,
        qr_mode=qr_mode,
        utm_labels=utm_labels if utm_labels else None,
        **kwargs
    )

    invoice.before_loyalty_sum = 0
    invoice.discount = 0
    invoice.bonuses_redeemed = 0
    invoice.discount_and_bonuses_redeemed = 0

    items_total_sum = 0

    for item in items:
        price = round(item.price * 100)
        unit_discount = round(item.unit_discount * 100)
        unit_bonuses_redeemed = round(item.unit_bonuses_redeemed * 100)
        unit_discount_and_bonuses_redeemed = unit_discount + unit_bonuses_redeemed
        final_price = price - unit_discount_and_bonuses_redeemed

        before_loyalty_sum = price * item.quantity
        discount = unit_discount * item.quantity
        bonuses_redeemed = unit_bonuses_redeemed * item.quantity
        discount_and_bonuses_redeemed = (unit_discount_and_bonuses_redeemed *
                                         item.quantity)
        final_sum = final_price * item.quantity

        invoice.items.append(
            InvoiceItem(
                name=item.name,
                category=item.category,
                quantity=item.quantity,
                item_code=item.item_code,
                price=price,
                unit_discount=unit_discount,
                unit_bonuses_redeemed=unit_bonuses_redeemed,
                unit_discount_and_bonuses_redeemed=unit_discount_and_bonuses_redeemed,
                final_price=final_price,
                before_loyalty_sum=before_loyalty_sum,
                discount=discount,
                bonuses_redeemed=bonuses_redeemed,
                discount_and_bonuses_redeemed=discount_and_bonuses_redeemed,
                final_sum=final_sum,
            )
        )

        invoice.before_loyalty_sum += before_loyalty_sum
        invoice.discount += discount
        invoice.bonuses_redeemed += bonuses_redeemed
        invoice.discount_and_bonuses_redeemed += discount_and_bonuses_redeemed

        items_total_sum += final_sum

    invoice.shipment_cost = shipment_cost
    invoice.total_sum = items_total_sum + shipment_cost
    invoice.total_sum_with_extra_fee = items_total_sum + shipment_cost
    invoice.tips_sum = tips_sum
    invoice.sum_to_pay = invoice.total_sum_with_extra_fee + tips_sum

    sess().add(invoice)
    sess().commit()
    return invoice


@db_func
def add_friend_to_invoice(
        invoice: Invoice,
        friend_id: int,
        comment: str | None = None,
        comment_media: MediaObject | None = None,
):
    invoice.is_friend = True

    invoice_to_friends = sess().query(InvoiceToFriend).filter(
        InvoiceToFriend.invoice_id == invoice.id,
        InvoiceToFriend.status == "pending"
    ).all()
    for invoice_to_friend in invoice_to_friends:
        invoice_to_friend.status = "canceled"

    invoice_to_friend = InvoiceToFriend(
        invoice_id=invoice.id,
        friend_id=friend_id,
        comment=comment,
        comment_media=comment_media,
    )
    sess().add(invoice_to_friend)

    # set last date friend used
    friend: Friend = sess().query(Friend).filter(
        or_(
            and_(
                Friend.user_id == invoice.user_id,
                Friend.friend_id == friend_id,
            ),
            and_(
                Friend.user_id == friend_id,
                Friend.friend_id == invoice.user_id,
            )
        )
    ).one_or_none()
    if not friend:
        raise Exception(f"Friend {friend_id = } for {invoice.user_id = } not found")

    friend.datetime_used = datetime.utcnow()

    sess().commit()
    return invoice_to_friend
