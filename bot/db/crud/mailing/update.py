from datetime import datetime

from sqlalchemy import func, update

import schemas
from db import db_func, sess
from db.models import Mailing, MailingMessage


@db_func
def mass_update_mailing_messages_status(
        status: schemas.MailingMessageStatusEnum,
        start_date: datetime,
        mailing_messages_ids: list[int],
):
    stmt = (
        update(MailingMessage)
        .where(MailingMessage.id.in_(mailing_messages_ids))
        .values(status=status, start_datetime=start_date)
    )
    sess().execute(stmt)
    sess().commit()


@db_func
def mass_update_mailings_status(
        status: schemas.MailingStatusEnum,
        mailing_ids: list[int],
):
    stmt = (
        update(Mailing)
        .where(Mailing.id.in_(mailing_ids))
        .values(status=status)
    )
    sess().execute(stmt)
    sess().commit()


@db_func
def update_mailing_message_status(
        mailing_message_id: int,
        mailing_id: int,
        status: schemas.MailingMessageStatusEnum,
        retry_info: dict | None = None,
        error_details: dict | None = None,
):
    message_data = {
        "status": status,
        "end_datetime": func.UTC_TIMESTAMP()
    }
    if retry_info:
        message_data["retry_info"] = retry_info
    if error_details:
        message_data["error_details"] = error_details

    sess().execute(
        update(MailingMessage)
        .values(message_data)
        .where(MailingMessage.id == mailing_message_id)
    )

    sess().execute(
        update(Mailing)
        .values(last_sent_datetime=func.UTC_TIMESTAMP())
        .where(Mailing.id == mailing_id)
    )

    sess().commit()

    return True
