from datetime import datetime, timedelta

from sqlalchemy import distinct, func, or_, select
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

import schemas
from db import db_func, sess
from db.models import (
    ClientBot, Customer, Group, Mailing, MailingMessage, Scope, User,
    UserClientBotActivity,
)
from db.query_builder.query_builder import DBQueryBuilder
from db.types.operation import Operation
from schemas import (
    ExtraFilterData, FilterType, QueryBuilderObjConf,
    QueryBuilderSettings, SortData,
)


@db_func
def get_mailing_list(
        profile_id: int,
        user_id: int,
        params: schemas.AdminListParams,
        operation: Operation = "list",
) -> list[Row] | int:
    stmt: Select

    available_data = {
        "profile_id": profile_id,
    }

    if operation == "count":
        stmt = select(func.count(distinct(Mailing.id)))
        stmt = stmt.where(
            Scope.filter_for_action(
                "profile:read",
                "user", user_id,
                available_data,
            )
        )
    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="profile",
            target="user",
            target_id=user_id,
            available_data=available_data
        )

        stmt = select(
            Group.id.label("profile_id"),
            Mailing.id,
            Mailing.name,
            Mailing.status,
            Mailing.sent_info,
            Mailing.channels,
            read_allowed,
            edit_allowed
        )
        stmt = stmt.where(read_allowed.is_(True))

    stmt = stmt.where(Mailing.is_test.is_(False))
    stmt = stmt.join(Mailing.group)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")

    if params.search_text:
        params.search_text = params.search_text.strip()
        stmt = stmt.where(
            or_(
                Mailing.name.contains(params.search_text),
                Mailing.id == params.search_text,
            )
        )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(Mailing.id.desc())

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    return sess().execute(stmt).fetchall()


@db_func
def get_mailing_messages_list(
        mailing_id: int,
        profile_id: int,
        user_id: int,
        params: schemas.AdminListParams,
        operation: Operation = "list",
) -> list[Row] | int:
    stmt: Select

    available_data = {
        "profile_id": profile_id,
    }

    if operation == "count":
        stmt = select(func.count(distinct(Mailing.id)))
        stmt = stmt.where(
            Scope.filter_for_action(
                "mailing:read",
                "user", user_id,
                available_data,
            )
        )
    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="mailing",
            target="user",
            target_id=user_id,
            available_data=available_data
        )

        stmt = select(
            MailingMessage.id,
            read_allowed,
            edit_allowed
        )
        stmt = stmt.where(read_allowed.is_(True))
        stmt = stmt.where(MailingMessage.mailing_id == mailing_id)

    if params.search_text:
        params.search_text = params.search_text.strip()
        stmt = stmt.where(
            or_(
                MailingMessage.id == params.search_text,
            )
        )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(MailingMessage.id.desc())

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    return sess().execute(stmt).fetchall()


@db_func
def get_customers_count_by_channel(
        profile_id: int,
        channel: schemas.MailingChannelTypeEnum,
        wa_without_template: bool = False,
):
    stmt = select(func.count(distinct(Customer.id)).label("count"))
    stmt = stmt.join(User, User.id == Customer.user_id)
    stmt = stmt.where(
        Customer.is_accept_agreement.is_(True),
        Customer.marketing_consent.is_(True),
        Customer.profile_id == profile_id,
    )

    if channel:
        if channel == "bot":
            bot = ClientBot.get_sync(group_id=profile_id)

            if not bot:
                return 0
            
            stmt = stmt.join(
                UserClientBotActivity, UserClientBotActivity.user_id == User.id,
                                       UserClientBotActivity.bot_id == bot.id
            )
            stmt = stmt.where(
                UserClientBotActivity.is_entered_bot.is_(True),
                UserClientBotActivity.is_active.is_(True)
            )
            if bot.bot_type == "telegram":
                stmt = stmt.where(User.chat_id.isnot(None))
            elif bot.bot_type == "whatsapp":
                stmt = stmt.where(User.wa_phone.isnot(None))
                if wa_without_template:
                    stmt = stmt.where(
                        UserClientBotActivity.last_activity >= datetime.utcnow() -
                        timedelta(
                            hours=24
                        )
                    )
        elif channel == "email":
            stmt = stmt.where(User.email.isnot(None))

    return sess().scalar(stmt)


@db_func
def get_customers_by_mailing_and_channel(
        mailing: Mailing,
        channel: schemas.MailingChannelTypeEnum,
        params: schemas.MailingParams,
        cursor: schemas.IDCursor | None = None,
) -> list[User]:
    if not cursor:
        cursor = params.cursor

    if cursor and cursor.direction != schemas.CursorDirection.NEXT:
        raise ValueError("Cursor direction PREV is not supported")

    stmt = select(User).select_from(Customer).join(User, User.id == Customer.user_id)
    stmt = stmt.where(
        Customer.is_accept_agreement.is_(True),
        Customer.marketing_consent.is_(True),
        Customer.profile_id == mailing.group_id,
    )

    if channel == "BOT":
        bot_stmt = select(ClientBot.id).where(ClientBot.group_id == mailing.group_id)
        bot = sess().scalar(bot_stmt)
        if bot.bot_type == "telegram":
            stmt = stmt.where(User.chat_id.isnot(None))
        elif bot.bot_type == "whatsapp":
            stmt = stmt.where(User.wa_phone.isnot(None))
    elif channel == "EMAIL":
        stmt = stmt.where(User.email.isnot(None))

    if cursor:
        if cursor.direction == schemas.CursorDirection.BACK:
            stmt = stmt.where(Customer.id > cursor.id)
        else:
            stmt = stmt.where(Customer.id < cursor.id)

    stmt = stmt.group_by(Customer.id)
    stmt = stmt.order_by(Customer.id.desc())

    return sess().scalars(stmt).all()


@db_func
def get_active_mailings() -> list[Mailing]:
    stmt = select(Mailing).where(Mailing.status == "created")

    return sess().execute(stmt).fetchall()


@db_func
def get_mailing_messages(
        params: schemas.MailingParams | None = None,
        cursor: schemas.IDCursor | None = None,
):
    if not cursor:
        cursor = params.cursor

    if cursor and cursor.direction != schemas.CursorDirection.NEXT:
        raise ValueError("Cursor direction PREV is not supported")

    stmt = select(
        MailingMessage.id,
        MailingMessage.status,
        MailingMessage.bot_id,
        MailingMessage.user_id,
        MailingMessage.email,
        MailingMessage.chat_id,
        MailingMessage.phone,
        MailingMessage.user_name,
        MailingMessage.message,
        MailingMessage.channel_type,
    )
    stmt = stmt.where(MailingMessage.status == "CREATED")

    if cursor:
        if cursor.direction == schemas.CursorDirection.BACK:
            stmt = stmt.where(MailingMessage.id > cursor.id)
        else:
            stmt = stmt.where(MailingMessage.id < cursor.id)

    stmt = stmt.group_by(MailingMessage.id)
    stmt = stmt.order_by(MailingMessage.id.desc())

    return sess().execute(stmt).fetchall()


@db_func
def get_users_for_mailing_test(profile_id: int):
    scopes = ["crm:edit", "crm:read", "profile:admin"]

    stmt = select(User).join(Scope, Scope.user_id == User.id).where(
        Scope.target == "user",
        Scope.scope.in_(scopes),
        Scope.profile_id == profile_id
    ).group_by(User.id).distinct()

    return sess().scalars(stmt).all()


@db_func
def get_mailing_messages_count_by_status(
        status: schemas.MailingMessageStatusEnum, mailing_id: int
):
    stmt = select(func.count(MailingMessage.id)).where(
        MailingMessage.status == status,
        MailingMessage.mailing_id == mailing_id
    )

    return sess().scalar(stmt)


default_customers_settings_objects = {
    "user": QueryBuilderObjConf(
        model=User,
        select_from=True,
        fields=[
            "id",
            "email",
            "wa_phone",
            "chat_id",
            "username",
            "full_name",
            "first_name",
            "last_name",
        ],
        fields_as_row_data=True
    ),
    "customer": QueryBuilderObjConf(
        model=Customer,
        fields=[
            "id",
            "marketing_consent",
            "is_accept_agreement",
            "profile_id",
            "lang",
        ],
        join_expr=User.id == Customer.user_id,
    ),
}

bot_activity_customers_settings_objects = {
    "user_client_bot_activity": QueryBuilderObjConf(
        model=UserClientBotActivity,
        fields=[
            "is_entered_bot",
            "last_activity",
            "bot_id",
        ],
        join_expr=User.id == UserClientBotActivity.user_id,
    ),
}

bot_activity_customers_extra = ExtraFilterData(
    type=FilterType.EQUAL,
    field="user_client_bot_activity.bot_id",
    name="bot_id",
)

customers = DBQueryBuilder(
    QueryBuilderSettings(
        objects={
            "user": QueryBuilderObjConf(
                model=User,
                select_from=True,
                fields=[
                    "id",
                    "email",
                    "wa_phone",
                    "chat_id",
                    "username",
                    "full_name",
                    "first_name",
                    "last_name",
                ],
                fields_as_row_data=True
            ),
            "customer": QueryBuilderObjConf(
                model=Customer,
                fields=[
                    "id",
                    "marketing_consent",
                    "is_accept_agreement",
                    "profile_id",
                    "lang",
                ],
                join_expr=User.id == Customer.user_id,
            ),
            "user_client_bot_activity": QueryBuilderObjConf(
                model=UserClientBotActivity,
                fields=[
                    "is_entered_bot",
                    "is_active",
                    "last_activity",
                    "bot_id",
                ],
                join_expr=User.id == UserClientBotActivity.user_id,
            ),
        },
        extra_filters=[
            ExtraFilterData(
                type=FilterType.EQUAL,
                field="customer.profile_id",
                name="profile_id",
            ),
            ExtraFilterData(
                type=FilterType.EQUAL,
                field="user_client_bot_activity.bot_id",
                name="bot_id",
            ),
            schemas.ExtraFilterData(
                type=schemas.FilterType.EQUAL,
                field="customer.marketing_consent",
                name="is_marketing_consent",
            ),
            schemas.ExtraFilterData(
                type=schemas.FilterType.EQUAL,
                field="customer.is_accept_agreement",
                name="is_accept_agreement",
            ),
            schemas.ExtraFilterData(
                type=schemas.FilterType.EQUAL,
                field="user_client_bot_activity.is_entered_bot",
                name="is_entered_bot",
            ),
            schemas.ExtraFilterData(
                type=schemas.FilterType.EQUAL,
                field="user_client_bot_activity.is_active",
                name="is_bot_active",
            ),
            schemas.ExtraFilterData(
                type=schemas.FilterType.GREATER_THAN_OR_EQUAL,
                field="user_client_bot_activity.last_activity",
                name="last_activity_gte",
            )
        ],
        default_sort=[
            SortData(field="customer.id", desc=True),
        ],
        row_data_model=schemas.MailingUser,
    ),
)

mailing_messages = DBQueryBuilder(
    QueryBuilderSettings(
        objects={
            "mailing_message": QueryBuilderObjConf(
                model=MailingMessage,
                select_from=True,
                fields=[
                    "id",
                    "email",
                    "phone",
                    "chat_id",
                    "user_name",
                    "message",
                    "channel_type",
                    "status",
                    "lang",
                    "mailing_id",
                    "bot_id",
                    "user_id",
                ],
                fields_as_row_data=True
            ),
            "user": QueryBuilderObjConf(
                model=User,
                fields=[
                    "first_name",
                    "last_name",
                    "full_name",
                ],
                join_expr=MailingMessage.user_id == User.id,
            ),
            "client_bot": QueryBuilderObjConf(
                model=ClientBot,
                fields=[
                    "bot_type",
                    "display_name",
                    "token",
                    "whatsapp_from",
                ],
                join_expr=MailingMessage.bot_id == ClientBot.id,
            ),
            "mailing": QueryBuilderObjConf(
                model=Mailing,
                fields=[
                    "message_info",
                ],
                join_expr=MailingMessage.mailing_id == Mailing.id,
            ),
            "group": QueryBuilderObjConf(
                model=Group,
                fields=[
                    "name",
                ],
                join_expr=Mailing.group_id == Group.id,
            ),
        },
        default_sort=[
            SortData(field="mailing_message.id", desc=True),
        ],
        row_data_model=schemas.MailingMessageQueryItemResult,
    ),
)
