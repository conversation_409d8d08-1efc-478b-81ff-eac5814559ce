import schemas
from db import db_func, sess
from db.models import (
    Mailing, MailingMessage,
)


@db_func
def create_mailing(
        data: schemas.CreateMailingSchema, profile_id: int,
        media_id: int | None = None,
        is_test: bool = False,
) -> Mailing:
    mailing = Mailing(
        group_id=profile_id,
        **data.dict(exclude_none=True, exclude={"channels", "media", "media_id"}),
        channels=[channel.value for channel in data.channels],
        status=schemas.MailingStatusEnum.CREATED,
        is_test=is_test,
        media_id=media_id,
    )
    sess().add(mailing)
    sess().commit()

    return mailing


@db_func
def create_mailing_message(
        data: schemas.CreateMailingMessageSchema
) -> MailingMessage:
    mailing_msg = MailingMessage(
        **data.dict(exclude_none=True),
        status="CREATED",
    )
    sess().add(mailing_msg)
    sess().commit()

    return mailing_msg


@db_func
def create_mailing_messages(
        data: list[schemas.CreateMailingMessageSchema]
) -> list[MailingMessage]:
    mailing_msgs = [
        MailingMessage(**msg.dict(exclude_none=True), status="CREATED")
        for msg in data
    ]
    sess().bulk_save_objects(mailing_msgs)
    sess().commit()

    return mailing_msgs
