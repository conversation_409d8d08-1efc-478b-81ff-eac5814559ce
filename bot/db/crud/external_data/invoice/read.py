from datetime import datetime

from core.external_data.schemas import InvoiceStatusLiteral

from db import sess
from db.models import Brand, Store, Group, Invoice

from sqlalchemy import select


def get_invoices_external_data(
        group_id: int,
        date_from: datetime,
        date_to: datetime,
        store_id: int | None = None,
        offset: int = 0,
        limit: int = 100,
        statuses: list[InvoiceStatusLiteral] | None = None,
) -> list[Invoice]:
    stmt = select(Invoice)
    stmt = stmt.join(Group, Group.id == Invoice.group_id)

    if store_id:
        stmt = stmt.join(Group.brand)
        stmt = stmt.join(Store, Brand.id == Store.brand_id)

    stmt = stmt.where(Group.id == group_id)
    if store_id:
        stmt = stmt.where(Store.id == store_id)

    stmt = stmt.where(Invoice.time_created >= date_from)
    stmt = stmt.where(Invoice.time_created < date_to)

    if statuses:
        stmt = stmt.where(Invoice.status.in_(statuses))

    stmt = stmt.order_by(Invoice.time_created)

    stmt = stmt.offset(offset)
    stmt = stmt.limit(limit)

    return sess().scalars(stmt).all()
