from datetime import datetime

from core.external_data.schemas import ShipmentStatusLiteral

from db import sess
from db.models import Brand, Store, StoreOrder, ExternalOrder, OrderProduct, OrderAttribute, OrderShippingStatus, \
    OrderShipment, OrderCustomPayment, StoreAttribute, StoreOrderBillingAddress

from sqlalchemy import desc, select


def get_orders_external_data(
        brand_id: int,
        date_from: datetime,
        date_to: datetime,
        store_id: int | None = None,
        offset: int = 0,
        limit: int = 100,
        statuses: list[ShipmentStatusLiteral] | None = None,
) -> list[StoreOrder]:
    stmt = select(StoreOrder)
    stmt = stmt.join(Store)
    stmt = stmt.join(Brand, Brand.id == Store.brand_id)
    if statuses:
        stmt = stmt.join(OrderShippingStatus, OrderShippingStatus.store_order_id == StoreOrder.id)

    stmt = stmt.where(Brand.id == brand_id)
    if store_id:
        stmt = stmt.where(Store.id == store_id)

    stmt = stmt.where(StoreOrder.create_date >= date_from)
    stmt = stmt.where(StoreOrder.create_date < date_to)

    if statuses:
        stmt = stmt.where(OrderShippingStatus.status.in_(statuses))

    stmt = stmt.order_by(StoreOrder.create_date)

    stmt = stmt.offset(offset)
    stmt = stmt.limit(limit)

    return sess().scalars(stmt).all()


def get_orders_products_external_data(order_ids: list[int]) -> dict[int, list[OrderProduct]]:
    stmt = select(OrderProduct)

    stmt = stmt.where(OrderProduct.store_order_id.in_(order_ids))

    products = sess().scalars(stmt).all()

    results = {}
    for order_product in products:
        if order_product.store_order_id not in results:
            results[order_product.store_order_id] = []
        results[order_product.store_order_id].append(order_product)

    return results


def get_order_product_attributes_external_data(
        order_product_ids: list[int]
) -> dict[int, list[tuple[OrderAttribute, StoreAttribute]]]:
    stmt = select(OrderAttribute, StoreAttribute)

    stmt = stmt.join(StoreAttribute, OrderAttribute.attribute_id == StoreAttribute.id)

    stmt = stmt.where(OrderAttribute.order_product_id.in_(order_product_ids))

    attributes = sess().execute(stmt).all()

    results = {}
    for order_attribute, attribute in attributes:
        if order_attribute.order_product_id not in results:
            results[order_attribute.order_product_id] = []
        results[order_attribute.order_product_id].append((order_attribute, attribute,))

    return results


def get_orders_custom_payments_external_data(order_ids: list[int]) -> dict[int, OrderCustomPayment]:
    stmt = select(OrderCustomPayment)
    stmt = stmt.where(OrderCustomPayment.store_order_id.in_(order_ids))
    custom_payments = sess().scalars(stmt).all()

    custom_payments = {custom_payment.store_order_id: custom_payment for custom_payment in custom_payments}
    return {order_id: custom_payments.get(order_id) for order_id in order_ids}


def get_orders_shipments_external_data(order_ids: list[int]) -> dict[int, OrderShipment]:
    stmt = select(OrderShipment)
    stmt = stmt.where(OrderShipment.store_order_id.in_(order_ids))
    shipments = sess().scalars(stmt).all()

    shipments = {shipment.store_order_id: shipment for shipment in shipments}
    return {order_id: shipments.get(order_id) for order_id in order_ids}


def get_shipment_statuses_external_data(order_ids: list[int]) -> dict[int, list[OrderShippingStatus]]:
    stmt = select(OrderShippingStatus)
    stmt = stmt.where(OrderShippingStatus.store_order_id.in_(order_ids))
    stmt = stmt.order_by(desc(OrderShippingStatus.time_created))
    statuses = sess().scalars(stmt).all()

    results = {}
    for status in statuses:
        if status.store_order_id not in results:
            results[status.store_order_id] = []
        results[status.store_order_id].append(status)

    return results


def get_orders_external_orders_external_data(order_ids: list[int]) -> dict[int, ExternalOrder]:
    stmt = select(ExternalOrder)
    stmt = stmt.where(ExternalOrder.order_id.in_(order_ids))
    external_orders = sess().scalars(stmt).all()

    external_orders = {external_order.order_id: external_order for external_order in external_orders}
    return {order_id: external_orders.get(order_id) for order_id in order_ids}


def get_orders_billing_address_external_data(order_ids: list[int]) -> dict[int, StoreOrderBillingAddress]:
    stmt = select(StoreOrderBillingAddress)
    stmt = stmt.where(StoreOrderBillingAddress.order_id.in_(order_ids))
    billings = sess().scalars(stmt).all()

    billings = {billing.order_id: billing for billing in billings}
    return {order_id: billings.get(order_id) for order_id in order_ids}
