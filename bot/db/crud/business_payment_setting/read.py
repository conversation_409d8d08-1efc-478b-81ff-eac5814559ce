from sqlalchemy import distinct, func, or_, select, outerjoin
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

import schemas
from db import db_func, sess
from db.models import BusinessPaymentSetting, BusinessPaymentData, Group, Scope
from db.types.operation import Operation


@db_func
def get_active_business_payment_data(
    business_payment_setting_id: int,
    payment_method: str | None = None,
    only_enabled: bool | None = None,
) -> list[BusinessPaymentData]:
    """
    Отримати невидалені записи BusinessPaymentData для конкретного BusinessPaymentSetting
    з можливістю фільтрації за payment_method та is_enabled
    
    Args:
        business_payment_setting_id: ID бізнес-налаштування оплати
        payment_method: Опціональна фільтрація за методом оплати
        only_enabled: Якщо True - отримати тільки записи з is_enabled=True,
                     якщо False - тільки з is_enabled=False,
                     якщо None - отримати всі невидалені записи
    
    Returns:
        Список BusinessPaymentData
    """
    stmt = select(BusinessPaymentData).where(
        BusinessPaymentData.business_payment_setting_id == business_payment_setting_id,
        BusinessPaymentData.is_deleted.is_(False)
    )
    
    if payment_method:
        stmt = stmt.where(BusinessPaymentData.payment_method == payment_method)
    
    if only_enabled is not None:
        stmt = stmt.where(BusinessPaymentData.is_enabled.is_(only_enabled))
    
    return list(sess().execute(stmt).scalars().all())


@db_func
def get_admin_business_payment_setting_list(
    params: schemas.AdminListParams,
    user_id: int,
    operation: Operation = "list",
) -> list[Row]:
    stmt: Select

    available_data = {
        "business_payment_setting_id": BusinessPaymentSetting.id,
    }

    if operation == "count":
        stmt = select(func.count(distinct(BusinessPaymentSetting.id)))
        stmt = stmt.where(
            Scope.filter_for_action(
                "profile:read",
                "user", user_id,
                available_data,
            )
        )
    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="profile",
            target="user",
            target_id=user_id,
            available_data=available_data
        )

        stmt = select(
            BusinessPaymentSetting.id,
            BusinessPaymentSetting.creator_id,
            BusinessPaymentSetting.incust_account_id,
            BusinessPaymentSetting.name,
            BusinessPaymentSetting.description,
            BusinessPaymentSetting.is_enabled,
            read_allowed, edit_allowed,
        )
        stmt = stmt.where(read_allowed.is_(True))
        
    stmt = stmt.where(BusinessPaymentSetting.is_deleted.is_(False))

    if params.search_text:
        params.search_text = params.search_text.strip()
        stmt = stmt.where(
            or_(
                BusinessPaymentSetting.name.contains(params.search_text),
                BusinessPaymentSetting.description.contains(params.search_text),
                BusinessPaymentSetting.id == params.search_text,
            )
        )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(BusinessPaymentSetting.time_created.desc(), BusinessPaymentSetting.id.desc())

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    settings = sess().execute(stmt).fetchall()

    if settings:
        setting_ids = [s.id for s in settings]

        payment_data_stmt = select(
            BusinessPaymentData
        ).where(
            BusinessPaymentData.business_payment_setting_id.in_(setting_ids),
            BusinessPaymentData.is_deleted.is_(False)
        )
        
        payment_data_list = sess().execute(payment_data_stmt).scalars().all()

        payment_data_dict = {}
        for pd in payment_data_list:
            if pd.business_payment_setting_id not in payment_data_dict:
                payment_data_dict[pd.business_payment_setting_id] = []
            payment_data_dict[pd.business_payment_setting_id].append(pd)

        return [(setting, payment_data_dict.get(setting.id, [])) for setting in settings]

    return [(setting, []) for setting in settings]
