from sqlalchemy import update

from db import db_func, sess
from db.models import BusinessPaymentData, BusinessPaymentSetting
from schemas import (
    BusinessPaymentDataUpdateSchema,
)


@db_func
def update_business_payment_setting(
        business_payment_setting_id: int, 
        incust_account_id: int,
        creator_id: int, 
        name: str | None = None, 
        description: str | None = None,
        is_enabled: bool | None = None,
        payment_data: list[BusinessPaymentDataUpdateSchema] | None = None,
):

    if is_enabled is not None:
        is_enabled = is_enabled
    else:
        is_enabled = False

    stmt = update(BusinessPaymentSetting).where(
        BusinessPaymentSetting.id == business_payment_setting_id
    ).values(
        incust_account_id=incust_account_id, 
        creator_id=creator_id,
        name=name, description=description, 
        is_enabled=is_enabled,
    )
    sess().execute(stmt)

    if payment_data:
        for pd in payment_data:
            if pd.id:
                sess().query(BusinessPaymentData).filter(
                    BusinessPaymentData.id == pd.id,
                ).update({BusinessPaymentData.payment_method: pd.payment_method,
                          BusinessPaymentData.is_enabled: pd.is_enabled,
                          BusinessPaymentData.json_data: pd.json_data}
                          )
            else:
                payment_data_item = BusinessPaymentData(
                    business_payment_setting_id=business_payment_setting_id,
                    payment_method=pd.payment_method,
                    json_data=pd.json_data,
                    is_enabled=pd.is_enabled,
                )
                sess().add(payment_data_item)
    
    sess().commit()


@db_func
def delete_business_payment_data(business_payment_data_id: int) -> bool:
    """
    Позначити запис BusinessPaymentData як видалений
    """
    stmt = update(BusinessPaymentData).where(
        BusinessPaymentData.id == business_payment_data_id
    ).values(
        is_deleted=True
    )
    result = sess().execute(stmt)
    sess().commit()
    
    return result.rowcount > 0


@db_func
def update_business_payment_data_enabled(business_payment_data_id: int, is_enabled: bool) -> bool:
    """
    Оновити статус is_enabled для BusinessPaymentData
    """
    stmt = update(BusinessPaymentData).where(
        BusinessPaymentData.id == business_payment_data_id,
        BusinessPaymentData.is_deleted.is_(False)
    ).values(
        is_enabled=is_enabled
    )
    result = sess().execute(stmt)
    sess().commit()
    
    return result.rowcount > 0
