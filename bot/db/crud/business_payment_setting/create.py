from sqlalchemy import update

from db import db_func, sess
from db.models import BusinessPaymentSetting, BusinessPaymentData
from schemas import BusinessPaymentMethodEnum, AdminBusinessPaymentSettingCreateSchema, BusinessPaymentDataCreateSchema


@db_func
def create_business_payment_setting(
        incust_account_id: str,
        creator_id: int, 
        name: str | None = None, 
        description: str | None = None,
        is_enabled: bool | None = None,
        payment_data: list[BusinessPaymentDataCreateSchema] | None = None
) -> BusinessPaymentSetting:
    if is_enabled is not None:
        is_enabled = is_enabled
    else:
        is_enabled = False

    existing_setting = sess().query(BusinessPaymentSetting).filter(
        BusinessPaymentSetting.incust_account_id == incust_account_id
    ).one_or_none()

    if existing_setting:
        stmt = update(BusinessPaymentSetting).where(
            BusinessPaymentSetting.incust_account_id == incust_account_id
        ).values(
            creator_id=creator_id,
            name=name,
            description=description,
            is_deleted=False,
            is_enabled=is_enabled,
        )
        sess().execute(stmt)
        
        # Оновлюємо або створюємо дані про платіжні методи
        if payment_data:
            # Видаляємо старі дані
            sess().query(BusinessPaymentData).filter(
                BusinessPaymentData.business_payment_setting_id == existing_setting.id,
                BusinessPaymentData.is_deleted.is_(False)
            ).update({BusinessPaymentData.is_deleted: True})

            # Додаємо нові дані
            for pd in payment_data:
                payment_data_item = BusinessPaymentData(
                    business_payment_setting_id=existing_setting.id,
                    payment_method=pd.payment_method,
                    json_data=pd.json_data,
                    is_enabled=pd.is_enabled,
                )
                sess().add(payment_data_item)

        updated_setting = sess().query(BusinessPaymentSetting).filter(
            BusinessPaymentSetting.incust_account_id == incust_account_id
        ).one()
        sess().commit()
        return updated_setting
    else:
        new_setting = BusinessPaymentSetting(
            incust_account_id=incust_account_id,
            creator_id=creator_id,
            name=name,
            description=description,
            is_enabled=is_enabled,
        )
        sess().add(new_setting)
        sess().flush()  # Отримуємо ID перед додаванням пов'язаних даних
        
        # Додаємо дані про платіжні методи
        if payment_data:
            for pd in payment_data:
                payment_data_item = BusinessPaymentData(
                    business_payment_setting_id=new_setting.id,
                    payment_method=pd.payment_method,
                    json_data=pd.json_data,
                    is_enabled=pd.is_enabled,
                )
                sess().add(payment_data_item)
                
        sess().commit()
        return new_setting
