from db import db_func, sess
from db.models import Friend


@db_func
def get_user_friend(user_id: int, friend_id: int) -> Friend | None:
    query = sess().query(Friend)
    query = query.filter(Friend.user_id == user_id, Friend.friend_id == friend_id)
    friend = query.one_or_none()
    if friend:
        return friend
    query = sess().query(Friend)
    query = query.filter(Friend.user_id == friend_id, Friend.friend_id == user_id)
    return query.one_or_none()

