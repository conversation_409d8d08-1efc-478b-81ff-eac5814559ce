from db import db_func, sess
from db.models import GalleryItem, Gallery, MediaObject


@db_func
def get_product_gallery_items(product_id: int) -> list[tuple[GalleryItem, MediaObject]]:
    query = sess().query(GalleryItem, MediaObject)
    query = query.join(GalleryItem.media)
    query = query.filter(
        GalleryItem.gallery.has(
            Gallery.store_products.any(id=product_id)
        )
    )
    query = query.order_by(GalleryItem.position)
    return query.all()
