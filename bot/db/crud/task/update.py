from datetime import datetime

from sqlalchemy import select, update

from db import db_func, sess
from db.models import Task
from schemas import TaskStatusEnum


@db_func
def update_task(
        task_id: int,
        status: TaskStatusEnum,
        error: str | None = None,
):
    task = sess().query(Task).get(task_id)
    task.status = status
    task.change_date = datetime.utcnow()
    if status == TaskStatusEnum.DELETED:
        task.is_deleted = True
    if status == TaskStatusEnum.CANCELED:
        task.cancel_date = datetime.utcnow()
    if error:
        task.error = error
    sess().commit()
    return task


@db_func
def update_tasks_for_group(
        group_id: int,
        status: TaskStatusEnum,
        data: list[int] | None = None,
        error: str | None = None,
):

    query_for_update = select(Task.id, Task.status).where(
        Task.group_id == group_id,
        Task.is_deleted.is_(False),
    )

    if status == TaskStatusEnum.CANCELED:
        query_for_update = query_for_update.where(Task.status.in_([TaskStatusEnum.PENDING, TaskStatusEnum.PROCESSING]))

    if data:
        query_for_update = query_for_update.where(Task.id.in_(data))

    tasks_for_update = sess().execute(query_for_update).fetchall()

    if not tasks_for_update:
        return []

    update_query = update(Task).where(
        Task.id.in_([task.id for task in tasks_for_update]))

    if status == TaskStatusEnum.CANCELED:
        update_query = update_query.values(cancel_date=datetime.utcnow())

    if error:
        update_query = update_query.values(error=error)

    update_query = update_query.values(status=status, change_date=datetime.utcnow())

    sess().execute(update_query)
    sess().commit()

    return tasks_for_update
