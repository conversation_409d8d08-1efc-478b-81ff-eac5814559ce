from sqlalchemy import distinct, func, select, text
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

from db import db_func, sess
from db.models import Task, Group, Scope
from schemas import TaskStatusEnum, TaskTypeTaskEnum


@db_func
def get_task(
        task_id: int,
        profile_id: int,
        user_id: int | None = None
):
    stmt = select(Task)
    stmt = stmt.where(Task.id == task_id)

    stmt = stmt.join(Task.group)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(Task.group_id == profile_id)

    stmt = stmt.group_by(Task.id)

    return sess().scalar(stmt)


@db_func
def get_admin_task_list(
        profile_id: int,
        user_id: int,
        statuses: list[TaskStatusEnum],
        offset: int | None = None,
        limit: int | None = None,
        include: list[int] | None = None,
        exclude: list[int] | None = None,
        need_check_access: bool = False,
        is_count: bool = False,
        object_id: int | None = None,
        type_tasks: list[TaskTypeTaskEnum] | None = None,
) -> list[Row] | int:
    if is_count:
        stmt: Select = select(func.count(distinct(Task.id)))
    else:
        stmt: Select = select(
            Task.id,
            *Scope.allowed_scopes_list(
                "read",
                # "edit",
                object_name="task",
                target="user",
                target_id=user_id,
                available_data={
                    "profile_id": profile_id,
                    "task_id": Task.id,
                }
            ),
            Task.type,
            Task.type_task,
            Task.ai_model,
            Task.status,
            Task.change_date,
            Task.object_id,
            Task.time_created,
            Task.json_data,
            Task.start_date,
            Task.end_date,
            Task.cancel_date,
        )

        stmt = stmt.distinct()

    stmt = stmt.join(Group, Task.group_id == Group.id)

    stmt = stmt.where(Task.is_deleted.is_(False))
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")

    if statuses:
        stmt = stmt.where(Task.status.in_(statuses))

    if need_check_access:
        stmt = stmt.where(text("read_allowed IS TRUE"))

    if exclude:
        stmt = stmt.where(Task.id.not_in(exclude))

    if include:
        stmt = stmt.where(Task.id.in_(include))

    if object_id:
        stmt = stmt.where(Task.object_id == object_id)

    if type_tasks:
        stmt = stmt.where(Task.type_task.in_(type_tasks))

    if is_count:
        return sess().scalar(stmt)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    stmt = stmt.order_by(Task.change_date.desc())

    return sess().execute(stmt).fetchall()
