import logging
from datetime import datetime
from typing import Any, Type

from fastapi import HTT<PERSON>Exception
from sqlalchemy import Integer, cast, exists, func, join, literal, not_, select, update
from sqlalchemy.engine import Row
from sqlalchemy.exc import SQLAlchemyError
from starlette import status as http_status

from db import db_func, sess
from exceptions.task import (AdminTaskTypeError, ObjectForProcessNotFoundError, TaskTypeError)
from db.models import (
    Brand, Group, InvoiceTemplate, Store, StoreBanner, StoreProduct, Task, User, VirtualManager,
    VirtualManagerStep,
)
from schemas import (
    CreateTaskSchema, TaskCrateObjectResponse, TaskCreateObject, TaskMode, TaskStatusEnum, TaskTypeTaskEnum,
)

debugger = logging.getLogger("debugger.task")


def find_object_by_id(objects: list[TaskCreateObject], target_id: int) -> TaskCreateObject | None:
    if not objects:
        return None
    return next((obj for obj in objects if obj.id == target_id), None)


@db_func
def create_product_tasks(
        user: User,
        group: Group,
        data: CreateTaskSchema
) -> tuple[list[Any], list[Any], list[Task]]:

    if data.type_task != TaskTypeTaskEnum.PRODUCT_IMAGE:
        raise TaskTypeError(data.type_task.value)

    chunk_size: int = 10
    replacement = []
    created = []
    created_tasks = []

    db_object, products_query, _ = _get_objects_query(
        group, data.mode, data.objects, data.type_task
    )

    offset = 0
    created = []
    while True:
        try:
            products_chunk = sess().execute(products_query.offset(offset).limit(chunk_size)).fetchall()
            if not products_chunk:
                break

            replacement_ = _replace_task_statuses(data, db_object, [obj.id for obj in products_chunk])
            replacement.extend(replacement_)

            created_tasks_ = _create_new_tasks(data, group, user, products_chunk)
            created_tasks.extend(created_tasks_)

            sess().flush()  # Щоб отримати ID для нових tasks

            for task in created_tasks_:
                update_query = update(StoreProduct).where(
                    StoreProduct.id == task.object_id
                ).values(task_id=task.id)
                sess().execute(update_query)

            sess().commit()

            for task in created_tasks_:
                created.append(
                    TaskCrateObjectResponse(
                        task_id=task.id,
                        object_id=task.object_id,
                        name=task.json_data.get("name"),
                        type_task=task.type_task,
                    )
                )
            offset += chunk_size
        except SQLAlchemyError as e:
            sess().rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error processing chunk: {str(e)}"
            )

    return created, replacement, created_tasks


@db_func
def create_task(
        user: User,
        group: Group,
        data: CreateTaskSchema,
) -> tuple[list[Any], list[Any], list[Task]]:

    _validate_create_task_data(data.objects, data.type_task)

    db_object, objects_query, db_object_task_id = _get_objects_query(
        group, data.mode, data.objects, data.type_task
    )

    objects_for_process = sess().execute(objects_query).fetchall()

    if not objects_for_process:
        raise ObjectForProcessNotFoundError(data.type_task.value, f"{data.mode=} {data.type_task=} {data.objects=}")

    replacement = _replace_task_statuses(data, db_object, [obj.id for obj in objects_for_process])

    created_tasks = _create_new_tasks(data, group, user, objects_for_process)

    sess().flush()  # Щоб отримати ID для нових tasks

    created = []
    for task in created_tasks:

        if task.json_data.get("banner_id"):
            db_object = VirtualManagerStep
        if data.type_task in (TaskTypeTaskEnum.PROFILE_IMAGE, TaskTypeTaskEnum.PROFILE_LOGO):
            db_object = Group

        update_query = update(db_object).where(
            db_object.id == task.object_id
        ).values({db_object_task_id: task.id})

        sess().execute(update_query)

        created.append(
            TaskCrateObjectResponse(
                task_id=task.id,
                object_id=task.object_id,
                name=task.json_data.get("name"),
                type_task=task.type_task,
            )
        )

    sess().commit()

    return created, replacement, created_tasks


def _validate_create_task_data(
        objects: list[TaskCreateObject], type_task: TaskTypeTaskEnum
) -> None:

    if type_task == TaskTypeTaskEnum.PRODUCT_IMAGE:
        raise AdminTaskTypeError(type_task.value)

    # if type_task == TaskTypeTaskEnum.STORE_BANNER:
    #     if not objects or len(objects) == 0:
    #         raise AdminTaskObjectsRequiredError()


def _get_objects_query(group: Group, mode: TaskMode, objects: list[TaskCreateObject], type_task: TaskTypeTaskEnum):
    obj_ids = [obj.id for obj in objects] if objects else []
    db_object_task_id = None

    match type_task:
        case TaskTypeTaskEnum.PRODUCT_IMAGE:
            db_object = StoreProduct
            objects_query = select(db_object.id, db_object.name, db_object.description).join(Brand).join(Group).where(
                Group.id == group.id,
                Group.status == "enabled",
                db_object.is_deleted.is_(False),
            )
        case TaskTypeTaskEnum.STORE_IMAGE | TaskTypeTaskEnum.STORE_BANNER:
            db_object = Store
            objects_query = select(Store.id, Store.name, Store.description).join(Brand).join(Group).where(
                Group.id == group.id,
                Group.status == "enabled",
                db_object.is_deleted.is_(False),
            )
        case TaskTypeTaskEnum.PROFILE_IMAGE | TaskTypeTaskEnum.PROFILE_LOGO:
            db_object = Brand
            objects_query = select(Group.id, Brand.name, literal(None).label("description")).join(Group).where(
                Group.id == group.id,
                Group.status == "enabled"
            )
            db_object_task_id = "logo_task_id" if type_task == TaskTypeTaskEnum.PROFILE_LOGO else "image_task_id"
        case TaskTypeTaskEnum.VM_STEP_IMAGE:
            db_object = VirtualManagerStep
            objects_query = select(
                db_object.id, func.concat(VirtualManager.name, '  [', VirtualManagerStep.position, ']').label("name"),
                db_object.text.label(
                    'description')
            ).join(
                VirtualManager
            ).join(Group).where(
                Group.id == group.id,
                Group.status == "enabled",
                db_object.is_deleted.is_(False),
            )
        case TaskTypeTaskEnum.INVOICE_TEMPLATE_IMAGE:
            db_object = InvoiceTemplate
            objects_query = select(
                db_object.id, db_object.title.label('name'), db_object.description
            ).join(Group).where(
                Group.id == group.id,
                Group.status == "enabled",
                db_object.is_deleted.is_(False),
            )
        case _:
            raise AdminTaskTypeError(type_task.value)

    if type_task not in (TaskTypeTaskEnum.PROFILE_IMAGE, TaskTypeTaskEnum.PROFILE_LOGO):
        if mode == TaskMode.WITHOUT_IMAGE:
            if type_task == TaskTypeTaskEnum.STORE_BANNER:
                banners_query = select(1).where(StoreBanner.store_id == Store.id)
                objects_query = objects_query.where(not_(exists(banners_query)))
            else:
                objects_query = objects_query.where(db_object.media_id.is_(None))
        elif mode == TaskMode.SELECTED:
            objects_query = objects_query.where(db_object.id.in_(obj_ids))
        else:
            ...
    db_object_task_id = "task_id" if not db_object_task_id else db_object_task_id
    debugger.debug(f"{db_object_task_id=}")
    return db_object, objects_query, db_object_task_id


def _create_new_tasks(
        data: CreateTaskSchema,
        group: Group,
        user: User,
        objects_for_process: list[Row]
) -> list[Task]:
    created_tasks = []

    def create_task_object(obj_id: int, json_data: dict) -> None:
        task = Task(
            type=data.type,
            status=TaskStatusEnum.PENDING,
            type_task=data.type_task,
            ai_model=data.ai_model,
            group=group,
            user=user,
            object_id=obj_id,
            prompt=data.prompt,
            json_data={k: v for k, v in json_data.items() if v is not None}
        )
        sess().add(task)
        created_tasks.append(task)

    for obj in objects_for_process:
        description = obj.description or obj.name
        current_obj = find_object_by_id(data.objects, obj.id)

        if current_obj:
            if getattr(current_obj, "banners", None) and len(current_obj.banners) > 0:
                for banner in current_obj.banners:
                    if not banner.is_use_inner_description and banner.description:
                        description = ""
                    description = "\n".join((description or "", banner.description or "")).strip()
                    create_task_object(obj.id,
                        {"name": obj.name, "description": description, "url": banner.url, "banner_id": banner.id})
            else:
                if not current_obj.is_use_inner_description and current_obj.description:
                    description = ""
                description = "\n".join((description or "", current_obj.description or "")).strip()

                create_task_object(obj.id, {"name": obj.name, "description": description})
        else:
            create_task_object(obj.id, {"name": obj.name, "description": description})

    return created_tasks


def _replace_task_statuses(
        data: CreateTaskSchema, db_object: Type[Store | Brand | VirtualManagerStep | InvoiceTemplate],
        obj_ids: list[int],
):
    replacement = []
    do_query = True

    existing_tasks_query = (
        select(Task.id, Task.object_id, Task.json_data, Task.type_task)
        .select_from(
            join(Task, db_object, Task.object_id == db_object.id)
        )
        .where(
            Task.object_id.in_(obj_ids),
            Task.status.in_([TaskStatusEnum.PENDING, TaskStatusEnum.PROCESSING]),
            Task.type_task == data.type_task,
        )
    )

    if data.type_task == TaskTypeTaskEnum.STORE_BANNER and data.objects:
        banner_ids = []
        for obj in data.objects or []:
            for banner in obj.banners or []:
                banner_ids.append(banner.id)

        if banner_ids:
            existing_tasks_query = existing_tasks_query.where(
                cast(func.JSON_UNQUOTE(func.JSON_EXTRACT(Task.json_data, '$.banner_id')), Integer).in_(banner_ids)
            )
        else:
            do_query = False

    existing_tasks = sess().execute(existing_tasks_query).fetchall() if do_query else None

    if existing_tasks:
        upd_query = update(Task).where(
            Task.id.in_([existing_task.id for existing_task in existing_tasks]),
            Task.status.in_([TaskStatusEnum.PENDING, TaskStatusEnum.PROCESSING])
        ).values(
            status=TaskStatusEnum.REPLACEMENT,
            cancel_date=datetime.utcnow(),
            change_date=datetime.utcnow(),
        )

        sess().execute(upd_query)

        for existing_task in existing_tasks:
            replacement.append(
                TaskCrateObjectResponse(
                    task_id=existing_task.id,
                    object_id=existing_task.object_id,
                    name=existing_task.json_data["name"],
                    type_task=existing_task.type_task,
                )
            )
    return replacement
