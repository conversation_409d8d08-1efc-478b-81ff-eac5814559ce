import copy
from sqlalchemy import select

import schemas
from db import db_func, sess
from db.crud.scope.create import grand_scopes_to_created_object_sync
from db.crud.vm.helpers import process_vm_name_id
from db.models import (
    Group, Translation, User, VirtualManager, VirtualManagerInteractive,
    VirtualManagerStep,
)


@db_func
def create_vm(
        group: Group,
        data: schemas.AdminCreateVirtualManagerData,
        creator: User | None = None,
) -> VirtualManager:
    data.name_id = process_vm_name_id(data.name_id)

    vm = VirtualManager(
        group=group,
        creator=creator,
        **data.dict(
            exclude_none=True,
            exclude={"create_mode", "copy_id", "is_copy", "copy_exist_id"}
        ),
    )
    sess().add(vm)

    if creator:
        grand_scopes_to_created_object_sync(
            "vm",
            vm, creator,
            {
                "profile_id": group.id,
            }
        )

    sess().commit()
    return vm


@db_func
def copy_vm(
        group: Group,
        data: schemas.AdminCreateVirtualManagerData,
        copy_id: int | str,
        creator: User | None = None,
) -> VirtualManager:
    stmt = select(VirtualManager)
    if isinstance(copy_id, str):
        stmt = stmt.where(VirtualManager.name_id == copy_id)
    else:
        stmt = stmt.where(VirtualManager.id == copy_id)
    stmt = stmt.where(VirtualManager.is_deleted.is_(False))
    vm: VirtualManager = sess().scalar(stmt)
    if vm:
        data.name_id = process_vm_name_id(data.name_id)

        vm_copy = VirtualManager(
            group=group,
            creator=creator,
            allow_click_old_messages=vm.allow_click_old_messages,
            start_only_in_owner_profile=vm.start_only_in_owner_profile,
            on_start_delay=vm.on_start_delay,
            message_delay=vm.message_delay,
            is_reminder_enabled=vm.is_reminder_enabled,
            reminder_delay=vm.reminder_delay,
            reminds_count=vm.reminds_count,
            name=data.name,
            name_id=data.name_id,
        )
        sess().add(vm_copy)

        if creator:
            grand_scopes_to_created_object_sync(
                "vm",
                vm_copy, creator,
                {
                    "profile_id": group.id,
                }
            )

        sess().flush()

        smtp = select(VirtualManagerStep)
        smtp = smtp.where(VirtualManagerStep.virtual_manager_id == vm.id)
        smtp = smtp.where(VirtualManagerStep.is_deleted.is_(False))
        steps = sess().execute(smtp).scalars().all()

        smtp = select(Group)
        smtp = smtp.where(Group.id == vm.group_id)
        original_group = sess().scalar(smtp)

        if original_group.id == group.id:
            langs_list = original_group.langs_list
        else:
            langs_list = group.langs_list

        steps_copy = []
        for step in steps:
            step_copy = VirtualManagerStep(
                position=step.position,
                virtual_manager=vm_copy,
                text=step.text,
                media_id=step.media_id,
                reminder_mode=step.reminder_mode,
                reminder_delay=step.reminder_delay,
                reminds_count=step.reminds_count,
                task_id=step.task_id,
            )
            sess().add(step_copy)
            sess().flush()

            translations = create_vm_object_translation_copy(
                langs_list, "VirtualManagerStep", step.id, step_copy.id
            )
            for translation in translations:
                sess().add(translation)

            steps_copy.append(step_copy)

        def get_step_copy_by_position(
                pos: int, sts: list[VirtualManagerStep]
        ) -> VirtualManagerStep:
            for st in sts:
                if st.position == pos:
                    return st

        def get_step_by_id(
                step_id: int, sts: list[VirtualManagerStep]
        ) -> VirtualManagerStep:
            for st in sts:
                if st.id == step_id:
                    return st

        for step in steps:
            step_copy = get_step_copy_by_position(step.position, steps_copy)
            smtp = select(VirtualManagerInteractive)
            smtp = smtp.where(VirtualManagerInteractive.step_id == step.id)
            smtp = smtp.where(VirtualManagerInteractive.is_deleted.is_(False))
            interactives = sess().execute(smtp).scalars().all()
            for interactive in interactives:
                params = None
                if interactive.params:
                    params = copy.deepcopy(interactive.params)
                    vm_id = params.get("vm_id", None)
                    vm_step_id = params.get("vm_step_id", None)

                    if vm_id and vm_step_id:
                        if vm_id == vm.id:
                            orig_step = get_step_by_id(vm_step_id, steps)
                            if orig_step is not None:
                                position = orig_step.position
                                copy_st = get_step_copy_by_position(
                                    position, steps_copy
                                )
                                if copy_st is not None:
                                    params["vm_step_id"] = copy_st.id
                                    params["vm_id"] = vm_copy.id

                    buttons = params.get("button_actions", None)

                    if buttons:
                        for button in buttons:
                            btn_params = button.get("params", None)
                            if btn_params:
                                vm_id = btn_params.get("vm_id", None)
                                if vm_id and vm_id == vm.id:
                                    orig_step = get_step_by_id(
                                        button["params"]["vm_step_id"], steps
                                    )
                                    if orig_step:
                                        position = orig_step.position
                                        copy_st = get_step_copy_by_position(
                                            position, steps_copy
                                        )
                                        button["params"]["vm_id"] = vm_copy.id
                                        button["params"]["vm_step_id"] = copy_st.id

                        params["button_actions"] = buttons

                interactive_copy = VirtualManagerInteractive(
                    type=interactive.type,
                    subtype=interactive.subtype,
                    position=interactive.position,
                    step_id=step_copy.id,
                    params=params,
                )
                sess().add(interactive_copy)
                sess().flush()

                translations = create_vm_object_translation_copy(
                    langs_list, "VirtualManagerInteractive", interactive.id,
                    interactive_copy.id
                )
                for translation in translations:
                    sess().add(translation)

        sess().commit()
        return vm_copy


def create_vm_object_translation_copy(
        langs: list[str], object_name: str,
        object_id: int, copy_object_id
) -> list[Translation]:
    res = []
    for lang in langs:
        smtp = select(Translation)
        smtp = smtp.where(Translation.id == f"{object_name}-{lang}-{object_id}")
        tl = sess().scalar(smtp)
        if tl:
            tl_copy = Translation(
                obj_type=object_name,
                obj_id=copy_object_id,
                lang=lang,
                data=tl.data,
            )
            res.append(tl_copy)

    return res
