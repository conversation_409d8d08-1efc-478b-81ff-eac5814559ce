from sqlalchemy import distinct, func, or_, select
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

import schemas
from db import db_func, sess
from db.models import Group, Scope, VirtualManager
from db.types.operation import Operation


@db_func
def get_vm_by_id_and_profile_id(
        vm_id: int,
        profile_id: int
) -> VirtualManager | None:
    stmt = select(VirtualManager)
    stmt = stmt.where(VirtualManager.id == vm_id)

    stmt = stmt.join(VirtualManager.group)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(VirtualManager.is_deleted.is_(False))

    return sess().scalar(stmt)


@db_func
def get_admin_vm_list(
        profile_id: int,
        user_id: int,
        params: schemas.AdminListParams,
        operation: Operation = "list",
) -> list[Row]:
    stmt: Select

    available_data = {
        "profile_id": profile_id,
        "vm_id": VirtualManager.id,
    }

    if operation == "count":
        stmt = select(func.count(distinct(VirtualManager.id)))
        stmt = stmt.where(
            Scope.filter_for_action(
                "vm:read",
                "user", user_id,
                available_data,
            )
        )
    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="vm",
            target="user",
            target_id=user_id,
            available_data=available_data
        )

        stmt = select(
            VirtualManager.id,
            Group.id.label("profile_id"),
            VirtualManager.creator_id,
            VirtualManager.name,
            VirtualManager.name_id,
            VirtualManager.allow_click_old_messages,
            VirtualManager.start_only_in_owner_profile,
            VirtualManager.on_start_delay,
            VirtualManager.message_delay,
            VirtualManager.is_reminder_enabled,
            VirtualManager.reminder_delay,
            VirtualManager.reminds_count,
            VirtualManager.bot_hello_message_enabled,
            read_allowed, edit_allowed,
        )
        stmt = stmt.where(read_allowed.is_(True))

    stmt = stmt.join(VirtualManager.group)

    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")
    stmt = stmt.where(VirtualManager.is_deleted.is_(False))

    if params.search_text:
        params.search_text = params.search_text.strip()
        stmt = stmt.where(
            or_(
                VirtualManager.name.contains(params.search_text),
                VirtualManager.id == params.search_text,
                VirtualManager.name_id == params.search_text,
            )
        )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(VirtualManager.time_created.desc(), VirtualManager.id.desc())

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    return sess().execute(stmt).fetchall()


@db_func
def get_vm_by_vmc(vmc_id: int) -> VirtualManager:
    stmt = select(VirtualManager).where(VirtualManager.chats_with_users.any(id=vmc_id))
    return sess().scalar(stmt)


@db_func
def get_vm_by_id_or_name_id(vm_or_id: VirtualManager | int | str):
    if isinstance(vm_or_id, str) and not vm_or_id.isdecimal():
        vm = VirtualManager.get_sync(name_id=vm_or_id, is_deleted=False)
    elif isinstance(vm_or_id, VirtualManager):
        vm = vm_or_id if not vm_or_id.is_deleted else None
    else:
        vm = VirtualManager.get_sync(vm_or_id, is_deleted=False)
    return vm
