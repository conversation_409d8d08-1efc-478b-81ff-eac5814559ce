from typing import Iterable

from sqlalchemy import select, func
from sqlalchemy.sql import Select

from db import db_func, sess
from db.mixins import BaseDBModel
from db.models import Translation


def get_translations_for_langs_list_sync(
        object: BaseDBModel,
        langs_list: Iterable[str],
) -> tuple[str, Translation]:
    stmt: Select = select(
        func.regexp_substr(Translation.id, '(?<=-)[a-z]{2}(?=-)'),
        Translation,
    )
    stmt = stmt.where(Translation.id.in_(
        [f"{object.__class__.__name__}-{lang}-{object.id}" for lang in langs_list]
    ))
    return sess().execute(stmt).fetchall()


@db_func
def get_translations_for_langs_list(
        object: BaseDBModel,
        langs_list: list[str],
) -> tuple[str, Translation]:
    return get_translations_for_langs_list_sync(object, langs_list)
