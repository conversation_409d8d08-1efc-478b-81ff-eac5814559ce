from psutils.translator.models import Trans<PERSON><PERSON><PERSON>, TranslatorModel
from psutils.translator.models.types import TranslatorDict
from pydantic import BaseModel
from sqlalchemy import func, update
from typing import Iterable

from db.mixins import BaseDBModel
from .read import get_translations_for_langs_list_sync
from ... import sess
from ...models import Translation


def update_object_translations_sync(
        object: BaseDBModel,
        translations: dict[str, BaseModel | None],
) -> dict[str, Translation | None]:
    new_objects = []

    # noinspection PyTypeChecker
    translations_objects: dict[str, Translation | None] \
        = dict(get_translations_for_langs_list_sync(object, translations.keys()))

    for lang, new_data in translations.items():
        translation_obj = translations_objects.get(lang)

        if not new_data or not new_data.dict(exclude_none=True):
            if translation_obj:
                translation_obj.data = {}
            else:
                translations_objects[lang] = None
            continue

        new_data_dict = new_data.dict(exclude_unset=True)

        if translation_obj:
            for key, value in new_data_dict.items():
                if (
                        isinstance(value, dict) and
                        isinstance(translation_obj.data.get(key), dict)
                ):
                    for sub_key, sub_value in value.items():
                        translation_obj.data[key][sub_key] = sub_value
                else:
                    translation_obj.data[key] = value
            translation_obj.data.update(new_data_dict)
        else:
            translation_obj = Translation(
                obj_type=object.__class__.__name__,
                obj_id=object.id,
                lang=lang,
                data=new_data_dict,
            )
            translations_objects[lang] = translation_obj
            new_objects.append(translation_obj)

    sess().add_all(new_objects)
    return translations_objects


def clear_object_fields_translations_sync(
        object: BaseDBModel,
        *fields: str,
        exclude_langs: Iterable[str] | None = None
):
    if not fields:
        return

    stmt = update(Translation)
    stmt = stmt.execution_options(synchronize_session=False)

    json_set_params = []
    for field in fields:
        json_set_params.append(f"$.{field}")
        json_set_params.append(None)

    stmt = stmt.values(
        {
            "data": func.JSON_SET(
                Translation.data,
                *json_set_params
            )
        }
    )
    stmt = stmt.where(Translation.obj_type == object.__class__.__name__)
    stmt = stmt.where(Translation.obj_id == object.id)

    if exclude_langs:
        stmt = stmt.where(Translation.lang.not_in(exclude_langs))

    sess().execute(stmt)


def clear_object_updated_fields_translations_sync(
        object: BaseDBModel,
        data: dict,
        exclude_langs: list[str],
):
    translator_model = TranslatorModel.detect_model(object.__class__.__name__)

    clear_auto_translations_fields = set()
    clear_all_translations_fields = set()

    field_name: str
    field: TranslatorField
    for field_name, field in translator_model.fields.items():
        if field_name in data:

            if field_value := data.get(field_name):
                if isinstance(field.type, TranslatorDict):
                    for key in field.type.keys:
                        if key_value := field_value.get(key):
                            if key_value != (getattr(object, field_name) or {}).get(
                                    key
                            ):
                                clear_auto_translations_fields.add(
                                    f"{field_name}.{key}"
                                )
                        else:
                            clear_all_translations_fields.add(f"{field_name}.{key}")
                elif field_value != getattr(object, field_name):
                    clear_auto_translations_fields.add(field_name)
            else:
                clear_all_translations_fields.add(field_name)

    if clear_auto_translations_fields:
        clear_object_fields_translations_sync(
            object, *clear_auto_translations_fields,
            exclude_langs=exclude_langs,
        )
    if clear_all_translations_fields:
        clear_object_fields_translations_sync(
            object, *clear_all_translations_fields,
        )
