from sqlalchemy import delete

import schemas
from db import db_func, sess
from db.models import UserData


@db_func
def delete_user_data_by_target_and_type(
        user_id: int,
        target: schemas.UserDataTarget,
        target_id: int,
        type: str
) -> bool:
    sess().execute(
        delete(UserData)
        .where(
            UserData.user_id == user_id,
            UserData.target == target,
            getattr(UserData, f"{target.value}_id") == target_id,
            UserData.type == type
        )
    )
    sess().commit()
    return True
