from sqlalchemy import desc, select, text, union_all

import schemas
from db import db_func, sess
from db.crud.chat.read import get_crm_chat_list_statement
from db.crud.crm_ticket.read import get_crm_ticket_list_statement
from db.crud.helpers.crm import get_inbox_status_sort_value
from db.crud.invoice.read import get_crm_invoice_list_statement
from db.crud.review.read import get_crm_review_list_statement
from db.crud.store.order.read import get_crm_order_list_statement
from db.crud.text_notification.read import get_crm_text_notification_list_statement
from db.types.operation import Operation
from schemas import CursorDirection
from ..ewallet_external_payment.read import get_crm_ewallet_ext_payment_list_statement
from ..scope.read import check_access_to_action_sync

EXCLUDE_ITEM_STATUSES = {
    schemas.InboxType.ORDER: {
        schemas.InboxStatus.NEW: [],
        schemas.InboxStatus.IN_PROGRESS: [
            "open_unconfirmed",
        ],
        schemas.InboxStatus.DELIVERING: [
            "open_unconfirmed",
            "open_confirmed",
            "wait_for_ship",
        ],
        schemas.InboxStatus.RECENT: [
            "open_unconfirmed",
            "open_confirmed",
            "shipped",
            "in_transit",
            "delivered"
        ]
    },
    schemas.InboxType.TICKET: {
        schemas.InboxStatus.NEW: [],
        schemas.InboxStatus.IN_PROGRESS: [
            "open_unconfirmed",
        ],
        schemas.InboxStatus.DELIVERING: [
            "open_unconfirmed",
            "oopen_confirmed",
        ],
        schemas.InboxStatus.RECENT: [
            "open_unconfirmed",
            "open_confirmed",
        ]
    }
}

ITEM_STATUSES = {
    schemas.InboxType.ORDER: {
        schemas.InboxStatus.NEW: [
            "open_unconfirmed",
        ],
        schemas.InboxStatus.IN_PROGRESS: [
            "open_confirmed",
            "wait_for_ship",
        ],
        schemas.InboxStatus.DELIVERING: [
            "shipped",
            "in_transit",
            "delivered"
        ],
        schemas.InboxStatus.RECENT: [
            "closed",
            "canceled",
        ]
    },
    schemas.InboxType.TICKET: {
        schemas.InboxStatus.NEW: [
            "open_unconfirmed",
        ],
        schemas.InboxStatus.IN_PROGRESS: [
            "open_confirmed",
        ],
        schemas.InboxStatus.DELIVERING: [],
        schemas.InboxStatus.RECENT: [
            "closed",
            "canceled",
        ]
    }
}


@db_func
def get_crm_inbox(
        user_id: int,
        params: schemas.InboxParams | None = None,
        operation: Operation = "list",
        cursor: schemas.InboxCursor | None = None,
):
    if not params:
        params = schemas.InboxParams()

    if not cursor:
        cursor = params.cursor

    if cursor and cursor.direction != CursorDirection.NEXT:
        raise ValueError("Cursor direction PREV is not supported")

    statements = []

    params_statuses = params.statuses or None
    params_types = params.types or None

    is_platform_admin = check_access_to_action_sync(
        "platform:admin",
        "user", user_id,
    )

    if not params_types or schemas.InboxType.ORDER in params_types:
        if params_statuses:
            all_order_statuses = sum(
                map(
                    lambda x: ITEM_STATUSES[schemas.InboxType.ORDER][x], params_statuses
                ), []
            )
        else:
            all_order_statuses = None

        order_statuses = [
            el for el in (
                "open_unconfirmed",
                "open_confirmed",
                "wait_for_ship",
                "in_transit",
                "delivered",
                "closed",
                "canceled"
            )
            if (
                    (not cursor or (el not in
                                    EXCLUDE_ITEM_STATUSES[schemas.InboxType.ORDER][
                                        cursor.status])) and
                    (all_order_statuses is None or el in all_order_statuses)
            )
        ]

        if order_statuses:
            statements.append(
                get_crm_order_list_statement(
                    user_id, schemas.CRMOrderListParams(
                        profile_ids=params.profile_ids,
                        store_ids=params.store_ids,
                        search_text=params.search_text,
                        statuses=order_statuses,
                        limit=0,  # remove limit,
                        is_paid=params.is_paid,
                    ), operation,
                    inbox_cursor=cursor,
                    for_inbox=True,
                    is_platform_admin=is_platform_admin,
                )
            )

    if not params_types or schemas.InboxType.INVOICE in params_types:
        invoice_params = schemas.CRMInvoiceListParams(
            profile_ids=params.profile_ids,
            search_text=params.search_text,
            statuses=["payed"],
            limit=0,  # remove limit
        )

        hide_invoices = False
        if params_statuses:
            if (schemas.InboxStatus.NEW in params_statuses and
                    schemas.InboxStatus.RECENT in params_statuses):
                invoice_params.is_read = None
            elif schemas.InboxStatus.NEW in params_statuses:
                invoice_params.is_read = False
            elif schemas.InboxStatus.RECENT in params_statuses:
                invoice_params.is_read = True
            else:
                hide_invoices = True

        if not hide_invoices:
            statements.append(
                get_crm_invoice_list_statement(
                    user_id, invoice_params, operation,
                    inbox_cursor=cursor,
                    for_inbox=True,
                    is_platform_admin=is_platform_admin,
                )
            )

    if not params_types or schemas.InboxType.TICKET in params_types:
        if params_statuses:
            all_ticket_statuses = sum(
                map(
                    lambda x: ITEM_STATUSES[schemas.InboxType.TICKET][x],
                    params_statuses
                ), []
            )
        else:
            all_ticket_statuses = None

        ticket_statuses = [
            el for el in (
                "open_unconfirmed",
                "open_confirmed",
                "closed",
                "canceled"
            )
            if (
                    (not cursor or (el not in
                                    EXCLUDE_ITEM_STATUSES[schemas.InboxType.TICKET][
                                        cursor.status])) and
                    (all_ticket_statuses is None or el in all_ticket_statuses)
            )
        ]
        if ticket_statuses:
            statements.append(
                get_crm_ticket_list_statement(
                    user_id, schemas.CRMTicketListParams(
                        profile_ids=params.profile_ids,
                        search_text=params.search_text,
                        statuses=ticket_statuses,
                        limit=0  # remove limit,
                    ), operation,
                    inbox_cursor=cursor,
                    for_inbox=True,
                    is_platform_admin=is_platform_admin,
                )
            )

    if not params_types or schemas.InboxType.REVIEW in params_types:
        review_params = schemas.CRMReviewListParams(
            profile_ids=params.profile_ids,
            search_text=params.search_text,
            limit=0,  # remove limit
        )

        hide_reviews = False
        if params_statuses:
            statuses = []
            for status in params_statuses:
                if status == schemas.InboxStatus.NEW:
                    statuses.append(schemas.CRMReviewReadStatusEnum.NOT_READ)
                elif status == schemas.InboxStatus.RECENT:
                    statuses.append(schemas.CRMReviewReadStatusEnum.READ)
            if statuses:
                review_params.statuses = statuses

            if not statuses:
                hide_reviews = True

        if not hide_reviews:
            statements.append(
                get_crm_review_list_statement(
                    user_id, review_params, operation,
                    inbox_cursor=cursor,
                    for_inbox=True,
                    is_platform_admin=is_platform_admin,
                )
            )

    if not params_types or schemas.InboxType.CHAT in params_types:
        chat_params = schemas.CRMChatListParams(
            profile_ids=params.profile_ids,
            search_text=params.search_text,
            limit=0,  # remove limit
        )

        hide_chats = False
        if params_statuses:
            statuses = []
            for status in params_statuses:
                if status == schemas.InboxStatus.NEW:
                    statuses.append(schemas.CRMChatPendingStatusEnum.PENDING)
                elif status == schemas.InboxStatus.RECENT:
                    statuses.append(schemas.CRMChatPendingStatusEnum.NOT_PENDING)
            if statuses:
                chat_params.statuses = statuses

            if not statuses:
                hide_chats = True

        if not hide_chats:
            statements.append(
                get_crm_chat_list_statement(
                    user_id, chat_params, operation,
                    inbox_cursor=cursor,
                    for_inbox=True,
                    is_platform_admin=is_platform_admin,
                )
            )

    if not params_types or schemas.InboxType.TEXT_NOTIFICATION in params_types:
        notification_params = schemas.CRMTextNotificationListParams(
            profile_ids=params.profile_ids,
            search_text=params.search_text,
            limit=0,  # remove limit
        )

        hide_notifications = False
        if params_statuses:
            statuses = []
            for status in params_statuses:
                if status == schemas.InboxStatus.NEW:
                    statuses.append(schemas.CRMTextNotificationReadStatusEnum.NOT_READ)
                elif status == schemas.InboxStatus.RECENT:
                    statuses.append(schemas.CRMTextNotificationReadStatusEnum.READ)
            if statuses:
                notification_params.statuses = statuses

            if not statuses:
                hide_notifications = True

        if not hide_notifications:
            statements.append(
                get_crm_text_notification_list_statement(
                    user_id, notification_params, operation,
                    inbox_cursor=cursor,
                    for_inbox=True,
                    is_platform_admin=is_platform_admin,
                )
            )

    if not params_types or schemas.InboxType.EWALLET_EXT_PAYMENT in params_types:
        ewallet_ext_payment_params = schemas.CRMEwalletExtPaymentListParams(
            profile_ids=params.profile_ids,
            search_text=params.search_text,
            limit=0,
        )

        hide_ewallet_ext_payments = False
        if params_statuses:
            statuses = []
            for status in params_statuses:
                if status == schemas.InboxStatus.NEW:
                    statuses.append(schemas.EWalletExternalPaymentStatus.CREATED)
                if status == schemas.InboxStatus.IN_PROGRESS:
                    statuses.append(schemas.EWalletExternalPaymentStatus.PENDING)
                elif status == schemas.InboxStatus.RECENT:
                    statuses.extend(
                        (
                            schemas.EWalletExternalPaymentStatus.CANCELLED,
                            schemas.EWalletExternalPaymentStatus.REJECTED,
                            schemas.EWalletExternalPaymentStatus.SUCCESS,
                        )
                    )
            if statuses:
                ewallet_ext_payment_params.statuses = statuses

            if not statuses:
                hide_ewallet_ext_payments = True

        if not hide_ewallet_ext_payments:
            statements.append(
                get_crm_ewallet_ext_payment_list_statement(
                    user_id, ewallet_ext_payment_params, operation,
                    inbox_cursor=cursor,
                    for_inbox=True,
                    is_platform_admin=is_platform_admin
                )
            )

    if not statements:
        match operation:
            case "list":
                return []
            case "count":
                return 0
            case "exists":
                return False

    for i, statement in enumerate(statements):
        statement = statement.order_by(text("inbox_status_sort"))
        statement = statement.order_by(desc(text("change_date")))
        statement = statement.order_by(desc(text("id")))
        if params.limit:
            statement = statement.limit(params.limit)

        statements[i] = statement

    if len(statements) == 1:
        query = statements[0].subquery()
    else:
        query = union_all(*statements).subquery()

    stmt = select(query)

    if cursor:
        stmt = stmt.where(
            text(f"inbox_status_sort >= {get_inbox_status_sort_value(cursor.status)}")
        )

    if operation == "exists":
        return sess().scalar(select(stmt.exists()))
    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(text("inbox_status_sort"))
    stmt = stmt.order_by(desc(text("change_date")))
    stmt = stmt.order_by(text("inbox_type_sort"))
    stmt = stmt.order_by(desc(text("id")))

    if params.limit:
        stmt = stmt.limit(params.limit)

    return sess().execute(stmt).fetchall()
