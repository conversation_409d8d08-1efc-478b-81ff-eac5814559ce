from datetime import datetime

from db import db_func, sess
from db.models import Customer


@db_func
def create_customer(
    lang: str,
    user_id: int,
    profile_id: int,
    marketing_consent: bool | None = None,
    is_accept_agreement: bool | None = None,
    no_commit: bool = False,
) -> Customer:
    customer = Customer(
        user_id=user_id,
        profile_id=profile_id,
        lang=lang,
        marketing_consent=marketing_consent,
        updated_date=datetime.utcnow(),
        is_accept_agreement=is_accept_agreement,
    )
    sess().add(customer)
    if not no_commit:
        sess().commit()

    return customer
