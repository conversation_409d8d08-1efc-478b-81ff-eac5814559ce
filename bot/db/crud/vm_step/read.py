from sqlalchemy import and_, func, select
from sqlalchemy.orm import aliased

from db import db_func, sess
from db.models import (
    MediaObject, Translation, VirtualManagerInteractive,
    VirtualManagerStep,
)
from utils.exec_time_logger import log_exec_time


@log_exec_time
@db_func
def get_vm_steps_data(
        vm_id: int,
) -> list[tuple[
    VirtualManagerStep,
    Translation | None,
    MediaObject | None,
    VirtualManagerInteractive | None,
    Translation | None,
]]:
    step_translation = aliased(Translation)
    interactive_translation = aliased(Translation)

    stmt = (
        select(
            VirtualManagerStep,
            step_translation,
            MediaObject,
            VirtualManagerInteractive,
            interactive_translation,
        ).outerjoin(VirtualManagerStep.media)
        .outerjoin(
            step_translation, and_(
                step_translation.id.startswith(f"{VirtualManagerStep.__name__}-"),
                step_translation.id.endswith(func.concat("-", VirtualManagerStep.id))
            )
        )
        .outerjoin(VirtualManagerStep.interactives).where(
            VirtualManagerStep.virtual_manager_id == vm_id,
            VirtualManagerStep.is_deleted.is_(False),
            # is not true used to include None values, where no interactives exists
            # for the step
            VirtualManagerInteractive.is_deleted.is_not(True)
        )

        .outerjoin(
            interactive_translation,
            and_(
                interactive_translation.id.startswith(
                    f"{VirtualManagerInteractive.__name__}-"
                ),
                interactive_translation.id.endswith(
                    func.concat("-", VirtualManagerInteractive.id)
                )
            )
        )
        .order_by(VirtualManagerStep.position, VirtualManagerInteractive.position)
    )

    # noinspection PyTypeChecker
    return sess().execute(stmt).fetchall()
