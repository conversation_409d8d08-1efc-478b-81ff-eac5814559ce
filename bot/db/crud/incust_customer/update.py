from datetime import datetime

from sqlalchemy import update

from db import db_func, sess
from db.models import IncustCustomer


@db_func
def update_incust_customer_push_token_info(
        incust_customer_id: int,
        push_token: str | None = None,
        push_token_wl_id: str | None = None,
        push_token_update_datetime: datetime | None = None,
):
    if push_token and push_token_wl_id and push_token_update_datetime:
        sess().execute(
            update(IncustCustomer).values(
                {
                    "push_token": push_token,
                    "push_token_wl_id": push_token_wl_id,
                    "push_token_update_datetime": push_token_update_datetime,
                }
            ).where(IncustCustomer.customer_id == incust_customer_id)
        )
    # commit anyway to release lock
    sess().commit()
