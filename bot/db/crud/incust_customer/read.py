from datetime import datetime

from sqlalchemy import select

from db import db_func, sess
from db.models import IncustCustomer


@db_func
def get_incust_customer_push_token_info(
        incust_customer_id: int,
) -> tuple[str | None, str | None, datetime | None]:
    stmt = select(
        IncustCustomer.push_token,
        IncustCustomer.push_token_wl_id,
        IncustCustomer.push_token_update_datetime,
    )
    stmt = stmt.where(IncustCustomer.id == incust_customer_id)
    stmt = stmt.with_for_update()
    result = sess().execute(stmt).fetchone()
    if not result:
        return None, None, None
    return result
