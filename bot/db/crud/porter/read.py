from datetime import datetime, timedelta

from sqlalchemy import or_

from core.ext.types import ActionType, StatusType
from db import db_func, sess
from db.models import Brand, DataPorter


@db_func
def get_active_porters(profile_id: str) -> list[DataPorter]:
    # one_week_ago = datetime.now() - timedelta(weeks=1)
    query = (
        sess().query(DataPorter)
        .join(Brand, Brand.id == DataPorter.brand_id)
        .filter(
            Brand.group_id == profile_id,
            DataPorter.is_read == 0,
            # or_(
            #     DataPorter.start_time.is_(None),
            #     DataPorter.start_time >= one_week_ago
            # )
        ).order_by(DataPorter.time_created.desc()).limit(50)
    )

    return query.all()


@db_func
def get_current_tasks(profile_id: str, action_type: ActionType) -> list[DataPorter]:
    query = (
        sess().query(DataPorter)
        .join(<PERSON>, Brand.id == DataPorter.brand_id)
        .filter(
            Brand.group_id == profile_id,
            DataPorter.status.not_in([StatusType.ERROR, StatusType.DONE]),
            DataPorter.action_type == action_type,
        )
    )

    return query.all()
