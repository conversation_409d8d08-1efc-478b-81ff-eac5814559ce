from sqlalchemy import select

from db import db_func, sess
from db.models import Tag, TagsUsersAssociation


@db_func
def get_user_tag_names(group_id: int, user_id: int) -> list[str]:
    stmt = select(
        Tag.name
    )
    stmt = stmt.join(TagsUsersAssociation)
    stmt = stmt.where(
        Tag.group_id == group_id,
        TagsUsersAssociation.telegramuser_id == user_id
    )

    stmt = stmt.order_by(TagsUsersAssociation.id)

    return sess().scalars(stmt).all()
