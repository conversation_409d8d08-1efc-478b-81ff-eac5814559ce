from sqlalchemy import delete, insert, select

from db import db_func, sess
from db.models import Tag, TagsUsersAssociation, User


@db_func
def update_user_tags_in_group(
        user: User,
        group_id: int,
        add_tags: list[str] | None = None,
        remove_tags: list[str] | None = None,
):
    db_tags_data = sess().execute(
        select(
            Tag.id,
            Tag.name,
            Tag.users.any(id=user.id)
        ).where(
            Tag.group_id == group_id,
            Tag.handle.in_({*(add_tags or []), *(remove_tags or [])})
        )
    ).fetchall()

    tags_data = {
        tag_name: {
            "id": tag_id,
            "is_added_to_user": is_added_to_user
        } for tag_name, tag_id, is_added_to_user in db_tags_data
    }

    if add_tags:
        create_tags = []
        add_association_to_tags = []
        for add_tag in add_tags:
            if remove_tags and add_tag in remove_tags:
                continue

            if add_tag in tags_data:
                if tags_data[add_tag]["is_added_to_user"]:
                    continue
                add_association_to_tags.append(tags_data[add_tag]["id"])
            else:
                create_tags.append(
                    Tag(
                        handle=add_tag,
                        group_id=group_id,
                        users=[user]
                    )
                )
        sess().add_all(create_tags)
        if add_association_to_tags:
            sess().execute(
                insert(TagsUsersAssociation).values(
                    [
                        {
                            "tag_id": tag_id,
                            "telegramuser_id": user.id,
                        }
                        for tag_id in add_association_to_tags
                    ]
                ).execution_options(
                    synchronize_session="fetch"
                )
            )

    if remove_tags:
        remove_association_from_tags = [
            tags_data[remove_tag]["id"]
            for remove_tag in remove_tags
            if remove_tag in tags_data and
               tags_data[remove_tag]["is_added_to_user"]
        ]
        sess().execute(
            delete(TagsUsersAssociation).where(
                TagsUsersAssociation.telegramuser_id == user.id,
                TagsUsersAssociation.tag_id.in_(remove_association_from_tags)
            ).execution_options(synchronize_session="fetch")
        )

    sess().commit()
