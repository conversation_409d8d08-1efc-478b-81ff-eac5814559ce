from select import select

from fastapi import HTTPException
from sqlalchemy import or_, select
from sqlalchemy.engine import Row
from sqlalchemy.orm import aliased, joinedload
from sqlalchemy.sql import Select, label

from db import db_func, sess
from db.helpers import get_query_by_operation, order_by_slice_and_result
from db.models import (
    Brand, Group, InvoiceTemplate, InvoiceTemplateItem, MediaObject,
    ObjectPaymentSettings, PaymentSettings,
)
from db.types.operation import Operation
from schemas import InvoiceTemplatePaymentModeEnum


@db_func
def get_invoice_template_items(
        invoice_template_id: int, operation: Operation = "list"
) -> list[InvoiceTemplateItem]:
    query = get_query_by_operation(InvoiceTemplateItem, operation)
    query = query.join(InvoiceTemplate)
    query = query.filter(
        InvoiceTemplateItem.invoice_template_id == invoice_template_id,
        InvoiceTemplateItem.is_deleted.is_(False),
        InvoiceTemplate.payment_mode == InvoiceTemplatePaymentModeEnum.ITEMS,
    )
    return order_by_slice_and_result(
        query, None, None, InvoiceTemplateItem.id, operation
    )


@db_func
def get_invoice_template_item(invoice_template_item_id: int) -> InvoiceTemplateItem:
    return sess().query(InvoiceTemplateItem).filter(
        InvoiceTemplateItem.id == invoice_template_item_id
    ).first()


@db_func
def get_invoice_templates(
        group_id: int,
        position: int = 0,
        limit: int = None,
        operation: str = "list",
        search_text: str | None = None,
        excluded_invoice_template_ids: list[int] | None = None,
        currency: str | None = None,
) -> list[InvoiceTemplate] | int:
    media_object = aliased(MediaObject)

    query = get_query_by_operation(InvoiceTemplate, operation)
    query = query.filter(
        InvoiceTemplate.group_id == group_id, InvoiceTemplate.is_deleted.is_(False)
    )
    if operation == "count":
        return query.scalar() - position

    if search_text:
        query = query.filter(
            or_(
                InvoiceTemplate.title.contains(search_text),
                InvoiceTemplate.description.contains(search_text),
                InvoiceTemplate.currency == search_text,
            )
        )

    if excluded_invoice_template_ids:
        query = query.filter(
            InvoiceTemplate.id.notin_(excluded_invoice_template_ids)
        )

    if currency:
        query = query.filter(InvoiceTemplate.currency == currency)

    query = query.outerjoin(media_object, InvoiceTemplate.media_id == media_object.id)

    query = query.with_entities(
        InvoiceTemplate.id,
        InvoiceTemplate.title,
        label("photo_url", media_object.url),
        InvoiceTemplate.is_deleted,
        InvoiceTemplate.group_id,
        InvoiceTemplate.currency,
    )

    return order_by_slice_and_result(
        query, position, limit, InvoiceTemplate.id.desc(), operation
    )


@db_func
def get_invoice_template(invoice_template_id: int, profile_id: int) -> InvoiceTemplate:
    stmt: Select = (
        select(InvoiceTemplate)
        .join(Group, InvoiceTemplate.group_id == Group.id)
        .options(joinedload(InvoiceTemplate.group))
        .where(
            Group.status == "enabled",
            Group.id == profile_id,
            InvoiceTemplate.id == invoice_template_id,
            InvoiceTemplate.is_deleted.is_(False)
        )
    )

    result = sess().execute(stmt)
    return result.scalar_one_or_none()


@db_func
def get_object_payment_data(
        profile_id: int, invoice_template_id: int | None = None,
        store_id: int | None = None, ) -> \
        list[Row]:
    stmt: Select = (select(ObjectPaymentSettings, PaymentSettings)
    .join(
        PaymentSettings, ObjectPaymentSettings.payment_settings_id == PaymentSettings.id
    )
    .join(Brand, PaymentSettings.brand_id == Brand.id)
    .join(Group, Brand.group_id == Group.id)
    .where(
        Group.status == "enabled",
        Group.id == profile_id,
        ObjectPaymentSettings.is_deleted.is_(False),
        PaymentSettings.is_deleted.is_(False),
        PaymentSettings.is_enabled.is_(True),
    ))

    if invoice_template_id:
        stmt = stmt.where(
            ObjectPaymentSettings.invoice_template_id == invoice_template_id,
            PaymentSettings.is_online.is_(True),
        )
    elif store_id:
        stmt = stmt.where(ObjectPaymentSettings.store_id == store_id, )
    else:
        raise HTTPException(
            status_code=400, detail="invoice_template_id or store_id must be specified"
        )

    result = sess().execute(stmt)
    return result.fetchall()
