from sqlalchemy import and_, select

import schemas
from config import ALL_PAYMENT_METHODS
from db import db_func, sess
from db.models import (
    Brand, ClientWebPage, Group, InvoiceTemplate, InvoiceTemplateItem,
    ObjectPaymentSettings,
    PaymentSettings,
)
from schemas import AdminInvoicePaymentInvoiceTemplate
from schemas.payment_settings.schemas_add import ObjectPaymentSettingsTarget


@db_func
def create_invoice_template(
        data: schemas.AdminCreateInvoiceTemplate, profile_id: int
) -> (
        InvoiceTemplate):

    client_web_pages_ids = data.client_web_pages or []
    items = data.items or []

    invoice_template_data = data.dict(exclude={"items", "image", 'client_web_pages'})
    invoice_template_data["group_id"] = profile_id

    template_data = {k: v for k, v in invoice_template_data.items() if
                     k != 'payment_data'}
    invoice_template = InvoiceTemplate(**template_data)
    sess().add(invoice_template)
    sess().flush()

    if client_web_pages_ids:
        client_web_pages_objects = sess().query(ClientWebPage).filter(
            and_(
                ClientWebPage.id.in_(client_web_pages_ids),
                ClientWebPage.group_id == profile_id
            )
        ).all()

        invoice_template.client_web_pages.extend(client_web_pages_objects)

    for item_data in items:
        invoice_template_data = item_data.dict()
        invoice_template_data["invoice_template_id"] = invoice_template.id

        invoice_template_data["price"] = round(invoice_template_data["price"] * 100)

        invoice_template_item = InvoiceTemplateItem(**invoice_template_data)
        sess().add(invoice_template_item)

    sess().commit()

    return invoice_template


@db_func
def create_invoice_template_item(
        invoice_template_id: int, data: schemas.AdminCreateInvoiceTemplateItemSchema
) -> (
        InvoiceTemplate):
    invoice_template_data = data.dict()
    invoice_template_data["invoice_template_id"] = invoice_template_id

    invoice_template_data["price"] = round(invoice_template_data["price"] * 100)

    invoice_template_item = InvoiceTemplateItem(**invoice_template_data)
    sess().add(invoice_template_item)
    sess().flush()

    sess().commit()

    return invoice_template_item


@db_func
def create_object_payment_settings_for_object(
        profile_id: int,
        invoice_template_id: int | None = None,
        store_id: int | None = None,
        payment_data: list[AdminInvoicePaymentInvoiceTemplate] | None = None
):

    if not payment_data:
        payment_data = []

    payments_settings = sess().execute(
        select(PaymentSettings).join(
            Group, Group.id == profile_id
        ).join(
            Brand, Brand.group_id == Group.id
        ).where(
            PaymentSettings.brand_id == Brand.id,
            PaymentSettings.payment_method.in_(ALL_PAYMENT_METHODS),
            Group.status == "enabled"
        )
    ).scalars().all()

    payment_datas = {pd.payment_settings_id: pd for pd in payment_data or []}

    for ps in payments_settings:
        invoice_template_payment_data = payment_datas.get(ps.id)
        json_data = None
        if (invoice_template_payment_data and invoice_template_payment_data.json_data
                and invoice_template_payment_data.json_data.data):
            json_data = invoice_template_payment_data.json_data.data.dict()
        object_payment_settings = ObjectPaymentSettings(
            invoice_template_id=invoice_template_id if invoice_template_id else None,
            store_id=store_id if store_id else None,
            payment_settings_id=ps.id,
            json_data=json_data,
            is_enabled=invoice_template_payment_data.is_enabled if
            invoice_template_payment_data and
            invoice_template_payment_data.is_enabled is not None else False,
            target=ObjectPaymentSettingsTarget.INVOICE_TEMPLATE if
            invoice_template_id else ObjectPaymentSettingsTarget.STORE,
        )

        sess().add(object_payment_settings)

    sess().commit()
