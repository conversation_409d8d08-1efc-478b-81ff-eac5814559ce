from datetime import datetime
from typing import Any, Iterable, Literal, Type

from sqlalchemy import and_, not_, or_, select
from sqlalchemy.sql import Select

import schemas
from core.auth.statements import get_notification_settings_statement
from db import db_func, sess
from db.models import Scope, User, UserGroup, UserServiceBotActivity
from exceptions import AuthNoObjectsOrHaveNotEnoughPermissionsError
from utils.date_time import utcnow
from utils.type_vars import T


def check_access_to_action_sync(
        action: str,
        target: schemas.ScopeTarget,
        target_id: Any,
        available_data: dict[str, Any] | None = None,
        with_groups: bool = True,
        expire_check_datetime: datetime | None = ...,
        with_for_update: bool = False,
) -> bool:
    """
    Params described in Scope.filter_for_action
    """
    stmt = select(
        Scope.filter_for_action(
            action, target,
            target_id,
            available_data,
            with_groups,
            expire_check_datetime,
        )
    )
    if with_for_update:
        stmt = stmt.with_for_update()
    return sess().scalar(stmt)


@db_func
def check_access_to_action(
        action: str,
        target: schemas.ScopeTarget,
        target_id: Any,
        available_data: dict[str, Any] | None = None,
        with_groups: bool = True,
        expire_check_datetime: datetime | None = ...,
        with_for_update: bool = False,
) -> bool:
    """
    Params described in Scope.filter_for_action
    """
    return check_access_to_action_sync(
        action, target, target_id,
        available_data, with_groups,
        expire_check_datetime,
        with_for_update,
    )


@db_func
def check_access_to_actions(
        actions: list[str],
        target: schemas.ScopeTarget,
        target_id: Any,
        available_data: dict[str, Any] | None = None,
        with_groups: bool = True,
        expire_check_datetime: datetime | None = ...,
) -> dict[str, bool]:
    result: dict[str, bool] = {}

    for action in actions:
        result[action] = check_access_to_action_sync(
            action, target,
            target_id, available_data,
            with_groups, expire_check_datetime,
        )

    return result


@db_func
def get_scopes(
        target: schemas.ScopeTarget,
        target_id: int,
        with_expired: bool = False,
        include_groups_scopes: bool = False,  # only for target user, ignored else
        exclude: Iterable[str] | None = None,
        **available_data,
) -> list[Scope]:
    stmt: Select = select(Scope)

    target_condition = and_(
        Scope.target == target,
        (getattr(Scope, f"{target}_id") == target_id),
    )

    if target == "user" and include_groups_scopes:
        user_group_condition = and_(
            Scope.target == "user_group",
            Scope.user_group_id.in_(
                select(UserGroup.id).where(
                    UserGroup.users.any(id=target_id)
                )
            )
        )

        stmt = stmt.where(
            or_(
                target_condition,
                user_group_condition
            )
        )
    else:
        stmt = stmt.where(target_condition)

    for key, value in available_data.items():
        stmt = stmt.where(getattr(Scope, key) == value)

    if not with_expired:
        stmt = stmt.where(
            or_(
                Scope.expire_datetime.is_(None),
                Scope.expire_datetime > utcnow()
            )
        )

    if exclude:
        stmt = stmt.where(Scope.scope.not_in(exclude))

    stmt = stmt.order_by(Scope.id)

    return sess().scalars(stmt).all()


def get_user_groups_to_grant_access_to_created_object_sync(
        user_id: int,
        create_action: str,
        object_action: str,
        available_data: dict[str, Any] | None = None,
) -> list[UserGroup]:
    stmt: Select = select(UserGroup)
    stmt = stmt.where(UserGroup.users.any(id=user_id))

    stmt = stmt.where(
        Scope.filter_for_action(
            create_action,
            "user_group",
            UserGroup.id,
            available_data,
        )
    )
    stmt = stmt.where(
        not_(
            Scope.filter_for_action(
                object_action,
                "user_group",
                UserGroup.id,
                available_data,
            )
        )
    )

    return sess().scalars(stmt).all()


@db_func
def get_user_groups_to_grant_access_to_created_object(
        user_id: int,
        create_action: str,
        object_action: str,
        available_data: dict[str, Any] | None = None,
) -> list[UserGroup]:
    return get_user_groups_to_grant_access_to_created_object_sync(
        user_id, create_action, object_action, available_data
    )


@db_func
def get_users_for_action(
        action: str,
        available_data: dict | None = None,
        ignore_user_id: int | None = None,
        required_fields: Iterable[str] | None = None,
        is_entered_service_bot: bool | None = False,
        exclude_scopes: Iterable[str] | None = None,
) -> list[User]:
    user_ids_stmt = select(Scope.user_id).where(
        Scope.filter_for_action(
            action,
            "user", User.id,
            available_data,
            exclude_scopes=exclude_scopes,
            exists_or_conditions="conditions",
        )
    )

    stmt = select(User)
    profile_id = available_data.get("profile_id", None) if available_data else None
    stmt = get_notification_settings_statement(
        stmt, profile_id, "CRM"
    )

    if is_entered_service_bot:
        stmt = stmt.join(UserServiceBotActivity)

    stmt = stmt.where(User.id.in_(user_ids_stmt))
    stmt = stmt.where(User.id != ignore_user_id)

    if required_fields:
        stmt = stmt.where(
            *[getattr(User, field).is_not(None) for field in required_fields]
        )

    return sess().scalars(stmt).all()


@db_func
def get_user_scopes(
        user_id: int,
        **available_data,
) -> list[Scope]:
    return sess().scalars(
        select(Scope)
        .where(
            Scope.target == "user",
            Scope.user_id == user_id,
            *[
                (
                    getattr(Scope, key).is_(None)
                    if value is None else
                    or_(
                        getattr(Scope, key) == value,
                        getattr(Scope, key).is_(None),
                    )
                )
                for key, value in available_data.items()
                if value != 0
            ],
            not_(Scope.is_expired),
        )
    ).all()


@db_func
def get_and_validate_access_on_objects(
        object_name: str,
        object_cls: Type[T],
        object_ids: Iterable[int] | None,
        user_id: int,
        profile_id: int,
        scope_name: Literal["read", "edit"] = "read",
) -> list[T] | None:
    if not object_ids:
        return None

    # noinspection PyTypeChecker
    db_objects: dict[int, T] = dict(
        sess().execute(
            select(object_cls.id, object_cls).where(
                object_cls.id.in_(object_ids),
                Scope.filter_for_action(
                    f"{object_name}:{scope_name}", "user", user_id,
                    available_data={
                        "profile_id": profile_id,
                        f"{object_name}_id": object_cls.id
                    }
                ),
                object_cls.is_deleted.is_(False),
            )
        ).fetchall()
    )

    objects = []
    for object_id in object_ids:
        object = db_objects.get(object_id)
        if not object:
            raise AuthNoObjectsOrHaveNotEnoughPermissionsError(
                f"{object_name}:{scope_name}", {
                    "profile_id": profile_id,
                    f"{object_name}_id": object_id,
                }
            )
        objects.append(object)

    return objects
