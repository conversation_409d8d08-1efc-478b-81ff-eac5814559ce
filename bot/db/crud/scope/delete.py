from sqlalchemy import select
from sqlalchemy.sql import Select

from db import db_func, sess
from db.models import Scope


@db_func
def delete_scope(scope_or_id: Scope | int | None = None, **kwargs):
    """
    Delete a scope
    @param scope_or_id: Scope or scope's id to delete
    @param kwargs: additional fields to be checked for security reasons. If scope_or_id is <PERSON><PERSON> then ignored
    @return: True if deleted, False if <PERSON><PERSON> not found
    """

    if isinstance(scope_or_id, Scope):
        scope = scope_or_id
    else:
        stmt: Select = select(Scope)
        if scope_or_id:
            stmt = stmt.where(Scope.id == scope_or_id)
        if kwargs:
            stmt = stmt.filter_by(**kwargs)
        stmt = stmt.with_for_update()
        scope = sess().scalar(stmt)

    if not scope:
        return False

    sess().delete(scope)
    sess().commit()
    return True
