from typing import Literal

from sqlalchemy import select
from sqlalchemy.sql import Select

from db import db_func, sess
from db.models import Scope


@db_func
def update_scope(scope_id: int, data: dict, **kwargs) -> Scope | Literal[False]:
    """
    Update a scope
    @param scope_id: scope_id to update
    @param data: new data to update
    @param kwargs: additional fields to be checked for security reasons
    @return: Scope if updated, False if Sc<PERSON> not found
    """

    stmt: Select = select(Scope)
    stmt = stmt.where(Scope.id == scope_id)
    if kwargs:
        stmt = stmt.filter_by(**kwargs)
    stmt = stmt.with_for_update()
    scope: Scope | None = sess().scalar(stmt)

    if not scope:
        return False

    scope.update_sync(data)
    return scope
