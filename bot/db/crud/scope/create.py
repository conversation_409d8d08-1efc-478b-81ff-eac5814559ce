from sqlalchemy import delete, select
from sqlalchemy.sql import Delete

import schemas
from db import db_func, sess
from db.models import <PERSON>ope, User
from .read import (
    check_access_to_action_sync, get_user_groups_to_grant_access_to_created_object_sync,
)
from ...connection import Base


@db_func
def grant_scopes(
        target: schemas.ScopeTarget,
        target_id: int,
        scopes: list[schemas.CreateScopeData],
        replace: bool = False,
        replace_for_data: dict[str, int] | None = None,
):
    db_scopes: list[Scope] = []
    target_params = {
        "target": target,
        f"{target}_id": target_id,
    }

    if not replace_for_data:
        replace_for_data = {}
    current_scopes = sess().scalars(
        select(Scope).filter_by(**target_params, **replace_for_data)
    ).all()

    existing: list[Scope] = []

    to_delete_ids: set[int] = set()

    def check_scope(el: Scope, data: schemas.CreateScopeData):
        return all(
            [getattr(el, key) == value for key, value in {
                **target_params, **data.dict(
                    exclude={
                        "expire_datetime"}
                )
            }.items()]
        )

    for scope_data in scopes:
        existing_list = [scope for scope in current_scopes if
                         check_scope(scope, scope_data)]

        if len(existing_list) > 1:
            [to_delete_ids.add(scope.id) for scope in existing_list[:-1]]
            existing_scope = existing_list[-1]
        elif len(existing_list) == 1:
            existing_scope = existing_list[0]
        else:
            existing_scope = None

        if existing_scope:
            existing.append(existing_scope)

    if replace:
        [to_delete_ids.add(scope.id) for scope in current_scopes if
         scope not in existing]

    if to_delete_ids:
        stmt: Delete = delete(Scope)
        stmt = stmt.filter(Scope.id.in_(to_delete_ids))
        sess().execute(stmt)
        sess().flush()

    for scope_data in scopes:
        scope = [el for el in existing if check_scope(el, scope_data)]

        if scope:
            scope = scope[0]
            scope.update_sync(scope_data.dict())
        else:
            scope = Scope(**target_params, **scope_data.dict())
            sess().add(scope)
            sess().flush()

        db_scopes.append(scope)

    sess().commit()

    return db_scopes


def grand_scopes_to_created_object_sync(
        object_name: str,
        object: Base,
        creator: User,
        data: dict[str, int]
):
    user_groups = get_user_groups_to_grant_access_to_created_object_sync(
        creator.id,
        f"{object_name}:create",
        f"{object_name}:edit",
        available_data=data
    )

    new_scopes = []

    new_scope_objects_data = {
        **data,
        object_name: object
    }

    if user_groups:
        for use_group in user_groups:
            new_scopes.append(
                Scope(
                    target="user_group",
                    user_group=use_group,
                    scope=f"{object_name}:edit",
                    **new_scope_objects_data
                )
            )

    elif not check_access_to_action_sync(
            f"{object_name}:edit", "user", creator.id,
            available_data=data,
    ):
        scope = Scope(
            target="user",
            user=creator,
            scope=f"{object_name}:edit",
            **new_scope_objects_data,
        )
        new_scopes.append(scope)

    if new_scopes:
        sess().add_all(new_scopes)

    return new_scopes
