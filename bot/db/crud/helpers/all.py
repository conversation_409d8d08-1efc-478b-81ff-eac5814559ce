import enum
import re
from typing import Sequence, Type, TypeVar

from sqlalchemy import func, select, update

import exceptions
from config import ANONYMOUS_USER_ID
from db import sess
from db.connection import Base
from db.models import User
from utils.helpers import get_duplicated_values
from utils.type_vars import T

VT = TypeVar('VT')


def check_items(
        values: Sequence[VT],
        model: Type[T],
        not_unique_error: Type[exceptions.IdNotUniqueError] | None = None,
        not_found_error: Type[exceptions.ObjectListNotFoundError] | None = None,
        value_field: str = "id",
) -> dict[VT, T]:
    if not_unique_error and len(values) != len(set(values)):
        raise not_unique_error(get_duplicated_values(values))

    found = {
        getattr(el, value_field): el
        for el in (
            sess().scalars(
                select(model).where(getattr(model, value_field).in_(values))
            ).all()
        )
    }

    if not_found_error and len(set(found)) != len(set(values)):
        raise not_found_error(
            [
                id.value if isinstance(id, enum.Enum) else id
                for id in values
                if id not in found
            ]
        )

    return found


def delete_objects(model: Type[Base], existing_ids: Sequence[int]):
    sess().execute(
        update(model)
        .values({"is_deleted": True})
        .where(model.id.not_in(existing_ids))
    )


def user_field_if_not_anonymous(
        user_id_expr = User.id, label: str = "user_id", field_expr = None
):
    if field_expr is None:
        field_expr = user_id_expr

    if not ANONYMOUS_USER_ID:
        return user_id_expr
    return func.IF(user_id_expr == ANONYMOUS_USER_ID, None, field_expr).label(label)


def filter_by_phone(phone_statement, search_text: str):
    return (
            func.regexp_replace(phone_statement, "[^0-9]", "") ==
            re.sub(r"[+-]|\s", "", search_text.strip())
    )
