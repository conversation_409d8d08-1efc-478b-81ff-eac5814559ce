from db import db_func, sess
from db.models import QuantitativeService, QuantitativeServiceUsageLog


@db_func
def use_quantitative_services(
        services_with_usages: dict[QuantitativeService, int]
):
    """
    @param services_with_usages: dict of services to update, where key is service id and value is difference
    @return: None
    """

    logs = []
    for service, usage in services_with_usages.items():
        used_before = service.used
        service.used += usage
        used_after = service.used

        logs.append(QuantitativeServiceUsageLog(
            service=service,
            service_type=service.service_type,
            quantity=service.quantity,
            used_before=used_before,
            used=usage,
            used_after=used_after,
        ))
    sess().add_all(logs)

    sess().commit()
