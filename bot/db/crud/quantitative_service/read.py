from typing import TypedDict

from sqlalchemy import or_

from db import db_func, sess
from db.models import QuantitativeService, QuantitativeServiceTypeEnum

from .create import create_default_quantitative_service_if_not_created_sync


def get_quantitative_service_to_use_sync(group_id: int, type_: QuantitativeServiceTypeEnum) -> QuantitativeService:
    query = sess().query(QuantitativeService)
    query = query.populate_existing()
    query = query.with_for_update()
    query = query.filter(QuantitativeService.group_id == group_id)
    query = query.filter(QuantitativeService.service_type == type_)
    # noinspection PyTypeChecker
    query = query.filter(or_(
        QuantitativeService.available.is_(None),
        QuantitativeService.available > 0,
    ))

    query = query.order_by(QuantitativeService.expire_at.is_(None))
    query = query.order_by(QuantitativeService.expire_at)
    query = query.order_by(QuantitativeService.available.is_not(None))
    query = query.order_by(QuantitativeService.available)

    query = query.limit(1)
    return query.one_or_none()


get_quantitative_service_to_use = db_func(get_quantitative_service_to_use_sync)


class ResultBase(TypedDict, total=True):
    success: bool


class Result(ResultBase, total=False):
    services_with_usages: dict[QuantitativeService, int]
    covered_quantity: int


@db_func
def get_quantitative_services_to_use(
        group_id: int,
        type_: QuantitativeServiceTypeEnum,
        quantity: int,
) -> Result:
    if not quantity:
        return {"success": True, "services_with_usages": {}}

    services_with_usages: dict[QuantitativeService, int] = {}

    covered_quantity = 0

    create_default_quantitative_service_if_not_created_sync(group_id, type_)

    while covered_quantity != quantity:
        service = get_quantitative_service_to_use_sync(group_id, type_)

        if not service:
            break

        left_quantity = quantity - covered_quantity

        if service.available is None or service.available > left_quantity:
            usage = left_quantity
        else:
            usage = service.available

        covered_quantity += usage
        services_with_usages[service] = usage

    if covered_quantity == quantity:
        return {"success": True, "services_with_usages": services_with_usages}

    sess().commit()

    return {"success": False, "covered_quantity": covered_quantity}


@db_func
def check_has_group_received_default_service(group_id: int, type_: QuantitativeServiceTypeEnum) -> bool:
    query = sess().query(QuantitativeService.id)
    query = query.populate_existing()
    query = query.with_for_update()
    query = query.filter(QuantitativeService.group_id == group_id)
    query = query.filter(QuantitativeService.service_type == type_)
    return sess().query(query.exists()).scalar()
