from datetime import datetime

from config import DEFAULT_QUANTITATIVE_SERVICES
from db import db_func, sess
from db.models import QuantitativeServiceTypeEnum, QuantitativeService


@db_func
def create_quantitative_service(
        group_id: int,
        type_: QuantitativeServiceTypeEnum,
        quantity: int,
        expire_at: datetime | None = None,
):
    service = QuantitativeService(
        group_id=group_id,
        service_type=type_,
        quantity=quantity,
        expire_at=expire_at,
    )
    sess().add(service)
    sess().commit()
    return service


def create_default_quantitative_service_if_not_created_sync(
        group_id: int,
        type_: QuantitativeServiceTypeEnum,
):
    query = sess().query(QuantitativeService.id)
    query = query.populate_existing()
    query = query.with_for_update()
    query = query.filter(QuantitativeService.group_id == group_id)
    query = query.filter(QuantitativeService.service_type == type_)
    is_received = sess().query(query.exists()).scalar()

    if not is_received:
        service = QuantitativeService(
            group_id=group_id,
            service_type=type_,
            quantity=DEFAULT_QUANTITATIVE_SERVICES[type_.value],
        )
        sess().add(service)
    sess().commit()
    return
