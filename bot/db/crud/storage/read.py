from sqlalchemy import func, select
from sqlalchemy.engine import Row
from sqlalchemy.sql import label

from db import db_func, sess
from db.models import Storage, Scope, MediaObject


@db_func
def get_storage_list(
        profile_id: int,
        user_id: int,
        search_text: str | None = None,
        offset: int | None = None,
        limit: int | None = None,
) -> list[Row]:
    read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="storage",
            target="user",
            target_id=user_id,
            available_data={
                "profile_id": profile_id,
            }
        )

    name = label("name", func.IFNULL(
        Storage.text,
        func.SUBSTRING_INDEX(MediaObject.file_path, '/', -1)
    ))

    stmt = select(
        Storage.id,
        Storage.media_id,
        Storage.is_multi_load,
        Storage.datetime_upload,
        name,
        label("media_url", MediaObject.url),
        MediaObject.media_type,
        label("media_mime_type", MediaObject.media_type),
        label("media_file_size", MediaObject.file_size),
        read_allowed, edit_allowed,
    )
    stmt = stmt.join(Storage.media)

    stmt = stmt.where(
        Storage.group_id == profile_id,
    )

    stmt = stmt.where(read_allowed.is_(True))

    if search_text:
        stmt = stmt.where(name.contains(search_text))

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    return sess().execute(stmt).fetchall()
