import schemas
from db import db_func, sess
from db.crud.scope.create import grand_scopes_to_created_object_sync
from db.models import ExtraFeeJournal, ExtraFeeSettings, Group, User
from schemas.base import ExtraFeeSchema


@db_func
def create_extra_fee(
        group: Group,
        data: schemas.AdminCreateExtraFeeData,
        creator: User | None = None,
) -> ExtraFeeSettings:
    extra_fee_data = data.dict(exclude_unset=True, exclude={"translations"})

    extra_fee = ExtraFeeSettings(
        group_id=group.id,
        is_active=True,
        position=0,
        **extra_fee_data,
    )
    sess().add(extra_fee)

    if creator:
        grand_scopes_to_created_object_sync(
            "extra_fee", extra_fee, creator, {
                "profile_id": group.id,
            }
        )

    sess().commit()
    return extra_fee


@db_func
def add_extra_fee_to_journal(
        journal_entries: list[ExtraFeeSchema],
        order_id: int | None = None,
        invoice_id: str | None = None,
):
    for entry in journal_entries:
        if order_id:
            entry.order_id = order_id
        if invoice_id:
            entry.invoice_id = invoice_id
        sess().add(
            ExtraFeeJournal(
                **entry.dict()
            )
        )
    sess().commit()
