import schemas
from db import db_func, sess
from db.models import ExtraFeeSettings


@db_func
def update_extra_fee(
        extra_fee: ExtraFeeSettings,
        data: schemas.AdminUpdateExtraFeeData | schemas.AdminExtraFeeListSchema,
):
    extra_fee_object_data = data.dict(
        exclude_unset=True, exclude={"translations", "id", "read_allowed", "edit_allowed"}
    )

    if extra_fee_object_data:
        extra_fee.update_sync(extra_fee_object_data, no_commit=True)

    sess().commit()

    return extra_fee
