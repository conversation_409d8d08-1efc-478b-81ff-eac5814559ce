from sqlalchemy import distinct, func, select, text
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

from db import db_func, sess
from db.models import (ExtraFeeSettings, Group, Scope)


@db_func
def get_extra_fee(extra_fee_id: int, profile_id: int) -> ExtraFeeSettings | None:
    stmt: Select = select(ExtraFeeSettings)
    stmt = stmt.join(Group, ExtraFeeSettings.group_id == Group.id)
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(ExtraFeeSettings.id == extra_fee_id)
    stmt = stmt.where(ExtraFeeSettings.is_deleted.is_(False))

    return sess().scalar(stmt)


@db_func
def get_admin_extra_fees_list(
        profile_id: int,
        user_id: int,
        search_text: str | None = None,
        exclude: list[int] | None = None,
        offset: int | None = None,
        limit: int | None = None,
        need_check_access: bool = False,
        is_count: bool = False,
) -> list[Row]:
    if is_count:
        stmt: Select = select(func.count(distinct(ExtraFeeSettings.id)))
    else:
        stmt: Select = select(
            ExtraFeeSettings.id,
            ExtraFeeSettings.name,
            *Scope.allowed_scopes_list(
                "read", "edit",
                object_name="extra_fee",
                target="user",
                target_id=user_id,
                available_data={
                    "profile_id": profile_id,
                    "extra_fee_id": ExtraFeeSettings.id,
                }
            ),
            ExtraFeeSettings.extra_fee_percent,
            ExtraFeeSettings.extra_fee_value,
            ExtraFeeSettings.is_active,
            ExtraFeeSettings.position,
        )

        stmt = stmt.distinct()

    stmt = stmt.join(Group, ExtraFeeSettings.group_id == Group.id)

    stmt = stmt.where(ExtraFeeSettings.is_deleted.is_(False))
    stmt = stmt.where(Group.id == profile_id)
    stmt = stmt.where(Group.status == "enabled")

    if need_check_access:
        stmt = stmt.where(text("read_allowed IS TRUE"))

    if exclude:
        stmt = stmt.where(ExtraFeeSettings.id.not_in(exclude))

    if search_text:
        stmt = stmt.where(ExtraFeeSettings.name.contains(search_text))

    if is_count:
        return sess().scalar(stmt)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)

    stmt = stmt.order_by(ExtraFeeSettings.id.desc())

    return sess().execute(stmt).fetchall()
