from sqlalchemy import or_, select, and_, case

import schemas
from db import db_func, sess
from db.models import Brand, Group, NotificationSetting, Scope
from db.types.operation import Operation
from schemas import IDCursor
from utils.scopes_map import crm


@db_func
def get_profiles_notification_settings(
    user_id: int,
    target: str = "CRM",
    params: schemas.CRMNotificationSettingsParams | None = None,
    operation: Operation = "list",
    cursor: IDCursor | None = None,
    for_all_profiles: bool = False,
):
    if not params:
        params = schemas.CRMNotificationSettingsParams()

    if not cursor:
        cursor = params.cursor

    if cursor and cursor.direction != schemas.CursorDirection.NEXT:
        raise ValueError("Cursor direction PREV is not supported")

    stmt = select(
        Group.name,
        Group.id,
        Brand.logo_media_id,
        case(
            [
                (NotificationSetting.id.isnot(None), NotificationSetting.is_enabled)
            ],
            else_=True
        ).label("is_enabled"),
        NotificationSetting.user_id,
    )

    stmt = stmt.outerjoin(Brand, Brand.group_id == Group.id)
    stmt = stmt.outerjoin(
        NotificationSetting,
        and_(
            NotificationSetting.group_id == Group.id,
            NotificationSetting.user_id == user_id,
            NotificationSetting.target == target
        )
    )
    stmt = stmt.where(
        Scope.filter_for_object(
            "profile", Group.id,
            "user", user_id,
            include_scopes=list(map(lambda x: x.scope, crm.scopes))
        )
    )

    if cursor:
        if cursor.direction == schemas.CursorDirection.BACK:
            stmt = stmt.where(Group.id > cursor.id)
        else:
            stmt = stmt.where(Group.id < cursor.id)

    if operation == "exists":
        return sess().scalar(select(stmt.exists()))

    if params.search:
        stmt = stmt.where(Group.name.ilike(f"%{params.search}%"))

    if operation != "count" and params and not for_all_profiles:
        if params.offset:
            stmt = stmt.offset(params.offset)
        if params.limit:
            stmt = stmt.limit(params.limit)

    stmt = stmt.group_by(Group.id)
    stmt = stmt.order_by(Group.id.desc())

    return sess().execute(stmt).fetchall()


@db_func
def are_all_notification_settings_disabled(user_id: int, target: str = "CRM") -> bool | None:
    stmt = select(NotificationSetting.is_enabled.is_(False))
    stmt = stmt.where(
        and_(
            NotificationSetting.user_id == user_id,
            NotificationSetting.target == target,
        )
    )

    result = sess().execute(stmt).scalars().all()

    if all(result):
        return True
    elif not any(result):
        return False
    else:
        return None
