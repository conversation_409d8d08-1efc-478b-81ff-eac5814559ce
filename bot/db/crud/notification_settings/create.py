import schemas
from schemas.group.group import NotificationsTarget
from db import db_func, sess
from db.models import NotificationSetting
from utils.decorators import catch_error_with_text_variable
from sqlalchemy.orm.exc import NoResultFound


@catch_error_with_text_variable
@db_func
def create_or_update_notification_settings(
    data: list[schemas.CRMUpdateNotificationSettingsItem], user_id: int, target: NotificationsTarget
) -> bool:
    for setting in data:
        try:
            existing_setting = sess().query(NotificationSetting).filter_by(
                group_id=setting.profile_id, user_id=user_id
            ).one()
            if existing_setting and existing_setting.target != target:
                continue
            existing_setting.is_enabled = setting.is_enabled
        except NoResultFound:
            new_setting = NotificationSetting(
                group_id=setting.profile_id,
                is_enabled=setting.is_enabled,
                target=target,
                user_id=user_id,
            )
            sess().add(new_setting)

    sess().commit()

    return True
