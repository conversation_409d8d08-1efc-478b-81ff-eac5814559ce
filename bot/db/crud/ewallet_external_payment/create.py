import schemas
from db import db_func, sess
from db.models import EWalletExternalPayment, EWalletExternalPaymentStatusHistory, User


@db_func
def create_ewallet_external_payment(
        user: User,
        **kwargs,
) -> EWalletExternalPayment:
    payment = EWalletExternalPayment.create_sync(
        **kwargs,
        user=user,
        no_commit=True
    )

    EWalletExternalPaymentStatusHistory.create_sync(
        payment=payment,
        initiated_by=schemas.EWalletExtPaymentStatusInitiatedBy.USER,
        initiated_by_user=user,
        status=schemas.EWalletExternalPaymentStatus.CREATED,
        no_commit=True,
    )

    sess().commit()

    return payment
