import schemas
from db import db_func, sess
from db.models import EWalletExternalPayment, EWalletExternalPaymentStatusHistory, User


@db_func
def set_ewallet_external_payment_status(
        payment: EWalletExternalPayment,
        new_status: schemas.EWalletExternalPaymentStatus,
        user: User
):
    payment.status = new_status
    if new_status == schemas.EWalletExternalPaymentStatus.PENDING:
        payment.payer_id = user.id

    EWalletExternalPaymentStatusHistory.create_sync(
        payment=payment,
        status=new_status,
        initiated_by=(
            schemas.EWalletExtPaymentStatusInitiatedBy.USER
            if user.id == payment.user_id else
            schemas.EWalletExtPaymentStatusInitiatedBy.MANAGER
        ),
        initiated_by_user=user,
        no_commit=True
    )

    sess().commit()
    return payment
