from sqlalchemy import case, func, literal_column, select

import schemas
from config import INBOX_MAX_AGE
from db import db_func, sess
from db.crud.helpers import (
    filter_by_phone, get_inbox_status_sort_stmt,
    user_field_if_not_anonymous,
)
from db.crud.helpers.crm import (
    crm_filter_by_inbox_cursor, crm_params_filter, crm_scope_filter,
    get_inbox_type_sort_stmt,
)
from db.models import (
    ClientBot, EWallet, EWalletExternalPayment,
    EWalletExternalPaymentStatusHistory, Group, Scope, User,
)
from db.types.operation import Operation
from schemas import EWalletExternalPaymentStatus
from utils.date_time import utcnow


@db_func
def get_ewallet_ext_payment(
        ewallet_ext_payment_id: int,
        action: str | None = None,
        user_id: int | None = None
):
    stmt = select(EWalletExternalPayment)
    stmt = stmt.where(EWalletExternalPayment.id == ewallet_ext_payment_id)

    if any((action, user_id)) and not all((action, user_id)):
        raise ValueError("Specify both action and user_id or none of them")

    if action and user_id:
        stmt = stmt.join(EWalletExternalPayment.profile)
        stmt = stmt.where(Group.status == "enabled")
        stmt = stmt.where(
            Scope.filter_for_action(
                action, "user", user_id,
                {
                    "profile_id": Group.id,
                    "ewallet_ext_payment_id": ewallet_ext_payment_id,
                }
            )
        )

    stmt = stmt.group_by(EWalletExternalPayment.id)

    return sess().scalar(stmt)


def get_crm_ewallet_ext_payment_list_statement(
        user_id: int,
        params: schemas.CRMEwalletExtPaymentListParams | None = None,
        operation: Operation = "list",
        cursor: schemas.IDCursor | None = None,
        inbox_cursor: schemas.InboxCursor | None = None,
        for_inbox: bool = False,
        is_platform_admin: bool | None = None,
):
    inbox_status = case(
        [
            (
                EWalletExternalPayment.status == EWalletExternalPaymentStatus.CREATED,
                literal_column(f"'{schemas.InboxStatus.NEW.value}'"),
            ),
            (
                EWalletExternalPayment.status == EWalletExternalPaymentStatus.PENDING,
                literal_column(f"'{schemas.InboxStatus.IN_PROGRESS.value}'"),
            ),
        ],
        else_=literal_column(f"'{schemas.InboxStatus.RECENT.value}'"),
    )
    inbox_status_sort = get_inbox_status_sort_stmt(inbox_status)

    if operation == "count":
        stmt = select(
            func.count(EWalletExternalPayment.id)
        )
    else:
        stmt = select(
            literal_column(f"'{schemas.InboxType.EWALLET_EXT_PAYMENT.value}'").label(
                "inbox_type"
            ),
            inbox_status.label("inbox_status"),
            inbox_status_sort,
            get_inbox_type_sort_stmt(schemas.InboxType.EWALLET_EXT_PAYMENT),
            EWalletExternalPayment.time_updated.label("change_date"),
            literal_column("NULL").label("desired_delivery_date"),
            EWalletExternalPayment.id,
            literal_column("NULL").label("is_pending"),
            literal_column("NULL").label("type"),
            literal_column("NULL").label("privacy"),
            literal_column("NULL").label("text"),
            literal_column("NULL").label("additional_text"),
            literal_column("NULL").label("media"),
            EWalletExternalPayment.crm_tag.label("crm_tag"),
            literal_column("NULL").label("invoice_type"),
            EWalletExternalPayment.status.label("status"),
            literal_column("NULL").label("status_pay"),
            EWalletExternalPayment.status.label("current_status"),
            literal_column("NULL").label("shipment_name"),
            literal_column("NULL").label("shipment_type"),
            user_field_if_not_anonymous(User.id, "first_name", User.first_name),
            user_field_if_not_anonymous(User.id, "last_name", User.last_name),
            user_field_if_not_anonymous(User.id, "full_name", User.name),
            user_field_if_not_anonymous(User.id, "email", User.email),
            user_field_if_not_anonymous(User.id, "phone", User.wa_phone),
            user_field_if_not_anonymous(User.id, "photo_url", User.photo_url),
            EWallet.currency,
            user_field_if_not_anonymous(User.id),
            literal_column("0").label("store_id"),
            literal_column("NULL").label("store_name"),
            literal_column("NULL").label("ticket_title"),
            Group.id.label("profile_id"),
            Group.name.label("profile_name"),
            Group.name.label("business_name"),
            ClientBot.display_name.label("bot_name"),
            literal_column("0").label("before_loyalty_sum"),
            literal_column("0").label("discount"),
            literal_column("0").label("bonuses_redeemed"),
            literal_column("0").label("discount_and_bonuses_redeemed"),
            EWalletExternalPayment.amount.label("total_sum"),
            literal_column("0").label("tips_sum"),
            EWalletExternalPayment.amount.label("sum_to_pay"),
            literal_column("0").label("payer_fee"),
            literal_column("0").label("paid_sum"),
            literal_column("0").label("menu_in_store_id"),
            literal_column("NULL").label("menu_in_store_comment"),
            literal_column("NULL").label("title"),
            literal_column("NULL").label("mark"),
            literal_column("''").label("items_text"),
            literal_column("NULL").label("last_message_text"),
            literal_column("NULL").label("last_message_content_type"),
            literal_column("NULL").label("last_message_media_url"),
            literal_column("NULL").label("last_message_media_mime_type"),
            literal_column("NULL").label("last_message_content"),
            literal_column("NULL").label("is_read"),
            literal_column("NULL").label("read_by_user_id"),
            literal_column("NULL").label("comment"),
            EWalletExternalPayment.time_created,
        )

    if operation != "count" or (params and params.search_text):
        stmt = stmt.outerjoin(User, EWalletExternalPayment.user_id == User.id)

    stmt = stmt.join(Group, EWalletExternalPayment.profile_id == Group.id)
    stmt = stmt.join(EWallet, EWalletExternalPayment.ewallet_id == EWallet.id)
    stmt = stmt.join(ClientBot, EWallet.bot_id == ClientBot.id)
    stmt = stmt.where(Group.status == "enabled")

    stmt = crm_filter_by_inbox_cursor(
        stmt, schemas.InboxType.EWALLET_EXT_PAYMENT, inbox_cursor,
        EWalletExternalPayment.time_updated, EWalletExternalPayment.id,
        inbox_status_sort
    )

    if for_inbox:
        stmt = stmt.where(
            EWalletExternalPayment.time_updated >= utcnow() - INBOX_MAX_AGE
        )

    extra_search_conditions = []
    if params:
        if params.search_text:
            extra_search_conditions.extend(
                [
                    User.full_name.contains(params.search_text.strip()),
                    User.email.contains(params.search_text.strip()),
                    filter_by_phone(User.wa_phone, params.search_text)
                ]
            )
        if params.statuses:
            stmt = stmt.where(
                EWalletExternalPayment.status.in_(params.statuses)
            )

    stmt = crm_scope_filter(
        stmt, "ewallet_ext_payment", user_id,
        EWalletExternalPayment.id, is_platform_admin=is_platform_admin,
    )

    stmt = crm_params_filter(
        stmt, EWalletExternalPayment, params, cursor, operation, extra_search_conditions
    )

    return stmt


@db_func
def get_ewallet_ext_payment_status_history(payment_id: int):
    stmt = select(
        EWalletExternalPaymentStatusHistory.id,
        EWalletExternalPaymentStatusHistory.status,
        func.LOWER(EWalletExternalPaymentStatusHistory.initiated_by).label(
            "initiated_by"
        ),
        EWalletExternalPaymentStatusHistory.initiated_by_user_id,
        EWalletExternalPaymentStatusHistory.time_created.label("set_datetime"),
        User.name.label("initiated_by_user_name"),
        User.email.label("initiated_by_email"),
    )
    stmt = stmt.outerjoin(EWalletExternalPaymentStatusHistory.initiated_by_user)
    stmt = stmt.where(EWalletExternalPaymentStatusHistory.payment_id == payment_id)
    stmt = stmt.order_by(
        EWalletExternalPaymentStatusHistory.time_created,
        EWalletExternalPaymentStatusHistory.id
    )

    data = sess().execute(stmt).fetchall()
    return [schemas.CRMEwalletExtPaymentStatusHistoryObject.from_orm(el) for el in data]
