from sqlalchemy import func, select
from sqlalchemy.orm import aliased

from db import db_func, sess
from db.models import CustomField, CustomFieldValue


@db_func
def get_custom_fields_for_user(group_id: int, user_id: int):
    stmt = select(
        CustomField.id.label("field_id"),
        CustomFieldValue.id.label("value_id"),
        CustomField.name,
        CustomFieldValue.value,
    )
    stmt = stmt.join(CustomField.values)

    aliased_value = aliased(CustomFieldValue)
    stmt = stmt.where(
        CustomFieldValue.id == select(
            func.max(
                aliased_value.id
            )
        ).where(
            aliased_value.field_id == CustomField.id,
            aliased_value.user_id == user_id,
        ).scalar_subquery()
    )

    stmt = stmt.where(
        CustomField.group_id == group_id,
        CustomFieldValue.user_id == user_id,
    )

    stmt = stmt.order_by(CustomFieldValue.time_created)
    stmt = stmt.order_by(CustomFieldValue.id)

    return sess().execute(stmt).fetchall()
