from sqlalchemy import func, select
from sqlalchemy.engine import Row

import schemas
from db import db_func, sess
from db.models import Chat, ChatMessage, Group, MediaObject, User, VirtualManager, VirtualManagerChat
from db.types.operation import Operation
from utils.media import make_preview_url


@db_func
def get_chat_message_history(
        chat_id: int,
        cursor: schemas.IDCursor | None = None,
        limit: int | None = None,
        operation: Operation = "list"
) -> list[schemas.ChatMessageSchema] | bool | int:
    if operation == "count":
        stmt = select(func.count(ChatMessage.id))
    elif operation == "exists":
        stmt = select(ChatMessage.id)
    else:
        stmt = select(
            ChatMessage.id,
            ChatMessage.chat_id,
            ChatMessage.sent_by,
            func.IFNULL(User.name, func.IFNULL(VirtualManager.name, Group.name)).label("sent_by_name"),
            ChatMessage.sent_by_user_id,
            User.name.label("sent_by_user_name"),
            User.email.label("sent_by_email"),
            User.photo_url.label("sent_by_photo_url"),
            ChatMessage.vmc_id,
            VirtualManager.name.label("vm_name"),
            ChatMessage.content_type,
            ChatMessage.text,
            ChatMessage.media_id,
            MediaObject.url.label("media_url"),
            MediaObject.file_path.label("media_file_path"),
            MediaObject.mime_type.label("media_mime_type"),
            MediaObject.file_size.label("media_file_size"),
            MediaObject.original_file_name.label("media_original_file_name"),
            ChatMessage.content,
            ChatMessage.is_mailing,
            ChatMessage.time_created,
        )
        stmt = stmt.join(Chat, ChatMessage.chat_id == Chat.id)
        stmt = stmt.join(Group, Chat.group_id == Group.id)
        stmt = stmt.outerjoin(User, ChatMessage.sent_by_user_id == User.id)
        stmt = stmt.outerjoin(VirtualManagerChat, ChatMessage.vmc_id == VirtualManagerChat.id)
        stmt = stmt.outerjoin(VirtualManager, VirtualManagerChat.virtual_manager_id == VirtualManager.id)
        stmt = stmt.outerjoin(MediaObject, ChatMessage.media_id == MediaObject.id)

    stmt = stmt.where(ChatMessage.chat_id == chat_id)

    if cursor:
        if cursor.direction == schemas.CursorDirection.BACK:
            stmt = stmt.where(ChatMessage.id > cursor.id)
            stmt = stmt.order_by(ChatMessage.id.asc())
        else:
            stmt = stmt.where(ChatMessage.id < cursor.id)
            stmt = stmt.order_by(ChatMessage.id.desc())
    else:
        stmt = stmt.order_by(ChatMessage.id.desc())

    stmt = stmt.group_by(ChatMessage.id)

    if operation == "exists":
        return sess().scalar(select(stmt.exists()))
    if operation == "count":
        return sess().scalar(stmt)

    if limit:
        stmt = stmt.limit(limit)

    result = sess().execute(stmt).fetchall()

    if cursor and cursor.direction == schemas.CursorDirection.BACK:
        result = result.reverse()

    def map_item(data: Row):
        schema = schemas.ChatMessageSchema.from_orm(data)
        if not schema.media_mime_type or not data["media_file_path"]:
            return schema

        schema.media_preview_url = make_preview_url(schema.media_mime_type, data["media_file_path"])
        return schema

    return list(map(map_item, result))
