from datetime import datetime

from fastapi import UploadFile
from sqlalchemy import update

from db import db_func, sess
from db.models import Cha<PERSON>, ChatMessage, MediaObject
from schemas import ChatMessageSentByEnum, MessageContentTypeEnum


@db_func
def save_chat_message(
        chat: Chat,
        content_type: MessageContentTypeEnum,
        text: str | None = None,
        media: MediaObject | UploadFile | str | None = None,
        content: dict | None = None, *,
        sent_by: ChatMessageSentByEnum,
        sent_by_user_id: int | None = None,
        vmc_id: int | None = None,
        is_mailing: bool = False,
        chat_pending: bool | None = False,
        menu_in_store_id: int | None = None,
        wa_master_template_id: int | None = None,
        wa_template_variables: list[dict] | None = None,
) -> ChatMessage:
    """Input parameters are not validated. Should be called from
    core.chat.functions.save_and_send_chat_message"""

    sess().execute(
        update(ChatMessage).values({"is_last": False}).where(
            ChatMessage.is_last.is_(True),
            ChatMessage.chat_id == chat.id,
        )
    )
    if chat_pending is not None:
        chat.is_pending = chat_pending
        now = datetime.utcnow()
        if chat_pending:
            chat.last_pending_set_datetime = now
        chat.change_date = now

    message = ChatMessage(
        chat=chat,
        sent_by=sent_by,
        sent_by_user_id=sent_by_user_id,
        vmc_id=vmc_id,
        content_type=content_type,
        text=text,
        media=media,
        content=content,
        is_mailing=is_mailing,
        is_last=True,
        menu_in_store_id=menu_in_store_id,
        wa_master_template_id=wa_master_template_id,
        wa_template_variables=wa_template_variables,
    )
    sess().add(message)
    sess().commit()
    return message
