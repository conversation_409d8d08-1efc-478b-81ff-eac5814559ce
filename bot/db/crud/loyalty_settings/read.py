from typing import Optional

from sqlalchemy import or_, select

import schemas
from db import sess
from db.decorators import db_func
from db.models import LoyaltySettings

# Конфігурація полів для кожного контексту (використовує priority для сортування)
TARGET_FIELDS = {
    schemas.LoyaltySettingsTarget.INVOICE_TEMPLATE: [
        "product_id", "invoice_template_id", "store_id", "brand_id", "profile_id"
    ],
    schemas.LoyaltySettingsTarget.STORE: [
        "product_id", "store_id", "brand_id", "profile_id"
    ],
    schemas.LoyaltySettingsTarget.PRODUCT: [
        "product_id", "store_id", "brand_id", "profile_id"
    ],
    schemas.LoyaltySettingsTarget.EWALLET: [
        "ewallet_id",
    ],
    schemas.LoyaltySettingsTarget.BRAND: [
        "brand_id", "profile_id"
    ],
    schemas.LoyaltySettingsTarget.PROFILE: [
        "profile_id"
    ],
}


@db_func
def get_loyalty_settings_for_context(
        target: schemas.LoyaltySettingsTarget,
        data: schemas.LoyaltySettingsData,
) -> Optional[LoyaltySettings]:
    """
    Отримати налаштування лояльності для заданого контексту.
    
    Використовує OR умову по всіх релевантних полях та покладається на 
    поле priority для правильного сортування результатів.
    
    Пріоритети (найвищий до найнижчого):
    - Product: 50 
    - InvoiceTemplate: 40
    - Store: 30
    - Profile: 10
    - EWallet: 5 (особлива логіка)
    
    Args:
        target: Тип контексту
        data: Дані з ID об'єктів
        
    Returns:
        Налаштування лояльності або None
    """
    # 1. Отримуємо релевантні поля для цього контексту
    fields = TARGET_FIELDS.get(target, [])
    if not fields:
        return None
    
    # 2. Будуємо OR умови для всіх заповнених полів
    conditions = []
    for field in fields:
        value = getattr(data, field, None)
        if not value:
            continue
        conditions.append(getattr(LoyaltySettings, field) == value)
    
    if not conditions:
        return None
    
    # 3. Створюємо запит з OR умовою (БЕЗ фільтра is_enabled)
    stmt = select(LoyaltySettings).where(or_(*conditions))
    
    # 4. Сортуємо по пріоритету (найвищий перший) та ID для детермінізму  
    loyalty_settings = sess().scalar(
        stmt
        .order_by(
            LoyaltySettings.priority.desc(), 
            LoyaltySettings.id.desc()
        )
        .limit(1)
    )
    
    # 5. Перевіряємо is_enabled після отримання найбільш пріоритетних налаштувань
    # За замовчуванням (include_disabled=True) НЕ перевіряємо активність
    if loyalty_settings and not data.include_disabled and not loyalty_settings.is_enabled:
        return None
    
    return loyalty_settings