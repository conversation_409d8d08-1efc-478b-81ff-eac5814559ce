import re
from datetime import datetime

import aiogram as tg
from aiowhatsapp.schemas import Debu<PERSON><PERSON><PERSON>, PhoneNumberInfo

import schemas
from db import db_func, sess
from db.crud.translation.update import update_object_translations_sync
from db.models import AdminBotAnalyticAction, ClientBot, CustomMenuButton, Group, User
from .read import find_existing_bot_sync
from ..group.create import create_group_sync


@db_func
def create_bot(
        owner: User,
        token: str,
        bot_type: str,  # telegram | whatsapp
        group: Group | None = None,
        whatsapp_from: str | None = None,
        whatsapp_app_id: str | None = None,
        whatsapp_app_secret: str | None = None,
        is_accepted_agreement: bool = False,
        country_iso_code: str | None = None,
        timezone: str | None = None,
        lang: str | None = None,
        tg_bot_user: tg.types.User | None = None,
        whatsapp_token_info: DebugToken | None = None,
        whatsapp_phone_number_info: PhoneNumberInfo | None = None,
        whatsapp_business_account_id: str | None = None,
) -> "ClientBot":
    bot_data = {
        "token": token,
        "username": None,
        "bot_type": bot_type,
        "whatsapp_from": whatsapp_from or None,
        "whatsapp_app_id": whatsapp_app_id or None,
        "whatsapp_app_secret": whatsapp_app_secret or None,
    }
    phone_number = None

    if bot_type == "telegram":
        if not tg_bot_user:
            raise ValueError("tg_bot_user must be set when creating telegram bot")

        bot_data["username"] = tg_bot_user.username
    else:
        bot_data["whatsapp_app_name"] = whatsapp_token_info.data.application
        phone_number = "".join(
            re.findall(r"\d", whatsapp_phone_number_info.display_phone_number)
        )
        bot_data["whatsapp_from_phone_number"] = phone_number
        if whatsapp_business_account_id:
            bot_data["whatsapp_business_account_id"] = whatsapp_business_account_id

    existing_bot = find_existing_bot_sync(
        bot_type, token,
        bot_data["username"],
        whatsapp_app_id,
        whatsapp_from,
        with_for_update=True,
        whatsapp_from_phone_number=phone_number,
    )

    if existing_bot:
        group = group or existing_bot.group

        existing_bot.update_sync(
            **bot_data, group=group, status="enabled", no_commit=True
        )

        accept_agreement_datetime = datetime.utcnow() if is_accepted_agreement else (
            group.accept_agreement_datetime)
        group.update_sync(
            status="enabled",
            lang=group.lang or lang,
            timezone=group.timezone or timezone,
            is_accepted_agreement=group.is_accepted_agreement or is_accepted_agreement,
            accept_agreement_datetime=accept_agreement_datetime,
            no_commit=True,
        )

        sess().add(
            AdminBotAnalyticAction(
                type="bot_recreated",
                user=owner,
                group=group,
                bot=existing_bot,
            )
        )
        sess().commit()
        return existing_bot

    if group and group.bot:
        existing_bot = group.bot

        existing_bot.update_sync(
            **bot_data, group=group, status="enabled", no_commit=True
        )

        accept_agreement_datetime = datetime.utcnow() if is_accepted_agreement else (
            group.accept_agreement_datetime)
        group.update_sync(
            status="enabled",
            lang=group.lang or lang,
            timezone=group.timezone or timezone,
            is_accepted_agreement=group.is_accepted_agreement or is_accepted_agreement,
            accept_agreement_datetime=accept_agreement_datetime,
            no_commit=True,
        )

        sess().add(
            AdminBotAnalyticAction(
                type="bot_recreated",
                user=owner,
                group=group,
                bot=existing_bot,
            )
        )
        sess().commit()
        return existing_bot

    if group is None:
        group_name = bot_data["username"] or bot_data.get("whatsapp_app_name")
        if not group_name:
            raise ValueError("Group name can not be empty")

        group = create_group_sync(
            group_name, owner,
            is_accepted_agreement=is_accepted_agreement,
            country_iso_code=country_iso_code,
            timezone=timezone, lang=lang,
            no_commit=True,
        )
    else:
        accept_agreement_datetime = datetime.utcnow() if is_accepted_agreement else (
            group.accept_agreement_datetime)
        group.update_sync(
            status="enabled",
            lang=group.lang or lang,
            timezone=group.timezone or timezone,
            is_accepted_agreement=group.is_accepted_agreement or is_accepted_agreement,
            accept_agreement_datetime=group.accept_agreement_datetime or
                                      accept_agreement_datetime,
            no_commit=True,
        )

    bot = ClientBot(
        group, **bot_data,
    )
    sess().add(bot)

    sess().add(
        AdminBotAnalyticAction(
            type="bot_created",
            user=owner,
            group=group,
            bot=bot,
        )
    )
    sess().commit()
    return bot


@db_func
def set_custom_menu_buttons(
        bot_id: int,
        buttons: list[schemas.SetAdminBotCustomMenuButtonSchema]
) -> None:
    existing_ids = [button.id for button in buttons if isinstance(button.id, int)]
    sess().query(CustomMenuButton).filter(CustomMenuButton.bot_id == bot_id).filter(
        CustomMenuButton.id.notin_(existing_ids)
    ).delete()
    sess().flush()

    db_buttons = []

    for button in buttons:
        data = button.dict(exclude_unset=True, exclude={"translations", "id"})

        if isinstance(button.id, str):
            custom_menu_button = CustomMenuButton(
                bot_id=bot_id,
                **data
            )
            sess().add(custom_menu_button)
            sess().flush()
        else:
            custom_menu_button = sess().query(CustomMenuButton).filter(
                CustomMenuButton.id == button.id,
                CustomMenuButton.bot_id == bot_id
            ).first()
            if custom_menu_button:
                for key, value in data.items():
                    setattr(custom_menu_button, key, value)

        db_buttons.append(custom_menu_button)

        if button.translations:
            update_object_translations_sync(custom_menu_button, button.translations)

    sess().commit()
    return None
