from sqlalchemy import and_, or_, select
from sqlalchemy.orm import aliased

import schemas
from db import db_func, sess
from db.helpers import get_query_by_operation, order_by_slice_and_result
from db.models import (
    Brand, ClientBot, CustomMenuButton, Group, Translation,
    UserClientBotActivity,
)
from db.types.operation import Operation


@db_func
def get_bot_by_brand(brand_id: int) -> ClientBot | None:
    query = sess().query(ClientBot)
    query = query.filter(
        ClientBot.group.has(
            and_(
                Group.status == "enabled",
                Group.brand.has(id=brand_id),
            )
        )
    )

    query = query.filter(ClientBot.status == "enabled")
    return query.one_or_none()


@db_func
def get_bot_by_store(store_id: int) -> ClientBot | None:
    query = sess().query(ClientBot)
    query = query.filter(
        ClientBot.group.has(
            and_(
                Group.brand.has(Brand.stores.any(id=store_id)),
                Group.status == "enabled"
            )
        )
    )
    query = query.filter(ClientBot.status == "enabled")
    return query.one_or_none()


@db_func
def get_bot_name(bot_id: int) -> str | None:
    query = sess().query(ClientBot)
    result = query.filter(ClientBot.id == bot_id).one_or_none()
    if result and result.username:
        return result.username
    return None


@db_func
def get_bots_to_stop(
        is_friendly: bool,
        bot_type: schemas.BotTypeLiteral
) -> list[ClientBot]:
    stmt = select(ClientBot)
    stmt = stmt.where(ClientBot.is_friendly.is_(is_friendly))
    stmt = stmt.where(ClientBot.bot_type == bot_type)
    stmt = stmt.where(ClientBot.is_started.is_(True))
    stmt = stmt.with_for_update()
    bots = sess().scalars(stmt).all()
    if not bots:
        sess().commit()
    return bots


def find_existing_bot_sync(
        bot_type: str,
        token: str,
        username: str | None = None,
        whatsapp_app_id: str | None = None,
        whatsapp_from: str | None = None,
        with_for_update: bool = False,
        whatsapp_from_phone_number: str | None = None,
) -> ClientBot | None:
    conditions = [ClientBot.token == token]
    if username:
        conditions.append(ClientBot.username == username)
    if whatsapp_app_id:
        conditions.append(ClientBot.whatsapp_app_id == whatsapp_app_id)
    if whatsapp_from:
        conditions.append(ClientBot.whatsapp_from == whatsapp_from)
    if whatsapp_from_phone_number:
        conditions.append(ClientBot.whatsapp_from_phone_number == whatsapp_from_phone_number)

    query = sess().query(ClientBot)
    if with_for_update:
        query = query.with_for_update().populate_existing()
    query = query.filter(ClientBot.bot_type == bot_type).filter(or_(*conditions))

    return query.one_or_none()


@db_func
def find_existing_bot(
        bot_type: str,
        token: str,
        username: str | None = None,
        whatsapp_app_id: str | None = None,
        whatsapp_from: str | None = None,
        with_for_update: bool = False,
) -> ClientBot | None:
    return find_existing_bot_sync(
        bot_type, token, username, whatsapp_app_id, whatsapp_from, with_for_update
    )


@db_func
def get_custom_menu_buttons(
        bot_id: int,
        profile_langs: list[str]
) -> list[tuple[CustomMenuButton, Translation | None]] | None:
    button_translation = aliased(Translation)

    results = sess().execute(
        select(
            CustomMenuButton,
            button_translation,
        )
        .outerjoin(
            button_translation,
            button_translation.id.in_(
                tuple(
                    Translation.build_id(CustomMenuButton, lang)
                    for lang in profile_langs
                )
            )
        )
        .where(CustomMenuButton.bot_id == bot_id)
        .order_by(CustomMenuButton.position)
    ).fetchall()

    if not results:
        return None

    return [
        (custom_menu_button, translation if translation else None)
        for custom_menu_button, translation in results
    ]


@db_func
def get_latest_bot_for_two_users(
        user_a_id: int,
        user_b_id: int,
) -> ClientBot | None:
    user_bot_activity_a = aliased(UserClientBotActivity)
    user_bot_activity_b = aliased(UserClientBotActivity)

    return sess().scalar(
        select(ClientBot)
        .join(user_bot_activity_a, user_bot_activity_a.bot_id == ClientBot.id)
        .join(user_bot_activity_b, user_bot_activity_b.bot_id == ClientBot.id)
        .where(
            user_bot_activity_a.user_id == user_a_id,
            user_bot_activity_b.user_id == user_b_id,
            user_bot_activity_a.is_entered_bot.is_(True),
            user_bot_activity_b.is_entered_bot.is_(True),
            user_bot_activity_a.is_active.is_(True),
            user_bot_activity_b.is_active.is_(True),
            user_bot_activity_a.is_accept_agreement.is_(True),
            user_bot_activity_b.is_accept_agreement.is_(True)
        )
        .order_by(
            user_bot_activity_a.last_activity.desc(),
            user_bot_activity_b.last_activity.desc(),
            user_bot_activity_a.id.desc(),
        )
        .limit(1)
    )


@db_func
def get_admin_client_bots(
    operation: Operation = "list",
    search_text: str | None = None,
    position: int = 0,
    limit: int | None = None,
) -> list[ClientBot] | int:
    query = get_query_by_operation(ClientBot, operation)
    query = query.filter(
        ClientBot.status == "enabled",
        ClientBot.is_friendly.is_not(True),
    )

    if operation == "count":
        return query.scalar() - position

    if search_text:
        query = query.filter(
            or_(
                ClientBot.username.contains(search_text),
                ClientBot.whatsapp_app_name.contains(search_text)
            )
        )

    query = query.with_entities(
        ClientBot.id,
        ClientBot.display_name,
        ClientBot.status,
        ClientBot.bot_type,
        ClientBot.group_id,
    )

    return order_by_slice_and_result(
        query, position, limit, ClientBot.id.desc(), operation
    ) 