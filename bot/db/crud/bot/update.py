from sqlalchemy import update

from db import db_func, sess
from db.models import ClientBot, UserClientBotActivity


@db_func
def stop_bots(
        *ids: int
):
    update_stmt = update(ClientBot)
    update_stmt = update_stmt.where(ClientBot.id.in_(ids))
    update_stmt = update_stmt.values(is_started=False)
    sess().execute(update_stmt)
    sess().commit()


@db_func
def change_bot(
        bot_id: int,
        update_data: dict,
):
    update_stmt = update(ClientBot)
    update_stmt = update_stmt.where(ClientBot.id == bot_id)
    update_stmt = update_stmt.values(**update_data)
    sess().execute(update_stmt)

    update_stmt = update(UserClientBotActivity)
    update_stmt = update_stmt.where(UserClientBotActivity.bot_id == bot_id)
    update_stmt = update_stmt.values(is_entered_bot=False)
    sess().execute(update_stmt)

    sess().commit()
