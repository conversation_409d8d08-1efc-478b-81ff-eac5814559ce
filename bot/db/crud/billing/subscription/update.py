from dataclasses import dataclass
from datetime import datetime, timezone
from operator import itemgetter

import stripe
from sqlalchemy import select, update

import schemas
from core.billing.stripe_client import bstripe
from db import db_func, sess
from db.decorators.other import safe_deadlock_handler
from db.models import (
    BillingProduct, BillingServicePacket, BillingServicePacketItem, BillingSubscription,
    BillingSubscriptionItem, BillingUsageRecord, Brand, Group, Scope,
)
from loggers import J<PERSON>NLogger
from schemas import BillingProductCode, BillingSubscriptionStatus
from utils.helpers import is_objects_keys_equal
from utils.platform_admins import sync_send_message_to_platform_admins
from ..subscription_item.helpers import make_db_subscription_item_data
from ...scope.read import check_access_to_action_sync
from ...store.product.read import get_products_to_disable
from ...store.store.read import get_stores_to_disable


@dataclass
class CreateOrUpdateSubscriptionResult:
    subscription: BillingSubscription | None = None
    packet: BillingServicePacket | None = None
    group: Group | None = None
    previous_status: BillingSubscriptionStatus | None = None
    status_changed: bool = False
    created_new_subscription: bool = False


@db_func
@safe_deadlock_handler
def create_or_update_subscription(
        data: stripe.Subscription,
        logger: JSONLogger,
) -> CreateOrUpdateSubscriptionResult:
    result = CreateOrUpdateSubscriptionResult()

    if group_id := data.metadata.get("7loc_profile_id"):
        group = Group.get_sync(group_id)
        customer_id = None
    else:
        customer_id = data.customer.id if isinstance(
            data.customer, stripe.Customer
        ) else data.customer
        group = Group.get_sync(stripe_customer_id=customer_id)

    result.group = group

    if not group:
        sync_send_message_to_platform_admins(
            f"Profile not found for subscription: {group_id = }, {customer_id = }"
        )
        logger.debug(
            f"Profile not found for subscription: {group_id = }, {customer_id = }", {
                "customer": data.customer,
                "metadata": data.metadata,
            }
        )
        return result

    new_status = schemas.BillingSubscriptionStatus(data.status)
    subscription_data = {
        "group": group,
        "stripe_id": data.stripe_id,
        "currency": data.currency,
        "description": data.description,
        "livemode": data.livemode,
        "status": new_status,
        "stripe_created": datetime.fromtimestamp(data.created, timezone.utc),
        "start_date": datetime.fromtimestamp(data.start_date, timezone.utc),
        "ended_at": datetime.fromtimestamp(
            data.ended_at, timezone.utc
        ) if data.ended_at else None,
        "current_period_start": datetime.fromtimestamp(
            data.current_period_start, timezone.utc
        ),
        "current_period_end": datetime.fromtimestamp(
            data.current_period_end, timezone.utc
        ),
        "trial_start": datetime.fromtimestamp(
            data.trial_start, timezone.utc
        ) if data.trial_start else None,
        "trial_end": datetime.fromtimestamp(
            data.trial_end, timezone.utc
        ) if data.trial_end else None,
        "cancel_at": datetime.fromtimestamp(
            data.cancel_at, timezone.utc
        ) if data.cancel_at else None,
        "cancel_at_period_end": data.cancel_at_period_end,
        "canceled_at": datetime.fromtimestamp(
            data.canceled_at, timezone.utc
        ) if data.canceled_at else None,
        "cancellation_details": data.cancellation_details,
    }

    need_reset_usage = False

    existing_subscription = BillingSubscription.get_sync(
        stripe_id=data.id, for_update=True
    )
    if existing_subscription:
        logger.debug(
            f"Updating existing subscription {data.id}", {
                "subscription_id": existing_subscription.id,
                "db_subscription_data": subscription_data
            }
        )

        need_reset_usage = not is_objects_keys_equal(
            existing_subscription, subscription_data,
            (
                "current_period_start",
                "current_period_end",
            )
        )

        if new_status != existing_subscription.status:
            result.previous_status = existing_subscription.status
            result.status_changed = True

        subscription = existing_subscription.update_sync(
            **subscription_data,
            no_commit=True
        )

        subscription_item_ids = [el.id for el in data["items"]]

        existing_items: dict[str, BillingSubscriptionItem] = {
            el.stripe_item_id: el
            for el in (
                sess().scalars(
                    select(BillingSubscriptionItem)
                    .where(
                        BillingSubscriptionItem.subscription_id ==
                        existing_subscription.id,
                        BillingSubscriptionItem.stripe_item_id.in_(
                            subscription_item_ids
                        )
                    )
                ).all()
            )
        }

        items_to_delete = sess().execute(
            select(
                BillingSubscriptionItem.id,
                BillingSubscriptionItem.product_id,
            )
            .where(
                BillingSubscriptionItem.subscription_id ==
                existing_subscription.id,
                BillingSubscriptionItem.stripe_item_id.not_in(existing_items.keys())
            )
        ).fetchall()

        sess().execute(
            update(BillingSubscriptionItem)
            .values(
                {
                    "is_deleted": True,
                }
            )
            .where(
                # subscription item id
                BillingSubscriptionItem.id.in_(map(itemgetter(0), items_to_delete))
            )
        )

        sess().execute(
            update(BillingUsageRecord)
            .values(
                {
                    "reported_quantity": 0,
                }
            )
            .where(
                # product id
                BillingUsageRecord.product_id.in_(map(itemgetter(1), items_to_delete)),
                BillingUsageRecord.group_id == group.id
            )
        )

        created = False
    else:
        logger.debug(
            f"Creating new subscription {data.id}", {
                "db_subscription_data": subscription_data
            }
        )
        subscription = BillingSubscription(**subscription_data)
        sess().add(subscription)

        existing_items = {}
        created = True
        result.created_new_subscription = True

    result.subscription = subscription

    stripe_price_ids = [
        item_data.price.id
        for item_data in data["items"]
    ]

    packet_items: dict[str, BillingServicePacketItem] = {
        item.stripe_price_id: item
        for item in (
            sess().scalars(
                select(BillingServicePacketItem)
                .where(BillingServicePacketItem.stripe_price_id.in_(stripe_price_ids))
            ).all()
        )
    }

    result.packet = BillingServicePacket.get_sync(
        list(packet_items.values())[0].packet_id
    )

    items: list[BillingSubscriptionItem] = []
    product_ids_to_reset_usages: set[int] = set()

    item_data: stripe.SubscriptionItem
    for item_data in data["items"]:
        if item_data.price.id not in packet_items:
            logger.debug(
                f"Received subscription with unknown price id {item_data.price.id}", {
                    "item": item_data,
                    "group": group,
                }
            )
            sync_send_message_to_platform_admins(
                f"Received subscription with unknown price id {item_data.price.id}"
            )
            continue

        packet_item = packet_items[item_data.price.id]

        if item_data.price.recurring.meter == packet_item.meter_id:
            meter_event_name = packet_item.meter_event_name
        elif item_data.price.recurring.meter:
            logger.debug(
                "Price meter differs from packet_item.meter_id. Retrieving "
                "meter_event_name from stripe"
            )
            meter_event_name = bstripe.billing.meters.retrieve(
                item_data.price.recurring.meter
            ).event_name
        else:
            meter_event_name = None

        db_item_data = make_db_subscription_item_data(
            subscription,
            packet_item,
            item_data,
            meter_event_name,
        )

        existing_item = existing_items.get(item_data.id)
        if existing_item:
            logger.debug(
                f"Updating existing item {item_data.id}", {
                    "item_id": existing_item.id,
                    "item_data": item_data,
                    "db_item_data": item_data
                }
            )
            item = existing_item.update_sync(db_item_data, no_commit=True)
            if need_reset_usage:
                product_ids_to_reset_usages.add(item.product_id)
        else:
            logger.debug(
                f"Creating new subscription item {item_data.id}", {
                    "item_data": item_data,
                    "db_item_data": item_data
                }
            )
            item = BillingSubscriptionItem(**db_item_data)
            sess().add(item)
        items.append(item)

    if product_ids_to_reset_usages:
        sess().execute(
            update(BillingUsageRecord)
            .values(
                {
                    "used_quantity": 0,
                    "reported_quantity": 0,
                }
            )
            .where(
                BillingUsageRecord.product_id.in_(product_ids_to_reset_usages),
                BillingUsageRecord.group_id == group.id
            )
        )

    # disabling objects, not included in subscription
    if subscription.status in (
            BillingSubscriptionStatus.ACTIVE,
            BillingSubscriptionStatus.TRIALING,
    ):
        brand = Brand.get_sync(group_id=group.id)
        stores = get_stores_to_disable(brand.id)
        products = get_products_to_disable(brand.id)

        def get_allowed_product_count(product_code: BillingProductCode):
            product_id = BillingProduct.get_sync(code=product_code).id
            return sum(
                map(
                    lambda x: x.quantity,
                    filter(lambda x: x.product_id == product_id, items)
                )
            )

        allowed_stores = get_allowed_product_count(BillingProductCode.STORE)
        allowed_products = get_allowed_product_count(BillingProductCode.PRODUCT)

        for store in stores[allowed_stores:]:
            store.is_enabled = False

        for product in products[allowed_products:]:
            product.is_enabled = False

    if subscription.status in (
            BillingSubscriptionStatus.ACTIVE,
            BillingSubscriptionStatus.INCOMPLETE,
            BillingSubscriptionStatus.TRIALING,
            BillingSubscriptionStatus.PAST_DUE,
            BillingSubscriptionStatus.PAUSED,
    ) and not check_access_to_action_sync(
        "billing:tester", "profile", group.id,
        with_for_update=True,
    ):
        sess().add(
            Scope(
                target="profile",
                profile=group,
                scope=f"billing:tester",
            )
        )

    sess().commit()

    created_or_updated = "created" if created else "updated"
    logger.debug(
        f"Subscription {created_or_updated}.", {
            "subscription": subscription,
            "items": items,
        }
    )

    return result
