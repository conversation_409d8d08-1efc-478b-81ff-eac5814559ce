from sqlalchemy import exists, select
from sqlalchemy.orm import aliased

from db import db_func, sess
from db.models import (
    BillingProduct, BillingServicePacket, BillingServicePacketItem, BillingSubscription,
    BillingSubscriptionItem, Translation,
)
from schemas import BillingSubscriptionStatus


@db_func
def get_profile_subscription(group_id: int, lang: str) -> list[tuple[
    BillingSubscription,
    BillingSubscriptionItem,
    BillingServicePacketItem,
    Translation | None,
    BillingServicePacket,
    Translation | None,
    BillingProduct
]]:
    packet_translation = aliased(Translation)
    packet_item_translation = aliased(Translation)

    # noinspection PyTypeChecker
    return sess().execute(
        select(
            BillingSubscription,
            BillingSubscriptionItem,
            BillingServicePacketItem,
            packet_item_translation,
            BillingServicePacket,
            packet_translation,
            BillingProduct
        )
        .join(BillingSubscription.items)
        .join(BillingSubscriptionItem.packet_item)
        .join(BillingServicePacketItem.packet)
        .outerjoin(
            packet_translation,
            packet_translation.filter(BillingServicePacket, lang)
        )
        .outerjoin(
            packet_item_translation,
            packet_item_translation.filter(BillingServicePacketItem, lang)
        )
        .join(BillingSubscriptionItem.product)
        .where(
            BillingSubscription.group_id == group_id,
            BillingSubscription.status.in_(
                (
                    *BillingSubscription.ACTIVE_STATUSES,
                    BillingSubscriptionStatus.INCOMPLETE,
                    BillingSubscriptionStatus.PAUSED,
                )
            ),
            BillingSubscriptionItem.is_deleted.is_(False),
        )
        .group_by(BillingSubscription.id, BillingSubscriptionItem.id)
        .order_by(
            BillingSubscription.stripe_created.desc(),
            BillingSubscription.id.desc(),
            BillingServicePacket.position,
            BillingServicePacket.id,
            BillingServicePacketItem.position,
            BillingServicePacketItem.id,
        )
    ).fetchall()


@db_func
def check_profile_subscription(group_id: int, only_active: bool = True) -> bool:
    stmt = (
        exists(BillingSubscription.id)
        .where(BillingSubscription.group_id == group_id)
    )
    if only_active:
        stmt = stmt.where(BillingSubscription.is_active)

    return sess().scalar(select(stmt)) or False


@db_func
def get_profile_subscription_currency(group_id: int) -> str | None:
    return sess().scalar(
        select(BillingSubscription.currency)
        .where(
            BillingSubscription.group_id == group_id,
            BillingSubscription.status.in_(
                (
                    *BillingSubscription.ACTIVE_STATUSES,
                    BillingSubscriptionStatus.INCOMPLETE,
                    BillingSubscriptionStatus.PAUSED,
                )
            )
        )
    )
