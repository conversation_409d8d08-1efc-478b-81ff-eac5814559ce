from sqlalchemy import and_, exists, func, select

from db import db_func, sess
from db.connection import Base
from db.models import (
    BillingProduct, BillingSubscription, BillingSubscriptionItem, Brand, Group,
)
from schemas import BillingProductCode


@db_func
def calculate_licensed_item_usage(
        model: type[Base],
        group_id: int,
) -> int:
    stmt = select(
        func.count(model.id.distinct())
    )
    if hasattr(model, "brand"):
        stmt = stmt.join(model.brand)
        stmt = stmt.where(Brand.group_id == group_id)
    elif hasattr(model, "group"):
        stmt = stmt.join(model.group)
        stmt = stmt.where(Group.id == group_id)
    else:
        raise ValueError("Invalid model")

    if hasattr(model, "is_deleted"):
        stmt = stmt.where(model.is_deleted.is_(False))
    if hasattr(model, "is_enabled"):
        stmt = stmt.where(model.is_enabled.is_(True))
    if hasattr(model, "status"):
        stmt = stmt.where(model.status == "enabled")

    return sess().scalar(stmt)


@db_func
def get_subscription_item_for_product(
        group_id: int,
        product_code: BillingProductCode,
) -> BillingSubscriptionItem | None:
    return sess().scalar(
        select(BillingSubscriptionItem)
        .join(BillingSubscriptionItem.subscription)
        .join(BillingSubscriptionItem.product)
        .where(
            BillingProduct.code == product_code,
            BillingSubscription.group_id == group_id,
            BillingSubscription.is_active,
            BillingSubscriptionItem.is_deleted.is_(False)
        )
        .order_by(
            BillingSubscription.stripe_created.desc(),
            BillingSubscription.id.desc(),
        )
        .limit(1)
    )


@db_func
def get_subscription_and_item_by_item(
        group_id: int, item_id: int
) -> tuple[BillingSubscriptionItem, BillingSubscription] | None:
    return sess().execute(
        select(
            BillingSubscriptionItem, BillingSubscription
        )
        .join(BillingSubscriptionItem.subscription)
        .where(
            BillingSubscription.is_active,
            BillingSubscription.group_id == group_id,
            BillingSubscriptionItem.id == item_id,
            BillingSubscriptionItem.is_deleted.is_(False),
        )
    ).fetchone()


@db_func
def get_is_item_exist_for_product(
        group_id: int,
        *product_codes: BillingProductCode,
) -> bool:
    return sess().scalar(
        select(
            exists(BillingSubscriptionItem)
            .where(
                BillingSubscriptionItem.subscription.has(
                    and_(
                        BillingSubscription.group_id == group_id,
                        BillingSubscription.is_active,
                    )
                ),
                BillingSubscriptionItem.product.has(
                    BillingProduct.code.in_(product_codes)
                )
            )
        )
    )
