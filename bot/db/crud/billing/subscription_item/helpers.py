from decimal import Decimal

import stripe

from db.models import BillingServicePacketItem, BillingSubscription
from schemas import (
    BillingRecurringInterval, BillingScheme, BillingTierSchema,
    BillingTiersMode, BillingTransformQuantity, BillingTransformQuantityRound,
    BillingUsageType,
)


def make_db_subscription_item_data(
        subscription: BillingSubscription,
        packet_item: BillingServicePacketItem,
        item_data: stripe.SubscriptionItem,
        meter_event_name: str | None,
):
    return {
        "subscription": subscription,
        "is_deleted": False,
        "product_id": packet_item.product_id,
        "packet_item": packet_item,
        "quantity": item_data.get("quantity") or 0,
        "billing_scheme": BillingScheme(item_data.price.billing_scheme),
        "tiers_mode": (
            BillingTiersMode(tiers_mode)
            if (tiers_mode := item_data.price.get("tiers_mode"))
            else None
        ),
        "tiers": [
            BillingTierSchema(
                up_to=tier.get("up_to"),
                flat_amount=(
                    Decimal(tier.flat_amount_decimal) / 100
                    if tier.flat_amount_decimal is not None else
                    Decimal(round(tier.flat_amount / 100, 2))
                    if tier.flat_amount is not None else 0
                ),
                unit_amount=(
                    Decimal(tier.unit_amount_decimal) / 100
                    if tier.unit_amount_decimal is not None else
                    Decimal(round(tier.unit_amount / 100, 2))
                    if tier.unit_amount is not None else 0
                ),
            )
            for tier in tiers
        ] if (tiers := item_data.price.get("tiers")) else None,
        "unit_amount": (
            Decimal(item_data.price.unit_amount_decimal) / 100
            if item_data.price.unit_amount_decimal is not None else
            Decimal(round(item_data.price.unit_amount / 100, 2))
            if item_data.price.unit_amount is not None else 0
        ) if item_data.price.billing_scheme == "per_unit" else 0,
        "recurring_interval": BillingRecurringInterval(
            item_data.price.recurring.interval
        ),
        "recurring_interval_count": item_data.price.recurring.interval_count,
        "usage_type": BillingUsageType(
            item_data.price.recurring.usage_type
        ),
        "meter_id": item_data.price.recurring.meter,
        "meter_event_name": meter_event_name,
        "transform_quantity": BillingTransformQuantity(
            divide_by=transform_quantity.divide_by,
            round=BillingTransformQuantityRound(transform_quantity.round),
        ) if (transform_quantity := item_data.price.get(
            "transform_quantity"
        )) else None,
        "stripe_item_id": item_data.id,
        "stripe_price_id": item_data.price.id,
    }
