from typing import Literal

from sqlalchemy import and_, select

from db import db_func, sess
from db.models import (
    BillingProduct, BillingSubscription, BillingSubscriptionItem, BillingUsageRecord,
    Group,
)
from schemas import (
    BILLING_PRODUCTS, BillingProductCode, BillingUsageRecordToReport, BillingUsageType,
)
from ...scope.read import check_access_to_action_sync


@db_func
def get_usage_records_to_report(limit: int) -> list[BillingUsageRecordToReport]:
    return [
        BillingUsageRecordToReport.from_orm(el)
        for el in (
            sess().execute(
                select(
                    BillingUsageRecord.id,
                    BillingUsageRecord.used_quantity,
                    BillingUsageRecord.reported_quantity,
                    BillingSubscriptionItem.meter_event_name,
                    Group.stripe_customer_id
                )
                .join(BillingUsageRecord.group)
                .join(BillingSubscription, BillingSubscription.group_id == Group.id)
                .join(
                    BillingSubscriptionItem, and_(
                        BillingSubscriptionItem.subscription_id ==
                        BillingSubscription.id,
                        BillingSubscriptionItem.product_id ==
                        BillingUsageRecord.product_id,
                    )
                )
                .where(
                    BillingSubscription.is_active,
                    BillingSubscriptionItem.is_deleted.is_(False),
                    BillingSubscriptionItem.meter_event_name.is_not(None),
                    BillingUsageRecord.reported_quantity <
                    BillingUsageRecord.used_quantity,
                )
                .limit(limit)
                .group_by(BillingUsageRecord.id)
            ).fetchall()
        )
    ]


def get_subscription_items_for_product(
        group_id: int,
        product_code: BillingProductCode | None = None,
        product_id: int | None = None,
        with_lock: bool = False,
) -> list[BillingSubscriptionItem]:
    stmt = (
        select(BillingSubscriptionItem)
        .join(BillingSubscriptionItem.subscription)
    )
    stmt = stmt

    if product_id:
        stmt = stmt.where(BillingSubscriptionItem.product_id == product_id)
    elif product_code:
        stmt = (
            stmt.join(BillingSubscriptionItem.product)
            .where(BillingProduct.code == product_code)
        )

    stmt = stmt.where(
        BillingSubscription.group_id == group_id,
        BillingSubscriptionItem.is_deleted.is_(False),
        BillingSubscription.is_active,
    )

    stmt = stmt.order_by(
        BillingSubscriptionItem.usage_type == BillingUsageType.METERED,
        BillingSubscription.current_period_end.desc(),
        BillingSubscriptionItem.id,
    )
    if with_lock:
        stmt = stmt.with_for_update()

    return sess().scalars(stmt).all()


def get_product_allowed_quantity(
        group_id: int,
        product_code: BillingProductCode,
        product_id: int | None = None,
        with_lock: bool = False,
        return_not_exists: bool = False,
        return_items: bool = False
) -> (
        int | Literal["inf", "not_exists"] |
        tuple[
            int | Literal["inf", "not_exists"],
            list[BillingSubscriptionItem]
        ]
):
    # noinspection PyTypeChecker
    available_items = get_subscription_items_for_product(
        group_id, product_code, product_id, with_lock,
    )

    if not available_items:
        if not check_access_to_action_sync(
                "billing:tester", "profile", group_id
        ):
            allowed_quantity = BILLING_PRODUCTS[product_code].placeholder_quantity
        elif return_not_exists:
            if return_items:
                return "not_exists", []
            return "not_exists"
        else:
            allowed_quantity = 0
    elif any(map(lambda x: x.usage_type == BillingUsageType.METERED, available_items)):
        allowed_quantity = "inf"
    else:
        allowed_quantity = sum(map(lambda x: x.quantity, available_items))

    if return_items:
        return allowed_quantity, available_items

    return allowed_quantity


def get_metered_item_usage_sync(
        group_id: int,
        product_id: int | None = None,  # BillingProduct.id
        product_code: BillingProductCode | None = None,
) -> int:
    stmt = (
        select(BillingUsageRecord.used_quantity)
        .where(BillingUsageRecord.group_id == group_id)
    )

    if product_id:
        stmt = stmt.where(BillingUsageRecord.product_id == product_id)
    elif product_code:
        stmt = stmt.join(BillingUsageRecord.product)
        stmt = stmt.where(BillingProduct.code == product_code)
    else:
        raise ValueError("Either product_id or product_code must be specified!")

    return sess().scalar(stmt) or 0


@db_func
def get_metered_item_usage(
        group_id: int,
        product_id: int | None = None,  # BillingProduct.id
        product_code: BillingProductCode | None = None,
) -> int:
    return get_metered_item_usage_sync(group_id, product_id, product_code)


@db_func
def get_product_available_quantity(
        group_id: int,
        product_code: BillingProductCode,
        return_not_exists: bool = False,
) -> int | Literal["inf", "not_exists"]:
    allowed_quantity = get_product_allowed_quantity(
        group_id, product_code, return_not_exists=return_not_exists
    )
    if not isinstance(allowed_quantity, int):
        return allowed_quantity  # type:ignore

    used_quantity = get_metered_item_usage_sync(group_id, product_code=product_code)
    return allowed_quantity - used_quantity
