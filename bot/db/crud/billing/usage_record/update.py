from datetime import datetime, timezone

from sqlalchemy import case, delete, update
from sqlalchemy.exc import MultipleResultsFound

import exceptions
from db import db_func, sess
from db.decorators.other import safe_deadlock_handler
from db.models import (
    BillingProduct, BillingUsageRecord,
)
from loggers import <PERSON><PERSON><PERSON>ogger
from schemas import BILLING_PRODUCTS, BillingProductCode
from utils.date_time import utcnow
from utils.list import split_list
from utils.log_func_exec_time import log_func_exec_time
from .read import get_product_allowed_quantity


@db_func
@safe_deadlock_handler
def record_usage(
        group_id: int,
        product_code: BillingProductCode,
        quantity: int,
        logger: J<PERSON>NLogger,
        ignore_validation: bool = False,
) -> BillingUsageRecord | None:
    with log_func_exec_time("record_usage:get_allowed_info", logger):
        product = BillingProduct.get_sync(code=product_code, is_deleted=False)

        allowed_quantity, available_items = get_product_allowed_quantity(
            group_id, product.code, product.id,
            return_items=True,
        )

    with log_func_exec_time("record_usage:get_record", logger):
        try:
            usage_record = BillingUsageRecord.get_sync(
                group_id=group_id,
                product_id=product.id,
                for_update=True
            )
        except MultipleResultsFound:
            all_records = BillingUsageRecord.get_list_sync(
                group_id=group_id,
                product_id=product.id,
                for_update=True,
            )
            usage_record = all_records[-1]

            ids_to_delete = []
            for record in all_records[:-1]:
                ids_to_delete.append(record.id)
                usage_record.used_quantity += record.used_quantity
                usage_record.reported_quantity += record.reported_quantity

            sess().execute(
                delete(BillingUsageRecord, )
                .where(BillingUsageRecord.id.in_(ids_to_delete))
            )
            sess().flush()

    if not usage_record:
        logger.debug("Creating usage_record")
        usage_record = BillingUsageRecord(
            group_id=group_id,
            product=product,
            used_quantity=0,
            reported_quantity=0
        )
        sess().add(usage_record)

    available_quantity = (
        allowed_quantity - usage_record.used_quantity
        if allowed_quantity != "inf"
        else "inf"
    )

    if available_quantity != "inf" and available_quantity < quantity:
        message = "Not enough units available"
        if ignore_validation:
            message += " IGNORING"

        logger.debug(
            "Not enough units available", {
                "available_quantity": available_quantity,
                "required_quantity": quantity,
                "available_items": available_items,
                "ignore_validation": ignore_validation,
            }
        )

        if not ignore_validation:
            sess().rollback()
            if not available_items:
                raise exceptions.BillingProductNotAvailableError(
                    BILLING_PRODUCTS[product_code].name,
                    quantity,
                )
            raise exceptions.BillingQuotaExceededError(
                BILLING_PRODUCTS[product_code].name,
                quantity,
            )

    usage_record.used_quantity += quantity
    usage_record.last_record_datetime = utcnow()
    sess().commit()

    logger.debug(
        "Record usages: SUCCESS", {
            "usage_record": usage_record,
        }
    )

    return usage_record


@db_func
def save_reported_usages(records: dict[int, int]):
    if not records:
        return True

    rows = list(records.items())

    chunks = split_list(rows, 100)

    report_datetime = datetime.now(timezone.utc)

    for chunk in chunks:
        sess().execute(
            update(BillingUsageRecord)
            .values(
                {
                    "last_report_datetime": report_datetime,
                    "reported_quantity": case(
                        [
                            (
                                BillingUsageRecord.id == record_id,
                                BillingUsageRecord.reported_quantity + reported_quantity
                            )
                            for record_id, reported_quantity in chunk
                        ]
                    )
                }
            )
            .where(BillingUsageRecord.id.in_([el[0] for el in chunk]))
        )

    sess().commit()
    return True
