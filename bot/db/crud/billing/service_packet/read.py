from dataclasses import dataclass, field

from itertools import groupby
from sqlalchemy import and_, exists, func, or_, select
from sqlalchemy.orm import aliased

import exceptions
import schemas
from db import db_func, sess
from db.models import (
    BillingProduct, BillingPromoCode, BillingServicePacket, BillingServicePacketItem,
    BillingSubscription,
    BillingSubscriptionItem, Group, Translation,
)
from schemas import (BillingSubscriptionStatus, ServicePacketCountryMode)


def get_is_exists_packets_with_country_sync(
        country_code: str,
) -> bool:
    stmt = (
        exists()
        .where(
            BillingServicePacket.country_mode ==
            ServicePacketCountryMode.SPECIFIC,
            func.JSON_CONTAINS(
                BillingServicePacket.countries, f'"{country_code}"'
            ),
            BillingServicePacket.is_public.is_(True)
        )
    )
    return sess().scalar(select(stmt))


def filter_packets_by_country(
        country_code: str,
):
    if get_is_exists_packets_with_country_sync(country_code):
        return (
            or_(
                BillingServicePacket.country_mode ==
                ServicePacketCountryMode.ALL,
                and_(
                    BillingServicePacket.country_mode ==
                    ServicePacketCountryMode.SPECIFIC,
                    func.JSON_CONTAINS(
                        BillingServicePacket.countries, f'"{country_code}"'
                    )
                )
            )
        )
    else:
        return (
            BillingServicePacket.country_mode.in_(
                (ServicePacketCountryMode.ALL,
                 ServicePacketCountryMode.DEFAULT)
            )
        )


@dataclass
class PacketItemData:
    item: BillingServicePacketItem
    product: BillingProduct
    translations: dict[str, Translation]


@dataclass
class PacketData:
    packet: BillingServicePacket
    translations: dict[str, Translation]
    items: list[PacketItemData]


@db_func
def get_catalog_packets_data(
        country_code: str | None = None,
        only_public: bool = True,
        recurring_interval: schemas.BillingRecurringInterval | None = None,
        # when only translation on one lang has to be returned
        specific_lang: str | None = None,
) -> list[PacketData]:
    packet_translation = aliased(Translation)
    item_translation = aliased(Translation)

    stmt = (
        select(
            BillingServicePacket,
            packet_translation,
            BillingServicePacketItem,
            item_translation,
            BillingProduct,
        )
        .join(BillingServicePacket.items)
        .outerjoin(
            packet_translation, (
                packet_translation.filter(BillingServicePacket, specific_lang)
                if specific_lang else
                and_(
                    packet_translation.id.startswith(
                        f"{BillingServicePacket.__name__}-",
                    ),
                    packet_translation.id.endswith(
                        func.concat("-", BillingServicePacket.id)
                    )
                )
            )
        )
        .outerjoin(
            item_translation,
            item_translation.filter(BillingServicePacketItem, specific_lang)
            if specific_lang else
            and_(
                item_translation.id.startswith(
                    f"{BillingServicePacketItem.__name__}-",
                ),
                item_translation.id.endswith(
                    func.concat("-", BillingServicePacketItem.id)
                )
            )
        )
        .join(BillingServicePacketItem.product)
        .where(
            BillingServicePacket.is_deleted.is_(False),
            BillingServicePacketItem.is_deleted.is_(False),
        )
    )

    if only_public:
        stmt = stmt.where(BillingServicePacket.is_public.is_(True))

    if country_code:
        stmt = stmt.where(filter_packets_by_country(country_code))

    if recurring_interval:
        stmt = stmt.where(
            BillingServicePacket.recurring_interval == recurring_interval,
        )

    stmt = stmt.order_by(
        BillingServicePacket.position,
        BillingServicePacket.id.desc(),
        BillingServicePacketItem.position,
        BillingServicePacketItem.id.desc()
    )

    # noinspection PyTypeChecker
    rows: list[tuple[
        BillingServicePacket,
        Translation | None,
        BillingServicePacketItem,
        Translation | None,
        BillingProduct,
    ]] = sess().execute(stmt).fetchall()

    packets: list[PacketData] = []

    last_packet_data: PacketData | None = None
    last_item_data: PacketItemData | None = None
    for packet, packet_translation, item, item_translation, product in rows:
        if last_packet_data and last_packet_data.packet.id == packet.id:
            packet_data = last_packet_data
        else:
            packet_data = PacketData(packet, {}, [])
            packets.append(packet_data)

        if last_item_data and last_item_data.item.id == item.id:
            item_data = last_item_data
        else:
            item_data = PacketItemData(item, product, {})
            packet_data.items.append(item_data)

        if (packet_translation and packet_translation.lang not in
                packet_data.translations):
            packet_data.translations[packet_translation.lang] = packet_translation

        if item_translation and item_translation.lang not in item_data.translations:
            item_data.translations[item_translation.lang] = item_translation

        last_packet_data = packet_data
        last_item_data = item_data

    return packets


@db_func
def get_packet_catalog_data(
        packet_id: int, no_error: bool = False
) -> PacketData | None:
    packet = BillingServicePacket.get_sync(id=packet_id, is_deleted=False)
    if not packet:
        if no_error:
            return None
        raise exceptions.BillingServicePacketNotFoundError(packet_id)

    packet_translations: dict[str, Translation] = {
        translation.lang: translation
        for translation in (
            sess().scalars(
                select(Translation)
                .where(
                    Translation.id.in_(
                        tuple(
                            Translation.build_id(
                                packet, lang,
                            ) for lang in packet.langs_list
                        )
                    )
                )
            ).all()
        )
    }

    # noinspection PyTypeChecker
    items_data: list[tuple[
        BillingServicePacketItem, Translation | None, BillingProduct]] = (
        sess().execute(
            select(
                BillingServicePacketItem,
                Translation,
                BillingProduct,
            )
            .join(BillingServicePacketItem.product)
            .outerjoin(
                Translation,
                Translation.id.in_(
                    tuple(
                        Translation.build_id(
                            BillingServicePacketItem, lang,
                        ) for lang in packet.langs_list
                    )
                )
            )
            .where(
                BillingServicePacketItem.packet_id == packet.id,
                BillingServicePacketItem.is_deleted.is_(False),
            )
            .order_by(
                BillingServicePacketItem.position,
                BillingServicePacketItem.id.desc()
            )
        ).fetchall()
    )

    items = []

    for _, rows in groupby(items_data, lambda x: x[0].id):
        item = None
        product = None
        translations = {}

        for item, translation, product in rows:
            if translation:
                translations[translation.lang] = translation

        items.append(PacketItemData(item, product, translations))

    return PacketData(packet, packet_translations, items)


@dataclass
class ItemToSubscribe:
    item: BillingServicePacketItem
    quantity: int
    existing_stripe_item_id: str | None = None


@dataclass
class PacketToSubscribe:
    packet: BillingServicePacket
    items: list[ItemToSubscribe]


@dataclass
class CreateSubscriptionData:
    packet: BillingServicePacket
    items: list[ItemToSubscribe]
    items_to_delete: list[str] = field(default_factory=list)
    existing_subscription_stripe_id: str | None = None


@dataclass
class ToSubscribeData:
    subscription_data: CreateSubscriptionData
    subscriptions_to_cancel: list[str]
    promo_code: BillingPromoCode | None


@db_func
def get_to_subscribe_data(
        data: schemas.BillingSubscribeData,
        group: Group,
        only_public: bool = True,
) -> ToSubscribeData:
    promo_code = (
        BillingPromoCode.get_sync(code=data.promo_code)
        if data.promo_code else None
    )
    if data.promo_code and not promo_code:
        raise exceptions.BillingPromoCodeNotFoundError(data.promo_code)

    if promo_code and promo_code.packet_id and data.packet.id != promo_code.packet_id:
        raise exceptions.BillingPromoCodeInvalidPacketError(
            data.packet.id, promo_code.packet_id
        )

    get_packet_kwargs = {
        "id": data.packet.id,
        "is_deleted": False,
    }
    if only_public and not data.promo_code:
        get_packet_kwargs["is_public"] = True

    packet = BillingServicePacket.get_sync(**get_packet_kwargs)

    if (
            not packet or
            (
                    packet.country_mode == ServicePacketCountryMode.SPECIFIC and
                    group.country_code not in (packet.countries or [])
            ) or
            (
                    packet.country_mode == ServicePacketCountryMode.DEFAULT and
                    get_is_exists_packets_with_country_sync(
                        group.country_code
                    )
            )
    ):
        raise exceptions.BillingServicePacketNotFoundError(data.packet.id)

    items = []

    packet_items: dict[int, BillingServicePacketItem] = {
        el.id: el for el in (
            sess().scalars(
                select(BillingServicePacketItem)
                .where(
                    BillingServicePacketItem.packet_id == packet.id,
                    BillingServicePacketItem.is_deleted.is_(False),
                )
            ).all()
        )
    }

    not_found_items = [id for id in data.packet.items.keys() if id not in packet_items]
    if not_found_items:
        raise exceptions.BillingServicePacketItemsNotFoundError(not_found_items)

    for item in packet_items.values():
        item_data = data.packet.items.get(item.id)

        if item.quantity_adjustable and item_data:
            quantity = max(
                min(item_data.quantity or item.quantity, item.max_quantity),
                item.min_quantity
            )
        else:
            quantity = item.quantity

        items.append(ItemToSubscribe(item, quantity))

    subscription_data = CreateSubscriptionData(
        packet,
        items,
    )

    existing_subscription: BillingSubscription | None = sess().scalar(
        select(BillingSubscription)
        .join(BillingSubscription.items)
        .where(
            BillingSubscription.group_id == group.id,
            BillingSubscription.currency == packet.currency,
            BillingSubscription.is_active,
            BillingSubscriptionItem.recurring_interval == packet.recurring_interval,
            BillingSubscriptionItem.recurring_interval_count ==
            packet.recurring_interval_count
        )
        .order_by(BillingSubscription.stripe_created.desc())
        .limit(1)
    )
    if existing_subscription:
        subscription_data.existing_subscription_stripe_id = (
            existing_subscription.stripe_id
        )

        existing_items: dict[int, BillingSubscriptionItem] = {
            el.packet_item_id: el
            for el in (
                sess().scalars(
                    select(BillingSubscriptionItem)
                    .where(
                        BillingSubscriptionItem.subscription_id ==
                        existing_subscription.id,
                        BillingSubscriptionItem.is_deleted.is_(False)
                    )
                )
            )
        }

        existing_items_to_update = []
        for item_data in subscription_data.items:
            if existing_item := existing_items.get(item_data.item.id):
                item_data.existing_stripe_item_id = existing_item.stripe_item_id
                existing_items_to_update.append(item_data.item.id)

        subscription_data.items_to_delete = [
            item.stripe_item_id
            for packet_item_id, item in existing_items.items()
            if packet_item_id not in existing_items_to_update
        ]

    def get_subscriptions_to_cancel():
        stmt = (
            select(BillingSubscription.stripe_id)
            .where(
                BillingSubscription.group_id == group.id,
                BillingSubscription.status.in_(
                    (
                        BillingSubscriptionStatus.ACTIVE,
                        BillingSubscriptionStatus.TRIALING,
                        BillingSubscriptionStatus.INCOMPLETE,
                        BillingSubscriptionStatus.PAUSED,
                    )
                )
            )
        )
        if existing_subscription:
            stmt = stmt.where(BillingSubscription.id != existing_subscription.id)
        return sess().scalars(stmt).all()

    return ToSubscribeData(
        subscription_data=subscription_data,
        subscriptions_to_cancel=get_subscriptions_to_cancel(),
        promo_code=promo_code,
    )
