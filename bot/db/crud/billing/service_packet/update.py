from sqlalchemy import select

import exceptions
import schemas
from config import SYSTEM_NAME
from core.billing.stripe_client import bstripe
from db import db_func, sess
from db.crud.translation.update import (
    clear_object_updated_fields_translations_sync,
    update_object_translations_sync,
)
from db.models import (
    BillingProduct, BillingServicePacket,
    BillingServicePacketItem,
)
from loggers import JSONLogger
from schemas import BillingScheme, BillingTiersMode
from utils.helpers import is_objects_keys_equal
from ...helpers import check_items, delete_objects


@db_func
def update_catalog(
        data: schemas.UpdateBillingCatalogData
):
    logger = JSONLogger(
        "billing", "update_catalog", {
            "update_data": data,
        }
    )

    existing_packets_ids = []
    existing_items_ids = []
    products_codes = []
    meter_events_names = []

    for packet_data in data.packets:
        if packet_data.id and isinstance(packet_data.id, int):
            existing_packets_ids.append(packet_data.id)

        for item_data in packet_data.items:
            products_codes.append(item_data.product_code)
            if item_data.meter_event_name:
                meter_events_names.append(item_data.meter_event_name)

            if item_data.id and isinstance(item_data.id, int):
                existing_items_ids.append(item_data.id)

    existing_packets = check_items(
        existing_packets_ids,
        BillingServicePacket,
        exceptions.BillingServicePacketsIdNotUniqueError,
        exceptions.BillingServicePacketsNotFoundError,
    )

    existing_items = check_items(
        existing_items_ids,
        BillingServicePacketItem,
        exceptions.BillingServicePacketItemsIdNotUniqueError,
        exceptions.BillingServicePacketItemsNotFoundError,
    )

    delete_objects(BillingServicePacket, existing_packets)
    delete_objects(BillingServicePacketItem, existing_items)

    products = check_items(
        products_codes,
        BillingProduct,
        not_found_error=exceptions.BillingProductsNotFoundError,
        value_field="code",
    )

    # noinspection PyTypeChecker
    meter_ids_by_event_name: dict[str, str] = dict(
        sess().execute(
            select(
                BillingServicePacketItem.meter_event_name,
                BillingServicePacketItem.meter_id,
            ).where(
                BillingServicePacketItem.meter_event_name.in_(meter_events_names),
            ).order_by(
                BillingServicePacketItem.time_created
            )
        ).fetchall()
    )

    prices_to_archive = []

    def process_packet():
        packet = existing_packets.get(packet_data.id)

        packet_data_dict = packet_data.dict(
            exclude={"id", "items", "translations"},
        )

        if packet:
            packet.update_sync(
                packet_data_dict,
                position=packet_position,
                no_commit=True,
            )

            clear_object_updated_fields_translations_sync(
                packet, packet_data_dict, packet.langs_list
            )
        else:
            packet = BillingServicePacket.create_sync(
                **packet_data_dict,
                position=packet_position,
                no_commit=True,
            )
            sess().flush()

        if packet_data.translations:
            update_object_translations_sync(packet, packet_data.translations)

        return packet

    def process_item():
        item = existing_items.get(item_data.id)

        # if the usage type is metered, checking if meter has to be created
        if item_data.usage_type == schemas.BillingUsageType.METERED:
            if not item or item_data.meter_event_name != item.meter_event_name:
                if not item_data.meter_event_name:
                    raise ValueError("Invalid meter_event_name")

                meter_id = meter_ids_by_event_name.get(
                    item_data.meter_event_name
                )
                if not meter_id:
                    meter_id = create_meter()
            else:
                meter_id = item.meter_id
        else:
            meter_id = None
            item_data.meter_event_name = None

        product = products[item_data.product_code]

        if item_data.billing_scheme == BillingScheme.TIERED:
            item_data.transform_quantity = None

        item_data_dict = item_data.dict(
            exclude={"id", "product_code", "translations"},
        )

        item_data_dict["meter_id"] = meter_id
        item_data_dict["product"] = product

        # if stripe price need to be recreated
        if not item or product.id != item.product_id or not is_objects_keys_equal(
                item, item_data, (
                        "meter_event_name",
                        "usage_type",
                        "transform_quantity",
                        "billing_scheme",
                        "tiers_mode",
                        "tiers",
                        "unit_amount",
                )
        ):
            price_payload = {
                "currency": db_packet.currency,
                "nickname": f"{SYSTEM_NAME} {item_data.name}",
                "product": product.stripe_id,
                "recurring": {
                    "interval": packet_data.recurring_interval.value,
                    "interval_count": packet_data.recurring_interval_count,
                    "meter": meter_id,
                    "usage_type": item_data.usage_type.value,
                },
                "transform_quantity": {
                    "divide_by": item_data.transform_quantity.divide_by,
                    "round": item_data.transform_quantity.round.value
                } if item_data.transform_quantity else None,
                "billing_scheme": item_data.billing_scheme.value,
                "tiers_mode": (
                    (item_data.tiers_mode or BillingTiersMode.GRADUATED).value
                    if item_data.billing_scheme == schemas.BillingScheme.TIERED
                    else None
                ),
                "tiers": (
                    [{
                        "up_to": el.up_to,
                        "flat_amount_decimal": str(
                            el.flat_amount * 100
                        ) if el.flat_amount else None,
                        "unit_amount_decimal": str(
                            el.unit_amount * 100
                        ) if el.unit_amount or not el.flat_amount else None,
                    } for el in item_data.tiers]
                    if item_data.billing_scheme == schemas.BillingScheme.TIERED
                    else None
                ),
                "unit_amount_decimal": (
                    str(item_data.unit_amount * 100)
                    if item_data.billing_scheme == schemas.BillingScheme.PER_UNIT
                    else None
                ),
                "metadata": {
                    "system_name": SYSTEM_NAME,
                    "7loc_product_code": product.code.value,
                }
            }

            logger.debug(
                "Creating stripe price", {
                    "payload": price_payload,
                    "item_data": item_data,
                }
            )

            # as stripe price cannot be updated, recreating it
            stripe_price = bstripe.prices.create(price_payload)
            logger.debug(
                "Stripe price created", {
                    "payload": price_payload,
                    "item_data": item_data,
                    "price": dict(stripe_price),
                }
            )
            item_data_dict["stripe_price_id"] = stripe_price.id

            if item:
                prices_to_archive.append(item.stripe_price_id)

        # if stripe price nickname need to be updated
        elif item.name != item_data.name:
            price_payload = {
                "active": True,
                "nickname": f"{SYSTEM_NAME} {item_data.name}",
            }
            logger.debug(
                "Updating stripe price", item.stripe_price_id, {
                    "payload": price_payload,
                    "item": item_data,
                }
            )
            updated_price = bstripe.prices.update(
                item.stripe_price_id, price_payload
            )
            logger.debug(
                "Stripe price updated successfully!", item.stripe_price_id, {
                    "payload": price_payload,
                    "item": item_data,
                    "result": dict(updated_price),
                }
            )

        if item:
            item.update_sync(
                item_data_dict,
                packet=db_packet,
                position=item_position,
                no_commit=True,
            )

            clear_object_updated_fields_translations_sync(
                item, item_data_dict, db_packet.langs_list,
            )
        else:
            item = BillingServicePacketItem.create_sync(
                **item_data_dict,
                position=item_position,
                packet=db_packet,
                no_commit=False,
            )
            sess().flush()

        if item_data.translations:
            update_object_translations_sync(item, item_data.translations)

    def create_meter():
        meter_payload = {
            "display_name": (
                f"{SYSTEM_NAME} "
                f"{item_data.meter_event_name}"
            ),
            "event_name": item_data.meter_event_name,
            "default_aggregation": {"formula": "sum"},
            "customer_mapping": {
                "type": "by_id",
                "event_payload_key": "stripe_customer_id"
            },
            "value_settings": {"event_payload_key": "value"}
        }

        logger.debug(
            "Creating stripe meter", {
                "payload": meter_payload,
                "item_data": item_data
            }
        )
        stripe_meter = bstripe.billing.meters.create(meter_payload)
        logger.debug(
            "Meter created", {
                "payload": meter_payload,
                "item_data": item_data,
                "meter": dict(stripe_meter)
            }
        )
        meter_id = stripe_meter.id
        meter_ids_by_event_name[item_data.meter_event_name] = meter_id
        return meter_id

    for packet_position, packet_data in enumerate(data.packets):
        db_packet = process_packet()

        for item_position, item_data in enumerate(packet_data.items):
            process_item()

    sess().commit()

    for price_id in prices_to_archive:
        try:
            logger.debug("Deactivating stripe price", price_id)
            result = bstripe.prices.update(price_id, {"active": False})
        except Exception as e:
            logger.debug("Price deactivating error", price_id, e)
        else:
            logger.debug(
                "Price deactivated successfully", price_id, {
                    "result": result,
                }
            )

    return True
