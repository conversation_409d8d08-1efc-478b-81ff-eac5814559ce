from sqlalchemy import and_, or_, select

import schemas
from db import db_func, sess
from db.models import BillingPromoCode, BillingServicePacket


@db_func
def get_promo_code_list(
        params: schemas.GetBillingPromoCodesParams
) -> list[tuple[BillingPromoCode, BillingServicePacket | None]]:
    stmt = (
        select(BillingPromoCode, BillingServicePacket)
        .outerjoin(
            BillingServicePacket, and_(
                BillingPromoCode.packet_id == BillingServicePacket.id,
                BillingServicePacket.is_deleted.is_(False),
            )
        )
    )

    if params.search_text:
        stmt = stmt.where(
            or_(
                BillingPromoCode.name.contains(params.search_text.strip()),
                BillingPromoCode.code.contains(params.search_text.strip()),
            )
        )
    if params.trial_period_days is not None:
        stmt = stmt.where(
            BillingPromoCode.trial_period_days == params.trial_period_days
        )
    if params.packet_id:
        stmt = stmt.where(BillingPromoCode.packet_id == params.packet_id)
    if params.stripe_coupon:
        stmt = stmt.where(BillingPromoCode.stripe_coupon == params.stripe_coupon)

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    return sess().execute(stmt).fetchall()
