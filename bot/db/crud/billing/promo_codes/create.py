import random
import string
from typing import Awaitable

import schemas
from config import GENERATED_PROMO_CODE_LENGTH, MAX_GENERATE_PROMO_CODE_TRIES
from db import db_func
from db.models import BillingPromoCode, BillingServicePacket


def generate_promo_code(length: int):
    characters = string.ascii_uppercase + string.digits
    return ''.join(random.choices(characters, k=length))


def make_unique_promo_code(length: int):
    tries = 0
    while True:
        if tries >= MAX_GENERATE_PROMO_CODE_TRIES:
            raise RuntimeError(f"Can't generate promo code in {tries} tries")

        tries += 1

        promo_code = generate_promo_code(length)
        if not BillingPromoCode.is_exists_sync(
                code=promo_code,
                for_update=True,
                release_on_exists=True,
        ):
            return promo_code


@db_func
def create_promo_code(
        data: schemas.CreateBillingPromoCodeData,
        packet: BillingServicePacket | None = None,
) -> BillingPromoCode:
    if not data.code:
        data.code = generate_unique_promo_code()

    return BillingPromoCode.create_sync(**data.dict(), packet=packet)


def generate_new_promo_code() -> str | Awaitable[str]:
    return generate_unique_promo_code()


def generate_unique_promo_code() -> str | Awaitable[str]:
    return make_unique_promo_code(GENERATED_PROMO_CODE_LENGTH)
