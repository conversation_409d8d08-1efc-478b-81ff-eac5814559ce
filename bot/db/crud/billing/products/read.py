from typing import Iterable

from sqlalchemy import select

import schemas
from db import db_func, sess
from db.models import BillingProduct


@db_func
def get_products(
        include_ids: Iterable[int] | None = None,
        exclude_ids: Iterable[int] | None = None,
) -> list[BillingProduct]:
    stmt = (
        select(BillingProduct)
        .where(BillingProduct.is_deleted.is_(False))
    )
    if include_ids:
        stmt = stmt.where(BillingProduct.id.in_(include_ids))
    if exclude_ids:
        stmt = stmt.where(BillingProduct.id.not_in(exclude_ids))

    stmt = stmt.order_by(BillingProduct.position)
    stmt = stmt.order_by(BillingProduct.time_created.desc())
    return sess().scalars(stmt).all()


@db_func
def get_products_by_codes(
        codes: Iterable[schemas.BillingProductCode]
) -> list[BillingProduct]:
    stmt = (
        select(BillingProduct)
        .where(
            BillingProduct.is_deleted.is_(False),
            BillingProduct.code.in_(codes),
        )
    )

    stmt = stmt.order_by(BillingProduct.position)
    stmt = stmt.order_by(BillingProduct.time_created.desc())
    return sess().scalars(stmt).all()
