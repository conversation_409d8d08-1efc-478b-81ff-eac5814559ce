from typing import Iterable

from sqlalchemy import update

from db import db_func, sess
from db.models import BillingProduct


@db_func
def delete_billing_products(ids: Iterable[int]):
    sess().execute(
        update(BillingProduct)
        .values(
            {
                "is_deleted": True,
            }
        )
        .where(
            BillingProduct.id.in_(ids)
        )
    )
    sess().commit()
