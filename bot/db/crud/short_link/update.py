from db import db_func, sess
from .read import get_short_link_by_display_id_sync
from ...models import ShortLink


@db_func
def use(short_link_display_id: str) -> tuple[ShortLink, int] | None:
    short_link = get_short_link_by_display_id_sync(short_link_display_id, True)
    if not short_link:
        sess().commit()
        return None

    uses = short_link.uses
    if short_link and short_link.is_usage_available:
        short_link.uses += 1
    sess().commit()
    return short_link, uses
