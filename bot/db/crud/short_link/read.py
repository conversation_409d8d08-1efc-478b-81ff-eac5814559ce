from datetime import datetime

from sqlalchemy import exists, func, or_, select

from db import db_func, sess
from db.models import ShortLink


def get_short_link_by_display_id_sync(display_id: str, with_for_update: bool = False) -> ShortLink | None:
    stmt = select(ShortLink)
    if with_for_update:
        stmt = stmt.with_for_update()
    stmt = stmt.where(func.binary(ShortLink.display_id) == display_id)
    stmt = stmt.order_by(ShortLink.time_created.desc())
    stmt = stmt.limit(1)
    return sess().scalar(stmt)


@db_func
def get_short_link_by_display_id(display_id: str, with_for_update: bool = False) -> ShortLink | None:
    return get_short_link_by_display_id_sync(display_id, with_for_update)


def is_exists_alive_short_link_sync(display_id: str, with_for_update: bool = False) -> bool:
    stmt = exists(ShortLink.id)
    if with_for_update:
        stmt = stmt.with_for_update()

    stmt = stmt.where(ShortLink.display_id == display_id)

    stmt = stmt.where(
        or_(
            ShortLink.max_uses.is_(None),
            ShortLink.uses < ShortLink.max_uses,
        )
    )
    stmt = stmt.where(
        or_(
            ShortLink.expiration_date.is_(None),
            ShortLink.expiration_date > datetime.utcnow()
        )
    )
    return sess().scalar(stmt)
