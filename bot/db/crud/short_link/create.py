from datetime import datetime
import logging

from sqlalchemy import select, text

import schemas
from config import MAX_SHORT_LINK_ID_LENGTH, SHORT_LINK_START
from db import db_func, sess
from db.crud.short_link.read import is_exists_alive_short_link_sync
from db.models import ShortLink

logger = logging.getLogger("debugger.short_link.create")


@db_func
def create_short_link(
        type: schemas.ShortLinkType,
        url: str | None = None,
        max_uses: int | None = None,
        expiration_datetime: datetime | None = None,
        display_id: str | None = None,  # will be auto generated if empty
):
    logger.debug(f"Creating short link: type={type}, url={url}, max_uses={max_uses}, expiration_datetime={expiration_datetime}, display_id={display_id}")
    
    if type == "url" and not url:
        logger.error("url cannot be empty when type is url")
        raise ValueError("url cannot be empty when type is url")

    link = None
    try:
        # check existing exact link
        stmt = select(ShortLink)
        stmt = stmt.with_for_update()
        stmt = stmt.where(
            ShortLink.type == type,
            ShortLink.url == url,
        )
        if max_uses:
            stmt = stmt.where(
                ShortLink.max_uses == max_uses,
                ShortLink.uses == 0
            )
        else:
            stmt = stmt.where(ShortLink.max_uses.is_(None))
        if display_id:
            stmt = stmt.where(ShortLink.display_id == display_id)
        if expiration_datetime:
            stmt = stmt.where(ShortLink.expiration_date == expiration_datetime)
        else:
            stmt = stmt.where(ShortLink.expiration_date.is_(None))

        stmt = stmt.order_by(ShortLink.time_created.desc())
        stmt = stmt.limit(1)

        existing = sess().scalar(stmt)
        if existing:
            logger.debug(f"Found existing short link: {existing.display_id}")
            link = existing
        else:
            # Create new short link
            if display_id:
                if len(display_id) > MAX_SHORT_LINK_ID_LENGTH:
                    logger.error(f"display_id too long: {len(display_id)} > {MAX_SHORT_LINK_ID_LENGTH}")
                    raise ValueError(
                        f"display_id cannot be longer than {MAX_SHORT_LINK_ID_LENGTH}"
                    )
                if is_exists_alive_short_link_sync(display_id, True):
                    logger.error(f"display_id already exists: {display_id}")
                    raise ValueError(f"display_id {display_id} already exists")
            else:
                logger.debug("Getting next short link ID from database procedure")
                display_id = sess().scalar(
                    text(
                        "CALL next_short_link_id(:max_length, :start)"
                    ),
                    params={
                        "max_length": MAX_SHORT_LINK_ID_LENGTH,
                        "start": SHORT_LINK_START,
                    },
                )
                if not display_id:
                    logger.error("No short link ID available - table overflow")
                    raise NoShortLinkAvailableError(
                        "Cannot make link due to table overflow"
                    )
                logger.debug(f"Generated new display_id: {display_id}")

            link = ShortLink(
                display_id=display_id,
                type=type,
                url=url,
                max_uses=max_uses,
                expiration_date=expiration_datetime,
            )
            sess().add(link)
            logger.debug(f"Created new short link: {display_id}")
    except Exception as e:
        logger.error(f"Error creating short link: {e}", exc_info=True)
        sess().rollback()
        raise
    else:
        sess().commit()
        if link:
            logger.debug(f"Successfully created/retrieved short link: {link.display_id}")
        else:
            logger.error("Unexpected: link is None after successful execution")
    
    return link


class NoShortLinkAvailableError(Exception):
    pass
