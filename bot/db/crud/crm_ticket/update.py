from datetime import datetime

from db import db_func, sess
from db.models import CRMTicket, CRMTicketStatus, User
from schemas import CRMTicketStatusEnum, CRMTicketStatusInitiatedByEnum


@db_func
def set_crm_ticket_status(
        ticket: CRMTicket,
        status: CRMTicketStatusEnum,
        initiated_by: CRMTicketStatusInitiatedByEnum,
        initiated_by_user: User | None = None,
        header: str | None = None,
        message: str | None = None,
        internal_comment: str | None = None,
):
    ticket.status = status
    ticket.change_date = datetime.utcnow()
    status_obj = CRMTicketStatus(
        ticket=ticket,
        status=status,
        initiated_by=initiated_by,
        initiated_by_user=initiated_by_user,
        header=header,
        message=message,
        internal_comment=internal_comment,
    )
    sess().add(status_obj)
    sess().commit()
    return status_obj
