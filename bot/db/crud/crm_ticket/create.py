from db import db_func, sess
from db.models import CRMTicket, CRMTicketStatus, ClientBot, Group, User, VirtualManagerChat
from schemas import CRMTicketSourceEnum, CRMTicketStatusEnum, CRMTicketStatusInitiatedByEnum


@db_func
def create_crm_ticket(
        ticket_title: str,
        source: CRMTicketSourceEnum,
        user: User,
        group: Group,
        initiated_by: CRMTicketStatusInitiatedByEnum,
        initiated_by_user: User | None = None,
        bot: ClientBot | None = None,
        internal_comment: str | None = None,
        vmc: VirtualManagerChat | None = None,
) -> CRMTicket:
    ticket = CRMTicket(
        source=source,
        status=CRMTicketStatusEnum.OPEN_UNCONFIRMED,
        title=ticket_title,
        group=group,
        user=user,
        bot=bot,
        internal_comment=internal_comment,
    )
    sess().add(ticket)
    sess().add(
        CRMTicketStatus(
            status=CRMTicketStatusEnum.OPEN_UNCONFIRMED,
            initiated_by=initiated_by,
            initiated_by_user=initiated_by_user or user,
            ticket=ticket,
        )
    )

    vmc.ticket = ticket

    sess().commit()
    return ticket
