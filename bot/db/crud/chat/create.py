from sqlalchemy import select

from sqlalchemy import select

from db import db_func, sess
from db.decorators.other import safe_deadlock_handler
from db.models import Chat, DrawResult, UserClientBotActivity
from schemas import Chat<PERSON><PERSON><PERSON><PERSON>


def get_or_create_chat_sync(
        chat_type: ChatTypeEnum,
        user_id: int,
        group_id: int,
        bot_id: int | None = None,
        commit: bool = True
) -> Chat:
    chat = sess().scalar(
        select(Chat).with_for_update()
        .where(
            Chat.user_id == user_id,
            Chat.group_id == group_id,
            Chat.bot_id == bot_id,
        )
    )
    if not chat:
        chat = Chat(
            type=chat_type,
            user_id=user_id,
            group_id=group_id,
            bot_id=bot_id,
        )
        sess().add(chat)
    if commit:
        sess().commit()
    return chat


@db_func
def get_or_create_chat_by_draw_result(draw_result: DrawResult):
    bot = draw_result.draw.bot
    return get_or_create_chat_sync(
        ChatTypeEnum.USER_WITH_GROUP, draw_result.user_id, bot.group_id, bot.id
    )


@db_func
def get_or_create_chat(
        chat_type: ChatTypeEnum,
        user_id: int,
        group_id: int,
        bot_id: int | None = None,
):
    return get_or_create_chat_sync(chat_type, user_id, group_id, bot_id)


@db_func
@safe_deadlock_handler
def user_to_chat(
        chat_type: ChatTypeEnum,
        user_id: int,
        group_id: int,
        bot_id: int | None = None,
        user_bot_activity: UserClientBotActivity | None = None
) -> Chat:
    chat = get_or_create_chat_sync(chat_type, user_id, group_id, bot_id)
    if not chat:
        chat = Chat(
            type=chat_type,
            user_id=user_id,
            group_id=group_id,
            bot_id=bot_id,
        )
        sess().add(chat)

    if user_bot_activity:
        user_bot_activity.active_chat = chat

    sess().commit()
    return chat
