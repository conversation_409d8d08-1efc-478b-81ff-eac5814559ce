from datetime import datetime

from sqlalchemy import and_, case, func, literal_column, select

import schemas
from config import INBOX_MAX_AGE
from db import db_func, sess
from db.crud.helpers import (
    filter_by_phone, get_inbox_status_sort_stmt,
    user_field_if_not_anonymous,
)
from db.crud.helpers.crm import (
    crm_filter_by_inbox_cursor, crm_params_filter,
    crm_scope_filter, get_crm_list, get_inbox_type_sort_stmt,
)
from db.models import Chat, ChatMessage, ClientBot, Group, MediaObject, Scope, User
from db.types.operation import Operation


@db_func
def get_chat(
        chat_id: int,
        action: str | None = None,
        user_id: int | None = None
):
    stmt = select(Chat)
    stmt = stmt.where(Chat.id == chat_id)

    if any((action, user_id)) and not all((action, user_id)):
        raise ValueError("Specify both action and user_id or none of them")

    if action and user_id:
        stmt = stmt.join(Chat.group)
        stmt = stmt.where(Group.status == "enabled")
        stmt = stmt.where(
            Scope.filter_for_action(
                action, "user", user_id,
                {
                    "profile_id": Group.id,
                    "chat_id": chat_id,
                }
            )
        )

    stmt = stmt.group_by(Chat.id)

    return sess().scalar(stmt)


def get_crm_chat_list_statement(
        user_id: int,
        params: schemas.CRMChatListParams | None = None,
        operation: Operation = "list",
        cursor: schemas.IDCursor | None = None,
        inbox_cursor: schemas.InboxCursor | None = None,
        for_inbox: bool = False,
        is_platform_admin: bool | None = None,
):
    inbox_status = case(
        [
            (Chat.is_pending.is_(True),
             literal_column(f"'{schemas.InboxStatus.NEW.value}'")),
            (Chat.is_pending.is_(False),
             literal_column(f"'{schemas.InboxStatus.RECENT.value}'"))
        ]
    )

    inbox_status_sort = get_inbox_status_sort_stmt(inbox_status)

    if operation == "count":
        stmt = select(
            func.count(Chat.id)
        )
    else:
        stmt = select(
            literal_column(f"'{schemas.InboxType.CHAT.value}'").label("inbox_type"),
            inbox_status.label("inbox_status"),
            inbox_status_sort,
            get_inbox_type_sort_stmt(schemas.InboxType.CHAT),
            (Chat.last_pending_set_datetime if for_inbox else Chat.change_date).label(
                "change_date"
            ),
            literal_column("NULL").label("desired_delivery_date"),
            Chat.id,
            Chat.is_pending.label("is_pending"),
            literal_column("'chat'").label("type"),
            literal_column("NULL").label("privacy"),
            literal_column("NULL").label("text"),
            literal_column("NULL").label("additional_text"),
            literal_column("NULL").label("media"),
            Chat.crm_tag.label("crm_tag"),
            literal_column("NULL").label("invoice_type"),
            literal_column("NULL").label("status"),
            literal_column("NULL").label("status_pay"),
            literal_column("NULL").label("current_status"),
            literal_column("NULL").label("shipment_name"),
            literal_column("NULL").label("shipment_type"),
            user_field_if_not_anonymous(User.id, "first_name", User.first_name),
            user_field_if_not_anonymous(User.id, "last_name", User.last_name),
            user_field_if_not_anonymous(User.id, "full_name", User.name),
            user_field_if_not_anonymous(User.id, "email", User.email),
            user_field_if_not_anonymous(User.id, "phone", User.wa_phone),
            user_field_if_not_anonymous(User.id, "photo_url", User.photo_url),
            literal_column("NULL").label("currency"),
            User.id.label("user_id"),
            literal_column("NULL").label("store_id"),
            literal_column("NULL").label("store_name"),
            literal_column("NULL").label("ticket_title"),
            Group.id.label("profile_id"),
            Group.name.label("profile_name"),
            Group.name.label("business_name"),
            ClientBot.display_name.label("bot_name"),
            literal_column("0").label("before_loyalty_sum"),
            literal_column("0").label("discount"),
            literal_column("0").label("bonuses_redeemed"),
            literal_column("0").label("discount_and_bonuses_redeemed"),
            literal_column("0").label("total_sum"),
            literal_column("0").label("tips_sum"),
            literal_column("0").label("sum_to_pay"),
            literal_column("0").label("payer_fee"),
            literal_column("0").label("paid_sum"),
            literal_column("0").label("menu_in_store_id"),
            literal_column("NULL").label("menu_in_store_comment"),
            User.name.label("title"),
            literal_column("NULL").label("mark"),
            func.IFNULL(ChatMessage.text, "").label("items_text"),
            ChatMessage.text.label("last_message_text"),
            func.lower(ChatMessage.content_type).label("last_message_content_type"),
            MediaObject.url.label("last_message_media_url"),
            MediaObject.mime_type.label("last_message_mime_type"),
            func.JSON_UNQUOTE(ChatMessage.content).label("last_message_content"),
            literal_column("NULL").label("is_read"),
            literal_column("NULL").label("read_by_user_id"),
            literal_column("NULL").label("comment"),
            Chat.time_created,
        )

        stmt = stmt.join(User, Chat.user_id == User.id)
        stmt = stmt.join(
            ChatMessage, and_(
                ChatMessage.chat_id == Chat.id,
                ChatMessage.is_last.is_(True)
            )
        )
        stmt = stmt.outerjoin(MediaObject, ChatMessage.media_id == MediaObject.id)

    stmt = stmt.join(Group, Chat.group_id == Group.id)
    stmt = stmt.outerjoin(ClientBot, Chat.bot_id == ClientBot.id)
    stmt = stmt.where(Group.status == "enabled")

    stmt = crm_filter_by_inbox_cursor(
        stmt, schemas.InboxType.CHAT, inbox_cursor,
        Chat.change_date, Chat.id, inbox_status_sort,
    )

    if for_inbox:
        stmt = stmt.where(Chat.last_pending_set_datetime.is_not(None))
        stmt = stmt.where(
            Chat.last_pending_set_datetime >= datetime.utcnow() - INBOX_MAX_AGE
        )

    extra_search_conditions = []
    if params:
        if params.search_text:
            extra_search_conditions.extend(
                [
                    User.username.contains(params.search_text.replace("@", "").strip()),
                    User.full_name.contains(params.search_text.strip()),
                    User.email.contains(params.search_text.strip()),
                    filter_by_phone(User.wa_phone, params.search_text)
                ]
            )
        if params.statuses and len(params.statuses) == 1:
            if params.statuses[0] == schemas.CRMChatPendingStatusEnum.PENDING:
                stmt = stmt.where(Chat.is_pending.is_(True))
            else:
                stmt = stmt.where(Chat.is_pending.is_(False))

    stmt = crm_scope_filter(
        stmt, "chat", user_id,
        Chat.id, is_platform_admin=is_platform_admin,
    )

    stmt = crm_params_filter(
        stmt, Chat, params, cursor,
        operation, extra_search_conditions
    )

    return stmt


@db_func
def get_crm_chat_list(
        user_id: int,
        params: schemas.CRMTicketListParams | None = None,
        operation: Operation = "list",
        cursor: schemas.IDCursor | None = None,
):
    stmt = get_crm_chat_list_statement(user_id, params, operation, cursor)
    return get_crm_list(stmt, Chat, operation, params, cursor, ("change_date", "id"))
