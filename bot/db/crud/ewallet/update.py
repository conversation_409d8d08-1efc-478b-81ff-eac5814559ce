from psutils.undefined import Undefined
from sqlalchemy import update

import schemas
from db import db_func, sess
from db.crud.ad.create import create_ad_sync
from db.crud.ad.delete import delete_ad_sync
from db.crud.ad.update import update_ad_sync
from db.crud.translation.update import (
    clear_object_updated_fields_translations_sync,
    update_object_translations_sync,
)
from db.models import EWallet, EWalletPayment, EWalletUser
from schemas import EWalletPaymentStatus


@db_func
def update_ewallet(
        ewallet_id: int,
        media_id: int | None = None,
        lang: str | None = None,
        langs_list: list[str] | None = None,
        translations:
        dict[
            str,
            schemas.platform.ewallets.EWalletTranslationSchema |
            None
        ] | None = None,
        countries: list[str] | None = None,
        is_enabled: bool | None = None,
        **kwargs,
):
    ad_data = kwargs.pop("ad", Undefined)

    if is_enabled is None:
        is_enabled = False

    update_data = {
        'is_enabled': is_enabled,
        'media_id': media_id,
    }

    if countries is not None:
        update_data['_countries'] = countries

    if lang:
        update_data['lang'] = lang

    if translations:
        translation_langs = list(translations.keys())
        if lang and lang not in translation_langs:
            translation_langs.append(lang)
        if translation_langs:
            update_data['_langs_list'] = translation_langs
    elif langs_list is not None:
        update_data['_langs_list'] = langs_list

    update_data.update(kwargs)

    stmt = (
        update(EWallet)
        .where(
            EWallet.id == ewallet_id,
            EWallet.is_deleted.is_(False),
        )
        .values(**update_data)
    )

    sess().execute(stmt)

    ewallet: EWallet | None
    if translations or ad_data is not Undefined:
        ewallet = sess().query(EWallet).filter(EWallet.id == ewallet_id).one()
    else:
        ewallet = None

    if translations:
        fields_to_update = ['info']
        update_fields = {k: v for k, v in update_data.items() if k in fields_to_update}

        translation_langs = list(translations.keys())

        if translation_langs:
            clear_object_updated_fields_translations_sync(
                ewallet,
                update_fields,
                exclude_langs=[]
            )

        update_object_translations_sync(ewallet, translations)

    if ad_data is not Undefined:
        if ewallet.ad_id:
            if not ad_data:
                delete_ad_sync(ewallet.ad_id)
            else:
                update_ad_sync(ewallet.ad_id, ad_data, False)
        elif ad_data:
            ad_data = create_ad_sync(ad_data, False)
            sess().flush()
            ewallet.ad_id = ad_data.ad.id

    sess().commit()


@db_func
def cancel_ewallet_payment(
        ewallet_payment: EWalletPayment,
) -> EWalletPayment:
    ewallet_payment.status = EWalletPaymentStatus.CANCELLED
    sess().commit()
    return ewallet_payment


@db_func
def delete_ewallet_user(
        ewallet_id: int,
        user_id: int,
):
    stmt = update(EWalletUser).where(
        EWalletUser.id == ewallet_id,
        EWalletUser.user_id == user_id,
    ).values(
        is_deleted=True,
    )
    sess().execute(stmt)
    sess().commit()
