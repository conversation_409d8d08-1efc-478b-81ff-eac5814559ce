from sqlalchemy import and_, distinct, func, or_, select
from sqlalchemy.engine import Row
from sqlalchemy.sql import Select

import schemas
from db import db_func, sess
from db.models import EWallet, EWalletUser, Group, Scope, Translation, User
from db.models.ewallet.ewallet_merchant import EWalletMerchant
from db.types.operation import Operation
from schemas import EWalletAdminProfileSchema


@db_func
def get_admin_ewallet_list(
        params: schemas.AdminListParams,
        user_id: int,
        operation: Operation = "list",
) -> list[EWallet]:
    stmt: Select

    available_data = {
        "ewallet_id": EWallet.id,
    }

    if operation == "count":
        stmt = select(func.count(distinct(EWallet.id)))
        stmt = stmt.where(
            Scope.filter_for_action(
                "profile:read",
                "user", user_id,
                available_data,
            )
        )
    else:
        read_allowed, edit_allowed = Scope.allowed_scopes_list(
            "read", "edit",
            object_name="profile",
            target="user",
            target_id=user_id,
            available_data=available_data
        )

        stmt = select(EWallet)
        stmt = stmt.where(read_allowed.is_(True))

    stmt = stmt.where(EWallet.is_deleted.is_(False))

    if params.search_text:
        params.search_text = params.search_text.strip()
        stmt = stmt.where(
            or_(
                EWallet.name.contains(params.search_text),
                EWallet.description.contains(params.search_text),
                EWallet.id == params.search_text,
            )
        )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(EWallet.time_created.desc(), EWallet.id.desc())

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    return sess().scalars(stmt).all()


@db_func
def get_admin_profile_ewallet_list(
        profile_id: int,
) -> list[EWalletAdminProfileSchema]:

    stmt: Select

    stmt = select(
        EWallet.id,
        EWallet.name,
        EWallet.description,
        EWallet.is_enabled,
        EWallet.media_id,
    ).join(
        Group, Group.id == profile_id
    ).where(
        EWallet.is_deleted.is_(False),
        # EWallet.is_enabled.is_(True),
        Group.status == "enabled",
        func.json_contains(
            EWallet._countries,
            func.json_quote(Group.country_code)
        )
    ).order_by(
        EWallet.time_created.desc(),
        EWallet.id.desc()
    )

    rows = sess().execute(stmt).fetchall()
    return [
        EWalletAdminProfileSchema(
            id=row.id,
            name=row.name,
            description=row.description,
            is_enabled=row.is_enabled,
        ) for row in rows
    ]


@db_func
def get_ewallet_list(bot_id: int, user_id: int, operation: Operation = "list", ) -> \
        list[Row]:
    stmt: Select

    if operation == "count":
        stmt = select(func.count(distinct(EWallet.id)))
    else:
        stmt = select(EWallet)

    stmt = stmt.where(
        EWallet.is_deleted.is_(False),
        EWallet.is_enabled.is_(True),
        # EWallet.bot_id == bot_id,
    )

    stmt = stmt.where(
        or_(
            EWallet.is_private.is_(False),
            and_(
                EWallet.is_private.is_(True),
                EWallet.id.in_(
                    select(EWalletUser.ewallet_id).where(
                        EWalletUser.user_id == user_id,
                        EWalletUser.is_deleted.is_(False)
                    )
                )
            )
        )
    )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(EWallet.time_created.desc(), EWallet.id.desc())

    return sess().execute(stmt).fetchall()


@db_func
def get_ewallet_users_list(ewallet_id: int, operation: Operation = "list", ) -> list[
    Row]:
    stmt: Select

    if operation == "count":
        stmt = select(func.count(distinct(User.id)))
    else:
        stmt = select(User)

    stmt = stmt.join(
        EWalletUser, User.id == EWalletUser.user_id
    )

    stmt = stmt.where(
        EWalletUser.ewallet_id == ewallet_id,
        EWalletUser.is_deleted.is_(False),
    )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(EWalletUser.time_created.desc(), EWalletUser.id.desc())
    return sess().scalars(stmt).all()


@db_func
def get_ewallet_with_translations(ewallet_or_id: EWallet | int, lang: str = None):

    if isinstance(ewallet_or_id, EWallet):
        ewallet = ewallet_or_id
    else:
        ewallet = sess().query(EWallet).filter(
            EWallet.id == ewallet_or_id,
            EWallet.is_deleted.is_(False)
        ).first()

    if not ewallet:
        return None, {}

    query = sess().query(Translation.id)
    query = query.filter(Translation.obj_type == ewallet.__class__.__name__)
    query = query.filter(Translation.obj_id == str(ewallet.id))

    translations_ids = query.all()
    langs_list = [translation_id[0].split("-")[1] for translation_id in
                  translations_ids]

    if lang and lang in langs_list:
        langs_to_get = [lang]
    else:
        langs_to_get = langs_list

    from db.crud.translation.read import get_translations_for_langs_list_sync

    translations_items = get_translations_for_langs_list_sync(ewallet, langs_to_get)

    translations = dict(translations_items)

    return ewallet, translations


@db_func
def check_access_user_to_ewallet(ewallet_id: int, user_id: int | None = None) -> bool:
    if not user_id:
        return False
    stmt = select(
        EWalletUser.id
    ).where(
        EWalletUser.ewallet_id == ewallet_id,
        EWalletUser.user_id == user_id,
        EWalletUser.is_deleted.is_(False),
    )

    return bool(sess().scalar(stmt))


@db_func
def get_ewallet(
        ewallet_uuid_id: str,
        user_id: int | None = None,
) -> EWallet | None:
    stmt = (
        select(
            EWallet
        )
        .where(EWallet.uuid_id == ewallet_uuid_id)
    )
    if user_id:
        stmt = stmt.where(
            or_(
                EWallet.is_private.is_(False),
                EWallet.users.any(
                    user_id=user_id,
                    is_deleted=False
                ),
            )
        )
    else:
        stmt = stmt.where(
            EWallet.is_private.is_(False),
        )

    return sess().scalar(stmt)


@db_func
def get_platform_ewallet_merchant_list(
        params: schemas.AdminListParams | schemas.EwalletMerchantsListParams,
        operation: Operation = "list",
) -> list[EWalletMerchant]:
    stmt: Select

    if operation == "count":
        stmt = select(func.count(EWalletMerchant.id))
    else:
        stmt = select(EWalletMerchant)

    stmt = stmt.where(EWalletMerchant.is_deleted.is_(False))

    # Filter by ewallet_id if provided
    if hasattr(params, 'ewallet_id') and params.ewallet_id is not None:
        stmt = stmt.where(EWalletMerchant.ewallet_id == params.ewallet_id)

    if params.search_text:
        params.search_text = params.search_text.strip()
        stmt = stmt.where(
            or_(
                EWalletMerchant.name.contains(params.search_text),
                EWalletMerchant.id == params.search_text,
            )
        )

    if operation == "count":
        return sess().scalar(stmt)

    stmt = stmt.order_by(EWalletMerchant.id.desc())

    if params.offset:
        stmt = stmt.offset(params.offset)
    if params.limit:
        stmt = stmt.limit(params.limit)

    return sess().scalars(stmt).all()
