from sqlalchemy import update

from db import db_func, sess
from db.models import EWallet, EWalletPayment, EWalletUser


@db_func
def create_ewallet(
        incust_account_id: str, terminal_api_key: str,
        server_api_url: str, bot_id: int,
        countries: list[str], currency: str,
        creator_id: int, name: str | None = None,
        description: str | None = None,
        is_enabled: bool | None = None,
        invoice_template_id: int | None = None,
        lang: str = "en",
        langs_list: list[str] | None = None,
        **kwargs,
) -> EWallet:

    if is_enabled is None:
        is_enabled = False

    if "discount_percent" in kwargs and not kwargs["discount_percent"]:
        kwargs["discount_percent"] = 0

    existing_setting = sess().query(EWallet).filter(
        EWallet.incust_account_id == incust_account_id
    ).one_or_none()

    if existing_setting:
        stmt = update(EWallet).where(
            EWallet.incust_account_id == incust_account_id
        ).values(
            terminal_api_key=terminal_api_key,
            server_api_url=server_api_url,
            bot_id=bot_id,
            currency=currency,
            creator_id=creator_id,
            name=name,
            description=description,
            is_deleted=False,
            _countries=countries,
            is_enabled=is_enabled,
            invoice_template_id=invoice_template_id,
            lang=lang,
            _langs_list=langs_list if langs_list else [lang],
            **kwargs,
        )
        sess().execute(stmt)

        updated_setting = sess().query(EWallet).filter(
            EWallet.incust_account_id == incust_account_id
        ).one()
        sess().commit()
        return updated_setting
    else:
        new_setting = EWallet(
            terminal_api_key=terminal_api_key,
            server_api_url=server_api_url,
            incust_account_id=incust_account_id,
            bot_id=bot_id,
            currency=currency,
            creator_id=creator_id,
            name=name,
            description=description,
            _countries=countries,
            is_enabled=is_enabled,
            invoice_template_id=invoice_template_id,
            lang=lang,
            _langs_list=langs_list if langs_list else [lang],
            **kwargs,
        )
        sess().add(new_setting)
        sess().commit()
        return new_setting


@db_func
def create_ewallet_payment(
        uuid_id: str,
        profile_id: int,
        ewallet_id: int,
        external_id: str,
        amount: int,
        creator_id: int,
        currency: str,
        bot_id: int,
        payment_settings_id: int,
        success_url: str | None = None,
        redirect_url: str | None = None,
        description: str | None = None,

) -> EWallet:

    ewallet_payment = EWalletPayment(
        uuid_id=uuid_id,
        profile_id=profile_id,
        ewallet_id=ewallet_id,
        external_id=external_id,
        amount=amount,
        bot_id=bot_id,
        creator_id=creator_id,
        currency=currency,
        success_url=success_url,
        redirect_url=redirect_url,
        description=description,
        payment_settings_id=payment_settings_id,
    )
    sess().add(ewallet_payment)
    sess().commit()
    return ewallet_payment


@db_func
def create_ewallet_user(ewallet_id: int, user_id: int) -> EWallet:
    ewallet_user = sess().query(EWalletUser).filter(
        EWalletUser.ewallet_id == ewallet_id,
        EWalletUser.user_id == user_id,
    ).one_or_none()
    if ewallet_user:
        if ewallet_user.is_deleted:
            ewallet_user.is_deleted = False
            sess().commit()
        return ewallet_user

    ewallet_user = EWalletUser(
        ewallet_id=ewallet_id,
        user_id=user_id,
    )
    sess().add(ewallet_user)

    sess().commit()
    return sess().query(EWalletUser).filter(
        EWalletUser.ewallet_id == ewallet_id,
        EWalletUser.user_id == user_id,
        EWalletUser.is_deleted.is_(False),
    ).one_or_none()
