from psutils.receipts.schemas import Receipt as ReceiptSchema, Organisation as OrganisationSchema, POS
from sqlalchemy import select, and_
from sqlalchemy.sql import Select

from db import db_func, sess
from db.models import Receipt, Organisation, Pos, User, Brand, ReceiptItem


@db_func
def create_receipt(
        receipt_schema: ReceiptSchema,
        user: User,
        brand: Brand,
        order_id: int | None = None,
        processed_check: dict | None = None,
) -> Receipt:
    receipt = make_receipt(receipt_schema, user, brand, order_id, processed_check)

    sess().add(receipt)
    sess().commit()

    return receipt


def make_receipt(
        receipt_schema: ReceiptSchema,
        user: User,
        brand: Brand,
        order_id: int | None = None,
        processed_check: dict | None = None,
) -> Receipt:
    organisation = make_organisation(receipt_schema.organisation)
    pos = make_pos(receipt_schema.pos, organisation)

    receipt = Receipt(
        user=user,
        receipt_id=receipt_schema.receipt_id,
        organisation=organisation,
        pos=pos,
        total_price=receipt_schema.total_price,
        issue_datetime=receipt_schema.issue_datetime,
        json_data=receipt_schema.json_data,
        brand=brand,
        group_id=brand.group_id,
        order_id=order_id,
        incust_check=processed_check,
        # ppo_id=receipt_schema.ppo_id or None,
    )

    make_items(receipt, receipt_schema)

    return receipt


def make_items(receipt: Receipt, receipt_schema: ReceiptSchema):
    for item in receipt_schema.items:
        receipt_item = ReceiptItem(
            receipt=receipt,
            name=item.name,
            price=item.price,
            quantity=item.quantity,
            product_code=item.product_code,
        )
        sess().add(receipt_item)


def make_organisation(org: OrganisationSchema) -> Organisation:
    stmt: Select = select(Organisation)
    stmt = stmt.where(and_(
        Organisation.ico_code == org.ico_code,
        Organisation.country == org.country
    ))
    exist_organisation = sess().scalar(stmt)
    if exist_organisation:
        return exist_organisation

    organisation = Organisation(
        name=org.name,
        country=org.country,
        ico_code=org.ico_code,
        municipality=org.municipality,
        postal_code=org.postal_code,
        street=org.street,
        # inn=org.inn or None,
    )

    return organisation


def make_pos(pos: POS, org: Organisation) -> Pos:
    stmt: Select = select(Pos)
    stmt = stmt.where(and_(
        Pos.country == pos.country,
        Pos.name == pos.name,
        Pos.municipality == pos.municipality,
        Pos.postal_code == pos.postal_code,
        Pos.street == pos.street,
    ))
    exist_pos = sess().scalar(stmt)
    if exist_pos and exist_pos.organisation_id == org.id:
        return exist_pos

    pos_db = Pos(
        organisation_id=org.id,
        country=pos.country,
        name=pos.name,
        municipality=pos.municipality,
        postal_code=pos.postal_code,
        street=pos.street,
        cash_register_code=pos.cash_register_code,
    )

    return pos_db
