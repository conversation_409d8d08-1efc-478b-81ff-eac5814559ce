from db import db_func, sess
from db.models import Receipt
from utils.datetime_utils import convert_datetime_to_str


@db_func
def update_receipt_loyalty_data(
        receipt_id: int,
        incust_check: dict | None = None,
        incust_transaction: dict | None = None,
        emitted_coupons: list[dict] | None = None,
) -> bool:
    receipt = sess().query(Receipt).get(receipt_id)
    if not receipt:
        return False

    if incust_check:
        receipt.incust_check = convert_datetime_to_str(incust_check)
    if incust_transaction:
        receipt.incust_transaction = convert_datetime_to_str(incust_transaction)
    if emitted_coupons:
        receipt.emitted_coupons = convert_datetime_to_str(emitted_coupons)

    sess().commit()
    return True
