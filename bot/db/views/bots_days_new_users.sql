CREATE OR REPLACE VIEW bots_days_new_users AS
    SELECT DISTINCT
        DATE(user_analytic_actions.datetime) AS `date`,
        user_analytic_actions.bot_id AS `bot_id`,
        user_analytic_actions.user_id AS `user_id`
    FROM user_analytic_actions
    INNER JOIN bots ON user_analytic_actions.bot_id = bots.id
    WHERE user_analytic_actions.type = 'user_joined_to_bot'
      AND bots.is_friendly IS FALSE
;
