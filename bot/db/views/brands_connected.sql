CREATE OR REPLACE VIEW brands_connected AS
    SELECT
        admin_bot_analytic_actions.datetime,
        DATE(admin_bot_analytic_actions.datetime) AS date,
        brands.id AS brand_id,
        brands.name AS brand_name,
        brands.domain AS brand_domain,
        `groups`.id AS group_id,
        `groups`.name AS group_name,
        `groups`.status = 'disabled' AS is_group_deleted,
        COUNT(stores.id) AS `stores_count`
    FROM admin_bot_analytic_actions

    INNER JOIN `groups` ON admin_bot_analytic_actions.group_id = `groups`.id
    INNER JOIN brands ON `groups`.id = brands.group_id
    INNER JOIN stores ON brands.id = stores.brand_id

    WHERE admin_bot_analytic_actions.type = 'group_created'

    GROUP BY brands.id
    HAVING stores_count > 1
;
