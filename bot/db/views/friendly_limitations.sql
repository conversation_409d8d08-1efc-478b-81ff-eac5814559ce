CREATE OR REPLACE VIEW friendly_limitations AS
    SELECT
        friendly_bot_analytic_actions.type,
        friendly_bot_analytic_actions.reason,
        friendly_bot_analytic_actions.datetime,
        DATE(friendly_bot_analytic_actions.datetime) as `date`,

        friendly_bot_analytic_actions.member_id,

        friendly_bot_analytic_actions.channel_id,
        channels.name AS channel_name

    FROM friendly_bot_analytic_actions

    INNER JOIN channels on friendly_bot_analytic_actions.channel_id = channels.id

    WHERE friendly_bot_analytic_actions.type = 'message_sent'
      AND friendly_bot_analytic_actions.is_deleted IS TRUE

    GROUP BY friendly_bot_analytic_actions.id
;
