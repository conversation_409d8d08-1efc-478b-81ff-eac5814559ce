CREATE OR REPLACE VIEW bots_days_new_users_counts AS
    SELECT
        bots_days.date as `date`,
        bots_days.bot_id as `bot_id`,
        COUNT(DISTINCT users.user_id) as `users_count`
    FROM bots_days
    LEFT OUTER JOIN
        (
            SELECT DISTINCT
                DATE(user_analytic_actions.datetime) AS `date`,
                user_analytic_actions.bot_id AS `bot_id`,
                user_analytic_actions.user_id AS `user_id`
            FROM user_analytic_actions
            WHERE user_analytic_actions.type = 'user_joined_to_bot'
        ) as `users`
            ON users.date = bots_days.date AND users.bot_id = bots_days.bot_id
    GROUP BY `bot_id`, `date`
;
