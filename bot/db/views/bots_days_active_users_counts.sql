CREATE OR R<PERSON>LACE VIEW bots_days_active_users_counts AS
    SELECT
        bots_days.date as `date`,
        bots_days.bot_id as `bot_id`,
        bots_days.bot_username as `bot_username`,
        (
          SELECT COUNT(DISTINCT users.user_id)
          FROM
           (
               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 6 DAY
                 AND user_analytic_actions.type NOT IN
                     (
                      'message_to_chat_with_user',
                      'voucher_canceled_by_manager',
                      'order_canceled_by_manager'
                     )
               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 5 DAY
                 AND user_analytic_actions.type NOT IN
                     (
                      'message_to_chat_with_user',
                      'voucher_canceled_by_manager',
                      'order_canceled_by_manager'
                     )

               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 4 DAY
                 AND user_analytic_actions.type NOT IN
                     (
                      'message_to_chat_with_user',
                      'voucher_canceled_by_manager',
                      'order_canceled_by_manager'
                     )

               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 3 DAY
                 AND user_analytic_actions.type NOT IN
                     (
                      'message_to_chat_with_user',
                      'voucher_canceled_by_manager',
                      'order_canceled_by_manager'
                     )

               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 2 DAY
                 AND user_analytic_actions.type NOT IN
                     (
                      'message_to_chat_with_user',
                      'voucher_canceled_by_manager',
                      'order_canceled_by_manager'
                     )

               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date - INTERVAL 1 DAY
                 AND user_analytic_actions.type NOT IN
                     (
                      'message_to_chat_with_user',
                      'voucher_canceled_by_manager',
                      'order_canceled_by_manager'
                     )

               UNION

               SELECT DISTINCT user_analytic_actions.user_id as `user_id`
               FROM user_analytic_actions
               WHERE user_analytic_actions.bot_id = bots_days.bot_id
                 AND DATE(user_analytic_actions.datetime) = bots_days.date
                 AND user_analytic_actions.type NOT IN
                     (
                      'message_to_chat_with_user',
                      'voucher_canceled_by_manager',
                      'order_canceled_by_manager'
                     )

               ) as `users`
           ) AS `users_count`
    FROM bots_days
;
