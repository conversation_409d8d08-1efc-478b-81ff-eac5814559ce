CREATE OR REPLACE VIEW stores_connected AS
    SELECT
        admin_bot_analytic_actions.datetime,
        DATE(admin_bot_analytic_actions.datetime) AS date,
        brands.id AS brand_id,
        brands.name AS brand_name,
        brands.domain AS brand_domain,
        `groups`.id AS group_id,
        `groups`.name AS group_name,
        `groups`.status = 'disabled' AS is_group_deleted,
        stores.id AS store_id,
        stores.name AS store_name,
        stores.is_deleted AS is_store_deleted,
        COUNT(store_products.id) AS products_count
    FROM admin_bot_analytic_actions

    INNER JOIN `groups` ON admin_bot_analytic_actions.group_id = `groups`.id
    INNER JOIN brands ON `groups`.id = brands.group_id
    INNER JOIN stores ON brands.id = stores.brand_id
    LEFT OUTER JOIN product_to_stores ON stores.id = product_to_stores.store_id
    LEFT OUTER JOIN store_products ON product_to_stores.product_id = store_products.id

    WHERE
        admin_bot_analytic_actions.type = 'group_created' AND
        store_products.is_deleted IS NOT TRUE

    GROUP BY stores.id
;
