CREATE OR REPLACE VIEW bots_days_users_counts AS
SELECT dates.date   as `date`,
       bots.id as `bot_id`,
       COUNT(user_id) AS `users_count`
FROM dates
         INNER JOIN bots ON dates.date >= bots.time_created
         LEFT JOIN user_analytic_actions
                   ON DATE(user_analytic_actions.datetime) <= dates.date
                       AND user_analytic_actions.bot_id = bots.id
                       AND user_analytic_actions.type = 'user_joined_to_bot'
WHERE dates.date <= UTC_TIMESTAMP()
AND bots.is_friendly is FALSE
GROUP BY `date`, `bot_id`, `user_id`
;
