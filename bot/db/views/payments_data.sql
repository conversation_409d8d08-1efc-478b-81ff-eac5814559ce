CREATE OR REPLACE VIEW `payments_data` AS
SELECT
    `invoices`.`id` AS `invoice_id`,
    `invoices`.`title` AS `invoice_title`,
    `invoices`.`description` AS `invoice_description`,
    `invoices`.`invoice_type` AS `invoice_type`,
    `invoices`.`payment_mode` AS `invoice_payment_mode`,
    `invoices`.`status` AS `invoice_status`,
    `invoices`.`time_created` AS `invoice_time_created`,
    CAST(`invoices`.`time_created` AS DATE) AS `invoice_date`,
    `store_order_payments`.`confirmed_datetime` AS `invoice_payed_datetime`,
    `invoices`.`expiration_datetime` AS `invoice_expiration_datetime`,
    (`invoices`.`time_created` + INTERVAL `invoices`.`_live_time` SECOND) AS `invoice_dead_time`,
    `invoices`.`bot_id` AS `bot_id`,
    CONCAT('@', `bots`.`username`) AS `bot_username`,
    `invoices`.`group_id` AS `group_id`,
    `groups`.`name` AS `group_name`,
    `invoices`.`user_id` AS `user_id`,
    IFNULL(
        CONCAT('@', `users`.`username`),
        IFNULL(
            `users`.`email`,
            IFNULL(
                CONCAT(`users`.`_first_name`, ' ', IFNULL(`users`.`_last_name`, '')),
                IFNULL(`users`.`wa_name`, IFNULL(`users`.`wa_phone`, `users`.`chat_id`))
            )
        )
    ) AS `calculated_user_name`,
    `users`.`chat_id` AS `user_chat_id`,
    CONCAT('@', `users`.`username`) AS `user_username`,
    `users`.`_first_name` AS `user_first_name`,
    `users`.`_last_name` AS `user_last_name`,
    `users`.`wa_phone` AS `user_wa_phone`,
    `users`.`wa_name` AS `user_wa_name`,
    CASE
        WHEN (`users`.`chat_id` IS NOT NULL) THEN 'TG'
        WHEN (`users`.`wa_phone` IS NOT NULL) THEN 'WA'
        WHEN (`users`.`email` <> '<EMAIL>') THEN 'EM'
        ELSE 'NA'
    END AS `user_contact`,
    `invoices`.`creator_id` AS `creator_user_id`,
    IFNULL(
        CONCAT('@', `creator`.`username`),
        IFNULL(
            `creator`.`email`,
            IFNULL(
                CONCAT(`creator`.`_first_name`, ' ', IFNULL(`creator`.`_last_name`, '')),
                IFNULL(`creator`.`wa_name`, IFNULL(`creator`.`wa_phone`, `creator`.`chat_id`))
            )
        )
    ) AS `calculated_creator_name`,
    `creator`.`chat_id` AS `creator_chat_id`,
    CONCAT('@', `creator`.`username`) AS `creator_username`,
    `creator`.`_first_name` AS `creator_first_name`,
    `creator`.`_last_name` AS `creator_last_name`,
    `creator`.`wa_phone` AS `creator_wa_phone`,
    `creator`.`wa_name` AS `creator_wa_name`,
    `invoices`.`menu_in_store_id` AS `menu_in_store_id`,
    `menus_in_store`.`comment` AS `menu_in_store_comment`,
    `menus_in_store`.`store_id` AS `menu_in_store_store_id`,
    `store_orders`.`id` AS `store_order_id`,
    IFNULL(IFNULL(`invoices`.`payment_bot_id`, `invoices`.`payment_bot_menu_id`), `invoices`.`bot_id`) AS `payed_in_bot`,
    CONCAT('@', IFNULL(IFNULL(`payment_bots`.`username`, `payment_menu_bots`.`username`), `bots`.`username`)) AS `payed_in_bot_username`,
    `invoices`.`currency` AS `invoice_currency`,
    ROUND(`invoices`.`total_sum` / 100, 0) AS `total_amount`,
    GROUP_CONCAT(
        IFNULL(`invoice_items`.`name`, 'position'),
        ': ', ROUND(`invoice_items`.`final_price` / 100, 2),
        ' x ', `invoice_items`.`quantity`,
        ' = ', ROUND(`invoice_items`.`final_sum` / 100, 0)
        SEPARATOR '\n'
    ) AS `positions_str`,
    (SELECT `payments`.`uuid_id`
     FROM `payments`
     WHERE (`invoices`.`id` = `payments`.`invoice_id` AND `payments`.`status` = 'payed')) AS `ext_id`,
    (SELECT `payments`.`payment_method`
     FROM `payments`
     WHERE (`invoices`.`id` = `payments`.`invoice_id` AND `payments`.`status` = 'payed')) AS `payment_method`,
    (SELECT `payments`.`external_id`
     FROM `payments`
     WHERE (`invoices`.`id` = `payments`.`invoice_id` AND `payments`.`status` = 'payed')) AS `external_id`,
    (SELECT `payments`.`card_mask`
     FROM `payments`
     WHERE (`invoices`.`id` = `payments`.`invoice_id` AND `payments`.`status` = 'payed')) AS `card_mask`
FROM
    `invoices`
    JOIN `invoice_items` ON (`invoices`.`id` = `invoice_items`.`invoice_id`)
    LEFT JOIN `menus_in_store` ON (`invoices`.`menu_in_store_id` = `menus_in_store`.`id`)
    LEFT JOIN `store_order_payments` ON (`store_order_payments`.invoice_id = invoices.id)
    LEFT JOIN `store_orders` ON (`invoices`.`id` = `store_orders`.`invoice_id`)
    LEFT JOIN `bots` AS `payment_bots` ON (`invoices`.`payment_bot_id` = `payment_bots`.`id`)
    LEFT JOIN `bots` AS `payment_menu_bots` ON (`invoices`.`payment_bot_menu_id` = `payment_menu_bots`.`id`)
    LEFT JOIN `groups` ON (`invoices`.`group_id` = `groups`.`id`)
    LEFT JOIN `bots` ON (`invoices`.`bot_id` = `bots`.`id`)
    JOIN `users` AS `creator` ON (`invoices`.`creator_id` = `creator`.`id`)
    JOIN `users` ON (`invoices`.`user_id` = `users`.`id`)
GROUP BY
    `invoices`.`id`;
