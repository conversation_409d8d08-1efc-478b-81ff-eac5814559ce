CREATE OR REPLACE VIEW friendly_entered_analytic AS
    SELECT
        chats_members.id AS `member_id`,
        chats_members.joined_datetime AS `member_joined_datetime`,
        users.id AS `user_id`,
        IF(users.username IS NOT NULL, CONCAT('@', users.username), users.full_name) AS `user_name`,
        channels.name AS `chat_name`
    FROM chats_members
    INNER JOIN users ON chats_members.user_id = users.id
    INNER JOIN channels ON chats_members.channel_id = channels.id
    INNER JOIN users_client_bots_activity ON
        users_client_bots_activity.user_id = users.id
            AND users_client_bots_activity.bot_id = channels.bot_id
    WHERE users_client_bots_activity.is_entered_bot IS TRUE
;
