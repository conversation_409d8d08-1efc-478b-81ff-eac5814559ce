CREATE OR REPLACE VIEW store_orders_view AS
    SELECT
        store_orders.id,

        store_orders.create_date as create_order_datetime,
        DATE(store_orders.create_date) as create_order_date,

        store_orders._status AS status,
        store_orders._status_pay AS status_pay,

        brands.id AS brand_id,
        store_orders.brand_name AS brand_name,

        stores.id AS store_id,
        stores.name AS store_name,
        stores.is_deleted AS is_store_deleted,

        ROUND(store_orders.before_loyalty_sum / 100, 2) AS before_loyalty_sum,
        ROUND(store_orders.discount / 100, 2) AS discount,
        ROUND(store_orders.discount_and_bonuses / 100, 2) AS discount_and_bonuses,
        ROUND(store_orders.total_sum / 100, 2) AS total_sum,

        store_orders.email AS order_email,
        store_orders.phone AS order_phone,
        store_orders.first_name AS order_first_name,
        store_orders.last_name AS order_last_name,

        store_orders.payment_method,
        order_shipments.base_type AS delivery_method,

        users.id AS user_id,
        users.email AS user_email,
        users.chat_id AS user_chat_id,
        users.username AS user_username,
        users._first_name AS user_first_name,
        users._last_name AS user_last_name,
        users.full_name AS user_full_name,
        users.wa_name AS user_wa_name,
        users.wa_phone AS user_wa_phone,

        users.chat_id IS NOT NULL AS is_telegram,
        users.wa_phone IS NOT NULL AS is_whatsapp,
        users.email IS NOT NULL AS is_email,

        (
            CASE
                WHEN users.chat_id IS NOT NULL THEN 'telegram'
                WHEN users.wa_phone IS NOT NULL THEN 'whatsapp'
                WHEN users.email IS NOT NULL THEN 'email'
                ELSE 'unknown'
            END
        ) AS `user_type`

    FROM store_orders

    INNER JOIN order_shipments ON order_shipments.store_order_id = store_orders.id

    INNER JOIN stores ON store_orders.store_id = stores.id
    INNER JOIN brands ON stores.brand_id = brands.id

    INNER JOIN users ON store_orders.user_id = users.id
;
