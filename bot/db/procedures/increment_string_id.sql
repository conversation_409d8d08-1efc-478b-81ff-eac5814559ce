DELIMITER //

DROP PROCEDURE IF EXISTS increment_string_id;
CREATE PROCEDURE increment_string_id(
    IN max_length INT,
    IN last_variant VARCHAR(255),
    OUT next_id VARCHAR(255)
)
BEGIN
    DECLARE characters VARCHAR(255) DEFAULT '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    DECLARE i INT DEFAULT 0;
    DECLARE indices VARCHAR(255);

    -- Перетворення рядка у список цілочисельних індексів
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_indices (id INT AUTO_INCREMENT PRIMARY KEY, val INT);
    TRUNCATE TABLE temp_indices;

    SET i = 1;
    WHILE i <= CHAR_LENGTH(last_variant) DO
        INSERT INTO temp_indices (val) VALUES (LOCATE(BINARY SUBSTRING(last_variant, i, 1), BINARY characters));
        SET i = i + 1;
    END WHILE;

    -- Інкрементування рядка
    SET i = CHAR_LENGTH(last_variant);
    IncrementLoop: WHILE i >= 1 DO
        UPDATE temp_indices SET val = val + 1 WHERE id = i;
        IF (SELECT val FROM temp_indices WHERE id = i) <= CHAR_LENGTH(characters) THEN
            LEAVE IncrementLoop;
        ELSE
            UPDATE temp_indices SET val = 1 WHERE id = i; -- set to index of first character '0'
            SET i = i - 1;
            IF i = 0 THEN
                UPDATE temp_indices set val = 1 WHERE id = 1;
                INSERT INTO temp_indices (val) VALUES (1);
            END IF;
        END IF;
    END WHILE;

    -- Перетворення індексів назад у рядок
    SET indices = '';
    SET i = 1;
    WHILE i <= (SELECT MAX(id) FROM temp_indices) DO
        SET indices = CONCAT(indices, SUBSTRING(characters, (SELECT val FROM temp_indices WHERE id = i), 1));
        SET i = i + 1;
    END WHILE;

    -- Повернення значення
    IF LENGTH(indices) > max_length THEN
        SET next_id = NULL;
    ELSE
        SET next_id = indices;
    END IF;

    DROP TEMPORARY TABLE IF EXISTS temp_indices;
END//

DELIMITER ;
