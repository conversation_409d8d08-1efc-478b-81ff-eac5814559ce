DELIMITER //

DROP PROCEDURE IF EXISTS next_short_link_id;
CREATE PROCEDURE next_short_link_id(
    IN max_length INT,
    IN start VARCHAR(6)
)
BEGIN
    DECLARE min_id VARCHAR(255);
    DECLARE last_id VARCHAR(255);
    DECLARE next_id VARCHAR(255);

    SELECT t1.display_id
    INTO min_id
    FROM short_links t1
    WHERE (
        expiration_date < UTC_TIMESTAMP()
            OR (max_uses IS NOT NULL AND uses >= max_uses)
        )
      AND NOT EXISTS (SELECT 1
                      FROM short_links t2
                      WHERE t1.display_id = t2.display_id
                        AND (t2.expiration_date IS NULL OR t2.expiration_date > UTC_TIMESTAMP())
                        AND (t2.max_uses IS NULL or t2.uses < t2.max_uses))
    ORDER BY LENGTH(t1.display_id), t1.display_id
    LIMIT 1
    FOR
    UPDATE;

    IF min_id IS NOT NULL THEN
        SET next_id = min_id;
    ELSE
        SELECT display_id INTO last_id FROM short_links ORDER BY LENGTH(display_id) DESC, display_id DESC LIMIT 1 FOR UPDATE;
        -- If there are no objects at all;
        if last_id IS NULL THEN
            SET next_id = start;
        ELSE
            CALL increment_string_id(max_length, last_id, next_id);
        END if;
    END IF;

    SELECT next_id;
END//

DELIMITER ;
