from typing import List
from contextvars import ContextVar

from db.connection import Base


class ContextMixin:

    classes: List[Base] = list()

    context_variables: List[ContextVar] = list()

    _ctx_instance: ContextVar

    def __init_subclass__(cls, **kwargs):
        cls._ctx_instance = ContextVar(f"instance_{cls.__name__}")
        ContextMixin.classes.append(cls)
        ContextMixin.context_variables.append(cls._ctx_instance)
        return cls


def reset_context_instances():
    for variable in ContextMixin.context_variables:
        variable.set(None)
