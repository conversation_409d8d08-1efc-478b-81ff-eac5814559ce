import enum
from datetime import datetime
from typing import Any, Literal, Type

from psutils.text import paschal_case_to_snake_case
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, inspect
from sqlalchemy.orm import declared_attr

from db.decorators.other import safe_deadlock_handler
from utils.date_time import utcnow
from utils.type_vars import T
from ..decorators import db_func
from ..helpers import sess


class BaseDBModel:
    __allow_unmapped__ = True

    GET_FIELDS: list[str] = None
    UPDATE_FIELDS: list[str] = None
    DEBUG_FIELDS: list[str] | Literal["auto"] = "auto"
    UPDATE_TIME: bool = True  # update time_updated (if field exists in model)

    @declared_attr
    def __tablename__(self):
        return paschal_case_to_snake_case(self.__name__) + "s"

    id: Column[int] | int = Column(BigInteger, autoincrement=True, primary_key=True)

    @classmethod
    def get_expression(
            cls: Type[T],
            exists_stmt: bool = False,
            for_update: bool = False,
            **kwargs,
    ):
        query = sess().query(cls)

        if for_update:
            query = query.populate_existing()
            query = query.with_for_update()

        for key, value in kwargs.items():
            if key not in dir(cls):
                raise AttributeError("Unknown attribute '%s'" % key)
            if cls.GET_FIELDS and key not in cls.GET_FIELDS:
                raise AttributeError(
                    "Forbidden to use attribute '%s' in get method" % key
                )

            if isinstance(value, bool | None):
                query = query.filter(getattr(cls, key).is_(value))
            else:
                query = query.filter(getattr(cls, key) == value)
        if exists_stmt:
            query = sess().query(query.exists())
        return query

    @classmethod
    def get_sync(
            cls: Type[T],
            id: Any = None, *,
            for_update: bool = False,
            release_on_exists: bool = False,
            **kwargs,
    ) -> T | None:
        if isinstance(id, cls):
            if not kwargs:
                return id
            kwargs["id"] = id.id

        if id:
            kwargs["id"] = id
        elif not kwargs:
            return None

        query = cls.get_expression(for_update=for_update, **kwargs)
        result = query.one_or_none()
        if result and for_update and release_on_exists:
            sess().commit()
        return result

    @classmethod
    @db_func
    @safe_deadlock_handler
    def get(
            cls: Type[T],
            id: Any = None, *,
            for_update: bool = False,
            release_on_exists: bool = False,
            **kwargs,
    ) -> T | None:
        return cls.get_sync(
            id,
            for_update=for_update,
            release_on_exists=release_on_exists,
            **kwargs,
        )

    @classmethod
    def is_exists_sync(
            cls, id: Any = None, *,
            for_update: bool = False,
            release_on_exists: bool = False,
            **kwargs
    ) -> bool:
        if id:
            kwargs["id"] = id
        result = cls.get_expression(
            exists_stmt=True, for_update=for_update, **kwargs
        ).scalar()
        if result and for_update and release_on_exists:
            sess().commit()
        return result

    @classmethod
    @db_func
    def is_exists(
            cls, id: Any = None, *,
            for_update: bool = False,
            release_on_exists: bool = False,
            **kwargs,
    ) -> bool:
        return cls.is_exists_sync(
            id, for_update=for_update,
            release_on_exists=release_on_exists,
            **kwargs,
        )

    @classmethod
    def get_list_sync(cls: Type[T], **kwargs) -> list[T]:
        expression = cls.get_expression(**kwargs)
        expression = cls.add_order_by_position_and_time(expression)
        expression = expression.order_by(cls.id.desc())
        return expression.all()

    @classmethod
    @db_func
    def get_list(cls: Type[T], **kwargs) -> list[T]:
        return cls.get_list_sync(**kwargs)

    @classmethod
    def get_by_ids_sync(
            cls: Type[T], ids: list[int] | set[int], **kwargs
    ) -> list[T]:
        expression = cls.get_expression(**kwargs)

        expression = expression.where(cls.id.in_(ids))

        expression = cls.add_order_by_position_and_time(expression)
        expression = expression.order_by(cls.id.desc())
        # compiled_sql = expression.statement.compile(
        #     sess().bind, compile_kwargs={"literal_binds": True}
        # )
        # print("SQL Query:", compiled_sql)
        result = expression.all()
        # print(result)
        return result

    @classmethod
    @db_func
    def get_by_ids(
            cls: Type[T], ids: list[int] | set[int], **kwargs
    ) -> list[T]:
        return cls.get_by_ids_sync(ids, **kwargs)

    @classmethod
    def get_by_in_field_sync(
            cls: Type[T],
            field_name: str,
            values: list | set,
            **kwargs
    ) -> list[T]:
        """
        Синхронний метод для отримання записів, де вказане поле містить значення зі
        списку

        Args:
            field_name: Назва поля для фільтрації IN
            values: Список або множина значень для фільтрації
            **kwargs: Додаткові параметри для get_expression

        Returns:
            Список об'єктів моделі
        """
        expression = cls.get_expression(**kwargs)

        # Отримуємо поле за ім'ям
        field = getattr(cls, field_name)
        expression = expression.where(field.in_(values))

        expression = cls.add_order_by_position_and_time(expression)
        expression = expression.order_by(cls.id.desc())

        result = expression.all()
        return result

    @classmethod
    @db_func
    def get_by_in_field(
            cls: Type[T],
            field_name: str,
            values: list | set,
            **kwargs
    ) -> list[T]:
        """
        Асинхронний метод для отримання записів, де вказане поле містить значення зі
        списку

        Args:
            field_name: Назва поля для фільтрації IN
            values: Список або множина значень для фільтрації
            **kwargs: Додаткові параметри для get_expression

        Returns:
            Список об'єктів моделі
        """
        return cls.get_by_in_field_sync(field_name, values, **kwargs)

    @classmethod
    def add_order_by_position_and_time(cls, expression):
        if hasattr(cls, "position"):
            expression = expression.order_by(cls.position)
        if hasattr(cls, "time_created"):
            expression = expression.order_by(cls.time_created.desc())
        return expression

    @classmethod
    def create_sync(cls: Type[T], *, no_commit: bool = False, **kwargs) -> T:
        obj = cls(**kwargs)
        sess().add(obj)
        if not no_commit:
            sess().commit()
        return obj

    @classmethod
    @db_func
    @safe_deadlock_handler
    def create(cls: Type[T], *, no_commit: bool = False, **kwargs) -> T:
        return cls.create_sync(no_commit=no_commit, **kwargs)

    def update_sync(
            self, __data: dict | None = None, *, no_commit: bool = False, **kwargs
    ):
        """
        Function to update model
        """
        data = kwargs if not __data else {
            **__data,
            **kwargs,
        }

        for key, value in data.items():
            if key not in dir(self):
                raise AttributeError("Unknown attribute '%s'" % key)
            if self.UPDATE_FIELDS and key not in self.UPDATE_FIELDS:
                raise AttributeError("Forbidden to update attribute '%s'" % key)
            if key == "id":
                raise AttributeError("Cannot update id attribute")
            setattr(self, key, value)

        if hasattr(self, "time_updated") and self.UPDATE_TIME:
            self.time_updated = utcnow()

        if not no_commit:
            sess().commit()

        return self

    @db_func
    @safe_deadlock_handler
    def update(self, __data: dict | None = None, *, no_commit: bool = False, **kwargs):
        return self.update_sync(__data, no_commit=no_commit, **kwargs)

    def delete_sync(self):
        if hasattr(self, "is_deleted"):
            # noinspection PyAttributeOutsideInit
            self.is_deleted = True
        else:
            sess().delete(self)
        sess().commit()

    @db_func
    def delete(self):
        self.delete_sync()

    def __str__(self):
        return repr(self)

    def __repr__(self):
        return f"<{self.__class__.__name__} #{self.id}>"

    def __int__(self):
        return self.id

    def as_dict(self, by_columns: bool = False):
        result = {}

        if by_columns:
            data = tuple(
                (c.name, getattr(self, c.name))
                for c in self.__table__.columns
            )
        else:
            data = inspect(self).dict.items()

        for key, value in data:
            if key == "_sa_instance_state":
                continue

            if isinstance(value, enum.Enum):
                value = value.value
            elif isinstance(value, datetime):
                value = value.isoformat()
            result[key] = value
        return result

    @property
    def logger_data(self):
        if self.DEBUG_FIELDS == "auto":
            result = {
                "id": self.id
            }
            if hasattr(self, "name"):
                result["name"] = self.name
            return result

        return {
            key: getattr(self, key)
            for key in self.DEBUG_FIELDS
        }
