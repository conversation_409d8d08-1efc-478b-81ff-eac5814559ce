from typing import Any, Literal, Type

from sqlalchemy import Column
from sqlalchemy.orm import Query

from utils.type_vars import T


class BaseDBModel:
    __tablename__: str
    __allow_unmapped__: bool

    GET_FIELDS: list[str] | None
    UPDATE_FIELDS: list[str] | None
    DEBUG_FIELDS: list[str] | Literal["auto"]
    UPDATE_TIME: bool

    id: Column[int] | int

    def __init__(self, *args, **kwargs): ...

    @classmethod
    def get_expression(
            cls, exists_stmt: bool = False, for_update: bool = False, **kwargs
    ) -> Query: ...

    @classmethod
    def get_sync(
            cls: Type[T],
            id: Any = None, *,
            for_update: bool = False,
            release_on_exists: bool = False,
            **kwargs,
    ) -> T | None: ...

    @classmethod
    async def get(
            cls: Type[T],
            id: Any = None, *,
            for_update: bool = False,
            release_on_exists: bool = False,
            **kwargs,
    ) -> T | None: ...

    @classmethod
    def is_exists_sync(
            cls,
            id: Any = None, *,
            for_update: bool = False,
            release_on_exists: bool = False,
            **kwargs,
    ) -> bool: ...

    @classmethod
    async def is_exists(
            cls,
            id: Any = None, *,
            for_update: bool = False,
            release_on_exists: bool = False,
            **kwargs,
    ) -> bool: ...

    @classmethod
    def get_list_sync(cls: Type[T], **kwargs) -> list[T]: ...

    @classmethod
    async def get_list(cls: Type[T], **kwargs) -> list[T]: ...

    @classmethod
    def create_sync(cls: Type[T], **kwargs) -> T: ...

    @classmethod
    async def create(cls: Type[T], **kwargs) -> T: ...

    async def update(
            self: T, data: dict | None = None, *, no_commit: bool = False, **kwargs
    ) -> T: ...

    def update_sync(
            self: T, data: dict | None = None, *, no_commit: bool = False, **kwargs
    ) -> T: ...

    def delete_sync(self): ...

    async def delete(self): ...

    def __str__(self) -> str: ...

    def as_dict(self, by_columns: bool = False) -> dict[str, Any]: ...

    @property
    def logger_data(self) -> dict[str, Any]: ...
