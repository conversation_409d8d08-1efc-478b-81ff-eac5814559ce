import asyncio
from typing import Iterable

from psutils.func import check_function_spec
from sqlalchemy import and_, desc, distinct, func, not_, or_, select
from sqlalchemy.engine import Row
from sqlalchemy.sql import label

import schemas
from db import db_func, sess
from db.helpers import statement_to_str
from db.types.operation import Operation
from loggers import JSONLogger
from schemas import (
    ExtraFilterData, FilterType, ListCursorResponse, QueryBuilderAPIParams,
    QueryBuilderCursor, QueryBuilderCursorField, QueryBuilderObjConf,
    QueryBuilderSettings, SortData,
)


class DBQueryBuilder:
    def __init__(
            self,
            settings: QueryBuilderSettings,
            debug: bool = False
    ):
        self.settings = settings
        self.debug = debug

    def join_obj_if_required(
            self,
            name: str,
            joined_objects: list[str],
            joins: list[QueryBuilderObjConf],
            conf: QueryBuilderObjConf | None = None,
    ):
        if not conf:
            conf = self.settings.objects[name]

        if name in joined_objects or conf.join_expr is None:
            return

        for depends_on_obj in conf.depends_on:
            if depends_on_obj in joined_objects:
                continue
            self.join_obj_if_required(depends_on_obj, joined_objects, joins)

        joins.append(conf)
        joined_objects.append(name)

    def get_sync(
            self, params: QueryBuilderAPIParams,
            operation: Operation = "list",
            override_cursor: schemas.QueryBuilderCursor | None = None,
            **extra_filters_values: float | int | str | list[float | int | str]
    ):
        select_fields = []
        joined_objects = []
        added_objects = []
        joins: list[QueryBuilderObjConf] = []

        def get_field(field_: str):
            obj_name_, field_name_ = field_.split(".")
            if obj_name_ not in self.settings.objects:
                raise ValueError("Invalid object name '%s'" % obj_name_)

            obj_conf_ = self.settings.objects[obj_name_]
            if field_name_ not in obj_conf_.fields:
                raise ValueError("Invalid field name '%s'" % field_name_)

            field_value_ = getattr(obj_conf_.model, field_name_)
            return obj_name_, field_name_, obj_conf_, field_value_

        def add_field(field_: str):
            obj_name_, field_name_, obj_conf_, select_value_ = get_field(field_)
            self.join_obj_if_required(obj_name_, joined_objects, joins, obj_conf_)

            if field_name_ in obj_conf_.amount_fields:
                select_value_ = func.round(
                    select_value_ / 100, 2
                )

            if operation != "count":
                select_value_ = label(field_, select_value_)

            select_fields.append(select_value_)
            if obj_name_ not in added_objects:
                added_objects.append(obj_name_)

        # add fields to select
        for field in params.fields:
            add_field(field)

        # adding default fields to added objects
        for added_obj in added_objects:
            for default_field in self.settings.objects[added_obj].default_fields:
                field_name = f"{added_obj}.{default_field}"
                if field_name not in params.fields:
                    add_field(field_name)

        # added default fields
        for obj, obj_conf in self.settings.objects.items():
            if not obj_conf.default_obj or obj in added_objects:
                continue

            for default_field in obj_conf.default_fields:
                field_name = f"{obj}.{default_field}"
                if field_name not in params.fields:
                    add_field(field_name)

        if operation == "count":
            stmt = select(func.count(distinct(select_fields[0])))
        else:
            stmt = select(*select_fields)

        select_from = []
        for obj_conf in self.settings.objects.values():
            if obj_conf.select_from:
                select_from.append(obj_conf.model)

        if select_from:
            stmt = stmt.select_from(*select_from)

        conditions = []
        # add filters
        for filter in params.filters + self.settings.extra_filters:
            if isinstance(filter, ExtraFilterData):
                if filter.name not in extra_filters_values:
                    continue
                filter_value = extra_filters_values[filter.name]
                if (
                        filter_value is None or
                        (
                                isinstance(filter_value, Iterable) and
                                len(filter_value) == 0
                        )
                ):
                    continue
            else:
                filter_value = filter.value

            obj_name, field_name, obj_conf, field_value = get_field(filter.field)
            self.join_obj_if_required(obj_name, joined_objects, joins, obj_conf)

            if (
                    field_name in obj_conf.amount_fields and
                    isinstance(filter_value, float | int)
            ):
                filter_value = round(filter_value * 100)

            match filter.type:
                case FilterType.EQUAL:
                    condition = field_value == filter_value
                case FilterType.NOT_EQUAL:
                    condition = field_value != filter_value
                case FilterType.ONE_OF:
                    condition = field_value.in_(filter_value)
                case FilterType.NOT_ONE_OF:
                    condition = field_value.not_in(filter_value)
                case FilterType.EMPTY:
                    condition = field_value.is_(None)
                case FilterType.NOT_EMPTY:
                    condition = field_value.is_not(None)
                case FilterType.CONTAINS:
                    condition = field_value.contains(filter_value)
                case FilterType.NOT_CONTAINS:
                    condition = not_(field_value.contains(filter_value))
                case FilterType.GREATER_THAN:
                    condition = field_value > filter_value
                case FilterType.GREATER_THAN_OR_EQUAL:
                    condition = field_value >= filter_value
                case FilterType.LESS_THAN:
                    condition = field_value < filter_value
                case FilterType.LESS_THAN_OR_EQUAL:
                    condition = field_value <= filter_value
                case _:
                    raise ValueError("Invalid filter type '%s'" % filter.type)

            conditions.append(condition)

        ordered_by_fields: list[str] = []
        order_by = []

        if override_cursor:
            cursor = override_cursor
        elif params.cursor:
            cursor = QueryBuilderCursor.from_str(params.cursor)
        else:
            cursor = None

        cursor_conditions = []
        prev_equal_condition = None

        cursor_fields = [
            QueryBuilderCursorField(
                sort.field,
                sort.desc
            )
            for sort in params.sort
        ]
        if cursor and cursor.fields != cursor_fields:
            raise ValueError("Invalid cursor fields '%s'" % cursor_fields)

        for i, sort in enumerate(params.sort + self.settings.default_sort):
            if sort.field in ordered_by_fields:
                continue

            obj_name, _, obj_conf, sort_value = get_field(sort.field)
            if sort.if_joined and obj_name not in joined_objects:
                continue

            self.join_obj_if_required(obj_name, joined_objects, joins, obj_conf)

            if cursor and i < len(cursor.values):
                cursor_value = cursor.values[i]

                if cursor_value is None:
                    if sort.desc:
                        current_condition = sort_value.is_(None)
                    else:
                        current_condition = sort_value.is_not(None)

                    equal_condition = sort_value.is_(None)
                else:
                    if sort.desc:
                        current_condition = sort_value < cursor_value
                    else:
                        current_condition = sort_value > cursor_value

                    equal_condition = sort_value == cursor_value

                if prev_equal_condition is not None:
                    cursor_conditions.append(
                        and_(
                            prev_equal_condition,
                            current_condition,
                        )
                    )
                else:
                    cursor_conditions.append(current_condition)

                prev_equal_condition = equal_condition

            if sort.desc:
                sort_value = desc(sort_value)

            order_by.append(sort_value)
            ordered_by_fields.append(sort.field)

        # join models for selected fields
        for join_conf in joins:
            join_params = [
                join_conf.model,
                join_conf.join_expr
            ]
            if join_conf.outerjoin:
                stmt = stmt.outerjoin(*join_params)
            else:
                stmt = stmt.join(*join_params)

        stmt = stmt.where(*conditions)

        if cursor_conditions:
            stmt = stmt.where(or_(*cursor_conditions))

        if operation == "exists":
            stmt = stmt.limit(1)
            stmt = select(stmt.exists())
        elif operation == "list":
            stmt = stmt.order_by(*order_by)
            if params.limit:
                stmt = stmt.limit(params.limit)

        if self.debug:
            logger = JSONLogger(
                "query-builder", "Query", {
                    "params": params,
                    "extra_filters_values": extra_filters_values,
                }
            )

            try:
                logger.debug(str(statement_to_str(stmt)))
            except Exception as e:
                logger.error("Statement logging error", e, str(stmt))

        if operation in ("exists", "count"):
            return sess().scalar(stmt)
        return sess().execute(stmt).fetchall()

    @db_func
    def get(
            self, params: QueryBuilderAPIParams,
            operation: Operation = "list",
            override_cursor: schemas.QueryBuilderCursor | None = None,
            **extra_filters_values: float | int | str | list[float | int | str]
    ):
        return self.get_sync(params, operation, override_cursor, **extra_filters_values)

    def build_cursor(self, sort: list[SortData], last_row: Row):
        fields = [
            QueryBuilderCursorField(
                sort_field.field,
                sort_field.desc,
            ) for sort_field in sort
            if hasattr(last_row, sort_field.field)
        ]
        if not fields:
            return None

        return QueryBuilderCursor.build(
            fields,
            last_row,
        )

    @db_func
    def get_data_for_list_response(
            self, params: QueryBuilderAPIParams,
            **extra_filters_values: float | int | str | list[float | int | str]
    ):
        db_data = self.get_sync(params, **extra_filters_values)

        if db_data:
            cursor = self.build_cursor(params.sort, db_data[-1])
        else:
            cursor = None

        if cursor:
            if not self.get_sync(
                    params,
                    operation="exists",
                    override_cursor=cursor,
                    **extra_filters_values,
            ):
                cursor = None
            else:
                cursor = cursor.to_str()

        return db_data, cursor

    async def make_list_response(
            self, params: QueryBuilderAPIParams,
            **extra_filters_values: float | int | str | list[float | int | str]
    ) -> ListCursorResponse:
        db_data, cursor = await self.get_data_for_list_response(
            params, **extra_filters_values
        )

        data = []

        for row in db_data:
            row_data = {}

            for key, value in dict(row).items():
                obj_name, field_name = key.split(".")
                obj_settings = self.settings.objects[obj_name]

                # calling field processor
                if field_name in obj_settings.fields_processors:
                    processor_params = {
                        "field_key": key,
                        "obj_name": obj_name,
                        "field_name": field_name,
                        "field_value": value,
                        "obj_settings": obj_settings,
                        "builder": self,
                        "row": row,
                    }

                    processor = obj_settings.fields_processors[field_name]
                    safe_kwargs = check_function_spec(processor, processor_params)
                    value = processor(**safe_kwargs)
                    if asyncio.iscoroutine(value):
                        value = await value

                if obj_settings.fields_as_row_data:
                    row_data[field_name] = value
                else:
                    if obj_name not in row_data:
                        row_data[obj_name] = {}
                    row_data[obj_name][field_name] = value

            if self.settings.row_data_model:
                data.append(self.settings.row_data_model(**row_data))
            else:
                data.append(row_data)

        return ListCursorResponse(
            data=data,
            next=cursor,
        )
