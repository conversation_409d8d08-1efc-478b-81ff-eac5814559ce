import asyncio
from collections.abc import Callable
from functools import partial, wraps
from typing import Awaitable

from sqlalchemy.exc import NoResultFound

from db.helpers import ERRORS_CATCHING_BY_DB_ERROR, sess
from utils.sync_to_async import run_in_threadpool
from utils.type_vars import P, RT


def db_func(func: Callable[P, RT]) -> Callable[P, Awaitable[RT]]:
    @wraps(func)
    async def wrapper(*args, **kwargs):
        if asyncio.iscoroutinefunction(func):
            raise TypeError(
                """"
        Coroutine function can not be used with decorator @db_func. 
        For every function like it python will create socket file
        and it rises too many opened files error and 
        in this reason coroutinefunction can not be used with @db_func
        """
            )

        # connection_alive = await run_in_threadpool(partial(check_connection, engine))
        # if not connection_alive:
        #     return

        try:
            partial_func = partial(func, *args, **kwargs)
            result = await run_in_threadpool(partial_func)

        except NoResultFound:
            return

        except ERRORS_CATCHING_BY_DB_ERROR:
            sess().rollback()
            raise

        except Exception:
            raise
        else:
            return result

    return wrapper
