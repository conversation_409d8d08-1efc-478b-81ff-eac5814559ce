import asyncio
import logging
import random
import time
from collections.abc import Callable
from functools import partial, wraps
from inspect import getfullargspec
from typing import Concatenate, Type

from pydantic.fields import Undefined
from sqlalchemy.exc import OperationalError
from sqlalchemy.orm import Query

from config import DEBUG
from db.connection import Base
from db.helpers import DBSession, T, get_query_by_operation, order_by_slice_and_result
from utils.type_vars import FuncT, P, RT


async def _get_instance_in_new_session(arg: T) -> T:
    if not isinstance(arg, Base):
        return arg

    raise ValueError("Cant give Base instance in run_function_in_new_context")


async def run_function_in_new_context(func: FuncT, *args, **kwargs):
    from ..mixins.context import reset_context_instances

    reset_context_instances()
    with DBSession():
        args = [await _get_instance_in_new_session(arg) for arg in args]
        kwargs = {k: await _get_instance_in_new_session(v) for k, v in kwargs.items()}
        return await func(*args, **kwargs)


def own_session(func: FuncT) -> FuncT:

    async def wrapper(*args, **kwargs):
        partial_func = partial(run_function_in_new_context, func, *args, **kwargs)
        return await asyncio.create_task(partial_func())

    return wrapper


def process_query_with_operation(object: Type[T], order_by = None):
    def decorator(func: Callable[Concatenate[Query, P], RT]) -> Callable[
        P, list[T] | bool | int]:
        spec = getfullargspec(func)
        if spec.defaults:
            defaults_starts_diff = len(spec.args) - len(spec.defaults) - 1
        else:
            defaults_starts_diff = None

        def get_argument(name: str, args: tuple, kwargs: dict):
            value = kwargs.get(name, Undefined)
            if value is not Undefined:
                return value

            if name not in spec.args:
                return None

            if (idx := (spec.args.index(name) - 1)) < len(args):
                return args[idx]

            if defaults_starts_diff is None or idx < defaults_starts_diff:
                return None

            return spec.defaults[idx - defaults_starts_diff]

        def wrapper(*args: P.args, **kwargs: P.kwargs) -> list[T] | bool | int:
            operation = get_argument("operation", args, kwargs)
            position = get_argument("position", args, kwargs)
            limit = get_argument("limit", args, kwargs)

            query = get_query_by_operation(object, operation)
            query = func(query, *args, **kwargs)
            return order_by_slice_and_result(
                query, position, limit, order_by, operation
            )

        return wrapper

    return decorator


def safe_deadlock_handler(func):
    def write_log():
        if DEBUG:
            func_name = getattr(func, "__name__", "_unknown_function_")
            logging.getLogger("error.deadlock_handler").error(
                f"{func_name}: Deadlock detected. Retrying..."
            )

    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        tries = 5
        for attempt in range(tries):
            try:
                return await func(*args, **kwargs)
            except OperationalError as e:
                if 'Deadlock found' in str(e):
                    if DEBUG:
                        write_log()
                    if attempt < tries - 1:
                        delay = random.uniform(0.01, 0.3)
                        await asyncio.sleep(delay)
                    else:
                        raise
                else:
                    raise

    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        tries = 5
        for attempt in range(tries):
            try:
                return func(*args, **kwargs)
            except OperationalError as e:
                if 'Deadlock found' in str(e):
                    write_log()
                    if attempt < tries - 1:
                        delay = random.uniform(0.01, 0.3)
                        time.sleep(delay)
                    else:
                        raise
                else:
                    raise

    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    return sync_wrapper
