import atexit

import config as cfg

from aiohttp import web

from config import WEBSERVER_HOST
from utils.logger import setup_logger

from client.init import dp, app, router
from client.handlers import on_startup, on_shutdown, register_web_handlers, register_bot_handlers, bind_filters
from client.router import setup_router

setup_logger("client")

bind_filters(dp)

setup_router(router)

register_bot_handlers(dp)

register_web_handlers(app, dp)


atexit.register(on_shutdown, dp)


async def run_app():
    await on_startup(dp)

    return app

web.run_app(run_app(), host=WEBSERVER_HOST, port=cfg.ROOT_BOT_PORT + 1)
