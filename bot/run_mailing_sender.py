import asyncio
import logging

from config import ROOT_BOT_API_TOKEN
from db import DBSession

from utils.logger import setup_logger

from utils.redefined_classes import <PERSON><PERSON>

from core.mailing.sender.api import get_mailing_users
from core.mailing.sender import send_mailing_messages


Bot.set_current(Bot(ROOT_BOT_API_TOKEN))

setup_logger("mailing_sender")


async def main():
    while True:
        try:

            with DBSession():
                mailing_users = await get_mailing_users()

            if not mailing_users:
                await asyncio.sleep(1)
                continue

            await send_mailing_messages(mailing_users)

        except Exception as e:
            logger = logging.getLogger()
            logger.error(e, exc_info=True)

        await asyncio.sleep(1/30)


asyncio.run(main())
