# Контекст міграції налаштувань лояльності

## Загальна концепція
Проект переходить від старого способу роботи з налаштуваннями лояльності до нового. Новий спосіб передбачає, що налаштування кожного об'єкту який може мати лояльність зберігається в таблиці `loyalty_settings`.

## Об'єкти що мають налаштування лояльності
1. **Профіль (Brand)** - через `bot/api/admin/router/incust`
2. **Продукт (StoreProduct)** - через `bot/api/admin/router/products`  
3. **Магазин (Store)** - через `bot/api/admin/router/stores` (потрібно додати)
4. **Шаблон рахунку (InvoiceTemplate)** - через `bot/api/admin/router/invoice_templates`
5. **E-wallet** - через `bot/api/platform/router/ewallets`

## Завдання
Переробити існуючі роути для роботи з новою таблицею `loyalty_settings` замість старих полів у відповідних таблицях:
- Зберегти існуючі роути але змінити їх для роботи з новою системою
- НЕ видаляти роути, НЕ переводити на loyalty_settings роут
- Додати можливість збереження/отримання налаштувань лояльності для магазинів
- Використовувати централізовану валідацію з `LoyaltySettingsAdminService`

## Важливі параметри
- **incust_redeem_type** (Спосіб списання бонусів) - НЕ використовується в коді, можна виключити
- **incust_type_client_auth** (Авторизація через бот) - використовується, потрібно зберегти
- **incust_terminal_id** та **loyalty_id** - отримуються автоматично під час валідації, не потрібно вводити вручну

## Заборони
- НЕ створювати міграції БД
- НЕ видаляти існуючі роути
- НЕ запускати тести автоматично
- НЕ робити коміти

## Статус роботи
Розробник почав роботу над міграцією:
- Видалив старі схеми incust
- Почав переробляти роути для роботи з новою системою
- Почав видаляти deprecated поля з моделей та схем

## Наступні кроки
Потрібно завершити переробку всіх роутів для роботи з новою системою LoyaltySettings.