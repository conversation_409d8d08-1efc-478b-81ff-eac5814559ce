/qrcodes_api_project/db.sqlite3
__pycache__/
.env/
.idea/
.vscode/
venv/
*.code-workspace
logs/
/bot/functions/config/personal_config.py
/bot/config/local_config.py
/translator_limiter/config/local_config.py
/translator_limiter/migrations/versions/*
/bot/logs/
/bot/static/static_db
/bot/static/static_files
/bot/static/storage
/bot/static/store_xlsx_files
/bot/static/temp
/bot/simplified_chat/*.session
/local_creds.json
/local_data.json
/site/allauth/migrations/0001_initial.py
/site/events/migrations/
/site/events/my_migrations/
/site/events/test_migrations
/site/pay4say/settings.py
/site/pay4say/PythonProject.json
/bot/config/PythonProject.json
/bot/config/ManageApiKeys.json
/bot/tests/test_config.py
.pytest_cache
/bot/static/uploads/
/bot/__translator_cache__/langs.json
/docs/.env
/bot/locales/*
/bot/test_positions.xlsx
/bot/aimenus/*
/bot/tst_*.py
/bot/*.sh
/bot/*.log
PROJECT.md
/tests/vik/*.txt

.DS_Store

# windsurf rules
.windsurfrules
/tables_split
/db_tools