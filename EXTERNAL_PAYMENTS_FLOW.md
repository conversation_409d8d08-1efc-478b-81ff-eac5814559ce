# Процес оплати через QR-код (Wave/Orange) в Сенегалі - 7loc

## Знайдені компоненти системи:

### 1. QR-код сканування (Frontend - 7loc-client-web)
- **Компонент сканера**: `/home/<USER>/7loc-client-web/src/routes/$lang/_shop/-components/ewallet/EwalletPay/EwalletPayForm/field-components/ScanQrField/ScanQrModal.tsx`
- Використовує бібліотеку `@yudiel/react-qr-scanner`
- Після сканування передає результат в поле форми

### 2. Створення заявки на оплату (Backend - pay4say)
- **API endpoint**: `/home/<USER>/pay4say/bot/api/ewallet/router/external_payments/router.py`
  - `POST /external_payments/` - створення нової заявки
- **Модель БД**: `/home/<USER>/pay4say/bot/db/models/ewallet/ewallet_external_payment.py`
- **Типи платежів**: WAVE та ORANGE (enum `EWalletExternalPaymentTransferType`)
- **Поля заявки**:
  - `transfer_data` - дані QR-коду
  - `amount` - сума платежу
  - `status` - статус (CREATED → PENDING → SUCCESS/REJECTED/CANCELLED)
  - `payer_id` - ID працівника який взяв заявку

### 3. Система блокування заявок
- **Механізм**: `/home/<USER>/pay4say/bot/db/crud/ewallet_external_payment/update.py:14`
- Коли працівник змінює статус на PENDING, встановлюється `payer_id = user.id`
- Це блокує заявку від інших працівників
- Тільки працівник з `payer_id` може далі працювати з заявкою

### 4. CRM для працівників
- **API endpoint**: `/home/<USER>/pay4say/bot/api/crm/router/ewallet_ext_payment/ewallet_ext_payment/router.py`
  - `GET /{ewallet_ext_payment_id}/` - отримання деталей заявки
  - `POST /{ewallet_ext_payment_id}/status/{new_status}` - зміна статусу
- **Права доступу**: `crm_ewallet_ext_payment:read` та `crm_ewallet_ext_payment:edit`
- **Сервіс**: `/home/<USER>/pay4say/bot/api/crm/router/ewallet_ext_payment/ewallet_ext_payment/service.py`

### 5. Процес підтвердження оплати
- **Функція**: `/home/<USER>/pay4say/bot/core/ewallet/external_payment/functions.py:144`
- **Статусні переходи**:
  - CREATED → PENDING (працівник бере в роботу)
  - PENDING → SUCCESS (працівник підтверджує оплату)
  - PENDING/CREATED → REJECTED (відхилення)
  - CREATED → CANCELLED (скасування користувачем)
- При SUCCESS створюється оплачений інвойс

### 6. Показ реклами під час очікування
- **Компонент відео**: `/home/<USER>/7loc-client-web/src/features/AdVideo.tsx`
- **Екран очікування**: `/home/<USER>/7loc-client-web/src/routes/$lang/_shop/-components/ewallet/EwalletPayment/AdPendingScreen/AdPendingScreen.tsx`
- **API для реклами**: `/home/<USER>/7loc-client-web/src/api/ad/ad/ad.ts`
- Реклама показується циклічно поки статус CREATED або PENDING
- Відео адаптується під орієнтацію екрану

### 7. Система кредитного запису через віртуальний менеджер "CreditEnrollment"

**Backend (pay4say)**:
- **Віртуальний менеджер**: ID 493, назва "CreditEnrollment" в таблиці `virtual_managers`
- **Модель VirtualManager**: `/home/<USER>/pay4say/bot/db/models/virtual_manager/virtual_manager.py`
- **Кроки процесу** (в таблиці `virtual_manager_steps`):
  - **Крок 2643** (position 0): Вітальне повідомлення з кнопкою "Continuer"
  - **Крок 2644** (position 2): Запит імені "Quel est votre nom complet ?" → зберігається в `fullName`
  - **Крок 2645** (position 3): Запит компанії "Dans quelle entreprise travaillez-vous?" → зберігається в `Company`
  - **Крок 2955** (position 4): Фінальне повідомлення + створення тікету

**Інтерактивні елементи** (таблиця `virtual_manager_interactives`):
- **ON_INPUT set_var**: Автоматичне збереження користувацького вводу у відповідні змінні
- **PRE_ACTION ticket_create**: Створення тікету "New company application" після завершення діалогу
- **Система нагадувань**: Автоматичне повторення питань кожні 2 години при відсутності відповіді

**Зберігання даних**:
- **Чати користувачів**: Таблиця `virtual_manager_chats` - відстеження прогресу кожного користувача
- **Тікети CRM**: Таблиця `crm_tickets` - заявки на створення кредитних рахунків
- **InCust інтеграція**: Після ручного створення спеціального рахунку `is_user_special_account` стає `True`

**Історичні зміни**:
- **До лютого 2025**: Були кнопки з вибором компаній (3D Techlogis, AdriDev Groupe, NoWelli) 
- **Після лютого 2025**: Кнопки видалені (`is_deleted = 1`), тільки вільне введення тексту

## Повний алгоритм роботи:

### A. Перевірка доступу до кредитної оплати (для нових користувачів)

**Якщо користувач НЕ має доступу до кредитних EWallet (`is_user_special_account = False`):**

1. **Запуск процедури кредитного запису**
   - Користувач активує віртуальний менеджер "CreditEnrollment" через команду `vm-CreditEnrollment`
   - Система вітає користувача повідомленням про можливість кредитної оплати

2. **Збір персональних даних**
   - **Крок 1**: Користувач натискає кнопку "Continuer" (Продовжити)
   - **Крок 2**: Система запитує повне ім'я: "Quel est votre nom complet ?"
   - Користувач вводить своє ім'я, воно зберігається в змінну `fullName`
   
3. **Збір інформації про роботодавця**  
   - **Крок 3**: Система запитує назву компанії: "Dans quelle entreprise travaillez-vous?"
   - Користувач вводить назву компанії (вільний текст), зберігається в змінну `Company`
   - *Примітка: Раніше були кнопки з готовими варіантами компаній, але їх видалили в лютому 2025*

4. **Створення заявки на кредит**
   - **Крок 4**: Система показує повідомлення "Merci pour votre demande ! Nous vous contacterons dès que possible."
   - Автоматично створюється тікет в CRM системі з типом "New company application"
   - Тікет містить дані: ім'я користувача, назва компанії, дата заявки

5. **Ручна обробка заявки**
   - Адміністратори обробляють тікет в CRM системі
   - Перевіряють інформацію про користувача та роботодавця
   - **Вручну створюють кредитний рахунок** в InCust loyalty системі з відповідними лімітами
   - Після створення рахунку `is_user_special_account` стає `True`

### B. Основний процес QR-оплати (для користувачів з доступом)

1. **Користувач сканує QR-код**
   - Відкриває додаток 7loc
   - Сканує QR-код мерчанта (Wave або Orange)
   - QR-код містить дані для оплати

2. **Створення заявки**
   - Frontend відправляє `POST /api/ewallet/external_payments/`
   - Створюється запис в БД зі статусом CREATED
   - Резервується транзакція в InCust API (з кредитного рахунку якщо доступний)

3. **Показ реклами**
   - Відкривається повноекранне модальне вікно
   - Циклічно показується відео-реклама
   - Користувач бачить інформацію про платіж поверх відео

4. **Працівник бере заявку**
   - Працівник в CRM бачить нову заявку
   - Змінює статус на PENDING через `POST /api/crm/ewallet_ext_payment/{id}/status/PENDING`
   - Встановлюється `payer_id` - заявка блокується

5. **Оплата мерчанту**
   - Працівник оплачує через свій Wave/Orange додаток
   - Використовує дані з `transfer_data` заявки

6. **Підтвердження оплати**
   - Працівник змінює статус на SUCCESS
   - Фіналізується транзакція в InCust (списання з кредитного рахунку)
   - Створюється оплачений інвойс
   - Записується історія статусів

7. **Завершення для користувача**
   - Реклама припиняється
   - Показується підтвердження оплати
   - Користувач може отримати товар/послугу
   - Кредитний баланс зменшується на суму покупки

## Система безпеки:
- Блокування заявок через `payer_id`
- Контроль прав доступу (scopes)
- Історія всіх змін статусів
- Транзакції через InCust API для лояльності

## Точки входу та інтерфейси

### Для користувача (7loc-client-web):

#### URL та маршрути:
- **Початок оплати**: `/{lang}/ewallet/{ewalletId}/pay`
  - Приклад: `/uk/ewallet/123/pay`
- **Екран оплати з рекламою**: `/{lang}/ewallet/payment/{paymentId}`
  - Після створення заявки користувач автоматично перенаправляється сюди

#### Форма оплати:
- **Компонент форми**: `/home/<USER>/7loc-client-web/src/routes/$lang/_shop/-components/ewallet/EwalletPay/EwalletPayForm/EwalletPayForm.tsx`
- **Блоки форми**:
  1. **TransferDataBlock** - сканування QR-коду (ScanQrField)
  2. **AmountBlock** - введення суми платежу
  3. **SubmitBlock** - кнопка підтвердження

#### Процес для користувача:
1. Переходить на `/{lang}/ewallet/{ewalletId}/pay`
2. Сканує QR-код через камеру (відкривається модальне вікно)
3. Вводить суму платежу
4. Натискає кнопку оплати
5. Автоматично перенаправляється на `/{lang}/ewallet/payment/{paymentId}`
6. Бачить повноекранну рекламу з інформацією про платіж

### Для менеджерів (CRM):

#### Компоненти CRM мобільного додатку (7loc-crm):

##### Inbox система:
- **Backend інтеграція**: `/home/<USER>/pay4say/bot/db/crud/inbox/read.py:321-350`
- **Тип inbox**: `InboxType.EWALLET_EXT_PAYMENT`
- **Статуси в inbox**:
  - `InboxStatus.NEW` → `EWalletExternalPaymentStatus.CREATED`
  - `InboxStatus.IN_PROGRESS` → `EWalletExternalPaymentStatus.PENDING`
  - `InboxStatus.RECENT` → `CANCELLED`, `REJECTED`, `SUCCESS`
- **Inbox компонент**: `/home/<USER>/7loc-crm/src/components/inbox/InboxPage.tsx:124-131`
  - Відображення EWallet платежів через `ListEwalletExtPayment`

##### UI компоненти для зовнішніх платежів:
- **Основний компонент заявки**: `/home/<USER>/7loc-crm/src/components/EwalletExtPayments/EwalletExtPayment.tsx`
  - Показує деталі заявки
  - Управління статусами (PENDING, SUCCESS, REJECTED)
  - Блокування заявки через `payer_id`
- **Список заявок**: `/home/<USER>/7loc-crm/src/components/EwalletExtPayments/ListEwalletExtPayment.tsx`
- **Контент платежу**: `/home/<USER>/7loc-crm/src/components/EwalletExtPayments/CustomContent.tsx`
  - Відображення суми та провайдера (Wave/Orange)
- **QR-код для оплати**: `/home/<USER>/7loc-crm/src/components/EwalletExtPayments/TransferData.tsx`
  - Генерація QR-коду для працівника
  - Відображення даних для переказу
  - Лінк для Wave платежів

##### API хуки для CRM:
- **API файл**: `/home/<USER>/7loc-crm/src/api/crm/ewallet-ext-payment/ewallet-ext-payment.ts`
- **Основні хуки**:
  - `useGetEwalletExtPayment` - отримання деталей заявки
  - `useSetEwalletExtPaymentStatus` - зміна статусу заявки
- **Endpoints**:
  - `GET /crm_new/ewallet_ext_payment/{id}/` - деталі заявки
  - `POST /crm_new/ewallet_ext_payment/{id}/status/{new_status}` - зміна статусу

#### Як працівник бачить заявки:
1. Заходить в CRM мобільний додаток (7loc-crm)
2. На вкладці "Inbox" з'являються нові заявки з типом `ewallet_ext_payment`
3. Заявки зі статусом CREATED показуються як "NEW"
4. Працівник відкриває заявку через `/ewalletExtPayment/[id]`
5. Бачить QR-код та дані для переказу (якщо статус PENDING)
6. Змінює статус на PENDING (блокує для себе через `payer_id`)
7. Оплачує через власний Wave/Orange додаток
8. Після оплати змінює на SUCCESS або REJECTED

### Інтеграція з платіжними системами:
- **Методи оплати через EWallet**:
  - В checkout процесі
  - Як окремий платіжний метод
  - Компонент: `EWalletPayModalContent.tsx`
- **FastPay функціонал**: швидка оплата через збережені EWallet

## Детальна технічна архітектура External Payments (досліджено 19.08.2025)

### Система reserve_check → finalize_check

#### 1. При створенні EWallet External Payment
**Функція**: `create_ewallet_ext_payment()`
**Файл**: `/home/<USER>/pay4say/bot/core/ewallet/external_payment/functions.py:42`

**Кроки обробки**:

1. **Обчислення сум**:
   ```python
   discount_amount = data.amount / 100 * ewallet.discount_percent
   amount_after_sale = data.amount - discount_amount
   fee = ewallet.ext_payment_fee_value + (amount_after_sale * ewallet.ext_payment_fee_percent / 100)
   total_amount = amount_after_sale + fee
   ```

2. **InCust Reserve Check** (створює резерв транзакції):
   ```python
   async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
       incust_transaction = await api.reserve_check(
           term.m.Check(
               payment_id=ewallet.incust_account_id,
               payment_type="special-account",
               skip_message=False,
               id=user.incust_external_id,
               id_type=term.m.IdType("external-id"),
               amount=total_amount,
               amount_to_pay=total_amount,
               check_items=[
                   term.m.CheckItem(
                       title=f"{ewallet.name} payment",
                       code=f"ewextp-{ewallet.id}",
                       price=total_amount,
                       quantity=1,
                       amount=total_amount,
                       category=f"ewextp-{ewallet.id}",
                   )
               ]
           )
       )
   ```

3. **Збереження transaction_id в БД**:
   ```python
   payment = await crud.create_ewallet_external_payment(
       **data.dict(),
       incust_transaction_id=incust_transaction.id,  # ← КЛЮЧОВЕ ПОЛЕ
   )
   ```

#### 2. При зміні статусу на SUCCESS - InCust Finalize Check
**Функція**: `set_ewallet_ext_payment_status()` 

```python
case EWalletExternalPaymentStatus.SUCCESS:
    async with incust.term.CheckTransactionsApi(loyalty_settings, lang=lang) as api:
        incust_result = await api.finalize_check(
            term.m.FinalizeCheckRequest(
                id=str(payment.incust_transaction_id),  # ← Використання збереженого ID
                comment=f"EwalletExtPayment #{payment.id} - paid"
            )
        )
```

#### 3. Створення та оплата Invoice
**Функція**: `create_ewallet_ext_payment_invoice()`

1. **Створення Invoice**:
   ```python
   invoice = await invoice_service.create_invoice(
       invoice_type=InvoiceTypeEnum.EWALLET_EXT_PAYMENT,
       payment_mode=InvoicePaymentModeEnum.ENTERED_AMOUNT.value,
       items=[CreateInvoiceItemData(...)],
       ewallet_id=ewallet.id,
       external_transaction_id=payment.uuid_id,
       payer_id=payment.payer_id,
   )
   ```

2. **Оплата Invoice**:
   ```python
   await invoice.payed(
       payment_method="ewallet",
       payer_fee=round(payment.fee * 100)
   )
   ```

3. **Додаткова обробка**:
   - `record_billing_transaction_usage()` - біллінг
   - `add_webhook_event()` - webhook повідомлення  
   - `add_invoice_payment_notification()` - Kafka повідомлення

### Система прав доступу (CRM)

#### ScopeMap конфігурація:
```python
crm_ewallet_ext_payment = ScopeBatch(
    crm,
    ScopeBatch(
        Scope("crm_ewallet_ext_payment:edit", "profile_id", tags=["edit", "read"]),
        Scope("crm_ewallet_ext_payment:read", "profile_id", tags=["read"]),
        ...
    )
)
```

#### Перевірка прав для різних статусів:
```python
match new_status:
    case EWalletExternalPaymentStatus.PENDING:
        change_status_allowed = (
            not payment.payer_id and                    # Платник не призначений
            await has_edit_access() and                # CRM права
            payment.status == EWalletExternalPaymentStatus.CREATED  # Статус CREATED
        )
    case EWalletExternalPaymentStatus.SUCCESS:
        change_status_allowed = (
            user.id == payment.payer_id and           # Тільки платник
            payment.status in (CREATED, PENDING)      # З CREATED/PENDING
        )
```

#### Правильна перевірка прав (виправлено):
```python
def has_edit_access():
    return crud.check_access_to_action(
        "crm_ewallet_ext_payment:edit",   # CRM action
        "user",                           # Check user permissions
        user.id,                          # For specific user
        {"profile_id": payment.profile_id}, # In profile context
    )
```

### ❌ Критична проблема: Втрата даних лояльності

**Проблема**: Результат `api.finalize_check()` не обробляється для збереження loyalty даних в Invoice.

**Наслідки**:
- `invoice.bonuses_added_amount = 0` 
- `invoice.loyalty_coupons_data = null`
- `invoice.special_accounts_charges = null`
- `invoice.loyalty_discount_amount = 0`

**Правильна обробка** (як в `/bot/core/invoice_loyalty/service.py`):
1. `response = await api.finalize_check(finalize_body)`
2. `receipt = await api.transaction_receipt(str(final_transaction_id))`
3. Витягти `emitted_coupons` та інші дані з receipt
4. Оновити поля invoice з loyalty даними

### База даних - ключові поля

#### Таблиця: ewallet_external_payments
- `incust_transaction_id` - ID резервованої InCust транзакції
- `profile_id` - Профіль для перевірки прав
- `payer_id` - Користувач-платник (для SUCCESS статусу) 
- `user_id` - Створювач payment (для CANCELLED статусу)

#### Права доступу (scopes таблиця)
**Для CRM операцій потрібні**:
- `crm_ewallet_ext_payment:edit` з `profile_id`
- `crm_ewallet_ext_payment:read` з `profile_id`

**Або глобальні права**:
- `platform:superadmin`
- `platform:admin`

### Послідовність викликів (повний флоу)

```
1. Користувач створює payment
   ↓
2. create_ewallet_ext_payment()
   ├─ Обчислення сум (discount, fees)
   ├─ api.reserve_check() → отримує incust_transaction.id
   ├─ crud.create_ewallet_external_payment(incust_transaction_id=...)
   └─ send_notifications()

3. Менеджер змінює статус на SUCCESS
   ↓
4. set_ewallet_ext_payment_status()
   ├─ Перевірка прав: has_edit_access()
   ├─ crud.set_ewallet_external_payment_status()
   ├─ api.finalize_check(payment.incust_transaction_id) ← Використання збереженого ID
   ├─ create_ewallet_ext_payment_invoice()
   │  ├─ invoice_service.create_invoice()
   │  ├─ invoice.payed()
   │  ├─ record_billing_transaction_usage()
   │  ├─ add_webhook_event()
   │  └─ add_invoice_payment_notification()
   └─ send_notifications()
```

### Рекомендації для виправлення

1. **Додати обробку loyalty результатів** в `create_ewallet_ext_payment_invoice()`
2. **Імплементувати `transaction_receipt()`** після `finalize_check()`  
3. **Зберігати emitted_coupons** в invoice поля
4. **Тестувати повний флоу** з loyalty правилами
5. **Відображати знижки та бонуси** на фронтенді після process_check